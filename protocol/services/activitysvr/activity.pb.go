// Code generated by protoc-gen-gogo.
// source: src/activitysvr/activity.proto
// DO NOT EDIT!

/*
	Package Activity is a generated protocol buffer package.

	It is generated from these files:
		src/activitysvr/activity.proto

	It has these top-level messages:
		OfferCategoryInfo
		OfferCategoryList
		ActivityOfferInfo
		ActivityInfo
		CreateActivityReq
		CreateActivityResp
		UpdateActivityReq
		UpdateActivityResp
		DeleteActivityReq
		GetActivityListReq
		GetActivityListResp
		OfferEventElem
		CreateOfferEventReq
		CreateOfferEventRsp
		ModifyOfferEventReq
		DelOfferEventReq
		GetOfferEventReq
		GetOfferEventRsp
		GameRankElem
		AddGameRankReq
		ModifyGameRankReq
		DelGameRankReq
		GetGameRankReq
		GetGameRankRsp
		GetGameOfferReq
		GameOfferInfo
		GetGameOfferRsp
		FirstVoucherAccountPassword
		FirstVoucherProduct
		GetFirstVoucherProductsReq
		GetFirstVoucherProductsResp
		AddFirstVoucherProductReq
		AddFirstVoucherProductResp
		ModifyFirstVoucherProductReq
		GetGameFirstVoucherProductReq
		GetGameFirstVoucherProductResp
		DeleteFirstVoucherProductReq
		FirstVoucherItem
		AddFirstVoucherItemReq
		AddFirstVoucherItemResp
		GetFirstVoucherItemReq
		GetFirstVoucherItemResp
		DeleteFirstVoucherItemReq
		FirstVoucherUserItem
		PurchaseFirstVoucherItemReq
		PurchaseFirstVoucherItemResp
		GetFirstVoucherUserItemReq
		GetFirstVoucherUserItemResp
		ActBannerInfo
		UpdateActBannerReq
		GetActBannersResp
		DelActBannerReq
		GetActBannerOpTimeResp
		ActSplashScreenInfo
		UpdateActSplashScreenReq
		GetActSplashScreenResp
		DelActSplashScreenReq
		ActSplashScreenOpTime
		GetActSplashScreenOpTimeResp
		GetGamePreorderResultReq
		GamePreorderResult
		GetGamePreorderResultResp
		GetGamePreorderUserSpptReq
		GetGamePreorderUserSpptResp
		SetGamePreorderUserSpptReq
		InviterInfo
		GetQQCarActivityRankResp
		NotifyInviteRecordReq
		GetPrizeInfoReq
		GetPrizeInfoResp
		SetUidPairQQReq
		TakePrizeReq
		TakePrizeResp
*/
package Activity

import "github.com/gogo/protobuf/proto"
import "fmt"
import "math"
import "gitlab.ttyuyin.com/golang/svrkit/protocol/common/tlvpickle"

import (
	"golang.org/x/net/context"
	"google.golang.org/grpc"
)

import github_com_gogo_protobuf_proto1 "github.com/gogo/protobuf/proto"

import io1 "io"
import fmt2 "fmt"
import github_com_gogo_protobuf_proto2 "github.com/gogo/protobuf/proto"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion2 // please upgrade the proto package

type ACTIVITY_STATUS int32

const (
	ACTIVITY_STATUS_ACTIVITY_ST_TOOPEN    ACTIVITY_STATUS = 1
	ACTIVITY_STATUS_ACTIVITY_ST_OPENING   ACTIVITY_STATUS = 2
	ACTIVITY_STATUS_ACTIVITY_ST_OVER      ACTIVITY_STATUS = 4
	ACTIVITY_STATUS_ACTIVITY_ST_ADMINSTOP ACTIVITY_STATUS = 8
	ACTIVITY_STATUS_ACTIVITY_ST_DELETE    ACTIVITY_STATUS = 16
	ACTIVITY_STATUS_ACTIVITY_ST_ALL       ACTIVITY_STATUS = 31
)

var ACTIVITY_STATUS_name = map[int32]string{
	1:  "ACTIVITY_ST_TOOPEN",
	2:  "ACTIVITY_ST_OPENING",
	4:  "ACTIVITY_ST_OVER",
	8:  "ACTIVITY_ST_ADMINSTOP",
	16: "ACTIVITY_ST_DELETE",
	31: "ACTIVITY_ST_ALL",
}
var ACTIVITY_STATUS_value = map[string]int32{
	"ACTIVITY_ST_TOOPEN":    1,
	"ACTIVITY_ST_OPENING":   2,
	"ACTIVITY_ST_OVER":      4,
	"ACTIVITY_ST_ADMINSTOP": 8,
	"ACTIVITY_ST_DELETE":    16,
	"ACTIVITY_ST_ALL":       31,
}

func (x ACTIVITY_STATUS) Enum() *ACTIVITY_STATUS {
	p := new(ACTIVITY_STATUS)
	*p = x
	return p
}
func (x ACTIVITY_STATUS) String() string {
	return proto.EnumName(ACTIVITY_STATUS_name, int32(x))
}
func (x *ACTIVITY_STATUS) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(ACTIVITY_STATUS_value, data, "ACTIVITY_STATUS")
	if err != nil {
		return err
	}
	*x = ACTIVITY_STATUS(value)
	return nil
}
func (ACTIVITY_STATUS) EnumDescriptor() ([]byte, []int) { return fileDescriptorActivity, []int{0} }

// 福利活动类型
type OFFEREVENT_TYPE int32

const (
	OFFEREVENT_TYPE_OFFER_EVENT_TYPE_NULL   OFFEREVENT_TYPE = 0
	OFFEREVENT_TYPE_OFFER_EVENT_TYPE_GAME   OFFEREVENT_TYPE = 1
	OFFEREVENT_TYPE_OFFER_EVENT_TYPE_GLOBAL OFFEREVENT_TYPE = 2
	OFFEREVENT_TYPE_OFFER_EVENT_TYPE_ALL    OFFEREVENT_TYPE = 3
)

var OFFEREVENT_TYPE_name = map[int32]string{
	0: "OFFER_EVENT_TYPE_NULL",
	1: "OFFER_EVENT_TYPE_GAME",
	2: "OFFER_EVENT_TYPE_GLOBAL",
	3: "OFFER_EVENT_TYPE_ALL",
}
var OFFEREVENT_TYPE_value = map[string]int32{
	"OFFER_EVENT_TYPE_NULL":   0,
	"OFFER_EVENT_TYPE_GAME":   1,
	"OFFER_EVENT_TYPE_GLOBAL": 2,
	"OFFER_EVENT_TYPE_ALL":    3,
}

func (x OFFEREVENT_TYPE) Enum() *OFFEREVENT_TYPE {
	p := new(OFFEREVENT_TYPE)
	*p = x
	return p
}
func (x OFFEREVENT_TYPE) String() string {
	return proto.EnumName(OFFEREVENT_TYPE_name, int32(x))
}
func (x *OFFEREVENT_TYPE) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(OFFEREVENT_TYPE_value, data, "OFFEREVENT_TYPE")
	if err != nil {
		return err
	}
	*x = OFFEREVENT_TYPE(value)
	return nil
}
func (OFFEREVENT_TYPE) EnumDescriptor() ([]byte, []int) { return fileDescriptorActivity, []int{1} }

type OFFEREVENT_STATUS int32

const (
	OFFEREVENT_STATUS_OFFER_EVENT_STATUS_OFFLINE OFFEREVENT_STATUS = 1
	OFFEREVENT_STATUS_OFFER_EVENT_STATUS_ONLINE  OFFEREVENT_STATUS = 2
	OFFEREVENT_STATUS_OFFER_EVENT_STATUS_ALL     OFFEREVENT_STATUS = 3
)

var OFFEREVENT_STATUS_name = map[int32]string{
	1: "OFFER_EVENT_STATUS_OFFLINE",
	2: "OFFER_EVENT_STATUS_ONLINE",
	3: "OFFER_EVENT_STATUS_ALL",
}
var OFFEREVENT_STATUS_value = map[string]int32{
	"OFFER_EVENT_STATUS_OFFLINE": 1,
	"OFFER_EVENT_STATUS_ONLINE":  2,
	"OFFER_EVENT_STATUS_ALL":     3,
}

func (x OFFEREVENT_STATUS) Enum() *OFFEREVENT_STATUS {
	p := new(OFFEREVENT_STATUS)
	*p = x
	return p
}
func (x OFFEREVENT_STATUS) String() string {
	return proto.EnumName(OFFEREVENT_STATUS_name, int32(x))
}
func (x *OFFEREVENT_STATUS) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(OFFEREVENT_STATUS_value, data, "OFFEREVENT_STATUS")
	if err != nil {
		return err
	}
	*x = OFFEREVENT_STATUS(value)
	return nil
}
func (OFFEREVENT_STATUS) EnumDescriptor() ([]byte, []int) { return fileDescriptorActivity, []int{2} }

type PrizeType int32

const (
	PrizeType_REGISTER  PrizeType = 1
	PrizeType_INVITE    PrizeType = 2
	PrizeType_DAYRANK   PrizeType = 3
	PrizeType_TOTALRANK PrizeType = 4
)

var PrizeType_name = map[int32]string{
	1: "REGISTER",
	2: "INVITE",
	3: "DAYRANK",
	4: "TOTALRANK",
}
var PrizeType_value = map[string]int32{
	"REGISTER":  1,
	"INVITE":    2,
	"DAYRANK":   3,
	"TOTALRANK": 4,
}

func (x PrizeType) Enum() *PrizeType {
	p := new(PrizeType)
	*p = x
	return p
}
func (x PrizeType) String() string {
	return proto.EnumName(PrizeType_name, int32(x))
}
func (x *PrizeType) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(PrizeType_value, data, "PrizeType")
	if err != nil {
		return err
	}
	*x = PrizeType(value)
	return nil
}
func (PrizeType) EnumDescriptor() ([]byte, []int) { return fileDescriptorActivity, []int{3} }

// offer category
type OfferCategoryInfo struct {
	Id   uint32 `protobuf:"varint,1,req,name=id" json:"id"`
	Name string `protobuf:"bytes,2,req,name=name" json:"name"`
}

func (m *OfferCategoryInfo) Reset()                    { *m = OfferCategoryInfo{} }
func (m *OfferCategoryInfo) String() string            { return proto.CompactTextString(m) }
func (*OfferCategoryInfo) ProtoMessage()               {}
func (*OfferCategoryInfo) Descriptor() ([]byte, []int) { return fileDescriptorActivity, []int{0} }

func (m *OfferCategoryInfo) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *OfferCategoryInfo) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

type OfferCategoryList struct {
	List []*OfferCategoryInfo `protobuf:"bytes,1,rep,name=list" json:"list,omitempty"`
}

func (m *OfferCategoryList) Reset()                    { *m = OfferCategoryList{} }
func (m *OfferCategoryList) String() string            { return proto.CompactTextString(m) }
func (*OfferCategoryList) ProtoMessage()               {}
func (*OfferCategoryList) Descriptor() ([]byte, []int) { return fileDescriptorActivity, []int{1} }

func (m *OfferCategoryList) GetList() []*OfferCategoryInfo {
	if m != nil {
		return m.List
	}
	return nil
}

//
type ActivityOfferInfo struct {
	GameId        uint32 `protobuf:"varint,1,req,name=game_id,json=gameId" json:"game_id"`
	ActivityId    uint32 `protobuf:"varint,2,opt,name=activity_id,json=activityId" json:"activity_id"`
	CategoryId    uint32 `protobuf:"varint,3,req,name=category_id,json=categoryId" json:"category_id"`
	Amount        uint32 `protobuf:"varint,4,opt,name=amount" json:"amount"`
	OfferDesc     string `protobuf:"bytes,5,opt,name=offer_desc,json=offerDesc" json:"offer_desc"`
	OfferNotes    string `protobuf:"bytes,6,opt,name=offer_notes,json=offerNotes" json:"offer_notes"`
	OfferAppendix string `protobuf:"bytes,7,opt,name=offer_appendix,json=offerAppendix" json:"offer_appendix"`
}

func (m *ActivityOfferInfo) Reset()                    { *m = ActivityOfferInfo{} }
func (m *ActivityOfferInfo) String() string            { return proto.CompactTextString(m) }
func (*ActivityOfferInfo) ProtoMessage()               {}
func (*ActivityOfferInfo) Descriptor() ([]byte, []int) { return fileDescriptorActivity, []int{2} }

func (m *ActivityOfferInfo) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *ActivityOfferInfo) GetActivityId() uint32 {
	if m != nil {
		return m.ActivityId
	}
	return 0
}

func (m *ActivityOfferInfo) GetCategoryId() uint32 {
	if m != nil {
		return m.CategoryId
	}
	return 0
}

func (m *ActivityOfferInfo) GetAmount() uint32 {
	if m != nil {
		return m.Amount
	}
	return 0
}

func (m *ActivityOfferInfo) GetOfferDesc() string {
	if m != nil {
		return m.OfferDesc
	}
	return ""
}

func (m *ActivityOfferInfo) GetOfferNotes() string {
	if m != nil {
		return m.OfferNotes
	}
	return ""
}

func (m *ActivityOfferInfo) GetOfferAppendix() string {
	if m != nil {
		return m.OfferAppendix
	}
	return ""
}

// 活动
type ActivityInfo struct {
	ActivityId  uint32               `protobuf:"varint,1,req,name=activity_id,json=activityId" json:"activity_id"`
	Name        string               `protobuf:"bytes,2,opt,name=name" json:"name"`
	Description string               `protobuf:"bytes,3,opt,name=description" json:"description"`
	Banner      string               `protobuf:"bytes,4,opt,name=banner" json:"banner"`
	BeginTime   int64                `protobuf:"varint,5,opt,name=begin_time,json=beginTime" json:"begin_time"`
	EndTime     int64                `protobuf:"varint,6,opt,name=end_time,json=endTime" json:"end_time"`
	Status      uint32               `protobuf:"varint,7,opt,name=status" json:"status"`
	OffersList  []*ActivityOfferInfo `protobuf:"bytes,8,rep,name=offers_list,json=offersList" json:"offers_list,omitempty"`
}

func (m *ActivityInfo) Reset()                    { *m = ActivityInfo{} }
func (m *ActivityInfo) String() string            { return proto.CompactTextString(m) }
func (*ActivityInfo) ProtoMessage()               {}
func (*ActivityInfo) Descriptor() ([]byte, []int) { return fileDescriptorActivity, []int{3} }

func (m *ActivityInfo) GetActivityId() uint32 {
	if m != nil {
		return m.ActivityId
	}
	return 0
}

func (m *ActivityInfo) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *ActivityInfo) GetDescription() string {
	if m != nil {
		return m.Description
	}
	return ""
}

func (m *ActivityInfo) GetBanner() string {
	if m != nil {
		return m.Banner
	}
	return ""
}

func (m *ActivityInfo) GetBeginTime() int64 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *ActivityInfo) GetEndTime() int64 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *ActivityInfo) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *ActivityInfo) GetOffersList() []*ActivityOfferInfo {
	if m != nil {
		return m.OffersList
	}
	return nil
}

type CreateActivityReq struct {
	Activity *ActivityInfo `protobuf:"bytes,1,req,name=activity" json:"activity,omitempty"`
}

func (m *CreateActivityReq) Reset()                    { *m = CreateActivityReq{} }
func (m *CreateActivityReq) String() string            { return proto.CompactTextString(m) }
func (*CreateActivityReq) ProtoMessage()               {}
func (*CreateActivityReq) Descriptor() ([]byte, []int) { return fileDescriptorActivity, []int{4} }

func (m *CreateActivityReq) GetActivity() *ActivityInfo {
	if m != nil {
		return m.Activity
	}
	return nil
}

type CreateActivityResp struct {
	ActivityId uint32 `protobuf:"varint,1,req,name=activity_id,json=activityId" json:"activity_id"`
}

func (m *CreateActivityResp) Reset()                    { *m = CreateActivityResp{} }
func (m *CreateActivityResp) String() string            { return proto.CompactTextString(m) }
func (*CreateActivityResp) ProtoMessage()               {}
func (*CreateActivityResp) Descriptor() ([]byte, []int) { return fileDescriptorActivity, []int{5} }

func (m *CreateActivityResp) GetActivityId() uint32 {
	if m != nil {
		return m.ActivityId
	}
	return 0
}

type UpdateActivityReq struct {
	Activity *ActivityInfo `protobuf:"bytes,1,req,name=activity" json:"activity,omitempty"`
}

func (m *UpdateActivityReq) Reset()                    { *m = UpdateActivityReq{} }
func (m *UpdateActivityReq) String() string            { return proto.CompactTextString(m) }
func (*UpdateActivityReq) ProtoMessage()               {}
func (*UpdateActivityReq) Descriptor() ([]byte, []int) { return fileDescriptorActivity, []int{6} }

func (m *UpdateActivityReq) GetActivity() *ActivityInfo {
	if m != nil {
		return m.Activity
	}
	return nil
}

type UpdateActivityResp struct {
	ActivityId uint32 `protobuf:"varint,1,req,name=activity_id,json=activityId" json:"activity_id"`
}

func (m *UpdateActivityResp) Reset()                    { *m = UpdateActivityResp{} }
func (m *UpdateActivityResp) String() string            { return proto.CompactTextString(m) }
func (*UpdateActivityResp) ProtoMessage()               {}
func (*UpdateActivityResp) Descriptor() ([]byte, []int) { return fileDescriptorActivity, []int{7} }

func (m *UpdateActivityResp) GetActivityId() uint32 {
	if m != nil {
		return m.ActivityId
	}
	return 0
}

type DeleteActivityReq struct {
	ActivityId uint32   `protobuf:"varint,1,req,name=activity_id,json=activityId" json:"activity_id"`
	GameidList []uint32 `protobuf:"varint,2,rep,name=gameid_list,json=gameidList" json:"gameid_list,omitempty"`
}

func (m *DeleteActivityReq) Reset()                    { *m = DeleteActivityReq{} }
func (m *DeleteActivityReq) String() string            { return proto.CompactTextString(m) }
func (*DeleteActivityReq) ProtoMessage()               {}
func (*DeleteActivityReq) Descriptor() ([]byte, []int) { return fileDescriptorActivity, []int{8} }

func (m *DeleteActivityReq) GetActivityId() uint32 {
	if m != nil {
		return m.ActivityId
	}
	return 0
}

func (m *DeleteActivityReq) GetGameidList() []uint32 {
	if m != nil {
		return m.GameidList
	}
	return nil
}

type GetActivityListReq struct {
	Status    uint32 `protobuf:"varint,1,opt,name=status" json:"status"`
	FromIndex uint32 `protobuf:"varint,2,opt,name=from_index,json=fromIndex" json:"from_index"`
	Count     uint32 `protobuf:"varint,3,opt,name=count" json:"count"`
	Reverse   bool   `protobuf:"varint,4,opt,name=reverse" json:"reverse"`
	GameId    uint32 `protobuf:"varint,5,opt,name=game_id,json=gameId" json:"game_id"`
}

func (m *GetActivityListReq) Reset()                    { *m = GetActivityListReq{} }
func (m *GetActivityListReq) String() string            { return proto.CompactTextString(m) }
func (*GetActivityListReq) ProtoMessage()               {}
func (*GetActivityListReq) Descriptor() ([]byte, []int) { return fileDescriptorActivity, []int{9} }

func (m *GetActivityListReq) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *GetActivityListReq) GetFromIndex() uint32 {
	if m != nil {
		return m.FromIndex
	}
	return 0
}

func (m *GetActivityListReq) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *GetActivityListReq) GetReverse() bool {
	if m != nil {
		return m.Reverse
	}
	return false
}

func (m *GetActivityListReq) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

type GetActivityListResp struct {
	ActivityList []*ActivityInfo `protobuf:"bytes,1,rep,name=activity_list,json=activityList" json:"activity_list,omitempty"`
}

func (m *GetActivityListResp) Reset()                    { *m = GetActivityListResp{} }
func (m *GetActivityListResp) String() string            { return proto.CompactTextString(m) }
func (*GetActivityListResp) ProtoMessage()               {}
func (*GetActivityListResp) Descriptor() ([]byte, []int) { return fileDescriptorActivity, []int{10} }

func (m *GetActivityListResp) GetActivityList() []*ActivityInfo {
	if m != nil {
		return m.ActivityList
	}
	return nil
}

// 福利活动
type OfferEventElem struct {
	Id          uint32 `protobuf:"varint,1,opt,name=id" json:"id"`
	Name        string `protobuf:"bytes,2,opt,name=name" json:"name"`
	Description string `protobuf:"bytes,3,opt,name=description" json:"description"`
	Notes       string `protobuf:"bytes,4,opt,name=notes" json:"notes"`
	BeginTime   int64  `protobuf:"varint,5,opt,name=begin_time,json=beginTime" json:"begin_time"`
	EndTime     int64  `protobuf:"varint,6,opt,name=end_time,json=endTime" json:"end_time"`
	NowTime     int64  `protobuf:"varint,7,opt,name=now_time,json=nowTime" json:"now_time"`
	Banner      string `protobuf:"bytes,8,opt,name=banner" json:"banner"`
	DetailHref  string `protobuf:"bytes,9,opt,name=detail_href,json=detailHref" json:"detail_href"`
	Type        uint32 `protobuf:"varint,10,opt,name=type" json:"type"`
	Rank        uint32 `protobuf:"varint,11,opt,name=rank" json:"rank"`
	GameId      uint32 `protobuf:"varint,12,opt,name=game_id,json=gameId" json:"game_id"`
	Status      uint32 `protobuf:"varint,13,opt,name=status" json:"status"`
}

func (m *OfferEventElem) Reset()                    { *m = OfferEventElem{} }
func (m *OfferEventElem) String() string            { return proto.CompactTextString(m) }
func (*OfferEventElem) ProtoMessage()               {}
func (*OfferEventElem) Descriptor() ([]byte, []int) { return fileDescriptorActivity, []int{11} }

func (m *OfferEventElem) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *OfferEventElem) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *OfferEventElem) GetDescription() string {
	if m != nil {
		return m.Description
	}
	return ""
}

func (m *OfferEventElem) GetNotes() string {
	if m != nil {
		return m.Notes
	}
	return ""
}

func (m *OfferEventElem) GetBeginTime() int64 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *OfferEventElem) GetEndTime() int64 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *OfferEventElem) GetNowTime() int64 {
	if m != nil {
		return m.NowTime
	}
	return 0
}

func (m *OfferEventElem) GetBanner() string {
	if m != nil {
		return m.Banner
	}
	return ""
}

func (m *OfferEventElem) GetDetailHref() string {
	if m != nil {
		return m.DetailHref
	}
	return ""
}

func (m *OfferEventElem) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *OfferEventElem) GetRank() uint32 {
	if m != nil {
		return m.Rank
	}
	return 0
}

func (m *OfferEventElem) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *OfferEventElem) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

type CreateOfferEventReq struct {
	OfferEvent *OfferEventElem `protobuf:"bytes,1,req,name=offer_event,json=offerEvent" json:"offer_event,omitempty"`
}

func (m *CreateOfferEventReq) Reset()                    { *m = CreateOfferEventReq{} }
func (m *CreateOfferEventReq) String() string            { return proto.CompactTextString(m) }
func (*CreateOfferEventReq) ProtoMessage()               {}
func (*CreateOfferEventReq) Descriptor() ([]byte, []int) { return fileDescriptorActivity, []int{12} }

func (m *CreateOfferEventReq) GetOfferEvent() *OfferEventElem {
	if m != nil {
		return m.OfferEvent
	}
	return nil
}

type CreateOfferEventRsp struct {
	Id uint32 `protobuf:"varint,1,req,name=id" json:"id"`
}

func (m *CreateOfferEventRsp) Reset()                    { *m = CreateOfferEventRsp{} }
func (m *CreateOfferEventRsp) String() string            { return proto.CompactTextString(m) }
func (*CreateOfferEventRsp) ProtoMessage()               {}
func (*CreateOfferEventRsp) Descriptor() ([]byte, []int) { return fileDescriptorActivity, []int{13} }

func (m *CreateOfferEventRsp) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

type ModifyOfferEventReq struct {
	OfferEvent *OfferEventElem `protobuf:"bytes,1,req,name=offer_event,json=offerEvent" json:"offer_event,omitempty"`
}

func (m *ModifyOfferEventReq) Reset()                    { *m = ModifyOfferEventReq{} }
func (m *ModifyOfferEventReq) String() string            { return proto.CompactTextString(m) }
func (*ModifyOfferEventReq) ProtoMessage()               {}
func (*ModifyOfferEventReq) Descriptor() ([]byte, []int) { return fileDescriptorActivity, []int{14} }

func (m *ModifyOfferEventReq) GetOfferEvent() *OfferEventElem {
	if m != nil {
		return m.OfferEvent
	}
	return nil
}

type DelOfferEventReq struct {
	Id uint32 `protobuf:"varint,1,req,name=id" json:"id"`
}

func (m *DelOfferEventReq) Reset()                    { *m = DelOfferEventReq{} }
func (m *DelOfferEventReq) String() string            { return proto.CompactTextString(m) }
func (*DelOfferEventReq) ProtoMessage()               {}
func (*DelOfferEventReq) Descriptor() ([]byte, []int) { return fileDescriptorActivity, []int{15} }

func (m *DelOfferEventReq) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

type GetOfferEventReq struct {
	Status     uint32   `protobuf:"varint,1,req,name=status" json:"status"`
	Type       uint32   `protobuf:"varint,2,req,name=type" json:"type"`
	GameidList []uint32 `protobuf:"varint,3,rep,name=gameid_list,json=gameidList" json:"gameid_list,omitempty"`
	FromIndex  uint32   `protobuf:"varint,4,opt,name=from_index,json=fromIndex" json:"from_index"`
	Count      uint32   `protobuf:"varint,5,opt,name=count" json:"count"`
}

func (m *GetOfferEventReq) Reset()                    { *m = GetOfferEventReq{} }
func (m *GetOfferEventReq) String() string            { return proto.CompactTextString(m) }
func (*GetOfferEventReq) ProtoMessage()               {}
func (*GetOfferEventReq) Descriptor() ([]byte, []int) { return fileDescriptorActivity, []int{16} }

func (m *GetOfferEventReq) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *GetOfferEventReq) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *GetOfferEventReq) GetGameidList() []uint32 {
	if m != nil {
		return m.GameidList
	}
	return nil
}

func (m *GetOfferEventReq) GetFromIndex() uint32 {
	if m != nil {
		return m.FromIndex
	}
	return 0
}

func (m *GetOfferEventReq) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

type GetOfferEventRsp struct {
	EventList []*OfferEventElem `protobuf:"bytes,1,rep,name=event_list,json=eventList" json:"event_list,omitempty"`
}

func (m *GetOfferEventRsp) Reset()                    { *m = GetOfferEventRsp{} }
func (m *GetOfferEventRsp) String() string            { return proto.CompactTextString(m) }
func (*GetOfferEventRsp) ProtoMessage()               {}
func (*GetOfferEventRsp) Descriptor() ([]byte, []int) { return fileDescriptorActivity, []int{17} }

func (m *GetOfferEventRsp) GetEventList() []*OfferEventElem {
	if m != nil {
		return m.EventList
	}
	return nil
}

// 福利活动的游戏榜
type GameRankElem struct {
	GameId uint32 `protobuf:"varint,1,req,name=game_id,json=gameId" json:"game_id"`
	Rank   uint32 `protobuf:"varint,2,req,name=rank" json:"rank"`
}

func (m *GameRankElem) Reset()                    { *m = GameRankElem{} }
func (m *GameRankElem) String() string            { return proto.CompactTextString(m) }
func (*GameRankElem) ProtoMessage()               {}
func (*GameRankElem) Descriptor() ([]byte, []int) { return fileDescriptorActivity, []int{18} }

func (m *GameRankElem) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *GameRankElem) GetRank() uint32 {
	if m != nil {
		return m.Rank
	}
	return 0
}

type AddGameRankReq struct {
	Gamerank *GameRankElem `protobuf:"bytes,1,req,name=gamerank" json:"gamerank,omitempty"`
}

func (m *AddGameRankReq) Reset()                    { *m = AddGameRankReq{} }
func (m *AddGameRankReq) String() string            { return proto.CompactTextString(m) }
func (*AddGameRankReq) ProtoMessage()               {}
func (*AddGameRankReq) Descriptor() ([]byte, []int) { return fileDescriptorActivity, []int{19} }

func (m *AddGameRankReq) GetGamerank() *GameRankElem {
	if m != nil {
		return m.Gamerank
	}
	return nil
}

type ModifyGameRankReq struct {
	Gamerank *GameRankElem `protobuf:"bytes,1,req,name=gamerank" json:"gamerank,omitempty"`
}

func (m *ModifyGameRankReq) Reset()                    { *m = ModifyGameRankReq{} }
func (m *ModifyGameRankReq) String() string            { return proto.CompactTextString(m) }
func (*ModifyGameRankReq) ProtoMessage()               {}
func (*ModifyGameRankReq) Descriptor() ([]byte, []int) { return fileDescriptorActivity, []int{20} }

func (m *ModifyGameRankReq) GetGamerank() *GameRankElem {
	if m != nil {
		return m.Gamerank
	}
	return nil
}

type DelGameRankReq struct {
	GameidList []uint32 `protobuf:"varint,1,rep,name=gameid_list,json=gameidList" json:"gameid_list,omitempty"`
}

func (m *DelGameRankReq) Reset()                    { *m = DelGameRankReq{} }
func (m *DelGameRankReq) String() string            { return proto.CompactTextString(m) }
func (*DelGameRankReq) ProtoMessage()               {}
func (*DelGameRankReq) Descriptor() ([]byte, []int) { return fileDescriptorActivity, []int{21} }

func (m *DelGameRankReq) GetGameidList() []uint32 {
	if m != nil {
		return m.GameidList
	}
	return nil
}

type GetGameRankReq struct {
	FromIndex uint32 `protobuf:"varint,1,opt,name=from_index,json=fromIndex" json:"from_index"`
	Count     uint32 `protobuf:"varint,2,opt,name=count" json:"count"`
}

func (m *GetGameRankReq) Reset()                    { *m = GetGameRankReq{} }
func (m *GetGameRankReq) String() string            { return proto.CompactTextString(m) }
func (*GetGameRankReq) ProtoMessage()               {}
func (*GetGameRankReq) Descriptor() ([]byte, []int) { return fileDescriptorActivity, []int{22} }

func (m *GetGameRankReq) GetFromIndex() uint32 {
	if m != nil {
		return m.FromIndex
	}
	return 0
}

func (m *GetGameRankReq) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

type GetGameRankRsp struct {
	GamerankList []*GameRankElem `protobuf:"bytes,1,rep,name=gamerank_list,json=gamerankList" json:"gamerank_list,omitempty"`
}

func (m *GetGameRankRsp) Reset()                    { *m = GetGameRankRsp{} }
func (m *GetGameRankRsp) String() string            { return proto.CompactTextString(m) }
func (*GetGameRankRsp) ProtoMessage()               {}
func (*GetGameRankRsp) Descriptor() ([]byte, []int) { return fileDescriptorActivity, []int{23} }

func (m *GetGameRankRsp) GetGamerankList() []*GameRankElem {
	if m != nil {
		return m.GamerankList
	}
	return nil
}

type GetGameOfferReq struct {
	GameId uint32 `protobuf:"varint,1,req,name=game_id,json=gameId" json:"game_id"`
}

func (m *GetGameOfferReq) Reset()                    { *m = GetGameOfferReq{} }
func (m *GetGameOfferReq) String() string            { return proto.CompactTextString(m) }
func (*GetGameOfferReq) ProtoMessage()               {}
func (*GetGameOfferReq) Descriptor() ([]byte, []int) { return fileDescriptorActivity, []int{24} }

func (m *GetGameOfferReq) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

type GameOfferInfo struct {
	GameId          uint32 `protobuf:"varint,1,req,name=game_id,json=gameId" json:"game_id"`
	ActivityName    string `protobuf:"bytes,2,req,name=activity_name,json=activityName" json:"activity_name"`
	ActivityDesc    string `protobuf:"bytes,3,opt,name=activity_desc,json=activityDesc" json:"activity_desc"`
	OfferDesc       string `protobuf:"bytes,4,opt,name=offer_desc,json=offerDesc" json:"offer_desc"`
	OfferNotes      string `protobuf:"bytes,5,opt,name=offer_notes,json=offerNotes" json:"offer_notes"`
	OfferCategoryid uint32 `protobuf:"varint,6,opt,name=offer_categoryid,json=offerCategoryid" json:"offer_categoryid"`
}

func (m *GameOfferInfo) Reset()                    { *m = GameOfferInfo{} }
func (m *GameOfferInfo) String() string            { return proto.CompactTextString(m) }
func (*GameOfferInfo) ProtoMessage()               {}
func (*GameOfferInfo) Descriptor() ([]byte, []int) { return fileDescriptorActivity, []int{25} }

func (m *GameOfferInfo) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *GameOfferInfo) GetActivityName() string {
	if m != nil {
		return m.ActivityName
	}
	return ""
}

func (m *GameOfferInfo) GetActivityDesc() string {
	if m != nil {
		return m.ActivityDesc
	}
	return ""
}

func (m *GameOfferInfo) GetOfferDesc() string {
	if m != nil {
		return m.OfferDesc
	}
	return ""
}

func (m *GameOfferInfo) GetOfferNotes() string {
	if m != nil {
		return m.OfferNotes
	}
	return ""
}

func (m *GameOfferInfo) GetOfferCategoryid() uint32 {
	if m != nil {
		return m.OfferCategoryid
	}
	return 0
}

type GetGameOfferRsp struct {
	GameOffer []*GameOfferInfo `protobuf:"bytes,1,rep,name=game_offer,json=gameOffer" json:"game_offer,omitempty"`
}

func (m *GetGameOfferRsp) Reset()                    { *m = GetGameOfferRsp{} }
func (m *GetGameOfferRsp) String() string            { return proto.CompactTextString(m) }
func (*GetGameOfferRsp) ProtoMessage()               {}
func (*GetGameOfferRsp) Descriptor() ([]byte, []int) { return fileDescriptorActivity, []int{26} }

func (m *GetGameOfferRsp) GetGameOffer() []*GameOfferInfo {
	if m != nil {
		return m.GameOffer
	}
	return nil
}

type FirstVoucherAccountPassword struct {
	Account  string `protobuf:"bytes,1,req,name=account" json:"account"`
	Password string `protobuf:"bytes,2,req,name=password" json:"password"`
}

func (m *FirstVoucherAccountPassword) Reset()         { *m = FirstVoucherAccountPassword{} }
func (m *FirstVoucherAccountPassword) String() string { return proto.CompactTextString(m) }
func (*FirstVoucherAccountPassword) ProtoMessage()    {}
func (*FirstVoucherAccountPassword) Descriptor() ([]byte, []int) {
	return fileDescriptorActivity, []int{27}
}

func (m *FirstVoucherAccountPassword) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *FirstVoucherAccountPassword) GetPassword() string {
	if m != nil {
		return m.Password
	}
	return ""
}

type FirstVoucherProduct struct {
	ProductId  uint32 `protobuf:"varint,1,req,name=product_id,json=productId" json:"product_id"`
	Price      uint32 `protobuf:"varint,2,req,name=price" json:"price"`
	Worth      uint32 `protobuf:"varint,3,req,name=worth" json:"worth"`
	GameId     uint32 `protobuf:"varint,4,req,name=game_id,json=gameId" json:"game_id"`
	ExpireTime uint64 `protobuf:"varint,5,req,name=expire_time,json=expireTime" json:"expire_time"`
}

func (m *FirstVoucherProduct) Reset()                    { *m = FirstVoucherProduct{} }
func (m *FirstVoucherProduct) String() string            { return proto.CompactTextString(m) }
func (*FirstVoucherProduct) ProtoMessage()               {}
func (*FirstVoucherProduct) Descriptor() ([]byte, []int) { return fileDescriptorActivity, []int{28} }

func (m *FirstVoucherProduct) GetProductId() uint32 {
	if m != nil {
		return m.ProductId
	}
	return 0
}

func (m *FirstVoucherProduct) GetPrice() uint32 {
	if m != nil {
		return m.Price
	}
	return 0
}

func (m *FirstVoucherProduct) GetWorth() uint32 {
	if m != nil {
		return m.Worth
	}
	return 0
}

func (m *FirstVoucherProduct) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *FirstVoucherProduct) GetExpireTime() uint64 {
	if m != nil {
		return m.ExpireTime
	}
	return 0
}

type GetFirstVoucherProductsReq struct {
	Index uint32 `protobuf:"varint,1,req,name=index" json:"index"`
	Limit uint32 `protobuf:"varint,2,req,name=limit" json:"limit"`
}

func (m *GetFirstVoucherProductsReq) Reset()         { *m = GetFirstVoucherProductsReq{} }
func (m *GetFirstVoucherProductsReq) String() string { return proto.CompactTextString(m) }
func (*GetFirstVoucherProductsReq) ProtoMessage()    {}
func (*GetFirstVoucherProductsReq) Descriptor() ([]byte, []int) {
	return fileDescriptorActivity, []int{29}
}

func (m *GetFirstVoucherProductsReq) GetIndex() uint32 {
	if m != nil {
		return m.Index
	}
	return 0
}

func (m *GetFirstVoucherProductsReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type GetFirstVoucherProductsResp struct {
	ProductList []*FirstVoucherProduct `protobuf:"bytes,1,rep,name=product_list,json=productList" json:"product_list,omitempty"`
}

func (m *GetFirstVoucherProductsResp) Reset()         { *m = GetFirstVoucherProductsResp{} }
func (m *GetFirstVoucherProductsResp) String() string { return proto.CompactTextString(m) }
func (*GetFirstVoucherProductsResp) ProtoMessage()    {}
func (*GetFirstVoucherProductsResp) Descriptor() ([]byte, []int) {
	return fileDescriptorActivity, []int{30}
}

func (m *GetFirstVoucherProductsResp) GetProductList() []*FirstVoucherProduct {
	if m != nil {
		return m.ProductList
	}
	return nil
}

type AddFirstVoucherProductReq struct {
	Price      uint32 `protobuf:"varint,1,req,name=price" json:"price"`
	Worth      uint32 `protobuf:"varint,2,req,name=worth" json:"worth"`
	GameId     uint32 `protobuf:"varint,3,req,name=game_id,json=gameId" json:"game_id"`
	ExpireTime uint64 `protobuf:"varint,4,req,name=expire_time,json=expireTime" json:"expire_time"`
}

func (m *AddFirstVoucherProductReq) Reset()         { *m = AddFirstVoucherProductReq{} }
func (m *AddFirstVoucherProductReq) String() string { return proto.CompactTextString(m) }
func (*AddFirstVoucherProductReq) ProtoMessage()    {}
func (*AddFirstVoucherProductReq) Descriptor() ([]byte, []int) {
	return fileDescriptorActivity, []int{31}
}

func (m *AddFirstVoucherProductReq) GetPrice() uint32 {
	if m != nil {
		return m.Price
	}
	return 0
}

func (m *AddFirstVoucherProductReq) GetWorth() uint32 {
	if m != nil {
		return m.Worth
	}
	return 0
}

func (m *AddFirstVoucherProductReq) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *AddFirstVoucherProductReq) GetExpireTime() uint64 {
	if m != nil {
		return m.ExpireTime
	}
	return 0
}

type AddFirstVoucherProductResp struct {
	ProductId uint32 `protobuf:"varint,1,req,name=product_id,json=productId" json:"product_id"`
}

func (m *AddFirstVoucherProductResp) Reset()         { *m = AddFirstVoucherProductResp{} }
func (m *AddFirstVoucherProductResp) String() string { return proto.CompactTextString(m) }
func (*AddFirstVoucherProductResp) ProtoMessage()    {}
func (*AddFirstVoucherProductResp) Descriptor() ([]byte, []int) {
	return fileDescriptorActivity, []int{32}
}

func (m *AddFirstVoucherProductResp) GetProductId() uint32 {
	if m != nil {
		return m.ProductId
	}
	return 0
}

type ModifyFirstVoucherProductReq struct {
	ProductId  uint32 `protobuf:"varint,1,req,name=product_id,json=productId" json:"product_id"`
	Invalid    uint32 `protobuf:"varint,2,opt,name=invalid" json:"invalid"`
	Price      uint32 `protobuf:"varint,3,opt,name=price" json:"price"`
	Worth      uint32 `protobuf:"varint,4,opt,name=worth" json:"worth"`
	ExpireTime uint64 `protobuf:"varint,5,opt,name=expire_time,json=expireTime" json:"expire_time"`
}

func (m *ModifyFirstVoucherProductReq) Reset()         { *m = ModifyFirstVoucherProductReq{} }
func (m *ModifyFirstVoucherProductReq) String() string { return proto.CompactTextString(m) }
func (*ModifyFirstVoucherProductReq) ProtoMessage()    {}
func (*ModifyFirstVoucherProductReq) Descriptor() ([]byte, []int) {
	return fileDescriptorActivity, []int{33}
}

func (m *ModifyFirstVoucherProductReq) GetProductId() uint32 {
	if m != nil {
		return m.ProductId
	}
	return 0
}

func (m *ModifyFirstVoucherProductReq) GetInvalid() uint32 {
	if m != nil {
		return m.Invalid
	}
	return 0
}

func (m *ModifyFirstVoucherProductReq) GetPrice() uint32 {
	if m != nil {
		return m.Price
	}
	return 0
}

func (m *ModifyFirstVoucherProductReq) GetWorth() uint32 {
	if m != nil {
		return m.Worth
	}
	return 0
}

func (m *ModifyFirstVoucherProductReq) GetExpireTime() uint64 {
	if m != nil {
		return m.ExpireTime
	}
	return 0
}

type GetGameFirstVoucherProductReq struct {
	GameId uint32 `protobuf:"varint,1,req,name=game_id,json=gameId" json:"game_id"`
}

func (m *GetGameFirstVoucherProductReq) Reset()         { *m = GetGameFirstVoucherProductReq{} }
func (m *GetGameFirstVoucherProductReq) String() string { return proto.CompactTextString(m) }
func (*GetGameFirstVoucherProductReq) ProtoMessage()    {}
func (*GetGameFirstVoucherProductReq) Descriptor() ([]byte, []int) {
	return fileDescriptorActivity, []int{34}
}

func (m *GetGameFirstVoucherProductReq) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

type GetGameFirstVoucherProductResp struct {
	Product *FirstVoucherProduct `protobuf:"bytes,1,req,name=product" json:"product,omitempty"`
}

func (m *GetGameFirstVoucherProductResp) Reset()         { *m = GetGameFirstVoucherProductResp{} }
func (m *GetGameFirstVoucherProductResp) String() string { return proto.CompactTextString(m) }
func (*GetGameFirstVoucherProductResp) ProtoMessage()    {}
func (*GetGameFirstVoucherProductResp) Descriptor() ([]byte, []int) {
	return fileDescriptorActivity, []int{35}
}

func (m *GetGameFirstVoucherProductResp) GetProduct() *FirstVoucherProduct {
	if m != nil {
		return m.Product
	}
	return nil
}

type DeleteFirstVoucherProductReq struct {
	ProductId uint32 `protobuf:"varint,1,req,name=product_id,json=productId" json:"product_id"`
}

func (m *DeleteFirstVoucherProductReq) Reset()         { *m = DeleteFirstVoucherProductReq{} }
func (m *DeleteFirstVoucherProductReq) String() string { return proto.CompactTextString(m) }
func (*DeleteFirstVoucherProductReq) ProtoMessage()    {}
func (*DeleteFirstVoucherProductReq) Descriptor() ([]byte, []int) {
	return fileDescriptorActivity, []int{36}
}

func (m *DeleteFirstVoucherProductReq) GetProductId() uint32 {
	if m != nil {
		return m.ProductId
	}
	return 0
}

type FirstVoucherItem struct {
	ItemHash   string `protobuf:"bytes,1,req,name=item_hash,json=itemHash" json:"item_hash"`
	ItemBinary []byte `protobuf:"bytes,2,req,name=item_binary,json=itemBinary" json:"item_binary"`
}

func (m *FirstVoucherItem) Reset()                    { *m = FirstVoucherItem{} }
func (m *FirstVoucherItem) String() string            { return proto.CompactTextString(m) }
func (*FirstVoucherItem) ProtoMessage()               {}
func (*FirstVoucherItem) Descriptor() ([]byte, []int) { return fileDescriptorActivity, []int{37} }

func (m *FirstVoucherItem) GetItemHash() string {
	if m != nil {
		return m.ItemHash
	}
	return ""
}

func (m *FirstVoucherItem) GetItemBinary() []byte {
	if m != nil {
		return m.ItemBinary
	}
	return nil
}

type AddFirstVoucherItemReq struct {
	ItemList  []*FirstVoucherItem `protobuf:"bytes,1,rep,name=item_list,json=itemList" json:"item_list,omitempty"`
	ProductId uint32              `protobuf:"varint,2,req,name=product_id,json=productId" json:"product_id"`
}

func (m *AddFirstVoucherItemReq) Reset()                    { *m = AddFirstVoucherItemReq{} }
func (m *AddFirstVoucherItemReq) String() string            { return proto.CompactTextString(m) }
func (*AddFirstVoucherItemReq) ProtoMessage()               {}
func (*AddFirstVoucherItemReq) Descriptor() ([]byte, []int) { return fileDescriptorActivity, []int{38} }

func (m *AddFirstVoucherItemReq) GetItemList() []*FirstVoucherItem {
	if m != nil {
		return m.ItemList
	}
	return nil
}

func (m *AddFirstVoucherItemReq) GetProductId() uint32 {
	if m != nil {
		return m.ProductId
	}
	return 0
}

type AddFirstVoucherItemResp struct {
	SuccessProductItemList []*FirstVoucherItem `protobuf:"bytes,1,rep,name=success_product_item_list,json=successProductItemList" json:"success_product_item_list,omitempty"`
	FailedProductItemList  []*FirstVoucherItem `protobuf:"bytes,2,rep,name=failed_product_item_list,json=failedProductItemList" json:"failed_product_item_list,omitempty"`
}

func (m *AddFirstVoucherItemResp) Reset()                    { *m = AddFirstVoucherItemResp{} }
func (m *AddFirstVoucherItemResp) String() string            { return proto.CompactTextString(m) }
func (*AddFirstVoucherItemResp) ProtoMessage()               {}
func (*AddFirstVoucherItemResp) Descriptor() ([]byte, []int) { return fileDescriptorActivity, []int{39} }

func (m *AddFirstVoucherItemResp) GetSuccessProductItemList() []*FirstVoucherItem {
	if m != nil {
		return m.SuccessProductItemList
	}
	return nil
}

func (m *AddFirstVoucherItemResp) GetFailedProductItemList() []*FirstVoucherItem {
	if m != nil {
		return m.FailedProductItemList
	}
	return nil
}

type GetFirstVoucherItemReq struct {
	ProductId uint32 `protobuf:"varint,1,opt,name=product_id,json=productId" json:"product_id"`
	Status    uint32 `protobuf:"varint,2,opt,name=status" json:"status"`
}

func (m *GetFirstVoucherItemReq) Reset()                    { *m = GetFirstVoucherItemReq{} }
func (m *GetFirstVoucherItemReq) String() string            { return proto.CompactTextString(m) }
func (*GetFirstVoucherItemReq) ProtoMessage()               {}
func (*GetFirstVoucherItemReq) Descriptor() ([]byte, []int) { return fileDescriptorActivity, []int{40} }

func (m *GetFirstVoucherItemReq) GetProductId() uint32 {
	if m != nil {
		return m.ProductId
	}
	return 0
}

func (m *GetFirstVoucherItemReq) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

type GetFirstVoucherItemResp struct {
	ItemList []*FirstVoucherItem `protobuf:"bytes,1,rep,name=item_list,json=itemList" json:"item_list,omitempty"`
}

func (m *GetFirstVoucherItemResp) Reset()                    { *m = GetFirstVoucherItemResp{} }
func (m *GetFirstVoucherItemResp) String() string            { return proto.CompactTextString(m) }
func (*GetFirstVoucherItemResp) ProtoMessage()               {}
func (*GetFirstVoucherItemResp) Descriptor() ([]byte, []int) { return fileDescriptorActivity, []int{41} }

func (m *GetFirstVoucherItemResp) GetItemList() []*FirstVoucherItem {
	if m != nil {
		return m.ItemList
	}
	return nil
}

type DeleteFirstVoucherItemReq struct {
	ProductId    uint32   `protobuf:"varint,1,req,name=product_id,json=productId" json:"product_id"`
	ItemHashList []string `protobuf:"bytes,2,rep,name=item_hash_list,json=itemHashList" json:"item_hash_list,omitempty"`
}

func (m *DeleteFirstVoucherItemReq) Reset()         { *m = DeleteFirstVoucherItemReq{} }
func (m *DeleteFirstVoucherItemReq) String() string { return proto.CompactTextString(m) }
func (*DeleteFirstVoucherItemReq) ProtoMessage()    {}
func (*DeleteFirstVoucherItemReq) Descriptor() ([]byte, []int) {
	return fileDescriptorActivity, []int{42}
}

func (m *DeleteFirstVoucherItemReq) GetProductId() uint32 {
	if m != nil {
		return m.ProductId
	}
	return 0
}

func (m *DeleteFirstVoucherItemReq) GetItemHashList() []string {
	if m != nil {
		return m.ItemHashList
	}
	return nil
}

type FirstVoucherUserItem struct {
	Uid             uint32               `protobuf:"varint,1,req,name=uid" json:"uid"`
	Timestamp       uint64               `protobuf:"varint,2,req,name=timestamp" json:"timestamp"`
	ProductSnapshot *FirstVoucherProduct `protobuf:"bytes,3,req,name=product_snapshot,json=productSnapshot" json:"product_snapshot,omitempty"`
	ItemSnapshot    *FirstVoucherItem    `protobuf:"bytes,4,req,name=item_snapshot,json=itemSnapshot" json:"item_snapshot,omitempty"`
	OrderId         string               `protobuf:"bytes,5,req,name=order_id,json=orderId" json:"order_id"`
}

func (m *FirstVoucherUserItem) Reset()                    { *m = FirstVoucherUserItem{} }
func (m *FirstVoucherUserItem) String() string            { return proto.CompactTextString(m) }
func (*FirstVoucherUserItem) ProtoMessage()               {}
func (*FirstVoucherUserItem) Descriptor() ([]byte, []int) { return fileDescriptorActivity, []int{43} }

func (m *FirstVoucherUserItem) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *FirstVoucherUserItem) GetTimestamp() uint64 {
	if m != nil {
		return m.Timestamp
	}
	return 0
}

func (m *FirstVoucherUserItem) GetProductSnapshot() *FirstVoucherProduct {
	if m != nil {
		return m.ProductSnapshot
	}
	return nil
}

func (m *FirstVoucherUserItem) GetItemSnapshot() *FirstVoucherItem {
	if m != nil {
		return m.ItemSnapshot
	}
	return nil
}

func (m *FirstVoucherUserItem) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

type PurchaseFirstVoucherItemReq struct {
	Uid       uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	ProductId uint32 `protobuf:"varint,2,req,name=product_id,json=productId" json:"product_id"`
}

func (m *PurchaseFirstVoucherItemReq) Reset()         { *m = PurchaseFirstVoucherItemReq{} }
func (m *PurchaseFirstVoucherItemReq) String() string { return proto.CompactTextString(m) }
func (*PurchaseFirstVoucherItemReq) ProtoMessage()    {}
func (*PurchaseFirstVoucherItemReq) Descriptor() ([]byte, []int) {
	return fileDescriptorActivity, []int{44}
}

func (m *PurchaseFirstVoucherItemReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *PurchaseFirstVoucherItemReq) GetProductId() uint32 {
	if m != nil {
		return m.ProductId
	}
	return 0
}

type PurchaseFirstVoucherItemResp struct {
	FirstVoucherItem *FirstVoucherUserItem `protobuf:"bytes,1,req,name=first_voucher_item,json=firstVoucherItem" json:"first_voucher_item,omitempty"`
}

func (m *PurchaseFirstVoucherItemResp) Reset()         { *m = PurchaseFirstVoucherItemResp{} }
func (m *PurchaseFirstVoucherItemResp) String() string { return proto.CompactTextString(m) }
func (*PurchaseFirstVoucherItemResp) ProtoMessage()    {}
func (*PurchaseFirstVoucherItemResp) Descriptor() ([]byte, []int) {
	return fileDescriptorActivity, []int{45}
}

func (m *PurchaseFirstVoucherItemResp) GetFirstVoucherItem() *FirstVoucherUserItem {
	if m != nil {
		return m.FirstVoucherItem
	}
	return nil
}

type GetFirstVoucherUserItemReq struct {
	Uid uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
}

func (m *GetFirstVoucherUserItemReq) Reset()         { *m = GetFirstVoucherUserItemReq{} }
func (m *GetFirstVoucherUserItemReq) String() string { return proto.CompactTextString(m) }
func (*GetFirstVoucherUserItemReq) ProtoMessage()    {}
func (*GetFirstVoucherUserItemReq) Descriptor() ([]byte, []int) {
	return fileDescriptorActivity, []int{46}
}

func (m *GetFirstVoucherUserItemReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetFirstVoucherUserItemResp struct {
	UserItemList []*FirstVoucherUserItem `protobuf:"bytes,1,rep,name=user_item_list,json=userItemList" json:"user_item_list,omitempty"`
}

func (m *GetFirstVoucherUserItemResp) Reset()         { *m = GetFirstVoucherUserItemResp{} }
func (m *GetFirstVoucherUserItemResp) String() string { return proto.CompactTextString(m) }
func (*GetFirstVoucherUserItemResp) ProtoMessage()    {}
func (*GetFirstVoucherUserItemResp) Descriptor() ([]byte, []int) {
	return fileDescriptorActivity, []int{47}
}

func (m *GetFirstVoucherUserItemResp) GetUserItemList() []*FirstVoucherUserItem {
	if m != nil {
		return m.UserItemList
	}
	return nil
}

type ActBannerInfo struct {
	Id             uint32 `protobuf:"varint,1,req,name=id" json:"id"`
	ActionUrl      string `protobuf:"bytes,2,req,name=action_url,json=actionUrl" json:"action_url"`
	Title          string `protobuf:"bytes,3,opt,name=title" json:"title"`
	SubTitle       string `protobuf:"bytes,4,opt,name=sub_title,json=subTitle" json:"sub_title"`
	IconUrl        string `protobuf:"bytes,5,opt,name=icon_url,json=iconUrl" json:"icon_url"`
	CoverUrl       string `protobuf:"bytes,6,opt,name=cover_url,json=coverUrl" json:"cover_url"`
	AndroidVersion uint32 `protobuf:"varint,7,opt,name=android_version,json=androidVersion" json:"android_version"`
	IosVersion     uint32 `protobuf:"varint,8,opt,name=ios_version,json=iosVersion" json:"ios_version"`
	Rank           uint32 `protobuf:"varint,9,opt,name=rank" json:"rank"`
}

func (m *ActBannerInfo) Reset()                    { *m = ActBannerInfo{} }
func (m *ActBannerInfo) String() string            { return proto.CompactTextString(m) }
func (*ActBannerInfo) ProtoMessage()               {}
func (*ActBannerInfo) Descriptor() ([]byte, []int) { return fileDescriptorActivity, []int{48} }

func (m *ActBannerInfo) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *ActBannerInfo) GetActionUrl() string {
	if m != nil {
		return m.ActionUrl
	}
	return ""
}

func (m *ActBannerInfo) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *ActBannerInfo) GetSubTitle() string {
	if m != nil {
		return m.SubTitle
	}
	return ""
}

func (m *ActBannerInfo) GetIconUrl() string {
	if m != nil {
		return m.IconUrl
	}
	return ""
}

func (m *ActBannerInfo) GetCoverUrl() string {
	if m != nil {
		return m.CoverUrl
	}
	return ""
}

func (m *ActBannerInfo) GetAndroidVersion() uint32 {
	if m != nil {
		return m.AndroidVersion
	}
	return 0
}

func (m *ActBannerInfo) GetIosVersion() uint32 {
	if m != nil {
		return m.IosVersion
	}
	return 0
}

func (m *ActBannerInfo) GetRank() uint32 {
	if m != nil {
		return m.Rank
	}
	return 0
}

type UpdateActBannerReq struct {
	Banner *ActBannerInfo `protobuf:"bytes,1,req,name=banner" json:"banner,omitempty"`
}

func (m *UpdateActBannerReq) Reset()                    { *m = UpdateActBannerReq{} }
func (m *UpdateActBannerReq) String() string            { return proto.CompactTextString(m) }
func (*UpdateActBannerReq) ProtoMessage()               {}
func (*UpdateActBannerReq) Descriptor() ([]byte, []int) { return fileDescriptorActivity, []int{49} }

func (m *UpdateActBannerReq) GetBanner() *ActBannerInfo {
	if m != nil {
		return m.Banner
	}
	return nil
}

type GetActBannersResp struct {
	BannerList []*ActBannerInfo `protobuf:"bytes,1,rep,name=banner_list,json=bannerList" json:"banner_list,omitempty"`
}

func (m *GetActBannersResp) Reset()                    { *m = GetActBannersResp{} }
func (m *GetActBannersResp) String() string            { return proto.CompactTextString(m) }
func (*GetActBannersResp) ProtoMessage()               {}
func (*GetActBannersResp) Descriptor() ([]byte, []int) { return fileDescriptorActivity, []int{50} }

func (m *GetActBannersResp) GetBannerList() []*ActBannerInfo {
	if m != nil {
		return m.BannerList
	}
	return nil
}

type DelActBannerReq struct {
	Id uint32 `protobuf:"varint,1,req,name=id" json:"id"`
}

func (m *DelActBannerReq) Reset()                    { *m = DelActBannerReq{} }
func (m *DelActBannerReq) String() string            { return proto.CompactTextString(m) }
func (*DelActBannerReq) ProtoMessage()               {}
func (*DelActBannerReq) Descriptor() ([]byte, []int) { return fileDescriptorActivity, []int{51} }

func (m *DelActBannerReq) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

type GetActBannerOpTimeResp struct {
	OpTime uint32 `protobuf:"varint,1,req,name=op_time,json=opTime" json:"op_time"`
}

func (m *GetActBannerOpTimeResp) Reset()                    { *m = GetActBannerOpTimeResp{} }
func (m *GetActBannerOpTimeResp) String() string            { return proto.CompactTextString(m) }
func (*GetActBannerOpTimeResp) ProtoMessage()               {}
func (*GetActBannerOpTimeResp) Descriptor() ([]byte, []int) { return fileDescriptorActivity, []int{52} }

func (m *GetActBannerOpTimeResp) GetOpTime() uint32 {
	if m != nil {
		return m.OpTime
	}
	return 0
}

type ActSplashScreenInfo struct {
	Id           uint32 `protobuf:"varint,1,req,name=id" json:"id"`
	ScreenUrl    string `protobuf:"bytes,2,req,name=screen_url,json=screenUrl" json:"screen_url"`
	ActionUrl    string `protobuf:"bytes,3,req,name=action_url,json=actionUrl" json:"action_url"`
	StartTime    string `protobuf:"bytes,4,req,name=start_time,json=startTime" json:"start_time"`
	EndTime      string `protobuf:"bytes,5,req,name=end_time,json=endTime" json:"end_time"`
	ContinueTime uint32 `protobuf:"varint,6,req,name=continue_time,json=continueTime" json:"continue_time"`
	ClientType   uint32 `protobuf:"varint,7,opt,name=client_type,json=clientType" json:"client_type"`
	AppId        uint32 `protobuf:"varint,8,opt,name=app_id,json=appId" json:"app_id"`
	MarketId     uint32 `protobuf:"varint,9,opt,name=market_id,json=marketId" json:"market_id"`
}

func (m *ActSplashScreenInfo) Reset()                    { *m = ActSplashScreenInfo{} }
func (m *ActSplashScreenInfo) String() string            { return proto.CompactTextString(m) }
func (*ActSplashScreenInfo) ProtoMessage()               {}
func (*ActSplashScreenInfo) Descriptor() ([]byte, []int) { return fileDescriptorActivity, []int{53} }

func (m *ActSplashScreenInfo) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *ActSplashScreenInfo) GetScreenUrl() string {
	if m != nil {
		return m.ScreenUrl
	}
	return ""
}

func (m *ActSplashScreenInfo) GetActionUrl() string {
	if m != nil {
		return m.ActionUrl
	}
	return ""
}

func (m *ActSplashScreenInfo) GetStartTime() string {
	if m != nil {
		return m.StartTime
	}
	return ""
}

func (m *ActSplashScreenInfo) GetEndTime() string {
	if m != nil {
		return m.EndTime
	}
	return ""
}

func (m *ActSplashScreenInfo) GetContinueTime() uint32 {
	if m != nil {
		return m.ContinueTime
	}
	return 0
}

func (m *ActSplashScreenInfo) GetClientType() uint32 {
	if m != nil {
		return m.ClientType
	}
	return 0
}

func (m *ActSplashScreenInfo) GetAppId() uint32 {
	if m != nil {
		return m.AppId
	}
	return 0
}

func (m *ActSplashScreenInfo) GetMarketId() uint32 {
	if m != nil {
		return m.MarketId
	}
	return 0
}

type UpdateActSplashScreenReq struct {
	SplashScreen *ActSplashScreenInfo `protobuf:"bytes,1,req,name=splash_screen,json=splashScreen" json:"splash_screen,omitempty"`
}

func (m *UpdateActSplashScreenReq) Reset()         { *m = UpdateActSplashScreenReq{} }
func (m *UpdateActSplashScreenReq) String() string { return proto.CompactTextString(m) }
func (*UpdateActSplashScreenReq) ProtoMessage()    {}
func (*UpdateActSplashScreenReq) Descriptor() ([]byte, []int) {
	return fileDescriptorActivity, []int{54}
}

func (m *UpdateActSplashScreenReq) GetSplashScreen() *ActSplashScreenInfo {
	if m != nil {
		return m.SplashScreen
	}
	return nil
}

type GetActSplashScreenResp struct {
	SplashScreen     *ActSplashScreenInfo   `protobuf:"bytes,1,opt,name=splash_screen,json=splashScreen" json:"splash_screen,omitempty"`
	SplashScreenIos  *ActSplashScreenInfo   `protobuf:"bytes,2,opt,name=splash_screen_ios,json=splashScreenIos" json:"splash_screen_ios,omitempty"`
	SplashScreenList []*ActSplashScreenInfo `protobuf:"bytes,3,rep,name=splash_screen_list,json=splashScreenList" json:"splash_screen_list,omitempty"`
}

func (m *GetActSplashScreenResp) Reset()                    { *m = GetActSplashScreenResp{} }
func (m *GetActSplashScreenResp) String() string            { return proto.CompactTextString(m) }
func (*GetActSplashScreenResp) ProtoMessage()               {}
func (*GetActSplashScreenResp) Descriptor() ([]byte, []int) { return fileDescriptorActivity, []int{55} }

func (m *GetActSplashScreenResp) GetSplashScreen() *ActSplashScreenInfo {
	if m != nil {
		return m.SplashScreen
	}
	return nil
}

func (m *GetActSplashScreenResp) GetSplashScreenIos() *ActSplashScreenInfo {
	if m != nil {
		return m.SplashScreenIos
	}
	return nil
}

func (m *GetActSplashScreenResp) GetSplashScreenList() []*ActSplashScreenInfo {
	if m != nil {
		return m.SplashScreenList
	}
	return nil
}

type DelActSplashScreenReq struct {
	Id uint32 `protobuf:"varint,1,req,name=id" json:"id"`
}

func (m *DelActSplashScreenReq) Reset()                    { *m = DelActSplashScreenReq{} }
func (m *DelActSplashScreenReq) String() string            { return proto.CompactTextString(m) }
func (*DelActSplashScreenReq) ProtoMessage()               {}
func (*DelActSplashScreenReq) Descriptor() ([]byte, []int) { return fileDescriptorActivity, []int{56} }

func (m *DelActSplashScreenReq) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

type ActSplashScreenOpTime struct {
	AppId      uint32 `protobuf:"varint,1,opt,name=app_id,json=appId" json:"app_id"`
	MarketId   uint32 `protobuf:"varint,2,opt,name=market_id,json=marketId" json:"market_id"`
	ClientType uint32 `protobuf:"varint,3,opt,name=client_type,json=clientType" json:"client_type"`
	OpTime     uint32 `protobuf:"varint,4,opt,name=op_time,json=opTime" json:"op_time"`
}

func (m *ActSplashScreenOpTime) Reset()                    { *m = ActSplashScreenOpTime{} }
func (m *ActSplashScreenOpTime) String() string            { return proto.CompactTextString(m) }
func (*ActSplashScreenOpTime) ProtoMessage()               {}
func (*ActSplashScreenOpTime) Descriptor() ([]byte, []int) { return fileDescriptorActivity, []int{57} }

func (m *ActSplashScreenOpTime) GetAppId() uint32 {
	if m != nil {
		return m.AppId
	}
	return 0
}

func (m *ActSplashScreenOpTime) GetMarketId() uint32 {
	if m != nil {
		return m.MarketId
	}
	return 0
}

func (m *ActSplashScreenOpTime) GetClientType() uint32 {
	if m != nil {
		return m.ClientType
	}
	return 0
}

func (m *ActSplashScreenOpTime) GetOpTime() uint32 {
	if m != nil {
		return m.OpTime
	}
	return 0
}

type GetActSplashScreenOpTimeResp struct {
	OpTime     uint32                   `protobuf:"varint,1,opt,name=op_time,json=opTime" json:"op_time"`
	OpTimeIos  uint32                   `protobuf:"varint,2,opt,name=op_time_ios,json=opTimeIos" json:"op_time_ios"`
	OptimeList []*ActSplashScreenOpTime `protobuf:"bytes,3,rep,name=optime_list,json=optimeList" json:"optime_list,omitempty"`
}

func (m *GetActSplashScreenOpTimeResp) Reset()         { *m = GetActSplashScreenOpTimeResp{} }
func (m *GetActSplashScreenOpTimeResp) String() string { return proto.CompactTextString(m) }
func (*GetActSplashScreenOpTimeResp) ProtoMessage()    {}
func (*GetActSplashScreenOpTimeResp) Descriptor() ([]byte, []int) {
	return fileDescriptorActivity, []int{58}
}

func (m *GetActSplashScreenOpTimeResp) GetOpTime() uint32 {
	if m != nil {
		return m.OpTime
	}
	return 0
}

func (m *GetActSplashScreenOpTimeResp) GetOpTimeIos() uint32 {
	if m != nil {
		return m.OpTimeIos
	}
	return 0
}

func (m *GetActSplashScreenOpTimeResp) GetOptimeList() []*ActSplashScreenOpTime {
	if m != nil {
		return m.OptimeList
	}
	return nil
}

type GetGamePreorderResultReq struct {
	GameId uint32 `protobuf:"varint,1,req,name=game_id,json=gameId" json:"game_id"`
}

func (m *GetGamePreorderResultReq) Reset()         { *m = GetGamePreorderResultReq{} }
func (m *GetGamePreorderResultReq) String() string { return proto.CompactTextString(m) }
func (*GetGamePreorderResultReq) ProtoMessage()    {}
func (*GetGamePreorderResultReq) Descriptor() ([]byte, []int) {
	return fileDescriptorActivity, []int{59}
}

func (m *GetGamePreorderResultReq) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

type GamePreorderResult struct {
	ObjId uint32 `protobuf:"varint,1,req,name=obj_id,json=objId" json:"obj_id"`
	Count uint32 `protobuf:"varint,2,req,name=count" json:"count"`
}

func (m *GamePreorderResult) Reset()                    { *m = GamePreorderResult{} }
func (m *GamePreorderResult) String() string            { return proto.CompactTextString(m) }
func (*GamePreorderResult) ProtoMessage()               {}
func (*GamePreorderResult) Descriptor() ([]byte, []int) { return fileDescriptorActivity, []int{60} }

func (m *GamePreorderResult) GetObjId() uint32 {
	if m != nil {
		return m.ObjId
	}
	return 0
}

func (m *GamePreorderResult) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

type GetGamePreorderResultResp struct {
	ResultList []*GamePreorderResult `protobuf:"bytes,1,rep,name=result_list,json=resultList" json:"result_list,omitempty"`
}

func (m *GetGamePreorderResultResp) Reset()         { *m = GetGamePreorderResultResp{} }
func (m *GetGamePreorderResultResp) String() string { return proto.CompactTextString(m) }
func (*GetGamePreorderResultResp) ProtoMessage()    {}
func (*GetGamePreorderResultResp) Descriptor() ([]byte, []int) {
	return fileDescriptorActivity, []int{61}
}

func (m *GetGamePreorderResultResp) GetResultList() []*GamePreorderResult {
	if m != nil {
		return m.ResultList
	}
	return nil
}

type GetGamePreorderUserSpptReq struct {
	GameId uint32 `protobuf:"varint,1,req,name=game_id,json=gameId" json:"game_id"`
	Uid    uint32 `protobuf:"varint,2,req,name=uid" json:"uid"`
}

func (m *GetGamePreorderUserSpptReq) Reset()         { *m = GetGamePreorderUserSpptReq{} }
func (m *GetGamePreorderUserSpptReq) String() string { return proto.CompactTextString(m) }
func (*GetGamePreorderUserSpptReq) ProtoMessage()    {}
func (*GetGamePreorderUserSpptReq) Descriptor() ([]byte, []int) {
	return fileDescriptorActivity, []int{62}
}

func (m *GetGamePreorderUserSpptReq) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *GetGamePreorderUserSpptReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetGamePreorderUserSpptResp struct {
	ObjId uint32 `protobuf:"varint,1,req,name=obj_id,json=objId" json:"obj_id"`
	Count uint32 `protobuf:"varint,2,req,name=count" json:"count"`
}

func (m *GetGamePreorderUserSpptResp) Reset()         { *m = GetGamePreorderUserSpptResp{} }
func (m *GetGamePreorderUserSpptResp) String() string { return proto.CompactTextString(m) }
func (*GetGamePreorderUserSpptResp) ProtoMessage()    {}
func (*GetGamePreorderUserSpptResp) Descriptor() ([]byte, []int) {
	return fileDescriptorActivity, []int{63}
}

func (m *GetGamePreorderUserSpptResp) GetObjId() uint32 {
	if m != nil {
		return m.ObjId
	}
	return 0
}

func (m *GetGamePreorderUserSpptResp) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

type SetGamePreorderUserSpptReq struct {
	GameId uint32 `protobuf:"varint,1,req,name=game_id,json=gameId" json:"game_id"`
	Uid    uint32 `protobuf:"varint,2,req,name=uid" json:"uid"`
	ObjId  uint32 `protobuf:"varint,3,req,name=obj_id,json=objId" json:"obj_id"`
	Count  uint32 `protobuf:"varint,4,req,name=count" json:"count"`
}

func (m *SetGamePreorderUserSpptReq) Reset()         { *m = SetGamePreorderUserSpptReq{} }
func (m *SetGamePreorderUserSpptReq) String() string { return proto.CompactTextString(m) }
func (*SetGamePreorderUserSpptReq) ProtoMessage()    {}
func (*SetGamePreorderUserSpptReq) Descriptor() ([]byte, []int) {
	return fileDescriptorActivity, []int{64}
}

func (m *SetGamePreorderUserSpptReq) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *SetGamePreorderUserSpptReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SetGamePreorderUserSpptReq) GetObjId() uint32 {
	if m != nil {
		return m.ObjId
	}
	return 0
}

func (m *SetGamePreorderUserSpptReq) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

type InviterInfo struct {
	Uid       uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	InviteNum uint32 `protobuf:"varint,2,req,name=invite_num,json=inviteNum" json:"invite_num"`
}

func (m *InviterInfo) Reset()                    { *m = InviterInfo{} }
func (m *InviterInfo) String() string            { return proto.CompactTextString(m) }
func (*InviterInfo) ProtoMessage()               {}
func (*InviterInfo) Descriptor() ([]byte, []int) { return fileDescriptorActivity, []int{65} }

func (m *InviterInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *InviterInfo) GetInviteNum() uint32 {
	if m != nil {
		return m.InviteNum
	}
	return 0
}

type GetQQCarActivityRankResp struct {
	InviteInfos []*InviterInfo `protobuf:"bytes,1,rep,name=invite_infos,json=inviteInfos" json:"invite_infos,omitempty"`
}

func (m *GetQQCarActivityRankResp) Reset()         { *m = GetQQCarActivityRankResp{} }
func (m *GetQQCarActivityRankResp) String() string { return proto.CompactTextString(m) }
func (*GetQQCarActivityRankResp) ProtoMessage()    {}
func (*GetQQCarActivityRankResp) Descriptor() ([]byte, []int) {
	return fileDescriptorActivity, []int{66}
}

func (m *GetQQCarActivityRankResp) GetInviteInfos() []*InviterInfo {
	if m != nil {
		return m.InviteInfos
	}
	return nil
}

type NotifyInviteRecordReq struct {
	Uid        uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	InviterUid uint32 `protobuf:"varint,2,req,name=inviter_uid,json=inviterUid" json:"inviter_uid"`
}

func (m *NotifyInviteRecordReq) Reset()                    { *m = NotifyInviteRecordReq{} }
func (m *NotifyInviteRecordReq) String() string            { return proto.CompactTextString(m) }
func (*NotifyInviteRecordReq) ProtoMessage()               {}
func (*NotifyInviteRecordReq) Descriptor() ([]byte, []int) { return fileDescriptorActivity, []int{67} }

func (m *NotifyInviteRecordReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *NotifyInviteRecordReq) GetInviterUid() uint32 {
	if m != nil {
		return m.InviterUid
	}
	return 0
}

type GetPrizeInfoReq struct {
	Uid uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
}

func (m *GetPrizeInfoReq) Reset()                    { *m = GetPrizeInfoReq{} }
func (m *GetPrizeInfoReq) String() string            { return proto.CompactTextString(m) }
func (*GetPrizeInfoReq) ProtoMessage()               {}
func (*GetPrizeInfoReq) Descriptor() ([]byte, []int) { return fileDescriptorActivity, []int{68} }

func (m *GetPrizeInfoReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetPrizeInfoResp struct {
	DayRank       uint32 `protobuf:"varint,1,req,name=day_rank,json=dayRank" json:"day_rank"`
	InviteNum     uint32 `protobuf:"varint,2,req,name=invite_num,json=inviteNum" json:"invite_num"`
	TotalRank     uint32 `protobuf:"varint,3,req,name=total_rank,json=totalRank" json:"total_rank"`
	FristRegister uint32 `protobuf:"varint,4,req,name=frist_register,json=fristRegister" json:"frist_register"`
	DayInvite     uint32 `protobuf:"varint,5,req,name=day_invite,json=dayInvite" json:"day_invite"`
	TotalInvite   uint32 `protobuf:"varint,6,req,name=total_invite,json=totalInvite" json:"total_invite"`
}

func (m *GetPrizeInfoResp) Reset()                    { *m = GetPrizeInfoResp{} }
func (m *GetPrizeInfoResp) String() string            { return proto.CompactTextString(m) }
func (*GetPrizeInfoResp) ProtoMessage()               {}
func (*GetPrizeInfoResp) Descriptor() ([]byte, []int) { return fileDescriptorActivity, []int{69} }

func (m *GetPrizeInfoResp) GetDayRank() uint32 {
	if m != nil {
		return m.DayRank
	}
	return 0
}

func (m *GetPrizeInfoResp) GetInviteNum() uint32 {
	if m != nil {
		return m.InviteNum
	}
	return 0
}

func (m *GetPrizeInfoResp) GetTotalRank() uint32 {
	if m != nil {
		return m.TotalRank
	}
	return 0
}

func (m *GetPrizeInfoResp) GetFristRegister() uint32 {
	if m != nil {
		return m.FristRegister
	}
	return 0
}

func (m *GetPrizeInfoResp) GetDayInvite() uint32 {
	if m != nil {
		return m.DayInvite
	}
	return 0
}

func (m *GetPrizeInfoResp) GetTotalInvite() uint32 {
	if m != nil {
		return m.TotalInvite
	}
	return 0
}

type SetUidPairQQReq struct {
	Uid uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	Qq  string `protobuf:"bytes,2,req,name=qq" json:"qq"`
}

func (m *SetUidPairQQReq) Reset()                    { *m = SetUidPairQQReq{} }
func (m *SetUidPairQQReq) String() string            { return proto.CompactTextString(m) }
func (*SetUidPairQQReq) ProtoMessage()               {}
func (*SetUidPairQQReq) Descriptor() ([]byte, []int) { return fileDescriptorActivity, []int{70} }

func (m *SetUidPairQQReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SetUidPairQQReq) GetQq() string {
	if m != nil {
		return m.Qq
	}
	return ""
}

type TakePrizeReq struct {
	Uid       uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	PrizeType uint32 `protobuf:"varint,2,req,name=prize_type,json=prizeType" json:"prize_type"`
	QqNumber  string `protobuf:"bytes,3,req,name=qq_number,json=qqNumber" json:"qq_number"`
}

func (m *TakePrizeReq) Reset()                    { *m = TakePrizeReq{} }
func (m *TakePrizeReq) String() string            { return proto.CompactTextString(m) }
func (*TakePrizeReq) ProtoMessage()               {}
func (*TakePrizeReq) Descriptor() ([]byte, []int) { return fileDescriptorActivity, []int{71} }

func (m *TakePrizeReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *TakePrizeReq) GetPrizeType() uint32 {
	if m != nil {
		return m.PrizeType
	}
	return 0
}

func (m *TakePrizeReq) GetQqNumber() string {
	if m != nil {
		return m.QqNumber
	}
	return ""
}

type TakePrizeResp struct {
}

func (m *TakePrizeResp) Reset()                    { *m = TakePrizeResp{} }
func (m *TakePrizeResp) String() string            { return proto.CompactTextString(m) }
func (*TakePrizeResp) ProtoMessage()               {}
func (*TakePrizeResp) Descriptor() ([]byte, []int) { return fileDescriptorActivity, []int{72} }

func init() {
	proto.RegisterType((*OfferCategoryInfo)(nil), "Activity.OfferCategoryInfo")
	proto.RegisterType((*OfferCategoryList)(nil), "Activity.OfferCategoryList")
	proto.RegisterType((*ActivityOfferInfo)(nil), "Activity.ActivityOfferInfo")
	proto.RegisterType((*ActivityInfo)(nil), "Activity.ActivityInfo")
	proto.RegisterType((*CreateActivityReq)(nil), "Activity.CreateActivityReq")
	proto.RegisterType((*CreateActivityResp)(nil), "Activity.CreateActivityResp")
	proto.RegisterType((*UpdateActivityReq)(nil), "Activity.UpdateActivityReq")
	proto.RegisterType((*UpdateActivityResp)(nil), "Activity.UpdateActivityResp")
	proto.RegisterType((*DeleteActivityReq)(nil), "Activity.DeleteActivityReq")
	proto.RegisterType((*GetActivityListReq)(nil), "Activity.GetActivityListReq")
	proto.RegisterType((*GetActivityListResp)(nil), "Activity.GetActivityListResp")
	proto.RegisterType((*OfferEventElem)(nil), "Activity.OfferEventElem")
	proto.RegisterType((*CreateOfferEventReq)(nil), "Activity.CreateOfferEventReq")
	proto.RegisterType((*CreateOfferEventRsp)(nil), "Activity.CreateOfferEventRsp")
	proto.RegisterType((*ModifyOfferEventReq)(nil), "Activity.ModifyOfferEventReq")
	proto.RegisterType((*DelOfferEventReq)(nil), "Activity.DelOfferEventReq")
	proto.RegisterType((*GetOfferEventReq)(nil), "Activity.GetOfferEventReq")
	proto.RegisterType((*GetOfferEventRsp)(nil), "Activity.GetOfferEventRsp")
	proto.RegisterType((*GameRankElem)(nil), "Activity.GameRankElem")
	proto.RegisterType((*AddGameRankReq)(nil), "Activity.AddGameRankReq")
	proto.RegisterType((*ModifyGameRankReq)(nil), "Activity.ModifyGameRankReq")
	proto.RegisterType((*DelGameRankReq)(nil), "Activity.DelGameRankReq")
	proto.RegisterType((*GetGameRankReq)(nil), "Activity.GetGameRankReq")
	proto.RegisterType((*GetGameRankRsp)(nil), "Activity.GetGameRankRsp")
	proto.RegisterType((*GetGameOfferReq)(nil), "Activity.GetGameOfferReq")
	proto.RegisterType((*GameOfferInfo)(nil), "Activity.GameOfferInfo")
	proto.RegisterType((*GetGameOfferRsp)(nil), "Activity.GetGameOfferRsp")
	proto.RegisterType((*FirstVoucherAccountPassword)(nil), "Activity.FirstVoucherAccountPassword")
	proto.RegisterType((*FirstVoucherProduct)(nil), "Activity.FirstVoucherProduct")
	proto.RegisterType((*GetFirstVoucherProductsReq)(nil), "Activity.GetFirstVoucherProductsReq")
	proto.RegisterType((*GetFirstVoucherProductsResp)(nil), "Activity.GetFirstVoucherProductsResp")
	proto.RegisterType((*AddFirstVoucherProductReq)(nil), "Activity.AddFirstVoucherProductReq")
	proto.RegisterType((*AddFirstVoucherProductResp)(nil), "Activity.AddFirstVoucherProductResp")
	proto.RegisterType((*ModifyFirstVoucherProductReq)(nil), "Activity.ModifyFirstVoucherProductReq")
	proto.RegisterType((*GetGameFirstVoucherProductReq)(nil), "Activity.GetGameFirstVoucherProductReq")
	proto.RegisterType((*GetGameFirstVoucherProductResp)(nil), "Activity.GetGameFirstVoucherProductResp")
	proto.RegisterType((*DeleteFirstVoucherProductReq)(nil), "Activity.DeleteFirstVoucherProductReq")
	proto.RegisterType((*FirstVoucherItem)(nil), "Activity.FirstVoucherItem")
	proto.RegisterType((*AddFirstVoucherItemReq)(nil), "Activity.AddFirstVoucherItemReq")
	proto.RegisterType((*AddFirstVoucherItemResp)(nil), "Activity.AddFirstVoucherItemResp")
	proto.RegisterType((*GetFirstVoucherItemReq)(nil), "Activity.GetFirstVoucherItemReq")
	proto.RegisterType((*GetFirstVoucherItemResp)(nil), "Activity.GetFirstVoucherItemResp")
	proto.RegisterType((*DeleteFirstVoucherItemReq)(nil), "Activity.DeleteFirstVoucherItemReq")
	proto.RegisterType((*FirstVoucherUserItem)(nil), "Activity.FirstVoucherUserItem")
	proto.RegisterType((*PurchaseFirstVoucherItemReq)(nil), "Activity.PurchaseFirstVoucherItemReq")
	proto.RegisterType((*PurchaseFirstVoucherItemResp)(nil), "Activity.PurchaseFirstVoucherItemResp")
	proto.RegisterType((*GetFirstVoucherUserItemReq)(nil), "Activity.GetFirstVoucherUserItemReq")
	proto.RegisterType((*GetFirstVoucherUserItemResp)(nil), "Activity.GetFirstVoucherUserItemResp")
	proto.RegisterType((*ActBannerInfo)(nil), "Activity.ActBannerInfo")
	proto.RegisterType((*UpdateActBannerReq)(nil), "Activity.UpdateActBannerReq")
	proto.RegisterType((*GetActBannersResp)(nil), "Activity.GetActBannersResp")
	proto.RegisterType((*DelActBannerReq)(nil), "Activity.DelActBannerReq")
	proto.RegisterType((*GetActBannerOpTimeResp)(nil), "Activity.GetActBannerOpTimeResp")
	proto.RegisterType((*ActSplashScreenInfo)(nil), "Activity.ActSplashScreenInfo")
	proto.RegisterType((*UpdateActSplashScreenReq)(nil), "Activity.UpdateActSplashScreenReq")
	proto.RegisterType((*GetActSplashScreenResp)(nil), "Activity.GetActSplashScreenResp")
	proto.RegisterType((*DelActSplashScreenReq)(nil), "Activity.DelActSplashScreenReq")
	proto.RegisterType((*ActSplashScreenOpTime)(nil), "Activity.ActSplashScreenOpTime")
	proto.RegisterType((*GetActSplashScreenOpTimeResp)(nil), "Activity.GetActSplashScreenOpTimeResp")
	proto.RegisterType((*GetGamePreorderResultReq)(nil), "Activity.GetGamePreorderResultReq")
	proto.RegisterType((*GamePreorderResult)(nil), "Activity.GamePreorderResult")
	proto.RegisterType((*GetGamePreorderResultResp)(nil), "Activity.GetGamePreorderResultResp")
	proto.RegisterType((*GetGamePreorderUserSpptReq)(nil), "Activity.GetGamePreorderUserSpptReq")
	proto.RegisterType((*GetGamePreorderUserSpptResp)(nil), "Activity.GetGamePreorderUserSpptResp")
	proto.RegisterType((*SetGamePreorderUserSpptReq)(nil), "Activity.SetGamePreorderUserSpptReq")
	proto.RegisterType((*InviterInfo)(nil), "Activity.InviterInfo")
	proto.RegisterType((*GetQQCarActivityRankResp)(nil), "Activity.GetQQCarActivityRankResp")
	proto.RegisterType((*NotifyInviteRecordReq)(nil), "Activity.NotifyInviteRecordReq")
	proto.RegisterType((*GetPrizeInfoReq)(nil), "Activity.GetPrizeInfoReq")
	proto.RegisterType((*GetPrizeInfoResp)(nil), "Activity.GetPrizeInfoResp")
	proto.RegisterType((*SetUidPairQQReq)(nil), "Activity.SetUidPairQQReq")
	proto.RegisterType((*TakePrizeReq)(nil), "Activity.TakePrizeReq")
	proto.RegisterType((*TakePrizeResp)(nil), "Activity.TakePrizeResp")
	proto.RegisterEnum("Activity.ACTIVITY_STATUS", ACTIVITY_STATUS_name, ACTIVITY_STATUS_value)
	proto.RegisterEnum("Activity.OFFEREVENT_TYPE", OFFEREVENT_TYPE_name, OFFEREVENT_TYPE_value)
	proto.RegisterEnum("Activity.OFFEREVENT_STATUS", OFFEREVENT_STATUS_name, OFFEREVENT_STATUS_value)
	proto.RegisterEnum("Activity.PrizeType", PrizeType_name, PrizeType_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// Client API for Activity service

type ActivityClient interface {
	// activities 100~
	GetGameOffer(ctx context.Context, in *GetGameOfferReq, opts ...grpc.CallOption) (*GetGameOfferRsp, error)
	// activities 1~
	GetOfferCategoryList(ctx context.Context, in *tlvpickle.SKBuiltinEmpty_PB, opts ...grpc.CallOption) (*OfferCategoryList, error)
	// activities 10 ~
	CreateActivity(ctx context.Context, in *CreateActivityReq, opts ...grpc.CallOption) (*CreateActivityResp, error)
	UpdateActivity(ctx context.Context, in *UpdateActivityReq, opts ...grpc.CallOption) (*UpdateActivityResp, error)
	DeleteActivity(ctx context.Context, in *DeleteActivityReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	GetActivityList(ctx context.Context, in *GetActivityListReq, opts ...grpc.CallOption) (*GetActivityListResp, error)
	// 福利活动 20~
	CreateOfferEvent(ctx context.Context, in *CreateOfferEventReq, opts ...grpc.CallOption) (*CreateOfferEventRsp, error)
	ModifyOfferEvent(ctx context.Context, in *ModifyOfferEventReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	DelOfferEvent(ctx context.Context, in *DelOfferEventReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	GetOfferEvent(ctx context.Context, in *GetOfferEventReq, opts ...grpc.CallOption) (*GetOfferEventRsp, error)
	// 福利活动的游戏榜 30~
	AddGameRank(ctx context.Context, in *AddGameRankReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	ModifyGameRank(ctx context.Context, in *ModifyGameRankReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	DelGameRank(ctx context.Context, in *DelGameRankReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	GetGameRank(ctx context.Context, in *GetGameRankReq, opts ...grpc.CallOption) (*GetGameRankRsp, error)
	GetActBanners(ctx context.Context, in *tlvpickle.SKBuiltinEmpty_PB, opts ...grpc.CallOption) (*GetActBannersResp, error)
	GetActBannerOpTime(ctx context.Context, in *tlvpickle.SKBuiltinEmpty_PB, opts ...grpc.CallOption) (*GetActBannerOpTimeResp, error)
	UpdateActBanner(ctx context.Context, in *UpdateActBannerReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	DelActBanner(ctx context.Context, in *DelActBannerReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	GetActSplashScreen(ctx context.Context, in *tlvpickle.SKBuiltinEmpty_PB, opts ...grpc.CallOption) (*GetActSplashScreenResp, error)
	GetActSplashScreenOpTime(ctx context.Context, in *tlvpickle.SKBuiltinEmpty_PB, opts ...grpc.CallOption) (*GetActSplashScreenOpTimeResp, error)
	UpdateActSplashScreen(ctx context.Context, in *UpdateActSplashScreenReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	DelActSplashScreen(ctx context.Context, in *DelActSplashScreenReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	GetFirstVoucherProducts(ctx context.Context, in *GetFirstVoucherProductsReq, opts ...grpc.CallOption) (*GetFirstVoucherProductsResp, error)
	AddFirstVoucherProduct(ctx context.Context, in *AddFirstVoucherProductReq, opts ...grpc.CallOption) (*AddFirstVoucherProductResp, error)
	ModifyFirstVoucherProduct(ctx context.Context, in *ModifyFirstVoucherProductReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	GetGameFirstVoucherProduct(ctx context.Context, in *GetGameFirstVoucherProductReq, opts ...grpc.CallOption) (*GetGameFirstVoucherProductResp, error)
	DeleteFirstVoucherProduct(ctx context.Context, in *DeleteFirstVoucherProductReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	AddFirstVoucherItem(ctx context.Context, in *AddFirstVoucherItemReq, opts ...grpc.CallOption) (*AddFirstVoucherItemResp, error)
	GetFirstVoucherItem(ctx context.Context, in *GetFirstVoucherItemReq, opts ...grpc.CallOption) (*GetFirstVoucherItemResp, error)
	DeleteFirstVoucherItem(ctx context.Context, in *DeleteFirstVoucherItemReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	PurchaseFirstVoucherItem(ctx context.Context, in *PurchaseFirstVoucherItemReq, opts ...grpc.CallOption) (*PurchaseFirstVoucherItemResp, error)
	GetFirstVoucherUserItem(ctx context.Context, in *GetFirstVoucherUserItemReq, opts ...grpc.CallOption) (*GetFirstVoucherUserItemResp, error)
	SetGamePreorderUserSppt(ctx context.Context, in *SetGamePreorderUserSpptReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	GetGamePreorderUserSppt(ctx context.Context, in *GetGamePreorderUserSpptReq, opts ...grpc.CallOption) (*GetGamePreorderUserSpptResp, error)
	GetGamePreorderResult(ctx context.Context, in *GetGamePreorderResultReq, opts ...grpc.CallOption) (*GetGamePreorderResultResp, error)
	GetQQCarActivityDayRank(ctx context.Context, in *tlvpickle.SKBuiltinEmpty_PB, opts ...grpc.CallOption) (*GetQQCarActivityRankResp, error)
	GetQQCarActivityTotalRank(ctx context.Context, in *tlvpickle.SKBuiltinEmpty_PB, opts ...grpc.CallOption) (*GetQQCarActivityRankResp, error)
	NotifyInviteRecord(ctx context.Context, in *NotifyInviteRecordReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	SetUidPairQQ(ctx context.Context, in *SetUidPairQQReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	GetPrizeInfo(ctx context.Context, in *GetPrizeInfoReq, opts ...grpc.CallOption) (*GetPrizeInfoResp, error)
	TakePrize(ctx context.Context, in *TakePrizeReq, opts ...grpc.CallOption) (*TakePrizeResp, error)
}

type activityClient struct {
	cc *grpc.ClientConn
}

func NewActivityClient(cc *grpc.ClientConn) ActivityClient {
	return &activityClient{cc}
}

func (c *activityClient) GetGameOffer(ctx context.Context, in *GetGameOfferReq, opts ...grpc.CallOption) (*GetGameOfferRsp, error) {
	out := new(GetGameOfferRsp)
	err := grpc.Invoke(ctx, "/Activity.Activity/GetGameOffer", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *activityClient) GetOfferCategoryList(ctx context.Context, in *tlvpickle.SKBuiltinEmpty_PB, opts ...grpc.CallOption) (*OfferCategoryList, error) {
	out := new(OfferCategoryList)
	err := grpc.Invoke(ctx, "/Activity.Activity/GetOfferCategoryList", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *activityClient) CreateActivity(ctx context.Context, in *CreateActivityReq, opts ...grpc.CallOption) (*CreateActivityResp, error) {
	out := new(CreateActivityResp)
	err := grpc.Invoke(ctx, "/Activity.Activity/CreateActivity", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *activityClient) UpdateActivity(ctx context.Context, in *UpdateActivityReq, opts ...grpc.CallOption) (*UpdateActivityResp, error) {
	out := new(UpdateActivityResp)
	err := grpc.Invoke(ctx, "/Activity.Activity/UpdateActivity", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *activityClient) DeleteActivity(ctx context.Context, in *DeleteActivityReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := grpc.Invoke(ctx, "/Activity.Activity/DeleteActivity", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *activityClient) GetActivityList(ctx context.Context, in *GetActivityListReq, opts ...grpc.CallOption) (*GetActivityListResp, error) {
	out := new(GetActivityListResp)
	err := grpc.Invoke(ctx, "/Activity.Activity/GetActivityList", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *activityClient) CreateOfferEvent(ctx context.Context, in *CreateOfferEventReq, opts ...grpc.CallOption) (*CreateOfferEventRsp, error) {
	out := new(CreateOfferEventRsp)
	err := grpc.Invoke(ctx, "/Activity.Activity/CreateOfferEvent", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *activityClient) ModifyOfferEvent(ctx context.Context, in *ModifyOfferEventReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := grpc.Invoke(ctx, "/Activity.Activity/ModifyOfferEvent", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *activityClient) DelOfferEvent(ctx context.Context, in *DelOfferEventReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := grpc.Invoke(ctx, "/Activity.Activity/DelOfferEvent", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *activityClient) GetOfferEvent(ctx context.Context, in *GetOfferEventReq, opts ...grpc.CallOption) (*GetOfferEventRsp, error) {
	out := new(GetOfferEventRsp)
	err := grpc.Invoke(ctx, "/Activity.Activity/GetOfferEvent", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *activityClient) AddGameRank(ctx context.Context, in *AddGameRankReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := grpc.Invoke(ctx, "/Activity.Activity/AddGameRank", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *activityClient) ModifyGameRank(ctx context.Context, in *ModifyGameRankReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := grpc.Invoke(ctx, "/Activity.Activity/ModifyGameRank", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *activityClient) DelGameRank(ctx context.Context, in *DelGameRankReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := grpc.Invoke(ctx, "/Activity.Activity/DelGameRank", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *activityClient) GetGameRank(ctx context.Context, in *GetGameRankReq, opts ...grpc.CallOption) (*GetGameRankRsp, error) {
	out := new(GetGameRankRsp)
	err := grpc.Invoke(ctx, "/Activity.Activity/GetGameRank", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *activityClient) GetActBanners(ctx context.Context, in *tlvpickle.SKBuiltinEmpty_PB, opts ...grpc.CallOption) (*GetActBannersResp, error) {
	out := new(GetActBannersResp)
	err := grpc.Invoke(ctx, "/Activity.Activity/GetActBanners", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *activityClient) GetActBannerOpTime(ctx context.Context, in *tlvpickle.SKBuiltinEmpty_PB, opts ...grpc.CallOption) (*GetActBannerOpTimeResp, error) {
	out := new(GetActBannerOpTimeResp)
	err := grpc.Invoke(ctx, "/Activity.Activity/GetActBannerOpTime", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *activityClient) UpdateActBanner(ctx context.Context, in *UpdateActBannerReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := grpc.Invoke(ctx, "/Activity.Activity/UpdateActBanner", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *activityClient) DelActBanner(ctx context.Context, in *DelActBannerReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := grpc.Invoke(ctx, "/Activity.Activity/DelActBanner", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *activityClient) GetActSplashScreen(ctx context.Context, in *tlvpickle.SKBuiltinEmpty_PB, opts ...grpc.CallOption) (*GetActSplashScreenResp, error) {
	out := new(GetActSplashScreenResp)
	err := grpc.Invoke(ctx, "/Activity.Activity/GetActSplashScreen", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *activityClient) GetActSplashScreenOpTime(ctx context.Context, in *tlvpickle.SKBuiltinEmpty_PB, opts ...grpc.CallOption) (*GetActSplashScreenOpTimeResp, error) {
	out := new(GetActSplashScreenOpTimeResp)
	err := grpc.Invoke(ctx, "/Activity.Activity/GetActSplashScreenOpTime", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *activityClient) UpdateActSplashScreen(ctx context.Context, in *UpdateActSplashScreenReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := grpc.Invoke(ctx, "/Activity.Activity/UpdateActSplashScreen", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *activityClient) DelActSplashScreen(ctx context.Context, in *DelActSplashScreenReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := grpc.Invoke(ctx, "/Activity.Activity/DelActSplashScreen", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *activityClient) GetFirstVoucherProducts(ctx context.Context, in *GetFirstVoucherProductsReq, opts ...grpc.CallOption) (*GetFirstVoucherProductsResp, error) {
	out := new(GetFirstVoucherProductsResp)
	err := grpc.Invoke(ctx, "/Activity.Activity/GetFirstVoucherProducts", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *activityClient) AddFirstVoucherProduct(ctx context.Context, in *AddFirstVoucherProductReq, opts ...grpc.CallOption) (*AddFirstVoucherProductResp, error) {
	out := new(AddFirstVoucherProductResp)
	err := grpc.Invoke(ctx, "/Activity.Activity/AddFirstVoucherProduct", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *activityClient) ModifyFirstVoucherProduct(ctx context.Context, in *ModifyFirstVoucherProductReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := grpc.Invoke(ctx, "/Activity.Activity/ModifyFirstVoucherProduct", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *activityClient) GetGameFirstVoucherProduct(ctx context.Context, in *GetGameFirstVoucherProductReq, opts ...grpc.CallOption) (*GetGameFirstVoucherProductResp, error) {
	out := new(GetGameFirstVoucherProductResp)
	err := grpc.Invoke(ctx, "/Activity.Activity/GetGameFirstVoucherProduct", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *activityClient) DeleteFirstVoucherProduct(ctx context.Context, in *DeleteFirstVoucherProductReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := grpc.Invoke(ctx, "/Activity.Activity/DeleteFirstVoucherProduct", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *activityClient) AddFirstVoucherItem(ctx context.Context, in *AddFirstVoucherItemReq, opts ...grpc.CallOption) (*AddFirstVoucherItemResp, error) {
	out := new(AddFirstVoucherItemResp)
	err := grpc.Invoke(ctx, "/Activity.Activity/AddFirstVoucherItem", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *activityClient) GetFirstVoucherItem(ctx context.Context, in *GetFirstVoucherItemReq, opts ...grpc.CallOption) (*GetFirstVoucherItemResp, error) {
	out := new(GetFirstVoucherItemResp)
	err := grpc.Invoke(ctx, "/Activity.Activity/GetFirstVoucherItem", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *activityClient) DeleteFirstVoucherItem(ctx context.Context, in *DeleteFirstVoucherItemReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := grpc.Invoke(ctx, "/Activity.Activity/DeleteFirstVoucherItem", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *activityClient) PurchaseFirstVoucherItem(ctx context.Context, in *PurchaseFirstVoucherItemReq, opts ...grpc.CallOption) (*PurchaseFirstVoucherItemResp, error) {
	out := new(PurchaseFirstVoucherItemResp)
	err := grpc.Invoke(ctx, "/Activity.Activity/PurchaseFirstVoucherItem", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *activityClient) GetFirstVoucherUserItem(ctx context.Context, in *GetFirstVoucherUserItemReq, opts ...grpc.CallOption) (*GetFirstVoucherUserItemResp, error) {
	out := new(GetFirstVoucherUserItemResp)
	err := grpc.Invoke(ctx, "/Activity.Activity/GetFirstVoucherUserItem", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *activityClient) SetGamePreorderUserSppt(ctx context.Context, in *SetGamePreorderUserSpptReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := grpc.Invoke(ctx, "/Activity.Activity/SetGamePreorderUserSppt", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *activityClient) GetGamePreorderUserSppt(ctx context.Context, in *GetGamePreorderUserSpptReq, opts ...grpc.CallOption) (*GetGamePreorderUserSpptResp, error) {
	out := new(GetGamePreorderUserSpptResp)
	err := grpc.Invoke(ctx, "/Activity.Activity/GetGamePreorderUserSppt", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *activityClient) GetGamePreorderResult(ctx context.Context, in *GetGamePreorderResultReq, opts ...grpc.CallOption) (*GetGamePreorderResultResp, error) {
	out := new(GetGamePreorderResultResp)
	err := grpc.Invoke(ctx, "/Activity.Activity/GetGamePreorderResult", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *activityClient) GetQQCarActivityDayRank(ctx context.Context, in *tlvpickle.SKBuiltinEmpty_PB, opts ...grpc.CallOption) (*GetQQCarActivityRankResp, error) {
	out := new(GetQQCarActivityRankResp)
	err := grpc.Invoke(ctx, "/Activity.Activity/GetQQCarActivityDayRank", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *activityClient) GetQQCarActivityTotalRank(ctx context.Context, in *tlvpickle.SKBuiltinEmpty_PB, opts ...grpc.CallOption) (*GetQQCarActivityRankResp, error) {
	out := new(GetQQCarActivityRankResp)
	err := grpc.Invoke(ctx, "/Activity.Activity/GetQQCarActivityTotalRank", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *activityClient) NotifyInviteRecord(ctx context.Context, in *NotifyInviteRecordReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := grpc.Invoke(ctx, "/Activity.Activity/NotifyInviteRecord", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *activityClient) SetUidPairQQ(ctx context.Context, in *SetUidPairQQReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := grpc.Invoke(ctx, "/Activity.Activity/SetUidPairQQ", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *activityClient) GetPrizeInfo(ctx context.Context, in *GetPrizeInfoReq, opts ...grpc.CallOption) (*GetPrizeInfoResp, error) {
	out := new(GetPrizeInfoResp)
	err := grpc.Invoke(ctx, "/Activity.Activity/GetPrizeInfo", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *activityClient) TakePrize(ctx context.Context, in *TakePrizeReq, opts ...grpc.CallOption) (*TakePrizeResp, error) {
	out := new(TakePrizeResp)
	err := grpc.Invoke(ctx, "/Activity.Activity/TakePrize", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// Server API for Activity service

type ActivityServer interface {
	// activities 100~
	GetGameOffer(context.Context, *GetGameOfferReq) (*GetGameOfferRsp, error)
	// activities 1~
	GetOfferCategoryList(context.Context, *tlvpickle.SKBuiltinEmpty_PB) (*OfferCategoryList, error)
	// activities 10 ~
	CreateActivity(context.Context, *CreateActivityReq) (*CreateActivityResp, error)
	UpdateActivity(context.Context, *UpdateActivityReq) (*UpdateActivityResp, error)
	DeleteActivity(context.Context, *DeleteActivityReq) (*tlvpickle.SKBuiltinEmpty_PB, error)
	GetActivityList(context.Context, *GetActivityListReq) (*GetActivityListResp, error)
	// 福利活动 20~
	CreateOfferEvent(context.Context, *CreateOfferEventReq) (*CreateOfferEventRsp, error)
	ModifyOfferEvent(context.Context, *ModifyOfferEventReq) (*tlvpickle.SKBuiltinEmpty_PB, error)
	DelOfferEvent(context.Context, *DelOfferEventReq) (*tlvpickle.SKBuiltinEmpty_PB, error)
	GetOfferEvent(context.Context, *GetOfferEventReq) (*GetOfferEventRsp, error)
	// 福利活动的游戏榜 30~
	AddGameRank(context.Context, *AddGameRankReq) (*tlvpickle.SKBuiltinEmpty_PB, error)
	ModifyGameRank(context.Context, *ModifyGameRankReq) (*tlvpickle.SKBuiltinEmpty_PB, error)
	DelGameRank(context.Context, *DelGameRankReq) (*tlvpickle.SKBuiltinEmpty_PB, error)
	GetGameRank(context.Context, *GetGameRankReq) (*GetGameRankRsp, error)
	GetActBanners(context.Context, *tlvpickle.SKBuiltinEmpty_PB) (*GetActBannersResp, error)
	GetActBannerOpTime(context.Context, *tlvpickle.SKBuiltinEmpty_PB) (*GetActBannerOpTimeResp, error)
	UpdateActBanner(context.Context, *UpdateActBannerReq) (*tlvpickle.SKBuiltinEmpty_PB, error)
	DelActBanner(context.Context, *DelActBannerReq) (*tlvpickle.SKBuiltinEmpty_PB, error)
	GetActSplashScreen(context.Context, *tlvpickle.SKBuiltinEmpty_PB) (*GetActSplashScreenResp, error)
	GetActSplashScreenOpTime(context.Context, *tlvpickle.SKBuiltinEmpty_PB) (*GetActSplashScreenOpTimeResp, error)
	UpdateActSplashScreen(context.Context, *UpdateActSplashScreenReq) (*tlvpickle.SKBuiltinEmpty_PB, error)
	DelActSplashScreen(context.Context, *DelActSplashScreenReq) (*tlvpickle.SKBuiltinEmpty_PB, error)
	GetFirstVoucherProducts(context.Context, *GetFirstVoucherProductsReq) (*GetFirstVoucherProductsResp, error)
	AddFirstVoucherProduct(context.Context, *AddFirstVoucherProductReq) (*AddFirstVoucherProductResp, error)
	ModifyFirstVoucherProduct(context.Context, *ModifyFirstVoucherProductReq) (*tlvpickle.SKBuiltinEmpty_PB, error)
	GetGameFirstVoucherProduct(context.Context, *GetGameFirstVoucherProductReq) (*GetGameFirstVoucherProductResp, error)
	DeleteFirstVoucherProduct(context.Context, *DeleteFirstVoucherProductReq) (*tlvpickle.SKBuiltinEmpty_PB, error)
	AddFirstVoucherItem(context.Context, *AddFirstVoucherItemReq) (*AddFirstVoucherItemResp, error)
	GetFirstVoucherItem(context.Context, *GetFirstVoucherItemReq) (*GetFirstVoucherItemResp, error)
	DeleteFirstVoucherItem(context.Context, *DeleteFirstVoucherItemReq) (*tlvpickle.SKBuiltinEmpty_PB, error)
	PurchaseFirstVoucherItem(context.Context, *PurchaseFirstVoucherItemReq) (*PurchaseFirstVoucherItemResp, error)
	GetFirstVoucherUserItem(context.Context, *GetFirstVoucherUserItemReq) (*GetFirstVoucherUserItemResp, error)
	SetGamePreorderUserSppt(context.Context, *SetGamePreorderUserSpptReq) (*tlvpickle.SKBuiltinEmpty_PB, error)
	GetGamePreorderUserSppt(context.Context, *GetGamePreorderUserSpptReq) (*GetGamePreorderUserSpptResp, error)
	GetGamePreorderResult(context.Context, *GetGamePreorderResultReq) (*GetGamePreorderResultResp, error)
	GetQQCarActivityDayRank(context.Context, *tlvpickle.SKBuiltinEmpty_PB) (*GetQQCarActivityRankResp, error)
	GetQQCarActivityTotalRank(context.Context, *tlvpickle.SKBuiltinEmpty_PB) (*GetQQCarActivityRankResp, error)
	NotifyInviteRecord(context.Context, *NotifyInviteRecordReq) (*tlvpickle.SKBuiltinEmpty_PB, error)
	SetUidPairQQ(context.Context, *SetUidPairQQReq) (*tlvpickle.SKBuiltinEmpty_PB, error)
	GetPrizeInfo(context.Context, *GetPrizeInfoReq) (*GetPrizeInfoResp, error)
	TakePrize(context.Context, *TakePrizeReq) (*TakePrizeResp, error)
}

func RegisterActivityServer(s *grpc.Server, srv ActivityServer) {
	s.RegisterService(&_Activity_serviceDesc, srv)
}

func _Activity_GetGameOffer_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGameOfferReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ActivityServer).GetGameOffer(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Activity.Activity/GetGameOffer",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ActivityServer).GetGameOffer(ctx, req.(*GetGameOfferReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Activity_GetOfferCategoryList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(tlvpickle.SKBuiltinEmpty_PB)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ActivityServer).GetOfferCategoryList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Activity.Activity/GetOfferCategoryList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ActivityServer).GetOfferCategoryList(ctx, req.(*tlvpickle.SKBuiltinEmpty_PB))
	}
	return interceptor(ctx, in, info, handler)
}

func _Activity_CreateActivity_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateActivityReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ActivityServer).CreateActivity(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Activity.Activity/CreateActivity",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ActivityServer).CreateActivity(ctx, req.(*CreateActivityReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Activity_UpdateActivity_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateActivityReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ActivityServer).UpdateActivity(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Activity.Activity/UpdateActivity",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ActivityServer).UpdateActivity(ctx, req.(*UpdateActivityReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Activity_DeleteActivity_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteActivityReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ActivityServer).DeleteActivity(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Activity.Activity/DeleteActivity",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ActivityServer).DeleteActivity(ctx, req.(*DeleteActivityReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Activity_GetActivityList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetActivityListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ActivityServer).GetActivityList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Activity.Activity/GetActivityList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ActivityServer).GetActivityList(ctx, req.(*GetActivityListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Activity_CreateOfferEvent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateOfferEventReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ActivityServer).CreateOfferEvent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Activity.Activity/CreateOfferEvent",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ActivityServer).CreateOfferEvent(ctx, req.(*CreateOfferEventReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Activity_ModifyOfferEvent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ModifyOfferEventReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ActivityServer).ModifyOfferEvent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Activity.Activity/ModifyOfferEvent",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ActivityServer).ModifyOfferEvent(ctx, req.(*ModifyOfferEventReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Activity_DelOfferEvent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelOfferEventReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ActivityServer).DelOfferEvent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Activity.Activity/DelOfferEvent",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ActivityServer).DelOfferEvent(ctx, req.(*DelOfferEventReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Activity_GetOfferEvent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetOfferEventReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ActivityServer).GetOfferEvent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Activity.Activity/GetOfferEvent",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ActivityServer).GetOfferEvent(ctx, req.(*GetOfferEventReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Activity_AddGameRank_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddGameRankReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ActivityServer).AddGameRank(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Activity.Activity/AddGameRank",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ActivityServer).AddGameRank(ctx, req.(*AddGameRankReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Activity_ModifyGameRank_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ModifyGameRankReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ActivityServer).ModifyGameRank(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Activity.Activity/ModifyGameRank",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ActivityServer).ModifyGameRank(ctx, req.(*ModifyGameRankReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Activity_DelGameRank_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelGameRankReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ActivityServer).DelGameRank(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Activity.Activity/DelGameRank",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ActivityServer).DelGameRank(ctx, req.(*DelGameRankReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Activity_GetGameRank_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGameRankReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ActivityServer).GetGameRank(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Activity.Activity/GetGameRank",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ActivityServer).GetGameRank(ctx, req.(*GetGameRankReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Activity_GetActBanners_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(tlvpickle.SKBuiltinEmpty_PB)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ActivityServer).GetActBanners(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Activity.Activity/GetActBanners",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ActivityServer).GetActBanners(ctx, req.(*tlvpickle.SKBuiltinEmpty_PB))
	}
	return interceptor(ctx, in, info, handler)
}

func _Activity_GetActBannerOpTime_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(tlvpickle.SKBuiltinEmpty_PB)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ActivityServer).GetActBannerOpTime(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Activity.Activity/GetActBannerOpTime",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ActivityServer).GetActBannerOpTime(ctx, req.(*tlvpickle.SKBuiltinEmpty_PB))
	}
	return interceptor(ctx, in, info, handler)
}

func _Activity_UpdateActBanner_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateActBannerReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ActivityServer).UpdateActBanner(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Activity.Activity/UpdateActBanner",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ActivityServer).UpdateActBanner(ctx, req.(*UpdateActBannerReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Activity_DelActBanner_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelActBannerReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ActivityServer).DelActBanner(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Activity.Activity/DelActBanner",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ActivityServer).DelActBanner(ctx, req.(*DelActBannerReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Activity_GetActSplashScreen_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(tlvpickle.SKBuiltinEmpty_PB)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ActivityServer).GetActSplashScreen(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Activity.Activity/GetActSplashScreen",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ActivityServer).GetActSplashScreen(ctx, req.(*tlvpickle.SKBuiltinEmpty_PB))
	}
	return interceptor(ctx, in, info, handler)
}

func _Activity_GetActSplashScreenOpTime_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(tlvpickle.SKBuiltinEmpty_PB)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ActivityServer).GetActSplashScreenOpTime(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Activity.Activity/GetActSplashScreenOpTime",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ActivityServer).GetActSplashScreenOpTime(ctx, req.(*tlvpickle.SKBuiltinEmpty_PB))
	}
	return interceptor(ctx, in, info, handler)
}

func _Activity_UpdateActSplashScreen_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateActSplashScreenReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ActivityServer).UpdateActSplashScreen(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Activity.Activity/UpdateActSplashScreen",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ActivityServer).UpdateActSplashScreen(ctx, req.(*UpdateActSplashScreenReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Activity_DelActSplashScreen_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelActSplashScreenReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ActivityServer).DelActSplashScreen(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Activity.Activity/DelActSplashScreen",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ActivityServer).DelActSplashScreen(ctx, req.(*DelActSplashScreenReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Activity_GetFirstVoucherProducts_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetFirstVoucherProductsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ActivityServer).GetFirstVoucherProducts(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Activity.Activity/GetFirstVoucherProducts",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ActivityServer).GetFirstVoucherProducts(ctx, req.(*GetFirstVoucherProductsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Activity_AddFirstVoucherProduct_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddFirstVoucherProductReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ActivityServer).AddFirstVoucherProduct(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Activity.Activity/AddFirstVoucherProduct",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ActivityServer).AddFirstVoucherProduct(ctx, req.(*AddFirstVoucherProductReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Activity_ModifyFirstVoucherProduct_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ModifyFirstVoucherProductReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ActivityServer).ModifyFirstVoucherProduct(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Activity.Activity/ModifyFirstVoucherProduct",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ActivityServer).ModifyFirstVoucherProduct(ctx, req.(*ModifyFirstVoucherProductReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Activity_GetGameFirstVoucherProduct_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGameFirstVoucherProductReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ActivityServer).GetGameFirstVoucherProduct(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Activity.Activity/GetGameFirstVoucherProduct",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ActivityServer).GetGameFirstVoucherProduct(ctx, req.(*GetGameFirstVoucherProductReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Activity_DeleteFirstVoucherProduct_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteFirstVoucherProductReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ActivityServer).DeleteFirstVoucherProduct(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Activity.Activity/DeleteFirstVoucherProduct",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ActivityServer).DeleteFirstVoucherProduct(ctx, req.(*DeleteFirstVoucherProductReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Activity_AddFirstVoucherItem_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddFirstVoucherItemReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ActivityServer).AddFirstVoucherItem(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Activity.Activity/AddFirstVoucherItem",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ActivityServer).AddFirstVoucherItem(ctx, req.(*AddFirstVoucherItemReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Activity_GetFirstVoucherItem_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetFirstVoucherItemReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ActivityServer).GetFirstVoucherItem(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Activity.Activity/GetFirstVoucherItem",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ActivityServer).GetFirstVoucherItem(ctx, req.(*GetFirstVoucherItemReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Activity_DeleteFirstVoucherItem_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteFirstVoucherItemReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ActivityServer).DeleteFirstVoucherItem(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Activity.Activity/DeleteFirstVoucherItem",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ActivityServer).DeleteFirstVoucherItem(ctx, req.(*DeleteFirstVoucherItemReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Activity_PurchaseFirstVoucherItem_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PurchaseFirstVoucherItemReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ActivityServer).PurchaseFirstVoucherItem(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Activity.Activity/PurchaseFirstVoucherItem",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ActivityServer).PurchaseFirstVoucherItem(ctx, req.(*PurchaseFirstVoucherItemReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Activity_GetFirstVoucherUserItem_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetFirstVoucherUserItemReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ActivityServer).GetFirstVoucherUserItem(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Activity.Activity/GetFirstVoucherUserItem",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ActivityServer).GetFirstVoucherUserItem(ctx, req.(*GetFirstVoucherUserItemReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Activity_SetGamePreorderUserSppt_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetGamePreorderUserSpptReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ActivityServer).SetGamePreorderUserSppt(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Activity.Activity/SetGamePreorderUserSppt",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ActivityServer).SetGamePreorderUserSppt(ctx, req.(*SetGamePreorderUserSpptReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Activity_GetGamePreorderUserSppt_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGamePreorderUserSpptReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ActivityServer).GetGamePreorderUserSppt(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Activity.Activity/GetGamePreorderUserSppt",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ActivityServer).GetGamePreorderUserSppt(ctx, req.(*GetGamePreorderUserSpptReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Activity_GetGamePreorderResult_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGamePreorderResultReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ActivityServer).GetGamePreorderResult(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Activity.Activity/GetGamePreorderResult",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ActivityServer).GetGamePreorderResult(ctx, req.(*GetGamePreorderResultReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Activity_GetQQCarActivityDayRank_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(tlvpickle.SKBuiltinEmpty_PB)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ActivityServer).GetQQCarActivityDayRank(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Activity.Activity/GetQQCarActivityDayRank",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ActivityServer).GetQQCarActivityDayRank(ctx, req.(*tlvpickle.SKBuiltinEmpty_PB))
	}
	return interceptor(ctx, in, info, handler)
}

func _Activity_GetQQCarActivityTotalRank_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(tlvpickle.SKBuiltinEmpty_PB)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ActivityServer).GetQQCarActivityTotalRank(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Activity.Activity/GetQQCarActivityTotalRank",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ActivityServer).GetQQCarActivityTotalRank(ctx, req.(*tlvpickle.SKBuiltinEmpty_PB))
	}
	return interceptor(ctx, in, info, handler)
}

func _Activity_NotifyInviteRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NotifyInviteRecordReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ActivityServer).NotifyInviteRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Activity.Activity/NotifyInviteRecord",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ActivityServer).NotifyInviteRecord(ctx, req.(*NotifyInviteRecordReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Activity_SetUidPairQQ_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetUidPairQQReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ActivityServer).SetUidPairQQ(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Activity.Activity/SetUidPairQQ",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ActivityServer).SetUidPairQQ(ctx, req.(*SetUidPairQQReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Activity_GetPrizeInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPrizeInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ActivityServer).GetPrizeInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Activity.Activity/GetPrizeInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ActivityServer).GetPrizeInfo(ctx, req.(*GetPrizeInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Activity_TakePrize_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TakePrizeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ActivityServer).TakePrize(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Activity.Activity/TakePrize",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ActivityServer).TakePrize(ctx, req.(*TakePrizeReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _Activity_serviceDesc = grpc.ServiceDesc{
	ServiceName: "Activity.Activity",
	HandlerType: (*ActivityServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetGameOffer",
			Handler:    _Activity_GetGameOffer_Handler,
		},
		{
			MethodName: "GetOfferCategoryList",
			Handler:    _Activity_GetOfferCategoryList_Handler,
		},
		{
			MethodName: "CreateActivity",
			Handler:    _Activity_CreateActivity_Handler,
		},
		{
			MethodName: "UpdateActivity",
			Handler:    _Activity_UpdateActivity_Handler,
		},
		{
			MethodName: "DeleteActivity",
			Handler:    _Activity_DeleteActivity_Handler,
		},
		{
			MethodName: "GetActivityList",
			Handler:    _Activity_GetActivityList_Handler,
		},
		{
			MethodName: "CreateOfferEvent",
			Handler:    _Activity_CreateOfferEvent_Handler,
		},
		{
			MethodName: "ModifyOfferEvent",
			Handler:    _Activity_ModifyOfferEvent_Handler,
		},
		{
			MethodName: "DelOfferEvent",
			Handler:    _Activity_DelOfferEvent_Handler,
		},
		{
			MethodName: "GetOfferEvent",
			Handler:    _Activity_GetOfferEvent_Handler,
		},
		{
			MethodName: "AddGameRank",
			Handler:    _Activity_AddGameRank_Handler,
		},
		{
			MethodName: "ModifyGameRank",
			Handler:    _Activity_ModifyGameRank_Handler,
		},
		{
			MethodName: "DelGameRank",
			Handler:    _Activity_DelGameRank_Handler,
		},
		{
			MethodName: "GetGameRank",
			Handler:    _Activity_GetGameRank_Handler,
		},
		{
			MethodName: "GetActBanners",
			Handler:    _Activity_GetActBanners_Handler,
		},
		{
			MethodName: "GetActBannerOpTime",
			Handler:    _Activity_GetActBannerOpTime_Handler,
		},
		{
			MethodName: "UpdateActBanner",
			Handler:    _Activity_UpdateActBanner_Handler,
		},
		{
			MethodName: "DelActBanner",
			Handler:    _Activity_DelActBanner_Handler,
		},
		{
			MethodName: "GetActSplashScreen",
			Handler:    _Activity_GetActSplashScreen_Handler,
		},
		{
			MethodName: "GetActSplashScreenOpTime",
			Handler:    _Activity_GetActSplashScreenOpTime_Handler,
		},
		{
			MethodName: "UpdateActSplashScreen",
			Handler:    _Activity_UpdateActSplashScreen_Handler,
		},
		{
			MethodName: "DelActSplashScreen",
			Handler:    _Activity_DelActSplashScreen_Handler,
		},
		{
			MethodName: "GetFirstVoucherProducts",
			Handler:    _Activity_GetFirstVoucherProducts_Handler,
		},
		{
			MethodName: "AddFirstVoucherProduct",
			Handler:    _Activity_AddFirstVoucherProduct_Handler,
		},
		{
			MethodName: "ModifyFirstVoucherProduct",
			Handler:    _Activity_ModifyFirstVoucherProduct_Handler,
		},
		{
			MethodName: "GetGameFirstVoucherProduct",
			Handler:    _Activity_GetGameFirstVoucherProduct_Handler,
		},
		{
			MethodName: "DeleteFirstVoucherProduct",
			Handler:    _Activity_DeleteFirstVoucherProduct_Handler,
		},
		{
			MethodName: "AddFirstVoucherItem",
			Handler:    _Activity_AddFirstVoucherItem_Handler,
		},
		{
			MethodName: "GetFirstVoucherItem",
			Handler:    _Activity_GetFirstVoucherItem_Handler,
		},
		{
			MethodName: "DeleteFirstVoucherItem",
			Handler:    _Activity_DeleteFirstVoucherItem_Handler,
		},
		{
			MethodName: "PurchaseFirstVoucherItem",
			Handler:    _Activity_PurchaseFirstVoucherItem_Handler,
		},
		{
			MethodName: "GetFirstVoucherUserItem",
			Handler:    _Activity_GetFirstVoucherUserItem_Handler,
		},
		{
			MethodName: "SetGamePreorderUserSppt",
			Handler:    _Activity_SetGamePreorderUserSppt_Handler,
		},
		{
			MethodName: "GetGamePreorderUserSppt",
			Handler:    _Activity_GetGamePreorderUserSppt_Handler,
		},
		{
			MethodName: "GetGamePreorderResult",
			Handler:    _Activity_GetGamePreorderResult_Handler,
		},
		{
			MethodName: "GetQQCarActivityDayRank",
			Handler:    _Activity_GetQQCarActivityDayRank_Handler,
		},
		{
			MethodName: "GetQQCarActivityTotalRank",
			Handler:    _Activity_GetQQCarActivityTotalRank_Handler,
		},
		{
			MethodName: "NotifyInviteRecord",
			Handler:    _Activity_NotifyInviteRecord_Handler,
		},
		{
			MethodName: "SetUidPairQQ",
			Handler:    _Activity_SetUidPairQQ_Handler,
		},
		{
			MethodName: "GetPrizeInfo",
			Handler:    _Activity_GetPrizeInfo_Handler,
		},
		{
			MethodName: "TakePrize",
			Handler:    _Activity_TakePrize_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "src/activitysvr/activity.proto",
}

func (m *OfferCategoryInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *OfferCategoryInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintActivity(dAtA, i, uint64(m.Id))
	dAtA[i] = 0x12
	i++
	i = encodeVarintActivity(dAtA, i, uint64(len(m.Name)))
	i += copy(dAtA[i:], m.Name)
	return i, nil
}

func (m *OfferCategoryList) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *OfferCategoryList) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.List) > 0 {
		for _, msg := range m.List {
			dAtA[i] = 0xa
			i++
			i = encodeVarintActivity(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *ActivityOfferInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ActivityOfferInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintActivity(dAtA, i, uint64(m.GameId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintActivity(dAtA, i, uint64(m.ActivityId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintActivity(dAtA, i, uint64(m.CategoryId))
	dAtA[i] = 0x20
	i++
	i = encodeVarintActivity(dAtA, i, uint64(m.Amount))
	dAtA[i] = 0x2a
	i++
	i = encodeVarintActivity(dAtA, i, uint64(len(m.OfferDesc)))
	i += copy(dAtA[i:], m.OfferDesc)
	dAtA[i] = 0x32
	i++
	i = encodeVarintActivity(dAtA, i, uint64(len(m.OfferNotes)))
	i += copy(dAtA[i:], m.OfferNotes)
	dAtA[i] = 0x3a
	i++
	i = encodeVarintActivity(dAtA, i, uint64(len(m.OfferAppendix)))
	i += copy(dAtA[i:], m.OfferAppendix)
	return i, nil
}

func (m *ActivityInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ActivityInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintActivity(dAtA, i, uint64(m.ActivityId))
	dAtA[i] = 0x12
	i++
	i = encodeVarintActivity(dAtA, i, uint64(len(m.Name)))
	i += copy(dAtA[i:], m.Name)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintActivity(dAtA, i, uint64(len(m.Description)))
	i += copy(dAtA[i:], m.Description)
	dAtA[i] = 0x22
	i++
	i = encodeVarintActivity(dAtA, i, uint64(len(m.Banner)))
	i += copy(dAtA[i:], m.Banner)
	dAtA[i] = 0x28
	i++
	i = encodeVarintActivity(dAtA, i, uint64(m.BeginTime))
	dAtA[i] = 0x30
	i++
	i = encodeVarintActivity(dAtA, i, uint64(m.EndTime))
	dAtA[i] = 0x38
	i++
	i = encodeVarintActivity(dAtA, i, uint64(m.Status))
	if len(m.OffersList) > 0 {
		for _, msg := range m.OffersList {
			dAtA[i] = 0x42
			i++
			i = encodeVarintActivity(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *CreateActivityReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CreateActivityReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Activity == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("activity")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintActivity(dAtA, i, uint64(m.Activity.Size()))
		n1, err := m.Activity.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n1
	}
	return i, nil
}

func (m *CreateActivityResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CreateActivityResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintActivity(dAtA, i, uint64(m.ActivityId))
	return i, nil
}

func (m *UpdateActivityReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UpdateActivityReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Activity == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("activity")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintActivity(dAtA, i, uint64(m.Activity.Size()))
		n2, err := m.Activity.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n2
	}
	return i, nil
}

func (m *UpdateActivityResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UpdateActivityResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintActivity(dAtA, i, uint64(m.ActivityId))
	return i, nil
}

func (m *DeleteActivityReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DeleteActivityReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintActivity(dAtA, i, uint64(m.ActivityId))
	if len(m.GameidList) > 0 {
		for _, num := range m.GameidList {
			dAtA[i] = 0x10
			i++
			i = encodeVarintActivity(dAtA, i, uint64(num))
		}
	}
	return i, nil
}

func (m *GetActivityListReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetActivityListReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintActivity(dAtA, i, uint64(m.Status))
	dAtA[i] = 0x10
	i++
	i = encodeVarintActivity(dAtA, i, uint64(m.FromIndex))
	dAtA[i] = 0x18
	i++
	i = encodeVarintActivity(dAtA, i, uint64(m.Count))
	dAtA[i] = 0x20
	i++
	if m.Reverse {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x28
	i++
	i = encodeVarintActivity(dAtA, i, uint64(m.GameId))
	return i, nil
}

func (m *GetActivityListResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetActivityListResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.ActivityList) > 0 {
		for _, msg := range m.ActivityList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintActivity(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *OfferEventElem) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *OfferEventElem) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintActivity(dAtA, i, uint64(m.Id))
	dAtA[i] = 0x12
	i++
	i = encodeVarintActivity(dAtA, i, uint64(len(m.Name)))
	i += copy(dAtA[i:], m.Name)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintActivity(dAtA, i, uint64(len(m.Description)))
	i += copy(dAtA[i:], m.Description)
	dAtA[i] = 0x22
	i++
	i = encodeVarintActivity(dAtA, i, uint64(len(m.Notes)))
	i += copy(dAtA[i:], m.Notes)
	dAtA[i] = 0x28
	i++
	i = encodeVarintActivity(dAtA, i, uint64(m.BeginTime))
	dAtA[i] = 0x30
	i++
	i = encodeVarintActivity(dAtA, i, uint64(m.EndTime))
	dAtA[i] = 0x38
	i++
	i = encodeVarintActivity(dAtA, i, uint64(m.NowTime))
	dAtA[i] = 0x42
	i++
	i = encodeVarintActivity(dAtA, i, uint64(len(m.Banner)))
	i += copy(dAtA[i:], m.Banner)
	dAtA[i] = 0x4a
	i++
	i = encodeVarintActivity(dAtA, i, uint64(len(m.DetailHref)))
	i += copy(dAtA[i:], m.DetailHref)
	dAtA[i] = 0x50
	i++
	i = encodeVarintActivity(dAtA, i, uint64(m.Type))
	dAtA[i] = 0x58
	i++
	i = encodeVarintActivity(dAtA, i, uint64(m.Rank))
	dAtA[i] = 0x60
	i++
	i = encodeVarintActivity(dAtA, i, uint64(m.GameId))
	dAtA[i] = 0x68
	i++
	i = encodeVarintActivity(dAtA, i, uint64(m.Status))
	return i, nil
}

func (m *CreateOfferEventReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CreateOfferEventReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.OfferEvent == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("offer_event")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintActivity(dAtA, i, uint64(m.OfferEvent.Size()))
		n3, err := m.OfferEvent.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n3
	}
	return i, nil
}

func (m *CreateOfferEventRsp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CreateOfferEventRsp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintActivity(dAtA, i, uint64(m.Id))
	return i, nil
}

func (m *ModifyOfferEventReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ModifyOfferEventReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.OfferEvent == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("offer_event")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintActivity(dAtA, i, uint64(m.OfferEvent.Size()))
		n4, err := m.OfferEvent.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n4
	}
	return i, nil
}

func (m *DelOfferEventReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DelOfferEventReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintActivity(dAtA, i, uint64(m.Id))
	return i, nil
}

func (m *GetOfferEventReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetOfferEventReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintActivity(dAtA, i, uint64(m.Status))
	dAtA[i] = 0x10
	i++
	i = encodeVarintActivity(dAtA, i, uint64(m.Type))
	if len(m.GameidList) > 0 {
		for _, num := range m.GameidList {
			dAtA[i] = 0x18
			i++
			i = encodeVarintActivity(dAtA, i, uint64(num))
		}
	}
	dAtA[i] = 0x20
	i++
	i = encodeVarintActivity(dAtA, i, uint64(m.FromIndex))
	dAtA[i] = 0x28
	i++
	i = encodeVarintActivity(dAtA, i, uint64(m.Count))
	return i, nil
}

func (m *GetOfferEventRsp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetOfferEventRsp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.EventList) > 0 {
		for _, msg := range m.EventList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintActivity(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *GameRankElem) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GameRankElem) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintActivity(dAtA, i, uint64(m.GameId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintActivity(dAtA, i, uint64(m.Rank))
	return i, nil
}

func (m *AddGameRankReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AddGameRankReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Gamerank == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("gamerank")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintActivity(dAtA, i, uint64(m.Gamerank.Size()))
		n5, err := m.Gamerank.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n5
	}
	return i, nil
}

func (m *ModifyGameRankReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ModifyGameRankReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Gamerank == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("gamerank")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintActivity(dAtA, i, uint64(m.Gamerank.Size()))
		n6, err := m.Gamerank.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n6
	}
	return i, nil
}

func (m *DelGameRankReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DelGameRankReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.GameidList) > 0 {
		for _, num := range m.GameidList {
			dAtA[i] = 0x8
			i++
			i = encodeVarintActivity(dAtA, i, uint64(num))
		}
	}
	return i, nil
}

func (m *GetGameRankReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetGameRankReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintActivity(dAtA, i, uint64(m.FromIndex))
	dAtA[i] = 0x10
	i++
	i = encodeVarintActivity(dAtA, i, uint64(m.Count))
	return i, nil
}

func (m *GetGameRankRsp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetGameRankRsp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.GamerankList) > 0 {
		for _, msg := range m.GamerankList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintActivity(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *GetGameOfferReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetGameOfferReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintActivity(dAtA, i, uint64(m.GameId))
	return i, nil
}

func (m *GameOfferInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GameOfferInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintActivity(dAtA, i, uint64(m.GameId))
	dAtA[i] = 0x12
	i++
	i = encodeVarintActivity(dAtA, i, uint64(len(m.ActivityName)))
	i += copy(dAtA[i:], m.ActivityName)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintActivity(dAtA, i, uint64(len(m.ActivityDesc)))
	i += copy(dAtA[i:], m.ActivityDesc)
	dAtA[i] = 0x22
	i++
	i = encodeVarintActivity(dAtA, i, uint64(len(m.OfferDesc)))
	i += copy(dAtA[i:], m.OfferDesc)
	dAtA[i] = 0x2a
	i++
	i = encodeVarintActivity(dAtA, i, uint64(len(m.OfferNotes)))
	i += copy(dAtA[i:], m.OfferNotes)
	dAtA[i] = 0x30
	i++
	i = encodeVarintActivity(dAtA, i, uint64(m.OfferCategoryid))
	return i, nil
}

func (m *GetGameOfferRsp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetGameOfferRsp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.GameOffer) > 0 {
		for _, msg := range m.GameOffer {
			dAtA[i] = 0xa
			i++
			i = encodeVarintActivity(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *FirstVoucherAccountPassword) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *FirstVoucherAccountPassword) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintActivity(dAtA, i, uint64(len(m.Account)))
	i += copy(dAtA[i:], m.Account)
	dAtA[i] = 0x12
	i++
	i = encodeVarintActivity(dAtA, i, uint64(len(m.Password)))
	i += copy(dAtA[i:], m.Password)
	return i, nil
}

func (m *FirstVoucherProduct) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *FirstVoucherProduct) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintActivity(dAtA, i, uint64(m.ProductId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintActivity(dAtA, i, uint64(m.Price))
	dAtA[i] = 0x18
	i++
	i = encodeVarintActivity(dAtA, i, uint64(m.Worth))
	dAtA[i] = 0x20
	i++
	i = encodeVarintActivity(dAtA, i, uint64(m.GameId))
	dAtA[i] = 0x28
	i++
	i = encodeVarintActivity(dAtA, i, uint64(m.ExpireTime))
	return i, nil
}

func (m *GetFirstVoucherProductsReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetFirstVoucherProductsReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintActivity(dAtA, i, uint64(m.Index))
	dAtA[i] = 0x10
	i++
	i = encodeVarintActivity(dAtA, i, uint64(m.Limit))
	return i, nil
}

func (m *GetFirstVoucherProductsResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetFirstVoucherProductsResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.ProductList) > 0 {
		for _, msg := range m.ProductList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintActivity(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *AddFirstVoucherProductReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AddFirstVoucherProductReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintActivity(dAtA, i, uint64(m.Price))
	dAtA[i] = 0x10
	i++
	i = encodeVarintActivity(dAtA, i, uint64(m.Worth))
	dAtA[i] = 0x18
	i++
	i = encodeVarintActivity(dAtA, i, uint64(m.GameId))
	dAtA[i] = 0x20
	i++
	i = encodeVarintActivity(dAtA, i, uint64(m.ExpireTime))
	return i, nil
}

func (m *AddFirstVoucherProductResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AddFirstVoucherProductResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintActivity(dAtA, i, uint64(m.ProductId))
	return i, nil
}

func (m *ModifyFirstVoucherProductReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ModifyFirstVoucherProductReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintActivity(dAtA, i, uint64(m.ProductId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintActivity(dAtA, i, uint64(m.Invalid))
	dAtA[i] = 0x18
	i++
	i = encodeVarintActivity(dAtA, i, uint64(m.Price))
	dAtA[i] = 0x20
	i++
	i = encodeVarintActivity(dAtA, i, uint64(m.Worth))
	dAtA[i] = 0x28
	i++
	i = encodeVarintActivity(dAtA, i, uint64(m.ExpireTime))
	return i, nil
}

func (m *GetGameFirstVoucherProductReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetGameFirstVoucherProductReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintActivity(dAtA, i, uint64(m.GameId))
	return i, nil
}

func (m *GetGameFirstVoucherProductResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetGameFirstVoucherProductResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Product == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("product")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintActivity(dAtA, i, uint64(m.Product.Size()))
		n7, err := m.Product.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n7
	}
	return i, nil
}

func (m *DeleteFirstVoucherProductReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DeleteFirstVoucherProductReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintActivity(dAtA, i, uint64(m.ProductId))
	return i, nil
}

func (m *FirstVoucherItem) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *FirstVoucherItem) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintActivity(dAtA, i, uint64(len(m.ItemHash)))
	i += copy(dAtA[i:], m.ItemHash)
	if m.ItemBinary != nil {
		dAtA[i] = 0x12
		i++
		i = encodeVarintActivity(dAtA, i, uint64(len(m.ItemBinary)))
		i += copy(dAtA[i:], m.ItemBinary)
	}
	return i, nil
}

func (m *AddFirstVoucherItemReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AddFirstVoucherItemReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.ItemList) > 0 {
		for _, msg := range m.ItemList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintActivity(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintActivity(dAtA, i, uint64(m.ProductId))
	return i, nil
}

func (m *AddFirstVoucherItemResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AddFirstVoucherItemResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.SuccessProductItemList) > 0 {
		for _, msg := range m.SuccessProductItemList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintActivity(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	if len(m.FailedProductItemList) > 0 {
		for _, msg := range m.FailedProductItemList {
			dAtA[i] = 0x12
			i++
			i = encodeVarintActivity(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *GetFirstVoucherItemReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetFirstVoucherItemReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintActivity(dAtA, i, uint64(m.ProductId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintActivity(dAtA, i, uint64(m.Status))
	return i, nil
}

func (m *GetFirstVoucherItemResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetFirstVoucherItemResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.ItemList) > 0 {
		for _, msg := range m.ItemList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintActivity(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *DeleteFirstVoucherItemReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DeleteFirstVoucherItemReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintActivity(dAtA, i, uint64(m.ProductId))
	if len(m.ItemHashList) > 0 {
		for _, s := range m.ItemHashList {
			dAtA[i] = 0x12
			i++
			l = len(s)
			for l >= 1<<7 {
				dAtA[i] = uint8(uint64(l)&0x7f | 0x80)
				l >>= 7
				i++
			}
			dAtA[i] = uint8(l)
			i++
			i += copy(dAtA[i:], s)
		}
	}
	return i, nil
}

func (m *FirstVoucherUserItem) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *FirstVoucherUserItem) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintActivity(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintActivity(dAtA, i, uint64(m.Timestamp))
	if m.ProductSnapshot == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("product_snapshot")
	} else {
		dAtA[i] = 0x1a
		i++
		i = encodeVarintActivity(dAtA, i, uint64(m.ProductSnapshot.Size()))
		n8, err := m.ProductSnapshot.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n8
	}
	if m.ItemSnapshot == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("item_snapshot")
	} else {
		dAtA[i] = 0x22
		i++
		i = encodeVarintActivity(dAtA, i, uint64(m.ItemSnapshot.Size()))
		n9, err := m.ItemSnapshot.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n9
	}
	dAtA[i] = 0x2a
	i++
	i = encodeVarintActivity(dAtA, i, uint64(len(m.OrderId)))
	i += copy(dAtA[i:], m.OrderId)
	return i, nil
}

func (m *PurchaseFirstVoucherItemReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *PurchaseFirstVoucherItemReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintActivity(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintActivity(dAtA, i, uint64(m.ProductId))
	return i, nil
}

func (m *PurchaseFirstVoucherItemResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *PurchaseFirstVoucherItemResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.FirstVoucherItem == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("first_voucher_item")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintActivity(dAtA, i, uint64(m.FirstVoucherItem.Size()))
		n10, err := m.FirstVoucherItem.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n10
	}
	return i, nil
}

func (m *GetFirstVoucherUserItemReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetFirstVoucherUserItemReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintActivity(dAtA, i, uint64(m.Uid))
	return i, nil
}

func (m *GetFirstVoucherUserItemResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetFirstVoucherUserItemResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.UserItemList) > 0 {
		for _, msg := range m.UserItemList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintActivity(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *ActBannerInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ActBannerInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintActivity(dAtA, i, uint64(m.Id))
	dAtA[i] = 0x12
	i++
	i = encodeVarintActivity(dAtA, i, uint64(len(m.ActionUrl)))
	i += copy(dAtA[i:], m.ActionUrl)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintActivity(dAtA, i, uint64(len(m.Title)))
	i += copy(dAtA[i:], m.Title)
	dAtA[i] = 0x22
	i++
	i = encodeVarintActivity(dAtA, i, uint64(len(m.SubTitle)))
	i += copy(dAtA[i:], m.SubTitle)
	dAtA[i] = 0x2a
	i++
	i = encodeVarintActivity(dAtA, i, uint64(len(m.IconUrl)))
	i += copy(dAtA[i:], m.IconUrl)
	dAtA[i] = 0x32
	i++
	i = encodeVarintActivity(dAtA, i, uint64(len(m.CoverUrl)))
	i += copy(dAtA[i:], m.CoverUrl)
	dAtA[i] = 0x38
	i++
	i = encodeVarintActivity(dAtA, i, uint64(m.AndroidVersion))
	dAtA[i] = 0x40
	i++
	i = encodeVarintActivity(dAtA, i, uint64(m.IosVersion))
	dAtA[i] = 0x48
	i++
	i = encodeVarintActivity(dAtA, i, uint64(m.Rank))
	return i, nil
}

func (m *UpdateActBannerReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UpdateActBannerReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Banner == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("banner")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintActivity(dAtA, i, uint64(m.Banner.Size()))
		n11, err := m.Banner.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n11
	}
	return i, nil
}

func (m *GetActBannersResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetActBannersResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.BannerList) > 0 {
		for _, msg := range m.BannerList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintActivity(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *DelActBannerReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DelActBannerReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintActivity(dAtA, i, uint64(m.Id))
	return i, nil
}

func (m *GetActBannerOpTimeResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetActBannerOpTimeResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintActivity(dAtA, i, uint64(m.OpTime))
	return i, nil
}

func (m *ActSplashScreenInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ActSplashScreenInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintActivity(dAtA, i, uint64(m.Id))
	dAtA[i] = 0x12
	i++
	i = encodeVarintActivity(dAtA, i, uint64(len(m.ScreenUrl)))
	i += copy(dAtA[i:], m.ScreenUrl)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintActivity(dAtA, i, uint64(len(m.ActionUrl)))
	i += copy(dAtA[i:], m.ActionUrl)
	dAtA[i] = 0x22
	i++
	i = encodeVarintActivity(dAtA, i, uint64(len(m.StartTime)))
	i += copy(dAtA[i:], m.StartTime)
	dAtA[i] = 0x2a
	i++
	i = encodeVarintActivity(dAtA, i, uint64(len(m.EndTime)))
	i += copy(dAtA[i:], m.EndTime)
	dAtA[i] = 0x30
	i++
	i = encodeVarintActivity(dAtA, i, uint64(m.ContinueTime))
	dAtA[i] = 0x38
	i++
	i = encodeVarintActivity(dAtA, i, uint64(m.ClientType))
	dAtA[i] = 0x40
	i++
	i = encodeVarintActivity(dAtA, i, uint64(m.AppId))
	dAtA[i] = 0x48
	i++
	i = encodeVarintActivity(dAtA, i, uint64(m.MarketId))
	return i, nil
}

func (m *UpdateActSplashScreenReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UpdateActSplashScreenReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.SplashScreen == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("splash_screen")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintActivity(dAtA, i, uint64(m.SplashScreen.Size()))
		n12, err := m.SplashScreen.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n12
	}
	return i, nil
}

func (m *GetActSplashScreenResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetActSplashScreenResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.SplashScreen != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintActivity(dAtA, i, uint64(m.SplashScreen.Size()))
		n13, err := m.SplashScreen.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n13
	}
	if m.SplashScreenIos != nil {
		dAtA[i] = 0x12
		i++
		i = encodeVarintActivity(dAtA, i, uint64(m.SplashScreenIos.Size()))
		n14, err := m.SplashScreenIos.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n14
	}
	if len(m.SplashScreenList) > 0 {
		for _, msg := range m.SplashScreenList {
			dAtA[i] = 0x1a
			i++
			i = encodeVarintActivity(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *DelActSplashScreenReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DelActSplashScreenReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintActivity(dAtA, i, uint64(m.Id))
	return i, nil
}

func (m *ActSplashScreenOpTime) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ActSplashScreenOpTime) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintActivity(dAtA, i, uint64(m.AppId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintActivity(dAtA, i, uint64(m.MarketId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintActivity(dAtA, i, uint64(m.ClientType))
	dAtA[i] = 0x20
	i++
	i = encodeVarintActivity(dAtA, i, uint64(m.OpTime))
	return i, nil
}

func (m *GetActSplashScreenOpTimeResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetActSplashScreenOpTimeResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintActivity(dAtA, i, uint64(m.OpTime))
	dAtA[i] = 0x10
	i++
	i = encodeVarintActivity(dAtA, i, uint64(m.OpTimeIos))
	if len(m.OptimeList) > 0 {
		for _, msg := range m.OptimeList {
			dAtA[i] = 0x1a
			i++
			i = encodeVarintActivity(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *GetGamePreorderResultReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetGamePreorderResultReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintActivity(dAtA, i, uint64(m.GameId))
	return i, nil
}

func (m *GamePreorderResult) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GamePreorderResult) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintActivity(dAtA, i, uint64(m.ObjId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintActivity(dAtA, i, uint64(m.Count))
	return i, nil
}

func (m *GetGamePreorderResultResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetGamePreorderResultResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.ResultList) > 0 {
		for _, msg := range m.ResultList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintActivity(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *GetGamePreorderUserSpptReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetGamePreorderUserSpptReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintActivity(dAtA, i, uint64(m.GameId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintActivity(dAtA, i, uint64(m.Uid))
	return i, nil
}

func (m *GetGamePreorderUserSpptResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetGamePreorderUserSpptResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintActivity(dAtA, i, uint64(m.ObjId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintActivity(dAtA, i, uint64(m.Count))
	return i, nil
}

func (m *SetGamePreorderUserSpptReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SetGamePreorderUserSpptReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintActivity(dAtA, i, uint64(m.GameId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintActivity(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x18
	i++
	i = encodeVarintActivity(dAtA, i, uint64(m.ObjId))
	dAtA[i] = 0x20
	i++
	i = encodeVarintActivity(dAtA, i, uint64(m.Count))
	return i, nil
}

func (m *InviterInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *InviterInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintActivity(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintActivity(dAtA, i, uint64(m.InviteNum))
	return i, nil
}

func (m *GetQQCarActivityRankResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetQQCarActivityRankResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.InviteInfos) > 0 {
		for _, msg := range m.InviteInfos {
			dAtA[i] = 0xa
			i++
			i = encodeVarintActivity(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *NotifyInviteRecordReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *NotifyInviteRecordReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintActivity(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintActivity(dAtA, i, uint64(m.InviterUid))
	return i, nil
}

func (m *GetPrizeInfoReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetPrizeInfoReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintActivity(dAtA, i, uint64(m.Uid))
	return i, nil
}

func (m *GetPrizeInfoResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetPrizeInfoResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintActivity(dAtA, i, uint64(m.DayRank))
	dAtA[i] = 0x10
	i++
	i = encodeVarintActivity(dAtA, i, uint64(m.InviteNum))
	dAtA[i] = 0x18
	i++
	i = encodeVarintActivity(dAtA, i, uint64(m.TotalRank))
	dAtA[i] = 0x20
	i++
	i = encodeVarintActivity(dAtA, i, uint64(m.FristRegister))
	dAtA[i] = 0x28
	i++
	i = encodeVarintActivity(dAtA, i, uint64(m.DayInvite))
	dAtA[i] = 0x30
	i++
	i = encodeVarintActivity(dAtA, i, uint64(m.TotalInvite))
	return i, nil
}

func (m *SetUidPairQQReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SetUidPairQQReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintActivity(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x12
	i++
	i = encodeVarintActivity(dAtA, i, uint64(len(m.Qq)))
	i += copy(dAtA[i:], m.Qq)
	return i, nil
}

func (m *TakePrizeReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *TakePrizeReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintActivity(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintActivity(dAtA, i, uint64(m.PrizeType))
	dAtA[i] = 0x1a
	i++
	i = encodeVarintActivity(dAtA, i, uint64(len(m.QqNumber)))
	i += copy(dAtA[i:], m.QqNumber)
	return i, nil
}

func (m *TakePrizeResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *TakePrizeResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func encodeFixed64Activity(dAtA []byte, offset int, v uint64) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	dAtA[offset+4] = uint8(v >> 32)
	dAtA[offset+5] = uint8(v >> 40)
	dAtA[offset+6] = uint8(v >> 48)
	dAtA[offset+7] = uint8(v >> 56)
	return offset + 8
}
func encodeFixed32Activity(dAtA []byte, offset int, v uint32) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	return offset + 4
}
func encodeVarintActivity(dAtA []byte, offset int, v uint64) int {
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return offset + 1
}
func (m *OfferCategoryInfo) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovActivity(uint64(m.Id))
	l = len(m.Name)
	n += 1 + l + sovActivity(uint64(l))
	return n
}

func (m *OfferCategoryList) Size() (n int) {
	var l int
	_ = l
	if len(m.List) > 0 {
		for _, e := range m.List {
			l = e.Size()
			n += 1 + l + sovActivity(uint64(l))
		}
	}
	return n
}

func (m *ActivityOfferInfo) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovActivity(uint64(m.GameId))
	n += 1 + sovActivity(uint64(m.ActivityId))
	n += 1 + sovActivity(uint64(m.CategoryId))
	n += 1 + sovActivity(uint64(m.Amount))
	l = len(m.OfferDesc)
	n += 1 + l + sovActivity(uint64(l))
	l = len(m.OfferNotes)
	n += 1 + l + sovActivity(uint64(l))
	l = len(m.OfferAppendix)
	n += 1 + l + sovActivity(uint64(l))
	return n
}

func (m *ActivityInfo) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovActivity(uint64(m.ActivityId))
	l = len(m.Name)
	n += 1 + l + sovActivity(uint64(l))
	l = len(m.Description)
	n += 1 + l + sovActivity(uint64(l))
	l = len(m.Banner)
	n += 1 + l + sovActivity(uint64(l))
	n += 1 + sovActivity(uint64(m.BeginTime))
	n += 1 + sovActivity(uint64(m.EndTime))
	n += 1 + sovActivity(uint64(m.Status))
	if len(m.OffersList) > 0 {
		for _, e := range m.OffersList {
			l = e.Size()
			n += 1 + l + sovActivity(uint64(l))
		}
	}
	return n
}

func (m *CreateActivityReq) Size() (n int) {
	var l int
	_ = l
	if m.Activity != nil {
		l = m.Activity.Size()
		n += 1 + l + sovActivity(uint64(l))
	}
	return n
}

func (m *CreateActivityResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovActivity(uint64(m.ActivityId))
	return n
}

func (m *UpdateActivityReq) Size() (n int) {
	var l int
	_ = l
	if m.Activity != nil {
		l = m.Activity.Size()
		n += 1 + l + sovActivity(uint64(l))
	}
	return n
}

func (m *UpdateActivityResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovActivity(uint64(m.ActivityId))
	return n
}

func (m *DeleteActivityReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovActivity(uint64(m.ActivityId))
	if len(m.GameidList) > 0 {
		for _, e := range m.GameidList {
			n += 1 + sovActivity(uint64(e))
		}
	}
	return n
}

func (m *GetActivityListReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovActivity(uint64(m.Status))
	n += 1 + sovActivity(uint64(m.FromIndex))
	n += 1 + sovActivity(uint64(m.Count))
	n += 2
	n += 1 + sovActivity(uint64(m.GameId))
	return n
}

func (m *GetActivityListResp) Size() (n int) {
	var l int
	_ = l
	if len(m.ActivityList) > 0 {
		for _, e := range m.ActivityList {
			l = e.Size()
			n += 1 + l + sovActivity(uint64(l))
		}
	}
	return n
}

func (m *OfferEventElem) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovActivity(uint64(m.Id))
	l = len(m.Name)
	n += 1 + l + sovActivity(uint64(l))
	l = len(m.Description)
	n += 1 + l + sovActivity(uint64(l))
	l = len(m.Notes)
	n += 1 + l + sovActivity(uint64(l))
	n += 1 + sovActivity(uint64(m.BeginTime))
	n += 1 + sovActivity(uint64(m.EndTime))
	n += 1 + sovActivity(uint64(m.NowTime))
	l = len(m.Banner)
	n += 1 + l + sovActivity(uint64(l))
	l = len(m.DetailHref)
	n += 1 + l + sovActivity(uint64(l))
	n += 1 + sovActivity(uint64(m.Type))
	n += 1 + sovActivity(uint64(m.Rank))
	n += 1 + sovActivity(uint64(m.GameId))
	n += 1 + sovActivity(uint64(m.Status))
	return n
}

func (m *CreateOfferEventReq) Size() (n int) {
	var l int
	_ = l
	if m.OfferEvent != nil {
		l = m.OfferEvent.Size()
		n += 1 + l + sovActivity(uint64(l))
	}
	return n
}

func (m *CreateOfferEventRsp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovActivity(uint64(m.Id))
	return n
}

func (m *ModifyOfferEventReq) Size() (n int) {
	var l int
	_ = l
	if m.OfferEvent != nil {
		l = m.OfferEvent.Size()
		n += 1 + l + sovActivity(uint64(l))
	}
	return n
}

func (m *DelOfferEventReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovActivity(uint64(m.Id))
	return n
}

func (m *GetOfferEventReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovActivity(uint64(m.Status))
	n += 1 + sovActivity(uint64(m.Type))
	if len(m.GameidList) > 0 {
		for _, e := range m.GameidList {
			n += 1 + sovActivity(uint64(e))
		}
	}
	n += 1 + sovActivity(uint64(m.FromIndex))
	n += 1 + sovActivity(uint64(m.Count))
	return n
}

func (m *GetOfferEventRsp) Size() (n int) {
	var l int
	_ = l
	if len(m.EventList) > 0 {
		for _, e := range m.EventList {
			l = e.Size()
			n += 1 + l + sovActivity(uint64(l))
		}
	}
	return n
}

func (m *GameRankElem) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovActivity(uint64(m.GameId))
	n += 1 + sovActivity(uint64(m.Rank))
	return n
}

func (m *AddGameRankReq) Size() (n int) {
	var l int
	_ = l
	if m.Gamerank != nil {
		l = m.Gamerank.Size()
		n += 1 + l + sovActivity(uint64(l))
	}
	return n
}

func (m *ModifyGameRankReq) Size() (n int) {
	var l int
	_ = l
	if m.Gamerank != nil {
		l = m.Gamerank.Size()
		n += 1 + l + sovActivity(uint64(l))
	}
	return n
}

func (m *DelGameRankReq) Size() (n int) {
	var l int
	_ = l
	if len(m.GameidList) > 0 {
		for _, e := range m.GameidList {
			n += 1 + sovActivity(uint64(e))
		}
	}
	return n
}

func (m *GetGameRankReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovActivity(uint64(m.FromIndex))
	n += 1 + sovActivity(uint64(m.Count))
	return n
}

func (m *GetGameRankRsp) Size() (n int) {
	var l int
	_ = l
	if len(m.GamerankList) > 0 {
		for _, e := range m.GamerankList {
			l = e.Size()
			n += 1 + l + sovActivity(uint64(l))
		}
	}
	return n
}

func (m *GetGameOfferReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovActivity(uint64(m.GameId))
	return n
}

func (m *GameOfferInfo) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovActivity(uint64(m.GameId))
	l = len(m.ActivityName)
	n += 1 + l + sovActivity(uint64(l))
	l = len(m.ActivityDesc)
	n += 1 + l + sovActivity(uint64(l))
	l = len(m.OfferDesc)
	n += 1 + l + sovActivity(uint64(l))
	l = len(m.OfferNotes)
	n += 1 + l + sovActivity(uint64(l))
	n += 1 + sovActivity(uint64(m.OfferCategoryid))
	return n
}

func (m *GetGameOfferRsp) Size() (n int) {
	var l int
	_ = l
	if len(m.GameOffer) > 0 {
		for _, e := range m.GameOffer {
			l = e.Size()
			n += 1 + l + sovActivity(uint64(l))
		}
	}
	return n
}

func (m *FirstVoucherAccountPassword) Size() (n int) {
	var l int
	_ = l
	l = len(m.Account)
	n += 1 + l + sovActivity(uint64(l))
	l = len(m.Password)
	n += 1 + l + sovActivity(uint64(l))
	return n
}

func (m *FirstVoucherProduct) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovActivity(uint64(m.ProductId))
	n += 1 + sovActivity(uint64(m.Price))
	n += 1 + sovActivity(uint64(m.Worth))
	n += 1 + sovActivity(uint64(m.GameId))
	n += 1 + sovActivity(uint64(m.ExpireTime))
	return n
}

func (m *GetFirstVoucherProductsReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovActivity(uint64(m.Index))
	n += 1 + sovActivity(uint64(m.Limit))
	return n
}

func (m *GetFirstVoucherProductsResp) Size() (n int) {
	var l int
	_ = l
	if len(m.ProductList) > 0 {
		for _, e := range m.ProductList {
			l = e.Size()
			n += 1 + l + sovActivity(uint64(l))
		}
	}
	return n
}

func (m *AddFirstVoucherProductReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovActivity(uint64(m.Price))
	n += 1 + sovActivity(uint64(m.Worth))
	n += 1 + sovActivity(uint64(m.GameId))
	n += 1 + sovActivity(uint64(m.ExpireTime))
	return n
}

func (m *AddFirstVoucherProductResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovActivity(uint64(m.ProductId))
	return n
}

func (m *ModifyFirstVoucherProductReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovActivity(uint64(m.ProductId))
	n += 1 + sovActivity(uint64(m.Invalid))
	n += 1 + sovActivity(uint64(m.Price))
	n += 1 + sovActivity(uint64(m.Worth))
	n += 1 + sovActivity(uint64(m.ExpireTime))
	return n
}

func (m *GetGameFirstVoucherProductReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovActivity(uint64(m.GameId))
	return n
}

func (m *GetGameFirstVoucherProductResp) Size() (n int) {
	var l int
	_ = l
	if m.Product != nil {
		l = m.Product.Size()
		n += 1 + l + sovActivity(uint64(l))
	}
	return n
}

func (m *DeleteFirstVoucherProductReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovActivity(uint64(m.ProductId))
	return n
}

func (m *FirstVoucherItem) Size() (n int) {
	var l int
	_ = l
	l = len(m.ItemHash)
	n += 1 + l + sovActivity(uint64(l))
	if m.ItemBinary != nil {
		l = len(m.ItemBinary)
		n += 1 + l + sovActivity(uint64(l))
	}
	return n
}

func (m *AddFirstVoucherItemReq) Size() (n int) {
	var l int
	_ = l
	if len(m.ItemList) > 0 {
		for _, e := range m.ItemList {
			l = e.Size()
			n += 1 + l + sovActivity(uint64(l))
		}
	}
	n += 1 + sovActivity(uint64(m.ProductId))
	return n
}

func (m *AddFirstVoucherItemResp) Size() (n int) {
	var l int
	_ = l
	if len(m.SuccessProductItemList) > 0 {
		for _, e := range m.SuccessProductItemList {
			l = e.Size()
			n += 1 + l + sovActivity(uint64(l))
		}
	}
	if len(m.FailedProductItemList) > 0 {
		for _, e := range m.FailedProductItemList {
			l = e.Size()
			n += 1 + l + sovActivity(uint64(l))
		}
	}
	return n
}

func (m *GetFirstVoucherItemReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovActivity(uint64(m.ProductId))
	n += 1 + sovActivity(uint64(m.Status))
	return n
}

func (m *GetFirstVoucherItemResp) Size() (n int) {
	var l int
	_ = l
	if len(m.ItemList) > 0 {
		for _, e := range m.ItemList {
			l = e.Size()
			n += 1 + l + sovActivity(uint64(l))
		}
	}
	return n
}

func (m *DeleteFirstVoucherItemReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovActivity(uint64(m.ProductId))
	if len(m.ItemHashList) > 0 {
		for _, s := range m.ItemHashList {
			l = len(s)
			n += 1 + l + sovActivity(uint64(l))
		}
	}
	return n
}

func (m *FirstVoucherUserItem) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovActivity(uint64(m.Uid))
	n += 1 + sovActivity(uint64(m.Timestamp))
	if m.ProductSnapshot != nil {
		l = m.ProductSnapshot.Size()
		n += 1 + l + sovActivity(uint64(l))
	}
	if m.ItemSnapshot != nil {
		l = m.ItemSnapshot.Size()
		n += 1 + l + sovActivity(uint64(l))
	}
	l = len(m.OrderId)
	n += 1 + l + sovActivity(uint64(l))
	return n
}

func (m *PurchaseFirstVoucherItemReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovActivity(uint64(m.Uid))
	n += 1 + sovActivity(uint64(m.ProductId))
	return n
}

func (m *PurchaseFirstVoucherItemResp) Size() (n int) {
	var l int
	_ = l
	if m.FirstVoucherItem != nil {
		l = m.FirstVoucherItem.Size()
		n += 1 + l + sovActivity(uint64(l))
	}
	return n
}

func (m *GetFirstVoucherUserItemReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovActivity(uint64(m.Uid))
	return n
}

func (m *GetFirstVoucherUserItemResp) Size() (n int) {
	var l int
	_ = l
	if len(m.UserItemList) > 0 {
		for _, e := range m.UserItemList {
			l = e.Size()
			n += 1 + l + sovActivity(uint64(l))
		}
	}
	return n
}

func (m *ActBannerInfo) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovActivity(uint64(m.Id))
	l = len(m.ActionUrl)
	n += 1 + l + sovActivity(uint64(l))
	l = len(m.Title)
	n += 1 + l + sovActivity(uint64(l))
	l = len(m.SubTitle)
	n += 1 + l + sovActivity(uint64(l))
	l = len(m.IconUrl)
	n += 1 + l + sovActivity(uint64(l))
	l = len(m.CoverUrl)
	n += 1 + l + sovActivity(uint64(l))
	n += 1 + sovActivity(uint64(m.AndroidVersion))
	n += 1 + sovActivity(uint64(m.IosVersion))
	n += 1 + sovActivity(uint64(m.Rank))
	return n
}

func (m *UpdateActBannerReq) Size() (n int) {
	var l int
	_ = l
	if m.Banner != nil {
		l = m.Banner.Size()
		n += 1 + l + sovActivity(uint64(l))
	}
	return n
}

func (m *GetActBannersResp) Size() (n int) {
	var l int
	_ = l
	if len(m.BannerList) > 0 {
		for _, e := range m.BannerList {
			l = e.Size()
			n += 1 + l + sovActivity(uint64(l))
		}
	}
	return n
}

func (m *DelActBannerReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovActivity(uint64(m.Id))
	return n
}

func (m *GetActBannerOpTimeResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovActivity(uint64(m.OpTime))
	return n
}

func (m *ActSplashScreenInfo) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovActivity(uint64(m.Id))
	l = len(m.ScreenUrl)
	n += 1 + l + sovActivity(uint64(l))
	l = len(m.ActionUrl)
	n += 1 + l + sovActivity(uint64(l))
	l = len(m.StartTime)
	n += 1 + l + sovActivity(uint64(l))
	l = len(m.EndTime)
	n += 1 + l + sovActivity(uint64(l))
	n += 1 + sovActivity(uint64(m.ContinueTime))
	n += 1 + sovActivity(uint64(m.ClientType))
	n += 1 + sovActivity(uint64(m.AppId))
	n += 1 + sovActivity(uint64(m.MarketId))
	return n
}

func (m *UpdateActSplashScreenReq) Size() (n int) {
	var l int
	_ = l
	if m.SplashScreen != nil {
		l = m.SplashScreen.Size()
		n += 1 + l + sovActivity(uint64(l))
	}
	return n
}

func (m *GetActSplashScreenResp) Size() (n int) {
	var l int
	_ = l
	if m.SplashScreen != nil {
		l = m.SplashScreen.Size()
		n += 1 + l + sovActivity(uint64(l))
	}
	if m.SplashScreenIos != nil {
		l = m.SplashScreenIos.Size()
		n += 1 + l + sovActivity(uint64(l))
	}
	if len(m.SplashScreenList) > 0 {
		for _, e := range m.SplashScreenList {
			l = e.Size()
			n += 1 + l + sovActivity(uint64(l))
		}
	}
	return n
}

func (m *DelActSplashScreenReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovActivity(uint64(m.Id))
	return n
}

func (m *ActSplashScreenOpTime) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovActivity(uint64(m.AppId))
	n += 1 + sovActivity(uint64(m.MarketId))
	n += 1 + sovActivity(uint64(m.ClientType))
	n += 1 + sovActivity(uint64(m.OpTime))
	return n
}

func (m *GetActSplashScreenOpTimeResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovActivity(uint64(m.OpTime))
	n += 1 + sovActivity(uint64(m.OpTimeIos))
	if len(m.OptimeList) > 0 {
		for _, e := range m.OptimeList {
			l = e.Size()
			n += 1 + l + sovActivity(uint64(l))
		}
	}
	return n
}

func (m *GetGamePreorderResultReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovActivity(uint64(m.GameId))
	return n
}

func (m *GamePreorderResult) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovActivity(uint64(m.ObjId))
	n += 1 + sovActivity(uint64(m.Count))
	return n
}

func (m *GetGamePreorderResultResp) Size() (n int) {
	var l int
	_ = l
	if len(m.ResultList) > 0 {
		for _, e := range m.ResultList {
			l = e.Size()
			n += 1 + l + sovActivity(uint64(l))
		}
	}
	return n
}

func (m *GetGamePreorderUserSpptReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovActivity(uint64(m.GameId))
	n += 1 + sovActivity(uint64(m.Uid))
	return n
}

func (m *GetGamePreorderUserSpptResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovActivity(uint64(m.ObjId))
	n += 1 + sovActivity(uint64(m.Count))
	return n
}

func (m *SetGamePreorderUserSpptReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovActivity(uint64(m.GameId))
	n += 1 + sovActivity(uint64(m.Uid))
	n += 1 + sovActivity(uint64(m.ObjId))
	n += 1 + sovActivity(uint64(m.Count))
	return n
}

func (m *InviterInfo) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovActivity(uint64(m.Uid))
	n += 1 + sovActivity(uint64(m.InviteNum))
	return n
}

func (m *GetQQCarActivityRankResp) Size() (n int) {
	var l int
	_ = l
	if len(m.InviteInfos) > 0 {
		for _, e := range m.InviteInfos {
			l = e.Size()
			n += 1 + l + sovActivity(uint64(l))
		}
	}
	return n
}

func (m *NotifyInviteRecordReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovActivity(uint64(m.Uid))
	n += 1 + sovActivity(uint64(m.InviterUid))
	return n
}

func (m *GetPrizeInfoReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovActivity(uint64(m.Uid))
	return n
}

func (m *GetPrizeInfoResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovActivity(uint64(m.DayRank))
	n += 1 + sovActivity(uint64(m.InviteNum))
	n += 1 + sovActivity(uint64(m.TotalRank))
	n += 1 + sovActivity(uint64(m.FristRegister))
	n += 1 + sovActivity(uint64(m.DayInvite))
	n += 1 + sovActivity(uint64(m.TotalInvite))
	return n
}

func (m *SetUidPairQQReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovActivity(uint64(m.Uid))
	l = len(m.Qq)
	n += 1 + l + sovActivity(uint64(l))
	return n
}

func (m *TakePrizeReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovActivity(uint64(m.Uid))
	n += 1 + sovActivity(uint64(m.PrizeType))
	l = len(m.QqNumber)
	n += 1 + l + sovActivity(uint64(l))
	return n
}

func (m *TakePrizeResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func sovActivity(x uint64) (n int) {
	for {
		n++
		x >>= 7
		if x == 0 {
			break
		}
	}
	return n
}
func sozActivity(x uint64) (n int) {
	return sovActivity(uint64((x << 1) ^ uint64(int64(x) >> 63)))
}
func (m *OfferCategoryInfo) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivity
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: OfferCategoryInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: OfferCategoryInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Id", wireType)
			}
			m.Id = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Id |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Name", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthActivity
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Name = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipActivity(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivity
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("name")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *OfferCategoryList) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivity
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: OfferCategoryList: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: OfferCategoryList: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field List", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthActivity
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.List = append(m.List, &OfferCategoryInfo{})
			if err := m.List[len(m.List)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipActivity(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivity
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ActivityOfferInfo) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivity
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ActivityOfferInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ActivityOfferInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameId", wireType)
			}
			m.GameId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GameId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ActivityId", wireType)
			}
			m.ActivityId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ActivityId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CategoryId", wireType)
			}
			m.CategoryId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CategoryId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Amount", wireType)
			}
			m.Amount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Amount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OfferDesc", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthActivity
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.OfferDesc = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OfferNotes", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthActivity
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.OfferNotes = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 7:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OfferAppendix", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthActivity
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.OfferAppendix = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipActivity(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivity
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("game_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("category_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ActivityInfo) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivity
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ActivityInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ActivityInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ActivityId", wireType)
			}
			m.ActivityId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ActivityId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Name", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthActivity
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Name = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Description", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthActivity
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Description = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Banner", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthActivity
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Banner = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BeginTime", wireType)
			}
			m.BeginTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BeginTime |= (int64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field EndTime", wireType)
			}
			m.EndTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.EndTime |= (int64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Status", wireType)
			}
			m.Status = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Status |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 8:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OffersList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthActivity
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.OffersList = append(m.OffersList, &ActivityOfferInfo{})
			if err := m.OffersList[len(m.OffersList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipActivity(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivity
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("activity_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CreateActivityReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivity
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CreateActivityReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CreateActivityReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Activity", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthActivity
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Activity == nil {
				m.Activity = &ActivityInfo{}
			}
			if err := m.Activity.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipActivity(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivity
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("activity")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CreateActivityResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivity
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CreateActivityResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CreateActivityResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ActivityId", wireType)
			}
			m.ActivityId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ActivityId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipActivity(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivity
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("activity_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UpdateActivityReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivity
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UpdateActivityReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UpdateActivityReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Activity", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthActivity
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Activity == nil {
				m.Activity = &ActivityInfo{}
			}
			if err := m.Activity.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipActivity(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivity
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("activity")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UpdateActivityResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivity
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UpdateActivityResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UpdateActivityResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ActivityId", wireType)
			}
			m.ActivityId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ActivityId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipActivity(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivity
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("activity_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DeleteActivityReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivity
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: DeleteActivityReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: DeleteActivityReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ActivityId", wireType)
			}
			m.ActivityId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ActivityId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowActivity
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.GameidList = append(m.GameidList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowActivity
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthActivity
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowActivity
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.GameidList = append(m.GameidList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameidList", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipActivity(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivity
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("activity_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetActivityListReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivity
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetActivityListReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetActivityListReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Status", wireType)
			}
			m.Status = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Status |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FromIndex", wireType)
			}
			m.FromIndex = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FromIndex |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Count", wireType)
			}
			m.Count = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Count |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Reverse", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.Reverse = bool(v != 0)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameId", wireType)
			}
			m.GameId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GameId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipActivity(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivity
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetActivityListResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivity
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetActivityListResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetActivityListResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ActivityList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthActivity
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ActivityList = append(m.ActivityList, &ActivityInfo{})
			if err := m.ActivityList[len(m.ActivityList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipActivity(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivity
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *OfferEventElem) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivity
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: OfferEventElem: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: OfferEventElem: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Id", wireType)
			}
			m.Id = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Id |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Name", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthActivity
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Name = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Description", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthActivity
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Description = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Notes", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthActivity
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Notes = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BeginTime", wireType)
			}
			m.BeginTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BeginTime |= (int64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field EndTime", wireType)
			}
			m.EndTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.EndTime |= (int64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field NowTime", wireType)
			}
			m.NowTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.NowTime |= (int64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 8:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Banner", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthActivity
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Banner = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 9:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DetailHref", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthActivity
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.DetailHref = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 10:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Type", wireType)
			}
			m.Type = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Type |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 11:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Rank", wireType)
			}
			m.Rank = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Rank |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 12:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameId", wireType)
			}
			m.GameId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GameId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 13:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Status", wireType)
			}
			m.Status = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Status |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipActivity(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivity
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CreateOfferEventReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivity
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CreateOfferEventReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CreateOfferEventReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OfferEvent", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthActivity
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.OfferEvent == nil {
				m.OfferEvent = &OfferEventElem{}
			}
			if err := m.OfferEvent.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipActivity(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivity
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("offer_event")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CreateOfferEventRsp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivity
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CreateOfferEventRsp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CreateOfferEventRsp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Id", wireType)
			}
			m.Id = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Id |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipActivity(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivity
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ModifyOfferEventReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivity
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ModifyOfferEventReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ModifyOfferEventReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OfferEvent", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthActivity
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.OfferEvent == nil {
				m.OfferEvent = &OfferEventElem{}
			}
			if err := m.OfferEvent.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipActivity(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivity
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("offer_event")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DelOfferEventReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivity
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: DelOfferEventReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: DelOfferEventReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Id", wireType)
			}
			m.Id = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Id |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipActivity(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivity
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetOfferEventReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivity
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetOfferEventReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetOfferEventReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Status", wireType)
			}
			m.Status = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Status |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Type", wireType)
			}
			m.Type = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Type |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowActivity
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.GameidList = append(m.GameidList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowActivity
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthActivity
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowActivity
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.GameidList = append(m.GameidList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameidList", wireType)
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FromIndex", wireType)
			}
			m.FromIndex = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FromIndex |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Count", wireType)
			}
			m.Count = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Count |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipActivity(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivity
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("status")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("type")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetOfferEventRsp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivity
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetOfferEventRsp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetOfferEventRsp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field EventList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthActivity
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.EventList = append(m.EventList, &OfferEventElem{})
			if err := m.EventList[len(m.EventList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipActivity(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivity
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GameRankElem) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivity
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GameRankElem: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GameRankElem: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameId", wireType)
			}
			m.GameId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GameId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Rank", wireType)
			}
			m.Rank = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Rank |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipActivity(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivity
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("game_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("rank")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AddGameRankReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivity
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AddGameRankReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AddGameRankReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Gamerank", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthActivity
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Gamerank == nil {
				m.Gamerank = &GameRankElem{}
			}
			if err := m.Gamerank.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipActivity(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivity
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("gamerank")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ModifyGameRankReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivity
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ModifyGameRankReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ModifyGameRankReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Gamerank", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthActivity
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Gamerank == nil {
				m.Gamerank = &GameRankElem{}
			}
			if err := m.Gamerank.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipActivity(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivity
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("gamerank")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DelGameRankReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivity
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: DelGameRankReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: DelGameRankReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowActivity
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.GameidList = append(m.GameidList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowActivity
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthActivity
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowActivity
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.GameidList = append(m.GameidList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameidList", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipActivity(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivity
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetGameRankReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivity
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetGameRankReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetGameRankReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FromIndex", wireType)
			}
			m.FromIndex = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FromIndex |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Count", wireType)
			}
			m.Count = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Count |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipActivity(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivity
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetGameRankRsp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivity
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetGameRankRsp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetGameRankRsp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GamerankList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthActivity
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.GamerankList = append(m.GamerankList, &GameRankElem{})
			if err := m.GamerankList[len(m.GamerankList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipActivity(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivity
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetGameOfferReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivity
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetGameOfferReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetGameOfferReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameId", wireType)
			}
			m.GameId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GameId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipActivity(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivity
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("game_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GameOfferInfo) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivity
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GameOfferInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GameOfferInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameId", wireType)
			}
			m.GameId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GameId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ActivityName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthActivity
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ActivityName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ActivityDesc", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthActivity
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ActivityDesc = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OfferDesc", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthActivity
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.OfferDesc = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OfferNotes", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthActivity
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.OfferNotes = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OfferCategoryid", wireType)
			}
			m.OfferCategoryid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OfferCategoryid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipActivity(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivity
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("game_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("activity_name")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetGameOfferRsp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivity
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetGameOfferRsp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetGameOfferRsp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameOffer", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthActivity
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.GameOffer = append(m.GameOffer, &GameOfferInfo{})
			if err := m.GameOffer[len(m.GameOffer)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipActivity(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivity
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *FirstVoucherAccountPassword) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivity
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: FirstVoucherAccountPassword: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: FirstVoucherAccountPassword: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Account", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthActivity
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Account = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Password", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthActivity
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Password = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipActivity(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivity
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("account")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("password")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *FirstVoucherProduct) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivity
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: FirstVoucherProduct: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: FirstVoucherProduct: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ProductId", wireType)
			}
			m.ProductId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ProductId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Price", wireType)
			}
			m.Price = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Price |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Worth", wireType)
			}
			m.Worth = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Worth |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameId", wireType)
			}
			m.GameId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GameId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ExpireTime", wireType)
			}
			m.ExpireTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ExpireTime |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000010)
		default:
			iNdEx = preIndex
			skippy, err := skipActivity(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivity
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("product_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("price")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("worth")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("game_id")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("expire_time")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetFirstVoucherProductsReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivity
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetFirstVoucherProductsReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetFirstVoucherProductsReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Index", wireType)
			}
			m.Index = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Index |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Limit", wireType)
			}
			m.Limit = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Limit |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipActivity(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivity
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("index")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("limit")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetFirstVoucherProductsResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivity
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetFirstVoucherProductsResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetFirstVoucherProductsResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ProductList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthActivity
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ProductList = append(m.ProductList, &FirstVoucherProduct{})
			if err := m.ProductList[len(m.ProductList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipActivity(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivity
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AddFirstVoucherProductReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivity
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AddFirstVoucherProductReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AddFirstVoucherProductReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Price", wireType)
			}
			m.Price = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Price |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Worth", wireType)
			}
			m.Worth = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Worth |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameId", wireType)
			}
			m.GameId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GameId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ExpireTime", wireType)
			}
			m.ExpireTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ExpireTime |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		default:
			iNdEx = preIndex
			skippy, err := skipActivity(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivity
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("price")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("worth")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("game_id")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("expire_time")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AddFirstVoucherProductResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivity
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AddFirstVoucherProductResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AddFirstVoucherProductResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ProductId", wireType)
			}
			m.ProductId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ProductId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipActivity(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivity
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("product_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ModifyFirstVoucherProductReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivity
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ModifyFirstVoucherProductReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ModifyFirstVoucherProductReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ProductId", wireType)
			}
			m.ProductId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ProductId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Invalid", wireType)
			}
			m.Invalid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Invalid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Price", wireType)
			}
			m.Price = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Price |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Worth", wireType)
			}
			m.Worth = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Worth |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ExpireTime", wireType)
			}
			m.ExpireTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ExpireTime |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipActivity(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivity
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("product_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetGameFirstVoucherProductReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivity
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetGameFirstVoucherProductReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetGameFirstVoucherProductReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameId", wireType)
			}
			m.GameId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GameId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipActivity(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivity
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("game_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetGameFirstVoucherProductResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivity
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetGameFirstVoucherProductResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetGameFirstVoucherProductResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Product", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthActivity
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Product == nil {
				m.Product = &FirstVoucherProduct{}
			}
			if err := m.Product.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipActivity(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivity
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("product")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DeleteFirstVoucherProductReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivity
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: DeleteFirstVoucherProductReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: DeleteFirstVoucherProductReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ProductId", wireType)
			}
			m.ProductId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ProductId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipActivity(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivity
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("product_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *FirstVoucherItem) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivity
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: FirstVoucherItem: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: FirstVoucherItem: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemHash", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthActivity
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ItemHash = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemBinary", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthActivity
			}
			postIndex := iNdEx + byteLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ItemBinary = append(m.ItemBinary[:0], dAtA[iNdEx:postIndex]...)
			if m.ItemBinary == nil {
				m.ItemBinary = []byte{}
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipActivity(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivity
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("item_hash")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("item_binary")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AddFirstVoucherItemReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivity
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AddFirstVoucherItemReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AddFirstVoucherItemReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthActivity
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ItemList = append(m.ItemList, &FirstVoucherItem{})
			if err := m.ItemList[len(m.ItemList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ProductId", wireType)
			}
			m.ProductId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ProductId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipActivity(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivity
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("product_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AddFirstVoucherItemResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivity
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AddFirstVoucherItemResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AddFirstVoucherItemResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SuccessProductItemList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthActivity
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.SuccessProductItemList = append(m.SuccessProductItemList, &FirstVoucherItem{})
			if err := m.SuccessProductItemList[len(m.SuccessProductItemList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FailedProductItemList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthActivity
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.FailedProductItemList = append(m.FailedProductItemList, &FirstVoucherItem{})
			if err := m.FailedProductItemList[len(m.FailedProductItemList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipActivity(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivity
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetFirstVoucherItemReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivity
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetFirstVoucherItemReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetFirstVoucherItemReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ProductId", wireType)
			}
			m.ProductId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ProductId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Status", wireType)
			}
			m.Status = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Status |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipActivity(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivity
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetFirstVoucherItemResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivity
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetFirstVoucherItemResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetFirstVoucherItemResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthActivity
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ItemList = append(m.ItemList, &FirstVoucherItem{})
			if err := m.ItemList[len(m.ItemList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipActivity(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivity
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DeleteFirstVoucherItemReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivity
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: DeleteFirstVoucherItemReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: DeleteFirstVoucherItemReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ProductId", wireType)
			}
			m.ProductId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ProductId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemHashList", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthActivity
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ItemHashList = append(m.ItemHashList, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipActivity(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivity
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("product_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *FirstVoucherUserItem) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivity
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: FirstVoucherUserItem: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: FirstVoucherUserItem: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Timestamp", wireType)
			}
			m.Timestamp = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Timestamp |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ProductSnapshot", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthActivity
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.ProductSnapshot == nil {
				m.ProductSnapshot = &FirstVoucherProduct{}
			}
			if err := m.ProductSnapshot.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemSnapshot", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthActivity
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.ItemSnapshot == nil {
				m.ItemSnapshot = &FirstVoucherItem{}
			}
			if err := m.ItemSnapshot.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OrderId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthActivity
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.OrderId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000010)
		default:
			iNdEx = preIndex
			skippy, err := skipActivity(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivity
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("timestamp")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("product_snapshot")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("item_snapshot")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("order_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *PurchaseFirstVoucherItemReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivity
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: PurchaseFirstVoucherItemReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: PurchaseFirstVoucherItemReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ProductId", wireType)
			}
			m.ProductId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ProductId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipActivity(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivity
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("product_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *PurchaseFirstVoucherItemResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivity
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: PurchaseFirstVoucherItemResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: PurchaseFirstVoucherItemResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FirstVoucherItem", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthActivity
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.FirstVoucherItem == nil {
				m.FirstVoucherItem = &FirstVoucherUserItem{}
			}
			if err := m.FirstVoucherItem.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipActivity(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivity
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("first_voucher_item")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetFirstVoucherUserItemReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivity
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetFirstVoucherUserItemReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetFirstVoucherUserItemReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipActivity(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivity
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetFirstVoucherUserItemResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivity
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetFirstVoucherUserItemResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetFirstVoucherUserItemResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UserItemList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthActivity
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.UserItemList = append(m.UserItemList, &FirstVoucherUserItem{})
			if err := m.UserItemList[len(m.UserItemList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipActivity(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivity
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ActBannerInfo) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivity
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ActBannerInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ActBannerInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Id", wireType)
			}
			m.Id = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Id |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ActionUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthActivity
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ActionUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Title", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthActivity
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Title = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SubTitle", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthActivity
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.SubTitle = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IconUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthActivity
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.IconUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CoverUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthActivity
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.CoverUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AndroidVersion", wireType)
			}
			m.AndroidVersion = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.AndroidVersion |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 8:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IosVersion", wireType)
			}
			m.IosVersion = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.IosVersion |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 9:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Rank", wireType)
			}
			m.Rank = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Rank |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipActivity(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivity
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("action_url")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UpdateActBannerReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivity
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UpdateActBannerReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UpdateActBannerReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Banner", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthActivity
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Banner == nil {
				m.Banner = &ActBannerInfo{}
			}
			if err := m.Banner.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipActivity(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivity
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("banner")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetActBannersResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivity
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetActBannersResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetActBannersResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BannerList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthActivity
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.BannerList = append(m.BannerList, &ActBannerInfo{})
			if err := m.BannerList[len(m.BannerList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipActivity(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivity
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DelActBannerReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivity
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: DelActBannerReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: DelActBannerReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Id", wireType)
			}
			m.Id = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Id |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipActivity(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivity
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetActBannerOpTimeResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivity
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetActBannerOpTimeResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetActBannerOpTimeResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OpTime", wireType)
			}
			m.OpTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OpTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipActivity(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivity
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("op_time")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ActSplashScreenInfo) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivity
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ActSplashScreenInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ActSplashScreenInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Id", wireType)
			}
			m.Id = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Id |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ScreenUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthActivity
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ScreenUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ActionUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthActivity
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ActionUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field StartTime", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthActivity
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.StartTime = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field EndTime", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthActivity
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.EndTime = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000010)
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ContinueTime", wireType)
			}
			m.ContinueTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ContinueTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000020)
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ClientType", wireType)
			}
			m.ClientType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ClientType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 8:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AppId", wireType)
			}
			m.AppId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.AppId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 9:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MarketId", wireType)
			}
			m.MarketId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MarketId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipActivity(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivity
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("screen_url")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("action_url")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("start_time")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("end_time")
	}
	if hasFields[0]&uint64(0x00000020) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("continue_time")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UpdateActSplashScreenReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivity
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UpdateActSplashScreenReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UpdateActSplashScreenReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SplashScreen", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthActivity
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.SplashScreen == nil {
				m.SplashScreen = &ActSplashScreenInfo{}
			}
			if err := m.SplashScreen.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipActivity(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivity
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("splash_screen")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetActSplashScreenResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivity
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetActSplashScreenResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetActSplashScreenResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SplashScreen", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthActivity
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.SplashScreen == nil {
				m.SplashScreen = &ActSplashScreenInfo{}
			}
			if err := m.SplashScreen.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SplashScreenIos", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthActivity
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.SplashScreenIos == nil {
				m.SplashScreenIos = &ActSplashScreenInfo{}
			}
			if err := m.SplashScreenIos.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SplashScreenList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthActivity
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.SplashScreenList = append(m.SplashScreenList, &ActSplashScreenInfo{})
			if err := m.SplashScreenList[len(m.SplashScreenList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipActivity(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivity
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DelActSplashScreenReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivity
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: DelActSplashScreenReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: DelActSplashScreenReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Id", wireType)
			}
			m.Id = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Id |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipActivity(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivity
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ActSplashScreenOpTime) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivity
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ActSplashScreenOpTime: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ActSplashScreenOpTime: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AppId", wireType)
			}
			m.AppId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.AppId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MarketId", wireType)
			}
			m.MarketId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MarketId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ClientType", wireType)
			}
			m.ClientType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ClientType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OpTime", wireType)
			}
			m.OpTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OpTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipActivity(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivity
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetActSplashScreenOpTimeResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivity
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetActSplashScreenOpTimeResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetActSplashScreenOpTimeResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OpTime", wireType)
			}
			m.OpTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OpTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OpTimeIos", wireType)
			}
			m.OpTimeIos = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OpTimeIos |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OptimeList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthActivity
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.OptimeList = append(m.OptimeList, &ActSplashScreenOpTime{})
			if err := m.OptimeList[len(m.OptimeList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipActivity(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivity
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetGamePreorderResultReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivity
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetGamePreorderResultReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetGamePreorderResultReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameId", wireType)
			}
			m.GameId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GameId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipActivity(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivity
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("game_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GamePreorderResult) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivity
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GamePreorderResult: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GamePreorderResult: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ObjId", wireType)
			}
			m.ObjId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ObjId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Count", wireType)
			}
			m.Count = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Count |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipActivity(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivity
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("obj_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("count")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetGamePreorderResultResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivity
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetGamePreorderResultResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetGamePreorderResultResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ResultList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthActivity
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ResultList = append(m.ResultList, &GamePreorderResult{})
			if err := m.ResultList[len(m.ResultList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipActivity(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivity
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetGamePreorderUserSpptReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivity
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetGamePreorderUserSpptReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetGamePreorderUserSpptReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameId", wireType)
			}
			m.GameId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GameId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipActivity(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivity
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("game_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetGamePreorderUserSpptResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivity
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetGamePreorderUserSpptResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetGamePreorderUserSpptResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ObjId", wireType)
			}
			m.ObjId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ObjId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Count", wireType)
			}
			m.Count = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Count |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipActivity(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivity
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("obj_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("count")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SetGamePreorderUserSpptReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivity
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SetGamePreorderUserSpptReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SetGamePreorderUserSpptReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameId", wireType)
			}
			m.GameId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GameId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ObjId", wireType)
			}
			m.ObjId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ObjId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Count", wireType)
			}
			m.Count = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Count |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		default:
			iNdEx = preIndex
			skippy, err := skipActivity(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivity
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("game_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("obj_id")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("count")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *InviterInfo) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivity
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: InviterInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: InviterInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field InviteNum", wireType)
			}
			m.InviteNum = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.InviteNum |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipActivity(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivity
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("invite_num")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetQQCarActivityRankResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivity
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetQQCarActivityRankResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetQQCarActivityRankResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field InviteInfos", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthActivity
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.InviteInfos = append(m.InviteInfos, &InviterInfo{})
			if err := m.InviteInfos[len(m.InviteInfos)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipActivity(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivity
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *NotifyInviteRecordReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivity
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: NotifyInviteRecordReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: NotifyInviteRecordReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field InviterUid", wireType)
			}
			m.InviterUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.InviterUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipActivity(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivity
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("inviter_uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetPrizeInfoReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivity
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetPrizeInfoReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetPrizeInfoReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipActivity(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivity
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetPrizeInfoResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivity
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetPrizeInfoResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetPrizeInfoResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DayRank", wireType)
			}
			m.DayRank = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.DayRank |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field InviteNum", wireType)
			}
			m.InviteNum = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.InviteNum |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TotalRank", wireType)
			}
			m.TotalRank = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TotalRank |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FristRegister", wireType)
			}
			m.FristRegister = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FristRegister |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DayInvite", wireType)
			}
			m.DayInvite = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.DayInvite |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000010)
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TotalInvite", wireType)
			}
			m.TotalInvite = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TotalInvite |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000020)
		default:
			iNdEx = preIndex
			skippy, err := skipActivity(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivity
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("day_rank")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("invite_num")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("total_rank")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("frist_register")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("day_invite")
	}
	if hasFields[0]&uint64(0x00000020) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("total_invite")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SetUidPairQQReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivity
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SetUidPairQQReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SetUidPairQQReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Qq", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthActivity
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Qq = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipActivity(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivity
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("qq")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *TakePrizeReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivity
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: TakePrizeReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: TakePrizeReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PrizeType", wireType)
			}
			m.PrizeType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PrizeType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field QqNumber", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthActivity
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.QqNumber = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipActivity(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivity
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("prize_type")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("qq_number")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *TakePrizeResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivity
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: TakePrizeResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: TakePrizeResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipActivity(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivity
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func skipActivity(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowActivity
			}
			if iNdEx >= l {
				return 0, io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
			return iNdEx, nil
		case 1:
			iNdEx += 8
			return iNdEx, nil
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowActivity
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			iNdEx += length
			if length < 0 {
				return 0, ErrInvalidLengthActivity
			}
			return iNdEx, nil
		case 3:
			for {
				var innerWire uint64
				var start = iNdEx
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return 0, ErrIntOverflowActivity
					}
					if iNdEx >= l {
						return 0, io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					innerWire |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				innerWireType := int(innerWire & 0x7)
				if innerWireType == 4 {
					break
				}
				next, err := skipActivity(dAtA[start:])
				if err != nil {
					return 0, err
				}
				iNdEx = start + next
			}
			return iNdEx, nil
		case 4:
			return iNdEx, nil
		case 5:
			iNdEx += 4
			return iNdEx, nil
		default:
			return 0, fmt2.Errorf("proto: illegal wireType %d", wireType)
		}
	}
	panic("unreachable")
}

var (
	ErrInvalidLengthActivity = fmt2.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowActivity   = fmt2.Errorf("proto: integer overflow")
)

func init() { proto.RegisterFile("src/activitysvr/activity.proto", fileDescriptorActivity) }

var fileDescriptorActivity = []byte{
	// 4138 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xcc, 0x7a, 0x4d, 0x6c, 0x1b, 0x49,
	0x76, 0xbf, 0x9b, 0x94, 0x2c, 0xf2, 0x91, 0x92, 0xe8, 0xb2, 0x25, 0xd1, 0xb4, 0x2c, 0xb5, 0xdb,
	0xf6, 0x58, 0x33, 0x03, 0x5a, 0x63, 0xcf, 0x62, 0x67, 0x97, 0xcb, 0xe1, 0x2c, 0x65, 0xc9, 0x1a,
	0xfe, 0x47, 0xa6, 0x64, 0x8a, 0xf6, 0xfe, 0xbd, 0x13, 0x84, 0x68, 0xb1, 0x4b, 0x52, 0x5b, 0x64,
	0x77, 0xab, 0xab, 0xa9, 0xb1, 0x36, 0x40, 0x92, 0x45, 0x0e, 0x09, 0x16, 0x01, 0x36, 0xd9, 0x4b,
	0x12, 0x04, 0x1b, 0x6c, 0x80, 0x09, 0x72, 0xc9, 0x21, 0xc8, 0x25, 0xb9, 0x24, 0xc8, 0x07, 0x02,
	0x6c, 0x3e, 0x36, 0xd9, 0x7c, 0x5d, 0x83, 0x64, 0x72, 0x99, 0x63, 0x3e, 0x76, 0x81, 0x9c, 0x16,
	0x41, 0x55, 0x75, 0x37, 0xab, 0xbf, 0x48, 0x7a, 0xd6, 0x87, 0xbd, 0x49, 0xaf, 0x5e, 0xbf, 0xf7,
	0xea, 0xf7, 0x3e, 0xea, 0xf1, 0x55, 0xc1, 0x0a, 0xb1, 0xbb, 0xeb, 0x6a, 0xd7, 0xd1, 0xcf, 0x74,
	0xe7, 0x9c, 0x9c, 0xd9, 0xfe, 0xdf, 0x77, 0x2d, 0xdb, 0x74, 0x4c, 0x94, 0xa9, 0xbb, 0xff, 0x97,
	0x6e, 0x75, 0xcd, 0x7e, 0xdf, 0x34, 0xd6, 0x9d, 0xde, 0x99, 0xa5, 0x77, 0x4f, 0x7a, 0x78, 0x9d,
	0x9c, 0x1c, 0x0c, 0xf4, 0x9e, 0xa3, 0x1b, 0xce, 0xb9, 0x85, 0x39, 0xbf, 0xf2, 0x00, 0x2e, 0xed,
	0x1e, 0x1e, 0x62, 0xfb, 0x81, 0xea, 0xe0, 0x23, 0xd3, 0x3e, 0x6f, 0x18, 0x87, 0x26, 0xba, 0x02,
	0x29, 0x5d, 0x2b, 0x4a, 0x72, 0x6a, 0x6d, 0x76, 0x63, 0xea, 0xbb, 0xff, 0xba, 0x7a, 0xa1, 0x95,
	0xd2, 0x35, 0x54, 0x84, 0x29, 0x43, 0xed, 0xe3, 0x62, 0x4a, 0x4e, 0xad, 0x65, 0x5d, 0x3a, 0xa3,
	0x28, 0x9b, 0x21, 0x21, 0x3b, 0x3a, 0x71, 0xd0, 0x3a, 0x4c, 0xf5, 0x74, 0xe2, 0x14, 0x25, 0x39,
	0xbd, 0x96, 0xbb, 0x7f, 0xed, 0xae, 0x67, 0xd8, 0xdd, 0x88, 0xbe, 0x16, 0x63, 0x54, 0x7e, 0x2d,
	0x05, 0x97, 0x3c, 0x26, 0xc6, 0xc3, 0x6c, 0xb9, 0x0e, 0x33, 0x47, 0x6a, 0x1f, 0x77, 0x42, 0x06,
	0x5d, 0xa4, 0xc4, 0x86, 0x86, 0x6e, 0x43, 0xce, 0x43, 0x80, 0xb2, 0xa4, 0x64, 0xc9, 0x67, 0x01,
	0x6f, 0x81, 0xb3, 0x75, 0x5d, 0x8d, 0x94, 0x2d, 0x2d, 0x48, 0x02, 0x6f, 0xa1, 0xa1, 0xa1, 0x65,
	0xb8, 0xa8, 0xf6, 0xcd, 0x81, 0xe1, 0x14, 0xa7, 0x04, 0x41, 0x2e, 0x0d, 0xdd, 0x04, 0x30, 0xa9,
	0x5d, 0x1d, 0x0d, 0x93, 0x6e, 0x71, 0x5a, 0x96, 0x7c, 0x18, 0xb2, 0x8c, 0xbe, 0x89, 0x49, 0x97,
	0x6a, 0xe2, 0x4c, 0x86, 0xe9, 0x60, 0x52, 0xbc, 0x28, 0x70, 0xf1, 0xaf, 0x9b, 0x94, 0x8e, 0xde,
	0x84, 0x39, 0xce, 0xa6, 0x5a, 0x16, 0x36, 0x34, 0xfd, 0x45, 0x71, 0x46, 0xe0, 0x9c, 0x65, 0x6b,
	0x75, 0x77, 0x49, 0xf9, 0xc3, 0x14, 0xe4, 0x3d, 0x64, 0x18, 0x28, 0xa1, 0x5d, 0x8b, 0xc0, 0x88,
	0xbb, 0x1e, 0x7a, 0x4c, 0x0a, 0x7a, 0x0c, 0xbd, 0x06, 0x39, 0xba, 0x09, 0x5b, 0xb7, 0x1c, 0xdd,
	0x34, 0x8a, 0x69, 0x81, 0x41, 0x5c, 0xa0, 0x80, 0x1c, 0xa8, 0x86, 0x81, 0x6d, 0x06, 0x88, 0xc7,
	0xe2, 0xd2, 0x28, 0x20, 0x07, 0xf8, 0x48, 0x37, 0x3a, 0x8e, 0xde, 0xc7, 0x0c, 0x90, 0xb4, 0x07,
	0x08, 0xa3, 0xb7, 0xf5, 0x3e, 0x46, 0xab, 0x90, 0xc1, 0x86, 0xc6, 0x59, 0x2e, 0x0a, 0x2c, 0x33,
	0xd8, 0xd0, 0x18, 0xc3, 0x32, 0x5c, 0x24, 0x8e, 0xea, 0x0c, 0x08, 0x83, 0xc0, 0x07, 0x9d, 0xd3,
	0x50, 0xd5, 0xc5, 0x93, 0x74, 0x58, 0x34, 0x65, 0xc2, 0xd1, 0x14, 0x89, 0x18, 0x17, 0x66, 0x42,
	0x83, 0x50, 0xd9, 0x86, 0x4b, 0x0f, 0x6c, 0xac, 0x3a, 0xd8, 0x63, 0x6b, 0xe1, 0x53, 0x74, 0x1f,
	0x32, 0x1e, 0x48, 0x0c, 0xba, 0xdc, 0xfd, 0xc5, 0xa8, 0x3c, 0x26, 0xca, 0xe7, 0x53, 0xbe, 0x04,
	0x28, 0x2c, 0x88, 0x58, 0x13, 0xfa, 0x81, 0x5a, 0xf1, 0xc4, 0xd2, 0x5e, 0x8d, 0x15, 0x61, 0x41,
	0x93, 0x5b, 0xf1, 0x21, 0x5c, 0xda, 0xc4, 0x3d, 0x1c, 0xb4, 0x62, 0xc2, 0x48, 0x5a, 0x85, 0x1c,
	0x4d, 0x38, 0x5d, 0xe3, 0x5e, 0x48, 0xc9, 0xe9, 0xb5, 0xd9, 0x16, 0x70, 0x12, 0x03, 0xfa, 0x0f,
	0x24, 0x40, 0xdb, 0xd8, 0xf1, 0x44, 0x53, 0x1a, 0x15, 0x3f, 0xf4, 0xad, 0x14, 0xe3, 0xdb, 0x9b,
	0x00, 0x87, 0xb6, 0xd9, 0xef, 0xe8, 0x86, 0x86, 0x5f, 0x04, 0x72, 0x37, 0x4b, 0xe9, 0x0d, 0x4a,
	0x46, 0x25, 0x98, 0xee, 0xb2, 0x94, 0x4c, 0x0b, 0xeb, 0x9c, 0x84, 0x56, 0x60, 0xc6, 0xc6, 0x67,
	0xd8, 0x26, 0x98, 0xc5, 0x67, 0xc6, 0x0b, 0x2d, 0x97, 0x28, 0x16, 0x8f, 0x69, 0x51, 0x3f, 0x2f,
	0x1e, 0x4a, 0x0b, 0x2e, 0x47, 0x6c, 0x26, 0x16, 0xfa, 0x12, 0xcc, 0xfa, 0x98, 0x08, 0x25, 0x2c,
	0xc9, 0x3d, 0x79, 0x55, 0x10, 0xa0, 0xfc, 0x76, 0x1a, 0xe6, 0x58, 0x2c, 0x6e, 0x9d, 0x61, 0xc3,
	0xd9, 0xea, 0xe1, 0xbe, 0x5f, 0x4e, 0xa5, 0x84, 0x72, 0xfa, 0x59, 0x93, 0xb3, 0x04, 0xd3, 0xbc,
	0xc8, 0x88, 0xb9, 0xc9, 0x49, 0xaf, 0x28, 0x35, 0x57, 0x21, 0x63, 0x98, 0x1f, 0x71, 0x86, 0x19,
	0x91, 0xc1, 0x30, 0x3f, 0xf2, 0x72, 0xd7, 0xad, 0x0f, 0x99, 0x98, 0xfa, 0x70, 0x9b, 0x6e, 0xc4,
	0x51, 0xf5, 0x5e, 0xe7, 0xd8, 0xc6, 0x87, 0xc5, 0xac, 0x58, 0x0b, 0xf9, 0xc2, 0xfb, 0x36, 0x3e,
	0xa4, 0x48, 0xd0, 0x13, 0xa9, 0x08, 0x02, 0x42, 0x8c, 0x42, 0x57, 0x6c, 0xd5, 0x38, 0x29, 0xe6,
	0xc4, 0x15, 0x4a, 0x11, 0x3d, 0x9b, 0x8f, 0x7a, 0x56, 0x88, 0xbb, 0xd9, 0x68, 0xdc, 0x29, 0x7b,
	0x70, 0x99, 0x27, 0xf3, 0xd0, 0x51, 0x34, 0x58, 0xbf, 0xe8, 0x95, 0x6e, 0x4c, 0x29, 0x6e, 0x52,
	0x16, 0x43, 0x07, 0x97, 0xef, 0x56, 0xb7, 0xce, 0xb0, 0xff, 0x95, 0x37, 0x63, 0x24, 0x12, 0x2b,
	0xfe, 0x20, 0xa5, 0xea, 0x1f, 0x99, 0x9a, 0x7e, 0x78, 0xfe, 0xca, 0xd4, 0xaf, 0x41, 0x61, 0x13,
	0xf7, 0x82, 0xe2, 0xe2, 0x75, 0xff, 0x9e, 0x04, 0x85, 0x6d, 0xec, 0x04, 0x59, 0xc5, 0x2c, 0x4d,
	0x45, 0xb2, 0xd4, 0x73, 0x4f, 0x4a, 0x58, 0xe3, 0xee, 0x09, 0x55, 0x85, 0x74, 0xb8, 0x2a, 0x84,
	0x12, 0x7c, 0x6a, 0x4c, 0x82, 0x4f, 0x47, 0x12, 0x5c, 0xf9, 0x20, 0x6c, 0x2d, 0xb1, 0xd0, 0x3b,
	0x00, 0x0c, 0x21, 0x31, 0x37, 0x93, 0x61, 0xca, 0x32, 0x5e, 0xf7, 0x30, 0xc8, 0x6f, 0xab, 0x7d,
	0xdc, 0x52, 0x8d, 0x13, 0x96, 0x97, 0x63, 0x5a, 0x0b, 0x2f, 0xf8, 0x02, 0xfb, 0xa6, 0x14, 0x65,
	0x13, 0xe6, 0xea, 0x9a, 0xe6, 0xc9, 0x72, 0x8b, 0x39, 0xfd, 0x8a, 0xf1, 0x47, 0x8a, 0xb9, 0xa8,
	0xb4, 0xe5, 0xf3, 0xd1, 0x53, 0x81, 0x87, 0xc1, 0x8f, 0x2b, 0xe8, 0x1e, 0xcc, 0x6d, 0xe2, 0x9e,
	0x28, 0x25, 0xe4, 0x18, 0x29, 0x52, 0xae, 0x1f, 0xc3, 0xdc, 0x36, 0x76, 0xc4, 0x4f, 0x82, 0xae,
	0x92, 0xc6, 0xb8, 0x2a, 0x15, 0x75, 0xd5, 0xa3, 0xa0, 0x48, 0x5e, 0x47, 0x3d, 0x1b, 0x13, 0xea,
	0x68, 0x60, 0x43, 0x79, 0x8f, 0x99, 0x59, 0xf8, 0x16, 0xcc, 0xbb, 0xe2, 0x98, 0x43, 0xa9, 0x89,
	0xa3, 0xfd, 0xa5, 0x7c, 0x3d, 0x05, 0xb3, 0x3e, 0xff, 0x24, 0xbd, 0xe3, 0xeb, 0x42, 0x9d, 0x8f,
	0x74, 0xb6, 0x7e, 0x55, 0x6f, 0xd2, 0x92, 0x2c, 0xb2, 0xb2, 0xee, 0x4f, 0x2c, 0xca, 0x3e, 0x2b,
	0x6b, 0x00, 0x83, 0x5d, 0xe2, 0xd4, 0x44, 0x5d, 0xe2, 0x74, 0x42, 0x97, 0xb8, 0x0e, 0x05, 0xce,
	0xe6, 0xf5, 0xa8, 0xba, 0xc6, 0x0a, 0xb5, 0xb7, 0x93, 0x79, 0x53, 0xec, 0xa5, 0x75, 0x4d, 0x69,
	0x84, 0x50, 0x23, 0x16, 0xfa, 0x3c, 0x30, 0xc7, 0x77, 0x18, 0xab, 0xeb, 0x82, 0xa5, 0xa0, 0x0b,
	0x86, 0xbd, 0x53, 0xf6, 0xc8, 0xfb, 0x57, 0xe9, 0xc0, 0xb5, 0x87, 0xba, 0x4d, 0x9c, 0xa7, 0xe6,
	0xa0, 0x7b, 0x8c, 0xed, 0x7a, 0x97, 0xb9, 0x79, 0x4f, 0x25, 0xe4, 0x23, 0xd3, 0xd6, 0xe8, 0xd1,
	0xab, 0x72, 0x12, 0xc3, 0xd6, 0xb3, 0xde, 0x23, 0x22, 0x19, 0x32, 0x96, 0xcb, 0x1b, 0xc0, 0xd5,
	0xa7, 0x2a, 0x7f, 0x24, 0xc1, 0x65, 0x51, 0xc3, 0x9e, 0x6d, 0x6a, 0x83, 0x2e, 0x2b, 0x1a, 0x16,
	0xff, 0x33, 0xec, 0xb8, 0xac, 0x4b, 0x6f, 0x68, 0x34, 0x12, 0x2d, 0x5b, 0xef, 0x06, 0xab, 0x12,
	0x27, 0xd1, 0xb5, 0x8f, 0x4c, 0xdb, 0x39, 0x0e, 0xb4, 0xf9, 0x9c, 0x24, 0x86, 0xc4, 0x54, 0xfc,
	0xcf, 0x09, 0xfc, 0xc2, 0xd2, 0x6d, 0xec, 0x9d, 0x9b, 0xa9, 0xb5, 0x29, 0xcf, 0x2f, 0x7c, 0x81,
	0x1e, 0x7b, 0x4a, 0x1b, 0x4a, 0xdb, 0xd8, 0x89, 0x31, 0x9e, 0xd0, 0x38, 0x2d, 0xc1, 0xb4, 0x97,
	0x45, 0x82, 0x7e, 0xdd, 0xcb, 0xa0, 0x9e, 0xde, 0xd7, 0x9d, 0xa0, 0xdd, 0x8c, 0x44, 0x11, 0x4f,
	0x94, 0x4a, 0x2c, 0xf4, 0x65, 0xc8, 0x7b, 0xb8, 0x08, 0xd9, 0x74, 0x7d, 0xe8, 0xca, 0x98, 0x2f,
	0x5b, 0x39, 0xf7, 0x13, 0x96, 0x53, 0xbf, 0x21, 0xc1, 0xd5, 0xba, 0xa6, 0xc5, 0xf1, 0x71, 0xb3,
	0x39, 0xa4, 0xd2, 0x08, 0x48, 0x53, 0x23, 0x21, 0x4d, 0x8f, 0x87, 0x74, 0x2a, 0x01, 0xd2, 0x3a,
	0x94, 0x92, 0x4c, 0x23, 0xd6, 0x44, 0x31, 0xa1, 0xfc, 0xb9, 0x04, 0xcb, 0xbc, 0xa2, 0x26, 0xec,
	0x70, 0xa2, 0xc8, 0x5a, 0x81, 0x19, 0xdd, 0x38, 0x53, 0x7b, 0xa1, 0x5f, 0x93, 0x1e, 0x71, 0x08,
	0x53, 0xa0, 0x1f, 0x0d, 0xc1, 0x24, 0x1e, 0x75, 0x2e, 0x4c, 0x91, 0xd0, 0x92, 0x62, 0x71, 0xa8,
	0xc1, 0x75, 0x37, 0x83, 0x13, 0x36, 0x31, 0xa6, 0x0a, 0x3e, 0x83, 0x95, 0x51, 0xdf, 0xb3, 0xf3,
	0x73, 0xc6, 0xdd, 0xad, 0x7b, 0xc2, 0x8c, 0x09, 0x21, 0x8f, 0x5b, 0x79, 0x00, 0xcb, 0xfc, 0x07,
	0xc4, 0x8f, 0x01, 0xaf, 0xf2, 0x53, 0x50, 0x10, 0x3f, 0x6f, 0x38, 0xb8, 0x8f, 0x6e, 0x40, 0x56,
	0x77, 0x70, 0xbf, 0x73, 0xac, 0x92, 0xe3, 0x40, 0x35, 0xc9, 0x50, 0xf2, 0xfb, 0x2a, 0x61, 0xe8,
	0x31, 0x96, 0x03, 0xdd, 0x50, 0xed, 0x73, 0x16, 0x86, 0x79, 0x0f, 0x3d, 0xba, 0xb0, 0xc1, 0xe8,
	0xca, 0x19, 0x2c, 0x86, 0xa2, 0x88, 0x2a, 0xa0, 0xc6, 0xbd, 0xe3, 0xea, 0x10, 0x52, 0xa7, 0x14,
	0xbf, 0x6f, 0xf6, 0x05, 0xd3, 0xec, 0xf5, 0x30, 0xc2, 0xae, 0x52, 0xf1, 0xbb, 0xfa, 0x4b, 0x09,
	0x96, 0x62, 0x15, 0x13, 0x0b, 0x3d, 0x81, 0xab, 0x64, 0xd0, 0xed, 0x62, 0x42, 0x3a, 0xbe, 0xa0,
	0x97, 0xb0, 0x64, 0xd1, 0xfd, 0xd8, 0x05, 0xba, 0xe1, 0xd9, 0xb5, 0x0f, 0xc5, 0x43, 0x55, 0xef,
	0x61, 0x2d, 0x46, 0x6a, 0x6a, 0xac, 0xd4, 0x05, 0xfe, 0x6d, 0x48, 0xa8, 0xf2, 0x21, 0x2c, 0x86,
	0x4a, 0x90, 0x87, 0x5f, 0xd8, 0xb9, 0x52, 0x5c, 0xee, 0x0c, 0x1b, 0xc9, 0x54, 0x4c, 0xdb, 0xdd,
	0x82, 0xa5, 0x58, 0xe1, 0x2c, 0x26, 0x3f, 0x9b, 0x77, 0x94, 0x43, 0xb8, 0x1a, 0x8d, 0xc9, 0x24,
	0x9b, 0x63, 0xf3, 0xfd, 0x16, 0xcc, 0xf9, 0xc1, 0x37, 0x44, 0x2f, 0xdb, 0xca, 0x7b, 0xb1, 0xc7,
	0xf4, 0xfc, 0x48, 0x82, 0x2b, 0xa2, 0x8a, 0x27, 0xc4, 0x8d, 0xdd, 0x45, 0x48, 0x0f, 0x42, 0xc2,
	0x29, 0x01, 0x29, 0x90, 0xa5, 0x79, 0x4e, 0x1c, 0xb5, 0x6f, 0xb1, 0xa8, 0xf1, 0x92, 0x7d, 0x48,
	0x46, 0xef, 0x43, 0xc1, 0xb3, 0x8f, 0x18, 0xaa, 0x45, 0x8e, 0x4d, 0x87, 0x95, 0xd0, 0xb1, 0x29,
	0x39, 0xef, 0x7e, 0xb6, 0xef, 0x7e, 0x85, 0xde, 0x83, 0x59, 0xb6, 0x09, 0x5f, 0xcc, 0x14, 0x13,
	0x33, 0x0a, 0x43, 0xb6, 0x3f, 0x5f, 0xc0, 0x2a, 0x64, 0x4c, 0x5b, 0xc3, 0x36, 0xff, 0xa9, 0x2c,
	0x9c, 0xe7, 0x8c, 0xda, 0xd0, 0x94, 0xaf, 0xc2, 0xb5, 0xbd, 0x81, 0xdd, 0x3d, 0x56, 0x49, 0x2c,
	0xd4, 0x49, 0x30, 0x4c, 0x94, 0x3d, 0x3d, 0x58, 0x4e, 0x96, 0x4d, 0x2c, 0xb4, 0x03, 0xe8, 0x90,
	0xd2, 0x3b, 0x67, 0x7c, 0x81, 0x45, 0xba, 0x5b, 0xbc, 0x56, 0xe2, 0xb7, 0xe8, 0xf9, 0xa7, 0x55,
	0x38, 0x0c, 0x49, 0x54, 0x3e, 0x17, 0x39, 0xbc, 0x7d, 0xe6, 0xe4, 0x8d, 0x28, 0xdd, 0xc8, 0xe1,
	0x3c, 0xfc, 0x8a, 0x58, 0x68, 0x13, 0xe6, 0x06, 0xc4, 0xb5, 0x4c, 0x8c, 0xe2, 0x71, 0xe6, 0xe5,
	0x07, 0xee, 0x5f, 0x2c, 0xca, 0xfe, 0x38, 0x05, 0xb3, 0xf5, 0xae, 0xb3, 0xc1, 0x7e, 0x3e, 0x8f,
	0x18, 0xc5, 0xde, 0x04, 0x36, 0x9c, 0x31, 0x8d, 0xce, 0xc0, 0xee, 0x05, 0xda, 0xab, 0x2c, 0xa7,
	0x3f, 0xb1, 0x7b, 0xf4, 0x30, 0x72, 0x74, 0xa7, 0x87, 0x03, 0xbd, 0x2a, 0x27, 0xd1, 0x8a, 0x4b,
	0x06, 0x07, 0x1d, 0xbe, 0x2e, 0xf6, 0xa8, 0x19, 0x32, 0x38, 0x68, 0x33, 0x96, 0x55, 0xc8, 0xe8,
	0x5d, 0x57, 0x83, 0xd8, 0x9f, 0xce, 0x50, 0x2a, 0x95, 0x7f, 0x03, 0xb2, 0x5d, 0xf3, 0x0c, 0xdb,
	0x8c, 0x43, 0x9c, 0x73, 0x66, 0x18, 0x99, 0xb2, 0x94, 0x61, 0x5e, 0x35, 0x34, 0xdb, 0xd4, 0xb5,
	0xce, 0x19, 0xb6, 0x89, 0x6e, 0x1a, 0x81, 0x19, 0xdf, 0x9c, 0xbb, 0xf8, 0x94, 0xaf, 0xb1, 0x22,
	0x6f, 0x12, 0x9f, 0x35, 0x23, 0x0e, 0x73, 0x75, 0x93, 0x78, 0x6c, 0xde, 0x0f, 0xb3, 0x6c, 0x78,
	0x2a, 0xa0, 0x6c, 0x09, 0xf3, 0x31, 0x0e, 0x22, 0x75, 0xe9, 0xba, 0x3f, 0xa4, 0xe0, 0x21, 0xb3,
	0x14, 0x18, 0xe4, 0x0c, 0xc1, 0xf6, 0xe6, 0x16, 0xca, 0x23, 0xb8, 0xc4, 0xe7, 0x42, 0x7c, 0x8d,
	0xb7, 0x5f, 0x5f, 0x80, 0x1c, 0x5f, 0x16, 0xdd, 0x9b, 0x28, 0x0a, 0x38, 0x2f, 0xf3, 0xea, 0x1d,
	0x98, 0xdf, 0xc4, 0xbd, 0x80, 0x49, 0xf1, 0x3f, 0xce, 0xdf, 0x61, 0xd5, 0xd7, 0x67, 0xdc, 0xb5,
	0x68, 0x47, 0xc0, 0x94, 0x5f, 0x87, 0x19, 0xd3, 0xe2, 0x8d, 0x43, 0xe0, 0xd0, 0x37, 0x19, 0x8b,
	0xf2, 0xbd, 0x14, 0x5c, 0xae, 0x77, 0x9d, 0x7d, 0xab, 0xa7, 0x92, 0xe3, 0xfd, 0xae, 0x8d, 0xb1,
	0x31, 0x3a, 0x7a, 0x08, 0xe3, 0x89, 0x46, 0x0f, 0xa7, 0x53, 0xd7, 0x05, 0x43, 0x2c, 0x1d, 0x1f,
	0x62, 0x54, 0x92, 0xa3, 0xda, 0xce, 0xb0, 0xb5, 0x1b, 0x4a, 0xa2, 0xf4, 0xc8, 0x94, 0x29, 0x50,
	0x5a, 0xbc, 0x29, 0xd3, 0xeb, 0x30, 0xdb, 0x35, 0x0d, 0x47, 0x37, 0x06, 0xd8, 0x9b, 0x45, 0x0d,
	0x0d, 0xce, 0x7b, 0x4b, 0x8c, 0xf5, 0x36, 0xe4, 0xba, 0x3d, 0x9d, 0xfe, 0xf8, 0x67, 0x23, 0x09,
	0x31, 0x98, 0x80, 0x2f, 0xb4, 0xcf, 0x2d, 0x8c, 0xae, 0xc1, 0x45, 0xd5, 0xb2, 0x68, 0xc5, 0x11,
	0x63, 0x68, 0x5a, 0xb5, 0xac, 0x86, 0x46, 0xe3, 0xb6, 0xaf, 0xda, 0x27, 0x98, 0x55, 0x24, 0x31,
	0x86, 0x32, 0x9c, 0xdc, 0xd0, 0x94, 0x9f, 0x86, 0xa2, 0x1f, 0x47, 0x22, 0xa8, 0xd4, 0x75, 0x1b,
	0x30, 0x4b, 0x18, 0xa9, 0xc3, 0xc1, 0x8a, 0x36, 0x51, 0x31, 0x9e, 0x68, 0xe5, 0x89, 0x40, 0x51,
	0x7e, 0x20, 0x79, 0x9e, 0x0e, 0x4a, 0x27, 0x56, 0x9c, 0x78, 0xe9, 0x25, 0xc5, 0xa3, 0x06, 0x5c,
	0x0a, 0xc8, 0xe8, 0xe8, 0x26, 0x3f, 0x91, 0xc7, 0xca, 0x99, 0x17, 0xe5, 0x34, 0x4c, 0x82, 0x3e,
	0x00, 0x14, 0x14, 0xe5, 0x4f, 0x7a, 0xc6, 0xca, 0x2a, 0x88, 0xb2, 0x58, 0x22, 0x94, 0x61, 0x81,
	0x27, 0x42, 0x18, 0xd3, 0xf8, 0x74, 0xf8, 0xb6, 0x04, 0x0b, 0x21, 0x66, 0x9e, 0x12, 0x82, 0x7f,
	0xa5, 0x31, 0xfe, 0x4d, 0xc5, 0xf9, 0x37, 0x1c, 0x46, 0xe9, 0x84, 0x30, 0x12, 0xb2, 0x2e, 0x70,
	0x1f, 0xe4, 0x66, 0xdd, 0xef, 0x48, 0xb0, 0x1c, 0xf5, 0x62, 0x52, 0xd6, 0x46, 0xbe, 0x47, 0xb7,
	0x20, 0xe7, 0x2e, 0xfb, 0x0e, 0xf2, 0x0f, 0x47, 0xce, 0x42, 0x3d, 0xf0, 0x65, 0xca, 0xc5, 0x98,
	0x04, 0xe8, 0x57, 0x13, 0xa1, 0x77, 0xd5, 0x03, 0xff, 0x86, 0xc1, 0xfe, 0x45, 0x28, 0xba, 0x3f,
	0x09, 0xf6, 0x6c, 0xcc, 0xce, 0xf3, 0x16, 0x26, 0x83, 0xde, 0x24, 0xbf, 0x26, 0x1e, 0x01, 0x8a,
	0x7e, 0x47, 0xe1, 0x37, 0x0f, 0x9e, 0x87, 0xbf, 0x99, 0x36, 0x0f, 0x9e, 0xf3, 0x5f, 0xe6, 0xde,
	0x8c, 0x28, 0x15, 0x9e, 0x11, 0x7d, 0x15, 0xae, 0x26, 0x58, 0x42, 0x2c, 0xf4, 0x2e, 0xe4, 0x6c,
	0xf6, 0x9f, 0x58, 0x60, 0x97, 0x83, 0x93, 0x8a, 0xd0, 0x67, 0xc0, 0x3f, 0x60, 0xbb, 0xdc, 0x67,
	0xc7, 0xba, 0xc8, 0x44, 0x0f, 0xd9, 0x7d, 0xcb, 0x9a, 0x60, 0x9f, 0xde, 0xa9, 0x9f, 0x0a, 0x9f,
	0xfa, 0x4f, 0xd9, 0xa9, 0x1f, 0x2f, 0x94, 0x58, 0x9f, 0x1d, 0x88, 0x5f, 0x96, 0xa0, 0xb4, 0xff,
	0xaa, 0xad, 0x15, 0xcc, 0x49, 0x8f, 0x30, 0x67, 0x2a, 0x6a, 0xce, 0xff, 0x83, 0x5c, 0xc3, 0x38,
	0xd3, 0x1d, 0xb7, 0xe9, 0x18, 0xd1, 0xcc, 0xe9, 0x8c, 0xad, 0x63, 0x0c, 0xfa, 0xc1, 0x66, 0x8e,
	0xd3, 0x9b, 0x83, 0xbe, 0xd2, 0x66, 0xd1, 0xf6, 0xf8, 0xf1, 0x03, 0xd5, 0xf6, 0x2f, 0x9a, 0xd8,
	0x8c, 0x91, 0x9d, 0xa1, 0x79, 0x57, 0x80, 0x6e, 0x1c, 0x9a, 0xc4, 0xf5, 0xf1, 0xc2, 0xd0, 0xc7,
	0x82, 0x15, 0xad, 0x1c, 0x67, 0xa5, 0x7f, 0x13, 0xe5, 0x29, 0x2c, 0x34, 0x4d, 0x47, 0x3f, 0x3c,
	0xe7, 0x1c, 0x2d, 0xdc, 0x35, 0x6d, 0x6d, 0x54, 0xe3, 0x49, 0x7b, 0x09, 0x2e, 0xac, 0x13, 0xc6,
	0xca, 0xdd, 0x84, 0xfd, 0x44, 0xd7, 0x94, 0xd7, 0xd9, 0xc0, 0x6c, 0xcf, 0xd6, 0xbf, 0xc6, 0x14,
	0x8d, 0xea, 0x00, 0xff, 0x97, 0x8f, 0xce, 0x05, 0x5e, 0x62, 0xd1, 0xc3, 0x4d, 0x53, 0xcf, 0x3b,
	0xfe, 0xbc, 0xd6, 0x1f, 0x17, 0x68, 0x2a, 0xdb, 0xf6, 0x44, 0x98, 0x51, 0x26, 0xc7, 0x74, 0xd4,
	0x1e, 0x97, 0x23, 0x3a, 0x2f, 0xcb, 0xe8, 0x4c, 0xd2, 0x9b, 0x30, 0x77, 0x68, 0xeb, 0xc4, 0xe9,
	0xd8, 0xf8, 0x48, 0x27, 0x0e, 0xbb, 0x93, 0x1d, 0x32, 0xce, 0xb2, 0xb5, 0x96, 0xbb, 0x44, 0x25,
	0x52, 0xbb, 0xb8, 0x0a, 0x76, 0xec, 0xfa, 0x12, 0x35, 0xd5, 0x05, 0x11, 0xdd, 0x81, 0x3c, 0x57,
	0xeb, 0xb2, 0x89, 0xe7, 0x6e, 0x8e, 0xad, 0x70, 0x46, 0xe5, 0x3d, 0x98, 0xdf, 0xc7, 0xce, 0x13,
	0x5d, 0xdb, 0x53, 0x75, 0xfb, 0xf1, 0xe3, 0x51, 0xb8, 0x5f, 0x81, 0xd4, 0xe9, 0x69, 0xa0, 0xa9,
	0x48, 0x9d, 0x9e, 0x2a, 0x06, 0xe4, 0xdb, 0xea, 0x09, 0x66, 0xd8, 0x8d, 0xfd, 0xb9, 0xa0, 0x7f,
	0x0d, 0x77, 0x22, 0x37, 0x0e, 0x59, 0x46, 0x67, 0x65, 0xf9, 0x06, 0x64, 0x4f, 0x4f, 0x29, 0x9c,
	0x07, 0xd8, 0x0e, 0x74, 0x26, 0x99, 0xd3, 0xd3, 0x26, 0xa3, 0x2a, 0xf3, 0x30, 0x2b, 0xe8, 0x23,
	0xd6, 0x1b, 0xdf, 0x91, 0x60, 0xbe, 0xfe, 0xa0, 0xdd, 0x78, 0xda, 0x68, 0x3f, 0xeb, 0xec, 0xb7,
	0xeb, 0xed, 0x27, 0xfb, 0x68, 0x11, 0x90, 0x40, 0xea, 0xb4, 0x77, 0x77, 0xf7, 0xb6, 0x9a, 0x05,
	0x09, 0x2d, 0xc1, 0x65, 0x91, 0x4e, 0xa9, 0x8d, 0xe6, 0x76, 0x21, 0x85, 0xae, 0x40, 0x21, 0xb0,
	0xf0, 0x74, 0xab, 0x55, 0x98, 0x42, 0x57, 0x61, 0x41, 0xa4, 0xd6, 0x37, 0x1f, 0x35, 0x9a, 0xfb,
	0xed, 0xdd, 0xbd, 0x42, 0x26, 0xac, 0x61, 0x73, 0x6b, 0x67, 0xab, 0xbd, 0x55, 0x28, 0xa0, 0xcb,
	0x01, 0x63, 0x3a, 0xf5, 0x9d, 0x9d, 0xc2, 0xea, 0x1b, 0x3f, 0x0b, 0xf3, 0xbb, 0x0f, 0x1f, 0x6e,
	0xb5, 0xb6, 0x9e, 0x6e, 0x35, 0xdb, 0x9d, 0xf6, 0xb3, 0xbd, 0x2d, 0x2a, 0x9a, 0x91, 0x3a, 0x43,
	0x5a, 0xa7, 0xf9, 0x64, 0x67, 0xa7, 0x70, 0x21, 0x76, 0x69, 0xbb, 0xfe, 0x68, 0xab, 0x20, 0xa1,
	0x6b, 0xb0, 0x14, 0x5d, 0xda, 0xd9, 0xdd, 0xa8, 0xef, 0x14, 0x52, 0xa8, 0x08, 0x57, 0x22, 0x8b,
	0x54, 0x7f, 0xfa, 0x0d, 0x03, 0x2e, 0x09, 0xfa, 0x5d, 0x8c, 0x56, 0xa0, 0x24, 0xb2, 0x73, 0x6a,
	0x67, 0xf7, 0xe1, 0xc3, 0x9d, 0x46, 0x93, 0xea, 0xba, 0x0e, 0x57, 0xe3, 0xd6, 0x9b, 0x6c, 0x39,
	0x85, 0x4a, 0xb0, 0x18, 0xb3, 0xcc, 0xf5, 0xd5, 0x21, 0xbb, 0xe7, 0xfb, 0x34, 0x0f, 0x99, 0xd6,
	0xd6, 0x76, 0x63, 0xbf, 0xbd, 0xd5, 0x2a, 0x48, 0x08, 0xe0, 0x62, 0xa3, 0xf9, 0xb4, 0xd1, 0xa6,
	0x22, 0x72, 0x30, 0xb3, 0x59, 0x7f, 0xd6, 0xaa, 0x37, 0x3f, 0x28, 0xa4, 0xd1, 0x2c, 0x64, 0xdb,
	0xbb, 0xed, 0xfa, 0x0e, 0xfb, 0x77, 0xea, 0xfe, 0x0f, 0xdf, 0x04, 0xff, 0xc1, 0x0b, 0xea, 0x42,
	0x5e, 0x9c, 0x7d, 0xa3, 0xab, 0xc2, 0xd1, 0x11, 0xbc, 0x49, 0x28, 0x25, 0x2d, 0x11, 0x4b, 0xb9,
	0xfe, 0xf3, 0x1f, 0x7f, 0x9a, 0xc6, 0xdf, 0xf8, 0xf8, 0xd3, 0x74, 0xea, 0xa8, 0xf2, 0xad, 0x8f,
	0x3f, 0x4d, 0xe7, 0xcb, 0x47, 0x72, 0x95, 0x16, 0x5e, 0x59, 0xd7, 0x6a, 0xe8, 0x00, 0xae, 0x78,
	0x17, 0x52, 0x81, 0xd7, 0x2e, 0xcb, 0x77, 0xfd, 0x77, 0x36, 0x77, 0xf7, 0x3f, 0xd8, 0xe0, 0xef,
	0x6c, 0xb6, 0xfa, 0x96, 0x73, 0xde, 0xd9, 0xdb, 0x28, 0x25, 0xbd, 0x7e, 0x61, 0x07, 0xd7, 0x3c,
	0xd5, 0x28, 0x51, 0x8d, 0x17, 0xa8, 0xbe, 0x0b, 0xe8, 0x47, 0x12, 0xcc, 0x05, 0x1f, 0x1b, 0x20,
	0x41, 0x40, 0xe4, 0x3d, 0x43, 0x69, 0x39, 0x79, 0x91, 0x58, 0xca, 0xef, 0x4b, 0x54, 0x7e, 0x8e,
	0xca, 0x5f, 0x34, 0x2b, 0x56, 0xe5, 0xb4, 0x62, 0x57, 0x48, 0xc5, 0xa9, 0x1c, 0x57, 0xf4, 0xca,
	0xf3, 0xca, 0x49, 0xa5, 0xc7, 0x76, 0xf9, 0x0d, 0xa9, 0x6c, 0xca, 0x55, 0x43, 0xed, 0xe3, 0x9a,
	0x5c, 0xb6, 0xe4, 0xaa, 0x86, 0x49, 0xb7, 0x26, 0x97, 0x4f, 0xe5, 0x2a, 0xff, 0x21, 0x53, 0x93,
	0xcb, 0xb6, 0x5c, 0x1d, 0x5e, 0x29, 0xd7, 0xe4, 0x32, 0x91, 0xab, 0x5e, 0x5f, 0x5f, 0x93, 0xcb,
	0x0e, 0x07, 0xa9, 0xa3, 0x6b, 0x35, 0xb9, 0x7c, 0x2c, 0x57, 0x85, 0xe7, 0x36, 0x35, 0xb9, 0xac,
	0xcb, 0x55, 0xfe, 0x88, 0xa6, 0x26, 0x97, 0x9f, 0xfb, 0xd2, 0x4f, 0xe4, 0x2a, 0xbb, 0xff, 0xa8,
	0xc9, 0xe5, 0x9e, 0x5c, 0xf5, 0x5e, 0xc2, 0xd4, 0xd0, 0xaf, 0xa6, 0x60, 0x2e, 0xf8, 0xce, 0x41,
	0x04, 0x20, 0xf2, 0x94, 0x42, 0x04, 0x20, 0xfa, 0x3c, 0x42, 0xf9, 0x0b, 0x06, 0x40, 0x9e, 0x02,
	0x50, 0x34, 0x2a, 0xc9, 0x10, 0xfc, 0xa6, 0x54, 0x36, 0xe4, 0xaa, 0xf0, 0x1a, 0xa2, 0x26, 0xff,
	0x04, 0x61, 0xf2, 0x4d, 0x89, 0xdd, 0xf2, 0xe1, 0x78, 0x4c, 0x22, 0x0f, 0x3b, 0x4a, 0x23, 0x03,
	0x52, 0xd9, 0xa0, 0x90, 0xcc, 0x52, 0x48, 0xa6, 0xd4, 0x0a, 0x8f, 0xf3, 0xf5, 0xb2, 0x1a, 0xde,
	0xfd, 0x91, 0x5c, 0xd5, 0xb5, 0x7b, 0xde, 0x1f, 0xf7, 0xbd, 0x3f, 0xde, 0xae, 0xc9, 0x77, 0xef,
	0xde, 0x45, 0x7f, 0x26, 0xb1, 0xb3, 0x53, 0x7c, 0x3e, 0x81, 0x96, 0x03, 0x89, 0x15, 0x7a, 0x0d,
	0x52, 0xba, 0x3e, 0x62, 0x95, 0x58, 0xca, 0x29, 0x35, 0x6a, 0x8e, 0x1a, 0x05, 0xa4, 0xd2, 0xab,
	0x18, 0x15, 0xdb, 0x35, 0xed, 0xff, 0x53, 0x5c, 0xf9, 0x10, 0x51, 0xbe, 0xb7, 0x7e, 0x7f, 0xfd,
	0xed, 0xf5, 0xcf, 0xad, 0x7f, 0x61, 0xfd, 0xde, 0xe7, 0xd7, 0xdf, 0xbe, 0xc7, 0x61, 0xe2, 0x37,
	0x97, 0xda, 0x8b, 0x9a, 0x4c, 0x5d, 0xd8, 0x75, 0x41, 0xb5, 0xe5, 0x2a, 0xeb, 0x9b, 0xdd, 0xd7,
	0x20, 0x95, 0xb7, 0xd6, 0xdd, 0xbd, 0xf8, 0x4e, 0xa1, 0x91, 0x56, 0x08, 0x5f, 0xdc, 0xa3, 0xeb,
	0xe1, 0x7c, 0x0a, 0xdc, 0x96, 0x97, 0x46, 0x2d, 0x13, 0x4b, 0xf9, 0x13, 0x16, 0x6e, 0x0b, 0x63,
	0xf2, 0xed, 0xd7, 0x13, 0xf3, 0xcd, 0xf3, 0x7e, 0x20, 0xb4, 0x2a, 0x4d, 0x12, 0x8c, 0x2e, 0x4e,
	0x70, 0x84, 0x50, 0x3c, 0xa6, 0x42, 0xfc, 0xc7, 0x16, 0x3c, 0xbe, 0xe8, 0x29, 0x5a, 0x61, 0x88,
	0xf1, 0x18, 0xa3, 0xed, 0x05, 0x8f, 0xb1, 0x61, 0x5c, 0xf6, 0x3c, 0x68, 0x5d, 0x46, 0xf4, 0xad,
	0x14, 0x14, 0xc2, 0xef, 0x13, 0x44, 0x4c, 0x62, 0xde, 0x2e, 0x8c, 0x89, 0xb6, 0x3f, 0x65, 0x90,
	0x2c, 0x8e, 0xcd, 0xc0, 0x9f, 0x60, 0x50, 0xba, 0x30, 0x1b, 0x78, 0x61, 0x81, 0x4a, 0x81, 0xe4,
	0x7b, 0x19, 0x34, 0x8a, 0x14, 0x8c, 0x25, 0x76, 0xc2, 0x18, 0x6c, 0xdb, 0x33, 0x34, 0x68, 0xe9,
	0xe1, 0xf2, 0x1d, 0x09, 0x66, 0x03, 0xcf, 0x1d, 0x44, 0x2d, 0xe1, 0x57, 0x1b, 0xa5, 0xc4, 0x35,
	0x62, 0x29, 0x5f, 0xa1, 0x3a, 0x8a, 0x2c, 0x95, 0x86, 0x70, 0x53, 0x5d, 0xbc, 0xa6, 0xf1, 0xbd,
	0x71, 0x90, 0x29, 0x1c, 0x1c, 0xe4, 0xe1, 0xf6, 0x6d, 0x2f, 0xa5, 0x0c, 0x0d, 0xbf, 0xe0, 0x18,
	0xf3, 0xa4, 0x42, 0x26, 0xe4, 0x84, 0xa7, 0x0f, 0x48, 0x78, 0x77, 0x11, 0x7c, 0x11, 0x31, 0x06,
	0x83, 0x35, 0x6a, 0xdf, 0x2a, 0xab, 0x3f, 0x47, 0x15, 0x9b, 0x59, 0xb6, 0xe0, 0x65, 0xa6, 0x67,
	0x01, 0xf3, 0x0b, 0x1a, 0xc0, 0x5c, 0xf0, 0x95, 0x84, 0x58, 0xf6, 0x22, 0xef, 0x27, 0x26, 0x51,
	0x2b, 0x4f, 0xa2, 0xb6, 0x0b, 0x39, 0xe1, 0x4d, 0x85, 0xb8, 0xcf, 0xe0, 0x53, 0x8b, 0x31, 0x0a,
	0x97, 0xa9, 0xc2, 0x1b, 0x42, 0x37, 0x91, 0x13, 0xd4, 0xa1, 0x13, 0xc8, 0x09, 0x4f, 0x26, 0x44,
	0x25, 0xc1, 0xc7, 0x19, 0xa5, 0x84, 0x15, 0x62, 0xf1, 0x1d, 0x29, 0x6c, 0x47, 0x34, 0x9f, 0xf8,
	0x8e, 0x78, 0xb5, 0xb4, 0x1d, 0x9e, 0x0c, 0xae, 0xe7, 0x3e, 0x64, 0xb1, 0x35, 0x1c, 0x6a, 0x4e,
	0xde, 0xb2, 0x44, 0x66, 0xa1, 0xbc, 0x65, 0xb9, 0x29, 0xb4, 0x2c, 0xd8, 0x7b, 0xfd, 0x27, 0x4e,
	0x2e, 0xc7, 0x68, 0x90, 0xe3, 0x35, 0x0c, 0xe7, 0x27, 0x5c, 0xcd, 0x2d, 0x41, 0xcd, 0xb7, 0x25,
	0x98, 0x0f, 0x0d, 0x78, 0x51, 0xdc, 0xe1, 0xef, 0x0f, 0x5a, 0xc7, 0xb8, 0xa7, 0x49, 0x15, 0xdc,
	0xa6, 0x0a, 0x32, 0x46, 0xe5, 0x79, 0xc5, 0x72, 0x63, 0xe2, 0x1d, 0x9a, 0x90, 0xee, 0xb0, 0x97,
	0x85, 0xc5, 0x73, 0x7e, 0x32, 0xf2, 0x61, 0x28, 0x4f, 0x1b, 0x7f, 0xf4, 0x2d, 0x46, 0xcd, 0x31,
	0xe4, 0xc5, 0x49, 0xaf, 0xd8, 0x82, 0x86, 0x26, 0xc0, 0x63, 0x0c, 0x5b, 0xa5, 0x86, 0xbd, 0xc6,
	0xe2, 0x46, 0x67, 0x26, 0xcd, 0xd1, 0xca, 0x35, 0x34, 0x69, 0x08, 0xb8, 0x38, 0xfb, 0x79, 0x59,
	0xc0, 0xc3, 0xc3, 0x47, 0x0e, 0xf8, 0x1d, 0x01, 0xf0, 0x53, 0xf6, 0x63, 0x3e, 0x7e, 0x08, 0x37,
	0x5a, 0xd9, 0x6b, 0xa3, 0x94, 0x85, 0x7d, 0xbc, 0x26, 0xa8, 0xfc, 0x85, 0x14, 0x2c, 0xc4, 0x0e,
	0x5f, 0x91, 0x12, 0xe3, 0xe9, 0xd0, 0x24, 0x71, 0x0c, 0xac, 0xbf, 0xcb, 0x0e, 0xa2, 0xd7, 0xa9,
	0xb6, 0x02, 0x77, 0x38, 0xa9, 0xe0, 0xca, 0x8b, 0x8a, 0x5a, 0xe9, 0x33, 0x94, 0x7f, 0x91, 0xb5,
	0x80, 0xde, 0xd0, 0x34, 0xc1, 0xf3, 0xc3, 0xd9, 0x39, 0xaf, 0x8c, 0xc3, 0x09, 0x78, 0x4d, 0x2e,
	0xe3, 0x40, 0x03, 0xf8, 0x42, 0xae, 0x0e, 0xc7, 0x89, 0xf2, 0x5b, 0xef, 0xaa, 0x86, 0x66, 0xeb,
	0xa6, 0x26, 0xdf, 0x7b, 0x57, 0x37, 0x69, 0xf9, 0x55, 0x59, 0x87, 0x27, 0x33, 0x4d, 0x7d, 0xb9,
	0xea, 0x8f, 0x2d, 0x6b, 0xc8, 0x01, 0x14, 0x1d, 0x95, 0xa2, 0xd5, 0x70, 0x3c, 0xbd, 0xdc, 0xf6,
	0x59, 0x54, 0xbd, 0x11, 0x8e, 0xaa, 0xe1, 0x76, 0xd1, 0x8b, 0xc8, 0x0d, 0xad, 0xf7, 0x02, 0x05,
	0xdd, 0x0a, 0xf8, 0x33, 0xe1, 0xe9, 0x4b, 0xe9, 0xf6, 0x04, 0x5c, 0xc4, 0x52, 0x0a, 0xd4, 0x90,
	0xbf, 0x12, 0x7f, 0xf3, 0x0c, 0x22, 0x17, 0xf7, 0xfe, 0x73, 0xa0, 0xc0, 0x11, 0x13, 0xff, 0xf4,
	0xa0, 0x74, 0x6b, 0x3c, 0x93, 0xa7, 0xf6, 0xaf, 0x45, 0xb5, 0x04, 0xae, 0x26, 0xbe, 0x18, 0x41,
	0xaf, 0x85, 0x0f, 0x9a, 0x04, 0xe5, 0xa3, 0x41, 0x67, 0x4a, 0xff, 0x46, 0x54, 0xfa, 0x75, 0xc9,
	0x1f, 0x55, 0xc6, 0xa9, 0xbd, 0x13, 0x29, 0xf6, 0x09, 0x7a, 0xd7, 0x26, 0x63, 0xf4, 0x36, 0xfe,
	0xb7, 0xa1, 0x8d, 0x27, 0xbe, 0xe5, 0x10, 0x37, 0x3e, 0xea, 0xc1, 0xc7, 0x24, 0x1b, 0xff, 0x9e,
	0xa8, 0xb4, 0x07, 0x97, 0x63, 0x1e, 0x49, 0x20, 0x39, 0xd1, 0x79, 0xee, 0xa5, 0x6c, 0xe9, 0xc6,
	0x18, 0x0e, 0x6f, 0x8b, 0x7f, 0x17, 0xd2, 0x16, 0xf3, 0xdc, 0x00, 0xc9, 0x89, 0x21, 0x1a, 0xa3,
	0x2d, 0xe1, 0xbd, 0x02, 0xd7, 0xf6, 0xf7, 0xa2, 0xb6, 0x3e, 0x2c, 0xc6, 0x3f, 0x44, 0x10, 0x03,
	0x38, 0xf1, 0xa9, 0xc2, 0x24, 0x50, 0x7e, 0x5f, 0x54, 0xf7, 0x33, 0x50, 0x4c, 0xba, 0x32, 0x47,
	0x42, 0x12, 0x8e, 0xb8, 0xb2, 0x17, 0x2b, 0xf4, 0xa8, 0xdb, 0x77, 0xae, 0xfc, 0x1f, 0x44, 0xe5,
	0xd1, 0x32, 0xe1, 0x3f, 0x87, 0x48, 0x2e, 0x13, 0xc2, 0x25, 0xfb, 0x88, 0x32, 0x21, 0x5e, 0xaa,
	0x73, 0xcd, 0xff, 0x28, 0x6a, 0xfe, 0x2d, 0x09, 0x96, 0x12, 0xe6, 0xe6, 0xa2, 0xea, 0xe4, 0xd1,
	0xfa, 0x18, 0xa0, 0xdf, 0xa3, 0x1a, 0xff, 0x89, 0x69, 0x9c, 0x39, 0xaa, 0x0c, 0x2a, 0x66, 0xc5,
	0xa0, 0x8a, 0xd7, 0xca, 0x47, 0x42, 0x77, 0x3c, 0xa8, 0x0e, 0xf8, 0x70, 0xa0, 0x6a, 0x3e, 0x3f,
	0xe0, 0x24, 0xc3, 0xeb, 0xb2, 0xbe, 0x29, 0x31, 0x6c, 0xc6, 0x19, 0x98, 0x7c, 0x53, 0x11, 0xc2,
	0x26, 0xe9, 0xea, 0x41, 0xb9, 0x43, 0x2d, 0xfd, 0x67, 0xc9, 0xed, 0x65, 0x07, 0xac, 0x9c, 0x5f,
	0x8e, 0x31, 0x13, 0xfd, 0x1c, 0x2c, 0xc4, 0xde, 0xb9, 0x88, 0xc7, 0x69, 0xd2, 0xf5, 0x50, 0xe9,
	0xe6, 0x58, 0x1e, 0x6f, 0x64, 0xf6, 0x2f, 0x52, 0xb0, 0xcb, 0xf5, 0x0d, 0x41, 0x7d, 0x86, 0x48,
	0xe0, 0x42, 0x60, 0xd3, 0x1d, 0x8e, 0x8f, 0x6e, 0x21, 0x82, 0x06, 0xc6, 0xde, 0x28, 0xf0, 0x10,
	0xf9, 0x4f, 0x31, 0x44, 0x4c, 0x76, 0xc7, 0x14, 0xe0, 0x6e, 0xfb, 0x33, 0xf4, 0x57, 0xa4, 0xf0,
	0xbf, 0xa4, 0x60, 0xef, 0x1b, 0xbd, 0x9a, 0x10, 0x8f, 0xea, 0xd8, 0x8b, 0x8b, 0x49, 0x32, 0xfe,
	0xbf, 0x45, 0x35, 0xcf, 0x20, 0x2f, 0xce, 0xe0, 0xc5, 0xde, 0x32, 0x34, 0x9b, 0x9f, 0x44, 0xf4,
	0xff, 0x88, 0xa2, 0xbf, 0xc2, 0x26, 0xa7, 0xfe, 0xc5, 0x46, 0x68, 0x72, 0x2a, 0x5e, 0x8e, 0x84,
	0x7e, 0x74, 0x06, 0xee, 0x42, 0xb8, 0xe0, 0x1f, 0x88, 0x82, 0x9b, 0x90, 0xf5, 0xc7, 0xf0, 0x48,
	0x78, 0xf7, 0x2d, 0xde, 0x05, 0x94, 0x96, 0x62, 0xe9, 0x9e, 0xbc, 0x1f, 0x0a, 0xf2, 0x4a, 0x17,
	0x7f, 0xe9, 0xe3, 0x4f, 0xd3, 0xff, 0x7e, 0xb6, 0x51, 0xf8, 0xee, 0x27, 0x2b, 0xd2, 0xf7, 0x3f,
	0x59, 0x91, 0xfe, 0xed, 0x93, 0x15, 0xe9, 0x57, 0xfe, 0x63, 0xe5, 0xc2, 0xff, 0x05, 0x00, 0x00,
	0xff, 0xff, 0x99, 0xed, 0xd2, 0xa0, 0x18, 0x39, 0x00, 0x00,
}
