package Activity

// Code generated by protoc-gen-svrkit-go. DO NOT EDIT.
// source: src/activitysvr/activity.proto

/*
 This is a generated svrkit golang dev toolkit.
*/

import svrkit "gitlab.ttyuyin.com/golang/svrkit"

import context "golang.org/x/net/context"

import tlvpickle "gitlab.ttyuyin.com/golang/svrkit/protocol/common/tlvpickle"

var _ = context.TODO

// Magic Number for Activity service
const ActivityMagic = uint16(15201)

// SvrkitClient API for Activity service

type ActivityClientInterface interface {
	// activities 100~
	GetGameOffer(ctx context.Context, uin uint32, in *GetGameOfferReq, opts ...svrkit.CallOption) (*GetGameOfferRsp, error)
	// activities 1~
	GetOfferCategoryList(ctx context.Context, uin uint32, in *tlvpickle.SKBuiltinEmpty_PB, opts ...svrkit.CallOption) (*OfferCategoryList, error)
	// activities 10 ~
	CreateActivity(ctx context.Context, uin uint32, in *CreateActivityReq, opts ...svrkit.CallOption) (*CreateActivityResp, error)
	UpdateActivity(ctx context.Context, uin uint32, in *UpdateActivityReq, opts ...svrkit.CallOption) (*UpdateActivityResp, error)
	DeleteActivity(ctx context.Context, uin uint32, in *DeleteActivityReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	GetActivityList(ctx context.Context, uin uint32, in *GetActivityListReq, opts ...svrkit.CallOption) (*GetActivityListResp, error)
	// 福利活动 20~
	CreateOfferEvent(ctx context.Context, uin uint32, in *CreateOfferEventReq, opts ...svrkit.CallOption) (*CreateOfferEventRsp, error)
	ModifyOfferEvent(ctx context.Context, uin uint32, in *ModifyOfferEventReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	DelOfferEvent(ctx context.Context, uin uint32, in *DelOfferEventReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	GetOfferEvent(ctx context.Context, uin uint32, in *GetOfferEventReq, opts ...svrkit.CallOption) (*GetOfferEventRsp, error)
	// 福利活动的游戏榜 30~
	AddGameRank(ctx context.Context, uin uint32, in *AddGameRankReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	ModifyGameRank(ctx context.Context, uin uint32, in *ModifyGameRankReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	DelGameRank(ctx context.Context, uin uint32, in *DelGameRankReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	GetGameRank(ctx context.Context, uin uint32, in *GetGameRankReq, opts ...svrkit.CallOption) (*GetGameRankRsp, error)
	GetActBanners(ctx context.Context, uin uint32, in *tlvpickle.SKBuiltinEmpty_PB, opts ...svrkit.CallOption) (*GetActBannersResp, error)
	GetActBannerOpTime(ctx context.Context, uin uint32, in *tlvpickle.SKBuiltinEmpty_PB, opts ...svrkit.CallOption) (*GetActBannerOpTimeResp, error)
	UpdateActBanner(ctx context.Context, uin uint32, in *UpdateActBannerReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	DelActBanner(ctx context.Context, uin uint32, in *DelActBannerReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	GetActSplashScreen(ctx context.Context, uin uint32, in *tlvpickle.SKBuiltinEmpty_PB, opts ...svrkit.CallOption) (*GetActSplashScreenResp, error)
	GetActSplashScreenOpTime(ctx context.Context, uin uint32, in *tlvpickle.SKBuiltinEmpty_PB, opts ...svrkit.CallOption) (*GetActSplashScreenOpTimeResp, error)
	UpdateActSplashScreen(ctx context.Context, uin uint32, in *UpdateActSplashScreenReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	DelActSplashScreen(ctx context.Context, uin uint32, in *DelActSplashScreenReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	GetFirstVoucherProducts(ctx context.Context, uin uint32, in *GetFirstVoucherProductsReq, opts ...svrkit.CallOption) (*GetFirstVoucherProductsResp, error)
	AddFirstVoucherProduct(ctx context.Context, uin uint32, in *AddFirstVoucherProductReq, opts ...svrkit.CallOption) (*AddFirstVoucherProductResp, error)
	ModifyFirstVoucherProduct(ctx context.Context, uin uint32, in *ModifyFirstVoucherProductReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	GetGameFirstVoucherProduct(ctx context.Context, uin uint32, in *GetGameFirstVoucherProductReq, opts ...svrkit.CallOption) (*GetGameFirstVoucherProductResp, error)
	DeleteFirstVoucherProduct(ctx context.Context, uin uint32, in *DeleteFirstVoucherProductReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	AddFirstVoucherItem(ctx context.Context, uin uint32, in *AddFirstVoucherItemReq, opts ...svrkit.CallOption) (*AddFirstVoucherItemResp, error)
	GetFirstVoucherItem(ctx context.Context, uin uint32, in *GetFirstVoucherItemReq, opts ...svrkit.CallOption) (*GetFirstVoucherItemResp, error)
	DeleteFirstVoucherItem(ctx context.Context, uin uint32, in *DeleteFirstVoucherItemReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	PurchaseFirstVoucherItem(ctx context.Context, uin uint32, in *PurchaseFirstVoucherItemReq, opts ...svrkit.CallOption) (*PurchaseFirstVoucherItemResp, error)
	GetFirstVoucherUserItem(ctx context.Context, uin uint32, in *GetFirstVoucherUserItemReq, opts ...svrkit.CallOption) (*GetFirstVoucherUserItemResp, error)
	SetGamePreorderUserSppt(ctx context.Context, uin uint32, in *SetGamePreorderUserSpptReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	GetGamePreorderUserSppt(ctx context.Context, uin uint32, in *GetGamePreorderUserSpptReq, opts ...svrkit.CallOption) (*GetGamePreorderUserSpptResp, error)
	GetGamePreorderResult(ctx context.Context, uin uint32, in *GetGamePreorderResultReq, opts ...svrkit.CallOption) (*GetGamePreorderResultResp, error)
	GetQQCarActivityDayRank(ctx context.Context, uin uint32, in *tlvpickle.SKBuiltinEmpty_PB, opts ...svrkit.CallOption) (*GetQQCarActivityRankResp, error)
	GetQQCarActivityTotalRank(ctx context.Context, uin uint32, in *tlvpickle.SKBuiltinEmpty_PB, opts ...svrkit.CallOption) (*GetQQCarActivityRankResp, error)
	NotifyInviteRecord(ctx context.Context, uin uint32, in *NotifyInviteRecordReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	SetUidPairQQ(ctx context.Context, uin uint32, in *SetUidPairQQReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	GetPrizeInfo(ctx context.Context, uin uint32, in *GetPrizeInfoReq, opts ...svrkit.CallOption) (*GetPrizeInfoResp, error)
	TakePrize(ctx context.Context, uin uint32, in *TakePrizeReq, opts ...svrkit.CallOption) (*TakePrizeResp, error)
}

type ActivitySvrkitClient struct {
	cc *svrkit.ClientConn
}

func NewActivitySvrkitClient(cc *svrkit.ClientConn) ActivityClientInterface {
	return &ActivitySvrkitClient{cc}
}

const (
	commandActivityGetSelfSvnInfo             = 9995
	commandActivityEcho                       = 9999
	commandActivityGetGameOffer               = 101
	commandActivityGetOfferCategoryList       = 1
	commandActivityCreateActivity             = 11
	commandActivityUpdateActivity             = 12
	commandActivityDeleteActivity             = 13
	commandActivityGetActivityList            = 14
	commandActivityCreateOfferEvent           = 21
	commandActivityModifyOfferEvent           = 22
	commandActivityDelOfferEvent              = 23
	commandActivityGetOfferEvent              = 24
	commandActivityAddGameRank                = 31
	commandActivityModifyGameRank             = 32
	commandActivityDelGameRank                = 33
	commandActivityGetGameRank                = 34
	commandActivityGetActBanners              = 35
	commandActivityGetActBannerOpTime         = 36
	commandActivityUpdateActBanner            = 37
	commandActivityDelActBanner               = 38
	commandActivityGetActSplashScreen         = 39
	commandActivityGetActSplashScreenOpTime   = 40
	commandActivityUpdateActSplashScreen      = 41
	commandActivityDelActSplashScreen         = 42
	commandActivityGetFirstVoucherProducts    = 201
	commandActivityAddFirstVoucherProduct     = 202
	commandActivityModifyFirstVoucherProduct  = 203
	commandActivityGetGameFirstVoucherProduct = 204
	commandActivityDeleteFirstVoucherProduct  = 205
	commandActivityAddFirstVoucherItem        = 206
	commandActivityGetFirstVoucherItem        = 207
	commandActivityDeleteFirstVoucherItem     = 208
	commandActivityPurchaseFirstVoucherItem   = 209
	commandActivityGetFirstVoucherUserItem    = 210
	commandActivitySetGamePreorderUserSppt    = 211
	commandActivityGetGamePreorderUserSppt    = 212
	commandActivityGetGamePreorderResult      = 213
	commandActivityGetQQCarActivityDayRank    = 240
	commandActivityGetQQCarActivityTotalRank  = 241
	commandActivityNotifyInviteRecord         = 242
	commandActivitySetUidPairQQ               = 243
	commandActivityGetPrizeInfo               = 244
	commandActivityTakePrize                  = 245
)

func (c *ActivitySvrkitClient) GetGameOffer(ctx context.Context, uin uint32, in *GetGameOfferReq, opts ...svrkit.CallOption) (*GetGameOfferRsp, error) {
	out := new(GetGameOfferRsp)
	err := c.cc.Invoke(ctx, uin, commandActivityGetGameOffer, "/Activity.Activity/GetGameOffer", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ActivitySvrkitClient) GetOfferCategoryList(ctx context.Context, uin uint32, in *tlvpickle.SKBuiltinEmpty_PB, opts ...svrkit.CallOption) (*OfferCategoryList, error) {
	out := new(OfferCategoryList)
	err := c.cc.Invoke(ctx, uin, commandActivityGetOfferCategoryList, "/Activity.Activity/GetOfferCategoryList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ActivitySvrkitClient) CreateActivity(ctx context.Context, uin uint32, in *CreateActivityReq, opts ...svrkit.CallOption) (*CreateActivityResp, error) {
	out := new(CreateActivityResp)
	err := c.cc.Invoke(ctx, uin, commandActivityCreateActivity, "/Activity.Activity/CreateActivity", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ActivitySvrkitClient) UpdateActivity(ctx context.Context, uin uint32, in *UpdateActivityReq, opts ...svrkit.CallOption) (*UpdateActivityResp, error) {
	out := new(UpdateActivityResp)
	err := c.cc.Invoke(ctx, uin, commandActivityUpdateActivity, "/Activity.Activity/UpdateActivity", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ActivitySvrkitClient) DeleteActivity(ctx context.Context, uin uint32, in *DeleteActivityReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := c.cc.Invoke(ctx, uin, commandActivityDeleteActivity, "/Activity.Activity/DeleteActivity", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ActivitySvrkitClient) GetActivityList(ctx context.Context, uin uint32, in *GetActivityListReq, opts ...svrkit.CallOption) (*GetActivityListResp, error) {
	out := new(GetActivityListResp)
	err := c.cc.Invoke(ctx, uin, commandActivityGetActivityList, "/Activity.Activity/GetActivityList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ActivitySvrkitClient) CreateOfferEvent(ctx context.Context, uin uint32, in *CreateOfferEventReq, opts ...svrkit.CallOption) (*CreateOfferEventRsp, error) {
	out := new(CreateOfferEventRsp)
	err := c.cc.Invoke(ctx, uin, commandActivityCreateOfferEvent, "/Activity.Activity/CreateOfferEvent", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ActivitySvrkitClient) ModifyOfferEvent(ctx context.Context, uin uint32, in *ModifyOfferEventReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := c.cc.Invoke(ctx, uin, commandActivityModifyOfferEvent, "/Activity.Activity/ModifyOfferEvent", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ActivitySvrkitClient) DelOfferEvent(ctx context.Context, uin uint32, in *DelOfferEventReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := c.cc.Invoke(ctx, uin, commandActivityDelOfferEvent, "/Activity.Activity/DelOfferEvent", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ActivitySvrkitClient) GetOfferEvent(ctx context.Context, uin uint32, in *GetOfferEventReq, opts ...svrkit.CallOption) (*GetOfferEventRsp, error) {
	out := new(GetOfferEventRsp)
	err := c.cc.Invoke(ctx, uin, commandActivityGetOfferEvent, "/Activity.Activity/GetOfferEvent", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ActivitySvrkitClient) AddGameRank(ctx context.Context, uin uint32, in *AddGameRankReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := c.cc.Invoke(ctx, uin, commandActivityAddGameRank, "/Activity.Activity/AddGameRank", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ActivitySvrkitClient) ModifyGameRank(ctx context.Context, uin uint32, in *ModifyGameRankReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := c.cc.Invoke(ctx, uin, commandActivityModifyGameRank, "/Activity.Activity/ModifyGameRank", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ActivitySvrkitClient) DelGameRank(ctx context.Context, uin uint32, in *DelGameRankReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := c.cc.Invoke(ctx, uin, commandActivityDelGameRank, "/Activity.Activity/DelGameRank", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ActivitySvrkitClient) GetGameRank(ctx context.Context, uin uint32, in *GetGameRankReq, opts ...svrkit.CallOption) (*GetGameRankRsp, error) {
	out := new(GetGameRankRsp)
	err := c.cc.Invoke(ctx, uin, commandActivityGetGameRank, "/Activity.Activity/GetGameRank", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ActivitySvrkitClient) GetActBanners(ctx context.Context, uin uint32, in *tlvpickle.SKBuiltinEmpty_PB, opts ...svrkit.CallOption) (*GetActBannersResp, error) {
	out := new(GetActBannersResp)
	err := c.cc.Invoke(ctx, uin, commandActivityGetActBanners, "/Activity.Activity/GetActBanners", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ActivitySvrkitClient) GetActBannerOpTime(ctx context.Context, uin uint32, in *tlvpickle.SKBuiltinEmpty_PB, opts ...svrkit.CallOption) (*GetActBannerOpTimeResp, error) {
	out := new(GetActBannerOpTimeResp)
	err := c.cc.Invoke(ctx, uin, commandActivityGetActBannerOpTime, "/Activity.Activity/GetActBannerOpTime", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ActivitySvrkitClient) UpdateActBanner(ctx context.Context, uin uint32, in *UpdateActBannerReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := c.cc.Invoke(ctx, uin, commandActivityUpdateActBanner, "/Activity.Activity/UpdateActBanner", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ActivitySvrkitClient) DelActBanner(ctx context.Context, uin uint32, in *DelActBannerReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := c.cc.Invoke(ctx, uin, commandActivityDelActBanner, "/Activity.Activity/DelActBanner", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ActivitySvrkitClient) GetActSplashScreen(ctx context.Context, uin uint32, in *tlvpickle.SKBuiltinEmpty_PB, opts ...svrkit.CallOption) (*GetActSplashScreenResp, error) {
	out := new(GetActSplashScreenResp)
	err := c.cc.Invoke(ctx, uin, commandActivityGetActSplashScreen, "/Activity.Activity/GetActSplashScreen", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ActivitySvrkitClient) GetActSplashScreenOpTime(ctx context.Context, uin uint32, in *tlvpickle.SKBuiltinEmpty_PB, opts ...svrkit.CallOption) (*GetActSplashScreenOpTimeResp, error) {
	out := new(GetActSplashScreenOpTimeResp)
	err := c.cc.Invoke(ctx, uin, commandActivityGetActSplashScreenOpTime, "/Activity.Activity/GetActSplashScreenOpTime", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ActivitySvrkitClient) UpdateActSplashScreen(ctx context.Context, uin uint32, in *UpdateActSplashScreenReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := c.cc.Invoke(ctx, uin, commandActivityUpdateActSplashScreen, "/Activity.Activity/UpdateActSplashScreen", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ActivitySvrkitClient) DelActSplashScreen(ctx context.Context, uin uint32, in *DelActSplashScreenReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := c.cc.Invoke(ctx, uin, commandActivityDelActSplashScreen, "/Activity.Activity/DelActSplashScreen", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ActivitySvrkitClient) GetFirstVoucherProducts(ctx context.Context, uin uint32, in *GetFirstVoucherProductsReq, opts ...svrkit.CallOption) (*GetFirstVoucherProductsResp, error) {
	out := new(GetFirstVoucherProductsResp)
	err := c.cc.Invoke(ctx, uin, commandActivityGetFirstVoucherProducts, "/Activity.Activity/GetFirstVoucherProducts", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ActivitySvrkitClient) AddFirstVoucherProduct(ctx context.Context, uin uint32, in *AddFirstVoucherProductReq, opts ...svrkit.CallOption) (*AddFirstVoucherProductResp, error) {
	out := new(AddFirstVoucherProductResp)
	err := c.cc.Invoke(ctx, uin, commandActivityAddFirstVoucherProduct, "/Activity.Activity/AddFirstVoucherProduct", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ActivitySvrkitClient) ModifyFirstVoucherProduct(ctx context.Context, uin uint32, in *ModifyFirstVoucherProductReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := c.cc.Invoke(ctx, uin, commandActivityModifyFirstVoucherProduct, "/Activity.Activity/ModifyFirstVoucherProduct", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ActivitySvrkitClient) GetGameFirstVoucherProduct(ctx context.Context, uin uint32, in *GetGameFirstVoucherProductReq, opts ...svrkit.CallOption) (*GetGameFirstVoucherProductResp, error) {
	out := new(GetGameFirstVoucherProductResp)
	err := c.cc.Invoke(ctx, uin, commandActivityGetGameFirstVoucherProduct, "/Activity.Activity/GetGameFirstVoucherProduct", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ActivitySvrkitClient) DeleteFirstVoucherProduct(ctx context.Context, uin uint32, in *DeleteFirstVoucherProductReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := c.cc.Invoke(ctx, uin, commandActivityDeleteFirstVoucherProduct, "/Activity.Activity/DeleteFirstVoucherProduct", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ActivitySvrkitClient) AddFirstVoucherItem(ctx context.Context, uin uint32, in *AddFirstVoucherItemReq, opts ...svrkit.CallOption) (*AddFirstVoucherItemResp, error) {
	out := new(AddFirstVoucherItemResp)
	err := c.cc.Invoke(ctx, uin, commandActivityAddFirstVoucherItem, "/Activity.Activity/AddFirstVoucherItem", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ActivitySvrkitClient) GetFirstVoucherItem(ctx context.Context, uin uint32, in *GetFirstVoucherItemReq, opts ...svrkit.CallOption) (*GetFirstVoucherItemResp, error) {
	out := new(GetFirstVoucherItemResp)
	err := c.cc.Invoke(ctx, uin, commandActivityGetFirstVoucherItem, "/Activity.Activity/GetFirstVoucherItem", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ActivitySvrkitClient) DeleteFirstVoucherItem(ctx context.Context, uin uint32, in *DeleteFirstVoucherItemReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := c.cc.Invoke(ctx, uin, commandActivityDeleteFirstVoucherItem, "/Activity.Activity/DeleteFirstVoucherItem", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ActivitySvrkitClient) PurchaseFirstVoucherItem(ctx context.Context, uin uint32, in *PurchaseFirstVoucherItemReq, opts ...svrkit.CallOption) (*PurchaseFirstVoucherItemResp, error) {
	out := new(PurchaseFirstVoucherItemResp)
	err := c.cc.Invoke(ctx, uin, commandActivityPurchaseFirstVoucherItem, "/Activity.Activity/PurchaseFirstVoucherItem", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ActivitySvrkitClient) GetFirstVoucherUserItem(ctx context.Context, uin uint32, in *GetFirstVoucherUserItemReq, opts ...svrkit.CallOption) (*GetFirstVoucherUserItemResp, error) {
	out := new(GetFirstVoucherUserItemResp)
	err := c.cc.Invoke(ctx, uin, commandActivityGetFirstVoucherUserItem, "/Activity.Activity/GetFirstVoucherUserItem", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ActivitySvrkitClient) SetGamePreorderUserSppt(ctx context.Context, uin uint32, in *SetGamePreorderUserSpptReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := c.cc.Invoke(ctx, uin, commandActivitySetGamePreorderUserSppt, "/Activity.Activity/SetGamePreorderUserSppt", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ActivitySvrkitClient) GetGamePreorderUserSppt(ctx context.Context, uin uint32, in *GetGamePreorderUserSpptReq, opts ...svrkit.CallOption) (*GetGamePreorderUserSpptResp, error) {
	out := new(GetGamePreorderUserSpptResp)
	err := c.cc.Invoke(ctx, uin, commandActivityGetGamePreorderUserSppt, "/Activity.Activity/GetGamePreorderUserSppt", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ActivitySvrkitClient) GetGamePreorderResult(ctx context.Context, uin uint32, in *GetGamePreorderResultReq, opts ...svrkit.CallOption) (*GetGamePreorderResultResp, error) {
	out := new(GetGamePreorderResultResp)
	err := c.cc.Invoke(ctx, uin, commandActivityGetGamePreorderResult, "/Activity.Activity/GetGamePreorderResult", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ActivitySvrkitClient) GetQQCarActivityDayRank(ctx context.Context, uin uint32, in *tlvpickle.SKBuiltinEmpty_PB, opts ...svrkit.CallOption) (*GetQQCarActivityRankResp, error) {
	out := new(GetQQCarActivityRankResp)
	err := c.cc.Invoke(ctx, uin, commandActivityGetQQCarActivityDayRank, "/Activity.Activity/GetQQCarActivityDayRank", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ActivitySvrkitClient) GetQQCarActivityTotalRank(ctx context.Context, uin uint32, in *tlvpickle.SKBuiltinEmpty_PB, opts ...svrkit.CallOption) (*GetQQCarActivityRankResp, error) {
	out := new(GetQQCarActivityRankResp)
	err := c.cc.Invoke(ctx, uin, commandActivityGetQQCarActivityTotalRank, "/Activity.Activity/GetQQCarActivityTotalRank", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ActivitySvrkitClient) NotifyInviteRecord(ctx context.Context, uin uint32, in *NotifyInviteRecordReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := c.cc.Invoke(ctx, uin, commandActivityNotifyInviteRecord, "/Activity.Activity/NotifyInviteRecord", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ActivitySvrkitClient) SetUidPairQQ(ctx context.Context, uin uint32, in *SetUidPairQQReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := c.cc.Invoke(ctx, uin, commandActivitySetUidPairQQ, "/Activity.Activity/SetUidPairQQ", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ActivitySvrkitClient) GetPrizeInfo(ctx context.Context, uin uint32, in *GetPrizeInfoReq, opts ...svrkit.CallOption) (*GetPrizeInfoResp, error) {
	out := new(GetPrizeInfoResp)
	err := c.cc.Invoke(ctx, uin, commandActivityGetPrizeInfo, "/Activity.Activity/GetPrizeInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ActivitySvrkitClient) TakePrize(ctx context.Context, uin uint32, in *TakePrizeReq, opts ...svrkit.CallOption) (*TakePrizeResp, error) {
	out := new(TakePrizeResp)
	err := c.cc.Invoke(ctx, uin, commandActivityTakePrize, "/Activity.Activity/TakePrize", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}
