// Code generated by protoc-gen-go. DO NOT EDIT.
// source: gold-commission/gold-commission.proto

package gold_commission // import "golang.52tt.com/protocol/services/gold-commission"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import reconcile_v2 "golang.52tt.com/protocol/services/reconcile-v2"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// 佣金类型
type GoldType int32

const (
	GoldType_UNKNOWN_GOLD       GoldType = 0
	GoldType_AMUSE_GOLD         GoldType = 4
	GoldType_YUYIN_GOLD         GoldType = 7
	GoldType_INTERACT_GAME_GOLD GoldType = 101
	GoldType_ESPORT_GOLD        GoldType = 102
)

var GoldType_name = map[int32]string{
	0:   "UNKNOWN_GOLD",
	4:   "AMUSE_GOLD",
	7:   "YUYIN_GOLD",
	101: "INTERACT_GAME_GOLD",
	102: "ESPORT_GOLD",
}
var GoldType_value = map[string]int32{
	"UNKNOWN_GOLD":       0,
	"AMUSE_GOLD":         4,
	"YUYIN_GOLD":         7,
	"INTERACT_GAME_GOLD": 101,
	"ESPORT_GOLD":        102,
}

func (x GoldType) String() string {
	return proto.EnumName(GoldType_name, int32(x))
}
func (GoldType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_gold_commission_18e8a43e3dce6c6d, []int{0}
}

// 流水来源
type SourceType int32

const (
	SourceType_UnknownType       SourceType = 0
	SourceType_SourceTypePresent SourceType = 1
	// 2\3 ignore
	SourceType_SourceTypeKnight       SourceType = 4
	SourceType_SourceTypeWerewolf     SourceType = 5
	SourceType_SourceTypeInteractGame SourceType = 6
	SourceType_SourceTypeESport       SourceType = 7
)

var SourceType_name = map[int32]string{
	0: "UnknownType",
	1: "SourceTypePresent",
	4: "SourceTypeKnight",
	5: "SourceTypeWerewolf",
	6: "SourceTypeInteractGame",
	7: "SourceTypeESport",
}
var SourceType_value = map[string]int32{
	"UnknownType":            0,
	"SourceTypePresent":      1,
	"SourceTypeKnight":       4,
	"SourceTypeWerewolf":     5,
	"SourceTypeInteractGame": 6,
	"SourceTypeESport":       7,
}

func (x SourceType) String() string {
	return proto.EnumName(SourceType_name, int32(x))
}
func (SourceType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_gold_commission_18e8a43e3dce6c6d, []int{1}
}

type ChannelType int32

const (
	// from ga:ChannelType
	ChannelType_CTypeInvalid ChannelType = 0
	ChannelType_CTypeAmuse   ChannelType = 4
	ChannelType_CTypeYuyin   ChannelType = 7
)

var ChannelType_name = map[int32]string{
	0: "CTypeInvalid",
	4: "CTypeAmuse",
	7: "CTypeYuyin",
}
var ChannelType_value = map[string]int32{
	"CTypeInvalid": 0,
	"CTypeAmuse":   4,
	"CTypeYuyin":   7,
}

func (x ChannelType) String() string {
	return proto.EnumName(ChannelType_name, int32(x))
}
func (ChannelType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_gold_commission_18e8a43e3dce6c6d, []int{2}
}

type TimeFilterUnit int32

const (
	TimeFilterUnit_BY_DAY   TimeFilterUnit = 0
	TimeFilterUnit_BY_WEEK  TimeFilterUnit = 1
	TimeFilterUnit_BY_MONTH TimeFilterUnit = 2
)

var TimeFilterUnit_name = map[int32]string{
	0: "BY_DAY",
	1: "BY_WEEK",
	2: "BY_MONTH",
}
var TimeFilterUnit_value = map[string]int32{
	"BY_DAY":   0,
	"BY_WEEK":  1,
	"BY_MONTH": 2,
}

func (x TimeFilterUnit) String() string {
	return proto.EnumName(TimeFilterUnit_name, int32(x))
}
func (TimeFilterUnit) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_gold_commission_18e8a43e3dce6c6d, []int{3}
}

// 请求结算状态 (实际转换为 0 未结算 1 已结算)
type ReqSettleStatus int32

const (
	ReqSettleStatus_ReqSettleStatusALL      ReqSettleStatus = 0
	ReqSettleStatus_ReqSettleStatusWait     ReqSettleStatus = 1
	ReqSettleStatus_ReqSettleStatusFinished ReqSettleStatus = 2
)

var ReqSettleStatus_name = map[int32]string{
	0: "ReqSettleStatusALL",
	1: "ReqSettleStatusWait",
	2: "ReqSettleStatusFinished",
}
var ReqSettleStatus_value = map[string]int32{
	"ReqSettleStatusALL":      0,
	"ReqSettleStatusWait":     1,
	"ReqSettleStatusFinished": 2,
}

func (x ReqSettleStatus) String() string {
	return proto.EnumName(ReqSettleStatus_name, int32(x))
}
func (ReqSettleStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_gold_commission_18e8a43e3dce6c6d, []int{4}
}

// RangeType 获取类型枚举值
type RangeType int32

const (
	RangeType_DAY_RANGE_TYPE          RangeType = 0
	RangeType_MONTH_RANGE_TYPE        RangeType = 1
	RangeType_DAY_RANGE_TYPE_FOR_TEST RangeType = 10001
)

var RangeType_name = map[int32]string{
	0:     "DAY_RANGE_TYPE",
	1:     "MONTH_RANGE_TYPE",
	10001: "DAY_RANGE_TYPE_FOR_TEST",
}
var RangeType_value = map[string]int32{
	"DAY_RANGE_TYPE":          0,
	"MONTH_RANGE_TYPE":        1,
	"DAY_RANGE_TYPE_FOR_TEST": 10001,
}

func (x RangeType) String() string {
	return proto.EnumName(RangeType_name, int32(x))
}
func (RangeType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_gold_commission_18e8a43e3dce6c6d, []int{5}
}

type GetGuildMonthMemberListReq_QueryType int32

const (
	GetGuildMonthMemberListReq_UNKNOWN   GetGuildMonthMemberListReq_QueryType = 0
	GetGuildMonthMemberListReq_ANCHOR_ID GetGuildMonthMemberListReq_QueryType = 1
	GetGuildMonthMemberListReq_PAID_UID  GetGuildMonthMemberListReq_QueryType = 2
)

var GetGuildMonthMemberListReq_QueryType_name = map[int32]string{
	0: "UNKNOWN",
	1: "ANCHOR_ID",
	2: "PAID_UID",
}
var GetGuildMonthMemberListReq_QueryType_value = map[string]int32{
	"UNKNOWN":   0,
	"ANCHOR_ID": 1,
	"PAID_UID":  2,
}

func (x GetGuildMonthMemberListReq_QueryType) String() string {
	return proto.EnumName(GetGuildMonthMemberListReq_QueryType_name, int32(x))
}
func (GetGuildMonthMemberListReq_QueryType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_gold_commission_18e8a43e3dce6c6d, []int{64, 0}
}

// 查询娱乐房房间日环比数据
type GetAmuseRoomDayQoqInfoReq struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	RoomId               uint32   `protobuf:"varint,2,opt,name=room_id,json=roomId,proto3" json:"room_id,omitempty"`
	Day                  int64    `protobuf:"varint,3,opt,name=day,proto3" json:"day,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAmuseRoomDayQoqInfoReq) Reset()         { *m = GetAmuseRoomDayQoqInfoReq{} }
func (m *GetAmuseRoomDayQoqInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetAmuseRoomDayQoqInfoReq) ProtoMessage()    {}
func (*GetAmuseRoomDayQoqInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_gold_commission_18e8a43e3dce6c6d, []int{0}
}
func (m *GetAmuseRoomDayQoqInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAmuseRoomDayQoqInfoReq.Unmarshal(m, b)
}
func (m *GetAmuseRoomDayQoqInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAmuseRoomDayQoqInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetAmuseRoomDayQoqInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAmuseRoomDayQoqInfoReq.Merge(dst, src)
}
func (m *GetAmuseRoomDayQoqInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetAmuseRoomDayQoqInfoReq.Size(m)
}
func (m *GetAmuseRoomDayQoqInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAmuseRoomDayQoqInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAmuseRoomDayQoqInfoReq proto.InternalMessageInfo

func (m *GetAmuseRoomDayQoqInfoReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GetAmuseRoomDayQoqInfoReq) GetRoomId() uint32 {
	if m != nil {
		return m.RoomId
	}
	return 0
}

func (m *GetAmuseRoomDayQoqInfoReq) GetDay() int64 {
	if m != nil {
		return m.Day
	}
	return 0
}

type GetAmuseRoomDayQoqInfoRsp struct {
	StatTime             int64    `protobuf:"varint,1,opt,name=stat_time,json=statTime,proto3" json:"stat_time,omitempty"`
	Income               int64    `protobuf:"varint,2,opt,name=income,proto3" json:"income,omitempty"`
	CompareStatTime      int64    `protobuf:"varint,3,opt,name=compare_stat_time,json=compareStatTime,proto3" json:"compare_stat_time,omitempty"`
	CompareIncome        int64    `protobuf:"varint,4,opt,name=compare_income,json=compareIncome,proto3" json:"compare_income,omitempty"`
	Qoq                  float32  `protobuf:"fixed32,5,opt,name=qoq,proto3" json:"qoq,omitempty"`
	RoomId               uint32   `protobuf:"varint,6,opt,name=room_id,json=roomId,proto3" json:"room_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAmuseRoomDayQoqInfoRsp) Reset()         { *m = GetAmuseRoomDayQoqInfoRsp{} }
func (m *GetAmuseRoomDayQoqInfoRsp) String() string { return proto.CompactTextString(m) }
func (*GetAmuseRoomDayQoqInfoRsp) ProtoMessage()    {}
func (*GetAmuseRoomDayQoqInfoRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_gold_commission_18e8a43e3dce6c6d, []int{1}
}
func (m *GetAmuseRoomDayQoqInfoRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAmuseRoomDayQoqInfoRsp.Unmarshal(m, b)
}
func (m *GetAmuseRoomDayQoqInfoRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAmuseRoomDayQoqInfoRsp.Marshal(b, m, deterministic)
}
func (dst *GetAmuseRoomDayQoqInfoRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAmuseRoomDayQoqInfoRsp.Merge(dst, src)
}
func (m *GetAmuseRoomDayQoqInfoRsp) XXX_Size() int {
	return xxx_messageInfo_GetAmuseRoomDayQoqInfoRsp.Size(m)
}
func (m *GetAmuseRoomDayQoqInfoRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAmuseRoomDayQoqInfoRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAmuseRoomDayQoqInfoRsp proto.InternalMessageInfo

func (m *GetAmuseRoomDayQoqInfoRsp) GetStatTime() int64 {
	if m != nil {
		return m.StatTime
	}
	return 0
}

func (m *GetAmuseRoomDayQoqInfoRsp) GetIncome() int64 {
	if m != nil {
		return m.Income
	}
	return 0
}

func (m *GetAmuseRoomDayQoqInfoRsp) GetCompareStatTime() int64 {
	if m != nil {
		return m.CompareStatTime
	}
	return 0
}

func (m *GetAmuseRoomDayQoqInfoRsp) GetCompareIncome() int64 {
	if m != nil {
		return m.CompareIncome
	}
	return 0
}

func (m *GetAmuseRoomDayQoqInfoRsp) GetQoq() float32 {
	if m != nil {
		return m.Qoq
	}
	return 0
}

func (m *GetAmuseRoomDayQoqInfoRsp) GetRoomId() uint32 {
	if m != nil {
		return m.RoomId
	}
	return 0
}

// 获取娱乐房房间收益详情
type GetAmuseChannelDetailReq struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAmuseChannelDetailReq) Reset()         { *m = GetAmuseChannelDetailReq{} }
func (m *GetAmuseChannelDetailReq) String() string { return proto.CompactTextString(m) }
func (*GetAmuseChannelDetailReq) ProtoMessage()    {}
func (*GetAmuseChannelDetailReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_gold_commission_18e8a43e3dce6c6d, []int{2}
}
func (m *GetAmuseChannelDetailReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAmuseChannelDetailReq.Unmarshal(m, b)
}
func (m *GetAmuseChannelDetailReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAmuseChannelDetailReq.Marshal(b, m, deterministic)
}
func (dst *GetAmuseChannelDetailReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAmuseChannelDetailReq.Merge(dst, src)
}
func (m *GetAmuseChannelDetailReq) XXX_Size() int {
	return xxx_messageInfo_GetAmuseChannelDetailReq.Size(m)
}
func (m *GetAmuseChannelDetailReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAmuseChannelDetailReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAmuseChannelDetailReq proto.InternalMessageInfo

func (m *GetAmuseChannelDetailReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

type GetAmuseChannelDetailRsp struct {
	ChannelFees          []*ChannelFee `protobuf:"bytes,1,rep,name=channel_fees,json=channelFees,proto3" json:"channel_fees,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetAmuseChannelDetailRsp) Reset()         { *m = GetAmuseChannelDetailRsp{} }
func (m *GetAmuseChannelDetailRsp) String() string { return proto.CompactTextString(m) }
func (*GetAmuseChannelDetailRsp) ProtoMessage()    {}
func (*GetAmuseChannelDetailRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_gold_commission_18e8a43e3dce6c6d, []int{3}
}
func (m *GetAmuseChannelDetailRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAmuseChannelDetailRsp.Unmarshal(m, b)
}
func (m *GetAmuseChannelDetailRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAmuseChannelDetailRsp.Marshal(b, m, deterministic)
}
func (dst *GetAmuseChannelDetailRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAmuseChannelDetailRsp.Merge(dst, src)
}
func (m *GetAmuseChannelDetailRsp) XXX_Size() int {
	return xxx_messageInfo_GetAmuseChannelDetailRsp.Size(m)
}
func (m *GetAmuseChannelDetailRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAmuseChannelDetailRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAmuseChannelDetailRsp proto.InternalMessageInfo

func (m *GetAmuseChannelDetailRsp) GetChannelFees() []*ChannelFee {
	if m != nil {
		return m.ChannelFees
	}
	return nil
}

// 房间收益
type ChannelFee struct {
	RoomId               uint32   `protobuf:"varint,1,opt,name=room_id,json=roomId,proto3" json:"room_id,omitempty"`
	Fee                  int64    `protobuf:"varint,2,opt,name=fee,proto3" json:"fee,omitempty"`
	MonthLastAndThisQoq  float32  `protobuf:"fixed32,3,opt,name=month_last_and_this_qoq,json=monthLastAndThisQoq,proto3" json:"month_last_and_this_qoq,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelFee) Reset()         { *m = ChannelFee{} }
func (m *ChannelFee) String() string { return proto.CompactTextString(m) }
func (*ChannelFee) ProtoMessage()    {}
func (*ChannelFee) Descriptor() ([]byte, []int) {
	return fileDescriptor_gold_commission_18e8a43e3dce6c6d, []int{4}
}
func (m *ChannelFee) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelFee.Unmarshal(m, b)
}
func (m *ChannelFee) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelFee.Marshal(b, m, deterministic)
}
func (dst *ChannelFee) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelFee.Merge(dst, src)
}
func (m *ChannelFee) XXX_Size() int {
	return xxx_messageInfo_ChannelFee.Size(m)
}
func (m *ChannelFee) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelFee.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelFee proto.InternalMessageInfo

func (m *ChannelFee) GetRoomId() uint32 {
	if m != nil {
		return m.RoomId
	}
	return 0
}

func (m *ChannelFee) GetFee() int64 {
	if m != nil {
		return m.Fee
	}
	return 0
}

func (m *ChannelFee) GetMonthLastAndThisQoq() float32 {
	if m != nil {
		return m.MonthLastAndThisQoq
	}
	return 0
}

//
type GetAmuseIncomeDetailReq struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAmuseIncomeDetailReq) Reset()         { *m = GetAmuseIncomeDetailReq{} }
func (m *GetAmuseIncomeDetailReq) String() string { return proto.CompactTextString(m) }
func (*GetAmuseIncomeDetailReq) ProtoMessage()    {}
func (*GetAmuseIncomeDetailReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_gold_commission_18e8a43e3dce6c6d, []int{5}
}
func (m *GetAmuseIncomeDetailReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAmuseIncomeDetailReq.Unmarshal(m, b)
}
func (m *GetAmuseIncomeDetailReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAmuseIncomeDetailReq.Marshal(b, m, deterministic)
}
func (dst *GetAmuseIncomeDetailReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAmuseIncomeDetailReq.Merge(dst, src)
}
func (m *GetAmuseIncomeDetailReq) XXX_Size() int {
	return xxx_messageInfo_GetAmuseIncomeDetailReq.Size(m)
}
func (m *GetAmuseIncomeDetailReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAmuseIncomeDetailReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAmuseIncomeDetailReq proto.InternalMessageInfo

func (m *GetAmuseIncomeDetailReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

type GetAmuseIncomeDetailRsp struct {
	TodayIncome          int64    `protobuf:"varint,1,opt,name=today_income,json=todayIncome,proto3" json:"today_income,omitempty"`
	YesterdayIncome      int64    `protobuf:"varint,2,opt,name=yesterday_income,json=yesterdayIncome,proto3" json:"yesterday_income,omitempty"`
	ThisMonthIncome      int64    `protobuf:"varint,3,opt,name=this_month_income,json=thisMonthIncome,proto3" json:"this_month_income,omitempty"`
	LastMonthIncome      int64    `protobuf:"varint,4,opt,name=last_month_income,json=lastMonthIncome,proto3" json:"last_month_income,omitempty"`
	DayQoq               float32  `protobuf:"fixed32,5,opt,name=day_qoq,json=dayQoq,proto3" json:"day_qoq,omitempty"`
	MonthQoq             float32  `protobuf:"fixed32,6,opt,name=month_qoq,json=monthQoq,proto3" json:"month_qoq,omitempty"`
	YesterdayQoq         float32  `protobuf:"fixed32,7,opt,name=yesterday_qoq,json=yesterdayQoq,proto3" json:"yesterday_qoq,omitempty"`
	MonthLastAndThisQoq  float32  `protobuf:"fixed32,8,opt,name=month_last_and_this_qoq,json=monthLastAndThisQoq,proto3" json:"month_last_and_this_qoq,omitempty"`
	LastMonthSamePeriod  int64    `protobuf:"varint,9,opt,name=last_month_same_period,json=lastMonthSamePeriod,proto3" json:"last_month_same_period,omitempty"`
	MonthSixIncome       int64    `protobuf:"varint,10,opt,name=month_six_income,json=monthSixIncome,proto3" json:"month_six_income,omitempty"`
	ThisMonthExtraIncome int64    `protobuf:"varint,11,opt,name=this_month_extra_income,json=thisMonthExtraIncome,proto3" json:"this_month_extra_income,omitempty"`
	LastMonthExtraIncome int64    `protobuf:"varint,12,opt,name=last_month_extra_income,json=lastMonthExtraIncome,proto3" json:"last_month_extra_income,omitempty"`
	NotSettleAmuseExtra  bool     `protobuf:"varint,13,opt,name=not_settle_amuse_extra,json=notSettleAmuseExtra,proto3" json:"not_settle_amuse_extra,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAmuseIncomeDetailRsp) Reset()         { *m = GetAmuseIncomeDetailRsp{} }
func (m *GetAmuseIncomeDetailRsp) String() string { return proto.CompactTextString(m) }
func (*GetAmuseIncomeDetailRsp) ProtoMessage()    {}
func (*GetAmuseIncomeDetailRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_gold_commission_18e8a43e3dce6c6d, []int{6}
}
func (m *GetAmuseIncomeDetailRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAmuseIncomeDetailRsp.Unmarshal(m, b)
}
func (m *GetAmuseIncomeDetailRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAmuseIncomeDetailRsp.Marshal(b, m, deterministic)
}
func (dst *GetAmuseIncomeDetailRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAmuseIncomeDetailRsp.Merge(dst, src)
}
func (m *GetAmuseIncomeDetailRsp) XXX_Size() int {
	return xxx_messageInfo_GetAmuseIncomeDetailRsp.Size(m)
}
func (m *GetAmuseIncomeDetailRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAmuseIncomeDetailRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAmuseIncomeDetailRsp proto.InternalMessageInfo

func (m *GetAmuseIncomeDetailRsp) GetTodayIncome() int64 {
	if m != nil {
		return m.TodayIncome
	}
	return 0
}

func (m *GetAmuseIncomeDetailRsp) GetYesterdayIncome() int64 {
	if m != nil {
		return m.YesterdayIncome
	}
	return 0
}

func (m *GetAmuseIncomeDetailRsp) GetThisMonthIncome() int64 {
	if m != nil {
		return m.ThisMonthIncome
	}
	return 0
}

func (m *GetAmuseIncomeDetailRsp) GetLastMonthIncome() int64 {
	if m != nil {
		return m.LastMonthIncome
	}
	return 0
}

func (m *GetAmuseIncomeDetailRsp) GetDayQoq() float32 {
	if m != nil {
		return m.DayQoq
	}
	return 0
}

func (m *GetAmuseIncomeDetailRsp) GetMonthQoq() float32 {
	if m != nil {
		return m.MonthQoq
	}
	return 0
}

func (m *GetAmuseIncomeDetailRsp) GetYesterdayQoq() float32 {
	if m != nil {
		return m.YesterdayQoq
	}
	return 0
}

func (m *GetAmuseIncomeDetailRsp) GetMonthLastAndThisQoq() float32 {
	if m != nil {
		return m.MonthLastAndThisQoq
	}
	return 0
}

func (m *GetAmuseIncomeDetailRsp) GetLastMonthSamePeriod() int64 {
	if m != nil {
		return m.LastMonthSamePeriod
	}
	return 0
}

func (m *GetAmuseIncomeDetailRsp) GetMonthSixIncome() int64 {
	if m != nil {
		return m.MonthSixIncome
	}
	return 0
}

func (m *GetAmuseIncomeDetailRsp) GetThisMonthExtraIncome() int64 {
	if m != nil {
		return m.ThisMonthExtraIncome
	}
	return 0
}

func (m *GetAmuseIncomeDetailRsp) GetLastMonthExtraIncome() int64 {
	if m != nil {
		return m.LastMonthExtraIncome
	}
	return 0
}

func (m *GetAmuseIncomeDetailRsp) GetNotSettleAmuseExtra() bool {
	if m != nil {
		return m.NotSettleAmuseExtra
	}
	return false
}

// 增长详情
type StatIncomeInfo struct {
	StatTime             int64    `protobuf:"varint,1,opt,name=stat_time,json=statTime,proto3" json:"stat_time,omitempty"`
	Members              int64    `protobuf:"varint,2,opt,name=members,proto3" json:"members,omitempty"`
	Incomes              int64    `protobuf:"varint,3,opt,name=incomes,proto3" json:"incomes,omitempty"`
	Fees                 int64    `protobuf:"varint,4,opt,name=fees,proto3" json:"fees,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StatIncomeInfo) Reset()         { *m = StatIncomeInfo{} }
func (m *StatIncomeInfo) String() string { return proto.CompactTextString(m) }
func (*StatIncomeInfo) ProtoMessage()    {}
func (*StatIncomeInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_gold_commission_18e8a43e3dce6c6d, []int{7}
}
func (m *StatIncomeInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StatIncomeInfo.Unmarshal(m, b)
}
func (m *StatIncomeInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StatIncomeInfo.Marshal(b, m, deterministic)
}
func (dst *StatIncomeInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StatIncomeInfo.Merge(dst, src)
}
func (m *StatIncomeInfo) XXX_Size() int {
	return xxx_messageInfo_StatIncomeInfo.Size(m)
}
func (m *StatIncomeInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_StatIncomeInfo.DiscardUnknown(m)
}

var xxx_messageInfo_StatIncomeInfo proto.InternalMessageInfo

func (m *StatIncomeInfo) GetStatTime() int64 {
	if m != nil {
		return m.StatTime
	}
	return 0
}

func (m *StatIncomeInfo) GetMembers() int64 {
	if m != nil {
		return m.Members
	}
	return 0
}

func (m *StatIncomeInfo) GetIncomes() int64 {
	if m != nil {
		return m.Incomes
	}
	return 0
}

func (m *StatIncomeInfo) GetFees() int64 {
	if m != nil {
		return m.Fees
	}
	return 0
}

// 获取娱乐房今日收益详情
type GetAmuseGuildRoomIncomeListReq struct {
	GuildId              uint32    `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	Offset               uint32    `protobuf:"varint,2,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32    `protobuf:"varint,3,opt,name=limit,proto3" json:"limit,omitempty"`
	RangeType            RangeType `protobuf:"varint,4,opt,name=range_type,json=rangeType,proto3,enum=gold_commission.RangeType" json:"range_type,omitempty"`
	BeginTime            int64     `protobuf:"varint,5,opt,name=begin_time,json=beginTime,proto3" json:"begin_time,omitempty"`
	EndTime              int64     `protobuf:"varint,6,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	RoomId               uint32    `protobuf:"varint,7,opt,name=room_id,json=roomId,proto3" json:"room_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *GetAmuseGuildRoomIncomeListReq) Reset()         { *m = GetAmuseGuildRoomIncomeListReq{} }
func (m *GetAmuseGuildRoomIncomeListReq) String() string { return proto.CompactTextString(m) }
func (*GetAmuseGuildRoomIncomeListReq) ProtoMessage()    {}
func (*GetAmuseGuildRoomIncomeListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_gold_commission_18e8a43e3dce6c6d, []int{8}
}
func (m *GetAmuseGuildRoomIncomeListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAmuseGuildRoomIncomeListReq.Unmarshal(m, b)
}
func (m *GetAmuseGuildRoomIncomeListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAmuseGuildRoomIncomeListReq.Marshal(b, m, deterministic)
}
func (dst *GetAmuseGuildRoomIncomeListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAmuseGuildRoomIncomeListReq.Merge(dst, src)
}
func (m *GetAmuseGuildRoomIncomeListReq) XXX_Size() int {
	return xxx_messageInfo_GetAmuseGuildRoomIncomeListReq.Size(m)
}
func (m *GetAmuseGuildRoomIncomeListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAmuseGuildRoomIncomeListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAmuseGuildRoomIncomeListReq proto.InternalMessageInfo

func (m *GetAmuseGuildRoomIncomeListReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GetAmuseGuildRoomIncomeListReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetAmuseGuildRoomIncomeListReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *GetAmuseGuildRoomIncomeListReq) GetRangeType() RangeType {
	if m != nil {
		return m.RangeType
	}
	return RangeType_DAY_RANGE_TYPE
}

func (m *GetAmuseGuildRoomIncomeListReq) GetBeginTime() int64 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *GetAmuseGuildRoomIncomeListReq) GetEndTime() int64 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *GetAmuseGuildRoomIncomeListReq) GetRoomId() uint32 {
	if m != nil {
		return m.RoomId
	}
	return 0
}

type GetAmuseGuildRoomIncomeListRsp struct {
	TotalFees            int64             `protobuf:"varint,1,opt,name=total_fees,json=totalFees,proto3" json:"total_fees,omitempty"`
	TotalIncomes         int64             `protobuf:"varint,2,opt,name=total_incomes,json=totalIncomes,proto3" json:"total_incomes,omitempty"`
	NextPage             bool              `protobuf:"varint,3,opt,name=next_page,json=nextPage,proto3" json:"next_page,omitempty"`
	StatIncomeInfo       []*StatIncomeInfo `protobuf:"bytes,4,rep,name=stat_income_info,json=statIncomeInfo,proto3" json:"stat_income_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetAmuseGuildRoomIncomeListRsp) Reset()         { *m = GetAmuseGuildRoomIncomeListRsp{} }
func (m *GetAmuseGuildRoomIncomeListRsp) String() string { return proto.CompactTextString(m) }
func (*GetAmuseGuildRoomIncomeListRsp) ProtoMessage()    {}
func (*GetAmuseGuildRoomIncomeListRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_gold_commission_18e8a43e3dce6c6d, []int{9}
}
func (m *GetAmuseGuildRoomIncomeListRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAmuseGuildRoomIncomeListRsp.Unmarshal(m, b)
}
func (m *GetAmuseGuildRoomIncomeListRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAmuseGuildRoomIncomeListRsp.Marshal(b, m, deterministic)
}
func (dst *GetAmuseGuildRoomIncomeListRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAmuseGuildRoomIncomeListRsp.Merge(dst, src)
}
func (m *GetAmuseGuildRoomIncomeListRsp) XXX_Size() int {
	return xxx_messageInfo_GetAmuseGuildRoomIncomeListRsp.Size(m)
}
func (m *GetAmuseGuildRoomIncomeListRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAmuseGuildRoomIncomeListRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAmuseGuildRoomIncomeListRsp proto.InternalMessageInfo

func (m *GetAmuseGuildRoomIncomeListRsp) GetTotalFees() int64 {
	if m != nil {
		return m.TotalFees
	}
	return 0
}

func (m *GetAmuseGuildRoomIncomeListRsp) GetTotalIncomes() int64 {
	if m != nil {
		return m.TotalIncomes
	}
	return 0
}

func (m *GetAmuseGuildRoomIncomeListRsp) GetNextPage() bool {
	if m != nil {
		return m.NextPage
	}
	return false
}

func (m *GetAmuseGuildRoomIncomeListRsp) GetStatIncomeInfo() []*StatIncomeInfo {
	if m != nil {
		return m.StatIncomeInfo
	}
	return nil
}

type SettlementYuyinGoldReq struct {
	Certain              bool     `protobuf:"varint,1,opt,name=certain,proto3" json:"certain,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SettlementYuyinGoldReq) Reset()         { *m = SettlementYuyinGoldReq{} }
func (m *SettlementYuyinGoldReq) String() string { return proto.CompactTextString(m) }
func (*SettlementYuyinGoldReq) ProtoMessage()    {}
func (*SettlementYuyinGoldReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_gold_commission_18e8a43e3dce6c6d, []int{10}
}
func (m *SettlementYuyinGoldReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SettlementYuyinGoldReq.Unmarshal(m, b)
}
func (m *SettlementYuyinGoldReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SettlementYuyinGoldReq.Marshal(b, m, deterministic)
}
func (dst *SettlementYuyinGoldReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SettlementYuyinGoldReq.Merge(dst, src)
}
func (m *SettlementYuyinGoldReq) XXX_Size() int {
	return xxx_messageInfo_SettlementYuyinGoldReq.Size(m)
}
func (m *SettlementYuyinGoldReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SettlementYuyinGoldReq.DiscardUnknown(m)
}

var xxx_messageInfo_SettlementYuyinGoldReq proto.InternalMessageInfo

func (m *SettlementYuyinGoldReq) GetCertain() bool {
	if m != nil {
		return m.Certain
	}
	return false
}

type SettlementYuyinGoldResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SettlementYuyinGoldResp) Reset()         { *m = SettlementYuyinGoldResp{} }
func (m *SettlementYuyinGoldResp) String() string { return proto.CompactTextString(m) }
func (*SettlementYuyinGoldResp) ProtoMessage()    {}
func (*SettlementYuyinGoldResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_gold_commission_18e8a43e3dce6c6d, []int{11}
}
func (m *SettlementYuyinGoldResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SettlementYuyinGoldResp.Unmarshal(m, b)
}
func (m *SettlementYuyinGoldResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SettlementYuyinGoldResp.Marshal(b, m, deterministic)
}
func (dst *SettlementYuyinGoldResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SettlementYuyinGoldResp.Merge(dst, src)
}
func (m *SettlementYuyinGoldResp) XXX_Size() int {
	return xxx_messageInfo_SettlementYuyinGoldResp.Size(m)
}
func (m *SettlementYuyinGoldResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SettlementYuyinGoldResp.DiscardUnknown(m)
}

var xxx_messageInfo_SettlementYuyinGoldResp proto.InternalMessageInfo

// 主动推送语音额外收益统计报表
type GenYuyinExtraReportReq struct {
	IsDaily              bool     `protobuf:"varint,1,opt,name=is_daily,json=isDaily,proto3" json:"is_daily,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GenYuyinExtraReportReq) Reset()         { *m = GenYuyinExtraReportReq{} }
func (m *GenYuyinExtraReportReq) String() string { return proto.CompactTextString(m) }
func (*GenYuyinExtraReportReq) ProtoMessage()    {}
func (*GenYuyinExtraReportReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_gold_commission_18e8a43e3dce6c6d, []int{12}
}
func (m *GenYuyinExtraReportReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GenYuyinExtraReportReq.Unmarshal(m, b)
}
func (m *GenYuyinExtraReportReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GenYuyinExtraReportReq.Marshal(b, m, deterministic)
}
func (dst *GenYuyinExtraReportReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GenYuyinExtraReportReq.Merge(dst, src)
}
func (m *GenYuyinExtraReportReq) XXX_Size() int {
	return xxx_messageInfo_GenYuyinExtraReportReq.Size(m)
}
func (m *GenYuyinExtraReportReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GenYuyinExtraReportReq.DiscardUnknown(m)
}

var xxx_messageInfo_GenYuyinExtraReportReq proto.InternalMessageInfo

func (m *GenYuyinExtraReportReq) GetIsDaily() bool {
	if m != nil {
		return m.IsDaily
	}
	return false
}

type GenYuyinExtraReportResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GenYuyinExtraReportResp) Reset()         { *m = GenYuyinExtraReportResp{} }
func (m *GenYuyinExtraReportResp) String() string { return proto.CompactTextString(m) }
func (*GenYuyinExtraReportResp) ProtoMessage()    {}
func (*GenYuyinExtraReportResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_gold_commission_18e8a43e3dce6c6d, []int{13}
}
func (m *GenYuyinExtraReportResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GenYuyinExtraReportResp.Unmarshal(m, b)
}
func (m *GenYuyinExtraReportResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GenYuyinExtraReportResp.Marshal(b, m, deterministic)
}
func (dst *GenYuyinExtraReportResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GenYuyinExtraReportResp.Merge(dst, src)
}
func (m *GenYuyinExtraReportResp) XXX_Size() int {
	return xxx_messageInfo_GenYuyinExtraReportResp.Size(m)
}
func (m *GenYuyinExtraReportResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GenYuyinExtraReportResp.DiscardUnknown(m)
}

var xxx_messageInfo_GenYuyinExtraReportResp proto.InternalMessageInfo

type AwardGoldOrder struct {
	GuildId              uint32      `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	OrderId              string      `protobuf:"bytes,2,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	Fee                  uint64      `protobuf:"varint,3,opt,name=fee,proto3" json:"fee,omitempty"`
	Income               uint64      `protobuf:"varint,4,opt,name=income,proto3" json:"income,omitempty"`
	ChannelId            uint32      `protobuf:"varint,5,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	TargetUid            uint32      `protobuf:"varint,6,opt,name=target_uid,json=targetUid,proto3" json:"target_uid,omitempty"`
	PaidUid              uint32      `protobuf:"varint,7,opt,name=paid_uid,json=paidUid,proto3" json:"paid_uid,omitempty"`
	BoughtTime           uint32      `protobuf:"varint,8,opt,name=bought_time,json=boughtTime,proto3" json:"bought_time,omitempty"`
	Extend               string      `protobuf:"bytes,9,opt,name=extend,proto3" json:"extend,omitempty"`
	SourceType           SourceType  `protobuf:"varint,10,opt,name=source_type,json=sourceType,proto3,enum=gold_commission.SourceType" json:"source_type,omitempty"`
	IsReconcileOrder     bool        `protobuf:"varint,11,opt,name=is_reconcile_order,json=isReconcileOrder,proto3" json:"is_reconcile_order,omitempty"`
	DealToken            string      `protobuf:"bytes,12,opt,name=deal_token,json=dealToken,proto3" json:"deal_token,omitempty"`
	IsUkwPaid            bool        `protobuf:"varint,13,opt,name=is_ukw_paid,json=isUkwPaid,proto3" json:"is_ukw_paid,omitempty"`
	IsUkwTarget          bool        `protobuf:"varint,14,opt,name=is_ukw_target,json=isUkwTarget,proto3" json:"is_ukw_target,omitempty"`
	ChannelType          ChannelType `protobuf:"varint,15,opt,name=channelType,proto3,enum=gold_commission.ChannelType" json:"channelType,omitempty"`
	UkwPaidAccount       string      `protobuf:"bytes,16,opt,name=ukw_paid_account,json=ukwPaidAccount,proto3" json:"ukw_paid_account,omitempty"`
	UkwTargetAccount     string      `protobuf:"bytes,17,opt,name=ukw_target_account,json=ukwTargetAccount,proto3" json:"ukw_target_account,omitempty"`
	GoldType             GoldType    `protobuf:"varint,18,opt,name=gold_type,json=goldType,proto3,enum=gold_commission.GoldType" json:"gold_type,omitempty"`
	IsVirtualLive        bool        `protobuf:"varint,19,opt,name=is_virtual_live,json=isVirtualLive,proto3" json:"is_virtual_live,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *AwardGoldOrder) Reset()         { *m = AwardGoldOrder{} }
func (m *AwardGoldOrder) String() string { return proto.CompactTextString(m) }
func (*AwardGoldOrder) ProtoMessage()    {}
func (*AwardGoldOrder) Descriptor() ([]byte, []int) {
	return fileDescriptor_gold_commission_18e8a43e3dce6c6d, []int{14}
}
func (m *AwardGoldOrder) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AwardGoldOrder.Unmarshal(m, b)
}
func (m *AwardGoldOrder) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AwardGoldOrder.Marshal(b, m, deterministic)
}
func (dst *AwardGoldOrder) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AwardGoldOrder.Merge(dst, src)
}
func (m *AwardGoldOrder) XXX_Size() int {
	return xxx_messageInfo_AwardGoldOrder.Size(m)
}
func (m *AwardGoldOrder) XXX_DiscardUnknown() {
	xxx_messageInfo_AwardGoldOrder.DiscardUnknown(m)
}

var xxx_messageInfo_AwardGoldOrder proto.InternalMessageInfo

func (m *AwardGoldOrder) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *AwardGoldOrder) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *AwardGoldOrder) GetFee() uint64 {
	if m != nil {
		return m.Fee
	}
	return 0
}

func (m *AwardGoldOrder) GetIncome() uint64 {
	if m != nil {
		return m.Income
	}
	return 0
}

func (m *AwardGoldOrder) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *AwardGoldOrder) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

func (m *AwardGoldOrder) GetPaidUid() uint32 {
	if m != nil {
		return m.PaidUid
	}
	return 0
}

func (m *AwardGoldOrder) GetBoughtTime() uint32 {
	if m != nil {
		return m.BoughtTime
	}
	return 0
}

func (m *AwardGoldOrder) GetExtend() string {
	if m != nil {
		return m.Extend
	}
	return ""
}

func (m *AwardGoldOrder) GetSourceType() SourceType {
	if m != nil {
		return m.SourceType
	}
	return SourceType_UnknownType
}

func (m *AwardGoldOrder) GetIsReconcileOrder() bool {
	if m != nil {
		return m.IsReconcileOrder
	}
	return false
}

func (m *AwardGoldOrder) GetDealToken() string {
	if m != nil {
		return m.DealToken
	}
	return ""
}

func (m *AwardGoldOrder) GetIsUkwPaid() bool {
	if m != nil {
		return m.IsUkwPaid
	}
	return false
}

func (m *AwardGoldOrder) GetIsUkwTarget() bool {
	if m != nil {
		return m.IsUkwTarget
	}
	return false
}

func (m *AwardGoldOrder) GetChannelType() ChannelType {
	if m != nil {
		return m.ChannelType
	}
	return ChannelType_CTypeInvalid
}

func (m *AwardGoldOrder) GetUkwPaidAccount() string {
	if m != nil {
		return m.UkwPaidAccount
	}
	return ""
}

func (m *AwardGoldOrder) GetUkwTargetAccount() string {
	if m != nil {
		return m.UkwTargetAccount
	}
	return ""
}

func (m *AwardGoldOrder) GetGoldType() GoldType {
	if m != nil {
		return m.GoldType
	}
	return GoldType_UNKNOWN_GOLD
}

func (m *AwardGoldOrder) GetIsVirtualLive() bool {
	if m != nil {
		return m.IsVirtualLive
	}
	return false
}

type BaseAnchorInfo struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	AnchorId             uint32   `protobuf:"varint,2,opt,name=anchor_id,json=anchorId,proto3" json:"anchor_id,omitempty"`
	Date                 uint32   `protobuf:"varint,3,opt,name=date,proto3" json:"date,omitempty"`
	PaidUidCnt           uint32   `protobuf:"varint,4,opt,name=paid_uid_cnt,json=paidUidCnt,proto3" json:"paid_uid_cnt,omitempty"`
	PaidUid              uint32   `protobuf:"varint,5,opt,name=paid_uid,json=paidUid,proto3" json:"paid_uid,omitempty"`
	PaidUkwAccount       string   `protobuf:"bytes,6,opt,name=paid_ukw_account,json=paidUkwAccount,proto3" json:"paid_ukw_account,omitempty"`
	Fee                  uint64   `protobuf:"varint,10,opt,name=fee,proto3" json:"fee,omitempty"`
	Income               uint64   `protobuf:"varint,11,opt,name=income,proto3" json:"income,omitempty"`
	PresentFee           uint64   `protobuf:"varint,12,opt,name=present_fee,json=presentFee,proto3" json:"present_fee,omitempty"`
	KnightFee            uint64   `protobuf:"varint,14,opt,name=knight_fee,json=knightFee,proto3" json:"knight_fee,omitempty"`
	InteractGameFee      uint64   `protobuf:"varint,15,opt,name=interact_game_fee,json=interactGameFee,proto3" json:"interact_game_fee,omitempty"`
	InteractGameDur      uint64   `protobuf:"varint,16,opt,name=interact_game_dur,json=interactGameDur,proto3" json:"interact_game_dur,omitempty"`
	EsportFee            uint64   `protobuf:"varint,17,opt,name=esport_fee,json=esportFee,proto3" json:"esport_fee,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BaseAnchorInfo) Reset()         { *m = BaseAnchorInfo{} }
func (m *BaseAnchorInfo) String() string { return proto.CompactTextString(m) }
func (*BaseAnchorInfo) ProtoMessage()    {}
func (*BaseAnchorInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_gold_commission_18e8a43e3dce6c6d, []int{15}
}
func (m *BaseAnchorInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BaseAnchorInfo.Unmarshal(m, b)
}
func (m *BaseAnchorInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BaseAnchorInfo.Marshal(b, m, deterministic)
}
func (dst *BaseAnchorInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BaseAnchorInfo.Merge(dst, src)
}
func (m *BaseAnchorInfo) XXX_Size() int {
	return xxx_messageInfo_BaseAnchorInfo.Size(m)
}
func (m *BaseAnchorInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_BaseAnchorInfo.DiscardUnknown(m)
}

var xxx_messageInfo_BaseAnchorInfo proto.InternalMessageInfo

func (m *BaseAnchorInfo) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *BaseAnchorInfo) GetAnchorId() uint32 {
	if m != nil {
		return m.AnchorId
	}
	return 0
}

func (m *BaseAnchorInfo) GetDate() uint32 {
	if m != nil {
		return m.Date
	}
	return 0
}

func (m *BaseAnchorInfo) GetPaidUidCnt() uint32 {
	if m != nil {
		return m.PaidUidCnt
	}
	return 0
}

func (m *BaseAnchorInfo) GetPaidUid() uint32 {
	if m != nil {
		return m.PaidUid
	}
	return 0
}

func (m *BaseAnchorInfo) GetPaidUkwAccount() string {
	if m != nil {
		return m.PaidUkwAccount
	}
	return ""
}

func (m *BaseAnchorInfo) GetFee() uint64 {
	if m != nil {
		return m.Fee
	}
	return 0
}

func (m *BaseAnchorInfo) GetIncome() uint64 {
	if m != nil {
		return m.Income
	}
	return 0
}

func (m *BaseAnchorInfo) GetPresentFee() uint64 {
	if m != nil {
		return m.PresentFee
	}
	return 0
}

func (m *BaseAnchorInfo) GetKnightFee() uint64 {
	if m != nil {
		return m.KnightFee
	}
	return 0
}

func (m *BaseAnchorInfo) GetInteractGameFee() uint64 {
	if m != nil {
		return m.InteractGameFee
	}
	return 0
}

func (m *BaseAnchorInfo) GetInteractGameDur() uint64 {
	if m != nil {
		return m.InteractGameDur
	}
	return 0
}

func (m *BaseAnchorInfo) GetEsportFee() uint64 {
	if m != nil {
		return m.EsportFee
	}
	return 0
}

type BaseChannelInfo struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Date                 uint32   `protobuf:"varint,3,opt,name=date,proto3" json:"date,omitempty"`
	PaidUidCnt           uint32   `protobuf:"varint,4,opt,name=paid_uid_cnt,json=paidUidCnt,proto3" json:"paid_uid_cnt,omitempty"`
	PaidUid              uint32   `protobuf:"varint,5,opt,name=paid_uid,json=paidUid,proto3" json:"paid_uid,omitempty"`
	PaidUkwAccount       string   `protobuf:"bytes,7,opt,name=paid_ukw_account,json=paidUkwAccount,proto3" json:"paid_ukw_account,omitempty"`
	Fee                  uint64   `protobuf:"varint,10,opt,name=fee,proto3" json:"fee,omitempty"`
	Income               uint64   `protobuf:"varint,11,opt,name=income,proto3" json:"income,omitempty"`
	PresentFee           uint64   `protobuf:"varint,12,opt,name=present_fee,json=presentFee,proto3" json:"present_fee,omitempty"`
	KnightFee            uint64   `protobuf:"varint,14,opt,name=knight_fee,json=knightFee,proto3" json:"knight_fee,omitempty"`
	WerewolfFee          uint64   `protobuf:"varint,15,opt,name=werewolf_fee,json=werewolfFee,proto3" json:"werewolf_fee,omitempty"`
	InteractGameFee      uint64   `protobuf:"varint,16,opt,name=interact_game_fee,json=interactGameFee,proto3" json:"interact_game_fee,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BaseChannelInfo) Reset()         { *m = BaseChannelInfo{} }
func (m *BaseChannelInfo) String() string { return proto.CompactTextString(m) }
func (*BaseChannelInfo) ProtoMessage()    {}
func (*BaseChannelInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_gold_commission_18e8a43e3dce6c6d, []int{16}
}
func (m *BaseChannelInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BaseChannelInfo.Unmarshal(m, b)
}
func (m *BaseChannelInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BaseChannelInfo.Marshal(b, m, deterministic)
}
func (dst *BaseChannelInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BaseChannelInfo.Merge(dst, src)
}
func (m *BaseChannelInfo) XXX_Size() int {
	return xxx_messageInfo_BaseChannelInfo.Size(m)
}
func (m *BaseChannelInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_BaseChannelInfo.DiscardUnknown(m)
}

var xxx_messageInfo_BaseChannelInfo proto.InternalMessageInfo

func (m *BaseChannelInfo) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *BaseChannelInfo) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *BaseChannelInfo) GetDate() uint32 {
	if m != nil {
		return m.Date
	}
	return 0
}

func (m *BaseChannelInfo) GetPaidUidCnt() uint32 {
	if m != nil {
		return m.PaidUidCnt
	}
	return 0
}

func (m *BaseChannelInfo) GetPaidUid() uint32 {
	if m != nil {
		return m.PaidUid
	}
	return 0
}

func (m *BaseChannelInfo) GetPaidUkwAccount() string {
	if m != nil {
		return m.PaidUkwAccount
	}
	return ""
}

func (m *BaseChannelInfo) GetFee() uint64 {
	if m != nil {
		return m.Fee
	}
	return 0
}

func (m *BaseChannelInfo) GetIncome() uint64 {
	if m != nil {
		return m.Income
	}
	return 0
}

func (m *BaseChannelInfo) GetPresentFee() uint64 {
	if m != nil {
		return m.PresentFee
	}
	return 0
}

func (m *BaseChannelInfo) GetKnightFee() uint64 {
	if m != nil {
		return m.KnightFee
	}
	return 0
}

func (m *BaseChannelInfo) GetWerewolfFee() uint64 {
	if m != nil {
		return m.WerewolfFee
	}
	return 0
}

func (m *BaseChannelInfo) GetInteractGameFee() uint64 {
	if m != nil {
		return m.InteractGameFee
	}
	return 0
}

type BaseGuildInfo struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	Date                 uint32   `protobuf:"varint,3,opt,name=date,proto3" json:"date,omitempty"`
	PaidUidCnt           uint32   `protobuf:"varint,4,opt,name=paid_uid_cnt,json=paidUidCnt,proto3" json:"paid_uid_cnt,omitempty"`
	Fee                  uint64   `protobuf:"varint,10,opt,name=fee,proto3" json:"fee,omitempty"`
	Income               uint64   `protobuf:"varint,11,opt,name=income,proto3" json:"income,omitempty"`
	PresentFee           uint64   `protobuf:"varint,12,opt,name=present_fee,json=presentFee,proto3" json:"present_fee,omitempty"`
	KnightFee            uint64   `protobuf:"varint,14,opt,name=knight_fee,json=knightFee,proto3" json:"knight_fee,omitempty"`
	WerewolfFee          uint64   `protobuf:"varint,15,opt,name=werewolf_fee,json=werewolfFee,proto3" json:"werewolf_fee,omitempty"`
	InteractGameFee      uint64   `protobuf:"varint,16,opt,name=interact_game_fee,json=interactGameFee,proto3" json:"interact_game_fee,omitempty"`
	EsportFee            uint64   `protobuf:"varint,17,opt,name=esport_fee,json=esportFee,proto3" json:"esport_fee,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BaseGuildInfo) Reset()         { *m = BaseGuildInfo{} }
func (m *BaseGuildInfo) String() string { return proto.CompactTextString(m) }
func (*BaseGuildInfo) ProtoMessage()    {}
func (*BaseGuildInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_gold_commission_18e8a43e3dce6c6d, []int{17}
}
func (m *BaseGuildInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BaseGuildInfo.Unmarshal(m, b)
}
func (m *BaseGuildInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BaseGuildInfo.Marshal(b, m, deterministic)
}
func (dst *BaseGuildInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BaseGuildInfo.Merge(dst, src)
}
func (m *BaseGuildInfo) XXX_Size() int {
	return xxx_messageInfo_BaseGuildInfo.Size(m)
}
func (m *BaseGuildInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_BaseGuildInfo.DiscardUnknown(m)
}

var xxx_messageInfo_BaseGuildInfo proto.InternalMessageInfo

func (m *BaseGuildInfo) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *BaseGuildInfo) GetDate() uint32 {
	if m != nil {
		return m.Date
	}
	return 0
}

func (m *BaseGuildInfo) GetPaidUidCnt() uint32 {
	if m != nil {
		return m.PaidUidCnt
	}
	return 0
}

func (m *BaseGuildInfo) GetFee() uint64 {
	if m != nil {
		return m.Fee
	}
	return 0
}

func (m *BaseGuildInfo) GetIncome() uint64 {
	if m != nil {
		return m.Income
	}
	return 0
}

func (m *BaseGuildInfo) GetPresentFee() uint64 {
	if m != nil {
		return m.PresentFee
	}
	return 0
}

func (m *BaseGuildInfo) GetKnightFee() uint64 {
	if m != nil {
		return m.KnightFee
	}
	return 0
}

func (m *BaseGuildInfo) GetWerewolfFee() uint64 {
	if m != nil {
		return m.WerewolfFee
	}
	return 0
}

func (m *BaseGuildInfo) GetInteractGameFee() uint64 {
	if m != nil {
		return m.InteractGameFee
	}
	return 0
}

func (m *BaseGuildInfo) GetEsportFee() uint64 {
	if m != nil {
		return m.EsportFee
	}
	return 0
}

type GetGuildsYuyinAnchorStatReq struct {
	GuildIds             []uint32        `protobuf:"varint,1,rep,packed,name=guild_ids,json=guildIds,proto3" json:"guild_ids,omitempty"`
	BeginTime            uint32          `protobuf:"varint,2,opt,name=begin_time,json=beginTime,proto3" json:"begin_time,omitempty"`
	EndTime              uint32          `protobuf:"varint,3,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	SettleStatus         ReqSettleStatus `protobuf:"varint,4,opt,name=settle_status,json=settleStatus,proto3,enum=gold_commission.ReqSettleStatus" json:"settle_status,omitempty"`
	Unit                 TimeFilterUnit  `protobuf:"varint,5,opt,name=unit,proto3,enum=gold_commission.TimeFilterUnit" json:"unit,omitempty"`
	Offset               uint32          `protobuf:"varint,6,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32          `protobuf:"varint,7,opt,name=limit,proto3" json:"limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *GetGuildsYuyinAnchorStatReq) Reset()         { *m = GetGuildsYuyinAnchorStatReq{} }
func (m *GetGuildsYuyinAnchorStatReq) String() string { return proto.CompactTextString(m) }
func (*GetGuildsYuyinAnchorStatReq) ProtoMessage()    {}
func (*GetGuildsYuyinAnchorStatReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_gold_commission_18e8a43e3dce6c6d, []int{18}
}
func (m *GetGuildsYuyinAnchorStatReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGuildsYuyinAnchorStatReq.Unmarshal(m, b)
}
func (m *GetGuildsYuyinAnchorStatReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGuildsYuyinAnchorStatReq.Marshal(b, m, deterministic)
}
func (dst *GetGuildsYuyinAnchorStatReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGuildsYuyinAnchorStatReq.Merge(dst, src)
}
func (m *GetGuildsYuyinAnchorStatReq) XXX_Size() int {
	return xxx_messageInfo_GetGuildsYuyinAnchorStatReq.Size(m)
}
func (m *GetGuildsYuyinAnchorStatReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGuildsYuyinAnchorStatReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetGuildsYuyinAnchorStatReq proto.InternalMessageInfo

func (m *GetGuildsYuyinAnchorStatReq) GetGuildIds() []uint32 {
	if m != nil {
		return m.GuildIds
	}
	return nil
}

func (m *GetGuildsYuyinAnchorStatReq) GetBeginTime() uint32 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *GetGuildsYuyinAnchorStatReq) GetEndTime() uint32 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *GetGuildsYuyinAnchorStatReq) GetSettleStatus() ReqSettleStatus {
	if m != nil {
		return m.SettleStatus
	}
	return ReqSettleStatus_ReqSettleStatusALL
}

func (m *GetGuildsYuyinAnchorStatReq) GetUnit() TimeFilterUnit {
	if m != nil {
		return m.Unit
	}
	return TimeFilterUnit_BY_DAY
}

func (m *GetGuildsYuyinAnchorStatReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetGuildsYuyinAnchorStatReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type GetGuildsYuyinAnchorStatResp struct {
	AnchorList           []*BaseAnchorInfo `protobuf:"bytes,1,rep,name=anchorList,proto3" json:"anchorList,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetGuildsYuyinAnchorStatResp) Reset()         { *m = GetGuildsYuyinAnchorStatResp{} }
func (m *GetGuildsYuyinAnchorStatResp) String() string { return proto.CompactTextString(m) }
func (*GetGuildsYuyinAnchorStatResp) ProtoMessage()    {}
func (*GetGuildsYuyinAnchorStatResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_gold_commission_18e8a43e3dce6c6d, []int{19}
}
func (m *GetGuildsYuyinAnchorStatResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGuildsYuyinAnchorStatResp.Unmarshal(m, b)
}
func (m *GetGuildsYuyinAnchorStatResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGuildsYuyinAnchorStatResp.Marshal(b, m, deterministic)
}
func (dst *GetGuildsYuyinAnchorStatResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGuildsYuyinAnchorStatResp.Merge(dst, src)
}
func (m *GetGuildsYuyinAnchorStatResp) XXX_Size() int {
	return xxx_messageInfo_GetGuildsYuyinAnchorStatResp.Size(m)
}
func (m *GetGuildsYuyinAnchorStatResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGuildsYuyinAnchorStatResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetGuildsYuyinAnchorStatResp proto.InternalMessageInfo

func (m *GetGuildsYuyinAnchorStatResp) GetAnchorList() []*BaseAnchorInfo {
	if m != nil {
		return m.AnchorList
	}
	return nil
}

type GetGuildsAmuseChannelStatReq struct {
	GuildIds             []uint32        `protobuf:"varint,1,rep,packed,name=guild_ids,json=guildIds,proto3" json:"guild_ids,omitempty"`
	BeginTime            uint32          `protobuf:"varint,2,opt,name=begin_time,json=beginTime,proto3" json:"begin_time,omitempty"`
	EndTime              uint32          `protobuf:"varint,3,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	SettleStatus         ReqSettleStatus `protobuf:"varint,4,opt,name=settle_status,json=settleStatus,proto3,enum=gold_commission.ReqSettleStatus" json:"settle_status,omitempty"`
	Unit                 TimeFilterUnit  `protobuf:"varint,5,opt,name=unit,proto3,enum=gold_commission.TimeFilterUnit" json:"unit,omitempty"`
	Offset               uint32          `protobuf:"varint,6,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32          `protobuf:"varint,7,opt,name=limit,proto3" json:"limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *GetGuildsAmuseChannelStatReq) Reset()         { *m = GetGuildsAmuseChannelStatReq{} }
func (m *GetGuildsAmuseChannelStatReq) String() string { return proto.CompactTextString(m) }
func (*GetGuildsAmuseChannelStatReq) ProtoMessage()    {}
func (*GetGuildsAmuseChannelStatReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_gold_commission_18e8a43e3dce6c6d, []int{20}
}
func (m *GetGuildsAmuseChannelStatReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGuildsAmuseChannelStatReq.Unmarshal(m, b)
}
func (m *GetGuildsAmuseChannelStatReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGuildsAmuseChannelStatReq.Marshal(b, m, deterministic)
}
func (dst *GetGuildsAmuseChannelStatReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGuildsAmuseChannelStatReq.Merge(dst, src)
}
func (m *GetGuildsAmuseChannelStatReq) XXX_Size() int {
	return xxx_messageInfo_GetGuildsAmuseChannelStatReq.Size(m)
}
func (m *GetGuildsAmuseChannelStatReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGuildsAmuseChannelStatReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetGuildsAmuseChannelStatReq proto.InternalMessageInfo

func (m *GetGuildsAmuseChannelStatReq) GetGuildIds() []uint32 {
	if m != nil {
		return m.GuildIds
	}
	return nil
}

func (m *GetGuildsAmuseChannelStatReq) GetBeginTime() uint32 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *GetGuildsAmuseChannelStatReq) GetEndTime() uint32 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *GetGuildsAmuseChannelStatReq) GetSettleStatus() ReqSettleStatus {
	if m != nil {
		return m.SettleStatus
	}
	return ReqSettleStatus_ReqSettleStatusALL
}

func (m *GetGuildsAmuseChannelStatReq) GetUnit() TimeFilterUnit {
	if m != nil {
		return m.Unit
	}
	return TimeFilterUnit_BY_DAY
}

func (m *GetGuildsAmuseChannelStatReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetGuildsAmuseChannelStatReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type GetGuildsAmuseChannelStatResp struct {
	ChannelList          []*BaseChannelInfo `protobuf:"bytes,1,rep,name=channelList,proto3" json:"channelList,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *GetGuildsAmuseChannelStatResp) Reset()         { *m = GetGuildsAmuseChannelStatResp{} }
func (m *GetGuildsAmuseChannelStatResp) String() string { return proto.CompactTextString(m) }
func (*GetGuildsAmuseChannelStatResp) ProtoMessage()    {}
func (*GetGuildsAmuseChannelStatResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_gold_commission_18e8a43e3dce6c6d, []int{21}
}
func (m *GetGuildsAmuseChannelStatResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGuildsAmuseChannelStatResp.Unmarshal(m, b)
}
func (m *GetGuildsAmuseChannelStatResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGuildsAmuseChannelStatResp.Marshal(b, m, deterministic)
}
func (dst *GetGuildsAmuseChannelStatResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGuildsAmuseChannelStatResp.Merge(dst, src)
}
func (m *GetGuildsAmuseChannelStatResp) XXX_Size() int {
	return xxx_messageInfo_GetGuildsAmuseChannelStatResp.Size(m)
}
func (m *GetGuildsAmuseChannelStatResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGuildsAmuseChannelStatResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetGuildsAmuseChannelStatResp proto.InternalMessageInfo

func (m *GetGuildsAmuseChannelStatResp) GetChannelList() []*BaseChannelInfo {
	if m != nil {
		return m.ChannelList
	}
	return nil
}

type GetGuildsInteractGameAnchorStatReq struct {
	GuildIds             []uint32        `protobuf:"varint,1,rep,packed,name=guild_ids,json=guildIds,proto3" json:"guild_ids,omitempty"`
	BeginTime            uint32          `protobuf:"varint,2,opt,name=begin_time,json=beginTime,proto3" json:"begin_time,omitempty"`
	EndTime              uint32          `protobuf:"varint,3,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	SettleStatus         ReqSettleStatus `protobuf:"varint,4,opt,name=settle_status,json=settleStatus,proto3,enum=gold_commission.ReqSettleStatus" json:"settle_status,omitempty"`
	Unit                 TimeFilterUnit  `protobuf:"varint,5,opt,name=unit,proto3,enum=gold_commission.TimeFilterUnit" json:"unit,omitempty"`
	Offset               uint32          `protobuf:"varint,6,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32          `protobuf:"varint,7,opt,name=limit,proto3" json:"limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *GetGuildsInteractGameAnchorStatReq) Reset()         { *m = GetGuildsInteractGameAnchorStatReq{} }
func (m *GetGuildsInteractGameAnchorStatReq) String() string { return proto.CompactTextString(m) }
func (*GetGuildsInteractGameAnchorStatReq) ProtoMessage()    {}
func (*GetGuildsInteractGameAnchorStatReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_gold_commission_18e8a43e3dce6c6d, []int{22}
}
func (m *GetGuildsInteractGameAnchorStatReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGuildsInteractGameAnchorStatReq.Unmarshal(m, b)
}
func (m *GetGuildsInteractGameAnchorStatReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGuildsInteractGameAnchorStatReq.Marshal(b, m, deterministic)
}
func (dst *GetGuildsInteractGameAnchorStatReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGuildsInteractGameAnchorStatReq.Merge(dst, src)
}
func (m *GetGuildsInteractGameAnchorStatReq) XXX_Size() int {
	return xxx_messageInfo_GetGuildsInteractGameAnchorStatReq.Size(m)
}
func (m *GetGuildsInteractGameAnchorStatReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGuildsInteractGameAnchorStatReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetGuildsInteractGameAnchorStatReq proto.InternalMessageInfo

func (m *GetGuildsInteractGameAnchorStatReq) GetGuildIds() []uint32 {
	if m != nil {
		return m.GuildIds
	}
	return nil
}

func (m *GetGuildsInteractGameAnchorStatReq) GetBeginTime() uint32 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *GetGuildsInteractGameAnchorStatReq) GetEndTime() uint32 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *GetGuildsInteractGameAnchorStatReq) GetSettleStatus() ReqSettleStatus {
	if m != nil {
		return m.SettleStatus
	}
	return ReqSettleStatus_ReqSettleStatusALL
}

func (m *GetGuildsInteractGameAnchorStatReq) GetUnit() TimeFilterUnit {
	if m != nil {
		return m.Unit
	}
	return TimeFilterUnit_BY_DAY
}

func (m *GetGuildsInteractGameAnchorStatReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetGuildsInteractGameAnchorStatReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type GetGuildsInteractGameAnchorStatResp struct {
	AnchorList           []*BaseAnchorInfo `protobuf:"bytes,1,rep,name=anchorList,proto3" json:"anchorList,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetGuildsInteractGameAnchorStatResp) Reset()         { *m = GetGuildsInteractGameAnchorStatResp{} }
func (m *GetGuildsInteractGameAnchorStatResp) String() string { return proto.CompactTextString(m) }
func (*GetGuildsInteractGameAnchorStatResp) ProtoMessage()    {}
func (*GetGuildsInteractGameAnchorStatResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_gold_commission_18e8a43e3dce6c6d, []int{23}
}
func (m *GetGuildsInteractGameAnchorStatResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGuildsInteractGameAnchorStatResp.Unmarshal(m, b)
}
func (m *GetGuildsInteractGameAnchorStatResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGuildsInteractGameAnchorStatResp.Marshal(b, m, deterministic)
}
func (dst *GetGuildsInteractGameAnchorStatResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGuildsInteractGameAnchorStatResp.Merge(dst, src)
}
func (m *GetGuildsInteractGameAnchorStatResp) XXX_Size() int {
	return xxx_messageInfo_GetGuildsInteractGameAnchorStatResp.Size(m)
}
func (m *GetGuildsInteractGameAnchorStatResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGuildsInteractGameAnchorStatResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetGuildsInteractGameAnchorStatResp proto.InternalMessageInfo

func (m *GetGuildsInteractGameAnchorStatResp) GetAnchorList() []*BaseAnchorInfo {
	if m != nil {
		return m.AnchorList
	}
	return nil
}

type GetGuildsESportCoachStatReq struct {
	GuildIds             []uint32        `protobuf:"varint,1,rep,packed,name=guild_ids,json=guildIds,proto3" json:"guild_ids,omitempty"`
	BeginTime            uint32          `protobuf:"varint,2,opt,name=begin_time,json=beginTime,proto3" json:"begin_time,omitempty"`
	EndTime              uint32          `protobuf:"varint,3,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	SettleStatus         ReqSettleStatus `protobuf:"varint,4,opt,name=settle_status,json=settleStatus,proto3,enum=gold_commission.ReqSettleStatus" json:"settle_status,omitempty"`
	Unit                 TimeFilterUnit  `protobuf:"varint,5,opt,name=unit,proto3,enum=gold_commission.TimeFilterUnit" json:"unit,omitempty"`
	Offset               uint32          `protobuf:"varint,6,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32          `protobuf:"varint,7,opt,name=limit,proto3" json:"limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *GetGuildsESportCoachStatReq) Reset()         { *m = GetGuildsESportCoachStatReq{} }
func (m *GetGuildsESportCoachStatReq) String() string { return proto.CompactTextString(m) }
func (*GetGuildsESportCoachStatReq) ProtoMessage()    {}
func (*GetGuildsESportCoachStatReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_gold_commission_18e8a43e3dce6c6d, []int{24}
}
func (m *GetGuildsESportCoachStatReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGuildsESportCoachStatReq.Unmarshal(m, b)
}
func (m *GetGuildsESportCoachStatReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGuildsESportCoachStatReq.Marshal(b, m, deterministic)
}
func (dst *GetGuildsESportCoachStatReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGuildsESportCoachStatReq.Merge(dst, src)
}
func (m *GetGuildsESportCoachStatReq) XXX_Size() int {
	return xxx_messageInfo_GetGuildsESportCoachStatReq.Size(m)
}
func (m *GetGuildsESportCoachStatReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGuildsESportCoachStatReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetGuildsESportCoachStatReq proto.InternalMessageInfo

func (m *GetGuildsESportCoachStatReq) GetGuildIds() []uint32 {
	if m != nil {
		return m.GuildIds
	}
	return nil
}

func (m *GetGuildsESportCoachStatReq) GetBeginTime() uint32 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *GetGuildsESportCoachStatReq) GetEndTime() uint32 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *GetGuildsESportCoachStatReq) GetSettleStatus() ReqSettleStatus {
	if m != nil {
		return m.SettleStatus
	}
	return ReqSettleStatus_ReqSettleStatusALL
}

func (m *GetGuildsESportCoachStatReq) GetUnit() TimeFilterUnit {
	if m != nil {
		return m.Unit
	}
	return TimeFilterUnit_BY_DAY
}

func (m *GetGuildsESportCoachStatReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetGuildsESportCoachStatReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type GetGuildsESportCoachStatResp struct {
	CoachList            []*BaseAnchorInfo `protobuf:"bytes,1,rep,name=coachList,proto3" json:"coachList,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetGuildsESportCoachStatResp) Reset()         { *m = GetGuildsESportCoachStatResp{} }
func (m *GetGuildsESportCoachStatResp) String() string { return proto.CompactTextString(m) }
func (*GetGuildsESportCoachStatResp) ProtoMessage()    {}
func (*GetGuildsESportCoachStatResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_gold_commission_18e8a43e3dce6c6d, []int{25}
}
func (m *GetGuildsESportCoachStatResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGuildsESportCoachStatResp.Unmarshal(m, b)
}
func (m *GetGuildsESportCoachStatResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGuildsESportCoachStatResp.Marshal(b, m, deterministic)
}
func (dst *GetGuildsESportCoachStatResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGuildsESportCoachStatResp.Merge(dst, src)
}
func (m *GetGuildsESportCoachStatResp) XXX_Size() int {
	return xxx_messageInfo_GetGuildsESportCoachStatResp.Size(m)
}
func (m *GetGuildsESportCoachStatResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGuildsESportCoachStatResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetGuildsESportCoachStatResp proto.InternalMessageInfo

func (m *GetGuildsESportCoachStatResp) GetCoachList() []*BaseAnchorInfo {
	if m != nil {
		return m.CoachList
	}
	return nil
}

type GetGuildsUnSettlementSummaryReq struct {
	GuildIds             []uint32    `protobuf:"varint,1,rep,packed,name=guild_ids,json=guildIds,proto3" json:"guild_ids,omitempty"`
	IncludeTodayPaidCnt  bool        `protobuf:"varint,2,opt,name=include_today_paid_cnt,json=includeTodayPaidCnt,proto3" json:"include_today_paid_cnt,omitempty"`
	IncludeMonthPaidCnt  bool        `protobuf:"varint,3,opt,name=include_month_paid_cnt,json=includeMonthPaidCnt,proto3" json:"include_month_paid_cnt,omitempty"`
	ChannelType          ChannelType `protobuf:"varint,4,opt,name=channel_type,json=channelType,proto3,enum=gold_commission.ChannelType" json:"channel_type,omitempty"`
	GoldType             GoldType    `protobuf:"varint,5,opt,name=gold_type,json=goldType,proto3,enum=gold_commission.GoldType" json:"gold_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *GetGuildsUnSettlementSummaryReq) Reset()         { *m = GetGuildsUnSettlementSummaryReq{} }
func (m *GetGuildsUnSettlementSummaryReq) String() string { return proto.CompactTextString(m) }
func (*GetGuildsUnSettlementSummaryReq) ProtoMessage()    {}
func (*GetGuildsUnSettlementSummaryReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_gold_commission_18e8a43e3dce6c6d, []int{26}
}
func (m *GetGuildsUnSettlementSummaryReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGuildsUnSettlementSummaryReq.Unmarshal(m, b)
}
func (m *GetGuildsUnSettlementSummaryReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGuildsUnSettlementSummaryReq.Marshal(b, m, deterministic)
}
func (dst *GetGuildsUnSettlementSummaryReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGuildsUnSettlementSummaryReq.Merge(dst, src)
}
func (m *GetGuildsUnSettlementSummaryReq) XXX_Size() int {
	return xxx_messageInfo_GetGuildsUnSettlementSummaryReq.Size(m)
}
func (m *GetGuildsUnSettlementSummaryReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGuildsUnSettlementSummaryReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetGuildsUnSettlementSummaryReq proto.InternalMessageInfo

func (m *GetGuildsUnSettlementSummaryReq) GetGuildIds() []uint32 {
	if m != nil {
		return m.GuildIds
	}
	return nil
}

func (m *GetGuildsUnSettlementSummaryReq) GetIncludeTodayPaidCnt() bool {
	if m != nil {
		return m.IncludeTodayPaidCnt
	}
	return false
}

func (m *GetGuildsUnSettlementSummaryReq) GetIncludeMonthPaidCnt() bool {
	if m != nil {
		return m.IncludeMonthPaidCnt
	}
	return false
}

func (m *GetGuildsUnSettlementSummaryReq) GetChannelType() ChannelType {
	if m != nil {
		return m.ChannelType
	}
	return ChannelType_CTypeInvalid
}

func (m *GetGuildsUnSettlementSummaryReq) GetGoldType() GoldType {
	if m != nil {
		return m.GoldType
	}
	return GoldType_UNKNOWN_GOLD
}

type GetGuildsUnSettlementSummaryRsp struct {
	TodayFee             uint64   `protobuf:"varint,1,opt,name=today_fee,json=todayFee,proto3" json:"today_fee,omitempty"`
	TodayIncome          uint64   `protobuf:"varint,2,opt,name=today_income,json=todayIncome,proto3" json:"today_income,omitempty"`
	MonthFee             uint64   `protobuf:"varint,3,opt,name=month_fee,json=monthFee,proto3" json:"month_fee,omitempty"`
	MonthIncome          uint64   `protobuf:"varint,4,opt,name=month_income,json=monthIncome,proto3" json:"month_income,omitempty"`
	TodayPaidCnt         uint64   `protobuf:"varint,5,opt,name=today_paid_cnt,json=todayPaidCnt,proto3" json:"today_paid_cnt,omitempty"`
	MonthPaidCnt         uint64   `protobuf:"varint,6,opt,name=month_paid_cnt,json=monthPaidCnt,proto3" json:"month_paid_cnt,omitempty"`
	ThisMonthExtraIncome uint64   `protobuf:"varint,7,opt,name=this_month_extra_income,json=thisMonthExtraIncome,proto3" json:"this_month_extra_income,omitempty"`
	LastMonthExtraIncome uint64   `protobuf:"varint,8,opt,name=last_month_extra_income,json=lastMonthExtraIncome,proto3" json:"last_month_extra_income,omitempty"`
	NotSettleAmuseExtra  bool     `protobuf:"varint,9,opt,name=not_settle_amuse_extra,json=notSettleAmuseExtra,proto3" json:"not_settle_amuse_extra,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGuildsUnSettlementSummaryRsp) Reset()         { *m = GetGuildsUnSettlementSummaryRsp{} }
func (m *GetGuildsUnSettlementSummaryRsp) String() string { return proto.CompactTextString(m) }
func (*GetGuildsUnSettlementSummaryRsp) ProtoMessage()    {}
func (*GetGuildsUnSettlementSummaryRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_gold_commission_18e8a43e3dce6c6d, []int{27}
}
func (m *GetGuildsUnSettlementSummaryRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGuildsUnSettlementSummaryRsp.Unmarshal(m, b)
}
func (m *GetGuildsUnSettlementSummaryRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGuildsUnSettlementSummaryRsp.Marshal(b, m, deterministic)
}
func (dst *GetGuildsUnSettlementSummaryRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGuildsUnSettlementSummaryRsp.Merge(dst, src)
}
func (m *GetGuildsUnSettlementSummaryRsp) XXX_Size() int {
	return xxx_messageInfo_GetGuildsUnSettlementSummaryRsp.Size(m)
}
func (m *GetGuildsUnSettlementSummaryRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGuildsUnSettlementSummaryRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GetGuildsUnSettlementSummaryRsp proto.InternalMessageInfo

func (m *GetGuildsUnSettlementSummaryRsp) GetTodayFee() uint64 {
	if m != nil {
		return m.TodayFee
	}
	return 0
}

func (m *GetGuildsUnSettlementSummaryRsp) GetTodayIncome() uint64 {
	if m != nil {
		return m.TodayIncome
	}
	return 0
}

func (m *GetGuildsUnSettlementSummaryRsp) GetMonthFee() uint64 {
	if m != nil {
		return m.MonthFee
	}
	return 0
}

func (m *GetGuildsUnSettlementSummaryRsp) GetMonthIncome() uint64 {
	if m != nil {
		return m.MonthIncome
	}
	return 0
}

func (m *GetGuildsUnSettlementSummaryRsp) GetTodayPaidCnt() uint64 {
	if m != nil {
		return m.TodayPaidCnt
	}
	return 0
}

func (m *GetGuildsUnSettlementSummaryRsp) GetMonthPaidCnt() uint64 {
	if m != nil {
		return m.MonthPaidCnt
	}
	return 0
}

func (m *GetGuildsUnSettlementSummaryRsp) GetThisMonthExtraIncome() uint64 {
	if m != nil {
		return m.ThisMonthExtraIncome
	}
	return 0
}

func (m *GetGuildsUnSettlementSummaryRsp) GetLastMonthExtraIncome() uint64 {
	if m != nil {
		return m.LastMonthExtraIncome
	}
	return 0
}

func (m *GetGuildsUnSettlementSummaryRsp) GetNotSettleAmuseExtra() bool {
	if m != nil {
		return m.NotSettleAmuseExtra
	}
	return false
}

// 获取会长服务号语音直播数据详情
type GetYuyinIncomeDetailReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	GuildId              uint32   `protobuf:"varint,2,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetYuyinIncomeDetailReq) Reset()         { *m = GetYuyinIncomeDetailReq{} }
func (m *GetYuyinIncomeDetailReq) String() string { return proto.CompactTextString(m) }
func (*GetYuyinIncomeDetailReq) ProtoMessage()    {}
func (*GetYuyinIncomeDetailReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_gold_commission_18e8a43e3dce6c6d, []int{28}
}
func (m *GetYuyinIncomeDetailReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetYuyinIncomeDetailReq.Unmarshal(m, b)
}
func (m *GetYuyinIncomeDetailReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetYuyinIncomeDetailReq.Marshal(b, m, deterministic)
}
func (dst *GetYuyinIncomeDetailReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetYuyinIncomeDetailReq.Merge(dst, src)
}
func (m *GetYuyinIncomeDetailReq) XXX_Size() int {
	return xxx_messageInfo_GetYuyinIncomeDetailReq.Size(m)
}
func (m *GetYuyinIncomeDetailReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetYuyinIncomeDetailReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetYuyinIncomeDetailReq proto.InternalMessageInfo

func (m *GetYuyinIncomeDetailReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetYuyinIncomeDetailReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

type GetYuyinIncomeDetailRsp struct {
	DailyIncome          *DailyIncome   `protobuf:"bytes,2,opt,name=daily_income,json=dailyIncome,proto3" json:"daily_income,omitempty"`
	WeeklyIncome         *WeeklyIncome  `protobuf:"bytes,3,opt,name=weekly_income,json=weeklyIncome,proto3" json:"weekly_income,omitempty"`
	MonthlyIncome        *MonthlyIncome `protobuf:"bytes,4,opt,name=monthly_income,json=monthlyIncome,proto3" json:"monthly_income,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *GetYuyinIncomeDetailRsp) Reset()         { *m = GetYuyinIncomeDetailRsp{} }
func (m *GetYuyinIncomeDetailRsp) String() string { return proto.CompactTextString(m) }
func (*GetYuyinIncomeDetailRsp) ProtoMessage()    {}
func (*GetYuyinIncomeDetailRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_gold_commission_18e8a43e3dce6c6d, []int{29}
}
func (m *GetYuyinIncomeDetailRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetYuyinIncomeDetailRsp.Unmarshal(m, b)
}
func (m *GetYuyinIncomeDetailRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetYuyinIncomeDetailRsp.Marshal(b, m, deterministic)
}
func (dst *GetYuyinIncomeDetailRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetYuyinIncomeDetailRsp.Merge(dst, src)
}
func (m *GetYuyinIncomeDetailRsp) XXX_Size() int {
	return xxx_messageInfo_GetYuyinIncomeDetailRsp.Size(m)
}
func (m *GetYuyinIncomeDetailRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetYuyinIncomeDetailRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GetYuyinIncomeDetailRsp proto.InternalMessageInfo

func (m *GetYuyinIncomeDetailRsp) GetDailyIncome() *DailyIncome {
	if m != nil {
		return m.DailyIncome
	}
	return nil
}

func (m *GetYuyinIncomeDetailRsp) GetWeeklyIncome() *WeeklyIncome {
	if m != nil {
		return m.WeeklyIncome
	}
	return nil
}

func (m *GetYuyinIncomeDetailRsp) GetMonthlyIncome() *MonthlyIncome {
	if m != nil {
		return m.MonthlyIncome
	}
	return nil
}

// DailyIncome 收益日增长信息
type DailyIncome struct {
	TodayIncome          uint64   `protobuf:"varint,1,opt,name=today_income,json=todayIncome,proto3" json:"today_income,omitempty"`
	YesterdayIncome      uint64   `protobuf:"varint,2,opt,name=yesterday_income,json=yesterdayIncome,proto3" json:"yesterday_income,omitempty"`
	DailyQoq             float32  `protobuf:"fixed32,3,opt,name=daily_qoq,json=dailyQoq,proto3" json:"daily_qoq,omitempty"`
	TodayFee             uint64   `protobuf:"varint,4,opt,name=today_fee,json=todayFee,proto3" json:"today_fee,omitempty"`
	YesterdayFee         uint64   `protobuf:"varint,5,opt,name=yesterday_fee,json=yesterdayFee,proto3" json:"yesterday_fee,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DailyIncome) Reset()         { *m = DailyIncome{} }
func (m *DailyIncome) String() string { return proto.CompactTextString(m) }
func (*DailyIncome) ProtoMessage()    {}
func (*DailyIncome) Descriptor() ([]byte, []int) {
	return fileDescriptor_gold_commission_18e8a43e3dce6c6d, []int{30}
}
func (m *DailyIncome) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DailyIncome.Unmarshal(m, b)
}
func (m *DailyIncome) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DailyIncome.Marshal(b, m, deterministic)
}
func (dst *DailyIncome) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DailyIncome.Merge(dst, src)
}
func (m *DailyIncome) XXX_Size() int {
	return xxx_messageInfo_DailyIncome.Size(m)
}
func (m *DailyIncome) XXX_DiscardUnknown() {
	xxx_messageInfo_DailyIncome.DiscardUnknown(m)
}

var xxx_messageInfo_DailyIncome proto.InternalMessageInfo

func (m *DailyIncome) GetTodayIncome() uint64 {
	if m != nil {
		return m.TodayIncome
	}
	return 0
}

func (m *DailyIncome) GetYesterdayIncome() uint64 {
	if m != nil {
		return m.YesterdayIncome
	}
	return 0
}

func (m *DailyIncome) GetDailyQoq() float32 {
	if m != nil {
		return m.DailyQoq
	}
	return 0
}

func (m *DailyIncome) GetTodayFee() uint64 {
	if m != nil {
		return m.TodayFee
	}
	return 0
}

func (m *DailyIncome) GetYesterdayFee() uint64 {
	if m != nil {
		return m.YesterdayFee
	}
	return 0
}

// WeeklyIncome 收益周增长信息
type WeeklyIncome struct {
	ThisWeekIncome       uint64   `protobuf:"varint,1,opt,name=this_week_income,json=thisWeekIncome,proto3" json:"this_week_income,omitempty"`
	LastWeekIncome       uint64   `protobuf:"varint,2,opt,name=last_week_income,json=lastWeekIncome,proto3" json:"last_week_income,omitempty"`
	CurrentTime          int64    `protobuf:"varint,3,opt,name=current_time,json=currentTime,proto3" json:"current_time,omitempty"`
	WeekStartTime        int64    `protobuf:"varint,4,opt,name=week_start_time,json=weekStartTime,proto3" json:"week_start_time,omitempty"`
	WeekEndTime          int64    `protobuf:"varint,5,opt,name=week_end_time,json=weekEndTime,proto3" json:"week_end_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *WeeklyIncome) Reset()         { *m = WeeklyIncome{} }
func (m *WeeklyIncome) String() string { return proto.CompactTextString(m) }
func (*WeeklyIncome) ProtoMessage()    {}
func (*WeeklyIncome) Descriptor() ([]byte, []int) {
	return fileDescriptor_gold_commission_18e8a43e3dce6c6d, []int{31}
}
func (m *WeeklyIncome) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WeeklyIncome.Unmarshal(m, b)
}
func (m *WeeklyIncome) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WeeklyIncome.Marshal(b, m, deterministic)
}
func (dst *WeeklyIncome) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WeeklyIncome.Merge(dst, src)
}
func (m *WeeklyIncome) XXX_Size() int {
	return xxx_messageInfo_WeeklyIncome.Size(m)
}
func (m *WeeklyIncome) XXX_DiscardUnknown() {
	xxx_messageInfo_WeeklyIncome.DiscardUnknown(m)
}

var xxx_messageInfo_WeeklyIncome proto.InternalMessageInfo

func (m *WeeklyIncome) GetThisWeekIncome() uint64 {
	if m != nil {
		return m.ThisWeekIncome
	}
	return 0
}

func (m *WeeklyIncome) GetLastWeekIncome() uint64 {
	if m != nil {
		return m.LastWeekIncome
	}
	return 0
}

func (m *WeeklyIncome) GetCurrentTime() int64 {
	if m != nil {
		return m.CurrentTime
	}
	return 0
}

func (m *WeeklyIncome) GetWeekStartTime() int64 {
	if m != nil {
		return m.WeekStartTime
	}
	return 0
}

func (m *WeeklyIncome) GetWeekEndTime() int64 {
	if m != nil {
		return m.WeekEndTime
	}
	return 0
}

// MonthlyIncome 收益月增长信息
type MonthlyIncome struct {
	ThisMonthIncome      uint64   `protobuf:"varint,1,opt,name=this_month_income,json=thisMonthIncome,proto3" json:"this_month_income,omitempty"`
	LastMonthIncome      uint64   `protobuf:"varint,2,opt,name=last_month_income,json=lastMonthIncome,proto3" json:"last_month_income,omitempty"`
	MonthlyQoq           float32  `protobuf:"fixed32,3,opt,name=monthly_qoq,json=monthlyQoq,proto3" json:"monthly_qoq,omitempty"`
	SameLastMonthIncome  uint64   `protobuf:"varint,4,opt,name=same_last_month_income,json=sameLastMonthIncome,proto3" json:"same_last_month_income,omitempty"`
	LastMonthValidIncome uint64   `protobuf:"varint,5,opt,name=last_month_valid_income,json=lastMonthValidIncome,proto3" json:"last_month_valid_income,omitempty"`
	LastSixMonthIncome   uint64   `protobuf:"varint,6,opt,name=last_six_month_income,json=lastSixMonthIncome,proto3" json:"last_six_month_income,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MonthlyIncome) Reset()         { *m = MonthlyIncome{} }
func (m *MonthlyIncome) String() string { return proto.CompactTextString(m) }
func (*MonthlyIncome) ProtoMessage()    {}
func (*MonthlyIncome) Descriptor() ([]byte, []int) {
	return fileDescriptor_gold_commission_18e8a43e3dce6c6d, []int{32}
}
func (m *MonthlyIncome) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MonthlyIncome.Unmarshal(m, b)
}
func (m *MonthlyIncome) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MonthlyIncome.Marshal(b, m, deterministic)
}
func (dst *MonthlyIncome) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MonthlyIncome.Merge(dst, src)
}
func (m *MonthlyIncome) XXX_Size() int {
	return xxx_messageInfo_MonthlyIncome.Size(m)
}
func (m *MonthlyIncome) XXX_DiscardUnknown() {
	xxx_messageInfo_MonthlyIncome.DiscardUnknown(m)
}

var xxx_messageInfo_MonthlyIncome proto.InternalMessageInfo

func (m *MonthlyIncome) GetThisMonthIncome() uint64 {
	if m != nil {
		return m.ThisMonthIncome
	}
	return 0
}

func (m *MonthlyIncome) GetLastMonthIncome() uint64 {
	if m != nil {
		return m.LastMonthIncome
	}
	return 0
}

func (m *MonthlyIncome) GetMonthlyQoq() float32 {
	if m != nil {
		return m.MonthlyQoq
	}
	return 0
}

func (m *MonthlyIncome) GetSameLastMonthIncome() uint64 {
	if m != nil {
		return m.SameLastMonthIncome
	}
	return 0
}

func (m *MonthlyIncome) GetLastMonthValidIncome() uint64 {
	if m != nil {
		return m.LastMonthValidIncome
	}
	return 0
}

func (m *MonthlyIncome) GetLastSixMonthIncome() uint64 {
	if m != nil {
		return m.LastSixMonthIncome
	}
	return 0
}

// 根据截止时间获取月收益日趋势列表
type GetIncomeTrendListReq struct {
	Uid                  uint32      `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	GuildId              uint32      `protobuf:"varint,2,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	EndTime              int64       `protobuf:"varint,3,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	ChannelType          ChannelType `protobuf:"varint,4,opt,name=channel_type,json=channelType,proto3,enum=gold_commission.ChannelType" json:"channel_type,omitempty"`
	GoldType             GoldType    `protobuf:"varint,5,opt,name=gold_type,json=goldType,proto3,enum=gold_commission.GoldType" json:"gold_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *GetIncomeTrendListReq) Reset()         { *m = GetIncomeTrendListReq{} }
func (m *GetIncomeTrendListReq) String() string { return proto.CompactTextString(m) }
func (*GetIncomeTrendListReq) ProtoMessage()    {}
func (*GetIncomeTrendListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_gold_commission_18e8a43e3dce6c6d, []int{33}
}
func (m *GetIncomeTrendListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetIncomeTrendListReq.Unmarshal(m, b)
}
func (m *GetIncomeTrendListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetIncomeTrendListReq.Marshal(b, m, deterministic)
}
func (dst *GetIncomeTrendListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetIncomeTrendListReq.Merge(dst, src)
}
func (m *GetIncomeTrendListReq) XXX_Size() int {
	return xxx_messageInfo_GetIncomeTrendListReq.Size(m)
}
func (m *GetIncomeTrendListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetIncomeTrendListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetIncomeTrendListReq proto.InternalMessageInfo

func (m *GetIncomeTrendListReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetIncomeTrendListReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GetIncomeTrendListReq) GetEndTime() int64 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *GetIncomeTrendListReq) GetChannelType() ChannelType {
	if m != nil {
		return m.ChannelType
	}
	return ChannelType_CTypeInvalid
}

func (m *GetIncomeTrendListReq) GetGoldType() GoldType {
	if m != nil {
		return m.GoldType
	}
	return GoldType_UNKNOWN_GOLD
}

type GetIncomeTrendListRsp struct {
	List                 []*TrendInfo `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetIncomeTrendListRsp) Reset()         { *m = GetIncomeTrendListRsp{} }
func (m *GetIncomeTrendListRsp) String() string { return proto.CompactTextString(m) }
func (*GetIncomeTrendListRsp) ProtoMessage()    {}
func (*GetIncomeTrendListRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_gold_commission_18e8a43e3dce6c6d, []int{34}
}
func (m *GetIncomeTrendListRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetIncomeTrendListRsp.Unmarshal(m, b)
}
func (m *GetIncomeTrendListRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetIncomeTrendListRsp.Marshal(b, m, deterministic)
}
func (dst *GetIncomeTrendListRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetIncomeTrendListRsp.Merge(dst, src)
}
func (m *GetIncomeTrendListRsp) XXX_Size() int {
	return xxx_messageInfo_GetIncomeTrendListRsp.Size(m)
}
func (m *GetIncomeTrendListRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetIncomeTrendListRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GetIncomeTrendListRsp proto.InternalMessageInfo

func (m *GetIncomeTrendListRsp) GetList() []*TrendInfo {
	if m != nil {
		return m.List
	}
	return nil
}

// TrendInfo 趋势值信息
type TrendInfo struct {
	Key                  string   `protobuf:"bytes,1,opt,name=key,proto3" json:"key,omitempty"`
	Value                uint64   `protobuf:"varint,2,opt,name=value,proto3" json:"value,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TrendInfo) Reset()         { *m = TrendInfo{} }
func (m *TrendInfo) String() string { return proto.CompactTextString(m) }
func (*TrendInfo) ProtoMessage()    {}
func (*TrendInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_gold_commission_18e8a43e3dce6c6d, []int{35}
}
func (m *TrendInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TrendInfo.Unmarshal(m, b)
}
func (m *TrendInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TrendInfo.Marshal(b, m, deterministic)
}
func (dst *TrendInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TrendInfo.Merge(dst, src)
}
func (m *TrendInfo) XXX_Size() int {
	return xxx_messageInfo_TrendInfo.Size(m)
}
func (m *TrendInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_TrendInfo.DiscardUnknown(m)
}

var xxx_messageInfo_TrendInfo proto.InternalMessageInfo

func (m *TrendInfo) GetKey() string {
	if m != nil {
		return m.Key
	}
	return ""
}

func (m *TrendInfo) GetValue() uint64 {
	if m != nil {
		return m.Value
	}
	return 0
}

// 获取消费排行榜
type GetConsumeRankReq struct {
	ConsumeType          RangeType   `protobuf:"varint,1,opt,name=consume_type,json=consumeType,proto3,enum=gold_commission.RangeType" json:"consume_type,omitempty"`
	GuildId              uint32      `protobuf:"varint,2,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	ChannelType          ChannelType `protobuf:"varint,3,opt,name=channel_type,json=channelType,proto3,enum=gold_commission.ChannelType" json:"channel_type,omitempty"`
	GoldType             GoldType    `protobuf:"varint,4,opt,name=gold_type,json=goldType,proto3,enum=gold_commission.GoldType" json:"gold_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *GetConsumeRankReq) Reset()         { *m = GetConsumeRankReq{} }
func (m *GetConsumeRankReq) String() string { return proto.CompactTextString(m) }
func (*GetConsumeRankReq) ProtoMessage()    {}
func (*GetConsumeRankReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_gold_commission_18e8a43e3dce6c6d, []int{36}
}
func (m *GetConsumeRankReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetConsumeRankReq.Unmarshal(m, b)
}
func (m *GetConsumeRankReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetConsumeRankReq.Marshal(b, m, deterministic)
}
func (dst *GetConsumeRankReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetConsumeRankReq.Merge(dst, src)
}
func (m *GetConsumeRankReq) XXX_Size() int {
	return xxx_messageInfo_GetConsumeRankReq.Size(m)
}
func (m *GetConsumeRankReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetConsumeRankReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetConsumeRankReq proto.InternalMessageInfo

func (m *GetConsumeRankReq) GetConsumeType() RangeType {
	if m != nil {
		return m.ConsumeType
	}
	return RangeType_DAY_RANGE_TYPE
}

func (m *GetConsumeRankReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GetConsumeRankReq) GetChannelType() ChannelType {
	if m != nil {
		return m.ChannelType
	}
	return ChannelType_CTypeInvalid
}

func (m *GetConsumeRankReq) GetGoldType() GoldType {
	if m != nil {
		return m.GoldType
	}
	return GoldType_UNKNOWN_GOLD
}

type GetConsumeRankRsp struct {
	ConsumeRankList      []*ConsumeRankItem `protobuf:"bytes,1,rep,name=consume_rank_list,json=consumeRankList,proto3" json:"consume_rank_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *GetConsumeRankRsp) Reset()         { *m = GetConsumeRankRsp{} }
func (m *GetConsumeRankRsp) String() string { return proto.CompactTextString(m) }
func (*GetConsumeRankRsp) ProtoMessage()    {}
func (*GetConsumeRankRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_gold_commission_18e8a43e3dce6c6d, []int{37}
}
func (m *GetConsumeRankRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetConsumeRankRsp.Unmarshal(m, b)
}
func (m *GetConsumeRankRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetConsumeRankRsp.Marshal(b, m, deterministic)
}
func (dst *GetConsumeRankRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetConsumeRankRsp.Merge(dst, src)
}
func (m *GetConsumeRankRsp) XXX_Size() int {
	return xxx_messageInfo_GetConsumeRankRsp.Size(m)
}
func (m *GetConsumeRankRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetConsumeRankRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GetConsumeRankRsp proto.InternalMessageInfo

func (m *GetConsumeRankRsp) GetConsumeRankList() []*ConsumeRankItem {
	if m != nil {
		return m.ConsumeRankList
	}
	return nil
}

// ConsumeRankItem 用户消费排行榜信息
type ConsumeRankItem struct {
	Alias                string   `protobuf:"bytes,1,opt,name=alias,proto3" json:"alias,omitempty"`
	NickName             string   `protobuf:"bytes,2,opt,name=nick_name,json=nickName,proto3" json:"nick_name,omitempty"`
	Account              string   `protobuf:"bytes,3,opt,name=account,proto3" json:"account,omitempty"`
	Consume              int64    `protobuf:"varint,4,opt,name=consume,proto3" json:"consume,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ConsumeRankItem) Reset()         { *m = ConsumeRankItem{} }
func (m *ConsumeRankItem) String() string { return proto.CompactTextString(m) }
func (*ConsumeRankItem) ProtoMessage()    {}
func (*ConsumeRankItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_gold_commission_18e8a43e3dce6c6d, []int{38}
}
func (m *ConsumeRankItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ConsumeRankItem.Unmarshal(m, b)
}
func (m *ConsumeRankItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ConsumeRankItem.Marshal(b, m, deterministic)
}
func (dst *ConsumeRankItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ConsumeRankItem.Merge(dst, src)
}
func (m *ConsumeRankItem) XXX_Size() int {
	return xxx_messageInfo_ConsumeRankItem.Size(m)
}
func (m *ConsumeRankItem) XXX_DiscardUnknown() {
	xxx_messageInfo_ConsumeRankItem.DiscardUnknown(m)
}

var xxx_messageInfo_ConsumeRankItem proto.InternalMessageInfo

func (m *ConsumeRankItem) GetAlias() string {
	if m != nil {
		return m.Alias
	}
	return ""
}

func (m *ConsumeRankItem) GetNickName() string {
	if m != nil {
		return m.NickName
	}
	return ""
}

func (m *ConsumeRankItem) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *ConsumeRankItem) GetConsume() int64 {
	if m != nil {
		return m.Consume
	}
	return 0
}

// 获取该日收益情况
type GetDayIncomeReq struct {
	Uid                  uint32      `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	GuildId              uint32      `protobuf:"varint,2,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	ChannelType          ChannelType `protobuf:"varint,3,opt,name=channel_type,json=channelType,proto3,enum=gold_commission.ChannelType" json:"channel_type,omitempty"`
	GoldType             GoldType    `protobuf:"varint,4,opt,name=gold_type,json=goldType,proto3,enum=gold_commission.GoldType" json:"gold_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *GetDayIncomeReq) Reset()         { *m = GetDayIncomeReq{} }
func (m *GetDayIncomeReq) String() string { return proto.CompactTextString(m) }
func (*GetDayIncomeReq) ProtoMessage()    {}
func (*GetDayIncomeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_gold_commission_18e8a43e3dce6c6d, []int{39}
}
func (m *GetDayIncomeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetDayIncomeReq.Unmarshal(m, b)
}
func (m *GetDayIncomeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetDayIncomeReq.Marshal(b, m, deterministic)
}
func (dst *GetDayIncomeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetDayIncomeReq.Merge(dst, src)
}
func (m *GetDayIncomeReq) XXX_Size() int {
	return xxx_messageInfo_GetDayIncomeReq.Size(m)
}
func (m *GetDayIncomeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetDayIncomeReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetDayIncomeReq proto.InternalMessageInfo

func (m *GetDayIncomeReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetDayIncomeReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GetDayIncomeReq) GetChannelType() ChannelType {
	if m != nil {
		return m.ChannelType
	}
	return ChannelType_CTypeInvalid
}

func (m *GetDayIncomeReq) GetGoldType() GoldType {
	if m != nil {
		return m.GoldType
	}
	return GoldType_UNKNOWN_GOLD
}

type GetDayIncomeRsp struct {
	DailyIncome          *DailyIncome `protobuf:"bytes,1,opt,name=daily_income,json=dailyIncome,proto3" json:"daily_income,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetDayIncomeRsp) Reset()         { *m = GetDayIncomeRsp{} }
func (m *GetDayIncomeRsp) String() string { return proto.CompactTextString(m) }
func (*GetDayIncomeRsp) ProtoMessage()    {}
func (*GetDayIncomeRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_gold_commission_18e8a43e3dce6c6d, []int{40}
}
func (m *GetDayIncomeRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetDayIncomeRsp.Unmarshal(m, b)
}
func (m *GetDayIncomeRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetDayIncomeRsp.Marshal(b, m, deterministic)
}
func (dst *GetDayIncomeRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetDayIncomeRsp.Merge(dst, src)
}
func (m *GetDayIncomeRsp) XXX_Size() int {
	return xxx_messageInfo_GetDayIncomeRsp.Size(m)
}
func (m *GetDayIncomeRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetDayIncomeRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GetDayIncomeRsp proto.InternalMessageInfo

func (m *GetDayIncomeRsp) GetDailyIncome() *DailyIncome {
	if m != nil {
		return m.DailyIncome
	}
	return nil
}

// 获取语音直播该周收益情况
type GetYuyinWeekIncomeReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	GuildId              uint32   `protobuf:"varint,2,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetYuyinWeekIncomeReq) Reset()         { *m = GetYuyinWeekIncomeReq{} }
func (m *GetYuyinWeekIncomeReq) String() string { return proto.CompactTextString(m) }
func (*GetYuyinWeekIncomeReq) ProtoMessage()    {}
func (*GetYuyinWeekIncomeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_gold_commission_18e8a43e3dce6c6d, []int{41}
}
func (m *GetYuyinWeekIncomeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetYuyinWeekIncomeReq.Unmarshal(m, b)
}
func (m *GetYuyinWeekIncomeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetYuyinWeekIncomeReq.Marshal(b, m, deterministic)
}
func (dst *GetYuyinWeekIncomeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetYuyinWeekIncomeReq.Merge(dst, src)
}
func (m *GetYuyinWeekIncomeReq) XXX_Size() int {
	return xxx_messageInfo_GetYuyinWeekIncomeReq.Size(m)
}
func (m *GetYuyinWeekIncomeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetYuyinWeekIncomeReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetYuyinWeekIncomeReq proto.InternalMessageInfo

func (m *GetYuyinWeekIncomeReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetYuyinWeekIncomeReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

type GetYuyinWeekIncomeRsp struct {
	WeeklyIncome         *WeeklyIncome `protobuf:"bytes,1,opt,name=weekly_income,json=weeklyIncome,proto3" json:"weekly_income,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetYuyinWeekIncomeRsp) Reset()         { *m = GetYuyinWeekIncomeRsp{} }
func (m *GetYuyinWeekIncomeRsp) String() string { return proto.CompactTextString(m) }
func (*GetYuyinWeekIncomeRsp) ProtoMessage()    {}
func (*GetYuyinWeekIncomeRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_gold_commission_18e8a43e3dce6c6d, []int{42}
}
func (m *GetYuyinWeekIncomeRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetYuyinWeekIncomeRsp.Unmarshal(m, b)
}
func (m *GetYuyinWeekIncomeRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetYuyinWeekIncomeRsp.Marshal(b, m, deterministic)
}
func (dst *GetYuyinWeekIncomeRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetYuyinWeekIncomeRsp.Merge(dst, src)
}
func (m *GetYuyinWeekIncomeRsp) XXX_Size() int {
	return xxx_messageInfo_GetYuyinWeekIncomeRsp.Size(m)
}
func (m *GetYuyinWeekIncomeRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetYuyinWeekIncomeRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GetYuyinWeekIncomeRsp proto.InternalMessageInfo

func (m *GetYuyinWeekIncomeRsp) GetWeeklyIncome() *WeeklyIncome {
	if m != nil {
		return m.WeeklyIncome
	}
	return nil
}

// 获取互动游戏该周收益情况
type GetInteractGameWeekIncomeReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	GuildId              uint32   `protobuf:"varint,2,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetInteractGameWeekIncomeReq) Reset()         { *m = GetInteractGameWeekIncomeReq{} }
func (m *GetInteractGameWeekIncomeReq) String() string { return proto.CompactTextString(m) }
func (*GetInteractGameWeekIncomeReq) ProtoMessage()    {}
func (*GetInteractGameWeekIncomeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_gold_commission_18e8a43e3dce6c6d, []int{43}
}
func (m *GetInteractGameWeekIncomeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetInteractGameWeekIncomeReq.Unmarshal(m, b)
}
func (m *GetInteractGameWeekIncomeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetInteractGameWeekIncomeReq.Marshal(b, m, deterministic)
}
func (dst *GetInteractGameWeekIncomeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetInteractGameWeekIncomeReq.Merge(dst, src)
}
func (m *GetInteractGameWeekIncomeReq) XXX_Size() int {
	return xxx_messageInfo_GetInteractGameWeekIncomeReq.Size(m)
}
func (m *GetInteractGameWeekIncomeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetInteractGameWeekIncomeReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetInteractGameWeekIncomeReq proto.InternalMessageInfo

func (m *GetInteractGameWeekIncomeReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetInteractGameWeekIncomeReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

type GetInteractGameWeekIncomeRsp struct {
	WeeklyIncome         *WeeklyIncome `protobuf:"bytes,1,opt,name=weekly_income,json=weeklyIncome,proto3" json:"weekly_income,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetInteractGameWeekIncomeRsp) Reset()         { *m = GetInteractGameWeekIncomeRsp{} }
func (m *GetInteractGameWeekIncomeRsp) String() string { return proto.CompactTextString(m) }
func (*GetInteractGameWeekIncomeRsp) ProtoMessage()    {}
func (*GetInteractGameWeekIncomeRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_gold_commission_18e8a43e3dce6c6d, []int{44}
}
func (m *GetInteractGameWeekIncomeRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetInteractGameWeekIncomeRsp.Unmarshal(m, b)
}
func (m *GetInteractGameWeekIncomeRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetInteractGameWeekIncomeRsp.Marshal(b, m, deterministic)
}
func (dst *GetInteractGameWeekIncomeRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetInteractGameWeekIncomeRsp.Merge(dst, src)
}
func (m *GetInteractGameWeekIncomeRsp) XXX_Size() int {
	return xxx_messageInfo_GetInteractGameWeekIncomeRsp.Size(m)
}
func (m *GetInteractGameWeekIncomeRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetInteractGameWeekIncomeRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GetInteractGameWeekIncomeRsp proto.InternalMessageInfo

func (m *GetInteractGameWeekIncomeRsp) GetWeeklyIncome() *WeeklyIncome {
	if m != nil {
		return m.WeeklyIncome
	}
	return nil
}

// 获取该月收益情况
type GetMonthIncomeReq struct {
	Uid                  uint32      `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	GuildId              uint32      `protobuf:"varint,2,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	ChannelType          ChannelType `protobuf:"varint,3,opt,name=channel_type,json=channelType,proto3,enum=gold_commission.ChannelType" json:"channel_type,omitempty"`
	GoldType             GoldType    `protobuf:"varint,4,opt,name=gold_type,json=goldType,proto3,enum=gold_commission.GoldType" json:"gold_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *GetMonthIncomeReq) Reset()         { *m = GetMonthIncomeReq{} }
func (m *GetMonthIncomeReq) String() string { return proto.CompactTextString(m) }
func (*GetMonthIncomeReq) ProtoMessage()    {}
func (*GetMonthIncomeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_gold_commission_18e8a43e3dce6c6d, []int{45}
}
func (m *GetMonthIncomeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMonthIncomeReq.Unmarshal(m, b)
}
func (m *GetMonthIncomeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMonthIncomeReq.Marshal(b, m, deterministic)
}
func (dst *GetMonthIncomeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMonthIncomeReq.Merge(dst, src)
}
func (m *GetMonthIncomeReq) XXX_Size() int {
	return xxx_messageInfo_GetMonthIncomeReq.Size(m)
}
func (m *GetMonthIncomeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMonthIncomeReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetMonthIncomeReq proto.InternalMessageInfo

func (m *GetMonthIncomeReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetMonthIncomeReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GetMonthIncomeReq) GetChannelType() ChannelType {
	if m != nil {
		return m.ChannelType
	}
	return ChannelType_CTypeInvalid
}

func (m *GetMonthIncomeReq) GetGoldType() GoldType {
	if m != nil {
		return m.GoldType
	}
	return GoldType_UNKNOWN_GOLD
}

type GetMonthIncomeRsp struct {
	MonthlyIncome        *MonthlyIncome `protobuf:"bytes,1,opt,name=monthly_income,json=monthlyIncome,proto3" json:"monthly_income,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *GetMonthIncomeRsp) Reset()         { *m = GetMonthIncomeRsp{} }
func (m *GetMonthIncomeRsp) String() string { return proto.CompactTextString(m) }
func (*GetMonthIncomeRsp) ProtoMessage()    {}
func (*GetMonthIncomeRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_gold_commission_18e8a43e3dce6c6d, []int{46}
}
func (m *GetMonthIncomeRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMonthIncomeRsp.Unmarshal(m, b)
}
func (m *GetMonthIncomeRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMonthIncomeRsp.Marshal(b, m, deterministic)
}
func (dst *GetMonthIncomeRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMonthIncomeRsp.Merge(dst, src)
}
func (m *GetMonthIncomeRsp) XXX_Size() int {
	return xxx_messageInfo_GetMonthIncomeRsp.Size(m)
}
func (m *GetMonthIncomeRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMonthIncomeRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GetMonthIncomeRsp proto.InternalMessageInfo

func (m *GetMonthIncomeRsp) GetMonthlyIncome() *MonthlyIncome {
	if m != nil {
		return m.MonthlyIncome
	}
	return nil
}

// 获取语音直播按月份额外收益
type GetGuildYuyinExtraIncomeReq struct {
	Uid                  uint32          `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	GuildIds             []uint32        `protobuf:"varint,2,rep,packed,name=guild_ids,json=guildIds,proto3" json:"guild_ids,omitempty"`
	BeginTime            int64           `protobuf:"varint,3,opt,name=begin_time,json=beginTime,proto3" json:"begin_time,omitempty"`
	EndTime              int64           `protobuf:"varint,4,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	SettleStatus         ReqSettleStatus `protobuf:"varint,5,opt,name=settle_status,json=settleStatus,proto3,enum=gold_commission.ReqSettleStatus" json:"settle_status,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *GetGuildYuyinExtraIncomeReq) Reset()         { *m = GetGuildYuyinExtraIncomeReq{} }
func (m *GetGuildYuyinExtraIncomeReq) String() string { return proto.CompactTextString(m) }
func (*GetGuildYuyinExtraIncomeReq) ProtoMessage()    {}
func (*GetGuildYuyinExtraIncomeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_gold_commission_18e8a43e3dce6c6d, []int{47}
}
func (m *GetGuildYuyinExtraIncomeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGuildYuyinExtraIncomeReq.Unmarshal(m, b)
}
func (m *GetGuildYuyinExtraIncomeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGuildYuyinExtraIncomeReq.Marshal(b, m, deterministic)
}
func (dst *GetGuildYuyinExtraIncomeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGuildYuyinExtraIncomeReq.Merge(dst, src)
}
func (m *GetGuildYuyinExtraIncomeReq) XXX_Size() int {
	return xxx_messageInfo_GetGuildYuyinExtraIncomeReq.Size(m)
}
func (m *GetGuildYuyinExtraIncomeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGuildYuyinExtraIncomeReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetGuildYuyinExtraIncomeReq proto.InternalMessageInfo

func (m *GetGuildYuyinExtraIncomeReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetGuildYuyinExtraIncomeReq) GetGuildIds() []uint32 {
	if m != nil {
		return m.GuildIds
	}
	return nil
}

func (m *GetGuildYuyinExtraIncomeReq) GetBeginTime() int64 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *GetGuildYuyinExtraIncomeReq) GetEndTime() int64 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *GetGuildYuyinExtraIncomeReq) GetSettleStatus() ReqSettleStatus {
	if m != nil {
		return m.SettleStatus
	}
	return ReqSettleStatus_ReqSettleStatusALL
}

type GetGuildYuyinExtraIncomeRsp struct {
	List                 []*ExtraIncome `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *GetGuildYuyinExtraIncomeRsp) Reset()         { *m = GetGuildYuyinExtraIncomeRsp{} }
func (m *GetGuildYuyinExtraIncomeRsp) String() string { return proto.CompactTextString(m) }
func (*GetGuildYuyinExtraIncomeRsp) ProtoMessage()    {}
func (*GetGuildYuyinExtraIncomeRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_gold_commission_18e8a43e3dce6c6d, []int{48}
}
func (m *GetGuildYuyinExtraIncomeRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGuildYuyinExtraIncomeRsp.Unmarshal(m, b)
}
func (m *GetGuildYuyinExtraIncomeRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGuildYuyinExtraIncomeRsp.Marshal(b, m, deterministic)
}
func (dst *GetGuildYuyinExtraIncomeRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGuildYuyinExtraIncomeRsp.Merge(dst, src)
}
func (m *GetGuildYuyinExtraIncomeRsp) XXX_Size() int {
	return xxx_messageInfo_GetGuildYuyinExtraIncomeRsp.Size(m)
}
func (m *GetGuildYuyinExtraIncomeRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGuildYuyinExtraIncomeRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GetGuildYuyinExtraIncomeRsp proto.InternalMessageInfo

func (m *GetGuildYuyinExtraIncomeRsp) GetList() []*ExtraIncome {
	if m != nil {
		return m.List
	}
	return nil
}

// ExtraIncome 额外收益记录
// 额外收益+当月公会任务奖励分成比例 的记录
type ExtraIncome struct {
	MonthRecordIncome    uint64   `protobuf:"varint,1,opt,name=month_record_income,json=monthRecordIncome,proto3" json:"month_record_income,omitempty"`
	ExtraIncomeRatio     uint32   `protobuf:"varint,2,opt,name=extra_income_ratio,json=extraIncomeRatio,proto3" json:"extra_income_ratio,omitempty"`
	Key                  string   `protobuf:"bytes,3,opt,name=key,proto3" json:"key,omitempty"`
	ExtraIncomeRatioV2   string   `protobuf:"bytes,4,opt,name=extra_income_ratio_v2,json=extraIncomeRatioV2,proto3" json:"extra_income_ratio_v2,omitempty"`
	GuildId              uint32   `protobuf:"varint,5,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ExtraIncome) Reset()         { *m = ExtraIncome{} }
func (m *ExtraIncome) String() string { return proto.CompactTextString(m) }
func (*ExtraIncome) ProtoMessage()    {}
func (*ExtraIncome) Descriptor() ([]byte, []int) {
	return fileDescriptor_gold_commission_18e8a43e3dce6c6d, []int{49}
}
func (m *ExtraIncome) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ExtraIncome.Unmarshal(m, b)
}
func (m *ExtraIncome) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ExtraIncome.Marshal(b, m, deterministic)
}
func (dst *ExtraIncome) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ExtraIncome.Merge(dst, src)
}
func (m *ExtraIncome) XXX_Size() int {
	return xxx_messageInfo_ExtraIncome.Size(m)
}
func (m *ExtraIncome) XXX_DiscardUnknown() {
	xxx_messageInfo_ExtraIncome.DiscardUnknown(m)
}

var xxx_messageInfo_ExtraIncome proto.InternalMessageInfo

func (m *ExtraIncome) GetMonthRecordIncome() uint64 {
	if m != nil {
		return m.MonthRecordIncome
	}
	return 0
}

func (m *ExtraIncome) GetExtraIncomeRatio() uint32 {
	if m != nil {
		return m.ExtraIncomeRatio
	}
	return 0
}

func (m *ExtraIncome) GetKey() string {
	if m != nil {
		return m.Key
	}
	return ""
}

func (m *ExtraIncome) GetExtraIncomeRatioV2() string {
	if m != nil {
		return m.ExtraIncomeRatioV2
	}
	return ""
}

func (m *ExtraIncome) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

// 获取语音直播工会任务列表
type GetGuildYuyinTaskListReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	GuildId              uint32   `protobuf:"varint,2,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGuildYuyinTaskListReq) Reset()         { *m = GetGuildYuyinTaskListReq{} }
func (m *GetGuildYuyinTaskListReq) String() string { return proto.CompactTextString(m) }
func (*GetGuildYuyinTaskListReq) ProtoMessage()    {}
func (*GetGuildYuyinTaskListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_gold_commission_18e8a43e3dce6c6d, []int{50}
}
func (m *GetGuildYuyinTaskListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGuildYuyinTaskListReq.Unmarshal(m, b)
}
func (m *GetGuildYuyinTaskListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGuildYuyinTaskListReq.Marshal(b, m, deterministic)
}
func (dst *GetGuildYuyinTaskListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGuildYuyinTaskListReq.Merge(dst, src)
}
func (m *GetGuildYuyinTaskListReq) XXX_Size() int {
	return xxx_messageInfo_GetGuildYuyinTaskListReq.Size(m)
}
func (m *GetGuildYuyinTaskListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGuildYuyinTaskListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetGuildYuyinTaskListReq proto.InternalMessageInfo

func (m *GetGuildYuyinTaskListReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetGuildYuyinTaskListReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

type GetGuildYuyinTaskListRsp struct {
	List                 []*GuildTaskInfo       `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	RemainTime           int64                  `protobuf:"varint,2,opt,name=remain_time,json=remainTime,proto3" json:"remain_time,omitempty"`
	BuffTotal            string                 `protobuf:"bytes,3,opt,name=buff_total,json=buffTotal,proto3" json:"buff_total,omitempty"`
	TaskDetail           []*GuildTaskDetailInfo `protobuf:"bytes,4,rep,name=task_detail,json=taskDetail,proto3" json:"task_detail,omitempty"`
	IsNewGuild           uint32                 `protobuf:"varint,5,opt,name=is_new_guild,json=isNewGuild,proto3" json:"is_new_guild,omitempty"`
	NewGuildPeriod       string                 `protobuf:"bytes,6,opt,name=new_guild_period,json=newGuildPeriod,proto3" json:"new_guild_period,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *GetGuildYuyinTaskListRsp) Reset()         { *m = GetGuildYuyinTaskListRsp{} }
func (m *GetGuildYuyinTaskListRsp) String() string { return proto.CompactTextString(m) }
func (*GetGuildYuyinTaskListRsp) ProtoMessage()    {}
func (*GetGuildYuyinTaskListRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_gold_commission_18e8a43e3dce6c6d, []int{51}
}
func (m *GetGuildYuyinTaskListRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGuildYuyinTaskListRsp.Unmarshal(m, b)
}
func (m *GetGuildYuyinTaskListRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGuildYuyinTaskListRsp.Marshal(b, m, deterministic)
}
func (dst *GetGuildYuyinTaskListRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGuildYuyinTaskListRsp.Merge(dst, src)
}
func (m *GetGuildYuyinTaskListRsp) XXX_Size() int {
	return xxx_messageInfo_GetGuildYuyinTaskListRsp.Size(m)
}
func (m *GetGuildYuyinTaskListRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGuildYuyinTaskListRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GetGuildYuyinTaskListRsp proto.InternalMessageInfo

func (m *GetGuildYuyinTaskListRsp) GetList() []*GuildTaskInfo {
	if m != nil {
		return m.List
	}
	return nil
}

func (m *GetGuildYuyinTaskListRsp) GetRemainTime() int64 {
	if m != nil {
		return m.RemainTime
	}
	return 0
}

func (m *GetGuildYuyinTaskListRsp) GetBuffTotal() string {
	if m != nil {
		return m.BuffTotal
	}
	return ""
}

func (m *GetGuildYuyinTaskListRsp) GetTaskDetail() []*GuildTaskDetailInfo {
	if m != nil {
		return m.TaskDetail
	}
	return nil
}

func (m *GetGuildYuyinTaskListRsp) GetIsNewGuild() uint32 {
	if m != nil {
		return m.IsNewGuild
	}
	return 0
}

func (m *GetGuildYuyinTaskListRsp) GetNewGuildPeriod() string {
	if m != nil {
		return m.NewGuildPeriod
	}
	return ""
}

// GuildTaskInfo 公会任务信息
type GuildTaskInfo struct {
	Value                uint64   `protobuf:"varint,1,opt,name=value,proto3" json:"value,omitempty"`
	Ratio                string   `protobuf:"bytes,2,opt,name=ratio,proto3" json:"ratio,omitempty"`
	Level                uint32   `protobuf:"varint,3,opt,name=level,proto3" json:"level,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GuildTaskInfo) Reset()         { *m = GuildTaskInfo{} }
func (m *GuildTaskInfo) String() string { return proto.CompactTextString(m) }
func (*GuildTaskInfo) ProtoMessage()    {}
func (*GuildTaskInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_gold_commission_18e8a43e3dce6c6d, []int{52}
}
func (m *GuildTaskInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GuildTaskInfo.Unmarshal(m, b)
}
func (m *GuildTaskInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GuildTaskInfo.Marshal(b, m, deterministic)
}
func (dst *GuildTaskInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GuildTaskInfo.Merge(dst, src)
}
func (m *GuildTaskInfo) XXX_Size() int {
	return xxx_messageInfo_GuildTaskInfo.Size(m)
}
func (m *GuildTaskInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_GuildTaskInfo.DiscardUnknown(m)
}

var xxx_messageInfo_GuildTaskInfo proto.InternalMessageInfo

func (m *GuildTaskInfo) GetValue() uint64 {
	if m != nil {
		return m.Value
	}
	return 0
}

func (m *GuildTaskInfo) GetRatio() string {
	if m != nil {
		return m.Ratio
	}
	return ""
}

func (m *GuildTaskInfo) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

// 公会任务比例信息
type GuildTaskRatioInfo struct {
	Value                uint64   `protobuf:"varint,1,opt,name=value,proto3" json:"value,omitempty"`
	Ratio                string   `protobuf:"bytes,2,opt,name=ratio,proto3" json:"ratio,omitempty"`
	ExtraRatio           string   `protobuf:"bytes,3,opt,name=extra_ratio,json=extraRatio,proto3" json:"extra_ratio,omitempty"`
	Level                uint32   `protobuf:"varint,4,opt,name=level,proto3" json:"level,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GuildTaskRatioInfo) Reset()         { *m = GuildTaskRatioInfo{} }
func (m *GuildTaskRatioInfo) String() string { return proto.CompactTextString(m) }
func (*GuildTaskRatioInfo) ProtoMessage()    {}
func (*GuildTaskRatioInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_gold_commission_18e8a43e3dce6c6d, []int{53}
}
func (m *GuildTaskRatioInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GuildTaskRatioInfo.Unmarshal(m, b)
}
func (m *GuildTaskRatioInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GuildTaskRatioInfo.Marshal(b, m, deterministic)
}
func (dst *GuildTaskRatioInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GuildTaskRatioInfo.Merge(dst, src)
}
func (m *GuildTaskRatioInfo) XXX_Size() int {
	return xxx_messageInfo_GuildTaskRatioInfo.Size(m)
}
func (m *GuildTaskRatioInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_GuildTaskRatioInfo.DiscardUnknown(m)
}

var xxx_messageInfo_GuildTaskRatioInfo proto.InternalMessageInfo

func (m *GuildTaskRatioInfo) GetValue() uint64 {
	if m != nil {
		return m.Value
	}
	return 0
}

func (m *GuildTaskRatioInfo) GetRatio() string {
	if m != nil {
		return m.Ratio
	}
	return ""
}

func (m *GuildTaskRatioInfo) GetExtraRatio() string {
	if m != nil {
		return m.ExtraRatio
	}
	return ""
}

func (m *GuildTaskRatioInfo) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

// 公会任务详情
type GuildTaskDetailInfo struct {
	Name                 string                `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	RatioList            []*GuildTaskRatioInfo `protobuf:"bytes,2,rep,name=ratio_list,json=ratioList,proto3" json:"ratio_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *GuildTaskDetailInfo) Reset()         { *m = GuildTaskDetailInfo{} }
func (m *GuildTaskDetailInfo) String() string { return proto.CompactTextString(m) }
func (*GuildTaskDetailInfo) ProtoMessage()    {}
func (*GuildTaskDetailInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_gold_commission_18e8a43e3dce6c6d, []int{54}
}
func (m *GuildTaskDetailInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GuildTaskDetailInfo.Unmarshal(m, b)
}
func (m *GuildTaskDetailInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GuildTaskDetailInfo.Marshal(b, m, deterministic)
}
func (dst *GuildTaskDetailInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GuildTaskDetailInfo.Merge(dst, src)
}
func (m *GuildTaskDetailInfo) XXX_Size() int {
	return xxx_messageInfo_GuildTaskDetailInfo.Size(m)
}
func (m *GuildTaskDetailInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_GuildTaskDetailInfo.DiscardUnknown(m)
}

var xxx_messageInfo_GuildTaskDetailInfo proto.InternalMessageInfo

func (m *GuildTaskDetailInfo) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *GuildTaskDetailInfo) GetRatioList() []*GuildTaskRatioInfo {
	if m != nil {
		return m.RatioList
	}
	return nil
}

type GetGuildDayIncomeListReq struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	Datetime             uint64   `protobuf:"varint,2,opt,name=datetime,proto3" json:"datetime,omitempty"`
	Offset               uint32   `protobuf:"varint,3,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32   `protobuf:"varint,4,opt,name=limit,proto3" json:"limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGuildDayIncomeListReq) Reset()         { *m = GetGuildDayIncomeListReq{} }
func (m *GetGuildDayIncomeListReq) String() string { return proto.CompactTextString(m) }
func (*GetGuildDayIncomeListReq) ProtoMessage()    {}
func (*GetGuildDayIncomeListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_gold_commission_18e8a43e3dce6c6d, []int{55}
}
func (m *GetGuildDayIncomeListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGuildDayIncomeListReq.Unmarshal(m, b)
}
func (m *GetGuildDayIncomeListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGuildDayIncomeListReq.Marshal(b, m, deterministic)
}
func (dst *GetGuildDayIncomeListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGuildDayIncomeListReq.Merge(dst, src)
}
func (m *GetGuildDayIncomeListReq) XXX_Size() int {
	return xxx_messageInfo_GetGuildDayIncomeListReq.Size(m)
}
func (m *GetGuildDayIncomeListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGuildDayIncomeListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetGuildDayIncomeListReq proto.InternalMessageInfo

func (m *GetGuildDayIncomeListReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GetGuildDayIncomeListReq) GetDatetime() uint64 {
	if m != nil {
		return m.Datetime
	}
	return 0
}

func (m *GetGuildDayIncomeListReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetGuildDayIncomeListReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type GetYuyinGuildDayIncomeListResp struct {
	AnchorList           []*BaseAnchorInfo `protobuf:"bytes,1,rep,name=anchor_list,json=anchorList,proto3" json:"anchor_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetYuyinGuildDayIncomeListResp) Reset()         { *m = GetYuyinGuildDayIncomeListResp{} }
func (m *GetYuyinGuildDayIncomeListResp) String() string { return proto.CompactTextString(m) }
func (*GetYuyinGuildDayIncomeListResp) ProtoMessage()    {}
func (*GetYuyinGuildDayIncomeListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_gold_commission_18e8a43e3dce6c6d, []int{56}
}
func (m *GetYuyinGuildDayIncomeListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetYuyinGuildDayIncomeListResp.Unmarshal(m, b)
}
func (m *GetYuyinGuildDayIncomeListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetYuyinGuildDayIncomeListResp.Marshal(b, m, deterministic)
}
func (dst *GetYuyinGuildDayIncomeListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetYuyinGuildDayIncomeListResp.Merge(dst, src)
}
func (m *GetYuyinGuildDayIncomeListResp) XXX_Size() int {
	return xxx_messageInfo_GetYuyinGuildDayIncomeListResp.Size(m)
}
func (m *GetYuyinGuildDayIncomeListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetYuyinGuildDayIncomeListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetYuyinGuildDayIncomeListResp proto.InternalMessageInfo

func (m *GetYuyinGuildDayIncomeListResp) GetAnchorList() []*BaseAnchorInfo {
	if m != nil {
		return m.AnchorList
	}
	return nil
}

type GetAmuseGuildDayIncomeListResp struct {
	ChannelList          []*BaseChannelInfo `protobuf:"bytes,1,rep,name=channel_list,json=channelList,proto3" json:"channel_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *GetAmuseGuildDayIncomeListResp) Reset()         { *m = GetAmuseGuildDayIncomeListResp{} }
func (m *GetAmuseGuildDayIncomeListResp) String() string { return proto.CompactTextString(m) }
func (*GetAmuseGuildDayIncomeListResp) ProtoMessage()    {}
func (*GetAmuseGuildDayIncomeListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_gold_commission_18e8a43e3dce6c6d, []int{57}
}
func (m *GetAmuseGuildDayIncomeListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAmuseGuildDayIncomeListResp.Unmarshal(m, b)
}
func (m *GetAmuseGuildDayIncomeListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAmuseGuildDayIncomeListResp.Marshal(b, m, deterministic)
}
func (dst *GetAmuseGuildDayIncomeListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAmuseGuildDayIncomeListResp.Merge(dst, src)
}
func (m *GetAmuseGuildDayIncomeListResp) XXX_Size() int {
	return xxx_messageInfo_GetAmuseGuildDayIncomeListResp.Size(m)
}
func (m *GetAmuseGuildDayIncomeListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAmuseGuildDayIncomeListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAmuseGuildDayIncomeListResp proto.InternalMessageInfo

func (m *GetAmuseGuildDayIncomeListResp) GetChannelList() []*BaseChannelInfo {
	if m != nil {
		return m.ChannelList
	}
	return nil
}

type GetInteractGameDayIncomeListResp struct {
	AnchorList           []*BaseAnchorInfo `protobuf:"bytes,1,rep,name=anchor_list,json=anchorList,proto3" json:"anchor_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetInteractGameDayIncomeListResp) Reset()         { *m = GetInteractGameDayIncomeListResp{} }
func (m *GetInteractGameDayIncomeListResp) String() string { return proto.CompactTextString(m) }
func (*GetInteractGameDayIncomeListResp) ProtoMessage()    {}
func (*GetInteractGameDayIncomeListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_gold_commission_18e8a43e3dce6c6d, []int{58}
}
func (m *GetInteractGameDayIncomeListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetInteractGameDayIncomeListResp.Unmarshal(m, b)
}
func (m *GetInteractGameDayIncomeListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetInteractGameDayIncomeListResp.Marshal(b, m, deterministic)
}
func (dst *GetInteractGameDayIncomeListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetInteractGameDayIncomeListResp.Merge(dst, src)
}
func (m *GetInteractGameDayIncomeListResp) XXX_Size() int {
	return xxx_messageInfo_GetInteractGameDayIncomeListResp.Size(m)
}
func (m *GetInteractGameDayIncomeListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetInteractGameDayIncomeListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetInteractGameDayIncomeListResp proto.InternalMessageInfo

func (m *GetInteractGameDayIncomeListResp) GetAnchorList() []*BaseAnchorInfo {
	if m != nil {
		return m.AnchorList
	}
	return nil
}

type GetESportDayIncomeListResp struct {
	CoachList            []*BaseAnchorInfo `protobuf:"bytes,1,rep,name=coach_list,json=coachList,proto3" json:"coach_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetESportDayIncomeListResp) Reset()         { *m = GetESportDayIncomeListResp{} }
func (m *GetESportDayIncomeListResp) String() string { return proto.CompactTextString(m) }
func (*GetESportDayIncomeListResp) ProtoMessage()    {}
func (*GetESportDayIncomeListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_gold_commission_18e8a43e3dce6c6d, []int{59}
}
func (m *GetESportDayIncomeListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetESportDayIncomeListResp.Unmarshal(m, b)
}
func (m *GetESportDayIncomeListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetESportDayIncomeListResp.Marshal(b, m, deterministic)
}
func (dst *GetESportDayIncomeListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetESportDayIncomeListResp.Merge(dst, src)
}
func (m *GetESportDayIncomeListResp) XXX_Size() int {
	return xxx_messageInfo_GetESportDayIncomeListResp.Size(m)
}
func (m *GetESportDayIncomeListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetESportDayIncomeListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetESportDayIncomeListResp proto.InternalMessageInfo

func (m *GetESportDayIncomeListResp) GetCoachList() []*BaseAnchorInfo {
	if m != nil {
		return m.CoachList
	}
	return nil
}

type GetGuildMonthIncomeListReq struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	Datetime             uint64   `protobuf:"varint,2,opt,name=datetime,proto3" json:"datetime,omitempty"`
	Offset               uint32   `protobuf:"varint,3,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32   `protobuf:"varint,4,opt,name=limit,proto3" json:"limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGuildMonthIncomeListReq) Reset()         { *m = GetGuildMonthIncomeListReq{} }
func (m *GetGuildMonthIncomeListReq) String() string { return proto.CompactTextString(m) }
func (*GetGuildMonthIncomeListReq) ProtoMessage()    {}
func (*GetGuildMonthIncomeListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_gold_commission_18e8a43e3dce6c6d, []int{60}
}
func (m *GetGuildMonthIncomeListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGuildMonthIncomeListReq.Unmarshal(m, b)
}
func (m *GetGuildMonthIncomeListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGuildMonthIncomeListReq.Marshal(b, m, deterministic)
}
func (dst *GetGuildMonthIncomeListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGuildMonthIncomeListReq.Merge(dst, src)
}
func (m *GetGuildMonthIncomeListReq) XXX_Size() int {
	return xxx_messageInfo_GetGuildMonthIncomeListReq.Size(m)
}
func (m *GetGuildMonthIncomeListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGuildMonthIncomeListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetGuildMonthIncomeListReq proto.InternalMessageInfo

func (m *GetGuildMonthIncomeListReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GetGuildMonthIncomeListReq) GetDatetime() uint64 {
	if m != nil {
		return m.Datetime
	}
	return 0
}

func (m *GetGuildMonthIncomeListReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetGuildMonthIncomeListReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type GetGuildMonthIncomeListResp struct {
	GuildList            []*BaseGuildInfo `protobuf:"bytes,1,rep,name=guild_list,json=guildList,proto3" json:"guild_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GetGuildMonthIncomeListResp) Reset()         { *m = GetGuildMonthIncomeListResp{} }
func (m *GetGuildMonthIncomeListResp) String() string { return proto.CompactTextString(m) }
func (*GetGuildMonthIncomeListResp) ProtoMessage()    {}
func (*GetGuildMonthIncomeListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_gold_commission_18e8a43e3dce6c6d, []int{61}
}
func (m *GetGuildMonthIncomeListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGuildMonthIncomeListResp.Unmarshal(m, b)
}
func (m *GetGuildMonthIncomeListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGuildMonthIncomeListResp.Marshal(b, m, deterministic)
}
func (dst *GetGuildMonthIncomeListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGuildMonthIncomeListResp.Merge(dst, src)
}
func (m *GetGuildMonthIncomeListResp) XXX_Size() int {
	return xxx_messageInfo_GetGuildMonthIncomeListResp.Size(m)
}
func (m *GetGuildMonthIncomeListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGuildMonthIncomeListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetGuildMonthIncomeListResp proto.InternalMessageInfo

func (m *GetGuildMonthIncomeListResp) GetGuildList() []*BaseGuildInfo {
	if m != nil {
		return m.GuildList
	}
	return nil
}

type GetInteractGameMonthIncomeListResp struct {
	AnchorList           []*BaseAnchorInfo `protobuf:"bytes,1,rep,name=anchor_list,json=anchorList,proto3" json:"anchor_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetInteractGameMonthIncomeListResp) Reset()         { *m = GetInteractGameMonthIncomeListResp{} }
func (m *GetInteractGameMonthIncomeListResp) String() string { return proto.CompactTextString(m) }
func (*GetInteractGameMonthIncomeListResp) ProtoMessage()    {}
func (*GetInteractGameMonthIncomeListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_gold_commission_18e8a43e3dce6c6d, []int{62}
}
func (m *GetInteractGameMonthIncomeListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetInteractGameMonthIncomeListResp.Unmarshal(m, b)
}
func (m *GetInteractGameMonthIncomeListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetInteractGameMonthIncomeListResp.Marshal(b, m, deterministic)
}
func (dst *GetInteractGameMonthIncomeListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetInteractGameMonthIncomeListResp.Merge(dst, src)
}
func (m *GetInteractGameMonthIncomeListResp) XXX_Size() int {
	return xxx_messageInfo_GetInteractGameMonthIncomeListResp.Size(m)
}
func (m *GetInteractGameMonthIncomeListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetInteractGameMonthIncomeListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetInteractGameMonthIncomeListResp proto.InternalMessageInfo

func (m *GetInteractGameMonthIncomeListResp) GetAnchorList() []*BaseAnchorInfo {
	if m != nil {
		return m.AnchorList
	}
	return nil
}

type GetESportMonthIncomeListResp struct {
	GuildList            []*BaseGuildInfo `protobuf:"bytes,1,rep,name=guild_list,json=guildList,proto3" json:"guild_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GetESportMonthIncomeListResp) Reset()         { *m = GetESportMonthIncomeListResp{} }
func (m *GetESportMonthIncomeListResp) String() string { return proto.CompactTextString(m) }
func (*GetESportMonthIncomeListResp) ProtoMessage()    {}
func (*GetESportMonthIncomeListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_gold_commission_18e8a43e3dce6c6d, []int{63}
}
func (m *GetESportMonthIncomeListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetESportMonthIncomeListResp.Unmarshal(m, b)
}
func (m *GetESportMonthIncomeListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetESportMonthIncomeListResp.Marshal(b, m, deterministic)
}
func (dst *GetESportMonthIncomeListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetESportMonthIncomeListResp.Merge(dst, src)
}
func (m *GetESportMonthIncomeListResp) XXX_Size() int {
	return xxx_messageInfo_GetESportMonthIncomeListResp.Size(m)
}
func (m *GetESportMonthIncomeListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetESportMonthIncomeListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetESportMonthIncomeListResp proto.InternalMessageInfo

func (m *GetESportMonthIncomeListResp) GetGuildList() []*BaseGuildInfo {
	if m != nil {
		return m.GuildList
	}
	return nil
}

type GetGuildMonthMemberListReq struct {
	ChannelType          ChannelType                          `protobuf:"varint,1,opt,name=channel_type,json=channelType,proto3,enum=gold_commission.ChannelType" json:"channel_type,omitempty"`
	GuildId              uint32                               `protobuf:"varint,2,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	Datetime             uint64                               `protobuf:"varint,3,opt,name=datetime,proto3" json:"datetime,omitempty"`
	Offset               uint32                               `protobuf:"varint,4,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32                               `protobuf:"varint,5,opt,name=limit,proto3" json:"limit,omitempty"`
	Type                 GetGuildMonthMemberListReq_QueryType `protobuf:"varint,6,opt,name=type,proto3,enum=gold_commission.GetGuildMonthMemberListReq_QueryType" json:"type,omitempty"`
	GoldType             GoldType                             `protobuf:"varint,7,opt,name=gold_type,json=goldType,proto3,enum=gold_commission.GoldType" json:"gold_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                             `json:"-"`
	XXX_unrecognized     []byte                               `json:"-"`
	XXX_sizecache        int32                                `json:"-"`
}

func (m *GetGuildMonthMemberListReq) Reset()         { *m = GetGuildMonthMemberListReq{} }
func (m *GetGuildMonthMemberListReq) String() string { return proto.CompactTextString(m) }
func (*GetGuildMonthMemberListReq) ProtoMessage()    {}
func (*GetGuildMonthMemberListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_gold_commission_18e8a43e3dce6c6d, []int{64}
}
func (m *GetGuildMonthMemberListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGuildMonthMemberListReq.Unmarshal(m, b)
}
func (m *GetGuildMonthMemberListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGuildMonthMemberListReq.Marshal(b, m, deterministic)
}
func (dst *GetGuildMonthMemberListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGuildMonthMemberListReq.Merge(dst, src)
}
func (m *GetGuildMonthMemberListReq) XXX_Size() int {
	return xxx_messageInfo_GetGuildMonthMemberListReq.Size(m)
}
func (m *GetGuildMonthMemberListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGuildMonthMemberListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetGuildMonthMemberListReq proto.InternalMessageInfo

func (m *GetGuildMonthMemberListReq) GetChannelType() ChannelType {
	if m != nil {
		return m.ChannelType
	}
	return ChannelType_CTypeInvalid
}

func (m *GetGuildMonthMemberListReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GetGuildMonthMemberListReq) GetDatetime() uint64 {
	if m != nil {
		return m.Datetime
	}
	return 0
}

func (m *GetGuildMonthMemberListReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetGuildMonthMemberListReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *GetGuildMonthMemberListReq) GetType() GetGuildMonthMemberListReq_QueryType {
	if m != nil {
		return m.Type
	}
	return GetGuildMonthMemberListReq_UNKNOWN
}

func (m *GetGuildMonthMemberListReq) GetGoldType() GoldType {
	if m != nil {
		return m.GoldType
	}
	return GoldType_UNKNOWN_GOLD
}

type GetGuildMonthMemberListResp struct {
	// 根据房间类型返回其中之一
	AnchorList           []*BaseAnchorInfo  `protobuf:"bytes,1,rep,name=anchor_list,json=anchorList,proto3" json:"anchor_list,omitempty"`
	ChannelList          []*BaseChannelInfo `protobuf:"bytes,2,rep,name=channel_list,json=channelList,proto3" json:"channel_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *GetGuildMonthMemberListResp) Reset()         { *m = GetGuildMonthMemberListResp{} }
func (m *GetGuildMonthMemberListResp) String() string { return proto.CompactTextString(m) }
func (*GetGuildMonthMemberListResp) ProtoMessage()    {}
func (*GetGuildMonthMemberListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_gold_commission_18e8a43e3dce6c6d, []int{65}
}
func (m *GetGuildMonthMemberListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGuildMonthMemberListResp.Unmarshal(m, b)
}
func (m *GetGuildMonthMemberListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGuildMonthMemberListResp.Marshal(b, m, deterministic)
}
func (dst *GetGuildMonthMemberListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGuildMonthMemberListResp.Merge(dst, src)
}
func (m *GetGuildMonthMemberListResp) XXX_Size() int {
	return xxx_messageInfo_GetGuildMonthMemberListResp.Size(m)
}
func (m *GetGuildMonthMemberListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGuildMonthMemberListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetGuildMonthMemberListResp proto.InternalMessageInfo

func (m *GetGuildMonthMemberListResp) GetAnchorList() []*BaseAnchorInfo {
	if m != nil {
		return m.AnchorList
	}
	return nil
}

func (m *GetGuildMonthMemberListResp) GetChannelList() []*BaseChannelInfo {
	if m != nil {
		return m.ChannelList
	}
	return nil
}

type SearchYuyinGuildDetailReq struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	Offset               uint32   `protobuf:"varint,3,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32   `protobuf:"varint,4,opt,name=limit,proto3" json:"limit,omitempty"`
	Start                uint64   `protobuf:"varint,5,opt,name=start,proto3" json:"start,omitempty"`
	End                  uint64   `protobuf:"varint,6,opt,name=end,proto3" json:"end,omitempty"`
	AnchorId             uint32   `protobuf:"varint,7,opt,name=anchor_id,json=anchorId,proto3" json:"anchor_id,omitempty"`
	PaidUid              uint32   `protobuf:"varint,8,opt,name=paid_uid,json=paidUid,proto3" json:"paid_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SearchYuyinGuildDetailReq) Reset()         { *m = SearchYuyinGuildDetailReq{} }
func (m *SearchYuyinGuildDetailReq) String() string { return proto.CompactTextString(m) }
func (*SearchYuyinGuildDetailReq) ProtoMessage()    {}
func (*SearchYuyinGuildDetailReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_gold_commission_18e8a43e3dce6c6d, []int{66}
}
func (m *SearchYuyinGuildDetailReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SearchYuyinGuildDetailReq.Unmarshal(m, b)
}
func (m *SearchYuyinGuildDetailReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SearchYuyinGuildDetailReq.Marshal(b, m, deterministic)
}
func (dst *SearchYuyinGuildDetailReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SearchYuyinGuildDetailReq.Merge(dst, src)
}
func (m *SearchYuyinGuildDetailReq) XXX_Size() int {
	return xxx_messageInfo_SearchYuyinGuildDetailReq.Size(m)
}
func (m *SearchYuyinGuildDetailReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SearchYuyinGuildDetailReq.DiscardUnknown(m)
}

var xxx_messageInfo_SearchYuyinGuildDetailReq proto.InternalMessageInfo

func (m *SearchYuyinGuildDetailReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *SearchYuyinGuildDetailReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *SearchYuyinGuildDetailReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *SearchYuyinGuildDetailReq) GetStart() uint64 {
	if m != nil {
		return m.Start
	}
	return 0
}

func (m *SearchYuyinGuildDetailReq) GetEnd() uint64 {
	if m != nil {
		return m.End
	}
	return 0
}

func (m *SearchYuyinGuildDetailReq) GetAnchorId() uint32 {
	if m != nil {
		return m.AnchorId
	}
	return 0
}

func (m *SearchYuyinGuildDetailReq) GetPaidUid() uint32 {
	if m != nil {
		return m.PaidUid
	}
	return 0
}

type SearchYuyinGuildDetailResp struct {
	DetailList           []*BaseAnchorInfo `protobuf:"bytes,1,rep,name=detail_list,json=detailList,proto3" json:"detail_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *SearchYuyinGuildDetailResp) Reset()         { *m = SearchYuyinGuildDetailResp{} }
func (m *SearchYuyinGuildDetailResp) String() string { return proto.CompactTextString(m) }
func (*SearchYuyinGuildDetailResp) ProtoMessage()    {}
func (*SearchYuyinGuildDetailResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_gold_commission_18e8a43e3dce6c6d, []int{67}
}
func (m *SearchYuyinGuildDetailResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SearchYuyinGuildDetailResp.Unmarshal(m, b)
}
func (m *SearchYuyinGuildDetailResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SearchYuyinGuildDetailResp.Marshal(b, m, deterministic)
}
func (dst *SearchYuyinGuildDetailResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SearchYuyinGuildDetailResp.Merge(dst, src)
}
func (m *SearchYuyinGuildDetailResp) XXX_Size() int {
	return xxx_messageInfo_SearchYuyinGuildDetailResp.Size(m)
}
func (m *SearchYuyinGuildDetailResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SearchYuyinGuildDetailResp.DiscardUnknown(m)
}

var xxx_messageInfo_SearchYuyinGuildDetailResp proto.InternalMessageInfo

func (m *SearchYuyinGuildDetailResp) GetDetailList() []*BaseAnchorInfo {
	if m != nil {
		return m.DetailList
	}
	return nil
}

type SearchAmuseGuildDetailReq struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	Offset               uint32   `protobuf:"varint,3,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32   `protobuf:"varint,4,opt,name=limit,proto3" json:"limit,omitempty"`
	Start                uint64   `protobuf:"varint,5,opt,name=start,proto3" json:"start,omitempty"`
	End                  uint64   `protobuf:"varint,6,opt,name=end,proto3" json:"end,omitempty"`
	ChannelId            uint32   `protobuf:"varint,7,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	PaidUid              uint32   `protobuf:"varint,8,opt,name=paid_uid,json=paidUid,proto3" json:"paid_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SearchAmuseGuildDetailReq) Reset()         { *m = SearchAmuseGuildDetailReq{} }
func (m *SearchAmuseGuildDetailReq) String() string { return proto.CompactTextString(m) }
func (*SearchAmuseGuildDetailReq) ProtoMessage()    {}
func (*SearchAmuseGuildDetailReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_gold_commission_18e8a43e3dce6c6d, []int{68}
}
func (m *SearchAmuseGuildDetailReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SearchAmuseGuildDetailReq.Unmarshal(m, b)
}
func (m *SearchAmuseGuildDetailReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SearchAmuseGuildDetailReq.Marshal(b, m, deterministic)
}
func (dst *SearchAmuseGuildDetailReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SearchAmuseGuildDetailReq.Merge(dst, src)
}
func (m *SearchAmuseGuildDetailReq) XXX_Size() int {
	return xxx_messageInfo_SearchAmuseGuildDetailReq.Size(m)
}
func (m *SearchAmuseGuildDetailReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SearchAmuseGuildDetailReq.DiscardUnknown(m)
}

var xxx_messageInfo_SearchAmuseGuildDetailReq proto.InternalMessageInfo

func (m *SearchAmuseGuildDetailReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *SearchAmuseGuildDetailReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *SearchAmuseGuildDetailReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *SearchAmuseGuildDetailReq) GetStart() uint64 {
	if m != nil {
		return m.Start
	}
	return 0
}

func (m *SearchAmuseGuildDetailReq) GetEnd() uint64 {
	if m != nil {
		return m.End
	}
	return 0
}

func (m *SearchAmuseGuildDetailReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SearchAmuseGuildDetailReq) GetPaidUid() uint32 {
	if m != nil {
		return m.PaidUid
	}
	return 0
}

type SearchAmuseGuildDetailResp struct {
	DetailList           []*BaseChannelInfo `protobuf:"bytes,1,rep,name=detail_list,json=detailList,proto3" json:"detail_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *SearchAmuseGuildDetailResp) Reset()         { *m = SearchAmuseGuildDetailResp{} }
func (m *SearchAmuseGuildDetailResp) String() string { return proto.CompactTextString(m) }
func (*SearchAmuseGuildDetailResp) ProtoMessage()    {}
func (*SearchAmuseGuildDetailResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_gold_commission_18e8a43e3dce6c6d, []int{69}
}
func (m *SearchAmuseGuildDetailResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SearchAmuseGuildDetailResp.Unmarshal(m, b)
}
func (m *SearchAmuseGuildDetailResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SearchAmuseGuildDetailResp.Marshal(b, m, deterministic)
}
func (dst *SearchAmuseGuildDetailResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SearchAmuseGuildDetailResp.Merge(dst, src)
}
func (m *SearchAmuseGuildDetailResp) XXX_Size() int {
	return xxx_messageInfo_SearchAmuseGuildDetailResp.Size(m)
}
func (m *SearchAmuseGuildDetailResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SearchAmuseGuildDetailResp.DiscardUnknown(m)
}

var xxx_messageInfo_SearchAmuseGuildDetailResp proto.InternalMessageInfo

func (m *SearchAmuseGuildDetailResp) GetDetailList() []*BaseChannelInfo {
	if m != nil {
		return m.DetailList
	}
	return nil
}

type GetGuildDayQoqReq struct {
	GuildId              uint32      `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	DayTime              uint64      `protobuf:"varint,2,opt,name=day_time,json=dayTime,proto3" json:"day_time,omitempty"`
	ChannelType          ChannelType `protobuf:"varint,3,opt,name=channel_type,json=channelType,proto3,enum=gold_commission.ChannelType" json:"channel_type,omitempty"`
	GoldType             GoldType    `protobuf:"varint,4,opt,name=gold_type,json=goldType,proto3,enum=gold_commission.GoldType" json:"gold_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *GetGuildDayQoqReq) Reset()         { *m = GetGuildDayQoqReq{} }
func (m *GetGuildDayQoqReq) String() string { return proto.CompactTextString(m) }
func (*GetGuildDayQoqReq) ProtoMessage()    {}
func (*GetGuildDayQoqReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_gold_commission_18e8a43e3dce6c6d, []int{70}
}
func (m *GetGuildDayQoqReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGuildDayQoqReq.Unmarshal(m, b)
}
func (m *GetGuildDayQoqReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGuildDayQoqReq.Marshal(b, m, deterministic)
}
func (dst *GetGuildDayQoqReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGuildDayQoqReq.Merge(dst, src)
}
func (m *GetGuildDayQoqReq) XXX_Size() int {
	return xxx_messageInfo_GetGuildDayQoqReq.Size(m)
}
func (m *GetGuildDayQoqReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGuildDayQoqReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetGuildDayQoqReq proto.InternalMessageInfo

func (m *GetGuildDayQoqReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GetGuildDayQoqReq) GetDayTime() uint64 {
	if m != nil {
		return m.DayTime
	}
	return 0
}

func (m *GetGuildDayQoqReq) GetChannelType() ChannelType {
	if m != nil {
		return m.ChannelType
	}
	return ChannelType_CTypeInvalid
}

func (m *GetGuildDayQoqReq) GetGoldType() GoldType {
	if m != nil {
		return m.GoldType
	}
	return GoldType_UNKNOWN_GOLD
}

type GetGuildDayQoqResp struct {
	DayTime              uint64   `protobuf:"varint,1,opt,name=day_time,json=dayTime,proto3" json:"day_time,omitempty"`
	Fee                  uint64   `protobuf:"varint,2,opt,name=fee,proto3" json:"fee,omitempty"`
	Income               uint64   `protobuf:"varint,3,opt,name=income,proto3" json:"income,omitempty"`
	CompareTime          uint64   `protobuf:"varint,4,opt,name=compare_time,json=compareTime,proto3" json:"compare_time,omitempty"`
	CompareFee           uint64   `protobuf:"varint,5,opt,name=compare_fee,json=compareFee,proto3" json:"compare_fee,omitempty"`
	CompareIncome        uint64   `protobuf:"varint,6,opt,name=compare_income,json=compareIncome,proto3" json:"compare_income,omitempty"`
	Qoq                  float64  `protobuf:"fixed64,7,opt,name=qoq,proto3" json:"qoq,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGuildDayQoqResp) Reset()         { *m = GetGuildDayQoqResp{} }
func (m *GetGuildDayQoqResp) String() string { return proto.CompactTextString(m) }
func (*GetGuildDayQoqResp) ProtoMessage()    {}
func (*GetGuildDayQoqResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_gold_commission_18e8a43e3dce6c6d, []int{71}
}
func (m *GetGuildDayQoqResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGuildDayQoqResp.Unmarshal(m, b)
}
func (m *GetGuildDayQoqResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGuildDayQoqResp.Marshal(b, m, deterministic)
}
func (dst *GetGuildDayQoqResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGuildDayQoqResp.Merge(dst, src)
}
func (m *GetGuildDayQoqResp) XXX_Size() int {
	return xxx_messageInfo_GetGuildDayQoqResp.Size(m)
}
func (m *GetGuildDayQoqResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGuildDayQoqResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetGuildDayQoqResp proto.InternalMessageInfo

func (m *GetGuildDayQoqResp) GetDayTime() uint64 {
	if m != nil {
		return m.DayTime
	}
	return 0
}

func (m *GetGuildDayQoqResp) GetFee() uint64 {
	if m != nil {
		return m.Fee
	}
	return 0
}

func (m *GetGuildDayQoqResp) GetIncome() uint64 {
	if m != nil {
		return m.Income
	}
	return 0
}

func (m *GetGuildDayQoqResp) GetCompareTime() uint64 {
	if m != nil {
		return m.CompareTime
	}
	return 0
}

func (m *GetGuildDayQoqResp) GetCompareFee() uint64 {
	if m != nil {
		return m.CompareFee
	}
	return 0
}

func (m *GetGuildDayQoqResp) GetCompareIncome() uint64 {
	if m != nil {
		return m.CompareIncome
	}
	return 0
}

func (m *GetGuildDayQoqResp) GetQoq() float64 {
	if m != nil {
		return m.Qoq
	}
	return 0
}

type AmuseGuildChannelIncomeListReq struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	ChannelId            []uint32 `protobuf:"varint,2,rep,packed,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Offset               uint32   `protobuf:"varint,3,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32   `protobuf:"varint,4,opt,name=limit,proto3" json:"limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AmuseGuildChannelIncomeListReq) Reset()         { *m = AmuseGuildChannelIncomeListReq{} }
func (m *AmuseGuildChannelIncomeListReq) String() string { return proto.CompactTextString(m) }
func (*AmuseGuildChannelIncomeListReq) ProtoMessage()    {}
func (*AmuseGuildChannelIncomeListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_gold_commission_18e8a43e3dce6c6d, []int{72}
}
func (m *AmuseGuildChannelIncomeListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AmuseGuildChannelIncomeListReq.Unmarshal(m, b)
}
func (m *AmuseGuildChannelIncomeListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AmuseGuildChannelIncomeListReq.Marshal(b, m, deterministic)
}
func (dst *AmuseGuildChannelIncomeListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AmuseGuildChannelIncomeListReq.Merge(dst, src)
}
func (m *AmuseGuildChannelIncomeListReq) XXX_Size() int {
	return xxx_messageInfo_AmuseGuildChannelIncomeListReq.Size(m)
}
func (m *AmuseGuildChannelIncomeListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AmuseGuildChannelIncomeListReq.DiscardUnknown(m)
}

var xxx_messageInfo_AmuseGuildChannelIncomeListReq proto.InternalMessageInfo

func (m *AmuseGuildChannelIncomeListReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *AmuseGuildChannelIncomeListReq) GetChannelId() []uint32 {
	if m != nil {
		return m.ChannelId
	}
	return nil
}

func (m *AmuseGuildChannelIncomeListReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *AmuseGuildChannelIncomeListReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type AmuseGuildChannelIncomeListResp struct {
	ChannelList          []*AmuseGuildChannelIncomeListRespLItem `protobuf:"bytes,1,rep,name=channel_list,json=channelList,proto3" json:"channel_list,omitempty"`
	Total                uint32                                  `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                                `json:"-"`
	XXX_unrecognized     []byte                                  `json:"-"`
	XXX_sizecache        int32                                   `json:"-"`
}

func (m *AmuseGuildChannelIncomeListResp) Reset()         { *m = AmuseGuildChannelIncomeListResp{} }
func (m *AmuseGuildChannelIncomeListResp) String() string { return proto.CompactTextString(m) }
func (*AmuseGuildChannelIncomeListResp) ProtoMessage()    {}
func (*AmuseGuildChannelIncomeListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_gold_commission_18e8a43e3dce6c6d, []int{73}
}
func (m *AmuseGuildChannelIncomeListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AmuseGuildChannelIncomeListResp.Unmarshal(m, b)
}
func (m *AmuseGuildChannelIncomeListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AmuseGuildChannelIncomeListResp.Marshal(b, m, deterministic)
}
func (dst *AmuseGuildChannelIncomeListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AmuseGuildChannelIncomeListResp.Merge(dst, src)
}
func (m *AmuseGuildChannelIncomeListResp) XXX_Size() int {
	return xxx_messageInfo_AmuseGuildChannelIncomeListResp.Size(m)
}
func (m *AmuseGuildChannelIncomeListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AmuseGuildChannelIncomeListResp.DiscardUnknown(m)
}

var xxx_messageInfo_AmuseGuildChannelIncomeListResp proto.InternalMessageInfo

func (m *AmuseGuildChannelIncomeListResp) GetChannelList() []*AmuseGuildChannelIncomeListRespLItem {
	if m != nil {
		return m.ChannelList
	}
	return nil
}

func (m *AmuseGuildChannelIncomeListResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

type AmuseGuildChannelIncomeListRespLItem struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Fee                  uint64   `protobuf:"varint,2,opt,name=fee,proto3" json:"fee,omitempty"`
	SamePeriodFee        uint64   `protobuf:"varint,3,opt,name=same_period_fee,json=samePeriodFee,proto3" json:"same_period_fee,omitempty"`
	SamePeriodQoq        float32  `protobuf:"fixed32,4,opt,name=same_period_qoq,json=samePeriodQoq,proto3" json:"same_period_qoq,omitempty"`
	LastMonthFee         uint64   `protobuf:"varint,5,opt,name=last_month_fee,json=lastMonthFee,proto3" json:"last_month_fee,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AmuseGuildChannelIncomeListRespLItem) Reset()         { *m = AmuseGuildChannelIncomeListRespLItem{} }
func (m *AmuseGuildChannelIncomeListRespLItem) String() string { return proto.CompactTextString(m) }
func (*AmuseGuildChannelIncomeListRespLItem) ProtoMessage()    {}
func (*AmuseGuildChannelIncomeListRespLItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_gold_commission_18e8a43e3dce6c6d, []int{73, 0}
}
func (m *AmuseGuildChannelIncomeListRespLItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AmuseGuildChannelIncomeListRespLItem.Unmarshal(m, b)
}
func (m *AmuseGuildChannelIncomeListRespLItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AmuseGuildChannelIncomeListRespLItem.Marshal(b, m, deterministic)
}
func (dst *AmuseGuildChannelIncomeListRespLItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AmuseGuildChannelIncomeListRespLItem.Merge(dst, src)
}
func (m *AmuseGuildChannelIncomeListRespLItem) XXX_Size() int {
	return xxx_messageInfo_AmuseGuildChannelIncomeListRespLItem.Size(m)
}
func (m *AmuseGuildChannelIncomeListRespLItem) XXX_DiscardUnknown() {
	xxx_messageInfo_AmuseGuildChannelIncomeListRespLItem.DiscardUnknown(m)
}

var xxx_messageInfo_AmuseGuildChannelIncomeListRespLItem proto.InternalMessageInfo

func (m *AmuseGuildChannelIncomeListRespLItem) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *AmuseGuildChannelIncomeListRespLItem) GetFee() uint64 {
	if m != nil {
		return m.Fee
	}
	return 0
}

func (m *AmuseGuildChannelIncomeListRespLItem) GetSamePeriodFee() uint64 {
	if m != nil {
		return m.SamePeriodFee
	}
	return 0
}

func (m *AmuseGuildChannelIncomeListRespLItem) GetSamePeriodQoq() float32 {
	if m != nil {
		return m.SamePeriodQoq
	}
	return 0
}

func (m *AmuseGuildChannelIncomeListRespLItem) GetLastMonthFee() uint64 {
	if m != nil {
		return m.LastMonthFee
	}
	return 0
}

type GetAmuseGuildIdsByRangeReq struct {
	StartTime            uint32   `protobuf:"varint,2,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	EndTime              uint32   `protobuf:"varint,3,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAmuseGuildIdsByRangeReq) Reset()         { *m = GetAmuseGuildIdsByRangeReq{} }
func (m *GetAmuseGuildIdsByRangeReq) String() string { return proto.CompactTextString(m) }
func (*GetAmuseGuildIdsByRangeReq) ProtoMessage()    {}
func (*GetAmuseGuildIdsByRangeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_gold_commission_18e8a43e3dce6c6d, []int{74}
}
func (m *GetAmuseGuildIdsByRangeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAmuseGuildIdsByRangeReq.Unmarshal(m, b)
}
func (m *GetAmuseGuildIdsByRangeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAmuseGuildIdsByRangeReq.Marshal(b, m, deterministic)
}
func (dst *GetAmuseGuildIdsByRangeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAmuseGuildIdsByRangeReq.Merge(dst, src)
}
func (m *GetAmuseGuildIdsByRangeReq) XXX_Size() int {
	return xxx_messageInfo_GetAmuseGuildIdsByRangeReq.Size(m)
}
func (m *GetAmuseGuildIdsByRangeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAmuseGuildIdsByRangeReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAmuseGuildIdsByRangeReq proto.InternalMessageInfo

func (m *GetAmuseGuildIdsByRangeReq) GetStartTime() uint32 {
	if m != nil {
		return m.StartTime
	}
	return 0
}

func (m *GetAmuseGuildIdsByRangeReq) GetEndTime() uint32 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

type GetAmuseGuildIdsByRangeResp struct {
	GuildIds             []uint32 `protobuf:"varint,1,rep,packed,name=guild_ids,json=guildIds,proto3" json:"guild_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAmuseGuildIdsByRangeResp) Reset()         { *m = GetAmuseGuildIdsByRangeResp{} }
func (m *GetAmuseGuildIdsByRangeResp) String() string { return proto.CompactTextString(m) }
func (*GetAmuseGuildIdsByRangeResp) ProtoMessage()    {}
func (*GetAmuseGuildIdsByRangeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_gold_commission_18e8a43e3dce6c6d, []int{75}
}
func (m *GetAmuseGuildIdsByRangeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAmuseGuildIdsByRangeResp.Unmarshal(m, b)
}
func (m *GetAmuseGuildIdsByRangeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAmuseGuildIdsByRangeResp.Marshal(b, m, deterministic)
}
func (dst *GetAmuseGuildIdsByRangeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAmuseGuildIdsByRangeResp.Merge(dst, src)
}
func (m *GetAmuseGuildIdsByRangeResp) XXX_Size() int {
	return xxx_messageInfo_GetAmuseGuildIdsByRangeResp.Size(m)
}
func (m *GetAmuseGuildIdsByRangeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAmuseGuildIdsByRangeResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAmuseGuildIdsByRangeResp proto.InternalMessageInfo

func (m *GetAmuseGuildIdsByRangeResp) GetGuildIds() []uint32 {
	if m != nil {
		return m.GuildIds
	}
	return nil
}

type AmuseExtraIncomeSettleItem struct {
	MasterUid            uint32                              `protobuf:"varint,1,opt,name=master_uid,json=masterUid,proto3" json:"master_uid,omitempty"`
	MasterGuildId        uint32                              `protobuf:"varint,2,opt,name=master_guild_id,json=masterGuildId,proto3" json:"master_guild_id,omitempty"`
	GuildList            []*AmuseExtraIncomeSettleItem_Guild `protobuf:"bytes,3,rep,name=guild_list,json=guildList,proto3" json:"guild_list,omitempty"`
	StatPeriodFee        uint64                              `protobuf:"varint,4,opt,name=stat_period_fee,json=statPeriodFee,proto3" json:"stat_period_fee,omitempty"`
	ComparePeriodFee     uint64                              `protobuf:"varint,5,opt,name=compare_period_fee,json=comparePeriodFee,proto3" json:"compare_period_fee,omitempty"`
	GrowRate             string                              `protobuf:"bytes,6,opt,name=grow_rate,json=growRate,proto3" json:"grow_rate,omitempty"`
	StatPeriodCny        string                              `protobuf:"bytes,7,opt,name=stat_period_cny,json=statPeriodCny,proto3" json:"stat_period_cny,omitempty"`
	ComparePeriodCny     string                              `protobuf:"bytes,8,opt,name=compare_period_cny,json=comparePeriodCny,proto3" json:"compare_period_cny,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                            `json:"-"`
	XXX_unrecognized     []byte                              `json:"-"`
	XXX_sizecache        int32                               `json:"-"`
}

func (m *AmuseExtraIncomeSettleItem) Reset()         { *m = AmuseExtraIncomeSettleItem{} }
func (m *AmuseExtraIncomeSettleItem) String() string { return proto.CompactTextString(m) }
func (*AmuseExtraIncomeSettleItem) ProtoMessage()    {}
func (*AmuseExtraIncomeSettleItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_gold_commission_18e8a43e3dce6c6d, []int{76}
}
func (m *AmuseExtraIncomeSettleItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AmuseExtraIncomeSettleItem.Unmarshal(m, b)
}
func (m *AmuseExtraIncomeSettleItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AmuseExtraIncomeSettleItem.Marshal(b, m, deterministic)
}
func (dst *AmuseExtraIncomeSettleItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AmuseExtraIncomeSettleItem.Merge(dst, src)
}
func (m *AmuseExtraIncomeSettleItem) XXX_Size() int {
	return xxx_messageInfo_AmuseExtraIncomeSettleItem.Size(m)
}
func (m *AmuseExtraIncomeSettleItem) XXX_DiscardUnknown() {
	xxx_messageInfo_AmuseExtraIncomeSettleItem.DiscardUnknown(m)
}

var xxx_messageInfo_AmuseExtraIncomeSettleItem proto.InternalMessageInfo

func (m *AmuseExtraIncomeSettleItem) GetMasterUid() uint32 {
	if m != nil {
		return m.MasterUid
	}
	return 0
}

func (m *AmuseExtraIncomeSettleItem) GetMasterGuildId() uint32 {
	if m != nil {
		return m.MasterGuildId
	}
	return 0
}

func (m *AmuseExtraIncomeSettleItem) GetGuildList() []*AmuseExtraIncomeSettleItem_Guild {
	if m != nil {
		return m.GuildList
	}
	return nil
}

func (m *AmuseExtraIncomeSettleItem) GetStatPeriodFee() uint64 {
	if m != nil {
		return m.StatPeriodFee
	}
	return 0
}

func (m *AmuseExtraIncomeSettleItem) GetComparePeriodFee() uint64 {
	if m != nil {
		return m.ComparePeriodFee
	}
	return 0
}

func (m *AmuseExtraIncomeSettleItem) GetGrowRate() string {
	if m != nil {
		return m.GrowRate
	}
	return ""
}

func (m *AmuseExtraIncomeSettleItem) GetStatPeriodCny() string {
	if m != nil {
		return m.StatPeriodCny
	}
	return ""
}

func (m *AmuseExtraIncomeSettleItem) GetComparePeriodCny() string {
	if m != nil {
		return m.ComparePeriodCny
	}
	return ""
}

type AmuseExtraIncomeSettleItem_Guild struct {
	GuildId              uint32                                `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	GuildDisplayId       uint32                                `protobuf:"varint,2,opt,name=guild_display_id,json=guildDisplayId,proto3" json:"guild_display_id,omitempty"`
	GuildName            string                                `protobuf:"bytes,3,opt,name=guild_name,json=guildName,proto3" json:"guild_name,omitempty"`
	ChannelList          []*AmuseExtraIncomeSettleItem_Channel `protobuf:"bytes,7,rep,name=channel_list,json=channelList,proto3" json:"channel_list,omitempty"`
	SettleMoney          uint64                                `protobuf:"varint,9,opt,name=settle_money,json=settleMoney,proto3" json:"settle_money,omitempty"`
	SettleMoneyCny       string                                `protobuf:"bytes,10,opt,name=settle_money_cny,json=settleMoneyCny,proto3" json:"settle_money_cny,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                              `json:"-"`
	XXX_unrecognized     []byte                                `json:"-"`
	XXX_sizecache        int32                                 `json:"-"`
}

func (m *AmuseExtraIncomeSettleItem_Guild) Reset()         { *m = AmuseExtraIncomeSettleItem_Guild{} }
func (m *AmuseExtraIncomeSettleItem_Guild) String() string { return proto.CompactTextString(m) }
func (*AmuseExtraIncomeSettleItem_Guild) ProtoMessage()    {}
func (*AmuseExtraIncomeSettleItem_Guild) Descriptor() ([]byte, []int) {
	return fileDescriptor_gold_commission_18e8a43e3dce6c6d, []int{76, 0}
}
func (m *AmuseExtraIncomeSettleItem_Guild) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AmuseExtraIncomeSettleItem_Guild.Unmarshal(m, b)
}
func (m *AmuseExtraIncomeSettleItem_Guild) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AmuseExtraIncomeSettleItem_Guild.Marshal(b, m, deterministic)
}
func (dst *AmuseExtraIncomeSettleItem_Guild) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AmuseExtraIncomeSettleItem_Guild.Merge(dst, src)
}
func (m *AmuseExtraIncomeSettleItem_Guild) XXX_Size() int {
	return xxx_messageInfo_AmuseExtraIncomeSettleItem_Guild.Size(m)
}
func (m *AmuseExtraIncomeSettleItem_Guild) XXX_DiscardUnknown() {
	xxx_messageInfo_AmuseExtraIncomeSettleItem_Guild.DiscardUnknown(m)
}

var xxx_messageInfo_AmuseExtraIncomeSettleItem_Guild proto.InternalMessageInfo

func (m *AmuseExtraIncomeSettleItem_Guild) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *AmuseExtraIncomeSettleItem_Guild) GetGuildDisplayId() uint32 {
	if m != nil {
		return m.GuildDisplayId
	}
	return 0
}

func (m *AmuseExtraIncomeSettleItem_Guild) GetGuildName() string {
	if m != nil {
		return m.GuildName
	}
	return ""
}

func (m *AmuseExtraIncomeSettleItem_Guild) GetChannelList() []*AmuseExtraIncomeSettleItem_Channel {
	if m != nil {
		return m.ChannelList
	}
	return nil
}

func (m *AmuseExtraIncomeSettleItem_Guild) GetSettleMoney() uint64 {
	if m != nil {
		return m.SettleMoney
	}
	return 0
}

func (m *AmuseExtraIncomeSettleItem_Guild) GetSettleMoneyCny() string {
	if m != nil {
		return m.SettleMoneyCny
	}
	return ""
}

type AmuseExtraIncomeSettleItem_Channel struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	ChannelDisplayId     uint32   `protobuf:"varint,2,opt,name=channel_display_id,json=channelDisplayId,proto3" json:"channel_display_id,omitempty"`
	ChannelName          string   `protobuf:"bytes,3,opt,name=channel_name,json=channelName,proto3" json:"channel_name,omitempty"`
	ChannelTag           string   `protobuf:"bytes,4,opt,name=channel_tag,json=channelTag,proto3" json:"channel_tag,omitempty"`
	StatPeriodFee        uint64   `protobuf:"varint,5,opt,name=stat_period_fee,json=statPeriodFee,proto3" json:"stat_period_fee,omitempty"`
	ComparePeriodFee     uint64   `protobuf:"varint,6,opt,name=compare_period_fee,json=comparePeriodFee,proto3" json:"compare_period_fee,omitempty"`
	GrowRate             string   `protobuf:"bytes,7,opt,name=grow_rate,json=growRate,proto3" json:"grow_rate,omitempty"`
	SettleRate           string   `protobuf:"bytes,8,opt,name=settle_rate,json=settleRate,proto3" json:"settle_rate,omitempty"`
	SettleMoney          uint64   `protobuf:"varint,9,opt,name=settle_money,json=settleMoney,proto3" json:"settle_money,omitempty"`
	SettleMoneyCny       string   `protobuf:"bytes,10,opt,name=settle_money_cny,json=settleMoneyCny,proto3" json:"settle_money_cny,omitempty"`
	StatPeriodCny        string   `protobuf:"bytes,11,opt,name=stat_period_cny,json=statPeriodCny,proto3" json:"stat_period_cny,omitempty"`
	ComparePeriodCny     string   `protobuf:"bytes,12,opt,name=compare_period_cny,json=comparePeriodCny,proto3" json:"compare_period_cny,omitempty"`
	ChannelViewId        string   `protobuf:"bytes,13,opt,name=channel_view_id,json=channelViewId,proto3" json:"channel_view_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AmuseExtraIncomeSettleItem_Channel) Reset()         { *m = AmuseExtraIncomeSettleItem_Channel{} }
func (m *AmuseExtraIncomeSettleItem_Channel) String() string { return proto.CompactTextString(m) }
func (*AmuseExtraIncomeSettleItem_Channel) ProtoMessage()    {}
func (*AmuseExtraIncomeSettleItem_Channel) Descriptor() ([]byte, []int) {
	return fileDescriptor_gold_commission_18e8a43e3dce6c6d, []int{76, 1}
}
func (m *AmuseExtraIncomeSettleItem_Channel) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AmuseExtraIncomeSettleItem_Channel.Unmarshal(m, b)
}
func (m *AmuseExtraIncomeSettleItem_Channel) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AmuseExtraIncomeSettleItem_Channel.Marshal(b, m, deterministic)
}
func (dst *AmuseExtraIncomeSettleItem_Channel) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AmuseExtraIncomeSettleItem_Channel.Merge(dst, src)
}
func (m *AmuseExtraIncomeSettleItem_Channel) XXX_Size() int {
	return xxx_messageInfo_AmuseExtraIncomeSettleItem_Channel.Size(m)
}
func (m *AmuseExtraIncomeSettleItem_Channel) XXX_DiscardUnknown() {
	xxx_messageInfo_AmuseExtraIncomeSettleItem_Channel.DiscardUnknown(m)
}

var xxx_messageInfo_AmuseExtraIncomeSettleItem_Channel proto.InternalMessageInfo

func (m *AmuseExtraIncomeSettleItem_Channel) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *AmuseExtraIncomeSettleItem_Channel) GetChannelDisplayId() uint32 {
	if m != nil {
		return m.ChannelDisplayId
	}
	return 0
}

func (m *AmuseExtraIncomeSettleItem_Channel) GetChannelName() string {
	if m != nil {
		return m.ChannelName
	}
	return ""
}

func (m *AmuseExtraIncomeSettleItem_Channel) GetChannelTag() string {
	if m != nil {
		return m.ChannelTag
	}
	return ""
}

func (m *AmuseExtraIncomeSettleItem_Channel) GetStatPeriodFee() uint64 {
	if m != nil {
		return m.StatPeriodFee
	}
	return 0
}

func (m *AmuseExtraIncomeSettleItem_Channel) GetComparePeriodFee() uint64 {
	if m != nil {
		return m.ComparePeriodFee
	}
	return 0
}

func (m *AmuseExtraIncomeSettleItem_Channel) GetGrowRate() string {
	if m != nil {
		return m.GrowRate
	}
	return ""
}

func (m *AmuseExtraIncomeSettleItem_Channel) GetSettleRate() string {
	if m != nil {
		return m.SettleRate
	}
	return ""
}

func (m *AmuseExtraIncomeSettleItem_Channel) GetSettleMoney() uint64 {
	if m != nil {
		return m.SettleMoney
	}
	return 0
}

func (m *AmuseExtraIncomeSettleItem_Channel) GetSettleMoneyCny() string {
	if m != nil {
		return m.SettleMoneyCny
	}
	return ""
}

func (m *AmuseExtraIncomeSettleItem_Channel) GetStatPeriodCny() string {
	if m != nil {
		return m.StatPeriodCny
	}
	return ""
}

func (m *AmuseExtraIncomeSettleItem_Channel) GetComparePeriodCny() string {
	if m != nil {
		return m.ComparePeriodCny
	}
	return ""
}

func (m *AmuseExtraIncomeSettleItem_Channel) GetChannelViewId() string {
	if m != nil {
		return m.ChannelViewId
	}
	return ""
}

type GetAmuseExtraIncomeSettleListReq struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	StartTime            uint32   `protobuf:"varint,2,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	EndTime              uint32   `protobuf:"varint,3,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	CompareStartTime     uint32   `protobuf:"varint,4,opt,name=compare_start_time,json=compareStartTime,proto3" json:"compare_start_time,omitempty"`
	CompareEndTime       uint32   `protobuf:"varint,5,opt,name=compare_end_time,json=compareEndTime,proto3" json:"compare_end_time,omitempty"`
	MasterUid            uint32   `protobuf:"varint,7,opt,name=master_uid,json=masterUid,proto3" json:"master_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAmuseExtraIncomeSettleListReq) Reset()         { *m = GetAmuseExtraIncomeSettleListReq{} }
func (m *GetAmuseExtraIncomeSettleListReq) String() string { return proto.CompactTextString(m) }
func (*GetAmuseExtraIncomeSettleListReq) ProtoMessage()    {}
func (*GetAmuseExtraIncomeSettleListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_gold_commission_18e8a43e3dce6c6d, []int{77}
}
func (m *GetAmuseExtraIncomeSettleListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAmuseExtraIncomeSettleListReq.Unmarshal(m, b)
}
func (m *GetAmuseExtraIncomeSettleListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAmuseExtraIncomeSettleListReq.Marshal(b, m, deterministic)
}
func (dst *GetAmuseExtraIncomeSettleListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAmuseExtraIncomeSettleListReq.Merge(dst, src)
}
func (m *GetAmuseExtraIncomeSettleListReq) XXX_Size() int {
	return xxx_messageInfo_GetAmuseExtraIncomeSettleListReq.Size(m)
}
func (m *GetAmuseExtraIncomeSettleListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAmuseExtraIncomeSettleListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAmuseExtraIncomeSettleListReq proto.InternalMessageInfo

func (m *GetAmuseExtraIncomeSettleListReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GetAmuseExtraIncomeSettleListReq) GetStartTime() uint32 {
	if m != nil {
		return m.StartTime
	}
	return 0
}

func (m *GetAmuseExtraIncomeSettleListReq) GetEndTime() uint32 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *GetAmuseExtraIncomeSettleListReq) GetCompareStartTime() uint32 {
	if m != nil {
		return m.CompareStartTime
	}
	return 0
}

func (m *GetAmuseExtraIncomeSettleListReq) GetCompareEndTime() uint32 {
	if m != nil {
		return m.CompareEndTime
	}
	return 0
}

func (m *GetAmuseExtraIncomeSettleListReq) GetMasterUid() uint32 {
	if m != nil {
		return m.MasterUid
	}
	return 0
}

type GetAmuseExtraIncomeSettleListResp struct {
	List                 []*AmuseExtraIncomeSettleItem `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	TotalFee             uint64                        `protobuf:"varint,2,opt,name=total_fee,json=totalFee,proto3" json:"total_fee,omitempty"`
	TotalFeeCny          string                        `protobuf:"bytes,3,opt,name=total_fee_cny,json=totalFeeCny,proto3" json:"total_fee_cny,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                      `json:"-"`
	XXX_unrecognized     []byte                        `json:"-"`
	XXX_sizecache        int32                         `json:"-"`
}

func (m *GetAmuseExtraIncomeSettleListResp) Reset()         { *m = GetAmuseExtraIncomeSettleListResp{} }
func (m *GetAmuseExtraIncomeSettleListResp) String() string { return proto.CompactTextString(m) }
func (*GetAmuseExtraIncomeSettleListResp) ProtoMessage()    {}
func (*GetAmuseExtraIncomeSettleListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_gold_commission_18e8a43e3dce6c6d, []int{78}
}
func (m *GetAmuseExtraIncomeSettleListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAmuseExtraIncomeSettleListResp.Unmarshal(m, b)
}
func (m *GetAmuseExtraIncomeSettleListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAmuseExtraIncomeSettleListResp.Marshal(b, m, deterministic)
}
func (dst *GetAmuseExtraIncomeSettleListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAmuseExtraIncomeSettleListResp.Merge(dst, src)
}
func (m *GetAmuseExtraIncomeSettleListResp) XXX_Size() int {
	return xxx_messageInfo_GetAmuseExtraIncomeSettleListResp.Size(m)
}
func (m *GetAmuseExtraIncomeSettleListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAmuseExtraIncomeSettleListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAmuseExtraIncomeSettleListResp proto.InternalMessageInfo

func (m *GetAmuseExtraIncomeSettleListResp) GetList() []*AmuseExtraIncomeSettleItem {
	if m != nil {
		return m.List
	}
	return nil
}

func (m *GetAmuseExtraIncomeSettleListResp) GetTotalFee() uint64 {
	if m != nil {
		return m.TotalFee
	}
	return 0
}

func (m *GetAmuseExtraIncomeSettleListResp) GetTotalFeeCny() string {
	if m != nil {
		return m.TotalFeeCny
	}
	return ""
}

type GetAmuseSettleGuildListReq struct {
	Keyword              uint32   `protobuf:"varint,1,opt,name=keyword,proto3" json:"keyword,omitempty"`
	Offset               uint32   `protobuf:"varint,2,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32   `protobuf:"varint,3,opt,name=limit,proto3" json:"limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAmuseSettleGuildListReq) Reset()         { *m = GetAmuseSettleGuildListReq{} }
func (m *GetAmuseSettleGuildListReq) String() string { return proto.CompactTextString(m) }
func (*GetAmuseSettleGuildListReq) ProtoMessage()    {}
func (*GetAmuseSettleGuildListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_gold_commission_18e8a43e3dce6c6d, []int{79}
}
func (m *GetAmuseSettleGuildListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAmuseSettleGuildListReq.Unmarshal(m, b)
}
func (m *GetAmuseSettleGuildListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAmuseSettleGuildListReq.Marshal(b, m, deterministic)
}
func (dst *GetAmuseSettleGuildListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAmuseSettleGuildListReq.Merge(dst, src)
}
func (m *GetAmuseSettleGuildListReq) XXX_Size() int {
	return xxx_messageInfo_GetAmuseSettleGuildListReq.Size(m)
}
func (m *GetAmuseSettleGuildListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAmuseSettleGuildListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAmuseSettleGuildListReq proto.InternalMessageInfo

func (m *GetAmuseSettleGuildListReq) GetKeyword() uint32 {
	if m != nil {
		return m.Keyword
	}
	return 0
}

func (m *GetAmuseSettleGuildListReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetAmuseSettleGuildListReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type GetAmuseSettleGuildListResp struct {
	GuildList            []*GetAmuseSettleGuildListResp_Guild `protobuf:"bytes,1,rep,name=guild_list,json=guildList,proto3" json:"guild_list,omitempty"`
	Total                uint32                               `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                             `json:"-"`
	XXX_unrecognized     []byte                               `json:"-"`
	XXX_sizecache        int32                                `json:"-"`
}

func (m *GetAmuseSettleGuildListResp) Reset()         { *m = GetAmuseSettleGuildListResp{} }
func (m *GetAmuseSettleGuildListResp) String() string { return proto.CompactTextString(m) }
func (*GetAmuseSettleGuildListResp) ProtoMessage()    {}
func (*GetAmuseSettleGuildListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_gold_commission_18e8a43e3dce6c6d, []int{80}
}
func (m *GetAmuseSettleGuildListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAmuseSettleGuildListResp.Unmarshal(m, b)
}
func (m *GetAmuseSettleGuildListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAmuseSettleGuildListResp.Marshal(b, m, deterministic)
}
func (dst *GetAmuseSettleGuildListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAmuseSettleGuildListResp.Merge(dst, src)
}
func (m *GetAmuseSettleGuildListResp) XXX_Size() int {
	return xxx_messageInfo_GetAmuseSettleGuildListResp.Size(m)
}
func (m *GetAmuseSettleGuildListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAmuseSettleGuildListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAmuseSettleGuildListResp proto.InternalMessageInfo

func (m *GetAmuseSettleGuildListResp) GetGuildList() []*GetAmuseSettleGuildListResp_Guild {
	if m != nil {
		return m.GuildList
	}
	return nil
}

func (m *GetAmuseSettleGuildListResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

type GetAmuseSettleGuildListResp_Guild struct {
	MasterUid            uint32   `protobuf:"varint,1,opt,name=master_uid,json=masterUid,proto3" json:"master_uid,omitempty"`
	GuildId              uint32   `protobuf:"varint,2,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	GuildDisplayId       uint32   `protobuf:"varint,3,opt,name=guild_display_id,json=guildDisplayId,proto3" json:"guild_display_id,omitempty"`
	GuildName            string   `protobuf:"bytes,4,opt,name=guild_name,json=guildName,proto3" json:"guild_name,omitempty"`
	ChannelNum           uint32   `protobuf:"varint,5,opt,name=channel_num,json=channelNum,proto3" json:"channel_num,omitempty"`
	SettleStatus         uint32   `protobuf:"varint,6,opt,name=settle_status,json=settleStatus,proto3" json:"settle_status,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAmuseSettleGuildListResp_Guild) Reset()         { *m = GetAmuseSettleGuildListResp_Guild{} }
func (m *GetAmuseSettleGuildListResp_Guild) String() string { return proto.CompactTextString(m) }
func (*GetAmuseSettleGuildListResp_Guild) ProtoMessage()    {}
func (*GetAmuseSettleGuildListResp_Guild) Descriptor() ([]byte, []int) {
	return fileDescriptor_gold_commission_18e8a43e3dce6c6d, []int{80, 0}
}
func (m *GetAmuseSettleGuildListResp_Guild) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAmuseSettleGuildListResp_Guild.Unmarshal(m, b)
}
func (m *GetAmuseSettleGuildListResp_Guild) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAmuseSettleGuildListResp_Guild.Marshal(b, m, deterministic)
}
func (dst *GetAmuseSettleGuildListResp_Guild) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAmuseSettleGuildListResp_Guild.Merge(dst, src)
}
func (m *GetAmuseSettleGuildListResp_Guild) XXX_Size() int {
	return xxx_messageInfo_GetAmuseSettleGuildListResp_Guild.Size(m)
}
func (m *GetAmuseSettleGuildListResp_Guild) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAmuseSettleGuildListResp_Guild.DiscardUnknown(m)
}

var xxx_messageInfo_GetAmuseSettleGuildListResp_Guild proto.InternalMessageInfo

func (m *GetAmuseSettleGuildListResp_Guild) GetMasterUid() uint32 {
	if m != nil {
		return m.MasterUid
	}
	return 0
}

func (m *GetAmuseSettleGuildListResp_Guild) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GetAmuseSettleGuildListResp_Guild) GetGuildDisplayId() uint32 {
	if m != nil {
		return m.GuildDisplayId
	}
	return 0
}

func (m *GetAmuseSettleGuildListResp_Guild) GetGuildName() string {
	if m != nil {
		return m.GuildName
	}
	return ""
}

func (m *GetAmuseSettleGuildListResp_Guild) GetChannelNum() uint32 {
	if m != nil {
		return m.ChannelNum
	}
	return 0
}

func (m *GetAmuseSettleGuildListResp_Guild) GetSettleStatus() uint32 {
	if m != nil {
		return m.SettleStatus
	}
	return 0
}

type GetAmuseSettleChannelListReq struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAmuseSettleChannelListReq) Reset()         { *m = GetAmuseSettleChannelListReq{} }
func (m *GetAmuseSettleChannelListReq) String() string { return proto.CompactTextString(m) }
func (*GetAmuseSettleChannelListReq) ProtoMessage()    {}
func (*GetAmuseSettleChannelListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_gold_commission_18e8a43e3dce6c6d, []int{81}
}
func (m *GetAmuseSettleChannelListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAmuseSettleChannelListReq.Unmarshal(m, b)
}
func (m *GetAmuseSettleChannelListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAmuseSettleChannelListReq.Marshal(b, m, deterministic)
}
func (dst *GetAmuseSettleChannelListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAmuseSettleChannelListReq.Merge(dst, src)
}
func (m *GetAmuseSettleChannelListReq) XXX_Size() int {
	return xxx_messageInfo_GetAmuseSettleChannelListReq.Size(m)
}
func (m *GetAmuseSettleChannelListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAmuseSettleChannelListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAmuseSettleChannelListReq proto.InternalMessageInfo

func (m *GetAmuseSettleChannelListReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GetAmuseSettleChannelListReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetAmuseSettleChannelListResp struct {
	ChannelList          []*GetAmuseSettleChannelListResp_Channel `protobuf:"bytes,1,rep,name=channel_list,json=channelList,proto3" json:"channel_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                                 `json:"-"`
	XXX_unrecognized     []byte                                   `json:"-"`
	XXX_sizecache        int32                                    `json:"-"`
}

func (m *GetAmuseSettleChannelListResp) Reset()         { *m = GetAmuseSettleChannelListResp{} }
func (m *GetAmuseSettleChannelListResp) String() string { return proto.CompactTextString(m) }
func (*GetAmuseSettleChannelListResp) ProtoMessage()    {}
func (*GetAmuseSettleChannelListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_gold_commission_18e8a43e3dce6c6d, []int{82}
}
func (m *GetAmuseSettleChannelListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAmuseSettleChannelListResp.Unmarshal(m, b)
}
func (m *GetAmuseSettleChannelListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAmuseSettleChannelListResp.Marshal(b, m, deterministic)
}
func (dst *GetAmuseSettleChannelListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAmuseSettleChannelListResp.Merge(dst, src)
}
func (m *GetAmuseSettleChannelListResp) XXX_Size() int {
	return xxx_messageInfo_GetAmuseSettleChannelListResp.Size(m)
}
func (m *GetAmuseSettleChannelListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAmuseSettleChannelListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAmuseSettleChannelListResp proto.InternalMessageInfo

func (m *GetAmuseSettleChannelListResp) GetChannelList() []*GetAmuseSettleChannelListResp_Channel {
	if m != nil {
		return m.ChannelList
	}
	return nil
}

type GetAmuseSettleChannelListResp_Channel struct {
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	ChannelDisplayId     uint32   `protobuf:"varint,3,opt,name=channel_display_id,json=channelDisplayId,proto3" json:"channel_display_id,omitempty"`
	ChannelName          string   `protobuf:"bytes,4,opt,name=channel_name,json=channelName,proto3" json:"channel_name,omitempty"`
	SettleStatus         uint32   `protobuf:"varint,5,opt,name=settle_status,json=settleStatus,proto3" json:"settle_status,omitempty"`
	ChannelViewId        string   `protobuf:"bytes,6,opt,name=channel_view_id,json=channelViewId,proto3" json:"channel_view_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAmuseSettleChannelListResp_Channel) Reset()         { *m = GetAmuseSettleChannelListResp_Channel{} }
func (m *GetAmuseSettleChannelListResp_Channel) String() string { return proto.CompactTextString(m) }
func (*GetAmuseSettleChannelListResp_Channel) ProtoMessage()    {}
func (*GetAmuseSettleChannelListResp_Channel) Descriptor() ([]byte, []int) {
	return fileDescriptor_gold_commission_18e8a43e3dce6c6d, []int{82, 0}
}
func (m *GetAmuseSettleChannelListResp_Channel) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAmuseSettleChannelListResp_Channel.Unmarshal(m, b)
}
func (m *GetAmuseSettleChannelListResp_Channel) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAmuseSettleChannelListResp_Channel.Marshal(b, m, deterministic)
}
func (dst *GetAmuseSettleChannelListResp_Channel) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAmuseSettleChannelListResp_Channel.Merge(dst, src)
}
func (m *GetAmuseSettleChannelListResp_Channel) XXX_Size() int {
	return xxx_messageInfo_GetAmuseSettleChannelListResp_Channel.Size(m)
}
func (m *GetAmuseSettleChannelListResp_Channel) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAmuseSettleChannelListResp_Channel.DiscardUnknown(m)
}

var xxx_messageInfo_GetAmuseSettleChannelListResp_Channel proto.InternalMessageInfo

func (m *GetAmuseSettleChannelListResp_Channel) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetAmuseSettleChannelListResp_Channel) GetChannelDisplayId() uint32 {
	if m != nil {
		return m.ChannelDisplayId
	}
	return 0
}

func (m *GetAmuseSettleChannelListResp_Channel) GetChannelName() string {
	if m != nil {
		return m.ChannelName
	}
	return ""
}

func (m *GetAmuseSettleChannelListResp_Channel) GetSettleStatus() uint32 {
	if m != nil {
		return m.SettleStatus
	}
	return 0
}

func (m *GetAmuseSettleChannelListResp_Channel) GetChannelViewId() string {
	if m != nil {
		return m.ChannelViewId
	}
	return ""
}

type SetAmuseSettleGuildReq struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	SettleStatus         uint32   `protobuf:"varint,5,opt,name=settle_status,json=settleStatus,proto3" json:"settle_status,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetAmuseSettleGuildReq) Reset()         { *m = SetAmuseSettleGuildReq{} }
func (m *SetAmuseSettleGuildReq) String() string { return proto.CompactTextString(m) }
func (*SetAmuseSettleGuildReq) ProtoMessage()    {}
func (*SetAmuseSettleGuildReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_gold_commission_18e8a43e3dce6c6d, []int{83}
}
func (m *SetAmuseSettleGuildReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetAmuseSettleGuildReq.Unmarshal(m, b)
}
func (m *SetAmuseSettleGuildReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetAmuseSettleGuildReq.Marshal(b, m, deterministic)
}
func (dst *SetAmuseSettleGuildReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetAmuseSettleGuildReq.Merge(dst, src)
}
func (m *SetAmuseSettleGuildReq) XXX_Size() int {
	return xxx_messageInfo_SetAmuseSettleGuildReq.Size(m)
}
func (m *SetAmuseSettleGuildReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetAmuseSettleGuildReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetAmuseSettleGuildReq proto.InternalMessageInfo

func (m *SetAmuseSettleGuildReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *SetAmuseSettleGuildReq) GetSettleStatus() uint32 {
	if m != nil {
		return m.SettleStatus
	}
	return 0
}

type SetAmuseSettleGuildResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetAmuseSettleGuildResp) Reset()         { *m = SetAmuseSettleGuildResp{} }
func (m *SetAmuseSettleGuildResp) String() string { return proto.CompactTextString(m) }
func (*SetAmuseSettleGuildResp) ProtoMessage()    {}
func (*SetAmuseSettleGuildResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_gold_commission_18e8a43e3dce6c6d, []int{84}
}
func (m *SetAmuseSettleGuildResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetAmuseSettleGuildResp.Unmarshal(m, b)
}
func (m *SetAmuseSettleGuildResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetAmuseSettleGuildResp.Marshal(b, m, deterministic)
}
func (dst *SetAmuseSettleGuildResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetAmuseSettleGuildResp.Merge(dst, src)
}
func (m *SetAmuseSettleGuildResp) XXX_Size() int {
	return xxx_messageInfo_SetAmuseSettleGuildResp.Size(m)
}
func (m *SetAmuseSettleGuildResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetAmuseSettleGuildResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetAmuseSettleGuildResp proto.InternalMessageInfo

type SetAmuseSettleChannelReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	SettleStatus         uint32   `protobuf:"varint,5,opt,name=settle_status,json=settleStatus,proto3" json:"settle_status,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetAmuseSettleChannelReq) Reset()         { *m = SetAmuseSettleChannelReq{} }
func (m *SetAmuseSettleChannelReq) String() string { return proto.CompactTextString(m) }
func (*SetAmuseSettleChannelReq) ProtoMessage()    {}
func (*SetAmuseSettleChannelReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_gold_commission_18e8a43e3dce6c6d, []int{85}
}
func (m *SetAmuseSettleChannelReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetAmuseSettleChannelReq.Unmarshal(m, b)
}
func (m *SetAmuseSettleChannelReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetAmuseSettleChannelReq.Marshal(b, m, deterministic)
}
func (dst *SetAmuseSettleChannelReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetAmuseSettleChannelReq.Merge(dst, src)
}
func (m *SetAmuseSettleChannelReq) XXX_Size() int {
	return xxx_messageInfo_SetAmuseSettleChannelReq.Size(m)
}
func (m *SetAmuseSettleChannelReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetAmuseSettleChannelReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetAmuseSettleChannelReq proto.InternalMessageInfo

func (m *SetAmuseSettleChannelReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SetAmuseSettleChannelReq) GetSettleStatus() uint32 {
	if m != nil {
		return m.SettleStatus
	}
	return 0
}

type SetAmuseSettleChannelResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetAmuseSettleChannelResp) Reset()         { *m = SetAmuseSettleChannelResp{} }
func (m *SetAmuseSettleChannelResp) String() string { return proto.CompactTextString(m) }
func (*SetAmuseSettleChannelResp) ProtoMessage()    {}
func (*SetAmuseSettleChannelResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_gold_commission_18e8a43e3dce6c6d, []int{86}
}
func (m *SetAmuseSettleChannelResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetAmuseSettleChannelResp.Unmarshal(m, b)
}
func (m *SetAmuseSettleChannelResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetAmuseSettleChannelResp.Marshal(b, m, deterministic)
}
func (dst *SetAmuseSettleChannelResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetAmuseSettleChannelResp.Merge(dst, src)
}
func (m *SetAmuseSettleChannelResp) XXX_Size() int {
	return xxx_messageInfo_SetAmuseSettleChannelResp.Size(m)
}
func (m *SetAmuseSettleChannelResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetAmuseSettleChannelResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetAmuseSettleChannelResp proto.InternalMessageInfo

type GetAmuseExtraDetailReq struct {
	GuildIds             []uint32 `protobuf:"varint,1,rep,packed,name=guild_ids,json=guildIds,proto3" json:"guild_ids,omitempty"`
	Yearmonth            uint32   `protobuf:"varint,2,opt,name=yearmonth,proto3" json:"yearmonth,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAmuseExtraDetailReq) Reset()         { *m = GetAmuseExtraDetailReq{} }
func (m *GetAmuseExtraDetailReq) String() string { return proto.CompactTextString(m) }
func (*GetAmuseExtraDetailReq) ProtoMessage()    {}
func (*GetAmuseExtraDetailReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_gold_commission_18e8a43e3dce6c6d, []int{87}
}
func (m *GetAmuseExtraDetailReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAmuseExtraDetailReq.Unmarshal(m, b)
}
func (m *GetAmuseExtraDetailReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAmuseExtraDetailReq.Marshal(b, m, deterministic)
}
func (dst *GetAmuseExtraDetailReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAmuseExtraDetailReq.Merge(dst, src)
}
func (m *GetAmuseExtraDetailReq) XXX_Size() int {
	return xxx_messageInfo_GetAmuseExtraDetailReq.Size(m)
}
func (m *GetAmuseExtraDetailReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAmuseExtraDetailReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAmuseExtraDetailReq proto.InternalMessageInfo

func (m *GetAmuseExtraDetailReq) GetGuildIds() []uint32 {
	if m != nil {
		return m.GuildIds
	}
	return nil
}

func (m *GetAmuseExtraDetailReq) GetYearmonth() uint32 {
	if m != nil {
		return m.Yearmonth
	}
	return 0
}

type GetAmuseExtraDetailResp struct {
	YearMonth            uint32                                 `protobuf:"varint,1,opt,name=year_month,json=yearMonth,proto3" json:"year_month,omitempty"`
	ThisMonthFee         uint64                                 `protobuf:"varint,2,opt,name=this_month_fee,json=thisMonthFee,proto3" json:"this_month_fee,omitempty"`
	LastMonthFee         uint64                                 `protobuf:"varint,3,opt,name=last_month_fee,json=lastMonthFee,proto3" json:"last_month_fee,omitempty"`
	ThisMonthIncome      uint64                                 `protobuf:"varint,4,opt,name=this_month_income,json=thisMonthIncome,proto3" json:"this_month_income,omitempty"`
	ThisMonthIncomeCny   string                                 `protobuf:"bytes,5,opt,name=this_month_income_cny,json=thisMonthIncomeCny,proto3" json:"this_month_income_cny,omitempty"`
	PrepaidMoney         uint64                                 `protobuf:"varint,6,opt,name=prepaid_money,json=prepaidMoney,proto3" json:"prepaid_money,omitempty"`
	PrepaidMoneyCny      string                                 `protobuf:"bytes,7,opt,name=prepaid_money_cny,json=prepaidMoneyCny,proto3" json:"prepaid_money_cny,omitempty"`
	Remark               string                                 `protobuf:"bytes,8,opt,name=remark,proto3" json:"remark,omitempty"`
	GrowRate             string                                 `protobuf:"bytes,9,opt,name=grow_rate,json=growRate,proto3" json:"grow_rate,omitempty"`
	ChannelList          []*GetAmuseExtraDetailResp_ChannelItem `protobuf:"bytes,10,rep,name=channel_list,json=channelList,proto3" json:"channel_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                               `json:"-"`
	XXX_unrecognized     []byte                                 `json:"-"`
	XXX_sizecache        int32                                  `json:"-"`
}

func (m *GetAmuseExtraDetailResp) Reset()         { *m = GetAmuseExtraDetailResp{} }
func (m *GetAmuseExtraDetailResp) String() string { return proto.CompactTextString(m) }
func (*GetAmuseExtraDetailResp) ProtoMessage()    {}
func (*GetAmuseExtraDetailResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_gold_commission_18e8a43e3dce6c6d, []int{88}
}
func (m *GetAmuseExtraDetailResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAmuseExtraDetailResp.Unmarshal(m, b)
}
func (m *GetAmuseExtraDetailResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAmuseExtraDetailResp.Marshal(b, m, deterministic)
}
func (dst *GetAmuseExtraDetailResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAmuseExtraDetailResp.Merge(dst, src)
}
func (m *GetAmuseExtraDetailResp) XXX_Size() int {
	return xxx_messageInfo_GetAmuseExtraDetailResp.Size(m)
}
func (m *GetAmuseExtraDetailResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAmuseExtraDetailResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAmuseExtraDetailResp proto.InternalMessageInfo

func (m *GetAmuseExtraDetailResp) GetYearMonth() uint32 {
	if m != nil {
		return m.YearMonth
	}
	return 0
}

func (m *GetAmuseExtraDetailResp) GetThisMonthFee() uint64 {
	if m != nil {
		return m.ThisMonthFee
	}
	return 0
}

func (m *GetAmuseExtraDetailResp) GetLastMonthFee() uint64 {
	if m != nil {
		return m.LastMonthFee
	}
	return 0
}

func (m *GetAmuseExtraDetailResp) GetThisMonthIncome() uint64 {
	if m != nil {
		return m.ThisMonthIncome
	}
	return 0
}

func (m *GetAmuseExtraDetailResp) GetThisMonthIncomeCny() string {
	if m != nil {
		return m.ThisMonthIncomeCny
	}
	return ""
}

func (m *GetAmuseExtraDetailResp) GetPrepaidMoney() uint64 {
	if m != nil {
		return m.PrepaidMoney
	}
	return 0
}

func (m *GetAmuseExtraDetailResp) GetPrepaidMoneyCny() string {
	if m != nil {
		return m.PrepaidMoneyCny
	}
	return ""
}

func (m *GetAmuseExtraDetailResp) GetRemark() string {
	if m != nil {
		return m.Remark
	}
	return ""
}

func (m *GetAmuseExtraDetailResp) GetGrowRate() string {
	if m != nil {
		return m.GrowRate
	}
	return ""
}

func (m *GetAmuseExtraDetailResp) GetChannelList() []*GetAmuseExtraDetailResp_ChannelItem {
	if m != nil {
		return m.ChannelList
	}
	return nil
}

type GetAmuseExtraDetailResp_ChannelItem struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	ChannelTag           string   `protobuf:"bytes,4,opt,name=channel_tag,json=channelTag,proto3" json:"channel_tag,omitempty"`
	ThisMonthFee         uint64   `protobuf:"varint,5,opt,name=this_month_fee,json=thisMonthFee,proto3" json:"this_month_fee,omitempty"`
	LastMonthFee         uint64   `protobuf:"varint,6,opt,name=last_month_fee,json=lastMonthFee,proto3" json:"last_month_fee,omitempty"`
	SettlementRate       string   `protobuf:"bytes,7,opt,name=settlement_rate,json=settlementRate,proto3" json:"settlement_rate,omitempty"`
	ThisMonthIncome      uint64   `protobuf:"varint,8,opt,name=this_month_income,json=thisMonthIncome,proto3" json:"this_month_income,omitempty"`
	GuildId              uint32   `protobuf:"varint,9,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	GrowRate             string   `protobuf:"bytes,10,opt,name=grow_rate,json=growRate,proto3" json:"grow_rate,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAmuseExtraDetailResp_ChannelItem) Reset()         { *m = GetAmuseExtraDetailResp_ChannelItem{} }
func (m *GetAmuseExtraDetailResp_ChannelItem) String() string { return proto.CompactTextString(m) }
func (*GetAmuseExtraDetailResp_ChannelItem) ProtoMessage()    {}
func (*GetAmuseExtraDetailResp_ChannelItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_gold_commission_18e8a43e3dce6c6d, []int{88, 0}
}
func (m *GetAmuseExtraDetailResp_ChannelItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAmuseExtraDetailResp_ChannelItem.Unmarshal(m, b)
}
func (m *GetAmuseExtraDetailResp_ChannelItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAmuseExtraDetailResp_ChannelItem.Marshal(b, m, deterministic)
}
func (dst *GetAmuseExtraDetailResp_ChannelItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAmuseExtraDetailResp_ChannelItem.Merge(dst, src)
}
func (m *GetAmuseExtraDetailResp_ChannelItem) XXX_Size() int {
	return xxx_messageInfo_GetAmuseExtraDetailResp_ChannelItem.Size(m)
}
func (m *GetAmuseExtraDetailResp_ChannelItem) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAmuseExtraDetailResp_ChannelItem.DiscardUnknown(m)
}

var xxx_messageInfo_GetAmuseExtraDetailResp_ChannelItem proto.InternalMessageInfo

func (m *GetAmuseExtraDetailResp_ChannelItem) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetAmuseExtraDetailResp_ChannelItem) GetChannelTag() string {
	if m != nil {
		return m.ChannelTag
	}
	return ""
}

func (m *GetAmuseExtraDetailResp_ChannelItem) GetThisMonthFee() uint64 {
	if m != nil {
		return m.ThisMonthFee
	}
	return 0
}

func (m *GetAmuseExtraDetailResp_ChannelItem) GetLastMonthFee() uint64 {
	if m != nil {
		return m.LastMonthFee
	}
	return 0
}

func (m *GetAmuseExtraDetailResp_ChannelItem) GetSettlementRate() string {
	if m != nil {
		return m.SettlementRate
	}
	return ""
}

func (m *GetAmuseExtraDetailResp_ChannelItem) GetThisMonthIncome() uint64 {
	if m != nil {
		return m.ThisMonthIncome
	}
	return 0
}

func (m *GetAmuseExtraDetailResp_ChannelItem) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GetAmuseExtraDetailResp_ChannelItem) GetGrowRate() string {
	if m != nil {
		return m.GrowRate
	}
	return ""
}

// 获取会长服务号互动游戏收益
type GetInteractGameIncomeDetailReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	GuildId              uint32   `protobuf:"varint,2,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetInteractGameIncomeDetailReq) Reset()         { *m = GetInteractGameIncomeDetailReq{} }
func (m *GetInteractGameIncomeDetailReq) String() string { return proto.CompactTextString(m) }
func (*GetInteractGameIncomeDetailReq) ProtoMessage()    {}
func (*GetInteractGameIncomeDetailReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_gold_commission_18e8a43e3dce6c6d, []int{89}
}
func (m *GetInteractGameIncomeDetailReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetInteractGameIncomeDetailReq.Unmarshal(m, b)
}
func (m *GetInteractGameIncomeDetailReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetInteractGameIncomeDetailReq.Marshal(b, m, deterministic)
}
func (dst *GetInteractGameIncomeDetailReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetInteractGameIncomeDetailReq.Merge(dst, src)
}
func (m *GetInteractGameIncomeDetailReq) XXX_Size() int {
	return xxx_messageInfo_GetInteractGameIncomeDetailReq.Size(m)
}
func (m *GetInteractGameIncomeDetailReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetInteractGameIncomeDetailReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetInteractGameIncomeDetailReq proto.InternalMessageInfo

func (m *GetInteractGameIncomeDetailReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetInteractGameIncomeDetailReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

type GetInteractGameIncomeDetailRsp struct {
	DailyIncome          *DailyIncome   `protobuf:"bytes,2,opt,name=daily_income,json=dailyIncome,proto3" json:"daily_income,omitempty"`
	WeeklyIncome         *WeeklyIncome  `protobuf:"bytes,3,opt,name=weekly_income,json=weeklyIncome,proto3" json:"weekly_income,omitempty"`
	MonthlyIncome        *MonthlyIncome `protobuf:"bytes,4,opt,name=monthly_income,json=monthlyIncome,proto3" json:"monthly_income,omitempty"`
	MonthlyExtraIncome   *MonthlyIncome `protobuf:"bytes,5,opt,name=monthly_extra_income,json=monthlyExtraIncome,proto3" json:"monthly_extra_income,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *GetInteractGameIncomeDetailRsp) Reset()         { *m = GetInteractGameIncomeDetailRsp{} }
func (m *GetInteractGameIncomeDetailRsp) String() string { return proto.CompactTextString(m) }
func (*GetInteractGameIncomeDetailRsp) ProtoMessage()    {}
func (*GetInteractGameIncomeDetailRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_gold_commission_18e8a43e3dce6c6d, []int{90}
}
func (m *GetInteractGameIncomeDetailRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetInteractGameIncomeDetailRsp.Unmarshal(m, b)
}
func (m *GetInteractGameIncomeDetailRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetInteractGameIncomeDetailRsp.Marshal(b, m, deterministic)
}
func (dst *GetInteractGameIncomeDetailRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetInteractGameIncomeDetailRsp.Merge(dst, src)
}
func (m *GetInteractGameIncomeDetailRsp) XXX_Size() int {
	return xxx_messageInfo_GetInteractGameIncomeDetailRsp.Size(m)
}
func (m *GetInteractGameIncomeDetailRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetInteractGameIncomeDetailRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GetInteractGameIncomeDetailRsp proto.InternalMessageInfo

func (m *GetInteractGameIncomeDetailRsp) GetDailyIncome() *DailyIncome {
	if m != nil {
		return m.DailyIncome
	}
	return nil
}

func (m *GetInteractGameIncomeDetailRsp) GetWeeklyIncome() *WeeklyIncome {
	if m != nil {
		return m.WeeklyIncome
	}
	return nil
}

func (m *GetInteractGameIncomeDetailRsp) GetMonthlyIncome() *MonthlyIncome {
	if m != nil {
		return m.MonthlyIncome
	}
	return nil
}

func (m *GetInteractGameIncomeDetailRsp) GetMonthlyExtraIncome() *MonthlyIncome {
	if m != nil {
		return m.MonthlyExtraIncome
	}
	return nil
}

type GetGuildInteractGamePerReq struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGuildInteractGamePerReq) Reset()         { *m = GetGuildInteractGamePerReq{} }
func (m *GetGuildInteractGamePerReq) String() string { return proto.CompactTextString(m) }
func (*GetGuildInteractGamePerReq) ProtoMessage()    {}
func (*GetGuildInteractGamePerReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_gold_commission_18e8a43e3dce6c6d, []int{91}
}
func (m *GetGuildInteractGamePerReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGuildInteractGamePerReq.Unmarshal(m, b)
}
func (m *GetGuildInteractGamePerReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGuildInteractGamePerReq.Marshal(b, m, deterministic)
}
func (dst *GetGuildInteractGamePerReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGuildInteractGamePerReq.Merge(dst, src)
}
func (m *GetGuildInteractGamePerReq) XXX_Size() int {
	return xxx_messageInfo_GetGuildInteractGamePerReq.Size(m)
}
func (m *GetGuildInteractGamePerReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGuildInteractGamePerReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetGuildInteractGamePerReq proto.InternalMessageInfo

func (m *GetGuildInteractGamePerReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

type GetGuildInteractGamePerResp struct {
	IsShow               bool     `protobuf:"varint,1,opt,name=is_show,json=isShow,proto3" json:"is_show,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGuildInteractGamePerResp) Reset()         { *m = GetGuildInteractGamePerResp{} }
func (m *GetGuildInteractGamePerResp) String() string { return proto.CompactTextString(m) }
func (*GetGuildInteractGamePerResp) ProtoMessage()    {}
func (*GetGuildInteractGamePerResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_gold_commission_18e8a43e3dce6c6d, []int{92}
}
func (m *GetGuildInteractGamePerResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGuildInteractGamePerResp.Unmarshal(m, b)
}
func (m *GetGuildInteractGamePerResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGuildInteractGamePerResp.Marshal(b, m, deterministic)
}
func (dst *GetGuildInteractGamePerResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGuildInteractGamePerResp.Merge(dst, src)
}
func (m *GetGuildInteractGamePerResp) XXX_Size() int {
	return xxx_messageInfo_GetGuildInteractGamePerResp.Size(m)
}
func (m *GetGuildInteractGamePerResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGuildInteractGamePerResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetGuildInteractGamePerResp proto.InternalMessageInfo

func (m *GetGuildInteractGamePerResp) GetIsShow() bool {
	if m != nil {
		return m.IsShow
	}
	return false
}

type GetInteractGameExtraIncomeReq struct {
	GuildIds             []uint32 `protobuf:"varint,1,rep,packed,name=guild_ids,json=guildIds,proto3" json:"guild_ids,omitempty"`
	MonthTime            uint32   `protobuf:"varint,2,opt,name=month_time,json=monthTime,proto3" json:"month_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetInteractGameExtraIncomeReq) Reset()         { *m = GetInteractGameExtraIncomeReq{} }
func (m *GetInteractGameExtraIncomeReq) String() string { return proto.CompactTextString(m) }
func (*GetInteractGameExtraIncomeReq) ProtoMessage()    {}
func (*GetInteractGameExtraIncomeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_gold_commission_18e8a43e3dce6c6d, []int{93}
}
func (m *GetInteractGameExtraIncomeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetInteractGameExtraIncomeReq.Unmarshal(m, b)
}
func (m *GetInteractGameExtraIncomeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetInteractGameExtraIncomeReq.Marshal(b, m, deterministic)
}
func (dst *GetInteractGameExtraIncomeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetInteractGameExtraIncomeReq.Merge(dst, src)
}
func (m *GetInteractGameExtraIncomeReq) XXX_Size() int {
	return xxx_messageInfo_GetInteractGameExtraIncomeReq.Size(m)
}
func (m *GetInteractGameExtraIncomeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetInteractGameExtraIncomeReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetInteractGameExtraIncomeReq proto.InternalMessageInfo

func (m *GetInteractGameExtraIncomeReq) GetGuildIds() []uint32 {
	if m != nil {
		return m.GuildIds
	}
	return nil
}

func (m *GetInteractGameExtraIncomeReq) GetMonthTime() uint32 {
	if m != nil {
		return m.MonthTime
	}
	return 0
}

type GetInteractGameExtraIncomeResp struct {
	Info                 *InteractGameExtraDetail `protobuf:"bytes,1,opt,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *GetInteractGameExtraIncomeResp) Reset()         { *m = GetInteractGameExtraIncomeResp{} }
func (m *GetInteractGameExtraIncomeResp) String() string { return proto.CompactTextString(m) }
func (*GetInteractGameExtraIncomeResp) ProtoMessage()    {}
func (*GetInteractGameExtraIncomeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_gold_commission_18e8a43e3dce6c6d, []int{94}
}
func (m *GetInteractGameExtraIncomeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetInteractGameExtraIncomeResp.Unmarshal(m, b)
}
func (m *GetInteractGameExtraIncomeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetInteractGameExtraIncomeResp.Marshal(b, m, deterministic)
}
func (dst *GetInteractGameExtraIncomeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetInteractGameExtraIncomeResp.Merge(dst, src)
}
func (m *GetInteractGameExtraIncomeResp) XXX_Size() int {
	return xxx_messageInfo_GetInteractGameExtraIncomeResp.Size(m)
}
func (m *GetInteractGameExtraIncomeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetInteractGameExtraIncomeResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetInteractGameExtraIncomeResp proto.InternalMessageInfo

func (m *GetInteractGameExtraIncomeResp) GetInfo() *InteractGameExtraDetail {
	if m != nil {
		return m.Info
	}
	return nil
}

type InteractGameExtraDetail struct {
	MonthTime            uint32                         `protobuf:"varint,1,opt,name=month_time,json=monthTime,proto3" json:"month_time,omitempty"`
	GameMonthTotalFee    uint64                         `protobuf:"varint,2,opt,name=game_month_total_fee,json=gameMonthTotalFee,proto3" json:"game_month_total_fee,omitempty"`
	GameMonthExtraIncome uint64                         `protobuf:"varint,3,opt,name=game_month_extra_income,json=gameMonthExtraIncome,proto3" json:"game_month_extra_income,omitempty"`
	AnchorInfoList       []*InteractGameExtraAnchorInfo `protobuf:"bytes,4,rep,name=anchor_info_list,json=anchorInfoList,proto3" json:"anchor_info_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                       `json:"-"`
	XXX_unrecognized     []byte                         `json:"-"`
	XXX_sizecache        int32                          `json:"-"`
}

func (m *InteractGameExtraDetail) Reset()         { *m = InteractGameExtraDetail{} }
func (m *InteractGameExtraDetail) String() string { return proto.CompactTextString(m) }
func (*InteractGameExtraDetail) ProtoMessage()    {}
func (*InteractGameExtraDetail) Descriptor() ([]byte, []int) {
	return fileDescriptor_gold_commission_18e8a43e3dce6c6d, []int{95}
}
func (m *InteractGameExtraDetail) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_InteractGameExtraDetail.Unmarshal(m, b)
}
func (m *InteractGameExtraDetail) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_InteractGameExtraDetail.Marshal(b, m, deterministic)
}
func (dst *InteractGameExtraDetail) XXX_Merge(src proto.Message) {
	xxx_messageInfo_InteractGameExtraDetail.Merge(dst, src)
}
func (m *InteractGameExtraDetail) XXX_Size() int {
	return xxx_messageInfo_InteractGameExtraDetail.Size(m)
}
func (m *InteractGameExtraDetail) XXX_DiscardUnknown() {
	xxx_messageInfo_InteractGameExtraDetail.DiscardUnknown(m)
}

var xxx_messageInfo_InteractGameExtraDetail proto.InternalMessageInfo

func (m *InteractGameExtraDetail) GetMonthTime() uint32 {
	if m != nil {
		return m.MonthTime
	}
	return 0
}

func (m *InteractGameExtraDetail) GetGameMonthTotalFee() uint64 {
	if m != nil {
		return m.GameMonthTotalFee
	}
	return 0
}

func (m *InteractGameExtraDetail) GetGameMonthExtraIncome() uint64 {
	if m != nil {
		return m.GameMonthExtraIncome
	}
	return 0
}

func (m *InteractGameExtraDetail) GetAnchorInfoList() []*InteractGameExtraAnchorInfo {
	if m != nil {
		return m.AnchorInfoList
	}
	return nil
}

type InteractGameExtraAnchorInfo struct {
	AnchorUid            uint32   `protobuf:"varint,1,opt,name=anchor_uid,json=anchorUid,proto3" json:"anchor_uid,omitempty"`
	GameMonthFee         uint64   `protobuf:"varint,2,opt,name=game_month_fee,json=gameMonthFee,proto3" json:"game_month_fee,omitempty"`
	GameIncomeRate       float32  `protobuf:"fixed32,3,opt,name=game_income_rate,json=gameIncomeRate,proto3" json:"game_income_rate,omitempty"`
	GameIncome           uint64   `protobuf:"varint,4,opt,name=game_income,json=gameIncome,proto3" json:"game_income,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *InteractGameExtraAnchorInfo) Reset()         { *m = InteractGameExtraAnchorInfo{} }
func (m *InteractGameExtraAnchorInfo) String() string { return proto.CompactTextString(m) }
func (*InteractGameExtraAnchorInfo) ProtoMessage()    {}
func (*InteractGameExtraAnchorInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_gold_commission_18e8a43e3dce6c6d, []int{96}
}
func (m *InteractGameExtraAnchorInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_InteractGameExtraAnchorInfo.Unmarshal(m, b)
}
func (m *InteractGameExtraAnchorInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_InteractGameExtraAnchorInfo.Marshal(b, m, deterministic)
}
func (dst *InteractGameExtraAnchorInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_InteractGameExtraAnchorInfo.Merge(dst, src)
}
func (m *InteractGameExtraAnchorInfo) XXX_Size() int {
	return xxx_messageInfo_InteractGameExtraAnchorInfo.Size(m)
}
func (m *InteractGameExtraAnchorInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_InteractGameExtraAnchorInfo.DiscardUnknown(m)
}

var xxx_messageInfo_InteractGameExtraAnchorInfo proto.InternalMessageInfo

func (m *InteractGameExtraAnchorInfo) GetAnchorUid() uint32 {
	if m != nil {
		return m.AnchorUid
	}
	return 0
}

func (m *InteractGameExtraAnchorInfo) GetGameMonthFee() uint64 {
	if m != nil {
		return m.GameMonthFee
	}
	return 0
}

func (m *InteractGameExtraAnchorInfo) GetGameIncomeRate() float32 {
	if m != nil {
		return m.GameIncomeRate
	}
	return 0
}

func (m *InteractGameExtraAnchorInfo) GetGameIncome() uint64 {
	if m != nil {
		return m.GameIncome
	}
	return 0
}

// 获取会长服务号电竞收益
type GetESportIncomeDetailReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	GuildId              uint32   `protobuf:"varint,2,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetESportIncomeDetailReq) Reset()         { *m = GetESportIncomeDetailReq{} }
func (m *GetESportIncomeDetailReq) String() string { return proto.CompactTextString(m) }
func (*GetESportIncomeDetailReq) ProtoMessage()    {}
func (*GetESportIncomeDetailReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_gold_commission_18e8a43e3dce6c6d, []int{97}
}
func (m *GetESportIncomeDetailReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetESportIncomeDetailReq.Unmarshal(m, b)
}
func (m *GetESportIncomeDetailReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetESportIncomeDetailReq.Marshal(b, m, deterministic)
}
func (dst *GetESportIncomeDetailReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetESportIncomeDetailReq.Merge(dst, src)
}
func (m *GetESportIncomeDetailReq) XXX_Size() int {
	return xxx_messageInfo_GetESportIncomeDetailReq.Size(m)
}
func (m *GetESportIncomeDetailReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetESportIncomeDetailReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetESportIncomeDetailReq proto.InternalMessageInfo

func (m *GetESportIncomeDetailReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetESportIncomeDetailReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

type GetESportIncomeDetailRsp struct {
	DailyIncome          *DailyIncome   `protobuf:"bytes,2,opt,name=daily_income,json=dailyIncome,proto3" json:"daily_income,omitempty"`
	WeeklyIncome         *WeeklyIncome  `protobuf:"bytes,3,opt,name=weekly_income,json=weeklyIncome,proto3" json:"weekly_income,omitempty"`
	MonthlyIncome        *MonthlyIncome `protobuf:"bytes,4,opt,name=monthly_income,json=monthlyIncome,proto3" json:"monthly_income,omitempty"`
	MonthlyExtraIncome   *MonthlyIncome `protobuf:"bytes,5,opt,name=monthly_extra_income,json=monthlyExtraIncome,proto3" json:"monthly_extra_income,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *GetESportIncomeDetailRsp) Reset()         { *m = GetESportIncomeDetailRsp{} }
func (m *GetESportIncomeDetailRsp) String() string { return proto.CompactTextString(m) }
func (*GetESportIncomeDetailRsp) ProtoMessage()    {}
func (*GetESportIncomeDetailRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_gold_commission_18e8a43e3dce6c6d, []int{98}
}
func (m *GetESportIncomeDetailRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetESportIncomeDetailRsp.Unmarshal(m, b)
}
func (m *GetESportIncomeDetailRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetESportIncomeDetailRsp.Marshal(b, m, deterministic)
}
func (dst *GetESportIncomeDetailRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetESportIncomeDetailRsp.Merge(dst, src)
}
func (m *GetESportIncomeDetailRsp) XXX_Size() int {
	return xxx_messageInfo_GetESportIncomeDetailRsp.Size(m)
}
func (m *GetESportIncomeDetailRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetESportIncomeDetailRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GetESportIncomeDetailRsp proto.InternalMessageInfo

func (m *GetESportIncomeDetailRsp) GetDailyIncome() *DailyIncome {
	if m != nil {
		return m.DailyIncome
	}
	return nil
}

func (m *GetESportIncomeDetailRsp) GetWeeklyIncome() *WeeklyIncome {
	if m != nil {
		return m.WeeklyIncome
	}
	return nil
}

func (m *GetESportIncomeDetailRsp) GetMonthlyIncome() *MonthlyIncome {
	if m != nil {
		return m.MonthlyIncome
	}
	return nil
}

func (m *GetESportIncomeDetailRsp) GetMonthlyExtraIncome() *MonthlyIncome {
	if m != nil {
		return m.MonthlyExtraIncome
	}
	return nil
}

// 获取电竞会长该周收益情况
type GetESportWeekIncomeReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	GuildId              uint32   `protobuf:"varint,2,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetESportWeekIncomeReq) Reset()         { *m = GetESportWeekIncomeReq{} }
func (m *GetESportWeekIncomeReq) String() string { return proto.CompactTextString(m) }
func (*GetESportWeekIncomeReq) ProtoMessage()    {}
func (*GetESportWeekIncomeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_gold_commission_18e8a43e3dce6c6d, []int{99}
}
func (m *GetESportWeekIncomeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetESportWeekIncomeReq.Unmarshal(m, b)
}
func (m *GetESportWeekIncomeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetESportWeekIncomeReq.Marshal(b, m, deterministic)
}
func (dst *GetESportWeekIncomeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetESportWeekIncomeReq.Merge(dst, src)
}
func (m *GetESportWeekIncomeReq) XXX_Size() int {
	return xxx_messageInfo_GetESportWeekIncomeReq.Size(m)
}
func (m *GetESportWeekIncomeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetESportWeekIncomeReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetESportWeekIncomeReq proto.InternalMessageInfo

func (m *GetESportWeekIncomeReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetESportWeekIncomeReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

type GetESportWeekIncomeRsp struct {
	WeeklyIncome         *WeeklyIncome `protobuf:"bytes,1,opt,name=weekly_income,json=weeklyIncome,proto3" json:"weekly_income,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetESportWeekIncomeRsp) Reset()         { *m = GetESportWeekIncomeRsp{} }
func (m *GetESportWeekIncomeRsp) String() string { return proto.CompactTextString(m) }
func (*GetESportWeekIncomeRsp) ProtoMessage()    {}
func (*GetESportWeekIncomeRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_gold_commission_18e8a43e3dce6c6d, []int{100}
}
func (m *GetESportWeekIncomeRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetESportWeekIncomeRsp.Unmarshal(m, b)
}
func (m *GetESportWeekIncomeRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetESportWeekIncomeRsp.Marshal(b, m, deterministic)
}
func (dst *GetESportWeekIncomeRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetESportWeekIncomeRsp.Merge(dst, src)
}
func (m *GetESportWeekIncomeRsp) XXX_Size() int {
	return xxx_messageInfo_GetESportWeekIncomeRsp.Size(m)
}
func (m *GetESportWeekIncomeRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetESportWeekIncomeRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GetESportWeekIncomeRsp proto.InternalMessageInfo

func (m *GetESportWeekIncomeRsp) GetWeeklyIncome() *WeeklyIncome {
	if m != nil {
		return m.WeeklyIncome
	}
	return nil
}

type ESportCoachIncomeInfo struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	CoachUid             uint32   `protobuf:"varint,2,opt,name=coach_uid,json=coachUid,proto3" json:"coach_uid,omitempty"`
	Date                 uint32   `protobuf:"varint,3,opt,name=date,proto3" json:"date,omitempty"`
	EsportFee            uint64   `protobuf:"varint,4,opt,name=esport_fee,json=esportFee,proto3" json:"esport_fee,omitempty"`
	EsportIncome         uint64   `protobuf:"varint,5,opt,name=esport_income,json=esportIncome,proto3" json:"esport_income,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ESportCoachIncomeInfo) Reset()         { *m = ESportCoachIncomeInfo{} }
func (m *ESportCoachIncomeInfo) String() string { return proto.CompactTextString(m) }
func (*ESportCoachIncomeInfo) ProtoMessage()    {}
func (*ESportCoachIncomeInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_gold_commission_18e8a43e3dce6c6d, []int{101}
}
func (m *ESportCoachIncomeInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ESportCoachIncomeInfo.Unmarshal(m, b)
}
func (m *ESportCoachIncomeInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ESportCoachIncomeInfo.Marshal(b, m, deterministic)
}
func (dst *ESportCoachIncomeInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ESportCoachIncomeInfo.Merge(dst, src)
}
func (m *ESportCoachIncomeInfo) XXX_Size() int {
	return xxx_messageInfo_ESportCoachIncomeInfo.Size(m)
}
func (m *ESportCoachIncomeInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ESportCoachIncomeInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ESportCoachIncomeInfo proto.InternalMessageInfo

func (m *ESportCoachIncomeInfo) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *ESportCoachIncomeInfo) GetCoachUid() uint32 {
	if m != nil {
		return m.CoachUid
	}
	return 0
}

func (m *ESportCoachIncomeInfo) GetDate() uint32 {
	if m != nil {
		return m.Date
	}
	return 0
}

func (m *ESportCoachIncomeInfo) GetEsportFee() uint64 {
	if m != nil {
		return m.EsportFee
	}
	return 0
}

func (m *ESportCoachIncomeInfo) GetEsportIncome() uint64 {
	if m != nil {
		return m.EsportIncome
	}
	return 0
}

type GetESportCoachMonthIncomeListReq struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	MonthTime            uint32   `protobuf:"varint,2,opt,name=month_time,json=monthTime,proto3" json:"month_time,omitempty"`
	Offset               uint32   `protobuf:"varint,3,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32   `protobuf:"varint,4,opt,name=limit,proto3" json:"limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetESportCoachMonthIncomeListReq) Reset()         { *m = GetESportCoachMonthIncomeListReq{} }
func (m *GetESportCoachMonthIncomeListReq) String() string { return proto.CompactTextString(m) }
func (*GetESportCoachMonthIncomeListReq) ProtoMessage()    {}
func (*GetESportCoachMonthIncomeListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_gold_commission_18e8a43e3dce6c6d, []int{102}
}
func (m *GetESportCoachMonthIncomeListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetESportCoachMonthIncomeListReq.Unmarshal(m, b)
}
func (m *GetESportCoachMonthIncomeListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetESportCoachMonthIncomeListReq.Marshal(b, m, deterministic)
}
func (dst *GetESportCoachMonthIncomeListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetESportCoachMonthIncomeListReq.Merge(dst, src)
}
func (m *GetESportCoachMonthIncomeListReq) XXX_Size() int {
	return xxx_messageInfo_GetESportCoachMonthIncomeListReq.Size(m)
}
func (m *GetESportCoachMonthIncomeListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetESportCoachMonthIncomeListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetESportCoachMonthIncomeListReq proto.InternalMessageInfo

func (m *GetESportCoachMonthIncomeListReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GetESportCoachMonthIncomeListReq) GetMonthTime() uint32 {
	if m != nil {
		return m.MonthTime
	}
	return 0
}

func (m *GetESportCoachMonthIncomeListReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetESportCoachMonthIncomeListReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type GetESportCoachMonthIncomeListResp struct {
	CoachList            []*ESportCoachIncomeInfo `protobuf:"bytes,1,rep,name=coach_list,json=coachList,proto3" json:"coach_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *GetESportCoachMonthIncomeListResp) Reset()         { *m = GetESportCoachMonthIncomeListResp{} }
func (m *GetESportCoachMonthIncomeListResp) String() string { return proto.CompactTextString(m) }
func (*GetESportCoachMonthIncomeListResp) ProtoMessage()    {}
func (*GetESportCoachMonthIncomeListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_gold_commission_18e8a43e3dce6c6d, []int{103}
}
func (m *GetESportCoachMonthIncomeListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetESportCoachMonthIncomeListResp.Unmarshal(m, b)
}
func (m *GetESportCoachMonthIncomeListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetESportCoachMonthIncomeListResp.Marshal(b, m, deterministic)
}
func (dst *GetESportCoachMonthIncomeListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetESportCoachMonthIncomeListResp.Merge(dst, src)
}
func (m *GetESportCoachMonthIncomeListResp) XXX_Size() int {
	return xxx_messageInfo_GetESportCoachMonthIncomeListResp.Size(m)
}
func (m *GetESportCoachMonthIncomeListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetESportCoachMonthIncomeListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetESportCoachMonthIncomeListResp proto.InternalMessageInfo

func (m *GetESportCoachMonthIncomeListResp) GetCoachList() []*ESportCoachIncomeInfo {
	if m != nil {
		return m.CoachList
	}
	return nil
}

type GetESportCoachDayIncomeListReq struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	StartTime            uint32   `protobuf:"varint,2,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	EndTime              uint32   `protobuf:"varint,3,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	CoachUid             uint32   `protobuf:"varint,4,opt,name=coach_uid,json=coachUid,proto3" json:"coach_uid,omitempty"`
	Offset               uint32   `protobuf:"varint,5,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32   `protobuf:"varint,6,opt,name=limit,proto3" json:"limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetESportCoachDayIncomeListReq) Reset()         { *m = GetESportCoachDayIncomeListReq{} }
func (m *GetESportCoachDayIncomeListReq) String() string { return proto.CompactTextString(m) }
func (*GetESportCoachDayIncomeListReq) ProtoMessage()    {}
func (*GetESportCoachDayIncomeListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_gold_commission_18e8a43e3dce6c6d, []int{104}
}
func (m *GetESportCoachDayIncomeListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetESportCoachDayIncomeListReq.Unmarshal(m, b)
}
func (m *GetESportCoachDayIncomeListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetESportCoachDayIncomeListReq.Marshal(b, m, deterministic)
}
func (dst *GetESportCoachDayIncomeListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetESportCoachDayIncomeListReq.Merge(dst, src)
}
func (m *GetESportCoachDayIncomeListReq) XXX_Size() int {
	return xxx_messageInfo_GetESportCoachDayIncomeListReq.Size(m)
}
func (m *GetESportCoachDayIncomeListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetESportCoachDayIncomeListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetESportCoachDayIncomeListReq proto.InternalMessageInfo

func (m *GetESportCoachDayIncomeListReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GetESportCoachDayIncomeListReq) GetStartTime() uint32 {
	if m != nil {
		return m.StartTime
	}
	return 0
}

func (m *GetESportCoachDayIncomeListReq) GetEndTime() uint32 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *GetESportCoachDayIncomeListReq) GetCoachUid() uint32 {
	if m != nil {
		return m.CoachUid
	}
	return 0
}

func (m *GetESportCoachDayIncomeListReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetESportCoachDayIncomeListReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type GetESportCoachDayIncomeListResp struct {
	CoachList            []*ESportCoachIncomeInfo `protobuf:"bytes,1,rep,name=coach_list,json=coachList,proto3" json:"coach_list,omitempty"`
	StartTime            uint32                   `protobuf:"varint,3,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	EndTime              uint32                   `protobuf:"varint,4,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	TotalEsportFee       uint64                   `protobuf:"varint,5,opt,name=total_esport_fee,json=totalEsportFee,proto3" json:"total_esport_fee,omitempty"`
	TotalEsportIncome    uint64                   `protobuf:"varint,6,opt,name=total_esport_income,json=totalEsportIncome,proto3" json:"total_esport_income,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *GetESportCoachDayIncomeListResp) Reset()         { *m = GetESportCoachDayIncomeListResp{} }
func (m *GetESportCoachDayIncomeListResp) String() string { return proto.CompactTextString(m) }
func (*GetESportCoachDayIncomeListResp) ProtoMessage()    {}
func (*GetESportCoachDayIncomeListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_gold_commission_18e8a43e3dce6c6d, []int{105}
}
func (m *GetESportCoachDayIncomeListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetESportCoachDayIncomeListResp.Unmarshal(m, b)
}
func (m *GetESportCoachDayIncomeListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetESportCoachDayIncomeListResp.Marshal(b, m, deterministic)
}
func (dst *GetESportCoachDayIncomeListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetESportCoachDayIncomeListResp.Merge(dst, src)
}
func (m *GetESportCoachDayIncomeListResp) XXX_Size() int {
	return xxx_messageInfo_GetESportCoachDayIncomeListResp.Size(m)
}
func (m *GetESportCoachDayIncomeListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetESportCoachDayIncomeListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetESportCoachDayIncomeListResp proto.InternalMessageInfo

func (m *GetESportCoachDayIncomeListResp) GetCoachList() []*ESportCoachIncomeInfo {
	if m != nil {
		return m.CoachList
	}
	return nil
}

func (m *GetESportCoachDayIncomeListResp) GetStartTime() uint32 {
	if m != nil {
		return m.StartTime
	}
	return 0
}

func (m *GetESportCoachDayIncomeListResp) GetEndTime() uint32 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *GetESportCoachDayIncomeListResp) GetTotalEsportFee() uint64 {
	if m != nil {
		return m.TotalEsportFee
	}
	return 0
}

func (m *GetESportCoachDayIncomeListResp) GetTotalEsportIncome() uint64 {
	if m != nil {
		return m.TotalEsportIncome
	}
	return 0
}

func init() {
	proto.RegisterType((*GetAmuseRoomDayQoqInfoReq)(nil), "gold_commission.GetAmuseRoomDayQoqInfoReq")
	proto.RegisterType((*GetAmuseRoomDayQoqInfoRsp)(nil), "gold_commission.GetAmuseRoomDayQoqInfoRsp")
	proto.RegisterType((*GetAmuseChannelDetailReq)(nil), "gold_commission.GetAmuseChannelDetailReq")
	proto.RegisterType((*GetAmuseChannelDetailRsp)(nil), "gold_commission.GetAmuseChannelDetailRsp")
	proto.RegisterType((*ChannelFee)(nil), "gold_commission.ChannelFee")
	proto.RegisterType((*GetAmuseIncomeDetailReq)(nil), "gold_commission.GetAmuseIncomeDetailReq")
	proto.RegisterType((*GetAmuseIncomeDetailRsp)(nil), "gold_commission.GetAmuseIncomeDetailRsp")
	proto.RegisterType((*StatIncomeInfo)(nil), "gold_commission.StatIncomeInfo")
	proto.RegisterType((*GetAmuseGuildRoomIncomeListReq)(nil), "gold_commission.GetAmuseGuildRoomIncomeListReq")
	proto.RegisterType((*GetAmuseGuildRoomIncomeListRsp)(nil), "gold_commission.GetAmuseGuildRoomIncomeListRsp")
	proto.RegisterType((*SettlementYuyinGoldReq)(nil), "gold_commission.SettlementYuyinGoldReq")
	proto.RegisterType((*SettlementYuyinGoldResp)(nil), "gold_commission.SettlementYuyinGoldResp")
	proto.RegisterType((*GenYuyinExtraReportReq)(nil), "gold_commission.GenYuyinExtraReportReq")
	proto.RegisterType((*GenYuyinExtraReportResp)(nil), "gold_commission.GenYuyinExtraReportResp")
	proto.RegisterType((*AwardGoldOrder)(nil), "gold_commission.AwardGoldOrder")
	proto.RegisterType((*BaseAnchorInfo)(nil), "gold_commission.BaseAnchorInfo")
	proto.RegisterType((*BaseChannelInfo)(nil), "gold_commission.BaseChannelInfo")
	proto.RegisterType((*BaseGuildInfo)(nil), "gold_commission.BaseGuildInfo")
	proto.RegisterType((*GetGuildsYuyinAnchorStatReq)(nil), "gold_commission.GetGuildsYuyinAnchorStatReq")
	proto.RegisterType((*GetGuildsYuyinAnchorStatResp)(nil), "gold_commission.GetGuildsYuyinAnchorStatResp")
	proto.RegisterType((*GetGuildsAmuseChannelStatReq)(nil), "gold_commission.GetGuildsAmuseChannelStatReq")
	proto.RegisterType((*GetGuildsAmuseChannelStatResp)(nil), "gold_commission.GetGuildsAmuseChannelStatResp")
	proto.RegisterType((*GetGuildsInteractGameAnchorStatReq)(nil), "gold_commission.GetGuildsInteractGameAnchorStatReq")
	proto.RegisterType((*GetGuildsInteractGameAnchorStatResp)(nil), "gold_commission.GetGuildsInteractGameAnchorStatResp")
	proto.RegisterType((*GetGuildsESportCoachStatReq)(nil), "gold_commission.GetGuildsESportCoachStatReq")
	proto.RegisterType((*GetGuildsESportCoachStatResp)(nil), "gold_commission.GetGuildsESportCoachStatResp")
	proto.RegisterType((*GetGuildsUnSettlementSummaryReq)(nil), "gold_commission.GetGuildsUnSettlementSummaryReq")
	proto.RegisterType((*GetGuildsUnSettlementSummaryRsp)(nil), "gold_commission.GetGuildsUnSettlementSummaryRsp")
	proto.RegisterType((*GetYuyinIncomeDetailReq)(nil), "gold_commission.GetYuyinIncomeDetailReq")
	proto.RegisterType((*GetYuyinIncomeDetailRsp)(nil), "gold_commission.GetYuyinIncomeDetailRsp")
	proto.RegisterType((*DailyIncome)(nil), "gold_commission.DailyIncome")
	proto.RegisterType((*WeeklyIncome)(nil), "gold_commission.WeeklyIncome")
	proto.RegisterType((*MonthlyIncome)(nil), "gold_commission.MonthlyIncome")
	proto.RegisterType((*GetIncomeTrendListReq)(nil), "gold_commission.GetIncomeTrendListReq")
	proto.RegisterType((*GetIncomeTrendListRsp)(nil), "gold_commission.GetIncomeTrendListRsp")
	proto.RegisterType((*TrendInfo)(nil), "gold_commission.TrendInfo")
	proto.RegisterType((*GetConsumeRankReq)(nil), "gold_commission.GetConsumeRankReq")
	proto.RegisterType((*GetConsumeRankRsp)(nil), "gold_commission.GetConsumeRankRsp")
	proto.RegisterType((*ConsumeRankItem)(nil), "gold_commission.ConsumeRankItem")
	proto.RegisterType((*GetDayIncomeReq)(nil), "gold_commission.GetDayIncomeReq")
	proto.RegisterType((*GetDayIncomeRsp)(nil), "gold_commission.GetDayIncomeRsp")
	proto.RegisterType((*GetYuyinWeekIncomeReq)(nil), "gold_commission.GetYuyinWeekIncomeReq")
	proto.RegisterType((*GetYuyinWeekIncomeRsp)(nil), "gold_commission.GetYuyinWeekIncomeRsp")
	proto.RegisterType((*GetInteractGameWeekIncomeReq)(nil), "gold_commission.GetInteractGameWeekIncomeReq")
	proto.RegisterType((*GetInteractGameWeekIncomeRsp)(nil), "gold_commission.GetInteractGameWeekIncomeRsp")
	proto.RegisterType((*GetMonthIncomeReq)(nil), "gold_commission.GetMonthIncomeReq")
	proto.RegisterType((*GetMonthIncomeRsp)(nil), "gold_commission.GetMonthIncomeRsp")
	proto.RegisterType((*GetGuildYuyinExtraIncomeReq)(nil), "gold_commission.GetGuildYuyinExtraIncomeReq")
	proto.RegisterType((*GetGuildYuyinExtraIncomeRsp)(nil), "gold_commission.GetGuildYuyinExtraIncomeRsp")
	proto.RegisterType((*ExtraIncome)(nil), "gold_commission.ExtraIncome")
	proto.RegisterType((*GetGuildYuyinTaskListReq)(nil), "gold_commission.GetGuildYuyinTaskListReq")
	proto.RegisterType((*GetGuildYuyinTaskListRsp)(nil), "gold_commission.GetGuildYuyinTaskListRsp")
	proto.RegisterType((*GuildTaskInfo)(nil), "gold_commission.GuildTaskInfo")
	proto.RegisterType((*GuildTaskRatioInfo)(nil), "gold_commission.GuildTaskRatioInfo")
	proto.RegisterType((*GuildTaskDetailInfo)(nil), "gold_commission.GuildTaskDetailInfo")
	proto.RegisterType((*GetGuildDayIncomeListReq)(nil), "gold_commission.GetGuildDayIncomeListReq")
	proto.RegisterType((*GetYuyinGuildDayIncomeListResp)(nil), "gold_commission.GetYuyinGuildDayIncomeListResp")
	proto.RegisterType((*GetAmuseGuildDayIncomeListResp)(nil), "gold_commission.GetAmuseGuildDayIncomeListResp")
	proto.RegisterType((*GetInteractGameDayIncomeListResp)(nil), "gold_commission.GetInteractGameDayIncomeListResp")
	proto.RegisterType((*GetESportDayIncomeListResp)(nil), "gold_commission.GetESportDayIncomeListResp")
	proto.RegisterType((*GetGuildMonthIncomeListReq)(nil), "gold_commission.GetGuildMonthIncomeListReq")
	proto.RegisterType((*GetGuildMonthIncomeListResp)(nil), "gold_commission.GetGuildMonthIncomeListResp")
	proto.RegisterType((*GetInteractGameMonthIncomeListResp)(nil), "gold_commission.GetInteractGameMonthIncomeListResp")
	proto.RegisterType((*GetESportMonthIncomeListResp)(nil), "gold_commission.GetESportMonthIncomeListResp")
	proto.RegisterType((*GetGuildMonthMemberListReq)(nil), "gold_commission.GetGuildMonthMemberListReq")
	proto.RegisterType((*GetGuildMonthMemberListResp)(nil), "gold_commission.GetGuildMonthMemberListResp")
	proto.RegisterType((*SearchYuyinGuildDetailReq)(nil), "gold_commission.SearchYuyinGuildDetailReq")
	proto.RegisterType((*SearchYuyinGuildDetailResp)(nil), "gold_commission.SearchYuyinGuildDetailResp")
	proto.RegisterType((*SearchAmuseGuildDetailReq)(nil), "gold_commission.SearchAmuseGuildDetailReq")
	proto.RegisterType((*SearchAmuseGuildDetailResp)(nil), "gold_commission.SearchAmuseGuildDetailResp")
	proto.RegisterType((*GetGuildDayQoqReq)(nil), "gold_commission.GetGuildDayQoqReq")
	proto.RegisterType((*GetGuildDayQoqResp)(nil), "gold_commission.GetGuildDayQoqResp")
	proto.RegisterType((*AmuseGuildChannelIncomeListReq)(nil), "gold_commission.AmuseGuildChannelIncomeListReq")
	proto.RegisterType((*AmuseGuildChannelIncomeListResp)(nil), "gold_commission.AmuseGuildChannelIncomeListResp")
	proto.RegisterType((*AmuseGuildChannelIncomeListRespLItem)(nil), "gold_commission.AmuseGuildChannelIncomeListResp.lItem")
	proto.RegisterType((*GetAmuseGuildIdsByRangeReq)(nil), "gold_commission.GetAmuseGuildIdsByRangeReq")
	proto.RegisterType((*GetAmuseGuildIdsByRangeResp)(nil), "gold_commission.GetAmuseGuildIdsByRangeResp")
	proto.RegisterType((*AmuseExtraIncomeSettleItem)(nil), "gold_commission.AmuseExtraIncomeSettleItem")
	proto.RegisterType((*AmuseExtraIncomeSettleItem_Guild)(nil), "gold_commission.AmuseExtraIncomeSettleItem.Guild")
	proto.RegisterType((*AmuseExtraIncomeSettleItem_Channel)(nil), "gold_commission.AmuseExtraIncomeSettleItem.Channel")
	proto.RegisterType((*GetAmuseExtraIncomeSettleListReq)(nil), "gold_commission.GetAmuseExtraIncomeSettleListReq")
	proto.RegisterType((*GetAmuseExtraIncomeSettleListResp)(nil), "gold_commission.GetAmuseExtraIncomeSettleListResp")
	proto.RegisterType((*GetAmuseSettleGuildListReq)(nil), "gold_commission.GetAmuseSettleGuildListReq")
	proto.RegisterType((*GetAmuseSettleGuildListResp)(nil), "gold_commission.GetAmuseSettleGuildListResp")
	proto.RegisterType((*GetAmuseSettleGuildListResp_Guild)(nil), "gold_commission.GetAmuseSettleGuildListResp.Guild")
	proto.RegisterType((*GetAmuseSettleChannelListReq)(nil), "gold_commission.GetAmuseSettleChannelListReq")
	proto.RegisterType((*GetAmuseSettleChannelListResp)(nil), "gold_commission.GetAmuseSettleChannelListResp")
	proto.RegisterType((*GetAmuseSettleChannelListResp_Channel)(nil), "gold_commission.GetAmuseSettleChannelListResp.Channel")
	proto.RegisterType((*SetAmuseSettleGuildReq)(nil), "gold_commission.SetAmuseSettleGuildReq")
	proto.RegisterType((*SetAmuseSettleGuildResp)(nil), "gold_commission.SetAmuseSettleGuildResp")
	proto.RegisterType((*SetAmuseSettleChannelReq)(nil), "gold_commission.SetAmuseSettleChannelReq")
	proto.RegisterType((*SetAmuseSettleChannelResp)(nil), "gold_commission.SetAmuseSettleChannelResp")
	proto.RegisterType((*GetAmuseExtraDetailReq)(nil), "gold_commission.GetAmuseExtraDetailReq")
	proto.RegisterType((*GetAmuseExtraDetailResp)(nil), "gold_commission.GetAmuseExtraDetailResp")
	proto.RegisterType((*GetAmuseExtraDetailResp_ChannelItem)(nil), "gold_commission.GetAmuseExtraDetailResp.ChannelItem")
	proto.RegisterType((*GetInteractGameIncomeDetailReq)(nil), "gold_commission.GetInteractGameIncomeDetailReq")
	proto.RegisterType((*GetInteractGameIncomeDetailRsp)(nil), "gold_commission.GetInteractGameIncomeDetailRsp")
	proto.RegisterType((*GetGuildInteractGamePerReq)(nil), "gold_commission.GetGuildInteractGamePerReq")
	proto.RegisterType((*GetGuildInteractGamePerResp)(nil), "gold_commission.GetGuildInteractGamePerResp")
	proto.RegisterType((*GetInteractGameExtraIncomeReq)(nil), "gold_commission.GetInteractGameExtraIncomeReq")
	proto.RegisterType((*GetInteractGameExtraIncomeResp)(nil), "gold_commission.GetInteractGameExtraIncomeResp")
	proto.RegisterType((*InteractGameExtraDetail)(nil), "gold_commission.InteractGameExtraDetail")
	proto.RegisterType((*InteractGameExtraAnchorInfo)(nil), "gold_commission.InteractGameExtraAnchorInfo")
	proto.RegisterType((*GetESportIncomeDetailReq)(nil), "gold_commission.GetESportIncomeDetailReq")
	proto.RegisterType((*GetESportIncomeDetailRsp)(nil), "gold_commission.GetESportIncomeDetailRsp")
	proto.RegisterType((*GetESportWeekIncomeReq)(nil), "gold_commission.GetESportWeekIncomeReq")
	proto.RegisterType((*GetESportWeekIncomeRsp)(nil), "gold_commission.GetESportWeekIncomeRsp")
	proto.RegisterType((*ESportCoachIncomeInfo)(nil), "gold_commission.ESportCoachIncomeInfo")
	proto.RegisterType((*GetESportCoachMonthIncomeListReq)(nil), "gold_commission.GetESportCoachMonthIncomeListReq")
	proto.RegisterType((*GetESportCoachMonthIncomeListResp)(nil), "gold_commission.GetESportCoachMonthIncomeListResp")
	proto.RegisterType((*GetESportCoachDayIncomeListReq)(nil), "gold_commission.GetESportCoachDayIncomeListReq")
	proto.RegisterType((*GetESportCoachDayIncomeListResp)(nil), "gold_commission.GetESportCoachDayIncomeListResp")
	proto.RegisterEnum("gold_commission.GoldType", GoldType_name, GoldType_value)
	proto.RegisterEnum("gold_commission.SourceType", SourceType_name, SourceType_value)
	proto.RegisterEnum("gold_commission.ChannelType", ChannelType_name, ChannelType_value)
	proto.RegisterEnum("gold_commission.TimeFilterUnit", TimeFilterUnit_name, TimeFilterUnit_value)
	proto.RegisterEnum("gold_commission.ReqSettleStatus", ReqSettleStatus_name, ReqSettleStatus_value)
	proto.RegisterEnum("gold_commission.RangeType", RangeType_name, RangeType_value)
	proto.RegisterEnum("gold_commission.GetGuildMonthMemberListReq_QueryType", GetGuildMonthMemberListReq_QueryType_name, GetGuildMonthMemberListReq_QueryType_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// GoldCommissionClient is the client API for GoldCommission service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type GoldCommissionClient interface {
	// 对账 语音礼物佣金补单
	GetYuyinGoldPresentOrderCount(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.CountResp, error)
	GetYuyinGoldPresentOrderList(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.OrderIdsResp, error)
	ReplaceYuyinGoldPresentOrder(ctx context.Context, in *reconcile_v2.ReplaceOrderReq, opts ...grpc.CallOption) (*reconcile_v2.EmptyResp, error)
	// 对账 语音骑士佣金补单
	GetYuyinGoldKnightOrderCount(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.CountResp, error)
	GetYuyinGoldKnightOrderList(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.OrderIdsResp, error)
	ReplaceYuyinGoldKnightOrder(ctx context.Context, in *reconcile_v2.ReplaceOrderReq, opts ...grpc.CallOption) (*reconcile_v2.EmptyResp, error)
	// 对账 娱乐房礼物佣金补单
	GetAmuseGoldPresentOrderCount(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.CountResp, error)
	GetAmuseGoldPresentOrderList(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.OrderIdsResp, error)
	ReplaceAmuseGoldPresentOrder(ctx context.Context, in *reconcile_v2.ReplaceOrderReq, opts ...grpc.CallOption) (*reconcile_v2.EmptyResp, error)
	// 对账 娱乐房狼人杀佣金补单
	GetAmuseGoldWerewolfOrderCount(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.CountResp, error)
	GetAmuseGoldWerewolfOrderList(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.OrderIdsResp, error)
	ReplaceAmuseGoldWerewolfOrder(ctx context.Context, in *reconcile_v2.ReplaceOrderReq, opts ...grpc.CallOption) (*reconcile_v2.EmptyResp, error)
	// 对账 互动游戏佣金补单
	GetInteractGameGoldPresentOrderCount(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.CountResp, error)
	GetInteractGameGoldPresentOrderList(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.OrderIdsResp, error)
	ReplaceInteractGameGoldPresentOrder(ctx context.Context, in *reconcile_v2.ReplaceOrderReq, opts ...grpc.CallOption) (*reconcile_v2.EmptyResp, error)
	// 对账 电竞佣金补单
	GetESportGoldOrderCount(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.CountResp, error)
	GetESportGoldOrderList(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.OrderIdsResp, error)
	ReplaceESportGoldOrder(ctx context.Context, in *reconcile_v2.ReplaceOrderReq, opts ...grpc.CallOption) (*reconcile_v2.EmptyResp, error)
	// 获取对公会长旗下多个公会下的语音主播/房间流水统计
	GetGuildsYuyinAnchorStat(ctx context.Context, in *GetGuildsYuyinAnchorStatReq, opts ...grpc.CallOption) (*GetGuildsYuyinAnchorStatResp, error)
	GetGuildsAmuseChannelStat(ctx context.Context, in *GetGuildsAmuseChannelStatReq, opts ...grpc.CallOption) (*GetGuildsAmuseChannelStatResp, error)
	GetGuildsInteractGameAnchorStat(ctx context.Context, in *GetGuildsInteractGameAnchorStatReq, opts ...grpc.CallOption) (*GetGuildsInteractGameAnchorStatResp, error)
	GetGuildsESportCoachStat(ctx context.Context, in *GetGuildsESportCoachStatReq, opts ...grpc.CallOption) (*GetGuildsESportCoachStatResp, error)
	// 获取公会下未结算总流水
	GetGuildsUnSettlementSummary(ctx context.Context, in *GetGuildsUnSettlementSummaryReq, opts ...grpc.CallOption) (*GetGuildsUnSettlementSummaryRsp, error)
	// 娱乐房收入情况查询
	AmuseGuildChannelIncomeList(ctx context.Context, in *AmuseGuildChannelIncomeListReq, opts ...grpc.CallOption) (*AmuseGuildChannelIncomeListResp, error)
	// 房间类型合并接口
	// 获取消费排行榜
	GetConsumeRank(ctx context.Context, in *GetConsumeRankReq, opts ...grpc.CallOption) (*GetConsumeRankRsp, error)
	// 根据截止时间获取月收益日趋势列表
	GetIncomeTrendList(ctx context.Context, in *GetIncomeTrendListReq, opts ...grpc.CallOption) (*GetIncomeTrendListRsp, error)
	// 获取日收益对比
	GetGuildDayQoq(ctx context.Context, in *GetGuildDayQoqReq, opts ...grpc.CallOption) (*GetGuildDayQoqResp, error)
	// 获取公会月详情列表（消费成员或主播维度）
	GetGuildMonthMemberList(ctx context.Context, in *GetGuildMonthMemberListReq, opts ...grpc.CallOption) (*GetGuildMonthMemberListResp, error)
	// 获取该日收益情况
	GetDayIncome(ctx context.Context, in *GetDayIncomeReq, opts ...grpc.CallOption) (*GetDayIncomeRsp, error)
	// 获取该月收益情况
	GetMonthIncome(ctx context.Context, in *GetMonthIncomeReq, opts ...grpc.CallOption) (*GetMonthIncomeRsp, error)
	// 娱乐房佣金接口
	// 获取娱乐房房间详情
	GetAmuseChannelDetail(ctx context.Context, in *GetAmuseChannelDetailReq, opts ...grpc.CallOption) (*GetAmuseChannelDetailRsp, error)
	// 获取娱乐房收入情况
	GetAmuseIncomeDetail(ctx context.Context, in *GetAmuseIncomeDetailReq, opts ...grpc.CallOption) (*GetAmuseIncomeDetailRsp, error)
	// 获取工会娱乐房房间日流水列表（房间纬度）
	GetAmuseGuildRoomIncomeList(ctx context.Context, in *GetAmuseGuildRoomIncomeListReq, opts ...grpc.CallOption) (*GetAmuseGuildRoomIncomeListRsp, error)
	// 获取语音直播公会日收益详情列表（主播维度）
	GetAmuseGuildDayIncomeList(ctx context.Context, in *GetGuildDayIncomeListReq, opts ...grpc.CallOption) (*GetAmuseGuildDayIncomeListResp, error)
	// 获取语音直播公会月详情列表（日维度）
	GetAmuseGuildMonthIncomeList(ctx context.Context, in *GetGuildMonthIncomeListReq, opts ...grpc.CallOption) (*GetGuildMonthIncomeListResp, error)
	// 查询娱乐房收益明细
	SearchAmuseGuildDetail(ctx context.Context, in *SearchAmuseGuildDetailReq, opts ...grpc.CallOption) (*SearchAmuseGuildDetailResp, error)
	// 查询娱乐房房间日环比数据
	GetAmuseRoomDayQoqInfo(ctx context.Context, in *GetAmuseRoomDayQoqInfoReq, opts ...grpc.CallOption) (*GetAmuseRoomDayQoqInfoRsp, error)
	// 获取多人互动额外收益详情（按月）
	GetAmuseExtraDetail(ctx context.Context, in *GetAmuseExtraDetailReq, opts ...grpc.CallOption) (*GetAmuseExtraDetailResp, error)
	// 语音房佣金接口
	// 获取会长服务号语音直播收益数据详情（会长服务号首页加载接口）
	GetYuyinIncomeDetail(ctx context.Context, in *GetYuyinIncomeDetailReq, opts ...grpc.CallOption) (*GetYuyinIncomeDetailRsp, error)
	// 获取语音直播该周收益情况
	GetYuyinWeekIncome(ctx context.Context, in *GetYuyinWeekIncomeReq, opts ...grpc.CallOption) (*GetYuyinWeekIncomeRsp, error)
	// 获取语音直播按月份额外收益
	GetGuildYuyinExtraIncome(ctx context.Context, in *GetGuildYuyinExtraIncomeReq, opts ...grpc.CallOption) (*GetGuildYuyinExtraIncomeRsp, error)
	// 获取语音直播工会任务列表
	GetGuildYuyinTaskList(ctx context.Context, in *GetGuildYuyinTaskListReq, opts ...grpc.CallOption) (*GetGuildYuyinTaskListRsp, error)
	// 获取语音直播公会日收益详情列表（主播维度）
	GetYuyinGuildDayIncomeList(ctx context.Context, in *GetGuildDayIncomeListReq, opts ...grpc.CallOption) (*GetYuyinGuildDayIncomeListResp, error)
	// 获取语音直播公会月详情列表（日维度）
	GetYuyinGuildMonthIncomeList(ctx context.Context, in *GetGuildMonthIncomeListReq, opts ...grpc.CallOption) (*GetGuildMonthIncomeListResp, error)
	// 查询语音直播收益明细
	SearchYuyinGuildDetail(ctx context.Context, in *SearchYuyinGuildDetailReq, opts ...grpc.CallOption) (*SearchYuyinGuildDetailResp, error)
	// 互动游戏佣金接口
	GetInteractGameIncomeDetail(ctx context.Context, in *GetInteractGameIncomeDetailReq, opts ...grpc.CallOption) (*GetInteractGameIncomeDetailRsp, error)
	// 获取互动游戏日收益详情列表（主播维度）
	GetInteractGameDayIncomeList(ctx context.Context, in *GetGuildDayIncomeListReq, opts ...grpc.CallOption) (*GetInteractGameDayIncomeListResp, error)
	// 获取互动游戏月详情列表（日维度）
	GetInteractGameMonthIncomeList(ctx context.Context, in *GetGuildMonthIncomeListReq, opts ...grpc.CallOption) (*GetInteractGameMonthIncomeListResp, error)
	// 获取互动游戏该周收益
	GetInteractGameWeekIncome(ctx context.Context, in *GetInteractGameWeekIncomeReq, opts ...grpc.CallOption) (*GetInteractGameWeekIncomeRsp, error)
	// 电竞佣金接口
	GetESportIncomeDetail(ctx context.Context, in *GetESportIncomeDetailReq, opts ...grpc.CallOption) (*GetESportIncomeDetailRsp, error)
	// 获取电竞日收益详情列表（主播维度）
	GetESportDayIncomeList(ctx context.Context, in *GetGuildDayIncomeListReq, opts ...grpc.CallOption) (*GetESportDayIncomeListResp, error)
	// 获取电竞月详情列表（日维度）
	GetESportMonthIncomeList(ctx context.Context, in *GetGuildMonthIncomeListReq, opts ...grpc.CallOption) (*GetESportMonthIncomeListResp, error)
	// 获取电竞该周收益
	GetESportWeekIncome(ctx context.Context, in *GetESportWeekIncomeReq, opts ...grpc.CallOption) (*GetESportWeekIncomeRsp, error)
	// 获取电竞指导月收益列表
	GetESportCoachMonthIncomeList(ctx context.Context, in *GetESportCoachMonthIncomeListReq, opts ...grpc.CallOption) (*GetESportCoachMonthIncomeListResp, error)
	// 获取电竞指导指定时间段日收益列表
	GetESportCoachDayIncomeList(ctx context.Context, in *GetESportCoachDayIncomeListReq, opts ...grpc.CallOption) (*GetESportCoachDayIncomeListResp, error)
	// 获取指定时间段内产生流水的娱乐房公会IDs
	GetAmuseGuildIdsByRange(ctx context.Context, in *GetAmuseGuildIdsByRangeReq, opts ...grpc.CallOption) (*GetAmuseGuildIdsByRangeResp, error)
	// 多人互动额外奖励结算报表（旧蓝色后台迁移）
	GetAmuseExtraIncomeSettleList(ctx context.Context, in *GetAmuseExtraIncomeSettleListReq, opts ...grpc.CallOption) (*GetAmuseExtraIncomeSettleListResp, error)
	// 获取结算公会名单（仅多人互动额外奖励）
	GetAmuseSettleGuildList(ctx context.Context, in *GetAmuseSettleGuildListReq, opts ...grpc.CallOption) (*GetAmuseSettleGuildListResp, error)
	// 获取结算房间名单
	GetAmuseSettleChannelList(ctx context.Context, in *GetAmuseSettleChannelListReq, opts ...grpc.CallOption) (*GetAmuseSettleChannelListResp, error)
	// 设置公会结算状态
	SetAmuseSettleGuild(ctx context.Context, in *SetAmuseSettleGuildReq, opts ...grpc.CallOption) (*SetAmuseSettleGuildResp, error)
	// 设置房间结算状态
	SetAmuseSettleChannel(ctx context.Context, in *SetAmuseSettleChannelReq, opts ...grpc.CallOption) (*SetAmuseSettleChannelResp, error)
	// 获取公会互动游戏历史权限
	GetGuildInteractGamePer(ctx context.Context, in *GetGuildInteractGamePerReq, opts ...grpc.CallOption) (*GetGuildInteractGamePerResp, error)
	// 获取互动游戏额外奖励
	GetInteractGameExtraIncome(ctx context.Context, in *GetInteractGameExtraIncomeReq, opts ...grpc.CallOption) (*GetInteractGameExtraIncomeResp, error)
	// Deprecated 废弃 主动结算语音会长佣金
	SettlementYuyinGold(ctx context.Context, in *SettlementYuyinGoldReq, opts ...grpc.CallOption) (*SettlementYuyinGoldResp, error)
	// Deprecated 废弃
	GenYuyinExtraReport(ctx context.Context, in *GenYuyinExtraReportReq, opts ...grpc.CallOption) (*GenYuyinExtraReportResp, error)
}

type goldCommissionClient struct {
	cc *grpc.ClientConn
}

func NewGoldCommissionClient(cc *grpc.ClientConn) GoldCommissionClient {
	return &goldCommissionClient{cc}
}

func (c *goldCommissionClient) GetYuyinGoldPresentOrderCount(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.CountResp, error) {
	out := new(reconcile_v2.CountResp)
	err := c.cc.Invoke(ctx, "/gold_commission.GoldCommission/GetYuyinGoldPresentOrderCount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *goldCommissionClient) GetYuyinGoldPresentOrderList(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.OrderIdsResp, error) {
	out := new(reconcile_v2.OrderIdsResp)
	err := c.cc.Invoke(ctx, "/gold_commission.GoldCommission/GetYuyinGoldPresentOrderList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *goldCommissionClient) ReplaceYuyinGoldPresentOrder(ctx context.Context, in *reconcile_v2.ReplaceOrderReq, opts ...grpc.CallOption) (*reconcile_v2.EmptyResp, error) {
	out := new(reconcile_v2.EmptyResp)
	err := c.cc.Invoke(ctx, "/gold_commission.GoldCommission/ReplaceYuyinGoldPresentOrder", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *goldCommissionClient) GetYuyinGoldKnightOrderCount(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.CountResp, error) {
	out := new(reconcile_v2.CountResp)
	err := c.cc.Invoke(ctx, "/gold_commission.GoldCommission/GetYuyinGoldKnightOrderCount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *goldCommissionClient) GetYuyinGoldKnightOrderList(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.OrderIdsResp, error) {
	out := new(reconcile_v2.OrderIdsResp)
	err := c.cc.Invoke(ctx, "/gold_commission.GoldCommission/GetYuyinGoldKnightOrderList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *goldCommissionClient) ReplaceYuyinGoldKnightOrder(ctx context.Context, in *reconcile_v2.ReplaceOrderReq, opts ...grpc.CallOption) (*reconcile_v2.EmptyResp, error) {
	out := new(reconcile_v2.EmptyResp)
	err := c.cc.Invoke(ctx, "/gold_commission.GoldCommission/ReplaceYuyinGoldKnightOrder", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *goldCommissionClient) GetAmuseGoldPresentOrderCount(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.CountResp, error) {
	out := new(reconcile_v2.CountResp)
	err := c.cc.Invoke(ctx, "/gold_commission.GoldCommission/GetAmuseGoldPresentOrderCount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *goldCommissionClient) GetAmuseGoldPresentOrderList(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.OrderIdsResp, error) {
	out := new(reconcile_v2.OrderIdsResp)
	err := c.cc.Invoke(ctx, "/gold_commission.GoldCommission/GetAmuseGoldPresentOrderList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *goldCommissionClient) ReplaceAmuseGoldPresentOrder(ctx context.Context, in *reconcile_v2.ReplaceOrderReq, opts ...grpc.CallOption) (*reconcile_v2.EmptyResp, error) {
	out := new(reconcile_v2.EmptyResp)
	err := c.cc.Invoke(ctx, "/gold_commission.GoldCommission/ReplaceAmuseGoldPresentOrder", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *goldCommissionClient) GetAmuseGoldWerewolfOrderCount(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.CountResp, error) {
	out := new(reconcile_v2.CountResp)
	err := c.cc.Invoke(ctx, "/gold_commission.GoldCommission/GetAmuseGoldWerewolfOrderCount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *goldCommissionClient) GetAmuseGoldWerewolfOrderList(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.OrderIdsResp, error) {
	out := new(reconcile_v2.OrderIdsResp)
	err := c.cc.Invoke(ctx, "/gold_commission.GoldCommission/GetAmuseGoldWerewolfOrderList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *goldCommissionClient) ReplaceAmuseGoldWerewolfOrder(ctx context.Context, in *reconcile_v2.ReplaceOrderReq, opts ...grpc.CallOption) (*reconcile_v2.EmptyResp, error) {
	out := new(reconcile_v2.EmptyResp)
	err := c.cc.Invoke(ctx, "/gold_commission.GoldCommission/ReplaceAmuseGoldWerewolfOrder", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *goldCommissionClient) GetInteractGameGoldPresentOrderCount(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.CountResp, error) {
	out := new(reconcile_v2.CountResp)
	err := c.cc.Invoke(ctx, "/gold_commission.GoldCommission/GetInteractGameGoldPresentOrderCount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *goldCommissionClient) GetInteractGameGoldPresentOrderList(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.OrderIdsResp, error) {
	out := new(reconcile_v2.OrderIdsResp)
	err := c.cc.Invoke(ctx, "/gold_commission.GoldCommission/GetInteractGameGoldPresentOrderList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *goldCommissionClient) ReplaceInteractGameGoldPresentOrder(ctx context.Context, in *reconcile_v2.ReplaceOrderReq, opts ...grpc.CallOption) (*reconcile_v2.EmptyResp, error) {
	out := new(reconcile_v2.EmptyResp)
	err := c.cc.Invoke(ctx, "/gold_commission.GoldCommission/ReplaceInteractGameGoldPresentOrder", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *goldCommissionClient) GetESportGoldOrderCount(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.CountResp, error) {
	out := new(reconcile_v2.CountResp)
	err := c.cc.Invoke(ctx, "/gold_commission.GoldCommission/GetESportGoldOrderCount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *goldCommissionClient) GetESportGoldOrderList(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.OrderIdsResp, error) {
	out := new(reconcile_v2.OrderIdsResp)
	err := c.cc.Invoke(ctx, "/gold_commission.GoldCommission/GetESportGoldOrderList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *goldCommissionClient) ReplaceESportGoldOrder(ctx context.Context, in *reconcile_v2.ReplaceOrderReq, opts ...grpc.CallOption) (*reconcile_v2.EmptyResp, error) {
	out := new(reconcile_v2.EmptyResp)
	err := c.cc.Invoke(ctx, "/gold_commission.GoldCommission/ReplaceESportGoldOrder", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *goldCommissionClient) GetGuildsYuyinAnchorStat(ctx context.Context, in *GetGuildsYuyinAnchorStatReq, opts ...grpc.CallOption) (*GetGuildsYuyinAnchorStatResp, error) {
	out := new(GetGuildsYuyinAnchorStatResp)
	err := c.cc.Invoke(ctx, "/gold_commission.GoldCommission/GetGuildsYuyinAnchorStat", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *goldCommissionClient) GetGuildsAmuseChannelStat(ctx context.Context, in *GetGuildsAmuseChannelStatReq, opts ...grpc.CallOption) (*GetGuildsAmuseChannelStatResp, error) {
	out := new(GetGuildsAmuseChannelStatResp)
	err := c.cc.Invoke(ctx, "/gold_commission.GoldCommission/GetGuildsAmuseChannelStat", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *goldCommissionClient) GetGuildsInteractGameAnchorStat(ctx context.Context, in *GetGuildsInteractGameAnchorStatReq, opts ...grpc.CallOption) (*GetGuildsInteractGameAnchorStatResp, error) {
	out := new(GetGuildsInteractGameAnchorStatResp)
	err := c.cc.Invoke(ctx, "/gold_commission.GoldCommission/GetGuildsInteractGameAnchorStat", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *goldCommissionClient) GetGuildsESportCoachStat(ctx context.Context, in *GetGuildsESportCoachStatReq, opts ...grpc.CallOption) (*GetGuildsESportCoachStatResp, error) {
	out := new(GetGuildsESportCoachStatResp)
	err := c.cc.Invoke(ctx, "/gold_commission.GoldCommission/GetGuildsESportCoachStat", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *goldCommissionClient) GetGuildsUnSettlementSummary(ctx context.Context, in *GetGuildsUnSettlementSummaryReq, opts ...grpc.CallOption) (*GetGuildsUnSettlementSummaryRsp, error) {
	out := new(GetGuildsUnSettlementSummaryRsp)
	err := c.cc.Invoke(ctx, "/gold_commission.GoldCommission/GetGuildsUnSettlementSummary", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *goldCommissionClient) AmuseGuildChannelIncomeList(ctx context.Context, in *AmuseGuildChannelIncomeListReq, opts ...grpc.CallOption) (*AmuseGuildChannelIncomeListResp, error) {
	out := new(AmuseGuildChannelIncomeListResp)
	err := c.cc.Invoke(ctx, "/gold_commission.GoldCommission/AmuseGuildChannelIncomeList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *goldCommissionClient) GetConsumeRank(ctx context.Context, in *GetConsumeRankReq, opts ...grpc.CallOption) (*GetConsumeRankRsp, error) {
	out := new(GetConsumeRankRsp)
	err := c.cc.Invoke(ctx, "/gold_commission.GoldCommission/GetConsumeRank", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *goldCommissionClient) GetIncomeTrendList(ctx context.Context, in *GetIncomeTrendListReq, opts ...grpc.CallOption) (*GetIncomeTrendListRsp, error) {
	out := new(GetIncomeTrendListRsp)
	err := c.cc.Invoke(ctx, "/gold_commission.GoldCommission/GetIncomeTrendList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *goldCommissionClient) GetGuildDayQoq(ctx context.Context, in *GetGuildDayQoqReq, opts ...grpc.CallOption) (*GetGuildDayQoqResp, error) {
	out := new(GetGuildDayQoqResp)
	err := c.cc.Invoke(ctx, "/gold_commission.GoldCommission/GetGuildDayQoq", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *goldCommissionClient) GetGuildMonthMemberList(ctx context.Context, in *GetGuildMonthMemberListReq, opts ...grpc.CallOption) (*GetGuildMonthMemberListResp, error) {
	out := new(GetGuildMonthMemberListResp)
	err := c.cc.Invoke(ctx, "/gold_commission.GoldCommission/GetGuildMonthMemberList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *goldCommissionClient) GetDayIncome(ctx context.Context, in *GetDayIncomeReq, opts ...grpc.CallOption) (*GetDayIncomeRsp, error) {
	out := new(GetDayIncomeRsp)
	err := c.cc.Invoke(ctx, "/gold_commission.GoldCommission/GetDayIncome", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *goldCommissionClient) GetMonthIncome(ctx context.Context, in *GetMonthIncomeReq, opts ...grpc.CallOption) (*GetMonthIncomeRsp, error) {
	out := new(GetMonthIncomeRsp)
	err := c.cc.Invoke(ctx, "/gold_commission.GoldCommission/GetMonthIncome", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *goldCommissionClient) GetAmuseChannelDetail(ctx context.Context, in *GetAmuseChannelDetailReq, opts ...grpc.CallOption) (*GetAmuseChannelDetailRsp, error) {
	out := new(GetAmuseChannelDetailRsp)
	err := c.cc.Invoke(ctx, "/gold_commission.GoldCommission/GetAmuseChannelDetail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *goldCommissionClient) GetAmuseIncomeDetail(ctx context.Context, in *GetAmuseIncomeDetailReq, opts ...grpc.CallOption) (*GetAmuseIncomeDetailRsp, error) {
	out := new(GetAmuseIncomeDetailRsp)
	err := c.cc.Invoke(ctx, "/gold_commission.GoldCommission/GetAmuseIncomeDetail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *goldCommissionClient) GetAmuseGuildRoomIncomeList(ctx context.Context, in *GetAmuseGuildRoomIncomeListReq, opts ...grpc.CallOption) (*GetAmuseGuildRoomIncomeListRsp, error) {
	out := new(GetAmuseGuildRoomIncomeListRsp)
	err := c.cc.Invoke(ctx, "/gold_commission.GoldCommission/GetAmuseGuildRoomIncomeList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *goldCommissionClient) GetAmuseGuildDayIncomeList(ctx context.Context, in *GetGuildDayIncomeListReq, opts ...grpc.CallOption) (*GetAmuseGuildDayIncomeListResp, error) {
	out := new(GetAmuseGuildDayIncomeListResp)
	err := c.cc.Invoke(ctx, "/gold_commission.GoldCommission/GetAmuseGuildDayIncomeList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *goldCommissionClient) GetAmuseGuildMonthIncomeList(ctx context.Context, in *GetGuildMonthIncomeListReq, opts ...grpc.CallOption) (*GetGuildMonthIncomeListResp, error) {
	out := new(GetGuildMonthIncomeListResp)
	err := c.cc.Invoke(ctx, "/gold_commission.GoldCommission/GetAmuseGuildMonthIncomeList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *goldCommissionClient) SearchAmuseGuildDetail(ctx context.Context, in *SearchAmuseGuildDetailReq, opts ...grpc.CallOption) (*SearchAmuseGuildDetailResp, error) {
	out := new(SearchAmuseGuildDetailResp)
	err := c.cc.Invoke(ctx, "/gold_commission.GoldCommission/SearchAmuseGuildDetail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *goldCommissionClient) GetAmuseRoomDayQoqInfo(ctx context.Context, in *GetAmuseRoomDayQoqInfoReq, opts ...grpc.CallOption) (*GetAmuseRoomDayQoqInfoRsp, error) {
	out := new(GetAmuseRoomDayQoqInfoRsp)
	err := c.cc.Invoke(ctx, "/gold_commission.GoldCommission/GetAmuseRoomDayQoqInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *goldCommissionClient) GetAmuseExtraDetail(ctx context.Context, in *GetAmuseExtraDetailReq, opts ...grpc.CallOption) (*GetAmuseExtraDetailResp, error) {
	out := new(GetAmuseExtraDetailResp)
	err := c.cc.Invoke(ctx, "/gold_commission.GoldCommission/GetAmuseExtraDetail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *goldCommissionClient) GetYuyinIncomeDetail(ctx context.Context, in *GetYuyinIncomeDetailReq, opts ...grpc.CallOption) (*GetYuyinIncomeDetailRsp, error) {
	out := new(GetYuyinIncomeDetailRsp)
	err := c.cc.Invoke(ctx, "/gold_commission.GoldCommission/GetYuyinIncomeDetail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *goldCommissionClient) GetYuyinWeekIncome(ctx context.Context, in *GetYuyinWeekIncomeReq, opts ...grpc.CallOption) (*GetYuyinWeekIncomeRsp, error) {
	out := new(GetYuyinWeekIncomeRsp)
	err := c.cc.Invoke(ctx, "/gold_commission.GoldCommission/GetYuyinWeekIncome", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *goldCommissionClient) GetGuildYuyinExtraIncome(ctx context.Context, in *GetGuildYuyinExtraIncomeReq, opts ...grpc.CallOption) (*GetGuildYuyinExtraIncomeRsp, error) {
	out := new(GetGuildYuyinExtraIncomeRsp)
	err := c.cc.Invoke(ctx, "/gold_commission.GoldCommission/GetGuildYuyinExtraIncome", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *goldCommissionClient) GetGuildYuyinTaskList(ctx context.Context, in *GetGuildYuyinTaskListReq, opts ...grpc.CallOption) (*GetGuildYuyinTaskListRsp, error) {
	out := new(GetGuildYuyinTaskListRsp)
	err := c.cc.Invoke(ctx, "/gold_commission.GoldCommission/GetGuildYuyinTaskList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *goldCommissionClient) GetYuyinGuildDayIncomeList(ctx context.Context, in *GetGuildDayIncomeListReq, opts ...grpc.CallOption) (*GetYuyinGuildDayIncomeListResp, error) {
	out := new(GetYuyinGuildDayIncomeListResp)
	err := c.cc.Invoke(ctx, "/gold_commission.GoldCommission/GetYuyinGuildDayIncomeList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *goldCommissionClient) GetYuyinGuildMonthIncomeList(ctx context.Context, in *GetGuildMonthIncomeListReq, opts ...grpc.CallOption) (*GetGuildMonthIncomeListResp, error) {
	out := new(GetGuildMonthIncomeListResp)
	err := c.cc.Invoke(ctx, "/gold_commission.GoldCommission/GetYuyinGuildMonthIncomeList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *goldCommissionClient) SearchYuyinGuildDetail(ctx context.Context, in *SearchYuyinGuildDetailReq, opts ...grpc.CallOption) (*SearchYuyinGuildDetailResp, error) {
	out := new(SearchYuyinGuildDetailResp)
	err := c.cc.Invoke(ctx, "/gold_commission.GoldCommission/SearchYuyinGuildDetail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *goldCommissionClient) GetInteractGameIncomeDetail(ctx context.Context, in *GetInteractGameIncomeDetailReq, opts ...grpc.CallOption) (*GetInteractGameIncomeDetailRsp, error) {
	out := new(GetInteractGameIncomeDetailRsp)
	err := c.cc.Invoke(ctx, "/gold_commission.GoldCommission/GetInteractGameIncomeDetail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *goldCommissionClient) GetInteractGameDayIncomeList(ctx context.Context, in *GetGuildDayIncomeListReq, opts ...grpc.CallOption) (*GetInteractGameDayIncomeListResp, error) {
	out := new(GetInteractGameDayIncomeListResp)
	err := c.cc.Invoke(ctx, "/gold_commission.GoldCommission/GetInteractGameDayIncomeList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *goldCommissionClient) GetInteractGameMonthIncomeList(ctx context.Context, in *GetGuildMonthIncomeListReq, opts ...grpc.CallOption) (*GetInteractGameMonthIncomeListResp, error) {
	out := new(GetInteractGameMonthIncomeListResp)
	err := c.cc.Invoke(ctx, "/gold_commission.GoldCommission/GetInteractGameMonthIncomeList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *goldCommissionClient) GetInteractGameWeekIncome(ctx context.Context, in *GetInteractGameWeekIncomeReq, opts ...grpc.CallOption) (*GetInteractGameWeekIncomeRsp, error) {
	out := new(GetInteractGameWeekIncomeRsp)
	err := c.cc.Invoke(ctx, "/gold_commission.GoldCommission/GetInteractGameWeekIncome", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *goldCommissionClient) GetESportIncomeDetail(ctx context.Context, in *GetESportIncomeDetailReq, opts ...grpc.CallOption) (*GetESportIncomeDetailRsp, error) {
	out := new(GetESportIncomeDetailRsp)
	err := c.cc.Invoke(ctx, "/gold_commission.GoldCommission/GetESportIncomeDetail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *goldCommissionClient) GetESportDayIncomeList(ctx context.Context, in *GetGuildDayIncomeListReq, opts ...grpc.CallOption) (*GetESportDayIncomeListResp, error) {
	out := new(GetESportDayIncomeListResp)
	err := c.cc.Invoke(ctx, "/gold_commission.GoldCommission/GetESportDayIncomeList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *goldCommissionClient) GetESportMonthIncomeList(ctx context.Context, in *GetGuildMonthIncomeListReq, opts ...grpc.CallOption) (*GetESportMonthIncomeListResp, error) {
	out := new(GetESportMonthIncomeListResp)
	err := c.cc.Invoke(ctx, "/gold_commission.GoldCommission/GetESportMonthIncomeList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *goldCommissionClient) GetESportWeekIncome(ctx context.Context, in *GetESportWeekIncomeReq, opts ...grpc.CallOption) (*GetESportWeekIncomeRsp, error) {
	out := new(GetESportWeekIncomeRsp)
	err := c.cc.Invoke(ctx, "/gold_commission.GoldCommission/GetESportWeekIncome", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *goldCommissionClient) GetESportCoachMonthIncomeList(ctx context.Context, in *GetESportCoachMonthIncomeListReq, opts ...grpc.CallOption) (*GetESportCoachMonthIncomeListResp, error) {
	out := new(GetESportCoachMonthIncomeListResp)
	err := c.cc.Invoke(ctx, "/gold_commission.GoldCommission/GetESportCoachMonthIncomeList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *goldCommissionClient) GetESportCoachDayIncomeList(ctx context.Context, in *GetESportCoachDayIncomeListReq, opts ...grpc.CallOption) (*GetESportCoachDayIncomeListResp, error) {
	out := new(GetESportCoachDayIncomeListResp)
	err := c.cc.Invoke(ctx, "/gold_commission.GoldCommission/GetESportCoachDayIncomeList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *goldCommissionClient) GetAmuseGuildIdsByRange(ctx context.Context, in *GetAmuseGuildIdsByRangeReq, opts ...grpc.CallOption) (*GetAmuseGuildIdsByRangeResp, error) {
	out := new(GetAmuseGuildIdsByRangeResp)
	err := c.cc.Invoke(ctx, "/gold_commission.GoldCommission/GetAmuseGuildIdsByRange", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *goldCommissionClient) GetAmuseExtraIncomeSettleList(ctx context.Context, in *GetAmuseExtraIncomeSettleListReq, opts ...grpc.CallOption) (*GetAmuseExtraIncomeSettleListResp, error) {
	out := new(GetAmuseExtraIncomeSettleListResp)
	err := c.cc.Invoke(ctx, "/gold_commission.GoldCommission/GetAmuseExtraIncomeSettleList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *goldCommissionClient) GetAmuseSettleGuildList(ctx context.Context, in *GetAmuseSettleGuildListReq, opts ...grpc.CallOption) (*GetAmuseSettleGuildListResp, error) {
	out := new(GetAmuseSettleGuildListResp)
	err := c.cc.Invoke(ctx, "/gold_commission.GoldCommission/GetAmuseSettleGuildList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *goldCommissionClient) GetAmuseSettleChannelList(ctx context.Context, in *GetAmuseSettleChannelListReq, opts ...grpc.CallOption) (*GetAmuseSettleChannelListResp, error) {
	out := new(GetAmuseSettleChannelListResp)
	err := c.cc.Invoke(ctx, "/gold_commission.GoldCommission/GetAmuseSettleChannelList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *goldCommissionClient) SetAmuseSettleGuild(ctx context.Context, in *SetAmuseSettleGuildReq, opts ...grpc.CallOption) (*SetAmuseSettleGuildResp, error) {
	out := new(SetAmuseSettleGuildResp)
	err := c.cc.Invoke(ctx, "/gold_commission.GoldCommission/SetAmuseSettleGuild", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *goldCommissionClient) SetAmuseSettleChannel(ctx context.Context, in *SetAmuseSettleChannelReq, opts ...grpc.CallOption) (*SetAmuseSettleChannelResp, error) {
	out := new(SetAmuseSettleChannelResp)
	err := c.cc.Invoke(ctx, "/gold_commission.GoldCommission/SetAmuseSettleChannel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *goldCommissionClient) GetGuildInteractGamePer(ctx context.Context, in *GetGuildInteractGamePerReq, opts ...grpc.CallOption) (*GetGuildInteractGamePerResp, error) {
	out := new(GetGuildInteractGamePerResp)
	err := c.cc.Invoke(ctx, "/gold_commission.GoldCommission/GetGuildInteractGamePer", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *goldCommissionClient) GetInteractGameExtraIncome(ctx context.Context, in *GetInteractGameExtraIncomeReq, opts ...grpc.CallOption) (*GetInteractGameExtraIncomeResp, error) {
	out := new(GetInteractGameExtraIncomeResp)
	err := c.cc.Invoke(ctx, "/gold_commission.GoldCommission/GetInteractGameExtraIncome", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *goldCommissionClient) SettlementYuyinGold(ctx context.Context, in *SettlementYuyinGoldReq, opts ...grpc.CallOption) (*SettlementYuyinGoldResp, error) {
	out := new(SettlementYuyinGoldResp)
	err := c.cc.Invoke(ctx, "/gold_commission.GoldCommission/SettlementYuyinGold", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *goldCommissionClient) GenYuyinExtraReport(ctx context.Context, in *GenYuyinExtraReportReq, opts ...grpc.CallOption) (*GenYuyinExtraReportResp, error) {
	out := new(GenYuyinExtraReportResp)
	err := c.cc.Invoke(ctx, "/gold_commission.GoldCommission/GenYuyinExtraReport", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// GoldCommissionServer is the server API for GoldCommission service.
type GoldCommissionServer interface {
	// 对账 语音礼物佣金补单
	GetYuyinGoldPresentOrderCount(context.Context, *reconcile_v2.TimeRangeReq) (*reconcile_v2.CountResp, error)
	GetYuyinGoldPresentOrderList(context.Context, *reconcile_v2.TimeRangeReq) (*reconcile_v2.OrderIdsResp, error)
	ReplaceYuyinGoldPresentOrder(context.Context, *reconcile_v2.ReplaceOrderReq) (*reconcile_v2.EmptyResp, error)
	// 对账 语音骑士佣金补单
	GetYuyinGoldKnightOrderCount(context.Context, *reconcile_v2.TimeRangeReq) (*reconcile_v2.CountResp, error)
	GetYuyinGoldKnightOrderList(context.Context, *reconcile_v2.TimeRangeReq) (*reconcile_v2.OrderIdsResp, error)
	ReplaceYuyinGoldKnightOrder(context.Context, *reconcile_v2.ReplaceOrderReq) (*reconcile_v2.EmptyResp, error)
	// 对账 娱乐房礼物佣金补单
	GetAmuseGoldPresentOrderCount(context.Context, *reconcile_v2.TimeRangeReq) (*reconcile_v2.CountResp, error)
	GetAmuseGoldPresentOrderList(context.Context, *reconcile_v2.TimeRangeReq) (*reconcile_v2.OrderIdsResp, error)
	ReplaceAmuseGoldPresentOrder(context.Context, *reconcile_v2.ReplaceOrderReq) (*reconcile_v2.EmptyResp, error)
	// 对账 娱乐房狼人杀佣金补单
	GetAmuseGoldWerewolfOrderCount(context.Context, *reconcile_v2.TimeRangeReq) (*reconcile_v2.CountResp, error)
	GetAmuseGoldWerewolfOrderList(context.Context, *reconcile_v2.TimeRangeReq) (*reconcile_v2.OrderIdsResp, error)
	ReplaceAmuseGoldWerewolfOrder(context.Context, *reconcile_v2.ReplaceOrderReq) (*reconcile_v2.EmptyResp, error)
	// 对账 互动游戏佣金补单
	GetInteractGameGoldPresentOrderCount(context.Context, *reconcile_v2.TimeRangeReq) (*reconcile_v2.CountResp, error)
	GetInteractGameGoldPresentOrderList(context.Context, *reconcile_v2.TimeRangeReq) (*reconcile_v2.OrderIdsResp, error)
	ReplaceInteractGameGoldPresentOrder(context.Context, *reconcile_v2.ReplaceOrderReq) (*reconcile_v2.EmptyResp, error)
	// 对账 电竞佣金补单
	GetESportGoldOrderCount(context.Context, *reconcile_v2.TimeRangeReq) (*reconcile_v2.CountResp, error)
	GetESportGoldOrderList(context.Context, *reconcile_v2.TimeRangeReq) (*reconcile_v2.OrderIdsResp, error)
	ReplaceESportGoldOrder(context.Context, *reconcile_v2.ReplaceOrderReq) (*reconcile_v2.EmptyResp, error)
	// 获取对公会长旗下多个公会下的语音主播/房间流水统计
	GetGuildsYuyinAnchorStat(context.Context, *GetGuildsYuyinAnchorStatReq) (*GetGuildsYuyinAnchorStatResp, error)
	GetGuildsAmuseChannelStat(context.Context, *GetGuildsAmuseChannelStatReq) (*GetGuildsAmuseChannelStatResp, error)
	GetGuildsInteractGameAnchorStat(context.Context, *GetGuildsInteractGameAnchorStatReq) (*GetGuildsInteractGameAnchorStatResp, error)
	GetGuildsESportCoachStat(context.Context, *GetGuildsESportCoachStatReq) (*GetGuildsESportCoachStatResp, error)
	// 获取公会下未结算总流水
	GetGuildsUnSettlementSummary(context.Context, *GetGuildsUnSettlementSummaryReq) (*GetGuildsUnSettlementSummaryRsp, error)
	// 娱乐房收入情况查询
	AmuseGuildChannelIncomeList(context.Context, *AmuseGuildChannelIncomeListReq) (*AmuseGuildChannelIncomeListResp, error)
	// 房间类型合并接口
	// 获取消费排行榜
	GetConsumeRank(context.Context, *GetConsumeRankReq) (*GetConsumeRankRsp, error)
	// 根据截止时间获取月收益日趋势列表
	GetIncomeTrendList(context.Context, *GetIncomeTrendListReq) (*GetIncomeTrendListRsp, error)
	// 获取日收益对比
	GetGuildDayQoq(context.Context, *GetGuildDayQoqReq) (*GetGuildDayQoqResp, error)
	// 获取公会月详情列表（消费成员或主播维度）
	GetGuildMonthMemberList(context.Context, *GetGuildMonthMemberListReq) (*GetGuildMonthMemberListResp, error)
	// 获取该日收益情况
	GetDayIncome(context.Context, *GetDayIncomeReq) (*GetDayIncomeRsp, error)
	// 获取该月收益情况
	GetMonthIncome(context.Context, *GetMonthIncomeReq) (*GetMonthIncomeRsp, error)
	// 娱乐房佣金接口
	// 获取娱乐房房间详情
	GetAmuseChannelDetail(context.Context, *GetAmuseChannelDetailReq) (*GetAmuseChannelDetailRsp, error)
	// 获取娱乐房收入情况
	GetAmuseIncomeDetail(context.Context, *GetAmuseIncomeDetailReq) (*GetAmuseIncomeDetailRsp, error)
	// 获取工会娱乐房房间日流水列表（房间纬度）
	GetAmuseGuildRoomIncomeList(context.Context, *GetAmuseGuildRoomIncomeListReq) (*GetAmuseGuildRoomIncomeListRsp, error)
	// 获取语音直播公会日收益详情列表（主播维度）
	GetAmuseGuildDayIncomeList(context.Context, *GetGuildDayIncomeListReq) (*GetAmuseGuildDayIncomeListResp, error)
	// 获取语音直播公会月详情列表（日维度）
	GetAmuseGuildMonthIncomeList(context.Context, *GetGuildMonthIncomeListReq) (*GetGuildMonthIncomeListResp, error)
	// 查询娱乐房收益明细
	SearchAmuseGuildDetail(context.Context, *SearchAmuseGuildDetailReq) (*SearchAmuseGuildDetailResp, error)
	// 查询娱乐房房间日环比数据
	GetAmuseRoomDayQoqInfo(context.Context, *GetAmuseRoomDayQoqInfoReq) (*GetAmuseRoomDayQoqInfoRsp, error)
	// 获取多人互动额外收益详情（按月）
	GetAmuseExtraDetail(context.Context, *GetAmuseExtraDetailReq) (*GetAmuseExtraDetailResp, error)
	// 语音房佣金接口
	// 获取会长服务号语音直播收益数据详情（会长服务号首页加载接口）
	GetYuyinIncomeDetail(context.Context, *GetYuyinIncomeDetailReq) (*GetYuyinIncomeDetailRsp, error)
	// 获取语音直播该周收益情况
	GetYuyinWeekIncome(context.Context, *GetYuyinWeekIncomeReq) (*GetYuyinWeekIncomeRsp, error)
	// 获取语音直播按月份额外收益
	GetGuildYuyinExtraIncome(context.Context, *GetGuildYuyinExtraIncomeReq) (*GetGuildYuyinExtraIncomeRsp, error)
	// 获取语音直播工会任务列表
	GetGuildYuyinTaskList(context.Context, *GetGuildYuyinTaskListReq) (*GetGuildYuyinTaskListRsp, error)
	// 获取语音直播公会日收益详情列表（主播维度）
	GetYuyinGuildDayIncomeList(context.Context, *GetGuildDayIncomeListReq) (*GetYuyinGuildDayIncomeListResp, error)
	// 获取语音直播公会月详情列表（日维度）
	GetYuyinGuildMonthIncomeList(context.Context, *GetGuildMonthIncomeListReq) (*GetGuildMonthIncomeListResp, error)
	// 查询语音直播收益明细
	SearchYuyinGuildDetail(context.Context, *SearchYuyinGuildDetailReq) (*SearchYuyinGuildDetailResp, error)
	// 互动游戏佣金接口
	GetInteractGameIncomeDetail(context.Context, *GetInteractGameIncomeDetailReq) (*GetInteractGameIncomeDetailRsp, error)
	// 获取互动游戏日收益详情列表（主播维度）
	GetInteractGameDayIncomeList(context.Context, *GetGuildDayIncomeListReq) (*GetInteractGameDayIncomeListResp, error)
	// 获取互动游戏月详情列表（日维度）
	GetInteractGameMonthIncomeList(context.Context, *GetGuildMonthIncomeListReq) (*GetInteractGameMonthIncomeListResp, error)
	// 获取互动游戏该周收益
	GetInteractGameWeekIncome(context.Context, *GetInteractGameWeekIncomeReq) (*GetInteractGameWeekIncomeRsp, error)
	// 电竞佣金接口
	GetESportIncomeDetail(context.Context, *GetESportIncomeDetailReq) (*GetESportIncomeDetailRsp, error)
	// 获取电竞日收益详情列表（主播维度）
	GetESportDayIncomeList(context.Context, *GetGuildDayIncomeListReq) (*GetESportDayIncomeListResp, error)
	// 获取电竞月详情列表（日维度）
	GetESportMonthIncomeList(context.Context, *GetGuildMonthIncomeListReq) (*GetESportMonthIncomeListResp, error)
	// 获取电竞该周收益
	GetESportWeekIncome(context.Context, *GetESportWeekIncomeReq) (*GetESportWeekIncomeRsp, error)
	// 获取电竞指导月收益列表
	GetESportCoachMonthIncomeList(context.Context, *GetESportCoachMonthIncomeListReq) (*GetESportCoachMonthIncomeListResp, error)
	// 获取电竞指导指定时间段日收益列表
	GetESportCoachDayIncomeList(context.Context, *GetESportCoachDayIncomeListReq) (*GetESportCoachDayIncomeListResp, error)
	// 获取指定时间段内产生流水的娱乐房公会IDs
	GetAmuseGuildIdsByRange(context.Context, *GetAmuseGuildIdsByRangeReq) (*GetAmuseGuildIdsByRangeResp, error)
	// 多人互动额外奖励结算报表（旧蓝色后台迁移）
	GetAmuseExtraIncomeSettleList(context.Context, *GetAmuseExtraIncomeSettleListReq) (*GetAmuseExtraIncomeSettleListResp, error)
	// 获取结算公会名单（仅多人互动额外奖励）
	GetAmuseSettleGuildList(context.Context, *GetAmuseSettleGuildListReq) (*GetAmuseSettleGuildListResp, error)
	// 获取结算房间名单
	GetAmuseSettleChannelList(context.Context, *GetAmuseSettleChannelListReq) (*GetAmuseSettleChannelListResp, error)
	// 设置公会结算状态
	SetAmuseSettleGuild(context.Context, *SetAmuseSettleGuildReq) (*SetAmuseSettleGuildResp, error)
	// 设置房间结算状态
	SetAmuseSettleChannel(context.Context, *SetAmuseSettleChannelReq) (*SetAmuseSettleChannelResp, error)
	// 获取公会互动游戏历史权限
	GetGuildInteractGamePer(context.Context, *GetGuildInteractGamePerReq) (*GetGuildInteractGamePerResp, error)
	// 获取互动游戏额外奖励
	GetInteractGameExtraIncome(context.Context, *GetInteractGameExtraIncomeReq) (*GetInteractGameExtraIncomeResp, error)
	// Deprecated 废弃 主动结算语音会长佣金
	SettlementYuyinGold(context.Context, *SettlementYuyinGoldReq) (*SettlementYuyinGoldResp, error)
	// Deprecated 废弃
	GenYuyinExtraReport(context.Context, *GenYuyinExtraReportReq) (*GenYuyinExtraReportResp, error)
}

func RegisterGoldCommissionServer(s *grpc.Server, srv GoldCommissionServer) {
	s.RegisterService(&_GoldCommission_serviceDesc, srv)
}

func _GoldCommission_GetYuyinGoldPresentOrderCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(reconcile_v2.TimeRangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GoldCommissionServer).GetYuyinGoldPresentOrderCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/gold_commission.GoldCommission/GetYuyinGoldPresentOrderCount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GoldCommissionServer).GetYuyinGoldPresentOrderCount(ctx, req.(*reconcile_v2.TimeRangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GoldCommission_GetYuyinGoldPresentOrderList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(reconcile_v2.TimeRangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GoldCommissionServer).GetYuyinGoldPresentOrderList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/gold_commission.GoldCommission/GetYuyinGoldPresentOrderList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GoldCommissionServer).GetYuyinGoldPresentOrderList(ctx, req.(*reconcile_v2.TimeRangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GoldCommission_ReplaceYuyinGoldPresentOrder_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(reconcile_v2.ReplaceOrderReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GoldCommissionServer).ReplaceYuyinGoldPresentOrder(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/gold_commission.GoldCommission/ReplaceYuyinGoldPresentOrder",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GoldCommissionServer).ReplaceYuyinGoldPresentOrder(ctx, req.(*reconcile_v2.ReplaceOrderReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GoldCommission_GetYuyinGoldKnightOrderCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(reconcile_v2.TimeRangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GoldCommissionServer).GetYuyinGoldKnightOrderCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/gold_commission.GoldCommission/GetYuyinGoldKnightOrderCount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GoldCommissionServer).GetYuyinGoldKnightOrderCount(ctx, req.(*reconcile_v2.TimeRangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GoldCommission_GetYuyinGoldKnightOrderList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(reconcile_v2.TimeRangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GoldCommissionServer).GetYuyinGoldKnightOrderList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/gold_commission.GoldCommission/GetYuyinGoldKnightOrderList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GoldCommissionServer).GetYuyinGoldKnightOrderList(ctx, req.(*reconcile_v2.TimeRangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GoldCommission_ReplaceYuyinGoldKnightOrder_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(reconcile_v2.ReplaceOrderReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GoldCommissionServer).ReplaceYuyinGoldKnightOrder(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/gold_commission.GoldCommission/ReplaceYuyinGoldKnightOrder",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GoldCommissionServer).ReplaceYuyinGoldKnightOrder(ctx, req.(*reconcile_v2.ReplaceOrderReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GoldCommission_GetAmuseGoldPresentOrderCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(reconcile_v2.TimeRangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GoldCommissionServer).GetAmuseGoldPresentOrderCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/gold_commission.GoldCommission/GetAmuseGoldPresentOrderCount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GoldCommissionServer).GetAmuseGoldPresentOrderCount(ctx, req.(*reconcile_v2.TimeRangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GoldCommission_GetAmuseGoldPresentOrderList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(reconcile_v2.TimeRangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GoldCommissionServer).GetAmuseGoldPresentOrderList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/gold_commission.GoldCommission/GetAmuseGoldPresentOrderList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GoldCommissionServer).GetAmuseGoldPresentOrderList(ctx, req.(*reconcile_v2.TimeRangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GoldCommission_ReplaceAmuseGoldPresentOrder_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(reconcile_v2.ReplaceOrderReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GoldCommissionServer).ReplaceAmuseGoldPresentOrder(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/gold_commission.GoldCommission/ReplaceAmuseGoldPresentOrder",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GoldCommissionServer).ReplaceAmuseGoldPresentOrder(ctx, req.(*reconcile_v2.ReplaceOrderReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GoldCommission_GetAmuseGoldWerewolfOrderCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(reconcile_v2.TimeRangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GoldCommissionServer).GetAmuseGoldWerewolfOrderCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/gold_commission.GoldCommission/GetAmuseGoldWerewolfOrderCount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GoldCommissionServer).GetAmuseGoldWerewolfOrderCount(ctx, req.(*reconcile_v2.TimeRangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GoldCommission_GetAmuseGoldWerewolfOrderList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(reconcile_v2.TimeRangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GoldCommissionServer).GetAmuseGoldWerewolfOrderList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/gold_commission.GoldCommission/GetAmuseGoldWerewolfOrderList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GoldCommissionServer).GetAmuseGoldWerewolfOrderList(ctx, req.(*reconcile_v2.TimeRangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GoldCommission_ReplaceAmuseGoldWerewolfOrder_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(reconcile_v2.ReplaceOrderReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GoldCommissionServer).ReplaceAmuseGoldWerewolfOrder(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/gold_commission.GoldCommission/ReplaceAmuseGoldWerewolfOrder",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GoldCommissionServer).ReplaceAmuseGoldWerewolfOrder(ctx, req.(*reconcile_v2.ReplaceOrderReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GoldCommission_GetInteractGameGoldPresentOrderCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(reconcile_v2.TimeRangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GoldCommissionServer).GetInteractGameGoldPresentOrderCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/gold_commission.GoldCommission/GetInteractGameGoldPresentOrderCount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GoldCommissionServer).GetInteractGameGoldPresentOrderCount(ctx, req.(*reconcile_v2.TimeRangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GoldCommission_GetInteractGameGoldPresentOrderList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(reconcile_v2.TimeRangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GoldCommissionServer).GetInteractGameGoldPresentOrderList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/gold_commission.GoldCommission/GetInteractGameGoldPresentOrderList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GoldCommissionServer).GetInteractGameGoldPresentOrderList(ctx, req.(*reconcile_v2.TimeRangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GoldCommission_ReplaceInteractGameGoldPresentOrder_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(reconcile_v2.ReplaceOrderReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GoldCommissionServer).ReplaceInteractGameGoldPresentOrder(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/gold_commission.GoldCommission/ReplaceInteractGameGoldPresentOrder",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GoldCommissionServer).ReplaceInteractGameGoldPresentOrder(ctx, req.(*reconcile_v2.ReplaceOrderReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GoldCommission_GetESportGoldOrderCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(reconcile_v2.TimeRangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GoldCommissionServer).GetESportGoldOrderCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/gold_commission.GoldCommission/GetESportGoldOrderCount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GoldCommissionServer).GetESportGoldOrderCount(ctx, req.(*reconcile_v2.TimeRangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GoldCommission_GetESportGoldOrderList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(reconcile_v2.TimeRangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GoldCommissionServer).GetESportGoldOrderList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/gold_commission.GoldCommission/GetESportGoldOrderList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GoldCommissionServer).GetESportGoldOrderList(ctx, req.(*reconcile_v2.TimeRangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GoldCommission_ReplaceESportGoldOrder_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(reconcile_v2.ReplaceOrderReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GoldCommissionServer).ReplaceESportGoldOrder(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/gold_commission.GoldCommission/ReplaceESportGoldOrder",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GoldCommissionServer).ReplaceESportGoldOrder(ctx, req.(*reconcile_v2.ReplaceOrderReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GoldCommission_GetGuildsYuyinAnchorStat_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGuildsYuyinAnchorStatReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GoldCommissionServer).GetGuildsYuyinAnchorStat(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/gold_commission.GoldCommission/GetGuildsYuyinAnchorStat",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GoldCommissionServer).GetGuildsYuyinAnchorStat(ctx, req.(*GetGuildsYuyinAnchorStatReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GoldCommission_GetGuildsAmuseChannelStat_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGuildsAmuseChannelStatReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GoldCommissionServer).GetGuildsAmuseChannelStat(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/gold_commission.GoldCommission/GetGuildsAmuseChannelStat",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GoldCommissionServer).GetGuildsAmuseChannelStat(ctx, req.(*GetGuildsAmuseChannelStatReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GoldCommission_GetGuildsInteractGameAnchorStat_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGuildsInteractGameAnchorStatReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GoldCommissionServer).GetGuildsInteractGameAnchorStat(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/gold_commission.GoldCommission/GetGuildsInteractGameAnchorStat",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GoldCommissionServer).GetGuildsInteractGameAnchorStat(ctx, req.(*GetGuildsInteractGameAnchorStatReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GoldCommission_GetGuildsESportCoachStat_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGuildsESportCoachStatReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GoldCommissionServer).GetGuildsESportCoachStat(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/gold_commission.GoldCommission/GetGuildsESportCoachStat",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GoldCommissionServer).GetGuildsESportCoachStat(ctx, req.(*GetGuildsESportCoachStatReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GoldCommission_GetGuildsUnSettlementSummary_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGuildsUnSettlementSummaryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GoldCommissionServer).GetGuildsUnSettlementSummary(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/gold_commission.GoldCommission/GetGuildsUnSettlementSummary",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GoldCommissionServer).GetGuildsUnSettlementSummary(ctx, req.(*GetGuildsUnSettlementSummaryReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GoldCommission_AmuseGuildChannelIncomeList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AmuseGuildChannelIncomeListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GoldCommissionServer).AmuseGuildChannelIncomeList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/gold_commission.GoldCommission/AmuseGuildChannelIncomeList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GoldCommissionServer).AmuseGuildChannelIncomeList(ctx, req.(*AmuseGuildChannelIncomeListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GoldCommission_GetConsumeRank_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetConsumeRankReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GoldCommissionServer).GetConsumeRank(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/gold_commission.GoldCommission/GetConsumeRank",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GoldCommissionServer).GetConsumeRank(ctx, req.(*GetConsumeRankReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GoldCommission_GetIncomeTrendList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetIncomeTrendListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GoldCommissionServer).GetIncomeTrendList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/gold_commission.GoldCommission/GetIncomeTrendList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GoldCommissionServer).GetIncomeTrendList(ctx, req.(*GetIncomeTrendListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GoldCommission_GetGuildDayQoq_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGuildDayQoqReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GoldCommissionServer).GetGuildDayQoq(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/gold_commission.GoldCommission/GetGuildDayQoq",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GoldCommissionServer).GetGuildDayQoq(ctx, req.(*GetGuildDayQoqReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GoldCommission_GetGuildMonthMemberList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGuildMonthMemberListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GoldCommissionServer).GetGuildMonthMemberList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/gold_commission.GoldCommission/GetGuildMonthMemberList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GoldCommissionServer).GetGuildMonthMemberList(ctx, req.(*GetGuildMonthMemberListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GoldCommission_GetDayIncome_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDayIncomeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GoldCommissionServer).GetDayIncome(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/gold_commission.GoldCommission/GetDayIncome",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GoldCommissionServer).GetDayIncome(ctx, req.(*GetDayIncomeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GoldCommission_GetMonthIncome_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMonthIncomeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GoldCommissionServer).GetMonthIncome(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/gold_commission.GoldCommission/GetMonthIncome",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GoldCommissionServer).GetMonthIncome(ctx, req.(*GetMonthIncomeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GoldCommission_GetAmuseChannelDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAmuseChannelDetailReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GoldCommissionServer).GetAmuseChannelDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/gold_commission.GoldCommission/GetAmuseChannelDetail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GoldCommissionServer).GetAmuseChannelDetail(ctx, req.(*GetAmuseChannelDetailReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GoldCommission_GetAmuseIncomeDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAmuseIncomeDetailReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GoldCommissionServer).GetAmuseIncomeDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/gold_commission.GoldCommission/GetAmuseIncomeDetail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GoldCommissionServer).GetAmuseIncomeDetail(ctx, req.(*GetAmuseIncomeDetailReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GoldCommission_GetAmuseGuildRoomIncomeList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAmuseGuildRoomIncomeListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GoldCommissionServer).GetAmuseGuildRoomIncomeList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/gold_commission.GoldCommission/GetAmuseGuildRoomIncomeList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GoldCommissionServer).GetAmuseGuildRoomIncomeList(ctx, req.(*GetAmuseGuildRoomIncomeListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GoldCommission_GetAmuseGuildDayIncomeList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGuildDayIncomeListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GoldCommissionServer).GetAmuseGuildDayIncomeList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/gold_commission.GoldCommission/GetAmuseGuildDayIncomeList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GoldCommissionServer).GetAmuseGuildDayIncomeList(ctx, req.(*GetGuildDayIncomeListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GoldCommission_GetAmuseGuildMonthIncomeList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGuildMonthIncomeListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GoldCommissionServer).GetAmuseGuildMonthIncomeList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/gold_commission.GoldCommission/GetAmuseGuildMonthIncomeList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GoldCommissionServer).GetAmuseGuildMonthIncomeList(ctx, req.(*GetGuildMonthIncomeListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GoldCommission_SearchAmuseGuildDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchAmuseGuildDetailReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GoldCommissionServer).SearchAmuseGuildDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/gold_commission.GoldCommission/SearchAmuseGuildDetail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GoldCommissionServer).SearchAmuseGuildDetail(ctx, req.(*SearchAmuseGuildDetailReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GoldCommission_GetAmuseRoomDayQoqInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAmuseRoomDayQoqInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GoldCommissionServer).GetAmuseRoomDayQoqInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/gold_commission.GoldCommission/GetAmuseRoomDayQoqInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GoldCommissionServer).GetAmuseRoomDayQoqInfo(ctx, req.(*GetAmuseRoomDayQoqInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GoldCommission_GetAmuseExtraDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAmuseExtraDetailReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GoldCommissionServer).GetAmuseExtraDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/gold_commission.GoldCommission/GetAmuseExtraDetail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GoldCommissionServer).GetAmuseExtraDetail(ctx, req.(*GetAmuseExtraDetailReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GoldCommission_GetYuyinIncomeDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetYuyinIncomeDetailReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GoldCommissionServer).GetYuyinIncomeDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/gold_commission.GoldCommission/GetYuyinIncomeDetail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GoldCommissionServer).GetYuyinIncomeDetail(ctx, req.(*GetYuyinIncomeDetailReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GoldCommission_GetYuyinWeekIncome_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetYuyinWeekIncomeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GoldCommissionServer).GetYuyinWeekIncome(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/gold_commission.GoldCommission/GetYuyinWeekIncome",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GoldCommissionServer).GetYuyinWeekIncome(ctx, req.(*GetYuyinWeekIncomeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GoldCommission_GetGuildYuyinExtraIncome_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGuildYuyinExtraIncomeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GoldCommissionServer).GetGuildYuyinExtraIncome(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/gold_commission.GoldCommission/GetGuildYuyinExtraIncome",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GoldCommissionServer).GetGuildYuyinExtraIncome(ctx, req.(*GetGuildYuyinExtraIncomeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GoldCommission_GetGuildYuyinTaskList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGuildYuyinTaskListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GoldCommissionServer).GetGuildYuyinTaskList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/gold_commission.GoldCommission/GetGuildYuyinTaskList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GoldCommissionServer).GetGuildYuyinTaskList(ctx, req.(*GetGuildYuyinTaskListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GoldCommission_GetYuyinGuildDayIncomeList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGuildDayIncomeListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GoldCommissionServer).GetYuyinGuildDayIncomeList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/gold_commission.GoldCommission/GetYuyinGuildDayIncomeList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GoldCommissionServer).GetYuyinGuildDayIncomeList(ctx, req.(*GetGuildDayIncomeListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GoldCommission_GetYuyinGuildMonthIncomeList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGuildMonthIncomeListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GoldCommissionServer).GetYuyinGuildMonthIncomeList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/gold_commission.GoldCommission/GetYuyinGuildMonthIncomeList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GoldCommissionServer).GetYuyinGuildMonthIncomeList(ctx, req.(*GetGuildMonthIncomeListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GoldCommission_SearchYuyinGuildDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchYuyinGuildDetailReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GoldCommissionServer).SearchYuyinGuildDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/gold_commission.GoldCommission/SearchYuyinGuildDetail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GoldCommissionServer).SearchYuyinGuildDetail(ctx, req.(*SearchYuyinGuildDetailReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GoldCommission_GetInteractGameIncomeDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetInteractGameIncomeDetailReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GoldCommissionServer).GetInteractGameIncomeDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/gold_commission.GoldCommission/GetInteractGameIncomeDetail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GoldCommissionServer).GetInteractGameIncomeDetail(ctx, req.(*GetInteractGameIncomeDetailReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GoldCommission_GetInteractGameDayIncomeList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGuildDayIncomeListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GoldCommissionServer).GetInteractGameDayIncomeList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/gold_commission.GoldCommission/GetInteractGameDayIncomeList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GoldCommissionServer).GetInteractGameDayIncomeList(ctx, req.(*GetGuildDayIncomeListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GoldCommission_GetInteractGameMonthIncomeList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGuildMonthIncomeListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GoldCommissionServer).GetInteractGameMonthIncomeList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/gold_commission.GoldCommission/GetInteractGameMonthIncomeList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GoldCommissionServer).GetInteractGameMonthIncomeList(ctx, req.(*GetGuildMonthIncomeListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GoldCommission_GetInteractGameWeekIncome_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetInteractGameWeekIncomeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GoldCommissionServer).GetInteractGameWeekIncome(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/gold_commission.GoldCommission/GetInteractGameWeekIncome",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GoldCommissionServer).GetInteractGameWeekIncome(ctx, req.(*GetInteractGameWeekIncomeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GoldCommission_GetESportIncomeDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetESportIncomeDetailReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GoldCommissionServer).GetESportIncomeDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/gold_commission.GoldCommission/GetESportIncomeDetail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GoldCommissionServer).GetESportIncomeDetail(ctx, req.(*GetESportIncomeDetailReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GoldCommission_GetESportDayIncomeList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGuildDayIncomeListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GoldCommissionServer).GetESportDayIncomeList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/gold_commission.GoldCommission/GetESportDayIncomeList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GoldCommissionServer).GetESportDayIncomeList(ctx, req.(*GetGuildDayIncomeListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GoldCommission_GetESportMonthIncomeList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGuildMonthIncomeListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GoldCommissionServer).GetESportMonthIncomeList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/gold_commission.GoldCommission/GetESportMonthIncomeList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GoldCommissionServer).GetESportMonthIncomeList(ctx, req.(*GetGuildMonthIncomeListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GoldCommission_GetESportWeekIncome_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetESportWeekIncomeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GoldCommissionServer).GetESportWeekIncome(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/gold_commission.GoldCommission/GetESportWeekIncome",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GoldCommissionServer).GetESportWeekIncome(ctx, req.(*GetESportWeekIncomeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GoldCommission_GetESportCoachMonthIncomeList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetESportCoachMonthIncomeListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GoldCommissionServer).GetESportCoachMonthIncomeList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/gold_commission.GoldCommission/GetESportCoachMonthIncomeList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GoldCommissionServer).GetESportCoachMonthIncomeList(ctx, req.(*GetESportCoachMonthIncomeListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GoldCommission_GetESportCoachDayIncomeList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetESportCoachDayIncomeListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GoldCommissionServer).GetESportCoachDayIncomeList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/gold_commission.GoldCommission/GetESportCoachDayIncomeList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GoldCommissionServer).GetESportCoachDayIncomeList(ctx, req.(*GetESportCoachDayIncomeListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GoldCommission_GetAmuseGuildIdsByRange_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAmuseGuildIdsByRangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GoldCommissionServer).GetAmuseGuildIdsByRange(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/gold_commission.GoldCommission/GetAmuseGuildIdsByRange",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GoldCommissionServer).GetAmuseGuildIdsByRange(ctx, req.(*GetAmuseGuildIdsByRangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GoldCommission_GetAmuseExtraIncomeSettleList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAmuseExtraIncomeSettleListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GoldCommissionServer).GetAmuseExtraIncomeSettleList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/gold_commission.GoldCommission/GetAmuseExtraIncomeSettleList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GoldCommissionServer).GetAmuseExtraIncomeSettleList(ctx, req.(*GetAmuseExtraIncomeSettleListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GoldCommission_GetAmuseSettleGuildList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAmuseSettleGuildListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GoldCommissionServer).GetAmuseSettleGuildList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/gold_commission.GoldCommission/GetAmuseSettleGuildList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GoldCommissionServer).GetAmuseSettleGuildList(ctx, req.(*GetAmuseSettleGuildListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GoldCommission_GetAmuseSettleChannelList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAmuseSettleChannelListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GoldCommissionServer).GetAmuseSettleChannelList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/gold_commission.GoldCommission/GetAmuseSettleChannelList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GoldCommissionServer).GetAmuseSettleChannelList(ctx, req.(*GetAmuseSettleChannelListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GoldCommission_SetAmuseSettleGuild_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetAmuseSettleGuildReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GoldCommissionServer).SetAmuseSettleGuild(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/gold_commission.GoldCommission/SetAmuseSettleGuild",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GoldCommissionServer).SetAmuseSettleGuild(ctx, req.(*SetAmuseSettleGuildReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GoldCommission_SetAmuseSettleChannel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetAmuseSettleChannelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GoldCommissionServer).SetAmuseSettleChannel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/gold_commission.GoldCommission/SetAmuseSettleChannel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GoldCommissionServer).SetAmuseSettleChannel(ctx, req.(*SetAmuseSettleChannelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GoldCommission_GetGuildInteractGamePer_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGuildInteractGamePerReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GoldCommissionServer).GetGuildInteractGamePer(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/gold_commission.GoldCommission/GetGuildInteractGamePer",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GoldCommissionServer).GetGuildInteractGamePer(ctx, req.(*GetGuildInteractGamePerReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GoldCommission_GetInteractGameExtraIncome_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetInteractGameExtraIncomeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GoldCommissionServer).GetInteractGameExtraIncome(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/gold_commission.GoldCommission/GetInteractGameExtraIncome",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GoldCommissionServer).GetInteractGameExtraIncome(ctx, req.(*GetInteractGameExtraIncomeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GoldCommission_SettlementYuyinGold_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SettlementYuyinGoldReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GoldCommissionServer).SettlementYuyinGold(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/gold_commission.GoldCommission/SettlementYuyinGold",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GoldCommissionServer).SettlementYuyinGold(ctx, req.(*SettlementYuyinGoldReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GoldCommission_GenYuyinExtraReport_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GenYuyinExtraReportReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GoldCommissionServer).GenYuyinExtraReport(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/gold_commission.GoldCommission/GenYuyinExtraReport",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GoldCommissionServer).GenYuyinExtraReport(ctx, req.(*GenYuyinExtraReportReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _GoldCommission_serviceDesc = grpc.ServiceDesc{
	ServiceName: "gold_commission.GoldCommission",
	HandlerType: (*GoldCommissionServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetYuyinGoldPresentOrderCount",
			Handler:    _GoldCommission_GetYuyinGoldPresentOrderCount_Handler,
		},
		{
			MethodName: "GetYuyinGoldPresentOrderList",
			Handler:    _GoldCommission_GetYuyinGoldPresentOrderList_Handler,
		},
		{
			MethodName: "ReplaceYuyinGoldPresentOrder",
			Handler:    _GoldCommission_ReplaceYuyinGoldPresentOrder_Handler,
		},
		{
			MethodName: "GetYuyinGoldKnightOrderCount",
			Handler:    _GoldCommission_GetYuyinGoldKnightOrderCount_Handler,
		},
		{
			MethodName: "GetYuyinGoldKnightOrderList",
			Handler:    _GoldCommission_GetYuyinGoldKnightOrderList_Handler,
		},
		{
			MethodName: "ReplaceYuyinGoldKnightOrder",
			Handler:    _GoldCommission_ReplaceYuyinGoldKnightOrder_Handler,
		},
		{
			MethodName: "GetAmuseGoldPresentOrderCount",
			Handler:    _GoldCommission_GetAmuseGoldPresentOrderCount_Handler,
		},
		{
			MethodName: "GetAmuseGoldPresentOrderList",
			Handler:    _GoldCommission_GetAmuseGoldPresentOrderList_Handler,
		},
		{
			MethodName: "ReplaceAmuseGoldPresentOrder",
			Handler:    _GoldCommission_ReplaceAmuseGoldPresentOrder_Handler,
		},
		{
			MethodName: "GetAmuseGoldWerewolfOrderCount",
			Handler:    _GoldCommission_GetAmuseGoldWerewolfOrderCount_Handler,
		},
		{
			MethodName: "GetAmuseGoldWerewolfOrderList",
			Handler:    _GoldCommission_GetAmuseGoldWerewolfOrderList_Handler,
		},
		{
			MethodName: "ReplaceAmuseGoldWerewolfOrder",
			Handler:    _GoldCommission_ReplaceAmuseGoldWerewolfOrder_Handler,
		},
		{
			MethodName: "GetInteractGameGoldPresentOrderCount",
			Handler:    _GoldCommission_GetInteractGameGoldPresentOrderCount_Handler,
		},
		{
			MethodName: "GetInteractGameGoldPresentOrderList",
			Handler:    _GoldCommission_GetInteractGameGoldPresentOrderList_Handler,
		},
		{
			MethodName: "ReplaceInteractGameGoldPresentOrder",
			Handler:    _GoldCommission_ReplaceInteractGameGoldPresentOrder_Handler,
		},
		{
			MethodName: "GetESportGoldOrderCount",
			Handler:    _GoldCommission_GetESportGoldOrderCount_Handler,
		},
		{
			MethodName: "GetESportGoldOrderList",
			Handler:    _GoldCommission_GetESportGoldOrderList_Handler,
		},
		{
			MethodName: "ReplaceESportGoldOrder",
			Handler:    _GoldCommission_ReplaceESportGoldOrder_Handler,
		},
		{
			MethodName: "GetGuildsYuyinAnchorStat",
			Handler:    _GoldCommission_GetGuildsYuyinAnchorStat_Handler,
		},
		{
			MethodName: "GetGuildsAmuseChannelStat",
			Handler:    _GoldCommission_GetGuildsAmuseChannelStat_Handler,
		},
		{
			MethodName: "GetGuildsInteractGameAnchorStat",
			Handler:    _GoldCommission_GetGuildsInteractGameAnchorStat_Handler,
		},
		{
			MethodName: "GetGuildsESportCoachStat",
			Handler:    _GoldCommission_GetGuildsESportCoachStat_Handler,
		},
		{
			MethodName: "GetGuildsUnSettlementSummary",
			Handler:    _GoldCommission_GetGuildsUnSettlementSummary_Handler,
		},
		{
			MethodName: "AmuseGuildChannelIncomeList",
			Handler:    _GoldCommission_AmuseGuildChannelIncomeList_Handler,
		},
		{
			MethodName: "GetConsumeRank",
			Handler:    _GoldCommission_GetConsumeRank_Handler,
		},
		{
			MethodName: "GetIncomeTrendList",
			Handler:    _GoldCommission_GetIncomeTrendList_Handler,
		},
		{
			MethodName: "GetGuildDayQoq",
			Handler:    _GoldCommission_GetGuildDayQoq_Handler,
		},
		{
			MethodName: "GetGuildMonthMemberList",
			Handler:    _GoldCommission_GetGuildMonthMemberList_Handler,
		},
		{
			MethodName: "GetDayIncome",
			Handler:    _GoldCommission_GetDayIncome_Handler,
		},
		{
			MethodName: "GetMonthIncome",
			Handler:    _GoldCommission_GetMonthIncome_Handler,
		},
		{
			MethodName: "GetAmuseChannelDetail",
			Handler:    _GoldCommission_GetAmuseChannelDetail_Handler,
		},
		{
			MethodName: "GetAmuseIncomeDetail",
			Handler:    _GoldCommission_GetAmuseIncomeDetail_Handler,
		},
		{
			MethodName: "GetAmuseGuildRoomIncomeList",
			Handler:    _GoldCommission_GetAmuseGuildRoomIncomeList_Handler,
		},
		{
			MethodName: "GetAmuseGuildDayIncomeList",
			Handler:    _GoldCommission_GetAmuseGuildDayIncomeList_Handler,
		},
		{
			MethodName: "GetAmuseGuildMonthIncomeList",
			Handler:    _GoldCommission_GetAmuseGuildMonthIncomeList_Handler,
		},
		{
			MethodName: "SearchAmuseGuildDetail",
			Handler:    _GoldCommission_SearchAmuseGuildDetail_Handler,
		},
		{
			MethodName: "GetAmuseRoomDayQoqInfo",
			Handler:    _GoldCommission_GetAmuseRoomDayQoqInfo_Handler,
		},
		{
			MethodName: "GetAmuseExtraDetail",
			Handler:    _GoldCommission_GetAmuseExtraDetail_Handler,
		},
		{
			MethodName: "GetYuyinIncomeDetail",
			Handler:    _GoldCommission_GetYuyinIncomeDetail_Handler,
		},
		{
			MethodName: "GetYuyinWeekIncome",
			Handler:    _GoldCommission_GetYuyinWeekIncome_Handler,
		},
		{
			MethodName: "GetGuildYuyinExtraIncome",
			Handler:    _GoldCommission_GetGuildYuyinExtraIncome_Handler,
		},
		{
			MethodName: "GetGuildYuyinTaskList",
			Handler:    _GoldCommission_GetGuildYuyinTaskList_Handler,
		},
		{
			MethodName: "GetYuyinGuildDayIncomeList",
			Handler:    _GoldCommission_GetYuyinGuildDayIncomeList_Handler,
		},
		{
			MethodName: "GetYuyinGuildMonthIncomeList",
			Handler:    _GoldCommission_GetYuyinGuildMonthIncomeList_Handler,
		},
		{
			MethodName: "SearchYuyinGuildDetail",
			Handler:    _GoldCommission_SearchYuyinGuildDetail_Handler,
		},
		{
			MethodName: "GetInteractGameIncomeDetail",
			Handler:    _GoldCommission_GetInteractGameIncomeDetail_Handler,
		},
		{
			MethodName: "GetInteractGameDayIncomeList",
			Handler:    _GoldCommission_GetInteractGameDayIncomeList_Handler,
		},
		{
			MethodName: "GetInteractGameMonthIncomeList",
			Handler:    _GoldCommission_GetInteractGameMonthIncomeList_Handler,
		},
		{
			MethodName: "GetInteractGameWeekIncome",
			Handler:    _GoldCommission_GetInteractGameWeekIncome_Handler,
		},
		{
			MethodName: "GetESportIncomeDetail",
			Handler:    _GoldCommission_GetESportIncomeDetail_Handler,
		},
		{
			MethodName: "GetESportDayIncomeList",
			Handler:    _GoldCommission_GetESportDayIncomeList_Handler,
		},
		{
			MethodName: "GetESportMonthIncomeList",
			Handler:    _GoldCommission_GetESportMonthIncomeList_Handler,
		},
		{
			MethodName: "GetESportWeekIncome",
			Handler:    _GoldCommission_GetESportWeekIncome_Handler,
		},
		{
			MethodName: "GetESportCoachMonthIncomeList",
			Handler:    _GoldCommission_GetESportCoachMonthIncomeList_Handler,
		},
		{
			MethodName: "GetESportCoachDayIncomeList",
			Handler:    _GoldCommission_GetESportCoachDayIncomeList_Handler,
		},
		{
			MethodName: "GetAmuseGuildIdsByRange",
			Handler:    _GoldCommission_GetAmuseGuildIdsByRange_Handler,
		},
		{
			MethodName: "GetAmuseExtraIncomeSettleList",
			Handler:    _GoldCommission_GetAmuseExtraIncomeSettleList_Handler,
		},
		{
			MethodName: "GetAmuseSettleGuildList",
			Handler:    _GoldCommission_GetAmuseSettleGuildList_Handler,
		},
		{
			MethodName: "GetAmuseSettleChannelList",
			Handler:    _GoldCommission_GetAmuseSettleChannelList_Handler,
		},
		{
			MethodName: "SetAmuseSettleGuild",
			Handler:    _GoldCommission_SetAmuseSettleGuild_Handler,
		},
		{
			MethodName: "SetAmuseSettleChannel",
			Handler:    _GoldCommission_SetAmuseSettleChannel_Handler,
		},
		{
			MethodName: "GetGuildInteractGamePer",
			Handler:    _GoldCommission_GetGuildInteractGamePer_Handler,
		},
		{
			MethodName: "GetInteractGameExtraIncome",
			Handler:    _GoldCommission_GetInteractGameExtraIncome_Handler,
		},
		{
			MethodName: "SettlementYuyinGold",
			Handler:    _GoldCommission_SettlementYuyinGold_Handler,
		},
		{
			MethodName: "GenYuyinExtraReport",
			Handler:    _GoldCommission_GenYuyinExtraReport_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "gold-commission/gold-commission.proto",
}

func init() {
	proto.RegisterFile("gold-commission/gold-commission.proto", fileDescriptor_gold_commission_18e8a43e3dce6c6d)
}

var fileDescriptor_gold_commission_18e8a43e3dce6c6d = []byte{
	// 5904 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xec, 0x3d, 0x5d, 0x6f, 0x1b, 0xcb,
	0x75, 0x5a, 0x92, 0x12, 0xc9, 0x43, 0x89, 0xa2, 0xc7, 0xb6, 0x24, 0x53, 0xd7, 0xd7, 0xf6, 0xda,
	0xb9, 0x51, 0x7c, 0x7d, 0xe5, 0x58, 0xbe, 0x4e, 0xd0, 0x22, 0x1f, 0x95, 0x25, 0x59, 0x11, 0xae,
	0x3f, 0xe4, 0x95, 0x64, 0xc7, 0x37, 0x1f, 0x8b, 0x35, 0x39, 0x92, 0x37, 0x22, 0x77, 0x69, 0xee,
	0xd2, 0xb2, 0x9a, 0xa2, 0x41, 0x8a, 0x3c, 0x04, 0x05, 0x5a, 0xa4, 0x40, 0x9f, 0x5b, 0x14, 0x48,
	0x9a, 0xa7, 0xf6, 0xb5, 0x69, 0x1f, 0x8a, 0xa2, 0x2d, 0x02, 0x14, 0xfd, 0x40, 0xdb, 0x87, 0xa2,
	0xfd, 0x01, 0xed, 0x4b, 0x81, 0xbe, 0xb6, 0x05, 0x52, 0xb4, 0x98, 0x33, 0xb3, 0xbb, 0xb3, 0xbb,
	0xb3, 0x4b, 0x52, 0x72, 0x72, 0x91, 0xe2, 0xbe, 0x71, 0xce, 0x9c, 0xf9, 0x3a, 0xe7, 0xcc, 0x39,
	0x67, 0xce, 0x9c, 0x59, 0xc2, 0x27, 0x0e, 0xdc, 0x4e, 0xfb, 0xbd, 0x96, 0xdb, 0xed, 0xda, 0x9e,
	0x67, 0xbb, 0xce, 0xcd, 0x44, 0x79, 0xb9, 0xd7, 0x77, 0x7d, 0x97, 0xcc, 0x32, 0xb0, 0x19, 0x81,
	0x9b, 0x97, 0xfa, 0xb4, 0xe5, 0x3a, 0x2d, 0xbb, 0x43, 0xdf, 0x7b, 0xb5, 0x72, 0x53, 0x2e, 0xf0,
	0x16, 0xba, 0x05, 0x17, 0x36, 0xa9, 0xbf, 0xda, 0x1d, 0x78, 0xd4, 0x70, 0xdd, 0xee, 0xba, 0x75,
	0xfc, 0xd8, 0x7d, 0xb9, 0xe5, 0xec, 0xbb, 0x06, 0x7d, 0x49, 0x2e, 0x40, 0xe5, 0x60, 0x60, 0x77,
	0xda, 0xa6, 0xdd, 0x5e, 0xd0, 0x2e, 0x6b, 0x4b, 0x33, 0x46, 0x19, 0xcb, 0x5b, 0x6d, 0x32, 0x0f,
	0xe5, 0xbe, 0xeb, 0x76, 0x59, 0x4d, 0x01, 0x6b, 0xa6, 0x58, 0x71, 0xab, 0x4d, 0x1a, 0x50, 0x6c,
	0x5b, 0xc7, 0x0b, 0xc5, 0xcb, 0xda, 0x52, 0xd1, 0x60, 0x3f, 0xf5, 0xbf, 0xd3, 0x32, 0xc7, 0xf0,
	0x7a, 0x64, 0x11, 0xaa, 0x9e, 0x6f, 0xf9, 0xa6, 0x6f, 0x77, 0x29, 0x0e, 0x52, 0x34, 0x2a, 0x0c,
	0xb0, 0x6b, 0x77, 0x29, 0x99, 0x83, 0x29, 0xdb, 0x69, 0xb9, 0x5d, 0x8a, 0x83, 0x14, 0x0d, 0x51,
	0x22, 0xd7, 0xe1, 0x4c, 0xcb, 0xed, 0xf6, 0xac, 0x3e, 0x35, 0xa3, 0xc6, 0x7c, 0xc8, 0x59, 0x51,
	0xb1, 0x13, 0xf4, 0xf1, 0x09, 0xa8, 0x07, 0xb8, 0xa2, 0xaf, 0x12, 0x22, 0xce, 0x08, 0xe8, 0x16,
	0xef, 0xb2, 0x01, 0xc5, 0x97, 0xee, 0xcb, 0x85, 0xc9, 0xcb, 0xda, 0x52, 0xc1, 0x60, 0x3f, 0xe5,
	0x25, 0x4e, 0xc9, 0x4b, 0xd4, 0xef, 0xc0, 0x42, 0xb0, 0x9e, 0xb5, 0x17, 0x96, 0xe3, 0xd0, 0xce,
	0x3a, 0xf5, 0x2d, 0xbb, 0x93, 0x4f, 0x32, 0xfd, 0xc3, 0xac, 0x66, 0x5e, 0x8f, 0x7c, 0x01, 0xa6,
	0x5b, 0x1c, 0x66, 0xee, 0x53, 0xea, 0x2d, 0x68, 0x97, 0x8b, 0x4b, 0xb5, 0x95, 0xc5, 0xe5, 0x04,
	0x3f, 0x97, 0x45, 0xc3, 0x7b, 0x94, 0x1a, 0xb5, 0x56, 0xf8, 0xdb, 0xd3, 0xbb, 0x00, 0x51, 0x95,
	0x3c, 0x73, 0x2d, 0xc9, 0x9c, 0x7d, 0x1a, 0x10, 0x93, 0xfd, 0x24, 0xef, 0xc3, 0x7c, 0xd7, 0x75,
	0xfc, 0x17, 0x66, 0xc7, 0xf2, 0x7c, 0xd3, 0x72, 0xda, 0xa6, 0xff, 0xc2, 0xf6, 0x4c, 0x46, 0x8a,
	0x22, 0x92, 0xe2, 0x2c, 0x56, 0xdf, 0xb7, 0x3c, 0x7f, 0xd5, 0x69, 0xef, 0xbe, 0xb0, 0xbd, 0xc7,
	0xee, 0x4b, 0xfd, 0x7d, 0x98, 0x0f, 0x96, 0xc2, 0xc9, 0x37, 0x12, 0x01, 0xfe, 0xb1, 0x94, 0xd1,
	0xcc, 0xeb, 0x91, 0x2b, 0x30, 0xed, 0xbb, 0x6d, 0xeb, 0x38, 0xe0, 0x11, 0x97, 0x84, 0x1a, 0xc2,
	0x04, 0x87, 0x3e, 0x05, 0x8d, 0x63, 0xea, 0xf9, 0xb4, 0x2f, 0xa1, 0xf1, 0x95, 0xcc, 0x86, 0xf0,
	0xad, 0x50, 0x3e, 0x70, 0x19, 0x7c, 0x69, 0x02, 0x57, 0xc8, 0x07, 0xab, 0x78, 0xc0, 0xe0, 0x11,
	0x2e, 0xae, 0x3d, 0x86, 0xcb, 0x45, 0x64, 0x96, 0x55, 0xc8, 0xb8, 0xf3, 0x50, 0x66, 0x83, 0x47,
	0x82, 0x32, 0xd5, 0x46, 0x61, 0x66, 0x52, 0xcc, 0xdb, 0xb3, 0xaa, 0x29, 0xac, 0xaa, 0x20, 0x80,
	0x55, 0x5e, 0x85, 0x99, 0x68, 0xe2, 0x0c, 0xa1, 0x8c, 0x08, 0xd3, 0x21, 0x90, 0x21, 0xe5, 0x30,
	0xa2, 0x92, 0xc9, 0x08, 0x72, 0x1b, 0xe6, 0xa4, 0xc9, 0x7b, 0x56, 0x97, 0x9a, 0x3d, 0xda, 0xb7,
	0xdd, 0xf6, 0x42, 0x15, 0x57, 0x70, 0x36, 0x5c, 0xc1, 0x8e, 0xd5, 0xa5, 0xdb, 0x58, 0x45, 0x96,
	0xa0, 0x21, 0xf0, 0xed, 0xd7, 0xc1, 0x82, 0x01, 0xd1, 0xeb, 0x08, 0xdf, 0xb1, 0x5f, 0x8b, 0xf5,
	0xde, 0x81, 0x79, 0x89, 0x8e, 0xf4, 0xb5, 0xdf, 0xb7, 0x82, 0x06, 0x35, 0x6c, 0x70, 0x2e, 0xa4,
	0xe6, 0x06, 0xab, 0x8c, 0x9a, 0x49, 0xb3, 0x8a, 0x35, 0x9b, 0xe6, 0xcd, 0xc2, 0x69, 0xc9, 0xcd,
	0x6e, 0xc3, 0x9c, 0xe3, 0xfa, 0xa6, 0x47, 0x7d, 0xbf, 0x43, 0x4d, 0x8b, 0x89, 0x09, 0x6f, 0xbc,
	0x30, 0x73, 0x59, 0x5b, 0xaa, 0x18, 0x67, 0x1d, 0xd7, 0xdf, 0xc1, 0x4a, 0x14, 0x21, 0x6c, 0xaa,
	0x0f, 0xa0, 0xce, 0xb6, 0x3a, 0xef, 0x82, 0x29, 0x95, 0x7c, 0x8d, 0xb2, 0x00, 0xe5, 0x2e, 0xed,
	0x3e, 0xa7, 0x7d, 0x4f, 0xc8, 0x4e, 0x50, 0x64, 0x35, 0x7c, 0x8e, 0x9e, 0x90, 0x94, 0xa0, 0x48,
	0x08, 0x94, 0x70, 0x53, 0x72, 0xa1, 0xc0, 0xdf, 0xfa, 0x7f, 0x6a, 0xf0, 0x76, 0x20, 0xcb, 0x9b,
	0x4c, 0xbe, 0x99, 0x66, 0xe3, 0x93, 0xb8, 0x6f, 0x7b, 0xfe, 0x10, 0xed, 0x39, 0x07, 0x53, 0xee,
	0xfe, 0xbe, 0x47, 0xfd, 0x40, 0x79, 0xf2, 0x12, 0x39, 0x07, 0x93, 0x1d, 0xbb, 0x6b, 0xfb, 0x38,
	0x83, 0x19, 0x83, 0x17, 0xc8, 0x2f, 0x00, 0xf4, 0x2d, 0xe7, 0x80, 0x9a, 0xfe, 0x71, 0x8f, 0x8b,
	0x66, 0x7d, 0xa5, 0x99, 0x52, 0x0d, 0x06, 0x43, 0xd9, 0x3d, 0xee, 0x51, 0xa3, 0xda, 0x0f, 0x7e,
	0x92, 0x8b, 0x00, 0xcf, 0xe9, 0x81, 0xed, 0x70, 0x62, 0x4c, 0xe2, 0x02, 0xaa, 0x08, 0x41, 0x6a,
	0x5c, 0x80, 0x0a, 0x65, 0x92, 0xc6, 0x2a, 0xa7, 0xf8, 0xa2, 0xa9, 0xd3, 0xc6, 0x2a, 0x49, 0x87,
	0x94, 0x63, 0xda, 0xef, 0x6f, 0x86, 0xac, 0xdc, 0xeb, 0xb1, 0x51, 0x7d, 0xd7, 0xb7, 0x42, 0x5d,
	0x86, 0xa3, 0x22, 0x84, 0x29, 0x2b, 0xb6, 0x1f, 0x78, 0x75, 0x40, 0x6f, 0xce, 0x89, 0x69, 0x04,
	0x6e, 0x09, 0xa2, 0x2f, 0x42, 0xd5, 0xa1, 0xaf, 0x7d, 0xb3, 0x67, 0x1d, 0xf0, 0xad, 0x5b, 0x31,
	0x2a, 0x0c, 0xb0, 0x6d, 0x1d, 0x50, 0xb2, 0x05, 0x0d, 0x64, 0x31, 0xef, 0xc0, 0xb4, 0x9d, 0x7d,
	0x77, 0xa1, 0x84, 0x2a, 0xf3, 0x52, 0x8a, 0x2e, 0x71, 0xe9, 0x30, 0xea, 0x5e, 0xac, 0xac, 0xaf,
	0xc0, 0x1c, 0x97, 0xa9, 0x2e, 0x75, 0xfc, 0x67, 0x83, 0x63, 0xdb, 0xd9, 0x74, 0x3b, 0x6d, 0xc6,
	0xbf, 0x05, 0x28, 0xb7, 0x68, 0xdf, 0xb7, 0x6c, 0x07, 0x97, 0x50, 0x31, 0x82, 0xa2, 0x7e, 0x01,
	0xe6, 0x95, 0x6d, 0xbc, 0x9e, 0x7e, 0x1b, 0xe6, 0x36, 0xa9, 0x83, 0x30, 0x94, 0x4f, 0x83, 0xf6,
	0xdc, 0x7e, 0x20, 0x0e, 0xb6, 0x67, 0xb6, 0x2d, 0xbb, 0x73, 0x1c, 0xf4, 0x67, 0x7b, 0xeb, 0xac,
	0xc8, 0xfa, 0x53, 0x36, 0xf2, 0x7a, 0xfa, 0x5f, 0x4d, 0x42, 0x7d, 0xf5, 0xc8, 0xea, 0xb7, 0xd9,
	0x08, 0x8f, 0xfa, 0x6d, 0xda, 0xcf, 0x93, 0xab, 0x0b, 0x50, 0x71, 0x19, 0x4e, 0x60, 0x96, 0xab,
	0x46, 0x19, 0xcb, 0x91, 0xea, 0x67, 0x94, 0x2c, 0x71, 0xd5, 0x1f, 0x19, 0xd7, 0x12, 0x02, 0x03,
	0xe3, 0x7a, 0x11, 0x20, 0xb0, 0x45, 0x76, 0x1b, 0x65, 0x66, 0xc6, 0xa8, 0x0a, 0xc8, 0x56, 0x1b,
	0x99, 0x6b, 0xf5, 0x0f, 0xa8, 0x6f, 0x0e, 0x42, 0xcb, 0x58, 0xe5, 0x90, 0x3d, 0x1b, 0xa7, 0xd0,
	0xb3, 0xec, 0x36, 0x56, 0x72, 0xc1, 0x29, 0xb3, 0x32, 0xab, 0xba, 0x04, 0xb5, 0xe7, 0xee, 0xe0,
	0xe0, 0x85, 0xd8, 0x9a, 0x15, 0xac, 0x05, 0x0e, 0x0a, 0xcc, 0x3d, 0x7d, 0xed, 0x53, 0x87, 0x6b,
	0xaf, 0xaa, 0x21, 0x4a, 0xe4, 0x73, 0x50, 0xf3, 0xdc, 0x41, 0xbf, 0x25, 0x76, 0x00, 0xe0, 0x0e,
	0x48, 0x1b, 0xc7, 0x1d, 0xc4, 0xc1, 0x2d, 0x00, 0x5e, 0xf8, 0x9b, 0xdc, 0x00, 0x62, 0x7b, 0x66,
	0xe8, 0xfb, 0x98, 0x48, 0x11, 0xd4, 0x5f, 0x15, 0xa3, 0x61, 0x7b, 0x46, 0x50, 0xc1, 0xa9, 0x7b,
	0x11, 0xa0, 0x4d, 0xad, 0x8e, 0xe9, 0xbb, 0x87, 0xd4, 0x41, 0x75, 0x55, 0x35, 0xaa, 0x0c, 0xb2,
	0xcb, 0x00, 0xe4, 0x6d, 0xa8, 0xd9, 0x9e, 0x39, 0x38, 0x3c, 0x32, 0xd9, 0xaa, 0x84, 0x62, 0xaa,
	0xda, 0xde, 0xde, 0xe1, 0xd1, 0xb6, 0x65, 0xb7, 0x89, 0x0e, 0x33, 0xa2, 0x9e, 0x93, 0x64, 0xa1,
	0x8e, 0x18, 0x35, 0xc4, 0xd8, 0x45, 0x10, 0xf9, 0x02, 0x04, 0xb6, 0x9b, 0xcd, 0x6f, 0x61, 0x16,
	0x97, 0xf3, 0x56, 0x96, 0xad, 0xc7, 0xf5, 0xc8, 0x0d, 0x98, 0xfe, 0x0e, 0x26, 0x60, 0x5a, 0xad,
	0x96, 0x3b, 0x70, 0xfc, 0x85, 0x06, 0x4e, 0xb4, 0x3e, 0xe0, 0xd3, 0x58, 0xe5, 0x50, 0xb6, 0xf4,
	0x68, 0x2a, 0x21, 0xee, 0x19, 0xc4, 0x65, 0x7d, 0xf0, 0x09, 0x05, 0xd8, 0x9f, 0x81, 0x2a, 0xce,
	0x01, 0x89, 0x4c, 0x70, 0x56, 0x17, 0x52, 0xb3, 0x62, 0x72, 0x88, 0x53, 0xaa, 0x1c, 0x88, 0x5f,
	0xe4, 0x1d, 0x98, 0xb5, 0x3d, 0xf3, 0x95, 0xdd, 0xf7, 0x07, 0x56, 0xc7, 0xec, 0xd8, 0xaf, 0xe8,
	0xc2, 0x59, 0x5c, 0xf5, 0x8c, 0xed, 0x3d, 0xe1, 0xd0, 0xfb, 0xf6, 0x2b, 0xaa, 0x7f, 0xbf, 0x08,
	0xf5, 0xbb, 0x96, 0x47, 0x57, 0x9d, 0xd6, 0x0b, 0xb7, 0x8f, 0xba, 0x3a, 0x47, 0x96, 0x17, 0xa1,
	0x6a, 0x21, 0x62, 0xe4, 0x63, 0x56, 0x38, 0x60, 0xab, 0xcd, 0x54, 0x72, 0xdb, 0xf2, 0xa9, 0xd0,
	0x93, 0xf8, 0x9b, 0x5c, 0x86, 0xe9, 0x40, 0xf2, 0xcc, 0x96, 0xe3, 0xa3, 0x54, 0xcf, 0x18, 0x20,
	0xa4, 0x6f, 0xcd, 0xf1, 0x63, 0xb2, 0x39, 0x19, 0x97, 0xcd, 0x25, 0x68, 0xf0, 0xaa, 0xc3, 0xa3,
	0x90, 0x4e, 0x53, 0x9c, 0xa6, 0x88, 0x72, 0x78, 0x14, 0x50, 0x49, 0x6c, 0x24, 0x50, 0x6d, 0xa4,
	0x5a, 0x6c, 0x23, 0x5d, 0x82, 0x5a, 0xaf, 0x4f, 0x3d, 0xea, 0xf8, 0x4c, 0x11, 0xa2, 0x2c, 0x95,
	0x0c, 0x10, 0x20, 0xe6, 0xa7, 0x5d, 0x04, 0x38, 0x74, 0x6c, 0xb6, 0x21, 0x58, 0x7d, 0x1d, 0xeb,
	0xab, 0x1c, 0xc2, 0xaa, 0xaf, 0xc3, 0x19, 0xdb, 0xf1, 0x69, 0xdf, 0x6a, 0xf9, 0xe6, 0x01, 0x33,
	0xed, 0x0c, 0x6b, 0x16, 0xb1, 0x66, 0x83, 0x8a, 0x4d, 0xab, 0x4b, 0x95, 0xb8, 0xed, 0x41, 0x1f,
	0x85, 0x22, 0x81, 0xbb, 0x3e, 0x40, 0x11, 0xa7, 0x1e, 0xd3, 0x30, 0xd8, 0xe1, 0x19, 0x3e, 0x2c,
	0x87, 0xdc, 0xa3, 0x54, 0xff, 0xdf, 0x02, 0xcc, 0x32, 0x36, 0x09, 0xf9, 0x1b, 0xc6, 0xa7, 0xb8,
	0xba, 0x28, 0x24, 0xd5, 0xc5, 0xcf, 0x84, 0x53, 0xe5, 0x8f, 0x98, 0x53, 0x57, 0x60, 0xfa, 0x88,
	0xf6, 0xe9, 0x91, 0xdb, 0xd9, 0x97, 0x98, 0x54, 0x0b, 0x60, 0x99, 0xcc, 0x6c, 0x28, 0x99, 0xa9,
	0xff, 0xa8, 0x00, 0x33, 0x8c, 0x03, 0x68, 0x5e, 0x87, 0xd1, 0xff, 0x64, 0x04, 0xfe, 0x79, 0xa5,
	0xcd, 0x30, 0xe1, 0xfd, 0x9d, 0x02, 0x2c, 0x6e, 0x52, 0x1f, 0x29, 0xe7, 0xa1, 0x45, 0xe5, 0xda,
	0x86, 0x79, 0x01, 0xcc, 0x0a, 0x2f, 0x42, 0x35, 0x20, 0x24, 0x3f, 0x65, 0xcd, 0x18, 0x15, 0x41,
	0x49, 0x2f, 0xe1, 0x2d, 0x09, 0x51, 0x56, 0x7b, 0x4b, 0x9c, 0xda, 0xa1, 0xb7, 0xb4, 0x01, 0x33,
	0xc2, 0x6d, 0x65, 0xee, 0xc5, 0xc0, 0x13, 0x5e, 0xda, 0xe5, 0xb4, 0x97, 0x46, 0x5f, 0x72, 0xd7,
	0x61, 0x07, 0xf1, 0x8c, 0x69, 0x4f, 0x2a, 0x91, 0xdb, 0x50, 0x1a, 0x38, 0xb6, 0x8f, 0x22, 0x5f,
	0x57, 0xf8, 0x32, 0x6c, 0xac, 0x7b, 0x76, 0xc7, 0xa7, 0xfd, 0x3d, 0xc7, 0xf6, 0x0d, 0x44, 0x96,
	0x9c, 0xc9, 0x29, 0xb5, 0x33, 0x59, 0x96, 0x9c, 0x49, 0xdd, 0x84, 0xb7, 0xb2, 0xe9, 0xe3, 0xf5,
	0xc8, 0x17, 0x01, 0xb8, 0x96, 0x65, 0xce, 0x9c, 0x38, 0x87, 0xa6, 0x27, 0x12, 0x57, 0xe3, 0x86,
	0xd4, 0x44, 0xff, 0xdd, 0x82, 0x34, 0x82, 0x7c, 0xda, 0xfd, 0x98, 0x05, 0x82, 0x05, 0x2d, 0xb8,
	0x98, 0x43, 0x20, 0xaf, 0x47, 0xee, 0x86, 0x0e, 0x82, 0xc4, 0x84, 0xcb, 0x4a, 0x26, 0x48, 0x4a,
	0xda, 0x90, 0x1b, 0xe9, 0x3f, 0x28, 0x80, 0x1e, 0x8e, 0xb2, 0x25, 0x6d, 0xa2, 0x8f, 0xf7, 0x43,
	0x8c, 0x19, 0xfb, 0x70, 0x75, 0x28, 0x99, 0xde, 0xc4, 0xb6, 0x88, 0x29, 0xa6, 0x8d, 0x1d, 0xa6,
	0xaf, 0xd6, 0x5c, 0xab, 0xf5, 0xe2, 0x63, 0x46, 0x08, 0x46, 0x7c, 0x4d, 0x52, 0x1b, 0x29, 0xfa,
	0x78, 0x3d, 0xf2, 0x79, 0xa8, 0xb6, 0x18, 0x60, 0x1c, 0x06, 0x44, 0x2d, 0xf4, 0xdf, 0x2b, 0xc0,
	0xa5, 0xb0, 0xff, 0x3d, 0x27, 0x3a, 0xbf, 0xed, 0x0c, 0xba, 0x5d, 0xab, 0x7f, 0x3c, 0x94, 0x07,
	0xb7, 0x61, 0xce, 0x76, 0x5a, 0x9d, 0x41, 0x9b, 0x9a, 0x3c, 0x52, 0x85, 0x16, 0x96, 0x59, 0xd7,
	0x02, 0x8f, 0x4e, 0x88, 0xda, 0x5d, 0x56, 0xc9, 0x9c, 0x70, 0x66, 0x66, 0xa5, 0x46, 0x3c, 0x18,
	0x12, 0x36, 0x2a, 0xc6, 0x1a, 0x61, 0x2c, 0x24, 0x68, 0xf4, 0xc5, 0x28, 0x18, 0x28, 0x9d, 0xf8,
	0xc7, 0x38, 0x20, 0xc4, 0x1c, 0xf9, 0xc9, 0x91, 0x1d, 0x79, 0xfd, 0x7b, 0xc5, 0x21, 0x34, 0xe2,
	0xf1, 0x5a, 0xbe, 0x7c, 0x66, 0x7e, 0x35, 0x34, 0xbf, 0x15, 0x04, 0x08, 0x5b, 0x1f, 0x8b, 0xe2,
	0x15, 0xb8, 0xad, 0x97, 0xa3, 0x78, 0x61, 0xa4, 0x2c, 0x3a, 0x8d, 0xf2, 0x48, 0x99, 0x68, 0x9f,
	0x0a, 0xc3, 0x95, 0x8c, 0x5a, 0x57, 0x0a, 0xc1, 0x5d, 0x83, 0x7a, 0x82, 0xfc, 0x93, 0x88, 0xc4,
	0x07, 0x0e, 0x48, 0x78, 0x0d, 0xea, 0x09, 0x7a, 0x4f, 0x71, 0xac, 0xae, 0x4c, 0xe8, 0x9c, 0xf0,
	0x56, 0x19, 0xd1, 0xc7, 0x0e, 0x6f, 0x55, 0x78, 0xb3, 0x31, 0xc3, 0x5b, 0xd5, 0xec, 0xf0, 0xd6,
	0x3d, 0x0c, 0x99, 0xa2, 0xa1, 0x4e, 0x46, 0x5a, 0x1b, 0x50, 0x1c, 0x84, 0xee, 0x20, 0xfb, 0x19,
	0xf3, 0x12, 0x0b, 0xf1, 0xd8, 0xeb, 0xbf, 0x6a, 0x19, 0x1d, 0xa1, 0x6e, 0x9b, 0xc6, 0xb0, 0x84,
	0xcc, 0xb5, 0x9a, 0x42, 0xde, 0x30, 0x58, 0xc1, 0x1b, 0x1b, 0xb5, 0x76, 0x54, 0x20, 0x77, 0x61,
	0xe6, 0x88, 0xd2, 0xc3, 0xa8, 0x87, 0x22, 0xf6, 0x70, 0x31, 0xd5, 0xc3, 0x53, 0xc4, 0x12, 0x5d,
	0x4c, 0x1f, 0x49, 0x25, 0xb2, 0x21, 0x38, 0x16, 0x75, 0x52, 0xc2, 0x4e, 0xde, 0x4e, 0x75, 0xf2,
	0x80, 0xa3, 0x89, 0x5e, 0x66, 0xba, 0x72, 0x51, 0xff, 0x13, 0x0d, 0x6a, 0xd2, 0x3c, 0x95, 0x71,
	0xe5, 0xd2, 0x68, 0x71, 0xe5, 0x52, 0x3a, 0xae, 0xbc, 0x08, 0x55, 0x4e, 0xa9, 0x28, 0x3e, 0x5e,
	0x41, 0x80, 0x88, 0x01, 0x47, 0x3b, 0xa3, 0x94, 0xd8, 0x19, 0xb1, 0x18, 0x30, 0x43, 0x10, 0x52,
	0x1b, 0x02, 0x99, 0xf3, 0xfa, 0x0f, 0x1a, 0x4c, 0xcb, 0x24, 0x62, 0x67, 0x1d, 0x14, 0x50, 0x46,
	0xa9, 0xf8, 0x0a, 0xea, 0x0c, 0xce, 0x70, 0x23, 0x4c, 0x94, 0x49, 0x19, 0x93, 0x2f, 0xa2, 0xce,
	0xe0, 0x12, 0xe6, 0x15, 0x98, 0x6e, 0x0d, 0xfa, 0x7d, 0xe6, 0xcf, 0x4b, 0xd7, 0x26, 0x35, 0x01,
	0x43, 0xa3, 0xf1, 0x0e, 0xcc, 0x62, 0x3f, 0x9e, 0x6f, 0xf5, 0x05, 0x96, 0xb8, 0x33, 0x61, 0xe0,
	0x1d, 0x06, 0x45, 0x3c, 0x9d, 0xf3, 0xdd, 0x0c, 0x8d, 0x0f, 0x0f, 0x30, 0xd6, 0x18, 0x70, 0x83,
	0x1b, 0x20, 0xfd, 0x87, 0x05, 0x98, 0x89, 0x71, 0x4c, 0x1d, 0x9c, 0xe7, 0xab, 0x1a, 0x2d, 0x38,
	0x2f, 0x98, 0x93, 0x0c, 0xce, 0x5f, 0x82, 0x5a, 0x20, 0x41, 0x11, 0x7b, 0x40, 0x80, 0x44, 0xb0,
	0x1c, 0x23, 0xe4, 0xea, 0x70, 0x7f, 0xc9, 0x38, 0xcb, 0x6a, 0xef, 0x27, 0x7a, 0x8d, 0x6f, 0xf6,
	0x57, 0x56, 0xc7, 0x6e, 0x07, 0xad, 0x26, 0x13, 0x9b, 0xfd, 0x09, 0xab, 0x14, 0xcd, 0x6e, 0xc1,
	0x79, 0x6c, 0xe6, 0xd9, 0xaf, 0xe3, 0x43, 0x71, 0x3d, 0x44, 0x58, 0xe5, 0x8e, 0xfd, 0x5a, 0x1a,
	0x49, 0xff, 0x67, 0x0d, 0xce, 0x6f, 0x52, 0x11, 0x9b, 0xdc, 0xed, 0x53, 0xa7, 0x1d, 0x44, 0x92,
	0xc7, 0xd9, 0xe9, 0x29, 0x67, 0x40, 0x8a, 0xe9, 0x7e, 0x64, 0x86, 0x65, 0x53, 0xb9, 0x32, 0xaf,
	0x47, 0x96, 0xa1, 0xd4, 0x89, 0xec, 0x79, 0x3a, 0xa8, 0x8d, 0xc8, 0x68, 0xca, 0x11, 0x4f, 0xbf,
	0x0d, 0xd5, 0x10, 0xc4, 0xc8, 0x72, 0x48, 0x79, 0x30, 0xb5, 0x6a, 0xb0, 0x9f, 0xcc, 0xb3, 0x78,
	0x65, 0x75, 0x06, 0x81, 0x88, 0xf0, 0x82, 0xfe, 0x6f, 0x1a, 0x9c, 0xd9, 0xa4, 0xfe, 0x9a, 0xeb,
	0x78, 0x83, 0x2e, 0x35, 0x2c, 0xe7, 0x90, 0x11, 0xf5, 0xf3, 0x30, 0xdd, 0xe2, 0x10, 0xbe, 0x1c,
	0x6d, 0x68, 0x5c, 0xbd, 0x26, 0xf0, 0x91, 0x14, 0x39, 0x1c, 0x48, 0x92, 0xb9, 0x78, 0x2a, 0x32,
	0x97, 0x46, 0x27, 0xb3, 0x95, 0x5a, 0xa7, 0xd7, 0x23, 0xf7, 0xe1, 0x4c, 0xb0, 0xce, 0xbe, 0xe5,
	0x1c, 0x9a, 0x9d, 0xbc, 0x23, 0x85, 0xd4, 0x76, 0xcb, 0xa7, 0x5d, 0x63, 0xb6, 0x15, 0x01, 0xd0,
	0x8d, 0x7a, 0x0d, 0xb3, 0x09, 0x1c, 0x46, 0x74, 0xab, 0x63, 0x5b, 0x9e, 0x60, 0x04, 0x2f, 0x60,
	0xfc, 0xde, 0x6e, 0x1d, 0x9a, 0x8e, 0x25, 0x76, 0x6c, 0xd5, 0xa8, 0x30, 0xc0, 0x43, 0x8b, 0xdf,
	0xc2, 0x04, 0xa1, 0x9b, 0x22, 0x0f, 0x53, 0x8b, 0x22, 0x06, 0xdd, 0x79, 0xff, 0x42, 0xe5, 0x04,
	0x45, 0xfd, 0x8f, 0x34, 0x98, 0xdd, 0xa4, 0xfe, 0x7a, 0xa0, 0x8c, 0xc7, 0xde, 0x18, 0x1f, 0x19,
	0x5b, 0x8c, 0xc4, 0xc4, 0x15, 0x26, 0x57, 0x1b, 0xd3, 0xe4, 0xea, 0xeb, 0xb8, 0xa3, 0xd0, 0x9c,
	0x47, 0xba, 0x7d, 0x6c, 0xaf, 0xe0, 0x2b, 0xca, 0x5e, 0xf0, 0x04, 0x9a, 0xb0, 0xe8, 0xda, 0xd8,
	0x16, 0x5d, 0xff, 0x00, 0x1d, 0x7a, 0xf9, 0x4c, 0x75, 0x8a, 0x99, 0x3e, 0xcf, 0xeb, 0xec, 0x0d,
	0x4d, 0xf8, 0x8f, 0xb9, 0x9e, 0x90, 0x74, 0xf2, 0xcf, 0x8f, 0x8c, 0x7d, 0x98, 0x9a, 0xba, 0xd7,
	0x53, 0xf8, 0x54, 0xda, 0x49, 0x7c, 0xaa, 0xbf, 0xd5, 0xa2, 0xa3, 0x6b, 0x74, 0x49, 0x95, 0x47,
	0xa1, 0xd8, 0x41, 0xaa, 0x90, 0x7b, 0x98, 0x2d, 0xe6, 0xdd, 0x49, 0x96, 0xe2, 0xf6, 0x2b, 0x75,
	0x98, 0x9d, 0x3c, 0xc9, 0x61, 0x56, 0x7f, 0x94, 0xb3, 0x1c, 0xaf, 0x47, 0x3e, 0x1d, 0xb3, 0x49,
	0x69, 0xde, 0xc9, 0xe8, 0xdc, 0x2a, 0xfd, 0xb5, 0x06, 0x35, 0xd9, 0xd3, 0x5f, 0x06, 0x7e, 0x59,
	0x8f, 0x97, 0x4e, 0xfd, 0x76, 0xdc, 0xc7, 0x39, 0x83, 0x55, 0x06, 0xd6, 0x08, 0xfc, 0x1b, 0x40,
	0xe4, 0x53, 0x84, 0xd9, 0xb7, 0x7c, 0xdb, 0x15, 0xa2, 0xd5, 0xa0, 0xd2, 0x70, 0x0c, 0x1e, 0x98,
	0xbd, 0x62, 0x64, 0xf6, 0x6e, 0xc1, 0xf9, 0x74, 0x7b, 0xf3, 0xd5, 0x0a, 0xd2, 0xaf, 0x6a, 0x90,
	0x64, 0x17, 0x4f, 0x56, 0x62, 0x32, 0x3c, 0x19, 0xdf, 0x6a, 0x9b, 0x98, 0xa7, 0x12, 0x91, 0x67,
	0xd7, 0xf2, 0x0e, 0x4f, 0xe2, 0x89, 0xb0, 0x23, 0x77, 0x46, 0x4f, 0x5e, 0x8f, 0xac, 0xc4, 0xa8,
	0x9c, 0x96, 0x48, 0x6c, 0xc5, 0x1a, 0x44, 0xd6, 0x9f, 0x79, 0x78, 0x7d, 0xda, 0xb5, 0xe4, 0x38,
	0x48, 0xd1, 0x00, 0x0e, 0x42, 0x01, 0x61, 0xa2, 0x35, 0xd8, 0xdf, 0x37, 0xf1, 0x26, 0x59, 0x50,
	0xa8, 0xca, 0x20, 0xbb, 0x0c, 0x40, 0x36, 0xa0, 0xe6, 0x5b, 0xde, 0xa1, 0xd9, 0xc6, 0xa3, 0x8f,
	0xb8, 0x31, 0xbe, 0x96, 0x3d, 0x34, 0x3f, 0x22, 0xf1, 0x50, 0x8e, 0x1f, 0x96, 0xc9, 0x65, 0x98,
	0xb6, 0x3d, 0xd3, 0xa1, 0x47, 0x26, 0xae, 0x54, 0xd0, 0x0f, 0x6c, 0xef, 0x21, 0x3d, 0xc2, 0xb6,
	0xcc, 0x1b, 0x0f, 0xab, 0x83, 0x84, 0x0c, 0x71, 0x9b, 0xe4, 0x08, 0x1c, 0x9e, 0x8b, 0xa1, 0x3f,
	0x86, 0x99, 0xd8, 0x4a, 0x23, 0x17, 0x46, 0x93, 0x5c, 0x18, 0x06, 0x8d, 0x84, 0xa2, 0x6a, 0xf0,
	0x02, 0x06, 0x52, 0xe8, 0x2b, 0xda, 0x09, 0xd3, 0x05, 0x58, 0x41, 0x3f, 0x02, 0x12, 0x76, 0x89,
	0xec, 0x1e, 0xbb, 0xdf, 0x4b, 0x50, 0xe3, 0xf2, 0xc4, 0xeb, 0x38, 0x1d, 0x01, 0x41, 0x46, 0x7c,
	0xe0, 0x92, 0x3c, 0x70, 0x17, 0xce, 0x2a, 0x48, 0x47, 0x08, 0x94, 0xd0, 0x09, 0xe0, 0xee, 0x01,
	0xfe, 0x26, 0x77, 0x01, 0xb8, 0x90, 0xa2, 0x0c, 0x14, 0x90, 0x11, 0x57, 0xb3, 0x19, 0x11, 0x2e,
	0xc3, 0xa8, 0x62, 0x33, 0x74, 0x45, 0xbe, 0x15, 0x49, 0x57, 0x68, 0x5b, 0x47, 0xc8, 0xbd, 0x68,
	0x42, 0xa5, 0x6d, 0xf9, 0x34, 0x94, 0xa0, 0x92, 0x11, 0x96, 0xa5, 0x88, 0x55, 0x51, 0x1d, 0xb1,
	0x2a, 0xc9, 0x11, 0xab, 0xe7, 0x98, 0x08, 0xc1, 0xef, 0xff, 0x15, 0xb3, 0xf0, 0x7a, 0xe4, 0x97,
	0xa0, 0x26, 0xee, 0x30, 0x3b, 0x27, 0x0c, 0x1b, 0xd2, 0x44, 0xb2, 0x45, 0x7a, 0x8c, 0xb5, 0xc8,
	0xe4, 0x74, 0x4e, 0x1c, 0x2d, 0x6e, 0xc3, 0xe5, 0x84, 0x79, 0xfd, 0x69, 0x2c, 0xe6, 0xab, 0xd0,
	0xdc, 0xa4, 0x3e, 0x0f, 0xee, 0xa5, 0xfb, 0xff, 0x02, 0x00, 0x86, 0xeb, 0xc6, 0xea, 0x5e, 0x8a,
	0xf0, 0x7d, 0x5b, 0xc3, 0xee, 0x91, 0x4c, 0x92, 0x21, 0xfc, 0x99, 0x8a, 0xc4, 0x57, 0x23, 0xd3,
	0x92, 0x9a, 0x02, 0xc6, 0x30, 0x81, 0xcf, 0x21, 0x57, 0xf5, 0xc5, 0xae, 0xfe, 0x0c, 0x6e, 0x49,
	0x71, 0x85, 0xfb, 0x18, 0xd2, 0x97, 0xb9, 0xa4, 0x1a, 0xe4, 0xf4, 0x7c, 0xe2, 0xa1, 0x58, 0xce,
	0xa7, 0x9f, 0xc2, 0x32, 0x7e, 0x52, 0x48, 0x30, 0xea, 0x01, 0xa6, 0x60, 0x05, 0x8c, 0x4a, 0xfa,
	0x50, 0xda, 0xb8, 0x3e, 0x54, 0x8e, 0x7f, 0x26, 0x73, 0xba, 0x98, 0xc9, 0xe9, 0x92, 0x9a, 0xd3,
	0x93, 0x72, 0x52, 0xd6, 0x16, 0x94, 0x70, 0x76, 0x53, 0x38, 0xbb, 0x3b, 0x69, 0xdd, 0x95, 0xb9,
	0xc0, 0xe5, 0xc7, 0x03, 0xda, 0x3f, 0xc6, 0x69, 0x63, 0x17, 0x71, 0x9f, 0xaf, 0x3c, 0xba, 0xcf,
	0x77, 0x07, 0xaa, 0x61, 0x57, 0xa4, 0x06, 0xe5, 0xbd, 0x87, 0x1f, 0x3c, 0x7c, 0xf4, 0xf4, 0x61,
	0x63, 0x82, 0xcc, 0x40, 0x75, 0xf5, 0xe1, 0xda, 0x97, 0x1e, 0x19, 0xe6, 0xd6, 0x7a, 0x43, 0x23,
	0xd3, 0x50, 0xd9, 0x5e, 0xdd, 0x5a, 0x37, 0xf7, 0xb6, 0xd6, 0x1b, 0x05, 0xfd, 0xfb, 0x5a, 0x42,
	0x48, 0xe5, 0xd9, 0xbd, 0x09, 0xf9, 0x49, 0xa9, 0xa4, 0xc2, 0x49, 0x54, 0xd2, 0x5f, 0x6a, 0x70,
	0x61, 0x87, 0x5a, 0xfd, 0xd6, 0x0b, 0x49, 0xc3, 0x8e, 0x90, 0x66, 0x3a, 0xde, 0x8e, 0x65, 0x50,
	0x0c, 0x73, 0x89, 0x68, 0x0e, 0x2f, 0x30, 0x3f, 0x87, 0x3a, 0x6d, 0x11, 0xac, 0x61, 0x3f, 0xe3,
	0xe9, 0x28, 0xe5, 0x44, 0x3a, 0x8a, 0x9c, 0xae, 0x50, 0x89, 0xa5, 0x2b, 0xe8, 0x5f, 0x87, 0x66,
	0xd6, 0x2a, 0x38, 0xad, 0xb9, 0x33, 0x32, 0x1e, 0xad, 0x79, 0x1b, 0x24, 0xd3, 0x8f, 0x43, 0x32,
	0x49, 0x46, 0xe2, 0xa3, 0x23, 0x53, 0x3c, 0x1b, 0xa4, 0x9c, 0xcc, 0x06, 0xc9, 0x21, 0x94, 0x19,
	0x10, 0x2a, 0xbd, 0x0e, 0xaf, 0x47, 0x56, 0x55, 0x84, 0x1a, 0x2e, 0x51, 0x32, 0xa5, 0xfe, 0x9c,
	0x1f, 0xef, 0x02, 0x33, 0xfa, 0xd8, 0x7d, 0x39, 0x84, 0x42, 0x17, 0x98, 0xb2, 0x38, 0x36, 0x25,
	0xb3, 0x50, 0x6e, 0x5b, 0xc7, 0xca, 0x48, 0xda, 0xcf, 0xec, 0x9c, 0xf7, 0x4f, 0x1a, 0x90, 0xe4,
	0x22, 0xbc, 0x5e, 0x6c, 0xaa, 0x5a, 0x7c, 0xaa, 0x52, 0xce, 0x77, 0x2a, 0xd3, 0xa3, 0x18, 0xcb,
	0xf4, 0xb8, 0x02, 0xd3, 0x41, 0xa6, 0x7c, 0x78, 0xfa, 0x2a, 0x19, 0x35, 0x01, 0xc3, 0xce, 0x2e,
	0x41, 0x50, 0x94, 0x82, 0xd8, 0x20, 0x40, 0xf7, 0xa8, 0x2a, 0xdb, 0x9e, 0x0b, 0x87, 0x3a, 0xdb,
	0x9e, 0xc9, 0x87, 0x86, 0xd9, 0xf6, 0xfa, 0x77, 0x35, 0x78, 0x3b, 0xe2, 0x7c, 0xc8, 0xc3, 0x11,
	0x2d, 0x78, 0x32, 0x09, 0xa9, 0x18, 0x17, 0xbb, 0xf1, 0x8c, 0xf8, 0x5f, 0x14, 0xe0, 0x52, 0xee,
	0x54, 0xbc, 0x1e, 0x79, 0xa6, 0xf4, 0xba, 0x3e, 0x93, 0x62, 0xe1, 0x90, 0x7e, 0x96, 0x3b, 0x18,
	0x66, 0x93, 0x15, 0x1f, 0x9b, 0x14, 0x3f, 0xbf, 0x70, 0xdb, 0xc5, 0x0b, 0xcd, 0x3f, 0xd4, 0x60,
	0x12, 0x91, 0x13, 0x6b, 0xd5, 0x92, 0x5b, 0x2c, 0xcd, 0xef, 0x77, 0x60, 0x56, 0xca, 0x0c, 0x97,
	0x2e, 0xde, 0x66, 0xbc, 0x30, 0x29, 0xfc, 0x5e, 0x1a, 0x8f, 0x31, 0xa8, 0x84, 0x41, 0x74, 0x09,
	0xef, 0xb1, 0xfb, 0x92, 0x5c, 0x83, 0xba, 0x14, 0x12, 0x97, 0x2e, 0x33, 0xc2, 0x48, 0xf8, 0x3d,
	0x4a, 0xf5, 0x27, 0x68, 0xe4, 0xa3, 0xf5, 0x6f, 0xb5, 0xbd, 0xbb, 0xc7, 0x18, 0x4c, 0x65, 0xbc,
	0xbc, 0x08, 0x20, 0xdd, 0x2e, 0x88, 0x1b, 0x6d, 0x2f, 0xbc, 0x59, 0xc8, 0xbe, 0xd1, 0xd6, 0x7f,
	0x11, 0xad, 0x97, 0xba, 0x5f, 0x7e, 0x3f, 0x99, 0x79, 0x87, 0xab, 0xff, 0x46, 0x15, 0x9a, 0xd1,
	0xe5, 0x1a, 0x67, 0x05, 0x0f, 0x14, 0x04, 0x94, 0xed, 0x5a, 0x9e, 0x4f, 0xfb, 0x66, 0x74, 0xc8,
	0xad, 0x72, 0xc8, 0x9e, 0xdd, 0x66, 0xf4, 0x11, 0xd5, 0x09, 0xf7, 0x62, 0x86, 0x83, 0xc5, 0x74,
	0xc8, 0x76, 0xcc, 0x3d, 0x2a, 0xa2, 0x64, 0xdc, 0x52, 0x4b, 0x86, 0x72, 0x1e, 0xfc, 0xdc, 0x23,
	0x79, 0x4c, 0xc8, 0x19, 0xdf, 0xf2, 0x65, 0x0e, 0x96, 0x04, 0x07, 0x7d, 0xcb, 0x8f, 0x38, 0x78,
	0x03, 0x48, 0xb0, 0xfb, 0x24, 0x54, 0xce, 0x9d, 0x86, 0xa8, 0x89, 0xb0, 0x19, 0xa9, 0xfa, 0xee,
	0x11, 0x3b, 0xe5, 0x51, 0x71, 0x3c, 0xad, 0x30, 0x80, 0x61, 0xf9, 0x34, 0x39, 0x64, 0xcb, 0x39,
	0x16, 0x59, 0x76, 0xd2, 0x90, 0x6b, 0xce, 0xb1, 0x62, 0x48, 0x86, 0x5a, 0xe1, 0x29, 0xa6, 0xb1,
	0x21, 0xd7, 0x9c, 0xe3, 0xe6, 0xf7, 0x0a, 0x30, 0xc9, 0x8f, 0xc8, 0x39, 0x9b, 0x79, 0x09, 0x1a,
	0xbc, 0xaa, 0x6d, 0x7b, 0xbd, 0x8e, 0x75, 0x1c, 0x11, 0xba, 0x8e, 0xf0, 0x75, 0x0e, 0xe6, 0xdb,
	0x9e, 0x63, 0xe2, 0x01, 0x53, 0x9c, 0xf7, 0x11, 0x82, 0x61, 0xe6, 0x27, 0x89, 0x4d, 0x5a, 0x46,
	0x56, 0xdc, 0x1e, 0x87, 0x15, 0x62, 0xd7, 0xc6, 0x77, 0xe8, 0x15, 0x10, 0x01, 0x25, 0xb6, 0x05,
	0xe8, 0x31, 0xde, 0xdf, 0x96, 0x8c, 0x1a, 0x87, 0x3d, 0x60, 0x20, 0xb6, 0x06, 0x19, 0x05, 0x89,
	0x02, 0x3c, 0x02, 0x20, 0xa1, 0x31, 0x92, 0xfc, 0x77, 0x11, 0xca, 0x62, 0x94, 0x61, 0x5b, 0x9b,
	0xd1, 0x5a, 0x54, 0xa7, 0x48, 0xd3, 0x10, 0x35, 0x11, 0x71, 0xae, 0x44, 0xab, 0x97, 0xc8, 0x13,
	0x2c, 0x04, 0x09, 0x74, 0x29, 0x4c, 0x34, 0x32, 0x7d, 0xeb, 0x40, 0x84, 0x8b, 0x82, 0x29, 0xec,
	0x5a, 0x07, 0x2a, 0xc1, 0x9b, 0x1c, 0x5d, 0xf0, 0xa6, 0x46, 0x11, 0xbc, 0x72, 0x42, 0xf0, 0x2e,
	0x81, 0x20, 0x24, 0xaf, 0xe6, 0x92, 0x04, 0x1c, 0x84, 0x08, 0x6f, 0x92, 0xfa, 0x2a, 0x31, 0xaf,
	0x8d, 0x2e, 0xe6, 0xd3, 0x6a, 0x31, 0x67, 0xbd, 0x06, 0x74, 0x7d, 0x65, 0xd3, 0x23, 0x53, 0x64,
	0x8a, 0x57, 0x8d, 0x19, 0x01, 0x7e, 0x62, 0xd3, 0xa3, 0xad, 0xb6, 0xfe, 0x1f, 0x1a, 0x9e, 0xbb,
	0xd5, 0xf2, 0x37, 0x9a, 0xd9, 0x3b, 0x99, 0x16, 0x95, 0xd7, 0x93, 0xb8, 0xe5, 0x9d, 0x09, 0xd7,
	0x13, 0x5d, 0xf4, 0x2e, 0x41, 0x00, 0x8b, 0xdf, 0xf5, 0xce, 0x18, 0x81, 0xb5, 0x17, 0xd7, 0xbd,
	0x09, 0x15, 0x5a, 0x4e, 0xa8, 0x50, 0xfd, 0x07, 0x1a, 0x5c, 0x19, 0xb2, 0x60, 0xbc, 0x1d, 0x91,
	0x63, 0x83, 0xef, 0x8e, 0xb1, 0x5f, 0x45, 0xa0, 0x10, 0xaf, 0xe2, 0xc5, 0x03, 0x94, 0xe0, 0x44,
	0x1f, 0xbc, 0x3f, 0x21, 0x7a, 0xf0, 0xfc, 0x64, 0x9f, 0x52, 0xe4, 0xa2, 0xd8, 0x18, 0x01, 0xc2,
	0x9a, 0x73, 0xac, 0xb7, 0x23, 0xe3, 0xc5, 0x3b, 0xdf, 0x0c, 0x74, 0xb1, 0x78, 0x19, 0x72, 0x48,
	0x8f, 0x8f, 0xdc, 0x7e, 0xc8, 0x10, 0x51, 0x1c, 0xef, 0x61, 0x8f, 0xfe, 0x2f, 0x85, 0xc8, 0x96,
	0xa5, 0x86, 0xf1, 0x7a, 0xe4, 0xb1, 0xe2, 0x9c, 0xbd, 0xa2, 0x3a, 0x69, 0x66, 0xf5, 0x90, 0xb6,
	0x24, 0x6a, 0xe7, 0xe2, 0xef, 0xb5, 0x40, 0x2d, 0x0f, 0x31, 0x81, 0x39, 0x47, 0x6b, 0x95, 0xd6,
	0x2e, 0x8e, 0xa0, 0xb5, 0x4b, 0x49, 0xad, 0x2d, 0x29, 0x25, 0x67, 0xd0, 0x0d, 0xa2, 0xab, 0x81,
	0xda, 0x1a, 0x74, 0xc9, 0xd5, 0xe4, 0x35, 0x00, 0x4f, 0x2f, 0x8b, 0x07, 0xf9, 0xbf, 0x8c, 0x31,
	0x0c, 0x89, 0x30, 0x6b, 0x91, 0x02, 0x1f, 0xd3, 0x99, 0x8c, 0x6b, 0x61, 0xe6, 0x1e, 0x5e, 0xcc,
	0xe9, 0x7a, 0x0c, 0xe7, 0x30, 0xb7, 0x17, 0xa5, 0xe9, 0x69, 0xfe, 0x58, 0xcb, 0xb2, 0x16, 0x85,
	0xd1, 0xac, 0x45, 0x71, 0x44, 0x6b, 0x51, 0x4a, 0x5b, 0x8b, 0xab, 0xaa, 0xeb, 0x97, 0x04, 0xdd,
	0x55, 0xaa, 0x6f, 0x4a, 0xa5, 0xfa, 0xbe, 0x8c, 0xef, 0xae, 0x92, 0x82, 0x3b, 0x84, 0x33, 0xa3,
	0xcc, 0x40, 0xbc, 0xce, 0x4a, 0xf7, 0xec, 0xf5, 0xf4, 0xaf, 0xc3, 0xc2, 0x8e, 0x8a, 0xe6, 0xc2,
	0x23, 0xcd, 0xb3, 0xbd, 0x23, 0x0d, 0xbd, 0xc8, 0xce, 0xe2, 0xca, 0xfe, 0xbd, 0x9e, 0xbe, 0x03,
	0x73, 0x31, 0xd5, 0x17, 0x9d, 0xd2, 0x73, 0xf3, 0x0e, 0xdf, 0x82, 0xea, 0x31, 0xb5, 0xfa, 0xe8,
	0x6c, 0x07, 0x4c, 0x0e, 0x01, 0xfa, 0xbf, 0x4f, 0x46, 0x6f, 0x6a, 0x63, 0xbd, 0xf2, 0x67, 0x78,
	0x0c, 0x91, 0xfb, 0xe9, 0xc1, 0x8a, 0x18, 0x04, 0x7d, 0x74, 0xcc, 0xa4, 0x8b, 0xf2, 0x70, 0x22,
	0x4d, 0x39, 0x1d, 0x26, 0xe1, 0x30, 0x6d, 0x99, 0x76, 0xf6, 0x8b, 0x69, 0x67, 0x5f, 0x9d, 0xd3,
	0x53, 0x52, 0xe7, 0xf4, 0xdc, 0x82, 0xf3, 0x29, 0x5c, 0xd4, 0xc3, 0x93, 0xfc, 0xb6, 0x2a, 0x81,
	0xcf, 0xec, 0xe9, 0x55, 0x98, 0xe9, 0xf5, 0x29, 0x46, 0x0e, 0xb8, 0xcd, 0x17, 0xd9, 0x7c, 0x02,
	0xc8, 0x8d, 0xfe, 0x75, 0x38, 0x13, 0x43, 0x92, 0x7c, 0xd6, 0x59, 0x19, 0x91, 0x75, 0x38, 0x07,
	0x53, 0x7d, 0xda, 0xb5, 0xfa, 0x87, 0xc2, 0xbf, 0x10, 0xa5, 0xb8, 0x67, 0x52, 0x4d, 0x78, 0x26,
	0x4f, 0x13, 0xdb, 0x1a, 0x70, 0x5b, 0xbf, 0x9f, 0xb9, 0xad, 0x13, 0xfc, 0x08, 0x36, 0x74, 0xea,
	0xc4, 0xd7, 0xfc, 0xfd, 0x02, 0xd4, 0xa4, 0xca, 0x61, 0xa2, 0x38, 0xd4, 0x6b, 0x4b, 0x73, 0x76,
	0x72, 0x24, 0xce, 0x4e, 0x29, 0x38, 0xfb, 0x49, 0x98, 0xf5, 0xc2, 0x3c, 0x50, 0xd9, 0x63, 0xab,
	0x47, 0x60, 0xa4, 0x8e, 0x52, 0x04, 0x2a, 0x6a, 0x11, 0x90, 0xb7, 0x78, 0x35, 0xf5, 0xec, 0x2b,
	0xe2, 0x00, 0xc4, 0x39, 0xa0, 0x3f, 0xc0, 0xdb, 0x10, 0x39, 0x00, 0x7e, 0xaa, 0xa4, 0xc8, 0x3f,
	0x2d, 0xe4, 0xf7, 0xf7, 0xff, 0x2b, 0x37, 0x92, 0x6c, 0xc3, 0xb9, 0xa0, 0x9b, 0x58, 0xd2, 0xea,
	0xe4, 0x48, 0x9d, 0x11, 0xd1, 0x56, 0xf2, 0xbb, 0xf4, 0xcf, 0x46, 0x81, 0x7c, 0x99, 0x88, 0xdb,
	0xb4, 0x3f, 0xe4, 0x53, 0x00, 0x9f, 0x89, 0x42, 0xd0, 0xa9, 0x86, 0x5e, 0x8f, 0xcc, 0x43, 0xd9,
	0xf6, 0x4c, 0xef, 0x85, 0x7b, 0x24, 0x9e, 0xca, 0x4e, 0xd9, 0xde, 0xce, 0x0b, 0xf7, 0x48, 0xff,
	0x0a, 0x9a, 0x5e, 0xb9, 0x49, 0x22, 0x17, 0x61, 0x58, 0x1a, 0x3d, 0x97, 0x4e, 0xd9, 0x5d, 0x46,
	0x08, 0x46, 0x16, 0xbe, 0x9e, 0x92, 0x86, 0x58, 0xe7, 0x5e, 0x8f, 0x7c, 0x0e, 0x4a, 0xf8, 0xd6,
	0x98, 0xa7, 0x51, 0x2c, 0xa5, 0x28, 0x96, 0x6a, 0x2b, 0x04, 0x09, 0x5b, 0xe9, 0xff, 0xa5, 0xc1,
	0x7c, 0x06, 0x46, 0x62, 0x6a, 0x5a, 0x62, 0x6a, 0xe4, 0x26, 0x9c, 0xc3, 0x87, 0x51, 0x02, 0x27,
	0xe1, 0xdb, 0x9e, 0x39, 0x08, 0xae, 0x82, 0x76, 0x03, 0x27, 0xf7, 0x0e, 0xcc, 0x4b, 0x0d, 0x62,
	0xec, 0xe6, 0xfa, 0xfb, 0x5c, 0xd8, 0x46, 0xce, 0x5c, 0x78, 0x02, 0x8d, 0x20, 0xca, 0xed, 0xec,
	0x8b, 0xdb, 0x59, 0x7e, 0x4d, 0x7e, 0x63, 0xf8, 0x62, 0xa5, 0x08, 0x75, 0xdd, 0x0a, 0x7f, 0x63,
	0xec, 0xf5, 0x0f, 0x34, 0x58, 0xcc, 0xc1, 0x67, 0xcb, 0x17, 0xe3, 0x4a, 0x6e, 0x27, 0x87, 0x30,
	0xb7, 0xf3, 0x1a, 0xd4, 0xa5, 0xd5, 0x48, 0xa6, 0x2a, 0x5c, 0x04, 0x5b, 0x33, 0xf3, 0x40, 0xad,
	0x2e, 0x95, 0xb2, 0x20, 0xa8, 0xc8, 0x02, 0xc5, 0xd6, 0x61, 0x02, 0x04, 0xba, 0x98, 0x12, 0xa6,
	0x30, 0x54, 0x10, 0x21, 0x89, 0x1c, 0x08, 0x7e, 0x03, 0x76, 0x2a, 0x15, 0xf3, 0xa3, 0x42, 0x56,
	0x4f, 0x1f, 0x2b, 0x97, 0x21, 0xca, 0x65, 0x03, 0xfd, 0x25, 0x4e, 0xb9, 0x53, 0x64, 0x8e, 0x7d,
	0x55, 0xdd, 0xcd, 0x1b, 0xca, 0x19, 0xfb, 0xa1, 0x06, 0xe7, 0xa5, 0xd7, 0x2a, 0xd2, 0x67, 0x28,
	0xf2, 0x9f, 0x36, 0xf3, 0x9b, 0xee, 0x41, 0xf4, 0xb4, 0x19, 0x01, 0x4c, 0xd6, 0x55, 0xef, 0x39,
	0xe3, 0x8f, 0x1e, 0x4b, 0x89, 0x47, 0x8f, 0xcc, 0x3d, 0x12, 0xd5, 0xb1, 0xcc, 0xe4, 0x69, 0x0e,
	0x14, 0x33, 0xfd, 0x75, 0x1e, 0x6b, 0x90, 0x26, 0x3b, 0xde, 0x25, 0x79, 0xbe, 0xf2, 0x1c, 0x33,
	0xc4, 0xfe, 0x0d, 0x0c, 0x03, 0xe4, 0xcd, 0x05, 0xd3, 0xd7, 0xd2, 0x09, 0x01, 0xef, 0xa4, 0xd3,
	0xb1, 0x54, 0xd4, 0x97, 0xf3, 0x02, 0xfe, 0x8c, 0x7f, 0xb0, 0x42, 0xc2, 0x1b, 0x27, 0x5d, 0xe4,
	0xe4, 0x21, 0x96, 0x18, 0x97, 0x4b, 0x09, 0x2e, 0x47, 0xe4, 0x9a, 0x54, 0x93, 0x6b, 0x4a, 0x26,
	0xd7, 0x4f, 0x34, 0x7c, 0x98, 0x93, 0xbd, 0x84, 0x37, 0x46, 0xad, 0xc4, 0x7a, 0x8b, 0x79, 0xeb,
	0x2d, 0xc5, 0xd7, 0xbb, 0x04, 0x0d, 0x6e, 0x98, 0x24, 0x51, 0x9d, 0x14, 0x8f, 0x15, 0x18, 0x7c,
	0x23, 0x94, 0xd7, 0x65, 0x38, 0x1b, 0xc3, 0x8c, 0xdd, 0x14, 0x9d, 0x91, 0x90, 0xf9, 0x14, 0xaf,
	0x53, 0xa8, 0x04, 0x57, 0x61, 0xa4, 0x01, 0xd3, 0xe2, 0x9e, 0xdb, 0xdc, 0x7c, 0x74, 0x7f, 0xbd,
	0x31, 0x41, 0xea, 0x00, 0xab, 0x0f, 0xf6, 0x76, 0x36, 0x78, 0xb9, 0xc4, 0xca, 0xcf, 0xf6, 0x9e,
	0x6d, 0x89, 0xfa, 0x32, 0x99, 0x03, 0xb2, 0xf5, 0x70, 0x77, 0xc3, 0x58, 0x5d, 0xdb, 0x35, 0x37,
	0x57, 0x1f, 0x08, 0x3c, 0x4a, 0x66, 0xa1, 0xb6, 0xb1, 0xb3, 0xfd, 0xc8, 0xd8, 0xe5, 0x80, 0xfd,
	0xeb, 0xbf, 0xad, 0x01, 0x44, 0xdf, 0x90, 0x60, 0xf5, 0x7b, 0xce, 0xa1, 0xe3, 0x1e, 0x39, 0xac,
	0xd8, 0x98, 0x20, 0xe7, 0xe1, 0x4c, 0x54, 0xbd, 0xcd, 0x1f, 0x40, 0x37, 0x34, 0x72, 0x0e, 0x1a,
	0x11, 0xf8, 0x03, 0x7c, 0xf7, 0xdc, 0x28, 0xb1, 0x51, 0x23, 0xe8, 0x53, 0xf1, 0xd8, 0xb9, 0x31,
	0x49, 0x9a, 0x30, 0x17, 0xc1, 0x65, 0x93, 0xd8, 0x98, 0x8a, 0xf7, 0xc4, 0x39, 0xd5, 0x28, 0x5f,
	0xff, 0x62, 0x78, 0x38, 0x08, 0x08, 0xb0, 0xc6, 0xdb, 0xe2, 0x63, 0x04, 0x4e, 0x00, 0x84, 0xe0,
	0xa1, 0x83, 0x13, 0x00, 0xcb, 0x78, 0xe3, 0xdc, 0x28, 0x5f, 0xff, 0x2c, 0xd4, 0xe3, 0xef, 0xf3,
	0x08, 0xc0, 0xd4, 0xdd, 0x67, 0xe6, 0xfa, 0xea, 0xb3, 0xc6, 0x04, 0xa9, 0x41, 0xf9, 0xee, 0x33,
	0xf3, 0xe9, 0xc6, 0xc6, 0x07, 0x3c, 0x53, 0xe0, 0xee, 0x33, 0xf3, 0xc1, 0xa3, 0x87, 0xbb, 0x5f,
	0x6a, 0x14, 0xae, 0x9b, 0x30, 0x9b, 0xc8, 0xa4, 0x64, 0xcb, 0x4a, 0x80, 0x56, 0xef, 0xdf, 0x6f,
	0x4c, 0x90, 0x79, 0x38, 0x9b, 0x80, 0x3f, 0xb5, 0x6c, 0x46, 0x9d, 0x45, 0x98, 0x4f, 0x54, 0xdc,
	0xb3, 0x1d, 0xdb, 0x7b, 0x41, 0xdb, 0x8d, 0xc2, 0xf5, 0x1d, 0xa8, 0x86, 0xe9, 0xf5, 0x84, 0x40,
	0x7d, 0x7d, 0xf5, 0x99, 0x69, 0xac, 0x3e, 0xdc, 0xdc, 0x30, 0x77, 0x9f, 0x6d, 0x6f, 0x34, 0x26,
	0x18, 0x45, 0x70, 0x32, 0x32, 0x54, 0x23, 0x6f, 0xc1, 0x7c, 0x1c, 0xd3, 0xbc, 0xf7, 0xc8, 0x30,
	0x77, 0x37, 0x76, 0x76, 0x1b, 0xbf, 0xf5, 0x70, 0xe5, 0x7f, 0x3e, 0x0b, 0x75, 0x26, 0x2e, 0x6b,
	0xa1, 0xd4, 0x93, 0x5d, 0x74, 0x1b, 0xc3, 0x2f, 0xb5, 0x08, 0xde, 0xe1, 0x07, 0x3f, 0xd6, 0x30,
	0xed, 0xfc, 0xc2, 0x72, 0xf8, 0x19, 0x90, 0x27, 0x2b, 0xf8, 0x9a, 0x31, 0xb8, 0xa9, 0x6a, 0xce,
	0xc5, 0xaa, 0x10, 0x1d, 0x8f, 0xf3, 0x13, 0xe4, 0x09, 0x86, 0x98, 0x94, 0xbd, 0xe2, 0x56, 0xca,
	0xe9, 0x34, 0x5e, 0xf5, 0x88, 0x7f, 0xa3, 0xc5, 0x8b, 0xfa, 0x35, 0x68, 0xaf, 0x63, 0xb5, 0xa8,
	0xb2, 0x6f, 0xf2, 0x56, 0xac, 0xb1, 0x40, 0xc5, 0xaa, 0xf4, 0x7c, 0x37, 0xba, 0x3d, 0xff, 0x58,
	0xf4, 0xbb, 0x13, 0x9f, 0x2f, 0x17, 0xd5, 0xd3, 0x12, 0x61, 0x0f, 0x3d, 0x79, 0x55, 0xa7, 0xa7,
	0xa2, 0xc1, 0x1e, 0x2c, 0x26, 0x69, 0x20, 0x75, 0x7d, 0x62, 0x12, 0xec, 0x46, 0xa1, 0xbb, 0x37,
	0x2e, 0x08, 0xca, 0x5e, 0xdf, 0x90, 0x20, 0x28, 0xfb, 0x3e, 0x31, 0x15, 0xf6, 0xa4, 0xa4, 0x42,
	0xb7, 0xd3, 0x0e, 0xb4, 0xd3, 0x69, 0xc9, 0xf0, 0x34, 0x4e, 0xdc, 0x58, 0xb7, 0xa7, 0xa2, 0xc3,
	0x53, 0xb8, 0x98, 0xa4, 0x43, 0xac, 0xf3, 0x13, 0x13, 0xe2, 0x2b, 0x70, 0x2d, 0x71, 0xe2, 0x7b,
	0x83, 0x52, 0xf1, 0x35, 0x7c, 0x59, 0x9e, 0xd7, 0xf9, 0xa9, 0x88, 0xf2, 0x35, 0xb8, 0x2a, 0x16,
	0x9a, 0x37, 0xc4, 0x89, 0x49, 0xf3, 0x10, 0xe3, 0x8a, 0xdc, 0x08, 0x85, 0xdf, 0x9e, 0x3a, 0x05,
	0x35, 0xb6, 0x25, 0x37, 0x3c, 0xec, 0xef, 0x54, 0x04, 0xd8, 0x86, 0x39, 0xb1, 0x9c, 0x44, 0xaf,
	0x27, 0x5e, 0xf3, 0x71, 0x94, 0x51, 0x9c, 0xfc, 0x36, 0x06, 0xb9, 0x91, 0x99, 0xe1, 0xa7, 0xf8,
	0xcc, 0x48, 0xf3, 0xbd, 0x31, 0xb0, 0x71, 0xe8, 0x5f, 0xc1, 0x6f, 0x64, 0xaa, 0xbf, 0x09, 0x41,
	0x72, 0x7a, 0x53, 0x7c, 0x60, 0xa3, 0xb9, 0x3c, 0x0e, 0x3a, 0x8e, 0xfe, 0x9b, 0x9a, 0xf4, 0xf0,
	0x5b, 0xfd, 0x15, 0x04, 0x72, 0x3b, 0xbb, 0xd7, 0xcc, 0xcf, 0x4b, 0x34, 0xdf, 0x1f, 0xbf, 0x51,
	0x8a, 0x13, 0x89, 0x8f, 0x01, 0xe4, 0x71, 0x22, 0xfd, 0x5d, 0x85, 0x3c, 0x4e, 0x28, 0xbe, 0x32,
	0xa0, 0x4f, 0x90, 0xef, 0x68, 0xd2, 0x87, 0x08, 0x14, 0x8f, 0xe0, 0xc9, 0xa7, 0xb3, 0x7b, 0x54,
	0x7f, 0x57, 0xa0, 0x39, 0x66, 0x0b, 0x9c, 0xc6, 0xaf, 0x69, 0xb0, 0x98, 0x93, 0x3c, 0x44, 0x6e,
	0x8e, 0x97, 0x6a, 0xa4, 0x9a, 0xc4, 0x90, 0xdc, 0x24, 0x7d, 0x82, 0x7c, 0x08, 0xf5, 0xf8, 0x83,
	0x42, 0xa2, 0xab, 0x96, 0x12, 0x7f, 0x59, 0xd9, 0x1c, 0x8a, 0x83, 0x7d, 0xef, 0x63, 0x22, 0x5b,
	0xe2, 0x4d, 0x28, 0x79, 0x47, 0xd5, 0x36, 0xfd, 0x24, 0xb6, 0x39, 0x12, 0x9e, 0xd0, 0xf1, 0xf5,
	0x78, 0xc2, 0x9c, 0x7a, 0x0d, 0xf1, 0xb4, 0xc0, 0xe6, 0xd5, 0xa1, 0x38, 0xd8, 0xf9, 0x2b, 0xd4,
	0x92, 0xaa, 0x54, 0x5a, 0xf2, 0xee, 0x18, 0x29, 0xc1, 0xcd, 0x1b, 0xa3, 0x23, 0x0b, 0x3f, 0x66,
	0x5a, 0x7e, 0x52, 0x48, 0x2e, 0xab, 0xda, 0xcb, 0x4f, 0x25, 0x9b, 0x43, 0x30, 0x24, 0x76, 0xcb,
	0x81, 0x7a, 0x25, 0xa9, 0xe2, 0x0f, 0xe4, 0x9a, 0x43, 0x71, 0xb0, 0x6f, 0x17, 0x9f, 0x1a, 0xa6,
	0xbf, 0x7e, 0x4b, 0x3e, 0x95, 0x79, 0x7f, 0x92, 0xfc, 0xb8, 0x6e, 0x73, 0x54, 0x54, 0x1c, 0xb0,
	0x03, 0xe7, 0x54, 0x1f, 0x9b, 0x25, 0x4b, 0x99, 0x9d, 0x24, 0x02, 0x7d, 0xcd, 0x11, 0x31, 0x71,
	0xb4, 0x6f, 0x6b, 0x89, 0xb4, 0xb4, 0xf8, 0x57, 0x31, 0x15, 0xdb, 0x35, 0xff, 0xeb, 0xa1, 0xcd,
	0xf1, 0x1a, 0xe0, 0x1c, 0x7e, 0x39, 0x91, 0x71, 0x17, 0x8b, 0x11, 0xa8, 0xe9, 0xac, 0x7c, 0x3d,
	0x33, 0x6c, 0xec, 0x54, 0xec, 0x41, 0x9f, 0x20, 0xdf, 0x94, 0x5c, 0x60, 0xc5, 0xeb, 0x87, 0x61,
	0xbb, 0x21, 0x3e, 0xfe, 0x8d, 0xd1, 0x91, 0x71, 0x70, 0x0f, 0xe6, 0xd4, 0xa9, 0xc3, 0xe4, 0x7a,
	0xfa, 0x23, 0x91, 0x59, 0xb9, 0xd2, 0xcd, 0x77, 0x47, 0xc6, 0xc5, 0x41, 0xfb, 0xd1, 0x75, 0x6e,
	0xfc, 0xab, 0xd6, 0x8a, 0x41, 0x33, 0x3f, 0xb1, 0xdd, 0x1c, 0x19, 0x17, 0xc7, 0xfc, 0x06, 0x9c,
	0x55, 0x5c, 0x2e, 0x92, 0x4f, 0x8e, 0x76, 0x05, 0x99, 0x27, 0xd1, 0x89, 0xbb, 0xca, 0x70, 0xff,
	0xa4, 0x3e, 0x18, 0xa2, 0xde, 0x3f, 0xaa, 0x0f, 0x94, 0x34, 0x47, 0xc4, 0x94, 0xac, 0x41, 0xe2,
	0x25, 0xb2, 0xda, 0x1a, 0xa4, 0x1f, 0x3d, 0x37, 0x47, 0xc2, 0xc3, 0x71, 0x5e, 0x27, 0x9e, 0x24,
	0xca, 0x97, 0x1f, 0xd9, 0x62, 0xa7, 0x78, 0xf5, 0xda, 0x1c, 0x03, 0x5b, 0x52, 0x80, 0xe9, 0xc7,
	0x90, 0x39, 0x1b, 0x33, 0xf9, 0xfc, 0xb2, 0x39, 0x2a, 0xaa, 0xa4, 0x0e, 0x32, 0x9e, 0xa7, 0x9d,
	0x5a, 0x1d, 0xe4, 0x3c, 0x7b, 0x0b, 0xd5, 0x41, 0x84, 0xf3, 0xd1, 0xa8, 0x83, 0xe4, 0x93, 0x8b,
	0x4c, 0x75, 0xa0, 0x78, 0x61, 0x92, 0xa9, 0x0e, 0x54, 0xef, 0x38, 0x22, 0x03, 0x90, 0x75, 0x97,
	0xac, 0x36, 0x00, 0x39, 0x37, 0xd9, 0xcd, 0xf1, 0x1a, 0xe0, 0x1c, 0x7e, 0x35, 0xf5, 0x48, 0xfe,
	0xc4, 0x3c, 0xbf, 0x35, 0x6c, 0x74, 0x15, 0xd7, 0xbf, 0xa3, 0xa5, 0x6e, 0x50, 0x4f, 0xc5, 0xf8,
	0xdb, 0xc3, 0x26, 0xa1, 0xe6, 0xff, 0x37, 0xf1, 0x2c, 0xa5, 0xfe, 0x56, 0x80, 0xfa, 0x2c, 0x95,
	0xf9, 0x91, 0x82, 0xe6, 0x38, 0xe8, 0xd2, 0x36, 0x4f, 0xdf, 0xf7, 0xa9, 0x89, 0xaf, 0xbc, 0x61,
	0x6c, 0x8e, 0x8a, 0x2a, 0xd9, 0x21, 0xc5, 0xa3, 0xca, 0x71, 0xd8, 0xfd, 0x6e, 0xf6, 0x88, 0x2a,
	0x46, 0xbf, 0x96, 0x2e, 0x35, 0x4f, 0xc5, 0xe1, 0xf7, 0xb2, 0xc7, 0x55, 0xf3, 0xd6, 0x46, 0x0b,
	0x98, 0xbc, 0xcd, 0x53, 0x5b, 0x40, 0xc5, 0xd5, 0x61, 0x73, 0x34, 0x44, 0x1c, 0xea, 0xbb, 0x1a,
	0xc6, 0xb3, 0xb2, 0x2f, 0xa9, 0xc8, 0xad, 0xec, 0xce, 0x32, 0x2e, 0xd8, 0x9a, 0x2b, 0xe3, 0x36,
	0x09, 0x0f, 0x83, 0x39, 0xf7, 0x3f, 0x6a, 0xe5, 0x92, 0x73, 0xe1, 0xa5, 0x3e, 0x91, 0xe6, 0x5d,
	0x2f, 0x85, 0x67, 0x1d, 0xd5, 0xc3, 0x0b, 0x35, 0xcf, 0x33, 0x9e, 0x7e, 0xa8, 0xd5, 0x79, 0xd6,
	0x7b, 0x8e, 0x88, 0x0f, 0xd9, 0x39, 0xc3, 0x6a, 0x3e, 0xe4, 0x26, 0x55, 0x37, 0x57, 0xc6, 0x6d,
	0x92, 0x24, 0x41, 0x22, 0xdb, 0x36, 0x87, 0x04, 0xe9, 0x04, 0xe2, 0x1c, 0x12, 0x28, 0x92, 0x78,
	0xc3, 0xe8, 0x90, 0x3a, 0x57, 0x54, 0xad, 0xd1, 0x32, 0x13, 0x5f, 0xd5, 0xd1, 0xa1, 0xec, 0x34,
	0x54, 0xee, 0x75, 0x2a, 0x12, 0x2a, 0x15, 0x7b, 0x4e, 0x9d, 0xd0, 0xa9, 0xf0, 0x03, 0xb3, 0xf2,
	0x33, 0x27, 0x48, 0x0f, 0xce, 0x2b, 0x33, 0x28, 0x15, 0xca, 0x2c, 0x2b, 0x93, 0xb3, 0x79, 0x7d,
	0x54, 0xd4, 0xe4, 0x11, 0x3e, 0x91, 0x8a, 0x94, 0xa3, 0xca, 0xd2, 0xd9, 0x4e, 0x39, 0x5e, 0x8a,
	0x22, 0xc3, 0x49, 0x9f, 0x20, 0xdf, 0x42, 0xf7, 0x2c, 0x23, 0xdb, 0x88, 0x2c, 0x0f, 0xb3, 0x3b,
	0x09, 0x6f, 0xf4, 0xe6, 0x58, 0xf8, 0x12, 0x5b, 0x93, 0xff, 0x62, 0xa0, 0x66, 0xab, 0xe2, 0xff,
	0x11, 0xd4, 0x6c, 0x55, 0xfe, 0x29, 0x82, 0x38, 0xb8, 0xa4, 0xfe, 0xe1, 0x40, 0xa9, 0xb6, 0x55,
	0x7f, 0x9e, 0xa0, 0x3c, 0x4a, 0xa8, 0xff, 0x30, 0x61, 0xe2, 0xee, 0xed, 0x0f, 0x6f, 0x1d, 0xb8,
	0x1d, 0xcb, 0x39, 0x58, 0xbe, 0xb3, 0xe2, 0xfb, 0xcb, 0x2d, 0xb7, 0x7b, 0x13, 0xff, 0xeb, 0xa8,
	0xe5, 0x76, 0x6e, 0x7a, 0xb4, 0xff, 0xca, 0x6e, 0x51, 0x2f, 0xf9, 0xff, 0x49, 0xcf, 0xa7, 0x10,
	0xe5, 0xf6, 0xff, 0x05, 0x00, 0x00, 0xff, 0xff, 0xf9, 0x24, 0xbf, 0xb9, 0x69, 0x69, 0x00, 0x00,
}
