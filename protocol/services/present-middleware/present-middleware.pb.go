// Code generated by protoc-gen-go. DO NOT EDIT.
// source: tt/quicksilver/present-middleware/present-middleware.proto

package present_middleware // import "golang.52tt.com/protocol/services/present-middleware"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// 送礼来源类型
type PresentSendSourceType int32

const (
	PresentSendSourceType_E_SEND_SOURCE_DEFLAUTE         PresentSendSourceType = 0
	PresentSendSourceType_E_SEND_SOURCE_GIFT_TURNTABLE   PresentSendSourceType = 1
	PresentSendSourceType_E_SEND_SOURCE_GIFT_SHELF       PresentSendSourceType = 2
	PresentSendSourceType_E_SEND_SOURCE_SPEECH_BALL      PresentSendSourceType = 3
	PresentSendSourceType_E_SEND_SOURCE_DRAW_GIFT        PresentSendSourceType = 4
	PresentSendSourceType_E_SEND_SOURCE_MASKED_CALL      PresentSendSourceType = 5
	PresentSendSourceType_E_SEND_SOURCE_IM               PresentSendSourceType = 6
	PresentSendSourceType_E_SEND_SOURCE_OFFICIAL_CHANNEL PresentSendSourceType = 7
	PresentSendSourceType_E_SEND_SOURCE_FELLOW           PresentSendSourceType = 8
	PresentSendSourceType_E_SEND_SOURCE_MAGIC            PresentSendSourceType = 9
	PresentSendSourceType_E_SEND_SOURCE_LOTTERY          PresentSendSourceType = 10
	PresentSendSourceType_E_SEND_SOURCE_OFFERING_ROOM    PresentSendSourceType = 11
	PresentSendSourceType_E_SEND_SOURCE_CHANNEL_GIFT_PK  PresentSendSourceType = 12
	PresentSendSourceType_E_SEND_SOURCE_GRAB_CHAIR_GAME  PresentSendSourceType = 13
)

var PresentSendSourceType_name = map[int32]string{
	0:  "E_SEND_SOURCE_DEFLAUTE",
	1:  "E_SEND_SOURCE_GIFT_TURNTABLE",
	2:  "E_SEND_SOURCE_GIFT_SHELF",
	3:  "E_SEND_SOURCE_SPEECH_BALL",
	4:  "E_SEND_SOURCE_DRAW_GIFT",
	5:  "E_SEND_SOURCE_MASKED_CALL",
	6:  "E_SEND_SOURCE_IM",
	7:  "E_SEND_SOURCE_OFFICIAL_CHANNEL",
	8:  "E_SEND_SOURCE_FELLOW",
	9:  "E_SEND_SOURCE_MAGIC",
	10: "E_SEND_SOURCE_LOTTERY",
	11: "E_SEND_SOURCE_OFFERING_ROOM",
	12: "E_SEND_SOURCE_CHANNEL_GIFT_PK",
	13: "E_SEND_SOURCE_GRAB_CHAIR_GAME",
}
var PresentSendSourceType_value = map[string]int32{
	"E_SEND_SOURCE_DEFLAUTE":         0,
	"E_SEND_SOURCE_GIFT_TURNTABLE":   1,
	"E_SEND_SOURCE_GIFT_SHELF":       2,
	"E_SEND_SOURCE_SPEECH_BALL":      3,
	"E_SEND_SOURCE_DRAW_GIFT":        4,
	"E_SEND_SOURCE_MASKED_CALL":      5,
	"E_SEND_SOURCE_IM":               6,
	"E_SEND_SOURCE_OFFICIAL_CHANNEL": 7,
	"E_SEND_SOURCE_FELLOW":           8,
	"E_SEND_SOURCE_MAGIC":            9,
	"E_SEND_SOURCE_LOTTERY":          10,
	"E_SEND_SOURCE_OFFERING_ROOM":    11,
	"E_SEND_SOURCE_CHANNEL_GIFT_PK":  12,
	"E_SEND_SOURCE_GRAB_CHAIR_GAME":  13,
}

func (x PresentSendSourceType) String() string {
	return proto.EnumName(PresentSendSourceType_name, int32(x))
}
func (PresentSendSourceType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_present_middleware_6a5ad00162c10c32, []int{0}
}

// 购买还是背包
type PresentSourceType int32

const (
	PresentSourceType_PRESENT_SOURCE_BUY             PresentSourceType = 0
	PresentSourceType_PRESENT_SOURCE_PACKAGE         PresentSourceType = 1
	PresentSourceType_PRESENT_SOURCE_PACKAGE_FIRST   PresentSourceType = 2
	PresentSourceType_PRESENT_SOURCE_FELLOW          PresentSourceType = 3
	PresentSourceType_PRESENT_SOURCE_MAGIC           PresentSourceType = 4
	PresentSourceType_PRESENT_SOURCE_LOTTERY_BUY     PresentSourceType = 5
	PresentSourceType_PRESENT_SOURCE_LOTTERY_PACKAGE PresentSourceType = 6
	PresentSourceType_PRESENT_SOURCE_LOTTERY_MAGIC   PresentSourceType = 7
	PresentSourceType_PRESENT_SOURCE_OFFERING_ROOM   PresentSourceType = 8
	PresentSourceType_PRESENT_SOURCE_CHANNEL_GIFT_PK PresentSourceType = 9
	PresentSourceType_PRESENT_SOURCE_GRAB_CHAIR_GAME PresentSourceType = 10
	PresentSourceType_PRESENT_SOURCE_WEDDING_RESERVE PresentSourceType = 11
)

var PresentSourceType_name = map[int32]string{
	0:  "PRESENT_SOURCE_BUY",
	1:  "PRESENT_SOURCE_PACKAGE",
	2:  "PRESENT_SOURCE_PACKAGE_FIRST",
	3:  "PRESENT_SOURCE_FELLOW",
	4:  "PRESENT_SOURCE_MAGIC",
	5:  "PRESENT_SOURCE_LOTTERY_BUY",
	6:  "PRESENT_SOURCE_LOTTERY_PACKAGE",
	7:  "PRESENT_SOURCE_LOTTERY_MAGIC",
	8:  "PRESENT_SOURCE_OFFERING_ROOM",
	9:  "PRESENT_SOURCE_CHANNEL_GIFT_PK",
	10: "PRESENT_SOURCE_GRAB_CHAIR_GAME",
	11: "PRESENT_SOURCE_WEDDING_RESERVE",
}
var PresentSourceType_value = map[string]int32{
	"PRESENT_SOURCE_BUY":             0,
	"PRESENT_SOURCE_PACKAGE":         1,
	"PRESENT_SOURCE_PACKAGE_FIRST":   2,
	"PRESENT_SOURCE_FELLOW":          3,
	"PRESENT_SOURCE_MAGIC":           4,
	"PRESENT_SOURCE_LOTTERY_BUY":     5,
	"PRESENT_SOURCE_LOTTERY_PACKAGE": 6,
	"PRESENT_SOURCE_LOTTERY_MAGIC":   7,
	"PRESENT_SOURCE_OFFERING_ROOM":   8,
	"PRESENT_SOURCE_CHANNEL_GIFT_PK": 9,
	"PRESENT_SOURCE_GRAB_CHAIR_GAME": 10,
	"PRESENT_SOURCE_WEDDING_RESERVE": 11,
}

func (x PresentSourceType) String() string {
	return proto.EnumName(PresentSourceType_name, int32(x))
}
func (PresentSourceType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_present_middleware_6a5ad00162c10c32, []int{1}
}

// 送礼类型
type PresentSendType int32

const (
	PresentSendType_PRESENT_SEND_NORMAL       PresentSendType = 0
	PresentSendType_PRESENT_SEND_DRAW         PresentSendType = 1
	PresentSendType_PRESENT_SENT_FANS_LOTTERY PresentSendType = 2
)

var PresentSendType_name = map[int32]string{
	0: "PRESENT_SEND_NORMAL",
	1: "PRESENT_SEND_DRAW",
	2: "PRESENT_SENT_FANS_LOTTERY",
}
var PresentSendType_value = map[string]int32{
	"PRESENT_SEND_NORMAL":       0,
	"PRESENT_SEND_DRAW":         1,
	"PRESENT_SENT_FANS_LOTTERY": 2,
}

func (x PresentSendType) String() string {
	return proto.EnumName(PresentSendType_name, int32(x))
}
func (PresentSendType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_present_middleware_6a5ad00162c10c32, []int{2}
}

// 批量送礼类型
type PresentBatchSendType int32

const (
	PresentBatchSendType_PRESENT_SOURCE_NONE           PresentBatchSendType = 0
	PresentBatchSendType_PRESENT_SOURCE_ALL_MIC        PresentBatchSendType = 1
	PresentBatchSendType_PRESENT_SOURCE_WITH_UID       PresentBatchSendType = 2
	PresentBatchSendType_PRESENT_SOURCE_WITH_ITEM_LIST PresentBatchSendType = 3
)

var PresentBatchSendType_name = map[int32]string{
	0: "PRESENT_SOURCE_NONE",
	1: "PRESENT_SOURCE_ALL_MIC",
	2: "PRESENT_SOURCE_WITH_UID",
	3: "PRESENT_SOURCE_WITH_ITEM_LIST",
}
var PresentBatchSendType_value = map[string]int32{
	"PRESENT_SOURCE_NONE":           0,
	"PRESENT_SOURCE_ALL_MIC":        1,
	"PRESENT_SOURCE_WITH_UID":       2,
	"PRESENT_SOURCE_WITH_ITEM_LIST": 3,
}

func (x PresentBatchSendType) String() string {
	return proto.EnumName(PresentBatchSendType_name, int32(x))
}
func (PresentBatchSendType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_present_middleware_6a5ad00162c10c32, []int{3}
}

// 送礼方式
type PresentSendMethodType int32

const (
	PresentSendMethodType_PRESENT_TYPE_ROOM   PresentSendMethodType = 0
	PresentSendMethodType_PRESENT_TYPE_IM     PresentSendMethodType = 1
	PresentSendMethodType_PRESENT_TYPE_FELLOW PresentSendMethodType = 2
)

var PresentSendMethodType_name = map[int32]string{
	0: "PRESENT_TYPE_ROOM",
	1: "PRESENT_TYPE_IM",
	2: "PRESENT_TYPE_FELLOW",
}
var PresentSendMethodType_value = map[string]int32{
	"PRESENT_TYPE_ROOM":   0,
	"PRESENT_TYPE_IM":     1,
	"PRESENT_TYPE_FELLOW": 2,
}

func (x PresentSendMethodType) String() string {
	return proto.EnumName(PresentSendMethodType_name, int32(x))
}
func (PresentSendMethodType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_present_middleware_6a5ad00162c10c32, []int{4}
}

type EUserPrivilegeType int32

const (
	EUserPrivilegeType_ENUM_USER_PRIVILEGE_UNKNOWN EUserPrivilegeType = 0
	EUserPrivilegeType_ENUM_USER_PRIVILEGE_UKW     EUserPrivilegeType = 1
)

var EUserPrivilegeType_name = map[int32]string{
	0: "ENUM_USER_PRIVILEGE_UNKNOWN",
	1: "ENUM_USER_PRIVILEGE_UKW",
}
var EUserPrivilegeType_value = map[string]int32{
	"ENUM_USER_PRIVILEGE_UNKNOWN": 0,
	"ENUM_USER_PRIVILEGE_UKW":     1,
}

func (x EUserPrivilegeType) String() string {
	return proto.EnumName(EUserPrivilegeType_name, int32(x))
}
func (EUserPrivilegeType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_present_middleware_6a5ad00162c10c32, []int{5}
}

// 送礼方式
type PushInfo_ChannelPushType int32

const (
	PushInfo_Channel_NONE       PushInfo_ChannelPushType = 0
	PushInfo_Channel_NORMAL     PushInfo_ChannelPushType = 1
	PushInfo_Channel_NORMAL_ALL PushInfo_ChannelPushType = 2
	PushInfo_Channel_MAGIC_PUSH PushInfo_ChannelPushType = 3
	PushInfo_Channel_ALLMIC     PushInfo_ChannelPushType = 4
	PushInfo_Channel_ALLMIC_ALL PushInfo_ChannelPushType = 5
)

var PushInfo_ChannelPushType_name = map[int32]string{
	0: "Channel_NONE",
	1: "Channel_NORMAL",
	2: "Channel_NORMAL_ALL",
	3: "Channel_MAGIC_PUSH",
	4: "Channel_ALLMIC",
	5: "Channel_ALLMIC_ALL",
}
var PushInfo_ChannelPushType_value = map[string]int32{
	"Channel_NONE":       0,
	"Channel_NORMAL":     1,
	"Channel_NORMAL_ALL": 2,
	"Channel_MAGIC_PUSH": 3,
	"Channel_ALLMIC":     4,
	"Channel_ALLMIC_ALL": 5,
}

func (x PushInfo_ChannelPushType) String() string {
	return proto.EnumName(PushInfo_ChannelPushType_name, int32(x))
}
func (PushInfo_ChannelPushType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_present_middleware_6a5ad00162c10c32, []int{11, 0}
}

type PushInfo_PersonalPushType int32

const (
	PushInfo_Person_NONE          PushInfo_PersonalPushType = 0
	PushInfo_Person_NORMAL        PushInfo_PersonalPushType = 1
	PushInfo_Person_NORMAL_SENDER PushInfo_PersonalPushType = 2
)

var PushInfo_PersonalPushType_name = map[int32]string{
	0: "Person_NONE",
	1: "Person_NORMAL",
	2: "Person_NORMAL_SENDER",
}
var PushInfo_PersonalPushType_value = map[string]int32{
	"Person_NONE":          0,
	"Person_NORMAL":        1,
	"Person_NORMAL_SENDER": 2,
}

func (x PushInfo_PersonalPushType) String() string {
	return proto.EnumName(PushInfo_PersonalPushType_name, int32(x))
}
func (PushInfo_PersonalPushType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_present_middleware_6a5ad00162c10c32, []int{11, 1}
}

type PushInfo_ImType int32

const (
	PushInfo_IM_NONE   PushInfo_ImType = 0
	PushInfo_IM_NORMAL PushInfo_ImType = 1
)

var PushInfo_ImType_name = map[int32]string{
	0: "IM_NONE",
	1: "IM_NORMAL",
}
var PushInfo_ImType_value = map[string]int32{
	"IM_NONE":   0,
	"IM_NORMAL": 1,
}

func (x PushInfo_ImType) String() string {
	return proto.EnumName(PushInfo_ImType_name, int32(x))
}
func (PushInfo_ImType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_present_middleware_6a5ad00162c10c32, []int{11, 2}
}

// 涂鸦礼物图
type DrawPresentPicture struct {
	LineList             []*PresentLine `protobuf:"bytes,1,rep,name=line_list,json=lineList,proto3" json:"line_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *DrawPresentPicture) Reset()         { *m = DrawPresentPicture{} }
func (m *DrawPresentPicture) String() string { return proto.CompactTextString(m) }
func (*DrawPresentPicture) ProtoMessage()    {}
func (*DrawPresentPicture) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_middleware_6a5ad00162c10c32, []int{0}
}
func (m *DrawPresentPicture) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DrawPresentPicture.Unmarshal(m, b)
}
func (m *DrawPresentPicture) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DrawPresentPicture.Marshal(b, m, deterministic)
}
func (dst *DrawPresentPicture) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DrawPresentPicture.Merge(dst, src)
}
func (m *DrawPresentPicture) XXX_Size() int {
	return xxx_messageInfo_DrawPresentPicture.Size(m)
}
func (m *DrawPresentPicture) XXX_DiscardUnknown() {
	xxx_messageInfo_DrawPresentPicture.DiscardUnknown(m)
}

var xxx_messageInfo_DrawPresentPicture proto.InternalMessageInfo

func (m *DrawPresentPicture) GetLineList() []*PresentLine {
	if m != nil {
		return m.LineList
	}
	return nil
}

type PresentLine struct {
	ItemId               uint32          `protobuf:"varint,1,opt,name=item_id,json=itemId,proto3" json:"item_id,omitempty"`
	PointList            []*PresentPoint `protobuf:"bytes,2,rep,name=point_list,json=pointList,proto3" json:"point_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *PresentLine) Reset()         { *m = PresentLine{} }
func (m *PresentLine) String() string { return proto.CompactTextString(m) }
func (*PresentLine) ProtoMessage()    {}
func (*PresentLine) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_middleware_6a5ad00162c10c32, []int{1}
}
func (m *PresentLine) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PresentLine.Unmarshal(m, b)
}
func (m *PresentLine) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PresentLine.Marshal(b, m, deterministic)
}
func (dst *PresentLine) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PresentLine.Merge(dst, src)
}
func (m *PresentLine) XXX_Size() int {
	return xxx_messageInfo_PresentLine.Size(m)
}
func (m *PresentLine) XXX_DiscardUnknown() {
	xxx_messageInfo_PresentLine.DiscardUnknown(m)
}

var xxx_messageInfo_PresentLine proto.InternalMessageInfo

func (m *PresentLine) GetItemId() uint32 {
	if m != nil {
		return m.ItemId
	}
	return 0
}

func (m *PresentLine) GetPointList() []*PresentPoint {
	if m != nil {
		return m.PointList
	}
	return nil
}

type PresentPoint struct {
	X                    float32  `protobuf:"fixed32,1,opt,name=x,proto3" json:"x,omitempty"`
	Y                    float32  `protobuf:"fixed32,2,opt,name=y,proto3" json:"y,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PresentPoint) Reset()         { *m = PresentPoint{} }
func (m *PresentPoint) String() string { return proto.CompactTextString(m) }
func (*PresentPoint) ProtoMessage()    {}
func (*PresentPoint) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_middleware_6a5ad00162c10c32, []int{2}
}
func (m *PresentPoint) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PresentPoint.Unmarshal(m, b)
}
func (m *PresentPoint) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PresentPoint.Marshal(b, m, deterministic)
}
func (dst *PresentPoint) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PresentPoint.Merge(dst, src)
}
func (m *PresentPoint) XXX_Size() int {
	return xxx_messageInfo_PresentPoint.Size(m)
}
func (m *PresentPoint) XXX_DiscardUnknown() {
	xxx_messageInfo_PresentPoint.DiscardUnknown(m)
}

var xxx_messageInfo_PresentPoint proto.InternalMessageInfo

func (m *PresentPoint) GetX() float32 {
	if m != nil {
		return m.X
	}
	return 0
}

func (m *PresentPoint) GetY() float32 {
	if m != nil {
		return m.Y
	}
	return 0
}

// 赠送礼物
type PresentSendMsg struct {
	ItemInfo             *PresentSendItemInfo `protobuf:"bytes,1,opt,name=item_info,json=itemInfo,proto3" json:"item_info,omitempty"`
	SendTime             uint64               `protobuf:"varint,2,opt,name=send_time,json=sendTime,proto3" json:"send_time,omitempty"`
	ChannelId            uint32               `protobuf:"varint,3,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	SendUid              uint32               `protobuf:"varint,4,opt,name=send_uid,json=sendUid,proto3" json:"send_uid,omitempty"`
	SendAccount          string               `protobuf:"bytes,5,opt,name=send_account,json=sendAccount,proto3" json:"send_account,omitempty"`
	SendNickname         string               `protobuf:"bytes,6,opt,name=send_nickname,json=sendNickname,proto3" json:"send_nickname,omitempty"`
	TargetUid            uint32               `protobuf:"varint,7,opt,name=target_uid,json=targetUid,proto3" json:"target_uid,omitempty"`
	TargetAccount        string               `protobuf:"bytes,8,opt,name=target_account,json=targetAccount,proto3" json:"target_account,omitempty"`
	TargetNickname       string               `protobuf:"bytes,9,opt,name=target_nickname,json=targetNickname,proto3" json:"target_nickname,omitempty"`
	ExtendJson           string               `protobuf:"bytes,10,opt,name=extend_json,json=extendJson,proto3" json:"extend_json,omitempty"`
	FromUserProfile      *UserProfile         `protobuf:"bytes,11,opt,name=from_user_profile,json=fromUserProfile,proto3" json:"from_user_profile,omitempty"`
	TargetUserProfile    *UserProfile         `protobuf:"bytes,12,opt,name=target_user_profile,json=targetUserProfile,proto3" json:"target_user_profile,omitempty"`
	OnlyShowMessage      bool                 `protobuf:"varint,13,opt,name=only_show_message,json=onlyShowMessage,proto3" json:"only_show_message,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *PresentSendMsg) Reset()         { *m = PresentSendMsg{} }
func (m *PresentSendMsg) String() string { return proto.CompactTextString(m) }
func (*PresentSendMsg) ProtoMessage()    {}
func (*PresentSendMsg) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_middleware_6a5ad00162c10c32, []int{3}
}
func (m *PresentSendMsg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PresentSendMsg.Unmarshal(m, b)
}
func (m *PresentSendMsg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PresentSendMsg.Marshal(b, m, deterministic)
}
func (dst *PresentSendMsg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PresentSendMsg.Merge(dst, src)
}
func (m *PresentSendMsg) XXX_Size() int {
	return xxx_messageInfo_PresentSendMsg.Size(m)
}
func (m *PresentSendMsg) XXX_DiscardUnknown() {
	xxx_messageInfo_PresentSendMsg.DiscardUnknown(m)
}

var xxx_messageInfo_PresentSendMsg proto.InternalMessageInfo

func (m *PresentSendMsg) GetItemInfo() *PresentSendItemInfo {
	if m != nil {
		return m.ItemInfo
	}
	return nil
}

func (m *PresentSendMsg) GetSendTime() uint64 {
	if m != nil {
		return m.SendTime
	}
	return 0
}

func (m *PresentSendMsg) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *PresentSendMsg) GetSendUid() uint32 {
	if m != nil {
		return m.SendUid
	}
	return 0
}

func (m *PresentSendMsg) GetSendAccount() string {
	if m != nil {
		return m.SendAccount
	}
	return ""
}

func (m *PresentSendMsg) GetSendNickname() string {
	if m != nil {
		return m.SendNickname
	}
	return ""
}

func (m *PresentSendMsg) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

func (m *PresentSendMsg) GetTargetAccount() string {
	if m != nil {
		return m.TargetAccount
	}
	return ""
}

func (m *PresentSendMsg) GetTargetNickname() string {
	if m != nil {
		return m.TargetNickname
	}
	return ""
}

func (m *PresentSendMsg) GetExtendJson() string {
	if m != nil {
		return m.ExtendJson
	}
	return ""
}

func (m *PresentSendMsg) GetFromUserProfile() *UserProfile {
	if m != nil {
		return m.FromUserProfile
	}
	return nil
}

func (m *PresentSendMsg) GetTargetUserProfile() *UserProfile {
	if m != nil {
		return m.TargetUserProfile
	}
	return nil
}

func (m *PresentSendMsg) GetOnlyShowMessage() bool {
	if m != nil {
		return m.OnlyShowMessage
	}
	return false
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
type SendPresentReq struct {
	SendUid              uint32              `protobuf:"varint,1,opt,name=send_uid,json=sendUid,proto3" json:"send_uid,omitempty"`
	BatchType            uint32              `protobuf:"varint,2,opt,name=batch_type,json=batchType,proto3" json:"batch_type,omitempty"`
	TargetInfo           *PresentTargetInfo  `protobuf:"bytes,3,opt,name=target_info,json=targetInfo,proto3" json:"target_info,omitempty"`
	ChannelId            uint32              `protobuf:"varint,4,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	SendSource           uint32              `protobuf:"varint,5,opt,name=send_source,json=sendSource,proto3" json:"send_source,omitempty"`
	ItemSource           uint32              `protobuf:"varint,6,opt,name=item_source,json=itemSource,proto3" json:"item_source,omitempty"`
	SendMethod           uint32              `protobuf:"varint,7,opt,name=send_method,json=sendMethod,proto3" json:"send_method,omitempty"`
	SendType             uint32              `protobuf:"varint,8,opt,name=send_type,json=sendType,proto3" json:"send_type,omitempty"`
	DrawPresentPic       *DrawPresentPicture `protobuf:"bytes,9,opt,name=draw_present_pic,json=drawPresentPic,proto3" json:"draw_present_pic,omitempty"`
	IsOptValid           bool                `protobuf:"varint,10,opt,name=is_opt_valid,json=isOptValid,proto3" json:"is_opt_valid,omitempty"`
	WithPay              bool                `protobuf:"varint,11,opt,name=with_pay,json=withPay,proto3" json:"with_pay,omitempty"`
	BackpackItemId       uint32              `protobuf:"varint,12,opt,name=backpack_item_id,json=backpackItemId,proto3" json:"backpack_item_id,omitempty"`
	WithPush             bool                `protobuf:"varint,13,opt,name=with_push,json=withPush,proto3" json:"with_push,omitempty"`
	PushInfo             *PushInfo           `protobuf:"bytes,14,opt,name=push_info,json=pushInfo,proto3" json:"push_info,omitempty"`
	ClientInfo           *PresentClientInfo  `protobuf:"bytes,15,opt,name=client_info,json=clientInfo,proto3" json:"client_info,omitempty"`
	SendTime             int64               `protobuf:"varint,16,opt,name=sendTime,proto3" json:"sendTime,omitempty"`
	SurpriseEffectCount  uint32              `protobuf:"varint,17,opt,name=surprise_effect_count,json=surpriseEffectCount,proto3" json:"surprise_effect_count,omitempty"`
	ChannelGameId        uint32              `protobuf:"varint,18,opt,name=channel_game_id,json=channelGameId,proto3" json:"channel_game_id,omitempty"`
	IsMulti              bool                `protobuf:"varint,19,opt,name=is_multi,json=isMulti,proto3" json:"is_multi,omitempty"`
	SendChannelId        uint32              `protobuf:"varint,20,opt,name=send_channel_id,json=sendChannelId,proto3" json:"send_channel_id,omitempty"`
	BirthdayType         uint32              `protobuf:"varint,21,opt,name=birthday_type,json=birthdayType,proto3" json:"birthday_type,omitempty"`
	BusinessType         uint32              `protobuf:"varint,22,opt,name=business_type,json=businessType,proto3" json:"business_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *SendPresentReq) Reset()         { *m = SendPresentReq{} }
func (m *SendPresentReq) String() string { return proto.CompactTextString(m) }
func (*SendPresentReq) ProtoMessage()    {}
func (*SendPresentReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_middleware_6a5ad00162c10c32, []int{4}
}
func (m *SendPresentReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SendPresentReq.Unmarshal(m, b)
}
func (m *SendPresentReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SendPresentReq.Marshal(b, m, deterministic)
}
func (dst *SendPresentReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendPresentReq.Merge(dst, src)
}
func (m *SendPresentReq) XXX_Size() int {
	return xxx_messageInfo_SendPresentReq.Size(m)
}
func (m *SendPresentReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SendPresentReq.DiscardUnknown(m)
}

var xxx_messageInfo_SendPresentReq proto.InternalMessageInfo

func (m *SendPresentReq) GetSendUid() uint32 {
	if m != nil {
		return m.SendUid
	}
	return 0
}

func (m *SendPresentReq) GetBatchType() uint32 {
	if m != nil {
		return m.BatchType
	}
	return 0
}

func (m *SendPresentReq) GetTargetInfo() *PresentTargetInfo {
	if m != nil {
		return m.TargetInfo
	}
	return nil
}

func (m *SendPresentReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SendPresentReq) GetSendSource() uint32 {
	if m != nil {
		return m.SendSource
	}
	return 0
}

func (m *SendPresentReq) GetItemSource() uint32 {
	if m != nil {
		return m.ItemSource
	}
	return 0
}

func (m *SendPresentReq) GetSendMethod() uint32 {
	if m != nil {
		return m.SendMethod
	}
	return 0
}

func (m *SendPresentReq) GetSendType() uint32 {
	if m != nil {
		return m.SendType
	}
	return 0
}

func (m *SendPresentReq) GetDrawPresentPic() *DrawPresentPicture {
	if m != nil {
		return m.DrawPresentPic
	}
	return nil
}

func (m *SendPresentReq) GetIsOptValid() bool {
	if m != nil {
		return m.IsOptValid
	}
	return false
}

func (m *SendPresentReq) GetWithPay() bool {
	if m != nil {
		return m.WithPay
	}
	return false
}

func (m *SendPresentReq) GetBackpackItemId() uint32 {
	if m != nil {
		return m.BackpackItemId
	}
	return 0
}

func (m *SendPresentReq) GetWithPush() bool {
	if m != nil {
		return m.WithPush
	}
	return false
}

func (m *SendPresentReq) GetPushInfo() *PushInfo {
	if m != nil {
		return m.PushInfo
	}
	return nil
}

func (m *SendPresentReq) GetClientInfo() *PresentClientInfo {
	if m != nil {
		return m.ClientInfo
	}
	return nil
}

func (m *SendPresentReq) GetSendTime() int64 {
	if m != nil {
		return m.SendTime
	}
	return 0
}

func (m *SendPresentReq) GetSurpriseEffectCount() uint32 {
	if m != nil {
		return m.SurpriseEffectCount
	}
	return 0
}

func (m *SendPresentReq) GetChannelGameId() uint32 {
	if m != nil {
		return m.ChannelGameId
	}
	return 0
}

func (m *SendPresentReq) GetIsMulti() bool {
	if m != nil {
		return m.IsMulti
	}
	return false
}

func (m *SendPresentReq) GetSendChannelId() uint32 {
	if m != nil {
		return m.SendChannelId
	}
	return 0
}

func (m *SendPresentReq) GetBirthdayType() uint32 {
	if m != nil {
		return m.BirthdayType
	}
	return 0
}

func (m *SendPresentReq) GetBusinessType() uint32 {
	if m != nil {
		return m.BusinessType
	}
	return 0
}

type PresentTargetInfo struct {
	// buf:lint:ignore ONEOF_LOWER_SNAKE_CASE
	//
	// Types that are valid to be assigned to Target:
	//	*PresentTargetInfo_SingleTarget
	//	*PresentTargetInfo_MultiTarget
	//	*PresentTargetInfo_MultiItem
	Target               isPresentTargetInfo_Target `protobuf_oneof:"Target"`
	XXX_NoUnkeyedLiteral struct{}                   `json:"-"`
	XXX_unrecognized     []byte                     `json:"-"`
	XXX_sizecache        int32                      `json:"-"`
}

func (m *PresentTargetInfo) Reset()         { *m = PresentTargetInfo{} }
func (m *PresentTargetInfo) String() string { return proto.CompactTextString(m) }
func (*PresentTargetInfo) ProtoMessage()    {}
func (*PresentTargetInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_middleware_6a5ad00162c10c32, []int{5}
}
func (m *PresentTargetInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PresentTargetInfo.Unmarshal(m, b)
}
func (m *PresentTargetInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PresentTargetInfo.Marshal(b, m, deterministic)
}
func (dst *PresentTargetInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PresentTargetInfo.Merge(dst, src)
}
func (m *PresentTargetInfo) XXX_Size() int {
	return xxx_messageInfo_PresentTargetInfo.Size(m)
}
func (m *PresentTargetInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_PresentTargetInfo.DiscardUnknown(m)
}

var xxx_messageInfo_PresentTargetInfo proto.InternalMessageInfo

type isPresentTargetInfo_Target interface {
	isPresentTargetInfo_Target()
}

type PresentTargetInfo_SingleTarget struct {
	SingleTarget *SingleTargetUser `protobuf:"bytes,1,opt,name=single_target,json=singleTarget,proto3,oneof"`
}

type PresentTargetInfo_MultiTarget struct {
	MultiTarget *MultiTargetUser `protobuf:"bytes,2,opt,name=multi_target,json=multiTarget,proto3,oneof"`
}

type PresentTargetInfo_MultiItem struct {
	MultiItem *MultiTargetItem `protobuf:"bytes,3,opt,name=multi_item,json=multiItem,proto3,oneof"`
}

func (*PresentTargetInfo_SingleTarget) isPresentTargetInfo_Target() {}

func (*PresentTargetInfo_MultiTarget) isPresentTargetInfo_Target() {}

func (*PresentTargetInfo_MultiItem) isPresentTargetInfo_Target() {}

func (m *PresentTargetInfo) GetTarget() isPresentTargetInfo_Target {
	if m != nil {
		return m.Target
	}
	return nil
}

func (m *PresentTargetInfo) GetSingleTarget() *SingleTargetUser {
	if x, ok := m.GetTarget().(*PresentTargetInfo_SingleTarget); ok {
		return x.SingleTarget
	}
	return nil
}

func (m *PresentTargetInfo) GetMultiTarget() *MultiTargetUser {
	if x, ok := m.GetTarget().(*PresentTargetInfo_MultiTarget); ok {
		return x.MultiTarget
	}
	return nil
}

func (m *PresentTargetInfo) GetMultiItem() *MultiTargetItem {
	if x, ok := m.GetTarget().(*PresentTargetInfo_MultiItem); ok {
		return x.MultiItem
	}
	return nil
}

// XXX_OneofFuncs is for the internal use of the proto package.
func (*PresentTargetInfo) XXX_OneofFuncs() (func(msg proto.Message, b *proto.Buffer) error, func(msg proto.Message, tag, wire int, b *proto.Buffer) (bool, error), func(msg proto.Message) (n int), []interface{}) {
	return _PresentTargetInfo_OneofMarshaler, _PresentTargetInfo_OneofUnmarshaler, _PresentTargetInfo_OneofSizer, []interface{}{
		(*PresentTargetInfo_SingleTarget)(nil),
		(*PresentTargetInfo_MultiTarget)(nil),
		(*PresentTargetInfo_MultiItem)(nil),
	}
}

func _PresentTargetInfo_OneofMarshaler(msg proto.Message, b *proto.Buffer) error {
	m := msg.(*PresentTargetInfo)
	// Target
	switch x := m.Target.(type) {
	case *PresentTargetInfo_SingleTarget:
		b.EncodeVarint(1<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.SingleTarget); err != nil {
			return err
		}
	case *PresentTargetInfo_MultiTarget:
		b.EncodeVarint(2<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.MultiTarget); err != nil {
			return err
		}
	case *PresentTargetInfo_MultiItem:
		b.EncodeVarint(3<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.MultiItem); err != nil {
			return err
		}
	case nil:
	default:
		return fmt.Errorf("PresentTargetInfo.Target has unexpected type %T", x)
	}
	return nil
}

func _PresentTargetInfo_OneofUnmarshaler(msg proto.Message, tag, wire int, b *proto.Buffer) (bool, error) {
	m := msg.(*PresentTargetInfo)
	switch tag {
	case 1: // Target.single_target
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(SingleTargetUser)
		err := b.DecodeMessage(msg)
		m.Target = &PresentTargetInfo_SingleTarget{msg}
		return true, err
	case 2: // Target.multi_target
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(MultiTargetUser)
		err := b.DecodeMessage(msg)
		m.Target = &PresentTargetInfo_MultiTarget{msg}
		return true, err
	case 3: // Target.multi_item
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(MultiTargetItem)
		err := b.DecodeMessage(msg)
		m.Target = &PresentTargetInfo_MultiItem{msg}
		return true, err
	default:
		return false, nil
	}
}

func _PresentTargetInfo_OneofSizer(msg proto.Message) (n int) {
	m := msg.(*PresentTargetInfo)
	// Target
	switch x := m.Target.(type) {
	case *PresentTargetInfo_SingleTarget:
		s := proto.Size(x.SingleTarget)
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(s))
		n += s
	case *PresentTargetInfo_MultiTarget:
		s := proto.Size(x.MultiTarget)
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(s))
		n += s
	case *PresentTargetInfo_MultiItem:
		s := proto.Size(x.MultiItem)
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(s))
		n += s
	case nil:
	default:
		panic(fmt.Sprintf("proto: unexpected type %T in oneof", x))
	}
	return n
}

// 对应PresentBatchSendType = 0
type SingleTargetUser struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ItemId               uint32   `protobuf:"varint,2,opt,name=item_id,json=itemId,proto3" json:"item_id,omitempty"`
	Count                uint32   `protobuf:"varint,3,opt,name=count,proto3" json:"count,omitempty"`
	OrderId              string   `protobuf:"bytes,4,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	DealToken            string   `protobuf:"bytes,5,opt,name=deal_token,json=dealToken,proto3" json:"deal_token,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SingleTargetUser) Reset()         { *m = SingleTargetUser{} }
func (m *SingleTargetUser) String() string { return proto.CompactTextString(m) }
func (*SingleTargetUser) ProtoMessage()    {}
func (*SingleTargetUser) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_middleware_6a5ad00162c10c32, []int{6}
}
func (m *SingleTargetUser) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SingleTargetUser.Unmarshal(m, b)
}
func (m *SingleTargetUser) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SingleTargetUser.Marshal(b, m, deterministic)
}
func (dst *SingleTargetUser) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SingleTargetUser.Merge(dst, src)
}
func (m *SingleTargetUser) XXX_Size() int {
	return xxx_messageInfo_SingleTargetUser.Size(m)
}
func (m *SingleTargetUser) XXX_DiscardUnknown() {
	xxx_messageInfo_SingleTargetUser.DiscardUnknown(m)
}

var xxx_messageInfo_SingleTargetUser proto.InternalMessageInfo

func (m *SingleTargetUser) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SingleTargetUser) GetItemId() uint32 {
	if m != nil {
		return m.ItemId
	}
	return 0
}

func (m *SingleTargetUser) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *SingleTargetUser) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *SingleTargetUser) GetDealToken() string {
	if m != nil {
		return m.DealToken
	}
	return ""
}

// 对应PresentBatchSendType = 1/2
type MultiTargetUser struct {
	Uid                  []uint32          `protobuf:"varint,1,rep,packed,name=uid,proto3" json:"uid,omitempty"`
	ItemId               uint32            `protobuf:"varint,2,opt,name=item_id,json=itemId,proto3" json:"item_id,omitempty"`
	Count                uint32            `protobuf:"varint,3,opt,name=count,proto3" json:"count,omitempty"`
	UniqueOrderId        string            `protobuf:"bytes,4,opt,name=unique_order_id,json=uniqueOrderId,proto3" json:"unique_order_id,omitempty"`
	UserInfo             []*TargetUserInfo `protobuf:"bytes,5,rep,name=user_info,json=userInfo,proto3" json:"user_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *MultiTargetUser) Reset()         { *m = MultiTargetUser{} }
func (m *MultiTargetUser) String() string { return proto.CompactTextString(m) }
func (*MultiTargetUser) ProtoMessage()    {}
func (*MultiTargetUser) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_middleware_6a5ad00162c10c32, []int{7}
}
func (m *MultiTargetUser) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MultiTargetUser.Unmarshal(m, b)
}
func (m *MultiTargetUser) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MultiTargetUser.Marshal(b, m, deterministic)
}
func (dst *MultiTargetUser) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MultiTargetUser.Merge(dst, src)
}
func (m *MultiTargetUser) XXX_Size() int {
	return xxx_messageInfo_MultiTargetUser.Size(m)
}
func (m *MultiTargetUser) XXX_DiscardUnknown() {
	xxx_messageInfo_MultiTargetUser.DiscardUnknown(m)
}

var xxx_messageInfo_MultiTargetUser proto.InternalMessageInfo

func (m *MultiTargetUser) GetUid() []uint32 {
	if m != nil {
		return m.Uid
	}
	return nil
}

func (m *MultiTargetUser) GetItemId() uint32 {
	if m != nil {
		return m.ItemId
	}
	return 0
}

func (m *MultiTargetUser) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *MultiTargetUser) GetUniqueOrderId() string {
	if m != nil {
		return m.UniqueOrderId
	}
	return ""
}

func (m *MultiTargetUser) GetUserInfo() []*TargetUserInfo {
	if m != nil {
		return m.UserInfo
	}
	return nil
}

type TargetUserInfo struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	OrderId              string   `protobuf:"bytes,2,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	DealToken            string   `protobuf:"bytes,3,opt,name=deal_token,json=dealToken,proto3" json:"deal_token,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TargetUserInfo) Reset()         { *m = TargetUserInfo{} }
func (m *TargetUserInfo) String() string { return proto.CompactTextString(m) }
func (*TargetUserInfo) ProtoMessage()    {}
func (*TargetUserInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_middleware_6a5ad00162c10c32, []int{8}
}
func (m *TargetUserInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TargetUserInfo.Unmarshal(m, b)
}
func (m *TargetUserInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TargetUserInfo.Marshal(b, m, deterministic)
}
func (dst *TargetUserInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TargetUserInfo.Merge(dst, src)
}
func (m *TargetUserInfo) XXX_Size() int {
	return xxx_messageInfo_TargetUserInfo.Size(m)
}
func (m *TargetUserInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_TargetUserInfo.DiscardUnknown(m)
}

var xxx_messageInfo_TargetUserInfo proto.InternalMessageInfo

func (m *TargetUserInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *TargetUserInfo) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *TargetUserInfo) GetDealToken() string {
	if m != nil {
		return m.DealToken
	}
	return ""
}

// 对应PresentBatchSendType = 3
type MultiTargetItem struct {
	ItemInfo             []*PresentItemInfo `protobuf:"bytes,1,rep,name=item_info,json=itemInfo,proto3" json:"item_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *MultiTargetItem) Reset()         { *m = MultiTargetItem{} }
func (m *MultiTargetItem) String() string { return proto.CompactTextString(m) }
func (*MultiTargetItem) ProtoMessage()    {}
func (*MultiTargetItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_middleware_6a5ad00162c10c32, []int{9}
}
func (m *MultiTargetItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MultiTargetItem.Unmarshal(m, b)
}
func (m *MultiTargetItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MultiTargetItem.Marshal(b, m, deterministic)
}
func (dst *MultiTargetItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MultiTargetItem.Merge(dst, src)
}
func (m *MultiTargetItem) XXX_Size() int {
	return xxx_messageInfo_MultiTargetItem.Size(m)
}
func (m *MultiTargetItem) XXX_DiscardUnknown() {
	xxx_messageInfo_MultiTargetItem.DiscardUnknown(m)
}

var xxx_messageInfo_MultiTargetItem proto.InternalMessageInfo

func (m *MultiTargetItem) GetItemInfo() []*PresentItemInfo {
	if m != nil {
		return m.ItemInfo
	}
	return nil
}

type PresentItemInfo struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ItemId               uint32   `protobuf:"varint,2,opt,name=item_id,json=itemId,proto3" json:"item_id,omitempty"`
	Count                uint32   `protobuf:"varint,3,opt,name=count,proto3" json:"count,omitempty"`
	OrderId              string   `protobuf:"bytes,4,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	DealToken            string   `protobuf:"bytes,5,opt,name=deal_token,json=dealToken,proto3" json:"deal_token,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PresentItemInfo) Reset()         { *m = PresentItemInfo{} }
func (m *PresentItemInfo) String() string { return proto.CompactTextString(m) }
func (*PresentItemInfo) ProtoMessage()    {}
func (*PresentItemInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_middleware_6a5ad00162c10c32, []int{10}
}
func (m *PresentItemInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PresentItemInfo.Unmarshal(m, b)
}
func (m *PresentItemInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PresentItemInfo.Marshal(b, m, deterministic)
}
func (dst *PresentItemInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PresentItemInfo.Merge(dst, src)
}
func (m *PresentItemInfo) XXX_Size() int {
	return xxx_messageInfo_PresentItemInfo.Size(m)
}
func (m *PresentItemInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_PresentItemInfo.DiscardUnknown(m)
}

var xxx_messageInfo_PresentItemInfo proto.InternalMessageInfo

func (m *PresentItemInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *PresentItemInfo) GetItemId() uint32 {
	if m != nil {
		return m.ItemId
	}
	return 0
}

func (m *PresentItemInfo) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *PresentItemInfo) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *PresentItemInfo) GetDealToken() string {
	if m != nil {
		return m.DealToken
	}
	return ""
}

type PushInfo struct {
	ChannelPushType      uint32   `protobuf:"varint,1,opt,name=channel_push_type,json=channelPushType,proto3" json:"channel_push_type,omitempty"`
	PersonalPushType     uint32   `protobuf:"varint,2,opt,name=personal_push_type,json=personalPushType,proto3" json:"personal_push_type,omitempty"`
	ImMsgType            uint32   `protobuf:"varint,3,opt,name=im_msg_type,json=imMsgType,proto3" json:"im_msg_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PushInfo) Reset()         { *m = PushInfo{} }
func (m *PushInfo) String() string { return proto.CompactTextString(m) }
func (*PushInfo) ProtoMessage()    {}
func (*PushInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_middleware_6a5ad00162c10c32, []int{11}
}
func (m *PushInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PushInfo.Unmarshal(m, b)
}
func (m *PushInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PushInfo.Marshal(b, m, deterministic)
}
func (dst *PushInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PushInfo.Merge(dst, src)
}
func (m *PushInfo) XXX_Size() int {
	return xxx_messageInfo_PushInfo.Size(m)
}
func (m *PushInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_PushInfo.DiscardUnknown(m)
}

var xxx_messageInfo_PushInfo proto.InternalMessageInfo

func (m *PushInfo) GetChannelPushType() uint32 {
	if m != nil {
		return m.ChannelPushType
	}
	return 0
}

func (m *PushInfo) GetPersonalPushType() uint32 {
	if m != nil {
		return m.PersonalPushType
	}
	return 0
}

func (m *PushInfo) GetImMsgType() uint32 {
	if m != nil {
		return m.ImMsgType
	}
	return 0
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
type PresentClientInfo struct {
	AppId                uint32           `protobuf:"varint,1,opt,name=appId,proto3" json:"appId,omitempty"`
	MarketId             uint32           `protobuf:"varint,2,opt,name=marketId,proto3" json:"marketId,omitempty"`
	ServiceInfo          *ServiceCtrlInfo `protobuf:"bytes,3,opt,name=service_info,json=serviceInfo,proto3" json:"service_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *PresentClientInfo) Reset()         { *m = PresentClientInfo{} }
func (m *PresentClientInfo) String() string { return proto.CompactTextString(m) }
func (*PresentClientInfo) ProtoMessage()    {}
func (*PresentClientInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_middleware_6a5ad00162c10c32, []int{12}
}
func (m *PresentClientInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PresentClientInfo.Unmarshal(m, b)
}
func (m *PresentClientInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PresentClientInfo.Marshal(b, m, deterministic)
}
func (dst *PresentClientInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PresentClientInfo.Merge(dst, src)
}
func (m *PresentClientInfo) XXX_Size() int {
	return xxx_messageInfo_PresentClientInfo.Size(m)
}
func (m *PresentClientInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_PresentClientInfo.DiscardUnknown(m)
}

var xxx_messageInfo_PresentClientInfo proto.InternalMessageInfo

func (m *PresentClientInfo) GetAppId() uint32 {
	if m != nil {
		return m.AppId
	}
	return 0
}

func (m *PresentClientInfo) GetMarketId() uint32 {
	if m != nil {
		return m.MarketId
	}
	return 0
}

func (m *PresentClientInfo) GetServiceInfo() *ServiceCtrlInfo {
	if m != nil {
		return m.ServiceInfo
	}
	return nil
}

type SendPresentResp struct {
	MsgInfo                 []*PresentSendMsg      `protobuf:"bytes,1,rep,name=msg_info,json=msgInfo,proto3" json:"msg_info,omitempty"`
	MemberContributionAdded uint32                 `protobuf:"varint,2,opt,name=member_contribution_added,json=memberContributionAdded,proto3" json:"member_contribution_added,omitempty"`
	Count                   uint32                 `protobuf:"varint,3,opt,name=count,proto3" json:"count,omitempty"`
	CurTbeans               int64                  `protobuf:"varint,4,opt,name=cur_tbeans,json=curTbeans,proto3" json:"cur_tbeans,omitempty"`
	ItemSource              uint32                 `protobuf:"varint,5,opt,name=item_source,json=itemSource,proto3" json:"item_source,omitempty"`
	SourceId                uint32                 `protobuf:"varint,6,opt,name=source_id,json=sourceId,proto3" json:"source_id,omitempty"`
	SourceRemain            uint32                 `protobuf:"varint,7,opt,name=source_remain,json=sourceRemain,proto3" json:"source_remain,omitempty"`
	BatchInfo               []*PresentBatchInfoMsg `protobuf:"bytes,8,rep,name=batch_info,json=batchInfo,proto3" json:"batch_info,omitempty"`
	OrderInfo               []*OrderInfo           `protobuf:"bytes,9,rep,name=order_info,json=orderInfo,proto3" json:"order_info,omitempty"`
	ExpireTime              uint32                 `protobuf:"varint,10,opt,name=expire_time,json=expireTime,proto3" json:"expire_time,omitempty"`
	BoxDetail               *PresentBoxDetail      `protobuf:"bytes,11,opt,name=box_detail,json=boxDetail,proto3" json:"box_detail,omitempty"`
	XXX_NoUnkeyedLiteral    struct{}               `json:"-"`
	XXX_unrecognized        []byte                 `json:"-"`
	XXX_sizecache           int32                  `json:"-"`
}

func (m *SendPresentResp) Reset()         { *m = SendPresentResp{} }
func (m *SendPresentResp) String() string { return proto.CompactTextString(m) }
func (*SendPresentResp) ProtoMessage()    {}
func (*SendPresentResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_middleware_6a5ad00162c10c32, []int{13}
}
func (m *SendPresentResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SendPresentResp.Unmarshal(m, b)
}
func (m *SendPresentResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SendPresentResp.Marshal(b, m, deterministic)
}
func (dst *SendPresentResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendPresentResp.Merge(dst, src)
}
func (m *SendPresentResp) XXX_Size() int {
	return xxx_messageInfo_SendPresentResp.Size(m)
}
func (m *SendPresentResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SendPresentResp.DiscardUnknown(m)
}

var xxx_messageInfo_SendPresentResp proto.InternalMessageInfo

func (m *SendPresentResp) GetMsgInfo() []*PresentSendMsg {
	if m != nil {
		return m.MsgInfo
	}
	return nil
}

func (m *SendPresentResp) GetMemberContributionAdded() uint32 {
	if m != nil {
		return m.MemberContributionAdded
	}
	return 0
}

func (m *SendPresentResp) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *SendPresentResp) GetCurTbeans() int64 {
	if m != nil {
		return m.CurTbeans
	}
	return 0
}

func (m *SendPresentResp) GetItemSource() uint32 {
	if m != nil {
		return m.ItemSource
	}
	return 0
}

func (m *SendPresentResp) GetSourceId() uint32 {
	if m != nil {
		return m.SourceId
	}
	return 0
}

func (m *SendPresentResp) GetSourceRemain() uint32 {
	if m != nil {
		return m.SourceRemain
	}
	return 0
}

func (m *SendPresentResp) GetBatchInfo() []*PresentBatchInfoMsg {
	if m != nil {
		return m.BatchInfo
	}
	return nil
}

func (m *SendPresentResp) GetOrderInfo() []*OrderInfo {
	if m != nil {
		return m.OrderInfo
	}
	return nil
}

func (m *SendPresentResp) GetExpireTime() uint32 {
	if m != nil {
		return m.ExpireTime
	}
	return 0
}

func (m *SendPresentResp) GetBoxDetail() *PresentBoxDetail {
	if m != nil {
		return m.BoxDetail
	}
	return nil
}

type OrderInfo struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	OrderId              string   `protobuf:"bytes,2,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *OrderInfo) Reset()         { *m = OrderInfo{} }
func (m *OrderInfo) String() string { return proto.CompactTextString(m) }
func (*OrderInfo) ProtoMessage()    {}
func (*OrderInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_middleware_6a5ad00162c10c32, []int{14}
}
func (m *OrderInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OrderInfo.Unmarshal(m, b)
}
func (m *OrderInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OrderInfo.Marshal(b, m, deterministic)
}
func (dst *OrderInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OrderInfo.Merge(dst, src)
}
func (m *OrderInfo) XXX_Size() int {
	return xxx_messageInfo_OrderInfo.Size(m)
}
func (m *OrderInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_OrderInfo.DiscardUnknown(m)
}

var xxx_messageInfo_OrderInfo proto.InternalMessageInfo

func (m *OrderInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *OrderInfo) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

type PresentTargetUserInfo struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Account              string   `protobuf:"bytes,2,opt,name=account,proto3" json:"account,omitempty"`
	Name                 string   `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PresentTargetUserInfo) Reset()         { *m = PresentTargetUserInfo{} }
func (m *PresentTargetUserInfo) String() string { return proto.CompactTextString(m) }
func (*PresentTargetUserInfo) ProtoMessage()    {}
func (*PresentTargetUserInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_middleware_6a5ad00162c10c32, []int{15}
}
func (m *PresentTargetUserInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PresentTargetUserInfo.Unmarshal(m, b)
}
func (m *PresentTargetUserInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PresentTargetUserInfo.Marshal(b, m, deterministic)
}
func (dst *PresentTargetUserInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PresentTargetUserInfo.Merge(dst, src)
}
func (m *PresentTargetUserInfo) XXX_Size() int {
	return xxx_messageInfo_PresentTargetUserInfo.Size(m)
}
func (m *PresentTargetUserInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_PresentTargetUserInfo.DiscardUnknown(m)
}

var xxx_messageInfo_PresentTargetUserInfo proto.InternalMessageInfo

func (m *PresentTargetUserInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *PresentTargetUserInfo) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *PresentTargetUserInfo) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

type PresentBatchTargetInfo struct {
	Uid                  uint32       `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Account              string       `protobuf:"bytes,2,opt,name=account,proto3" json:"account,omitempty"`
	Nickname             string       `protobuf:"bytes,3,opt,name=nickname,proto3" json:"nickname,omitempty"`
	ExtendJson           string       `protobuf:"bytes,4,opt,name=extend_json,json=extendJson,proto3" json:"extend_json,omitempty"`
	UserProfile          *UserProfile `protobuf:"bytes,11,opt,name=user_profile,json=userProfile,proto3" json:"user_profile,omitempty"`
	CustomText           string       `protobuf:"bytes,12,opt,name=custom_text,json=customText,proto3" json:"custom_text,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *PresentBatchTargetInfo) Reset()         { *m = PresentBatchTargetInfo{} }
func (m *PresentBatchTargetInfo) String() string { return proto.CompactTextString(m) }
func (*PresentBatchTargetInfo) ProtoMessage()    {}
func (*PresentBatchTargetInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_middleware_6a5ad00162c10c32, []int{16}
}
func (m *PresentBatchTargetInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PresentBatchTargetInfo.Unmarshal(m, b)
}
func (m *PresentBatchTargetInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PresentBatchTargetInfo.Marshal(b, m, deterministic)
}
func (dst *PresentBatchTargetInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PresentBatchTargetInfo.Merge(dst, src)
}
func (m *PresentBatchTargetInfo) XXX_Size() int {
	return xxx_messageInfo_PresentBatchTargetInfo.Size(m)
}
func (m *PresentBatchTargetInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_PresentBatchTargetInfo.DiscardUnknown(m)
}

var xxx_messageInfo_PresentBatchTargetInfo proto.InternalMessageInfo

func (m *PresentBatchTargetInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *PresentBatchTargetInfo) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *PresentBatchTargetInfo) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *PresentBatchTargetInfo) GetExtendJson() string {
	if m != nil {
		return m.ExtendJson
	}
	return ""
}

func (m *PresentBatchTargetInfo) GetUserProfile() *UserProfile {
	if m != nil {
		return m.UserProfile
	}
	return nil
}

func (m *PresentBatchTargetInfo) GetCustomText() string {
	if m != nil {
		return m.CustomText
	}
	return ""
}

type PresentSendItemInfo struct {
	ItemId               uint32              `protobuf:"varint,1,opt,name=item_id,json=itemId,proto3" json:"item_id,omitempty"`
	Count                uint32              `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"`
	ShowEffect           uint32              `protobuf:"varint,3,opt,name=show_effect,json=showEffect,proto3" json:"show_effect,omitempty"`
	ShowEffectV2         uint32              `protobuf:"varint,4,opt,name=show_effect_v2,json=showEffectV2,proto3" json:"show_effect_v2,omitempty"`
	FlowId               uint32              `protobuf:"varint,5,opt,name=flow_id,json=flowId,proto3" json:"flow_id,omitempty"`
	IsBatch              bool                `protobuf:"varint,6,opt,name=is_batch,json=isBatch,proto3" json:"is_batch,omitempty"`
	ShowBatchEffect      bool                `protobuf:"varint,7,opt,name=show_batch_effect,json=showBatchEffect,proto3" json:"show_batch_effect,omitempty"`
	SendType             uint32              `protobuf:"varint,8,opt,name=send_type,json=sendType,proto3" json:"send_type,omitempty"`
	DrawPresentPic       *DrawPresentPicture `protobuf:"bytes,9,opt,name=draw_present_pic,json=drawPresentPic,proto3" json:"draw_present_pic,omitempty"`
	DynamicTemplateId    uint32              `protobuf:"varint,10,opt,name=dynamic_template_id,json=dynamicTemplateId,proto3" json:"dynamic_template_id,omitempty"`
	IsVisibleToSender    bool                `protobuf:"varint,11,opt,name=is_visible_to_sender,json=isVisibleToSender,proto3" json:"is_visible_to_sender,omitempty"`
	IsShowSurprise       bool                `protobuf:"varint,12,opt,name=is_show_surprise,json=isShowSurprise,proto3" json:"is_show_surprise,omitempty"`
	SurpriseCount        uint32              `protobuf:"varint,13,opt,name=surprise_count,json=surpriseCount,proto3" json:"surprise_count,omitempty"`
	CustomTextJson       string              `protobuf:"bytes,14,opt,name=custom_text_json,json=customTextJson,proto3" json:"custom_text_json,omitempty"`
	ShowImPreEffect      bool                `protobuf:"varint,15,opt,name=show_im_pre_effect,json=showImPreEffect,proto3" json:"show_im_pre_effect,omitempty"`
	PreEffectText        string              `protobuf:"bytes,16,opt,name=pre_effect_text,json=preEffectText,proto3" json:"pre_effect_text,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *PresentSendItemInfo) Reset()         { *m = PresentSendItemInfo{} }
func (m *PresentSendItemInfo) String() string { return proto.CompactTextString(m) }
func (*PresentSendItemInfo) ProtoMessage()    {}
func (*PresentSendItemInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_middleware_6a5ad00162c10c32, []int{17}
}
func (m *PresentSendItemInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PresentSendItemInfo.Unmarshal(m, b)
}
func (m *PresentSendItemInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PresentSendItemInfo.Marshal(b, m, deterministic)
}
func (dst *PresentSendItemInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PresentSendItemInfo.Merge(dst, src)
}
func (m *PresentSendItemInfo) XXX_Size() int {
	return xxx_messageInfo_PresentSendItemInfo.Size(m)
}
func (m *PresentSendItemInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_PresentSendItemInfo.DiscardUnknown(m)
}

var xxx_messageInfo_PresentSendItemInfo proto.InternalMessageInfo

func (m *PresentSendItemInfo) GetItemId() uint32 {
	if m != nil {
		return m.ItemId
	}
	return 0
}

func (m *PresentSendItemInfo) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *PresentSendItemInfo) GetShowEffect() uint32 {
	if m != nil {
		return m.ShowEffect
	}
	return 0
}

func (m *PresentSendItemInfo) GetShowEffectV2() uint32 {
	if m != nil {
		return m.ShowEffectV2
	}
	return 0
}

func (m *PresentSendItemInfo) GetFlowId() uint32 {
	if m != nil {
		return m.FlowId
	}
	return 0
}

func (m *PresentSendItemInfo) GetIsBatch() bool {
	if m != nil {
		return m.IsBatch
	}
	return false
}

func (m *PresentSendItemInfo) GetShowBatchEffect() bool {
	if m != nil {
		return m.ShowBatchEffect
	}
	return false
}

func (m *PresentSendItemInfo) GetSendType() uint32 {
	if m != nil {
		return m.SendType
	}
	return 0
}

func (m *PresentSendItemInfo) GetDrawPresentPic() *DrawPresentPicture {
	if m != nil {
		return m.DrawPresentPic
	}
	return nil
}

func (m *PresentSendItemInfo) GetDynamicTemplateId() uint32 {
	if m != nil {
		return m.DynamicTemplateId
	}
	return 0
}

func (m *PresentSendItemInfo) GetIsVisibleToSender() bool {
	if m != nil {
		return m.IsVisibleToSender
	}
	return false
}

func (m *PresentSendItemInfo) GetIsShowSurprise() bool {
	if m != nil {
		return m.IsShowSurprise
	}
	return false
}

func (m *PresentSendItemInfo) GetSurpriseCount() uint32 {
	if m != nil {
		return m.SurpriseCount
	}
	return 0
}

func (m *PresentSendItemInfo) GetCustomTextJson() string {
	if m != nil {
		return m.CustomTextJson
	}
	return ""
}

func (m *PresentSendItemInfo) GetShowImPreEffect() bool {
	if m != nil {
		return m.ShowImPreEffect
	}
	return false
}

func (m *PresentSendItemInfo) GetPreEffectText() string {
	if m != nil {
		return m.PreEffectText
	}
	return ""
}

// 批量送礼信息
type PresentBatchInfoMsg struct {
	ItemId               uint32                    `protobuf:"varint,1,opt,name=item_id,json=itemId,proto3" json:"item_id,omitempty"`
	TotalItemCount       uint32                    `protobuf:"varint,2,opt,name=total_item_count,json=totalItemCount,proto3" json:"total_item_count,omitempty"`
	BatchType            uint32                    `protobuf:"varint,3,opt,name=batch_type,json=batchType,proto3" json:"batch_type,omitempty"`
	SendTime             uint64                    `protobuf:"varint,4,opt,name=send_time,json=sendTime,proto3" json:"send_time,omitempty"`
	ChannelId            uint32                    `protobuf:"varint,5,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	SendUid              uint32                    `protobuf:"varint,6,opt,name=send_uid,json=sendUid,proto3" json:"send_uid,omitempty"`
	SendAccount          string                    `protobuf:"bytes,7,opt,name=send_account,json=sendAccount,proto3" json:"send_account,omitempty"`
	SendNickname         string                    `protobuf:"bytes,8,opt,name=send_nickname,json=sendNickname,proto3" json:"send_nickname,omitempty"`
	ExtendJson           string                    `protobuf:"bytes,9,opt,name=extend_json,json=extendJson,proto3" json:"extend_json,omitempty"`
	TargetList           []*PresentBatchTargetInfo `protobuf:"bytes,10,rep,name=target_list,json=targetList,proto3" json:"target_list,omitempty"`
	ItemInfo             *PresentSendItemInfo      `protobuf:"bytes,11,opt,name=item_info,json=itemInfo,proto3" json:"item_info,omitempty"`
	FromUserProfile      *UserProfile              `protobuf:"bytes,12,opt,name=from_user_profile,json=fromUserProfile,proto3" json:"from_user_profile,omitempty"`
	IsMulti              bool                      `protobuf:"varint,13,opt,name=is_multi,json=isMulti,proto3" json:"is_multi,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *PresentBatchInfoMsg) Reset()         { *m = PresentBatchInfoMsg{} }
func (m *PresentBatchInfoMsg) String() string { return proto.CompactTextString(m) }
func (*PresentBatchInfoMsg) ProtoMessage()    {}
func (*PresentBatchInfoMsg) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_middleware_6a5ad00162c10c32, []int{18}
}
func (m *PresentBatchInfoMsg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PresentBatchInfoMsg.Unmarshal(m, b)
}
func (m *PresentBatchInfoMsg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PresentBatchInfoMsg.Marshal(b, m, deterministic)
}
func (dst *PresentBatchInfoMsg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PresentBatchInfoMsg.Merge(dst, src)
}
func (m *PresentBatchInfoMsg) XXX_Size() int {
	return xxx_messageInfo_PresentBatchInfoMsg.Size(m)
}
func (m *PresentBatchInfoMsg) XXX_DiscardUnknown() {
	xxx_messageInfo_PresentBatchInfoMsg.DiscardUnknown(m)
}

var xxx_messageInfo_PresentBatchInfoMsg proto.InternalMessageInfo

func (m *PresentBatchInfoMsg) GetItemId() uint32 {
	if m != nil {
		return m.ItemId
	}
	return 0
}

func (m *PresentBatchInfoMsg) GetTotalItemCount() uint32 {
	if m != nil {
		return m.TotalItemCount
	}
	return 0
}

func (m *PresentBatchInfoMsg) GetBatchType() uint32 {
	if m != nil {
		return m.BatchType
	}
	return 0
}

func (m *PresentBatchInfoMsg) GetSendTime() uint64 {
	if m != nil {
		return m.SendTime
	}
	return 0
}

func (m *PresentBatchInfoMsg) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *PresentBatchInfoMsg) GetSendUid() uint32 {
	if m != nil {
		return m.SendUid
	}
	return 0
}

func (m *PresentBatchInfoMsg) GetSendAccount() string {
	if m != nil {
		return m.SendAccount
	}
	return ""
}

func (m *PresentBatchInfoMsg) GetSendNickname() string {
	if m != nil {
		return m.SendNickname
	}
	return ""
}

func (m *PresentBatchInfoMsg) GetExtendJson() string {
	if m != nil {
		return m.ExtendJson
	}
	return ""
}

func (m *PresentBatchInfoMsg) GetTargetList() []*PresentBatchTargetInfo {
	if m != nil {
		return m.TargetList
	}
	return nil
}

func (m *PresentBatchInfoMsg) GetItemInfo() *PresentSendItemInfo {
	if m != nil {
		return m.ItemInfo
	}
	return nil
}

func (m *PresentBatchInfoMsg) GetFromUserProfile() *UserProfile {
	if m != nil {
		return m.FromUserProfile
	}
	return nil
}

func (m *PresentBatchInfoMsg) GetIsMulti() bool {
	if m != nil {
		return m.IsMulti
	}
	return false
}

type ServiceCtrlInfo struct {
	ClientIp             string   `protobuf:"bytes,1,opt,name=client_ip,json=clientIp,proto3" json:"client_ip,omitempty"`
	ClientPort           uint32   `protobuf:"varint,2,opt,name=client_port,json=clientPort,proto3" json:"client_port,omitempty"`
	DeviceId             []byte   `protobuf:"bytes,3,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	ClientType           uint32   `protobuf:"varint,4,opt,name=client_type,json=clientType,proto3" json:"client_type,omitempty"`
	TerminalType         uint32   `protobuf:"varint,5,opt,name=terminal_type,json=terminalType,proto3" json:"terminal_type,omitempty"`
	ClientId             uint32   `protobuf:"varint,6,opt,name=client_id,json=clientId,proto3" json:"client_id,omitempty"`
	ClientVersion        uint32   `protobuf:"varint,7,opt,name=client_version,json=clientVersion,proto3" json:"client_version,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ServiceCtrlInfo) Reset()         { *m = ServiceCtrlInfo{} }
func (m *ServiceCtrlInfo) String() string { return proto.CompactTextString(m) }
func (*ServiceCtrlInfo) ProtoMessage()    {}
func (*ServiceCtrlInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_middleware_6a5ad00162c10c32, []int{19}
}
func (m *ServiceCtrlInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ServiceCtrlInfo.Unmarshal(m, b)
}
func (m *ServiceCtrlInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ServiceCtrlInfo.Marshal(b, m, deterministic)
}
func (dst *ServiceCtrlInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ServiceCtrlInfo.Merge(dst, src)
}
func (m *ServiceCtrlInfo) XXX_Size() int {
	return xxx_messageInfo_ServiceCtrlInfo.Size(m)
}
func (m *ServiceCtrlInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ServiceCtrlInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ServiceCtrlInfo proto.InternalMessageInfo

func (m *ServiceCtrlInfo) GetClientIp() string {
	if m != nil {
		return m.ClientIp
	}
	return ""
}

func (m *ServiceCtrlInfo) GetClientPort() uint32 {
	if m != nil {
		return m.ClientPort
	}
	return 0
}

func (m *ServiceCtrlInfo) GetDeviceId() []byte {
	if m != nil {
		return m.DeviceId
	}
	return nil
}

func (m *ServiceCtrlInfo) GetClientType() uint32 {
	if m != nil {
		return m.ClientType
	}
	return 0
}

func (m *ServiceCtrlInfo) GetTerminalType() uint32 {
	if m != nil {
		return m.TerminalType
	}
	return 0
}

func (m *ServiceCtrlInfo) GetClientId() uint32 {
	if m != nil {
		return m.ClientId
	}
	return 0
}

func (m *ServiceCtrlInfo) GetClientVersion() uint32 {
	if m != nil {
		return m.ClientVersion
	}
	return 0
}

// 批量赠送礼物
type BatchSendPresentReq struct {
	ItemId               uint32              `protobuf:"varint,1,opt,name=item_id,json=itemId,proto3" json:"item_id,omitempty"`
	ChannelId            uint32              `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Count                uint32              `protobuf:"varint,3,opt,name=count,proto3" json:"count,omitempty"`
	SendSource           uint32              `protobuf:"varint,4,opt,name=send_source,json=sendSource,proto3" json:"send_source,omitempty"`
	ItemSource           uint32              `protobuf:"varint,5,opt,name=item_source,json=itemSource,proto3" json:"item_source,omitempty"`
	SourceId             uint32              `protobuf:"varint,6,opt,name=source_id,json=sourceId,proto3" json:"source_id,omitempty"`
	BatchType            uint32              `protobuf:"varint,7,opt,name=batch_type,json=batchType,proto3" json:"batch_type,omitempty"`
	SendType             uint32              `protobuf:"varint,8,opt,name=send_type,json=sendType,proto3" json:"send_type,omitempty"`
	DrawPresentPic       *DrawPresentPicture `protobuf:"bytes,9,opt,name=draw_present_pic,json=drawPresentPic,proto3" json:"draw_present_pic,omitempty"`
	AppId                uint32              `protobuf:"varint,10,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	MarketId             uint32              `protobuf:"varint,11,opt,name=market_id,json=marketId,proto3" json:"market_id,omitempty"`
	TargetUidList        []uint32            `protobuf:"varint,12,rep,packed,name=target_uid_list,json=targetUidList,proto3" json:"target_uid_list,omitempty"`
	SendUid              uint32              `protobuf:"varint,13,opt,name=send_uid,json=sendUid,proto3" json:"send_uid,omitempty"`
	ServiceInfo          *ServiceCtrlInfo    `protobuf:"bytes,14,opt,name=service_info,json=serviceInfo,proto3" json:"service_info,omitempty"`
	AnotherChannelId     uint32              `protobuf:"varint,15,opt,name=another_channel_id,json=anotherChannelId,proto3" json:"another_channel_id,omitempty"`
	BirthdayType         uint32              `protobuf:"varint,16,opt,name=birthday_type,json=birthdayType,proto3" json:"birthday_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *BatchSendPresentReq) Reset()         { *m = BatchSendPresentReq{} }
func (m *BatchSendPresentReq) String() string { return proto.CompactTextString(m) }
func (*BatchSendPresentReq) ProtoMessage()    {}
func (*BatchSendPresentReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_middleware_6a5ad00162c10c32, []int{20}
}
func (m *BatchSendPresentReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchSendPresentReq.Unmarshal(m, b)
}
func (m *BatchSendPresentReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchSendPresentReq.Marshal(b, m, deterministic)
}
func (dst *BatchSendPresentReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchSendPresentReq.Merge(dst, src)
}
func (m *BatchSendPresentReq) XXX_Size() int {
	return xxx_messageInfo_BatchSendPresentReq.Size(m)
}
func (m *BatchSendPresentReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchSendPresentReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchSendPresentReq proto.InternalMessageInfo

func (m *BatchSendPresentReq) GetItemId() uint32 {
	if m != nil {
		return m.ItemId
	}
	return 0
}

func (m *BatchSendPresentReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *BatchSendPresentReq) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *BatchSendPresentReq) GetSendSource() uint32 {
	if m != nil {
		return m.SendSource
	}
	return 0
}

func (m *BatchSendPresentReq) GetItemSource() uint32 {
	if m != nil {
		return m.ItemSource
	}
	return 0
}

func (m *BatchSendPresentReq) GetSourceId() uint32 {
	if m != nil {
		return m.SourceId
	}
	return 0
}

func (m *BatchSendPresentReq) GetBatchType() uint32 {
	if m != nil {
		return m.BatchType
	}
	return 0
}

func (m *BatchSendPresentReq) GetSendType() uint32 {
	if m != nil {
		return m.SendType
	}
	return 0
}

func (m *BatchSendPresentReq) GetDrawPresentPic() *DrawPresentPicture {
	if m != nil {
		return m.DrawPresentPic
	}
	return nil
}

func (m *BatchSendPresentReq) GetAppId() uint32 {
	if m != nil {
		return m.AppId
	}
	return 0
}

func (m *BatchSendPresentReq) GetMarketId() uint32 {
	if m != nil {
		return m.MarketId
	}
	return 0
}

func (m *BatchSendPresentReq) GetTargetUidList() []uint32 {
	if m != nil {
		return m.TargetUidList
	}
	return nil
}

func (m *BatchSendPresentReq) GetSendUid() uint32 {
	if m != nil {
		return m.SendUid
	}
	return 0
}

func (m *BatchSendPresentReq) GetServiceInfo() *ServiceCtrlInfo {
	if m != nil {
		return m.ServiceInfo
	}
	return nil
}

func (m *BatchSendPresentReq) GetAnotherChannelId() uint32 {
	if m != nil {
		return m.AnotherChannelId
	}
	return 0
}

func (m *BatchSendPresentReq) GetBirthdayType() uint32 {
	if m != nil {
		return m.BirthdayType
	}
	return 0
}

type BatchSendPresentResp struct {
	MsgInfo              *PresentBatchInfoMsg     `protobuf:"bytes,1,opt,name=msg_info,json=msgInfo,proto3" json:"msg_info,omitempty"`
	CurTbeans            uint64                   `protobuf:"varint,2,opt,name=cur_tbeans,json=curTbeans,proto3" json:"cur_tbeans,omitempty"`
	ItemSource           uint32                   `protobuf:"varint,3,opt,name=item_source,json=itemSource,proto3" json:"item_source,omitempty"`
	SourceId             uint32                   `protobuf:"varint,4,opt,name=source_id,json=sourceId,proto3" json:"source_id,omitempty"`
	SourceRemain         uint32                   `protobuf:"varint,5,opt,name=source_remain,json=sourceRemain,proto3" json:"source_remain,omitempty"`
	TargetList           []*PresentTargetUserInfo `protobuf:"bytes,6,rep,name=target_list,json=targetList,proto3" json:"target_list,omitempty"`
	ItemInfo             *PresentSendItemInfo     `protobuf:"bytes,7,opt,name=item_info,json=itemInfo,proto3" json:"item_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *BatchSendPresentResp) Reset()         { *m = BatchSendPresentResp{} }
func (m *BatchSendPresentResp) String() string { return proto.CompactTextString(m) }
func (*BatchSendPresentResp) ProtoMessage()    {}
func (*BatchSendPresentResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_middleware_6a5ad00162c10c32, []int{21}
}
func (m *BatchSendPresentResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchSendPresentResp.Unmarshal(m, b)
}
func (m *BatchSendPresentResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchSendPresentResp.Marshal(b, m, deterministic)
}
func (dst *BatchSendPresentResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchSendPresentResp.Merge(dst, src)
}
func (m *BatchSendPresentResp) XXX_Size() int {
	return xxx_messageInfo_BatchSendPresentResp.Size(m)
}
func (m *BatchSendPresentResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchSendPresentResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchSendPresentResp proto.InternalMessageInfo

func (m *BatchSendPresentResp) GetMsgInfo() *PresentBatchInfoMsg {
	if m != nil {
		return m.MsgInfo
	}
	return nil
}

func (m *BatchSendPresentResp) GetCurTbeans() uint64 {
	if m != nil {
		return m.CurTbeans
	}
	return 0
}

func (m *BatchSendPresentResp) GetItemSource() uint32 {
	if m != nil {
		return m.ItemSource
	}
	return 0
}

func (m *BatchSendPresentResp) GetSourceId() uint32 {
	if m != nil {
		return m.SourceId
	}
	return 0
}

func (m *BatchSendPresentResp) GetSourceRemain() uint32 {
	if m != nil {
		return m.SourceRemain
	}
	return 0
}

func (m *BatchSendPresentResp) GetTargetList() []*PresentTargetUserInfo {
	if m != nil {
		return m.TargetList
	}
	return nil
}

func (m *BatchSendPresentResp) GetItemInfo() *PresentSendItemInfo {
	if m != nil {
		return m.ItemInfo
	}
	return nil
}

// buf:lint:ignore MESSAGE_PASCAL_CASE
type PresentSendBase struct {
	ItemId               uint32              `protobuf:"varint,1,opt,name=item_id,json=itemId,proto3" json:"item_id,omitempty"`
	ChannelId            uint32              `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Count                uint32              `protobuf:"varint,3,opt,name=count,proto3" json:"count,omitempty"`
	SendSource           uint32              `protobuf:"varint,4,opt,name=send_source,json=sendSource,proto3" json:"send_source,omitempty"`
	ItemSource           uint32              `protobuf:"varint,5,opt,name=item_source,json=itemSource,proto3" json:"item_source,omitempty"`
	SourceId             uint32              `protobuf:"varint,6,opt,name=source_id,json=sourceId,proto3" json:"source_id,omitempty"`
	BatchType            uint32              `protobuf:"varint,7,opt,name=batch_type,json=batchType,proto3" json:"batch_type,omitempty"`
	SendType             uint32              `protobuf:"varint,8,opt,name=send_type,json=sendType,proto3" json:"send_type,omitempty"`
	DrawPresentPic       *DrawPresentPicture `protobuf:"bytes,9,opt,name=draw_present_pic,json=drawPresentPic,proto3" json:"draw_present_pic,omitempty"`
	AppId                uint32              `protobuf:"varint,10,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	MarketId             uint32              `protobuf:"varint,11,opt,name=market_id,json=marketId,proto3" json:"market_id,omitempty"`
	TargetUidList        []uint32            `protobuf:"varint,12,rep,packed,name=target_uid_list,json=targetUidList,proto3" json:"target_uid_list,omitempty"`
	SendUid              uint32              `protobuf:"varint,13,opt,name=send_uid,json=sendUid,proto3" json:"send_uid,omitempty"`
	ServiceInfo          *ServiceCtrlInfo    `protobuf:"bytes,14,opt,name=service_info,json=serviceInfo,proto3" json:"service_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *PresentSendBase) Reset()         { *m = PresentSendBase{} }
func (m *PresentSendBase) String() string { return proto.CompactTextString(m) }
func (*PresentSendBase) ProtoMessage()    {}
func (*PresentSendBase) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_middleware_6a5ad00162c10c32, []int{22}
}
func (m *PresentSendBase) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PresentSendBase.Unmarshal(m, b)
}
func (m *PresentSendBase) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PresentSendBase.Marshal(b, m, deterministic)
}
func (dst *PresentSendBase) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PresentSendBase.Merge(dst, src)
}
func (m *PresentSendBase) XXX_Size() int {
	return xxx_messageInfo_PresentSendBase.Size(m)
}
func (m *PresentSendBase) XXX_DiscardUnknown() {
	xxx_messageInfo_PresentSendBase.DiscardUnknown(m)
}

var xxx_messageInfo_PresentSendBase proto.InternalMessageInfo

func (m *PresentSendBase) GetItemId() uint32 {
	if m != nil {
		return m.ItemId
	}
	return 0
}

func (m *PresentSendBase) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *PresentSendBase) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *PresentSendBase) GetSendSource() uint32 {
	if m != nil {
		return m.SendSource
	}
	return 0
}

func (m *PresentSendBase) GetItemSource() uint32 {
	if m != nil {
		return m.ItemSource
	}
	return 0
}

func (m *PresentSendBase) GetSourceId() uint32 {
	if m != nil {
		return m.SourceId
	}
	return 0
}

func (m *PresentSendBase) GetBatchType() uint32 {
	if m != nil {
		return m.BatchType
	}
	return 0
}

func (m *PresentSendBase) GetSendType() uint32 {
	if m != nil {
		return m.SendType
	}
	return 0
}

func (m *PresentSendBase) GetDrawPresentPic() *DrawPresentPicture {
	if m != nil {
		return m.DrawPresentPic
	}
	return nil
}

func (m *PresentSendBase) GetAppId() uint32 {
	if m != nil {
		return m.AppId
	}
	return 0
}

func (m *PresentSendBase) GetMarketId() uint32 {
	if m != nil {
		return m.MarketId
	}
	return 0
}

func (m *PresentSendBase) GetTargetUidList() []uint32 {
	if m != nil {
		return m.TargetUidList
	}
	return nil
}

func (m *PresentSendBase) GetSendUid() uint32 {
	if m != nil {
		return m.SendUid
	}
	return 0
}

func (m *PresentSendBase) GetServiceInfo() *ServiceCtrlInfo {
	if m != nil {
		return m.ServiceInfo
	}
	return nil
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
type ImSendPresentReq struct {
	TargetUid            uint32           `protobuf:"varint,1,opt,name=target_uid,json=targetUid,proto3" json:"target_uid,omitempty"`
	ItemId               uint32           `protobuf:"varint,2,opt,name=item_id,json=itemId,proto3" json:"item_id,omitempty"`
	Count                uint32           `protobuf:"varint,3,opt,name=count,proto3" json:"count,omitempty"`
	SendSource           uint32           `protobuf:"varint,4,opt,name=send_source,json=sendSource,proto3" json:"send_source,omitempty"`
	ItemSource           uint32           `protobuf:"varint,5,opt,name=item_source,json=itemSource,proto3" json:"item_source,omitempty"`
	SourceId             uint32           `protobuf:"varint,6,opt,name=source_id,json=sourceId,proto3" json:"source_id,omitempty"`
	SendType             uint32           `protobuf:"varint,7,opt,name=send_type,json=sendType,proto3" json:"send_type,omitempty"`
	AppId                uint32           `protobuf:"varint,8,opt,name=appId,proto3" json:"appId,omitempty"`
	MarketId             uint32           `protobuf:"varint,9,opt,name=marketId,proto3" json:"marketId,omitempty"`
	SendUid              uint32           `protobuf:"varint,10,opt,name=send_uid,json=sendUid,proto3" json:"send_uid,omitempty"`
	ServiceInfo          *ServiceCtrlInfo `protobuf:"bytes,11,opt,name=service_info,json=serviceInfo,proto3" json:"service_info,omitempty"`
	IsOptValid           bool             `protobuf:"varint,12,opt,name=is_opt_valid,json=isOptValid,proto3" json:"is_opt_valid,omitempty"`
	PresentTextType      uint32           `protobuf:"varint,13,opt,name=present_text_type,json=presentTextType,proto3" json:"present_text_type,omitempty"`
	SendMethod           uint32           `protobuf:"varint,14,opt,name=send_method,json=sendMethod,proto3" json:"send_method,omitempty"`
	ShowImPreEffect      bool             `protobuf:"varint,15,opt,name=show_im_pre_effect,json=showImPreEffect,proto3" json:"show_im_pre_effect,omitempty"`
	PreEffectText        string           `protobuf:"bytes,16,opt,name=pre_effect_text,json=preEffectText,proto3" json:"pre_effect_text,omitempty"`
	BusinessType         uint32           `protobuf:"varint,17,opt,name=business_type,json=businessType,proto3" json:"business_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *ImSendPresentReq) Reset()         { *m = ImSendPresentReq{} }
func (m *ImSendPresentReq) String() string { return proto.CompactTextString(m) }
func (*ImSendPresentReq) ProtoMessage()    {}
func (*ImSendPresentReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_middleware_6a5ad00162c10c32, []int{23}
}
func (m *ImSendPresentReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ImSendPresentReq.Unmarshal(m, b)
}
func (m *ImSendPresentReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ImSendPresentReq.Marshal(b, m, deterministic)
}
func (dst *ImSendPresentReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ImSendPresentReq.Merge(dst, src)
}
func (m *ImSendPresentReq) XXX_Size() int {
	return xxx_messageInfo_ImSendPresentReq.Size(m)
}
func (m *ImSendPresentReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ImSendPresentReq.DiscardUnknown(m)
}

var xxx_messageInfo_ImSendPresentReq proto.InternalMessageInfo

func (m *ImSendPresentReq) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

func (m *ImSendPresentReq) GetItemId() uint32 {
	if m != nil {
		return m.ItemId
	}
	return 0
}

func (m *ImSendPresentReq) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *ImSendPresentReq) GetSendSource() uint32 {
	if m != nil {
		return m.SendSource
	}
	return 0
}

func (m *ImSendPresentReq) GetItemSource() uint32 {
	if m != nil {
		return m.ItemSource
	}
	return 0
}

func (m *ImSendPresentReq) GetSourceId() uint32 {
	if m != nil {
		return m.SourceId
	}
	return 0
}

func (m *ImSendPresentReq) GetSendType() uint32 {
	if m != nil {
		return m.SendType
	}
	return 0
}

func (m *ImSendPresentReq) GetAppId() uint32 {
	if m != nil {
		return m.AppId
	}
	return 0
}

func (m *ImSendPresentReq) GetMarketId() uint32 {
	if m != nil {
		return m.MarketId
	}
	return 0
}

func (m *ImSendPresentReq) GetSendUid() uint32 {
	if m != nil {
		return m.SendUid
	}
	return 0
}

func (m *ImSendPresentReq) GetServiceInfo() *ServiceCtrlInfo {
	if m != nil {
		return m.ServiceInfo
	}
	return nil
}

func (m *ImSendPresentReq) GetIsOptValid() bool {
	if m != nil {
		return m.IsOptValid
	}
	return false
}

func (m *ImSendPresentReq) GetPresentTextType() uint32 {
	if m != nil {
		return m.PresentTextType
	}
	return 0
}

func (m *ImSendPresentReq) GetSendMethod() uint32 {
	if m != nil {
		return m.SendMethod
	}
	return 0
}

func (m *ImSendPresentReq) GetShowImPreEffect() bool {
	if m != nil {
		return m.ShowImPreEffect
	}
	return false
}

func (m *ImSendPresentReq) GetPreEffectText() string {
	if m != nil {
		return m.PreEffectText
	}
	return ""
}

func (m *ImSendPresentReq) GetBusinessType() uint32 {
	if m != nil {
		return m.BusinessType
	}
	return 0
}

type ImSendPresentResp struct {
	ItemId                  uint32          `protobuf:"varint,1,opt,name=item_id,json=itemId,proto3" json:"item_id,omitempty"`
	MsgInfo                 *PresentSendMsg `protobuf:"bytes,2,opt,name=msg_info,json=msgInfo,proto3" json:"msg_info,omitempty"`
	MemberContributionAdded uint32          `protobuf:"varint,3,opt,name=member_contribution_added,json=memberContributionAdded,proto3" json:"member_contribution_added,omitempty"`
	Count                   uint32          `protobuf:"varint,4,opt,name=count,proto3" json:"count,omitempty"`
	CurTbeans               uint64          `protobuf:"varint,5,opt,name=cur_tbeans,json=curTbeans,proto3" json:"cur_tbeans,omitempty"`
	ItemSource              uint32          `protobuf:"varint,6,opt,name=item_source,json=itemSource,proto3" json:"item_source,omitempty"`
	SourceId                uint32          `protobuf:"varint,7,opt,name=source_id,json=sourceId,proto3" json:"source_id,omitempty"`
	SourceRemain            uint32          `protobuf:"varint,8,opt,name=source_remain,json=sourceRemain,proto3" json:"source_remain,omitempty"`
	XXX_NoUnkeyedLiteral    struct{}        `json:"-"`
	XXX_unrecognized        []byte          `json:"-"`
	XXX_sizecache           int32           `json:"-"`
}

func (m *ImSendPresentResp) Reset()         { *m = ImSendPresentResp{} }
func (m *ImSendPresentResp) String() string { return proto.CompactTextString(m) }
func (*ImSendPresentResp) ProtoMessage()    {}
func (*ImSendPresentResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_middleware_6a5ad00162c10c32, []int{24}
}
func (m *ImSendPresentResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ImSendPresentResp.Unmarshal(m, b)
}
func (m *ImSendPresentResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ImSendPresentResp.Marshal(b, m, deterministic)
}
func (dst *ImSendPresentResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ImSendPresentResp.Merge(dst, src)
}
func (m *ImSendPresentResp) XXX_Size() int {
	return xxx_messageInfo_ImSendPresentResp.Size(m)
}
func (m *ImSendPresentResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ImSendPresentResp.DiscardUnknown(m)
}

var xxx_messageInfo_ImSendPresentResp proto.InternalMessageInfo

func (m *ImSendPresentResp) GetItemId() uint32 {
	if m != nil {
		return m.ItemId
	}
	return 0
}

func (m *ImSendPresentResp) GetMsgInfo() *PresentSendMsg {
	if m != nil {
		return m.MsgInfo
	}
	return nil
}

func (m *ImSendPresentResp) GetMemberContributionAdded() uint32 {
	if m != nil {
		return m.MemberContributionAdded
	}
	return 0
}

func (m *ImSendPresentResp) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *ImSendPresentResp) GetCurTbeans() uint64 {
	if m != nil {
		return m.CurTbeans
	}
	return 0
}

func (m *ImSendPresentResp) GetItemSource() uint32 {
	if m != nil {
		return m.ItemSource
	}
	return 0
}

func (m *ImSendPresentResp) GetSourceId() uint32 {
	if m != nil {
		return m.SourceId
	}
	return 0
}

func (m *ImSendPresentResp) GetSourceRemain() uint32 {
	if m != nil {
		return m.SourceRemain
	}
	return 0
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
type FellowSendPresentReq struct {
	TargetUid            uint32           `protobuf:"varint,1,opt,name=target_uid,json=targetUid,proto3" json:"target_uid,omitempty"`
	ItemId               uint32           `protobuf:"varint,2,opt,name=item_id,json=itemId,proto3" json:"item_id,omitempty"`
	Count                uint32           `protobuf:"varint,3,opt,name=count,proto3" json:"count,omitempty"`
	SendSource           uint32           `protobuf:"varint,4,opt,name=send_source,json=sendSource,proto3" json:"send_source,omitempty"`
	ItemSource           uint32           `protobuf:"varint,5,opt,name=item_source,json=itemSource,proto3" json:"item_source,omitempty"`
	SourceId             uint32           `protobuf:"varint,6,opt,name=source_id,json=sourceId,proto3" json:"source_id,omitempty"`
	SendType             uint32           `protobuf:"varint,7,opt,name=send_type,json=sendType,proto3" json:"send_type,omitempty"`
	AppId                uint32           `protobuf:"varint,8,opt,name=appId,proto3" json:"appId,omitempty"`
	MarketId             uint32           `protobuf:"varint,9,opt,name=marketId,proto3" json:"marketId,omitempty"`
	SendUid              uint32           `protobuf:"varint,10,opt,name=send_uid,json=sendUid,proto3" json:"send_uid,omitempty"`
	ServiceInfo          *ServiceCtrlInfo `protobuf:"bytes,11,opt,name=service_info,json=serviceInfo,proto3" json:"service_info,omitempty"`
	IsOptValid           bool             `protobuf:"varint,12,opt,name=is_opt_valid,json=isOptValid,proto3" json:"is_opt_valid,omitempty"`
	OrderId              string           `protobuf:"bytes,13,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	Ctime                string           `protobuf:"bytes,14,opt,name=ctime,proto3" json:"ctime,omitempty"`
	ChannelId            uint32           `protobuf:"varint,15,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	DealToken            string           `protobuf:"bytes,16,opt,name=deal_token,json=dealToken,proto3" json:"deal_token,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *FellowSendPresentReq) Reset()         { *m = FellowSendPresentReq{} }
func (m *FellowSendPresentReq) String() string { return proto.CompactTextString(m) }
func (*FellowSendPresentReq) ProtoMessage()    {}
func (*FellowSendPresentReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_middleware_6a5ad00162c10c32, []int{25}
}
func (m *FellowSendPresentReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FellowSendPresentReq.Unmarshal(m, b)
}
func (m *FellowSendPresentReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FellowSendPresentReq.Marshal(b, m, deterministic)
}
func (dst *FellowSendPresentReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FellowSendPresentReq.Merge(dst, src)
}
func (m *FellowSendPresentReq) XXX_Size() int {
	return xxx_messageInfo_FellowSendPresentReq.Size(m)
}
func (m *FellowSendPresentReq) XXX_DiscardUnknown() {
	xxx_messageInfo_FellowSendPresentReq.DiscardUnknown(m)
}

var xxx_messageInfo_FellowSendPresentReq proto.InternalMessageInfo

func (m *FellowSendPresentReq) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

func (m *FellowSendPresentReq) GetItemId() uint32 {
	if m != nil {
		return m.ItemId
	}
	return 0
}

func (m *FellowSendPresentReq) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *FellowSendPresentReq) GetSendSource() uint32 {
	if m != nil {
		return m.SendSource
	}
	return 0
}

func (m *FellowSendPresentReq) GetItemSource() uint32 {
	if m != nil {
		return m.ItemSource
	}
	return 0
}

func (m *FellowSendPresentReq) GetSourceId() uint32 {
	if m != nil {
		return m.SourceId
	}
	return 0
}

func (m *FellowSendPresentReq) GetSendType() uint32 {
	if m != nil {
		return m.SendType
	}
	return 0
}

func (m *FellowSendPresentReq) GetAppId() uint32 {
	if m != nil {
		return m.AppId
	}
	return 0
}

func (m *FellowSendPresentReq) GetMarketId() uint32 {
	if m != nil {
		return m.MarketId
	}
	return 0
}

func (m *FellowSendPresentReq) GetSendUid() uint32 {
	if m != nil {
		return m.SendUid
	}
	return 0
}

func (m *FellowSendPresentReq) GetServiceInfo() *ServiceCtrlInfo {
	if m != nil {
		return m.ServiceInfo
	}
	return nil
}

func (m *FellowSendPresentReq) GetIsOptValid() bool {
	if m != nil {
		return m.IsOptValid
	}
	return false
}

func (m *FellowSendPresentReq) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *FellowSendPresentReq) GetCtime() string {
	if m != nil {
		return m.Ctime
	}
	return ""
}

func (m *FellowSendPresentReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *FellowSendPresentReq) GetDealToken() string {
	if m != nil {
		return m.DealToken
	}
	return ""
}

type FellowSendPresentResp struct {
	ItemId                  uint32          `protobuf:"varint,1,opt,name=item_id,json=itemId,proto3" json:"item_id,omitempty"`
	MsgInfo                 *PresentSendMsg `protobuf:"bytes,2,opt,name=msg_info,json=msgInfo,proto3" json:"msg_info,omitempty"`
	MemberContributionAdded uint32          `protobuf:"varint,3,opt,name=member_contribution_added,json=memberContributionAdded,proto3" json:"member_contribution_added,omitempty"`
	Count                   uint32          `protobuf:"varint,4,opt,name=count,proto3" json:"count,omitempty"`
	CurTbeans               uint64          `protobuf:"varint,5,opt,name=cur_tbeans,json=curTbeans,proto3" json:"cur_tbeans,omitempty"`
	ItemSource              uint32          `protobuf:"varint,6,opt,name=item_source,json=itemSource,proto3" json:"item_source,omitempty"`
	SourceId                uint32          `protobuf:"varint,7,opt,name=source_id,json=sourceId,proto3" json:"source_id,omitempty"`
	SourceRemain            uint32          `protobuf:"varint,8,opt,name=source_remain,json=sourceRemain,proto3" json:"source_remain,omitempty"`
	XXX_NoUnkeyedLiteral    struct{}        `json:"-"`
	XXX_unrecognized        []byte          `json:"-"`
	XXX_sizecache           int32           `json:"-"`
}

func (m *FellowSendPresentResp) Reset()         { *m = FellowSendPresentResp{} }
func (m *FellowSendPresentResp) String() string { return proto.CompactTextString(m) }
func (*FellowSendPresentResp) ProtoMessage()    {}
func (*FellowSendPresentResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_middleware_6a5ad00162c10c32, []int{26}
}
func (m *FellowSendPresentResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FellowSendPresentResp.Unmarshal(m, b)
}
func (m *FellowSendPresentResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FellowSendPresentResp.Marshal(b, m, deterministic)
}
func (dst *FellowSendPresentResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FellowSendPresentResp.Merge(dst, src)
}
func (m *FellowSendPresentResp) XXX_Size() int {
	return xxx_messageInfo_FellowSendPresentResp.Size(m)
}
func (m *FellowSendPresentResp) XXX_DiscardUnknown() {
	xxx_messageInfo_FellowSendPresentResp.DiscardUnknown(m)
}

var xxx_messageInfo_FellowSendPresentResp proto.InternalMessageInfo

func (m *FellowSendPresentResp) GetItemId() uint32 {
	if m != nil {
		return m.ItemId
	}
	return 0
}

func (m *FellowSendPresentResp) GetMsgInfo() *PresentSendMsg {
	if m != nil {
		return m.MsgInfo
	}
	return nil
}

func (m *FellowSendPresentResp) GetMemberContributionAdded() uint32 {
	if m != nil {
		return m.MemberContributionAdded
	}
	return 0
}

func (m *FellowSendPresentResp) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *FellowSendPresentResp) GetCurTbeans() uint64 {
	if m != nil {
		return m.CurTbeans
	}
	return 0
}

func (m *FellowSendPresentResp) GetItemSource() uint32 {
	if m != nil {
		return m.ItemSource
	}
	return 0
}

func (m *FellowSendPresentResp) GetSourceId() uint32 {
	if m != nil {
		return m.SourceId
	}
	return 0
}

func (m *FellowSendPresentResp) GetSourceRemain() uint32 {
	if m != nil {
		return m.SourceRemain
	}
	return 0
}

type AllMicSendPresentReq struct {
	ItemId               uint32              `protobuf:"varint,1,opt,name=item_id,json=itemId,proto3" json:"item_id,omitempty"`
	ChannelId            uint32              `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Count                uint32              `protobuf:"varint,3,opt,name=count,proto3" json:"count,omitempty"`
	SendSource           uint32              `protobuf:"varint,4,opt,name=send_source,json=sendSource,proto3" json:"send_source,omitempty"`
	ItemSource           uint32              `protobuf:"varint,5,opt,name=item_source,json=itemSource,proto3" json:"item_source,omitempty"`
	SourceId             uint32              `protobuf:"varint,6,opt,name=source_id,json=sourceId,proto3" json:"source_id,omitempty"`
	BatchType            uint32              `protobuf:"varint,7,opt,name=batch_type,json=batchType,proto3" json:"batch_type,omitempty"`
	SendType             uint32              `protobuf:"varint,8,opt,name=send_type,json=sendType,proto3" json:"send_type,omitempty"`
	DrawPresentPic       *DrawPresentPicture `protobuf:"bytes,9,opt,name=draw_present_pic,json=drawPresentPic,proto3" json:"draw_present_pic,omitempty"`
	AppId                uint32              `protobuf:"varint,10,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	MarketId             uint32              `protobuf:"varint,11,opt,name=market_id,json=marketId,proto3" json:"market_id,omitempty"`
	TargetUidList        []uint32            `protobuf:"varint,12,rep,packed,name=target_uid_list,json=targetUidList,proto3" json:"target_uid_list,omitempty"`
	SendUid              uint32              `protobuf:"varint,13,opt,name=send_uid,json=sendUid,proto3" json:"send_uid,omitempty"`
	ServiceInfo          *ServiceCtrlInfo    `protobuf:"bytes,14,opt,name=service_info,json=serviceInfo,proto3" json:"service_info,omitempty"`
	IsOptValid           bool                `protobuf:"varint,15,opt,name=is_opt_valid,json=isOptValid,proto3" json:"is_opt_valid,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *AllMicSendPresentReq) Reset()         { *m = AllMicSendPresentReq{} }
func (m *AllMicSendPresentReq) String() string { return proto.CompactTextString(m) }
func (*AllMicSendPresentReq) ProtoMessage()    {}
func (*AllMicSendPresentReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_middleware_6a5ad00162c10c32, []int{27}
}
func (m *AllMicSendPresentReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AllMicSendPresentReq.Unmarshal(m, b)
}
func (m *AllMicSendPresentReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AllMicSendPresentReq.Marshal(b, m, deterministic)
}
func (dst *AllMicSendPresentReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AllMicSendPresentReq.Merge(dst, src)
}
func (m *AllMicSendPresentReq) XXX_Size() int {
	return xxx_messageInfo_AllMicSendPresentReq.Size(m)
}
func (m *AllMicSendPresentReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AllMicSendPresentReq.DiscardUnknown(m)
}

var xxx_messageInfo_AllMicSendPresentReq proto.InternalMessageInfo

func (m *AllMicSendPresentReq) GetItemId() uint32 {
	if m != nil {
		return m.ItemId
	}
	return 0
}

func (m *AllMicSendPresentReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *AllMicSendPresentReq) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *AllMicSendPresentReq) GetSendSource() uint32 {
	if m != nil {
		return m.SendSource
	}
	return 0
}

func (m *AllMicSendPresentReq) GetItemSource() uint32 {
	if m != nil {
		return m.ItemSource
	}
	return 0
}

func (m *AllMicSendPresentReq) GetSourceId() uint32 {
	if m != nil {
		return m.SourceId
	}
	return 0
}

func (m *AllMicSendPresentReq) GetBatchType() uint32 {
	if m != nil {
		return m.BatchType
	}
	return 0
}

func (m *AllMicSendPresentReq) GetSendType() uint32 {
	if m != nil {
		return m.SendType
	}
	return 0
}

func (m *AllMicSendPresentReq) GetDrawPresentPic() *DrawPresentPicture {
	if m != nil {
		return m.DrawPresentPic
	}
	return nil
}

func (m *AllMicSendPresentReq) GetAppId() uint32 {
	if m != nil {
		return m.AppId
	}
	return 0
}

func (m *AllMicSendPresentReq) GetMarketId() uint32 {
	if m != nil {
		return m.MarketId
	}
	return 0
}

func (m *AllMicSendPresentReq) GetTargetUidList() []uint32 {
	if m != nil {
		return m.TargetUidList
	}
	return nil
}

func (m *AllMicSendPresentReq) GetSendUid() uint32 {
	if m != nil {
		return m.SendUid
	}
	return 0
}

func (m *AllMicSendPresentReq) GetServiceInfo() *ServiceCtrlInfo {
	if m != nil {
		return m.ServiceInfo
	}
	return nil
}

func (m *AllMicSendPresentReq) GetIsOptValid() bool {
	if m != nil {
		return m.IsOptValid
	}
	return false
}

type AllMicSendPresentResp struct {
	MsgInfo              *PresentBatchInfoMsg     `protobuf:"bytes,1,opt,name=msg_info,json=msgInfo,proto3" json:"msg_info,omitempty"`
	CurTbeans            uint64                   `protobuf:"varint,2,opt,name=cur_tbeans,json=curTbeans,proto3" json:"cur_tbeans,omitempty"`
	ItemSource           uint32                   `protobuf:"varint,3,opt,name=item_source,json=itemSource,proto3" json:"item_source,omitempty"`
	SourceId             uint32                   `protobuf:"varint,4,opt,name=source_id,json=sourceId,proto3" json:"source_id,omitempty"`
	SourceRemain         uint32                   `protobuf:"varint,5,opt,name=source_remain,json=sourceRemain,proto3" json:"source_remain,omitempty"`
	TargetList           []*PresentTargetUserInfo `protobuf:"bytes,6,rep,name=target_list,json=targetList,proto3" json:"target_list,omitempty"`
	ItemInfo             *PresentSendItemInfo     `protobuf:"bytes,7,opt,name=item_info,json=itemInfo,proto3" json:"item_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *AllMicSendPresentResp) Reset()         { *m = AllMicSendPresentResp{} }
func (m *AllMicSendPresentResp) String() string { return proto.CompactTextString(m) }
func (*AllMicSendPresentResp) ProtoMessage()    {}
func (*AllMicSendPresentResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_middleware_6a5ad00162c10c32, []int{28}
}
func (m *AllMicSendPresentResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AllMicSendPresentResp.Unmarshal(m, b)
}
func (m *AllMicSendPresentResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AllMicSendPresentResp.Marshal(b, m, deterministic)
}
func (dst *AllMicSendPresentResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AllMicSendPresentResp.Merge(dst, src)
}
func (m *AllMicSendPresentResp) XXX_Size() int {
	return xxx_messageInfo_AllMicSendPresentResp.Size(m)
}
func (m *AllMicSendPresentResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AllMicSendPresentResp.DiscardUnknown(m)
}

var xxx_messageInfo_AllMicSendPresentResp proto.InternalMessageInfo

func (m *AllMicSendPresentResp) GetMsgInfo() *PresentBatchInfoMsg {
	if m != nil {
		return m.MsgInfo
	}
	return nil
}

func (m *AllMicSendPresentResp) GetCurTbeans() uint64 {
	if m != nil {
		return m.CurTbeans
	}
	return 0
}

func (m *AllMicSendPresentResp) GetItemSource() uint32 {
	if m != nil {
		return m.ItemSource
	}
	return 0
}

func (m *AllMicSendPresentResp) GetSourceId() uint32 {
	if m != nil {
		return m.SourceId
	}
	return 0
}

func (m *AllMicSendPresentResp) GetSourceRemain() uint32 {
	if m != nil {
		return m.SourceRemain
	}
	return 0
}

func (m *AllMicSendPresentResp) GetTargetList() []*PresentTargetUserInfo {
	if m != nil {
		return m.TargetList
	}
	return nil
}

func (m *AllMicSendPresentResp) GetItemInfo() *PresentSendItemInfo {
	if m != nil {
		return m.ItemInfo
	}
	return nil
}

type MagicSendPresentReq struct {
	GiftItemList         []*GiftItem      `protobuf:"bytes,1,rep,name=gift_item_list,json=giftItemList,proto3" json:"gift_item_list,omitempty"`
	ChannelId            uint32           `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	SendUid              uint32           `protobuf:"varint,3,opt,name=send_uid,json=sendUid,proto3" json:"send_uid,omitempty"`
	AppId                uint32           `protobuf:"varint,4,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	MarketId             uint32           `protobuf:"varint,5,opt,name=market_id,json=marketId,proto3" json:"market_id,omitempty"`
	ServiceInfo          *ServiceCtrlInfo `protobuf:"bytes,6,opt,name=service_info,json=serviceInfo,proto3" json:"service_info,omitempty"`
	ConsumeOrderId       string           `protobuf:"bytes,7,opt,name=consume_order_id,json=consumeOrderId,proto3" json:"consume_order_id,omitempty"`
	CombConfig           []byte           `protobuf:"bytes,8,opt,name=comb_config,json=combConfig,proto3" json:"comb_config,omitempty"`
	IsBatch              bool             `protobuf:"varint,9,opt,name=is_batch,json=isBatch,proto3" json:"is_batch,omitempty"`
	MagicSpiritId        uint32           `protobuf:"varint,10,opt,name=magic_spirit_id,json=magicSpiritId,proto3" json:"magic_spirit_id,omitempty"`
	MagicSpiritCnt       uint32           `protobuf:"varint,11,opt,name=magic_spirit_cnt,json=magicSpiritCnt,proto3" json:"magic_spirit_cnt,omitempty"`
	MagicSpiritName      string           `protobuf:"bytes,12,opt,name=magic_spirit_name,json=magicSpiritName,proto3" json:"magic_spirit_name,omitempty"`
	MagicSpiritIcon      string           `protobuf:"bytes,13,opt,name=magic_spirit_icon,json=magicSpiritIcon,proto3" json:"magic_spirit_icon,omitempty"`
	SendTime             uint32           `protobuf:"varint,14,opt,name=send_time,json=sendTime,proto3" json:"send_time,omitempty"`
	BindChannelId        uint32           `protobuf:"varint,15,opt,name=bind_channel_id,json=bindChannelId,proto3" json:"bind_channel_id,omitempty"`
	SysAutoSend          bool             `protobuf:"varint,16,opt,name=sys_auto_send,json=sysAutoSend,proto3" json:"sys_auto_send,omitempty"`
	ItemSource           uint32           `protobuf:"varint,17,opt,name=item_source,json=itemSource,proto3" json:"item_source,omitempty"`
	IsMulti              bool             `protobuf:"varint,18,opt,name=is_multi,json=isMulti,proto3" json:"is_multi,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *MagicSendPresentReq) Reset()         { *m = MagicSendPresentReq{} }
func (m *MagicSendPresentReq) String() string { return proto.CompactTextString(m) }
func (*MagicSendPresentReq) ProtoMessage()    {}
func (*MagicSendPresentReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_middleware_6a5ad00162c10c32, []int{29}
}
func (m *MagicSendPresentReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MagicSendPresentReq.Unmarshal(m, b)
}
func (m *MagicSendPresentReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MagicSendPresentReq.Marshal(b, m, deterministic)
}
func (dst *MagicSendPresentReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MagicSendPresentReq.Merge(dst, src)
}
func (m *MagicSendPresentReq) XXX_Size() int {
	return xxx_messageInfo_MagicSendPresentReq.Size(m)
}
func (m *MagicSendPresentReq) XXX_DiscardUnknown() {
	xxx_messageInfo_MagicSendPresentReq.DiscardUnknown(m)
}

var xxx_messageInfo_MagicSendPresentReq proto.InternalMessageInfo

func (m *MagicSendPresentReq) GetGiftItemList() []*GiftItem {
	if m != nil {
		return m.GiftItemList
	}
	return nil
}

func (m *MagicSendPresentReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *MagicSendPresentReq) GetSendUid() uint32 {
	if m != nil {
		return m.SendUid
	}
	return 0
}

func (m *MagicSendPresentReq) GetAppId() uint32 {
	if m != nil {
		return m.AppId
	}
	return 0
}

func (m *MagicSendPresentReq) GetMarketId() uint32 {
	if m != nil {
		return m.MarketId
	}
	return 0
}

func (m *MagicSendPresentReq) GetServiceInfo() *ServiceCtrlInfo {
	if m != nil {
		return m.ServiceInfo
	}
	return nil
}

func (m *MagicSendPresentReq) GetConsumeOrderId() string {
	if m != nil {
		return m.ConsumeOrderId
	}
	return ""
}

func (m *MagicSendPresentReq) GetCombConfig() []byte {
	if m != nil {
		return m.CombConfig
	}
	return nil
}

func (m *MagicSendPresentReq) GetIsBatch() bool {
	if m != nil {
		return m.IsBatch
	}
	return false
}

func (m *MagicSendPresentReq) GetMagicSpiritId() uint32 {
	if m != nil {
		return m.MagicSpiritId
	}
	return 0
}

func (m *MagicSendPresentReq) GetMagicSpiritCnt() uint32 {
	if m != nil {
		return m.MagicSpiritCnt
	}
	return 0
}

func (m *MagicSendPresentReq) GetMagicSpiritName() string {
	if m != nil {
		return m.MagicSpiritName
	}
	return ""
}

func (m *MagicSendPresentReq) GetMagicSpiritIcon() string {
	if m != nil {
		return m.MagicSpiritIcon
	}
	return ""
}

func (m *MagicSendPresentReq) GetSendTime() uint32 {
	if m != nil {
		return m.SendTime
	}
	return 0
}

func (m *MagicSendPresentReq) GetBindChannelId() uint32 {
	if m != nil {
		return m.BindChannelId
	}
	return 0
}

func (m *MagicSendPresentReq) GetSysAutoSend() bool {
	if m != nil {
		return m.SysAutoSend
	}
	return false
}

func (m *MagicSendPresentReq) GetItemSource() uint32 {
	if m != nil {
		return m.ItemSource
	}
	return 0
}

func (m *MagicSendPresentReq) GetIsMulti() bool {
	if m != nil {
		return m.IsMulti
	}
	return false
}

type GiftItem struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ItemId               uint32   `protobuf:"varint,2,opt,name=item_id,json=itemId,proto3" json:"item_id,omitempty"`
	OrderId              string   `protobuf:"bytes,3,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	Count                uint32   `protobuf:"varint,4,opt,name=count,proto3" json:"count,omitempty"`
	AwardEffect          uint32   `protobuf:"varint,5,opt,name=award_effect,json=awardEffect,proto3" json:"award_effect,omitempty"`
	DealToken            string   `protobuf:"bytes,6,opt,name=deal_token,json=dealToken,proto3" json:"deal_token,omitempty"`
	BreakingNewsId       uint32   `protobuf:"varint,7,opt,name=breaking_news_id,json=breakingNewsId,proto3" json:"breaking_news_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GiftItem) Reset()         { *m = GiftItem{} }
func (m *GiftItem) String() string { return proto.CompactTextString(m) }
func (*GiftItem) ProtoMessage()    {}
func (*GiftItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_middleware_6a5ad00162c10c32, []int{30}
}
func (m *GiftItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GiftItem.Unmarshal(m, b)
}
func (m *GiftItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GiftItem.Marshal(b, m, deterministic)
}
func (dst *GiftItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GiftItem.Merge(dst, src)
}
func (m *GiftItem) XXX_Size() int {
	return xxx_messageInfo_GiftItem.Size(m)
}
func (m *GiftItem) XXX_DiscardUnknown() {
	xxx_messageInfo_GiftItem.DiscardUnknown(m)
}

var xxx_messageInfo_GiftItem proto.InternalMessageInfo

func (m *GiftItem) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GiftItem) GetItemId() uint32 {
	if m != nil {
		return m.ItemId
	}
	return 0
}

func (m *GiftItem) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *GiftItem) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *GiftItem) GetAwardEffect() uint32 {
	if m != nil {
		return m.AwardEffect
	}
	return 0
}

func (m *GiftItem) GetDealToken() string {
	if m != nil {
		return m.DealToken
	}
	return ""
}

func (m *GiftItem) GetBreakingNewsId() uint32 {
	if m != nil {
		return m.BreakingNewsId
	}
	return 0
}

type MagicSendPresentResp struct {
	MsgInfo              *MagicPresentInfoMsg `protobuf:"bytes,1,opt,name=msg_info,json=msgInfo,proto3" json:"msg_info,omitempty"`
	SpiritOpt            *SendMagicSpiritOpt  `protobuf:"bytes,2,opt,name=spirit_opt,json=spiritOpt,proto3" json:"spirit_opt,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *MagicSendPresentResp) Reset()         { *m = MagicSendPresentResp{} }
func (m *MagicSendPresentResp) String() string { return proto.CompactTextString(m) }
func (*MagicSendPresentResp) ProtoMessage()    {}
func (*MagicSendPresentResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_middleware_6a5ad00162c10c32, []int{31}
}
func (m *MagicSendPresentResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MagicSendPresentResp.Unmarshal(m, b)
}
func (m *MagicSendPresentResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MagicSendPresentResp.Marshal(b, m, deterministic)
}
func (dst *MagicSendPresentResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MagicSendPresentResp.Merge(dst, src)
}
func (m *MagicSendPresentResp) XXX_Size() int {
	return xxx_messageInfo_MagicSendPresentResp.Size(m)
}
func (m *MagicSendPresentResp) XXX_DiscardUnknown() {
	xxx_messageInfo_MagicSendPresentResp.DiscardUnknown(m)
}

var xxx_messageInfo_MagicSendPresentResp proto.InternalMessageInfo

func (m *MagicSendPresentResp) GetMsgInfo() *MagicPresentInfoMsg {
	if m != nil {
		return m.MsgInfo
	}
	return nil
}

func (m *MagicSendPresentResp) GetSpiritOpt() *SendMagicSpiritOpt {
	if m != nil {
		return m.SpiritOpt
	}
	return nil
}

// 幸运礼物回包信息
type MagicPresentInfoMsg struct {
	TotalItemCount       uint32   `protobuf:"varint,1,opt,name=total_item_count,json=totalItemCount,proto3" json:"total_item_count,omitempty"`
	BatchType            uint32   `protobuf:"varint,2,opt,name=batch_type,json=batchType,proto3" json:"batch_type,omitempty"`
	SendTime             uint64   `protobuf:"varint,3,opt,name=send_time,json=sendTime,proto3" json:"send_time,omitempty"`
	ChannelId            uint32   `protobuf:"varint,5,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	SendUid              uint32   `protobuf:"varint,6,opt,name=send_uid,json=sendUid,proto3" json:"send_uid,omitempty"`
	SendAccount          string   `protobuf:"bytes,7,opt,name=send_account,json=sendAccount,proto3" json:"send_account,omitempty"`
	SendNickname         string   `protobuf:"bytes,8,opt,name=send_nickname,json=sendNickname,proto3" json:"send_nickname,omitempty"`
	ExtendJson           string   `protobuf:"bytes,9,opt,name=extend_json,json=extendJson,proto3" json:"extend_json,omitempty"`
	TotalPrice           uint32   `protobuf:"varint,10,opt,name=total_price,json=totalPrice,proto3" json:"total_price,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MagicPresentInfoMsg) Reset()         { *m = MagicPresentInfoMsg{} }
func (m *MagicPresentInfoMsg) String() string { return proto.CompactTextString(m) }
func (*MagicPresentInfoMsg) ProtoMessage()    {}
func (*MagicPresentInfoMsg) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_middleware_6a5ad00162c10c32, []int{32}
}
func (m *MagicPresentInfoMsg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MagicPresentInfoMsg.Unmarshal(m, b)
}
func (m *MagicPresentInfoMsg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MagicPresentInfoMsg.Marshal(b, m, deterministic)
}
func (dst *MagicPresentInfoMsg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MagicPresentInfoMsg.Merge(dst, src)
}
func (m *MagicPresentInfoMsg) XXX_Size() int {
	return xxx_messageInfo_MagicPresentInfoMsg.Size(m)
}
func (m *MagicPresentInfoMsg) XXX_DiscardUnknown() {
	xxx_messageInfo_MagicPresentInfoMsg.DiscardUnknown(m)
}

var xxx_messageInfo_MagicPresentInfoMsg proto.InternalMessageInfo

func (m *MagicPresentInfoMsg) GetTotalItemCount() uint32 {
	if m != nil {
		return m.TotalItemCount
	}
	return 0
}

func (m *MagicPresentInfoMsg) GetBatchType() uint32 {
	if m != nil {
		return m.BatchType
	}
	return 0
}

func (m *MagicPresentInfoMsg) GetSendTime() uint64 {
	if m != nil {
		return m.SendTime
	}
	return 0
}

func (m *MagicPresentInfoMsg) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *MagicPresentInfoMsg) GetSendUid() uint32 {
	if m != nil {
		return m.SendUid
	}
	return 0
}

func (m *MagicPresentInfoMsg) GetSendAccount() string {
	if m != nil {
		return m.SendAccount
	}
	return ""
}

func (m *MagicPresentInfoMsg) GetSendNickname() string {
	if m != nil {
		return m.SendNickname
	}
	return ""
}

func (m *MagicPresentInfoMsg) GetExtendJson() string {
	if m != nil {
		return m.ExtendJson
	}
	return ""
}

func (m *MagicPresentInfoMsg) GetTotalPrice() uint32 {
	if m != nil {
		return m.TotalPrice
	}
	return 0
}

type SendMagicSpiritOpt struct {
	MagicSpiritId        uint32                 `protobuf:"varint,1,opt,name=magic_spirit_id,json=magicSpiritId,proto3" json:"magic_spirit_id,omitempty"`
	MagicSpiritCnt       uint32                 `protobuf:"varint,2,opt,name=magic_spirit_cnt,json=magicSpiritCnt,proto3" json:"magic_spirit_cnt,omitempty"`
	SendUid              uint32                 `protobuf:"varint,3,opt,name=send_uid,json=sendUid,proto3" json:"send_uid,omitempty"`
	SendAccount          string                 `protobuf:"bytes,4,opt,name=send_account,json=sendAccount,proto3" json:"send_account,omitempty"`
	SendNickname         string                 `protobuf:"bytes,5,opt,name=send_nickname,json=sendNickname,proto3" json:"send_nickname,omitempty"`
	ChannelId            uint32                 `protobuf:"varint,6,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	IsBatch              bool                   `protobuf:"varint,7,opt,name=is_batch,json=isBatch,proto3" json:"is_batch,omitempty"`
	ItemList             []*MagicSpiritSendItem `protobuf:"bytes,8,rep,name=item_list,json=itemList,proto3" json:"item_list,omitempty"`
	CombInfo             []byte                 `protobuf:"bytes,9,opt,name=comb_info,json=combInfo,proto3" json:"comb_info,omitempty"`
	TotalItemPrice       uint32                 `protobuf:"varint,10,opt,name=total_item_price,json=totalItemPrice,proto3" json:"total_item_price,omitempty"`
	SysAutoSend          bool                   `protobuf:"varint,11,opt,name=sys_auto_send,json=sysAutoSend,proto3" json:"sys_auto_send,omitempty"`
	FromUserProfile      *UserProfile           `protobuf:"bytes,12,opt,name=from_user_profile,json=fromUserProfile,proto3" json:"from_user_profile,omitempty"`
	IsMulti              bool                   `protobuf:"varint,13,opt,name=is_multi,json=isMulti,proto3" json:"is_multi,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *SendMagicSpiritOpt) Reset()         { *m = SendMagicSpiritOpt{} }
func (m *SendMagicSpiritOpt) String() string { return proto.CompactTextString(m) }
func (*SendMagicSpiritOpt) ProtoMessage()    {}
func (*SendMagicSpiritOpt) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_middleware_6a5ad00162c10c32, []int{33}
}
func (m *SendMagicSpiritOpt) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SendMagicSpiritOpt.Unmarshal(m, b)
}
func (m *SendMagicSpiritOpt) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SendMagicSpiritOpt.Marshal(b, m, deterministic)
}
func (dst *SendMagicSpiritOpt) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendMagicSpiritOpt.Merge(dst, src)
}
func (m *SendMagicSpiritOpt) XXX_Size() int {
	return xxx_messageInfo_SendMagicSpiritOpt.Size(m)
}
func (m *SendMagicSpiritOpt) XXX_DiscardUnknown() {
	xxx_messageInfo_SendMagicSpiritOpt.DiscardUnknown(m)
}

var xxx_messageInfo_SendMagicSpiritOpt proto.InternalMessageInfo

func (m *SendMagicSpiritOpt) GetMagicSpiritId() uint32 {
	if m != nil {
		return m.MagicSpiritId
	}
	return 0
}

func (m *SendMagicSpiritOpt) GetMagicSpiritCnt() uint32 {
	if m != nil {
		return m.MagicSpiritCnt
	}
	return 0
}

func (m *SendMagicSpiritOpt) GetSendUid() uint32 {
	if m != nil {
		return m.SendUid
	}
	return 0
}

func (m *SendMagicSpiritOpt) GetSendAccount() string {
	if m != nil {
		return m.SendAccount
	}
	return ""
}

func (m *SendMagicSpiritOpt) GetSendNickname() string {
	if m != nil {
		return m.SendNickname
	}
	return ""
}

func (m *SendMagicSpiritOpt) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SendMagicSpiritOpt) GetIsBatch() bool {
	if m != nil {
		return m.IsBatch
	}
	return false
}

func (m *SendMagicSpiritOpt) GetItemList() []*MagicSpiritSendItem {
	if m != nil {
		return m.ItemList
	}
	return nil
}

func (m *SendMagicSpiritOpt) GetCombInfo() []byte {
	if m != nil {
		return m.CombInfo
	}
	return nil
}

func (m *SendMagicSpiritOpt) GetTotalItemPrice() uint32 {
	if m != nil {
		return m.TotalItemPrice
	}
	return 0
}

func (m *SendMagicSpiritOpt) GetSysAutoSend() bool {
	if m != nil {
		return m.SysAutoSend
	}
	return false
}

func (m *SendMagicSpiritOpt) GetFromUserProfile() *UserProfile {
	if m != nil {
		return m.FromUserProfile
	}
	return nil
}

func (m *SendMagicSpiritOpt) GetIsMulti() bool {
	if m != nil {
		return m.IsMulti
	}
	return false
}

type MagicSpiritSendItem struct {
	ItemId               uint32                  `protobuf:"varint,1,opt,name=item_id,json=itemId,proto3" json:"item_id,omitempty"`
	ShowEffect           uint32                  `protobuf:"varint,2,opt,name=show_effect,json=showEffect,proto3" json:"show_effect,omitempty"`
	ShowEffectV2         uint32                  `protobuf:"varint,3,opt,name=show_effect_v2,json=showEffectV2,proto3" json:"show_effect_v2,omitempty"`
	FlowId               uint32                  `protobuf:"varint,4,opt,name=flow_id,json=flowId,proto3" json:"flow_id,omitempty"`
	IsBatch              bool                    `protobuf:"varint,5,opt,name=is_batch,json=isBatch,proto3" json:"is_batch,omitempty"`
	ShowBatchEffect      bool                    `protobuf:"varint,6,opt,name=show_batch_effect,json=showBatchEffect,proto3" json:"show_batch_effect,omitempty"`
	SendType             uint32                  `protobuf:"varint,7,opt,name=send_type,json=sendType,proto3" json:"send_type,omitempty"`
	DynamicTemplateId    uint32                  `protobuf:"varint,8,opt,name=dynamic_template_id,json=dynamicTemplateId,proto3" json:"dynamic_template_id,omitempty"`
	IsVisibleToSender    bool                    `protobuf:"varint,9,opt,name=is_visible_to_sender,json=isVisibleToSender,proto3" json:"is_visible_to_sender,omitempty"`
	AwardList            []*MagicSpiritAwardInfo `protobuf:"bytes,10,rep,name=award_list,json=awardList,proto3" json:"award_list,omitempty"`
	Effect               uint32                  `protobuf:"varint,11,opt,name=effect,proto3" json:"effect,omitempty"`
	AwardText            string                  `protobuf:"bytes,12,opt,name=award_text,json=awardText,proto3" json:"award_text,omitempty"`
	ExternInfo           string                  `protobuf:"bytes,13,opt,name=extern_info,json=externInfo,proto3" json:"extern_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *MagicSpiritSendItem) Reset()         { *m = MagicSpiritSendItem{} }
func (m *MagicSpiritSendItem) String() string { return proto.CompactTextString(m) }
func (*MagicSpiritSendItem) ProtoMessage()    {}
func (*MagicSpiritSendItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_middleware_6a5ad00162c10c32, []int{34}
}
func (m *MagicSpiritSendItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MagicSpiritSendItem.Unmarshal(m, b)
}
func (m *MagicSpiritSendItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MagicSpiritSendItem.Marshal(b, m, deterministic)
}
func (dst *MagicSpiritSendItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MagicSpiritSendItem.Merge(dst, src)
}
func (m *MagicSpiritSendItem) XXX_Size() int {
	return xxx_messageInfo_MagicSpiritSendItem.Size(m)
}
func (m *MagicSpiritSendItem) XXX_DiscardUnknown() {
	xxx_messageInfo_MagicSpiritSendItem.DiscardUnknown(m)
}

var xxx_messageInfo_MagicSpiritSendItem proto.InternalMessageInfo

func (m *MagicSpiritSendItem) GetItemId() uint32 {
	if m != nil {
		return m.ItemId
	}
	return 0
}

func (m *MagicSpiritSendItem) GetShowEffect() uint32 {
	if m != nil {
		return m.ShowEffect
	}
	return 0
}

func (m *MagicSpiritSendItem) GetShowEffectV2() uint32 {
	if m != nil {
		return m.ShowEffectV2
	}
	return 0
}

func (m *MagicSpiritSendItem) GetFlowId() uint32 {
	if m != nil {
		return m.FlowId
	}
	return 0
}

func (m *MagicSpiritSendItem) GetIsBatch() bool {
	if m != nil {
		return m.IsBatch
	}
	return false
}

func (m *MagicSpiritSendItem) GetShowBatchEffect() bool {
	if m != nil {
		return m.ShowBatchEffect
	}
	return false
}

func (m *MagicSpiritSendItem) GetSendType() uint32 {
	if m != nil {
		return m.SendType
	}
	return 0
}

func (m *MagicSpiritSendItem) GetDynamicTemplateId() uint32 {
	if m != nil {
		return m.DynamicTemplateId
	}
	return 0
}

func (m *MagicSpiritSendItem) GetIsVisibleToSender() bool {
	if m != nil {
		return m.IsVisibleToSender
	}
	return false
}

func (m *MagicSpiritSendItem) GetAwardList() []*MagicSpiritAwardInfo {
	if m != nil {
		return m.AwardList
	}
	return nil
}

func (m *MagicSpiritSendItem) GetEffect() uint32 {
	if m != nil {
		return m.Effect
	}
	return 0
}

func (m *MagicSpiritSendItem) GetAwardText() string {
	if m != nil {
		return m.AwardText
	}
	return ""
}

func (m *MagicSpiritSendItem) GetExternInfo() string {
	if m != nil {
		return m.ExternInfo
	}
	return ""
}

type MagicSpiritAwardInfo struct {
	ItemId               uint32       `protobuf:"varint,1,opt,name=item_id,json=itemId,proto3" json:"item_id,omitempty"`
	Count                uint32       `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"`
	SendTime             uint64       `protobuf:"varint,3,opt,name=send_time,json=sendTime,proto3" json:"send_time,omitempty"`
	ChannelId            uint32       `protobuf:"varint,4,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	TargetUid            uint32       `protobuf:"varint,5,opt,name=target_uid,json=targetUid,proto3" json:"target_uid,omitempty"`
	TargetAccount        string       `protobuf:"bytes,6,opt,name=target_account,json=targetAccount,proto3" json:"target_account,omitempty"`
	TargetNickname       string       `protobuf:"bytes,7,opt,name=target_nickname,json=targetNickname,proto3" json:"target_nickname,omitempty"`
	ItemOrderId          string       `protobuf:"bytes,8,opt,name=item_order_id,json=itemOrderId,proto3" json:"item_order_id,omitempty"`
	ExtendJson           string       `protobuf:"bytes,9,opt,name=extend_json,json=extendJson,proto3" json:"extend_json,omitempty"`
	UserProfile          *UserProfile `protobuf:"bytes,10,opt,name=user_profile,json=userProfile,proto3" json:"user_profile,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *MagicSpiritAwardInfo) Reset()         { *m = MagicSpiritAwardInfo{} }
func (m *MagicSpiritAwardInfo) String() string { return proto.CompactTextString(m) }
func (*MagicSpiritAwardInfo) ProtoMessage()    {}
func (*MagicSpiritAwardInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_middleware_6a5ad00162c10c32, []int{35}
}
func (m *MagicSpiritAwardInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MagicSpiritAwardInfo.Unmarshal(m, b)
}
func (m *MagicSpiritAwardInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MagicSpiritAwardInfo.Marshal(b, m, deterministic)
}
func (dst *MagicSpiritAwardInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MagicSpiritAwardInfo.Merge(dst, src)
}
func (m *MagicSpiritAwardInfo) XXX_Size() int {
	return xxx_messageInfo_MagicSpiritAwardInfo.Size(m)
}
func (m *MagicSpiritAwardInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_MagicSpiritAwardInfo.DiscardUnknown(m)
}

var xxx_messageInfo_MagicSpiritAwardInfo proto.InternalMessageInfo

func (m *MagicSpiritAwardInfo) GetItemId() uint32 {
	if m != nil {
		return m.ItemId
	}
	return 0
}

func (m *MagicSpiritAwardInfo) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *MagicSpiritAwardInfo) GetSendTime() uint64 {
	if m != nil {
		return m.SendTime
	}
	return 0
}

func (m *MagicSpiritAwardInfo) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *MagicSpiritAwardInfo) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

func (m *MagicSpiritAwardInfo) GetTargetAccount() string {
	if m != nil {
		return m.TargetAccount
	}
	return ""
}

func (m *MagicSpiritAwardInfo) GetTargetNickname() string {
	if m != nil {
		return m.TargetNickname
	}
	return ""
}

func (m *MagicSpiritAwardInfo) GetItemOrderId() string {
	if m != nil {
		return m.ItemOrderId
	}
	return ""
}

func (m *MagicSpiritAwardInfo) GetExtendJson() string {
	if m != nil {
		return m.ExtendJson
	}
	return ""
}

func (m *MagicSpiritAwardInfo) GetUserProfile() *UserProfile {
	if m != nil {
		return m.UserProfile
	}
	return nil
}

type UserProfile struct {
	Uid                  uint32         `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Account              string         `protobuf:"bytes,2,opt,name=account,proto3" json:"account,omitempty"`
	Nickname             string         `protobuf:"bytes,3,opt,name=nickname,proto3" json:"nickname,omitempty"`
	AccountAlias         string         `protobuf:"bytes,4,opt,name=account_alias,json=accountAlias,proto3" json:"account_alias,omitempty"`
	Sex                  uint32         `protobuf:"varint,5,opt,name=sex,proto3" json:"sex,omitempty"`
	Privilege            *UserPrivilege `protobuf:"bytes,6,opt,name=privilege,proto3" json:"privilege,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *UserProfile) Reset()         { *m = UserProfile{} }
func (m *UserProfile) String() string { return proto.CompactTextString(m) }
func (*UserProfile) ProtoMessage()    {}
func (*UserProfile) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_middleware_6a5ad00162c10c32, []int{36}
}
func (m *UserProfile) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserProfile.Unmarshal(m, b)
}
func (m *UserProfile) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserProfile.Marshal(b, m, deterministic)
}
func (dst *UserProfile) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserProfile.Merge(dst, src)
}
func (m *UserProfile) XXX_Size() int {
	return xxx_messageInfo_UserProfile.Size(m)
}
func (m *UserProfile) XXX_DiscardUnknown() {
	xxx_messageInfo_UserProfile.DiscardUnknown(m)
}

var xxx_messageInfo_UserProfile proto.InternalMessageInfo

func (m *UserProfile) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserProfile) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *UserProfile) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *UserProfile) GetAccountAlias() string {
	if m != nil {
		return m.AccountAlias
	}
	return ""
}

func (m *UserProfile) GetSex() uint32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

func (m *UserProfile) GetPrivilege() *UserPrivilege {
	if m != nil {
		return m.Privilege
	}
	return nil
}

type UserPrivilege struct {
	Account              string   `protobuf:"bytes,1,opt,name=account,proto3" json:"account,omitempty"`
	Nickname             string   `protobuf:"bytes,2,opt,name=nickname,proto3" json:"nickname,omitempty"`
	Type                 uint32   `protobuf:"varint,3,opt,name=type,proto3" json:"type,omitempty"`
	Options              []byte   `protobuf:"bytes,4,opt,name=options,proto3" json:"options,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserPrivilege) Reset()         { *m = UserPrivilege{} }
func (m *UserPrivilege) String() string { return proto.CompactTextString(m) }
func (*UserPrivilege) ProtoMessage()    {}
func (*UserPrivilege) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_middleware_6a5ad00162c10c32, []int{37}
}
func (m *UserPrivilege) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserPrivilege.Unmarshal(m, b)
}
func (m *UserPrivilege) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserPrivilege.Marshal(b, m, deterministic)
}
func (dst *UserPrivilege) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserPrivilege.Merge(dst, src)
}
func (m *UserPrivilege) XXX_Size() int {
	return xxx_messageInfo_UserPrivilege.Size(m)
}
func (m *UserPrivilege) XXX_DiscardUnknown() {
	xxx_messageInfo_UserPrivilege.DiscardUnknown(m)
}

var xxx_messageInfo_UserPrivilege proto.InternalMessageInfo

func (m *UserPrivilege) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *UserPrivilege) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *UserPrivilege) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *UserPrivilege) GetOptions() []byte {
	if m != nil {
		return m.Options
	}
	return nil
}

// 神秘人结构
type UserUKWInfo struct {
	Level                uint32   `protobuf:"varint,1,opt,name=level,proto3" json:"level,omitempty"`
	Medal                string   `protobuf:"bytes,2,opt,name=medal,proto3" json:"medal,omitempty"`
	HeadFrame            string   `protobuf:"bytes,3,opt,name=head_frame,json=headFrame,proto3" json:"head_frame,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserUKWInfo) Reset()         { *m = UserUKWInfo{} }
func (m *UserUKWInfo) String() string { return proto.CompactTextString(m) }
func (*UserUKWInfo) ProtoMessage()    {}
func (*UserUKWInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_middleware_6a5ad00162c10c32, []int{38}
}
func (m *UserUKWInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserUKWInfo.Unmarshal(m, b)
}
func (m *UserUKWInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserUKWInfo.Marshal(b, m, deterministic)
}
func (dst *UserUKWInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserUKWInfo.Merge(dst, src)
}
func (m *UserUKWInfo) XXX_Size() int {
	return xxx_messageInfo_UserUKWInfo.Size(m)
}
func (m *UserUKWInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_UserUKWInfo.DiscardUnknown(m)
}

var xxx_messageInfo_UserUKWInfo proto.InternalMessageInfo

func (m *UserUKWInfo) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

func (m *UserUKWInfo) GetMedal() string {
	if m != nil {
		return m.Medal
	}
	return ""
}

func (m *UserUKWInfo) GetHeadFrame() string {
	if m != nil {
		return m.HeadFrame
	}
	return ""
}

type FixBackpackOrderReq struct {
	OrderId              string   `protobuf:"bytes,1,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *FixBackpackOrderReq) Reset()         { *m = FixBackpackOrderReq{} }
func (m *FixBackpackOrderReq) String() string { return proto.CompactTextString(m) }
func (*FixBackpackOrderReq) ProtoMessage()    {}
func (*FixBackpackOrderReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_middleware_6a5ad00162c10c32, []int{39}
}
func (m *FixBackpackOrderReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FixBackpackOrderReq.Unmarshal(m, b)
}
func (m *FixBackpackOrderReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FixBackpackOrderReq.Marshal(b, m, deterministic)
}
func (dst *FixBackpackOrderReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FixBackpackOrderReq.Merge(dst, src)
}
func (m *FixBackpackOrderReq) XXX_Size() int {
	return xxx_messageInfo_FixBackpackOrderReq.Size(m)
}
func (m *FixBackpackOrderReq) XXX_DiscardUnknown() {
	xxx_messageInfo_FixBackpackOrderReq.DiscardUnknown(m)
}

var xxx_messageInfo_FixBackpackOrderReq proto.InternalMessageInfo

func (m *FixBackpackOrderReq) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

type FixBackpackOrderResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *FixBackpackOrderResp) Reset()         { *m = FixBackpackOrderResp{} }
func (m *FixBackpackOrderResp) String() string { return proto.CompactTextString(m) }
func (*FixBackpackOrderResp) ProtoMessage()    {}
func (*FixBackpackOrderResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_middleware_6a5ad00162c10c32, []int{40}
}
func (m *FixBackpackOrderResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FixBackpackOrderResp.Unmarshal(m, b)
}
func (m *FixBackpackOrderResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FixBackpackOrderResp.Marshal(b, m, deterministic)
}
func (dst *FixBackpackOrderResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FixBackpackOrderResp.Merge(dst, src)
}
func (m *FixBackpackOrderResp) XXX_Size() int {
	return xxx_messageInfo_FixBackpackOrderResp.Size(m)
}
func (m *FixBackpackOrderResp) XXX_DiscardUnknown() {
	xxx_messageInfo_FixBackpackOrderResp.DiscardUnknown(m)
}

var xxx_messageInfo_FixBackpackOrderResp proto.InternalMessageInfo

type LevelUpSurpriseKafkaInfo struct {
	ItemId               uint32       `protobuf:"varint,1,opt,name=item_id,json=itemId,proto3" json:"item_id,omitempty"`
	Count                uint32       `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"`
	SendTime             uint64       `protobuf:"varint,3,opt,name=send_time,json=sendTime,proto3" json:"send_time,omitempty"`
	ChannelId            uint32       `protobuf:"varint,4,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	TargetUid            uint32       `protobuf:"varint,5,opt,name=target_uid,json=targetUid,proto3" json:"target_uid,omitempty"`
	TargetAccount        string       `protobuf:"bytes,6,opt,name=target_account,json=targetAccount,proto3" json:"target_account,omitempty"`
	TargetNickname       string       `protobuf:"bytes,7,opt,name=target_nickname,json=targetNickname,proto3" json:"target_nickname,omitempty"`
	ItemOrderId          string       `protobuf:"bytes,8,opt,name=item_order_id,json=itemOrderId,proto3" json:"item_order_id,omitempty"`
	ExtendJson           string       `protobuf:"bytes,9,opt,name=extend_json,json=extendJson,proto3" json:"extend_json,omitempty"`
	FromUserProfile      *UserProfile `protobuf:"bytes,10,opt,name=from_user_profile,json=fromUserProfile,proto3" json:"from_user_profile,omitempty"`
	ToUserProfile        *UserProfile `protobuf:"bytes,11,opt,name=to_user_profile,json=toUserProfile,proto3" json:"to_user_profile,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *LevelUpSurpriseKafkaInfo) Reset()         { *m = LevelUpSurpriseKafkaInfo{} }
func (m *LevelUpSurpriseKafkaInfo) String() string { return proto.CompactTextString(m) }
func (*LevelUpSurpriseKafkaInfo) ProtoMessage()    {}
func (*LevelUpSurpriseKafkaInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_middleware_6a5ad00162c10c32, []int{41}
}
func (m *LevelUpSurpriseKafkaInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LevelUpSurpriseKafkaInfo.Unmarshal(m, b)
}
func (m *LevelUpSurpriseKafkaInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LevelUpSurpriseKafkaInfo.Marshal(b, m, deterministic)
}
func (dst *LevelUpSurpriseKafkaInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LevelUpSurpriseKafkaInfo.Merge(dst, src)
}
func (m *LevelUpSurpriseKafkaInfo) XXX_Size() int {
	return xxx_messageInfo_LevelUpSurpriseKafkaInfo.Size(m)
}
func (m *LevelUpSurpriseKafkaInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_LevelUpSurpriseKafkaInfo.DiscardUnknown(m)
}

var xxx_messageInfo_LevelUpSurpriseKafkaInfo proto.InternalMessageInfo

func (m *LevelUpSurpriseKafkaInfo) GetItemId() uint32 {
	if m != nil {
		return m.ItemId
	}
	return 0
}

func (m *LevelUpSurpriseKafkaInfo) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *LevelUpSurpriseKafkaInfo) GetSendTime() uint64 {
	if m != nil {
		return m.SendTime
	}
	return 0
}

func (m *LevelUpSurpriseKafkaInfo) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *LevelUpSurpriseKafkaInfo) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

func (m *LevelUpSurpriseKafkaInfo) GetTargetAccount() string {
	if m != nil {
		return m.TargetAccount
	}
	return ""
}

func (m *LevelUpSurpriseKafkaInfo) GetTargetNickname() string {
	if m != nil {
		return m.TargetNickname
	}
	return ""
}

func (m *LevelUpSurpriseKafkaInfo) GetItemOrderId() string {
	if m != nil {
		return m.ItemOrderId
	}
	return ""
}

func (m *LevelUpSurpriseKafkaInfo) GetExtendJson() string {
	if m != nil {
		return m.ExtendJson
	}
	return ""
}

func (m *LevelUpSurpriseKafkaInfo) GetFromUserProfile() *UserProfile {
	if m != nil {
		return m.FromUserProfile
	}
	return nil
}

func (m *LevelUpSurpriseKafkaInfo) GetToUserProfile() *UserProfile {
	if m != nil {
		return m.ToUserProfile
	}
	return nil
}

type UnpackPresentBoxReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	BoxId                string   `protobuf:"bytes,2,opt,name=box_id,json=boxId,proto3" json:"box_id,omitempty"`
	ChannelId            uint32   `protobuf:"varint,3,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UnpackPresentBoxReq) Reset()         { *m = UnpackPresentBoxReq{} }
func (m *UnpackPresentBoxReq) String() string { return proto.CompactTextString(m) }
func (*UnpackPresentBoxReq) ProtoMessage()    {}
func (*UnpackPresentBoxReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_middleware_6a5ad00162c10c32, []int{42}
}
func (m *UnpackPresentBoxReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UnpackPresentBoxReq.Unmarshal(m, b)
}
func (m *UnpackPresentBoxReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UnpackPresentBoxReq.Marshal(b, m, deterministic)
}
func (dst *UnpackPresentBoxReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UnpackPresentBoxReq.Merge(dst, src)
}
func (m *UnpackPresentBoxReq) XXX_Size() int {
	return xxx_messageInfo_UnpackPresentBoxReq.Size(m)
}
func (m *UnpackPresentBoxReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UnpackPresentBoxReq.DiscardUnknown(m)
}

var xxx_messageInfo_UnpackPresentBoxReq proto.InternalMessageInfo

func (m *UnpackPresentBoxReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UnpackPresentBoxReq) GetBoxId() string {
	if m != nil {
		return m.BoxId
	}
	return ""
}

func (m *UnpackPresentBoxReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type UnpackPresentBoxResp struct {
	BoxInfo              *PresentBoxInfo `protobuf:"bytes,1,opt,name=box_info,json=boxInfo,proto3" json:"box_info,omitempty"`
	SetBoxInfo           *SetBoxInfo     `protobuf:"bytes,2,opt,name=set_box_info,json=setBoxInfo,proto3" json:"set_box_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *UnpackPresentBoxResp) Reset()         { *m = UnpackPresentBoxResp{} }
func (m *UnpackPresentBoxResp) String() string { return proto.CompactTextString(m) }
func (*UnpackPresentBoxResp) ProtoMessage()    {}
func (*UnpackPresentBoxResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_middleware_6a5ad00162c10c32, []int{43}
}
func (m *UnpackPresentBoxResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UnpackPresentBoxResp.Unmarshal(m, b)
}
func (m *UnpackPresentBoxResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UnpackPresentBoxResp.Marshal(b, m, deterministic)
}
func (dst *UnpackPresentBoxResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UnpackPresentBoxResp.Merge(dst, src)
}
func (m *UnpackPresentBoxResp) XXX_Size() int {
	return xxx_messageInfo_UnpackPresentBoxResp.Size(m)
}
func (m *UnpackPresentBoxResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UnpackPresentBoxResp.DiscardUnknown(m)
}

var xxx_messageInfo_UnpackPresentBoxResp proto.InternalMessageInfo

func (m *UnpackPresentBoxResp) GetBoxInfo() *PresentBoxInfo {
	if m != nil {
		return m.BoxInfo
	}
	return nil
}

func (m *UnpackPresentBoxResp) GetSetBoxInfo() *SetBoxInfo {
	if m != nil {
		return m.SetBoxInfo
	}
	return nil
}

// 展示礼物盒会用到的信息
type PresentBoxInfo struct {
	ItemMsg              *PresentSendMsg   `protobuf:"bytes,1,opt,name=item_msg,json=itemMsg,proto3" json:"item_msg,omitempty"`
	BoxDetail            *PresentBoxDetail `protobuf:"bytes,2,opt,name=box_detail,json=boxDetail,proto3" json:"box_detail,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *PresentBoxInfo) Reset()         { *m = PresentBoxInfo{} }
func (m *PresentBoxInfo) String() string { return proto.CompactTextString(m) }
func (*PresentBoxInfo) ProtoMessage()    {}
func (*PresentBoxInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_middleware_6a5ad00162c10c32, []int{44}
}
func (m *PresentBoxInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PresentBoxInfo.Unmarshal(m, b)
}
func (m *PresentBoxInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PresentBoxInfo.Marshal(b, m, deterministic)
}
func (dst *PresentBoxInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PresentBoxInfo.Merge(dst, src)
}
func (m *PresentBoxInfo) XXX_Size() int {
	return xxx_messageInfo_PresentBoxInfo.Size(m)
}
func (m *PresentBoxInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_PresentBoxInfo.DiscardUnknown(m)
}

var xxx_messageInfo_PresentBoxInfo proto.InternalMessageInfo

func (m *PresentBoxInfo) GetItemMsg() *PresentSendMsg {
	if m != nil {
		return m.ItemMsg
	}
	return nil
}

func (m *PresentBoxInfo) GetBoxDetail() *PresentBoxDetail {
	if m != nil {
		return m.BoxDetail
	}
	return nil
}

type PresentBoxDetail struct {
	BoxId                string       `protobuf:"bytes,1,opt,name=box_id,json=boxId,proto3" json:"box_id,omitempty"`
	FromUserProfile      *UserProfile `protobuf:"bytes,2,opt,name=from_user_profile,json=fromUserProfile,proto3" json:"from_user_profile,omitempty"`
	ToUserProfile        *UserProfile `protobuf:"bytes,3,opt,name=to_user_profile,json=toUserProfile,proto3" json:"to_user_profile,omitempty"`
	ItemId               uint32       `protobuf:"varint,4,opt,name=item_id,json=itemId,proto3" json:"item_id,omitempty"`
	ItemName             string       `protobuf:"bytes,5,opt,name=item_name,json=itemName,proto3" json:"item_name,omitempty"`
	SendTime             uint64       `protobuf:"varint,6,opt,name=send_time,json=sendTime,proto3" json:"send_time,omitempty"`
	ExtendJson           string       `protobuf:"bytes,7,opt,name=extend_json,json=extendJson,proto3" json:"extend_json,omitempty"`
	DelayTime            uint64       `protobuf:"varint,8,opt,name=delay_time,json=delayTime,proto3" json:"delay_time,omitempty"`
	IsVisibleToSender    bool         `protobuf:"varint,9,opt,name=is_visible_to_sender,json=isVisibleToSender,proto3" json:"is_visible_to_sender,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *PresentBoxDetail) Reset()         { *m = PresentBoxDetail{} }
func (m *PresentBoxDetail) String() string { return proto.CompactTextString(m) }
func (*PresentBoxDetail) ProtoMessage()    {}
func (*PresentBoxDetail) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_middleware_6a5ad00162c10c32, []int{45}
}
func (m *PresentBoxDetail) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PresentBoxDetail.Unmarshal(m, b)
}
func (m *PresentBoxDetail) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PresentBoxDetail.Marshal(b, m, deterministic)
}
func (dst *PresentBoxDetail) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PresentBoxDetail.Merge(dst, src)
}
func (m *PresentBoxDetail) XXX_Size() int {
	return xxx_messageInfo_PresentBoxDetail.Size(m)
}
func (m *PresentBoxDetail) XXX_DiscardUnknown() {
	xxx_messageInfo_PresentBoxDetail.DiscardUnknown(m)
}

var xxx_messageInfo_PresentBoxDetail proto.InternalMessageInfo

func (m *PresentBoxDetail) GetBoxId() string {
	if m != nil {
		return m.BoxId
	}
	return ""
}

func (m *PresentBoxDetail) GetFromUserProfile() *UserProfile {
	if m != nil {
		return m.FromUserProfile
	}
	return nil
}

func (m *PresentBoxDetail) GetToUserProfile() *UserProfile {
	if m != nil {
		return m.ToUserProfile
	}
	return nil
}

func (m *PresentBoxDetail) GetItemId() uint32 {
	if m != nil {
		return m.ItemId
	}
	return 0
}

func (m *PresentBoxDetail) GetItemName() string {
	if m != nil {
		return m.ItemName
	}
	return ""
}

func (m *PresentBoxDetail) GetSendTime() uint64 {
	if m != nil {
		return m.SendTime
	}
	return 0
}

func (m *PresentBoxDetail) GetExtendJson() string {
	if m != nil {
		return m.ExtendJson
	}
	return ""
}

func (m *PresentBoxDetail) GetDelayTime() uint64 {
	if m != nil {
		return m.DelayTime
	}
	return 0
}

func (m *PresentBoxDetail) GetIsVisibleToSender() bool {
	if m != nil {
		return m.IsVisibleToSender
	}
	return false
}

type GetChannelPresentBoxReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChannelPresentBoxReq) Reset()         { *m = GetChannelPresentBoxReq{} }
func (m *GetChannelPresentBoxReq) String() string { return proto.CompactTextString(m) }
func (*GetChannelPresentBoxReq) ProtoMessage()    {}
func (*GetChannelPresentBoxReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_middleware_6a5ad00162c10c32, []int{46}
}
func (m *GetChannelPresentBoxReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelPresentBoxReq.Unmarshal(m, b)
}
func (m *GetChannelPresentBoxReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelPresentBoxReq.Marshal(b, m, deterministic)
}
func (dst *GetChannelPresentBoxReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelPresentBoxReq.Merge(dst, src)
}
func (m *GetChannelPresentBoxReq) XXX_Size() int {
	return xxx_messageInfo_GetChannelPresentBoxReq.Size(m)
}
func (m *GetChannelPresentBoxReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelPresentBoxReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelPresentBoxReq proto.InternalMessageInfo

func (m *GetChannelPresentBoxReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetChannelPresentBoxResp struct {
	PresentBoxList       []*PresentBoxInfo `protobuf:"bytes,1,rep,name=present_box_list,json=presentBoxList,proto3" json:"present_box_list,omitempty"`
	SetBoxList           []*SetBoxInfo     `protobuf:"bytes,2,rep,name=set_box_list,json=setBoxList,proto3" json:"set_box_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetChannelPresentBoxResp) Reset()         { *m = GetChannelPresentBoxResp{} }
func (m *GetChannelPresentBoxResp) String() string { return proto.CompactTextString(m) }
func (*GetChannelPresentBoxResp) ProtoMessage()    {}
func (*GetChannelPresentBoxResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_middleware_6a5ad00162c10c32, []int{47}
}
func (m *GetChannelPresentBoxResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelPresentBoxResp.Unmarshal(m, b)
}
func (m *GetChannelPresentBoxResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelPresentBoxResp.Marshal(b, m, deterministic)
}
func (dst *GetChannelPresentBoxResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelPresentBoxResp.Merge(dst, src)
}
func (m *GetChannelPresentBoxResp) XXX_Size() int {
	return xxx_messageInfo_GetChannelPresentBoxResp.Size(m)
}
func (m *GetChannelPresentBoxResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelPresentBoxResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelPresentBoxResp proto.InternalMessageInfo

func (m *GetChannelPresentBoxResp) GetPresentBoxList() []*PresentBoxInfo {
	if m != nil {
		return m.PresentBoxList
	}
	return nil
}

func (m *GetChannelPresentBoxResp) GetSetBoxList() []*SetBoxInfo {
	if m != nil {
		return m.SetBoxList
	}
	return nil
}

type DelPresentConfigReq struct {
	ItemId               uint32   `protobuf:"varint,1,opt,name=item_id,json=itemId,proto3" json:"item_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelPresentConfigReq) Reset()         { *m = DelPresentConfigReq{} }
func (m *DelPresentConfigReq) String() string { return proto.CompactTextString(m) }
func (*DelPresentConfigReq) ProtoMessage()    {}
func (*DelPresentConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_middleware_6a5ad00162c10c32, []int{48}
}
func (m *DelPresentConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelPresentConfigReq.Unmarshal(m, b)
}
func (m *DelPresentConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelPresentConfigReq.Marshal(b, m, deterministic)
}
func (dst *DelPresentConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelPresentConfigReq.Merge(dst, src)
}
func (m *DelPresentConfigReq) XXX_Size() int {
	return xxx_messageInfo_DelPresentConfigReq.Size(m)
}
func (m *DelPresentConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelPresentConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelPresentConfigReq proto.InternalMessageInfo

func (m *DelPresentConfigReq) GetItemId() uint32 {
	if m != nil {
		return m.ItemId
	}
	return 0
}

type DelPresentConfigResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelPresentConfigResp) Reset()         { *m = DelPresentConfigResp{} }
func (m *DelPresentConfigResp) String() string { return proto.CompactTextString(m) }
func (*DelPresentConfigResp) ProtoMessage()    {}
func (*DelPresentConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_middleware_6a5ad00162c10c32, []int{49}
}
func (m *DelPresentConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelPresentConfigResp.Unmarshal(m, b)
}
func (m *DelPresentConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelPresentConfigResp.Marshal(b, m, deterministic)
}
func (dst *DelPresentConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelPresentConfigResp.Merge(dst, src)
}
func (m *DelPresentConfigResp) XXX_Size() int {
	return xxx_messageInfo_DelPresentConfigResp.Size(m)
}
func (m *DelPresentConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelPresentConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_DelPresentConfigResp proto.InternalMessageInfo

// 按组送出礼物， 一对一，但是送出不止一个礼物
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
type SetSendPresentReq struct {
	SendUid              uint32             `protobuf:"varint,1,opt,name=send_uid,json=sendUid,proto3" json:"send_uid,omitempty"`
	TargetInfo           *MultiTargetItem   `protobuf:"bytes,2,opt,name=target_info,json=targetInfo,proto3" json:"target_info,omitempty"`
	ChannelId            uint32             `protobuf:"varint,3,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	SendSource           uint32             `protobuf:"varint,4,opt,name=send_source,json=sendSource,proto3" json:"send_source,omitempty"`
	ItemSource           uint32             `protobuf:"varint,5,opt,name=item_source,json=itemSource,proto3" json:"item_source,omitempty"`
	SendMethod           uint32             `protobuf:"varint,6,opt,name=send_method,json=sendMethod,proto3" json:"send_method,omitempty"`
	WithPay              bool               `protobuf:"varint,7,opt,name=with_pay,json=withPay,proto3" json:"with_pay,omitempty"`
	WithPush             bool               `protobuf:"varint,8,opt,name=with_push,json=withPush,proto3" json:"with_push,omitempty"`
	ClientInfo           *PresentClientInfo `protobuf:"bytes,9,opt,name=client_info,json=clientInfo,proto3" json:"client_info,omitempty"`
	SendTime             int64              `protobuf:"varint,10,opt,name=sendTime,proto3" json:"sendTime,omitempty"`
	ChannelGameId        uint32             `protobuf:"varint,11,opt,name=channel_game_id,json=channelGameId,proto3" json:"channel_game_id,omitempty"`
	EmperorSetId         uint32             `protobuf:"varint,12,opt,name=emperor_set_id,json=emperorSetId,proto3" json:"emperor_set_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *SetSendPresentReq) Reset()         { *m = SetSendPresentReq{} }
func (m *SetSendPresentReq) String() string { return proto.CompactTextString(m) }
func (*SetSendPresentReq) ProtoMessage()    {}
func (*SetSendPresentReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_middleware_6a5ad00162c10c32, []int{50}
}
func (m *SetSendPresentReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetSendPresentReq.Unmarshal(m, b)
}
func (m *SetSendPresentReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetSendPresentReq.Marshal(b, m, deterministic)
}
func (dst *SetSendPresentReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetSendPresentReq.Merge(dst, src)
}
func (m *SetSendPresentReq) XXX_Size() int {
	return xxx_messageInfo_SetSendPresentReq.Size(m)
}
func (m *SetSendPresentReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetSendPresentReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetSendPresentReq proto.InternalMessageInfo

func (m *SetSendPresentReq) GetSendUid() uint32 {
	if m != nil {
		return m.SendUid
	}
	return 0
}

func (m *SetSendPresentReq) GetTargetInfo() *MultiTargetItem {
	if m != nil {
		return m.TargetInfo
	}
	return nil
}

func (m *SetSendPresentReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SetSendPresentReq) GetSendSource() uint32 {
	if m != nil {
		return m.SendSource
	}
	return 0
}

func (m *SetSendPresentReq) GetItemSource() uint32 {
	if m != nil {
		return m.ItemSource
	}
	return 0
}

func (m *SetSendPresentReq) GetSendMethod() uint32 {
	if m != nil {
		return m.SendMethod
	}
	return 0
}

func (m *SetSendPresentReq) GetWithPay() bool {
	if m != nil {
		return m.WithPay
	}
	return false
}

func (m *SetSendPresentReq) GetWithPush() bool {
	if m != nil {
		return m.WithPush
	}
	return false
}

func (m *SetSendPresentReq) GetClientInfo() *PresentClientInfo {
	if m != nil {
		return m.ClientInfo
	}
	return nil
}

func (m *SetSendPresentReq) GetSendTime() int64 {
	if m != nil {
		return m.SendTime
	}
	return 0
}

func (m *SetSendPresentReq) GetChannelGameId() uint32 {
	if m != nil {
		return m.ChannelGameId
	}
	return 0
}

func (m *SetSendPresentReq) GetEmperorSetId() uint32 {
	if m != nil {
		return m.EmperorSetId
	}
	return 0
}

type SetSendPresentResp struct {
	MsgInfo                 *SetPresentSendMsg `protobuf:"bytes,1,opt,name=msg_info,json=msgInfo,proto3" json:"msg_info,omitempty"`
	MemberContributionAdded uint32             `protobuf:"varint,2,opt,name=member_contribution_added,json=memberContributionAdded,proto3" json:"member_contribution_added,omitempty"`
	Count                   uint32             `protobuf:"varint,3,opt,name=count,proto3" json:"count,omitempty"`
	CurTbeans               int64              `protobuf:"varint,4,opt,name=cur_tbeans,json=curTbeans,proto3" json:"cur_tbeans,omitempty"`
	ItemSource              uint32             `protobuf:"varint,5,opt,name=item_source,json=itemSource,proto3" json:"item_source,omitempty"`
	SourceId                uint32             `protobuf:"varint,6,opt,name=source_id,json=sourceId,proto3" json:"source_id,omitempty"`
	SourceRemain            uint32             `protobuf:"varint,7,opt,name=source_remain,json=sourceRemain,proto3" json:"source_remain,omitempty"`
	BoxDetail               *SetBoxDetail      `protobuf:"bytes,11,opt,name=box_detail,json=boxDetail,proto3" json:"box_detail,omitempty"`
	XXX_NoUnkeyedLiteral    struct{}           `json:"-"`
	XXX_unrecognized        []byte             `json:"-"`
	XXX_sizecache           int32              `json:"-"`
}

func (m *SetSendPresentResp) Reset()         { *m = SetSendPresentResp{} }
func (m *SetSendPresentResp) String() string { return proto.CompactTextString(m) }
func (*SetSendPresentResp) ProtoMessage()    {}
func (*SetSendPresentResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_middleware_6a5ad00162c10c32, []int{51}
}
func (m *SetSendPresentResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetSendPresentResp.Unmarshal(m, b)
}
func (m *SetSendPresentResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetSendPresentResp.Marshal(b, m, deterministic)
}
func (dst *SetSendPresentResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetSendPresentResp.Merge(dst, src)
}
func (m *SetSendPresentResp) XXX_Size() int {
	return xxx_messageInfo_SetSendPresentResp.Size(m)
}
func (m *SetSendPresentResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetSendPresentResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetSendPresentResp proto.InternalMessageInfo

func (m *SetSendPresentResp) GetMsgInfo() *SetPresentSendMsg {
	if m != nil {
		return m.MsgInfo
	}
	return nil
}

func (m *SetSendPresentResp) GetMemberContributionAdded() uint32 {
	if m != nil {
		return m.MemberContributionAdded
	}
	return 0
}

func (m *SetSendPresentResp) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *SetSendPresentResp) GetCurTbeans() int64 {
	if m != nil {
		return m.CurTbeans
	}
	return 0
}

func (m *SetSendPresentResp) GetItemSource() uint32 {
	if m != nil {
		return m.ItemSource
	}
	return 0
}

func (m *SetSendPresentResp) GetSourceId() uint32 {
	if m != nil {
		return m.SourceId
	}
	return 0
}

func (m *SetSendPresentResp) GetSourceRemain() uint32 {
	if m != nil {
		return m.SourceRemain
	}
	return 0
}

func (m *SetSendPresentResp) GetBoxDetail() *SetBoxDetail {
	if m != nil {
		return m.BoxDetail
	}
	return nil
}

// 赠送礼物
type SetPresentSendMsg struct {
	ItemInfo             []*PresentSendItemInfo `protobuf:"bytes,1,rep,name=item_info,json=itemInfo,proto3" json:"item_info,omitempty"`
	SendTime             uint64                 `protobuf:"varint,2,opt,name=send_time,json=sendTime,proto3" json:"send_time,omitempty"`
	ChannelId            uint32                 `protobuf:"varint,3,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	SendUid              uint32                 `protobuf:"varint,4,opt,name=send_uid,json=sendUid,proto3" json:"send_uid,omitempty"`
	SendAccount          string                 `protobuf:"bytes,5,opt,name=send_account,json=sendAccount,proto3" json:"send_account,omitempty"`
	SendNickname         string                 `protobuf:"bytes,6,opt,name=send_nickname,json=sendNickname,proto3" json:"send_nickname,omitempty"`
	TargetUid            uint32                 `protobuf:"varint,7,opt,name=target_uid,json=targetUid,proto3" json:"target_uid,omitempty"`
	TargetAccount        string                 `protobuf:"bytes,8,opt,name=target_account,json=targetAccount,proto3" json:"target_account,omitempty"`
	TargetNickname       string                 `protobuf:"bytes,9,opt,name=target_nickname,json=targetNickname,proto3" json:"target_nickname,omitempty"`
	ExtendJson           string                 `protobuf:"bytes,10,opt,name=extend_json,json=extendJson,proto3" json:"extend_json,omitempty"`
	FromUserProfile      *UserProfile           `protobuf:"bytes,11,opt,name=from_user_profile,json=fromUserProfile,proto3" json:"from_user_profile,omitempty"`
	TargetUserProfile    *UserProfile           `protobuf:"bytes,12,opt,name=target_user_profile,json=targetUserProfile,proto3" json:"target_user_profile,omitempty"`
	EmperorEffectJson    string                 `protobuf:"bytes,13,opt,name=emperor_effect_json,json=emperorEffectJson,proto3" json:"emperor_effect_json,omitempty"`
	ViewingEffectJson    string                 `protobuf:"bytes,14,opt,name=viewing_effect_json,json=viewingEffectJson,proto3" json:"viewing_effect_json,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *SetPresentSendMsg) Reset()         { *m = SetPresentSendMsg{} }
func (m *SetPresentSendMsg) String() string { return proto.CompactTextString(m) }
func (*SetPresentSendMsg) ProtoMessage()    {}
func (*SetPresentSendMsg) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_middleware_6a5ad00162c10c32, []int{52}
}
func (m *SetPresentSendMsg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetPresentSendMsg.Unmarshal(m, b)
}
func (m *SetPresentSendMsg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetPresentSendMsg.Marshal(b, m, deterministic)
}
func (dst *SetPresentSendMsg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetPresentSendMsg.Merge(dst, src)
}
func (m *SetPresentSendMsg) XXX_Size() int {
	return xxx_messageInfo_SetPresentSendMsg.Size(m)
}
func (m *SetPresentSendMsg) XXX_DiscardUnknown() {
	xxx_messageInfo_SetPresentSendMsg.DiscardUnknown(m)
}

var xxx_messageInfo_SetPresentSendMsg proto.InternalMessageInfo

func (m *SetPresentSendMsg) GetItemInfo() []*PresentSendItemInfo {
	if m != nil {
		return m.ItemInfo
	}
	return nil
}

func (m *SetPresentSendMsg) GetSendTime() uint64 {
	if m != nil {
		return m.SendTime
	}
	return 0
}

func (m *SetPresentSendMsg) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SetPresentSendMsg) GetSendUid() uint32 {
	if m != nil {
		return m.SendUid
	}
	return 0
}

func (m *SetPresentSendMsg) GetSendAccount() string {
	if m != nil {
		return m.SendAccount
	}
	return ""
}

func (m *SetPresentSendMsg) GetSendNickname() string {
	if m != nil {
		return m.SendNickname
	}
	return ""
}

func (m *SetPresentSendMsg) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

func (m *SetPresentSendMsg) GetTargetAccount() string {
	if m != nil {
		return m.TargetAccount
	}
	return ""
}

func (m *SetPresentSendMsg) GetTargetNickname() string {
	if m != nil {
		return m.TargetNickname
	}
	return ""
}

func (m *SetPresentSendMsg) GetExtendJson() string {
	if m != nil {
		return m.ExtendJson
	}
	return ""
}

func (m *SetPresentSendMsg) GetFromUserProfile() *UserProfile {
	if m != nil {
		return m.FromUserProfile
	}
	return nil
}

func (m *SetPresentSendMsg) GetTargetUserProfile() *UserProfile {
	if m != nil {
		return m.TargetUserProfile
	}
	return nil
}

func (m *SetPresentSendMsg) GetEmperorEffectJson() string {
	if m != nil {
		return m.EmperorEffectJson
	}
	return ""
}

func (m *SetPresentSendMsg) GetViewingEffectJson() string {
	if m != nil {
		return m.ViewingEffectJson
	}
	return ""
}

// 展示礼物盒会用到的信息
type SetBoxInfo struct {
	ItemMsg              []byte        `protobuf:"bytes,1,opt,name=item_msg,json=itemMsg,proto3" json:"item_msg,omitempty"`
	BoxDetail            *SetBoxDetail `protobuf:"bytes,2,opt,name=box_detail,json=boxDetail,proto3" json:"box_detail,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *SetBoxInfo) Reset()         { *m = SetBoxInfo{} }
func (m *SetBoxInfo) String() string { return proto.CompactTextString(m) }
func (*SetBoxInfo) ProtoMessage()    {}
func (*SetBoxInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_middleware_6a5ad00162c10c32, []int{53}
}
func (m *SetBoxInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetBoxInfo.Unmarshal(m, b)
}
func (m *SetBoxInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetBoxInfo.Marshal(b, m, deterministic)
}
func (dst *SetBoxInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetBoxInfo.Merge(dst, src)
}
func (m *SetBoxInfo) XXX_Size() int {
	return xxx_messageInfo_SetBoxInfo.Size(m)
}
func (m *SetBoxInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_SetBoxInfo.DiscardUnknown(m)
}

var xxx_messageInfo_SetBoxInfo proto.InternalMessageInfo

func (m *SetBoxInfo) GetItemMsg() []byte {
	if m != nil {
		return m.ItemMsg
	}
	return nil
}

func (m *SetBoxInfo) GetBoxDetail() *SetBoxDetail {
	if m != nil {
		return m.BoxDetail
	}
	return nil
}

type SetBoxDetail struct {
	BoxId                string       `protobuf:"bytes,1,opt,name=box_id,json=boxId,proto3" json:"box_id,omitempty"`
	FromUserProfile      *UserProfile `protobuf:"bytes,2,opt,name=from_user_profile,json=fromUserProfile,proto3" json:"from_user_profile,omitempty"`
	ToUserProfile        *UserProfile `protobuf:"bytes,3,opt,name=to_user_profile,json=toUserProfile,proto3" json:"to_user_profile,omitempty"`
	SetId                uint32       `protobuf:"varint,4,opt,name=set_id,json=setId,proto3" json:"set_id,omitempty"`
	SetName              string       `protobuf:"bytes,5,opt,name=set_name,json=setName,proto3" json:"set_name,omitempty"`
	SendTime             uint64       `protobuf:"varint,6,opt,name=send_time,json=sendTime,proto3" json:"send_time,omitempty"`
	ExtendJson           string       `protobuf:"bytes,7,opt,name=extend_json,json=extendJson,proto3" json:"extend_json,omitempty"`
	DelayTime            uint64       `protobuf:"varint,8,opt,name=delay_time,json=delayTime,proto3" json:"delay_time,omitempty"`
	IsVisibleToSender    bool         `protobuf:"varint,9,opt,name=is_visible_to_sender,json=isVisibleToSender,proto3" json:"is_visible_to_sender,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *SetBoxDetail) Reset()         { *m = SetBoxDetail{} }
func (m *SetBoxDetail) String() string { return proto.CompactTextString(m) }
func (*SetBoxDetail) ProtoMessage()    {}
func (*SetBoxDetail) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_middleware_6a5ad00162c10c32, []int{54}
}
func (m *SetBoxDetail) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetBoxDetail.Unmarshal(m, b)
}
func (m *SetBoxDetail) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetBoxDetail.Marshal(b, m, deterministic)
}
func (dst *SetBoxDetail) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetBoxDetail.Merge(dst, src)
}
func (m *SetBoxDetail) XXX_Size() int {
	return xxx_messageInfo_SetBoxDetail.Size(m)
}
func (m *SetBoxDetail) XXX_DiscardUnknown() {
	xxx_messageInfo_SetBoxDetail.DiscardUnknown(m)
}

var xxx_messageInfo_SetBoxDetail proto.InternalMessageInfo

func (m *SetBoxDetail) GetBoxId() string {
	if m != nil {
		return m.BoxId
	}
	return ""
}

func (m *SetBoxDetail) GetFromUserProfile() *UserProfile {
	if m != nil {
		return m.FromUserProfile
	}
	return nil
}

func (m *SetBoxDetail) GetToUserProfile() *UserProfile {
	if m != nil {
		return m.ToUserProfile
	}
	return nil
}

func (m *SetBoxDetail) GetSetId() uint32 {
	if m != nil {
		return m.SetId
	}
	return 0
}

func (m *SetBoxDetail) GetSetName() string {
	if m != nil {
		return m.SetName
	}
	return ""
}

func (m *SetBoxDetail) GetSendTime() uint64 {
	if m != nil {
		return m.SendTime
	}
	return 0
}

func (m *SetBoxDetail) GetExtendJson() string {
	if m != nil {
		return m.ExtendJson
	}
	return ""
}

func (m *SetBoxDetail) GetDelayTime() uint64 {
	if m != nil {
		return m.DelayTime
	}
	return 0
}

func (m *SetBoxDetail) GetIsVisibleToSender() bool {
	if m != nil {
		return m.IsVisibleToSender
	}
	return false
}

func init() {
	proto.RegisterType((*DrawPresentPicture)(nil), "present_middleware.DrawPresentPicture")
	proto.RegisterType((*PresentLine)(nil), "present_middleware.PresentLine")
	proto.RegisterType((*PresentPoint)(nil), "present_middleware.PresentPoint")
	proto.RegisterType((*PresentSendMsg)(nil), "present_middleware.PresentSendMsg")
	proto.RegisterType((*SendPresentReq)(nil), "present_middleware.SendPresentReq")
	proto.RegisterType((*PresentTargetInfo)(nil), "present_middleware.PresentTargetInfo")
	proto.RegisterType((*SingleTargetUser)(nil), "present_middleware.SingleTargetUser")
	proto.RegisterType((*MultiTargetUser)(nil), "present_middleware.MultiTargetUser")
	proto.RegisterType((*TargetUserInfo)(nil), "present_middleware.TargetUserInfo")
	proto.RegisterType((*MultiTargetItem)(nil), "present_middleware.MultiTargetItem")
	proto.RegisterType((*PresentItemInfo)(nil), "present_middleware.PresentItemInfo")
	proto.RegisterType((*PushInfo)(nil), "present_middleware.PushInfo")
	proto.RegisterType((*PresentClientInfo)(nil), "present_middleware.PresentClientInfo")
	proto.RegisterType((*SendPresentResp)(nil), "present_middleware.SendPresentResp")
	proto.RegisterType((*OrderInfo)(nil), "present_middleware.OrderInfo")
	proto.RegisterType((*PresentTargetUserInfo)(nil), "present_middleware.PresentTargetUserInfo")
	proto.RegisterType((*PresentBatchTargetInfo)(nil), "present_middleware.PresentBatchTargetInfo")
	proto.RegisterType((*PresentSendItemInfo)(nil), "present_middleware.PresentSendItemInfo")
	proto.RegisterType((*PresentBatchInfoMsg)(nil), "present_middleware.PresentBatchInfoMsg")
	proto.RegisterType((*ServiceCtrlInfo)(nil), "present_middleware.ServiceCtrlInfo")
	proto.RegisterType((*BatchSendPresentReq)(nil), "present_middleware.BatchSendPresentReq")
	proto.RegisterType((*BatchSendPresentResp)(nil), "present_middleware.BatchSendPresentResp")
	proto.RegisterType((*PresentSendBase)(nil), "present_middleware.presentSendBase")
	proto.RegisterType((*ImSendPresentReq)(nil), "present_middleware.ImSendPresentReq")
	proto.RegisterType((*ImSendPresentResp)(nil), "present_middleware.ImSendPresentResp")
	proto.RegisterType((*FellowSendPresentReq)(nil), "present_middleware.FellowSendPresentReq")
	proto.RegisterType((*FellowSendPresentResp)(nil), "present_middleware.FellowSendPresentResp")
	proto.RegisterType((*AllMicSendPresentReq)(nil), "present_middleware.AllMicSendPresentReq")
	proto.RegisterType((*AllMicSendPresentResp)(nil), "present_middleware.AllMicSendPresentResp")
	proto.RegisterType((*MagicSendPresentReq)(nil), "present_middleware.MagicSendPresentReq")
	proto.RegisterType((*GiftItem)(nil), "present_middleware.GiftItem")
	proto.RegisterType((*MagicSendPresentResp)(nil), "present_middleware.MagicSendPresentResp")
	proto.RegisterType((*MagicPresentInfoMsg)(nil), "present_middleware.MagicPresentInfoMsg")
	proto.RegisterType((*SendMagicSpiritOpt)(nil), "present_middleware.SendMagicSpiritOpt")
	proto.RegisterType((*MagicSpiritSendItem)(nil), "present_middleware.MagicSpiritSendItem")
	proto.RegisterType((*MagicSpiritAwardInfo)(nil), "present_middleware.MagicSpiritAwardInfo")
	proto.RegisterType((*UserProfile)(nil), "present_middleware.UserProfile")
	proto.RegisterType((*UserPrivilege)(nil), "present_middleware.UserPrivilege")
	proto.RegisterType((*UserUKWInfo)(nil), "present_middleware.UserUKWInfo")
	proto.RegisterType((*FixBackpackOrderReq)(nil), "present_middleware.FixBackpackOrderReq")
	proto.RegisterType((*FixBackpackOrderResp)(nil), "present_middleware.FixBackpackOrderResp")
	proto.RegisterType((*LevelUpSurpriseKafkaInfo)(nil), "present_middleware.LevelUpSurpriseKafkaInfo")
	proto.RegisterType((*UnpackPresentBoxReq)(nil), "present_middleware.UnpackPresentBoxReq")
	proto.RegisterType((*UnpackPresentBoxResp)(nil), "present_middleware.UnpackPresentBoxResp")
	proto.RegisterType((*PresentBoxInfo)(nil), "present_middleware.PresentBoxInfo")
	proto.RegisterType((*PresentBoxDetail)(nil), "present_middleware.PresentBoxDetail")
	proto.RegisterType((*GetChannelPresentBoxReq)(nil), "present_middleware.GetChannelPresentBoxReq")
	proto.RegisterType((*GetChannelPresentBoxResp)(nil), "present_middleware.GetChannelPresentBoxResp")
	proto.RegisterType((*DelPresentConfigReq)(nil), "present_middleware.DelPresentConfigReq")
	proto.RegisterType((*DelPresentConfigResp)(nil), "present_middleware.DelPresentConfigResp")
	proto.RegisterType((*SetSendPresentReq)(nil), "present_middleware.SetSendPresentReq")
	proto.RegisterType((*SetSendPresentResp)(nil), "present_middleware.SetSendPresentResp")
	proto.RegisterType((*SetPresentSendMsg)(nil), "present_middleware.SetPresentSendMsg")
	proto.RegisterType((*SetBoxInfo)(nil), "present_middleware.SetBoxInfo")
	proto.RegisterType((*SetBoxDetail)(nil), "present_middleware.SetBoxDetail")
	proto.RegisterEnum("present_middleware.PresentSendSourceType", PresentSendSourceType_name, PresentSendSourceType_value)
	proto.RegisterEnum("present_middleware.PresentSourceType", PresentSourceType_name, PresentSourceType_value)
	proto.RegisterEnum("present_middleware.PresentSendType", PresentSendType_name, PresentSendType_value)
	proto.RegisterEnum("present_middleware.PresentBatchSendType", PresentBatchSendType_name, PresentBatchSendType_value)
	proto.RegisterEnum("present_middleware.PresentSendMethodType", PresentSendMethodType_name, PresentSendMethodType_value)
	proto.RegisterEnum("present_middleware.EUserPrivilegeType", EUserPrivilegeType_name, EUserPrivilegeType_value)
	proto.RegisterEnum("present_middleware.PushInfo_ChannelPushType", PushInfo_ChannelPushType_name, PushInfo_ChannelPushType_value)
	proto.RegisterEnum("present_middleware.PushInfo_PersonalPushType", PushInfo_PersonalPushType_name, PushInfo_PersonalPushType_value)
	proto.RegisterEnum("present_middleware.PushInfo_ImType", PushInfo_ImType_name, PushInfo_ImType_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// PresentMiddlewareClient is the client API for PresentMiddleware service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type PresentMiddlewareClient interface {
	SendPresent(ctx context.Context, in *SendPresentReq, opts ...grpc.CallOption) (*SendPresentResp, error)
	BatchSendPresent(ctx context.Context, in *BatchSendPresentReq, opts ...grpc.CallOption) (*BatchSendPresentResp, error)
	ImSendPresent(ctx context.Context, in *ImSendPresentReq, opts ...grpc.CallOption) (*ImSendPresentResp, error)
	AllMicSendPresent(ctx context.Context, in *AllMicSendPresentReq, opts ...grpc.CallOption) (*AllMicSendPresentResp, error)
	FellowSendPresent(ctx context.Context, in *FellowSendPresentReq, opts ...grpc.CallOption) (*FellowSendPresentResp, error)
	MagicSendPresent(ctx context.Context, in *MagicSendPresentReq, opts ...grpc.CallOption) (*MagicSendPresentResp, error)
	SetSendPresent(ctx context.Context, in *SetSendPresentReq, opts ...grpc.CallOption) (*SetSendPresentResp, error)
	FixBackpackOrder(ctx context.Context, in *FixBackpackOrderReq, opts ...grpc.CallOption) (*FixBackpackOrderResp, error)
	GetChannelPresentBox(ctx context.Context, in *GetChannelPresentBoxReq, opts ...grpc.CallOption) (*GetChannelPresentBoxResp, error)
	UnpackPresentBox(ctx context.Context, in *UnpackPresentBoxReq, opts ...grpc.CallOption) (*UnpackPresentBoxResp, error)
	DelPresentConfig(ctx context.Context, in *DelPresentConfigReq, opts ...grpc.CallOption) (*DelPresentConfigResp, error)
}

type presentMiddlewareClient struct {
	cc *grpc.ClientConn
}

func NewPresentMiddlewareClient(cc *grpc.ClientConn) PresentMiddlewareClient {
	return &presentMiddlewareClient{cc}
}

func (c *presentMiddlewareClient) SendPresent(ctx context.Context, in *SendPresentReq, opts ...grpc.CallOption) (*SendPresentResp, error) {
	out := new(SendPresentResp)
	err := c.cc.Invoke(ctx, "/present_middleware.PresentMiddleware/SendPresent", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *presentMiddlewareClient) BatchSendPresent(ctx context.Context, in *BatchSendPresentReq, opts ...grpc.CallOption) (*BatchSendPresentResp, error) {
	out := new(BatchSendPresentResp)
	err := c.cc.Invoke(ctx, "/present_middleware.PresentMiddleware/BatchSendPresent", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *presentMiddlewareClient) ImSendPresent(ctx context.Context, in *ImSendPresentReq, opts ...grpc.CallOption) (*ImSendPresentResp, error) {
	out := new(ImSendPresentResp)
	err := c.cc.Invoke(ctx, "/present_middleware.PresentMiddleware/ImSendPresent", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *presentMiddlewareClient) AllMicSendPresent(ctx context.Context, in *AllMicSendPresentReq, opts ...grpc.CallOption) (*AllMicSendPresentResp, error) {
	out := new(AllMicSendPresentResp)
	err := c.cc.Invoke(ctx, "/present_middleware.PresentMiddleware/AllMicSendPresent", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *presentMiddlewareClient) FellowSendPresent(ctx context.Context, in *FellowSendPresentReq, opts ...grpc.CallOption) (*FellowSendPresentResp, error) {
	out := new(FellowSendPresentResp)
	err := c.cc.Invoke(ctx, "/present_middleware.PresentMiddleware/FellowSendPresent", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *presentMiddlewareClient) MagicSendPresent(ctx context.Context, in *MagicSendPresentReq, opts ...grpc.CallOption) (*MagicSendPresentResp, error) {
	out := new(MagicSendPresentResp)
	err := c.cc.Invoke(ctx, "/present_middleware.PresentMiddleware/MagicSendPresent", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *presentMiddlewareClient) SetSendPresent(ctx context.Context, in *SetSendPresentReq, opts ...grpc.CallOption) (*SetSendPresentResp, error) {
	out := new(SetSendPresentResp)
	err := c.cc.Invoke(ctx, "/present_middleware.PresentMiddleware/SetSendPresent", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *presentMiddlewareClient) FixBackpackOrder(ctx context.Context, in *FixBackpackOrderReq, opts ...grpc.CallOption) (*FixBackpackOrderResp, error) {
	out := new(FixBackpackOrderResp)
	err := c.cc.Invoke(ctx, "/present_middleware.PresentMiddleware/FixBackpackOrder", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *presentMiddlewareClient) GetChannelPresentBox(ctx context.Context, in *GetChannelPresentBoxReq, opts ...grpc.CallOption) (*GetChannelPresentBoxResp, error) {
	out := new(GetChannelPresentBoxResp)
	err := c.cc.Invoke(ctx, "/present_middleware.PresentMiddleware/GetChannelPresentBox", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *presentMiddlewareClient) UnpackPresentBox(ctx context.Context, in *UnpackPresentBoxReq, opts ...grpc.CallOption) (*UnpackPresentBoxResp, error) {
	out := new(UnpackPresentBoxResp)
	err := c.cc.Invoke(ctx, "/present_middleware.PresentMiddleware/UnpackPresentBox", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *presentMiddlewareClient) DelPresentConfig(ctx context.Context, in *DelPresentConfigReq, opts ...grpc.CallOption) (*DelPresentConfigResp, error) {
	out := new(DelPresentConfigResp)
	err := c.cc.Invoke(ctx, "/present_middleware.PresentMiddleware/DelPresentConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PresentMiddlewareServer is the server API for PresentMiddleware service.
type PresentMiddlewareServer interface {
	SendPresent(context.Context, *SendPresentReq) (*SendPresentResp, error)
	BatchSendPresent(context.Context, *BatchSendPresentReq) (*BatchSendPresentResp, error)
	ImSendPresent(context.Context, *ImSendPresentReq) (*ImSendPresentResp, error)
	AllMicSendPresent(context.Context, *AllMicSendPresentReq) (*AllMicSendPresentResp, error)
	FellowSendPresent(context.Context, *FellowSendPresentReq) (*FellowSendPresentResp, error)
	MagicSendPresent(context.Context, *MagicSendPresentReq) (*MagicSendPresentResp, error)
	SetSendPresent(context.Context, *SetSendPresentReq) (*SetSendPresentResp, error)
	FixBackpackOrder(context.Context, *FixBackpackOrderReq) (*FixBackpackOrderResp, error)
	GetChannelPresentBox(context.Context, *GetChannelPresentBoxReq) (*GetChannelPresentBoxResp, error)
	UnpackPresentBox(context.Context, *UnpackPresentBoxReq) (*UnpackPresentBoxResp, error)
	DelPresentConfig(context.Context, *DelPresentConfigReq) (*DelPresentConfigResp, error)
}

func RegisterPresentMiddlewareServer(s *grpc.Server, srv PresentMiddlewareServer) {
	s.RegisterService(&_PresentMiddleware_serviceDesc, srv)
}

func _PresentMiddleware_SendPresent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SendPresentReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PresentMiddlewareServer).SendPresent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/present_middleware.PresentMiddleware/SendPresent",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PresentMiddlewareServer).SendPresent(ctx, req.(*SendPresentReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PresentMiddleware_BatchSendPresent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchSendPresentReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PresentMiddlewareServer).BatchSendPresent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/present_middleware.PresentMiddleware/BatchSendPresent",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PresentMiddlewareServer).BatchSendPresent(ctx, req.(*BatchSendPresentReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PresentMiddleware_ImSendPresent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ImSendPresentReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PresentMiddlewareServer).ImSendPresent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/present_middleware.PresentMiddleware/ImSendPresent",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PresentMiddlewareServer).ImSendPresent(ctx, req.(*ImSendPresentReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PresentMiddleware_AllMicSendPresent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AllMicSendPresentReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PresentMiddlewareServer).AllMicSendPresent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/present_middleware.PresentMiddleware/AllMicSendPresent",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PresentMiddlewareServer).AllMicSendPresent(ctx, req.(*AllMicSendPresentReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PresentMiddleware_FellowSendPresent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FellowSendPresentReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PresentMiddlewareServer).FellowSendPresent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/present_middleware.PresentMiddleware/FellowSendPresent",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PresentMiddlewareServer).FellowSendPresent(ctx, req.(*FellowSendPresentReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PresentMiddleware_MagicSendPresent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MagicSendPresentReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PresentMiddlewareServer).MagicSendPresent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/present_middleware.PresentMiddleware/MagicSendPresent",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PresentMiddlewareServer).MagicSendPresent(ctx, req.(*MagicSendPresentReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PresentMiddleware_SetSendPresent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetSendPresentReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PresentMiddlewareServer).SetSendPresent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/present_middleware.PresentMiddleware/SetSendPresent",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PresentMiddlewareServer).SetSendPresent(ctx, req.(*SetSendPresentReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PresentMiddleware_FixBackpackOrder_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FixBackpackOrderReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PresentMiddlewareServer).FixBackpackOrder(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/present_middleware.PresentMiddleware/FixBackpackOrder",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PresentMiddlewareServer).FixBackpackOrder(ctx, req.(*FixBackpackOrderReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PresentMiddleware_GetChannelPresentBox_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChannelPresentBoxReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PresentMiddlewareServer).GetChannelPresentBox(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/present_middleware.PresentMiddleware/GetChannelPresentBox",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PresentMiddlewareServer).GetChannelPresentBox(ctx, req.(*GetChannelPresentBoxReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PresentMiddleware_UnpackPresentBox_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UnpackPresentBoxReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PresentMiddlewareServer).UnpackPresentBox(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/present_middleware.PresentMiddleware/UnpackPresentBox",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PresentMiddlewareServer).UnpackPresentBox(ctx, req.(*UnpackPresentBoxReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PresentMiddleware_DelPresentConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelPresentConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PresentMiddlewareServer).DelPresentConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/present_middleware.PresentMiddleware/DelPresentConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PresentMiddlewareServer).DelPresentConfig(ctx, req.(*DelPresentConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _PresentMiddleware_serviceDesc = grpc.ServiceDesc{
	ServiceName: "present_middleware.PresentMiddleware",
	HandlerType: (*PresentMiddlewareServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SendPresent",
			Handler:    _PresentMiddleware_SendPresent_Handler,
		},
		{
			MethodName: "BatchSendPresent",
			Handler:    _PresentMiddleware_BatchSendPresent_Handler,
		},
		{
			MethodName: "ImSendPresent",
			Handler:    _PresentMiddleware_ImSendPresent_Handler,
		},
		{
			MethodName: "AllMicSendPresent",
			Handler:    _PresentMiddleware_AllMicSendPresent_Handler,
		},
		{
			MethodName: "FellowSendPresent",
			Handler:    _PresentMiddleware_FellowSendPresent_Handler,
		},
		{
			MethodName: "MagicSendPresent",
			Handler:    _PresentMiddleware_MagicSendPresent_Handler,
		},
		{
			MethodName: "SetSendPresent",
			Handler:    _PresentMiddleware_SetSendPresent_Handler,
		},
		{
			MethodName: "FixBackpackOrder",
			Handler:    _PresentMiddleware_FixBackpackOrder_Handler,
		},
		{
			MethodName: "GetChannelPresentBox",
			Handler:    _PresentMiddleware_GetChannelPresentBox_Handler,
		},
		{
			MethodName: "UnpackPresentBox",
			Handler:    _PresentMiddleware_UnpackPresentBox_Handler,
		},
		{
			MethodName: "DelPresentConfig",
			Handler:    _PresentMiddleware_DelPresentConfig_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "tt/quicksilver/present-middleware/present-middleware.proto",
}

func init() {
	proto.RegisterFile("tt/quicksilver/present-middleware/present-middleware.proto", fileDescriptor_present_middleware_6a5ad00162c10c32)
}

var fileDescriptor_present_middleware_6a5ad00162c10c32 = []byte{
	// 4491 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xec, 0x5c, 0x4d, 0x6c, 0x1b, 0x49,
	0x76, 0x16, 0x7f, 0xc4, 0x9f, 0xc7, 0xbf, 0x56, 0x4b, 0xb2, 0x35, 0xf6, 0xcc, 0x58, 0xd3, 0xf6,
	0x78, 0xb5, 0xda, 0x8d, 0x1d, 0x38, 0x3f, 0xd8, 0x2c, 0x36, 0xf0, 0x50, 0x14, 0x25, 0x73, 0x4d,
	0x4a, 0x44, 0x93, 0xb2, 0x77, 0x82, 0x4d, 0x3a, 0x2d, 0xb2, 0x44, 0xf5, 0x9a, 0xec, 0xee, 0xe9,
	0x6e, 0x5a, 0xd2, 0x39, 0xc0, 0x1e, 0x32, 0x09, 0x12, 0x6c, 0x02, 0xe4, 0x92, 0x4b, 0xae, 0x39,
	0xee, 0x35, 0x08, 0x72, 0x48, 0xae, 0xc1, 0x02, 0x39, 0xe6, 0x14, 0xe4, 0x90, 0x4b, 0x80, 0xe4,
	0x12, 0x04, 0x48, 0x72, 0x09, 0xea, 0x55, 0x75, 0xb3, 0x7f, 0x8a, 0x14, 0x25, 0xcc, 0xcc, 0xee,
	0x62, 0x7c, 0x63, 0xbf, 0x7a, 0xf5, 0xf7, 0xea, 0xbd, 0x57, 0xef, 0x7d, 0x55, 0x45, 0xf8, 0xae,
	0xe7, 0x3d, 0xfd, 0x6c, 0x6a, 0x0c, 0xde, 0xb8, 0xc6, 0xf8, 0x2d, 0x71, 0x9e, 0xda, 0x0e, 0x71,
	0x89, 0xe9, 0xfd, 0xca, 0xc4, 0x18, 0x0e, 0xc7, 0xe4, 0x42, 0x77, 0x88, 0x80, 0xf4, 0xc4, 0x76,
	0x2c, 0xcf, 0x92, 0x65, 0x5e, 0xa2, 0xcd, 0x4a, 0x14, 0x15, 0xe4, 0x7d, 0x47, 0xbf, 0xe8, 0xb2,
	0x92, 0xae, 0x31, 0xf0, 0xa6, 0x0e, 0x91, 0xbf, 0x07, 0xc5, 0xb1, 0x61, 0x12, 0x6d, 0x6c, 0xb8,
	0xde, 0x56, 0x6a, 0x3b, 0xb3, 0x53, 0x7a, 0xf6, 0xe0, 0x49, 0xb2, 0xf6, 0x13, 0x5e, 0xad, 0x6d,
	0x98, 0x44, 0x2d, 0xd0, 0x1a, 0x6d, 0xc3, 0xf5, 0x94, 0x11, 0x94, 0x42, 0x05, 0xf2, 0x5d, 0xc8,
	0x1b, 0x1e, 0x99, 0x68, 0xc6, 0x70, 0x2b, 0xb5, 0x9d, 0xda, 0xa9, 0xa8, 0x39, 0xfa, 0xd9, 0x1a,
	0xca, 0xcf, 0x01, 0x6c, 0xcb, 0x30, 0x3d, 0xd6, 0x4d, 0x1a, 0xbb, 0xd9, 0x5e, 0xd0, 0x4d, 0x97,
	0x32, 0xab, 0x45, 0xac, 0x83, 0x1d, 0xed, 0x42, 0x39, 0x5c, 0x24, 0x97, 0x21, 0x75, 0x89, 0x7d,
	0xa4, 0xd5, 0xd4, 0x25, 0xfd, 0xba, 0xda, 0x4a, 0xb3, 0xaf, 0x2b, 0xe5, 0x67, 0x59, 0xa8, 0x72,
	0xe6, 0x1e, 0x31, 0x87, 0x1d, 0x77, 0x24, 0xef, 0x43, 0x91, 0x0d, 0xcc, 0x3c, 0xb3, 0xb0, 0x5a,
	0xe9, 0xd9, 0x37, 0x16, 0x74, 0x4f, 0xab, 0xb5, 0xe8, 0xc8, 0xcd, 0x33, 0x4b, 0x2d, 0x18, 0xfc,
	0x97, 0x7c, 0x1f, 0x8a, 0x2e, 0x31, 0x87, 0x9a, 0x67, 0x4c, 0x08, 0x76, 0x97, 0x55, 0x0b, 0x94,
	0xd0, 0x37, 0x26, 0x44, 0xfe, 0x00, 0x60, 0x70, 0xae, 0x9b, 0x26, 0x19, 0xd3, 0xe9, 0x67, 0x70,
	0xfa, 0x45, 0x4e, 0x69, 0x0d, 0xe5, 0xf7, 0x00, 0x59, 0xb5, 0xa9, 0x31, 0xdc, 0xca, 0x62, 0x61,
	0x9e, 0x7e, 0x9f, 0x18, 0x43, 0xf9, 0x23, 0x28, 0x63, 0x91, 0x3e, 0x18, 0x58, 0x53, 0xd3, 0xdb,
	0x5a, 0xdd, 0x4e, 0xed, 0x14, 0xd5, 0x12, 0xa5, 0xd5, 0x19, 0x49, 0x7e, 0x08, 0x15, 0x64, 0x31,
	0x8d, 0xc1, 0x1b, 0x53, 0x9f, 0x90, 0xad, 0x1c, 0xf2, 0x60, 0xbd, 0x23, 0x4e, 0xa3, 0x23, 0xf0,
	0x74, 0x67, 0x44, 0x3c, 0xec, 0x24, 0xcf, 0x46, 0xc0, 0x28, 0xb4, 0x9b, 0x8f, 0xa1, 0xca, 0x8b,
	0xfd, 0x8e, 0x0a, 0xd8, 0x48, 0x85, 0x51, 0xfd, 0xae, 0xbe, 0x01, 0x35, 0xce, 0x16, 0x74, 0x56,
	0x44, 0x3e, 0x5e, 0x3b, 0xe8, 0xee, 0x01, 0x94, 0xc8, 0xa5, 0x47, 0x47, 0xf5, 0x23, 0xd7, 0x32,
	0xb7, 0x00, 0x99, 0x80, 0x91, 0xbe, 0xef, 0x5a, 0xa6, 0xfc, 0x12, 0xd6, 0xce, 0x1c, 0x6b, 0xa2,
	0x4d, 0x5d, 0xe2, 0x68, 0xb6, 0x63, 0x9d, 0x19, 0x63, 0xb2, 0x55, 0x42, 0xe1, 0x0b, 0x55, 0xec,
	0xc4, 0x25, 0x4e, 0x97, 0xb1, 0xa9, 0x35, 0x5a, 0x33, 0x44, 0x90, 0x8f, 0x61, 0xdd, 0x9f, 0x5c,
	0xb8, 0xb9, 0xf2, 0x72, 0xcd, 0xad, 0x71, 0x31, 0x84, 0x1a, 0xdc, 0x85, 0x35, 0xcb, 0x1c, 0x5f,
	0x69, 0xee, 0xb9, 0x75, 0xa1, 0x4d, 0x88, 0xeb, 0xea, 0x23, 0xb2, 0x55, 0xd9, 0x4e, 0xed, 0x14,
	0xd4, 0x1a, 0x2d, 0xe8, 0x9d, 0x5b, 0x17, 0x1d, 0x46, 0x56, 0xfe, 0x24, 0x0f, 0x55, 0xaa, 0x13,
	0x5c, 0x3d, 0x54, 0xf2, 0x59, 0x64, 0x3d, 0x53, 0xd1, 0xf5, 0xfc, 0x00, 0xe0, 0x54, 0xf7, 0x06,
	0xe7, 0x9a, 0x77, 0x65, 0x33, 0x3d, 0xa9, 0xa8, 0x45, 0xa4, 0xf4, 0xaf, 0x6c, 0x22, 0x1f, 0x40,
	0x89, 0xcf, 0x04, 0xb5, 0x31, 0x83, 0x33, 0xf8, 0x78, 0x81, 0x36, 0xf6, 0x91, 0x1b, 0x75, 0x91,
	0x2f, 0x30, 0x6a, 0x63, 0x54, 0xe1, 0xb2, 0x71, 0x85, 0x7b, 0x00, 0xa8, 0x41, 0x9a, 0x6b, 0x4d,
	0x9d, 0x01, 0x41, 0xa5, 0xaa, 0xa8, 0x40, 0x49, 0x3d, 0xa4, 0x50, 0x06, 0xb4, 0x09, 0xce, 0x90,
	0x63, 0x0c, 0x94, 0x34, 0x63, 0xc0, 0x16, 0x26, 0xc4, 0x3b, 0xb7, 0x7c, 0x85, 0xc2, 0x16, 0x3a,
	0x48, 0x99, 0xd9, 0x03, 0x9d, 0x67, 0x01, 0x8b, 0x99, 0x3d, 0xd0, 0x69, 0x76, 0x41, 0x1a, 0x3a,
	0xfa, 0x85, 0xe6, 0xcf, 0xcb, 0x36, 0x06, 0xa8, 0x48, 0xa5, 0x67, 0x8f, 0x45, 0x73, 0x4d, 0xba,
	0x26, 0xb5, 0x3a, 0x8c, 0xd0, 0xe4, 0x6d, 0x28, 0x1b, 0xae, 0x66, 0xd9, 0x9e, 0xf6, 0x56, 0x1f,
	0x1b, 0x43, 0xd4, 0xb8, 0x82, 0x0a, 0x86, 0x7b, 0x6c, 0x7b, 0xaf, 0x28, 0x85, 0x2e, 0xca, 0x85,
	0xe1, 0x9d, 0x6b, 0xb6, 0x7e, 0x85, 0x8a, 0x56, 0x50, 0xf3, 0xf4, 0xbb, 0xab, 0x5f, 0xc9, 0x3b,
	0x20, 0x9d, 0xea, 0x83, 0x37, 0xb6, 0x3e, 0x78, 0xa3, 0xf9, 0x3e, 0xaa, 0x8c, 0x43, 0xae, 0xfa,
	0xf4, 0x16, 0xf3, 0x55, 0xf7, 0xa1, 0xc8, 0x1a, 0x99, 0xba, 0xe7, 0x5c, 0x21, 0xb0, 0xd5, 0xee,
	0xd4, 0x3d, 0x97, 0x7f, 0x0b, 0x8a, 0x94, 0xce, 0x96, 0xae, 0x8a, 0xd3, 0x79, 0x5f, 0xb8, 0x74,
	0x53, 0xf7, 0x9c, 0x79, 0x0f, 0x9b, 0xff, 0xa2, 0xeb, 0x3e, 0x18, 0x1b, 0x94, 0x0f, 0x2b, 0xd7,
	0xae, 0x5d, 0xf7, 0x06, 0x72, 0xb3, 0x75, 0x1f, 0x04, 0xbf, 0xe5, 0x7b, 0x10, 0x38, 0x9d, 0x2d,
	0x69, 0x3b, 0xb5, 0x93, 0x09, 0x39, 0xa1, 0x67, 0xb0, 0xe9, 0x4e, 0x1d, 0xdb, 0x31, 0x5c, 0xa2,
	0x91, 0xb3, 0x33, 0x32, 0xf0, 0x34, 0x66, 0xea, 0x6b, 0x38, 0xd5, 0x75, 0xbf, 0xb0, 0x89, 0x65,
	0x0d, 0x34, 0xf8, 0xc7, 0x50, 0xf3, 0xf5, 0x68, 0xa4, 0x4f, 0x08, 0x15, 0x8c, 0x8c, 0xdc, 0x15,
	0x4e, 0x3e, 0xd4, 0x27, 0x84, 0x79, 0x30, 0xc3, 0xd5, 0x26, 0xd3, 0xb1, 0x67, 0x6c, 0xad, 0x33,
	0xe1, 0x1a, 0x6e, 0x87, 0x7e, 0xd2, 0x26, 0x50, 0x11, 0x42, 0xfa, 0xb8, 0xc1, 0x9a, 0xa0, 0xe4,
	0x46, 0xa0, 0x93, 0x0f, 0xa1, 0x72, 0x6a, 0x38, 0xde, 0xf9, 0x50, 0xbf, 0x62, 0x4a, 0xb3, 0x89,
	0x5c, 0x65, 0x9f, 0x88, 0x8a, 0x43, 0x99, 0xa6, 0xae, 0x61, 0x12, 0xd7, 0x65, 0x4c, 0x77, 0x38,
	0x13, 0x27, 0x52, 0x26, 0xe5, 0x7f, 0x53, 0xb0, 0x96, 0x30, 0x0f, 0xf9, 0x25, 0x54, 0x5c, 0xc3,
	0x1c, 0x8d, 0x89, 0xc6, 0xec, 0x84, 0xbb, 0xfa, 0x47, 0x22, 0x21, 0xf7, 0x90, 0xb1, 0x1f, 0xf8,
	0x85, 0x17, 0x2b, 0x6a, 0xd9, 0x0d, 0xd1, 0xe4, 0x17, 0x50, 0xc6, 0xc9, 0xfa, 0x6d, 0xa5, 0xb1,
	0xad, 0x87, 0xa2, 0xb6, 0x50, 0x0a, 0x91, 0xa6, 0x4a, 0x93, 0x19, 0x49, 0xde, 0x07, 0x60, 0x2d,
	0x51, 0xc5, 0xe3, 0x06, 0x7f, 0x5d, 0x3b, 0x54, 0x19, 0x5f, 0xac, 0xa8, 0x45, 0xac, 0x48, 0x3f,
	0xf6, 0x0a, 0x90, 0x63, 0x45, 0xca, 0x1f, 0xa7, 0x40, 0x8a, 0x0f, 0x5f, 0x96, 0x20, 0x33, 0xf3,
	0x45, 0xf4, 0x67, 0x78, 0x37, 0x4e, 0x47, 0x76, 0xe3, 0x0d, 0x58, 0x65, 0x5a, 0xc1, 0x76, 0x29,
	0xf6, 0x41, 0xd7, 0xd7, 0x72, 0x86, 0xc4, 0xf1, 0xbd, 0x49, 0x51, 0xcd, 0xe3, 0x77, 0x0b, 0x3d,
	0xda, 0x90, 0xe8, 0x63, 0xcd, 0xb3, 0xde, 0x10, 0x93, 0xef, 0x4f, 0x45, 0x4a, 0xe9, 0x53, 0x82,
	0xf2, 0x37, 0x29, 0xa8, 0xc5, 0x44, 0x30, 0x1b, 0x4e, 0xe6, 0x96, 0xc3, 0x79, 0x0c, 0xb5, 0xa9,
	0x69, 0x7c, 0x36, 0x25, 0x5a, 0x6c, 0x54, 0x15, 0x46, 0x3e, 0xe6, 0x63, 0x7b, 0x0e, 0x45, 0xdc,
	0x11, 0xd0, 0xa8, 0x56, 0x31, 0xb2, 0x50, 0x44, 0xb2, 0x9d, 0x8d, 0x8d, 0xd9, 0xe5, 0x94, 0xff,
	0x52, 0x7e, 0x08, 0xd5, 0x68, 0x99, 0x40, 0x94, 0x61, 0xd9, 0xa4, 0x17, 0xc9, 0x26, 0x13, 0x97,
	0x4d, 0x2f, 0x22, 0x1a, 0xba, 0x90, 0xf2, 0x27, 0xd1, 0x60, 0x24, 0x33, 0x4f, 0x1b, 0xb8, 0x7e,
	0x27, 0x03, 0x11, 0xe5, 0x8f, 0x52, 0x50, 0x8b, 0x95, 0xfe, 0x3c, 0xd7, 0xff, 0x27, 0x19, 0x28,
	0xf8, 0x0e, 0x8f, 0xee, 0xab, 0xbe, 0x1b, 0x40, 0x4f, 0x89, 0x26, 0xcc, 0x46, 0xe5, 0xfb, 0x19,
	0xca, 0x8b, 0xa6, 0xfe, 0x6d, 0x90, 0x6d, 0xe2, 0xb8, 0x96, 0xa9, 0x87, 0x99, 0xd9, 0x60, 0x25,
	0xbf, 0x24, 0xe0, 0xfe, 0x10, 0x4a, 0xc6, 0x44, 0x9b, 0xb8, 0x23, 0xc6, 0xc6, 0x43, 0x2c, 0x63,
	0xd2, 0x71, 0x47, 0xe8, 0x13, 0xfe, 0x2c, 0x05, 0xb5, 0x46, 0xac, 0x07, 0x09, 0xca, 0x9c, 0xa4,
	0x1d, 0x1d, 0x1f, 0x35, 0xa5, 0x15, 0x59, 0x86, 0xea, 0x8c, 0xa2, 0x76, 0xea, 0x6d, 0x29, 0x25,
	0xdf, 0x01, 0x39, 0x4a, 0xd3, 0xea, 0xed, 0xb6, 0x94, 0x0e, 0xd3, 0x3b, 0xf5, 0xc3, 0x56, 0x43,
	0xeb, 0x9e, 0xf4, 0x5e, 0x48, 0x99, 0x70, 0x1b, 0xf5, 0x76, 0xbb, 0xd3, 0x6a, 0x48, 0xd9, 0x30,
	0x2f, 0xa3, 0x61, 0x1b, 0xab, 0x4a, 0x17, 0xa4, 0x6e, 0x7c, 0x26, 0x35, 0x28, 0x31, 0x9a, 0x3f,
	0xa8, 0x35, 0xa8, 0x04, 0x04, 0x3e, 0xa6, 0x2d, 0xd8, 0x88, 0x90, 0xb4, 0x5e, 0xf3, 0x68, 0xbf,
	0xa9, 0x4a, 0x69, 0xe5, 0x11, 0xe4, 0x5a, 0x13, 0x6c, 0xa7, 0x04, 0xf9, 0x56, 0xc7, 0x6f, 0xa3,
	0x02, 0x45, 0xfc, 0x60, 0xf5, 0xa9, 0x93, 0x58, 0x4b, 0x6c, 0x24, 0x74, 0xe9, 0x75, 0xdb, 0x6e,
	0xf9, 0x7a, 0xc2, 0x3e, 0xe8, 0x96, 0x32, 0xd1, 0x9d, 0x37, 0xc4, 0x6b, 0xf9, 0xaa, 0x12, 0x7c,
	0xcb, 0x07, 0x34, 0x3a, 0x75, 0xde, 0x1a, 0x03, 0x12, 0x8e, 0x57, 0x84, 0x0a, 0xdb, 0x63, 0x7c,
	0x0d, 0xcf, 0x19, 0xa3, 0xc2, 0x96, 0x78, 0x45, 0xd4, 0xd9, 0x3f, 0xcd, 0x42, 0x2d, 0x12, 0x43,
	0xb9, 0xb6, 0xfc, 0xdb, 0x50, 0xa0, 0xcb, 0x19, 0x32, 0x04, 0xe5, 0x9a, 0xa8, 0xbc, 0xe3, 0x8e,
	0xd4, 0xfc, 0xc4, 0x1d, 0xe1, 0x64, 0xbe, 0x0b, 0xef, 0x4d, 0xc8, 0xe4, 0x94, 0x38, 0xda, 0xc0,
	0x32, 0x3d, 0xc7, 0x38, 0x9d, 0x7a, 0x86, 0x65, 0x6a, 0xfa, 0x70, 0x48, 0xfc, 0x79, 0xdc, 0x65,
	0x0c, 0x8d, 0x50, 0x79, 0x9d, 0x16, 0xcf, 0xb1, 0x01, 0x1a, 0x53, 0x4d, 0x1d, 0xcd, 0x3b, 0x25,
	0xba, 0xe9, 0xa2, 0x15, 0x64, 0xd4, 0xe2, 0x60, 0xea, 0xf4, 0x91, 0x10, 0x0f, 0x99, 0x56, 0x13,
	0x21, 0x13, 0x8d, 0x88, 0xf0, 0x17, 0x35, 0xa2, 0x1c, 0x8f, 0x88, 0x90, 0xc0, 0x76, 0x3f, 0x5e,
	0xe8, 0x90, 0x89, 0x6e, 0x98, 0x3c, 0xa2, 0x2a, 0x33, 0xa2, 0x8a, 0x34, 0xf9, 0xc0, 0x0f, 0x1e,
	0x51, 0x28, 0x05, 0x14, 0xca, 0xa2, 0x54, 0x65, 0x8f, 0x32, 0x53, 0x69, 0x50, 0xc9, 0xb0, 0x28,
	0x13, 0x65, 0xf3, 0x3d, 0x00, 0x6e, 0xcd, 0xb4, 0x9d, 0x22, 0xb6, 0xf3, 0x81, 0xa8, 0x1d, 0xe6,
	0x47, 0xe9, 0x72, 0x15, 0x2d, 0xff, 0x27, 0x8b, 0xed, 0x6d, 0xc3, 0x21, 0x2c, 0xd7, 0x01, 0x36,
	0x51, 0x46, 0xc2, 0x40, 0xa3, 0x01, 0x70, 0x6a, 0x5d, 0x6a, 0x43, 0xe2, 0xe9, 0xc6, 0x98, 0x07,
	0xf5, 0x8f, 0x16, 0x0d, 0xd3, 0xba, 0xdc, 0x47, 0x5e, 0xb5, 0x78, 0xea, 0xff, 0x54, 0xbe, 0x03,
	0xc5, 0xa0, 0xf7, 0x1b, 0x39, 0x5d, 0xe5, 0x35, 0x6c, 0x46, 0x76, 0xff, 0x05, 0xae, 0x7b, 0x0b,
	0xf2, 0x7e, 0xbe, 0xc3, 0x1b, 0xe1, 0x9f, 0xb2, 0x0c, 0x59, 0x4c, 0x6f, 0x98, 0xcf, 0xc6, 0xdf,
	0xca, 0xbf, 0xa5, 0xe0, 0x4e, 0x58, 0xb2, 0xa1, 0xe0, 0xe2, 0x26, 0x4d, 0xdf, 0x83, 0x42, 0x90,
	0x3d, 0xb1, 0xe6, 0x83, 0xef, 0x78, 0xde, 0x94, 0x4d, 0xe4, 0x4d, 0x7b, 0x50, 0xbe, 0x4d, 0xca,
	0x54, 0x9a, 0x86, 0xb2, 0x9b, 0x07, 0x50, 0x1a, 0x4c, 0x5d, 0xcf, 0x9a, 0x68, 0x1e, 0xb9, 0xf4,
	0x30, 0xd2, 0x2d, 0xaa, 0xc0, 0x48, 0x7d, 0x72, 0xe9, 0x29, 0x9f, 0xaf, 0xc2, 0xba, 0x20, 0xdb,
	0x9d, 0x9f, 0xc2, 0x07, 0x06, 0x93, 0x0e, 0x1b, 0x0c, 0xcd, 0x11, 0x68, 0x02, 0xc5, 0x82, 0x4d,
	0x6e, 0x4c, 0x40, 0x49, 0x2c, 0xc4, 0x94, 0x1f, 0x41, 0x35, 0xc4, 0xa0, 0xbd, 0x7d, 0xc6, 0x33,
	0x95, 0xf2, 0x8c, 0xe7, 0xd5, 0x33, 0xda, 0xeb, 0xd9, 0xd8, 0xba, 0xa0, 0xbd, 0x32, 0xa3, 0xca,
	0xd1, 0xcf, 0x20, 0xe8, 0x44, 0xb5, 0x46, 0x7b, 0xc2, 0xa0, 0x13, 0x57, 0x86, 0x6e, 0x34, 0xd8,
	0x32, 0x33, 0x17, 0x3e, 0x80, 0x3c, 0x4b, 0xe0, 0x68, 0x01, 0x72, 0xf1, 0x51, 0x7c, 0xc5, 0x99,
	0xca, 0x13, 0x58, 0x1f, 0x5e, 0x99, 0xfa, 0xc4, 0x18, 0x68, 0x1e, 0x99, 0xd8, 0x63, 0xdd, 0x43,
	0x87, 0xc0, 0xcc, 0x68, 0x8d, 0x17, 0xf5, 0x79, 0x49, 0x6b, 0x28, 0x3f, 0x85, 0x0d, 0xc3, 0xd5,
	0xde, 0x1a, 0xae, 0x71, 0x4a, 0x63, 0x57, 0x4b, 0xa3, 0x63, 0x23, 0x0e, 0xcf, 0x61, 0xd6, 0x0c,
	0xf7, 0x15, 0x2b, 0xea, 0x5b, 0x3d, 0x2c, 0xa0, 0xd9, 0x8c, 0xe1, 0xb2, 0xd4, 0xd5, 0x0f, 0xe9,
	0x71, 0x8d, 0x0b, 0x6a, 0xd5, 0x70, 0x69, 0xe6, 0xda, 0xe3, 0x54, 0x9a, 0xf5, 0x07, 0x19, 0x01,
	0x5b, 0xbf, 0x0a, 0x8f, 0xcc, 0x39, 0x95, 0x25, 0x01, 0x3b, 0x20, 0x85, 0xf4, 0x85, 0x69, 0x66,
	0x95, 0xa5, 0xfd, 0x33, 0xa5, 0x41, 0xed, 0xfc, 0x16, 0xc8, 0xd8, 0xaf, 0x31, 0xa1, 0x02, 0xf3,
	0xe5, 0x5e, 0x9b, 0xc9, 0xbd, 0x35, 0xe9, 0x3a, 0x3c, 0xc1, 0xa0, 0x41, 0xdc, 0x8c, 0x89, 0xa9,
	0xa2, 0xc4, 0x82, 0x38, 0xdb, 0xe7, 0x41, 0x6d, 0xfc, 0x69, 0x36, 0xd0, 0xc6, 0xb0, 0x43, 0x9b,
	0xaf, 0x8d, 0x3b, 0x20, 0x79, 0x96, 0xa7, 0x8f, 0x59, 0x2e, 0x17, 0x56, 0xcc, 0x2a, 0xd2, 0xa9,
	0x3e, 0x37, 0x7c, 0x97, 0x1e, 0xca, 0xc6, 0x33, 0xf1, 0x6c, 0x3c, 0x82, 0xe9, 0x64, 0x17, 0x62,
	0x3a, 0xab, 0x8b, 0x30, 0x9d, 0xdc, 0x62, 0x4c, 0x27, 0xbf, 0x04, 0xa6, 0x53, 0x10, 0x60, 0x3a,
	0x31, 0x67, 0x51, 0x14, 0x80, 0x2c, 0x3e, 0x9a, 0x80, 0xd0, 0x1a, 0xa0, 0xa3, 0xdf, 0xbd, 0x6e,
	0xc3, 0x48, 0x42, 0x0a, 0x6d, 0xc3, 0xf5, 0xa2, 0x30, 0x59, 0xe9, 0xb6, 0x30, 0x99, 0x10, 0xf7,
	0x29, 0xdf, 0x12, 0xf7, 0x09, 0x67, 0x9d, 0x95, 0x48, 0xd6, 0xa9, 0xfc, 0x77, 0x8a, 0x46, 0x14,
	0x91, 0x90, 0x83, 0x2e, 0xa7, 0x9f, 0x64, 0xdb, 0xa8, 0x32, 0x45, 0xb5, 0xc0, 0x73, 0x67, 0x1b,
	0x9d, 0x22, 0x2b, 0xb4, 0x2d, 0xc7, 0xd7, 0x17, 0x9e, 0x5a, 0x77, 0x2d, 0x07, 0xdd, 0xc4, 0x90,
	0xb0, 0x50, 0x87, 0x41, 0x78, 0x65, 0xb5, 0xc0, 0x08, 0x0c, 0x50, 0xe1, 0xb5, 0x51, 0x93, 0xb2,
	0xe1, 0xda, 0x7e, 0xe2, 0xea, 0x11, 0x67, 0x62, 0xd0, 0x68, 0x16, 0x59, 0x98, 0xc2, 0x94, 0x7d,
	0xa2, 0xaf, 0x6f, 0xfe, 0x00, 0x83, 0x08, 0x81, 0x0f, 0x10, 0x21, 0x3a, 0x5e, 0xf8, 0x96, 0x38,
	0xae, 0x61, 0xf9, 0x21, 0x42, 0x85, 0x51, 0x5f, 0x31, 0xa2, 0xf2, 0xaf, 0x59, 0x58, 0xc7, 0x65,
	0x8c, 0x61, 0x52, 0x73, 0xad, 0x25, 0xaa, 0xc7, 0xe9, 0xb8, 0x1e, 0x8b, 0x63, 0xa1, 0x18, 0x80,
	0x94, 0xbd, 0x0e, 0x40, 0xba, 0x61, 0x34, 0x14, 0xb5, 0xcb, 0xfc, 0x5c, 0xbb, 0xfc, 0x6a, 0x3c,
	0xf6, 0x26, 0xe4, 0x74, 0xdb, 0x9e, 0x39, 0x69, 0x1e, 0x18, 0xdf, 0x87, 0x22, 0x0b, 0x84, 0x69,
	0x49, 0x29, 0x16, 0x19, 0x3f, 0x0e, 0x90, 0xd2, 0xa9, 0x31, 0x64, 0xe6, 0x57, 0xc6, 0x74, 0xb7,
	0x12, 0x80, 0xae, 0x68, 0x55, 0x61, 0x37, 0x51, 0x89, 0xba, 0x89, 0x78, 0x70, 0x5d, 0xbd, 0x5d,
	0x70, 0x4d, 0x13, 0x29, 0xdd, 0xb4, 0xbc, 0x73, 0x1a, 0x0a, 0xcf, 0x16, 0xba, 0xc6, 0x12, 0x29,
	0x5e, 0xb2, 0x00, 0x86, 0x91, 0x92, 0x30, 0x8c, 0xf2, 0x1f, 0x69, 0xd8, 0x48, 0x2a, 0x99, 0x6b,
	0xcb, 0x7b, 0x91, 0xa0, 0x3d, 0x75, 0x93, 0xf8, 0x34, 0x88, 0xdc, 0xa3, 0x71, 0x36, 0x83, 0xd2,
	0xe7, 0xc7, 0xd9, 0x99, 0xc5, 0x9a, 0x95, 0xbd, 0x2e, 0xce, 0x5e, 0x15, 0xc4, 0xd9, 0xdf, 0x8f,
	0xfa, 0xcd, 0x1c, 0xfa, 0xcd, 0x6f, 0x5e, 0x8b, 0xc2, 0x06, 0xf8, 0xc1, 0x5c, 0xb7, 0x99, 0xbf,
	0xa5, 0xdb, 0x54, 0x3e, 0xcf, 0xe2, 0x66, 0xe9, 0x73, 0xec, 0xe9, 0x2e, 0x79, 0x67, 0xd1, 0x5f,
	0x57, 0x8b, 0x56, 0xfe, 0x39, 0x0b, 0x52, 0x6b, 0x12, 0x73, 0xf0, 0xd1, 0x13, 0x9e, 0x54, 0xfc,
	0x84, 0xe7, 0x86, 0x80, 0xcf, 0x97, 0xac, 0x0e, 0x91, 0xf5, 0xce, 0xc7, 0xd6, 0x3b, 0xc0, 0x21,
	0x0a, 0xf3, 0x70, 0x88, 0x62, 0x6c, 0x6d, 0xc2, 0x32, 0x87, 0xc5, 0x32, 0x2f, 0xdd, 0xd2, 0x8b,
	0xc6, 0x0f, 0x18, 0xca, 0x89, 0x03, 0x86, 0x5d, 0x58, 0xf3, 0x1b, 0xc5, 0x38, 0x19, 0xe7, 0xc6,
	0x34, 0xc0, 0xb7, 0x5d, 0x1a, 0xcf, 0xe2, 0x14, 0x63, 0xc7, 0x27, 0xd5, 0xc4, 0xf1, 0xc9, 0x97,
	0x11, 0x49, 0x27, 0xd1, 0xf3, 0x35, 0x01, 0x7a, 0xfe, 0x77, 0x69, 0x58, 0x8b, 0x29, 0x97, 0x6b,
	0xcf, 0x77, 0x36, 0x61, 0x98, 0x86, 0xa1, 0xe0, 0x5f, 0x1c, 0x4c, 0x93, 0x59, 0x12, 0xa6, 0xc9,
	0xce, 0x87, 0x69, 0x56, 0xaf, 0xd9, 0x3e, 0x72, 0x8b, 0xf5, 0x36, 0x7f, 0xdd, 0xf6, 0x51, 0x48,
	0x6e, 0x1f, 0xca, 0x8f, 0xb3, 0xb0, 0x71, 0x40, 0xc6, 0x63, 0xeb, 0xe2, 0x9d, 0x89, 0xfe, 0x62,
	0x99, 0x68, 0x18, 0x35, 0xaa, 0x44, 0x61, 0x6c, 0x2a, 0x63, 0xcc, 0xf3, 0x58, 0x66, 0xcb, 0x3e,
	0x62, 0x5b, 0x69, 0x2d, 0xbe, 0x95, 0x46, 0xb1, 0x6f, 0x29, 0x8e, 0x7d, 0xff, 0x7d, 0x1a, 0x36,
	0x05, 0x8a, 0xf0, 0xce, 0x9c, 0x6e, 0x64, 0x4e, 0x3f, 0xcd, 0xc2, 0x46, 0x7d, 0x3c, 0xee, 0x18,
	0x83, 0x77, 0x29, 0xcd, 0xbb, 0x00, 0x48, 0x68, 0xe9, 0xb5, 0xb8, 0xa5, 0x2b, 0xff, 0x99, 0x86,
	0x4d, 0x81, 0xd2, 0xbc, 0x4b, 0x51, 0xbe, 0xb4, 0x14, 0xe5, 0x1f, 0x56, 0x61, 0xbd, 0xa3, 0x8f,
	0x12, 0x56, 0xba, 0x07, 0xd5, 0x91, 0x71, 0xe6, 0x31, 0x30, 0x2e, 0x74, 0x93, 0x4c, 0x78, 0x35,
	0xe2, 0xd0, 0x38, 0xc3, 0x53, 0x4b, 0xb5, 0x3c, 0xe2, 0xbf, 0x70, 0x84, 0xd7, 0x18, 0x74, 0x58,
	0xe3, 0x32, 0x51, 0x8d, 0x9b, 0x29, 0x7a, 0x76, 0xae, 0xa2, 0xaf, 0x5e, 0x73, 0xaa, 0x95, 0xbb,
	0xa5, 0x96, 0xee, 0x80, 0x34, 0xb0, 0x4c, 0x77, 0x3a, 0x09, 0x1d, 0x53, 0xe7, 0x39, 0x6e, 0xca,
	0xe8, 0xfe, 0x39, 0xf5, 0x03, 0x28, 0x0d, 0xac, 0xc9, 0x29, 0x75, 0xda, 0x67, 0xc6, 0x08, 0xed,
	0xbf, 0xac, 0x02, 0x25, 0x35, 0x90, 0x12, 0x81, 0xba, 0x8b, 0x51, 0xa8, 0xfb, 0x31, 0xd4, 0x26,
	0x54, 0xec, 0x9a, 0x6b, 0x1b, 0x8e, 0xe1, 0xcd, 0x6c, 0xba, 0x82, 0xe4, 0x1e, 0x52, 0x19, 0x2a,
	0x1a, 0xe1, 0x1b, 0x98, 0x1e, 0x37, 0xf1, 0x6a, 0x88, 0xb1, 0x61, 0x7a, 0x34, 0x90, 0x8d, 0x70,
	0x22, 0x00, 0xc9, 0x4e, 0x09, 0x6a, 0x21, 0xd6, 0x23, 0x7d, 0x42, 0x12, 0xbc, 0xc6, 0xc0, 0x32,
	0xf9, 0xd6, 0x1a, 0xe6, 0x6d, 0x0d, 0x2c, 0x33, 0x0a, 0xa7, 0x56, 0x43, 0x3e, 0x8e, 0xee, 0xb4,
	0x8f, 0xa1, 0x76, 0x6a, 0x44, 0xaf, 0x89, 0xb0, 0xed, 0xb6, 0x42, 0xc9, 0x33, 0x7c, 0x42, 0x81,
	0x8a, 0x7b, 0xe5, 0x6a, 0xfa, 0x94, 0x23, 0xe1, 0xb8, 0xeb, 0x16, 0xd4, 0x92, 0x7b, 0xe5, 0xd6,
	0xa7, 0x1e, 0x62, 0xe0, 0x71, 0xfb, 0x5b, 0x4b, 0xd8, 0x5f, 0x18, 0x38, 0x94, 0xa3, 0xc0, 0xe1,
	0x3f, 0xa5, 0xa0, 0xe0, 0x6b, 0xe1, 0x4d, 0xce, 0xcd, 0xc3, 0xa1, 0x45, 0x26, 0x19, 0x5a, 0x24,
	0x37, 0xd6, 0x8f, 0xa0, 0xac, 0x5f, 0xe8, 0xce, 0xd0, 0x8f, 0xed, 0x99, 0x16, 0x96, 0x90, 0xc6,
	0xe3, 0xfa, 0x68, 0x78, 0x91, 0x8b, 0x85, 0x17, 0x78, 0x6d, 0xc9, 0x21, 0xfa, 0x1b, 0xc3, 0x1c,
	0x69, 0x26, 0xb9, 0x70, 0x67, 0x3b, 0x6c, 0xd5, 0xa7, 0x1f, 0x91, 0x0b, 0xb7, 0x35, 0x54, 0xfe,
	0x2a, 0x05, 0x1b, 0x49, 0xdb, 0x5c, 0xde, 0x19, 0x62, 0x5d, 0xff, 0x52, 0x41, 0xdc, 0x19, 0x36,
	0x01, 0xf8, 0xe2, 0x5b, 0xb6, 0x7f, 0x13, 0xe6, 0xb1, 0xd8, 0x58, 0xcc, 0x61, 0x67, 0xa6, 0x13,
	0xc7, 0xb6, 0xa7, 0x16, 0x5d, 0xff, 0x27, 0xcd, 0x3b, 0xd6, 0x05, 0xfd, 0x08, 0xd1, 0xfc, 0xd4,
	0x12, 0x68, 0x7e, 0x7a, 0x21, 0x9a, 0x9f, 0xf9, 0xe5, 0x43, 0xf3, 0x1f, 0x40, 0x89, 0x09, 0xc2,
	0x76, 0x8c, 0x41, 0x70, 0xee, 0x8a, 0xa4, 0x2e, 0xa5, 0x28, 0x7f, 0x99, 0x05, 0x39, 0x29, 0x63,
	0x91, 0x83, 0x48, 0x2d, 0xeb, 0x20, 0xd2, 0x42, 0x07, 0xb1, 0xc0, 0xdf, 0xc6, 0xa5, 0x91, 0x5d,
	0x42, 0x1a, 0xab, 0xe2, 0xfb, 0xaa, 0xa1, 0xf5, 0xc8, 0x09, 0xd6, 0x23, 0xf0, 0x87, 0xf9, 0xa8,
	0x3f, 0xf4, 0x77, 0x33, 0xdc, 0x6a, 0x16, 0x9c, 0x91, 0x87, 0xa4, 0xe4, 0xef, 0x68, 0x6c, 0x37,
	0xc3, 0x1d, 0xe7, 0x3e, 0x14, 0xd1, 0x23, 0xf3, 0x13, 0x72, 0x44, 0xfb, 0x29, 0xc1, 0x77, 0xec,
	0x21, 0x95, 0x0c, 0x2f, 0xc7, 0x4c, 0x25, 0x71, 0x49, 0x92, 0xde, 0xaa, 0x94, 0xf4, 0x56, 0x5f,
	0xd5, 0x91, 0xc8, 0x7f, 0x65, 0xfc, 0x0d, 0x3a, 0x32, 0xe9, 0xf9, 0x61, 0x74, 0xec, 0xfc, 0x36,
	0xbd, 0xc4, 0xf9, 0x6d, 0x66, 0xf1, 0xf9, 0x6d, 0x76, 0xee, 0xf9, 0xed, 0xea, 0x12, 0xe7, 0xb7,
	0xb9, 0x25, 0xce, 0x6f, 0xe3, 0x89, 0xea, 0x9c, 0xd3, 0xd6, 0xc2, 0x4d, 0x4f, 0x5b, 0x8b, 0xf3,
	0x4e, 0x5b, 0x0f, 0x01, 0x98, 0x1b, 0x0f, 0x1d, 0xb1, 0xed, 0x5c, 0xa3, 0x6f, 0x75, 0x5a, 0x81,
	0x5d, 0xab, 0xc0, 0xba, 0xa8, 0x71, 0x77, 0x20, 0xc7, 0xe7, 0xc9, 0x76, 0x65, 0xfe, 0x45, 0x2d,
	0x81, 0x75, 0x10, 0x3a, 0xac, 0x67, 0xd5, 0x10, 0xd3, 0xe1, 0x6e, 0xc3, 0x31, 0x99, 0xaa, 0x56,
	0x66, 0x6e, 0xc3, 0x31, 0x31, 0x2e, 0xfb, 0x9f, 0xb4, 0xef, 0xfb, 0xa3, 0x7d, 0xdf, 0xf4, 0x34,
	0xff, 0x06, 0xee, 0x33, 0x2b, 0xc8, 0x93, 0x43, 0xc0, 0xc7, 0xea, 0xf5, 0xb7, 0xcf, 0x73, 0x4b,
	0xde, 0x3e, 0xcf, 0x0b, 0x6f, 0x9f, 0x2b, 0x50, 0xc1, 0x99, 0x05, 0x1b, 0x32, 0xf3, 0xb7, 0x18,
	0x14, 0x84, 0x42, 0xae, 0xc5, 0xee, 0x36, 0x7e, 0xd3, 0x02, 0x6e, 0x7e, 0xd3, 0x42, 0xf9, 0xc7,
	0x14, 0x94, 0xc2, 0xd6, 0xf9, 0x45, 0x5d, 0x13, 0x79, 0x08, 0x15, 0xce, 0xa6, 0xe9, 0x63, 0x43,
	0x77, 0xb9, 0x9b, 0x2d, 0x73, 0x62, 0x9d, 0xd2, 0x68, 0x67, 0x2e, 0xb9, 0xe4, 0xd2, 0xa6, 0x3f,
	0xe5, 0xe7, 0x50, 0xb4, 0x1d, 0xe3, 0xad, 0x31, 0x26, 0x23, 0xc2, 0xa3, 0xda, 0x8f, 0xe6, 0xcf,
	0x87, 0x33, 0xaa, 0xb3, 0x3a, 0x8a, 0x0b, 0x95, 0x48, 0x59, 0x78, 0xf8, 0xa9, 0xf9, 0xc3, 0x4f,
	0xc7, 0x86, 0x2f, 0x43, 0x36, 0x74, 0xe0, 0x8e, 0xbf, 0x69, 0x4b, 0x96, 0xed, 0x19, 0x16, 0xbf,
	0x5a, 0x55, 0x56, 0xfd, 0x4f, 0xe5, 0x07, 0x4c, 0x86, 0x27, 0x2f, 0x5f, 0xfb, 0xb7, 0xd4, 0xc6,
	0xe4, 0x2d, 0x19, 0xfb, 0xb7, 0xd4, 0xf0, 0x83, 0x52, 0x27, 0x64, 0xa8, 0x8f, 0x79, 0x5f, 0xec,
	0x83, 0xea, 0xdd, 0x39, 0xd1, 0x87, 0xda, 0x99, 0x33, 0x93, 0x62, 0x91, 0x52, 0x0e, 0x28, 0x41,
	0xf9, 0x55, 0x58, 0x3f, 0x30, 0x2e, 0xf7, 0xf8, 0x15, 0x6f, 0xd4, 0x0c, 0x7e, 0x7d, 0x3f, 0xd0,
	0x9c, 0x54, 0xf4, 0x6e, 0xd1, 0x1d, 0xd8, 0x48, 0xd6, 0x70, 0x6d, 0xe5, 0x67, 0x19, 0xd8, 0x6a,
	0xd3, 0x81, 0x9c, 0xd8, 0xfe, 0xed, 0x8a, 0x97, 0xfa, 0xd9, 0x1b, 0xfd, 0x9d, 0xa1, 0x2d, 0x73,
	0x4b, 0x41, 0xb0, 0xff, 0xc1, 0x2d, 0xf7, 0xbf, 0x43, 0xa8, 0x79, 0xd6, 0xad, 0x5e, 0x95, 0x54,
	0x3c, 0x2b, 0xf4, 0xa9, 0xfc, 0x2e, 0xac, 0x9f, 0x98, 0x74, 0x91, 0x67, 0x97, 0xd4, 0xa8, 0x6e,
	0x24, 0x2d, 0x78, 0x13, 0x72, 0xa7, 0xd6, 0xe5, 0xec, 0x1e, 0xda, 0xea, 0xa9, 0x75, 0x99, 0xc0,
	0xa0, 0xe2, 0x4f, 0x7e, 0x94, 0xbf, 0x48, 0xc1, 0x46, 0xb2, 0x7d, 0x76, 0xed, 0x11, 0x9b, 0x9b,
	0x45, 0xe4, 0xca, 0xe2, 0xab, 0x73, 0xb8, 0x8f, 0xe4, 0x4f, 0xd9, 0x0f, 0xf9, 0x13, 0x1a, 0x7f,
	0x79, 0x5a, 0xd0, 0x04, 0x0b, 0xc7, 0x3f, 0x14, 0x87, 0xe3, 0x41, 0x75, 0x70, 0x83, 0xdf, 0xca,
	0x9f, 0xa7, 0x82, 0x17, 0x52, 0x9c, 0x44, 0xc7, 0x84, 0xcb, 0x3c, 0x71, 0x47, 0x4b, 0x8c, 0x29,
	0x00, 0x25, 0x69, 0x1d, 0x1a, 0xc1, 0x47, 0xef, 0x03, 0xa6, 0x6f, 0x77, 0x1f, 0xf0, 0x0f, 0x32,
	0x20, 0xc5, 0xcb, 0x43, 0xb2, 0x4f, 0x85, 0x65, 0x2f, 0xd4, 0xa8, 0xf4, 0x17, 0xa7, 0x51, 0x99,
	0xdb, 0x68, 0x54, 0xd8, 0x0d, 0x64, 0x23, 0x6e, 0xe0, 0x3e, 0x8f, 0x58, 0x43, 0xc1, 0x30, 0xca,
	0x1b, 0x13, 0xec, 0x88, 0x37, 0xc8, 0xc5, 0xbc, 0x41, 0xcc, 0xb6, 0xf2, 0x09, 0xdb, 0xc2, 0x0c,
	0x72, 0xac, 0x5f, 0xb1, 0xea, 0x05, 0x06, 0x54, 0x21, 0x05, 0xeb, 0xdf, 0x34, 0xda, 0x51, 0xbe,
	0x03, 0x77, 0x0f, 0x89, 0xe7, 0x5f, 0xa4, 0x8e, 0x58, 0x46, 0x54, 0xe1, 0x53, 0x71, 0x85, 0xff,
	0xeb, 0x14, 0x6c, 0x89, 0xab, 0xba, 0xb6, 0xdc, 0x06, 0xc9, 0x97, 0x25, 0x5d, 0xcf, 0x10, 0x4a,
	0xb4, 0x8c, 0xf2, 0x57, 0xed, 0xe0, 0x1b, 0x23, 0xa9, 0x90, 0x0d, 0x84, 0x9e, 0x14, 0x2e, 0x69,
	0x03, 0xf8, 0xa2, 0xf0, 0x09, 0xac, 0xef, 0x07, 0x83, 0x64, 0x10, 0xcc, 0x22, 0xc0, 0x99, 0x6e,
	0x0b, 0x49, 0x7e, 0xd7, 0x56, 0xfe, 0x25, 0x03, 0x6b, 0x3d, 0xe2, 0x2d, 0xff, 0x3c, 0x6c, 0x3f,
	0xfa, 0xfe, 0x2b, 0xbd, 0xf4, 0x73, 0x90, 0x05, 0xaf, 0xbf, 0x32, 0xd7, 0xbc, 0xfe, 0xba, 0x05,
	0xd2, 0x1d, 0x3b, 0xbe, 0xcc, 0x25, 0x8e, 0x2f, 0xc3, 0x8f, 0xad, 0xf2, 0xd1, 0xc7, 0x56, 0x91,
	0x27, 0x54, 0x85, 0xd8, 0x13, 0xaa, 0xd8, 0x3b, 0xa8, 0xe2, 0x17, 0xf1, 0x0e, 0x0a, 0x62, 0xef,
	0xa0, 0x04, 0x6f, 0x9a, 0x4a, 0xa2, 0x37, 0x4d, 0x8f, 0xa0, 0x4a, 0x26, 0x36, 0x71, 0x2c, 0x47,
	0x73, 0x19, 0x50, 0xc8, 0xde, 0x84, 0x95, 0x39, 0xb5, 0x47, 0xbc, 0xd6, 0x50, 0xf9, 0xf7, 0x34,
	0x4d, 0xba, 0xbd, 0x38, 0xb0, 0xf2, 0x49, 0x02, 0x58, 0xf9, 0x78, 0x8e, 0xfe, 0x7d, 0x9d, 0x2e,
	0xb0, 0x3f, 0x17, 0xdc, 0x0c, 0xdf, 0x9e, 0x6f, 0x97, 0xc9, 0x5d, 0xe0, 0xc7, 0xab, 0x68, 0x50,
	0x8b, 0x5f, 0xf0, 0x66, 0xde, 0xbd, 0xe0, 0xfd, 0xda, 0xbf, 0xe0, 0x7d, 0x02, 0xeb, 0xbe, 0xf1,
	0x72, 0x74, 0x02, 0xa7, 0xc1, 0xd2, 0xe3, 0x35, 0x5e, 0xc4, 0x00, 0x04, 0x9c, 0xcd, 0x13, 0x58,
	0x7f, 0x6b, 0x90, 0x0b, 0xc3, 0x1c, 0x45, 0xf8, 0xd9, 0x61, 0xf0, 0x1a, 0x2f, 0x9a, 0xf1, 0x2b,
	0xe7, 0x00, 0xb3, 0xbd, 0x03, 0x91, 0x8c, 0x70, 0x80, 0x54, 0x9e, 0x05, 0x3f, 0xcf, 0x05, 0xc1,
	0xcf, 0x8d, 0x54, 0xfe, 0xff, 0xd2, 0x50, 0x0e, 0x97, 0xfd, 0x72, 0x05, 0x3d, 0x9b, 0x90, 0xe3,
	0xce, 0x93, 0x83, 0xdf, 0xee, 0xec, 0x36, 0x80, 0x17, 0x8e, 0x78, 0xf2, 0x2e, 0xf1, 0x7e, 0xf1,
	0x02, 0x9e, 0xdd, 0xbf, 0xcd, 0x04, 0xaf, 0x49, 0x7a, 0xc1, 0x06, 0x89, 0xc8, 0xd2, 0x3d, 0xb8,
	0xd3, 0xc4, 0x77, 0x57, 0x5a, 0xef, 0xf8, 0x44, 0x6d, 0x34, 0xb5, 0xfd, 0xe6, 0x41, 0xbb, 0x7e,
	0xd2, 0x6f, 0x4a, 0x2b, 0xf2, 0x36, 0xbc, 0x1f, 0x2d, 0x3b, 0x6c, 0x1d, 0xf4, 0xb5, 0xfe, 0x89,
	0x7a, 0xd4, 0xaf, 0xef, 0xb5, 0x9b, 0x52, 0x4a, 0x7e, 0x1f, 0xb6, 0x04, 0x1c, 0xbd, 0x17, 0xcd,
	0xf6, 0x81, 0x94, 0x96, 0x3f, 0x80, 0xf7, 0xa2, 0xa5, 0xbd, 0x6e, 0xb3, 0xd9, 0x78, 0xa1, 0xed,
	0xd5, 0xdb, 0x6d, 0x29, 0x23, 0xdf, 0x87, 0xbb, 0xb1, 0xae, 0xd5, 0xfa, 0x6b, 0x6c, 0x41, 0xca,
	0x26, 0xeb, 0x76, 0xea, 0xbd, 0x97, 0xcd, 0x7d, 0xad, 0x81, 0x4f, 0xce, 0xe4, 0x0d, 0x90, 0xa2,
	0xc5, 0xad, 0x8e, 0x94, 0x93, 0x15, 0xf8, 0x30, 0x4a, 0x3d, 0x3e, 0x38, 0x68, 0x35, 0x5a, 0xf5,
	0xb6, 0xd6, 0x78, 0x51, 0x3f, 0x3a, 0x6a, 0xb6, 0xa5, 0xbc, 0xbc, 0x05, 0x1b, 0x51, 0x9e, 0x83,
	0x66, 0xbb, 0x7d, 0xfc, 0x5a, 0x2a, 0xc8, 0x77, 0x61, 0x3d, 0xde, 0xe5, 0x61, 0xab, 0x21, 0x15,
	0xe5, 0xf7, 0x60, 0x33, 0x5a, 0xd0, 0x3e, 0xee, 0xf7, 0x9b, 0xea, 0xa7, 0x12, 0xc8, 0x0f, 0xe0,
	0x7e, 0xa2, 0xc7, 0xa6, 0xda, 0x3a, 0x3a, 0xd4, 0xd4, 0xe3, 0xe3, 0x8e, 0x54, 0x92, 0x3f, 0x82,
	0x0f, 0xa2, 0x0c, 0x7c, 0x24, 0x4c, 0x52, 0xdd, 0x97, 0x52, 0x39, 0xc9, 0x72, 0xa8, 0xd6, 0xf7,
	0x28, 0x5f, 0x4b, 0xd5, 0x0e, 0xeb, 0x9d, 0xa6, 0x54, 0xd9, 0xfd, 0x49, 0x26, 0x78, 0xe9, 0x16,
	0x5a, 0xbb, 0x3b, 0x20, 0x77, 0xd5, 0x66, 0xaf, 0x79, 0xd4, 0xf7, 0x6b, 0xee, 0x9d, 0x7c, 0x2a,
	0xad, 0xd0, 0x35, 0x8d, 0xd1, 0xbb, 0xf5, 0xc6, 0xcb, 0xfa, 0x21, 0x5d, 0xb1, 0x6d, 0x78, 0x5f,
	0x5c, 0xa6, 0x1d, 0xb4, 0xd4, 0x5e, 0x5f, 0x4a, 0xd3, 0xd9, 0xc6, 0x38, 0xb8, 0x84, 0x32, 0xf8,
	0x60, 0x2f, 0x5a, 0xc4, 0x44, 0x94, 0x95, 0x3f, 0x84, 0x7b, 0xb1, 0x12, 0x2e, 0x23, 0x1c, 0xd2,
	0x2a, 0x5d, 0x99, 0x39, 0xe5, 0xfe, 0xd0, 0x72, 0x82, 0xa1, 0xf9, 0x3c, 0xac, 0x97, 0xbc, 0x80,
	0x23, 0x2a, 0xee, 0x82, 0xa0, 0x9f, 0xb8, 0xbc, 0x8b, 0x02, 0x9e, 0xb8, 0xc0, 0x41, 0xc0, 0xf3,
	0xba, 0xb9, 0xbf, 0x8f, 0x1d, 0x35, 0x7b, 0x4d, 0xf5, 0x55, 0x53, 0x2a, 0xed, 0xfe, 0x7e, 0xf0,
	0x42, 0xb5, 0xe7, 0xe3, 0xb4, 0x77, 0x61, 0x3d, 0xa8, 0x46, 0x17, 0x94, 0x3f, 0x55, 0x5c, 0x91,
	0x37, 0x61, 0x2d, 0x52, 0x40, 0x55, 0x5d, 0x4a, 0x51, 0x2d, 0x0f, 0x91, 0xfb, 0xda, 0x41, 0xfd,
	0xa8, 0x17, 0x68, 0x57, 0x7a, 0xf7, 0xf3, 0x14, 0x6c, 0x84, 0x4f, 0xf0, 0x85, 0xfd, 0xb0, 0xe1,
	0xf1, 0x17, 0x92, 0xc9, 0xa5, 0xaf, 0xb7, 0xdb, 0x5a, 0xa7, 0xd5, 0x90, 0x52, 0xd4, 0xde, 0xe2,
	0x73, 0x6a, 0xf5, 0x5f, 0x68, 0x27, 0xad, 0x7d, 0x29, 0x4d, 0x95, 0x50, 0x54, 0xd8, 0xea, 0x37,
	0x3b, 0x5a, 0xbb, 0xd5, 0xeb, 0x4b, 0x99, 0xdd, 0x1f, 0x46, 0x7c, 0x08, 0x0b, 0x91, 0x71, 0x34,
	0xa1, 0xc9, 0xf5, 0x3f, 0xed, 0x36, 0xd9, 0x5a, 0xac, 0xc8, 0xeb, 0x50, 0x8b, 0x90, 0x5b, 0x1d,
	0x29, 0x15, 0x1e, 0x39, 0x12, 0xb9, 0x6e, 0xa5, 0x77, 0x55, 0x90, 0x9b, 0x11, 0x54, 0x8e, 0xdf,
	0x30, 0xbc, 0xdf, 0x3c, 0x3a, 0xe9, 0x68, 0x27, 0xbd, 0xa6, 0xaa, 0x75, 0xd5, 0xd6, 0xab, 0x56,
	0xbb, 0x79, 0xd8, 0xd4, 0x4e, 0x8e, 0x5e, 0x1e, 0x1d, 0xbf, 0x3e, 0x92, 0x56, 0xd0, 0x89, 0x88,
	0x18, 0x5e, 0xbe, 0x96, 0x52, 0xcf, 0xfe, 0xb0, 0x18, 0x98, 0x4d, 0x27, 0xf0, 0xf3, 0xf2, 0x0f,
	0xa0, 0x14, 0x8a, 0x73, 0x65, 0x65, 0xde, 0x21, 0xdf, 0x2c, 0xd7, 0xb9, 0xf7, 0xf0, 0x5a, 0x1e,
	0xd7, 0x56, 0x56, 0xe4, 0x11, 0x48, 0xf1, 0xfb, 0xe4, 0xb2, 0x30, 0x84, 0x13, 0x3c, 0x6d, 0xb8,
	0xb7, 0xb3, 0x1c, 0x23, 0x76, 0xf4, 0x7b, 0x50, 0x89, 0x5c, 0x6e, 0x94, 0x85, 0x40, 0x44, 0xfc,
	0x72, 0xed, 0xbd, 0x8f, 0x97, 0xe0, 0xc2, 0xf6, 0x7f, 0x04, 0x6b, 0x89, 0x6b, 0x27, 0xb2, 0x70,
	0x80, 0xa2, 0x2b, 0x4d, 0xf7, 0xbe, 0xb9, 0x24, 0xa7, 0xdf, 0x57, 0xe2, 0x76, 0x99, 0xb8, 0x2f,
	0xd1, 0x6d, 0x44, 0x71, 0x5f, 0xc2, 0xeb, 0x6a, 0x6c, 0x81, 0xe2, 0x07, 0xc8, 0xf2, 0x82, 0x63,
	0xb5, 0x25, 0x16, 0x48, 0x74, 0x1e, 0xad, 0xac, 0xc8, 0x3a, 0x54, 0xa3, 0xe9, 0x94, 0x3c, 0x2f,
	0x71, 0x8a, 0x75, 0xf2, 0x78, 0x19, 0x36, 0x7f, 0x2e, 0x71, 0x10, 0x57, 0x3c, 0x17, 0x01, 0x38,
	0x2c, 0x9e, 0x8b, 0x10, 0x13, 0x5e, 0x91, 0x5d, 0xd8, 0x10, 0x41, 0x1e, 0xf2, 0xb7, 0x84, 0x57,
	0x5f, 0xc4, 0xb8, 0xca, 0xbd, 0x6f, 0x2f, 0xcf, 0xec, 0xcf, 0x2e, 0x0e, 0x2c, 0x8a, 0x67, 0x27,
	0x80, 0x37, 0xc5, 0xb3, 0x13, 0xe1, 0x94, 0xac, 0xa3, 0x38, 0xe8, 0x21, 0xee, 0x48, 0x00, 0xa5,
	0x88, 0x3b, 0x12, 0x62, 0x28, 0x2b, 0x7b, 0xbf, 0xf9, 0x3b, 0xbf, 0x3e, 0xb2, 0xc6, 0xba, 0x39,
	0x7a, 0xf2, 0x1b, 0xcf, 0x3c, 0xef, 0xc9, 0xc0, 0x9a, 0x3c, 0xc5, 0x7f, 0xb2, 0x1a, 0x58, 0xe3,
	0xa7, 0xfc, 0xbe, 0x8d, 0x2b, 0xf8, 0xbb, 0xab, 0xd3, 0x1c, 0x72, 0xfd, 0xda, 0xff, 0x07, 0x00,
	0x00, 0xff, 0xff, 0xb6, 0x1f, 0x90, 0xd7, 0x2d, 0x4b, 0x00, 0x00,
}
