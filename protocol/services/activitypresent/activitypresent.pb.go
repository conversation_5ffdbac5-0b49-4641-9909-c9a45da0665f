// Code generated by protoc-gen-gogo.
// source: services/activitypresent/activitypresent.proto
// DO NOT EDIT!

/*
	Package activitypresent is a generated protocol buffer package.

	namespace

	It is generated from these files:
		services/activitypresent/activitypresent.proto

	It has these top-level messages:
		StActivityPresentBriefConfig
		StActivityPresent
		StPresentCount
		StIntKeyValue
		StStrKeyValue
		StActivityListDetail
		GetPresentActivityInfoReq
		GetPresentActivityInfoResp
		StPresentActUserFansInfo
		StPresentActUserExtentInfo
		StPresentActUserRankInfo
		GetPresentActivityRankingListReq
		GetPresentActivityRankingListResp
		GetPresentActivityUserInfoReq
		GetPresentActivityUserInfoResp
		GetPresentActivityRankingByUidReq
		GetPresentActivityRankingByUidResp
		RecordActivitySendPresentReq
		RecalculateUserActivityInfoReq
		RecalculateActivityInfoReq
		NotifyActivityPhaseChangeReq
		LoveLetterInfo
		LoveLetterInfoList
		ModifyLoveLetterReq
		ModifyLoveLetterResp
		GetLoveLetterListReq
		GetLoveLetterListResp
		ActCommonAward
		ConsumeRecord
		ActBattleSubMsg
		ActBattleProgress
		GetBattleProgressListReq
		GetBattleProgressListResp
		ModifyUserActivityInfoReq
		ModifyUserActivityInfoResp
		DoTestReq
		DoTestResp
		IdolInfo
		IdolInfoList
		GetIdolListReq
		GetIdolListResp
		GetPresentActivityExtentInfoReq
		GetPresentActivityExtentInfoResp
		GetPresentActivityUserExtentInfoReq
		GetPresentActivityUserExtentInfoResp
		ActDailyRanking
		ActHistoryRanking
		LanternFestivalUserData
		WorldEarthDayUserData
		MayDayUserData
		UserDailyInfoData
		GetRankBlackUidListReq
		GetRankBlackUidListResp
*/
package activitypresent

import "github.com/gogo/protobuf/proto"
import "fmt"
import "math"
import _ "gitlab.ttyuyin.com/golang/svrkit/protocol/common/tlvpickle"

import io1 "io"
import fmt2 "fmt"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion2 // please upgrade the proto package

type PresentRankingListType int32

const (
	PresentRankingListType_List_Unknown PresentRankingListType = 0
	// 通用
	PresentRankingListType_List_Recv PresentRankingListType = 1
	PresentRankingListType_List_Send PresentRankingListType = 2
	// 开学季
	PresentRankingListType_List_Term_Begin_Min          PresentRankingListType = 3
	PresentRankingListType_List_XueJing                 PresentRankingListType = 3
	PresentRankingListType_List_XueBa                   PresentRankingListType = 4
	PresentRankingListType_List_XueZha                  PresentRankingListType = 5
	PresentRankingListType_List_XueCan                  PresentRankingListType = 6
	PresentRankingListType_List_Term_Begin_Max          PresentRankingListType = 6
	PresentRankingListType_List_National_Day_2018_Min   PresentRankingListType = 7
	PresentRankingListType_List_Medal_King              PresentRankingListType = 7
	PresentRankingListType_List_Yu_Di                   PresentRankingListType = 8
	PresentRankingListType_List_Yue_Lao                 PresentRankingListType = 9
	PresentRankingListType_List_Chang_E                 PresentRankingListType = 10
	PresentRankingListType_List_Hou_Yi                  PresentRankingListType = 11
	PresentRankingListType_List_Wu_Gang                 PresentRankingListType = 12
	PresentRankingListType_List_National_Day_2018_Max   PresentRankingListType = 12
	PresentRankingListType_List_Halloween_Min           PresentRankingListType = 13
	PresentRankingListType_List_Vampire                 PresentRankingListType = 13
	PresentRankingListType_List_Mummy                   PresentRankingListType = 14
	PresentRankingListType_List_Frankenstein            PresentRankingListType = 15
	PresentRankingListType_List_Skeleton                PresentRankingListType = 16
	PresentRankingListType_List_Halloween_Max           PresentRankingListType = 16
	PresentRankingListType_List_Singles_Day_Min         PresentRankingListType = 17
	PresentRankingListType_List_Singles_Day_Receive_Min PresentRankingListType = 17
	// 17. 佳偶天成
	// 18. 心心相印
	// 19. 成双成对
	// 20. 坠入爱河
	PresentRankingListType_List_Singles_Day_Receive_Max PresentRankingListType = 20
	PresentRankingListType_List_Singles_Day_Send_Min    PresentRankingListType = 21
	// 21. 助攻达人
	// 22. 恋爱专家
	// 23. 脱单圣手
	// 24. 情感导师
	PresentRankingListType_List_Singles_Day_Send_Max                  PresentRankingListType = 24
	PresentRankingListType_List_Singles_Day_Max                       PresentRankingListType = 24
	PresentRankingListType_List_Singles_Day_Send_Item                 PresentRankingListType = 25
	PresentRankingListType_List_Anniversary_Receive                   PresentRankingListType = 26
	PresentRankingListType_List_Anniversary_Send                      PresentRankingListType = 27
	PresentRankingListType_List_Anniversary_Send_TT                   PresentRankingListType = 28
	PresentRankingListType_List_Anniversary_Mic_Receive_Min           PresentRankingListType = 31
	PresentRankingListType_List_Anniversary_Mic_Receive_Max           PresentRankingListType = 35
	PresentRankingListType_List_Anniversary_Mic_Send_Min              PresentRankingListType = 38
	PresentRankingListType_List_Anniversary_Mic_Send_Max              PresentRankingListType = 42
	PresentRankingListType_List_SpringFestival_Rec_Min                PresentRankingListType = 43
	PresentRankingListType_List_SpringFestival_Rec_Max                PresentRankingListType = 46
	PresentRankingListType_List_SpringFestival_Send_Min               PresentRankingListType = 47
	PresentRankingListType_List_SpringFestival_Send_Max               PresentRankingListType = 50
	PresentRankingListType_List_SpringFestival_Rec                    PresentRankingListType = 51
	PresentRankingListType_List_SpringFestival_Send                   PresentRankingListType = 52
	PresentRankingListType_List_SpringFestival_Room                   PresentRankingListType = 53
	PresentRankingListType_List_SpringFestival_Rec_Daily              PresentRankingListType = 54
	PresentRankingListType_List_SpringFestival_Send_Daily             PresentRankingListType = 55
	PresentRankingListType_List_SpringFestival_Room_Daily             PresentRankingListType = 56
	PresentRankingListType_List_Lantern_Min                           PresentRankingListType = 60
	PresentRankingListType_List_Lantern_Send                          PresentRankingListType = 60
	PresentRankingListType_List_Lantern_Receive                       PresentRankingListType = 61
	PresentRankingListType_List_Lantern_Score_Min                     PresentRankingListType = 62
	PresentRankingListType_List_Lantern_Score_Max                     PresentRankingListType = 65
	PresentRankingListType_List_Lantern_Max                           PresentRankingListType = 65
	PresentRankingListType_List_Fools_Day_Send                        PresentRankingListType = 67
	PresentRankingListType_List_Fools_Day_Receive                     PresentRankingListType = 68
	PresentRankingListType_List_Fools_Day_Medal_Min                   PresentRankingListType = 69
	PresentRankingListType_List_Fools_Day_Medal_Max                   PresentRankingListType = 72
	PresentRankingListType_List_May_Day_Send                          PresentRankingListType = 74
	PresentRankingListType_List_May_Day_Channel                       PresentRankingListType = 75
	PresentRankingListType_List_May_Day_Medal_Min                     PresentRankingListType = 76
	PresentRankingListType_List_May_Day_Medal_Max                     PresentRankingListType = 79
	PresentRankingListType_List_Network_Valentines_Day_Send           PresentRankingListType = 80
	PresentRankingListType_List_Network_Valentines_Day_Recv           PresentRankingListType = 81
	PresentRankingListType_List_Network_Valentines_Day_Medal_Min      PresentRankingListType = 82
	PresentRankingListType_List_Network_Valentines_Day_Medal_Send_Min PresentRankingListType = 82
	PresentRankingListType_List_Network_Valentines_Day_Medal_Send_Max PresentRankingListType = 85
	PresentRankingListType_List_Network_Valentines_Day_Medal_Recv_Min PresentRankingListType = 86
	PresentRankingListType_List_Network_Valentines_Day_Medal_Recv_Max PresentRankingListType = 89
	PresentRankingListType_List_Network_Valentines_Day_Medal_Max      PresentRankingListType = 89
	PresentRankingListType_List_Magpie_Festival_Send                  PresentRankingListType = 90
	PresentRankingListType_List_Magpie_Festival_Recv                  PresentRankingListType = 91
	PresentRankingListType_List_Magpie_Festival_Medal_Min             PresentRankingListType = 92
	PresentRankingListType_List_Magpie_Festival_Medal_Send_Min        PresentRankingListType = 92
	PresentRankingListType_List_Magpie_Festival_Medal_Send_Max        PresentRankingListType = 94
	PresentRankingListType_List_Magpie_Festival_Medal_Recv_Min        PresentRankingListType = 95
	PresentRankingListType_List_Magpie_Festival_Medal_Recv_Max        PresentRankingListType = 97
	PresentRankingListType_List_Magpie_Festival_Medal_Max             PresentRankingListType = 97
	PresentRankingListType_List_Mid_Autumn_Festival_Send              PresentRankingListType = 98
	PresentRankingListType_List_Mid_Autumn_Festival_Recv              PresentRankingListType = 99
	PresentRankingListType_List_Mid_Autumn_Festival_Medal_Min         PresentRankingListType = 100
	PresentRankingListType_List_Mid_Autumn_Festival_Medal_Send_Min    PresentRankingListType = 100
	PresentRankingListType_List_Mid_Autumn_Festival_Medal_Send_Max    PresentRankingListType = 103
	PresentRankingListType_List_Mid_Autumn_Festival_Medal_Recv_Min    PresentRankingListType = 104
	PresentRankingListType_List_Mid_Autumn_Festival_Medal_Recv_Max    PresentRankingListType = 107
	PresentRankingListType_List_Mid_Autumn_Festival_Medal_Max         PresentRankingListType = 107
	PresentRankingListType_List_National_Day_2018_Send                PresentRankingListType = 108
	PresentRankingListType_List_National_Day_2018_Recv                PresentRankingListType = 109
	PresentRankingListType_List_National_Day_2018_Medal_Min           PresentRankingListType = 110
	PresentRankingListType_List_National_Day_2018_Medal_Send_Min      PresentRankingListType = 110
	PresentRankingListType_List_National_Day_2018_Medal_Send_Max      PresentRankingListType = 113
	PresentRankingListType_List_National_Day_2018_Medal_Recv_Min      PresentRankingListType = 114
	PresentRankingListType_List_National_Day_2018_Medal_Recv_Max      PresentRankingListType = 117
	PresentRankingListType_List_National_Day_2018_Medal_Max           PresentRankingListType = 117
	PresentRankingListType_List_Year_Award_Game_SignUp_2018_Send      PresentRankingListType = 118
	PresentRankingListType_List_Year_Award_Game_KnockOut_2018_Send    PresentRankingListType = 119
	PresentRankingListType_List_Year_Award_Game_KnockOut_2018_Recv    PresentRankingListType = 120
	PresentRankingListType_List_New_Years_Day_2019_Send               PresentRankingListType = 121
	PresentRankingListType_List_New_Years_Day_2019_Recv               PresentRankingListType = 122
	PresentRankingListType_List_New_Years_Day_2019_Medal_Min          PresentRankingListType = 123
	PresentRankingListType_List_New_Years_Day_2019_Medal_Send_Min     PresentRankingListType = 123
	PresentRankingListType_List_New_Years_Day_2019_Medal_Send_Max     PresentRankingListType = 127
	PresentRankingListType_List_New_Years_Day_2019_Medal_Recv_Min     PresentRankingListType = 128
	PresentRankingListType_List_New_Years_Day_2019_Medal_Recv_Max     PresentRankingListType = 132
	PresentRankingListType_List_New_Years_Day_2019_Medal_Max          PresentRankingListType = 132
	PresentRankingListType_List_Catching_Nian_2019_Medal_Min          PresentRankingListType = 135
	PresentRankingListType_List_Catching_Nian_2019_Medal_Send_Min     PresentRankingListType = 135
	PresentRankingListType_List_Catching_Nian_2019_Medal_Send_Max     PresentRankingListType = 139
	PresentRankingListType_List_Catching_Nian_2019_Medal_Recv_Min     PresentRankingListType = 140
	PresentRankingListType_List_Catching_Nian_2019_Medal_Recv_Max     PresentRankingListType = 144
	PresentRankingListType_List_Catching_Nian_2019_Medal_Max          PresentRankingListType = 144
	PresentRankingListType_List_Valentineday_Day_2019_Min             PresentRankingListType = 152
	PresentRankingListType_List_Valentineday_Day_2019_Send_Min        PresentRankingListType = 152
	PresentRankingListType_List_Valentineday_Day_2019_Send_Max        PresentRankingListType = 156
	PresentRankingListType_List_Valentineday_Day_2019_Recv_Min        PresentRankingListType = 157
	PresentRankingListType_List_Valentineday_Day_2019_Recv_Max        PresentRankingListType = 161
	PresentRankingListType_List_Valentineday_Day_2019_Max             PresentRankingListType = 161
	PresentRankingListType_List_Valentineday_Day_2019_Love_Letter     PresentRankingListType = 162
	PresentRankingListType_List_Valentineday_Day_2019_Idol_List       PresentRankingListType = 163
	PresentRankingListType_List_Valentineday_Day_2019_Fans_List       PresentRankingListType = 164
	PresentRankingListType_List_Valentineday_Day_2019_Send_Daily      PresentRankingListType = 165
	PresentRankingListType_List_Valentineday_Day_2019_Recv_Daily      PresentRankingListType = 166
	PresentRankingListType_List_Goddess_2019_Medal_Min                PresentRankingListType = 170
	PresentRankingListType_List_Goddess_2019_Medal_Send_Min           PresentRankingListType = 170
	PresentRankingListType_List_Goddess_2019_Medal_Send_Max           PresentRankingListType = 174
	PresentRankingListType_List_Goddess_2019_Medal_Recv_Min           PresentRankingListType = 175
	PresentRankingListType_List_Goddess_2019_Medal_Recv_Max           PresentRankingListType = 179
	PresentRankingListType_List_Goddess_2019_Medal_Max                PresentRankingListType = 179
)

var PresentRankingListType_name = map[int32]string{
	0: "List_Unknown",
	1: "List_Recv",
	2: "List_Send",
	3: "List_Term_Begin_Min",
	// Duplicate value: 3: "List_XueJing",
	4: "List_XueBa",
	5: "List_XueZha",
	6: "List_XueCan",
	// Duplicate value: 6: "List_Term_Begin_Max",
	7: "List_National_Day_2018_Min",
	// Duplicate value: 7: "List_Medal_King",
	8:  "List_Yu_Di",
	9:  "List_Yue_Lao",
	10: "List_Chang_E",
	11: "List_Hou_Yi",
	12: "List_Wu_Gang",
	// Duplicate value: 12: "List_National_Day_2018_Max",
	13: "List_Halloween_Min",
	// Duplicate value: 13: "List_Vampire",
	14: "List_Mummy",
	15: "List_Frankenstein",
	16: "List_Skeleton",
	// Duplicate value: 16: "List_Halloween_Max",
	17: "List_Singles_Day_Min",
	// Duplicate value: 17: "List_Singles_Day_Receive_Min",
	20: "List_Singles_Day_Receive_Max",
	21: "List_Singles_Day_Send_Min",
	24: "List_Singles_Day_Send_Max",
	// Duplicate value: 24: "List_Singles_Day_Max",
	25: "List_Singles_Day_Send_Item",
	26: "List_Anniversary_Receive",
	27: "List_Anniversary_Send",
	28: "List_Anniversary_Send_TT",
	31: "List_Anniversary_Mic_Receive_Min",
	35: "List_Anniversary_Mic_Receive_Max",
	38: "List_Anniversary_Mic_Send_Min",
	42: "List_Anniversary_Mic_Send_Max",
	43: "List_SpringFestival_Rec_Min",
	46: "List_SpringFestival_Rec_Max",
	47: "List_SpringFestival_Send_Min",
	50: "List_SpringFestival_Send_Max",
	51: "List_SpringFestival_Rec",
	52: "List_SpringFestival_Send",
	53: "List_SpringFestival_Room",
	54: "List_SpringFestival_Rec_Daily",
	55: "List_SpringFestival_Send_Daily",
	56: "List_SpringFestival_Room_Daily",
	60: "List_Lantern_Min",
	// Duplicate value: 60: "List_Lantern_Send",
	61: "List_Lantern_Receive",
	62: "List_Lantern_Score_Min",
	65: "List_Lantern_Score_Max",
	// Duplicate value: 65: "List_Lantern_Max",
	67: "List_Fools_Day_Send",
	68: "List_Fools_Day_Receive",
	69: "List_Fools_Day_Medal_Min",
	72: "List_Fools_Day_Medal_Max",
	74: "List_May_Day_Send",
	75: "List_May_Day_Channel",
	76: "List_May_Day_Medal_Min",
	79: "List_May_Day_Medal_Max",
	80: "List_Network_Valentines_Day_Send",
	81: "List_Network_Valentines_Day_Recv",
	82: "List_Network_Valentines_Day_Medal_Min",
	// Duplicate value: 82: "List_Network_Valentines_Day_Medal_Send_Min",
	85: "List_Network_Valentines_Day_Medal_Send_Max",
	86: "List_Network_Valentines_Day_Medal_Recv_Min",
	89: "List_Network_Valentines_Day_Medal_Recv_Max",
	// Duplicate value: 89: "List_Network_Valentines_Day_Medal_Max",
	90: "List_Magpie_Festival_Send",
	91: "List_Magpie_Festival_Recv",
	92: "List_Magpie_Festival_Medal_Min",
	// Duplicate value: 92: "List_Magpie_Festival_Medal_Send_Min",
	94: "List_Magpie_Festival_Medal_Send_Max",
	95: "List_Magpie_Festival_Medal_Recv_Min",
	97: "List_Magpie_Festival_Medal_Recv_Max",
	// Duplicate value: 97: "List_Magpie_Festival_Medal_Max",
	98:  "List_Mid_Autumn_Festival_Send",
	99:  "List_Mid_Autumn_Festival_Recv",
	100: "List_Mid_Autumn_Festival_Medal_Min",
	// Duplicate value: 100: "List_Mid_Autumn_Festival_Medal_Send_Min",
	103: "List_Mid_Autumn_Festival_Medal_Send_Max",
	104: "List_Mid_Autumn_Festival_Medal_Recv_Min",
	107: "List_Mid_Autumn_Festival_Medal_Recv_Max",
	// Duplicate value: 107: "List_Mid_Autumn_Festival_Medal_Max",
	108: "List_National_Day_2018_Send",
	109: "List_National_Day_2018_Recv",
	110: "List_National_Day_2018_Medal_Min",
	// Duplicate value: 110: "List_National_Day_2018_Medal_Send_Min",
	113: "List_National_Day_2018_Medal_Send_Max",
	114: "List_National_Day_2018_Medal_Recv_Min",
	117: "List_National_Day_2018_Medal_Recv_Max",
	// Duplicate value: 117: "List_National_Day_2018_Medal_Max",
	118: "List_Year_Award_Game_SignUp_2018_Send",
	119: "List_Year_Award_Game_KnockOut_2018_Send",
	120: "List_Year_Award_Game_KnockOut_2018_Recv",
	121: "List_New_Years_Day_2019_Send",
	122: "List_New_Years_Day_2019_Recv",
	123: "List_New_Years_Day_2019_Medal_Min",
	// Duplicate value: 123: "List_New_Years_Day_2019_Medal_Send_Min",
	127: "List_New_Years_Day_2019_Medal_Send_Max",
	128: "List_New_Years_Day_2019_Medal_Recv_Min",
	132: "List_New_Years_Day_2019_Medal_Recv_Max",
	// Duplicate value: 132: "List_New_Years_Day_2019_Medal_Max",
	135: "List_Catching_Nian_2019_Medal_Min",
	// Duplicate value: 135: "List_Catching_Nian_2019_Medal_Send_Min",
	139: "List_Catching_Nian_2019_Medal_Send_Max",
	140: "List_Catching_Nian_2019_Medal_Recv_Min",
	144: "List_Catching_Nian_2019_Medal_Recv_Max",
	// Duplicate value: 144: "List_Catching_Nian_2019_Medal_Max",
	152: "List_Valentineday_Day_2019_Min",
	// Duplicate value: 152: "List_Valentineday_Day_2019_Send_Min",
	156: "List_Valentineday_Day_2019_Send_Max",
	157: "List_Valentineday_Day_2019_Recv_Min",
	161: "List_Valentineday_Day_2019_Recv_Max",
	// Duplicate value: 161: "List_Valentineday_Day_2019_Max",
	162: "List_Valentineday_Day_2019_Love_Letter",
	163: "List_Valentineday_Day_2019_Idol_List",
	164: "List_Valentineday_Day_2019_Fans_List",
	165: "List_Valentineday_Day_2019_Send_Daily",
	166: "List_Valentineday_Day_2019_Recv_Daily",
	170: "List_Goddess_2019_Medal_Min",
	// Duplicate value: 170: "List_Goddess_2019_Medal_Send_Min",
	174: "List_Goddess_2019_Medal_Send_Max",
	175: "List_Goddess_2019_Medal_Recv_Min",
	179: "List_Goddess_2019_Medal_Recv_Max",
	// Duplicate value: 179: "List_Goddess_2019_Medal_Max",
}
var PresentRankingListType_value = map[string]int32{
	"List_Unknown":                               0,
	"List_Recv":                                  1,
	"List_Send":                                  2,
	"List_Term_Begin_Min":                        3,
	"List_XueJing":                               3,
	"List_XueBa":                                 4,
	"List_XueZha":                                5,
	"List_XueCan":                                6,
	"List_Term_Begin_Max":                        6,
	"List_National_Day_2018_Min":                 7,
	"List_Medal_King":                            7,
	"List_Yu_Di":                                 8,
	"List_Yue_Lao":                               9,
	"List_Chang_E":                               10,
	"List_Hou_Yi":                                11,
	"List_Wu_Gang":                               12,
	"List_National_Day_2018_Max":                 12,
	"List_Halloween_Min":                         13,
	"List_Vampire":                               13,
	"List_Mummy":                                 14,
	"List_Frankenstein":                          15,
	"List_Skeleton":                              16,
	"List_Halloween_Max":                         16,
	"List_Singles_Day_Min":                       17,
	"List_Singles_Day_Receive_Min":               17,
	"List_Singles_Day_Receive_Max":               20,
	"List_Singles_Day_Send_Min":                  21,
	"List_Singles_Day_Send_Max":                  24,
	"List_Singles_Day_Max":                       24,
	"List_Singles_Day_Send_Item":                 25,
	"List_Anniversary_Receive":                   26,
	"List_Anniversary_Send":                      27,
	"List_Anniversary_Send_TT":                   28,
	"List_Anniversary_Mic_Receive_Min":           31,
	"List_Anniversary_Mic_Receive_Max":           35,
	"List_Anniversary_Mic_Send_Min":              38,
	"List_Anniversary_Mic_Send_Max":              42,
	"List_SpringFestival_Rec_Min":                43,
	"List_SpringFestival_Rec_Max":                46,
	"List_SpringFestival_Send_Min":               47,
	"List_SpringFestival_Send_Max":               50,
	"List_SpringFestival_Rec":                    51,
	"List_SpringFestival_Send":                   52,
	"List_SpringFestival_Room":                   53,
	"List_SpringFestival_Rec_Daily":              54,
	"List_SpringFestival_Send_Daily":             55,
	"List_SpringFestival_Room_Daily":             56,
	"List_Lantern_Min":                           60,
	"List_Lantern_Send":                          60,
	"List_Lantern_Receive":                       61,
	"List_Lantern_Score_Min":                     62,
	"List_Lantern_Score_Max":                     65,
	"List_Lantern_Max":                           65,
	"List_Fools_Day_Send":                        67,
	"List_Fools_Day_Receive":                     68,
	"List_Fools_Day_Medal_Min":                   69,
	"List_Fools_Day_Medal_Max":                   72,
	"List_May_Day_Send":                          74,
	"List_May_Day_Channel":                       75,
	"List_May_Day_Medal_Min":                     76,
	"List_May_Day_Medal_Max":                     79,
	"List_Network_Valentines_Day_Send":           80,
	"List_Network_Valentines_Day_Recv":           81,
	"List_Network_Valentines_Day_Medal_Min":      82,
	"List_Network_Valentines_Day_Medal_Send_Min": 82,
	"List_Network_Valentines_Day_Medal_Send_Max": 85,
	"List_Network_Valentines_Day_Medal_Recv_Min": 86,
	"List_Network_Valentines_Day_Medal_Recv_Max": 89,
	"List_Network_Valentines_Day_Medal_Max":      89,
	"List_Magpie_Festival_Send":                  90,
	"List_Magpie_Festival_Recv":                  91,
	"List_Magpie_Festival_Medal_Min":             92,
	"List_Magpie_Festival_Medal_Send_Min":        92,
	"List_Magpie_Festival_Medal_Send_Max":        94,
	"List_Magpie_Festival_Medal_Recv_Min":        95,
	"List_Magpie_Festival_Medal_Recv_Max":        97,
	"List_Magpie_Festival_Medal_Max":             97,
	"List_Mid_Autumn_Festival_Send":              98,
	"List_Mid_Autumn_Festival_Recv":              99,
	"List_Mid_Autumn_Festival_Medal_Min":         100,
	"List_Mid_Autumn_Festival_Medal_Send_Min":    100,
	"List_Mid_Autumn_Festival_Medal_Send_Max":    103,
	"List_Mid_Autumn_Festival_Medal_Recv_Min":    104,
	"List_Mid_Autumn_Festival_Medal_Recv_Max":    107,
	"List_Mid_Autumn_Festival_Medal_Max":         107,
	"List_National_Day_2018_Send":                108,
	"List_National_Day_2018_Recv":                109,
	"List_National_Day_2018_Medal_Min":           110,
	"List_National_Day_2018_Medal_Send_Min":      110,
	"List_National_Day_2018_Medal_Send_Max":      113,
	"List_National_Day_2018_Medal_Recv_Min":      114,
	"List_National_Day_2018_Medal_Recv_Max":      117,
	"List_National_Day_2018_Medal_Max":           117,
	"List_Year_Award_Game_SignUp_2018_Send":      118,
	"List_Year_Award_Game_KnockOut_2018_Send":    119,
	"List_Year_Award_Game_KnockOut_2018_Recv":    120,
	"List_New_Years_Day_2019_Send":               121,
	"List_New_Years_Day_2019_Recv":               122,
	"List_New_Years_Day_2019_Medal_Min":          123,
	"List_New_Years_Day_2019_Medal_Send_Min":     123,
	"List_New_Years_Day_2019_Medal_Send_Max":     127,
	"List_New_Years_Day_2019_Medal_Recv_Min":     128,
	"List_New_Years_Day_2019_Medal_Recv_Max":     132,
	"List_New_Years_Day_2019_Medal_Max":          132,
	"List_Catching_Nian_2019_Medal_Min":          135,
	"List_Catching_Nian_2019_Medal_Send_Min":     135,
	"List_Catching_Nian_2019_Medal_Send_Max":     139,
	"List_Catching_Nian_2019_Medal_Recv_Min":     140,
	"List_Catching_Nian_2019_Medal_Recv_Max":     144,
	"List_Catching_Nian_2019_Medal_Max":          144,
	"List_Valentineday_Day_2019_Min":             152,
	"List_Valentineday_Day_2019_Send_Min":        152,
	"List_Valentineday_Day_2019_Send_Max":        156,
	"List_Valentineday_Day_2019_Recv_Min":        157,
	"List_Valentineday_Day_2019_Recv_Max":        161,
	"List_Valentineday_Day_2019_Max":             161,
	"List_Valentineday_Day_2019_Love_Letter":     162,
	"List_Valentineday_Day_2019_Idol_List":       163,
	"List_Valentineday_Day_2019_Fans_List":       164,
	"List_Valentineday_Day_2019_Send_Daily":      165,
	"List_Valentineday_Day_2019_Recv_Daily":      166,
	"List_Goddess_2019_Medal_Min":                170,
	"List_Goddess_2019_Medal_Send_Min":           170,
	"List_Goddess_2019_Medal_Send_Max":           174,
	"List_Goddess_2019_Medal_Recv_Min":           175,
	"List_Goddess_2019_Medal_Recv_Max":           179,
	"List_Goddess_2019_Medal_Max":                179,
}

func (x PresentRankingListType) String() string {
	return proto.EnumName(PresentRankingListType_name, int32(x))
}
func (PresentRankingListType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorActivitypresent, []int{0}
}

type PresentActivityId int32

const (
	PresentActivityId_PresentActivityId_Unknown     PresentActivityId = 0
	PresentActivityId_Tanabata_Festival             PresentActivityId = 1
	PresentActivityId_Honors_Day                    PresentActivityId = 2
	PresentActivityId_National_Day                  PresentActivityId = 3
	PresentActivityId_Halloween                     PresentActivityId = 4
	PresentActivityId_Singles_Day                   PresentActivityId = 5
	PresentActivityId_Anniversary                   PresentActivityId = 6
	PresentActivityId_Spring_Festival               PresentActivityId = 7
	PresentActivityId_Lantern_Festival              PresentActivityId = 8
	PresentActivityId_Fools_Day                     PresentActivityId = 9
	PresentActivityId_World_Earth_Day               PresentActivityId = 10
	PresentActivityId_May_Day                       PresentActivityId = 11
	PresentActivityId_Network_Valentines_Day        PresentActivityId = 12
	PresentActivityId_Childrens_Day                 PresentActivityId = 13
	PresentActivityId_Dragon_Boat_Festival          PresentActivityId = 14
	PresentActivityId_TT_Anniversary                PresentActivityId = 15
	PresentActivityId_Magpie_Festival_2018          PresentActivityId = 16
	PresentActivityId_Medal_Collecting              PresentActivityId = 17
	PresentActivityId_Mid_Autumn_Festival           PresentActivityId = 18
	PresentActivityId_National_Day_2018             PresentActivityId = 19
	PresentActivityId_Year_Award_Game_SignUp_2018   PresentActivityId = 20
	PresentActivityId_Year_Award_Game_KnockOut_2018 PresentActivityId = 21
	PresentActivityId_New_Years_Day_2019            PresentActivityId = 22
	PresentActivityId_Catching_Nian_2019            PresentActivityId = 23
	PresentActivityId_Valentine_Day_2019            PresentActivityId = 24
	PresentActivityId_Goddess_2019                  PresentActivityId = 25
	PresentActivityId_White_Valentines_Day_2019     PresentActivityId = 26
)

var PresentActivityId_name = map[int32]string{
	0:  "PresentActivityId_Unknown",
	1:  "Tanabata_Festival",
	2:  "Honors_Day",
	3:  "National_Day",
	4:  "Halloween",
	5:  "Singles_Day",
	6:  "Anniversary",
	7:  "Spring_Festival",
	8:  "Lantern_Festival",
	9:  "Fools_Day",
	10: "World_Earth_Day",
	11: "May_Day",
	12: "Network_Valentines_Day",
	13: "Childrens_Day",
	14: "Dragon_Boat_Festival",
	15: "TT_Anniversary",
	16: "Magpie_Festival_2018",
	17: "Medal_Collecting",
	18: "Mid_Autumn_Festival",
	19: "National_Day_2018",
	20: "Year_Award_Game_SignUp_2018",
	21: "Year_Award_Game_KnockOut_2018",
	22: "New_Years_Day_2019",
	23: "Catching_Nian_2019",
	24: "Valentine_Day_2019",
	25: "Goddess_2019",
	26: "White_Valentines_Day_2019",
}
var PresentActivityId_value = map[string]int32{
	"PresentActivityId_Unknown":     0,
	"Tanabata_Festival":             1,
	"Honors_Day":                    2,
	"National_Day":                  3,
	"Halloween":                     4,
	"Singles_Day":                   5,
	"Anniversary":                   6,
	"Spring_Festival":               7,
	"Lantern_Festival":              8,
	"Fools_Day":                     9,
	"World_Earth_Day":               10,
	"May_Day":                       11,
	"Network_Valentines_Day":        12,
	"Childrens_Day":                 13,
	"Dragon_Boat_Festival":          14,
	"TT_Anniversary":                15,
	"Magpie_Festival_2018":          16,
	"Medal_Collecting":              17,
	"Mid_Autumn_Festival":           18,
	"National_Day_2018":             19,
	"Year_Award_Game_SignUp_2018":   20,
	"Year_Award_Game_KnockOut_2018": 21,
	"New_Years_Day_2019":            22,
	"Catching_Nian_2019":            23,
	"Valentine_Day_2019":            24,
	"Goddess_2019":                  25,
	"White_Valentines_Day_2019":     26,
}

func (x PresentActivityId) String() string {
	return proto.EnumName(PresentActivityId_name, int32(x))
}
func (PresentActivityId) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorActivitypresent, []int{1}
}

type PresentFromType int32

const (
	PresentFromType_Present_TT           PresentFromType = 0
	PresentFromType_Present_Happy_Center PresentFromType = 2
)

var PresentFromType_name = map[int32]string{
	0: "Present_TT",
	2: "Present_Happy_Center",
}
var PresentFromType_value = map[string]int32{
	"Present_TT":           0,
	"Present_Happy_Center": 2,
}

func (x PresentFromType) String() string {
	return proto.EnumName(PresentFromType_name, int32(x))
}
func (PresentFromType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorActivitypresent, []int{2}
}

type ActivityPhaseChangeType int32

const (
	ActivityPhaseChangeType_Phase_Change_None  ActivityPhaseChangeType = 0
	ActivityPhaseChangeType_Phase_Change_Daily ActivityPhaseChangeType = 1
)

var ActivityPhaseChangeType_name = map[int32]string{
	0: "Phase_Change_None",
	1: "Phase_Change_Daily",
}
var ActivityPhaseChangeType_value = map[string]int32{
	"Phase_Change_None":  0,
	"Phase_Change_Daily": 1,
}

func (x ActivityPhaseChangeType) String() string {
	return proto.EnumName(ActivityPhaseChangeType_name, int32(x))
}
func (ActivityPhaseChangeType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorActivitypresent, []int{3}
}

type PresentActivityExtentInfoType int32

const (
	PresentActivityExtentInfoType_List_Lanternfestival_2019_IdolList PresentActivityExtentInfoType = 0
	PresentActivityExtentInfoType_List_Lanternfestival_2019_FansList PresentActivityExtentInfoType = 1
	PresentActivityExtentInfoType_List_Lanternfestival_2019_Test     PresentActivityExtentInfoType = 1024
	PresentActivityExtentInfoType_List_Lanternfestival_2019_Clear    PresentActivityExtentInfoType = 1025
)

var PresentActivityExtentInfoType_name = map[int32]string{
	0:    "List_Lanternfestival_2019_IdolList",
	1:    "List_Lanternfestival_2019_FansList",
	1024: "List_Lanternfestival_2019_Test",
	1025: "List_Lanternfestival_2019_Clear",
}
var PresentActivityExtentInfoType_value = map[string]int32{
	"List_Lanternfestival_2019_IdolList": 0,
	"List_Lanternfestival_2019_FansList": 1,
	"List_Lanternfestival_2019_Test":     1024,
	"List_Lanternfestival_2019_Clear":    1025,
}

func (x PresentActivityExtentInfoType) String() string {
	return proto.EnumName(PresentActivityExtentInfoType_name, int32(x))
}
func (PresentActivityExtentInfoType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorActivitypresent, []int{4}
}

type LoveLetterLinkType int32

const (
	LoveLetterLinkType_List_Online LoveLetterLinkType = 0
	LoveLetterLinkType_List_Gray   LoveLetterLinkType = 1
	LoveLetterLinkType_List_Test   LoveLetterLinkType = 2
)

var LoveLetterLinkType_name = map[int32]string{
	0: "List_Online",
	1: "List_Gray",
	2: "List_Test",
}
var LoveLetterLinkType_value = map[string]int32{
	"List_Online": 0,
	"List_Gray":   1,
	"List_Test":   2,
}

func (x LoveLetterLinkType) String() string {
	return proto.EnumName(LoveLetterLinkType_name, int32(x))
}
func (LoveLetterLinkType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorActivitypresent, []int{5}
}

type ModifyLoveLetterReq_LoveLetterType int32

const (
	ModifyLoveLetterReq_Love_Letter_ty0 ModifyLoveLetterReq_LoveLetterType = 0
	ModifyLoveLetterReq_Love_Letter_ty1 ModifyLoveLetterReq_LoveLetterType = 1
	ModifyLoveLetterReq_Love_Letter_ty2 ModifyLoveLetterReq_LoveLetterType = 2
)

var ModifyLoveLetterReq_LoveLetterType_name = map[int32]string{
	0: "Love_Letter_ty0",
	1: "Love_Letter_ty1",
	2: "Love_Letter_ty2",
}
var ModifyLoveLetterReq_LoveLetterType_value = map[string]int32{
	"Love_Letter_ty0": 0,
	"Love_Letter_ty1": 1,
	"Love_Letter_ty2": 2,
}

func (x ModifyLoveLetterReq_LoveLetterType) String() string {
	return proto.EnumName(ModifyLoveLetterReq_LoveLetterType_name, int32(x))
}
func (ModifyLoveLetterReq_LoveLetterType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorActivitypresent, []int{23, 0}
}

type ActCommonAward_ActCommonAwardType int32

const (
	ActCommonAward_Act_Award_None                 ActCommonAward_ActCommonAwardType = 0
	ActCommonAward_Act_Award_Red_Diamond          ActCommonAward_ActCommonAwardType = 1
	ActCommonAward_Act_Award_Medal                ActCommonAward_ActCommonAwardType = 2
	ActCommonAward_Act_Award_Headwear             ActCommonAward_ActCommonAwardType = 3
	ActCommonAward_Act_Award_Package              ActCommonAward_ActCommonAwardType = 4
	ActCommonAward_Act_Award_Channel_Enter_Effect ActCommonAward_ActCommonAwardType = 5
)

var ActCommonAward_ActCommonAwardType_name = map[int32]string{
	0: "Act_Award_None",
	1: "Act_Award_Red_Diamond",
	2: "Act_Award_Medal",
	3: "Act_Award_Headwear",
	4: "Act_Award_Package",
	5: "Act_Award_Channel_Enter_Effect",
}
var ActCommonAward_ActCommonAwardType_value = map[string]int32{
	"Act_Award_None":                 0,
	"Act_Award_Red_Diamond":          1,
	"Act_Award_Medal":                2,
	"Act_Award_Headwear":             3,
	"Act_Award_Package":              4,
	"Act_Award_Channel_Enter_Effect": 5,
}

func (x ActCommonAward_ActCommonAwardType) String() string {
	return proto.EnumName(ActCommonAward_ActCommonAwardType_name, int32(x))
}
func (ActCommonAward_ActCommonAwardType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorActivitypresent, []int{27, 0}
}

type ActBattleSubMsg_ActBattleSubMsgType int32

const (
	ActBattleSubMsg_Act_Battle_Sub_Msg_Normal   ActBattleSubMsg_ActBattleSubMsgType = 0
	ActBattleSubMsg_Act_Battle_Sub_Msg_Nickname ActBattleSubMsg_ActBattleSubMsgType = 1
	ActBattleSubMsg_Act_Battle_Sub_Msg_Prefix   ActBattleSubMsg_ActBattleSubMsgType = 2
)

var ActBattleSubMsg_ActBattleSubMsgType_name = map[int32]string{
	0: "Act_Battle_Sub_Msg_Normal",
	1: "Act_Battle_Sub_Msg_Nickname",
	2: "Act_Battle_Sub_Msg_Prefix",
}
var ActBattleSubMsg_ActBattleSubMsgType_value = map[string]int32{
	"Act_Battle_Sub_Msg_Normal":   0,
	"Act_Battle_Sub_Msg_Nickname": 1,
	"Act_Battle_Sub_Msg_Prefix":   2,
}

func (x ActBattleSubMsg_ActBattleSubMsgType) String() string {
	return proto.EnumName(ActBattleSubMsg_ActBattleSubMsgType_name, int32(x))
}
func (ActBattleSubMsg_ActBattleSubMsgType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorActivitypresent, []int{29, 0}
}

type ModifyUserActivityInfoReq_ModifyUserInfoType int32

const (
	ModifyUserActivityInfoReq_Modify_Type_None        ModifyUserActivityInfoReq_ModifyUserInfoType = 0
	ModifyUserActivityInfoReq_Modify_Type_Love_Letter ModifyUserActivityInfoReq_ModifyUserInfoType = 1
)

var ModifyUserActivityInfoReq_ModifyUserInfoType_name = map[int32]string{
	0: "Modify_Type_None",
	1: "Modify_Type_Love_Letter",
}
var ModifyUserActivityInfoReq_ModifyUserInfoType_value = map[string]int32{
	"Modify_Type_None":        0,
	"Modify_Type_Love_Letter": 1,
}

func (x ModifyUserActivityInfoReq_ModifyUserInfoType) String() string {
	return proto.EnumName(ModifyUserActivityInfoReq_ModifyUserInfoType_name, int32(x))
}
func (ModifyUserActivityInfoReq_ModifyUserInfoType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorActivitypresent, []int{33, 0}
}

type StActivityPresentBriefConfig struct {
	ItemId    uint32 `protobuf:"varint,1,opt,name=item_id,json=itemId,proto3" json:"item_id,omitempty"`
	Price     uint32 `protobuf:"varint,2,opt,name=price,proto3" json:"price,omitempty"`
	PriceType uint32 `protobuf:"varint,3,opt,name=price_type,json=priceType,proto3" json:"price_type,omitempty"`
	Score     uint32 `protobuf:"varint,4,opt,name=score,proto3" json:"score,omitempty"`
	Charm     uint32 `protobuf:"varint,5,opt,name=charm,proto3" json:"charm,omitempty"`
	RichValue uint32 `protobuf:"varint,6,opt,name=rich_value,json=richValue,proto3" json:"rich_value,omitempty"`
}

func (m *StActivityPresentBriefConfig) Reset()         { *m = StActivityPresentBriefConfig{} }
func (m *StActivityPresentBriefConfig) String() string { return proto.CompactTextString(m) }
func (*StActivityPresentBriefConfig) ProtoMessage()    {}
func (*StActivityPresentBriefConfig) Descriptor() ([]byte, []int) {
	return fileDescriptorActivitypresent, []int{0}
}

func (m *StActivityPresentBriefConfig) GetItemId() uint32 {
	if m != nil {
		return m.ItemId
	}
	return 0
}

func (m *StActivityPresentBriefConfig) GetPrice() uint32 {
	if m != nil {
		return m.Price
	}
	return 0
}

func (m *StActivityPresentBriefConfig) GetPriceType() uint32 {
	if m != nil {
		return m.PriceType
	}
	return 0
}

func (m *StActivityPresentBriefConfig) GetScore() uint32 {
	if m != nil {
		return m.Score
	}
	return 0
}

func (m *StActivityPresentBriefConfig) GetCharm() uint32 {
	if m != nil {
		return m.Charm
	}
	return 0
}

func (m *StActivityPresentBriefConfig) GetRichValue() uint32 {
	if m != nil {
		return m.RichValue
	}
	return 0
}

type StActivityPresent struct {
	ItemId     uint32 `protobuf:"varint,1,opt,name=item_id,json=itemId,proto3" json:"item_id,omitempty"`
	IconUrl    string `protobuf:"bytes,2,opt,name=icon_url,json=iconUrl,proto3" json:"icon_url,omitempty"`
	ActivityId uint32 `protobuf:"varint,3,opt,name=activity_id,json=activityId,proto3" json:"activity_id,omitempty"`
	Extend     string `protobuf:"bytes,4,opt,name=extend,proto3" json:"extend,omitempty"`
	ShowIndex  uint32 `protobuf:"varint,5,opt,name=show_index,json=showIndex,proto3" json:"show_index,omitempty"`
}

func (m *StActivityPresent) Reset()                    { *m = StActivityPresent{} }
func (m *StActivityPresent) String() string            { return proto.CompactTextString(m) }
func (*StActivityPresent) ProtoMessage()               {}
func (*StActivityPresent) Descriptor() ([]byte, []int) { return fileDescriptorActivitypresent, []int{1} }

func (m *StActivityPresent) GetItemId() uint32 {
	if m != nil {
		return m.ItemId
	}
	return 0
}

func (m *StActivityPresent) GetIconUrl() string {
	if m != nil {
		return m.IconUrl
	}
	return ""
}

func (m *StActivityPresent) GetActivityId() uint32 {
	if m != nil {
		return m.ActivityId
	}
	return 0
}

func (m *StActivityPresent) GetExtend() string {
	if m != nil {
		return m.Extend
	}
	return ""
}

func (m *StActivityPresent) GetShowIndex() uint32 {
	if m != nil {
		return m.ShowIndex
	}
	return 0
}

type StPresentCount struct {
	ItemId   uint32 `protobuf:"varint,1,opt,name=item_id,json=itemId,proto3" json:"item_id,omitempty"`
	Count    uint32 `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"`
	ListType uint32 `protobuf:"varint,3,opt,name=list_type,json=listType,proto3" json:"list_type,omitempty"`
}

func (m *StPresentCount) Reset()                    { *m = StPresentCount{} }
func (m *StPresentCount) String() string            { return proto.CompactTextString(m) }
func (*StPresentCount) ProtoMessage()               {}
func (*StPresentCount) Descriptor() ([]byte, []int) { return fileDescriptorActivitypresent, []int{2} }

func (m *StPresentCount) GetItemId() uint32 {
	if m != nil {
		return m.ItemId
	}
	return 0
}

func (m *StPresentCount) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *StPresentCount) GetListType() uint32 {
	if m != nil {
		return m.ListType
	}
	return 0
}

type StIntKeyValue struct {
	Key   uint32 `protobuf:"varint,1,opt,name=key,proto3" json:"key,omitempty"`
	Value uint32 `protobuf:"varint,2,opt,name=value,proto3" json:"value,omitempty"`
}

func (m *StIntKeyValue) Reset()                    { *m = StIntKeyValue{} }
func (m *StIntKeyValue) String() string            { return proto.CompactTextString(m) }
func (*StIntKeyValue) ProtoMessage()               {}
func (*StIntKeyValue) Descriptor() ([]byte, []int) { return fileDescriptorActivitypresent, []int{3} }

func (m *StIntKeyValue) GetKey() uint32 {
	if m != nil {
		return m.Key
	}
	return 0
}

func (m *StIntKeyValue) GetValue() uint32 {
	if m != nil {
		return m.Value
	}
	return 0
}

type StStrKeyValue struct {
	Key   string `protobuf:"bytes,1,opt,name=key,proto3" json:"key,omitempty"`
	Value string `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`
}

func (m *StStrKeyValue) Reset()                    { *m = StStrKeyValue{} }
func (m *StStrKeyValue) String() string            { return proto.CompactTextString(m) }
func (*StStrKeyValue) ProtoMessage()               {}
func (*StStrKeyValue) Descriptor() ([]byte, []int) { return fileDescriptorActivitypresent, []int{4} }

func (m *StStrKeyValue) GetKey() string {
	if m != nil {
		return m.Key
	}
	return ""
}

func (m *StStrKeyValue) GetValue() string {
	if m != nil {
		return m.Value
	}
	return ""
}

// 达到指定榜单需要的礼物数量
type StActivityListDetail struct {
	ListType uint32            `protobuf:"varint,1,opt,name=list_type,json=listType,proto3" json:"list_type,omitempty"`
	ItemList []*StPresentCount `protobuf:"bytes,2,rep,name=item_list,json=itemList" json:"item_list,omitempty"`
}

func (m *StActivityListDetail) Reset()         { *m = StActivityListDetail{} }
func (m *StActivityListDetail) String() string { return proto.CompactTextString(m) }
func (*StActivityListDetail) ProtoMessage()    {}
func (*StActivityListDetail) Descriptor() ([]byte, []int) {
	return fileDescriptorActivitypresent, []int{5}
}

func (m *StActivityListDetail) GetListType() uint32 {
	if m != nil {
		return m.ListType
	}
	return 0
}

func (m *StActivityListDetail) GetItemList() []*StPresentCount {
	if m != nil {
		return m.ItemList
	}
	return nil
}

// 获取礼物活动信息
type GetPresentActivityInfoReq struct {
	ActivityId uint32 `protobuf:"varint,1,opt,name=activity_id,json=activityId,proto3" json:"activity_id,omitempty"`
}

func (m *GetPresentActivityInfoReq) Reset()         { *m = GetPresentActivityInfoReq{} }
func (m *GetPresentActivityInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetPresentActivityInfoReq) ProtoMessage()    {}
func (*GetPresentActivityInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptorActivitypresent, []int{6}
}

func (m *GetPresentActivityInfoReq) GetActivityId() uint32 {
	if m != nil {
		return m.ActivityId
	}
	return 0
}

type GetPresentActivityInfoResp struct {
	Title              string                  `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`
	BannerUrl          string                  `protobuf:"bytes,2,opt,name=banner_url,json=bannerUrl,proto3" json:"banner_url,omitempty"`
	BeginTime          uint32                  `protobuf:"varint,3,opt,name=begin_time,json=beginTime,proto3" json:"begin_time,omitempty"`
	EndTime            uint32                  `protobuf:"varint,4,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	PresentList        []*StActivityPresent    `protobuf:"bytes,5,rep,name=present_list,json=presentList" json:"present_list,omitempty"`
	ActivityId         uint32                  `protobuf:"varint,6,opt,name=activity_id,json=activityId,proto3" json:"activity_id,omitempty"`
	DetailList         []*StActivityListDetail `protobuf:"bytes,7,rep,name=detail_list,json=detailList" json:"detail_list,omitempty"`
	BroadcastImgUrl    string                  `protobuf:"bytes,8,opt,name=broadcast_img_url,json=broadcastImgUrl,proto3" json:"broadcast_img_url,omitempty"`
	JumpUrl            string                  `protobuf:"bytes,9,opt,name=jump_url,json=jumpUrl,proto3" json:"jump_url,omitempty"`
	BackImgUrl         string                  `protobuf:"bytes,10,opt,name=back_img_url,json=backImgUrl,proto3" json:"back_img_url,omitempty"`
	IconImgUrl         string                  `protobuf:"bytes,11,opt,name=icon_img_url,json=iconImgUrl,proto3" json:"icon_img_url,omitempty"`
	ActivityExtentInfo []byte                  `protobuf:"bytes,12,opt,name=activity_extent_info,json=activityExtentInfo,proto3" json:"activity_extent_info,omitempty"`
}

func (m *GetPresentActivityInfoResp) Reset()         { *m = GetPresentActivityInfoResp{} }
func (m *GetPresentActivityInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetPresentActivityInfoResp) ProtoMessage()    {}
func (*GetPresentActivityInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptorActivitypresent, []int{7}
}

func (m *GetPresentActivityInfoResp) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *GetPresentActivityInfoResp) GetBannerUrl() string {
	if m != nil {
		return m.BannerUrl
	}
	return ""
}

func (m *GetPresentActivityInfoResp) GetBeginTime() uint32 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *GetPresentActivityInfoResp) GetEndTime() uint32 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *GetPresentActivityInfoResp) GetPresentList() []*StActivityPresent {
	if m != nil {
		return m.PresentList
	}
	return nil
}

func (m *GetPresentActivityInfoResp) GetActivityId() uint32 {
	if m != nil {
		return m.ActivityId
	}
	return 0
}

func (m *GetPresentActivityInfoResp) GetDetailList() []*StActivityListDetail {
	if m != nil {
		return m.DetailList
	}
	return nil
}

func (m *GetPresentActivityInfoResp) GetBroadcastImgUrl() string {
	if m != nil {
		return m.BroadcastImgUrl
	}
	return ""
}

func (m *GetPresentActivityInfoResp) GetJumpUrl() string {
	if m != nil {
		return m.JumpUrl
	}
	return ""
}

func (m *GetPresentActivityInfoResp) GetBackImgUrl() string {
	if m != nil {
		return m.BackImgUrl
	}
	return ""
}

func (m *GetPresentActivityInfoResp) GetIconImgUrl() string {
	if m != nil {
		return m.IconImgUrl
	}
	return ""
}

func (m *GetPresentActivityInfoResp) GetActivityExtentInfo() []byte {
	if m != nil {
		return m.ActivityExtentInfo
	}
	return nil
}

type StPresentActUserFansInfo struct {
	Uid       uint32 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	RankValue uint32 `protobuf:"varint,2,opt,name=rank_value,json=rankValue,proto3" json:"rank_value,omitempty"`
	Ranking   uint32 `protobuf:"varint,3,opt,name=ranking,proto3" json:"ranking,omitempty"`
}

func (m *StPresentActUserFansInfo) Reset()         { *m = StPresentActUserFansInfo{} }
func (m *StPresentActUserFansInfo) String() string { return proto.CompactTextString(m) }
func (*StPresentActUserFansInfo) ProtoMessage()    {}
func (*StPresentActUserFansInfo) Descriptor() ([]byte, []int) {
	return fileDescriptorActivitypresent, []int{8}
}

func (m *StPresentActUserFansInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *StPresentActUserFansInfo) GetRankValue() uint32 {
	if m != nil {
		return m.RankValue
	}
	return 0
}

func (m *StPresentActUserFansInfo) GetRanking() uint32 {
	if m != nil {
		return m.Ranking
	}
	return 0
}

type StPresentActUserExtentInfo struct {
	Uid        uint32 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ActivityId uint32 `protobuf:"varint,2,opt,name=activity_id,json=activityId,proto3" json:"activity_id,omitempty"`
	ListType   uint32 `protobuf:"varint,3,opt,name=list_type,json=listType,proto3" json:"list_type,omitempty"`
	Ranking    uint32 `protobuf:"varint,4,opt,name=ranking,proto3" json:"ranking,omitempty"`
	Extent     string `protobuf:"bytes,5,opt,name=extent,proto3" json:"extent,omitempty"`
}

func (m *StPresentActUserExtentInfo) Reset()         { *m = StPresentActUserExtentInfo{} }
func (m *StPresentActUserExtentInfo) String() string { return proto.CompactTextString(m) }
func (*StPresentActUserExtentInfo) ProtoMessage()    {}
func (*StPresentActUserExtentInfo) Descriptor() ([]byte, []int) {
	return fileDescriptorActivitypresent, []int{9}
}

func (m *StPresentActUserExtentInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *StPresentActUserExtentInfo) GetActivityId() uint32 {
	if m != nil {
		return m.ActivityId
	}
	return 0
}

func (m *StPresentActUserExtentInfo) GetListType() uint32 {
	if m != nil {
		return m.ListType
	}
	return 0
}

func (m *StPresentActUserExtentInfo) GetRanking() uint32 {
	if m != nil {
		return m.Ranking
	}
	return 0
}

func (m *StPresentActUserExtentInfo) GetExtent() string {
	if m != nil {
		return m.Extent
	}
	return ""
}

type StPresentActUserRankInfo struct {
	Uid        uint32                      `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ItemId     uint32                      `protobuf:"varint,2,opt,name=item_id,json=itemId,proto3" json:"item_id,omitempty"`
	ListType   uint32                      `protobuf:"varint,3,opt,name=list_type,json=listType,proto3" json:"list_type,omitempty"`
	RankValue  uint32                      `protobuf:"varint,4,opt,name=rank_value,json=rankValue,proto3" json:"rank_value,omitempty"`
	Ranking    uint32                      `protobuf:"varint,5,opt,name=ranking,proto3" json:"ranking,omitempty"`
	ActivityId uint32                      `protobuf:"varint,6,opt,name=activity_id,json=activityId,proto3" json:"activity_id,omitempty"`
	ChannelId  uint32                      `protobuf:"varint,7,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	FansList   []*StPresentActUserFansInfo `protobuf:"bytes,8,rep,name=fans_list,json=fansList" json:"fans_list,omitempty"`
}

func (m *StPresentActUserRankInfo) Reset()         { *m = StPresentActUserRankInfo{} }
func (m *StPresentActUserRankInfo) String() string { return proto.CompactTextString(m) }
func (*StPresentActUserRankInfo) ProtoMessage()    {}
func (*StPresentActUserRankInfo) Descriptor() ([]byte, []int) {
	return fileDescriptorActivitypresent, []int{10}
}

func (m *StPresentActUserRankInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *StPresentActUserRankInfo) GetItemId() uint32 {
	if m != nil {
		return m.ItemId
	}
	return 0
}

func (m *StPresentActUserRankInfo) GetListType() uint32 {
	if m != nil {
		return m.ListType
	}
	return 0
}

func (m *StPresentActUserRankInfo) GetRankValue() uint32 {
	if m != nil {
		return m.RankValue
	}
	return 0
}

func (m *StPresentActUserRankInfo) GetRanking() uint32 {
	if m != nil {
		return m.Ranking
	}
	return 0
}

func (m *StPresentActUserRankInfo) GetActivityId() uint32 {
	if m != nil {
		return m.ActivityId
	}
	return 0
}

func (m *StPresentActUserRankInfo) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *StPresentActUserRankInfo) GetFansList() []*StPresentActUserFansInfo {
	if m != nil {
		return m.FansList
	}
	return nil
}

// 获取礼物活动排行榜
type GetPresentActivityRankingListReq struct {
	ItemId     uint32 `protobuf:"varint,1,opt,name=item_id,json=itemId,proto3" json:"item_id,omitempty"`
	ListType   uint32 `protobuf:"varint,2,opt,name=list_type,json=listType,proto3" json:"list_type,omitempty"`
	Offset     uint32 `protobuf:"varint,3,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit      uint32 `protobuf:"varint,4,opt,name=limit,proto3" json:"limit,omitempty"`
	ActivityId uint32 `protobuf:"varint,5,opt,name=activity_id,json=activityId,proto3" json:"activity_id,omitempty"`
}

func (m *GetPresentActivityRankingListReq) Reset()         { *m = GetPresentActivityRankingListReq{} }
func (m *GetPresentActivityRankingListReq) String() string { return proto.CompactTextString(m) }
func (*GetPresentActivityRankingListReq) ProtoMessage()    {}
func (*GetPresentActivityRankingListReq) Descriptor() ([]byte, []int) {
	return fileDescriptorActivitypresent, []int{11}
}

func (m *GetPresentActivityRankingListReq) GetItemId() uint32 {
	if m != nil {
		return m.ItemId
	}
	return 0
}

func (m *GetPresentActivityRankingListReq) GetListType() uint32 {
	if m != nil {
		return m.ListType
	}
	return 0
}

func (m *GetPresentActivityRankingListReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetPresentActivityRankingListReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *GetPresentActivityRankingListReq) GetActivityId() uint32 {
	if m != nil {
		return m.ActivityId
	}
	return 0
}

type GetPresentActivityRankingListResp struct {
	RankList   []*StPresentActUserRankInfo `protobuf:"bytes,1,rep,name=rank_list,json=rankList" json:"rank_list,omitempty"`
	TotalCount uint32                      `protobuf:"varint,2,opt,name=total_count,json=totalCount,proto3" json:"total_count,omitempty"`
}

func (m *GetPresentActivityRankingListResp) Reset()         { *m = GetPresentActivityRankingListResp{} }
func (m *GetPresentActivityRankingListResp) String() string { return proto.CompactTextString(m) }
func (*GetPresentActivityRankingListResp) ProtoMessage()    {}
func (*GetPresentActivityRankingListResp) Descriptor() ([]byte, []int) {
	return fileDescriptorActivitypresent, []int{12}
}

func (m *GetPresentActivityRankingListResp) GetRankList() []*StPresentActUserRankInfo {
	if m != nil {
		return m.RankList
	}
	return nil
}

func (m *GetPresentActivityRankingListResp) GetTotalCount() uint32 {
	if m != nil {
		return m.TotalCount
	}
	return 0
}

// 获取用户的活动信息
type GetPresentActivityUserInfoReq struct {
	Uid        uint32 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ActivityId uint32 `protobuf:"varint,2,opt,name=activity_id,json=activityId,proto3" json:"activity_id,omitempty"`
	ListType   uint32 `protobuf:"varint,3,opt,name=list_type,json=listType,proto3" json:"list_type,omitempty"`
}

func (m *GetPresentActivityUserInfoReq) Reset()         { *m = GetPresentActivityUserInfoReq{} }
func (m *GetPresentActivityUserInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetPresentActivityUserInfoReq) ProtoMessage()    {}
func (*GetPresentActivityUserInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptorActivitypresent, []int{13}
}

func (m *GetPresentActivityUserInfoReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetPresentActivityUserInfoReq) GetActivityId() uint32 {
	if m != nil {
		return m.ActivityId
	}
	return 0
}

func (m *GetPresentActivityUserInfoReq) GetListType() uint32 {
	if m != nil {
		return m.ListType
	}
	return 0
}

type GetPresentActivityUserInfoResp struct {
	UserInfo     []byte `protobuf:"bytes,1,opt,name=user_info,json=userInfo,proto3" json:"user_info,omitempty"`
	ConsumeValue uint32 `protobuf:"varint,2,opt,name=consume_value,json=consumeValue,proto3" json:"consume_value,omitempty"`
	ExValue      uint32 `protobuf:"varint,3,opt,name=ex_value,json=exValue,proto3" json:"ex_value,omitempty"`
	ExValue2     uint32 `protobuf:"varint,4,opt,name=ex_value2,json=exValue2,proto3" json:"ex_value2,omitempty"`
	UserInfo2    []byte `protobuf:"bytes,5,opt,name=user_info2,json=userInfo2,proto3" json:"user_info2,omitempty"`
}

func (m *GetPresentActivityUserInfoResp) Reset()         { *m = GetPresentActivityUserInfoResp{} }
func (m *GetPresentActivityUserInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetPresentActivityUserInfoResp) ProtoMessage()    {}
func (*GetPresentActivityUserInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptorActivitypresent, []int{14}
}

func (m *GetPresentActivityUserInfoResp) GetUserInfo() []byte {
	if m != nil {
		return m.UserInfo
	}
	return nil
}

func (m *GetPresentActivityUserInfoResp) GetConsumeValue() uint32 {
	if m != nil {
		return m.ConsumeValue
	}
	return 0
}

func (m *GetPresentActivityUserInfoResp) GetExValue() uint32 {
	if m != nil {
		return m.ExValue
	}
	return 0
}

func (m *GetPresentActivityUserInfoResp) GetExValue2() uint32 {
	if m != nil {
		return m.ExValue2
	}
	return 0
}

func (m *GetPresentActivityUserInfoResp) GetUserInfo2() []byte {
	if m != nil {
		return m.UserInfo2
	}
	return nil
}

// 获取用户的礼物活动排名
type GetPresentActivityRankingByUidReq struct {
	Uid        uint32 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ItemId     uint32 `protobuf:"varint,2,opt,name=item_id,json=itemId,proto3" json:"item_id,omitempty"`
	ListType   uint32 `protobuf:"varint,3,opt,name=list_type,json=listType,proto3" json:"list_type,omitempty"`
	ActivityId uint32 `protobuf:"varint,4,opt,name=activity_id,json=activityId,proto3" json:"activity_id,omitempty"`
	GetUpper   bool   `protobuf:"varint,5,opt,name=get_upper,json=getUpper,proto3" json:"get_upper,omitempty"`
	GetFollow  bool   `protobuf:"varint,6,opt,name=get_follow,json=getFollow,proto3" json:"get_follow,omitempty"`
}

func (m *GetPresentActivityRankingByUidReq) Reset()         { *m = GetPresentActivityRankingByUidReq{} }
func (m *GetPresentActivityRankingByUidReq) String() string { return proto.CompactTextString(m) }
func (*GetPresentActivityRankingByUidReq) ProtoMessage()    {}
func (*GetPresentActivityRankingByUidReq) Descriptor() ([]byte, []int) {
	return fileDescriptorActivitypresent, []int{15}
}

func (m *GetPresentActivityRankingByUidReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetPresentActivityRankingByUidReq) GetItemId() uint32 {
	if m != nil {
		return m.ItemId
	}
	return 0
}

func (m *GetPresentActivityRankingByUidReq) GetListType() uint32 {
	if m != nil {
		return m.ListType
	}
	return 0
}

func (m *GetPresentActivityRankingByUidReq) GetActivityId() uint32 {
	if m != nil {
		return m.ActivityId
	}
	return 0
}

func (m *GetPresentActivityRankingByUidReq) GetGetUpper() bool {
	if m != nil {
		return m.GetUpper
	}
	return false
}

func (m *GetPresentActivityRankingByUidReq) GetGetFollow() bool {
	if m != nil {
		return m.GetFollow
	}
	return false
}

type GetPresentActivityRankingByUidResp struct {
	RankInfo       *StPresentActUserRankInfo `protobuf:"bytes,1,opt,name=rank_info,json=rankInfo" json:"rank_info,omitempty"`
	UpperRankInfo  *StPresentActUserRankInfo `protobuf:"bytes,2,opt,name=upper_rank_info,json=upperRankInfo" json:"upper_rank_info,omitempty"`
	FollowRankInfo *StPresentActUserRankInfo `protobuf:"bytes,3,opt,name=follow_rank_info,json=followRankInfo" json:"follow_rank_info,omitempty"`
}

func (m *GetPresentActivityRankingByUidResp) Reset()         { *m = GetPresentActivityRankingByUidResp{} }
func (m *GetPresentActivityRankingByUidResp) String() string { return proto.CompactTextString(m) }
func (*GetPresentActivityRankingByUidResp) ProtoMessage()    {}
func (*GetPresentActivityRankingByUidResp) Descriptor() ([]byte, []int) {
	return fileDescriptorActivitypresent, []int{16}
}

func (m *GetPresentActivityRankingByUidResp) GetRankInfo() *StPresentActUserRankInfo {
	if m != nil {
		return m.RankInfo
	}
	return nil
}

func (m *GetPresentActivityRankingByUidResp) GetUpperRankInfo() *StPresentActUserRankInfo {
	if m != nil {
		return m.UpperRankInfo
	}
	return nil
}

func (m *GetPresentActivityRankingByUidResp) GetFollowRankInfo() *StPresentActUserRankInfo {
	if m != nil {
		return m.FollowRankInfo
	}
	return nil
}

// 记录活动中送出的礼物
type RecordActivitySendPresentReq struct {
	Uid             uint32                        `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	TargetUid       uint32                        `protobuf:"varint,2,opt,name=target_uid,json=targetUid,proto3" json:"target_uid,omitempty"`
	OrderId         string                        `protobuf:"bytes,3,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	ItemConfig      *StActivityPresentBriefConfig `protobuf:"bytes,4,opt,name=item_config,json=itemConfig" json:"item_config,omitempty"`
	ItemCount       uint32                        `protobuf:"varint,5,opt,name=item_count,json=itemCount,proto3" json:"item_count,omitempty"`
	PresentFromType uint32                        `protobuf:"varint,6,opt,name=present_from_type,json=presentFromType,proto3" json:"present_from_type,omitempty"`
	SendTime        uint32                        `protobuf:"varint,7,opt,name=send_time,json=sendTime,proto3" json:"send_time,omitempty"`
	ChannelId       uint32                        `protobuf:"varint,8,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	ChannelType     uint32                        `protobuf:"varint,9,opt,name=channel_type,json=channelType,proto3" json:"channel_type,omitempty"`
}

func (m *RecordActivitySendPresentReq) Reset()         { *m = RecordActivitySendPresentReq{} }
func (m *RecordActivitySendPresentReq) String() string { return proto.CompactTextString(m) }
func (*RecordActivitySendPresentReq) ProtoMessage()    {}
func (*RecordActivitySendPresentReq) Descriptor() ([]byte, []int) {
	return fileDescriptorActivitypresent, []int{17}
}

func (m *RecordActivitySendPresentReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *RecordActivitySendPresentReq) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

func (m *RecordActivitySendPresentReq) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *RecordActivitySendPresentReq) GetItemConfig() *StActivityPresentBriefConfig {
	if m != nil {
		return m.ItemConfig
	}
	return nil
}

func (m *RecordActivitySendPresentReq) GetItemCount() uint32 {
	if m != nil {
		return m.ItemCount
	}
	return 0
}

func (m *RecordActivitySendPresentReq) GetPresentFromType() uint32 {
	if m != nil {
		return m.PresentFromType
	}
	return 0
}

func (m *RecordActivitySendPresentReq) GetSendTime() uint32 {
	if m != nil {
		return m.SendTime
	}
	return 0
}

func (m *RecordActivitySendPresentReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *RecordActivitySendPresentReq) GetChannelType() uint32 {
	if m != nil {
		return m.ChannelType
	}
	return 0
}

// 重新计算用户在活动中的信息
type RecalculateUserActivityInfoReq struct {
	Uid        uint32 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ActivityId uint32 `protobuf:"varint,2,opt,name=activity_id,json=activityId,proto3" json:"activity_id,omitempty"`
	ListType   uint32 `protobuf:"varint,3,opt,name=list_type,json=listType,proto3" json:"list_type,omitempty"`
}

func (m *RecalculateUserActivityInfoReq) Reset()         { *m = RecalculateUserActivityInfoReq{} }
func (m *RecalculateUserActivityInfoReq) String() string { return proto.CompactTextString(m) }
func (*RecalculateUserActivityInfoReq) ProtoMessage()    {}
func (*RecalculateUserActivityInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptorActivitypresent, []int{18}
}

func (m *RecalculateUserActivityInfoReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *RecalculateUserActivityInfoReq) GetActivityId() uint32 {
	if m != nil {
		return m.ActivityId
	}
	return 0
}

func (m *RecalculateUserActivityInfoReq) GetListType() uint32 {
	if m != nil {
		return m.ListType
	}
	return 0
}

// 重新计算活动的信息
type RecalculateActivityInfoReq struct {
	ActivityId uint32 `protobuf:"varint,1,opt,name=activity_id,json=activityId,proto3" json:"activity_id,omitempty"`
	ListType   uint32 `protobuf:"varint,2,opt,name=list_type,json=listType,proto3" json:"list_type,omitempty"`
}

func (m *RecalculateActivityInfoReq) Reset()         { *m = RecalculateActivityInfoReq{} }
func (m *RecalculateActivityInfoReq) String() string { return proto.CompactTextString(m) }
func (*RecalculateActivityInfoReq) ProtoMessage()    {}
func (*RecalculateActivityInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptorActivitypresent, []int{19}
}

func (m *RecalculateActivityInfoReq) GetActivityId() uint32 {
	if m != nil {
		return m.ActivityId
	}
	return 0
}

func (m *RecalculateActivityInfoReq) GetListType() uint32 {
	if m != nil {
		return m.ListType
	}
	return 0
}

// 通知活动阶段更新
type NotifyActivityPhaseChangeReq struct {
	Type uint32 `protobuf:"varint,1,opt,name=type,proto3" json:"type,omitempty"`
}

func (m *NotifyActivityPhaseChangeReq) Reset()         { *m = NotifyActivityPhaseChangeReq{} }
func (m *NotifyActivityPhaseChangeReq) String() string { return proto.CompactTextString(m) }
func (*NotifyActivityPhaseChangeReq) ProtoMessage()    {}
func (*NotifyActivityPhaseChangeReq) Descriptor() ([]byte, []int) {
	return fileDescriptorActivitypresent, []int{20}
}

func (m *NotifyActivityPhaseChangeReq) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

// 情书结构
type LoveLetterInfo struct {
	SendUid      uint32 `protobuf:"varint,1,opt,name=send_uid,json=sendUid,proto3" json:"send_uid,omitempty"`
	TargetUid    uint32 `protobuf:"varint,2,opt,name=target_uid,json=targetUid,proto3" json:"target_uid,omitempty"`
	LetterStatus uint32 `protobuf:"varint,3,opt,name=letter_status,json=letterStatus,proto3" json:"letter_status,omitempty"`
	LetterType   uint32 `protobuf:"varint,4,opt,name=letter_type,json=letterType,proto3" json:"letter_type,omitempty"`
	LetterText   string `protobuf:"bytes,5,opt,name=letter_text,json=letterText,proto3" json:"letter_text,omitempty"`
	DateInt      uint32 `protobuf:"varint,6,opt,name=date_int,json=dateInt,proto3" json:"date_int,omitempty"`
	LetterId     uint32 `protobuf:"varint,7,opt,name=letter_id,json=letterId,proto3" json:"letter_id,omitempty"`
}

func (m *LoveLetterInfo) Reset()                    { *m = LoveLetterInfo{} }
func (m *LoveLetterInfo) String() string            { return proto.CompactTextString(m) }
func (*LoveLetterInfo) ProtoMessage()               {}
func (*LoveLetterInfo) Descriptor() ([]byte, []int) { return fileDescriptorActivitypresent, []int{21} }

func (m *LoveLetterInfo) GetSendUid() uint32 {
	if m != nil {
		return m.SendUid
	}
	return 0
}

func (m *LoveLetterInfo) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

func (m *LoveLetterInfo) GetLetterStatus() uint32 {
	if m != nil {
		return m.LetterStatus
	}
	return 0
}

func (m *LoveLetterInfo) GetLetterType() uint32 {
	if m != nil {
		return m.LetterType
	}
	return 0
}

func (m *LoveLetterInfo) GetLetterText() string {
	if m != nil {
		return m.LetterText
	}
	return ""
}

func (m *LoveLetterInfo) GetDateInt() uint32 {
	if m != nil {
		return m.DateInt
	}
	return 0
}

func (m *LoveLetterInfo) GetLetterId() uint32 {
	if m != nil {
		return m.LetterId
	}
	return 0
}

// 情书墙列表
type LoveLetterInfoList struct {
	LoveLetterList []*LoveLetterInfo `protobuf:"bytes,1,rep,name=love_letter_list,json=loveLetterList" json:"love_letter_list,omitempty"`
}

func (m *LoveLetterInfoList) Reset()         { *m = LoveLetterInfoList{} }
func (m *LoveLetterInfoList) String() string { return proto.CompactTextString(m) }
func (*LoveLetterInfoList) ProtoMessage()    {}
func (*LoveLetterInfoList) Descriptor() ([]byte, []int) {
	return fileDescriptorActivitypresent, []int{22}
}

func (m *LoveLetterInfoList) GetLoveLetterList() []*LoveLetterInfo {
	if m != nil {
		return m.LoveLetterList
	}
	return nil
}

// 写情书
type ModifyLoveLetterReq struct {
	LetterId   uint32 `protobuf:"varint,1,opt,name=letter_id,json=letterId,proto3" json:"letter_id,omitempty"`
	SendUid    uint32 `protobuf:"varint,2,opt,name=send_uid,json=sendUid,proto3" json:"send_uid,omitempty"`
	TargetUid  uint32 `protobuf:"varint,3,opt,name=target_uid,json=targetUid,proto3" json:"target_uid,omitempty"`
	ActivityId uint32 `protobuf:"varint,4,opt,name=activity_id,json=activityId,proto3" json:"activity_id,omitempty"`
	DateInt    uint32 `protobuf:"varint,5,opt,name=date_int,json=dateInt,proto3" json:"date_int,omitempty"`
	LetterType uint32 `protobuf:"varint,6,opt,name=letter_type,json=letterType,proto3" json:"letter_type,omitempty"`
	LetterText string `protobuf:"bytes,7,opt,name=letter_text,json=letterText,proto3" json:"letter_text,omitempty"`
}

func (m *ModifyLoveLetterReq) Reset()         { *m = ModifyLoveLetterReq{} }
func (m *ModifyLoveLetterReq) String() string { return proto.CompactTextString(m) }
func (*ModifyLoveLetterReq) ProtoMessage()    {}
func (*ModifyLoveLetterReq) Descriptor() ([]byte, []int) {
	return fileDescriptorActivitypresent, []int{23}
}

func (m *ModifyLoveLetterReq) GetLetterId() uint32 {
	if m != nil {
		return m.LetterId
	}
	return 0
}

func (m *ModifyLoveLetterReq) GetSendUid() uint32 {
	if m != nil {
		return m.SendUid
	}
	return 0
}

func (m *ModifyLoveLetterReq) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

func (m *ModifyLoveLetterReq) GetActivityId() uint32 {
	if m != nil {
		return m.ActivityId
	}
	return 0
}

func (m *ModifyLoveLetterReq) GetDateInt() uint32 {
	if m != nil {
		return m.DateInt
	}
	return 0
}

func (m *ModifyLoveLetterReq) GetLetterType() uint32 {
	if m != nil {
		return m.LetterType
	}
	return 0
}

func (m *ModifyLoveLetterReq) GetLetterText() string {
	if m != nil {
		return m.LetterText
	}
	return ""
}

type ModifyLoveLetterResp struct {
	Code       uint32 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	NewContent string `protobuf:"bytes,2,opt,name=new_content,json=newContent,proto3" json:"new_content,omitempty"`
}

func (m *ModifyLoveLetterResp) Reset()         { *m = ModifyLoveLetterResp{} }
func (m *ModifyLoveLetterResp) String() string { return proto.CompactTextString(m) }
func (*ModifyLoveLetterResp) ProtoMessage()    {}
func (*ModifyLoveLetterResp) Descriptor() ([]byte, []int) {
	return fileDescriptorActivitypresent, []int{24}
}

func (m *ModifyLoveLetterResp) GetCode() uint32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *ModifyLoveLetterResp) GetNewContent() string {
	if m != nil {
		return m.NewContent
	}
	return ""
}

type GetLoveLetterListReq struct {
	DateInt uint32 `protobuf:"varint,1,opt,name=date_int,json=dateInt,proto3" json:"date_int,omitempty"`
}

func (m *GetLoveLetterListReq) Reset()         { *m = GetLoveLetterListReq{} }
func (m *GetLoveLetterListReq) String() string { return proto.CompactTextString(m) }
func (*GetLoveLetterListReq) ProtoMessage()    {}
func (*GetLoveLetterListReq) Descriptor() ([]byte, []int) {
	return fileDescriptorActivitypresent, []int{25}
}

func (m *GetLoveLetterListReq) GetDateInt() uint32 {
	if m != nil {
		return m.DateInt
	}
	return 0
}

type GetLoveLetterListResp struct {
	Code           uint32            `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	LoveLetterList []*LoveLetterInfo `protobuf:"bytes,2,rep,name=love_letter_list,json=loveLetterList" json:"love_letter_list,omitempty"`
}

func (m *GetLoveLetterListResp) Reset()         { *m = GetLoveLetterListResp{} }
func (m *GetLoveLetterListResp) String() string { return proto.CompactTextString(m) }
func (*GetLoveLetterListResp) ProtoMessage()    {}
func (*GetLoveLetterListResp) Descriptor() ([]byte, []int) {
	return fileDescriptorActivitypresent, []int{26}
}

func (m *GetLoveLetterListResp) GetCode() uint32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *GetLoveLetterListResp) GetLoveLetterList() []*LoveLetterInfo {
	if m != nil {
		return m.LoveLetterList
	}
	return nil
}

// 活动奖励
type ActCommonAward struct {
	AwardType  uint32 `protobuf:"varint,1,opt,name=award_type,json=awardType,proto3" json:"award_type,omitempty"`
	AwardId    uint32 `protobuf:"varint,2,opt,name=award_id,json=awardId,proto3" json:"award_id,omitempty"`
	StrAwardId string `protobuf:"bytes,3,opt,name=str_award_id,json=strAwardId,proto3" json:"str_award_id,omitempty"`
	AwardNum   uint32 `protobuf:"varint,4,opt,name=award_num,json=awardNum,proto3" json:"award_num,omitempty"`
	AwardTime  uint32 `protobuf:"varint,5,opt,name=award_time,json=awardTime,proto3" json:"award_time,omitempty"`
	AwardLmt   uint32 `protobuf:"varint,6,opt,name=award_lmt,json=awardLmt,proto3" json:"award_lmt,omitempty"`
	AwardVer   string `protobuf:"bytes,7,opt,name=award_ver,json=awardVer,proto3" json:"award_ver,omitempty"`
}

func (m *ActCommonAward) Reset()                    { *m = ActCommonAward{} }
func (m *ActCommonAward) String() string            { return proto.CompactTextString(m) }
func (*ActCommonAward) ProtoMessage()               {}
func (*ActCommonAward) Descriptor() ([]byte, []int) { return fileDescriptorActivitypresent, []int{27} }

func (m *ActCommonAward) GetAwardType() uint32 {
	if m != nil {
		return m.AwardType
	}
	return 0
}

func (m *ActCommonAward) GetAwardId() uint32 {
	if m != nil {
		return m.AwardId
	}
	return 0
}

func (m *ActCommonAward) GetStrAwardId() string {
	if m != nil {
		return m.StrAwardId
	}
	return ""
}

func (m *ActCommonAward) GetAwardNum() uint32 {
	if m != nil {
		return m.AwardNum
	}
	return 0
}

func (m *ActCommonAward) GetAwardTime() uint32 {
	if m != nil {
		return m.AwardTime
	}
	return 0
}

func (m *ActCommonAward) GetAwardLmt() uint32 {
	if m != nil {
		return m.AwardLmt
	}
	return 0
}

func (m *ActCommonAward) GetAwardVer() string {
	if m != nil {
		return m.AwardVer
	}
	return ""
}

// 消费记录
type ConsumeRecord struct {
	Uid          uint32 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ConsumeValue uint32 `protobuf:"varint,2,opt,name=consume_value,json=consumeValue,proto3" json:"consume_value,omitempty"`
	Ts           uint32 `protobuf:"varint,3,opt,name=ts,proto3" json:"ts,omitempty"`
	ActivityId   uint32 `protobuf:"varint,4,opt,name=activity_id,json=activityId,proto3" json:"activity_id,omitempty"`
	OrderId      string `protobuf:"bytes,5,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
}

func (m *ConsumeRecord) Reset()                    { *m = ConsumeRecord{} }
func (m *ConsumeRecord) String() string            { return proto.CompactTextString(m) }
func (*ConsumeRecord) ProtoMessage()               {}
func (*ConsumeRecord) Descriptor() ([]byte, []int) { return fileDescriptorActivitypresent, []int{28} }

func (m *ConsumeRecord) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ConsumeRecord) GetConsumeValue() uint32 {
	if m != nil {
		return m.ConsumeValue
	}
	return 0
}

func (m *ConsumeRecord) GetTs() uint32 {
	if m != nil {
		return m.Ts
	}
	return 0
}

func (m *ConsumeRecord) GetActivityId() uint32 {
	if m != nil {
		return m.ActivityId
	}
	return 0
}

func (m *ConsumeRecord) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

// 战况播报
type ActBattleSubMsg struct {
	Type    uint32 `protobuf:"varint,1,opt,name=type,proto3" json:"type,omitempty"`
	Content string `protobuf:"bytes,2,opt,name=content,proto3" json:"content,omitempty"`
}

func (m *ActBattleSubMsg) Reset()                    { *m = ActBattleSubMsg{} }
func (m *ActBattleSubMsg) String() string            { return proto.CompactTextString(m) }
func (*ActBattleSubMsg) ProtoMessage()               {}
func (*ActBattleSubMsg) Descriptor() ([]byte, []int) { return fileDescriptorActivitypresent, []int{29} }

func (m *ActBattleSubMsg) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *ActBattleSubMsg) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

type ActBattleProgress struct {
	TimeTs     uint32             `protobuf:"varint,1,opt,name=time_ts,json=timeTs,proto3" json:"time_ts,omitempty"`
	Msg        string             `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	SubMsgList []*ActBattleSubMsg `protobuf:"bytes,3,rep,name=sub_msg_list,json=subMsgList" json:"sub_msg_list,omitempty"`
}

func (m *ActBattleProgress) Reset()         { *m = ActBattleProgress{} }
func (m *ActBattleProgress) String() string { return proto.CompactTextString(m) }
func (*ActBattleProgress) ProtoMessage()    {}
func (*ActBattleProgress) Descriptor() ([]byte, []int) {
	return fileDescriptorActivitypresent, []int{30}
}

func (m *ActBattleProgress) GetTimeTs() uint32 {
	if m != nil {
		return m.TimeTs
	}
	return 0
}

func (m *ActBattleProgress) GetMsg() string {
	if m != nil {
		return m.Msg
	}
	return ""
}

func (m *ActBattleProgress) GetSubMsgList() []*ActBattleSubMsg {
	if m != nil {
		return m.SubMsgList
	}
	return nil
}

type GetBattleProgressListReq struct {
	ActivityId uint32 `protobuf:"varint,1,opt,name=activity_id,json=activityId,proto3" json:"activity_id,omitempty"`
	ListType   uint32 `protobuf:"varint,2,opt,name=list_type,json=listType,proto3" json:"list_type,omitempty"`
	Offset     uint32 `protobuf:"varint,3,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit      uint32 `protobuf:"varint,4,opt,name=limit,proto3" json:"limit,omitempty"`
}

func (m *GetBattleProgressListReq) Reset()         { *m = GetBattleProgressListReq{} }
func (m *GetBattleProgressListReq) String() string { return proto.CompactTextString(m) }
func (*GetBattleProgressListReq) ProtoMessage()    {}
func (*GetBattleProgressListReq) Descriptor() ([]byte, []int) {
	return fileDescriptorActivitypresent, []int{31}
}

func (m *GetBattleProgressListReq) GetActivityId() uint32 {
	if m != nil {
		return m.ActivityId
	}
	return 0
}

func (m *GetBattleProgressListReq) GetListType() uint32 {
	if m != nil {
		return m.ListType
	}
	return 0
}

func (m *GetBattleProgressListReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetBattleProgressListReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type GetBattleProgressListResp struct {
	ProgressList []*ActBattleProgress `protobuf:"bytes,1,rep,name=progress_list,json=progressList" json:"progress_list,omitempty"`
}

func (m *GetBattleProgressListResp) Reset()         { *m = GetBattleProgressListResp{} }
func (m *GetBattleProgressListResp) String() string { return proto.CompactTextString(m) }
func (*GetBattleProgressListResp) ProtoMessage()    {}
func (*GetBattleProgressListResp) Descriptor() ([]byte, []int) {
	return fileDescriptorActivitypresent, []int{32}
}

func (m *GetBattleProgressListResp) GetProgressList() []*ActBattleProgress {
	if m != nil {
		return m.ProgressList
	}
	return nil
}

// 修改用户的活动信息
type ModifyUserActivityInfoReq struct {
	Uid        uint32 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ActivityId uint32 `protobuf:"varint,2,opt,name=activity_id,json=activityId,proto3" json:"activity_id,omitempty"`
	OpType     uint32 `protobuf:"varint,3,opt,name=op_type,json=opType,proto3" json:"op_type,omitempty"`
	OpValue    uint32 `protobuf:"varint,4,opt,name=op_value,json=opValue,proto3" json:"op_value,omitempty"`
	OpData     string `protobuf:"bytes,5,opt,name=op_data,json=opData,proto3" json:"op_data,omitempty"`
}

func (m *ModifyUserActivityInfoReq) Reset()         { *m = ModifyUserActivityInfoReq{} }
func (m *ModifyUserActivityInfoReq) String() string { return proto.CompactTextString(m) }
func (*ModifyUserActivityInfoReq) ProtoMessage()    {}
func (*ModifyUserActivityInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptorActivitypresent, []int{33}
}

func (m *ModifyUserActivityInfoReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ModifyUserActivityInfoReq) GetActivityId() uint32 {
	if m != nil {
		return m.ActivityId
	}
	return 0
}

func (m *ModifyUserActivityInfoReq) GetOpType() uint32 {
	if m != nil {
		return m.OpType
	}
	return 0
}

func (m *ModifyUserActivityInfoReq) GetOpValue() uint32 {
	if m != nil {
		return m.OpValue
	}
	return 0
}

func (m *ModifyUserActivityInfoReq) GetOpData() string {
	if m != nil {
		return m.OpData
	}
	return ""
}

type ModifyUserActivityInfoResp struct {
}

func (m *ModifyUserActivityInfoResp) Reset()         { *m = ModifyUserActivityInfoResp{} }
func (m *ModifyUserActivityInfoResp) String() string { return proto.CompactTextString(m) }
func (*ModifyUserActivityInfoResp) ProtoMessage()    {}
func (*ModifyUserActivityInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptorActivitypresent, []int{34}
}

// 测试接口
type DoTestReq struct {
	Uid        uint32 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ActivityId uint32 `protobuf:"varint,2,opt,name=activity_id,json=activityId,proto3" json:"activity_id,omitempty"`
	OpType     uint32 `protobuf:"varint,3,opt,name=op_type,json=opType,proto3" json:"op_type,omitempty"`
	OpValue    uint32 `protobuf:"varint,4,opt,name=op_value,json=opValue,proto3" json:"op_value,omitempty"`
	OpData     string `protobuf:"bytes,5,opt,name=op_data,json=opData,proto3" json:"op_data,omitempty"`
}

func (m *DoTestReq) Reset()                    { *m = DoTestReq{} }
func (m *DoTestReq) String() string            { return proto.CompactTextString(m) }
func (*DoTestReq) ProtoMessage()               {}
func (*DoTestReq) Descriptor() ([]byte, []int) { return fileDescriptorActivitypresent, []int{35} }

func (m *DoTestReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *DoTestReq) GetActivityId() uint32 {
	if m != nil {
		return m.ActivityId
	}
	return 0
}

func (m *DoTestReq) GetOpType() uint32 {
	if m != nil {
		return m.OpType
	}
	return 0
}

func (m *DoTestReq) GetOpValue() uint32 {
	if m != nil {
		return m.OpValue
	}
	return 0
}

func (m *DoTestReq) GetOpData() string {
	if m != nil {
		return m.OpData
	}
	return ""
}

type DoTestResp struct {
}

func (m *DoTestResp) Reset()                    { *m = DoTestResp{} }
func (m *DoTestResp) String() string            { return proto.CompactTextString(m) }
func (*DoTestResp) ProtoMessage()               {}
func (*DoTestResp) Descriptor() ([]byte, []int) { return fileDescriptorActivitypresent, []int{36} }

type IdolInfo struct {
	Uid       uint32 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	RankValue uint32 `protobuf:"varint,2,opt,name=rank_value,json=rankValue,proto3" json:"rank_value,omitempty"`
}

func (m *IdolInfo) Reset()                    { *m = IdolInfo{} }
func (m *IdolInfo) String() string            { return proto.CompactTextString(m) }
func (*IdolInfo) ProtoMessage()               {}
func (*IdolInfo) Descriptor() ([]byte, []int) { return fileDescriptorActivitypresent, []int{37} }

func (m *IdolInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *IdolInfo) GetRankValue() uint32 {
	if m != nil {
		return m.RankValue
	}
	return 0
}

type IdolInfoList struct {
	IdolInfoList []*IdolInfo `protobuf:"bytes,1,rep,name=idol_info_list,json=idolInfoList" json:"idol_info_list,omitempty"`
}

func (m *IdolInfoList) Reset()                    { *m = IdolInfoList{} }
func (m *IdolInfoList) String() string            { return proto.CompactTextString(m) }
func (*IdolInfoList) ProtoMessage()               {}
func (*IdolInfoList) Descriptor() ([]byte, []int) { return fileDescriptorActivitypresent, []int{38} }

func (m *IdolInfoList) GetIdolInfoList() []*IdolInfo {
	if m != nil {
		return m.IdolInfoList
	}
	return nil
}

type GetIdolListReq struct {
	Offset uint32 `protobuf:"varint,1,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit  uint32 `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
}

func (m *GetIdolListReq) Reset()                    { *m = GetIdolListReq{} }
func (m *GetIdolListReq) String() string            { return proto.CompactTextString(m) }
func (*GetIdolListReq) ProtoMessage()               {}
func (*GetIdolListReq) Descriptor() ([]byte, []int) { return fileDescriptorActivitypresent, []int{39} }

func (m *GetIdolListReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetIdolListReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type GetIdolListResp struct {
	Code         uint32        `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	IdolInfoList *IdolInfoList `protobuf:"bytes,2,opt,name=idol_info_list,json=idolInfoList" json:"idol_info_list,omitempty"`
}

func (m *GetIdolListResp) Reset()                    { *m = GetIdolListResp{} }
func (m *GetIdolListResp) String() string            { return proto.CompactTextString(m) }
func (*GetIdolListResp) ProtoMessage()               {}
func (*GetIdolListResp) Descriptor() ([]byte, []int) { return fileDescriptorActivitypresent, []int{40} }

func (m *GetIdolListResp) GetCode() uint32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *GetIdolListResp) GetIdolInfoList() *IdolInfoList {
	if m != nil {
		return m.IdolInfoList
	}
	return nil
}

// ****拿活动数据或者玩家数据通用接口。通过extent_info_type区别数据类型。*****
type GetPresentActivityExtentInfoReq struct {
	ActivityId     uint32 `protobuf:"varint,1,opt,name=activity_id,json=activityId,proto3" json:"activity_id,omitempty"`
	ExtentInfoType uint32 `protobuf:"varint,2,opt,name=extent_info_type,json=extentInfoType,proto3" json:"extent_info_type,omitempty"`
}

func (m *GetPresentActivityExtentInfoReq) Reset()         { *m = GetPresentActivityExtentInfoReq{} }
func (m *GetPresentActivityExtentInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetPresentActivityExtentInfoReq) ProtoMessage()    {}
func (*GetPresentActivityExtentInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptorActivitypresent, []int{41}
}

func (m *GetPresentActivityExtentInfoReq) GetActivityId() uint32 {
	if m != nil {
		return m.ActivityId
	}
	return 0
}

func (m *GetPresentActivityExtentInfoReq) GetExtentInfoType() uint32 {
	if m != nil {
		return m.ExtentInfoType
	}
	return 0
}

type GetPresentActivityExtentInfoResp struct {
	Code               uint32 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	ActivityExtentInfo []byte `protobuf:"bytes,2,opt,name=activity_extent_info,json=activityExtentInfo,proto3" json:"activity_extent_info,omitempty"`
}

func (m *GetPresentActivityExtentInfoResp) Reset()         { *m = GetPresentActivityExtentInfoResp{} }
func (m *GetPresentActivityExtentInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetPresentActivityExtentInfoResp) ProtoMessage()    {}
func (*GetPresentActivityExtentInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptorActivitypresent, []int{42}
}

func (m *GetPresentActivityExtentInfoResp) GetCode() uint32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *GetPresentActivityExtentInfoResp) GetActivityExtentInfo() []byte {
	if m != nil {
		return m.ActivityExtentInfo
	}
	return nil
}

type GetPresentActivityUserExtentInfoReq struct {
	ActivityId     uint32 `protobuf:"varint,1,opt,name=activity_id,json=activityId,proto3" json:"activity_id,omitempty"`
	Uid            uint32 `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	ExtentInfoType uint32 `protobuf:"varint,3,opt,name=extent_info_type,json=extentInfoType,proto3" json:"extent_info_type,omitempty"`
	ExtentParam    string `protobuf:"bytes,4,opt,name=extent_param,json=extentParam,proto3" json:"extent_param,omitempty"`
}

func (m *GetPresentActivityUserExtentInfoReq) Reset()         { *m = GetPresentActivityUserExtentInfoReq{} }
func (m *GetPresentActivityUserExtentInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetPresentActivityUserExtentInfoReq) ProtoMessage()    {}
func (*GetPresentActivityUserExtentInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptorActivitypresent, []int{43}
}

func (m *GetPresentActivityUserExtentInfoReq) GetActivityId() uint32 {
	if m != nil {
		return m.ActivityId
	}
	return 0
}

func (m *GetPresentActivityUserExtentInfoReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetPresentActivityUserExtentInfoReq) GetExtentInfoType() uint32 {
	if m != nil {
		return m.ExtentInfoType
	}
	return 0
}

func (m *GetPresentActivityUserExtentInfoReq) GetExtentParam() string {
	if m != nil {
		return m.ExtentParam
	}
	return ""
}

type GetPresentActivityUserExtentInfoResp struct {
	Code           uint32 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	UserExtentInfo []byte `protobuf:"bytes,2,opt,name=user_extent_info,json=userExtentInfo,proto3" json:"user_extent_info,omitempty"`
}

func (m *GetPresentActivityUserExtentInfoResp) Reset()         { *m = GetPresentActivityUserExtentInfoResp{} }
func (m *GetPresentActivityUserExtentInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetPresentActivityUserExtentInfoResp) ProtoMessage()    {}
func (*GetPresentActivityUserExtentInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptorActivitypresent, []int{44}
}

func (m *GetPresentActivityUserExtentInfoResp) GetCode() uint32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *GetPresentActivityUserExtentInfoResp) GetUserExtentInfo() []byte {
	if m != nil {
		return m.UserExtentInfo
	}
	return nil
}

// ******************* 礼物活动内部的数据 *******************
type ActDailyRanking struct {
	RankList []*StPresentActUserRankInfo `protobuf:"bytes,1,rep,name=rank_list,json=rankList" json:"rank_list,omitempty"`
	Date     uint32                      `protobuf:"varint,2,opt,name=date,proto3" json:"date,omitempty"`
}

func (m *ActDailyRanking) Reset()                    { *m = ActDailyRanking{} }
func (m *ActDailyRanking) String() string            { return proto.CompactTextString(m) }
func (*ActDailyRanking) ProtoMessage()               {}
func (*ActDailyRanking) Descriptor() ([]byte, []int) { return fileDescriptorActivitypresent, []int{45} }

func (m *ActDailyRanking) GetRankList() []*StPresentActUserRankInfo {
	if m != nil {
		return m.RankList
	}
	return nil
}

func (m *ActDailyRanking) GetDate() uint32 {
	if m != nil {
		return m.Date
	}
	return 0
}

type ActHistoryRanking struct {
	HistoryList      []*ActDailyRanking `protobuf:"bytes,1,rep,name=history_list,json=historyList" json:"history_list,omitempty"`
	LastModifiedDate uint32             `protobuf:"varint,2,opt,name=last_modified_date,json=lastModifiedDate,proto3" json:"last_modified_date,omitempty"`
	DisableTodayList bool               `protobuf:"varint,3,opt,name=disable_today_list,json=disableTodayList,proto3" json:"disable_today_list,omitempty"`
}

func (m *ActHistoryRanking) Reset()         { *m = ActHistoryRanking{} }
func (m *ActHistoryRanking) String() string { return proto.CompactTextString(m) }
func (*ActHistoryRanking) ProtoMessage()    {}
func (*ActHistoryRanking) Descriptor() ([]byte, []int) {
	return fileDescriptorActivitypresent, []int{46}
}

func (m *ActHistoryRanking) GetHistoryList() []*ActDailyRanking {
	if m != nil {
		return m.HistoryList
	}
	return nil
}

func (m *ActHistoryRanking) GetLastModifiedDate() uint32 {
	if m != nil {
		return m.LastModifiedDate
	}
	return 0
}

func (m *ActHistoryRanking) GetDisableTodayList() bool {
	if m != nil {
		return m.DisableTodayList
	}
	return false
}

type LanternFestivalUserData struct {
	Uid            uint32            `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ActivityId     uint32            `protobuf:"varint,2,opt,name=activity_id,json=activityId,proto3" json:"activity_id,omitempty"`
	Phase          uint32            `protobuf:"varint,3,opt,name=phase,proto3" json:"phase,omitempty"`
	Score          uint32            `protobuf:"varint,4,opt,name=score,proto3" json:"score,omitempty"`
	PresentList    []*StPresentCount `protobuf:"bytes,5,rep,name=present_list,json=presentList" json:"present_list,omitempty"`
	LastModifiedTs uint32            `protobuf:"varint,6,opt,name=last_modified_ts,json=lastModifiedTs,proto3" json:"last_modified_ts,omitempty"`
}

func (m *LanternFestivalUserData) Reset()         { *m = LanternFestivalUserData{} }
func (m *LanternFestivalUserData) String() string { return proto.CompactTextString(m) }
func (*LanternFestivalUserData) ProtoMessage()    {}
func (*LanternFestivalUserData) Descriptor() ([]byte, []int) {
	return fileDescriptorActivitypresent, []int{47}
}

func (m *LanternFestivalUserData) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *LanternFestivalUserData) GetActivityId() uint32 {
	if m != nil {
		return m.ActivityId
	}
	return 0
}

func (m *LanternFestivalUserData) GetPhase() uint32 {
	if m != nil {
		return m.Phase
	}
	return 0
}

func (m *LanternFestivalUserData) GetScore() uint32 {
	if m != nil {
		return m.Score
	}
	return 0
}

func (m *LanternFestivalUserData) GetPresentList() []*StPresentCount {
	if m != nil {
		return m.PresentList
	}
	return nil
}

func (m *LanternFestivalUserData) GetLastModifiedTs() uint32 {
	if m != nil {
		return m.LastModifiedTs
	}
	return 0
}

type WorldEarthDayUserData struct {
	Uid              uint32           `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ActivityId       uint32           `protobuf:"varint,2,opt,name=activity_id,json=activityId,proto3" json:"activity_id,omitempty"`
	HistoryRankList  []*StIntKeyValue `protobuf:"bytes,3,rep,name=history_rank_list,json=historyRankList" json:"history_rank_list,omitempty"`
	LastModifiedDate uint32           `protobuf:"varint,4,opt,name=last_modified_date,json=lastModifiedDate,proto3" json:"last_modified_date,omitempty"`
	ExValue          uint32           `protobuf:"varint,5,opt,name=ex_value,json=exValue,proto3" json:"ex_value,omitempty"`
}

func (m *WorldEarthDayUserData) Reset()         { *m = WorldEarthDayUserData{} }
func (m *WorldEarthDayUserData) String() string { return proto.CompactTextString(m) }
func (*WorldEarthDayUserData) ProtoMessage()    {}
func (*WorldEarthDayUserData) Descriptor() ([]byte, []int) {
	return fileDescriptorActivitypresent, []int{48}
}

func (m *WorldEarthDayUserData) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *WorldEarthDayUserData) GetActivityId() uint32 {
	if m != nil {
		return m.ActivityId
	}
	return 0
}

func (m *WorldEarthDayUserData) GetHistoryRankList() []*StIntKeyValue {
	if m != nil {
		return m.HistoryRankList
	}
	return nil
}

func (m *WorldEarthDayUserData) GetLastModifiedDate() uint32 {
	if m != nil {
		return m.LastModifiedDate
	}
	return 0
}

func (m *WorldEarthDayUserData) GetExValue() uint32 {
	if m != nil {
		return m.ExValue
	}
	return 0
}

type MayDayUserData struct {
	Uid            uint32 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ActivityId     uint32 `protobuf:"varint,2,opt,name=activity_id,json=activityId,proto3" json:"activity_id,omitempty"`
	Phase          uint32 `protobuf:"varint,3,opt,name=phase,proto3" json:"phase,omitempty"`
	TotalScore     uint32 `protobuf:"varint,4,opt,name=total_score,json=totalScore,proto3" json:"total_score,omitempty"`
	TodayScore     uint32 `protobuf:"varint,5,opt,name=today_score,json=todayScore,proto3" json:"today_score,omitempty"`
	PresentValue   uint32 `protobuf:"varint,6,opt,name=present_value,json=presentValue,proto3" json:"present_value,omitempty"`
	LastModifiedTs uint32 `protobuf:"varint,7,opt,name=last_modified_ts,json=lastModifiedTs,proto3" json:"last_modified_ts,omitempty"`
}

func (m *MayDayUserData) Reset()                    { *m = MayDayUserData{} }
func (m *MayDayUserData) String() string            { return proto.CompactTextString(m) }
func (*MayDayUserData) ProtoMessage()               {}
func (*MayDayUserData) Descriptor() ([]byte, []int) { return fileDescriptorActivitypresent, []int{49} }

func (m *MayDayUserData) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *MayDayUserData) GetActivityId() uint32 {
	if m != nil {
		return m.ActivityId
	}
	return 0
}

func (m *MayDayUserData) GetPhase() uint32 {
	if m != nil {
		return m.Phase
	}
	return 0
}

func (m *MayDayUserData) GetTotalScore() uint32 {
	if m != nil {
		return m.TotalScore
	}
	return 0
}

func (m *MayDayUserData) GetTodayScore() uint32 {
	if m != nil {
		return m.TodayScore
	}
	return 0
}

func (m *MayDayUserData) GetPresentValue() uint32 {
	if m != nil {
		return m.PresentValue
	}
	return 0
}

func (m *MayDayUserData) GetLastModifiedTs() uint32 {
	if m != nil {
		return m.LastModifiedTs
	}
	return 0
}

type UserDailyInfoData struct {
	Uid              uint32 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ActivityId       uint32 `protobuf:"varint,2,opt,name=activity_id,json=activityId,proto3" json:"activity_id,omitempty"`
	ListType         uint32 `protobuf:"varint,3,opt,name=list_type,json=listType,proto3" json:"list_type,omitempty"`
	LastModifiedDate uint32 `protobuf:"varint,4,opt,name=last_modified_date,json=lastModifiedDate,proto3" json:"last_modified_date,omitempty"`
	TodayRedDiamond  uint32 `protobuf:"varint,5,opt,name=today_red_diamond,json=todayRedDiamond,proto3" json:"today_red_diamond,omitempty"`
	TodayTbean       uint32 `protobuf:"varint,6,opt,name=today_tbean,json=todayTbean,proto3" json:"today_tbean,omitempty"`
}

func (m *UserDailyInfoData) Reset()         { *m = UserDailyInfoData{} }
func (m *UserDailyInfoData) String() string { return proto.CompactTextString(m) }
func (*UserDailyInfoData) ProtoMessage()    {}
func (*UserDailyInfoData) Descriptor() ([]byte, []int) {
	return fileDescriptorActivitypresent, []int{50}
}

func (m *UserDailyInfoData) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserDailyInfoData) GetActivityId() uint32 {
	if m != nil {
		return m.ActivityId
	}
	return 0
}

func (m *UserDailyInfoData) GetListType() uint32 {
	if m != nil {
		return m.ListType
	}
	return 0
}

func (m *UserDailyInfoData) GetLastModifiedDate() uint32 {
	if m != nil {
		return m.LastModifiedDate
	}
	return 0
}

func (m *UserDailyInfoData) GetTodayRedDiamond() uint32 {
	if m != nil {
		return m.TodayRedDiamond
	}
	return 0
}

func (m *UserDailyInfoData) GetTodayTbean() uint32 {
	if m != nil {
		return m.TodayTbean
	}
	return 0
}

// 获取当前 各种送礼/收礼活动通用的 排行榜黑名单用户列表
// 该接口数据是从配置中心SDK获取，因此该接口只提供给WEB服务来获取数据
// 其他服务 可以直接使用通用配置中心SDK来获取黑名单数据 (commConfigCenterSDK/rankblacklist/RankBlackUidListWatcher.h)
type GetRankBlackUidListReq struct {
}

func (m *GetRankBlackUidListReq) Reset()         { *m = GetRankBlackUidListReq{} }
func (m *GetRankBlackUidListReq) String() string { return proto.CompactTextString(m) }
func (*GetRankBlackUidListReq) ProtoMessage()    {}
func (*GetRankBlackUidListReq) Descriptor() ([]byte, []int) {
	return fileDescriptorActivitypresent, []int{51}
}

type GetRankBlackUidListResp struct {
	RecvRankUidList []uint32 `protobuf:"varint,1,rep,packed,name=recv_rank_uid_list,json=recvRankUidList" json:"recv_rank_uid_list,omitempty"`
	SendRankUidList []uint32 `protobuf:"varint,2,rep,packed,name=send_rank_uid_list,json=sendRankUidList" json:"send_rank_uid_list,omitempty"`
}

func (m *GetRankBlackUidListResp) Reset()         { *m = GetRankBlackUidListResp{} }
func (m *GetRankBlackUidListResp) String() string { return proto.CompactTextString(m) }
func (*GetRankBlackUidListResp) ProtoMessage()    {}
func (*GetRankBlackUidListResp) Descriptor() ([]byte, []int) {
	return fileDescriptorActivitypresent, []int{52}
}

func (m *GetRankBlackUidListResp) GetRecvRankUidList() []uint32 {
	if m != nil {
		return m.RecvRankUidList
	}
	return nil
}

func (m *GetRankBlackUidListResp) GetSendRankUidList() []uint32 {
	if m != nil {
		return m.SendRankUidList
	}
	return nil
}

func init() {
	proto.RegisterType((*StActivityPresentBriefConfig)(nil), "activitypresent.StActivityPresentBriefConfig")
	proto.RegisterType((*StActivityPresent)(nil), "activitypresent.StActivityPresent")
	proto.RegisterType((*StPresentCount)(nil), "activitypresent.StPresentCount")
	proto.RegisterType((*StIntKeyValue)(nil), "activitypresent.StIntKeyValue")
	proto.RegisterType((*StStrKeyValue)(nil), "activitypresent.StStrKeyValue")
	proto.RegisterType((*StActivityListDetail)(nil), "activitypresent.StActivityListDetail")
	proto.RegisterType((*GetPresentActivityInfoReq)(nil), "activitypresent.GetPresentActivityInfoReq")
	proto.RegisterType((*GetPresentActivityInfoResp)(nil), "activitypresent.GetPresentActivityInfoResp")
	proto.RegisterType((*StPresentActUserFansInfo)(nil), "activitypresent.StPresentActUserFansInfo")
	proto.RegisterType((*StPresentActUserExtentInfo)(nil), "activitypresent.StPresentActUserExtentInfo")
	proto.RegisterType((*StPresentActUserRankInfo)(nil), "activitypresent.StPresentActUserRankInfo")
	proto.RegisterType((*GetPresentActivityRankingListReq)(nil), "activitypresent.GetPresentActivityRankingListReq")
	proto.RegisterType((*GetPresentActivityRankingListResp)(nil), "activitypresent.GetPresentActivityRankingListResp")
	proto.RegisterType((*GetPresentActivityUserInfoReq)(nil), "activitypresent.GetPresentActivityUserInfoReq")
	proto.RegisterType((*GetPresentActivityUserInfoResp)(nil), "activitypresent.GetPresentActivityUserInfoResp")
	proto.RegisterType((*GetPresentActivityRankingByUidReq)(nil), "activitypresent.GetPresentActivityRankingByUidReq")
	proto.RegisterType((*GetPresentActivityRankingByUidResp)(nil), "activitypresent.GetPresentActivityRankingByUidResp")
	proto.RegisterType((*RecordActivitySendPresentReq)(nil), "activitypresent.RecordActivitySendPresentReq")
	proto.RegisterType((*RecalculateUserActivityInfoReq)(nil), "activitypresent.RecalculateUserActivityInfoReq")
	proto.RegisterType((*RecalculateActivityInfoReq)(nil), "activitypresent.RecalculateActivityInfoReq")
	proto.RegisterType((*NotifyActivityPhaseChangeReq)(nil), "activitypresent.NotifyActivityPhaseChangeReq")
	proto.RegisterType((*LoveLetterInfo)(nil), "activitypresent.LoveLetterInfo")
	proto.RegisterType((*LoveLetterInfoList)(nil), "activitypresent.LoveLetterInfoList")
	proto.RegisterType((*ModifyLoveLetterReq)(nil), "activitypresent.modifyLoveLetterReq")
	proto.RegisterType((*ModifyLoveLetterResp)(nil), "activitypresent.modifyLoveLetterResp")
	proto.RegisterType((*GetLoveLetterListReq)(nil), "activitypresent.GetLoveLetterListReq")
	proto.RegisterType((*GetLoveLetterListResp)(nil), "activitypresent.GetLoveLetterListResp")
	proto.RegisterType((*ActCommonAward)(nil), "activitypresent.ActCommonAward")
	proto.RegisterType((*ConsumeRecord)(nil), "activitypresent.ConsumeRecord")
	proto.RegisterType((*ActBattleSubMsg)(nil), "activitypresent.ActBattleSubMsg")
	proto.RegisterType((*ActBattleProgress)(nil), "activitypresent.ActBattleProgress")
	proto.RegisterType((*GetBattleProgressListReq)(nil), "activitypresent.GetBattleProgressListReq")
	proto.RegisterType((*GetBattleProgressListResp)(nil), "activitypresent.GetBattleProgressListResp")
	proto.RegisterType((*ModifyUserActivityInfoReq)(nil), "activitypresent.ModifyUserActivityInfoReq")
	proto.RegisterType((*ModifyUserActivityInfoResp)(nil), "activitypresent.ModifyUserActivityInfoResp")
	proto.RegisterType((*DoTestReq)(nil), "activitypresent.DoTestReq")
	proto.RegisterType((*DoTestResp)(nil), "activitypresent.DoTestResp")
	proto.RegisterType((*IdolInfo)(nil), "activitypresent.IdolInfo")
	proto.RegisterType((*IdolInfoList)(nil), "activitypresent.IdolInfoList")
	proto.RegisterType((*GetIdolListReq)(nil), "activitypresent.GetIdolListReq")
	proto.RegisterType((*GetIdolListResp)(nil), "activitypresent.GetIdolListResp")
	proto.RegisterType((*GetPresentActivityExtentInfoReq)(nil), "activitypresent.GetPresentActivityExtentInfoReq")
	proto.RegisterType((*GetPresentActivityExtentInfoResp)(nil), "activitypresent.GetPresentActivityExtentInfoResp")
	proto.RegisterType((*GetPresentActivityUserExtentInfoReq)(nil), "activitypresent.GetPresentActivityUserExtentInfoReq")
	proto.RegisterType((*GetPresentActivityUserExtentInfoResp)(nil), "activitypresent.GetPresentActivityUserExtentInfoResp")
	proto.RegisterType((*ActDailyRanking)(nil), "activitypresent.ActDailyRanking")
	proto.RegisterType((*ActHistoryRanking)(nil), "activitypresent.ActHistoryRanking")
	proto.RegisterType((*LanternFestivalUserData)(nil), "activitypresent.LanternFestivalUserData")
	proto.RegisterType((*WorldEarthDayUserData)(nil), "activitypresent.WorldEarthDayUserData")
	proto.RegisterType((*MayDayUserData)(nil), "activitypresent.MayDayUserData")
	proto.RegisterType((*UserDailyInfoData)(nil), "activitypresent.UserDailyInfoData")
	proto.RegisterType((*GetRankBlackUidListReq)(nil), "activitypresent.GetRankBlackUidListReq")
	proto.RegisterType((*GetRankBlackUidListResp)(nil), "activitypresent.GetRankBlackUidListResp")
	proto.RegisterEnum("activitypresent.PresentRankingListType", PresentRankingListType_name, PresentRankingListType_value)
	proto.RegisterEnum("activitypresent.PresentActivityId", PresentActivityId_name, PresentActivityId_value)
	proto.RegisterEnum("activitypresent.PresentFromType", PresentFromType_name, PresentFromType_value)
	proto.RegisterEnum("activitypresent.ActivityPhaseChangeType", ActivityPhaseChangeType_name, ActivityPhaseChangeType_value)
	proto.RegisterEnum("activitypresent.PresentActivityExtentInfoType", PresentActivityExtentInfoType_name, PresentActivityExtentInfoType_value)
	proto.RegisterEnum("activitypresent.LoveLetterLinkType", LoveLetterLinkType_name, LoveLetterLinkType_value)
	proto.RegisterEnum("activitypresent.ModifyLoveLetterReq_LoveLetterType", ModifyLoveLetterReq_LoveLetterType_name, ModifyLoveLetterReq_LoveLetterType_value)
	proto.RegisterEnum("activitypresent.ActCommonAward_ActCommonAwardType", ActCommonAward_ActCommonAwardType_name, ActCommonAward_ActCommonAwardType_value)
	proto.RegisterEnum("activitypresent.ActBattleSubMsg_ActBattleSubMsgType", ActBattleSubMsg_ActBattleSubMsgType_name, ActBattleSubMsg_ActBattleSubMsgType_value)
	proto.RegisterEnum("activitypresent.ModifyUserActivityInfoReq_ModifyUserInfoType", ModifyUserActivityInfoReq_ModifyUserInfoType_name, ModifyUserActivityInfoReq_ModifyUserInfoType_value)
}
func (m *StActivityPresentBriefConfig) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *StActivityPresentBriefConfig) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.ItemId != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(m.ItemId))
	}
	if m.Price != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(m.Price))
	}
	if m.PriceType != 0 {
		dAtA[i] = 0x18
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(m.PriceType))
	}
	if m.Score != 0 {
		dAtA[i] = 0x20
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(m.Score))
	}
	if m.Charm != 0 {
		dAtA[i] = 0x28
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(m.Charm))
	}
	if m.RichValue != 0 {
		dAtA[i] = 0x30
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(m.RichValue))
	}
	return i, nil
}

func (m *StActivityPresent) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *StActivityPresent) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.ItemId != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(m.ItemId))
	}
	if len(m.IconUrl) > 0 {
		dAtA[i] = 0x12
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(len(m.IconUrl)))
		i += copy(dAtA[i:], m.IconUrl)
	}
	if m.ActivityId != 0 {
		dAtA[i] = 0x18
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(m.ActivityId))
	}
	if len(m.Extend) > 0 {
		dAtA[i] = 0x22
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(len(m.Extend)))
		i += copy(dAtA[i:], m.Extend)
	}
	if m.ShowIndex != 0 {
		dAtA[i] = 0x28
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(m.ShowIndex))
	}
	return i, nil
}

func (m *StPresentCount) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *StPresentCount) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.ItemId != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(m.ItemId))
	}
	if m.Count != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(m.Count))
	}
	if m.ListType != 0 {
		dAtA[i] = 0x18
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(m.ListType))
	}
	return i, nil
}

func (m *StIntKeyValue) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *StIntKeyValue) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Key != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(m.Key))
	}
	if m.Value != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(m.Value))
	}
	return i, nil
}

func (m *StStrKeyValue) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *StStrKeyValue) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.Key) > 0 {
		dAtA[i] = 0xa
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(len(m.Key)))
		i += copy(dAtA[i:], m.Key)
	}
	if len(m.Value) > 0 {
		dAtA[i] = 0x12
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(len(m.Value)))
		i += copy(dAtA[i:], m.Value)
	}
	return i, nil
}

func (m *StActivityListDetail) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *StActivityListDetail) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.ListType != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(m.ListType))
	}
	if len(m.ItemList) > 0 {
		for _, msg := range m.ItemList {
			dAtA[i] = 0x12
			i++
			i = encodeVarintActivitypresent(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *GetPresentActivityInfoReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetPresentActivityInfoReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.ActivityId != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(m.ActivityId))
	}
	return i, nil
}

func (m *GetPresentActivityInfoResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetPresentActivityInfoResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.Title) > 0 {
		dAtA[i] = 0xa
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(len(m.Title)))
		i += copy(dAtA[i:], m.Title)
	}
	if len(m.BannerUrl) > 0 {
		dAtA[i] = 0x12
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(len(m.BannerUrl)))
		i += copy(dAtA[i:], m.BannerUrl)
	}
	if m.BeginTime != 0 {
		dAtA[i] = 0x18
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(m.BeginTime))
	}
	if m.EndTime != 0 {
		dAtA[i] = 0x20
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(m.EndTime))
	}
	if len(m.PresentList) > 0 {
		for _, msg := range m.PresentList {
			dAtA[i] = 0x2a
			i++
			i = encodeVarintActivitypresent(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	if m.ActivityId != 0 {
		dAtA[i] = 0x30
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(m.ActivityId))
	}
	if len(m.DetailList) > 0 {
		for _, msg := range m.DetailList {
			dAtA[i] = 0x3a
			i++
			i = encodeVarintActivitypresent(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	if len(m.BroadcastImgUrl) > 0 {
		dAtA[i] = 0x42
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(len(m.BroadcastImgUrl)))
		i += copy(dAtA[i:], m.BroadcastImgUrl)
	}
	if len(m.JumpUrl) > 0 {
		dAtA[i] = 0x4a
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(len(m.JumpUrl)))
		i += copy(dAtA[i:], m.JumpUrl)
	}
	if len(m.BackImgUrl) > 0 {
		dAtA[i] = 0x52
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(len(m.BackImgUrl)))
		i += copy(dAtA[i:], m.BackImgUrl)
	}
	if len(m.IconImgUrl) > 0 {
		dAtA[i] = 0x5a
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(len(m.IconImgUrl)))
		i += copy(dAtA[i:], m.IconImgUrl)
	}
	if len(m.ActivityExtentInfo) > 0 {
		dAtA[i] = 0x62
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(len(m.ActivityExtentInfo)))
		i += copy(dAtA[i:], m.ActivityExtentInfo)
	}
	return i, nil
}

func (m *StPresentActUserFansInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *StPresentActUserFansInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Uid != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(m.Uid))
	}
	if m.RankValue != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(m.RankValue))
	}
	if m.Ranking != 0 {
		dAtA[i] = 0x18
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(m.Ranking))
	}
	return i, nil
}

func (m *StPresentActUserExtentInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *StPresentActUserExtentInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Uid != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(m.Uid))
	}
	if m.ActivityId != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(m.ActivityId))
	}
	if m.ListType != 0 {
		dAtA[i] = 0x18
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(m.ListType))
	}
	if m.Ranking != 0 {
		dAtA[i] = 0x20
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(m.Ranking))
	}
	if len(m.Extent) > 0 {
		dAtA[i] = 0x2a
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(len(m.Extent)))
		i += copy(dAtA[i:], m.Extent)
	}
	return i, nil
}

func (m *StPresentActUserRankInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *StPresentActUserRankInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Uid != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(m.Uid))
	}
	if m.ItemId != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(m.ItemId))
	}
	if m.ListType != 0 {
		dAtA[i] = 0x18
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(m.ListType))
	}
	if m.RankValue != 0 {
		dAtA[i] = 0x20
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(m.RankValue))
	}
	if m.Ranking != 0 {
		dAtA[i] = 0x28
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(m.Ranking))
	}
	if m.ActivityId != 0 {
		dAtA[i] = 0x30
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(m.ActivityId))
	}
	if m.ChannelId != 0 {
		dAtA[i] = 0x38
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(m.ChannelId))
	}
	if len(m.FansList) > 0 {
		for _, msg := range m.FansList {
			dAtA[i] = 0x42
			i++
			i = encodeVarintActivitypresent(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *GetPresentActivityRankingListReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetPresentActivityRankingListReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.ItemId != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(m.ItemId))
	}
	if m.ListType != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(m.ListType))
	}
	if m.Offset != 0 {
		dAtA[i] = 0x18
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(m.Offset))
	}
	if m.Limit != 0 {
		dAtA[i] = 0x20
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(m.Limit))
	}
	if m.ActivityId != 0 {
		dAtA[i] = 0x28
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(m.ActivityId))
	}
	return i, nil
}

func (m *GetPresentActivityRankingListResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetPresentActivityRankingListResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.RankList) > 0 {
		for _, msg := range m.RankList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintActivitypresent(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	if m.TotalCount != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(m.TotalCount))
	}
	return i, nil
}

func (m *GetPresentActivityUserInfoReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetPresentActivityUserInfoReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Uid != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(m.Uid))
	}
	if m.ActivityId != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(m.ActivityId))
	}
	if m.ListType != 0 {
		dAtA[i] = 0x18
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(m.ListType))
	}
	return i, nil
}

func (m *GetPresentActivityUserInfoResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetPresentActivityUserInfoResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.UserInfo) > 0 {
		dAtA[i] = 0xa
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(len(m.UserInfo)))
		i += copy(dAtA[i:], m.UserInfo)
	}
	if m.ConsumeValue != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(m.ConsumeValue))
	}
	if m.ExValue != 0 {
		dAtA[i] = 0x18
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(m.ExValue))
	}
	if m.ExValue2 != 0 {
		dAtA[i] = 0x20
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(m.ExValue2))
	}
	if len(m.UserInfo2) > 0 {
		dAtA[i] = 0x2a
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(len(m.UserInfo2)))
		i += copy(dAtA[i:], m.UserInfo2)
	}
	return i, nil
}

func (m *GetPresentActivityRankingByUidReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetPresentActivityRankingByUidReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Uid != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(m.Uid))
	}
	if m.ItemId != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(m.ItemId))
	}
	if m.ListType != 0 {
		dAtA[i] = 0x18
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(m.ListType))
	}
	if m.ActivityId != 0 {
		dAtA[i] = 0x20
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(m.ActivityId))
	}
	if m.GetUpper {
		dAtA[i] = 0x28
		i++
		if m.GetUpper {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i++
	}
	if m.GetFollow {
		dAtA[i] = 0x30
		i++
		if m.GetFollow {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i++
	}
	return i, nil
}

func (m *GetPresentActivityRankingByUidResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetPresentActivityRankingByUidResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.RankInfo != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(m.RankInfo.Size()))
		n1, err := m.RankInfo.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n1
	}
	if m.UpperRankInfo != nil {
		dAtA[i] = 0x12
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(m.UpperRankInfo.Size()))
		n2, err := m.UpperRankInfo.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n2
	}
	if m.FollowRankInfo != nil {
		dAtA[i] = 0x1a
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(m.FollowRankInfo.Size()))
		n3, err := m.FollowRankInfo.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n3
	}
	return i, nil
}

func (m *RecordActivitySendPresentReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RecordActivitySendPresentReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Uid != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(m.Uid))
	}
	if m.TargetUid != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(m.TargetUid))
	}
	if len(m.OrderId) > 0 {
		dAtA[i] = 0x1a
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(len(m.OrderId)))
		i += copy(dAtA[i:], m.OrderId)
	}
	if m.ItemConfig != nil {
		dAtA[i] = 0x22
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(m.ItemConfig.Size()))
		n4, err := m.ItemConfig.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n4
	}
	if m.ItemCount != 0 {
		dAtA[i] = 0x28
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(m.ItemCount))
	}
	if m.PresentFromType != 0 {
		dAtA[i] = 0x30
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(m.PresentFromType))
	}
	if m.SendTime != 0 {
		dAtA[i] = 0x38
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(m.SendTime))
	}
	if m.ChannelId != 0 {
		dAtA[i] = 0x40
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(m.ChannelId))
	}
	if m.ChannelType != 0 {
		dAtA[i] = 0x48
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(m.ChannelType))
	}
	return i, nil
}

func (m *RecalculateUserActivityInfoReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RecalculateUserActivityInfoReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Uid != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(m.Uid))
	}
	if m.ActivityId != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(m.ActivityId))
	}
	if m.ListType != 0 {
		dAtA[i] = 0x18
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(m.ListType))
	}
	return i, nil
}

func (m *RecalculateActivityInfoReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RecalculateActivityInfoReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.ActivityId != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(m.ActivityId))
	}
	if m.ListType != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(m.ListType))
	}
	return i, nil
}

func (m *NotifyActivityPhaseChangeReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *NotifyActivityPhaseChangeReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Type != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(m.Type))
	}
	return i, nil
}

func (m *LoveLetterInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *LoveLetterInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.SendUid != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(m.SendUid))
	}
	if m.TargetUid != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(m.TargetUid))
	}
	if m.LetterStatus != 0 {
		dAtA[i] = 0x18
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(m.LetterStatus))
	}
	if m.LetterType != 0 {
		dAtA[i] = 0x20
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(m.LetterType))
	}
	if len(m.LetterText) > 0 {
		dAtA[i] = 0x2a
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(len(m.LetterText)))
		i += copy(dAtA[i:], m.LetterText)
	}
	if m.DateInt != 0 {
		dAtA[i] = 0x30
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(m.DateInt))
	}
	if m.LetterId != 0 {
		dAtA[i] = 0x38
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(m.LetterId))
	}
	return i, nil
}

func (m *LoveLetterInfoList) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *LoveLetterInfoList) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.LoveLetterList) > 0 {
		for _, msg := range m.LoveLetterList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintActivitypresent(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *ModifyLoveLetterReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ModifyLoveLetterReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.LetterId != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(m.LetterId))
	}
	if m.SendUid != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(m.SendUid))
	}
	if m.TargetUid != 0 {
		dAtA[i] = 0x18
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(m.TargetUid))
	}
	if m.ActivityId != 0 {
		dAtA[i] = 0x20
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(m.ActivityId))
	}
	if m.DateInt != 0 {
		dAtA[i] = 0x28
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(m.DateInt))
	}
	if m.LetterType != 0 {
		dAtA[i] = 0x30
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(m.LetterType))
	}
	if len(m.LetterText) > 0 {
		dAtA[i] = 0x3a
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(len(m.LetterText)))
		i += copy(dAtA[i:], m.LetterText)
	}
	return i, nil
}

func (m *ModifyLoveLetterResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ModifyLoveLetterResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Code != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(m.Code))
	}
	if len(m.NewContent) > 0 {
		dAtA[i] = 0x12
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(len(m.NewContent)))
		i += copy(dAtA[i:], m.NewContent)
	}
	return i, nil
}

func (m *GetLoveLetterListReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetLoveLetterListReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.DateInt != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(m.DateInt))
	}
	return i, nil
}

func (m *GetLoveLetterListResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetLoveLetterListResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Code != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(m.Code))
	}
	if len(m.LoveLetterList) > 0 {
		for _, msg := range m.LoveLetterList {
			dAtA[i] = 0x12
			i++
			i = encodeVarintActivitypresent(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *ActCommonAward) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ActCommonAward) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.AwardType != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(m.AwardType))
	}
	if m.AwardId != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(m.AwardId))
	}
	if len(m.StrAwardId) > 0 {
		dAtA[i] = 0x1a
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(len(m.StrAwardId)))
		i += copy(dAtA[i:], m.StrAwardId)
	}
	if m.AwardNum != 0 {
		dAtA[i] = 0x20
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(m.AwardNum))
	}
	if m.AwardTime != 0 {
		dAtA[i] = 0x28
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(m.AwardTime))
	}
	if m.AwardLmt != 0 {
		dAtA[i] = 0x30
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(m.AwardLmt))
	}
	if len(m.AwardVer) > 0 {
		dAtA[i] = 0x3a
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(len(m.AwardVer)))
		i += copy(dAtA[i:], m.AwardVer)
	}
	return i, nil
}

func (m *ConsumeRecord) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ConsumeRecord) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Uid != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(m.Uid))
	}
	if m.ConsumeValue != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(m.ConsumeValue))
	}
	if m.Ts != 0 {
		dAtA[i] = 0x18
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(m.Ts))
	}
	if m.ActivityId != 0 {
		dAtA[i] = 0x20
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(m.ActivityId))
	}
	if len(m.OrderId) > 0 {
		dAtA[i] = 0x2a
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(len(m.OrderId)))
		i += copy(dAtA[i:], m.OrderId)
	}
	return i, nil
}

func (m *ActBattleSubMsg) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ActBattleSubMsg) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Type != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(m.Type))
	}
	if len(m.Content) > 0 {
		dAtA[i] = 0x12
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(len(m.Content)))
		i += copy(dAtA[i:], m.Content)
	}
	return i, nil
}

func (m *ActBattleProgress) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ActBattleProgress) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.TimeTs != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(m.TimeTs))
	}
	if len(m.Msg) > 0 {
		dAtA[i] = 0x12
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(len(m.Msg)))
		i += copy(dAtA[i:], m.Msg)
	}
	if len(m.SubMsgList) > 0 {
		for _, msg := range m.SubMsgList {
			dAtA[i] = 0x1a
			i++
			i = encodeVarintActivitypresent(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *GetBattleProgressListReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetBattleProgressListReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.ActivityId != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(m.ActivityId))
	}
	if m.ListType != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(m.ListType))
	}
	if m.Offset != 0 {
		dAtA[i] = 0x18
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(m.Offset))
	}
	if m.Limit != 0 {
		dAtA[i] = 0x20
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(m.Limit))
	}
	return i, nil
}

func (m *GetBattleProgressListResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetBattleProgressListResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.ProgressList) > 0 {
		for _, msg := range m.ProgressList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintActivitypresent(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *ModifyUserActivityInfoReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ModifyUserActivityInfoReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Uid != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(m.Uid))
	}
	if m.ActivityId != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(m.ActivityId))
	}
	if m.OpType != 0 {
		dAtA[i] = 0x18
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(m.OpType))
	}
	if m.OpValue != 0 {
		dAtA[i] = 0x20
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(m.OpValue))
	}
	if len(m.OpData) > 0 {
		dAtA[i] = 0x2a
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(len(m.OpData)))
		i += copy(dAtA[i:], m.OpData)
	}
	return i, nil
}

func (m *ModifyUserActivityInfoResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ModifyUserActivityInfoResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *DoTestReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DoTestReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Uid != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(m.Uid))
	}
	if m.ActivityId != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(m.ActivityId))
	}
	if m.OpType != 0 {
		dAtA[i] = 0x18
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(m.OpType))
	}
	if m.OpValue != 0 {
		dAtA[i] = 0x20
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(m.OpValue))
	}
	if len(m.OpData) > 0 {
		dAtA[i] = 0x2a
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(len(m.OpData)))
		i += copy(dAtA[i:], m.OpData)
	}
	return i, nil
}

func (m *DoTestResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DoTestResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *IdolInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *IdolInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Uid != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(m.Uid))
	}
	if m.RankValue != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(m.RankValue))
	}
	return i, nil
}

func (m *IdolInfoList) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *IdolInfoList) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.IdolInfoList) > 0 {
		for _, msg := range m.IdolInfoList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintActivitypresent(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *GetIdolListReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetIdolListReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Offset != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(m.Offset))
	}
	if m.Limit != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(m.Limit))
	}
	return i, nil
}

func (m *GetIdolListResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetIdolListResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Code != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(m.Code))
	}
	if m.IdolInfoList != nil {
		dAtA[i] = 0x12
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(m.IdolInfoList.Size()))
		n5, err := m.IdolInfoList.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n5
	}
	return i, nil
}

func (m *GetPresentActivityExtentInfoReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetPresentActivityExtentInfoReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.ActivityId != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(m.ActivityId))
	}
	if m.ExtentInfoType != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(m.ExtentInfoType))
	}
	return i, nil
}

func (m *GetPresentActivityExtentInfoResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetPresentActivityExtentInfoResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Code != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(m.Code))
	}
	if len(m.ActivityExtentInfo) > 0 {
		dAtA[i] = 0x12
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(len(m.ActivityExtentInfo)))
		i += copy(dAtA[i:], m.ActivityExtentInfo)
	}
	return i, nil
}

func (m *GetPresentActivityUserExtentInfoReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetPresentActivityUserExtentInfoReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.ActivityId != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(m.ActivityId))
	}
	if m.Uid != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(m.Uid))
	}
	if m.ExtentInfoType != 0 {
		dAtA[i] = 0x18
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(m.ExtentInfoType))
	}
	if len(m.ExtentParam) > 0 {
		dAtA[i] = 0x22
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(len(m.ExtentParam)))
		i += copy(dAtA[i:], m.ExtentParam)
	}
	return i, nil
}

func (m *GetPresentActivityUserExtentInfoResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetPresentActivityUserExtentInfoResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Code != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(m.Code))
	}
	if len(m.UserExtentInfo) > 0 {
		dAtA[i] = 0x12
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(len(m.UserExtentInfo)))
		i += copy(dAtA[i:], m.UserExtentInfo)
	}
	return i, nil
}

func (m *ActDailyRanking) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ActDailyRanking) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.RankList) > 0 {
		for _, msg := range m.RankList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintActivitypresent(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	if m.Date != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(m.Date))
	}
	return i, nil
}

func (m *ActHistoryRanking) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ActHistoryRanking) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.HistoryList) > 0 {
		for _, msg := range m.HistoryList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintActivitypresent(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	if m.LastModifiedDate != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(m.LastModifiedDate))
	}
	if m.DisableTodayList {
		dAtA[i] = 0x18
		i++
		if m.DisableTodayList {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i++
	}
	return i, nil
}

func (m *LanternFestivalUserData) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *LanternFestivalUserData) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Uid != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(m.Uid))
	}
	if m.ActivityId != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(m.ActivityId))
	}
	if m.Phase != 0 {
		dAtA[i] = 0x18
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(m.Phase))
	}
	if m.Score != 0 {
		dAtA[i] = 0x20
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(m.Score))
	}
	if len(m.PresentList) > 0 {
		for _, msg := range m.PresentList {
			dAtA[i] = 0x2a
			i++
			i = encodeVarintActivitypresent(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	if m.LastModifiedTs != 0 {
		dAtA[i] = 0x30
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(m.LastModifiedTs))
	}
	return i, nil
}

func (m *WorldEarthDayUserData) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *WorldEarthDayUserData) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Uid != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(m.Uid))
	}
	if m.ActivityId != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(m.ActivityId))
	}
	if len(m.HistoryRankList) > 0 {
		for _, msg := range m.HistoryRankList {
			dAtA[i] = 0x1a
			i++
			i = encodeVarintActivitypresent(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	if m.LastModifiedDate != 0 {
		dAtA[i] = 0x20
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(m.LastModifiedDate))
	}
	if m.ExValue != 0 {
		dAtA[i] = 0x28
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(m.ExValue))
	}
	return i, nil
}

func (m *MayDayUserData) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *MayDayUserData) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Uid != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(m.Uid))
	}
	if m.ActivityId != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(m.ActivityId))
	}
	if m.Phase != 0 {
		dAtA[i] = 0x18
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(m.Phase))
	}
	if m.TotalScore != 0 {
		dAtA[i] = 0x20
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(m.TotalScore))
	}
	if m.TodayScore != 0 {
		dAtA[i] = 0x28
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(m.TodayScore))
	}
	if m.PresentValue != 0 {
		dAtA[i] = 0x30
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(m.PresentValue))
	}
	if m.LastModifiedTs != 0 {
		dAtA[i] = 0x38
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(m.LastModifiedTs))
	}
	return i, nil
}

func (m *UserDailyInfoData) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UserDailyInfoData) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Uid != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(m.Uid))
	}
	if m.ActivityId != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(m.ActivityId))
	}
	if m.ListType != 0 {
		dAtA[i] = 0x18
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(m.ListType))
	}
	if m.LastModifiedDate != 0 {
		dAtA[i] = 0x20
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(m.LastModifiedDate))
	}
	if m.TodayRedDiamond != 0 {
		dAtA[i] = 0x28
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(m.TodayRedDiamond))
	}
	if m.TodayTbean != 0 {
		dAtA[i] = 0x30
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(m.TodayTbean))
	}
	return i, nil
}

func (m *GetRankBlackUidListReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetRankBlackUidListReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *GetRankBlackUidListResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetRankBlackUidListResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.RecvRankUidList) > 0 {
		dAtA7 := make([]byte, len(m.RecvRankUidList)*10)
		var j6 int
		for _, num := range m.RecvRankUidList {
			for num >= 1<<7 {
				dAtA7[j6] = uint8(uint64(num)&0x7f | 0x80)
				num >>= 7
				j6++
			}
			dAtA7[j6] = uint8(num)
			j6++
		}
		dAtA[i] = 0xa
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(j6))
		i += copy(dAtA[i:], dAtA7[:j6])
	}
	if len(m.SendRankUidList) > 0 {
		dAtA9 := make([]byte, len(m.SendRankUidList)*10)
		var j8 int
		for _, num := range m.SendRankUidList {
			for num >= 1<<7 {
				dAtA9[j8] = uint8(uint64(num)&0x7f | 0x80)
				num >>= 7
				j8++
			}
			dAtA9[j8] = uint8(num)
			j8++
		}
		dAtA[i] = 0x12
		i++
		i = encodeVarintActivitypresent(dAtA, i, uint64(j8))
		i += copy(dAtA[i:], dAtA9[:j8])
	}
	return i, nil
}

func encodeFixed64Activitypresent(dAtA []byte, offset int, v uint64) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	dAtA[offset+4] = uint8(v >> 32)
	dAtA[offset+5] = uint8(v >> 40)
	dAtA[offset+6] = uint8(v >> 48)
	dAtA[offset+7] = uint8(v >> 56)
	return offset + 8
}
func encodeFixed32Activitypresent(dAtA []byte, offset int, v uint32) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	return offset + 4
}
func encodeVarintActivitypresent(dAtA []byte, offset int, v uint64) int {
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return offset + 1
}
func (m *StActivityPresentBriefConfig) Size() (n int) {
	var l int
	_ = l
	if m.ItemId != 0 {
		n += 1 + sovActivitypresent(uint64(m.ItemId))
	}
	if m.Price != 0 {
		n += 1 + sovActivitypresent(uint64(m.Price))
	}
	if m.PriceType != 0 {
		n += 1 + sovActivitypresent(uint64(m.PriceType))
	}
	if m.Score != 0 {
		n += 1 + sovActivitypresent(uint64(m.Score))
	}
	if m.Charm != 0 {
		n += 1 + sovActivitypresent(uint64(m.Charm))
	}
	if m.RichValue != 0 {
		n += 1 + sovActivitypresent(uint64(m.RichValue))
	}
	return n
}

func (m *StActivityPresent) Size() (n int) {
	var l int
	_ = l
	if m.ItemId != 0 {
		n += 1 + sovActivitypresent(uint64(m.ItemId))
	}
	l = len(m.IconUrl)
	if l > 0 {
		n += 1 + l + sovActivitypresent(uint64(l))
	}
	if m.ActivityId != 0 {
		n += 1 + sovActivitypresent(uint64(m.ActivityId))
	}
	l = len(m.Extend)
	if l > 0 {
		n += 1 + l + sovActivitypresent(uint64(l))
	}
	if m.ShowIndex != 0 {
		n += 1 + sovActivitypresent(uint64(m.ShowIndex))
	}
	return n
}

func (m *StPresentCount) Size() (n int) {
	var l int
	_ = l
	if m.ItemId != 0 {
		n += 1 + sovActivitypresent(uint64(m.ItemId))
	}
	if m.Count != 0 {
		n += 1 + sovActivitypresent(uint64(m.Count))
	}
	if m.ListType != 0 {
		n += 1 + sovActivitypresent(uint64(m.ListType))
	}
	return n
}

func (m *StIntKeyValue) Size() (n int) {
	var l int
	_ = l
	if m.Key != 0 {
		n += 1 + sovActivitypresent(uint64(m.Key))
	}
	if m.Value != 0 {
		n += 1 + sovActivitypresent(uint64(m.Value))
	}
	return n
}

func (m *StStrKeyValue) Size() (n int) {
	var l int
	_ = l
	l = len(m.Key)
	if l > 0 {
		n += 1 + l + sovActivitypresent(uint64(l))
	}
	l = len(m.Value)
	if l > 0 {
		n += 1 + l + sovActivitypresent(uint64(l))
	}
	return n
}

func (m *StActivityListDetail) Size() (n int) {
	var l int
	_ = l
	if m.ListType != 0 {
		n += 1 + sovActivitypresent(uint64(m.ListType))
	}
	if len(m.ItemList) > 0 {
		for _, e := range m.ItemList {
			l = e.Size()
			n += 1 + l + sovActivitypresent(uint64(l))
		}
	}
	return n
}

func (m *GetPresentActivityInfoReq) Size() (n int) {
	var l int
	_ = l
	if m.ActivityId != 0 {
		n += 1 + sovActivitypresent(uint64(m.ActivityId))
	}
	return n
}

func (m *GetPresentActivityInfoResp) Size() (n int) {
	var l int
	_ = l
	l = len(m.Title)
	if l > 0 {
		n += 1 + l + sovActivitypresent(uint64(l))
	}
	l = len(m.BannerUrl)
	if l > 0 {
		n += 1 + l + sovActivitypresent(uint64(l))
	}
	if m.BeginTime != 0 {
		n += 1 + sovActivitypresent(uint64(m.BeginTime))
	}
	if m.EndTime != 0 {
		n += 1 + sovActivitypresent(uint64(m.EndTime))
	}
	if len(m.PresentList) > 0 {
		for _, e := range m.PresentList {
			l = e.Size()
			n += 1 + l + sovActivitypresent(uint64(l))
		}
	}
	if m.ActivityId != 0 {
		n += 1 + sovActivitypresent(uint64(m.ActivityId))
	}
	if len(m.DetailList) > 0 {
		for _, e := range m.DetailList {
			l = e.Size()
			n += 1 + l + sovActivitypresent(uint64(l))
		}
	}
	l = len(m.BroadcastImgUrl)
	if l > 0 {
		n += 1 + l + sovActivitypresent(uint64(l))
	}
	l = len(m.JumpUrl)
	if l > 0 {
		n += 1 + l + sovActivitypresent(uint64(l))
	}
	l = len(m.BackImgUrl)
	if l > 0 {
		n += 1 + l + sovActivitypresent(uint64(l))
	}
	l = len(m.IconImgUrl)
	if l > 0 {
		n += 1 + l + sovActivitypresent(uint64(l))
	}
	l = len(m.ActivityExtentInfo)
	if l > 0 {
		n += 1 + l + sovActivitypresent(uint64(l))
	}
	return n
}

func (m *StPresentActUserFansInfo) Size() (n int) {
	var l int
	_ = l
	if m.Uid != 0 {
		n += 1 + sovActivitypresent(uint64(m.Uid))
	}
	if m.RankValue != 0 {
		n += 1 + sovActivitypresent(uint64(m.RankValue))
	}
	if m.Ranking != 0 {
		n += 1 + sovActivitypresent(uint64(m.Ranking))
	}
	return n
}

func (m *StPresentActUserExtentInfo) Size() (n int) {
	var l int
	_ = l
	if m.Uid != 0 {
		n += 1 + sovActivitypresent(uint64(m.Uid))
	}
	if m.ActivityId != 0 {
		n += 1 + sovActivitypresent(uint64(m.ActivityId))
	}
	if m.ListType != 0 {
		n += 1 + sovActivitypresent(uint64(m.ListType))
	}
	if m.Ranking != 0 {
		n += 1 + sovActivitypresent(uint64(m.Ranking))
	}
	l = len(m.Extent)
	if l > 0 {
		n += 1 + l + sovActivitypresent(uint64(l))
	}
	return n
}

func (m *StPresentActUserRankInfo) Size() (n int) {
	var l int
	_ = l
	if m.Uid != 0 {
		n += 1 + sovActivitypresent(uint64(m.Uid))
	}
	if m.ItemId != 0 {
		n += 1 + sovActivitypresent(uint64(m.ItemId))
	}
	if m.ListType != 0 {
		n += 1 + sovActivitypresent(uint64(m.ListType))
	}
	if m.RankValue != 0 {
		n += 1 + sovActivitypresent(uint64(m.RankValue))
	}
	if m.Ranking != 0 {
		n += 1 + sovActivitypresent(uint64(m.Ranking))
	}
	if m.ActivityId != 0 {
		n += 1 + sovActivitypresent(uint64(m.ActivityId))
	}
	if m.ChannelId != 0 {
		n += 1 + sovActivitypresent(uint64(m.ChannelId))
	}
	if len(m.FansList) > 0 {
		for _, e := range m.FansList {
			l = e.Size()
			n += 1 + l + sovActivitypresent(uint64(l))
		}
	}
	return n
}

func (m *GetPresentActivityRankingListReq) Size() (n int) {
	var l int
	_ = l
	if m.ItemId != 0 {
		n += 1 + sovActivitypresent(uint64(m.ItemId))
	}
	if m.ListType != 0 {
		n += 1 + sovActivitypresent(uint64(m.ListType))
	}
	if m.Offset != 0 {
		n += 1 + sovActivitypresent(uint64(m.Offset))
	}
	if m.Limit != 0 {
		n += 1 + sovActivitypresent(uint64(m.Limit))
	}
	if m.ActivityId != 0 {
		n += 1 + sovActivitypresent(uint64(m.ActivityId))
	}
	return n
}

func (m *GetPresentActivityRankingListResp) Size() (n int) {
	var l int
	_ = l
	if len(m.RankList) > 0 {
		for _, e := range m.RankList {
			l = e.Size()
			n += 1 + l + sovActivitypresent(uint64(l))
		}
	}
	if m.TotalCount != 0 {
		n += 1 + sovActivitypresent(uint64(m.TotalCount))
	}
	return n
}

func (m *GetPresentActivityUserInfoReq) Size() (n int) {
	var l int
	_ = l
	if m.Uid != 0 {
		n += 1 + sovActivitypresent(uint64(m.Uid))
	}
	if m.ActivityId != 0 {
		n += 1 + sovActivitypresent(uint64(m.ActivityId))
	}
	if m.ListType != 0 {
		n += 1 + sovActivitypresent(uint64(m.ListType))
	}
	return n
}

func (m *GetPresentActivityUserInfoResp) Size() (n int) {
	var l int
	_ = l
	l = len(m.UserInfo)
	if l > 0 {
		n += 1 + l + sovActivitypresent(uint64(l))
	}
	if m.ConsumeValue != 0 {
		n += 1 + sovActivitypresent(uint64(m.ConsumeValue))
	}
	if m.ExValue != 0 {
		n += 1 + sovActivitypresent(uint64(m.ExValue))
	}
	if m.ExValue2 != 0 {
		n += 1 + sovActivitypresent(uint64(m.ExValue2))
	}
	l = len(m.UserInfo2)
	if l > 0 {
		n += 1 + l + sovActivitypresent(uint64(l))
	}
	return n
}

func (m *GetPresentActivityRankingByUidReq) Size() (n int) {
	var l int
	_ = l
	if m.Uid != 0 {
		n += 1 + sovActivitypresent(uint64(m.Uid))
	}
	if m.ItemId != 0 {
		n += 1 + sovActivitypresent(uint64(m.ItemId))
	}
	if m.ListType != 0 {
		n += 1 + sovActivitypresent(uint64(m.ListType))
	}
	if m.ActivityId != 0 {
		n += 1 + sovActivitypresent(uint64(m.ActivityId))
	}
	if m.GetUpper {
		n += 2
	}
	if m.GetFollow {
		n += 2
	}
	return n
}

func (m *GetPresentActivityRankingByUidResp) Size() (n int) {
	var l int
	_ = l
	if m.RankInfo != nil {
		l = m.RankInfo.Size()
		n += 1 + l + sovActivitypresent(uint64(l))
	}
	if m.UpperRankInfo != nil {
		l = m.UpperRankInfo.Size()
		n += 1 + l + sovActivitypresent(uint64(l))
	}
	if m.FollowRankInfo != nil {
		l = m.FollowRankInfo.Size()
		n += 1 + l + sovActivitypresent(uint64(l))
	}
	return n
}

func (m *RecordActivitySendPresentReq) Size() (n int) {
	var l int
	_ = l
	if m.Uid != 0 {
		n += 1 + sovActivitypresent(uint64(m.Uid))
	}
	if m.TargetUid != 0 {
		n += 1 + sovActivitypresent(uint64(m.TargetUid))
	}
	l = len(m.OrderId)
	if l > 0 {
		n += 1 + l + sovActivitypresent(uint64(l))
	}
	if m.ItemConfig != nil {
		l = m.ItemConfig.Size()
		n += 1 + l + sovActivitypresent(uint64(l))
	}
	if m.ItemCount != 0 {
		n += 1 + sovActivitypresent(uint64(m.ItemCount))
	}
	if m.PresentFromType != 0 {
		n += 1 + sovActivitypresent(uint64(m.PresentFromType))
	}
	if m.SendTime != 0 {
		n += 1 + sovActivitypresent(uint64(m.SendTime))
	}
	if m.ChannelId != 0 {
		n += 1 + sovActivitypresent(uint64(m.ChannelId))
	}
	if m.ChannelType != 0 {
		n += 1 + sovActivitypresent(uint64(m.ChannelType))
	}
	return n
}

func (m *RecalculateUserActivityInfoReq) Size() (n int) {
	var l int
	_ = l
	if m.Uid != 0 {
		n += 1 + sovActivitypresent(uint64(m.Uid))
	}
	if m.ActivityId != 0 {
		n += 1 + sovActivitypresent(uint64(m.ActivityId))
	}
	if m.ListType != 0 {
		n += 1 + sovActivitypresent(uint64(m.ListType))
	}
	return n
}

func (m *RecalculateActivityInfoReq) Size() (n int) {
	var l int
	_ = l
	if m.ActivityId != 0 {
		n += 1 + sovActivitypresent(uint64(m.ActivityId))
	}
	if m.ListType != 0 {
		n += 1 + sovActivitypresent(uint64(m.ListType))
	}
	return n
}

func (m *NotifyActivityPhaseChangeReq) Size() (n int) {
	var l int
	_ = l
	if m.Type != 0 {
		n += 1 + sovActivitypresent(uint64(m.Type))
	}
	return n
}

func (m *LoveLetterInfo) Size() (n int) {
	var l int
	_ = l
	if m.SendUid != 0 {
		n += 1 + sovActivitypresent(uint64(m.SendUid))
	}
	if m.TargetUid != 0 {
		n += 1 + sovActivitypresent(uint64(m.TargetUid))
	}
	if m.LetterStatus != 0 {
		n += 1 + sovActivitypresent(uint64(m.LetterStatus))
	}
	if m.LetterType != 0 {
		n += 1 + sovActivitypresent(uint64(m.LetterType))
	}
	l = len(m.LetterText)
	if l > 0 {
		n += 1 + l + sovActivitypresent(uint64(l))
	}
	if m.DateInt != 0 {
		n += 1 + sovActivitypresent(uint64(m.DateInt))
	}
	if m.LetterId != 0 {
		n += 1 + sovActivitypresent(uint64(m.LetterId))
	}
	return n
}

func (m *LoveLetterInfoList) Size() (n int) {
	var l int
	_ = l
	if len(m.LoveLetterList) > 0 {
		for _, e := range m.LoveLetterList {
			l = e.Size()
			n += 1 + l + sovActivitypresent(uint64(l))
		}
	}
	return n
}

func (m *ModifyLoveLetterReq) Size() (n int) {
	var l int
	_ = l
	if m.LetterId != 0 {
		n += 1 + sovActivitypresent(uint64(m.LetterId))
	}
	if m.SendUid != 0 {
		n += 1 + sovActivitypresent(uint64(m.SendUid))
	}
	if m.TargetUid != 0 {
		n += 1 + sovActivitypresent(uint64(m.TargetUid))
	}
	if m.ActivityId != 0 {
		n += 1 + sovActivitypresent(uint64(m.ActivityId))
	}
	if m.DateInt != 0 {
		n += 1 + sovActivitypresent(uint64(m.DateInt))
	}
	if m.LetterType != 0 {
		n += 1 + sovActivitypresent(uint64(m.LetterType))
	}
	l = len(m.LetterText)
	if l > 0 {
		n += 1 + l + sovActivitypresent(uint64(l))
	}
	return n
}

func (m *ModifyLoveLetterResp) Size() (n int) {
	var l int
	_ = l
	if m.Code != 0 {
		n += 1 + sovActivitypresent(uint64(m.Code))
	}
	l = len(m.NewContent)
	if l > 0 {
		n += 1 + l + sovActivitypresent(uint64(l))
	}
	return n
}

func (m *GetLoveLetterListReq) Size() (n int) {
	var l int
	_ = l
	if m.DateInt != 0 {
		n += 1 + sovActivitypresent(uint64(m.DateInt))
	}
	return n
}

func (m *GetLoveLetterListResp) Size() (n int) {
	var l int
	_ = l
	if m.Code != 0 {
		n += 1 + sovActivitypresent(uint64(m.Code))
	}
	if len(m.LoveLetterList) > 0 {
		for _, e := range m.LoveLetterList {
			l = e.Size()
			n += 1 + l + sovActivitypresent(uint64(l))
		}
	}
	return n
}

func (m *ActCommonAward) Size() (n int) {
	var l int
	_ = l
	if m.AwardType != 0 {
		n += 1 + sovActivitypresent(uint64(m.AwardType))
	}
	if m.AwardId != 0 {
		n += 1 + sovActivitypresent(uint64(m.AwardId))
	}
	l = len(m.StrAwardId)
	if l > 0 {
		n += 1 + l + sovActivitypresent(uint64(l))
	}
	if m.AwardNum != 0 {
		n += 1 + sovActivitypresent(uint64(m.AwardNum))
	}
	if m.AwardTime != 0 {
		n += 1 + sovActivitypresent(uint64(m.AwardTime))
	}
	if m.AwardLmt != 0 {
		n += 1 + sovActivitypresent(uint64(m.AwardLmt))
	}
	l = len(m.AwardVer)
	if l > 0 {
		n += 1 + l + sovActivitypresent(uint64(l))
	}
	return n
}

func (m *ConsumeRecord) Size() (n int) {
	var l int
	_ = l
	if m.Uid != 0 {
		n += 1 + sovActivitypresent(uint64(m.Uid))
	}
	if m.ConsumeValue != 0 {
		n += 1 + sovActivitypresent(uint64(m.ConsumeValue))
	}
	if m.Ts != 0 {
		n += 1 + sovActivitypresent(uint64(m.Ts))
	}
	if m.ActivityId != 0 {
		n += 1 + sovActivitypresent(uint64(m.ActivityId))
	}
	l = len(m.OrderId)
	if l > 0 {
		n += 1 + l + sovActivitypresent(uint64(l))
	}
	return n
}

func (m *ActBattleSubMsg) Size() (n int) {
	var l int
	_ = l
	if m.Type != 0 {
		n += 1 + sovActivitypresent(uint64(m.Type))
	}
	l = len(m.Content)
	if l > 0 {
		n += 1 + l + sovActivitypresent(uint64(l))
	}
	return n
}

func (m *ActBattleProgress) Size() (n int) {
	var l int
	_ = l
	if m.TimeTs != 0 {
		n += 1 + sovActivitypresent(uint64(m.TimeTs))
	}
	l = len(m.Msg)
	if l > 0 {
		n += 1 + l + sovActivitypresent(uint64(l))
	}
	if len(m.SubMsgList) > 0 {
		for _, e := range m.SubMsgList {
			l = e.Size()
			n += 1 + l + sovActivitypresent(uint64(l))
		}
	}
	return n
}

func (m *GetBattleProgressListReq) Size() (n int) {
	var l int
	_ = l
	if m.ActivityId != 0 {
		n += 1 + sovActivitypresent(uint64(m.ActivityId))
	}
	if m.ListType != 0 {
		n += 1 + sovActivitypresent(uint64(m.ListType))
	}
	if m.Offset != 0 {
		n += 1 + sovActivitypresent(uint64(m.Offset))
	}
	if m.Limit != 0 {
		n += 1 + sovActivitypresent(uint64(m.Limit))
	}
	return n
}

func (m *GetBattleProgressListResp) Size() (n int) {
	var l int
	_ = l
	if len(m.ProgressList) > 0 {
		for _, e := range m.ProgressList {
			l = e.Size()
			n += 1 + l + sovActivitypresent(uint64(l))
		}
	}
	return n
}

func (m *ModifyUserActivityInfoReq) Size() (n int) {
	var l int
	_ = l
	if m.Uid != 0 {
		n += 1 + sovActivitypresent(uint64(m.Uid))
	}
	if m.ActivityId != 0 {
		n += 1 + sovActivitypresent(uint64(m.ActivityId))
	}
	if m.OpType != 0 {
		n += 1 + sovActivitypresent(uint64(m.OpType))
	}
	if m.OpValue != 0 {
		n += 1 + sovActivitypresent(uint64(m.OpValue))
	}
	l = len(m.OpData)
	if l > 0 {
		n += 1 + l + sovActivitypresent(uint64(l))
	}
	return n
}

func (m *ModifyUserActivityInfoResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *DoTestReq) Size() (n int) {
	var l int
	_ = l
	if m.Uid != 0 {
		n += 1 + sovActivitypresent(uint64(m.Uid))
	}
	if m.ActivityId != 0 {
		n += 1 + sovActivitypresent(uint64(m.ActivityId))
	}
	if m.OpType != 0 {
		n += 1 + sovActivitypresent(uint64(m.OpType))
	}
	if m.OpValue != 0 {
		n += 1 + sovActivitypresent(uint64(m.OpValue))
	}
	l = len(m.OpData)
	if l > 0 {
		n += 1 + l + sovActivitypresent(uint64(l))
	}
	return n
}

func (m *DoTestResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *IdolInfo) Size() (n int) {
	var l int
	_ = l
	if m.Uid != 0 {
		n += 1 + sovActivitypresent(uint64(m.Uid))
	}
	if m.RankValue != 0 {
		n += 1 + sovActivitypresent(uint64(m.RankValue))
	}
	return n
}

func (m *IdolInfoList) Size() (n int) {
	var l int
	_ = l
	if len(m.IdolInfoList) > 0 {
		for _, e := range m.IdolInfoList {
			l = e.Size()
			n += 1 + l + sovActivitypresent(uint64(l))
		}
	}
	return n
}

func (m *GetIdolListReq) Size() (n int) {
	var l int
	_ = l
	if m.Offset != 0 {
		n += 1 + sovActivitypresent(uint64(m.Offset))
	}
	if m.Limit != 0 {
		n += 1 + sovActivitypresent(uint64(m.Limit))
	}
	return n
}

func (m *GetIdolListResp) Size() (n int) {
	var l int
	_ = l
	if m.Code != 0 {
		n += 1 + sovActivitypresent(uint64(m.Code))
	}
	if m.IdolInfoList != nil {
		l = m.IdolInfoList.Size()
		n += 1 + l + sovActivitypresent(uint64(l))
	}
	return n
}

func (m *GetPresentActivityExtentInfoReq) Size() (n int) {
	var l int
	_ = l
	if m.ActivityId != 0 {
		n += 1 + sovActivitypresent(uint64(m.ActivityId))
	}
	if m.ExtentInfoType != 0 {
		n += 1 + sovActivitypresent(uint64(m.ExtentInfoType))
	}
	return n
}

func (m *GetPresentActivityExtentInfoResp) Size() (n int) {
	var l int
	_ = l
	if m.Code != 0 {
		n += 1 + sovActivitypresent(uint64(m.Code))
	}
	l = len(m.ActivityExtentInfo)
	if l > 0 {
		n += 1 + l + sovActivitypresent(uint64(l))
	}
	return n
}

func (m *GetPresentActivityUserExtentInfoReq) Size() (n int) {
	var l int
	_ = l
	if m.ActivityId != 0 {
		n += 1 + sovActivitypresent(uint64(m.ActivityId))
	}
	if m.Uid != 0 {
		n += 1 + sovActivitypresent(uint64(m.Uid))
	}
	if m.ExtentInfoType != 0 {
		n += 1 + sovActivitypresent(uint64(m.ExtentInfoType))
	}
	l = len(m.ExtentParam)
	if l > 0 {
		n += 1 + l + sovActivitypresent(uint64(l))
	}
	return n
}

func (m *GetPresentActivityUserExtentInfoResp) Size() (n int) {
	var l int
	_ = l
	if m.Code != 0 {
		n += 1 + sovActivitypresent(uint64(m.Code))
	}
	l = len(m.UserExtentInfo)
	if l > 0 {
		n += 1 + l + sovActivitypresent(uint64(l))
	}
	return n
}

func (m *ActDailyRanking) Size() (n int) {
	var l int
	_ = l
	if len(m.RankList) > 0 {
		for _, e := range m.RankList {
			l = e.Size()
			n += 1 + l + sovActivitypresent(uint64(l))
		}
	}
	if m.Date != 0 {
		n += 1 + sovActivitypresent(uint64(m.Date))
	}
	return n
}

func (m *ActHistoryRanking) Size() (n int) {
	var l int
	_ = l
	if len(m.HistoryList) > 0 {
		for _, e := range m.HistoryList {
			l = e.Size()
			n += 1 + l + sovActivitypresent(uint64(l))
		}
	}
	if m.LastModifiedDate != 0 {
		n += 1 + sovActivitypresent(uint64(m.LastModifiedDate))
	}
	if m.DisableTodayList {
		n += 2
	}
	return n
}

func (m *LanternFestivalUserData) Size() (n int) {
	var l int
	_ = l
	if m.Uid != 0 {
		n += 1 + sovActivitypresent(uint64(m.Uid))
	}
	if m.ActivityId != 0 {
		n += 1 + sovActivitypresent(uint64(m.ActivityId))
	}
	if m.Phase != 0 {
		n += 1 + sovActivitypresent(uint64(m.Phase))
	}
	if m.Score != 0 {
		n += 1 + sovActivitypresent(uint64(m.Score))
	}
	if len(m.PresentList) > 0 {
		for _, e := range m.PresentList {
			l = e.Size()
			n += 1 + l + sovActivitypresent(uint64(l))
		}
	}
	if m.LastModifiedTs != 0 {
		n += 1 + sovActivitypresent(uint64(m.LastModifiedTs))
	}
	return n
}

func (m *WorldEarthDayUserData) Size() (n int) {
	var l int
	_ = l
	if m.Uid != 0 {
		n += 1 + sovActivitypresent(uint64(m.Uid))
	}
	if m.ActivityId != 0 {
		n += 1 + sovActivitypresent(uint64(m.ActivityId))
	}
	if len(m.HistoryRankList) > 0 {
		for _, e := range m.HistoryRankList {
			l = e.Size()
			n += 1 + l + sovActivitypresent(uint64(l))
		}
	}
	if m.LastModifiedDate != 0 {
		n += 1 + sovActivitypresent(uint64(m.LastModifiedDate))
	}
	if m.ExValue != 0 {
		n += 1 + sovActivitypresent(uint64(m.ExValue))
	}
	return n
}

func (m *MayDayUserData) Size() (n int) {
	var l int
	_ = l
	if m.Uid != 0 {
		n += 1 + sovActivitypresent(uint64(m.Uid))
	}
	if m.ActivityId != 0 {
		n += 1 + sovActivitypresent(uint64(m.ActivityId))
	}
	if m.Phase != 0 {
		n += 1 + sovActivitypresent(uint64(m.Phase))
	}
	if m.TotalScore != 0 {
		n += 1 + sovActivitypresent(uint64(m.TotalScore))
	}
	if m.TodayScore != 0 {
		n += 1 + sovActivitypresent(uint64(m.TodayScore))
	}
	if m.PresentValue != 0 {
		n += 1 + sovActivitypresent(uint64(m.PresentValue))
	}
	if m.LastModifiedTs != 0 {
		n += 1 + sovActivitypresent(uint64(m.LastModifiedTs))
	}
	return n
}

func (m *UserDailyInfoData) Size() (n int) {
	var l int
	_ = l
	if m.Uid != 0 {
		n += 1 + sovActivitypresent(uint64(m.Uid))
	}
	if m.ActivityId != 0 {
		n += 1 + sovActivitypresent(uint64(m.ActivityId))
	}
	if m.ListType != 0 {
		n += 1 + sovActivitypresent(uint64(m.ListType))
	}
	if m.LastModifiedDate != 0 {
		n += 1 + sovActivitypresent(uint64(m.LastModifiedDate))
	}
	if m.TodayRedDiamond != 0 {
		n += 1 + sovActivitypresent(uint64(m.TodayRedDiamond))
	}
	if m.TodayTbean != 0 {
		n += 1 + sovActivitypresent(uint64(m.TodayTbean))
	}
	return n
}

func (m *GetRankBlackUidListReq) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *GetRankBlackUidListResp) Size() (n int) {
	var l int
	_ = l
	if len(m.RecvRankUidList) > 0 {
		l = 0
		for _, e := range m.RecvRankUidList {
			l += sovActivitypresent(uint64(e))
		}
		n += 1 + sovActivitypresent(uint64(l)) + l
	}
	if len(m.SendRankUidList) > 0 {
		l = 0
		for _, e := range m.SendRankUidList {
			l += sovActivitypresent(uint64(e))
		}
		n += 1 + sovActivitypresent(uint64(l)) + l
	}
	return n
}

func sovActivitypresent(x uint64) (n int) {
	for {
		n++
		x >>= 7
		if x == 0 {
			break
		}
	}
	return n
}
func sozActivitypresent(x uint64) (n int) {
	return sovActivitypresent(uint64((x << 1) ^ uint64(int64(x) >> 63)))
}
func (m *StActivityPresentBriefConfig) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivitypresent
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: StActivityPresentBriefConfig: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: StActivityPresentBriefConfig: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemId", wireType)
			}
			m.ItemId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ItemId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Price", wireType)
			}
			m.Price = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Price |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PriceType", wireType)
			}
			m.PriceType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PriceType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Score", wireType)
			}
			m.Score = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Score |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Charm", wireType)
			}
			m.Charm = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Charm |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RichValue", wireType)
			}
			m.RichValue = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RichValue |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipActivitypresent(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivitypresent
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *StActivityPresent) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivitypresent
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: StActivityPresent: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: StActivityPresent: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemId", wireType)
			}
			m.ItemId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ItemId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IconUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthActivitypresent
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.IconUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ActivityId", wireType)
			}
			m.ActivityId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ActivityId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Extend", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthActivitypresent
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Extend = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ShowIndex", wireType)
			}
			m.ShowIndex = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ShowIndex |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipActivitypresent(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivitypresent
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *StPresentCount) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivitypresent
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: StPresentCount: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: StPresentCount: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemId", wireType)
			}
			m.ItemId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ItemId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Count", wireType)
			}
			m.Count = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Count |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ListType", wireType)
			}
			m.ListType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ListType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipActivitypresent(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivitypresent
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *StIntKeyValue) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivitypresent
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: StIntKeyValue: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: StIntKeyValue: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Key", wireType)
			}
			m.Key = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Key |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Value", wireType)
			}
			m.Value = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Value |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipActivitypresent(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivitypresent
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *StStrKeyValue) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivitypresent
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: StStrKeyValue: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: StStrKeyValue: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Key", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthActivitypresent
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Key = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Value", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthActivitypresent
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Value = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipActivitypresent(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivitypresent
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *StActivityListDetail) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivitypresent
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: StActivityListDetail: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: StActivityListDetail: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ListType", wireType)
			}
			m.ListType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ListType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthActivitypresent
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ItemList = append(m.ItemList, &StPresentCount{})
			if err := m.ItemList[len(m.ItemList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipActivitypresent(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivitypresent
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetPresentActivityInfoReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivitypresent
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetPresentActivityInfoReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetPresentActivityInfoReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ActivityId", wireType)
			}
			m.ActivityId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ActivityId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipActivitypresent(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivitypresent
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetPresentActivityInfoResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivitypresent
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetPresentActivityInfoResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetPresentActivityInfoResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Title", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthActivitypresent
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Title = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BannerUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthActivitypresent
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.BannerUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BeginTime", wireType)
			}
			m.BeginTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BeginTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field EndTime", wireType)
			}
			m.EndTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.EndTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PresentList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthActivitypresent
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.PresentList = append(m.PresentList, &StActivityPresent{})
			if err := m.PresentList[len(m.PresentList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ActivityId", wireType)
			}
			m.ActivityId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ActivityId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DetailList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthActivitypresent
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.DetailList = append(m.DetailList, &StActivityListDetail{})
			if err := m.DetailList[len(m.DetailList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 8:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BroadcastImgUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthActivitypresent
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.BroadcastImgUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 9:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field JumpUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthActivitypresent
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.JumpUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 10:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BackImgUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthActivitypresent
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.BackImgUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 11:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IconImgUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthActivitypresent
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.IconImgUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 12:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ActivityExtentInfo", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthActivitypresent
			}
			postIndex := iNdEx + byteLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ActivityExtentInfo = append(m.ActivityExtentInfo[:0], dAtA[iNdEx:postIndex]...)
			if m.ActivityExtentInfo == nil {
				m.ActivityExtentInfo = []byte{}
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipActivitypresent(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivitypresent
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *StPresentActUserFansInfo) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivitypresent
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: StPresentActUserFansInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: StPresentActUserFansInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RankValue", wireType)
			}
			m.RankValue = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RankValue |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Ranking", wireType)
			}
			m.Ranking = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Ranking |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipActivitypresent(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivitypresent
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *StPresentActUserExtentInfo) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivitypresent
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: StPresentActUserExtentInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: StPresentActUserExtentInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ActivityId", wireType)
			}
			m.ActivityId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ActivityId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ListType", wireType)
			}
			m.ListType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ListType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Ranking", wireType)
			}
			m.Ranking = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Ranking |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Extent", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthActivitypresent
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Extent = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipActivitypresent(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivitypresent
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *StPresentActUserRankInfo) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivitypresent
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: StPresentActUserRankInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: StPresentActUserRankInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemId", wireType)
			}
			m.ItemId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ItemId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ListType", wireType)
			}
			m.ListType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ListType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RankValue", wireType)
			}
			m.RankValue = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RankValue |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Ranking", wireType)
			}
			m.Ranking = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Ranking |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ActivityId", wireType)
			}
			m.ActivityId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ActivityId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 8:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FansList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthActivitypresent
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.FansList = append(m.FansList, &StPresentActUserFansInfo{})
			if err := m.FansList[len(m.FansList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipActivitypresent(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivitypresent
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetPresentActivityRankingListReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivitypresent
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetPresentActivityRankingListReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetPresentActivityRankingListReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemId", wireType)
			}
			m.ItemId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ItemId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ListType", wireType)
			}
			m.ListType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ListType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Offset", wireType)
			}
			m.Offset = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Offset |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Limit", wireType)
			}
			m.Limit = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Limit |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ActivityId", wireType)
			}
			m.ActivityId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ActivityId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipActivitypresent(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivitypresent
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetPresentActivityRankingListResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivitypresent
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetPresentActivityRankingListResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetPresentActivityRankingListResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RankList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthActivitypresent
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.RankList = append(m.RankList, &StPresentActUserRankInfo{})
			if err := m.RankList[len(m.RankList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TotalCount", wireType)
			}
			m.TotalCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TotalCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipActivitypresent(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivitypresent
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetPresentActivityUserInfoReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivitypresent
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetPresentActivityUserInfoReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetPresentActivityUserInfoReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ActivityId", wireType)
			}
			m.ActivityId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ActivityId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ListType", wireType)
			}
			m.ListType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ListType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipActivitypresent(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivitypresent
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetPresentActivityUserInfoResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivitypresent
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetPresentActivityUserInfoResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetPresentActivityUserInfoResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UserInfo", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthActivitypresent
			}
			postIndex := iNdEx + byteLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.UserInfo = append(m.UserInfo[:0], dAtA[iNdEx:postIndex]...)
			if m.UserInfo == nil {
				m.UserInfo = []byte{}
			}
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ConsumeValue", wireType)
			}
			m.ConsumeValue = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ConsumeValue |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ExValue", wireType)
			}
			m.ExValue = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ExValue |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ExValue2", wireType)
			}
			m.ExValue2 = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ExValue2 |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UserInfo2", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthActivitypresent
			}
			postIndex := iNdEx + byteLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.UserInfo2 = append(m.UserInfo2[:0], dAtA[iNdEx:postIndex]...)
			if m.UserInfo2 == nil {
				m.UserInfo2 = []byte{}
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipActivitypresent(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivitypresent
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetPresentActivityRankingByUidReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivitypresent
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetPresentActivityRankingByUidReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetPresentActivityRankingByUidReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemId", wireType)
			}
			m.ItemId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ItemId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ListType", wireType)
			}
			m.ListType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ListType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ActivityId", wireType)
			}
			m.ActivityId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ActivityId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GetUpper", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.GetUpper = bool(v != 0)
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GetFollow", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.GetFollow = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipActivitypresent(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivitypresent
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetPresentActivityRankingByUidResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivitypresent
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetPresentActivityRankingByUidResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetPresentActivityRankingByUidResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RankInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthActivitypresent
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.RankInfo == nil {
				m.RankInfo = &StPresentActUserRankInfo{}
			}
			if err := m.RankInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UpperRankInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthActivitypresent
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.UpperRankInfo == nil {
				m.UpperRankInfo = &StPresentActUserRankInfo{}
			}
			if err := m.UpperRankInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FollowRankInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthActivitypresent
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.FollowRankInfo == nil {
				m.FollowRankInfo = &StPresentActUserRankInfo{}
			}
			if err := m.FollowRankInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipActivitypresent(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivitypresent
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *RecordActivitySendPresentReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivitypresent
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: RecordActivitySendPresentReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: RecordActivitySendPresentReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TargetUid", wireType)
			}
			m.TargetUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TargetUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OrderId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthActivitypresent
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.OrderId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemConfig", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthActivitypresent
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.ItemConfig == nil {
				m.ItemConfig = &StActivityPresentBriefConfig{}
			}
			if err := m.ItemConfig.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemCount", wireType)
			}
			m.ItemCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ItemCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PresentFromType", wireType)
			}
			m.PresentFromType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PresentFromType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SendTime", wireType)
			}
			m.SendTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SendTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 8:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 9:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelType", wireType)
			}
			m.ChannelType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipActivitypresent(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivitypresent
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *RecalculateUserActivityInfoReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivitypresent
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: RecalculateUserActivityInfoReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: RecalculateUserActivityInfoReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ActivityId", wireType)
			}
			m.ActivityId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ActivityId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ListType", wireType)
			}
			m.ListType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ListType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipActivitypresent(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivitypresent
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *RecalculateActivityInfoReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivitypresent
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: RecalculateActivityInfoReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: RecalculateActivityInfoReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ActivityId", wireType)
			}
			m.ActivityId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ActivityId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ListType", wireType)
			}
			m.ListType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ListType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipActivitypresent(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivitypresent
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *NotifyActivityPhaseChangeReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivitypresent
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: NotifyActivityPhaseChangeReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: NotifyActivityPhaseChangeReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Type", wireType)
			}
			m.Type = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Type |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipActivitypresent(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivitypresent
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *LoveLetterInfo) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivitypresent
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: LoveLetterInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: LoveLetterInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SendUid", wireType)
			}
			m.SendUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SendUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TargetUid", wireType)
			}
			m.TargetUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TargetUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LetterStatus", wireType)
			}
			m.LetterStatus = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LetterStatus |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LetterType", wireType)
			}
			m.LetterType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LetterType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LetterText", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthActivitypresent
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.LetterText = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DateInt", wireType)
			}
			m.DateInt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.DateInt |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LetterId", wireType)
			}
			m.LetterId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LetterId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipActivitypresent(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivitypresent
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *LoveLetterInfoList) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivitypresent
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: LoveLetterInfoList: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: LoveLetterInfoList: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LoveLetterList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthActivitypresent
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.LoveLetterList = append(m.LoveLetterList, &LoveLetterInfo{})
			if err := m.LoveLetterList[len(m.LoveLetterList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipActivitypresent(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivitypresent
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ModifyLoveLetterReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivitypresent
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: modifyLoveLetterReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: modifyLoveLetterReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LetterId", wireType)
			}
			m.LetterId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LetterId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SendUid", wireType)
			}
			m.SendUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SendUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TargetUid", wireType)
			}
			m.TargetUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TargetUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ActivityId", wireType)
			}
			m.ActivityId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ActivityId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DateInt", wireType)
			}
			m.DateInt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.DateInt |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LetterType", wireType)
			}
			m.LetterType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LetterType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LetterText", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthActivitypresent
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.LetterText = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipActivitypresent(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivitypresent
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ModifyLoveLetterResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivitypresent
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: modifyLoveLetterResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: modifyLoveLetterResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Code", wireType)
			}
			m.Code = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Code |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field NewContent", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthActivitypresent
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.NewContent = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipActivitypresent(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivitypresent
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetLoveLetterListReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivitypresent
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetLoveLetterListReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetLoveLetterListReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DateInt", wireType)
			}
			m.DateInt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.DateInt |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipActivitypresent(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivitypresent
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetLoveLetterListResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivitypresent
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetLoveLetterListResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetLoveLetterListResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Code", wireType)
			}
			m.Code = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Code |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LoveLetterList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthActivitypresent
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.LoveLetterList = append(m.LoveLetterList, &LoveLetterInfo{})
			if err := m.LoveLetterList[len(m.LoveLetterList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipActivitypresent(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivitypresent
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ActCommonAward) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivitypresent
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ActCommonAward: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ActCommonAward: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AwardType", wireType)
			}
			m.AwardType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.AwardType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AwardId", wireType)
			}
			m.AwardId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.AwardId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field StrAwardId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthActivitypresent
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.StrAwardId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AwardNum", wireType)
			}
			m.AwardNum = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.AwardNum |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AwardTime", wireType)
			}
			m.AwardTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.AwardTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AwardLmt", wireType)
			}
			m.AwardLmt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.AwardLmt |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AwardVer", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthActivitypresent
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.AwardVer = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipActivitypresent(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivitypresent
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ConsumeRecord) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivitypresent
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ConsumeRecord: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ConsumeRecord: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ConsumeValue", wireType)
			}
			m.ConsumeValue = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ConsumeValue |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Ts", wireType)
			}
			m.Ts = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Ts |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ActivityId", wireType)
			}
			m.ActivityId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ActivityId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OrderId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthActivitypresent
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.OrderId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipActivitypresent(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivitypresent
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ActBattleSubMsg) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivitypresent
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ActBattleSubMsg: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ActBattleSubMsg: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Type", wireType)
			}
			m.Type = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Type |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Content", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthActivitypresent
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Content = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipActivitypresent(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivitypresent
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ActBattleProgress) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivitypresent
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ActBattleProgress: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ActBattleProgress: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TimeTs", wireType)
			}
			m.TimeTs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TimeTs |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Msg", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthActivitypresent
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Msg = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SubMsgList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthActivitypresent
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.SubMsgList = append(m.SubMsgList, &ActBattleSubMsg{})
			if err := m.SubMsgList[len(m.SubMsgList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipActivitypresent(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivitypresent
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetBattleProgressListReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivitypresent
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetBattleProgressListReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetBattleProgressListReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ActivityId", wireType)
			}
			m.ActivityId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ActivityId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ListType", wireType)
			}
			m.ListType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ListType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Offset", wireType)
			}
			m.Offset = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Offset |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Limit", wireType)
			}
			m.Limit = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Limit |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipActivitypresent(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivitypresent
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetBattleProgressListResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivitypresent
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetBattleProgressListResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetBattleProgressListResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ProgressList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthActivitypresent
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ProgressList = append(m.ProgressList, &ActBattleProgress{})
			if err := m.ProgressList[len(m.ProgressList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipActivitypresent(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivitypresent
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ModifyUserActivityInfoReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivitypresent
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ModifyUserActivityInfoReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ModifyUserActivityInfoReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ActivityId", wireType)
			}
			m.ActivityId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ActivityId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OpType", wireType)
			}
			m.OpType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OpType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OpValue", wireType)
			}
			m.OpValue = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OpValue |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OpData", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthActivitypresent
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.OpData = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipActivitypresent(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivitypresent
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ModifyUserActivityInfoResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivitypresent
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ModifyUserActivityInfoResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ModifyUserActivityInfoResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipActivitypresent(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivitypresent
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DoTestReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivitypresent
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: DoTestReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: DoTestReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ActivityId", wireType)
			}
			m.ActivityId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ActivityId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OpType", wireType)
			}
			m.OpType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OpType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OpValue", wireType)
			}
			m.OpValue = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OpValue |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OpData", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthActivitypresent
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.OpData = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipActivitypresent(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivitypresent
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DoTestResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivitypresent
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: DoTestResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: DoTestResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipActivitypresent(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivitypresent
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *IdolInfo) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivitypresent
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: IdolInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: IdolInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RankValue", wireType)
			}
			m.RankValue = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RankValue |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipActivitypresent(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivitypresent
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *IdolInfoList) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivitypresent
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: IdolInfoList: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: IdolInfoList: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IdolInfoList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthActivitypresent
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.IdolInfoList = append(m.IdolInfoList, &IdolInfo{})
			if err := m.IdolInfoList[len(m.IdolInfoList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipActivitypresent(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivitypresent
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetIdolListReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivitypresent
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetIdolListReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetIdolListReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Offset", wireType)
			}
			m.Offset = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Offset |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Limit", wireType)
			}
			m.Limit = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Limit |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipActivitypresent(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivitypresent
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetIdolListResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivitypresent
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetIdolListResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetIdolListResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Code", wireType)
			}
			m.Code = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Code |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IdolInfoList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthActivitypresent
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.IdolInfoList == nil {
				m.IdolInfoList = &IdolInfoList{}
			}
			if err := m.IdolInfoList.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipActivitypresent(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivitypresent
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetPresentActivityExtentInfoReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivitypresent
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetPresentActivityExtentInfoReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetPresentActivityExtentInfoReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ActivityId", wireType)
			}
			m.ActivityId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ActivityId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ExtentInfoType", wireType)
			}
			m.ExtentInfoType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ExtentInfoType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipActivitypresent(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivitypresent
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetPresentActivityExtentInfoResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivitypresent
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetPresentActivityExtentInfoResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetPresentActivityExtentInfoResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Code", wireType)
			}
			m.Code = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Code |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ActivityExtentInfo", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthActivitypresent
			}
			postIndex := iNdEx + byteLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ActivityExtentInfo = append(m.ActivityExtentInfo[:0], dAtA[iNdEx:postIndex]...)
			if m.ActivityExtentInfo == nil {
				m.ActivityExtentInfo = []byte{}
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipActivitypresent(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivitypresent
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetPresentActivityUserExtentInfoReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivitypresent
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetPresentActivityUserExtentInfoReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetPresentActivityUserExtentInfoReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ActivityId", wireType)
			}
			m.ActivityId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ActivityId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ExtentInfoType", wireType)
			}
			m.ExtentInfoType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ExtentInfoType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ExtentParam", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthActivitypresent
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ExtentParam = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipActivitypresent(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivitypresent
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetPresentActivityUserExtentInfoResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivitypresent
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetPresentActivityUserExtentInfoResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetPresentActivityUserExtentInfoResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Code", wireType)
			}
			m.Code = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Code |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UserExtentInfo", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthActivitypresent
			}
			postIndex := iNdEx + byteLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.UserExtentInfo = append(m.UserExtentInfo[:0], dAtA[iNdEx:postIndex]...)
			if m.UserExtentInfo == nil {
				m.UserExtentInfo = []byte{}
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipActivitypresent(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivitypresent
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ActDailyRanking) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivitypresent
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ActDailyRanking: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ActDailyRanking: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RankList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthActivitypresent
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.RankList = append(m.RankList, &StPresentActUserRankInfo{})
			if err := m.RankList[len(m.RankList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Date", wireType)
			}
			m.Date = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Date |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipActivitypresent(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivitypresent
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ActHistoryRanking) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivitypresent
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ActHistoryRanking: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ActHistoryRanking: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field HistoryList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthActivitypresent
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.HistoryList = append(m.HistoryList, &ActDailyRanking{})
			if err := m.HistoryList[len(m.HistoryList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LastModifiedDate", wireType)
			}
			m.LastModifiedDate = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LastModifiedDate |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DisableTodayList", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.DisableTodayList = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipActivitypresent(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivitypresent
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *LanternFestivalUserData) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivitypresent
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: LanternFestivalUserData: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: LanternFestivalUserData: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ActivityId", wireType)
			}
			m.ActivityId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ActivityId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Phase", wireType)
			}
			m.Phase = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Phase |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Score", wireType)
			}
			m.Score = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Score |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PresentList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthActivitypresent
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.PresentList = append(m.PresentList, &StPresentCount{})
			if err := m.PresentList[len(m.PresentList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LastModifiedTs", wireType)
			}
			m.LastModifiedTs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LastModifiedTs |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipActivitypresent(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivitypresent
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *WorldEarthDayUserData) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivitypresent
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: WorldEarthDayUserData: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: WorldEarthDayUserData: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ActivityId", wireType)
			}
			m.ActivityId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ActivityId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field HistoryRankList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthActivitypresent
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.HistoryRankList = append(m.HistoryRankList, &StIntKeyValue{})
			if err := m.HistoryRankList[len(m.HistoryRankList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LastModifiedDate", wireType)
			}
			m.LastModifiedDate = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LastModifiedDate |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ExValue", wireType)
			}
			m.ExValue = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ExValue |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipActivitypresent(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivitypresent
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *MayDayUserData) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivitypresent
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: MayDayUserData: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: MayDayUserData: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ActivityId", wireType)
			}
			m.ActivityId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ActivityId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Phase", wireType)
			}
			m.Phase = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Phase |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TotalScore", wireType)
			}
			m.TotalScore = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TotalScore |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TodayScore", wireType)
			}
			m.TodayScore = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TodayScore |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PresentValue", wireType)
			}
			m.PresentValue = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PresentValue |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LastModifiedTs", wireType)
			}
			m.LastModifiedTs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LastModifiedTs |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipActivitypresent(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivitypresent
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UserDailyInfoData) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivitypresent
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UserDailyInfoData: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UserDailyInfoData: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ActivityId", wireType)
			}
			m.ActivityId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ActivityId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ListType", wireType)
			}
			m.ListType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ListType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LastModifiedDate", wireType)
			}
			m.LastModifiedDate = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LastModifiedDate |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TodayRedDiamond", wireType)
			}
			m.TodayRedDiamond = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TodayRedDiamond |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TodayTbean", wireType)
			}
			m.TodayTbean = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TodayTbean |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipActivitypresent(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivitypresent
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetRankBlackUidListReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivitypresent
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetRankBlackUidListReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetRankBlackUidListReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipActivitypresent(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivitypresent
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetRankBlackUidListResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivitypresent
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetRankBlackUidListResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetRankBlackUidListResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowActivitypresent
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.RecvRankUidList = append(m.RecvRankUidList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowActivitypresent
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthActivitypresent
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowActivitypresent
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.RecvRankUidList = append(m.RecvRankUidList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field RecvRankUidList", wireType)
			}
		case 2:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowActivitypresent
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.SendRankUidList = append(m.SendRankUidList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowActivitypresent
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthActivitypresent
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowActivitypresent
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.SendRankUidList = append(m.SendRankUidList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field SendRankUidList", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipActivitypresent(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivitypresent
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func skipActivitypresent(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowActivitypresent
			}
			if iNdEx >= l {
				return 0, io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
			return iNdEx, nil
		case 1:
			iNdEx += 8
			return iNdEx, nil
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowActivitypresent
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			iNdEx += length
			if length < 0 {
				return 0, ErrInvalidLengthActivitypresent
			}
			return iNdEx, nil
		case 3:
			for {
				var innerWire uint64
				var start = iNdEx
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return 0, ErrIntOverflowActivitypresent
					}
					if iNdEx >= l {
						return 0, io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					innerWire |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				innerWireType := int(innerWire & 0x7)
				if innerWireType == 4 {
					break
				}
				next, err := skipActivitypresent(dAtA[start:])
				if err != nil {
					return 0, err
				}
				iNdEx = start + next
			}
			return iNdEx, nil
		case 4:
			return iNdEx, nil
		case 5:
			iNdEx += 4
			return iNdEx, nil
		default:
			return 0, fmt2.Errorf("proto: illegal wireType %d", wireType)
		}
	}
	panic("unreachable")
}

var (
	ErrInvalidLengthActivitypresent = fmt2.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowActivitypresent   = fmt2.Errorf("proto: integer overflow")
)

func init() {
	proto.RegisterFile("services/activitypresent/activitypresent.proto", fileDescriptorActivitypresent)
}

var fileDescriptorActivitypresent = []byte{
	// 4700 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xcc, 0x5b, 0x4b, 0x6c, 0x24, 0x49,
	0x5a, 0xee, 0x2c, 0xbf, 0xaa, 0x7e, 0x97, 0xed, 0x70, 0xf4, 0xcb, 0xae, 0x7e, 0x55, 0x67, 0xf7,
	0xcc, 0xf6, 0xb8, 0xb7, 0xdd, 0xd3, 0x9e, 0x1d, 0x66, 0xf1, 0x7a, 0x8d, 0xda, 0x76, 0xb7, 0xdb,
	0xd3, 0x76, 0x77, 0x6f, 0xd9, 0xdd, 0xc3, 0xcc, 0xee, 0x92, 0x0a, 0x57, 0x86, 0xcb, 0xb9, 0xce,
	0xca, 0xcc, 0xc9, 0x8c, 0xb2, 0xab, 0xe0, 0xc0, 0x80, 0x10, 0xa0, 0xdd, 0x3d, 0x8c, 0x56, 0x7b,
	0x58, 0x21, 0xde, 0x3b, 0x0b, 0x02, 0x24, 0x16, 0x09, 0x10, 0xe2, 0x21, 0xce, 0xcb, 0x8d, 0x3b,
	0x12, 0x8b, 0x86, 0x15, 0x1a, 0x90, 0xb8, 0x70, 0x01, 0x71, 0x42, 0xf1, 0x47, 0xbe, 0xaa, 0x2a,
	0xeb, 0xe1, 0x9e, 0x39, 0x70, 0xb2, 0xe3, 0xff, 0xbf, 0x88, 0xf8, 0x5f, 0xf1, 0xc7, 0x1f, 0x91,
	0x51, 0xb0, 0x18, 0x70, 0xff, 0xd8, 0xaa, 0xf2, 0xe0, 0x2e, 0xab, 0x0a, 0xeb, 0xd8, 0x12, 0x2d,
	0xcf, 0xe7, 0x01, 0x77, 0x44, 0x67, 0x7b, 0xd1, 0xf3, 0x5d, 0xe1, 0xd2, 0x99, 0x0e, 0x72, 0xe9,
	0x66, 0xd5, 0xad, 0xd7, 0x5d, 0xe7, 0xae, 0xb0, 0x8f, 0x3d, 0xab, 0x7a, 0x64, 0xf3, 0xbb, 0xc1,
	0xd1, 0x7e, 0xc3, 0xb2, 0x85, 0xe5, 0x88, 0x96, 0xc7, 0x55, 0x37, 0xfd, 0xaf, 0x34, 0xb8, 0xbc,
	0x2b, 0xee, 0x87, 0x7d, 0x9f, 0xa9, 0xbe, 0x6b, 0xbe, 0xc5, 0x0f, 0xd6, 0x5d, 0xe7, 0xc0, 0xaa,
	0xd1, 0x8b, 0x30, 0x61, 0x09, 0x5e, 0x37, 0x2c, 0x73, 0x4e, 0x2b, 0x6b, 0xb7, 0xa6, 0x2a, 0xe3,
	0xb2, 0xb9, 0x65, 0xd2, 0x73, 0x30, 0xe6, 0xf9, 0x56, 0x95, 0xcf, 0xe5, 0x90, 0xac, 0x1a, 0xf4,
	0x0a, 0x00, 0xfe, 0x63, 0xc8, 0x39, 0xe6, 0x46, 0x90, 0x55, 0x40, 0xca, 0x5e, 0xcb, 0xe3, 0xb2,
	0x53, 0x50, 0x75, 0x7d, 0x3e, 0x37, 0xaa, 0x3a, 0x61, 0x43, 0x52, 0xab, 0x87, 0xcc, 0xaf, 0xcf,
	0x8d, 0x29, 0x2a, 0x36, 0xe4, 0x50, 0xbe, 0x55, 0x3d, 0x34, 0x8e, 0x99, 0xdd, 0xe0, 0x73, 0xe3,
	0x6a, 0x28, 0x49, 0x79, 0x21, 0x09, 0xfa, 0xef, 0x68, 0x30, 0xdb, 0x25, 0x79, 0x6f, 0x71, 0xe7,
	0x21, 0x6f, 0x55, 0x5d, 0xc7, 0x68, 0xf8, 0x36, 0x4a, 0x5c, 0xa8, 0x4c, 0xc8, 0xf6, 0x73, 0xdf,
	0xa6, 0xd7, 0x60, 0x32, 0x32, 0x9e, 0xec, 0xa7, 0x84, 0x86, 0x88, 0xb4, 0x65, 0xd2, 0x0b, 0x30,
	0xce, 0x9b, 0x82, 0x3b, 0x26, 0x8a, 0x5d, 0xa8, 0x84, 0x2d, 0x29, 0x61, 0x70, 0xe8, 0x9e, 0x18,
	0x96, 0x63, 0xf2, 0x66, 0x28, 0x7c, 0x41, 0x52, 0xb6, 0x24, 0x41, 0xff, 0x1a, 0x4c, 0xef, 0x8a,
	0x50, 0xb0, 0x75, 0xb7, 0xd1, 0x4f, 0x3a, 0x69, 0x01, 0x89, 0x88, 0x8c, 0x89, 0x0d, 0x7a, 0x09,
	0x0a, 0xb6, 0x15, 0x88, 0xb4, 0x2d, 0xf3, 0x92, 0x20, 0x4d, 0xa9, 0xbf, 0x05, 0x53, 0xbb, 0x62,
	0xcb, 0x11, 0x8f, 0x79, 0x0b, 0x0d, 0x42, 0x09, 0x8c, 0x1c, 0xf1, 0x56, 0x38, 0xb0, 0xfc, 0x57,
	0x8e, 0xaa, 0x8c, 0x17, 0x8e, 0x8a, 0x0d, 0xd5, 0x71, 0x57, 0xf8, 0x59, 0x1d, 0x0b, 0x19, 0x1d,
	0x0b, 0x51, 0xc7, 0xf7, 0xe1, 0x5c, 0x62, 0xf0, 0x6d, 0x2b, 0x10, 0x1b, 0x5c, 0x30, 0xcb, 0x6e,
	0x17, 0x53, 0x6b, 0x17, 0x93, 0xae, 0x40, 0x01, 0x55, 0x96, 0x84, 0xb9, 0x5c, 0x79, 0xe4, 0xd6,
	0xe4, 0xd2, 0xb5, 0xc5, 0xce, 0x10, 0x6e, 0x37, 0x53, 0x25, 0x2f, 0x7b, 0xc8, 0x09, 0xf4, 0x15,
	0x98, 0xdf, 0xe4, 0x11, 0x33, 0x9a, 0x7a, 0xcb, 0x39, 0x70, 0x2b, 0xfc, 0xfd, 0x4e, 0xbf, 0x69,
	0x9d, 0x7e, 0xd3, 0xff, 0x67, 0x04, 0x4a, 0xbd, 0xba, 0x07, 0x9e, 0xd4, 0x52, 0x58, 0xc2, 0xe6,
	0xa1, 0xe6, 0xaa, 0x21, 0x9d, 0xba, 0xcf, 0x1c, 0x87, 0xfb, 0xa9, 0x50, 0x29, 0x28, 0x8a, 0x0c,
	0x16, 0xc9, 0xe6, 0x35, 0xcb, 0x31, 0x84, 0x55, 0x8f, 0x03, 0x1c, 0x29, 0x7b, 0x56, 0x9d, 0xcb,
	0x30, 0xe3, 0x8e, 0xa9, 0x98, 0x2a, 0xc6, 0x27, 0xb8, 0x63, 0x22, 0xeb, 0x01, 0x14, 0x43, 0x7d,
	0x95, 0x31, 0xc6, 0xd0, 0x18, 0x7a, 0x86, 0x31, 0x3a, 0x82, 0xba, 0x32, 0x19, 0xb2, 0xa4, 0x49,
	0x3a, 0xb5, 0x1e, 0xef, 0x8a, 0xd6, 0x87, 0x30, 0x69, 0xa2, 0x63, 0xd4, 0x34, 0x13, 0x38, 0xcd,
	0x2b, 0x7d, 0xa6, 0x49, 0x5c, 0x59, 0x01, 0xd5, 0x13, 0x27, 0x5a, 0x80, 0xd9, 0x7d, 0xdf, 0x65,
	0x66, 0x95, 0x05, 0xc2, 0xb0, 0xea, 0x35, 0xb4, 0x47, 0x1e, 0xed, 0x31, 0x13, 0x33, 0xb6, 0xea,
	0x35, 0x69, 0x95, 0x79, 0xc8, 0x7f, 0xa3, 0x51, 0xf7, 0x10, 0x52, 0x50, 0xab, 0x4b, 0xb6, 0x25,
	0xab, 0x0c, 0xc5, 0x7d, 0x56, 0x3d, 0x8a, 0x47, 0x00, 0x64, 0x83, 0xa4, 0x85, 0x9d, 0xcb, 0x50,
	0xc4, 0xa5, 0x19, 0x21, 0x26, 0x15, 0x42, 0xd2, 0x42, 0xc4, 0xeb, 0x70, 0x2e, 0xd6, 0x19, 0xd7,
	0x9e, 0x30, 0x2c, 0xe7, 0xc0, 0x9d, 0x2b, 0x96, 0xb5, 0x5b, 0xc5, 0x0a, 0x8d, 0x78, 0x0f, 0x90,
	0x25, 0xfd, 0xab, 0x73, 0x98, 0xdb, 0x4d, 0x39, 0xfe, 0x79, 0xc0, 0xfd, 0x87, 0xcc, 0x09, 0x24,
	0x4f, 0xc6, 0x7b, 0x23, 0x8e, 0x17, 0xf9, 0x2f, 0xa6, 0x1a, 0xe6, 0x1c, 0x19, 0xe9, 0xd5, 0x52,
	0x90, 0x14, 0xb5, 0x40, 0xe6, 0x60, 0x42, 0x36, 0x2c, 0xa7, 0x16, 0x3a, 0x3c, 0x6a, 0xea, 0xbf,
	0xad, 0x41, 0xa9, 0x73, 0x9e, 0x44, 0x8a, 0x8c, 0x99, 0x3a, 0xbc, 0x97, 0xeb, 0xf2, 0x5e, 0xbf,
	0x35, 0x9f, 0x16, 0x64, 0xb4, 0x4d, 0x90, 0x38, 0x45, 0x09, 0x4c, 0x43, 0x51, 0x8a, 0x12, 0xfa,
	0xf7, 0x73, 0xdd, 0x86, 0xa8, 0x30, 0xe7, 0xa8, 0x87, 0x78, 0xa9, 0x04, 0x95, 0x6b, 0x4b, 0x50,
	0x7d, 0xc5, 0x6a, 0x37, 0xdf, 0x68, 0x1f, 0xf3, 0x8d, 0xb5, 0x4b, 0x3d, 0x30, 0x96, 0xaf, 0x00,
	0x54, 0x0f, 0xe5, 0xda, 0xb3, 0x25, 0x7f, 0x42, 0x8d, 0x1c, 0x52, 0x30, 0xd4, 0x0b, 0x07, 0xcc,
	0x09, 0x54, 0xa0, 0xe7, 0x31, 0xd0, 0x5f, 0xeb, 0x9d, 0x5c, 0x3a, 0xe2, 0xa0, 0x92, 0x97, 0x7d,
	0x31, 0xcd, 0xfc, 0xa1, 0x06, 0xe5, 0xee, 0x44, 0x51, 0x51, 0x52, 0x4a, 0x84, 0x4c, 0x37, 0x3d,
	0x93, 0x77, 0x9b, 0x6d, 0x72, 0x1d, 0xb6, 0xb9, 0x00, 0xe3, 0xee, 0xc1, 0x41, 0xc0, 0x45, 0x68,
	0xb5, 0xb0, 0x25, 0x93, 0x8f, 0x6d, 0xd5, 0x2d, 0x11, 0xed, 0x84, 0xd8, 0xe8, 0x34, 0xc8, 0x58,
	0x57, 0x4a, 0xfb, 0xb6, 0x06, 0xd7, 0x07, 0x48, 0x1a, 0x78, 0xd2, 0x2e, 0xe8, 0x10, 0xb4, 0x8b,
	0x36, 0xa4, 0x5d, 0xa2, 0xb0, 0xa8, 0xe4, 0x65, 0xdf, 0x28, 0xd7, 0x08, 0x57, 0x30, 0xdb, 0x48,
	0x6f, 0x4e, 0x80, 0x24, 0xcc, 0xd4, 0xba, 0x0b, 0x57, 0xba, 0xa5, 0x91, 0x83, 0x45, 0x39, 0xfa,
	0x33, 0x5e, 0x01, 0xfa, 0x5f, 0x6b, 0x70, 0xb5, 0xdf, 0x8c, 0x81, 0x27, 0xfb, 0x37, 0x02, 0xee,
	0xab, 0x0c, 0xa1, 0x61, 0x86, 0xc8, 0x37, 0x42, 0x00, 0xbd, 0x01, 0x53, 0x55, 0xd7, 0x09, 0x1a,
	0x75, 0xde, 0xb6, 0xd8, 0x8b, 0x21, 0x51, 0x05, 0xac, 0x4c, 0xe2, 0xcd, 0x90, 0x1f, 0x2e, 0x78,
	0xde, 0x54, 0xac, 0x4b, 0x50, 0x88, 0x58, 0x4b, 0xa1, 0xeb, 0xf2, 0x21, 0x6f, 0x49, 0x46, 0x6b,
	0x3c, 0xf3, 0x12, 0x3a, 0xaf, 0x58, 0x29, 0x44, 0x53, 0x2f, 0xe9, 0x3f, 0xea, 0xe7, 0xbb, 0xb5,
	0xd6, 0x73, 0xcb, 0xcc, 0xb6, 0xd8, 0xcb, 0x2d, 0xca, 0x0e, 0x3b, 0x8f, 0x66, 0xd9, 0xb9, 0xc6,
	0x85, 0xd1, 0xf0, 0x3c, 0xee, 0xa3, 0xb0, 0xf9, 0x4a, 0xbe, 0xc6, 0xc5, 0x73, 0xd9, 0x96, 0xaa,
	0x48, 0xe6, 0x81, 0x6b, 0xdb, 0xee, 0x09, 0x2e, 0xcc, 0x7c, 0x45, 0xc2, 0x1f, 0x22, 0x41, 0xff,
	0x6e, 0x0e, 0xf4, 0x41, 0xaa, 0xa4, 0xe2, 0x30, 0x76, 0xc5, 0xe9, 0xe3, 0x10, 0xbd, 0xf6, 0x15,
	0x98, 0x41, 0x31, 0x8d, 0x64, 0xb4, 0xdc, 0x69, 0x47, 0x9b, 0xc2, 0x11, 0xe2, 0xdc, 0xb7, 0x0b,
	0x44, 0x29, 0x97, 0x1a, 0x73, 0xe4, 0xb4, 0x63, 0x4e, 0xab, 0x21, 0xa2, 0xb6, 0xfe, 0x93, 0x1c,
	0x5c, 0xae, 0xf0, 0xaa, 0xeb, 0x9b, 0x91, 0x49, 0x76, 0xb9, 0x63, 0x46, 0xdb, 0x78, 0xa6, 0x73,
	0xaf, 0x00, 0x08, 0xe6, 0xa3, 0x23, 0x62, 0xff, 0x16, 0x14, 0xe5, 0xb9, 0x85, 0x65, 0xab, 0xeb,
	0x9b, 0x32, 0xa6, 0x54, 0x61, 0x5a, 0xa8, 0x4c, 0x60, 0x7b, 0xcb, 0xa4, 0x4f, 0x60, 0x12, 0xc3,
	0xa2, 0x8a, 0x85, 0x3a, 0x3a, 0x78, 0x72, 0xe9, 0xce, 0xe0, 0x72, 0x22, 0x55, 0xdd, 0x57, 0x40,
	0x8e, 0x10, 0x56, 0xfa, 0x57, 0x00, 0xc2, 0xf1, 0x1a, 0xe1, 0x36, 0x32, 0x55, 0x29, 0x28, 0xbe,
	0x2c, 0x46, 0x17, 0x60, 0x36, 0x2a, 0x5f, 0x0e, 0x7c, 0xb7, 0xae, 0x82, 0x4e, 0x65, 0xec, 0x99,
	0x90, 0xf1, 0xd0, 0x77, 0xeb, 0x18, 0x7b, 0x97, 0xa0, 0x10, 0xc4, 0x65, 0x90, 0xca, 0xda, 0xf9,
	0x20, 0xaa, 0x83, 0xda, 0x73, 0x7a, 0xbe, 0x33, 0xa7, 0x5f, 0x87, 0x62, 0xc4, 0xc6, 0x29, 0x0a,
	0x08, 0x98, 0x0c, 0x69, 0x98, 0x04, 0x3c, 0xb8, 0x5a, 0xe1, 0x55, 0x66, 0x57, 0x1b, 0x36, 0x13,
	0x5c, 0x7a, 0xa4, 0xb3, 0x34, 0xfc, 0xac, 0xd3, 0xce, 0x7b, 0x50, 0x4a, 0xcd, 0x78, 0xda, 0x42,
	0xb4, 0xef, 0x0e, 0xa1, 0x2f, 0xc1, 0xe5, 0x27, 0xae, 0xb0, 0x0e, 0x5a, 0xb1, 0x9f, 0x0e, 0x59,
	0xc0, 0xd7, 0x0f, 0x99, 0x53, 0xe3, 0x72, 0x74, 0x0a, 0xa3, 0xa9, 0xca, 0x1a, 0xff, 0xd7, 0xff,
	0x5d, 0x83, 0xe9, 0x6d, 0xf7, 0x98, 0x6f, 0x73, 0x21, 0xc2, 0xcc, 0x36, 0x0f, 0x68, 0x62, 0x23,
	0xd1, 0x7b, 0x42, 0xb6, 0x9f, 0x0f, 0x8e, 0xb1, 0x1b, 0x30, 0x65, 0xe3, 0x38, 0x46, 0x20, 0x98,
	0x68, 0x04, 0xa1, 0xf6, 0x45, 0x45, 0xdc, 0x45, 0x9a, 0xd4, 0x31, 0x04, 0xa1, 0x30, 0x61, 0x3a,
	0x51, 0xa4, 0x28, 0xdf, 0x44, 0x00, 0xde, 0x8c, 0xca, 0x90, 0x08, 0xc0, 0x9b, 0x42, 0x0a, 0x68,
	0x32, 0xc1, 0x0d, 0xcb, 0x11, 0x61, 0xdc, 0x4c, 0xc8, 0xf6, 0x56, 0x78, 0xd0, 0x51, 0x7d, 0xe3,
	0x5d, 0x3e, 0xaf, 0x08, 0x5b, 0xa6, 0x6e, 0x00, 0x6d, 0x57, 0x15, 0xb7, 0xa6, 0x2d, 0x20, 0xb6,
	0x7b, 0xcc, 0x8d, 0xb0, 0x5f, 0x6a, 0xa7, 0xeb, 0x3e, 0x5e, 0xb4, 0x77, 0xaf, 0x4c, 0xdb, 0x71,
	0x1b, 0x77, 0xff, 0xbf, 0xcd, 0xc1, 0xd9, 0xba, 0x6b, 0x5a, 0x07, 0xad, 0x04, 0x28, 0x0d, 0xdf,
	0x26, 0x95, 0xd6, 0x2e, 0x55, 0x9b, 0xb9, 0x73, 0xfd, 0xcc, 0x3d, 0xd2, 0x69, 0xee, 0x81, 0x89,
	0x39, 0x6d, 0xa8, 0xb1, 0x76, 0x43, 0x75, 0x78, 0x61, 0x7c, 0x90, 0x17, 0x26, 0x3a, 0xbd, 0xa0,
	0x3f, 0x4d, 0x07, 0x0e, 0x76, 0x39, 0x0b, 0x33, 0x92, 0x62, 0x6c, 0x47, 0x03, 0xbf, 0x4e, 0xce,
	0x74, 0x13, 0xef, 0x11, 0xad, 0x9b, 0xb8, 0x44, 0x72, 0xfa, 0x63, 0x38, 0xd7, 0x6d, 0xbc, 0xc0,
	0x93, 0x61, 0x5b, 0x75, 0xcd, 0x38, 0x6c, 0xe5, 0xff, 0x52, 0x3a, 0x87, 0x9f, 0xc8, 0x8c, 0x85,
	0xa5, 0xaa, 0x3a, 0x5c, 0x81, 0xc3, 0x4f, 0xd6, 0x15, 0x45, 0xbf, 0x07, 0xe7, 0x36, 0xb9, 0xd8,
	0x6e, 0xf3, 0x8f, 0x74, 0x45, 0xda, 0x24, 0x5a, 0x9b, 0x49, 0xf4, 0x63, 0x38, 0x9f, 0xd1, 0xa5,
	0x87, 0x00, 0x59, 0x51, 0x93, 0x7b, 0xb9, 0xa8, 0xf9, 0x70, 0x04, 0xa6, 0xef, 0x57, 0xc5, 0x3a,
	0xde, 0xb2, 0xdc, 0x3f, 0x61, 0x3e, 0x3a, 0x9e, 0xc9, 0x7f, 0xd2, 0x27, 0xe1, 0x02, 0x52, 0xd0,
	0xd0, 0xf3, 0x90, 0x57, 0xec, 0x24, 0x64, 0xb0, 0xbd, 0x65, 0xca, 0x23, 0x50, 0x20, 0x7c, 0x23,
	0x66, 0xab, 0x54, 0x0f, 0x81, 0xf0, 0xef, 0x87, 0x88, 0x4b, 0xa0, 0x46, 0x32, 0x9c, 0x46, 0x3d,
	0x2a, 0x3c, 0x90, 0xf0, 0xa4, 0x51, 0x4f, 0x4d, 0x2c, 0x13, 0xee, 0x58, 0x7a, 0x62, 0x99, 0x71,
	0xe3, 0xbe, 0x76, 0x3d, 0x5a, 0x7a, 0xaa, 0xef, 0x76, 0x5d, 0x24, 0xcc, 0x63, 0xee, 0x87, 0xf1,
	0xa2, 0x98, 0x2f, 0xb8, 0xaf, 0xff, 0x89, 0x06, 0xb4, 0x5d, 0x49, 0xd4, 0x84, 0xa2, 0xea, 0x06,
	0x12, 0x8c, 0x27, 0xae, 0xc3, 0xc9, 0x19, 0x3a, 0x0f, 0xe7, 0x13, 0x5a, 0x85, 0x9b, 0xc6, 0x86,
	0xc5, 0xea, 0xae, 0x63, 0xaa, 0xb8, 0x49, 0x58, 0x3b, 0xdc, 0x64, 0x36, 0xc9, 0xd1, 0x0b, 0x38,
	0x72, 0x48, 0x7c, 0xc4, 0x99, 0x79, 0xc2, 0x99, 0x4f, 0x46, 0xe8, 0x79, 0x98, 0x4d, 0xe8, 0xcf,
	0x58, 0xf5, 0x88, 0xd5, 0x38, 0x19, 0xa5, 0x3a, 0x5c, 0x4d, 0xc8, 0xeb, 0xe1, 0x06, 0xf1, 0xc0,
	0x91, 0x5e, 0x7c, 0x70, 0x70, 0xc0, 0xab, 0x82, 0x8c, 0xe9, 0xdf, 0xd1, 0x60, 0x6a, 0x5d, 0x15,
	0x72, 0x6a, 0x17, 0xce, 0xd8, 0x07, 0x86, 0x2a, 0x00, 0xa7, 0x21, 0x27, 0xa2, 0x34, 0x98, 0x13,
	0xc1, 0x50, 0x4b, 0x36, 0xde, 0xa6, 0xc7, 0xda, 0xb6, 0x69, 0xfd, 0x2f, 0x35, 0xd4, 0x7e, 0x8d,
	0x09, 0x61, 0xf3, 0xdd, 0xc6, 0xfe, 0x4e, 0x50, 0xcb, 0x4a, 0xe9, 0xf2, 0x94, 0xd4, 0xbe, 0x2e,
	0xa2, 0xa6, 0x2e, 0xe0, 0x6c, 0xc7, 0x00, 0xe1, 0xa9, 0x6b, 0x5e, 0x5a, 0x44, 0xd1, 0x8d, 0xdd,
	0xc6, 0xbe, 0xb1, 0x13, 0xd4, 0x8c, 0x27, 0xae, 0x5f, 0x67, 0x36, 0x39, 0x43, 0xaf, 0xc1, 0xa5,
	0x2c, 0xb6, 0x55, 0x3d, 0x72, 0x58, 0x9d, 0x13, 0xad, 0x47, 0xff, 0x67, 0x3e, 0x3f, 0xb0, 0x9a,
	0x24, 0xa7, 0xff, 0xb2, 0x86, 0x8e, 0x50, 0xec, 0x67, 0xbe, 0x5b, 0xf3, 0x79, 0x10, 0xc8, 0x5a,
	0x54, 0xc6, 0x98, 0x21, 0x82, 0xe8, 0x10, 0x24, 0x9b, 0x7b, 0x81, 0xb4, 0x74, 0x3d, 0xa8, 0x85,
	0xa2, 0xcb, 0x7f, 0xe9, 0x1a, 0x14, 0x83, 0xc6, 0xbe, 0x51, 0x0f, 0x6a, 0x6a, 0x9d, 0x8d, 0xe0,
	0x3a, 0x2b, 0x77, 0xad, 0xb3, 0x0e, 0xdd, 0x2a, 0x10, 0xe0, 0x5f, 0x5c, 0x64, 0xbf, 0xaa, 0xc1,
	0xdc, 0x26, 0xef, 0x10, 0x22, 0x4a, 0x0a, 0x9f, 0x6a, 0xdb, 0x3d, 0xdd, 0xc1, 0x4c, 0x37, 0xf1,
	0x22, 0x2a, 0x4b, 0x8e, 0xc0, 0xa3, 0x9b, 0x30, 0xe5, 0x85, 0xb4, 0xf4, 0x46, 0xa4, 0xf7, 0x56,
	0x35, 0x1a, 0xa2, 0x52, 0xf4, 0x52, 0x83, 0xe9, 0x3f, 0xd1, 0x60, 0x7e, 0x07, 0x93, 0xe9, 0x67,
	0x54, 0xd4, 0x5c, 0x84, 0x09, 0xd7, 0x4b, 0x97, 0x34, 0xe3, 0xae, 0x17, 0xe5, 0x22, 0xd7, 0x6b,
	0x3b, 0xb0, 0x4f, 0xb8, 0x9e, 0x0a, 0x7e, 0xd5, 0xc7, 0x64, 0x82, 0x45, 0x77, 0x09, 0xae, 0xb7,
	0xc1, 0x04, 0xd3, 0x37, 0x81, 0x26, 0xc2, 0x49, 0xa1, 0xc2, 0x2b, 0x5d, 0xa2, 0xa8, 0x86, 0x6c,
	0x46, 0xd9, 0xe0, 0x12, 0x5c, 0x4c, 0x53, 0x53, 0xdb, 0x06, 0xd1, 0xf4, 0xcb, 0x50, 0xea, 0xa5,
	0x65, 0xe0, 0xe9, 0xdf, 0xd2, 0xa0, 0xb0, 0xe1, 0xee, 0xf1, 0x40, 0xfc, 0x7f, 0x50, 0xba, 0x08,
	0x10, 0x09, 0x13, 0x78, 0xfa, 0x97, 0x20, 0xbf, 0x65, 0xba, 0xf6, 0x4b, 0x5d, 0x23, 0xe9, 0x4f,
	0xa1, 0x18, 0x75, 0xc6, 0x12, 0xe6, 0x67, 0x60, 0xda, 0x32, 0x5d, 0x1b, 0xcf, 0x1e, 0xe9, 0xb8,
	0x99, 0xef, 0x8a, 0x9b, 0xa8, 0x5b, 0xa5, 0x68, 0xa5, 0x06, 0xd0, 0x57, 0x61, 0x7a, 0x93, 0x0b,
	0xc9, 0x8c, 0x96, 0x44, 0x12, 0xd4, 0x5a, 0x76, 0x50, 0xe7, 0xd2, 0x41, 0xfd, 0x0d, 0x98, 0x69,
	0xeb, 0xdf, 0x63, 0xd3, 0x5c, 0xef, 0x92, 0x53, 0x1d, 0xbe, 0xae, 0xf4, 0x94, 0x13, 0x87, 0x6b,
	0x97, 0xd5, 0x86, 0x6b, 0xdd, 0x07, 0xc6, 0xe4, 0xaa, 0x6c, 0xa8, 0xf5, 0x7c, 0x0b, 0x48, 0xea,
	0xf6, 0x2f, 0xbd, 0xac, 0xa7, 0x79, 0x3c, 0x12, 0xd6, 0xd4, 0x87, 0x59, 0xf7, 0x39, 0xe9, 0xd9,
	0x7a, 0xa8, 0xda, 0xeb, 0xa2, 0x31, 0xd7, 0xf3, 0xa2, 0xf1, 0x07, 0x1a, 0xdc, 0xc8, 0xbe, 0x90,
	0x38, 0xa5, 0x72, 0x61, 0x38, 0xe5, 0x92, 0x70, 0xca, 0x52, 0x77, 0x24, 0x4b, 0x5d, 0x79, 0x66,
	0x0a, 0x91, 0x1e, 0xf3, 0x59, 0x3d, 0xfc, 0x4c, 0x31, 0xa9, 0x68, 0xcf, 0x24, 0x49, 0x37, 0xe1,
	0xe6, 0x60, 0x31, 0x7b, 0x58, 0xe5, 0x16, 0x10, 0xbc, 0xd7, 0xe8, 0xb6, 0xc8, 0x74, 0xa3, 0x6d,
	0x04, 0xbd, 0x8e, 0x7b, 0xdd, 0x06, 0xb3, 0xec, 0xe8, 0x32, 0xe0, 0x33, 0xbb, 0x8b, 0xa2, 0x30,
	0x2a, 0x4b, 0xbe, 0xd0, 0x40, 0xf8, 0xbf, 0xfe, 0x17, 0x6a, 0x8f, 0x7a, 0x64, 0x05, 0xc2, 0xf5,
	0xe3, 0x19, 0xd7, 0xa1, 0x78, 0xa8, 0x28, 0xe9, 0x49, 0x33, 0x37, 0x9e, 0xb4, 0xa4, 0x95, 0xc9,
	0xb0, 0x17, 0x4e, 0xf7, 0x79, 0xa0, 0x36, 0x0b, 0x84, 0x81, 0xb5, 0xad, 0xc5, 0x4d, 0x23, 0x35,
	0x39, 0x91, 0x9c, 0x9d, 0x90, 0xb1, 0xc1, 0x04, 0x97, 0x68, 0xd3, 0x0a, 0xd8, 0xbe, 0xcd, 0x0d,
	0xe1, 0x9a, 0xac, 0x15, 0xed, 0x78, 0xda, 0xad, 0x7c, 0x85, 0x84, 0x9c, 0x3d, 0xc9, 0xc0, 0xb5,
	0xf0, 0x6f, 0x1a, 0x5c, 0xdc, 0x66, 0xb2, 0x76, 0x71, 0x1e, 0xf2, 0x40, 0x58, 0xc7, 0xcc, 0x96,
	0x4a, 0xcb, 0x7c, 0xf3, 0x32, 0xf9, 0xee, 0x1c, 0x8c, 0x79, 0xf2, 0xc8, 0x18, 0x06, 0x87, 0x6a,
	0xf4, 0xf8, 0xd4, 0xb6, 0x96, 0xf9, 0x11, 0x62, 0xe0, 0x17, 0x99, 0xb6, 0x2f, 0x10, 0xb7, 0x80,
	0xb4, 0x9b, 0x46, 0x04, 0x61, 0x55, 0x39, 0x9d, 0x36, 0xcc, 0x5e, 0xa0, 0xff, 0x58, 0x83, 0xf3,
	0xef, 0xb8, 0xbe, 0x6d, 0x3e, 0x60, 0xbe, 0x38, 0xdc, 0x60, 0xad, 0x4f, 0xa3, 0xe6, 0xdb, 0x30,
	0x1b, 0xb9, 0x35, 0x09, 0x28, 0x55, 0x54, 0x5c, 0xcd, 0x90, 0x3f, 0xf5, 0x69, 0xac, 0x32, 0x73,
	0x98, 0x44, 0x48, 0x1f, 0xef, 0x8e, 0xf6, 0xf0, 0x6e, 0xfa, 0x3e, 0x70, 0xac, 0xed, 0x3e, 0x50,
	0xff, 0x0f, 0x0d, 0xa6, 0x77, 0x58, 0xeb, 0x53, 0xaa, 0x96, 0xed, 0xc1, 0xf8, 0xf6, 0x35, 0xed,
	0x47, 0x75, 0xfb, 0xba, 0x8b, 0xce, 0x44, 0x80, 0x8c, 0x36, 0x05, 0x18, 0x8b, 0x00, 0x26, 0x6b,
	0x29, 0xc0, 0x0d, 0x59, 0x98, 0x28, 0x6f, 0xa7, 0xbf, 0xa2, 0x46, 0x21, 0xa0, 0xb6, 0xbe, 0x2c,
	0x77, 0x4e, 0x64, 0xba, 0xf3, 0x9f, 0x35, 0x98, 0x55, 0x6a, 0x5a, 0x36, 0xee, 0xd7, 0x2f, 0xab,
	0x6f, 0xdf, 0x8b, 0xcb, 0xd3, 0xf9, 0x66, 0x01, 0x66, 0x95, 0x0d, 0x7c, 0x89, 0x54, 0x47, 0x8e,
	0xd0, 0x12, 0x33, 0xc8, 0xa8, 0x70, 0x33, 0x3c, 0x89, 0x24, 0xf6, 0x12, 0xfb, 0x9c, 0x39, 0xd1,
	0xe9, 0x19, 0x49, 0x7b, 0x92, 0xa2, 0xcf, 0xc1, 0x85, 0x4d, 0x2e, 0x64, 0x94, 0xac, 0xd9, 0xac,
	0x7a, 0xf4, 0xdc, 0x32, 0xc3, 0x8d, 0x55, 0x0f, 0xe0, 0x62, 0x26, 0x27, 0xf0, 0xe8, 0x6d, 0xa0,
	0x3e, 0xaf, 0x1e, 0xab, 0xa0, 0x6c, 0x58, 0x66, 0x92, 0x74, 0xa6, 0x2a, 0x33, 0x92, 0x23, 0x7b,
	0x85, 0x1d, 0x24, 0x18, 0xaf, 0x0d, 0xda, 0xc1, 0x39, 0x05, 0x96, 0x9c, 0x14, 0x78, 0xe1, 0x3f,
	0xaf, 0xc3, 0x85, 0xe8, 0xf2, 0x30, 0xb9, 0xe1, 0x47, 0x23, 0x11, 0x28, 0xca, 0xff, 0x8d, 0xe7,
	0xce, 0x91, 0xe3, 0x9e, 0x38, 0xe4, 0x0c, 0x9d, 0x82, 0x02, 0x52, 0x2a, 0xbc, 0x7a, 0x4c, 0xb4,
	0xb8, 0xb9, 0xcb, 0x1d, 0x93, 0xe4, 0xe8, 0x45, 0x38, 0x8b, 0xcd, 0x3d, 0xee, 0xd7, 0x8d, 0x35,
	0xfc, 0x80, 0xb9, 0x63, 0x39, 0x64, 0x24, 0x1e, 0xe8, 0x67, 0x1b, 0xfc, 0x6d, 0xcb, 0xa9, 0x91,
	0x11, 0x3a, 0x0d, 0x10, 0x51, 0xd6, 0x18, 0x19, 0xa5, 0x33, 0x30, 0x19, 0xb5, 0xdf, 0x3b, 0x64,
	0x64, 0x2c, 0x4d, 0x58, 0x67, 0x0e, 0x19, 0xcf, 0x1c, 0x9c, 0x35, 0xc9, 0x38, 0xbd, 0x0a, 0x25,
	0x64, 0x3c, 0x61, 0xc2, 0x72, 0x1d, 0x66, 0x1b, 0x1b, 0xac, 0x65, 0x2c, 0xbd, 0x7e, 0xef, 0x8b,
	0x38, 0xf9, 0x04, 0x5e, 0x28, 0x48, 0x3e, 0x9e, 0x09, 0x8d, 0xc7, 0x72, 0xfe, 0x89, 0x78, 0xfe,
	0x77, 0x1b, 0xc6, 0x86, 0x45, 0xf2, 0xb1, 0x84, 0xef, 0x36, 0xb8, 0xb1, 0xcd, 0x5c, 0x52, 0x88,
	0x29, 0x78, 0x47, 0x66, 0x3c, 0x20, 0x10, 0x8b, 0xf4, 0xc8, 0x6d, 0x18, 0xef, 0x5a, 0x64, 0x32,
	0x86, 0xbc, 0xd3, 0x30, 0x36, 0x99, 0x53, 0x23, 0xc5, 0x7e, 0xb2, 0xb0, 0x26, 0x29, 0xca, 0xf3,
	0xa8, 0x1a, 0x82, 0xd9, 0xb6, 0x7b, 0xc2, 0xb9, 0x32, 0xd0, 0x54, 0x3c, 0xd2, 0x0b, 0x56, 0xf7,
	0x2c, 0x9f, 0x93, 0xa9, 0x58, 0xc0, 0x9d, 0x46, 0xbd, 0xde, 0x22, 0xd3, 0xf2, 0xc4, 0x8a, 0xed,
	0x87, 0xd2, 0xa9, 0xdc, 0x09, 0x04, 0xb7, 0x1c, 0x32, 0x43, 0x67, 0x61, 0x4a, 0x79, 0xe0, 0x88,
	0xdb, 0x5c, 0xb8, 0x0e, 0x21, 0x59, 0x73, 0xb0, 0x26, 0x21, 0x74, 0x0e, 0xce, 0x29, 0xa8, 0xe5,
	0xd4, 0x6c, 0x1e, 0xa0, 0x68, 0x72, 0xf6, 0x59, 0x5a, 0x86, 0xcb, 0x5d, 0x9c, 0x0a, 0xaf, 0x72,
	0xeb, 0x98, 0x0f, 0x83, 0x60, 0x4d, 0x72, 0x4e, 0x1e, 0xf4, 0xba, 0x10, 0x32, 0x2c, 0x70, 0x80,
	0xf3, 0x7d, 0xd8, 0xac, 0x49, 0xe6, 0xb2, 0x65, 0x43, 0x4e, 0x64, 0xd1, 0xae, 0x8e, 0x5b, 0x82,
	0xd7, 0xc9, 0x3c, 0xbd, 0x0c, 0x73, 0xc8, 0xbf, 0xef, 0x38, 0xd6, 0x31, 0xf7, 0x03, 0xe6, 0xc7,
	0x92, 0x91, 0x12, 0x9d, 0x87, 0xf3, 0x5d, 0x5c, 0x0c, 0xd6, 0x4b, 0x99, 0x1d, 0x71, 0xe0, 0xbd,
	0x3d, 0x72, 0x99, 0xde, 0x84, 0x72, 0x17, 0x77, 0xc7, 0xaa, 0xb6, 0x99, 0xe5, 0xda, 0x60, 0x14,
	0x6b, 0x92, 0x1b, 0xf4, 0x3a, 0x5c, 0xc9, 0x44, 0xc5, 0xe6, 0x79, 0x75, 0x00, 0x84, 0x35, 0xc9,
	0x82, 0x3c, 0x6a, 0x2b, 0x43, 0x78, 0xbe, 0xe5, 0xd4, 0xa2, 0x2d, 0x5d, 0x4e, 0x85, 0x63, 0xdc,
	0xee, 0x0b, 0x60, 0x4d, 0xb2, 0x98, 0x38, 0xb1, 0x1d, 0x10, 0x8b, 0x71, 0xb7, 0x3f, 0x82, 0x35,
	0xc9, 0x92, 0x3c, 0x72, 0xf5, 0x98, 0x84, 0xbc, 0x11, 0x9b, 0x34, 0xa3, 0x3b, 0xf9, 0x42, 0x2f,
	0x6e, 0xc5, 0x75, 0xeb, 0xe4, 0xcd, 0xd8, 0x02, 0x19, 0xd2, 0xe3, 0x4e, 0x40, 0x7e, 0x8a, 0xea,
	0x70, 0xb5, 0xa7, 0x74, 0x0a, 0xf3, 0x56, 0x2f, 0x8c, 0x9c, 0x24, 0xc4, 0x7c, 0x51, 0x1e, 0x26,
	0x11, 0x13, 0x56, 0x47, 0xa8, 0xfb, 0x4a, 0xbc, 0xc0, 0x22, 0x2a, 0x4a, 0xbd, 0x12, 0x47, 0x66,
	0x44, 0x8e, 0x62, 0xeb, 0xcb, 0xb4, 0x04, 0x17, 0xda, 0x3b, 0xc8, 0xdd, 0x10, 0x07, 0x5b, 0xed,
	0xc5, 0x63, 0x4d, 0x72, 0xbf, 0x7b, 0x7a, 0xa4, 0x46, 0xe9, 0xed, 0xa1, 0xeb, 0xda, 0x49, 0x94,
	0x93, 0xf5, 0x78, 0xa8, 0x84, 0x11, 0x89, 0xb0, 0x11, 0x9b, 0x34, 0xe1, 0xa9, 0x24, 0x27, 0x85,
	0x78, 0xd0, 0x9b, 0xcb, 0x9a, 0xe4, 0x51, 0xac, 0xef, 0x0e, 0x6b, 0x25, 0xd3, 0xbd, 0x1d, 0xeb,
	0x1b, 0x91, 0xc3, 0x5b, 0x30, 0xf2, 0x38, 0x16, 0x24, 0xe2, 0x24, 0x53, 0x6d, 0xf7, 0xe2, 0xb1,
	0x26, 0x79, 0x1a, 0x2f, 0x92, 0x27, 0x5c, 0x9c, 0xb8, 0xfe, 0x91, 0xf1, 0x82, 0xd9, 0xdc, 0x11,
	0x96, 0x93, 0x5a, 0xcc, 0xe4, 0xd9, 0x20, 0x14, 0x6e, 0x38, 0x5f, 0xa1, 0xaf, 0xc1, 0x2b, 0xfd,
	0x50, 0x89, 0x48, 0x15, 0xba, 0x08, 0x0b, 0x83, 0xa1, 0x71, 0xec, 0x9f, 0x0a, 0xcf, 0x9a, 0xe4,
	0xf9, 0x70, 0x78, 0x29, 0x36, 0x8e, 0xff, 0xe2, 0x34, 0x78, 0xd6, 0x24, 0xef, 0x0e, 0xa9, 0x2a,
	0x42, 0xa3, 0xe4, 0xba, 0xc3, 0x6a, 0x9e, 0xc5, 0x8d, 0xf6, 0x85, 0xf7, 0x5e, 0x4f, 0x36, 0xda,
	0xf4, 0xab, 0xf1, 0x92, 0xe9, 0x64, 0x27, 0xc6, 0xfc, 0x1a, 0xfd, 0x1c, 0xdc, 0xe8, 0x83, 0x89,
	0xad, 0x38, 0x1c, 0x90, 0x35, 0xc9, 0xcf, 0x0d, 0x00, 0xc6, 0x76, 0x33, 0x86, 0x02, 0xb2, 0x26,
	0x61, 0x83, 0xf4, 0x40, 0x4c, 0x94, 0x65, 0x76, 0x2c, 0xd3, 0xb8, 0xdf, 0x10, 0x8d, 0xba, 0xd3,
	0x61, 0xad, 0xfd, 0xbe, 0x10, 0xb4, 0x58, 0x95, 0xbe, 0x0a, 0x7a, 0x4f, 0x48, 0x62, 0x35, 0x93,
	0xde, 0x86, 0xcf, 0x0d, 0xc0, 0xc5, 0x96, 0x1b, 0x1e, 0xcc, 0x9a, 0xa4, 0x36, 0x04, 0x38, 0xb6,
	0xe0, 0xe1, 0xd0, 0x60, 0xd6, 0x24, 0x47, 0xc3, 0xe8, 0x86, 0xb8, 0x68, 0xb7, 0xe9, 0xae, 0x74,
	0xd0, 0x8e, 0x76, 0x1f, 0x00, 0x5a, 0xb1, 0x9e, 0xac, 0xf8, 0xee, 0x5a, 0x29, 0xb6, 0xa1, 0x93,
	0x2c, 0x83, 0x1e, 0xa8, 0xd8, 0x82, 0xc3, 0x42, 0x59, 0x93, 0xbc, 0x3f, 0x10, 0x1a, 0x5b, 0xcf,
	0x1f, 0x12, 0xca, 0x9a, 0xa4, 0x31, 0x58, 0x23, 0x44, 0x45, 0x03, 0xbe, 0xcb, 0x99, 0x1f, 0x7e,
	0x6a, 0xd8, 0x64, 0x75, 0x6e, 0xec, 0x5a, 0x35, 0xe7, 0xb9, 0x97, 0xb2, 0xe1, 0x71, 0xec, 0xb9,
	0x4e, 0xe8, 0x63, 0xc7, 0xad, 0x1e, 0x3d, 0x6d, 0x88, 0x14, 0xf8, 0x64, 0x48, 0x30, 0x1a, 0xbf,
	0x19, 0xef, 0xf4, 0x4f, 0xf8, 0x09, 0x76, 0x08, 0x22, 0x59, 0x7f, 0x5a, 0x0d, 0xd7, 0xea, 0x87,
	0xc0, 0x31, 0x7e, 0x9e, 0xbe, 0x02, 0xd7, 0x7b, 0x21, 0x12, 0x0f, 0xfe, 0x02, 0x5d, 0x80, 0x57,
	0xfb, 0xc3, 0x62, 0x17, 0x0e, 0x8d, 0x65, 0x4d, 0xf2, 0x8b, 0xf4, 0xf6, 0x20, 0x6c, 0xec, 0xc4,
	0x0f, 0xb4, 0x61, 0xc1, 0xac, 0x49, 0x7e, 0x45, 0xa3, 0xaf, 0x0e, 0x54, 0xac, 0x1d, 0xb7, 0xce,
	0x44, 0xf5, 0xd0, 0x72, 0x6a, 0xc6, 0x13, 0x8b, 0x39, 0x9d, 0x06, 0xf8, 0xb5, 0x64, 0xf2, 0x9e,
	0xb8, 0xd8, 0x02, 0xc3, 0x83, 0x59, 0x93, 0x7c, 0x6b, 0x08, 0x70, 0x6c, 0x83, 0x6f, 0x0f, 0x0d,
	0x66, 0x4d, 0xf2, 0xe1, 0x30, 0xba, 0x29, 0xdc, 0x8d, 0x30, 0xeb, 0xc6, 0xdb, 0x93, 0x19, 0x96,
	0x00, 0x0a, 0x6a, 0x39, 0xe4, 0x7b, 0x1a, 0xbd, 0x15, 0xe6, 0xf0, 0x6c, 0x50, 0xac, 0xfd, 0x90,
	0x48, 0xd6, 0x24, 0xbf, 0x39, 0x08, 0x19, 0xeb, 0xfd, 0x5b, 0xc3, 0x21, 0x59, 0x93, 0xfc, 0xde,
	0x40, 0x65, 0x14, 0x28, 0x32, 0x63, 0x36, 0x28, 0xfd, 0x11, 0xe2, 0xf7, 0x35, 0xfa, 0x1a, 0xdc,
	0xec, 0x03, 0xde, 0x32, 0x5d, 0xdb, 0x90, 0x7c, 0xf2, 0xfd, 0x41, 0xd0, 0x87, 0xcc, 0x09, 0x14,
	0xf4, 0x23, 0x8d, 0x2e, 0x84, 0x29, 0xa4, 0x8f, 0x95, 0x54, 0xb1, 0xfb, 0x83, 0x41, 0x58, 0xd4,
	0x5e, 0x61, 0xff, 0x40, 0xa3, 0xe5, 0x30, 0x67, 0x6f, 0xba, 0xa6, 0xc9, 0x83, 0xa0, 0x33, 0x94,
	0xff, 0x58, 0xa3, 0xaf, 0x84, 0x29, 0x2e, 0x03, 0x11, 0xbb, 0x71, 0x18, 0x18, 0x6b, 0x92, 0x3f,
	0xed, 0x0b, 0x8b, 0x1d, 0xf8, 0xc3, 0x21, 0x60, 0xac, 0x49, 0xfe, 0xbc, 0xbf, 0xf4, 0x88, 0x28,
	0xe5, 0x88, 0xb6, 0xf0, 0x5f, 0xa3, 0x30, 0xdb, 0xf9, 0x58, 0xdb, 0x94, 0x35, 0x52, 0x17, 0x31,
	0x75, 0xef, 0x71, 0x1e, 0x66, 0xf7, 0x98, 0xc3, 0xf6, 0x99, 0x60, 0xf1, 0x76, 0x48, 0x34, 0x79,
	0x48, 0x7f, 0xe4, 0x3a, 0xae, 0x4a, 0x10, 0x24, 0x27, 0x8f, 0xf1, 0xe9, 0xdc, 0x4f, 0x46, 0xe8,
	0x14, 0x14, 0xe2, 0x73, 0xb8, 0xba, 0xe6, 0x48, 0x1d, 0x64, 0xd5, 0x35, 0x47, 0xea, 0xcc, 0x47,
	0xc6, 0xe9, 0x59, 0x98, 0x51, 0x67, 0x97, 0x64, 0x9e, 0x09, 0x3c, 0x32, 0x84, 0xa7, 0x85, 0x98,
	0x9a, 0x97, 0x63, 0xc7, 0xa5, 0x3d, 0x29, 0xc8, 0x9e, 0x78, 0x0d, 0x6a, 0xe0, 0x3d, 0x28, 0x12,
	0x81, 0x4e, 0xc2, 0x44, 0x58, 0x93, 0x93, 0x49, 0x59, 0xa5, 0x67, 0x57, 0x93, 0xa4, 0x48, 0x67,
	0x61, 0x6a, 0xfd, 0xd0, 0xb2, 0x4d, 0x9f, 0x3b, 0x8a, 0x34, 0x25, 0x8f, 0x02, 0x1b, 0x3e, 0xab,
	0xb9, 0x8e, 0xb1, 0xe6, 0x32, 0x91, 0xcc, 0x3c, 0x4d, 0x29, 0x4c, 0xef, 0xed, 0xa5, 0x0f, 0xab,
	0x64, 0x46, 0xa2, 0x3b, 0x2b, 0x2f, 0xb9, 0xe1, 0x10, 0x82, 0x1f, 0xef, 0xd0, 0x0b, 0xeb, 0xae,
	0x6d, 0xf3, 0xaa, 0xb0, 0x9c, 0x1a, 0x99, 0x95, 0x07, 0x9e, 0x8c, 0x1a, 0x83, 0x50, 0x69, 0xeb,
	0xae, 0x0d, 0x94, 0x9c, 0x95, 0xf5, 0x44, 0x9f, 0x1d, 0x93, 0x9c, 0x93, 0x85, 0x5b, 0xdf, 0xad,
	0x8f, 0x9c, 0xa7, 0x17, 0x80, 0x76, 0xe7, 0x74, 0x72, 0x41, 0xd2, 0xbb, 0xf3, 0x1c, 0xb9, 0x28,
	0xe9, 0xb1, 0xa1, 0x12, 0xfc, 0x9c, 0xf4, 0x73, 0x3a, 0xc8, 0xc8, 0xbc, 0x8c, 0x9f, 0x77, 0x0e,
	0x2d, 0xc1, 0x3b, 0xcb, 0x74, 0x64, 0x97, 0x16, 0xbe, 0x04, 0x33, 0xcf, 0x3a, 0x9e, 0xaf, 0x4d,
	0x03, 0x84, 0x24, 0x63, 0x6f, 0x8f, 0x9c, 0x91, 0xf6, 0x8b, 0xda, 0x8f, 0x98, 0xe7, 0xb5, 0x8c,
	0x75, 0x2e, 0x3d, 0x4e, 0x72, 0x0b, 0x8f, 0xe0, 0x62, 0xc6, 0xab, 0x2d, 0x1c, 0xe4, 0x3c, 0xcc,
	0x22, 0x49, 0xdd, 0x52, 0xc5, 0x1f, 0x46, 0x2f, 0x00, 0x6d, 0x23, 0xab, 0x05, 0xae, 0x2d, 0xfc,
	0x8d, 0x06, 0x57, 0x7a, 0x7e, 0xaf, 0xc2, 0x01, 0xa3, 0xf2, 0x2f, 0x0c, 0xb7, 0x83, 0x94, 0x2b,
	0x55, 0xba, 0xc2, 0x14, 0x74, 0xa6, 0x3f, 0xee, 0x61, 0xf8, 0xe4, 0x99, 0x24, 0x19, 0x35, 0x13,
	0xb7, 0xc7, 0x03, 0x41, 0x3e, 0xc8, 0xd3, 0x9b, 0x70, 0xad, 0x37, 0x68, 0xdd, 0xe6, 0xcc, 0x27,
	0xbf, 0x94, 0x5f, 0x58, 0x4f, 0x3f, 0xd1, 0xda, 0xb6, 0x9c, 0x23, 0x14, 0x38, 0xba, 0x94, 0x7b,
	0xea, 0xd8, 0x16, 0xea, 0x1e, 0xdd, 0x49, 0x6e, 0xfa, 0xac, 0x95, 0xba, 0xa2, 0xc4, 0xb9, 0x72,
	0x4b, 0xff, 0x70, 0x1e, 0x3f, 0x1e, 0xb5, 0xfd, 0x9c, 0xe7, 0xbb, 0x1a, 0xde, 0xc8, 0x66, 0xfc,
	0x82, 0x83, 0x2e, 0x74, 0xdd, 0xf9, 0xf7, 0xfc, 0xa5, 0x48, 0xe9, 0xf6, 0xd0, 0xd8, 0xc0, 0xd3,
	0xaf, 0x7f, 0xf0, 0xd1, 0x27, 0x23, 0xda, 0x37, 0x3f, 0xfa, 0x64, 0x24, 0xc7, 0x96, 0xbf, 0xf3,
	0xd1, 0x27, 0x23, 0xe4, 0x0e, 0x2b, 0xaf, 0xa4, 0x2e, 0xb4, 0x57, 0xe9, 0x7f, 0x6b, 0x59, 0xef,
	0x9e, 0x53, 0x77, 0xb4, 0xf4, 0xde, 0x10, 0x33, 0xb6, 0xbf, 0x2f, 0x2f, 0x2d, 0x9d, 0xb6, 0x4b,
	0xe0, 0xe9, 0xae, 0x94, 0x35, 0x27, 0x65, 0x05, 0xb6, 0xdc, 0x5c, 0x16, 0xcb, 0xee, 0xb2, 0x83,
	0x32, 0xbf, 0xe8, 0x94, 0xb9, 0x7c, 0xa7, 0x59, 0x5e, 0x09, 0x9f, 0x11, 0xaf, 0x96, 0xef, 0x88,
	0xf2, 0x4a, 0x7c, 0x03, 0x5f, 0xbe, 0xb7, 0x18, 0x70, 0xc7, 0x2c, 0x2f, 0x2d, 0xfa, 0xea, 0xfa,
	0x62, 0xb5, 0xfc, 0xd5, 0x3b, 0x6e, 0x79, 0x45, 0x7d, 0x3b, 0x5e, 0x2d, 0xdf, 0x71, 0x24, 0xb8,
	0x6e, 0x89, 0xd5, 0xaf, 0xd3, 0x4f, 0x32, 0xdf, 0x5f, 0xa7, 0x1f, 0xfe, 0xd2, 0x53, 0xe8, 0x11,
	0x3d, 0x7a, 0x2e, 0xbd, 0x71, 0xea, 0x3e, 0x81, 0xa7, 0x7f, 0x5d, 0x2a, 0x3f, 0x22, 0x95, 0xcf,
	0xb3, 0xe5, 0x06, 0xaa, 0x2f, 0x55, 0x7f, 0xd8, 0xad, 0x7a, 0xa3, 0xbc, 0xd2, 0x38, 0xad, 0x0d,
	0xe8, 0x8f, 0x35, 0x98, 0xef, 0xf9, 0x98, 0x97, 0x76, 0x3f, 0xb4, 0xed, 0xf7, 0xf0, 0xb7, 0x74,
	0x79, 0x31, 0xfe, 0x1d, 0xde, 0xe2, 0xee, 0xe3, 0x35, 0xf5, 0x3b, 0xbc, 0x07, 0x75, 0x4f, 0xb4,
	0x8c, 0x67, 0x6b, 0x7a, 0x43, 0x6a, 0x32, 0x2a, 0x35, 0x29, 0x36, 0x96, 0xc5, 0x72, 0x13, 0xdd,
	0xe8, 0xa1, 0x36, 0xef, 0x25, 0xc2, 0x8b, 0xf2, 0x4a, 0xf2, 0xae, 0xb0, 0x53, 0x19, 0x27, 0x6c,
	0xe0, 0xeb, 0xdd, 0xd5, 0x32, 0xfa, 0x2f, 0x7c, 0x91, 0xb4, 0x5a, 0xbe, 0xe3, 0x95, 0x57, 0xba,
	0x1e, 0xef, 0xae, 0xd2, 0xdf, 0xd0, 0xe0, 0x52, 0x9f, 0x87, 0xb4, 0xf4, 0x6e, 0x96, 0x8e, 0x7d,
	0x9e, 0xdd, 0x0e, 0xd0, 0xf2, 0xf3, 0x52, 0xcb, 0x31, 0xa9, 0xe5, 0x68, 0x63, 0x59, 0x2d, 0xad,
	0xf9, 0x44, 0xbb, 0xce, 0x35, 0xf6, 0x4d, 0x0d, 0xe6, 0x7b, 0xbe, 0x8b, 0xcd, 0x30, 0x7f, 0xbf,
	0x37, 0xb4, 0x03, 0x04, 0x2b, 0x4b, 0xc1, 0xc6, 0x71, 0xc5, 0xab, 0x10, 0x9a, 0x41, 0x5b, 0xab,
	0xb0, 0x30, 0x59, 0x6b, 0x95, 0xfe, 0x9d, 0x96, 0xf5, 0x4b, 0xb2, 0xe8, 0x1d, 0x0c, 0x5d, 0x1c,
	0x22, 0x7c, 0x53, 0xbf, 0x8a, 0x28, 0xdd, 0x3d, 0x15, 0x3e, 0xf0, 0xf4, 0x2f, 0x4b, 0x09, 0x27,
	0xa4, 0x84, 0xe3, 0xd2, 0x74, 0x4a, 0xca, 0x5b, 0x3d, 0x8d, 0xd7, 0x1e, 0xdb, 0xab, 0xf4, 0x7b,
	0x1a, 0x5c, 0xec, 0xf1, 0x7e, 0x99, 0xde, 0xee, 0xe7, 0xe4, 0xd3, 0x39, 0x78, 0x49, 0x4a, 0x99,
	0x47, 0x07, 0x47, 0x32, 0x5e, 0x1b, 0x24, 0xda, 0xdf, 0x6b, 0xf8, 0x7e, 0xb3, 0xfb, 0x65, 0x15,
	0x7d, 0x2d, 0xcb, 0x48, 0x99, 0x2f, 0xc1, 0x4a, 0x0b, 0xc3, 0x42, 0x03, 0x4f, 0x7f, 0x22, 0x85,
	0x2c, 0x84, 0x59, 0x23, 0x49, 0x98, 0x6f, 0x0d, 0x10, 0xb4, 0x57, 0x4a, 0xa4, 0xff, 0xa4, 0xc1,
	0x85, 0xec, 0xd7, 0x4c, 0x19, 0x5b, 0x54, 0xcf, 0xc7, 0x5d, 0x19, 0x5b, 0x54, 0x9f, 0x27, 0x52,
	0x87, 0x52, 0x07, 0xc0, 0xb4, 0xaf, 0xc2, 0xc1, 0x59, 0x0e, 0x50, 0x8b, 0xa7, 0xfd, 0x43, 0x22,
	0x7c, 0x16, 0x55, 0xbe, 0xb7, 0x98, 0x7a, 0x05, 0xab, 0xb4, 0x89, 0x1e, 0x46, 0xad, 0x96, 0xef,
	0x04, 0xd8, 0x32, 0x99, 0x60, 0xab, 0xf4, 0xcf, 0x32, 0x7f, 0x19, 0xd5, 0xf1, 0x33, 0xb7, 0x2f,
	0x0c, 0x19, 0xce, 0x6d, 0x2f, 0x62, 0x4a, 0x6f, 0xbe, 0x44, 0xaf, 0xc0, 0xd3, 0xaf, 0x49, 0xdd,
	0x27, 0xd1, 0x7f, 0x4a, 0x77, 0x8e, 0x9a, 0xe7, 0x23, 0xcd, 0xe9, 0xef, 0x6a, 0x70, 0xb9, 0xdf,
	0xe3, 0x1f, 0xfa, 0xfa, 0x10, 0x13, 0xb7, 0x8b, 0x7a, 0xef, 0x94, 0x3d, 0x02, 0x4f, 0xbf, 0x24,
	0xc5, 0x2c, 0x86, 0xc9, 0x4e, 0x74, 0x88, 0xf8, 0x47, 0x1a, 0x14, 0xc3, 0x47, 0xd3, 0xe8, 0x02,
	0x7a, 0xb3, 0x6b, 0x82, 0x8c, 0x07, 0xe9, 0xa5, 0x57, 0x86, 0x40, 0x05, 0x9e, 0xbe, 0x2d, 0xa7,
	0x9e, 0x0a, 0x2d, 0x64, 0x2e, 0x8b, 0x30, 0x36, 0xde, 0x4c, 0x62, 0xc3, 0x2c, 0xaf, 0x98, 0x4c,
	0x70, 0x0b, 0x77, 0x0a, 0xb1, 0xa2, 0xe2, 0xa0, 0xac, 0xe2, 0xfc, 0x4e, 0x10, 0xb7, 0x79, 0x53,
	0xac, 0xd2, 0x1f, 0x6a, 0x30, 0xae, 0x5e, 0xc0, 0xd1, 0x52, 0xd7, 0xfc, 0xf1, 0x3b, 0xbd, 0xd2,
	0xa5, 0x9e, 0xbc, 0xc0, 0xd3, 0x6d, 0x29, 0xd1, 0x74, 0x46, 0xbc, 0xee, 0x0e, 0x1b, 0xaf, 0x5e,
	0x23, 0x38, 0x2c, 0xef, 0xe3, 0x8a, 0x2e, 0xd7, 0x83, 0x5a, 0xdf, 0x98, 0x6d, 0xc1, 0xd9, 0x8c,
	0x6f, 0xf5, 0xf4, 0x73, 0x59, 0x4e, 0xcc, 0xf8, 0xd6, 0x5f, 0xba, 0x35, 0x1c, 0x30, 0xf0, 0xf4,
	0x19, 0xa9, 0xd7, 0x55, 0xa9, 0xd7, 0x19, 0xa9, 0xcd, 0x99, 0xd2, 0xf8, 0xaf, 0x7f, 0xf4, 0xc9,
	0xc8, 0xff, 0xb6, 0xd6, 0xc8, 0x8f, 0x3e, 0xbe, 0xaa, 0xfd, 0xe3, 0xc7, 0x57, 0xb5, 0x7f, 0xf9,
	0xf8, 0xaa, 0xf6, 0xe1, 0xbf, 0x5e, 0x3d, 0xb3, 0x3f, 0x8e, 0xbf, 0xb7, 0x7f, 0xe3, 0xff, 0x02,
	0x00, 0x00, 0xff, 0xff, 0xb8, 0x30, 0x22, 0x71, 0xd8, 0x3f, 0x00, 0x00,
}
