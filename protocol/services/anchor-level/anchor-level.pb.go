// Code generated by protoc-gen-go. DO NOT EDIT.
// source: tt/quicksilver/anchor-level/anchor-level.proto

package anchor_level // import "golang.52tt.com/protocol/services/anchor-level"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import _ "golang.52tt.com/protocol/services/extension/options"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// buf:lint:ignore ENUM_PASCAL_CASE
type ANCHOR_LEVEL_TYPE int32

const (
	ANCHOR_LEVEL_TYPE_ANCHOR_LEVEL_TYPE_INVALID ANCHOR_LEVEL_TYPE = 0
	ANCHOR_LEVEL_TYPE_ANCHOR_LEVEL_TYPE_A       ANCHOR_LEVEL_TYPE = 1
	ANCHOR_LEVEL_TYPE_ANCHOR_LEVEL_TYPE_B       ANCHOR_LEVEL_TYPE = 2
	ANCHOR_LEVEL_TYPE_ANCHOR_LEVEL_TYPE_C       ANCHOR_LEVEL_TYPE = 3
	ANCHOR_LEVEL_TYPE_ANCHOR_LEVEL_TYPE_D       ANCHOR_LEVEL_TYPE = 4
	ANCHOR_LEVEL_TYPE_ANCHOR_LEVEL_TYPE_E       ANCHOR_LEVEL_TYPE = 5
	ANCHOR_LEVEL_TYPE_ANCHOR_LEVEL_TYPE_V2_A    ANCHOR_LEVEL_TYPE = 10
	ANCHOR_LEVEL_TYPE_ANCHOR_LEVEL_TYPE_V2_B    ANCHOR_LEVEL_TYPE = 20
	ANCHOR_LEVEL_TYPE_ANCHOR_LEVEL_TYPE_V2_C    ANCHOR_LEVEL_TYPE = 30
	ANCHOR_LEVEL_TYPE_ANCHOR_LEVEL_TYPE_V2_D    ANCHOR_LEVEL_TYPE = 40
	ANCHOR_LEVEL_TYPE_ANCHOR_LEVEL_TYPE_V2_E    ANCHOR_LEVEL_TYPE = 50
	ANCHOR_LEVEL_TYPE_ANCHOR_LEVEL_TYPE_V2_F    ANCHOR_LEVEL_TYPE = 60
)

var ANCHOR_LEVEL_TYPE_name = map[int32]string{
	0:  "ANCHOR_LEVEL_TYPE_INVALID",
	1:  "ANCHOR_LEVEL_TYPE_A",
	2:  "ANCHOR_LEVEL_TYPE_B",
	3:  "ANCHOR_LEVEL_TYPE_C",
	4:  "ANCHOR_LEVEL_TYPE_D",
	5:  "ANCHOR_LEVEL_TYPE_E",
	10: "ANCHOR_LEVEL_TYPE_V2_A",
	20: "ANCHOR_LEVEL_TYPE_V2_B",
	30: "ANCHOR_LEVEL_TYPE_V2_C",
	40: "ANCHOR_LEVEL_TYPE_V2_D",
	50: "ANCHOR_LEVEL_TYPE_V2_E",
	60: "ANCHOR_LEVEL_TYPE_V2_F",
}
var ANCHOR_LEVEL_TYPE_value = map[string]int32{
	"ANCHOR_LEVEL_TYPE_INVALID": 0,
	"ANCHOR_LEVEL_TYPE_A":       1,
	"ANCHOR_LEVEL_TYPE_B":       2,
	"ANCHOR_LEVEL_TYPE_C":       3,
	"ANCHOR_LEVEL_TYPE_D":       4,
	"ANCHOR_LEVEL_TYPE_E":       5,
	"ANCHOR_LEVEL_TYPE_V2_A":    10,
	"ANCHOR_LEVEL_TYPE_V2_B":    20,
	"ANCHOR_LEVEL_TYPE_V2_C":    30,
	"ANCHOR_LEVEL_TYPE_V2_D":    40,
	"ANCHOR_LEVEL_TYPE_V2_E":    50,
	"ANCHOR_LEVEL_TYPE_V2_F":    60,
}

func (x ANCHOR_LEVEL_TYPE) String() string {
	return proto.EnumName(ANCHOR_LEVEL_TYPE_name, int32(x))
}
func (ANCHOR_LEVEL_TYPE) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_anchor_level_127474984171a0bf, []int{0}
}

type UidReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UidReq) Reset()         { *m = UidReq{} }
func (m *UidReq) String() string { return proto.CompactTextString(m) }
func (*UidReq) ProtoMessage()    {}
func (*UidReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_anchor_level_127474984171a0bf, []int{0}
}
func (m *UidReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UidReq.Unmarshal(m, b)
}
func (m *UidReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UidReq.Marshal(b, m, deterministic)
}
func (dst *UidReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UidReq.Merge(dst, src)
}
func (m *UidReq) XXX_Size() int {
	return xxx_messageInfo_UidReq.Size(m)
}
func (m *UidReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UidReq.DiscardUnknown(m)
}

var xxx_messageInfo_UidReq proto.InternalMessageInfo

func (m *UidReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type ManualSettleAnchorLevelReq struct {
	SettleMonth          uint32   `protobuf:"varint,1,opt,name=settle_month,json=settleMonth,proto3" json:"settle_month,omitempty"`
	Uids                 []uint32 `protobuf:"varint,2,rep,packed,name=uids,proto3" json:"uids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ManualSettleAnchorLevelReq) Reset()         { *m = ManualSettleAnchorLevelReq{} }
func (m *ManualSettleAnchorLevelReq) String() string { return proto.CompactTextString(m) }
func (*ManualSettleAnchorLevelReq) ProtoMessage()    {}
func (*ManualSettleAnchorLevelReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_anchor_level_127474984171a0bf, []int{1}
}
func (m *ManualSettleAnchorLevelReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ManualSettleAnchorLevelReq.Unmarshal(m, b)
}
func (m *ManualSettleAnchorLevelReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ManualSettleAnchorLevelReq.Marshal(b, m, deterministic)
}
func (dst *ManualSettleAnchorLevelReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ManualSettleAnchorLevelReq.Merge(dst, src)
}
func (m *ManualSettleAnchorLevelReq) XXX_Size() int {
	return xxx_messageInfo_ManualSettleAnchorLevelReq.Size(m)
}
func (m *ManualSettleAnchorLevelReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ManualSettleAnchorLevelReq.DiscardUnknown(m)
}

var xxx_messageInfo_ManualSettleAnchorLevelReq proto.InternalMessageInfo

func (m *ManualSettleAnchorLevelReq) GetSettleMonth() uint32 {
	if m != nil {
		return m.SettleMonth
	}
	return 0
}

func (m *ManualSettleAnchorLevelReq) GetUids() []uint32 {
	if m != nil {
		return m.Uids
	}
	return nil
}

type UidCheckLevelReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	CheckLevel           string   `protobuf:"bytes,2,opt,name=check_level,json=checkLevel,proto3" json:"check_level,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UidCheckLevelReq) Reset()         { *m = UidCheckLevelReq{} }
func (m *UidCheckLevelReq) String() string { return proto.CompactTextString(m) }
func (*UidCheckLevelReq) ProtoMessage()    {}
func (*UidCheckLevelReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_anchor_level_127474984171a0bf, []int{2}
}
func (m *UidCheckLevelReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UidCheckLevelReq.Unmarshal(m, b)
}
func (m *UidCheckLevelReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UidCheckLevelReq.Marshal(b, m, deterministic)
}
func (dst *UidCheckLevelReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UidCheckLevelReq.Merge(dst, src)
}
func (m *UidCheckLevelReq) XXX_Size() int {
	return xxx_messageInfo_UidCheckLevelReq.Size(m)
}
func (m *UidCheckLevelReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UidCheckLevelReq.DiscardUnknown(m)
}

var xxx_messageInfo_UidCheckLevelReq proto.InternalMessageInfo

func (m *UidCheckLevelReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UidCheckLevelReq) GetCheckLevel() string {
	if m != nil {
		return m.CheckLevel
	}
	return ""
}

type Empty struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Empty) Reset()         { *m = Empty{} }
func (m *Empty) String() string { return proto.CompactTextString(m) }
func (*Empty) ProtoMessage()    {}
func (*Empty) Descriptor() ([]byte, []int) {
	return fileDescriptor_anchor_level_127474984171a0bf, []int{3}
}
func (m *Empty) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Empty.Unmarshal(m, b)
}
func (m *Empty) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Empty.Marshal(b, m, deterministic)
}
func (dst *Empty) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Empty.Merge(dst, src)
}
func (m *Empty) XXX_Size() int {
	return xxx_messageInfo_Empty.Size(m)
}
func (m *Empty) XXX_DiscardUnknown() {
	xxx_messageInfo_Empty.DiscardUnknown(m)
}

var xxx_messageInfo_Empty proto.InternalMessageInfo

type AnchorLevelNewTask struct {
	RemainTime             uint32   `protobuf:"varint,1,opt,name=remain_time,json=remainTime,proto3" json:"remain_time,omitempty"`
	NeedWeekActiveDays     uint32   `protobuf:"varint,2,opt,name=need_week_active_days,json=needWeekActiveDays,proto3" json:"need_week_active_days,omitempty"`
	RemainWeekActiveDays   uint32   `protobuf:"varint,3,opt,name=remain_week_active_days,json=remainWeekActiveDays,proto3" json:"remain_week_active_days,omitempty"`
	NeedWeekPlatformDays   uint32   `protobuf:"varint,4,opt,name=need_week_platform_days,json=needWeekPlatformDays,proto3" json:"need_week_platform_days,omitempty"`
	RemainWeekPlatformDays uint32   `protobuf:"varint,5,opt,name=remain_week_platform_days,json=remainWeekPlatformDays,proto3" json:"remain_week_platform_days,omitempty"`
	WeekGiftValue          uint32   `protobuf:"varint,6,opt,name=week_gift_value,json=weekGiftValue,proto3" json:"week_gift_value,omitempty"`
	AnchorCheckLevel       string   `protobuf:"bytes,7,opt,name=anchor_check_level,json=anchorCheckLevel,proto3" json:"anchor_check_level,omitempty"`
	WeekNum                uint32   `protobuf:"varint,8,opt,name=week_num,json=weekNum,proto3" json:"week_num,omitempty"`
	TaskType               uint32   `protobuf:"varint,9,opt,name=task_type,json=taskType,proto3" json:"task_type,omitempty"`
	XXX_NoUnkeyedLiteral   struct{} `json:"-"`
	XXX_unrecognized       []byte   `json:"-"`
	XXX_sizecache          int32    `json:"-"`
}

func (m *AnchorLevelNewTask) Reset()         { *m = AnchorLevelNewTask{} }
func (m *AnchorLevelNewTask) String() string { return proto.CompactTextString(m) }
func (*AnchorLevelNewTask) ProtoMessage()    {}
func (*AnchorLevelNewTask) Descriptor() ([]byte, []int) {
	return fileDescriptor_anchor_level_127474984171a0bf, []int{4}
}
func (m *AnchorLevelNewTask) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AnchorLevelNewTask.Unmarshal(m, b)
}
func (m *AnchorLevelNewTask) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AnchorLevelNewTask.Marshal(b, m, deterministic)
}
func (dst *AnchorLevelNewTask) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AnchorLevelNewTask.Merge(dst, src)
}
func (m *AnchorLevelNewTask) XXX_Size() int {
	return xxx_messageInfo_AnchorLevelNewTask.Size(m)
}
func (m *AnchorLevelNewTask) XXX_DiscardUnknown() {
	xxx_messageInfo_AnchorLevelNewTask.DiscardUnknown(m)
}

var xxx_messageInfo_AnchorLevelNewTask proto.InternalMessageInfo

func (m *AnchorLevelNewTask) GetRemainTime() uint32 {
	if m != nil {
		return m.RemainTime
	}
	return 0
}

func (m *AnchorLevelNewTask) GetNeedWeekActiveDays() uint32 {
	if m != nil {
		return m.NeedWeekActiveDays
	}
	return 0
}

func (m *AnchorLevelNewTask) GetRemainWeekActiveDays() uint32 {
	if m != nil {
		return m.RemainWeekActiveDays
	}
	return 0
}

func (m *AnchorLevelNewTask) GetNeedWeekPlatformDays() uint32 {
	if m != nil {
		return m.NeedWeekPlatformDays
	}
	return 0
}

func (m *AnchorLevelNewTask) GetRemainWeekPlatformDays() uint32 {
	if m != nil {
		return m.RemainWeekPlatformDays
	}
	return 0
}

func (m *AnchorLevelNewTask) GetWeekGiftValue() uint32 {
	if m != nil {
		return m.WeekGiftValue
	}
	return 0
}

func (m *AnchorLevelNewTask) GetAnchorCheckLevel() string {
	if m != nil {
		return m.AnchorCheckLevel
	}
	return ""
}

func (m *AnchorLevelNewTask) GetWeekNum() uint32 {
	if m != nil {
		return m.WeekNum
	}
	return 0
}

func (m *AnchorLevelNewTask) GetTaskType() uint32 {
	if m != nil {
		return m.TaskType
	}
	return 0
}

type AnchorMicPosTask struct {
	Show                   bool     `protobuf:"varint,1,opt,name=show,proto3" json:"show,omitempty"`
	NeedWeekActiveDays     uint32   `protobuf:"varint,2,opt,name=need_week_active_days,json=needWeekActiveDays,proto3" json:"need_week_active_days,omitempty"`
	RemainWeekActiveDays   uint32   `protobuf:"varint,3,opt,name=remain_week_active_days,json=remainWeekActiveDays,proto3" json:"remain_week_active_days,omitempty"`
	NeedWeekPlatformDays   uint32   `protobuf:"varint,4,opt,name=need_week_platform_days,json=needWeekPlatformDays,proto3" json:"need_week_platform_days,omitempty"`
	RemainWeekPlatformDays uint32   `protobuf:"varint,5,opt,name=remain_week_platform_days,json=remainWeekPlatformDays,proto3" json:"remain_week_platform_days,omitempty"`
	WeekGiftValue          uint32   `protobuf:"varint,6,opt,name=week_gift_value,json=weekGiftValue,proto3" json:"week_gift_value,omitempty"`
	XXX_NoUnkeyedLiteral   struct{} `json:"-"`
	XXX_unrecognized       []byte   `json:"-"`
	XXX_sizecache          int32    `json:"-"`
}

func (m *AnchorMicPosTask) Reset()         { *m = AnchorMicPosTask{} }
func (m *AnchorMicPosTask) String() string { return proto.CompactTextString(m) }
func (*AnchorMicPosTask) ProtoMessage()    {}
func (*AnchorMicPosTask) Descriptor() ([]byte, []int) {
	return fileDescriptor_anchor_level_127474984171a0bf, []int{5}
}
func (m *AnchorMicPosTask) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AnchorMicPosTask.Unmarshal(m, b)
}
func (m *AnchorMicPosTask) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AnchorMicPosTask.Marshal(b, m, deterministic)
}
func (dst *AnchorMicPosTask) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AnchorMicPosTask.Merge(dst, src)
}
func (m *AnchorMicPosTask) XXX_Size() int {
	return xxx_messageInfo_AnchorMicPosTask.Size(m)
}
func (m *AnchorMicPosTask) XXX_DiscardUnknown() {
	xxx_messageInfo_AnchorMicPosTask.DiscardUnknown(m)
}

var xxx_messageInfo_AnchorMicPosTask proto.InternalMessageInfo

func (m *AnchorMicPosTask) GetShow() bool {
	if m != nil {
		return m.Show
	}
	return false
}

func (m *AnchorMicPosTask) GetNeedWeekActiveDays() uint32 {
	if m != nil {
		return m.NeedWeekActiveDays
	}
	return 0
}

func (m *AnchorMicPosTask) GetRemainWeekActiveDays() uint32 {
	if m != nil {
		return m.RemainWeekActiveDays
	}
	return 0
}

func (m *AnchorMicPosTask) GetNeedWeekPlatformDays() uint32 {
	if m != nil {
		return m.NeedWeekPlatformDays
	}
	return 0
}

func (m *AnchorMicPosTask) GetRemainWeekPlatformDays() uint32 {
	if m != nil {
		return m.RemainWeekPlatformDays
	}
	return 0
}

func (m *AnchorMicPosTask) GetWeekGiftValue() uint32 {
	if m != nil {
		return m.WeekGiftValue
	}
	return 0
}

type GetLiveAnchorTaskEntryReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetLiveAnchorTaskEntryReq) Reset()         { *m = GetLiveAnchorTaskEntryReq{} }
func (m *GetLiveAnchorTaskEntryReq) String() string { return proto.CompactTextString(m) }
func (*GetLiveAnchorTaskEntryReq) ProtoMessage()    {}
func (*GetLiveAnchorTaskEntryReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_anchor_level_127474984171a0bf, []int{6}
}
func (m *GetLiveAnchorTaskEntryReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetLiveAnchorTaskEntryReq.Unmarshal(m, b)
}
func (m *GetLiveAnchorTaskEntryReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetLiveAnchorTaskEntryReq.Marshal(b, m, deterministic)
}
func (dst *GetLiveAnchorTaskEntryReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetLiveAnchorTaskEntryReq.Merge(dst, src)
}
func (m *GetLiveAnchorTaskEntryReq) XXX_Size() int {
	return xxx_messageInfo_GetLiveAnchorTaskEntryReq.Size(m)
}
func (m *GetLiveAnchorTaskEntryReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetLiveAnchorTaskEntryReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetLiveAnchorTaskEntryReq proto.InternalMessageInfo

func (m *GetLiveAnchorTaskEntryReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetLiveAnchorTaskEntryReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetLiveAnchorTaskEntryResp struct {
	Title                string   `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	JumpUrl              string   `protobuf:"bytes,3,opt,name=jump_url,json=jumpUrl,proto3" json:"jump_url,omitempty"`
	BaseImgurl           string   `protobuf:"bytes,4,opt,name=base_imgurl,json=baseImgurl,proto3" json:"base_imgurl,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetLiveAnchorTaskEntryResp) Reset()         { *m = GetLiveAnchorTaskEntryResp{} }
func (m *GetLiveAnchorTaskEntryResp) String() string { return proto.CompactTextString(m) }
func (*GetLiveAnchorTaskEntryResp) ProtoMessage()    {}
func (*GetLiveAnchorTaskEntryResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_anchor_level_127474984171a0bf, []int{7}
}
func (m *GetLiveAnchorTaskEntryResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetLiveAnchorTaskEntryResp.Unmarshal(m, b)
}
func (m *GetLiveAnchorTaskEntryResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetLiveAnchorTaskEntryResp.Marshal(b, m, deterministic)
}
func (dst *GetLiveAnchorTaskEntryResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetLiveAnchorTaskEntryResp.Merge(dst, src)
}
func (m *GetLiveAnchorTaskEntryResp) XXX_Size() int {
	return xxx_messageInfo_GetLiveAnchorTaskEntryResp.Size(m)
}
func (m *GetLiveAnchorTaskEntryResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetLiveAnchorTaskEntryResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetLiveAnchorTaskEntryResp proto.InternalMessageInfo

func (m *GetLiveAnchorTaskEntryResp) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *GetLiveAnchorTaskEntryResp) GetJumpUrl() string {
	if m != nil {
		return m.JumpUrl
	}
	return ""
}

func (m *GetLiveAnchorTaskEntryResp) GetBaseImgurl() string {
	if m != nil {
		return m.BaseImgurl
	}
	return ""
}

type GetLiveAnchorLevelReq struct {
	Level                ANCHOR_LEVEL_TYPE `protobuf:"varint,1,opt,name=level,proto3,enum=anchor_level.ANCHOR_LEVEL_TYPE" json:"level,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetLiveAnchorLevelReq) Reset()         { *m = GetLiveAnchorLevelReq{} }
func (m *GetLiveAnchorLevelReq) String() string { return proto.CompactTextString(m) }
func (*GetLiveAnchorLevelReq) ProtoMessage()    {}
func (*GetLiveAnchorLevelReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_anchor_level_127474984171a0bf, []int{8}
}
func (m *GetLiveAnchorLevelReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetLiveAnchorLevelReq.Unmarshal(m, b)
}
func (m *GetLiveAnchorLevelReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetLiveAnchorLevelReq.Marshal(b, m, deterministic)
}
func (dst *GetLiveAnchorLevelReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetLiveAnchorLevelReq.Merge(dst, src)
}
func (m *GetLiveAnchorLevelReq) XXX_Size() int {
	return xxx_messageInfo_GetLiveAnchorLevelReq.Size(m)
}
func (m *GetLiveAnchorLevelReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetLiveAnchorLevelReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetLiveAnchorLevelReq proto.InternalMessageInfo

func (m *GetLiveAnchorLevelReq) GetLevel() ANCHOR_LEVEL_TYPE {
	if m != nil {
		return m.Level
	}
	return ANCHOR_LEVEL_TYPE_ANCHOR_LEVEL_TYPE_INVALID
}

type LiveAnchorLevelInfo struct {
	Uid                  uint32            `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Level                ANCHOR_LEVEL_TYPE `protobuf:"varint,2,opt,name=level,proto3,enum=anchor_level.ANCHOR_LEVEL_TYPE" json:"level,omitempty"`
	ItemName             string            `protobuf:"bytes,3,opt,name=item_name,json=itemName,proto3" json:"item_name,omitempty"`
	BaseImgurl           string            `protobuf:"bytes,4,opt,name=base_imgurl,json=baseImgurl,proto3" json:"base_imgurl,omitempty"`
	ShadowColor          string            `protobuf:"bytes,5,opt,name=shadow_color,json=shadowColor,proto3" json:"shadow_color,omitempty"`
	StartTime            uint32            `protobuf:"varint,6,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	EndTime              uint32            `protobuf:"varint,7,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *LiveAnchorLevelInfo) Reset()         { *m = LiveAnchorLevelInfo{} }
func (m *LiveAnchorLevelInfo) String() string { return proto.CompactTextString(m) }
func (*LiveAnchorLevelInfo) ProtoMessage()    {}
func (*LiveAnchorLevelInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_anchor_level_127474984171a0bf, []int{9}
}
func (m *LiveAnchorLevelInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LiveAnchorLevelInfo.Unmarshal(m, b)
}
func (m *LiveAnchorLevelInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LiveAnchorLevelInfo.Marshal(b, m, deterministic)
}
func (dst *LiveAnchorLevelInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LiveAnchorLevelInfo.Merge(dst, src)
}
func (m *LiveAnchorLevelInfo) XXX_Size() int {
	return xxx_messageInfo_LiveAnchorLevelInfo.Size(m)
}
func (m *LiveAnchorLevelInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_LiveAnchorLevelInfo.DiscardUnknown(m)
}

var xxx_messageInfo_LiveAnchorLevelInfo proto.InternalMessageInfo

func (m *LiveAnchorLevelInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *LiveAnchorLevelInfo) GetLevel() ANCHOR_LEVEL_TYPE {
	if m != nil {
		return m.Level
	}
	return ANCHOR_LEVEL_TYPE_ANCHOR_LEVEL_TYPE_INVALID
}

func (m *LiveAnchorLevelInfo) GetItemName() string {
	if m != nil {
		return m.ItemName
	}
	return ""
}

func (m *LiveAnchorLevelInfo) GetBaseImgurl() string {
	if m != nil {
		return m.BaseImgurl
	}
	return ""
}

func (m *LiveAnchorLevelInfo) GetShadowColor() string {
	if m != nil {
		return m.ShadowColor
	}
	return ""
}

func (m *LiveAnchorLevelInfo) GetStartTime() uint32 {
	if m != nil {
		return m.StartTime
	}
	return 0
}

func (m *LiveAnchorLevelInfo) GetEndTime() uint32 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

type GetLiveAnchorLevelResp struct {
	List                 []*LiveAnchorLevelInfo `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *GetLiveAnchorLevelResp) Reset()         { *m = GetLiveAnchorLevelResp{} }
func (m *GetLiveAnchorLevelResp) String() string { return proto.CompactTextString(m) }
func (*GetLiveAnchorLevelResp) ProtoMessage()    {}
func (*GetLiveAnchorLevelResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_anchor_level_127474984171a0bf, []int{10}
}
func (m *GetLiveAnchorLevelResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetLiveAnchorLevelResp.Unmarshal(m, b)
}
func (m *GetLiveAnchorLevelResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetLiveAnchorLevelResp.Marshal(b, m, deterministic)
}
func (dst *GetLiveAnchorLevelResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetLiveAnchorLevelResp.Merge(dst, src)
}
func (m *GetLiveAnchorLevelResp) XXX_Size() int {
	return xxx_messageInfo_GetLiveAnchorLevelResp.Size(m)
}
func (m *GetLiveAnchorLevelResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetLiveAnchorLevelResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetLiveAnchorLevelResp proto.InternalMessageInfo

func (m *GetLiveAnchorLevelResp) GetList() []*LiveAnchorLevelInfo {
	if m != nil {
		return m.List
	}
	return nil
}

type GetLiveAnchorLevelByUidReq struct {
	Uids                 []uint32 `protobuf:"varint,1,rep,packed,name=uids,proto3" json:"uids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetLiveAnchorLevelByUidReq) Reset()         { *m = GetLiveAnchorLevelByUidReq{} }
func (m *GetLiveAnchorLevelByUidReq) String() string { return proto.CompactTextString(m) }
func (*GetLiveAnchorLevelByUidReq) ProtoMessage()    {}
func (*GetLiveAnchorLevelByUidReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_anchor_level_127474984171a0bf, []int{11}
}
func (m *GetLiveAnchorLevelByUidReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetLiveAnchorLevelByUidReq.Unmarshal(m, b)
}
func (m *GetLiveAnchorLevelByUidReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetLiveAnchorLevelByUidReq.Marshal(b, m, deterministic)
}
func (dst *GetLiveAnchorLevelByUidReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetLiveAnchorLevelByUidReq.Merge(dst, src)
}
func (m *GetLiveAnchorLevelByUidReq) XXX_Size() int {
	return xxx_messageInfo_GetLiveAnchorLevelByUidReq.Size(m)
}
func (m *GetLiveAnchorLevelByUidReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetLiveAnchorLevelByUidReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetLiveAnchorLevelByUidReq proto.InternalMessageInfo

func (m *GetLiveAnchorLevelByUidReq) GetUids() []uint32 {
	if m != nil {
		return m.Uids
	}
	return nil
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
type GetLiveAnchorLevelByUidResp struct {
	Uid2AnchorLevel      map[uint32]*LiveAnchorLevelInfo `protobuf:"bytes,1,rep,name=uid2AnchorLevel,proto3" json:"uid2AnchorLevel,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}                        `json:"-"`
	XXX_unrecognized     []byte                          `json:"-"`
	XXX_sizecache        int32                           `json:"-"`
}

func (m *GetLiveAnchorLevelByUidResp) Reset()         { *m = GetLiveAnchorLevelByUidResp{} }
func (m *GetLiveAnchorLevelByUidResp) String() string { return proto.CompactTextString(m) }
func (*GetLiveAnchorLevelByUidResp) ProtoMessage()    {}
func (*GetLiveAnchorLevelByUidResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_anchor_level_127474984171a0bf, []int{12}
}
func (m *GetLiveAnchorLevelByUidResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetLiveAnchorLevelByUidResp.Unmarshal(m, b)
}
func (m *GetLiveAnchorLevelByUidResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetLiveAnchorLevelByUidResp.Marshal(b, m, deterministic)
}
func (dst *GetLiveAnchorLevelByUidResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetLiveAnchorLevelByUidResp.Merge(dst, src)
}
func (m *GetLiveAnchorLevelByUidResp) XXX_Size() int {
	return xxx_messageInfo_GetLiveAnchorLevelByUidResp.Size(m)
}
func (m *GetLiveAnchorLevelByUidResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetLiveAnchorLevelByUidResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetLiveAnchorLevelByUidResp proto.InternalMessageInfo

func (m *GetLiveAnchorLevelByUidResp) GetUid2AnchorLevel() map[uint32]*LiveAnchorLevelInfo {
	if m != nil {
		return m.Uid2AnchorLevel
	}
	return nil
}

type GetAnchorLevelMonthTaskResp struct {
	CurrLevelName           string   `protobuf:"bytes,1,opt,name=curr_level_name,json=currLevelName,proto3" json:"curr_level_name,omitempty"`
	NeedMonthActiveDays     uint32   `protobuf:"varint,3,opt,name=need_month_active_days,json=needMonthActiveDays,proto3" json:"need_month_active_days,omitempty"`
	RemainMonthActiveDays   uint32   `protobuf:"varint,4,opt,name=remain_month_active_days,json=remainMonthActiveDays,proto3" json:"remain_month_active_days,omitempty"`
	NeedMonthPlatformDays   uint32   `protobuf:"varint,5,opt,name=need_month_platform_days,json=needMonthPlatformDays,proto3" json:"need_month_platform_days,omitempty"`
	RemainMonthPlatformDays uint32   `protobuf:"varint,6,opt,name=remain_month_platform_days,json=remainMonthPlatformDays,proto3" json:"remain_month_platform_days,omitempty"`
	MonthNewFansCnt         uint32   `protobuf:"varint,7,opt,name=month_new_fans_cnt,json=monthNewFansCnt,proto3" json:"month_new_fans_cnt,omitempty"`
	MonthIncomeValue        uint32   `protobuf:"varint,8,opt,name=month_income_value,json=monthIncomeValue,proto3" json:"month_income_value,omitempty"`
	NeedMonthIncomeValue    uint32   `protobuf:"varint,9,opt,name=need_month_income_value,json=needMonthIncomeValue,proto3" json:"need_month_income_value,omitempty"`
	MonthConsumerCnt        uint32   `protobuf:"varint,10,opt,name=month_consumer_cnt,json=monthConsumerCnt,proto3" json:"month_consumer_cnt,omitempty"`
	IsViolation             bool     `protobuf:"varint,11,opt,name=is_violation,json=isViolation,proto3" json:"is_violation,omitempty"`
	MonthActiveDays         uint32   `protobuf:"varint,12,opt,name=month_active_days,json=monthActiveDays,proto3" json:"month_active_days,omitempty"`
	AdvancedTaskDone        bool     `protobuf:"varint,13,opt,name=advanced_task_done,json=advancedTaskDone,proto3" json:"advanced_task_done,omitempty"`
	XXX_NoUnkeyedLiteral    struct{} `json:"-"`
	XXX_unrecognized        []byte   `json:"-"`
	XXX_sizecache           int32    `json:"-"`
}

func (m *GetAnchorLevelMonthTaskResp) Reset()         { *m = GetAnchorLevelMonthTaskResp{} }
func (m *GetAnchorLevelMonthTaskResp) String() string { return proto.CompactTextString(m) }
func (*GetAnchorLevelMonthTaskResp) ProtoMessage()    {}
func (*GetAnchorLevelMonthTaskResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_anchor_level_127474984171a0bf, []int{13}
}
func (m *GetAnchorLevelMonthTaskResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAnchorLevelMonthTaskResp.Unmarshal(m, b)
}
func (m *GetAnchorLevelMonthTaskResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAnchorLevelMonthTaskResp.Marshal(b, m, deterministic)
}
func (dst *GetAnchorLevelMonthTaskResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAnchorLevelMonthTaskResp.Merge(dst, src)
}
func (m *GetAnchorLevelMonthTaskResp) XXX_Size() int {
	return xxx_messageInfo_GetAnchorLevelMonthTaskResp.Size(m)
}
func (m *GetAnchorLevelMonthTaskResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAnchorLevelMonthTaskResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAnchorLevelMonthTaskResp proto.InternalMessageInfo

func (m *GetAnchorLevelMonthTaskResp) GetCurrLevelName() string {
	if m != nil {
		return m.CurrLevelName
	}
	return ""
}

func (m *GetAnchorLevelMonthTaskResp) GetNeedMonthActiveDays() uint32 {
	if m != nil {
		return m.NeedMonthActiveDays
	}
	return 0
}

func (m *GetAnchorLevelMonthTaskResp) GetRemainMonthActiveDays() uint32 {
	if m != nil {
		return m.RemainMonthActiveDays
	}
	return 0
}

func (m *GetAnchorLevelMonthTaskResp) GetNeedMonthPlatformDays() uint32 {
	if m != nil {
		return m.NeedMonthPlatformDays
	}
	return 0
}

func (m *GetAnchorLevelMonthTaskResp) GetRemainMonthPlatformDays() uint32 {
	if m != nil {
		return m.RemainMonthPlatformDays
	}
	return 0
}

func (m *GetAnchorLevelMonthTaskResp) GetMonthNewFansCnt() uint32 {
	if m != nil {
		return m.MonthNewFansCnt
	}
	return 0
}

func (m *GetAnchorLevelMonthTaskResp) GetMonthIncomeValue() uint32 {
	if m != nil {
		return m.MonthIncomeValue
	}
	return 0
}

func (m *GetAnchorLevelMonthTaskResp) GetNeedMonthIncomeValue() uint32 {
	if m != nil {
		return m.NeedMonthIncomeValue
	}
	return 0
}

func (m *GetAnchorLevelMonthTaskResp) GetMonthConsumerCnt() uint32 {
	if m != nil {
		return m.MonthConsumerCnt
	}
	return 0
}

func (m *GetAnchorLevelMonthTaskResp) GetIsViolation() bool {
	if m != nil {
		return m.IsViolation
	}
	return false
}

func (m *GetAnchorLevelMonthTaskResp) GetMonthActiveDays() uint32 {
	if m != nil {
		return m.MonthActiveDays
	}
	return 0
}

func (m *GetAnchorLevelMonthTaskResp) GetAdvancedTaskDone() bool {
	if m != nil {
		return m.AdvancedTaskDone
	}
	return false
}

type TestSendImReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Level                uint32   `protobuf:"varint,2,opt,name=level,proto3" json:"level,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TestSendImReq) Reset()         { *m = TestSendImReq{} }
func (m *TestSendImReq) String() string { return proto.CompactTextString(m) }
func (*TestSendImReq) ProtoMessage()    {}
func (*TestSendImReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_anchor_level_127474984171a0bf, []int{14}
}
func (m *TestSendImReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TestSendImReq.Unmarshal(m, b)
}
func (m *TestSendImReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TestSendImReq.Marshal(b, m, deterministic)
}
func (dst *TestSendImReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TestSendImReq.Merge(dst, src)
}
func (m *TestSendImReq) XXX_Size() int {
	return xxx_messageInfo_TestSendImReq.Size(m)
}
func (m *TestSendImReq) XXX_DiscardUnknown() {
	xxx_messageInfo_TestSendImReq.DiscardUnknown(m)
}

var xxx_messageInfo_TestSendImReq proto.InternalMessageInfo

func (m *TestSendImReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *TestSendImReq) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

type ReplenishAnchorLevelReq struct {
	SettleMonth          uint32                                              `protobuf:"varint,1,opt,name=settle_month,json=settleMonth,proto3" json:"settle_month,omitempty"`
	InfoList             []*ReplenishAnchorLevelReq_ReplenishAnchorLevelInfo `protobuf:"bytes,2,rep,name=info_list,json=infoList,proto3" json:"info_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                                            `json:"-"`
	XXX_unrecognized     []byte                                              `json:"-"`
	XXX_sizecache        int32                                               `json:"-"`
}

func (m *ReplenishAnchorLevelReq) Reset()         { *m = ReplenishAnchorLevelReq{} }
func (m *ReplenishAnchorLevelReq) String() string { return proto.CompactTextString(m) }
func (*ReplenishAnchorLevelReq) ProtoMessage()    {}
func (*ReplenishAnchorLevelReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_anchor_level_127474984171a0bf, []int{15}
}
func (m *ReplenishAnchorLevelReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReplenishAnchorLevelReq.Unmarshal(m, b)
}
func (m *ReplenishAnchorLevelReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReplenishAnchorLevelReq.Marshal(b, m, deterministic)
}
func (dst *ReplenishAnchorLevelReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReplenishAnchorLevelReq.Merge(dst, src)
}
func (m *ReplenishAnchorLevelReq) XXX_Size() int {
	return xxx_messageInfo_ReplenishAnchorLevelReq.Size(m)
}
func (m *ReplenishAnchorLevelReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ReplenishAnchorLevelReq.DiscardUnknown(m)
}

var xxx_messageInfo_ReplenishAnchorLevelReq proto.InternalMessageInfo

func (m *ReplenishAnchorLevelReq) GetSettleMonth() uint32 {
	if m != nil {
		return m.SettleMonth
	}
	return 0
}

func (m *ReplenishAnchorLevelReq) GetInfoList() []*ReplenishAnchorLevelReq_ReplenishAnchorLevelInfo {
	if m != nil {
		return m.InfoList
	}
	return nil
}

type ReplenishAnchorLevelReq_ReplenishAnchorLevelInfo struct {
	AnchorUid            uint32   `protobuf:"varint,1,opt,name=anchor_uid,json=anchorUid,proto3" json:"anchor_uid,omitempty"`
	AnchorLevel          uint32   `protobuf:"varint,2,opt,name=anchor_level,json=anchorLevel,proto3" json:"anchor_level,omitempty"`
	AdvancedTaskDone     uint32   `protobuf:"varint,3,opt,name=advanced_task_done,json=advancedTaskDone,proto3" json:"advanced_task_done,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReplenishAnchorLevelReq_ReplenishAnchorLevelInfo) Reset() {
	*m = ReplenishAnchorLevelReq_ReplenishAnchorLevelInfo{}
}
func (m *ReplenishAnchorLevelReq_ReplenishAnchorLevelInfo) String() string {
	return proto.CompactTextString(m)
}
func (*ReplenishAnchorLevelReq_ReplenishAnchorLevelInfo) ProtoMessage() {}
func (*ReplenishAnchorLevelReq_ReplenishAnchorLevelInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_anchor_level_127474984171a0bf, []int{15, 0}
}
func (m *ReplenishAnchorLevelReq_ReplenishAnchorLevelInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReplenishAnchorLevelReq_ReplenishAnchorLevelInfo.Unmarshal(m, b)
}
func (m *ReplenishAnchorLevelReq_ReplenishAnchorLevelInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReplenishAnchorLevelReq_ReplenishAnchorLevelInfo.Marshal(b, m, deterministic)
}
func (dst *ReplenishAnchorLevelReq_ReplenishAnchorLevelInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReplenishAnchorLevelReq_ReplenishAnchorLevelInfo.Merge(dst, src)
}
func (m *ReplenishAnchorLevelReq_ReplenishAnchorLevelInfo) XXX_Size() int {
	return xxx_messageInfo_ReplenishAnchorLevelReq_ReplenishAnchorLevelInfo.Size(m)
}
func (m *ReplenishAnchorLevelReq_ReplenishAnchorLevelInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ReplenishAnchorLevelReq_ReplenishAnchorLevelInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ReplenishAnchorLevelReq_ReplenishAnchorLevelInfo proto.InternalMessageInfo

func (m *ReplenishAnchorLevelReq_ReplenishAnchorLevelInfo) GetAnchorUid() uint32 {
	if m != nil {
		return m.AnchorUid
	}
	return 0
}

func (m *ReplenishAnchorLevelReq_ReplenishAnchorLevelInfo) GetAnchorLevel() uint32 {
	if m != nil {
		return m.AnchorLevel
	}
	return 0
}

func (m *ReplenishAnchorLevelReq_ReplenishAnchorLevelInfo) GetAdvancedTaskDone() uint32 {
	if m != nil {
		return m.AdvancedTaskDone
	}
	return 0
}

type ReplenishAnchorLevelResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReplenishAnchorLevelResp) Reset()         { *m = ReplenishAnchorLevelResp{} }
func (m *ReplenishAnchorLevelResp) String() string { return proto.CompactTextString(m) }
func (*ReplenishAnchorLevelResp) ProtoMessage()    {}
func (*ReplenishAnchorLevelResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_anchor_level_127474984171a0bf, []int{16}
}
func (m *ReplenishAnchorLevelResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReplenishAnchorLevelResp.Unmarshal(m, b)
}
func (m *ReplenishAnchorLevelResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReplenishAnchorLevelResp.Marshal(b, m, deterministic)
}
func (dst *ReplenishAnchorLevelResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReplenishAnchorLevelResp.Merge(dst, src)
}
func (m *ReplenishAnchorLevelResp) XXX_Size() int {
	return xxx_messageInfo_ReplenishAnchorLevelResp.Size(m)
}
func (m *ReplenishAnchorLevelResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ReplenishAnchorLevelResp.DiscardUnknown(m)
}

var xxx_messageInfo_ReplenishAnchorLevelResp proto.InternalMessageInfo

type GetAnchorLevelResp struct {
	AnchorUid            uint32            `protobuf:"varint,1,opt,name=anchor_uid,json=anchorUid,proto3" json:"anchor_uid,omitempty"`
	AnchorLevel          ANCHOR_LEVEL_TYPE `protobuf:"varint,2,opt,name=anchor_level,json=anchorLevel,proto3,enum=anchor_level.ANCHOR_LEVEL_TYPE" json:"anchor_level,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetAnchorLevelResp) Reset()         { *m = GetAnchorLevelResp{} }
func (m *GetAnchorLevelResp) String() string { return proto.CompactTextString(m) }
func (*GetAnchorLevelResp) ProtoMessage()    {}
func (*GetAnchorLevelResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_anchor_level_127474984171a0bf, []int{17}
}
func (m *GetAnchorLevelResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAnchorLevelResp.Unmarshal(m, b)
}
func (m *GetAnchorLevelResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAnchorLevelResp.Marshal(b, m, deterministic)
}
func (dst *GetAnchorLevelResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAnchorLevelResp.Merge(dst, src)
}
func (m *GetAnchorLevelResp) XXX_Size() int {
	return xxx_messageInfo_GetAnchorLevelResp.Size(m)
}
func (m *GetAnchorLevelResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAnchorLevelResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAnchorLevelResp proto.InternalMessageInfo

func (m *GetAnchorLevelResp) GetAnchorUid() uint32 {
	if m != nil {
		return m.AnchorUid
	}
	return 0
}

func (m *GetAnchorLevelResp) GetAnchorLevel() ANCHOR_LEVEL_TYPE {
	if m != nil {
		return m.AnchorLevel
	}
	return ANCHOR_LEVEL_TYPE_ANCHOR_LEVEL_TYPE_INVALID
}

type SettleNewTaskAwardReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SettleNewTaskAwardReq) Reset()         { *m = SettleNewTaskAwardReq{} }
func (m *SettleNewTaskAwardReq) String() string { return proto.CompactTextString(m) }
func (*SettleNewTaskAwardReq) ProtoMessage()    {}
func (*SettleNewTaskAwardReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_anchor_level_127474984171a0bf, []int{18}
}
func (m *SettleNewTaskAwardReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SettleNewTaskAwardReq.Unmarshal(m, b)
}
func (m *SettleNewTaskAwardReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SettleNewTaskAwardReq.Marshal(b, m, deterministic)
}
func (dst *SettleNewTaskAwardReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SettleNewTaskAwardReq.Merge(dst, src)
}
func (m *SettleNewTaskAwardReq) XXX_Size() int {
	return xxx_messageInfo_SettleNewTaskAwardReq.Size(m)
}
func (m *SettleNewTaskAwardReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SettleNewTaskAwardReq.DiscardUnknown(m)
}

var xxx_messageInfo_SettleNewTaskAwardReq proto.InternalMessageInfo

func init() {
	proto.RegisterType((*UidReq)(nil), "anchor_level.UidReq")
	proto.RegisterType((*ManualSettleAnchorLevelReq)(nil), "anchor_level.ManualSettleAnchorLevelReq")
	proto.RegisterType((*UidCheckLevelReq)(nil), "anchor_level.UidCheckLevelReq")
	proto.RegisterType((*Empty)(nil), "anchor_level.Empty")
	proto.RegisterType((*AnchorLevelNewTask)(nil), "anchor_level.AnchorLevelNewTask")
	proto.RegisterType((*AnchorMicPosTask)(nil), "anchor_level.AnchorMicPosTask")
	proto.RegisterType((*GetLiveAnchorTaskEntryReq)(nil), "anchor_level.GetLiveAnchorTaskEntryReq")
	proto.RegisterType((*GetLiveAnchorTaskEntryResp)(nil), "anchor_level.GetLiveAnchorTaskEntryResp")
	proto.RegisterType((*GetLiveAnchorLevelReq)(nil), "anchor_level.GetLiveAnchorLevelReq")
	proto.RegisterType((*LiveAnchorLevelInfo)(nil), "anchor_level.LiveAnchorLevelInfo")
	proto.RegisterType((*GetLiveAnchorLevelResp)(nil), "anchor_level.GetLiveAnchorLevelResp")
	proto.RegisterType((*GetLiveAnchorLevelByUidReq)(nil), "anchor_level.GetLiveAnchorLevelByUidReq")
	proto.RegisterType((*GetLiveAnchorLevelByUidResp)(nil), "anchor_level.GetLiveAnchorLevelByUidResp")
	proto.RegisterMapType((map[uint32]*LiveAnchorLevelInfo)(nil), "anchor_level.GetLiveAnchorLevelByUidResp.Uid2AnchorLevelEntry")
	proto.RegisterType((*GetAnchorLevelMonthTaskResp)(nil), "anchor_level.GetAnchorLevelMonthTaskResp")
	proto.RegisterType((*TestSendImReq)(nil), "anchor_level.TestSendImReq")
	proto.RegisterType((*ReplenishAnchorLevelReq)(nil), "anchor_level.ReplenishAnchorLevelReq")
	proto.RegisterType((*ReplenishAnchorLevelReq_ReplenishAnchorLevelInfo)(nil), "anchor_level.ReplenishAnchorLevelReq.ReplenishAnchorLevelInfo")
	proto.RegisterType((*ReplenishAnchorLevelResp)(nil), "anchor_level.ReplenishAnchorLevelResp")
	proto.RegisterType((*GetAnchorLevelResp)(nil), "anchor_level.GetAnchorLevelResp")
	proto.RegisterType((*SettleNewTaskAwardReq)(nil), "anchor_level.SettleNewTaskAwardReq")
	proto.RegisterEnum("anchor_level.ANCHOR_LEVEL_TYPE", ANCHOR_LEVEL_TYPE_name, ANCHOR_LEVEL_TYPE_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// AnchorLevelClient is the client API for AnchorLevel service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type AnchorLevelClient interface {
	// 获取新主播任务状态
	GetAnchorLevelNewTask(ctx context.Context, in *UidReq, opts ...grpc.CallOption) (*AnchorLevelNewTask, error)
	// 获取麦位任务
	GetAnchorMicPosTask(ctx context.Context, in *UidReq, opts ...grpc.CallOption) (*AnchorMicPosTask, error)
	// 设置主播考核通过
	SetAnchorCheckPass(ctx context.Context, in *UidCheckLevelReq, opts ...grpc.CallOption) (*Empty, error)
	// 手动触发麦位任务奖励
	ManualSendMicPosReward(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*Empty, error)
	// 手动触发新主播任务奖励
	ManualSendNewAnchorTaskReward(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*Empty, error)
	// 手动触发麦位任务提醒
	ManualSendAllNotMicPosMsg(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*Empty, error)
	// 直播间主播任务入口
	GetLiveAnchorTaskEntry(ctx context.Context, in *GetLiveAnchorTaskEntryReq, opts ...grpc.CallOption) (*GetLiveAnchorTaskEntryResp, error)
	GetLiveAnchorLevel(ctx context.Context, in *GetLiveAnchorLevelReq, opts ...grpc.CallOption) (*GetLiveAnchorLevelResp, error)
	GetLiveAnchorLevelByUid(ctx context.Context, in *GetLiveAnchorLevelByUidReq, opts ...grpc.CallOption) (*GetLiveAnchorLevelByUidResp, error)
	GetAnchorLevelMonthTask(ctx context.Context, in *UidReq, opts ...grpc.CallOption) (*GetAnchorLevelMonthTaskResp, error)
	GetAnchorLevel(ctx context.Context, in *UidReq, opts ...grpc.CallOption) (*GetAnchorLevelResp, error)
	// 补发主播等级(包含发奖)
	ReplenishAnchorLevel(ctx context.Context, in *ReplenishAnchorLevelReq, opts ...grpc.CallOption) (*ReplenishAnchorLevelResp, error)
	// 手动结算主播等级(包含发奖)
	ManualSettleAnchorLevel(ctx context.Context, in *ManualSettleAnchorLevelReq, opts ...grpc.CallOption) (*Empty, error)
	SettleNewTaskAward(ctx context.Context, in *SettleNewTaskAwardReq, opts ...grpc.CallOption) (*Empty, error)
}

type anchorLevelClient struct {
	cc *grpc.ClientConn
}

func NewAnchorLevelClient(cc *grpc.ClientConn) AnchorLevelClient {
	return &anchorLevelClient{cc}
}

func (c *anchorLevelClient) GetAnchorLevelNewTask(ctx context.Context, in *UidReq, opts ...grpc.CallOption) (*AnchorLevelNewTask, error) {
	out := new(AnchorLevelNewTask)
	err := c.cc.Invoke(ctx, "/anchor_level.AnchorLevel/GetAnchorLevelNewTask", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *anchorLevelClient) GetAnchorMicPosTask(ctx context.Context, in *UidReq, opts ...grpc.CallOption) (*AnchorMicPosTask, error) {
	out := new(AnchorMicPosTask)
	err := c.cc.Invoke(ctx, "/anchor_level.AnchorLevel/GetAnchorMicPosTask", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *anchorLevelClient) SetAnchorCheckPass(ctx context.Context, in *UidCheckLevelReq, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, "/anchor_level.AnchorLevel/SetAnchorCheckPass", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *anchorLevelClient) ManualSendMicPosReward(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, "/anchor_level.AnchorLevel/ManualSendMicPosReward", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *anchorLevelClient) ManualSendNewAnchorTaskReward(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, "/anchor_level.AnchorLevel/ManualSendNewAnchorTaskReward", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *anchorLevelClient) ManualSendAllNotMicPosMsg(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, "/anchor_level.AnchorLevel/ManualSendAllNotMicPosMsg", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *anchorLevelClient) GetLiveAnchorTaskEntry(ctx context.Context, in *GetLiveAnchorTaskEntryReq, opts ...grpc.CallOption) (*GetLiveAnchorTaskEntryResp, error) {
	out := new(GetLiveAnchorTaskEntryResp)
	err := c.cc.Invoke(ctx, "/anchor_level.AnchorLevel/GetLiveAnchorTaskEntry", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *anchorLevelClient) GetLiveAnchorLevel(ctx context.Context, in *GetLiveAnchorLevelReq, opts ...grpc.CallOption) (*GetLiveAnchorLevelResp, error) {
	out := new(GetLiveAnchorLevelResp)
	err := c.cc.Invoke(ctx, "/anchor_level.AnchorLevel/GetLiveAnchorLevel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *anchorLevelClient) GetLiveAnchorLevelByUid(ctx context.Context, in *GetLiveAnchorLevelByUidReq, opts ...grpc.CallOption) (*GetLiveAnchorLevelByUidResp, error) {
	out := new(GetLiveAnchorLevelByUidResp)
	err := c.cc.Invoke(ctx, "/anchor_level.AnchorLevel/GetLiveAnchorLevelByUid", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *anchorLevelClient) GetAnchorLevelMonthTask(ctx context.Context, in *UidReq, opts ...grpc.CallOption) (*GetAnchorLevelMonthTaskResp, error) {
	out := new(GetAnchorLevelMonthTaskResp)
	err := c.cc.Invoke(ctx, "/anchor_level.AnchorLevel/GetAnchorLevelMonthTask", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *anchorLevelClient) GetAnchorLevel(ctx context.Context, in *UidReq, opts ...grpc.CallOption) (*GetAnchorLevelResp, error) {
	out := new(GetAnchorLevelResp)
	err := c.cc.Invoke(ctx, "/anchor_level.AnchorLevel/GetAnchorLevel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *anchorLevelClient) ReplenishAnchorLevel(ctx context.Context, in *ReplenishAnchorLevelReq, opts ...grpc.CallOption) (*ReplenishAnchorLevelResp, error) {
	out := new(ReplenishAnchorLevelResp)
	err := c.cc.Invoke(ctx, "/anchor_level.AnchorLevel/ReplenishAnchorLevel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *anchorLevelClient) ManualSettleAnchorLevel(ctx context.Context, in *ManualSettleAnchorLevelReq, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, "/anchor_level.AnchorLevel/ManualSettleAnchorLevel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *anchorLevelClient) SettleNewTaskAward(ctx context.Context, in *SettleNewTaskAwardReq, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, "/anchor_level.AnchorLevel/SettleNewTaskAward", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AnchorLevelServer is the server API for AnchorLevel service.
type AnchorLevelServer interface {
	// 获取新主播任务状态
	GetAnchorLevelNewTask(context.Context, *UidReq) (*AnchorLevelNewTask, error)
	// 获取麦位任务
	GetAnchorMicPosTask(context.Context, *UidReq) (*AnchorMicPosTask, error)
	// 设置主播考核通过
	SetAnchorCheckPass(context.Context, *UidCheckLevelReq) (*Empty, error)
	// 手动触发麦位任务奖励
	ManualSendMicPosReward(context.Context, *Empty) (*Empty, error)
	// 手动触发新主播任务奖励
	ManualSendNewAnchorTaskReward(context.Context, *Empty) (*Empty, error)
	// 手动触发麦位任务提醒
	ManualSendAllNotMicPosMsg(context.Context, *Empty) (*Empty, error)
	// 直播间主播任务入口
	GetLiveAnchorTaskEntry(context.Context, *GetLiveAnchorTaskEntryReq) (*GetLiveAnchorTaskEntryResp, error)
	GetLiveAnchorLevel(context.Context, *GetLiveAnchorLevelReq) (*GetLiveAnchorLevelResp, error)
	GetLiveAnchorLevelByUid(context.Context, *GetLiveAnchorLevelByUidReq) (*GetLiveAnchorLevelByUidResp, error)
	GetAnchorLevelMonthTask(context.Context, *UidReq) (*GetAnchorLevelMonthTaskResp, error)
	GetAnchorLevel(context.Context, *UidReq) (*GetAnchorLevelResp, error)
	// 补发主播等级(包含发奖)
	ReplenishAnchorLevel(context.Context, *ReplenishAnchorLevelReq) (*ReplenishAnchorLevelResp, error)
	// 手动结算主播等级(包含发奖)
	ManualSettleAnchorLevel(context.Context, *ManualSettleAnchorLevelReq) (*Empty, error)
	SettleNewTaskAward(context.Context, *SettleNewTaskAwardReq) (*Empty, error)
}

func RegisterAnchorLevelServer(s *grpc.Server, srv AnchorLevelServer) {
	s.RegisterService(&_AnchorLevel_serviceDesc, srv)
}

func _AnchorLevel_GetAnchorLevelNewTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UidReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AnchorLevelServer).GetAnchorLevelNewTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/anchor_level.AnchorLevel/GetAnchorLevelNewTask",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AnchorLevelServer).GetAnchorLevelNewTask(ctx, req.(*UidReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AnchorLevel_GetAnchorMicPosTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UidReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AnchorLevelServer).GetAnchorMicPosTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/anchor_level.AnchorLevel/GetAnchorMicPosTask",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AnchorLevelServer).GetAnchorMicPosTask(ctx, req.(*UidReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AnchorLevel_SetAnchorCheckPass_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UidCheckLevelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AnchorLevelServer).SetAnchorCheckPass(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/anchor_level.AnchorLevel/SetAnchorCheckPass",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AnchorLevelServer).SetAnchorCheckPass(ctx, req.(*UidCheckLevelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AnchorLevel_ManualSendMicPosReward_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AnchorLevelServer).ManualSendMicPosReward(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/anchor_level.AnchorLevel/ManualSendMicPosReward",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AnchorLevelServer).ManualSendMicPosReward(ctx, req.(*Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _AnchorLevel_ManualSendNewAnchorTaskReward_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AnchorLevelServer).ManualSendNewAnchorTaskReward(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/anchor_level.AnchorLevel/ManualSendNewAnchorTaskReward",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AnchorLevelServer).ManualSendNewAnchorTaskReward(ctx, req.(*Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _AnchorLevel_ManualSendAllNotMicPosMsg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AnchorLevelServer).ManualSendAllNotMicPosMsg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/anchor_level.AnchorLevel/ManualSendAllNotMicPosMsg",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AnchorLevelServer).ManualSendAllNotMicPosMsg(ctx, req.(*Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _AnchorLevel_GetLiveAnchorTaskEntry_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLiveAnchorTaskEntryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AnchorLevelServer).GetLiveAnchorTaskEntry(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/anchor_level.AnchorLevel/GetLiveAnchorTaskEntry",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AnchorLevelServer).GetLiveAnchorTaskEntry(ctx, req.(*GetLiveAnchorTaskEntryReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AnchorLevel_GetLiveAnchorLevel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLiveAnchorLevelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AnchorLevelServer).GetLiveAnchorLevel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/anchor_level.AnchorLevel/GetLiveAnchorLevel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AnchorLevelServer).GetLiveAnchorLevel(ctx, req.(*GetLiveAnchorLevelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AnchorLevel_GetLiveAnchorLevelByUid_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLiveAnchorLevelByUidReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AnchorLevelServer).GetLiveAnchorLevelByUid(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/anchor_level.AnchorLevel/GetLiveAnchorLevelByUid",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AnchorLevelServer).GetLiveAnchorLevelByUid(ctx, req.(*GetLiveAnchorLevelByUidReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AnchorLevel_GetAnchorLevelMonthTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UidReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AnchorLevelServer).GetAnchorLevelMonthTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/anchor_level.AnchorLevel/GetAnchorLevelMonthTask",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AnchorLevelServer).GetAnchorLevelMonthTask(ctx, req.(*UidReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AnchorLevel_GetAnchorLevel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UidReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AnchorLevelServer).GetAnchorLevel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/anchor_level.AnchorLevel/GetAnchorLevel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AnchorLevelServer).GetAnchorLevel(ctx, req.(*UidReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AnchorLevel_ReplenishAnchorLevel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReplenishAnchorLevelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AnchorLevelServer).ReplenishAnchorLevel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/anchor_level.AnchorLevel/ReplenishAnchorLevel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AnchorLevelServer).ReplenishAnchorLevel(ctx, req.(*ReplenishAnchorLevelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AnchorLevel_ManualSettleAnchorLevel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ManualSettleAnchorLevelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AnchorLevelServer).ManualSettleAnchorLevel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/anchor_level.AnchorLevel/ManualSettleAnchorLevel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AnchorLevelServer).ManualSettleAnchorLevel(ctx, req.(*ManualSettleAnchorLevelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AnchorLevel_SettleNewTaskAward_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SettleNewTaskAwardReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AnchorLevelServer).SettleNewTaskAward(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/anchor_level.AnchorLevel/SettleNewTaskAward",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AnchorLevelServer).SettleNewTaskAward(ctx, req.(*SettleNewTaskAwardReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _AnchorLevel_serviceDesc = grpc.ServiceDesc{
	ServiceName: "anchor_level.AnchorLevel",
	HandlerType: (*AnchorLevelServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetAnchorLevelNewTask",
			Handler:    _AnchorLevel_GetAnchorLevelNewTask_Handler,
		},
		{
			MethodName: "GetAnchorMicPosTask",
			Handler:    _AnchorLevel_GetAnchorMicPosTask_Handler,
		},
		{
			MethodName: "SetAnchorCheckPass",
			Handler:    _AnchorLevel_SetAnchorCheckPass_Handler,
		},
		{
			MethodName: "ManualSendMicPosReward",
			Handler:    _AnchorLevel_ManualSendMicPosReward_Handler,
		},
		{
			MethodName: "ManualSendNewAnchorTaskReward",
			Handler:    _AnchorLevel_ManualSendNewAnchorTaskReward_Handler,
		},
		{
			MethodName: "ManualSendAllNotMicPosMsg",
			Handler:    _AnchorLevel_ManualSendAllNotMicPosMsg_Handler,
		},
		{
			MethodName: "GetLiveAnchorTaskEntry",
			Handler:    _AnchorLevel_GetLiveAnchorTaskEntry_Handler,
		},
		{
			MethodName: "GetLiveAnchorLevel",
			Handler:    _AnchorLevel_GetLiveAnchorLevel_Handler,
		},
		{
			MethodName: "GetLiveAnchorLevelByUid",
			Handler:    _AnchorLevel_GetLiveAnchorLevelByUid_Handler,
		},
		{
			MethodName: "GetAnchorLevelMonthTask",
			Handler:    _AnchorLevel_GetAnchorLevelMonthTask_Handler,
		},
		{
			MethodName: "GetAnchorLevel",
			Handler:    _AnchorLevel_GetAnchorLevel_Handler,
		},
		{
			MethodName: "ReplenishAnchorLevel",
			Handler:    _AnchorLevel_ReplenishAnchorLevel_Handler,
		},
		{
			MethodName: "ManualSettleAnchorLevel",
			Handler:    _AnchorLevel_ManualSettleAnchorLevel_Handler,
		},
		{
			MethodName: "SettleNewTaskAward",
			Handler:    _AnchorLevel_SettleNewTaskAward_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "tt/quicksilver/anchor-level/anchor-level.proto",
}

func init() {
	proto.RegisterFile("tt/quicksilver/anchor-level/anchor-level.proto", fileDescriptor_anchor_level_127474984171a0bf)
}

var fileDescriptor_anchor_level_127474984171a0bf = []byte{
	// 1505 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xec, 0x58, 0xcd, 0x72, 0xdb, 0xb6,
	0x16, 0x96, 0x64, 0x3b, 0xb6, 0x8e, 0xac, 0x98, 0x81, 0x7f, 0x24, 0x33, 0x93, 0xc4, 0xe1, 0xcd,
	0xcd, 0xf5, 0xcd, 0xbd, 0x95, 0x53, 0x67, 0x3c, 0xe9, 0xdf, 0x74, 0x46, 0xb6, 0x95, 0xd4, 0x8d,
	0xac, 0xb8, 0xf2, 0x4f, 0xa7, 0x69, 0x67, 0x38, 0x0c, 0x09, 0x5b, 0xa8, 0x48, 0x50, 0x11, 0x20,
	0xa9, 0x7a, 0x85, 0xbe, 0x44, 0x77, 0x5d, 0xf4, 0x71, 0xba, 0xe9, 0x1b, 0x74, 0xd9, 0xe9, 0xb6,
	0x5d, 0x75, 0x00, 0x50, 0x12, 0x29, 0x92, 0xb1, 0xd2, 0x75, 0x57, 0x12, 0xcf, 0x87, 0xf3, 0x1d,
	0x00, 0xe7, 0x97, 0x84, 0x0a, 0xe7, 0x3b, 0x6f, 0x7a, 0xc4, 0x6e, 0x33, 0xe2, 0xf6, 0x71, 0x77,
	0xc7, 0xa2, 0x76, 0xcb, 0xef, 0xbe, 0xe7, 0xe2, 0x3e, 0x76, 0x23, 0x0f, 0x95, 0x4e, 0xd7, 0xe7,
	0x3e, 0x5a, 0x56, 0x32, 0x53, 0xca, 0xf4, 0x69, 0x6d, 0xfc, 0x1d, 0xc7, 0x94, 0x11, 0x9f, 0xee,
	0xf8, 0x1d, 0x4e, 0x7c, 0xca, 0x46, 0xbf, 0x4a, 0xdb, 0xd0, 0xe1, 0xc6, 0x39, 0x71, 0x9a, 0xf8,
	0x0d, 0xd2, 0x60, 0xae, 0x47, 0x9c, 0x72, 0x76, 0x2b, 0xbb, 0x5d, 0x6c, 0x8a, 0xbf, 0xc6, 0x29,
	0xe8, 0xc7, 0x16, 0xed, 0x59, 0xee, 0x29, 0xe6, 0xdc, 0xc5, 0x55, 0x69, 0xa7, 0x2e, 0xcc, 0x88,
	0xf5, 0xf7, 0x61, 0x99, 0x49, 0xb9, 0xe9, 0xf9, 0x94, 0xb7, 0x02, 0xc5, 0x82, 0x92, 0x1d, 0x0b,
	0x11, 0x42, 0x30, 0xdf, 0x23, 0x0e, 0x2b, 0xe7, 0xb6, 0xe6, 0xb6, 0x8b, 0x4d, 0xf9, 0xdf, 0xa8,
	0x81, 0x76, 0x4e, 0x9c, 0x83, 0x16, 0xb6, 0xdb, 0x63, 0xaa, 0x98, 0x69, 0x74, 0x0f, 0x0a, 0xb6,
	0x58, 0xa2, 0x4e, 0x55, 0xce, 0x6d, 0x65, 0xb7, 0xf3, 0x4d, 0xb0, 0xc7, 0x5a, 0xc6, 0x22, 0x2c,
	0xd4, 0xbc, 0x0e, 0x1f, 0x1a, 0x3f, 0xcc, 0x01, 0x0a, 0xed, 0xac, 0x81, 0x07, 0x67, 0x16, 0x6b,
	0x0b, 0x82, 0x2e, 0xf6, 0x2c, 0x42, 0x4d, 0x4e, 0x3c, 0x1c, 0x50, 0x83, 0x12, 0x9d, 0x11, 0x0f,
	0xa3, 0xf7, 0x61, 0x9d, 0x62, 0xec, 0x98, 0x03, 0x8c, 0xdb, 0xa6, 0x65, 0x73, 0xd2, 0xc7, 0xa6,
	0x63, 0x0d, 0x99, 0xb4, 0x55, 0x6c, 0x22, 0x01, 0x7e, 0x89, 0x71, 0xbb, 0x2a, 0xa1, 0x43, 0x6b,
	0xc8, 0xd0, 0x1e, 0x94, 0x02, 0xce, 0x98, 0xd2, 0x9c, 0x54, 0x5a, 0x53, 0x70, 0x5c, 0x6d, 0x62,
	0xa9, 0xe3, 0x5a, 0xfc, 0xd2, 0xef, 0x7a, 0x4a, 0x6d, 0x5e, 0xa9, 0x8d, 0x6c, 0x9d, 0x04, 0xa0,
	0x54, 0xfb, 0x10, 0x36, 0xc3, 0xd6, 0xa2, 0x8a, 0x0b, 0x52, 0x71, 0x63, 0x62, 0x2f, 0xa2, 0xfa,
	0x10, 0x56, 0xa4, 0xce, 0x15, 0xb9, 0xe4, 0x66, 0xdf, 0x72, 0x7b, 0xb8, 0x7c, 0x43, 0x2a, 0x14,
	0x85, 0xf8, 0x39, 0xb9, 0xe4, 0x17, 0x42, 0x88, 0xfe, 0x0f, 0x28, 0x08, 0x9e, 0xf0, 0x65, 0x2f,
	0xca, 0xcb, 0xd6, 0x14, 0x32, 0x71, 0x14, 0xda, 0x84, 0x25, 0xc9, 0x4a, 0x7b, 0x5e, 0x79, 0x49,
	0xd2, 0x2d, 0x8a, 0xe7, 0x46, 0xcf, 0x43, 0xb7, 0x21, 0xcf, 0x2d, 0xd6, 0x36, 0xf9, 0xb0, 0x83,
	0xcb, 0x79, 0x89, 0x2d, 0x09, 0xc1, 0xd9, 0xb0, 0x83, 0x8d, 0x9f, 0x72, 0xa0, 0x29, 0x0f, 0x1d,
	0x13, 0xfb, 0xc4, 0x67, 0xd2, 0x3f, 0x08, 0xe6, 0x59, 0xcb, 0x1f, 0x48, 0xc7, 0x2c, 0x35, 0xe5,
	0xff, 0x7f, 0x5c, 0xd2, 0xc3, 0x46, 0x1d, 0x36, 0x9f, 0x63, 0x5e, 0x27, 0xfd, 0x20, 0xdd, 0xc4,
	0x65, 0xd5, 0x28, 0xef, 0x0e, 0x93, 0xf3, 0xe4, 0x0e, 0x80, 0xdd, 0xb2, 0x28, 0xc5, 0xae, 0x49,
	0x9c, 0xe0, 0x9e, 0xf2, 0x81, 0xe4, 0xc8, 0x31, 0x28, 0xe8, 0x69, 0x6c, 0xac, 0x83, 0xd6, 0x60,
	0x81, 0x13, 0xee, 0xe2, 0x20, 0xbd, 0xd4, 0x83, 0x70, 0xf3, 0xb7, 0x3d, 0xaf, 0x63, 0xf6, 0xba,
	0xae, 0xbc, 0xc3, 0x7c, 0x73, 0x51, 0x3c, 0x9f, 0x77, 0x5d, 0x91, 0x54, 0xaf, 0x2d, 0x86, 0x4d,
	0xe2, 0x5d, 0x09, 0x74, 0x5e, 0x65, 0xa5, 0x10, 0x1d, 0x49, 0x89, 0xd1, 0x80, 0xf5, 0x88, 0xbd,
	0x71, 0x86, 0xef, 0xc1, 0x82, 0x0a, 0x2e, 0xb1, 0xf7, 0x9b, 0xbb, 0xf7, 0x2a, 0xe1, 0xa2, 0x55,
	0xa9, 0x36, 0x0e, 0x3e, 0x7b, 0xd9, 0x34, 0xeb, 0xb5, 0x8b, 0x5a, 0xdd, 0x3c, 0xfb, 0xea, 0xa4,
	0xd6, 0x54, 0xab, 0x8d, 0x3f, 0xb2, 0xb0, 0x3a, 0xc5, 0x76, 0x44, 0x2f, 0xfd, 0x84, 0x8b, 0x18,
	0x1b, 0xc8, 0xbd, 0x8b, 0x01, 0x11, 0xb8, 0x84, 0x63, 0xcf, 0xa4, 0x96, 0x87, 0x83, 0xd3, 0x2e,
	0x09, 0x41, 0xc3, 0xf2, 0xf0, 0xb5, 0xc7, 0x95, 0x25, 0xb0, 0x65, 0x39, 0xfe, 0xc0, 0xb4, 0x7d,
	0xd7, 0xef, 0xca, 0x10, 0xc8, 0x37, 0x0b, 0x4a, 0x76, 0x20, 0x44, 0xc2, 0x41, 0x8c, 0x5b, 0x5d,
	0xae, 0xca, 0x90, 0x72, 0x79, 0x5e, 0x4a, 0x64, 0x15, 0xda, 0x84, 0x25, 0x4c, 0x1d, 0x05, 0x2e,
	0xaa, 0x9c, 0xc2, 0xd4, 0x11, 0x90, 0xf1, 0x12, 0x36, 0x92, 0xee, 0x92, 0x75, 0xd0, 0x1e, 0xcc,
	0xbb, 0x84, 0xf1, 0x72, 0x76, 0x6b, 0x6e, 0xbb, 0xb0, 0x7b, 0x3f, 0x7a, 0xd4, 0x84, 0xeb, 0x6a,
	0xca, 0xe5, 0xc6, 0xe3, 0xa9, 0x60, 0x90, 0xf8, 0xfe, 0x30, 0x28, 0xff, 0xa3, 0x5a, 0x9d, 0x0d,
	0xd5, 0xea, 0xdf, 0xb2, 0x70, 0x3b, 0x55, 0x85, 0x75, 0x50, 0x0b, 0x56, 0x7a, 0xc4, 0xd9, 0x0d,
	0x61, 0xc1, 0x9e, 0x3e, 0x8d, 0xee, 0xe9, 0x2d, 0x1c, 0x95, 0xf3, 0x28, 0x81, 0x8a, 0xce, 0x69,
	0x5a, 0x1d, 0xc3, 0x5a, 0xd2, 0x42, 0x11, 0x08, 0x6d, 0x3c, 0x1c, 0x05, 0x42, 0x1b, 0x0f, 0xd1,
	0x53, 0x58, 0x50, 0xe9, 0x25, 0x02, 0x61, 0xa6, 0xdb, 0x51, 0xeb, 0x3f, 0xca, 0x7d, 0x90, 0x35,
	0x7e, 0x9d, 0x97, 0x07, 0x0e, 0xad, 0x90, 0x8d, 0x4c, 0xa4, 0x8d, 0x3c, 0xf0, 0x43, 0x58, 0xb1,
	0x7b, 0xdd, 0x80, 0x4c, 0x05, 0x4d, 0x56, 0xfa, 0xbc, 0x28, 0xc4, 0xaa, 0x01, 0x89, 0xc8, 0x79,
	0x02, 0x1b, 0xb2, 0xbe, 0xc8, 0xce, 0x98, 0x50, 0x95, 0x56, 0x05, 0x2a, 0xa9, 0x43, 0x45, 0xe9,
	0x29, 0x94, 0x83, 0xea, 0x12, 0x57, 0x53, 0x55, 0x69, 0x5d, 0xe1, 0x09, 0x8a, 0x21, 0x6b, 0x49,
	0x55, 0x69, 0x7d, 0x6c, 0x2f, 0x52, 0x94, 0x3e, 0x06, 0x3d, 0x62, 0x31, 0xaa, 0xaa, 0x82, 0xb5,
	0x14, 0xb2, 0x19, 0x51, 0xfe, 0x1f, 0x20, 0xa5, 0x45, 0xf1, 0xc0, 0xbc, 0xb4, 0x28, 0x33, 0x6d,
	0xca, 0x83, 0x20, 0x5e, 0x91, 0x48, 0x03, 0x0f, 0x9e, 0x59, 0x94, 0x1d, 0x50, 0x2e, 0x3a, 0x8d,
	0x5a, 0x4c, 0xa8, 0xed, 0x7b, 0x38, 0xa8, 0x80, 0xaa, 0x8b, 0x68, 0x12, 0x39, 0x92, 0x80, 0xea,
	0x4b, 0xa3, 0xf2, 0x9c, 0xa0, 0x92, 0x9f, 0x94, 0xe7, 0xe3, 0x69, 0xb5, 0xb1, 0x11, 0xdb, 0xa7,
	0xac, 0xe7, 0xe1, 0xae, 0xdc, 0x11, 0x84, 0x8c, 0x1c, 0x04, 0x80, 0xd8, 0xd2, 0x7d, 0x58, 0x26,
	0xcc, 0xec, 0x13, 0xdf, 0xb5, 0xc4, 0x40, 0x54, 0x2e, 0xc8, 0x4e, 0x54, 0x20, 0xec, 0x62, 0x24,
	0x42, 0x8f, 0xe0, 0x56, 0xdc, 0x15, 0xcb, 0xa1, 0x13, 0x86, 0x9c, 0x20, 0x7a, 0xa9, 0xd3, 0xb7,
	0xa8, 0x8d, 0x1d, 0x53, 0xf6, 0x42, 0xc7, 0xa7, 0xb8, 0x5c, 0x94, 0xa4, 0xda, 0x08, 0x11, 0x81,
	0x74, 0xe8, 0x53, 0x6c, 0x3c, 0x85, 0xe2, 0x19, 0x66, 0xfc, 0x14, 0x53, 0xe7, 0xc8, 0x4b, 0x2e,
	0xed, 0x6b, 0xe1, 0x8a, 0x56, 0x1c, 0x55, 0xc4, 0x1f, 0x73, 0x50, 0x6a, 0xe2, 0x8e, 0x8b, 0x29,
	0x61, 0xad, 0x77, 0x9f, 0xc8, 0xbe, 0x86, 0x3c, 0xa1, 0x97, 0xbe, 0x29, 0xeb, 0x47, 0x2e, 0x29,
	0x57, 0x53, 0xc8, 0x13, 0xe5, 0x32, 0x7d, 0x96, 0x04, 0x61, 0x9d, 0x30, 0xae, 0x7f, 0x9f, 0x85,
	0x72, 0xda, 0x32, 0x51, 0x08, 0x03, 0x3b, 0x93, 0x73, 0xe6, 0x95, 0xe4, 0x9c, 0x38, 0x62, 0xef,
	0xe1, 0x6d, 0x04, 0x87, 0x2e, 0x58, 0x13, 0x96, 0x94, 0x1b, 0x56, 0x09, 0x15, 0xbf, 0x61, 0x3d,
	0x79, 0x2f, 0x22, 0x8d, 0x8d, 0x01, 0xa0, 0x68, 0x96, 0xcb, 0xe4, 0xbe, 0x66, 0x87, 0xfb, 0x09,
	0x3b, 0x9c, 0xa1, 0xd1, 0x84, 0x8f, 0x60, 0x94, 0x60, 0x5d, 0xcd, 0xd2, 0xc1, 0x98, 0x5a, 0x1d,
	0x58, 0x5d, 0x51, 0x7d, 0x1f, 0xfd, 0x92, 0x83, 0x5b, 0x31, 0x5d, 0x74, 0x07, 0x36, 0x63, 0x42,
	0xf3, 0xa8, 0x71, 0x51, 0xad, 0x1f, 0x1d, 0x6a, 0x19, 0x54, 0x82, 0xd5, 0x38, 0x5c, 0xd5, 0xb2,
	0xc9, 0xc0, 0xbe, 0x96, 0x4b, 0x06, 0x0e, 0xb4, 0xb9, 0x64, 0xe0, 0x50, 0x9b, 0x4f, 0x06, 0x6a,
	0xda, 0x02, 0xd2, 0x61, 0x23, 0x0e, 0x5c, 0xec, 0x9a, 0x55, 0x0d, 0x52, 0xb1, 0x7d, 0x6d, 0x2d,
	0x15, 0x3b, 0xd0, 0xee, 0xa6, 0x62, 0x87, 0xda, 0x76, 0x2a, 0x56, 0xd3, 0x76, 0x53, 0xb1, 0x67,
	0xda, 0x27, 0xbb, 0x7f, 0xe6, 0xa1, 0x10, 0xf2, 0x34, 0xfa, 0x42, 0x8e, 0x28, 0x09, 0x6f, 0x0c,
	0x6b, 0x51, 0x4f, 0xaa, 0xb6, 0xa8, 0x6f, 0x4d, 0xf9, 0x37, 0xa6, 0x67, 0x64, 0xd0, 0x31, 0xac,
	0x8e, 0x29, 0x43, 0x23, 0x6e, 0x32, 0xe1, 0xdd, 0x24, 0xc2, 0x89, 0x96, 0x91, 0x41, 0x2f, 0x00,
	0x9d, 0x8e, 0xe8, 0xe4, 0xf8, 0x7d, 0x62, 0x31, 0x86, 0xee, 0xc6, 0xd8, 0x22, 0xef, 0x50, 0xfa,
	0x6a, 0x14, 0x57, 0x2f, 0x47, 0x19, 0x74, 0x08, 0x1b, 0xa3, 0x77, 0x38, 0xea, 0x28, 0x33, 0x4d,
	0x2c, 0xa2, 0x0e, 0x25, 0x29, 0xa4, 0xb1, 0xbc, 0x80, 0x3b, 0x13, 0x96, 0x06, 0x1e, 0x4c, 0xa6,
	0xc9, 0xbf, 0x41, 0xf6, 0x1c, 0x36, 0x27, 0x64, 0x55, 0xd7, 0x6d, 0xf8, 0x5c, 0x6d, 0xec, 0x98,
	0x5d, 0xbd, 0x13, 0x91, 0x37, 0x35, 0x21, 0x8d, 0xa7, 0x5b, 0xf4, 0x9f, 0xb7, 0xcc, 0x1f, 0xe1,
	0x89, 0x5a, 0xdf, 0x9e, 0x6d, 0x21, 0xeb, 0x18, 0x19, 0x64, 0xc9, 0xaa, 0x31, 0x35, 0x41, 0xa0,
	0x7f, 0x5d, 0x37, 0xea, 0x08, 0x33, 0x0f, 0xae, 0x5f, 0x24, 0x4d, 0x74, 0xa0, 0x94, 0x32, 0x2b,
	0xa1, 0xed, 0x19, 0x47, 0xaa, 0x37, 0xfa, 0x7f, 0x67, 0x1e, 0xbe, 0x8c, 0x0c, 0xfa, 0x46, 0x5a,
	0x4c, 0x1a, 0x78, 0x52, 0xe2, 0x37, 0xce, 0x9e, 0x36, 0x2d, 0x19, 0x19, 0xf4, 0x39, 0xdc, 0x8c,
	0x2e, 0x98, 0x2d, 0xcb, 0xe2, 0xc5, 0xd9, 0xc8, 0xa0, 0x2b, 0x58, 0x4b, 0x2a, 0xe8, 0xe8, 0xdf,
	0x33, 0xf5, 0x2f, 0xfd, 0xe1, 0x2c, 0xcb, 0xa4, 0xa1, 0x57, 0x50, 0x4a, 0xf9, 0xec, 0x31, 0xed,
	0x84, 0xf4, 0xaf, 0x23, 0x69, 0x21, 0x7b, 0x22, 0x73, 0x7b, 0xaa, 0x01, 0x4c, 0xc7, 0x50, 0x62,
	0x8b, 0x48, 0x61, 0xd4, 0x6f, 0xff, 0xfc, 0xfb, 0x83, 0x52, 0xc8, 0x78, 0xb8, 0x44, 0xed, 0x3f,
	0x7e, 0x55, 0xb9, 0xf2, 0x5d, 0x8b, 0x5e, 0x55, 0xf6, 0x76, 0x39, 0xaf, 0xd8, 0xbe, 0xb7, 0x23,
	0x3f, 0xfb, 0xd8, 0xbe, 0xbb, 0xc3, 0x70, 0xb7, 0x4f, 0x6c, 0xcc, 0x22, 0xdf, 0x94, 0x5e, 0xdf,
	0x90, 0xf8, 0x93, 0xbf, 0x02, 0x00, 0x00, 0xff, 0xff, 0xb4, 0x63, 0xd2, 0xea, 0x86, 0x12, 0x00,
	0x00,
}
