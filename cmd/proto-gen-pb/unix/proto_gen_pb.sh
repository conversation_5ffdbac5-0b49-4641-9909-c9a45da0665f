#!/bin/bash

# QUICKSILVER_PATH 项目路径环境变量，例如 /home/<USER>/quicksilver/src/golang.52tt.com
project_path=${QUICKSILVER_PATH}



echo -e "\033[41;37mworkspace->"${project_path}"\0:33[0m"
if [[ ${project_path} = "" ]];then
  echo "Please export QUICKSILVER_PATH"
  exit -1
fi

proto_path="${project_path}/third-party"
app_path="${proto_path}/tt-protocol/app"
rcmd_path="${project_path}/third-party/rcmd"
common_path="${proto_path}/tt-protocol/common"
cpp_service_path="${proto_path}/tt-protocol/service/appsvr"
go_service_path="${proto_path}/tt-protocol/service/quicksilver"

include_db_config="--proto_path=${project_path}/cmd/proto-gen-pb/protoc-include --proto_path=${app_path} --proto_path=${common_path} --proto_path=${cpp_service_path} --proto_path=${go_service_path} --proto_path=${proto_path}/tt-protocol --proto_path=${rcmd_path} --proto_path=${cpp_service_path}/src/proto/svr_pbfile"

# 判断系统是mac还是linux
bin_path="${project_path}/cmd/proto-gen-pb/bin"
sys_type=`uname`
if [[ ${sys_type} =~ "Linux" ]]; then
  bin_path=${bin_path}/linux
elif [[ ${sys_type} =~ "Darwin" ]]; then
  bin_path=${bin_path}/mac
else
  echo "${sys_type} sys type not support."
  exit -1
fi

export PATH=${bin_path}:${PATH}
export GOPATH="${project_path}"

chmod +x ${bin_path}/*

function UsageDesc() {
  echo "Usage:"
  echo "proto_gen_pb.sh -f proto_file"
  echo "Description:"
  echo "proto_file, proto file or errors.def"
  exit -1
}

while getopts 'hf:' OPT; do
    case $OPT in
        f) proto_file="$OPTARG";;
        h) UsageDesc;;
        *) UsageDesc;;
    esac
done

if [[ ${proto_file} = "" ]];then
  UsageDesc
fi

function GenProto() {
  echo -e "\033[41;37mStart to gen proto ${proto_file}...\033[0m"
  #service proto添加grpc插件
  grep "service " ${proto_file} >/dev/null
  if [[ $? -eq 0 ]]; then
    plugin="plugins=grpc:"
  else
    plugin=""
  fi

  # 目录
  project_dir=$(dirname ${project_path})
  # 项目文件名
  project_name=$(basename ${project_path})

  echo ${project_dir} ${project_name}

  #是否有指定生成目录
  grep "option go_package" ${proto_file} >/dev/null
  if [[ $? -eq 0 ]]; then
    go_out=${project_dir}
  else
    go_out="${project_path}/protocol/services"
  fi

  #是否需要用svrkit-go生成
  grep "tlvpickle" ${proto_file} >/dev/null
  if [[ $? -eq 0 ]]; then
    set -e
    ###临时
    if [[ ${proto_file} =~ .*channel.proto$ ]] || [[ ${proto_file} =~ .*attachment.proto$ ]] || [[ ${proto_file} =~ .*entertainmentrecommendback.proto$ ]]; then
      echo "${bin_path}/protoc --go_out=${plugin}${go_out} ${include_db_config} ${proto_file} "
      ${bin_path}/protoc --go_out=${plugin}${go_out} ${include_db_config} ${proto_file} 2>&1
    else
      echo "${bin_path}/protoc --gogofaster_out=${plugin}${go_out} ${include_db_config} ${proto_file}"
      ${bin_path}/protoc --gogofaster_out=${plugin}${go_out} ${include_db_config} ${proto_file} 2>&1
    fi
    set +e
  else
    grep "proto3" ${proto_file} >/dev/null
    if [[ $? -ne 0 ]]; then
      set -e
      echo "${bin_path}/protoc --gogofaster_out=${plugin}${go_out} ${include_db_config} ${proto_file} "
      ${bin_path}/protoc --gogofaster_out=${plugin}${go_out} ${include_db_config} ${proto_file} 2>&1
      set +e
    else
      set -e
      echo "${bin_path}/protoc --go_out=${plugin}${go_out} ${include_db_config} ${proto_file} "
      ${bin_path}/protoc --go_out=${plugin}${go_out} ${include_db_config} ${proto_file} 2>&1
      set +e
    fi
  fi

  if [[ ${project_name} != "golang.52tt.com" ]]; then
    echo "cp -rf ${project_dir}/golang.52tt.com/protocol/* ${project_path}/protocol/"
    cp -rf "${project_dir}/golang.52tt.com/protocol/"* "${project_path}/protocol"
    rm -rf "${project_dir}/golang.52tt.com"
  fi

  #无指定生成目录
  grep "option go_package" ${proto_file} >/dev/null
  if [[ $? -ne 0 ]]; then
    # CPP 协议需要这样处理
    prefix="${project_path}/protocol/services"
    cp -rf "${prefix}/src/"* ${prefix}
    rm -rf "${prefix}/src"
  fi

  echo -e "\033[41;37mGen proto finish\033[0m"
}

function GenCommonError() {
  echo -e "\033[41;37mStart to gen ${proto_file}...\033[0m"

  mkdir -p "${project_path}/protocol/common/status"
  set -e
  python "${common_path}/codegen/error/gen_error.py" "--go_out=${project_path}/protocol/common/status" ${proto_file}

  echo -e "\033[41;37mGen ${proto_file} finish\033[0m"
}

function FindProtoFile() {
  proto_base_name=$(basename ${proto_file})
  file_list=($(find ${proto_path} -name ${proto_base_name}))

  if [[ ${#file_list[@]} = 0 ]]; then
    # 未找到文件
    echo -e "\033[41;37m${proto_file} not found, please check!!!\033[0m"
    exit -1

  elif [[ ${#file_list[@]} = 1 ]]; then
    # 仅找到一个符合的文件
    echo "target_path:"${file_list[0]}
    proto_file=${file_list[0]}

  else
    # 找到多个符合的文件。让用户去选择
    echo -e "\n>>>可选文件\n"
    for i in ${!file_list[@]}
    do
      echo -e ${i}: ${file_list[${i}]}"\n"
    done

    read -p ">>>选择文件 0-$((${#file_list[@]}-1))：" index
    if [ -z ${index} ]; then
        index=0
    fi

    proto_file=${file_list[${index}]}
    echo -e "\n您已选择 ${file_list[${index}]}\n"

  fi
}

if [ ! -e ${proto_file} ]; then
  # 文件不存在，去搜索该文件
  FindProtoFile
fi

# 错误码生成
if [[ $(basename ${proto_file}) = "errors.def" ]]; then
  GenCommonError
  exit 0
fi

if ! [[ ${proto_file} =~ ^"/" ]]; then
  # 拼接成绝对路径
  proto_file=$(cd $(dirname ${proto_file});pwd)"/"$(basename ${proto_file})
fi

echo "proto abs path: "${proto_file}

# 生成pb文件
if [[ $(basename ${proto_file}) =~ ".proto" ]]; then
  GenProto
  exit 0
fi

echo ${proto_file}" is illegal "
