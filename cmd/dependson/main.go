package main

import (
	"encoding/json"
	"flag"
	"fmt"
	"go/ast"
	"go/parser"
	"go/scanner"
	"go/token"
	"io"
	"io/ioutil"
	"os"
	"os/exec"
	"path/filepath"
	"reflect"
	"sort"
	"strings"

	"gopkg.in/yaml.v2"
)

var (
	exitCode = 0

	unknownService = make(map[string]struct{})

	help    *bool
	verbose *bool
)

func init() {
	help = flag.Bool("help", false, prettify(`
		Print usage instructions and exit.`))
	verbose = flag.Bool("v", false, prettify(`
		Enable verbose log.`))
}

// argumentType is which mode goimports was invoked as.
type argumentType int

const (
	// fromStdin means the user is piping their source into goimports.
	fromStdin argumentType = iota

	// singleArg is the common case from editors, when goimports is run on
	// a single file.
	singleArg

	// multipleArg is when the user ran "goimports file1.go file2.go"
	// or ran goimports on a directory tree.
	multipleArg
)

func main() {
	flag.Usage = usage
	flag.Parse()
	if *help {
		usage()
		os.Exit(0)
	}

	dir, _ := filepath.Abs(filepath.Dir(os.Args[0]))
	if !strings.HasSuffix(dir, "golang.52tt.com") {
		usage()
		os.Exit(0)
	}
	fmt.Println("开始分析服务依赖关系。")

	// 读ci/inventory.yaml，找出所有go服务和代码路径
	inventoryConf, err := ioutil.ReadFile("ci/inventory.yaml")
	if err != nil {
		fmt.Println(err)
		return
	}

	var inventory Inventory
	_ = yaml.Unmarshal(inventoryConf, &inventory)

	argType := singleArg

	// 记录每个服务下每个go文件中引用的库
	serviceImports := make(map[string]map[string][]string)
	for _, category := range inventory.Categories {
		for _, target := range category.Targets {
			pathToImports := make(map[string][]string)
			path := target.Src
			switch dir, err := os.Stat(path); {
			case err != nil:
				report(err)
			case dir.IsDir():
				visitFile := func(path string, f os.FileInfo, err error) error {
					if err == nil && isGoFile(f) {
						imps, err := processFile(path, nil, os.Stdout, multipleArg)
						if err != nil {
							report(err)
						}
						pathToImports[path] = imps
					}
					if err != nil {
						report(err)
					}
					return nil
				}
				filepath.Walk(path, visitFile)
			default:
				if isGoFile(dir) {
					imps, _ := processFile(path, nil, os.Stdout, argType)
					pathToImports[path] = imps
				}
			}
			serviceImports[target.Name] = pathToImports
		}
	}

	logFile, _ := os.OpenFile("log.txt", os.O_WRONLY|os.O_TRUNC|os.O_CREATE, 0644)
	var notExistService []string
	var notExistPath []string

	fmt.Fprintf(logFile, "---------- 服务依赖关系 ----------\n")
	// 记录服务所依赖的服务（通过这个来找存在循环依赖的服务和生产依赖关系图）
	serviceDependsOn := make(map[string][]string)
	for service, pathToImports := range serviceImports {
		var dependsOn []string
		for path, imps := range pathToImports {
			for _, impPath := range imps {
				if s := getDependsOnService(impPath); s != "" {
					if s != service {
						dependsOn = appendToSlice(dependsOn, s)
					}

					if _, ok := serviceImports[s]; !ok {
						if _, ok := cppService[s]; !ok {
							notExistService = appendToSlice(notExistService, s)
							unknownService[s] = struct{}{}

							notExistPath = appendToSlice(notExistPath, impPath)
						}
					}
				}
			}
			if *verbose {
				fmt.Fprintf(
					logFile,
					`
path:%s
len(imps):%d
imps:%v
			`,
					path,
					len(imps),
					imps,
				)
			}

		}
		serviceDependsOn[service] = dependsOn
		fmt.Fprintf(logFile, "%s 依赖于: %v\n", service, dependsOn)
	}
	fmt.Fprintf(logFile, "\n")

	fmt.Fprintf(logFile, "notExistPath: %v\n", notExistPath)
	fmt.Fprintf(logFile, "notExistService: %v\n", notExistService)
	fmt.Fprintf(logFile, "\n")

	// 找出存在循环依赖的服务
	for service := range serviceDependsOn {
		a := []string{service}
		findUnreliableServices(service, a, serviceDependsOn)
	}

	var temBads [][]string
	for _, b := range unreliableServices {
		found := false
		for _, tb := range temBads {
			if reflect.DeepEqual(b, tb) {
				found = true
				break
			}
		}

		if !found {
			temBads = append(temBads, b)
		}
	}

	fmt.Fprintln(logFile, "以下服务存在循环依赖（--> 表示依赖于）:")
	for _, b := range temBads {
		out := ""
		for i, s := range b {
			if i != len(b)-1 {
				out += s + " --> "
			} else {
				out += s
			}
		}
		fmt.Fprintf(logFile, "%s\n", out)
	}
	fmt.Fprintln(logFile, "")

	for service := range serviceDependsOn {
		if len(serviceDependsOn[service]) > 0 {
			a := []string{service}
			result := goodluck1(service, a, serviceDependsOn)
			fResult := map[string]interface{}{service: result}
			bytes, _ := json.Marshal(fResult)
			if *verbose {
				fmt.Fprintf(logFile, "%s\n", string(bytes))
				fmt.Fprintf(logFile, "\n")
			}
		}
	}

	// 创建goodluck文件夹，生成的依赖关系图放这里
	exec.Command("mkdir", "-p", "goodluck").Run()
	for service := range serviceDependsOn {
		a := []string{service}
		result := goodluck2(service, a, serviceDependsOn)
		fResult := Node{Name: service, Children: result, ItemStyle: ItemStyles[service]}

		fileName := fmt.Sprintf("goodluck/%s.html", service)
		ioutil.WriteFile(fileName, getHtmlContent(fResult), 0666)

		bytes, _ := json.Marshal(fResult)
		if *verbose {
			fmt.Fprintf(logFile, "%s\n", string(bytes))
			fmt.Fprintf(logFile, "\n")
		}
	}

	if *verbose {
		fmt.Fprintf(logFile, "allImports: %d allServices: %d\n", len(allImports), len(allServices))
		for imp := range allImports {
			for i := range allImports {
				if imp != i && getDependsOnService(imp) == getDependsOnService(i) {
					fmt.Fprintf(logFile, "%s - %s\n", imp, i)
				}
			}
		}
	}

	fmt.Println(`完成分析，
服务依赖关系图位于goodluck文件夹下，
分析日志文件为log.txt。`)

	logFile.Sync()
	os.Exit(exitCode)
}

func goodluck1(service string, arr []string, serviceDependsOn map[string][]string) interface{} {
	results := make(map[string]interface{})
	for _, s := range serviceDependsOn[service] {
		if !isContain(arr, s) {
			t := make([]string, len(arr))
			copy(t, arr)
			t = append(t, s)
			results[s] = goodluck1(s, t, serviceDependsOn)
		}
	}

	return results
}

func goodluck2(service string, arr []string, serviceDependsOn map[string][]string) interface{} {
	var results []interface{}

	for _, s := range serviceDependsOn[service] {
		// 不存在循环依赖的时候，才能继续下去，要不然会栈溢出
		var result interface{}
		if !isContain(arr, s) {
			t := make([]string, len(arr))
			copy(t, arr)
			t = append(t, s)
			result = goodluck2(s, t, serviceDependsOn)
		}
		results = append(results, Node{Name: markNode(s), Children: result, ItemStyle: ItemStyles[s]})
	}

	sort.Slice(results, func(i, j int) bool {
		nodeI := results[i].(Node)
		nodeJ := results[j].(Node)
		return nodeI.Name < nodeJ.Name
	})
	return results
}

func markNode(service string) string {
	if _, ok := cppService[service]; ok {
		return service + "( c++ )"
	}

	if _, ok := unknownService[service]; ok {
		if service == "APNs" {
			return service + "( apns-token/apns-push )"
		}

		return service + "( unknown )"
	}

	return service
}

var unreliableServices [][]string

func findUnreliableServices(service string, arr []string, serviceDependsOn map[string][]string) {
	dependsOn := serviceDependsOn[service]
	for _, s := range dependsOn {
		if isContain(arr, s) {
			t := make([]string, len(arr))
			copy(t, arr)
			t = append(t, s)
			for i, e := range t {
				if e == s {
					t = t[i:]
					break
				}
			}

			unreliableServices = append(unreliableServices, t)
			return
		}

		t := make([]string, len(arr))
		copy(t, arr)
		t = append(t, s)
		findUnreliableServices(s, t, serviceDependsOn)
	}
}

func isContain(arr []string, elem string) bool {
	for _, e := range arr {
		if e == elem {
			return true
		}
	}

	return false
}

var allImports, allServices = make(map[string]struct{}), make(map[string]struct{})

func getDependsOnService(importPath string) string {
	if strings.HasPrefix(importPath, "golang.52tt.com/clients") {
		allImports[importPath] = struct{}{}
		array := strings.Split(importPath, "/")
		service := array[len(array)-1]

		if s, ok := importPathToService[importPath]; ok {
			service = s
		}
		allServices[service] = struct{}{}
		return service
	}

	return ""
}

func processFile(filename string, in io.Reader, out io.Writer, argType argumentType) ([]string, error) {
	if in == nil {
		f, err := os.Open(filename)
		if err != nil {
			return nil, err
		}
		defer f.Close()
		in = f
	}

	src, err := ioutil.ReadAll(in)
	if err != nil {
		return nil, err
	}

	fileSet := token.NewFileSet()
	file, err := parse(fileSet, filename, src)
	if err != nil {
		return nil, err
	}

	imports := collectImports(file)

	return imports, nil
}

func parse(fset *token.FileSet, filename string, src []byte) (*ast.File, error) {
	parserMode := parser.Mode(0)

	// Try as whole source file.
	file, err := parser.ParseFile(fset, filename, src, parserMode)
	if err != nil {
		return nil, err
	}

	return file, nil
}

func appendToSlice(dst []string, src ...string) []string {
	for _, imp := range src {
		exist := false
		for _, eImp := range dst {
			if eImp == imp {
				exist = true
				break
			}
		}
		if !exist {
			dst = append(dst, imp)
		}
	}
	return dst
}

// collectImports returns all the imports in f.
// Unnamed imports (., _) and "C" are ignored.
func collectImports(f *ast.File) []string {
	var imports []string
	for _, imp := range f.Imports {
		var name string
		if imp.Name != nil {
			name = imp.Name.Name
		}
		if imp.Path.Value == `"C"` || name == "_" || name == "." {
			continue
		}
		path := strings.Trim(imp.Path.Value, `"`)
		imports = append(imports, path)
	}
	return imports
}

func usage() {
	fmt.Fprintf(os.Stderr, `Usage:
%s [flags] （只能golang.52tt.com目录下执行）
`,
		os.Args[0])
	flag.PrintDefaults()
	os.Exit(2)
}

func report(err error) {
	scanner.PrintError(os.Stderr, err)
	exitCode = 2
}

func isGoFile(f os.FileInfo) bool {
	// ignore non-Go files
	name := f.Name()
	return !f.IsDir() && !strings.HasPrefix(name, ".") && strings.HasSuffix(name, ".go")
}

// parseFlags parses command line flags and returns the paths to process.
// It's a var so that custom implementations can replace it in other files.
var parseFlags = func() []string {
	flag.Parse()
	return flag.Args()
}

func prettify(docString string) string {
	parts := strings.Split(docString, "\n")

	// cull empty lines and also remove trailing and leading spaces
	// from each line in the doc string
	j := 0
	for _, part := range parts {
		part = strings.TrimSpace(part)
		if part == "" {
			continue
		}
		parts[j] = part
		j++
	}

	return strings.Join(parts[:j], "\n"+indent())
}

func indent() string {
	// In Go 1.10 and up, the flag package automatically
	// adds the right indentation.
	return ""
}
