# This is a file of api route command
# 1st cmdID 2nd command
# 云测试、云开发环境允许直接修改api路由，将命令行的create 换成force-update 即可
# 但是线上不允许通过force-update修改！

2063:
api-route-configurator --etcd-endpoints *************:2379 create --id 2063 --source api/channel_minigame_go/grpc_channel_minigame_go.proto --lang go --method /ga.api.channel_minigame_go.ChannelMiniGameGoLogic/ChannelGame
2067:
api-route-configurator --etcd-endpoints *************:2379 create --id 2067 --source api/channel_minigame_go/grpc_channel_minigame_go.proto --lang go --method /ga.api.channel_minigame_go.ChannelMiniGameGoLogic/SendMagicExpression
2510:
api-route-configurator --etcd-endpoints *************:2379 create --id 2510 --source api/channel_minigame_go/grpc_channel_minigame_go.proto --lang go --method /ga.api.channel_minigame_go.ChannelMiniGameGoLogic/GetChannelPresentRunwayList
30000:
api-route-configurator --etcd-endpoints *************:2379 create --id 30000 --source api/channel_minigame_go/grpc_channel_minigame_go.proto --lang go --method /ga.api.channel_minigame_go.ChannelMiniGameGoLogic/ChannelPresentCountSwitch
30001:
api-route-configurator --etcd-endpoints *************:2379 create --id 30001 --source api/channel_minigame_go/grpc_channel_minigame_go.proto --lang go --method /ga.api.channel_minigame_go.ChannelMiniGameGoLogic/ChannelPresentCountInit
30002:
api-route-configurator --etcd-endpoints *************:2379 create --id 30002 --source api/channel_minigame_go/grpc_channel_minigame_go.proto --lang go --method /ga.api.channel_minigame_go.ChannelMiniGameGoLogic/GetPresentCountRank
32100:
api-route-configurator --etcd-endpoints *************:2379 create --id 32100 --source api/channel_minigame_go/grpc_channel_minigame_go.proto --lang go --method /ga.api.channel_minigame_go.ChannelMiniGameGoLogic/SearchPkCandidate
32101:
api-route-configurator --etcd-endpoints *************:2379 create --id 32101 --source api/channel_minigame_go/grpc_channel_minigame_go.proto --lang go --method /ga.api.channel_minigame_go.ChannelMiniGameGoLogic/ChannelLiveVotePKVote
32102:
api-route-configurator --etcd-endpoints *************:2379 create --id 32102 --source api/channel_minigame_go/grpc_channel_minigame_go.proto --lang go --method /ga.api.channel_minigame_go.ChannelMiniGameGoLogic/ChannelLiveVotePKStart
32103:
api-route-configurator --etcd-endpoints *************:2379 create --id 32103 --source api/channel_minigame_go/grpc_channel_minigame_go.proto --lang go --method /ga.api.channel_minigame_go.ChannelMiniGameGoLogic/ChannelLiveVotePKCancel
32104:
api-route-configurator --etcd-endpoints *************:2379 create --id 32104 --source api/channel_minigame_go/grpc_channel_minigame_go.proto --lang go --method /ga.api.channel_minigame_go.ChannelMiniGameGoLogic/ChannelLiveVotePKGetInfo
32105:
api-route-configurator --etcd-endpoints *************:2379 create --id 32105 --source api/channel_minigame_go/grpc_channel_minigame_go.proto --lang go --method /ga.api.channel_minigame_go.ChannelMiniGameGoLogic/GetVotePkStatus
5002:
api-route-configurator --etcd-endpoints *************:2379 create --id 5002 --source api/channel_minigame_go/grpc_channel_minigame_go.proto --lang go --method /ga.api.channel_minigame_go.ChannelMiniGameGoLogic/ChannelVotePKVote
5003:
api-route-configurator --etcd-endpoints *************:2379 create --id 5003 --source api/channel_minigame_go/grpc_channel_minigame_go.proto --lang go --method /ga.api.channel_minigame_go.ChannelMiniGameGoLogic/ChannelVotePKStart
5004:
api-route-configurator --etcd-endpoints *************:2379 create --id 5004 --source api/channel_minigame_go/grpc_channel_minigame_go.proto --lang go --method /ga.api.channel_minigame_go.ChannelMiniGameGoLogic/ChannelVotePKCancel
5005:
api-route-configurator --etcd-endpoints *************:2379 create --id 5005 --source api/channel_minigame_go/grpc_channel_minigame_go.proto --lang go --method /ga.api.channel_minigame_go.ChannelMiniGameGoLogic/ChannelVotePKGetInfo
50606:
api-route-configurator --etcd-endpoints *************:2379 create --id 50606 --source api/channel_minigame_go/grpc_channel_minigame_go.proto --lang go --method /ga.api.channel_minigame_go.ChannelMiniGameGoLogic/GetInteractionEmojiConfList
50607:
api-route-configurator --etcd-endpoints *************:2379 create --id 50607 --source api/channel_minigame_go/grpc_channel_minigame_go.proto --lang go --method /ga.api.channel_minigame_go.ChannelMiniGameGoLogic/GetInteractionEmojiPrivilege
50608:
api-route-configurator --etcd-endpoints *************:2379 create --id 50608 --source api/channel_minigame_go/grpc_channel_minigame_go.proto --lang go --method /ga.api.channel_minigame_go.ChannelMiniGameGoLogic/SendInteractionEmoji
