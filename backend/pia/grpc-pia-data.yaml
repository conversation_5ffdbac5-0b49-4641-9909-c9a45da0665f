cmdInfoList:
  - source: api/pia/grpc_pia.proto
    cmd: 33021
    lang: go
    method: /ga.api.pia.PiaLogic/GetChannelPiaStatus
  - source: api/pia/grpc_pia.proto
    cmd: 33022
    lang: go
    method: /ga.api.pia.PiaLogic/SetPiaSwitch
  - source: api/pia/grpc_pia.proto
    cmd: 33023
    lang: go
    method: /ga.api.pia.PiaLogic/GetDrama
  - source: api/pia/grpc_pia.proto
    cmd: 33024
    lang: go
    method: /ga.api.pia.PiaLogic/GetSearchOptionGroup
  - source: api/pia/grpc_pia.proto
    cmd: 33026
    lang: go
    method: /ga.api.pia.PiaLogic/GetCurrentPiaInfo
  - source: api/pia/grpc_pia.proto
    cmd: 33027
    lang: go
    method: /ga.api.pia.PiaLogic/SetPiaPhase
  - source: api/pia/grpc_pia.proto
    cmd: 33028
    lang: go
    method: /ga.api.pia.PiaLogic/SetPiaProgress
  - source: api/pia/grpc_pia.proto
    cmd: 33029
    lang: go
    method: /ga.api.pia.PiaLogic/GetPlayingChannel
  - source: api/pia/grpc_pia.proto
    cmd: 33030
    lang: go
    method: /ga.api.pia.PiaLogic/GetQualityDramaList
  - source: api/pia/grpc_pia.proto
    cmd: 33031
    lang: go
    method: /ga.api.pia.PiaLogic/GetPracticeDramaList
  - source: api/pia/grpc_pia.proto
    cmd: 33032
    lang: go
    method: /ga.api.pia.PiaLogic/SelectDrama
  - source: api/pia/grpc_pia.proto
    cmd: 33033
    lang: go
    method: /ga.api.pia.PiaLogic/SetBgmInfo
  - source: api/pia/grpc_pia.proto
    cmd: 33034
    lang: go
    method: /ga.api.pia.PiaLogic/GetBgmInfo
  - source: api/pia/grpc_pia.proto
    cmd: 33035
    lang: go
    method: /ga.api.pia.PiaLogic/SetCompereMic
  - source: api/pia/grpc_pia.proto
    cmd: 33036
    lang: go
    method: /ga.api.pia.PiaLogic/OrderDrama
  - source: api/pia/grpc_pia.proto
    cmd: 33037
    lang: go
    method: /ga.api.pia.PiaLogic/GetOrderDramaList
  - source: api/pia/grpc_pia.proto
    cmd: 33038
    lang: go
    method: /ga.api.pia.PiaLogic/DeleteOrderDrama
  - source: api/pia/grpc_pia.proto
    cmd: 33039
    lang: go
    method: /ga.api.pia.PiaLogic/PiaSelectRole
  - source: api/pia/grpc_pia.proto
    cmd: 33040
    lang: go
    method: /ga.api.pia.PiaLogic/PiaCancelSelectRole
  - source: api/pia/grpc_pia.proto
    cmd: 33041
    lang: go
    method: /ga.api.pia.PiaLogic/SelectDramaV2
  - source: api/pia/grpc_pia.proto
    cmd: 33042
    lang: go
    method: /ga.api.pia.PiaLogic/PiaOperateDrama
  - source: api/pia/grpc_pia.proto
    cmd: 33043
    lang: go
    method: /ga.api.pia.PiaLogic/PiaGetDramaStatus
  - source: api/pia/grpc_pia.proto
    cmd: 33044
    lang: go
    method: /ga.api.pia.PiaLogic/PiaOperateBgm
  - source: api/pia/grpc_pia.proto
    cmd: 33045
    lang: go
    method: /ga.api.pia.PiaLogic/GetSearchOptionGroupV2
  - source: api/pia/grpc_pia.proto
    cmd: 33046
    lang: go
    method: /ga.api.pia.PiaLogic/GetDramaList
  - source: api/pia/grpc_pia.proto
    cmd: 33047
    lang: go
    method: /ga.api.pia.PiaLogic/GetDramaDetailById
  - source: api/pia/grpc_pia.proto
    cmd: 33048
    lang: go
    method: /ga.api.pia.PiaLogic/PiaGetDramaCopyId
  - source: api/pia/grpc_pia.proto
    cmd: 33049
    lang: go
    method: /ga.api.pia.PiaLogic/PiaCreateDramaCopy
  - source: api/pia/grpc_pia.proto
    cmd: 33050
    lang: go
    method: /ga.api.pia.PiaLogic/PiaOperateBgmVol
  - source: api/pia/grpc_pia.proto
    cmd: 33051
    lang: go
    method: /ga.api.pia.PiaLogic/DoUserDramaCollect
  - source: api/pia/grpc_pia.proto
    cmd: 33052
    lang: go
    method: /ga.api.pia.PiaLogic/GetUserDramaCollection
  - source: api/pia/grpc_pia.proto
    cmd: 33053
    lang: go
    method: /ga.api.pia.PiaLogic/GetPlayingChannelV2
  - source: api/pia/grpc_pia.proto
    cmd: 33054
    lang: go
    method: /ga.api.pia.PiaLogic/PiaChangePlayType
  - source: api/pia/grpc_pia.proto
    cmd: 33055
    lang: go
    method: /ga.api.pia.PiaLogic/GetMyDramaPlayingRecord
  - source: api/pia/grpc_pia.proto
    cmd: 33056
    lang: go
    method: /ga.api.pia.PiaLogic/PiaBatchDeleteMyPlayingRecord
  - source: api/pia/grpc_pia.proto
    cmd: 33057
    lang: go
    method: /ga.api.pia.PiaLogic/PiaGetRankingList
  - source: api/pia/grpc_pia.proto
    cmd: 33058
    lang: go
    method: /ga.api.pia.PiaLogic/PiaGetMyPlayingRecordIdList
  - source: api/pia/grpc_pia.proto
    cmd: 33059
    lang: go
    method: /ga.api.pia.PiaLogic/GetMyDramaCopyList
  - source: api/pia/grpc_pia.proto
    cmd: 33060
    lang: go
    method: /ga.api.pia.PiaLogic/PiaCopyDramaList
  - source: api/pia/grpc_pia.proto
    cmd: 33061
    lang: go
    method: /ga.api.pia.PiaLogic/PiaConfirmCoverCopy
  - source: api/pia/grpc_pia.proto
    cmd: 33062
    lang: go
    method: /ga.api.pia.PiaLogic/SetDramaCopyStatus
  - source: api/pia/grpc_pia.proto
    cmd: 33063
    lang: go
    method: /ga.api.pia.PiaLogic/DeleteDramaCopy
  - source: api/pia/grpc_pia.proto
    cmd: 33064
    lang: go
    method: /ga.api.pia.PiaLogic/PiaCreateDramaCopyV2
  - source: api/pia/grpc_pia.proto
    cmd: 33065
    lang: go
    method: /ga.api.pia.PiaLogic/PiaPerformDrama
  - source: api/pia/grpc_pia.proto
    cmd: 33066
    lang: go
    method: /ga.api.pia.PiaLogic/PiaSendDialogueIndex
  - source: api/pia/grpc_pia.proto
    cmd: 33067
    lang: go
    method: /ga.api.pia.PiaLogic/PiaFollowMic
  - source: api/pia/grpc_pia.proto
    cmd: 33068
    lang: go
    method: /ga.api.pia.PiaLogic/PiaReportDialogueIndex
  - source: api/pia/grpc_pia.proto
    cmd: 33069
    lang: go
    method: /ga.api.pia.PiaLogic/PiaGetPreviousDialogueIndex
  - source: api/pia/grpc_pia.proto
    cmd: 33070
    lang: go
    method: /ga.api.pia.PiaLogic/PiaUnFollowMic
  - source: api/pia/grpc_pia.proto
    cmd: 33071
    lang: go
    method: /ga.api.pia.PiaLogic/PiaGetMyFollowInfo
  - source: api/pia/grpc_pia.proto
    cmd: 33072
    lang: go
    method: /ga.api.pia.PiaLogic/PiaGetFollowedStatusOfMicList

