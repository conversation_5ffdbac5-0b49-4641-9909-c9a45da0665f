# This is a file of api route command
# 1st cmdID 2nd command
# 云测试、云开发环境允许直接修改api路由，将命令行的create 换成force-update 即可
# 但是线上不允许通过force-update修改！

371:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 371 --source api/group_management/grpc_group_management.proto --lang go --method /ga.api.group_management.GroupManagementLogic/SearchTGroup
373:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 373 --source api/group_management/grpc_group_management.proto --lang go --method /ga.api.group_management.GroupManagementLogic/CreateTGroup
375:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 375 --source api/group_management/grpc_group_management.proto --lang go --method /ga.api.group_management.GroupManagementLogic/JoinTGroup
376:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 376 --source api/group_management/grpc_group_management.proto --lang go --method /ga.api.group_management.GroupManagementLogic/QuitTGroup
377:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 377 --source api/group_management/grpc_group_management.proto --lang go --method /ga.api.group_management.GroupManagementLogic/TGroupAddMember
385:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 385 --source api/group_management/grpc_group_management.proto --lang go --method /ga.api.group_management.GroupManagementLogic/SearchTGroupByGameId
