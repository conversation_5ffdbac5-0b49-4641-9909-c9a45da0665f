# This is a file of api route command
# 1st cmdID 2nd command
# 云测试、云开发环境允许直接修改api路由，将命令行的create 换成force-update 即可
# 但是线上不允许通过force-update修改！

30030:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 30030 --source api/smash_egg/grpc_smash_egg.proto --lang go --method /ga.api.smash_egg.SmashEggLogic/GetConsumeRecord
30031:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 30031 --source api/smash_egg/grpc_smash_egg.proto --lang go --method /ga.api.smash_egg.SmashEggLogic/GetRecentWinningRecord
30032:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 30032 --source api/smash_egg/grpc_smash_egg.proto --lang go --method /ga.api.smash_egg.SmashEggLogic/GetWinningRecord
30033:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 30033 --source api/smash_egg/grpc_smash_egg.proto --lang go --method /ga.api.smash_egg.SmashEggLogic/Recharge
30034:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 30034 --source api/smash_egg/grpc_smash_egg.proto --lang go --method /ga.api.smash_egg.SmashEggLogic/Smash
30035:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 30035 --source api/smash_egg/grpc_smash_egg.proto --lang go --method /ga.api.smash_egg.SmashEggLogic/GetSmashStatus
30036:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 30036 --source api/smash_egg/grpc_smash_egg.proto --lang go --method /ga.api.smash_egg.SmashEggLogic/GetConfig
30038:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 30038 --source api/smash_egg/grpc_smash_egg.proto --lang go --method /ga.api.smash_egg.SmashEggLogic/GetResourceConfig
30039:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 30039 --source api/smash_egg/grpc_smash_egg.proto --lang go --method /ga.api.smash_egg.SmashEggLogic/GetUserPropList
30040:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 30040 --source api/smash_egg/grpc_smash_egg.proto --lang go --method /ga.api.smash_egg.SmashEggLogic/GetUserExpirePropNotify
