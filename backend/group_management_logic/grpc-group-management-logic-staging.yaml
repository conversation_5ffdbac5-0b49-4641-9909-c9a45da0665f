apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  namespace: quicksilver
  name: grpc-group-management-logic-logic-staging
spec:
  gateways:
  - quicksilver/tt-api-gateway
  - quicksilver/tt-api-gateway-internal
  hosts:
  - staging-apiv2.ttyuyin.com
  http:
  - match:
    - uri:
        prefix: /ga.api.group_management_logic.GroupManagementLogic/
    route:
    - destination:
        host: group-management-logic.quicksilver.svc.cluster.local
        port:
          number: 80
