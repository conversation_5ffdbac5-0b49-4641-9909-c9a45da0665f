# This is a file of api route command
# 1st cmdID 2nd command
# 云测试、云开发环境允许直接修改api路由，将命令行的create 换成force-update 即可
# 但是线上不允许通过force-update修改！

2055:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 2055 --source api/channel_music/grpc_channel_music_cpp.proto --lang cpp --method /ga.api.channel_music.ChannelMusicLogic/ChannelMusicV2CtrlCommand
2056:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 2056 --source api/channel_music/grpc_channel_music_cpp.proto --lang cpp --method /ga.api.channel_music.ChannelMusicLogic/ChannelMusicV2AddMusic
2057:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 2057 --source api/channel_music/grpc_channel_music_cpp.proto --lang cpp --method /ga.api.channel_music.ChannelMusicLogic/ChannelMusicV2RemoveMusic
2058:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 2058 --source api/channel_music/grpc_channel_music_cpp.proto --lang cpp --method /ga.api.channel_music.ChannelMusicLogic/ChannelMusicV2HeartBeat
2059:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 2059 --source api/channel_music/grpc_channel_music_cpp.proto --lang cpp --method /ga.api.channel_music.ChannelMusicLogic/ChannelMusicV2SetNextMusic
2060:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 2060 --source api/channel_music/grpc_channel_music_cpp.proto --lang cpp --method /ga.api.channel_music.ChannelMusicLogic/ChannelMusicV2GetList
2061:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 2061 --source api/channel_music/grpc_channel_music_cpp.proto --lang cpp --method /ga.api.channel_music.ChannelMusicLogic/ChannelMusicV2SetPlayMode
2062:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 2062 --source api/channel_music/grpc_channel_music_cpp.proto --lang cpp --method /ga.api.channel_music.ChannelMusicLogic/ChannelMusicV2SetVolume
2064:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 2064 --source api/channel_music/grpc_channel_music_cpp.proto --lang cpp --method /ga.api.channel_music.ChannelMusicLogic/ChannelMusicV2GetStatus
2065:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 2065 --source api/channel_music/grpc_channel_music_cpp.proto --lang cpp --method /ga.api.channel_music.ChannelMusicLogic/ChannelMusicV2SetCanShare
2066:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 2066 --source api/channel_music/grpc_channel_music_cpp.proto --lang cpp --method /ga.api.channel_music.ChannelMusicLogic/ChannelMusicV2PlayTheMusic
2072:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 2072 --source api/channel_music/grpc_channel_music_cpp.proto --lang cpp --method /ga.api.channel_music.ChannelMusicLogic/ChannelMusicV2SetFreeMode
2097:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 2097 --source api/channel_music/grpc_channel_music_cpp.proto --lang cpp --method /ga.api.channel_music.ChannelMusicLogic/ChannelMusicV2CheckAddMusic
450:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 450 --source api/channel_music/grpc_channel_music_cpp.proto --lang cpp --method /ga.api.channel_music.ChannelMusicLogic/StartChannelMusic
451:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 451 --source api/channel_music/grpc_channel_music_cpp.proto --lang cpp --method /ga.api.channel_music.ChannelMusicLogic/StopChannelMusic
452:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 452 --source api/channel_music/grpc_channel_music_cpp.proto --lang cpp --method /ga.api.channel_music.ChannelMusicLogic/GetChannelCurrMusic
453:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 453 --source api/channel_music/grpc_channel_music_cpp.proto --lang cpp --method /ga.api.channel_music.ChannelMusicLogic/GetChannelMusicList
454:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 454 --source api/channel_music/grpc_channel_music_cpp.proto --lang cpp --method /ga.api.channel_music.ChannelMusicLogic/GetChannelMusicInfoByMid
455:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 455 --source api/channel_music/grpc_channel_music_cpp.proto --lang cpp --method /ga.api.channel_music.ChannelMusicLogic/SearchChannelMusic
