cmdInfoList:
  - source: api/circle/grpc_circle_cpp.proto
    cmd: 142
    lang: cpp
    method: /ga.api.circle.CircleLogic/GameCircleGetMyCircle
  - source: api/circle/grpc_circle_cpp.proto
    cmd: 143
    lang: cpp
    method: /ga.api.circle.CircleLogic/GameCircleGetTopList
  - source: api/circle/grpc_circle_cpp.proto
    cmd: 144
    lang: cpp
    method: /ga.api.circle.CircleLogic/GameCircleJoin
  - source: api/circle/grpc_circle_cpp.proto
    cmd: 145
    lang: cpp
    method: /ga.api.circle.CircleLogic/GameCircleQuit
  - source: api/circle/grpc_circle_cpp.proto
    cmd: 146
    lang: cpp
    method: /ga.api.circle.CircleLogic/GameCircleGetTopicList
  - source: api/circle/grpc_circle_cpp.proto
    cmd: 147
    lang: cpp
    method: /ga.api.circle.CircleLogic/GameCircleGetTopic
  - source: api/circle/grpc_circle_cpp.proto
    cmd: 148
    lang: cpp
    method: /ga.api.circle.CircleLogic/GameCircleSendTopic
  - source: api/circle/grpc_circle_cpp.proto
    cmd: 149
    lang: cpp
    method: /ga.api.circle.CircleLogic/GameCircleGetCommentList
  - source: api/circle/grpc_circle_cpp.proto
    cmd: 150
    lang: cpp
    method: /ga.api.circle.CircleLogic/GameCircleSendComment
  - source: api/circle/grpc_circle_cpp.proto
    cmd: 151
    lang: cpp
    method: /ga.api.circle.CircleLogic/GameCircleLikeTopic
  - source: api/circle/grpc_circle_cpp.proto
    cmd: 152
    lang: cpp
    method: /ga.api.circle.CircleLogic/GameCircleReportTopic
  - source: api/circle/grpc_circle_cpp.proto
    cmd: 153
    lang: cpp
    method: /ga.api.circle.CircleLogic/GameCircleDeleteTopic
  - source: api/circle/grpc_circle_cpp.proto
    cmd: 154
    lang: cpp
    method: /ga.api.circle.CircleLogic/GameCircleDeleteComment
  - source: api/circle/grpc_circle_cpp.proto
    cmd: 155
    lang: cpp
    method: /ga.api.circle.CircleLogic/GameCircleGetCircle
  - source: api/circle/grpc_circle_cpp.proto
    cmd: 156
    lang: cpp
    method: /ga.api.circle.CircleLogic/GameCircleGetUserTopic
  - source: api/circle/grpc_circle_cpp.proto
    cmd: 157
    lang: cpp
    method: /ga.api.circle.CircleLogic/GameCircleMarkReaded
  - source: api/circle/grpc_circle_cpp.proto
    cmd: 160
    lang: cpp
    method: /ga.api.circle.CircleLogic/GameCircleGetLikeUserList
  - source: api/circle/grpc_circle_cpp.proto
    cmd: 164
    lang: cpp
    method: /ga.api.circle.CircleLogic/GameCircleMuteUser
  - source: api/circle/grpc_circle_cpp.proto
    cmd: 165
    lang: cpp
    method: /ga.api.circle.CircleLogic/GameCircleUnmuteUser
  - source: api/circle/grpc_circle_cpp.proto
    cmd: 176
    lang: cpp
    method: /ga.api.circle.CircleLogic/GameCircleCheckCircleUpdate
  - source: api/circle/grpc_circle_cpp.proto
    cmd: 177
    lang: cpp
    method: /ga.api.circle.CircleLogic/GameCircleSetMyCircleOrder
  - source: api/circle/grpc_circle_cpp.proto
    cmd: 178
    lang: cpp
    method: /ga.api.circle.CircleLogic/GameCircleHighlightTopic
  - source: api/circle/grpc_circle_cpp.proto
    cmd: 179
    lang: cpp
    method: /ga.api.circle.CircleLogic/GameCircleCancelHighlightTopic
  - source: api/circle/grpc_circle_cpp.proto
    cmd: 180
    lang: cpp
    method: /ga.api.circle.CircleLogic/GameCircleManagerDeleteComment
  - source: api/circle/grpc_circle_cpp.proto
    cmd: 300
    lang: cpp
    method: /ga.api.circle.CircleLogic/CircleGetList
  - source: api/circle/grpc_circle_cpp.proto
    cmd: 303
    lang: cpp
    method: /ga.api.circle.CircleLogic/CircleGetTopicList
  - source: api/circle/grpc_circle_cpp.proto
    cmd: 304
    lang: cpp
    method: /ga.api.circle.CircleLogic/CircleGetTopicDetail
  - source: api/circle/grpc_circle_cpp.proto
    cmd: 305
    lang: cpp
    method: /ga.api.circle.CircleLogic/CirclePostTopic
  - source: api/circle/grpc_circle_cpp.proto
    cmd: 306
    lang: cpp
    method: /ga.api.circle.CircleLogic/CircleGetCommentList
  - source: api/circle/grpc_circle_cpp.proto
    cmd: 307
    lang: cpp
    method: /ga.api.circle.CircleLogic/CirclePostComment
  - source: api/circle/grpc_circle_cpp.proto
    cmd: 308
    lang: cpp
    method: /ga.api.circle.CircleLogic/CircleGetCircleDetail
  - source: api/circle/grpc_circle_cpp.proto
    cmd: 309
    lang: cpp
    method: /ga.api.circle.CircleLogic/CircleGetUserTopicList
  - source: api/circle/grpc_circle_cpp.proto
    cmd: 310
    lang: cpp
    method: /ga.api.circle.CircleLogic/CircleGetLikeUserList
  - source: api/circle/grpc_circle_cpp.proto
    cmd: 311
    lang: cpp
    method: /ga.api.circle.CircleLogic/GameCircleMuteReasonList
  - source: api/circle/grpc_circle_cpp.proto
    cmd: 312
    lang: cpp
    method: /ga.api.circle.CircleLogic/CircleGetCommentListV2
  - source: api/circle/grpc_circle_cpp.proto
    cmd: 313
    lang: cpp
    method: /ga.api.circle.CircleLogic/CircleGetCommentReplyList
  - source: api/circle/grpc_circle_cpp.proto
    cmd: 314
    lang: cpp
    method: /ga.api.circle.CircleLogic/CircleGetAnn
  - source: api/circle/grpc_circle_cpp.proto
    cmd: 315
    lang: cpp
    method: /ga.api.circle.CircleLogic/CircleGetHot
  - source: api/circle/grpc_circle_cpp.proto
    cmd: 470
    lang: cpp
    method: /ga.api.circle.CircleLogic/PostRecruit
  - source: api/circle/grpc_circle_cpp.proto
    cmd: 471
    lang: cpp
    method: /ga.api.circle.CircleLogic/SupportRecruit
  - source: api/circle/grpc_circle_cpp.proto
    cmd: 472
    lang: cpp
    method: /ga.api.circle.CircleLogic/GetRecruitList
  - source: api/circle/grpc_circle_cpp.proto
    cmd: 473
    lang: cpp
    method: /ga.api.circle.CircleLogic/ReportRecruit
  - source: api/circle/grpc_circle_cpp.proto
    cmd: 474
    lang: cpp
    method: /ga.api.circle.CircleLogic/GetRecruitRecommendGameList

