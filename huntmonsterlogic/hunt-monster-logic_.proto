syntax = "proto3";

package ga.huntmonsterlogic;

import "ga_base.proto";

option java_package = "com.yiyou.ga.model.proto";
option go_package = "golang.52tt.com/protocol/app/huntmonsterlogic";

message HuntMonsterLogicReq {
    ga.BaseReq base_req = 1;
    //string a = 2;
}

message HuntMonsterLogicResp {
    ga.BaseResp base_resp = 1;
    //string b = 2;
}

message TimeRange {
    uint32 begin_time = 1;
    uint32 end_time = 2;
}

//取boss活动开启时间段，和boss倒计时和逃跑时间
//message GetMonsterActivityConfReq {
//    ga.BaseReq base_req = 1;
//}

//message GetMonsterActivityConfResp {
//    ga.BaseResp base_resp = 1;
//    repeated TimeRange time_range_list = 2;
//    int64 count_down = 3; //boss从出现到开始打的倒计时
//    int64 live_time  = 4; //boss从出现到结束的时间，包括count_down
//}

message User {
    uint32 uid = 1;
    string account = 2;
    string nickname = 3;
    string alias    = 4;
    int32 sex       = 5;
    int64 value    = 6;
}

enum MonsterStatus {
    ACTIVATE = 0; //当前房间正在打或者倒计时的boss
    IN_QUEUE = 1; //在房间队列中，还没激活
    ESCAPE = 2; //时间到逃跑了
    KILLED = 3; //击败了
}

//怪物信息
message Monster {
    uint32 channel_id = 1;  //触发房间
    User   trigger_user = 2; //触发的用户
    int64 monster_id = 3; //服务端生成怪物唯一ID
    int64 curr_life_point = 4;  //当前生命值
    int64 max_life_point = 5;   //最大生命值
    int64 create_time = 6; //触发时间戳
    string icon_url = 7; //boss美术资源
    MonsterStatus status = 8;//
    int64 count_down = 9; //boss从出现到开始打的倒计时
    int64 live_time  = 10; //boss从出现到结束的时间，包括count_down
}

//攻击效果数据
message AttackResult {
    int64 attack_value = 1; //本次攻击有效值，包括暴击值
    int64 critical_value = 2; //暴击值
    int64 total_value = 3; //本人攻击当前怪物总值
    int64 attack_rank = 4; //对当前怪物造成伤害排名
}

//怪物出现全服推送
//message MonsterAppearedPushMsg {
//    Monster monster = 1;
//}

//打boss玩法房间推送相关
message HuntMonsterChannelPushMsg {
    enum HuntMonsterChannelMsgType {
       MonsterInfoChange = 0; //当前房间怪物生命值变化，逃跑，击败
       TopThreeChange    = 1; //当前房间攻击怪物排行前3变化推送，monster字段和rank_list字段都有值，用于更新排行榜前3和怪物血量。
    }
    HuntMonsterChannelMsgType hunt_monster_msg_type = 1; // 根据 enum HuntMonsterChannelMsgType 读对应的字段
    Monster monster = 2;  //当前房间怪物生命值变化推送
    repeated User rank_list = 3; //当前房间攻击怪物排行前3变化推送
    int64 version = 4; //版本号，客户端取最大版本的值
}

enum MonsterAwardType {
    INVALID    = 0;
    PRESENT    = 1; //包裹礼物
    HEAD_WEARA = 2; //头像框
    MIC_WEARA  = 3; //麦位框
    HORSE      = 4; //坐骑
    TBEAM      = 5; //T豆
}

message AwardItem {
    MonsterAwardType award_type = 1;
    string item_id = 2;
    string icon_url = 3;
    int64 item_cnt = 4;
    int64 hold_day = 5;// 麦位框天数
    string name = 6; //礼物名
}

message UserAttackData {
    uint64 attack_monster_cnt = 1; //打龙数量
    uint64 award_tbean_value  = 2; //获得奖励T豆价值
}

//打boss奖励推送，单推给个人的
message HuntMonsterAwardPushMsg {
    uint32 uid = 1;
    uint32 channel_id = 2;
    int64 monster_id = 3;
    bool received = 4; //这个推送会发两条，打完龙后收到一条展示，真正收到奖励的时候收到一条。received == false 是展示用， true是奖励到账
    repeated AwardItem award_list = 5;
    int64 my_attack_cnt = 6; //自己打龙伤害总值。
    bool isdefeat = 7 ; //龙是否击败
    repeated User rank_list = 8; //前3排名
    UserAttackData user_attack_data = 9; //本次活动当前用户攻击数据统计
}

//随机取一个前有boss的房间
message GetOneMonsterChannelReq {
    ga.BaseReq base_req = 1;
}

message GetOneMonsterChannelResp {
    ga.BaseResp base_resp = 1;
    uint32 monster_channel_id = 2;
}

//取当前房间的boss列表，包括正在打的boss和排队中的boss
message GetMonsterListReq {
    ga.BaseReq base_req = 1;
    uint32 channel_id = 2;
}

message GetMonsterListResp {
    ga.BaseResp base_resp = 1;
    repeated Monster monster_list = 2;
    uint32 item_cnt = 3; //目前拥有道具数量
    AttackResult attack_result = 4; //本人对当前怪物攻击的效果
    repeated User rank_list = 5;
}

message AttackMonsterReq {
    ga.BaseReq base_req = 1;
    uint32 uid = 2;
    uint32 channel_id = 3;
    int64 monster_id = 4;
    int64 cnt = 5; //使用道具数量
}

message AttackMonsterResp {
    ga.BaseResp base_resp = 1;
    Monster monster = 2;
    AttackResult atc_res = 3;
    int64 attack_delay = 4; //攻击时间间隔，客户端根据这个值调整用户点击攻击按钮的时间间隔。
    int64 item_cnt = 5; //剩余道具数量
    int64 version = 6; //版本号，客户端取最大版本的值
}


message GetHuntMonsterItemReq {
    ga.BaseReq base_req = 1;
}

message GetHuntMonsterItemResp {
    ga.BaseResp base_resp = 1;
    int64 item_cnt = 2; //当前道具数量
    int64 version = 3; //版本号，客户端取最大版本的值
    UserAttackData user_attack_data = 4; //本次活动当前用户攻击数据统计
}

message GetUserHuntMissionInfoReq {
    ga.BaseReq base_req = 1;
    uint32  uid = 2;
    uint32  mission_type = 3;   // see ga_base.proto HuntMonsterMissionType
    uint32  channel_id = 4;    
}

message GetUserHuntMissionInfoResp {
    ga.BaseResp base_resp = 1;
    bool  is_finish = 2;
    string pop_msg = 3;
    int64 update_ms = 4;  // 毫秒  
}