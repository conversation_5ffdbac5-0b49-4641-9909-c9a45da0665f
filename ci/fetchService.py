# !/usr/bin/python
import os
import re
import sys

import yaml


def print_init_parameters(global_config, inventory_file, last_commit_sha, extra_build_services):
    print("--------------------")
    print("global config: {}".format(global_config))
    print("inventory config: {}".format(inventory_file))
    print("last commit sha: {}".format(last_commit_sha))
    print("extra build services: {}".format(extra_build_services))
    print("--------------------")


def find_out_which_services_need_to_build(global_config, inventory_file, updated_paths, extra_build_service_map):
    f = open(inventory_file)
    res = yaml.load(f, Loader=yaml.FullLoader)
    f.close()

    check_repeat_map = {}
    go_build_services = []
    docker_build_services = []
    build_only_services = []
    check_path = []
    command_lines = []

    # loop to compare service in inventory.yaml
    for key in res.get('categories'):
        for dict_ in res.get("categories").get(key).get("targets"):
            if not dict_.get("type"):
                continue

            for type in dict_["type"].split(","):
                if not dict_.get("src"):
                    continue
                if not dict_.get("name"):
                    continue

                name = dict_.get("name")
                src = dict_.get("src") + "/"
                if name not in extra_build_service_map:
                    for updatePath in updated_paths:
                        if not updatePath.startswith(src):
                            continue
                        add_to_build_list(type, name, src, key, global_config, check_repeat_map, go_build_services,
                                          docker_build_services, build_only_services, command_lines, check_path)
                else:
                    add_to_build_list(type, name, src, key, global_config, check_repeat_map, go_build_services,
                                      docker_build_services, build_only_services, command_lines, check_path)

    write_to_global_config(global_config, go_build_services, docker_build_services, build_only_services, command_lines,
                           check_path)


def add_to_build_list(type, name, src, key, global_config, check_repeat_map, go_build_services, docker_build_services,
                      build_only_services, command_lines, check_path):
    if type == "service":
        if name not in check_repeat_map:
            check_repeat_map[name] = 1
            go_build_services.append(name)
            check_path.append(name+":"+src)
            command_lines.append("echo " + name.replace("-", "_") + "_path=" + src + ">>" + global_config)

    elif type == "docker-service":
        if "docker-" + name not in check_repeat_map:
            check_repeat_map["docker-" + name] = 1
            docker_build_services.append(name)
            check_path.append(name+":"+src)
            command_lines.append("echo " + name.replace("-", "_") + "_path=" + src + ">>" + global_config)
            command_lines.append("echo " + name.replace("-", "_") + "_category=" + key + ">>" + global_config)

    elif type == "build-only":
        if "build-only-" + name not in check_repeat_map:
            check_repeat_map["build-only-" + name] = 1
            build_only_services.append(name)
            docker_build_services.append(name)
            check_path.append(name+":"+src)
            command_lines.append("echo " + name.replace("-", "_") + "_path=" + src + ">>" + global_config)


def write_to_global_config(global_config, go_build_services, docker_build_services, build_only_services, command_lines,
                           check_path):
    if len(go_build_services) > 0:
        code = os.system("echo go_build_services=" + ','.join(go_build_services) + ">>" + global_config)
        if code != 0:
            exit(1)

    if len(docker_build_services) > 0:
        code = os.system("echo docker_build_services=" + ','.join(docker_build_services) + ">>" + global_config)
        if code != 0:
            exit(1)

    if len(build_only_services) > 0:
        code = os.system("echo build_only_services=" + ','.join(build_only_services) + ">>" + global_config)
        if code != 0:
            exit(1)

    if len(check_path) > 0:
        code = os.system("echo check_path=" + ','.join(check_path) + ">>" + global_config)
        if code != 0:
            exit(1)

    for line in command_lines:
        code = os.system(line)
        if code != 0:
            exit(1)


def find_out_kubernetes_conf_path(updated_paths):
    # Avoid interaction
    # commitFileRecord = os.popen("git diff --dirstat=files,0,cumulative --name-status " + last_commit_sha).read()
    print("-----------")
    print("git commit updated_paths:")
    print(updated_paths)
    print("-----------")
    lastCommitFile = updated_paths
    checkRepeatConfPath = {}
    kubernetes_conf_path = []
    for f in lastCommitFile:
        if f != "":
            c = re.search('\s[\D\d]*', f)
            if c:
                raw = c.group(0)
                real = raw.lstrip().rstrip("/")
                print(real)
                if real not in checkRepeatConfPath:
                    if not real.startswith("kubernetes") or real.startswith("kubernetes/charts") or not real.startswith(
                            "kubernetes/develop"):
                        continue
                    kubernetes_conf_path = kubernetes_conf_path.append(real)
                    checkRepeatConfPath[real] = 1

    if len(kubernetes_conf_path)>0:
        code = os.system("echo kubernetes_conf_path=" + ','.join(kubernetes_conf_path) + ">>" + sys.argv[1])
        if code != 0:
            exit(1)


def main():
    # define some args
    global_config = sys.argv[1]
    inventory_file = sys.argv[2]
    updated_paths = sys.argv[3].split(",")

    extra_build_services = []
    extra_build_service_map = {}

    if len(sys.argv) > 4:
        extra_build_services = sys.argv[4].split(",")
    for service in extra_build_services:
        extra_build_service_map[service] = 1

    # print something only
    # print_init_parameters(global_config, inventory_file, last_commit_sha, extra_build_services)
    #
    # # find the update path compare to last commit in this branch if not assign target service
    # if len(extra_build_services)==0:
    #     updated_paths = get_update_paths(last_commit_sha)
    # else:
    #     updated_paths = ""
    # find out the services which need to build, and write to the global config file which pass through to the next stage
    find_out_which_services_need_to_build(global_config, inventory_file, updated_paths, extra_build_service_map)

    # record kubernetes path
    find_out_kubernetes_conf_path(updated_paths)


if __name__ == "__main__":
    main()
