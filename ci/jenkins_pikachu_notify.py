#!/usr/bin/env python
# --coding:utf-8--

import json
import os
import sys
from urllib import request

msg = "[{}]{}正在覆盖{}的服务{}(tag：{})"


def send_to_pikachu(environment, from_user, to_user, service_name, image_tag):
    url = "https://dark-portal.ttyuyin.com:9090/feishu/api/v1/ci/golang"
    headers = {
        "Content-Type": "application/json",
    }

    req_body = {
        "chat_id": "oc_2c2ed180e64d42aa3c032acb77c05af0",
        "msg_type": "text",
        "environment": environment,
        "from_user": from_user,
        "to_user": to_user,
        "service_name": service_name,
        "image_tag": image_tag
    }

    data = bytes(json.dumps(req_body), encoding='utf8')
    req = request.Request(url=url, data=data, headers=headers, method='POST')
    try:
        response = request.urlopen(req)
    except Exception as e:
        print(e.read().decode())
        return

    rsp_body = response.read().decode('utf-8')
    rsp_dict = json.loads(rsp_body)
    code = rsp_dict.get("code", -1)
    if code != 0:
        print("send message error, code = ", code, ", msg =", rsp_dict.get("msg", ""))


def get_image_tag(service_name):
    p = os.popen(
        "kubectl get deploy %s --kubeconfig kubernetes/develop/pokemon-kube-config.yaml -n quicksilver -o=yaml | "
        "grep \" image:\" | awk -F:  '{print $3}'" % service_name)
    image_tag = p.read()
    return image_tag


def main():
    from_user = os.getenv('gitlabMergedByUser')
    environment = "测试环境"

    i = 1
    while i < len(sys.argv):
        service_name = sys.argv[i]
        i += 1
        image_tag = get_image_tag(service_name)
        array = image_tag.split("-", -1)
        to_user = ""
        if len(array) >= 2:
            to_user = array[1]

        if not (
                to_user == "master" or to_user.startswith("release")
                or to_user.startswith("develop") or to_user.startswith("hotfix")
                or to_user.startswith("feature") or to_user == ""):
            if from_user != to_user:
                print(msg.format(environment, from_user, to_user, service_name, image_tag))
                send_to_pikachu(environment, from_user, to_user, service_name, image_tag)


if __name__ == "__main__":
    main()
