# 定义触发的步骤
# test
stages:
  - build
  - deploy
  - test

# 构建参数  不可变动
build:
  - name: service_name  # 服务名
    service_module: service_module_name   # 服务名

# 服务接口测试环境 【开发环境】 不可变动
deploy:
  strategy: single
  envs_name:
    - dev

# 测试 不可变动
test:
  - name: quicksilver_service_autotest
    repo:
      strategy: currentRepo

rules:
  branchs:
    - feature/*
  events:
    - push
  strategy:
    auto_cancel: false
  match_folders:
    match_folders_tree:
    # 服务-业务代码路径-测试代码路径
#      - name: helloworld  # 服务名
#        service_module: helloworld  # 服务名
#        test_src: clients/channel  # 服务测试代码路径【不写默认不执行】
#        file_tree:
#          - services/helloworld  # 服务代码路径
      - name: helloworld  # 服务名
        service_module: helloworld  # 服务名
        test_src: clients/channel  # 服务测试代码路径
        file_tree:
          - services/helloworld  # 服务代码路径
      - name: websession
        service_module: websession
        file_tree:
          - services/websession
      - name: market-turnover
        service_module: market-turnover
        file_tree:
          - services/market-turnover
      - name: gift-data-statistics
        service_module: gift-data-statistics
        file_tree:
          - services/gift-data-statistics
      - name: real-name-black-list
        service_module: real-name-black-list
        file_tree:
          - services/real-name-black-list
      - name: grpc-load-balancer
        service_module: grpc-load-balancer
        file_tree:
          - services/grpc-load-balancer
      - name: token-v2
        service_module: token-v2
        file_tree:
          - services/token-v2
      - name: oauth-logic
        service_module: oauth-logic
        file_tree:
          - services/oauth-logic
      - name: online-logic
        service_module: online-logic
        file_tree:
          - services/online-logic
      - name: channel-follow
        service_module: channel-follow
        file_tree:
          - services/channel-follow
      - name: channel-follow-subscriber
        service_module: channel-follow-subscriber
        file_tree:
          - services/channel-follow-subscriber
      - name: im-logic
        service_module: im-logic
        file_tree:
          - services/im-logic
      - name: internal-logic
        service_module: internal-logic
        file_tree:
          - services/internal-logic
      - name: channel-personalization-config-api
        service_module: channel-personalization-config-api
        file_tree:
          - services/channel-personalization/config-api
      - name: channel-personalization
        service_module: channel-personalization
        file_tree:
          - services/channel-personalization
      - name: green-baba-subscriber
        service_module: green-baba-subscriber
        file_tree:
          - services/green-baba-subscriber
      - name: smash-egg
        service_module: smash-egg
        file_tree:
          - services/smash-egg/kernel
        test_src: clients/smash-egg
      - name: smash-egg-admission
        service_module: smash-egg-admission
        file_tree:
          - services/smash-egg/admission
      - name: smash-egg-logic
        service_module: smash-egg-logic
        file_tree:
          - services/smash-egg/logic
      - name: tmp-channel-alloc
        service_module: tmp-channel-alloc
        file_tree:
          - services/tmp-channel-alloc
      - name: user-black-list
        service_module: user-black-list
        file_tree:
          - services/user-black-list
      - name: user-black-list-logic
        service_module: user-black-list-logic
        file_tree:
          - services/user-black-list-logic
      - name: knock
        service_module: knock
        file_tree:
          - services/knock
      - name: knocklogic
        service_module: knocklogic
        file_tree:
          - docker-service
      - name: active-fans
        service_module: active-fans
        file_tree:
          - services/active-fans
      - name: active-fans-subscriber
        service_module: active-fans-subscriber
        file_tree:
          - services/active-fans-subscriber
      - name: user-achievement
        service_module: user-achievement
        file_tree:
          - services/user-achievement
      - name: tagmatchrecommend
        service_module: tagmatchrecommend
        file_tree:
          - services/tagmatchrecommend
      - name: audio-stream
        service_module: audio-stream
        file_tree:
          - services/audio-stream
      - name: avatar
        service_module: avatar
        file_tree:
          - services/avatar
      - name: avatar-api
        service_module: avatar-api
        file_tree:
          - services/avatar/api
      - name: timeline-v2
        service_module: timeline-v2
        file_tree:
          - services/timeline-v2
      - name: channelbackground
        service_module: channelbackground
        file_tree:
          - services/channelbackground
      - name: channelbackground-logic
        service_module: channelbackground-logic
        file_tree:
          - services/channelbackground-logic
      - name: profile-logic
        service_module: profile-logic
        file_tree:
          - services/profile-logic
      - name: user-decoration
        service_module: user-decoration
        file_tree:
          - services/user-decoration
      - name: filebeat-logsvr
        service_module: filebeat-logsvr
        file_tree:
          - services/logsvr
      - name: award-http-logic
        service_module: award-http-logic
        file_tree:
          - services/award-http-logic
      - name: conversion
        service_module: conversion
        file_tree:
          - services/conversion
      - name: authority-permission
        service_module: authority-permission
        file_tree:
          - services/permission
      - name: log-main-test
        service_module: log-main-test
        file_tree:
          - services/logsvr/main-test
      - name: log-side-car
        service_module: log-side-car
        file_tree:
          - services/logsvr/sidecar
      - name: qiniu-storage-token
        service_module: qiniu-storage-token
        file_tree:
          - services/qiniu-storage-token
      - name: golddiamonn
        service_module: golddiamonn
        file_tree:
          - services/golddiamonn
      - name: yuyin-gold
        service_module: yuyin-gold
        file_tree:
          - services/yuyin-gold
      - name: golddiamond-logic
        service_module: golddiamond-logic
        file_tree:
          - services/golddiamond-logic
      - name: lbs-supervise-server
        service_module: lbs-supervise-server
        file_tree:
          - services/lbs/lbs-supervise-server
      - name: account-appeal
        service_module: account-appeal
        file_tree:
          - services/account-appeal
      - name: account-appeal-http-logic
        service_module: account-appeal-http-logic
        file_tree:
          - services/account-appeal-http-logic
      - name: tab-notify
        service_module: tab-notify
        file_tree:
          - services/tab-notify
      - name: channel-room
        service_module: channel-room
        file_tree:
          - services/channel-room
      - name: missiongo-logic
        service_module: missiongo-logic
        file_tree:
          - services/missiongo-logic
      - name: httpgo-logic
        service_module: httpgo-logic
        file_tree:
          - services/httpgo-logic
      - name: datacenter-report-http-logic
        service_module: datacenter-report-http-logic
        file_tree:
          - services/datacenter-report-http-logic
      - name: playerlogic
        service_module: playerlogic
        file_tree:
          - services/playerlogic
      - name: player-found
        service_module: player-found
        file_tree:
          - services/player-found
      - name: user-visitor-record-logic
        service_module: user-visitor-record-logic
        file_tree:
          - services/user-visitor-record-logic
      - name: user-visitor-record
        service_module: user-visitor-record
        file_tree:
          - services/user-visitor-record
      - name: channel-quality-logic
        service_module: channel-quality-logic
        file_tree:
          - services/channel-quality-logic
      - name: ad-center-logic
        service_module: ad-center-logic
        file_tree:
          - services/ad-center-logic
      - name: ad-center
        service_module: ad-center
        file_tree:
          - services/ad-center
      - name: unclaimed-logic
        service_module: unclaimed-logic
        file_tree:
          - services/unclaimed-logic
      - name: unclaimed
        service_module: unclaimed
        file_tree:
          - services/unclaimed
      - name: masked-call-logic
        service_module: masked-call-logic
        file_tree:
          - services/masked-call-logic
      - name: masked-call
        service_module: masked-call
        file_tree:
          - services/masked-call
      - name: im-promote-logic
        service_module: im-promote-logic
        file_tree:
          - services/im-promote-logic
      - name: configserver
        service_module: configserver
        file_tree:
          - services/configserver
      - name: smash-egg-notify
        service_module: smash-egg-notify
        file_tree:
          - services/smash-egg-notify
      - name: smash-egg-notify-logic
        service_module: smash-egg-notify-logic
        file_tree:
          - services/smash-egg-notify-logic
      - name: offlinepush
        service_module: offlinepush
        file_tree:
          - services/offlinepush
      - name: immsg-subscriber
        service_module: immsg-subscriber
        file_tree:
          - services/immsg-subscriber
      - name: auto-award
        service_module: auto-award
        file_tree:
          - services/auto-award
      - name: reconcile
        service_module: reconcile
        file_tree:
          - services/reconcile
      - name: channel-performance
        service_module: channel-performance
        file_tree:
          - services/channel-performance
      - name: channel-performance-logic
        service_module: channel-performance-logic
        file_tree:
          - services/channel-performance-logic
      - name: channel-level
        service_module: channel-level
        file_tree:
          - services/channel-level
      - name: channel-level-logic
        service_module: channel-level-logic
        file_tree:
          - services/channel-level-logic
      - name: recommend-dialog
        service_module: recommend-dialog
        file_tree:
          - services/recommend-dialog
      - name: test-ci-new
        service_module: test-ci-new
        file_tree:
          - services/test-ci-new
      - name: master-apprentice
        service_module: master-apprentice
        file_tree:
          - services/master-apprentice
      - name: master-apprentice-logic
        service_module: master-apprentice-logic
        file_tree:
          - services/master-apprentice-logic
      - name: master-apprentice-http
        service_module: master-apprentice-http
        file_tree:
          - services/master-apprentice-http
      - name: welfare-center
        service_module: welfare-center
        file_tree:
          - services/welfare-center
      - name: welfare-center-http
        service_module: welfare-center-http
        file_tree:
          - services/welfare-center-http
      - name: userpresent-subscriber-go
        service_module: userpresent-subscriber-go
        file_tree:
          - services/userpresent-subscriber-go
      - name: channel-team
        service_module: channel-team
        file_tree:
          - services/channel-team
      - name: channel-team-logic
        service_module: channel-team-logic
        file_tree:
          - services/channel-team-logic
      - name: room-master-apprentice
        service_module: room-master-apprentice
        file_tree:
          - services/room-master-apprentice
      - name: room-master-apprentice-logic
        service_module: room-master-apprentice-logic
        file_tree:
          - services/room-master-apprentice-logic
      - name: room-master-apprentice-http
        service_module: room-master-apprentice-http
        file_tree:
          - services/room-master-apprentice-http
      - name: gameradar
        service_module: gameradar
        file_tree:
          - services/gameradar
      - name: gameradar-logic
        service_module: gameradar-logic
        file_tree:
          - services/gameradar-logic
      - name: event-report-logic
        service_module: event-report-logic
        file_tree:
          - services/event-report-logic
      - name: operate-platform
        service_module: operate-platform
        file_tree:
          - services/operate-platform
      - name: present-go-logic
        service_module: present-go-logic
        file_tree:
          - services/present-go-logic
      - name: imstranger-go
        service_module: imstranger-go
        file_tree:
          - services/imstranger-go
      - name: im-activity-center
        service_module: im-activity-center
        file_tree:
          - services/im-activity-center
      - name: im-activity-center-logic
        service_module: im-activity-center-logic
        file_tree:
          - services/im-activity-center-logic
      - name: apicenter-go
        service_module: apicenter-go
        file_tree:
          - services/apicenter-go
      - name: channel-game
        service_module: channel-game
        file_tree:
          - services/channel-game
      - name: channel-minigame-go-logic
        service_module: channel-minigame-go-logic
        file_tree:
          - services/channel-minigame-go-logic
      - name: usersetting-go
        service_module: usersetting-go
        file_tree:
          - services/usersetting-go
      - name: present-middleware
        service_module: present-middleware
        file_tree:
          - services/present-middleware
      - name: medalgo
        service_module: medalgo
        file_tree:
          - services/medalgo
      - name: photo-album-go
        service_module: photo-album-go
        file_tree:
          - services/photo-album-go
      - name: tshield-room-event-report
        service_module: tshield-room-event-report
        file_tree:
          - services/tshield-room-event-report
      - name: reconcile-additional-order
        service_module: reconcile-additional-order
        file_tree:
          - services/reconcile-additional-order
      - name: headwear-go
        service_module: headwear-go
        file_tree:
          - services/headwear-go
      - name: present-event-consumer
        service_module: present-event-consumer
        file_tree:
          - services/present-event-consumer
      - name: udesk-api-logic
        service_module: udesk-api-logic
        file_tree:
          - services/udesk-api-logic
      - name: udesk-api
        service_module: udesk-api
        file_tree:
          - services/udesk-api
      - name: udesk-api-http
        service_module: udesk-api-http
        file_tree:
          - services/udesk-api-http
      - name: official-live-channel
        service_module: official-live-channel
        file_tree:
          - services/official-live-channel
      - name: official-live-channel-logic
        service_module: official-live-channel-logic
        file_tree:
          - services/official-live-channel-logic
      - name: channel-msg-express
        service_module: channel-msg-express
        file_tree:
          - services/channel-msg-express
      - name: channel-lottery
        service_module: channel-lottery
        file_tree:
          - services/channel-lottery
      - name: channel-lottery-logic
        service_module: channel-lottery-logic
        file_tree:
          - services/channel-lottery-logic
      - name: client-gen-push
        service_module: client-gen-push
        file_tree:
          - services/client-gen-push
      - name: client-gen-push-logic
        service_module: client-gen-push-logic
        file_tree:
          - services/client-gen-push-logic
      - name: channelol-stat-go
        service_module: channelol-stat-go
        file_tree:
          - services/channelol-stat-go
      - name: recall-new-user
        service_module: recall-new-user
        file_tree:
          - services/recall-new-user
      - name: channel-mic-ext
        service_module: channel-mic-ext
        file_tree:
          - services/channel-mic-ext
      - name: channelext-logic-go
        service_module: channelext-logic-go
        file_tree:
          - services/channelext-logic-go
      - name: invite
        service_module: invite
        file_tree:
          - services/invite
      - name: invitelogic
        service_module: invitelogic
        file_tree:
          - services/invitelogic
      - name: retcode-metrics
        service_module: retcode-metrics
        file_tree:
          - services/retcode-metrics
      - name: client-conf-mgr-logic
        service_module: client-conf-mgr-logic
        file_tree:
          - services/client-conf-mgr-logic
      - name: client-conf-mgr
        service_module: client-conf-mgr
        file_tree:
          - services/client-conf-mgr
      - name: censoring-proxy
        service_module: censoring-proxy
        file_tree:
          - services/censoring-proxy
      - name: super-star-protector
        service_module: super-star-protector
        file_tree:
          - services/super-star-protector
      - name: comm-push-schedule
        service_module: comm-push-schedule
        file_tree:
          - services/comm-push-schedule
      - name: comm-push-monitor
        service_module: comm-push-monitor
        file_tree:
          - services/comm-push-monitor
      - name: silent-user-audit
        service_module: silent-user-audit
        file_tree:
          - services/silent-user-audit
      - name: sub-new-user-recall
        service_module: sub-new-user-recall
        file_tree:
          - services/sub-new-user-recall
      - name: auth-http-logic
        service_module: auth-http-logic
        file_tree:
          - services/auth-http-logic
      - name: channel-core-logic
        service_module: channel-core-logic
        file_tree:
          - services/channel-core-logic
      - name: comm-push-placeholder
        service_module: comm-push-placeholder
        file_tree:
          - services/comm-push-placeholder
      - name: guild-management-http-logic
        service_module: guild-management-http-logic
        file_tree:
          - services/guild-management/guild-management-http-logic
      - name: unified-interface-logic
        service_module: unified-interface-logic
        file_tree:
          - services/unified-interface-logic
      - name: tt-regulation-config
        service_module: tt-regulation-config
        file_tree:
          - services/tt-regulation-config
      - name: user-tag-go
        service_module: user-tag-go
        file_tree:
          - services/user-tag-go
      - name: user-tag-logic-go
        service_module: user-tag-logic-go
        file_tree:
          - services/user-tag-logic-go
      - name: realnameauth-go-logic
        service_module: realnameauth-go-logic
        file_tree:
          - services/realnameauth-go/logic
      - name: magic-spirit
        service_module: magic-spirit
        file_tree:
          - services/magic-spirit
      - name: magic-spirit-logic
        service_module: magic-spirit-logic
        file_tree:
          - services/magic-spirit-logic
      - name: channel-open-game-controller
        service_module: channel-open-game-controller
        file_tree:
          - services/channel-open-game/controller
      - name: channel-open-game-controller-logic
        service_module: channel-open-game-controller-logic
        file_tree:
          - services/channel-open-game-logic/controller
      - name: channel-open-game
        service_module: channel-open-game
        file_tree:
          - services/channel-open-game/loader
      - name: channel-open-game-auth
        service_module: channel-open-game-auth
        file_tree:
          - services/channel-open-game/auth
      - name: channel-open-game-logic
        service_module: channel-open-game-logic
        file_tree:
          - services/channel-open-game-logic/loader
      - name: tmp-channel-open-game
        service_module: tmp-channel-open-game
        file_tree:
          - services/channel-open-game/matcher
      - name: channel-open-game-playmate
        service_module: channel-open-game-playmate
        file_tree:
          - services/channel-open-game/playmate
      - name: channel-open-game-record
        service_module: channel-open-game-record
        file_tree:
          - services/channel-open-game/record
      - name: unified-pay-notifier
        service_module: unified-pay-notifier
        file_tree:
          - services/unified-pay/notifier
      - name: unified-pay
        service_module: unified-pay
        file_tree:
          - services/unified-pay
      - name: exchange-logic-configer
        service_module: exchange-logic-configer
        file_tree:
          - services/exchange-logic/models/update_exchange_config
      - name: exchange-logic
        service_module: exchange-logic
        file_tree:
          - services/exchange-logic
      - name: exchange
        service_module: exchange
        file_tree:
          - services/exchange
      - name: unified-pay-callback
        service_module: unified-pay-callback
        file_tree:
          - services/unified-pay/pay-callback
      - name: find-friends-like-event-sub
        service_module: find-friends-like-event-sub
        file_tree:
          - services/find-friends-logic/event/like_event_sub
      - name: find-friends-logic
        service_module: find-friends-logic
        file_tree:
          - services/find-friends-logic
      - name: find-friends-quick-match-generator
        service_module: find-friends-quick-match-generator
        file_tree:
          - services/find-friends/cmd/quick_match_generator
      - name: find-friends-quick-match-config-sync
        service_module: find-friends-quick-match-config-sync
        file_tree:
          - services/find-friends/cmd/quick_match_config_sync
      - name: find-friends-get-user
        service_module: find-friends-get-user
        file_tree:
          - services/find-friends/cmd/get_user
      - name: find-friends
        service_module: find-friends
        file_tree:
          - services/find-friends
      - name: seqgen-v2-migration-tool
        service_module: seqgen-v2-migration-tool
        file_tree:
          - services/seqgen-v2/cmd/seqgen-migration-tool
      - name: seqgen-v2-benchmark
        service_module: seqgen-v2-benchmark
        file_tree:
          - services/seqgen-v2/cmd/seqgen-benchmark
      - name: seqgen-v2-mongo-slow-distribution
        service_module: seqgen-v2-mongo-slow-distribution
        file_tree:
          - services/seqgen-v2/cmd/mongo-slow-distribution
      - name: seqgen-v2
        service_module: seqgen-v2
        file_tree:
          - services/seqgen-v2
      - name: ugc-logic-grpc-gateway
        service_module: ugc-logic-grpc-gateway
        file_tree:
          - services/grpc_gateway/cmd/ugc-logic-grpc-gateway
      - name: internal-grpc-gateway
        service_module: internal-grpc-gateway
        file_tree:
          - services/grpc_gateway/cmd/internal-grpc-gateway
      - name: logic-grpc-gateway
        service_module: logic-grpc-gateway
        file_tree:
          - services/logic-grpc-gateway
      - name: apns-token
        service_module: apns-token
        file_tree:
          - services/push-notification/applepush/token
      - name: apns-push
        service_module: apns-push
        file_tree:
          - services/push-notification/applepush/push
      - name: apns-proxy
        service_module: apns-proxy
        file_tree:
          - services/push-notification/applepush/proxy
      - name: offline-msg-logic
        service_module: offline-msg-logic
        file_tree:
          - services/offline-msg-logic
      - name: push-logic
        service_module: push-logic
        file_tree:
          - services/push-logic
      - name: push-v3-auth
        service_module: push-v3-auth
        file_tree:
          - services/push-notification/v3/push-auth
      - name: push-v3-stats
        service_module: push-v3-stats
        file_tree:
          - services/push-notification/v3/push-stats
      - name: push-v3-token
        service_module: push-v3-token
        file_tree:
          - services/push-notification/v3/token
      - name: push-v3
        service_module: push-v3
        file_tree:
          - services/push-notification/v3/push-server
      - name: push-ack-subscriber
        service_module: push-ack-subscriber
        file_tree:
          - services/push-notification/v3/ack-subscriber
      - name: push-event-subscriber-batch
        service_module: push-event-subscriber-batch
        file_tree:
          - services/push-notification/v3/push-event-subscriber
      - name: push-event-subscriber-direct
        service_module: push-event-subscriber-direct
        file_tree:
          - services/push-notification/v3/push-event-subscriber
      - name: push-ops
        service_module: push-ops
        file_tree:
          - services/push-notification/v3/push-ops
      - name: push-v3-proxy
        service_module: push-v3-proxy
        file_tree:
          - services/push-notification/v3/push-proxy
      - name: push-getui-receipt
        service_module: push-getui-receipt
        file_tree:
          - services/push-notification/v3/getui-receipt
      - name: emoji-import-tool
        service_module: emoji-import-tool
        file_tree:
          - services/emoji/cmd/import_tool
      - name: emoji-official-import-tool
        service_module: emoji-official-import-tool
        file_tree:
          - services/emoji/cmd/official_import_tool
      - name: emoji
        service_module: emoji
        file_tree:
          - services/emoji
      - name: emoji-logic
        service_module: emoji-logic
        file_tree:
          - services/emoji-logic
      - name: fs-http-logic
        service_module: fs-http-logic
        file_tree:
          - services/fs-http-logic
      - name: synchronizer-test
        service_module: synchronizer-test
        file_tree:
          - clients/ugc/friendship/synchronizer/test
      - name: ugc-logic
        service_module: ugc-logic
        file_tree:
          - services/ugc-logic
      - name: ugc-robot-attitude
        service_module: ugc-robot-attitude
        file_tree:
          - services/ugc/robot-attitude
      - name: ugc-robot-follow
        service_module: ugc-robot-follow
        file_tree:
          - services/ugc/robot-follow
      - name: ugc-activity-stream
        service_module: ugc-activity-stream
        file_tree:
          - services/ugc/activity-stream-server
      - name: ugc-topic
        service_module: ugc-topic
        file_tree:
          - services/ugc/topic-server
      - name: ugc-interactive-subscriber
        service_module: ugc-interactive-subscriber
        file_tree:
          - services/ugc/interactive-subscriber
      - name: ugc-recommendation
        service_module: ugc-recommendation
        file_tree:
          - services/ugc/recommendation-server
      - name: ugc-recommendation-post
        service_module: ugc-recommendation-post
        file_tree:
          - services/ugc/recommendation-server
      - name: ugc-recommendation-lfm
        service_module: ugc-recommendation-lfm
        file_tree:
          - services/ugc/recommendation-lfm-server
      - name: ugc-friendship-synchronizer
        service_module: ugc-friendship-synchronizer
        file_tree:
          - services/ugc/friendship-synchronizer
      - name: ugc-content
        service_module: ugc-content
        file_tree:
          - services/ugc/content-server
      - name: ugc-post-examine
        service_module: ugc-post-examine
        file_tree:
          - services/ugc/post-examine-server
      - name: ugc-topic-feed-subscriber
        service_module: ugc-topic-feed-subscriber
        file_tree:
          - services/ugc/topic-feed-subscriber
      - name: ugc-post-hot-subscriber
        service_module: ugc-post-hot-subscriber
        file_tree:
          - services/ugc/post-hot-subscriber
      - name: ugc-interactive
        service_module: ugc-interactive
        file_tree:
          - services/ugc/interactive-server
      - name: ugc-friendship
        service_module: ugc-friendship
        file_tree:
          - services/ugc/friendship-server
      - name: ugc-recommendation-offline
        service_module: ugc-recommendation-offline
        file_tree:
          - services/ugc/recommendation-offline-server
      - name: ugc-user-feed-subscriber
        service_module: ugc-user-feed-subscriber
        file_tree:
          - services/ugc/user-feed-subscriber
      - name: ugc-recommendation-collector
        service_module: ugc-recommendation-collector
        file_tree:
          - services/ugc/recommendation-server/collector
      - name: ugc-user-behavior-subscriber
        service_module: ugc-user-behavior-subscriber
        file_tree:
          - services/ugc/user-behavior-subscriber
      - name: ugc-user-behavior
        service_module: ugc-user-behavior
        file_tree:
          - services/ugc/user-behavior-server
      - name: ugc-celebrity
        service_module: ugc-celebrity
        file_tree:
          - services/ugc/celebrity-server
      - name: ugc-robot-common
        service_module: ugc-robot-common
        file_tree:
          - services/ugc/robot-common
      - name: ugc-robot-commend
        service_module: ugc-robot-commend
        file_tree:
          - services/ugc/robot-commend
      - name: ugc-recommendation-ulib
        service_module: ugc-recommendation-ulib
        file_tree:
          - services/ugc/recommendation-ulib
      - name: ugc-user-online
        service_module: ugc-user-online
        file_tree:
          - services/ugc/user-online
      - name: ugc-keep-user-offline-push
        service_module: ugc-keep-user-offline-push
        file_tree:
          - services/ugc/keep-user-offline-push
      - name: sync-ulib
        service_module: sync-ulib
        file_tree:
          - services/ugc/recommendation-ulib/cmd
      - name: ugc-following-stream
        service_module: ugc-following-stream
        file_tree:
          - services/ugc/following-stream
      - name: ugc-mission-handler
        service_module: ugc-mission-handler
        file_tree:
          - services/ugc/mission-handler
      - name: script
        service_module: script
        file_tree:
          - services/ugc/script
      - name: audio-post
        service_module: audio-post
        file_tree:
          - services/ugc/audio-post
      - name: ugc-highcontent
        service_module: ugc-highcontent
        file_tree:
          - services/ugc/highcontent
      - name: ugc-highcontent-proxy
        service_module: ugc-highcontent-proxy
        file_tree:
          - services/ugc/highcontent-proxy
      - name: ugc-content-view-rewriter
        service_module: ugc-content-view-rewriter
        file_tree:
          - services/ugc/content-view-rewriter
      - name: ugc-rcmd-post
        service_module: ugc-rcmd-post
        file_tree:
          - services/ugc/rcmd-post-server
      - name: ugc-geo-topic
        service_module: ugc-geo-topic
        file_tree:
          - services/ugc/geo-topic-server
      - name: ugc-geo-topic-feed-subscriber
        service_module: ugc-geo-topic-feed-subscriber
        file_tree:
          - services/ugc/geo-topic-feed-subscriber
      - name: ugc-text-check-review-subscriber
        service_module: ugc-text-check-review-subscriber
        file_tree:
          - services/ugc/text-check-review-subscriber
      - name: ugc-audit-result-subscriber
        service_module: ugc-audit-result-subscriber
        file_tree:
          - services/ugc/audit-result-subscriber
      - name: ugc-ope-config
        service_module: ugc-ope-config
        file_tree:
          - services/ugc/ope-config
      - name: topic-channel-logic
        service_module: topic-channel-logic
        file_tree:
          - services/topic-channel-logic
      - name: topic-channel-logic-v2
        service_module: topic-channel-logic-v2
        file_tree:
          - services/topic-channel-logic-v2
      - name: topic-channel-tab
        service_module: topic-channel-tab
        file_tree:
          - services/topic-channel/tab-server
      - name: topic-channel-recommendation
        service_module: topic-channel-recommendation
        file_tree:
          - services/topic-channel/recommendation-server
      - name: topic-channel
        service_module: topic-channel
        file_tree:
          - services/topic-channel/channel-server
        test_src: clients/topic-channel/channel
      - name: topic-channel-subscriber
        service_module: topic-channel-subscriber
        file_tree:
          - services/topic-channel/topic-channel-subscriber
      - name: antispam-logic-subscriber
        service_module: antispam-logic-subscriber
        file_tree:
          - services/topic-channel/antispam-logic-subscriber
      - name: topic-channel-recommendation-gen
        service_module: topic-channel-recommendation-gen
        file_tree:
          - services/topic-channel/recommendation-gen-server
      - name: topic-channel-recommendation-subscriber
        service_module: topic-channel-recommendation-subscriber
        file_tree:
          - services/topic-channel/recommendation-subscriber
      - name: topic-channel-recommendation-statistics
        service_module: topic-channel-recommendation-statistics
        file_tree:
          - services/topic-channel/recommendation-statistics-server
      - name: topic-channel-recommendation-mini-game-gen
        service_module: topic-channel-recommendation-mini-game-gen
        file_tree:
          - services/topic-channel/recommendation-mini-game-gen-server
      - name: cybros-subscriber
        service_module: cybros-subscriber
        file_tree:
          - services/cybros-subscriber
      - name: zego-callback
        service_module: zego-callback
        file_tree:
          - services/zego/callback
      - name: zego-access-token-refresher
        service_module: zego-access-token-refresher
        file_tree:
          - services/zego/refresh-access-token
      - name: zego-identify-token-refresher
        service_module: zego-identify-token-refresher
        file_tree:
          - services/zego/refresh-identify-token
      - name: auth-event-tool
        service_module: auth-event-tool
        file_tree:
          - cmd/recall/cmd/auth_event_tool
      - name: wechat-refresh-access-token
        service_module: wechat-refresh-access-token
        file_tree:
          - services/wechat-token/refresh-access-token
      - name: wechat-refresh-api-ticket
        service_module: wechat-refresh-api-ticket
        file_tree:
          - services/wechat-token/refresh-api-ticket
      - name: audit-api
        service_module: audit-api
        file_tree:
          - services/audit/audit-api
      - name: audit-post
        service_module: audit-post
        file_tree:
          - services/audit/audit-post
      - name: audit-post-subscriber
        service_module: audit-post-subscriber
        file_tree:
          - services/audit/audit-post-subscriber
      - name: audit-comment-subscriber
        service_module: audit-comment-subscriber
        file_tree:
          - services/audit/audit-comment-subscriber
      - name: audit-channel-subscriber
        service_module: audit-channel-subscriber
        file_tree:
          - services/audit/audit-channel-subscriber
      - name: games-mgr
        service_module: games-mgr
        file_tree:
          - services/games-mgr
      - name: games-config
        service_module: games-config
        file_tree:
          - services/games-config
      - name: gameserver
        service_module: gameserver
        file_tree:
          - services/gameserver
      - name: games-oauth
        service_module: games-oauth
        file_tree:
          - services/games-oauth
      - name: molebeat
        service_module: molebeat
        file_tree:
          - services/molebeat
      - name: molebeat-logic
        service_module: molebeat-logic
        file_tree:
          - services/molebeat-logic
      - name: mole-beat-rank-list-logic
        service_module: mole-beat-rank-list-logic
        file_tree:
          - services/mole-beat-rank-list-logic
      - name: channel-live-logic
        service_module: channel-live-logic
        file_tree:
          - services/channel-live-logic
      - name: channel-live-mission
        service_module: channel-live-mission
        file_tree:
          - services/channel-live-mission
      - name: channel-live-mgr
        service_module: channel-live-mgr
        file_tree:
          - services/channel-live-mgr
      - name: channel-live-fans
        service_module: channel-live-fans
        file_tree:
          - services/channel-live-fans
      - name: channel-live-ranking
        service_module: channel-live-ranking
        file_tree:
          - services/channel-live-ranking
      - name: channel-live-stats
        service_module: channel-live-stats
        file_tree:
          - services/channel-live-stats
      - name: channel-live-push
        service_module: channel-live-push
        file_tree:
          - services/channel-live-push
      - name: playmate-rcmd-playmate
        service_module: playmate-rcmd-playmate
        file_tree:
          - services/playmate/rcmd-playmate-server
      - name: abtest
        service_module: abtest
        file_tree:
          - services/abtest
      - name: abtest-logic
        service_module: abtest-logic
        file_tree:
          - services/abtest-logic
      - name: sakura
        service_module: sakura
        file_tree:
          - services/bots/sakura
      - name: monkey-robot
        service_module: monkey-robot
        file_tree:
          - services/bots/monkey
        test_src: clients/smash-egg
      - name: tsubasa
        service_module: tsubasa
        file_tree:
          - services/bots/tsubasa
      - name: interaction-intimacy
        service_module: interaction-intimacy
        file_tree:
          - services/interaction/interaction-intimacy
      - name: interaction-intimacy-logic
        service_module: interaction-intimacy-logic
        file_tree:
          - services/interaction-logic/interaction-intimacy-logic
      - name: interaction-intimacy-recommend
        service_module: interaction-intimacy-recommend
        file_tree:
          - services/interaction/interaction-intimacy-recommend
      - name: fellow-svr
        service_module: fellow-svr
        file_tree:
          - services/interaction/fellow-svr
      - name: fellow-logic
        service_module: fellow-logic
        file_tree:
          - services/interaction/fellow-logic
      - name: newbiesvr
        service_module: newbiesvr
        file_tree:
          - services/newbiesvr
      - name: hunt-monster
        service_module: hunt-monster
        file_tree:
          - services/hunt-monster
      - name: hunt-monster-logic
        service_module: hunt-monster-logic
        file_tree:
          - services/hunt-monster-logic
      - name: hunt-monster-mission
        service_module: hunt-monster-mission
        file_tree:
          - services/hunt-monster-mission
      - name: backpack-sender
        service_module: backpack-sender
        file_tree:
          - services/risk-control/backpack-sender
      - name: award-center
        service_module: award-center
        file_tree:
          - services/risk-control/award-center
      - name: channel-deeplink-recommend
        service_module: channel-deeplink-recommend
        file_tree:
          - services/channel-deeplink-recommend
      - name: channel-deeplink-recommend-logic
        service_module: channel-deeplink-recommend-logic
        file_tree:
          - services/channel-deeplink-recommend-logic
      - name: present-extra-conf
        service_module: present-extra-conf
        file_tree:
          - services/present-extra-conf
      - name: user-checkin
        service_module: user-checkin
        file_tree:
          - services/user/user-checkin
      - name: user-checkin-logic
        service_module: user-checkin-logic
        file_tree:
          - services/user/user-checkin-logic
      - name: chat-card
        service_module: chat-card
        file_tree:
          - services/user/chat-card
      - name: chat-card-logic
        service_module: chat-card-logic
        file_tree:
          - services/user/chat-card-logic
      - name: cpl-search
        service_module: cpl-search
        file_tree:
          - services/user/cpl-search
      - name: user-logic
        service_module: user-logic
        file_tree:
          - services/user/user-logic
      - name: slip-note
        service_module: slip-note
        file_tree:
          - services/user/slip-note
      - name: slip-note-logic
        service_module: slip-note-logic
        file_tree:
          - services/user/slip-note-logic
      - name: user-httplogic
        service_module: user-httplogic
        file_tree:
          - services/user/user-httplogic
      - name: dark-checker
        service_module: dark-checker
        file_tree:
          - services/user/dark-checker
      - name: darkserver
        service_module: darkserver
        file_tree:
          - services/user/darkserver
      - name: oauth2
        service_module: oauth2
        file_tree:
          - services/oauth2
      - name: oauth2-logic
        service_module: oauth2-logic
        file_tree:
          - services/oauth2-logic
      - name: oauth2-http
        service_module: oauth2-http
        file_tree:
          - services/oauth2-http
      - name: super-channel-logic
        service_module: super-channel-logic
        file_tree:
          - services/super-channel-logic
      - name: super-channel
        service_module: super-channel
        file_tree:
          - services/super-channel
      - name: anchorcontract-go
        service_module: anchorcontract-go
        file_tree:
          - services/anchor-contract/anchorcontract-go
      - name: sign-anchor-stats
        service_module: sign-anchor-stats
        file_tree:
          - services/anchor-contract/sign-anchor-stats
      - name: super-player-svr
        service_module: super-player-svr
        file_tree:
          - services/super-player/super-player-svr
      - name: super-player-logic
        service_module: super-player-logic
        file_tree:
          - services/super-player/super-player-logic
      - name: super-player-mission
        service_module: super-player-mission
        file_tree:
          - services/super-player/super-player-mission
      - name: super-player-privilege
        service_module: super-player-privilege
        file_tree:
          - services/super-player/super-player-privilege
      - name: super-player-http-logic
        service_module: super-player-http-logic
        file_tree:
          - services/super-player/super-player-http-logic
      - name: super-player-dress-logic
        service_module: super-player-dress-logic
        file_tree:
          - services/super-player/super-player-dress-logic
      - name: super-player-dress
        service_module: super-player-dress
        file_tree:
          - services/super-player/super-player-dress
      - name: gnobility
        service_module: gnobility
        file_tree:
          - services/gnobility
      - name: gnobility-logic
        service_module: gnobility-logic
        file_tree:
          - services/gnobility-logic
      - name: masked-pk-svr
        service_module: masked-pk-svr
        file_tree:
          - services/masked-pk/masked-pk-svr
      - name: masked-pk-logic
        service_module: masked-pk-logic
        file_tree:
          - services/masked-pk/masked-pk-logic
      - name: masked-pk-score
        service_module: masked-pk-score
        file_tree:
          - services/masked-pk/masked-pk-score
      - name: masked-pk-live
        service_module: masked-pk-live
        file_tree:
          - services/masked-pk/masked-pk-live
      - name: obs-gateway
        service_module: obs-gateway
        file_tree:
          - services/obs/obs-gateway
      - name: obs-subscriber
        service_module: obs-subscriber
        file_tree:
          - services/obs/obs-subscriber
      - name: obs-http-logic
        service_module: obs-http-logic
        file_tree:
          - services/obs/obs-http-logic
      - name: obs-cdn-gateway
        service_module: obs-cdn-gateway
        file_tree:
          - services/obs/obs-cdn-gateway
      - name: obs-cdn-http-logic
        service_module: obs-cdn-http-logic
        file_tree:
          - services/obs/obs-cdn-http-logic
      - name: obs-object-keeper
        service_module: obs-object-keeper
        file_tree:
          - services/obs/obs-object-keeper
      - name: obs-delay-subscriber
        service_module: obs-delay-subscriber
        file_tree:
          - services/obs/obs-delay-subscriber
      - name: channel-cp-game
        service_module: channel-cp-game
        file_tree:
          - services/channel-cp-game
      - name: channel-cp-game-logic
        service_module: channel-cp-game-logic
        file_tree:
          - services/channel-cp-game-logic
      - name: game-server-v2
        service_module: game-server-v2
        file_tree:
          - services/game-server-v2
      - name: game-server-logic-v2
        service_module: game-server-logic-v2
        file_tree:
          - services/game-server-logic-v2
      - name: channel-relationship
        service_module: channel-relationship
        file_tree:
          - services/relationship/channel-relationship
      - name: relationship-push
        service_module: relationship-push
        file_tree:
          - services/relationship/relationship-push
      - name: user-relationship
        service_module: user-relationship
        file_tree:
          - services/relationship/user-relationship
      - name: channel-red-packet
        service_module: channel-red-packet
        file_tree:
          - services/channel-red-packet/channel-red-packet
      - name: channel-red-packet-logic
        service_module: channel-red-packet-logic
        file_tree:
          - services/channel-red-packet/channel-red-packet-logic
cache_set:
  ignore_cache: false
  reset_cache: false
