syntax = "proto2";

package ga.channel;

import "ga_base.proto";

option java_package = "com.yiyou.ga.model.proto";
option go_package = "golang.52tt.com/protocol/app/channel";

//-------------语音频道---------------------//
enum ChannelMsgType {

    // 纯文本 (消息的pb_opt_content字段中会携带 channel_opt_.proto 中定义的 ChannelImOpt 信息)
    CHANNEL_TEXT_MSG = 1;

    // 加入房间 消息的pb_opt_content字段中会携带 channel_opt_.proto 中定义的 ChannelEnterOpt 信息
    CHANNEL_ENTER_MSG = 2;

    // 退出房间 消息的pb_opt_content字段中会携带 channel_opt_.proto 中定义的 ChannelExitOpt 信息
    CHANNEL_EXIT_MSG = 3;

    // 上麦 (消息的pb_opt_content字段中会携带channel_opt_.proto 中定义的 ChannelMicOpt 信息)
    EN_MIC_QUEUE = 4;

    // 下麦 (消息的pb_opt_content字段中会携带channel_opt_.proto 中定义的 ChannelMicOpt 信息)
    DE_MIC_QUEUE = 5;

    CHANNEL_AIR_TICKET = 6; // 频道飞机票

    // 图片消息 （消息的pb_opt_content字段中会携带 channel_opt_.proto 中定义的 ChannelImOpt 信息)
    CHANNEL_IMAGE_MSG = 7;
    CHANNEL_EMOTICON_MSG = 8; // 表情消息

    // html文本 （消息的pb_opt_content字段中会携带channel_opt_.proto 中定义的 ChannelImOpt 信息)
    CHANNEL_HTML_MSG = 9;

    // 被踢出频道
    //如果push没收到，一般是客户端断开连接了，重连之后马上请求一次GetChannelMsgReq，如果服务器发现客户端不在频道了会通过错误码告诉客户端
    CHANNEL_KICKED = 10;

    // 被踢下麦位 （消息的pb_opt_content字段中会携带channel_opt_.proto 中定义的 ChannelMicOpt 信息)
    CHANNEL_KICKED_MIC = 11;

    CHANNEL_DISMISS = 12; // 频道被解散

    // 麦位设置为禁用状态 （消息的pb_opt_content字段中会携带channel_opt_.proto 中定义的 ChannelMicOpt 信息)
    CHANNEL_MIC_ENTRY_DISABLE = 13;

    // 麦位设置为正常状态 （消息的pb_opt_content字段中会携带 channel_opt_.proto 中定义的 ChannelMicOpt 信息)
    CHANNEL_MIC_ENTRY_ENABLE = 14;

    // 房间配置信息有变化 包括 房间名称 密码 麦模式 房间图标 房间描述等配置
    // 具体内容参见 ChannelMsgSubType 此时ChannelMsg的content字段为JSON格式
    CHANNEL_CONFIG_MODIFY_MSG = 15;

    CHANNEL_MUSIC_INFO = 16; // 房间音乐信息 此时ChannelMsg的content字段为JSON格式
    CHANNEL_FREE_MIC_MODE_MUTE_USER = 17; // 房间自由模式时 禁止某个用户发言
    CHANNEL_SEND_PRESENT = 18;            // 赠送礼物

    CHANNEL_TEXT_SYS_MSG = 19; // 纯文本系统消息

    CHANNEL_MUSIC_LIST_CHANGED = 20;    // 音乐列表变化
    CHANNEL_MUSIC_VOLUME_CHANGED = 21;  // 音量变化
    CHANNEL_MUSIC_USER_PLAY_MUSIC = 22; // 指定用户播某个歌曲

    CHANNEL_FUN_GAME_RESULT_MSG = 23; //房间小游戏消息

    CHANNEL_MUSIC_CAN_SHARE_CHANGED = 24; // 房间音乐可贡献开关变化

    CHANNEL_PRESENT_MSG = 25;     // 房间礼物消息

    // 麦位设置为闭麦状态（闭麦状态为可以上麦但是不能说话）
    // att_content 字段为全量麦位json字段
    CHANNEL_MIC_ENTRY_MUTED = 26;

    // 房间内"在线成员的排序值"有变化
    CHANNEL_MEMBER_LIST_SORT_CHANGED = 27;

    CHANNEL_ADMIN_MEMBER_CHANGED = 28; // 个人房房间管理员有变化  可能是增加或者移除
    CHANNEL_MAGIC_EXPRESSION_MSG = 29; // 房间麦上魔法表情消息

    // 用户换了个麦位 (消息的pb_opt_content字段中会携带 channel_opt_.proto中定义的 ChannelChangeMicPosOpt 信息)
    CHANNEL_CHANGE_MIC_POS = 30;

    // 让指定用户持有指定麦 (消息的pb_opt_content字段中会携带channel_opt_.proto 中定义的 ChannelMicOpt 信息)
    CHANNEL_TAKE_USER_HOLD_MIC = 31;

    CHANNEL_MUSIC_SOMEONE_PLAY_MUSIC = 32; // 同22功能,区别是广播消息

    CHANNEL_MUSIC_FREE_MODE_CHANGED = 33; // 自由模式变化

    // 用户信息变化 一般是土豪魅力等级升级 消息的opt_content字段中会携带 channel_opt_.proto 中定义的 ChannelMemberOpt 信息
    CHANNEL_MEMBER_OPT_INFO_CHANGED = 34;

    CHANNEL_WARNING = 35; // 房间警告信息 警告的内容字符串放在content字段

    // 用户头饰变化 （消息的pb_opt_content字段中会携带 channel_opt_.proto中定义的 ChannelUserHeadwearOpt 信息）
    CHANNEL_USER_HEADWEAR_CHANGED = 36;

    // 房间知识问答游戏定时器动画 （消息的pb_opt_content字段中会携带channel_opt_.proto 中定义的ChannelTriviaGameTimerOpt信息）
    CHANNEL_TRIVIA_GAME_TIMER = 37;

    // 房间知识问答游戏的动画 （消息的pb_opt_content字段中会携带channel_opt_.proto 中定义的ChannelTriviaGameAnimationOpt信息)
    CHANNEL_TRIVIA_GAME_ANIMATION = 38;

    // 房间知识问答游戏 的 出题 （消息的pb_opt_content字段中会携带channel_opt_.proto 中定义的ChannelTriviaGameQuestionOpt 信息）
    CHANNEL_TRIVIA_GAME_QUESTION = 39;

    // 房间知识问答游戏 的 答案 （消息的pb_opt_content字段中会携带channel_opt_.proto 中定义的ChannelTriviaGameWinnerListOpt信息）
    CHANNEL_TRIVIA_GAME_WINNER = 40;

    // 房间知识问答游戏 推送给主持人的阶段变化（消息的pb_opt_content字段中会携带 channel_opt_.proto中定义的ChannelTriviaGamePhaseUpdateOpt信息）
    CHANNEL_TRIVIA_GAME_PHASE_FORWARD = 41;

    CHANNEL_TRIVIA_GAME_END = 42; // 房间知识问答游戏 通知客户端踢用户出房间

    // 游客发起连麦请求（包括自己申请连麦和自己取消连麦）
    //（消息的pb_opt_content字段中会携带 channel_opt_.proto ChannelLiveConnectMicApplyOpt 信息 ）
    CHANNEL_CONNECT_MIC_APPLY_MSG = 43;

    // 主播对连麦请求回应（包括同意连麦和拒绝连麦）（消息的pb_opt_content字段中会携带
    // channel_opt_.proto 中定义的 ChannelLiveConnectMicHandleOpt 信息 ）
    CHANNEL_CONNECT_MIC_HANDLE_MSG = 44;

    CHANNEL_CONNECT_MIC_APPLY_EXPIRE_MSG = 45; // 系统对房间内连麦请求进行超时处理（消息的pb_opt_content字段中会携带
          // channel_opt_.proto 中定义的 ChannelLiveConnectMicApplyExpireOpt信息 ）

    CHANNEL_GUILD_BULLETIN_CREATE_MSG =
            46; //   当公会有新公告发布时 公会主房间会收到一条推送
          //   （消息的pb_opt_content字段中会携带 channel_opt_.proto 中定义的
          //   ChannelGuildBulletinOpt 信息 ）

    CHANNEL_GAME_RECRUIT_PUBLISH =
            47; //  推送游戏招募消息（消息的 pb_opt_content 字段中会携带 team_.proto
          //  中定义的 GameRecruitDetail 信息 ）

    CHANNEL_LIVE_FIN_MSG =
            48; //  表明上一轮直播结束 仅在直播麦位模式下 才有可能发送
    CHANNEL_GUILD_MEMBER_JOIN_MSG =
            49; //  当公会有新人加入的时候 公会主房间会收到一条推送

    CHANNEL_DATING_GAME_HAT =
            50; // 相亲房 用户获得帽子 消息的 pb_opt_content 字段中会携带
          // channel_dating_game_.proto 中定义的 GetDatingGameHatUser 信息 ）
    CHANNEL_DATING_GAME_VIP_HOLD_MIC = 51; // 相亲房 VIP麦位上的用户更新
    CHANNEL_DATING_GAME_LIKE_BEAT_VAL =
            52; // 相亲房 麦位上用户心动值更新  pb_opt_content 字段中会携带
          // channel_dating_game_.proto 中定义的 ga::LikeBeatValChangeMsg
    CHANNEL_DATING_GAME_SELECT_STATUS =
            53; // 相亲房 选择状态更新，pb_opt_content 字段中会携带
          // channel_dating_game_.proto 中定义的 ga::DatingSelectStatusMsg
    CHANNEL_DATING_GAME_OPEN_LIKE_USER =
            54; // 相亲房 公布心动对象，pb_opt_content 字段中会携带
          // channel_dating_game_.proto 中定义的 ga::OpenDatingLikeUserMsg
    CHANNEL_DATING_GAME_APPLY_MIC =
            55; // 相亲房 连麦位申请  pb_opt_content 字段中会携带
          // channel_dating_game_.proto 中定义的 ga::ApplyDatingMicMsg
    CHANNEL_DATING_GAME_VIP_MSG =
            56; // 相亲房的可上vip麦位通知  pb_opt_content 字段中会携带
          // channel_dating_game_.proto DatingGameVipMsg
    CHANNEL_DATING_SHOWOFF =
            57; // 公布结果后的，  pb_opt_content 字段中会携带 动画效果推送协议定义在
          // channel_dating_game_.proto DatingGameShowoff

    // 相亲活动 阶段变化
    // pb_opt_content 协议定义在 channel_dating_game_.proto DatingGamePhasePushNotifyOpt
    CHANNEL_DATING_PHASE_CHANGE = 58;

    // 房间送礼统计 推送新的统计值
    //（麦上用户收到礼物时推送，以及刚上麦的用户会推送）
    // 推送协议定义在 channel_opt_.proto ChannelPresentCountNotifyMsg
    CHANNEL_PRESENT_COUNT_CHANGE = 59;

    // 房间送礼统计开关，开或关
    // pb_opt_content 协议定义在 channel_opt_.proto ChannelPresentCountStatusMsg
    CHANNEL_PRESENT_COUNT_STATUS_CHANGE = 60;

    // 房间成员VIP等级提醒
    // 推送协议定义在 channel_opt_.proto ChannelMemberVipNotifyOpt
    CHANNEL_MEMBER_VIP_LEVEL_NOTIFY = 61;

    CHANNEL_BATCH_SEND_PRESENT_NOTIFY =
            62; // 批量赠送礼物附加信息 消息的  pb_opt_content
          // 字段中会携带userpresent_.proto中定义的PresentBatchInfoMsg

    CHANNEL_MEMBER_VIP_LEVEL_UPGRADE =
            63; // 房间成员VIP等级升级 推送协议定义在 channel_opt_.proto
          // ChannelMemberVipNotifyOpt

    CHANNEL_PRESENT_RUNWAY_ROCKET_CHANGE = 64; // 房间礼物跑道的火箭变化
    CHANNEL_PRESENT_RUNWAY_ROCKET_MSG = 65; // 房间礼物跑道的火箭跑道公屏消息

    CHANNEL_POP_UP_MSG =
            66; // 房间弹窗消息 推送协议定义在 channel_opt_.proto  ChannelPopUpMsgOpt

    CHANNEL_2019NEWYEAR_BEAT_AWARD = 67; // 房间打地鼠游戏 发奖    推送协议定义在
                                       // activity_.proto NewYear2019BeatAward
    CHANNEL_2019NEWYEAR_BEAT_UNAWARD = 68; // 房间打地鼠游戏 未中奖 推送协议定义在
                                         // activity_.proto NewYear2019BeatAward

    //房间PK，排名有变化（包括开始 结束 和 有变化）
    //推送协议 ChannelVotePKPushNotifyInfo
    CHANNEL_PK_CHANGED = 69;

    //房间画布推送（添加线，取消线，删除画 推送协议）
    //ChannelDrawGamePushNotifyInfo
    CHANNEL_DRAW_GAME = 70;

    //普通房间的排麦变化事件（消息的pb_opt_content字段中会携带
    //channel_opt_.proto 中定义的 ChannelQueueUpMicOpt 信息 ）
    CHANNEL_NORMAL_QUEUE_UP_MIC = 71;
    CHANNEL_COLOR_NOTIDY_MSG = 72; //房间有颜色配置公屏消息

    //创建主题房时公屏消息 推送协议定义在 channel_opt_.proto 中定义的
    //CreateTopicChannel
    CHANNEL_CREATE_TOPIC_CHANNEL = 73;

    CHANNEL_MODIFY_MSG = 74; //更改客户端的房间消息

    //主题房不在大厅展示了的公屏消息，推送协议定义在
    //channel_opt_.proto 中定义的 HideTopicChannelMsg
    TOPIC_CHANNEL_HIDDEN = 75;

    //处理敲门返回给公屏的结果信息 knocklogic_.proto 中定义的 ScreenPush
    KNOCK_CHANNEL_RESULT = 76;

    CHANNEL_DO_MUTE = 77;   // 禁言
    CHANNEL_UNDO_MUTE = 78; // 解除禁言

    // 当前房间背景变化  推送协议见 channelbackgroundlogic.proto 中的ChannelBackgroundInfo
    CHANNEL_CURRENT_BACKGROUND_CHANGE = 79;

    // 监管协议 （踢出房间内所有人 客户端收到该协议需要退出房间）
    CHANNEL_KICKED_ALL_MEMBER = 80;
    CHANNEL_CLEAN_SCREEN = 81; // 监管协议 （清理公屏所有内容）

    // 游戏加载
    // 消息的optcontent字段中会携带channel-open-game_.proto中定义的ChannelGameInfo
    CHANNEL_OPEN_GAME_LOAD = 82;

    // 游戏信息变化
    // 消息的optcontent字段中会携带channel-open-game_.proto中定义的 ChannelGameInfo
    CHANNEL_OPEN_GAME_CHANGE = 83;

    // 游戏完成
    // 消息的optcontent字段中会携带channel-open-game_.proto中定义的ChannelGameResult
    CHANNEL_OPEN_GAME_COMPLETE = 84;

    // 游戏玩法房麦上用户游戏标签段位变化推送协议,协议在 channel_opt_.proto ChannelPlayMicroGameTagChangeOpt
    CHANNEL_PLAY_USER_GAMETAG_CHANGE = 85;

    // 游戏玩法房,用户进房,游戏标签卡片推送协议,协议在 channel_opt_.proto ChannelPlayMicroGameTagOpt
    CHANNEL_PLAY_PUSH_GAMETAG = 86;

    CHANNEL_SWITCH_PLAY_GAME = 87;  // 玩法切换，推送消息给房主
    CHANNEL_TRUMPET_MSG = 88;       //房间小喇叭消息类型

    //贵族信息相关，用于通知客户端房间里面的玩家贵族系统变化
    //channel_opt_.proto NobilityEventMsg
    CHANNEL_NOBILITY_INFO_MSG = 90;

    // 房间送礼弹窗 可扩展类型 协议在 channel_opt_.proto ChannelPresentActPopUpOpt
    CHANNEL_PRESENT_POPUP_ACT_MSG = 89;

    NOBILITY_CHANNEL_MSG = 91; //贵族等级变化房间推送
    CHANNEL_HOUR_RANK_LIST = 92; //房间小时榜 协议在 channel_opt_.proto ChannelHourRankListMsg

    // 房间游戏流程控制器，游戏信息变化
    // 消息的optcontent字段中会携带
    // channel-open-game-controller-logic_.proto中定义的 ChannelGameInfoStatus
    CHANNEL_OPEN_GAME_CONTROLLER_CHANGE = 93;

    // 游戏维护
    // 消息的optcontent字段中会携带
    // channel-open-game_.proto中定义的 ChannelGameMaintain
    CHANNEL_OPEN_GAME_MAINTAIN = 94;

    CHANNEL_LIVE_STATUS_MSG = 95; //语音直播房状态推送消息
    CHANNEL_LIVE_PK_APPLY_MSG = 96; // 废弃, 语音直播房PK申请状态推送消息 PkApplyPushMsg (改为使用push_.CHANNEL_LIVE_PK_APPLY_PUSH)
    CHANNEL_LIVE_PK_SCORE_MSG = 97; //语音直播房PK分数推送消息 ChannelPkScorePushMsg
    CHANNEL_LIVE_PK_TOOL_EFFECT_MSG = 98; //语音直播房PK道具效果 SendGiftEffectPushMsg
    CHANNEL_LIVE_PK_GET_TOOL_MSG = 99; //语音直播房获取PK道具效果 GetGiftPushMsg

    // 由于ios本地使用了 100 - 108 枚举值， 所以只能从 109 开始定义

    CHANNEL_LIVE_ACTOR_MISSION_FINISH_MSG = 109; // 语音直播房主播任务完成时的公屏消息
    CHANNEL_LIVE_USER_MISSION_AWARD_MSG = 110; // 语音直播房用户任务的奖励弹窗 MissionAwardOptMsg
    CHANNEL_LIVE_FANS_MSG = 111;   //语音直播房粉丝团推送信息 消息的optcontent字段中会携带channel-live-logic_.proto中定义的 FansNotifyOpt
    CHANNEL_LIVE_FOLLOW_MSG = 112;  // 语音直播房内关注主播的推送信息

    // 蒙面通话状态变更推送消息
    // 消息的optcontent字段中会携带masked-call_.proto中定义的Call
    MASKED_CALL_INFO_CHANGE = 113;

    // 蒙面通话骰子结果推送消息
    // 消息的optcontent字段中会携带masked-call_.proto中定义的RollResultMsg
    MASKED_CALL_ROLL_RESULT = 114;

    //互动推送消息
    //消息的optcontent字段中会携带masked-call_.proto中定义的InteractMsg
    MASKED_CALL_INTERACT_MSG = 115;

    CHANNEL_LIVE_SHARE_MSG = 116;   // 语音直播分享分享直播间的推送消息

    // 语音直播排行榜入口推送
    // ChannelBroadcastMsg消息的pb_opt_content字段中会携带channel-live-logic_.proto中定义的LiveRankingEnterOptMsg
    CHANNEL_LIVE_RANKING_ENTER_MSG = 117;

    // 语音直播主播荣誉铭牌变化
    // ChannelBroadcastMsg消息的pb_opt_content字段中会携带channel-live-logic_.proto中定义的HonorNameplate
    CHANNEL_LIVE_HONOR_NAMEPLATE_CHANGE_MSG = 118;

    CHANNEL_LIVE_OPT_MIC_FLAG_MSG = 119; //PK直播中，主播设置对面房间声音

    CHANNEL_LIVE_PK_STATUS_MGS = 121; //PK阶段push ChannelLivePkStatusPushMsg
    CHANNEL_LIVE_DATA_MSG = 122;     //直播分值变化，ChannelLiveDataPushMsg
    CHANNEL_LIVE_MIC_LIST_MSG = 123;     //PK麦位和语音流变化推送 PkMicInfoPushMsg
    CHANNEL_LIVE_PK_TARGET_CHANNEL_STATUS_MSG = 124;     //PK中对手房间的直播状态，暂停，继续等

    CHANNEL_PERFORMANCE_CHANGE_MSG = 125;         // 房间节目单变更，消息的optcontent字段中会携带channel-performance_.proto中定义
    CHANNEL_PERFORMANCE_STAGE_CHANGE_MSG = 126;   // 房间当前节目变更，消息的optcontent字段中会携带channel-performance_.proto中定义

    CHANNEL_LIVE_RANKING_LIST_MSG = 127;   // 语音直播房榜单消息，消息的optcontent字段中会携带push_.proto中定义的LiveRoomRankingPushMsg
    CHANNEL_HUNT_MONSTER_MSG = 128; //打boss单个房间推送相关,比如当前房间boss生存值变化，boss逃跑等
    CHANNEL_INTIMACY_FINISH_MISSION = 130; // 房间内用户完成任务消息 推送协议见im.proto的RichTextMsg
    CHANNEL_HUNT_MONSTER_MSG_V2 = 131; //打boss单个房间推送相关,比如当前房间boss生存值变化，boss逃跑等

    CHANNEL_TEAM_CTRL_MSG = 132; // 房间小队操作消息 channel-team-logic_.proto -> CtrlMsg
    CHANNEL_TEAM_APPLY = 133; // 房间小队申请 channel-team-logic_.proto -> ApplyMember
    CHANNEL_TEAM_MEMBER_UPDATE = 134; // 房间小队成员信息更新 channel-team-logic_.proto -> ChannelTeamInfo

    CHANNEL_LEVEL_INFO_CHANGE = 135; // 房间等级变更，消息的optcontent字段中会携带channel-level_.proto中定义的ChannelLevelChangeMsg

    LIVE_CHANNEL_RELAY_CHANGE = 136; // 排班直播间转播变更，消息的optcontent字段中会携带official-live-channel_.proto中定义的LiveChannelRelayChangeMsg
    OFFICIAL_LIVE_CHANNEL_RELAY_CHANGE = 137; // 官频直播间转播变更，消息的optcontent字段中会携带official-live-channel_.proto中定义的OfficialLiveChannelRelayChangeMsg
    OFFICIAL_LIVE_CHANNEL_MODIFY = 138; // 官频直播间属性变更，消息的optcontent字段中会携带official-live-channel_.proto中定义的OfficialLiveChannelModifyMsg

    CHANNEL_LOTTERY_BEGIN = 139; // 开通抽奖push
    CHANNEL_LOTTERY_RESULT = 140;// 抽奖结果push

    LIVE_CHANNEL_MIC_CHANGE = 141; // 排班直播间麦位变更，消息的optcontent字段中会携带official-live-channel_.proto中定义的LiveChannelMicChangeMsg
    OFFICIAL_LIVE_CHANNEL_OTHER_CHANNEL_ADMIN_CHANGE = 142; // 官频/排班直播间管理员变更，消息的optcontent字段中会携带official-live-channel_.proto中定义的OfficialLiveChannelOtherChannelAdminChangeMsg

    OFFICIAL_LIVE_CHANNEL_RELAY_SECTION_CHANGE = 143; //官频播放节目变更

    OFFICIAL_FANS_BATCH_SEND_PRESENT = 144;  // 官频抽奖推送，消息的optcontent字段会携带userpresent_.proto中的FansPresentMessage字段

    CHANNEL_GAME_NICK = 145;  // 房间发送游戏昵称,消息的optcontent字段会携带ga_base.proto的ChannelGameNickOpt

    CHANNEL_LOTTERY_CANCEL = 146;// 抽奖取消push

    CHANNEL_LIVE_COMMON_MSG = 150; //语音直播房通用推送，见 channel-live-logic_.proto message ChannelLivePkCommonPushMsg

    SUPER_CHANNEL_MIC_CHANGE = 151; // 大房麦位变更事件

    CHANNEL_DRESS_CHANGE = 152; // 房主切换会员套装  推送协议定义在 channel_opt_.proto  ChannelDressChangeNotifyMsg

    SUPER_CHANNEL_MEMBER_TOPN = 153;  //大房前3成员变更事件

    SUPER_CHANNEL_HOLD_MIC_INVITE = 154; // 活动大房上麦邀请
    SUPER_CHANNEL_INFO_CHANGE = 155; // 活动大房信息变更

    CHANNEL_MIC_MODE_CHANGE = 156;  // 房间麦位模式变化 [2021-6-18 5.5.11新增] 推送协议定义在 channel_opt_.proto ChannelMicModeOpt

    CHANNEL_MEDIA_CHANGE = 157;  //房间多媒体信息变化 现在只有大房直播流链接 消息的optcontent字段中会携带unclaimed_.proto中定义的ChannelMediaChangeMsg

    ENTERTAINMENT_MASKED_PK_STATUS_CHANGE = 158;  // 娱乐厅蒙面pk状态变更 see masked-pk-logic_.proto ChannelMaskedPKStatus
    ENTERTAINMENT_MASKED_PK_GAME_BEGIN = 159;     // 娱乐厅蒙面pk活动开始 see masked-pk-logic_.proto ChannelMaskedPKConf
    ENTERTAINMENT_MASKED_PK_BATTLE = 160;         // 娱乐厅蒙面pk PK战况 see masked-pk-logic_.proto ChannelMaskedPKBattle
    ENTERTAINMENT_MASKED_PK_SETTLE = 161;         // 娱乐厅蒙面pk单场结算 see masked-pk-logic_.proto ChannelMaskedPKResult
    ENTERTAINMENT_MASKED_PK_REVIVE = 162;         // 娱乐厅蒙面pk单场复活阶段进度 see masked-pk-logic_.proto ChannelMaskedPKRevive
    ENTERTAINMENT_MASKED_PK_REVIVE_RET = 163;     // 娱乐厅蒙面pk单场复活结果 see masked-pk-logic_.proto ChannelMaskedPKReviveRet
    ENTERTAINMENT_MASKED_PK_GAME_END = 164;       // 娱乐厅蒙面pk活动结束 see masked-pk-logic_.proto ChannelMaskedPKConf

    LIVE_MASKED_PK_STATUS_CHANGE = 165;  // 语音直播厅蒙面pk状态变更 see masked-pk-logic_.proto ChannelMaskedPKStatus
    LIVE_MASKED_PK_GAME_BEGIN = 166;     // 语音直播厅蒙面pk活动开始 see masked-pk-logic_.proto ChannelMaskedPKConf
    LIVE_MASKED_PK_BATTLE = 167;         // 语音直播厅蒙面pk PK战况 see masked-pk-logic_.proto ChannelMaskedPKBattle
    LIVE_MASKED_PK_SETTLE = 168;         // 语音直播厅蒙面pk单场结算 see masked-pk-logic_.proto ChannelMaskedPKResult
    LIVE_MASKED_PK_REVIVE = 169;         // 语音直播厅蒙面pk单场复活阶段进度 see masked-pk-logic_.proto ChannelMaskedPKRevive
    LIVE_MASKED_PK_REVIVE_RET = 170;     // 语音直播厅蒙面pk单场复活结果 see masked-pk-logic_.proto ChannelMaskedPKReviveRet
    LIVE_MASKED_PK_GAME_END = 171;       // 语音直播厅蒙面pk活动结束 see masked-pk-logic_.proto ChannelMaskedPKConf

    ENTERTAINMENT_MASKED_PK_QUALIFIED_ANCHOR_NOTICE = 172;       // 有资格主播比赛开始提示弹窗 see masked-pk-logic_.proto ChannelQualifiedAnchorNotice

    ENTER_CHANNEL_WITH_RCMD_LABEL_PUSH_TO_OWNER = 173;    //基于历史行为标签的进房行为推送消息给房主

    CHANNEL_CP_GAME_INFO_CHANGE = 174;  // cp战信息变化 pb_opt_content see channel-cp-game-logic_.proto CpGameInfo
    CHANNEL_CP_GAME_RESULT = 175;       // cp战结果 pb_opt_content see channel-cp-game-logic_.proto CpGameResult

    ENTERTAINMENT_MASKED_PK_STATUS_CHANGE_V2 = 176;  // 娱乐厅蒙面pk状态变更 see masked-pk-logic_.proto ChannelMaskedPKStatus
    ENTERTAINMENT_MASKED_PK_GAME_BEGIN_V2 = 177;     // 娱乐厅蒙面pk活动开始 see masked-pk-logic_.proto ChannelMaskedPKConf
    ENTERTAINMENT_MASKED_PK_BATTLE_V2 = 178;         // 娱乐厅蒙面pk PK战况 see masked-pk-logic_.proto ChannelMaskedPKBattle
    ENTERTAINMENT_MASKED_PK_SETTLE_V2 = 179;         // 娱乐厅蒙面pk单场结算 see masked-pk-logic_.proto ChannelMaskedPKResult
    ENTERTAINMENT_MASKED_PK_REVIVE_V2 = 180;         // 娱乐厅蒙面pk单场复活阶段进度 see masked-pk-logic_.proto ChannelMaskedPKRevive
    ENTERTAINMENT_MASKED_PK_REVIVE_RET_V2 = 181;     // 娱乐厅蒙面pk单场复活结果 see masked-pk-logic_.proto ChannelMaskedPKReviveRet
    ENTERTAINMENT_MASKED_PK_GAME_END_V2 = 182;       // 娱乐厅蒙面pk活动结束 see masked-pk-logic_.proto ChannelMaskedPKConf
    ENTERTAINMENT_MASKED_PK_QUALIFIED_ANCHOR_NOTICE_V2 = 183;       // 有资格主播比赛开始提示弹窗 see masked-pk-logic_.proto ChannelQualifiedAnchorNotice
    ENTERTAINMENT_MASKED_PK_QUICK_KILL_CHANGE = 184; // 娱乐厅蒙面pk 斩杀变更 see masked-pk-logic_.proto QuickKillChangeOpt
    ENTERTAINMENT_MASKED_PK_QUALIFIED_ANCHOR_CHANGE = 185;    // 娱乐厅蒙面pk房间签约成员变成 see masked-pk-logic_.proto  ChannelQualifiedAnchorChangeMsg

    LIVE_MASKED_PK_STATUS_CHANGE_V2 = 186;  // 语音直播厅蒙面pk状态变更 see masked-pk-logic_.proto ChannelMaskedPKStatus
    LIVE_MASKED_PK_GAME_BEGIN_V2 = 187;     // 语音直播厅蒙面pk活动开始 see masked-pk-logic_.proto ChannelMaskedPKConf
    LIVE_MASKED_PK_BATTLE_V2 = 188;         // 语音直播厅蒙面pk PK战况 see masked-pk-logic_.proto ChannelMaskedPKBattle
    LIVE_MASKED_PK_SETTLE_V2 = 189;         // 语音直播厅蒙面pk单场结算 see masked-pk-logic_.proto ChannelMaskedPKResult
    LIVE_MASKED_PK_REVIVE_V2 = 190;         // 语音直播厅蒙面pk单场复活阶段进度 see masked-pk-logic_.proto ChannelMaskedPKRevive
    LIVE_MASKED_PK_REVIVE_RET_V2 = 191;     // 语音直播厅蒙面pk单场复活结果 see masked-pk-logic_.proto ChannelMaskedPKReviveRet
    LIVE_MASKED_PK_GAME_END_V2 = 192;       // 语音直播厅蒙面pk活动结束 see masked-pk-logic_.proto ChannelMaskedPKConf
    LIVE_MASKED_PK_QUICK_KILL_CHANGE = 193; // 语音直播厅蒙面pk 斩杀变更 see masked-pk-logic_.proto QuickKillChangeOpt
  //LIVE_MASKED_PK_PEAK_PK_CHANGE = 194;    // 语音直播厅蒙面pk 巅峰对决变更 see masked-pk-logic_.proto PeakPkChangeOpt

    SING_A_ROUND_STATUS_CHANGE = 195; // 接歌抢唱 状态变更 对应 sing-a-round-logic_.proto SingingGameChannelInfoNotify
    SING_A_ROUND_GRAB_MIC = 196;      // 接歌抢唱 开始抢唱 对应 sing-a-round-logic_.proto GrabSingingGameMicNotify
    SING_A_ROUND_HELP = 197;          // 接歌抢唱 开始帮唱 对应 sing-a-round-logic_.proto SingingHelpNotify
    SING_A_ROUND_SONG_RESULT = 198;   // 接歌抢唱 歌曲成绩 对应 sing-a-round-logic_.proto SingingGameSongResultNotify
    SING_A_ROUND_GAME_RESULT = 199;   // 接歌抢唱 游戏成绩 对应 sing-a-round-logic_.proto SingingGameResultNotify
    SING_A_ROUND_LIKE = 200;          // 接歌抢唱 “牛啊” 对应 sing-a-round-logic_.proto SingingGameLikeNotify
    SING_A_ROUND_FIST_BUMP = 201;     // 接歌抢唱 碰拳通知 对应 sing-a-round-logic_.proto SingingGameFistBumpNotify
    SING_A_ROUND_LIKE_MSG = 202;      // 接歌抢唱 “牛啊”公屏消息 对应 sing-a-round-logic_.proto SingingGameLikeMsg
    SING_A_ROUND_LIKES = 203;         // 接歌抢唱 “一键碰拳” 对应 sing-a-round-logic_.proto SingingGameLikesNotify
    SING_A_ROUND_SWITCH_GAME_TYPE = 204; // 接歌抢唱 切换游戏类型 对应 sing-a-round-logic_.proto SwitchSingingGameTypeNotify


    CHANNEL_RED_PACKET_LIST_CHANGE = 206; // 房间红包列表变更 see channel-red-packet-logic_.proto RedPacketChangeOpt
    CHANNEL_RED_PACKET_RAIN_CHANGE = 207; // 房间红包雨变更 see channel-red-packet-logic_.proto RedPacketRainChangeOpt
    CHANNEL_RED_PACKET_SETTLE = 208; // 房间红包结算推送 see channel-red-packet-logic_.proto RedPacketSettleOpt

    CHANNEL_KTV_UPDATE = 215; // 歌曲播放变化push see channel-ktv-logic_.proto ChannelKTVUpdate
    CHANNEL_KTV_MSG = 216; // ktv相关公屏消息push  推送协议见im.proto的RichTextMsg
    CHANNEL_KTV_HAND_CLAP = 217; // 鼓掌push see channel-ktv-logic_.proto ChannelKTVHandClap
    CHANNEL_KTV_PLAYLIST_UPDATE = 218; // 歌单变更push see channel-ktv-logic_.proto PlayListUpdate

    MAGIC_SPIRIT_PRESENT = 220; // 魔法精灵礼物push see magic-spirit-logic_.proto SendMagicSpiritOpt

    FELLOW_BOUND_MSG = 221; // 挚友绑定成功的房间推送  see fellow-logic_.proto  ChannelFellowMsg
    FELLOW_CHANNEL_INVITE_MSG = 222; // 房间内挚友邀请的房间推送  see fellow-logic_.proto  ChannelFellowMsg
    FELLOW_MIC_CHANGE_MSG = 223; // 麦上挚友信息变更推送  see fellow-logic_.proto  MicFellowInfoChangeInfo

    //MAGIC_SPIRIT_UNPACK_PRESENT = 224;                // 魔法精灵 待开箱礼物push see magic-spirit-logic_.proto UnpackGiftOpt
    MAGIC_SPIRIT_CHANNEL_UNPACK_PRESENT_CHANGE = 224; // 魔法精灵 房间待开箱礼物列表变更 push see magic-spirit-logic_.proto ChannelUnpackGiftListChangeOpt

    LEVELUP_PRESENT_LEVELUP = 225;    //升级礼物用户升级房间推送

    ENTERTAINMENT_MASKED_PK_TAIL_LIGHT = 226;  //娱乐厅蒙面PK尾灯push see masked-pk-logic_.proto MaskedPkTailLight
    LIVE_MASKED_PK_TAIL_LIGHT = 227;  //语音直播房蒙面PK尾灯push  see masked-pk-logic_.proto MaskedPkTailLight

    CHANNEL_CP_GAME_MVP_AUTO_HOLD_MIC_MSG = 228; //cp战MVP用户自动上麦推送 see channel-cp-game-logic_.proto CpGameMvpAutoHoldMicInfo

    CHANNEL_HOT_VALUE_MSG = 229; //房间热度值刷新 推送协议定义在 channel_opt_.proto  ChannelHotValueNotifyMsg//

    SING_A_ROUND_VOTE = 230;    // 接歌抢唱 投票 对应 sing-a-round-logic_.proto SingingGameVoteNotify
    SING_A_ROUND_PRELOAD = 231; // 接歌抢唱 预加载资源 对应 sing-a-round-logic_.proto SingingGamePreloadNotify

    USER_MUSIC_RANK_LEVEL_UP = 233; // 升段推送push see user-music-rank-logic_.proto UserMusicRankLevelUp
    USER_MUSIC_RANK_MIC_EXT_INFO = 234; // 麦位框推送push see user-music-rank-logic_.proto UserMusicRankMicExtInfo

    CHANNEL_CP_CHOSE_RARE_RESULT = 235;       // cp战选择稀缺关系结果 pb_opt_content see channel-cp-game-logic_.proto CpChoseRareResult

    LISTENING_FLOWER_AWARD_MSG = 236; // 听歌花花奖励消息 对应 channel-listening-logic_.proto FlowerAwardMsg
    USER_OPE_CHANNEL_BOX_NOTIFY = 237;  //用户进退包厢的推送消息
    LISTENING_FLOWER_CHANGE = 238; // 花花数量变化（挂房听歌） 对应 channel-listening-logic_.proto FlowerNotify
    LISTENING_LIKE_SONG = 239; // 喜欢歌曲（挂房听歌） 对应 channel-listening-logic_.proto LikeSongNotify
    USER_APPLY_CHANNEL_BOX_NOTIFY = 240;  //用户申请进入包厢的推送消息
    LISTENING_CHANGE_THEME = 241; // 修改主题（挂房听歌） 对应 channel-listening-logic_.proto ChannelListenThemeNotify

    CHANNEL_ROLE_CHANGE_MSG = 242; // 房间角色修改消息
    CHANNEL_MEMBER_INFLATE = 243; // 房间灌水消息

    NEW_CHANNEL_CP_GAME_INFO_CHANGE = 244;  // cp战信息变化(新版本) pb_opt_content see channel-cp-game-logic_.proto CpGameInfo

    MELEE_CHANNEL_MIC_WHITE_LIST_CHANGE = 245;   //团战房白名单变更 pb_opt_content see melee-channel-logic_.proto MicWhiteListChangeMsg
    MELEE_CHANNEL_MIC_WHITE_APPLY_CHANGE = 246;  //团战房白名单申请列表变更 pb_opt_content see melee-channel-logic_.proto MicWhiteApplyListChangeMsg

    // 批量踢下麦位通知 （消息的pb_opt_content字段中会携带channel_opt_.proto 中定义的 BatKickMicOpt)
    Bat_CHANNEL_KICKED_MIC = 247;

    MN_CHANNEL_PERFORMANCE_CHANGE_MSG = 248; // 乐窝房间节目单内容变化 消息的optcontent字段中会携带music-nest-logic_.proto中定义
    MN_CHANNEL_PERFORMANCE_STAGE_CHANGE_MSG = 249; // 乐窝房间节目单环节变化 消息的optcontent字段中会携带music-nest-logic_.proto中定义

    MELEE_CHANNEL_MIC_WHITE_LIST_SWITCH_CHANGE = 250;  //团战房白名单开关变更 pb_opt_content see melee-channel-logic_.proto WhiteListSwitchStatusChangeMsg

    USER_MUSIC_RANK_DIALOG = 251;//音乐唱力弹窗

    CHANNEL_CALL_GROUP_CREATE_NOTIFY = 252;  //房主开了召集令

    PIA_SWITCH = 253; // Pia戏模式切换广播 pb_opt_content see pia_.proto PiaSwitch
    PIA_INFO = 254; // Pia戏信息广播 pb_opt_content see pia_.proto PiaInfo

    CHANNEL_KTV_BURST_LIGHT = 255; // 爆灯push see channel-ktv-logic_.proto ChannelKTVHandClap
    CHANNEL_KTV_HAND_FIVE = 256; // 一键击掌push see channel-ktv-logic_.proto ChannelKTVHandClap

    MUSIC_NEST_NEXT_DIRECTION_ACT = 257; /* 下一场引导 */
    MUSIC_NEST_NEXT_DIRECTION_ACT_DISAPPEAR = 258; /* 下一场引导消失 */

    RAP_EXPRESS_RESPECT_ANIMATION_NOTIFY = 259;  // 说唱房respect点击动画通知
    RAP_EXPRESS_RESPECT_SCREEN_NOTIFY = 260; // 说唱房屏幕消息通知

    CHANNEL_SWITCH_CHANNEL_MODE = 261;  // 房间玩法切换 pb_opt_content see channel-mode-mgr_.proto SwitchChannelMode

    LISTENING_CHECK_IN_TOPIC_CHANGE = 262; // 挂房听歌打卡话题改变
    LISTENING_CHECK_IN = 263; // 挂房听歌打卡

    KNIGHT_GROUP_CHANNEL_MSG = 264;// 骑士团

    SWITCH_CHANNEL_SCHEME_V2 = 265; //房间玩法切换push v2   see channel-scheme_push_.proto ChannelSchemeSwitchEvent

    AUTO_EXIT_CHANNEL = 266;        //自动退房push  see channel_opt_.proto AutoExitChannelOpt

    RHYTHM_CHANNEL_VISITED_SIZE = 267; // UGC房间推送已看过人数的总数 music-nest-logic_.proto VisitedSizeInfo

    ONE_PIECE_WIND_CHANGE = 268; // 航海寻宝风向变化 see one-piece-logic_.proto WindChangeNotify

    UGC_LIVE_TOGETHER_CONFIG_CHANGE = 269;  //UGC一起看直播，直播配置变更  see topic_channel_.proto LiveConfigChangeMsg

    UGC_LIVE_TOGETHER_ADS_CHANGE = 270; //UGC一起看直播，直播广告变更   see topic_channel_.proto AdsConfigChangeMsg
    UGC_LIVE_TOGETHER_BUBBLE_CHANGE = 271; //UGC一起看直播，引导气泡变更   see topic_channel_.proto BubbleConfigChangeMsg

    RICH_CHARM_CHANGE = 272; // 财富魅力值变化

    USER_DEFINED_VOTE_PK_CHANGE = 273; /* 房间自定义投票推送 UserDefinedVotePKNotify */

    CHANNEL_PUNISH_ABUSE_WARNING = 275; // 房间辱骂惩罚警告 see channel_opt_.proto ChannelPunishAbuseOpt

    LISTENING_SHARED_PAGE = 276; // 挂房听歌 分享页弹窗 对应 channel-listening-logic_.proto SharedPageNotify

    LIVE_WELCOME_POP =277;  //欢迎弹层公屏推送 对应music-nest-logic.proto

    COKE_STATE_INFO=278; //快乐水开始与结束推送消息 对应music-nest.proto

    YOU_KNOW_WHO_CHANNEL_MSG = 279; // 神秘人状态变更推送 对应you-know-who-logic_.proto UKWChangeChannelMsg

    PIA_DRAMA_ORDER_LIST = 280; // pia戏-已点列表通知 对应pia_.proto DramaOrderList
    PIA_MIC_ROLE_MAP = 281; // pia戏-麦位绑定角色通知 对应pia_.proto MicRoleMap
    PIA_CHANNEL_DRAMA_STATUS = 282; // pia戏-房间走本状态通知 对应pia_.proto ChannelDramaStatus
    PIA_CHANNEL_DRAMA_BGM_STATUS = 283; // pia戏-房间剧本的BGM状态通知 对应pia_.proto DramaBgmStatus

    CONCERT_PRELOAD_RESOURCE = 284; // 预加载资源 对应 concert-logic_.proto PreloadNotify
    CONCERT_SINGING_INFO_UPDATE = 285; // 演唱信息更新 对应 concert-logic_.proto SingingInfoNotify
    CONCERT_MAIN_OFFLINE = 286; // 推流的人掉线 对应 concert-logic_.proto SingingInfoNotify

    CHANNEL_BOX_INFO_CHANGE_NOTIFY = 287; // 子频道信息变更推送
    CHANNEL_BOX_COMMON_MIC_NOTIFY = 288; // 团战公共麦推送

    CONCERT_SONG_RESULT = 289; // 演唱结果 对应 concert-logic_.proto SongResultNotify
    CHANNEL_CONCERT_MSG = 290; // 乐队相关公屏消息push  推送协议见im.proto的RichTextMsg

    PIA_CHANNEL_DRAMA_BGM_VOL_STATUS = 291; // pia戏-房间剧本的BGM音量状态通知 对应pia_.proto DramaBgmVolStatus

    //赛事临时房变更通知，结构体详见game-tmp-channel_.proto
    GAME_TMP_CHANNEL_CONTEST_INFO_CHANGE = 292; //赛事信息变更通知
    GAME_TMP_CHANNEL_PLAYER_CHANGE = 293; //玩家名单变更通知
    GAME_TMP_CHANNEL_BUTTON_CHANGE = 294; //赛事按钮变更通知

    YOU_KNOW_WHO_EXPOSURE_CHANNEL_MSG = 295; // 神秘人现身房间推送 对应you-know-who-logic_.proto UKWExposureChannelMsg

    MUSIC_TOPIC_REUNION_INTERACTION = 296; // 进房重逢互动 push see music-topic-channel-logic_.proto ReunionInteractionPush

    CHANNEL_LIVE_LOTTERY_BEGIN = 297; // 开始抽奖push see channel-lottery_.proto ChannelLotteryInfoPush
    CHANNEL_LIVE_LOTTERY_RESULT = 298;// 抽奖结果push see channel-lottery_.proto ChannelLotteryResultPush

    CHANNEL_KTV_HAND_FIVE_NEW = 299; // 一键击掌push see channel-ktv-logic_.proto ChannelKTVHandClap

    CHANNEL_BRAND_BG_TOPIC = 300; // 是否开启厂牌专属房间背景及话题入口

    CHANNEL_CONTROL_TYPE_CHANGE_MSG = 301;  // 进房控制类型变更推送

    PGC_CHANNEL_PK_BATTLE_INFO = 302;  // PGC-PK实时战况信息,PK值 MVP变更等  see pgc-channel-pk-loigc_.proto PgcChannelPKBattle
    PGC_CHANNEL_PK_INVITE_MSG = 303;  // PGC-PK 收到PK邀请  see pgc-channel-pk-loigc_.proto PgcChannelPKInviteOpt
    PGC_CHANNEL_PK_INVITE_RESP = 304;  // PGC-PK 通知PK发起方PK邀请结果  see pgc-channel-pk-loigc_.proto PgcChannelPKInviteRespOpt
    PGC_CHANNEL_PK_MIC_CHANGE = 305;  // PGC-PK 连麦音频流信息变更  see pgc-channel-pk-loigc_.proto PgcChannelPKMicRadioInfoPushOpt
    PGC_CHANNEL_PK_QUICK_KILL_CHANGE = 306;  // PGC-PK 斩杀变更  see pgc-channel-pk-loigc_.proto PgcChannelPKQuickKillChangeOpt
    PGC_CHANNEL_PK_PEAK_CHANGE = 307;  // PGC-PK 巅峰对决变更  see pgc-channel-pk-loigc_.proto PgcChannelPKPeakChangeOpt
    PGC_CHANNEL_PK_RESULT = 308;  // PGC-PK 结果  see pgc-channel-pk-loigc_.proto PgcChannelPKResultOpt
    PGC_CHANNEL_OPPONENT_MIC_BAN = 309;  // PGC-PK 禁言通知  see pgc-channel-pk-loigc_.proto PgcChannelPKOpponentMicFlagPushMsg
    MUSE_RECD_INTERACTIVE_MSG = 310; // 收到互动消息 对应muse-post-logic_.proto NewInteractiveMsgNotify

    NUM_BOMB_CHANGE_NOTIFY = 311; //数字炸弹变更通知，see channel-minigame-go-logic_.proto NumBombInfo

    PIA_CHANGE_PLAY_TYPE = 312; // pia戏-房间剧本的播放类型变更通知 对应pia_.proto PiaChangePlayType

    MELEE_CHANNEL_ENTER_ROOM_APPLY = 313; // 团战房白名单申请列表变更 pb_opt_content see melee-channel-logic_.proto NewChannelRoomApplicantNotify

    MUSE_NEW_POST_TIP = 314; // 新留言提醒 对应muse-post-logic_.proto NewPostNotify

    CONCERT_UPDATE_GRADE = 315; // 演唱成绩更新 对应 concert-logic_.proto UpdateGradeNotify

    ACTIVITY_POP_WINDOW_BASE_CHANNEL_MSG = 316; // 营收活动弹窗基础推送 对应 activity-push_.proto ActivityPopWindowBaseChannelMsg

    MUSIC_NEST_LIVE_ACTIVITY_CHANNEL_MSG = 317; // 乐窝活动 各种活动开始&结束的推送 对应 music-nest-logic_.proto MusicNestLiveActivitiesNotify

    TREASURE_BOX_ABOUT_TO_TRIGGER_CHANNEL_MSG = 318; // 活动宝箱 即将触发宝箱通知 see treasure-box-logic_.proto AboutToTriggerBoxOpt
    TREASURE_BOX_COME_CHANNEL_MSG = 319; // 活动宝箱 宝箱触发通知 see treasure-box-logic_.proto BoxComeOpt
    TREASURE_BOX_COME_ATTRACT_CHANNEL_MSG = 320; // 活动宝箱 宝箱降临引流浮层通知 see treasure-box-logic_.proto BoxComeAttractOpt
    TREASURE_BOX_OPEN_CHANNEL_MSG = 321; // 活动宝箱 宝箱开启通知 see treasure-box-logic_.proto BoxOpenOpt
    RHYTHM_MIC_SORT_INFO_CHANNEL_MSG=322;// Rhythm 麦位排序推送   see  rhythm_.proto
    WISH_LIST_CHANNEL_MSG = 323; //  see wish-list-logic_.proto WishListChangePushMsg
    STAY_ADD_TICKET_CHANNEL_MSG = 324; /* 停留加票  rhythm_.proto StayAddTicketNotify */
    CHANNEL_LISTENING_MOOD_UPDATE = 325; // 修改挂房状态 对应 channel-listening-logic_.proto ListeningMoodNotify

    PGC_CHANNEL_PK_CHOSE_INTERACTION_MSG = 326;  // PGC-PK 选择互动提示消息  see pgc-channel-pk-loigc_.proto PgcChannelPKChoseInteractionOpt
    PGC_CHANNEL_PK_INTERACTION_END = 327;  // PGC-PK 互动结束  see pgc-channel-pk-loigc_.proto PgcChannelPKInteractionEndOpt
    PGC_CHANNEL_PK_CHOSE_INTERACTION_ROOM_MSG = 328;  // PGC-PK 互动公屏消息  see pgc-channel-pk-loigc_.proto PgcChannelPKChoseInteractionRoomMsgOpt

    CONCERT_IMAGE_UPDATE = 329; // 修改形象 对应 concert-logic_.proto ConcertImageNotify

    PGC_CHANNEL_PK_BATTLE_INFO_V2 = 330;  // PGC-PK实时战况信息 V2,PK值 MVP变更等  see pgc-channel-pk-loigc_.proto PgcChannelPKBattle

    UGC_CHANNEL_DENOISE_MODE_CHANGE = 331; // UGC房间降噪模式变更推送 see user-logic_.proto UgcChannnelDenoiseModeChange

    CONCERT_SONG_TASK_PROGRESS_UPDATE = 332; // 乐队歌曲星级变化 对应 concert-logic_.proto ConcertSongTaskProgressUpdateNotify

    LISTENING_SHARE_CARD_V3 = 333;//挂房听歌，掉落卡片  对应 channel-listening-logic_.proto ChannelListeningShareCardV3

    LISTENING_AUTO_SONG_STATUS_CHANGE = 334;//挂房听歌，自动播放歌曲暂停/播放状态通知  对应 channel-listening-auto-play-logic_.proto AutoSongStatusNotify

    LISTENING_CHANNEL_MODE_CHANGE = 335;//挂房听歌，切换自动播放/点歌模式通知  对应 channel-listening-auto-play-logic_.proto ChannelPlayModeNotify

    LISTENING_CHANNEL_VOLUME_CHANGE = 336;//挂房听歌，音量改变通知  对应 channel-listening-auto-play-logic_.proto ChannelVolumeNotify

    RICH_TEXT_WITH_NO_SPECIAL_ACTION = 337; // 房间富文本消息内容，客户端不会做特殊的处理，直接将富文本消息按照协议显露在房间

    CHANNEL_ENTERTAINMENT_LOTTERY_BEGIN = 338; // 开始抽奖push see channel-lottery_.proto ChannelLotteryInfoPush
    CHANNEL_ENTERTAINMENT_LOTTERY_RESULT = 339;// 抽奖结果push see channel-lottery_.proto ChannelLotteryResultPush
    RHYTHM_MIC_NAME_INFO_CHANNEL_MSG=340;// Rhythm 麦位名称推送   see  rhythm_.proto


    PGC_GAME_PHASE_CHANGE = 341;  //see pgc-channel-game-logic_.proto GamePhaseChangeNotifyOpt
    PGC_GAME_BOMB_USER_INFO = 342;  //see pgc-channel-game-logic_.proto GameBombUserNotifyOPt
    PGC_GAME_BOMB_PUNISH_INFO = 343;  //see pgc-channel-game-logic_.proto GameBombPunishNotifyOPt
    PGC_GAME_THROW_USER_INFO = 344;  //see pgc-channel-game-logic_.proto GameThrowUserNotifyOPt
    PGC_GAME_BOMB_RESULT = 345;  //see pgc-channel-game-logic_.proto GameBombResultOpt
    SetPriorityDisplayCertNotify = 346;  //see personal-certification-logic_.proto SetPriorityDisplayCertNotify

    MUSE_RECD_INTERACTIVE_MSG_V2 = 347; // 收到互动消息 对应muse-post-logic_.proto NewInteractiveMsgNotify（V2版本用于兼容旧版）

    RHYTHM_BATTLE_CHANGE = 348; // /* 开启battle推送 */ 对应rhythm_.proto BattleStartNotify
    PGC_GAME_BOMB_RESCUE = 349;  //see pgc-channel-game-logic_.proto GameRescueNotifyOPt

    ACTIVITY_POP_WINDOW_CHANNEL_MSG = 350; // 营收活动新弹窗推送 对应activity_push_.proto ActivityPushMsg

    PGC_GAME_DIGITAL_BOMB_INFO_CHANGE = 351;  //see pgc-channel-game-logic_.proto DigitalBombGameInfoChange

    LISTENING_FLOWER_TO_POST = 352; // 花花通知手账本（挂房听歌） 对应 channel-listening-logic_.proto FlowerToPostNotify
    AUTO_PLAY_MODE_SELECT_PUSH = 353; /* 自动播放的播放模式选择 顺序、单曲、随机  app/channel-listening-auto-play-logic_.proto ChannelPlayModeSelectNotify */

    PGC_GAME_ADVENTURE_INFO_CHANGE = 354;  // pgc小游戏大冒险信息变更 see pgc-channel-game-logic_.proto AdventureInfoChangeOpt
    PIA_DIALOGUE_INDEX_FOLLOW_PUSH = 355; // pia戏段落跟随推送 pia_.proto PiaDialogueIndexFollowMsg

    PERFECT_COUPLE_GAME_INFO_CHANGE = 356;      //天配房间玩法 本轮游戏信息变更(阶段变更) see PerfectCpGameInfo
    PERFECT_COUPLE_PLAYER_INFO_CHANGE = 357;    //天配房间玩法 用户送礼值、爆灯信息变更 see PlayerDynamicInfo
    PERFECT_COUPLE_ANNOUNCE_RESULT = 358;       //天配房间玩法 公布结果 opt see PerfectCpGameResultOpt
    PERFECT_COUPLE_GAME_SYSTEM_MSG = 359;       //天配房间玩法 系统提示消息，文案 see content
    PERFECT_COUPLE_RELEASE_CLUE_EVENT = 360;    //天配房间玩法 主持人发布了线索  无opt
    PERFECT_COUPLE_PLAYER_BLOW_LIGHT = 361;     //天配房间玩法 玩家爆灯 opt see perfect_couple_match_logic.proto PlayerBlowLightOpt

    New_USER_RECEPTION = 362;     //新用户接新团 房客进房推送 消息体对应 UserReceptionGroupNotifyMsg

    CHANNEL_COMPETITION_BUTTON_NOTIFY = 363;     //房间内赛事活动 悬浮组件消息推送 消息体对应 CompetitionButtonNotifyMsg
    CHANNEL_COMPETITION_PUSH_NOTIFY = 364;     // 房间内赛事活动 ugc房赛事公屏推送 消息体对应 ChannelCompetitionNotifyMsg

    OFFERING_APPLY_LIST_UPDATE = 365; // 拍卖房报名列表更新 offer_room/offer_room.proto OfferRoomApplyList
    OFFERING_CUR_OFFERING_GAME_INFO = 366; // 拍卖房当前的游戏信息 offer_room/offer_room.proto  OfferRoomCurOfferingGameInfo
    OFFERING_NAME_PRICE_PUSH_MSG = 367; // 出价/一键定拍推送 offer_room/offer_room.proto OfferRoomNamePricePushMsg

    SOCIAL_COMMUNITY_CHAT_CHANNEL_TASK_MSG = 369; /* 社群聊天室公屏消息的任务消息 channel_opt_.proto SocialCommunityChatChannelTaskMsgOpt */
    SOCIAL_COMMUNITY_CHAT_CHANNEL_REPLY_TASK_MSG = 370; /* 社群聊天室公屏消息的完成任务，回复任务消息 channel_opt_.proto SocialCommunityChatChannelTaskReplyMsgOpt */

    PRESENT_SHOW_BOX = 371; // 全服礼物特效后置 - 显示礼物盒 userpresent_.proto PresentBoxInfo
    PRESENT_SHOW_BOX_OPEN = 372; // 全服礼物特效后置 - 打开礼物盒 userpresent_.proto PresentBoxOpenMsg

    REVENUE_EXT_GAME_WANT_PLAY_SYSTEM_MSG = 373; // 营收外部互动游戏-“我想玩”系统消息，see app\revenue_ext_game_logic\revenue_ext_game_logic.proto UserWantPlaySysMsgOpt
    REVENUE_EXT_GAME_MOUNT_CONF_MSG = 374; // 营收外部互动游戏-挂载游戏配置消息，see app\revenue_ext_game_logic\revenue_ext_game_logic.proto ExtGameCfg
    REVENUE_EXT_GAME_OP_CONF_MSG = 375; // 营收外部互动游戏-操作区域的配置消息，see app\revenue_ext_game_logic\revenue_ext_game_logic.proto ExtGameOpOpt

    CHANNEL_INTERACTION_MAGIC_EXPRESSION_MSG = 376; // 房间麦上互动表情消息

    BOOT_BACK_OFF_MSG = 377; // 公屏引导回关，channel-ktv-logic_.proto 的 ChannelKTVBootBackOffPushMsg

    NEED_REAL_NAME_AUTH_NOTIFICATION = 378; // 需要实名认证的通知，app/channel_opt_.proto NeedRealNameAuthNotification

    GLORY_WORLD_TASK_MSG = 379; // 荣耀时间弹窗通知，glory-world-logic_.proto GloryRewardChannelTaskMsg

    CHANNEL_SOCIAL_COMMUNITY_JOIN_FANS_MSG = 380; // 成功加入粉丝团推送

    CHANNEL_SOCIAL_COMMUNITY_ENTER_CHANNEL_MSG = 381;//社群房进房事件

    CHANNEL_SOCIAL_COMMUNITY_USER_ROLE_MSG = 382;//社群房用户角色推送

    SOCIAL_COMMUNITY_RANK_IN_CHANNEL_MSG = 383;   // 上榜社群的公演房增加榜单入口 推送 muse_social_community_logic.proto  RankInChannelNotify

    CHANNEL_UPPER_MIC_GUIDE = 384;  // 房间上麦引导推送
    CHANNEL_BIND_PHONE_NOTICE = 385; // 房间用户未绑定手机号推送

    UGC_WORD_CHANNEL_TYPING_STATUS_MSG = 386; //文字房用户打字状态推送 推送协议见channel-play_.proto TypeStatusPushMsg
    UGC_CHANNEL_PLAY_MODE_CHANGE_MSG = 387[deprecated=true]; //ugc房间玩法模式变化推送 推送协议见channel-play_.proto UgcChannelPlayModeChangeMsg

    USER_ASSISTED_VIOLATION_CHECKING = 394; // 用户辅助违规检查公屏 推送富文本，推送协议见im.proto的RichTextMsg

    CHANNEL_MIC_NAME_UPDATE = 395; // 麦位名称更新通知，推送 ChannelMicNameUpdateEvent

    TOPIC_CHANNEL_USER_WARN_MSG = 396; // 主题房全体用户警告
    CHANNEL_LIVE_MULTI_PK_STATUS_MSG = 397;     // 直播间多人pk状态推送 see MultiPkInfo
    CHANNEL_LIVE_MULTI_PK_KNIGHT_MSG = 398; // 直播间多人pk骑士在房信息推送 see channel_live_proto_.proto MultiPkKnightInfo

    // 新版本进房消息 pb_opt_content字段中会携带 channel_opt_v2.proto 中定义的 ChannelEnterOptV2 信息
    CHANNEL_ENTER_MSG_V2 = 399;

    REVENUE_PK_AUDIO_STREAM_INFO_MSG = 400; // 营收房pk语音流信息推送
    REVENUE_PK_BLANKING_STREAM_INFO_MSG = 401; // 营收房pk 房间闭麦信息

    CHANNEL_LIVE_MULTI_PK_SETTLE_MSG = 402;     // 直播间多人pk结算推送 see MultiPkSettleInfo
    CHANNEL_LIVE_MULTI_PK_LIVE_STATUS_CHANGE_MSG = 403;     // 直播间多人pk对方直播状态变更 see channel_live_proto_.proto PkChannelLiveStatusPushMsg

    REVENUE_EXT_GAME_SETTLE_MSG = 404; // 营收外部互动游戏-游戏结算，see app\revenue_ext_game_logic\revenue_ext_game_logic.proto ExtGameSettleOpt
    TREASURE_PRIVILEGE_CHANNEL_MSG = 405;     // 获得权限礼物权限 房间推送 see  present-go-logic_.proto TreasurePrivilegeChannelMsg

    CHARACTER_CHANNEL_APPLY_ON_MIC_MSG = 406; // 文字房申请/邀请 上麦推送   see  character_channel_logic.proto CharacterApplyOnMicNotify
    CHARACTER_CHANNEL_AGREE_ON_MIC_MSG = 407; //  文字房上麦推送，只推给申请人  see  character_channel_logic.proto CharacterAgreeOnMicNotify
    CHARACTER_CHANNEL_HANDLE_ON_MIC_MSG = 408; //  文字房 处理申请通知，房间广播，公屏消失 see  character_channel_logic.proto CharacterHandleMicNotify

    ACTIVITY_BIG_RACE_POP_WINDOW_CHANNEL_MSG = 409; // 营收活动大赛弹窗推送 对应activity_push_.proto  ActivityPushMsg
    PRESENT_WALL_NAMING_CHANNEL_MSG = 410; // 礼物墙冠名推送 对应present_wall_logic.proto PresentNamingMsg
    PRESENT_ILLUSTRATION_CHANNEL_MSG = 411;     //礼物图鉴推送消息对应present_illustration_logic.proto PresentIllustrationPushMsg
    MIJING_NPC_CHANNEL_IM_TEXT_MSG = 412; // 谜境NPC 文本公屏消息
    MIJING_NPC_CHANNEL_IM_IMG_MSG = 413; // 谜境NPC 图片公屏消息
    MIJING_NPC_EXIT_MSG = 414; // 谜境NPC 异常退出消息

    CHANNEL_LIVE_FANS_INFO_CHANGE_MSG = 415;   //语音直播房粉丝团信息变更推送 消息的optcontent字段中会携带channel-live-logic_.proto中定义的 FansInfo

    ESCAPE_CHANNEL_CLICK_SCREENSHOT_IMG_MSG = 416; // 密逃房间游戏中 一键截屏发送到公屏消息

    MUSE_CHANNEL_LIKE_SYNC = 417; // 用户点击推送，不一定参数有效的点击rap-logic_.proto的
    MUSE_CHANNEL_HOT_GAME_FLOAT = 419; // 兴趣热聊挑战浮窗

    ESCAPE_CHANNEL_GAME_STATUS_TEXT_MSG = 420; // 密逃房间游戏中 游戏状态公屏消息

    MUSE_CHANNEL_WELCOME_PUSH = 421; // 用户进房欢迎推送，push see rap-logic_.proto MuseChannelWelcomePush

    CHANNEL_MUSIC_LIST_TYPE_CHANGE_MSG = 422;  //歌曲播放列表模式变更推送（战歌/网易云，消息的pb_opt_content字段中会携带 channel_opt_.proto中定义的 ChannelMusicListTypeChangeOpt

    CHANNEL_AUDIO_VIOLATION_JUDGMENT_VOTE_INVITATION = 423; // 房间违规审判投票邀请，see channel_audio_violation.proto ChannelAudioViolationJudgmentVoteInvitation
    CHANNEL_AUDIO_VIOLATION_JUDGMENT_RESULT = 424; // 房间违规审判结果，see channel_audio_violation.proto ChannelAudioViolationJudgmentResult
    USER_VIEW_SOCIAL_COMMUNITY_CHANNEL_MSG=425 ;//个人房非社群用户点击社群入口，向房主发送推送  see muse_social_community_logic.proto    SocialCommunityChannelPush

    NOBILITY_CHANNEL_MSG_NEW = 426;  // 贵族等级变化房间推送新类型 see channel_.proto 的NobilityChannelNotifyMsg

    STAR_TRAIN_ACTIVITY_BEGIN_NOTIFY = 427; // 摘星列车活动即将开始提醒， see star_train_logic.proto StarTrainActBeginNotify
    STAR_TRAIN_PROGRESS_CHANGE = 428;       // 摘星列车进程信息更新， see star_train_logic.proto StarTrainProgressNotify
//    MT_INVITE_USER_ENTER_CHANNEL_WELCOME = 4xx;       // 非上麦好友邀请进房 push， see music-topic-channel-logic_.proto MTInviteUserEnterChannelWelcomePush
    CHANNEL_USER_MIC_VIRTUAL_AVATAR_CHANGE = 429; // 用户麦位虚拟形象变化通知，see virtual_avatar_logic.proto MicVirtualAvatarInfoChanegeOpt

    CHANNEL_MIC_SIZE_CHANGE_EVENT = 430; // 房间麦位数量变化推送，see channel_opt_v2.proto ChannelMicSizeChangeEvent

    ENUM_CHANNEL_EMPEROR_SET_PRESENT_PUSH = 431; // 帝王套礼物推送 see present_go_logic_.proto EmperorSetPresentInfo

    CHANNEL_EXTEND_MIC_SIZE_CHANGE_TEXT_MSG = 432; // 房间扩展麦位数量改变纯文本推送  see

    CHANNEL_FELLOW_UPGRADE_PUSH = 433; // 挚友升级推送 see fellow_logic_.proto FellowUpgradeAnimationPushMsg

    CHANNEL_GIFT_PK_RESULT = 434; // 礼物PK结果推送 see channel_gift_pk.proto ChannelGiftPkResultOpt

    CHANEL_IM_EMOJI_MSG = 435; // 房间公屏表情消息

    CHANNEL_TITLE_SUCCESS_MSG = 436; // 房间冠名成功弹窗 see pgc_channel_logic.proto TitleSuccessNotify

    CHANNEL_MUSE_ONE_CLICK_INVITE_MSG = 437; // muse房间一键邀请消息 see muse_interest_hub_logic.proto OneClickInviteMsg

    EMPEROR_SHOW_BOX = 438; // 全服礼物特效后置 - 显示礼物盒 present-go-logic_.proto PresentBoxInfo
    EMPEROR_SHOW_BOX_OPEN = 439; // 全服礼物特效后置 - 打开礼物盒 present-go-logic_.proto PresentBoxOpenMsg
    CHANNEL_NEW_MAGIC_EXPRESSION = 440; // 房间新魔法表情消息 see magic_expression_logic.proto MagicExpressionPush
    CHANNEL_ACCUMULATE_FOLLOW_MSG = 441;  //房间累计关注推送消息     music-topic-channel-logic_.proto   UserFollowInChannel

    MUSE_CHANNEL_HOLD_USER_ON_MIC_MSG = 442; // 进房抱上麦推送消息  muse_interest_hub_logic.proto HoldUserOnMicMsg

    MUSE_COMMON_CHANNEL_XML_MSG = 443; // muse房间公屏XML消息 see muse_interest_hub_logic.proto MuseCommonXMLMsgNotify

    TIME_PRESENT_LIST_CHANGE_MSG = 444; // 时间礼物列表变更消息 see time_present.proto TimePresentList

    CHANNEL_MIC_LAYOUT_TEMPLATE_SWITCH_MSG = 445; // 切换房间麦位布局模板推送 see channel_mic_layout_template.proto SwitchChannelMicLayoutTemplateOpt
    CHANNEL_MIC_LAYOUT_TEMPLATE_MIRRORED_MIC_CHANNEL_MSG = 446; // 设置镜像麦位id推送 see channel_mic_layout_template.proto SetMicLayoutMirroredMicIdOpt

    TIME_PRESENT_CHANGE_CHANNEL_IM_MSG = 447; // 时间礼物播放/停止的公屏消息 see time_present.proto TimePresentChangeChannelImMsg

    LIVE_INTIMATE_PRESENT_LIST_CHANGE_MSG = 448; // 直播间亲密礼物列表变更消息 see time_present.proto LiveIntimatePresentMsg
    LIVE_INTIMATE_PRESENT_CHANGE_CHANNEL_IM_MSG = 449; // 直播间亲密礼物播放/停止的公屏消息 see time_present.proto LiveIntimatePresentChangeChannelImMsg

    CHANEL_IM_SYS_SWIFT_EMOJI_MSG = 450; // 房间公屏快捷表情
    CHANNEL_OWNER_ICE_BREAKER_MSG=451;  //房间房主破冰信息

    CHANNEL_WEDDING_STAGE_CHANGE_MSG = 452; // 婚礼房阶段变化推送 see channel_wedding_logic.proto WeddingStageChangeOpt
    CHANNEL_WEDDING_LEVEL_CHANGE_MSG = 453; // 婚礼房等级变化推送 see channel_wedding_logic.proto WeddingLevelChangeOpt

    CHANNEL_GRAB_CHAIR_GAME_INFO_CHANEG_MSG = 454; // 抢椅子游戏完整游戏信息推送 opt:channel_wedding_logic.proto ChairGameInfo
    CHANNEL_GRAB_CHAIR_GAME_HAS_BEEN_CHOOSEN = 455; // [废弃, 暂时没用] 用户成为抢椅子游戏玩家提示,see channel_wedding_logic.proto ChairGamePlayerOpt

    CHANNEL_WEDDING_USER_POSE_CHANGE_MSG = 456; // [废弃, 暂时没用] 婚礼房用户站姿变化推送 see channel_wedding_logic.proto UserWeddingPoseChangeOpt
    CHANNEL_WEDDING_SCENE_NOTIFY_MSG = 457; // 婚礼房场景动画推送 see channel_wedding_logic.proto WeddingSceneNotifyOpt
    CHANNEL_WEDDING_GROUP_PHOTO_SEAT_CHANGE_MSG = 458; // 婚礼房合照位置信息变化推送 see channel_wedding_logic.proto WeddingGroupPhotoSeatChangeOpt
    
    CHANNEL_GRAB_CHAIR_GAME_INVITE_PUBLIC_MSG = 459; // 用户报名房间公屏推送 opt: ChairGameApplyMsgOpt
    CHANNEL_WEDDING_MIC_USER_CLOTHES_CHANGE_MSG = 460; // 婚礼房麦上用户服装变更通知信息 see channel_wedding_logic.proto WeddingClothesChangeOpt
    CHANNEL_WEDDING_GUEST_ENTER_MSG = 461; // 新人/嘉宾进房通知 see channel_wedding_logic.proto WeddingGuestEnterRoomOpt
    CHANNEL_WEDDING_HAPPINESS_CHANGE_MSG = 462; // 婚礼幸福值变化通知信息 see channel_wedding_logic.proto WeddingHappinessChangeOpt

    TSPEED_SPECIFIC_CHANNEL_MSG = 463; // T次元独属的房间消息 see tspeed_channel_opt.proto TSpeedSpecificChannelMsg

    // pc极速版房间背景变化  推送协议见 channel_background_logic_.proto 中的PclfgChannelBackgroundInfo
    PCLFG_CHANNEL_BACKGROUND_CHANGE = 464;

    COMMON_HIGHLIGHT_CHANNEL_IM = 465; // 通用高亮房间公屏通知 see push_.proto CommonHighLightChannelIm

    // pc极速房间麦位音量设置变化  推送协议见 channel-play_.proto 中的ChannelMicVolSetChangePushMsg
    CHANNEL_MIC_VOL_SET_CHANGE = 466;
    CHANNEL_AUDIO_BIT_RATE_CHANGE = 467; // 房间音频码率变化推送 see channel_mic_audio.proto ChannelAudioBitRateChangeMsg
    CHANNEL_MIC_SILENT_USER_MSG = 468; // 房间最新静默列表推送 see risk_mng_logic.proto ChannelMicSilentUserMsg

    CHANNEL_WEDDING_RECV_PRESENT_VAL_CHANGE = 469; // 婚礼中收礼值变化 see channel_wedding_logic.proto WeddingRecvPresentValChangeOpt
    CHANNEL_WEDDING_PRESENT_VAL_TOP_CHANGE = 470; // 婚礼中收/送礼值前N名用户变化 see channel_wedding_logic.proto WeddingPresentValTopChangeOpt
    CHANNEL_WEDDING_MVP_CHANGE = 471; // 婚礼中mvp用户变化 see channel_wedding_logic.proto WeddingPresentValMvpChangeOpt
    CHANNEL_WEDDING_MVP_SETTLEMENT = 472; // 婚礼结束mvp用户结算 see channel_wedding_logic.proto WeddingMvpUserSettlementNotify
    WEDDING_PRE_PROGRESS_UPDATE_PUSH = 473; // 婚礼前进度更新推送 see channel_wedding_logic.proto WeddingProgressUpdatePush

    CHANNEL_NEW_WEALTH_GOD_BOX_NOTIFY = 474; // 房间新生成财神宝箱通知 see wealth_god_logic.proto ChannelNewWealthGodBoxNotify
    RELIABLE_NEW_WEALTH_GOD_NOTIFY = 475; // 可靠-新财神降临通知 see wealth_god_logic.proto NewWealthGodNotify
    CHANNEL_WEALTH_GOD_PUBLIC_SCREEN_NOTIFY = 476; // 财神降临房间公屏通知 see wealth_god_logic.proto ChannelWealthGodPublicScreenNotify

    CHANNEL_MUSE_LIKE_ACTIVITY_MSG=550; //推送点赞活动消息 see muse_interest_hub_logic.proto PushLikeActivityMsg
    CHANNEL_USER_MIC_VIRTUAL_IMAGE_CHANGE = 551; // 用户麦位虚拟形象(二期)变化通知，see virtual_image_logic.proto UserVirtualImageChangeOpt
    CHANNEL_WEDDING_BRIDESMAID_UPDATE_MSG = 552; // 婚礼房伴娘信息变化推送 see channel_wedding_logic.proto WeddingBridesmaidUpdateOpt

    // 注意：先别往下加了，上面550之前还有很多空间！！
}

// 房间类型
enum ChannelType {
    INVALID_CHANNEL_TYPE = 0; // 无效值
    GUILD_TYPE = 1;           // 被公会绑定的房间 = 普通公会房
    FREE_TYPE = 2;            // 自由房间         = 欢城房间
    USER_CHANNEL_TYPE = 3;    // 被用户绑定的房间 = 个人房
    GUILD_PUBLIC_FUN_CHANNEL_TYPE = 4; // 被公会绑定的公开娱乐房间

    // 被用户绑定的 知识问答游戏房 (只能使用 单麦位模式)
    TRIVIA_GAME_CHANNEL_TYPE = 5;
    TEMP_KH_CHANNEL_TYPE = 6;    // 临时开黑房间(只能使用 高音质开黑麦位模式)
    RADIO_LIVE_CHANNEL_TYPE = 7; // 电台直播房间(只能使用 电台麦位模式)
    GUILD_HOME_CHANNEL_TYPE = 8; // 公会 主房间(只能使用 电台麦位模式)
    OFFICIAL_LIVE_CHANNEL_TYPE = 9; // 官方语音直播频道

    CPL_SUPER_CHANNEL_TYPE = 10; // cpl 活动大房

    COMMUNITY_CHANNEL_TYPE = 11; // 社群房间
}

enum ChannelMsgSubType {
    CHANNEL_MSG_SUB_CHANGE_NAME = 1;
    CHANNEL_MSG_SUB_CHANGE_PASSWORD = 2;
    CHANNEL_MSG_SUB_CHANGE_MIC_MODE = 3;         // 房间麦模式变化 [2021-6-18 5.6.0开始废弃这个类型]
    CHANNEL_MSG_SUB_CHANGE_ICON = 4;             // 房间图标
    CHANNEL_MSG_SUB_CHANGE_DESC = 5;             // 房间话题描述（标题）
    CHANNEL_MSG_SUB_CHANGE_RECOMMEND_SWITCH = 6; // 房间推荐开关
    CHANNEL_MSG_SUB_CHANGE_LOCKSCREEN_SWITCH = 7;       // 房间锁屏开关
    CHANNEL_MSG_SUB_CHANGE_AUTO_DISABLE_MIC_SWITCH = 8; // 房间自动锁麦开关
    CHANNEL_MSG_SUB_CHANGE_TAG_ID = 9;                  // 房间标签变化
  // 禁止公屏发图片开关
    CHANNEL_MSG_SUB_CHANGE_DISABLE_ATTACHMENT_MSG_SWITCH = 10;
    // 禁止等级限制的用户在公屏发言开关
    CHANNEL_MSG_SUB_CHANGE_DISABLE_LEVEL_LMT_SWITCH = 11;
    // 语音直播房 连麦 开关
    CHANNEL_MSG_SUB_CHANGE_OPEN_LIVE_CONNECT_MIC_SWITCH = 12;
    // 普通房间的 排麦 开关
    CHANNEL_MSG_SUB_CHANGE_OPEN_QUEUE_UP_MIC_SWITCH = 13;
    // 约玩主题房间的房间标签变化
    CHANNEL_MSG_SUB_CHANGE_TOPIC_CHANNEL_TAB_ID = 14;
    //房间（小队）信息
    CHANNEL_MSG_SUB_CHANGE_TOPIC_CHANNEL_TEAM_DESC = 15;
    //房间话题详情
    CHANNEL_MSG_SUB_CHANGE_TOPIC_DETAIL = 16;
}

//　麦位模式
enum EChannelMicMode {
    INVALID_MIC_SPACE_MODE = 0;         // 无效值
    HAVE_MIC_SPACE_MODE = 1;            // 开黑主席麦位模式 (废弃)
    FREE_MIC_SPACE_MODE = 2;            // 开黑自由模式 (废弃)
    FUN_MIC_SPACE_MODE = 3;             // 娱乐房麦位模式
    SINGLE_MIC_SPACE_MODE = 4;          // 单麦位模式(仅针对答题房间)
    HQ_KH_MIC_SPACE_MODE = 5;           // 高音质开黑麦位模式
    LIVE_MIC_SPACE_MODE = 6;            // 直播麦位模式
    DATING_MIC_SPACE_MODE = 7;          // 相亲麦位模式
    OPENGAME_MIC_SPACE_MODE = 8;        // 小游戏麦位模式
    WEREWOLVES_GAME_MIC_SPACE_MODE = 9; // 狼人杀麦位模式
    MASKED_DATING_MIC_SPACE_MODE = 10;  // 1V1语音聊天（蒙面聊天）

    CP_MIC_SPACE_MODE = 11; // cp麦位模式，用于活动大房
    IDOL_MIC_SPACE_MODE = 12; // 偶像麦位模式，用于活动大房

    MOVIE_MIC_SPACE_MODE = 13;   //电影房麦位模式

    CP_GAME_MIC_SPACE_MODE = 14; // cp站麦位模式，用于pgc
    SING_A_ROUND_MIC_SPACE_MODE = 15; // 接歌抢唱麦位模式

    KTV_MIC_SPACE_MODE = 16;  //ktv房麦位模式

    ROLE_PLAY_MIC_SPACE_MODE = 17;     //角色扮演

    LISTENING_MIC_SPACE_MODE = 18;  //挂房听歌

    MULTI_BATTLE_MIC_SPACE_MODE = 19; //团战

    OPENGAME_MULTI_MIC_SPACE_MODE = 20;  //小游戏多麦位模式，支持21麦

    MUSIC_NEST_MIC_SPACE_MODE = 21;   //乐窝麦位模式

    RAP_MIC_SPACE_MODE = 22;   //rap麦位模式

    PGC_WEREWOLVES_MIC_SPACE_MODE = 23;  //PGC狼人杀麦位模式

    CONCERT_MIC_SPACE_MODE = 24; // 乐队玩法

    PIA_V2_MIC_SPACE_MODE = 25; //新pia v2

    ESCAPE_GAME_MIC_SPACE_MODE = 26;  //密室逃脱

    GAME_RACE_MIC_SPACE_MODE = 27;    //赛事玩法

    LAST_MIC_SPACE_MODE = 99;   //最后使用的麦位模式,之后新增玩法时，不应该再增加麦位模式，而是增加布局模式
}

// 麦位框类型
enum EHeadwareType {
    COMMON_HEADWARE_TYPE = 0; // 普通麦位框，无附加内容
    CP_HEADWARE_TYPE =
            1; // CP麦位框，附加内容协议见channel_opt_.proto 中定义的 CPHeadwareOpt
}

// 房间人数负载
enum EChannelOverflowWarningType {
    OVERFLOW_WARNING_UNKNOWN = 0; // 未知类型，只返回负载信息
    OVERFLOW_WARNING_NEED_ID_AUTH = 1; // 需要一档实名
    OVERFLOW_WARNING_NEED_FACE_AUTH = 2; // 需要二档实名
}

enum EChannelMsgSubType {
    CHANNEL_TRUMPET_SUB_MSG = 1;
}

// 广播消息1
message ChannelMsg {
    required uint32 seq = 1;

    // 操作者的uid
    required uint32 from_uid = 2;
    required string from_account = 3;
    required string from_nick = 4;

    required uint64 time = 5;
    required uint32 to_channel_id = 6;
    required uint32 type = 7; // ChannelMsgType

    // 消息的明文UTF8内容部分(不包括附件内容)
    // 如果是type是文本消息那么是文本内容;如果是系统消息可能是json信息;
    required string content = 8;

    // 消息来源,0/1：App， 2：语音球
    optional uint32 origin = 9;

    // 被操作者uid，假如是被踢出频道，就是自己的uid
    optional uint32 target_uid = 10;
    optional string target_account = 11;
    optional string target_nick = 12;


    // 附件内容 (ps 由于IOS旧版本的实现问题他们将该字段强行解析为ChannelImageMsgContent
    // 那么该字段以后只能放 ChannelImageMsgContent 结构体)
    optional bytes att_content = 13;

    // 附加内容 废弃（务必注意该字段 可能存放任何类型的消息务必根据type进行判断）
    optional bytes opt_content = 14;

    // 附加内容 （新增 妈蛋 因为 原先 opt_content 里面放的多是json
    // json传输数据太大了 后续改用pb, 不同type 会有不同pb协议定义在channel_opt_.proto）
    optional bytes pb_opt_content = 15;

    optional int32 from_sex = 16;   // 操作者 性别
    optional int32 target_sex = 17; // 被操作者 性别
    optional int32 sub_type = 18; // 1 小喇叭消息类型 enum EChannelMsgSubType

    optional uint32 to_channel_type = 19;

    optional bool print_upgrade_tips = 20; // 此值为true时，客户端收到不支持的房间消息需要提示用户升级版本

    optional uint32 channel_box_id = 21;  //包厢id

    optional bool is_cross_channel_msg = 22; // 是否跨房消息

    // 操作者信息
    optional UserProfile from_user_profile = 23;
    // 被操作者信息
    optional UserProfile target_user_profile = 24;
}

// 广播消息2 与 ChannelMsg的区别 只是没有seq 不存储在channelim
message ChannelBroadcastMsg {
    required uint32 from_uid = 1; // 操作者的uid
    required string from_account = 2;
    required string from_nick = 3;
    required uint64 time = 4;
    required uint32 to_channel_id = 6;
    required uint32 type = 7; // ChannelMsgType
    required bytes content = 8;

    // 被操作者uid，假如是被踢出频道，就是自己的uid
    optional uint32 target_uid = 9;
    optional string target_account = 10;
    optional string target_nick = 11;

    // 附件内容
    // （务必注意该字段 可能存放任何类型的消息务必根据type进行判断）
    optional bytes att_content = 12;

    // 附加内容 （新增 妈蛋 因为 原先 opt_content 里面放的多是json
    // json传输数据太大了 后续改用pb 不同 type 会有不同pb协议 定义在 channel_opt_.proto）
    optional bytes pb_opt_content = 13;

    optional int32 from_sex = 14;   // 操作者 性别
    optional int32 target_sex = 15; // 被操作者 性别

    optional uint32 to_channel_type = 16;

    optional bool print_upgrade_tips = 17; // 此值为true时，客户端收到不支持的房间消息需要提示用户升级版本

    optional uint32 channel_box_id = 18;  //包厢id

    optional bool is_cross_channel_msg = 19; // 是否跨房消息

    // 操作者信息
    optional UserProfile from_user_profile = 20;
    // 被操作者信息
    optional UserProfile target_user_profile = 21;
}

// （3.1.0该结构已经废弃）麦上用户信息
message MicInfo {required GenericMember member = 1;}

// 3.1.0 后新的麦位信息
message MicrSpace {
    enum EMicrSpaceState {
        MIC_SPACE_NOMAL = 1;   // 正常
        MIC_SPACE_DISABLE = 2; // 锁麦位 此时麦位不能用
        MIC_SPACE_MUTE = 3;    // 麦位禁言 可以上麦但是不能说话
    }
    required uint32 mic_id = 1; // 麦位ID 1 - 9
    optional uint32 mic_state = 2; // EMicrSpaceState 麦位状态 1 正常 2 禁用 3 不可发言
    optional GenericMember mic_member = 3; // 麦上用户信息 如果麦上有人的话
    optional string headware_key = 4; // 麦上用户的头像装饰(麦位框)key 如果麦上有人的话
    optional int32 sex = 5;      // 麦上用户的性别
    optional uint32 hold_ts = 6; // 麦上用户的上麦时间

    // 7,8字段仅在公会主房间时会有填写
    optional string guild_official_name = 7; // 在公会内的角色名字(比如会长/副会长...)
    optional bool is_guild_channel_permission = 8; // 是否有公会的房间管理权限

    //官方认证信息
    optional string certify_title = 9; // 官方认证(仅在拉取麦列表请求中填写)
    optional string certify_intro = 10; // 认证介绍(仅在拉取麦列表请求中填写)

    // 等级
    // 房间内VIP等级 定义在ga_base 中
    optional ChannelMemberVipLevel vip_level = 11;

    // 用户的头像md5 (仅在拉取麦列表请求中填写)
    optional string mic_facemd5 = 12;

    optional uint32 headware_type = 13; // 麦位框类型 见EHeadwareType

    // 麦位框附加内容 （不同headware_type 会有不同pb协议，定义在channel_opt_.proto ）
    optional bytes pb_headware_opt_content = 14;

    optional uint32 nobility_level = 15; // 麦上用户贵族等级

    optional string certify_style = 16;   // 官方认证样式
    optional string headware_url = 17; // 头像装饰的url，已废弃

    optional UserChannelBoxInfo cur_box_info = 18;   //用户的包厢信息

    optional string v_style = 19; // 正在穿戴的 大v标识

    optional string certify_special_effect_icon_url = 20; // 大v特效url

    /* 个人认证标识 */
    optional uint32 cert_type = 21; /* personal-certification.proto CertType */
    optional string icon = 22;
    optional string text = 23;
    repeated string color = 24;
    optional string text_shadow_color = 25; /* 文字阴影颜色 */
    /* 个人认证标识 */

    optional string knight_nameplate_resource_url = 26; // 骑士铭牌

    optional UserUKWInfo ukw_info = 27; // 神秘人信息
    optional UserProfile user_profile = 28; // 新版麦上用户信息 如果麦上有人的话

    optional ga.EChannelMemberKnightLevelID knight_level = 29; // 骑士等级

    optional string headwear_extend_json = 30; // 通用的扩展配置
    optional string headwear_custom_text = 31; // 麦位框的自定义文案
    /* 个人认证标识 */
    optional ga.PersonalCertOptInfo cert_opt_info = 32;

    optional bytes revenue_mic_extern_data = 33 ;           //以后新增营收相关的数据都在该字段带给客户端 , see ga.RevenueMicExtendOpt
    optional  ga.PersonalCertificationMuseSocialCommunity  social_community= 34 [deprecated = true];     //认证标上的社群相关信息 （废弃）
}







message ChannelInfo {
    required uint32 channel_id = 1;              // 频道id
    required string channel_name = 2;            // 频道名字
    required uint32 channel_member_count = 3;    // 频道人数
    required uint32 session_id = 4;              // SDK语音房间id
    required uint32 channel_member_capacity = 5; // 频道容纳最大人数
    optional bool has_pwd = 6;                   // 是否有密码
    optional uint32 channel_display_id = 7;      // 房间的显示ID

    // 频道类型, 跟DetailInfo的一致 see enum ChannelType
    optional uint32 channel_type = 8;
    optional bool has_collected = 9;   // 是否已收藏
    optional string channel_icon = 10; // 房间图标的MD5
    optional string channel_desc = 11; // 房间话题的描述(标题)

    optional string channel_view_id = 12 ;   //新增string类型的房间显示id,旧的channel_display_id不满足超过10位的房间id
}

message ChannelDetailInfo {
    required ChannelInfo channel_info = 1;   // 频道信息
    required uint32 channel_creator_uid = 2; // 频道创建者uid

    // 频道创建者account (在进房请求中 不填写)
    required string channel_creator_account = 3;

    // 最多可以有多少个麦位 包括被关闭的 (废弃)
    required uint32 mic_capacity = 4;

    // 频道类型 see enum ChannelType
    required uint32 channel_type = 5;

    // 麦位被关掉了几个 (废弃)
    optional uint32 mic_entry_closesize = 6;

    // 麦位模式 see EChannelMicMode
    optional uint32 mic_mode = 7;

    // 创建者昵称 (在进房请求中 不填写)
    optional string channel_creator_nick = 8;

    // 房间的关联ID，如果是公会房 这个是公会的长ID
    // 如果是个人房这个是个人UID
    optional uint32 channel_bind_id = 9;

    // 是否打开了房间推荐开关 (PS:
    // 目前仅在进房请求和拉取详情请求中会返回该字段内容)
    optional bool is_open_recommend_switch = 10;

    // 是否打开了房间锁屏开关 (PS:
    // 目前仅在进房请求和拉取详情请求中会返回该字段内容)
    optional bool is_open_lock_screen_switch = 11;

    // 是否打开了房间自动锁麦开关 (PS:
    // 目前仅在进房请求和拉取详情请求中会返回该字段内容)
    optional bool is_open_auto_disable_mic_switch = 12;

    // 是否打开了房间禁止发图开关 (PS:
    // 目前仅在进房请求和拉取详情请求中会返回该字段内容)
    optional bool is_open_disable_attachment_msg_switch = 13;

    // 是否打开了房间发言用户等级限制开关 (PS:
    // 目前仅在进房请求和拉取详情请求中会返回该字段内容)
    optional bool is_open_disable_level_lmt_switch = 14;

    // 是否开启电台直播连麦功能，0关闭 1开启; (PS:
    // 目前仅在进房请求和拉取详情请求中会返回该字段内容)
    optional bool is_open_live_connect_mic_switch = 15;

    // 频道创建者de性别(在进房请求中不填写)
    optional int32 channel_creator_sex = 16;

    // 是否开启普通房间的排麦功能，0关闭 1开启; (PS:
    // 目前仅在进房请求和拉取详情请求中会返回该字段内容)
    optional bool is_open_normal_queue_up_mic_switch = 17;

    optional uint32 channel_bind_guild_display_id = 18; // for channel search
    optional string channel_bind_guild_name = 19;       // for channel search

    // 服务端下发, 收藏来源; (PS:
    // 仅在收藏列表请求中返回该字段内容)
    optional uint32 collect_source = 20;  // see ChannelCollectSource

    // 进房控制类型; (PS:
    // 目前仅在进房请求和拉取详情请求中会返回该字段内容)
    optional uint32 enter_control_type = 21;  // see EnterControlType
}

//
message GetChannelMemberInfoReq {
    required BaseReq base_req = 1;
    required uint32 channel_id = 2;
    required uint32 target_uid = 3; // 消息的明文UTF8内容部分 比如
                                  // 如果消息是个文本消息 那么这个字段是纯文本
}

message GetChannelMemberInfoResp {
    required BaseResp base_resp = 1;

    optional ChannelMemberVipLevel vip_level =
            2; // 房间内VIP等级 定义在ga_base 中
    optional NobilityInfo nobility_info = 3; // 贵族等级信息
    optional ChannelLiveFansInfo fans_info = 4; //语音直播房粉丝信息
}

// 房间成员
message ChannelMemberInfo {
    required string account = 1; // 成员账号
    required uint32 uid = 2;     // 成员uid
    required bool is_muted = 3; // 是否禁言 ( 字段已经废弃 是否禁言由禁言列表返回 )
    required string nick_name = 4; // 成员昵称

    // ( 字段已经废弃 ) 频道所在公会的角色 (不存在表示是频道没有绑定公会
    // 或者 用户是公会普通成员)  1是会长 2公会官员 3普通公会成员
    optional uint32 guild_role = 5;

    // ( 字段已经废弃 ) 频道所在公会的群的角色(不存在表示是频道没有绑定公会
    // 或者 用户是群普通成员) 1是群主 2是群管理员 3普通群成员
    optional uint32 group_role = 6;

    optional bool isHoldMic = 7;             // 是否在麦上
    optional uint32 sendgift_reddiamond = 8; // 房间消费数
    optional uint32 sex = 9;                 // 性别

    optional uint32 new_rich_level = 10;  // 用户的财富等级
    optional uint32 new_charm_level = 11; // 用户的魅力等级
    optional bool is_newbie = 12;         // 是否是新人

    // 在房间类型为 GUILD_HOME_CHANNEL_TYPE 的 公会主房间里面 才有(13，14)字段
    optional string guild_official_name = 13; // 在公会内的职位名字(比如会长/副会长...)
    optional bool is_guild_channel_permission = 14; // 是否有公会的房间管理权限

    optional ChannelMemberVipLevel vip_level = 15;                        // 房间内VIP等级 定义在ga_base 中
    optional string face_md5 = 16; // 用户头像；add 2019/11/28,only fill for
                                 // ChannelGetUnderTheMicroListResp
    optional NobilityInfo nobility_info = 17; //贵族属性

    // 房间用户排行榜中我的排名信息相关
    optional uint32 rank = 18; // 我的排名
    optional uint32 d_value = 19; // 距离上一名的差值

    // 仅房间在线榜有效
    optional uint32 total_consum = 20;    // 历史消费总值

    optional ChannelLiveFansInfo fans_info = 21;  // 语音直播粉丝信息
    optional SuperPlayerLevel super_player_level = 22;  // 超级会员等级信息

    optional bool is_hidden_consume = 23; // 是否隐藏

    optional uint32 knight_level = 24; // 骑士等级 定义在ga_base.EChannelMemberKnightLevelID

    optional UserUKWInfo ukw_info = 25; // 神秘人信息

    optional UserProfile user_profile = 26; // 用户信息
}


// 根据ChannelType带上用户的guild_id和group_id，以便服务器检查权限
// 该结构后续不需要使用
message ChannelPermissionInfo {
    required uint32 channel_type = 1; // see enum ChannelType
    optional uint32 guild_id = 2;     // channel_type == GUILD_TYPE 带guild id
    optional uint32 group_id = 3; // 群管理的时候带groupid (这个参数现在不需要了)
}

//消息引用结构体
message ChannelImReference {
    //引用消息类型
    required uint32 reference_type = 1; //enum ChannelImReferenceType
    //被引用数据,根据reference_type不同，数据结构不同
    optional bytes reference_data = 2;
    //引用消息所属的用户信息,非必填，看具体业务需要
    optional UserProfile reference_user_profile = 3;
    //被引用消息中,需要审核的内容,看具体业务需要,有填该值，会把该值和发送的公屏消息原文一起拼接送审
    optional string need_check_content = 4;
}
enum ChannelImReferenceType {
    ChannelImReferenceType_unspecified = 0;
    //通用图片消息引用
    ChannelImReferenceType_IMAGE = 1;
    //MT破冰问题消息引用
    ChannelImReferenceType_MT_ICE_BREAKING = 2;
}
//引用图片数据结构
message ChannelImReferenceImage {
    //缩略图的key,客户端根据该key拼接出缩略图的url;从图片消息ChannelImageMsgContent.thumb_image_key获取该值
    required string thumb_image_key = 1;
    //是否有原图，从图片消息ChannelImageMsgContent.is_exist_sourceimage 获取该值
    required bool is_exist_sourceimage = 2;
    //原图的key,下载原图走的是原来下载公屏图片原图的接口:DownloadChannelMsgAttachmentReq
    //从图片消息ChannelImageMsgContent.msg_attachment_key获取该值
    required string source_image_key = 3;
}
//MT破冰问题数据结构
message ChannelImReferenceMtIceBreaking {
    required string content = 1;
}

// 请求发送房间消息
message SendChannelTextMsgReq {
    required BaseReq base_req = 1;
    required uint32 channel_id = 2;
    required string content = 3; // 消息的明文UTF8内容部分 比如
                               // 如果消息是个文本消息 那么这个字段是纯文本
    optional uint32 origin = 4; // 消息来源,0/1：App， 2：语音球
    optional uint32 type = 5;   // 0:普通文本, 6:频道飞机票
                            // （ChannelMsgType）87:CHANNEL_TRUMPET_MSG 消息类型
    optional bytes opt_content =
            6; // 附加内容 （新增 务必注意该字段 可能存放任何类型的消息
         // 务必根据type进行判断）

    optional uint32 target_uid = 7; //新增 1对1发送时目标的UID
    optional uint32 channel_box_id = 8; //包厢id

    optional bytes client_ext_context = 9;//客户端透传的字段，团战房马甲等(看ClientExtContextList)
    optional bytes msg_at_context = 10; //房间@消息透传字段，富文本内容，格式与ugc_.proto -> PostInfo.content 一致
    optional ChannelImReference im_reference = 11; //消息引用
}

message SendChannelTextMsgResp {
    required BaseResp base_resp = 1;
    optional bytes zego_push_content =
            2; // 该字段不为空表示
         // 使用即构SDK推送文本消息，zego_push_content内容为ChannelBroadcastMsg结构体
    optional uint32 trump_left = 3; // 小喇叭剩余次数
}

enum ChannelMsgImageSetType {
    ChannelMsgImageSetType_None = 0;         // 无效
    ChannelMsgImageSetType_SCREENSHOTS = 1;  // 开黑游戏截图
}

message SendChannelAttachmentMsgReq {
    required BaseReq base_req = 1;
    required uint32 channel_id = 2;
    required bytes att_content = 3; // 附件内容 如果是图片则是图片的二进制
    optional uint32 origin = 4; // 消息来源,0/1：App， 2：语音球
    optional uint32 type = 5;   // 消息类型 ChannelMsgType
    optional uint32 channel_box_id = 6;  //包厢id
    optional bytes client_ext_context = 7;//客户端透传的字段(一般只放不是很重要的数据)，团战房马甲等(看ClientExtContextList)
    optional uint32 image_set_type = 8;  // 图片组类型，use ChannelMsgImageSetType
    optional string image_set_key = 9;   // image_set_type有效时携带，同一key视为一组
    // 扩展，消息类型不同结构不同，CHANEL_IM_EMOJI_MSG对应ChannelEmojiMsgContent
    //CHANEL_IM_SYS_SWIFT_EMOJI_MSG 对应ChannelImSysSwiftEmojiContent
    optional bytes msg_ext = 10;
    optional ChannelImReference im_reference = 11; //消息引用
}

message SendChannelAttachmentMsgResp {
    required BaseResp base_resp = 1;
    optional string image_format = 2; // 'PNG' 'GIF' 'JPG' 'JPEG'
    optional string msg_attachment_key =
            3; // 附件key 当 is_exist_sourceimage==ture时可以用于下载
    optional bool is_exist_sourceimage =
            4; // 表示是否还有原始数据  false表示没有原始数据了 无需下载
    optional uint32 channel_id = 5;
}

// 房间图片消息内容
message ChannelImageMsgContent {
    required bytes image_bin = 1; // 图片的缩略图的二进制流 如果
                                // is_exist_sourceimage == false 表示没有大图
    optional string image_format = 2; // 'PNG' 'GIF' 'JPG' 'JPEG'
    optional string msg_attachment_key =
            3; // 附件key 当 is_exist_sourceimage==ture时可以用于下载
    optional string msg_attachment_url =
            4; // 预留， 表示大图附件的url，可能为空 为空则使用key去下载附件
    optional bool is_exist_sourceimage =
            5; // 表示是否还有原始数据  false表示没有原始数据了 无需下载

    //缩略图key,用于图片引用时，客户端根据该key拼接出缩略图url
    optional string thumb_image_key = 6;
}

message ChannelEmojiMsgContent {
    required string id = 1;         //表情id
    required string url = 2;        //下载原图url，若有优先使用，若无使用id拼接，公屏发表情直接传最终访问资源的url，避免两端拼接出来的结果不一致导致审核问题
    required string thumbnail = 3;  //缩略url，若有优先使用，若无使用id拼接
    required uint32 height = 6;     //表情高像素(px)
    required uint32 width = 7;      //表情宽像素(px)
    optional string type = 8;       //表情包类型，备用字段 use "normal_emoji_pkg" 、"super_player_privilege_lv1"
}

//快捷表情消息
message ChannelImSysSwiftEmoji {
    required string url = 1;
    optional string text = 2;
}
message ChannelImSysSwiftEmojiContent {
    repeated ChannelImSysSwiftEmoji emoji =1;
}

// 根据附件KEY拉取消息附件内容
message DownloadChannelMsgAttachmentReq {
    required BaseReq base_req = 1;
    required uint32 channel_id = 2;
    required uint32 msg_type = 3;           // 消息类型 ChannelMsgType
    required string msg_attachment_key = 4; // 附件key 用于下载
}

message DownloadChannelMsgAttachmentResp {
    required BaseResp base_resp = 1;
    required uint32 channel_id = 2;
    required uint32 msg_type = 3; // 消息类型 ChannelMsgType 从请求中原样带回
    required bytes attachment_content =
            4; // 附件内容 如果是图片就是完整的图片二进制
    optional string msg_attachment_key = 5; // 附件key 用于下载 从请求中原样带回
}

//拉取频道缺失的seq的内容
message GetChannelMsgReq {
    required BaseReq base_req = 1;
    required uint32 channel_id = 2;
    required uint32 begin_seq = 3;
    required uint32 end_seq = 4;
    optional bool is_init =
            5; // true表明是初始化拉取历史消息，此时只返回部分历史消息列表
}

message GetChannelMsgResp {
    required BaseResp base_resp = 1;
    required uint32 channel_id = 2;
    required uint32 begin_seq = 3; // 如果 is_init == true 该字段无意义
    required uint32 end_seq = 4;   // 如果 is_init == true 该字段无意义
    repeated ChannelMsg channel_msg_list =
            5; // 如果 is_init == true 将只会返回特定类型的消息

    // 附件信息
    repeated MicInfo mic_list =
            6; // 上麦的用户列表 (该字段 在 is_init==true 情况下没有数据)
         // (3.0以上新版客户端 不返回该字段)
    required uint32 channel_user_count = 7;
    optional uint32 mic_entry_closesize =
            8; // 开黑模式下 麦位被关掉了几个 (3.0以上新版客户端 不返回该字段)
    optional bool is_init =
            9; // true表明是初始化拉取历史消息，此时只返回部分历史消息列表

    repeated MicrSpace full_mic_list =
            10; // 当前房间的完整的麦位信息 (该字段 在 is_init==true 情况下没有数据)
    optional uint32 mic_mode =
            11; // 当前房间麦模式           (该字段 在 is_init==true 情况下没有数据)
}

message CreateChannelOpt {optional uint32 max_member_count = 1;}


// 创建频道
message CreateChannelReq {
    required BaseReq base_req = 1;
    required string channel_name = 2;                   // 频道名字
    required ChannelPermissionInfo permission_info = 3; // 用户权限信息
    optional string passwd = 4;                         // 密码
    optional CreateChannelOpt create_opt = 5;           //额外选项
}

message CreateChannelResp {

    required BaseResp base_resp = 1;
    required ChannelDetailInfo channel_info = 2; // 频道信息
    required string create_message = 3;          // 创建频道成功的文案

    // 公会普通房 参数
    // 当前公会普通房剩余创建数量
    optional uint32 guild_nomal_remain = 4;

    // 公会公开娱乐房 的参数
    // 当前公会公开娱乐房剩余创建数量 ,  当创建类型type =
    // REQUEST_GUILD_PUB_FUN_ALL 时有效
    optional uint32 guild_pub_remain = 5;

    // 公会公开娱乐房禁止创建的描述语句, 当创建类型type =
    // REQUEST_GUILD_PUB_FUN_ALL 且 guild_pub_remain = 0 时有效
    optional string guild_pub_band_text = 6;

    // 有值则启用GRPC独立推送通道
    repeated GRPCPushProxy grpc_push_proxies = 7;

    optional bool audio_token_switch = 8;   //true才进行token校验
    optional bool push_token_switch = 9;    //true才进行token校验

    optional ChannelLayoutInfo layout_info = 10;
    optional ChannelMicAudioInfo mic_audio_info = 11;
}

// 解散频道
message ChannelDismissReq {
    required BaseReq base_req = 1;
    required uint32 channel_id = 2;                     // 频道账号
    required ChannelPermissionInfo permission_info = 3; // 用户权限信息
}

message ChannelDismissResp {
    required BaseResp base_resp = 1;
    required uint32 channel_id = 2; // 频道id
}

// 修改频道名字
message ChannelModifyNameReq {
    required BaseReq base_req = 1;
    required uint32 channel_id = 2;                     // 频道id
    required string channel_name = 3;                   // 频道名字
    required ChannelPermissionInfo permission_info = 4; // 用户权限信息
}

message ChannelModifyNameResp {
    required BaseResp base_resp = 1;
    required uint32 channel_id = 2;   // 频道id
    required string channel_name = 3; // 频道新名字
}

// 修改频道密码
message ChannelModifyPasswdReq {
    required BaseReq base_req = 1;
    required uint32 channel_id = 2;                     // 频道id
    required ChannelPermissionInfo permission_info = 3; // 用户权限信息
    optional string passwd = 4;                         // 密码
}

message ChannelModifyPasswdResp {
    required BaseResp base_resp = 1;
    required uint32 channel_id = 2; // 频道id
    required string passwd = 3;     // 密码
}

// 获取密码
message ChannelGetPasswdReq {
    required BaseReq base_req = 1;
    required uint32 channel_id = 2;
    required ChannelPermissionInfo permission_info = 3; // 用户权限信息
}

message ChannelGetPasswdResp {
    required BaseResp base_resp = 1;
    required uint32 channel_id = 2; // 频道id
    required string passwd = 3;     // 密码
}

enum EnterControlType {
    EnterControlType_UNKNOW    = 0;
    EnterControlType_CLOSED    = 1;  // 所有人可进房
    EnterControlType_PASSWD    = 2;  // 密码进房
    EnterControlType_WHITELIST = 3;  // 白名单
}
// 设置进房控制状态
message ChannelSetEnterControlTypeReq {
    required BaseReq base_req = 1;
    required uint32 channel_id = 2;
    required uint32 control_type = 3;  //see EnterControlType
    optional string passwd = 4;        // 如果上锁，需要携带密码
}

message ChannelSetEnterControlTypeResp {
    required BaseResp base_resp = 1;
    required uint32 channel_id = 2;
    optional string passwd = 4;
}

// 获取进房控制状态
message ChannelGetEnterControlTypeReq {
    required BaseReq base_req = 1;
    required uint32 channel_id = 2;
}

message ChannelGetEnterControlTypeResp {
    required BaseResp base_resp = 1;
    required uint32 channel_id = 2;
    required uint32 control_type = 3;  //see EnterControlType
    optional string passwd = 4;        // 如果上锁，下发密码
}

enum ESpecialOptSwitchBitMap {
    ENUM_SPC_OPT_SWITCH_NONE = 0;   // NONE
    ENUM_SPC_OPT_SWITCH_STERAO = 1; // 立體聲
}

message GRPCPushProxy {
    required string addr = 1;
    optional bool insecure = 2;     // 如果为true，则开启非TLS连接
    optional string identifier = 3; // 用于指定`push-proxy`头部

    // addr为ip+port形式返回时，下列字段通常会一起返回，需要设置到响应的连接参数中
    optional string authority = 4;
}

// 进入频道
message ChannelEnterReq {
    enum EChannelEnterSource {
        ENUM_CHANNEL_ENTER_NORMAL = 0;
        ENUM_CHANNEL_ENTER_TEMP_KH_PAIR = 1; // 从 临时开黑配对 入口
        ENUM_CHANNEL_ENTER_NEAR_BY = 2;      // 从 附近的人 入口
        ENUM_CHANNEL_ENTER_KH_TEAM = 3;      // 从 开黑组队 入口
        ENUM_CHANNEL_ENTER_LIVE = 4;         // 从 语音直播 入口
        ENUM_CHANNEL_ENTER_ROBOT = 5; // 从 机器人 入口(表明这是个机器人)
        ENUM_CHANNEL_ENTER_APICENTER = 6;            // 从 api center 入口
        ENUM_CHANNEL_ENTER_CHANNEL_GAME_RECRUIT = 7; // 从 房间游戏招募 入口
        ENUM_CHANNEL_ENTER_USER_ASYNC_NEWS_FEED = 8; // 从 异步内容动态 入口

        ENUM_CHANNEL_ENTER_IM = 9;            // 从单个IM聊天界面 跟随
        ENUM_CHANNEL_ENTER_CH_COLLECT = 10;   // 从 房间收藏
        ENUM_CHANNEL_ENTER_FUN_TAB = 11;      // 从 娱乐TAB
        ENUM_CHANNEL_ENTER_SEARCH = 12;       // 从 搜索 入口
        ENUM_CHANNEL_ENTER_FINDPLAY_TAB = 13; // 从 约玩 TAB

        ENUM_CHANNEL_ENTER_WEB = 14;             // 从 WEB进入
        ENUM_CHANNEL_ENTER_FRIENDOL_BANNER = 15; // 从 好友在线banner 跟随

        ENUM_CHANNEL_ENTER_PUBLISH_TOPICROOM = 16; // 从 发布主题房
        ENUM_CHANNEL_ENTER_FRIENDLIST_TAB = 17;    // 从 玩伴列表页
        ENUM_CHANNEL_ENTER_FINDPLAY_TAB_OFFICIAL = 18; // 从 约玩 TAB 官方推荐进去的
        ENUM_CHANNEL_ENTER_INVITE_ENTER_ROOM = 19;    // 从 邀请进房弹窗页
        ENUM_CHANNEL_ENTER_PUBLISH_TOPICROOM_V2 = 20; // 从 新版本发布主题房
        ENUM_CHANNEL_ENTER_QUICK_MATCH = 21;          // 从 速配队友进房

        ENUM_CHANNEL_ENTER_RECENTLY_ROOM_HISTORYLIST = 22; // 从 最近进房历史列表 进房
        ENUM_CHANNEL_ENTER_MSGTAB_USERHEADIMG_FOLLOW = 23; // 从 消息列表页 点用户 跟随进房
        ENUM_CHANNEL_ENTER_USER_DETAILINFO_FOLLOW = 24; // 从 用户资料页 跟随进房
        ENUM_CHANNEL_ENTER_MYSELF_ROOM = 25; // 从 我的房间页 点击进入我自己的房间
        ENUM_CHANNEL_ENTER_QUICK_MATCH_V2 = 26; // 从 新版本速配队友进房
        ENUM_CHANNEL_ENTER_INVITE_ENTER_ROOM_V2 = 27; // 从 新版本邀请进房弹窗页

        ENUM_CHANNEL_ENTER_HOUR_RANK_LIST = 28; // 从房间小时榜进房
        ENUM_CHANNEL_ENTER_MORE_CHANNELS = 29; // 从更多房间进房

        ENUM_CHANNEL_ENTER_VOICE_BALL = 30;                 // 用户语音球进房
        ENUM_CHANNEL_ENTER_CLICK_BROADCAST = 31;            // 通过点击全服进房
        ENUM_CHANNEL_ENTER_PEOPLE_SHARE = 32;               // 通过他人分享进房
        ENUM_CHANNEL_ENTER_GUILD_INVITE_CODE = 33;          // 新用户通过公会邀请码进房

        ENUM_CHANNEL_ENTER_FUN_PAGE = 34;                   // 娱乐频道页进房（5.2.0版本）
        ENUM_CHANNEL_ENTER_FUN_PAGE_CARD = 35;              // 娱乐频道页快速进房
        ENUM_CHANNEL_ENTER_FUN_PAGE_MORE = 36;              // 娱乐频道二级页进房（指从娱乐频道页点击更多后出现的页面）
        ENUM_CHANNEL_ENTER_FUN_PAGE_HOUR_RANK_LIST = 37;    // 小时榜页进房（特指点击娱乐频道页进入的小时榜页）

        ENUM_CHANNEL_ENTER_LOCAL_PAGE = 38;                // 同城玩列表页进房

        ENUM_CHANNEL_ENTER_LIVE_CLOSE_PAGE = 39;           // 用户侧直播结束页的推荐房

        ENUM_CHANNEL_ENTER_LIVE_START_BROADCAST = 40;      // 开播提醒页（指横幅的那个全局弹窗）

        ENUM_CHANNEL_ENTER_LIVE_MORE_FOLLOW_ANCHOR = 41;   // 直播间更多/关注主播

        ENUM_CHANNEL_ENTER_MASKED_CALL_MATCH = 42;         // 1v1聊天匹配进房

        ENUM_CHANNEL_ENTER_PLAY_TOGETHER = 43;              //一起玩进房

        ENUM_CHANNEL_ENTER_WARSONG_RECOMMEND = 44;          // 战歌主播推荐位进房

        ENUM_CHANNEL_ENTER_ANCHOR_RANK_LIST = 45;           // 房间主播榜单进房

        ENUM_CHANNEL_ENTER_KH_BANNER = 46;                  // 开黑banner资源位进房

        ENUM_CHANNEL_ENTER_HUNT_MONSTER_BREAKING_NEWS = 47;   //打龙王横幅进房

        ENUM_CHANNEL_ENTER_HUNT_MONSTER_FLOAT_ENTRY = 48;    // 房间内打龙王悬浮入口

        ENUM_CHANNEL_ENTER_IM_LIST_RECOMMENDED_CHANNEL_TO_FRESHMAN = 49;    // 聊天列表（萌新房推荐）

        ENUM_CHANNEL_ENTER_RADAR_ISSUE = 50;                        //雷达下发进房

        ENUM_CHANNEL_ENTER_CHANNEL_TRANSFER = 51;                    //房间转移进房

        ENUM_CHANNEL_ENTER_RADAR_INVITE = 52;                     //雷达约玩邀请

        ENUM_CHANNEL_ENTER_CHANNEL_DETAIL_PAGE_INVITE = 53;         //房间详情页发送邀请进房

        ENUM_CHANNEL_ENTER_GET_PLAYMATE_BLANK_LIST = 54; // 找玩伴空白列表

        ENUM_CHANNEL_ENTER_FROM_OFFICIAL_LIVE_CHANNEL = 55; // 从语音官频进入
        ENUM_CHANNEL_ENTER_FROM_INVITE_FROM_CHANNEL = 58;//房间邀请进房
        ENUM_CHANNEL_ENTER_SING_A_ROUND = 59;         // 接歌抢唱进房

        ENUM_CHANNEL_ENTER_MUSIC_TAB_QUICK_ENTRANCE = 60; //音乐tab快速入口进房
        ENUM_CHANNEL_ENTER_MUSIC_TAB_LIST = 61; //音乐tab列表
        ENUM_CHANNEL_ENTER_MUSIC_TAB_MUSIC_NEST = 62;//音乐tab乐窝音乐现场进房
        ENUM_CHANNEL_ENTER_MUSIC_NEST_TIME_SHAFT = 63;//乐窝主页时间轴
        ENUM_CHANNEL_ENTER_MUSIC_NEST_CLASSIFY_LIST = 64;//乐窝主页分类列表
        ENUM_CHANNEL_ENTER_MY_MUSIC_NEST = 65;//我的乐窝
        ENUM_CHANNEL_ENTER_MUSIC_NEST_DETAIL_TOGETHER_ENTER = 66;//乐窝详情页-进房一起作
        ENUM_CHANNEL_ENTER_MUSIC_NEST_DETAIL_NEWEST_SCENE = 67;//乐窝详情页-ta的最新现场

        ENUM_CHANNEL_ENTER_MATCH_PAGE_RECOMMEND_BUSINESS_CHANNEL_ENTER = 68;  // 匹配Tab-经营房间推荐入口进房
        ENUM_CHANNEL_ENTER_BOOK_MOTORCADE = 69;  // 预约车队弹窗进房
        ENUM_CHANNEL_ENTER_K_SINGING_RIGHT_NOW = 70; //K歌之王web页“马上开唱”按钮
        ENUM_CHANNEL_ENTER_K_SINGING_RECORD = 71; //K歌之王web页演唱记录进房
        ENUM_CHANNEL_ENTER_MUSIC_TAB_CASUAL_LISTENING = 72;//音乐频道页随便听听进房
        ENUM_CHANNEL_ENTER_MUSIC_NEW_USER_ACCEPTANCE = 73; // 音乐素材新用户承接进房
        ENUM_CHANNEL_ENTER_PIA_AGGREGATE = 74; // pia戏聚合页
        ENUM_CHANNEL_ENTER_HALF_SCREEN_COLLECT = 75;//玩伴在房间半屏页收藏进房
        ENUM_CHANNEL_ENTER_MUSIC_NEST_LARGE_SCREEN = 76;//乐窝大屏卡片入口页
        ENUM_CHANNEL_ENTER_NEXT_PLAY_LIST = 77;//下一场节目单引导进房
        ENUM_CHANNEL_ENTER_PIA_AGGREGATE_HOME = 78; // pia戏聚合页-我的房间
        ENUM_CHANNEL_ENTER_PIA_DRAMA_LIST_PLAYING = 79; //剧本库pia戏中
        ENUM_CHANNEL_ENTER_PIA_AGG_PAGE_FAST =  80; // pia戏聚合页快速进房
        ENUM_CHANNEL_ENTER_PIA_QUALITY_PLAYHOUSE = 81; //pia戏聚合页优质剧场
        ENUM_CHANNEL_ENTER_PIA_REHEARSAL = 82; //pia戏聚合页排练厅
        ENUM_CHANNEL_ENTER_PIA_FORECAST = 83; // pia戏房预告位
        ENUM_CHANNEL_ENTER_LIVE_HIGH_QUALITY_RANK = 84; // 语音直播间优质歌手榜单

        ENUM_CHANNEL_ENTER_MUSIC_NEW_USER_QUICK_PLAY = 86; // 音乐素材新用户快速开玩页
        ENUM_CHANNEL_ENTER_RECOMMEND_SUB_TAB_BUTTON = 87; //推荐流子tab头像和按钮进房

        ENUM_CHANNEL_MAIN_PAGE_CHANNEL_RECOMMEND = 88; //专区主页房间流推荐tab
        ENUM_CHANNEL_MAIN_PAGE_MY_CHANNEL =    89; //专区主页我的房间
        ENUM_CHANNEL_MAIN_PAGE_HELP_FIND_CHANNEL = 90;            //专区主页帮你找房

        ENUM_CHANNEL_ENTER_ACTIVITY_DEFAULT = 91; // 活动页默认进房来源

        ENUM_CHANNEL_ENTER_SCENARIO_DETAIL = 92; // 剧本详情页进房来源

        ENUM_CHANNEL_ENTER_MINI_GAME_WEB = 93; //  从小游戏WEB进入

        ENUM_CHANNEL_ENTER_MYSTERY_HOME_QUICK_MATCH = 94; //迷境首页快速匹配进房

        ENUM_CHANNEL_ENTER_MYSTERY_HOME_CONTINUE_PLAY = 95; //迷境首页继续通关进房

        ENUM_CHANNEL_ENTER_MYSTERY_RIGHT_CORNER = 96; //迷境首页右上角进房

        ENUM_CHANNEL_ENTER_MYSTERY_CHANNEL_LIST = 97; //迷境首页房间列表进房

        ENUM_CHANNEL_ENTER_GAME_RACE_ACTIVITY_PAGE = 98; //赛事活动页进房

        ENUM_CHANNEL_ENTER_HOME_FOLLOW_LAST_PLAY_LIST = 99; //首页跟随进房的最近玩过列表进房

        ENUM_CHANNEL_ENTER_PGC_CHANNEL_PK = 100; //PGC 跨房PK房主头像进房

        ENUM_CHANNEL_ENTER_SCHOOL_STUDY_ROOM_PAGE = 101; //校园自习赛活动页进房

        ENUM_CHANNEL_ENTER_MYSTERY_HOME_SCENARIO_INTRODUCE = 102;//102-谜境首页剧本介绍进房

        ENUM_CHANNEL_ENTER_AI_SINGING_GUIDE = 103; //AI说唱模拟器引导弹窗进房

        ENUM_CHANNEL_ENTER_EXTERNAL_SHARE_PAGE = 104; //通过房间端外分享页进房

        ENUM_CHANNEL_ENTER_DRAMA_RANKING_LIST = 105; //-剧本库排行榜进房
        ENUM_CHANNEL_ENTER_ASSISTANT_PUSH = 106 ;//-助手推送进房
        ENUM_CHANNEL_ENTER_LOGIN_TIPS_SOURCE = 107; //-登录弹窗资源位
        ENUM_CHANNEL_ENTER_GAME_TAB_ADVERTISE = 108 ; //-开黑tab广告位
        ENUM_CHANNEL_ENTER_GAME_LIST_SOURCE_CARD = 109; //-开黑列表资源位卡片页
        ENUM_CHANNEL_ENTER_MYSTERY_ACTIVE_CENTER_HALF_PAGE = 110; //-谜境活动中心半屏页

        ENUM_CHANNEL_ENTER_MYSTERY_DOUBLE_PEOPLE_ACTIVE = 111 ;//-谜境双人合集活动进房

        ENUM_CHANNEL_ENTER_MELEE_SDK = 112; //-团战的SDK拉起进房

        ENUM_CHANNEL_ENTER_MYSTERY_HOME_SCREEN = 113; // 谜境首页半屏
        ENUM_CHANNEL_ENTER_MYSTERY_CHANNEL_SCREEN = 114; // 谜境密逃房半屏
        ENUM_CHANNEL_ENTER_MYSTERY_HOME_CAROUSEL = 115; // 谜境首页剧本推荐用户（用户头像）

        ENUM_CHANNEL_ENTER_PC_IS_RAP_QUICK = 116; // PC就是说唱快速进房

        ENUM_CHANNEL_MYSTERY_HOME_SCENARIO_CORNER_CREATE = 117; // 谜境首页剧本右下角创建房间
        ENUM_CHANNEL_MYSTERY_OB_DIRECT_JUMP = 118; // 密室逃脱OB直接跳转进房

        ENUM_CHANNEL_ENTER_DEEPLINK = 119; //deeplink相关进房

        ENUM_CHANNEL_ENTER_FUN_PAGE_CARD_RECOMMENDED_LIST = 120; // 娱乐频道页的天选之人列表进房
        ENUM_CHANNEL_ENTER_FUN_PAGE_CARD_LOTTERY_HALF_PAGE = 121; // 娱乐频道页的(正在抽奖的房间)半屏页进房
        ENUM_CHANNEL_ENTER_ROOMER_NOTIFICATION_INSIDE = 122; // 端内房客进房通知进房
        ENUM_CHANNEL_ENTER_ROOMER_NOTIFICATION_OUTSIDE = 123; // 端外房客进房通知推送进房

        ENUM_CHANNEL_ENTER_HUANYOU_BOTTOM_NAVIGATION_BAR_MY_ROOM = 124 ; //-欢游底部导航栏我的房间半屏页
        ENUM_CHANNEL_ENTER_MYSTERY_OFFLINE_PUSH_RECALL = 125 ; //-谜境离线推送召回用户进房

        ENUM_CHANNEL_ENTER_CPL_KEY_SEARCH_ENTER = 126 ; // CPL关键词搜索房间进房

        ENUM_CHANNEL_ENTER_RECOMMEND_TAB_MUSIC_NEST_PAGE = 127;  //-广场推荐tab乐窝卡片页进房
        ENUM_CHANNEL_ENTER_HOBBY_TAB_MY_CHANNEL_PAGE = 128;      //-广场兴趣交流tab我的房间进房
        ENUM_CHANNEL_ENTER_HOBBY_TAB_CHANNEL_LIST_PAGE = 129;    //-广场兴趣交流tab房间列表进房
        ENUM_CHANNEL_ENTER_MIJING_DREAMLAND_ACTIVITY = 130;      //-谜境蚀梦活动进房
        ENUM_CHANNEL_ENTER_HOBBY_TAB_NEST_CARD_PAGE = 131;  //广场兴趣交流tab乐窝卡片页进房
        ENUM_CHANNEL_ENTER_MIJING_PIECING_GROUP = 132; // 谜境拼场通知


        ENUM_CHANNEL_ENTER_HOT_MINIGAME = 133; //休闲专区-大家都在玩快速进房
        ENUM_CHANNEL_ENTER_QUICK_MINIGAME = 134; //休闲专区快速匹配
        ENUM_CHANNEL_ENTER_PLAY_QUESTION_FORM_TEAM = 135; //135-问题弹窗发起组队
        ENUM_CHANNEL_ENTER_PLAY_QUESTION_PUBLISH = 136; //问题弹窗引导创建房间进房

        ENUM_CHANNEL_ENTER_MIJING_PLAYWITH_HOME_PAGE_RECOMMENDED = 137;   //-谜境首页找人玩推荐完本房间进房
        ENUM_CHANNEL_ENTER_MIJING_PLAYWITH_HALF_SCREEN_RECOMMENDED = 138; //-谜境找人玩列表半屏推荐完本房间进房
        ENUM_CHANNEL_ENTER_POPUP_FULL_TIPS = 139;                         //-房间满人提示弹窗进房（进房围观）
        ENUM_CHANNEL_ENTER_MT_ACTIVITY_NIGHT_PLAN = 140;                  //-情绪小酒馆活动进房(短信)
        ENUM_CHANNEL_ENTER_MT_SETTLE_PAGE_COMPLETE = 141;                         //谜境我页结算页去完本进房
        ENUM_CHANNEL_ENTER_MT_ACTIVITY_NIGHT_PLAN_WEB = 142;              //-情绪小酒馆活动页跳转进房(H5)
        ENUM_CHANNEL_ENTER_MJ_GAME_QINGGUAN_PARTNER_ACTIVITY_PAGE = 143;  //-143-谜境情棺密逃搭子活动-点击进房
        ENUM_CHANNEL_ENTER_MJ_GAME_QINGGUAN_PARTNER_LIST_PAGE = 144;      //-144-谜境情棺密逃搭子活动-点击搭子列表进房
        ENUM_CHANNEL_ENTER_MJ_QUICK_MATCH_HOME_PAGE_DRAMA = 145;          //-谜境首页剧本右下角快速匹配
        ENUM_CHANNEL_ENTER_MJ_QUICK_MATCH_HALF_SCREEN_CARD = 146;         //-谜境房间快速匹配半屏卡片进房
        ENUM_CHANNEL_ENTER_MJ_DRAMA_PROMOTION_ACTIVITY = 147;             //-谜境血色蔷薇等剧本推广活动进房
        ENUM_CHANNEL_ENTER_MSG_MAIN_PAGE_FOLLOW_RESOURCE = 148;              //148-消息主页跟随资源位
        ENUM_CHANNEL_ENTER_MJ_DRAMA_RECOMMEND_QUIT_SCENE = 149;           //-谜境房间内退房时-剧本推荐进房
        ENUM_CHANNEL_ENTER_MJ_CHAMBER_PERSONALITY_PARTNER_MATCH = 150;    //-谜境密室人格测试搭子匹配引导进房
        ENUM_CHANNEL_ENTER_MJ_USE_COUPON = 151;                           //-谜境首页优惠券“去使用”入口

        ENUM_CHANNEL_ENTER_PGC_TICKET_POPUP_FIRST_PAGE = 152;   // pgc体验券首页弹窗跳转入口
        ENUM_CHANNEL_ENTER_PGC_TICKET_IM = 153;   // pgc体验券im推送跳转入口
        ENUM_CHANNEL_ENTER_PGC_TICKET_CHANNEl = 154;   // pgc体验券房间内跳转入口

        ENUM_CHANNEL_ENTER_GAME_ZONE_QUICK_MATCH_PAGE = 155;              //6.30.0-游戏专区速配队友页进房
        ENUM_CHANNEL_ENTER_COMMUNITY_CHANNEL_SWITCH = 156;                //6.30.0-社群房间切换房间
        ENUM_CHANNEL_ENTER_MJ_ACTIVITY_TERROR_THEME = 157;                //6.30.0-谜境恐怖主题元素活动进房
        ENUM_CHANNEL_ENTER_APP_SHORT_LINK_TRIGGER_QUICK_MATCH_PAGE = 158; //6.30.0-客户端快速匹配短链拉起匹配页进房
        ENUM_CHANNEL_ENTER_MiNI_GAME_CHANNEL_LIST_HOME_PAGE = 159;        //-小游戏房间列表首页
        ENUM_CHANNEL_ENTER_MSG_TAB_MY_COMMUNITY = 160;                    //-消息tab我的社团点击进房
        ENUM_CHANNEL_ENTER_DASHEN_TICKET_MAIN_PAGE = 161;                 //-大神带飞券首页弹窗
        ENUM_CHANNEL_ENTER_USING_PACKAGE_DASHEN_TICKET = 162;             //-用户背包大神带飞券点击使用
        ENUM_CHANNEL_ENTER_KH_COMMUNITY_MAIN_PAGE_MY_CHANNEL = 163;       //-开黑首页-群聊派对-我的房间
        ENUM_CHANNEL_ENTER_KH_COMMUNITY_MAIN_PAGE_MUSIC_NEST_CARD = 164;  //-开黑首页-群聊派对-乐窝卡片
        ENUM_CHANNEL_ENTER_KH_COMMUNITY_MAIN_PAGE_CHANNEL_LIST = 165;     //-开黑首页-群聊派对-房间列表进房
        ENUM_CHANNEL_ENTER_MJ_DRAMA_DETAIL_PAGE_CHANNEL_SHARE = 166;      //谜境1.33.0-剧本详情页-剧本价格-进房分享
        ENUM_CHANNEL_ENTER_MJ_DRAMA_DETAIL_PAGE_BOTTOM = 167;             //谜境1.33.0-剧本详情页-底部去开玩进房
        ENUM_CHANNEL_ENTER_MJ_DRAMA_DETAIL_PAGE_PLAY_PUSH = 168;          //谜境1.33.0-剧本详情页-找人玩-点击进房
        ENUM_CHANNEL_ENTER_MJ_DRAMA_DETAIL_PAGE_PLAY_HALF_SCREEN = 169;   //谜境1.33.0-剧本详情页-找人玩-半屏进房
        ENUM_CHANNEL_ENTER_MJ_MAIN_PAGE_HOT_CHAMBER = 170;                //谜境1.33.0-首页-最热密室-去开玩进房
        ENUM_CHANNEL_ENTER_MJ_MAIN_PAGE_MY_CHAMBER_RANKS_ICON = 171;      //谜境1.33.0-首页-我的密逃小队-用户头像点击跟随
        ENUM_CHANNEL_ENTER_MJ_MAIN_PAGE_MY_CHAMBER_RANKS_MINE = 172;      //谜境1.33.0-首页-我的密逃小队-我的头像点击进房

        ENUM_CHANNEL_ENTER_KH_POST_DYNAMIC_SECTION = 173;                 //6.34-开黑专区-动态板块进房（废弃）
        ENUM_CHANNEL_ENTER_MUSIC_TYPE_CHANNEL_RECOMMEND_PAGE = 174;       //6.34-音乐品类房退房-推荐房间列表进房

        ENUM_CHANNEL_ENTER_MJ_MAIN_PAGE_ZONE_DRAMA_DETAIL_PAGE = 175;     //6.35-谜境首页-豆腐块专区-剧本详情进房
        ENUM_CHANNEL_ENTER_MJ_FRIEND_LIST_FOLLOW = 176;                   //6.35-谜境房间-当前好友列表跟随进房
        ENUM_CHANNEL_ENTER_COMMUNITY_PAGE_ACTIVE_RECOMMEND = 177;         //6.35-我的社团页-推荐活跃社团

        ENUM_CHANNEL_ENTER_MJ_ZHIJIAYI_APPOINTENT = 178;                  //6.38.0-谜境-纸嫁衣上线活动-立即探索
        ENUM_CHANNEL_ENTER_COMMUNITY_HALF_SCREEN_TOP = 179;               //6.38.0-社群群聊-活跃半屏顶部进房
        ENUM_CHANNEL_ENTER_COMMUNITY_NOTICE_CARD = 180;                   //6.38.0-社群通告-通告卡片进房
        ENUM_CHANNEL_ENTER_GAME_PAL_STEPPING = 181;                       //6.38.0-游戏搭子-从游戏搭子列表踩房进入
        ENUM_CHANNEL_ENTER_GAME_PAL_STEPPING_ZONE = 182;                  //6.38.0-游戏搭子-游戏专区综合大厅搭子卡片踩房
        ENUM_CHANNEL_ENTER_GAME_PAL_STEPPING_DISCOVER = 183;              //6.38.0-游戏搭子-房间找搭子页踩房

        ENUM_CHANNEL_ENTER_MJ_CHAMBER_MATCH_SUCCESS_PAGE = 184;           //谜境1.39.0-密逃搭子匹配成功页-去ta的房间
        ENUM_CHANNEL_ENTER_MINI_GAME_PURCHASE = 185;                      //小游戏买量用户自动进房

        ENUM_CHANNEL_ENTER_COMMUNITY_QUICK_JOIN_CHANNEL = 186;            //群聊派对-快速进房
        ENUM_CHANNEL_ENTER_COMMUNITY_MY_CHANNEL = 187;                    //群聊派对-我的房间进房

        ENUM_CHANNEL_ENTER_HOT_CHAT_CHALLENGE_DETAIL_PAGE = 188;          //6.42.0-房间-热聊挑战详情页
        ENUM_CHANNEL_ENTER_MJ_QUICK_MATCH_HALF_SCREEN_MATCH_USER = 189;   //6.42.0-谜境房间-快速匹配半屏匹配ta

        ENUM_CHANNEL_ENTER_GAME_TEAM_HALF_SCREEN_ROOM_LIST = 190;         //6.43.0-小游戏房间组队半屏房间列表
        ENUM_CHANNEL_ENTER_GAME_AREA_IM_CHANNEL_LINK = 191;               //6.43.0-通过游戏专区消息聊天中的房间链接进房
        ENUM_CHANNEL_ENTER_CHANNEL_SMALL_ENVELOPE_IM_CHANNEL_LINK = 192;  //6.43.0-房间小信封消息聊天中的房间链接进房

        ENUM_CHANNEL_ENTER_SEARCH_RES_USER_MAIN_PAGE_FOLLOW = 193;        //-搜索玩伴进入用户个人中心页跟随进房
        ENUM_CHANNEL_ENTER_SEARCH_POST_RES_USER_MAIN_PAGE_FOLLOW = 194;   //-搜索帖子进入用户个人中心页跟随进房

        ENUM_CHANNEL_ENTER_ACTIVITY_CENTER_PAGE = 195;                    //-活动中心进房
        ENUM_CHANNEL_ENTER_REVENUE_ACTIVITY_PAGE = 196;                   //-营收线的活动页进房（由运营判断使用）

        //不要乱加好吧，从上面最后一个值往下加
        ENUM_CHANNEL_ENTER_MIX_HOME_PAGE = 197;//推荐-改版首页
        ENUM_CHANNEL_ENTER_GAME_ZONE = 198;//推荐-开黑游戏专区
        ENUM_CHANNEL_ENTER_MINI_GAME_ARENA = 199;//推荐-小游戏专区

        //中间还有一堆值，为啥就从200开始了呢？？
        ENUM_CHANNEL_ENTER_TMP_ACTIVITY = 200;//推荐-临时活动页来源
        ENUM_CHANNEL_ENTER_MUSE_POST_INTERACTIVE_MSG = 201; // 碎碎念助手消息
        ENUM_CHANNEL_ENTER_PERFECT_MATCH_SUCCESS = 202; // 营收互动-天配匹配成功

        CHANNEL_LIVE_MULTI_PK_STATUS_MSG = 203;     // 定义错了，废弃，协议规范不能删除，直播间多人pk状态推送 see MultiPkInfo

        ENUM_CHANNEL_ENTER_RECOMMEND_SQUARE_TOP_RESOURCE_POS = 204; // 广场推荐流顶部资源位
        ENUM_CHANNEL_ENTER_OFFLINE_PUSH = 205;                      // 离线push进房
        ENUM_CHANNEL_ENTER_HOME_PAGE_QUICK_MATCH = 206; // 新版首页快速匹配进房

        ENUM_CHANNEL_ENTER_HOUR_MEMBER_RANK_LIST = 207; // 小时成员榜进房

        ENUM_CHANNEL_ENTER_GAME_INVITE_STRANGER_ENTER_ROOM = 208; // 开黑邀请陌生人进房来源
        
        ENUM_CHANNEL_ENTER_MT_COMMUNITY_RANK_LIST = 209; // MT社群榜单-列表及头像进房来源

        //消息主页跟随资源位电竞房
        ENUM_CHANNEL_ENTER_MSG_MAIN_PAGE_FOLLOW_RESOURCE_ESPORTS = 210;

        ENUM_CHANNEL_ENTER_UGC_RECOMMEND_REVENUE_ROOM = 211;  // 首页推荐进营收房间
        ENUM_CHANNEL_ENTER_UGC_KING_REVENUE_ROOM = 212; // 首页王者进营收房

        ENUM_CHANNEL_ENTER_MT_INVITE_NOT_HOLD_MIC_USER = 213; // 主线mt-邀请非上麦用户接受邀请进房

        ENUM_CHANNEL_TOP_OVER_LAY = 214;  // 房间顶部浮窗进房

        ENUM_CHANNEL_ENTER_MINI_GAME_H5_PAGE_MATCH = 215;  // 小游戏H5页面匹配进房

        ENUM_CHANNEL_ENTER_GAME_INVITE_STRANGER_ENTER_ROOM_V2 = 216; // 6.52.5-开黑邀请陌生人第二期进房来源

        ENUM_CHANNEL_ENTER_GAME_HALL_INVITE = 217; // 组队大厅邀请进房来源
        ENUM_CHANNEL_ENTER_MT_COMMUNITY_ACTIVE_USER_AVATAR = 218; // 6.54.5-社团群聊-活跃用户头像进房
        ENUM_CHANNEL_ENTER_FLOATING_AVATAR_AREA_FOLLOW = 219; // 新版跟随进房浮窗头像区域进房

        ENUM_CHANNEL_ENTER_HOME_PAGE_TAG_QUICK_MATCH_BUBBLE = 220; // 6.57.5-首页标签式快速匹配气泡进房
        ENUM_CHANNEL_ENTER_POTENTIAL_NON_ENTER_ROOM_PUSH = 221; // 潜在非进房用户push跳转进房
        ENUM_CHANNEL_ENTER_IOP_RECESSION_PERIOD_FOLLOW_USER = 222; // 智能运营的衰退期关注用户进房

        ENUM_CHANNEL_ENTER_CALL_COMPLETE_GUIDE_POPUP_MY_CHANNEL = 223; // 喊话发布完成引导弹窗-回我的房间进房
        ENUM_CHANNEL_ENTER_CUSTOMER_CALL_DETAIL_PAGE_TARGET_CHANNEL = 224; // 客态喊话详情页-进ta房间进房
        ENUM_CHANNEL_ENTER_HOME_FOLLOW_CALL = 225; // 首页跟随进房-喊话点击接受进房
        ENUM_CHANNEL_ENTER_HOME_FOLLOW_CALL_AVATAR = 226; //首页跟随进房-喊话点击头像进房
        ENUM_CHANNEL_ENTER_ROOM_NOTIFY_PUSH = 227; // 6.59.5-收到进房提醒-点击弹窗或推送进房
        ENUM_CHANNEL_ENTER_PC_FAST_HOME_PAGE = 228; // PC极速版首页进房
        ENUM_CHANNEL_ENTER_PC_FAST_HALL = 229; // PC极速版大厅进房
        ENUM_CHANNEL_ENTER_WEDDING_HALL = 230; // 婚礼房仪式大厅进房
        ENUM_CHANNEL_ENTER_WEDDING_START_NOTIFY_IM = 231; // 婚礼开始IM通知进房
        ENUM_CHANNEL_ENTER_WEDDING_START_NOTIFY_GLOBAL_POPUP_WINDOW = 232; // 婚礼开始全局弹窗进房
        ENUM_CHANNEL_ENTER_THIRDPART_GAME_ENTRY_ZDXX = 233;     // 第三方小游戏跳转/进房

        ENUM_CHANNEL_ENTER_PC_FAST_SEARCH_RESULT_CHANNEL = 234; // PC极速版搜索结果房间进房
        ENUM_CHANNEL_ENTER_PC_FAST_SEARCH_RESULT_USER_PLAYMATE = 235; // PC极速版搜索结果玩伴进房点击
        ENUM_CHANNEL_ENTER_PC_FAST_HOME_PAGE_RECOMMEND = 236; // PC极速版开黑首页游戏推荐悬停点击
        ENUM_CHANNEL_ENTER_PC_FAST_COLLECT_CHANNEL = 237; // PC极速版收藏房间进房点击
        ENUM_CHANNEL_ENTER_PC_FAST_ENTER_HISTORY = 238; // PC极速版历史进房点击
        ENUM_CHANNEL_ENTER_PC_FAST_HALL_QUICK_MATCH = 239; // PC极速版游戏大厅快速匹配进房
        ENUM_CHANNEL_ENTER_PC_FAST_IM_PAGE_FOLLOW = 240; // PC极速版消息列表IM跟随进房
        ENUM_CHANNEL_ENTER_PC_FAST_CONTACT_LIST = 241; // PC极速版联系人列表去踩房
        ENUM_CHANNEL_ENTER_PC_FAST_PERSONAL_DATA_CARD = 242; // PC极速版个人卡片
        ENUM_CHANNEL_ENTER_PC_FAST_FRIEND_CHAT_PAGE_FOLLOW = 243; // PC极速版好友聊天页跟随进房
        ENUM_CHANNEL_ENTER_WEDDING_IM_QUICK_ENTRY = 244; // 婚礼IM快速入口进房
        ENUM_CHANNEL_ENTER_WEALTH_GOD_FIXED_ENTRY = 245; // 财神降临固定入口进房
        ENUM_CHANNEL_ENTER_WEALTH_GOD_FLOATING_ENTRY = 246; // 财神降临悬浮入口进房
    }

    required BaseReq base_req = 1;
    required uint32 channel_id = 2;

    // (可以不用填) 用于支持频道成员列表按照管理员级别排序
    // 如果没有表示该用户是普通成员
    optional ChannelPermissionInfo permission_info = 3;

    optional string passwd = 4; // 密码

    // 房间的显示ID, 如果 channel_id 为0 可以根据channel_show_id进入房间
    optional uint32 channel_show_id = 5;

    // 如果是跟随某人进房间 这里填写跟随的目标用户的UID
    optional uint32 follow_friend_uid = 6;

    optional uint32 enter_source = 7; //  see EChannelEnterSource

    optional string knock_door_sign = 8; // 敲门进房带的加密签名信息

    optional string enter_source_second = 9; // 二级子来源

    optional string recommend_intro = 10;    // 战歌主播推荐位进房, 需要填写主播的推荐简介

    optional string enter_source_trace_id = 11; // 进房来源追踪id，用于推荐业务等

    optional string channel_view_id = 12;  //新的房间显示id
}

message ChannelLayoutInfo {
    required uint32 layout_type = 1; //布局类型，见channel-scheme_.proto
    optional uint32 fallback_layout_type = 2;   //如果旧版客户端不认识layout_type，则用该值做兜底逻辑
    optional uint32 cur_mic_size = 3;           //当前的麦位数
}
message ChannelMicAudioInfo {
    required uint32 mic_audio_type = 1;   //枚举：高音质，低音质,KTV等，见channel-scheme_.proto
    optional uint32 special_opt_switch_bitmap = 2; // 特殊的開關類型 ESpecialOptSwitchBitMap 這是個bitmap 哦！！！！
    //6.59.5版本动态配置音频参数需求新增字段
    map<uint32, string> mic_audio_sdk_info = 3; //key为客户端类型，see ga.TT_CLIENT_TYPE  value为sdk配置信息，json格式,客户端自己解析
    optional uint64  mic_audio_sdk_info_update_ms = 4; //sdk配置信息更新时间,毫秒时间戳
    optional uint32 high_bit_rate = 5; // 优先高码率，单位kbps，非零时优先使用该码率
}
message UKWEnterUserInfo {
    required uint32 uid = 1;
    required string account = 2; //账号
    required string nickname = 3; // 昵称
}
message ChannelEnterResp {
    required BaseResp base_resp = 1;
    required ChannelDetailInfo channel_info =
            2; // 此时返回的房间详细信息里 创建者的信息包括昵称和帐号信息均为空
    optional uint32 channel_id = 3;
    optional uint32 user_confirm_status =
            4; // MemberConfirmStatus 用于房间召集状态
    optional uint32 sdk_version =
            5; // sdk版本 默认不填为空 1表示旧版本 2表示新版本(废弃 不能用了)
    optional uint32 server_time = 6; // 32bit 秒级 服务器时间

    optional bytes pb_opt_content =
            7; // 附加内容 （定义在channel_opt_.proto 的 ChannelEnterOpt）
    optional bytes enter_msg = 8;   // 附加文本
    optional bytes welcome_msg = 9; // 欢迎语文本

    optional uint32 special_opt_switch_bitmap =
            10; // 特殊的開關類型 ESpecialOptSwitchBitMap 這是個bitmap 哦！！！！
    optional uint32 enter_source = 11;    //   see EChannelEnterSource
    optional bool is_live_start = 12;     //   直播房是否开播中
    optional bool recommend_channel = 13; // 房间是否为推荐房

    repeated GRPCPushProxy grpc_push_proxies = 14; // 有值则启用GRPC独立推送通道

    optional uint32 overflow_warning = 15; // 人数负载告警, see EChannelOverflowWarningType

    optional ChannelLayoutInfo layout_info = 16;
    optional ChannelMicAudioInfo mic_audio_info = 17;

    optional UKWEnterUserInfo ukw_enter_user_info = 18;         //非神秘人信息为空

    optional bool audio_token_switch = 19;   //true才进行token校验
    optional bool push_token_switch = 20;    //true才进行token校验
}

// 用户的PC助手进入房间 （前提是用户已经进入了某个房间）
message ChannelPcHelperEnterReq {required BaseReq base_req = 1;}

message ChannelPcHelperEnterResp {
    required BaseResp base_resp = 1;
    required uint32 channel_id = 2;
}

// PC助手 检查用户是否在麦位上以及麦位状态
message ChannelPcHelperMicCheckReq {
    required BaseReq base_req = 1;
    required uint32 channel_id = 2;
}

message ChannelPcHelperMicCheckResp {
    required BaseResp base_resp = 1;
    optional MicrSpace mic_info = 2;
    optional uint32 mic_mode = 3; // 麦位模式 see EChannelMicMode
}

// 退出频道
message ChannelQuitReq {
    required BaseReq base_req = 1;
    required uint32 channel_id = 2;
    optional bool need_collected = 3;
}

message ChannelQuitResp {
    required BaseResp base_resp = 1;
    required uint32 channel_id = 2;
}

enum ChannelRequestType {
    REQUEST_HOME_PAGE = 1; // 公会首页的 普通房间列表
    REQUEST_DETAIL = 2;    // 公会完整的 普通房间列表
    REQUEST_DETAIL_ALLMEMBERSIZE = 3; // 公会完整的 普通房间列表的全部成员的总数
    REQUEST_GUILD_PUB_FUN_ALL = 4; // 公会全部公开娱乐房列表
}

// 获取频道列表（仅用于公会房间列表获取）
message GetChannelListReq {
    required BaseReq base_req = 1;
    required uint32 request_type = 2; // see ChannelRequestType
}

message GetChannelListResp {
    required BaseResp base_resp = 1;
    required uint32 request_type = 2; // see enum ChannelRequestType

    // 频道列表, 当request_type = REQUEST_DETAIL_ALLMEMBERSIZE时该值为空
    repeated ChannelInfo channel_list = 3;

    // 全部频道列表的数量,
    // 当request_type = REQUEST_HOME_PAGE 和 REQUEST_DETAIL_ALLMEMBERSIZE 时有效
    optional uint32 all_channel_count = 4;

    // 全部频道的全体成员数量,
    // 当request_type = REQUEST_DETAIL_ALLMEMBERSIZE时有效
    optional uint32 all_member_count = 5;

    // 公会公开娱乐房 的参数
    // 当前公会公开娱乐房剩余创建数量
    // 当request_type =REQUEST_GUILD_PUB_FUN_ALL 时有效
    optional uint32 guild_pub_remain = 6;

    // 公会公开娱乐房禁止创建的描述语句,
    // 当request_type = REQUEST_GUILD_PUB_FUN_ALL 且 guild_pub_remain = 0 时有效
    optional string guild_pub_band_text = 7;
}

// 获取频道详情
message GetChannelDetailReq {
    required BaseReq base_req = 1;
    required uint32 channel_id = 2;
    optional uint32 channel_show_id = 3; // 房间的显示ID, 如果 channel_id 为0
                                       // 可以根据channel_show_id 获取房间信息
}

message GetChannelDetailResp {
    required BaseResp base_resp = 1;
    required ChannelDetailInfo channel_info = 2;
}

// 获取频道成员列表（现为房间在线成员今日消费榜）
message ChannelGetMemberListReq {
    required BaseReq base_req = 1;
    required uint32 channel_id = 2;
    required uint32 begin_id = 3; // 开始查询的索引编号 用于翻页 第一页可以填0
    required uint32 req_cnt = 4; // 每次获取列表最多需要多少条 一般填50
}

message ChannelGetMemberListResp {
    required BaseResp base_resp = 1;
    required uint32 channel_id = 2;
    repeated ChannelMemberInfo channel_member_list = 3; // 成员列表(按排名排序)
    required uint32 begin_id = 4; // 本次返回的成员列表的起始索引序号
    required uint32 all_member_cnt = 5; // 当前房间内所有成员数目

    optional ChannelMemberInfo my_info = 6; // 我的信息
    optional uint32 view_cnt = 7; // 榜单上展示的个数

    optional KnightNameplateInfo knight_nameplate_info = 8; //骑士铭牌 url 目前只在直播房返回此字段
}

// 获得麦下用户列表
message ChannelGetUnderTheMicroListReq {
    required BaseReq base_req = 1;
    required uint32 channel_id = 2;
}

message ChannelGetUnderTheMicroListResp {
    required BaseResp base_resp = 1;
    required uint32 channel_id = 2;
    repeated ChannelMemberInfo channel_member_list = 3; // ChannelMemberInfo only fill 1/2/3/4/9/16
    required uint32 curr_member_total = 4; // 房间当前总在线人数
}
// 获得房间麦上用户游戏段位
message ChannelGetMicroUserGameTagReq {
    required BaseReq base_req = 1;
    required uint32 channel_id = 2;

    // uid_list 非空，则不会查当前麦上人的卡片，uid_list
    // 为空，则查询当前麦上人卡片,uid_list 限制最大查 8 个
    repeated uint32 uid_list = 3;
}

message ChannelMicroGameCardOpt {
    required string opt_name = 1;
    required uint32 opt_id = 2;
    required string url = 3;
    repeated string value_list = 4;
}
message ChannelMicroUserGameTag {
    required uint32 uid = 1;
    required string game_name = 2;            // 当前房间玩法游戏名
    optional string game_dan = 3;             // 用户该游戏卡片中的段位
    optional string game_nickname = 4;        // 用户该游戏昵称
    optional string game_area = 5;            // 用户该游戏区服
    optional string game_dan_url_for_mic = 6; // 麦下展示的游戏段位图
    optional string account = 7;
    optional string nickname = 8;
    optional int32 sex = 9;
    optional string game_role = 10; // 游戏角色
    repeated string game_position = 11;//玩哪个游戏位置
    repeated ChannelMicroGameCardOpt game_card_opt_list = 12; //房间内展示哪些游戏卡选项  5.9.0后麦位下展示的游戏卡选项只需该字段
}

message ChannelGetMicroUserGameTagResp {
    required BaseResp base_resp = 1;
    repeated ChannelMicroUserGameTag game_tag_list = 2; // 麦上用户的游戏卡片信息
}

// 房间历史消费榜
message ChannelGetConsumTopNReq {
    required BaseReq base_req = 1;
    required uint32 channel_id = 2;
    required uint32 begin_id = 3; // 开始查询的索引编号 用于翻页 第一页可以填0
    required uint32 req_cnt = 4; // 每次获取列表最多需要多少条 一般填50

    optional bool is_show_hidden = 5; // 显示隐藏用户，目前只支持自动隐藏用户
}

message ChannelGetConsumTopNResp {
    required BaseResp base_resp = 1;
    required uint32 channel_id = 2;
    required uint32 begin_id = 3; // 本次返回的成员列表的起始索引序号
    repeated ChannelMemberInfo member_list = 4; //  列表(按排名排序)

    optional ChannelMemberInfo my_info = 5; // 我的信息
    optional uint32 view_cnt = 6; // 榜单上展示的个数

    optional bool is_can_hide_consume = 7; // 是否可隐藏消费
    optional bool is_hidden_consume = 8; // 是否隐藏消费

    optional bool is_auto_hidden_consume = 9; // 是否自动隐藏消费

    optional KnightNameplateInfo knight_nameplate_info = 10; //骑士铭牌 url 目前只在直播房返回此字段
}

// 隐藏/显示消费数据
message ChannelHideConsumeReq {
    required BaseReq base_req = 1;
    required uint32 channel_id = 2;
    required bool is_cancel = 3; // false 代表隐藏，true 代表取消隐藏
}

message ChannelHideConsumeResp {
    required BaseResp base_resp = 1;
}

// 获取用户房间vip等级统计
message ChannelMemberVipStatistics {
    required uint32 level = 1;  // 等级
    required uint32 count = 2; // 数量
}
message ChannelGetChannelMemberVipStatisticsReq {
    required BaseReq base_req = 1;
    optional uint32 uid = 2;
}
message ChannelGetChannelMemberVipStatisticsResp {
    required BaseResp base_resp = 1;
    repeated ChannelMemberVipStatistics statistics_list = 2; // 统计数组
    optional uint32 total_vip_count = 3; // 获得的 vip 总数
}

// 房间成员消费周榜
message ChannelGetWeekConsumeTopNReq {
    required BaseReq base_req = 1;
    required uint32 channel_id = 2;
    required uint32 begin_id = 3;  // 开始查询的索引编号 用于翻页 第一页可以填0
    required uint32 req_cnt = 4;  // 每次获取列表最多需要多少条 一般填50
}

message ChannelGetWeekConsumeTopNResp {
    required BaseResp base_resp = 1;
    required uint32 channel_id = 2;
    required uint32 begin_id = 3; // 本次返回的成员列表的起始索引序号
    repeated ChannelMemberInfo member_list = 4;         //  列表 (按排名排序)

    optional ChannelMemberInfo my_info = 5; // 我的信息
    optional uint32 view_cnt = 6; // 榜单上展示的个数

    optional KnightNameplateInfo knight_nameplate_info = 7; //骑士铭牌 url 目前只在直播房返回此字段
}

// 房间小时榜类型
enum ChannelHourRankType {
    TOTAL = 0;           // 小时总榜

    /* 其他的类型用2000以上的娱乐tag下的tag_id做类型值 */
}

// 房间小时榜成员信息
message ChannelHourRankInfo {
    required uint32 channel_id = 1;
    required uint32 channel_type = 2;   // see enum ChannelType
    required string channel_name = 3;
    required uint32 score = 4;          // 分数
    required uint32 rank = 5;           // 排名
    optional string channel_icon = 6;   // 房间图标的MD5

    optional uint32 guild_id = 7;   // 房间绑定的公会id（语音直播房除外）
    optional string guild_name = 8;    // 房间绑定的公会名字（语音直播房除外）
    optional uint32 tag_id = 9; // (仅在ChannelHourRankType 为 TOTAL 时有效)房间所在分类的tag_id

    /* 我的房间的排名信息相关*/
    optional uint32 d_value = 10; // 分数差值（当排名为第一名时d_value为与第二名的差值，否则d_value为与上一名的差值）

    /* 语音直播相关，仅当channel_type为RADIO_LIVE_CHANNEL_TYPE时有效 */
    optional bool is_living = 11;           // 是否正在直播
    optional string actor_nick_name = 12;   // 昵称
    optional string actor_account = 13;     // 账号
    optional uint32 actor_uid = 14;         // 直播者uid
    optional string actor_face_md5 = 15;    // 头像md5

    enum LivingStatus {
        Normal = 0;
        InPk = 1;     // pk中
    }
    optional uint32 living_status = 16; //pk状态(当is_living 为true有效) see enum LivingStatus

    optional bool is_hide_d_value = 17; // 隐藏差值，展示礼物值

    optional string lottery_cert = 18;  // 抽奖标识内容

    optional string game_cert_url = 19;  // 弹幕游戏标识

    optional ChannelHourRankAwardScore award_score = 20; // 奖励分数信息
    optional uint32 mic_id = 21; // 麦位
}

// 小时榜奖励分
message ChannelHourRankAwardScore {
    optional uint32 score = 1; // 奖励分数
    optional string source_text = 2; // 奖励来源
}

// 多维度榜单类型
enum MultiDimChannelRankType {
    MULTI_DIM_CHANNEL_RANK_TYPE_UNSPECIFIED = 0; // 默认总榜
    MULTI_DIM_CHANNEL_RANK_TYPE_MEMBER = 1; // 成员榜
    MULTI_DIM_CHANNEL_RANK_TYPE_NEW_STAR = 2; // 新星榜
}

// 多维度房间榜单
message MultiDimChannelRankTab {
    optional uint32 type = 1; // 榜单类型
    optional string name = 2; // 榜单名称
}

// 获取房间小时榜topN
message GetChannelHourRankTopNReq {
    required BaseReq base_req = 1;
    required uint32 rank_type = 2;       // see ChannelHourRankType
    optional uint32 channel_id = 3;      // 若当前在房间中，则填
    optional uint32 multi_dim_rank_type = 4; // 多维度榜单类型
}

message GetChannelHourRankTopNResp {
    required BaseResp base_resp = 1;
    repeated ChannelHourRankInfo rank_list = 2;
    required uint32 rank_view_cnt = 3;    // 榜上可展示的房间数量（后台可配）
    required uint32 goal_score = 4;    // 榜单第一名目标分数，第一名达到该分数在结算时会进行推送通知（后台可配）
    optional ChannelHourRankInfo my_rank_info = 5;  // 当前房间排名信息
    optional uint32 next_settle_ts = 6;    // 下一次结算时间戳

    repeated ChannelHourRankInfo last_top_rank_list = 7; // 上一小时的TopN
    repeated MultiDimChannelRankTab multi_dim_rank_tab_list = 8; // 多维度榜单tab
    optional bool is_member_rank = 22; // 是否是成员榜单，用于区分房间榜单
    optional bool is_hide_member_rank_d_value = 23; // 是否隐藏成员榜单的差值
}

// 通过房间id获取该房间小时榜排名信息
message GetChannelHourRankByIdReq {
    required BaseReq base_req = 1;
    required uint32 channel_id = 2;
    required uint32 rank_type = 3;       // see ChannelHourRankType
}

message GetChannelHourRankByIdResp {
    required BaseResp base_resp = 1;
    required ChannelHourRankInfo rank_info = 2;
    required uint32 rank_view_cnt = 3;          // 榜上可展示的房间数量（后台可配）
    required uint32 next_req_interval_sec = 4;  // 下次请求的间隔时间（后台可配）
}

// 欢游-获取房间小时榜单Top1列表
message GetChannelHourRankTop1ListRequest {
	required BaseReq base_req = 1;
}
message GetChannelHourRankTop1ListResponse {
	required BaseResp base_resp = 1;
	repeated ChannelHourRankTop1Info rank_list = 2;
}
message ChannelHourRankTop1Info {
	required uint32 tag_id = 1;
	required string tag_name = 2;
	required uint32 channel_id = 3; // 如果为0 就是没有房间上榜
	required string channel_name = 4;
	optional string channel_icon = 5; // 房间图标的MD5
}


// 频道禁言成员列表
message ChannelGetMutedMemberListReq {
    required BaseReq base_req = 1;
    required uint32 channel_id = 2;
}

message ChannelGetMutedMemberListResp {
    required BaseResp base_resp = 1;
    required uint32 channel_id = 2;
    repeated GenericMember member_list = 3;
}

// 禁言
message ChannelMemberMuteReq {
    required BaseReq base_req = 1;
    required uint32 channel_id = 2;
    repeated uint32 uid_list = 3;

    // permission_info 用户权限信息 现在没什么用了...
    required ChannelPermissionInfo permission_info = 4;
}

message ChannelMemberMuteResp {
    required BaseResp base_resp = 1;
    required uint32 channel_id = 2;
    repeated uint32 uid_list = 3;
}

// 解除禁言
message ChannelMemberUnmuteReq {
    required BaseReq base_req = 1;
    required uint32 channel_id = 2;
    repeated uint32 uid_list = 3;

    // permission_info 用户权限信息 现在没什么用了...
    required ChannelPermissionInfo permission_info = 4;
}

message ChannelMemberUnmuteResp {
    required BaseResp base_resp = 1;
    required uint32 channel_id = 2;
    repeated uint32 uid_list = 3;
}

// 上麦
message ChannelGetMicReq {
    required BaseReq base_req = 1;
    required uint32 channel_id = 2;

    // permission_info 用户权限信息 现在没什么用了...
    required ChannelPermissionInfo permission_info = 3;

    optional MicrSpace micr_info = 4; // 上麦的麦位信息 必填

    // 是否需要强行上麦 必须指定麦位信息 如果指定的麦位上有人则踢人下麦
    // 非管理员不允许强行上麦
    optional bool is_force = 5;

    optional string token = 6;   //上麦token，团战房间需要带token才能上麦
}

message ChannelGetMicResp {
    required BaseResp base_resp = 1;
    required uint32 channel_id = 2;
    optional MicrSpace micr_info = 3; // 上麦的麦位信息
    optional bool invisible = 4; //隐身上麦需要弹toast
    optional ChannelAudioTokenV2 channel_audio_token = 5 [deprecated = true]; // 已废弃，使用audio_token_data
    optional bytes audio_token_data = 6; // 音频token序列化后数据，see channel-audio-token_.proto AudioToken
}

// 下麦
message ChannelReleaseMicReq {
    required BaseReq base_req = 1;
    required uint32 channel_id = 2;

    // permission_info 用户权限信息 现在没什么用了...
    required ChannelPermissionInfo permission_info = 3;
}

message ChannelReleaseMicResp {
    required BaseResp base_resp = 1;
    required uint32 channel_id = 2;
}

// 房间玩法类型
enum ChannelPlayWayType {
    ChannelPlayWayType_INVALID = 0;
    ChannelPlayWayType_GAME = 1;
    ChannelPlayWayType_OTHER = 2;
}
// 获取麦列表
message ChannelGetMicListReq {
    required BaseReq base_req = 1;
    required uint32 channel_id = 2;
}

message ChannelGetMicListResp {
    required BaseResp base_resp = 1;
    required uint32 channel_id = 2;

    // 上麦的用户列表  (字段3.1废弃)
    repeated MicInfo mic_list = 3;

    // 麦位被关掉了几个(字段3.1废弃)
    optional uint32 mic_entry_closesize = 4;

    // 全体麦位信息列表 包括关闭的/没有人上的 3.1.0新增
    repeated MicrSpace all_micr_list = 5;
    optional uint32 mic_mode = 6;        // 当前房间麦模式
    optional uint64 server_time_ms = 7;  // 64bit 毫秒级 服务器时间
}

message QuickJoinChannelReq {
    required BaseReq base_req = 1;
    required uint32 channel_id = 2;
}

message QuickJoinChannelResp {
    required BaseResp base_resp = 1;
    required uint32 channel_id = 2;

    // sdk版本 默认不填为空 1表示旧版本 2表示新版本
    optional uint32 sdk_version = 3;

    repeated MicrSpace full_mic_list = 4; // 当前房间 只有完整的麦位的基础信息（快速进房命令不应该也不允许查询额外数据）
    optional uint32 mic_mode = 5;         // 当前房间麦模式
    optional bool is_live_start = 6;      // 当前房间直播是否开始

    repeated GRPCPushProxy grpc_push_proxies = 7; // 有值则启用GRPC独立推送通道

    optional ChannelLayoutInfo layout_info = 8;
    optional ChannelMicAudioInfo mic_audio_info = 9;

    optional bool audio_token_switch = 10;   //true才进行token校验
    optional bool push_token_switch = 11;    //true才进行token校验

    optional uint32 channel_msg_cur_seq_id = 12;

    optional uint32 mic_layout_template_id = 13; // 麦位布局模板id
}

// 将用户踢出频道
message KickoutChannelReq {
    enum EKickOutSOURCE {
        KICK_OUT_SOURCE_INVALID = 0;
        KICK_OUT_SOURCE_BATTLE_CHANNEL_CHANGE = 1;   //从团战房切到其他房间导致的踢人
    }
    required BaseReq base_req = 1;
    required uint32 channel_id = 2;
    required ChannelPermissionInfo permission_info = 3; // 执行操作的用户权限信息
    repeated uint32 targetuid_list = 4;
    optional bool is_weak_notify = 5; // 是否弱提醒    //废弃
    optional uint32 kick_out_source = 6;    //踢人来源
}

message KickoutChannelResp {
    required BaseResp base_resp = 1;
    required uint32 channel_id = 2;
    repeated uint32 tickout_list = 3;
}

// 关闭麦位
message CloseChannelMicEntryReq {
    required BaseReq base_req = 1;
    required uint32 channel_id = 2;

    // permission_info 执行操作的用户权限信息 [废弃字段]
    required ChannelPermissionInfo permission_info = 3;

    optional uint32 target_uid = 4; // 如果麦上有用户 需要将该用户踢出
    optional uint32 mic_pos_id = 5; // 被关闭的麦位编号 可以先填上 1-9
}

message CloseChannelMicEntryResp {
    required BaseResp base_resp = 1;
    required uint32 channel_id = 2;
    optional uint32 mic_pos_id = 3; // 被关闭的麦位编号 可以先填上
    optional uint32 mic_entry_closesize = 4; // 新增 当前已经有多少个麦位被关掉了
    optional uint32 target_uid = 5; // 如果麦上有用户 需要将该用户踢出
}

// 打开麦位
message OpenChannelMicEntryReq {
    required BaseReq base_req = 1;
    required uint32 channel_id = 2;

    // permission_info 执行操作的用户权限信息 [废弃字段]
    required ChannelPermissionInfo permission_info = 3;
    optional uint32 mic_pos_id = 4; // 被打开的麦位编号 可以先填上 1-9
}

message OpenChannelMicEntryResp {
    required BaseResp base_resp = 1;
    required uint32 channel_id = 2;
    optional uint32 mic_pos_id = 3; // 被打开的麦位编号 可以先填上
    optional uint32 mic_entry_closesize = 4; // 新增 当前还有多少个麦位被关掉了
}

// 设置麦位状态 比如打开/关闭/禁言 麦位 (可以取代上面的麦位开启和麦位关闭命令)
message SetChannelMicSpaceStatusReq {
    required BaseReq base_req = 1;
    required uint32 channel_id = 2;
    required MicrSpace target_mic_info = 3;
    repeated uint32 mic_id_list = 4; // 需要批量设置状态的麦位id列表（如果此列表不为空则优先使用批量模式，忽略target_mic_info）
    optional uint32 mic_state = 5; // 批量设置的状态（EMicrSpaceState 1 正常 2 禁用 3 不可发言）
}

message SetChannelMicSpaceStatusResp {
    required BaseResp base_resp = 1;
    required uint32 channel_id = 2;
    repeated MicrSpace full_mic_list = 3; // 操作完之后 完整的麦位信息
}

enum KickoutMicSceneType {
    KICKOUT_MIC_SCENE_TYPE_UNSPECIFIED = 0;
    KICKOUT_MIC_SCENE_TYPE_PUT_MYSELF_ON_MIC = 1; // 换我上麦
}

// 将用户踢下麦
message KickoutChannelMicReq {
    required BaseReq base_req = 1;
    required uint32 channel_id = 2;
    required ChannelPermissionInfo permission_info = 3; // 执行操作的用户权限信息
    repeated uint32 targetuid_list = 4;
    optional bool is_weak_notify = 5; // 是否弱提醒
    optional KickoutMicSceneType scene = 6; // 踢人场景
}

message KickoutChannelMicResp {
    required BaseResp base_resp = 1;
    required uint32 channel_id = 2;
    repeated uint32 tickout_list = 3; // 被踢下麦的用户UID列表
    repeated MicrSpace full_mic_list = 4; // 操作完之后 完整的全量麦位列表
}

// 切换房间麦模式
// 有麦模式(主席模式是有麦模式的特例 即全部麦位被锁上的麦模式) 或者 自由模式
// 或者 娱乐模式
message SetChannelMicModeReq {

    required BaseReq base_req = 1;
    required uint32 channel_id = 2;
    required uint32 mic_mode = 3; // 麦模式 see EChannelMicMode

    // 是否锁定所有的麦位 仅在 HAVE_MIC_SPACE_MODE 模式下有效
    optional bool is_disable_all_mic = 4;

    // 本人是否需要上麦 仅在 HAVE_MIC_SPACE_MODE 模式下有效
    optional bool is_need_hold_mic = 5;

    required ChannelPermissionInfo permission_info = 6; // 执行操作的用户权限信息
}

message SetChannelMicModeResp {
    required BaseResp base_resp = 1;
    required uint32 channel_id = 2;
    required uint32 mic_mode = 3; // 麦模式 see EChannelMicMode

    optional bool is_disable_all_mic =
            4; // 是否锁定所有的麦位 仅在 HAVE_MIC_SPACE_MODE 模式下有效
    optional bool is_need_hold_mic =
            5; // 本人是否需要上麦 仅在 HAVE_MIC_SPACE_MODE 模式下有效

    repeated MicrSpace full_mic_list = 6; // 操作完之后 完整的麦位信息
}

enum EChannelAdminRoleType {
    CHANNEL_ROLE_INVALID = 0;     // 无效
    CHANNEL_ROLE_OWNER = 1;       // 房主
    CHANNEL_ROLE_ADMIN = 2;       // 管理员
    CHANNEL_ROLE_NOMAL = 3;       // 普通用户
    CHANNEL_ROLE_SUPER_ADMIN = 4; // 超级管理员
}

// 获取我管理的房间列表
message GetUserAdminChannelListReq {
    required BaseReq base_req = 1;

    // 指定获取某种类型的管理列表 see EChannelAdminRoleType,
    // 如果为0表示获取所有有管理权限的房间
    required uint32 admin_role = 2;
}

message GetUserAdminChannelListResp {

    required BaseResp base_resp = 1;

    // 指定获取某种类型的管理列表 see EChannelAdminRoleType,
    // 如果为0表示获取所有有管理权限的房间
    required uint32 admin_role = 2;

    repeated ChannelInfo channel_list = 3; // 频道列表
}

//// 搜索房间
//message SearchChannelReq {
//  required BaseReq base_req = 1;
//  required string keyword = 2;
//
//  enum EKeywordResultType {
//    ENUM_KEYWORD_CHANNEL = 1;
//    ENUM_KEYWORD_TAG = 2;
//  };
//
//  optional uint32 keyword_type =
//      3; // SEE EKeywordResultType 表示搜索的结果是什么
//  optional uint32 page = 4;
//  optional uint32 page_count = 5;
//}
//
//message SearchChannelResp {
//  required BaseResp base_resp = 1;
//  required string keyword = 2;
//
//  // 搜索结果
//  repeated ChannelDetailInfo channel_list = 3; // 频道列表
//  repeated SCTagInfo sc_tag_list = 4;          // 房间标签列表
//
//  // 翻页属性
//  optional uint32 keyword_type = 5; // SEE EKeywordResultType
//  optional uint32 page = 6;
//  optional bool is_reach_end = 7;
//}

// 批量获取房间信息
message BatchGetChannelListReq {
    required BaseReq base_req = 1;
    repeated uint32 channel_id_list = 2;
}

message BatchGetChannelListResp {
    required BaseResp base_resp = 1;
    repeated ChannelInfo channel_list = 2;
}

enum ESpecialMusicID {
    CHANNEL_SPECIAL_MUSIC_ID_PERSONAL =
            1; // 特殊的musicID 表示用户自定义播放的音乐
}

// 房间音乐(废弃)
message ChannelMusic {
    required uint32 music_id = 1;
    required string music_name = 2;
    optional string artist_name = 3;
    optional string album_name = 4;
    optional string play_web_url = 5;
    optional string download_url = 6; // 只有在播放时这个字段才会被填写
    optional string img_url = 7;
    optional uint32 music_type = 8; // 1原唱、2伴奏
}

// 播放音乐(废弃)
message StartChannelMusicReq {
    required BaseReq base_req = 1;
    required uint32 channel_id = 2;
    required ChannelMusic music_info = 3;
}

message StartChannelMusicResp {required BaseResp base_resp = 1;}

// 停止播放音乐(废弃)
message StopChannelMusicReq {
    required BaseReq base_req = 1;
    required uint32 channel_id = 2;
    required ChannelMusic music_info = 3;
}

message StopChannelMusicResp {required BaseResp base_resp = 1;}

// 获取房间当前音乐播放信息(废弃)
message GetChannelCurrMusicReq {
    required BaseReq base_req = 1;
    required uint32 channel_id = 2;
}

message GetChannelCurrMusicResp {
    required BaseResp base_resp = 1;
    optional GenericMember start_music_user = 2;
    optional ChannelMusic music_info = 3;
}

// 搜索音乐(废弃)
message ChannelMusicProvider {
    required string provider_ico = 1; // "xx音乐"的ico url
  // 本功能有"xx音乐"提供技术支持 之类的文本描述
    required string provider_info = 2;
}

message SearchChannelMusicReq {
    required BaseReq base_req = 1;
    required string keyword = 2;
}

message SearchChannelMusicResp {
    required BaseResp base_resp = 1;
    required string keyword = 2;
    repeated ChannelMusic music_list = 3; // 列表
  // 音乐提供方信息 比如"xx音乐"
    optional ChannelMusicProvider provider_info = 4;
}

// 获取房间播放列表
message GetChannelMusicListReq {
    required BaseReq base_req = 1;
    required uint32 channel_id = 2;
}

message GetChannelMusicListResp {
    required BaseResp base_resp = 1;
    required uint32 channel_id = 2;
    repeated ChannelMusic history_music_list = 3;    // 历史列表
    repeated ChannelMusic recommend_music_list = 4;  // 推荐列表
    optional ChannelMusicProvider provider_info = 5; // 音乐提供方信息
}

// 根据musicID获取音乐下载地址(废弃)
message GetChannelMusicInfoByMidReq {
    required BaseReq base_req = 1;
    required uint32 music_id = 2;
}

message GetChannelMusicInfoByMidResp {
    required BaseResp base_resp = 1;
    required ChannelMusic music_info = 2;
}

// 收藏来源
enum ChannelCollectSource {
    ChannelCollectSource_Default = 0;
    ChannelCollectSource_TelCall = 1;  // 绑定闪电召集
    ChannelCollectSource_TEAMBAT = 2;  // 基于团战sdk进房自动收藏
}

message ChannelUint32Wrapper {
  required uint32 val_value = 1;
}

// 更新频道的收藏状态
message UpdateChannelCollectStatusReq {
    required BaseReq base_req = 1;
    required bool is_collected = 2;
    required uint32 channel_id = 3;
    optional ChannelUint32Wrapper collect_source = 4;  // see ChannelCollectSource
}

message UpdateChannelCollectStatusResp {
    required BaseResp base_resp = 1;
    required bool is_collected = 2;
    required uint32 channel_id = 3;
}

enum ChannelCollectionsRequestType {
    REQUEST_COLLECT_HOME_PAGE = 1;      // 收藏频道首页的频道列表
    REQUEST_COLLECT_DETAIL = 2;         // 收藏频道完整的频道列表
    REQUEST_COLLECT_HOME_PAGE_LITE = 3; // 仅获取首页在线的收藏房间id，GetChannelCollectionListResp.online_channel_list
}

// 获取收藏房间列表
message GetChannelCollectionListReq {
    required BaseReq base_req = 1;
    required uint32 request_type = 2; // ChannelCollectionsRequestType
}

message GetChannelCollectionListResp {
    required BaseResp base_resp = 1;
    repeated ChannelDetailInfo channel_list = 2;
    required uint32 request_type = 3; // ChannelCollectionsRequestType
    required uint32 total_channel_count = 4;
    repeated uint32 admin_channel_list = 5; // 管理的频道列表
    repeated uint32 online_channel_list = 6; // REQUEST_COLLECT_HOME_PAGE_LITE 首页在线的收藏房间id
}

// 发起频道召集
message ConveneChannelReq {
    required BaseReq base_req = 1;
    required uint32 channel_id = 2;
    required string convene_msg = 3; // 召集描述
}

message ConveneChannelResp {
    required BaseResp base_resp = 1;
    required uint32 convene_ts = 2;
    required uint32 channel_id = 3;
    optional uint32 duration_min = 4; // 召集有效时间(分钟)
}

// 取消频道召集
message DisableConveneChannelReq {
    required BaseReq base_req = 1;
    required uint32 channel_id = 2;
}

message DisableConveneChannelResp {
    required BaseResp base_resp = 1;
    required uint32 channel_id = 2;
}

enum MemberConfirmStatus {
    CONVENE_NOT_RECIEVE = 0; // 未收到召集
    CONVENE_RECIEVE = 1;     // 【已送达】，未对召集令进行操作
    CONVENE_HOLDING = 2; // 【我正在忙，稍后就来】，召集令中选择“等一下”
    CONVENE_REJECT = 3; // 【未响应召集】，关闭召集按钮
    ENTER_CHANNEL = 4;  // 【已进房间】
}

// 频道召集用户信息
message ChannelConveneMember {
    required uint32 channel_id = 1;
    required string account = 2;        // 账号
    required uint32 uid = 3;            // uid
    required string nick_name = 4;      // 名字
    required uint32 confirm_status = 5; // MemberConfirmStatus

    optional UserProfile user_profile = 6;
}

// 获取响应频道召集的用户列表
message GetChannelConfirmMemberListReq {
    required BaseReq base_req = 1;
    required uint32 channel_id = 2;
}

message GetChannelConfirmMemberListResp {
    required BaseResp base_resp = 1;
    repeated ChannelConveneMember member_list = 2;
    optional uint32 total_enter_count = 3; // 已通过召集进房间的总人数
    optional uint32 total_confirm_count = 4; // 收到召集的总成员数
    optional uint32 channel_id = 5;
    optional uint32 total_collect_count = 6; // 收藏房间的总人数
}

// 频道召集提示
message ChannelConveneMsg {
    required uint32 channel_id = 1;
    required uint32 convene_ts = 2;
    required string convene_msg = 3;
    required uint32 duration_min = 4; // 召集有效时间(分钟)
    optional ChannelInfo channel_info = 5;
}

// 获取正在召集指定用户的频道
message GetUserConveneChannelListReq {required BaseReq base_req = 1;}

message GetUserConveneChannelListResp {
    required BaseResp base_resp = 1;
    repeated ChannelConveneMsg convene_list = 2;
}

// 更新频道召集的用户的响应状态
message UpdateMemberConfirmStatusReq {
    required BaseReq base_req = 1;
    required uint32 channel_id = 2;
    required uint32 confirm_status = 3; // MemberConfirmStatus
}

message UpdateMemberConfirmStatusResp {
    required BaseResp base_resp = 1;
    required uint32 confirm_status = 2; // MemberConfirmStatus
    required uint32 channel_id = 3;
}

// 获取频道的召集信息
message GetChannelConveneInfoReq {
    required BaseReq base_req = 1;
    required uint32 channel_id = 2;
}

message GetChannelConveneInfoResp {
    required BaseResp base_resp = 1;
    required uint32 channel_id = 2;
    required uint32 valid_convene_ts = 3;     // 可发起下次召集的时间
    required uint32 convene_status = 4;       // 0.未召集 1.召集中
    required uint32 confirm_count = 5;        // 响应召集的人数
    required uint32 convene_duration_min = 6; // 召集有效时间(分钟)
    optional uint32 last_convene_ts = 7;      // 上次发起召集时间
    optional uint32 last_disable_ts = 8;      // 上次取消召集时间
    optional string last_convene_msg = 9;     // 上次的召集描述
}

enum MusicInfoStats {
    MUSIC_INFO_NORMAL = 1;
    MUSIC_INFO_PLAYING = 2;
    MUSIC_INFO_NEXT = 3;
}
// 音乐V2
message MusicInfoV2 {
    required string client_key = 1;  // 客户端唯一标识
    required string name = 2;        // 歌曲名
    required string author = 3;      // 作者
    required uint32 uid = 4;         // 上传者id
    optional uint32 volume = 5;      // 音量
    optional int64 key = 6;          // 服务器标识
    optional string account = 7;     // 上传者account
    optional uint32 is_local = 8;    // 是否是客户端本地歌曲 1-是 0-否
    optional uint32 status = 9;      // 播放状态
    required string nickname = 10;   // 上传者名字
    optional uint32 music_type = 11; //音乐类型 1原唱、2伴奏
}
// 获取音乐列表
message GetChannelMusicListV2Req {
    required BaseReq base_req = 1;
    required uint32 channel_id = 2;
}
message GetChannelMusicListV2Resp {
    required BaseResp base_resp = 1;
    repeated MusicInfoV2 music_list = 2;
}

// 设置可分享歌曲
message SetChannelMusicV2CanShareReq {
    required BaseReq base_req = 1;
    required uint32 channel_id = 2;
    required bool can_share = 3;
}
message SetChannelMusicV2CanShareResp {required BaseResp base_resp = 1;}

// 设置自由模式
message SetChannelMusicV2FreeModeReq {
    required BaseReq base_req = 1;
    required uint32 channel_id = 2;
    required bool free_mode = 3;
}
message SetChannelMusicV2FreeModeResp {required BaseResp base_resp = 1;}

// 战歌列表类型
enum ChannelMusicListType {
    ENUM_MUSIC_LIST_TYPE_UNSPECIFIED = 0;  // TT
    ENUM_MUSIC_LIST_TYPE_NC = 1;           // 网易云音乐
}

// 播放状态
message ChannelMusicStatus {
    required uint32 play_mode = 1;
    required uint32 volume = 2;
    required bool can_share = 3;
    required bool is_playing = 4;
    optional int64 current_playing = 5;
    optional bool free_mode = 6;
    optional uint32 music_list_type = 7; // see ChannelMusicListType，区分tt歌单及其他第三方渠道歌单
}
message GetChannelMusicV2StatusReq {
    required BaseReq base_req = 1;
    required uint32 channel_id = 2;
}
message GetChannelMusicV2StatusResp {
    required BaseResp base_resp = 1;
    required ChannelMusicStatus channel_music_status = 2;
}

// 添加歌曲
message AddChannelMusicV2Req {
    required BaseReq base_req = 1;
    required uint32 channel_id = 2;
    repeated MusicInfoV2 music_list = 3;
    optional uint32 is_local = 4; // 1 从本地上传到播放列表
}
message AddChannelMusicV2Resp {
    required BaseResp base_resp = 1;
    repeated MusicInfoV2 music_added_list = 2;
}

// 审核上传歌曲的歌名和作者名字
message CheckAddMusicV2Req {
    required BaseReq base_req = 1;
    required uint32 channel_id = 2;
    repeated MusicInfoV2 music_list = 3;
}
message CheckAddMusicV2Resp {
    required BaseResp base_resp = 1;
    repeated MusicInfoV2 music_pass_list = 2;
}

// 删除歌曲
message RemoveChannelMusicV2Req {
    required BaseReq base_req = 1;
    required uint32 channel_id = 2;
    repeated int64 music_list = 3;
}
message RemoveChannelMusicV2Resp {
    required BaseResp base_resp = 1;
    repeated int64 music_removed_list = 2;
}
// 设定下一首
message SetNextMusicV2Req {
    required BaseReq base_req = 1;
    required uint32 channel_id = 2;
    required int64 music_id = 3; // 服务器标识
}
message SetNextMusicV2Resp {required BaseResp base_resp = 1;}
// 播放指定歌曲
message PlayMusicV2MusicReq {
    required BaseReq base_req = 1;
    required uint32 channel_id = 2;
    required int64 music_id = 3; // 服务器标识
}
message PlayMusicV2MusicResp {required BaseResp base_resp = 1;}

// 播放命令
enum ChannelMusicCtrl {
    MUSIC_CTRL_START = 1;
    MUSIC_CTRL_STOP = 2;
    MUSIC_CTRL_NEXT = 3;
    MUSIC_CTRL_PREV = 4;
}

message ChanneMusicV2CommandReq {
    required BaseReq base_req = 1;
    required uint32 uid = 2;
    required uint32 channel_id = 3;
    required uint32 command_ctrl = 4;
}
message ChanneMusicV2CommandResp {
    required BaseResp base_resp = 1;
    optional MusicInfoV2 next_music = 2;
}

// 播放模式
enum ChannelMusicPlayMode {
    MUSIC_PLAY_MODE_ORDER = 1;
    MUSIC_PLAY_MODE_RANDOM = 2;
    MUSIC_PLAY_MODE_LOOP = 3;
}
message SetChannelMusicV2PlayModeReq {
    required BaseReq base_req = 1;
    required uint32 uid = 2;
    required uint32 channel_id = 3;
    required uint32 play_mode = 4;
}

message SetChannelMusicV2PlayModeResp {required BaseResp base_resp = 1;}
// 音量
message SetChannelMusicV2VolumeReq {
    required BaseReq base_req = 1;
    required uint32 uid = 2;
    required uint32 channel_id = 3;
    required uint32 volume = 4;
}
message SetChannelMusicV2VolumeResp {required BaseResp base_resp = 1;}

// 心跳
message ChanneMusicV2HeartBeatReq {
    enum ECLIENT_EVENT {
        ECLIENT_EVENT_NORMAL = 0;
        ECLIENT_EVENT_NOT_PLAY_NETWORK = 1; // 网络原因无法播放
        ECLIENT_EVENT_NOT_PLAY_FILE = 2;    // 文件原因无法播放
    };

    required BaseReq base_req = 1;
    required uint32 channel_id = 2;
    required int64 music_key = 3;
    required uint32 percent = 4; // 播放进度,最大100

    optional uint32 client_event = 5; // ECLIENT_EVENT
}
message ChanneMusicV2HeartBeatResp {
    required BaseResp base_resp = 1;
    required uint32 uid = 2;
    required uint32 channel_id = 3;
    required uint32 volume = 4;
    optional int64 music_id = 5;
}

// 快速组队信息
message GetGameMatchOptionsReq {
    required BaseReq base_req = 1;
    required uint32 version = 2;
}

message GameMatchOptions {
    required uint32 game_id = 1;
    required string game_name = 2;
    required string icon_url = 3;
    repeated uint32 member_count = 4;
    repeated string ann_list = 5;
    required uint32 channel_tag_id = 6;
}

message GetGameMatchOptionsResp {
    required BaseResp base_resp = 1;
    repeated GameMatchOptions game_match_opt_list = 2;
    required uint32 version = 3;
}

// 发起组队
message StartGameMatchReq {
    required BaseReq base_req = 1;
    required uint32 channel_id = 2;
    required string channel_ann = 3;
    required uint32 max_member_count = 4;
    required uint32 tag_id = 5;

    // 新人推荐位展示状态 see NOVICE_RECOMMEND_CHANNEL_DISPLY_STATUS
    optional uint32 recommend_status = 6;
}

message StartGameMatchResp {required BaseResp base_resp = 1;}

// 组队房相关
message GameMatchMember {
    required uint32 uid = 1;
    required uint32 sex = 2;
    required string account = 3;
}
message GameMatchChannel {
    required ChannelDetailInfo channel_detail = 1;
    required uint32 max_member_count = 2;
    required uint32 cur_member_count = 3;
    repeated GameMatchMember game_match_member_list = 4;
    optional string game_name = 5;
    optional string icon_url = 6;
    required uint32 create_ts = 7;
}

// 组队首页
message GetGameMatchListHomePageReq {
    required BaseReq base_req = 1;
    required uint32 page = 2;
    required uint32 page_count = 3;
}
message GetGameMatchListHomePageResp {
    required BaseResp base_resp = 1;
    repeated GameMatchChannel game_match_channel_list = 2;
    required uint32 page = 3; // 请求是第几页
    required bool reach_end = 4; // 是否还存在后续页 ture表示到底了 没有了


    // 针对新用户的推荐房列表，当page==0 时有值。有值时与
    // game_match_member_list 无交集
    repeated GameMatchChannel novice_recommend_gm_channel_list = 5;
}
message GetGameMatchListByTagIdReq {
    required BaseReq base_req = 1;
    required uint32 page = 2;
    required uint32 page_count = 3;
    required uint32 tag_id = 4;
}
message GetGameMatchListByTagIdResp {
    required BaseResp base_resp = 1;
    repeated GameMatchChannel game_match_channel_list = 2;
    required uint32 page = 3; // 请求是第几页
    required bool reach_end = 4; // 是否还存在后续页 ture表示到底了 没有了
    required uint32 tag_id = 5;

    // 针对新用户的推荐房列表，当page==0 时有值。有值时与
    // game_match_member_list 无交集
    repeated GameMatchChannel novice_recommend_gm_channel_list = 6;
}

// 房间历史 （看看谁来过）
message ChannelHistoryInfo {
    required uint32 histroy_type = 1;
    required uint32 ts = 2;
    required GenericMember member = 3;
}

message GetChannelHistoryReq {
    required BaseReq base_req = 1;
    required uint32 channel_id = 2;
    required uint32 begin_idx = 3;   //没用的参数，服务端都是直接返回最近20条数据
    required uint32 limit = 4; //没用的参数，服务端都是直接返回最近20条数据
}

message GetChannelHistoryResp {
    required BaseResp base_resp = 1;
    required uint32 channel_id = 2;
    repeated ChannelHistoryInfo history_list = 3;
    required uint32 all_history_cnt = 4;
}

// 房间礼物历史消费信息
message GetChannelGiftHistoryReq {
    required BaseReq base_req = 1;
    required uint32 channel_id = 2;
}

message GetChannelGiftHistoryResp {
    required BaseResp base_resp = 1;
    required uint32 channel_id = 2;
    required PresentSummary present_info = 3;
}

// 房间统计信息
message ChannelStatistics {
    optional uint32 max_online = 1;    // 最高在线人数
    optional uint32 max_online_ts = 2; // 最高在线人数 出现的时间
    optional uint32 collect_cnt = 3;   // 当前收藏房间的用户数
}
// 房间管理员信息 (个人房间才有独立的管理员)
message ChannelAdminInfo {
    required GenericMember member = 1;
    required uint32 admin_role = 2; // see EChannelAdminRoleType
}

// 房间消费
message ChannelMemberConsumeInfo {
    required GenericMember member = 1;
    required uint32 red_diamond_consume = 2;
}

// 房间附加信息的bitmap类型 值为0x1 0x2 0x4 0x8 ...
enum EChannelExtendType {
    ENUM_EXTEND_STAT = 1;  // 统计信息
    ENUM_EXTEND_ADMIN = 2; // 管理员列表 仅对个人房有效

    // 土豪榜 返回前N个用户 N由服务器控制 现在是3
    ENUM_EXTEND_CONSUM_TOPN = 4;

    // 房间历史 历史进房间的用户TOP N的数量由服务器控制 现在是3
    ENUM_EXTEND_HISTORY_TOPN = 8;
    ENUM_EXTEND_TOPIC_DETAIL = 16;  // 房间话题详情
    ENUM_EXTEND_WELCOME_MSG = 32;  // 房间欢迎语
    ENUM_EXTEND_TAG_RANK = 64;  // tag周榜
}

message GetChannelExtendInfoReq {
    required BaseReq base_req = 1;
    required uint32 channel_id = 2;


    // extend_type_bitmap 是基于 EChannelExtendType 的bitmap
    // 如果要请求统计信息就填0x1
    // 如果要统计信息和管理员列表就填（0x1|0x2）= 0x3
    required uint32 extend_type_bitmap = 3;
}

message ChannelTagRankItem {
    required uint32 channel_id = 1;
    required uint32 tag_id = 2;
    required uint32 value = 3;
    required uint32 rank = 4;
    required uint32 last_week_rank = 5; // 0是不在榜里
    required string tag_name = 6;
}

message GetChannelExtendInfoResp {
    required BaseResp base_resp = 1;
    required uint32 channel_id = 2;

    // 基于 EChannelExtendType 的bitmap, 如果要请求统计信息就填0x1
    // 如果要统计信息和管理员列表就填（0x1|0x2）= 0x3
    required uint32 extend_type_bitmap = 3;

    optional ChannelStatistics stat_info = 4;               // 统计信息
    repeated ChannelAdminInfo admin_list = 5;               // 管理员列表
    repeated ChannelMemberConsumeInfo consum_topn_list = 6; // 房间的消费榜
    repeated ChannelHistoryInfo history_list = 7; // 房间的历史列表
    optional string topic_detail = 8;             // 房间话题的详细内容
    optional string welcome_msg = 9;              // 房间欢迎语
    repeated ChannelTagRankItem tag_ranks = 10;   // tag周榜
}

// 设置管理员 目前只对 个人房 和 公会公开房 有效
message OperChannelAdminReq {
    required BaseReq base_req = 1;
    required uint32 channel_id = 2;
    required uint32 oper_type = 3; // 1是增加管理员 2是删除管理员
    required uint32 target_uid = 4;
    optional uint32 admin_role = 5; // 管理员类型 see EChannelAdminRoleType

    // 如果 target_uid == 0 可以使用 target_user_account进行查询
    optional string target_user_account = 6;
}

message OperChannelAdminResp {
    required BaseResp base_resp = 1;
    required uint32 channel_id = 2;
    required uint32 oper_type = 3; // 1是增加管理员 2是删除管理员
    required uint32 target_uid = 4;
}

// 换自己的麦的位置
message ChannelChangeMicReq {
    required BaseReq base_req = 1;
    required uint32 channel_id = 2;
    required uint32 target_mic_id = 3; // 换麦位的目标麦位
}

message ChannelChangeMicResp {
    required BaseResp base_resp = 1;
    required uint32 channel_id = 2;
    // 如果换成功 目标麦位的状态信息 只有UID信息和MICid信息
    required MicrSpace target_mic_info = 3;
}

// 将指定用户放到指定麦位上
message TakeUserToChannelMicReq {
    required BaseReq base_req = 1;
    required uint32 channel_id = 2;
    required uint32 target_uid = 3;
    required uint32 target_mic_id = 4;

    optional string target_ttid = 5;
}

message TakeUserToChannelMicResp {
    required BaseResp base_resp = 1;
    required uint32 channel_id = 2;
    required uint32 target_uid = 3;
    required MicrSpace mic_info = 4;
}

message ChannelMicTakeChangeRequest {
    required BaseReq base_req = 1;
    required uint32 channel_id = 2;
    required uint32 target_uid = 3;
    required uint32 target_mic_id = 4;
    optional string target_ttid = 5;
    optional bool is_force = 6;    // 是否需要强行上麦，如果指定的麦位上有人则踢人下麦
}

message ChannelMicTakeChangeResponse {
    required BaseResp base_resp = 1;
    required uint32 channel_id = 2;
    required uint32 target_uid = 3;
    required MicrSpace mic_info = 4;
}

//房间小游戏
enum ChannelGameType {
    GAME_DRAW = 1; //抽签
    GAME_DICE = 2; //摇骰子
}

message ChannelGameReq {
    required BaseReq base_req = 1;
    required uint32 channel_id = 2;
    required uint32 game_id = 3;
}

message ChannelGameResp {required BaseResp base_resp = 1;}


//魔法表情类型
enum MagicExpressionType {
    MAGIC_NORMAL = 0; //原魔法表情
    MAGIC_INTERACTION = 1; //互动表情
}


//娱乐房麦上魔法表情
message SendMagicExpressionReq {
    required BaseReq base_req = 1;
    required uint32 channel_id = 2; //房间id
    required uint32 magic_id = 3;   //表情id
    optional uint32 opt_max_num = 4;
    repeated uint32 magic_num_list = 5;
}

message SendMagicExpressionResp {required BaseResp base_resp = 1;}




//娱乐房麦上互动表情
message SendInteractionEmojiReq {
    required BaseReq base_req = 1;
    required uint32 channel_id = 2; //房间id
    required uint32 magic_id = 3;   //表情id
    optional uint32 magic_type = 4; // enum MagicExpressionType
    optional uint32 target_uid = 5; //目标用户UID
    optional uint32 target_mic_id = 6; //目标麦位ID
}

message SendInteractionEmojiResp {required BaseResp base_resp = 1;}

// 修改房间属性(图标 描述 标签 ...)
enum EChannelModifyBitmapType {
    ENUM_CHANNEL_MODIFY_ICON = 1;  // 图标
    ENUM_CHANNEL_MODIFY_DESC = 2;  // 标题标题
    ENUM_CHANNEL_MODIFY_LABEL = 4; // 标签

    ENUM_CHANNEL_MODIFY_RECOMMEND_SWITCH_FLAG = 8;  // 是否加入推荐的开关
    ENUM_CHANNEL_MODIFY_LOCKSCREEN_SWITCH_FLAG = 16; // 是否开启锁屏的开关
    ENUM_CHANNEL_MODIFY_AUTO_DISABLE_MIC_SWITCH_FLAG = 32; // 是否开启自动锁麦开关

    ENUM_CHANNEL_MODIFY_TOPIC_DETAIL = 64;  // 话题详情
    ENUM_CHANNEL_MODIFY_WELCOME_MSG = 128; // 房间欢迎语

    // 是否开启禁止公屏发图片开关
    ENUM_CHANNEL_MODIFY_DISABLE_ATTACHMENT_MSG_FLAG = 256;
    // 是否开启禁止5级以下用户在公屏发言开关
    ENUM_CHANNEL_MODIFY_DISABLE_LEVEL_LMT_FLAG = 512;
    // 是否开启开启电台直播连麦功能的开关
    ENUM_CHANNEL_MODIFY_LIVE_CONNECT_MIC_FLAG = 1024;
    // 是否开启开启 普通房的 排麦功能的开关
    ENUM_CHANNEL_MODIFY_NORMAL_QUEUE_UP_MIC_FLAG = 2048;
}
message ModifyChannelExtentReq {
    required BaseReq base_req = 1;
    required uint32 channel_id = 2;    // 房间id
    required uint32 modify_bitmap = 3; // EChannelModifyBitmapType 的 bitmap
    optional bytes icon = 4; // 如果是要修改图标 这里是图标的二进制数据
    optional string desc = 5; // 如果是修改话题标题 这里填话题标题 内容

    optional bool recommend_switch_flag = 6; // 是否加入推荐的开关 0 关闭 1开启
    optional bool lockscreen_switch_flag = 7; // 是否开启锁屏的开关 0 关闭 1开启
    optional bool auto_disable_mic_switch_flag =
            8; // 是否开启自动锁麦开关 0 关闭 1开启

    optional string topic_detail = 9; // 如果是修改话题详情 这里填话题详情内容
    optional string welcome_msg = 10; // 如果是修改房间欢迎语 这里填房间欢迎语内容

    optional bool disable_attachment_msg_switch_flag =
            11; // 是否开启禁止公屏发图片开关 0 关闭 1开启
    optional bool disable_level_lmt_switch_flag =
            12; // 是否开启禁止5级以下用户在公屏发言开关 0 关闭 1开启
    optional bool open_live_connect_mic_switch_flag =
            13; // 是否开启电台直播连麦功能，0关闭 1开启
    optional bool open_normal_queue_up_mic_switch_flag =
            14; // 是否开启开启 普通房的 排麦功能，0关闭 1开启
}

message ModifyChannelExtentResp {
    required BaseResp base_resp = 1;
    required uint32 channel_id = 2;    // 房间id
    required uint32 modify_bitmap = 3; // EChannelModifyBitmapType 的 bitmap
    optional string icon_md5 = 4;
    optional string desc = 5; // 房间话题描述（标题）

    optional bool recommend_switch_flag = 6; // 是否加入推荐的开关 0 关闭 1开启
    optional bool lockscreen_switch_flag = 7; // 是否开启锁屏的开关 0 关闭 1开启
    optional bool auto_disable_mic_switch_flag =
            8; // 是否开启自动锁麦开关 0 关闭 1开启

    optional string topic_detail = 9; // 如果是修改话题详情 这里填话题详情内容
    optional string welcome_msg = 10; // 如果是修改房间欢迎语 这里填房间欢迎语内容

    optional bool disable_attachment_msg_switch_flag =
            11; // 是否开启禁止公屏发图片开关 0 关闭 1开启
    optional bool disable_level_lmt_switch_flag =
            12; // 是否开启禁止5级以下用户在公屏发言开关 0 关闭 1开启
    optional bool open_live_connect_mic_switch_flag =
            13; // 是否开启电台直播连麦功能，0关闭 1开启
    optional bool open_normal_queue_up_mic_switch_flag =
            14; // 是否开启开启 普通房的 排麦功能，0关闭 1开启
}

// 获取推荐频道列表
message GetRecommendChannelListReq {
    required BaseReq base_req = 1;
    required uint32 page = 2;       // 第几页
    required uint32 page_count = 3; // 每页多少条记录
}

message ChannelTagInfo {
    required uint32 channel_id = 1;
    required string tag_info = 2;
    required string bk_color = 3;

}

message GetRecommendChannelListResp {
    required BaseResp base_resp = 1;
    repeated ChannelDetailInfo channel_list = 2;
    required uint32 page = 3; // 请求是第几页
    required bool reach_end = 4; // 是否还存在后续页 ture表示到底了 没有了

    repeated uint32 official_activity_list = 5;       // 官方活动列表
    repeated uint32 official_recommend_list = 6;      // 官方推荐列表
    repeated ChannelTagInfo channel_taginfo_list = 7; // 标签信息
    repeated uint32 newuser_recommend_list = 8; //新用户推荐channelid列表
}

message ChannelRecommendInfo {
    required ChannelDetailInfo detail_info = 1;
    required string tag_name = 2;
    required string tag_color = 3;
    required bool is_highlight = 4;
}

message GetRecommendChannelListV2Req {
    required BaseReq base_req = 1;
    required uint32 page = 2;       // 第几页
    required uint32 page_count = 3; // 每页多少条记录
}

message GetRecommendChannelListV2Resp {
    required BaseResp base_resp = 1;
    required uint32 page = 2;       // 请求是第几页
    required uint32 page_count = 3; // 每页多少条记录
    required bool reach_end = 4; // 是否还存在后续页 ture表示到底了 没有了

    repeated ChannelRecommendInfo office_list =
            5; // 官方推荐列表 仅在page=0时有效
    repeated ChannelRecommendInfo special_top_list =
            6; // 特殊置顶列表 仅在page=0时有效

    repeated ChannelRecommendInfo channel_nomal_list = 7; // 普通列表
}

// 标签相关
enum FCTagType {
    FCTT_FUNNY_ROOM = 1;
    FCTT_GAME = 2;
}
message CardInfo {// 卡片信息
    required string card_name = 1;
    required string card_image = 2;
    required string back_image = 3;
    required string client_link = 4;
    optional uint32 tag_id = 5;
    optional uint32 user_count = 6;
}
message FCTagInfo {// 一级标签
    required string tag_name = 1;
    required uint32 tag_id = 2;
    optional uint32 fc_tag_type = 3;
}
message SCTagInfo {// 二级标签
    enum SCTagFlag {
        SCTF_HIDE_SETTING = 1;
        SCTF_HIDE_TAB = 2;
    }
    required string tag_name = 1;
    required string icon = 2;
    required uint32 tag_id = 3;
    optional bool is_show = 4; // 兼并到ext_flags
    optional string jump_url = 5;
    optional uint32 ext_flag =
            6; //如果有,优先使用这个字段,兼容字段4,bit or SCTagFlag
    optional string bk_color = 7; //背景颜色
    optional string sub_tag =
            8; // 来自 ChannelRecommendSimpleInfo,top 10 之类的例如（年度扩列冠军)
    optional uint32 channel_id = 9; // for search
    repeated string multi_color = 10; // 渐变色
}
message TagInfo {
    required FCTagInfo fc_tag_info = 1;
    repeated SCTagInfo sc_tag_info_list = 2;
}

// 获取卡片
message GetChannelCardReq {required BaseReq base_req = 1;}
message GetChannelCardResp {
    required BaseResp base_resp = 1;
    repeated CardInfo card_info_list = 2;
    required SCTagInfo sc_game_tag_info = 3;
    optional bool game_on_top = 4;
    optional bool has_visit_game = 5;
}
// 获取标签列表
message GetChannelTagListReq {required BaseReq base_req = 1;}
message GetChannelTagListResp {
    required BaseResp base_resp = 1;
    repeated TagInfo tag_info_list = 2;
}

// 首页 推荐语音直播房
message GetRecommendLiveChannelReq {required BaseReq base_req = 1;}

message GetRecommendLiveChannelResp {
    required BaseResp base_resp = 1;
    repeated ChannelDetailInfo channel_list = 2;
}

// 二级页 语音直播房
message GetLiveChannelListReq {
    required BaseReq base_req = 1;
    required uint32 page = 2;
    required uint32 page_count = 3;
}
message GetLiveChannelListResp {
    required BaseResp base_resp = 1;
    repeated ChannelDetailInfo channel_list = 2;
    required uint32 page = 3;
    optional bool reach_end = 4;
    optional uint32 user_count = 5;
}

// 获取标签下的房间列表
message GetChannelListByTagIdReq {
    required BaseReq base_req = 1;
    required uint32 tag_id = 2;
    required uint32 page = 3;
    required uint32 page_count = 4;
}
message GetChannelListByTagIdResp {
    required BaseResp base_resp = 1;
    repeated ChannelDetailInfo channel_list = 2;
    required uint32 page = 3;
    required uint32 tag_id = 4;
    optional bool reach_end = 5;
    optional uint32 user_count = 6;
    repeated uint32 recommend_channel_list = 7;
}
// 获取房间标签
message GetChannelTagIdReq {
    required BaseReq base_req = 1;
    required uint32 channel_id = 2;
}
message GetChannelTagIdResp {
    required BaseResp base_resp = 1;
    required SCTagInfo sc_tag_info = 2;
    required bool is_set = 3;
}
// 设置房间标签
message SetChannelTagIdReq {
    required BaseReq base_req = 1;
    required uint32 channel_id = 2;
    required uint32 tag_id = 3;
}
message SetChannelTagIdResp {required BaseResp base_resp = 1;}

// 刷新剩余cd
message GetChannelRefreshCDReq {
    required BaseReq base_req = 1;
    required uint32 channel_id = 2;
}
message GetChannelRefreshCDResp {
    required BaseResp base_resp = 1;
    required uint32 remain_seconds = 2;
}

// 刷新房间时间
message RefreshChannelReq {
    required BaseReq base_req = 1;
    required uint32 channel_id = 2;
}
message RefreshChannelResp {
    required BaseResp base_resp = 1;
    required uint32 remain_seconds = 2;
}

// 获取房间广告位
enum CHANNEL_ADV_TYPE {
    CAT_CHANNEL_FUNNY = 0;
    CAT_CHANNEL_HOMEPAGE = 1;
}
message GetChannelAdvReq {
    required BaseReq base_req = 1;
    optional uint32 adv_type = 2;
}
message ChannelAdv {
    required string pic_url = 1;
    required string adv_url = 2;
}
message GetChannelAdvResp {
    required BaseResp base_resp = 1;
    repeated ChannelAdv adv_list = 2;
}

message GetChannelHomeDetailReq {required BaseReq base_req = 1;}

message GetChannelHomeDetailResp {
    required BaseResp base_resp = 1;
    repeated ChannelAdv adv_list = 2;
    repeated ChannelRecommendInfo channel_recommend_info_list = 3;
}

// 获取热门频道列表
message GetHotChannelListReq {
    required BaseReq base_req = 1;
    optional uint32 count_limit = 2; // 列表数量 默认10
}

message GetHotChannelListResp {
    required BaseResp base_resp = 1;
    repeated ChannelDetailInfo channel_list = 2;
    repeated ChannelTagInfo channel_taginfo_list = 3; // 标签信息
    repeated uint32 official_activity_list = 4;       // 官方活动列表
    repeated uint32 official_recommend_list = 5;      // 官方推荐列表
}

enum EChannelShowSwitchType {
    ENUM_CHANNEL_SHOW_SWITCH_HOT = 1; // 热门频道
}

// 获取房间tab的频道列表开关
message GetChannelShowSwitchReq {
    required BaseReq base_req = 1;
    required uint32 switch_type = 2; // EChannelShowSwitchType
}

message GetChannelShowSwitchResp {
    required BaseResp base_resp = 1;
    required uint32 switch_type = 2;   // EChannelShowSwitchType
    required uint32 switch_status = 3; // 0.off 1.on
}

// 结束直播
message FinishChannelLiveReq {
    required BaseReq base_req = 1;
    required uint32 channel_id = 2;
}

message FinishChannelLiveResp {
    required BaseResp base_resp = 1;

    required uint32 channel_id = 2;
    required uint32 user_cnt = 3;    //从直播开始到结束时 用户的人数
    required uint32 enter_cnt = 4;   //从直播开始到结束时 进房的次数
    required uint32 tcoin_cnt = 5;   //从直播开始到结束时 T豆的数
    required uint32 time_second = 6; //从直播开始到结束时 持续的秒数
}

// 发起连麦申请
message LiveChannelMicApplyReq {
    required BaseReq base_req = 1;
    required uint32 channel_id = 2;
    required bool is_cancel = 3; // ture表示 取消之前的申请
}

message LiveChannelMicApplyResp {
    required BaseResp base_resp = 1;
    required uint32 channel_id = 2;
    required bool is_cancel = 3; // ture表示 取消之前的申请
}

// 处理连麦请求
message LiveChannelMicHandleReq {
    required BaseReq base_req = 1;
    required uint32 channel_id = 2;
    repeated uint32 uid_list = 3;
    required bool is_allow = 4;
}

message LiveChannelMicHandleResp {
    required BaseResp base_resp = 1;
    required uint32 channel_id = 2;
    repeated uint32 success_uid_list = 3;
    required bool is_allow = 4;
}

// 获取连麦申请人列表
message LiveChannelApplyMember {
    required string account = 1;   // 成员账号
    required uint32 uid = 2;       // 成员uid
    required string nick_name = 3; // 成员昵称
    optional uint32 sex = 4;       // 性别
    optional UserProfile user_profile = 5; // 用户信息
}

message GetLiveChannelMicApplyUserListReq {
    required BaseReq base_req = 1;
    required uint32 channel_id = 2;
    optional uint32 limit_cnt = 3;
}

message GetLiveChannelMicApplyUserListResp {
    required BaseResp base_resp = 1;
    repeated LiveChannelApplyMember member_list = 2;
    optional uint32 limit_cnt = 3;     // 请求值
    optional uint32 all_apply_cnt = 4; // 总的申请人数量
}

//获取用户的语音直播房信息
message GetUserChannelLiveReq {required BaseReq base_req = 1;}

message GetUserChannelLiveResp {
    required BaseResp base_resp = 1;
    required uint32 channel_id = 2;
}
// 房间送礼统计
message ChannelPresentCountReq {
    required BaseReq base_req = 1;
    required uint32 channel_id = 2;
    required bool on_or_off = 3; // ture on; false off;
}

message ChannelPresentCountResp {required BaseResp base_resp = 1;}

// 6.18.0 计数器优化相关 ---------- begin

// 用户进房时查询房间是否开启送礼统计
message GetChannelPresentCountStateReq {
    required BaseReq base_req = 1;
    required uint32 channel_id = 2;
}

// 房间当前麦上用户的礼物统计
message ChannelMicPresentCountInfo {
    required uint32 uid = 1;
    required uint32 price = 2;
    optional string bg_suffix = 3; //ugc房暂时不返回，通过服务端开关控制
    optional string ic_suffix = 4;
}

message GetChannelPresentCountStateResp {
    required BaseResp base_resp = 1;
    required bool status = 2; // true ,已开启;false,已关闭
    repeated ChannelMicPresentCountInfo present_count_infos = 3; //房间当前各麦位用户的礼物统计
    optional string style_prefix = 4; //图标资源前缀
    optional string bg_suffix = 5;    //0档位样式资源
    optional string ic_suffix = 6;
    optional string uncompiled_prefix = 7; //未编译的后缀背景图前缀，暂时ios使用
}

message CounterRankUserInfo{
    required UserProfile user_profile = 1; // 用户信息

    // 用户对目标用户的送礼值及排名信息
    required uint32 send_value = 2; // 送礼值
    required uint32 rank = 3;      // 用户的排名
    required uint32 d_value = 4;   // 第一名时，为与第二名的差值；其他在榜用户为与上一名的差值；未上榜用户为距上榜的差值

    required uint32 new_rich_level = 5;             // 用户的财富等级
    required uint32 new_charm_level = 6;            // 用户的魅力等级
    optional ChannelMemberVipLevel vip_level = 7;   // 房间内VIP等级 定义在ga_base 中
    optional NobilityInfo nobility_info = 8;        //贵族属性
}

//目标用户信息
message CounterTargetUserInfo{
    required UserProfile user_profile = 1; // 用户信息
    required uint32 receive = 2;
    required uint32 next_level_value = 3;  // 下一档位目标值
    required string next_lv_url = 4;        // 下一档(或最高档)皮肤样式
}

//获取计数器榜单
message GetPresentCountRankReq{
    required ga.BaseReq base_req = 1;
    required uint32 channel_id = 2;
    required uint32 target_uid = 3;
    required uint32 offset = 4; // 分页偏移量，第一页填0，否则填resp中的next_page_offset
    required uint32 limit = 5;  // 每次获取记录数量，最大填50，一般填50
}

message GetPresentCountRankResp{
    required ga.BaseResp base_resp = 1;
    required uint32 channel_id = 2;
    required uint32 next_page_offset = 3;    // 下一页的索引，返回值为0时，没有下一页
    required uint32 view_cnt = 4;  //榜单展示数
    required CounterTargetUserInfo target_user_info = 5;  // 目标用户信息
    required CounterRankUserInfo my_info = 6;             // 我的用户信息，仅在第一页返回
    repeated CounterRankUserInfo member_list = 7; // 成员列表(按排名排序)
}

// 6.18.0 计数器优化相关 ---------- end

// 房间送礼的火箭跑道信息
message ChannelPresentRunwayInfo {
    required uint32 uid = 1;
    required string account = 2;
    required string nickname = 3;
    optional string face_md5 = 4;
    required uint32 channel_id = 5;
    required uint32 level = 6; // 火箭/跑道等级
    required uint32 expired_time = 7;
    required uint32 present_value = 8;      // 当前礼物值
    required uint32 present_value_min = 9;  // 此等级的最低礼物值
    required uint32 present_value_max = 10; // 此等级的最高礼物值
    optional uint32 present_add_value =
            11; // 礼物的增加值(进房退房之类的情况下，这里会是0)
    optional uint32 item_id = 12; // 导致火箭变化的礼物id

    optional UserUKWInfo user_ukw_info = 13; // 神秘人信息
    optional UserProfile user_profile = 14; // 用户信息
}

// 跑道特效
message ChannelPresentRunwayEffect {
    required uint32 runway_present_base =
            1; // 单次每送出指定值的礼物，可额外增加跑道时间
    required uint32 runway_add_seconds =
            2; // 跑道在礼物的播放时间基础上额外再增加的时间（秒）
    required uint32 max_runway_add_seconds = 3; // 跑道最多可增加的时间（秒）
}

// 获取房间内的火箭跑道信息
message GetChannelPresentRunwayListReq {
    required BaseReq base_req = 1;
    required uint32 channel_id = 2;
}

message GetChannelPresentRunwayListResp {
    required BaseResp base_resp = 1;
    repeated ChannelPresentRunwayInfo runway_list = 2;
    optional ChannelPresentRunwayEffect runway_effect = 3;
}

// 房间投票PK

// PK类型
enum ChannelVotePkType {
    ChannelVotePkType_INVALID_TYPE = 0;    // 无效
    ChannelVotePkType_VOTE_PK_TYPE = 1;    // 观众点击投票
    ChannelVotePkType_DIAMOND_PK_TYPE = 2; // 用送红钻礼物方式投票
    ChannelVotePkType_TBEAN_PK_TYPE = 3;   // 用送T豆礼物方式投票
}

enum ChannelVotePKNumType {
    ChannelVotePKNum_INVALID_TYPE = 0; //无效
    ChannelVotePKNum_TWO_PERSON = 1;   //两人
    ChannelVotePKNum_MUTI_PERSON = 2;  //多人
}

//排行榜中玩家排名信息
message ChannelVotePkCompetitor {
    required uint32 uid = 1;
    required string nickname = 2;
    required string account = 3;
    optional string face_md5 = 4;

    required uint32 vote = 5; //得分
    required uint32 rank = 6; //排名
}

// PK基本信息
message ChannelPkInfo {
    required uint32 uid = 1; //发起玩家
    required uint32 channel_id = 2;
    required uint32 type = 3; // enum ChannelVotePkType PK类型
    required uint32 duration_min = 4;
    required uint32 start_timestamp = 5; //开始时间戳
    required uint32 person_type = 6;     // ChannelVotePKNumType
    optional uint32 vote_cnt = 7; //如果是投票类型的情况，每个观众的票数
    optional string pk_name = 8; // PK名字
    optional uint32 channel_type = 9; // 房间类型
    optional string channel_name = 10; // 房间名
}

// PK全量信息
message ChannelVotePKRankInfo {
    required ChannelPkInfo info = 1;                      // PK基本信息
    repeated ChannelVotePkCompetitor competiter_list = 2; //排行榜列表
}

// PK全量PUSH信息
message ChannelVotePKPushNotifyInfo {
    enum EPKNotifyStatus {
        ENUM_PK_NOTIFY_STATUS_START = 1;   // PK 开始
        ENUM_PK_NOTIFY_STATUS_ING = 2;     // PK 进行中
        ENUM_PK_NOTIFY_STATUS_TIMEOUT = 3; // PK 时间到结束
        ENUM_PK_NOTIFY_STATUS_CANCEL = 4;  // PK 被取消
    }

    required ChannelPkInfo info = 1;                      // PK基本信息
    repeated ChannelVotePkCompetitor competiter_list = 2; //排行榜列表

    optional uint32 notify_status = 3; // EPKNotifyStatus

    // 操作者数据 仅在PK 开始和被取消时 需要填写
    optional uint32 op_uid = 5;      // 操作者UID
    optional string op_nickname = 6; // 操作者昵称
    optional string op_account = 7;
    optional string op_face_md5 = 8; // 操作者头像MD5
}

//房管设置PK开始
message ChannelVotePkStartReq {
    required BaseReq base_req = 1;
    required uint32 channel_id = 2;
    required uint32 duration_min = 3; // 持续分钟 单位分钟
    required uint32 type = 4;         // enum ChannelVotePkType PK类型
    repeated uint32 uid_list = 5;     // 参与UID
    optional uint32 vote_cnt = 6;     // 每个观众的票数
    optional string pk_name = 7;      // PK名
}

message ChannelVotePkStartResp {
    required BaseResp base_resp = 1;
    required uint32 channel_id = 2;
    required uint32 duration_min = 3; //持续分钟
    required uint32 start_timestamp =
            4; //服务端时间，PK开始时间戳
         //channel_id和start_timestamp唯一确定一个PK对象
    required uint32 left_vote = 5; //自己剩余票数
}

//直播房 - 房管设置PK开始
message ChannelLiveVotePkStartReq {
    required BaseReq base_req = 1;
    required uint32 channel_id = 2;
    required uint32 duration_min = 3; // 持续分钟 单位分钟
    required uint32 type = 4;         // enum ChannelVotePkType PK类型
    repeated uint32 uid_list = 5;     // 参与UID
    optional uint32 vote_cnt = 6;     // 每个观众的票数
    optional string pk_name = 7;      // PK名
}

message ChannelLiveVotePkStartResp {
    required BaseResp base_resp = 1;
    required uint32 channel_id = 2;
    required uint32 duration_min = 3; //持续分钟
    required uint32 start_timestamp =
        4; //服务端时间，PK开始时间戳
    //channel_id和start_timestamp唯一确定一个PK对象
    required uint32 left_vote = 5; //自己剩余票数
}


//房管取消PK
message ChannelPkCancelReq {
    required BaseReq base_req = 1;
    required uint32 channel_id = 2;
    required uint32 start_timestamp = 3;
}

message ChannelPKCancelResp {required BaseResp base_resp = 1;}

// 直播房 - 房管取消PK
message ChannelLivePkCancelReq {
    required BaseReq base_req = 1;
    required uint32 channel_id = 2;
    required uint32 start_timestamp = 3;
}

message ChannelLivePKCancelResp {required BaseResp base_resp = 1;}

//  获取PK全量信息,刚进房的时候使用
message GetChannelVotePKInfoReq {
    required BaseReq base_req = 1;
    required uint32 channel_id = 2;
}

message GetChannelVotePKInfoResp {
    required BaseResp base_resp = 1;
    optional uint32 left_vote = 2; // 如果是观众点击投票的PK 这类显示观众的剩余票数
    optional ChannelVotePKRankInfo pk_info = 3; // PK全量信息
    optional uint32 left_extra_vote = 4; // 如果是观众点击投票的PK 这类显示观众的剩余额外票数
}

//  直播房 - 获取PK全量信息,刚进房的时候使用
message GetChannelLiveVotePKInfoReq {
    required BaseReq base_req = 1;
    required uint32 channel_id = 2;
}

message GetChannelLiveVotePKInfoResp {
    required BaseResp base_resp = 1;
    optional uint32 left_vote =
        2; // 如果是观众点击投票的PK 这类显示观众的剩余票数
    optional ChannelVotePKRankInfo pk_info = 3; // PK全量信息
}

// 观众点击投票
message ChannelVotePkVoteReq {
    required BaseReq base_req = 1;
    required uint32 to_uid = 2;   //目标玩家
    required uint32 vote_cnt = 3; //投票数量
    required uint32 channel_id = 4;
    required uint32 start_timestamp = 5; //开始时间戳
}

message ChannelVotePkVoteResp {
    required BaseResp base_resp = 1;
    required uint32 left_vote = 2; //剩余票数
    required uint32 remain_extra_vote = 3; //剩余额外票数
}

// 直播房 - 观众点击投票
message ChannelLiveVotePkVoteReq {
    required BaseReq base_req = 1;
    required uint32 to_uid = 2;   //目标玩家
    required uint32 vote_cnt = 3; //投票数量
    required uint32 channel_id = 4;
    required uint32 start_timestamp = 5; //开始时间戳
}

message ChannelLiveVotePkVoteResp {
    required BaseResp base_resp = 1;
    required uint32 left_vote = 2; //剩余票数
}


// 房间画布
message Point {
    required uint32 x = 1;
    required uint32 y = 2;
}

enum LineType {
    PURE_COLOR = 0; //纯色线
    MULT_COLOR = 1; //荧光笔
    ICON_IMAGE = 2; // icon
    RUBBER = 3;     //橡皮擦
}

//线结构，由很多点组成。
message Line {
    required uint32 lineID = 1;
    required uint32 uid = 2;
    required uint32 size = 3;
    required string color = 4;
    repeated Point PointList = 5;
    optional string para_id = 6;   // LinePara结构ID
    optional uint32 line_type = 7; // enum LineType
}

//画结构，由很多线组成
message Picture {
    required uint32 channelID = 1;
    repeated Line lineList = 2;
}

//撤回一条线
message CancelLineReq {
    required BaseReq base_req = 1;
    required uint32 channelID = 2;
    required uint32 lineID = 3;
}

message CancelLineResp {required BaseResp base_resp = 1;}

//拿整幅画
message GetPictureReq {
    required BaseReq base_req = 1;
    required uint32 channelID = 2;
}

message GetPictureResp {
    required BaseResp base_resp = 1;
    optional Picture picture = 2;
    optional uint32 operID = 3;
}

//房管用于删除整幅画
message RemovePictureReq {
    required BaseReq base_req = 1;
    required uint32 channelID = 2;
}

message RemovePictureResp {required BaseResp base_resp = 1;}

//创建线
message CreatLineReq {
    required BaseReq base_req = 1;
    required uint32 channelID = 2;
    required uint32 size = 3;
    required string color = 4; // para_id没有的情况下，客户端给透明色，兼容旧版本
    optional string para_id = 5;   // LinePara结构para_id
    optional uint32 line_type = 6; // enum LineType
}

message CreatLineResp {
    required BaseResp base_resp = 1;
    required uint32 channelID = 2;
    required uint32 lineID = 3;
    required uint32 operID = 4;
}

//向线中加点
message AddPointListReq {
    required BaseReq base_req = 1;
    required uint32 channelID = 2;
    required uint32 lineID = 3;
    repeated Point pointList = 4;
}

message AddPointListResp {required BaseResp base_resp = 1;}

//清楚某个玩家画的所有线
message CancelByUidReq {
    required BaseReq base_req = 1;
    required uint32 channelID = 2;
}

message CancelByUidResp {required BaseResp base_resp = 1;}

message LinePara {
    required string para_id = 1;
    required string name = 2;
    required string preview_url = 3; //预览图URL
    required string icon_url = 4;    //小图资源 ZIP URL
    repeated string color_list = 5;  // 荧光笔颜色
    required uint32 type = 6;        // enum LineType
    optional string md5 = 7;
}

//取画笔配置列表
message GetLineParaListReq {required BaseReq base_req = 1;}

message GetLineParaListResp {
    required BaseResp base_resp = 1;
    repeated LinePara line_list = 2;
}

enum DRAW_GAME_SWITCH_STATUS {
    ENUM_DRAW_GAME_UNKUNOW = 0;
    ENUM_DRAW_GAME_OFF = 1;
    ENUM_DRAW_GAME_ON = 2;
}

//玩家开始关闭画板。用于确定玩家的画画状态
message SetDrawStatusReq {
    required BaseReq base_req = 1;
    required uint32 channelID = 2;
    required uint32 status = 3; // DRAW_GAME_SWITCH_STATUS
}

message SetDrawStatusResp {
    required BaseResp base_resp = 1;
    required uint32 status = 2;
}

//房管开启关闭画板功能
message SetBoardStatusReq {
    required BaseReq base_req = 1;
    required uint32 channelID = 2;
    required uint32 status = 3; // DRAW_GAME_SWITCH_STATUS
}

message SetBoardStatusResp {
    required BaseResp base_resp = 1;
    required uint32 status = 2;
}

message GetBoardStatusReq {
    required BaseReq base_req = 1;
    required uint32 channelID = 2;
}

message GetBoardStatusResp {
    required BaseResp base_resp = 1;
    required uint32 channelID = 2;
    required uint32 status = 3;
    optional uint32 interval = 4; //加点协议间隔
}

//拿正在绘画UID列表
message GetDrawStatusListReq {
    required BaseReq base_req = 1;
    required uint32 channelID = 2;
}

message GetDrawStatusListResp {
    required BaseResp base_resp = 1;
    repeated uint32 uidList = 2;
}

message ChannelDrawGamePushNotifyInfo {
    enum ENUM_DrawGameNotifyEvent {
        ENUM_DRAW_GAME_NOTIFY_EVENT_ADD_LINE = 1;      // 添加线
        ENUM_DRAW_GAME_NOTIFY_EVENT_CANCEL = 2;        // 取消线
        ENUM_DRAW_GAME_NOTIFY_EVENT_REMOVE = 3;        // 清空图
        ENUM_DRAW_GAME_NOTIFY_EVENT_ADD_POINT = 4;     //向线中加点
        ENUM_DRAW_GAME_NOTIFY_EVENT_CANCEL_BY_UID = 5; //删除UID对应的全部线
        ENUM_DRAW_GAME_NOTIFY_EVENT_STATUS_EVENT = 6; //房主开启关闭画板事件
        ENUM_DRAW_GAME_NOTIFY_EVENT_DRAW_STATUS = 7; //玩家正在画画状态
        ENUM_DRAW_GAME_NOTIFY_EVENT_NOTIFY = 8; // 10分钟后关闭公屏提示
    }

    required uint32 channelID = 1;
    required uint32 event_type = 2; // ENUM_DrawGameNotifyEvent
    required uint32 operID = 3;     //操作ID，每次操作都+1

    repeated Point pointList = 4;
    optional uint32 uid = 5; //操作用户UID
    optional uint32 lineID = 6;
    optional Line line = 7;
    optional uint32 broadStatus = 8; //画板开始、关闭状态 DRAW_GAME_SWITCH_STATUS
    optional uint32 drawStatus = 9; //玩家正在画画状态 DRAW_GAME_SWITCH_STATUS
}

//有颜色配置的公屏消息
message ChannelColorNotifyMsg {
    required uint32 channelID = 1;
    required string msg = 2;
    required string color = 3;
    optional uint32 uid = 4;
    optional string account = 5;
    optional string nickname = 6;
    optional string title = 7;
    optional ga.UserProfile user_profile = 8;
}

// 申请排麦/取消排麦请求 （迁移到GO后新接口使用，为了满足协议规范）
message ChannelNormalQueueUpMicApplyRequest {
    required BaseReq base_req = 1;
    required uint32 channel_id = 2;
    required bool is_cancel = 3; // 0 申请 1 取消
}

message ChannelNormalQueueUpMicApplyResponse {
    required BaseResp base_resp = 1;
    required bool is_cancel = 2;
    repeated LiveChannelApplyMember apply_user_list = 3;
}

// 获取排麦列表请求（迁移到GO后新接口使用，为了满足协议规范）
message ChannelNormalQueueUpMicListRequest {
    required BaseReq base_req = 1;
    required uint32 channel_id = 2;
    required uint32 offset = 3;
    required uint32 limit = 4;
}

message ChannelNormalQueueUpMicListResponse {
    required BaseResp base_resp = 1;
    required uint32 channel_id = 2;
    repeated LiveChannelApplyMember apply_user_list = 3;
    required uint32 all_apply_cnt = 4;
    required uint32 offset = 5;
    required uint32 limit = 6;
}

// 普通房间的排麦申请 （或者取消申请）
message ChannelNormalQueueUpMicApplyReq {
    required BaseReq base_req = 1;
    required uint32 channel_id = 2;
    required bool is_cancel = 3; // 0 申请 1 取消
}

message ChannelNormalQueueUpMicApplyResp {
    required BaseResp base_resp = 1;
    required bool is_cancel = 2;
    repeated LiveChannelApplyMember apply_user_list = 3;
}

// 获取普通房间的排麦列表
message GetChannelNormalQueueUpMicListReq {
    required BaseReq base_req = 1;
    required uint32 channel_id = 2;
    required uint32 offset = 3;
    required uint32 limit = 4;
}
message GetChannelNormalQueueUpMicListResp {
    required BaseResp base_resp = 1;
    required uint32 channel_id = 2;
    repeated LiveChannelApplyMember apply_user_list = 3;
    required uint32 all_apply_cnt = 4;
    required uint32 offset = 5;
    required uint32 limit = 6;
}

/*------------------------------------------------ 开黑新人推荐房
 * 房主设置展示状态 ------------------------------*/
enum NOVICE_RECOMMEND_CHANNEL_DISPLY_STATUS {
    ENUM_NOT_RECOMMEND = 0;     // 不是新人开黑推荐房
    ENUM_RECOMMEND_DISPLAY = 1; // 在新人推荐位展示
    ENUM_RECOMMEND_HIDE = 2;    // 不在新人推荐位展示
}

message GetNoviceRecommendChannelStatusReq {
    required BaseReq base_req = 1;
    required uint32 cid = 2;
}

message GetNoviceRecommendChannelStatusResp {
    required BaseResp base_resp = 1;
    required uint32 recommend_status =
            2; // see NOVICE_RECOMMEND_CHANNEL_DISPLY_STATUS
}

/*------------------------------------------------ 开黑新人推荐房
 * 房主设置展示状态 ------------------------------*/

message ModifyChannelIMMsg {
    required uint32 seq_id = 1;     //消息序列
    required uint32 channel_id = 2; //房间消息
    required bytes bin_msg = 3;     // channelim::ChannelCommonMsg 序列化;
}

/*------------entertainmentRecommendSvr begin----------------*/

enum ChannelCategory {
    Activity_BIG = 0;   //大活动房
    Activity_SAMLL = 1; //小活动房
    Used_Enter = 2;     //曾经进入过
    Normal_Level = 3;   //普通等级房
    Hot_Channel = 4;    //热门房
    Roi_Channel = 5;  // roi承接房间
    Guild_Channel = 6;     //公会房区域
    Relationship_Channel = 7; //关注链房
    HighQualityPgc_Channel = 8; //指定优质PGC房
}

//房间0麦位玩家信息
message McSimpleInfo {
    required uint32 uid = 1;

    optional string account = 2;
    optional string nick_name = 3;
    optional string head_img = 4;  // md5
    optional string game_url = 5; // 游戏图片
    optional int32  sex = 6;   // 性别
}

//房间头像、房间名等channel服务数据
message ChannelCommonBaseInfo {
    required uint32 channel_id = 1;
    required string name = 2;
    required string icon_md5 = 3;   //房间头像
    required uint32 online_cnt = 4; //在线人数
    required uint32 display_id = 5; // 显示Id

    optional uint32 app_id = 6;       // 频道对应的APPID
    optional bool has_pwd = 7;        // 是否有密码
    optional uint32 channel_type = 8; // 频道类型 see ga::ChannelType
    optional uint32 bind_id = 9;
    optional uint32 switch_flag = 10; // 各种房间开关的标记
    optional uint32 creater_uid = 11;
    optional uint32 create_ts = 12;

    optional string topic_title = 13; // 房间话题描述（标题）
    optional string passwd = 14;      // 密码 明文
}

//房间等级、tagID等信息，entertainmentRecommend服务数据
message ChannelRecommendSimpleInfo {
    required uint32 channel_level = 1; // enum ChannelLevel
    required uint32 category = 2;      // enum ChannelCategory 房间类型
    required uint32 tag_id = 3;        // tag_id
    optional string sub_tag = 4;       //配置的一个标签，TOP10什么的
    optional string tag_name = 5;
    optional string tag_intro = 6;  // 标签简介
}

// 主播认证标识
message AnchorCertInfo {
    optional string item_name = 1;           // 标识名称
    optional string base_imgurl = 2;         // 标识底图
    optional string shadow_color = 3;
}

//PK类型
enum EChannelPkType{
    ENUM_PK_TYPE_INVALID = 0;  //无效
    ENUM_PK_TYPE_SINGLE = 1;  // 单人PK
    ENUM_PK_TYPE_MULTI_2V2 = 2;  // 多人pk 2v2
    ENUM_PK_TYPE_MULTI_1V1 = 3;  // 多人pk 1v1
}

message PgcChannelExtraInfo {
    optional uint32 category = 1;   // enum ChannelCategory 房间类型
    optional uint32 pk_status = 2;  // see EChannelPkStatusType
    optional string official_identity_text = 3;  // 官频标识文案
    optional bool is_lottery = 4;    // 是否在抽奖
    optional string sound_label = 5;  //  声音标签
    repeated AnchorCertInfo cert_info_list = 6;  // 主播认证标识
    optional bool is_play_red_packet = 7;  // 是否在玩红包
    optional int32 creator_sex = 8; // 0:女 1:男
    optional string lottery_cert = 9;  // 抽奖标识内容
    optional uint32 channel_pk_type = 10;  // see EChannelPkType
    optional string game_cert_url = 11;  // 弹幕游戏标识
    optional string roi_url = 12;  // roi承接房间标识
    optional McSimpleInfo mc_simple_info = 13;  // 0 号麦位用户信息
}

// 房间加权标签
message ChannelWeightCert{
    optional string icon = 1;
    optional string text = 2;
    repeated string color = 3;
    optional string text_shadow_color = 4;
}

// 房间类型标签
message ChannelTypeCert{
    optional string icon = 1;
    optional string text = 2;
    repeated string color = 3;
    optional string icon_md5 = 4; 
}

//推荐池来源 废弃
enum ERecPoorSource{
    ENUM_REC_POOR_SOURCE_INVALID = 0;  //无效
    ENUM_REC_POOR_SOURCE_UGC_REC = 1;  // ugc首页推荐池
    ENUM_REC_POOR_SOURCE_UGC_KING = 2;  // ugc王者推荐池
}

//推荐池来源 
enum ERecPoorSourceV2{
    ENUM_REC_POOR_SOURCE_V2_INVALID = 0;  //无效
    ENUM_REC_POOR_SOURCE_V2_BASE = 1;  //兜底推荐池
    ENUM_REC_POOR_SOURCE_V2_RELATIONSHIP = 2;  // 关系链推荐池
    ENUM_REC_POOR_SOURCE_V2_LIST_CONSUME = 3;    //推荐列表-曾经付费
    ENUM_REC_POOR_SOURCE_V2_LIST_ENTER   = 4;    //推荐列表-曾经看过
    ENUM_REC_POOR_SOURCE_V2_LIST_NO_ENTER  = 5;  //推荐列表-未曾看过
    ENUM_REC_POOR_SOURCE_V2_TOPWIN_CONSUME = 6;  //顶部浮窗-曾经付费
    ENUM_REC_POOR_SOURCE_V2_TOPWIN_ENTER   = 7;  //顶部浮窗-曾经看过
    ENUM_REC_POOR_SOURCE_V2_TOPWIN_NO_ENTER= 8;  //顶部浮窗-未曾看过
}

//应用场景类型
enum ESceneType {
   ENUM_SCENE_TYPE_INVALID = 0; 
   ENUM_SCENE_TYPE_UGC_REC = 1; // ugc首页推荐
   ENUM_SCENE_TYPE_UGC_KING = 2; // ugc王者推荐
}

message RecommendChannelInfo {
    required ChannelCommonBaseInfo channel_base_info = 1;
    required ChannelRecommendSimpleInfo channel_simple_info = 2;
    optional McSimpleInfo mc_simple_info = 3;

    enum EChannelPkStatusType {
        ENUM_STATUS_PK_CLOSE = 0;   // 不在pk
        ENUM_STATUS_PK_OPEN = 1;    // 在pk
    }
    optional uint32 pk_status = 4;  // see EChannelPkStatusType

    optional string official_identity_text = 5;  // 官频标识文案
    optional bool is_lotterying = 6;    // 是否在抽奖

    optional string sound_label = 7;  //  声音标签

    repeated AnchorCertInfo cert_info_list = 8;  // 主播认证标识

    optional bool is_play_red_packet = 9;  // 是否在玩红包

    optional int32 creater_sex = 10; // 0:女 1:男

    optional string lottery_cert = 11;  // 抽奖标识内容

    optional uint32 channel_pk_type = 12;  // see EChannelPkType

    optional string game_cert_url = 13;  // 弹幕游戏标识

    optional string roi_url = 14;  // roi承接房间标识

    repeated RealTimeChannelDetail real_time_channel_detail = 15; // 实时房间信息
    repeated RealTimeChannelMicDetail real_time_channel_mic_detail = 16; // 实时麦位信息

    optional bool is_follow = 17;  // 是否关注

    optional ChannelWeightCert weight_cert = 18;  // 加权标签 

    optional  ChannelTypeCert type_cert = 19;  // 类型标签

    optional uint32 rec_poor_source = 20; // 推荐池来源 see ERecPoorSourceV2

    optional uint32 scene_type = 21;  // 应用场景类型 see ESceneType

    optional RecommendGlorySubscriptInfo glory_subscript_info = 22;  // 推荐荣誉角标信息
    optional string hot_wedding_label = 23; // 热门婚礼标识url 
}

// 推荐荣誉角标信息
message RecommendGlorySubscriptInfo {
    required string bg_url = 1;  // 背景图
    required string text = 2;    // 文字
}

message RealTimeChannelDetail{
    enum RealTimeDetailType{
        ENUM_REAL_TIME_DETAIL_TYPE_INVALID = 0;
        ENUM_REAL_TIME_DETAIL_TYPE_RANK = 1; // 排行榜信息
        ENUM_REAL_TIME_DETAIL_TYPE_RED_PACKET = 2; // 发红包
        ENUM_REAL_TIME_DETAIL_TYPE_HIGH_VALUE_PRESENT = 3; // 高额送礼
        ENUM_REAL_TIME_DETAIL_TYPE_CHANNEL_DATING = 4; // 相亲交友
        ENUM_REAL_TIME_DETAIL_TYPE_CP_GAME = 5; // CP战
        ENUM_REAL_TIME_DETAIL_TYPE_OFFERING_ROOM = 6; // 拍拍
        ENUM_REAL_TIME_DETAIL_TYPE_THROW_BOMB = 7; // 甩雷
        ENUM_REAL_TIME_DETAIL_TYPE_DIGITAL_BOMB = 8; //  数字炸弹
        ENUM_REAL_TIME_DETAIL_TYPE_GAME_ADVENTURE = 9; //  大冒险
        ENUM_REAL_TIME_DETAIL_TYPE_ADD_FRIEND = 10; //  扩列
        ENUM_REAL_TIME_DETAIL_TYPE_KICK_PROTECT = 11; // 踢保
        ENUM_REAL_TIME_DETAIL_TYPE_KTV = 12; // 点唱
        ENUM_REAL_TIME_DETAIL_TYPE_PARTY = 13; // 排队
        ENUM_REAL_TIME_DETAIL_TYPE_STORY = 14; // 小故事
        ENUM_REAL_TIME_DETAIL_TYPE_CARRY_GAME = 15; // 大神带飞
        ENUM_REAL_TIME_DETAIL_TYPE_PIA = 16; // pia戏
        ENUM_REAL_TIME_DETAIL_TYPE_FRIEND_MIC = 17;     //玩伴在麦
        ENUM_REAL_TIME_DETAIL_TYPE_FRIEND_CHANNEL = 18; //玩伴在房
        ENUM_REAL_TIME_DETAIL_TYPE_COLLECT = 19;        //我的收藏
        ENUM_REAL_TIME_DETAIL_TYPE_FOLLOW  = 20;        //我的关注
        ENUM_REAL_TIME_DETAIL_TYPE_USED_ENTER = 21;      //曾经来过
        ENUM_REAL_TIME_DETAIL_TYPE_LOTTERY = 22;      //抽奖
        ENUM_REAL_TIME_DETAIL_TYPE_LIVE_GAME = 23;    //弹幕游戏
        ENUM_REAL_TIME_DETAIL_TYPE_LIVE_MUSIC = 24;   //音乐直播房
        ENUM_REAL_TIME_DETAIL_TYPE_LIVE_EMO = 25;     //情感
        ENUM_REAL_TIME_DETAIL_TYPE_LIVE_ACG = 26;    //二次元
        ENUM_REAL_TIME_DETAIL_TYPE_LIVE_STORY = 27;  //故事
        ENUM_REAL_TIME_DETAIL_TYPE_LIVE_TALK = 28;   //脱口秀
        ENUM_REAL_TIME_DETAIL_TYPE_LIVE_VIRTUAL_INTERACTION = 29; //虚拟互动
        ENUM_REAL_TIME_DETAIL_TYPE_WEDDING_CHANNEL = 30; // 婚礼房
    }

    required uint32 detail_type = 1;
    required string detail_content = 2;
    required string detail_icon = 3; // 详情图标
    required string detail_background_color = 4; // 详情背景色
    required string detail_font_color = 5;  // 详情字体颜色
}

message RealTimeChannelMicDetail{
    required uint32 mic_id = 1;
    required uint32 uid = 2;
    required string nickname = 3;
    required string head_img = 4;  // md5
    required uint32 sex = 5;  // 性别：0-女 1-男
    required string account = 6;
}

// 推荐房间额外信息
message RecommendExtendInfo{
    enum RecommendExtendType{
        ENUM_RECOMMEND_EXTEND_TYPE_INVALID = 0;
        ENUM_RECOMMEND_EXTEND_TYPE_RANK = 1;  // 魅力榜/财富榜
        ENUM_RECOMMEND_EXTEND_TYPE_ADV = 2;   // 广告位
        ENUM_RECOMMEND_EXTEND_TYPE_GROUP = 3; // 推荐房间组
    }
    required uint32 extend_type = 1; //  see RecommendExtendType，然后只取对应type的信息
    required uint32 index = 2; // 插入的位置，在当前页第x个推荐房间之后（0则显示在顶端）
    repeated RecommendChannelRankInfo rank_info = 3; // 魅力榜/财富榜信息，一个数组，分别包含两榜的信息
    repeated ChannelAdv adv_info = 4; // 广告位信息，数组，包含所有广告位
    optional RecommendChannelGroupInfo group_info = 5; // 推荐房间组信息
}

message RecommendChannelRankInfo{
    required uint32 rank_type = 1; // 1:魅力榜 2:财富榜 3:陪伴榜
    repeated RecommendChannelRankUserInfo rank_list = 2;
    optional RecommendAccompanyRankInfo accompany_rank_info = 3; // 陪伴榜使用
}

message RecommendChannelRankUserInfo{
    required uint32 uid = 1;
    required string nickname = 2;
    required string head_img = 3; // md5
    required uint32 rank = 4;  // 排名
    optional string account = 5;
}

message RecommendAccompanyRankInfo {
    // 陪伴用户信息
    message AccompanyUserInfo {
        required uint32 uid = 1;
        required string nickname = 2;
        required string head_img = 3;
        optional string account = 4;
    }
    // 陪伴小屋
    message AccompanyHouse {
        enum AccompanyHouseResourceType {
            ACCOMPANY_HOUSE_RESOURCE_TYPE_UNSPECIFIED = 0;
            ACCOMPANY_HOUSE_RESOURCE_TYPE_VAP = 1;  // vap
            ACCOMPANY_HOUSE_RESOURCE_TYPE_LOTTIE = 2; // lottie
        }
        required AccompanyHouseResourceType resource_type = 1; // 资源类型
        required string resource_url = 2; // 资源url
        required string md5 = 3; // 资源md5
        required string icon = 4; // 资源首帧图
        required string house_name = 5; // 房子名字
    }

    required AccompanyUserInfo uidA = 1; // 陪伴用户A
    required AccompanyUserInfo uidB = 2; // 陪伴用户B
    required AccompanyHouse house = 3; // 陪伴小屋
}

// 推荐房间组信息
message RecommendChannelGroupInfo {
    required uint32 group_id = 1;
    optional string group_name = 2;  // 组名
    optional string sub_name = 3;   // 子标题
    optional string jump_url = 4;   // 跳转链接
    repeated GroupChannelInfo channel_list = 5; // 房间列表
    optional string background_img = 6;  // 背景图
}

message GroupChannelInfo{
    required uint32 channel_id = 1;
    optional string channel_name = 2;
    optional string channel_img = 3;   // 房间封面
    optional string channel_jump_url = 4;  // 房间跳转链接
    optional string channel_mic_name = 5;  // 房间0号麦昵称
    optional string channel_hot_value = 6;  // 房间热度值
    optional uint32 channel_type = 7; // 房间类型
    optional string creator_account = 8; // 创建者的账号
    optional uint32 guild_id = 9; // 公会ID
    optional uint32 bind_id = 10;
    optional string icon_md5 = 11; // 房间头像
}

// tag配置
message ChannelTagConfigInfo {
    enum TagType {
      TagType_No = 0;         // 普通
      TagType_Wedding = 1;  // 婚礼房标签
    }

    required uint32 tag_id = 1;
    required string name = 2;
    required string bk_color = 3;
    optional string icon = 4;
    repeated string multi_color = 5;
    optional uint32 tag_type = 6; // 标签类型 see TagType
}

// 娱乐tab的二级web界面配置
message ChannelTagWebConfig
{
    required string name = 1;
    required string jump_url = 2;
}

// 个性标签配置
message ChannelPersonalityTag
{
   optional uint32 personality_tag_id = 1;
   optional string personality_tag_name = 2;
}

// 多级tab配置
message ChannelMultiTagConfigInfo {
    required uint32 root_tag_id = 1;
    required string root_tab_name = 2;          // 一级标签名称
    repeated ChannelTagConfigInfo sub_tab_list = 3;   // 二级标签配置
    repeated ChannelPersonalityTag personality_tag_list = 4;  // 个性标签配置
}


//请求推荐房间数据协议
message GetRecommonChannelListReq {
    enum HomeType {
       HOME_ENTERTAIN = 0;  // 娱乐
       HOME_MUSIC = 1; // 音乐
       HOME_GAME = 2;  // 开黑
    }
    required BaseReq base_req = 1;
    required uint32 start = 2; //偏移
    required uint32 count = 3; //数量
    optional uint32 home_type = 4;   //首页类型
}

message GetRecommonChannelListResp {
    required BaseResp base_resp = 1;
    repeated RecommendChannelInfo recommon_channel_list = 2;
    required bool is_end = 3; //是否到头了
}

//按tagID请求房间数据
message GetRecommonChannelListByTagIdReq {
    required BaseReq base_req = 1;
    required uint32 start = 2; //偏移
    required uint32 count = 3; //数量
    required uint32 tag_id = 4;
}

message GetRecommonChannelListByTagIdResp {
    required BaseResp base_resp = 1;
    repeated RecommendChannelInfo recommon_channel_list = 2;
    required bool is_end = 3; // 是否到头了
}

//根据个性tagid请求房间数据
message GetRecChListByPerTagIdReq {
    required BaseReq base_req = 1;
    optional uint32 start = 2; //偏移
    optional uint32 count = 3; //数量
    optional uint32 personality_tag_id = 4;
}

message GetRecChListByPerTagIdResp {
    required BaseResp base_resp = 1;
    repeated RecommendChannelInfo recommon_channel_list = 2;
    required bool is_end = 3; // 是否到头了
}

//新版本娱乐TAB-推荐列表协议
message GetRecommonChannelListV2Req {
    required BaseReq base_req = 1;
    required uint32 start = 2; //偏移
    required uint32 count = 3; //数量
    optional uint32 real_start = 4; // 客户端实际偏移，仅用于插入额外信息
}
message GetRecommonChannelListV2Resp {
    required BaseResp base_resp = 1;
    repeated RecommendChannelInfo recommon_channel_list = 2;
    required bool is_end = 3; //是否到头了
    repeated RecommendExtendInfo extend_info_list = 4;
    optional bool show_recommend_icon = 5; //展示推荐标识
}

//拿tag数据
message GetChannelTagInfoReq {required BaseReq base_req = 1;}

message GetChannelTagInfoResp {
    required BaseResp base_resp = 1;
    repeated ChannelTagConfigInfo channel_tag_list = 2;
    repeated ChannelTagWebConfig tag_web_list = 3;
    repeated ChannelMultiTagConfigInfo multi_tag_list = 4;
    repeated ChannelTagConfigInfo extra_tag_list = 5;
}

//拿房间tag数据
message GetTagInfoByChannelIdReq
{
    required BaseReq base_req = 1;
    required uint32 channel_id = 2;
}
message GetTagInfoByChannelIdResp
{
    required BaseResp base_resp = 1;
    required uint32 tag_id = 2;
    required string name = 3;

    optional uint32 parent_tag_id = 4;   // 父tagId
    optional string parent_tag_name = 5; // 父tag名字
}


//拿热门数据
message GetHotChannelReq {required BaseReq base_req = 1;}

message GetHotChannelResp {
    required BaseResp base_resp = 1;
    repeated RecommendChannelInfo recommon_channel_list = 2;
    optional bool is_end = 3;
}

message GetIsOpenEntertainmentTagReq {required BaseResp base_req = 1;}

message GetIsOpenEntertainmentTagResp {
    required BaseResp base_resp = 1;
    required bool is_open = 2;
}

message QuickEntryConfigInfo
{
    enum EEntrySceneType {
        ENUM_SCENE_CHANNEL = 1;   //房间
        ENUM_SCENE_ACTIVITY = 2;  //活动页
        ENUM_SCENE_PERFECT_MATCH = 3;  // 天配
    }
    enum EDyncmicType {
        ENUM_DYNCMIC_TYPE_INVALID = 0;
        ENUM_DYNCMIC_TYPE_LOTTIE = 1;  // LOTTIE
        ENUM_DYNCMIC_TYPE_VAP = 2; // VAP
    }
    required uint32 scene_type = 1; // see EEntrySceneType
    required string name = 2;
    required string intro = 3;
    required string big_img_url = 4;
    required string small_img_url = 5;
    optional string jump_url = 6;
    optional uint32 online_num = 7;
    optional string dyncmic_effect_url = 8;   //动效url
    optional string dyncmic_effect_md5 = 9;   //动效md5
    optional uint32 tag_id = 10;
    optional string static_bg_url = 11;    //静态背景图
    optional string dyncmic_effect_json = 12;  // 动态图json配置
    optional string sub_title = 13;  // 副标题
    optional uint32 dyncmic_type = 14; // 动效类型:EDyncmicType
}

//获取娱乐tab快速入口场景配置
message GetQuickEntryConfigReq
{
    required BaseReq base_req = 1;
    required uint32 refresh_count = 2;
}
message GetQuickEntryConfigResp
{
    enum EQuickEntryType {
        ENUM_QUICK_ENTRY_COMMON = 1;   //通用类型
        ENUM_QUICK_ENTRY_SCENE_PERFECT_MATCH = 2;  // 天配
    }
    required BaseResp base_resp = 1;
    repeated QuickEntryConfigInfo entry_info_list = 2;
    required uint32 refresh_count = 3;
    optional uint32 config_type = 4;  // see EQuickEntryType
}

//根据tagId获取快速入口推荐房间
message GetQuickRecChannelByTagIdReq
{
    required BaseReq base_req = 1;
    required uint32 tag_id = 2;
}
message GetQuickRecChannelByTagIdResp
{
    required BaseResp base_resp = 1;
    required uint32 channel_id = 2;
}


// 战歌处推荐房间标签信息
message LabelInfo {
    required string background_color = 1;   // 底图颜色
    required string text_color = 2;         // 文字颜色
    required string content = 3;      // 文字内容
}

// 战歌处房间推荐信息
message WarSongRecommendInfo {
    required uint32 channel_id = 1;
    required uint32 uid = 2;
    required string account = 3;
    required int32 sex = 4;
    required LabelInfo label_info = 5;
    required string intro = 6;  //简介
    optional ChannelCommonBaseInfo channel_base_info = 7;   // 房间信息，推荐房间是官频才会填，用于获取房间头像信息
}

// 获取战歌处房间推荐列表
message GetWarSongRecommonChannelListReq {
    required BaseReq base_req = 1;
    optional uint32 offset = 2;
}

message GetWarSongRecommonChannelListResp {
    required BaseResp base_resp = 1;
    repeated WarSongRecommendInfo channel_list = 2;
    required bool is_end = 3;
    optional uint32 next_offset = 4;
}

/*------------entertainmentRecommendSvr end----------------*/

enum ActivityRankTypeEnum {
    YearActivityRank = 0; //年度活动排行榜
}

message ActivityRankChannelInfo {
    required ChannelCommonBaseInfo channel_base_info = 1;
    required ChannelRecommendSimpleInfo channel_simple_info = 2;
    optional McSimpleInfo mc_simple_info = 3;
}

message GetActivityRankChannelReq {
    required BaseReq base_req = 1;
    required uint32 rank_type = 2; //排行榜类型，见ActivityRankTypeEnum
    required uint32 page = 3;
    required uint32 count = 4;
}

message GetActivityRankChannelResp {
    required BaseResp base_resp = 1;
    repeated ActivityRankChannelInfo activity_rank_channel_list = 2;
    optional bool is_end = 3;
}

enum InVisibleStatusType {
    global_status = 0; //全局
    temp_status = 1;   //临时
}

message SetChannelVisibleStatusReq {
    required BaseReq base_req = 1;
    required bool visible_status = 2; //是否隐身
    optional uint32 status_type = 3;  // enum InVisibleStatusType
    optional uint32 channel_id = 4;   //在哪个房间临时隐身
}

message SetChannelVisibleStatusResp {
    required BaseResp base_resp = 1;
    required int32 status = 2; //设置完成后服务端当前的状态
}

message GetTrumpetLeftCntReq {required BaseReq base_req = 1;}

message GetTrumpetLeftCntResp {
    required BaseResp base_resp = 1;
    optional uint32 left_cnt = 2;
}

message NobilityChannelNotifyMsg {
    enum ENUM_NobilityChannelNotifyType {
        ENUM_NOBILITY_LEVEL_UP = 1;     // 升级
        ENUM_NOBILITY_UP_LEFT = 2;      //升级进度,还差多少升下一级
        ENUM_NOBILITY_KEEP_LEFT = 3;    //还差多少保级成功
        ENUM_NOBILITY_KEEP_SUCCESS = 4; //保级成功
        ENUM_NOBILITY_VALUE_ADD = 5; //贵族值增加
    }

    required uint32 uid = 1;
    required string nick_name = 2;
    required uint32 channel_id = 3;
    required uint32 event_type = 4; // enum ENUM_NobilityChannelNotifyType
    optional uint32 nobility_level = 5;  //6.49.0客户端开始使用新的nobility_info字段
    optional string nobility_level_name = 6;
    optional uint32 left = 7; //由event_type定，如果是保级就是还差多少保级成功，如果是升级就差多少升级成功
    optional string account = 8;
    optional uint32 add_value = 9; //增加的贵族值
    optional string msg = 10; //展示的消息

    optional UserUKWInfo ukw_info = 11; // 神秘人信息
    optional UserProfile ukw_profile = 12; // 神秘人用户信息
    optional NobilityInfo nobility_info = 13; // 贵族等级信息
}

/*------------channel inner recommend ----------------*/
enum RcmdChannelLiveStatus {
    LIVE_STATUS_DEFAULT = 0;
    LIVE_STATUS_OPEN = 1;
    LIVE_STATUS_PAUSE = 2;
    LIVE_STATUS_CONTINUE = 3;
    LIVE_STATUS_PK = 11;
}

message ChannelInnerRecommendPositionInfo {
    required uint32 channel_id = 1;
    optional uint32 display_id = 2; // 显示Id
    optional string icon_md5 = 3;   // 房间图标
    optional string sub_tag = 4;    // 左上角
    optional uint32 tag_id = 5;
    optional uint32 channel_members_cnt = 6; // 麦下人数 总人数
    optional string channel_name = 7;
    optional string owner_account = 8;
    optional uint32 channel_type = 9;
    optional RcmdChannelLiveStatus channel_live_status = 10; // 直播状态
    optional string official_identity_text = 11;  // 官频标识文案
    optional uint32 channel_pk_type = 12;  // see EChannelPkType
}


enum ChannelRecommendChannelType {
    CHANNEL_TYPE_ENTERTAINMENT = 0;
    CHANNEL_TYPE_LIVE = 1;
}

// 获取房间内推荐房
message GetChannelInnerRecommendReq {
    required BaseReq base_req = 1;
    required uint32 channel_id = 2;
    optional uint32 page = 3;   // default = 0
    optional uint32 limit = 4;    // default = 0
    optional ChannelRecommendChannelType channel_type = 5; // 区分娱乐房和语音直播房 和resp的不同
}

message GetChannelInnerRecommendResp {
    required BaseResp base_resp = 1;
    repeated ChannelInnerRecommendPositionInfo infos = 2;
    optional bool is_end = 3;
}
/*------------channel inner recommend end----------------*/
// 获取麦上其他的信息，包括游戏昵称等
message ChannelGetMicExternInfoReq {
    required BaseReq base_req = 1;
    required uint32 channel_id = 2;
}
message MicrExternInfo
{
    required uint32 mic_id = 1;              //麦位ID 1 - 9
    required uint32 mic_uid = 2;             //麦上用户uid
    required string game_nick = 3;           //麦上游戏昵称
}
message ChannelGetMicExternInfoResp {
    required BaseResp base_resp = 1;
    required uint32 channel_id = 2;
    repeated MicrExternInfo micr_info_list = 3;
}
//公屏发送游戏昵称
// 请求发送房间消息
message SendChannelGameNickReq {
    required BaseReq base_req = 1;
    required uint32 channel_id = 2;
    required string game_nick = 3; // 需要继续填该字段
    optional uint32 origin = 4; // 消息来源,0/1：App， 2：语音球
    optional ChannelGameNickOpt game_nick_opt = 5;
}


message SendChannelGameNickResp {
    required BaseResp base_resp = 1;
}

// 获得房间麦上用户游戏展示信息
message GetChannelMicroUserGameCardReq {
    required BaseReq base_req = 1;
    required uint32 channel_id = 2;

    // uid_list 非空，则不会查当前麦上人的卡片，uid_list
    // 为空，则查询当前麦上人卡片,uid_list 限制最大查 8 个
    repeated uint32 uid_list = 3;
}
message GetChannelMicroUserGameCardResp {
    required BaseResp base_resp = 1;
    repeated ChannelMicroUserGameTag game_tag_list = 2; // 麦上用户的游戏卡片信息
}

//  CHANNEL_MEMBER_INFLATE = 243; // 房间灌水消息
message ChannelMemberInflateNotifyInfo {
    required uint32 channel_id = 1;
    optional uint32 member_cnt = 2; /* 灌水后的人数 */
}

// 简单进房消息：用于APP退后台或收起房间时，房客进房通知栏通知或者弹窗通知
message ChannelEnterSimpleMsg
{
    required uint32 uid = 1;
    required string nickname = 2;
    required int32  sex = 3;            // 性别：0-女 1-男
    required string username = 4;
    required uint32 channel_id = 5;
    optional uint32 channel_type = 6;
    optional uint64 ts_ms = 7;          // 毫秒时间戳
}

/* 麦位名称 begin */
message MicName {
    required uint32 mic_id = 1;    // 麦位ID
    required string mic_name = 2;  // 麦位名称
}

message SchemeMicName {
    required uint32 scheme_id = 1;
    repeated MicName mic_names = 2;
    required int64 last_operate_time = 3;  // 该玩法麦位名称的最后修改时间
}

// 麦位名称更新事件通知
message ChannelMicNameUpdateEvent {
    enum EventType {
        Unknown = 0;
        SwitchUpdate = 1;        // 麦位名称开关状态更新
        MicNameUpdate = 2;       // 麦位名称更新
    }
    required EventType event = 1;       // 变更事件类型
    required bool switch_state = 2;     // 开关状态，true为打开（打开时会携带所有麦位的名称数据）
    required uint32 operator_uid = 3;   // 变更操作者UID
    required int64 operate_time = 4;    // 变更时间
    required uint32 scheme_id = 5;      // 玩法id
    repeated MicName mic_names = 6;     // 麦位名称
    required uint32 channel_id = 7;     // 房间ID
    optional bool audit_result = 8;     // 审核结果，true为通过
}

// 获取房间麦位名称
message GetChannelMicNameRequest {
    required BaseReq base_req = 1;
    optional uint32 channel_id = 2;     // 房间ID
    optional uint32 scheme_id = 3;      // 玩法ID，获取指定玩法的麦位名称
    optional bool is_get_all = 4;       // 是否获取全部玩法的麦位名称
}
message GetChannelMicNameResponse {
    required BaseResp base_resp = 1;
    optional uint32 channel_id = 2;              // 房间ID
    optional bool switch_state = 3;              // 开关状态，true为打开
    repeated SchemeMicName scheme_mic_names = 4; // 玩法麦位名称信息
    optional int64 last_switch_operate_time = 5; // 上一次操作开关状态的时间
    optional int64 last_modified_time = 6;       // 麦位名称全部数据的最后修改时间（开关的修改也包含在内）
}

// 设置麦位名称信息
message SetChannelMicNameRequest {
    required BaseReq base_req = 1;
    optional uint32 channel_id = 2;         // 房间ID
    optional uint32 uid = 3;                // 操作者UID
    optional uint32 scheme_id = 4;          // 玩法ID
    repeated MicName mic_names = 5;         // 麦位名称信息
}
message SetChannelMicNameResponse {
    required BaseResp base_resp = 1;
    optional uint32 channel_id = 2;     // 房间ID
}

// 【废弃，命名有歧义】
message SetChannelMicSwitchRequest {
    required BaseReq base_req = 1;
    optional uint32 channel_id = 2;         // 房间ID
    optional uint32 uid = 3;                // 操作者UID
    optional bool state = 4;                // 开关状态，true-开启，false-关闭
}
// 【废弃，命名有歧义】
message SetChannelMicSwitchResponse {
    required BaseResp base_resp = 1;
    optional uint32 channel_id = 2;     // 房间ID
}

// 设置麦位名称开关状态
message SetChannelMicNameSwitchRequest {
    required BaseReq base_req = 1;
    optional uint32 channel_id = 2;         // 房间ID
    optional uint32 uid = 3;                // 操作者UID
    optional bool state = 4;                // 开关状态，true-开启，false-关闭
}
message SetChannelMicNameSwitchResponse {
    required BaseResp base_resp = 1;
    optional uint32 channel_id = 2;     // 房间ID
}
/* 麦位名称 end */

message CommonHighLightChannelIm{
    required uint32 uid = 1;
    required uint32 cid = 2;
    required string content = 3;
    required string high_light_content = 4;  // 高亮的内容仅支持匹配第一个
    required string jump_url = 5;
    optional string font_color = 6;     // 字体颜色
    optional string border_color = 7;   // 边框颜色
    optional string high_light_color = 8;   // 高亮字体颜色
    optional string background_color = 9;   // 背景颜色
}