syntax = "proto3";

package ga.api.offer_room;

import "api/extension/extension.proto";
import "offer_room/offer_room.proto";

option go_package = "golang.52tt.com/protocol/app/api/offer_room;offer_room";
option java_package = "com.quwan.tt.proto.api";
option objc_class_prefix = "RPC";


service OfferRoom {
    option (ga.api.extension.logic_service_language) = "go";
    option (ga.api.extension.logic_service_name) = "offer-room-logic";

    // 获取报名队列
    rpc OfferRoomGetApplyList (ga.offer_room.OfferRoomGetApplyListRequest) returns (ga.offer_room.OfferRoomGetApplyListResponse) {
        option (ga.api.extension.command) = {
            id: 96030;
        };
    };
    // 用户报名
    rpc OfferRoomUserApply (ga.offer_room.OfferRoomUserApplyRequest) returns (ga.offer_room.OfferRoomUserApplyResponse) {
        option (ga.api.extension.command) = {
            id: 96031;
        };
    };
    // 用户取消报名
    rpc OfferRoomUserCancelApply (ga.offer_room.OfferRoomUserCancelApplyRequest) returns (ga.offer_room.OfferRoomUserCancelApplyResponse) {
        option (ga.api.extension.command) = {
            id: 96032;
        };
    };
    // 获取当前游戏信息
    rpc OfferRoomGetCurOfferingGameInfo (ga.offer_room.OfferRoomGetCurOfferingGameInfoRequest) returns (ga.offer_room.OfferRoomGetCurOfferingGameInfoResponse) {
        option (ga.api.extension.command) = {
            id: 96033;
        };
    };
    // 获取拍卖配置
    rpc OfferRoomGetOfferingConfig (ga.offer_room.OfferRoomGetOfferingConfigRequest) returns (ga.offer_room.OfferRoomGetOfferingConfigResponse) {
        option (ga.api.extension.command) = {
            id: 96034;
        };
    };
    // 提交拍卖设置
    rpc OfferRoomSubmitOfferingSetting (ga.offer_room.OfferRoomSubmitOfferingSettingRequest) returns (ga.offer_room.OfferRoomSubmitOfferingSettingResponse) {
        option (ga.api.extension.command) = {
            id: 96035;
        };
    };
    // 出价
    rpc OfferRoomNamePriceOnce (ga.offer_room.OfferRoomNamePriceOnceRequest) returns (ga.offer_room.OfferRoomNamePriceOnceResponse) {
        option (ga.api.extension.command) = {
            id: 96036;
        };
    };
    // 一键定拍
    rpc OfferRoomNamePriceMax (ga.offer_room.OfferRoomNamePriceMaxRequest) returns (ga.offer_room.OfferRoomNamePriceMaxResponse) {
        option (ga.api.extension.command) = {
            id: 96037;
        };
    };
    // 定拍
    rpc OfferRoomOfferingSet (ga.offer_room.OfferRoomOfferingSetRequest) returns (ga.offer_room.OfferRoomOfferingSetResponse) {
        option (ga.api.extension.command) = {
            id: 96038;
        };
    };
    // 流拍
    rpc OfferRoomOfferingPass (ga.offer_room.OfferRoomOfferingPassRequest) returns (ga.offer_room.OfferRoomOfferingPassResponse) {
        option (ga.api.extension.command) = {
            id: 96039;
        };
    };
    // 结束
    rpc OfferRoomOfferingEnd (ga.offer_room.OfferRoomOfferingEndRequest) returns (ga.offer_room.OfferRoomOfferingEndResponse) {
        option (ga.api.extension.command) = {
            id: 96040;
        };
    };
    // 获取关系列表
    rpc OfferRoomOfferingRelationships (ga.offer_room.OfferRoomOfferingRelationshipsRequest) returns (ga.offer_room.OfferRoomOfferingRelationshipsResponse) {
        option (ga.api.extension.command) = {
            id: 96041;
        };
    };
    // 删除关系
    rpc OfferRoomDeleteRelationship (ga.offer_room.OfferRoomDeleteRelationshipRequest) returns (ga.offer_room.OfferRoomDeleteRelationshipResponse) {
        option (ga.api.extension.command) = {
            id: 96042;
        };
    };
    // 初始化游戏
    rpc OfferRoomOfferingInit (ga.offer_room.OfferRoomOfferingInitRequest) returns (ga.offer_room.OfferRoomOfferingInitResponse) {
        option (ga.api.extension.command) = {
            id: 96043;
        };
    };
    // 资料卡关系信息
    rpc OfferRoomCardInfo (ga.offer_room.OfferRoomCardInfoRequest) returns (ga.offer_room.OfferRoomCardInfoResponse) {
        option (ga.api.extension.command) = {
            id: 96044;
        };
    };
    // 获取拍卖铭牌资源配置
    rpc OfferRoomNameplateConfig (ga.offer_room.OfferRoomNameplateConfigReq) returns (ga.offer_room.OfferRoomNameplateConfigResp) {
        option (ga.api.extension.command) = {
            id: 96045;
        };
    };

    // 获取用户铭牌信息
    rpc OfferRoomGetUserNameplateInfo (ga.offer_room.OfferRoomGetUserNameplateInfoReq) returns (ga.offer_room.OfferRoomGetUserNameplateInfoResp) {
        option (ga.api.extension.command) = {
            id: 96046;
        };
    };
}
