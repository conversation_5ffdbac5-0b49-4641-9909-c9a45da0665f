// Code generated by protoc-gen-ga-api. DO NOT EDIT.
// 	protoc        v3.9.1

syntax = "proto3";
package ga.api.ugc_friendship;



import "ugc/ugc_.proto";
import "api/extension/extension.proto";

option java_package      = "com.quwan.tt.proto.api";
option objc_class_prefix = "RPC";
option go_package = "golang.52tt.com/protocol/app/api/ugc_friendship;ugc_friendship";

service UgcFriendshipLogic {
    option (ga.api.extension.logic_service_name) = "ugc-friendship-logic";
    option (ga.api.extension.logic_service_language) = "go";
    option (ga.api.extension.logic_service_uri_rewrite) = "/logic.UgcFriendshipLogic/";
    rpc GetUserFriendships(ga.ugc.GetUserFriendshipsReq) returns (ga.ugc.GetUserFriendshipsResp) {
        option (ga.api.extension.command) = {
             id: 2561
        };
    }
    rpc FollowUser(ga.ugc.FriendshipOperationReq) returns (ga.ugc.FriendshipOperationResp) {
        option (ga.api.extension.command) = {
             id: 2562
        };
    }
    rpc UnfollowUser(ga.ugc.FriendshipOperationReq) returns (ga.ugc.FriendshipOperationResp) {
        option (ga.api.extension.command) = {
             id: 2563
        };
    }
    rpc RemoveFollower(ga.ugc.FriendshipOperationReq) returns (ga.ugc.FriendshipOperationResp) {
        option (ga.api.extension.command) = {
             id: 2564
        };
    }
    rpc QuickCheckFriendship(ga.ugc.QuickCheckFriendshipReq) returns (ga.ugc.QuickCheckFriendshipResp) {
        option (ga.api.extension.command) = {
             id: 2565
        };
    }
    rpc FollowBatchUser(ga.ugc.FollowBatchUserReq) returns (ga.ugc.FollowBatchUserResp) {
        option (ga.api.extension.command) = {
             id: 2566
        };
    }
    rpc GetShowUserFollow(ga.ugc.GetShowUserFollowReq) returns (ga.ugc.GetShowUserFollowResp) {
        option (ga.api.extension.command) = {
             id: 2568
        };
    }
    rpc SetShowUserFollow(ga.ugc.SetShowUserFollowReq) returns (ga.ugc.SetShowUserFollowResp) {
        option (ga.api.extension.command) = {
             id: 2569
        };
    }

    rpc BatchUnFollowUser(ga.ugc.BatchUnFollowUserRequest) returns (ga.ugc.BatchUnFollowUserResponse) {
        option (ga.api.extension.command) = {
            id: 2576
        };
    }
}

