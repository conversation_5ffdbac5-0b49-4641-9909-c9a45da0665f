syntax = "proto3";

package rcmd.rcmd_invite_user;

option go_package = "golang.52tt.com/protocol/services/rcmd/rcmd_invite_user";


//邀请陌生人进房
service RcmdInviteUser {
  // 邀请陌生人接口
  rpc GetInviteStrangerUser (GetInviteStrangerUserReq) returns (GetInviteStrangerUserResp);
}


message GetInviteStrangerUserReq{
  uint32 uid = 1;
  uint32 tab_id = 2;
  enum InviteSource {
    Default = 0;
    Entrance = 1; // 邀请入口
    List = 2; // 邀请列表
    ShowEntrance = 3; // 邀请入口展示
  }
  InviteSource invite_source = 3;
  enum ReqType {
    Invalid = 0;
    NextPage = 1; // 请求下一页
    Refresh = 2; // 刷新
  }
  ReqType req_type = 4;
  uint32 limit = 5;
  repeated string labels = 6;// 筛选标签
  repeated uint32 force_uid = 7; // 强推uid,邀请入口点击用户头像需带上
  repeated uint32 no_browse_list = 8; //上次请求未曝光id列表
}


message UserDetail{
  int64 last_play_ts = 1; // 上次一起玩时间戳
  uint32 last_play_tab_id = 2; // 上次一起玩tabid
  string invite_rate_text = 3; // 邀请进房成功率文案，如“邀请成功率：高”，空字符串则不展示
}


message UserInfo{
  uint32 uid = 1;
  uint32 show_tab_id = 2;// 卡片展示的玩法
  enum TitleShowType {
    Default = 0;
    Channel = 1; // 房间发布标签
    PalCard = 2; // 搭子卡标签
    SelectLabel = 3; // 房间垂直列表筛选标签
    GameCard = 4; // 游戏卡标签
  }
  TitleShowType title_show_type = 3; // 卡片需展示的标题标签
  UserDetail detail = 4; // 详细用户信息
}

message GetInviteStrangerUserResp{
  bool is_show_inviter_entrance = 1; // 是否为高危用户，是则不返回用户列表
  repeated UserInfo stranger_list = 2;
  bool bottom_reached = 3; // 列表是否到底
}