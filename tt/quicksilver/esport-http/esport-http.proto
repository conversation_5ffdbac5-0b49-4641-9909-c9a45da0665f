syntax = "proto3";

option go_package = "esport-http";

package esport_http;

import "tt/quicksilver/esport-hall/esport-hall.proto";

// 前缀：tt-revenue-http-logic/esport

// 游戏类型
// buf:lint:ignore ENUM_PASCAL_CASE
enum GAME_TYPE {
    GAME_TYPE_INVALID = 0;
    GAME_TYPE_MOBILE = 1; // 手游
    GAME_TYPE_PC = 2; // 端游
}

enum SelectType {
    SELECT_TYPE_UNSPECIFIED = 0;
    SELECT_TYPE_SINGLE = 1; // 单选
    SELECT_TYPE_MULTI = 2; // 多选
}

//段位等信息
message SectionInfo {
    string section_name = 1;        // 资料名称（段位信息、擅长位置、擅长英雄...）
    repeated string item_list = 2;  // 填写项
    uint32 section_id = 3;          // section_id
}

//用户游戏资料信息
message UserSkillInfo {
    uint32 game_id = 1;
    string game_name = 2;                              // 游戏名称
    string skill_evidence = 3;                         // 技能图
    string skill_desc = 4;                              // 技能图说明
    string audio = 5;                                   // 语音介绍
    uint32 audio_duration = 6;                          // 语音介绍时长(秒数)
    repeated SectionInfo section_list = 7;             // 技能详细信息
    string text_desc = 8;
    uint32 game_rank = 9;                                //游戏排名
    bool is_guarantee_win = 10; // 大神包赢开关
}

// uri: /add_user_skill
//前端接口：新技能审核信息请求
message AddUserSkillRequest {
    UserSkillInfo skill = 1;           //技能信息
}

//增加新技能响应
message AddUserSkillResponse {
}


// uri: /get_user_current_skill
//前端接口：获取用户当前技能信息
message GetUserCurrentSkillRequest {
    uint32 target_uid = 1;
}

message GetUserCurrentSkillResponse {
    uint32 target_uid = 1;
    repeated UserSkillInfo skill = 2;  //技能信息
    uint32 audit_type = 3;             //提交技能修改，显示审核状态 see esport-role.proto EsportAuditType
}

// uri: /get_user_skill_by_game_id
//前端接口：获取用户当前技能信息
message GetUserSkillByGameIdRequest {
    uint32 target_uid = 1;
    uint32 game_id = 2;
}

message GetUserSkillByGameIdResponse {
    uint32 target_uid = 1;
    uint32 game_id = 2;
    repeated UserSkillInfo audit_skill = 3;    //审核中的技能信息
    UserSkillInfo current_skill = 4;           //已经获得的技能信息
    uint32 audit_type = 5;                    //提交技能修改，显示审核状态 see esport-role.proto EsportAuditType
    bool has_guarantee_win_permission = 6;    //是否有开启包赢的权限
}


// uri: /modify_user_skill
//前端接口： 修改用户技能
message ModifyUserSkillRequest {
    UserSkillInfo skill = 1;
}

message ModifyUserSkillResponse {
}

// uri: /del_user_skill
//前端接口：删除技能
message DelUserSkillRequest {
    uint32 game_id = 1;
}

message DelUserSkillResponse {
}


// uri: /get_esport_master_info
//前端接口：获取用户大神页信息
message GetESportMasterInfoRequest {
    uint32 target_uid = 1;
}

message GetESportMasterInfoResponse {
    uint32 target_uid = 1;
    string account = 2;
    string nick_name = 3;
    string ttid = 4;
    uint32 esport_role = 5;                      // see ESportErType
    uint32 guild_id = 6;                         // 如果是工会签约指导，返回对应的工会id
    string guild_name = 7;
    uint32 divide = 8;                         //分成比例
    repeated TradeStat stat_list = 9;         //交易数据统计
    repeated GodPageSkill skill_list = 10;    //大神页技能列表
    string head_dy_md5 = 11;                  //动态头像md5
    CoachLabel label = 12; // 电竞标识
    repeated string coach_label_list = 13; // 大神标识
    string guarantee_desc = 14; // 大神侧大神包赢说明文案
    bool show_grab_order_center = 15; // 是否显示抢单中心
}



// 电竞指导身份类型
enum ESportErType {
    ESPORT_TYPE_UNSPECIFIED = 0; // 未知，非电竞指导身份
    ESPORT_TYPE_PERSONAL = 1;    // 个人指导
    ESPORT_TYPE_GUILD = 2;       // 工会签约指导
}


message TradeData {
    uint32 income = 1;         //订单收入
    uint32 service_user = 2;   //服务人数
    uint32 retrade_user = 3;   //复购人数
    uint32 fresh_user = 4;     //新客人数
}


// 电竞指导身份类型
enum ESportStatType {
    ESPORT_STAT_TYPE_UNSPECIFIED = 0;   // 未知
    ESPORT_STAT_TYPE_WEEKLY = 1;        // 本周
    ESPORT_STAT_TYPE_MONTHLY = 2;       // 本月
    ESPORT_STAT_TYPE_ALL = 3;           // 历史数据
}

message TradeStat {
    uint32 stat_type = 1;  //see ESportStatType
    TradeData trade = 2;
}

// 资源类型
enum LabelSourceType {
    LABEL_SOURCE_TYPE_UNSPECIFIED = 0;
    LABEL_SOURCE_TYPE_PNG = 1; // PNG
    LABEL_SOURCE_TYPE_WEBP = 2; // WEBP
}

message CoachLabel {
    LabelSourceType type = 1; // 资源类型
    string source_url = 2; //资源url
}



// 搜索类型
enum AuditSearchType {
    AUDIT_SEARCH_TYPE_UNSPECIFIED = 0;     // 未知
    AUDIT_SEARCH_TYPE_BY_USER = 1;    // 个人记录
    AUDIT_SEARCH_TYPE_BY_GUILD = 2;   // 公会记录
    AUDIT_SEARCH_TYPE_ALL = 3;        // 全部
}

// uri: /batch_get_audit_skill
//公会后台 获取新增技能审核信息请求
message BatchGetAuditSkillRequest {
    uint32 guild_id = 1;
    repeated uint32 uids = 2;
    repeated uint32 audit_type = 3;     // 审核结果 see esport-role.proto EsportAuditType
    uint32 off_set = 4;
    uint32 limit = 5;
    uint32 need_total = 6;             //是否需要总数量 1 需要
    uint32 search_type = 7;            //搜索类型
    repeated uint32 audit_source = 8;  //技能审核来源 see AuditSource
}


message AuditSkillRecord {
    uint32 uid = 1;
    string account = 2;
    string nick_name = 3;
    string ttid = 4;
    string audit_token = 5;            //审核唯一记录
    uint32 audit_type = 6;             //审核结果 see esport-role.proto EsportAuditType
    uint32 apply_time = 7;             //申请时间
    repeated UserSkillInfo skill = 8;  //技能信息
    uint32 audit_source = 9;          //技能审核来源 see AuditSource
    string reason = 10;
    uint32 guild_id = 11;
    string sign_duration = 12;        //签约时长
    string operator = 13;             //审核人员
    uint32 update_time = 14;          //最后更新时间
}

//获取新增技能审核信息请求
message BatchGetAuditSkillResponse {
    uint32 guild_id = 1;
    repeated uint32 uids = 2;
    repeated uint32 audit_type = 3;     // 审核结果 see esport-role.proto EsportAuditType
    uint32 off_set = 4;
    uint32 limit = 5;
    repeated AuditSkillRecord list = 6; //审核记录
    uint32 total_count = 7;
    uint32 search_type = 8;              //搜索类型
    repeated uint32 audit_source = 9;    //技能审核来源 see AuditSource
}

// uri: /set_user_skill_audit_type
// 公会后台:设置用户技能审核结果
message SetUserSkillAuditTypeRequest {
    uint32 uid = 1;
    string audit_token = 2;
    uint32 audit_type = 3;        //审核结果 see esport-role.proto EsportAuditType
    string reason = 4;            //原因
}

message SetUserSkillAuditTypeResponse {
}




// ============================== 申请电竞指导身份 ==============================

message ESportGameSimpleInfo {
    uint32 game_id = 1;
    string name = 2; // 游戏名称
    string icon = 3; // 游戏图标
    uint32 game_type = 4; // 游戏类型
    uint32 rank = 5; // 游戏展示排名
    uint32 audit_type = 6; //审核状态
    FreezeType freeze_type = 7; // 冻结状态
    int64 freeze_stop_ts = 8; // 冻结结束时间
}

message ESportGameTypeInfo {
    uint32 game_type = 1;  // see esport_logic.proto EsportGameType 1-手游，2-端游
    string name = 2;  // 游戏类型名称
    uint32 rank = 3;  // 展示顺序，升序
}

// url: /get_game_list
// 根据当前用户身份，如果已是陪陪，则返回用户已有技能游戏列表；如果是普通用户身份，则返回游戏列表
message GetESportGameListRequest {
    // uint32 uid = 1;
    uint32 source = 1;    // 申请来源 1-申请个人指导身份； 2-申请签约工会
}

message GetESportGameListResponse {
    uint32 user_cur_role = 1;  // 当前用户身份ESportErType
    repeated ESportGameSimpleInfo game_list = 2;
    repeated ESportGameTypeInfo game_type_list = 3; // 游戏类型排序

    // 当前审核状态
    uint32 apply_status = 4;  // 1-工会审核中，2-平台审核中
    uint32 apply_type = 5;    // see ESportApplyType
}

// url: /apply/apply_risk_check
// 申请风控检查，可能命中人脸风控检查，前端需要根据对应的错误码做相应处理
message ApplyRiskCheckRequest {
    string device_id = 1;
    string ip = 2;         // 能拿到就传
    uint32 client_type = 3;
    uint32 app_version = 4;
    string face_auth_result_token = 5; // 人脸
    uint32 source = 6;      // 申请来源 1-申请个人指导身份； 2-申请签约工会

    uint32 guild_id = 7;    // 申请签约的工会id
}

message ApplyRiskCheckResponse {
    uint32 user_cur_role = 1;  // 当前用户身份ESportErType
   // 当前审核状态
    uint32 apply_status = 2;  // 1-工会审核中，2-平台审核中
    uint32 apply_type = 3;    // see ESportApplyType

    // 人脸context
    string face_auth_context_json = 4;

    uint32 guild_id = 5;    // 当前审核中的工会id
}

message GetEsportGameListWithAuditRequest {

}

message GetEsportGameListWithAuditResponse {
    repeated ESportGameSimpleInfo game_list = 1;
    repeated ESportGameTypeInfo game_type_list = 2; // 游戏类型排序
}


message SelfIntroConf {
    uint32 text_max_cnt = 1;  // 文字介绍最大限制
    uint32 audio_max_sec = 2; // 语音最大时长；
    bool audio_req = 3;       // 语音介绍是否必填，true时为必填
}

// 游戏资料
message GameInformation {
    // 游戏资料类型
    // buf:lint:ignore ENUM_PASCAL_CASE
    enum GAME_INFORMATION_TYPE {
        GAME_INFORMATION_TYPE_INVALID = 0;
        GAME_INFORMATION_TYPE_RANK = 1; // 段位信息
        GAME_INFORMATION_TYPE_DIY = 2; // 自定义
    }
    // buf:lint:ignore ENUM_PASCAL_CASE
    enum GAME_INFORMATION_SELECT_TYPE {
        GAME_INFORMATION_SELECT_TYPE_INVALID = 0;
        GAME_INFORMATION_SELECT_TYPE_SINGLE = 1; // 单选
        GAME_INFORMATION_SELECT_TYPE_MULTI = 2; // 多选
    }

    uint32 information_type = 1;    // SEE GAME_INFORMATION_TYPE
    string section_name = 2;        // 资料名称（段位信息、擅长位置、擅长英雄...）
    repeated string item_list = 3;  // 选择项（数量需要大于等于2）
    uint32 select_type = 4;         // 单选还是多选,SEE GAME_INFORMATION_SELECT_TYPE
    uint32 section_id = 5;          // 信息id
}

message UserGameInfo {
    string skill_url = 1; // 用户技能图
    string intro_text = 2; // 文字介绍
    string intro_audio = 3; // 语音介绍

    repeated GameInformation info_list = 4; // 更多游戏信息，段位信息、擅长位置、擅长英雄等
}

message GameSkillConf {
    uint32 game_id = 1;
    string name = 2;      // 游戏名称
    uint32 game_type = 3; // 游戏类型,see esport-skill.proto GAME_TYPE
    string game_icon = 4; // 游戏图标
    string game_background = 5; // 游戏背景图
    string game_color = 6; // 游戏背景颜色
    string skill_evidence = 7; // 技能示意图
    string skill_desc = 8;     // 技能图说明
    repeated GameInformation game_info_list = 9; // 更多游戏信息，比如擅长位置、擅长英雄等，如果有段位信息，根据ui要求展示
}

// url: /apply/get_skill_conf
// 获取指定游戏技能配置
message GetESportGameSkillConfRequest {
    uint32 game_id = 1;
}

message GetESportGameSkillConfResponse {
    uint32 game_id = 1;
    GameSkillConf skill_conf = 2; // 游戏技能信息配置
    SelfIntroConf intro_conf = 3; // 个人介绍配置
    string guarantee_desc = 4; // 包赢文案
}


enum ESportApplyType {
    ESPORT_APPLY_TYPE_INVALID = 0;
    ESPORT_APPLY_TYPE_PERSONAL = 1;             // 个人指导
    ESPORT_APPLY_TYPE_GUILD = 2;                // 工会签约指导
    ESPORT_APPLY_TYPE_PERSONAL_TO_GUILD = 3;    // 个人身份签约工会
}

// url: /apply/apply_role
// 提交审核申请
message ApplyESportRoleReq {
    uint32 apply_type = 1; // 申请身份类型 ESportApplyType

    // 签约工会需要填写相关信息
    uint32 guild_id = 2;
    uint32 sign_duration = 3;     // 签约时长（单位：月）

    UserSkillInfo user_skill = 4; // 用户技能信息，个人身份签约工会申请时不需要填写

    // 开关判断需要
    string device_id = 5;
    uint32 client_type = 6;
    uint32 app_version = 7;
}

message ApplyESportRoleResp {
}

// 退款类型枚举
enum RefundType {
    REFUND_TYPE_FULL = 0; // 全额退款
    REFUND_TYPE_PARTIAL = 1; // 部分退款
  // 未来可以在此添加更多的退款类型
}

// 用户创建退款请求的消息
message CreateRefundRequest {
    string order_id = 1; // 订单ID
    uint32 quantity = 2; // 退款数量
    string reason = 3; // 退款原因
    string description = 4; // 详细的退款说明
    RefundType type = 5; // 退款类型
    string device_id = 6;// 设备id，用于内审接口
}

// 电竞指导者拒绝退款请求的消息
message RejectRefundRequest {
    string refund_request_id = 1; // 退款请求ID
    string reason = 2; // 拒绝原因
    string description = 3; // 详细的拒绝说明
    string device_id = 4;// 设备id，用于内审接口
}

// 退款操作的通用响应消息
message RefundResponse {}

// 获取退款原因的请求消息
message GetRefundReasonsRequest {
}

// 获取退款原因的响应消息
message GetRefundReasonsResponse {
    // 带急速退款的退款原因
    message ReasonWithFastRefund {
        string reason = 1; // 退款原因
        bool could_fast_refund = 2; // 是否支持急速退款
    }
    repeated string reasons = 1; // 退款原因列表
    repeated ReasonWithFastRefund reasons_with_fast_refund = 2; // 带急速退款的退款原因列表
}

// 获取拒绝退款的原因请求消息
message GetRejectReasonsRequest {
}

// 获取拒绝退款的原因响应消息
message GetRejectReasonsResponse {
    repeated string reasons = 1; // 拒绝退款的原因列表
}

// 用户发起退款申诉请求
message InitiateAppealRequest {
    string refund_id = 1; // 订单退款申请的ID
    string description = 2; // 申诉原因
    repeated string proof_images = 3; // 申诉凭证图片
    string device_id = 6;// 设备id，用于内审接口
}

// 用户发起退款申诉响应
message InitiateAppealResponse {
    string appeal_id = 1; // 申诉ID
}

// 电竞指导提交自己的申诉信息请求
message SubmitGuideAppealInfoRequest {
    string appeal_id = 1; // 申诉ID
    string coach_appeal_description = 2; // 电竞指导的申诉描述
    repeated string coach_appeal_images = 3; // 电竞指导的申诉图片
    string device_id = 6;// 设备id，用于内审接口
}

// 电竞指导提交自己的申诉信息响应
message SubmitGuideAppealInfoResponse {
}

// =============================== 接单时间, 价格修改 ===============================
// 获取接单时间
message GetReceiveTimeFrameRequest {}

message GetReceiveTimeFrameResponse {
    uint32 start_time = 2; // 开始时间
    uint32 end_time = 3; // 结束时间
    repeated bool day_of_week = 4; // 数组表示周一到周日是否开放
}

// 设置接单时间
message SetReceiveTimeFrameRequest {
    uint32 start_time = 2; // 开始时间
    uint32 end_time = 3; // 结束时间K
    repeated bool day_of_week = 4; // 数组表示周一到周日是否开放
}

message SetReceiveTimeFrameResponse {
}

// 开启/关闭接单
message SetSkillReceiveSwitchRequest {
    uint32 skill_id = 1; // 技能id
    bool switch = 2; // 是否接单
}

message SetSkillReceiveSwitchResponse {}



// 设置技能价格
message SetSkillPriceRequest {
    uint32 skill_id = 1; // 技能id
    uint32 price_val = 2; // 价格
}

message SetSkillPriceResponse {
}

message GodPageSkill {
    // 包赢信息
    message GuaranteeInfo {
        repeated string guarantee_rank = 1; // 包赢段位
        repeated uint32 current_guarantee_index_list = 2; // 当前选中的包赢段位索引
        uint32 section_id = 3; // 配置项id
        SelectType select_type = 4;     // 单选还是多选
    }
    uint32 id = 1;
    string name = 2;  // 技能名称
    string icon = 3;  // 技能图标
    PriceInfo price = 4; // 价格
    bool receive_switch = 5; // 接单开关
    uint32 game_id = 6; // 游戏id
    GuaranteeInfo guarantee_info = 7; // 包赢信息
    FreezeType freeze_type = 8; // 冻结状态
    int64 freeze_stop_ts = 9; // 冻结结束时间
}

enum FreezeType {
    FREEZE_TYPE_UNFREEZE = 0; // 未冻结
    FREEZE_TYPE_FOREVER = 1; // 永久冻结
    FREEZE_TYPE_TO_TIME = 2; // 冻结至指定时间
}

message PriceInfo {
    uint32 price = 1; // 价格        单价格式: price+price_unit/measure_cnt+measure_unit
    string price_unit = 2; // 价格单位
    uint32 min_price = 3; // 最低价格
    uint32 base_price = 4; // 基础价格
    uint32 max_price = 5; // 最高价格
}

// 电竞专区大神详情
message GetCoachDetailRequest {
    uint32 coach_uid = 1; // 教练uid
    uint32 game_id = 2; // 游戏id
}

message GetCoachDetailResponse {
    CoachDetail coach_detail = 1; // 教练信息
}

message CoachDetail {
    UserInfo user_info = 1; // 电竞指导个人信息
    string text_desc = 2; // 文字描述
    string voice_desc = 3; // 语音描述
    string game_name = 4; // 游戏名称
    string tag = 5; // 标签 eg:段位
    float score = 6; // 评分
    uint32 service_cnt = 7; // 服务次数
    repeated GameProperty game_property_list = 8; // 游戏属性
    repeated string game_img = 9; // 游戏图片
    CoachLabel coach_label = 10; // 电竞指导标识
    uint32 online_type = 11; // 在线类型 1在线, 2离线
    uint32 video_duration = 12; // 语音时长
    uint32 price = 13; // 价格
    string price_unit = 14; // 价格单位
    string game_bg = 15; // 游戏背景
    string game_bg_color = 16; // 游戏背景颜色
    bool is_follow = 17; // 是否关注
    bool is_famous_player = 18; // 是否是知名选手
    repeated string coach_label_list = 19; // 大神标识
    repeated string skill_label_list = 20; // 技能标识
    string grantee_win_text = 21; // 包赢文案
    repeated string skill_label_url_list = 22; // 技能类型的标识图标的 url
    bool has_first_round_discount = 23; // 是否有首局优惠
    uint32 first_round_price = 24; // 首局优惠价格
    bool is_customer_hosting = 25; // 是否有客服托管
    bool has_discount = 26; // 是否有优惠
    uint32 discount_price = 27; // 优惠价，有可能优惠后为0
    uint32 discount_type = 28; // 优惠类型，参考DiscountType
    string discount_desc = 29; // 优惠描述，如：券后价/首局价
    string order_button_hint_text = 30; // 立即下单 按钮 提示文案
    CoachChannelInfo coach_channel_info = 31; // 房间信息
}

message CoachChannelInfo {
    uint32 channel_id = 1; // 房间id
    uint32 channel_type = 2; // 房间类型
    bool   has_pwd = 3; // 是否有密码
    uint32 creator_uid = 4; // 创建者uid
}

// 游戏属性
message GameProperty {
    enum PropertyType {
        PROPERTY_TYPE_UNSPECIFIED = 0;
        PROPERTY_TYPE_GENDER = 1;  // 性别
        PROPERTY_TYPE_PRICE = 2;    // 价格
        PROPERTY_TYPE_CUSTOM = 3;   // 自定义
    }

    enum SelectType {
        SELECT_TYPE_UNSPECIFIED = 0;
        SELECT_TYPE_SINGLE = 1; // 单选
        SELECT_TYPE_MULTI = 2; // 多选
    }

    uint32 id = 1; // 属性id
    string name = 2; // 属性名称
    repeated GamePropertyVal val_list = 3; // 属性值列表
    uint32 property_type = 4; // 属性类型
    uint32 select_type = 5; // 选择类型
    bool expose = 6; // 是否外显
}

// 游戏属性值
message GamePropertyVal {
    uint32 id = 1; // 属性值id
    string name = 2; // 属性值名称
}


// 获取订单详情
message GetOrderDetailRequest {
    string order_id = 1;
    //int64 pay_time = 3;       // 下单时间
}

message GetOrderDetailResponse {
    SkillProductOrderDetail order = 1;    // 订单信息
    OrderRefund refund = 2;         // 退款信息
}

// 商品订单详情
message SkillProductOrderDetail {
    //    UserProfile player = 1;   // 玩家个人信息
    ProductOrder product_order = 2; // 商品订单信息
    string order_id = 3;      // 订单号
    uint32 status = 4;        // 订单状态 see OrderStatus
    uint32 sub_status = 5;    // 子状态 例如 CanceledOrderSubStatus
    int64 pay_time = 6;       // 下单时间
    int64 receive_time = 7;   // 接单时间
    int64 finish_time = 8;    // 完成时间
    int64 cancel_time = 9;    // 取消时间
    int64 order_end_time = 10;// 订单过期时间
    string status_desc = 11;  // 状态描述， 若是拒绝接单，则为原因
    string order_number = 12;  // 订单编号
    string order_remark = 13; // 订单备注
    bool is_notify_finish = 14; // 是否已提醒去完成
    uint32 coach_uid = 15; // 大神uid
}

message ProductOrderPriceInfo {
    uint32 price = 1; // 价格        单价格式: price+price_unit/measure_cnt+measure_unit
    string price_unit = 2; // 价格单位
    uint32 measure_cnt = 3; // 计量数目
    string measure_unit = 4; // 计量单位
    bool has_first_round_discount = 6; // 是否有首局优惠
    uint32 first_round_price = 7; // 首局优惠价格
    bool has_discount = 8; // 是否有优惠
    uint32 discount_price = 9; // 优惠价，有可能优惠后为0
    uint32 discount_type = 10; // 优惠类型，参考DiscountType
    string discount_desc = 11; // 优惠描述，如：券后价/首局价
}

// 优惠类型
enum DiscountType {
    DISCOUNT_TYPE_UNSPECIFIED = 0;
    DISCOUNT_TYPE_FIRST_ROUND = 1; // 有首局优惠
    DISCOUNT_TYPE_COUPON = 2; // 有优惠券
    DISCOUNT_TYPE_NEW_CUSTOMER = 3; // 有新客价
}

// 商品订单
message ProductOrder {
    UserProfile coach = 1;    // 电竞指导个人信息
    uint64 product_id = 2;    // 商品id
    string name = 3;    // 技能名称
    string icon = 4;    // 技能图标
    ProductOrderPriceInfo price_info = 5; // 价格信息
    uint32 count = 6;         // 商品数量
    uint32 total_price = 7;   // 总价，是用户实际支付的金额
    string guarantee_win_text = 8; // 包赢文案
    uint32 game_id = 9;       // 游戏id
    bool can_call_customer = 10;// 能否联系客服
    uint32 coach_total_price = 11; // 大神侧看到的订单总价
    CouponUseDetail coupon_use_detail = 12; // 优惠券使用详情
    NewCustomerUseDetail new_customer_use_detail = 13; // 新客优惠使用详情
}

// 优惠券使用详情
message CouponUseDetail {
    bool use_coupon = 1; // 订单是否使用了优惠券
    uint32 coupon_money = 2; // 优惠券抵扣的金额，单位是T豆
}

// 新客优惠使用详情
message NewCustomerUseDetail {
    bool use_new_customer_discount = 1; // 是否使用新客优惠
    uint32 new_customer_price = 2; // 新客价，单位豆
    uint32 plat_bonus_fee = 3; // 平台补贴金额，单位豆
}

// 退款信息
message OrderRefund {
    string refund_id = 1;      // 退款id
    string appeal_id = 2;// 申诉id
//    uint32 status = 2;         // 状态 包含退款中申诉中等等 RefundStatus
//    RefundType refund_type = 3;    // 退款类型
//    string refund_reason = 4;         // 退款原因
//    uint32 refund_price = 5;   // 退款金额
//    uint32 refund_count = 6;   // 退款数量
//    string refund_desc = 7;           // 退款说明
//    int64 end_time = 8;        // 该状态的截止时间
//    bool can_appeal = 9;       // 能否申诉
//    string appeal_desc = 10;// 申诉说明
//    string refund_reject_reason = 11;// 退款拒绝原因
//    string refund_reject_desc = 12;// 退款拒绝说明
//    string appeal_reason = 13;// 申诉原因
//    bool can_coach_upload_appeal = 14;// 能否上传申诉凭证
}

// =================== 临时接口 ==========
message ManualAddESportCoachRoleRequest {
}

message ManualAddESportCoachRoleResponse {
}


message GetCoachStatisticsRequest {
    uint32 uid = 1;
    uint32 game_id = 2; // 按game_id统计
}

message GetCoachStatisticsResponse {
    uint32 order_num = 1; // 订单总量
    uint32 customer_num = 2; // 服务用户总数
}


/*********************** 评价 ***********************/

message EvaluateWordCnt {
    string word = 1;
    uint32 cnt = 2;
}

message EvaluateScore {
    float avg_score = 1;      // 平均分
    float service_score = 2;  // 服务水平
    float skill_score = 3;    // 技术水平
    float voice_score = 4;    // 声音质感
}

// 评价汇总
message EvaluateSummary {
    uint32 total_cnt = 1; // 评价总条数
    EvaluateScore score_info = 2;        // 评分
    repeated EvaluateWordCnt word_cnt_list = 3; // 评价快捷词统计
}

message UserInfo {
    uint32 uid = 1;
    string nickname = 2;
    string account = 3;
    uint32 sex = 4; // 0女 1男
    string head_dy_md5 = 5;                  //动态头像md5
    string remark = 6; // 好友备注
}

// 订单评价信息
message EvaluateInfo {
    bool is_anonymous = 1; // 是否匿名
    int64 evaluate_time = 2;  // 评价时间戳（秒）
    repeated string word_list = 3; // 评价快捷词
    string content = 4; // 评价内容
    EvaluateScore score_info = 5;        // 评分
    string order_id = 6; // 订单id
    UserInfo player = 7; // 玩家(即评论人)信息
    bool is_query_user = 8; // 是否是查询人评价的
}

// 分页获取用户评价列表
message GetEvaluateListRequest {
    uint32 uid = 1;
    string word = 2;
    uint32 offset = 3;
    uint32 limit = 4;
    uint32 game_id = 5;
}

message GetEvaluateListResponse {
    EvaluateSummary summary = 1;  // 评价汇总， 仅当 offset为0且word为“” 时有效
    repeated EvaluateInfo list = 2;
}

// 检查是否可以举报该电竞陪玩
message CheckCanReportCoachRequest {
    uint32 target_uid = 1;
    string target_account = 2;
    string version = 3;
    string os_type = 4;
    string platform = 5;
    string market_id = 6;
    string ip = 7;
    string device_id = 8;
    string app = 9;
}

message CheckCanReportCoachResponse {
    bool can_report = 1;
}

// 设置秒接单开关
message SetQuickReceiveSwitchRequest {
    bool switch = 1; // 是否开启秒接单
}

message SetQuickReceiveSwitchResponse {}

// 获取秒接单开关状态
message GetQuickReceiveSwitchRequest {}

message GetQuickReceiveSwitchResponse {
    bool switch = 1; // 是否开启秒接单
}

// LabelType 标识类型
enum LabelType {
    LABEL_TYPE_UNSPECIFIED = 0; // 非法
    LABEL_TYPE_COACH = 1; // 大神标识
    LABEL_TYPE_SKILL = 2; // 技能标识
}

// 获取当前用户的定价等级信息
// uri: /get_cur_pricing_info
message GetCurPricingInfoRequest {
    uint32 game_id = 1; // 技能 id
}

// 获取当前用户的定价等级信息响应
message GetCurPricingInfoResponse {
    repeated PricingLevel pricing_levels = 1; // 定价等级列表
    string price_unit = 2; // 定价单位
    uint32 user_level = 3; // 用户当前达到的等级（可用的等级）
    uint32 setting_price_level = 4; // 用户设置的价格等级（设置的等级）
    uint32 current_price = 5; // 用户当前价格
    bool is_renowned_player = 6; // 是否是知名选手
}

// 设置定价等级
// uri: /set_pricing_level
message SetPricingLevelRequest {
    uint32 level = 1; // 选择的定价等级
    uint32 game_id = 2; // 技能 id
}

// 设置定价等级响应
message SetPricingLevelResponse {
}

// 获取可申请的标识列表
// uri: /get_applicable_labels
message GetApplicableLabelsRequest {
    uint32 game_id = 1; // 查询指定游戏的可申请标签列表（填0则查用户所有游戏的可申请标签）
}

// 获取可申请的标识列表响应
message GetApplicableLabelsResponse {
    repeated Label applied_labels = 1; // 已申请的标识列表
}

message PricingLevel {
    int32 level = 1; // 定价等级
    string level_name = 2; // 定价等级名称
    int32 price = 3; // 豆
}

message Label {
    uint32 label_id = 1; // 标识ID
    string label_name = 2; // 标识名称
    string label_icon = 3; // 标识图标
    int32 additional_price = 4; // 加成价格
    string label_description = 5; // 标识简介
    LabelType label_type = 6; // 标识类型
    int32 apply_level = 7; // 申请等级
    bool is_applied = 8; // 是否已申请
    string apply_entry = 9; // 申请入口
    bool is_met_condition = 10;//是否满足条件
    string game_name = 11; // 技能名称
    string requirement = 12;// 标识要求

    bool price_additional_switch = 13; // 是否参与价格加成开关
}

// 设置标识加成价格开关
// uri: /set_label_price_switch
message SetLabelPriceSwitchRequest {
    uint32 label_id = 1; // 标识ID
    bool target_state = 2; // 目标开关状态
}

message SetLabelPriceSwitchResponse {
}


/************************************大神等级begin***********************************************/

//权益
message GodPrivilege {
    uint32 privilege_id = 1;           //权益ID
    string name = 2;           //权益名称
    string desc = 3;           //权益描述
    string url = 4;           //图标url
    bool locked = 5;           //是否上锁
    uint32 unlock_lv = 6;           //解锁等级
    string big_url = 7;           //大图URL
}

//当前指标
message GodLevelKpi {
    string name = 1;             //名称
    string score = 2;             //当前分值
    string upgrade_desc = 3;             //升级要求
    string progress = 4;             //完成进度
    bool finished = 5;             //是否完成
}

//等级
message GodLevel {
    uint32 level = 1;   // 等级
    string name = 2;   // 等级名称
    string url = 3;   // 图片URL
    repeated GodPrivilege privilege_list = 4;  //权益列表
    repeated GodLevelKpi kpi_list = 5;  //指标列表
    string locked_lv_icon = 6;   //锁等级小图标
    string cur_lv_icon = 7;   //"当前等级"小图标
}

//获取用户大神等级
message GetGodLevelRequest {
}
message GetGodLevelResponse {
    uint32 level = 1;
    string icon_url = 2;
    bool no_show = 3;     //true 不展示
}

//获取用户大神等级详情
message GetGodLevelDetailRequest {
}
message GetGodLevelDetailResponse {
    repeated GodLevel all_level_info = 1;              //用有等级配置
    uint32 level = 2;                                //当前等级
    string upgrade_desc = 3;                         //升级描述
    string keep_desc = 4;                         //保级描述
    string upgrade_desc_v2 = 5;                         //升级描述v2
}

/************************************大神等级end***********************************************/

// 检查是否有急速退款的权限
message CheckFastRefundPermissionRequest {
    string order_id = 1;
}
message CheckFastRefundPermissionResponse {
    bool has = 1;
}

// 用户创建退款请求的消息
message CreateFastRefundRequest {
    string order_id = 1; // 订单ID
    uint32 quantity = 2; // 退款数量
    string reason = 3; // 退款原因
    string description = 4; // 详细的退款说明
    RefundType type = 5; // 退款类型
    string device_id = 6;// 设备id，用于内审接口
}

// 设置游戏包赢开关
message SetGameGuaranteeStatusRequest {
    uint32 game_id = 1; // 游戏id
    bool is_guarantee_win = 2; // 是否包赢
}

message SetGameGuaranteeStatusResponse {
}



// ************ 大神侧 谁看过我功能begin  ***********
// uri: /visit/report_visit
// 用户访问上报
message ReportGameCardVisitRequest {
    uint32 coach_id = 1;
    uint32 game_id = 2;
}

message ReportGameCardVisitResponse {
}

// uri: /visit/get_visitor_cnt
// 大神获取被访问记录数
message GetBeVisitorRecordCountReq {
}
message GetBeVisitorRecordCountResp {
    uint32 total_user_count = 1;
}

// VisitRecord
message VisitRecord {
    UserInfo user_info = 1;
    uint32 esport_role = 2; // see ESportErType 用来判断是否是大神
    bool is_playmate = 3;   // 是否是玩伴

    string game_name = 4;   // 最新访问的游戏名称
    int64 update_time = 5;  // 最新访问时间戳(秒级)
    int64 last_place_order_ts = 6;  // 最后一次下单时间戳（秒级）

    uint32 cnt = 7;  // 访问次数
    bool is_online = 8;  // 是否在线
}

// uri: /visit/get_visitor_record
// 大神获取被访问记录
message GetBeVisitorRecordListReq {
    uint32 offset = 1;
    uint32 limit = 2;
}
message GetBeVisitorRecordListResp {
    repeated VisitRecord records = 1;
    uint32 next_offset = 2;
    bool is_end = 3;  // 是否已达到底部
}

// uri:/visit/report_enter_im
// 上报用户进入大神IM私聊页
message ReportEnterIMPageRequest {
    uint32 coach_id = 1;
    uint32 game_id = 2;
}

message ReportEnterIMPageResponse {
}
// ************ 大神侧 谁看过我功能end ************

// 获取电竞助手推荐给用户的活动大神列表
// /tt-revenue-http-logic/esport/hall/getRecommandCoachList
message GetRecommandCoachListRequest {
    uint32 game_id = 1; // 游戏id
    string series_id = 2; // 系列id
    uint32 scene_type = 3; // 场景类型
    repeated esport_hall.GameProperty filter_option = 4; // 筛选条件
    uint32 page_num = 5; // 页码
    uint32 page_size = 6; // 每页数量
}

message GetRecommandCoachListResponse {
    repeated EsportAreaCoachInfo coach_list = 1; // 教练列表
}

message EsportAreaCoachInfo {
    UserProfile user_profile = 1; // 电竞指导个人信息
    string text_desc = 2; // 文字描述
    string guarantee_win_text = 3; // 包赢文案
    bool is_followed = 4; // 是否关注
    ProductOrderPriceInfo price = 5; // 价格
    bool is_famous_player = 6; // 是否是知名选手
    string voice_desc = 7; // 语音描述
    string tone = 8;// 音色
    uint32 strategy_id = 9; // 策略id
    uint32 recall_source_id = 10; // 回溯来源id
    repeated string feature_label_list = 14; // 特色标签
    repeated string feature_label_img_list = 17; // 特色标签
    repeated string skill_label_list = 15; // 技能标签
    string rank = 16; // 段位
}

message UserProfile {
    uint32 uid = 1;
    string account = 2; //账号
    string nickname = 3; // 昵称
    string account_alias = 4; // 靓号，预留字段，暂时未赋值
    uint32 sex = 5; //用户性别

    string head_img_md5 = 6; //用户头像md5，如果有填该值，客户端必须用该值加上account去获取用户头像，避免获取头像回源到服务端
    string head_dy_img_md5 = 7; // 动态头像
}

// ************* 首局低价begin **************

// uri: /get_first_round_discount_info
// 获取首局优惠信息
message GetFirstRoundDiscountInfoRequest {
    uint32 game_id = 1; // 游戏id
}

message GetFirstRoundDiscountInfoResponse {
    bool show_switch = 1; // 是否展示优惠信息，总开关
    uint32 first_round_price = 2; // 首局优惠价格
    bool is_open = 3; // 当前是否开启
}

// uri: /set_first_round_switch
// 设置首局优惠
message SetFirstRoundSwitchRequest {
    uint32 game_id = 1; // 游戏id
    bool is_open = 2; // 是否开启
}

message SetFirstRoundSwitchResponse {
}

// uri: /get_first_round_discount_game_list
// 获取所有首局优惠游戏
message GetFirstRoundDiscountGameListRequest {
}

message FirstRoundDiscountGameInfo {
    uint32 game_id = 1; // 游戏id
    string game_name = 2; // 游戏名称
    uint32 first_round_price = 3; // 首局优惠价格
}

message GetFirstRoundDiscountGameListResponse {
    repeated FirstRoundDiscountGameInfo game_list = 1;
}

// uri: /refresh_discount_info
// 刷新优惠信息
message RefreshDiscountInfoRequest {
    uint32 coach_uid = 1; // 教练uid
    uint32 game_id = 2; // 游戏id
}

message RefreshDiscountInfoResponse {
    bool has_first_round_discount = 1; // 是否有首局优惠
    uint32 first_round_price = 2; // 首局优惠价格
    bool has_discount = 3; // 是否有优惠
    uint32 discount_price = 4; // 优惠价，有可能优惠后为0
    uint32 discount_type = 5; // 优惠类型，参考DiscountType
    string discount_desc = 6; // 优惠描述，如：券后价/首局价
}

// uri: /get_new_customer_discount_info
// 获取新客优惠信息
message GetNewCustomerDiscountInfoRequest {
    uint32 game_id = 1; // 游戏id
}

message GetNewCustomerDiscountInfoResponse {
    bool show_switch = 1; // 是否展示新客价信息，总开关
    uint32 new_customer_price = 2; // 新客价，单位豆
    uint32 plat_bonus_fee = 3; // 平台补贴费用，单位豆
    bool is_open = 4; // 当前是否开启
}

// uri: /set_new_customer_switch
// 设置新客价
message SetNewCustomerSwitchRequest {
    uint32 game_id = 1; // 游戏id
    bool is_open = 2; // 是否开启
}

message SetNewCustomerSwitchResponse {
}

message GetCoachIncentiveTaskInfoRequest {}

message GetCoachIncentiveTaskInfoResponse {
    uint32 sum_score = 1; // 本周积分
    uint32 new_score = 2; // 新增积分
    uint32 new_order_cnt = 3; // 新增
    uint32 first_order_score = 4; // 首单积分
    bool is_quick_receive = 5; // 是否秒接单
    repeated esport_hall.IncentiveTask task_list = 6; // 激励任务列表
}

// 客服托管开关操作类型
enum HostingOperation {
    HOSTING_OPERATION_UNSPECIFIED = 0; // 未指定
    HOSTING_OPERATION_ENABLE = 1; // 开启托管
    HOSTING_OPERATION_DISABLE = 2; // 关闭托管
}

// 请求客服托管开关操作
// /esport/hall/customer_hosting_operation
message HostingOperationRequest {
    HostingOperation operation = 1; // 托管操作类型
}

// 客服托管开关操作响应
message HostingOperationResponse {}


// 获取客服托管状态
// /esport/hall/get_hosting_status
message GetHostingStatusRequest {
}

message GetHostingStatusResponse {
    bool is_open = 1;// 开关状态
    UserProfile customer_info = 2;// 客服信息
}

// 联系客服
// /esport/hall/contact_customer_service
message ContactCustomerServiceRequest {
    uint32 coach_id = 1; // 大神ID
    uint32 game_id = 2; // 游戏id
}
// 联系客服响应
message ContactCustomerServiceResponse {
    UserProfile customer_info = 1; // 客服个人信息
}

enum TabType {
    TAB_TYPE_UNSPECIFIED = 0; // 未指定，不要用，服务端做兼容，默认转成第一个
    TAB_TYPE_PAYED = 1; // 待接单
    TAB_TYPE_RECEIVED = 2; // 进行中
    TAB_TYPE_IN_REFUNDING = 3; // 退款中
    TAB_TYPE_IN_APPEALING = 4; // 申诉中
    TAB_TYPE_CANCELED = 5; // 已取消
    TAB_TYPE_FINISHED = 6; // 已完成
}


// 请求获取订单列表
// /esport/order/get_guild_order_list_for_customer
message GetGuildOrderListForCustomerRequest {
    uint32 tab_type = 1;        // 订单tab的类型，不是状态！！！！：TabType
    uint32 offset = 2;//
    uint32 limit = 3;//
}

// 订单售后状态
enum RefundStatus {
    REFUND_STATUS_UNSPECIFIED = 0;
    REFUND_STATUS_REFUNDING = 1;         // 退款中
    REFUND_STATUS_REFUND_ACCEPT = 2;     // 电竞指导接受退款
    REFUND_STATUS_REFUND_REJECT = 3;     // 电竞指导拒绝退款
    REFUND_STATUS_APPEALING = 4;         // 申诉中
    REFUND_STATUS_APPEALING_ACCEPT = 5;  // 申诉成功
    REFUND_STATUS_APPEALING_REJECT = 6;  // 申诉失败
    REFUND_STATUS_APPEALING_APPLIED = 7;  // 用户发起申诉，但是大神还未处理
}

// 订单已取消子状态
enum CanceledOrderSubStatus {
    CANCELED_ORDER_SUB_STATUS_UNSPECIFIED = 0;
    CANCELED_ORDER_SUB_STATUS_PLAYER_CANCEL = 1;    // 玩家取消
    CANCELED_ORDER_SUB_STATUS_COACH_REFUSE = 2;     // 电竞指导拒绝
    CANCELED_ORDER_SUB_STATUS_COACH_TIMEOUT = 3;    // 超时未处理
}

// 订单状态
enum OrderStatus {
    ORDER_STATUS_UNSPECIFIED = 0;
    ORDER_STATUS_PAYED = 1;             // 已支付/待接单
    ORDER_STATUS_RECEIVED = 2;          // 已接单/进行中
    ORDER_STATUS_IN_REFUNDING = 3;      // 售后退款申诉中 子状态 see RefundStatus
    ORDER_STATUS_FINISHED = 4;          // 已完成
    ORDER_STATUS_CANCELED = 5;          // 已取消 子状态 see CanceledOrderSubStatus
    ORDER_STATUS_REFUNDED = 6;          // 已退款
}

// 订单简略信息
message OrderSimpleInfo {
    UserProfile player = 1;   // 玩家个人信息
    ProductOrder product_order = 2; // 商品订单信息
    string order_id = 3;      // 订单号
    uint32 status = 4;        // 订单状态 see OrderStatus
    uint32 sub_status = 5;    // 子状态 例如 ReceivedOrderSubStatus 、CanceledOrderSubStatus
    int64 pay_time = 6;       // 下单时间
    int64 order_end_time = 7; // 订单过期时间
    string offset_id = 8;     // 位置id
    string status_desc = 9;   // 描述
    bool is_notify_finish = 10; // 是否已提醒去完成
    bool can_coach_upload_appeal = 11;// 能否上传申诉凭证, 在退款申诉中时有效
    RefundType refund_type = 12;    // 退款类型， 在退款申诉中时有效
    int64 update_time = 13;
    bool evaluate_entry = 14;  // 是否展示评论入口
    bool can_appeal = 15;       // 能否申诉
    string appeal_id = 16;      // 申诉id
    string refund_id = 17;      // 退款id
    int64 coach_upload_appeal_deadline = 18;// 电竞指导上传申诉凭证截止时间
    int64 statue_update_time = 19; // 状态更新时间
}

// 订单列表响应
message GetGuildOrderListForCustomerResponse {
    repeated OrderSimpleInfo order_list = 1;
    uint32 next_offset = 2;
}

// 获取顶部游戏列表
// /esport/skill/get_top_game_list
message GetTopGameListRequest {}

message GameItem {
    uint32 game_id = 1; // 游戏id
    string game_name = 2; // 游戏名称
    string game_icon = 3; // 游戏图标
}
message GetTopGameListResponse {
    repeated GameItem item_list = 1;
}

// 客服获取公会旗下的大神列表（分页）
// esport/hall/get_coach_list_for_customer
message GetCoachListForCustomerRequest {
    uint32 page_offset = 1; // 分页，第一页传0
    uint32 page_size = 2; // 分页大小
    string condition = 3; // 条件
    uint32 game_id = 4; // 游戏id
    repeated uint32 viewed_uid = 5;// 已查看的大神uid
}

// 客服获取公会旗下的大神列表（分页）响应
message GetCoachListForCustomerResponse {
    repeated EsportAreaCoachInfo coach_list = 1; // 教练列表
    uint32 next_page_offset = 2; // 下一页
}

// 请求发送大神名片
// /esport/hall/send_coach_skill_card
message SendCoachSkillCardRequest {
    uint32 coach_id = 1; // 大神ID
    uint32 game_id = 2; // 游戏id
    uint32 user_id = 3;// 用户的id
}

// 发送大神名片响应
message SendCoachSkillCardResponse {}

// 获取优惠券背包
// uri: /esport/coupon/get_coupon_package
message GetCouponPackageRequest {
    uint32 page = 1;
    uint32 page_size = 2;
    uint32 request_type = 3; // 0:未使用 1:已过期
}
message GetCouponPackageResponse {
    uint32 total = 1;
    bool has_more = 2;
    repeated CouponDetail coupons = 3;
}

// 获取可用优惠券
// uri: /esport/coupon/get_available_coupon
message GetAvailableCouponRequest {
    uint32 page = 1;
    uint32 page_size = 2;
    uint32 request_type = 3; // 0:可使用 1:不可用
    uint32 product_id = 4;    // 商品id
    uint32 product_count = 5; // 商品数量
    repeated string chosen_coupons = 6; // 选中的优惠券列表，目前单选。在第一页最前面返回，其它页不返回
}
message GetAvailableCouponResponse {
    uint32 total = 1;
    bool has_more = 2;
    repeated CouponDetail coupons = 3;
}

message CouponDetail {
    string coupon_id = 1;
    string coupon_name = 2; // 名称
    string usage_limit_text = 3; // 限制文案
    string valid_date_text = 4; // 有效期文案
    string corner_text = 5; // 角标文案
    uint32 main_desc_reduce_price = 6; // 主描述中，减免的T豆（需要前端转换为元）
    string sub_desc_text = 7; // 副描述文案
}



// 上报已曝光大神列表
message ReportExposeCoachRequest {
    enum ExposeCoachType {
        EXPOSE_COACH_TYPE_UNSPECIFIED = 0;
        EXPOSE_COACH_TYPE_ESPORT_AREA = 1; // 电竞专区
        EXPOSE_COACH_TYPE_ESPORT_KING_TAB = 2; // 王者tab
        EXPOSE_COACH_TYPE_ESPORT_UGC_CHANNEL = 3; // ugc开黑房
        EXPOSE_COACH_TYPE_ESPORT_HELPER_ACTIVITY = 4; // 助手活动页
    }
    repeated uint32 expose_coach_list = 1; // 曝光大神uid列表
    uint32 game_id = 2; // 曝光的游戏id
    uint32 expose_type = 3; // 曝光类型 see ExposeCoachType
}

message ReportExposeCoachResponse {
}

// 获取游戏筛选项
message GetActivityGamePropertyRequest {
    uint32 scene_type = 1; // 场景类型
    uint32 game_id = 2; // 游戏id
}

message GetActivityGamePropertyResponse {
    repeated esport_hall.GameProperty property_list = 1; // 游戏属性列表
}

message GetCoachMissionInfoPageRequest {
}


enum MasterSwitchType {
    MASTER_SWITCH_TYPE_UNSPECIFIED = 0;
    MASTER_SWITCH_TYPE_QUICK_RECEIVE = 1; // 秒接单
    MASTER_SWITCH_TYPE_FIRST_ROUND_DISCOUNT = 2; // 首局优惠
    MASTER_SWITCH_TYPE_NEW_CUSTOMER_DISCOUNT = 3; // 新客价
}

message MasterSwitch {
    bool is_open = 1; // 是否开启
    uint32 master_switch_type = 2; // 开关类型
    bool could_open = 3; // 是否可开启 // 秒接单开关，需要有技能处于接单时才能打开
}

message GetCoachMissionInfoPageResponse {
    uint32 god_level = 1; // 大神等级
    uint32 today_exposure = 2; // 今日曝光
    uint32 total_view_cnt = 3; // 总访问量
    uint32 recent_addition_view_cnt = 4; // 最近新增访问量
    uint32 proportion = 5; // 分成比例，0-100
    uint32 projected_income = 6; // 预计收益 积分
    repeated MasterSwitch master_switch_list = 7; // 开关列表
}

message GetIncentiveTaskEntrySwitchRequest {
}

message GetIncentiveTaskEntrySwitchResponse {
    bool show_switch = 1; // 是否展示激励任务入口
}

// 一键找人 ////////////////////////////////////

// 获取抢单中心概要
// uri: /esport/grab/get_grab_center_overview
message GetGrabCenterOverviewRequest {
    int64 last_refresh_ts = 1; // 上次刷新时间戳，单位秒
}
message GetGrabCenterOverviewResponse {
    uint32 update_order_count = 1; // 更新订单数量
    uint32 going_order_count = 2; // 进行中订单数量
}

// 获取待抢订单列表
// uri: /esport/grab/get_pending_grab_order_list
message GetPendingGrabOrderListRequest {
    uint32 page = 1;
    uint32 size = 2;
    int64 refresh_ts = 3; // 本次刷新所用的时间戳，单位秒。page=1时不用传；page>1时，传page=1返回的时间戳
}
message GetPendingGrabOrderListResponse {
    repeated OneKeyFindCoachDemand order_list = 1;
    bool has_more = 2;
    int64 refresh_ts = 3; // 本次刷新所用的时间戳，单位秒
}

// 获取已抢订单列表
// uri: /esport/grab/get_grabbed_order_list
message GetGrabbedOrderListRequest {
    uint32 page = 1;
    uint32 size = 2;
}
message GetGrabbedOrderListResponse {
    repeated GrabOrderInfo order_list = 1;
    bool has_more = 2;
}

// 发起抢单
// uri: /esport/grab/grab_order
message GrabOrderRequest {
    string publish_id = 1;
    uint32 grab_type = 2; // 参考GrabType
    string grab_text = 3;
    string grab_audio = 4;
    uint32 grab_audio_duration = 5;
}
message GrabOrderResponse {
}

// 异步检测语音
// uri: /esport/grab/async_check_audio
message AsyncCheckAudioRequest {
    string grab_audio = 1;
}
message AsyncCheckAudioResponse {
}

// 查询检测语音结果
// 1. 如果检测结果为正在检测，则每隔1s轮询；直到检测结果为有效或无效，或者10s超时，再停止轮询
// 2. 如果检测结果为无效，则提示用户，结束流程
// 3. 如果检测结果为有效，则继续调用抢单接口
// uri: /esport/grab/query_check_audio_status
message QueryCheckAudioStatusRequest {
    string grab_audio = 1;
}
message QueryCheckAudioStatusResponse {
    enum CheckStatus {
        CHECK_STATUS_UNSPECIFIED = 0;
        CHECK_STATUS_CHECKING = 1; // 正在检测
        CHECK_STATUS_VALID = 2; // 有效
        CHECK_STATUS_INVALID = 3; // 无效
    }
    uint32 check_status = 1; // 参考CheckStatus
}

enum GrabType {
    GRAB_TYPE_UNSPECIFIED = 0;
    GRAB_TYPE_TEXT = 1;
    GRAB_TYPE_AUDIO = 2;
}

message OneKeyFindCoachDemand {
    string publish_id = 1;
    uint32 game_id = 2;
    string game_name = 3;
    string game_icon = 4;
    uint32 player_uid = 5;
    string player_gender = 6;
    repeated string player_tag_list = 7;
    repeated string target_tag_list = 8;
    string target_custom_requirement = 9;
    uint32 grabbed_cnt = 10; // 已抢单人数
}

message GrabOrderInfo {
    OneKeyFindCoachDemand demand = 1;
    uint32 grab_type = 2; // 参考GrabType
    string grab_text = 3;
    string grab_audio = 4;
    uint32 grab_audio_duration = 5;
    uint32 grab_status = 6; // 参考GrabStatus
}

enum GrabStatus {
    GRAB_STATUS_UNSPECIFIED = 0;
    GRAB_STATUS_PENDING = 1; // 抢单中
    GRAB_STATUS_END = 2; // 已结束
    GRAB_STATUS_CHOSEN = 3; // 已抢到单
}