syntax = "proto3";

option go_package = "golang.52tt.com/protocol/services/apicentergo";
// buf:lint:ignore DIRECTORY_SAME_PACKAGE
package apicentergo;

message StConfigIosExtend
{
  bytes video_effect_url = 1;      // 特效url
}

message StPresentItemConfigExtend
{
  uint32 item_id = 1;
  bytes video_effect_url = 2;      // 特效url
  uint32 show_effect = 3;        // 播放特效 ga::PRESENT_SHOW_EFFECT_TYPE
  bool unshow_batch_option = 4;  // 是否展示批量送礼选项
  bool is_test = 5;          // 该礼物是否是测试 只有白名单用户可以拉取到
  uint32 flow_id = 6;        // 使用指定的流光id(流光配置不存在时，客户端会按默认的流光规则)
  StConfigIosExtend ios_extend = 7;  // ios的扩展信息
  bool notify_all = 8;      // 是否全服通知
  uint32 tag = 9;          // ga::PresentTagType
  bool force_sendable = 10;    // 强制可送的礼物(暂时只有背包礼物，这种礼物即使下架了也可以送出去)
  uint32 nobility_level = 11;     // 使用这个礼物的最低贵族等级
  bool unshow_present_shelf = 12;   //是否在礼物架上显示           true:不在礼物架显示          false:在礼物架显示
  bool show_effect_end = 13; // 是否显示下架时间
  bool effect_end_delay = 14; // 是否支持延长上架时间
  repeated CustomText custom_text = 15; // 自定义文案
  string mic_effect_url = 16; // 麦位延展特效的动效url
  string mic_effect_md5 = 17; // 麦位延展特效的动效md5
  string small_vap_effect_url = 18; // 小礼物的vap动效url
  string small_vap_effect_md5 = 19; // 小礼物的vap动效md5
  bool fusion_present = 20; // 是否是融合头像礼物
  bool is_box_breaking = 21; // 是否是需要开盒的全服礼物
  uint32 fans_level = 22; // 使用礼物所需粉丝等级
}

message CustomText {
  string key = 1;
  repeated string text = 2;
}

// 礼物配置信息
message StPresentItemConfig
{
  uint32 item_id = 1;
  string name = 2;        // 名称
  string icon_url = 3;      // 图标url
  uint32 price = 4;        // 价格
  uint32 score = 5;        // 收到一个礼物 收礼者 增加的 积分
  uint32 charm = 6;        // 收到一个礼物 收礼者 增加的 魅力值
  uint32 rank = 7;        // 排名
  uint32 effect_begin = 8;    // 上架时间
  uint32 effect_end = 9;      // 下架时间
  uint32 update_time = 10;    // 更新时间
  uint32 create_time = 11;    // 添加时间
  bool   is_del = 12;        // 是否已删除
  uint32 price_type = 13;      // PresentPriceType
  uint32 rich_value = 14;      // 送出一个礼物 送礼者 增加的土豪值
  StPresentItemConfigExtend extend = 15;  // 扩展信息，这部分会整块存到mysql的一个字段里
  FellowPresentConfig fellow = 16;  // 挚友相关信息，当extend.tag = 5 时才需要填
  repeated EffectDelayLevelInfo delay_info = 17; // 延迟下架相关参数， effect_end_delay
  bool is_banned = 18; // 是否强制下架
  float rank_float = 19;        // float的排序，新版用
}

message FellowPresentConfig {
  string unique_background_url = 1; // 唯一关系背景
  uint32 unique_source_type = 2; // 唯一关系背景的资源类型，FellowSourceType
  string unique_md5 = 3; // 唯一关系背景的md5
  string multi_background_url = 4; // 非唯一关系背景
  uint32 multi_source_type = 5; // 非唯一关系背景的资源类型，FellowSourceType
  string multi_md5 = 6; // 非唯一关系背景的md5
  string unique_background_img = 7; // 唯一关系动态背景的底图
  string multi_background_img = 8; // 非唯一关系动态背景的底图
  string source_zip = 9; // 资源包url
}

// 购买礼物的货币
enum PresentPriceType {
  PRESENT_PRICE_UNKNOWN = 0;
  PRESENT_PRICE_RED_DIAMOND = 1;
  PRESENT_PRICE_TBEAN = 2;
}

// 根据礼物id获取礼物配置
message GetPresentConfigByIdReq
{
  uint32 item_id = 1;
}
message GetPresentConfigByIdResp
{
  StPresentItemConfig item_config = 1;
}

// 礼物配置列表类型
enum ConfigListTypeBitMap {
  CONFIG_UNLIMIT = 0;
  CONFIG_NOT_EXPIRED = 1;
  CONFIG_NOT_DELETED = 2;
}

message GetPresentConfigListReq
{
  uint32 type_bitmap = 1;
  bool get_all = 2; // 是否获取所有礼物配置
}
message GetPresentConfigListResp
{
  repeated StPresentItemConfig item_list = 1;
}

// 增加礼物配置
message AddPresentConfigReq
{
  string name = 1;          // 名称
  string icon_url = 2;        // 图标url
  uint32 price = 3;          // 价格
  uint32 effect_begin = 4;      // 上架时间
  uint32 effect_end = 5;        // 下架时间
  uint32 rank = 6;          // 排名
  uint32 price_type = 7;        // PresentPriceType
  StPresentItemConfigExtend extend = 8;
  FellowPresentConfig fellow = 9;  // 挚友相关信息，当extend.tag = 5 时才需要填
  repeated EffectDelayLevelInfo delay_info = 10; // 延迟下架相关参数， effect_end_delay = true才填
  float rank_float = 11;  // float的排序，新版用
}

message EffectDelayLevelInfo{
  uint32 level = 1;  // 延迟等级
  uint32 send_count = 2;  // 升级所需数量
  uint32 day_count = 3;  // 可以延长的天数（0为无限）
  uint32 expire_day_count = 4; // 如果是无限期的等级，几天不送之后会自动下线
  uint32 notice_day_count = 5; // 离过期几天会提醒
}

message AddPresentConfigResp
{
  StPresentItemConfig item_config = 1;
}

// 删除礼物配置
message DelPresentConfigReq
{
  uint32 item_id = 1;
}
message DelPresentConfigResp
{
}

// 更新礼物配置
message UpdatePresentConfigReq
{
  StPresentItemConfig item_config = 1;
}
message UpdatePresentConfigResp
{
}

message UserPresentSend{
  uint32 to_uid = 1;
  uint32 to_ttid = 2;
  string to_nickname = 3;
  uint32 item_id = 4;
  string item_name = 5;
  uint32 item_count = 6;
  uint32 add_rich = 7;
  uint32 send_time = 8;
}

message UserPresentReceive{
  uint32 from_uid = 1;
  uint32 from_ttid = 2;
  string from_nickname = 3;
  uint32 item_id = 4;
  string item_name = 5;
  uint32 item_count = 6;
  uint32 add_charm = 7;
  uint32 add_score = 8;
  uint32 receive_time = 9;
}

// 用户送礼查询
message GetUserPresentSendReq
{
  uint32 uid = 1;
  uint32 begin_ts = 2;
  uint32 end_ts = 3;
}

message GetUserPresentSendResp
{
  repeated UserPresentSend present_send_detail = 1;
}


// 用户送礼查询
message GetUserPresentReceiveReq
{
  uint32 uid = 1;
  uint32 begin_ts = 2;
  uint32 end_ts = 3;
}

message GetUserPresentReceiveResp
{
  repeated UserPresentReceive present_receive_detail = 1;
}


// 用户送礼查询
message GetAllFellowPresentReq
{
}

message GetAllFellowPresentResp
{
  repeated StPresentItemConfig fellow_present_list = 1;
}

// 增加礼物配置
message AddCustomizedPresentConfigReq
{
  CustomizedPresentConfig config = 1;
}

message AddCustomizedPresentConfigResp
{
}

message CustomizedPresentConfig{
  uint32 gift_id = 1;       // 专属礼物id， 插入时不用填
  string name = 2;          // 名称
  string icon_url = 3;        // 图标url
  uint32 price = 4;          // 价格
  uint32 rank = 5;          // 排名
  uint32 price_type = 6;        // PresentPriceType
  StPresentItemConfigExtend extend = 7;
  string cmd_url = 8;  // 专属礼物的cms链接
  uint32 max_level = 9; // 最高权限等级
  repeated LevelEffectConfig effect_config = 10; // 不同权限等级对应的上架时间
  repeated CustomConfig custom_config = 11; // 可用的组件与样式
  repeated LevelConfig level_config = 12;   // 不同等级具有的组件权限
  repeated CustomMethod custom_method = 13; // 样式与礼物特效的对应关系
  string level_text = 14; // 专属礼物的等级说明
  uint32 update_time = 15;    // 更新时间
  uint32 create_time = 16;    // 添加时间
  uint32 score = 17;        // 收到一个礼物 收礼者 增加的 积分
  uint32 charm = 18;        // 收到一个礼物 收礼者 增加的 魅力值
  uint32 rich_value = 19;        // 收到一个礼物 收礼者 增加的 积分
  float rank_float = 20; // float的排序，新版用
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message CustomConfig{
  uint32 custom_id = 1; // 部件id
  string custom_name = 2;  // 部件名
  string custom_text = 3;  // 部件说明
  repeated OptionConfig OptionConfig = 4;  // 选项
}

message LevelConfig{
  uint32 level = 1; // 权限等级
  repeated CustomOption custom_option = 2; // 该等级可用的组件
}

message OptionConfig{
  uint32 option_id = 1; // 样式id
  string option_name = 2;  // 样式名
}

message LevelEffectConfig{
  uint32 level = 1;   // 权限等级
  uint32 effect_begin = 2;
  uint32 effect_end = 3;
}


message CustomMethod{
  repeated CustomOption options = 1;
  string effect_url = 2;
  string effect_md5 = 3;
  uint32 preview_type = 4;   // 0 图片， 1 MP4
  string preview_url = 5;
  string preview_md5 = 6;
}

message CustomOption{
  uint32 custom_id = 1; // 样式id
  uint32 option_id = 2; // 部件id
}

// 更新礼物配置
message UpdateCustomizedPresentConfigReq
{
  CustomizedPresentConfig config = 1;
}

// 更新礼物配置
message UpdateCustomizedPresentConfigResp
{
}


// 获取礼物配置
message GetAllCustomizedPresentConfigReq
{

}

message GetAllCustomizedPresentConfigResp{
  repeated CustomizedPresentConfig config = 1;
}

// 删除礼物配置
message DelCustomizedPresentConfigReq
{
  uint32 id = 1; // 要删除的礼物id
}

message DelCustomizedPresentConfigResp{
}

message TicketConfig{
  uint32 id = 1 ;  // id ，同礼物id
  repeated uint32 channel_id_list = 2; // 可用的房间列表
  string backpack_top_text = 3;// 顶部文案
  string homepage_text = 4; // 主页文案
  uint32 last_operator_time = 5; // 最后操作时间
  string im_push_msg = 6; // im推送文案
}

message TicketPresentConfig{
  StPresentItemConfig config = 1;  // 礼物本身的配置
  TicketConfig ticket_config = 2; // 卷的配置
}

// 增加礼物配置
message AddTicketPresentConfigReq
{
   TicketPresentConfig ticket_present_config = 1;
}

message AddTicketPresentConfigResp
{
   TicketPresentConfig ticket_present_config = 1;
}

// 更新礼物配置
message UpdateTicketPresentConfigReq
{
   TicketPresentConfig ticket_present_config = 1;
}

message UpdateTicketPresentConfigResp
{
   TicketPresentConfig ticket_present_config = 1;
}


// 获取礼物配置
message GetAllTicketPresentConfigReq
{

}

message GetAllTicketPresentConfigResp{
  repeated TicketPresentConfig config = 1;
}

// 删除礼物配置
message DelTicketPresentConfigReq
{
  uint32 id = 1; // 要删除的礼物id
}

message DelTicketPresentConfigResp{
}