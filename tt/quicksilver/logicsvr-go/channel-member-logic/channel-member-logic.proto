syntax = "proto3";

// buf:lint:ignore PACKAGE_SAME_DIRECTORY
package logic;

// buf:lint:ignore PACKAGE_SAME_GO_PACKAGE
option go_package = "golang.52tt.com/protocol/services/logicsvr-go/channel-member-logic";

import "tt/quicksilver/logicsvr-go/gateway/options/options.proto";
import "channel/channel_.proto";

service ChannelMemberLogic {
    option (logic.gateway.service_ext) = {
        service_name: "channel-member-logic"
    };

    rpc ChannelHideConsume (ga.channel.ChannelHideConsumeReq) returns (ga.channel.ChannelHideConsumeResp) {
        option (logic.gateway.command) = {
          id: 31011
        };
    }

    rpc ChannelGetChannelMemberVipStatistics (ga.channel.ChannelGetChannelMemberVipStatisticsReq) returns (ga.channel.ChannelGetChannelMemberVipStatisticsResp) {
        option (logic.gateway.command) = {
            id: 31012
        };
    }
}