syntax = "proto3";

option go_package = "golang.52tt.com/protocol/services/esport-skill";

package esport_skill;


service EsportSkill {
    //----------------- 运营后台接口 -----------------
    rpc AddEsportGameConfig (AddEsportGameConfigRequest) returns (AddEsportGameConfigResponse);
    rpc UpdateEsportGameConfig (UpdateEsportGameConfigRequest) returns (UpdateEsportGameConfigResponse);
    rpc DeleteEsportGameConfig (DeleteEsportGameConfigRequest) returns (DeleteEsportGameConfigResponse);
    rpc GetEsportGameConfigListByPage (GetEsportGameConfigListByPageRequest) returns (GetEsportGameConfigListByPageResponse);
    rpc GetUserAuditSkill (GetUserAuditSkillRequest) returns (GetUserAuditSkillResponse);
    // 冻结大神技能
    rpc FreezeCoachSkill (FreezeCoachSkillRequest) returns (FreezeCoachSkillResponse);
    // 解冻大神技能
    rpc UnfreezeCoachSkill (UnfreezeCoachSkillRequest) returns (UnfreezeCoachSkillResponse);
    // 获取技能冻结操作列表
    rpc GetSkillFreezeOperationList (GetSkillFreezeOperationListRequest) returns (GetSkillFreezeOperationListResponse);

    // 设置技能审核状态
    rpc SetUserSkillAuditType (SetUserSkillAuditTypeRequest) returns (SetUserSkillAuditTypeResponse);

    // 获取所有游戏名片配置
    rpc GetAllGameCardConfig (GetAllGameCardConfigRequest) returns (GetAllGameCardConfigResponse);


    //获取用户当前的技能列表
    rpc GetUserCurrentSkill (GetUserCurrentSkillRequest) returns (GetUserCurrentSkillResponse);
    rpc BatchGetUserCurrentSkill (BatchGetUserCurrentSkillRequest) returns (BatchGetUserCurrentSkillResponse);
    rpc ModifyUserSkill (ModifyUserSkillRequest) returns (ModifyUserSkillResponse);
    rpc DelUserSkill (DelUserSkillRequest) returns (DelUserSkillResponse);
    rpc GetGameList (GetGameListRequest) returns (GetGameListResponse);

    rpc AddUserAuditSkill (AddUserAuditSkillRequest) returns (AddUserAuditSkillResponse);
    rpc TestAddUserSkill (TestAddUserSkillRequest) returns (TestAddUserSkillResponse);
    rpc BatchGetAuditSkill (BatchGetAuditSkillRequest) returns (BatchGetAuditSkillResponse);

    rpc HandleGameUpdate (HandleGameUpdateRequest) returns (HandleGameUpdateResponse);

    // 获取开关状态
    rpc GetSwitch (GetSwitchRequest) returns (GetSwitchResponse);
    // 设置开关状态
    rpc SetSwitch (SetSwitchRequest) returns (SetSwitchResponse);
    // 获取顶部游戏列表
    rpc GetTopGameList (GetTopGameListRequest) returns (GetTopGameListResponse);

    // 获取游戏信息
    rpc GetGameDetailById (GetGameDetailByIdRequest) returns (GetGameDetailByIdResponse);

    // 批量获取游戏信息
    rpc GetGameDetailByIds (GetGameDetailByIdsRequest) returns (GetGameDetailByIdsResponse);
    // 通过游戏名称批量获取游戏信息
    rpc BatchGetGameInfoByNames (BatchGetGameInfoByNamesRequest) returns (BatchGetGameInfoByNamesResponse);

    // 设置技能风控状态
    rpc SetUserSkillRiskAuditType (SetUserSkillRiskAuditTypeRequest) returns (SetUserSkillRiskAuditTypeResponse);

    // 获取全部游戏简略信息
    rpc GetAllGameSimpleInfo (GetAllGameSimpleInfoRequest) returns (GetAllGameSimpleInfoResponse);

    rpc GetUserSkillByGameId (GetUserSkillByGameIdRequest) returns (GetUserSkillByGameIdResponse);
    // 获取最低价格
    rpc GetMinimumPrice (GetMinimumPriceRequest) returns (GetMinimumPriceResponse);

    // 获取技能状态
    rpc GetUserSkillStatus (GetUserSkillStatusRequest) returns (GetUserSkillStatusResponse);
    // 获取技能冻结状态
    rpc GetUserSkillFreezeStatus (GetUserSkillFreezeStatusRequest) returns (GetUserSkillFreezeStatusResponse);
    // 批量获取技能冻结状态
    rpc BatGetUserSkillFreezeStatus (BatGetUserSkillFreezeStatusRequest) returns (BatGetUserSkillFreezeStatusResponse);

    // PricingConfigurationService 为运营管理后台提供定价配置服务接口

    // 获取用户知名选手信息
    rpc BatchGetUserRenownedInfo (BatchGetUserRenownedInfoRequest) returns (BatchGetUserRenownedInfoResponse);
    // CreateLabel 创建大神或技能标识
    rpc CreateLabel (CreateLabelRequest) returns (CreateLabelResponse) {}
    // EditLabel 编辑标识配置
    rpc EditLabel (EditLabelRequest) returns (EditLabelResponse) {}
    // DeleteLabel 删除标识配置
    rpc DeleteLabel (DeleteLabelRequest) returns (DeleteLabelResponse) {}
    // ListLabels 查询标识列表
    rpc ListLabels (ListLabelsRequest) returns (ListLabelsResponse) {}
    // IssueLabel 发放标识
    rpc IssueLabel (IssueLabelRequest) returns (IssueLabelResponse) {}
    // RevokeLabel 用于回收已发放标识。
    rpc RevokeLabel (RevokeLabelRequest) returns (RevokeLabelResponse);
    // QueryIssuanceRecords 用于查询发放记录列表。
    rpc QueryIssuanceRecords (QueryIssuanceRecordsRequest) returns (QueryIssuanceRecordsResponse);
    // AddRenownedPlayer 发放知名选手标识
    rpc AddRenownedPlayer (AddRenownedPlayerRequest) returns (AddRenownedPlayerResponse) {}
    // ListRenownedPlayers 查询知名选手列表
    rpc ListRenownedPlayers (ListRenownedPlayersRequest) returns (ListRenownedPlayersResponse) {}
    // BatchRemoveRenownedPlayers 批量移除知名选手
    rpc BatchRemoveRenownedPlayers (BatchRemoveRenownedPlayersRequest) returns (BatchRemoveRenownedPlayersResponse) {}
    // UpdateRenownedPlayerRequest 更新知名选手信息
    rpc UpdateRenownedPlayer (UpdateRenownedPlayerRequest) returns (UpdateRenownedPlayerResponse) {}
    // 获取基础定价
    rpc GetBasePriceSetting (GetBasePriceSettingRequest) returns (GetBasePriceSettingResponse) {}
    // 设置基础定价
    rpc SetBasePriceSetting (SetBasePriceSettingRequest) returns (SetBasePriceSettingResponse) {}
    // 计算教练价格
    rpc CalculatePrice (CalculatePriceRequest) returns (CalculatePriceResponse);
    // 计算教练的技能列表的价格
    rpc CalculatePriceByGames (CalculatePriceByGamesRequest) returns (CalculatePriceByGamesResponse);
    // 获取教练当前可申请的标识列表
    rpc GetCoachApplicableLabels (GetCoachApplicableLabelsRequest) returns (GetCoachApplicableLabelsResponse);
    // 检查教练身份和检查是否拥有游戏技能
    rpc CheckCoachIdentityAndSkill (CheckCoachIdentityAndSkillRequest) returns (CheckCoachIdentityAndSkillResponse);
    // 批量获取指定游戏的多个教练的标识列表
    rpc BatchGetCoachLabelsForGame (BatchGetCoachLabelsForGameRequest) returns (BatchGetCoachLabelsForGameResponse);
    // 批量检查教练是否拥有指定的技能
    rpc BatchCheckCoachHasGame (BatchCheckCoachHasGameRequest) returns (BatchCheckCoachHasGameResponse);
    // SetGameGuaranteeStatus
    rpc SetGameGuaranteeStatus (SetGameGuaranteeStatusRequest) returns (SetGameGuaranteeStatusResponse);
    // debug接口，检查大神是否拥有开启包赢权限
    rpc DebugCheckGuaranteeWinPermission (DebugCheckGuaranteeWinPermissionRequest) returns (DebugCheckGuaranteeWinPermissionResponse);


    // ========================== 特色标签 =====================
    // 获取特色标签列表
    rpc GetSpecialLabelList (GetSpecialLabelListRequest) returns (GetSpecialLabelListResponse);
    // 更新用户特色标签
    rpc UpdateUserSpecialLabel (UpdateUserSpecialLabelRequest) returns (UpdateUserSpecialLabelResponse);
    // 批量获取用户特色标签
    rpc BatchGetUserSpecialLabel (BatchGetUserSpecialLabelRequest) returns (BatchGetUserSpecialLabelResponse);
    // 检查标签展示顺序
    rpc CheckLabelOrder (CheckLabelOrderRequest) returns (CheckLabelOrderResponse);
    // 获取用户在游戏x中的特色标签
    rpc BatchGetUserGameSpecialLabel (BatchGetUserGameSpecialLabelRequest) returns (BatchGetUserGameSpecialLabelResponse);

    // 获取标签发放记录
    rpc GetLabelIssueRecord (GetLabelIssuanceRecordRequest) returns (GetLabelIssuanceRecordResponse);
    // 获取特色标签发放记录
    rpc GetSpecialIssueRecord (GetSpecialLabelIssuanceRecordRequest) returns (GetSpecialLabelIssuanceRecordResponse);

    // 用户设置标识是否参与加价开关
    rpc SetLabelPriceSwitch (SetLabelPriceSwitchRequest) returns (SetLabelPriceSwitchResponse);
}

// 游戏类型
// buf:lint:ignore ENUM_PASCAL_CASE
enum GAME_TYPE {
    GAME_TYPE_INVALID = 0;
    GAME_TYPE_MOBILE = 1; // 手游
    GAME_TYPE_PC = 2; // 端游
}

// 游戏section（段位信息、擅长位置、擅长英雄...）
message GameInformation {
    // 游戏资料类型
    // buf:lint:ignore ENUM_PASCAL_CASE
    enum GAME_INFORMATION_TYPE {
        GAME_INFORMATION_TYPE_INVALID = 0;
        GAME_INFORMATION_TYPE_RANK = 1; // 段位信息
        GAME_INFORMATION_TYPE_DIY = 2; // 自定义
    }
    // buf:lint:ignore ENUM_PASCAL_CASE
    enum GAME_INFORMATION_SELECT_TYPE {
        GAME_INFORMATION_SELECT_TYPE_INVALID = 0;
        GAME_INFORMATION_SELECT_TYPE_SINGLE = 1; // 单选
        GAME_INFORMATION_SELECT_TYPE_MULTI = 2; // 多选
    }
    GAME_INFORMATION_TYPE information_type = 1;
    string section_name = 2; // 资料名称（段位信息、擅长位置、擅长英雄...）
    repeated string item_list = 3; // 选择项（数量需要大于等于2）
    GAME_INFORMATION_SELECT_TYPE select_type = 4; // 单选还是多选
    uint32 section_id = 5; // 信息id
}

// 基础定价
message BasePrice {
    GAME_PRICING_UNIT_TYPE game_pricing_unit_type = 1; // 价格单位
    map<uint32, uint32> rank_price_map = 2; // 段位价格 <CoachRank, price>
}

// 定价类型
// buf:lint:ignore ENUM_PASCAL_CASE
enum GAME_PRICING_TYPE {
    GAME_PRICING_TYPE_INVALID = 0;
    GAME_PRICING_TYPE_UNIFY = 1; // 统一定价
    GAME_PRICING_TYPE_RANK = 2; // 分段位定价
}
// 价格单位
// buf:lint:ignore ENUM_PASCAL_CASE
enum GAME_PRICING_UNIT_TYPE {
    GAME_PRICING_UNIT_TYPE_INVALID = 0;
    GAME_PRICING_UNIT_TYPE_PER_30_MIN = 1; // 30分钟价格
    GAME_PRICING_UNIT_TYPE_PER_GAME = 2; // 每场游戏价格
}

// 定价
message GamePricing {
    GAME_PRICING_TYPE game_pricing_type = 1; // 定价类型
    GAME_PRICING_UNIT_TYPE game_pricing_unit_type = 2; // 价格单位
    repeated uint32 price = 3; // 选择分段位定价时，该字段大于等于1，并且按照数组下标一一对应上方的段位名称，段位从低到高
}

message EsportGameConfig {
    uint32 game_id = 1; // 插入时不填
    string name = 2; // 游戏名称， 运营后台限制文字长度
    GAME_TYPE game_type = 3; // 游戏类型
    string game_icon = 4; // 游戏图标
    string game_background = 5; // 游戏背景
    string game_color = 6; // 色值
    float game_rank = 7; // 游戏排行
    string skill_evidence = 8; // 技能示意图
    string skill_desc = 9; // 技能图说明
    repeated GameInformation game_information_list = 10; // 游戏资料详情
    GamePricing game_pricing = 11; // 游戏定价
    int64 update_time = 12; // 更新时间，读取列表时返回
    BasePrice base_price = 13; // 基础定价
    repeated GameCardInfoItem game_card_info_item_list = 14;  // 游戏卡片信息项
}

// 新增游戏配置
message AddEsportGameConfigRequest {
    EsportGameConfig esport_game_config = 1;
}

message AddEsportGameConfigResponse {

}



// 更新游戏配置
message UpdateEsportGameConfigRequest {
    EsportGameConfig config = 1;
}

message UpdateEsportGameConfigResponse {

}

// 删除游戏
message DeleteEsportGameConfigRequest {
    uint32 game_id = 1;
}

message DeleteEsportGameConfigResponse {

}

// 分页获取游戏列表
message GetEsportGameConfigListByPageRequest {
    GAME_TYPE game_type = 1; // 游戏类型（端游/手游）
    string game_name = 2; // 通过游戏名称搜索（可选）
    uint32 page_num = 3; // 页码（从0开始）
    uint32 page_size = 4; // 页大小
}

message GetEsportGameConfigListByPageResponse {
    repeated EsportGameConfig item_list = 1;
    uint32 total_cnt = 2; // 记录总数
}



// 获取顶部游戏列表
message GetTopGameListRequest {
    uint32 uid = 1; // 不同用户返回列表可能不同
    GAME_TYPE game_type = 2; // 游戏类型 pc端需要
}

message GetTopGameListResponse {
    repeated GameItem item_list = 1; // 游戏列表
}

message SectionInfo {
    string section_name = 1;        // 资料名称（段位信息、擅长位置、擅长英雄...）
    repeated string item_list = 2;  // 填写项
    uint32 section_id = 3;  // section_id
}

//用户游戏资料信息
message UserSkillInfo {
    uint32 game_id = 1;
    string game_name = 2;                      // 游戏名称
    string skill_evidence = 3;                 // 技能图
    string audio = 4;                          // 语音介绍
    uint32 audio_duration = 5;                 // 语音介绍时长(秒数)
    repeated SectionInfo section_list = 6;    // 技能详细信息
    string text_desc = 7;                      // 文字介绍
    uint32 game_rank = 9;                     //游戏排名
    bool is_guarantee_win = 10; // 大神包赢开关
    FreezeType freeze_type = 11; // 冻结类型
    int64 freeze_stop_ts = 12; // 冻结结束时间
}

// 技能审核来源
enum AuditSource {
    AUDIT_SOURCE_UNSPECIFIED = 0;   // 未知
    AUDIT_SOURCE_NEW_ROLE = 1;      // 申请成为电竞指导
    AUDIT_SOURCE_NEW_SKILL = 2;   // 新增技能
    AUDIT_SOURCE_MOD_SKILL = 3;   // 修改技能
}


//增加新技能审核信息请求
message AddUserAuditSkillRequest {
    enum IgnoreAuditResource {// ps: 新增值应遵循2^n
        IGNORE_AUDIT_RESOURCE_UNSPECIFIED = 0; // 未知
        IGNORE_AUDIT_RESOURCE_EVIDENCE = 1; // 忽略技能图审核
        IGNORE_AUDIT_RESOURCE_AUDIO = 2;    // 忽略语音介绍审核
    }
    uint32 uid = 1;
    string audit_token = 2;            //申请陪陪的审核版本信息
    uint32 audit_source = 3;           //技能审核来源 see AuditSource
    repeated UserSkillInfo skill = 4;  //技能信息
    uint32 guild_id = 5;               //申请公会ID
    uint32 audit_type = 6;             //个人陪玩申请签约公会，直接设置成已审核
    uint64 ignore_audit_resource = 7;  //忽略审核资源 see IngoreAuditResource
}

//增加新技能审核信息响应
message AddUserAuditSkillResponse {
}

//获取技能审核信息请求
message GetUserAuditSkillRequest {
    uint32 uid = 1;
    string audit_token = 2;    //申请陪陪的审核版本信息
    uint32 audit_source = 3;   //技能审核来源 see AuditSource
    bool with_url_prefix = 4;       //是否需要带上url前缀
}

//获取技能审核信息响应
message GetUserAuditSkillResponse {
    uint32 uid = 1;
    repeated UserSkillInfo skill = 2;  //技能信息
    uint32 audit_type = 3;             //平台审核结果 see esport-role.proto EsportAuditType
}



// 搜索类型
enum AuditSearchType {
    AUDIT_SEARCH_TYPE_UNSPECIFIED = 0;     // 未知
    AUDIT_SEARCH_TYPE_BY_USER = 1;    // 个人记录
    AUDIT_SEARCH_TYPE_BY_GUILD = 2;   // 公会记录
    AUDIT_SEARCH_TYPE_ALL = 3;        // 全部
}


//获取新增技能审核信息请求
message BatchGetAuditSkillRequest {
    uint32 guild_id = 1;                //按公会ID搜索
    repeated uint32 uids = 2;           //按uid搜索
    repeated uint32 audit_type = 3;     // 审核结果 see esport-role.proto EsportAuditType
    uint32 off_set = 4;
    uint32 limit = 5;
    uint32 need_total = 6;             //是否需要总数量 1 需要
    uint32 search_type = 7;            //搜索类型
    repeated uint32 audit_source = 8;  //技能审核来源 see AuditSource
}

message AuditSkillRecord {
    uint32 uid = 1;
    string account = 2;
    string nick_name = 3;
    string ttid = 4;
    string audit_token = 5;            //审核唯一记录
    uint32 audit_type = 6;             //审核结果 see esport-role.proto EsportAuditType
    uint32 apply_time = 7;             //申请时间
    repeated UserSkillInfo skill = 8;  //技能信息
    uint32 audit_source = 9;          //技能审核来源 see AuditSource
    string reason = 10;               //拒绝原因
    uint32 guild_id = 11;             //公会ID
    string sign_duration = 12;        //签约时长
    string operator = 13;             //审核人员
    uint32 update_time = 14;          //最后更新时间
}

//获取新增技能审核信息请求
message BatchGetAuditSkillResponse {
    uint32 guild_id = 1;
    repeated uint32 uids = 2;
    repeated uint32 audit_type = 3;     // 审核结果 see esport-role.proto EsportAuditType
    uint32 off_set = 4;
    uint32 limit = 5;
    repeated AuditSkillRecord list = 6; //审核记录
    uint32 total_count = 7;
    uint32 search_type = 8;              //搜索类型
    repeated uint32 audit_source = 9;  //技能审核来源 see AuditSource
}


//设置用户技能审核结果
message SetUserSkillAuditTypeRequest {
    uint32 uid = 1;
    string audit_token = 2;
    uint32 audit_type = 3;        // 审核结果 see esport-role.proto EsportAuditType
    string reason = 4;            // 拒绝原因
    string operator = 5;          // 审核人
    repeated string audit_token_list = 6;  //批量审核
    bool is_cancel = 7;           //是否撤销，撤销不需要发送原因
}

message SetUserSkillAuditTypeResponse {
}

message SetUserSkillRiskAuditTypeRequest {
    uint32 uid = 1;
    string audit_token = 2;
    uint32 audit_type = 3;        // 审核结果 see esport-role.proto EsportAuditType
    string scene_code = 4;        // 场景码
}

message SetUserSkillRiskAuditTypeResponse {
}



//获取用户当前技能信息
message GetUserCurrentSkillRequest {
    uint32 uid = 1;
    bool with_url_prefix = 2;       //是否需要带上url前缀
}

message GetUserCurrentSkillResponse {
    uint32 uid = 1;
    repeated UserSkillInfo skill = 2;  //技能信息
    uint32 audit_type = 3;             //提交技能修改，显示审核状态 see esport-role.proto EsportAuditType
}

// 测试增加技能接口
message TestAddUserSkillRequest {
    uint32 uid = 1;
    UserSkillInfo skill = 2;  //技能信息
}

message TestAddUserSkillResponse {
}


//批量获取用户当前技能信息
message BatchGetUserCurrentSkillRequest {
    repeated uint32 uid = 1;
}

message UserCurrentSkill {
    uint32 uid = 1;
    repeated UserSkillInfo skill = 2;  //技能信息
    uint32 audit_type = 3;             //提交技能修改，显示审核状态 see esport-role.proto EsportAuditType
}

message BatchGetUserCurrentSkillResponse {
    repeated UserCurrentSkill list = 1;  //技能信息
}


//修改用户技能
message ModifyUserSkillRequest {
    uint32 uid = 1;
    UserSkillInfo skill = 2;
    uint32 guild_id = 3;
}

message ModifyUserSkillResponse {
}


message DelUserSkillRequest {
    uint32 uid = 1;
    uint32 game_id = 2;
}

message DelUserSkillResponse {
}

message GetUserSkillByGameIdRequest {
    uint32 uid = 1;
    uint32 game_id = 2;
    bool is_host = 3;  //是否主态查看
}


message GetUserSkillByGameIdResponse {
    uint32 uid = 1;
    uint32 game_id = 2;
    repeated UserSkillInfo audit_skill = 3;    //审核中的技能信息
    UserSkillInfo current_skill = 4;  //已经获得的技能信息
    uint32 audit_type = 5;            //审核状态 see esport-role.proto EsportAuditType
}


message GetUserSkillStatusRequest {
    uint32 uid = 1;
}

message GetUserSkillStatusResponse {
    uint32 uid = 1;
    map<uint32, uint32> status_map = 2; // status 1已通过， 2审核中
}

message GetUserSkillFreezeStatusRequest {
    uint32 uid = 1;
}

message GetUserSkillFreezeStatusResponse {
    map<uint32, SkillFreezeStatus> game_status_map = 1; // key是game_id
}

message BatGetUserSkillFreezeStatusRequest {
    repeated uint32 uid_list = 1;
}

message SkillFreezeStatusMap {
    map<uint32, SkillFreezeStatus> game_status_map = 1; // key是game_id
}
message BatGetUserSkillFreezeStatusResponse {
    map<uint32, SkillFreezeStatusMap> user_map = 1; // key是uid
}

message SkillFreezeStatus {
    uint32 game_id = 1;
    FreezeType freeze_type = 2;
    int64 freeze_stop_ts = 3;
}

// 分页请求游戏列表
message GetGameListRequest {
    GAME_TYPE game_type = 1; // 游戏类型
    string page_token = 2; // 分页token，第一页传空字符串
    uint32 page_size = 3; // 分页大小
}


message GameItem {
    uint32 game_id = 1; // 游戏id
    string game_name = 2; // 游戏名称
    string game_icon = 3; // 游戏图标
}

message GetGameListResponse {
    repeated GameItem item_list = 1; // 游戏列表
    string next_page_token = 2; // 非空则（无需判断记录条数）数据还未请求完全，下次请求带上这个token
}

// 开关状态
enum EsportSwitchStatus {
    SWITCH_STATUS_UNSPECIFIED = 0;
    SWITCH_STATUS_ON = 1; // 打开
    SWITCH_STATUS_OFF = 2; // 关闭
}

// 获取电竞指导开关
message GetSwitchRequest {
    uint32 uid = 1; // 用户uid
}

message GetSwitchResponse {
    SwitchStatus switch_status = 1;
}

message SwitchStatus {
    EsportSwitchStatus main_switch_status = 1; // 开关状态
    EsportSwitchStatus homepage_switch_status = 2; // 个人主页开关状态
    EsportSwitchStatus appeal_switch_status = 3; // 申诉开关
    EsportSwitchStatus sidebar_switch_status = 4; // 侧边栏开关
}

// 获取电竞指导开关
message SetSwitchRequest {
    SwitchStatus switch_status = 1; // 开关状态
}

message SetSwitchResponse {
}

message GetGameDetailByIdRequest {
    uint32 game_id = 1;
}

message GetGameDetailByIdResponse {
    EsportGameConfig config = 1;
    uint32 minimum_price = 2; // 统一最低价格
}

message GetGameDetailByIdsRequest {
    repeated uint32 game_ids = 1;
}

message GetGameDetailByIdsResponse {
    repeated EsportGameConfig config_list = 1;
    uint32 minimum_price = 2; // 统一最低价格
}

message GetAllGameSimpleInfoRequest {

}


// 获取最低价格
message GetMinimumPriceRequest {

}

message GetMinimumPriceResponse {
    uint32 minimum_price = 1; // 最低价格
}

message SimpleGameInfo {
    uint32 game_id = 1;
    string game_name = 2;
    string game_icon = 3;
    float rank = 4;
    GAME_TYPE game_type = 5;
}
message GetAllGameSimpleInfoResponse {
    repeated SimpleGameInfo game_list = 1;
}

message BatchGetGameInfoByNamesRequest {
    repeated string name_list = 1; // 游戏列表
}

message BatchGetGameInfoByNamesResponse {
    repeated SimpleGameInfo game_list = 1;
}


// 获取用户知名选手信息
message BatchGetUserRenownedInfoRequest {
    uint32 game_id = 1; // 游戏（技能）id
    repeated uint32 uid_list = 2; // 用户uid列表
}

// 用户知名选手信息
message BatchGetUserRenownedInfoResponse {
    message UserRenownedInfo {
    }
    map<uint32, UserRenownedInfo> user_renowned_info_map = 1; // 用户uid -> 用户知名选手信息,非知名选手不在map中
}

// LabelType 标识类型
enum LabelType {
    LABEL_TYPE_UNSPECIFIED = 0; // 非法
    LABEL_TYPE_COACH = 1; // 大神标识
    LABEL_TYPE_SKILL = 2; // 技能标识
    LABEL_TYPE_SPECIAL = 3; // 特色标识
    LABEL_TYPE_VOICE = 4; // 语音标识
}

// LabelInfo 标识信息
message LabelInfo {
    uint32 id = 1; // 标识ID
    LabelType label_type = 2; // 标识类型
    string label_image = 3; // 标识图片
    bool has_pricing = 4; // 是否加成价格
    uint32 pricing_amount = 5; // 加成价格，单位为T豆
    uint32 applicable_level = 6; // 标签可申请等级，如“X级及以上”
    uint32 display_order = 7; // 展示顺序，数字越小越靠前
    string label_description = 8; // 标识简介
    string label_requirements = 9; // 标识要求
    string label_name = 10; // 标识名称，仅对技能标识类型有效
    uint32 game_id = 11; // 技能 id，仅对技能标识类型有效
    string apply_entry = 12; // 申请入口(问卷链接)
    string game_name = 13;// 技能名称，仅对技能标识类型有效
}

// 创建大神或技能标识
message CreateLabelRequest {
    LabelInfo label_info = 1; // 标识信息
}

// 创建标识响应
message CreateLabelResponse {
}

// 编辑标识配置
message EditLabelRequest {
    LabelInfo label_info = 1; // 标识信息
}

// 编辑标识响应
message EditLabelResponse {
}

// 删除标识配置
message DeleteLabelRequest {
    uint32 label_id = 1; // 标识ID
}

// 删除标识响应
message DeleteLabelResponse {
}

// 查询标识列表
message ListLabelsRequest {
    uint32 page_number = 1; // 页码
    uint32 page_size = 2;
    uint32 label_id = 3; // 标识ID（精确搜索）
    LabelType label_type = 4; // 标识类型（按类型）
    uint32 game_id = 5; // 游戏ID(搜索该游戏下所有标签)
}

// 查询标识列表响应
message ListLabelsResponse {
    repeated LabelInfo label_info_list = 1; // 标识信息列表
    uint32 total_cnt = 2; // 记录总数
}

// IssueLabelRequest 发放标识请求
message IssueLabelRequest {
    uint32 label_id = 1; // 标识ID
    repeated uint32 uid = 2; // 用户 id
    int64 effective_start_time = 3; // 生效开始时间
    int64 effective_end_time = 4; // 生效结束时间
}

// IssueLabelResponse 发放标识响应
message IssueLabelResponse {
}

// RevokeLabelRequest 用于回收已发放标识
message RevokeLabelRequest {
    uint32 label_id = 1; // 标识ID
}

// RevokeLabelResponse 用于回收已发放标识
message RevokeLabelResponse {
}

enum IssuanceStatus {
    ISSUANCE_STATUS_UNSPECIFIED = 0; // 非法
    ISSUANCE_STATUS_EFFECTIVE = 1; // 生效中
    ISSUANCE_STATUS_EXPIRED = 2; // 已过期
    ISSUANCE_STATUS_PENDING = 3; // 待生效
}

// QueryIssuanceRecordsRequest 用于查询发放记录列表
message QueryIssuanceRecordsRequest {
    int32 page_number = 1;
    int32 page_size = 2;
    repeated uint32 coach_ids = 3; // 教练 id 列表
    uint32 label_id = 4; // 标识ID
    int64 effective_start_time = 5; // 生效开始时间
    int64 effective_end_time = 6; // 生效结束时间
    LabelType label_type = 7; // 标识类型
    IssuanceStatus status = 8; // 状态
}

// 发放记录的数据模型。
message IssuanceRecord {
    uint32 id = 1;
    uint32 uid = 2; // 用户的 id
    int64 start_time = 3;
    int64 end_time = 4;
    LabelInfo label_info = 5;
}

// QueryIssuanceRecordsResponse 用于查询发放记录列表
message QueryIssuanceRecordsResponse {
    repeated IssuanceRecord issuance_record_list = 1; // 发放记录列表
    uint32 total_cnt = 2; // 记录总数
}

// AddRenownedPlayerRequest 发放知名选手标识
message AddRenownedPlayerRequest {
    message RenownedPlayerInfo {
        uint32 uid = 1; // 大神的用户 id
        string skill_name = 2; // 技能名称
        int64 price = 3; // 知名选手的游戏价格
    }
    repeated RenownedPlayerInfo info_list = 1; // 知名选手列表
}

// AddRenownedPlayerResponse 发放知名选手标识
message AddRenownedPlayerResponse {
    string err_msg = 1; // 错误信息
}

// ListRenownedPlayersRequest 查询知名选手列表
message ListRenownedPlayersRequest {
    int32 page_number = 1;
    int32 page_size = 2;
    uint32 uid = 3;// 大神的用户 id
    string game_name = 4; // 技能名称
}

// RenownedPlayerInfo 知名选手信息
message RenownedPlayerInfo {
    uint32 id = 1; // 记录 id
    uint32 uid = 2; // 大神uid
    uint32 game_id = 3; // 技能id
    int64 order_price = 4; // 接单价格
    GAME_PRICING_UNIT_TYPE game_pricing_unit_type = 5; // 价格单位
    string game_name = 6; // 技能名称
    int64 update_time = 7; // 更新时间
    int64 create_time = 8; // 创建时间
}

// ListRenownedPlayersResponse 查询知名选手列表响应
message ListRenownedPlayersResponse {
    repeated RenownedPlayerInfo player_infos = 1; // 知名选手信息列表
    uint32 total_cnt = 2; // 总数
}

// BatchRemoveRenownedPlayersRequest 批量移除知名选手
message BatchRemoveRenownedPlayersRequest {
    repeated uint32 id_list = 1; // 记录 id 列表
}

// BatchRemoveRenownedPlayersResponse 批量移除知名选手响应
message BatchRemoveRenownedPlayersResponse {
    string err_msg = 1; // 错误信息
}

// UpdateRenownedPlayerRequest 更新知名选手信息
message UpdateRenownedPlayerRequest {
    uint32 id = 1; // 记录 id
    int64 order_price = 2; // 接单价格
}

// UpdateRenownedPlayerResponse 更新知名选手信息响应
message UpdateRenownedPlayerResponse {
    string err_msg = 1; // 错误信息
}

message GetBasePriceSettingRequest {
    uint32 coach_id = 1;// 教练 id
    uint32 game_id = 2;// 技能 id
}

message GetBasePriceSettingResponse {
    uint32 level = 1; // 等级
    BasePrice base_price = 2; // 基础定价
}

message SetBasePriceSettingRequest {
    uint32 coach_id = 1; // 教练 id
    uint32 game_id = 2; // 技能 id
    uint32 level = 3; // 等级
}

message SetBasePriceSettingResponse {

}

message Price {
    uint32 price = 1;// 价格
    GAME_PRICING_UNIT_TYPE game_pricing_unit_type = 2; // 价格单位
    uint32 game_id = 3;// 技能 id
    uint32 coach_id = 4;// 教练 id
}

message CalculatePriceRequest {
    uint32 game_id = 1; // 技能 id
    repeated uint32 coach_ids = 2; // 教练 id 列表
    bool with_cache = 3; // 是否使用缓存
}

message CalculatePriceResponse {
    map<uint32, Price> price_map = 1; // 价格索引，key 是教练 id
}

message CalculatePriceByGamesRequest {
    repeated uint32 game_ids = 1; // 技能 id
    uint32 coach_id = 2; // 教练 id 列表
    bool with_cache = 3; // 是否使用缓存
}

message CalculatePriceByGamesResponse {
    repeated Price prices = 1; // 价格
}

message GetCoachApplicableLabelsRequest {
    uint32 coach_id = 1;// 教练 id
    repeated uint32 game_id = 2;// 技能 id，查询指定的技能标识和大神标识(因为大神类型的标识是通用的)
}

message GetCoachApplicableLabelsResponse {
    message ApplicableLabel {
        LabelInfo info = 1; // 标识
        bool is_applied = 2; // 是否已申请
        bool is_add_to_price = 3; // 是否使用标签加成价格
    }
    repeated ApplicableLabel label_list = 1; // 标识列表
}

message SetLabelPriceSwitchRequest {
    uint32 coach_id = 1; // 教练 id
    uint32 label_id = 2; // 标识ID
    bool target_state = 3; // 目标开关状态
}

message SetLabelPriceSwitchResponse {
}

message CheckCoachIdentityAndSkillRequest {
    message CheckItem {
        uint32 coach_id = 1;// 教练 id
        string game_name = 2;// 技能名称
    }
    repeated CheckItem check_list = 1; // 检查列表
}

// 检查不同过则报错
message CheckCoachIdentityAndSkillResponse {
    string err_msg = 1; // 错误信息
}

message BatchGetCoachLabelsForGameRequest {
    uint32 game_id = 1;// 技能 id
    repeated uint32 coach_ids = 2;// 教练 id 列表
}

message BatchGetCoachLabelsForGameResponse {
    message LabelList {
        repeated LabelInfo label_list = 1;// 标识列表
    }
    message CoachLabelInfo {
        map<uint32, LabelList> label_map = 1;// 标识map，按照类型区分
        bool is_renowned = 2;// 是否是知名选手
        uint32 coach_id = 3;// 教练 id
    }
    repeated CoachLabelInfo label_list = 1; // 标识列表
}

message BatchCheckCoachHasGameRequest {
    repeated uint32 coach_ids = 1;// 教练 id 列表
    uint32 game_id = 2;// 技能 id
}

message BatchCheckCoachHasGameResponse {
    map<uint32, bool> coach_has_game_map = 1;// 教练是否拥有技能
}

/********************** 游戏名片配置 ************************/

// 游戏名片选项
message GameCardInfoItem {
    string item_name = 1; // 选项名称
    repeated string item_list = 2; // 选择项（不填则为用户输入）
    string tips = 3; // 提示语
}

message GameCardConfig {
    uint32 game_id = 1; // 游戏id
    string game_name = 2; // 游戏名称
    repeated GameCardInfoItem game_card_info_item_list = 3;  // 游戏卡片信息项
}

message GetAllGameCardConfigRequest {}

message GetAllGameCardConfigResponse {
    repeated GameCardConfig game_card_config_list = 1; // 游戏名片配置列表
}


/********************** 游戏名片配置 ************************/


// 设置游戏包赢开关
message SetGameGuaranteeStatusRequest {
    uint32 uid = 1; // 用户uid
    uint32 game_id = 2; // 游戏id
    bool is_guarantee_win = 3; // 是否包赢
}


message SetGameGuaranteeStatusResponse {
}


message HandleGameUpdateRequest {
    uint32 game_id = 1;
}

message HandleGameUpdateResponse {
}

enum FreezeType {
    FREEZE_TYPE_UNFREEZE = 0; // 未冻结
    FREEZE_TYPE_FOREVER = 1; // 永久冻结
    FREEZE_TYPE_TO_TIME = 2; // 冻结至指定时间
}

message FreezeCoachSkillRequest {
    uint32 uid = 1;
    repeated uint32 game_id_list = 2;
    FreezeType freeze_type = 3;
    int64 freeze_stop_ts = 4;
    string freeze_reason = 5;
    string op_user = 6;
}

message FreezeCoachSkillResponse {
}

message UnfreezeCoachSkillRequest {
    uint32 uid = 1;
    repeated uint32 game_id_list = 2;
    string op_user = 3;
}

message UnfreezeCoachSkillResponse {
}

message GetSkillFreezeOperationListRequest {
    int32 page = 1;
    int32 page_size = 2;
    repeated uint32 uid_list = 3;
    uint32 guild_id = 4;
    uint32 game_id = 5;
}

message GetSkillFreezeOperationListResponse {
    repeated SkillFreezeOperation operation_list = 1;
    int32 total = 2;
}

message SkillFreezeOperation {
    uint32 uid = 1;
    uint32 guild_id = 2;
    repeated uint32 game_id_list = 3;
    FreezeType freeze_type = 4;
    int64 freeze_stop_ts = 5;
    string freeze_reason = 6;
    string op_user = 7;
    int64 op_ts = 8;
}

message DebugCheckGuaranteeWinPermissionRequest {}
message DebugCheckGuaranteeWinPermissionResponse {}

// 特色标签类型
enum SpecialLabelType {
    SPECIAL_LABEL_TYPE_UNSPECIFIED = 0; // 非法
    SPECIAL_LABEL_TYPE_VOICE = 1; // 语音标识
    SPECIAL_LABEL_TYPE_SPECIAL = 2; // 特色标识
}

// 特色标签列表接口
message GetSpecialLabelListRequest {
    SpecialLabelType label_type = 1; // 标签类型
    uint32 game_id = 2; // 游戏ID,标签类型为特色时必填
}

message SpecialLabel {
    uint32 id = 1; // 标签ID
    string label_name = 2; // 标签名称
}

// 特色标签列表响应
message GetSpecialLabelListResponse {
    repeated SpecialLabel label_list = 1; // 标签列表
}

// 用户特色标签信息 web运营后台请求
message BatchGetUserSpecialLabelRequest {
    repeated uint32 uid_list = 1; // 用户uid列表
}

// 用户特色标签信息 web运营后台请求
message BatchGetUserSpecialLabelResponse {
    message UserGameSpecialLabelList {
        repeated UserGameSpecialLabelItem game_list = 1; // 标签ID列表
    }
    map<uint32, UserGameSpecialLabelList> user_special_label_map = 1; // 用户特色标签信息列表
}

// 用户特色标签信息 app请求
message BatchGetUserGameSpecialLabelRequest {
    repeated uint32 uid_list = 1; // 用户uid列表
    uint32 game_id = 2; // 游戏ID
}

// 用户特色标签信息 app请求
message BatchGetUserGameSpecialLabelResponse {
    message SimpleSpecialLabelInfo {
        string label_name = 1; // 标签名称
        string label_image = 2; // 标签图片
        uint32 display_order = 3; // 展示顺序
    }
    message UserGameSpecialLabelList {
        repeated SimpleSpecialLabelInfo label_list = 1; // 特色标签列表
        string voice_label = 2; // 语音标签
    }
    map<uint32, UserGameSpecialLabelList> user_special_label_map = 1; // 用户特色标签信息列表
}


message UserSpecialLabelItem {
    uint32 label_id = 1; // 标签ID
    SpecialLabelType label_type = 2; // 标签类型
    string label_name = 3; // 标签名称
    string label_image = 4; // 标签图片
    string pricing_amount = 5; // 加成价格
}

// 同一个游戏下的多个 标签
message UserGameSpecialLabelItem {
    uint32 game_id = 1; // 游戏ID
    repeated UserSpecialLabelItem label_list = 2; // 标签ID列表
}

// 更新用户特色标签信息
message UpdateUserSpecialLabelRequest {
    uint32 uid = 1; // 用户uid
    repeated UserGameSpecialLabelItem game_list = 2; // 标签ID列表,多个游戏
}

// 更新用户特色标签信息
message UpdateUserSpecialLabelResponse {
}

// 校验优先级是否冲突
message CheckLabelOrderRequest {
    enum CheckType {
        CHECK_TYPE_UNSPECIFIED = 0; // 未知
        CHECK_TYPE_ADD = 1; // 新增
        CHECK_TYPE_EDIT = 2; // 编辑
    }
    LabelType label_type = 1; // 标签类型
    uint32 display_order = 2; // 优先级
    CheckType check_type = 3; // 校验类型
    uint32 label_id = 4; // 标签ID,编辑时必填
}

// 校验优先级是否冲突,失败时接口报错
message CheckLabelOrderResponse {
}

message GetLabelIssuanceRecordRequest {
    int32 page_num = 1;
    int32 page_size = 2;
}

message SimpleIssuanceRecord {
    uint32 coach_uid = 1; // 教练uid
    uint32 label_id = 2; // 标签ID
    uint32 game_id = 3; // 游戏ID
}

message GetLabelIssuanceRecordResponse {
    repeated SimpleIssuanceRecord record_list = 1; // 发放记录列表
}

message GetSpecialLabelIssuanceRecordRequest {
    int32 page_num = 1;
    int32 page_size = 2;
}

message GetSpecialLabelIssuanceRecordResponse {
    repeated SimpleIssuanceRecord record_list = 1; // 发放记录列表
}
