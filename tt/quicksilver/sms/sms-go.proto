syntax = "proto3";

option go_package = "golang.52tt.com/protocol/services/sms-go";

// namespace
// buf:lint:ignore DIRECTORY_SAME_PACKAGE
package sms_go;

// 专用错误码从-20开始
// buf:lint:ignore ENUM_PASCAL_CASE
enum ERR_SMS
{
  ERR_SMS_OK = 0;    // 成功
  ERR_SMS_VERFIYCODE_VALIDATE_FAIL = -20;    // 验证码验证失败
  ERR_SMS_TOO_MANY_PHONE = -21;    // 同时群发手机号码不能超过100个
  ERR_SMS_SEND_SMS_FREQ = -22;    // 30s内不能对同一个手机号码调用sendsms
  ERR_SMS_TYPE_INVALID = -23;         // 无效的sms类型
  ERR_SMS_NO_ENOUGH_PARAMS = -24;     // 参数数量不够
  ERR_SMS_SEND_SMS_THRESHHOLD_EXCEED = -25;        //向同一用户发送消息当日超限
  ERR_SMS_INVALID_PHONE = -26;
  ERR_SMS_INVALID_VERIFYCODE = -27;
  ERR_SMS_NOT_SUPPORT_INN = -28; //不支持国际号码
  ERR_SMS_REDIS_ERROR = -29;          // 缓存操作异常
  ERR_SMS_PARSE_FROM_PB_ERROR = -30;          // 反序列化失败

  ERR_VALID_APK_URL_CANNOT_REACH = -31;  // URL访问不到
  ERR_VALID_APK_URL_NOT_APK = -32;    // 非apkurl
  ERR_SMS_INVALID_SENDER = -33;    // 无效的合作商
}

//单发短信
message SendSmsReq {
  string phone = 1;
  uint32 sms_type = 2;    //短信模板，
  repeated string param_list = 3;    //短信模板参数
  bool without_cooldown = 4;  //(每日)周期发送频率限制
  string verify_code_key = 5;    //短信验证码key
  string verify_code_usage = 6;  //短信验证码用途
  uint32 market_id = 7;
  //	uint32 retry_times = 4;	//当前的重试次数(start with 0)
  uint32 biz_id = 8;
  uint32 ext_biz_id = 9;
  string request_id = 10;
  uint64 create_time = 11;
}

message SendSmsResp {
  // nothing
}

// 用户信息
message UserInfo {
  string phone = 1;         // 手机号
  uint32 uid = 2;           // uid
  string sign = 3;          // 签名
  repeated string param_list = 4; // 参数列表
}

// 通用短信请求，必须指定模板的usage(不清楚时找短信负责人确认）内容，可发行业短信及营销短信
message SendCommonSmsReq {
  repeated UserInfo user_info_list = 1; //用户信息列表
  string usage = 2;               //短信模板，
  bool without_cooldown = 3;    //(每日)周期发送频率限制
}

message ErrorPhone {
  string phone = 1;
  int32 code = 2;
}

// 通用短信应答
message SendCommonSmsResp {
  int32 result = 1; // 0成功，其余失败
  repeated ErrorPhone error_phones = 2; // 为空表示全部提交成功
}

message SendRandomVerifyCodeReq {
  string phone = 1;                 // 用户信息
  uint32 expire_ttl = 2;            // 验证码过期时间，秒
  uint32 length = 3;                // 验证码长度，只允许4-6位
  string usage = 4;                 // 短信模板，
  repeated string param_lists = 5;  // 参数列表
  uint32 uid = 6;                   // uid
}

message SendRandomVerifyCodeResp {
  int32 result = 1;       // 0成功，其余失败
  string verify_code = 2; // 中台生成的随机验证码
  string sid = 3;         // 中台短信sid，仅用于定位问题
}

message ValidateVerifyCodeReq {
  string phone = 1;         // 用户信息
  string usage = 2;         // 短信模板，
  string verify_code = 3;   // 验证码
}

message ValidateVerifyCodeResp {
  int32 result = 1; // 0成功，其余失败
}

message DirectSendSmsReq {
  string phone = 1;
  string text = 2;
  uint32 biz_id = 3;
  uint32 ext_biz_id = 4;
}

message DirectSendSmsResp {
  // nothing
}

// 群发短信, 所有人收到短信都一样
//message SendGroupSmsReq {
//	uint32 sms_type = 1;	// 短信模版
//	repeated string param_list = 2;  //短信模板参数
//	repeated string phone_list = 3;  // 批量手机号
//}


////////////////////////////////////////////////
// 验证码相关逻辑， 暂时放在此server， 以后独立
////////////////////////////////////////////////
// 为uid生成验证码
message CreateVerifyCodeReq {
  string key = 1;
  uint32 code_len = 2;   //请求的验证码长度
}

message CreateVerifyCodeResp {
  string key = 1;
  string verify_code = 2;  // 验证码
  uint32 expire_time = 3;  // 过期时间
}

/////////////////////////////////////////////////////////

message CheckUrlValidApkUrlReq {
  string url = 1;
}

message CheckUrlValidApkUrlResp {
  uint32 content_length = 1;
}

message DownLoadUrlReq{
  string url = 1;
  uint32 timeout = 2;
}

message DownLoadUrlResp{
  string msg = 1;
}

message DownLoadUrlByteReq{
  string url = 1;
  uint32 timeout = 2;
}

message DownLoadUrlByteResp{
  bytes msg = 1;
}

message Foo{
}

// post url 接口
message PostUrlDataReq{
  string url = 1;
  repeated bytes head_info_list = 2;
  bytes data_info = 3;
}

message PostUrlDataResp{
  bytes resp_msg = 1;
}

message SendVoiceVerifyCodeReq {
  string phone = 1;
  string verify_code = 2;
  uint32 uid = 3;
  string nation_code = 5;
  uint32 voice_type = 6;
  repeated string param_list = 7;
  uint32 biz_id = 8;
  uint32 ext_biz_id = 9;
}

message SendVoiceVerifyCodeResp {
}

message SendSmsWithProviderReq{
  string provider = 1;
  repeated string phones = 2;
  uint32 sms_type = 3;    //短信模板，
  repeated string param_list = 4;    //短信模板参数
  uint32 market_id = 5;
}
message SendSmsWithProviderResp {
}

message RecordVerifyCodePassReq {
  string verify_code_key = 1;
  uint32 verify_at = 2;
}

message RecordVerifyCodePassResp {
}

// 多个phone 对应一个message 或者
// 多个phone 对应 同样数量的message，对应关系发送
message SendMarketingSmsReq {
  repeated string phones = 1; // 一次最多发送100个手机号码
  repeated string messages = 2;
  uint32 biz_id = 3;
  uint32 ext_biz_id = 4;
  string app_id = 5;
}

message SendMarketingPhoneErrResult {
  string phone = 1;
  int32 code = 2;
}

message SendMarketingSmsResp {
  int32 req_result = 1; // 处理是否成功( == 0)
  repeated SendMarketingPhoneErrResult err_phones = 2; // 处理成功情况下，有问题的号码（号码有问题，达到频率限制等等）
}


message AuthSmsReq {
  string app_id = 1;
  string app_secret = 2;
  string client_ip = 3;
}

message AuthSmsResp {
  enum Result {
    SUCCESS = 0x00;
    INVALID_APP_INFO = 0x01;
    INVALID_CLIENT_IP = 0x02;
  }
  int32 result = 1;
  uint32 biz_id = 2;
}

message GetPhoneSendCountReq {
  string phone = 1;
}

message GetPhoneSendCountResp {
  int64 count = 1;
}

message ClearPhoneSendCountReq {
  string phone = 1;
}

message ClearPhoneSendCountResp {
  // nothing
}

service Sms {
  rpc SendCommonSms(SendCommonSmsReq) returns(SendCommonSmsResp) {}
  rpc SendRandomVerifyCode(SendRandomVerifyCodeReq) returns(SendRandomVerifyCodeResp) {}
  rpc ValidateVerifyCode(ValidateVerifyCodeReq) returns(ValidateVerifyCodeResp) {}

  rpc SendSms(SendSmsReq) returns(SendSmsResp) {}

  // 这个接口测试用, 业务不要调用该接口
  rpc SendSmsWithProvider(SendSmsWithProviderReq) returns (SendSmsWithProviderResp) {}

  rpc CheckUrlValidApkUrl(CheckUrlValidApkUrlReq) returns(CheckUrlValidApkUrlResp) {}

  rpc DownLoadUrl(DownLoadUrlReq) returns(DownLoadUrlResp) {}

  rpc DirectSendSms(DirectSendSmsReq) returns(DirectSendSmsResp) {}

  rpc PostUrlData(PostUrlDataReq) returns(PostUrlDataResp) {}

  rpc DownLoadUrlByte(DownLoadUrlByteReq) returns(DownLoadUrlByteResp) {}

  rpc SendVoiceVerifyCode(SendVoiceVerifyCodeReq) returns (SendVoiceVerifyCodeResp) {}

  rpc RecordVerifyCodePass(RecordVerifyCodePassReq) returns (RecordVerifyCodePassResp) {}

  rpc SendMarketingSms(SendMarketingSmsReq) returns (SendMarketingSmsResp) {}

  rpc AuthSms(AuthSmsReq) returns (AuthSmsResp) {}

  rpc GetPhoneSendCount(GetPhoneSendCountReq) returns (GetPhoneSendCountResp) {}

  rpc ClearPhoneSendCount(ClearPhoneSendCountReq) returns (ClearPhoneSendCountResp) {}
}

