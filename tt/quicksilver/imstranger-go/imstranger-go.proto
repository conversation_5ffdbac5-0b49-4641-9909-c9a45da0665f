syntax = "proto3";

option go_package = "golang.52tt.com/protocol/services/imstrangergo";
package imstrangergo;


service ImstrangerGo {

    // 是否是陌生人聊天的发起者
    rpc IsStrangerMsgSender (IsStrangerMsgSenderReq) returns (IsStrangerMsgSenderResp) {
    }
    // 是陌生人聊天的接收者，需要记录回复记录
    rpc CheckStrangerMsgTargeter (CheckStrangerMsgTargeterReq) returns (CheckStrangerMsgTargeterResp) {
    }
    rpc CheckStrangerMsgLimit (CheckStrangerMsgLimitReq) returns (CheckStrangerMsgLimitResp) {
    }
    rpc IncrStrangerMsgLimit (IncrStrangerMsgLimitReq) returns (IncrStrangerMsgLimitResp) {
    }
    rpc SetHighConsumeIn24Hour (SetHighConsumeIn24HourReq) returns (SetHighConsumeIn24HourResp) {
    }
    rpc IsHighConsumeIn24Hour (IsHighConsumeIn24HourReq) returns (IsHighConsumeIn24HourResp) {
    }
    rpc ChatAward (ChatAwardReq) returns (ChatAwardResp) {
    }
    rpc GetStrangerGreetDetail (GetStrangerGreetDetailReq) returns (GetStrangerGreetDetailResp) {
    }
    // 检查用户有没有到被撩上限 批量
    rpc BatchCheckUserRecvLimit (BatchCheckUserRecvLimitReq) returns (BatchCheckUserRecvLimitResp) {
    }
    // 设置当天IM送礼记录
    rpc SetIMPresentToTarget (SetIMPresentToTargetReq) returns (SetIMPresentToTargetResp) {
    }
    rpc CheckIMPresentToTarget (CheckIMPresentToTargetReq) returns (CheckIMPresentToTargetResp) {
    }
    /*判断用户会员状态*/
    rpc CheckSuperPlayerStatus (CheckSuperPlayerStatusReq) returns (CheckSuperPlayerStatusResp) {
    }

    /* 检查贵族特权限制 */
    rpc CheckNobilityPrivilege (CheckNobilityPrivilegeReq) returns (CheckNobilityPrivilegeResp) {
    }
    rpc BatchCheckNobilityPrivilege (BatchCheckNobilityPrivilegeReq) returns (BatchCheckNobilityPrivilegeResp) {
    }

    /* 批量获取被撩上限*/
    rpc BatchGetStrangerRecvMsgLimit (BatchGetStrangerRecvMsgLimitReq) returns (BatchGetStrangerRecvMsgLimitResp) {
    }

    // 是否签约公会的上下属关系
    rpc IsContractGuildSuperiorAndSubordinate(IsContractGuildSuperiorAndSubordinateReq) returns (IsContractGuildSuperiorAndSubordinateResp) {}

}

message BatchGetStrangerRecvMsgLimitReq {
    repeated uint32 target_uids = 1;
    string scene = 2;//场景 Hey_targets 为陌生人1v1聊天 friend_targets为好友聊天 group_targets组聊天  guild_targets公会聊天
}

message BatchGetStrangerRecvMsgLimitResp {
    // buf:lint:ignore MESSAGE_PASCAL_CASE
    message limitInfo {
        uint32 target_uid = 1;
        uint32 cnt = 2  ;//次数 没有的话 是0
    }
    repeated limitInfo info = 1;
}


message IsStrangerMsgSenderReq {
    uint32 sender = 1;
    uint32 target = 2;
}

message IsStrangerMsgSenderResp {
    bool result = 1;
}

message CheckStrangerMsgTargeterReq {
    uint32 sender = 1;
    uint32 target = 2;
}

message CheckStrangerMsgTargeterResp {
    bool result = 1;
}

message CheckStrangerMsgLimitReq {
    uint32 uid = 1;
    uint32 target_uid = 2;
    string ip = 3;
    string check_man_cnt_key = 4;
    uint32 msg_source_type = 5;
}

message CheckStrangerMsgLimitResp {
    int32 result = 1;
    bool is_new_stranger_target_user = 2;
    bool is_new_stranger_ip = 3;
    string result_msg = 4;
}

message IncrStrangerMsgLimitReq {
    uint32 uid = 1;
    uint32 target_uid = 2;
    string ip = 3;
    string check_man_cnt_key = 4;
    bool is_new_stranger_target_user = 5;
    bool is_new_stranger_ip = 6;
    uint32 msg_source_type = 7;
    bool is_only_reply = 8;
}
message SetHighConsumeIn24HourReq{
    uint32 uid = 1;
}
message SetHighConsumeIn24HourResp{
}

message IsHighConsumeIn24HourReq{
    uint32 uid = 1;
}
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message IsHighConsumeIn24HourResp{
    bool isHighConsume = 1;
}

message IncrStrangerMsgLimitResp {
    uint32 result = 1;
}

/*聊天奖励 每日发起的前5个会话*/
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message ChatAwardReq{
    uint32 uin = 1;
    uint32 targetUid = 2;
}
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message ChatAwardResp{
    bool awardUin = 1;  /*true 符合条件 发奖*/
    bool awardTarget = 2;  /*true 符合条件 发奖*/
}

// 用户向陌生人聊天 targetuid的集合查询（当天）
message GetStrangerGreetDetailReq{
    uint32 uid = 1;
}
message GetStrangerGreetDetailResp{
    repeated uint32 target_uids = 1;
}

// 检查用户有没有到被撩上限 批量
message BatchCheckUserRecvLimitReq{
    uint32 uid = 1;
    repeated uint32 uid_list = 2;
}
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message LimitInfo{
    uint32 uid = 1;
    bool isLimit = 2;
}
message BatchCheckUserRecvLimitResp{
    repeated LimitInfo result_list = 1;
}

// 设置当天IM送礼记录
message SetIMPresentToTargetReq{
    uint32 uid = 1;
    uint32 target_uid = 2;
}
message SetIMPresentToTargetResp{
}
message CheckIMPresentToTargetReq{
    uint32 uid = 1;
    uint32 target_uid = 2;
}
message CheckIMPresentToTargetResp{
    bool has_record = 1;
}

message CheckSuperPlayerStatusReq{
    uint32 uid = 1;
}
message CheckSuperPlayerStatusResp{
    bool not_expired = 1;
}

/* 检查贵族特权限制 */
message CheckNobilityPrivilegeReq{
    uint32 uid = 1;
    uint32 target_uid = 2; /* 查看target uid 是否是贵族，是否设置特权 */
}
message CheckNobilityPrivilegeResp{
    bool is_limit = 1;
}

message BatchCheckNobilityPrivilegeReq {
    uint32 uid = 1;
    repeated uint32 target_uid_list = 2; /* 查看target uid 是否是贵族，是否设置特权 */
}

message NobilityPrivilegeCheckInfo {
    uint32 uid = 1;
    uint32 target_uid = 2;
    bool is_limit = 3;
}

message BatchCheckNobilityPrivilegeResp {
    repeated NobilityPrivilegeCheckInfo check_info_list = 1;
}

message IsContractGuildSuperiorAndSubordinateReq {
    uint64 uid = 1;
    uint64 target = 2;
}

message IsContractGuildSuperiorAndSubordinateResp {
    bool is = 1;
}

