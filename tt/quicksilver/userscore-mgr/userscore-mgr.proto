syntax = "proto3";

package userscore_mgr;

import "tt/quicksilver/extension/options/options.proto";

option go_package = "golang.52tt.com/protocol/services/userscore-mgr";

service UserScoreMgr {
  option (service.options.old_package_name) = "UserScoreMgr.UserScoreMgr";

  //通过uid查询用户积分
  rpc GetUserScoreByUidList(UidListReq) returns (UserScoreList) {}
  //创建导出所有用户积分任务
  rpc CreateExportAllUserScoreTask(TimeRangeReq) returns (EmptyMsg) {}
  //查看导出任务列表
  rpc GetExportAllUserScoreTaskList(OffsetReq) returns (ExportAllUserScoreTaskDataList) {}
  //获取临时下载地址
  rpc GetTempZipUrl(IdReq) returns (TempZipUrlResp) {}
  //收礼明细
  rpc GetUserPresentByUidList(UidReq) returns (UserPresentList) {}

  //礼物积分库存
  rpc GetInventory(GetInventoryReq) returns (GetInventoryResp) {}

  //跑礼物积分库存快照
  rpc GenerateInventory(GenerateInventoryReq) returns (EmptyMsg) {}

  //跑时间段里的累计积分
  rpc RunHistoryScoreAmount(RunHistoryScoreAmountReq) returns (EmptyMsg) {}

  //获取某个uid的累计积分
  rpc GetScoreAmount(UidReq) returns (GetScoreAmountResp) {}
}

message RunHistoryScoreAmountReq {
  int64 begin_time = 1;
  int64 end_time = 2;
  bool replace = 3; //是否替换
  bool async = 4; //异步运行
}

message GetScoreAmountResp {
  uint64 amount = 1;
}


message GenerateInventoryReq {
  int64 inventory_time = 1;
}

message GetInventoryReq {
  uint32 year_month = 1;  //例如：202303
}


//期末计算价值 = begin_shortcut + increase - decrease
//差异价值 = 期末计算价值 - end_shortcut
message GetInventoryResp {
  uint64 begin_shortcut = 1;  //期初快照价值
  uint64 increase = 2;        //本期新增价值
  uint64 decrease = 3;        //本期消耗价值
  uint64 end_shortcut = 4;    //期末快照价值
}

message IdReq {
  uint32 id = 1;
}

message TempZipUrlResp {
  string temp_zip_url = 1;
}

message ExportAllUserScoreTaskDataList {
  repeated ExportAllUserScoreTaskData list = 1;
  uint32 total = 2;
}

message ExportAllUserScoreTaskData {
  uint32 id = 1;
  uint32 begin_time = 2;
  uint32 end_time = 3;
  uint32 type = 4;  //0全部，1提现积分或已汇总积分大于0
  uint32 percent = 5; //运行百分比
  uint32 create_time = 6;
  string err_data = 7;  //错误信息
}

message OffsetReq {
  uint32 offset = 1;
  uint32 limit = 2;
}

message EmptyMsg {
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message TimeRangeReq {
  uint32 beginTime = 1;
  uint32 endTime = 2;
  uint32 type = 3;  //0全部，1提现积分或已汇总积分大于0
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message UidListReq {
  repeated uint32 uid_list = 1;
  uint32 beginTime = 2;
  uint32 endTime = 3;
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message UidReq {
  uint32 uid = 1;
  uint32 beginTime = 2;
  uint32 endTime = 3;
  uint32 offset = 4;
  uint32 limit = 5;
}

message UserScoreSimpleData {
  uint32 uid = 1;
  int64 last_remain_score = 2; //上月剩余积分，截止到本月1号00：00分用户剩余的个人积分
  int64 sum_score = 3; //1类礼物积分
  int64 settlement_score = 4;  //已提现积分，用户本统计周期内已申请提现的积分
  int64 guild_exchange_score = 5;  //已汇总对公积分
  int64 failed_roll_score = 6; //提现失败返还积分
  int64 exchange_tbean_score = 7;  //已兑换积分1类，用户本统计周期内已兑换成T豆的积分
  int64 official_reward_score = 8; //官方发放积分
  int64 official_recycle_score = 9;  //官方回收积分
  int64 werewolf_score = 10;  //狼人杀积分
  int64 last_remain_tbean_only_score = 11;  //上月剩余只可兑换积分
  int64 exchange_tbean_score_2 = 12; //已兑换积分2类
  int64 remain_score = 13;  //剩余1类
  int64 remain_tbean_2 = 14; //剩余2类
  int64 sum_score_2 = 15; //可兑换礼物积分，2类积分
}

message UserScoreData {
  UserScoreSimpleData simple_data = 1;
  string ttid = 2;
  string nickname = 3;
  uint32 freeze_status = 4; //冻结状态，0正常，1全部，2部分
}

message UserScoreList {
  repeated UserScoreData list = 1;
}


message UserPresentData {
  uint32 to_uid = 1;
  string to_ttid = 2;
  string to_nickname = 3;
  uint32 create_time = 4; //收礼时间
  int32 change_score = 5; //本次产生积分
  uint32 finally_score = 6; //总积分，截止收礼时间 该账号的总积分 ，已有积分+本次产生积分
  string from_ttid = 7; //送礼人TT ID
  string from_nickname = 8; //送礼人昵称
  uint32 display_id = 9;  //房间 display ID
  string channel_name = 10; //房间名
  uint32 item_id = 11;  //礼物ID
  string item_name = 12;  //礼物名
  uint32 item_price = 13; //礼物单价
  uint32 item_count = 14; //收礼数量
  string channel_view_id = 15; //房间 view ID
  uint32 score_type = 16; //积分类型，0不可提现，1可提现
}

message UserPresentList {
  repeated UserPresentData list = 1;
  uint32 total = 2;
}