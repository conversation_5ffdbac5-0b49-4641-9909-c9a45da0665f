syntax = "proto3";

package glory_magic;
option go_package = "golang.52tt.com/protocol/services/glory-magic";

service GloryMagic {
  // -------------------- 运营后台协议 begin ----------------------------------
  //增加或删除库存配置
  rpc UpdateBaseStock(UpdateBaseStockReq) returns(UpdateBaseStockResp){}
  //获取生效中的库存配置
  rpc GetBaseStock(GetBaseStockReq) returns(GetBaseStockResp){}
  //获取当前小时的库存配置
  rpc GetCurrentStock(GetCurrentStockReq) returns(GetCurrentStockResp){}
  //增加或删除手动库存配置
  rpc UpdateManualStock(UpdateManualStockReq) returns(UpdateManualStockResp){}
  //获取手动库存配置
  rpc GetManualStock(GetManualStockReq) returns(GetManualStockResp){}
  //更新概率配置
  rpc UpdateChanceStock(UpdateChanceStockReq) returns(UpdateChanceStockResp){}
  //获取概率配置
  rpc GetChanceStock(GetChanceStockReq) returns(GetChanceStockResp){}

  // -------------------- 运营后台协议 end ----------------------------------

  // -------------------- 活动页协议 begin ----------------------------------
  // 检查入口
  rpc CheckEntry(CheckEntryReq) returns(CheckEntryResp) {}
  // 获取抽奖前置信息
  rpc GetPreInfo(GetPreInfoReq) returns(GetPreInfoResp) {}
  // 获取奖池预览
  rpc GetAwardPreview(GetAwardPreviewReq) returns(GetAwardPreviewResp) {}
  // 获取中奖轮播
  rpc GetWinningCarousel(GetWinningCarouselReq) returns(GetWinningCarouselResp) {}
  // 抽奖接口
  rpc Lottery(LotteryReq) returns(LotteryResp) {}
  // 获取抽奖记录类型
  rpc GetMyLotteryRecordType(GetMyLotteryRecordTypeReq) returns(GetMyLotteryRecordTypeResp) {}
  // 获取抽奖记录
  rpc GetMyLotteryRecord(GetLotteryRecordReq) returns(GetMyLotteryRecordResp) {}
  // 增加用户消费值
  rpc AddUserConsumeVal(AddUserConsumeValReq) returns (AddUserConsumeValResp) {}
  // 测试
  rpc Test(TestReq) returns(TestResp) {}
  // -------------------- 活动页协议 end ----------------------------------

}


enum LotteryItemType {
  LOTTERY_ITEM_UNSPECIFIED = 0;         // 无效值
  LOTTERY_ITEM_PACKAGE = 1;             // 包裹礼物
  LOTTERY_ITEM_HORSE = 2;               // 坐骑
  LOTTERY_ITEM_MIC_STYLE = 3;           // 麦位框
  Lottery_ITEM_WEALTH_CARD = 4;         // 财富值卡
  LOTTERY_ITEM_INFORMATION_CARD = 5;    // 资料卡
  LOTTERY_ITEM_DYNAMIC_ICON = 6;        // 动态头像
  LOTTERY_ITEM_MIC_EMOJI = 7;           // 互动麦位表情
  LOTTERY_ITEM_DECORATION = 8;          // 主页飘
  LOTTERY_ITEM_DRESSUP_DEBRIS = 9;      // 装扮碎片
  LOTTERY_ITEM_NONE = 10;               // 无奖励
}


//库存类型
enum StockType {
  STOCK_TYPE_UNSPECIFIED = 0;         // 无效值
  STOCK_TYPE_LIMITED = 1;             // 有限制库存
  STOCK_TYPE_INFINITE = 2;            // 无限制库存
}

//刷新类型
enum RefreshType {
  REFRESH_TYPE_UNSPECIFIED = 0;         // 无效值
  REFRESH_TYPE_DAILY = 1;             // 每日刷新
  REFRESH_TYPE_HOURLY = 2;            // 每小时刷新
  REFRESH_TYPE_WEEKLY = 3;            // 每周刷新
  REFRESH_TYPE_MONTHLY = 4;            // 每月刷新
}

enum ResetType {
  RESET_TYPE_UNSPECIFIED = 0; // 无效值
  RESET_TYPE_DAY = 1;         // 每日重置
  RESET_TYPE_MONTH = 2;        // 每月重置
}


//库存等级类型
enum StockRankType {
  STOCK_RANK_TYPE_UNSPECIFIED = 0;         // 无效值
  STOCK_RANK_TYPE_COMMON = 1;             // 普通库存
  STOCK_RANK_TYPE_SENIOR = 2;            // 高级库存
}

//奖池配置
message StockConf{
  uint32 id = 1;           //库存id
  uint32 item_type = 2;    //物品类型 see LotteryItemType
  string item_name = 3;    //物品名称
  string item_id = 4;      //物品id
  uint32 stock_price = 5;  //奖池价值 
  uint32 base_count = 6;   //初始库存数量
  uint32 stock_type = 7;   //库存类型 see StockType
  uint32 refresh_type = 8; //刷新类型 see RefreshType
  uint32 reset_type = 9;   //库存充值类型 see ResetType
  uint32 rank_type = 10;   //库存等级类型 see StockRankType
  uint32 incr_count = 11;  //单次增加库存数量
}

message UpdateBaseStockReq{
  uint32 rank_type = 1;
  repeated StockConf list = 2;  //根据 rank_type 更新普通奖池或者高级奖池
}

message UpdateBaseStockResp{
}

message GetBaseStockReq{
  uint32 rank_type = 1;  //库存等级类型 see StockRankType
}

message GetBaseStockResp{
  uint32 rank_type = 1;
  repeated StockConf list = 2;  //奖池
}


message GetCurrentStockReq{
  uint32 rank_type = 1;
}

message GetCurrentStockResp{
  uint32 rank_type = 1;
  repeated StockConf list = 2;
  uint32 last_update_time = 3; // 当前库存刷新时间,秒级时间戳
}

message UpdateManualStockReq{
  uint32 rank_type = 1;
  repeated StockConf list = 2;
  uint32 effect_start = 3; // 生效时间,秒级时间戳
  uint32 effect_end = 4; // 生效时间,秒级时间戳
}

message UpdateManualStockResp{
}


message GetManualStockReq{
  uint32 rank_type = 1;
}

message ManualStockConf{
  repeated StockConf list = 1;
  uint32 effect_start = 2; // 生效时间,秒级时间戳
  uint32 effect_end = 3; // 生效时间,秒级时间戳
}

message GetManualStockResp{
  uint32 rank_type = 1;
  ManualStockConf current_stock = 2;  //当前库存
  ManualStockConf future_stock = 3;   //未生效库存
}

//概率配置
message ChanceConf{
  uint32 id = 1;           //概率配置id 自增 
  uint32 item_type = 2;    //物品类型 see LotteryItemType
  string item_name = 3;    //物品名称
  string item_id = 4;      //物品id
  uint32 stock_price = 5;  //奖池价值 
  uint32 day_count = 6;   //天数
  uint32 weight = 7;     //权重
  uint32 stock_id = 8;   //对应 StockConf 的id
  uint32 rank_type = 9;  // See StockRankType
}



message GetChanceStockReq{
  uint32 rank_type = 1;
}

message AllChanceStock{
  ChanceConf top_conf = 1;                    //大奖概率
  ChanceConf guarantee_conf = 2;              //出光保底概率
  repeated ChanceConf unstable_list = 3;      //浮动概率
  uint32 effect_time = 4;                     //生效时间,秒级时间戳
}

message GetChanceStockResp{
  uint32 rank_type = 1;
  AllChanceStock current_stock = 2; //当前库存
  AllChanceStock future_stock = 3;  //未生效库存
}

message UpdateChanceStockReq{
  uint32 rank_type = 1;
  AllChanceStock stock = 2;
}

message UpdateChanceStockResp{
  uint32 rank_type = 1;
}



// ================================== 活动页 =======================================
enum PondType {
  POND_TYPE_NONE = 0; // 无
  POND_TYPE_NORMAL = 1; // 普通池
  POND_TYPE_GRAND = 2; // 高级池
}


message CheckEntryReq {}

message CheckEntryResp {
  bool common_is_open = 1; // 普通奖池是否开启
  bool grand_is_open = 2; // 高级奖池是否开启
}

message GetPreInfoReq {
  uint32 pond_type = 1; // 奖池类型 see PondType
  uint32 uid = 2;
}

message GetPreInfoResp {
  uint32 single_lottery_cost = 1; // 单次抽奖消耗
  uint32 remain_lottery_times = 2; // 剩余抽奖次数
  uint32 incr_lottery_tbean = 3; // 增加抽奖次数需要的T豆
  uint32 incr_lottery_times = 4;  // 增加的抽奖次数
}


// 获取奖池预览
message GetAwardPreviewReq {
  uint32 pond_type = 1; // 奖池类型
}

message GetAwardPreviewResp {
  repeated AwardPreviewInfo award_preview_list = 1; // 奖励预览列表
}

message AwardPreviewInfo {
  AwardInfo award_info = 1; // 奖励信息
  uint32 chance = 2;  // 放大1w倍, 0.01%的概率 = 1
}

message AwardInfo {
  string award_name = 1;    // 奖励名称
  string award_icon = 2;    // 奖励图标
  uint32 award_worth = 3;   // 奖励价值
  uint32 award_num = 4;     // 奖励数量
  uint32 award_days = 5;    // 奖励天数
}

// 获取中奖轮播
message GetWinningCarouselReq {
  uint32 pond_type = 1; // 奖池类型
  uint32 uid = 2; // 用于判断主客态
}

message WinningInfo {
  uint32 uid = 1;
  string nickname = 2;      // 昵称
  AwardInfo award_info = 3;
}

message GetWinningCarouselResp {
  repeated WinningInfo winning_info_list = 1; // 轮播列表
}

// 抽奖接口
message LotteryReq {
  uint32 pond_type = 1; // 奖池类型
  uint32 uid = 2;
  uint32 times = 3; // 抽取次数（连抽）
}

message LotteryResp {
  // buf:lint:ignore ENUM_PASCAL_CASE
  enum WINNING_TYPE {
    WINNING_TYPE_UNKNOWN = 0;
    WINNING_TYPE_BIG = 1; // 大奖
    WINNING_TYPE_FLASH = 2; // 出光
    WINNING_TYPE_NORMAL = 3; // 普通奖
    WINNING_TYPE_NONE = 4; // 未中奖
  }
  uint32 winning_type = 1;
  repeated AwardInfo award_info = 2; // 中奖奖品
}

// 获取抽奖记录
message GetLotteryRecordReq {
  uint32 pond_type = 1; // 奖池类型 0.查全部 1.普通池 2.高级池
  uint32 uid = 2;
  string offset = 3; // 起始游标, 空串代表第一页
  uint32 limit = 4;  // 单页条数
  uint32 exchange_type = 5; // 筛选类型 see LotteryItemType
}

message LotteryRecord {
  AwardInfo award_info = 1;
  uint32 cost = 2;
  uint32 got_time = 3;

}

message GetMyLotteryRecordResp {
  repeated LotteryRecord lottery_record_list = 1;
  string offset = 2; // 下一页起始游标, 空串代表没有下一页
}

message AddUserConsumeValReq {
  uint32 uid = 1; // 消费用户Uid
  uint32 val = 2; // 增加的消费值
}

message AddUserConsumeValResp {}


message TestReq {
  string body = 1;
}

message TestResp {}

message GetMyLotteryRecordTypeReq {
  uint32 uid = 1;
  uint32 pond_type = 2; // 奖池类型 0.查全部 1.普通池 2.高级池
}

message  GetMyLotteryRecordTypeResp {
  repeated uint32 type_list = 1; // 奖品类型列表 see LotteryItemType
}