syntax = "proto3";

package banserver;

option go_package = "golang.52tt.com/protocol/services/banserver";

service BanServer {
  // 更新封禁状态
  rpc UpdateBannedStatus(UpdateBannedStatusReq) returns (UpdateBannedStatusResp) {}
  // 获取封禁状态
  rpc GetBannedStatus(GetBannedStatusReq) returns (GetBannedStatusResp) {}
  // 批量获取用户封禁状态
  rpc BatchGetUserBannedStatus(BatchGetUserBannedStatusReq) returns (BatchGetUserBannedStatusResp) {}
  // 获取封禁历史
  rpc GetBannedHistory(GetBannedHistoryReq) returns (GetBannedHistoryResp) {}
  // 批量获取设备封禁状态
  rpc BatchGetDeviceBannedStatus(BatchGetDeviceBannedStatusReq) returns (BatchGetDeviceBannedStatusResp) {}
  // 获取封禁申诉记录
  rpc GetBannedAppealRecord(GetBannedAppealRecordReq) returns (GetBannedAppealRecordResp) {}
  // 新增封禁申诉记录
  rpc SetBannedAppealRecord(SetBannedAppealRecordReq) returns (SetBannedAppealRecordResp) {}
  // 更新封禁申诉记录
  rpc UpdateBannedAppealRecord(UpdateBannedAppealRecordReq) returns (UpdateBannedAppealRecordResp) {}
  // 获取封禁操作人列表
  rpc GetBannedOperator(GetBannedOperatorReq) returns (GetBannedOperatorResp) {}
  // 批量更新封禁状态
  rpc BatchUpdateBanedStatus(BatchUpdateBanedStatusReq) returns (BatchUpdateBanedStatusResp) {}

  // 获取黑名单复核记录
  rpc GetBannedCheckRecord(GetBannedCheckRecordReq) returns (GetBannedCheckRecordResp) {}
  // 更新黑名单复核记录
  rpc UpdateBannedCheckRecord(UpdateBannedCheckRecordReq) returns (UpdateBannedCheckRecordResp) {}
  // 新增黑名单复核记录
  rpc AddBannedCheckRecord(AddBannedCheckRecordReq) returns (AddBannedCheckRecordResp) {}
  // 获取黑名单复核操作人
  rpc GetBannedCheckOperator(GetBannedCheckOperatorReq) returns (GetBannedCheckOperatorResp) {}
  // 写入黑名单复核操作人
  rpc SetBannedCheckOperator(SetBannedCheckOperatorReq) returns (SetBannedCheckOperatorResp) {}

  // 提交封禁申诉
  rpc SubmitBannedAppeal(SubmitBannedAppealReq) returns (SubmitBannedAppealResp) {}
  // 撤销封禁申诉
  rpc RemoveBannedAppeal(RemoveBannedAppealReq) returns (RemoveBannedAppealResp) {}
}

// 封禁操作类型
enum BanOpType {
  // 未指定操作
  BAN_OP_UNSPECIFIED = 0;
  // 封用户
  BAN_OP_BAN_USER = 0x0001;
  // 封设备
  BAN_OP_BAN_DEVICE = 0x0002;
  // 封 客户端ip, reserved
  BAN_OP_BAN_CLIENT_IP = 0x0004;
  // 封 手机号, reserved
  BAN_OP_BAN_PHONE = 0x0008;

  // 解封 用户
  BAN_OP_UNBAN_USER = 0x0100;
  // 解封 设备
  BAN_OP_UNBAN_DEVICE = 0x0200;
  // 解封 客户端ip, reserved
  BAN_OP_UNBAN_CLIENT_IP = 0x0400;
  // 解封 手机号, reserved
  BAN_OP_UNBAN_PHONE = 0x0800;
}

enum BanStatusType {
  // 未封禁
  BAN_STATUS_UNSPECIFIED = 0;
  // 已封禁
  BAN_STATUS_BANNED = 1;
}

message UpdateBannedStatusReq {
  // 更新状态
  UpdateBannedStatus status = 1;
}

message UpdateBannedStatus {
  // 封禁操作类型 see BanOpType
  uint32 op_type = 1;
  // uid
  uint32 uid = 2;
  // 设备id
  string device_id = 3;
  // 客户端ip
  string client_ip = 4;
  // 封禁时间
  uint32 banned_at = 5;
  // 解封时间
  uint32 recovery_at = 6;
  // 封禁原因
  string reason = 7;
  // 操作人
  string operator_name = 8;
  // 证明图
  string proof_pic = 9;
  // 扩展信息
  string ext_info = 10;
  // 不记录日志
  bool  no_log = 11;
  // 原因详情
  string reason_detail = 12;
  // 封禁级别 see BAN_LEVEL_TYPE
  uint32 ban_level = 13;
}

message UpdateBannedStatusResp{
}

message GetBannedStatusReq{
  // uid
  uint32 uid = 1;
  // 设备id
  string device_id = 2;
  // 客户端ip
  string client_ip = 3;
  // 手机
  string phone = 4;
}

enum ReasonCodeType {
  // 未封禁
  REASON_CODE_UNSPECIFIED = 0;
  // 普通封禁
  REASON_CODE_BAN = 1;
  // 账号回收封禁
  REASON_CODE_ACCOUNT_RECYCLE = 2;
  // 黑产封禁
  REASON_CODE_ACCOUNT_BLACK = 4;
}

message BannedStatus {
  // 封禁状态
  BanStatusType status = 1;
  // 封禁类型
  BanByType banned_type = 2;
  // uid
  uint32 uid = 3;
  // 封禁时间
  uint32 banned_at = 4;
  // 解封时间
  uint32 recovery_at = 5;
  // 面对用户的原因提示
  string reason = 6;
  // 原因分类
  uint32 reason_code = 7;
  // 原因的详情内容,运营后台使用
  string reason_detail = 8;
}

enum BanByType {
  // 未指定
  BAN_BY_UNSPECIFIED = 0;
  // 用户封禁
  BAN_BY_USER = 1;
  // 设备封禁
  BAN_BY_DEVICE = 2;
  // 客户端ip封禁
  BAN_BY_CLIENT_IP = 3;
  // 手机号封禁
  BAN_BY_PHONE = 4;
}

message GetBannedStatusResp {
  // 封禁状态
  BannedStatus banned_status = 1;
}

message BatchGetUserBannedStatusReq {
  // uid 列表
  repeated uint32 uid_list = 1;
}

message BatchGetUserBannedStatusResp {
  // 封禁状态列表
  repeated BannedStatus status_list = 1;
}

message GetBannedHistoryReq {
  // uid
  uint32 uid = 1;
  // 设备id
  string device_id = 2;
  // reserved
  string client_ip = 3;
  // reserved
  uint32 op_type = 4;
  // 分页偏移
  uint32 offset = 5;
  // 分页大小
  uint32 limit = 6;
  // 根据封禁时间查询，开始时间
  uint32 begin_time = 7;
  // 根据封禁时间查询，结束时间
  uint32 end_time = 8;
  // 操作人
  string operator_name = 9;
  // 封禁原因
  string reason = 10;
}

message BannedRecord {
  // 封禁操作类型 see BanOpType
  uint32 op = 1;
  // 封禁时间
  uint32 banned_at = 2;
  // 解封时间
  uint32 recovery_at = 3;
  // 原因
  string reason = 4;
  // 操作人
  string operator_name = 5;
  // uid
  uint32 uid = 6;
  // 设备id
  string device_id = 7;
  // 客户端ip，预留
  string client_ip = 8;
  // 证明图
  string proof_pic = 9;
  // 扩展信息
  string ext_info = 10;
  // 原因详情
  string reason_detail = 11;
}

message GetBannedHistoryResp {
  // 封禁记录列表
  repeated BannedRecord records = 1;
  // 总数
  uint32 total = 2;
}

message DeviceBannedStatus {
  // 封禁状态
  BanStatusType status = 1;
  // 封禁类型
  BanByType banned_type = 2;
  // 设备id
  string device_id = 3;
  // 封禁时间
  uint32 banned_at = 4;
  // 解封时间
  uint32 recovery_at = 5;
  // 面对用户的原因提示
  string reason = 6;
  // 原因的详情内容,运营后台使用
  string reason_detail = 7;
}

message BatchGetDeviceBannedStatusReq {
  // 设备id列表
  repeated string device_id_list = 1;
}

message BatchGetDeviceBannedStatusResp {
  // 封禁状态列表
  repeated DeviceBannedStatus status_list = 1;
}

// 封禁申诉
enum BannedAppealState {
  // 未指定
  BANNED_APPEAL_STATE_NONE = 0;
  // 待定
  BANNED_APPEAL_STATE_PEND = 1;
  // 通过
  BANNED_APPEAL_STATE_PASS = 2;
  // 拒绝
  BANNED_APPEAL_STATE_DENY = 4;
}

message BannedAppealRecord {
  // 记录id
  uint32 id = 1;
  // uid
  uint32 uid = 2;
  // 状态
  BannedAppealState state = 3;
  // 原因分类
  uint32 reason_code = 4;
  // 封禁开始时间查询条件
  int64 banned_begin_at = 5;
  // 封禁结束时间查询条件
  int64 banned_end_at = 6;
  // 记录创建时间
  int64 create_at = 7;
  // 记录更新时间
  int64 update_at = 8;
  // 创建描述
  string create_desc = 9;
  // 更新描述
  string update_desc = 10;
  // 操作人
  string operator_name = 11;
  // 马甲包id
  uint32 market_id = 12;
}

message GetBannedAppealRecordReq {
  // uid列表
  repeated uint32 uid_list = 1;
  // 状态列表
  repeated uint32 state_list = 2;
  // 原因分类
  repeated ReasonCodeType reason_code_list = 3;
  // 创建开始时间
  int64 create_begin_at = 4;
  // 创建结束时间
  int64 create_end_at = 5;
  // 分页偏移量
  uint32 offset = 6;
  // 分页大小
  uint32 limit = 7;
  // 是否返回总数
  bool resp_total = 8;
}

message GetBannedAppealRecordResp {
  // 记录列表
  repeated BannedAppealRecord record_list = 1;
  // 记录总数
  uint32 total = 2;
}

message SetBannedAppealRecordReq {
  // 记录列表
  repeated BannedAppealRecord record_list = 1;
}

message SetBannedAppealRecordResp {
}

message UpdateBannedAppealRecordReq {
  // 记录id
  uint32 id = 1;
  // uid
  uint32 uid = 2;
  // 状态
  uint32 state = 3;
  // 更新描述
  string update_desc = 4;
  // 操作人
  string operator_name = 5;
}

message UpdateBannedAppealRecordResp {
  // 是否更新
  bool updated = 1;
}

// 获取封禁操作人
message GetBannedOperatorReq {
  // 封禁操作人
  string operator_name = 1;
  // 数量限制
  uint32 limit = 2;
}

// 封禁操作记录
message BannedOperator {
  // 操作人
  string operator_name = 1;
  // 最后操作时间
  int64 last_op_at = 2;
  // 最后操作类型
  uint32 last_op_type = 3;
}

message GetBannedOperatorResp{
  // 操作人列表
  repeated BannedOperator operator_list = 1;
}


message BatchUpdateBanedStatusReq {
  // 封禁更新列表
  repeated UpdateBannedStatus list = 1;
}

message BatchUpdateBanedStatusResp {
  // 成功数
  int32 success_size = 1;
}

// 黑名单复核用户标签
enum BannedCheckUserTag {
  // 未指定
  BANNED_CHECK_USER_TAG_UNSPECIFIED = 0;
  // 高付费
  BANNED_CHECK_USER_TAG_HIGH_PAY = 1;
  // 专业从业者
  BANNED_CHECK_USER_TAG_PROFESSION = 2;
}

// 黑名单复核封禁来源
enum BannedCheckSource {
  // 未指定
  BANNED_CHECK_SOURCE_UNSPECIFIED = 0;
  // 审核封禁
  BANNED_CHECK_SOURCE_AUDIT = 1;
  // 策略封禁
  BANNED_CHECK_SOURCE_STRATEGY = 2;
}

// 黑名单复核记录类型
enum BannedCheckRecordType {
  // 未指定
  BANNED_CHECK_RECORD_TYPE_UNSPECIFIED = 0;
  // 待核实
  BANNED_CHECK_RECORD_TYPE_TO_CHECK = 1;
  // 已核实
  BANNED_CHECK_RECORD_TYPE_CHECKED = 2;
  // 审批中
  BANNED_CHECK_RECORD_TYPE_CHECKING = 3;
  // 已删除
  BANNED_CHECK_RECORD_TYPE_DELETED = 4;
}

// 黑名单复核处理结果类型
enum BannedCheckType {
  // 未指定
  BANNED_CHECK_TYPE_UNSPECIFIED = 0;
  // 保持封禁
  BANNED_CHECK_TYPE_KEEP_BANNED = 1;
  // 已复核解封
  BANNED_CHECK_TYPE_UNBANNED_BY_CHECK = 2;
  // 黑名单解封
  BANNED_CHECK_TYPE_UNBANNED_BY_OTHER = 3;
  // 驳回解封
  BANNED_CHECK_TYPE_REJECT_UNBAN = 4;
}

// 获取黑名单复核请求
message GetBannedCheckRecordReq {
  // 工单id列表
  repeated uint32 task_id_list = 1;
  // 用户id列表
  repeated uint32 uid_list = 2;
  // 记录类型列表
  repeated BannedCheckRecordType record_type_list = 3;
  // 用户标签列表
  repeated BannedCheckUserTag user_tag_list = 4;
  // 封禁来源列表
  repeated BannedCheckSource source_list = 5;
  // 对外原因列表
  repeated string reason_list = 6;
  // 操作人列表
  repeated string operator_name_list = 7;
  // 封禁开始时间戳
  int64 banned_begin_at = 8;
  // 封禁结束时间戳
  int64 banned_end_at = 9;
  // 复核操作人列表
  repeated string check_operator_name_list = 10;
  // 复核开始时间戳
  int64 check_begin_at = 11;
  // 复核结束时间戳
  int64 check_end_at = 12;
  // 处理结果
  repeated BannedCheckResultType check_result_type_list = 13;
  // 分页偏移
  uint32 offset = 14;
  // 分页大小
  uint32 limit = 15;
}

// 黑名单复核处理结果类型
enum BannedCheckResultType {
  // 未指定
  BANNED_CHECK_RESULT_TYPE_UNSPECIFIED = 0;
  // 已解封
  BANNED_CHECK_RESULT_TYPE_UNBANNED = 1;
  // 复核异议
  BANNED_CHECK_RESULT_TYPE_OBJECTION = 2;
  // 保持封禁
  BANNED_CHECK_RESULT_TYPE_KEEP_BANNED = 3;
  // 黑名单解封
  BANNED_CHECK_RESULT_TYPE_UNBANNED_BY_OTHER = 4;
  // 复核解封，待其他复核
  BANNED_CHECK_RESULT_TYPE_OPERATOR_UNBANNED = 5;
  // 保持封禁，待其他复核
  BANNED_CHECK_RESULT_TYPE_OPERATOR_KEEP_BANNED = 6;
}

// 黑名单复核记录
message BannedCheckRecord {
  // 工单id
  uint32 task_id = 1;
  // 记录类型
  BannedCheckRecordType record_type = 2;
  // 用户id
  uint32 uid = 3;
  // 用户昵称
  string nickname = 4;
  // 手机号码
  string phone = 5;
  // 身份证号码
  string identity_num = 6;
  // 用户标签
  BannedCheckUserTag user_tag = 7;
  // 对外原因
  string reason = 8;
  // 封禁原因扩展
  string reason_detail = 9;
  // 违规证明图
  string proof_pic = 10;
  // 封禁来源
  BannedCheckSource source = 11;
  // 封禁时间
  int64 banned_at = 12;
  // 恢复时间
  int64 recovery_at = 13;
  // 封禁操作人
  string operator_name = 14;
  // 封禁状态 see BanOpType
  uint32 op_type = 15;
  // 复核时间
  int64 check_at = 16;
  // 复核操作人
  string check_operator_name = 17;
  // 解封原因
  string recover_reason = 18;
  // 处理类型
  BannedCheckType check_type = 19;
  // 处理结果类型
  BannedCheckResultType check_result_type = 20;
  // 处理结果描述
  string check_result_desc = 21;
}

// 获取黑名单复核响应
message GetBannedCheckRecordResp {
  // 黑名单复核记录列表
  repeated BannedCheckRecord record_list = 1;
  // 总数
  uint32 total_cnt = 2;
}

// 新增黑名单复核记录请求
message AddBannedCheckRecordReq {
  // 黑名单复核记录
  BannedCheckRecord record = 1;
}

// 新增黑名单复核记录响应
message AddBannedCheckRecordResp {
}

// 更新黑名单复核记录请求
message UpdateBannedCheckRecordReq {
  // 用户id
  uint32 uid = 1;
  // 工单id列表
  repeated uint32 task_id_list = 2;
  // 记录类型列表
  repeated BannedCheckRecordType record_type_list = 3;
  // 处理类型
  BannedCheckRecord update_record = 4;
}

// 更新黑名单复核记录响应
message UpdateBannedCheckRecordResp {
  // 影响记录数
  int64 affected = 1;
}

// 获取黑名单复核操作人请求
message GetBannedCheckOperatorReq {
  // 操作人
  string operator_name = 1;
  // 分页偏移
  uint32 limit = 2;
}

// 获取黑名单复核操作人响应
message GetBannedCheckOperatorResp {
  // 黑名单复核操作人列表
  repeated BannedOperator operator_list = 1;
}

// 写入黑名单复核操作人请求
message SetBannedCheckOperatorReq {
  // 操作人
  string operator_name = 1;
}

// 写入黑名单复核操作人响应
message SetBannedCheckOperatorResp {
}

// 提交封禁申诉
message SubmitBannedAppealReq {
  // 用户id
  uint32 uid = 1;
  // 封禁原因
  uint32 reason_code = 2;
}

// 提交封禁申诉响应
message SubmitBannedAppealResp {
  // 申诉已存在
  bool existed = 1;
}

// 撤销封禁申诉
message RemoveBannedAppealReq {
  // 用户id
  uint32 uid = 1;
  // 封禁原因
  uint32 reason_code = 2;
}

// 撤销封禁申诉响应
message RemoveBannedAppealResp {
}

