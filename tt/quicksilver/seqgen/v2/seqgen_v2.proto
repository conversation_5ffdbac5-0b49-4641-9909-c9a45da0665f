syntax = "proto3";

// buf:lint:ignore PACKAGE_DIRECTORY_MATCH
// buf:lint:ignore PACKAGE_SAME_DIRECTORY
package seqgen;

// buf:lint:ignore PACKAGE_SAME_GO_PACKAGE
option go_package = "golang.52tt.com/protocol/services/seqgen/v2;seqgen";

service SeqGen {
  // 获取严格递增的序列号
  rpc GenerateSeq(GenerateSeqReq) returns(GenerateSeqRsp) {}
  // 批量获取
  rpc BatchGenerateSeq(BatchGenerateSeqReq) returns(BatchGenerateSeqRsp) {}
  // 检查序列号
  rpc RetrieveSeq(RetrieveSeqReq) returns(RetrieveSeqRsp) {}
  // 批量检查序列号
  rpc BatchRetrieveSeq(BatchRetrieveSeqReq) returns(BatchRetrieveSeqRsp) {}
  // 批量检查序列号，多个key
  rpc RetrieveMultiSeq(RetrieveMultiSeqReq) returns(RetrieveMultiSeqRsp) {}
  // 设置一个新序列号
  rpc SetSeq(SetSeqReq) returns(SetSeqRsp) {}

  // 趋势递增的全局唯一序列号
  // 开箱即用 可以通过id溯源
  rpc GenSnowflakeId(GenSnowflakeReq) returns (GenSnowflakeRsp) {}
  // 解码序列号
  rpc DecodeSnowflakeId(DecodeSnowflakeReq) returns (DecodeSnowflakeRsp) {}
}

message GenerateSeqReq {
  uint32 id = 1;
  string namespace = 2;
  string key = 3;
  uint32 incr = 4;
}

message GenerateSeqRsp {
  uint64 seq = 1;
}

message BatchGenerateSeqReq {
  repeated uint32 id_list = 1;
  string namespace = 2;
  string key = 3;
  uint32 incr = 4;
}

message BatchGenerateSeqRsp {
  // nothing
}

message RetrieveSeqReq {
  uint32 id = 1;
  string namespace = 2;
  string key = 3;
}

message RetrieveSeqRsp {
  uint64 seq = 1;
}

message BatchRetrieveSeqReq {
  repeated uint32 id_list = 1;
  string namespace = 2;
  string key = 3;
}

message BatchRetrieveSeqRsp {
  map<uint32, uint64> seq_map = 1;
}

message RetrieveMultiSeqReq {
  uint32 id = 1;
  string namespace = 2;
  repeated string keys = 3;
}

message RetrieveMultiSeqRsp {
  map<string, uint64> seq_map = 1;
}

message SetSeqReq {
  uint32 id = 1;
  string namespace = 2;
  string key = 3;
  uint64 seq = 4;
}

message SetSeqRsp {
  // nothing
}

message GenSnowflakeReq {
  // nothing
}

message GenSnowflakeRsp {
  int64 id = 1;
}

message DecodeSnowflakeReq {
  int64 id = 1;
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message DecodeSnowflakeRsp {
  int64 timestamp = 1; // ms
  int64 workerId = 2;  // etcd node id
  int64 sequence = 3;  // seq
}
