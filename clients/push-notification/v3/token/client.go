package push_token

import (
	"context"

	"golang.52tt.com/pkg/client"
	grpcClient "golang.52tt.com/pkg/foundation/grpc/client"
	pb "golang.52tt.com/protocol/services/push-notification/v3"
	"google.golang.org/grpc"
)

const (
	serviceName = "push-v3-token"
)

type Client struct {
	client.BaseClient
}

func newClient(dopts ...grpc.DialOption) (*Client, error) {
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(serviceName, func(cc *grpc.ClientConn) interface{} {
			return pb.NewPushTokenClient(cc)
		}, dopts...),
	}, nil
}

func NewClient(dopts ...grpc.DialOption) (*Client, error) {
	dopts = append(dopts, grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return newClient(dopts...)
}

func (c *Client) typedStub() pb.PushTokenClient { return c.Stub().(pb.PushTokenClient) }

func (c *Client) RegisterDeviceToken(ctx context.Context, in *pb.RegisterDeviceTokenReq, opts ...grpc.CallOption) (*pb.RegisterDeviceTokenResp, error) {
	return c.typedStub().RegisterDeviceToken(ctx, in, opts...)
}

func (c *Client) UnregisterDeviceToken(ctx context.Context, in *pb.UnregisterDeviceTokenReq, opts ...grpc.CallOption) (*pb.UnregisterDeviceTokenResp, error) {
	return c.typedStub().UnregisterDeviceToken(ctx, in, opts...)
}

func (c *Client) QueryDeviceToken(ctx context.Context, in *pb.QueryDeviceTokenReq, opts ...grpc.CallOption) (*pb.QueryDeviceTokenResp, error) {
	return c.typedStub().QueryDeviceToken(ctx, in, opts...)
}
