package usual_device

import (
	"golang.52tt.com/pkg/client"
	grpcClient "golang.52tt.com/pkg/foundation/grpc/client"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/tracing"
	tracingGrpc "golang.52tt.com/pkg/tracing/grpc"
	pb "golang.52tt.com/protocol/services/usual-device-svr"
	"golang.org/x/net/context"
	"google.golang.org/grpc"
)

const (
	serviceName = "usual-device-svr"
)

type Client struct {
	client.BaseClient
}

func NewClient(dopts ...grpc.DialOption) *Client {
	return newClient(dopts...)
}

func NewTracedClient(tracer tracing.Tracer) *Client {
	tracerInt := tracingGrpc.TracedUnaryClientInterceptor(
		tracing.LogPayloads(true),
		tracing.UsingTracer(tracer),
	)

	return newClient(grpc.WithUnaryInterceptor(tracerInt))
}

func newClient(dopts ...grpc.DialOption) *Client {

	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(serviceName, func(cc *grpc.ClientConn) interface{} {
			return pb.NewUsualDeviceSvrClient(cc)
		}, dopts...),
	}
}
func (c *Client) typedStub() pb.UsualDeviceSvrClient { return c.Stub().(pb.UsualDeviceSvrClient) }

func (c *Client) CheckMessage(ctx context.Context, phone, content string, checkType uint32) (*pb.CheckMessageResp, protocol.ServerError) {
	res, err := c.typedStub().CheckMessage(ctx, &pb.CheckMessageReq{
		Phone:          phone,
		CheckType:      checkType,
		MessageContent: content,
	})
	return res, protocol.ToServerError(err)
}

func (c *Client) RecordMessageCheck(ctx context.Context, phone, content string, checkType uint32) protocol.ServerError {
	_, err := c.typedStub().RecordMessageCheck(ctx, &pb.RecordMessageCheckReq{
		Phone:          phone,
		CheckType:      checkType,
		MessageContent: content,
	})
	return protocol.ToServerError(err)
}

func (c *Client) UpdateUsualDevice(ctx context.Context, deviceID []byte, uid, checkType uint32) protocol.ServerError {
	_, err := c.typedStub().UpdateUsualDevice(ctx, &pb.UpdateUsualDeviceReq{
		Uid:       uid,
		CheckType: checkType,
		DeviceId:  deviceID,
	})
	return protocol.ToServerError(err)
}

func (c *Client) CheckUsualDevice(ctx context.Context, deviceID string, uid, checkType, deviceType uint32) (*pb.CheckUsualDeviceResp, protocol.ServerError) {
	res, err := c.typedStub().CheckUsualDevice(ctx, &pb.CheckUsualDeviceReq{
		Uid:        uid,
		CheckType:  checkType,
		DeviceId:   []byte(deviceID),
		DeviceType: deviceType,
	})
	return res, protocol.ToServerError(err)
}

func (c *Client) RecordRandString(ctx context.Context, phone, rs string, checkType uint32) (*pb.RecordRandStringResp, protocol.ServerError) {
	res, err := c.typedStub().RecordRandString(ctx, &pb.RecordRandStringReq{
		Phone:      phone,
		CheckType:  checkType,
		RandString: rs,
	})
	return res, protocol.ToServerError(err)
}

func (c *Client) GetRandString(ctx context.Context, phone string, checkType uint32) (*pb.GetRandStringResp, protocol.ServerError) {
	res, err := c.typedStub().GetRandString(ctx, &pb.GetRandStringReq{
		Phone:     phone,
		CheckType: checkType,
	})
	return res, protocol.ToServerError(err)
}

func (c *Client) RecordDeviceCheck(ctx context.Context, deviceID []byte, uid, checkType uint32) (*pb.RecordDeviceCheckResp, protocol.ServerError) {
	res, err := c.typedStub().RecordDeviceCheck(ctx, &pb.RecordDeviceCheckReq{
		Uid:       uid,
		DeviceId:  deviceID,
		CheckType: checkType,
	})
	return res, protocol.ToServerError(err)
}

func (c *Client) RecordCheckResult(ctx context.Context, deviceID []byte, uid, checkType uint32) (*pb.RecordCheckResultResp, protocol.ServerError) {
	res, err := c.typedStub().RecordCheckResult(ctx, &pb.RecordCheckResultReq{
		Uid:       uid,
		DeviceId:  deviceID,
		CheckType: checkType,
	})
	return res, protocol.ToServerError(err)
}

func (c *Client) GetMsgContent(ctx context.Context, deviceID []byte, uid, checkType uint32) (*pb.GetMsgContentResp, protocol.ServerError) {
	res, err := c.typedStub().GetMsgContent(ctx, &pb.GetMsgContentReq{
		Uid:       uid,
		DeviceId:  deviceID,
		CheckType: checkType,
	})
	return res, protocol.ToServerError(err)
}

func (c *Client) CheckVerifyMessage(ctx context.Context, deviceID []byte, uid, checkType uint32) (*pb.CheckVerifyMessageResp, protocol.ServerError) {
	res, err := c.typedStub().CheckVerifyMessage(ctx, &pb.CheckVerifyMessageReq{
		Uid:       uid,
		DeviceId:  deviceID,
		CheckType: checkType,
	})
	return res, protocol.ToServerError(err)
}

func (c *Client) UpdateConsumeVerifyUidList(ctx context.Context, in *pb.UpdateConsumeVerifyUidListReq) (*pb.UpdateConsumeVerifyUidListResp, protocol.ServerError) {
	res, err := c.typedStub().UpdateConsumeVerifyUidList(ctx, in, grpc.WaitForReady(true))
	return res, protocol.ToServerError(err)
}

func (c *Client) InConsumeVerifyUidList(ctx context.Context, uid uint64) (bool, protocol.ServerError) {
	res, err := c.typedStub().InConsumeVerifyUidList(ctx, &pb.InConsumeVerifyUidListReq{Uid: uid}, grpc.WaitForReady(true))
	if err != nil {
		return false, protocol.ToServerError(err)
	}
	return res.InList, protocol.ToServerError(err)
}

func (c *Client) GetDeviceAuthError(ctx context.Context, uid uint64, clientType uint16, clientVersion uint32) protocol.ServerError {
	_, err := c.typedStub().GetDeviceAuthError(ctx, &pb.GetDeviceAuthErrorReq{
		Uid:           uid,
		ClientVersion: clientVersion,
		ClientType:    uint32(clientType),
	})
	if err != nil {
		return protocol.ToServerError(err)
	}

	return nil
}

type GetUserUsualDeviceResp = pb.GetUserUsualDeviceResp

func (c *Client) GetUserUsualDevice(ctx context.Context, uid uint64, start, end int64, limit int32) (*GetUserUsualDeviceResp, protocol.ServerError) {
	out, err := c.typedStub().GetUserUsualDevice(ctx, &pb.GetUserUsualDeviceReq{
		Uid:       uid,
		BeginTime: start,
		EndTime:   end,
		Limit:     limit,
	})

	return out, protocol.ToServerError(err)
}
