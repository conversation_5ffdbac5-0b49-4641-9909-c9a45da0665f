package apicenter

import (
	"golang.52tt.com/services/notify"
	"strconv"
	"time"

	"golang.52tt.com/pkg/client"
	grpcClient "golang.52tt.com/pkg/foundation/grpc/client"
	"golang.org/x/net/context"
	"google.golang.org/grpc"
	"google.golang.org/grpc/metadata"

	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/tracing"
	tracingGrpc "golang.52tt.com/pkg/tracing/grpc" //
	pb "golang.52tt.com/protocol/services/apicenter/apiserver"
)

const (
	serviceName = "apicenter"

	SenderUser            = uint32(pb.IM_SENDER_TYPE_IM_SENDER_NORMAL)
	SenderOneYuanTreasure = uint32(pb.IM_SENDER_TYPE_IM_SENDER_ASSISTANT_OneYuanWinTreasure)
	SenderPublicAccount   = uint32(pb.IM_SENDER_TYPE_IM_SENDER_PUBLIC_ACCOUNT)

	ReceiverUser          = uint32(pb.IM_RECEIVER_TYPE_IM_RECEIVER_USER)
	ReceiverPublicAccount = uint32(pb.IM_RECEIVER_TYPE_IM_RECEIVER_PUBLIC_ACCOUNT)
	ReceiverBroadcast     = uint32(pb.IM_RECEIVER_TYPE_IM_RECEIVER_BROADCAST)

	ContentText                 = uint32(pb.IM_CONTENT_TYPE_IM_CONTENT_TEXT)
	ContentTextWithHighlightURL = uint32(pb.IM_CONTENT_TYPE_IM_CONTENT_TEXT_WITH_HL_URL)
	ContentOfficialMessage      = uint32(pb.IM_CONTENT_TYPE_IM_CONTENT_OFFICIAL_MESSAGE)
)

type ImMsg = pb.ImMsg

type Client struct {
	client.BaseClient
}

func NewClient(dopts ...grpc.DialOption) *Client {
	return newClient(dopts...)
}

func NewTracedClient(tracer tracing.Tracer) *Client {
	tracerInt := tracingGrpc.TracedUnaryClientInterceptor(
		tracing.LogPayloads(true),
		tracing.UsingTracer(tracer),
	)

	return newClient(grpc.WithUnaryInterceptor(tracerInt))
}

func newClient(dopts ...grpc.DialOption) *Client {

	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(serviceName, func(cc *grpc.ClientConn) interface{} {
			return pb.NewApicenterClient(cc)
		}, dopts...),
	}
}
func (c *Client) typedStub() pb.ApicenterClient { return c.Stub().(pb.ApicenterClient) }

// 不建议使用，接入 im-api 接口可使用 IM 标准化能力
func (c *Client) SendImMsg(ctx context.Context, uid uint32, appID protocol.AppID, msgList []*pb.ImMsg, notify bool) protocol.ServerError {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uid))))
	r, err := c.typedStub().SendImMsg(context.Background(),
		&pb.SendImMsgReq{
			BaseReq:    &pb.BaseReq{Appid: uint32(appID)},
			MsgList:    msgList,
			WithNotify: notify,
		},
	)

	if err != nil {
		return protocol.ToServerError(err)
	}

	if r.GetBaseResp().GetRet() != 0 {
		return protocol.NewServerError(int(r.GetBaseResp().GetRet()), r.GetBaseResp().GetErrMsg())
	}
	return nil
}

// Deprecated, use im-api.SimpleSendTTAssistantText(ctx, uid, text, "", "")
func (c *Client) SendTTHelperMsg(ctx context.Context, uid uint32, text string) protocol.ServerError {
	imType := &pb.ImType{
		SenderType:   uint32(pb.IM_SENDER_TYPE_IM_SENDER_NORMAL),
		ReceiverType: uint32(pb.IM_RECEIVER_TYPE_IM_RECEIVER_USER),
		ContentType:  uint32(pb.IM_CONTENT_TYPE_IM_CONTENT_TEXT),
	}
	fromUID := uint32(10000)
	toIDList := []uint32{
		uid,
	}
	imContent := &pb.ImContent{
		TextNormal: &pb.ImTextNormal{
			Content: text,
		},
	}
	msg := &pb.ImMsg{
		ImType:    imType,
		FromUid:   fromUID,
		ToIdList:  toIDList,
		ImContent: imContent,
		Platform:  pb.Platform_UNSPECIFIED,
	}
	msgList := []*pb.ImMsg{
		msg,
	}

	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uid))))
	r, err := c.typedStub().SendImMsg(context.Background(),
		&pb.SendImMsgReq{
			BaseReq:    &pb.BaseReq{Appid: uint32(protocol.TT)},
			MsgList:    msgList,
			WithNotify: true,
		},
	)

	if err != nil {
		return protocol.ToServerError(err)
	}

	if r.GetBaseResp().GetRet() != 0 {
		return protocol.NewServerError(int(r.GetBaseResp().GetRet()), r.GetBaseResp().GetErrMsg())
	}
	return nil
}

// Deprecated, use im-api.SimpleSendTTAssistantText(ctx, uid, content, hlight, url)
func (c *Client) SendTTHelperMsgWithUrl(uid uint32, content, hlight, url string) error {
	msg := new(pb.ImMsg)
	msg.ImType = &pb.ImType{
		SenderType:   uint32(pb.IM_SENDER_TYPE_IM_SENDER_NORMAL),
		ReceiverType: uint32(pb.IM_RECEIVER_TYPE_IM_RECEIVER_USER),
	}
	msg.FromUid = 10000 // TT语音助手
	msg.ToIdList = []uint32{uid}
	msg.ImContent = &pb.ImContent{}
	msg.ImContent.TextHlUrl = &pb.ImTextWithHighlightUrl{
		Content:    content,
		Hightlight: hlight,
		Url:        url,
	}
	msg.ImType.ContentType = uint32(pb.IM_CONTENT_TYPE_IM_CONTENT_TEXT_WITH_HL_URL)
	msg.Platform = pb.Platform_UNSPECIFIED
	msg.AppPlatform = "all"

	ctx, _ := context.WithTimeout(context.Background(), time.Second*5)
	err := c.SendImMsg(ctx, uid, protocol.TT, []*pb.ImMsg{msg}, true)

	if err != nil {
		return err
	}
	notify.NotifySync(uid, notify.ImMsgV2)
	return nil
}

func (c *Client) KickoutChannelMember(ctx context.Context, opUID uint32, kickedUIDs []uint32, channelID uint32, second uint32, kickType uint32, kickText string) protocol.ServerError {

	in := &pb.KickoutChannelMemberReq{
		BaseReq:        &pb.BaseReq{Appid: uint32(0)},
		ChannelId:      channelID,
		BanEnterSecond: second,
		TargetUidList:  kickedUIDs,
		KickType:       kickType,
		KickText:       kickText,
	}
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(opUID))))
	_, err := c.typedStub().KickoutChannelMember(ctx, in)
	if err != nil {
		return protocol.ToServerError(err)
	}

	return nil
}

func (c *Client) KickoutUser(ctx context.Context, uin uint32, UID uint32,
	reason string) protocol.ServerError {

	in := &pb.KickoutUserReq{
		BaseReq: &pb.BaseReq{Appid: uint32(0)},
		Uid:     UID,
		Reason:  reason,
	}
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uin))))
	_, err := c.typedStub().KickoutUser(ctx, in)
	if err != nil {
		return protocol.ToServerError(err)
	}

	return nil
}

// 重置密码
func (c *Client) ResetPassword(ctx context.Context, uid, reqSourceType uint32, phone string, marketId uint32) protocol.ServerError {
	in := &pb.ResetPasswrodReq{
		Uid:           uid,
		ReqSourceType: reqSourceType,
		MobilePhone:   phone,
		MarketId:      marketId,
	}
	_, err := c.typedStub().ResetPasswrod(ctx, in, grpc.WaitForReady(true))
	if err != nil {
		return protocol.ToServerError(err)
	}

	return nil
}

// 解绑三方账户
func (c *Client) DetachThirdpart(ctx context.Context, uid, reqSourceType uint32) protocol.ServerError {
	in := &pb.DetachThirdpartReq{
		Uid:           uid,
		ReqSourceType: reqSourceType,
	}
	_, err := c.typedStub().DetachThirdpart(ctx, in, grpc.WaitForReady(true))
	if err != nil {
		return protocol.ToServerError(err)
	}

	return nil
}

// 解绑手机号
func (c *Client) UnbindPhone(ctx context.Context, uid, reqSourceType uint32) (*pb.UnbindPhoneResp, protocol.ServerError) {
	in := &pb.UnbindPhoneReq{
		Uid:           uid,
		ReqSourceType: reqSourceType,
	}
	out, err := c.typedStub().UnbindPhone(ctx, in, grpc.WaitForReady(true))
	if err != nil {
		return out, protocol.ToServerError(err)
	}

	return out, nil
}

// 重置密保问题
func (c *Client) ClearSecurityQuestion(ctx context.Context, uid, reqSourceType uint32) protocol.ServerError {
	in := &pb.ClearSecurityQuestionReq{
		Uid:           uid,
		ReqSourceType: reqSourceType,
	}
	_, err := c.typedStub().ClearSecurityQuestion(ctx, in, grpc.WaitForReady(true))
	if err != nil {
		return protocol.ToServerError(err)
	}

	return nil
}

// 关注/取关公众号
func (c *Client) FollowPublic(ctx context.Context, in *pb.FollowPublicReq) (*pb.FollowPublicResp, protocol.ServerError) {
	r, err := c.typedStub().FollowPublic(ctx, in)
	return r, protocol.ToServerError(err)
}

// 关注/取关语音主播会长服务号
func (c *Client) FollowOrUnFollowYuYinFuWuHao(ctx context.Context, actorUid uint32, isFollow bool) protocol.ServerError {
	in := &pb.FollowOrUnFollowYuYinFuWuHaoReq{
		ActorUid: actorUid,
		IsFollow: isFollow,
	}
	_, err := c.typedStub().FollowOrUnFollowYuYinFuWuHao(ctx, in, grpc.WaitForReady(true))
	if err != nil {
		return protocol.ToServerError(err)
	}

	return nil
}

func (c *Client) PushMsgToChannel(ctx context.Context, uid, channelId, msgType uint32, content string, msg []byte) protocol.ServerError {
	in := &pb.PushMsgToChannelReq{
		BaseReq:   &pb.BaseReq{Appid: uint32(0)},
		Uid:       uid,
		ChannelId: channelId,
		Type:      msgType,
		Data:      msg,
	}
	r, err := c.typedStub().PushMsgToChannel(ctx, in)
	if err != nil {
		return protocol.ToServerError(err)
	}

	if r.GetBaseResp().GetRet() != 0 {
		return protocol.NewServerError(int(r.GetBaseResp().GetRet()), r.GetBaseResp().GetErrMsg())
	}
	return nil
}

func (c *Client) PushCommonBreakingNews(ctx context.Context, uid uint32, msg []byte) protocol.ServerError {
	in := &pb.PushCommonBreakingNewsReq{
		BaseReq:            &pb.BaseReq{Appid: uint32(0)},
		OpUid:              uid,
		BreakNewsPbContent: msg,
	}
	r, err := c.typedStub().PushCommonBreakingNews(ctx, in)
	if err != nil {
		return protocol.ToServerError(err)
	}

	if r.GetBaseResp().GetRet() != 0 {
		return protocol.NewServerError(int(r.GetBaseResp().GetRet()), r.GetBaseResp().GetErrMsg())
	}
	return nil
}

func (c *Client) NotifyGrowInfoSync(ctx context.Context, uid uint32) protocol.ServerError {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uid))))
	_, err := c.typedStub().NotifyGrowInfoSync(ctx, &pb.NotifyGrowInfoSyncReq{Uid: uid})
	return protocol.ToServerError(err)
}

func (c *Client) GuildInfoUpdateSync(ctx context.Context, uid uint32, in *pb.GuildInfoUpdateSyncReq) (*pb.GuildInfoUpdateSyncResp, protocol.ServerError) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uid))))
	r, err := c.typedStub().GuildInfoUpdateSync(ctx, in)
	if err != nil {
		return nil, protocol.ToServerError(err)
	}
	return r, nil
}

func (c *Client) BanUser(ctx context.Context, uid uint32, in *pb.BanUserReq) (*pb.BanUserResp, protocol.ServerError) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uid))))
	r, err := c.typedStub().BanUser(ctx, in)

	return r, protocol.ToServerError(err)
}
