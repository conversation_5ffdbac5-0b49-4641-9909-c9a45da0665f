// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: interactive.proto

#define INTERNAL_SUPPRESS_PROTOBUF_FIELD_DEPRECATION
#include "interactive.pb.h"

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/port.h>
#include <google/protobuf/stubs/once.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)

namespace ugc {
namespace interactive {
class StoreMsgReqDefaultTypeInternal : public ::google::protobuf::internal::ExplicitlyConstructed<StoreMsgReq> {
} _StoreMsgReq_default_instance_;
class StoreMsgRespDefaultTypeInternal : public ::google::protobuf::internal::ExplicitlyConstructed<StoreMsgResp> {
} _StoreMsgResp_default_instance_;
class CommentMsgDefaultTypeInternal : public ::google::protobuf::internal::ExplicitlyConstructed<CommentMsg> {
} _CommentMsg_default_instance_;
class AttitudeMsgDefaultTypeInternal : public ::google::protobuf::internal::ExplicitlyConstructed<AttitudeMsg> {
} _AttitudeMsg_default_instance_;
class InteractiveMsgDefaultTypeInternal : public ::google::protobuf::internal::ExplicitlyConstructed<InteractiveMsg> {
} _InteractiveMsg_default_instance_;
class FollowingStreamUpdateInfoDefaultTypeInternal : public ::google::protobuf::internal::ExplicitlyConstructed<FollowingStreamUpdateInfo> {
} _FollowingStreamUpdateInfo_default_instance_;
class UinfoDefaultTypeInternal : public ::google::protobuf::internal::ExplicitlyConstructed<Uinfo> {
} _Uinfo_default_instance_;
class GetInteractiveListReqDefaultTypeInternal : public ::google::protobuf::internal::ExplicitlyConstructed<GetInteractiveListReq> {
} _GetInteractiveListReq_default_instance_;
class GetInteractiveListRespDefaultTypeInternal : public ::google::protobuf::internal::ExplicitlyConstructed<GetInteractiveListResp> {
} _GetInteractiveListResp_default_instance_;
class DeleteMsgReqDefaultTypeInternal : public ::google::protobuf::internal::ExplicitlyConstructed<DeleteMsgReq> {
} _DeleteMsgReq_default_instance_;
class DeleteMsgRespDefaultTypeInternal : public ::google::protobuf::internal::ExplicitlyConstructed<DeleteMsgResp> {
} _DeleteMsgResp_default_instance_;
class MarkReadReqDefaultTypeInternal : public ::google::protobuf::internal::ExplicitlyConstructed<MarkReadReq> {
} _MarkReadReq_default_instance_;
class MarkReadRespDefaultTypeInternal : public ::google::protobuf::internal::ExplicitlyConstructed<MarkReadResp> {
} _MarkReadResp_default_instance_;
class GetUinfoReqDefaultTypeInternal : public ::google::protobuf::internal::ExplicitlyConstructed<GetUinfoReq> {
} _GetUinfoReq_default_instance_;
class GetUinfoRespDefaultTypeInternal : public ::google::protobuf::internal::ExplicitlyConstructed<GetUinfoResp> {
} _GetUinfoResp_default_instance_;
class ClearNewFollowCountReqDefaultTypeInternal : public ::google::protobuf::internal::ExplicitlyConstructed<ClearNewFollowCountReq> {
} _ClearNewFollowCountReq_default_instance_;
class ClearNewFollowCountRespDefaultTypeInternal : public ::google::protobuf::internal::ExplicitlyConstructed<ClearNewFollowCountResp> {
} _ClearNewFollowCountResp_default_instance_;
class AddFollowCountReqDefaultTypeInternal : public ::google::protobuf::internal::ExplicitlyConstructed<AddFollowCountReq> {
} _AddFollowCountReq_default_instance_;
class AddFollowCountRespDefaultTypeInternal : public ::google::protobuf::internal::ExplicitlyConstructed<AddFollowCountResp> {
} _AddFollowCountResp_default_instance_;
class UpdateFollowingStreamReqDefaultTypeInternal : public ::google::protobuf::internal::ExplicitlyConstructed<UpdateFollowingStreamReq> {
} _UpdateFollowingStreamReq_default_instance_;
class UpdateFollowingStreamRespDefaultTypeInternal : public ::google::protobuf::internal::ExplicitlyConstructed<UpdateFollowingStreamResp> {
} _UpdateFollowingStreamResp_default_instance_;
class AddUnreadCountsReqDefaultTypeInternal : public ::google::protobuf::internal::ExplicitlyConstructed<AddUnreadCountsReq> {
} _AddUnreadCountsReq_default_instance_;
class AddUnreadCountsRespDefaultTypeInternal : public ::google::protobuf::internal::ExplicitlyConstructed<AddUnreadCountsResp> {
} _AddUnreadCountsResp_default_instance_;

namespace protobuf_interactive_2eproto {


namespace {

::google::protobuf::Metadata file_level_metadata[23];
const ::google::protobuf::EnumDescriptor* file_level_enum_descriptors[1];

}  // namespace

const ::google::protobuf::uint32 TableStruct::offsets[] = {
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(StoreMsgReq, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(StoreMsgReq, user_id_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(StoreMsgReq, msgs_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(StoreMsgResp, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(CommentMsg, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(CommentMsg, post_object_id_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(CommentMsg, comment_object_id_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(CommentMsg, comment_id_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(AttitudeMsg, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(AttitudeMsg, post_object_id_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(AttitudeMsg, comment_object_id_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(AttitudeMsg, attitude_type_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(InteractiveMsg, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(InteractiveMsg, id_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(InteractiveMsg, time_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(InteractiveMsg, user_id_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(InteractiveMsg, from_user_id_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(InteractiveMsg, type_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(InteractiveMsg, comment_msg_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(InteractiveMsg, attitude_msg_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(FollowingStreamUpdateInfo, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(FollowingStreamUpdateInfo, update_at_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(FollowingStreamUpdateInfo, latest_actors_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(Uinfo, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(Uinfo, user_id_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(Uinfo, last_read_id_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(Uinfo, new_attitude_count_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(Uinfo, new_comment_count_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(Uinfo, new_follow_count_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(Uinfo, unread_sequence_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(Uinfo, following_stream_update_info_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(GetInteractiveListReq, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(GetInteractiveListReq, user_id_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(GetInteractiveListReq, last_id_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(GetInteractiveListReq, limit_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(GetInteractiveListResp, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(GetInteractiveListResp, info_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(GetInteractiveListResp, last_id_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(DeleteMsgReq, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(DeleteMsgReq, user_id_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(DeleteMsgReq, ids_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(DeleteMsgResp, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MarkReadReq, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MarkReadReq, user_id_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MarkReadReq, id_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MarkReadResp, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(GetUinfoReq, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(GetUinfoReq, user_id_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(GetUinfoResp, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(GetUinfoResp, uinfo_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ClearNewFollowCountReq, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ClearNewFollowCountReq, user_id_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ClearNewFollowCountResp, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(AddFollowCountReq, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(AddFollowCountReq, user_id_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(AddFollowCountReq, count_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(AddFollowCountResp, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(UpdateFollowingStreamReq, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(UpdateFollowingStreamReq, uid_list_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(UpdateFollowingStreamReq, update_info_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(UpdateFollowingStreamResp, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(AddUnreadCountsReq, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(AddUnreadCountsReq, user_id_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(AddUnreadCountsReq, new_comment_count_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(AddUnreadCountsReq, new_attitude_count_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(AddUnreadCountsReq, new_follower_count_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(AddUnreadCountsResp, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
};

static const ::google::protobuf::internal::MigrationSchema schemas[] = {
  { 0, -1, sizeof(StoreMsgReq)},
  { 6, -1, sizeof(StoreMsgResp)},
  { 10, -1, sizeof(CommentMsg)},
  { 17, -1, sizeof(AttitudeMsg)},
  { 24, -1, sizeof(InteractiveMsg)},
  { 35, -1, sizeof(FollowingStreamUpdateInfo)},
  { 41, -1, sizeof(Uinfo)},
  { 52, -1, sizeof(GetInteractiveListReq)},
  { 59, -1, sizeof(GetInteractiveListResp)},
  { 65, -1, sizeof(DeleteMsgReq)},
  { 71, -1, sizeof(DeleteMsgResp)},
  { 75, -1, sizeof(MarkReadReq)},
  { 81, -1, sizeof(MarkReadResp)},
  { 85, -1, sizeof(GetUinfoReq)},
  { 90, -1, sizeof(GetUinfoResp)},
  { 95, -1, sizeof(ClearNewFollowCountReq)},
  { 100, -1, sizeof(ClearNewFollowCountResp)},
  { 104, -1, sizeof(AddFollowCountReq)},
  { 110, -1, sizeof(AddFollowCountResp)},
  { 114, -1, sizeof(UpdateFollowingStreamReq)},
  { 120, -1, sizeof(UpdateFollowingStreamResp)},
  { 124, -1, sizeof(AddUnreadCountsReq)},
  { 132, -1, sizeof(AddUnreadCountsResp)},
};

static ::google::protobuf::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::google::protobuf::Message*>(&_StoreMsgReq_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&_StoreMsgResp_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&_CommentMsg_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&_AttitudeMsg_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&_InteractiveMsg_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&_FollowingStreamUpdateInfo_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&_Uinfo_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&_GetInteractiveListReq_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&_GetInteractiveListResp_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&_DeleteMsgReq_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&_DeleteMsgResp_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&_MarkReadReq_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&_MarkReadResp_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&_GetUinfoReq_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&_GetUinfoResp_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&_ClearNewFollowCountReq_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&_ClearNewFollowCountResp_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&_AddFollowCountReq_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&_AddFollowCountResp_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&_UpdateFollowingStreamReq_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&_UpdateFollowingStreamResp_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&_AddUnreadCountsReq_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&_AddUnreadCountsResp_default_instance_),
};

namespace {

void protobuf_AssignDescriptors() {
  AddDescriptors();
  ::google::protobuf::MessageFactory* factory = NULL;
  AssignDescriptors(
      "interactive.proto", schemas, file_default_instances, TableStruct::offsets, factory,
      file_level_metadata, file_level_enum_descriptors, NULL);
}

void protobuf_AssignDescriptorsOnce() {
  static GOOGLE_PROTOBUF_DECLARE_ONCE(once);
  ::google::protobuf::GoogleOnceInit(&once, &protobuf_AssignDescriptors);
}

void protobuf_RegisterTypes(const ::std::string&) GOOGLE_ATTRIBUTE_COLD;
void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::internal::RegisterAllTypes(file_level_metadata, 23);
}

}  // namespace

void TableStruct::Shutdown() {
  _StoreMsgReq_default_instance_.Shutdown();
  delete file_level_metadata[0].reflection;
  _StoreMsgResp_default_instance_.Shutdown();
  delete file_level_metadata[1].reflection;
  _CommentMsg_default_instance_.Shutdown();
  delete file_level_metadata[2].reflection;
  _AttitudeMsg_default_instance_.Shutdown();
  delete file_level_metadata[3].reflection;
  _InteractiveMsg_default_instance_.Shutdown();
  delete file_level_metadata[4].reflection;
  _FollowingStreamUpdateInfo_default_instance_.Shutdown();
  delete file_level_metadata[5].reflection;
  _Uinfo_default_instance_.Shutdown();
  delete file_level_metadata[6].reflection;
  _GetInteractiveListReq_default_instance_.Shutdown();
  delete file_level_metadata[7].reflection;
  _GetInteractiveListResp_default_instance_.Shutdown();
  delete file_level_metadata[8].reflection;
  _DeleteMsgReq_default_instance_.Shutdown();
  delete file_level_metadata[9].reflection;
  _DeleteMsgResp_default_instance_.Shutdown();
  delete file_level_metadata[10].reflection;
  _MarkReadReq_default_instance_.Shutdown();
  delete file_level_metadata[11].reflection;
  _MarkReadResp_default_instance_.Shutdown();
  delete file_level_metadata[12].reflection;
  _GetUinfoReq_default_instance_.Shutdown();
  delete file_level_metadata[13].reflection;
  _GetUinfoResp_default_instance_.Shutdown();
  delete file_level_metadata[14].reflection;
  _ClearNewFollowCountReq_default_instance_.Shutdown();
  delete file_level_metadata[15].reflection;
  _ClearNewFollowCountResp_default_instance_.Shutdown();
  delete file_level_metadata[16].reflection;
  _AddFollowCountReq_default_instance_.Shutdown();
  delete file_level_metadata[17].reflection;
  _AddFollowCountResp_default_instance_.Shutdown();
  delete file_level_metadata[18].reflection;
  _UpdateFollowingStreamReq_default_instance_.Shutdown();
  delete file_level_metadata[19].reflection;
  _UpdateFollowingStreamResp_default_instance_.Shutdown();
  delete file_level_metadata[20].reflection;
  _AddUnreadCountsReq_default_instance_.Shutdown();
  delete file_level_metadata[21].reflection;
  _AddUnreadCountsResp_default_instance_.Shutdown();
  delete file_level_metadata[22].reflection;
}

void TableStruct::InitDefaultsImpl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  ::google::protobuf::internal::InitProtobufDefaults();
  _StoreMsgReq_default_instance_.DefaultConstruct();
  _StoreMsgResp_default_instance_.DefaultConstruct();
  _CommentMsg_default_instance_.DefaultConstruct();
  _AttitudeMsg_default_instance_.DefaultConstruct();
  _InteractiveMsg_default_instance_.DefaultConstruct();
  _FollowingStreamUpdateInfo_default_instance_.DefaultConstruct();
  _Uinfo_default_instance_.DefaultConstruct();
  _GetInteractiveListReq_default_instance_.DefaultConstruct();
  _GetInteractiveListResp_default_instance_.DefaultConstruct();
  _DeleteMsgReq_default_instance_.DefaultConstruct();
  _DeleteMsgResp_default_instance_.DefaultConstruct();
  _MarkReadReq_default_instance_.DefaultConstruct();
  _MarkReadResp_default_instance_.DefaultConstruct();
  _GetUinfoReq_default_instance_.DefaultConstruct();
  _GetUinfoResp_default_instance_.DefaultConstruct();
  _ClearNewFollowCountReq_default_instance_.DefaultConstruct();
  _ClearNewFollowCountResp_default_instance_.DefaultConstruct();
  _AddFollowCountReq_default_instance_.DefaultConstruct();
  _AddFollowCountResp_default_instance_.DefaultConstruct();
  _UpdateFollowingStreamReq_default_instance_.DefaultConstruct();
  _UpdateFollowingStreamResp_default_instance_.DefaultConstruct();
  _AddUnreadCountsReq_default_instance_.DefaultConstruct();
  _AddUnreadCountsResp_default_instance_.DefaultConstruct();
  _InteractiveMsg_default_instance_.get_mutable()->comment_msg_ = const_cast< ::ugc::interactive::CommentMsg*>(
      ::ugc::interactive::CommentMsg::internal_default_instance());
  _InteractiveMsg_default_instance_.get_mutable()->attitude_msg_ = const_cast< ::ugc::interactive::AttitudeMsg*>(
      ::ugc::interactive::AttitudeMsg::internal_default_instance());
  _Uinfo_default_instance_.get_mutable()->following_stream_update_info_ = const_cast< ::ugc::interactive::FollowingStreamUpdateInfo*>(
      ::ugc::interactive::FollowingStreamUpdateInfo::internal_default_instance());
  _GetUinfoResp_default_instance_.get_mutable()->uinfo_ = const_cast< ::ugc::interactive::Uinfo*>(
      ::ugc::interactive::Uinfo::internal_default_instance());
  _UpdateFollowingStreamReq_default_instance_.get_mutable()->update_info_ = const_cast< ::ugc::interactive::FollowingStreamUpdateInfo*>(
      ::ugc::interactive::FollowingStreamUpdateInfo::internal_default_instance());
}

void InitDefaults() {
  static GOOGLE_PROTOBUF_DECLARE_ONCE(once);
  ::google::protobuf::GoogleOnceInit(&once, &TableStruct::InitDefaultsImpl);
}
void AddDescriptorsImpl() {
  InitDefaults();
  static const char descriptor[] = {
      "\n\021interactive.proto\022\017ugc.interactive\"M\n\013"
      "StoreMsgReq\022\017\n\007user_id\030\001 \001(\r\022-\n\004msgs\030\002 \003"
      "(\0132\037.ugc.interactive.InteractiveMsg\"\016\n\014S"
      "toreMsgResp\"S\n\nCommentMsg\022\026\n\016post_object"
      "_id\030\001 \001(\t\022\031\n\021comment_object_id\030\002 \001(\t\022\022\n\n"
      "comment_id\030\003 \001(\t\"W\n\013AttitudeMsg\022\026\n\016post_"
      "object_id\030\001 \001(\t\022\031\n\021comment_object_id\030\002 \001"
      "(\t\022\025\n\rattitude_type\030\003 \001(\005\"\337\001\n\016Interactiv"
      "eMsg\022\n\n\002id\030\001 \001(\t\022\014\n\004time\030\002 \001(\003\022\017\n\007user_i"
      "d\030\003 \001(\r\022\024\n\014from_user_id\030\004 \001(\r\022&\n\004type\030\005 "
      "\001(\0162\030.ugc.interactive.MsgType\0220\n\013comment"
      "_msg\030\006 \001(\0132\033.ugc.interactive.CommentMsg\022"
      "2\n\014attitude_msg\030\007 \001(\0132\034.ugc.interactive."
      "AttitudeMsg\"E\n\031FollowingStreamUpdateInfo"
      "\022\021\n\tupdate_at\030\001 \001(\004\022\025\n\rlatest_actors\030\002 \003"
      "(\r\"\352\001\n\005Uinfo\022\017\n\007user_id\030\001 \001(\r\022\024\n\014last_re"
      "ad_id\030\002 \001(\t\022\032\n\022new_attitude_count\030\003 \001(\005\022"
      "\031\n\021new_comment_count\030\004 \001(\005\022\030\n\020new_follow"
      "_count\030\005 \001(\005\022\027\n\017unread_sequence\030\006 \001(\r\022P\n"
      "\034following_stream_update_info\030\007 \001(\0132*.ug"
      "c.interactive.FollowingStreamUpdateInfo\""
      "H\n\025GetInteractiveListReq\022\017\n\007user_id\030\001 \001("
      "\r\022\017\n\007last_id\030\002 \001(\t\022\r\n\005limit\030\003 \001(\005\"X\n\026Get"
      "InteractiveListResp\022-\n\004info\030\001 \003(\0132\037.ugc."
      "interactive.InteractiveMsg\022\017\n\007last_id\030\002 "
      "\001(\t\",\n\014DeleteMsgReq\022\017\n\007user_id\030\001 \001(\r\022\013\n\003"
      "ids\030\002 \003(\t\"\017\n\rDeleteMsgResp\"*\n\013MarkReadRe"
      "q\022\017\n\007user_id\030\001 \001(\r\022\n\n\002id\030\002 \001(\t\"\016\n\014MarkRe"
      "adResp\"\036\n\013GetUinfoReq\022\017\n\007user_id\030\001 \001(\r\"5"
      "\n\014GetUinfoResp\022%\n\005uinfo\030\001 \001(\0132\026.ugc.inte"
      "ractive.Uinfo\")\n\026ClearNewFollowCountReq\022"
      "\017\n\007user_id\030\001 \001(\r\"\031\n\027ClearNewFollowCountR"
      "esp\"3\n\021AddFollowCountReq\022\017\n\007user_id\030\001 \001("
      "\r\022\r\n\005count\030\002 \001(\r\"\024\n\022AddFollowCountResp\"m"
      "\n\030UpdateFollowingStreamReq\022\020\n\010uid_list\030\001"
      " \003(\r\022\?\n\013update_info\030\002 \001(\0132*.ugc.interact"
      "ive.FollowingStreamUpdateInfo\"\033\n\031UpdateF"
      "ollowingStreamResp\"x\n\022AddUnreadCountsReq"
      "\022\017\n\007user_id\030\001 \001(\r\022\031\n\021new_comment_count\030\002"
      " \001(\005\022\032\n\022new_attitude_count\030\003 \001(\005\022\032\n\022new_"
      "follower_count\030\004 \001(\005\"\025\n\023AddUnreadCountsR"
      "esp*<\n\007MsgType\022\016\n\nNEW_FOLLOW\020\000\022\020\n\014NEW_AT"
      "TITUDE\020\001\022\017\n\013NEW_COMMENT\020\0022\271\006\n\013Interactiv"
      "e\022G\n\010MarkRead\022\034.ugc.interactive.MarkRead"
      "Req\032\035.ugc.interactive.MarkReadResp\022R\n\023Ge"
      "tInteractiveUinfo\022\034.ugc.interactive.GetU"
      "infoReq\032\035.ugc.interactive.GetUinfoResp\022h"
      "\n\023ClearNewFollowCount\022\'.ugc.interactive."
      "ClearNewFollowCountReq\032(.ugc.interactive"
      ".ClearNewFollowCountResp\022Y\n\016AddFollowCou"
      "nt\022\".ugc.interactive.AddFollowCountReq\032#"
      ".ugc.interactive.AddFollowCountResp\022\\\n\017A"
      "ddUnreadCounts\022#.ugc.interactive.AddUnre"
      "adCountsReq\032$.ugc.interactive.AddUnreadC"
      "ountsResp\022n\n\025UpdateFollowingStream\022).ugc"
      ".interactive.UpdateFollowingStreamReq\032*."
      "ugc.interactive.UpdateFollowingStreamRes"
      "p\022e\n\022GetInteractiveList\022&.ugc.interactiv"
      "e.GetInteractiveListReq\032\'.ugc.interactiv"
      "e.GetInteractiveListResp\022G\n\010StoreMsg\022\034.u"
      "gc.interactive.StoreMsgReq\032\035.ugc.interac"
      "tive.StoreMsgResp\022J\n\tDeleteMsg\022\035.ugc.int"
      "eractive.DeleteMsgReq\032\036.ugc.interactive."
      "DeleteMsgRespB-Z+golang.52tt.com/services/ugc/pro"
      "tocol/go/interactiveb\006proto3"
  };
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
      descriptor, 2588);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "interactive.proto", &protobuf_RegisterTypes);
  ::google::protobuf::internal::OnShutdown(&TableStruct::Shutdown);
}

void AddDescriptors() {
  static GOOGLE_PROTOBUF_DECLARE_ONCE(once);
  ::google::protobuf::GoogleOnceInit(&once, &AddDescriptorsImpl);
}
// Force AddDescriptors() to be called at static initialization time.
struct StaticDescriptorInitializer {
  StaticDescriptorInitializer() {
    AddDescriptors();
  }
} static_descriptor_initializer;

}  // namespace protobuf_interactive_2eproto

const ::google::protobuf::EnumDescriptor* MsgType_descriptor() {
  protobuf_interactive_2eproto::protobuf_AssignDescriptorsOnce();
  return protobuf_interactive_2eproto::file_level_enum_descriptors[0];
}
bool MsgType_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
      return true;
    default:
      return false;
  }
}


// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int StoreMsgReq::kUserIdFieldNumber;
const int StoreMsgReq::kMsgsFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

StoreMsgReq::StoreMsgReq()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (GOOGLE_PREDICT_TRUE(this != internal_default_instance())) {
    protobuf_interactive_2eproto::InitDefaults();
  }
  SharedCtor();
  // @@protoc_insertion_point(constructor:ugc.interactive.StoreMsgReq)
}
StoreMsgReq::StoreMsgReq(const StoreMsgReq& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      msgs_(from.msgs_),
      _cached_size_(0) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  user_id_ = from.user_id_;
  // @@protoc_insertion_point(copy_constructor:ugc.interactive.StoreMsgReq)
}

void StoreMsgReq::SharedCtor() {
  user_id_ = 0u;
  _cached_size_ = 0;
}

StoreMsgReq::~StoreMsgReq() {
  // @@protoc_insertion_point(destructor:ugc.interactive.StoreMsgReq)
  SharedDtor();
}

void StoreMsgReq::SharedDtor() {
}

void StoreMsgReq::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* StoreMsgReq::descriptor() {
  protobuf_interactive_2eproto::protobuf_AssignDescriptorsOnce();
  return protobuf_interactive_2eproto::file_level_metadata[0].descriptor;
}

const StoreMsgReq& StoreMsgReq::default_instance() {
  protobuf_interactive_2eproto::InitDefaults();
  return *internal_default_instance();
}

StoreMsgReq* StoreMsgReq::New(::google::protobuf::Arena* arena) const {
  StoreMsgReq* n = new StoreMsgReq;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void StoreMsgReq::Clear() {
// @@protoc_insertion_point(message_clear_start:ugc.interactive.StoreMsgReq)
  msgs_.Clear();
  user_id_ = 0u;
}

bool StoreMsgReq::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:ugc.interactive.StoreMsgReq)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // uint32 user_id = 1;
      case 1: {
        if (tag == 8u) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_UINT32>(
                 input, &user_id_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated .ugc.interactive.InteractiveMsg msgs = 2;
      case 2: {
        if (tag == 18u) {
          DO_(input->IncrementRecursionDepth());
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtualNoRecursionDepth(
                input, add_msgs()));
        } else {
          goto handle_unusual;
        }
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:ugc.interactive.StoreMsgReq)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:ugc.interactive.StoreMsgReq)
  return false;
#undef DO_
}

void StoreMsgReq::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:ugc.interactive.StoreMsgReq)
  // uint32 user_id = 1;
  if (this->user_id() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt32(1, this->user_id(), output);
  }

  // repeated .ugc.interactive.InteractiveMsg msgs = 2;
  for (unsigned int i = 0, n = this->msgs_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      2, this->msgs(i), output);
  }

  // @@protoc_insertion_point(serialize_end:ugc.interactive.StoreMsgReq)
}

::google::protobuf::uint8* StoreMsgReq::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic;  // Unused
  // @@protoc_insertion_point(serialize_to_array_start:ugc.interactive.StoreMsgReq)
  // uint32 user_id = 1;
  if (this->user_id() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt32ToArray(1, this->user_id(), target);
  }

  // repeated .ugc.interactive.InteractiveMsg msgs = 2;
  for (unsigned int i = 0, n = this->msgs_size(); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        2, this->msgs(i), false, target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:ugc.interactive.StoreMsgReq)
  return target;
}

size_t StoreMsgReq::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:ugc.interactive.StoreMsgReq)
  size_t total_size = 0;

  // repeated .ugc.interactive.InteractiveMsg msgs = 2;
  {
    unsigned int count = this->msgs_size();
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->msgs(i));
    }
  }

  // uint32 user_id = 1;
  if (this->user_id() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::UInt32Size(
        this->user_id());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void StoreMsgReq::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:ugc.interactive.StoreMsgReq)
  GOOGLE_DCHECK_NE(&from, this);
  const StoreMsgReq* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const StoreMsgReq>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:ugc.interactive.StoreMsgReq)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:ugc.interactive.StoreMsgReq)
    MergeFrom(*source);
  }
}

void StoreMsgReq::MergeFrom(const StoreMsgReq& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:ugc.interactive.StoreMsgReq)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  msgs_.MergeFrom(from.msgs_);
  if (from.user_id() != 0) {
    set_user_id(from.user_id());
  }
}

void StoreMsgReq::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:ugc.interactive.StoreMsgReq)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void StoreMsgReq::CopyFrom(const StoreMsgReq& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:ugc.interactive.StoreMsgReq)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool StoreMsgReq::IsInitialized() const {
  return true;
}

void StoreMsgReq::Swap(StoreMsgReq* other) {
  if (other == this) return;
  InternalSwap(other);
}
void StoreMsgReq::InternalSwap(StoreMsgReq* other) {
  msgs_.UnsafeArenaSwap(&other->msgs_);
  std::swap(user_id_, other->user_id_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata StoreMsgReq::GetMetadata() const {
  protobuf_interactive_2eproto::protobuf_AssignDescriptorsOnce();
  return protobuf_interactive_2eproto::file_level_metadata[0];
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// StoreMsgReq

// uint32 user_id = 1;
void StoreMsgReq::clear_user_id() {
  user_id_ = 0u;
}
::google::protobuf::uint32 StoreMsgReq::user_id() const {
  // @@protoc_insertion_point(field_get:ugc.interactive.StoreMsgReq.user_id)
  return user_id_;
}
void StoreMsgReq::set_user_id(::google::protobuf::uint32 value) {
  
  user_id_ = value;
  // @@protoc_insertion_point(field_set:ugc.interactive.StoreMsgReq.user_id)
}

// repeated .ugc.interactive.InteractiveMsg msgs = 2;
int StoreMsgReq::msgs_size() const {
  return msgs_.size();
}
void StoreMsgReq::clear_msgs() {
  msgs_.Clear();
}
const ::ugc::interactive::InteractiveMsg& StoreMsgReq::msgs(int index) const {
  // @@protoc_insertion_point(field_get:ugc.interactive.StoreMsgReq.msgs)
  return msgs_.Get(index);
}
::ugc::interactive::InteractiveMsg* StoreMsgReq::mutable_msgs(int index) {
  // @@protoc_insertion_point(field_mutable:ugc.interactive.StoreMsgReq.msgs)
  return msgs_.Mutable(index);
}
::ugc::interactive::InteractiveMsg* StoreMsgReq::add_msgs() {
  // @@protoc_insertion_point(field_add:ugc.interactive.StoreMsgReq.msgs)
  return msgs_.Add();
}
::google::protobuf::RepeatedPtrField< ::ugc::interactive::InteractiveMsg >*
StoreMsgReq::mutable_msgs() {
  // @@protoc_insertion_point(field_mutable_list:ugc.interactive.StoreMsgReq.msgs)
  return &msgs_;
}
const ::google::protobuf::RepeatedPtrField< ::ugc::interactive::InteractiveMsg >&
StoreMsgReq::msgs() const {
  // @@protoc_insertion_point(field_list:ugc.interactive.StoreMsgReq.msgs)
  return msgs_;
}

#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

StoreMsgResp::StoreMsgResp()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (GOOGLE_PREDICT_TRUE(this != internal_default_instance())) {
    protobuf_interactive_2eproto::InitDefaults();
  }
  SharedCtor();
  // @@protoc_insertion_point(constructor:ugc.interactive.StoreMsgResp)
}
StoreMsgResp::StoreMsgResp(const StoreMsgResp& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      _cached_size_(0) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:ugc.interactive.StoreMsgResp)
}

void StoreMsgResp::SharedCtor() {
  _cached_size_ = 0;
}

StoreMsgResp::~StoreMsgResp() {
  // @@protoc_insertion_point(destructor:ugc.interactive.StoreMsgResp)
  SharedDtor();
}

void StoreMsgResp::SharedDtor() {
}

void StoreMsgResp::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* StoreMsgResp::descriptor() {
  protobuf_interactive_2eproto::protobuf_AssignDescriptorsOnce();
  return protobuf_interactive_2eproto::file_level_metadata[1].descriptor;
}

const StoreMsgResp& StoreMsgResp::default_instance() {
  protobuf_interactive_2eproto::InitDefaults();
  return *internal_default_instance();
}

StoreMsgResp* StoreMsgResp::New(::google::protobuf::Arena* arena) const {
  StoreMsgResp* n = new StoreMsgResp;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void StoreMsgResp::Clear() {
// @@protoc_insertion_point(message_clear_start:ugc.interactive.StoreMsgResp)
}

bool StoreMsgResp::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:ugc.interactive.StoreMsgResp)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
  handle_unusual:
    if (tag == 0 ||
        ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
        ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
      goto success;
    }
    DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
  }
success:
  // @@protoc_insertion_point(parse_success:ugc.interactive.StoreMsgResp)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:ugc.interactive.StoreMsgResp)
  return false;
#undef DO_
}

void StoreMsgResp::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:ugc.interactive.StoreMsgResp)
  // @@protoc_insertion_point(serialize_end:ugc.interactive.StoreMsgResp)
}

::google::protobuf::uint8* StoreMsgResp::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic;  // Unused
  // @@protoc_insertion_point(serialize_to_array_start:ugc.interactive.StoreMsgResp)
  // @@protoc_insertion_point(serialize_to_array_end:ugc.interactive.StoreMsgResp)
  return target;
}

size_t StoreMsgResp::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:ugc.interactive.StoreMsgResp)
  size_t total_size = 0;

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void StoreMsgResp::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:ugc.interactive.StoreMsgResp)
  GOOGLE_DCHECK_NE(&from, this);
  const StoreMsgResp* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const StoreMsgResp>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:ugc.interactive.StoreMsgResp)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:ugc.interactive.StoreMsgResp)
    MergeFrom(*source);
  }
}

void StoreMsgResp::MergeFrom(const StoreMsgResp& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:ugc.interactive.StoreMsgResp)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
}

void StoreMsgResp::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:ugc.interactive.StoreMsgResp)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void StoreMsgResp::CopyFrom(const StoreMsgResp& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:ugc.interactive.StoreMsgResp)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool StoreMsgResp::IsInitialized() const {
  return true;
}

void StoreMsgResp::Swap(StoreMsgResp* other) {
  if (other == this) return;
  InternalSwap(other);
}
void StoreMsgResp::InternalSwap(StoreMsgResp* other) {
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata StoreMsgResp::GetMetadata() const {
  protobuf_interactive_2eproto::protobuf_AssignDescriptorsOnce();
  return protobuf_interactive_2eproto::file_level_metadata[1];
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// StoreMsgResp

#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int CommentMsg::kPostObjectIdFieldNumber;
const int CommentMsg::kCommentObjectIdFieldNumber;
const int CommentMsg::kCommentIdFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

CommentMsg::CommentMsg()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (GOOGLE_PREDICT_TRUE(this != internal_default_instance())) {
    protobuf_interactive_2eproto::InitDefaults();
  }
  SharedCtor();
  // @@protoc_insertion_point(constructor:ugc.interactive.CommentMsg)
}
CommentMsg::CommentMsg(const CommentMsg& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      _cached_size_(0) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  post_object_id_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.post_object_id().size() > 0) {
    post_object_id_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.post_object_id_);
  }
  comment_object_id_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.comment_object_id().size() > 0) {
    comment_object_id_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.comment_object_id_);
  }
  comment_id_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.comment_id().size() > 0) {
    comment_id_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.comment_id_);
  }
  // @@protoc_insertion_point(copy_constructor:ugc.interactive.CommentMsg)
}

void CommentMsg::SharedCtor() {
  post_object_id_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  comment_object_id_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  comment_id_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  _cached_size_ = 0;
}

CommentMsg::~CommentMsg() {
  // @@protoc_insertion_point(destructor:ugc.interactive.CommentMsg)
  SharedDtor();
}

void CommentMsg::SharedDtor() {
  post_object_id_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  comment_object_id_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  comment_id_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void CommentMsg::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* CommentMsg::descriptor() {
  protobuf_interactive_2eproto::protobuf_AssignDescriptorsOnce();
  return protobuf_interactive_2eproto::file_level_metadata[2].descriptor;
}

const CommentMsg& CommentMsg::default_instance() {
  protobuf_interactive_2eproto::InitDefaults();
  return *internal_default_instance();
}

CommentMsg* CommentMsg::New(::google::protobuf::Arena* arena) const {
  CommentMsg* n = new CommentMsg;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void CommentMsg::Clear() {
// @@protoc_insertion_point(message_clear_start:ugc.interactive.CommentMsg)
  post_object_id_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  comment_object_id_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  comment_id_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

bool CommentMsg::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:ugc.interactive.CommentMsg)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // string post_object_id = 1;
      case 1: {
        if (tag == 10u) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_post_object_id()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->post_object_id().data(), this->post_object_id().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "ugc.interactive.CommentMsg.post_object_id"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string comment_object_id = 2;
      case 2: {
        if (tag == 18u) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_comment_object_id()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->comment_object_id().data(), this->comment_object_id().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "ugc.interactive.CommentMsg.comment_object_id"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string comment_id = 3;
      case 3: {
        if (tag == 26u) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_comment_id()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->comment_id().data(), this->comment_id().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "ugc.interactive.CommentMsg.comment_id"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:ugc.interactive.CommentMsg)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:ugc.interactive.CommentMsg)
  return false;
#undef DO_
}

void CommentMsg::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:ugc.interactive.CommentMsg)
  // string post_object_id = 1;
  if (this->post_object_id().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->post_object_id().data(), this->post_object_id().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "ugc.interactive.CommentMsg.post_object_id");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->post_object_id(), output);
  }

  // string comment_object_id = 2;
  if (this->comment_object_id().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->comment_object_id().data(), this->comment_object_id().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "ugc.interactive.CommentMsg.comment_object_id");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      2, this->comment_object_id(), output);
  }

  // string comment_id = 3;
  if (this->comment_id().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->comment_id().data(), this->comment_id().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "ugc.interactive.CommentMsg.comment_id");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      3, this->comment_id(), output);
  }

  // @@protoc_insertion_point(serialize_end:ugc.interactive.CommentMsg)
}

::google::protobuf::uint8* CommentMsg::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic;  // Unused
  // @@protoc_insertion_point(serialize_to_array_start:ugc.interactive.CommentMsg)
  // string post_object_id = 1;
  if (this->post_object_id().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->post_object_id().data(), this->post_object_id().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "ugc.interactive.CommentMsg.post_object_id");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->post_object_id(), target);
  }

  // string comment_object_id = 2;
  if (this->comment_object_id().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->comment_object_id().data(), this->comment_object_id().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "ugc.interactive.CommentMsg.comment_object_id");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        2, this->comment_object_id(), target);
  }

  // string comment_id = 3;
  if (this->comment_id().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->comment_id().data(), this->comment_id().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "ugc.interactive.CommentMsg.comment_id");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        3, this->comment_id(), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:ugc.interactive.CommentMsg)
  return target;
}

size_t CommentMsg::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:ugc.interactive.CommentMsg)
  size_t total_size = 0;

  // string post_object_id = 1;
  if (this->post_object_id().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->post_object_id());
  }

  // string comment_object_id = 2;
  if (this->comment_object_id().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->comment_object_id());
  }

  // string comment_id = 3;
  if (this->comment_id().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->comment_id());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void CommentMsg::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:ugc.interactive.CommentMsg)
  GOOGLE_DCHECK_NE(&from, this);
  const CommentMsg* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const CommentMsg>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:ugc.interactive.CommentMsg)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:ugc.interactive.CommentMsg)
    MergeFrom(*source);
  }
}

void CommentMsg::MergeFrom(const CommentMsg& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:ugc.interactive.CommentMsg)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  if (from.post_object_id().size() > 0) {

    post_object_id_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.post_object_id_);
  }
  if (from.comment_object_id().size() > 0) {

    comment_object_id_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.comment_object_id_);
  }
  if (from.comment_id().size() > 0) {

    comment_id_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.comment_id_);
  }
}

void CommentMsg::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:ugc.interactive.CommentMsg)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void CommentMsg::CopyFrom(const CommentMsg& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:ugc.interactive.CommentMsg)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool CommentMsg::IsInitialized() const {
  return true;
}

void CommentMsg::Swap(CommentMsg* other) {
  if (other == this) return;
  InternalSwap(other);
}
void CommentMsg::InternalSwap(CommentMsg* other) {
  post_object_id_.Swap(&other->post_object_id_);
  comment_object_id_.Swap(&other->comment_object_id_);
  comment_id_.Swap(&other->comment_id_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata CommentMsg::GetMetadata() const {
  protobuf_interactive_2eproto::protobuf_AssignDescriptorsOnce();
  return protobuf_interactive_2eproto::file_level_metadata[2];
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// CommentMsg

// string post_object_id = 1;
void CommentMsg::clear_post_object_id() {
  post_object_id_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& CommentMsg::post_object_id() const {
  // @@protoc_insertion_point(field_get:ugc.interactive.CommentMsg.post_object_id)
  return post_object_id_.GetNoArena();
}
void CommentMsg::set_post_object_id(const ::std::string& value) {
  
  post_object_id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:ugc.interactive.CommentMsg.post_object_id)
}
#if LANG_CXX11
void CommentMsg::set_post_object_id(::std::string&& value) {
  
  post_object_id_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:ugc.interactive.CommentMsg.post_object_id)
}
#endif
void CommentMsg::set_post_object_id(const char* value) {
  
  post_object_id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:ugc.interactive.CommentMsg.post_object_id)
}
void CommentMsg::set_post_object_id(const char* value, size_t size) {
  
  post_object_id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:ugc.interactive.CommentMsg.post_object_id)
}
::std::string* CommentMsg::mutable_post_object_id() {
  
  // @@protoc_insertion_point(field_mutable:ugc.interactive.CommentMsg.post_object_id)
  return post_object_id_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* CommentMsg::release_post_object_id() {
  // @@protoc_insertion_point(field_release:ugc.interactive.CommentMsg.post_object_id)
  
  return post_object_id_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void CommentMsg::set_allocated_post_object_id(::std::string* post_object_id) {
  if (post_object_id != NULL) {
    
  } else {
    
  }
  post_object_id_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), post_object_id);
  // @@protoc_insertion_point(field_set_allocated:ugc.interactive.CommentMsg.post_object_id)
}

// string comment_object_id = 2;
void CommentMsg::clear_comment_object_id() {
  comment_object_id_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& CommentMsg::comment_object_id() const {
  // @@protoc_insertion_point(field_get:ugc.interactive.CommentMsg.comment_object_id)
  return comment_object_id_.GetNoArena();
}
void CommentMsg::set_comment_object_id(const ::std::string& value) {
  
  comment_object_id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:ugc.interactive.CommentMsg.comment_object_id)
}
#if LANG_CXX11
void CommentMsg::set_comment_object_id(::std::string&& value) {
  
  comment_object_id_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:ugc.interactive.CommentMsg.comment_object_id)
}
#endif
void CommentMsg::set_comment_object_id(const char* value) {
  
  comment_object_id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:ugc.interactive.CommentMsg.comment_object_id)
}
void CommentMsg::set_comment_object_id(const char* value, size_t size) {
  
  comment_object_id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:ugc.interactive.CommentMsg.comment_object_id)
}
::std::string* CommentMsg::mutable_comment_object_id() {
  
  // @@protoc_insertion_point(field_mutable:ugc.interactive.CommentMsg.comment_object_id)
  return comment_object_id_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* CommentMsg::release_comment_object_id() {
  // @@protoc_insertion_point(field_release:ugc.interactive.CommentMsg.comment_object_id)
  
  return comment_object_id_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void CommentMsg::set_allocated_comment_object_id(::std::string* comment_object_id) {
  if (comment_object_id != NULL) {
    
  } else {
    
  }
  comment_object_id_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), comment_object_id);
  // @@protoc_insertion_point(field_set_allocated:ugc.interactive.CommentMsg.comment_object_id)
}

// string comment_id = 3;
void CommentMsg::clear_comment_id() {
  comment_id_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& CommentMsg::comment_id() const {
  // @@protoc_insertion_point(field_get:ugc.interactive.CommentMsg.comment_id)
  return comment_id_.GetNoArena();
}
void CommentMsg::set_comment_id(const ::std::string& value) {
  
  comment_id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:ugc.interactive.CommentMsg.comment_id)
}
#if LANG_CXX11
void CommentMsg::set_comment_id(::std::string&& value) {
  
  comment_id_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:ugc.interactive.CommentMsg.comment_id)
}
#endif
void CommentMsg::set_comment_id(const char* value) {
  
  comment_id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:ugc.interactive.CommentMsg.comment_id)
}
void CommentMsg::set_comment_id(const char* value, size_t size) {
  
  comment_id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:ugc.interactive.CommentMsg.comment_id)
}
::std::string* CommentMsg::mutable_comment_id() {
  
  // @@protoc_insertion_point(field_mutable:ugc.interactive.CommentMsg.comment_id)
  return comment_id_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* CommentMsg::release_comment_id() {
  // @@protoc_insertion_point(field_release:ugc.interactive.CommentMsg.comment_id)
  
  return comment_id_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void CommentMsg::set_allocated_comment_id(::std::string* comment_id) {
  if (comment_id != NULL) {
    
  } else {
    
  }
  comment_id_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), comment_id);
  // @@protoc_insertion_point(field_set_allocated:ugc.interactive.CommentMsg.comment_id)
}

#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int AttitudeMsg::kPostObjectIdFieldNumber;
const int AttitudeMsg::kCommentObjectIdFieldNumber;
const int AttitudeMsg::kAttitudeTypeFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

AttitudeMsg::AttitudeMsg()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (GOOGLE_PREDICT_TRUE(this != internal_default_instance())) {
    protobuf_interactive_2eproto::InitDefaults();
  }
  SharedCtor();
  // @@protoc_insertion_point(constructor:ugc.interactive.AttitudeMsg)
}
AttitudeMsg::AttitudeMsg(const AttitudeMsg& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      _cached_size_(0) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  post_object_id_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.post_object_id().size() > 0) {
    post_object_id_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.post_object_id_);
  }
  comment_object_id_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.comment_object_id().size() > 0) {
    comment_object_id_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.comment_object_id_);
  }
  attitude_type_ = from.attitude_type_;
  // @@protoc_insertion_point(copy_constructor:ugc.interactive.AttitudeMsg)
}

void AttitudeMsg::SharedCtor() {
  post_object_id_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  comment_object_id_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  attitude_type_ = 0;
  _cached_size_ = 0;
}

AttitudeMsg::~AttitudeMsg() {
  // @@protoc_insertion_point(destructor:ugc.interactive.AttitudeMsg)
  SharedDtor();
}

void AttitudeMsg::SharedDtor() {
  post_object_id_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  comment_object_id_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void AttitudeMsg::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* AttitudeMsg::descriptor() {
  protobuf_interactive_2eproto::protobuf_AssignDescriptorsOnce();
  return protobuf_interactive_2eproto::file_level_metadata[3].descriptor;
}

const AttitudeMsg& AttitudeMsg::default_instance() {
  protobuf_interactive_2eproto::InitDefaults();
  return *internal_default_instance();
}

AttitudeMsg* AttitudeMsg::New(::google::protobuf::Arena* arena) const {
  AttitudeMsg* n = new AttitudeMsg;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void AttitudeMsg::Clear() {
// @@protoc_insertion_point(message_clear_start:ugc.interactive.AttitudeMsg)
  post_object_id_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  comment_object_id_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  attitude_type_ = 0;
}

bool AttitudeMsg::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:ugc.interactive.AttitudeMsg)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // string post_object_id = 1;
      case 1: {
        if (tag == 10u) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_post_object_id()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->post_object_id().data(), this->post_object_id().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "ugc.interactive.AttitudeMsg.post_object_id"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string comment_object_id = 2;
      case 2: {
        if (tag == 18u) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_comment_object_id()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->comment_object_id().data(), this->comment_object_id().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "ugc.interactive.AttitudeMsg.comment_object_id"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int32 attitude_type = 3;
      case 3: {
        if (tag == 24u) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &attitude_type_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:ugc.interactive.AttitudeMsg)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:ugc.interactive.AttitudeMsg)
  return false;
#undef DO_
}

void AttitudeMsg::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:ugc.interactive.AttitudeMsg)
  // string post_object_id = 1;
  if (this->post_object_id().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->post_object_id().data(), this->post_object_id().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "ugc.interactive.AttitudeMsg.post_object_id");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->post_object_id(), output);
  }

  // string comment_object_id = 2;
  if (this->comment_object_id().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->comment_object_id().data(), this->comment_object_id().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "ugc.interactive.AttitudeMsg.comment_object_id");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      2, this->comment_object_id(), output);
  }

  // int32 attitude_type = 3;
  if (this->attitude_type() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(3, this->attitude_type(), output);
  }

  // @@protoc_insertion_point(serialize_end:ugc.interactive.AttitudeMsg)
}

::google::protobuf::uint8* AttitudeMsg::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic;  // Unused
  // @@protoc_insertion_point(serialize_to_array_start:ugc.interactive.AttitudeMsg)
  // string post_object_id = 1;
  if (this->post_object_id().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->post_object_id().data(), this->post_object_id().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "ugc.interactive.AttitudeMsg.post_object_id");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->post_object_id(), target);
  }

  // string comment_object_id = 2;
  if (this->comment_object_id().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->comment_object_id().data(), this->comment_object_id().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "ugc.interactive.AttitudeMsg.comment_object_id");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        2, this->comment_object_id(), target);
  }

  // int32 attitude_type = 3;
  if (this->attitude_type() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(3, this->attitude_type(), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:ugc.interactive.AttitudeMsg)
  return target;
}

size_t AttitudeMsg::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:ugc.interactive.AttitudeMsg)
  size_t total_size = 0;

  // string post_object_id = 1;
  if (this->post_object_id().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->post_object_id());
  }

  // string comment_object_id = 2;
  if (this->comment_object_id().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->comment_object_id());
  }

  // int32 attitude_type = 3;
  if (this->attitude_type() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->attitude_type());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void AttitudeMsg::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:ugc.interactive.AttitudeMsg)
  GOOGLE_DCHECK_NE(&from, this);
  const AttitudeMsg* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const AttitudeMsg>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:ugc.interactive.AttitudeMsg)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:ugc.interactive.AttitudeMsg)
    MergeFrom(*source);
  }
}

void AttitudeMsg::MergeFrom(const AttitudeMsg& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:ugc.interactive.AttitudeMsg)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  if (from.post_object_id().size() > 0) {

    post_object_id_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.post_object_id_);
  }
  if (from.comment_object_id().size() > 0) {

    comment_object_id_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.comment_object_id_);
  }
  if (from.attitude_type() != 0) {
    set_attitude_type(from.attitude_type());
  }
}

void AttitudeMsg::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:ugc.interactive.AttitudeMsg)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void AttitudeMsg::CopyFrom(const AttitudeMsg& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:ugc.interactive.AttitudeMsg)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool AttitudeMsg::IsInitialized() const {
  return true;
}

void AttitudeMsg::Swap(AttitudeMsg* other) {
  if (other == this) return;
  InternalSwap(other);
}
void AttitudeMsg::InternalSwap(AttitudeMsg* other) {
  post_object_id_.Swap(&other->post_object_id_);
  comment_object_id_.Swap(&other->comment_object_id_);
  std::swap(attitude_type_, other->attitude_type_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata AttitudeMsg::GetMetadata() const {
  protobuf_interactive_2eproto::protobuf_AssignDescriptorsOnce();
  return protobuf_interactive_2eproto::file_level_metadata[3];
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// AttitudeMsg

// string post_object_id = 1;
void AttitudeMsg::clear_post_object_id() {
  post_object_id_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& AttitudeMsg::post_object_id() const {
  // @@protoc_insertion_point(field_get:ugc.interactive.AttitudeMsg.post_object_id)
  return post_object_id_.GetNoArena();
}
void AttitudeMsg::set_post_object_id(const ::std::string& value) {
  
  post_object_id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:ugc.interactive.AttitudeMsg.post_object_id)
}
#if LANG_CXX11
void AttitudeMsg::set_post_object_id(::std::string&& value) {
  
  post_object_id_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:ugc.interactive.AttitudeMsg.post_object_id)
}
#endif
void AttitudeMsg::set_post_object_id(const char* value) {
  
  post_object_id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:ugc.interactive.AttitudeMsg.post_object_id)
}
void AttitudeMsg::set_post_object_id(const char* value, size_t size) {
  
  post_object_id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:ugc.interactive.AttitudeMsg.post_object_id)
}
::std::string* AttitudeMsg::mutable_post_object_id() {
  
  // @@protoc_insertion_point(field_mutable:ugc.interactive.AttitudeMsg.post_object_id)
  return post_object_id_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* AttitudeMsg::release_post_object_id() {
  // @@protoc_insertion_point(field_release:ugc.interactive.AttitudeMsg.post_object_id)
  
  return post_object_id_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void AttitudeMsg::set_allocated_post_object_id(::std::string* post_object_id) {
  if (post_object_id != NULL) {
    
  } else {
    
  }
  post_object_id_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), post_object_id);
  // @@protoc_insertion_point(field_set_allocated:ugc.interactive.AttitudeMsg.post_object_id)
}

// string comment_object_id = 2;
void AttitudeMsg::clear_comment_object_id() {
  comment_object_id_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& AttitudeMsg::comment_object_id() const {
  // @@protoc_insertion_point(field_get:ugc.interactive.AttitudeMsg.comment_object_id)
  return comment_object_id_.GetNoArena();
}
void AttitudeMsg::set_comment_object_id(const ::std::string& value) {
  
  comment_object_id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:ugc.interactive.AttitudeMsg.comment_object_id)
}
#if LANG_CXX11
void AttitudeMsg::set_comment_object_id(::std::string&& value) {
  
  comment_object_id_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:ugc.interactive.AttitudeMsg.comment_object_id)
}
#endif
void AttitudeMsg::set_comment_object_id(const char* value) {
  
  comment_object_id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:ugc.interactive.AttitudeMsg.comment_object_id)
}
void AttitudeMsg::set_comment_object_id(const char* value, size_t size) {
  
  comment_object_id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:ugc.interactive.AttitudeMsg.comment_object_id)
}
::std::string* AttitudeMsg::mutable_comment_object_id() {
  
  // @@protoc_insertion_point(field_mutable:ugc.interactive.AttitudeMsg.comment_object_id)
  return comment_object_id_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* AttitudeMsg::release_comment_object_id() {
  // @@protoc_insertion_point(field_release:ugc.interactive.AttitudeMsg.comment_object_id)
  
  return comment_object_id_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void AttitudeMsg::set_allocated_comment_object_id(::std::string* comment_object_id) {
  if (comment_object_id != NULL) {
    
  } else {
    
  }
  comment_object_id_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), comment_object_id);
  // @@protoc_insertion_point(field_set_allocated:ugc.interactive.AttitudeMsg.comment_object_id)
}

// int32 attitude_type = 3;
void AttitudeMsg::clear_attitude_type() {
  attitude_type_ = 0;
}
::google::protobuf::int32 AttitudeMsg::attitude_type() const {
  // @@protoc_insertion_point(field_get:ugc.interactive.AttitudeMsg.attitude_type)
  return attitude_type_;
}
void AttitudeMsg::set_attitude_type(::google::protobuf::int32 value) {
  
  attitude_type_ = value;
  // @@protoc_insertion_point(field_set:ugc.interactive.AttitudeMsg.attitude_type)
}

#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int InteractiveMsg::kIdFieldNumber;
const int InteractiveMsg::kTimeFieldNumber;
const int InteractiveMsg::kUserIdFieldNumber;
const int InteractiveMsg::kFromUserIdFieldNumber;
const int InteractiveMsg::kTypeFieldNumber;
const int InteractiveMsg::kCommentMsgFieldNumber;
const int InteractiveMsg::kAttitudeMsgFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

InteractiveMsg::InteractiveMsg()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (GOOGLE_PREDICT_TRUE(this != internal_default_instance())) {
    protobuf_interactive_2eproto::InitDefaults();
  }
  SharedCtor();
  // @@protoc_insertion_point(constructor:ugc.interactive.InteractiveMsg)
}
InteractiveMsg::InteractiveMsg(const InteractiveMsg& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      _cached_size_(0) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  id_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.id().size() > 0) {
    id_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.id_);
  }
  if (from.has_comment_msg()) {
    comment_msg_ = new ::ugc::interactive::CommentMsg(*from.comment_msg_);
  } else {
    comment_msg_ = NULL;
  }
  if (from.has_attitude_msg()) {
    attitude_msg_ = new ::ugc::interactive::AttitudeMsg(*from.attitude_msg_);
  } else {
    attitude_msg_ = NULL;
  }
  ::memcpy(&time_, &from.time_,
    reinterpret_cast<char*>(&type_) -
    reinterpret_cast<char*>(&time_) + sizeof(type_));
  // @@protoc_insertion_point(copy_constructor:ugc.interactive.InteractiveMsg)
}

void InteractiveMsg::SharedCtor() {
  id_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&comment_msg_, 0, reinterpret_cast<char*>(&type_) -
    reinterpret_cast<char*>(&comment_msg_) + sizeof(type_));
  _cached_size_ = 0;
}

InteractiveMsg::~InteractiveMsg() {
  // @@protoc_insertion_point(destructor:ugc.interactive.InteractiveMsg)
  SharedDtor();
}

void InteractiveMsg::SharedDtor() {
  id_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) {
    delete comment_msg_;
  }
  if (this != internal_default_instance()) {
    delete attitude_msg_;
  }
}

void InteractiveMsg::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* InteractiveMsg::descriptor() {
  protobuf_interactive_2eproto::protobuf_AssignDescriptorsOnce();
  return protobuf_interactive_2eproto::file_level_metadata[4].descriptor;
}

const InteractiveMsg& InteractiveMsg::default_instance() {
  protobuf_interactive_2eproto::InitDefaults();
  return *internal_default_instance();
}

InteractiveMsg* InteractiveMsg::New(::google::protobuf::Arena* arena) const {
  InteractiveMsg* n = new InteractiveMsg;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void InteractiveMsg::Clear() {
// @@protoc_insertion_point(message_clear_start:ugc.interactive.InteractiveMsg)
  id_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (GetArenaNoVirtual() == NULL && comment_msg_ != NULL) {
    delete comment_msg_;
  }
  comment_msg_ = NULL;
  if (GetArenaNoVirtual() == NULL && attitude_msg_ != NULL) {
    delete attitude_msg_;
  }
  attitude_msg_ = NULL;
  ::memset(&time_, 0, reinterpret_cast<char*>(&type_) -
    reinterpret_cast<char*>(&time_) + sizeof(type_));
}

bool InteractiveMsg::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:ugc.interactive.InteractiveMsg)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // string id = 1;
      case 1: {
        if (tag == 10u) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_id()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->id().data(), this->id().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "ugc.interactive.InteractiveMsg.id"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 time = 2;
      case 2: {
        if (tag == 16u) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &time_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // uint32 user_id = 3;
      case 3: {
        if (tag == 24u) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_UINT32>(
                 input, &user_id_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // uint32 from_user_id = 4;
      case 4: {
        if (tag == 32u) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_UINT32>(
                 input, &from_user_id_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .ugc.interactive.MsgType type = 5;
      case 5: {
        if (tag == 40u) {
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_type(static_cast< ::ugc::interactive::MsgType >(value));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .ugc.interactive.CommentMsg comment_msg = 6;
      case 6: {
        if (tag == 50u) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_comment_msg()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .ugc.interactive.AttitudeMsg attitude_msg = 7;
      case 7: {
        if (tag == 58u) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_attitude_msg()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:ugc.interactive.InteractiveMsg)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:ugc.interactive.InteractiveMsg)
  return false;
#undef DO_
}

void InteractiveMsg::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:ugc.interactive.InteractiveMsg)
  // string id = 1;
  if (this->id().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->id().data(), this->id().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "ugc.interactive.InteractiveMsg.id");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->id(), output);
  }

  // int64 time = 2;
  if (this->time() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(2, this->time(), output);
  }

  // uint32 user_id = 3;
  if (this->user_id() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt32(3, this->user_id(), output);
  }

  // uint32 from_user_id = 4;
  if (this->from_user_id() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt32(4, this->from_user_id(), output);
  }

  // .ugc.interactive.MsgType type = 5;
  if (this->type() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      5, this->type(), output);
  }

  // .ugc.interactive.CommentMsg comment_msg = 6;
  if (this->has_comment_msg()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      6, *this->comment_msg_, output);
  }

  // .ugc.interactive.AttitudeMsg attitude_msg = 7;
  if (this->has_attitude_msg()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      7, *this->attitude_msg_, output);
  }

  // @@protoc_insertion_point(serialize_end:ugc.interactive.InteractiveMsg)
}

::google::protobuf::uint8* InteractiveMsg::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic;  // Unused
  // @@protoc_insertion_point(serialize_to_array_start:ugc.interactive.InteractiveMsg)
  // string id = 1;
  if (this->id().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->id().data(), this->id().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "ugc.interactive.InteractiveMsg.id");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->id(), target);
  }

  // int64 time = 2;
  if (this->time() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(2, this->time(), target);
  }

  // uint32 user_id = 3;
  if (this->user_id() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt32ToArray(3, this->user_id(), target);
  }

  // uint32 from_user_id = 4;
  if (this->from_user_id() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt32ToArray(4, this->from_user_id(), target);
  }

  // .ugc.interactive.MsgType type = 5;
  if (this->type() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      5, this->type(), target);
  }

  // .ugc.interactive.CommentMsg comment_msg = 6;
  if (this->has_comment_msg()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        6, *this->comment_msg_, false, target);
  }

  // .ugc.interactive.AttitudeMsg attitude_msg = 7;
  if (this->has_attitude_msg()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        7, *this->attitude_msg_, false, target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:ugc.interactive.InteractiveMsg)
  return target;
}

size_t InteractiveMsg::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:ugc.interactive.InteractiveMsg)
  size_t total_size = 0;

  // string id = 1;
  if (this->id().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->id());
  }

  // .ugc.interactive.CommentMsg comment_msg = 6;
  if (this->has_comment_msg()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->comment_msg_);
  }

  // .ugc.interactive.AttitudeMsg attitude_msg = 7;
  if (this->has_attitude_msg()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->attitude_msg_);
  }

  // int64 time = 2;
  if (this->time() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->time());
  }

  // uint32 user_id = 3;
  if (this->user_id() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::UInt32Size(
        this->user_id());
  }

  // uint32 from_user_id = 4;
  if (this->from_user_id() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::UInt32Size(
        this->from_user_id());
  }

  // .ugc.interactive.MsgType type = 5;
  if (this->type() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->type());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void InteractiveMsg::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:ugc.interactive.InteractiveMsg)
  GOOGLE_DCHECK_NE(&from, this);
  const InteractiveMsg* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const InteractiveMsg>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:ugc.interactive.InteractiveMsg)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:ugc.interactive.InteractiveMsg)
    MergeFrom(*source);
  }
}

void InteractiveMsg::MergeFrom(const InteractiveMsg& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:ugc.interactive.InteractiveMsg)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  if (from.id().size() > 0) {

    id_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.id_);
  }
  if (from.has_comment_msg()) {
    mutable_comment_msg()->::ugc::interactive::CommentMsg::MergeFrom(from.comment_msg());
  }
  if (from.has_attitude_msg()) {
    mutable_attitude_msg()->::ugc::interactive::AttitudeMsg::MergeFrom(from.attitude_msg());
  }
  if (from.time() != 0) {
    set_time(from.time());
  }
  if (from.user_id() != 0) {
    set_user_id(from.user_id());
  }
  if (from.from_user_id() != 0) {
    set_from_user_id(from.from_user_id());
  }
  if (from.type() != 0) {
    set_type(from.type());
  }
}

void InteractiveMsg::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:ugc.interactive.InteractiveMsg)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void InteractiveMsg::CopyFrom(const InteractiveMsg& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:ugc.interactive.InteractiveMsg)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool InteractiveMsg::IsInitialized() const {
  return true;
}

void InteractiveMsg::Swap(InteractiveMsg* other) {
  if (other == this) return;
  InternalSwap(other);
}
void InteractiveMsg::InternalSwap(InteractiveMsg* other) {
  id_.Swap(&other->id_);
  std::swap(comment_msg_, other->comment_msg_);
  std::swap(attitude_msg_, other->attitude_msg_);
  std::swap(time_, other->time_);
  std::swap(user_id_, other->user_id_);
  std::swap(from_user_id_, other->from_user_id_);
  std::swap(type_, other->type_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata InteractiveMsg::GetMetadata() const {
  protobuf_interactive_2eproto::protobuf_AssignDescriptorsOnce();
  return protobuf_interactive_2eproto::file_level_metadata[4];
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// InteractiveMsg

// string id = 1;
void InteractiveMsg::clear_id() {
  id_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& InteractiveMsg::id() const {
  // @@protoc_insertion_point(field_get:ugc.interactive.InteractiveMsg.id)
  return id_.GetNoArena();
}
void InteractiveMsg::set_id(const ::std::string& value) {
  
  id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:ugc.interactive.InteractiveMsg.id)
}
#if LANG_CXX11
void InteractiveMsg::set_id(::std::string&& value) {
  
  id_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:ugc.interactive.InteractiveMsg.id)
}
#endif
void InteractiveMsg::set_id(const char* value) {
  
  id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:ugc.interactive.InteractiveMsg.id)
}
void InteractiveMsg::set_id(const char* value, size_t size) {
  
  id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:ugc.interactive.InteractiveMsg.id)
}
::std::string* InteractiveMsg::mutable_id() {
  
  // @@protoc_insertion_point(field_mutable:ugc.interactive.InteractiveMsg.id)
  return id_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* InteractiveMsg::release_id() {
  // @@protoc_insertion_point(field_release:ugc.interactive.InteractiveMsg.id)
  
  return id_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void InteractiveMsg::set_allocated_id(::std::string* id) {
  if (id != NULL) {
    
  } else {
    
  }
  id_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), id);
  // @@protoc_insertion_point(field_set_allocated:ugc.interactive.InteractiveMsg.id)
}

// int64 time = 2;
void InteractiveMsg::clear_time() {
  time_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 InteractiveMsg::time() const {
  // @@protoc_insertion_point(field_get:ugc.interactive.InteractiveMsg.time)
  return time_;
}
void InteractiveMsg::set_time(::google::protobuf::int64 value) {
  
  time_ = value;
  // @@protoc_insertion_point(field_set:ugc.interactive.InteractiveMsg.time)
}

// uint32 user_id = 3;
void InteractiveMsg::clear_user_id() {
  user_id_ = 0u;
}
::google::protobuf::uint32 InteractiveMsg::user_id() const {
  // @@protoc_insertion_point(field_get:ugc.interactive.InteractiveMsg.user_id)
  return user_id_;
}
void InteractiveMsg::set_user_id(::google::protobuf::uint32 value) {
  
  user_id_ = value;
  // @@protoc_insertion_point(field_set:ugc.interactive.InteractiveMsg.user_id)
}

// uint32 from_user_id = 4;
void InteractiveMsg::clear_from_user_id() {
  from_user_id_ = 0u;
}
::google::protobuf::uint32 InteractiveMsg::from_user_id() const {
  // @@protoc_insertion_point(field_get:ugc.interactive.InteractiveMsg.from_user_id)
  return from_user_id_;
}
void InteractiveMsg::set_from_user_id(::google::protobuf::uint32 value) {
  
  from_user_id_ = value;
  // @@protoc_insertion_point(field_set:ugc.interactive.InteractiveMsg.from_user_id)
}

// .ugc.interactive.MsgType type = 5;
void InteractiveMsg::clear_type() {
  type_ = 0;
}
::ugc::interactive::MsgType InteractiveMsg::type() const {
  // @@protoc_insertion_point(field_get:ugc.interactive.InteractiveMsg.type)
  return static_cast< ::ugc::interactive::MsgType >(type_);
}
void InteractiveMsg::set_type(::ugc::interactive::MsgType value) {
  
  type_ = value;
  // @@protoc_insertion_point(field_set:ugc.interactive.InteractiveMsg.type)
}

// .ugc.interactive.CommentMsg comment_msg = 6;
bool InteractiveMsg::has_comment_msg() const {
  return this != internal_default_instance() && comment_msg_ != NULL;
}
void InteractiveMsg::clear_comment_msg() {
  if (GetArenaNoVirtual() == NULL && comment_msg_ != NULL) delete comment_msg_;
  comment_msg_ = NULL;
}
const ::ugc::interactive::CommentMsg& InteractiveMsg::comment_msg() const {
  // @@protoc_insertion_point(field_get:ugc.interactive.InteractiveMsg.comment_msg)
  return comment_msg_ != NULL ? *comment_msg_
                         : *::ugc::interactive::CommentMsg::internal_default_instance();
}
::ugc::interactive::CommentMsg* InteractiveMsg::mutable_comment_msg() {
  
  if (comment_msg_ == NULL) {
    comment_msg_ = new ::ugc::interactive::CommentMsg;
  }
  // @@protoc_insertion_point(field_mutable:ugc.interactive.InteractiveMsg.comment_msg)
  return comment_msg_;
}
::ugc::interactive::CommentMsg* InteractiveMsg::release_comment_msg() {
  // @@protoc_insertion_point(field_release:ugc.interactive.InteractiveMsg.comment_msg)
  
  ::ugc::interactive::CommentMsg* temp = comment_msg_;
  comment_msg_ = NULL;
  return temp;
}
void InteractiveMsg::set_allocated_comment_msg(::ugc::interactive::CommentMsg* comment_msg) {
  delete comment_msg_;
  comment_msg_ = comment_msg;
  if (comment_msg) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:ugc.interactive.InteractiveMsg.comment_msg)
}

// .ugc.interactive.AttitudeMsg attitude_msg = 7;
bool InteractiveMsg::has_attitude_msg() const {
  return this != internal_default_instance() && attitude_msg_ != NULL;
}
void InteractiveMsg::clear_attitude_msg() {
  if (GetArenaNoVirtual() == NULL && attitude_msg_ != NULL) delete attitude_msg_;
  attitude_msg_ = NULL;
}
const ::ugc::interactive::AttitudeMsg& InteractiveMsg::attitude_msg() const {
  // @@protoc_insertion_point(field_get:ugc.interactive.InteractiveMsg.attitude_msg)
  return attitude_msg_ != NULL ? *attitude_msg_
                         : *::ugc::interactive::AttitudeMsg::internal_default_instance();
}
::ugc::interactive::AttitudeMsg* InteractiveMsg::mutable_attitude_msg() {
  
  if (attitude_msg_ == NULL) {
    attitude_msg_ = new ::ugc::interactive::AttitudeMsg;
  }
  // @@protoc_insertion_point(field_mutable:ugc.interactive.InteractiveMsg.attitude_msg)
  return attitude_msg_;
}
::ugc::interactive::AttitudeMsg* InteractiveMsg::release_attitude_msg() {
  // @@protoc_insertion_point(field_release:ugc.interactive.InteractiveMsg.attitude_msg)
  
  ::ugc::interactive::AttitudeMsg* temp = attitude_msg_;
  attitude_msg_ = NULL;
  return temp;
}
void InteractiveMsg::set_allocated_attitude_msg(::ugc::interactive::AttitudeMsg* attitude_msg) {
  delete attitude_msg_;
  attitude_msg_ = attitude_msg;
  if (attitude_msg) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:ugc.interactive.InteractiveMsg.attitude_msg)
}

#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int FollowingStreamUpdateInfo::kUpdateAtFieldNumber;
const int FollowingStreamUpdateInfo::kLatestActorsFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

FollowingStreamUpdateInfo::FollowingStreamUpdateInfo()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (GOOGLE_PREDICT_TRUE(this != internal_default_instance())) {
    protobuf_interactive_2eproto::InitDefaults();
  }
  SharedCtor();
  // @@protoc_insertion_point(constructor:ugc.interactive.FollowingStreamUpdateInfo)
}
FollowingStreamUpdateInfo::FollowingStreamUpdateInfo(const FollowingStreamUpdateInfo& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      latest_actors_(from.latest_actors_),
      _cached_size_(0) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  update_at_ = from.update_at_;
  // @@protoc_insertion_point(copy_constructor:ugc.interactive.FollowingStreamUpdateInfo)
}

void FollowingStreamUpdateInfo::SharedCtor() {
  update_at_ = GOOGLE_ULONGLONG(0);
  _cached_size_ = 0;
}

FollowingStreamUpdateInfo::~FollowingStreamUpdateInfo() {
  // @@protoc_insertion_point(destructor:ugc.interactive.FollowingStreamUpdateInfo)
  SharedDtor();
}

void FollowingStreamUpdateInfo::SharedDtor() {
}

void FollowingStreamUpdateInfo::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* FollowingStreamUpdateInfo::descriptor() {
  protobuf_interactive_2eproto::protobuf_AssignDescriptorsOnce();
  return protobuf_interactive_2eproto::file_level_metadata[5].descriptor;
}

const FollowingStreamUpdateInfo& FollowingStreamUpdateInfo::default_instance() {
  protobuf_interactive_2eproto::InitDefaults();
  return *internal_default_instance();
}

FollowingStreamUpdateInfo* FollowingStreamUpdateInfo::New(::google::protobuf::Arena* arena) const {
  FollowingStreamUpdateInfo* n = new FollowingStreamUpdateInfo;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void FollowingStreamUpdateInfo::Clear() {
// @@protoc_insertion_point(message_clear_start:ugc.interactive.FollowingStreamUpdateInfo)
  latest_actors_.Clear();
  update_at_ = GOOGLE_ULONGLONG(0);
}

bool FollowingStreamUpdateInfo::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:ugc.interactive.FollowingStreamUpdateInfo)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // uint64 update_at = 1;
      case 1: {
        if (tag == 8u) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_UINT64>(
                 input, &update_at_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated uint32 latest_actors = 2;
      case 2: {
        if (tag == 18u) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_UINT32>(
                 input, this->mutable_latest_actors())));
        } else if (tag == 16u) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_UINT32>(
                 1, 18u, input, this->mutable_latest_actors())));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:ugc.interactive.FollowingStreamUpdateInfo)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:ugc.interactive.FollowingStreamUpdateInfo)
  return false;
#undef DO_
}

void FollowingStreamUpdateInfo::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:ugc.interactive.FollowingStreamUpdateInfo)
  // uint64 update_at = 1;
  if (this->update_at() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt64(1, this->update_at(), output);
  }

  // repeated uint32 latest_actors = 2;
  if (this->latest_actors_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(2, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(_latest_actors_cached_byte_size_);
  }
  for (int i = 0; i < this->latest_actors_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt32NoTag(
      this->latest_actors(i), output);
  }

  // @@protoc_insertion_point(serialize_end:ugc.interactive.FollowingStreamUpdateInfo)
}

::google::protobuf::uint8* FollowingStreamUpdateInfo::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic;  // Unused
  // @@protoc_insertion_point(serialize_to_array_start:ugc.interactive.FollowingStreamUpdateInfo)
  // uint64 update_at = 1;
  if (this->update_at() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt64ToArray(1, this->update_at(), target);
  }

  // repeated uint32 latest_actors = 2;
  if (this->latest_actors_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      2,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
      _latest_actors_cached_byte_size_, target);
  }
  for (int i = 0; i < this->latest_actors_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteUInt32NoTagToArray(this->latest_actors(i), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:ugc.interactive.FollowingStreamUpdateInfo)
  return target;
}

size_t FollowingStreamUpdateInfo::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:ugc.interactive.FollowingStreamUpdateInfo)
  size_t total_size = 0;

  // repeated uint32 latest_actors = 2;
  {
    size_t data_size = ::google::protobuf::internal::WireFormatLite::
      UInt32Size(this->latest_actors_);
    if (data_size > 0) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(data_size);
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _latest_actors_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // uint64 update_at = 1;
  if (this->update_at() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::UInt64Size(
        this->update_at());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void FollowingStreamUpdateInfo::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:ugc.interactive.FollowingStreamUpdateInfo)
  GOOGLE_DCHECK_NE(&from, this);
  const FollowingStreamUpdateInfo* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const FollowingStreamUpdateInfo>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:ugc.interactive.FollowingStreamUpdateInfo)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:ugc.interactive.FollowingStreamUpdateInfo)
    MergeFrom(*source);
  }
}

void FollowingStreamUpdateInfo::MergeFrom(const FollowingStreamUpdateInfo& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:ugc.interactive.FollowingStreamUpdateInfo)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  latest_actors_.MergeFrom(from.latest_actors_);
  if (from.update_at() != 0) {
    set_update_at(from.update_at());
  }
}

void FollowingStreamUpdateInfo::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:ugc.interactive.FollowingStreamUpdateInfo)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void FollowingStreamUpdateInfo::CopyFrom(const FollowingStreamUpdateInfo& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:ugc.interactive.FollowingStreamUpdateInfo)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool FollowingStreamUpdateInfo::IsInitialized() const {
  return true;
}

void FollowingStreamUpdateInfo::Swap(FollowingStreamUpdateInfo* other) {
  if (other == this) return;
  InternalSwap(other);
}
void FollowingStreamUpdateInfo::InternalSwap(FollowingStreamUpdateInfo* other) {
  latest_actors_.UnsafeArenaSwap(&other->latest_actors_);
  std::swap(update_at_, other->update_at_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata FollowingStreamUpdateInfo::GetMetadata() const {
  protobuf_interactive_2eproto::protobuf_AssignDescriptorsOnce();
  return protobuf_interactive_2eproto::file_level_metadata[5];
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// FollowingStreamUpdateInfo

// uint64 update_at = 1;
void FollowingStreamUpdateInfo::clear_update_at() {
  update_at_ = GOOGLE_ULONGLONG(0);
}
::google::protobuf::uint64 FollowingStreamUpdateInfo::update_at() const {
  // @@protoc_insertion_point(field_get:ugc.interactive.FollowingStreamUpdateInfo.update_at)
  return update_at_;
}
void FollowingStreamUpdateInfo::set_update_at(::google::protobuf::uint64 value) {
  
  update_at_ = value;
  // @@protoc_insertion_point(field_set:ugc.interactive.FollowingStreamUpdateInfo.update_at)
}

// repeated uint32 latest_actors = 2;
int FollowingStreamUpdateInfo::latest_actors_size() const {
  return latest_actors_.size();
}
void FollowingStreamUpdateInfo::clear_latest_actors() {
  latest_actors_.Clear();
}
::google::protobuf::uint32 FollowingStreamUpdateInfo::latest_actors(int index) const {
  // @@protoc_insertion_point(field_get:ugc.interactive.FollowingStreamUpdateInfo.latest_actors)
  return latest_actors_.Get(index);
}
void FollowingStreamUpdateInfo::set_latest_actors(int index, ::google::protobuf::uint32 value) {
  latest_actors_.Set(index, value);
  // @@protoc_insertion_point(field_set:ugc.interactive.FollowingStreamUpdateInfo.latest_actors)
}
void FollowingStreamUpdateInfo::add_latest_actors(::google::protobuf::uint32 value) {
  latest_actors_.Add(value);
  // @@protoc_insertion_point(field_add:ugc.interactive.FollowingStreamUpdateInfo.latest_actors)
}
const ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >&
FollowingStreamUpdateInfo::latest_actors() const {
  // @@protoc_insertion_point(field_list:ugc.interactive.FollowingStreamUpdateInfo.latest_actors)
  return latest_actors_;
}
::google::protobuf::RepeatedField< ::google::protobuf::uint32 >*
FollowingStreamUpdateInfo::mutable_latest_actors() {
  // @@protoc_insertion_point(field_mutable_list:ugc.interactive.FollowingStreamUpdateInfo.latest_actors)
  return &latest_actors_;
}

#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int Uinfo::kUserIdFieldNumber;
const int Uinfo::kLastReadIdFieldNumber;
const int Uinfo::kNewAttitudeCountFieldNumber;
const int Uinfo::kNewCommentCountFieldNumber;
const int Uinfo::kNewFollowCountFieldNumber;
const int Uinfo::kUnreadSequenceFieldNumber;
const int Uinfo::kFollowingStreamUpdateInfoFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

Uinfo::Uinfo()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (GOOGLE_PREDICT_TRUE(this != internal_default_instance())) {
    protobuf_interactive_2eproto::InitDefaults();
  }
  SharedCtor();
  // @@protoc_insertion_point(constructor:ugc.interactive.Uinfo)
}
Uinfo::Uinfo(const Uinfo& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      _cached_size_(0) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  last_read_id_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.last_read_id().size() > 0) {
    last_read_id_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.last_read_id_);
  }
  if (from.has_following_stream_update_info()) {
    following_stream_update_info_ = new ::ugc::interactive::FollowingStreamUpdateInfo(*from.following_stream_update_info_);
  } else {
    following_stream_update_info_ = NULL;
  }
  ::memcpy(&user_id_, &from.user_id_,
    reinterpret_cast<char*>(&unread_sequence_) -
    reinterpret_cast<char*>(&user_id_) + sizeof(unread_sequence_));
  // @@protoc_insertion_point(copy_constructor:ugc.interactive.Uinfo)
}

void Uinfo::SharedCtor() {
  last_read_id_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&following_stream_update_info_, 0, reinterpret_cast<char*>(&unread_sequence_) -
    reinterpret_cast<char*>(&following_stream_update_info_) + sizeof(unread_sequence_));
  _cached_size_ = 0;
}

Uinfo::~Uinfo() {
  // @@protoc_insertion_point(destructor:ugc.interactive.Uinfo)
  SharedDtor();
}

void Uinfo::SharedDtor() {
  last_read_id_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) {
    delete following_stream_update_info_;
  }
}

void Uinfo::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* Uinfo::descriptor() {
  protobuf_interactive_2eproto::protobuf_AssignDescriptorsOnce();
  return protobuf_interactive_2eproto::file_level_metadata[6].descriptor;
}

const Uinfo& Uinfo::default_instance() {
  protobuf_interactive_2eproto::InitDefaults();
  return *internal_default_instance();
}

Uinfo* Uinfo::New(::google::protobuf::Arena* arena) const {
  Uinfo* n = new Uinfo;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void Uinfo::Clear() {
// @@protoc_insertion_point(message_clear_start:ugc.interactive.Uinfo)
  last_read_id_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (GetArenaNoVirtual() == NULL && following_stream_update_info_ != NULL) {
    delete following_stream_update_info_;
  }
  following_stream_update_info_ = NULL;
  ::memset(&user_id_, 0, reinterpret_cast<char*>(&unread_sequence_) -
    reinterpret_cast<char*>(&user_id_) + sizeof(unread_sequence_));
}

bool Uinfo::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:ugc.interactive.Uinfo)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // uint32 user_id = 1;
      case 1: {
        if (tag == 8u) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_UINT32>(
                 input, &user_id_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string last_read_id = 2;
      case 2: {
        if (tag == 18u) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_last_read_id()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->last_read_id().data(), this->last_read_id().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "ugc.interactive.Uinfo.last_read_id"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int32 new_attitude_count = 3;
      case 3: {
        if (tag == 24u) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &new_attitude_count_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int32 new_comment_count = 4;
      case 4: {
        if (tag == 32u) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &new_comment_count_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int32 new_follow_count = 5;
      case 5: {
        if (tag == 40u) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &new_follow_count_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // uint32 unread_sequence = 6;
      case 6: {
        if (tag == 48u) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_UINT32>(
                 input, &unread_sequence_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .ugc.interactive.FollowingStreamUpdateInfo following_stream_update_info = 7;
      case 7: {
        if (tag == 58u) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_following_stream_update_info()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:ugc.interactive.Uinfo)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:ugc.interactive.Uinfo)
  return false;
#undef DO_
}

void Uinfo::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:ugc.interactive.Uinfo)
  // uint32 user_id = 1;
  if (this->user_id() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt32(1, this->user_id(), output);
  }

  // string last_read_id = 2;
  if (this->last_read_id().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->last_read_id().data(), this->last_read_id().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "ugc.interactive.Uinfo.last_read_id");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      2, this->last_read_id(), output);
  }

  // int32 new_attitude_count = 3;
  if (this->new_attitude_count() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(3, this->new_attitude_count(), output);
  }

  // int32 new_comment_count = 4;
  if (this->new_comment_count() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(4, this->new_comment_count(), output);
  }

  // int32 new_follow_count = 5;
  if (this->new_follow_count() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(5, this->new_follow_count(), output);
  }

  // uint32 unread_sequence = 6;
  if (this->unread_sequence() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt32(6, this->unread_sequence(), output);
  }

  // .ugc.interactive.FollowingStreamUpdateInfo following_stream_update_info = 7;
  if (this->has_following_stream_update_info()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      7, *this->following_stream_update_info_, output);
  }

  // @@protoc_insertion_point(serialize_end:ugc.interactive.Uinfo)
}

::google::protobuf::uint8* Uinfo::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic;  // Unused
  // @@protoc_insertion_point(serialize_to_array_start:ugc.interactive.Uinfo)
  // uint32 user_id = 1;
  if (this->user_id() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt32ToArray(1, this->user_id(), target);
  }

  // string last_read_id = 2;
  if (this->last_read_id().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->last_read_id().data(), this->last_read_id().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "ugc.interactive.Uinfo.last_read_id");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        2, this->last_read_id(), target);
  }

  // int32 new_attitude_count = 3;
  if (this->new_attitude_count() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(3, this->new_attitude_count(), target);
  }

  // int32 new_comment_count = 4;
  if (this->new_comment_count() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(4, this->new_comment_count(), target);
  }

  // int32 new_follow_count = 5;
  if (this->new_follow_count() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(5, this->new_follow_count(), target);
  }

  // uint32 unread_sequence = 6;
  if (this->unread_sequence() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt32ToArray(6, this->unread_sequence(), target);
  }

  // .ugc.interactive.FollowingStreamUpdateInfo following_stream_update_info = 7;
  if (this->has_following_stream_update_info()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        7, *this->following_stream_update_info_, false, target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:ugc.interactive.Uinfo)
  return target;
}

size_t Uinfo::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:ugc.interactive.Uinfo)
  size_t total_size = 0;

  // string last_read_id = 2;
  if (this->last_read_id().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->last_read_id());
  }

  // .ugc.interactive.FollowingStreamUpdateInfo following_stream_update_info = 7;
  if (this->has_following_stream_update_info()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->following_stream_update_info_);
  }

  // uint32 user_id = 1;
  if (this->user_id() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::UInt32Size(
        this->user_id());
  }

  // int32 new_attitude_count = 3;
  if (this->new_attitude_count() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->new_attitude_count());
  }

  // int32 new_comment_count = 4;
  if (this->new_comment_count() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->new_comment_count());
  }

  // int32 new_follow_count = 5;
  if (this->new_follow_count() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->new_follow_count());
  }

  // uint32 unread_sequence = 6;
  if (this->unread_sequence() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::UInt32Size(
        this->unread_sequence());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void Uinfo::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:ugc.interactive.Uinfo)
  GOOGLE_DCHECK_NE(&from, this);
  const Uinfo* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const Uinfo>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:ugc.interactive.Uinfo)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:ugc.interactive.Uinfo)
    MergeFrom(*source);
  }
}

void Uinfo::MergeFrom(const Uinfo& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:ugc.interactive.Uinfo)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  if (from.last_read_id().size() > 0) {

    last_read_id_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.last_read_id_);
  }
  if (from.has_following_stream_update_info()) {
    mutable_following_stream_update_info()->::ugc::interactive::FollowingStreamUpdateInfo::MergeFrom(from.following_stream_update_info());
  }
  if (from.user_id() != 0) {
    set_user_id(from.user_id());
  }
  if (from.new_attitude_count() != 0) {
    set_new_attitude_count(from.new_attitude_count());
  }
  if (from.new_comment_count() != 0) {
    set_new_comment_count(from.new_comment_count());
  }
  if (from.new_follow_count() != 0) {
    set_new_follow_count(from.new_follow_count());
  }
  if (from.unread_sequence() != 0) {
    set_unread_sequence(from.unread_sequence());
  }
}

void Uinfo::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:ugc.interactive.Uinfo)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void Uinfo::CopyFrom(const Uinfo& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:ugc.interactive.Uinfo)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Uinfo::IsInitialized() const {
  return true;
}

void Uinfo::Swap(Uinfo* other) {
  if (other == this) return;
  InternalSwap(other);
}
void Uinfo::InternalSwap(Uinfo* other) {
  last_read_id_.Swap(&other->last_read_id_);
  std::swap(following_stream_update_info_, other->following_stream_update_info_);
  std::swap(user_id_, other->user_id_);
  std::swap(new_attitude_count_, other->new_attitude_count_);
  std::swap(new_comment_count_, other->new_comment_count_);
  std::swap(new_follow_count_, other->new_follow_count_);
  std::swap(unread_sequence_, other->unread_sequence_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata Uinfo::GetMetadata() const {
  protobuf_interactive_2eproto::protobuf_AssignDescriptorsOnce();
  return protobuf_interactive_2eproto::file_level_metadata[6];
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// Uinfo

// uint32 user_id = 1;
void Uinfo::clear_user_id() {
  user_id_ = 0u;
}
::google::protobuf::uint32 Uinfo::user_id() const {
  // @@protoc_insertion_point(field_get:ugc.interactive.Uinfo.user_id)
  return user_id_;
}
void Uinfo::set_user_id(::google::protobuf::uint32 value) {
  
  user_id_ = value;
  // @@protoc_insertion_point(field_set:ugc.interactive.Uinfo.user_id)
}

// string last_read_id = 2;
void Uinfo::clear_last_read_id() {
  last_read_id_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& Uinfo::last_read_id() const {
  // @@protoc_insertion_point(field_get:ugc.interactive.Uinfo.last_read_id)
  return last_read_id_.GetNoArena();
}
void Uinfo::set_last_read_id(const ::std::string& value) {
  
  last_read_id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:ugc.interactive.Uinfo.last_read_id)
}
#if LANG_CXX11
void Uinfo::set_last_read_id(::std::string&& value) {
  
  last_read_id_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:ugc.interactive.Uinfo.last_read_id)
}
#endif
void Uinfo::set_last_read_id(const char* value) {
  
  last_read_id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:ugc.interactive.Uinfo.last_read_id)
}
void Uinfo::set_last_read_id(const char* value, size_t size) {
  
  last_read_id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:ugc.interactive.Uinfo.last_read_id)
}
::std::string* Uinfo::mutable_last_read_id() {
  
  // @@protoc_insertion_point(field_mutable:ugc.interactive.Uinfo.last_read_id)
  return last_read_id_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* Uinfo::release_last_read_id() {
  // @@protoc_insertion_point(field_release:ugc.interactive.Uinfo.last_read_id)
  
  return last_read_id_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void Uinfo::set_allocated_last_read_id(::std::string* last_read_id) {
  if (last_read_id != NULL) {
    
  } else {
    
  }
  last_read_id_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), last_read_id);
  // @@protoc_insertion_point(field_set_allocated:ugc.interactive.Uinfo.last_read_id)
}

// int32 new_attitude_count = 3;
void Uinfo::clear_new_attitude_count() {
  new_attitude_count_ = 0;
}
::google::protobuf::int32 Uinfo::new_attitude_count() const {
  // @@protoc_insertion_point(field_get:ugc.interactive.Uinfo.new_attitude_count)
  return new_attitude_count_;
}
void Uinfo::set_new_attitude_count(::google::protobuf::int32 value) {
  
  new_attitude_count_ = value;
  // @@protoc_insertion_point(field_set:ugc.interactive.Uinfo.new_attitude_count)
}

// int32 new_comment_count = 4;
void Uinfo::clear_new_comment_count() {
  new_comment_count_ = 0;
}
::google::protobuf::int32 Uinfo::new_comment_count() const {
  // @@protoc_insertion_point(field_get:ugc.interactive.Uinfo.new_comment_count)
  return new_comment_count_;
}
void Uinfo::set_new_comment_count(::google::protobuf::int32 value) {
  
  new_comment_count_ = value;
  // @@protoc_insertion_point(field_set:ugc.interactive.Uinfo.new_comment_count)
}

// int32 new_follow_count = 5;
void Uinfo::clear_new_follow_count() {
  new_follow_count_ = 0;
}
::google::protobuf::int32 Uinfo::new_follow_count() const {
  // @@protoc_insertion_point(field_get:ugc.interactive.Uinfo.new_follow_count)
  return new_follow_count_;
}
void Uinfo::set_new_follow_count(::google::protobuf::int32 value) {
  
  new_follow_count_ = value;
  // @@protoc_insertion_point(field_set:ugc.interactive.Uinfo.new_follow_count)
}

// uint32 unread_sequence = 6;
void Uinfo::clear_unread_sequence() {
  unread_sequence_ = 0u;
}
::google::protobuf::uint32 Uinfo::unread_sequence() const {
  // @@protoc_insertion_point(field_get:ugc.interactive.Uinfo.unread_sequence)
  return unread_sequence_;
}
void Uinfo::set_unread_sequence(::google::protobuf::uint32 value) {
  
  unread_sequence_ = value;
  // @@protoc_insertion_point(field_set:ugc.interactive.Uinfo.unread_sequence)
}

// .ugc.interactive.FollowingStreamUpdateInfo following_stream_update_info = 7;
bool Uinfo::has_following_stream_update_info() const {
  return this != internal_default_instance() && following_stream_update_info_ != NULL;
}
void Uinfo::clear_following_stream_update_info() {
  if (GetArenaNoVirtual() == NULL && following_stream_update_info_ != NULL) delete following_stream_update_info_;
  following_stream_update_info_ = NULL;
}
const ::ugc::interactive::FollowingStreamUpdateInfo& Uinfo::following_stream_update_info() const {
  // @@protoc_insertion_point(field_get:ugc.interactive.Uinfo.following_stream_update_info)
  return following_stream_update_info_ != NULL ? *following_stream_update_info_
                         : *::ugc::interactive::FollowingStreamUpdateInfo::internal_default_instance();
}
::ugc::interactive::FollowingStreamUpdateInfo* Uinfo::mutable_following_stream_update_info() {
  
  if (following_stream_update_info_ == NULL) {
    following_stream_update_info_ = new ::ugc::interactive::FollowingStreamUpdateInfo;
  }
  // @@protoc_insertion_point(field_mutable:ugc.interactive.Uinfo.following_stream_update_info)
  return following_stream_update_info_;
}
::ugc::interactive::FollowingStreamUpdateInfo* Uinfo::release_following_stream_update_info() {
  // @@protoc_insertion_point(field_release:ugc.interactive.Uinfo.following_stream_update_info)
  
  ::ugc::interactive::FollowingStreamUpdateInfo* temp = following_stream_update_info_;
  following_stream_update_info_ = NULL;
  return temp;
}
void Uinfo::set_allocated_following_stream_update_info(::ugc::interactive::FollowingStreamUpdateInfo* following_stream_update_info) {
  delete following_stream_update_info_;
  following_stream_update_info_ = following_stream_update_info;
  if (following_stream_update_info) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:ugc.interactive.Uinfo.following_stream_update_info)
}

#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int GetInteractiveListReq::kUserIdFieldNumber;
const int GetInteractiveListReq::kLastIdFieldNumber;
const int GetInteractiveListReq::kLimitFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

GetInteractiveListReq::GetInteractiveListReq()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (GOOGLE_PREDICT_TRUE(this != internal_default_instance())) {
    protobuf_interactive_2eproto::InitDefaults();
  }
  SharedCtor();
  // @@protoc_insertion_point(constructor:ugc.interactive.GetInteractiveListReq)
}
GetInteractiveListReq::GetInteractiveListReq(const GetInteractiveListReq& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      _cached_size_(0) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  last_id_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.last_id().size() > 0) {
    last_id_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.last_id_);
  }
  ::memcpy(&user_id_, &from.user_id_,
    reinterpret_cast<char*>(&limit_) -
    reinterpret_cast<char*>(&user_id_) + sizeof(limit_));
  // @@protoc_insertion_point(copy_constructor:ugc.interactive.GetInteractiveListReq)
}

void GetInteractiveListReq::SharedCtor() {
  last_id_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&user_id_, 0, reinterpret_cast<char*>(&limit_) -
    reinterpret_cast<char*>(&user_id_) + sizeof(limit_));
  _cached_size_ = 0;
}

GetInteractiveListReq::~GetInteractiveListReq() {
  // @@protoc_insertion_point(destructor:ugc.interactive.GetInteractiveListReq)
  SharedDtor();
}

void GetInteractiveListReq::SharedDtor() {
  last_id_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void GetInteractiveListReq::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* GetInteractiveListReq::descriptor() {
  protobuf_interactive_2eproto::protobuf_AssignDescriptorsOnce();
  return protobuf_interactive_2eproto::file_level_metadata[7].descriptor;
}

const GetInteractiveListReq& GetInteractiveListReq::default_instance() {
  protobuf_interactive_2eproto::InitDefaults();
  return *internal_default_instance();
}

GetInteractiveListReq* GetInteractiveListReq::New(::google::protobuf::Arena* arena) const {
  GetInteractiveListReq* n = new GetInteractiveListReq;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void GetInteractiveListReq::Clear() {
// @@protoc_insertion_point(message_clear_start:ugc.interactive.GetInteractiveListReq)
  last_id_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&user_id_, 0, reinterpret_cast<char*>(&limit_) -
    reinterpret_cast<char*>(&user_id_) + sizeof(limit_));
}

bool GetInteractiveListReq::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:ugc.interactive.GetInteractiveListReq)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // uint32 user_id = 1;
      case 1: {
        if (tag == 8u) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_UINT32>(
                 input, &user_id_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string last_id = 2;
      case 2: {
        if (tag == 18u) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_last_id()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->last_id().data(), this->last_id().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "ugc.interactive.GetInteractiveListReq.last_id"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int32 limit = 3;
      case 3: {
        if (tag == 24u) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &limit_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:ugc.interactive.GetInteractiveListReq)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:ugc.interactive.GetInteractiveListReq)
  return false;
#undef DO_
}

void GetInteractiveListReq::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:ugc.interactive.GetInteractiveListReq)
  // uint32 user_id = 1;
  if (this->user_id() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt32(1, this->user_id(), output);
  }

  // string last_id = 2;
  if (this->last_id().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->last_id().data(), this->last_id().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "ugc.interactive.GetInteractiveListReq.last_id");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      2, this->last_id(), output);
  }

  // int32 limit = 3;
  if (this->limit() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(3, this->limit(), output);
  }

  // @@protoc_insertion_point(serialize_end:ugc.interactive.GetInteractiveListReq)
}

::google::protobuf::uint8* GetInteractiveListReq::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic;  // Unused
  // @@protoc_insertion_point(serialize_to_array_start:ugc.interactive.GetInteractiveListReq)
  // uint32 user_id = 1;
  if (this->user_id() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt32ToArray(1, this->user_id(), target);
  }

  // string last_id = 2;
  if (this->last_id().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->last_id().data(), this->last_id().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "ugc.interactive.GetInteractiveListReq.last_id");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        2, this->last_id(), target);
  }

  // int32 limit = 3;
  if (this->limit() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(3, this->limit(), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:ugc.interactive.GetInteractiveListReq)
  return target;
}

size_t GetInteractiveListReq::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:ugc.interactive.GetInteractiveListReq)
  size_t total_size = 0;

  // string last_id = 2;
  if (this->last_id().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->last_id());
  }

  // uint32 user_id = 1;
  if (this->user_id() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::UInt32Size(
        this->user_id());
  }

  // int32 limit = 3;
  if (this->limit() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->limit());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void GetInteractiveListReq::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:ugc.interactive.GetInteractiveListReq)
  GOOGLE_DCHECK_NE(&from, this);
  const GetInteractiveListReq* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const GetInteractiveListReq>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:ugc.interactive.GetInteractiveListReq)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:ugc.interactive.GetInteractiveListReq)
    MergeFrom(*source);
  }
}

void GetInteractiveListReq::MergeFrom(const GetInteractiveListReq& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:ugc.interactive.GetInteractiveListReq)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  if (from.last_id().size() > 0) {

    last_id_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.last_id_);
  }
  if (from.user_id() != 0) {
    set_user_id(from.user_id());
  }
  if (from.limit() != 0) {
    set_limit(from.limit());
  }
}

void GetInteractiveListReq::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:ugc.interactive.GetInteractiveListReq)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void GetInteractiveListReq::CopyFrom(const GetInteractiveListReq& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:ugc.interactive.GetInteractiveListReq)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetInteractiveListReq::IsInitialized() const {
  return true;
}

void GetInteractiveListReq::Swap(GetInteractiveListReq* other) {
  if (other == this) return;
  InternalSwap(other);
}
void GetInteractiveListReq::InternalSwap(GetInteractiveListReq* other) {
  last_id_.Swap(&other->last_id_);
  std::swap(user_id_, other->user_id_);
  std::swap(limit_, other->limit_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata GetInteractiveListReq::GetMetadata() const {
  protobuf_interactive_2eproto::protobuf_AssignDescriptorsOnce();
  return protobuf_interactive_2eproto::file_level_metadata[7];
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// GetInteractiveListReq

// uint32 user_id = 1;
void GetInteractiveListReq::clear_user_id() {
  user_id_ = 0u;
}
::google::protobuf::uint32 GetInteractiveListReq::user_id() const {
  // @@protoc_insertion_point(field_get:ugc.interactive.GetInteractiveListReq.user_id)
  return user_id_;
}
void GetInteractiveListReq::set_user_id(::google::protobuf::uint32 value) {
  
  user_id_ = value;
  // @@protoc_insertion_point(field_set:ugc.interactive.GetInteractiveListReq.user_id)
}

// string last_id = 2;
void GetInteractiveListReq::clear_last_id() {
  last_id_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& GetInteractiveListReq::last_id() const {
  // @@protoc_insertion_point(field_get:ugc.interactive.GetInteractiveListReq.last_id)
  return last_id_.GetNoArena();
}
void GetInteractiveListReq::set_last_id(const ::std::string& value) {
  
  last_id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:ugc.interactive.GetInteractiveListReq.last_id)
}
#if LANG_CXX11
void GetInteractiveListReq::set_last_id(::std::string&& value) {
  
  last_id_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:ugc.interactive.GetInteractiveListReq.last_id)
}
#endif
void GetInteractiveListReq::set_last_id(const char* value) {
  
  last_id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:ugc.interactive.GetInteractiveListReq.last_id)
}
void GetInteractiveListReq::set_last_id(const char* value, size_t size) {
  
  last_id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:ugc.interactive.GetInteractiveListReq.last_id)
}
::std::string* GetInteractiveListReq::mutable_last_id() {
  
  // @@protoc_insertion_point(field_mutable:ugc.interactive.GetInteractiveListReq.last_id)
  return last_id_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* GetInteractiveListReq::release_last_id() {
  // @@protoc_insertion_point(field_release:ugc.interactive.GetInteractiveListReq.last_id)
  
  return last_id_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void GetInteractiveListReq::set_allocated_last_id(::std::string* last_id) {
  if (last_id != NULL) {
    
  } else {
    
  }
  last_id_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), last_id);
  // @@protoc_insertion_point(field_set_allocated:ugc.interactive.GetInteractiveListReq.last_id)
}

// int32 limit = 3;
void GetInteractiveListReq::clear_limit() {
  limit_ = 0;
}
::google::protobuf::int32 GetInteractiveListReq::limit() const {
  // @@protoc_insertion_point(field_get:ugc.interactive.GetInteractiveListReq.limit)
  return limit_;
}
void GetInteractiveListReq::set_limit(::google::protobuf::int32 value) {
  
  limit_ = value;
  // @@protoc_insertion_point(field_set:ugc.interactive.GetInteractiveListReq.limit)
}

#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int GetInteractiveListResp::kInfoFieldNumber;
const int GetInteractiveListResp::kLastIdFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

GetInteractiveListResp::GetInteractiveListResp()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (GOOGLE_PREDICT_TRUE(this != internal_default_instance())) {
    protobuf_interactive_2eproto::InitDefaults();
  }
  SharedCtor();
  // @@protoc_insertion_point(constructor:ugc.interactive.GetInteractiveListResp)
}
GetInteractiveListResp::GetInteractiveListResp(const GetInteractiveListResp& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      info_(from.info_),
      _cached_size_(0) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  last_id_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.last_id().size() > 0) {
    last_id_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.last_id_);
  }
  // @@protoc_insertion_point(copy_constructor:ugc.interactive.GetInteractiveListResp)
}

void GetInteractiveListResp::SharedCtor() {
  last_id_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  _cached_size_ = 0;
}

GetInteractiveListResp::~GetInteractiveListResp() {
  // @@protoc_insertion_point(destructor:ugc.interactive.GetInteractiveListResp)
  SharedDtor();
}

void GetInteractiveListResp::SharedDtor() {
  last_id_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void GetInteractiveListResp::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* GetInteractiveListResp::descriptor() {
  protobuf_interactive_2eproto::protobuf_AssignDescriptorsOnce();
  return protobuf_interactive_2eproto::file_level_metadata[8].descriptor;
}

const GetInteractiveListResp& GetInteractiveListResp::default_instance() {
  protobuf_interactive_2eproto::InitDefaults();
  return *internal_default_instance();
}

GetInteractiveListResp* GetInteractiveListResp::New(::google::protobuf::Arena* arena) const {
  GetInteractiveListResp* n = new GetInteractiveListResp;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void GetInteractiveListResp::Clear() {
// @@protoc_insertion_point(message_clear_start:ugc.interactive.GetInteractiveListResp)
  info_.Clear();
  last_id_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

bool GetInteractiveListResp::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:ugc.interactive.GetInteractiveListResp)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // repeated .ugc.interactive.InteractiveMsg info = 1;
      case 1: {
        if (tag == 10u) {
          DO_(input->IncrementRecursionDepth());
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtualNoRecursionDepth(
                input, add_info()));
        } else {
          goto handle_unusual;
        }
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // string last_id = 2;
      case 2: {
        if (tag == 18u) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_last_id()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->last_id().data(), this->last_id().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "ugc.interactive.GetInteractiveListResp.last_id"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:ugc.interactive.GetInteractiveListResp)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:ugc.interactive.GetInteractiveListResp)
  return false;
#undef DO_
}

void GetInteractiveListResp::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:ugc.interactive.GetInteractiveListResp)
  // repeated .ugc.interactive.InteractiveMsg info = 1;
  for (unsigned int i = 0, n = this->info_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, this->info(i), output);
  }

  // string last_id = 2;
  if (this->last_id().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->last_id().data(), this->last_id().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "ugc.interactive.GetInteractiveListResp.last_id");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      2, this->last_id(), output);
  }

  // @@protoc_insertion_point(serialize_end:ugc.interactive.GetInteractiveListResp)
}

::google::protobuf::uint8* GetInteractiveListResp::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic;  // Unused
  // @@protoc_insertion_point(serialize_to_array_start:ugc.interactive.GetInteractiveListResp)
  // repeated .ugc.interactive.InteractiveMsg info = 1;
  for (unsigned int i = 0, n = this->info_size(); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        1, this->info(i), false, target);
  }

  // string last_id = 2;
  if (this->last_id().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->last_id().data(), this->last_id().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "ugc.interactive.GetInteractiveListResp.last_id");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        2, this->last_id(), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:ugc.interactive.GetInteractiveListResp)
  return target;
}

size_t GetInteractiveListResp::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:ugc.interactive.GetInteractiveListResp)
  size_t total_size = 0;

  // repeated .ugc.interactive.InteractiveMsg info = 1;
  {
    unsigned int count = this->info_size();
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->info(i));
    }
  }

  // string last_id = 2;
  if (this->last_id().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->last_id());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void GetInteractiveListResp::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:ugc.interactive.GetInteractiveListResp)
  GOOGLE_DCHECK_NE(&from, this);
  const GetInteractiveListResp* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const GetInteractiveListResp>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:ugc.interactive.GetInteractiveListResp)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:ugc.interactive.GetInteractiveListResp)
    MergeFrom(*source);
  }
}

void GetInteractiveListResp::MergeFrom(const GetInteractiveListResp& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:ugc.interactive.GetInteractiveListResp)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  info_.MergeFrom(from.info_);
  if (from.last_id().size() > 0) {

    last_id_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.last_id_);
  }
}

void GetInteractiveListResp::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:ugc.interactive.GetInteractiveListResp)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void GetInteractiveListResp::CopyFrom(const GetInteractiveListResp& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:ugc.interactive.GetInteractiveListResp)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetInteractiveListResp::IsInitialized() const {
  return true;
}

void GetInteractiveListResp::Swap(GetInteractiveListResp* other) {
  if (other == this) return;
  InternalSwap(other);
}
void GetInteractiveListResp::InternalSwap(GetInteractiveListResp* other) {
  info_.UnsafeArenaSwap(&other->info_);
  last_id_.Swap(&other->last_id_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata GetInteractiveListResp::GetMetadata() const {
  protobuf_interactive_2eproto::protobuf_AssignDescriptorsOnce();
  return protobuf_interactive_2eproto::file_level_metadata[8];
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// GetInteractiveListResp

// repeated .ugc.interactive.InteractiveMsg info = 1;
int GetInteractiveListResp::info_size() const {
  return info_.size();
}
void GetInteractiveListResp::clear_info() {
  info_.Clear();
}
const ::ugc::interactive::InteractiveMsg& GetInteractiveListResp::info(int index) const {
  // @@protoc_insertion_point(field_get:ugc.interactive.GetInteractiveListResp.info)
  return info_.Get(index);
}
::ugc::interactive::InteractiveMsg* GetInteractiveListResp::mutable_info(int index) {
  // @@protoc_insertion_point(field_mutable:ugc.interactive.GetInteractiveListResp.info)
  return info_.Mutable(index);
}
::ugc::interactive::InteractiveMsg* GetInteractiveListResp::add_info() {
  // @@protoc_insertion_point(field_add:ugc.interactive.GetInteractiveListResp.info)
  return info_.Add();
}
::google::protobuf::RepeatedPtrField< ::ugc::interactive::InteractiveMsg >*
GetInteractiveListResp::mutable_info() {
  // @@protoc_insertion_point(field_mutable_list:ugc.interactive.GetInteractiveListResp.info)
  return &info_;
}
const ::google::protobuf::RepeatedPtrField< ::ugc::interactive::InteractiveMsg >&
GetInteractiveListResp::info() const {
  // @@protoc_insertion_point(field_list:ugc.interactive.GetInteractiveListResp.info)
  return info_;
}

// string last_id = 2;
void GetInteractiveListResp::clear_last_id() {
  last_id_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& GetInteractiveListResp::last_id() const {
  // @@protoc_insertion_point(field_get:ugc.interactive.GetInteractiveListResp.last_id)
  return last_id_.GetNoArena();
}
void GetInteractiveListResp::set_last_id(const ::std::string& value) {
  
  last_id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:ugc.interactive.GetInteractiveListResp.last_id)
}
#if LANG_CXX11
void GetInteractiveListResp::set_last_id(::std::string&& value) {
  
  last_id_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:ugc.interactive.GetInteractiveListResp.last_id)
}
#endif
void GetInteractiveListResp::set_last_id(const char* value) {
  
  last_id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:ugc.interactive.GetInteractiveListResp.last_id)
}
void GetInteractiveListResp::set_last_id(const char* value, size_t size) {
  
  last_id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:ugc.interactive.GetInteractiveListResp.last_id)
}
::std::string* GetInteractiveListResp::mutable_last_id() {
  
  // @@protoc_insertion_point(field_mutable:ugc.interactive.GetInteractiveListResp.last_id)
  return last_id_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* GetInteractiveListResp::release_last_id() {
  // @@protoc_insertion_point(field_release:ugc.interactive.GetInteractiveListResp.last_id)
  
  return last_id_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void GetInteractiveListResp::set_allocated_last_id(::std::string* last_id) {
  if (last_id != NULL) {
    
  } else {
    
  }
  last_id_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), last_id);
  // @@protoc_insertion_point(field_set_allocated:ugc.interactive.GetInteractiveListResp.last_id)
}

#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int DeleteMsgReq::kUserIdFieldNumber;
const int DeleteMsgReq::kIdsFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

DeleteMsgReq::DeleteMsgReq()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (GOOGLE_PREDICT_TRUE(this != internal_default_instance())) {
    protobuf_interactive_2eproto::InitDefaults();
  }
  SharedCtor();
  // @@protoc_insertion_point(constructor:ugc.interactive.DeleteMsgReq)
}
DeleteMsgReq::DeleteMsgReq(const DeleteMsgReq& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      ids_(from.ids_),
      _cached_size_(0) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  user_id_ = from.user_id_;
  // @@protoc_insertion_point(copy_constructor:ugc.interactive.DeleteMsgReq)
}

void DeleteMsgReq::SharedCtor() {
  user_id_ = 0u;
  _cached_size_ = 0;
}

DeleteMsgReq::~DeleteMsgReq() {
  // @@protoc_insertion_point(destructor:ugc.interactive.DeleteMsgReq)
  SharedDtor();
}

void DeleteMsgReq::SharedDtor() {
}

void DeleteMsgReq::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* DeleteMsgReq::descriptor() {
  protobuf_interactive_2eproto::protobuf_AssignDescriptorsOnce();
  return protobuf_interactive_2eproto::file_level_metadata[9].descriptor;
}

const DeleteMsgReq& DeleteMsgReq::default_instance() {
  protobuf_interactive_2eproto::InitDefaults();
  return *internal_default_instance();
}

DeleteMsgReq* DeleteMsgReq::New(::google::protobuf::Arena* arena) const {
  DeleteMsgReq* n = new DeleteMsgReq;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void DeleteMsgReq::Clear() {
// @@protoc_insertion_point(message_clear_start:ugc.interactive.DeleteMsgReq)
  ids_.Clear();
  user_id_ = 0u;
}

bool DeleteMsgReq::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:ugc.interactive.DeleteMsgReq)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // uint32 user_id = 1;
      case 1: {
        if (tag == 8u) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_UINT32>(
                 input, &user_id_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated string ids = 2;
      case 2: {
        if (tag == 18u) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->add_ids()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->ids(this->ids_size() - 1).data(),
            this->ids(this->ids_size() - 1).length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "ugc.interactive.DeleteMsgReq.ids"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:ugc.interactive.DeleteMsgReq)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:ugc.interactive.DeleteMsgReq)
  return false;
#undef DO_
}

void DeleteMsgReq::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:ugc.interactive.DeleteMsgReq)
  // uint32 user_id = 1;
  if (this->user_id() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt32(1, this->user_id(), output);
  }

  // repeated string ids = 2;
  for (int i = 0; i < this->ids_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->ids(i).data(), this->ids(i).length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "ugc.interactive.DeleteMsgReq.ids");
    ::google::protobuf::internal::WireFormatLite::WriteString(
      2, this->ids(i), output);
  }

  // @@protoc_insertion_point(serialize_end:ugc.interactive.DeleteMsgReq)
}

::google::protobuf::uint8* DeleteMsgReq::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic;  // Unused
  // @@protoc_insertion_point(serialize_to_array_start:ugc.interactive.DeleteMsgReq)
  // uint32 user_id = 1;
  if (this->user_id() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt32ToArray(1, this->user_id(), target);
  }

  // repeated string ids = 2;
  for (int i = 0; i < this->ids_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->ids(i).data(), this->ids(i).length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "ugc.interactive.DeleteMsgReq.ids");
    target = ::google::protobuf::internal::WireFormatLite::
      WriteStringToArray(2, this->ids(i), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:ugc.interactive.DeleteMsgReq)
  return target;
}

size_t DeleteMsgReq::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:ugc.interactive.DeleteMsgReq)
  size_t total_size = 0;

  // repeated string ids = 2;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->ids_size());
  for (int i = 0; i < this->ids_size(); i++) {
    total_size += ::google::protobuf::internal::WireFormatLite::StringSize(
      this->ids(i));
  }

  // uint32 user_id = 1;
  if (this->user_id() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::UInt32Size(
        this->user_id());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void DeleteMsgReq::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:ugc.interactive.DeleteMsgReq)
  GOOGLE_DCHECK_NE(&from, this);
  const DeleteMsgReq* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const DeleteMsgReq>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:ugc.interactive.DeleteMsgReq)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:ugc.interactive.DeleteMsgReq)
    MergeFrom(*source);
  }
}

void DeleteMsgReq::MergeFrom(const DeleteMsgReq& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:ugc.interactive.DeleteMsgReq)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ids_.MergeFrom(from.ids_);
  if (from.user_id() != 0) {
    set_user_id(from.user_id());
  }
}

void DeleteMsgReq::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:ugc.interactive.DeleteMsgReq)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void DeleteMsgReq::CopyFrom(const DeleteMsgReq& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:ugc.interactive.DeleteMsgReq)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool DeleteMsgReq::IsInitialized() const {
  return true;
}

void DeleteMsgReq::Swap(DeleteMsgReq* other) {
  if (other == this) return;
  InternalSwap(other);
}
void DeleteMsgReq::InternalSwap(DeleteMsgReq* other) {
  ids_.UnsafeArenaSwap(&other->ids_);
  std::swap(user_id_, other->user_id_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata DeleteMsgReq::GetMetadata() const {
  protobuf_interactive_2eproto::protobuf_AssignDescriptorsOnce();
  return protobuf_interactive_2eproto::file_level_metadata[9];
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// DeleteMsgReq

// uint32 user_id = 1;
void DeleteMsgReq::clear_user_id() {
  user_id_ = 0u;
}
::google::protobuf::uint32 DeleteMsgReq::user_id() const {
  // @@protoc_insertion_point(field_get:ugc.interactive.DeleteMsgReq.user_id)
  return user_id_;
}
void DeleteMsgReq::set_user_id(::google::protobuf::uint32 value) {
  
  user_id_ = value;
  // @@protoc_insertion_point(field_set:ugc.interactive.DeleteMsgReq.user_id)
}

// repeated string ids = 2;
int DeleteMsgReq::ids_size() const {
  return ids_.size();
}
void DeleteMsgReq::clear_ids() {
  ids_.Clear();
}
const ::std::string& DeleteMsgReq::ids(int index) const {
  // @@protoc_insertion_point(field_get:ugc.interactive.DeleteMsgReq.ids)
  return ids_.Get(index);
}
::std::string* DeleteMsgReq::mutable_ids(int index) {
  // @@protoc_insertion_point(field_mutable:ugc.interactive.DeleteMsgReq.ids)
  return ids_.Mutable(index);
}
void DeleteMsgReq::set_ids(int index, const ::std::string& value) {
  // @@protoc_insertion_point(field_set:ugc.interactive.DeleteMsgReq.ids)
  ids_.Mutable(index)->assign(value);
}
void DeleteMsgReq::set_ids(int index, const char* value) {
  ids_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:ugc.interactive.DeleteMsgReq.ids)
}
void DeleteMsgReq::set_ids(int index, const char* value, size_t size) {
  ids_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:ugc.interactive.DeleteMsgReq.ids)
}
::std::string* DeleteMsgReq::add_ids() {
  // @@protoc_insertion_point(field_add_mutable:ugc.interactive.DeleteMsgReq.ids)
  return ids_.Add();
}
void DeleteMsgReq::add_ids(const ::std::string& value) {
  ids_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:ugc.interactive.DeleteMsgReq.ids)
}
void DeleteMsgReq::add_ids(const char* value) {
  ids_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:ugc.interactive.DeleteMsgReq.ids)
}
void DeleteMsgReq::add_ids(const char* value, size_t size) {
  ids_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:ugc.interactive.DeleteMsgReq.ids)
}
const ::google::protobuf::RepeatedPtrField< ::std::string>&
DeleteMsgReq::ids() const {
  // @@protoc_insertion_point(field_list:ugc.interactive.DeleteMsgReq.ids)
  return ids_;
}
::google::protobuf::RepeatedPtrField< ::std::string>*
DeleteMsgReq::mutable_ids() {
  // @@protoc_insertion_point(field_mutable_list:ugc.interactive.DeleteMsgReq.ids)
  return &ids_;
}

#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

DeleteMsgResp::DeleteMsgResp()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (GOOGLE_PREDICT_TRUE(this != internal_default_instance())) {
    protobuf_interactive_2eproto::InitDefaults();
  }
  SharedCtor();
  // @@protoc_insertion_point(constructor:ugc.interactive.DeleteMsgResp)
}
DeleteMsgResp::DeleteMsgResp(const DeleteMsgResp& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      _cached_size_(0) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:ugc.interactive.DeleteMsgResp)
}

void DeleteMsgResp::SharedCtor() {
  _cached_size_ = 0;
}

DeleteMsgResp::~DeleteMsgResp() {
  // @@protoc_insertion_point(destructor:ugc.interactive.DeleteMsgResp)
  SharedDtor();
}

void DeleteMsgResp::SharedDtor() {
}

void DeleteMsgResp::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* DeleteMsgResp::descriptor() {
  protobuf_interactive_2eproto::protobuf_AssignDescriptorsOnce();
  return protobuf_interactive_2eproto::file_level_metadata[10].descriptor;
}

const DeleteMsgResp& DeleteMsgResp::default_instance() {
  protobuf_interactive_2eproto::InitDefaults();
  return *internal_default_instance();
}

DeleteMsgResp* DeleteMsgResp::New(::google::protobuf::Arena* arena) const {
  DeleteMsgResp* n = new DeleteMsgResp;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void DeleteMsgResp::Clear() {
// @@protoc_insertion_point(message_clear_start:ugc.interactive.DeleteMsgResp)
}

bool DeleteMsgResp::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:ugc.interactive.DeleteMsgResp)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
  handle_unusual:
    if (tag == 0 ||
        ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
        ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
      goto success;
    }
    DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
  }
success:
  // @@protoc_insertion_point(parse_success:ugc.interactive.DeleteMsgResp)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:ugc.interactive.DeleteMsgResp)
  return false;
#undef DO_
}

void DeleteMsgResp::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:ugc.interactive.DeleteMsgResp)
  // @@protoc_insertion_point(serialize_end:ugc.interactive.DeleteMsgResp)
}

::google::protobuf::uint8* DeleteMsgResp::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic;  // Unused
  // @@protoc_insertion_point(serialize_to_array_start:ugc.interactive.DeleteMsgResp)
  // @@protoc_insertion_point(serialize_to_array_end:ugc.interactive.DeleteMsgResp)
  return target;
}

size_t DeleteMsgResp::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:ugc.interactive.DeleteMsgResp)
  size_t total_size = 0;

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void DeleteMsgResp::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:ugc.interactive.DeleteMsgResp)
  GOOGLE_DCHECK_NE(&from, this);
  const DeleteMsgResp* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const DeleteMsgResp>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:ugc.interactive.DeleteMsgResp)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:ugc.interactive.DeleteMsgResp)
    MergeFrom(*source);
  }
}

void DeleteMsgResp::MergeFrom(const DeleteMsgResp& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:ugc.interactive.DeleteMsgResp)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
}

void DeleteMsgResp::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:ugc.interactive.DeleteMsgResp)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void DeleteMsgResp::CopyFrom(const DeleteMsgResp& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:ugc.interactive.DeleteMsgResp)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool DeleteMsgResp::IsInitialized() const {
  return true;
}

void DeleteMsgResp::Swap(DeleteMsgResp* other) {
  if (other == this) return;
  InternalSwap(other);
}
void DeleteMsgResp::InternalSwap(DeleteMsgResp* other) {
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata DeleteMsgResp::GetMetadata() const {
  protobuf_interactive_2eproto::protobuf_AssignDescriptorsOnce();
  return protobuf_interactive_2eproto::file_level_metadata[10];
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// DeleteMsgResp

#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int MarkReadReq::kUserIdFieldNumber;
const int MarkReadReq::kIdFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

MarkReadReq::MarkReadReq()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (GOOGLE_PREDICT_TRUE(this != internal_default_instance())) {
    protobuf_interactive_2eproto::InitDefaults();
  }
  SharedCtor();
  // @@protoc_insertion_point(constructor:ugc.interactive.MarkReadReq)
}
MarkReadReq::MarkReadReq(const MarkReadReq& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      _cached_size_(0) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  id_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.id().size() > 0) {
    id_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.id_);
  }
  user_id_ = from.user_id_;
  // @@protoc_insertion_point(copy_constructor:ugc.interactive.MarkReadReq)
}

void MarkReadReq::SharedCtor() {
  id_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  user_id_ = 0u;
  _cached_size_ = 0;
}

MarkReadReq::~MarkReadReq() {
  // @@protoc_insertion_point(destructor:ugc.interactive.MarkReadReq)
  SharedDtor();
}

void MarkReadReq::SharedDtor() {
  id_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void MarkReadReq::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* MarkReadReq::descriptor() {
  protobuf_interactive_2eproto::protobuf_AssignDescriptorsOnce();
  return protobuf_interactive_2eproto::file_level_metadata[11].descriptor;
}

const MarkReadReq& MarkReadReq::default_instance() {
  protobuf_interactive_2eproto::InitDefaults();
  return *internal_default_instance();
}

MarkReadReq* MarkReadReq::New(::google::protobuf::Arena* arena) const {
  MarkReadReq* n = new MarkReadReq;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void MarkReadReq::Clear() {
// @@protoc_insertion_point(message_clear_start:ugc.interactive.MarkReadReq)
  id_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  user_id_ = 0u;
}

bool MarkReadReq::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:ugc.interactive.MarkReadReq)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // uint32 user_id = 1;
      case 1: {
        if (tag == 8u) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_UINT32>(
                 input, &user_id_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string id = 2;
      case 2: {
        if (tag == 18u) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_id()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->id().data(), this->id().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "ugc.interactive.MarkReadReq.id"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:ugc.interactive.MarkReadReq)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:ugc.interactive.MarkReadReq)
  return false;
#undef DO_
}

void MarkReadReq::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:ugc.interactive.MarkReadReq)
  // uint32 user_id = 1;
  if (this->user_id() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt32(1, this->user_id(), output);
  }

  // string id = 2;
  if (this->id().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->id().data(), this->id().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "ugc.interactive.MarkReadReq.id");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      2, this->id(), output);
  }

  // @@protoc_insertion_point(serialize_end:ugc.interactive.MarkReadReq)
}

::google::protobuf::uint8* MarkReadReq::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic;  // Unused
  // @@protoc_insertion_point(serialize_to_array_start:ugc.interactive.MarkReadReq)
  // uint32 user_id = 1;
  if (this->user_id() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt32ToArray(1, this->user_id(), target);
  }

  // string id = 2;
  if (this->id().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->id().data(), this->id().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "ugc.interactive.MarkReadReq.id");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        2, this->id(), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:ugc.interactive.MarkReadReq)
  return target;
}

size_t MarkReadReq::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:ugc.interactive.MarkReadReq)
  size_t total_size = 0;

  // string id = 2;
  if (this->id().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->id());
  }

  // uint32 user_id = 1;
  if (this->user_id() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::UInt32Size(
        this->user_id());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void MarkReadReq::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:ugc.interactive.MarkReadReq)
  GOOGLE_DCHECK_NE(&from, this);
  const MarkReadReq* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const MarkReadReq>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:ugc.interactive.MarkReadReq)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:ugc.interactive.MarkReadReq)
    MergeFrom(*source);
  }
}

void MarkReadReq::MergeFrom(const MarkReadReq& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:ugc.interactive.MarkReadReq)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  if (from.id().size() > 0) {

    id_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.id_);
  }
  if (from.user_id() != 0) {
    set_user_id(from.user_id());
  }
}

void MarkReadReq::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:ugc.interactive.MarkReadReq)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void MarkReadReq::CopyFrom(const MarkReadReq& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:ugc.interactive.MarkReadReq)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool MarkReadReq::IsInitialized() const {
  return true;
}

void MarkReadReq::Swap(MarkReadReq* other) {
  if (other == this) return;
  InternalSwap(other);
}
void MarkReadReq::InternalSwap(MarkReadReq* other) {
  id_.Swap(&other->id_);
  std::swap(user_id_, other->user_id_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata MarkReadReq::GetMetadata() const {
  protobuf_interactive_2eproto::protobuf_AssignDescriptorsOnce();
  return protobuf_interactive_2eproto::file_level_metadata[11];
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// MarkReadReq

// uint32 user_id = 1;
void MarkReadReq::clear_user_id() {
  user_id_ = 0u;
}
::google::protobuf::uint32 MarkReadReq::user_id() const {
  // @@protoc_insertion_point(field_get:ugc.interactive.MarkReadReq.user_id)
  return user_id_;
}
void MarkReadReq::set_user_id(::google::protobuf::uint32 value) {
  
  user_id_ = value;
  // @@protoc_insertion_point(field_set:ugc.interactive.MarkReadReq.user_id)
}

// string id = 2;
void MarkReadReq::clear_id() {
  id_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MarkReadReq::id() const {
  // @@protoc_insertion_point(field_get:ugc.interactive.MarkReadReq.id)
  return id_.GetNoArena();
}
void MarkReadReq::set_id(const ::std::string& value) {
  
  id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:ugc.interactive.MarkReadReq.id)
}
#if LANG_CXX11
void MarkReadReq::set_id(::std::string&& value) {
  
  id_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:ugc.interactive.MarkReadReq.id)
}
#endif
void MarkReadReq::set_id(const char* value) {
  
  id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:ugc.interactive.MarkReadReq.id)
}
void MarkReadReq::set_id(const char* value, size_t size) {
  
  id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:ugc.interactive.MarkReadReq.id)
}
::std::string* MarkReadReq::mutable_id() {
  
  // @@protoc_insertion_point(field_mutable:ugc.interactive.MarkReadReq.id)
  return id_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MarkReadReq::release_id() {
  // @@protoc_insertion_point(field_release:ugc.interactive.MarkReadReq.id)
  
  return id_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MarkReadReq::set_allocated_id(::std::string* id) {
  if (id != NULL) {
    
  } else {
    
  }
  id_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), id);
  // @@protoc_insertion_point(field_set_allocated:ugc.interactive.MarkReadReq.id)
}

#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

MarkReadResp::MarkReadResp()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (GOOGLE_PREDICT_TRUE(this != internal_default_instance())) {
    protobuf_interactive_2eproto::InitDefaults();
  }
  SharedCtor();
  // @@protoc_insertion_point(constructor:ugc.interactive.MarkReadResp)
}
MarkReadResp::MarkReadResp(const MarkReadResp& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      _cached_size_(0) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:ugc.interactive.MarkReadResp)
}

void MarkReadResp::SharedCtor() {
  _cached_size_ = 0;
}

MarkReadResp::~MarkReadResp() {
  // @@protoc_insertion_point(destructor:ugc.interactive.MarkReadResp)
  SharedDtor();
}

void MarkReadResp::SharedDtor() {
}

void MarkReadResp::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* MarkReadResp::descriptor() {
  protobuf_interactive_2eproto::protobuf_AssignDescriptorsOnce();
  return protobuf_interactive_2eproto::file_level_metadata[12].descriptor;
}

const MarkReadResp& MarkReadResp::default_instance() {
  protobuf_interactive_2eproto::InitDefaults();
  return *internal_default_instance();
}

MarkReadResp* MarkReadResp::New(::google::protobuf::Arena* arena) const {
  MarkReadResp* n = new MarkReadResp;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void MarkReadResp::Clear() {
// @@protoc_insertion_point(message_clear_start:ugc.interactive.MarkReadResp)
}

bool MarkReadResp::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:ugc.interactive.MarkReadResp)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
  handle_unusual:
    if (tag == 0 ||
        ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
        ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
      goto success;
    }
    DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
  }
success:
  // @@protoc_insertion_point(parse_success:ugc.interactive.MarkReadResp)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:ugc.interactive.MarkReadResp)
  return false;
#undef DO_
}

void MarkReadResp::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:ugc.interactive.MarkReadResp)
  // @@protoc_insertion_point(serialize_end:ugc.interactive.MarkReadResp)
}

::google::protobuf::uint8* MarkReadResp::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic;  // Unused
  // @@protoc_insertion_point(serialize_to_array_start:ugc.interactive.MarkReadResp)
  // @@protoc_insertion_point(serialize_to_array_end:ugc.interactive.MarkReadResp)
  return target;
}

size_t MarkReadResp::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:ugc.interactive.MarkReadResp)
  size_t total_size = 0;

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void MarkReadResp::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:ugc.interactive.MarkReadResp)
  GOOGLE_DCHECK_NE(&from, this);
  const MarkReadResp* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const MarkReadResp>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:ugc.interactive.MarkReadResp)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:ugc.interactive.MarkReadResp)
    MergeFrom(*source);
  }
}

void MarkReadResp::MergeFrom(const MarkReadResp& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:ugc.interactive.MarkReadResp)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
}

void MarkReadResp::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:ugc.interactive.MarkReadResp)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void MarkReadResp::CopyFrom(const MarkReadResp& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:ugc.interactive.MarkReadResp)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool MarkReadResp::IsInitialized() const {
  return true;
}

void MarkReadResp::Swap(MarkReadResp* other) {
  if (other == this) return;
  InternalSwap(other);
}
void MarkReadResp::InternalSwap(MarkReadResp* other) {
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata MarkReadResp::GetMetadata() const {
  protobuf_interactive_2eproto::protobuf_AssignDescriptorsOnce();
  return protobuf_interactive_2eproto::file_level_metadata[12];
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// MarkReadResp

#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int GetUinfoReq::kUserIdFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

GetUinfoReq::GetUinfoReq()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (GOOGLE_PREDICT_TRUE(this != internal_default_instance())) {
    protobuf_interactive_2eproto::InitDefaults();
  }
  SharedCtor();
  // @@protoc_insertion_point(constructor:ugc.interactive.GetUinfoReq)
}
GetUinfoReq::GetUinfoReq(const GetUinfoReq& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      _cached_size_(0) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  user_id_ = from.user_id_;
  // @@protoc_insertion_point(copy_constructor:ugc.interactive.GetUinfoReq)
}

void GetUinfoReq::SharedCtor() {
  user_id_ = 0u;
  _cached_size_ = 0;
}

GetUinfoReq::~GetUinfoReq() {
  // @@protoc_insertion_point(destructor:ugc.interactive.GetUinfoReq)
  SharedDtor();
}

void GetUinfoReq::SharedDtor() {
}

void GetUinfoReq::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* GetUinfoReq::descriptor() {
  protobuf_interactive_2eproto::protobuf_AssignDescriptorsOnce();
  return protobuf_interactive_2eproto::file_level_metadata[13].descriptor;
}

const GetUinfoReq& GetUinfoReq::default_instance() {
  protobuf_interactive_2eproto::InitDefaults();
  return *internal_default_instance();
}

GetUinfoReq* GetUinfoReq::New(::google::protobuf::Arena* arena) const {
  GetUinfoReq* n = new GetUinfoReq;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void GetUinfoReq::Clear() {
// @@protoc_insertion_point(message_clear_start:ugc.interactive.GetUinfoReq)
  user_id_ = 0u;
}

bool GetUinfoReq::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:ugc.interactive.GetUinfoReq)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // uint32 user_id = 1;
      case 1: {
        if (tag == 8u) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_UINT32>(
                 input, &user_id_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:ugc.interactive.GetUinfoReq)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:ugc.interactive.GetUinfoReq)
  return false;
#undef DO_
}

void GetUinfoReq::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:ugc.interactive.GetUinfoReq)
  // uint32 user_id = 1;
  if (this->user_id() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt32(1, this->user_id(), output);
  }

  // @@protoc_insertion_point(serialize_end:ugc.interactive.GetUinfoReq)
}

::google::protobuf::uint8* GetUinfoReq::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic;  // Unused
  // @@protoc_insertion_point(serialize_to_array_start:ugc.interactive.GetUinfoReq)
  // uint32 user_id = 1;
  if (this->user_id() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt32ToArray(1, this->user_id(), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:ugc.interactive.GetUinfoReq)
  return target;
}

size_t GetUinfoReq::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:ugc.interactive.GetUinfoReq)
  size_t total_size = 0;

  // uint32 user_id = 1;
  if (this->user_id() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::UInt32Size(
        this->user_id());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void GetUinfoReq::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:ugc.interactive.GetUinfoReq)
  GOOGLE_DCHECK_NE(&from, this);
  const GetUinfoReq* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const GetUinfoReq>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:ugc.interactive.GetUinfoReq)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:ugc.interactive.GetUinfoReq)
    MergeFrom(*source);
  }
}

void GetUinfoReq::MergeFrom(const GetUinfoReq& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:ugc.interactive.GetUinfoReq)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  if (from.user_id() != 0) {
    set_user_id(from.user_id());
  }
}

void GetUinfoReq::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:ugc.interactive.GetUinfoReq)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void GetUinfoReq::CopyFrom(const GetUinfoReq& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:ugc.interactive.GetUinfoReq)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetUinfoReq::IsInitialized() const {
  return true;
}

void GetUinfoReq::Swap(GetUinfoReq* other) {
  if (other == this) return;
  InternalSwap(other);
}
void GetUinfoReq::InternalSwap(GetUinfoReq* other) {
  std::swap(user_id_, other->user_id_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata GetUinfoReq::GetMetadata() const {
  protobuf_interactive_2eproto::protobuf_AssignDescriptorsOnce();
  return protobuf_interactive_2eproto::file_level_metadata[13];
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// GetUinfoReq

// uint32 user_id = 1;
void GetUinfoReq::clear_user_id() {
  user_id_ = 0u;
}
::google::protobuf::uint32 GetUinfoReq::user_id() const {
  // @@protoc_insertion_point(field_get:ugc.interactive.GetUinfoReq.user_id)
  return user_id_;
}
void GetUinfoReq::set_user_id(::google::protobuf::uint32 value) {
  
  user_id_ = value;
  // @@protoc_insertion_point(field_set:ugc.interactive.GetUinfoReq.user_id)
}

#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int GetUinfoResp::kUinfoFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

GetUinfoResp::GetUinfoResp()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (GOOGLE_PREDICT_TRUE(this != internal_default_instance())) {
    protobuf_interactive_2eproto::InitDefaults();
  }
  SharedCtor();
  // @@protoc_insertion_point(constructor:ugc.interactive.GetUinfoResp)
}
GetUinfoResp::GetUinfoResp(const GetUinfoResp& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      _cached_size_(0) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  if (from.has_uinfo()) {
    uinfo_ = new ::ugc::interactive::Uinfo(*from.uinfo_);
  } else {
    uinfo_ = NULL;
  }
  // @@protoc_insertion_point(copy_constructor:ugc.interactive.GetUinfoResp)
}

void GetUinfoResp::SharedCtor() {
  uinfo_ = NULL;
  _cached_size_ = 0;
}

GetUinfoResp::~GetUinfoResp() {
  // @@protoc_insertion_point(destructor:ugc.interactive.GetUinfoResp)
  SharedDtor();
}

void GetUinfoResp::SharedDtor() {
  if (this != internal_default_instance()) {
    delete uinfo_;
  }
}

void GetUinfoResp::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* GetUinfoResp::descriptor() {
  protobuf_interactive_2eproto::protobuf_AssignDescriptorsOnce();
  return protobuf_interactive_2eproto::file_level_metadata[14].descriptor;
}

const GetUinfoResp& GetUinfoResp::default_instance() {
  protobuf_interactive_2eproto::InitDefaults();
  return *internal_default_instance();
}

GetUinfoResp* GetUinfoResp::New(::google::protobuf::Arena* arena) const {
  GetUinfoResp* n = new GetUinfoResp;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void GetUinfoResp::Clear() {
// @@protoc_insertion_point(message_clear_start:ugc.interactive.GetUinfoResp)
  if (GetArenaNoVirtual() == NULL && uinfo_ != NULL) {
    delete uinfo_;
  }
  uinfo_ = NULL;
}

bool GetUinfoResp::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:ugc.interactive.GetUinfoResp)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // .ugc.interactive.Uinfo uinfo = 1;
      case 1: {
        if (tag == 10u) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_uinfo()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:ugc.interactive.GetUinfoResp)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:ugc.interactive.GetUinfoResp)
  return false;
#undef DO_
}

void GetUinfoResp::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:ugc.interactive.GetUinfoResp)
  // .ugc.interactive.Uinfo uinfo = 1;
  if (this->has_uinfo()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, *this->uinfo_, output);
  }

  // @@protoc_insertion_point(serialize_end:ugc.interactive.GetUinfoResp)
}

::google::protobuf::uint8* GetUinfoResp::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic;  // Unused
  // @@protoc_insertion_point(serialize_to_array_start:ugc.interactive.GetUinfoResp)
  // .ugc.interactive.Uinfo uinfo = 1;
  if (this->has_uinfo()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        1, *this->uinfo_, false, target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:ugc.interactive.GetUinfoResp)
  return target;
}

size_t GetUinfoResp::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:ugc.interactive.GetUinfoResp)
  size_t total_size = 0;

  // .ugc.interactive.Uinfo uinfo = 1;
  if (this->has_uinfo()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->uinfo_);
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void GetUinfoResp::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:ugc.interactive.GetUinfoResp)
  GOOGLE_DCHECK_NE(&from, this);
  const GetUinfoResp* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const GetUinfoResp>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:ugc.interactive.GetUinfoResp)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:ugc.interactive.GetUinfoResp)
    MergeFrom(*source);
  }
}

void GetUinfoResp::MergeFrom(const GetUinfoResp& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:ugc.interactive.GetUinfoResp)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  if (from.has_uinfo()) {
    mutable_uinfo()->::ugc::interactive::Uinfo::MergeFrom(from.uinfo());
  }
}

void GetUinfoResp::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:ugc.interactive.GetUinfoResp)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void GetUinfoResp::CopyFrom(const GetUinfoResp& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:ugc.interactive.GetUinfoResp)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetUinfoResp::IsInitialized() const {
  return true;
}

void GetUinfoResp::Swap(GetUinfoResp* other) {
  if (other == this) return;
  InternalSwap(other);
}
void GetUinfoResp::InternalSwap(GetUinfoResp* other) {
  std::swap(uinfo_, other->uinfo_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata GetUinfoResp::GetMetadata() const {
  protobuf_interactive_2eproto::protobuf_AssignDescriptorsOnce();
  return protobuf_interactive_2eproto::file_level_metadata[14];
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// GetUinfoResp

// .ugc.interactive.Uinfo uinfo = 1;
bool GetUinfoResp::has_uinfo() const {
  return this != internal_default_instance() && uinfo_ != NULL;
}
void GetUinfoResp::clear_uinfo() {
  if (GetArenaNoVirtual() == NULL && uinfo_ != NULL) delete uinfo_;
  uinfo_ = NULL;
}
const ::ugc::interactive::Uinfo& GetUinfoResp::uinfo() const {
  // @@protoc_insertion_point(field_get:ugc.interactive.GetUinfoResp.uinfo)
  return uinfo_ != NULL ? *uinfo_
                         : *::ugc::interactive::Uinfo::internal_default_instance();
}
::ugc::interactive::Uinfo* GetUinfoResp::mutable_uinfo() {
  
  if (uinfo_ == NULL) {
    uinfo_ = new ::ugc::interactive::Uinfo;
  }
  // @@protoc_insertion_point(field_mutable:ugc.interactive.GetUinfoResp.uinfo)
  return uinfo_;
}
::ugc::interactive::Uinfo* GetUinfoResp::release_uinfo() {
  // @@protoc_insertion_point(field_release:ugc.interactive.GetUinfoResp.uinfo)
  
  ::ugc::interactive::Uinfo* temp = uinfo_;
  uinfo_ = NULL;
  return temp;
}
void GetUinfoResp::set_allocated_uinfo(::ugc::interactive::Uinfo* uinfo) {
  delete uinfo_;
  uinfo_ = uinfo;
  if (uinfo) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:ugc.interactive.GetUinfoResp.uinfo)
}

#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int ClearNewFollowCountReq::kUserIdFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

ClearNewFollowCountReq::ClearNewFollowCountReq()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (GOOGLE_PREDICT_TRUE(this != internal_default_instance())) {
    protobuf_interactive_2eproto::InitDefaults();
  }
  SharedCtor();
  // @@protoc_insertion_point(constructor:ugc.interactive.ClearNewFollowCountReq)
}
ClearNewFollowCountReq::ClearNewFollowCountReq(const ClearNewFollowCountReq& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      _cached_size_(0) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  user_id_ = from.user_id_;
  // @@protoc_insertion_point(copy_constructor:ugc.interactive.ClearNewFollowCountReq)
}

void ClearNewFollowCountReq::SharedCtor() {
  user_id_ = 0u;
  _cached_size_ = 0;
}

ClearNewFollowCountReq::~ClearNewFollowCountReq() {
  // @@protoc_insertion_point(destructor:ugc.interactive.ClearNewFollowCountReq)
  SharedDtor();
}

void ClearNewFollowCountReq::SharedDtor() {
}

void ClearNewFollowCountReq::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* ClearNewFollowCountReq::descriptor() {
  protobuf_interactive_2eproto::protobuf_AssignDescriptorsOnce();
  return protobuf_interactive_2eproto::file_level_metadata[15].descriptor;
}

const ClearNewFollowCountReq& ClearNewFollowCountReq::default_instance() {
  protobuf_interactive_2eproto::InitDefaults();
  return *internal_default_instance();
}

ClearNewFollowCountReq* ClearNewFollowCountReq::New(::google::protobuf::Arena* arena) const {
  ClearNewFollowCountReq* n = new ClearNewFollowCountReq;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void ClearNewFollowCountReq::Clear() {
// @@protoc_insertion_point(message_clear_start:ugc.interactive.ClearNewFollowCountReq)
  user_id_ = 0u;
}

bool ClearNewFollowCountReq::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:ugc.interactive.ClearNewFollowCountReq)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // uint32 user_id = 1;
      case 1: {
        if (tag == 8u) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_UINT32>(
                 input, &user_id_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:ugc.interactive.ClearNewFollowCountReq)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:ugc.interactive.ClearNewFollowCountReq)
  return false;
#undef DO_
}

void ClearNewFollowCountReq::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:ugc.interactive.ClearNewFollowCountReq)
  // uint32 user_id = 1;
  if (this->user_id() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt32(1, this->user_id(), output);
  }

  // @@protoc_insertion_point(serialize_end:ugc.interactive.ClearNewFollowCountReq)
}

::google::protobuf::uint8* ClearNewFollowCountReq::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic;  // Unused
  // @@protoc_insertion_point(serialize_to_array_start:ugc.interactive.ClearNewFollowCountReq)
  // uint32 user_id = 1;
  if (this->user_id() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt32ToArray(1, this->user_id(), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:ugc.interactive.ClearNewFollowCountReq)
  return target;
}

size_t ClearNewFollowCountReq::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:ugc.interactive.ClearNewFollowCountReq)
  size_t total_size = 0;

  // uint32 user_id = 1;
  if (this->user_id() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::UInt32Size(
        this->user_id());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void ClearNewFollowCountReq::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:ugc.interactive.ClearNewFollowCountReq)
  GOOGLE_DCHECK_NE(&from, this);
  const ClearNewFollowCountReq* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const ClearNewFollowCountReq>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:ugc.interactive.ClearNewFollowCountReq)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:ugc.interactive.ClearNewFollowCountReq)
    MergeFrom(*source);
  }
}

void ClearNewFollowCountReq::MergeFrom(const ClearNewFollowCountReq& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:ugc.interactive.ClearNewFollowCountReq)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  if (from.user_id() != 0) {
    set_user_id(from.user_id());
  }
}

void ClearNewFollowCountReq::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:ugc.interactive.ClearNewFollowCountReq)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void ClearNewFollowCountReq::CopyFrom(const ClearNewFollowCountReq& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:ugc.interactive.ClearNewFollowCountReq)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ClearNewFollowCountReq::IsInitialized() const {
  return true;
}

void ClearNewFollowCountReq::Swap(ClearNewFollowCountReq* other) {
  if (other == this) return;
  InternalSwap(other);
}
void ClearNewFollowCountReq::InternalSwap(ClearNewFollowCountReq* other) {
  std::swap(user_id_, other->user_id_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata ClearNewFollowCountReq::GetMetadata() const {
  protobuf_interactive_2eproto::protobuf_AssignDescriptorsOnce();
  return protobuf_interactive_2eproto::file_level_metadata[15];
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// ClearNewFollowCountReq

// uint32 user_id = 1;
void ClearNewFollowCountReq::clear_user_id() {
  user_id_ = 0u;
}
::google::protobuf::uint32 ClearNewFollowCountReq::user_id() const {
  // @@protoc_insertion_point(field_get:ugc.interactive.ClearNewFollowCountReq.user_id)
  return user_id_;
}
void ClearNewFollowCountReq::set_user_id(::google::protobuf::uint32 value) {
  
  user_id_ = value;
  // @@protoc_insertion_point(field_set:ugc.interactive.ClearNewFollowCountReq.user_id)
}

#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

ClearNewFollowCountResp::ClearNewFollowCountResp()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (GOOGLE_PREDICT_TRUE(this != internal_default_instance())) {
    protobuf_interactive_2eproto::InitDefaults();
  }
  SharedCtor();
  // @@protoc_insertion_point(constructor:ugc.interactive.ClearNewFollowCountResp)
}
ClearNewFollowCountResp::ClearNewFollowCountResp(const ClearNewFollowCountResp& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      _cached_size_(0) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:ugc.interactive.ClearNewFollowCountResp)
}

void ClearNewFollowCountResp::SharedCtor() {
  _cached_size_ = 0;
}

ClearNewFollowCountResp::~ClearNewFollowCountResp() {
  // @@protoc_insertion_point(destructor:ugc.interactive.ClearNewFollowCountResp)
  SharedDtor();
}

void ClearNewFollowCountResp::SharedDtor() {
}

void ClearNewFollowCountResp::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* ClearNewFollowCountResp::descriptor() {
  protobuf_interactive_2eproto::protobuf_AssignDescriptorsOnce();
  return protobuf_interactive_2eproto::file_level_metadata[16].descriptor;
}

const ClearNewFollowCountResp& ClearNewFollowCountResp::default_instance() {
  protobuf_interactive_2eproto::InitDefaults();
  return *internal_default_instance();
}

ClearNewFollowCountResp* ClearNewFollowCountResp::New(::google::protobuf::Arena* arena) const {
  ClearNewFollowCountResp* n = new ClearNewFollowCountResp;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void ClearNewFollowCountResp::Clear() {
// @@protoc_insertion_point(message_clear_start:ugc.interactive.ClearNewFollowCountResp)
}

bool ClearNewFollowCountResp::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:ugc.interactive.ClearNewFollowCountResp)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
  handle_unusual:
    if (tag == 0 ||
        ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
        ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
      goto success;
    }
    DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
  }
success:
  // @@protoc_insertion_point(parse_success:ugc.interactive.ClearNewFollowCountResp)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:ugc.interactive.ClearNewFollowCountResp)
  return false;
#undef DO_
}

void ClearNewFollowCountResp::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:ugc.interactive.ClearNewFollowCountResp)
  // @@protoc_insertion_point(serialize_end:ugc.interactive.ClearNewFollowCountResp)
}

::google::protobuf::uint8* ClearNewFollowCountResp::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic;  // Unused
  // @@protoc_insertion_point(serialize_to_array_start:ugc.interactive.ClearNewFollowCountResp)
  // @@protoc_insertion_point(serialize_to_array_end:ugc.interactive.ClearNewFollowCountResp)
  return target;
}

size_t ClearNewFollowCountResp::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:ugc.interactive.ClearNewFollowCountResp)
  size_t total_size = 0;

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void ClearNewFollowCountResp::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:ugc.interactive.ClearNewFollowCountResp)
  GOOGLE_DCHECK_NE(&from, this);
  const ClearNewFollowCountResp* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const ClearNewFollowCountResp>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:ugc.interactive.ClearNewFollowCountResp)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:ugc.interactive.ClearNewFollowCountResp)
    MergeFrom(*source);
  }
}

void ClearNewFollowCountResp::MergeFrom(const ClearNewFollowCountResp& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:ugc.interactive.ClearNewFollowCountResp)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
}

void ClearNewFollowCountResp::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:ugc.interactive.ClearNewFollowCountResp)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void ClearNewFollowCountResp::CopyFrom(const ClearNewFollowCountResp& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:ugc.interactive.ClearNewFollowCountResp)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ClearNewFollowCountResp::IsInitialized() const {
  return true;
}

void ClearNewFollowCountResp::Swap(ClearNewFollowCountResp* other) {
  if (other == this) return;
  InternalSwap(other);
}
void ClearNewFollowCountResp::InternalSwap(ClearNewFollowCountResp* other) {
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata ClearNewFollowCountResp::GetMetadata() const {
  protobuf_interactive_2eproto::protobuf_AssignDescriptorsOnce();
  return protobuf_interactive_2eproto::file_level_metadata[16];
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// ClearNewFollowCountResp

#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int AddFollowCountReq::kUserIdFieldNumber;
const int AddFollowCountReq::kCountFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

AddFollowCountReq::AddFollowCountReq()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (GOOGLE_PREDICT_TRUE(this != internal_default_instance())) {
    protobuf_interactive_2eproto::InitDefaults();
  }
  SharedCtor();
  // @@protoc_insertion_point(constructor:ugc.interactive.AddFollowCountReq)
}
AddFollowCountReq::AddFollowCountReq(const AddFollowCountReq& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      _cached_size_(0) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::memcpy(&user_id_, &from.user_id_,
    reinterpret_cast<char*>(&count_) -
    reinterpret_cast<char*>(&user_id_) + sizeof(count_));
  // @@protoc_insertion_point(copy_constructor:ugc.interactive.AddFollowCountReq)
}

void AddFollowCountReq::SharedCtor() {
  ::memset(&user_id_, 0, reinterpret_cast<char*>(&count_) -
    reinterpret_cast<char*>(&user_id_) + sizeof(count_));
  _cached_size_ = 0;
}

AddFollowCountReq::~AddFollowCountReq() {
  // @@protoc_insertion_point(destructor:ugc.interactive.AddFollowCountReq)
  SharedDtor();
}

void AddFollowCountReq::SharedDtor() {
}

void AddFollowCountReq::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* AddFollowCountReq::descriptor() {
  protobuf_interactive_2eproto::protobuf_AssignDescriptorsOnce();
  return protobuf_interactive_2eproto::file_level_metadata[17].descriptor;
}

const AddFollowCountReq& AddFollowCountReq::default_instance() {
  protobuf_interactive_2eproto::InitDefaults();
  return *internal_default_instance();
}

AddFollowCountReq* AddFollowCountReq::New(::google::protobuf::Arena* arena) const {
  AddFollowCountReq* n = new AddFollowCountReq;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void AddFollowCountReq::Clear() {
// @@protoc_insertion_point(message_clear_start:ugc.interactive.AddFollowCountReq)
  ::memset(&user_id_, 0, reinterpret_cast<char*>(&count_) -
    reinterpret_cast<char*>(&user_id_) + sizeof(count_));
}

bool AddFollowCountReq::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:ugc.interactive.AddFollowCountReq)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // uint32 user_id = 1;
      case 1: {
        if (tag == 8u) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_UINT32>(
                 input, &user_id_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // uint32 count = 2;
      case 2: {
        if (tag == 16u) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_UINT32>(
                 input, &count_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:ugc.interactive.AddFollowCountReq)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:ugc.interactive.AddFollowCountReq)
  return false;
#undef DO_
}

void AddFollowCountReq::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:ugc.interactive.AddFollowCountReq)
  // uint32 user_id = 1;
  if (this->user_id() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt32(1, this->user_id(), output);
  }

  // uint32 count = 2;
  if (this->count() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt32(2, this->count(), output);
  }

  // @@protoc_insertion_point(serialize_end:ugc.interactive.AddFollowCountReq)
}

::google::protobuf::uint8* AddFollowCountReq::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic;  // Unused
  // @@protoc_insertion_point(serialize_to_array_start:ugc.interactive.AddFollowCountReq)
  // uint32 user_id = 1;
  if (this->user_id() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt32ToArray(1, this->user_id(), target);
  }

  // uint32 count = 2;
  if (this->count() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt32ToArray(2, this->count(), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:ugc.interactive.AddFollowCountReq)
  return target;
}

size_t AddFollowCountReq::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:ugc.interactive.AddFollowCountReq)
  size_t total_size = 0;

  // uint32 user_id = 1;
  if (this->user_id() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::UInt32Size(
        this->user_id());
  }

  // uint32 count = 2;
  if (this->count() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::UInt32Size(
        this->count());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void AddFollowCountReq::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:ugc.interactive.AddFollowCountReq)
  GOOGLE_DCHECK_NE(&from, this);
  const AddFollowCountReq* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const AddFollowCountReq>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:ugc.interactive.AddFollowCountReq)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:ugc.interactive.AddFollowCountReq)
    MergeFrom(*source);
  }
}

void AddFollowCountReq::MergeFrom(const AddFollowCountReq& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:ugc.interactive.AddFollowCountReq)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  if (from.user_id() != 0) {
    set_user_id(from.user_id());
  }
  if (from.count() != 0) {
    set_count(from.count());
  }
}

void AddFollowCountReq::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:ugc.interactive.AddFollowCountReq)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void AddFollowCountReq::CopyFrom(const AddFollowCountReq& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:ugc.interactive.AddFollowCountReq)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool AddFollowCountReq::IsInitialized() const {
  return true;
}

void AddFollowCountReq::Swap(AddFollowCountReq* other) {
  if (other == this) return;
  InternalSwap(other);
}
void AddFollowCountReq::InternalSwap(AddFollowCountReq* other) {
  std::swap(user_id_, other->user_id_);
  std::swap(count_, other->count_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata AddFollowCountReq::GetMetadata() const {
  protobuf_interactive_2eproto::protobuf_AssignDescriptorsOnce();
  return protobuf_interactive_2eproto::file_level_metadata[17];
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// AddFollowCountReq

// uint32 user_id = 1;
void AddFollowCountReq::clear_user_id() {
  user_id_ = 0u;
}
::google::protobuf::uint32 AddFollowCountReq::user_id() const {
  // @@protoc_insertion_point(field_get:ugc.interactive.AddFollowCountReq.user_id)
  return user_id_;
}
void AddFollowCountReq::set_user_id(::google::protobuf::uint32 value) {
  
  user_id_ = value;
  // @@protoc_insertion_point(field_set:ugc.interactive.AddFollowCountReq.user_id)
}

// uint32 count = 2;
void AddFollowCountReq::clear_count() {
  count_ = 0u;
}
::google::protobuf::uint32 AddFollowCountReq::count() const {
  // @@protoc_insertion_point(field_get:ugc.interactive.AddFollowCountReq.count)
  return count_;
}
void AddFollowCountReq::set_count(::google::protobuf::uint32 value) {
  
  count_ = value;
  // @@protoc_insertion_point(field_set:ugc.interactive.AddFollowCountReq.count)
}

#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

AddFollowCountResp::AddFollowCountResp()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (GOOGLE_PREDICT_TRUE(this != internal_default_instance())) {
    protobuf_interactive_2eproto::InitDefaults();
  }
  SharedCtor();
  // @@protoc_insertion_point(constructor:ugc.interactive.AddFollowCountResp)
}
AddFollowCountResp::AddFollowCountResp(const AddFollowCountResp& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      _cached_size_(0) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:ugc.interactive.AddFollowCountResp)
}

void AddFollowCountResp::SharedCtor() {
  _cached_size_ = 0;
}

AddFollowCountResp::~AddFollowCountResp() {
  // @@protoc_insertion_point(destructor:ugc.interactive.AddFollowCountResp)
  SharedDtor();
}

void AddFollowCountResp::SharedDtor() {
}

void AddFollowCountResp::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* AddFollowCountResp::descriptor() {
  protobuf_interactive_2eproto::protobuf_AssignDescriptorsOnce();
  return protobuf_interactive_2eproto::file_level_metadata[18].descriptor;
}

const AddFollowCountResp& AddFollowCountResp::default_instance() {
  protobuf_interactive_2eproto::InitDefaults();
  return *internal_default_instance();
}

AddFollowCountResp* AddFollowCountResp::New(::google::protobuf::Arena* arena) const {
  AddFollowCountResp* n = new AddFollowCountResp;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void AddFollowCountResp::Clear() {
// @@protoc_insertion_point(message_clear_start:ugc.interactive.AddFollowCountResp)
}

bool AddFollowCountResp::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:ugc.interactive.AddFollowCountResp)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
  handle_unusual:
    if (tag == 0 ||
        ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
        ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
      goto success;
    }
    DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
  }
success:
  // @@protoc_insertion_point(parse_success:ugc.interactive.AddFollowCountResp)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:ugc.interactive.AddFollowCountResp)
  return false;
#undef DO_
}

void AddFollowCountResp::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:ugc.interactive.AddFollowCountResp)
  // @@protoc_insertion_point(serialize_end:ugc.interactive.AddFollowCountResp)
}

::google::protobuf::uint8* AddFollowCountResp::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic;  // Unused
  // @@protoc_insertion_point(serialize_to_array_start:ugc.interactive.AddFollowCountResp)
  // @@protoc_insertion_point(serialize_to_array_end:ugc.interactive.AddFollowCountResp)
  return target;
}

size_t AddFollowCountResp::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:ugc.interactive.AddFollowCountResp)
  size_t total_size = 0;

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void AddFollowCountResp::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:ugc.interactive.AddFollowCountResp)
  GOOGLE_DCHECK_NE(&from, this);
  const AddFollowCountResp* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const AddFollowCountResp>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:ugc.interactive.AddFollowCountResp)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:ugc.interactive.AddFollowCountResp)
    MergeFrom(*source);
  }
}

void AddFollowCountResp::MergeFrom(const AddFollowCountResp& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:ugc.interactive.AddFollowCountResp)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
}

void AddFollowCountResp::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:ugc.interactive.AddFollowCountResp)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void AddFollowCountResp::CopyFrom(const AddFollowCountResp& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:ugc.interactive.AddFollowCountResp)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool AddFollowCountResp::IsInitialized() const {
  return true;
}

void AddFollowCountResp::Swap(AddFollowCountResp* other) {
  if (other == this) return;
  InternalSwap(other);
}
void AddFollowCountResp::InternalSwap(AddFollowCountResp* other) {
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata AddFollowCountResp::GetMetadata() const {
  protobuf_interactive_2eproto::protobuf_AssignDescriptorsOnce();
  return protobuf_interactive_2eproto::file_level_metadata[18];
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// AddFollowCountResp

#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int UpdateFollowingStreamReq::kUidListFieldNumber;
const int UpdateFollowingStreamReq::kUpdateInfoFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

UpdateFollowingStreamReq::UpdateFollowingStreamReq()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (GOOGLE_PREDICT_TRUE(this != internal_default_instance())) {
    protobuf_interactive_2eproto::InitDefaults();
  }
  SharedCtor();
  // @@protoc_insertion_point(constructor:ugc.interactive.UpdateFollowingStreamReq)
}
UpdateFollowingStreamReq::UpdateFollowingStreamReq(const UpdateFollowingStreamReq& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      uid_list_(from.uid_list_),
      _cached_size_(0) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  if (from.has_update_info()) {
    update_info_ = new ::ugc::interactive::FollowingStreamUpdateInfo(*from.update_info_);
  } else {
    update_info_ = NULL;
  }
  // @@protoc_insertion_point(copy_constructor:ugc.interactive.UpdateFollowingStreamReq)
}

void UpdateFollowingStreamReq::SharedCtor() {
  update_info_ = NULL;
  _cached_size_ = 0;
}

UpdateFollowingStreamReq::~UpdateFollowingStreamReq() {
  // @@protoc_insertion_point(destructor:ugc.interactive.UpdateFollowingStreamReq)
  SharedDtor();
}

void UpdateFollowingStreamReq::SharedDtor() {
  if (this != internal_default_instance()) {
    delete update_info_;
  }
}

void UpdateFollowingStreamReq::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* UpdateFollowingStreamReq::descriptor() {
  protobuf_interactive_2eproto::protobuf_AssignDescriptorsOnce();
  return protobuf_interactive_2eproto::file_level_metadata[19].descriptor;
}

const UpdateFollowingStreamReq& UpdateFollowingStreamReq::default_instance() {
  protobuf_interactive_2eproto::InitDefaults();
  return *internal_default_instance();
}

UpdateFollowingStreamReq* UpdateFollowingStreamReq::New(::google::protobuf::Arena* arena) const {
  UpdateFollowingStreamReq* n = new UpdateFollowingStreamReq;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void UpdateFollowingStreamReq::Clear() {
// @@protoc_insertion_point(message_clear_start:ugc.interactive.UpdateFollowingStreamReq)
  uid_list_.Clear();
  if (GetArenaNoVirtual() == NULL && update_info_ != NULL) {
    delete update_info_;
  }
  update_info_ = NULL;
}

bool UpdateFollowingStreamReq::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:ugc.interactive.UpdateFollowingStreamReq)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // repeated uint32 uid_list = 1;
      case 1: {
        if (tag == 10u) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_UINT32>(
                 input, this->mutable_uid_list())));
        } else if (tag == 8u) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_UINT32>(
                 1, 10u, input, this->mutable_uid_list())));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .ugc.interactive.FollowingStreamUpdateInfo update_info = 2;
      case 2: {
        if (tag == 18u) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_update_info()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:ugc.interactive.UpdateFollowingStreamReq)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:ugc.interactive.UpdateFollowingStreamReq)
  return false;
#undef DO_
}

void UpdateFollowingStreamReq::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:ugc.interactive.UpdateFollowingStreamReq)
  // repeated uint32 uid_list = 1;
  if (this->uid_list_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(1, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(_uid_list_cached_byte_size_);
  }
  for (int i = 0; i < this->uid_list_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt32NoTag(
      this->uid_list(i), output);
  }

  // .ugc.interactive.FollowingStreamUpdateInfo update_info = 2;
  if (this->has_update_info()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      2, *this->update_info_, output);
  }

  // @@protoc_insertion_point(serialize_end:ugc.interactive.UpdateFollowingStreamReq)
}

::google::protobuf::uint8* UpdateFollowingStreamReq::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic;  // Unused
  // @@protoc_insertion_point(serialize_to_array_start:ugc.interactive.UpdateFollowingStreamReq)
  // repeated uint32 uid_list = 1;
  if (this->uid_list_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      1,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
      _uid_list_cached_byte_size_, target);
  }
  for (int i = 0; i < this->uid_list_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteUInt32NoTagToArray(this->uid_list(i), target);
  }

  // .ugc.interactive.FollowingStreamUpdateInfo update_info = 2;
  if (this->has_update_info()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        2, *this->update_info_, false, target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:ugc.interactive.UpdateFollowingStreamReq)
  return target;
}

size_t UpdateFollowingStreamReq::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:ugc.interactive.UpdateFollowingStreamReq)
  size_t total_size = 0;

  // repeated uint32 uid_list = 1;
  {
    size_t data_size = ::google::protobuf::internal::WireFormatLite::
      UInt32Size(this->uid_list_);
    if (data_size > 0) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(data_size);
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _uid_list_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // .ugc.interactive.FollowingStreamUpdateInfo update_info = 2;
  if (this->has_update_info()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->update_info_);
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void UpdateFollowingStreamReq::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:ugc.interactive.UpdateFollowingStreamReq)
  GOOGLE_DCHECK_NE(&from, this);
  const UpdateFollowingStreamReq* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const UpdateFollowingStreamReq>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:ugc.interactive.UpdateFollowingStreamReq)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:ugc.interactive.UpdateFollowingStreamReq)
    MergeFrom(*source);
  }
}

void UpdateFollowingStreamReq::MergeFrom(const UpdateFollowingStreamReq& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:ugc.interactive.UpdateFollowingStreamReq)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  uid_list_.MergeFrom(from.uid_list_);
  if (from.has_update_info()) {
    mutable_update_info()->::ugc::interactive::FollowingStreamUpdateInfo::MergeFrom(from.update_info());
  }
}

void UpdateFollowingStreamReq::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:ugc.interactive.UpdateFollowingStreamReq)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void UpdateFollowingStreamReq::CopyFrom(const UpdateFollowingStreamReq& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:ugc.interactive.UpdateFollowingStreamReq)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool UpdateFollowingStreamReq::IsInitialized() const {
  return true;
}

void UpdateFollowingStreamReq::Swap(UpdateFollowingStreamReq* other) {
  if (other == this) return;
  InternalSwap(other);
}
void UpdateFollowingStreamReq::InternalSwap(UpdateFollowingStreamReq* other) {
  uid_list_.UnsafeArenaSwap(&other->uid_list_);
  std::swap(update_info_, other->update_info_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata UpdateFollowingStreamReq::GetMetadata() const {
  protobuf_interactive_2eproto::protobuf_AssignDescriptorsOnce();
  return protobuf_interactive_2eproto::file_level_metadata[19];
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// UpdateFollowingStreamReq

// repeated uint32 uid_list = 1;
int UpdateFollowingStreamReq::uid_list_size() const {
  return uid_list_.size();
}
void UpdateFollowingStreamReq::clear_uid_list() {
  uid_list_.Clear();
}
::google::protobuf::uint32 UpdateFollowingStreamReq::uid_list(int index) const {
  // @@protoc_insertion_point(field_get:ugc.interactive.UpdateFollowingStreamReq.uid_list)
  return uid_list_.Get(index);
}
void UpdateFollowingStreamReq::set_uid_list(int index, ::google::protobuf::uint32 value) {
  uid_list_.Set(index, value);
  // @@protoc_insertion_point(field_set:ugc.interactive.UpdateFollowingStreamReq.uid_list)
}
void UpdateFollowingStreamReq::add_uid_list(::google::protobuf::uint32 value) {
  uid_list_.Add(value);
  // @@protoc_insertion_point(field_add:ugc.interactive.UpdateFollowingStreamReq.uid_list)
}
const ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >&
UpdateFollowingStreamReq::uid_list() const {
  // @@protoc_insertion_point(field_list:ugc.interactive.UpdateFollowingStreamReq.uid_list)
  return uid_list_;
}
::google::protobuf::RepeatedField< ::google::protobuf::uint32 >*
UpdateFollowingStreamReq::mutable_uid_list() {
  // @@protoc_insertion_point(field_mutable_list:ugc.interactive.UpdateFollowingStreamReq.uid_list)
  return &uid_list_;
}

// .ugc.interactive.FollowingStreamUpdateInfo update_info = 2;
bool UpdateFollowingStreamReq::has_update_info() const {
  return this != internal_default_instance() && update_info_ != NULL;
}
void UpdateFollowingStreamReq::clear_update_info() {
  if (GetArenaNoVirtual() == NULL && update_info_ != NULL) delete update_info_;
  update_info_ = NULL;
}
const ::ugc::interactive::FollowingStreamUpdateInfo& UpdateFollowingStreamReq::update_info() const {
  // @@protoc_insertion_point(field_get:ugc.interactive.UpdateFollowingStreamReq.update_info)
  return update_info_ != NULL ? *update_info_
                         : *::ugc::interactive::FollowingStreamUpdateInfo::internal_default_instance();
}
::ugc::interactive::FollowingStreamUpdateInfo* UpdateFollowingStreamReq::mutable_update_info() {
  
  if (update_info_ == NULL) {
    update_info_ = new ::ugc::interactive::FollowingStreamUpdateInfo;
  }
  // @@protoc_insertion_point(field_mutable:ugc.interactive.UpdateFollowingStreamReq.update_info)
  return update_info_;
}
::ugc::interactive::FollowingStreamUpdateInfo* UpdateFollowingStreamReq::release_update_info() {
  // @@protoc_insertion_point(field_release:ugc.interactive.UpdateFollowingStreamReq.update_info)
  
  ::ugc::interactive::FollowingStreamUpdateInfo* temp = update_info_;
  update_info_ = NULL;
  return temp;
}
void UpdateFollowingStreamReq::set_allocated_update_info(::ugc::interactive::FollowingStreamUpdateInfo* update_info) {
  delete update_info_;
  update_info_ = update_info;
  if (update_info) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:ugc.interactive.UpdateFollowingStreamReq.update_info)
}

#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

UpdateFollowingStreamResp::UpdateFollowingStreamResp()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (GOOGLE_PREDICT_TRUE(this != internal_default_instance())) {
    protobuf_interactive_2eproto::InitDefaults();
  }
  SharedCtor();
  // @@protoc_insertion_point(constructor:ugc.interactive.UpdateFollowingStreamResp)
}
UpdateFollowingStreamResp::UpdateFollowingStreamResp(const UpdateFollowingStreamResp& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      _cached_size_(0) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:ugc.interactive.UpdateFollowingStreamResp)
}

void UpdateFollowingStreamResp::SharedCtor() {
  _cached_size_ = 0;
}

UpdateFollowingStreamResp::~UpdateFollowingStreamResp() {
  // @@protoc_insertion_point(destructor:ugc.interactive.UpdateFollowingStreamResp)
  SharedDtor();
}

void UpdateFollowingStreamResp::SharedDtor() {
}

void UpdateFollowingStreamResp::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* UpdateFollowingStreamResp::descriptor() {
  protobuf_interactive_2eproto::protobuf_AssignDescriptorsOnce();
  return protobuf_interactive_2eproto::file_level_metadata[20].descriptor;
}

const UpdateFollowingStreamResp& UpdateFollowingStreamResp::default_instance() {
  protobuf_interactive_2eproto::InitDefaults();
  return *internal_default_instance();
}

UpdateFollowingStreamResp* UpdateFollowingStreamResp::New(::google::protobuf::Arena* arena) const {
  UpdateFollowingStreamResp* n = new UpdateFollowingStreamResp;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void UpdateFollowingStreamResp::Clear() {
// @@protoc_insertion_point(message_clear_start:ugc.interactive.UpdateFollowingStreamResp)
}

bool UpdateFollowingStreamResp::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:ugc.interactive.UpdateFollowingStreamResp)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
  handle_unusual:
    if (tag == 0 ||
        ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
        ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
      goto success;
    }
    DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
  }
success:
  // @@protoc_insertion_point(parse_success:ugc.interactive.UpdateFollowingStreamResp)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:ugc.interactive.UpdateFollowingStreamResp)
  return false;
#undef DO_
}

void UpdateFollowingStreamResp::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:ugc.interactive.UpdateFollowingStreamResp)
  // @@protoc_insertion_point(serialize_end:ugc.interactive.UpdateFollowingStreamResp)
}

::google::protobuf::uint8* UpdateFollowingStreamResp::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic;  // Unused
  // @@protoc_insertion_point(serialize_to_array_start:ugc.interactive.UpdateFollowingStreamResp)
  // @@protoc_insertion_point(serialize_to_array_end:ugc.interactive.UpdateFollowingStreamResp)
  return target;
}

size_t UpdateFollowingStreamResp::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:ugc.interactive.UpdateFollowingStreamResp)
  size_t total_size = 0;

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void UpdateFollowingStreamResp::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:ugc.interactive.UpdateFollowingStreamResp)
  GOOGLE_DCHECK_NE(&from, this);
  const UpdateFollowingStreamResp* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const UpdateFollowingStreamResp>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:ugc.interactive.UpdateFollowingStreamResp)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:ugc.interactive.UpdateFollowingStreamResp)
    MergeFrom(*source);
  }
}

void UpdateFollowingStreamResp::MergeFrom(const UpdateFollowingStreamResp& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:ugc.interactive.UpdateFollowingStreamResp)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
}

void UpdateFollowingStreamResp::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:ugc.interactive.UpdateFollowingStreamResp)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void UpdateFollowingStreamResp::CopyFrom(const UpdateFollowingStreamResp& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:ugc.interactive.UpdateFollowingStreamResp)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool UpdateFollowingStreamResp::IsInitialized() const {
  return true;
}

void UpdateFollowingStreamResp::Swap(UpdateFollowingStreamResp* other) {
  if (other == this) return;
  InternalSwap(other);
}
void UpdateFollowingStreamResp::InternalSwap(UpdateFollowingStreamResp* other) {
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata UpdateFollowingStreamResp::GetMetadata() const {
  protobuf_interactive_2eproto::protobuf_AssignDescriptorsOnce();
  return protobuf_interactive_2eproto::file_level_metadata[20];
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// UpdateFollowingStreamResp

#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int AddUnreadCountsReq::kUserIdFieldNumber;
const int AddUnreadCountsReq::kNewCommentCountFieldNumber;
const int AddUnreadCountsReq::kNewAttitudeCountFieldNumber;
const int AddUnreadCountsReq::kNewFollowerCountFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

AddUnreadCountsReq::AddUnreadCountsReq()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (GOOGLE_PREDICT_TRUE(this != internal_default_instance())) {
    protobuf_interactive_2eproto::InitDefaults();
  }
  SharedCtor();
  // @@protoc_insertion_point(constructor:ugc.interactive.AddUnreadCountsReq)
}
AddUnreadCountsReq::AddUnreadCountsReq(const AddUnreadCountsReq& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      _cached_size_(0) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::memcpy(&user_id_, &from.user_id_,
    reinterpret_cast<char*>(&new_follower_count_) -
    reinterpret_cast<char*>(&user_id_) + sizeof(new_follower_count_));
  // @@protoc_insertion_point(copy_constructor:ugc.interactive.AddUnreadCountsReq)
}

void AddUnreadCountsReq::SharedCtor() {
  ::memset(&user_id_, 0, reinterpret_cast<char*>(&new_follower_count_) -
    reinterpret_cast<char*>(&user_id_) + sizeof(new_follower_count_));
  _cached_size_ = 0;
}

AddUnreadCountsReq::~AddUnreadCountsReq() {
  // @@protoc_insertion_point(destructor:ugc.interactive.AddUnreadCountsReq)
  SharedDtor();
}

void AddUnreadCountsReq::SharedDtor() {
}

void AddUnreadCountsReq::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* AddUnreadCountsReq::descriptor() {
  protobuf_interactive_2eproto::protobuf_AssignDescriptorsOnce();
  return protobuf_interactive_2eproto::file_level_metadata[21].descriptor;
}

const AddUnreadCountsReq& AddUnreadCountsReq::default_instance() {
  protobuf_interactive_2eproto::InitDefaults();
  return *internal_default_instance();
}

AddUnreadCountsReq* AddUnreadCountsReq::New(::google::protobuf::Arena* arena) const {
  AddUnreadCountsReq* n = new AddUnreadCountsReq;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void AddUnreadCountsReq::Clear() {
// @@protoc_insertion_point(message_clear_start:ugc.interactive.AddUnreadCountsReq)
  ::memset(&user_id_, 0, reinterpret_cast<char*>(&new_follower_count_) -
    reinterpret_cast<char*>(&user_id_) + sizeof(new_follower_count_));
}

bool AddUnreadCountsReq::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:ugc.interactive.AddUnreadCountsReq)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // uint32 user_id = 1;
      case 1: {
        if (tag == 8u) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_UINT32>(
                 input, &user_id_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int32 new_comment_count = 2;
      case 2: {
        if (tag == 16u) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &new_comment_count_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int32 new_attitude_count = 3;
      case 3: {
        if (tag == 24u) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &new_attitude_count_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int32 new_follower_count = 4;
      case 4: {
        if (tag == 32u) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &new_follower_count_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:ugc.interactive.AddUnreadCountsReq)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:ugc.interactive.AddUnreadCountsReq)
  return false;
#undef DO_
}

void AddUnreadCountsReq::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:ugc.interactive.AddUnreadCountsReq)
  // uint32 user_id = 1;
  if (this->user_id() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt32(1, this->user_id(), output);
  }

  // int32 new_comment_count = 2;
  if (this->new_comment_count() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(2, this->new_comment_count(), output);
  }

  // int32 new_attitude_count = 3;
  if (this->new_attitude_count() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(3, this->new_attitude_count(), output);
  }

  // int32 new_follower_count = 4;
  if (this->new_follower_count() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(4, this->new_follower_count(), output);
  }

  // @@protoc_insertion_point(serialize_end:ugc.interactive.AddUnreadCountsReq)
}

::google::protobuf::uint8* AddUnreadCountsReq::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic;  // Unused
  // @@protoc_insertion_point(serialize_to_array_start:ugc.interactive.AddUnreadCountsReq)
  // uint32 user_id = 1;
  if (this->user_id() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt32ToArray(1, this->user_id(), target);
  }

  // int32 new_comment_count = 2;
  if (this->new_comment_count() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(2, this->new_comment_count(), target);
  }

  // int32 new_attitude_count = 3;
  if (this->new_attitude_count() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(3, this->new_attitude_count(), target);
  }

  // int32 new_follower_count = 4;
  if (this->new_follower_count() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(4, this->new_follower_count(), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:ugc.interactive.AddUnreadCountsReq)
  return target;
}

size_t AddUnreadCountsReq::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:ugc.interactive.AddUnreadCountsReq)
  size_t total_size = 0;

  // uint32 user_id = 1;
  if (this->user_id() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::UInt32Size(
        this->user_id());
  }

  // int32 new_comment_count = 2;
  if (this->new_comment_count() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->new_comment_count());
  }

  // int32 new_attitude_count = 3;
  if (this->new_attitude_count() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->new_attitude_count());
  }

  // int32 new_follower_count = 4;
  if (this->new_follower_count() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->new_follower_count());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void AddUnreadCountsReq::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:ugc.interactive.AddUnreadCountsReq)
  GOOGLE_DCHECK_NE(&from, this);
  const AddUnreadCountsReq* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const AddUnreadCountsReq>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:ugc.interactive.AddUnreadCountsReq)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:ugc.interactive.AddUnreadCountsReq)
    MergeFrom(*source);
  }
}

void AddUnreadCountsReq::MergeFrom(const AddUnreadCountsReq& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:ugc.interactive.AddUnreadCountsReq)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  if (from.user_id() != 0) {
    set_user_id(from.user_id());
  }
  if (from.new_comment_count() != 0) {
    set_new_comment_count(from.new_comment_count());
  }
  if (from.new_attitude_count() != 0) {
    set_new_attitude_count(from.new_attitude_count());
  }
  if (from.new_follower_count() != 0) {
    set_new_follower_count(from.new_follower_count());
  }
}

void AddUnreadCountsReq::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:ugc.interactive.AddUnreadCountsReq)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void AddUnreadCountsReq::CopyFrom(const AddUnreadCountsReq& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:ugc.interactive.AddUnreadCountsReq)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool AddUnreadCountsReq::IsInitialized() const {
  return true;
}

void AddUnreadCountsReq::Swap(AddUnreadCountsReq* other) {
  if (other == this) return;
  InternalSwap(other);
}
void AddUnreadCountsReq::InternalSwap(AddUnreadCountsReq* other) {
  std::swap(user_id_, other->user_id_);
  std::swap(new_comment_count_, other->new_comment_count_);
  std::swap(new_attitude_count_, other->new_attitude_count_);
  std::swap(new_follower_count_, other->new_follower_count_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata AddUnreadCountsReq::GetMetadata() const {
  protobuf_interactive_2eproto::protobuf_AssignDescriptorsOnce();
  return protobuf_interactive_2eproto::file_level_metadata[21];
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// AddUnreadCountsReq

// uint32 user_id = 1;
void AddUnreadCountsReq::clear_user_id() {
  user_id_ = 0u;
}
::google::protobuf::uint32 AddUnreadCountsReq::user_id() const {
  // @@protoc_insertion_point(field_get:ugc.interactive.AddUnreadCountsReq.user_id)
  return user_id_;
}
void AddUnreadCountsReq::set_user_id(::google::protobuf::uint32 value) {
  
  user_id_ = value;
  // @@protoc_insertion_point(field_set:ugc.interactive.AddUnreadCountsReq.user_id)
}

// int32 new_comment_count = 2;
void AddUnreadCountsReq::clear_new_comment_count() {
  new_comment_count_ = 0;
}
::google::protobuf::int32 AddUnreadCountsReq::new_comment_count() const {
  // @@protoc_insertion_point(field_get:ugc.interactive.AddUnreadCountsReq.new_comment_count)
  return new_comment_count_;
}
void AddUnreadCountsReq::set_new_comment_count(::google::protobuf::int32 value) {
  
  new_comment_count_ = value;
  // @@protoc_insertion_point(field_set:ugc.interactive.AddUnreadCountsReq.new_comment_count)
}

// int32 new_attitude_count = 3;
void AddUnreadCountsReq::clear_new_attitude_count() {
  new_attitude_count_ = 0;
}
::google::protobuf::int32 AddUnreadCountsReq::new_attitude_count() const {
  // @@protoc_insertion_point(field_get:ugc.interactive.AddUnreadCountsReq.new_attitude_count)
  return new_attitude_count_;
}
void AddUnreadCountsReq::set_new_attitude_count(::google::protobuf::int32 value) {
  
  new_attitude_count_ = value;
  // @@protoc_insertion_point(field_set:ugc.interactive.AddUnreadCountsReq.new_attitude_count)
}

// int32 new_follower_count = 4;
void AddUnreadCountsReq::clear_new_follower_count() {
  new_follower_count_ = 0;
}
::google::protobuf::int32 AddUnreadCountsReq::new_follower_count() const {
  // @@protoc_insertion_point(field_get:ugc.interactive.AddUnreadCountsReq.new_follower_count)
  return new_follower_count_;
}
void AddUnreadCountsReq::set_new_follower_count(::google::protobuf::int32 value) {
  
  new_follower_count_ = value;
  // @@protoc_insertion_point(field_set:ugc.interactive.AddUnreadCountsReq.new_follower_count)
}

#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

AddUnreadCountsResp::AddUnreadCountsResp()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (GOOGLE_PREDICT_TRUE(this != internal_default_instance())) {
    protobuf_interactive_2eproto::InitDefaults();
  }
  SharedCtor();
  // @@protoc_insertion_point(constructor:ugc.interactive.AddUnreadCountsResp)
}
AddUnreadCountsResp::AddUnreadCountsResp(const AddUnreadCountsResp& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      _cached_size_(0) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:ugc.interactive.AddUnreadCountsResp)
}

void AddUnreadCountsResp::SharedCtor() {
  _cached_size_ = 0;
}

AddUnreadCountsResp::~AddUnreadCountsResp() {
  // @@protoc_insertion_point(destructor:ugc.interactive.AddUnreadCountsResp)
  SharedDtor();
}

void AddUnreadCountsResp::SharedDtor() {
}

void AddUnreadCountsResp::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* AddUnreadCountsResp::descriptor() {
  protobuf_interactive_2eproto::protobuf_AssignDescriptorsOnce();
  return protobuf_interactive_2eproto::file_level_metadata[22].descriptor;
}

const AddUnreadCountsResp& AddUnreadCountsResp::default_instance() {
  protobuf_interactive_2eproto::InitDefaults();
  return *internal_default_instance();
}

AddUnreadCountsResp* AddUnreadCountsResp::New(::google::protobuf::Arena* arena) const {
  AddUnreadCountsResp* n = new AddUnreadCountsResp;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void AddUnreadCountsResp::Clear() {
// @@protoc_insertion_point(message_clear_start:ugc.interactive.AddUnreadCountsResp)
}

bool AddUnreadCountsResp::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:ugc.interactive.AddUnreadCountsResp)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
  handle_unusual:
    if (tag == 0 ||
        ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
        ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
      goto success;
    }
    DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
  }
success:
  // @@protoc_insertion_point(parse_success:ugc.interactive.AddUnreadCountsResp)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:ugc.interactive.AddUnreadCountsResp)
  return false;
#undef DO_
}

void AddUnreadCountsResp::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:ugc.interactive.AddUnreadCountsResp)
  // @@protoc_insertion_point(serialize_end:ugc.interactive.AddUnreadCountsResp)
}

::google::protobuf::uint8* AddUnreadCountsResp::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic;  // Unused
  // @@protoc_insertion_point(serialize_to_array_start:ugc.interactive.AddUnreadCountsResp)
  // @@protoc_insertion_point(serialize_to_array_end:ugc.interactive.AddUnreadCountsResp)
  return target;
}

size_t AddUnreadCountsResp::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:ugc.interactive.AddUnreadCountsResp)
  size_t total_size = 0;

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void AddUnreadCountsResp::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:ugc.interactive.AddUnreadCountsResp)
  GOOGLE_DCHECK_NE(&from, this);
  const AddUnreadCountsResp* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const AddUnreadCountsResp>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:ugc.interactive.AddUnreadCountsResp)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:ugc.interactive.AddUnreadCountsResp)
    MergeFrom(*source);
  }
}

void AddUnreadCountsResp::MergeFrom(const AddUnreadCountsResp& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:ugc.interactive.AddUnreadCountsResp)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
}

void AddUnreadCountsResp::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:ugc.interactive.AddUnreadCountsResp)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void AddUnreadCountsResp::CopyFrom(const AddUnreadCountsResp& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:ugc.interactive.AddUnreadCountsResp)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool AddUnreadCountsResp::IsInitialized() const {
  return true;
}

void AddUnreadCountsResp::Swap(AddUnreadCountsResp* other) {
  if (other == this) return;
  InternalSwap(other);
}
void AddUnreadCountsResp::InternalSwap(AddUnreadCountsResp* other) {
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata AddUnreadCountsResp::GetMetadata() const {
  protobuf_interactive_2eproto::protobuf_AssignDescriptorsOnce();
  return protobuf_interactive_2eproto::file_level_metadata[22];
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// AddUnreadCountsResp

#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// @@protoc_insertion_point(namespace_scope)

}  // namespace interactive
}  // namespace ugc

// @@protoc_insertion_point(global_scope)
