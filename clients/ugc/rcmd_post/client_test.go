package rcmd_post

import (
	"context"
	"fmt"
	antiCli "golang.52tt.com/clients/anti"
	antCli "golang.52tt.com/clients/antispamlogic"
	lbsCli "golang.52tt.com/clients/lbs-supervise"
	friendShipCli "golang.52tt.com/clients/ugc/friendship"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/protocol/services/lbs/lbs_supervise"
	rcmdPostPb "golang.52tt.com/protocol/services/ugc/rcmd_post"
	"google.golang.org/grpc"
	"os/exec"
	"testing"
)

var cli *Client
func init() {
	log.SetLevel(log.DebugLevel)
	var err error
	cli, err = NewClient(grpc.WithBlock())
	if err != nil {
		panic(err)
	}
}

func Test(t *testing.T) {
	resp, err := cli.GetRecommendStream(context.Background(), &rcmdPostPb.GetRecommendStreamReq{
		Limit: 20,
		Tags: []uint32{},
	})

	fmt.Println(resp, err)
}

func Test2(t *testing.T) {
	exec.Command("/bin/bash","-c","/Users/<USER>/bin/dns230.sh").Run()
	defer exec.Command("/bin/bash","-c","/Users/<USER>/bin/dns8.sh").Run()
	resp, err := cli.GetSystemRecommendStream(context.Background(), &rcmdPostPb.GetSystemRecommendStreamReq{
		Limit: 20,
	})
	fmt.Println(resp, err)
}



// 城市
//GetLocationbyIpReq
func TestGetLocationbyIpReq(t *testing.T) {
	uids := []uint32{1,2,3,4}
	cli:=antiCli.NewClient()
	rsp, err := cli.BatchGetUserLastLoginInfo(context.Background(), 0, uids)
	if err != nil {
		log.Errorf("antiClient.BatchGetUserLastLoginInfo err: %s, uids:%+v", err.Error(), uids)
	}
	userIps := make(map[uint32]string)
	ips := make([]string, 0)
	for _, info := range rsp.GetInfoList() {
		userIps[info.GetUid()] = info.GetClientIp()
		ips = append(ips, info.GetClientIp())
	}

	req := &lbs_supervise.GetLocationbyIpReq{
		IpList: ips,
	}
	cli2,e:=lbsCli.NewClient()
	if e!=nil{
		log.Errorln("e=",e)
	}
	locRsp, err := cli2.GetLocationbyIp(context.Background(), req)
	if err != nil {
		log.Errorf("GetLocationbyIp err: %s, req:%+v", err.Error(), req)
	}
	ip2loc := make(map[string]string)
	for _, info := range locRsp.GetDetailList() {
		if info.GetExist() {
			ip2loc[info.GetIp()] = info.GetAdrDetail().GetCity()
		}
	}
	userLocs := make(map[uint32]string)
	for uid, ip := range userIps {
		if loc, ok := ip2loc[ip]; ok {
			userLocs[uid] = loc
		}
	}
	for uid,loc:=range userLocs{
		fmt.Println("uid=",uid)
		fmt.Println("loc=",loc)
	}

}

//需要学一下kafka怎么写 //todo


//点赞评论动态的数据 kafka
//删除动态 kafka
//发布动态 kafka

//帖子内容接口 //todo


//好友数据 //todo
//拿用户的年龄、性别 //todo 需要学习一下怎么从hive拿数据


//拿粉丝数
func TestBatchGetUserCounts(t *testing.T){
	cli,err:=friendShipCli.NewClient(grpc.WithBlock())
	if err!=nil{
		panic(err)
	}
	count,err:=cli.BatchGetUserCounts(context.Background(),[]uint32{1,2,100,12,34,32,2})
	fmt.Println("count=",count)
	for k,v:=range count{
		fmt.Println("k=",k)
		fmt.Println("v=",v)
	}
	fmt.Println("err=",err)
}

//黑产用户
//批量确认是不是黑产
//BatchUserBehaviorCheckReq
func TestUserBehaviorCheck(t *testing.T){
	cli:=antCli.NewClient(grpc.WithBlock())
	uid,typ,err:=cli.UserBehaviorCheck(context.Background(),1,0,[]uint32{21}) //21表示黑产
	if err!=nil{
		fmt.Println("err=",err)
	}
	fmt.Println("uid=",uid)
	fmt.Println("typ=",typ)
}

