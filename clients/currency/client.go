package currency

import (
	"golang.52tt.com/pkg/client"
	grpcClient "golang.52tt.com/pkg/foundation/grpc/client"
	"golang.org/x/net/context"
	"google.golang.org/grpc"
	"google.golang.org/grpc/metadata"
	"strconv"

	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/tracing"
	tracingGrpc "golang.52tt.com/pkg/tracing/grpc" //
	currencyPB "golang.52tt.com/protocol/services/currencysvr"
)

const (
	serviceName = "currency"
)

const (
	// APPID
	AppId_TT = "TT_HZ"

	// ORDERID_USERTYPE

	CurrencyUser      = 0
	CurrencyHappycity = 11
	CurrencySdk       = 12

	// BIZ_MAX_TYPE
	BizMission      = 1
	BizFetchGiftPkg = 2

	BizOperation = 3

	BizMall                   = 4
	BizLottery                = 5
	BizRecruit                = 6 // 公会成员招募
	BizFirstVoucher           = 7
	BizGuildCheckInSupplement = 8  //补签
	BizGuildDonate            = 9  // 公会捐献
	BizSendPresent            = 10 // 赠送礼物
	BizSettlement             = 11 // 结算
	BizAddInviteCode          = 12 //新用户添加邀请码
	BizPresentActivity        = 13 //礼物活动
	BizDailyCheckIn           = 14 //每日签到
	Biz2018CeremonyIn         = 15 //2018 年度盛典
	Biz2019NewYearIn          = 19 //2019 新春活动
)

type Client struct {
	client.BaseClient
}

func NewClient(dopts ...grpc.DialOption) *Client {
	return newClient(dopts...)
}

func NewTracedClient(tracer tracing.Tracer) *Client {
	tracerInt := tracingGrpc.TracedUnaryClientInterceptor(
		tracing.LogPayloads(true),
		tracing.UsingTracer(tracer),
	)

	return newClient(grpc.WithUnaryInterceptor(tracerInt))
}

func newClient(dopts ...grpc.DialOption) *Client {

	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(serviceName, func(cc *grpc.ClientConn) interface{} {
			return currencyPB.NewCurrencyClient(cc)
		}, dopts...),
	}
}
func (c *Client) typedStub() currencyPB.CurrencyClient { return c.Stub().(currencyPB.CurrencyClient) }

func (c *Client) GetUserCurrency(ctx context.Context, uid uint32) (int32, protocol.ServerError) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uid))))
	r, err := c.typedStub().GetUserCurrency(ctx, &currencyPB.GetUserCurrencyReq{
		Uid: uid,
	})
	return r.GetCurrency(), protocol.ToServerError(err)
}

func (c *Client) AddUserCurrency(ctx context.Context, uid uint32, add int32, orderID, desc string, reason uint32) protocol.ServerError {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uid))))
	_, err := c.typedStub().AddUserCurrency(ctx, &currencyPB.AddUserCurrencyReq{
		Uid:         uid,
		Currency:    add,
		MissionKey:  orderID,
		MissionDesc: desc,
		OpUid:       0,
		WithStock:   false,
		Appid:       0,
		IsValuable:  false,
		OrderId:     orderID,
		Reason:      reason,
	})
	return protocol.ToServerError(err)
}

func (c *Client) GetUserCurrencyLog(ctx context.Context, uid uint32, in *currencyPB.GetUserCurrencyLogReq) (*currencyPB.GetUserCurrencyLogResp, protocol.ServerError) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uid))))
	r, err := c.typedStub().GetUserCurrencyLog(ctx, in)
	return r, protocol.ToServerError(err)
}
