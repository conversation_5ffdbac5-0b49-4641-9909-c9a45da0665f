package rcmd_channel_label

import (
	"context"
	"golang.52tt.com/pkg/client"
	"golang.52tt.com/pkg/tracing"
	pb "golang.52tt.com/protocol/services/topic_channel/rcmd_channel_label"
	"google.golang.org/grpc"
	"reflect"
	"testing"
)

func TestClient_GetGameLabels(t *testing.T) {
	type fields struct {
		BaseClient client.BaseClient
	}
	type args struct {
		ctx context.Context
		in  *pb.GetGameLabelsReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.GetGameLabelsResp
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &Client{
				BaseClient: tt.fields.BaseClient,
			}
			got, err := c.GetGameLabels(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetGameLabels() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.<PERSON><PERSON>("GetGameLabels() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestClient_LabelSearch(t *testing.T) {
	type fields struct {
		BaseClient client.BaseClient
	}
	type args struct {
		ctx context.Context
		in  *pb.LabelSearchReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.LabelSearchResp
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &Client{
				BaseClient: tt.fields.BaseClient,
			}
			got, err := c.LabelSearch(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("LabelSearch() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("LabelSearch() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestClient_typedStub(t *testing.T) {
	type fields struct {
		BaseClient client.BaseClient
	}
	tests := []struct {
		name   string
		fields fields
		want   pb.RCMDChannelLabelClient
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &Client{
				BaseClient: tt.fields.BaseClient,
			}
			if got := c.typedStub(); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("typedStub() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestNewClient(t *testing.T) {
	type args struct {
		dopts []grpc.DialOption
	}
	tests := []struct {
		name    string
		args    args
		want    *Client
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := NewClient(tt.args.dopts...)
			if (err != nil) != tt.wantErr {
				t.Errorf("NewClient() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("NewClient() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestNewTracedClient(t *testing.T) {
	type args struct {
		t     tracing.Tracer
		dopts []grpc.DialOption
	}
	tests := []struct {
		name    string
		args    args
		want    *Client
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := NewTracedClient(tt.args.t, tt.args.dopts...)
			if (err != nil) != tt.wantErr {
				t.Errorf("NewTracedClient() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("NewTracedClient() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_newClient(t *testing.T) {
	type args struct {
		dopts []grpc.DialOption
	}
	tests := []struct {
		name    string
		args    args
		want    *Client
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := newClient(tt.args.dopts...)
			if (err != nil) != tt.wantErr {
				t.Errorf("newClient() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("newClient() got = %v, want %v", got, tt.want)
			}
		})
	}
}
