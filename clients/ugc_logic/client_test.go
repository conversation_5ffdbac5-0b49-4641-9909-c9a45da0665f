package ugc_logic

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"golang.52tt.com/pkg/foundation/utils"
	"golang.52tt.com/services/ugc/common/contentfmt"
	"image"
	_ "image/gif"
	_ "image/jpeg"
	_ "image/png"
	"log"
	"math/rand"
	"os"
	"testing"
	"time"

	"google.golang.org/grpc"
	"google.golang.org/grpc/grpclog"
	"google.golang.org/grpc/metadata"

	"golang.52tt.com/clients/ugc/activity_stream"
	"golang.52tt.com/protocol/app"

	"github.com/qiniu/api.v7/storage"
	. "github.com/smartystreets/goconvey/convey"
	pb "golang.52tt.com/protocol/app/ugc"
)

var (
	testClient *UgcLogicContentClient
	userClient *UgcLogicUserClient
	err        error
)

func init() {
	grpclog.SetLoggerV2(grpclog.NewLoggerV2(os.<PERSON>, os.<PERSON>, os.Stdout))
	//testClient, err = NewClient(grpc.WithBlock())
	// os.Setenv("UGC_LOGIC_TARGET", "192.168.9.152:80")
	testClient = NewUgcLogicContentClient(grpc.WithBlock(), grpc.WithAuthority("ugc-logic.52tt.local"))
	if err != nil {
		log.Fatalln(err)
	}
	userClient = NewUgcLogicUserClient(grpc.WithBlock(), grpc.WithAuthority("ugc-logic.52tt.local"))
	if err != nil {
		log.Fatalln(err)
	}
}

func contextWithUid(uid uint32) context.Context {
	return withUid(context.Background(), uid)
}

func TestUgcLogicClient_GetTopicList(t *testing.T) {

	ctx := contextWithUid(1966030)
	ctx = metadata.AppendToOutgoingContext(ctx, "robot", "true")
	topicList, err := testClient.GetTopicList(ctx)
	if err != nil {
		t.Fatal(err)
	}

	t.Log(topicList)
}

func TestUgcLogicClient_GetSubscribeTopicList(t *testing.T) {

	resp, err := testClient.GetSubscriberTopicList(contextWithUid(2201771))
	if err != nil {
		t.Fatal(err)
	}

	t.Log(resp)
}

func TestUgcLogicClient_GetUnSubscribeTopicList(t *testing.T) {

	topicList, err := testClient.GetUnSubscribeTopicList(contextWithUid(1966030))
	if err != nil {
		t.Fatal(err)
	}

	t.Log(topicList)
}

func TestUgcLogicClient_GetPost(t *testing.T) {

	post, err := testClient.GetPost(contextWithUid(2216514), "5f2d19bffc86100001766df4")
	if err != nil {
		t.Fatal(err)
	}
	t.Log(post)
}

func TestUgcLogicClient_ExpressAttitude(t *testing.T) {

	err := testClient.ExpressAttitude(contextWithUid(1966030), "5bf28917ef57057cd20fac24")
	if err != nil {
		t.Fatal(err)
	}
}

func TestUgcLogicClient_ReportViewCount(t *testing.T) {

	err := testClient.ReportViewCount(contextWithUid(1966030), "5bf28917ef57057cd20fac24")
	if err != nil {
		t.Fatal(err)
	}
}

func TestUgcLogicClient_GetCommentList(t *testing.T) {

	resp, err := testClient.GetCommentList(contextWithUid(2191335), &pb.GetCommentListReq{
		PostId: "5c04f883ef57055f57551ef9",
		Count:  100,
	})
	if err != nil {
		t.Fatal(err)
	}
	t.Log(resp)
}

func TestUgcLogicClient_SubscribeTopic(t *testing.T) {
	Convey("TestUgcLogicClient_SubscribeTopic", t, func() {

		resp, err := testClient.SubscribeTopic(contextWithUid(2191335), "5c0e13c1ef5705579dfd5596")
		So(err, ShouldBeNil)
		t.Log(resp)
	})

}

func TestUgcLogicClient_GetInteractiveMsg(t *testing.T) {
	Convey("TestUgcLogicClient_GetInteractiveMsg", t, func() {

		resp, err := userClient.GetInteractiveMsg(contextWithUid(2191335), &pb.GetInteractiveMsgReq{Type: pb.InteractiveType_NEW_ATTITUDE | pb.InteractiveType_NEW_COMMENT | pb.InteractiveType_NEW_AT_MSG})
		So(err, ShouldBeNil)
		t.Log(resp)
	})
}

func TestUgcLogicClient_VisitFeeds(t *testing.T) {

	var ctx = contextWithUid(2191335)
	_, err := testClient.ReportVisitRecord(ctx, &pb.ReportVisitRecordReq{PostId: "5c05010def570504234ffb5a"})
	if err != nil {
		t.Error(err)
		return
	}

	resp, err := testClient.GetNewsFeeds(ctx, &pb.GetNewsFeedsReq{RequestType: &pb.GetNewsFeedsReq_UserVisitReq_{
		UserVisitReq: &pb.GetNewsFeedsReq_UserVisitReq{},
	}})
	if err != nil {
		t.Error(err)
		return
	}
	fmt.Println(resp)
	if resp.Group != activity_stream.UserVisitGroup {
		t.Error("group return err", resp.Group)
		return
	}
	var idlist []string
	for _, feed := range resp.Feeds {
		idlist = append(idlist, feed.FeedId)
	}
	if len(idlist) == 0 {
		t.Error("return feed list is null")
		return
	}

	_, err = testClient.RemoveFeeds(ctx, &pb.RemoveFeedsReq{Group: pb.RemoveFeedsReq_USER_VISIT, IdList: idlist})
	/*	_, err = testClient.RemoveFeeds(ctx, &pb.RemoveFeedsReq{Group: resp.Group, IdList: idlist})
		if err != nil {
			t.Error(err)
			return
		}*/

	resp, err = testClient.GetNewsFeeds(ctx, &pb.GetNewsFeedsReq{RequestType: &pb.GetNewsFeedsReq_UserVisitReq_{
		UserVisitReq: &pb.GetNewsFeedsReq_UserVisitReq{},
	}})
	if len(resp.Feeds) != 0 {
		t.Error("return feed list is not null")
		return
	}
}
/*
func TestUgcLogicClient_Follow(t *testing.T) {
	var user1, user2 uint32 = 2199998, 2216514
	var ctx = contextWithUid(user1)
	//var ctx2 = contextWithUid(user2)

	//1关注2
	//1取消关注2,2关注1
	//2取消关注1

	fresp, err := testClient.FollowUser(ctx, &pb.FriendshipOperationReq{
		UserIdentifier: &pb.UserIdentifier{KeyType: &pb.UserIdentifier_Uid{Uid: user2}},
		DatacenterContextInfo: map[string]string{
			"channelID": "1", "displayID": "2",
		},
	})
	if err != nil {
		t.Error(err)
		return
	}
	if fresp.BaseResp.Ret != 0 {
		t.Error(fresp)
		return
	}

	//resp, err := testClient.QuickCheckFriendship(ctx, &pb.QuickCheckFriendshipReq{UserIdentifier: &pb.UserIdentifier{KeyType: &pb.UserIdentifier_Uid{Uid: user2}}})
	//if err != nil {
	//	t.Error(err)
	//	return
	//}
	//if resp.BaseResp.Ret != 0 {
	//	t.Error(resp)
	//	return
	//}
	//if !resp.Following || resp.FollowMe {
	//	t.Error("friendship error", resp.Following, resp.FollowMe)
	//	return
	//}
	//
	//fresp, err = testClient.UnfollowUser(ctx, &pb.FriendshipOperationReq{UserIdentifier: &pb.UserIdentifier{KeyType: &pb.UserIdentifier_Uid{Uid: user2}}})
	//if err != nil {
	//	t.Error(err)
	//	return
	//}
	//if fresp.BaseResp.Ret != 0 {
	//	t.Error(fresp)
	//	return
	//}
	//
	//fresp, err = testClient.FollowUser(ctx2, &pb.FriendshipOperationReq{UserIdentifier: &pb.UserIdentifier{KeyType: &pb.UserIdentifier_Uid{Uid: user1}}})
	//if err != nil {
	//	t.Error(err)
	//	return
	//}
	//if fresp.BaseResp.Ret != 0 {
	//	t.Error(fresp)
	//	return
	//}
	//
	//resp, err = testClient.QuickCheckFriendship(ctx2, &pb.QuickCheckFriendshipReq{UserIdentifier: &pb.UserIdentifier{KeyType: &pb.UserIdentifier_Uid{Uid: user1}}})
	//if err != nil {
	//	t.Error(err)
	//	return
	//}
	//if resp.BaseResp.Ret != 0 {
	//	t.Error(resp)
	//	return
	//}
	//if !resp.Following || resp.FollowMe {
	//	t.Error("friendship error", resp.Following, resp.FollowMe)
	//	return
	//}
	//
	//fresp, err = testClient.UnfollowUser(ctx2, &pb.FriendshipOperationReq{UserIdentifier: &pb.UserIdentifier{KeyType: &pb.UserIdentifier_Uid{Uid: user1}}})
	//if err != nil {
	//	t.Error(err)
	//	return
	//}
	//if fresp.BaseResp.Ret != 0 {
	//	t.Error(fresp)
	//	return
	//}
	//
	//resp, err = testClient.QuickCheckFriendship(ctx2, &pb.QuickCheckFriendshipReq{UserIdentifier: &pb.UserIdentifier{KeyType: &pb.UserIdentifier_Uid{Uid: user1}}})
	//if err != nil {
	//	t.Error(err)
	//	return
	//}
	//if resp.Following || resp.FollowMe {
	//	t.Error("friendship error", resp.Following, resp.FollowMe)
	//	return
	//}

}

 */
/*
func TestUgcLogicClient_FollowBatchUser(t *testing.T) {
	var ctx = contextWithUid(2191335)
	var batchUser = []uint32{2204713, 2196237}

	bresp, err := testClient.FollowBatchUser(ctx, &pb.FollowBatchUserReq{Uid: batchUser})

	if err != nil {
		t.Error(err)
		return
	}
	log.Printf("输出的内容为%v", bresp)
}

 */

func TestUserAttitudeFeed(t *testing.T) {
	var ctx = contextWithUid(1967127)

	resp, err := testClient.GetNewsFeeds(ctx, &pb.GetNewsFeedsReq{RequestType: &pb.GetNewsFeedsReq_UserAttitudeReq_{
		UserAttitudeReq: &pb.GetNewsFeedsReq_UserAttitudeReq{},
	}})
	if err != nil {
		t.Error(err)
		return
	}
	fmt.Println(resp)
}

func TestUgcLogicClient_GetNewsFeeds(t *testing.T) {

	Convey("Test Get Topics ", t, func() {

		resp, err := testClient.GetNewsFeeds(context.Background(), &pb.GetNewsFeedsReq{
			RequestType: &pb.GetNewsFeedsReq_UserRecommendationReq_{
				UserRecommendationReq: &pb.GetNewsFeedsReq_UserRecommendationReq{},
			},
		})

		So(err, ShouldBeNil)

		t.Log("len", len(resp.Feeds))

		for _, feed := range resp.Feeds {

			if feed.GetRecommendTopics() == nil {
				continue
			}

			if reses := feed.GetRecommendTopics().Resources; reses != nil {

				for _, res := range reses {
					t.Logf("res %+v", res)
				}

			}

			if topics := feed.GetRecommendTopics().Topics; topics != nil {

				for _, topic := range topics {
					t.Logf("topic %+v", topic)
				}

			}

		}

	})

}

func TestUgcLogicClient_GetTopicInfo(t *testing.T) {
	Convey("Test  TestUgcLogicClient_GetTopicInfo ", t, func() {

		info, err := testClient.GetTopicInfo(context.Background(), "5c0e17edef5705579dfd5598")
		So(err, ShouldBeNil)

		t.Logf("%+v", info)
	})
}

func TestUgcRemoveFavouriteByPostId(t *testing.T) {
	var ctx = contextWithUid(1967127)
	removeResp, err := testClient.RemoveFeeds(ctx, &pb.RemoveFeedsReq{BaseReq: &app.BaseReq{}, Group: pb.RemoveFeedsReq_USER_FAVOURITE, ClearAll: true})
	if err != nil || removeResp.BaseResp.Ret != 0 {
		t.Error(removeResp.String())
		t.Error(err)
		return
	}

	postResp, err := testClient.PostPost(ctx, &pb.PostPostReq{BaseReq: &app.BaseReq{}, Content: "I am just a test favourite post it is.", Type: pb.PostType_POST_TYPE_TEXT})
	if err != nil || postResp.BaseResp.Ret != 0 {
		t.Error(postResp.String())
		t.Error(err)
		return
	}


	listResp, err := testClient.GetNewsFeeds(ctx, &pb.GetNewsFeedsReq{BaseReq: &app.BaseReq{}, Count: 100, RequestType: &pb.GetNewsFeedsReq_UserFavouriteReq_{
		UserFavouriteReq: &pb.GetNewsFeedsReq_UserFavouriteReq{},
	}})
	if err != nil || listResp.BaseResp.Ret != 0 {
		t.Error(listResp.String())
		t.Error(err)
		return
	}
	if len(listResp.Feeds) != 1 {
		t.Error(listResp.String())
		t.Error("feed list length error")
		return
	}

	_, err = testClient.RemoveFeeds(ctx, &pb.RemoveFeedsReq{BaseReq: &app.BaseReq{}, Group: pb.RemoveFeedsReq_USER_FAVOURITE, PostIdList: []string{postResp.PostId}})
	if err != nil {
		t.Error(err)
		return
	}

	listResp, err = testClient.GetNewsFeeds(ctx, &pb.GetNewsFeedsReq{BaseReq: &app.BaseReq{}, Count: 100, RequestType: &pb.GetNewsFeedsReq_UserFavouriteReq_{
		UserFavouriteReq: &pb.GetNewsFeedsReq_UserFavouriteReq{},
	}})
	if err != nil || listResp.BaseResp.Ret != 0 {
		t.Error(listResp.String())
		t.Error(err)
		return
	}
	if len(listResp.Feeds) != 0 {
		t.Error(listResp.String())
		t.Error("feed list is not null")
		return
	}

}

func TestUgcFavourite(t *testing.T) {
	var ctx = contextWithUid(2198917)

	//清空收藏feed
	//获取feed流，为空
	//发2帖，收藏2帖，获取feed流，显示2帖
	//删1帖，获取feed流，显示1帖，单独拉帖详情（已收藏属性为true）
	//取消收藏显示的1帖，再获取，为空，单独拉帖详情（已收藏属性为false）
	removeResp, err := testClient.RemoveFeeds(ctx, &pb.RemoveFeedsReq{BaseReq: &app.BaseReq{}, Group: pb.RemoveFeedsReq_USER_FAVOURITE, ClearAll: true})
	if err != nil || removeResp.BaseResp.Ret != 0 {
		t.Error(removeResp.String())
		t.Error(err)
		return
	}

	listResp, err := testClient.GetNewsFeeds(ctx, &pb.GetNewsFeedsReq{BaseReq: &app.BaseReq{}, Count: 100, RequestType: &pb.GetNewsFeedsReq_UserFavouriteReq_{
		UserFavouriteReq: &pb.GetNewsFeedsReq_UserFavouriteReq{},
	}})
	if err != nil || listResp.BaseResp.Ret != 0 {
		t.Error(listResp.String())
		t.Error(err)
		return
	}
	if len(listResp.Feeds) != 0 {
		t.Error("feed list is not null")
		return
	}

	postResp, err := testClient.PostPost(ctx, &pb.PostPostReq{BaseReq: &app.BaseReq{}, Content: "I am just a test favourite post it is.", Type: pb.PostType_POST_TYPE_TEXT})
	if err != nil || postResp.BaseResp.Ret != 0 {
		t.Error(postResp.String())
		t.Error(err)
		t.Error(postResp.BaseResp.ErrMsg)
		return
	}

	time.Sleep(time.Second * 80)

	postResp2, err := testClient.PostPost(ctx, &pb.PostPostReq{BaseReq: &app.BaseReq{}, Content: "I am just a test favourite post2 it is.", Type: pb.PostType_POST_TYPE_TEXT})
	if err != nil || postResp2.BaseResp.Ret != 0 {
		t.Error(postResp2.String())
		t.Error(err)
		return
	}
	fmt.Println("post id :", postResp.PostId, "----", postResp2.PostId)

	listResp, err = testClient.GetNewsFeeds(ctx, &pb.GetNewsFeedsReq{BaseReq: &app.BaseReq{}, Count: 100, RequestType: &pb.GetNewsFeedsReq_UserFavouriteReq_{
		UserFavouriteReq: &pb.GetNewsFeedsReq_UserFavouriteReq{},
	}})
	if err != nil || listResp.BaseResp.Ret != 0 {
		t.Error(listResp.String())
		t.Error(err)
		return
	}
	if len(listResp.Feeds) != 2 {
		t.Error(listResp.String())
		t.Error("feed list length error")
		return
	}

	delResp, err := testClient.DeletePost(ctx, &pb.DeletePostReq{BaseReq: &app.BaseReq{}, PostId: postResp2.PostId})
	if err != nil || delResp.BaseResp.Ret != 0 {
		t.Error(delResp.String())
		t.Error(err)
		return
	}

	listResp, err = testClient.GetNewsFeeds(ctx, &pb.GetNewsFeedsReq{BaseReq: &app.BaseReq{}, Count: 100, RequestType: &pb.GetNewsFeedsReq_UserFavouriteReq_{
		UserFavouriteReq: &pb.GetNewsFeedsReq_UserFavouriteReq{},
	}})
	if err != nil || listResp.BaseResp.Ret != 0 {
		t.Error(listResp.String())
		t.Error(err)
		return
	}
	if len(listResp.Feeds) != 1 {
		t.Error(listResp.String())
		t.Error("feed list length error")
		return
	}

	postInfo, err := testClient.GetPost(ctx, listResp.Feeds[0].GetPost().Post.PostId)
	if err != nil {
		t.Error(err)
		return
	}
	if !postInfo.HadFavoured {
		t.Error(postInfo.String())
		t.Error("user had favoured")
		return
	}

	_, err = testClient.RemoveFeeds(ctx, &pb.RemoveFeedsReq{BaseReq: &app.BaseReq{}, Group: pb.RemoveFeedsReq_USER_FAVOURITE, IdList: []string{listResp.Feeds[0].FeedId}})
	if err != nil {
		t.Error(err)
		return
	}

	listResp, err = testClient.GetNewsFeeds(ctx, &pb.GetNewsFeedsReq{BaseReq: &app.BaseReq{}, Count: 100, RequestType: &pb.GetNewsFeedsReq_UserFavouriteReq_{
		UserFavouriteReq: &pb.GetNewsFeedsReq_UserFavouriteReq{},
	}})
	if err != nil || listResp.BaseResp.Ret != 0 {
		t.Error(listResp.String())
		t.Error(err)
		return
	}
	if len(listResp.Feeds) != 0 {
		t.Error(listResp.String())
		t.Error("feed list is not null")
		return
	}

	postInfo, err = testClient.GetPost(ctx, postInfo.PostId)
	if err != nil {
		t.Error(err)
		return
	}
	if postInfo.HadFavoured {
		t.Error(postInfo.String())
		t.Error("user had not favoured")
		return
	}

}

func TestUgc_Comment(t *testing.T) {
	ctx, cancel := context.WithCancel(contextWithUid(500001))
	defer cancel()

	rnd := rand.New(rand.NewSource(time.Now().UnixNano()))

	commentsOrigin := []string{
		"么么哒[t041/鼓掌][t041/鼓掌]", "损塞", "乖乖", "666", "不回家", "被爱的都是祖宗不假 可惜，我不供祖宗           我自然明白 谁让我难过，我就离开谁", "16", "妈卖批", "第二个那么帅，我肯定选第一个啊", "技术监督局", "很高兴认识你。小哥哥", "流弊了，宾利开上了。阔以啊[t040/抠鼻]", "去你大爷的[t003/发呆]", "这种", "有啊95年老大叔，坐标贵州凯里", "致敬所有为我们服务的叔叔阿姨", "这不是刚找到嘛。。。这俩天你心情不好，也没敢找你说话[t024/饥饿]", "快乐，快乐", "优秀", "他妈逼", "啥时候变女的了[t020/偷笑]", "哈哈哈哈哈", "nxjxjd", "谢谢", "优秀", "该回来的他自然会回来，回不来的终究回不来", "我不想", "优秀[t016/酷]", "把我的小白鹿还给我😭", "。", "别人转", "咦~", "略略略略略", "[t001/撇嘴]", "哦酷我", "我希望谁给我转71我买个蔡文姬新皮肤奶她  奶她一辈子！笔芯", "哈哈哈", "我处", "贼优秀", "哈哈", "是老婆没错了", "好的", "[t001/撇嘴][t010/尴尬]", "漂亮的小姐姐", "我写了评测报告", "T裤自己", "那你", "这么关键的吃鸡课程，你怎么就只记得身份证[t000/微笑]", "你岁", "悬崖打的贼溜哦", "破一千万", "好美", "一张大饼脸", "你手可真快", "mua，mua[t013/呲牙][t013/呲牙][t013/呲牙]", "呀", "这就是狗狗么😂", "俄军在了[t011/发怒][t011/发怒][t012/调皮][t011/发怒]", "知道她为什么做模嘛？", "[t006/害羞]我是你爸爸不", "额咯", "Hd", "夕阳红", "厉害了👍", "互关", "啧啧啧", "踩", "Hyijk1", "迷人的背影", "我这脑子不行 也没看完这么老长 反正你能写这么多 那你nb", "叫什么", "那你还问？", "总给我织围巾，", "猜起来多累", "还不是因为你长得不好看", "……", "11", "记得记得叫", "我也是慢慢瞅了好久", "优秀的人呐", "对啊对啊", "苍天，绕过谁", "嘿嘿", "啧啧啧", "很开心", "都要可以吗。我请客", "我要做丰胸", "急急急", "小姐姐一起上分呀", "嗯嗯", "我完事了，你们呢?", "加个雪碧晃一晃", "1", "只能是我家的小仙女[t013/呲牙]", "😂", "每次忍不住偷偷看你的空间，都很难过，即使这样还是想知道看看你，只因为没有放下，我想这不怪你，因为没有理由给不了你幸福，还要你因为我不幸福。 希望你的身边能有个他  好好对你  wx❤", "看我行不行  小姐姐", "有时间带我玩玩   我要看你飘成什么样", "期待你的加入", "啦啦啦", "快乐", "[t013/呲牙]", "对啊", "韩雪", "为啥", "约", "漂亮女人", "[t023/傲慢][t023/傲慢][t023/傲慢]", "不能那么", "后搜寻", "啊偶", "。", "笑死", "问问你自己为什么你知道她是模你也喜欢她", "有", "等会昂", "滴滴，你手可真黑", "啦啦", "让他太尴尬", "那祝你新年新年你新年新年那祝你新年那祝你在那祝你新年祝你在哪在哪你在不行不行吧吧吧上班下班打你的呢难道不行不行吧不断不断吧别的吧上班不断不断吧吧祝你新年那对男女少男少女那些不是你不行别的吧就想继续进行家就死就是你南山南少男少女南山南你在哪你死就是你南山呢", "啥都好，就是容易动心", "啊哈哈哈哈", "哈哈哈", "求评论[t009/大哭]", "滴滴", "我把我的袜子都拿出来l", "叔叔给我吹个泡泡", "5", "97年可以了解一下你吗，想和你谈一下", "😂😂😂", "欢迎[t006/害羞]", "检测报告", "+1", "醒醒", "护士小姐姐", "提莫大爷，快来阿，这小姑娘想你想疯了🙋🙋🙋", "dd", "要知道我胸大", "哇哇哇   谁送的🙃", "我不是在呢吗", "Rom 人", "[t009/大哭][t009/大哭][t009/大哭]", "地", "😂", "你丑你丑", "哈哈哈哈哈哈哈", "B型h系基督教", "抱抱泥，永远不要不开心呐", "💔💔😭😭😭", "所迫", "你们干啥啊，互相黑啊", "厉害了", "我可以吗", "[t057/顶][t057/顶][t057/顶]", "共勉", "谢谢", "iiii", "过分。呵呵呵", "嘿嘿[t006/害羞]", "[t006/害羞][t006/害羞]", "而不是", "噗哈哈嘿嘿", "[t001/撇嘴][t002/色]", "啦啦", "……我还是个宝宝", "退伍兵也是兵", "好看", "2楼[t040/抠鼻]", "哥哥，酱酱想家了", "回家过圣诞节么", "快乐快乐[t006/害羞]", "还", "哈哈哈", "第二张不是鸟巢那边吗！！！", "3", "宝贝吃饭", "真好", "幼稚聊几句", "嘿嘿", "哈哈，走位走位，看不见", "Did", "需要来我房间:444995   欢迎大家来踩！", "额。屌丝挺好", "好的呐", "咱俩用，好不好", "低调", "摸摸", "S", "叽里咕噜", "人心对人心", "哪里败家了", "姐姐", "同年", "能把你们两个字去掉么，真难听", "老子想让人追咋的。不让么。", "太久", "可以 可以", "爱过像狗的人", "宝~[t006/害羞]", "没咯", "优秀[t050/阴险]", "啥", "v今年", "人身", "[t024/饥饿][t024/饥饿]", "这么晚了还没下班呢😂😂😂😂", "阔以深入了解一哈", "又凶我[t009/大哭][t009/大哭]", "有毛病", "美妞", "。在不在不", "[t065/腹黑][t065/腹黑][t065/腹黑]膈应你", "[t006/害羞]我也想做一个", "啦啦", "嗯呢，是的", "好嗨哟", "Hetui", "还可以评论😂", "真好", "诚信互关", "男女姐姐", "666了  石榴姐", "身体最重要，注意休息啊，大腿姐。", "111", "小莫莫，温油点", "你看你那大肥脸", "[t001/撇嘴]", "9", "媳妇生日快乐", "来咯", "这么多年", "你爸爸给你了 哈哈哈嗝", "真好", "我会一直陪你", "玩着玩着就不想脱手了", "姑娘，酒不能乱喝话不能乱讲", "日常一丧", "99", "老女人好", "把是不是就是", "无聊啊", "喜欢玩空军", "说一万遍我爱你，不如好好在一起💕", "和你就算了吧[t043/坏笑]", "不阔能", "哈哈哈", "第一", "抱抱泥，眼泪留给值得的人", "超美", "想要嘛，帮你弄", "bbb", "求翻牌", "秉笔直书计算机", "喜欢", "咱妈会打我，我妈不打我", "……", "这就是个骗号的[t000/微笑]", "生日快乐", "是啊真", "谢谢", "hh", "人妖吧", "1", "快点滴先来个400万给厅过点流水", "[t040/抠鼻][t040/抠鼻][t040/抠鼻]", "破壳纪念日", "呵呵呵", "[t013/呲牙]", "骚的鸭皮", "嗯，在你婆婆家里", "结局", "记得吧直播直播呢", "丑拒", "帅", "repo", "是的", "可以的", "[t000/微笑][t000/微笑]", "吼吼吼吼", "没然后", "[t040/抠鼻][t040/抠鼻][t040/抠鼻]", "再见，老女人", "嗝  肉麻", "一", "♡", "一给我离giao", "这手好看", "[t000/微笑][t001/撇嘴]昌忆发发发kll哟nnnnnn", "[t006/害羞][t006/害羞][t006/害羞]", "来哇", "男女个很好", "我羡慕你们没肉的[t001/撇嘴]", "希望你能善良", "加油", "借楼蹭粉", "[t024/饥饿][t025/困][t025/困]", "强行推到  上去就是一个mua", "好帅的小哥哥", "看得我恶心", "平安夜快乐", "拿图", "生日快乐呀~", "哦咯", "哦哦哦啦啦啦啦啦啦", "何必搭理你", "好在赢了[t020/偷笑]", "啊哈哈", "哈哈哈", "[t006/害羞][t006/害羞][t006/害羞]", "还回家", "订", "加我", "不想猜  想要你 。。。", "好像傻老娘们", "咱俩为啥要这样聊天。", "借楼蹭粉", "我在", "这是事实，不能出去水印，我有什么办法😂", "在线", "Is", "萝卜丁", "？？？？", "🌚", "99", "到处蹭", "想摸", "[t009/大哭]送出去一堆，没收到一个", "便宜", "在你心里", "额额额  好的呢", "就是的", "真好", "感情不都是小细节决定的么", "厉害", "666", "得劲不", "笔芯", "不知道啊", "沙发非你莫属[t064/怪蜀黍]", "你跟BB……有猫腻啊[t014/惊讶]", "这个叫什么电影", "nn", "哎哟喂", "[t000/微笑][t000/微笑][t000/微笑]", "358", "哦噢喔偶", "记得记得记得哈", "那是你段位低。你去试试王牌的人机？", "我就睡觉了", "666", "后来呀，什么都没有了......", "唔扩圈++", "生活来点糖[t012/调皮]美滋滋", "啦啦啦", "[t001/撇嘴][t001/撇嘴][t001/撇嘴]", "ysysyshsjsjnx", "[t012/调皮]", "对象是啥⊙∀⊙？", "就是喝多了", "噗哈哈哈[t020/偷笑][t020/偷笑]", "没事  凯纱身边还有小雅", "优秀", "百战百胜", "跟谁？", "哈哈哈笑死我了", "笑出猪声哈哈哈哈", "见证你百万粉丝的第一步[t000/微笑]", "就是分享给你们的", "[t006/害羞]呵呵哈哈哈", "优秀", "我爱交友[t000/微笑]", "北愣子[t006/害羞]", "好", "测试方法有", "18，要一直在一起", "实话", "你咋了", "没有小姐姐要我了", "😂", "海口柏菲？", "哈哈哈", "反正我很怕", "打一顿就好了", "啦啦", "妈耶，狗粮", "+1", "正面呢", "死开", "呃呃呃", "竞技等级", "我不想有，因为我怕了[t009/大哭][t009/大哭][t009/大哭]", "[f020]", "想我想我", "We", "hhhjjjhj", "我的", "还回家脚后跟", "[t001/撇嘴]", "噢裙子", "感谢", "我也出门了", "嗯呢谢谢", "你也快乐", "可能我心比较大，窝什么都想拥有", "想要这个头像", "百战百胜", "谷歌", "罢了", "9", "君主我希望你别爆发[t022/白眼][t017/冷汗]", "还来么", "嗯嗯[t002/色]看你发的图片配的文字", "爱你  想你操哭你", "哈哈", "要怎么哦哦", "今天很多人要扶我[t000/微笑]", "楼", "若没有人做你的英雄那就自己做自己的英雄", "[f021]", "啦啦", "gg", "嘘，天机", "[t006/害羞][t006/害羞]", "+1", "别用了", "加油", "猪精", "在tt宣传抖音干", "睡觉吧", "好可爱啊", "2", "哼(ノ=Д=)ノ┻━┻", "网图吧", "闪", "什么在我婆婆家里？", "真好", "。", "确实是，但是我跟她关系挺好的", "可以", "好的吧", "😂😂😂", "见过这么可爱的吗", "嗝", "咦这是我的小祖宗[t002/色]", "咋了", "退游已经很长时间了", "在下瞄人缝", "口红(别买芭比粉) 零食 纪念意义的项链手镯 娃娃 惊喜", "活抓一只小仙女", "一切会好的乖柠萌活宝", "师傅有点皮", "还能回复的吗", "圣诞节我生日", "Dior", "[t000/微笑][t000/微笑][t000/微笑]", "嘘。", "你是大可爱", "谢谢", "嘿嘿，我也没有", "什么游戏", "20号？", "打了北鼻扒了舅", "哈哈哈哈哈哈哈哈", "和反腐败", "活着不好吗", "拿图", "公公明敏", "咦～", "he...pu～", "略略略", "是的……", "前排", "[t038/再见][t038/再见][t038/再见]啥？我去，二十几天评论，一大半都是我", "哈哈哈", "蹭楼", "图3我拿了[t006/害羞]", "气不气啦", "又是你[t028/憨笑]", "HK咯", "哈哈哈", "么事么事，小姑娘挺漂亮的[t013/呲牙]", "迪不好蹦嘛？处啥对象", "哈哈哈", "星期天回来", "二哦婆婆哦www", "哈哈哈哈都活着", "新版本才多少人用", "啊哈", "嗯呢", "吓我一跳", "1", "3", "是呀", "6", "无关痛痒的是非", "这也叫没钱吃饭了啊", "太贵的她不会要", "灵敏", "宁总威武", "99啊",
	}
	p := rnd.Perm(len(commentsOrigin))
	comments := make([]string, len(commentsOrigin))
	for i, pp := range p {
		comments[i] = commentsOrigin[pp]
	}

	posts := []string{
		"你觉得我说的对吗？", "楚留香手游格挡熟练特技怎么获取 格挡熟练特技介绍，打造属性：对怪物的格挡率提升4%，获取途径：加入天工奇石有一定几率获取1-3星特技，打造位置：发冠、上衣、下装和腰带，适合门派：门派通用", "决赛圈不要紧张，出来一起玩啊", "约会大作战人物：鸢一折纸，你可能不知道的十件事情", "第三季背景推演", "毒圈“蛮横无理”？31名玩家心怀鬼胎，为吃鸡表里不一", "“闪电激斗”玩法的战斗节奏和激烈程度在休闲模式里那都是首屈一指的存在，目前还缩小了初始安全区了，使得游戏激烈程度进一步提升。\n \n而在这样的模式下，玩家的各方面能力都能得到快速的增长。今天也给大家介绍一下加强版“闪电激斗”的玩法。\n \n进入加强版“闪电激斗”玩法的技巧很简单，只需要在进入游戏前取消“匹配时补充队友”的功能。在这样的特殊玩法下，荒友需要考虑的事情也多了很多。由于“闪电激斗”玩法的信号区缩小太快，大多数小队都会直接选择信号区内的房区降落，但是独狼的话，显然不能直接进入房区和其他队伍开战，毕竟你只有自己一个人。", "如果大冬天你在学校操场看到这一幕，你的第一反应是什么？", "民工漫", "等多久大家", "刚想秀一波操作的时候发现对面神装我才一件", "啦啦哈哈我这", "星期天回来\n在下自己", "有小丑真的可以为所欲为~", "【日式冷吐槽】之互动冷吐槽：请配吐槽语~", "还有比这个更极限的1打4么？", "当代父母最关心的问题——我聊天头像[拜拜]", "阴阳师里每个角色都有一段自己的故事，你还知道哪些关于他们的故事吗？评论下方分享给大家", "Tgccc", "第一次拍连续剧，好激动啊！结局想不到",
	}

	cfg := storage.Config{}
	formUploader := storage.NewFormUploader(&cfg)

	Convey("comment", t, func() {
		postResp, err := testClient.PostPost(ctx, &pb.PostPostReq{
			Content: posts[rnd.Int()%len(posts)],
			Type:    pb.PostType_POST_TYPE_TEXT},
		)

		So(err, ShouldBeNil)
		So(postResp.BaseResp.Ret, ShouldBeZeroValue)
		So(postResp.PostId, ShouldNotBeEmpty)

		// 发1级评论列表
		n := rnd.Int()%50 + 50

		topLevelComments := make([]string, 0, n)

		uploadTasks := make([]*pb.PostCommentResp, 0)
		for i := 0; i < n; i++ {
			imageCount := 0
			if rnd.Int()%3 == 0 {
				imageCount = 1
			}

			commentResp, err := testClient.PostComment(ctx, &pb.PostCommentReq{
				PostId:               postResp.PostId,
				Content:              comments[rnd.Int()%len(comments)],
				AttachmentImageCount: uint32(imageCount),
			})

			So(err, ShouldBeNil)
			So(commentResp.BaseResp.Ret, ShouldBeZeroValue)
			topLevelComments = append(topLevelComments, commentResp.CommentId)

			if imageCount == 1 {
				uploadTasks = append(uploadTasks, commentResp)
			}

			if imageCount == 0 {
				// 发2级评论列表
				m := rnd.Int()%50 + 50
				lastComment := commentResp.CommentId
				for j := 0; j < m; j++ {
					imageCount := 0
					if rnd.Int()%7 == 1 {
						imageCount = 1
					}

					commentResp2, err := testClient.PostComment(ctx, &pb.PostCommentReq{
						PostId:               postResp.PostId,
						ConversationId:       commentResp.CommentId,
						CommentId:            lastComment,
						Content:              comments[rnd.Int()%len(comments)],
						AttachmentImageCount: uint32(imageCount),
					})

					So(err, ShouldBeNil)
					So(commentResp2.BaseResp.Ret, ShouldBeZeroValue)

					if imageCount == 1 {
						uploadTasks = append(uploadTasks, commentResp2)
					}

					if rnd.Int()%3 == 1 {
						lastComment = commentResp2.CommentId
					} else {
						lastComment = commentResp.CommentId
					}
				}

			}
		}

		So(len(topLevelComments), ShouldEqual, n)

		// 获取评论列表
		commentListResp, err := testClient.GetCommentList(ctx, &pb.GetCommentListReq{
			PostId:  postResp.PostId,
			Count:   uint32(n),
			OrderBy: pb.GetCommentListReq_AUTOMATIC,
		})
		So(err, ShouldBeNil)
		So(len(commentListResp.Comments), ShouldEqual, n)

		for i := 0; i < n; i++ {
			So(commentListResp.Comments[i].CommentId, ShouldEqual, topLevelComments[n-1-i])
		}

		localFiles := []string{
			"test-images/taki.jpg",
			"test-images/mitsuha.jpg",
		}

		for i, commentResp := range uploadTasks {
			So(len(commentResp.ImageKeys), ShouldEqual, 1)
			So(commentResp.ImageToken, ShouldNotBeBlank)

			func(id, local, token, key string) {
				// upload image
				f, err := os.Open(local)
				So(err, ShouldBeNil)
				defer f.Close()

				c, _, err := image.DecodeConfig(f)
				So(err, ShouldBeNil)

				ret := storage.PutRet{}
				err = formUploader.PutFile(ctx, &ret, token, key, local, nil)

				cj, _ := json.Marshal(&struct {
					Width  int `json:"width"`
					Height int `json:"height"`
				}{
					Width:  c.Width,
					Height: c.Height,
				})
				t.Logf("upload %s -> %s extra=%s: %v %v", local, commentResp.ImageKeys[0], cj, err, ret)
				So(err, ShouldBeNil)

				_, err = testClient.MarkPostAttachmentUploaded(ctx, &pb.MarkPostAttachmentUploadedReq{
					PostId:    postResp.PostId,
					CommentId: id,
					AttachmentList: []*pb.UploadAttachmentInfo{
						&pb.UploadAttachmentInfo{
							AttachmentType: pb.AttachmentType_IMAGE,
							AttachmentKey:  key,
							ExtraInfo:      string(cj),
						},
					},
				})
				So(err, ShouldBeNil)

			}(commentResp.CommentId, localFiles[i%len(localFiles)], commentResp.ImageToken, commentResp.ImageKeys[0])
		}
	})

}

//func TestUgcBatchExpressAttitude(t *testing.T) {
//
//	rmClient, err := recommendation.NewClient(grpc.WithBlock())
//	if err != nil {
//		t.Fatal(err)
//	}
//
//	rr, err := rmClient.GetPostsWithFilter(context.Background(), &recommendation2.GetPostsWithFilterReq{
//		UserId: 2214120,
//		Filter: &recommendation2.Filter{Method: recommendation2.Filter_RANDOM},
//		Count:  1500,
//	})
//	if err != nil {
//		t.Fatal(err)
//	}
//
//	fmt.Println(len(rr.PostList))
//
//	s := []string{
//		"Post:5c1b0939ef57051531e09370",
//		"Post:5c1b093bef57051531e0937f",
//		"Post:5c1b0736ef57051531e09362",
//		"Post:5c1b0939ef57051531e09365",
//		"Post:5c1b0939ef57051531e09369",
//		"Post:5c1b0939ef57051531e0936c",
//		"Post:5c1b093aef57051531e0937a",
//		"Post:5c1b093aef57051531e0937b",
//		"Post:5c1b093bef57051531e09383",
//		"Post:5c1b093bef57051531e09384",
//		"Post:5c1b093bef57051531e09388",
//		"Post:5c1b093aef57051531e09375",
//		"Post:5c1b093aef57051531e09372",
//		"Post:5c1b093aef57051531e09373",
//		"Post:5c1b0939ef57051531e0936b",
//		"Post:5c1b093aef57051531e09378",
//		"Post:5c1b0939ef57051531e09366",
//		"Post:5c2eca1fef570533c373c421",
//		"Post:5c2dc90cef57052c35da6c6d",
//		"Post:5c2dc4fcf7c24128c10d62ce",
//		"Post:5c2dc213ef570567b21b9573",
//		"Post:5c2d857af7c241538e7e8dd1",
//		"Post:5c1b093aef57051531e09379",
//		"Post:5c1b093bef57051531e0937e",
//		"Post:5c1b093bef57051531e09386",
//		"Post:5c1b093bef57051531e09380",
//		"Post:5c1b0939ef57051531e0936f",
//		"Post:5c1b0939ef57051531e09368",
//		"Post:5c1b0939ef57051531e0936e",
//		"Post:5c1b0939ef57051531e0936a",
//		"Post:5c1b093aef57051531e09374",
//		"Post:5c244e49ef570548793442c4",
//		"Post:5c234dd0ef5705487934427e",
//		"Post:5c234c4bef5705487934425a",
//		"Post:5c23319cef570528eac6fb97",
//		"Post:5c1b0939ef57051531e09367",
//		"Post:5c1b0829ef57051531e09364",
//		"Post:5c1b093aef57051531e09377",
//		"Post:5c1b093bef57051531e09382",
//		"Post:5c1cc7cbef57056a41e1fffe",
//		"Post:5c1cc770ef57056a41e1fffa",
//		"Post:5c1c9d28ef57057cb1926d9e",
//		"Post:5c1c9ba3ef57057cb1926d98",
//		"Post:5c1c99bcf7c24145f3d2f8eb",
//		"Post:5c1c9416ef57054dc076262a",
//		"Post:5c1c9395ef57054dc0762629",
//		"Post:5c13a18cef57050d7448a3aa",
//		"Post:5c13a18eef57050d7448a3c8",
//		"Post:5c13a18def57050d7448a3b0",
//		"Post:5c13a191ef57050d7448a3e6",
//		"Post:5c13a192ef57050d7448a3f4",
//		"Post:5c13a190ef57050d7448a3e0",
//		"Post:5c13a191ef57050d7448a3eb",
//		"Post:5c13a18bef57050d7448a396",
//		"Post:5c13a190ef57050d7448a3df",
//		"Post:5c13a18bef57050d7448a398",
//		"Post:5c13a194ef57050d7448a414",
//		"Post:5c13a192ef57050d7448a3f5",
//		"Post:5c13a18eef57050d7448a3bc",
//		"Post:5c13a18def57050d7448a3b8",
//		"Post:5c13a18bef57050d7448a39b",
//		"Post:5c13a18def57050d7448a3af",
//		"Post:5c13a194ef57050d7448a415",
//		"Post:5c13a18eef57050d7448a3c4",
//		"Post:5c19f43aef570563b4a0b80c",
//		"Post:5c19ecd1ef570563b4a0b7eb",
//		"Post:5c13a193ef57050d7448a404",
//		"Post:5c13a18cef57050d7448a3a3",
//		"Post:5c13a18def57050d7448a3ae",
//		"Post:5c13a192ef57050d7448a3f7",
//		"Post:5c13a18def57050d7448a3b7",
//		"Post:5c19c29cef570502d446c06f",
//		"Post:5c13a18fef57050d7448a3d5",
//		"Post:5c19c04def570502d446c06d",
//		"Post:5c13a190ef57050d7448a3dc",
//		"Post:5c13a190ef57050d7448a3e3",
//		"Post:5c13a193ef57050d7448a40b",
//		"Post:5c13a18bef57050d7448a394",
//		"Post:5c13a18def57050d7448a3b9",
//		"Post:5c13a18fef57050d7448a3d0",
//		"Post:5c13a18fef57050d7448a3d3",
//		"Post:5c13a190ef57050d7448a3de",
//		"Post:5c18eb11ef570542635b7376",
//		"Post:5c18e246ef57055ebf72bce2",
//		"Post:5c18d31def57055ebf72bcd2",
//		"Post:5c18c329ef57055ebf72bcd0",
//		"Post:5c18ba7eef57055ebf72bccc",
//		"Post:5c18b3d2ef57055ebf72bcc7",
//		"Post:5c18adabef57055ebf72bcc5",
//		"Post:5c18ad8bef57055ebf72bcc4",
//		"Post:5c13a193ef57050d7448a403",
//		"Post:5c13a18cef57050d7448a3ab",
//		"Post:5c13a18cef57050d7448a3ac",
//		"Post:5c13a193ef57050d7448a40c",
//		"Post:5c13a18bef57050d7448a397",
//		"Post:5c13a18def57050d7448a3b1",
//		"Post:5c13a18eef57050d7448a3bf",
//		"Post:5c13a18fef57050d7448a3d4",
//		"Post:5c13a18fef57050d7448a3cd",
//		"Post:5c13a18bef57050d7448a39a",
//		"Post:5c13a193ef57050d7448a408",
//		"Post:5c13a190ef57050d7448a3da",
//		"Post:5c13a18def57050d7448a3b3",
//		"Post:5c13a18fef57050d7448a3d6",
//		"Post:5c13a18cef57050d7448a3a1",
//		"Post:5c13a18eef57050d7448a3c5",
//		"Post:5c13a18fef57050d7448a3d1",
//		"Post:5c13a18aef57050d7448a391",
//		"Post:5c13a18cef57050d7448a3a2",
//		"Post:5c13a192ef57050d7448a3f9",
//		"Post:5c13a18cef57050d7448a3a0",
//		"Post:5c17a4aaef570533ab663946",
//		"Post:5c174bcfef5705176cb17ec2",
//		"Post:5c174510ef5705176cb157a2",
//		"Post:5c13a190ef57050d7448a3dd",
//		"Post:5c173346ef5705176cb15776",
//		"Post:5c1732a5ef5705176cb15774",
//		"Post:5c173285ef5705176cb15773",
//		"Post:5c13a18bef57050d7448a392",
//		"Post:5c13a18eef57050d7448a3c3",
//		"Post:5c13a18bef57050d7448a39e",
//		"Post:5c170fdbef5705176cb15739",
//		"Post:5c170fc2ef5705176cb15737",
//		"Post:5c170f9bef5705176cb15736",
//		"Post:5c170f7fef5705176cb15734",
//		"Post:5c13a193ef57050d7448a401",
//		"Post:5c13a191ef57050d7448a3e9",
//		"Post:5c13a18eef57050d7448a3ca",
//		"Post:5c13a18cef57050d7448a3a8",
//		"Post:5c13a190ef57050d7448a3e5",
//		"Post:5c13a190ef57050d7448a3e4",
//		"Post:5c13a18aef57050d7448a390",
//		"Post:5c13a191ef57050d7448a3ef",
//		"Post:5c13a18cef57050d7448a3a6",
//		"Post:5c13a18eef57050d7448a3c0",
//		"Post:5c13a195ef57050d7448a41b",
//		"Post:5c13a18fef57050d7448a3cc",
//		"Post:5c13a190ef57050d7448a3d9",
//		"Post:5c13a192ef57050d7448a3f6",
//		"Post:5c15ebe0ef5705176cb10884",
//		"Post:5c13a18cef57050d7448a3a4",
//		"Post:5c13a18cef57050d7448a3a9",
//		"Post:5c13a18bef57050d7448a393",
//		"Post:5c13a18def57050d7448a3b4",
//		"Post:5c13a194ef57050d7448a40f",
//		"Post:5c13a191ef57050d7448a3ea",
//		"Post:5c13a18fef57050d7448a3ce",
//		"Post:5c13a18eef57050d7448a3c9",
//		"Post:5c13a193ef57050d7448a402",
//		"Post:5c13a190ef57050d7448a3e1",
//		"Post:5c13a192ef57050d7448a3f3",
//		"Post:5c13a18fef57050d7448a3cb",
//		"Post:5c13a18cef57050d7448a3a7",
//		"Post:5c13a18bef57050d7448a39c",
//		"Post:5c14a184ef57050d7448a4d9",
//		"Post:5c13a18def57050d7448a3b6",
//		"Post:5c13a18eef57050d7448a3c2",
//		"Post:5c13a192ef57050d7448a3fe",
//		"Post:5c1479b4ef57050d7448a498",
//		"Post:5c13a18fef57050d7448a3d2",
//		"Post:5c13a18bef57050d7448a39d",
//		"Post:5c13a194ef57050d7448a412",
//		"Post:5c13a192ef57050d7448a3fc",
//		"Post:5c13a18eef57050d7448a3c6",
//		"Post:5c13a18aef57050d7448a38e",
//		"Post:5c13a18eef57050d7448a3be",
//		"Post:5c13a194ef57050d7448a40d",
//		"Post:5c13a192ef57050d7448a3fb",
//		"Post:5c13a191ef57050d7448a3e7",
//		"Post:5c13a18eef57050d7448a3c7",
//		"Post:5c13a18def57050d7448a3ba",
//		"Post:5c13a190ef57050d7448a3db",
//		"Post:5c13a18cef57050d7448a3a5",
//		"Post:5c13b88eef57050d7448a44c",
//		"Post:5c13b6bfef57050d7448a44b",
//		"Post:5c13af7cef57050d7448a42f",
//		"Post:5c13af73ef57050d7448a42e",
//		"Post:5c13a99fef57050d7448a424",
//		"Post:5c13a56cef57050d7448a420",
//		"Post:5c13a52fef57050d7448a41e",
//		"Post:5c13a51def57050d7448a41d",
//		"Post:5c13a2dfef57050d7448a41c",
//		"Post:5c139b8eef57050d7448a385",
//		"Post:5c13993bef57050d7448a381",
//		"Post:5c136f5eef5705594fd893f9",
//		"Post:5c136e0def5705594fd893e6",
//		"Post:5c136e0def5705594fd893e5",
//		"Post:5c136e0def5705594fd893e4",
//		"Post:5c136e0def5705594fd893e3",
//		"Post:5c136e0def5705594fd893e2",
//		"Post:5c136e0def5705594fd893e1",
//		"Post:5c136e0def5705594fd893e0",
//		"Post:5c136e0def5705594fd893df",
//		"Post:5c136e0cef5705594fd893de",
//		"Post:5c136e0cef5705594fd893dd",
//		"Post:5c136e0cef5705594fd893dc",
//		"Post:5c136e0cef5705594fd893db",
//		"Post:5c136e0cef5705594fd893da",
//		"Post:5c136e0cef5705594fd893d8",
//		"Post:5c136e0cef5705594fd893d7",
//		"Post:5c136e0cef5705594fd893d6",
//		"Post:5c136e0cef5705594fd893d5",
//		"Post:5c136e0bef5705594fd893d3",
//		"Post:5c136e0bef5705594fd893d2",
//		"Post:5c136e0bef5705594fd893d1",
//		"Post:5c136e0bef5705594fd893d0",
//		"Post:5c136e0bef5705594fd893cf",
//		"Post:5c136e0bef5705594fd893ce",
//		"Post:5c136e0bef5705594fd893cd",
//		"Post:5c136e0bef5705594fd893cc",
//		"Post:5c136e0bef5705594fd893cb",
//		"Post:5c136e0aef5705594fd893c9",
//		"Post:5c136e0aef5705594fd893c8",
//		"Post:5c136e0aef5705594fd893c7",
//		"Post:5c136e0aef5705594fd893c6",
//		"Post:5c136e0aef5705594fd893c5",
//		"Post:5c136e0aef5705594fd893c4",
//		"Post:5c136e0aef5705594fd893c3",
//		"Post:5c136e0aef5705594fd893c2",
//		"Post:5c136e0aef5705594fd893c1",
//		"Post:5c136e0aef5705594fd893c0",
//		"Post:5c136e0aef5705594fd893bf",
//		"Post:5c136e0aef5705594fd893be",
//		"Post:5c136e0aef5705594fd893bd",
//		"Post:5c136e09ef5705594fd893bc",
//		"Post:5c136e09ef5705594fd893bb",
//		"Post:5c136e09ef5705594fd893ba",
//		"Post:5c136e09ef5705594fd893b9",
//		"Post:5c136e07ef5705594fd893b8",
//		"Post:5c136e07ef5705594fd893b7",
//		"Post:5c136e07ef5705594fd893b6",
//		"Post:5c136e07ef5705594fd893b5",
//		"Post:5c136e07ef5705594fd893b4",
//		"Post:5c136e07ef5705594fd893b3",
//		"Post:5c136e07ef5705594fd893b2",
//		"Post:5c136e07ef5705594fd893b1",
//		"Post:5c136e07ef5705594fd893b0",
//		"Post:5c136e07ef5705594fd893af",
//		"Post:5c136e06ef5705594fd893ae",
//		"Post:5c136e06ef5705594fd893ad",
//		"Post:5c136e06ef5705594fd893ac",
//		"Post:5c136e06ef5705594fd893ab",
//		"Post:5c136e06ef5705594fd893aa",
//		"Post:5c136e06ef5705594fd893a9",
//		"Post:5c136e06ef5705594fd893a8",
//		"Post:5c136e06ef5705594fd893a7",
//		"Post:5c136e06ef5705594fd893a6",
//		"Post:5c136e05ef5705594fd893a5",
//		"Post:5c136e05ef5705594fd893a4",
//		"Post:5c136e05ef5705594fd893a1",
//		"Post:5c136e05ef5705594fd893a0",
//		"Post:5c136e05ef5705594fd8939e",
//		"Post:5c136e05ef5705594fd8939d",
//		"Post:5c136e05ef5705594fd8939c",
//		"Post:5c136e05ef5705594fd8939a",
//		"Post:5c136e05ef5705594fd89399",
//		"Post:5c136e04ef5705594fd89398",
//		"Post:5c136e04ef5705594fd89397",
//		"Post:5c136e04ef5705594fd89396",
//		"Post:5c136e04ef5705594fd89395",
//		"Post:5c136e04ef5705594fd89394",
//		"Post:5c136e04ef5705594fd89393",
//		"Post:5c136e04ef5705594fd89392",
//		"Post:5c136e04ef5705594fd89391",
//		"Post:5c136e04ef5705594fd89390",
//		"Post:5c136e04ef5705594fd8938f",
//		"Post:5c136e04ef5705594fd8938e",
//		"Post:5c136e04ef5705594fd8938d",
//		"Post:5c136e04ef5705594fd8938c",
//		"Post:5c136e04ef5705594fd8938b",
//		"Post:5c136e04ef5705594fd8938a",
//		"Post:5c136e04ef5705594fd89389",
//		"Post:5c136e03ef5705594fd89387",
//		"Post:5c136e03ef5705594fd89386",
//		"Post:5c136e03ef5705594fd89383",
//		"Post:5c136e03ef5705594fd89382",
//		"Post:5c136e03ef5705594fd89381",
//		"Post:5c136e03ef5705594fd8937f",
//		"Post:5c136e03ef5705594fd8937b",
//		"Post:5c136e02ef5705594fd8937a",
//		"Post:5c136e02ef5705594fd89379",
//		"Post:5c136e02ef5705594fd89378",
//		"Post:5c136e02ef5705594fd89377",
//		"Post:5c136e02ef5705594fd89376",
//		"Post:5c136e02ef5705594fd89375",
//		"Post:5c136e02ef5705594fd89374",
//		"Post:5c136e02ef5705594fd89373",
//		"Post:5c136e02ef5705594fd89372",
//		"Post:5c136e02ef5705594fd89371",
//		"Post:5c136e02ef5705594fd89370",
//		"Post:5c136e02ef5705594fd8936f",
//		"Post:5c136e01ef5705594fd8936d",
//		"Post:5c136e01ef5705594fd8936b",
//		"Post:5c136e01ef5705594fd8936a",
//		"Post:5c136e01ef5705594fd89367",
//		"Post:5c136e01ef5705594fd89362",
//		"Post:5c136e01ef5705594fd89361",
//		"Post:5c136e00ef5705594fd89360",
//		"Post:5c136e00ef5705594fd8935f",
//		"Post:5c136e00ef5705594fd8935e",
//		"Post:5c136e00ef5705594fd8935d",
//		"Post:5c136e00ef5705594fd8935c",
//		"Post:5c136e00ef5705594fd8935b",
//		"Post:5c136e00ef5705594fd8935a",
//		"Post:5c136e00ef5705594fd89359",
//		"Post:5c136e00ef5705594fd89358",
//		"Post:5c136e00ef5705594fd89357",
//		"Post:5c136e00ef5705594fd89356",
//		"Post:5c136e00ef5705594fd89355",
//		"Post:5c136e00ef5705594fd89354",
//		"Post:5c136e00ef5705594fd89353",
//		"Post:5c136dffef5705594fd89352",
//		"Post:5c136dffef5705594fd89351",
//		"Post:5c136dffef5705594fd89350",
//		"Post:5c136dffef5705594fd8934f",
//		"Post:5c136dffef5705594fd8934d",
//		"Post:5c136dffef5705594fd8934c",
//		"Post:5c136dffef5705594fd8934b",
//		"Post:5c136dffef5705594fd8934a",
//		"Post:5c136dffef5705594fd89349",
//		"Post:5c136dffef5705594fd89348",
//		"Post:5c136dffef5705594fd89347",
//		"Post:5c136dfeef5705594fd89346",
//		"Post:5c136dfeef5705594fd89345",
//		"Post:5c136dfeef5705594fd89342",
//		"Post:5c136dfeef5705594fd89340",
//		"Post:5c136dfeef5705594fd8933f",
//		"Post:5c136dfeef5705594fd8933e",
//		"Post:5c136dfeef5705594fd8933d",
//		"Post:5c136dfeef5705594fd8933c",
//		"Post:5c136dfeef5705594fd8933b",
//		"Post:5c136dfdef5705594fd8933a",
//		"Post:5c136dfdef5705594fd89339",
//		"Post:5c136dfdef5705594fd89338",
//		"Post:5c136dfdef5705594fd89337",
//		"Post:5c136dfdef5705594fd89336",
//		"Post:5c136dfdef5705594fd89335",
//		"Post:5c136dfdef5705594fd89334",
//		"Post:5c136dfdef5705594fd89333",
//		"Post:5c136dfdef5705594fd89332",
//		"Post:5c136dfdef5705594fd89331",
//		"Post:5c136dfdef5705594fd89330",
//		"Post:5c136dfdef5705594fd8932f",
//		"Post:5c136dfcef5705594fd8932e",
//		"Post:5c136dfcef5705594fd8932d",
//		"Post:5c136dfcef5705594fd8932a",
//		"Post:5c136dfcef5705594fd89329",
//		"Post:5c136dfcef5705594fd89327",
//		"Post:5c136dfcef5705594fd89326",
//		"Post:5c136dfcef5705594fd89325",
//		"Post:5c136dfcef5705594fd89324",
//		"Post:5c136dfbef5705594fd89323",
//		"Post:5c136dfbef5705594fd89321",
//		"Post:5c136dfbef5705594fd89320",
//		"Post:5c136dfbef5705594fd8931f",
//		"Post:5c136dfbef5705594fd8931e",
//		"Post:5c136dfbef5705594fd8931d",
//		"Post:5c136dfbef5705594fd8931b",
//		"Post:5c136dfaef5705594fd89319",
//		"Post:5c136dfaef5705594fd89318",
//		"Post:5c136dfaef5705594fd89317",
//		"Post:5c136dfaef5705594fd89316",
//		"Post:5c136dfaef5705594fd89314",
//		"Post:5c136dfaef5705594fd89312",
//		"Post:5c136dfaef5705594fd89311",
//		"Post:5c136dfaef5705594fd89310",
//		"Post:5c136df9ef5705594fd8930f",
//		"Post:5c136df9ef5705594fd8930e",
//		"Post:5c136df9ef5705594fd8930d",
//		"Post:5c136df9ef5705594fd8930c",
//		"Post:5c136df9ef5705594fd8930b",
//		"Post:5c136df9ef5705594fd8930a",
//		"Post:5c136df9ef5705594fd89309",
//		"Post:5c136df9ef5705594fd89308",
//		"Post:5c136df9ef5705594fd89307",
//		"Post:5c136df8ef5705594fd89306",
//		"Post:5c136df8ef5705594fd89304",
//		"Post:5c136df8ef5705594fd89302",
//		"Post:5c136df8ef5705594fd89301",
//		"Post:5c136df8ef5705594fd89300",
//		"Post:5c136df8ef5705594fd892fe",
//		"Post:5c136df7ef5705594fd892fd",
//		"Post:5c136df7ef5705594fd892fc",
//		"Post:5c136df7ef5705594fd892fb",
//		"Post:5c136df7ef5705594fd892fa",
//		"Post:5c136df7ef5705594fd892f9",
//		"Post:5c136df7ef5705594fd892f8",
//		"Post:5c136df7ef5705594fd892f7",
//		"Post:5c136df7ef5705594fd892f6",
//		"Post:5c136df7ef5705594fd892f5",
//		"Post:5c136df7ef5705594fd892f4",
//		"Post:5c136df7ef5705594fd892f3",
//		"Post:5c136df7ef5705594fd892f2",
//		"Post:5c136df7ef5705594fd892f1",
//		"Post:5c136df7ef5705594fd892f0",
//		"Post:5c136df6ef5705594fd892ef",
//		"Post:5c136df6ef5705594fd892ee",
//		"Post:5c136df6ef5705594fd892ed",
//		"Post:5c136df6ef5705594fd892ec",
//		"Post:5c136df6ef5705594fd892eb",
//		"Post:5c136df6ef5705594fd892ea",
//		"Post:5c136df6ef5705594fd892e9",
//		"Post:5c136df6ef5705594fd892e8",
//		"Post:5c136df6ef5705594fd892e7",
//		"Post:5c136df6ef5705594fd892e6",
//		"Post:5c136df5ef5705594fd892e5",
//		"Post:5c136df5ef5705594fd892e4",
//		"Post:5c136df5ef5705594fd892e3",
//		"Post:5c136df5ef5705594fd892e1",
//		"Post:5c136df5ef5705594fd892df",
//		"Post:5c136df5ef5705594fd892dd",
//		"Post:5c136df5ef5705594fd892dc",
//		"Post:5c136df5ef5705594fd892db",
//		"Post:5c136df5ef5705594fd892da",
//		"Post:5c136df4ef5705594fd892d9",
//		"Post:5c136df4ef5705594fd892d8",
//		"Post:5c136df4ef5705594fd892d7",
//		"Post:5c136df4ef5705594fd892d6",
//		"Post:5c136df4ef5705594fd892d5",
//		"Post:5c136df4ef5705594fd892d4",
//		"Post:5c136df4ef5705594fd892d3",
//		"Post:5c136df3ef5705594fd892d2",
//		"Post:5c136df3ef5705594fd892d1",
//		"Post:5c136df3ef5705594fd892d0",
//		"Post:5c136df3ef5705594fd892cf",
//		"Post:5c136df3ef5705594fd892ce",
//		"Post:5c136df3ef5705594fd892cd",
//		"Post:5c136df2ef5705594fd892cb",
//		"Post:5c136df2ef5705594fd892c9",
//		"Post:5c136df2ef5705594fd892c6",
//		"Post:5c136df1ef5705594fd892c0",
//		"Post:5c136df1ef5705594fd892bf",
//		"Post:5c136df1ef5705594fd892be",
//		"Post:5c136df1ef5705594fd892bd",
//		"Post:5c136df1ef5705594fd892bb",
//		"Post:5c136df1ef5705594fd892ba",
//		"Post:5c136df0ef5705594fd892b6",
//		"Post:5c136df0ef5705594fd892b5",
//		"Post:5c136df0ef5705594fd892b4",
//		"Post:5c136df0ef5705594fd892b3",
//		"Post:5c136df0ef5705594fd892b2",
//		"Post:5c136df0ef5705594fd892b1",
//		"Post:5c136df0ef5705594fd892b0",
//		"Post:5c136df0ef5705594fd892af",
//		"Post:5c136df0ef5705594fd892ae",
//		"Post:5c136df0ef5705594fd892ad",
//		"Post:5c136defef5705594fd892ab",
//		"Post:5c136defef5705594fd892aa",
//		"Post:5c136defef5705594fd892a9",
//		"Post:5c136defef5705594fd892a4",
//		"Post:5c136deeef5705594fd892a2",
//		"Post:5c136deeef5705594fd892a1",
//		"Post:5c136deeef5705594fd892a0",
//		"Post:5c136deeef5705594fd8929e",
//		"Post:5c136deeef5705594fd8929d",
//		"Post:5c136deeef5705594fd8929a",
//		"Post:5c136deeef5705594fd89299",
//		"Post:5c136dedef5705594fd89295",
//		"Post:5c136dedef5705594fd89294",
//		"Post:5c136dedef5705594fd89293",
//		"Post:5c136dedef5705594fd89291",
//		"Post:5c136dedef5705594fd89290",
//		"Post:5c136dedef5705594fd8928f",
//		"Post:5c136dedef5705594fd8928e",
//		"Post:5c136dedef5705594fd8928d",
//		"Post:5c136dedef5705594fd8928b",
//		"Post:5c136decef5705594fd89288",
//		"Post:5c136decef5705594fd89287",
//		"Post:5c136decef5705594fd89286",
//		"Post:5c136decef5705594fd89285",
//		"Post:5c136decef5705594fd89284",
//		"Post:5c136decef5705594fd89282",
//		"Post:5c136decef5705594fd89281",
//		"Post:5c136decef5705594fd89280",
//		"Post:5c136decef5705594fd8927f",
//		"Post:5c136debef5705594fd8927e",
//		"Post:5c136debef5705594fd8927d",
//		"Post:5c136debef5705594fd8927c",
//		"Post:5c136debef5705594fd8927b",
//		"Post:5c136debef5705594fd8927a",
//		"Post:5c136debef5705594fd89279",
//		"Post:5c136debef5705594fd89278",
//		"Post:5c13a18def57050d7448a3ad",
//		"Post:5c13a192ef57050d7448a3fd",
//		"Post:5c13a18eef57050d7448a3bd",
//		"Post:5c13a18eef57050d7448a3bb",
//		"Post:5c1340f8ef5705594fd889ee",
//		"Post:5c1340f8ef5705594fd889ed",
//		"Post:5c1340f8ef5705594fd889ec",
//		"Post:5c1340f8ef5705594fd889eb",
//		"Post:5c13407aef5705594fd888e8",
//		"Post:5c13a18bef57050d7448a395",
//		"Post:5c133784ef5705594fd88550",
//		"Post:5c133784ef5705594fd8854c",
//		"Post:5c13a192ef57050d7448a3f8",
//		"Post:5c13a190ef57050d7448a3e2",
//		"Post:5c13a18def57050d7448a3b5",
//		"Post:5c13a18def57050d7448a3b2",
//		"Post:5c124d88ef5705594fd88365",
//		"Post:5c0e2832ef570544279beae3",
//	}
//
//
//	for _, p := range s {
//		err := testClient.ExpressAttitude(contextWithUid(2214120),strings.TrimPrefix(p, "Post:"))
//		if err != nil {
//		   t.Fatal(err)
//		}
//		_, err = testClient.ReportVisitRecord(contextWithUid(2214120), &pb.ReportVisitRecordReq{
//			PostId: strings.TrimPrefix(p, "Post:"),
//		})
//		if err != nil {
//			t.Fatal(err)
//		}
//	}
//
//	return
//
//	success := 0
//	for _, p := range rr.PostList {
//		//err := testClient.ExpressAttitude(contextWithUid(2198917), p.PostId)
//		//if err != nil {
//		//     t.Fatal(err)
//		//}
//		_, err = testClient.ReportVisitRecord(contextWithUid(2198917), &pb.ReportVisitRecordReq{
//			PostId: strings.TrimPrefix(p.PostId, "Post:"),
//		})
//		if err != nil {
//			t.Fatal(err)
//		}
//		success++
//		if success >= 500 {
//			break
//		}
//	}
//
//}

func TestGetFeeds(t *testing.T) {
	var ctx = contextWithUid(2204930)
	listResp, err := testClient.GetNewsFeeds(ctx, &pb.GetNewsFeedsReq{BaseReq: &app.BaseReq{}, Count: 2, RequestType: &pb.GetNewsFeedsReq_UserFollowingReq_{
		UserFollowingReq: &pb.GetNewsFeedsReq_UserFollowingReq{},
	}})
	if err != nil || listResp.BaseResp.Ret != 0 {
		t.Error(listResp.String())
		t.Error(err)
		return
	}
	log.Printf("输出结果为%v", listResp)

}

func TestGetTimelineFeeds(t *testing.T) {
	var ctx = contextWithUid(2204930)
	feedsResp, err := testClient.GetNewsFeeds(ctx, &pb.GetNewsFeedsReq{BaseReq: &app.BaseReq{}, Count: 1, RequestType: &pb.GetNewsFeedsReq_UserTimelineReq_{
		UserTimelineReq: &pb.GetNewsFeedsReq_UserTimelineReq{UserIdentifier: &pb.UserIdentifier{KeyType: &pb.UserIdentifier_Uid{Uid: 2204930}}}}})
	if err != nil || feedsResp.BaseResp.Ret != 0 {
		t.Error(feedsResp.String())
		t.Error(err)
		return
	}
	log.Printf("输出结果为%v", feedsResp)

}

//本地测试不了
func TestGetRecommendFollow(t *testing.T) {
	var ctx = contextWithUid(2204930)
	recommendResp, err := testClient.GetNewsFeeds(ctx, &pb.GetNewsFeedsReq{BaseReq: &app.BaseReq{}, Count: 2, RequestType: &pb.GetNewsFeedsReq_UserRecommendationReq_{
		UserRecommendationReq: &pb.GetNewsFeedsReq_UserRecommendationReq{},
	}})
	if err != nil || recommendResp.BaseResp.Ret != 0 {
		t.Error(recommendResp.String())
		t.Error(err)
		return
	}
	log.Printf("输出结果为%v", recommendResp)
}

func TestGetTopicTimeLine(t *testing.T) {
	var ctx = contextWithUid(2204930)
	topicTimeLine, err := testClient.GetNewsFeeds(ctx, &pb.GetNewsFeedsReq{BaseReq: &app.BaseReq{}, Count: 1, RequestType: &pb.GetNewsFeedsReq_TopicTimelineReq_{
		TopicTimelineReq: &pb.GetNewsFeedsReq_TopicTimelineReq{TopicId: "5bf2b99bef57057ef6ae2429"},
	}})
	if err != nil || topicTimeLine.BaseResp.Ret != 0 {
		t.Error(topicTimeLine.String())
		t.Error(err)
		return
	}
	log.Printf("输出结果为%v", topicTimeLine)
}

func TestGetAttitudeUserList(t *testing.T) {
	var ctx = contextWithUid(2198917)
	listResp, err := testClient.GetAttitudeUserList(ctx, &pb.GetAttitudeUserListReq{PostId: "5c13a18def57050d7448a3b0"})
	if err != nil || listResp.BaseResp.Ret != 0 {
		t.Error(listResp.String())
		t.Error(err)
		return
	}

	for _, user := range listResp.UserList {
		fmt.Println(user.User)
	}
}

func TestRichText(t *testing.T) {
	var uid1, uid2 uint32 = 2198917, 2214969
	var ctx = contextWithUid(uid1)
	var ctx2 = contextWithUid(uid2)

	Convey("test @user text", t, func() {
		var text = utils.ToJson(pb.RichText{Type: pb.RichTextType_AT, AtData: &pb.RichText_AtData{Uid: uid2, Account: "********", Nickname: "yidun"}})
		size := base64.StdEncoding.EncodedLen(len(text))
		afterEncode := make([]byte, size)
		base64.StdEncoding.Encode(afterEncode, []byte(text))
		var content = "hello&^" + string(afterEncode) + "&^"
		postPostResp, err := testClient.PostPost(ctx, &pb.PostPostReq{Type: pb.PostType_POST_TYPE_TEXT, Content: content})
		So(err, ShouldBeNil)

		var postId = postPostResp.PostId
		fmt.Println(postId)

		getPostResp, err := testClient.GetPost(ctx, postId)
		So(err, ShouldBeNil)
		So(getPostResp.Content, ShouldEqual, "hello@yidun ")

		//getPostResp, err = testClient.GetPost(ctx, postId)
		//So(err, ShouldBeNil)
		//So(getPostResp.Content, ShouldEqual, content)

		feedsResp, err := testClient.GetNewsFeeds(ctx, &pb.GetNewsFeedsReq{BaseReq: &app.BaseReq{}, Count: 100, RequestType: &pb.GetNewsFeedsReq_UserTimelineReq_{
			UserTimelineReq: &pb.GetNewsFeedsReq_UserTimelineReq{UserIdentifier: &pb.UserIdentifier{KeyType: &pb.UserIdentifier_Uid{Uid: uid1}}}}})
		So(err, ShouldBeNil)
		So(len(feedsResp.Feeds), ShouldBeGreaterThan, 0)
		So(feedsResp.Feeds[0].GetPost().Post.Content, ShouldEqual, "hello@yidun ")

		feedsResp, err = testClient.GetNewsFeeds(ctx, &pb.GetNewsFeedsReq{BaseReq: &app.BaseReq{}, Count: 100, ContentType: pb.ContentType_FORMATTED, RequestType: &pb.GetNewsFeedsReq_UserTimelineReq_{
			UserTimelineReq: &pb.GetNewsFeedsReq_UserTimelineReq{UserIdentifier: &pb.UserIdentifier{KeyType: &pb.UserIdentifier_Uid{Uid: uid1}}}}})
		So(err, ShouldBeNil)
		So(len(feedsResp.Feeds), ShouldBeGreaterThan, 0)
		So(feedsResp.Feeds[0].GetPost().Post.Content, ShouldEqual, content)

		text = utils.ToJson(pb.RichText{Type: pb.RichTextType_AT, AtData: &pb.RichText_AtData{Uid: uid1, Account: "********", Nickname: "kingsdom"}})
		size = base64.StdEncoding.EncodedLen(len(text))
		afterEncode = make([]byte, size)
		base64.StdEncoding.Encode(afterEncode, []byte(text))
		content = "copy that...&^&^" + string(afterEncode) + "&^"
		postCommentResp, err := testClient.PostComment(ctx2, &pb.PostCommentReq{PostId: postId, Content: content})
		So(err, ShouldBeNil)

		var commentId = postCommentResp.CommentId
		fmt.Println(commentId)

		listResp, err := testClient.GetCommentList(ctx, &pb.GetCommentListReq{PostId: postId, Count: 10})
		So(err, ShouldBeNil)
		So(len(listResp.Comments), ShouldEqual, 1)
		So(listResp.Comments[0].CommentId, ShouldEqual, commentId)
		So(listResp.Comments[0].Content, ShouldEqual, "copy that...&^@kingsdom ")

		listResp, err = testClient.GetCommentList(ctx, &pb.GetCommentListReq{PostId: postId, Count: 10, ContentType: pb.ContentType_FORMATTED})
		So(err, ShouldBeNil)
		So(len(listResp.Comments), ShouldEqual, 1)
		So(listResp.Comments[0].CommentId, ShouldEqual, commentId)
		So(listResp.Comments[0].Content, ShouldEqual, content)

		time.Sleep(time.Second * 5)

		interResp, err := userClient.GetInteractiveMsg(ctx2, &pb.GetInteractiveMsgReq{Limit: 10, Type: pb.InteractiveType_NEW_AT_MSG | pb.InteractiveType_NEW_COMMENT | pb.InteractiveType_NEW_ATTITUDE})
		So(err, ShouldBeNil)
		So(interResp.AtCount, ShouldBeGreaterThan, 0)

		interResp, err = userClient.GetInteractiveMsg(ctx, &pb.GetInteractiveMsgReq{Limit: 10, Type: pb.InteractiveType_NEW_AT_MSG | pb.InteractiveType_NEW_COMMENT | pb.InteractiveType_NEW_ATTITUDE})
		So(err, ShouldBeNil)
		So(interResp.AtCount, ShouldBeGreaterThan, 0)
	})

}

func TestUgx(t *testing.T) {

	//resp, err := testClient.GetInteractiveMsg(contextWithUid(2188754), &pb.GetInteractiveMsgReq{Type: pb.InteractiveType_NEW_ATTITUDE | pb.InteractiveType_NEW_COMMENT | pb.InteractiveType_NEW_AT_MSG})
	//if err != nil {
	//	t.Error(err)
	//	return
	//}
	//fmt.Println(resp)
	//fmt.Println(utils.ToJson(resp))
	//
	//feedsResp, err := testClient.GetNewsFeeds(contextWithUid(2207259), &pb.GetNewsFeedsReq{BaseReq: &app.BaseReq{}, Count: 10, ContentType: pb.ContentType_FORMATTED, RequestType: &pb.GetNewsFeedsReq_UserTimelineReq_{
	//	UserTimelineReq: &pb.GetNewsFeedsReq_UserTimelineReq{UserIdentifier: &pb.UserIdentifier{KeyType: &pb.UserIdentifier_Uid{Uid: 2207259}}}}})
	//fmt.Println(feedsResp)
	//fmt.Println(utils.ToJson(feedsResp))
	//
	//var c = feedsResp.Feeds[0].GetPost().Post.Content
	//fmt.Println(contentfmt.FormatPlainText(c), feedsResp.Feeds[0].GetPost().Post.PostId)
	var x = "Irish’s&^eyJ0eXBlIjoxLCJhdF9kYXRhIjp7Im5pY2tuYW1lIjoi5Li75omT5q2MIiwidWlkIjoyMjAyMjE0LCJhY2NvdW50IjoiMTAwMDM1NzAifX0==&^ 🉐️……哈哈哈，。"
	fmt.Println(x[108], len(x))
	info := contentfmt.ResolveContent(x)
	fmt.Println(info)
	fmt.Println(contentfmt.FormatPlainText(x))
	return
	feedsResp, _ := testClient.GetNewsFeeds(contextWithUid(2194199), &pb.GetNewsFeedsReq{BaseReq: &app.BaseReq{}, Count: 10, ContentType: pb.ContentType_FORMATTED, RequestType: &pb.GetNewsFeedsReq_UserTimelineReq_{
		UserTimelineReq: &pb.GetNewsFeedsReq_UserTimelineReq{UserIdentifier: &pb.UserIdentifier{KeyType: &pb.UserIdentifier_Uid{Uid: 2194199}}}}})
	fmt.Println(feedsResp)
	fmt.Println(utils.ToJson(feedsResp))

	for _, feed := range feedsResp.Feeds {
		fmt.Println(feed.GetPost().Post.Content)
		fmt.Println(contentfmt.FormatPlainText(feed.GetPost().Post.Content))
		fmt.Println("==============")
	}
}

func TestPostPost(t *testing.T) {
	resp, err := testClient.PostPost(contextWithUid(2214969), &pb.PostPostReq{
		BaseReq: &app.BaseReq{},
		Content: "123456789",
		Type:    pb.PostType_POST_TYPE_TEXT,
	})
	if err != nil {
		panic(err)
	}
	fmt.Println(resp)
	fmt.Println(resp.BaseResp.SuccessMsg)

	time.Sleep(time.Second * 2)

	cresp, err := testClient.PostComment(contextWithUid(2214969), &pb.PostCommentReq{
		PostId:  resp.PostId,
		BaseReq: &app.BaseReq{},
		Content: "123456789",
	})
	if err != nil {
		panic(err)
	}
	fmt.Println(cresp)
	fmt.Println(cresp.BaseResp.SuccessMsg)
}

func TestGetFollowing(t *testing.T) {
	var ctx = contextWithUid(2214969)
	listResp, err := testClient.GetNewsFeeds(ctx, &pb.GetNewsFeedsReq{BaseReq: &app.BaseReq{}, Count: 10, RequestType: &pb.GetNewsFeedsReq_UserFollowingReq_{
		UserFollowingReq: &pb.GetNewsFeedsReq_UserFollowingReq{},
	}})
	if err != nil || listResp.BaseResp.Ret != 0 {
		t.Error(listResp.String())
		t.Error(err)
		return
	}
	log.Printf("输出结果为%v", listResp)
	fmt.Println(utils.ToJson(listResp))

}
/*
func TestUgcLogicClient_GetShowUserFollow(t *testing.T) {
	var ctx = contextWithUid(2214969)
	// res, _ := testClient.SetShowUserFollow(ctx, &pb.SetShowUserFollowReq{BaseReq: &app.BaseReq{}, Show: false})
	// log.Printf("输出结果为%+v", res)
	// time.Sleep(time.Second)
	// res1, _ := testClient.GetShowUserFollow(ctx, &pb.GetShowUserFollowReq{BaseReq: &app.BaseReq{}})
	// log.Printf("输出结果为%+v", res1.Show)
	// time.Sleep(time.Second)
	res2, _ := testClient.SetShowUserFollow(ctx, &pb.SetShowUserFollowReq{BaseReq: &app.BaseReq{}, Show: true})
	log.Printf("输出结果为%+v", res2)
	time.Sleep(time.Second)
	res3, _ := testClient.GetShowUserFollow(ctx, &pb.GetShowUserFollowReq{BaseReq: &app.BaseReq{}})
	log.Printf("输出结果为%+v", res3.Show)
}

 */

// me : 2199998
// tester : 2212784
// postId : 5f18084f15e6940001453ad0
//var postId string = "5f2d19bffc86100001766df4"
var postId = "5f35f117a0486b000147a47f"

func TestUgcLogicClient_UpdatePostPrivacyPolicyPrivate(t *testing.T) {
	var ctx = contextWithUid(2199998)

	req := &pb.UpdatePostPrivacyPolicyReq{
		BaseReq: &app.BaseReq{},
		Policy:  pb.PostPrivacyPolicy_POST_PRIVACY_POLICY_PRIVATE,
		PostId:  postId,
	}
	resp, err := testClient.UpdatePostPrivacyPolicy(ctx, req)
	t.Log(resp, err)
}

func TestUgcLogicClient_UpdatePostPrivacyPolicyDefault(t *testing.T) {
	var ctx = contextWithUid(2199998)

	req := &pb.UpdatePostPrivacyPolicyReq{
		BaseReq: &app.BaseReq{},
		Policy:  pb.PostPrivacyPolicy_POST_PRIVACY_POLICY_DEFAULT,
		PostId:  postId,
	}
	resp, err := testClient.UpdatePostPrivacyPolicy(ctx, req)
	t.Log(resp, err)
}

func TestUgcLogicClient_GetNewestPosts(t *testing.T) {
	var ctx = contextWithUid(2199998)

	req := &pb.GetNewestPostsReq{
		BaseReq: &app.BaseReq{},
		LastPosts: []*pb.GetNewestPostsReq_LastPost{&pb.GetNewestPostsReq_LastPost{
			Account: "tt1150009853",
		}},
	}
	resp, err := testClient.GetNewestPosts(ctx, req)
	t.Log(resp, err)
}
