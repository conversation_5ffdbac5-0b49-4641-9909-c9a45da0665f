// Code generated by MockGen. DO NOT EDIT.
// Source: D:\go-tt\quicksilver\clients\chance-game-entry\iclient.go

// Package chance_game_entry is a generated GoMock package.
package chance_game_entry

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	client "golang.52tt.com/pkg/client"
	protocol "golang.52tt.com/pkg/protocol"
	chance_game_entry "golang.52tt.com/protocol/services/chance-game-entry"
	grpc "google.golang.org/grpc"
)

// MockIClient is a mock of IClient interface.
type MockIClient struct {
	ctrl     *gomock.Controller
	recorder *MockIClientMockRecorder
}

// MockIClientMockRecorder is the mock recorder for MockIClient.
type MockIClientMockRecorder struct {
	mock *MockIClient
}

// NewMockIClient creates a new mock instance.
func NewMockIClient(ctrl *gomock.Controller) *MockIClient {
	mock := &MockIClient{ctrl: ctrl}
	mock.recorder = &MockIClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIClient) EXPECT() *MockIClientMockRecorder {
	return m.recorder
}

// AddChannelBWListV2 mocks base method.
func (m *MockIClient) AddChannelBWListV2(ctx context.Context, opUid uint32, req *chance_game_entry.AddChannelBWListV2Req) (*chance_game_entry.AddChannelBWListV2Resp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddChannelBWListV2", ctx, opUid, req)
	ret0, _ := ret[0].(*chance_game_entry.AddChannelBWListV2Resp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// AddChannelBWListV2 indicates an expected call of AddChannelBWListV2.
func (mr *MockIClientMockRecorder) AddChannelBWListV2(ctx, opUid, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddChannelBWListV2", reflect.TypeOf((*MockIClient)(nil).AddChannelBWListV2), ctx, opUid, req)
}

// AddUserBWListV2 mocks base method.
func (m *MockIClient) AddUserBWListV2(ctx context.Context, opUid uint32, req *chance_game_entry.AddUserBWListV2Req) (*chance_game_entry.AddUserBWListV2Resp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddUserBWListV2", ctx, opUid, req)
	ret0, _ := ret[0].(*chance_game_entry.AddUserBWListV2Resp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// AddUserBWListV2 indicates an expected call of AddUserBWListV2.
func (mr *MockIClientMockRecorder) AddUserBWListV2(ctx, opUid, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddUserBWListV2", reflect.TypeOf((*MockIClient)(nil).AddUserBWListV2), ctx, opUid, req)
}

// BatDelChannelBWListV2 mocks base method.
func (m *MockIClient) BatDelChannelBWListV2(ctx context.Context, opUid uint32, req *chance_game_entry.BatDelChannelBWListV2Req) (*chance_game_entry.BatDelChannelBWListV2Resp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatDelChannelBWListV2", ctx, opUid, req)
	ret0, _ := ret[0].(*chance_game_entry.BatDelChannelBWListV2Resp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// BatDelChannelBWListV2 indicates an expected call of BatDelChannelBWListV2.
func (mr *MockIClientMockRecorder) BatDelChannelBWListV2(ctx, opUid, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatDelChannelBWListV2", reflect.TypeOf((*MockIClient)(nil).BatDelChannelBWListV2), ctx, opUid, req)
}

// BatDelUserBWListV2 mocks base method.
func (m *MockIClient) BatDelUserBWListV2(ctx context.Context, opUid uint32, req *chance_game_entry.BatDelUserBWListV2Req) (*chance_game_entry.BatDelUserBWListV2Resp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatDelUserBWListV2", ctx, opUid, req)
	ret0, _ := ret[0].(*chance_game_entry.BatDelUserBWListV2Resp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// BatDelUserBWListV2 indicates an expected call of BatDelUserBWListV2.
func (mr *MockIClientMockRecorder) BatDelUserBWListV2(ctx, opUid, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatDelUserBWListV2", reflect.TypeOf((*MockIClient)(nil).BatDelUserBWListV2), ctx, opUid, req)
}

// BatGetNotifyInfo mocks base method.
func (m *MockIClient) BatGetNotifyInfo(ctx context.Context, req *chance_game_entry.BatGetNotifyInfoReq, opUid uint32, opts ...grpc.CallOption) (*chance_game_entry.BatGetNotifyInfoResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, req, opUid}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatGetNotifyInfo", varargs...)
	ret0, _ := ret[0].(*chance_game_entry.BatGetNotifyInfoResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// BatGetNotifyInfo indicates an expected call of BatGetNotifyInfo.
func (mr *MockIClientMockRecorder) BatGetNotifyInfo(ctx, req, opUid interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, req, opUid}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatGetNotifyInfo", reflect.TypeOf((*MockIClient)(nil).BatGetNotifyInfo), varargs...)
}

// CC mocks base method.
func (m *MockIClient) CC() client.Conn {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CC")
	ret0, _ := ret[0].(client.Conn)
	return ret0
}

// CC indicates an expected call of CC.
func (mr *MockIClientMockRecorder) CC() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CC", reflect.TypeOf((*MockIClient)(nil).CC))
}

// CheckChanceGameIsOpen mocks base method.
func (m *MockIClient) CheckChanceGameIsOpen(ctx context.Context, req *chance_game_entry.CheckChanceGameIsOpenReq, opts ...grpc.CallOption) (*chance_game_entry.CheckChanceGameIsOpenResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, req}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CheckChanceGameIsOpen", varargs...)
	ret0, _ := ret[0].(*chance_game_entry.CheckChanceGameIsOpenResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckChanceGameIsOpen indicates an expected call of CheckChanceGameIsOpen.
func (mr *MockIClientMockRecorder) CheckChanceGameIsOpen(ctx, req interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, req}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckChanceGameIsOpen", reflect.TypeOf((*MockIClient)(nil).CheckChanceGameIsOpen), varargs...)
}

// CheckGameEntryAccess mocks base method.
func (m *MockIClient) CheckGameEntryAccess(ctx context.Context, opUid uint32, req *chance_game_entry.CheckGameEntryAccessReq) (*chance_game_entry.CheckGameEntryAccessResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckGameEntryAccess", ctx, opUid, req)
	ret0, _ := ret[0].(*chance_game_entry.CheckGameEntryAccessResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// CheckGameEntryAccess indicates an expected call of CheckGameEntryAccess.
func (mr *MockIClientMockRecorder) CheckGameEntryAccess(ctx, opUid, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckGameEntryAccess", reflect.TypeOf((*MockIClient)(nil).CheckGameEntryAccess), ctx, opUid, req)
}

// CheckMagicSpiritAccess mocks base method.
func (m *MockIClient) CheckMagicSpiritAccess(ctx context.Context, req *chance_game_entry.CheckMagicSpiritAccessReq, opUid uint32, opts ...grpc.CallOption) (*chance_game_entry.CheckMagicSpiritAccessResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, req, opUid}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CheckMagicSpiritAccess", varargs...)
	ret0, _ := ret[0].(*chance_game_entry.CheckMagicSpiritAccessResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// CheckMagicSpiritAccess indicates an expected call of CheckMagicSpiritAccess.
func (mr *MockIClientMockRecorder) CheckMagicSpiritAccess(ctx, req, opUid interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, req, opUid}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckMagicSpiritAccess", reflect.TypeOf((*MockIClient)(nil).CheckMagicSpiritAccess), varargs...)
}

// Close mocks base method.
func (m *MockIClient) Close() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Close")
}

// Close indicates an expected call of Close.
func (mr *MockIClientMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockIClient)(nil).Close))
}

// GetAllChanceGameSwitchState mocks base method.
func (m *MockIClient) GetAllChanceGameSwitchState(ctx context.Context, opUid uint32, req *chance_game_entry.GetAllChanceGameSwitchStateReq) (*chance_game_entry.GetAllChanceGameSwitchStateResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllChanceGameSwitchState", ctx, opUid, req)
	ret0, _ := ret[0].(*chance_game_entry.GetAllChanceGameSwitchStateResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetAllChanceGameSwitchState indicates an expected call of GetAllChanceGameSwitchState.
func (mr *MockIClientMockRecorder) GetAllChanceGameSwitchState(ctx, opUid, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllChanceGameSwitchState", reflect.TypeOf((*MockIClient)(nil).GetAllChanceGameSwitchState), ctx, opUid, req)
}

// GetChanceGameAccessCond mocks base method.
func (m *MockIClient) GetChanceGameAccessCond(ctx context.Context, opUid uint32, req *chance_game_entry.GetChanceGameAccessCondReq) (*chance_game_entry.GetChanceGameAccessCondResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChanceGameAccessCond", ctx, opUid, req)
	ret0, _ := ret[0].(*chance_game_entry.GetChanceGameAccessCondResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetChanceGameAccessCond indicates an expected call of GetChanceGameAccessCond.
func (mr *MockIClientMockRecorder) GetChanceGameAccessCond(ctx, opUid, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChanceGameAccessCond", reflect.TypeOf((*MockIClient)(nil).GetChanceGameAccessCond), ctx, opUid, req)
}

// GetChanceGameOpenTime mocks base method.
func (m *MockIClient) GetChanceGameOpenTime(ctx context.Context, req *chance_game_entry.GetGameSwitchOpenTimeReq, opts ...grpc.CallOption) (*chance_game_entry.GetGameSwitchOpenTimeResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, req}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetChanceGameOpenTime", varargs...)
	ret0, _ := ret[0].(*chance_game_entry.GetGameSwitchOpenTimeResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChanceGameOpenTime indicates an expected call of GetChanceGameOpenTime.
func (mr *MockIClientMockRecorder) GetChanceGameOpenTime(ctx, req interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, req}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChanceGameOpenTime", reflect.TypeOf((*MockIClient)(nil).GetChanceGameOpenTime), varargs...)
}

// GetChannelBWListInfoV2 mocks base method.
func (m *MockIClient) GetChannelBWListInfoV2(ctx context.Context, opUid uint32, req *chance_game_entry.GetChannelBWListInfoV2Req) (*chance_game_entry.GetChannelBWListInfoV2Resp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChannelBWListInfoV2", ctx, opUid, req)
	ret0, _ := ret[0].(*chance_game_entry.GetChannelBWListInfoV2Resp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetChannelBWListInfoV2 indicates an expected call of GetChannelBWListInfoV2.
func (mr *MockIClientMockRecorder) GetChannelBWListInfoV2(ctx, opUid, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChannelBWListInfoV2", reflect.TypeOf((*MockIClient)(nil).GetChannelBWListInfoV2), ctx, opUid, req)
}

// GetGameNotifyInfo mocks base method.
func (m *MockIClient) GetGameNotifyInfo(ctx context.Context, req *chance_game_entry.GetGameNotifyInfoReq, opUid uint32, opts ...grpc.CallOption) (*chance_game_entry.GetGameNotifyInfoResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, req, opUid}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetGameNotifyInfo", varargs...)
	ret0, _ := ret[0].(*chance_game_entry.GetGameNotifyInfoResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetGameNotifyInfo indicates an expected call of GetGameNotifyInfo.
func (mr *MockIClientMockRecorder) GetGameNotifyInfo(ctx, req, opUid interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, req, opUid}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGameNotifyInfo", reflect.TypeOf((*MockIClient)(nil).GetGameNotifyInfo), varargs...)
}

// GetLocalCacheConf mocks base method.
func (m *MockIClient) GetLocalCacheConf(ctx context.Context, opUid uint32, req *chance_game_entry.GetLocalCacheConfReq) (*chance_game_entry.GetLocalCacheConfResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLocalCacheConf", ctx, opUid, req)
	ret0, _ := ret[0].(*chance_game_entry.GetLocalCacheConfResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetLocalCacheConf indicates an expected call of GetLocalCacheConf.
func (mr *MockIClientMockRecorder) GetLocalCacheConf(ctx, opUid, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLocalCacheConf", reflect.TypeOf((*MockIClient)(nil).GetLocalCacheConf), ctx, opUid, req)
}

// GetUserBWListInfoV2 mocks base method.
func (m *MockIClient) GetUserBWListInfoV2(ctx context.Context, opUid uint32, req *chance_game_entry.GetUserBWListInfoV2Req) (*chance_game_entry.GetUserBWListInfoV2Resp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserBWListInfoV2", ctx, opUid, req)
	ret0, _ := ret[0].(*chance_game_entry.GetUserBWListInfoV2Resp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetUserBWListInfoV2 indicates an expected call of GetUserBWListInfoV2.
func (mr *MockIClientMockRecorder) GetUserBWListInfoV2(ctx, opUid, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserBWListInfoV2", reflect.TypeOf((*MockIClient)(nil).GetUserBWListInfoV2), ctx, opUid, req)
}

// GetUserValue mocks base method.
func (m *MockIClient) GetUserValue(ctx context.Context, req *chance_game_entry.GetUserValueReq, opUid uint32, opts ...grpc.CallOption) (*chance_game_entry.GetUserValueResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, req, opUid}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetUserValue", varargs...)
	ret0, _ := ret[0].(*chance_game_entry.GetUserValueResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetUserValue indicates an expected call of GetUserValue.
func (mr *MockIClientMockRecorder) GetUserValue(ctx, req, opUid interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, req, opUid}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserValue", reflect.TypeOf((*MockIClient)(nil).GetUserValue), varargs...)
}

// SetChanceGameAccessCond mocks base method.
func (m *MockIClient) SetChanceGameAccessCond(ctx context.Context, opUid uint32, req *chance_game_entry.SetChanceGameAccessCondReq) (*chance_game_entry.SetChanceGameAccessCondResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetChanceGameAccessCond", ctx, opUid, req)
	ret0, _ := ret[0].(*chance_game_entry.SetChanceGameAccessCondResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// SetChanceGameAccessCond indicates an expected call of SetChanceGameAccessCond.
func (mr *MockIClientMockRecorder) SetChanceGameAccessCond(ctx, opUid, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetChanceGameAccessCond", reflect.TypeOf((*MockIClient)(nil).SetChanceGameAccessCond), ctx, opUid, req)
}

// SetChanceGameOpenTime mocks base method.
func (m *MockIClient) SetChanceGameOpenTime(ctx context.Context, req *chance_game_entry.SetGameSwitchOpenTimeReq, opts ...grpc.CallOption) (*chance_game_entry.SetGameSwitchOpenTimeResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, req}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SetChanceGameOpenTime", varargs...)
	ret0, _ := ret[0].(*chance_game_entry.SetGameSwitchOpenTimeResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetChanceGameOpenTime indicates an expected call of SetChanceGameOpenTime.
func (mr *MockIClientMockRecorder) SetChanceGameOpenTime(ctx, req interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, req}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetChanceGameOpenTime", reflect.TypeOf((*MockIClient)(nil).SetChanceGameOpenTime), varargs...)
}

// SetChanceGameSwitch mocks base method.
func (m *MockIClient) SetChanceGameSwitch(ctx context.Context, opUid uint32, req *chance_game_entry.SetChanceGameSwitchReq) (*chance_game_entry.SetChanceGameSwitchResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetChanceGameSwitch", ctx, opUid, req)
	ret0, _ := ret[0].(*chance_game_entry.SetChanceGameSwitchResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// SetChanceGameSwitch indicates an expected call of SetChanceGameSwitch.
func (mr *MockIClientMockRecorder) SetChanceGameSwitch(ctx, opUid, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetChanceGameSwitch", reflect.TypeOf((*MockIClient)(nil).SetChanceGameSwitch), ctx, opUid, req)
}

// SetGameNotifyInfo mocks base method.
func (m *MockIClient) SetGameNotifyInfo(ctx context.Context, req *chance_game_entry.SetGameNotifyInfoReq, opUid uint32, opts ...grpc.CallOption) (*chance_game_entry.SetGameNotifyInfoResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, req, opUid}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SetGameNotifyInfo", varargs...)
	ret0, _ := ret[0].(*chance_game_entry.SetGameNotifyInfoResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// SetGameNotifyInfo indicates an expected call of SetGameNotifyInfo.
func (mr *MockIClientMockRecorder) SetGameNotifyInfo(ctx, req, opUid interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, req, opUid}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetGameNotifyInfo", reflect.TypeOf((*MockIClient)(nil).SetGameNotifyInfo), varargs...)
}

// Stub mocks base method.
func (m *MockIClient) Stub() interface{} {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Stub")
	ret0, _ := ret[0].(interface{})
	return ret0
}

// Stub indicates an expected call of Stub.
func (mr *MockIClientMockRecorder) Stub() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Stub", reflect.TypeOf((*MockIClient)(nil).Stub))
}
