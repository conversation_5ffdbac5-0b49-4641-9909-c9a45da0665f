// Code generated by MockGen. DO NOT EDIT.
// Source: /Users/<USER>/project/quicksilver/clients/room-master-apprentice/iclient.go

// Package roommasterapprentice is a generated GoMock package.
package roommasterapprentice

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	client "golang.52tt.com/pkg/client"
	protocol "golang.52tt.com/pkg/protocol"
	roommasterapprentice "golang.52tt.com/protocol/services/roommasterapprentice"
)

// MockIClient is a mock of IClient interface.
type MockIClient struct {
	ctrl     *gomock.Controller
	recorder *MockIClientMockRecorder
}

// MockIClientMockRecorder is the mock recorder for MockIClient.
type MockIClientMockRecorder struct {
	mock *MockIClient
}

// NewMockIClient creates a new mock instance.
func NewMockIClient(ctrl *gomock.Controller) *MockIClient {
	mock := &MockIClient{ctrl: ctrl}
	mock.recorder = &MockIClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIClient) EXPECT() *MockIClientMockRecorder {
	return m.recorder
}

// BatchAddMaster mocks base method.
func (m *MockIClient) BatchAddMaster(ctx context.Context, uids []uint32) protocol.ServerError {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchAddMaster", ctx, uids)
	ret0, _ := ret[0].(protocol.ServerError)
	return ret0
}

// BatchAddMaster indicates an expected call of BatchAddMaster.
func (mr *MockIClientMockRecorder) BatchAddMaster(ctx, uids interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchAddMaster", reflect.TypeOf((*MockIClient)(nil).BatchAddMaster), ctx, uids)
}

// BindWXUserPayInfo mocks base method.
func (m *MockIClient) BindWXUserPayInfo(ctx context.Context, in *roommasterapprentice.BindWXUserPayInfoReq) (*roommasterapprentice.BindWXUserPayInfoResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BindWXUserPayInfo", ctx, in)
	ret0, _ := ret[0].(*roommasterapprentice.BindWXUserPayInfoResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// BindWXUserPayInfo indicates an expected call of BindWXUserPayInfo.
func (mr *MockIClientMockRecorder) BindWXUserPayInfo(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BindWXUserPayInfo", reflect.TypeOf((*MockIClient)(nil).BindWXUserPayInfo), ctx, in)
}

// CC mocks base method.
func (m *MockIClient) CC() client.Conn {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CC")
	ret0, _ := ret[0].(client.Conn)
	return ret0
}

// CC indicates an expected call of CC.
func (mr *MockIClientMockRecorder) CC() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CC", reflect.TypeOf((*MockIClient)(nil).CC))
}

// CheckUserTypeList mocks base method.
func (m *MockIClient) CheckUserTypeList(ctx context.Context, uids []uint32) (*roommasterapprentice.CheckUserTypeListResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckUserTypeList", ctx, uids)
	ret0, _ := ret[0].(*roommasterapprentice.CheckUserTypeListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckUserTypeList indicates an expected call of CheckUserTypeList.
func (mr *MockIClientMockRecorder) CheckUserTypeList(ctx, uids interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckUserTypeList", reflect.TypeOf((*MockIClient)(nil).CheckUserTypeList), ctx, uids)
}

// Close mocks base method.
func (m *MockIClient) Close() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Close")
}

// Close indicates an expected call of Close.
func (mr *MockIClientMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockIClient)(nil).Close))
}

// DrawBalance mocks base method.
func (m *MockIClient) DrawBalance(ctx context.Context, in *roommasterapprentice.DrawBalanceReq) (*roommasterapprentice.DrawBalanceResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DrawBalance", ctx, in)
	ret0, _ := ret[0].(*roommasterapprentice.DrawBalanceResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// DrawBalance indicates an expected call of DrawBalance.
func (mr *MockIClientMockRecorder) DrawBalance(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DrawBalance", reflect.TypeOf((*MockIClient)(nil).DrawBalance), ctx, in)
}

// GetActConfig mocks base method.
func (m *MockIClient) GetActConfig(ctx context.Context) (*roommasterapprentice.GetActConfigResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetActConfig", ctx)
	ret0, _ := ret[0].(*roommasterapprentice.GetActConfigResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetActConfig indicates an expected call of GetActConfig.
func (mr *MockIClientMockRecorder) GetActConfig(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetActConfig", reflect.TypeOf((*MockIClient)(nil).GetActConfig), ctx)
}

// GetActivityStatistics mocks base method.
func (m *MockIClient) GetActivityStatistics(ctx context.Context, date string) (*roommasterapprentice.ActivityStatistics, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetActivityStatistics", ctx, date)
	ret0, _ := ret[0].(*roommasterapprentice.ActivityStatistics)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetActivityStatistics indicates an expected call of GetActivityStatistics.
func (mr *MockIClientMockRecorder) GetActivityStatistics(ctx, date interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetActivityStatistics", reflect.TypeOf((*MockIClient)(nil).GetActivityStatistics), ctx, date)
}

// GetBindingInfo mocks base method.
func (m *MockIClient) GetBindingInfo(ctx context.Context, in *roommasterapprentice.GetBindingInfoReq) (*roommasterapprentice.GetBindingInfoResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBindingInfo", ctx, in)
	ret0, _ := ret[0].(*roommasterapprentice.GetBindingInfoResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetBindingInfo indicates an expected call of GetBindingInfo.
func (mr *MockIClientMockRecorder) GetBindingInfo(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBindingInfo", reflect.TypeOf((*MockIClient)(nil).GetBindingInfo), ctx, in)
}

// GetOrderInfoByUserIDOrderID mocks base method.
func (m *MockIClient) GetOrderInfoByUserIDOrderID(ctx context.Context, in *roommasterapprentice.GetOrderInfoByUserIDOrderIDReq) (*roommasterapprentice.GetOrderInfoByUserIDOrderIDResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOrderInfoByUserIDOrderID", ctx, in)
	ret0, _ := ret[0].(*roommasterapprentice.GetOrderInfoByUserIDOrderIDResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetOrderInfoByUserIDOrderID indicates an expected call of GetOrderInfoByUserIDOrderID.
func (mr *MockIClientMockRecorder) GetOrderInfoByUserIDOrderID(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOrderInfoByUserIDOrderID", reflect.TypeOf((*MockIClient)(nil).GetOrderInfoByUserIDOrderID), ctx, in)
}

// GetOrderListByUserID mocks base method.
func (m *MockIClient) GetOrderListByUserID(ctx context.Context, in *roommasterapprentice.GetOrderListByUserIDReq) (*roommasterapprentice.GetOrderListByUserIDResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOrderListByUserID", ctx, in)
	ret0, _ := ret[0].(*roommasterapprentice.GetOrderListByUserIDResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetOrderListByUserID indicates an expected call of GetOrderListByUserID.
func (mr *MockIClientMockRecorder) GetOrderListByUserID(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOrderListByUserID", reflect.TypeOf((*MockIClient)(nil).GetOrderListByUserID), ctx, in)
}

// GetUserBalanceByUserID mocks base method.
func (m *MockIClient) GetUserBalanceByUserID(ctx context.Context, in *roommasterapprentice.GetUserBalanceByUserIDReq) (*roommasterapprentice.GetUserBalanceByUserIDResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserBalanceByUserID", ctx, in)
	ret0, _ := ret[0].(*roommasterapprentice.GetUserBalanceByUserIDResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetUserBalanceByUserID indicates an expected call of GetUserBalanceByUserID.
func (mr *MockIClientMockRecorder) GetUserBalanceByUserID(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserBalanceByUserID", reflect.TypeOf((*MockIClient)(nil).GetUserBalanceByUserID), ctx, in)
}

// IsMaster mocks base method.
func (m *MockIClient) IsMaster(ctx context.Context, uid uint32) (bool, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IsMaster", ctx, uid)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// IsMaster indicates an expected call of IsMaster.
func (mr *MockIClientMockRecorder) IsMaster(ctx, uid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsMaster", reflect.TypeOf((*MockIClient)(nil).IsMaster), ctx, uid)
}

// IsMasterInValidTime mocks base method.
func (m *MockIClient) IsMasterInValidTime(ctx context.Context, masterUid uint32) (*roommasterapprentice.IsMasterInValidTimeResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IsMasterInValidTime", ctx, masterUid)
	ret0, _ := ret[0].(*roommasterapprentice.IsMasterInValidTimeResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// IsMasterInValidTime indicates an expected call of IsMasterInValidTime.
func (mr *MockIClientMockRecorder) IsMasterInValidTime(ctx, masterUid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsMasterInValidTime", reflect.TypeOf((*MockIClient)(nil).IsMasterInValidTime), ctx, masterUid)
}

// MasterInitForWeb mocks base method.
func (m *MockIClient) MasterInitForWeb(ctx context.Context, masterUid uint32) (*roommasterapprentice.MasterInitForWebResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "MasterInitForWeb", ctx, masterUid)
	ret0, _ := ret[0].(*roommasterapprentice.MasterInitForWebResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// MasterInitForWeb indicates an expected call of MasterInitForWeb.
func (mr *MockIClientMockRecorder) MasterInitForWeb(ctx, masterUid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MasterInitForWeb", reflect.TypeOf((*MockIClient)(nil).MasterInitForWeb), ctx, masterUid)
}

// MasterMonitorInToday mocks base method.
func (m *MockIClient) MasterMonitorInToday(ctx context.Context, limitIncome, limitWithdraw uint32, checkTs int64) (*roommasterapprentice.MasterMonitorInTodayResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "MasterMonitorInToday", ctx, limitIncome, limitWithdraw, checkTs)
	ret0, _ := ret[0].(*roommasterapprentice.MasterMonitorInTodayResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// MasterMonitorInToday indicates an expected call of MasterMonitorInToday.
func (mr *MockIClientMockRecorder) MasterMonitorInToday(ctx, limitIncome, limitWithdraw, checkTs interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MasterMonitorInToday", reflect.TypeOf((*MockIClient)(nil).MasterMonitorInToday), ctx, limitIncome, limitWithdraw, checkTs)
}

// Stub mocks base method.
func (m *MockIClient) Stub() interface{} {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Stub")
	ret0, _ := ret[0].(interface{})
	return ret0
}

// Stub indicates an expected call of Stub.
func (mr *MockIClientMockRecorder) Stub() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Stub", reflect.TypeOf((*MockIClient)(nil).Stub))
}
