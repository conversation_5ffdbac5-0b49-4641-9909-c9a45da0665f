// Code generated by MockGen. DO NOT EDIT.
// Source: ../../conversion/iclient.go

// Package conversion is a generated GoMock package.
package conversion

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	client "golang.52tt.com/pkg/client"
	protocol "golang.52tt.com/pkg/protocol"
	conversion "golang.52tt.com/protocol/services/conversion"
)

// MockIClient is a mock of IClient interface.
type MockIClient struct {
	ctrl     *gomock.Controller
	recorder *MockIClientMockRecorder
}

// MockIClientMockRecorder is the mock recorder for MockIClient.
type MockIClientMockRecorder struct {
	mock *MockIClient
}

// NewMockIClient creates a new mock instance.
func NewMockIClient(ctrl *gomock.Controller) *MockIClient {
	mock := &MockIClient{ctrl: ctrl}
	mock.recorder = &MockIClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIClient) EXPECT() *MockIClientMockRecorder {
	return m.recorder
}

// AddComposeGiftConf mocks base method.
func (m *MockIClient) AddComposeGiftConf(ctx context.Context, uid uint32, req *conversion.AddComposeGiftConfReq) (*conversion.AddComposeGiftConfResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddComposeGiftConf", ctx, uid, req)
	ret0, _ := ret[0].(*conversion.AddComposeGiftConfResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// AddComposeGiftConf indicates an expected call of AddComposeGiftConf.
func (mr *MockIClientMockRecorder) AddComposeGiftConf(ctx, uid, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddComposeGiftConf", reflect.TypeOf((*MockIClient)(nil).AddComposeGiftConf), ctx, uid, req)
}

// AddComposeMaterialConf mocks base method.
func (m *MockIClient) AddComposeMaterialConf(ctx context.Context, uid uint32, req *conversion.AddComposeMaterialConfReq) (*conversion.AddComposeMaterialConfResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddComposeMaterialConf", ctx, uid, req)
	ret0, _ := ret[0].(*conversion.AddComposeMaterialConfResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// AddComposeMaterialConf indicates an expected call of AddComposeMaterialConf.
func (mr *MockIClientMockRecorder) AddComposeMaterialConf(ctx, uid, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddComposeMaterialConf", reflect.TypeOf((*MockIClient)(nil).AddComposeMaterialConf), ctx, uid, req)
}

// AddConversionConfig mocks base method.
func (m *MockIClient) AddConversionConfig(ctx context.Context, req conversion.AddConversionConfigReq) (conversion.AddConversionConfigResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddConversionConfig", ctx, req)
	ret0, _ := ret[0].(conversion.AddConversionConfigResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// AddConversionConfig indicates an expected call of AddConversionConfig.
func (mr *MockIClientMockRecorder) AddConversionConfig(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddConversionConfig", reflect.TypeOf((*MockIClient)(nil).AddConversionConfig), ctx, req)
}

// AddDarkComposeGiftConf mocks base method.
func (m *MockIClient) AddDarkComposeGiftConf(ctx context.Context, uid uint32, req *conversion.AddDarkComposeGiftConfReq) (*conversion.AddDarkComposeGiftConfResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddDarkComposeGiftConf", ctx, uid, req)
	ret0, _ := ret[0].(*conversion.AddDarkComposeGiftConfResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// AddDarkComposeGiftConf indicates an expected call of AddDarkComposeGiftConf.
func (mr *MockIClientMockRecorder) AddDarkComposeGiftConf(ctx, uid, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddDarkComposeGiftConf", reflect.TypeOf((*MockIClient)(nil).AddDarkComposeGiftConf), ctx, uid, req)
}

// AddDarkMaterialConf mocks base method.
func (m *MockIClient) AddDarkMaterialConf(ctx context.Context, uid uint32, req *conversion.AddDarkMaterialConfReq) (*conversion.AddDarkMaterialConfResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddDarkMaterialConf", ctx, uid, req)
	ret0, _ := ret[0].(*conversion.AddDarkMaterialConfResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// AddDarkMaterialConf indicates an expected call of AddDarkMaterialConf.
func (mr *MockIClientMockRecorder) AddDarkMaterialConf(ctx, uid, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddDarkMaterialConf", reflect.TypeOf((*MockIClient)(nil).AddDarkMaterialConf), ctx, uid, req)
}

// CC mocks base method.
func (m *MockIClient) CC() client.Conn {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CC")
	ret0, _ := ret[0].(client.Conn)
	return ret0
}

// CC indicates an expected call of CC.
func (mr *MockIClientMockRecorder) CC() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CC", reflect.TypeOf((*MockIClient)(nil).CC))
}

// CheckGiftComposeEntry mocks base method.
func (m *MockIClient) CheckGiftComposeEntry(ctx context.Context, uid uint32, req *conversion.CheckGiftComposeEntryReq) (*conversion.CheckGiftComposeEntryResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckGiftComposeEntry", ctx, uid, req)
	ret0, _ := ret[0].(*conversion.CheckGiftComposeEntryResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// CheckGiftComposeEntry indicates an expected call of CheckGiftComposeEntry.
func (mr *MockIClientMockRecorder) CheckGiftComposeEntry(ctx, uid, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckGiftComposeEntry", reflect.TypeOf((*MockIClient)(nil).CheckGiftComposeEntry), ctx, uid, req)
}

// Close mocks base method.
func (m *MockIClient) Close() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Close")
}

// Close indicates an expected call of Close.
func (mr *MockIClientMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockIClient)(nil).Close))
}

// ConversionDebris mocks base method.
func (m *MockIClient) ConversionDebris(ctx context.Context, req *conversion.ConversionDebrisReq) (*conversion.ConversionDebrisResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ConversionDebris", ctx, req)
	ret0, _ := ret[0].(*conversion.ConversionDebrisResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// ConversionDebris indicates an expected call of ConversionDebris.
func (mr *MockIClientMockRecorder) ConversionDebris(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ConversionDebris", reflect.TypeOf((*MockIClient)(nil).ConversionDebris), ctx, req)
}

// DarkGiftCompose mocks base method.
func (m *MockIClient) DarkGiftCompose(ctx context.Context, uid uint32, req *conversion.DarkGiftComposeReq) (*conversion.DarkGiftComposeResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DarkGiftCompose", ctx, uid, req)
	ret0, _ := ret[0].(*conversion.DarkGiftComposeResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// DarkGiftCompose indicates an expected call of DarkGiftCompose.
func (mr *MockIClientMockRecorder) DarkGiftCompose(ctx, uid, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DarkGiftCompose", reflect.TypeOf((*MockIClient)(nil).DarkGiftCompose), ctx, uid, req)
}

// DelComposeGiftConf mocks base method.
func (m *MockIClient) DelComposeGiftConf(ctx context.Context, uid, id uint32) (*conversion.DelComposeGiftConfResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelComposeGiftConf", ctx, uid, id)
	ret0, _ := ret[0].(*conversion.DelComposeGiftConfResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// DelComposeGiftConf indicates an expected call of DelComposeGiftConf.
func (mr *MockIClientMockRecorder) DelComposeGiftConf(ctx, uid, id interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelComposeGiftConf", reflect.TypeOf((*MockIClient)(nil).DelComposeGiftConf), ctx, uid, id)
}

// DelComposeMaterialConf mocks base method.
func (m *MockIClient) DelComposeMaterialConf(ctx context.Context, uid, materialId uint32) (*conversion.DelComposeMaterialConfResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelComposeMaterialConf", ctx, uid, materialId)
	ret0, _ := ret[0].(*conversion.DelComposeMaterialConfResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// DelComposeMaterialConf indicates an expected call of DelComposeMaterialConf.
func (mr *MockIClientMockRecorder) DelComposeMaterialConf(ctx, uid, materialId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelComposeMaterialConf", reflect.TypeOf((*MockIClient)(nil).DelComposeMaterialConf), ctx, uid, materialId)
}

// DelConversionConfByIds mocks base method.
func (m *MockIClient) DelConversionConfByIds(ctx context.Context, req *conversion.DelConversionConfByIdsReq) (*conversion.DelConversionConfByIdsResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelConversionConfByIds", ctx, req)
	ret0, _ := ret[0].(*conversion.DelConversionConfByIdsResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// DelConversionConfByIds indicates an expected call of DelConversionConfByIds.
func (mr *MockIClientMockRecorder) DelConversionConfByIds(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelConversionConfByIds", reflect.TypeOf((*MockIClient)(nil).DelConversionConfByIds), ctx, req)
}

// DelConversionConfig mocks base method.
func (m *MockIClient) DelConversionConfig(ctx context.Context, req conversion.DelConversionConfigReq) (conversion.DelConversionConfigResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelConversionConfig", ctx, req)
	ret0, _ := ret[0].(conversion.DelConversionConfigResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// DelConversionConfig indicates an expected call of DelConversionConfig.
func (mr *MockIClientMockRecorder) DelConversionConfig(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelConversionConfig", reflect.TypeOf((*MockIClient)(nil).DelConversionConfig), ctx, req)
}

// DelDarkComposeGiftConf mocks base method.
func (m *MockIClient) DelDarkComposeGiftConf(ctx context.Context, uid uint32, req *conversion.DelDarkComposeGiftConfReq) (*conversion.DelDarkComposeGiftConfResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelDarkComposeGiftConf", ctx, uid, req)
	ret0, _ := ret[0].(*conversion.DelDarkComposeGiftConfResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// DelDarkComposeGiftConf indicates an expected call of DelDarkComposeGiftConf.
func (mr *MockIClientMockRecorder) DelDarkComposeGiftConf(ctx, uid, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelDarkComposeGiftConf", reflect.TypeOf((*MockIClient)(nil).DelDarkComposeGiftConf), ctx, uid, req)
}

// DelDarkMaterialConf mocks base method.
func (m *MockIClient) DelDarkMaterialConf(ctx context.Context, uid uint32, req *conversion.DelDarkMaterialConfReq) (*conversion.DelDarkMaterialConfResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelDarkMaterialConf", ctx, uid, req)
	ret0, _ := ret[0].(*conversion.DelDarkMaterialConfResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// DelDarkMaterialConf indicates an expected call of DelDarkMaterialConf.
func (mr *MockIClientMockRecorder) DelDarkMaterialConf(ctx, uid, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelDarkMaterialConf", reflect.TypeOf((*MockIClient)(nil).DelDarkMaterialConf), ctx, uid, req)
}

// GetAllComposeGiftConf mocks base method.
func (m *MockIClient) GetAllComposeGiftConf(ctx context.Context, uid uint32) (*conversion.GetAllComposeGiftConfResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllComposeGiftConf", ctx, uid)
	ret0, _ := ret[0].(*conversion.GetAllComposeGiftConfResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetAllComposeGiftConf indicates an expected call of GetAllComposeGiftConf.
func (mr *MockIClientMockRecorder) GetAllComposeGiftConf(ctx, uid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllComposeGiftConf", reflect.TypeOf((*MockIClient)(nil).GetAllComposeGiftConf), ctx, uid)
}

// GetAllComposeMaterialConf mocks base method.
func (m *MockIClient) GetAllComposeMaterialConf(ctx context.Context, uid uint32) (*conversion.GetAllComposeMaterialConfResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllComposeMaterialConf", ctx, uid)
	ret0, _ := ret[0].(*conversion.GetAllComposeMaterialConfResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetAllComposeMaterialConf indicates an expected call of GetAllComposeMaterialConf.
func (mr *MockIClientMockRecorder) GetAllComposeMaterialConf(ctx, uid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllComposeMaterialConf", reflect.TypeOf((*MockIClient)(nil).GetAllComposeMaterialConf), ctx, uid)
}

// GetAllConversionConfig mocks base method.
func (m *MockIClient) GetAllConversionConfig(ctx context.Context, req conversion.GetAllConversionConfigReq) (conversion.GetAllConversionConfigResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllConversionConfig", ctx, req)
	ret0, _ := ret[0].(conversion.GetAllConversionConfigResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetAllConversionConfig indicates an expected call of GetAllConversionConfig.
func (mr *MockIClientMockRecorder) GetAllConversionConfig(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllConversionConfig", reflect.TypeOf((*MockIClient)(nil).GetAllConversionConfig), ctx, req)
}

// GetAllDarkComposeGiftConf mocks base method.
func (m *MockIClient) GetAllDarkComposeGiftConf(ctx context.Context, uid uint32, req *conversion.GetAllDarkComposeGiftConfReq) (*conversion.GetAllDarkComposeGiftConfResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllDarkComposeGiftConf", ctx, uid, req)
	ret0, _ := ret[0].(*conversion.GetAllDarkComposeGiftConfResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetAllDarkComposeGiftConf indicates an expected call of GetAllDarkComposeGiftConf.
func (mr *MockIClientMockRecorder) GetAllDarkComposeGiftConf(ctx, uid, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllDarkComposeGiftConf", reflect.TypeOf((*MockIClient)(nil).GetAllDarkComposeGiftConf), ctx, uid, req)
}

// GetAllDarkMaterialConf mocks base method.
func (m *MockIClient) GetAllDarkMaterialConf(ctx context.Context, uid uint32, req *conversion.GetAllDarkMaterialConfReq) (*conversion.GetAllDarkMaterialConfResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllDarkMaterialConf", ctx, uid, req)
	ret0, _ := ret[0].(*conversion.GetAllDarkMaterialConfResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetAllDarkMaterialConf indicates an expected call of GetAllDarkMaterialConf.
func (mr *MockIClientMockRecorder) GetAllDarkMaterialConf(ctx, uid, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllDarkMaterialConf", reflect.TypeOf((*MockIClient)(nil).GetAllDarkMaterialConf), ctx, uid, req)
}

// GetComposeGiftConfById mocks base method.
func (m *MockIClient) GetComposeGiftConfById(ctx context.Context, uid, id uint32) (*conversion.GetComposeGiftConfByIdResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetComposeGiftConfById", ctx, uid, id)
	ret0, _ := ret[0].(*conversion.GetComposeGiftConfByIdResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetComposeGiftConfById indicates an expected call of GetComposeGiftConfById.
func (mr *MockIClientMockRecorder) GetComposeGiftConfById(ctx, uid, id interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetComposeGiftConfById", reflect.TypeOf((*MockIClient)(nil).GetComposeGiftConfById), ctx, uid, id)
}

// GetConversionLogs mocks base method.
func (m *MockIClient) GetConversionLogs(ctx context.Context, req *conversion.GetConversionLogsReq) (*conversion.GetConversionLogsResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetConversionLogs", ctx, req)
	ret0, _ := ret[0].(*conversion.GetConversionLogsResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetConversionLogs indicates an expected call of GetConversionLogs.
func (mr *MockIClientMockRecorder) GetConversionLogs(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetConversionLogs", reflect.TypeOf((*MockIClient)(nil).GetConversionLogs), ctx, req)
}

// GetDarkComposeGiftConfById mocks base method.
func (m *MockIClient) GetDarkComposeGiftConfById(ctx context.Context, uid uint32, req *conversion.GetDarkComposeGiftConfByIdReq) (*conversion.GetDarkComposeGiftConfByIdResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetDarkComposeGiftConfById", ctx, uid, req)
	ret0, _ := ret[0].(*conversion.GetDarkComposeGiftConfByIdResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetDarkComposeGiftConfById indicates an expected call of GetDarkComposeGiftConfById.
func (mr *MockIClientMockRecorder) GetDarkComposeGiftConfById(ctx, uid, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDarkComposeGiftConfById", reflect.TypeOf((*MockIClient)(nil).GetDarkComposeGiftConfById), ctx, uid, req)
}

// GetDarkGift2Materials mocks base method.
func (m *MockIClient) GetDarkGift2Materials(ctx context.Context, uid uint32, req *conversion.GetDarkGift2MaterialsReq) (*conversion.GetDarkGift2MaterialsResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetDarkGift2Materials", ctx, uid, req)
	ret0, _ := ret[0].(*conversion.GetDarkGift2MaterialsResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetDarkGift2Materials indicates an expected call of GetDarkGift2Materials.
func (mr *MockIClientMockRecorder) GetDarkGift2Materials(ctx, uid, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDarkGift2Materials", reflect.TypeOf((*MockIClient)(nil).GetDarkGift2Materials), ctx, uid, req)
}

// GetFriendGift mocks base method.
func (m *MockIClient) GetFriendGift(ctx context.Context, req *conversion.GetFriendGiftReq) (*conversion.GetFriendGiftResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetFriendGift", ctx, req)
	ret0, _ := ret[0].(*conversion.GetFriendGiftResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetFriendGift indicates an expected call of GetFriendGift.
func (mr *MockIClientMockRecorder) GetFriendGift(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFriendGift", reflect.TypeOf((*MockIClient)(nil).GetFriendGift), ctx, req)
}

// GetUserComposeMonthLogs mocks base method.
func (m *MockIClient) GetUserComposeMonthLogs(ctx context.Context, uid uint32, req *conversion.GetUserComposeMonthLogsReq) (*conversion.GetUserComposeMonthLogsResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserComposeMonthLogs", ctx, uid, req)
	ret0, _ := ret[0].(*conversion.GetUserComposeMonthLogsResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetUserComposeMonthLogs indicates an expected call of GetUserComposeMonthLogs.
func (mr *MockIClientMockRecorder) GetUserComposeMonthLogs(ctx, uid, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserComposeMonthLogs", reflect.TypeOf((*MockIClient)(nil).GetUserComposeMonthLogs), ctx, uid, req)
}

// GetUserComposeMonthLogsCnt mocks base method.
func (m *MockIClient) GetUserComposeMonthLogsCnt(ctx context.Context, uid uint32, req *conversion.GetUserComposeMonthLogsCntReq) (uint32, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserComposeMonthLogsCnt", ctx, uid, req)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetUserComposeMonthLogsCnt indicates an expected call of GetUserComposeMonthLogsCnt.
func (mr *MockIClientMockRecorder) GetUserComposeMonthLogsCnt(ctx, uid, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserComposeMonthLogsCnt", reflect.TypeOf((*MockIClient)(nil).GetUserComposeMonthLogsCnt), ctx, uid, req)
}

// GetUserDarkComposeMonthLogs mocks base method.
func (m *MockIClient) GetUserDarkComposeMonthLogs(ctx context.Context, uid uint32, req *conversion.GetUserDarkComposeMonthLogsReq) (*conversion.GetUserDarkComposeMonthLogsResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserDarkComposeMonthLogs", ctx, uid, req)
	ret0, _ := ret[0].(*conversion.GetUserDarkComposeMonthLogsResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetUserDarkComposeMonthLogs indicates an expected call of GetUserDarkComposeMonthLogs.
func (mr *MockIClientMockRecorder) GetUserDarkComposeMonthLogs(ctx, uid, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserDarkComposeMonthLogs", reflect.TypeOf((*MockIClient)(nil).GetUserDarkComposeMonthLogs), ctx, uid, req)
}

// GetUserDarkComposeMonthLogsCnt mocks base method.
func (m *MockIClient) GetUserDarkComposeMonthLogsCnt(ctx context.Context, uid uint32, req *conversion.GetUserDarkComposeMonthLogsCntReq) (*conversion.GetUserDarkComposeMonthLogsCntResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserDarkComposeMonthLogsCnt", ctx, uid, req)
	ret0, _ := ret[0].(*conversion.GetUserDarkComposeMonthLogsCntResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetUserDarkComposeMonthLogsCnt indicates an expected call of GetUserDarkComposeMonthLogsCnt.
func (mr *MockIClientMockRecorder) GetUserDarkComposeMonthLogsCnt(ctx, uid, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserDarkComposeMonthLogsCnt", reflect.TypeOf((*MockIClient)(nil).GetUserDarkComposeMonthLogsCnt), ctx, uid, req)
}

// GiftCompose mocks base method.
func (m *MockIClient) GiftCompose(ctx context.Context, uid uint32, req *conversion.GiftComposeReq) (*conversion.GiftComposeResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GiftCompose", ctx, uid, req)
	ret0, _ := ret[0].(*conversion.GiftComposeResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GiftCompose indicates an expected call of GiftCompose.
func (mr *MockIClientMockRecorder) GiftCompose(ctx, uid, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GiftCompose", reflect.TypeOf((*MockIClient)(nil).GiftCompose), ctx, uid, req)
}

// Stub mocks base method.
func (m *MockIClient) Stub() interface{} {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Stub")
	ret0, _ := ret[0].(interface{})
	return ret0
}

// Stub indicates an expected call of Stub.
func (mr *MockIClientMockRecorder) Stub() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Stub", reflect.TypeOf((*MockIClient)(nil).Stub))
}
