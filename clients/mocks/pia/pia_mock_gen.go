// Code generated by MockGen. DO NOT EDIT.
// Source: F:\go\quicksilver\protocol\services\pia\pia.pb.go

// Package pia is a generated GoMock package.
package pia

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	pia "golang.52tt.com/protocol/services/pia"
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// MockPiaClient is a mock of PiaClient interface.
type MockPiaClient struct {
	ctrl     *gomock.Controller
	recorder *MockPiaClientMockRecorder
}

// MockPiaClientMockRecorder is the mock recorder for MockPiaClient.
type MockPiaClientMockRecorder struct {
	mock *MockPiaClient
}

// NewMockPiaClient creates a new mock instance.
func NewMockPiaClient(ctrl *gomock.Controller) *MockPiaClient {
	mock := &MockPiaClient{ctrl: ctrl}
	mock.recorder = &MockPiaClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockPiaClient) EXPECT() *MockPiaClientMockRecorder {
	return m.recorder
}

// AddStickDramaV2 mocks base method.
func (m *MockPiaClient) AddStickDramaV2(ctx context.Context, in *pia.AddStickDramaV2Req, opts ...grpc.CallOption) (*pia.EmptyMsg, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AddStickDramaV2", varargs...)
	ret0, _ := ret[0].(*pia.EmptyMsg)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddStickDramaV2 indicates an expected call of AddStickDramaV2.
func (mr *MockPiaClientMockRecorder) AddStickDramaV2(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddStickDramaV2", reflect.TypeOf((*MockPiaClient)(nil).AddStickDramaV2), varargs...)
}

// AddStickPiaRoom mocks base method.
func (m *MockPiaClient) AddStickPiaRoom(ctx context.Context, in *pia.AddStickPiaRoomReq, opts ...grpc.CallOption) (*pia.EmptyMsg, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AddStickPiaRoom", varargs...)
	ret0, _ := ret[0].(*pia.EmptyMsg)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddStickPiaRoom indicates an expected call of AddStickPiaRoom.
func (mr *MockPiaClientMockRecorder) AddStickPiaRoom(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddStickPiaRoom", reflect.TypeOf((*MockPiaClient)(nil).AddStickPiaRoom), varargs...)
}

// AggChannelOrderAndDramaInfos mocks base method.
func (m *MockPiaClient) AggChannelOrderAndDramaInfos(ctx context.Context, in *pia.AggChannelOrderAndDramaInfosReq, opts ...grpc.CallOption) (*pia.AggChannelOrderAndDramaInfosResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AggChannelOrderAndDramaInfos", varargs...)
	ret0, _ := ret[0].(*pia.AggChannelOrderAndDramaInfosResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AggChannelOrderAndDramaInfos indicates an expected call of AggChannelOrderAndDramaInfos.
func (mr *MockPiaClientMockRecorder) AggChannelOrderAndDramaInfos(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AggChannelOrderAndDramaInfos", reflect.TypeOf((*MockPiaClient)(nil).AggChannelOrderAndDramaInfos), varargs...)
}

// BatchCancelPiaRoomPermission mocks base method.
func (m *MockPiaClient) BatchCancelPiaRoomPermission(ctx context.Context, in *pia.BatchCancelPiaRoomPermissionReq, opts ...grpc.CallOption) (*pia.EmptyMsg, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchCancelPiaRoomPermission", varargs...)
	ret0, _ := ret[0].(*pia.EmptyMsg)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchCancelPiaRoomPermission indicates an expected call of BatchCancelPiaRoomPermission.
func (mr *MockPiaClientMockRecorder) BatchCancelPiaRoomPermission(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchCancelPiaRoomPermission", reflect.TypeOf((*MockPiaClient)(nil).BatchCancelPiaRoomPermission), varargs...)
}

// BatchGetDramaSubInfoByIds mocks base method.
func (m *MockPiaClient) BatchGetDramaSubInfoByIds(ctx context.Context, in *pia.BatchGetDramaSubInfoByIdsReq, opts ...grpc.CallOption) (*pia.BatchGetDramaSubInfoByIdsResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchGetDramaSubInfoByIds", varargs...)
	ret0, _ := ret[0].(*pia.BatchGetDramaSubInfoByIdsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetDramaSubInfoByIds indicates an expected call of BatchGetDramaSubInfoByIds.
func (mr *MockPiaClientMockRecorder) BatchGetDramaSubInfoByIds(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetDramaSubInfoByIds", reflect.TypeOf((*MockPiaClient)(nil).BatchGetDramaSubInfoByIds), varargs...)
}

// BatchGetPiaRoomPermission mocks base method.
func (m *MockPiaClient) BatchGetPiaRoomPermission(ctx context.Context, in *pia.BatchGetPiaRoomPermissionReq, opts ...grpc.CallOption) (*pia.BatchGetPiaRoomPermissionResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchGetPiaRoomPermission", varargs...)
	ret0, _ := ret[0].(*pia.BatchGetPiaRoomPermissionResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetPiaRoomPermission indicates an expected call of BatchGetPiaRoomPermission.
func (mr *MockPiaClientMockRecorder) BatchGetPiaRoomPermission(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetPiaRoomPermission", reflect.TypeOf((*MockPiaClient)(nil).BatchGetPiaRoomPermission), varargs...)
}

// BatchGrantPiaRoomPermission mocks base method.
func (m *MockPiaClient) BatchGrantPiaRoomPermission(ctx context.Context, in *pia.BatchGrantPiaRoomPermissionReq, opts ...grpc.CallOption) (*pia.EmptyMsg, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchGrantPiaRoomPermission", varargs...)
	ret0, _ := ret[0].(*pia.EmptyMsg)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGrantPiaRoomPermission indicates an expected call of BatchGrantPiaRoomPermission.
func (mr *MockPiaClientMockRecorder) BatchGrantPiaRoomPermission(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGrantPiaRoomPermission", reflect.TypeOf((*MockPiaClient)(nil).BatchGrantPiaRoomPermission), varargs...)
}

// CancelPiaRoomPermission mocks base method.
func (m *MockPiaClient) CancelPiaRoomPermission(ctx context.Context, in *pia.CancelPiaRoomPermissionReq, opts ...grpc.CallOption) (*pia.EmptyMsg, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CancelPiaRoomPermission", varargs...)
	ret0, _ := ret[0].(*pia.EmptyMsg)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CancelPiaRoomPermission indicates an expected call of CancelPiaRoomPermission.
func (mr *MockPiaClientMockRecorder) CancelPiaRoomPermission(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CancelPiaRoomPermission", reflect.TypeOf((*MockPiaClient)(nil).CancelPiaRoomPermission), varargs...)
}

// CheckStickDramaTime mocks base method.
func (m *MockPiaClient) CheckStickDramaTime(ctx context.Context, in *pia.CheckStickDramaTimeReq, opts ...grpc.CallOption) (*pia.EmptyMsg, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CheckStickDramaTime", varargs...)
	ret0, _ := ret[0].(*pia.EmptyMsg)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckStickDramaTime indicates an expected call of CheckStickDramaTime.
func (mr *MockPiaClientMockRecorder) CheckStickDramaTime(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckStickDramaTime", reflect.TypeOf((*MockPiaClient)(nil).CheckStickDramaTime), varargs...)
}

// ConfirmCopiedDrama mocks base method.
func (m *MockPiaClient) ConfirmCopiedDrama(ctx context.Context, in *pia.PiaConfirmCopiedDramaRequest, opts ...grpc.CallOption) (*pia.PiaConfirmCopiedDramaResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ConfirmCopiedDrama", varargs...)
	ret0, _ := ret[0].(*pia.PiaConfirmCopiedDramaResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ConfirmCopiedDrama indicates an expected call of ConfirmCopiedDrama.
func (mr *MockPiaClientMockRecorder) ConfirmCopiedDrama(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ConfirmCopiedDrama", reflect.TypeOf((*MockPiaClient)(nil).ConfirmCopiedDrama), varargs...)
}

// CreateCopiedDrama mocks base method.
func (m *MockPiaClient) CreateCopiedDrama(ctx context.Context, in *pia.PiaCreateCopiedDramaRequest, opts ...grpc.CallOption) (*pia.PiaCreateCopiedDramaResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateCopiedDrama", varargs...)
	ret0, _ := ret[0].(*pia.PiaCreateCopiedDramaResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateCopiedDrama indicates an expected call of CreateCopiedDrama.
func (mr *MockPiaClientMockRecorder) CreateCopiedDrama(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateCopiedDrama", reflect.TypeOf((*MockPiaClient)(nil).CreateCopiedDrama), varargs...)
}

// CreateDramaCopy mocks base method.
func (m *MockPiaClient) CreateDramaCopy(ctx context.Context, in *pia.PiaCreateDramaCopyReq, opts ...grpc.CallOption) (*pia.PiaCreateDramaCopyResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateDramaCopy", varargs...)
	ret0, _ := ret[0].(*pia.PiaCreateDramaCopyResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateDramaCopy indicates an expected call of CreateDramaCopy.
func (mr *MockPiaClientMockRecorder) CreateDramaCopy(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateDramaCopy", reflect.TypeOf((*MockPiaClient)(nil).CreateDramaCopy), varargs...)
}

// CreateTempDrama mocks base method.
func (m *MockPiaClient) CreateTempDrama(ctx context.Context, in *pia.PiaCreateTempDramaRequest, opts ...grpc.CallOption) (*pia.PiaCreateTempDramaResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateTempDrama", varargs...)
	ret0, _ := ret[0].(*pia.PiaCreateTempDramaResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateTempDrama indicates an expected call of CreateTempDrama.
func (mr *MockPiaClientMockRecorder) CreateTempDrama(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateTempDrama", reflect.TypeOf((*MockPiaClient)(nil).CreateTempDrama), varargs...)
}

// DelPiaRoomRecord mocks base method.
func (m *MockPiaClient) DelPiaRoomRecord(ctx context.Context, in *pia.DelPiaRoomRecordReq, opts ...grpc.CallOption) (*pia.EmptyMsg, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DelPiaRoomRecord", varargs...)
	ret0, _ := ret[0].(*pia.EmptyMsg)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DelPiaRoomRecord indicates an expected call of DelPiaRoomRecord.
func (mr *MockPiaClientMockRecorder) DelPiaRoomRecord(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelPiaRoomRecord", reflect.TypeOf((*MockPiaClient)(nil).DelPiaRoomRecord), varargs...)
}

// DeleteDramaCopy mocks base method.
func (m *MockPiaClient) DeleteDramaCopy(ctx context.Context, in *pia.DeleteDramaCopyReq, opts ...grpc.CallOption) (*pia.DeleteDramaCopyResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DeleteDramaCopy", varargs...)
	ret0, _ := ret[0].(*pia.DeleteDramaCopyResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteDramaCopy indicates an expected call of DeleteDramaCopy.
func (mr *MockPiaClientMockRecorder) DeleteDramaCopy(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteDramaCopy", reflect.TypeOf((*MockPiaClient)(nil).DeleteDramaCopy), varargs...)
}

// DeleteOrderDrama mocks base method.
func (m *MockPiaClient) DeleteOrderDrama(ctx context.Context, in *pia.DeleteOrderDramaReq, opts ...grpc.CallOption) (*pia.DeleteOrderDramaResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DeleteOrderDrama", varargs...)
	ret0, _ := ret[0].(*pia.DeleteOrderDramaResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteOrderDrama indicates an expected call of DeleteOrderDrama.
func (mr *MockPiaClientMockRecorder) DeleteOrderDrama(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteOrderDrama", reflect.TypeOf((*MockPiaClient)(nil).DeleteOrderDrama), varargs...)
}

// DeleteOrderDramaForAllChannel mocks base method.
func (m *MockPiaClient) DeleteOrderDramaForAllChannel(ctx context.Context, in *pia.DeleteOrderDramaForAllChannelRequest, opts ...grpc.CallOption) (*pia.DeleteOrderDramaForAllChannelResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DeleteOrderDramaForAllChannel", varargs...)
	ret0, _ := ret[0].(*pia.DeleteOrderDramaForAllChannelResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteOrderDramaForAllChannel indicates an expected call of DeleteOrderDramaForAllChannel.
func (mr *MockPiaClientMockRecorder) DeleteOrderDramaForAllChannel(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteOrderDramaForAllChannel", reflect.TypeOf((*MockPiaClient)(nil).DeleteOrderDramaForAllChannel), varargs...)
}

// DeleteStickDrama mocks base method.
func (m *MockPiaClient) DeleteStickDrama(ctx context.Context, in *pia.DeleteStickDramaReq, opts ...grpc.CallOption) (*pia.EmptyMsg, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DeleteStickDrama", varargs...)
	ret0, _ := ret[0].(*pia.EmptyMsg)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteStickDrama indicates an expected call of DeleteStickDrama.
func (mr *MockPiaClientMockRecorder) DeleteStickDrama(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteStickDrama", reflect.TypeOf((*MockPiaClient)(nil).DeleteStickDrama), varargs...)
}

// DoUserDramaCollect mocks base method.
func (m *MockPiaClient) DoUserDramaCollect(ctx context.Context, in *pia.DoUserDramaCollectReq, opts ...grpc.CallOption) (*pia.DoUserDramaCollectResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DoUserDramaCollect", varargs...)
	ret0, _ := ret[0].(*pia.DoUserDramaCollectResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DoUserDramaCollect indicates an expected call of DoUserDramaCollect.
func (mr *MockPiaClientMockRecorder) DoUserDramaCollect(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DoUserDramaCollect", reflect.TypeOf((*MockPiaClient)(nil).DoUserDramaCollect), varargs...)
}

// GetAuthorGenInfo mocks base method.
func (m *MockPiaClient) GetAuthorGenInfo(ctx context.Context, in *pia.PiaAuthorGenInfoReq, opts ...grpc.CallOption) (*pia.PiaAuthorGenInfoResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetAuthorGenInfo", varargs...)
	ret0, _ := ret[0].(*pia.PiaAuthorGenInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAuthorGenInfo indicates an expected call of GetAuthorGenInfo.
func (mr *MockPiaClientMockRecorder) GetAuthorGenInfo(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAuthorGenInfo", reflect.TypeOf((*MockPiaClient)(nil).GetAuthorGenInfo), varargs...)
}

// GetAuthorWorksList mocks base method.
func (m *MockPiaClient) GetAuthorWorksList(ctx context.Context, in *pia.PiaAuthorWorksListReq, opts ...grpc.CallOption) (*pia.PiaAuthorWorksListResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetAuthorWorksList", varargs...)
	ret0, _ := ret[0].(*pia.PiaAuthorWorksListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAuthorWorksList indicates an expected call of GetAuthorWorksList.
func (mr *MockPiaClientMockRecorder) GetAuthorWorksList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAuthorWorksList", reflect.TypeOf((*MockPiaClient)(nil).GetAuthorWorksList), varargs...)
}

// GetBgmInfo mocks base method.
func (m *MockPiaClient) GetBgmInfo(ctx context.Context, in *pia.GetBgmInfoReq, opts ...grpc.CallOption) (*pia.GetBgmInfoResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetBgmInfo", varargs...)
	ret0, _ := ret[0].(*pia.GetBgmInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBgmInfo indicates an expected call of GetBgmInfo.
func (mr *MockPiaClientMockRecorder) GetBgmInfo(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBgmInfo", reflect.TypeOf((*MockPiaClient)(nil).GetBgmInfo), varargs...)
}

// GetChannelDramaInfos mocks base method.
func (m *MockPiaClient) GetChannelDramaInfos(ctx context.Context, in *pia.GetChannelDramaInfosReq, opts ...grpc.CallOption) (*pia.GetChannelDramaInfosResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetChannelDramaInfos", varargs...)
	ret0, _ := ret[0].(*pia.GetChannelDramaInfosResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChannelDramaInfos indicates an expected call of GetChannelDramaInfos.
func (mr *MockPiaClientMockRecorder) GetChannelDramaInfos(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChannelDramaInfos", reflect.TypeOf((*MockPiaClient)(nil).GetChannelDramaInfos), varargs...)
}

// GetChannelPiaStatus mocks base method.
func (m *MockPiaClient) GetChannelPiaStatus(ctx context.Context, in *pia.GetChannelPiaStatusReq, opts ...grpc.CallOption) (*pia.GetChannelPiaStatusResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetChannelPiaStatus", varargs...)
	ret0, _ := ret[0].(*pia.GetChannelPiaStatusResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChannelPiaStatus indicates an expected call of GetChannelPiaStatus.
func (mr *MockPiaClientMockRecorder) GetChannelPiaStatus(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChannelPiaStatus", reflect.TypeOf((*MockPiaClient)(nil).GetChannelPiaStatus), varargs...)
}

// GetChannelTopConfigInfos mocks base method.
func (m *MockPiaClient) GetChannelTopConfigInfos(ctx context.Context, in *pia.GetChannelTopConfigInfosReq, opts ...grpc.CallOption) (*pia.GetChannelTopConfigInfosResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetChannelTopConfigInfos", varargs...)
	ret0, _ := ret[0].(*pia.GetChannelTopConfigInfosResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChannelTopConfigInfos indicates an expected call of GetChannelTopConfigInfos.
func (mr *MockPiaClientMockRecorder) GetChannelTopConfigInfos(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChannelTopConfigInfos", reflect.TypeOf((*MockPiaClient)(nil).GetChannelTopConfigInfos), varargs...)
}

// GetCopiedDramaList mocks base method.
func (m *MockPiaClient) GetCopiedDramaList(ctx context.Context, in *pia.PiaCopyDramaListRequest, opts ...grpc.CallOption) (*pia.PiaCopyDramaListResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetCopiedDramaList", varargs...)
	ret0, _ := ret[0].(*pia.PiaCopyDramaListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCopiedDramaList indicates an expected call of GetCopiedDramaList.
func (mr *MockPiaClientMockRecorder) GetCopiedDramaList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCopiedDramaList", reflect.TypeOf((*MockPiaClient)(nil).GetCopiedDramaList), varargs...)
}

// GetCurrentPiaInfo mocks base method.
func (m *MockPiaClient) GetCurrentPiaInfo(ctx context.Context, in *pia.GetCurrentPiaInfoReq, opts ...grpc.CallOption) (*pia.GetCurrentPiaInfoResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetCurrentPiaInfo", varargs...)
	ret0, _ := ret[0].(*pia.GetCurrentPiaInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCurrentPiaInfo indicates an expected call of GetCurrentPiaInfo.
func (mr *MockPiaClientMockRecorder) GetCurrentPiaInfo(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCurrentPiaInfo", reflect.TypeOf((*MockPiaClient)(nil).GetCurrentPiaInfo), varargs...)
}

// GetDrama mocks base method.
func (m *MockPiaClient) GetDrama(ctx context.Context, in *pia.GetDramaReq, opts ...grpc.CallOption) (*pia.GetDramaResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetDrama", varargs...)
	ret0, _ := ret[0].(*pia.GetDramaResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetDrama indicates an expected call of GetDrama.
func (mr *MockPiaClientMockRecorder) GetDrama(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDrama", reflect.TypeOf((*MockPiaClient)(nil).GetDrama), varargs...)
}

// GetDramaDetailByID mocks base method.
func (m *MockPiaClient) GetDramaDetailByID(ctx context.Context, in *pia.GetDramaDetailByIDReq, opts ...grpc.CallOption) (*pia.GetDramaDetailByIDResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetDramaDetailByID", varargs...)
	ret0, _ := ret[0].(*pia.GetDramaDetailByIDResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetDramaDetailByID indicates an expected call of GetDramaDetailByID.
func (mr *MockPiaClientMockRecorder) GetDramaDetailByID(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDramaDetailByID", reflect.TypeOf((*MockPiaClient)(nil).GetDramaDetailByID), varargs...)
}

// GetDramaDetailByIdV2 mocks base method.
func (m *MockPiaClient) GetDramaDetailByIdV2(ctx context.Context, in *pia.GetDramaDetailByIdV2Req, opts ...grpc.CallOption) (*pia.GetDramaDetailByIdV2Resp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetDramaDetailByIdV2", varargs...)
	ret0, _ := ret[0].(*pia.GetDramaDetailByIdV2Resp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetDramaDetailByIdV2 indicates an expected call of GetDramaDetailByIdV2.
func (mr *MockPiaClientMockRecorder) GetDramaDetailByIdV2(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDramaDetailByIdV2", reflect.TypeOf((*MockPiaClient)(nil).GetDramaDetailByIdV2), varargs...)
}

// GetDramaExpansion mocks base method.
func (m *MockPiaClient) GetDramaExpansion(ctx context.Context, in *pia.GetDramaExpansionReq, opts ...grpc.CallOption) (*pia.GetDramaExpansionResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetDramaExpansion", varargs...)
	ret0, _ := ret[0].(*pia.GetDramaExpansionResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetDramaExpansion indicates an expected call of GetDramaExpansion.
func (mr *MockPiaClientMockRecorder) GetDramaExpansion(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDramaExpansion", reflect.TypeOf((*MockPiaClient)(nil).GetDramaExpansion), varargs...)
}

// GetDramaInfoByDramaId mocks base method.
func (m *MockPiaClient) GetDramaInfoByDramaId(ctx context.Context, in *pia.GetDramaInfoByDramaIdReq, opts ...grpc.CallOption) (*pia.GetDramaInfoByDramaIdResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetDramaInfoByDramaId", varargs...)
	ret0, _ := ret[0].(*pia.GetDramaInfoByDramaIdResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetDramaInfoByDramaId indicates an expected call of GetDramaInfoByDramaId.
func (mr *MockPiaClientMockRecorder) GetDramaInfoByDramaId(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDramaInfoByDramaId", reflect.TypeOf((*MockPiaClient)(nil).GetDramaInfoByDramaId), varargs...)
}

// GetDramaList mocks base method.
func (m *MockPiaClient) GetDramaList(ctx context.Context, in *pia.GetDramaListReq, opts ...grpc.CallOption) (*pia.GetDramaListResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetDramaList", varargs...)
	ret0, _ := ret[0].(*pia.GetDramaListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetDramaList indicates an expected call of GetDramaList.
func (mr *MockPiaClientMockRecorder) GetDramaList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDramaList", reflect.TypeOf((*MockPiaClient)(nil).GetDramaList), varargs...)
}

// GetDramaListByIds mocks base method.
func (m *MockPiaClient) GetDramaListByIds(ctx context.Context, in *pia.GetDramaListByIdsReq, opts ...grpc.CallOption) (*pia.GetDramaListByIdsResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetDramaListByIds", varargs...)
	ret0, _ := ret[0].(*pia.GetDramaListByIdsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetDramaListByIds indicates an expected call of GetDramaListByIds.
func (mr *MockPiaClientMockRecorder) GetDramaListByIds(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDramaListByIds", reflect.TypeOf((*MockPiaClient)(nil).GetDramaListByIds), varargs...)
}

// GetMyDramaCopyList mocks base method.
func (m *MockPiaClient) GetMyDramaCopyList(ctx context.Context, in *pia.GetMyDramaCopyListReq, opts ...grpc.CallOption) (*pia.GetMyDramaCopyListResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetMyDramaCopyList", varargs...)
	ret0, _ := ret[0].(*pia.GetMyDramaCopyListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMyDramaCopyList indicates an expected call of GetMyDramaCopyList.
func (mr *MockPiaClientMockRecorder) GetMyDramaCopyList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMyDramaCopyList", reflect.TypeOf((*MockPiaClient)(nil).GetMyDramaCopyList), varargs...)
}

// GetMyDramaPlayingRecord mocks base method.
func (m *MockPiaClient) GetMyDramaPlayingRecord(ctx context.Context, in *pia.GetMyDramaPlayingRecordReq, opts ...grpc.CallOption) (*pia.GetMyDramaPlayingRecordResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetMyDramaPlayingRecord", varargs...)
	ret0, _ := ret[0].(*pia.GetMyDramaPlayingRecordResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMyDramaPlayingRecord indicates an expected call of GetMyDramaPlayingRecord.
func (mr *MockPiaClientMockRecorder) GetMyDramaPlayingRecord(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMyDramaPlayingRecord", reflect.TypeOf((*MockPiaClient)(nil).GetMyDramaPlayingRecord), varargs...)
}

// GetOrderDramaList mocks base method.
func (m *MockPiaClient) GetOrderDramaList(ctx context.Context, in *pia.GetOrderDramaListReq, opts ...grpc.CallOption) (*pia.GetOrderDramaListResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetOrderDramaList", varargs...)
	ret0, _ := ret[0].(*pia.GetOrderDramaListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOrderDramaList indicates an expected call of GetOrderDramaList.
func (mr *MockPiaClientMockRecorder) GetOrderDramaList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOrderDramaList", reflect.TypeOf((*MockPiaClient)(nil).GetOrderDramaList), varargs...)
}

// GetPiaRoomPermission mocks base method.
func (m *MockPiaClient) GetPiaRoomPermission(ctx context.Context, in *pia.GetPiaRoomPermissionReq, opts ...grpc.CallOption) (*pia.GetPiaRoomPermissionResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetPiaRoomPermission", varargs...)
	ret0, _ := ret[0].(*pia.GetPiaRoomPermissionResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPiaRoomPermission indicates an expected call of GetPiaRoomPermission.
func (mr *MockPiaClientMockRecorder) GetPiaRoomPermission(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPiaRoomPermission", reflect.TypeOf((*MockPiaClient)(nil).GetPiaRoomPermission), varargs...)
}

// GetPlayingChannel mocks base method.
func (m *MockPiaClient) GetPlayingChannel(ctx context.Context, in *pia.GetPlayingChannelReq, opts ...grpc.CallOption) (*pia.GetPlayingChannelResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetPlayingChannel", varargs...)
	ret0, _ := ret[0].(*pia.GetPlayingChannelResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPlayingChannel indicates an expected call of GetPlayingChannel.
func (mr *MockPiaClientMockRecorder) GetPlayingChannel(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPlayingChannel", reflect.TypeOf((*MockPiaClient)(nil).GetPlayingChannel), varargs...)
}

// GetPlayingChannelV2 mocks base method.
func (m *MockPiaClient) GetPlayingChannelV2(ctx context.Context, in *pia.GetPlayingChannelV2Req, opts ...grpc.CallOption) (*pia.GetPlayingChannelV2Resp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetPlayingChannelV2", varargs...)
	ret0, _ := ret[0].(*pia.GetPlayingChannelV2Resp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPlayingChannelV2 indicates an expected call of GetPlayingChannelV2.
func (mr *MockPiaClientMockRecorder) GetPlayingChannelV2(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPlayingChannelV2", reflect.TypeOf((*MockPiaClient)(nil).GetPlayingChannelV2), varargs...)
}

// GetSearchOptionGroup mocks base method.
func (m *MockPiaClient) GetSearchOptionGroup(ctx context.Context, in *pia.GetSearchOptionGroupReq, opts ...grpc.CallOption) (*pia.GetSearchOptionGroupResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetSearchOptionGroup", varargs...)
	ret0, _ := ret[0].(*pia.GetSearchOptionGroupResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSearchOptionGroup indicates an expected call of GetSearchOptionGroup.
func (mr *MockPiaClientMockRecorder) GetSearchOptionGroup(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSearchOptionGroup", reflect.TypeOf((*MockPiaClient)(nil).GetSearchOptionGroup), varargs...)
}

// GetSearchOptionGroupV2 mocks base method.
func (m *MockPiaClient) GetSearchOptionGroupV2(ctx context.Context, in *pia.GetSearchOptionGroupV2Req, opts ...grpc.CallOption) (*pia.GetSearchOptionGroupV2Resp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetSearchOptionGroupV2", varargs...)
	ret0, _ := ret[0].(*pia.GetSearchOptionGroupV2Resp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSearchOptionGroupV2 indicates an expected call of GetSearchOptionGroupV2.
func (mr *MockPiaClientMockRecorder) GetSearchOptionGroupV2(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSearchOptionGroupV2", reflect.TypeOf((*MockPiaClient)(nil).GetSearchOptionGroupV2), varargs...)
}

// GetStickDramaTags mocks base method.
func (m *MockPiaClient) GetStickDramaTags(ctx context.Context, in *pia.GetStickDramaTagsReq, opts ...grpc.CallOption) (*pia.GetStickDramaTagsResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetStickDramaTags", varargs...)
	ret0, _ := ret[0].(*pia.GetStickDramaTagsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetStickDramaTags indicates an expected call of GetStickDramaTags.
func (mr *MockPiaClientMockRecorder) GetStickDramaTags(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetStickDramaTags", reflect.TypeOf((*MockPiaClient)(nil).GetStickDramaTags), varargs...)
}

// GetStickPiaRoom mocks base method.
func (m *MockPiaClient) GetStickPiaRoom(ctx context.Context, in *pia.GetStickPiaRoomReq, opts ...grpc.CallOption) (*pia.GetStickPiaRoomResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetStickPiaRoom", varargs...)
	ret0, _ := ret[0].(*pia.GetStickPiaRoomResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetStickPiaRoom indicates an expected call of GetStickPiaRoom.
func (mr *MockPiaClientMockRecorder) GetStickPiaRoom(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetStickPiaRoom", reflect.TypeOf((*MockPiaClient)(nil).GetStickPiaRoom), varargs...)
}

// GetTempDrama mocks base method.
func (m *MockPiaClient) GetTempDrama(ctx context.Context, in *pia.PiaGetTempDramaRequest, opts ...grpc.CallOption) (*pia.PiaGetTempDramaResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetTempDrama", varargs...)
	ret0, _ := ret[0].(*pia.PiaGetTempDramaResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTempDrama indicates an expected call of GetTempDrama.
func (mr *MockPiaClientMockRecorder) GetTempDrama(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTempDrama", reflect.TypeOf((*MockPiaClient)(nil).GetTempDrama), varargs...)
}

// GetTimeOverlapped mocks base method.
func (m *MockPiaClient) GetTimeOverlapped(ctx context.Context, in *pia.GetTimeOverlappedReq, opts ...grpc.CallOption) (*pia.EmptyMsg, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetTimeOverlapped", varargs...)
	ret0, _ := ret[0].(*pia.EmptyMsg)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTimeOverlapped indicates an expected call of GetTimeOverlapped.
func (mr *MockPiaClientMockRecorder) GetTimeOverlapped(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTimeOverlapped", reflect.TypeOf((*MockPiaClient)(nil).GetTimeOverlapped), varargs...)
}

// GetUgcChannels mocks base method.
func (m *MockPiaClient) GetUgcChannels(ctx context.Context, in *pia.GetUgcChannelsReq, opts ...grpc.CallOption) (*pia.GetUgcChannelsResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetUgcChannels", varargs...)
	ret0, _ := ret[0].(*pia.GetUgcChannelsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUgcChannels indicates an expected call of GetUgcChannels.
func (mr *MockPiaClientMockRecorder) GetUgcChannels(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUgcChannels", reflect.TypeOf((*MockPiaClient)(nil).GetUgcChannels), varargs...)
}

// GetUgcChannelsV2 mocks base method.
func (m *MockPiaClient) GetUgcChannelsV2(ctx context.Context, in *pia.GetUgcChannelsReqV2, opts ...grpc.CallOption) (*pia.GetUgcChannelsRespV2, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetUgcChannelsV2", varargs...)
	ret0, _ := ret[0].(*pia.GetUgcChannelsRespV2)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUgcChannelsV2 indicates an expected call of GetUgcChannelsV2.
func (mr *MockPiaClientMockRecorder) GetUgcChannelsV2(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUgcChannelsV2", reflect.TypeOf((*MockPiaClient)(nil).GetUgcChannelsV2), varargs...)
}

// GetUserDramaCollList mocks base method.
func (m *MockPiaClient) GetUserDramaCollList(ctx context.Context, in *pia.GetUserDramaCollListReq, opts ...grpc.CallOption) (*pia.GetUserDramaCollListResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetUserDramaCollList", varargs...)
	ret0, _ := ret[0].(*pia.GetUserDramaCollListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserDramaCollList indicates an expected call of GetUserDramaCollList.
func (mr *MockPiaClientMockRecorder) GetUserDramaCollList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserDramaCollList", reflect.TypeOf((*MockPiaClient)(nil).GetUserDramaCollList), varargs...)
}

// GetUserDramaStatus mocks base method.
func (m *MockPiaClient) GetUserDramaStatus(ctx context.Context, in *pia.GetUserDramaStatusReq, opts ...grpc.CallOption) (*pia.GetUserDramaStatusResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetUserDramaStatus", varargs...)
	ret0, _ := ret[0].(*pia.GetUserDramaStatusResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserDramaStatus indicates an expected call of GetUserDramaStatus.
func (mr *MockPiaClientMockRecorder) GetUserDramaStatus(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserDramaStatus", reflect.TypeOf((*MockPiaClient)(nil).GetUserDramaStatus), varargs...)
}

// GrantPiaRoomPermission mocks base method.
func (m *MockPiaClient) GrantPiaRoomPermission(ctx context.Context, in *pia.GrantPiaRoomPermissionReq, opts ...grpc.CallOption) (*pia.EmptyMsg, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GrantPiaRoomPermission", varargs...)
	ret0, _ := ret[0].(*pia.EmptyMsg)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GrantPiaRoomPermission indicates an expected call of GrantPiaRoomPermission.
func (mr *MockPiaClientMockRecorder) GrantPiaRoomPermission(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GrantPiaRoomPermission", reflect.TypeOf((*MockPiaClient)(nil).GrantPiaRoomPermission), varargs...)
}

// HandleDramaReportResult mocks base method.
func (m *MockPiaClient) HandleDramaReportResult(ctx context.Context, in *pia.PiaHandleDramaReportResultReq, opts ...grpc.CallOption) (*pia.PiaHandleDramaReportResultResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "HandleDramaReportResult", varargs...)
	ret0, _ := ret[0].(*pia.PiaHandleDramaReportResultResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// HandleDramaReportResult indicates an expected call of HandleDramaReportResult.
func (mr *MockPiaClientMockRecorder) HandleDramaReportResult(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HandleDramaReportResult", reflect.TypeOf((*MockPiaClient)(nil).HandleDramaReportResult), varargs...)
}

// InternalStopChannel mocks base method.
func (m *MockPiaClient) InternalStopChannel(ctx context.Context, in *pia.InternalStopChannelReq, opts ...grpc.CallOption) (*pia.InternalStopChannelResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "InternalStopChannel", varargs...)
	ret0, _ := ret[0].(*pia.InternalStopChannelResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// InternalStopChannel indicates an expected call of InternalStopChannel.
func (mr *MockPiaClientMockRecorder) InternalStopChannel(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InternalStopChannel", reflect.TypeOf((*MockPiaClient)(nil).InternalStopChannel), varargs...)
}

// OrderDrama mocks base method.
func (m *MockPiaClient) OrderDrama(ctx context.Context, in *pia.OrderDramaReq, opts ...grpc.CallOption) (*pia.OrderDramaResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "OrderDrama", varargs...)
	ret0, _ := ret[0].(*pia.OrderDramaResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// OrderDrama indicates an expected call of OrderDrama.
func (mr *MockPiaClientMockRecorder) OrderDrama(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "OrderDrama", reflect.TypeOf((*MockPiaClient)(nil).OrderDrama), varargs...)
}

// PauseBgm mocks base method.
func (m *MockPiaClient) PauseBgm(ctx context.Context, in *pia.PiaOperateBgmReq, opts ...grpc.CallOption) (*pia.PiaOperateBgmResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PauseBgm", varargs...)
	ret0, _ := ret[0].(*pia.PiaOperateBgmResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PauseBgm indicates an expected call of PauseBgm.
func (mr *MockPiaClientMockRecorder) PauseBgm(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PauseBgm", reflect.TypeOf((*MockPiaClient)(nil).PauseBgm), varargs...)
}

// PauseDrama mocks base method.
func (m *MockPiaClient) PauseDrama(ctx context.Context, in *pia.PiaOperateDramaReq, opts ...grpc.CallOption) (*pia.PiaOperateDramaResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PauseDrama", varargs...)
	ret0, _ := ret[0].(*pia.PiaOperateDramaResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PauseDrama indicates an expected call of PauseDrama.
func (mr *MockPiaClientMockRecorder) PauseDrama(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PauseDrama", reflect.TypeOf((*MockPiaClient)(nil).PauseDrama), varargs...)
}

// PiaAddDramaFeedBack mocks base method.
func (m *MockPiaClient) PiaAddDramaFeedBack(ctx context.Context, in *pia.PiaAddDramaFeedBackReq, opts ...grpc.CallOption) (*pia.PiaAddDramaFeedBackResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PiaAddDramaFeedBack", varargs...)
	ret0, _ := ret[0].(*pia.PiaAddDramaFeedBackResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PiaAddDramaFeedBack indicates an expected call of PiaAddDramaFeedBack.
func (mr *MockPiaClientMockRecorder) PiaAddDramaFeedBack(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PiaAddDramaFeedBack", reflect.TypeOf((*MockPiaClient)(nil).PiaAddDramaFeedBack), varargs...)
}

// PiaBatchDeleteMyPlayingRecord mocks base method.
func (m *MockPiaClient) PiaBatchDeleteMyPlayingRecord(ctx context.Context, in *pia.PiaBatchDeleteMyPlayingRecordReq, opts ...grpc.CallOption) (*pia.PiaBatchDeleteMyPlayingRecordResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PiaBatchDeleteMyPlayingRecord", varargs...)
	ret0, _ := ret[0].(*pia.PiaBatchDeleteMyPlayingRecordResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PiaBatchDeleteMyPlayingRecord indicates an expected call of PiaBatchDeleteMyPlayingRecord.
func (mr *MockPiaClientMockRecorder) PiaBatchDeleteMyPlayingRecord(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PiaBatchDeleteMyPlayingRecord", reflect.TypeOf((*MockPiaClient)(nil).PiaBatchDeleteMyPlayingRecord), varargs...)
}

// PiaBatchGetAuthorBaseInfo mocks base method.
func (m *MockPiaClient) PiaBatchGetAuthorBaseInfo(ctx context.Context, in *pia.PiaBatchGetAuthorBaseInfoReq, opts ...grpc.CallOption) (*pia.PiaBatchGetAuthorBaseInfoResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PiaBatchGetAuthorBaseInfo", varargs...)
	ret0, _ := ret[0].(*pia.PiaBatchGetAuthorBaseInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PiaBatchGetAuthorBaseInfo indicates an expected call of PiaBatchGetAuthorBaseInfo.
func (mr *MockPiaClientMockRecorder) PiaBatchGetAuthorBaseInfo(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PiaBatchGetAuthorBaseInfo", reflect.TypeOf((*MockPiaClient)(nil).PiaBatchGetAuthorBaseInfo), varargs...)
}

// PiaCancelSelectRole mocks base method.
func (m *MockPiaClient) PiaCancelSelectRole(ctx context.Context, in *pia.PiaCancelSelectRoleReq, opts ...grpc.CallOption) (*pia.PiaCancelSelectRoleResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PiaCancelSelectRole", varargs...)
	ret0, _ := ret[0].(*pia.PiaCancelSelectRoleResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PiaCancelSelectRole indicates an expected call of PiaCancelSelectRole.
func (mr *MockPiaClientMockRecorder) PiaCancelSelectRole(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PiaCancelSelectRole", reflect.TypeOf((*MockPiaClient)(nil).PiaCancelSelectRole), varargs...)
}

// PiaCancelSelectRoleV2 mocks base method.
func (m *MockPiaClient) PiaCancelSelectRoleV2(ctx context.Context, in *pia.PiaCancelSelectRoleReq, opts ...grpc.CallOption) (*pia.PiaCancelSelectRoleResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PiaCancelSelectRoleV2", varargs...)
	ret0, _ := ret[0].(*pia.PiaCancelSelectRoleResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PiaCancelSelectRoleV2 indicates an expected call of PiaCancelSelectRoleV2.
func (mr *MockPiaClientMockRecorder) PiaCancelSelectRoleV2(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PiaCancelSelectRoleV2", reflect.TypeOf((*MockPiaClient)(nil).PiaCancelSelectRoleV2), varargs...)
}

// PiaChangePlayType mocks base method.
func (m *MockPiaClient) PiaChangePlayType(ctx context.Context, in *pia.PiaChangePlayTypeReq, opts ...grpc.CallOption) (*pia.PiaChangePlayTypeResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PiaChangePlayType", varargs...)
	ret0, _ := ret[0].(*pia.PiaChangePlayTypeResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PiaChangePlayType indicates an expected call of PiaChangePlayType.
func (mr *MockPiaClientMockRecorder) PiaChangePlayType(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PiaChangePlayType", reflect.TypeOf((*MockPiaClient)(nil).PiaChangePlayType), varargs...)
}

// PiaChangePlayTypeV2 mocks base method.
func (m *MockPiaClient) PiaChangePlayTypeV2(ctx context.Context, in *pia.PiaChangePlayTypeV2Request, opts ...grpc.CallOption) (*pia.PiaChangePlayTypeV2Response, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PiaChangePlayTypeV2", varargs...)
	ret0, _ := ret[0].(*pia.PiaChangePlayTypeV2Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PiaChangePlayTypeV2 indicates an expected call of PiaChangePlayTypeV2.
func (mr *MockPiaClientMockRecorder) PiaChangePlayTypeV2(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PiaChangePlayTypeV2", reflect.TypeOf((*MockPiaClient)(nil).PiaChangePlayTypeV2), varargs...)
}

// PiaFollowMic mocks base method.
func (m *MockPiaClient) PiaFollowMic(ctx context.Context, in *pia.PiaFollowMicRequest, opts ...grpc.CallOption) (*pia.PiaFollowMicResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PiaFollowMic", varargs...)
	ret0, _ := ret[0].(*pia.PiaFollowMicResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PiaFollowMic indicates an expected call of PiaFollowMic.
func (mr *MockPiaClientMockRecorder) PiaFollowMic(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PiaFollowMic", reflect.TypeOf((*MockPiaClient)(nil).PiaFollowMic), varargs...)
}

// PiaGetDramaCopyId mocks base method.
func (m *MockPiaClient) PiaGetDramaCopyId(ctx context.Context, in *pia.PiaGetDramaCopyIdReq, opts ...grpc.CallOption) (*pia.PiaGetDramaCopyIdResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PiaGetDramaCopyId", varargs...)
	ret0, _ := ret[0].(*pia.PiaGetDramaCopyIdResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PiaGetDramaCopyId indicates an expected call of PiaGetDramaCopyId.
func (mr *MockPiaClientMockRecorder) PiaGetDramaCopyId(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PiaGetDramaCopyId", reflect.TypeOf((*MockPiaClient)(nil).PiaGetDramaCopyId), varargs...)
}

// PiaGetDramaStatus mocks base method.
func (m *MockPiaClient) PiaGetDramaStatus(ctx context.Context, in *pia.PiaGetDramaStatusReq, opts ...grpc.CallOption) (*pia.PiaGetDramaStatusResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PiaGetDramaStatus", varargs...)
	ret0, _ := ret[0].(*pia.PiaGetDramaStatusResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PiaGetDramaStatus indicates an expected call of PiaGetDramaStatus.
func (mr *MockPiaClientMockRecorder) PiaGetDramaStatus(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PiaGetDramaStatus", reflect.TypeOf((*MockPiaClient)(nil).PiaGetDramaStatus), varargs...)
}

// PiaGetDramaStatusV2 mocks base method.
func (m *MockPiaClient) PiaGetDramaStatusV2(ctx context.Context, in *pia.PiaGetDramaStatusReq, opts ...grpc.CallOption) (*pia.PiaGetDramaStatusResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PiaGetDramaStatusV2", varargs...)
	ret0, _ := ret[0].(*pia.PiaGetDramaStatusResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PiaGetDramaStatusV2 indicates an expected call of PiaGetDramaStatusV2.
func (mr *MockPiaClientMockRecorder) PiaGetDramaStatusV2(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PiaGetDramaStatusV2", reflect.TypeOf((*MockPiaClient)(nil).PiaGetDramaStatusV2), varargs...)
}

// PiaGetFollowedStatusOfMicList mocks base method.
func (m *MockPiaClient) PiaGetFollowedStatusOfMicList(ctx context.Context, in *pia.PiaGetFollowedStatusOfMicListRequest, opts ...grpc.CallOption) (*pia.PiaGetFollowedStatusOfMicListResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PiaGetFollowedStatusOfMicList", varargs...)
	ret0, _ := ret[0].(*pia.PiaGetFollowedStatusOfMicListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PiaGetFollowedStatusOfMicList indicates an expected call of PiaGetFollowedStatusOfMicList.
func (mr *MockPiaClientMockRecorder) PiaGetFollowedStatusOfMicList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PiaGetFollowedStatusOfMicList", reflect.TypeOf((*MockPiaClient)(nil).PiaGetFollowedStatusOfMicList), varargs...)
}

// PiaGetMyFollowInfo mocks base method.
func (m *MockPiaClient) PiaGetMyFollowInfo(ctx context.Context, in *pia.PiaGetMyFollowInfoRequest, opts ...grpc.CallOption) (*pia.PiaGetMyFollowInfoResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PiaGetMyFollowInfo", varargs...)
	ret0, _ := ret[0].(*pia.PiaGetMyFollowInfoResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PiaGetMyFollowInfo indicates an expected call of PiaGetMyFollowInfo.
func (mr *MockPiaClientMockRecorder) PiaGetMyFollowInfo(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PiaGetMyFollowInfo", reflect.TypeOf((*MockPiaClient)(nil).PiaGetMyFollowInfo), varargs...)
}

// PiaGetMyPlayingRecordIdList mocks base method.
func (m *MockPiaClient) PiaGetMyPlayingRecordIdList(ctx context.Context, in *pia.PiaGetMyPlayingRecordIdListReq, opts ...grpc.CallOption) (*pia.PiaGetMyPlayingRecordIdListResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PiaGetMyPlayingRecordIdList", varargs...)
	ret0, _ := ret[0].(*pia.PiaGetMyPlayingRecordIdListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PiaGetMyPlayingRecordIdList indicates an expected call of PiaGetMyPlayingRecordIdList.
func (mr *MockPiaClientMockRecorder) PiaGetMyPlayingRecordIdList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PiaGetMyPlayingRecordIdList", reflect.TypeOf((*MockPiaClient)(nil).PiaGetMyPlayingRecordIdList), varargs...)
}

// PiaGetPreviousDialogueIndex mocks base method.
func (m *MockPiaClient) PiaGetPreviousDialogueIndex(ctx context.Context, in *pia.PiaGetPreviousDialogueIndexRequest, opts ...grpc.CallOption) (*pia.PiaGetPreviousDialogueIndexResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PiaGetPreviousDialogueIndex", varargs...)
	ret0, _ := ret[0].(*pia.PiaGetPreviousDialogueIndexResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PiaGetPreviousDialogueIndex indicates an expected call of PiaGetPreviousDialogueIndex.
func (mr *MockPiaClientMockRecorder) PiaGetPreviousDialogueIndex(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PiaGetPreviousDialogueIndex", reflect.TypeOf((*MockPiaClient)(nil).PiaGetPreviousDialogueIndex), varargs...)
}

// PiaGetRankingList mocks base method.
func (m *MockPiaClient) PiaGetRankingList(ctx context.Context, in *pia.PiaGetRankingListReq, opts ...grpc.CallOption) (*pia.PiaGetRankingListResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PiaGetRankingList", varargs...)
	ret0, _ := ret[0].(*pia.PiaGetRankingListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PiaGetRankingList indicates an expected call of PiaGetRankingList.
func (mr *MockPiaClientMockRecorder) PiaGetRankingList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PiaGetRankingList", reflect.TypeOf((*MockPiaClient)(nil).PiaGetRankingList), varargs...)
}

// PiaOperateBgm mocks base method.
func (m *MockPiaClient) PiaOperateBgm(ctx context.Context, in *pia.PiaOperateBgmReq, opts ...grpc.CallOption) (*pia.PiaOperateBgmResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PiaOperateBgm", varargs...)
	ret0, _ := ret[0].(*pia.PiaOperateBgmResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PiaOperateBgm indicates an expected call of PiaOperateBgm.
func (mr *MockPiaClientMockRecorder) PiaOperateBgm(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PiaOperateBgm", reflect.TypeOf((*MockPiaClient)(nil).PiaOperateBgm), varargs...)
}

// PiaOperateBgmVol mocks base method.
func (m *MockPiaClient) PiaOperateBgmVol(ctx context.Context, in *pia.PiaOperateBgmVolReq, opts ...grpc.CallOption) (*pia.PiaOperateBgmVolResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PiaOperateBgmVol", varargs...)
	ret0, _ := ret[0].(*pia.PiaOperateBgmVolResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PiaOperateBgmVol indicates an expected call of PiaOperateBgmVol.
func (mr *MockPiaClientMockRecorder) PiaOperateBgmVol(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PiaOperateBgmVol", reflect.TypeOf((*MockPiaClient)(nil).PiaOperateBgmVol), varargs...)
}

// PiaOperateDrama mocks base method.
func (m *MockPiaClient) PiaOperateDrama(ctx context.Context, in *pia.PiaOperateDramaReq, opts ...grpc.CallOption) (*pia.PiaOperateDramaResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PiaOperateDrama", varargs...)
	ret0, _ := ret[0].(*pia.PiaOperateDramaResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PiaOperateDrama indicates an expected call of PiaOperateDrama.
func (mr *MockPiaClientMockRecorder) PiaOperateDrama(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PiaOperateDrama", reflect.TypeOf((*MockPiaClient)(nil).PiaOperateDrama), varargs...)
}

// PiaRebuildDramaCollectedCache mocks base method.
func (m *MockPiaClient) PiaRebuildDramaCollectedCache(ctx context.Context, in *pia.PiaRebuildDramaCollectedCacheReq, opts ...grpc.CallOption) (*pia.PiaRebuildDramaCollectedCacheResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PiaRebuildDramaCollectedCache", varargs...)
	ret0, _ := ret[0].(*pia.PiaRebuildDramaCollectedCacheResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PiaRebuildDramaCollectedCache indicates an expected call of PiaRebuildDramaCollectedCache.
func (mr *MockPiaClientMockRecorder) PiaRebuildDramaCollectedCache(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PiaRebuildDramaCollectedCache", reflect.TypeOf((*MockPiaClient)(nil).PiaRebuildDramaCollectedCache), varargs...)
}

// PiaReportDialogueIndex mocks base method.
func (m *MockPiaClient) PiaReportDialogueIndex(ctx context.Context, in *pia.PiaReportDialogueIndexRequest, opts ...grpc.CallOption) (*pia.PiaReportDialogueIndexResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PiaReportDialogueIndex", varargs...)
	ret0, _ := ret[0].(*pia.PiaReportDialogueIndexResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PiaReportDialogueIndex indicates an expected call of PiaReportDialogueIndex.
func (mr *MockPiaClientMockRecorder) PiaReportDialogueIndex(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PiaReportDialogueIndex", reflect.TypeOf((*MockPiaClient)(nil).PiaReportDialogueIndex), varargs...)
}

// PiaSelectRole mocks base method.
func (m *MockPiaClient) PiaSelectRole(ctx context.Context, in *pia.PiaSelectRoleReq, opts ...grpc.CallOption) (*pia.PiaSelectRoleResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PiaSelectRole", varargs...)
	ret0, _ := ret[0].(*pia.PiaSelectRoleResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PiaSelectRole indicates an expected call of PiaSelectRole.
func (mr *MockPiaClientMockRecorder) PiaSelectRole(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PiaSelectRole", reflect.TypeOf((*MockPiaClient)(nil).PiaSelectRole), varargs...)
}

// PiaSelectRoleV2 mocks base method.
func (m *MockPiaClient) PiaSelectRoleV2(ctx context.Context, in *pia.PiaSelectRoleReq, opts ...grpc.CallOption) (*pia.PiaSelectRoleResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PiaSelectRoleV2", varargs...)
	ret0, _ := ret[0].(*pia.PiaSelectRoleResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PiaSelectRoleV2 indicates an expected call of PiaSelectRoleV2.
func (mr *MockPiaClientMockRecorder) PiaSelectRoleV2(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PiaSelectRoleV2", reflect.TypeOf((*MockPiaClient)(nil).PiaSelectRoleV2), varargs...)
}

// PiaSendDialogueIndex mocks base method.
func (m *MockPiaClient) PiaSendDialogueIndex(ctx context.Context, in *pia.PiaSendDialogueIndexRequest, opts ...grpc.CallOption) (*pia.PiaSendDialogueIndexResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PiaSendDialogueIndex", varargs...)
	ret0, _ := ret[0].(*pia.PiaSendDialogueIndexResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PiaSendDialogueIndex indicates an expected call of PiaSendDialogueIndex.
func (mr *MockPiaClientMockRecorder) PiaSendDialogueIndex(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PiaSendDialogueIndex", reflect.TypeOf((*MockPiaClient)(nil).PiaSendDialogueIndex), varargs...)
}

// PiaUnFollowMic mocks base method.
func (m *MockPiaClient) PiaUnFollowMic(ctx context.Context, in *pia.PiaUnFollowMicRequest, opts ...grpc.CallOption) (*pia.PiaUnFollowMicResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PiaUnFollowMic", varargs...)
	ret0, _ := ret[0].(*pia.PiaUnFollowMicResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PiaUnFollowMic indicates an expected call of PiaUnFollowMic.
func (mr *MockPiaClientMockRecorder) PiaUnFollowMic(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PiaUnFollowMic", reflect.TypeOf((*MockPiaClient)(nil).PiaUnFollowMic), varargs...)
}

// PlayBgm mocks base method.
func (m *MockPiaClient) PlayBgm(ctx context.Context, in *pia.PiaOperateBgmReq, opts ...grpc.CallOption) (*pia.PiaOperateBgmResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PlayBgm", varargs...)
	ret0, _ := ret[0].(*pia.PiaOperateBgmResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PlayBgm indicates an expected call of PlayBgm.
func (mr *MockPiaClientMockRecorder) PlayBgm(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PlayBgm", reflect.TypeOf((*MockPiaClient)(nil).PlayBgm), varargs...)
}

// PlayDrama mocks base method.
func (m *MockPiaClient) PlayDrama(ctx context.Context, in *pia.PiaOperateDramaReq, opts ...grpc.CallOption) (*pia.PiaOperateDramaResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PlayDrama", varargs...)
	ret0, _ := ret[0].(*pia.PiaOperateDramaResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PlayDrama indicates an expected call of PlayDrama.
func (mr *MockPiaClientMockRecorder) PlayDrama(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PlayDrama", reflect.TypeOf((*MockPiaClient)(nil).PlayDrama), varargs...)
}

// PreformDrama mocks base method.
func (m *MockPiaClient) PreformDrama(ctx context.Context, in *pia.PerformDramaRequest, opts ...grpc.CallOption) (*pia.PerformDramaResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PreformDrama", varargs...)
	ret0, _ := ret[0].(*pia.PerformDramaResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PreformDrama indicates an expected call of PreformDrama.
func (mr *MockPiaClientMockRecorder) PreformDrama(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PreformDrama", reflect.TypeOf((*MockPiaClient)(nil).PreformDrama), varargs...)
}

// RebuildAuthorWorksCache mocks base method.
func (m *MockPiaClient) RebuildAuthorWorksCache(ctx context.Context, in *pia.PiaAuthorGenInfoRebuildReq, opts ...grpc.CallOption) (*pia.PiaAuthorGenInfoRebuildResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "RebuildAuthorWorksCache", varargs...)
	ret0, _ := ret[0].(*pia.PiaAuthorGenInfoRebuildResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RebuildAuthorWorksCache indicates an expected call of RebuildAuthorWorksCache.
func (mr *MockPiaClientMockRecorder) RebuildAuthorWorksCache(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RebuildAuthorWorksCache", reflect.TypeOf((*MockPiaClient)(nil).RebuildAuthorWorksCache), varargs...)
}

// SearchStickDrama mocks base method.
func (m *MockPiaClient) SearchStickDrama(ctx context.Context, in *pia.SearchStickDramaReq, opts ...grpc.CallOption) (*pia.SearchStickDramaResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SearchStickDrama", varargs...)
	ret0, _ := ret[0].(*pia.SearchStickDramaResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SearchStickDrama indicates an expected call of SearchStickDrama.
func (mr *MockPiaClientMockRecorder) SearchStickDrama(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SearchStickDrama", reflect.TypeOf((*MockPiaClient)(nil).SearchStickDrama), varargs...)
}

// SelectDrama mocks base method.
func (m *MockPiaClient) SelectDrama(ctx context.Context, in *pia.SelectDramaReq, opts ...grpc.CallOption) (*pia.SelectDramaResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SelectDrama", varargs...)
	ret0, _ := ret[0].(*pia.SelectDramaResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SelectDrama indicates an expected call of SelectDrama.
func (mr *MockPiaClientMockRecorder) SelectDrama(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SelectDrama", reflect.TypeOf((*MockPiaClient)(nil).SelectDrama), varargs...)
}

// SelectDramaV2 mocks base method.
func (m *MockPiaClient) SelectDramaV2(ctx context.Context, in *pia.SelectDramaV2Req, opts ...grpc.CallOption) (*pia.SelectDramaV2Resp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SelectDramaV2", varargs...)
	ret0, _ := ret[0].(*pia.SelectDramaV2Resp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SelectDramaV2 indicates an expected call of SelectDramaV2.
func (mr *MockPiaClientMockRecorder) SelectDramaV2(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SelectDramaV2", reflect.TypeOf((*MockPiaClient)(nil).SelectDramaV2), varargs...)
}

// SetBgmInfo mocks base method.
func (m *MockPiaClient) SetBgmInfo(ctx context.Context, in *pia.SetBgmInfoReq, opts ...grpc.CallOption) (*pia.SetBgmInfoResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SetBgmInfo", varargs...)
	ret0, _ := ret[0].(*pia.SetBgmInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetBgmInfo indicates an expected call of SetBgmInfo.
func (mr *MockPiaClientMockRecorder) SetBgmInfo(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetBgmInfo", reflect.TypeOf((*MockPiaClient)(nil).SetBgmInfo), varargs...)
}

// SetBgmVol mocks base method.
func (m *MockPiaClient) SetBgmVol(ctx context.Context, in *pia.PiaOperateBgmVolReq, opts ...grpc.CallOption) (*pia.PiaOperateBgmVolResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SetBgmVol", varargs...)
	ret0, _ := ret[0].(*pia.PiaOperateBgmVolResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetBgmVol indicates an expected call of SetBgmVol.
func (mr *MockPiaClientMockRecorder) SetBgmVol(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetBgmVol", reflect.TypeOf((*MockPiaClient)(nil).SetBgmVol), varargs...)
}

// SetCompereMic mocks base method.
func (m *MockPiaClient) SetCompereMic(ctx context.Context, in *pia.SetCompereMicReq, opts ...grpc.CallOption) (*pia.SetCompereMicResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SetCompereMic", varargs...)
	ret0, _ := ret[0].(*pia.SetCompereMicResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetCompereMic indicates an expected call of SetCompereMic.
func (mr *MockPiaClientMockRecorder) SetCompereMic(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetCompereMic", reflect.TypeOf((*MockPiaClient)(nil).SetCompereMic), varargs...)
}

// SetDramaCopyStatus mocks base method.
func (m *MockPiaClient) SetDramaCopyStatus(ctx context.Context, in *pia.SetDramaCopyStatusReq, opts ...grpc.CallOption) (*pia.SetDramaCopyStatusResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SetDramaCopyStatus", varargs...)
	ret0, _ := ret[0].(*pia.SetDramaCopyStatusResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetDramaCopyStatus indicates an expected call of SetDramaCopyStatus.
func (mr *MockPiaClientMockRecorder) SetDramaCopyStatus(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetDramaCopyStatus", reflect.TypeOf((*MockPiaClient)(nil).SetDramaCopyStatus), varargs...)
}

// SetPiaPhase mocks base method.
func (m *MockPiaClient) SetPiaPhase(ctx context.Context, in *pia.SetPiaPhaseReq, opts ...grpc.CallOption) (*pia.SetPiaPhaseResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SetPiaPhase", varargs...)
	ret0, _ := ret[0].(*pia.SetPiaPhaseResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetPiaPhase indicates an expected call of SetPiaPhase.
func (mr *MockPiaClientMockRecorder) SetPiaPhase(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetPiaPhase", reflect.TypeOf((*MockPiaClient)(nil).SetPiaPhase), varargs...)
}

// SetPiaProgress mocks base method.
func (m *MockPiaClient) SetPiaProgress(ctx context.Context, in *pia.SetPiaProgressReq, opts ...grpc.CallOption) (*pia.SetPiaProgressResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SetPiaProgress", varargs...)
	ret0, _ := ret[0].(*pia.SetPiaProgressResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetPiaProgress indicates an expected call of SetPiaProgress.
func (mr *MockPiaClientMockRecorder) SetPiaProgress(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetPiaProgress", reflect.TypeOf((*MockPiaClient)(nil).SetPiaProgress), varargs...)
}

// SetPiaSwitch mocks base method.
func (m *MockPiaClient) SetPiaSwitch(ctx context.Context, in *pia.SetPiaSwitchReq, opts ...grpc.CallOption) (*pia.SetPiaSwitchResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SetPiaSwitch", varargs...)
	ret0, _ := ret[0].(*pia.SetPiaSwitchResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetPiaSwitch indicates an expected call of SetPiaSwitch.
func (mr *MockPiaClientMockRecorder) SetPiaSwitch(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetPiaSwitch", reflect.TypeOf((*MockPiaClient)(nil).SetPiaSwitch), varargs...)
}

// SetPiaSwitchV2 mocks base method.
func (m *MockPiaClient) SetPiaSwitchV2(ctx context.Context, in *pia.SetPiaSwitchV2Req, opts ...grpc.CallOption) (*pia.SetPiaSwitchV2Resp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SetPiaSwitchV2", varargs...)
	ret0, _ := ret[0].(*pia.SetPiaSwitchV2Resp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetPiaSwitchV2 indicates an expected call of SetPiaSwitchV2.
func (mr *MockPiaClientMockRecorder) SetPiaSwitchV2(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetPiaSwitchV2", reflect.TypeOf((*MockPiaClient)(nil).SetPiaSwitchV2), varargs...)
}

// StopDrama mocks base method.
func (m *MockPiaClient) StopDrama(ctx context.Context, in *pia.PiaOperateDramaReq, opts ...grpc.CallOption) (*pia.PiaOperateDramaResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "StopDrama", varargs...)
	ret0, _ := ret[0].(*pia.PiaOperateDramaResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// StopDrama indicates an expected call of StopDrama.
func (mr *MockPiaClientMockRecorder) StopDrama(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "StopDrama", reflect.TypeOf((*MockPiaClient)(nil).StopDrama), varargs...)
}

// TestDeleteDrama mocks base method.
func (m *MockPiaClient) TestDeleteDrama(ctx context.Context, in *pia.TestDeleteDramaReq, opts ...grpc.CallOption) (*pia.TestDeleteDramaResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "TestDeleteDrama", varargs...)
	ret0, _ := ret[0].(*pia.TestDeleteDramaResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// TestDeleteDrama indicates an expected call of TestDeleteDrama.
func (mr *MockPiaClientMockRecorder) TestDeleteDrama(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TestDeleteDrama", reflect.TypeOf((*MockPiaClient)(nil).TestDeleteDrama), varargs...)
}

// UpdateStickDramaV2 mocks base method.
func (m *MockPiaClient) UpdateStickDramaV2(ctx context.Context, in *pia.UpdateStickDramaV2Req, opts ...grpc.CallOption) (*pia.EmptyMsg, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateStickDramaV2", varargs...)
	ret0, _ := ret[0].(*pia.EmptyMsg)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateStickDramaV2 indicates an expected call of UpdateStickDramaV2.
func (mr *MockPiaClientMockRecorder) UpdateStickDramaV2(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateStickDramaV2", reflect.TypeOf((*MockPiaClient)(nil).UpdateStickDramaV2), varargs...)
}

// UpdateStickPiaRoom mocks base method.
func (m *MockPiaClient) UpdateStickPiaRoom(ctx context.Context, in *pia.UpdateStickPiaRoomReq, opts ...grpc.CallOption) (*pia.EmptyMsg, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateStickPiaRoom", varargs...)
	ret0, _ := ret[0].(*pia.EmptyMsg)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateStickPiaRoom indicates an expected call of UpdateStickPiaRoom.
func (mr *MockPiaClientMockRecorder) UpdateStickPiaRoom(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateStickPiaRoom", reflect.TypeOf((*MockPiaClient)(nil).UpdateStickPiaRoom), varargs...)
}

// MockPiaServer is a mock of PiaServer interface.
type MockPiaServer struct {
	ctrl     *gomock.Controller
	recorder *MockPiaServerMockRecorder
}

// MockPiaServerMockRecorder is the mock recorder for MockPiaServer.
type MockPiaServerMockRecorder struct {
	mock *MockPiaServer
}

// NewMockPiaServer creates a new mock instance.
func NewMockPiaServer(ctrl *gomock.Controller) *MockPiaServer {
	mock := &MockPiaServer{ctrl: ctrl}
	mock.recorder = &MockPiaServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockPiaServer) EXPECT() *MockPiaServerMockRecorder {
	return m.recorder
}

// AddStickDramaV2 mocks base method.
func (m *MockPiaServer) AddStickDramaV2(arg0 context.Context, arg1 *pia.AddStickDramaV2Req) (*pia.EmptyMsg, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddStickDramaV2", arg0, arg1)
	ret0, _ := ret[0].(*pia.EmptyMsg)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddStickDramaV2 indicates an expected call of AddStickDramaV2.
func (mr *MockPiaServerMockRecorder) AddStickDramaV2(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddStickDramaV2", reflect.TypeOf((*MockPiaServer)(nil).AddStickDramaV2), arg0, arg1)
}

// AddStickPiaRoom mocks base method.
func (m *MockPiaServer) AddStickPiaRoom(arg0 context.Context, arg1 *pia.AddStickPiaRoomReq) (*pia.EmptyMsg, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddStickPiaRoom", arg0, arg1)
	ret0, _ := ret[0].(*pia.EmptyMsg)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddStickPiaRoom indicates an expected call of AddStickPiaRoom.
func (mr *MockPiaServerMockRecorder) AddStickPiaRoom(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddStickPiaRoom", reflect.TypeOf((*MockPiaServer)(nil).AddStickPiaRoom), arg0, arg1)
}

// AggChannelOrderAndDramaInfos mocks base method.
func (m *MockPiaServer) AggChannelOrderAndDramaInfos(arg0 context.Context, arg1 *pia.AggChannelOrderAndDramaInfosReq) (*pia.AggChannelOrderAndDramaInfosResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AggChannelOrderAndDramaInfos", arg0, arg1)
	ret0, _ := ret[0].(*pia.AggChannelOrderAndDramaInfosResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AggChannelOrderAndDramaInfos indicates an expected call of AggChannelOrderAndDramaInfos.
func (mr *MockPiaServerMockRecorder) AggChannelOrderAndDramaInfos(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AggChannelOrderAndDramaInfos", reflect.TypeOf((*MockPiaServer)(nil).AggChannelOrderAndDramaInfos), arg0, arg1)
}

// BatchCancelPiaRoomPermission mocks base method.
func (m *MockPiaServer) BatchCancelPiaRoomPermission(arg0 context.Context, arg1 *pia.BatchCancelPiaRoomPermissionReq) (*pia.EmptyMsg, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchCancelPiaRoomPermission", arg0, arg1)
	ret0, _ := ret[0].(*pia.EmptyMsg)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchCancelPiaRoomPermission indicates an expected call of BatchCancelPiaRoomPermission.
func (mr *MockPiaServerMockRecorder) BatchCancelPiaRoomPermission(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchCancelPiaRoomPermission", reflect.TypeOf((*MockPiaServer)(nil).BatchCancelPiaRoomPermission), arg0, arg1)
}

// BatchGetDramaSubInfoByIds mocks base method.
func (m *MockPiaServer) BatchGetDramaSubInfoByIds(arg0 context.Context, arg1 *pia.BatchGetDramaSubInfoByIdsReq) (*pia.BatchGetDramaSubInfoByIdsResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetDramaSubInfoByIds", arg0, arg1)
	ret0, _ := ret[0].(*pia.BatchGetDramaSubInfoByIdsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetDramaSubInfoByIds indicates an expected call of BatchGetDramaSubInfoByIds.
func (mr *MockPiaServerMockRecorder) BatchGetDramaSubInfoByIds(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetDramaSubInfoByIds", reflect.TypeOf((*MockPiaServer)(nil).BatchGetDramaSubInfoByIds), arg0, arg1)
}

// BatchGetPiaRoomPermission mocks base method.
func (m *MockPiaServer) BatchGetPiaRoomPermission(arg0 context.Context, arg1 *pia.BatchGetPiaRoomPermissionReq) (*pia.BatchGetPiaRoomPermissionResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetPiaRoomPermission", arg0, arg1)
	ret0, _ := ret[0].(*pia.BatchGetPiaRoomPermissionResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetPiaRoomPermission indicates an expected call of BatchGetPiaRoomPermission.
func (mr *MockPiaServerMockRecorder) BatchGetPiaRoomPermission(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetPiaRoomPermission", reflect.TypeOf((*MockPiaServer)(nil).BatchGetPiaRoomPermission), arg0, arg1)
}

// BatchGrantPiaRoomPermission mocks base method.
func (m *MockPiaServer) BatchGrantPiaRoomPermission(arg0 context.Context, arg1 *pia.BatchGrantPiaRoomPermissionReq) (*pia.EmptyMsg, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGrantPiaRoomPermission", arg0, arg1)
	ret0, _ := ret[0].(*pia.EmptyMsg)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGrantPiaRoomPermission indicates an expected call of BatchGrantPiaRoomPermission.
func (mr *MockPiaServerMockRecorder) BatchGrantPiaRoomPermission(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGrantPiaRoomPermission", reflect.TypeOf((*MockPiaServer)(nil).BatchGrantPiaRoomPermission), arg0, arg1)
}

// CancelPiaRoomPermission mocks base method.
func (m *MockPiaServer) CancelPiaRoomPermission(arg0 context.Context, arg1 *pia.CancelPiaRoomPermissionReq) (*pia.EmptyMsg, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CancelPiaRoomPermission", arg0, arg1)
	ret0, _ := ret[0].(*pia.EmptyMsg)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CancelPiaRoomPermission indicates an expected call of CancelPiaRoomPermission.
func (mr *MockPiaServerMockRecorder) CancelPiaRoomPermission(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CancelPiaRoomPermission", reflect.TypeOf((*MockPiaServer)(nil).CancelPiaRoomPermission), arg0, arg1)
}

// CheckStickDramaTime mocks base method.
func (m *MockPiaServer) CheckStickDramaTime(arg0 context.Context, arg1 *pia.CheckStickDramaTimeReq) (*pia.EmptyMsg, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckStickDramaTime", arg0, arg1)
	ret0, _ := ret[0].(*pia.EmptyMsg)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckStickDramaTime indicates an expected call of CheckStickDramaTime.
func (mr *MockPiaServerMockRecorder) CheckStickDramaTime(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckStickDramaTime", reflect.TypeOf((*MockPiaServer)(nil).CheckStickDramaTime), arg0, arg1)
}

// ConfirmCopiedDrama mocks base method.
func (m *MockPiaServer) ConfirmCopiedDrama(arg0 context.Context, arg1 *pia.PiaConfirmCopiedDramaRequest) (*pia.PiaConfirmCopiedDramaResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ConfirmCopiedDrama", arg0, arg1)
	ret0, _ := ret[0].(*pia.PiaConfirmCopiedDramaResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ConfirmCopiedDrama indicates an expected call of ConfirmCopiedDrama.
func (mr *MockPiaServerMockRecorder) ConfirmCopiedDrama(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ConfirmCopiedDrama", reflect.TypeOf((*MockPiaServer)(nil).ConfirmCopiedDrama), arg0, arg1)
}

// CreateCopiedDrama mocks base method.
func (m *MockPiaServer) CreateCopiedDrama(arg0 context.Context, arg1 *pia.PiaCreateCopiedDramaRequest) (*pia.PiaCreateCopiedDramaResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateCopiedDrama", arg0, arg1)
	ret0, _ := ret[0].(*pia.PiaCreateCopiedDramaResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateCopiedDrama indicates an expected call of CreateCopiedDrama.
func (mr *MockPiaServerMockRecorder) CreateCopiedDrama(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateCopiedDrama", reflect.TypeOf((*MockPiaServer)(nil).CreateCopiedDrama), arg0, arg1)
}

// CreateDramaCopy mocks base method.
func (m *MockPiaServer) CreateDramaCopy(arg0 context.Context, arg1 *pia.PiaCreateDramaCopyReq) (*pia.PiaCreateDramaCopyResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateDramaCopy", arg0, arg1)
	ret0, _ := ret[0].(*pia.PiaCreateDramaCopyResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateDramaCopy indicates an expected call of CreateDramaCopy.
func (mr *MockPiaServerMockRecorder) CreateDramaCopy(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateDramaCopy", reflect.TypeOf((*MockPiaServer)(nil).CreateDramaCopy), arg0, arg1)
}

// CreateTempDrama mocks base method.
func (m *MockPiaServer) CreateTempDrama(arg0 context.Context, arg1 *pia.PiaCreateTempDramaRequest) (*pia.PiaCreateTempDramaResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateTempDrama", arg0, arg1)
	ret0, _ := ret[0].(*pia.PiaCreateTempDramaResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateTempDrama indicates an expected call of CreateTempDrama.
func (mr *MockPiaServerMockRecorder) CreateTempDrama(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateTempDrama", reflect.TypeOf((*MockPiaServer)(nil).CreateTempDrama), arg0, arg1)
}

// DelPiaRoomRecord mocks base method.
func (m *MockPiaServer) DelPiaRoomRecord(arg0 context.Context, arg1 *pia.DelPiaRoomRecordReq) (*pia.EmptyMsg, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelPiaRoomRecord", arg0, arg1)
	ret0, _ := ret[0].(*pia.EmptyMsg)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DelPiaRoomRecord indicates an expected call of DelPiaRoomRecord.
func (mr *MockPiaServerMockRecorder) DelPiaRoomRecord(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelPiaRoomRecord", reflect.TypeOf((*MockPiaServer)(nil).DelPiaRoomRecord), arg0, arg1)
}

// DeleteDramaCopy mocks base method.
func (m *MockPiaServer) DeleteDramaCopy(arg0 context.Context, arg1 *pia.DeleteDramaCopyReq) (*pia.DeleteDramaCopyResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteDramaCopy", arg0, arg1)
	ret0, _ := ret[0].(*pia.DeleteDramaCopyResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteDramaCopy indicates an expected call of DeleteDramaCopy.
func (mr *MockPiaServerMockRecorder) DeleteDramaCopy(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteDramaCopy", reflect.TypeOf((*MockPiaServer)(nil).DeleteDramaCopy), arg0, arg1)
}

// DeleteOrderDrama mocks base method.
func (m *MockPiaServer) DeleteOrderDrama(arg0 context.Context, arg1 *pia.DeleteOrderDramaReq) (*pia.DeleteOrderDramaResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteOrderDrama", arg0, arg1)
	ret0, _ := ret[0].(*pia.DeleteOrderDramaResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteOrderDrama indicates an expected call of DeleteOrderDrama.
func (mr *MockPiaServerMockRecorder) DeleteOrderDrama(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteOrderDrama", reflect.TypeOf((*MockPiaServer)(nil).DeleteOrderDrama), arg0, arg1)
}

// DeleteOrderDramaForAllChannel mocks base method.
func (m *MockPiaServer) DeleteOrderDramaForAllChannel(arg0 context.Context, arg1 *pia.DeleteOrderDramaForAllChannelRequest) (*pia.DeleteOrderDramaForAllChannelResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteOrderDramaForAllChannel", arg0, arg1)
	ret0, _ := ret[0].(*pia.DeleteOrderDramaForAllChannelResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteOrderDramaForAllChannel indicates an expected call of DeleteOrderDramaForAllChannel.
func (mr *MockPiaServerMockRecorder) DeleteOrderDramaForAllChannel(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteOrderDramaForAllChannel", reflect.TypeOf((*MockPiaServer)(nil).DeleteOrderDramaForAllChannel), arg0, arg1)
}

// DeleteStickDrama mocks base method.
func (m *MockPiaServer) DeleteStickDrama(arg0 context.Context, arg1 *pia.DeleteStickDramaReq) (*pia.EmptyMsg, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteStickDrama", arg0, arg1)
	ret0, _ := ret[0].(*pia.EmptyMsg)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteStickDrama indicates an expected call of DeleteStickDrama.
func (mr *MockPiaServerMockRecorder) DeleteStickDrama(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteStickDrama", reflect.TypeOf((*MockPiaServer)(nil).DeleteStickDrama), arg0, arg1)
}

// DoUserDramaCollect mocks base method.
func (m *MockPiaServer) DoUserDramaCollect(arg0 context.Context, arg1 *pia.DoUserDramaCollectReq) (*pia.DoUserDramaCollectResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DoUserDramaCollect", arg0, arg1)
	ret0, _ := ret[0].(*pia.DoUserDramaCollectResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DoUserDramaCollect indicates an expected call of DoUserDramaCollect.
func (mr *MockPiaServerMockRecorder) DoUserDramaCollect(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DoUserDramaCollect", reflect.TypeOf((*MockPiaServer)(nil).DoUserDramaCollect), arg0, arg1)
}

// GetAuthorGenInfo mocks base method.
func (m *MockPiaServer) GetAuthorGenInfo(arg0 context.Context, arg1 *pia.PiaAuthorGenInfoReq) (*pia.PiaAuthorGenInfoResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAuthorGenInfo", arg0, arg1)
	ret0, _ := ret[0].(*pia.PiaAuthorGenInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAuthorGenInfo indicates an expected call of GetAuthorGenInfo.
func (mr *MockPiaServerMockRecorder) GetAuthorGenInfo(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAuthorGenInfo", reflect.TypeOf((*MockPiaServer)(nil).GetAuthorGenInfo), arg0, arg1)
}

// GetAuthorWorksList mocks base method.
func (m *MockPiaServer) GetAuthorWorksList(arg0 context.Context, arg1 *pia.PiaAuthorWorksListReq) (*pia.PiaAuthorWorksListResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAuthorWorksList", arg0, arg1)
	ret0, _ := ret[0].(*pia.PiaAuthorWorksListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAuthorWorksList indicates an expected call of GetAuthorWorksList.
func (mr *MockPiaServerMockRecorder) GetAuthorWorksList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAuthorWorksList", reflect.TypeOf((*MockPiaServer)(nil).GetAuthorWorksList), arg0, arg1)
}

// GetBgmInfo mocks base method.
func (m *MockPiaServer) GetBgmInfo(arg0 context.Context, arg1 *pia.GetBgmInfoReq) (*pia.GetBgmInfoResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBgmInfo", arg0, arg1)
	ret0, _ := ret[0].(*pia.GetBgmInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBgmInfo indicates an expected call of GetBgmInfo.
func (mr *MockPiaServerMockRecorder) GetBgmInfo(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBgmInfo", reflect.TypeOf((*MockPiaServer)(nil).GetBgmInfo), arg0, arg1)
}

// GetChannelDramaInfos mocks base method.
func (m *MockPiaServer) GetChannelDramaInfos(arg0 context.Context, arg1 *pia.GetChannelDramaInfosReq) (*pia.GetChannelDramaInfosResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChannelDramaInfos", arg0, arg1)
	ret0, _ := ret[0].(*pia.GetChannelDramaInfosResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChannelDramaInfos indicates an expected call of GetChannelDramaInfos.
func (mr *MockPiaServerMockRecorder) GetChannelDramaInfos(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChannelDramaInfos", reflect.TypeOf((*MockPiaServer)(nil).GetChannelDramaInfos), arg0, arg1)
}

// GetChannelPiaStatus mocks base method.
func (m *MockPiaServer) GetChannelPiaStatus(arg0 context.Context, arg1 *pia.GetChannelPiaStatusReq) (*pia.GetChannelPiaStatusResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChannelPiaStatus", arg0, arg1)
	ret0, _ := ret[0].(*pia.GetChannelPiaStatusResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChannelPiaStatus indicates an expected call of GetChannelPiaStatus.
func (mr *MockPiaServerMockRecorder) GetChannelPiaStatus(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChannelPiaStatus", reflect.TypeOf((*MockPiaServer)(nil).GetChannelPiaStatus), arg0, arg1)
}

// GetChannelTopConfigInfos mocks base method.
func (m *MockPiaServer) GetChannelTopConfigInfos(arg0 context.Context, arg1 *pia.GetChannelTopConfigInfosReq) (*pia.GetChannelTopConfigInfosResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChannelTopConfigInfos", arg0, arg1)
	ret0, _ := ret[0].(*pia.GetChannelTopConfigInfosResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChannelTopConfigInfos indicates an expected call of GetChannelTopConfigInfos.
func (mr *MockPiaServerMockRecorder) GetChannelTopConfigInfos(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChannelTopConfigInfos", reflect.TypeOf((*MockPiaServer)(nil).GetChannelTopConfigInfos), arg0, arg1)
}

// GetCopiedDramaList mocks base method.
func (m *MockPiaServer) GetCopiedDramaList(arg0 context.Context, arg1 *pia.PiaCopyDramaListRequest) (*pia.PiaCopyDramaListResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCopiedDramaList", arg0, arg1)
	ret0, _ := ret[0].(*pia.PiaCopyDramaListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCopiedDramaList indicates an expected call of GetCopiedDramaList.
func (mr *MockPiaServerMockRecorder) GetCopiedDramaList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCopiedDramaList", reflect.TypeOf((*MockPiaServer)(nil).GetCopiedDramaList), arg0, arg1)
}

// GetCurrentPiaInfo mocks base method.
func (m *MockPiaServer) GetCurrentPiaInfo(arg0 context.Context, arg1 *pia.GetCurrentPiaInfoReq) (*pia.GetCurrentPiaInfoResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCurrentPiaInfo", arg0, arg1)
	ret0, _ := ret[0].(*pia.GetCurrentPiaInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCurrentPiaInfo indicates an expected call of GetCurrentPiaInfo.
func (mr *MockPiaServerMockRecorder) GetCurrentPiaInfo(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCurrentPiaInfo", reflect.TypeOf((*MockPiaServer)(nil).GetCurrentPiaInfo), arg0, arg1)
}

// GetDrama mocks base method.
func (m *MockPiaServer) GetDrama(arg0 context.Context, arg1 *pia.GetDramaReq) (*pia.GetDramaResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetDrama", arg0, arg1)
	ret0, _ := ret[0].(*pia.GetDramaResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetDrama indicates an expected call of GetDrama.
func (mr *MockPiaServerMockRecorder) GetDrama(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDrama", reflect.TypeOf((*MockPiaServer)(nil).GetDrama), arg0, arg1)
}

// GetDramaDetailByID mocks base method.
func (m *MockPiaServer) GetDramaDetailByID(arg0 context.Context, arg1 *pia.GetDramaDetailByIDReq) (*pia.GetDramaDetailByIDResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetDramaDetailByID", arg0, arg1)
	ret0, _ := ret[0].(*pia.GetDramaDetailByIDResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetDramaDetailByID indicates an expected call of GetDramaDetailByID.
func (mr *MockPiaServerMockRecorder) GetDramaDetailByID(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDramaDetailByID", reflect.TypeOf((*MockPiaServer)(nil).GetDramaDetailByID), arg0, arg1)
}

// GetDramaDetailByIdV2 mocks base method.
func (m *MockPiaServer) GetDramaDetailByIdV2(arg0 context.Context, arg1 *pia.GetDramaDetailByIdV2Req) (*pia.GetDramaDetailByIdV2Resp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetDramaDetailByIdV2", arg0, arg1)
	ret0, _ := ret[0].(*pia.GetDramaDetailByIdV2Resp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetDramaDetailByIdV2 indicates an expected call of GetDramaDetailByIdV2.
func (mr *MockPiaServerMockRecorder) GetDramaDetailByIdV2(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDramaDetailByIdV2", reflect.TypeOf((*MockPiaServer)(nil).GetDramaDetailByIdV2), arg0, arg1)
}

// GetDramaExpansion mocks base method.
func (m *MockPiaServer) GetDramaExpansion(arg0 context.Context, arg1 *pia.GetDramaExpansionReq) (*pia.GetDramaExpansionResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetDramaExpansion", arg0, arg1)
	ret0, _ := ret[0].(*pia.GetDramaExpansionResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetDramaExpansion indicates an expected call of GetDramaExpansion.
func (mr *MockPiaServerMockRecorder) GetDramaExpansion(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDramaExpansion", reflect.TypeOf((*MockPiaServer)(nil).GetDramaExpansion), arg0, arg1)
}

// GetDramaInfoByDramaId mocks base method.
func (m *MockPiaServer) GetDramaInfoByDramaId(arg0 context.Context, arg1 *pia.GetDramaInfoByDramaIdReq) (*pia.GetDramaInfoByDramaIdResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetDramaInfoByDramaId", arg0, arg1)
	ret0, _ := ret[0].(*pia.GetDramaInfoByDramaIdResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetDramaInfoByDramaId indicates an expected call of GetDramaInfoByDramaId.
func (mr *MockPiaServerMockRecorder) GetDramaInfoByDramaId(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDramaInfoByDramaId", reflect.TypeOf((*MockPiaServer)(nil).GetDramaInfoByDramaId), arg0, arg1)
}

// GetDramaList mocks base method.
func (m *MockPiaServer) GetDramaList(arg0 context.Context, arg1 *pia.GetDramaListReq) (*pia.GetDramaListResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetDramaList", arg0, arg1)
	ret0, _ := ret[0].(*pia.GetDramaListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetDramaList indicates an expected call of GetDramaList.
func (mr *MockPiaServerMockRecorder) GetDramaList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDramaList", reflect.TypeOf((*MockPiaServer)(nil).GetDramaList), arg0, arg1)
}

// GetDramaListByIds mocks base method.
func (m *MockPiaServer) GetDramaListByIds(arg0 context.Context, arg1 *pia.GetDramaListByIdsReq) (*pia.GetDramaListByIdsResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetDramaListByIds", arg0, arg1)
	ret0, _ := ret[0].(*pia.GetDramaListByIdsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetDramaListByIds indicates an expected call of GetDramaListByIds.
func (mr *MockPiaServerMockRecorder) GetDramaListByIds(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDramaListByIds", reflect.TypeOf((*MockPiaServer)(nil).GetDramaListByIds), arg0, arg1)
}

// GetMyDramaCopyList mocks base method.
func (m *MockPiaServer) GetMyDramaCopyList(arg0 context.Context, arg1 *pia.GetMyDramaCopyListReq) (*pia.GetMyDramaCopyListResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMyDramaCopyList", arg0, arg1)
	ret0, _ := ret[0].(*pia.GetMyDramaCopyListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMyDramaCopyList indicates an expected call of GetMyDramaCopyList.
func (mr *MockPiaServerMockRecorder) GetMyDramaCopyList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMyDramaCopyList", reflect.TypeOf((*MockPiaServer)(nil).GetMyDramaCopyList), arg0, arg1)
}

// GetMyDramaPlayingRecord mocks base method.
func (m *MockPiaServer) GetMyDramaPlayingRecord(arg0 context.Context, arg1 *pia.GetMyDramaPlayingRecordReq) (*pia.GetMyDramaPlayingRecordResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMyDramaPlayingRecord", arg0, arg1)
	ret0, _ := ret[0].(*pia.GetMyDramaPlayingRecordResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMyDramaPlayingRecord indicates an expected call of GetMyDramaPlayingRecord.
func (mr *MockPiaServerMockRecorder) GetMyDramaPlayingRecord(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMyDramaPlayingRecord", reflect.TypeOf((*MockPiaServer)(nil).GetMyDramaPlayingRecord), arg0, arg1)
}

// GetOrderDramaList mocks base method.
func (m *MockPiaServer) GetOrderDramaList(arg0 context.Context, arg1 *pia.GetOrderDramaListReq) (*pia.GetOrderDramaListResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOrderDramaList", arg0, arg1)
	ret0, _ := ret[0].(*pia.GetOrderDramaListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOrderDramaList indicates an expected call of GetOrderDramaList.
func (mr *MockPiaServerMockRecorder) GetOrderDramaList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOrderDramaList", reflect.TypeOf((*MockPiaServer)(nil).GetOrderDramaList), arg0, arg1)
}

// GetPiaRoomPermission mocks base method.
func (m *MockPiaServer) GetPiaRoomPermission(arg0 context.Context, arg1 *pia.GetPiaRoomPermissionReq) (*pia.GetPiaRoomPermissionResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPiaRoomPermission", arg0, arg1)
	ret0, _ := ret[0].(*pia.GetPiaRoomPermissionResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPiaRoomPermission indicates an expected call of GetPiaRoomPermission.
func (mr *MockPiaServerMockRecorder) GetPiaRoomPermission(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPiaRoomPermission", reflect.TypeOf((*MockPiaServer)(nil).GetPiaRoomPermission), arg0, arg1)
}

// GetPlayingChannel mocks base method.
func (m *MockPiaServer) GetPlayingChannel(arg0 context.Context, arg1 *pia.GetPlayingChannelReq) (*pia.GetPlayingChannelResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPlayingChannel", arg0, arg1)
	ret0, _ := ret[0].(*pia.GetPlayingChannelResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPlayingChannel indicates an expected call of GetPlayingChannel.
func (mr *MockPiaServerMockRecorder) GetPlayingChannel(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPlayingChannel", reflect.TypeOf((*MockPiaServer)(nil).GetPlayingChannel), arg0, arg1)
}

// GetPlayingChannelV2 mocks base method.
func (m *MockPiaServer) GetPlayingChannelV2(arg0 context.Context, arg1 *pia.GetPlayingChannelV2Req) (*pia.GetPlayingChannelV2Resp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPlayingChannelV2", arg0, arg1)
	ret0, _ := ret[0].(*pia.GetPlayingChannelV2Resp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPlayingChannelV2 indicates an expected call of GetPlayingChannelV2.
func (mr *MockPiaServerMockRecorder) GetPlayingChannelV2(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPlayingChannelV2", reflect.TypeOf((*MockPiaServer)(nil).GetPlayingChannelV2), arg0, arg1)
}

// GetSearchOptionGroup mocks base method.
func (m *MockPiaServer) GetSearchOptionGroup(arg0 context.Context, arg1 *pia.GetSearchOptionGroupReq) (*pia.GetSearchOptionGroupResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSearchOptionGroup", arg0, arg1)
	ret0, _ := ret[0].(*pia.GetSearchOptionGroupResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSearchOptionGroup indicates an expected call of GetSearchOptionGroup.
func (mr *MockPiaServerMockRecorder) GetSearchOptionGroup(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSearchOptionGroup", reflect.TypeOf((*MockPiaServer)(nil).GetSearchOptionGroup), arg0, arg1)
}

// GetSearchOptionGroupV2 mocks base method.
func (m *MockPiaServer) GetSearchOptionGroupV2(arg0 context.Context, arg1 *pia.GetSearchOptionGroupV2Req) (*pia.GetSearchOptionGroupV2Resp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSearchOptionGroupV2", arg0, arg1)
	ret0, _ := ret[0].(*pia.GetSearchOptionGroupV2Resp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSearchOptionGroupV2 indicates an expected call of GetSearchOptionGroupV2.
func (mr *MockPiaServerMockRecorder) GetSearchOptionGroupV2(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSearchOptionGroupV2", reflect.TypeOf((*MockPiaServer)(nil).GetSearchOptionGroupV2), arg0, arg1)
}

// GetStickDramaTags mocks base method.
func (m *MockPiaServer) GetStickDramaTags(arg0 context.Context, arg1 *pia.GetStickDramaTagsReq) (*pia.GetStickDramaTagsResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetStickDramaTags", arg0, arg1)
	ret0, _ := ret[0].(*pia.GetStickDramaTagsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetStickDramaTags indicates an expected call of GetStickDramaTags.
func (mr *MockPiaServerMockRecorder) GetStickDramaTags(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetStickDramaTags", reflect.TypeOf((*MockPiaServer)(nil).GetStickDramaTags), arg0, arg1)
}

// GetStickPiaRoom mocks base method.
func (m *MockPiaServer) GetStickPiaRoom(arg0 context.Context, arg1 *pia.GetStickPiaRoomReq) (*pia.GetStickPiaRoomResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetStickPiaRoom", arg0, arg1)
	ret0, _ := ret[0].(*pia.GetStickPiaRoomResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetStickPiaRoom indicates an expected call of GetStickPiaRoom.
func (mr *MockPiaServerMockRecorder) GetStickPiaRoom(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetStickPiaRoom", reflect.TypeOf((*MockPiaServer)(nil).GetStickPiaRoom), arg0, arg1)
}

// GetTempDrama mocks base method.
func (m *MockPiaServer) GetTempDrama(arg0 context.Context, arg1 *pia.PiaGetTempDramaRequest) (*pia.PiaGetTempDramaResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTempDrama", arg0, arg1)
	ret0, _ := ret[0].(*pia.PiaGetTempDramaResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTempDrama indicates an expected call of GetTempDrama.
func (mr *MockPiaServerMockRecorder) GetTempDrama(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTempDrama", reflect.TypeOf((*MockPiaServer)(nil).GetTempDrama), arg0, arg1)
}

// GetTimeOverlapped mocks base method.
func (m *MockPiaServer) GetTimeOverlapped(arg0 context.Context, arg1 *pia.GetTimeOverlappedReq) (*pia.EmptyMsg, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTimeOverlapped", arg0, arg1)
	ret0, _ := ret[0].(*pia.EmptyMsg)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTimeOverlapped indicates an expected call of GetTimeOverlapped.
func (mr *MockPiaServerMockRecorder) GetTimeOverlapped(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTimeOverlapped", reflect.TypeOf((*MockPiaServer)(nil).GetTimeOverlapped), arg0, arg1)
}

// GetUgcChannels mocks base method.
func (m *MockPiaServer) GetUgcChannels(arg0 context.Context, arg1 *pia.GetUgcChannelsReq) (*pia.GetUgcChannelsResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUgcChannels", arg0, arg1)
	ret0, _ := ret[0].(*pia.GetUgcChannelsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUgcChannels indicates an expected call of GetUgcChannels.
func (mr *MockPiaServerMockRecorder) GetUgcChannels(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUgcChannels", reflect.TypeOf((*MockPiaServer)(nil).GetUgcChannels), arg0, arg1)
}

// GetUgcChannelsV2 mocks base method.
func (m *MockPiaServer) GetUgcChannelsV2(arg0 context.Context, arg1 *pia.GetUgcChannelsReqV2) (*pia.GetUgcChannelsRespV2, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUgcChannelsV2", arg0, arg1)
	ret0, _ := ret[0].(*pia.GetUgcChannelsRespV2)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUgcChannelsV2 indicates an expected call of GetUgcChannelsV2.
func (mr *MockPiaServerMockRecorder) GetUgcChannelsV2(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUgcChannelsV2", reflect.TypeOf((*MockPiaServer)(nil).GetUgcChannelsV2), arg0, arg1)
}

// GetUserDramaCollList mocks base method.
func (m *MockPiaServer) GetUserDramaCollList(arg0 context.Context, arg1 *pia.GetUserDramaCollListReq) (*pia.GetUserDramaCollListResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserDramaCollList", arg0, arg1)
	ret0, _ := ret[0].(*pia.GetUserDramaCollListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserDramaCollList indicates an expected call of GetUserDramaCollList.
func (mr *MockPiaServerMockRecorder) GetUserDramaCollList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserDramaCollList", reflect.TypeOf((*MockPiaServer)(nil).GetUserDramaCollList), arg0, arg1)
}

// GetUserDramaStatus mocks base method.
func (m *MockPiaServer) GetUserDramaStatus(arg0 context.Context, arg1 *pia.GetUserDramaStatusReq) (*pia.GetUserDramaStatusResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserDramaStatus", arg0, arg1)
	ret0, _ := ret[0].(*pia.GetUserDramaStatusResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserDramaStatus indicates an expected call of GetUserDramaStatus.
func (mr *MockPiaServerMockRecorder) GetUserDramaStatus(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserDramaStatus", reflect.TypeOf((*MockPiaServer)(nil).GetUserDramaStatus), arg0, arg1)
}

// GrantPiaRoomPermission mocks base method.
func (m *MockPiaServer) GrantPiaRoomPermission(arg0 context.Context, arg1 *pia.GrantPiaRoomPermissionReq) (*pia.EmptyMsg, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GrantPiaRoomPermission", arg0, arg1)
	ret0, _ := ret[0].(*pia.EmptyMsg)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GrantPiaRoomPermission indicates an expected call of GrantPiaRoomPermission.
func (mr *MockPiaServerMockRecorder) GrantPiaRoomPermission(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GrantPiaRoomPermission", reflect.TypeOf((*MockPiaServer)(nil).GrantPiaRoomPermission), arg0, arg1)
}

// HandleDramaReportResult mocks base method.
func (m *MockPiaServer) HandleDramaReportResult(arg0 context.Context, arg1 *pia.PiaHandleDramaReportResultReq) (*pia.PiaHandleDramaReportResultResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "HandleDramaReportResult", arg0, arg1)
	ret0, _ := ret[0].(*pia.PiaHandleDramaReportResultResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// HandleDramaReportResult indicates an expected call of HandleDramaReportResult.
func (mr *MockPiaServerMockRecorder) HandleDramaReportResult(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HandleDramaReportResult", reflect.TypeOf((*MockPiaServer)(nil).HandleDramaReportResult), arg0, arg1)
}

// InternalStopChannel mocks base method.
func (m *MockPiaServer) InternalStopChannel(arg0 context.Context, arg1 *pia.InternalStopChannelReq) (*pia.InternalStopChannelResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InternalStopChannel", arg0, arg1)
	ret0, _ := ret[0].(*pia.InternalStopChannelResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// InternalStopChannel indicates an expected call of InternalStopChannel.
func (mr *MockPiaServerMockRecorder) InternalStopChannel(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InternalStopChannel", reflect.TypeOf((*MockPiaServer)(nil).InternalStopChannel), arg0, arg1)
}

// OrderDrama mocks base method.
func (m *MockPiaServer) OrderDrama(arg0 context.Context, arg1 *pia.OrderDramaReq) (*pia.OrderDramaResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "OrderDrama", arg0, arg1)
	ret0, _ := ret[0].(*pia.OrderDramaResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// OrderDrama indicates an expected call of OrderDrama.
func (mr *MockPiaServerMockRecorder) OrderDrama(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "OrderDrama", reflect.TypeOf((*MockPiaServer)(nil).OrderDrama), arg0, arg1)
}

// PauseBgm mocks base method.
func (m *MockPiaServer) PauseBgm(arg0 context.Context, arg1 *pia.PiaOperateBgmReq) (*pia.PiaOperateBgmResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PauseBgm", arg0, arg1)
	ret0, _ := ret[0].(*pia.PiaOperateBgmResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PauseBgm indicates an expected call of PauseBgm.
func (mr *MockPiaServerMockRecorder) PauseBgm(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PauseBgm", reflect.TypeOf((*MockPiaServer)(nil).PauseBgm), arg0, arg1)
}

// PauseDrama mocks base method.
func (m *MockPiaServer) PauseDrama(arg0 context.Context, arg1 *pia.PiaOperateDramaReq) (*pia.PiaOperateDramaResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PauseDrama", arg0, arg1)
	ret0, _ := ret[0].(*pia.PiaOperateDramaResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PauseDrama indicates an expected call of PauseDrama.
func (mr *MockPiaServerMockRecorder) PauseDrama(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PauseDrama", reflect.TypeOf((*MockPiaServer)(nil).PauseDrama), arg0, arg1)
}

// PiaAddDramaFeedBack mocks base method.
func (m *MockPiaServer) PiaAddDramaFeedBack(arg0 context.Context, arg1 *pia.PiaAddDramaFeedBackReq) (*pia.PiaAddDramaFeedBackResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PiaAddDramaFeedBack", arg0, arg1)
	ret0, _ := ret[0].(*pia.PiaAddDramaFeedBackResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PiaAddDramaFeedBack indicates an expected call of PiaAddDramaFeedBack.
func (mr *MockPiaServerMockRecorder) PiaAddDramaFeedBack(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PiaAddDramaFeedBack", reflect.TypeOf((*MockPiaServer)(nil).PiaAddDramaFeedBack), arg0, arg1)
}

// PiaBatchDeleteMyPlayingRecord mocks base method.
func (m *MockPiaServer) PiaBatchDeleteMyPlayingRecord(arg0 context.Context, arg1 *pia.PiaBatchDeleteMyPlayingRecordReq) (*pia.PiaBatchDeleteMyPlayingRecordResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PiaBatchDeleteMyPlayingRecord", arg0, arg1)
	ret0, _ := ret[0].(*pia.PiaBatchDeleteMyPlayingRecordResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PiaBatchDeleteMyPlayingRecord indicates an expected call of PiaBatchDeleteMyPlayingRecord.
func (mr *MockPiaServerMockRecorder) PiaBatchDeleteMyPlayingRecord(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PiaBatchDeleteMyPlayingRecord", reflect.TypeOf((*MockPiaServer)(nil).PiaBatchDeleteMyPlayingRecord), arg0, arg1)
}

// PiaBatchGetAuthorBaseInfo mocks base method.
func (m *MockPiaServer) PiaBatchGetAuthorBaseInfo(arg0 context.Context, arg1 *pia.PiaBatchGetAuthorBaseInfoReq) (*pia.PiaBatchGetAuthorBaseInfoResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PiaBatchGetAuthorBaseInfo", arg0, arg1)
	ret0, _ := ret[0].(*pia.PiaBatchGetAuthorBaseInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PiaBatchGetAuthorBaseInfo indicates an expected call of PiaBatchGetAuthorBaseInfo.
func (mr *MockPiaServerMockRecorder) PiaBatchGetAuthorBaseInfo(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PiaBatchGetAuthorBaseInfo", reflect.TypeOf((*MockPiaServer)(nil).PiaBatchGetAuthorBaseInfo), arg0, arg1)
}

// PiaCancelSelectRole mocks base method.
func (m *MockPiaServer) PiaCancelSelectRole(arg0 context.Context, arg1 *pia.PiaCancelSelectRoleReq) (*pia.PiaCancelSelectRoleResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PiaCancelSelectRole", arg0, arg1)
	ret0, _ := ret[0].(*pia.PiaCancelSelectRoleResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PiaCancelSelectRole indicates an expected call of PiaCancelSelectRole.
func (mr *MockPiaServerMockRecorder) PiaCancelSelectRole(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PiaCancelSelectRole", reflect.TypeOf((*MockPiaServer)(nil).PiaCancelSelectRole), arg0, arg1)
}

// PiaCancelSelectRoleV2 mocks base method.
func (m *MockPiaServer) PiaCancelSelectRoleV2(arg0 context.Context, arg1 *pia.PiaCancelSelectRoleReq) (*pia.PiaCancelSelectRoleResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PiaCancelSelectRoleV2", arg0, arg1)
	ret0, _ := ret[0].(*pia.PiaCancelSelectRoleResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PiaCancelSelectRoleV2 indicates an expected call of PiaCancelSelectRoleV2.
func (mr *MockPiaServerMockRecorder) PiaCancelSelectRoleV2(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PiaCancelSelectRoleV2", reflect.TypeOf((*MockPiaServer)(nil).PiaCancelSelectRoleV2), arg0, arg1)
}

// PiaChangePlayType mocks base method.
func (m *MockPiaServer) PiaChangePlayType(arg0 context.Context, arg1 *pia.PiaChangePlayTypeReq) (*pia.PiaChangePlayTypeResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PiaChangePlayType", arg0, arg1)
	ret0, _ := ret[0].(*pia.PiaChangePlayTypeResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PiaChangePlayType indicates an expected call of PiaChangePlayType.
func (mr *MockPiaServerMockRecorder) PiaChangePlayType(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PiaChangePlayType", reflect.TypeOf((*MockPiaServer)(nil).PiaChangePlayType), arg0, arg1)
}

// PiaChangePlayTypeV2 mocks base method.
func (m *MockPiaServer) PiaChangePlayTypeV2(arg0 context.Context, arg1 *pia.PiaChangePlayTypeV2Request) (*pia.PiaChangePlayTypeV2Response, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PiaChangePlayTypeV2", arg0, arg1)
	ret0, _ := ret[0].(*pia.PiaChangePlayTypeV2Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PiaChangePlayTypeV2 indicates an expected call of PiaChangePlayTypeV2.
func (mr *MockPiaServerMockRecorder) PiaChangePlayTypeV2(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PiaChangePlayTypeV2", reflect.TypeOf((*MockPiaServer)(nil).PiaChangePlayTypeV2), arg0, arg1)
}

// PiaFollowMic mocks base method.
func (m *MockPiaServer) PiaFollowMic(arg0 context.Context, arg1 *pia.PiaFollowMicRequest) (*pia.PiaFollowMicResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PiaFollowMic", arg0, arg1)
	ret0, _ := ret[0].(*pia.PiaFollowMicResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PiaFollowMic indicates an expected call of PiaFollowMic.
func (mr *MockPiaServerMockRecorder) PiaFollowMic(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PiaFollowMic", reflect.TypeOf((*MockPiaServer)(nil).PiaFollowMic), arg0, arg1)
}

// PiaGetDramaCopyId mocks base method.
func (m *MockPiaServer) PiaGetDramaCopyId(arg0 context.Context, arg1 *pia.PiaGetDramaCopyIdReq) (*pia.PiaGetDramaCopyIdResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PiaGetDramaCopyId", arg0, arg1)
	ret0, _ := ret[0].(*pia.PiaGetDramaCopyIdResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PiaGetDramaCopyId indicates an expected call of PiaGetDramaCopyId.
func (mr *MockPiaServerMockRecorder) PiaGetDramaCopyId(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PiaGetDramaCopyId", reflect.TypeOf((*MockPiaServer)(nil).PiaGetDramaCopyId), arg0, arg1)
}

// PiaGetDramaStatus mocks base method.
func (m *MockPiaServer) PiaGetDramaStatus(arg0 context.Context, arg1 *pia.PiaGetDramaStatusReq) (*pia.PiaGetDramaStatusResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PiaGetDramaStatus", arg0, arg1)
	ret0, _ := ret[0].(*pia.PiaGetDramaStatusResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PiaGetDramaStatus indicates an expected call of PiaGetDramaStatus.
func (mr *MockPiaServerMockRecorder) PiaGetDramaStatus(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PiaGetDramaStatus", reflect.TypeOf((*MockPiaServer)(nil).PiaGetDramaStatus), arg0, arg1)
}

// PiaGetDramaStatusV2 mocks base method.
func (m *MockPiaServer) PiaGetDramaStatusV2(arg0 context.Context, arg1 *pia.PiaGetDramaStatusReq) (*pia.PiaGetDramaStatusResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PiaGetDramaStatusV2", arg0, arg1)
	ret0, _ := ret[0].(*pia.PiaGetDramaStatusResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PiaGetDramaStatusV2 indicates an expected call of PiaGetDramaStatusV2.
func (mr *MockPiaServerMockRecorder) PiaGetDramaStatusV2(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PiaGetDramaStatusV2", reflect.TypeOf((*MockPiaServer)(nil).PiaGetDramaStatusV2), arg0, arg1)
}

// PiaGetFollowedStatusOfMicList mocks base method.
func (m *MockPiaServer) PiaGetFollowedStatusOfMicList(arg0 context.Context, arg1 *pia.PiaGetFollowedStatusOfMicListRequest) (*pia.PiaGetFollowedStatusOfMicListResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PiaGetFollowedStatusOfMicList", arg0, arg1)
	ret0, _ := ret[0].(*pia.PiaGetFollowedStatusOfMicListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PiaGetFollowedStatusOfMicList indicates an expected call of PiaGetFollowedStatusOfMicList.
func (mr *MockPiaServerMockRecorder) PiaGetFollowedStatusOfMicList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PiaGetFollowedStatusOfMicList", reflect.TypeOf((*MockPiaServer)(nil).PiaGetFollowedStatusOfMicList), arg0, arg1)
}

// PiaGetMyFollowInfo mocks base method.
func (m *MockPiaServer) PiaGetMyFollowInfo(arg0 context.Context, arg1 *pia.PiaGetMyFollowInfoRequest) (*pia.PiaGetMyFollowInfoResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PiaGetMyFollowInfo", arg0, arg1)
	ret0, _ := ret[0].(*pia.PiaGetMyFollowInfoResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PiaGetMyFollowInfo indicates an expected call of PiaGetMyFollowInfo.
func (mr *MockPiaServerMockRecorder) PiaGetMyFollowInfo(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PiaGetMyFollowInfo", reflect.TypeOf((*MockPiaServer)(nil).PiaGetMyFollowInfo), arg0, arg1)
}

// PiaGetMyPlayingRecordIdList mocks base method.
func (m *MockPiaServer) PiaGetMyPlayingRecordIdList(arg0 context.Context, arg1 *pia.PiaGetMyPlayingRecordIdListReq) (*pia.PiaGetMyPlayingRecordIdListResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PiaGetMyPlayingRecordIdList", arg0, arg1)
	ret0, _ := ret[0].(*pia.PiaGetMyPlayingRecordIdListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PiaGetMyPlayingRecordIdList indicates an expected call of PiaGetMyPlayingRecordIdList.
func (mr *MockPiaServerMockRecorder) PiaGetMyPlayingRecordIdList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PiaGetMyPlayingRecordIdList", reflect.TypeOf((*MockPiaServer)(nil).PiaGetMyPlayingRecordIdList), arg0, arg1)
}

// PiaGetPreviousDialogueIndex mocks base method.
func (m *MockPiaServer) PiaGetPreviousDialogueIndex(arg0 context.Context, arg1 *pia.PiaGetPreviousDialogueIndexRequest) (*pia.PiaGetPreviousDialogueIndexResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PiaGetPreviousDialogueIndex", arg0, arg1)
	ret0, _ := ret[0].(*pia.PiaGetPreviousDialogueIndexResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PiaGetPreviousDialogueIndex indicates an expected call of PiaGetPreviousDialogueIndex.
func (mr *MockPiaServerMockRecorder) PiaGetPreviousDialogueIndex(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PiaGetPreviousDialogueIndex", reflect.TypeOf((*MockPiaServer)(nil).PiaGetPreviousDialogueIndex), arg0, arg1)
}

// PiaGetRankingList mocks base method.
func (m *MockPiaServer) PiaGetRankingList(arg0 context.Context, arg1 *pia.PiaGetRankingListReq) (*pia.PiaGetRankingListResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PiaGetRankingList", arg0, arg1)
	ret0, _ := ret[0].(*pia.PiaGetRankingListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PiaGetRankingList indicates an expected call of PiaGetRankingList.
func (mr *MockPiaServerMockRecorder) PiaGetRankingList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PiaGetRankingList", reflect.TypeOf((*MockPiaServer)(nil).PiaGetRankingList), arg0, arg1)
}

// PiaOperateBgm mocks base method.
func (m *MockPiaServer) PiaOperateBgm(arg0 context.Context, arg1 *pia.PiaOperateBgmReq) (*pia.PiaOperateBgmResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PiaOperateBgm", arg0, arg1)
	ret0, _ := ret[0].(*pia.PiaOperateBgmResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PiaOperateBgm indicates an expected call of PiaOperateBgm.
func (mr *MockPiaServerMockRecorder) PiaOperateBgm(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PiaOperateBgm", reflect.TypeOf((*MockPiaServer)(nil).PiaOperateBgm), arg0, arg1)
}

// PiaOperateBgmVol mocks base method.
func (m *MockPiaServer) PiaOperateBgmVol(arg0 context.Context, arg1 *pia.PiaOperateBgmVolReq) (*pia.PiaOperateBgmVolResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PiaOperateBgmVol", arg0, arg1)
	ret0, _ := ret[0].(*pia.PiaOperateBgmVolResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PiaOperateBgmVol indicates an expected call of PiaOperateBgmVol.
func (mr *MockPiaServerMockRecorder) PiaOperateBgmVol(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PiaOperateBgmVol", reflect.TypeOf((*MockPiaServer)(nil).PiaOperateBgmVol), arg0, arg1)
}

// PiaOperateDrama mocks base method.
func (m *MockPiaServer) PiaOperateDrama(arg0 context.Context, arg1 *pia.PiaOperateDramaReq) (*pia.PiaOperateDramaResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PiaOperateDrama", arg0, arg1)
	ret0, _ := ret[0].(*pia.PiaOperateDramaResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PiaOperateDrama indicates an expected call of PiaOperateDrama.
func (mr *MockPiaServerMockRecorder) PiaOperateDrama(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PiaOperateDrama", reflect.TypeOf((*MockPiaServer)(nil).PiaOperateDrama), arg0, arg1)
}

// PiaRebuildDramaCollectedCache mocks base method.
func (m *MockPiaServer) PiaRebuildDramaCollectedCache(arg0 context.Context, arg1 *pia.PiaRebuildDramaCollectedCacheReq) (*pia.PiaRebuildDramaCollectedCacheResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PiaRebuildDramaCollectedCache", arg0, arg1)
	ret0, _ := ret[0].(*pia.PiaRebuildDramaCollectedCacheResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PiaRebuildDramaCollectedCache indicates an expected call of PiaRebuildDramaCollectedCache.
func (mr *MockPiaServerMockRecorder) PiaRebuildDramaCollectedCache(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PiaRebuildDramaCollectedCache", reflect.TypeOf((*MockPiaServer)(nil).PiaRebuildDramaCollectedCache), arg0, arg1)
}

// PiaReportDialogueIndex mocks base method.
func (m *MockPiaServer) PiaReportDialogueIndex(arg0 context.Context, arg1 *pia.PiaReportDialogueIndexRequest) (*pia.PiaReportDialogueIndexResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PiaReportDialogueIndex", arg0, arg1)
	ret0, _ := ret[0].(*pia.PiaReportDialogueIndexResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PiaReportDialogueIndex indicates an expected call of PiaReportDialogueIndex.
func (mr *MockPiaServerMockRecorder) PiaReportDialogueIndex(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PiaReportDialogueIndex", reflect.TypeOf((*MockPiaServer)(nil).PiaReportDialogueIndex), arg0, arg1)
}

// PiaSelectRole mocks base method.
func (m *MockPiaServer) PiaSelectRole(arg0 context.Context, arg1 *pia.PiaSelectRoleReq) (*pia.PiaSelectRoleResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PiaSelectRole", arg0, arg1)
	ret0, _ := ret[0].(*pia.PiaSelectRoleResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PiaSelectRole indicates an expected call of PiaSelectRole.
func (mr *MockPiaServerMockRecorder) PiaSelectRole(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PiaSelectRole", reflect.TypeOf((*MockPiaServer)(nil).PiaSelectRole), arg0, arg1)
}

// PiaSelectRoleV2 mocks base method.
func (m *MockPiaServer) PiaSelectRoleV2(arg0 context.Context, arg1 *pia.PiaSelectRoleReq) (*pia.PiaSelectRoleResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PiaSelectRoleV2", arg0, arg1)
	ret0, _ := ret[0].(*pia.PiaSelectRoleResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PiaSelectRoleV2 indicates an expected call of PiaSelectRoleV2.
func (mr *MockPiaServerMockRecorder) PiaSelectRoleV2(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PiaSelectRoleV2", reflect.TypeOf((*MockPiaServer)(nil).PiaSelectRoleV2), arg0, arg1)
}

// PiaSendDialogueIndex mocks base method.
func (m *MockPiaServer) PiaSendDialogueIndex(arg0 context.Context, arg1 *pia.PiaSendDialogueIndexRequest) (*pia.PiaSendDialogueIndexResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PiaSendDialogueIndex", arg0, arg1)
	ret0, _ := ret[0].(*pia.PiaSendDialogueIndexResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PiaSendDialogueIndex indicates an expected call of PiaSendDialogueIndex.
func (mr *MockPiaServerMockRecorder) PiaSendDialogueIndex(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PiaSendDialogueIndex", reflect.TypeOf((*MockPiaServer)(nil).PiaSendDialogueIndex), arg0, arg1)
}

// PiaUnFollowMic mocks base method.
func (m *MockPiaServer) PiaUnFollowMic(arg0 context.Context, arg1 *pia.PiaUnFollowMicRequest) (*pia.PiaUnFollowMicResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PiaUnFollowMic", arg0, arg1)
	ret0, _ := ret[0].(*pia.PiaUnFollowMicResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PiaUnFollowMic indicates an expected call of PiaUnFollowMic.
func (mr *MockPiaServerMockRecorder) PiaUnFollowMic(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PiaUnFollowMic", reflect.TypeOf((*MockPiaServer)(nil).PiaUnFollowMic), arg0, arg1)
}

// PlayBgm mocks base method.
func (m *MockPiaServer) PlayBgm(arg0 context.Context, arg1 *pia.PiaOperateBgmReq) (*pia.PiaOperateBgmResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PlayBgm", arg0, arg1)
	ret0, _ := ret[0].(*pia.PiaOperateBgmResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PlayBgm indicates an expected call of PlayBgm.
func (mr *MockPiaServerMockRecorder) PlayBgm(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PlayBgm", reflect.TypeOf((*MockPiaServer)(nil).PlayBgm), arg0, arg1)
}

// PlayDrama mocks base method.
func (m *MockPiaServer) PlayDrama(arg0 context.Context, arg1 *pia.PiaOperateDramaReq) (*pia.PiaOperateDramaResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PlayDrama", arg0, arg1)
	ret0, _ := ret[0].(*pia.PiaOperateDramaResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PlayDrama indicates an expected call of PlayDrama.
func (mr *MockPiaServerMockRecorder) PlayDrama(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PlayDrama", reflect.TypeOf((*MockPiaServer)(nil).PlayDrama), arg0, arg1)
}

// PreformDrama mocks base method.
func (m *MockPiaServer) PreformDrama(arg0 context.Context, arg1 *pia.PerformDramaRequest) (*pia.PerformDramaResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PreformDrama", arg0, arg1)
	ret0, _ := ret[0].(*pia.PerformDramaResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PreformDrama indicates an expected call of PreformDrama.
func (mr *MockPiaServerMockRecorder) PreformDrama(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PreformDrama", reflect.TypeOf((*MockPiaServer)(nil).PreformDrama), arg0, arg1)
}

// RebuildAuthorWorksCache mocks base method.
func (m *MockPiaServer) RebuildAuthorWorksCache(arg0 context.Context, arg1 *pia.PiaAuthorGenInfoRebuildReq) (*pia.PiaAuthorGenInfoRebuildResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RebuildAuthorWorksCache", arg0, arg1)
	ret0, _ := ret[0].(*pia.PiaAuthorGenInfoRebuildResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RebuildAuthorWorksCache indicates an expected call of RebuildAuthorWorksCache.
func (mr *MockPiaServerMockRecorder) RebuildAuthorWorksCache(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RebuildAuthorWorksCache", reflect.TypeOf((*MockPiaServer)(nil).RebuildAuthorWorksCache), arg0, arg1)
}

// SearchStickDrama mocks base method.
func (m *MockPiaServer) SearchStickDrama(arg0 context.Context, arg1 *pia.SearchStickDramaReq) (*pia.SearchStickDramaResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SearchStickDrama", arg0, arg1)
	ret0, _ := ret[0].(*pia.SearchStickDramaResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SearchStickDrama indicates an expected call of SearchStickDrama.
func (mr *MockPiaServerMockRecorder) SearchStickDrama(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SearchStickDrama", reflect.TypeOf((*MockPiaServer)(nil).SearchStickDrama), arg0, arg1)
}

// SelectDrama mocks base method.
func (m *MockPiaServer) SelectDrama(arg0 context.Context, arg1 *pia.SelectDramaReq) (*pia.SelectDramaResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SelectDrama", arg0, arg1)
	ret0, _ := ret[0].(*pia.SelectDramaResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SelectDrama indicates an expected call of SelectDrama.
func (mr *MockPiaServerMockRecorder) SelectDrama(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SelectDrama", reflect.TypeOf((*MockPiaServer)(nil).SelectDrama), arg0, arg1)
}

// SelectDramaV2 mocks base method.
func (m *MockPiaServer) SelectDramaV2(arg0 context.Context, arg1 *pia.SelectDramaV2Req) (*pia.SelectDramaV2Resp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SelectDramaV2", arg0, arg1)
	ret0, _ := ret[0].(*pia.SelectDramaV2Resp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SelectDramaV2 indicates an expected call of SelectDramaV2.
func (mr *MockPiaServerMockRecorder) SelectDramaV2(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SelectDramaV2", reflect.TypeOf((*MockPiaServer)(nil).SelectDramaV2), arg0, arg1)
}

// SetBgmInfo mocks base method.
func (m *MockPiaServer) SetBgmInfo(arg0 context.Context, arg1 *pia.SetBgmInfoReq) (*pia.SetBgmInfoResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetBgmInfo", arg0, arg1)
	ret0, _ := ret[0].(*pia.SetBgmInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetBgmInfo indicates an expected call of SetBgmInfo.
func (mr *MockPiaServerMockRecorder) SetBgmInfo(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetBgmInfo", reflect.TypeOf((*MockPiaServer)(nil).SetBgmInfo), arg0, arg1)
}

// SetBgmVol mocks base method.
func (m *MockPiaServer) SetBgmVol(arg0 context.Context, arg1 *pia.PiaOperateBgmVolReq) (*pia.PiaOperateBgmVolResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetBgmVol", arg0, arg1)
	ret0, _ := ret[0].(*pia.PiaOperateBgmVolResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetBgmVol indicates an expected call of SetBgmVol.
func (mr *MockPiaServerMockRecorder) SetBgmVol(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetBgmVol", reflect.TypeOf((*MockPiaServer)(nil).SetBgmVol), arg0, arg1)
}

// SetCompereMic mocks base method.
func (m *MockPiaServer) SetCompereMic(arg0 context.Context, arg1 *pia.SetCompereMicReq) (*pia.SetCompereMicResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetCompereMic", arg0, arg1)
	ret0, _ := ret[0].(*pia.SetCompereMicResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetCompereMic indicates an expected call of SetCompereMic.
func (mr *MockPiaServerMockRecorder) SetCompereMic(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetCompereMic", reflect.TypeOf((*MockPiaServer)(nil).SetCompereMic), arg0, arg1)
}

// SetDramaCopyStatus mocks base method.
func (m *MockPiaServer) SetDramaCopyStatus(arg0 context.Context, arg1 *pia.SetDramaCopyStatusReq) (*pia.SetDramaCopyStatusResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetDramaCopyStatus", arg0, arg1)
	ret0, _ := ret[0].(*pia.SetDramaCopyStatusResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetDramaCopyStatus indicates an expected call of SetDramaCopyStatus.
func (mr *MockPiaServerMockRecorder) SetDramaCopyStatus(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetDramaCopyStatus", reflect.TypeOf((*MockPiaServer)(nil).SetDramaCopyStatus), arg0, arg1)
}

// SetPiaPhase mocks base method.
func (m *MockPiaServer) SetPiaPhase(arg0 context.Context, arg1 *pia.SetPiaPhaseReq) (*pia.SetPiaPhaseResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetPiaPhase", arg0, arg1)
	ret0, _ := ret[0].(*pia.SetPiaPhaseResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetPiaPhase indicates an expected call of SetPiaPhase.
func (mr *MockPiaServerMockRecorder) SetPiaPhase(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetPiaPhase", reflect.TypeOf((*MockPiaServer)(nil).SetPiaPhase), arg0, arg1)
}

// SetPiaProgress mocks base method.
func (m *MockPiaServer) SetPiaProgress(arg0 context.Context, arg1 *pia.SetPiaProgressReq) (*pia.SetPiaProgressResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetPiaProgress", arg0, arg1)
	ret0, _ := ret[0].(*pia.SetPiaProgressResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetPiaProgress indicates an expected call of SetPiaProgress.
func (mr *MockPiaServerMockRecorder) SetPiaProgress(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetPiaProgress", reflect.TypeOf((*MockPiaServer)(nil).SetPiaProgress), arg0, arg1)
}

// SetPiaSwitch mocks base method.
func (m *MockPiaServer) SetPiaSwitch(arg0 context.Context, arg1 *pia.SetPiaSwitchReq) (*pia.SetPiaSwitchResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetPiaSwitch", arg0, arg1)
	ret0, _ := ret[0].(*pia.SetPiaSwitchResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetPiaSwitch indicates an expected call of SetPiaSwitch.
func (mr *MockPiaServerMockRecorder) SetPiaSwitch(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetPiaSwitch", reflect.TypeOf((*MockPiaServer)(nil).SetPiaSwitch), arg0, arg1)
}

// SetPiaSwitchV2 mocks base method.
func (m *MockPiaServer) SetPiaSwitchV2(arg0 context.Context, arg1 *pia.SetPiaSwitchV2Req) (*pia.SetPiaSwitchV2Resp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetPiaSwitchV2", arg0, arg1)
	ret0, _ := ret[0].(*pia.SetPiaSwitchV2Resp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetPiaSwitchV2 indicates an expected call of SetPiaSwitchV2.
func (mr *MockPiaServerMockRecorder) SetPiaSwitchV2(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetPiaSwitchV2", reflect.TypeOf((*MockPiaServer)(nil).SetPiaSwitchV2), arg0, arg1)
}

// StopDrama mocks base method.
func (m *MockPiaServer) StopDrama(arg0 context.Context, arg1 *pia.PiaOperateDramaReq) (*pia.PiaOperateDramaResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "StopDrama", arg0, arg1)
	ret0, _ := ret[0].(*pia.PiaOperateDramaResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// StopDrama indicates an expected call of StopDrama.
func (mr *MockPiaServerMockRecorder) StopDrama(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "StopDrama", reflect.TypeOf((*MockPiaServer)(nil).StopDrama), arg0, arg1)
}

// TestDeleteDrama mocks base method.
func (m *MockPiaServer) TestDeleteDrama(arg0 context.Context, arg1 *pia.TestDeleteDramaReq) (*pia.TestDeleteDramaResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TestDeleteDrama", arg0, arg1)
	ret0, _ := ret[0].(*pia.TestDeleteDramaResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// TestDeleteDrama indicates an expected call of TestDeleteDrama.
func (mr *MockPiaServerMockRecorder) TestDeleteDrama(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TestDeleteDrama", reflect.TypeOf((*MockPiaServer)(nil).TestDeleteDrama), arg0, arg1)
}

// UpdateStickDramaV2 mocks base method.
func (m *MockPiaServer) UpdateStickDramaV2(arg0 context.Context, arg1 *pia.UpdateStickDramaV2Req) (*pia.EmptyMsg, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateStickDramaV2", arg0, arg1)
	ret0, _ := ret[0].(*pia.EmptyMsg)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateStickDramaV2 indicates an expected call of UpdateStickDramaV2.
func (mr *MockPiaServerMockRecorder) UpdateStickDramaV2(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateStickDramaV2", reflect.TypeOf((*MockPiaServer)(nil).UpdateStickDramaV2), arg0, arg1)
}

// UpdateStickPiaRoom mocks base method.
func (m *MockPiaServer) UpdateStickPiaRoom(arg0 context.Context, arg1 *pia.UpdateStickPiaRoomReq) (*pia.EmptyMsg, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateStickPiaRoom", arg0, arg1)
	ret0, _ := ret[0].(*pia.EmptyMsg)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateStickPiaRoom indicates an expected call of UpdateStickPiaRoom.
func (mr *MockPiaServerMockRecorder) UpdateStickPiaRoom(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateStickPiaRoom", reflect.TypeOf((*MockPiaServer)(nil).UpdateStickPiaRoom), arg0, arg1)
}
