// Code generated by MockGen. DO NOT EDIT.
// Source: /home/<USER>/gopath/src/golang.52tt.com/clients/apicenter-go/iclient.go

// Package apicenter_go is a generated GoMock package.
package apicenter_go

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	client "golang.52tt.com/pkg/client"
	protocol "golang.52tt.com/pkg/protocol"
	apicentergo "golang.52tt.com/protocol/services/apicentergo"
)

// MockIClient is a mock of IClient interface.
type MockIClient struct {
	ctrl     *gomock.Controller
	recorder *MockIClientMockRecorder
}

// MockIClientMockRecorder is the mock recorder for MockIClient.
type MockIClientMockRecorder struct {
	mock *MockIClient
}

// NewMockIClient creates a new mock instance.
func NewMockIClient(ctrl *gomock.Controller) *MockIClient {
	mock := &MockIClient{ctrl: ctrl}
	mock.recorder = &MockIClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIClient) EXPECT() *MockIClientMockRecorder {
	return m.recorder
}

// AddLevelupChildPresent mocks base method.
func (m *MockIClient) AddLevelupChildPresent(ctx context.Context, req *apicentergo.AddLevelupChildPresentReq) (*apicentergo.EmptyMsg, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddLevelupChildPresent", ctx, req)
	ret0, _ := ret[0].(*apicentergo.EmptyMsg)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddLevelupChildPresent indicates an expected call of AddLevelupChildPresent.
func (mr *MockIClientMockRecorder) AddLevelupChildPresent(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddLevelupChildPresent", reflect.TypeOf((*MockIClient)(nil).AddLevelupChildPresent), ctx, req)
}

// AddLevelupParentPresent mocks base method.
func (m *MockIClient) AddLevelupParentPresent(ctx context.Context, req *apicentergo.AddLevelupParentPresentReq) (*apicentergo.EmptyMsg, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddLevelupParentPresent", ctx, req)
	ret0, _ := ret[0].(*apicentergo.EmptyMsg)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddLevelupParentPresent indicates an expected call of AddLevelupParentPresent.
func (mr *MockIClientMockRecorder) AddLevelupParentPresent(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddLevelupParentPresent", reflect.TypeOf((*MockIClient)(nil).AddLevelupParentPresent), ctx, req)
}

// AddLevelupPresentVersion mocks base method.
func (m *MockIClient) AddLevelupPresentVersion(ctx context.Context, itemId, version uint32) (*apicentergo.EmptyMsg, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddLevelupPresentVersion", ctx, itemId, version)
	ret0, _ := ret[0].(*apicentergo.EmptyMsg)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddLevelupPresentVersion indicates an expected call of AddLevelupPresentVersion.
func (mr *MockIClientMockRecorder) AddLevelupPresentVersion(ctx, itemId, version interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddLevelupPresentVersion", reflect.TypeOf((*MockIClient)(nil).AddLevelupPresentVersion), ctx, itemId, version)
}

// CC mocks base method.
func (m *MockIClient) CC() client.Conn {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CC")
	ret0, _ := ret[0].(client.Conn)
	return ret0
}

// CC indicates an expected call of CC.
func (mr *MockIClientMockRecorder) CC() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CC", reflect.TypeOf((*MockIClient)(nil).CC))
}

// Close mocks base method.
func (m *MockIClient) Close() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Close")
}

// Close indicates an expected call of Close.
func (mr *MockIClientMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockIClient)(nil).Close))
}

// DeleteLevelupChildPresent mocks base method.
func (m *MockIClient) DeleteLevelupChildPresent(ctx context.Context, itemId uint32) (*apicentergo.EmptyMsg, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteLevelupChildPresent", ctx, itemId)
	ret0, _ := ret[0].(*apicentergo.EmptyMsg)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteLevelupChildPresent indicates an expected call of DeleteLevelupChildPresent.
func (mr *MockIClientMockRecorder) DeleteLevelupChildPresent(ctx, itemId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteLevelupChildPresent", reflect.TypeOf((*MockIClient)(nil).DeleteLevelupChildPresent), ctx, itemId)
}

// DeleteLevelupParentPresent mocks base method.
func (m *MockIClient) DeleteLevelupParentPresent(ctx context.Context, itemId uint32) (*apicentergo.EmptyMsg, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteLevelupParentPresent", ctx, itemId)
	ret0, _ := ret[0].(*apicentergo.EmptyMsg)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteLevelupParentPresent indicates an expected call of DeleteLevelupParentPresent.
func (mr *MockIClientMockRecorder) DeleteLevelupParentPresent(ctx, itemId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteLevelupParentPresent", reflect.TypeOf((*MockIClient)(nil).DeleteLevelupParentPresent), ctx, itemId)
}

// GetAnchorList mocks base method.
func (m *MockIClient) GetAnchorList(ctx context.Context, req *apicentergo.GetAnchorListReq) (*apicentergo.GetAnchorListResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAnchorList", ctx, req)
	ret0, _ := ret[0].(*apicentergo.GetAnchorListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAnchorList indicates an expected call of GetAnchorList.
func (mr *MockIClientMockRecorder) GetAnchorList(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAnchorList", reflect.TypeOf((*MockIClient)(nil).GetAnchorList), ctx, req)
}

// GetLevelupChildPresentList mocks base method.
func (m *MockIClient) GetLevelupChildPresentList(ctx context.Context, parentItemId uint32) (*apicentergo.EntireChildPresentDataList, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLevelupChildPresentList", ctx, parentItemId)
	ret0, _ := ret[0].(*apicentergo.EntireChildPresentDataList)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLevelupChildPresentList indicates an expected call of GetLevelupChildPresentList.
func (mr *MockIClientMockRecorder) GetLevelupChildPresentList(ctx, parentItemId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLevelupChildPresentList", reflect.TypeOf((*MockIClient)(nil).GetLevelupChildPresentList), ctx, parentItemId)
}

// GetLevelupParentPresentList mocks base method.
func (m *MockIClient) GetLevelupParentPresentList(ctx context.Context, offset, limit uint32, presentType int32) (*apicentergo.EntireParentPresentDataList, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLevelupParentPresentList", ctx, offset, limit, presentType)
	ret0, _ := ret[0].(*apicentergo.EntireParentPresentDataList)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLevelupParentPresentList indicates an expected call of GetLevelupParentPresentList.
func (mr *MockIClientMockRecorder) GetLevelupParentPresentList(ctx, offset, limit, presentType interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLevelupParentPresentList", reflect.TypeOf((*MockIClient)(nil).GetLevelupParentPresentList), ctx, offset, limit, presentType)
}

// GetLevelupPresentVersionList mocks base method.
func (m *MockIClient) GetLevelupPresentVersionList(ctx context.Context, itemId uint32) (*apicentergo.VersionList, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLevelupPresentVersionList", ctx, itemId)
	ret0, _ := ret[0].(*apicentergo.VersionList)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLevelupPresentVersionList indicates an expected call of GetLevelupPresentVersionList.
func (mr *MockIClientMockRecorder) GetLevelupPresentVersionList(ctx, itemId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLevelupPresentVersionList", reflect.TypeOf((*MockIClient)(nil).GetLevelupPresentVersionList), ctx, itemId)
}

// GetPresentConfigById mocks base method.
func (m *MockIClient) GetPresentConfigById(ctx context.Context, in *apicentergo.GetPresentConfigByIdReq) (*apicentergo.GetPresentConfigByIdResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPresentConfigById", ctx, in)
	ret0, _ := ret[0].(*apicentergo.GetPresentConfigByIdResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetPresentConfigById indicates an expected call of GetPresentConfigById.
func (mr *MockIClientMockRecorder) GetPresentConfigById(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPresentConfigById", reflect.TypeOf((*MockIClient)(nil).GetPresentConfigById), ctx, in)
}

// GetUserInfo mocks base method.
func (m *MockIClient) GetUserInfo(ctx context.Context, uin uint32) (*apicentergo.GetUserInfoResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserInfo", ctx, uin)
	ret0, _ := ret[0].(*apicentergo.GetUserInfoResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetUserInfo indicates an expected call of GetUserInfo.
func (mr *MockIClientMockRecorder) GetUserInfo(ctx, uin interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserInfo", reflect.TypeOf((*MockIClient)(nil).GetUserInfo), ctx, uin)
}

// OfficialHandleApplySign mocks base method.
func (m *MockIClient) OfficialHandleApplySign(ctx context.Context, uin uint32, req *apicentergo.OfficialHandleApplySignReq) protocol.ServerError {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "OfficialHandleApplySign", ctx, uin, req)
	ret0, _ := ret[0].(protocol.ServerError)
	return ret0
}

// OfficialHandleApplySign indicates an expected call of OfficialHandleApplySign.
func (mr *MockIClientMockRecorder) OfficialHandleApplySign(ctx, uin, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "OfficialHandleApplySign", reflect.TypeOf((*MockIClient)(nil).OfficialHandleApplySign), ctx, uin, req)
}

// PushFromExcel mocks base method.
func (m *MockIClient) PushFromExcel(ctx context.Context, in *apicentergo.PushFromExcelReq) (*apicentergo.PushFromExcelResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PushFromExcel", ctx, in)
	ret0, _ := ret[0].(*apicentergo.PushFromExcelResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// PushFromExcel indicates an expected call of PushFromExcel.
func (mr *MockIClientMockRecorder) PushFromExcel(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PushFromExcel", reflect.TypeOf((*MockIClient)(nil).PushFromExcel), ctx, in)
}

// Stub mocks base method.
func (m *MockIClient) Stub() interface{} {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Stub")
	ret0, _ := ret[0].(interface{})
	return ret0
}

// Stub indicates an expected call of Stub.
func (mr *MockIClientMockRecorder) Stub() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Stub", reflect.TypeOf((*MockIClient)(nil).Stub))
}

// UpdateLevelupChildPresent mocks base method.
func (m *MockIClient) UpdateLevelupChildPresent(ctx context.Context, req *apicentergo.UpdateLevelupChildPresentReq) (*apicentergo.EmptyMsg, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateLevelupChildPresent", ctx, req)
	ret0, _ := ret[0].(*apicentergo.EmptyMsg)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateLevelupChildPresent indicates an expected call of UpdateLevelupChildPresent.
func (mr *MockIClientMockRecorder) UpdateLevelupChildPresent(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateLevelupChildPresent", reflect.TypeOf((*MockIClient)(nil).UpdateLevelupChildPresent), ctx, req)
}

// UpdateLevelupParentPresent mocks base method.
func (m *MockIClient) UpdateLevelupParentPresent(ctx context.Context, req *apicentergo.UpdateLevelupParentPresentReq) (*apicentergo.EmptyMsg, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateLevelupParentPresent", ctx, req)
	ret0, _ := ret[0].(*apicentergo.EmptyMsg)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateLevelupParentPresent indicates an expected call of UpdateLevelupParentPresent.
func (mr *MockIClientMockRecorder) UpdateLevelupParentPresent(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateLevelupParentPresent", reflect.TypeOf((*MockIClient)(nil).UpdateLevelupParentPresent), ctx, req)
}
