// Code generated by MockGen. DO NOT EDIT.
// Source: /Users/<USER>/project/quicksilver/clients/backpack/iclient.go

// Package backpack is a generated GoMock package.
package backpack

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	client "golang.52tt.com/pkg/client"
	protocol "golang.52tt.com/pkg/protocol"
	backpack "golang.52tt.com/protocol/services/backpacksvr"
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// MockIClient is a mock of IClient interface.
type MockIClient struct {
	ctrl     *gomock.Controller
	recorder *MockIClientMockRecorder
}

// MockIClientMockRecorder is the mock recorder for MockIClient.
type MockIClientMockRecorder struct {
	mock *MockIClient
}

// NewMockIClient creates a new mock instance.
func NewMockIClient(ctrl *gomock.Controller) *MockIClient {
	mock := &MockIClient{ctrl: ctrl}
	mock.recorder = &MockIClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIClient) EXPECT() *MockIClientMockRecorder {
	return m.recorder
}

// AddFuncCardCfg mocks base method.
func (m *MockIClient) AddFuncCardCfg(ctx context.Context, cfg *backpack.FuncCardCfg) (*backpack.AddFuncCardCfgResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddFuncCardCfg", ctx, cfg)
	ret0, _ := ret[0].(*backpack.AddFuncCardCfgResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// AddFuncCardCfg indicates an expected call of AddFuncCardCfg.
func (mr *MockIClientMockRecorder) AddFuncCardCfg(ctx, cfg interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddFuncCardCfg", reflect.TypeOf((*MockIClient)(nil).AddFuncCardCfg), ctx, cfg)
}

// AddItemCfg mocks base method.
func (m *MockIClient) AddItemCfg(ctx context.Context, in *backpack.AddItemCfgReq) (*backpack.AddItemCfgResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddItemCfg", ctx, in)
	ret0, _ := ret[0].(*backpack.AddItemCfgResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// AddItemCfg indicates an expected call of AddItemCfg.
func (mr *MockIClientMockRecorder) AddItemCfg(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddItemCfg", reflect.TypeOf((*MockIClient)(nil).AddItemCfg), ctx, in)
}

// AddPackageCfg mocks base method.
func (m *MockIClient) AddPackageCfg(ctx context.Context, bgName, bgDesc string) (*backpack.AddPackageCfgResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddPackageCfg", ctx, bgName, bgDesc)
	ret0, _ := ret[0].(*backpack.AddPackageCfgResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// AddPackageCfg indicates an expected call of AddPackageCfg.
func (mr *MockIClientMockRecorder) AddPackageCfg(ctx, bgName, bgDesc interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddPackageCfg", reflect.TypeOf((*MockIClient)(nil).AddPackageCfg), ctx, bgName, bgDesc)
}

// AddPackageItemCfg mocks base method.
func (m *MockIClient) AddPackageItemCfg(ctx context.Context, cfg *backpack.PackageItemCfg) (*backpack.AddPackageItemCfgResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddPackageItemCfg", ctx, cfg)
	ret0, _ := ret[0].(*backpack.AddPackageItemCfgResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// AddPackageItemCfg indicates an expected call of AddPackageItemCfg.
func (mr *MockIClientMockRecorder) AddPackageItemCfg(ctx, cfg interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddPackageItemCfg", reflect.TypeOf((*MockIClient)(nil).AddPackageItemCfg), ctx, cfg)
}

// BatchDeductUserItem mocks base method.
func (m *MockIClient) BatchDeductUserItem(ctx context.Context, req *backpack.BatchDeductUserItemReq) (*backpack.BatchDeductUserItemResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchDeductUserItem", ctx, req)
	ret0, _ := ret[0].(*backpack.BatchDeductUserItemResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// BatchDeductUserItem indicates an expected call of BatchDeductUserItem.
func (mr *MockIClientMockRecorder) BatchDeductUserItem(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchDeductUserItem", reflect.TypeOf((*MockIClient)(nil).BatchDeductUserItem), ctx, req)
}

// BatchGetUserFuncCardUse mocks base method.
func (m *MockIClient) BatchGetUserFuncCardUse(ctx context.Context, in *backpack.BatchGetUserFuncCardUseReq) (map[uint32][]*backpack.FuncCardCfg, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetUserFuncCardUse", ctx, in)
	ret0, _ := ret[0].(map[uint32][]*backpack.FuncCardCfg)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// BatchGetUserFuncCardUse indicates an expected call of BatchGetUserFuncCardUse.
func (mr *MockIClientMockRecorder) BatchGetUserFuncCardUse(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetUserFuncCardUse", reflect.TypeOf((*MockIClient)(nil).BatchGetUserFuncCardUse), ctx, in)
}

// CC mocks base method.
func (m *MockIClient) CC() client.Conn {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CC")
	ret0, _ := ret[0].(client.Conn)
	return ret0
}

// CC indicates an expected call of CC.
func (mr *MockIClientMockRecorder) CC() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CC", reflect.TypeOf((*MockIClient)(nil).CC))
}

// Close mocks base method.
func (m *MockIClient) Close() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Close")
}

// Close indicates an expected call of Close.
func (mr *MockIClientMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockIClient)(nil).Close))
}

// ConversionItem mocks base method.
func (m *MockIClient) ConversionItem(ctx context.Context, uid uint32, req *backpack.ConversionItemReq) (*backpack.ConversionItemResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ConversionItem", ctx, uid, req)
	ret0, _ := ret[0].(*backpack.ConversionItemResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// ConversionItem indicates an expected call of ConversionItem.
func (mr *MockIClientMockRecorder) ConversionItem(ctx, uid, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ConversionItem", reflect.TypeOf((*MockIClient)(nil).ConversionItem), ctx, uid, req)
}

// DelFuncCardCfg mocks base method.
func (m *MockIClient) DelFuncCardCfg(ctx context.Context, cardId uint32) (*backpack.DelFuncCardCfgResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelFuncCardCfg", ctx, cardId)
	ret0, _ := ret[0].(*backpack.DelFuncCardCfgResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// DelFuncCardCfg indicates an expected call of DelFuncCardCfg.
func (mr *MockIClientMockRecorder) DelFuncCardCfg(ctx, cardId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelFuncCardCfg", reflect.TypeOf((*MockIClient)(nil).DelFuncCardCfg), ctx, cardId)
}

// DelPackageCfg mocks base method.
func (m *MockIClient) DelPackageCfg(ctx context.Context, bgId uint32) (*backpack.DelPackageCfgResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelPackageCfg", ctx, bgId)
	ret0, _ := ret[0].(*backpack.DelPackageCfgResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// DelPackageCfg indicates an expected call of DelPackageCfg.
func (mr *MockIClientMockRecorder) DelPackageCfg(ctx, bgId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelPackageCfg", reflect.TypeOf((*MockIClient)(nil).DelPackageCfg), ctx, bgId)
}

// DelPackageItemCfg mocks base method.
func (m *MockIClient) DelPackageItemCfg(ctx context.Context, bgId, bgItemId uint32) (*backpack.DelPackageItemCfgResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelPackageItemCfg", ctx, bgId, bgItemId)
	ret0, _ := ret[0].(*backpack.DelPackageItemCfgResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// DelPackageItemCfg indicates an expected call of DelPackageItemCfg.
func (mr *MockIClientMockRecorder) DelPackageItemCfg(ctx, bgId, bgItemId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelPackageItemCfg", reflect.TypeOf((*MockIClient)(nil).DelPackageItemCfg), ctx, bgId, bgItemId)
}

// FreeZeItem mocks base method.
func (m *MockIClient) FreeZeItem(ctx context.Context, uid uint32, req *backpack.FreeZeItemReq) (*backpack.FreeZeItemResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FreeZeItem", ctx, uid, req)
	ret0, _ := ret[0].(*backpack.FreeZeItemResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// FreeZeItem indicates an expected call of FreeZeItem.
func (mr *MockIClientMockRecorder) FreeZeItem(ctx, uid, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FreeZeItem", reflect.TypeOf((*MockIClient)(nil).FreeZeItem), ctx, uid, req)
}

// GetFuncCardCfg mocks base method.
func (m *MockIClient) GetFuncCardCfg(ctx context.Context, uid uint32, req *backpack.GetFuncCardCfgReq) (*backpack.GetFuncCardCfgResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetFuncCardCfg", ctx, uid, req)
	ret0, _ := ret[0].(*backpack.GetFuncCardCfgResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetFuncCardCfg indicates an expected call of GetFuncCardCfg.
func (mr *MockIClientMockRecorder) GetFuncCardCfg(ctx, uid, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFuncCardCfg", reflect.TypeOf((*MockIClient)(nil).GetFuncCardCfg), ctx, uid, req)
}

// GetItemCfg mocks base method.
func (m *MockIClient) GetItemCfg(ctx context.Context, uid uint32, req *backpack.GetItemCfgReq) (*backpack.GetItemCfgResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetItemCfg", ctx, uid, req)
	ret0, _ := ret[0].(*backpack.GetItemCfgResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetItemCfg indicates an expected call of GetItemCfg.
func (mr *MockIClientMockRecorder) GetItemCfg(ctx, uid, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetItemCfg", reflect.TypeOf((*MockIClient)(nil).GetItemCfg), ctx, uid, req)
}

// GetOrderCountByTimeRange mocks base method.
func (m *MockIClient) GetOrderCountByTimeRange(ctx context.Context, in *backpack.GetOrderCountByTimeRangeReq) (*backpack.GetOrderCountByTimeRangeResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOrderCountByTimeRange", ctx, in)
	ret0, _ := ret[0].(*backpack.GetOrderCountByTimeRangeResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetOrderCountByTimeRange indicates an expected call of GetOrderCountByTimeRange.
func (mr *MockIClientMockRecorder) GetOrderCountByTimeRange(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOrderCountByTimeRange", reflect.TypeOf((*MockIClient)(nil).GetOrderCountByTimeRange), ctx, in)
}

// GetOrderListByTimeRange mocks base method.
func (m *MockIClient) GetOrderListByTimeRange(ctx context.Context, startTime, endTime, logType uint32) ([]string, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOrderListByTimeRange", ctx, startTime, endTime, logType)
	ret0, _ := ret[0].([]string)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetOrderListByTimeRange indicates an expected call of GetOrderListByTimeRange.
func (mr *MockIClientMockRecorder) GetOrderListByTimeRange(ctx, startTime, endTime, logType interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOrderListByTimeRange", reflect.TypeOf((*MockIClient)(nil).GetOrderListByTimeRange), ctx, startTime, endTime, logType)
}

// GetPackageCfg mocks base method.
func (m *MockIClient) GetPackageCfg(ctx context.Context, uid uint32, req *backpack.GetPackageCfgReq) (*backpack.GetPackageCfgResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPackageCfg", ctx, uid, req)
	ret0, _ := ret[0].(*backpack.GetPackageCfgResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetPackageCfg indicates an expected call of GetPackageCfg.
func (mr *MockIClientMockRecorder) GetPackageCfg(ctx, uid, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPackageCfg", reflect.TypeOf((*MockIClient)(nil).GetPackageCfg), ctx, uid, req)
}

// GetPackageCfgV2 mocks base method.
func (m *MockIClient) GetPackageCfgV2(ctx context.Context) (*backpack.GetPackageCfgResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPackageCfgV2", ctx)
	ret0, _ := ret[0].(*backpack.GetPackageCfgResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetPackageCfgV2 indicates an expected call of GetPackageCfgV2.
func (mr *MockIClientMockRecorder) GetPackageCfgV2(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPackageCfgV2", reflect.TypeOf((*MockIClient)(nil).GetPackageCfgV2), ctx)
}

// GetPackageItemCfg mocks base method.
func (m *MockIClient) GetPackageItemCfg(ctx context.Context, uid uint32, req *backpack.GetPackageItemCfgReq, opts ...grpc.CallOption) (*backpack.GetPackageItemCfgResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, uid, req}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetPackageItemCfg", varargs...)
	ret0, _ := ret[0].(*backpack.GetPackageItemCfgResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetPackageItemCfg indicates an expected call of GetPackageItemCfg.
func (mr *MockIClientMockRecorder) GetPackageItemCfg(ctx, uid, req interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, uid, req}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPackageItemCfg", reflect.TypeOf((*MockIClient)(nil).GetPackageItemCfg), varargs...)
}

// GetTimeRangeOrderList mocks base method.
func (m *MockIClient) GetTimeRangeOrderList(ctx context.Context, in *backpack.TimeRangeReq) (*backpack.OrderIdsResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTimeRangeOrderList", ctx, in)
	ret0, _ := ret[0].(*backpack.OrderIdsResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetTimeRangeOrderList indicates an expected call of GetTimeRangeOrderList.
func (mr *MockIClientMockRecorder) GetTimeRangeOrderList(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTimeRangeOrderList", reflect.TypeOf((*MockIClient)(nil).GetTimeRangeOrderList), ctx, in)
}

// GetTimeRangeUseOrderData mocks base method.
func (m *MockIClient) GetTimeRangeUseOrderData(ctx context.Context, in *backpack.TimeRangeReq) (*backpack.CountResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTimeRangeUseOrderData", ctx, in)
	ret0, _ := ret[0].(*backpack.CountResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetTimeRangeUseOrderData indicates an expected call of GetTimeRangeUseOrderData.
func (mr *MockIClientMockRecorder) GetTimeRangeUseOrderData(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTimeRangeUseOrderData", reflect.TypeOf((*MockIClient)(nil).GetTimeRangeUseOrderData), ctx, in)
}

// GetUseItemOrderInfo mocks base method.
func (m *MockIClient) GetUseItemOrderInfo(ctx context.Context, in *backpack.GetUseItemOrderInfoReq) (*backpack.GetUseItemOrderInfoResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUseItemOrderInfo", ctx, in)
	ret0, _ := ret[0].(*backpack.GetUseItemOrderInfoResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetUseItemOrderInfo indicates an expected call of GetUseItemOrderInfo.
func (mr *MockIClientMockRecorder) GetUseItemOrderInfo(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUseItemOrderInfo", reflect.TypeOf((*MockIClient)(nil).GetUseItemOrderInfo), ctx, in)
}

// GetUserBackpack mocks base method.
func (m *MockIClient) GetUserBackpack(ctx context.Context, uid uint32) (*backpack.GetUserBackpackResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserBackpack", ctx, uid)
	ret0, _ := ret[0].(*backpack.GetUserBackpackResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetUserBackpack indicates an expected call of GetUserBackpack.
func (mr *MockIClientMockRecorder) GetUserBackpack(ctx, uid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserBackpack", reflect.TypeOf((*MockIClient)(nil).GetUserBackpack), ctx, uid)
}

// GetUserBackpackLog mocks base method.
func (m *MockIClient) GetUserBackpackLog(ctx context.Context, uid uint32) (*backpack.GetUserBackpackLogResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserBackpackLog", ctx, uid)
	ret0, _ := ret[0].(*backpack.GetUserBackpackLogResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetUserBackpackLog indicates an expected call of GetUserBackpackLog.
func (mr *MockIClientMockRecorder) GetUserBackpackLog(ctx, uid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserBackpackLog", reflect.TypeOf((*MockIClient)(nil).GetUserBackpackLog), ctx, uid)
}

// GetUserFuncCardUse mocks base method.
func (m *MockIClient) GetUserFuncCardUse(ctx context.Context, in *backpack.GetUserFuncCardUseReq) (*backpack.GetUserFuncCardUseResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserFuncCardUse", ctx, in)
	ret0, _ := ret[0].(*backpack.GetUserFuncCardUseResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetUserFuncCardUse indicates an expected call of GetUserFuncCardUse.
func (mr *MockIClientMockRecorder) GetUserFuncCardUse(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserFuncCardUse", reflect.TypeOf((*MockIClient)(nil).GetUserFuncCardUse), ctx, in)
}

// GiveUserPackage mocks base method.
func (m *MockIClient) GiveUserPackage(ctx context.Context, uid uint32, req *backpack.GiveUserPackageReq) (*backpack.GiveUserPackageResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GiveUserPackage", ctx, uid, req)
	ret0, _ := ret[0].(*backpack.GiveUserPackageResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GiveUserPackage indicates an expected call of GiveUserPackage.
func (mr *MockIClientMockRecorder) GiveUserPackage(ctx, uid, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GiveUserPackage", reflect.TypeOf((*MockIClient)(nil).GiveUserPackage), ctx, uid, req)
}

// ModPackageItemCfg mocks base method.
func (m *MockIClient) ModPackageItemCfg(ctx context.Context, cfg *backpack.PackageItemCfg) (*backpack.ModPackageItemCfgResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ModPackageItemCfg", ctx, cfg)
	ret0, _ := ret[0].(*backpack.ModPackageItemCfgResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// ModPackageItemCfg indicates an expected call of ModPackageItemCfg.
func (mr *MockIClientMockRecorder) ModPackageItemCfg(ctx, cfg interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ModPackageItemCfg", reflect.TypeOf((*MockIClient)(nil).ModPackageItemCfg), ctx, cfg)
}

// RollBackUserItem mocks base method.
func (m *MockIClient) RollBackUserItem(ctx context.Context, uid, createTime uint32, orderID string) (*backpack.RollBackUserItemResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RollBackUserItem", ctx, uid, createTime, orderID)
	ret0, _ := ret[0].(*backpack.RollBackUserItemResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// RollBackUserItem indicates an expected call of RollBackUserItem.
func (mr *MockIClientMockRecorder) RollBackUserItem(ctx, uid, createTime, orderID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RollBackUserItem", reflect.TypeOf((*MockIClient)(nil).RollBackUserItem), ctx, uid, createTime, orderID)
}

// Stub mocks base method.
func (m *MockIClient) Stub() interface{} {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Stub")
	ret0, _ := ret[0].(interface{})
	return ret0
}

// Stub indicates an expected call of Stub.
func (mr *MockIClientMockRecorder) Stub() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Stub", reflect.TypeOf((*MockIClient)(nil).Stub))
}

// UseBackpackItem mocks base method.
func (m *MockIClient) UseBackpackItem(ctx context.Context, in *backpack.UseBackpackItemReq) (*backpack.UseBackpackItemResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UseBackpackItem", ctx, in)
	ret0, _ := ret[0].(*backpack.UseBackpackItemResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// UseBackpackItem indicates an expected call of UseBackpackItem.
func (mr *MockIClientMockRecorder) UseBackpackItem(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UseBackpackItem", reflect.TypeOf((*MockIClient)(nil).UseBackpackItem), ctx, in)
}
