// Code generated by MockGen. DO NOT EDIT.
// Source: /Users/<USER>/code/quicksilver/clients/mijing-invitation/iclient.go

// Package mijing_invitation is a generated GoMock package.
package mijing_invitation

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	client "golang.52tt.com/pkg/client"
	mijing_invitation "golang.52tt.com/protocol/services/mijing-invitation"
	grpc "google.golang.org/grpc"
)

// MockIClient is a mock of IClient interface.
type MockIClient struct {
	ctrl     *gomock.Controller
	recorder *MockIClientMockRecorder
}

// MockIClientMockRecorder is the mock recorder for MockIClient.
type MockIClientMockRecorder struct {
	mock *MockIClient
}

// NewMockIClient creates a new mock instance.
func NewMockIClient(ctrl *gomock.Controller) *MockIClient {
	mock := &MockIClient{ctrl: ctrl}
	mock.recorder = &MockIClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIClient) EXPECT() *MockIClientMockRecorder {
	return m.recorder
}

// AcceptInvitation mocks base method.
func (m *MockIClient) AcceptInvitation(ctx context.Context, in *mijing_invitation.AcceptInvitationReq, opts ...grpc.CallOption) (*mijing_invitation.AcceptInvitationResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AcceptInvitation", varargs...)
	ret0, _ := ret[0].(*mijing_invitation.AcceptInvitationResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AcceptInvitation indicates an expected call of AcceptInvitation.
func (mr *MockIClientMockRecorder) AcceptInvitation(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AcceptInvitation", reflect.TypeOf((*MockIClient)(nil).AcceptInvitation), varargs...)
}

// CC mocks base method.
func (m *MockIClient) CC() client.Conn {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CC")
	ret0, _ := ret[0].(client.Conn)
	return ret0
}

// CC indicates an expected call of CC.
func (mr *MockIClientMockRecorder) CC() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CC", reflect.TypeOf((*MockIClient)(nil).CC))
}

// Close mocks base method.
func (m *MockIClient) Close() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Close")
}

// Close indicates an expected call of Close.
func (mr *MockIClientMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockIClient)(nil).Close))
}

// GetGenInvitation mocks base method.
func (m *MockIClient) GetGenInvitation(ctx context.Context, in *mijing_invitation.GetGenInvitationReq, opts ...grpc.CallOption) (*mijing_invitation.GetGenInvitationResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetGenInvitation", varargs...)
	ret0, _ := ret[0].(*mijing_invitation.GetGenInvitationResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGenInvitation indicates an expected call of GetGenInvitation.
func (mr *MockIClientMockRecorder) GetGenInvitation(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGenInvitation", reflect.TypeOf((*MockIClient)(nil).GetGenInvitation), varargs...)
}

// GetInviterByCode mocks base method.
func (m *MockIClient) GetInviterByCode(ctx context.Context, in *mijing_invitation.GetInviterByCodeReq, opts ...grpc.CallOption) (*mijing_invitation.GetInviterByCodeResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetInviterByCode", varargs...)
	ret0, _ := ret[0].(*mijing_invitation.GetInviterByCodeResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetInviterByCode indicates an expected call of GetInviterByCode.
func (mr *MockIClientMockRecorder) GetInviterByCode(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetInviterByCode", reflect.TypeOf((*MockIClient)(nil).GetInviterByCode), varargs...)
}

// Stub mocks base method.
func (m *MockIClient) Stub() interface{} {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Stub")
	ret0, _ := ret[0].(interface{})
	return ret0
}

// Stub indicates an expected call of Stub.
func (mr *MockIClientMockRecorder) Stub() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Stub", reflect.TypeOf((*MockIClient)(nil).Stub))
}
