// Code generated by MockGen. DO NOT EDIT.
// Source: D:\go-tt\griffin\clients\cat-canteen\iclient.go

// Package cat_canteen is a generated GoMock package.
package cat_canteen

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	client "golang.52tt.com/pkg/client"
	cat_canteen "golang.52tt.com/protocol/services/cat-canteen"
	reconcile_v2 "golang.52tt.com/protocol/services/reconcile-v2"
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// MockIClient is a mock of IClient interface.
type MockIClient struct {
	ctrl     *gomock.Controller
	recorder *MockIClientMockRecorder
}

// MockIClientMockRecorder is the mock recorder for MockIClient.
type MockIClientMockRecorder struct {
	mock *MockIClient
}

// NewMockIClient creates a new mock instance.
func NewMockIClient(ctrl *gomock.Controller) *MockIClient {
	mock := &MockIClient{ctrl: ctrl}
	mock.recorder = &MockIClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIClient) EXPECT() *MockIClientMockRecorder {
	return m.recorder
}

// AddCatLightEffects mocks base method.
func (m *MockIClient) AddCatLightEffects(ctx context.Context, req *cat_canteen.AddCatLightEffectsReq, opts ...grpc.CallOption) (*cat_canteen.AddCatLightEffectsResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, req}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AddCatLightEffects", varargs...)
	ret0, _ := ret[0].(*cat_canteen.AddCatLightEffectsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddCatLightEffects indicates an expected call of AddCatLightEffects.
func (mr *MockIClientMockRecorder) AddCatLightEffects(ctx, req interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, req}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddCatLightEffects", reflect.TypeOf((*MockIClient)(nil).AddCatLightEffects), varargs...)
}

// AddCatLightEffectsV2 mocks base method.
func (m *MockIClient) AddCatLightEffectsV2(ctx context.Context, in *cat_canteen.AddCatLightEffectsV2Req, opts ...grpc.CallOption) (*cat_canteen.AddCatLightEffectsV2Resp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AddCatLightEffectsV2", varargs...)
	ret0, _ := ret[0].(*cat_canteen.AddCatLightEffectsV2Resp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddCatLightEffectsV2 indicates an expected call of AddCatLightEffectsV2.
func (mr *MockIClientMockRecorder) AddCatLightEffectsV2(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddCatLightEffectsV2", reflect.TypeOf((*MockIClient)(nil).AddCatLightEffectsV2), varargs...)
}

// BatGetLightEffectByPackIdList mocks base method.
func (m *MockIClient) BatGetLightEffectByPackIdList(ctx context.Context, req *cat_canteen.BatGetLightEffectByPackIdListReq, opts ...grpc.CallOption) (*cat_canteen.BatGetLightEffectByPackIdListResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, req}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatGetLightEffectByPackIdList", varargs...)
	ret0, _ := ret[0].(*cat_canteen.BatGetLightEffectByPackIdListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatGetLightEffectByPackIdList indicates an expected call of BatGetLightEffectByPackIdList.
func (mr *MockIClientMockRecorder) BatGetLightEffectByPackIdList(ctx, req interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, req}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatGetLightEffectByPackIdList", reflect.TypeOf((*MockIClient)(nil).BatGetLightEffectByPackIdList), varargs...)
}

// BuyChance mocks base method.
func (m *MockIClient) BuyChance(ctx context.Context, req *cat_canteen.BuyChanceReq, opts ...grpc.CallOption) (*cat_canteen.BuyChanceResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, req}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BuyChance", varargs...)
	ret0, _ := ret[0].(*cat_canteen.BuyChanceResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BuyChance indicates an expected call of BuyChance.
func (mr *MockIClientMockRecorder) BuyChance(ctx, req interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, req}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BuyChance", reflect.TypeOf((*MockIClient)(nil).BuyChance), varargs...)
}

// CC mocks base method.
func (m *MockIClient) CC() client.Conn {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CC")
	ret0, _ := ret[0].(client.Conn)
	return ret0
}

// CC indicates an expected call of CC.
func (mr *MockIClientMockRecorder) CC() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CC", reflect.TypeOf((*MockIClient)(nil).CC))
}

// Close mocks base method.
func (m *MockIClient) Close() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Close")
}

// Close indicates an expected call of Close.
func (mr *MockIClientMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockIClient)(nil).Close))
}

// DelAllPendingLevelConf mocks base method.
func (m *MockIClient) DelAllPendingLevelConf(ctx context.Context, req *cat_canteen.DelAllPendingLevelConfReq, opts ...grpc.CallOption) (*cat_canteen.DelAllPendingLevelConfResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, req}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DelAllPendingLevelConf", varargs...)
	ret0, _ := ret[0].(*cat_canteen.DelAllPendingLevelConfResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DelAllPendingLevelConf indicates an expected call of DelAllPendingLevelConf.
func (mr *MockIClientMockRecorder) DelAllPendingLevelConf(ctx, req interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, req}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelAllPendingLevelConf", reflect.TypeOf((*MockIClient)(nil).DelAllPendingLevelConf), varargs...)
}

// DelCatLightEffectByConfId mocks base method.
func (m *MockIClient) DelCatLightEffectByConfId(ctx context.Context, req *cat_canteen.DelCatLightEffectByConfIdReq, opts ...grpc.CallOption) (*cat_canteen.DelCatLightEffectByConfIdResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, req}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DelCatLightEffectByConfId", varargs...)
	ret0, _ := ret[0].(*cat_canteen.DelCatLightEffectByConfIdResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DelCatLightEffectByConfId indicates an expected call of DelCatLightEffectByConfId.
func (mr *MockIClientMockRecorder) DelCatLightEffectByConfId(ctx, req interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, req}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelCatLightEffectByConfId", reflect.TypeOf((*MockIClient)(nil).DelCatLightEffectByConfId), varargs...)
}

// DelEffectiveLevel mocks base method.
func (m *MockIClient) DelEffectiveLevel(ctx context.Context, in *cat_canteen.DelEffectiveLevelReq, opts ...grpc.CallOption) (*cat_canteen.DelEffectiveLevelResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DelEffectiveLevel", varargs...)
	ret0, _ := ret[0].(*cat_canteen.DelEffectiveLevelResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DelEffectiveLevel indicates an expected call of DelEffectiveLevel.
func (mr *MockIClientMockRecorder) DelEffectiveLevel(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelEffectiveLevel", reflect.TypeOf((*MockIClient)(nil).DelEffectiveLevel), varargs...)
}

// DelLightEffectById mocks base method.
func (m *MockIClient) DelLightEffectById(ctx context.Context, in *cat_canteen.DelLightEffectByIdReq, opts ...grpc.CallOption) (*cat_canteen.DelLightEffectByIdResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DelLightEffectById", varargs...)
	ret0, _ := ret[0].(*cat_canteen.DelLightEffectByIdResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DelLightEffectById indicates an expected call of DelLightEffectById.
func (mr *MockIClientMockRecorder) DelLightEffectById(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelLightEffectById", reflect.TypeOf((*MockIClient)(nil).DelLightEffectById), varargs...)
}

// GenFinancialFile mocks base method.
func (m *MockIClient) GenFinancialFile(ctx context.Context, req *reconcile_v2.GenFinancialFileReq, opts ...grpc.CallOption) (*reconcile_v2.GenFinancialFileResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, req}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GenFinancialFile", varargs...)
	ret0, _ := ret[0].(*reconcile_v2.GenFinancialFileResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GenFinancialFile indicates an expected call of GenFinancialFile.
func (mr *MockIClientMockRecorder) GenFinancialFile(ctx, req interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, req}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GenFinancialFile", reflect.TypeOf((*MockIClient)(nil).GenFinancialFile), varargs...)
}

// GetAllCatLightEffects mocks base method.
func (m *MockIClient) GetAllCatLightEffects(ctx context.Context, req *cat_canteen.GetAllCatLightEffectsReq, opts ...grpc.CallOption) (*cat_canteen.GetAllCatLightEffectsResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, req}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetAllCatLightEffects", varargs...)
	ret0, _ := ret[0].(*cat_canteen.GetAllCatLightEffectsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllCatLightEffects indicates an expected call of GetAllCatLightEffects.
func (mr *MockIClientMockRecorder) GetAllCatLightEffects(ctx, req interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, req}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllCatLightEffects", reflect.TypeOf((*MockIClient)(nil).GetAllCatLightEffects), varargs...)
}

// GetAllCatLightEffectsV2 mocks base method.
func (m *MockIClient) GetAllCatLightEffectsV2(ctx context.Context, in *cat_canteen.GetAllCatLightEffectsV2Req, opts ...grpc.CallOption) (*cat_canteen.GetAllCatLightEffectsV2Resp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetAllCatLightEffectsV2", varargs...)
	ret0, _ := ret[0].(*cat_canteen.GetAllCatLightEffectsV2Resp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllCatLightEffectsV2 indicates an expected call of GetAllCatLightEffectsV2.
func (mr *MockIClientMockRecorder) GetAllCatLightEffectsV2(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllCatLightEffectsV2", reflect.TypeOf((*MockIClient)(nil).GetAllCatLightEffectsV2), varargs...)
}

// GetAllLevelConf mocks base method.
func (m *MockIClient) GetAllLevelConf(ctx context.Context, req *cat_canteen.GetAllLevelConfReq, opts ...grpc.CallOption) (*cat_canteen.GetAllLevelConfResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, req}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetAllLevelConf", varargs...)
	ret0, _ := ret[0].(*cat_canteen.GetAllLevelConfResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllLevelConf indicates an expected call of GetAllLevelConf.
func (mr *MockIClientMockRecorder) GetAllLevelConf(ctx, req interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, req}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllLevelConf", reflect.TypeOf((*MockIClient)(nil).GetAllLevelConf), varargs...)
}

// GetAwardOrderIds mocks base method.
func (m *MockIClient) GetAwardOrderIds(ctx context.Context, req *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.OrderIdsResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, req}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetAwardOrderIds", varargs...)
	ret0, _ := ret[0].(*reconcile_v2.OrderIdsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAwardOrderIds indicates an expected call of GetAwardOrderIds.
func (mr *MockIClientMockRecorder) GetAwardOrderIds(ctx, req interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, req}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAwardOrderIds", reflect.TypeOf((*MockIClient)(nil).GetAwardOrderIds), varargs...)
}

// GetAwardTotalCount mocks base method.
func (m *MockIClient) GetAwardTotalCount(ctx context.Context, req *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.CountResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, req}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetAwardTotalCount", varargs...)
	ret0, _ := ret[0].(*reconcile_v2.CountResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAwardTotalCount indicates an expected call of GetAwardTotalCount.
func (mr *MockIClientMockRecorder) GetAwardTotalCount(ctx, req interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, req}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAwardTotalCount", reflect.TypeOf((*MockIClient)(nil).GetAwardTotalCount), varargs...)
}

// GetBackPackCostOrderIds mocks base method.
func (m *MockIClient) GetBackPackCostOrderIds(ctx context.Context, req *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.OrderIdsResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, req}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetBackPackCostOrderIds", varargs...)
	ret0, _ := ret[0].(*reconcile_v2.OrderIdsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBackPackCostOrderIds indicates an expected call of GetBackPackCostOrderIds.
func (mr *MockIClientMockRecorder) GetBackPackCostOrderIds(ctx, req interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, req}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBackPackCostOrderIds", reflect.TypeOf((*MockIClient)(nil).GetBackPackCostOrderIds), varargs...)
}

// GetBackPackCostTotalCount mocks base method.
func (m *MockIClient) GetBackPackCostTotalCount(ctx context.Context, req *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.CountResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, req}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetBackPackCostTotalCount", varargs...)
	ret0, _ := ret[0].(*reconcile_v2.CountResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBackPackCostTotalCount indicates an expected call of GetBackPackCostTotalCount.
func (mr *MockIClientMockRecorder) GetBackPackCostTotalCount(ctx, req interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, req}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBackPackCostTotalCount", reflect.TypeOf((*MockIClient)(nil).GetBackPackCostTotalCount), varargs...)
}

// GetCatCanteenExemptValue mocks base method.
func (m *MockIClient) GetCatCanteenExemptValue(ctx context.Context, req *cat_canteen.GetCatCanteenExemptValueReq, opts ...grpc.CallOption) (*cat_canteen.GetCatCanteenExemptValueResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, req}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetCatCanteenExemptValue", varargs...)
	ret0, _ := ret[0].(*cat_canteen.GetCatCanteenExemptValueResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCatCanteenExemptValue indicates an expected call of GetCatCanteenExemptValue.
func (mr *MockIClientMockRecorder) GetCatCanteenExemptValue(ctx, req interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, req}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCatCanteenExemptValue", reflect.TypeOf((*MockIClient)(nil).GetCatCanteenExemptValue), varargs...)
}

// GetCatCanteenLimitConf mocks base method.
func (m *MockIClient) GetCatCanteenLimitConf(ctx context.Context, req *cat_canteen.GetCatCanteenLimitConfReq, opts ...grpc.CallOption) (*cat_canteen.GetCatCanteenLimitConfResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, req}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetCatCanteenLimitConf", varargs...)
	ret0, _ := ret[0].(*cat_canteen.GetCatCanteenLimitConfResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCatCanteenLimitConf indicates an expected call of GetCatCanteenLimitConf.
func (mr *MockIClientMockRecorder) GetCatCanteenLimitConf(ctx, req interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, req}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCatCanteenLimitConf", reflect.TypeOf((*MockIClient)(nil).GetCatCanteenLimitConf), varargs...)
}

// GetCatLightEffectsV2ByWorth mocks base method.
func (m *MockIClient) GetCatLightEffectsV2ByWorth(ctx context.Context, in *cat_canteen.GetCatLightEffectsV2ByWorthReq, opts ...grpc.CallOption) (*cat_canteen.GetCatLightEffectsV2ByWorthResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetCatLightEffectsV2ByWorth", varargs...)
	ret0, _ := ret[0].(*cat_canteen.GetCatLightEffectsV2ByWorthResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCatLightEffectsV2ByWorth indicates an expected call of GetCatLightEffectsV2ByWorth.
func (mr *MockIClientMockRecorder) GetCatLightEffectsV2ByWorth(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCatLightEffectsV2ByWorth", reflect.TypeOf((*MockIClient)(nil).GetCatLightEffectsV2ByWorth), varargs...)
}

// GetConsumeOrderIds mocks base method.
func (m *MockIClient) GetConsumeOrderIds(ctx context.Context, req *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.OrderIdsResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, req}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetConsumeOrderIds", varargs...)
	ret0, _ := ret[0].(*reconcile_v2.OrderIdsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetConsumeOrderIds indicates an expected call of GetConsumeOrderIds.
func (mr *MockIClientMockRecorder) GetConsumeOrderIds(ctx, req interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, req}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetConsumeOrderIds", reflect.TypeOf((*MockIClient)(nil).GetConsumeOrderIds), varargs...)
}

// GetConsumeTotalCount mocks base method.
func (m *MockIClient) GetConsumeTotalCount(ctx context.Context, req *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.CountResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, req}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetConsumeTotalCount", varargs...)
	ret0, _ := ret[0].(*reconcile_v2.CountResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetConsumeTotalCount indicates an expected call of GetConsumeTotalCount.
func (mr *MockIClientMockRecorder) GetConsumeTotalCount(ctx, req interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, req}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetConsumeTotalCount", reflect.TypeOf((*MockIClient)(nil).GetConsumeTotalCount), varargs...)
}

// GetGameInfo mocks base method.
func (m *MockIClient) GetGameInfo(ctx context.Context, req *cat_canteen.GetGameInfoReq, opts ...grpc.CallOption) (*cat_canteen.GetGameInfoResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, req}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetGameInfo", varargs...)
	ret0, _ := ret[0].(*cat_canteen.GetGameInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGameInfo indicates an expected call of GetGameInfo.
func (mr *MockIClientMockRecorder) GetGameInfo(ctx, req interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, req}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGameInfo", reflect.TypeOf((*MockIClient)(nil).GetGameInfo), varargs...)
}

// GetRecentWinningRecords mocks base method.
func (m *MockIClient) GetRecentWinningRecords(ctx context.Context, req *cat_canteen.GetRecentWinningRecordsReq, opts ...grpc.CallOption) (*cat_canteen.GetRecentWinningRecordsResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, req}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetRecentWinningRecords", varargs...)
	ret0, _ := ret[0].(*cat_canteen.GetRecentWinningRecordsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRecentWinningRecords indicates an expected call of GetRecentWinningRecords.
func (mr *MockIClientMockRecorder) GetRecentWinningRecords(ctx, req interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, req}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRecentWinningRecords", reflect.TypeOf((*MockIClient)(nil).GetRecentWinningRecords), varargs...)
}

// GetResourcesCfg mocks base method.
func (m *MockIClient) GetResourcesCfg(ctx context.Context, in *cat_canteen.GetResourcesCfgReq, opts ...grpc.CallOption) (*cat_canteen.GetResourcesCfgResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetResourcesCfg", varargs...)
	ret0, _ := ret[0].(*cat_canteen.GetResourcesCfgResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetResourcesCfg indicates an expected call of GetResourcesCfg.
func (mr *MockIClientMockRecorder) GetResourcesCfg(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetResourcesCfg", reflect.TypeOf((*MockIClient)(nil).GetResourcesCfg), varargs...)
}

// GetUserPlayFile mocks base method.
func (m *MockIClient) GetUserPlayFile(ctx context.Context, req *cat_canteen.GetUserPlayFileReq, opts ...grpc.CallOption) (*cat_canteen.GetUserPlayFileResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, req}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetUserPlayFile", varargs...)
	ret0, _ := ret[0].(*cat_canteen.GetUserPlayFileResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserPlayFile indicates an expected call of GetUserPlayFile.
func (mr *MockIClientMockRecorder) GetUserPlayFile(ctx, req interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, req}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserPlayFile", reflect.TypeOf((*MockIClient)(nil).GetUserPlayFile), varargs...)
}

// GetUserProp mocks base method.
func (m *MockIClient) GetUserProp(ctx context.Context, in *cat_canteen.GetUserPropReq, opts ...grpc.CallOption) (*cat_canteen.GetUserPropResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetUserProp", varargs...)
	ret0, _ := ret[0].(*cat_canteen.GetUserPropResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserProp indicates an expected call of GetUserProp.
func (mr *MockIClientMockRecorder) GetUserProp(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserProp", reflect.TypeOf((*MockIClient)(nil).GetUserProp), varargs...)
}

// GetUserPropExpireDetail mocks base method.
func (m *MockIClient) GetUserPropExpireDetail(ctx context.Context, in *cat_canteen.GetUserPropExpireDetailReq, opts ...grpc.CallOption) (*cat_canteen.GetUserPropExpireDetailResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetUserPropExpireDetail", varargs...)
	ret0, _ := ret[0].(*cat_canteen.GetUserPropExpireDetailResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserPropExpireDetail indicates an expected call of GetUserPropExpireDetail.
func (mr *MockIClientMockRecorder) GetUserPropExpireDetail(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserPropExpireDetail", reflect.TypeOf((*MockIClient)(nil).GetUserPropExpireDetail), varargs...)
}

// GetUserWinningRecords mocks base method.
func (m *MockIClient) GetUserWinningRecords(ctx context.Context, req *cat_canteen.GetUserWinningRecordsReq, opts ...grpc.CallOption) (*cat_canteen.GetUserWinningRecordsResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, req}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetUserWinningRecords", varargs...)
	ret0, _ := ret[0].(*cat_canteen.GetUserWinningRecordsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserWinningRecords indicates an expected call of GetUserWinningRecords.
func (mr *MockIClientMockRecorder) GetUserWinningRecords(ctx, req interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, req}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserWinningRecords", reflect.TypeOf((*MockIClient)(nil).GetUserWinningRecords), varargs...)
}

// LotteryDraw mocks base method.
func (m *MockIClient) LotteryDraw(ctx context.Context, req *cat_canteen.LotteryDrawReq, opts ...grpc.CallOption) (*cat_canteen.LotteryDrawResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, req}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "LotteryDraw", varargs...)
	ret0, _ := ret[0].(*cat_canteen.LotteryDrawResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// LotteryDraw indicates an expected call of LotteryDraw.
func (mr *MockIClientMockRecorder) LotteryDraw(ctx, req interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, req}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "LotteryDraw", reflect.TypeOf((*MockIClient)(nil).LotteryDraw), varargs...)
}

// ReleaseFusing mocks base method.
func (m *MockIClient) ReleaseFusing(ctx context.Context, req *cat_canteen.ReleaseFusingReq, opts ...grpc.CallOption) (*cat_canteen.ReleaseFusingResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, req}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ReleaseFusing", varargs...)
	ret0, _ := ret[0].(*cat_canteen.ReleaseFusingResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ReleaseFusing indicates an expected call of ReleaseFusing.
func (mr *MockIClientMockRecorder) ReleaseFusing(ctx, req interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, req}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReleaseFusing", reflect.TypeOf((*MockIClient)(nil).ReleaseFusing), varargs...)
}

// ReportDayStats mocks base method.
func (m *MockIClient) ReportDayStats(ctx context.Context, req *cat_canteen.ReportStatsReq, opts ...grpc.CallOption) (*cat_canteen.ReportStatsResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, req}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ReportDayStats", varargs...)
	ret0, _ := ret[0].(*cat_canteen.ReportStatsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ReportDayStats indicates an expected call of ReportDayStats.
func (mr *MockIClientMockRecorder) ReportDayStats(ctx, req interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, req}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReportDayStats", reflect.TypeOf((*MockIClient)(nil).ReportDayStats), varargs...)
}

// ReportHourStats mocks base method.
func (m *MockIClient) ReportHourStats(ctx context.Context, req *cat_canteen.ReportStatsReq, opts ...grpc.CallOption) (*cat_canteen.ReportStatsResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, req}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ReportHourStats", varargs...)
	ret0, _ := ret[0].(*cat_canteen.ReportStatsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ReportHourStats indicates an expected call of ReportHourStats.
func (mr *MockIClientMockRecorder) ReportHourStats(ctx, req interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, req}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReportHourStats", reflect.TypeOf((*MockIClient)(nil).ReportHourStats), varargs...)
}

// RollbackBackPackCostOrder mocks base method.
func (m *MockIClient) RollbackBackPackCostOrder(ctx context.Context, req *reconcile_v2.ReplaceOrderReq, opts ...grpc.CallOption) (*reconcile_v2.EmptyResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, req}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "RollbackBackPackCostOrder", varargs...)
	ret0, _ := ret[0].(*reconcile_v2.EmptyResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RollbackBackPackCostOrder indicates an expected call of RollbackBackPackCostOrder.
func (mr *MockIClientMockRecorder) RollbackBackPackCostOrder(ctx, req interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, req}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RollbackBackPackCostOrder", reflect.TypeOf((*MockIClient)(nil).RollbackBackPackCostOrder), varargs...)
}

// SendBreakNews mocks base method.
func (m *MockIClient) SendBreakNews(ctx context.Context, req *cat_canteen.SendBreakNewsReq, opts ...grpc.CallOption) (*cat_canteen.SendBreakNewsResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, req}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SendBreakNews", varargs...)
	ret0, _ := ret[0].(*cat_canteen.SendBreakNewsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SendBreakNews indicates an expected call of SendBreakNews.
func (mr *MockIClientMockRecorder) SendBreakNews(ctx, req interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, req}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendBreakNews", reflect.TypeOf((*MockIClient)(nil).SendBreakNews), varargs...)
}

// SetCatCanteenLimitConf mocks base method.
func (m *MockIClient) SetCatCanteenLimitConf(ctx context.Context, req *cat_canteen.SetCatCanteenLimitConfReq, opts ...grpc.CallOption) (*cat_canteen.SetCatCanteenLimitConfResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, req}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SetCatCanteenLimitConf", varargs...)
	ret0, _ := ret[0].(*cat_canteen.SetCatCanteenLimitConfResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetCatCanteenLimitConf indicates an expected call of SetCatCanteenLimitConf.
func (mr *MockIClientMockRecorder) SetCatCanteenLimitConf(ctx, req interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, req}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetCatCanteenLimitConf", reflect.TypeOf((*MockIClient)(nil).SetCatCanteenLimitConf), varargs...)
}

// SetLevelConf mocks base method.
func (m *MockIClient) SetLevelConf(ctx context.Context, req *cat_canteen.SetLevelConfReq, opts ...grpc.CallOption) (*cat_canteen.SetLevelConfResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, req}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SetLevelConf", varargs...)
	ret0, _ := ret[0].(*cat_canteen.SetLevelConfResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetLevelConf indicates an expected call of SetLevelConf.
func (mr *MockIClientMockRecorder) SetLevelConf(ctx, req interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, req}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetLevelConf", reflect.TypeOf((*MockIClient)(nil).SetLevelConf), varargs...)
}

// SimulateWithPrizePool mocks base method.
func (m *MockIClient) SimulateWithPrizePool(ctx context.Context, req *cat_canteen.SimulateWithPrizePoolReq, opts ...grpc.CallOption) (*cat_canteen.SimulateWithPrizePoolResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, req}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SimulateWithPrizePool", varargs...)
	ret0, _ := ret[0].(*cat_canteen.SimulateWithPrizePoolResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SimulateWithPrizePool indicates an expected call of SimulateWithPrizePool.
func (mr *MockIClientMockRecorder) SimulateWithPrizePool(ctx, req interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, req}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SimulateWithPrizePool", reflect.TypeOf((*MockIClient)(nil).SimulateWithPrizePool), varargs...)
}

// Stub mocks base method.
func (m *MockIClient) Stub() interface{} {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Stub")
	ret0, _ := ret[0].(interface{})
	return ret0
}

// Stub indicates an expected call of Stub.
func (mr *MockIClientMockRecorder) Stub() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Stub", reflect.TypeOf((*MockIClient)(nil).Stub))
}

// UpdateCatLightEffect mocks base method.
func (m *MockIClient) UpdateCatLightEffect(ctx context.Context, req *cat_canteen.UpdateCatLightEffectReq, opts ...grpc.CallOption) (*cat_canteen.UpdateCatLightEffectResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, req}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateCatLightEffect", varargs...)
	ret0, _ := ret[0].(*cat_canteen.UpdateCatLightEffectResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateCatLightEffect indicates an expected call of UpdateCatLightEffect.
func (mr *MockIClientMockRecorder) UpdateCatLightEffect(ctx, req interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, req}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateCatLightEffect", reflect.TypeOf((*MockIClient)(nil).UpdateCatLightEffect), varargs...)
}

// UpdateCatLightEffectV2 mocks base method.
func (m *MockIClient) UpdateCatLightEffectV2(ctx context.Context, in *cat_canteen.UpdateCatLightEffectV2Req, opts ...grpc.CallOption) (*cat_canteen.UpdateCatLightEffectV2Resp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateCatLightEffectV2", varargs...)
	ret0, _ := ret[0].(*cat_canteen.UpdateCatLightEffectV2Resp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateCatLightEffectV2 indicates an expected call of UpdateCatLightEffectV2.
func (mr *MockIClientMockRecorder) UpdateCatLightEffectV2(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateCatLightEffectV2", reflect.TypeOf((*MockIClient)(nil).UpdateCatLightEffectV2), varargs...)
}
