// Code generated by MockGen. DO NOT EDIT.
// Source: /Users/<USER>/project/quicksilver/clients/sing-a-round/iclient.go

// Package singaround is a generated GoMock package.
package singaround

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	client "golang.52tt.com/pkg/client"
	protocol "golang.52tt.com/pkg/protocol"
	singaround "golang.52tt.com/protocol/services/singaround"
)

// MockIClient is a mock of IClient interface.
type MockIClient struct {
	ctrl     *gomock.Controller
	recorder *MockIClientMockRecorder
}

// MockIClientMockRecorder is the mock recorder for MockIClient.
type MockIClientMockRecorder struct {
	mock *MockIClient
}

// NewMockIClient creates a new mock instance.
func NewMockIClient(ctrl *gomock.Controller) *MockIClient {
	mock := &MockIClient{ctrl: ctrl}
	mock.recorder = &MockIClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIClient) EXPECT() *MockIClientMockRecorder {
	return m.recorder
}

// AccomplishLoadingRes mocks base method.
func (m *MockIClient) AccomplishLoadingRes(ctx context.Context, req *singaround.AccomplishLoadingResReq) (*singaround.AccomplishLoadingResResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AccomplishLoadingRes", ctx, req)
	ret0, _ := ret[0].(*singaround.AccomplishLoadingResResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// AccomplishLoadingRes indicates an expected call of AccomplishLoadingRes.
func (mr *MockIClientMockRecorder) AccomplishLoadingRes(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AccomplishLoadingRes", reflect.TypeOf((*MockIClient)(nil).AccomplishLoadingRes), ctx, req)
}

// AccomplishSingingGameSong mocks base method.
func (m *MockIClient) AccomplishSingingGameSong(ctx context.Context, req *singaround.AccomplishSingingGameSongReq) (*singaround.AccomplishSingingGameSongResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AccomplishSingingGameSong", ctx, req)
	ret0, _ := ret[0].(*singaround.AccomplishSingingGameSongResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// AccomplishSingingGameSong indicates an expected call of AccomplishSingingGameSong.
func (mr *MockIClientMockRecorder) AccomplishSingingGameSong(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AccomplishSingingGameSong", reflect.TypeOf((*MockIClient)(nil).AccomplishSingingGameSong), ctx, req)
}

// AnswerSingingHelp mocks base method.
func (m *MockIClient) AnswerSingingHelp(ctx context.Context, req *singaround.AnswerSingingHelpReq) (*singaround.AnswerSingingHelpResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AnswerSingingHelp", ctx, req)
	ret0, _ := ret[0].(*singaround.AnswerSingingHelpResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// AnswerSingingHelp indicates an expected call of AnswerSingingHelp.
func (mr *MockIClientMockRecorder) AnswerSingingHelp(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AnswerSingingHelp", reflect.TypeOf((*MockIClient)(nil).AnswerSingingHelp), ctx, req)
}

// AskForSingingHelp mocks base method.
func (m *MockIClient) AskForSingingHelp(ctx context.Context, req *singaround.AskForSingingHelpReq) (*singaround.AskForSingingHelpResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AskForSingingHelp", ctx, req)
	ret0, _ := ret[0].(*singaround.AskForSingingHelpResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// AskForSingingHelp indicates an expected call of AskForSingingHelp.
func (mr *MockIClientMockRecorder) AskForSingingHelp(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AskForSingingHelp", reflect.TypeOf((*MockIClient)(nil).AskForSingingHelp), ctx, req)
}

// CC mocks base method.
func (m *MockIClient) CC() client.Conn {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CC")
	ret0, _ := ret[0].(client.Conn)
	return ret0
}

// CC indicates an expected call of CC.
func (mr *MockIClientMockRecorder) CC() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CC", reflect.TypeOf((*MockIClient)(nil).CC))
}

// CancelSingingGamePreparation mocks base method.
func (m *MockIClient) CancelSingingGamePreparation(ctx context.Context, req *singaround.CancelSingingGamePreparationReq) (*singaround.CancelSingingGamePreparationResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CancelSingingGamePreparation", ctx, req)
	ret0, _ := ret[0].(*singaround.CancelSingingGamePreparationResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// CancelSingingGamePreparation indicates an expected call of CancelSingingGamePreparation.
func (mr *MockIClientMockRecorder) CancelSingingGamePreparation(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CancelSingingGamePreparation", reflect.TypeOf((*MockIClient)(nil).CancelSingingGamePreparation), ctx, req)
}

// Close mocks base method.
func (m *MockIClient) Close() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Close")
}

// Close indicates an expected call of Close.
func (mr *MockIClientMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockIClient)(nil).Close))
}

// ExpressSingingGameLike mocks base method.
func (m *MockIClient) ExpressSingingGameLike(ctx context.Context, req *singaround.ExpressSingingGameLikeReq) (*singaround.ExpressSingingGameLikeResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ExpressSingingGameLike", ctx, req)
	ret0, _ := ret[0].(*singaround.ExpressSingingGameLikeResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// ExpressSingingGameLike indicates an expected call of ExpressSingingGameLike.
func (mr *MockIClientMockRecorder) ExpressSingingGameLike(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ExpressSingingGameLike", reflect.TypeOf((*MockIClient)(nil).ExpressSingingGameLike), ctx, req)
}

// GetAllSingImageConf mocks base method.
func (m *MockIClient) GetAllSingImageConf(ctx context.Context, version uint32) (*singaround.GetAllSingImageConfResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllSingImageConf", ctx, version)
	ret0, _ := ret[0].(*singaround.GetAllSingImageConfResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetAllSingImageConf indicates an expected call of GetAllSingImageConf.
func (mr *MockIClientMockRecorder) GetAllSingImageConf(ctx, version interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllSingImageConf", reflect.TypeOf((*MockIClient)(nil).GetAllSingImageConf), ctx, version)
}

// GetEnableTagIds mocks base method.
func (m *MockIClient) GetEnableTagIds(ctx context.Context, req *singaround.GetEnableTagsReq) (*singaround.GetEnableTagsResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetEnableTagIds", ctx, req)
	ret0, _ := ret[0].(*singaround.GetEnableTagsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetEnableTagIds indicates an expected call of GetEnableTagIds.
func (mr *MockIClientMockRecorder) GetEnableTagIds(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetEnableTagIds", reflect.TypeOf((*MockIClient)(nil).GetEnableTagIds), ctx, req)
}

// GetLessUser mocks base method.
func (m *MockIClient) GetLessUser(ctx context.Context, req *singaround.GetLessUserReq) (*singaround.GetLessUserResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLessUser", ctx, req)
	ret0, _ := ret[0].(*singaround.GetLessUserResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetLessUser indicates an expected call of GetLessUser.
func (mr *MockIClientMockRecorder) GetLessUser(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLessUser", reflect.TypeOf((*MockIClient)(nil).GetLessUser), ctx, req)
}

// GetLessUserByMicMode mocks base method.
func (m *MockIClient) GetLessUserByMicMode(ctx context.Context, req *singaround.GetLessUserReq) (*singaround.GetLessUserResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLessUserByMicMode", ctx, req)
	ret0, _ := ret[0].(*singaround.GetLessUserResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetLessUserByMicMode indicates an expected call of GetLessUserByMicMode.
func (mr *MockIClientMockRecorder) GetLessUserByMicMode(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLessUserByMicMode", reflect.TypeOf((*MockIClient)(nil).GetLessUserByMicMode), ctx, req)
}

// GetSingGame mocks base method.
func (m *MockIClient) GetSingGame(ctx context.Context, req *singaround.GetSingingGameForAppReq) (*singaround.GetSingingGameForAppResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSingGame", ctx, req)
	ret0, _ := ret[0].(*singaround.GetSingingGameForAppResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetSingGame indicates an expected call of GetSingGame.
func (mr *MockIClientMockRecorder) GetSingGame(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSingGame", reflect.TypeOf((*MockIClient)(nil).GetSingGame), ctx, req)
}

// GetSingingGameChannelInfo mocks base method.
func (m *MockIClient) GetSingingGameChannelInfo(ctx context.Context, req *singaround.GetSingingGameChannelInfoReq) (*singaround.GetSingingGameChannelInfoResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSingingGameChannelInfo", ctx, req)
	ret0, _ := ret[0].(*singaround.GetSingingGameChannelInfoResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetSingingGameChannelInfo indicates an expected call of GetSingingGameChannelInfo.
func (mr *MockIClientMockRecorder) GetSingingGameChannelInfo(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSingingGameChannelInfo", reflect.TypeOf((*MockIClient)(nil).GetSingingGameChannelInfo), ctx, req)
}

// GetSingingGameCountdown mocks base method.
func (m *MockIClient) GetSingingGameCountdown(ctx context.Context, req *singaround.GetSingingGameCountdownReq) (*singaround.GetSingingGameCountdownResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSingingGameCountdown", ctx, req)
	ret0, _ := ret[0].(*singaround.GetSingingGameCountdownResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetSingingGameCountdown indicates an expected call of GetSingingGameCountdown.
func (mr *MockIClientMockRecorder) GetSingingGameCountdown(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSingingGameCountdown", reflect.TypeOf((*MockIClient)(nil).GetSingingGameCountdown), ctx, req)
}

// GetSingingGameRankList mocks base method.
func (m *MockIClient) GetSingingGameRankList(ctx context.Context, req *singaround.GetSingingGameRankListReq) (*singaround.GetSingingGameRankListResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSingingGameRankList", ctx, req)
	ret0, _ := ret[0].(*singaround.GetSingingGameRankListResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetSingingGameRankList indicates an expected call of GetSingingGameRankList.
func (mr *MockIClientMockRecorder) GetSingingGameRankList(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSingingGameRankList", reflect.TypeOf((*MockIClient)(nil).GetSingingGameRankList), ctx, req)
}

// GetSingingGameRecordList mocks base method.
func (m *MockIClient) GetSingingGameRecordList(ctx context.Context, req *singaround.GetSingingGameRecordListReq) (*singaround.GetSingingGameRecordListResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSingingGameRecordList", ctx, req)
	ret0, _ := ret[0].(*singaround.GetSingingGameRecordListResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetSingingGameRecordList indicates an expected call of GetSingingGameRecordList.
func (mr *MockIClientMockRecorder) GetSingingGameRecordList(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSingingGameRecordList", reflect.TypeOf((*MockIClient)(nil).GetSingingGameRecordList), ctx, req)
}

// GetSingingGameRoundInfo mocks base method.
func (m *MockIClient) GetSingingGameRoundInfo(ctx context.Context, req *singaround.GetSingingGameRoundInfoReq) (*singaround.GetSingingGameRoundInfoResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSingingGameRoundInfo", ctx, req)
	ret0, _ := ret[0].(*singaround.GetSingingGameRoundInfoResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetSingingGameRoundInfo indicates an expected call of GetSingingGameRoundInfo.
func (mr *MockIClientMockRecorder) GetSingingGameRoundInfo(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSingingGameRoundInfo", reflect.TypeOf((*MockIClient)(nil).GetSingingGameRoundInfo), ctx, req)
}

// GetSingingGameUserInfo mocks base method.
func (m *MockIClient) GetSingingGameUserInfo(ctx context.Context, uid uint32) (*singaround.GetSingingGameUserInfoResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSingingGameUserInfo", ctx, uid)
	ret0, _ := ret[0].(*singaround.GetSingingGameUserInfoResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetSingingGameUserInfo indicates an expected call of GetSingingGameUserInfo.
func (mr *MockIClientMockRecorder) GetSingingGameUserInfo(ctx, uid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSingingGameUserInfo", reflect.TypeOf((*MockIClient)(nil).GetSingingGameUserInfo), ctx, uid)
}

// GetSongForApp mocks base method.
func (m *MockIClient) GetSongForApp(ctx context.Context, tag, cnt uint32) (*singaround.GetSongForAppResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSongForApp", ctx, tag, cnt)
	ret0, _ := ret[0].(*singaround.GetSongForAppResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetSongForApp indicates an expected call of GetSongForApp.
func (mr *MockIClientMockRecorder) GetSongForApp(ctx, tag, cnt interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSongForApp", reflect.TypeOf((*MockIClient)(nil).GetSongForApp), ctx, tag, cnt)
}

// GetUserSingImage mocks base method.
func (m *MockIClient) GetUserSingImage(ctx context.Context, uid uint32) (*singaround.GetUserSingImageResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserSingImage", ctx, uid)
	ret0, _ := ret[0].(*singaround.GetUserSingImageResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetUserSingImage indicates an expected call of GetUserSingImage.
func (mr *MockIClientMockRecorder) GetUserSingImage(ctx, uid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserSingImage", reflect.TypeOf((*MockIClient)(nil).GetUserSingImage), ctx, uid)
}

// GrabSingingGameMic mocks base method.
func (m *MockIClient) GrabSingingGameMic(ctx context.Context, req *singaround.GrabSingingGameMicReq) (*singaround.GrabSingingGameMicResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GrabSingingGameMic", ctx, req)
	ret0, _ := ret[0].(*singaround.GrabSingingGameMicResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GrabSingingGameMic indicates an expected call of GrabSingingGameMic.
func (mr *MockIClientMockRecorder) GrabSingingGameMic(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GrabSingingGameMic", reflect.TypeOf((*MockIClient)(nil).GrabSingingGameMic), ctx, req)
}

// JoinSingingGame mocks base method.
func (m *MockIClient) JoinSingingGame(ctx context.Context, req *singaround.JoinSingingGameReq) (*singaround.JoinSingingGameResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "JoinSingingGame", ctx, req)
	ret0, _ := ret[0].(*singaround.JoinSingingGameResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// JoinSingingGame indicates an expected call of JoinSingingGame.
func (mr *MockIClientMockRecorder) JoinSingingGame(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "JoinSingingGame", reflect.TypeOf((*MockIClient)(nil).JoinSingingGame), ctx, req)
}

// ReportSingingGameSongScore mocks base method.
func (m *MockIClient) ReportSingingGameSongScore(ctx context.Context, req *singaround.ReportSingingGameSongScoreReq) (*singaround.ReportSingingGameSongScoreResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ReportSingingGameSongScore", ctx, req)
	ret0, _ := ret[0].(*singaround.ReportSingingGameSongScoreResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// ReportSingingGameSongScore indicates an expected call of ReportSingingGameSongScore.
func (mr *MockIClientMockRecorder) ReportSingingGameSongScore(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReportSingingGameSongScore", reflect.TypeOf((*MockIClient)(nil).ReportSingingGameSongScore), ctx, req)
}

// SetUserSingImage mocks base method.
func (m *MockIClient) SetUserSingImage(ctx context.Context, req *singaround.SetUserSingImageReq) (*singaround.SetUserSingImageResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetUserSingImage", ctx, req)
	ret0, _ := ret[0].(*singaround.SetUserSingImageResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// SetUserSingImage indicates an expected call of SetUserSingImage.
func (mr *MockIClientMockRecorder) SetUserSingImage(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetUserSingImage", reflect.TypeOf((*MockIClient)(nil).SetUserSingImage), ctx, req)
}

// SingingGameFistBump mocks base method.
func (m *MockIClient) SingingGameFistBump(ctx context.Context, req *singaround.SingingGameFistBumpReq) (*singaround.SingingGameFistBumpResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SingingGameFistBump", ctx, req)
	ret0, _ := ret[0].(*singaround.SingingGameFistBumpResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// SingingGameFistBump indicates an expected call of SingingGameFistBump.
func (mr *MockIClientMockRecorder) SingingGameFistBump(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SingingGameFistBump", reflect.TypeOf((*MockIClient)(nil).SingingGameFistBump), ctx, req)
}

// SingingGameVote mocks base method.
func (m *MockIClient) SingingGameVote(ctx context.Context, req *singaround.SingingGameVoteReq) (*singaround.SingingGameVoteResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SingingGameVote", ctx, req)
	ret0, _ := ret[0].(*singaround.SingingGameVoteResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// SingingGameVote indicates an expected call of SingingGameVote.
func (mr *MockIClientMockRecorder) SingingGameVote(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SingingGameVote", reflect.TypeOf((*MockIClient)(nil).SingingGameVote), ctx, req)
}

// StartSingingGame mocks base method.
func (m *MockIClient) StartSingingGame(ctx context.Context, req *singaround.StartSingingGameReq) (*singaround.StartSingingGameResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "StartSingingGame", ctx, req)
	ret0, _ := ret[0].(*singaround.StartSingingGameResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// StartSingingGame indicates an expected call of StartSingingGame.
func (mr *MockIClientMockRecorder) StartSingingGame(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "StartSingingGame", reflect.TypeOf((*MockIClient)(nil).StartSingingGame), ctx, req)
}

// StartSingingGameMatching mocks base method.
func (m *MockIClient) StartSingingGameMatching(ctx context.Context, req *singaround.StartSingingGameMatchingReq) (*singaround.StartSingingGameMatchingResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "StartSingingGameMatching", ctx, req)
	ret0, _ := ret[0].(*singaround.StartSingingGameMatchingResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// StartSingingGameMatching indicates an expected call of StartSingingGameMatching.
func (mr *MockIClientMockRecorder) StartSingingGameMatching(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "StartSingingGameMatching", reflect.TypeOf((*MockIClient)(nil).StartSingingGameMatching), ctx, req)
}

// StartUgcChannelSingingGame mocks base method.
func (m *MockIClient) StartUgcChannelSingingGame(ctx context.Context, req *singaround.StartUgcChannelSingingGameReq) (*singaround.StartUgcChannelSingingGameResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "StartUgcChannelSingingGame", ctx, req)
	ret0, _ := ret[0].(*singaround.StartUgcChannelSingingGameResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// StartUgcChannelSingingGame indicates an expected call of StartUgcChannelSingingGame.
func (mr *MockIClientMockRecorder) StartUgcChannelSingingGame(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "StartUgcChannelSingingGame", reflect.TypeOf((*MockIClient)(nil).StartUgcChannelSingingGame), ctx, req)
}

// Stub mocks base method.
func (m *MockIClient) Stub() interface{} {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Stub")
	ret0, _ := ret[0].(interface{})
	return ret0
}

// Stub indicates an expected call of Stub.
func (mr *MockIClientMockRecorder) Stub() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Stub", reflect.TypeOf((*MockIClient)(nil).Stub))
}

// SwitchSingingGameType mocks base method.
func (m *MockIClient) SwitchSingingGameType(ctx context.Context, req *singaround.SwitchSingingGameTypeReq) (*singaround.SwitchSingingGameTypeResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SwitchSingingGameType", ctx, req)
	ret0, _ := ret[0].(*singaround.SwitchSingingGameTypeResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// SwitchSingingGameType indicates an expected call of SwitchSingingGameType.
func (mr *MockIClientMockRecorder) SwitchSingingGameType(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SwitchSingingGameType", reflect.TypeOf((*MockIClient)(nil).SwitchSingingGameType), ctx, req)
}
