// Code generated by MockGen. DO NOT EDIT.
// Source: E:\git\quicksilver\clients\channelguild-go\iclient.go

// Package channelguild_go is a generated GoMock package.
package channelguild_go

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	client "golang.52tt.com/pkg/client"
	protocol "golang.52tt.com/pkg/protocol"
	channelguild_go "golang.52tt.com/protocol/services/channelguild-go"
)

// MockIClient is a mock of IClient interface.
type MockIClient struct {
	ctrl     *gomock.Controller
	recorder *MockIClientMockRecorder
}

// MockIClientMockRecorder is the mock recorder for MockIClient.
type MockIClientMockRecorder struct {
	mock *MockIClient
}

// NewMockIClient creates a new mock instance.
func NewMockIClient(ctrl *gomock.Controller) *MockIClient {
	mock := &MockIClient{ctrl: ctrl}
	mock.recorder = &MockIClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIClient) EXPECT() *MockIClientMockRecorder {
	return m.recorder
}

// AddChannelGuild mocks base method.
func (m *MockIClient) AddChannelGuild(ctx context.Context, guildId, channelId, channelType uint32) protocol.ServerError {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddChannelGuild", ctx, guildId, channelId, channelType)
	ret0, _ := ret[0].(protocol.ServerError)
	return ret0
}

// AddChannelGuild indicates an expected call of AddChannelGuild.
func (mr *MockIClientMockRecorder) AddChannelGuild(ctx, guildId, channelId, channelType interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddChannelGuild", reflect.TypeOf((*MockIClient)(nil).AddChannelGuild), ctx, guildId, channelId, channelType)
}

// AddGuildMemberChannel mocks base method.
func (m *MockIClient) AddGuildMemberChannel(ctx context.Context, guildId, channelId uint32, account string) protocol.ServerError {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddGuildMemberChannel", ctx, guildId, channelId, account)
	ret0, _ := ret[0].(protocol.ServerError)
	return ret0
}

// AddGuildMemberChannel indicates an expected call of AddGuildMemberChannel.
func (mr *MockIClientMockRecorder) AddGuildMemberChannel(ctx, guildId, channelId, account interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddGuildMemberChannel", reflect.TypeOf((*MockIClient)(nil).AddGuildMemberChannel), ctx, guildId, channelId, account)
}

// AddPartnerChannelGuild mocks base method.
func (m *MockIClient) AddPartnerChannelGuild(ctx context.Context, guildId, channelId uint32) protocol.ServerError {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddPartnerChannelGuild", ctx, guildId, channelId)
	ret0, _ := ret[0].(protocol.ServerError)
	return ret0
}

// AddPartnerChannelGuild indicates an expected call of AddPartnerChannelGuild.
func (mr *MockIClientMockRecorder) AddPartnerChannelGuild(ctx, guildId, channelId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddPartnerChannelGuild", reflect.TypeOf((*MockIClient)(nil).AddPartnerChannelGuild), ctx, guildId, channelId)
}

// BatGetChannelGuildList mocks base method.
func (m *MockIClient) BatGetChannelGuildList(ctx context.Context, guildIds []uint32, channelType uint32) ([]*channelguild_go.BatGetChannelGuildListEntry, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatGetChannelGuildList", ctx, guildIds, channelType)
	ret0, _ := ret[0].([]*channelguild_go.BatGetChannelGuildListEntry)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// BatGetChannelGuildList indicates an expected call of BatGetChannelGuildList.
func (mr *MockIClientMockRecorder) BatGetChannelGuildList(ctx, guildIds, channelType interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatGetChannelGuildList", reflect.TypeOf((*MockIClient)(nil).BatGetChannelGuildList), ctx, guildIds, channelType)
}

// BatGetChannelGuildMap mocks base method.
func (m *MockIClient) BatGetChannelGuildMap(ctx context.Context, guildIds []uint32, channelType uint32) (map[uint32]*channelguild_go.BatGetChannelGuildListEntry, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatGetChannelGuildMap", ctx, guildIds, channelType)
	ret0, _ := ret[0].(map[uint32]*channelguild_go.BatGetChannelGuildListEntry)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// BatGetChannelGuildMap indicates an expected call of BatGetChannelGuildMap.
func (mr *MockIClientMockRecorder) BatGetChannelGuildMap(ctx, guildIds, channelType interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatGetChannelGuildMap", reflect.TypeOf((*MockIClient)(nil).BatGetChannelGuildMap), ctx, guildIds, channelType)
}

// CC mocks base method.
func (m *MockIClient) CC() client.Conn {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CC")
	ret0, _ := ret[0].(client.Conn)
	return ret0
}

// CC indicates an expected call of CC.
func (mr *MockIClientMockRecorder) CC() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CC", reflect.TypeOf((*MockIClient)(nil).CC))
}

// Close mocks base method.
func (m *MockIClient) Close() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Close")
}

// Close indicates an expected call of Close.
func (mr *MockIClientMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockIClient)(nil).Close))
}

// CreateGuildPubChannel mocks base method.
func (m *MockIClient) CreateGuildPubChannel(ctx context.Context, guildId, uid uint32) protocol.ServerError {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateGuildPubChannel", ctx, guildId, uid)
	ret0, _ := ret[0].(protocol.ServerError)
	return ret0
}

// CreateGuildPubChannel indicates an expected call of CreateGuildPubChannel.
func (mr *MockIClientMockRecorder) CreateGuildPubChannel(ctx, guildId, uid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateGuildPubChannel", reflect.TypeOf((*MockIClient)(nil).CreateGuildPubChannel), ctx, guildId, uid)
}

// DelChannelGuild mocks base method.
func (m *MockIClient) DelChannelGuild(ctx context.Context, guildId, channelId uint32) protocol.ServerError {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelChannelGuild", ctx, guildId, channelId)
	ret0, _ := ret[0].(protocol.ServerError)
	return ret0
}

// DelChannelGuild indicates an expected call of DelChannelGuild.
func (mr *MockIClientMockRecorder) DelChannelGuild(ctx, guildId, channelId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelChannelGuild", reflect.TypeOf((*MockIClient)(nil).DelChannelGuild), ctx, guildId, channelId)
}

// DelGuildMemberChannel mocks base method.
func (m *MockIClient) DelGuildMemberChannel(ctx context.Context, guildId, channelId uint32, account string) protocol.ServerError {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelGuildMemberChannel", ctx, guildId, channelId, account)
	ret0, _ := ret[0].(protocol.ServerError)
	return ret0
}

// DelGuildMemberChannel indicates an expected call of DelGuildMemberChannel.
func (mr *MockIClientMockRecorder) DelGuildMemberChannel(ctx, guildId, channelId, account interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelGuildMemberChannel", reflect.TypeOf((*MockIClient)(nil).DelGuildMemberChannel), ctx, guildId, channelId, account)
}

// DelPartnerChannelGuild mocks base method.
func (m *MockIClient) DelPartnerChannelGuild(ctx context.Context, guildId, channelId uint32) protocol.ServerError {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelPartnerChannelGuild", ctx, guildId, channelId)
	ret0, _ := ret[0].(protocol.ServerError)
	return ret0
}

// DelPartnerChannelGuild indicates an expected call of DelPartnerChannelGuild.
func (mr *MockIClientMockRecorder) DelPartnerChannelGuild(ctx, guildId, channelId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelPartnerChannelGuild", reflect.TypeOf((*MockIClient)(nil).DelPartnerChannelGuild), ctx, guildId, channelId)
}

// DismissChannelGuild mocks base method.
func (m *MockIClient) DismissChannelGuild(ctx context.Context, guildId uint32) ([]uint32, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DismissChannelGuild", ctx, guildId)
	ret0, _ := ret[0].([]uint32)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// DismissChannelGuild indicates an expected call of DismissChannelGuild.
func (mr *MockIClientMockRecorder) DismissChannelGuild(ctx, guildId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DismissChannelGuild", reflect.TypeOf((*MockIClient)(nil).DismissChannelGuild), ctx, guildId)
}

// GetChannelGuildList mocks base method.
func (m *MockIClient) GetChannelGuildList(ctx context.Context, guildId, channelType uint32) ([]uint32, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChannelGuildList", ctx, guildId, channelType)
	ret0, _ := ret[0].([]uint32)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetChannelGuildList indicates an expected call of GetChannelGuildList.
func (mr *MockIClientMockRecorder) GetChannelGuildList(ctx, guildId, channelType interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChannelGuildList", reflect.TypeOf((*MockIClient)(nil).GetChannelGuildList), ctx, guildId, channelType)
}

// GetChannelGuildTotal mocks base method.
func (m *MockIClient) GetChannelGuildTotal(ctx context.Context, guildId, channelType uint32) (uint32, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChannelGuildTotal", ctx, guildId, channelType)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetChannelGuildTotal indicates an expected call of GetChannelGuildTotal.
func (mr *MockIClientMockRecorder) GetChannelGuildTotal(ctx, guildId, channelType interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChannelGuildTotal", reflect.TypeOf((*MockIClient)(nil).GetChannelGuildTotal), ctx, guildId, channelType)
}

// GetPartnerChannelGuild mocks base method.
func (m *MockIClient) GetPartnerChannelGuild(ctx context.Context) ([]*channelguild_go.PartnerChannelGuildInfo, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPartnerChannelGuild", ctx)
	ret0, _ := ret[0].([]*channelguild_go.PartnerChannelGuildInfo)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetPartnerChannelGuild indicates an expected call of GetPartnerChannelGuild.
func (mr *MockIClientMockRecorder) GetPartnerChannelGuild(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPartnerChannelGuild", reflect.TypeOf((*MockIClient)(nil).GetPartnerChannelGuild), ctx)
}

// GetPartnerChannelGuildByGuildId mocks base method.
func (m *MockIClient) GetPartnerChannelGuildByGuildId(ctx context.Context, guildId uint32) ([]uint32, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPartnerChannelGuildByGuildId", ctx, guildId)
	ret0, _ := ret[0].([]uint32)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetPartnerChannelGuildByGuildId indicates an expected call of GetPartnerChannelGuildByGuildId.
func (mr *MockIClientMockRecorder) GetPartnerChannelGuildByGuildId(ctx, guildId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPartnerChannelGuildByGuildId", reflect.TypeOf((*MockIClient)(nil).GetPartnerChannelGuildByGuildId), ctx, guildId)
}

// ListGuildPubChannel mocks base method.
func (m *MockIClient) ListGuildPubChannel(ctx context.Context, in *channelguild_go.ListGuildPubChannelReq) (*channelguild_go.ListChannelGuildResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListGuildPubChannel", ctx, in)
	ret0, _ := ret[0].(*channelguild_go.ListChannelGuildResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// ListGuildPubChannel indicates an expected call of ListGuildPubChannel.
func (mr *MockIClientMockRecorder) ListGuildPubChannel(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListGuildPubChannel", reflect.TypeOf((*MockIClient)(nil).ListGuildPubChannel), ctx, in)
}

// Stub mocks base method.
func (m *MockIClient) Stub() interface{} {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Stub")
	ret0, _ := ret[0].(interface{})
	return ret0
}

// Stub indicates an expected call of Stub.
func (mr *MockIClientMockRecorder) Stub() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Stub", reflect.TypeOf((*MockIClient)(nil).Stub))
}
