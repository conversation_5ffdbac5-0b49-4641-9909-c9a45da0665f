// Code generated by MockGen. DO NOT EDIT.
// Source: /Users/<USER>/project/quicksilver/clients/gameradar/iclient.go

// Package gameradar is a generated GoMock package.
package gameradar

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	client "golang.52tt.com/pkg/client"
	protocol "golang.52tt.com/pkg/protocol"
	gameradar "golang.52tt.com/protocol/services/gameradar"
)

// MockIClient is a mock of IClient interface.
type MockIClient struct {
	ctrl     *gomock.Controller
	recorder *MockIClientMockRecorder
}

// MockIClientMockRecorder is the mock recorder for MockIClient.
type MockIClientMockRecorder struct {
	mock *MockIClient
}

// NewMockIClient creates a new mock instance.
func NewMockIClient(ctrl *gomock.Controller) *MockIClient {
	mock := &MockIClient{ctrl: ctrl}
	mock.recorder = &MockIClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIClient) EXPECT() *MockIClientMockRecorder {
	return m.recorder
}

// BatchGetUserRadarStatus mocks base method.
func (m *MockIClient) BatchGetUserRadarStatus(ctx context.Context, uids []uint32) (*gameradar.BatchGetUserRadarStatusResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetUserRadarStatus", ctx, uids)
	ret0, _ := ret[0].(*gameradar.BatchGetUserRadarStatusResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// BatchGetUserRadarStatus indicates an expected call of BatchGetUserRadarStatus.
func (mr *MockIClientMockRecorder) BatchGetUserRadarStatus(ctx, uids interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetUserRadarStatus", reflect.TypeOf((*MockIClient)(nil).BatchGetUserRadarStatus), ctx, uids)
}

// Blocks mocks base method.
func (m *MockIClient) Blocks(ctx context.Context, tabId, tagId uint32) ([]*gameradar.Block, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Blocks", ctx, tabId, tagId)
	ret0, _ := ret[0].([]*gameradar.Block)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// Blocks indicates an expected call of Blocks.
func (mr *MockIClientMockRecorder) Blocks(ctx, tabId, tagId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Blocks", reflect.TypeOf((*MockIClient)(nil).Blocks), ctx, tabId, tagId)
}

// CC mocks base method.
func (m *MockIClient) CC() client.Conn {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CC")
	ret0, _ := ret[0].(client.Conn)
	return ret0
}

// CC indicates an expected call of CC.
func (mr *MockIClientMockRecorder) CC() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CC", reflect.TypeOf((*MockIClient)(nil).CC))
}

// Close mocks base method.
func (m *MockIClient) Close() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Close")
}

// Close indicates an expected call of Close.
func (mr *MockIClientMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockIClient)(nil).Close))
}

// DelInviteInfo mocks base method.
func (m *MockIClient) DelInviteInfo(ctx context.Context, req gameradar.DelInviteInfoReq) (*gameradar.DelInviteInfoResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelInviteInfo", ctx, req)
	ret0, _ := ret[0].(*gameradar.DelInviteInfoResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// DelInviteInfo indicates an expected call of DelInviteInfo.
func (mr *MockIClientMockRecorder) DelInviteInfo(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelInviteInfo", reflect.TypeOf((*MockIClient)(nil).DelInviteInfo), ctx, req)
}

// GetAllUserInRadarOpening mocks base method.
func (m *MockIClient) GetAllUserInRadarOpening(ctx context.Context) (*gameradar.GetAllUserInRadarOpeningResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllUserInRadarOpening", ctx)
	ret0, _ := ret[0].(*gameradar.GetAllUserInRadarOpeningResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetAllUserInRadarOpening indicates an expected call of GetAllUserInRadarOpening.
func (mr *MockIClientMockRecorder) GetAllUserInRadarOpening(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllUserInRadarOpening", reflect.TypeOf((*MockIClient)(nil).GetAllUserInRadarOpening), ctx)
}

// GetAllUserInRadarOpeningV2 mocks base method.
func (m *MockIClient) GetAllUserInRadarOpeningV2(ctx context.Context) (*gameradar.GetAllUserInRadarOpeningV2Resp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllUserInRadarOpeningV2", ctx)
	ret0, _ := ret[0].(*gameradar.GetAllUserInRadarOpeningV2Resp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetAllUserInRadarOpeningV2 indicates an expected call of GetAllUserInRadarOpeningV2.
func (mr *MockIClientMockRecorder) GetAllUserInRadarOpeningV2(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllUserInRadarOpeningV2", reflect.TypeOf((*MockIClient)(nil).GetAllUserInRadarOpeningV2), ctx)
}

// GetGetRadarIconConfig mocks base method.
func (m *MockIClient) GetGetRadarIconConfig(ctx context.Context) (*gameradar.GetRadarIconConfigResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGetRadarIconConfig", ctx)
	ret0, _ := ret[0].(*gameradar.GetRadarIconConfigResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetGetRadarIconConfig indicates an expected call of GetGetRadarIconConfig.
func (mr *MockIClientMockRecorder) GetGetRadarIconConfig(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGetRadarIconConfig", reflect.TypeOf((*MockIClient)(nil).GetGetRadarIconConfig), ctx)
}

// GetHasInviteInfo mocks base method.
func (m *MockIClient) GetHasInviteInfo(ctx context.Context, req gameradar.GetHasInviteReq) (*gameradar.GetHasInviteRsp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetHasInviteInfo", ctx, req)
	ret0, _ := ret[0].(*gameradar.GetHasInviteRsp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetHasInviteInfo indicates an expected call of GetHasInviteInfo.
func (mr *MockIClientMockRecorder) GetHasInviteInfo(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetHasInviteInfo", reflect.TypeOf((*MockIClient)(nil).GetHasInviteInfo), ctx, req)
}

// GetInviteInfo mocks base method.
func (m *MockIClient) GetInviteInfo(ctx context.Context, req gameradar.InviteInfoReq) (*gameradar.InviteInfoRsp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetInviteInfo", ctx, req)
	ret0, _ := ret[0].(*gameradar.InviteInfoRsp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetInviteInfo indicates an expected call of GetInviteInfo.
func (mr *MockIClientMockRecorder) GetInviteInfo(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetInviteInfo", reflect.TypeOf((*MockIClient)(nil).GetInviteInfo), ctx, req)
}

// GetRadarConfig mocks base method.
func (m *MockIClient) GetRadarConfig(ctx context.Context, req gameradar.GetRadarConfigReq) (*gameradar.GetRadarConfigResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRadarConfig", ctx, req)
	ret0, _ := ret[0].(*gameradar.GetRadarConfigResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetRadarConfig indicates an expected call of GetRadarConfig.
func (mr *MockIClientMockRecorder) GetRadarConfig(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRadarConfig", reflect.TypeOf((*MockIClient)(nil).GetRadarConfig), ctx, req)
}

// GetTotalOpenUserNum mocks base method.
func (m *MockIClient) GetTotalOpenUserNum(ctx context.Context) (uint32, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTotalOpenUserNum", ctx)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetTotalOpenUserNum indicates an expected call of GetTotalOpenUserNum.
func (mr *MockIClientMockRecorder) GetTotalOpenUserNum(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTotalOpenUserNum", reflect.TypeOf((*MockIClient)(nil).GetTotalOpenUserNum), ctx)
}

// GetUserChannelPushInfo mocks base method.
func (m *MockIClient) GetUserChannelPushInfo(ctx context.Context, uid uint32) (*gameradar.GetUserChannelPushInfoResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserChannelPushInfo", ctx, uid)
	ret0, _ := ret[0].(*gameradar.GetUserChannelPushInfoResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetUserChannelPushInfo indicates an expected call of GetUserChannelPushInfo.
func (mr *MockIClientMockRecorder) GetUserChannelPushInfo(ctx, uid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserChannelPushInfo", reflect.TypeOf((*MockIClient)(nil).GetUserChannelPushInfo), ctx, uid)
}

// GetUserRadarStatus mocks base method.
func (m *MockIClient) GetUserRadarStatus(ctx context.Context, uid uint32) (*gameradar.GetUserRadarStatusResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserRadarStatus", ctx, uid)
	ret0, _ := ret[0].(*gameradar.GetUserRadarStatusResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetUserRadarStatus indicates an expected call of GetUserRadarStatus.
func (mr *MockIClientMockRecorder) GetUserRadarStatus(ctx, uid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserRadarStatus", reflect.TypeOf((*MockIClient)(nil).GetUserRadarStatus), ctx, uid)
}

// GetValidOpenRadarUser mocks base method.
func (m *MockIClient) GetValidOpenRadarUser(ctx context.Context, req gameradar.GetValidOpenRadarUserReq) (*gameradar.GetValidOpenRadarUserResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetValidOpenRadarUser", ctx, req)
	ret0, _ := ret[0].(*gameradar.GetValidOpenRadarUserResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetValidOpenRadarUser indicates an expected call of GetValidOpenRadarUser.
func (mr *MockIClientMockRecorder) GetValidOpenRadarUser(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetValidOpenRadarUser", reflect.TypeOf((*MockIClient)(nil).GetValidOpenRadarUser), ctx, req)
}

// InvitePlay mocks base method.
func (m *MockIClient) InvitePlay(ctx context.Context, req gameradar.InvitePlayReq) (*gameradar.InvitePlayRsp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InvitePlay", ctx, req)
	ret0, _ := ret[0].(*gameradar.InvitePlayRsp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// InvitePlay indicates an expected call of InvitePlay.
func (mr *MockIClientMockRecorder) InvitePlay(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InvitePlay", reflect.TypeOf((*MockIClient)(nil).InvitePlay), ctx, req)
}

// InviteSuc mocks base method.
func (m *MockIClient) InviteSuc(ctx context.Context, req gameradar.InviteSucReq) (*gameradar.InviteSucRsp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InviteSuc", ctx, req)
	ret0, _ := ret[0].(*gameradar.InviteSucRsp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// InviteSuc indicates an expected call of InviteSuc.
func (mr *MockIClientMockRecorder) InviteSuc(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InviteSuc", reflect.TypeOf((*MockIClient)(nil).InviteSuc), ctx, req)
}

// RadarDisplay mocks base method.
func (m *MockIClient) RadarDisplay(ctx context.Context, req gameradar.RadarDisplayReq) (*gameradar.RadarDisplayRsp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RadarDisplay", ctx, req)
	ret0, _ := ret[0].(*gameradar.RadarDisplayRsp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// RadarDisplay indicates an expected call of RadarDisplay.
func (mr *MockIClientMockRecorder) RadarDisplay(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RadarDisplay", reflect.TypeOf((*MockIClient)(nil).RadarDisplay), ctx, req)
}

// StartRadar mocks base method.
func (m *MockIClient) StartRadar(ctx context.Context, req gameradar.StartRadarReq) (*gameradar.StartRadarResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "StartRadar", ctx, req)
	ret0, _ := ret[0].(*gameradar.StartRadarResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// StartRadar indicates an expected call of StartRadar.
func (mr *MockIClientMockRecorder) StartRadar(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "StartRadar", reflect.TypeOf((*MockIClient)(nil).StartRadar), ctx, req)
}

// StopRadar mocks base method.
func (m *MockIClient) StopRadar(ctx context.Context, req gameradar.StopRadarReq) (*gameradar.StopRadarResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "StopRadar", ctx, req)
	ret0, _ := ret[0].(*gameradar.StopRadarResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// StopRadar indicates an expected call of StopRadar.
func (mr *MockIClientMockRecorder) StopRadar(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "StopRadar", reflect.TypeOf((*MockIClient)(nil).StopRadar), ctx, req)
}

// Stub mocks base method.
func (m *MockIClient) Stub() interface{} {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Stub")
	ret0, _ := ret[0].(interface{})
	return ret0
}

// Stub indicates an expected call of Stub.
func (mr *MockIClientMockRecorder) Stub() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Stub", reflect.TypeOf((*MockIClient)(nil).Stub))
}

// Tabs mocks base method.
func (m *MockIClient) Tabs(ctx context.Context) ([]*gameradar.Tab, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Tabs", ctx)
	ret0, _ := ret[0].([]*gameradar.Tab)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// Tabs indicates an expected call of Tabs.
func (mr *MockIClientMockRecorder) Tabs(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Tabs", reflect.TypeOf((*MockIClient)(nil).Tabs), ctx)
}
