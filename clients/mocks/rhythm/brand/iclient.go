// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/clients/rhythm/brand (interfaces: IClient)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	client "golang.52tt.com/pkg/client"
	protocol "golang.52tt.com/pkg/protocol"
	brand "golang.52tt.com/protocol/services/rhythm/brand"
)

// MockIClient is a mock of IClient interface.
type MockIClient struct {
	ctrl     *gomock.Controller
	recorder *MockIClientMockRecorder
}

// MockIClientMockRecorder is the mock recorder for MockIClient.
type MockIClientMockRecorder struct {
	mock *MockIClient
}

// NewMockIClient creates a new mock instance.
func NewMockIClient(ctrl *gomock.Controller) *MockIClient {
	mock := &MockIClient{ctrl: ctrl}
	mock.recorder = &MockIClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIClient) EXPECT() *MockIClientMockRecorder {
	return m.recorder
}

// BatGetBrandMember mocks base method.
func (m *MockIClient) BatGetBrandMember(arg0 context.Context, arg1 []string) (*brand.BatGetBrandMemberResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatGetBrandMember", arg0, arg1)
	ret0, _ := ret[0].(*brand.BatGetBrandMemberResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// BatGetBrandMember indicates an expected call of BatGetBrandMember.
func (mr *MockIClientMockRecorder) BatGetBrandMember(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatGetBrandMember", reflect.TypeOf((*MockIClient)(nil).BatGetBrandMember), arg0, arg1)
}

// CC mocks base method.
func (m *MockIClient) CC() client.Conn {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CC")
	ret0, _ := ret[0].(client.Conn)
	return ret0
}

// CC indicates an expected call of CC.
func (mr *MockIClientMockRecorder) CC() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CC", reflect.TypeOf((*MockIClient)(nil).CC))
}

// Close mocks base method.
func (m *MockIClient) Close() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Close")
}

// Close indicates an expected call of Close.
func (mr *MockIClientMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockIClient)(nil).Close))
}

// GetBrand mocks base method.
func (m *MockIClient) GetBrand(arg0 context.Context, arg1, arg2 uint32) (*brand.GetBrandResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBrand", arg0, arg1, arg2)
	ret0, _ := ret[0].(*brand.GetBrandResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetBrand indicates an expected call of GetBrand.
func (mr *MockIClientMockRecorder) GetBrand(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBrand", reflect.TypeOf((*MockIClient)(nil).GetBrand), arg0, arg1, arg2)
}

// GetBrandById mocks base method.
func (m *MockIClient) GetBrandById(arg0 context.Context, arg1 string) (*brand.GetBrandByIdResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBrandById", arg0, arg1)
	ret0, _ := ret[0].(*brand.GetBrandByIdResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetBrandById indicates an expected call of GetBrandById.
func (mr *MockIClientMockRecorder) GetBrandById(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBrandById", reflect.TypeOf((*MockIClient)(nil).GetBrandById), arg0, arg1)
}

// GetFansAuthentication mocks base method.
func (m *MockIClient) GetFansAuthentication(arg0 context.Context, arg1 uint32) (*brand.GetFansAuthenticationResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetFansAuthentication", arg0, arg1)
	ret0, _ := ret[0].(*brand.GetFansAuthenticationResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetFansAuthentication indicates an expected call of GetFansAuthentication.
func (mr *MockIClientMockRecorder) GetFansAuthentication(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFansAuthentication", reflect.TypeOf((*MockIClient)(nil).GetFansAuthentication), arg0, arg1)
}

// GetUserBrandTopicID mocks base method.
func (m *MockIClient) GetUserBrandTopicID(arg0 context.Context, arg1 uint32) (*brand.GetUserBrandTopicIDResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserBrandTopicID", arg0, arg1)
	ret0, _ := ret[0].(*brand.GetUserBrandTopicIDResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetUserBrandTopicID indicates an expected call of GetUserBrandTopicID.
func (mr *MockIClientMockRecorder) GetUserBrandTopicID(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserBrandTopicID", reflect.TypeOf((*MockIClient)(nil).GetUserBrandTopicID), arg0, arg1)
}

// Stub mocks base method.
func (m *MockIClient) Stub() interface{} {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Stub")
	ret0, _ := ret[0].(interface{})
	return ret0
}

// Stub indicates an expected call of Stub.
func (mr *MockIClientMockRecorder) Stub() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Stub", reflect.TypeOf((*MockIClient)(nil).Stub))
}
