// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/clients/mystery-place (interfaces: IClient)

// Package mystery_place is a generated GoMock package.
package mystery_place

import (
    context "context"
    reflect "reflect"

    gomock "github.com/golang/mock/gomock"
    client "golang.52tt.com/pkg/client"
    mystery_place "golang.52tt.com/protocol/services/mystery-place"
    grpc "google.golang.org/grpc"
)

// MockIClient is a mock of IClient interface.
type MockIClient struct {
    ctrl     *gomock.Controller
    recorder *MockIClientMockRecorder
}

// MockIClientMockRecorder is the mock recorder for MockIClient.
type MockIClientMockRecorder struct {
    mock *MockIClient
}

// NewMockIClient creates a new mock instance.
func NewMockIClient(ctrl *gomock.Controller) *MockIClient {
    mock := &MockIClient{ctrl: ctrl}
    mock.recorder = &MockIClientMockRecorder{mock}
    return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIClient) EXPECT() *MockIClientMockRecorder {
    return m.recorder
}

// AddFeedback mocks base method.
func (m *MockIClient) AddFeedback(arg0 context.Context, arg1 *mystery_place.AddFeedbackReq, arg2 ...grpc.CallOption) (*mystery_place.AddFeedbackResp, error) {
    m.ctrl.T.Helper()
    varargs := []interface{}{arg0, arg1}
    for _, a := range arg2 {
        varargs = append(varargs, a)
    }
    ret := m.ctrl.Call(m, "AddFeedback", varargs...)
    ret0, _ := ret[0].(*mystery_place.AddFeedbackResp)
    ret1, _ := ret[1].(error)
    return ret0, ret1
}

// AddFeedback indicates an expected call of AddFeedback.
func (mr *MockIClientMockRecorder) AddFeedback(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
    mr.mock.ctrl.T.Helper()
    varargs := append([]interface{}{arg0, arg1}, arg2...)
    return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddFeedback", reflect.TypeOf((*MockIClient)(nil).AddFeedback), varargs...)
}

// AddPlaymateTag mocks base method.
func (m *MockIClient) AddPlaymateTag(arg0 context.Context, arg1 *mystery_place.AddPlaymateTagReq, arg2 ...grpc.CallOption) (*mystery_place.AddPlaymateTagResp, error) {
    m.ctrl.T.Helper()
    varargs := []interface{}{arg0, arg1}
    for _, a := range arg2 {
        varargs = append(varargs, a)
    }
    ret := m.ctrl.Call(m, "AddPlaymateTag", varargs...)
    ret0, _ := ret[0].(*mystery_place.AddPlaymateTagResp)
    ret1, _ := ret[1].(error)
    return ret0, ret1
}

// AddPlaymateTag indicates an expected call of AddPlaymateTag.
func (mr *MockIClientMockRecorder) AddPlaymateTag(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
    mr.mock.ctrl.T.Helper()
    varargs := append([]interface{}{arg0, arg1}, arg2...)
    return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddPlaymateTag", reflect.TypeOf((*MockIClient)(nil).AddPlaymateTag), varargs...)
}

// AddUserCommentScenario mocks base method.
func (m *MockIClient) AddUserCommentScenario(arg0 context.Context, arg1 *mystery_place.AddUserCommentScenarioReq, arg2 ...grpc.CallOption) (*mystery_place.AddUserCommentScenarioResp, error) {
    m.ctrl.T.Helper()
    varargs := []interface{}{arg0, arg1}
    for _, a := range arg2 {
        varargs = append(varargs, a)
    }
    ret := m.ctrl.Call(m, "AddUserCommentScenario", varargs...)
    ret0, _ := ret[0].(*mystery_place.AddUserCommentScenarioResp)
    ret1, _ := ret[1].(error)
    return ret0, ret1
}

// AddUserCommentScenario indicates an expected call of AddUserCommentScenario.
func (mr *MockIClientMockRecorder) AddUserCommentScenario(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
    mr.mock.ctrl.T.Helper()
    varargs := append([]interface{}{arg0, arg1}, arg2...)
    return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddUserCommentScenario", reflect.TypeOf((*MockIClient)(nil).AddUserCommentScenario), varargs...)
}

// AddUserPlaymateTag mocks base method.
func (m *MockIClient) AddUserPlaymateTag(arg0 context.Context, arg1 *mystery_place.AddUserPlaymateTagReq, arg2 ...grpc.CallOption) (*mystery_place.AddUserPlaymateTagResp, error) {
    m.ctrl.T.Helper()
    varargs := []interface{}{arg0, arg1}
    for _, a := range arg2 {
        varargs = append(varargs, a)
    }
    ret := m.ctrl.Call(m, "AddUserPlaymateTag", varargs...)
    ret0, _ := ret[0].(*mystery_place.AddUserPlaymateTagResp)
    ret1, _ := ret[1].(error)
    return ret0, ret1
}

// AddUserPlaymateTag indicates an expected call of AddUserPlaymateTag.
func (mr *MockIClientMockRecorder) AddUserPlaymateTag(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
    mr.mock.ctrl.T.Helper()
    varargs := append([]interface{}{arg0, arg1}, arg2...)
    return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddUserPlaymateTag", reflect.TypeOf((*MockIClient)(nil).AddUserPlaymateTag), varargs...)
}

// BatGetUserHotPlaymateTag mocks base method.
func (m *MockIClient) BatGetUserHotPlaymateTag(arg0 context.Context, arg1 *mystery_place.BatGetUserHotPlaymateTagReq, arg2 ...grpc.CallOption) (map[uint32]*mystery_place.UserHotPlaymateTag, error) {
    m.ctrl.T.Helper()
    varargs := []interface{}{arg0, arg1}
    for _, a := range arg2 {
        varargs = append(varargs, a)
    }
    ret := m.ctrl.Call(m, "BatGetUserHotPlaymateTag", varargs...)
    ret0, _ := ret[0].(map[uint32]*mystery_place.UserHotPlaymateTag)
    ret1, _ := ret[1].(error)
    return ret0, ret1
}

// BatGetUserHotPlaymateTag indicates an expected call of BatGetUserHotPlaymateTag.
func (mr *MockIClientMockRecorder) BatGetUserHotPlaymateTag(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
    mr.mock.ctrl.T.Helper()
    varargs := append([]interface{}{arg0, arg1}, arg2...)
    return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatGetUserHotPlaymateTag", reflect.TypeOf((*MockIClient)(nil).BatGetUserHotPlaymateTag), varargs...)
}

// BatchCheckHelperList mocks base method.
func (m *MockIClient) BatchCheckHelperList(arg0 context.Context, arg1 *mystery_place.BatchCheckHelperListReq, arg2 ...grpc.CallOption) (*mystery_place.BatchCheckHelperListResp, error) {
    m.ctrl.T.Helper()
    varargs := []interface{}{arg0, arg1}
    for _, a := range arg2 {
        varargs = append(varargs, a)
    }
    ret := m.ctrl.Call(m, "BatchCheckHelperList", varargs...)
    ret0, _ := ret[0].(*mystery_place.BatchCheckHelperListResp)
    ret1, _ := ret[1].(error)
    return ret0, ret1
}

// BatchCheckHelperList indicates an expected call of BatchCheckHelperList.
func (mr *MockIClientMockRecorder) BatchCheckHelperList(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
    mr.mock.ctrl.T.Helper()
    varargs := append([]interface{}{arg0, arg1}, arg2...)
    return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchCheckHelperList", reflect.TypeOf((*MockIClient)(nil).BatchCheckHelperList), varargs...)
}

// BatchCheckHelperListMap mocks base method.
func (m *MockIClient) BatchCheckHelperListMap(arg0 context.Context, arg1 *mystery_place.BatchCheckHelperListReq, arg2 ...grpc.CallOption) (map[uint32]bool, error) {
    m.ctrl.T.Helper()
    varargs := []interface{}{arg0, arg1}
    for _, a := range arg2 {
        varargs = append(varargs, a)
    }
    ret := m.ctrl.Call(m, "BatchCheckHelperListMap", varargs...)
    ret0, _ := ret[0].(map[uint32]bool)
    ret1, _ := ret[1].(error)
    return ret0, ret1
}

// BatchCheckHelperListMap indicates an expected call of BatchCheckHelperListMap.
func (mr *MockIClientMockRecorder) BatchCheckHelperListMap(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
    mr.mock.ctrl.T.Helper()
    varargs := append([]interface{}{arg0, arg1}, arg2...)
    return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchCheckHelperListMap", reflect.TypeOf((*MockIClient)(nil).BatchCheckHelperListMap), varargs...)
}

// BatchGetGamePercentByChannelIds mocks base method.
func (m *MockIClient) BatchGetGamePercentByChannelIds(arg0 context.Context, arg1 *mystery_place.BatchGetGamePercentByChannelIdsReq, arg2 ...grpc.CallOption) (*mystery_place.BatchGetGamePercentByChannelIdsResp, error) {
    m.ctrl.T.Helper()
    varargs := []interface{}{arg0, arg1}
    for _, a := range arg2 {
        varargs = append(varargs, a)
    }
    ret := m.ctrl.Call(m, "BatchGetGamePercentByChannelIds", varargs...)
    ret0, _ := ret[0].(*mystery_place.BatchGetGamePercentByChannelIdsResp)
    ret1, _ := ret[1].(error)
    return ret0, ret1
}

// BatchGetGamePercentByChannelIds indicates an expected call of BatchGetGamePercentByChannelIds.
func (mr *MockIClientMockRecorder) BatchGetGamePercentByChannelIds(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
    mr.mock.ctrl.T.Helper()
    varargs := append([]interface{}{arg0, arg1}, arg2...)
    return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetGamePercentByChannelIds", reflect.TypeOf((*MockIClient)(nil).BatchGetGamePercentByChannelIds), varargs...)
}

// BatchGetPlayerGamePercentByGameIds mocks base method.
func (m *MockIClient) BatchGetPlayerGamePercentByGameIds(arg0 context.Context, arg1 *mystery_place.BatchGetPlayerGamePercentByGameIdsReq, arg2 ...grpc.CallOption) (*mystery_place.BatchGetPlayerGamePercentByGameIdsResp, error) {
    m.ctrl.T.Helper()
    varargs := []interface{}{arg0, arg1}
    for _, a := range arg2 {
        varargs = append(varargs, a)
    }
    ret := m.ctrl.Call(m, "BatchGetPlayerGamePercentByGameIds", varargs...)
    ret0, _ := ret[0].(*mystery_place.BatchGetPlayerGamePercentByGameIdsResp)
    ret1, _ := ret[1].(error)
    return ret0, ret1
}

// BatchGetPlayerGamePercentByGameIds indicates an expected call of BatchGetPlayerGamePercentByGameIds.
func (mr *MockIClientMockRecorder) BatchGetPlayerGamePercentByGameIds(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
    mr.mock.ctrl.T.Helper()
    varargs := append([]interface{}{arg0, arg1}, arg2...)
    return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetPlayerGamePercentByGameIds", reflect.TypeOf((*MockIClient)(nil).BatchGetPlayerGamePercentByGameIds), varargs...)
}

// BatchGetRcmdTabById mocks base method.
func (m *MockIClient) BatchGetRcmdTabById(arg0 context.Context, arg1 *mystery_place.BatchGetRcmdTabByIdReq, arg2 ...grpc.CallOption) (*mystery_place.BatchGetRcmdTabByIdResp, error) {
    m.ctrl.T.Helper()
    varargs := []interface{}{arg0, arg1}
    for _, a := range arg2 {
        varargs = append(varargs, a)
    }
    ret := m.ctrl.Call(m, "BatchGetRcmdTabById", varargs...)
    ret0, _ := ret[0].(*mystery_place.BatchGetRcmdTabByIdResp)
    ret1, _ := ret[1].(error)
    return ret0, ret1
}

// BatchGetRcmdTabById indicates an expected call of BatchGetRcmdTabById.
func (mr *MockIClientMockRecorder) BatchGetRcmdTabById(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
    mr.mock.ctrl.T.Helper()
    varargs := append([]interface{}{arg0, arg1}, arg2...)
    return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetRcmdTabById", reflect.TypeOf((*MockIClient)(nil).BatchGetRcmdTabById), varargs...)
}

// BatchGetScenarioShareLink mocks base method.
func (m *MockIClient) BatchGetScenarioShareLink(arg0 context.Context, arg1 *mystery_place.BatchGetScenarioShareLinkReq, arg2 ...grpc.CallOption) (*mystery_place.BatchGetScenarioShareLinkResp, error) {
    m.ctrl.T.Helper()
    varargs := []interface{}{arg0, arg1}
    for _, a := range arg2 {
        varargs = append(varargs, a)
    }
    ret := m.ctrl.Call(m, "BatchGetScenarioShareLink", varargs...)
    ret0, _ := ret[0].(*mystery_place.BatchGetScenarioShareLinkResp)
    ret1, _ := ret[1].(error)
    return ret0, ret1
}

// BatchGetScenarioShareLink indicates an expected call of BatchGetScenarioShareLink.
func (mr *MockIClientMockRecorder) BatchGetScenarioShareLink(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
    mr.mock.ctrl.T.Helper()
    varargs := append([]interface{}{arg0, arg1}, arg2...)
    return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetScenarioShareLink", reflect.TypeOf((*MockIClient)(nil).BatchGetScenarioShareLink), varargs...)
}

// BatchGetUserCommentScenario mocks base method.
func (m *MockIClient) BatchGetUserCommentScenario(arg0 context.Context, arg1 *mystery_place.BatchGetUserCommentScenarioReq, arg2 ...grpc.CallOption) (*mystery_place.BatchGetUserCommentScenarioResp, error) {
    m.ctrl.T.Helper()
    varargs := []interface{}{arg0, arg1}
    for _, a := range arg2 {
        varargs = append(varargs, a)
    }
    ret := m.ctrl.Call(m, "BatchGetUserCommentScenario", varargs...)
    ret0, _ := ret[0].(*mystery_place.BatchGetUserCommentScenarioResp)
    ret1, _ := ret[1].(error)
    return ret0, ret1
}

// BatchGetUserCommentScenario indicates an expected call of BatchGetUserCommentScenario.
func (mr *MockIClientMockRecorder) BatchGetUserCommentScenario(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
    mr.mock.ctrl.T.Helper()
    varargs := append([]interface{}{arg0, arg1}, arg2...)
    return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetUserCommentScenario", reflect.TypeOf((*MockIClient)(nil).BatchGetUserCommentScenario), varargs...)
}

// CC mocks base method.
func (m *MockIClient) CC() client.Conn {
    m.ctrl.T.Helper()
    ret := m.ctrl.Call(m, "CC")
    ret0, _ := ret[0].(client.Conn)
    return ret0
}

// CC indicates an expected call of CC.
func (mr *MockIClientMockRecorder) CC() *gomock.Call {
    mr.mock.ctrl.T.Helper()
    return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CC", reflect.TypeOf((*MockIClient)(nil).CC))
}

// Close mocks base method.
func (m *MockIClient) Close() {
    m.ctrl.T.Helper()
    m.ctrl.Call(m, "Close")
}

// Close indicates an expected call of Close.
func (mr *MockIClientMockRecorder) Close() *gomock.Call {
    mr.mock.ctrl.T.Helper()
    return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockIClient)(nil).Close))
}

// DelScenarioInfo mocks base method.
func (m *MockIClient) DelScenarioInfo(arg0 context.Context, arg1 *mystery_place.DelScenarioInfoReq, arg2 ...grpc.CallOption) (*mystery_place.DelScenarioInfoResp, error) {
    m.ctrl.T.Helper()
    varargs := []interface{}{arg0, arg1}
    for _, a := range arg2 {
        varargs = append(varargs, a)
    }
    ret := m.ctrl.Call(m, "DelScenarioInfo", varargs...)
    ret0, _ := ret[0].(*mystery_place.DelScenarioInfoResp)
    ret1, _ := ret[1].(error)
    return ret0, ret1
}

// DelScenarioInfo indicates an expected call of DelScenarioInfo.
func (mr *MockIClientMockRecorder) DelScenarioInfo(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
    mr.mock.ctrl.T.Helper()
    varargs := append([]interface{}{arg0, arg1}, arg2...)
    return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelScenarioInfo", reflect.TypeOf((*MockIClient)(nil).DelScenarioInfo), varargs...)
}

// DelScenarioShareLink mocks base method.
func (m *MockIClient) DelScenarioShareLink(arg0 context.Context, arg1 *mystery_place.DelScenarioShareLinkReq, arg2 ...grpc.CallOption) (*mystery_place.DelScenarioShareLinkResp, error) {
    m.ctrl.T.Helper()
    varargs := []interface{}{arg0, arg1}
    for _, a := range arg2 {
        varargs = append(varargs, a)
    }
    ret := m.ctrl.Call(m, "DelScenarioShareLink", varargs...)
    ret0, _ := ret[0].(*mystery_place.DelScenarioShareLinkResp)
    ret1, _ := ret[1].(error)
    return ret0, ret1
}

// DelScenarioShareLink indicates an expected call of DelScenarioShareLink.
func (mr *MockIClientMockRecorder) DelScenarioShareLink(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
    mr.mock.ctrl.T.Helper()
    varargs := append([]interface{}{arg0, arg1}, arg2...)
    return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelScenarioShareLink", reflect.TypeOf((*MockIClient)(nil).DelScenarioShareLink), varargs...)
}

// DeletePlaymateTag mocks base method.
func (m *MockIClient) DeletePlaymateTag(arg0 context.Context, arg1 *mystery_place.DeletePlaymateTagReq, arg2 ...grpc.CallOption) (*mystery_place.DeletePlaymateTagResp, error) {
    m.ctrl.T.Helper()
    varargs := []interface{}{arg0, arg1}
    for _, a := range arg2 {
        varargs = append(varargs, a)
    }
    ret := m.ctrl.Call(m, "DeletePlaymateTag", varargs...)
    ret0, _ := ret[0].(*mystery_place.DeletePlaymateTagResp)
    ret1, _ := ret[1].(error)
    return ret0, ret1
}

// DeletePlaymateTag indicates an expected call of DeletePlaymateTag.
func (mr *MockIClientMockRecorder) DeletePlaymateTag(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
    mr.mock.ctrl.T.Helper()
    varargs := append([]interface{}{arg0, arg1}, arg2...)
    return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeletePlaymateTag", reflect.TypeOf((*MockIClient)(nil).DeletePlaymateTag), varargs...)
}

// GetGamePercentMapByChannelIds mocks base method.
func (m *MockIClient) GetGamePercentMapByChannelIds(arg0 context.Context, arg1 *mystery_place.BatchGetGamePercentByChannelIdsReq, arg2 ...grpc.CallOption) (map[uint32]*mystery_place.RoomGamePercent, error) {
    m.ctrl.T.Helper()
    varargs := []interface{}{arg0, arg1}
    for _, a := range arg2 {
        varargs = append(varargs, a)
    }
    ret := m.ctrl.Call(m, "GetGamePercentMapByChannelIds", varargs...)
    ret0, _ := ret[0].(map[uint32]*mystery_place.RoomGamePercent)
    ret1, _ := ret[1].(error)
    return ret0, ret1
}

// GetGamePercentMapByChannelIds indicates an expected call of GetGamePercentMapByChannelIds.
func (mr *MockIClientMockRecorder) GetGamePercentMapByChannelIds(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
    mr.mock.ctrl.T.Helper()
    varargs := append([]interface{}{arg0, arg1}, arg2...)
    return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGamePercentMapByChannelIds", reflect.TypeOf((*MockIClient)(nil).GetGamePercentMapByChannelIds), varargs...)
}

// GetNewScenarioTips mocks base method.
func (m *MockIClient) GetNewScenarioTips(arg0 context.Context, arg1 *mystery_place.GetNewScenarioTipsReq, arg2 ...grpc.CallOption) (*mystery_place.GetNewScenarioTipsResp, error) {
    m.ctrl.T.Helper()
    varargs := []interface{}{arg0, arg1}
    for _, a := range arg2 {
        varargs = append(varargs, a)
    }
    ret := m.ctrl.Call(m, "GetNewScenarioTips", varargs...)
    ret0, _ := ret[0].(*mystery_place.GetNewScenarioTipsResp)
    ret1, _ := ret[1].(error)
    return ret0, ret1
}

// GetNewScenarioTips indicates an expected call of GetNewScenarioTips.
func (mr *MockIClientMockRecorder) GetNewScenarioTips(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
    mr.mock.ctrl.T.Helper()
    varargs := append([]interface{}{arg0, arg1}, arg2...)
    return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNewScenarioTips", reflect.TypeOf((*MockIClient)(nil).GetNewScenarioTips), varargs...)
}

// GetNewUsers mocks base method.
func (m *MockIClient) GetNewUsers(arg0 context.Context, arg1 *mystery_place.GetNewUsersReq, arg2 ...grpc.CallOption) (*mystery_place.GetNewUsersResp, error) {
    m.ctrl.T.Helper()
    varargs := []interface{}{arg0, arg1}
    for _, a := range arg2 {
        varargs = append(varargs, a)
    }
    ret := m.ctrl.Call(m, "GetNewUsers", varargs...)
    ret0, _ := ret[0].(*mystery_place.GetNewUsersResp)
    ret1, _ := ret[1].(error)
    return ret0, ret1
}

// GetNewUsers indicates an expected call of GetNewUsers.
func (mr *MockIClientMockRecorder) GetNewUsers(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
    mr.mock.ctrl.T.Helper()
    varargs := append([]interface{}{arg0, arg1}, arg2...)
    return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNewUsers", reflect.TypeOf((*MockIClient)(nil).GetNewUsers), varargs...)
}

// GetPlayedScenarioRecordDetail mocks base method.
func (m *MockIClient) GetPlayedScenarioRecordDetail(arg0 context.Context, arg1 *mystery_place.GetPlayedScenarioRecordDetailReq, arg2 ...grpc.CallOption) (*mystery_place.GetPlayedScenarioRecordDetailResp, error) {
    m.ctrl.T.Helper()
    varargs := []interface{}{arg0, arg1}
    for _, a := range arg2 {
        varargs = append(varargs, a)
    }
    ret := m.ctrl.Call(m, "GetPlayedScenarioRecordDetail", varargs...)
    ret0, _ := ret[0].(*mystery_place.GetPlayedScenarioRecordDetailResp)
    ret1, _ := ret[1].(error)
    return ret0, ret1
}

// GetPlayedScenarioRecordDetail indicates an expected call of GetPlayedScenarioRecordDetail.
func (mr *MockIClientMockRecorder) GetPlayedScenarioRecordDetail(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
    mr.mock.ctrl.T.Helper()
    varargs := append([]interface{}{arg0, arg1}, arg2...)
    return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPlayedScenarioRecordDetail", reflect.TypeOf((*MockIClient)(nil).GetPlayedScenarioRecordDetail), varargs...)
}

// GetPlayedScenarioRecordList mocks base method.
func (m *MockIClient) GetPlayedScenarioRecordList(arg0 context.Context, arg1 *mystery_place.GetPlayedScenarioRecordListReq, arg2 ...grpc.CallOption) (*mystery_place.GetPlayedScenarioRecordListResp, error) {
    m.ctrl.T.Helper()
    varargs := []interface{}{arg0, arg1}
    for _, a := range arg2 {
        varargs = append(varargs, a)
    }
    ret := m.ctrl.Call(m, "GetPlayedScenarioRecordList", varargs...)
    ret0, _ := ret[0].(*mystery_place.GetPlayedScenarioRecordListResp)
    ret1, _ := ret[1].(error)
    return ret0, ret1
}

// GetPlayedScenarioRecordList indicates an expected call of GetPlayedScenarioRecordList.
func (mr *MockIClientMockRecorder) GetPlayedScenarioRecordList(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
    mr.mock.ctrl.T.Helper()
    varargs := append([]interface{}{arg0, arg1}, arg2...)
    return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPlayedScenarioRecordList", reflect.TypeOf((*MockIClient)(nil).GetPlayedScenarioRecordList), varargs...)
}

// GetPlayerGamePercentMapByGameIds mocks base method.
func (m *MockIClient) GetPlayerGamePercentMapByGameIds(arg0 context.Context, arg1 *mystery_place.BatchGetPlayerGamePercentByGameIdsReq, arg2 ...grpc.CallOption) (map[uint32]*mystery_place.UserGamePercent, error) {
    m.ctrl.T.Helper()
    varargs := []interface{}{arg0, arg1}
    for _, a := range arg2 {
        varargs = append(varargs, a)
    }
    ret := m.ctrl.Call(m, "GetPlayerGamePercentMapByGameIds", varargs...)
    ret0, _ := ret[0].(map[uint32]*mystery_place.UserGamePercent)
    ret1, _ := ret[1].(error)
    return ret0, ret1
}

// GetPlayerGamePercentMapByGameIds indicates an expected call of GetPlayerGamePercentMapByGameIds.
func (mr *MockIClientMockRecorder) GetPlayerGamePercentMapByGameIds(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
    mr.mock.ctrl.T.Helper()
    varargs := append([]interface{}{arg0, arg1}, arg2...)
    return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPlayerGamePercentMapByGameIds", reflect.TypeOf((*MockIClient)(nil).GetPlayerGamePercentMapByGameIds), varargs...)
}

// GetPlayerLastRecord mocks base method.
func (m *MockIClient) GetPlayerLastRecord(arg0 context.Context, arg1 *mystery_place.GetPlayerLastRecordReq, arg2 ...grpc.CallOption) (*mystery_place.GetPlayerLastRecordResp, error) {
    m.ctrl.T.Helper()
    varargs := []interface{}{arg0, arg1}
    for _, a := range arg2 {
        varargs = append(varargs, a)
    }
    ret := m.ctrl.Call(m, "GetPlayerLastRecord", varargs...)
    ret0, _ := ret[0].(*mystery_place.GetPlayerLastRecordResp)
    ret1, _ := ret[1].(error)
    return ret0, ret1
}

// GetPlayerLastRecord indicates an expected call of GetPlayerLastRecord.
func (mr *MockIClientMockRecorder) GetPlayerLastRecord(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
    mr.mock.ctrl.T.Helper()
    varargs := append([]interface{}{arg0, arg1}, arg2...)
    return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPlayerLastRecord", reflect.TypeOf((*MockIClient)(nil).GetPlayerLastRecord), varargs...)
}

// GetPlaymateTagByIds mocks base method.
func (m *MockIClient) GetPlaymateTagByIds(arg0 context.Context, arg1 *mystery_place.GetPlaymateTagByIdsReq, arg2 ...grpc.CallOption) (*mystery_place.GetPlaymateTagByIdsResp, error) {
    m.ctrl.T.Helper()
    varargs := []interface{}{arg0, arg1}
    for _, a := range arg2 {
        varargs = append(varargs, a)
    }
    ret := m.ctrl.Call(m, "GetPlaymateTagByIds", varargs...)
    ret0, _ := ret[0].(*mystery_place.GetPlaymateTagByIdsResp)
    ret1, _ := ret[1].(error)
    return ret0, ret1
}

// GetPlaymateTagByIds indicates an expected call of GetPlaymateTagByIds.
func (mr *MockIClientMockRecorder) GetPlaymateTagByIds(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
    mr.mock.ctrl.T.Helper()
    varargs := append([]interface{}{arg0, arg1}, arg2...)
    return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPlaymateTagByIds", reflect.TypeOf((*MockIClient)(nil).GetPlaymateTagByIds), varargs...)
}

// GetPlaymateTags mocks base method.
func (m *MockIClient) GetPlaymateTags(arg0 context.Context, arg1 *mystery_place.GetPlaymateTagsReq, arg2 ...grpc.CallOption) (*mystery_place.GetPlaymateTagsResp, error) {
    m.ctrl.T.Helper()
    varargs := []interface{}{arg0, arg1}
    for _, a := range arg2 {
        varargs = append(varargs, a)
    }
    ret := m.ctrl.Call(m, "GetPlaymateTags", varargs...)
    ret0, _ := ret[0].(*mystery_place.GetPlaymateTagsResp)
    ret1, _ := ret[1].(error)
    return ret0, ret1
}

// GetPlaymateTags indicates an expected call of GetPlaymateTags.
func (mr *MockIClientMockRecorder) GetPlaymateTags(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
    mr.mock.ctrl.T.Helper()
    varargs := append([]interface{}{arg0, arg1}, arg2...)
    return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPlaymateTags", reflect.TypeOf((*MockIClient)(nil).GetPlaymateTags), varargs...)
}

// GetScenarioChapterSummary mocks base method.
func (m *MockIClient) GetScenarioChapterSummary(arg0 context.Context, arg1 *mystery_place.GetScenarioChapterSummaryReq, arg2 ...grpc.CallOption) (*mystery_place.GetScenarioChapterSummaryResp, error) {
    m.ctrl.T.Helper()
    varargs := []interface{}{arg0, arg1}
    for _, a := range arg2 {
        varargs = append(varargs, a)
    }
    ret := m.ctrl.Call(m, "GetScenarioChapterSummary", varargs...)
    ret0, _ := ret[0].(*mystery_place.GetScenarioChapterSummaryResp)
    ret1, _ := ret[1].(error)
    return ret0, ret1
}

// GetScenarioChapterSummary indicates an expected call of GetScenarioChapterSummary.
func (mr *MockIClientMockRecorder) GetScenarioChapterSummary(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
    mr.mock.ctrl.T.Helper()
    varargs := append([]interface{}{arg0, arg1}, arg2...)
    return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetScenarioChapterSummary", reflect.TypeOf((*MockIClient)(nil).GetScenarioChapterSummary), varargs...)
}

// GetScenarioHotRank mocks base method.
func (m *MockIClient) GetScenarioHotRank(arg0 context.Context, arg1 *mystery_place.GetScenarioHotRankReq, arg2 ...grpc.CallOption) (*mystery_place.GetScenarioHotRankResp, error) {
    m.ctrl.T.Helper()
    varargs := []interface{}{arg0, arg1}
    for _, a := range arg2 {
        varargs = append(varargs, a)
    }
    ret := m.ctrl.Call(m, "GetScenarioHotRank", varargs...)
    ret0, _ := ret[0].(*mystery_place.GetScenarioHotRankResp)
    ret1, _ := ret[1].(error)
    return ret0, ret1
}

// GetScenarioHotRank indicates an expected call of GetScenarioHotRank.
func (mr *MockIClientMockRecorder) GetScenarioHotRank(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
    mr.mock.ctrl.T.Helper()
    varargs := append([]interface{}{arg0, arg1}, arg2...)
    return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetScenarioHotRank", reflect.TypeOf((*MockIClient)(nil).GetScenarioHotRank), varargs...)
}

// GetScenarioInfo mocks base method.
func (m *MockIClient) GetScenarioInfo(arg0 context.Context, arg1 *mystery_place.GetScenarioInfoReq, arg2 ...grpc.CallOption) (*mystery_place.GetScenarioInfoResp, error) {
    m.ctrl.T.Helper()
    varargs := []interface{}{arg0, arg1}
    for _, a := range arg2 {
        varargs = append(varargs, a)
    }
    ret := m.ctrl.Call(m, "GetScenarioInfo", varargs...)
    ret0, _ := ret[0].(*mystery_place.GetScenarioInfoResp)
    ret1, _ := ret[1].(error)
    return ret0, ret1
}

// GetScenarioInfo indicates an expected call of GetScenarioInfo.
func (mr *MockIClientMockRecorder) GetScenarioInfo(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
    mr.mock.ctrl.T.Helper()
    varargs := append([]interface{}{arg0, arg1}, arg2...)
    return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetScenarioInfo", reflect.TypeOf((*MockIClient)(nil).GetScenarioInfo), varargs...)
}

// GetScenarioInfoTotalCount mocks base method.
func (m *MockIClient) GetScenarioInfoTotalCount(arg0 context.Context, arg1 *mystery_place.GetScenarioInfoTotalCountReq, arg2 ...grpc.CallOption) (*mystery_place.GetScenarioInfoTotalCountResp, error) {
    m.ctrl.T.Helper()
    varargs := []interface{}{arg0, arg1}
    for _, a := range arg2 {
        varargs = append(varargs, a)
    }
    ret := m.ctrl.Call(m, "GetScenarioInfoTotalCount", varargs...)
    ret0, _ := ret[0].(*mystery_place.GetScenarioInfoTotalCountResp)
    ret1, _ := ret[1].(error)
    return ret0, ret1
}

// GetScenarioInfoTotalCount indicates an expected call of GetScenarioInfoTotalCount.
func (mr *MockIClientMockRecorder) GetScenarioInfoTotalCount(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
    mr.mock.ctrl.T.Helper()
    varargs := append([]interface{}{arg0, arg1}, arg2...)
    return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetScenarioInfoTotalCount", reflect.TypeOf((*MockIClient)(nil).GetScenarioInfoTotalCount), varargs...)
}

// IsNewUser mocks base method.
func (m *MockIClient) IsNewUser(arg0 context.Context, arg1 *mystery_place.IsNewUserReq, arg2 ...grpc.CallOption) (*mystery_place.IsNewUserResp, error) {
    m.ctrl.T.Helper()
    varargs := []interface{}{arg0, arg1}
    for _, a := range arg2 {
        varargs = append(varargs, a)
    }
    ret := m.ctrl.Call(m, "IsNewUser", varargs...)
    ret0, _ := ret[0].(*mystery_place.IsNewUserResp)
    ret1, _ := ret[1].(error)
    return ret0, ret1
}

// IsNewUser indicates an expected call of IsNewUser.
func (mr *MockIClientMockRecorder) IsNewUser(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
    mr.mock.ctrl.T.Helper()
    varargs := append([]interface{}{arg0, arg1}, arg2...)
    return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsNewUser", reflect.TypeOf((*MockIClient)(nil).IsNewUser), varargs...)
}

// ListScenarioComment mocks base method.
func (m *MockIClient) ListScenarioComment(arg0 context.Context, arg1 *mystery_place.ListScenarioCommentReq, arg2 ...grpc.CallOption) (*mystery_place.ListScenarioCommentResp, error) {
    m.ctrl.T.Helper()
    varargs := []interface{}{arg0, arg1}
    for _, a := range arg2 {
        varargs = append(varargs, a)
    }
    ret := m.ctrl.Call(m, "ListScenarioComment", varargs...)
    ret0, _ := ret[0].(*mystery_place.ListScenarioCommentResp)
    ret1, _ := ret[1].(error)
    return ret0, ret1
}

// ListScenarioComment indicates an expected call of ListScenarioComment.
func (mr *MockIClientMockRecorder) ListScenarioComment(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
    mr.mock.ctrl.T.Helper()
    varargs := append([]interface{}{arg0, arg1}, arg2...)
    return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListScenarioComment", reflect.TypeOf((*MockIClient)(nil).ListScenarioComment), varargs...)
}

// ListScenarioCommentSummary mocks base method.
func (m *MockIClient) ListScenarioCommentSummary(arg0 context.Context, arg1 *mystery_place.ListScenarioCommentSummaryReq, arg2 ...grpc.CallOption) (*mystery_place.ListScenarioCommentSummaryResp, error) {
    m.ctrl.T.Helper()
    varargs := []interface{}{arg0, arg1}
    for _, a := range arg2 {
        varargs = append(varargs, a)
    }
    ret := m.ctrl.Call(m, "ListScenarioCommentSummary", varargs...)
    ret0, _ := ret[0].(*mystery_place.ListScenarioCommentSummaryResp)
    ret1, _ := ret[1].(error)
    return ret0, ret1
}

// ListScenarioCommentSummary indicates an expected call of ListScenarioCommentSummary.
func (mr *MockIClientMockRecorder) ListScenarioCommentSummary(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
    mr.mock.ctrl.T.Helper()
    varargs := append([]interface{}{arg0, arg1}, arg2...)
    return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListScenarioCommentSummary", reflect.TypeOf((*MockIClient)(nil).ListScenarioCommentSummary), varargs...)
}

// ListScenarioInfo mocks base method.
func (m *MockIClient) ListScenarioInfo(arg0 context.Context, arg1 *mystery_place.ListScenarioInfoReq, arg2 ...grpc.CallOption) (*mystery_place.ListScenarioInfoResp, error) {
    m.ctrl.T.Helper()
    varargs := []interface{}{arg0, arg1}
    for _, a := range arg2 {
        varargs = append(varargs, a)
    }
    ret := m.ctrl.Call(m, "ListScenarioInfo", varargs...)
    ret0, _ := ret[0].(*mystery_place.ListScenarioInfoResp)
    ret1, _ := ret[1].(error)
    return ret0, ret1
}

// ListScenarioInfo indicates an expected call of ListScenarioInfo.
func (mr *MockIClientMockRecorder) ListScenarioInfo(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
    mr.mock.ctrl.T.Helper()
    varargs := append([]interface{}{arg0, arg1}, arg2...)
    return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListScenarioInfo", reflect.TypeOf((*MockIClient)(nil).ListScenarioInfo), varargs...)
}

// MarkNewScenarioTipRead mocks base method.
func (m *MockIClient) MarkNewScenarioTipRead(arg0 context.Context, arg1 *mystery_place.MarkNewScenarioTipReadReq, arg2 ...grpc.CallOption) (*mystery_place.MarkNewScenarioTipReadResp, error) {
    m.ctrl.T.Helper()
    varargs := []interface{}{arg0, arg1}
    for _, a := range arg2 {
        varargs = append(varargs, a)
    }
    ret := m.ctrl.Call(m, "MarkNewScenarioTipRead", varargs...)
    ret0, _ := ret[0].(*mystery_place.MarkNewScenarioTipReadResp)
    ret1, _ := ret[1].(error)
    return ret0, ret1
}

// MarkNewScenarioTipRead indicates an expected call of MarkNewScenarioTipRead.
func (mr *MockIClientMockRecorder) MarkNewScenarioTipRead(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
    mr.mock.ctrl.T.Helper()
    varargs := append([]interface{}{arg0, arg1}, arg2...)
    return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MarkNewScenarioTipRead", reflect.TypeOf((*MockIClient)(nil).MarkNewScenarioTipRead), varargs...)
}

// RearrangeScenario mocks base method.
func (m *MockIClient) RearrangeScenario(arg0 context.Context, arg1 *mystery_place.RearrangeScenarioReq, arg2 ...grpc.CallOption) (*mystery_place.RearrangeScenarioResp, error) {
    m.ctrl.T.Helper()
    varargs := []interface{}{arg0, arg1}
    for _, a := range arg2 {
        varargs = append(varargs, a)
    }
    ret := m.ctrl.Call(m, "RearrangeScenario", varargs...)
    ret0, _ := ret[0].(*mystery_place.RearrangeScenarioResp)
    ret1, _ := ret[1].(error)
    return ret0, ret1
}

// RearrangeScenario indicates an expected call of RearrangeScenario.
func (mr *MockIClientMockRecorder) RearrangeScenario(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
    mr.mock.ctrl.T.Helper()
    varargs := append([]interface{}{arg0, arg1}, arg2...)
    return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RearrangeScenario", reflect.TypeOf((*MockIClient)(nil).RearrangeScenario), varargs...)
}

// ReorderPlaymateTags mocks base method.
func (m *MockIClient) ReorderPlaymateTags(arg0 context.Context, arg1 *mystery_place.ReorderPlaymateTagsReq, arg2 ...grpc.CallOption) (*mystery_place.ReorderPlaymateTagsResp, error) {
    m.ctrl.T.Helper()
    varargs := []interface{}{arg0, arg1}
    for _, a := range arg2 {
        varargs = append(varargs, a)
    }
    ret := m.ctrl.Call(m, "ReorderPlaymateTags", varargs...)
    ret0, _ := ret[0].(*mystery_place.ReorderPlaymateTagsResp)
    ret1, _ := ret[1].(error)
    return ret0, ret1
}

// ReorderPlaymateTags indicates an expected call of ReorderPlaymateTags.
func (mr *MockIClientMockRecorder) ReorderPlaymateTags(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
    mr.mock.ctrl.T.Helper()
    varargs := append([]interface{}{arg0, arg1}, arg2...)
    return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReorderPlaymateTags", reflect.TypeOf((*MockIClient)(nil).ReorderPlaymateTags), varargs...)
}

// SetPlayedScenarioRecordVisibility mocks base method.
func (m *MockIClient) SetPlayedScenarioRecordVisibility(arg0 context.Context, arg1 *mystery_place.SetPlayedScenarioRecordVisibilityReq, arg2 ...grpc.CallOption) (*mystery_place.SetPlayedScenarioRecordVisibilityResp, error) {
    m.ctrl.T.Helper()
    varargs := []interface{}{arg0, arg1}
    for _, a := range arg2 {
        varargs = append(varargs, a)
    }
    ret := m.ctrl.Call(m, "SetPlayedScenarioRecordVisibility", varargs...)
    ret0, _ := ret[0].(*mystery_place.SetPlayedScenarioRecordVisibilityResp)
    ret1, _ := ret[1].(error)
    return ret0, ret1
}

// SetPlayedScenarioRecordVisibility indicates an expected call of SetPlayedScenarioRecordVisibility.
func (mr *MockIClientMockRecorder) SetPlayedScenarioRecordVisibility(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
    mr.mock.ctrl.T.Helper()
    varargs := append([]interface{}{arg0, arg1}, arg2...)
    return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetPlayedScenarioRecordVisibility", reflect.TypeOf((*MockIClient)(nil).SetPlayedScenarioRecordVisibility), varargs...)
}

// SetScenarioManualAverage mocks base method.
func (m *MockIClient) SetScenarioManualAverage(arg0 context.Context, arg1 *mystery_place.SetScenarioManualAverageReq, arg2 ...grpc.CallOption) (*mystery_place.SetScenarioManualAverageResp, error) {
    m.ctrl.T.Helper()
    varargs := []interface{}{arg0, arg1}
    for _, a := range arg2 {
        varargs = append(varargs, a)
    }
    ret := m.ctrl.Call(m, "SetScenarioManualAverage", varargs...)
    ret0, _ := ret[0].(*mystery_place.SetScenarioManualAverageResp)
    ret1, _ := ret[1].(error)
    return ret0, ret1
}

// SetScenarioManualAverage indicates an expected call of SetScenarioManualAverage.
func (mr *MockIClientMockRecorder) SetScenarioManualAverage(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
    mr.mock.ctrl.T.Helper()
    varargs := append([]interface{}{arg0, arg1}, arg2...)
    return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetScenarioManualAverage", reflect.TypeOf((*MockIClient)(nil).SetScenarioManualAverage), varargs...)
}

// SetScenarioShareLink mocks base method.
func (m *MockIClient) SetScenarioShareLink(arg0 context.Context, arg1 *mystery_place.SetScenarioShareLinkReq, arg2 ...grpc.CallOption) (*mystery_place.SetScenarioShareLinkResp, error) {
    m.ctrl.T.Helper()
    varargs := []interface{}{arg0, arg1}
    for _, a := range arg2 {
        varargs = append(varargs, a)
    }
    ret := m.ctrl.Call(m, "SetScenarioShareLink", varargs...)
    ret0, _ := ret[0].(*mystery_place.SetScenarioShareLinkResp)
    ret1, _ := ret[1].(error)
    return ret0, ret1
}

// SetScenarioShareLink indicates an expected call of SetScenarioShareLink.
func (mr *MockIClientMockRecorder) SetScenarioShareLink(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
    mr.mock.ctrl.T.Helper()
    varargs := append([]interface{}{arg0, arg1}, arg2...)
    return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetScenarioShareLink", reflect.TypeOf((*MockIClient)(nil).SetScenarioShareLink), varargs...)
}

// Stub mocks base method.
func (m *MockIClient) Stub() interface{} {
    m.ctrl.T.Helper()
    ret := m.ctrl.Call(m, "Stub")
    ret0, _ := ret[0].(interface{})
    return ret0
}

// Stub indicates an expected call of Stub.
func (mr *MockIClientMockRecorder) Stub() *gomock.Call {
    mr.mock.ctrl.T.Helper()
    return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Stub", reflect.TypeOf((*MockIClient)(nil).Stub))
}

// UpdatePlaymateTag mocks base method.
func (m *MockIClient) UpdatePlaymateTag(arg0 context.Context, arg1 *mystery_place.UpdatePlaymateTagReq, arg2 ...grpc.CallOption) (*mystery_place.UpdatePlaymateTagResp, error) {
    m.ctrl.T.Helper()
    varargs := []interface{}{arg0, arg1}
    for _, a := range arg2 {
        varargs = append(varargs, a)
    }
    ret := m.ctrl.Call(m, "UpdatePlaymateTag", varargs...)
    ret0, _ := ret[0].(*mystery_place.UpdatePlaymateTagResp)
    ret1, _ := ret[1].(error)
    return ret0, ret1
}

// UpdatePlaymateTag indicates an expected call of UpdatePlaymateTag.
func (mr *MockIClientMockRecorder) UpdatePlaymateTag(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
    mr.mock.ctrl.T.Helper()
    varargs := append([]interface{}{arg0, arg1}, arg2...)
    return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdatePlaymateTag", reflect.TypeOf((*MockIClient)(nil).UpdatePlaymateTag), varargs...)
}

// UpdateScenarioComment mocks base method.
func (m *MockIClient) UpdateScenarioComment(arg0 context.Context, arg1 *mystery_place.UpdateScenarioCommentReq, arg2 ...grpc.CallOption) (*mystery_place.UpdateScenarioCommentResp, error) {
    m.ctrl.T.Helper()
    varargs := []interface{}{arg0, arg1}
    for _, a := range arg2 {
        varargs = append(varargs, a)
    }
    ret := m.ctrl.Call(m, "UpdateScenarioComment", varargs...)
    ret0, _ := ret[0].(*mystery_place.UpdateScenarioCommentResp)
    ret1, _ := ret[1].(error)
    return ret0, ret1
}

// UpdateScenarioComment indicates an expected call of UpdateScenarioComment.
func (mr *MockIClientMockRecorder) UpdateScenarioComment(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
    mr.mock.ctrl.T.Helper()
    varargs := append([]interface{}{arg0, arg1}, arg2...)
    return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateScenarioComment", reflect.TypeOf((*MockIClient)(nil).UpdateScenarioComment), varargs...)
}

// UpsertScenarioInfo mocks base method.
func (m *MockIClient) UpsertScenarioInfo(arg0 context.Context, arg1 *mystery_place.UpsertScenarioInfoReq, arg2 ...grpc.CallOption) (*mystery_place.UpsertScenarioInfoResp, error) {
    m.ctrl.T.Helper()
    varargs := []interface{}{arg0, arg1}
    for _, a := range arg2 {
        varargs = append(varargs, a)
    }
    ret := m.ctrl.Call(m, "UpsertScenarioInfo", varargs...)
    ret0, _ := ret[0].(*mystery_place.UpsertScenarioInfoResp)
    ret1, _ := ret[1].(error)
    return ret0, ret1
}

// UpsertScenarioInfo indicates an expected call of UpsertScenarioInfo.
func (mr *MockIClientMockRecorder) UpsertScenarioInfo(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
    mr.mock.ctrl.T.Helper()
    varargs := append([]interface{}{arg0, arg1}, arg2...)
    return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpsertScenarioInfo", reflect.TypeOf((*MockIClient)(nil).UpsertScenarioInfo), varargs...)
}
