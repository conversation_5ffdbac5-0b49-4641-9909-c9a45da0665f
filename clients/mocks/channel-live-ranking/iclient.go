// Code generated by MockGen. DO NOT EDIT.
// Source: /Users/<USER>/project/quicksilver/clients/channel-live-ranking/iclient.go

// Package channelliveranking is a generated GoMock package.
package channelliveranking

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	client "golang.52tt.com/pkg/client"
	protocol "golang.52tt.com/pkg/protocol"
	channel_live_ranking "golang.52tt.com/protocol/services/channel-live-ranking"
)

// MockIClient is a mock of IClient interface.
type MockIClient struct {
	ctrl     *gomock.Controller
	recorder *MockIClientMockRecorder
}

// MockIClientMockRecorder is the mock recorder for MockIClient.
type MockIClientMockRecorder struct {
	mock *MockIClient
}

// NewMockIClient creates a new mock instance.
func NewMockIClient(ctrl *gomock.Controller) *MockIClient {
	mock := &MockIClient{ctrl: ctrl}
	mock.recorder = &MockIClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIClient) EXPECT() *MockIClientMockRecorder {
	return m.recorder
}

// BatchGetAnchorHonorNameplate mocks base method.
func (m *MockIClient) BatchGetAnchorHonorNameplate(ctx context.Context, opUid uint32, actorUidList []uint32) (*channel_live_ranking.BatchGetAnchorHonorNameplateResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetAnchorHonorNameplate", ctx, opUid, actorUidList)
	ret0, _ := ret[0].(*channel_live_ranking.BatchGetAnchorHonorNameplateResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// BatchGetAnchorHonorNameplate indicates an expected call of BatchGetAnchorHonorNameplate.
func (mr *MockIClientMockRecorder) BatchGetAnchorHonorNameplate(ctx, opUid, actorUidList interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetAnchorHonorNameplate", reflect.TypeOf((*MockIClient)(nil).BatchGetAnchorHonorNameplate), ctx, opUid, actorUidList)
}

// CC mocks base method.
func (m *MockIClient) CC() client.Conn {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CC")
	ret0, _ := ret[0].(client.Conn)
	return ret0
}

// CC indicates an expected call of CC.
func (mr *MockIClientMockRecorder) CC() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CC", reflect.TypeOf((*MockIClient)(nil).CC))
}

// Close mocks base method.
func (m *MockIClient) Close() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Close")
}

// Close indicates an expected call of Close.
func (mr *MockIClientMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockIClient)(nil).Close))
}

// GetAnchorHonorNameplate mocks base method.
func (m *MockIClient) GetAnchorHonorNameplate(ctx context.Context, opUid, actorUid uint32) (*channel_live_ranking.GetAnchorHonorNameplateResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAnchorHonorNameplate", ctx, opUid, actorUid)
	ret0, _ := ret[0].(*channel_live_ranking.GetAnchorHonorNameplateResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetAnchorHonorNameplate indicates an expected call of GetAnchorHonorNameplate.
func (mr *MockIClientMockRecorder) GetAnchorHonorNameplate(ctx, opUid, actorUid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAnchorHonorNameplate", reflect.TypeOf((*MockIClient)(nil).GetAnchorHonorNameplate), ctx, opUid, actorUid)
}

// GetRankingList mocks base method.
func (m *MockIClient) GetRankingList(ctx context.Context, opUid uint32, req *channel_live_ranking.GetRankingListReq) (*channel_live_ranking.GetRankingListResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRankingList", ctx, opUid, req)
	ret0, _ := ret[0].(*channel_live_ranking.GetRankingListResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetRankingList indicates an expected call of GetRankingList.
func (mr *MockIClientMockRecorder) GetRankingList(ctx, opUid, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRankingList", reflect.TypeOf((*MockIClient)(nil).GetRankingList), ctx, opUid, req)
}

// GiveAnchorHonorNameplate mocks base method.
func (m *MockIClient) GiveAnchorHonorNameplate(ctx context.Context, opUid, actorUid, honorId, expiredTs uint32) (*channel_live_ranking.GiveAnchorHonorNameplateResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GiveAnchorHonorNameplate", ctx, opUid, actorUid, honorId, expiredTs)
	ret0, _ := ret[0].(*channel_live_ranking.GiveAnchorHonorNameplateResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GiveAnchorHonorNameplate indicates an expected call of GiveAnchorHonorNameplate.
func (mr *MockIClientMockRecorder) GiveAnchorHonorNameplate(ctx, opUid, actorUid, honorId, expiredTs interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GiveAnchorHonorNameplate", reflect.TypeOf((*MockIClient)(nil).GiveAnchorHonorNameplate), ctx, opUid, actorUid, honorId, expiredTs)
}

// Stub mocks base method.
func (m *MockIClient) Stub() interface{} {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Stub")
	ret0, _ := ret[0].(interface{})
	return ret0
}

// Stub indicates an expected call of Stub.
func (mr *MockIClientMockRecorder) Stub() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Stub", reflect.TypeOf((*MockIClient)(nil).Stub))
}
