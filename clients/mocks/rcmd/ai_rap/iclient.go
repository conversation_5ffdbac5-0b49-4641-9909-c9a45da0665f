// Code generated by MockGen. DO NOT EDIT.
// Source: /Users/<USER>/project/quicksilver/clients/rcmd/ai_rap/iclient.go

// Package ai_rap is a generated GoMock package.
package ai_rap

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	client "golang.52tt.com/pkg/client"
	ai_rap "golang.52tt.com/protocol/services/ai_voice/ai_rap"
)

// MockIClient is a mock of IClient interface.
type MockIClient struct {
	ctrl     *gomock.Controller
	recorder *MockIClientMockRecorder
}

// MockIClientMockRecorder is the mock recorder for MockIClient.
type MockIClientMockRecorder struct {
	mock *MockIClient
}

// NewMockIClient creates a new mock instance.
func NewMockIClient(ctrl *gomock.Controller) *MockIClient {
	mock := &MockIClient{ctrl: ctrl}
	mock.recorder = &MockIClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIClient) EXPECT() *MockIClientMockRecorder {
	return m.recorder
}

// AIRapVoiceComposed mocks base method.
func (m *MockIClient) AIRapVoiceComposed(ctx context.Context, in *ai_rap.CompletedVoiceEvent) (*ai_rap.CompletedVoiceEventRsp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AIRapVoiceComposed", ctx, in)
	ret0, _ := ret[0].(*ai_rap.CompletedVoiceEventRsp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AIRapVoiceComposed indicates an expected call of AIRapVoiceComposed.
func (mr *MockIClientMockRecorder) AIRapVoiceComposed(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AIRapVoiceComposed", reflect.TypeOf((*MockIClient)(nil).AIRapVoiceComposed), ctx, in)
}

// CC mocks base method.
func (m *MockIClient) CC() client.Conn {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CC")
	ret0, _ := ret[0].(client.Conn)
	return ret0
}

// CC indicates an expected call of CC.
func (mr *MockIClientMockRecorder) CC() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CC", reflect.TypeOf((*MockIClient)(nil).CC))
}

// Close mocks base method.
func (m *MockIClient) Close() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Close")
}

// Close indicates an expected call of Close.
func (mr *MockIClientMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockIClient)(nil).Close))
}

// SongTemplate mocks base method.
func (m *MockIClient) SongTemplate(ctx context.Context, items []*ai_rap.ItemSong) (*ai_rap.SongTemplateRsp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SongTemplate", ctx, items)
	ret0, _ := ret[0].(*ai_rap.SongTemplateRsp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SongTemplate indicates an expected call of SongTemplate.
func (mr *MockIClientMockRecorder) SongTemplate(ctx, items interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SongTemplate", reflect.TypeOf((*MockIClient)(nil).SongTemplate), ctx, items)
}

// Stub mocks base method.
func (m *MockIClient) Stub() interface{} {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Stub")
	ret0, _ := ret[0].(interface{})
	return ret0
}

// Stub indicates an expected call of Stub.
func (mr *MockIClientMockRecorder) Stub() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Stub", reflect.TypeOf((*MockIClient)(nil).Stub))
}
