package accountlogic

import (
	"crypto/tls"
	"errors"
	"fmt"
	"github.com/stretchr/testify/assert"
	grpcClient "golang.52tt.com/pkg/foundation/grpc/client"
	ga "golang.52tt.com/protocol/app"
	statuspb "golang.52tt.com/protocol/app/api/status"
	"golang.52tt.com/protocol/app/contact"
	tokenv2 "golang.52tt.com/protocol/services/token.v2"
	"golang.org/x/net/context"
	"google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/credentials"
	"google.golang.org/grpc/metadata"
	"google.golang.org/grpc/status"
	"testing"
)

var (
	//// 云开发
	defaultTokenServiceAddress = "*************:80"
	defaultLogicApiAddress     = "dev-apiv2.ttyuyin.com:443"

	// 云测
	// defaultTokenServiceAddress = "************:80"
	// defaultLogicApiAddress     = "testing-apiv2.ttyuyin.com:443"

	supportGrpcError   = false
	supportTraceError  = false
	supportTrailerOnly = false

	printHeaderAndTrailer = false

	trafficMark = "quicksilver-fault-inject-1"

	method = "/ga.api.account.AccountLogic/GetUserDetail"
	//uid = uint32(2196923)
	targetAccount = "tt*********"
	uid           = uint32(2208046)
)

type LogicClient struct{}

func (c *LogicClient) newGrpcConn(ctx context.Context, addr string, opts ...grpc.DialOption) (*grpc.ClientConn, error) {
	cc, err := grpc.DialContext(ctx, addr, opts...)
	if err != nil {
		return nil, err
	}
	return cc, nil
}

func (c *LogicClient) getToken(ctx context.Context, uid uint32) (string, error) {
	cc, err := c.newGrpcConn(ctx, defaultTokenServiceAddress,
		grpc.WithBlock(), grpc.WithInsecure(), grpc.WithAuthority("token-v2.52tt.local"),
		grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor),
	)
	if err != nil {
		return "", err
	}

	client := tokenv2.NewTokenClient(cc)
	resp, err := client.GrantToken(context.Background(), &tokenv2.GrantTokenReq{
		TokenInfo: &tokenv2.TokenInfoV2{
			Uid: uid,
		},
	})
	if err != nil {
		return "", err
	}
	return resp.GetAccessToken().GetToken(), nil
}

func unaryClientInterceptor(ctx context.Context, method string, req, reply interface{}, cc *grpc.ClientConn, invoker grpc.UnaryInvoker, opts ...grpc.CallOption) error {
	var (
		header  metadata.MD
		trailer metadata.MD
	)

	opts = append(opts, grpc.Header(&header), grpc.Trailer(&trailer))

	err := invoker(ctx, method, req, reply, cc, opts...)
	if err != nil {
		if testTraceError(header, trailer) != nil {
			return errors.New("Not found trace")
		}

		if testTrailerOnly(header, trailer) != nil {
			return errors.New("check trailer only failed")
		}
	}

	fmt.Println("---- resp head |")
	for name, vals := range header {
		//if !strings.HasPrefix(name, "x-") {
		//	continue
		//}
		if printHeaderAndTrailer {
			fmt.Printf("[head] %-20s -> %v\n", name, vals)
		}
	}
	for name, vals := range trailer {
		//if !strings.HasPrefix(name, "x-qw-common-code") && !strings.HasPrefix(name, "x-server-error-trace") {
		//	continue
		//}
		if printHeaderAndTrailer {
			fmt.Printf("[trailer] %-20s -> %v\n", name, vals)
		}
	}


	return err
}

func testTraceError(header, trailer metadata.MD) error {
	serverErrorTrace := ""
	for name, vals := range header {
		if name == "x-server-error-trace" {
			serverErrorTrace = vals[0]
			break
		}
	}
	for name, vals := range trailer {
		if name == "x-server-error-trace" {
			serverErrorTrace = vals[0]
			break
		}
	}

	if supportTraceError && serverErrorTrace == "" {
		fmt.Println("x-server-error-trace not found", serverErrorTrace)
		return errors.New("Not found trace")
	} else {
		fmt.Println("x-server-error-trace:", serverErrorTrace)
		return nil
	}

	return nil
}

func testTrailerOnly(header, trailer metadata.MD) error {
	for name, vals := range header {
		if name == "x-qw-common-code" && supportTrailerOnly {
			fmt.Println("found x-qw-common-code in header:", vals)
			return errors.New(fmt.Sprintf("found x-qw-common-code in header:%s", vals))
		}
	}
	for name, vals := range trailer {
		if name == "x-qw-common-code" {
			fmt.Println("found x-qw-common-code in trailer:", vals)
			break
		}
	}

	return nil
}

func initClientAndRequest() (*LogicClient, *contact.GetUserDetailReq, *contact.GetUserDetailResp) {
	return &LogicClient{},
		&contact.GetUserDetailReq{
			BaseReq:       &ga.BaseReq{},
			TargetAccount: "xxxxxxx",
		},
		&contact.GetUserDetailResp{}
}

func newGrpcConn(ctx context.Context, c *LogicClient, address string) *grpc.ClientConn {
	cc, err := c.newGrpcConn(ctx, address, grpc.WithBlock(), grpc.WithTransportCredentials(credentials.NewTLS(&tls.Config{})),
		grpc.WithUnaryInterceptor(unaryClientInterceptor),
	)
	if err != nil {
		fmt.Println("create grpc conn error:", err)
		panic(err)
	}

	return cc
}

func getContext(c *LogicClient, trafficMark string) context.Context {
	ctx := context.Background()
	if len(trafficMark) > 0 {
		ctx = metadata.AppendToOutgoingContext(ctx, "x-qw-traffic-mark", trafficMark)
	}

	if supportGrpcError {
		ctx = metadata.AppendToOutgoingContext(ctx, "x-tmp-support-grpc-error-v2", "true")
	}

	if supportTrailerOnly {
		ctx = metadata.AppendToOutgoingContext(ctx, "x-tmp-support-trailer-only", "true")
	}

	token, err := c.getToken(ctx, uid)
	if err != nil {
		fmt.Println("token err:", err)
		panic(err)
	}
	ctx = metadata.AppendToOutgoingContext(ctx, "authorization", "Bearer "+token, "x-tt-market", "1")

	return ctx
}

// TestAccountLogicSupportTraceError 确保服务端已启用trace错误
func TestAccountLogicSupportTraceError(t *testing.T) {
	c, req, resp := initClientAndRequest()

	fmt.Println("-------- with head x-tmp-support-trace-error --------------")
	req.TargetAccount = "xxxx"

	cc := newGrpcConn(context.Background(), c, defaultLogicApiAddress)
	supportGrpcError, supportTraceError, supportTrailerOnly = true, true, true
	ctx := getContext(c, trafficMark)

	err := cc.Invoke(ctx, method, req, resp)
	assert.NotNil(t, err)
	if err != nil {
		sts, _ := status.FromError(err)
		assert.Equal(t, sts.Code(), codes.FailedPrecondition)
		if 0 != len(sts.Details()) {
			// fmt.Println("grpc details:", reflect.TypeOf(sts.Details()[0]), utils.ToFormatJson(sts.Details()[0]))
			detail := sts.Details()[0]
			commonStatus := detail.(*statuspb.CommonStatus)
			assert.Equal(t, commonStatus.Code, int32(-103))
			fmt.Printf("grpc status:%d, msg:%s, common status code:%d, msg:%s\n", sts.Code(), sts.Message(), commonStatus.Code, commonStatus.Message)
		}
	} else {
		// fmt.Println("resp :", reflect.TypeOf(resp), utils.ToFormatJson(resp))
		assert.Equal(t, resp.GetContact().GetAccountAlias(), "*********")
	}
}

// TestAccountLogicSupportTraceErrorAndClientNotSupportTraceError 确保服务端已启用trace错误
func TestAccountLogicSupportTraceErrorAndClientNotSupportTraceError(t *testing.T) {
	c, req, resp := initClientAndRequest()

	fmt.Println("-------- without head x-tmp-support-trace-error --------------")
	req.TargetAccount = "xxxx"

	cc := newGrpcConn(context.Background(), c, defaultLogicApiAddress)
	supportGrpcError, supportTraceError, supportTrailerOnly = true, false, false
	ctx := getContext(c, trafficMark)

	err := cc.Invoke(ctx, method, req, resp)
	assert.NotNil(t, err)
	if err != nil {
		sts, _ := status.FromError(err)
		assert.Equal(t, sts.Code(), codes.FailedPrecondition)
		if 0 != len(sts.Details()) {
			// fmt.Println("grpc details:", reflect.TypeOf(sts.Details()[0]), utils.ToFormatJson(sts.Details()[0]))
			detail := sts.Details()[0]
			commonStatus := detail.(*statuspb.CommonStatus)
			assert.Equal(t, commonStatus.Code, int32(-103))
			fmt.Printf("grpc status:%d, msg:%s, common status code:%d, msg:%s\n", sts.Code(), sts.Message(), commonStatus.Code, commonStatus.Message)
		}
	} else {
		// fmt.Println("resp :", reflect.TypeOf(resp), utils.ToFormatJson(resp))
		assert.Equal(t, resp.GetContact().GetAccountAlias(), "*********")
	}
}

// TestAccountLogicSupportGrpcError 确保服务端已启用grpc标准错误
func TestAccountLogicSupportGrpcError(t *testing.T) {
	c, req, resp := initClientAndRequest()

	fmt.Println("-------- with head x-tmp-support-grpc-error --------------")
	req.TargetAccount = "xxxx"

	cc := newGrpcConn(context.Background(), c, defaultLogicApiAddress)
	supportGrpcError, supportTraceError, supportTrailerOnly = true, true, false
	ctx := getContext(c, trafficMark)

	err := cc.Invoke(ctx, method, req, resp)
	assert.NotNil(t, err)
	if err != nil {
		sts, _ := status.FromError(err)
		assert.Equal(t, sts.Code(), codes.FailedPrecondition)
		if 0 != len(sts.Details()) {
			// fmt.Println("grpc details:", reflect.TypeOf(sts.Details()[0]), utils.ToFormatJson(sts.Details()[0]))
			detail := sts.Details()[0]
			commonStatus := detail.(*statuspb.CommonStatus)
			assert.Equal(t, commonStatus.Code, int32(-103))
			fmt.Printf("grpc status:%d, msg:%s, common status code:%d, msg:%s\n", sts.Code(), sts.Message(), commonStatus.Code, commonStatus.Message)
		}
	} else {
		// fmt.Println("resp :", reflect.TypeOf(resp), utils.ToFormatJson(resp))
		assert.Equal(t, resp.GetContact().GetAccountAlias(), "*********")
	}
}

// TestAccountLogicSupportGrpcErrorAndClientNotSupportGrpcError 确保服务端已启用grpc标准错误
func TestAccountLogicSupportGrpcErrorAndClientNotSupportGrpcError(t *testing.T) {
	c, req, resp := initClientAndRequest()

	fmt.Println("-------- without head x-tmp-support-grpc-error --------------")
	req.TargetAccount = "xxxx"

	cc := newGrpcConn(context.Background(), c, defaultLogicApiAddress)
	supportGrpcError, supportTraceError, supportTrailerOnly = false, false, false
	ctx := getContext(c, trafficMark)

	err := cc.Invoke(ctx, method, req, resp)
	assert.NotNil(t, err)
	if err != nil {
		sts, _ := status.FromError(err)
		assert.Equal(t, sts.Code(), codes.Internal) // c++返回空pb，会导致grpc客户端返回Internal错误
	} else {
		// fmt.Println("resp :", reflect.TypeOf(resp), utils.ToFormatJson(resp))
		assert.Equal(t, resp.GetContact().GetAccountAlias(), "*********")
	}
}

func TestAccountLogicTrailerOnly(t *testing.T) {
	c, req, resp := initClientAndRequest()

	fmt.Println("-------- with head x-tmp-support-grpc-error and x-tmp-support-trailer-only --------------")
	req.TargetAccount = "xxx"
	// req.TargetAccount = targetAccount

	cc := newGrpcConn(context.Background(), c, defaultLogicApiAddress)
	supportGrpcError, supportTraceError, supportTrailerOnly = true, true, true
	ctx := getContext(c, trafficMark)

	err := cc.Invoke(ctx, method, req, resp)
	assert.NotNil(t, err)
	if err != nil {
		sts, _ := status.FromError(err)
		assert.Equal(t, sts.Code(), codes.FailedPrecondition)
		if 0 != len(sts.Details()) {
			// fmt.Println("grpc details:", reflect.TypeOf(sts.Details()[0]), utils.ToFormatJson(sts.Details()[0]))
			detail := sts.Details()[0]
			commonStatus := detail.(*statuspb.CommonStatus)
			assert.Equal(t, commonStatus.Code, int32(-103))
			fmt.Printf("grpc status:%d, msg:%s, common status code:%d, msg:%s\n", sts.Code(), sts.Message(), commonStatus.Code, commonStatus.Message)
		}
	} else {
		// fmt.Println("resp :", reflect.TypeOf(resp), utils.ToFormatJson(resp))
		assert.Equal(t, resp.GetContact().GetAccountAlias(), "*********")
		fmt.Printf("targetAccount:%s, account alias:%s\n", req.GetTargetAccount(), resp.GetContact().GetAccountAlias())
	}
}

func TestAccountLogicTrailerOnlyAndServerReturnOK(t *testing.T) {
	c, req, resp := initClientAndRequest()

	fmt.Println("-------- with head x-tmp-support-grpc-error and x-tmp-support-trailer-only --------------")
	// req.TargetAccount = "xxx"
	req.TargetAccount = targetAccount

	cc := newGrpcConn(context.Background(), c, defaultLogicApiAddress)
	supportGrpcError, supportTraceError, supportTrailerOnly = true, true, true
	ctx := getContext(c, trafficMark)

	err := cc.Invoke(ctx, method, req, resp)
	assert.Nil(t, err)
	if err != nil {
		sts, _ := status.FromError(err)
		assert.Equal(t, sts.Code(), codes.FailedPrecondition)
		if 0 != len(sts.Details()) {
			// fmt.Println("grpc details:", reflect.TypeOf(sts.Details()[0]), utils.ToFormatJson(sts.Details()[0]))
			detail := sts.Details()[0]
			commonStatus := detail.(*statuspb.CommonStatus)
			assert.Equal(t, commonStatus.Code, int32(-103))
			fmt.Printf("grpc status:%d, msg:%s, common status code:%d, msg:%s\n", sts.Code(), sts.Message(), commonStatus.Code, commonStatus.Message)
		}
	} else {
		// fmt.Println("resp :", reflect.TypeOf(resp), utils.ToFormatJson(resp))
		assert.Equal(t, resp.GetContact().GetAccountAlias(), "*********")
		fmt.Printf("targetAccount:%s, account alias:%s\n", req.GetTargetAccount(), resp.GetContact().GetAccountAlias())
	}
}
//// 云测网关默认打开trailer only 开关，该case无法通过网关验证
//// TestAccountLogicTrailerOnlyAndClientNotSupportTrailerOnly
//func TestAccountLogicTrailerOnlyAndClientNotSupportTrailerOnly(t *testing.T) {
//	c, req, resp := initClientAndRequest()
//
//	fmt.Println("-------- with head x-tmp-support-grpc-error and without head x-tmp-support-trailer-only --------------")
//	req.TargetAccount = "xxx"
//	// req.TargetAccount = targetAccount
//
//	cc := newGrpcConn(context.Background(), c, defaultLogicApiAddress)
//	supportGrpcError, supportTraceError, supportTrailerOnly = true, true, false
//	ctx := getContext(c, trafficMark)
//
//	err := cc.Invoke(ctx, method, req, resp)
//	assert.NotNil(t, err)
//	if err != nil {
//		sts, _ := status.FromError(err)
//		assert.Equal(t, sts.Code(), codes.FailedPrecondition)
//		if 0 != len(sts.Details()) {
//			// fmt.Println("grpc details:", reflect.TypeOf(sts.Details()[0]), utils.ToFormatJson(sts.Details()[0]))
//			detail := sts.Details()[0]
//			commonStatus := detail.(*statuspb.CommonStatus)
//			assert.Equal(t, commonStatus.Code, int32(-103))
//			fmt.Printf("grpc status:%d, msg:%s, common status code:%d, msg:%s\n", sts.Code(), sts.Message(), commonStatus.Code, commonStatus.Message)
//		}
//	} else {
//		// fmt.Println("resp :", reflect.TypeOf(resp), utils.ToFormatJson(resp))
//		assert.Equal(t, resp.GetContact().GetAccountAlias(), "*********")
//		fmt.Printf("targetAccount:%s, account alias:%s\n", req.GetTargetAccount(), resp.GetContact().GetAccountAlias())
//	}
//}
//

// TestAccountLogicSupportTraceError 确保服务端已启用trace错误
func TestAccountLogicIllegalHeaderValue(t *testing.T) {
	c, req, resp := initClientAndRequest()

	fmt.Println("-------- with illegal head value --------------")
	req.TargetAccount = "xxxx"

	cc := newGrpcConn(context.Background(), c, defaultLogicApiAddress)
	supportGrpcError, supportTraceError, supportTrailerOnly = true, true, true
	ctx := getContext(c, trafficMark)
	ctx = metadata.AppendToOutgoingContext(ctx, "req_seqid", "A946026A-7999-44A3-A41C-CCA71446D\ufffe9999")
	ctx = metadata.AppendToOutgoingContext(ctx, "x-qw-aaaaaaa", "A946026A-\ufffe9999")


	err := cc.Invoke(ctx, method, req, resp)
	assert.NotNil(t, err)
	if err != nil {
		sts, _ := status.FromError(err)
		assert.Equal(t, sts.Code(), codes.FailedPrecondition)
		if 0 != len(sts.Details()) {
			// fmt.Println("grpc details:", reflect.TypeOf(sts.Details()[0]), utils.ToFormatJson(sts.Details()[0]))
			detail := sts.Details()[0]
			commonStatus := detail.(*statuspb.CommonStatus)
			assert.Equal(t, commonStatus.Code, int32(-103))
			fmt.Printf("grpc status:%d, msg:%s, common status code:%d, msg:%s\n", sts.Code(), sts.Message(), commonStatus.Code, commonStatus.Message)
		}
	} else {
		// fmt.Println("resp :", reflect.TypeOf(resp), utils.ToFormatJson(resp))
		assert.Equal(t, resp.GetContact().GetAccountAlias(), "*********")
	}
}


