package muse_social_community_achieve

import (
	"context"
	"golang.52tt.com/pkg/client"
	"golang.52tt.com/pkg/protocol"
	pb "golang.52tt.com/protocol/services/muse-social-community-achieve"
	"google.golang.org/grpc"
)

type IClient interface {
	client.BaseClient

	CheckIn(ctx context.Context,
		socialCommunityId string) (*pb.CheckInResponse, protocol.ServerError)
	GetCheckInSimple(ctx context.Context,
		socialCommunityId string) (*pb.GetCheckInSimpleResponse, protocol.ServerError)

	GetLevelDetail(ctx context.Context, req *pb.GetLevelDetailRequest) (*pb.GetLevelDetailResponse, protocol.ServerError)

	GetSocialCommunityUpdateLevelTip(ctx context.Context, req *pb.GetSocialCommunityUpdateLevelTipRequest) (*pb.GetSocialCommunityUpdateLevelTipResponse, protocol.ServerError)
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli, _ := NewClient(dopts...)
	return cli
}
