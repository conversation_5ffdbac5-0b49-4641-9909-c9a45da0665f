package verifycode

import (
	"golang.52tt.com/pkg/client"
	grpcClient "golang.52tt.com/pkg/foundation/grpc/client"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/tracing"
	tracingGrpc "golang.52tt.com/pkg/tracing/grpc"
	pb "golang.52tt.com/protocol/services/verifycodesvr"
	"golang.org/x/net/context"
	"google.golang.org/grpc"
	"google.golang.org/grpc/metadata"
	"strconv"
)

type CheckResult = pb.CheckResult

const (
	serviceName = "verifycode"

	OK             = pb.CheckResult_ok
	NotExists      = pb.CheckResult_not_exists
	SessionChanged = pb.CheckResult_session_changed
	Incorrect      = pb.CheckResult_incorrect_code
	NotVerified    = pb.CheckResult_not_verified
	Pass           = pb.CheckResult_pass
)

type Client struct {
	client.BaseClient
}

func NewClient(dopts ...grpc.DialOption) *Client {
	return newClient(dopts...)
}

func NewTracedClient(tracer tracing.Tracer) *Client {
	tracerInt := tracingGrpc.TracedUnaryClientInterceptor(
		tracing.LogPayloads(true),
		tracing.UsingTracer(tracer),
	)

	return newClient(grpc.WithUnaryInterceptor(tracerInt))
}

func newClient(dopts ...grpc.DialOption) *Client {

	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(serviceName, func(cc *grpc.ClientConn) interface{} {
			return pb.NewVerifyCodeClient(cc)
		}, dopts...),
	}
}
func (c *Client) typedStub() pb.VerifyCodeClient { return c.Stub().(pb.VerifyCodeClient) }

func (c *Client) CheckValid(ctx context.Context, uid, checkType uint32, session, code string) (*pb.CheckValidResp, protocol.ServerError) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uid))))
	r, err := c.typedStub().CheckValid(ctx, &pb.CheckValidReq{
		Uid:     uid,
		Type:    checkType,
		Session: session,
		Code:    code,
	})
	return r, protocol.ToServerError(err)
}

func (c *Client) CheckVerifyCodeInUse(ctx context.Context, uid uint32) (bool, protocol.ServerError) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uid))))
	r, err := c.typedStub().CheckVerifyCodeGlobalSwitch(ctx, &pb.CheckVerifyCodeGlobalSwitchReq{})
	return r.GetIsOpen(), protocol.ToServerError(err)
}

func (c *Client) CheckVerifyCodeStatus(ctx context.Context, uid, checkType uint32, session string) (*pb.CheckVerifyCodeStatusResp, protocol.ServerError) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uid))))
	r, err := c.typedStub().CheckVerifyCodeStatus(ctx, &pb.CheckVerifyCodeStatusReq{
		Uid:     uid,
		Type:    checkType,
		Session: session,
	})
	return r, protocol.ToServerError(err)
}

func (c *Client) CreateVerifyCodeByKey(ctx context.Context, key string, len, ttl uint32) (string, protocol.ServerError) {
	r, err := c.typedStub().CreateVerifyCodeByKey(ctx, &pb.CreateVerifyCodeByKeyReq{
		Key: key,
		Len: len,
		Ttl: ttl,
	}, grpc.WaitForReady(true))
	return r.GetVerifyCode(), protocol.ToServerError(err)
}

func (c *Client) ValidateVerifyCode(ctx context.Context, key, code string) protocol.ServerError {
	_, err := c.typedStub().ValidateVerifyCode(ctx, &pb.ValidateVerifyCodeReq{
		Key:        key,
		VerifyCode: code,
	}, grpc.WaitForReady(true))
	return protocol.ToServerError(err)
}

func (c *Client) ValidateVerifyCodeV2(ctx context.Context, key, code string, delIfSuccess bool) protocol.ServerError {
	_, err := c.typedStub().ValidateVerifyCode(ctx, &pb.ValidateVerifyCodeReq{
		Key:          key,
		VerifyCode:   code,
		DelIfSuccess: delIfSuccess,
	}, grpc.WaitForReady(true))
	return protocol.ToServerError(err)
}
