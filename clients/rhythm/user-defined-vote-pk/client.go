package userdefinedvotepk

import (
	"context"

	"github.com/grpc-ecosystem/go-grpc-middleware"
	"golang.52tt.com/pkg/client"
	"golang.52tt.com/pkg/foundation/grpc/client"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/tracing"
	traceGRPC "golang.52tt.com/pkg/tracing/grpc"
	pb "golang.52tt.com/protocol/services/rhythm/userdefinedvotepk"
	"google.golang.org/grpc"
)

const (
	serviceName = "rhythm-user-defined-vote-pk"
)

type Client struct {
	client.BaseClient
}

func newClient(dopts ...grpc.DialOption) (*Client, error) {
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(
			serviceName,
			func(cc *grpc.ClientConn) interface{} {
				return pb.NewUserDefinedVotePKClient(cc)
			}, dopts...,
		),
	}, nil
}

func (c *Client) typedStub() pb.UserDefinedVotePKClient { return c.Stub().(pb.UserDefinedVotePKClient) }

func NewTracedClient(t tracing.Tracer, dopts ...grpc.DialOption) (*Client, error) {
	unaryInt := grpc_middleware.ChainUnaryClient(grpcClient.UnaryClientInterceptor, traceGRPC.TracedUnaryClientInterceptor(
		tracing.UsingTracer(t), tracing.LogPayloads(true),
	))
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(unaryInt))
	return newClient(dopts...)
}

func NewClient(dopts ...grpc.DialOption) (*Client, error) {
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return newClient(dopts...)
}

func (c *Client) UserDefinedVote(ctx context.Context, req *pb.UserDefinedVoteReq) (*pb.UserDefinedVoteResp, protocol.ServerError) {
	resp, err := c.typedStub().UserDefinedVote(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) UserDefinedVotePKCancel(ctx context.Context, uid, channelId, startTs uint32) protocol.ServerError {
	_, err := c.typedStub().UserDefinedVotePKCancel(ctx, &pb.UserDefinedVotePKCancelReq{Uid: uid, ChannelId: channelId, StartTimestamp: startTs})
	return protocol.ToServerError(err)
}

func (c *Client) AddVotePkUserTicket(ctx context.Context, cnt, channelId, startTs uint32, uidlist []uint32, now, allExtraVotes, surplusVotes uint32, flag bool) protocol.ServerError {
	_, err := c.typedStub().AddVotePkUserTicket(ctx, &pb.AddVotePkUserTicketReq{
		ChannelId:           channelId,
		StartTimestamp:      startTs,
		UidList:             uidlist,
		Cnt:                 cnt,
		PushVersion:         now,
		UserAddTicketCnt:    allExtraVotes,
		UserRemainTicketCnt: surplusVotes,
		IsEndAct:            flag,
	})
	return protocol.ToServerError(err)
}
func (c *Client) UserDefinedVotePKStart(ctx context.Context, channelId, uid, durationMin, pkType, voteCnt uint32, pkName string, optionList []string) (out *pb.UserDefinedVotePKStartResp, err error) {
	str := make([]*pb.UserDefinedVotePkOptionInfo, 0)

	for _, v := range optionList {
		strOne := &pb.UserDefinedVotePkOptionInfo{}
		strOne.OptionName = v
		str = append(str, strOne)
	}
	resp, err := c.typedStub().UserDefinedVotePKStart(ctx, &pb.UserDefinedVotePKStartReq{
		Uid:         uid,
		ChannelId:   channelId,
		DurationMin: durationMin,
		PkType:      pkType,
		OptionList:  str,
		VoteCnt:     voteCnt,
		PkName:      pkName,
	})
	return resp, protocol.ToServerError(err)

}

func (c *Client) GetUserDefinedVotePK(ctx context.Context, channelId, uid uint32) (out *pb.GetUserDefinedVotePKResp, err error) {
	resp, err := c.typedStub().GetUserDefinedVotePK(ctx, &pb.GetUserDefinedVotePKReq{ChannelId: channelId, Uid: uid})
	return resp, protocol.ToServerError(err)
}

func (c *Client) HasUserDefinedVotePK(ctx context.Context, channelId uint32) (out *pb.HasUserDefinedVotePKResp, err error) {
	resp, err := c.typedStub().HasUserDefinedVotePK(ctx, &pb.HasUserDefinedVotePKReq{ChannelId: channelId})
	return resp, protocol.ToServerError(err)

}
