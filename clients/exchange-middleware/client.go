package exchange_middleware

import (
    "context"
    "golang.52tt.com/pkg/client"
    grpcClient "golang.52tt.com/pkg/foundation/grpc/client"
    pb "golang.52tt.com/protocol/services/exchange-middleware"
    "google.golang.org/grpc"
)

const serviceName = "exchange-middleware"

type ExchangeMiddlewareClient struct {
    client.BaseClient
}

func newExchangeMiddlewareClient(dopts ...grpc.DialOption) (*ExchangeMiddlewareClient, error) {

    return &ExchangeMiddlewareClient{
        BaseClient: client.NewInsecureGRPCClient(
            serviceName,
            func(cc *grpc.ClientConn) interface{} {
                return pb.NewExchangeMiddleWareClient(cc)
            }, dopts...,
        ),
    }, nil
}

func NewExchangeMiddlewareClient(dopts ...grpc.DialOption) (*ExchangeMiddlewareClient, error) {
    dopts = append(dopts, grpc.WithInsecure(), grpc.WithBlock(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
    return newExchangeMiddlewareClient(dopts...)
}

func (c *ExchangeMiddlewareClient) Exchange(ctx context.Context, req *pb.ExchangeReq) (*pb.ExchangeResp, error) {
    ct := c.Stub().(pb.ExchangeMiddleWareClient)
    return ct.Exchange(ctx, req)
}

func (c *ExchangeMiddlewareClient) GetBalance(ctx context.Context, req *pb.GetBalanceReq) (*pb.GetBalanceResp, error) {
    ct := c.Stub().(pb.ExchangeMiddleWareClient)
    return ct.GetBalance(ctx, req)
}




