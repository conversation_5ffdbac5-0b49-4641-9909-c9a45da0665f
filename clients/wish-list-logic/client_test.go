package wishlistlogic

import (
	"context"
	. "github.com/smartystreets/goconvey/convey"
	pb "golang.52tt.com/protocol/app/wishlistlogic"
	"google.golang.org/grpc/grpclog"
	"os"
	"testing"
)

func init() {
	grpclog.SetLoggerV2(grpclog.NewLoggerV2(os.Stdout, os.Stdout, os.Stdout))
}

func TestGetHelloWorld(t *testing.T) {

	<PERSON><PERSON>("GetWishListLogic", t, func() {
		client, err := NewClient()
		So(err, ShouldBeNil)
		var req pb.WishListLogicReq
		resp, err := client.GetWishListLogic(context.Background(), req)
		So(err, ShouldBeNil)

		t.Logf("GetWishListLogic %+v", resp)
	})

}
