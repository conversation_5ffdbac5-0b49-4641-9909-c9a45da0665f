// Code generated by quicksilver-cli. DO NOT EDIT.
package perfect_match

import(
	"golang.52tt.com/pkg/client"
	"google.golang.org/grpc"
	context "context"
	pb "golang.52tt.com/protocol/services/rcmd/perfect_match"
	protocol "golang.52tt.com/pkg/protocol"
)

type IClient interface {
	client.BaseClient
	AckMatchedUsers(ctx context.Context, req pb.AckMatchedUsersReq) (*pb.AckMatchedUsersRsp,protocol.ServerError)
	Cancel(ctx context.Context, req pb.CancelReq) (*pb.CancelRsp,protocol.ServerError)
	GetMatchedUsers(ctx context.Context, req pb.GetMatchedUsersReq) (*pb.GetMatchedUsersRsp,protocol.ServerError)
	GetQuestions(ctx context.Context, req pb.GetQuestionsReq) (*pb.GetQuestionsRsp,protocol.ServerError)
	GetResult(ctx context.Context, req pb.GetResultReq) (*pb.GetResultRsp,protocol.ServerError)
	GetWaitingTime(ctx context.Context, req pb.GetWaitingTimeReq) (*pb.GetWaitingTimeRsp,protocol.ServerError)
	Start(ctx context.Context, req pb.StartReq) (*pb.StartRsp,protocol.ServerError)
	Inspect(ctx context.Context, req pb.InspectReq) (*pb.InspectRsp, protocol.ServerError)
	GetMatchInfo(ctx context.Context, req pb.GetMatchInfoReq) (*pb.GetMatchInfoRsp, protocol.ServerError)
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli, _ := NewClient(dopts...)
	return cli
}
