package censoring_proxy

import (
	"golang.52tt.com/pkg/client"
	grpcClient "golang.52tt.com/pkg/foundation/grpc/client"
	censoringProxy "golang.52tt.com/protocol/services/cybros/arbiter/v2"

	"google.golang.org/grpc"
)

const (
	serviceName = "censoring-proxy"
	AppId       = "quicksilver"

	CategoryUgcPost                     = "UGC_POST"
	CategoryUgcComment                  = "UGC_COMMENT"
	CategoryUgcTopic                    = "UGC_DIY_TOPIC"
	CategorySilentUserNickName          = "SILENT_USER_NICK_NAME"
	CategorySilentUserHeadImg           = "SILENT_USER_HEAD_IMG"
	CategoryGuildOrGroupPostNotice      = "GUILD_OR_GROUP_POST_NOTICE"
	CategoryGuildOrGroupPostNoticeIMAGE = "GUILD_OR_GROUP_POST_NOTICE_IMAGE"

	CategoryEmoji = "IM_UPLOAD_STICKER"

	CategoryGuildOrGroupEnterVerifyQuestion = "GUILD_OR_GROUP_ENTER_VERIFY_QUESTION"

	Category_IM_MESSAGE_GROUP          = "IM_MESSAGE_GROUP"
	Category_IM_MESSAGE_STRANGER_TALK  = "IM_STRANGER_TALK"
	Category_IM_MESSAGE_IM_FRIEND_TALK = "IM_FRIEND_TALK"

	CategoryNonPublicUgcPost         = "NON_PUBLIC_UGC_POST"
	CategoryNonPublicUgcPostLongText = "NON_PUBLIC_UGC_POST_LONG_TEXT"
	CategoryNonPublicUgcComment      = "NON_PUBLIC_UGC_COMMENT"
)

type mux struct {
	image     censoringProxy.ImageClient
	audio     censoringProxy.AudioClient
	video     censoringProxy.VideoClient
	text      censoringProxy.TextClient
	censoring censoringProxy.CensoringClient
}

// Client -
type Client struct {
	client.BaseClient
}

func NewClient(dopts ...grpc.DialOption) *Client {
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(
			serviceName,
			func(cc *grpc.ClientConn) interface{} {
				return &mux{
					image:     censoringProxy.NewImageClient(cc),
					audio:     censoringProxy.NewAudioClient(cc),
					video:     censoringProxy.NewVideoClient(cc),
					text:      censoringProxy.NewTextClient(cc),
					censoring: censoringProxy.NewCensoringClient(cc),
				}
			}, dopts...,
		),
	}
}

func (c *Client) Image() censoringProxy.ImageClient         { return c.Stub().(*mux).image }
func (c *Client) Audio() censoringProxy.AudioClient         { return c.Stub().(*mux).audio }
func (c *Client) Video() censoringProxy.VideoClient         { return c.Stub().(*mux).video }
func (c *Client) Text() censoringProxy.TextClient           { return c.Stub().(*mux).text }
func (c *Client) Censoring() censoringProxy.CensoringClient { return c.Stub().(*mux).censoring }
