package pay_reward

import (
	"context"
	"errors"
	"fmt"
	"github.com/allegro/bigcache"
	"github.com/go-redis/redis"
	"github.com/jmoiron/sqlx"
	"gitlab.ttyuyin.com/tt-infra/tyr/log"
	accountCli "golang.52tt.com/clients/account"
	apicenter "golang.52tt.com/clients/apicenter/apiserver"
	backpackgo "golang.52tt.com/clients/backpack-go"
	"golang.52tt.com/clients/currency"
	levelup_present_inner_logic "golang.52tt.com/clients/levelup-present-inner-logic"
	magicSpirit "golang.52tt.com/clients/magic-spirit"
	presend_middleware_client "golang.52tt.com/clients/present-middleware"
	unifiedPay "golang.52tt.com/clients/unified_pay"
	userPresentClient "golang.52tt.com/clients/userpresent"
	"golang.52tt.com/pkg/deal_token"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/protocol/common/status"
	backpackgoPb "golang.52tt.com/protocol/services/backpackgo"
	backpackPB "golang.52tt.com/protocol/services/backpacksvr"
	pb "golang.52tt.com/protocol/services/channel-lottery"
	currencyPB "golang.52tt.com/protocol/services/currencysvr"
	LevelupPresentInnerLogic "golang.52tt.com/protocol/services/levelup-present-inner-logic"
	magic_spirit "golang.52tt.com/protocol/services/magic-spirit"
	present_middlewarePb "golang.52tt.com/protocol/services/present-middleware"
	reconcile_v2 "golang.52tt.com/protocol/services/reconcile-v2"
	"golang.52tt.com/protocol/services/unified_pay"
	presentPb "golang.52tt.com/protocol/services/userpresent"
	"golang.52tt.com/services/channel-lottery/internal/conf/business"
	"golang.52tt.com/services/channel-lottery/internal/model/lottery"
	"golang.52tt.com/services/channel-lottery/utils/mongo"
	"time"
)

const cbDelaySec = int64(180)

type Manager struct {
	// 检查任务数的时间间隔
	ticker *time.Ticker

	cache lottery.ICache
	dao   lottery.IMongo
	mysql lottery.IStore

	presentMiddle presend_middleware_client.IClient
	userPresent   userPresentClient.IClient
	magicSpirit   magicSpirit.IClient
	unifiedPay    unifiedPay.IClient
	currency      currency.IClient
	backpackGo    backpackgo.IClient
	account       accountCli.IClient
	levelUpLogic  levelup_present_inner_logic.IClient
	apiClient     apicenter.IClient
}

// NewPayRewardManager 初始化奖励控制器
func NewPayRewardManager(cmder redis.Cmdable, m mongo.MongoDao, localCache *bigcache.BigCache, mysql *sqlx.DB) (*Manager, error) {
	r := &Manager{
		ticker: time.NewTicker(5 * time.Second),
		cache:  lottery.NewCache(cmder, localCache),
		dao:    lottery.NewMongo(m),
		mysql:  lottery.NewStore(mysql),
	}

	r.presentMiddle, _ = presend_middleware_client.NewClient()
	r.userPresent = userPresentClient.NewClient()
	r.magicSpirit, _ = magicSpirit.NewClient()
	r.unifiedPay, _ = unifiedPay.NewClient()
	r.currency = currency.NewClient()
	r.backpackGo, _ = backpackgo.NewClient()
	r.account, _ = accountCli.NewClient()
	r.levelUpLogic, _ = levelup_present_inner_logic.NewClient()
	r.apiClient = apicenter.NewClient()

	return r, nil
}

func (s *Manager) FreezeConsumeGift(ctx context.Context, info *lottery.LotterInfo) error {
	if info == nil || info.AwardGiftId == 0 || info.Limit == 0 {
		return nil
	}
	price, priceType, err := s.GetPresentPrice(ctx, info.GiftSendType, info.AwardGiftId)
	if err != nil {
		log.ErrorWithCtx(ctx, "FreezeConsumeGift fail to GetPresentPrice. info:%+v, err:%v", info, err)
		return err
	}

	totalPrice := price * info.Limit
	now := time.Unix(time.Now().Unix(), 0)
	orderId := fmt.Sprintf("CLOTTERY_%d_%d_%d", info.Id, info.SponsorUid, info.SponsorChannelId)

	costType := uint32(0)
	if info.GiftSendType == uint32(pb.LotteryGiftSendType_LOTTERY_GIFT_TYPE_BACKPACK) {
		costType = lottery.CostBackpackItem

	} else if info.GiftSendType == uint32(pb.LotteryGiftSendType_LOTTERY_GIFT_TYPE_LUCK) {
		costType = lottery.CostMagicSpiritTBean

	} else if priceType == uint32(presentPb.PresentPriceType_PRESENT_PRICE_TBEAN) {
		costType = lottery.CostTBean

	} else if priceType == uint32(presentPb.PresentPriceType_PRESENT_PRICE_RED_DIAMOND) {
		costType = lottery.CostRedDiamond
	}

	err = s.dao.CreateCostLog(&lottery.UserLotteryCostLog{
		LotteryId:    info.Id,
		OrderId:      orderId,
		Uid:          info.SponsorUid,
		ChannelId:    info.SponsorChannelId,
		CostType:     costType,
		PresentId:    info.AwardGiftId,
		FreezeAmount: info.Limit,
		Price:        price,
		PriceType:    priceType,
		FreezeTime:   now.Unix(),
		Status:       lottery.CostLogStatusInit,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "FreezeConsumeGift fail to CreateCostLog. info:%+v, err:%v", info, err)
		return err
	}

	switch costType {
	case lottery.CostBackpackItem:
		// 冻结背包礼物
		_, err = s.backpackGo.FreezeItem(ctx, backpackgoPb.FreezeItemReq{
			OrderId:    orderId,
			Uid:        info.SponsorUid,
			FreezeTime: uint32(now.Unix()),
			SourceId:   uint32(backpackPB.PackageSourceType_PACKAGE_SOURCE_CHANNEL_LOTTERY),
			OperType:   uint32(backpackPB.LogType_LOG_TYPE_CHANNEL_LOTTERY),
			ItemInfo: &backpackgoPb.ItemInfo{
				ItemId:    info.AwardGiftId,
				ItemType:  uint32(backpackPB.PackageItemType_BACKPACK_PRESENT),
				ItemCount: info.Limit,
			},
			CallBack: &backpackgoPb.Callback{
				Type:      backpackgoPb.Callback_GRPC,
				Url:       "channel-lottery",
				FirstTime: uint32(now.Unix()) + business.GetConfig().Countdown + uint32(cbDelaySec),
			},
		})

	case lottery.CostTBean:
		// 冻结T豆
		_, err = s.unifiedPay.PresetFreeze(ctx, info.SponsorUid, totalPrice, business.GetConfig().GetTBeanAppId(), orderId,
			now.Format("2006-01-02 15:04:05"), "天选之人赠送礼物")

	case lottery.CostMagicSpiritTBean:
		// 冻结T豆-幸运礼物
		_, err = s.unifiedPay.PresetFreezeWithSubId(ctx, info.SponsorUid, totalPrice, business.GetConfig().GetTBeanAppId(), orderId,
			now.Format("2006-01-02 15:04:05"), "天选之人赠送守护精灵", "幸运礼物")

	case lottery.CostRedDiamond:
		// 扣红钻
		err = s.currency.AddUserCurrency(ctx, info.SponsorUid, -int32(totalPrice), orderId, "天选之人扣除", uint32(currencyPB.ADD_CURRENCY_REASON_CHANNEL_LOTTERY))
		_ = s.apiClient.NotifyGrowInfoSync(ctx, info.SponsorUid)

	default:
		return protocol.NewExactServerError(nil, status.ErrChannelLotteryInfoErr, "该礼物配置类型有误")
	}

	if err != nil {
		log.ErrorWithCtx(ctx, "FreezeConsumeGift fail . info:%+v, price:%d, priceType:%d err:%v", info, price, priceType, err)
		return err
	}

	_, err = s.dao.UpdateCostLogStatus(orderId, lottery.CostLogStatusInit, lottery.CostLogStatusFreeze)
	if err != nil {
		log.ErrorWithCtx(ctx, "FreezeConsumeGift fail to UpdateCostLogStatus . info:%+v, price:%d, priceType:%d err:%v", info, price, priceType, err)
	}

	log.InfoWithCtx(ctx, "FreezeConsumeGift info:%+v, price:%d, priceType:%d", info, price, priceType)
	return nil
}

func (s *Manager) CallbackConsumeOrder(ctx context.Context, orderId string) error {
	if orderId == "" {
		return nil
	}

	costLog, err := s.dao.GetLotteryCostLogByOrder(orderId)
	if err != nil {
		log.ErrorWithCtx(ctx, "CallbackConsumeOrder fail to GetLotteryCostLogByOrder. orderId:%s, err:%v", orderId, err)
		return err
	}

	lotteryInfo, err := s.dao.GetLottery(costLog.LotteryId)
	if err != nil {
		log.ErrorWithCtx(ctx, "CallbackConsumeOrder fail to GetLotteryCostLogByOrder. orderId:%s, lotteryId:%d, err:%v", orderId, costLog.LotteryId, err)
		return err
	}

	now := time.Now()
	limitTs := lotteryInfo.BeginTime + int64(lotteryInfo.Countdown) + cbDelaySec

	if costLog.LotteryId != lotteryInfo.Id || limitTs > now.Unix() {
		log.ErrorWithCtx(ctx, "CallbackConsumeOrder fail. orderId:%s, costLog:%+v, limitTs:%d, err:info err",
			orderId, costLog, limitTs)
		return protocol.NewExactServerError(nil, status.ErrChannelLotteryInfoErr, "回调时机或者抽奖消费信息有误")
	}

	preStatus := lotteryInfo.Status
	if preStatus != lottery.RESULT_ERROR && preStatus != lottery.FINISH && preStatus != lottery.BREAK {
		var toStatus int
		if preStatus == lottery.RESULT || len(lotteryInfo.UidList) > 0 {
			toStatus = lottery.FINISH
		} else {
			toStatus = lottery.BREAK
		}

		ok, _, err := s.dao.UpdateLotteryResultWithCheck(lotteryInfo.Id, lotteryInfo.UidList, int(preStatus), toStatus)
		if err != nil {
			log.ErrorWithCtx(ctx, "CallbackConsumeOrder fail to UpdateLotteryResultWithCheck. orderId:%s, lotteryInfo:%+v, err:%v",
				orderId, lotteryInfo, err)
			return err
		}

		if !ok {
			log.ErrorWithCtx(ctx, "CallbackConsumeOrder fail. orderId:%s, costLog:%+v, limitTs:%d, err:lottery status update err",
				orderId, costLog, limitTs)
			return protocol.NewExactServerError(nil, status.ErrChannelLotteryInfoErr, "抽奖信息更新有误")
		}
	}

	// 确认消费
	_, err = s.CommitConsumeGift(ctx, lotteryInfo, costLog)
	if err != nil {
		log.ErrorWithCtx(ctx, "CallbackConsumeOrder fail to CommitConsumeGift. orderId:%s, costLog:%+v, limitTs:%d, err:%v",
			orderId, costLog, limitTs, err)
		return err
	}

	log.InfoWithCtx(ctx, "CallbackConsumeOrder orderId:%s, costLog:%+v", orderId, costLog)
	return nil
}

func (s *Manager) commitTBeanOrder(ctx context.Context, info *lottery.LotterInfo, costLog *lottery.UserLotteryCostLog, commitTime time.Time) (time.Time, string, error) {
	uid := info.SponsorUid
	orderId := costLog.OrderId
	awardAmount := uint32(len(info.UidList))
	dealToken := ""

	TBeanAppId := business.GetConfig().GetTBeanAppId()
	itemName := "天选之人赠送礼物"
	if costLog.CostType == lottery.CostMagicSpiritTBean {
		itemName = "天选之人赠送守护精灵"
	}

	if awardAmount > 0 {
		user, err := s.account.GetUser(ctx, uid)
		if err != nil {
			log.ErrorWithCtx(ctx, "commitTBeanOrder fail to GetUser. info:%+v, err:%v", info, err)
		}

		// 确认扣除T豆
		var ctime string
		ctime, dealToken, err = s.unifiedPay.UnfreezeAndConsume(ctx, &unified_pay.UnfreezeAndConsumeReq{
			AppId:        TBeanAppId,
			Uid:          uid,
			UserName:     user.GetUsername(),
			ItemId:       info.AwardGiftId,
			ItemName:     itemName,
			ItemNum:      awardAmount,
			ItemPrice:    costLog.Price,
			TotalPrice:   costLog.Price * awardAmount,
			Platform:     "0",
			OutTradeNo:   orderId,
			Notes:        "channel-lottery",
			OutOrderTime: commitTime.Format("2006-01-02 15:04:05"),
			RefundPrice:  (info.Limit - awardAmount) * costLog.Price,
		})
		if err != nil {
			log.ErrorWithCtx(ctx, "commitTBeanOrder fail to unifiedPay.UnfreezeAndConsume. orderId:%s, info:%+v, err:%v", orderId, info, err)
			return commitTime, dealToken, err
		}

		t, err2 := time.ParseInLocation("2006-01-02 15:04:05", ctime, time.Local)
		if err2 == nil {
			commitTime = t
		}

	} else {
		// 无获奖需全部回退
		err := s.unifiedPay.UnFreezeAndRefund(ctx, uid, TBeanAppId, orderId)
		if err != nil {
			log.ErrorWithCtx(ctx, "commitTBeanOrder fail to unifiedPay.UnFreezeAndRefund. orderId:%s, info:%+v, err:%v", orderId, info, err)
			return commitTime, dealToken, err
		}
	}

	return commitTime, dealToken, nil
}

func (s *Manager) CommitConsumeGift(ctx context.Context, info *lottery.LotterInfo, costLog *lottery.UserLotteryCostLog) (string, error) {
	if info == nil || info.AwardGiftId == 0 ||
		costLog == nil || costLog.FreezeAmount != info.Limit {
		return "", errors.New("commit check not pass")
	}

	// 测试确认消费失败
	if business.GetConfig().TestPayCommitFail {
		return "", errors.New("test pay commit fail")
	}

	uid := info.SponsorUid
	orderId := costLog.OrderId
	dealToken := ""
	var err error

	commitTime := time.Unix(costLog.CommitTime, 0)
	if costLog.CommitTime == 0 {
		commitTime = time.Unix(time.Now().Unix(), 0)
	}

	awardAmount := uint32(len(info.UidList))
	if awardAmount > info.Limit {
		return dealToken, nil
	}

	switch costLog.CostType {
	case lottery.CostBackpackItem:
		// 确认扣除背包礼物
		resp, err := s.backpackGo.CommitItem(ctx, backpackgoPb.CommitItemReq{
			OrderId:     orderId,
			Uid:         uid,
			FreezeTime:  uint32(costLog.FreezeTime),
			CommitTime:  uint32(commitTime.Unix()),
			CommitCount: awardAmount,
		})
		if err != nil {
			log.ErrorWithCtx(ctx, "CommitConsumeGift fail to backpackGo.CommitItem. orderId:%s, info:%+v, err:%v", orderId, info, err)
			return dealToken, err
		}

		dealToken = resp.GetDealToken()

	case lottery.CostTBean, lottery.CostMagicSpiritTBean:
		// 确认T豆订单
		commitTime, dealToken, err = s.commitTBeanOrder(ctx, info, costLog, commitTime)
		if err != nil {
			log.ErrorWithCtx(ctx, "CommitConsumeGift fail to commitTBeanOrder. orderId:%s, info:%+v, err:%v", orderId, info, err)
			return dealToken, err
		}

	case lottery.CostRedDiamond:
		if awardAmount == info.Limit {
			return dealToken, nil
		}
		// 回滚多扣的红钻
		add := (info.Limit - awardAmount) * costLog.Price
		err := s.currency.AddUserCurrency(ctx, uid, int32(add), orderId+"_rb", "天选之人扣除回滚", uint32(currencyPB.ADD_CURRENCY_REASON_CHANNEL_LOTTERY))
		if err != nil {
			if err.Code() == status.ErrGrowCurrencyAdded {
				return dealToken, nil
			}
			log.ErrorWithCtx(ctx, "CommitConsumeGift fail to currency.AddUserCurrency. orderId:%s, info:%+v, err:%v", orderId, info, err)
			return dealToken, err
		}

		// 触发用户成长信息（经验/财富魅力值/红钻）sync notify通知
		_ = s.apiClient.NotifyGrowInfoSync(ctx, uid)

	default:
		return dealToken, protocol.NewExactServerError(nil, status.ErrChannelLotteryInfoErr, "该礼物配置类型有误")
	}

	err = s.dao.UpdateCostLogCommit(orderId, dealToken, lottery.CostLogStatusCommit, awardAmount, commitTime)
	if err != nil {
		log.ErrorWithCtx(ctx, "CommitConsumeGift fail to UpdateCostLogCommit. info:%+v, awardAmount:%d, commitTime:%v, err:%v",
			info, awardAmount, commitTime, err)
	}

	return dealToken, nil
}

func GenDealToken(uid, totalPrice uint32, preDealToken, baseOrderId, orderId string) (string, error) {
	if preDealToken == "" {
		return "", nil
	}

	//因为有全麦的情况，所以要修改deal_token的order_id，防止主键冲突
	dt, err := deal_token.Decode(preDealToken)
	if err != nil {
		return "", err
	}

	dt.OrderID = orderId
	preDealToken, err = deal_token.Encode(dt)
	if err != nil {
		return "", err
	}

	newDt := deal_token.NewDealTokenData(baseOrderId, orderId, "channel-lottery", int64(uid), int64(totalPrice))
	outDealToken, err := deal_token.AddDealToken(preDealToken, newDt)
	if err != nil {
		log.Errorf("GenDealToken fail to AddDealToken. uid:%d, newDt:%+v, err:%v", uid, newDt, err)
		return outDealToken, err
	}

	return outDealToken, nil
}

func genAwardLog(info *lottery.LotterInfo, costLog *lottery.UserLotteryCostLog, sendTime time.Time) *lottery.UserLotteryAwardLog {
	sendList := make([]*lottery.SendInfo, 0, len(info.UidList))
	for _, toUid := range info.UidList {
		sendList = append(sendList, &lottery.SendInfo{
			ToUid:       toUid,
			SendOrderId: fmt.Sprintf("%s_%d", costLog.OrderId, toUid),
		})
	}
	return &lottery.UserLotteryAwardLog{
		LotteryId:   costLog.LotteryId,
		OrderId:     costLog.OrderId,
		FromUid:     costLog.Uid,
		ChannelId:   costLog.ChannelId,
		SendList:    sendList,
		PresentId:   info.AwardGiftId,
		PresentType: info.GiftSendType,
		Amount:      uint32(len(sendList)),
		Price:       costLog.Price,
		PriceType:   costLog.PriceType,
		Status:      0,
		CTime:       time.Now().Unix(),
		AwardTime:   sendTime.Unix(),
	}
}

func (s *Manager) AwardBingoUsers(ctx context.Context, info *lottery.LotterInfo) error {
	if info == nil || info.AwardGiftId == 0 ||
		(info.Status != lottery.RESULT_ERROR && info.Status != lottery.RESULT) {
		return nil
	}

	costLog, err := s.dao.GetLotteryCostLog(info.SponsorUid, info.Id)
	if err != nil {
		log.ErrorWithCtx(ctx, "AwardBingoUsers fail to GetLotteryCostLog. info:%+v, err:%v", info, err)
		return err
	}

	// 检查一下
	if costLog.OrderId == "" || costLog.LotteryId != info.Id ||
		costLog.FreezeAmount < uint32(len(info.UidList)) {

		log.ErrorWithCtx(ctx, "AwardBingoUsers fail. info:%+v, err:消费扣除记录异常", info)
		return nil
	}

	sendTime := time.Now()
	awardLog := genAwardLog(info, costLog, sendTime)

	// 插入奖励记录
	if len(awardLog.SendList) > 0 {
		err = s.dao.CreateAwardLog(awardLog)
		if err != nil {
			log.ErrorWithCtx(ctx, "AwardBingoUsers fail to CreateAwardLog. info:%+v, err:%v", info, err)
			return err
		}
	}

	// 确认消费订单
	dealToken, err := s.CommitConsumeGift(ctx, info, costLog)
	if err != nil {
		log.ErrorWithCtx(ctx, "AwardBingoUsers fail to CommitConsumeGift. info:%+v, err:%v", info, err)
		return err
	}

	// 送礼
	err = s.SendGift(ctx, dealToken, info, awardLog)
	if err != nil {
		log.ErrorWithCtx(ctx, "AwardBingoUsers fail to SendGift. awardLog:%+v, err:%v", awardLog, err)
		return err
	}

	if info.Status != lottery.RESULT_ERROR {
		err := s.dao.UpdateLotteryStatus(info.Id, lottery.FINISH)
		if err != nil {
			log.ErrorWithCtx(ctx, "AwardBingoUsers fail to UpdateLotteryStatus. info:%+v, err:%v", info, err)
		}
	}

	log.InfoWithCtx(ctx, "AwardBingoUsers id:%d, uid:%d, awardLog:%+v", info.Id, info.SponsorUid, awardLog)
	return nil
}

func (s *Manager) ReissueAwardGift(ctx context.Context, orderId string, byBaseOrder bool) error {
	if orderId == "" {
		return nil
	}

	var err error
	var awardLog *lottery.UserLotteryAwardLog

	if byBaseOrder {
		// 根据父订单号获取订单
		awardLog, err = s.dao.GetAwardLogByOrderId(orderId)
		if err != nil {
			log.ErrorWithCtx(ctx, "ReissueAwardGift fail to GetAwardLogByOrderId. orderId:%s, err:%v", orderId, err)
			return err
		}
	} else {
		awardLog, err = s.dao.GetAwardLogBySubOrderId(orderId)
		if err != nil {
			log.ErrorWithCtx(ctx, "ReissueAwardGift fail to GetAwardLogBySubOrderId. orderId:%s, err:%v", orderId, err)
			return err
		}
	}

	if awardLog == nil || len(awardLog.SendList) == 0 {
		log.ErrorWithCtx(ctx, "ReissueAwardGift fail. orderId:%s, orderId:%s, err:awardLog err", orderId, awardLog.OrderId)
		return nil
	}

	costLog, err := s.dao.GetLotteryCostLogByOrder(awardLog.OrderId)
	if err != nil {
		log.ErrorWithCtx(ctx, "ReissueAwardGift fail to GetAwardLogBySubOrderId. orderId:%s, orderId:%s, err:%v", orderId, awardLog.OrderId, err)
		return err
	}

	lotteryInfo, err := s.dao.GetLottery(awardLog.LotteryId)
	if err != nil {
		log.ErrorWithCtx(ctx, "ReissueAwardGift fail to GetLottery. orderId:%s, lotteryId:%d, err:%v", orderId, awardLog.LotteryId, err)
		return err
	}

	now := time.Now()
	if lotteryInfo.BeginTime+int64(lotteryInfo.Countdown)+cbDelaySec > now.Unix() ||
		awardLog.AwardTime > now.Unix() || now.Unix()-awardLog.AwardTime > 14*24*3600 {
		return protocol.NewExactServerError(nil, status.ErrChannelLotteryInfoErr, "补发时机有误")
	}

	dealToken := costLog.TBeanDealToken
	if dealToken == "" {
		// 确认消费订单
		dealToken, err = s.CommitConsumeGift(ctx, lotteryInfo, costLog)
		if err != nil {
			log.ErrorWithCtx(ctx, "ReissueAwardGift fail to CommitConsumeGift. info:%+v, err:%v", lotteryInfo, err)
			return err
		}
	}

	// 不是整笔订单补发
	if !byBaseOrder {
		awardList := make([]*lottery.SendInfo, 0, 1)
		for _, award := range awardLog.SendList {
			if award.SendOrderId == orderId {
				awardList = append(awardList, award)
				break
			}
		}

		// 每次只补一个子订单
		awardLog.SendList = awardList
		if len(awardLog.SendList) == 0 {
			log.ErrorWithCtx(ctx, "ReissueAwardGift fail. orderId:%s, orderId:%s, err:awardLog err", orderId, awardLog.OrderId)
			return nil
		}
	}

	// 送礼
	err = s.SendGift(ctx, dealToken, lotteryInfo, awardLog)
	if err != nil {
		log.ErrorWithCtx(ctx, "ReissueAwardGift fail to SendGift. awardLog:%+v, err:%v", awardLog, err)
		return err
	}

	if lotteryInfo.Status != lottery.RESULT_ERROR {
		err := s.dao.UpdateLotteryStatus(lotteryInfo.Id, lottery.FINISH)
		if err != nil {
			log.ErrorWithCtx(ctx, "ReissueAwardGift fail to UpdateLotteryStatus. info:%+v, err:%v", lotteryInfo, err)
		}
	}

	log.InfoWithCtx(ctx, "ReissueAwardGift id:%d, uid:%d, awardLog:%+v", lotteryInfo.Id, lotteryInfo.SponsorUid, awardLog)
	return nil
}

func (s *Manager) SendGift(ctx context.Context, dealToken string, info *lottery.LotterInfo, awardLog *lottery.UserLotteryAwardLog) error {
	if awardLog == nil || awardLog.PresentId == 0 ||
		len(awardLog.SendList) == 0 || uint32(len(awardLog.SendList)) > info.Limit {
		return nil
	}
	orderId := awardLog.OrderId

	switch pb.LotteryGiftSendType(info.GiftSendType) {
	case pb.LotteryGiftSendType_LOTTERY_GIFT_TYPE_BACKPACK, pb.LotteryGiftSendType_LOTTERY_GIFT_TYPE_COMMON:
		// 送普通礼物
		err := s.sendCommGiftToUsers(ctx, awardLog, dealToken)
		if err != nil {
			log.ErrorWithCtx(ctx, "SendGift fail to sendCommGiftToUsers. orderId:%s, info:%+v, err:%v", orderId, info, err)
			return err
		}

	case pb.LotteryGiftSendType_LOTTERY_GIFT_TYPE_LEVELUP:
		// 送升级礼物
		err := s.sendLevelUpGiftToUsers(ctx, awardLog, dealToken, info.LevelUpGiftVersion)
		if err != nil {
			log.ErrorWithCtx(ctx, "SendGift fail to sendLevelUpGiftToUsers. orderId:%s, info:%+v, err:%v", orderId, info, err)
			return err
		}

	case pb.LotteryGiftSendType_LOTTERY_GIFT_TYPE_LUCK:
		// 送幸运礼物
		err := s.sendMagicSpiritGiftToUsers(ctx, awardLog, dealToken)
		if err != nil {
			log.ErrorWithCtx(ctx, "SendGift fail to sendMagicSpiritGiftToUsers. orderId:%s, info:%+v, err:%v", orderId, info, err)
			return err
		}

	default:
		return errors.New("不支持送出的礼物类型")
	}

	ok, err := s.dao.UpdateAwardLogStatus(orderId, 0, 1)
	if err != nil || !ok {
		log.ErrorWithCtx(ctx, "SendGift fail to UpdateAwardLogStatus. orderId:%s, info:%+v, ok:%v, err:%v", orderId, info, ok, err)
	}

	return nil
}

func (s *Manager) sendCommGiftToUsers(ctx context.Context, award *lottery.UserLotteryAwardLog, dealToken string) error {
	if award == nil || award.PresentId == 0 || len(award.SendList) == 0 {
		return nil
	}

	itemSource := uint32(0)
	if award.PresentType == uint32(pb.LotteryGiftSendType_LOTTERY_GIFT_TYPE_BACKPACK) {
		itemSource = uint32(present_middlewarePb.PresentSourceType_PRESENT_SOURCE_LOTTERY_PACKAGE)
	} else {
		itemSource = uint32(present_middlewarePb.PresentSourceType_PRESENT_SOURCE_LOTTERY_BUY)
	}

	targetList := make([]*present_middlewarePb.TargetUserInfo, 0, len(award.SendList))
	for _, info := range award.SendList {
		dk, err := GenDealToken(award.FromUid, award.Price, dealToken, award.OrderId, info.SendOrderId)
		if err != nil {
			log.ErrorWithCtx(ctx, "sendCommGiftToUsers fail to GenDealToken. award:%+v, dealToken:%s, err:%v", award, dealToken, err)
			continue
		}

		targetList = append(targetList, &present_middlewarePb.TargetUserInfo{
			Uid:       info.ToUid,
			OrderId:   info.SendOrderId,
			DealToken: dk,
		})
	}

	req := &present_middlewarePb.SendPresentReq{
		SendUid:   award.FromUid,
		BatchType: uint32(present_middlewarePb.PresentBatchSendType_PRESENT_SOURCE_WITH_UID),
		TargetInfo: &present_middlewarePb.PresentTargetInfo{
			Target: &present_middlewarePb.PresentTargetInfo_MultiTarget{
				MultiTarget: &present_middlewarePb.MultiTargetUser{
					UserInfo:      targetList,
					ItemId:        award.PresentId,
					Count:         1,
					UniqueOrderId: award.OrderId,
				}}},
		ChannelId:  award.ChannelId,
		SendSource: uint32(present_middlewarePb.PresentSendSourceType_E_SEND_SOURCE_LOTTERY),
		ItemSource: itemSource,
		SendMethod: uint32(present_middlewarePb.PresentSendMethodType_PRESENT_TYPE_ROOM),
		IsOptValid: true,
		WithPay:    false, // 无需再支付了
		WithPush:   true,
		PushInfo: &present_middlewarePb.PushInfo{
			ChannelPushType:  uint32(present_middlewarePb.PushInfo_Channel_NORMAL),
			PersonalPushType: uint32(present_middlewarePb.PushInfo_Person_NORMAL_SENDER),
			ImMsgType:        uint32(present_middlewarePb.PushInfo_IM_NONE),
		},
		SendTime: award.AwardTime,
	}

	_, err := s.presentMiddle.SendPresent(ctx, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "sendCommGiftToUsers fail to SendPresent. award:%+v, err:%v", award, err)
		return err
	}

	log.InfoWithCtx(ctx, "sendCommGiftToUsers award:%+v", award)
	return nil
}

func (s *Manager) sendLevelUpGiftToUsers(ctx context.Context, award *lottery.UserLotteryAwardLog, dealToken string, levelUpVersion uint32) error {
	if award == nil || award.PresentId == 0 || len(award.SendList) == 0 {
		return nil
	}

	targetList := make([]*LevelupPresentInnerLogic.TargetInfo, 0, len(award.SendList))
	for _, info := range award.SendList {
		dk, err := GenDealToken(award.FromUid, award.Price, dealToken, award.OrderId, info.SendOrderId)
		if err != nil {
			log.ErrorWithCtx(ctx, "sendCommGiftToUsers fail to GenDealToken. award:%+v, dealToken:%s, err:%v", award, dealToken, err)
			continue
		}

		targetList = append(targetList, &LevelupPresentInnerLogic.TargetInfo{
			Uid:       info.ToUid,
			OrderId:   info.SendOrderId,
			DealToken: dk,
		})
	}

	req := &LevelupPresentInnerLogic.SendLevelUpPresentReq{
		SenderUid:     award.FromUid,
		BatchType:     uint32(present_middlewarePb.PresentBatchSendType_PRESENT_SOURCE_WITH_UID),
		TargetUidList: targetList,
		ItemId:        award.PresentId,
		ItemCount:     1,
		Version:       levelUpVersion,
		ChannelId:     award.ChannelId,
		SendSource:    uint32(present_middlewarePb.PresentSendSourceType_E_SEND_SOURCE_LOTTERY),
		ItemSource:    uint32(present_middlewarePb.PresentSourceType_PRESENT_SOURCE_LOTTERY_BUY),
		SendTime:      award.AwardTime,
	}

	_, err := s.levelUpLogic.SendLevelUpPresent(ctx, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "sendLevelUpGiftToUsers fail to SendLevelUpPresent. award:%+v, err:%v", award, err)
		return err
	}

	log.InfoWithCtx(ctx, "sendLevelUpGiftToUsers award:%+v", award)
	return nil
}

func (s *Manager) sendMagicSpiritGiftToUsers(ctx context.Context, award *lottery.UserLotteryAwardLog, dealToken string) error {
	if award == nil || award.PresentId == 0 || len(award.SendList) == 0 {
		return nil
	}

	targetList := make([]uint32, 0, len(award.SendList))
	for _, info := range award.SendList {
		targetList = append(targetList, info.ToUid)
	}

	newDealToken, err := GenDealToken(award.FromUid, award.Price, dealToken, award.OrderId, award.OrderId)
	if err != nil {
		log.ErrorWithCtx(ctx, "sendMagicSpiritGiftToUsers fail to GenDealToken. award:%+v, dealToken:%s, err:%v", award, dealToken, err)
		return err
	}

	req := &magic_spirit.SendMagicWithSourceReq{
		Uid:           award.FromUid,
		MagicSpiritId: award.PresentId,
		TargetUidList: targetList,
		AverageCnt:    1,
		ChannelId:     award.ChannelId,
		PayOrderId:    award.OrderId,
		DealToken:     newDealToken,
		Source:        uint32(magic_spirit.SendMagicSource_ChannelLottery),
		OutsideTime:   award.AwardTime,
	}

	_, err = s.magicSpirit.SendMagicWithSource(ctx, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "sendMagicSpiritGiftToUsers fail to SendMagicWithSource. award:%+v, err:%v", award, err)
		return err
	}

	log.InfoWithCtx(ctx, "sendMagicSpiritGiftToUsers award:%+v", award)
	return nil
}

func (s *Manager) GetPresentPrice(ctx context.Context, giftType, giftId uint32) (price, priceType uint32, err error) {
	// 幸运礼物
	if giftType == uint32(pb.LotteryGiftSendType_LOTTERY_GIFT_TYPE_LUCK) {
		magicResp, err := s.magicSpirit.GetMagicSpirit(ctx, &magic_spirit.GetMagicSpiritReq{})
		if err != nil {
			log.ErrorWithCtx(ctx, "GetPresentPrice fail to GetMagicSpirit. giftType:%v, giftId:%d, err:%v", giftType, giftId, err)
			return 0, 0, err
		}

		for _, info := range magicResp.GetMagicSpirit() {
			if giftId == info.GetMagicSpiritId() {
				return info.GetPrice(), uint32(presentPb.PresentPriceType_PRESENT_PRICE_TBEAN), err
			}
		}

		return 0, 0, protocol.NewExactServerError(nil, status.ErrChannelLotteryInfoErr, "该幸运礼物配置不存在")
	}

	presentConfig, err := s.userPresent.GetPresentConfigById(ctx, giftId)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetPresentPrice fail to GetPresentConfigById. giftType:%v, giftId:%d, err:%v", giftType, giftId, err)
		return 0, 0, err
	}

	if presentConfig.GetItemConfig() == nil {
		return 0, 0, protocol.NewExactServerError(nil, status.ErrChannelLotteryInfoErr, "该礼物配置不存在")
	}

	return presentConfig.GetItemConfig().GetPrice(), presentConfig.GetItemConfig().GetPriceType(), nil
}

func (s *Manager) GenFinancialFile(ctx context.Context, req *reconcile_v2.GenFinancialFileReq) error {
	begin := time.Unix(req.GetBeginTime(), 0)
	end := time.Unix(req.GetEndTime(), 0)
	if begin.AddDate(0, 0, 7).Before(end) {
		return protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}

	summaryMap, err := s.dao.GetConfirmSummaryStatistics(begin, end)
	if err != nil {
		log.ErrorWithCtx(ctx, "GenFinancialFile fail to GetConfirmSummaryStatistics. req:%v, err:%v", req, err)
		return err
	}

	costTbean := int64(0)
	if summary, ok := summaryMap[lottery.CostTBean]; ok {
		costTbean += summary.CommitPrice + summary.LastCommitPrice
	}
	if summary, ok := summaryMap[lottery.CostMagicSpiritTBean]; ok {
		costTbean += summary.CommitPrice + summary.LastCommitPrice
	}

	if req.GetTbeanPrice() != costTbean {
		log.ErrorWithCtx(ctx, "GenFinancialFile 【昨日对账失败】房间抽奖-T豆，时间：%v-%v, 总价值：%d-%d", begin, end, costTbean, req.GetTbeanPrice())
		return protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "t豆数据对不上")
	}

	date := begin.Format("2006-01-02")
	list := make([]*lottery.ReconcileDateLog, 0, len(summaryMap))
	for _, info := range summaryMap {
		list = append(list, &lottery.ReconcileDateLog{
			LogDate:           date,
			CostType:          info.CostType,
			FreezeCnt:         info.FreezeCnt,
			FreezePrice:       info.FreezePrice,
			CommitCnt:         info.CommitCnt,
			CommitPrice:       info.CommitPrice,
			LastCommitCnt:     info.LastCommitCnt,
			LastCommitPrice:   info.LastCommitPrice,
			RollbackCnt:       info.RollbackCnt,
			RollbackPrice:     info.RollbackPrice,
			LastRollbackCnt:   info.LastRollbackCnt,
			LastRollbackPrice: info.LastRollbackPrice,
		})
	}

	err = s.mysql.RecordReconcileDataLogs(list)
	if err != nil {
		log.ErrorWithCtx(ctx, "GenFinancialFile fail to RecordReconcileDataLogs. req:%v, err:%v", req, err)
		return err
	}

	log.InfoWithCtx(ctx, "GenFinancialFile 【昨日对账】房间抽奖-T豆，时间：%v-%v, 总价值：%d-%d", begin, end, costTbean, req.GetTbeanPrice())
	return nil
}
