package main

import (
	"github.com/globalsign/mgo"
	"github.com/globalsign/mgo/bson"
	"golang.52tt.com/pkg/config"
	presentPb "golang.52tt.com/protocol/services/userpresent"
	"push/pkg/log"
	"time"
)

type MongoDao struct {
	infoSession *mgo.Session
	dbName      string
}

func NewMongoDao(cfg *config.MongoConfig) (*MongoDao, error) {
	url := cfg.URI()
	info, err := mgo.ParseURL(url)
	if err != nil {
		return nil, err
	}

	info.Timeout = 3 * time.Second
	info.ReadTimeout = 15 * time.Second
	sess, err := mgo.DialWithInfo(info)
	if err != nil {
		log.Errorf("Failed to mgo.DialWithTimeout url(%s) err(%v)", url, err)
		return nil, err
	}

	sess.SetPoolLimit(cfg.MaxPoolSize)
	sess.SetMode(mgo.PrimaryPreferred, true)
	sess.SetSafe(&mgo.Safe{})

	mongoDao := &MongoDao{
		infoSession: sess,
		dbName:      cfg.Database,
	}

	return mongoDao, nil
}

func (m *MongoDao) Connect(collection string) (*mgo.Session, *mgo.Collection) {
	s := m.infoSession.Clone()
	c := s.DB("").C(collection)
	return s, c
}

const costLogCollection = "lottery_cost_log"

type Summary struct {
	PresentType uint32 `json:"present_type" bson:"_id"`
	TotalCnt    int64  `json:"total_cnt" bson:"total_cnt"`
	TotalPrice  int64  `json:"total_price" bson:"total_price"`
}

func (m *MongoDao) GetCommitSummaryStatistics(begin, end time.Time) (map[uint32]*Summary, error) {
	out := make(map[uint32]*Summary)
	list := make([]*Summary, 0)

	s, c := m.Connect(costLogCollection)
	defer s.Close()

	pipe := []bson.M{
		{"$match": bson.M{
			"commit_time": bson.M{"$gte": begin.Unix(), "$lt": end.Unix()},
			"price_type":  uint32(presentPb.PresentPriceType_PRESENT_PRICE_TBEAN),
			"status":      bson.M{"$gt": 1},
		}},
		{"$group": bson.M{
			"_id":       "$cost_type",
			"total_cnt": bson.M{"$sum": "$commit_cost_amount"},
			"total_price": bson.M{
				"$sum": bson.M{
					"$multiply": []interface{}{"$price", "$commit_cost_amount"},
				},
			},
		}},
	}

	err := c.Pipe(pipe).All(&list)
	if err == mgo.ErrNotFound {
		return out, nil
	}

	for _, summary := range list {
		out[summary.PresentType] = summary
		log.Infof("%+v", summary)
	}

	return out, nil
}

func (m *MongoDao) GetRollbackSummaryStatistics(begin, end time.Time) (map[uint32]*Summary, error) {
	out := make(map[uint32]*Summary)
	list := make([]*Summary, 0)

	s, c := m.Connect(costLogCollection)
	defer s.Close()

	pipe := []bson.M{
		{"$match": bson.M{
			"commit_time": bson.M{"$gte": begin.Unix(), "$lt": end.Unix()},
			"price_type":  uint32(presentPb.PresentPriceType_PRESENT_PRICE_TBEAN),
			"status":      bson.M{"$gt": 1},
		}},
		{"$group": bson.M{
			"_id": "$cost_type",
			"total_cnt": bson.M{
				"$sum": bson.M{
					"$subtract": []interface{}{"$freeze_amount", "$commit_cost_amount"},
				},
			},
			"total_price": bson.M{
				"$sum": bson.M{
					"$multiply": []interface{}{"$price", bson.M{"$subtract": []interface{}{"$freeze_amount", "$commit_cost_amount"}}},
				},
			},
		}},
	}

	err := c.Pipe(pipe).All(&list)
	if err == mgo.ErrNotFound {
		return out, nil
	}

	for _, summary := range list {
		out[summary.PresentType] = summary
		log.Infof("%+v", summary)
	}

	return out, nil
}

func (m *MongoDao) GetFreezeSummaryStatistics(begin, end time.Time) (map[uint32]*Summary, error) {
	out := make(map[uint32]*Summary)
	list := make([]*Summary, 0)

	s, c := m.Connect(costLogCollection)
	defer s.Close()

	pipe := []bson.M{
		{"$match": bson.M{
			"freeze_time": bson.M{"$gte": begin.Unix(), "$lt": end.Unix()},
			"price_type":  uint32(presentPb.PresentPriceType_PRESENT_PRICE_TBEAN),
			"status":      bson.M{"$gt": 1},
		}},
		{"$group": bson.M{
			"_id":       "$cost_type",
			"total_cnt": bson.M{"$sum": "$freeze_amount"},
			"total_price": bson.M{
				"$sum": bson.M{
					"$multiply": []interface{}{"$price", "$freeze_amount"},
				},
			},
		}},
	}

	err := c.Pipe(pipe).All(&list)
	if err == mgo.ErrNotFound {
		return out, nil
	}

	for _, summary := range list {
		out[summary.PresentType] = summary
		log.Infof("%+v", summary)
	}

	return out, nil
}

type ConfirmSummary struct {
	CostType          uint32 `json:"cost_type" bson:"_id"`
	FreezeCnt         int64  `json:"freeze_cnt" bson:"freeze_cnt"`
	FreezePrice       int64  `json:"freeze_price" bson:"freeze_price"`
	CommitCnt         int64  `json:"commit_cnt" bson:"commit_cnt"`
	CommitPrice       int64  `json:"commit_price" bson:"commit_price"`
	LastCommitCnt     int64  `json:"last_commit_cnt" bson:"last_commit_cnt"`
	LastCommitPrice   int64  `json:"last_commit_price" bson:"last_commit_price"`
	RollbackCnt       int64  `json:"rollback_cnt" bson:"rollback_cnt"`
	RollbackPrice     int64  `json:"rollback_price" bson:"rollback_price"`
	LastRollbackCnt   int64  `json:"last_rollback_cnt" bson:"last_rollback_cnt"`
	LastRollbackPrice int64  `json:"last_rollback_price" bson:"last_rollback_price"`
}

func (m *MongoDao) GetConfirmSummaryStatistics(begin, end time.Time) (map[uint32]*ConfirmSummary, error) {
	out := make(map[uint32]*ConfirmSummary)
	list := make([]*ConfirmSummary, 0)

	s, c := m.Connect(costLogCollection)
	defer s.Close()

	query := bson.M{
		"$or": []bson.M{
			{"commit_time": bson.M{"$gte": begin.Unix(), "$lt": end.Unix()}},
			{"freeze_time": bson.M{"$gte": begin.Unix(), "$lt": end.Unix()}},
		},
		"price_type": uint32(presentPb.PresentPriceType_PRESENT_PRICE_TBEAN),
		"status":     bson.M{"$gt": 1},
	}

	different := bson.M{"$and": []bson.M{
		{"$gte": []interface{}{"$commit_time", begin.Unix()}},
		{"$lt": []interface{}{"$commit_time", end.Unix()}},
		{"$lt": []interface{}{"$freeze_time", begin.Unix()}},
	}}

	same := bson.M{"$and": []bson.M{
		{"$gte": []interface{}{"$commit_time", begin.Unix()}},
		{"$lt": []interface{}{"$commit_time", end.Unix()}},
		{"$gte": []interface{}{"$freeze_time", begin.Unix()}},
	}}

	freeze := bson.M{"$and": []bson.M{
		{"$gte": []interface{}{"$freeze_time", begin.Unix()}},
		{"$lt": []interface{}{"$freeze_time", end.Unix()}},
	}}

	commitPrice := bson.M{
		"$sum": bson.M{
			"$multiply": []interface{}{"$price", "$commit_cost_amount"},
		}}

	rollbackPrice := bson.M{
		"$sum": bson.M{
			"$multiply": []interface{}{"$price", bson.M{"$subtract": []interface{}{"$freeze_amount", "$commit_cost_amount"}}},
		}}

	group := bson.M{
		"_id": "$cost_type",
		"freeze_cnt": bson.M{
			"$sum": bson.M{
				"$cond": []interface{}{
					freeze, "$freeze_amount", 0},
			},
		},
		"freeze_price": bson.M{
			"$sum": bson.M{
				"$cond": []interface{}{
					freeze, bson.M{
						"$sum": bson.M{
							"$multiply": []interface{}{"$price", "$freeze_amount"},
						}}, 0},
			},
		},
		"last_commit_cnt": bson.M{
			"$sum": bson.M{
				"$cond": []interface{}{
					different, "$commit_cost_amount", 0},
			},
		},
		"last_commit_price": bson.M{
			"$sum": bson.M{
				"$cond": []interface{}{
					different, commitPrice, 0},
			},
		},
		"commit_cnt": bson.M{
			"$sum": bson.M{
				"$cond": []interface{}{
					same, "$commit_cost_amount", 0},
			},
		},
		"commit_price": bson.M{
			"$sum": bson.M{
				"$cond": []interface{}{
					same, commitPrice, 0},
			},
		},
		"last_rollback_cnt": bson.M{
			"$sum": bson.M{
				"$cond": []interface{}{
					different, bson.M{
						"$sum": bson.M{
							"$subtract": []interface{}{"$freeze_amount", "$commit_cost_amount"},
						}}, 0},
			},
		},
		"last_rollback_price": bson.M{
			"$sum": bson.M{
				"$cond": []interface{}{
					different, rollbackPrice, 0},
			},
		},
		"rollback_cnt": bson.M{
			"$sum": bson.M{
				"$cond": []interface{}{
					same, bson.M{
						"$sum": bson.M{
							"$subtract": []interface{}{"$freeze_amount", "$commit_cost_amount"},
						}}, 0},
			},
		},
		"rollback_price": bson.M{
			"$sum": bson.M{
				"$cond": []interface{}{
					same, rollbackPrice, 0},
			},
		},
	}

	pipe := []bson.M{
		{"$match": query},
		{"$group": group},
	}

	err := c.Pipe(pipe).All(&list)
	if err != nil {
		if err == mgo.ErrNotFound {
			return out, nil
		}

		log.Errorf("GetConfirmSummaryStatistics Pipe fail. err:%v", err)
		return out, err
	}

	for _, summary := range list {
		out[summary.CostType] = summary
		log.Infof("%+v", summary)
	}

	return out, nil
}
