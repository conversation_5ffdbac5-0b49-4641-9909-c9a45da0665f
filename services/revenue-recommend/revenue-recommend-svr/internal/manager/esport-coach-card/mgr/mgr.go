package mgr

import (
    "context"
    "fmt"
    headdynamicimage "golang.52tt.com/clients/head-dynamic-image-logic"
    headImageCli "golang.52tt.com/clients/headimage"
    userProfileApi "golang.52tt.com/clients/user-profile-api"
    "golang.52tt.com/pkg/log"
    protogrpc "golang.52tt.com/pkg/protocol/grpc"
    "golang.52tt.com/protocol/app"
    "golang.52tt.com/protocol/app/esport_logic"
    esport_skill "golang.52tt.com/protocol/services/esport-skill"
    esport_statistics "golang.52tt.com/protocol/services/esport-statistics"
    esport_trade "golang.52tt.com/protocol/services/esport-trade"
    "golang.52tt.com/protocol/services/esport_hall"
    "golang.52tt.com/protocol/services/esport_internal"
    "golang.52tt.com/protocol/services/esport_rcmd"
    "golang.52tt.com/services/revenue-recommend/revenue-recommend-svr/internal/config"
    "math"
    "math/rand"
    "strings"
    "sync"
    "time"
)

type SectionName string

const (
    SectionNameServiceArea SectionName = "接单大区" // 区服
    SectionNameRank        SectionName = "可接段位" // 段位
    SectionNameGameFeature SectionName = "游戏特色" // 游戏特色
)

type EsportCoachCardMgr struct {
    esportHallCli       esport_hall.EsportHallClient
    userProfileCli      userProfileApi.IClient
    esportSkillCli      esport_skill.EsportSkillClient
    esportTradeCli      esport_trade.EsportTradeClient
    esportStatCli       esport_statistics.EsportStatisticsClient
    headDynamicImageCli headdynamicimage.IClient
    headImageCli        headImageCli.IClient
    esportRcmdCli       esport_rcmd.EsportRcmdServiceClient
    esportInternalCli   esport_internal.EsportInternalClient
}

func NewEsportCoachCardMgr(ctx context.Context) (*EsportCoachCardMgr, error) {
    esportHallCli, err := esport_hall.NewClient(ctx)
    if err != nil {
        log.ErrorWithCtx(ctx, "NewClient esportHallCli failed", err)
        return nil, err
    }

    userProfileCli := userProfileApi.NewIClient()

    esportSkillCli, err := esport_skill.NewClient(ctx)
    if err != nil {
        log.ErrorWithCtx(ctx, "NewClient esportSkillCli failed", err)
        return nil, err
    }

    esportTradeCli, err := esport_trade.NewClient(ctx)
    if err != nil {
        log.ErrorWithCtx(ctx, "NewClient esportTradeCli failed", err)
        return nil, err
    }

    esportStatCli, err := esport_statistics.NewClient(ctx)
    if err != nil {
        log.ErrorWithCtx(ctx, "NewClient esportStatCli failed", err)
        return nil, err
    }
    esportInternalCli, err := esport_internal.NewClient(ctx)
    if err != nil {
        log.ErrorWithCtx(ctx, "NewClient esportInternalCli failed", err)
        return nil, err
    }

    headdynamicimageCli := headdynamicimage.NewClient()

    headImageCli := headImageCli.NewClient()

    esportRcmdCli, _ := esport_rcmd.NewClient(context.Background())
    mgr_ := &EsportCoachCardMgr{
        esportHallCli:       esportHallCli,
        userProfileCli:      userProfileCli,
        esportSkillCli:      esportSkillCli,
        esportTradeCli:      esportTradeCli,
        esportStatCli:       esportStatCli,
        headDynamicImageCli: headdynamicimageCli,
        headImageCli:        headImageCli,
        esportRcmdCli:       esportRcmdCli,
        esportInternalCli:   esportInternalCli,
    }
    return mgr_, nil
}

func (m *EsportCoachCardMgr) ShutDown() {

}

type StrategyItem struct {
    strategyId     uint32
    recallSourceId uint32
}

// GetEsportCoachCard 获取电竞教练卡片, gameId不确认可以不传, reqNum请求数量
func (m *EsportCoachCardMgr) GetEsportCoachCard(ctx context.Context, tabId, reqNum uint32) ([]*esport_logic.EsportAreaCoachInfo, error) {
    rs := make([]*esport_logic.EsportAreaCoachInfo, 0)
    // 获取映射的gameid
    gameId := config.DyConfInstance.GetTabIdToGameIdMapping()[tabId]

    // 如果没有映射的gameId，则直接返回
    if gameId == 0 {
        log.DebugWithCtx(ctx, "GetEsportCoachCard tabId: %d, gameId: %d, reqNum: %d", tabId, gameId, reqNum)
        return rs, nil
    }

    serviceInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
    if !ok {
        log.ErrorWithCtx(ctx, "GetEsportCoachCard ServiceInfoFromContext failed")
        return rs, nil
    }

    rcmdSwitchResp, err := m.esportRcmdCli.GetNewRcmdSwitch(ctx, &esport_rcmd.GetNewRcmdSwitchRequest{
        Uid: serviceInfo.UserID,
    })
    if err != nil {
        log.WarnWithCtx(ctx, "GetEsportCoachCard failed to GetNewRcmdSwitch tabId: %d, gameId: %d, reqNum: %d, err: %v", tabId, gameId, reqNum, err)
    }

    strategyMap := make(map[uint32]*StrategyItem)
    var recommendSkillProductList []*esport_hall.SkillProduct
    getSpBegin := time.Now()
    if rcmdSwitchResp.GetNewRcmdSwitch() {
        log.InfoWithCtx(ctx, "新推荐接口 GetEsportCoachCard GetNewRcmdSwitch, gameId: %d, reqNum: %d", gameId, reqNum)
        rcmdResp, err := m.esportRcmdCli.GetEsportRcmdSkillProduct(ctx, &esport_rcmd.GetEsportRcmdSkillProductReq{
            SceneType:    esport_rcmd.SceneType_SCENE_TYPE_HOME_PAGE_COACH_LIST,
            SkillId:      gameId,
            Offset:       0,
            Limit:        reqNum,
            GameProperty: nil,
        })
        if err != nil {
            log.ErrorWithCtx(ctx, "GetEsportCoachCard GetEsportRcmdSkillProduct failed", err)
            return rs, err
        }
        if len(rcmdResp.GetSkillProductList()) == 0 {
            return rs, nil
        }

        skillProductIdList := make([]uint32, 0, len(rcmdResp.GetSkillProductList()))
        for _, item := range rcmdResp.GetSkillProductList() {
            skillProductIdList = append(skillProductIdList, item.GetSkillProductId())
            strategyMap[item.GetSkillProductId()] = &StrategyItem{
                strategyId:     item.GetStrategyId(),
                recallSourceId: item.GetRecallSourceId(),
            }
        }
        infoResp, err := m.esportHallCli.BatchGetSkillProductInfo(ctx, &esport_hall.BatchGetSkillProductInfoRequest{
            ProductIdList: skillProductIdList,
        })
        if err != nil {
            log.ErrorWithCtx(ctx, "GetEsportCoachCard BatchGetSkillProductInfo failed", err)
            return rs, err
        }
        recommendSkillProductList = infoResp.GetProductList()
    } else {
        recommendSkillProduct, err := m.esportHallCli.GetRecommendSkillProduct(ctx, &esport_hall.GetRecommendSkillProductReq{
            GameId: gameId,
            ReqNum: reqNum,
        })
        if err != nil {
            log.ErrorWithCtx(ctx, "GetRecommendTabCoachCardInfo failed", err)
            return rs, err
        }
        recommendSkillProductList = recommendSkillProduct.GetSkillProductList()
    }

    if len(recommendSkillProductList) == 0 {
        return rs, nil
    }
    log.InfoWithCtx(ctx, "GetRecommendSkillProduct cost: %vms", time.Since(getSpBegin).Milliseconds())

    uidList := make([]uint32, 0, len(recommendSkillProductList))
    for _, v := range recommendSkillProductList {
        uidList = append(uidList, v.GetUid())
    }

    conCallBegin := time.Now()
    wg := sync.WaitGroup{}
    // 判断电竞总开关
    var eSportSwitch uint32
    wg.Add(1)
    go func() {
        defer wg.Done()
        st := time.Now()
        eSportSwitchResp, err := m.esportSkillCli.GetSwitch(ctx, &esport_skill.GetSwitchRequest{})
        if err != nil {
            log.ErrorWithCtx(ctx, "GetEsportCoachCard GetSwitch failed, gameId: %d, reqNum: %d", gameId, reqNum, err)
        }
        eSportSwitch = uint32(eSportSwitchResp.GetSwitchStatus().GetMainSwitchStatus())
        log.DebugWithCtx(ctx, "GetEsportCoachCard GetSwitch cost: %vms", time.Since(st).Milliseconds())
    }()

    // 获取用户信息
    userProfile := make(map[uint32]*app.UserProfile)
    var userProfileErr error
    wg.Add(1)
    go func() {
        defer wg.Done()
        st := time.Now()
        userProfile, userProfileErr = m.batGetUserProfile(ctx, uidList)
        log.DebugWithCtx(ctx, "GetEsportCoachCard batGetUserProfile cost: %vms", time.Since(st).Milliseconds())
    }()

    // 获取用户技能信息
    userSkill := make(map[uint32]*esport_skill.UserSkillInfo)
    var userSkillErr error
    wg.Add(1)
    go func() {
        defer wg.Done()
        st := time.Now()
        userSkill, userSkillErr = m.batGetUserSkill(ctx, gameId, uidList)
        log.DebugWithCtx(ctx, "GetEsportCoachCard batGetUserSkill cost: %vms", time.Since(st).Milliseconds())
    }()

    // 获取价格信息
    priceInfo := make(map[uint32]*esport_logic.PriceInfo)
    wg.Add(1)
    go func() {
        defer wg.Done()
        st := time.Now()
        priceInfo, _ = m.buildPriceInfo(ctx, serviceInfo.UserID, gameId, uidList)
        log.DebugWithCtx(ctx, "GetEsportCoachCard buildPriceInfo cost: %vms", time.Since(st).Milliseconds())
    }()

    // 包赢文案
    guaranteeWinText := make(map[uint32]string)
    wg.Add(1)
    go func() {
        defer wg.Done()
        st := time.Now()
        guaranteeWinText = m.batGetGuaranteeWinText(ctx, recommendSkillProductList)
        log.DebugWithCtx(ctx, "GetEsportCoachCard batGetGuaranteeWinText cost: %vms", time.Since(st).Milliseconds())
    }()

    // 获取平均分
    score := make(map[uint32]float32)
    wg.Add(1)
    go func() {
        defer wg.Done()
        st := time.Now()
        score = m.batGetEvaluateScoreSummary(ctx, gameId, uidList)
        log.DebugWithCtx(ctx, "GetEsportCoachCard batGetEvaluateScoreSummary cost: %vms", time.Since(st).Milliseconds())
    }()

    // 获取接单量
    orderCnt := make(map[uint32]uint32)
    wg.Add(1)
    go func() {
        defer wg.Done()
        st := time.Now()
        orderCnt = m.batGetOrderCnt(ctx, gameId, uidList)
        log.DebugWithCtx(ctx, "GetEsportCoachCard batGetOrderCnt cost: %vms", time.Since(st).Milliseconds())
    }()

    // 获取技能标识
    skillLabel := make(map[uint32][]string)
    wg.Add(1)
    go func() {
        defer wg.Done()
        st := time.Now()
        goCtx, cancel := protogrpc.NewContextWithInfoTimeout(ctx, 120*time.Millisecond)
        defer cancel()
        skillLabel = m.batGetSkillLabelUrl(goCtx, gameId, uidList)
        log.DebugWithCtx(ctx, "GetEsportCoachCard batGetSkillLabelUrl cost: %vms", time.Since(st).Milliseconds())
    }()

    wg.Wait()
    log.InfoWithCtx(ctx, "GetEsportCoachCard concurrent cost: %vms", time.Since(conCallBegin).Milliseconds())
    if userProfileErr != nil || userSkillErr != nil {
        log.ErrorWithCtx(ctx, "batch get failed", userProfileErr, userSkillErr)
        return rs, fmt.Errorf("batch get failed, userProfileErr: %v, userSkillErr: %v", userProfileErr, userSkillErr)
    }
    if eSportSwitch == uint32(esport_skill.EsportSwitchStatus_SWITCH_STATUS_OFF) {
        log.ErrorWithCtx(ctx, "GetEsportCoachCard switch close, gameId: %d, reqNum: %d, eSportSwitch: %+v", gameId, reqNum, eSportSwitch)
        return rs, nil
    }
    if len(priceInfo) == 0 {
        return rs, nil
    }

    var strategyId, recallSourceId uint32
    for _, item := range recommendSkillProductList {
        if strategyItem, ok := strategyMap[item.GetId()]; ok {
            strategyId = strategyItem.strategyId
            recallSourceId = strategyItem.recallSourceId
        }
        rs = append(rs, &esport_logic.EsportAreaCoachInfo{
            UserProfile:           userProfile[item.GetUid()],
            Price:                 priceInfo[item.GetUid()],
            TextDesc:              userSkill[item.GetUid()].GetTextDesc(),
            VoiceDesc:             userSkill[item.GetUid()].GetAudio(),
            VoiceDescDuration:     userSkill[item.GetUid()].GetAudioDuration(),
            GuaranteeWinText:      guaranteeWinText[item.GetUid()],
            EvaluateAvgScore:      score[item.GetUid()],
            ServiceOrderNum:       orderCnt[item.GetUid()],
            SkillLabelUrlList:     skillLabel[item.GetUid()],
            GameId:                gameId,
            Tag:                   item.GetTag(),
            LabelListForFrontPage: m.getLabelListForFrontPage(ctx, item),
            StrategyId:            strategyId,
            RecallSourceId:        recallSourceId,
        })
    }

    return rs, nil
}

// batGetUserSkill 批量获取用户技能信息
func (m *EsportCoachCardMgr) batGetUserSkill(ctx context.Context, gameId uint32, uidList []uint32) (map[uint32]*esport_skill.UserSkillInfo, error) {
    rs := make(map[uint32]*esport_skill.UserSkillInfo)
    // 获取用户技能信息
    userSkill, err := m.esportSkillCli.BatchGetUserCurrentSkill(ctx, &esport_skill.BatchGetUserCurrentSkillRequest{
        Uid: uidList,
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "BatchGetUserCurrentSkill failed", err)
        return rs, err
    }

    for _, item := range userSkill.GetList() {
        for _, skillItem := range item.GetSkill() {
            if skillItem.GetGameId() == gameId {
                rs[item.GetUid()] = skillItem
            }
        }
    }

    return rs, nil
}

// batGetEvaluateScoreSummary 批量获取评分
func (m *EsportCoachCardMgr) batGetEvaluateScoreSummary(ctx context.Context, gameId uint32, uidList []uint32) map[uint32]float32 {
    essMap := make(map[uint32]float32)
    essResp, err := m.esportTradeCli.BatGetEvaluateScoreSummary(ctx, &esport_trade.BatGetEvaluateScoreSummaryRequest{
        UidList: uidList,
        GameId:  gameId,
    })
    if err != nil {
        log.WarnWithCtx(ctx, "batGetEvaluateScoreSummary BatGetEvaluateScoreSummary, gameId: %d, uidList: %+v, err: %v", gameId, uidList, err)
        return essMap
    }

    for _, item := range essResp.GetSummaryList() {
        essMap[item.GetUid()] = float32(math.Floor(float64(item.GetAvgScore())) / 10)
    }
    return essMap
}

// batGetOrderCnt 批量获取接单量
func (m *EsportCoachCardMgr) batGetOrderCnt(ctx context.Context, gameId uint32, uidList []uint32) map[uint32]uint32 {
    rs := make(map[uint32]uint32)
    // 获取接单量
    orderCntResp, err := m.esportStatCli.BatchGetOrderCntByGameId(ctx, &esport_statistics.BatchGetOrderCntByGameIdRequest{
        UidList: uidList,
        GameId:  gameId,
    })
    if err != nil {
        log.WarnWithCtx(ctx, "GetEsportAreaCoachList failed to BatchGetOrderCntByGameId, uidList:%v, err:%v", uidList, err)
        return rs
    }
    return orderCntResp.GetOrderNumMap()
}

// batGetSkillLabelUrl 批量获取技能标识
func (m *EsportCoachCardMgr) batGetSkillLabelUrl(ctx context.Context, gameId uint32, uidList []uint32) map[uint32][]string {
    rs := make(map[uint32][]string)
    // 获取技能标识
    label, err := m.esportSkillCli.BatchGetCoachLabelsForGame(ctx, &esport_skill.BatchGetCoachLabelsForGameRequest{
        GameId:   gameId,
        CoachIds: uidList,
    })
    if err != nil {
        log.WarnWithCtx(ctx, "BatchGetCoachLabelsForGame failed", err)
        return rs
    }

    for _, item := range label.GetLabelList() {
        skillLabel := item.GetLabelMap()[uint32(esport_skill.LabelType_LABEL_TYPE_SKILL)].GetLabelList()
        for _, skillLabelItem := range skillLabel {
            if skillLabelItem.GetLabelImage() == "" {
                continue
            }
            rs[item.GetCoachId()] = append(rs[item.GetCoachId()], skillLabelItem.GetLabelImage())
        }
    }

    return rs
}

func (m *EsportCoachCardMgr) batGetGuaranteeWinText(ctx context.Context, skillProduct []*esport_hall.SkillProduct) map[uint32]string {
    rs := make(map[uint32]string)
    for _, item := range skillProduct {
        if item.GetIsGuaranteeWin() && len(item.GetGuaranteeWinTexts()) > 0 {
            rs[item.GetUid()] = item.GetGuaranteeWinTexts()[0]
        }
    }
    return rs
}

func (m *EsportCoachCardMgr) buildPriceInfo(ctx context.Context, opUid, gameId uint32, uidList []uint32) (map[uint32]*esport_logic.PriceInfo, error) {
    rs := make(map[uint32]*esport_logic.PriceInfo)

    pricingInfoResponse, err := m.esportInternalCli.BatchGetCoachPricingInfo(ctx, &esport_internal.BatchGetCoachPricingInfoRequest{
        Uid:       opUid,
        CoachUid:  uidList,
        GameId:    gameId,
        BuyAmount: 1,
        QueryOption: &esport_internal.CouponQueryOption{
            DoNotCheckRisk:                true,
            DoNotCheckVersion:             true,
            DoNotCheckPlayerEverydayCount: true,
            DoNotCheckCoachEverydayCount:  true,
        },
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "BatchGetCoachPricingInfo failed", err)
        return rs, nil
    }
    for k, v := range pricingInfoResponse.GetPriceMap() {
        rs[k] = &esport_logic.PriceInfo{
            Price:       v.GetPrice(),
            PriceUnit:   v.GetUnit(),
            MeasureCnt:  v.GetMeasureCnt(),
            MeasureUnit: v.GetMeasureUnit(),
        }
        if len(v.GetDiscountList()) > 0 {
            // 因为目前业务只允许一个优惠，所以直接取第一个
            // 判断用户使用的客户端版本号，如果不是符合新客价的版本，并且有新客价优惠，则使用顺位第二的优惠
            discountInfo := v.GetDiscountList()[0]
            rs[k].HasDiscount = true
            rs[k].DiscountPrice = discountInfo.GetPrice()
            rs[k].DiscountType = uint32(discountInfo.GetType())
            rs[k].DiscountDesc = discountInfo.GetDesc()
            // 额外处理原来的逻辑
            switch discountInfo.GetType() {
            case esport_internal.DiscountType_DISCOUNT_TYPE_FIRST_ORDER:
                rs[k].HasFirstRoundDiscount = true
                rs[k].FirstRoundPrice = discountInfo.GetPrice()
            }
        }
    }
    return rs, nil
}

func (m *EsportCoachCardMgr) batGetUserProfile(ctx context.Context, uidList []uint32) (map[uint32]*app.UserProfile, error) {
    rs := make(map[uint32]*app.UserProfile)

    userProfile, err := m.userProfileCli.BatchGetUserProfileV2(ctx, uidList, false)
    if err != nil {
        log.ErrorWithCtx(ctx, "batGetUserProfile.BatchGetUserProfileV2 failed", err)
        return rs, err
    }
    for _, item := range userProfile {
        rs[item.GetUid()] = item
    }

    usernames := make([]string, 0, len(userProfile))
    invokeUid := uint32(0)
    for _, item := range userProfile {
        if invokeUid == 0 {
            invokeUid = item.GetUid()
        }
        usernames = append(usernames, item.GetAccount())
    }

    wg := sync.WaitGroup{}
    wg.Add(1)
    dyHeadMap := make(map[uint32]string)
    go func() {
        defer wg.Done()
        var subErr error
        dyHeadMap, subErr = m.headDynamicImageCli.GetHeadDynamicImageMd5(ctx, uidList)
        if subErr != nil {
            log.WarnWithCtx(ctx, "batGetUserProfile.GetHeadDynamicImageMd5 uidList: %+v, err: %v", uidList, subErr)
        }
    }()

    wg.Add(1)
    headImgMap := make(map[string]string)
    go func() {
        defer wg.Done()
        var subErr error
        headImgMap, subErr = m.headImageCli.BatchGetHeadImageMd5(ctx, invokeUid, usernames)
        if subErr != nil {
            log.WarnWithCtx(ctx, "batGetUserProfile.BatchGetHeadImageMd5 uidList: %+v, err: %v", uidList, subErr)
        }
    }()

    wg.Wait()
    for _, item := range userProfile {
        item.HeadImgMd5 = headImgMap[item.GetAccount()]
        item.HeadDyImgMd5 = dyHeadMap[item.Uid]
    }

    return rs, nil
}

func (m *EsportCoachCardMgr) getLabelListForFrontPage(ctx context.Context, data *esport_hall.SkillProduct) []string {
    // 判断 Property 列表中满足一下的条件
    // 1. 存在 SectionName 为 SectionNameServiceArea 的，且值不为空
    // 2. SectionNameRank 的值包括 "全段位" 字符
    // 3. 存在 SectionName 为 SectionNameGameFeature 的，且值不为空

    var serviceArea, rank, gameFeature *esport_hall.SectionInfo
    rankLevelSubStr := "全段位"
    rankLevel := "全段位可接"
    for _, item := range data.GetPropertyList() {
        switch SectionName(item.GetSectionName()) {
        case SectionNameServiceArea:
            if len(item.GetItemList()) > 0 {
                serviceArea = item
            }
        case SectionNameRank:
            // 判断值是否包括 "全段位" 字符
            for _, val := range item.GetItemList() {
                if strings.Contains(val, rankLevelSubStr) {
                    rank = item
                    rankLevel = val
                    break
                }
            }
        case SectionNameGameFeature:
            if len(item.GetItemList()) > 0 {
                gameFeature = item
            }
        default:
            // do nothing
        }
    }
    // 判断是否三个条件都满足了
    log.DebugWithCtx(ctx, "GetEsportCoachCard getLabelListForFrontPage, serviceArea: %+v, rank: %+v, gameFeature: %+v", serviceArea, rank, gameFeature)
    // 构造逻辑
    result := make([]string, 0, len(serviceArea.GetItemList())+2)
    if serviceArea != nil {
        // 区服全要
        result = append(result, strings.Join(serviceArea.GetItemList(), "/"))
    }
    if rank != nil {
        // 全段位可接
        result = append(result, rankLevel)
    }
    if gameFeature != nil {
        // 游戏特色随机取一个, 先生成一个随机数
        randIdx := rand.Intn(len(gameFeature.GetItemList()))
        result = append(result, gameFeature.GetItemList()[randIdx])
    }
    return result
}
