package event

/*
import (
	"context"
	"github.com/Shopify/sarama"
	"github.com/golang/protobuf/proto"
	"gitlab.ttyuyin.com/golang/gudetama/log"
	apicenter "golang.52tt.com/clients/apicenter/apiserver"
	"golang.52tt.com/pkg/protocol"
	apiPB "golang.52tt.com/protocol/services/apicenter/apiserver"
	kfkPB "golang.52tt.com/protocol/services/minToolkit/kafka/pb/tt_auth_ev"
	"golang.52tt.com/services/operate-platform/cache"
	config "golang.52tt.com/services/operate-platform/conf"
	"golang.52tt.com/services/operate-platform/manager"
	"golang.52tt.com/services/ugc/common/event"
	"time"
)

const (
	loginTypeEvent = "tt_auth_ev"
)

var andriod_6_1_0 = v(6, 1, 0)

type KafkaSubscriber struct {
	*event.KafkaSub
	cacheClient  *cache.OperatePlatformCache
	authAwardMgr *manager.AuthAwardMgr
	businessConf *config.BusinessConfManager
}

func NewKafkaSubscriber(clientId, groupId string, topics, brokers []string, cacheClient *cache.OperatePlatformCache, authAwardMgr *manager.AuthAwardMgr) (*KafkaSubscriber, error) {

	conf := sarama.NewConfig()
	conf.ClientID = clientId
	conf.Consumer.Offsets.Initial = sarama.OffsetNewest
	conf.Consumer.Return.Errors = true

	kafkaSub, err := event.NewKafkaSub(loginTypeEvent, brokers, groupId, topics, conf)
	if err != nil {
		log.Errorf("Failed to create kafka-subscriber %+v", err)
		return nil, err
	}

	business := config.NewBusinessConfManager()

	sub := &KafkaSubscriber{
		KafkaSub:     kafkaSub,
		cacheClient:  cacheClient,
		authAwardMgr: authAwardMgr,
		businessConf: business,
	}

	sub.SetMessageProcessor(sub.handlerEvent)
	return sub, nil
}

func (s *KafkaSubscriber) Close() {
	s.KafkaSub.Stop()
}

func (s *KafkaSubscriber) handlerEvent(msg *sarama.ConsumerMessage) (error, bool) {
	switch msg.Topic {
	case loginTypeEvent:
		return s.handlerLoginEvent(msg)
	}
	return nil, false
}

func (s *KafkaSubscriber) handlerLoginEvent(msg *sarama.ConsumerMessage) (error, bool) {
	authEvent := kfkPB.TTAuthEvent{}
	err := proto.Unmarshal(msg.Value, &authEvent)
	if err != nil {
		log.Errorf("Failed to proto.Unmarshal %+v", err)
		return err, false
	}
	log.Infof("handlerLoginEvent %+v", authEvent.String())

	// 处理新版本登录送礼
	if authEvent.ClientType == protocol.ClientTypeANDROID && authEvent.GetClientVersion() >= andriod_6_1_0 && authEvent.GetMarketId() == uint32(protocol.TT_MarketID) {
		if authEvent.EventType == uint32(kfkPB.ETT_AUTH_EVENT_TYPE_ENUM_TTAUTH_REG) {
			s.handleNewVersionReg(&authEvent)
		} else {
			s.handleNewVersionAuthAward(&authEvent)
		}
	}

	beforeTime := time.Date(2021, 1, 21, 11, 00, 0, 0, time.Local)
	afterTime := time.Date(2021, 2, 2, 23, 59, 0, 0, time.Local)
	if time.Now().Before(beforeTime) {
		log.Infof("time err %+v", beforeTime)
		return nil, false
	}
	if time.Now().After(afterTime) {
		log.Infof("time err %+v", afterTime)
		return nil, false
	}
	if authEvent.GetClientType() != protocol.ClientTypePcTT {
		log.Errorf("SendImMsg type wrong, type:%d, uid:%d", authEvent.GetClientType(), authEvent.GetUid())
		return nil, false
	}
	limit, _ := s.cacheClient.SendMsgLimit(authEvent.GetDeviceIdHex())
	if limit > 1 {
		log.Warnf("SendMsgLimit, uid: %d", authEvent.GetUid())
		return nil, false
	}
	content := "欢迎勇士来到TT语音PC端！新春福利请收下~ 点击兑换链接输入口令码 TTyuyin888 领取你的DNF新春礼包，一人一码，请勿泄露~兑换传送门：https://dnf.qq.com/cp/a20190312welfare/"
	s.writeOfficialIMMsgToUser(authEvent.GetUid(), content)
	return nil, false
}

func (s *KafkaSubscriber) writeOfficialIMMsgToUser(toUid uint32, content string) (err error) {
	msg := &apiPB.ImMsg{
		ImType: &apiPB.ImType{
			SenderType:   uint32(apiPB.IM_SENDER_TYPE_IM_SENDER_NORMAL),
			ReceiverType: uint32(apiPB.IM_RECEIVER_TYPE_IM_RECEIVER_USER),
			ContentType:  uint32(apiPB.IM_CONTENT_TYPE_IM_CONTENT_TEXT),
		},
		FromUid: uint32(10000),
		ToIdList: []uint32{
			toUid,
		},
		ImContent: &apiPB.ImContent{
			TextNormal: &apiPB.ImTextNormal{Content: content},
		},
		Platform:    apiPB.Platform_PC,
		AppPlatform: "pc",
	}
	log.Debugf("send mission msg: %+v, uid:%d", msg, toUid)
//	err = apicenter.StdClient.SendImMsg(context.Background(), toUid, protocol.TT, []*apiPB.ImMsg{msg}, true)
	if err != nil {
		log.Errorf("SendImMsg err: %s", err.Error())
		return err
	}
	return nil
}

func (s *KafkaSubscriber) handleNewVersionAuthAward(authEvent *kfkPB.TTAuthEvent) {
	authConf := s.businessConf.GetAuthAwardConfig()
	log.Debugf("GetAuthAwardConfig res: %+v  ", authConf)
	if authConf != nil {
		ctx := context.Background()
		ok, err := s.authAwardMgr.IsAuthAward(ctx, authConf.Id, authEvent.GetUid())
		if err != nil {
			log.Errorf("handlerLoginEvent IsAuthAward err, uid %d,err %+v", authEvent.GetUid(), err)
			return
		}
		if !ok {
			err = s.authAwardMgr.GiveUserAward(ctx, authEvent.GetUid(), authConf.Value, authConf.AwardType, authConf.Id)
			if err != nil {
				log.Errorf("handlerLoginEvent GiveUserAward err, uid %d,err %+v", authEvent.GetUid(), err)
				return
			}
			err = s.authAwardMgr.RecordAuthAward(ctx, authConf.Id, authEvent.GetUid())
			if err != nil {
				log.Errorf("handlerLoginEvent RecordAuthAward err, uid %d,err %+v", authEvent.GetUid(), err)
				return
			}
		}
	}
}

func (s *KafkaSubscriber) handleNewVersionReg(authEvent *kfkPB.TTAuthEvent) {
	authConf := s.businessConf.GetAuthAwardConfig()
	log.Debugf("GetAuthAwardConfig res: %+v  ", authConf)
	if authConf != nil {
		ctx := context.Background()
		ok, err := s.authAwardMgr.IsAuthAward(ctx, authConf.Id, authEvent.GetUid())
		if err != nil {
			log.Errorf("handlerLoginEvent IsAuthAward err, uid %d,err %+v", authEvent.GetUid(), err)
			return
		}
		if !ok {
			err = s.authAwardMgr.RecordAuthAward(ctx, authConf.Id, authEvent.GetUid())
			if err != nil {
				log.Errorf("handlerLoginEvent RecordAuthAward err, uid %d,err %+v", authEvent.GetUid(), err)
				return
			}
		}
	}
}

func v(major, minor, release uint) uint32 {
	return uint32((major << 24) + (minor << 16) + release)
}

*/
