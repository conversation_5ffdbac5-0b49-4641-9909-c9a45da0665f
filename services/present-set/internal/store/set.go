package store

import (
	"context"
	"encoding/json"
	"golang.52tt.com/pkg/log"
	pb "golang.52tt.com/protocol/services/present-set"
	"golang.52tt.com/services/present-set/internal/utils"
	"gorm.io/datatypes"
	"gorm.io/gorm"
	"strconv"
	"time"
)

// PresentSetConfig 礼物套组配置表
type PresentSetConfig struct {
	ID             uint32              `gorm:"primaryKey;not null;autoIncrement;comment:套组id" json:"id"`                                                                     // 套组id
	SetName        string              `gorm:"type:varchar(32);not null;default:'';comment:套组名称" json:"set_name"`                                                            // 套组名称
	CreateTime     time.Time           `gorm:"autoCreateTime;type:datetime;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"create_time"`                              // 创建时间
	UpdateTime     time.Time           `gorm:"autoUpdateTime;type:datetime;not null;default:CURRENT_TIMESTAMP;onUpdate:CURRENT_TIMESTAMP;comment:更新时间" json:"update_time"`   // 更新时间
	ChangeTime     time.Time           `gorm:"autoUpdateTime;type:datetime;not null;default:CURRENT_TIMESTAMP;onUpdate:CURRENT_TIMESTAMP;comment:配置变更时间" json:"change_time"` // 配置变更时间（包含套组配置、礼物配置、奖励配置变更）
	SetPeriod      pb.PresentSetPeriod `gorm:"type:tinyint unsigned;not null;default:0;comment:时效" json:"set_period"`                                                        // 时效
	StartTime      time.Time           `gorm:"type:datetime;comment:开始时间" json:"start_time"`                                                                                 // 开始时间
	EndTime        time.Time           `gorm:"type:datetime;comment:结束时间" json:"end_time"`                                                                                   // 结束时间
	Ranking        float64             `gorm:"type:decimal(10,5);not null;comment:礼物架排序" json:"ranking"`                                                                     // 礼物架排序
	IsDel          bool                `gorm:"type:tinyint(1);not null;default:0;comment:是否删除" json:"is_del"`                                                                // 是否删除
	AwardText      string              `gorm:"type:varchar(255);not null;default:'';comment:奖励文案" json:"award_text"`                                                         // 奖励文案
	BreakingNewsID uint32              `gorm:"type:int unsigned;not null;default:0;comment:全服ID" json:"breaking_news_id"`                                                    // 全服ID
	EmperorSetID   uint32              `gorm:"type:int unsigned;not null;default:0;comment:帝王套id" json:"emperor_set_id"`                                                     // 关联帝王套ID
	KingSetConfig  datatypes.JSON      `gorm:"type:json;comment:帝王套配置(废弃)" json:"king_set_config"`                                                                           // 帝王套配置 (废弃)
	PreviewConfig  datatypes.JSON      `gorm:"type:json;comment:预览配置" json:"preview_config"`                                                                                 // 预览配置
	ActivityConfig datatypes.JSON      `gorm:"type:json;comment:活动配置" json:"activity_config"`                                                                                // 活动配置
}

// PresentSetKingSetConfig 套组帝王套配置
type PresentSetKingSetConfig struct {
	EnableEmperorSet bool   `json:"enable_emperor_set,omitempty"` // 是否配置帝王套
	EmperorSetId     uint32 `json:"emperor_set_id,omitempty"`     // 帝王套id
}

// PresentSetPreviewConfig 套组预览配置
type PresentSetPreviewConfig struct {
	EnablePreview bool                     `json:"enable_preview,omitempty"`  // 是否配置预览
	PreviewType   pb.PresentSetPreviewType `json:"preview_type,omitempty"`    // 预览类型
	PreviewResUrl string                   `json:"preview_res_url,omitempty"` // 预览资源链接
	PreviewResMD5 string                   `json:"preview_res_md5,omitempty"` // 预览资源MD5
}

// PresentSetActivityConfig 套组活动配置
type PresentSetActivityConfig struct {
	EnableActivity         bool                `json:"enable_activity,omitempty"`           // 是否配置活动
	ActivityLinkType       pb.ActivityLinkType `json:"activity_link_type"`                  // 活动链接类型
	ActivityEntranceResUrl string              `json:"activity_entrance_res_url,omitempty"` // 活动入口资源
	ActivityJumpUrl        string              `json:"activity_jump_url,omitempty"`         // 活动链接
}

func NewPresentSetConfigFromPb(p *pb.PresentSetConfig) *PresentSetConfig {
	var st, et time.Time
	if p.StartTime != 0 {
		st = time.Unix(int64(p.StartTime), 0)
	}
	if p.EndTime != 0 {
		et = time.Unix(int64(p.EndTime), 0)
	}
	emperorSetCfg, _ := json.Marshal(&PresentSetKingSetConfig{
		EnableEmperorSet: p.GetEnableEmperorSet(),
		EmperorSetId:     p.GetEmperorSetId(),
	})
	previewCfg, _ := json.Marshal(&PresentSetPreviewConfig{
		EnablePreview: p.GetEnablePreview(),
		PreviewType:   p.GetPreviewType(),
		PreviewResUrl: p.GetPreviewResUrl(),
		PreviewResMD5: p.GetPreviewResMd5(),
	})
	activityCfg, _ := json.Marshal(&PresentSetActivityConfig{
		EnableActivity:         p.GetEnableActivity(),
		ActivityEntranceResUrl: p.GetActivityEntranceResUrl(),
		ActivityLinkType:       p.GetActivityLinkType(),
		ActivityJumpUrl:        p.GetActivityJumpUrl(),
	})
	return &PresentSetConfig{
		ID:             p.SetId,
		SetName:        p.SetName,
		SetPeriod:      p.Period,
		StartTime:      st,
		EndTime:        et,
		Ranking:        p.Rank,
		AwardText:      p.AwardText,
		KingSetConfig:  emperorSetCfg,
		PreviewConfig:  previewCfg,
		ActivityConfig: activityCfg,
		BreakingNewsID: p.BreakingNewsId,
		EmperorSetID:   p.EmperorSetId,
	}
}

func (p *PresentSetConfig) TableName() string {
	return "present_set_config"
}

func (p *PresentSetConfig) ToPb() *pb.PresentSetConfig {
	var activeStatus pb.PresentSetActiveStatus

	var st, et uint32
	if !utils.IsNullTime(p.StartTime) {
		st = uint32(p.StartTime.Unix())
	}
	if !utils.IsNullTime(p.EndTime) {
		et = uint32(p.EndTime.Unix())
	}

	emperorSetCfg, previewCfg, activityCfg := &PresentSetKingSetConfig{}, &PresentSetPreviewConfig{}, &PresentSetActivityConfig{}
	_ = json.Unmarshal(p.KingSetConfig, emperorSetCfg)
	_ = json.Unmarshal(p.PreviewConfig, previewCfg)
	_ = json.Unmarshal(p.ActivityConfig, activityCfg)

	tmpPb := &pb.PresentSetConfig{
		SetId:                  p.ID,
		SetName:                p.SetName,
		ActiveStatus:           activeStatus,
		Rank:                   p.Ranking,
		AwardText:              p.AwardText,
		Period:                 p.SetPeriod,
		EnableEmperorSet:       p.EmperorSetID > 0,
		EmperorSetId:           p.EmperorSetID,
		EnablePreview:          previewCfg.EnablePreview,
		PreviewType:            previewCfg.PreviewType,
		PreviewResUrl:          previewCfg.PreviewResUrl,
		PreviewResMd5:          previewCfg.PreviewResMD5,
		EnableActivity:         activityCfg.EnableActivity,
		ActivityEntranceResUrl: activityCfg.ActivityEntranceResUrl,
		ActivityLinkType:       activityCfg.ActivityLinkType,
		ActivityJumpUrl:        activityCfg.ActivityJumpUrl,
		BreakingNewsId:         p.BreakingNewsID,
		PresentList:            nil,
		AwardItemList:          nil,
		StartTime:              st,
		EndTime:                et,
		CreateTime:             utils.GetUnixTime(p.CreateTime),
		UpdateTime:             utils.GetUnixTime(p.UpdateTime),
		ChangeTime:             utils.GetUnixTime(p.ChangeTime),
		IsDel:                  p.IsDel,
	}

	if utils.CheckSetActive(tmpPb) {
		activeStatus = pb.PresentSetActiveStatus_PRESENT_SET_ACTIVE_STATUS_ACTIVE
	} else {
		activeStatus = pb.PresentSetActiveStatus_PRESENT_SET_ACTIVE_STATUS_INACTIVE
	}
	tmpPb.ActiveStatus = activeStatus

	return tmpPb
}

// SetPresentItem 礼物套组中的礼物配置表
type SetPresentItem struct {
	ID           uint32          `gorm:"primaryKey;not null;autoIncrement;comment:套组礼物id" json:"id"`                                                                 // 套组礼物id
	SetID        uint32          `gorm:"type:int unsigned;not null;default:0;comment:套组id" json:"set_id"`                                                            // 套组id
	PresentID    uint32          `gorm:"type:int unsigned;not null;default:0;comment:礼物id" json:"present_id"`                                                        // 礼物id
	PresentLevel pb.PresentLevel `gorm:"type:tinyint unsigned;not null;default:0;comment:礼物等级" json:"present_level"`                                                 // 礼物等级
	Probability  float64         `gorm:"type:DECIMAL(10,7);comment:概率（%）" json:"probability"`                                                                        // 概率（%）
	Ranking      float64         `gorm:"type:DECIMAL;not null;default:0;comment:套组内排序" json:"ranking"`                                                               // 套组内排序
	Threshold    uint32          `gorm:"type:int unsigned;not null;default:0;comment:门槛次数" json:"threshold"`                                                         // 门槛次数
	Guaranteed   int32           `gorm:"type:int;not null;default:0;comment:保底次数" json:"guaranteed"`                                                                 // 保底次数
	IsOrigin     bool            `gorm:"type:tinyint(1);not null;default:0;comment:是否初始礼物" json:"is_origin"`                                                         // 是否初始礼物
	CreateTime   time.Time       `gorm:"autoCreateTime;type:datetime;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"create_time"`                            // 创建时间
	UpdateTime   time.Time       `gorm:"autoUpdateTime;type:datetime;not null;default:CURRENT_TIMESTAMP;onUpdate:CURRENT_TIMESTAMP;comment:更新时间" json:"update_time"` // 更新时间
}

func NewSetPresentItemFromPb(p *pb.SetPresentItem) *SetPresentItem {
	return &SetPresentItem{
		SetID:        p.SetId,
		PresentID:    p.PresentId,
		PresentLevel: p.PresentLevel,
		Probability:  p.Probability,
		Ranking:      p.Rank,
		Threshold:    p.Threshold,
		Guaranteed:   p.Guaranteed,
		IsOrigin:     p.IsOrigin,
	}
}

func (s *SetPresentItem) TableName() string {
	return "set_present_item"
}

func (s *SetPresentItem) ToPb() *pb.SetPresentItem {
	return &pb.SetPresentItem{
		SetId:        s.SetID,
		PresentId:    s.PresentID,
		PresentLevel: s.PresentLevel,
		Probability:  s.Probability,
		Rank:         s.Ranking,
		Threshold:    s.Threshold,
		Guaranteed:   s.Guaranteed,
		IsOrigin:     s.IsOrigin,
	}
}

func (s *Store) SavePresentSetConfig(ctx context.Context, psc *PresentSetConfig) (uint32, error) {
	if psc == nil {
		return 0, nil
	}
	if psc.ID == 0 {
		if err := s.db.WithContext(ctx).Model(&PresentSetConfig{}).Create(psc).Error; err != nil {
			log.ErrorWithCtx(ctx, "SavePresentSetConfig Create err", err)
			return 0, err
		}
	} else {
		if err := s.db.WithContext(ctx).Model(psc).Omit("create_time").Save(psc).Error; err != nil {
			log.ErrorWithCtx(ctx, "SavePresentSetConfig Save err", err)
			return 0, err
		}
	}
	return psc.ID, nil
}

// SoftDeletePresentSetConfig 软删除
func (s *Store) SoftDeletePresentSetConfig(ctx context.Context, id uint32) error {
	if id == 0 {
		return nil
	}
	return s.db.WithContext(ctx).Model(&PresentSetConfig{}).Where("id = ?", id).Limit(1).Update("is_del", true).Error
}

// HardDeletePresentSetConfig 硬删除，同时删掉所有套组礼物配置
func (s *Store) HardDeletePresentSetConfig(ctx context.Context, id uint32) error {
	if id == 0 {
		return nil
	}
	return s.Transaction(ctx, func(tx *gorm.DB) error {
		if err := tx.Model(&PresentSetConfig{}).Where("id = ?", id).Limit(1).Unscoped().Delete(&PresentSetConfig{}).Error; err != nil {
			log.ErrorWithCtx(ctx, "HardDeletePresentSetConfig Delete PresentSetConfig err", err)
			return err
		}
		if err := tx.Model(&SetPresentItem{}).Where("set_id = ?", id).Unscoped().Delete(&SetPresentItem{}).Error; err != nil {
			log.ErrorWithCtx(ctx, "HardDeletePresentSetConfig Delete SetPresentItem err", err)
			return err
		}
		return nil
	})
}

func (s *Store) GetPresentSetConfigList(ctx context.Context, req *pb.GetPresentSetListNoCacheReq) ([]*PresentSetConfig, uint32, error) {
	var res []*PresentSetConfig
	var count int64
	q := s.db.WithContext(ctx).Model(&PresentSetConfig{})
	if req.GetLimit() == 0 {
		req.Limit = 10
	}
	if req.GetActiveStatus() != pb.PresentSetActiveStatus_PRESENT_SET_ACTIVE_STATUS_UNSPECIFIED {
		now := time.Now()
		if req.ActiveStatus == pb.PresentSetActiveStatus_PRESENT_SET_ACTIVE_STATUS_ACTIVE {
			q = q.Where("start_time < ?", now).Where("end_time > ?", now)
		} else {
			q = q.Where("start_time > ?", now).Or("end_time < ?", now)
		}
	}
	if req.GetSetId() > 0 {
		idStr := strconv.Itoa(int(req.GetSetId()))
		q = q.Where("id like ?", "%"+idStr+"%")
	}
	if req.GetSetName() != "" {
		q = q.Where("set_name like ?", "%"+req.GetSetName()+"%")
	}
	q = q.Where("is_del = ?", false)

	if err := q.Count(&count).Error; err != nil {
		log.ErrorWithCtx(ctx, "GetPresentSetConfigList Count err", err)
		return nil, 0, err
	}

	if err := q.Order("id desc").Offset(int(req.GetOffset())).Limit(int(req.GetLimit())).Find(&res).Error; err != nil {
		if isNotFound(err) {
			return res, uint32(count), nil
		}
		log.ErrorWithCtx(ctx, "GetPresentSetConfigList Find err", err)
		return nil, 0, err
	}
	return res, uint32(count), nil
}

func (s *Store) GetPresentSetConfigSimpleList(ctx context.Context, req *pb.GetPresentSetSimpleListNoCacheReq) ([]*PresentSetConfig, uint32, error) {
	var res []*PresentSetConfig
	var count int64
	q := s.db.WithContext(ctx).Model(&PresentSetConfig{})
	if req.GetKeyword() != "" {
		// search id or name
		keyword := req.GetKeyword()
		q = q.Where("id like ?", "%"+keyword+"%").Or("set_name like ?", "%"+keyword+"%")
	}
	q = q.Where("is_del = ?", false)

	if err := q.Count(&count).Error; err != nil {
		log.ErrorWithCtx(ctx, "GetPresentSetConfigList Count err", err)
		return nil, 0, err
	}

	if err := q.Order("id desc").Find(&res).Error; err != nil {
		if isNotFound(err) {
			return res, uint32(count), nil
		}
		log.ErrorWithCtx(ctx, "GetPresentSetConfigList Find err", err)
		return nil, 0, err
	}
	return res, uint32(count), nil
}

func (s *Store) GetPresentSetConfig(ctx context.Context, id uint32) (*PresentSetConfig, error) {
	if id == 0 {
		return nil, nil
	}
	var res PresentSetConfig
	err := s.db.WithContext(ctx).Model(&PresentSetConfig{}).
		Where("id = ?", id).
		Where("is_del = ?", false).
		First(&res).Error
	if err != nil {
		if isNotFound(err) {
			return &res, nil
		}
		log.ErrorWithCtx(ctx, "GetPresentSetConfig err", err)
		return nil, err
	}
	return &res, err
}

func (s *Store) GetPresentSetConfigsByIds(ctx context.Context, ids []uint32) ([]*PresentSetConfig, error) {
	var res []*PresentSetConfig
	if len(ids) == 0 {
		return res, nil
	}
	err := s.db.WithContext(ctx).Model(&PresentSetConfig{}).
		Where("id IN (?)", ids).
		Where("is_del = ?", false).
		Find(&res).Error
	if err != nil {
		if isNotFound(err) {
			return res, nil
		}
		log.ErrorWithCtx(ctx, "GetPresentSetConfigsByIds err", err)
		return nil, err
	}
	return res, err
}

func (s *Store) GetPresentSetConfigPBsByIds(ctx context.Context, ids []uint32) ([]*pb.PresentSetConfig, error) {
	res, err := s.GetPresentSetConfigsByIds(ctx, ids)
	if err != nil {
		return nil, err
	}
	pbRes := make([]*pb.PresentSetConfig, 0)
	for _, r := range res {
		pbRes = append(pbRes, r.ToPb())
	}
	return pbRes, nil
}

func (s *Store) SaveSetPresentItems(ctx context.Context, setId uint32, items []*SetPresentItem) error {
	if setId == 0 || len(items) == 0 {
		return nil
	}
	return s.Transaction(ctx, func(tx *gorm.DB) error {
		if err := s.updateSetChangeTime(ctx, tx, setId); err != nil {
			log.ErrorWithCtx(ctx, "SaveSetPresentItems updateSetChangeTime err", err)
			return err
		}
		if err := tx.Model(&SetPresentItem{}).Where("set_id = ?", setId).Unscoped().Delete(&SetPresentItem{}).Error; err != nil {
			log.ErrorWithCtx(ctx, "SaveSetPresentItems Delete SetPresentItem err", err)
			return err
		}
		if err := tx.Model(&SetPresentItem{}).CreateInBatches(items, 100).Error; err != nil {
			log.ErrorWithCtx(ctx, "SaveSetPresentItems CreateInBatches err", err)
			return err
		}
		return nil
	})
}

func (s *Store) updateSetChangeTime(ctx context.Context, tx *gorm.DB, setId uint32) error {
	if tx == nil {
		tx = s.db.WithContext(ctx)
	}
	if setId == 0 {
		return nil
	}
	return tx.Model(&PresentSetConfig{}).Where("id = ?", setId).Limit(1).Update("change_time", time.Now()).Error
}

func (s *Store) GetSetPresentItemsBySetIds(ctx context.Context, setId []uint32) ([]*SetPresentItem, error) {
	var res []*SetPresentItem
	if len(setId) == 0 {
		return res, nil
	}
	err := s.db.WithContext(ctx).Model(&SetPresentItem{}).
		Where("set_id IN (?)", setId).
		Find(&res).Error
	if err != nil {
		if isNotFound(err) {
			return res, nil
		}
		log.ErrorWithCtx(ctx, "GetSetPresentItemsBySetIds Find err", err)
		return nil, err
	}
	return res, err
}

func (s *Store) GetSetIdsByPresentIds(ctx context.Context, presentIds []uint32) ([]uint32, error) {
	var res []uint32
	if len(presentIds) == 0 {
		return res, nil
	}
	err := s.db.WithContext(ctx).Model(&SetPresentItem{}).
		Where("present_id IN (?)", presentIds).
		Pluck("DISTINCT set_id", &res).Error
	if err != nil {
		if isNotFound(err) {
			return res, nil
		}
		log.ErrorWithCtx(ctx, "GetSetIdsByPresentIds Pluck err", err)
		return nil, err
	}
	return res, err
}

// DeleteEmperorSetAssociated 删除帝王套关联
func (s *Store) DeleteEmperorSetAssociated(ctx context.Context, tx *gorm.DB, emperorSetId uint32) error {
	if emperorSetId == 0 {
		return nil
	}
	if tx == nil {
		tx = s.db.WithContext(ctx)
	}

	// 获取关联帝王套的礼物套组IDs
	var setIds []uint32
	if err := tx.Model(&PresentSetConfig{}).Where("emperor_set_id = ?", emperorSetId).Pluck("id", &setIds).Error; err != nil {
		if !isNotFound(err) {
			log.ErrorWithCtx(ctx, "DeleteEmperorSetAssociated Pluck err", err)
			return err
		}
	}

	if len(setIds) == 0 {
		return nil
	}

	log.InfoWithCtx(ctx, "DeleteEmperorSetAssociated delete emperor set associated, emperorSetId:%d, setIds: %v", emperorSetId, setIds)

	return tx.Model(&PresentSetConfig{}).Where("id in (?)", setIds).
		Update("emperor_set_id", 0).Error
}

// GetSetIdListByEmperorId 获取帝王套关联的礼物套组ID列表
func (s *Store) GetSetIdListByEmperorId(ctx context.Context, emperorSetId uint32) ([]uint32, error) {
	var res []uint32
	if emperorSetId == 0 {
		return res, nil
	}
	err := s.db.WithContext(ctx).Model(&PresentSetConfig{}).
		Where("emperor_set_id = ?", emperorSetId).
		Pluck("id", &res).Error
	if err != nil {
		if isNotFound(err) {
			return res, nil
		}
		log.ErrorWithCtx(ctx, "GetSetIdListByEmperorId Pluck err", err)
		return nil, err
	}
	return res, err
}
