package mgr

import (
	"context"
	"fmt"
	"gitlab.ttyuyin.com/avengers/tyr/pkg/cluster/timer"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/protocol/grpc"
	"golang.52tt.com/protocol/common/status"
	pb "golang.52tt.com/protocol/services/present-set"
	"golang.52tt.com/services/present-set/internal/cache"
	"golang.52tt.com/services/present-set/internal/conf"
	config "golang.52tt.com/services/present-set/internal/conf/ttconfig/present_set"
	"golang.52tt.com/services/present-set/internal/event"
	"golang.52tt.com/services/present-set/internal/producer"
	"golang.52tt.com/services/present-set/internal/rpc"
	"golang.52tt.com/services/present-set/internal/store"
	"golang.52tt.com/services/present-set/internal/utils"
	"google.golang.org/grpc/codes"
	"math/big"
	"time"
)

type Manager struct {
	sc                  conf.IConfig
	store               store.IStore
	cache               cache.ICache
	rpc                 rpc.IRpcClients
	presentEventLinkSub event.IEvent
	dyConf              config.DynamicConfig
	kafkaProducer       *producer.KafkaProduceMgr
	timer               *timer.Timer
}

func NewManager(ctx context.Context, sc conf.IConfig) (*Manager, error) {
	var err error
	m := &Manager{sc: sc}

	m.dyConf, err = config.InitPresentSetConfig()
	if err != nil {
		log.ErrorWithCtx(ctx, "NewManager InitPresentSetConfig err:%s", err)
		return nil, err
	}

	m.rpc = rpc.NewClients()

	m.store, err = store.NewMysql(sc.GetMySqlConfig())
	if err != nil {
		log.ErrorWithCtx(ctx, "NewManager NewMysql err:%s", err)
		return nil, err
	}

	m.cache, err = cache.NewCache(ctx, sc.GetRedisConfig())
	if err != nil {
		log.ErrorWithCtx(ctx, "NewManager NewCache err:%s", err)
		return nil, err
	}

	if err = m.cache.InitPresentCache(ctx, m.rpc.GetPresentConfigList); err != nil {
		log.ErrorWithCtx(ctx, "NewManager InitPresentCache err:%s", err)
		return nil, err
	}
	if err = m.cache.InitPresentSetCache(ctx, m.getPresentSetPbAllListNoCache); err != nil {
		log.ErrorWithCtx(ctx, "NewManager InitPresentSetCache err:%s", err)
		return nil, err
	}
	if err = m.cache.InitEmperorSetCache(ctx, m.getEmperorSetPbAllListNoCache); err != nil {
		log.ErrorWithCtx(ctx, "NewManager InitEmperorSetCache err:%s", err)
		return nil, err
	}

	m.timer, err = m.NewTimer(ctx, m.cache.GetRedisClient())
	if err != nil {
		log.ErrorWithCtx(ctx, "Failed to NewTimer err %s", err.Error())
		return nil, err
	}

	m.kafkaProducer, err = producer.NewKafkaProducer(ctx, sc.GetPresentSetKafkaConfig().BrokerList(), sc.GetPresentSetKafkaConfig().Topics)
	if err != nil {
		log.ErrorWithCtx(ctx, "NewManager NewKafkaProducer err:%s", err)
		return nil, err
	}

	return m, nil
}

func (m *Manager) ShutDown() {
	m.presentEventLinkSub.Close()
}

func (m *Manager) UserSentSetPresent(ctx context.Context, req *pb.UserSentSetPresentReq) (*pb.UserSentSetPresentResp, error) {
	resp := &pb.UserSentSetPresentResp{}
	if req.GetPresentId() == 0 || req.GetPresentCount() == 0 || req.GetUid() == 0 || len(req.GetOrderId()) == 0 {
		return resp, nil
	}

	sendCountMax := m.dyConf.GetBatchSendMax()
	if len(req.GetOrderId()) > int(sendCountMax) {
		log.WarnWithCtx(ctx, "UserSentSetPresent err, send count over max, uid:%d, count:%d, max:%d", req.GetUid(), len(req.GetOrderId()), sendCountMax)
		return resp, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid)
	}

	uid, presentId, count, sentTimeU := req.GetUid(), req.GetPresentId(), req.GetPresentCount(), req.GetSendTime()
	sendTime := time.Unix(int64(sentTimeU), 0)
	batchOrderId := m.genBatchOrderID(req.GetOrderId()[0])

	// 检查礼物信息
	presentCfg := m.cache.PresentConfigCache().GetPresent(ctx, presentId)
	if presentCfg == nil || !isPresentValid(presentCfg) {
		log.WarnWithCtx(ctx, "UserSentSetPresent GetPresent err, cfg not found or expired, presentId:%d, uid:%d", presentId, uid)
		return resp, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, fmt.Sprintf("礼物ID: %d 失效或不存在", presentId))
	}

	if !m.checkSetPresent(presentId, presentCfg) {
		log.DebugWithCtx(ctx, "UserSentSetPresent checkSetPresent err, is not setCfg tag type, presentId:%d, uid:%d", presentId, uid)
		return resp, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, fmt.Sprintf("礼物ID: %d 非套组礼物类型", presentId))
	}

	// 获取所有礼物配置，找出对应礼物当前活跃的set
	setCfg := m.cache.PresentSetConfigCache().GetCurrentActivePresentSetByPid(ctx, presentId)
	if setCfg == nil {
		log.WarnWithCtx(ctx, "UserSentSetPresent GetCurrentActivePresentSetByPid err: setCfg not found by presentId:%d, uid:%d", presentId, uid)
		return resp, nil
	}
	if !utils.CheckSetActive(setCfg) {
		log.WarnWithCtx(ctx, "UserSentSetPresent checkSetActive err: setCfg not active, presentId:%d, uid:%d", presentId, uid)
		return resp, nil
	}
	setId := setCfg.GetSetId()

	log.InfoWithCtx(ctx, "UserSentSetPresent start. uid:%d, pid:%d, count:%d, setId:%d, batchOrderId:%s", uid, presentId, count, setId, batchOrderId)

	debugTimer := utils.NewDebugTimer()
	defer func() {
		log.DebugWithCtx(ctx, "UserSentSetPresent cost: %+v", debugTimer.GetNodeList())
	}()

	var lottery *SetLottery
	var isGetAll, hasNotifyAll, hasGetAll bool
	preCollectedPresentIdList := make([]uint32, 0) // 已收集的礼物ID列表

	handle := func() error {
		// record user last send present
		err := m.RecordSendPresent(ctx, uid, setId, presentId, sendTime)
		if err != nil {
			log.ErrorWithCtx(ctx, "UserSentSetPresent RecordSendPresent err:%s, uid:%d, presentId:%d, setId:%d", err, uid, presentId, setId)
			return err
		}

		// 获取用户已经收集的礼物
		userSetPresentInfoMap, err := m.store.GetPresentSetUserInfo(ctx, uid, []uint32{setId})
		if err != nil {
			log.ErrorWithCtx(ctx, "UserSentSetPresent GetUserInfoBySetId err:%s, uid:%d, setId:%d", err, uid, setId)
			return err
		}

		userSetPresentInfo := userSetPresentInfoMap[setId]
		if userSetPresentInfo == nil {
			log.WarnWithCtx(ctx, "UserSentSetPresent GetUserInfoBySetId err userSetPresentInfo not found, uid:%d, setId:%d", uid, setId)
			return nil
		}

		// 套组礼物计数
		sumCounter, err := m.cache.IncrUserSetCounter(ctx, uid, setId, count)
		if err != nil {
			log.ErrorWithCtx(ctx, "UserSentSetPresent IncrUserSetCounter err:%s, uid:%d", err, uid)
			return err
		}

		isSetPresentMap := make(map[uint32]bool)
		originPresent := setCfg.GetOriginPresent().GetPresentId()
		if len(setCfg.GetPresentList()) > 0 {
			for _, item := range setCfg.GetPresentList() {
				isSetPresentMap[item.GetPresentId()] = true
			}
		}

		preCollectedPresentIdList = append(preCollectedPresentIdList, originPresent)
		// 已收集的礼物
		for _, item := range userSetPresentInfo {
			log.DebugWithCtx(ctx, "UserSentSetPresent uid:%d userSetPresentInfo:%+v", uid, item)
			if item.IsUnlock && isSetPresentMap[item.PresentId] {
				preCollectedPresentIdList = append(preCollectedPresentIdList, item.PresentId)
			}
		}

		// 检查是否获取全部
		isGetAll, hasGetAll, hasNotifyAll = CheckCollectAndNotify(userSetPresentInfo, setCfg)
		if isGetAll {
			log.DebugWithCtx(ctx, "UserSentSetPresent already get all, uid:%d", uid)
			return nil
		}

		// 抽奖
		lottery = NewSetLottery(ctx, uid, setId, sumCounter, count, batchOrderId, setCfg.GetPresentList(), preCollectedPresentIdList)
		return nil
	}

	if err := m.store.CreateSetPresentOrderReconcile(ctx, batchOrderId, req); err != nil {
		log.ErrorWithCtx(ctx, "UserSentSetPresent CreateSetPresentOrderReconcile err:%s, uid:%d", err, uid)
		return resp, err
	}

	var reconcileStatus store.ReconcileStatus
	if err := handle(); err != nil {
		reconcileStatus = store.ReconcileFailed
	} else {
		reconcileStatus = store.ReconcileSuccess
	}

	debugTimer.ElapsedTag("END_LOTTERY")

	// 抽奖结果
	collection := lottery.GetCollection()
	collectIds, collectRecords := collection.GetCollectPresentIds(), collection.GetCollectRecords()

	// 记录成功解锁收集的礼物，并更新对账状态
	if err := m.store.CreateSetPresentCollectRecord(ctx, batchOrderId, sendTime, reconcileStatus, collectRecords); err != nil {
		log.ErrorWithCtx(ctx, "UserSentSetPresent CreateSetPresentCollectRecord err:%s, uid:%d", err, uid)
		return resp, err
	}

	if collection.IsEmpty() {
		log.DebugWithCtx(ctx, "UserSentSetPresent no will collect present, uid:%d", uid)
		// 如果没集齐也没解锁新的，但是已解锁又和总数一致
		// 说明删除了套组中的某些礼物导致集齐了
		if !hasGetAll && collection.IsEmpty() && len(setCfg.GetPresentList()) == len(preCollectedPresentIdList) {
			_ = m.RecordGetAllPresent(ctx, uid, setId)
			timeStr := time.Unix(int64(setCfg.GetEndTime()), 0).Local().Format("2006-01-02 15:04")

			tmpCtx, _ := NewContextWithInfoTimeout(ctx, time.Second*2)

			_ = m.PresentSetUnlockNotify(tmpCtx, uid, setId, make([]uint32, 0), true)

			if setCfg.GetPeriod() == pb.PresentSetPeriod_PRESENT_SET_PERIOD_LONG {
				go m.rpc.SendGetAllImLong(tmpCtx, uid, setCfg.GetSetName())
			} else {
				go m.rpc.SendGetAllIm(tmpCtx, uid, setCfg.GetSetName(), timeStr)
			}

			if !hasNotifyAll {
				go m.NotifyAllFirstCollect(tmpCtx, uid, setId)
				log.DebugWithCtx(ctx, "UserSentSetPresent notify all, uid:%d, setId:%d createTime %v", uid, setId, req.GetSendTime())
			}
		}

		return resp, nil
	}

	log.InfoWithCtx(ctx, "UserSentSetPresent start unlock. will collect present list:%+v, batchOrderId:%s, uid:%d", collectIds, batchOrderId, uid)

	debugTimer.ElapsedTag("END_RECONCILE")

	// 收集解锁

	tmpCtx, _ := NewContextWithInfoTimeout(ctx, time.Second*2)

	unlockItems := make([]uint32, 0)
	for _, pid := range collectIds {
		tmpGetAll, err := m.collect(tmpCtx, uid, setId, pid)
		if err != nil {
			log.ErrorWithCtx(ctx, "UserSentSetPresent collect present id:%d err:%s, uid:%d", pid, err, uid)
			continue
		}

		unlockItems = append(unlockItems, pid)

		if tmpGetAll {
			isGetAll = true
		}
	}

	debugTimer.ElapsedTag("END_UNLOCK")

	// push
	_ = m.PresentSetUnlockNotify(tmpCtx, uid, setId, unlockItems, isGetAll)

	debugTimer.ElapsedTag("END_PUSH")

	return resp, nil
}

func (m *Manager) collect(ctx context.Context, uid, setId, presentId uint32) (bool, error) {
	// finish collect
	// record new collect present

	setInfo := m.cache.PresentSetConfigCache().GetPresentSet(ctx, setId)
	presentInfo := m.cache.PresentConfigCache().GetPresent(ctx, presentId)
	presentSetInfo := &pb.SetPresentItem{}
	for _, item := range setInfo.GetPresentList() {
		if item.GetPresentId() == presentId {
			presentSetInfo = item
			break
		}
	}
	timeStr := time.Unix(int64(setInfo.GetEndTime()), 0).Local().Format("2006-01-02 15:04")

	presentSetUserInfo, createTime, err := m.UnlockSetPresent(ctx, uid, setId, presentId)
	if err != nil {
		log.ErrorWithCtx(ctx, "UserSentSetPresent UnlockSetPresent err:%s, uid:%d, setId:%d, presentId:%d", err, uid, setId, presentId)
		return false, err
	}

	if setInfo.GetPeriod() == pb.PresentSetPeriod_PRESENT_SET_PERIOD_LONG {
		go m.rpc.SendUnlockImLong(context.Background(), uid, getLevelString(presentSetInfo.GetPresentLevel()), presentInfo.GetName())
	} else {
		go m.rpc.SendUnlockIm(context.Background(), uid, getLevelString(presentSetInfo.GetPresentLevel()), presentInfo.GetName(), timeStr)
	}

	collected := uint32(0)
	for _, item := range presentSetUserInfo {
		if item.IsUnlock || item.PresentId == setInfo.GetOriginPresent().GetPresentId() {
			collected++
		}
	}

	go m.SendUserPresentSetAward(grpc.NewContextWithInfo(ctx), uid, setId, collected, createTime)

	// append to last 10 collect users
	_ = m.cache.AddAwardBroadcast(ctx, uid, setId, presentId)

	isGetAll, _, hasNotifyAll := CheckCollectAndNotify(presentSetUserInfo, m.cache.PresentSetConfigCache().GetPresentSet(ctx, setId))

	if isGetAll {
		_ = m.RecordGetAllPresent(ctx, uid, setId)

		if setInfo.GetPeriod() == pb.PresentSetPeriod_PRESENT_SET_PERIOD_LONG {
			go m.rpc.SendGetAllImLong(context.Background(), uid, setInfo.GetSetName())
		} else {
			go m.rpc.SendGetAllIm(context.Background(), uid, setInfo.GetSetName(), timeStr)
		}
	}

	if isGetAll && !hasNotifyAll {
		m.NotifyAllFirstCollect(ctx, uid, setId)
		log.DebugWithCtx(ctx, "UserSentSetPresent notify all, uid:%d, setId:%d createTime %v", uid, setId, createTime)
	}

	return isGetAll, nil
}

func CheckCollectAndNotify(presentSetUserInfo []*store.PresentSetUserInfo, set *pb.PresentSetConfig) (bool, bool, bool) {
	isCollectAll := true
	hasNotifyAll := false
	hasGetAll := true

	presentMap := make(map[uint32]*store.PresentSetUserInfo)
	for _, item := range presentSetUserInfo {
		presentMap[item.PresentId] = item
	}

	for _, item := range set.GetPresentList() {
		if !item.GetIsOrigin() && (presentMap[item.GetPresentId()] == nil || !presentMap[item.GetPresentId()].IsUnlock) {
			isCollectAll = false
		}
		if !item.GetIsOrigin() && (presentMap[item.GetPresentId()] == nil || !presentMap[item.GetPresentId()].GetAll) {
			hasGetAll = false
		}
		if presentMap[item.GetPresentId()] != nil && presentMap[item.GetPresentId()].NotifyAll {
			hasNotifyAll = true
		}
	}

	return isCollectAll, hasGetAll, hasNotifyAll
}

func (m *Manager) genBatchOrderID(orderID string) string {
	return fmt.Sprintf("SET_%s", orderID)
}

// TestGetUserSetLotteryInfo 获取用户套组抽奖信息
func (m *Manager) TestGetUserSetLotteryInfo(ctx context.Context, req *pb.TestGetUserSetLotteryInfoReq) (*pb.TestGetUserSetLotteryInfoResp, error) {
	uid := req.GetUid()
	setId := req.GetSetId()
	resp := &pb.TestGetUserSetLotteryInfoResp{
		Uid: uid, SetId: setId,
		LotteryPresentInfoList: make([]*pb.LotteryPresentInfo, 0),
		PoolInfoList:           make([]*pb.TestGetUserSetLotteryInfoResp_PoolInfo, 0),
	}
	if uid == 0 || setId == 0 {
		return resp, nil
	}

	pCache := m.cache.PresentConfigCache()
	setCfg := m.cache.PresentSetConfigCache().GetPresentSet(ctx, setId)
	if setCfg == nil {
		log.WarnWithCtx(ctx, "TestGetUserSetLotteryInfo GetPresentSet err, setCfg not found, uid:%d, setId:%d", uid, setId)
		return resp, nil
	}

	counter, _ := m.cache.GetUserSetCounter(ctx, uid, setId)
	resp.SendPresentCounter = counter

	// 获取用户已经收集的礼物

	userSetPresentInfoMap, err := m.store.GetPresentSetUserInfo(ctx, uid, []uint32{setId})
	if err != nil {
		log.ErrorWithCtx(ctx, "TestGetUserSetLotteryInfo GetUserInfoBySetId err:%s, uid:%d, setId:%d", err, uid, setId)
		return resp, err
	}

	userSetPresentInfo := userSetPresentInfoMap[setId]
	if userSetPresentInfo == nil {
		log.WarnWithCtx(ctx, "TestGetUserSetLotteryInfo GetUserInfoBySetId userSetPresentInfo not found, uid:%d, setId:%d", uid, setId)
	}

	preCollectedPresentIdList := make([]uint32, 0) // 已收集的礼物ID列表
	// 已收集的礼物
	for _, item := range userSetPresentInfo {
		log.DebugWithCtx(ctx, "TestGetUserSetLotteryInfo uid:%d userSetPresentInfo:%+v", uid, item)
		if item.IsUnlock {
			preCollectedPresentIdList = append(preCollectedPresentIdList, item.PresentId)
		}
	}
	resp.CollectedPresentList = preCollectedPresentIdList

	lottery := NewSetLottery(ctx, uid, setId, counter, 1, "TestGetUserSetLotteryInfo", setCfg.GetPresentList(), preCollectedPresentIdList)
	for _, present := range lottery.GetLotteryPresentInfoList() {
		present.PresentName = pCache.GetPresent(ctx, present.GetPresentId()).GetName()
		resp.LotteryPresentInfoList = append(resp.LotteryPresentInfoList, present)
	}
	convPart := func(f big.Float) string {
		return fmt.Sprintf("%s %%", f.Mul(&f, big.NewFloat(100)).Text('f', 6))
	}
	probabilitySum := big.NewFloat(0).SetPrec(48).SetMode(big.ToNearestEven)
	resp.LotteryProbability = convPart(*probabilitySum)
	return resp, nil
}

// TestResetUserInfo 重置用户信息
func (m *Manager) TestResetUserInfo(ctx context.Context, req *pb.TestResetUserInfoReq) (*pb.TestResetUserInfoResp, error) {
	resp := &pb.TestResetUserInfoResp{}
	uid := req.GetUid()
	if uid == 0 {
		return resp, nil
	}
	sets := m.cache.PresentSetConfigCache().GetPresentSetList(ctx)
	setIds := make([]uint32, 0)
	for _, set := range sets {
		setIds = append(setIds, set.GetSetId())
	}

	// 删除送礼计数
	if err := m.cache.DeleteUserSetsCounter(ctx, uid, setIds); err != nil {
		log.ErrorWithCtx(ctx, "TestResetUserInfo DeleteUserSetsCounter err:%s, uid:%d", err, uid)
	}

	// 删除用户收集记录
	if err := m.ClearUserUnlockInfo(ctx, uid); err != nil {
		log.ErrorWithCtx(ctx, "TestResetUserInfo DeleteUserCollectRecord err:%s, uid:%d", err, uid)
	}

	// 删除奖励
	if err := m.RemoveUserPresentSetAward(ctx, uid, setIds); err != nil {
		log.ErrorWithCtx(ctx, "TestResetUserInfo RemoveUserPresentSetAward err:%s, uid:%d", err, uid)
	}

	return resp, nil
}

func (m *Manager) CheckPresentSetValid(ctx context.Context, uid, presentId uint32) error {
	// 先找出活跃的set
	setCfg := m.cache.PresentSetConfigCache().GetCurrentActivePresentSetByPid(ctx, presentId)
	if setCfg == nil {
		return protocol.NewExactServerError(nil, status.ErrRevenueSvrErr, "活动已过期")
	}

	// 检查是否活跃
	if !utils.CheckSetActive(setCfg) {
		return protocol.NewExactServerError(nil, status.ErrRevenueSvrErr, "活动已过期")
	}

	// 检查是否有权限
	userInfo, _, err := m.GetUserInfo(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "CheckPresentSetValid GetUserInfo err:%s, uid:%d", err, uid)
		return err
	}

	hasPresent := false
	for _, item := range userInfo[setCfg.GetSetId()].GetPresentList() {
		if item.PresentId == presentId && (item.IsCollected || item.GetPresentId() == setCfg.GetOriginPresent().GetPresentId()) {
			hasPresent = true
		}
	}

	if !hasPresent {
		return protocol.NewExactServerError(nil, status.ErrRevenueSvrErr, "没有该礼物权限！")
	}

	return nil
}

func getLevelString(level pb.PresentLevel) string {
	switch level {
	case pb.PresentLevel_PRESENT_LEVEL_R:
		return "R"
	case pb.PresentLevel_PRESENT_LEVEL_SR:
		return "SR"
	case pb.PresentLevel_PRESENT_LEVEL_SSR:
		return "SSR"
	default:
		return "N"
	}
}

func NewContextWithInfoTimeout(ctx context.Context, timeOut time.Duration) (newCtx context.Context, cancel context.CancelFunc) {
	newCtx, cancel = context.WithTimeout(context.Background(), timeOut)
	sv, _ := grpc.ServiceInfoFromContext(ctx)
	newCtx = grpc.WithServiceInfo(newCtx, sv)
	return newCtx, cancel
}
