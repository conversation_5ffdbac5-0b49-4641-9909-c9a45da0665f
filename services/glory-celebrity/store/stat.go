package store

import (
	"context"
	"errors"
	"time"

	mysql_driver "github.com/go-sql-driver/mysql"
	"golang.52tt.com/pkg/log"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

// 添加进入名流殿堂
func (s *Store) AddCelebrityPalaceInfo(ctx context.Context, info *CelebrityPalaceInfo, orderId string, sendTs, price uint32) error {
	now := time.Now()

	//先创建一条。
	m := &CelebrityPalaceInfo{
		Uid:       info.Uid,
		GiftId:    info.GiftId,
		PeriodsNo: info.PeriodsNo,
		Ctime:     now,
		Mtime:     now,
	}
	err := s.db.Table(m.TableName()).Clauses(clause.Insert{
		Modifier: "IGNORE",
	}).Create(m).Error

	if driverErr, ok := err.(*mysql_driver.MySQLError); ok && driverErr.Number == 1146 { //未创建
		s.createCelebrityPalaceTb(0, info.PeriodsNo)

		err = s.db.Table(m.TableName()).Clauses(clause.Insert{
			Modifier: "IGNORE",
		}).Create(m).Error
	}

	if err != nil {
		log.ErrorWithCtx(ctx, "AddCelebrityPalaceInfo failed, info %s, err: %v", info.String(), err)
		return err
	}

	sendTime := time.Unix(int64(sendTs), 0)
	e := s.Transaction(ctx, func(tx *gorm.DB) error {
		//添加统计流水
		stat := &CelebrityPalaceGiftStat{
			OrderId:     orderId,
			Uid:         info.Uid,
			GiftId:      info.GiftId,
			GiftNum:     info.GiftNum,
			Price:       price,
			OutsideTime: sendTime,
		}
		err := s.db.Table(stat.TableName()).Create(stat).Error
		if err != nil {
			return err
		}
		//更新
		updateData := map[string]interface{}{
			"gift_num":          gorm.Expr("gift_num+?", info.GiftNum),
			"last_target":       info.LastTarget,
			"last_num":          info.LastNum,
			"last_cid":          info.LastCid,
			"from_ukw_account":  info.FromUkwAccount,
			"from_ukw_nickname": info.FromUkwNickname,
			"to_ukw_account":    info.ToUkwAccount,
			"to_ukw_nickname":   info.ToUkwNickname,
			"is_anchor":         info.IsAnchor,
			"is_guild_leader":   info.IsGuildLeader,
			"is_guild_mgr":      info.IsGuildMgr,
			"is_black":          info.IsBlack,
			"mtime":             gorm.Expr("now()"),
		}
		if info.GuildId > 0 {
			updateData["guild_id"] = info.GuildId
		}
		err = s.db.Table(info.TableName()).Where("uid=? and gift_id=?", info.Uid, info.GiftId).Updates(updateData).Error
		if err != nil {
			return err
		}
		return nil
	})
	if e != nil {
		log.ErrorWithCtx(ctx, "UpdateCelebrityPalaceInfo failed, info %s, err: %v", info.String(), e)
		if driverErr, ok := e.(*mysql_driver.MySQLError); ok && driverErr.Number == 1062 { //订单已存在,已统计
			return nil
		}
		return e
	}
	return nil
}

///处理一条记录到名流周榜统计
func (s *Store) AddCelebrityWeekOne(ctx context.Context, r *GloryWorldFragmentReward, rank *CelebrityWeekRank) error {
	now := time.Now()
	rank.Score = 0
	rank.Ctime = now
	rank.Mtime = now
	e := s.db.Table(rank.TableName()).Clauses(clause.Insert{
		Modifier: "IGNORE",
	}).Create(rank).Error
	if e != nil {
		log.ErrorWithCtx(ctx, "addCelebrityWeekOne create fail err %v", e)
		return e
	}

	e = s.Transaction(ctx, func(tx *gorm.DB) error {
		updateData := map[string]interface{}{
			"score":           gorm.Expr("score+?", r.FragmentNum),
			"is_anchor":       rank.IsAnchor,
			"is_guild_leader": rank.IsGuildLeader,
			"is_guild_mgr":    rank.IsGuildMgr,
			"is_black":        rank.IsBlack,
		}
		if rank.GuildId > 0 {
			updateData["guild_id"] = rank.GuildId
		}
		err := tx.Table(rank.TableName()).Where("uid = ? and fragment_type = ?", r.Uid, r.FragmentType).Updates(updateData).Error

		if err != nil {
			log.ErrorWithCtx(ctx, "addCelebrityWeekOne update fail err %v", err)
			return err
		}

		err = tx.Table(r.TableName()).Where("id=?", r.ID).Update("is_stat", 1).Error
		if err != nil {
			log.ErrorWithCtx(ctx, "addCelebrityWeekOne update progress fail err %v", err)
			return err
		}
		return nil
	})

	return e
}

//获取未统计的星钻获取记录
func (s *Store) GetCelebrityFragmentRecordNoStat(ctx context.Context, beginTime time.Time, limit uint32) ([]*GloryWorldFragmentReward, error) {
	recordList := []*GloryWorldFragmentReward{}
	err := s.readOnlyDb.Where("update_time >=? and is_send=1 and is_stat = 0", beginTime).Limit(int(limit)).Order("id").Find(&recordList).Error
	if err != nil {
		log.ErrorWithCtx(ctx, "GetCelebrityFragmentRewardRecord fail, beginTime:%v, err %v", beginTime, err)
		return recordList, err
	}
	return recordList, nil
}

//更新统计状态
func (s *Store) UpdateCelebrityFragementRecordStat(ctx context.Context, Id uint64) error {
	r := &GloryWorldFragmentReward{}
	err := s.db.Table(r.TableName()).Where("id=?", Id).Update("is_stat", 1).Error
	if err != nil {
		log.ErrorWithCtx(ctx, "UpdateCelebrityFragementRecordStat fail err %v， ID:%d", err, Id)
		return err
	}
	return nil
}

const (
	Stat_Hour  = 0
	Stat_Day   = 1
	Stat_Month = 2
)

type Result struct {
	Num uint32
}

//统计发放星钻
func (s *Store) StatStarFragment(ctx context.Context, beginTime time.Time, endTime time.Time, fragmentType uint32, statPeriods uint32) (*StarFragmentStat, error) {

	//beginTime := t.Add(-time.Duration(t.Minute())).Add(-time.Duration(t.Second()))
	//endTime := beginTime.Add(time.Hour).Add(time.Second)

	sql := "select sum(fragment_num) as num from glory_world_fragment_reward where fragment_type=? and is_send = 1 and update_time between ? and ? "
	result := &Result{}
	err := s.readOnlyDb.Raw(sql, fragmentType, beginTime, endTime).Scan(result).Error
	if err != nil {
		log.ErrorWithCtx(ctx, "StatStarFragment fragment num :sql:%s fail err %v", sql, err)
		return nil, err
	}
	now := time.Now()
	stat := &StarFragmentStat{
		StatPeriods:  statPeriods,
		FragmentType: fragmentType,
		FragmentNum:  result.Num,
		Ctime:        now,
		Mtime:        now,
	}
	switch statPeriods {
	case Stat_Hour:
		stat.StatTime = beginTime.Format("20060102:15")
	case Stat_Day:
		stat.StatTime = beginTime.Format("20060102")
	case Stat_Month:
		stat.StatTime = beginTime.Format("200601")
	default:
		return nil, errors.New("err stat periods")
	}

	result.Num = 0
	sql = "select count(distinct uid) as num from glory_world_fragment_reward where fragment_type=? and is_send = 1 and update_time between ? and ?"
	err = s.readOnlyDb.Raw(sql, fragmentType, beginTime, endTime).Scan(result).Error
	if err != nil {
		log.ErrorWithCtx(ctx, "StatStarFragment person num :sql:%s fail err %v", sql, err)
		return nil, err
	}
	stat.PersonNum = result.Num

	err = s.db.Table(stat.TableName()).Clauses(clause.OnConflict{
		DoUpdates: clause.AssignmentColumns([]string{"fragment_num", "person_num"}),
	}).Create(stat).Error
	if err != nil {
		log.ErrorWithCtx(ctx, "StatStarFragment create fail err %v. stat:%v", err, stat)
		return nil, err
	}
	return stat, nil
}

//根据时间获取统计值
func (s *Store) GetStarFragmentStat(ctx context.Context, t time.Time, fragmentType uint32, statPeriods uint32) (*StarFragmentStat, error) {
	stat := &StarFragmentStat{}
	stat.FragmentType = fragmentType
	stat.StatPeriods = statPeriods
	switch statPeriods {
	case Stat_Hour:
		stat.StatTime = t.Format("20060102:15")
	case Stat_Day:
		stat.StatTime = t.Format("20060102")
	case Stat_Month:
		stat.StatTime = t.Format("200601")
	default:
		return nil, errors.New("err stat periods")
	}
	err := s.readOnlyDb.Table(stat.TableName()).Where("stat_time=? and fragment_type=? and stat_periods=?", stat.StatTime, fragmentType, stat.StatPeriods).First(stat).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return stat, nil
		}
		log.ErrorWithCtx(ctx, "GetStarFragmentStat err fail err %v. stat:%v", err, stat)
		return nil, err
	}
	return stat, nil
}

func (s *Store) GetCelebrityPlaceGiftOrders(ctx context.Context, beginTime, endTime int64) ([]*CelebrityPalaceGiftStat, error) {
	stat := &CelebrityPalaceGiftStat{
		OutsideTime: time.Unix(beginTime, 0),
	}
	statList := []*CelebrityPalaceGiftStat{}
	err := s.readOnlyDb.Table(stat.TableName()).Where("outside_time>=? and outside_time <?", time.Unix(beginTime, 0), time.Unix(endTime, 0)).Find(&statList).Error
	if err != nil {
		if driverErr, ok := err.(*mysql_driver.MySQLError); ok && driverErr.Number == 1146 { //未创建
			return nil, nil
		}
		log.ErrorWithCtx(ctx, "GetCelebrityPlaceGiftOrders err fail err %v. stat:%v", err, stat)
		return nil, err
	}
	return statList, nil
}
