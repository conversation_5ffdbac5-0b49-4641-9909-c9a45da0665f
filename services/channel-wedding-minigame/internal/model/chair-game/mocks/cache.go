// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/services/channel-wedding-minigame/internal/model/chair-game/cache (interfaces: ICache)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"
	time "time"

	redis "github.com/go-redis/redis/v8"
	gomock "github.com/golang/mock/gomock"
	cache "golang.52tt.com/services/channel-wedding-minigame/internal/model/chair-game/cache"
)

// MockICache is a mock of ICache interface.
type MockICache struct {
	ctrl     *gomock.Controller
	recorder *MockICacheMockRecorder
}

// MockICacheMockRecorder is the mock recorder for MockICache.
type MockICacheMockRecorder struct {
	mock *MockICache
}

// NewMockICache creates a new mock instance.
func NewMockICache(ctrl *gomock.Controller) *MockICache {
	mock := &MockICache{ctrl: ctrl}
	mock.recorder = &MockICacheMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockICache) EXPECT() *MockICacheMockRecorder {
	return m.recorder
}

// AddChairGameEnrollList mocks base method.
func (m *MockICache) AddChairGameEnrollList(arg0 context.Context, arg1, arg2, arg3 uint32, arg4 int64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddChairGameEnrollList", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddChairGameEnrollList indicates an expected call of AddChairGameEnrollList.
func (mr *MockICacheMockRecorder) AddChairGameEnrollList(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddChairGameEnrollList", reflect.TypeOf((*MockICache)(nil).AddChairGameEnrollList), arg0, arg1, arg2, arg3, arg4)
}

// AddChairGameGrabQueue mocks base method.
func (m *MockICache) AddChairGameGrabQueue(arg0 context.Context, arg1, arg2, arg3, arg4 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddChairGameGrabQueue", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddChairGameGrabQueue indicates an expected call of AddChairGameGrabQueue.
func (mr *MockICacheMockRecorder) AddChairGameGrabQueue(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddChairGameGrabQueue", reflect.TypeOf((*MockICache)(nil).AddChairGameGrabQueue), arg0, arg1, arg2, arg3, arg4)
}

// AddChairGameSettleQueue mocks base method.
func (m *MockICache) AddChairGameSettleQueue(arg0 context.Context, arg1 *cache.GameSettleInfo, arg2 int64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddChairGameSettleQueue", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddChairGameSettleQueue indicates an expected call of AddChairGameSettleQueue.
func (mr *MockICacheMockRecorder) AddChairGameSettleQueue(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddChairGameSettleQueue", reflect.TypeOf((*MockICache)(nil).AddChairGameSettleQueue), arg0, arg1, arg2)
}

// AddChairGameTimerQueue mocks base method.
func (m *MockICache) AddChairGameTimerQueue(arg0 context.Context, arg1 uint32, arg2 *cache.ChairGameRound, arg3 int64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddChairGameTimerQueue", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddChairGameTimerQueue indicates an expected call of AddChairGameTimerQueue.
func (mr *MockICacheMockRecorder) AddChairGameTimerQueue(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddChairGameTimerQueue", reflect.TypeOf((*MockICache)(nil).AddChairGameTimerQueue), arg0, arg1, arg2, arg3)
}

// AddToPlayerOffMicSet mocks base method.
func (m *MockICache) AddToPlayerOffMicSet(arg0 context.Context, arg1, arg2, arg3 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddToPlayerOffMicSet", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddToPlayerOffMicSet indicates an expected call of AddToPlayerOffMicSet.
func (mr *MockICacheMockRecorder) AddToPlayerOffMicSet(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddToPlayerOffMicSet", reflect.TypeOf((*MockICache)(nil).AddToPlayerOffMicSet), arg0, arg1, arg2, arg3)
}

// BatGetChairGameInfo mocks base method.
func (m *MockICache) BatGetChairGameInfo(arg0 context.Context, arg1 []uint32) (map[uint32]*cache.ChairGameInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatGetChairGameInfo", arg0, arg1)
	ret0, _ := ret[0].(map[uint32]*cache.ChairGameInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatGetChairGameInfo indicates an expected call of BatGetChairGameInfo.
func (mr *MockICacheMockRecorder) BatGetChairGameInfo(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatGetChairGameInfo", reflect.TypeOf((*MockICache)(nil).BatGetChairGameInfo), arg0, arg1)
}

// CheckIfNotInChairGameEnrollList mocks base method.
func (m *MockICache) CheckIfNotInChairGameEnrollList(arg0 context.Context, arg1, arg2 uint32, arg3 []uint32) ([]uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckIfNotInChairGameEnrollList", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].([]uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckIfNotInChairGameEnrollList indicates an expected call of CheckIfNotInChairGameEnrollList.
func (mr *MockICacheMockRecorder) CheckIfNotInChairGameEnrollList(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckIfNotInChairGameEnrollList", reflect.TypeOf((*MockICache)(nil).CheckIfNotInChairGameEnrollList), arg0, arg1, arg2, arg3)
}

// Close mocks base method.
func (m *MockICache) Close() error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Close")
	ret0, _ := ret[0].(error)
	return ret0
}

// Close indicates an expected call of Close.
func (mr *MockICacheMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockICache)(nil).Close))
}

// DelChairGameEnrollList mocks base method.
func (m *MockICache) DelChairGameEnrollList(arg0 context.Context, arg1, arg2 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelChairGameEnrollList", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// DelChairGameEnrollList indicates an expected call of DelChairGameEnrollList.
func (mr *MockICacheMockRecorder) DelChairGameEnrollList(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelChairGameEnrollList", reflect.TypeOf((*MockICache)(nil).DelChairGameEnrollList), arg0, arg1, arg2)
}

// DelChairGameSettleQueue mocks base method.
func (m *MockICache) DelChairGameSettleQueue(arg0 context.Context, arg1 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelChairGameSettleQueue", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// DelChairGameSettleQueue indicates an expected call of DelChairGameSettleQueue.
func (mr *MockICacheMockRecorder) DelChairGameSettleQueue(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelChairGameSettleQueue", reflect.TypeOf((*MockICache)(nil).DelChairGameSettleQueue), arg0, arg1)
}

// DelChairGameTimerQueue mocks base method.
func (m *MockICache) DelChairGameTimerQueue(arg0 context.Context, arg1 uint32, arg2 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelChairGameTimerQueue", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// DelChairGameTimerQueue indicates an expected call of DelChairGameTimerQueue.
func (mr *MockICacheMockRecorder) DelChairGameTimerQueue(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelChairGameTimerQueue", reflect.TypeOf((*MockICache)(nil).DelChairGameTimerQueue), arg0, arg1, arg2)
}

// DelUserFromEnrollList mocks base method.
func (m *MockICache) DelUserFromEnrollList(arg0 context.Context, arg1, arg2 uint32, arg3 []uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelUserFromEnrollList", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// DelUserFromEnrollList indicates an expected call of DelUserFromEnrollList.
func (mr *MockICacheMockRecorder) DelUserFromEnrollList(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelUserFromEnrollList", reflect.TypeOf((*MockICache)(nil).DelUserFromEnrollList), arg0, arg1, arg2, arg3)
}

// GetChairGameEnrollList mocks base method.
func (m *MockICache) GetChairGameEnrollList(arg0 context.Context, arg1, arg2 uint32) ([]uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChairGameEnrollList", arg0, arg1, arg2)
	ret0, _ := ret[0].([]uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChairGameEnrollList indicates an expected call of GetChairGameEnrollList.
func (mr *MockICacheMockRecorder) GetChairGameEnrollList(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChairGameEnrollList", reflect.TypeOf((*MockICache)(nil).GetChairGameEnrollList), arg0, arg1, arg2)
}

// GetChairGameGrabQueue mocks base method.
func (m *MockICache) GetChairGameGrabQueue(arg0 context.Context, arg1, arg2, arg3 uint32) ([]uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChairGameGrabQueue", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].([]uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChairGameGrabQueue indicates an expected call of GetChairGameGrabQueue.
func (mr *MockICacheMockRecorder) GetChairGameGrabQueue(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChairGameGrabQueue", reflect.TypeOf((*MockICache)(nil).GetChairGameGrabQueue), arg0, arg1, arg2, arg3)
}

// GetChairGameInfo mocks base method.
func (m *MockICache) GetChairGameInfo(arg0 context.Context, arg1 uint32) (*cache.ChairGameInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChairGameInfo", arg0, arg1)
	ret0, _ := ret[0].(*cache.ChairGameInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChairGameInfo indicates an expected call of GetChairGameInfo.
func (mr *MockICacheMockRecorder) GetChairGameInfo(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChairGameInfo", reflect.TypeOf((*MockICache)(nil).GetChairGameInfo), arg0, arg1)
}

// GetChairGameSettleQueueByScore mocks base method.
func (m *MockICache) GetChairGameSettleQueueByScore(arg0 context.Context, arg1 int64) ([]string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChairGameSettleQueueByScore", arg0, arg1)
	ret0, _ := ret[0].([]string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChairGameSettleQueueByScore indicates an expected call of GetChairGameSettleQueueByScore.
func (mr *MockICacheMockRecorder) GetChairGameSettleQueueByScore(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChairGameSettleQueueByScore", reflect.TypeOf((*MockICache)(nil).GetChairGameSettleQueueByScore), arg0, arg1)
}

// GetChairGameTimerQueueByScore mocks base method.
func (m *MockICache) GetChairGameTimerQueueByScore(arg0 context.Context, arg1 uint32, arg2 time.Time) ([]string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChairGameTimerQueueByScore", arg0, arg1, arg2)
	ret0, _ := ret[0].([]string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChairGameTimerQueueByScore indicates an expected call of GetChairGameTimerQueueByScore.
func (mr *MockICacheMockRecorder) GetChairGameTimerQueueByScore(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChairGameTimerQueueByScore", reflect.TypeOf((*MockICache)(nil).GetChairGameTimerQueueByScore), arg0, arg1, arg2)
}

// GetEnrollTotalCnt mocks base method.
func (m *MockICache) GetEnrollTotalCnt(arg0 context.Context, arg1, arg2 uint32) (uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetEnrollTotalCnt", arg0, arg1, arg2)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetEnrollTotalCnt indicates an expected call of GetEnrollTotalCnt.
func (mr *MockICacheMockRecorder) GetEnrollTotalCnt(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetEnrollTotalCnt", reflect.TypeOf((*MockICache)(nil).GetEnrollTotalCnt), arg0, arg1, arg2)
}

// GetPlayerOffMicSet mocks base method.
func (m *MockICache) GetPlayerOffMicSet(arg0 context.Context, arg1, arg2 uint32) (map[uint32]bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPlayerOffMicSet", arg0, arg1, arg2)
	ret0, _ := ret[0].(map[uint32]bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPlayerOffMicSet indicates an expected call of GetPlayerOffMicSet.
func (mr *MockICacheMockRecorder) GetPlayerOffMicSet(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPlayerOffMicSet", reflect.TypeOf((*MockICache)(nil).GetPlayerOffMicSet), arg0, arg1, arg2)
}

// GetRedisClient mocks base method.
func (m *MockICache) GetRedisClient() redis.Cmdable {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRedisClient")
	ret0, _ := ret[0].(redis.Cmdable)
	return ret0
}

// GetRedisClient indicates an expected call of GetRedisClient.
func (mr *MockICacheMockRecorder) GetRedisClient() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRedisClient", reflect.TypeOf((*MockICache)(nil).GetRedisClient))
}

// GetRedisServerTime mocks base method.
func (m *MockICache) GetRedisServerTime(arg0 context.Context) (time.Time, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRedisServerTime", arg0)
	ret0, _ := ret[0].(time.Time)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRedisServerTime indicates an expected call of GetRedisServerTime.
func (mr *MockICacheMockRecorder) GetRedisServerTime(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRedisServerTime", reflect.TypeOf((*MockICache)(nil).GetRedisServerTime), arg0)
}

// IsInPlayerOffMicSet mocks base method.
func (m *MockICache) IsInPlayerOffMicSet(arg0 context.Context, arg1, arg2, arg3 uint32) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IsInPlayerOffMicSet", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IsInPlayerOffMicSet indicates an expected call of IsInPlayerOffMicSet.
func (mr *MockICacheMockRecorder) IsInPlayerOffMicSet(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsInPlayerOffMicSet", reflect.TypeOf((*MockICache)(nil).IsInPlayerOffMicSet), arg0, arg1, arg2, arg3)
}

// LockTimer mocks base method.
func (m *MockICache) LockTimer(arg0 context.Context, arg1 string, arg2 time.Duration) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "LockTimer", arg0, arg1, arg2)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// LockTimer indicates an expected call of LockTimer.
func (mr *MockICacheMockRecorder) LockTimer(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "LockTimer", reflect.TypeOf((*MockICache)(nil).LockTimer), arg0, arg1, arg2)
}

// LockUpdateChairGameInfo mocks base method.
func (m *MockICache) LockUpdateChairGameInfo(arg0 context.Context, arg1 uint32, arg2 time.Duration) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "LockUpdateChairGameInfo", arg0, arg1, arg2)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// LockUpdateChairGameInfo indicates an expected call of LockUpdateChairGameInfo.
func (mr *MockICacheMockRecorder) LockUpdateChairGameInfo(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "LockUpdateChairGameInfo", reflect.TypeOf((*MockICache)(nil).LockUpdateChairGameInfo), arg0, arg1, arg2)
}

// SetChairGameInfo mocks base method.
func (m *MockICache) SetChairGameInfo(arg0 context.Context, arg1 *cache.ChairGameInfo) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetChairGameInfo", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetChairGameInfo indicates an expected call of SetChairGameInfo.
func (mr *MockICacheMockRecorder) SetChairGameInfo(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetChairGameInfo", reflect.TypeOf((*MockICache)(nil).SetChairGameInfo), arg0, arg1)
}

// UnlockTimer mocks base method.
func (m *MockICache) UnlockTimer(arg0 context.Context, arg1 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UnlockTimer", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// UnlockTimer indicates an expected call of UnlockTimer.
func (mr *MockICacheMockRecorder) UnlockTimer(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UnlockTimer", reflect.TypeOf((*MockICache)(nil).UnlockTimer), arg0, arg1)
}

// UnlockUpdateChairGameInfo mocks base method.
func (m *MockICache) UnlockUpdateChairGameInfo(arg0 context.Context, arg1 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UnlockUpdateChairGameInfo", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// UnlockUpdateChairGameInfo indicates an expected call of UnlockUpdateChairGameInfo.
func (mr *MockICacheMockRecorder) UnlockUpdateChairGameInfo(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UnlockUpdateChairGameInfo", reflect.TypeOf((*MockICache)(nil).UnlockUpdateChairGameInfo), arg0, arg1)
}
