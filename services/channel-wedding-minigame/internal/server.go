package internal

import (
    "context"
    "gitlab.ttyuyin.com/avengers/tyr/core/log"
    "golang.52tt.com/protocol/services/demo/echo"
    "google.golang.org/grpc/codes"
    pb "golang.52tt.com/protocol/services/channel-wedding-minigame"
    "golang.52tt.com/services/channel-wedding-minigame/internal/conf"
    anti_corruption_layer "golang.52tt.com/services/channel-wedding-minigame/internal/model/anti-corruption-layer"
    chair_game "golang.52tt.com/services/channel-wedding-minigame/internal/model/chair-game"
    redisConnect "gitlab.ttyuyin.com/avengers/tyr/core/middleware/redis/connect"
    mysqlConnect "gitlab.ttyuyin.com/avengers/tyr/core/middleware/mysql/connect"
    "time"
    protogrpc "golang.52tt.com/pkg/protocol/grpc"
    "golang.52tt.com/pkg/protocol"
    "golang.52tt.com/protocol/common/status"
    chair_game_award "golang.52tt.com/services/channel-wedding-minigame/internal/model/chair-game-award"
    UnifiedPayCallback "golang.52tt.com/protocol/services/unified_pay/cb"
    "golang.52tt.com/services/channel-wedding-minigame/internal/event"
    "fmt"
    logicPb "golang.52tt.com/protocol/app/channel_wedding_logic"
    reconcile_v2 "golang.52tt.com/protocol/services/reconcile-v2"
    "golang.52tt.com/pkg/bylink"
)

var (
    ReconcileMaxDuration = 3 * 24 * time.Hour // 对账最大可查询范围时长
    ErrTimeRangeOver     = protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "time range over")
    ErrParamInValid      = protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid)
)

type StartConfig struct {
}

func NewServer(ctx context.Context, cfg *conf.StartConfig) (*Server, error) {
    log.Infof("server startup with cfg: %+v", *cfg)

    redisClient, err := redisConnect.NewClient(ctx, cfg.RedisConfig)
    if err != nil {
        log.ErrorWithCtx(ctx, "NewServer fail to redisConnect.NewClient, %+v, err:%v", cfg.RedisConfig, err)
        return nil, err
    }

    mysqlDBCli, err := mysqlConnect.NewClient(ctx, cfg.MysqlConfig)
    if err != nil {
        log.ErrorWithCtx(ctx, "NewServer fail to mysqlConnect.NewClient, %+v, err:%v", cfg.MysqlConfig, err)
        return nil, err
    }
    readOnlyDBCli, err := mysqlConnect.NewClient(ctx, cfg.MysqlReadOnlyConfig)
    if err != nil {
        log.ErrorWithCtx(ctx, "NewServer fail to mysqlConnect.NewClient, %+v, err:%v", cfg.MysqlConfig, err)
        return nil, err
    }

    bc, err := conf.NewBusinessConfManager()
    if err != nil {
        return nil, err
    }

    // 百灵数据统计 初始化
    bylinkCollect, err := bylink.NewKfkCollector()
    if err != nil {
        log.Errorf("bylink.NewKfkCollector() failed err:%v", err)
        return nil, err
    }
    bylink.InitGlobalCollector(bylinkCollect)

    acl, err := anti_corruption_layer.NewMgr(bc)
    if err != nil {
        log.ErrorWithCtx(ctx, "NewServer fail to anti_corruption_layer.NewMgr, err:%v", err)
        return nil, err
    }
    chairGameAwardMgr, err := chair_game_award.NewMgr(mysqlDBCli, readOnlyDBCli, redisClient, bc, acl)
    if err != nil {
        log.ErrorWithCtx(ctx, "NewServer fail to chair_game_award.NewMgr, err:%v", err)
        return nil, err
    }

    chairGameMgr, err := chair_game.NewMgr(mysqlDBCli, readOnlyDBCli, redisClient, bc, acl, chairGameAwardMgr)

    kfkEvent, err := event.NewKafkaEvent(cfg, chairGameMgr, acl)
    if err != nil {
        log.ErrorWithCtx(ctx, "NewServer fail to event.NewChannelEvent, %+v, err:%v", cfg, err)
        return nil, err
    }

    s := &Server{
        bc:           bc,
        acLayerMgr:   acl,
        chairGameMgr: chairGameMgr,
        kfkEvent:     kfkEvent,

        chairGameAwardMgr: chairGameAwardMgr,
    }

    return s, nil
}

type Server struct {
    bc                conf.IBusinessConfManager
    acLayerMgr        anti_corruption_layer.IMgr
    chairGameMgr      chair_game.IMgr
    chairGameAwardMgr chair_game_award.IMgr

    kfkEvent event.IKafkaEvent
}

func (s *Server) Notify(c context.Context, notify *UnifiedPayCallback.PayNotify) (*UnifiedPayCallback.PayNotifyResponse, error) {
    out := &UnifiedPayCallback.PayNotifyResponse{}
    op := UnifiedPayCallback.Op_COMMIT
    // 根据orderId 获取订单信息
    OrderInfo, err := s.chairGameAwardMgr.GetOrderInfoByOrderId(c, notify.GetOutTradeNo())
    if err != nil {
        log.ErrorWithCtx(c, "Notify fail to GetOrderInfoByOrderId. orderId:%+v, err:%v", notify.GetOutTradeNo(), err)
        return out, err
    }

    if OrderInfo == nil {
        log.ErrorWithCtx(c, "Notify fail to GetOrderInfoByOrderId. orderId:%+v not exist", notify.GetOutTradeNo())
        return out, fmt.Errorf("%s not exist", notify.GetOutTradeNo())
    }

    var opCaseDesc string

    switch OrderInfo.Status {
    case conf.ConsumeOrderStatusInit, conf.ConsumeOrderStatusFreezing:
        rollbackTime := time.Now().Add(-time.Hour)
        if OrderInfo.Ctime.Before(rollbackTime) {
            opCaseDesc = "timeout rollback"
            op = UnifiedPayCallback.Op_ROLLBACK
            // 回滚订单
            err = s.chairGameAwardMgr.ConsumeRollBack(c, notify.GetOutTradeNo())
        } else {
            log.InfoWithCtx(c, "Notify 未达到rollback时间，不处理, orderId:%s", notify.GetOutTradeNo())
            return out, nil
        }

    case conf.ConsumeOrderStatusRollback:
        opCaseDesc = "rollback by order status"
        op = UnifiedPayCallback.Op_ROLLBACK
        // 回滚订单
        err = s.chairGameAwardMgr.ConsumeRollBack(c, notify.GetOutTradeNo())

    case conf.ConsumeOrderStatusAwarded, conf.ConsumeOrderStatusCommit:
        opCaseDesc = "commit by order status"
        _, _, err = s.chairGameAwardMgr.ConsumeCommit(c, notify.GetOutTradeNo())

    case conf.ConsumeOrderStatusInUse:
        // 查询游戏进程
        gameLog, err := s.chairGameMgr.GetGameLogByGameId(c, OrderInfo.GameId)
        if err != nil {
            log.ErrorWithCtx(c, "Notify fail to GetGameLogByGameId. orderId:%v, err:%v", notify.GetOutTradeNo(), err)
            return out, err
        }

        if gameLog == nil || gameLog.ResultStatus == conf.GameLogStatusFail || gameLog.ResultStatus == conf.GameLogStatusTimeOut {
            opCaseDesc = "rollback by game status"
            op = UnifiedPayCallback.Op_ROLLBACK
            //回滚订单
            err = s.chairGameAwardMgr.ConsumeRollBack(c, notify.GetOutTradeNo())

        } else if gameLog.ResultStatus == conf.GameLogStatusSuccess {
            opCaseDesc = "commit by game status"
            op = UnifiedPayCallback.Op_COMMIT
            //确认订单
            _, _, err = s.chairGameAwardMgr.ConsumeCommit(c, notify.GetOutTradeNo())

        } else {
            log.InfoWithCtx(c, "Notify game is going. orderId:%v 无需处理", notify.GetOutTradeNo())
            return out, nil
        }

    default:
        log.ErrorWithCtx(c, "Notify fail. orderId:%v OrderStatus unexpected, order:%+v", notify.GetOutTradeNo(), OrderInfo)
        return out, fmt.Errorf("wrong OrderStatus")
    }

    if err != nil {
        log.ErrorWithCtx(c, "Notify fail to handle. orderId:%v, err:%v", notify.GetOutTradeNo(), err)
        return out, err
    }

    out.Op = op
    out.Confirmed = true

    log.InfoWithCtx(c, "Notify success. orderId:%v, op:%d opCaseDesc:%s", notify.GetOutTradeNo(), op, opCaseDesc)
    return out, nil
}

func (s *Server) GetChairGameReward(c context.Context, request *pb.GetChairGameRewardRequest) (*pb.GetChairGameRewardResponse, error) {
    out := &pb.GetChairGameRewardResponse{}

    award, err := s.chairGameAwardMgr.GetCurRewardInfo(c, request.GetChannelId(), request.GetWeddingId())
    if err != nil {
        log.ErrorWithCtx(c, "GetChairGameReward GetCurRewardInfo failed, err:%v", err)
        return out, err
    }
    out.RewardSetting = &pb.ChairGameRewardSetting{
        GiftType:         s.bc.GetChairGameRewardSetting().GiftTypeList,
        SupportMagicGift: s.bc.GetChairGameRewardSetting().SupportMagicGift,
        PriceLimit:       s.bc.GetChairGameRewardSetting().PriceLimit,
    }

    if award == nil {
        return out, nil
    }

    log.DebugWithCtx(c, "GetChairGameReward GetCurRewardInfo success, award:%+v", award)

    rewardList := make([]*pb.ChairGameRewardInfo, 0)
    if award.PackId > 0 {
        // 包裹礼物
        awardList := s.bc.GetRewardInfoListByPackId(award.PackId)
        for _, award := range awardList {
            rewardList = append(rewardList, transformRewardInfo2PB(award))
        }
    } else {
        if len(award.GiftInfoList) == 0 {
            log.ErrorWithCtx(c, "GetChairGameReward GetCurRewardInfo failed, err:%v", err)
            return out, err
        }
        rewardList = append(rewardList, transformRewardInfo2PB(award.GiftInfoList[0]))
    }

    out.RewardList = rewardList
    out.SponsorUid = award.SponsorUid
    return out, nil
}

func transformRewardInfo2PB(award *conf.RewardInfo) *pb.ChairGameRewardInfo {
    return &pb.ChairGameRewardInfo{
        Icon:       award.BasePic,
        Name:       award.GiftName,
        Value:      award.Price,
        RewardUnit: conf.GetPriceUnitStr(award.PriceType),
        Amount:     award.Amount,
    }
}

func (s *Server) StartNewGamePreCheck(c context.Context, request *pb.StartChairGameRequest) (*pb.StartChairGameResponse, error) {
    out := &pb.StartChairGameResponse{}

    notApplyList, err := s.chairGameMgr.StartNewGamePreCheck(c, request.GetChannelId(), request.GetWeddingId(), request.GetUids())
    if err != nil {
        log.ErrorWithCtx(c, "StartNewGamePreCheck failed, err:%v", err)
        return out, err
    }

    if len(notApplyList) == 0 {
        out.Players = request.GetUids()
        return out, nil
    }

    notApplyMap := make(map[uint32]struct{})
    for _, v := range notApplyList {
        notApplyMap[v] = struct{}{}
    }

    playerUidList := make([]uint32, 0)
    for _, v := range request.GetUids() {
        if _, ok := notApplyMap[v]; !ok {
            playerUidList = append(playerUidList, v)
        }
    }

    out.Players = playerUidList
    return out, nil
}

func (s *Server) SetChairGameReward(c context.Context, request *pb.SetChairGameRewardRequest) (*pb.SetChairGameRewardResponse, error) {
    out := &pb.SetChairGameRewardResponse{}
    // 检查参数
    if request.GetReward() == nil {
        return out, conf.ToCommonErrWithMsg("未设置奖励")
    }

    if !request.GetReward().GetIsMagicGift() {
        var ok bool
        for _, giftType := range s.bc.GetChairGameRewardSetting().GiftTypeList {
            if request.GetReward().GetGiftType() == giftType {
                ok = true
                break
            }
        }

        if !ok {
            return out, conf.ToCommonErrWithMsg("不支持的礼物类型")
        }
    }

    err := s.chairGameAwardMgr.SetRewardInfo(c, request)
    if err != nil {
        log.ErrorWithCtx(c, "SetRewardInfo failed. err:%v", err)
        return out, err
    }

    return out, nil
}

func (s *Server) ShutDown() {

}

func (s *Server) Echo(ctx context.Context, req *echo.StringMessage) (*echo.StringMessage, error) {
    return req, nil
}

// GetChairGameApplyList 获取用户报名列表
func (s *Server) GetChairGameApplyList(ctx context.Context, req *pb.GetChairGameApplyListRequest) (*pb.GetChairGameApplyListResponse, error) {
    out := &pb.GetChairGameApplyListResponse{}

    applyList, err := s.chairGameMgr.GetEnrollList(ctx, req.GetChannelId(), req.GetWeddingId())
    if err != nil {
        log.Errorf("GetChairGameApplyList GetEnrollList err:%v", err)
        return out, err
    }

    out.ApplyUserList = applyList
    return out, nil
}

// ApplyToJoinChairGame 用户报名/取消报名
func (s *Server) ApplyToJoinChairGame(ctx context.Context, req *pb.ApplyToJoinChairGameRequest) (*pb.ApplyToJoinChairGameResponse, error) {
    out := &pb.ApplyToJoinChairGameResponse{}
    now := time.Now()
    var err error
    if req.GetChannelId() == 0 || req.GetUid() == 0 {
        log.ErrorWithCtx(ctx, "ApplyToJoinChairGame cid==0.opUid:%d channelId:%+v", req.GetUid(), req.GetChannelId())
        return out, ErrParamInValid
    }

    if !req.GetIsCancel() {
        // 获取当前奖励信息
        award, err := s.chairGameAwardMgr.GetCurRewardInfo(ctx, req.GetChannelId(), req.GetWeddingId())
        if err != nil {
            log.ErrorWithCtx(ctx, "ApplyToJoinChairGame GetCurRewardInfo req:%v err:%v", req, err)
            return out, err
        }
        if award == nil {
            log.ErrorWithCtx(ctx, "ApplyToJoinChairGame GetCurRewardInfo req:%v err:%v", req, err)
            return out, conf.ToCommonErrWithMsg("当前未设置游戏奖励")

        } else {
            if award.SponsorUid > 0 && award.SponsorUid == req.GetUid() {
                log.ErrorWithCtx(ctx, "ApplyToJoinChairGame req:%+v 不支持参与自己赞助的游戏哦~", req)
                return out, conf.ToCommonErrWithMsg("不支持参与自己赞助的游戏哦~")
            }
        }
    }

    if req.GetIsCancel() {
        err = s.chairGameMgr.UserCancelEnroll(ctx, req.GetChannelId(), req.GetUid(), req.GetWeddingId())
    } else {
        err = s.chairGameMgr.UserEnroll(ctx, req.GetChannelId(), req.GetUid(), req.GetWeddingId(), now.Unix())
    }
    if err != nil {
        log.ErrorWithCtx(ctx, "ApplyToJoinChairGame req:%+v err:%v", req, err)
        return out, err
    }

    totalCnt, err := s.chairGameMgr.GetEnrollTotalCnt(ctx, req.GetChannelId(), req.GetWeddingId())
    if err != nil {
        log.ErrorWithCtx(ctx, "ApplyToJoinChairGame GetEnrollTotalCnt err:%v", err)
        return out, err
    }

    GoroutineWithTimeoutCtx(ctx, 3*time.Second, func(ctx context.Context) {
        // 推送
        _ = s.acLayerMgr.ChairGameApplyMsgNotify(ctx, req.GetChannelId(), req.GetUid(), totalCnt, req.GetIsCancel())
    })

    log.InfoWithCtx(ctx, "ApplyToJoinChairGame req:%+v success", req)
    return out, nil
}

// GetChairGameInfo 用户获取抢椅子游戏信息
func (s *Server) GetChairGameInfo(ctx context.Context, req *pb.GetChairGameInfoRequest) (*pb.GetChairGameInfoResponse, error) {
    out := &pb.GetChairGameInfoResponse{}
    if req.GetChannelId() == 0 {
        log.WarnWithCtx(ctx, "GetChairGameInfo req:%+v cid is invalid", req)
        return out, nil
    }

    gameInfo, err := s.chairGameMgr.GetChannelChairGameInfo(ctx, req.GetChannelId())
    if err != nil {
        log.ErrorWithCtx(ctx, "GetChairGameInfo req:%+v err:%v", req, err)
        return out, err
    }

    if gameInfo == nil ||
        gameInfo.GetGameProgress().GetRoundStatus() == uint32(logicPb.ChairRoundStatus_CHAIR_ROUND_STATUS_GAME_OVER) {
        return out, nil
    }

    out.GameInfo = gameInfo
    return out, nil
}

// GrabChair 用户抢椅子
func (s *Server) GrabChair(ctx context.Context, req *pb.GrabChairRequest) (*pb.GrabChairResponse, error) {
    out := &pb.GrabChairResponse{}

    needPush, err := s.chairGameMgr.GrabChair(ctx, req.GetChannelId(), req.GetUid())
    if err != nil {
        log.ErrorWithCtx(ctx, "GrabChair req:%+v err:%v", req, err)
        return out, err
    }

    if needPush {
        GoroutineWithTimeoutCtx(ctx, 3*time.Second, func(ctx context.Context) {
            // 检查游戏进度
            _ = s.chairGameMgr.CheckRoundEndHandle(ctx, req.GetChannelId())
        })
    }

    return out, nil
}

// StartChairGame 主持人开启一句新游戏
func (s *Server) StartChairGame(ctx context.Context, req *pb.StartChairGameRequest) (*pb.StartChairGameResponse, error) {
    out := &pb.StartChairGameResponse{}

    gameInfo, err := s.chairGameMgr.StartNewGame(ctx, req.GetChannelId(), req.GetWeddingId(), req.GetPlayers())
    if err != nil {
        if err.Error() == chair_game.LockFailErr {
            log.WarnWithCtx(ctx, "StartChairGame req:%+v err:%v", req, err)
            return out, nil
        }
        log.ErrorWithCtx(ctx, "StartChairGame req:%+v err:%v", req, err)
        return out, err
    }

    // 将玩家的姿势设为正面
    uidList := make([]uint32, 0, len(req.GetPlayers()))
    for _, player := range req.GetPlayers() {
        uidList = append(uidList, player.GetUid())
    }

    _ = s.acLayerMgr.ResetPlayerDefaultPose(ctx, req.GetChannelId(), uidList) // 非关键错误

    GoroutineWithTimeoutCtx(ctx, 5*time.Second, func(ctx context.Context) {
        // 新增游戏计数
        _ = s.chairGameAwardMgr.IncrWeddingChairGameCnt(ctx, req.GetChannelId(), req.GetWeddingId())

        totalCnt, _ := s.chairGameMgr.GetEnrollTotalCnt(ctx, req.GetChannelId(), req.GetWeddingId())
        // 游戏信息变更推送
        _ = s.acLayerMgr.ChairGameInfoChannelNotify(ctx, req.GetChannelId(), gameInfo)

        // 推送
        _ = s.acLayerMgr.ChairGameApplyMsgNotify(ctx, req.GetChannelId(), req.GetUid(), totalCnt, true)
    })

    // 数据埋点上报
    GoroutineWithTimeoutCtx(ctx, 3*time.Second, func(ctx context.Context) {
        s.acLayerMgr.ReportStartChairGame(ctx, req.GetChannelId(), gameInfo.GetGameId(), req.GetWeddingId(), uidList)
    })

    return out, nil
}

// SetChairGameToNextRound 主持人点击进入下一轮
func (s *Server) SetChairGameToNextRound(ctx context.Context, req *pb.SetChairGameToNextRoundRequest) (*pb.SetChairGameToNextRoundResponse, error) {
    out := &pb.SetChairGameToNextRoundResponse{}

    gameInfo, err := s.chairGameMgr.StartNextRound(ctx, req.GetChannelId())
    if err != nil {
        if err.Error() == chair_game.LockFailErr {
            log.WarnWithCtx(ctx, "SetChairGameToNextRound req:%+v err:%v", req, err)
            return out, nil
        }

        if err.Error() == chair_game.SetNextRoundWrongStatus {
            // 发一个当前游戏信息推送
            _ = s.chairGameMgr.PushCurrGameInfo(ctx, req.GetChannelId())
            return out, nil
        }

        log.ErrorWithCtx(ctx, "SetChairGameToNextRound req:%+v err:%v", req, err)
        return out, err
    }

    GoroutineWithTimeoutCtx(ctx, 3*time.Second, func(ctx context.Context) {
        // 游戏信息变更推送
        _ = s.acLayerMgr.ChairGameInfoChannelNotify(ctx, req.GetChannelId(), gameInfo)
    })

    return out, nil
}

// StartGrabChair 主持人点击开始游戏
func (s *Server) StartGrabChair(ctx context.Context, req *pb.StartGrabChairRequest) (*pb.StartGrabChairResponse, error) {
    out := &pb.StartGrabChairResponse{}

    gameInfo, err := s.chairGameMgr.StartGrabNow(ctx, req.GetChannelId())
    if err != nil {
        if err.Error() == chair_game.LockFailErr {
            log.WarnWithCtx(ctx, "StartGrabChair req:%+v err:%v", req, err)
            return out, nil
        }
        log.ErrorWithCtx(ctx, "StartGrabChair req:%+v err:%v", req, err)
        return out, err
    }

    GoroutineWithTimeoutCtx(ctx, 3*time.Second, func(ctx context.Context) {
        // 游戏信息变更推送
        _ = s.acLayerMgr.ChairGameInfoChannelNotify(ctx, req.GetChannelId(), gameInfo)
    })

    return out, nil
}

func GoroutineWithTimeoutCtx(ctx context.Context, timeout time.Duration, fn func(ctx context.Context)) {
    timeoutCtx, cancel := protogrpc.NewContextWithInfoTimeout(ctx, timeout)
    go func() {
        defer cancel()
        fn(timeoutCtx)
    }()
}

// GetCurChairGamePlayers 获取房间内正在参与游戏的用户
func (s *Server) GetCurChairGamePlayers(ctx context.Context, req *pb.GetCurChairGamePlayersRequest) (*pb.GetCurChairGamePlayersResponse, error) {
    out := &pb.GetCurChairGamePlayersResponse{}
    // 获取房间内游戏信息
    gameInfo, err := s.chairGameMgr.GetChannelChairGameInfo(ctx, req.GetChannelId())
    if err != nil {
        log.ErrorWithCtx(ctx, "GetChairGamePlayers req:%+v err:%v", req, err)
        return out, err
    }

    if gameInfo == nil ||
        gameInfo.GetGameProgress().GetRoundStatus() == uint32(logicPb.ChairRoundStatus_CHAIR_ROUND_STATUS_GAME_OVER) {
        return out, nil
    }

    curPlayerMap := make(map[uint32]struct{})

    for _, v := range gameInfo.GetGameProgress().GetRoundPalyerUids() {
        curPlayerMap[v] = struct{}{}
    }

    for _, player := range gameInfo.GetPlayers() {
        if _, ok := curPlayerMap[player.GetUid()]; ok {
            out.Players = append(out.Players, player)
        }
    }

    return out, nil
}

func (s *Server) ForceCloseChairGame(c context.Context, request *pb.ForceCloseChairGameRequest) (*pb.ForceCloseChairGameResponse, error) {
    out := &pb.ForceCloseChairGameResponse{}
    log.InfoWithCtx(c, "ForceCloseChairGame req:%+v", request)

    return out, s.chairGameMgr.ForceEndGame(c, request.GetChannelId(), request.GetWeddingId())
}

func (s *Server) PushCurChairGameInfo(c context.Context, req *pb.PushCurChairGameInfoReq) (*pb.PushCurChairGameInfoResp, error) {
    log.InfoWithCtx(c, "PushCurChairGameInfo req:%+v", req)
    out := &pb.PushCurChairGameInfoResp{}
    return out, s.chairGameMgr.PushCurrGameInfo(c, req.GetChannelId())
}

// GetAwardPackageOrderIds 废弃
func (s *Server) GetAwardPackageOrderIds(c context.Context, req *reconcile_v2.TimeRangeReq) (*reconcile_v2.OrderIdsResp, error) {
    out := &reconcile_v2.OrderIdsResp{}
    return out, protocol.NewExactServerError(codes.OK, status.ErrGrpcUnimplemented)
}

// GetAwardPackageTotalCount 废弃
func (s *Server) GetAwardPackageTotalCount(c context.Context, req *reconcile_v2.TimeRangeReq) (*reconcile_v2.CountResp, error) {
    out := &reconcile_v2.CountResp{}
    return out, protocol.NewExactServerError(codes.OK, status.ErrGrpcUnimplemented)
}

func (s *Server) BatGetIfChannelInChairGame(c context.Context, req *pb.BatGetIfChannelInChairGameReq) (*pb.BatGetIfChannelInChairGameResp, error) {
    out := &pb.BatGetIfChannelInChairGameResp{}

    cidList, err := s.chairGameMgr.BatGetIfChannelInChairGame(c, req.GetChannelIds())
    if err != nil {
        log.ErrorWithCtx(c, "BatGetIfChannelInChairGame req:%+v err:%v", req, err)
        return out, err
    }

    out.GamingCidList = cidList
    return out, nil
}
