package mgr

import (
	"bytes"
	"context"
	"fmt"
	"gitlab.ttyuyin.com/gengo/bizplatform-ttinfra/tt/bizplatform/ttinfra/channelol_go"
	"golang.52tt.com/pkg/log"
	channelPB "golang.52tt.com/protocol/app/channel"
	pushPb "golang.52tt.com/protocol/app/push"
	"golang.52tt.com/services/channel-wedding-plan/internal/store"
	"text/template"
	"time"
)

type WeddingPopupPushContext struct {
	Plan         *store.WeddingPlan
	Uids         []uint32
	Template     string
	TemplateName string
	Data         interface{}
	Title        string
	Duration     int
	Scene        string
}

type WeddingImPushContext struct {
	FromUid      uint32
	ToUids       []uint32
	Template     string
	TemplateName string
	Data         interface{}
}

// 通用模板渲染
func (m *WeddingPlanMgr) renderWeddingTmpl(ctx context.Context, tplName, tpl string, data interface{}) (string, error) {
	var buf bytes.Buffer
	t := template.Must(template.New(tplName).Parse(tpl))
	err := t.Execute(&buf, data)
	if err != nil {
		log.ErrorWithCtx(ctx, "renderWeddingTmpl fail: %v", err)
		return "", err
	}
	return buf.String(), nil
}

// 弹窗推送
func (m *WeddingPlanMgr) pushWeddingPopup(ctx context.Context, popupCtx *WeddingPopupPushContext) error {
	content, err := m.renderWeddingTmpl(ctx, popupCtx.TemplateName, popupCtx.Template, popupCtx.Data)
	if err != nil {
		return err
	}
	opt := &pushPb.CommonTopRichTextDialogNotify{
		Content:       content,
		AnnounceScope: uint32(pushPb.CommBreakingNewsBaseOpt_OUTSIDE_CHANNEL + pushPb.CommBreakingNewsBaseOpt_INSIDE_CHANNEL),
		Duration:      uint32(popupCtx.Duration),
		Scene:         popupCtx.Scene,
	}
	notification := buildNotification(opt, uint32(pushPb.PushMessage_COMMON_TOP_RICH_TEXT_DIALOG_NOTIFY), popupCtx.Title)
	return m.rpc.PushCli.PushToUsers(ctx, popupCtx.Uids, notification)
}

// IM推送
func (m *WeddingPlanMgr) pushWeddingIm(ctx context.Context, imCtx *WeddingImPushContext) error {
	content, err := m.renderWeddingTmpl(ctx, imCtx.TemplateName, imCtx.Template, imCtx.Data)
	if err != nil {
		return err
	}
	return m.notifyWeddingStartIm(ctx, imCtx.FromUid, imCtx.ToUids, content)
}

// 组装模板数据的辅助函数（可根据实际业务扩展）
type NotifyUserInfo struct {
	Account    string
	HeadImgMd5 string
	Nickname   string
}
type NotifyGroomAndBrideData struct {
	Title            string
	UserInfo         NotifyUserInfo
	ChannelId        uint32
	TimeRangeStr     string
	TTTimerDuration  uint32
	TTTimerEventLink string
}
func buildGroomAndBrideData(plan *store.WeddingPlan, userInfo NotifyUserInfo, title string, duration uint32, eventLink string) NotifyGroomAndBrideData {
	return NotifyGroomAndBrideData{
		Title:           title,
		UserInfo:        userInfo,
		ChannelId:       plan.ReserveInfo.ChannelId,
		TimeRangeStr:    fmt.Sprintf("%s-%s", time.Unix(int64(plan.ReserveInfo.StartTime), 0).Format("15:04"), time.Unix(int64(plan.ReserveInfo.EndTime), 0).Format("15:04")),
		TTTimerDuration: duration,
		TTTimerEventLink: eventLink,
	}
}

// NotifyWeddingComing 新人婚礼准备开始定时推送
func (m *WeddingPlanMgr) NotifyWeddingComing(ctx context.Context) {
	ctx, cancel := context.WithTimeout(ctx, time.Minute)
	defer cancel()
	st := time.Now()
	defer func() {
		log.Debugf("NotifyWeddingComing cost: %vms", time.Since(st).Milliseconds())
	}()

	startTime := time.Now().Add(5 * time.Second).Truncate(time.Minute)
	aheadNotifyTime := m.bc.GetConfig().ReserveConf.AheadNotifyGroomAndBrideTime
	startTime = startTime.Add(time.Duration(aheadNotifyTime) * time.Minute)

	weddingPlanData, err := m.st.GetWeddingPlanByTimeRangeStatus(ctx, uint32(startTime.Unix()), uint32(startTime.Unix()), 100, []uint32{store.WeddingPlanStatusReserved})
	if err != nil {
		log.ErrorWithCtx(ctx, "NotifyWeddingComing fail to GetWeddingPlanByTimeRangeStatus. err:%v", err)
		return
	}
	if len(weddingPlanData) == 0 {
		return
	}
	for _, plan := range weddingPlanData {
		userprofile, err := m.rpc.BatchGetUserProfile(ctx, []uint32{plan.GroomUid, plan.BrideUid})
		if err != nil {
			log.ErrorWithCtx(ctx, "NotifyWeddingComing fail to BatchGetUserProfileV2. err:%v", err)
			continue
		}
		// 新郎弹窗
		groomData := buildGroomAndBrideData(plan, NotifyUserInfo{
			Account:    userprofile[plan.BrideUid].GetAccount(),
			HeadImgMd5: userprofile[plan.BrideUid].GetHeadImgMd5(),
			Nickname:   userprofile[plan.BrideUid].GetNickname(),
		}, "宝，我们的婚礼开始啦，等你来", 0, "")
		popupCtx := &WeddingPopupPushContext{
			Plan:         plan,
			Uids:         []uint32{plan.GroomUid},
			Template:     m.bc.GetGroomAndBridePopupXml(),
			TemplateName: "GroomComingPopup",
			Data:         groomData,
			Title:        "婚礼准备开始弹窗",
			Duration:     5,
			Scene:        "wedding-plan",
		}
		_ = m.pushWeddingPopup(ctx, popupCtx)
		// 新娘弹窗
		brideData := buildGroomAndBrideData(plan, NotifyUserInfo{
			Account:    userprofile[plan.GroomUid].GetAccount(),
			HeadImgMd5: userprofile[plan.GroomUid].GetHeadImgMd5(),
			Nickname:   userprofile[plan.GroomUid].GetNickname(),
		}, "宝，我们的婚礼开始啦，等你来", 0, "")
		popupCtx = &WeddingPopupPushContext{
			Plan:         plan,
			Uids:         []uint32{plan.BrideUid},
			Template:     m.bc.GetGroomAndBridePopupXml(),
			TemplateName: "BrideComingPopup",
			Data:         brideData,
			Title:        "婚礼准备开始弹窗",
			Duration:     5,
			Scene:        "wedding-plan",
		}
		_ = m.pushWeddingPopup(ctx, popupCtx)
		// 新郎IM
		imCtx := &WeddingImPushContext{
			FromUid:      plan.GroomUid,
			ToUids:       []uint32{plan.BrideUid},
			Template:     m.bc.GetGroomAndBrideImXml(),
			TemplateName: "GroomIm",
			Data:         groomData,
		}
		_ = m.pushWeddingIm(ctx, imCtx)
	}
}

// NotifyWeddingStarting 婚礼开始定时推送
func (m *WeddingPlanMgr) NotifyWeddingStarting(ctx context.Context) {
	ctx, cancel := context.WithTimeout(ctx, time.Minute)
	defer cancel()
	st := time.Now().Add(5 * time.Second).Truncate(time.Minute)
	hasBeenNotify := make(map[uint32]struct{})
	StartingNowWedding, err := m.st.GetWeddingPlanByTimeRangeStatus(ctx, uint32(st.Unix()), uint32(st.Unix()), 100, []uint32{store.WeddingPlanStatusReserved})
	if err != nil {
		log.ErrorWithCtx(ctx, "NotifyWeddingStarting fail to GetWeddingPlanByTimeRangeStatus. err:%v", err)
		return
	}
	if len(StartingNowWedding) == 0 {
		return
	}
	for _, plan := range StartingNowWedding {
		if plan == nil || plan.ReserveInfo == nil {
			log.ErrorWithCtx(ctx, "NotifyWeddingStarting weddingPlan is nil. plan:%+v", plan)
			continue
		}
		// 新人, 伴郎/伴娘自动进房弹窗
		m.notifyWeddingStartingAutoInChannel(ctx, plan, hasBeenNotify)

		// 预约观礼推送
		m.notifyWeddingStartingOther(ctx, plan)
	}
}

func (m *WeddingPlanMgr) notifyWeddingStartingAutoInChannel(ctx context.Context, plan *store.WeddingPlan, hasBeenNotify map[uint32]struct{}) {
	notifyUid := make([]uint32, 0, 2+len(plan.Groomsman)+len(plan.Bridesmaid))
	notifyUid = append(notifyUid, plan.GroomUid, plan.BrideUid)
	hasBeenNotify[plan.GroomUid] = struct{}{}
	hasBeenNotify[plan.BrideUid] = struct{}{}
	for _, v := range plan.Groomsman {
		if _, ok := hasBeenNotify[v.Uid]; ok {
			continue
		}
		notifyUid = append(notifyUid, v.Uid)
		hasBeenNotify[v.Uid] = struct{}{}
	}
	for _, v := range plan.Bridesmaid {
		if _, ok := hasBeenNotify[v.Uid]; ok {
			continue
		}
		notifyUid = append(notifyUid, v.Uid)
		hasBeenNotify[v.Uid] = struct{}{}
	}
	userChannelIdResp, err := m.rpc.ChannelOlGoCli.BatchGetUserChannelId(ctx, &channelol_go.BatchGetUserChannelIdReq{
		UidList: notifyUid,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "NotifyWeddingStarting fail to BatchGetUserChannelId. err:%v", err)
		return
	}
	actualNotifyUid := make([]uint32, 0, len(notifyUid))
	for _, v := range notifyUid {
		if userChannelIdResp.GetResults()[v] != plan.ReserveInfo.ChannelId {
			actualNotifyUid = append(actualNotifyUid, v)
		}
	}
	notifyUid = actualNotifyUid
	if len(notifyUid) == 0 {
		log.DebugWithCtx(ctx, "NotifyWeddingStarting no need to notify. planId:%d", plan.ID)
		return
	}

	userprofile, err := m.rpc.BatchGetUserProfile(ctx, []uint32{plan.GroomUid, plan.BrideUid})
	if err != nil {
		log.ErrorWithCtx(ctx, "notifyWeddingStartingPopup fail to BatchGetUserProfileV2. err:%v", err)
		return
	}

	for _, uid := range notifyUid {
		sendUid := plan.GroomUid
		title := "宝，我们的婚礼开始啦，等你来"
		if uid == plan.GroomUid {
			sendUid = plan.BrideUid
		}
		if uid != plan.GroomUid && uid != plan.BrideUid && plan.BuyerUid != 0 {
			sendUid = plan.BuyerUid
			title = "婚礼要开始啦快来参加吧~"
		}

		popupCtx := &WeddingPopupPushContext{
			Plan:         plan,
			Uids:         []uint32{uid},
			Template:     m.bc.GetWeddingStartedNotifyPopupTpl(), // 新人跟伴郎/伴娘的要自动进房
			TemplateName: "weddingStartingPopup",
			Data: buildGroomAndBrideData(
				plan,
				NotifyUserInfo{
					Account:    userprofile[sendUid].GetAccount(),
					HeadImgMd5: userprofile[sendUid].GetHeadImgMd5(),
					Nickname:   userprofile[sendUid].GetNickname(),
				},
				title,
				5,
				fmt.Sprintf("tt://m.52tt.com/channel?channel_id=%d&channel_enter_source=%d",
					plan.ReserveInfo.ChannelId, channelPB.ChannelEnterReq_ENUM_CHANNEL_ENTER_WEDDING_START_NOTIFY_IM)),
			Title:        "婚礼开始弹窗",
			Duration:     10,
			Scene:        "wedding-plan",
		}
		_ = m.pushWeddingPopup(ctx, popupCtx)
	}
}

func (m *WeddingPlanMgr) notifyWeddingStartingOther(ctx context.Context, plan *store.WeddingPlan) {
	ctx, cancel := context.WithTimeout(context.Background(), time.Minute)
	defer cancel()
	log.InfoWithCtx(ctx, "notifyFriendsWeddingStart, plan: %+v", *plan)
	planId := plan.ID

	subscribeUidList, err := m.st.GetWeddingSubscribeList(ctx, planId)
	if err != nil {
		log.ErrorWithCtx(ctx, "%d notifyFriendsWeddingStart GetWeddingSubscribeList err: %v", planId, err)
		return
	}
	log.DebugWithCtx(ctx, "%d notifyFriendsWeddingStart subscribeUidList: %v", planId, subscribeUidList)

	inviteMap := make(map[uint32][]uint32) // 不同人显示的邀请对象不一样
	uidSet := make(map[uint32]bool)        // 还得去重
	defaultUid := plan.BuyerUid            // 默认的邀请人
	if defaultUid == 0 {
		defaultUid = plan.GroomUid
	}

	// 发伴郎伴娘的, 仅发im
	gabList := append(plan.Groomsman, plan.Bridesmaid...)
	for _, guest := range gabList {
		if guest == nil {
			continue
		}
		uidSet[guest.Uid] = true
		if guest.InviteUid != 0 {
			inviteMap[guest.InviteUid] = append(inviteMap[guest.InviteUid], guest.Uid)
		} else {
			inviteMap[defaultUid] = append(inviteMap[defaultUid], guest.Uid)
		}
	}
	for uid, inviteList := range inviteMap {
		m.notifyWeddingStartByInviter(ctx, plan, uid, inviteList, false, subscribeUidList)
	}

	// 亲友团和订阅的, im和弹窗
	// 显示的是邀请人
	inviteMap = make(map[uint32][]uint32) // 清空发送人
	for _, guest := range plan.Friends {
		if guest == nil {
			continue
		}
		if uidSet[guest.Uid] {
			continue
		}
		uidSet[guest.Uid] = true

		if guest.InviteUid != 0 {
			inviteMap[guest.InviteUid] = append(inviteMap[guest.InviteUid], guest.Uid)
		} else {
			inviteMap[defaultUid] = append(inviteMap[defaultUid], guest.Uid)
		}
	}
	// 显示的是购买人
	for _, uid := range subscribeUidList {
		if uidSet[uid] {
			continue
		}
		uidSet[uid] = true
		inviteMap[defaultUid] = append(inviteMap[defaultUid], uid)
	}
	if plan.HostUid != 0 && !uidSet[plan.HostUid] {
		uidSet[plan.HostUid] = true
		inviteMap[defaultUid] = append(inviteMap[defaultUid], plan.HostUid)
	}
	for uid, inviteList := range inviteMap {
		m.notifyWeddingStartingByInviter(ctx, plan, uid, inviteList, true, subscribeUidList)
	}
}

func (m *WeddingPlanMgr) notifyWeddingStartingByInviter(ctx context.Context, plan *store.WeddingPlan, uid uint32, inviteList []uint32, sendPopup bool, subUidList []uint32) {
	if uid == 0 || len(inviteList) == 0 {
		return
	}
	planId := plan.ID
	log.DebugWithCtx(ctx, "%d notifyWeddingStartingByInviter, uid: %d, inviteList: %v", planId, uid, inviteList)
	log.InfoWithCtx(ctx, "%d notifyWeddingStartingByInviter, uid: %d, len(inviteList): %d", planId, uid, len(inviteList))

	userInfoResp, serr := m.rpc.BatchGetUserProfile(ctx, []uint32{uid})
	if serr != nil {
		log.ErrorWithCtx(ctx, "%d notifyWeddingStartingByInviter GetUserProfileV2 err: %v", planId, serr)
		return
	}
	userInfo := userInfoResp[uid]

	startTimeStr := time.Unix(int64(plan.ReserveInfo.StartTime), 0).Format("1月2日 15:04")
	endTimeStr := time.Unix(int64(plan.ReserveInfo.EndTime), 0).Format("15:04")

	tplData := &WeddingStartTplData{
		UserInfo:     userInfo,
		ChannelId:    plan.ReserveInfo.ChannelId,
		TimeRangeStr: fmt.Sprintf("%s-%s", startTimeStr, endTimeStr),
	}

	// 分批发送，因为接口有限制
	for start := 0; start < len(inviteList); start += notifyBatSize {
		end := start + notifyBatSize
		if end > len(inviteList) {
			end = len(inviteList)
		}
		curInviteList := inviteList[start:end]

		if sendPopup {
			popupCtx := &WeddingPopupPushContext{
				Plan:         plan,
				Uids:         curInviteList,
				Template:     m.bc.GetConfig().WeddingStartNotifyPopupTpl,
				TemplateName: "WeddingStartNotifyPopupTpl",
				Data:         tplData,
				Title:        "婚礼开始弹窗",
				Duration:     5,
				Scene:        "wedding-plan",
			}
			err := m.pushWeddingPopup(ctx, popupCtx)
			if err != nil {
				log.ErrorWithCtx(ctx, "%d notifyWeddingStartingByInviter notifyWeddingStartPopup err: %v", planId, err)
			}
		}

		subUidMap := make(map[uint32]bool)
		for _, subUid := range subUidList {
			subUidMap[subUid] = true
		}
		// curInviteList去掉subUidList
		finalInviteList := curInviteList
		if len(subUidList) > 0 {
			newInviteList := make([]uint32, 0, len(curInviteList))
			for _, inviteUid := range curInviteList {
				if !subUidMap[inviteUid] {
					newInviteList = append(newInviteList, inviteUid)
				}
			}
			log.DebugWithCtx(ctx, "%d notifyWeddingStartingByInviter, uid: %d, curInviteList: %v, newInviteList: %v", planId, uid, curInviteList, newInviteList)
			finalInviteList = newInviteList
		}

		if len(finalInviteList) > 0 {
			imCtx := &WeddingImPushContext{
				FromUid:      uid,
				ToUids:       finalInviteList,
				Template:     m.bc.GetConfig().WeddingStartNotifyImTpl,
				TemplateName: "WeddingStartNotifyImTpl",
				Data:         tplData,
			}
			err := m.pushWeddingIm(ctx, imCtx)
			if err != nil {
				log.ErrorWithCtx(ctx, "%d notifyWeddingStartingByInviter notifyWeddingStartIm err: %v", planId, err)
			}
		}
	}
}
