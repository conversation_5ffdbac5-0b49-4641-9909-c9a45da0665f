package store

import (
    "context"
    "errors"
    "fmt"
    "go.mongodb.org/mongo-driver/bson"
    "go.mongodb.org/mongo-driver/bson/primitive"
    "go.mongodb.org/mongo-driver/mongo"
    "go.mongodb.org/mongo-driver/mongo/options"
    "golang.52tt.com/pkg/log"
    "time"
)

const (
    WeddingBigScreenReviewMaxDuration = time.Hour
)

const (
    WeddingPlanStatusInit = iota
    WeddingPlanStatusReserved
    WeddingPlanStatusFinish
    WeddingPlanStatusCancel   // 取消中
    WeddingPlanStatusCanceled // 已取消

    ThemeTypeFree = 1
    ThemeTypePay  = 2
)

type WeddingPlan struct {
    ID            uint32           `bson:"_id"`
    GroomUid      uint32           `bson:"groom_uid"`       // 新郎
    BrideUid      uint32           `bson:"bride_uid"`       // 新娘
    ThemeId       uint32           `bson:"theme_id"`        // 主题id
    ThemeType     uint32           `bson:"theme_type"`      // 主题类型 1.免费 2.付费
    ReserveInfo   *ReserveInfo     `bson:"reserve_info"`    // 预约信息
    Groomsman     []*WeddingGuest  `bson:"groomsman"`       // 伴郎
    Bridesmaid    []*WeddingGuest  `bson:"bridesmaid"`      // 伴娘
    Friends       []*WeddingGuest  `bson:"friends"`         // 亲友
    BigScreenList []*BigScreenItem `bson:"big_screen_list"` // 大屏图片列表
    HostUid       uint32           `bson:"host_uid"`        // 主持人uid
    BuyerUid      uint32           `bson:"buyer_uid"`       // 购买人uid
    CreateTime    uint32           `bson:"create_time"`     // 创建时间
    UpdateTime    uint32           `bson:"update_time"`     // 更新时间
    Status        uint32           `bson:"status"`          // 状态 0-策划中, 1.进行中 2-已结束 3-已取消
    IsHot         bool             `bson:"is_hot"`          // 是否热门
    IsAdminCancel bool             `bson:"is_admin_cancel"` // 管理员取消
    IsChannelBuy  bool             `bson:"is_channel_buy"`  // 是否房间购买流程
}

type BigScreenItem struct {
    ImgUrl       string    `bson:"img_url"`
    ReviewStatus uint32    `bson:"review_status"` // 审核状态 0-审核中 1-通过 2-拒绝
    UpdateAt     time.Time `bson:"update_at"`
    UploadByUid  uint32    `bson:"upload_by_uid"`
}

type ReserveInfo struct {
    ChannelId   uint32 `bson:"channel_id"`   // 房间id
    StartTime   uint32 `bson:"start_time"`   // 开始时间
    EndTime     uint32 `bson:"end_time"`     // 结束时间
    ChangeTimes uint32 `bson:"change_times"` // 修改次数
}

type WeddingGuest struct {
    Uid        uint32 `bson:"uid"`         // 客人uid
    InviteUid  uint32 `bson:"invite_uid" ` // 邀请人uid
    CreateTime uint32 `bson:"create_time"` // 创建时间
}

func (s *Store) WeddingPlanEnsureIndex(ctx context.Context) error {
    _, err := s.weddingPlanColl.Collection.Indexes().CreateMany(ctx, []mongo.IndexModel{
        {
            Keys: bson.D{
                primitive.E{Key: "reserve_info.channel_id", Value: 1},
                primitive.E{Key: "reserve_info.start_time", Value: 1},
                primitive.E{Key: "reserve_info.end_time", Value: 1},
            },
        },
    })
    return err
}

func (s *Store) GenWeddingPlanId(ctx context.Context) uint32 {
    return s.IncCounter(ctx, "wedding_plan")
}

func (s *Store) InsertWeddingPlan(ctx context.Context, plan *WeddingPlan) error {
    if plan.ID == 0 {
        log.ErrorWithCtx(ctx, "WeddingPlanEnsureIndex failed, plan id is 0")
        return fmt.Errorf("wedding plan id is 0")
    }
    _, err := s.weddingPlanColl.Collection.InsertOne(ctx, plan)
    return err
}

func (s *Store) GetWeddingPlan(ctx context.Context, weddingPlanId uint32) (*WeddingPlan, error) {
    filter := bson.M{"_id": weddingPlanId}
    plan := &WeddingPlan{}
    err := s.weddingPlanColl.Collection.FindOne(ctx, filter).Decode(plan)
    return plan, err
}

func (s *Store) UpdateWeddingPlanReserveInfo(ctx context.Context, weddingPlanId uint32, reserveInfo *ReserveInfo) error {
    filter := bson.M{"_id": weddingPlanId}
    update := bson.M{"$set": bson.M{"reserve_info": reserveInfo}}
    _, err := s.weddingPlanColl.Collection.UpdateOne(ctx, filter, update)
    return err
}

func (s *Store) PushWeddingGroomsman(ctx context.Context, weddingPlanId uint32, groomsman *WeddingGuest) error {
    filter := bson.M{"_id": weddingPlanId}
    update := bson.M{"$push": bson.M{"groomsman": groomsman}}
    _, err := s.weddingPlanColl.Collection.UpdateOne(ctx, filter, update)
    return err
}

func (s *Store) UpdateWeddingGroomsman(ctx context.Context, weddingPlanId uint32, groomsman []*WeddingGuest) error {
    filter := bson.M{"_id": weddingPlanId}
    update := bson.M{"$set": bson.M{"groomsman": groomsman}}
    _, err := s.weddingPlanColl.Collection.UpdateOne(ctx, filter, update)
    return err
}

func (s *Store) PushWeddingBridesmaid(ctx context.Context, weddingPlanId uint32, bridesmaid *WeddingGuest) error {
    filter := bson.M{"_id": weddingPlanId}
    update := bson.M{"$push": bson.M{"bridesmaid": bridesmaid}}
    _, err := s.weddingPlanColl.Collection.UpdateOne(ctx, filter, update)
    return err
}

func (s *Store) UpdateWeddingBridesmaid(ctx context.Context, weddingPlanId uint32, bridesmaid []*WeddingGuest) error {
    filter := bson.M{"_id": weddingPlanId}
    update := bson.M{"$set": bson.M{"bridesmaid": bridesmaid}}
    _, err := s.weddingPlanColl.Collection.UpdateOne(ctx, filter, update)
    return err
}

func (s *Store) PushWeddingFriends(ctx context.Context, weddingPlanId uint32, friend *WeddingGuest) error {
    filter := bson.M{"_id": weddingPlanId}
    update := bson.M{"$push": bson.M{"friends": friend}}
    _, err := s.weddingPlanColl.Collection.UpdateOne(ctx, filter, update)
    return err
}

func (s *Store) UpdateWeddingFriends(ctx context.Context, weddingPlanId uint32, friends []*WeddingGuest) error {
    filter := bson.M{"_id": weddingPlanId}
    update := bson.M{"$set": bson.M{"friends": friends}}
    _, err := s.weddingPlanColl.Collection.UpdateOne(ctx, filter, update)
    return err
}

func (s *Store) GetWeddingPlanByChannelIdTimeRange(ctx context.Context, channelId, startTime, endTime uint32) ([]*WeddingPlan, error) {
    var rs []*WeddingPlan
    filter := bson.M{
        "reserve_info.channel_id": channelId,
        "$or": []bson.M{
            {
                "reserve_info.start_time": bson.M{"$gte": startTime, "$lt": endTime},
            },
            {
                "reserve_info.end_time": bson.M{"$gt": startTime, "$lte": endTime},
            },
            {
                "reserve_info.start_time": bson.M{"$lt": startTime},
                "reserve_info.end_time":   bson.M{"$gt": endTime},
            },
        },
        "status": bson.M{"$in": []uint32{WeddingPlanStatusInit, WeddingPlanStatusReserved}},
    }
    sort := bson.M{"reserve_info.start_time": 1}
    cursor, err := s.weddingPlanColl.Collection.Find(ctx, filter, options.Find().SetSort(sort))
    if err != nil {
        return rs, err
    }
    defer cursor.Close(ctx)

    for cursor.Next(ctx) {
        var plan WeddingPlan
        if err := cursor.Decode(&plan); err != nil {
            return rs, err
        }
        rs = append(rs, &plan)
    }
    return rs, err
}

func (s *Store) GetWeddingPlanByThemeTypeTimeRange(ctx context.Context, themeType uint32, startTime, endTime, channelId uint32) ([]*WeddingPlan, error) {
    var rs []*WeddingPlan
    filter := bson.M{
        "reserve_info.start_time": bson.M{"$gte": startTime},
        "reserve_info.end_time":   bson.M{"$lte": endTime},
        "status":                  bson.M{"$in": []uint32{WeddingPlanStatusInit, WeddingPlanStatusReserved, WeddingPlanStatusFinish}},
    }
    if channelId != 0 {
        filter["reserve_info.channel_id"] = channelId
    }
    if themeType != 0 {
        filter["theme_type"] = themeType
    }
    sort := bson.M{"reserve_info.start_time": 1}
    cursor, err := s.weddingPlanColl.Collection.Find(ctx, filter, options.Find().SetSort(sort))
    if err != nil {
        return nil, err
    }
    defer cursor.Close(ctx)

    for cursor.Next(ctx) {
        var plan WeddingPlan
        if err := cursor.Decode(&plan); err != nil {
            return rs, err
        }
        rs = append(rs, &plan)
    }
    return rs, err
}

func (s *Store) GetWeddingPlanById(ctx context.Context, weddingPlanId uint32) (*WeddingPlan, error) {
    filter := bson.M{"_id": weddingPlanId}
    plan := &WeddingPlan{}
    err := s.weddingPlanColl.Collection.FindOne(ctx, filter).Decode(plan)
    if err != nil {
        if errors.Is(err, mongo.ErrNoDocuments) {
            log.DebugWithCtx(ctx, "GetWeddingPlanById failed to FindOne, err:%v, weddingPlanId:%v", err, weddingPlanId)
            return nil, nil
        }
        log.ErrorWithCtx(ctx, "GetWeddingPlanById failed to FindOne, err:%v, weddingPlanId:%v", err, weddingPlanId)
        return nil, err
    }
    return plan, err
}

func (s *Store) UpdateWeddingReserveInfo(ctx context.Context, weddingPlanId uint32, reserveInfo *ReserveInfo) error {
    filter := bson.M{"_id": weddingPlanId}
    update := bson.M{"$set": bson.M{"reserve_info": reserveInfo}}
    _, err := s.weddingPlanColl.Collection.UpdateOne(ctx, filter, update)
    return err
}

func (s *Store) AddWeddingBigScreen(ctx context.Context, weddingPlanId uint32, item *BigScreenItem) error {
    filter := bson.M{"_id": weddingPlanId}
    update := bson.M{"$push": bson.M{"big_screen_list": item}}
    _, err := s.weddingPlanColl.Collection.UpdateOne(ctx, filter, update)
    return err
}

func (s *Store) DelWeddingBigScreen(ctx context.Context, weddingPlanId uint32, imgUrl string) error {
    filter := bson.M{"_id": weddingPlanId}
    update := bson.M{"$pull": bson.M{"big_screen_list": bson.M{"img_url": imgUrl}}}
    _, err := s.weddingPlanColl.Collection.UpdateOne(ctx, filter, update)
    return err
}

func (s *Store) UpdateWeddingBigScreenReviewStatus(ctx context.Context, weddingPlanId, reviewStatus uint32, imgUrl string) error {
    filter := bson.M{"_id": weddingPlanId}
    update := bson.M{"$set": bson.M{
        "big_screen_list.$[item].review_status": reviewStatus,
        "big_screen_list.$[item].update_at":     time.Now(),
    }}
    opt := &options.UpdateOptions{
        ArrayFilters: &options.ArrayFilters{
            Filters: []interface{}{
                bson.M{"item.img_url": imgUrl},
            },
        }}
    _, err := s.weddingPlanColl.Collection.UpdateOne(ctx, filter, update, opt)
    return err
}

func (s *Store) PageWeddingPlanByChannelIdReserveTime(ctx context.Context, channelIds []uint32, page, pageSize, sortType int, startTime, endTime int64) ([]*WeddingPlan, error) {
    var rs []*WeddingPlan
    filter := bson.M{
        "reserve_info.channel_id": bson.M{"$in": channelIds},
        "$or": []bson.M{
            {"status": bson.M{"$in": []uint32{WeddingPlanStatusInit, WeddingPlanStatusReserved, WeddingPlanStatusFinish}}},
            {"status": bson.M{"$in": []uint32{WeddingPlanStatusCancel, WeddingPlanStatusCanceled}}, "is_admin_cancel": true}, // 已取消且是管理员取消的
        },
    }
    filter["reserve_info.start_time"] = bson.M{"$gte": startTime, "$lte": endTime}
    sort := bson.M{"reserve_info.start_time": 1}
    if sortType == 1 {
        sort = bson.M{"reserve_info.start_time": -1}
    }
    cursor, err := s.weddingPlanColl.Collection.Find(ctx, filter, options.Find().SetSort(sort).SetSkip(int64(page*pageSize)).SetLimit(int64(pageSize)))
    if err != nil {
        return nil, err
    }
    defer cursor.Close(ctx)

    for cursor.Next(ctx) {
        var plan WeddingPlan
        if err := cursor.Decode(&plan); err != nil {
            return rs, err
        }
        rs = append(rs, &plan)
    }
    return rs, err
}

func (s *Store) CountWeddingPlanByChannelIdReserveTime(ctx context.Context, channelIds []uint32, startTime, endTime int64) (int64, error) {
    filter := bson.M{
        "reserve_info.channel_id": bson.M{"$in": channelIds},
        "$or": []bson.M{
            {"status": bson.M{"$in": []uint32{WeddingPlanStatusInit, WeddingPlanStatusReserved, WeddingPlanStatusFinish}}},
            {"status": bson.M{"$in": []uint32{WeddingPlanStatusCancel, WeddingPlanStatusCanceled}}, "is_admin_cancel": true}, // 已取消且是管理员取消的
        },
    }
    filter["reserve_info.start_time"] = bson.M{"$gte": startTime, "$lte": endTime}
    return s.weddingPlanColl.Collection.CountDocuments(ctx, filter)
}

func (s *Store) UpdateWeddingPlanHost(ctx context.Context, weddingPlanId uint32, hostUid uint32) error {
    filter := bson.M{"_id": weddingPlanId}
    update := bson.M{"$set": bson.M{"host_uid": hostUid}}
    _, err := s.weddingPlanColl.Collection.UpdateOne(ctx, filter, update)
    return err
}

func (s *Store) PageGetWeddingList(ctx context.Context, limit, offset, startTs int64) ([]*WeddingPlan, error) {
    filter := bson.M{"reserve_info.start_time": bson.M{"$gt": startTs}, "status": bson.M{"$in": []uint32{WeddingPlanStatusInit, WeddingPlanStatusReserved}}}
    opts := options.Find().SetLimit(limit).SetSkip(offset).SetSort(bson.M{"_id": 1})
    cursor, err := s.weddingPlanColl.Collection.Find(ctx, filter, opts)
    if err != nil {
        return nil, err
    }
    defer cursor.Close(ctx)

    var plans []*WeddingPlan
    for cursor.Next(ctx) {
        var plan WeddingPlan
        if err := cursor.Decode(&plan); err != nil {
            return nil, err
        }
        plans = append(plans, &plan)
    }
    return plans, err
}

func (s *Store) GetTodayAllComingWeddingList(ctx context.Context) ([]*WeddingPlan, error) {
    now := time.Now()
    endOfDay := time.Date(now.Year(), now.Month(), now.Day()+1, 0, 0, 0, 0, now.Location())
    filter := bson.M{
        "reserve_info.start_time": bson.M{"$gte": uint32(now.Unix()), "$lt": uint32(endOfDay.Unix())},
        "status":                  bson.M{"$in": []uint32{WeddingPlanStatusInit, WeddingPlanStatusReserved}},
    }

    cursor, err := s.weddingPlanColl.Collection.Find(ctx, filter)
    if err != nil {
        return nil, err
    }
    defer cursor.Close(ctx)

    var plans []*WeddingPlan
    for cursor.Next(ctx) {
        var plan WeddingPlan
        if err := cursor.Decode(&plan); err != nil {
            return nil, err
        }
        plans = append(plans, &plan)
    }
    return plans, err
}

func (s *Store) BatGetWeddingPlan(ctx context.Context, idList []uint32) ([]*WeddingPlan, error) {
    var rs []*WeddingPlan
    if len(idList) == 0 {
        return nil, nil
    }
    filter := bson.M{"_id": bson.M{"$in": idList}}
    cursor, err := s.weddingPlanColl.Collection.Find(ctx, filter)
    if err != nil {
        return rs, err
    }
    defer cursor.Close(ctx)

    for cursor.Next(ctx) {
        var plan WeddingPlan
        if err := cursor.Decode(&plan); err != nil {
            return nil, err
        }
        rs = append(rs, &plan)
    }
    return rs, err
}

func (s *Store) GetWeddingPlanByTimeRangeStatus(ctx context.Context, st, et, limit uint32, status []uint32) ([]*WeddingPlan, error) {
    var rs []*WeddingPlan
    filter := bson.M{"reserve_info.start_time": bson.M{"$gte": st, "$lte": et},
        "status": bson.M{"$in": status}}
    cur, err := s.weddingPlanColl.Collection.Find(ctx, filter, options.Find().SetLimit(int64(limit)), options.Find().SetSort(bson.M{"_id": 1}))
    if err != nil {
        return rs, err
    }
    defer cur.Close(ctx)

    for cur.Next(ctx) {
        var plan WeddingPlan
        if err := cur.Decode(&plan); err != nil {
            return rs, err
        }
        rs = append(rs, &plan)
    }
    return rs, nil
}

func (s *Store) BatchUpdateWeddingPlanStatus(ctx context.Context, idList []uint32, status uint32) error {
    filter := bson.M{"_id": bson.M{"$in": idList}}
    update := bson.M{"$set": bson.M{"status": status}}
    _, err := s.weddingPlanColl.Collection.UpdateMany(ctx, filter, update)
    if err != nil {
        log.ErrorWithCtx(ctx, "BatchUpdateWeddingPlanStatus, id_list:%v, err:%v, status:%d", idList, err, status)
        return err
    }
    log.InfoWithCtx(ctx, "BatchUpdateWeddingPlanStatus, id_list:%v, status:%d", idList, status)
    return err
}

func (s *Store) GetNoChannelFreeWeddingPlanByTimeRange(ctx context.Context, st, et uint32, limit int64) ([]*WeddingPlan, error) {
    var rs []*WeddingPlan
    filter := bson.M{"reserve_info.start_time": bson.M{"$gte": st, "$lte": et},
        "theme_type":              ThemeTypeFree,
        "reserve_info.channel_id": 0}
    cur, err := s.weddingPlanColl.Collection.Find(ctx, filter, options.Find().SetLimit(limit))
    if err != nil {
        return rs, err
    }
    defer cur.Close(ctx)

    for cur.Next(ctx) {
        var plan WeddingPlan
        if err := cur.Decode(&plan); err != nil {
            return rs, err
        }
        rs = append(rs, &plan)
    }
    return rs, err
}

func (s *Store) GetAllStartupWeddingPlan(ctx context.Context, startTime int64) ([]*WeddingPlan, error) {
    var plans []*WeddingPlan
    filter := bson.M{"reserve_info.start_time": startTime, "status": WeddingPlanStatusReserved}
    cur, err := s.weddingPlanColl.Collection.Find(ctx, filter)
    if err != nil {
        return plans, err
    }
    defer cur.Close(ctx)

    for cur.Next(ctx) {
        var plan WeddingPlan
        if err := cur.Decode(&plan); err != nil {
            return plans, err
        }
        plans = append(plans, &plan)
    }
    return plans, err
}

func (s *Store) GetGoingPlan(ctx context.Context, uid uint32) (*WeddingPlan, error) {
    filter := bson.M{
        "status": bson.M{"$in": []uint32{WeddingPlanStatusInit, WeddingPlanStatusReserved}},
        "$or": []bson.M{
            {"groom_uid": uid},
            {"bride_uid": uid},
        },
    }
    var plan WeddingPlan
    err := s.weddingPlanColl.Collection.FindOne(ctx, filter).Decode(&plan)
    if err != nil {
        if errors.Is(err, mongo.ErrNoDocuments) {
            log.DebugWithCtx(ctx, "GetGoingPlan no documents")
            return nil, nil
        }
        log.ErrorWithCtx(ctx, "GetGoingPlan failed to FindOne, err:%v", err)
        return nil, err
    }
    log.DebugWithCtx(ctx, "GetGoingPlan plan:%+v, uid:%v", plan, uid)
    return &plan, err
}

func (s *Store) UpdateWeddingPlanStatusStrict(ctx context.Context, weddingPlanId, oldStatus, newStatus uint32) error {
    filter := bson.M{"_id": weddingPlanId, "status": oldStatus}
    update := bson.M{"$set": bson.M{"status": newStatus}}
    result, err := s.weddingPlanColl.Collection.UpdateOne(ctx, filter, update)
    if err != nil {
        return err
    }
    if result.ModifiedCount == 0 {
        return fmt.Errorf("update wedding plan status failed, weddingPlanId:%v, oldStatus:%v, newStatus:%v", weddingPlanId, oldStatus, newStatus)
    }
    return nil
}

func (s *Store) BatchClearReserveInfo(ctx context.Context, planIdList []uint32) error {
    filter := bson.M{"_id": bson.M{"$in": planIdList}}
    update := bson.M{"$set": bson.M{"reserve_info": nil}}
    _, err := s.weddingPlanColl.Collection.UpdateMany(ctx, filter, update)
    return err
}

func (s *Store) GetWeddingPlanByEndTimeTimeRangeAndStatus(ctx context.Context, startTime, endTime, limit uint32, status []uint32) ([]*WeddingPlan, error) {
    var rs []*WeddingPlan
    filter := bson.M{"reserve_info.end_time": bson.M{"$gte": startTime, "$lte": endTime},
        "status": bson.M{"$in": status}}
    cur, err := s.weddingPlanColl.Collection.Find(ctx, filter, options.Find().SetLimit(int64(limit)))
    if err != nil {
        return rs, err
    }
    defer cur.Close(ctx)

    for cur.Next(ctx) {
        var plan WeddingPlan
        if err := cur.Decode(&plan); err != nil {
            return rs, err
        }
        rs = append(rs, &plan)
    }
    return rs, nil
}

func (s *Store) MarkAdminCancel(ctx context.Context, planId uint32) error {
    filter := bson.M{"_id": planId}
    update := bson.M{"$set": bson.M{"is_admin_cancel": true}}
    err := s.weddingPlanColl.Collection.FindOneAndUpdate(ctx, filter, update).Err()
    if err != nil {
        if errors.Is(err, mongo.ErrNoDocuments) {
            log.DebugWithCtx(ctx, "MarkAdminCancel no documents")
            return nil
        }
        log.ErrorWithCtx(ctx, "MarkAdminCancel failed to FindOneAndUpdate, err:%v", err)
        return err
    }
    return nil
}
