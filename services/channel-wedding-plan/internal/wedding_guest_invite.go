package internal

import (
    "context"
    "golang.52tt.com/pkg/log"
    "golang.52tt.com/pkg/protocol"
    grpcProtocol "golang.52tt.com/pkg/protocol/grpc"
    "golang.52tt.com/protocol/common/status"
    pb "golang.52tt.com/protocol/services/channel-wedding-plan"
    "golang.52tt.com/services/channel-wedding-plan/internal/store"
    "google.golang.org/grpc/codes"
    "sort"
)


var (
    CommonServerErr = protocol.NewExactServerError(codes.OK, status.ErrChannelWeddingPlanCommonError, "参数错误")
)

func (s *Server) GetGroomsmanAndBridesmaidInfo(ctx context.Context, request *pb.GetGroomsmanAndBridesmaidInfoRequest) (*pb.GetGroomsmanAndBridesmaidInfoResponse, error) {
    resp := &pb.GetGroomsmanAndBridesmaidInfoResponse{}
    if request.WeddingPlanId == 0 {
        return resp, nil
    }
    groomsman, briedsmaid, err := s.mgr.GetGroomsmanAndBridesmaid(ctx, request.WeddingPlanId)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetGroomsmanAndBridesmaidInfo fail to GetGroomsmanAndBridesmaid. err:%v", err)
        return resp, err
    }
    resp.GroomsmanList = weddingGuest2VO(groomsman)
    resp.BridesmaidList = weddingGuest2VO(briedsmaid)

    invite, accept, reject, err := s.mgr.GetWeddingInvite(ctx, request.WeddingPlanId, []uint32{store.GuestTypeGroomsman, store.GuestTypeBridesmaid})
    if err != nil {
        log.ErrorWithCtx(ctx, "GetGroomsmanAndBridesmaidInfo fail to GetWeddingInvite. err:%v", err)
        return resp, err
    }
    resp.AgreedList = weddingGuestInvite2VO(accept)
    resp.InvitedList = weddingGuestInvite2VO(invite)
    resp.RefusedList = weddingGuestInvite2VO(reject)

    return resp, nil
}

func weddingGuest2VO(guests []*store.WeddingGuest) []*pb.WeddingGuestInfo {
    var result []*pb.WeddingGuestInfo
    sort.Slice(guests, func(i, j int) bool {
        return guests[i].CreateTime > guests[j].CreateTime
    })

    for _, item := range guests {
        result = append(result, &pb.WeddingGuestInfo{
            Uid:        item.Uid,
            InviteUid:  item.InviteUid,
            CreateTime: item.CreateTime,
        })
    }
    return result
}

func weddingGuestInvite2VO(invites []*store.WeddingGuestInvite) []*pb.WeddingGuestInfo {
    var result []*pb.WeddingGuestInfo
    for _, item := range invites {
        result = append(result, &pb.WeddingGuestInfo{
            Uid:         item.Uid,
            InviteUid:   item.InviteUid,
        })
    }
    return result
}

func (s *Server) GetWeddingFriendInfo(ctx context.Context, request *pb.GetWeddingFriendInfoRequest) (*pb.GetWeddingFriendInfoResponse, error) {
    resp := &pb.GetWeddingFriendInfoResponse{}
    if request.WeddingPlanId == 0 {
        return resp, nil
    }

    friends, err := s.mgr.GetWeddingFriends(ctx, request.WeddingPlanId)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetWeddingFriendInfo fail to GetWeddingFriends. err:%v", err)
        return resp, err
    }
    resp.FriendList = weddingGuest2VO(friends)

    invite, accept, reject, err := s.mgr.GetWeddingInvite(ctx, request.WeddingPlanId, []uint32{store.GuestTypeFriend})
    if err != nil {
        log.ErrorWithCtx(ctx, "GetWeddingFriendInfo fail to GetWeddingInvite. err:%v", err)
        return resp, err
    }
    resp.AgreedList = weddingGuestInvite2VO(accept)
    resp.InvitedList = weddingGuestInvite2VO(invite)
    resp.RefusedList = weddingGuestInvite2VO(reject)
    resp.MaxFriendNum = uint32(s.bc.GetConfig().GuestInviteConf.MaxWeddingFriendNum)
    return resp, nil
}

func (s *Server) InviteWeddingGuest(ctx context.Context, req *pb.InviteWeddingGuestRequest) (*pb.InviteWeddingGuestResponse, error) {
    resp := &pb.InviteWeddingGuestResponse{}
    serviceInfo, ok := grpcProtocol.ServiceInfoFromContext(ctx)
    if !ok {
        log.ErrorWithCtx(ctx, "InviteWeddingGuest fail to get service info")
        return resp, nil
    }

    err := s.mgr.InviteWeddingGuest(ctx, req.WeddingPlanId, req.GetWeddingGuestType(), req.Uid, serviceInfo.UserID)
    if err != nil {
        log.ErrorWithCtx(ctx, "InviteWeddingGuest fail to InviteWeddingGuest. req: %+v, err:%v", req, err)
        return resp, err
    }

    return resp, nil
}

func (s *Server) DelWeddingGuest(ctx context.Context, request *pb.DelWeddingGuestRequest) (*pb.DelWeddingGuestResponse, error) {
    resp := &pb.DelWeddingGuestResponse{}
    err := s.mgr.DelWeddingGuest(ctx, request.WeddingPlanId, request.GetWeddingGuestType(), request.Uid)
    if err != nil {
        log.ErrorWithCtx(ctx, "DelWeddingGuest fail to DelWeddingGuest. req: %+v, err:%v", request, err)
        return resp, err
    }
    return resp, nil
}

func (s *Server) HandleWeddingInvite(ctx context.Context, req *pb.HandleWeddingInviteRequest) (*pb.HandleWeddingInviteResponse, error) {
    resp := &pb.HandleWeddingInviteResponse{}
    err := s.mgr.HandleWeddingInvite(ctx, req.InviteId, req.GetUid(), req.GetHandleStatus())
    if err != nil {
        log.ErrorWithCtx(ctx, "HandleWeddingInvite fail to HandleWeddingInvite. req: %+v, err:%v", req, err)
        return resp, err
    }
    return resp, nil
}