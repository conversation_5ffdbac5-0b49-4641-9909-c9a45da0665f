package manager

import (
	"context"
	"golang.52tt.com/pkg/deal_token"
	"math"
	"time"

	"golang.52tt.com/services/esport-score/internal/common"

	"golang.52tt.com/pkg/protocol"
	errStatus "golang.52tt.com/protocol/common/status"

	pb "golang.52tt.com/protocol/services/esport_score"

	"gitlab.ttyuyin.com/tt-infra/tyr/log"
)

type EsportUserScoreDetail struct {
	ID         uint64    `db:"id"`          //  自增id
	OrderId    string    `db:"order_id"`    //  订单id
	Uid        uint32    `db:"uid"`         //  uid
	GuildId    uint32    `db:"guild_id"`    //  若签约公会，则展示公会id，若未签约公会，则展示0
	TotalPrice uint32    `db:"total_price"` //  订单金额
	Score      int64     `db:"score"`       //  用户电竞积分
	FinalScore int64     `db:"final_score"` //  变化后用户电竞积分
	ReasonType uint32    `db:"reason_type"` //  变更原因统计枚举
	Reason     string    `db:"reason"`      //  变更原因
	CreateTime time.Time `db:"create_time"` //  记录订单创建时间
	ServerTime time.Time `db:"server_time"` //  记录创建服务器时间
}

// createScoreDetailTable 建立积分变更明细表
func (m *manager) createScoreDetailTable(ctx context.Context, now time.Time) {
	thisYear := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, now.Location())
	nextYear := time.Date(now.Year()+1, now.Month(), 1, 0, 0, 0, 0, now.Location())
	// 创建当月表
	if err := m.store.CreateScoreDetailTable(ctx, thisYear); err != nil {
		log.ErrorWithCtx(ctx, "CreateScoreDetailTable this year fail , err :%v", err)
	}
	// 创建下月表
	if err := m.store.CreateScoreDetailTable(ctx, nextYear); err != nil {
		log.ErrorWithCtx(ctx, "CreateScoreDetailTable next year fail , err :%v", err)
	}
}

// CalculateEsportScore 计算用户电竞积分
// 积分 = 向下取整（订单金额 * 积分比例）
// 积分比例为动态配置，根据订单创建时间获取
func (m *manager) CalculateEsportScore(payPrice uint32, orderTime time.Time, isGuildTrade bool) int64 {
	rate := m.config.GetEsportScoreRate(orderTime, isGuildTrade)
	log.DebugWithCtx(context.Background(), "CalculateEsportScore rate:[%+v]", rate)
	return int64(math.Floor(float64(payPrice) * rate))
}

// GetEsportScoreDetail 获取用户电竞积分变更明细
func (m *manager) GetEsportScoreDetail(ctx context.Context, startTime, endTime time.Time, uid uint32, reasonTypeList []uint32, offset, limit uint32) (
	[]*pb.EsportScoreDetail, error) {
	// 获取积分变更明细
	details, err := m.store.GetUserScoreDetail(ctx, startTime, endTime, uid, reasonTypeList, offset, limit)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetUserScoreDetail failed, err:[%+v]", err)
		return nil, err
	}

	return m.convertDetailDB2PB(details), nil
}

// convertDetailDB2PB 将数据库中的积分变更明细转换为pb
func (m *manager) convertDetailDB2PB(details []*EsportUserScoreDetail) []*pb.EsportScoreDetail {
	var pbDetails []*pb.EsportScoreDetail
	for _, detail := range details {
		pbDetails = append(pbDetails, &pb.EsportScoreDetail{
			ChangeScore:  detail.Score,
			ReasonType:   pb.ReasonType(detail.ReasonType),
			ReasonDetail: detail.Reason,
			OrderId:      detail.OrderId,
			CreateTime:   detail.CreateTime.Unix(),
		})
	}
	return pbDetails
}

// -------------- 对账接口 ---------------------

// GetOrderInfoByPeriod 获取某个时间段内的订单数和积分数
func (m *manager) GetOrderInfoByPeriod(ctx context.Context, startTime, endTime time.Time, reasonTypeList []uint32) (count uint32, sum uint32, err error) {

	// 如果是对订单金额
	if len(reasonTypeList) == 1 && reasonTypeList[0] == uint32(pb.ReasonType_REASON_FINISH_ORDER) {
		return m.store.GetScoreInfoByPeriodWithPrice(ctx, startTime, endTime)
	}

	// 如果是对积分
	return m.store.GetScoreInfoByPeriod(ctx, startTime, endTime, reasonTypeList)
}

// GetOrdersByPeriod 获取某个时间段内的订单号
func (m *manager) GetOrdersByPeriod(ctx context.Context, startTime, endTime time.Time, reasonTypeList []uint32) ([]string, error) {
	return m.store.GetOrdersByPeriod(ctx, startTime, endTime, reasonTypeList)
}

// ReSendOrder 补单接口
func (m *manager) ReSendOrder(ctx context.Context, orderId string) error {

	// 查询订单详情
	orderDetail, err := m.client.GetEsportTradeOrderDetail(ctx, orderId)
	if err != nil {
		log.ErrorWithCtx(ctx, "ReSendOrder GetEsportTradeOrderDetail fail, err: %v", err)
		return err
	}

	// 如果价值为零，返回报错
	if orderDetail.GetCommitTotalPrice() == 0 {
		log.ErrorWithCtx(ctx, "ReSendOrder order price is zero, orderDetail: %+v", orderDetail)
		return protocol.NewExactServerError(nil, errStatus.ErrRequestParamInvalid, "补单订单有效价格为0！")
	}

	// 校验dealToken
	tokenInfo, err := deal_token.Decode(orderDetail.GetDealToken())
	if nil != err {
		log.ErrorWithCtx(ctx, "ReSendOrder Decode dealToken failed, order:%+v err:%v", orderDetail, err)
	}
	// 校验dealToken和订单信息的正确性
	if tokenInfo.OrderID != orderDetail.GetOrderId() || uint32(tokenInfo.TotalPrice) != orderDetail.GetCommitTotalPrice() {
		log.ErrorWithCtx(ctx, "ReSendOrder dealToken check failed, tokenInfo:%+v, order check:%+v, totalPrice check:%+v",
			tokenInfo, tokenInfo.OrderID != orderDetail.GetOrderId(), uint32(tokenInfo.TotalPrice) != orderDetail.GetCommitTotalPrice())
		return protocol.NewExactServerError(nil, errStatus.ErrRequestParamInvalid, "该订单为无效订单，dealToken校验失败！")
	}
	// 校验调用链
	if code := deal_token.HandleDealToken(orderDetail.GetDealToken()); code != 0 {
		log.ErrorWithCtx(ctx, "ReSendOrder HandleDealToken failed, event:%+v errCode:%+v", orderDetail, code)
		return protocol.NewExactServerError(nil, errStatus.ErrRequestParamInvalid, "该订单为无效订单，dealToken校验失败！")
	}

	// 按照时间获取积分
	calculatePrice := orderDetail.GetCoachTotalPrice()
	if calculatePrice == 0 {
		calculatePrice = orderDetail.GetCommitTotalPrice()
	}
	orderTime := time.Unix(orderDetail.GetCommitTime(), 0)
	score := m.CalculateEsportScore(calculatePrice, orderTime, orderDetail.GetSignGuildId() != 0)

	// 按照订单详情进行补单操作
	orderScoreDetail := &common.EsportOrderDetail{
		Uid:        orderDetail.GetCoachUid(),
		TotalPrice: calculatePrice,
		Score:      score,
		ReasonType: pb.ReasonType_REASON_FINISH_ORDER,
		Reason:     "完成电竞订单",
		OrderId:    orderDetail.GetOrderId(),
		ServerTime: orderTime,
		GuildId:    orderDetail.GetSignGuildId(),
	}
	if _, err = m.AddEsportScore(ctx, orderScoreDetail); err != nil {
		log.ErrorWithCtx(ctx, "ReSendOrder AddEsportScore fail, err: %v", err)
		return err
	}

	return nil
}
