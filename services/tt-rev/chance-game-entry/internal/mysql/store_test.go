package mysql

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"github.com/jmoiron/sqlx"
	"github.com/stretchr/testify/assert"
	sqlxmock "github.com/zhashkevych/go-sqlxmock"
	pb "golang.52tt.com/protocol/services/chance-game-entry"
	"reflect"
	"regexp"
	"testing"
	"time"
)

func GetMockMysql() (mock sqlxmock.Sqlmock, db *sqlx.DB) {
	var err error
	db, mock, err = sqlxmock.Newx()
	if err != nil {
		panic(err)
	}

	return
}

func TestEntryMysql_AddToBlackWhiteList(t *testing.T) {
	mock, db := GetMockMysql()
	defer func() {
		_ = db.Close()
	}()
	test1 := &ChannelBlackConfig{
		ChannelId: 1,
		GameType:  uint32(pb.NewChanceGameType_NewChanceGameType_OnePiece),
	}

	sql1 := fmt.Sprintf("insert into %s (channel_id,game_type) VALUES (%d,%d) ON DUPLICATE KEY UPDATE del_id=0", ChannelBlackConfigTbl, test1.ChannelId, test1.GameType)
	mock.ExpectExec(regexp.QuoteMeta(sql1)).WillReturnResult(sqlxmock.NewResult(1, 1))
	mock.ExpectExec(regexp.QuoteMeta(sql1)).WillReturnResult(sqlxmock.NewResult(0, 0))

	test2 := &UserListConfig{
		Uid:      1,
		GameType: uint32(pb.NewChanceGameType_NewChanceGameType_OnePiece),
	}
	listType := uint32(pb.ChanceGameListType_ChanceGameListType_Black)
	sql2 := fmt.Sprintf("insert into %s (uid,game_type) VALUES (%d,%d) ON DUPLICATE KEY UPDATE del_id=0", getTableName(listType), test1.ChannelId, test1.GameType)
	mock.ExpectExec(regexp.QuoteMeta(sql2)).WillReturnResult(sqlxmock.NewResult(1, 1))
	mock.ExpectExec(regexp.QuoteMeta(sql2)).WillReturnResult(sqlxmock.NewResult(0, 0))

	type fields struct {
		db *sqlx.DB
	}
	type args struct {
		ctx       context.Context
		uid       uint32
		channelId uint32
		gameType  uint32
		listType  uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name:   "TestEntryMysql_AddToBlackWhiteList, add cid1",
			fields: fields{db: db},
			args: args{
				ctx:       context.Background(),
				uid:       0,
				channelId: test1.ChannelId,
				gameType:  test1.GameType,
				listType:  uint32(pb.ChanceGameListType_ChanceGameListType_Black),
			},
			wantErr: false,
		},
		{
			name:   "TestEntryMysql_AddToBlackWhiteList, add cid2",
			fields: fields{db: db},
			args: args{
				ctx:       context.Background(),
				uid:       0,
				channelId: test1.ChannelId,
				gameType:  test1.GameType,
				listType:  uint32(pb.ChanceGameListType_ChanceGameListType_Black),
			},
			wantErr: false,
		},
		{
			name:   "TestEntryMysql_AddToBlackWhiteList,add uid1",
			fields: fields{db: db},
			args: args{
				ctx:       context.Background(),
				uid:       test2.Uid,
				channelId: 0,
				gameType:  test2.GameType,
				listType:  listType,
			},
			wantErr: false,
		},
		{
			name:   "TestEntryMysql_AddToBlackWhiteList,add uid2",
			fields: fields{db: db},
			args: args{
				ctx:       context.Background(),
				uid:       test2.Uid,
				channelId: 0,
				gameType:  test2.GameType,
				listType:  listType,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &EntryMysql{
				db: tt.fields.db,
			}
			if err := c.AddToBlackWhiteList(tt.args.ctx, tt.args.uid, tt.args.channelId, tt.args.gameType, tt.args.listType); (err != nil) != tt.wantErr {
				t.Errorf("AddToBlackWhiteList() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestEntryMysql_BatDelBlackWhiteList(t *testing.T) {
	mock, db := GetMockMysql()
	defer func() {
		_ = db.Close()
	}()

	uidList := []uint32{1, 2, 3}
	cidList := []uint32{1, 2, 3}
	listType := uint32(pb.ChanceGameListType_ChanceGameListType_Black)

	sql1 := fmt.Sprintf("update %s set del_id=id where uid in (%s)", getTableName(listType), genParamJoinStr(uidList))
	sql2 := fmt.Sprintf("update %s set del_id=id where channel_id in (%s)", ChannelBlackConfigTbl, genParamJoinStr(cidList))
	mock.ExpectExec(regexp.QuoteMeta(sql1)).WillReturnResult(sqlxmock.NewResult(0, 1))
	mock.ExpectExec(regexp.QuoteMeta(sql2)).WillReturnResult(sqlxmock.NewResult(0, 4))

	type fields struct {
		db *sqlx.DB
	}
	type args struct {
		ctx      context.Context
		uidList  []uint32
		cidList  []uint32
		listType uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name:   "TestEntryMysql_BatDelBlackWhiteList, uidList",
			fields: fields{db: db},
			args: args{
				ctx:      context.Background(),
				uidList:  uidList,
				cidList:  nil,
				listType: listType,
			},
			wantErr: false,
		},
		{
			name:   "TestEntryMysql_BatDelBlackWhiteList, cidList",
			fields: fields{db: db},
			args: args{
				ctx:      context.Background(),
				uidList:  nil,
				cidList:  cidList,
				listType: 0,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &EntryMysql{
				db: tt.fields.db,
			}
			if err := c.BatDelBlackWhiteList(tt.args.ctx, tt.args.uidList, tt.args.cidList, tt.args.listType); (err != nil) != tt.wantErr {
				t.Errorf("BatDelBlackWhiteList() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestEntryMysql_GetChannelList(t *testing.T) {
	mock, db := GetMockMysql()
	defer func() {
		_ = db.Close()
	}()

	ctime := time.Now().Add(-30 * time.Minute)

	gameType := uint32(pb.NewChanceGameType_NewChanceGameType_OnePiece)
	res := []*ChannelBlackConfig{
		{
			Id:        1,
			ChannelId: 1,
			GameType:  gameType,
			CTime:     uint32(ctime.Unix()),
			MTime:     uint32(ctime.Unix()),
		},
		{
			Id:        2,
			ChannelId: 2,
			GameType:  gameType,
			CTime:     uint32(ctime.Unix()),
			MTime:     uint32(ctime.Unix()),
		},
	}

	sql := fmt.Sprintf("select distinct(channel_id) from %s where del_id=0 limit %d,%d", ChannelBlackConfigTbl, 0, 10)
	sql2 := fmt.Sprintf("select id,channel_id,game_type,UNIX_TIMESTAMP(ctime) as ctime,UNIX_TIMESTAMP(mtime) as mtime "+
		"from %s where channel_id in (%s) and del_id=0", ChannelBlackConfigTbl, genParamJoinStr([]uint32{1, 2}))
	rows1 := sqlxmock.NewRows([]string{"channel_id"}).AddRow(1).AddRow(2)
	rows2 := sqlxmock.NewRows([]string{"id", "channel_id", "game_type", "ctime", "mtime"}).
		AddRow(res[0].Id, res[0].ChannelId, res[0].GameType, res[0].CTime, res[0].MTime).
		AddRow(res[1].Id, res[1].ChannelId, res[1].GameType, res[1].CTime, res[1].MTime)
	mock.ExpectQuery(regexp.QuoteMeta(sql)).WillReturnRows(rows1)
	mock.ExpectQuery(regexp.QuoteMeta(sql2)).WillReturnRows(rows2)

	type fields struct {
		db *sqlx.DB
	}
	type args struct {
		ctx    context.Context
		offset uint32
		limit  uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    []*ChannelBlackConfig
		wantErr bool
	}{
		{
			name:   "TestEntryMysql_GetChannelList: not nil",
			fields: fields{db: db},
			args: args{
				ctx:    context.Background(),
				offset: 0,
				limit:  10,
			},
			want:    res,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &EntryMysql{
				db: tt.fields.db,
			}
			got, err := c.GetChannelList(tt.args.ctx, tt.args.offset, tt.args.limit)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetChannelList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetChannelList() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestEntryMysql_GetRecordCnt(t *testing.T) {
	mock, db := GetMockMysql()
	defer func() {
		_ = db.Close()
	}()

	ctime := time.Now().Add(-30 * time.Minute)
	gameType := uint32(pb.NewChanceGameType_NewChanceGameType_OnePiece)
	cidList := []*ChannelBlackConfig{
		{
			Id:        1,
			ChannelId: 1,
			GameType:  gameType,
			CTime:     uint32(ctime.Unix()),
			MTime:     uint32(ctime.Unix()),
		},
		{
			Id:        2,
			ChannelId: 2,
			GameType:  gameType,
			DelId:     2,
			CTime:     uint32(ctime.Unix()),
			MTime:     uint32(ctime.Unix()),
		},
	}

	uidList := []*UserListConfig{
		{
			Id:       1,
			Uid:      1,
			GameType: gameType,
			CTime:    uint32(ctime.Unix()),
			MTime:    uint32(ctime.Unix()),
		},
	}
	listType := uint32(pb.ChanceGameListType_ChanceGameListType_White)

	sql1 := fmt.Sprintf("select count(distinct(channel_id)) from %s where del_id=0", ChannelBlackConfigTbl)
	sql2 := fmt.Sprintf("select count(distinct(uid)) from %s where del_id=0", getTableName(listType))

	mock.NewRows([]string{"id", "channel_id", "game_type", "del_id", "ctime", "mtime"}).
		AddRow(cidList[0].Id, cidList[0].ChannelId, cidList[0].GameType, cidList[0].DelId, cidList[0].CTime, cidList[0].MTime).
		AddRow(cidList[1].Id, cidList[1].ChannelId, cidList[1].GameType, cidList[1].DelId, cidList[1].CTime, cidList[1].MTime)
	mock.NewRows([]string{"id", "uid", "game_type", "del_id", "ctime", "mtime"}).
		AddRow(uidList[0].Id, uidList[0].Uid, uidList[0].GameType, uidList[0].DelId, uidList[0].CTime, uidList[0].MTime)

	mock.ExpectQuery(regexp.QuoteMeta(sql1)).WillReturnRows(sqlxmock.NewRows([]string{"count(distinct(channel_id))"}).AddRow(1))
	mock.ExpectQuery(regexp.QuoteMeta(sql2)).WillReturnRows(sqlxmock.NewRows([]string{"count(distinct(uid))"}).AddRow(1))

	type fields struct {
		db *sqlx.DB
	}
	type args struct {
		ctx          context.Context
		queryChannel bool
		listType     uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    uint32
		wantErr bool
	}{
		{
			name:   "TestEntryMysql_GetRecordCnt channelId",
			fields: fields{db: db},
			args: args{
				ctx:          context.Background(),
				queryChannel: true,
				listType:     listType,
			},
			want:    1,
			wantErr: false,
		},
		{
			name:   "TestEntryMysql_GetRecordCnt uid",
			fields: fields{db: db},
			args: args{
				ctx:          context.Background(),
				queryChannel: false,
				listType:     listType,
			},
			want:    1,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &EntryMysql{
				db: tt.fields.db,
			}
			got, err := c.GetRecordCnt(tt.args.ctx, tt.args.queryChannel, tt.args.listType)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetRecordCnt() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("GetRecordCnt() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestEntryMysql_GetUserList(t *testing.T) {
	mock, db := GetMockMysql()
	defer func() {
		_ = db.Close()
	}()

	ctime := time.Now().Add(-30 * time.Minute)

	listType := uint32(pb.ChanceGameListType_ChanceGameListType_Black)
	gameType := uint32(pb.NewChanceGameType_NewChanceGameType_OnePiece)
	res := []*UserListConfig{
		{
			Id:       1,
			Uid:      1,
			GameType: gameType,
			CTime:    uint32(ctime.Unix()),
			MTime:    uint32(ctime.Unix()),
		},
		{
			Id:       2,
			Uid:      2,
			GameType: gameType,
			CTime:    uint32(ctime.Unix()),
			MTime:    uint32(ctime.Unix()),
		},
	}

	sql := fmt.Sprintf("select distinct(uid) from %s where del_id=0 limit %d,%d", getTableName(listType), 0, 10)
	sql2 := fmt.Sprintf("select id,uid,game_type,UNIX_TIMESTAMP(ctime) as ctime,UNIX_TIMESTAMP(mtime) as mtime "+
		"from %s where uid in (%s) and del_id=0", getTableName(listType), genParamJoinStr([]uint32{1, 2}))
	rows1 := sqlxmock.NewRows([]string{"uid"}).AddRow(1).AddRow(2)
	rows2 := sqlxmock.NewRows([]string{"id", "uid", "game_type", "ctime", "mtime"}).
		AddRow(res[0].Id, res[0].Uid, res[0].GameType, res[0].CTime, res[0].MTime).
		AddRow(res[1].Id, res[1].Uid, res[1].GameType, res[1].CTime, res[1].MTime)
	mock.ExpectQuery(regexp.QuoteMeta(sql)).WillReturnRows(rows1)
	mock.ExpectQuery(regexp.QuoteMeta(sql2)).WillReturnRows(rows2)

	type fields struct {
		db *sqlx.DB
	}
	type args struct {
		ctx      context.Context
		offset   uint32
		limit    uint32
		listType uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    []*UserListConfig
		wantErr bool
	}{
		{
			name:   "TestEntryMysql_GetUserList not nil",
			fields: fields{db: db},
			args: args{
				ctx:      context.Background(),
				offset:   0,
				limit:    10,
				listType: listType,
			},
			want:    res,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &EntryMysql{
				db: tt.fields.db,
			}
			got, err := c.GetUserList(tt.args.ctx, tt.args.offset, tt.args.limit, tt.args.listType)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetUserList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetUserList() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestEntryMysql_SearchChannelBlackRecord(t *testing.T) {
	mock, db := GetMockMysql()
	defer func() {
		_ = db.Close()
	}()

	now := time.Now()
	channelInfo := []*ChannelBlackConfig{&ChannelBlackConfig{
		Id:        1,
		ChannelId: 1,
		GameType:  1,
		DelId:     0,
		CTime:     uint32(now.Unix()),
		MTime:     uint32(now.Unix()),
	}}

	sql := fmt.Sprintf("select id,channel_id,game_type,UNIX_TIMESTAMP(ctime) as ctime,UNIX_TIMESTAMP(mtime) as mtime "+
		"from %s where channel_id=%d and del_id=0", ChannelBlackConfigTbl, channelInfo[0].ChannelId)
	//sql2 := fmt.Sprintf("select id,channel_id,game_type,UNIX_TIMESTAMP(ctime) as ctime,UNIX_TIMESTAMP(mtime) as mtime "+
	//	"from %s where channel_id=%d and del_id=0", ChannelBlackConfigTbl, 100)

	row1 := mock.NewRows([]string{"id", "channel_id", "game_type", "del_id", "ctime", "mtime"}).
		AddRow(channelInfo[0].Id, channelInfo[0].ChannelId, channelInfo[0].GameType, channelInfo[0].DelId, channelInfo[0].CTime, channelInfo[0].MTime)

	mock.ExpectQuery(regexp.QuoteMeta(sql)).WillReturnRows(row1)
	//mock.ExpectQuery(regexp.QuoteMeta(sql2)).WillReturnRows(mock.NewRows([]string{}))

	type fields struct {
		db *sqlx.DB
	}
	type args struct {
		ctx context.Context
		cid uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    []*ChannelBlackConfig
		wantErr bool
	}{
		{
			name:   "TestEntryMysql_SearchChannelBlackRecord not nil",
			fields: fields{db: db},
			args: args{
				ctx: context.Background(),
				cid: channelInfo[0].ChannelId,
			},
			want:    channelInfo,
			wantErr: false,
		},
		//{
		//	name:   "TestEntryMysql_SearchChannelBlackRecord nil",
		//	fields: fields{db: db},
		//	args: args{
		//		ctx: context.Background(),
		//		cid: 100,
		//	},
		//	want:    nil,
		//	wantErr: false,
		//},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &EntryMysql{
				db: tt.fields.db,
			}
			got, err := c.SearchChannelBlackRecord(tt.args.ctx, tt.args.cid)
			if (err != nil) != tt.wantErr {
				t.Errorf("SearchChannelBlackRecord() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("SearchChannelBlackRecord() got = %v,type(got):%v, want %v type(want):%v", got, reflect.TypeOf(got), tt.want, reflect.TypeOf(tt.want))
			}
		})
	}
}

func TestEntryMysql_SearchUserBlackWhiteRecord(t *testing.T) {
	mock, db := GetMockMysql()
	defer func() {
		_ = db.Close()
	}()

	now := time.Now()
	userInfo := []*UserListConfig{&UserListConfig{
		Id:       1,
		Uid:      1,
		GameType: 1,
		DelId:    0,
		CTime:    uint32(now.Unix()),
		MTime:    uint32(now.Unix()),
	}}
	listType := uint32(pb.ChanceGameListType_ChanceGameListType_White)
	sql := fmt.Sprintf("select id,uid,game_type,UNIX_TIMESTAMP(ctime) as ctime,UNIX_TIMESTAMP(mtime) as mtime "+
		"from %s where uid=%d and del_id=0", getTableName(listType), userInfo[0].Uid)
	//sql2 := fmt.Sprintf("select id,uid,game_type,UNIX_TIMESTAMP(ctime) as ctime,UNIX_TIMESTAMP(mtime) as mtime "+
	//	"from %s where uid=%d and del_id=0", getTableName(listType), 100)

	row1 := mock.NewRows([]string{"id", "uid", "game_type", "del_id", "ctime", "mtime"}).
		AddRow(userInfo[0].Id, userInfo[0].Uid, userInfo[0].GameType, userInfo[0].DelId, userInfo[0].CTime, userInfo[0].MTime)

	mock.ExpectQuery(regexp.QuoteMeta(sql)).WillReturnRows(row1)
	//mock.ExpectQuery(regexp.QuoteMeta(sql2)).WillReturnRows(mock.NewRows([]string{}))

	type fields struct {
		db *sqlx.DB
	}
	type args struct {
		ctx      context.Context
		uid      uint32
		listType uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    []*UserListConfig
		wantErr bool
	}{
		{
			name:   "TestEntryMysql_SearchUserBlackWhiteRecord not nil",
			fields: fields{db: db},
			args: args{
				ctx:      context.Background(),
				uid:      userInfo[0].Uid,
				listType: listType,
			},
			want:    userInfo,
			wantErr: false,
		},
		//{
		//	name:   "TestEntryMysql_SearchUserBlackWhiteRecord nil",
		//	fields: fields{db: db},
		//	args: args{
		//		ctx:      context.Background(),
		//		uid:      100,
		//		listType: listType,
		//	},
		//	want:    nil,
		//	wantErr: false,
		//},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &EntryMysql{
				db: tt.fields.db,
			}
			got, err := c.SearchUserBlackWhiteRecord(tt.args.ctx, tt.args.uid, tt.args.listType)
			if (err != nil) != tt.wantErr {
				t.Errorf("SearchUserBlackWhiteRecord() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("SearchUserBlackWhiteRecord() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_genParamJoinStr(t *testing.T) {
	type args struct {
		list []uint32
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{
			name: "Test_genParamJoinStr",
			args: args{
				list: []uint32{1, 2},
			},
			want: "1,2",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := genParamJoinStr(tt.args.list); got != tt.want {
				t.Errorf("genParamJoinStr() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_getTableName(t *testing.T) {
	type args struct {
		listType uint32
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{
			name: "Test_getTableName",
			args: args{listType: uint32(pb.ChanceGameListType_ChanceGameListType_Black)},
			want: UserBlackConfigTbl,
		},
		{
			name: "Test_getTableName",
			args: args{listType: uint32(pb.ChanceGameListType_ChanceGameListType_White)},
			want: UserWhiteConfigTbl,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := getTableName(tt.args.listType); got != tt.want {
				t.Errorf("getTableName() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestEntryMysql_DelAccessConfById(t *testing.T) {
	mock, db := GetMockMysql()
	defer func() {
		_ = db.Close()
	}()

	id := uint32(1)

	type fields struct {
		db *sqlx.DB
	}
	type args struct {
		ctx context.Context
		id  uint32
	}
	tests := []struct {
		name     string
		fields   fields
		initFunc func()
		args     args
		wantErr  bool
	}{
		{
			name:   "TestEntryMysql_DelAccessConfById",
			fields: fields{db: db},
			initFunc: func() {
				sql := fmt.Sprintf("update %s set del_id=id where id=%d", AccessConditionTbl, id)
				mock.ExpectExec(regexp.QuoteMeta(sql)).WillReturnResult(sqlxmock.NewResult(0, 1))
			},
			args: args{
				ctx: context.Background(),
				id:  id,
			},
			wantErr: false,
		},
		{
			name:   "TestEntryMysql_DelAccessConfById want err",
			fields: fields{db: db},
			initFunc: func() {
				sql := fmt.Sprintf("update %s set del_id=id where id=%d", AccessConditionTbl, id)
				mock.ExpectExec(regexp.QuoteMeta(sql)).WillReturnError(errors.New("some errs"))
			},
			args: args{
				ctx: context.Background(),
				id:  id,
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &EntryMysql{
				db: tt.fields.db,
			}
			if tt.initFunc != nil {
				tt.initFunc()
			}
			if err := c.DelAccessConfById(tt.args.ctx, tt.args.id); (err != nil) != tt.wantErr {
				t.Errorf("DelAccessConfById() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestEntryMysql_GetAccessConditionConf(t *testing.T) {
	mock, db := GetMockMysql()
	defer func() {
		_ = db.Close()
	}()

	type fields struct {
		db *sqlx.DB
	}
	type args struct {
		ctx      context.Context
		gameType uint32
		condType uint32
	}
	tests := []struct {
		name     string
		fields   fields
		initFunc func()
		args     args
		want     string
		wantErr  bool
	}{
		{
			name:   "TestEntryMysql_GetAccessConditionConf",
			fields: fields{db: db},
			initFunc: func() {
				query := fmt.Sprintf("select cond from %s where game_type=%d and cond_type=%d and magic_spirit_id=0 and del_id=0", AccessConditionTbl, uint32(1), uint32(1))
				rows1 := sqlxmock.NewRows([]string{"cond"}).AddRow("want string")
				mock.ExpectQuery(regexp.QuoteMeta(query)).WillReturnRows(rows1)
			},
			args: args{
				ctx:      context.Background(),
				gameType: uint32(1),
				condType: uint32(1),
			},
			want:    "want string",
			wantErr: false,
		},
		{
			name:   "TestEntryMysql_GetAccessConditionConf no rows",
			fields: fields{db: db},
			initFunc: func() {
				query := fmt.Sprintf("select cond from %s where game_type=%d and cond_type=%d and magic_spirit_id=0 and del_id=0", AccessConditionTbl, uint32(1), uint32(1))
				mock.ExpectQuery(regexp.QuoteMeta(query)).WillReturnError(sql.ErrNoRows)
			},
			args: args{
				ctx:      context.Background(),
				gameType: uint32(1),
				condType: uint32(1),
			},
			want:    "",
			wantErr: false,
		},
		{
			name:   "TestEntryMysql_GetAccessConditionConf want err",
			fields: fields{db: db},
			initFunc: func() {
				query := fmt.Sprintf("select cond from %s where game_type=%d and cond_type=%d and magic_spirit_id=0 and del_id=0", AccessConditionTbl, uint32(1), uint32(1))
				mock.ExpectQuery(regexp.QuoteMeta(query)).WillReturnError(errors.New("some err"))
			},
			args: args{
				ctx:      context.Background(),
				gameType: uint32(1),
				condType: uint32(1),
			},
			want:    "",
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &EntryMysql{
				db: tt.fields.db,
			}
			if tt.initFunc != nil {
				tt.initFunc()
			}
			got, err := c.GetAccessConditionConf(tt.args.ctx, tt.args.gameType, tt.args.condType)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetAccessConditionConf() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("GetAccessConditionConf() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestEntryMysql_SetAccessConditionConf(t *testing.T) {
	mock, db := GetMockMysql()
	defer func() {
		_ = db.Close()
	}()

	type fields struct {
		db *sqlx.DB
	}
	type args struct {
		ctx           context.Context
		gameType      uint32
		magicSpiritId uint32
		condType      uint32
		cond          string
	}
	tests := []struct {
		name     string
		fields   fields
		initFunc func()
		args     args
		wantErr  bool
	}{
		{
			name:   "TestEntryMysql_SetAccessConditionConf",
			fields: fields{db: db},
			initFunc: func() {
				query := fmt.Sprintf("insert into %s (game_type,magic_spirit_id,cond_type,cond,del_id) VALUES (%d,%d,%d,'%s',0) ON DUPLICATE KEY UPDATE cond='%s',del_id=0",
					AccessConditionTbl, uint32(1), uint32(0), uint32(1), "11111111", "11111111")
				mock.ExpectExec(regexp.QuoteMeta(query)).WillReturnResult(sqlxmock.NewResult(1, 1))
			},
			args: args{
				ctx:      context.Background(),
				gameType: uint32(1),
				condType: uint32(1),
				cond:     "11111111",
			},
			wantErr: false,
		},
		{
			name:   "TestEntryMysql_SetAccessConditionConf want err",
			fields: fields{db: db},
			initFunc: func() {
				query := fmt.Sprintf("insert into %s (game_type,magic_spirit_id,cond_type,cond,del_id) VALUES (%d,%d,%d,'%s',0) ON DUPLICATE KEY UPDATE cond='%s',del_id=0",
					AccessConditionTbl, uint32(1), uint32(0), uint32(1), "11111111", "11111111")
				mock.ExpectExec(regexp.QuoteMeta(query)).WillReturnError(errors.New("some errs"))
			},
			args: args{
				ctx:      context.Background(),
				gameType: uint32(1),
				condType: uint32(1),
				cond:     "11111111",
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &EntryMysql{
				db: tt.fields.db,
			}
			if tt.initFunc != nil {
				tt.initFunc()
			}
			if err := c.SetAccessConditionConf(tt.args.ctx, tt.args.gameType, tt.args.magicSpiritId, tt.args.condType, tt.args.cond); (err != nil) != tt.wantErr {
				t.Errorf("SetAccessConditionConf() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestEntryMysql_GetAccessCondMaxMTime(t *testing.T) {
	mock, db := GetMockMysql()
	defer func() {
		_ = db.Close()
	}()

	mtime := uint32(time.Now().Unix())

	type fields struct {
		db *sqlx.DB
	}
	type args struct {
		ctx context.Context
	}
	tests := []struct {
		name     string
		fields   fields
		initFunc func()
		args     args
		want     uint32
		wantErr  bool
	}{
		{
			name:   "TestEntryMysql_GetAccessCondMaxMTime",
			fields: fields{db: db},
			initFunc: func() {
				query := fmt.Sprintf("select IFNULL(max(UNIX_TIMESTAMP(mtime)), 0) as mtime from %s ", AccessConditionTbl)
				rows1 := sqlxmock.NewRows([]string{"max(mtime)"}).AddRow(mtime)
				mock.ExpectQuery(regexp.QuoteMeta(query)).WillReturnRows(rows1)
			},
			args: args{
				ctx: context.Background(),
			},
			want:    mtime,
			wantErr: false,
		},
		{
			name:   "TestEntryMysql_GetAccessCondMaxMTime want err",
			fields: fields{db: db},
			initFunc: func() {
				query := fmt.Sprintf("select IFNULL(max(UNIX_TIMESTAMP(mtime)), 0) as mtime from %s ", AccessConditionTbl)
				mock.ExpectQuery(regexp.QuoteMeta(query)).WillReturnError(errors.New("some err"))
			},
			args: args{
				ctx: context.Background(),
			},
			want:    0,
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &EntryMysql{
				db: tt.fields.db,
			}
			if tt.initFunc != nil {
				tt.initFunc()
			}
			got, err := c.GetAccessCondMaxMTime(tt.args.ctx)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetAccessConditionConf() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !assert.Equalf(t, tt.want, got, "GetAccessConditionConf() = %v, want %v", got, tt.want) {
				t.Errorf("GetAccessConditionConf() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestEntryMysql_GetMagicSpiritAccessConf(t *testing.T) {
	mock, db := GetMockMysql()
	defer func() {
		_ = db.Close()
	}()

	condType := uint32(0)
	accessCond := &AccessCond{
		Id:            1,
		GameType:      3,
		MagicSpiritId: 1,
		CondType:      0,
		Cond:          "111",
	}
	wantResp := []*AccessCond{
		accessCond,
	}

	type fields struct {
		db *sqlx.DB
	}
	type args struct {
		ctx      context.Context
		gameType uint32
		condType uint32
	}
	tests := []struct {
		name     string
		fields   fields
		initFunc func()
		args     args
		want     []*AccessCond
		wantErr  bool
	}{
		{
			name:   "TestEntryMysql_GetMagicSpiritAccessConf",
			fields: fields{db: db},
			initFunc: func() {
				query := fmt.Sprintf("select id,game_type,magic_spirit_id,cond_type,cond from %s where magic_spirit_id<>0 and cond_type=%d and del_id=0",
					AccessConditionTbl, condType)
				rows1 := sqlxmock.NewRows([]string{"id", "game_type", "magic_spirit_id", "cond_type", "cond"}).AddRow(accessCond.Id, accessCond.GameType, accessCond.MagicSpiritId, accessCond.CondType, accessCond.Cond)
				mock.ExpectQuery(regexp.QuoteMeta(query)).WillReturnRows(rows1)
			},
			args: args{
				ctx:      context.Background(),
				condType: condType,
			},
			want:    wantResp,
			wantErr: false,
		},
		{
			name:   "TestEntryMysql_GetMagicSpiritAccessConf no rows",
			fields: fields{db: db},
			initFunc: func() {
				query := fmt.Sprintf("select id,game_type,magic_spirit_id,cond_type,cond from %s where magic_spirit_id<>0 and cond_type=%d and del_id=0",
					AccessConditionTbl, condType)
				mock.ExpectQuery(regexp.QuoteMeta(query)).WillReturnError(sql.ErrNoRows)
			},
			args: args{
				ctx:      context.Background(),
				condType: condType,
			},
			want:    []*AccessCond{},
			wantErr: false,
		},
		{
			name:   "TestEntryMysql_GetMagicSpiritAccessConf want err",
			fields: fields{db: db},
			initFunc: func() {
				query := fmt.Sprintf("select cond from %s where game_type=%d and cond_type=%d and magic_spirit_id=0 and del_id=0", AccessConditionTbl, uint32(1), uint32(1))
				mock.ExpectQuery(regexp.QuoteMeta(query)).WillReturnError(errors.New("some err"))
			},
			args: args{
				ctx:      context.Background(),
				condType: condType,
			},
			want:    []*AccessCond{},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &EntryMysql{
				db: tt.fields.db,
			}
			if tt.initFunc != nil {
				tt.initFunc()
			}
			got, err := c.GetMagicSpiritAccessConf(tt.args.ctx, tt.args.condType)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetAccessConditionConf() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !assert.Equalf(t, tt.want, got, "GetAccessConditionConf() = %v, want %v", got, tt.want) {
				t.Errorf("GetAccessConditionConf() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestEntryMysql_GetChanceGameSwitch(t *testing.T) {
	mock, db := GetMockMysql()
	defer func() {
		_ = db.Close()
	}()

	mtime := time.Now().Add(-10 * time.Second)
	res := &ChanceGameSwitch{
		GameType: 1,
		State:    1,
		Mtime:    mtime,
	}

	rows := sqlxmock.NewRows([]string{"game_type", "state", "mtime"}).
		AddRow(res.GameType, res.State, res.Mtime)

	sql := fmt.Sprintf("select game_type,state,mtime from %s order by mtime desc limit 10", ChanceGameSwitchTbl)

	mock.ExpectQuery(regexp.QuoteMeta(sql)).WillReturnRows(rows)

	type fields struct {
		db *sqlx.DB
	}
	type args struct {
		ctx context.Context
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    []*ChanceGameSwitch
		wantErr bool
	}{
		{
			name:   "TestEntryMysql_GetChanceGameSwitch2",
			fields: fields{db: db},
			args:   args{ctx: context.Background()},
			want: []*ChanceGameSwitch{
				{
					GameType: 1,
					State:    1,
					Mtime:    mtime,
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &EntryMysql{
				db: tt.fields.db,
			}
			got, err := c.GetChanceGameSwitch(tt.args.ctx)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetChanceGameSwitch() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetChanceGameSwitch() got = %+v, want %+v", got, tt.want)
			}
		})
	}
}

func TestEntryMysql_SetChanceGameSwitch(t *testing.T) {
	mock, db := GetMockMysql()
	defer func() {
		_ = db.Close()
	}()

	gameType := uint32(1)
	state := uint32(0)
	sql1 := fmt.Sprintf("insert into %s (game_type,state) VALUES (%d,%d) ON DUPLICATE KEY UPDATE state=%d",
		ChanceGameSwitchTbl, gameType, state, state)
	sql2 := fmt.Sprintf("insert into %s (game_type,state) VALUES (%d,%d) ON DUPLICATE KEY UPDATE state=%d",
		ChanceGameSwitchTbl, gameType, state, state)
	sql3 := fmt.Sprintf("insert into %s (game_type,state) VALUES (%d,%d) ON DUPLICATE KEY UPDATE state=%d",
		ChanceGameSwitchTbl, gameType, 1, 1)
	mock.ExpectExec(regexp.QuoteMeta(sql1)).WillReturnResult(sqlxmock.NewResult(1, 1))
	mock.ExpectExec(regexp.QuoteMeta(sql2)).WillReturnResult(sqlxmock.NewResult(1, 0))
	mock.ExpectExec(regexp.QuoteMeta(sql3)).WillReturnResult(sqlxmock.NewResult(2, 1))

	type fields struct {
		db *sqlx.DB
	}
	type args struct {
		ctx      context.Context
		gameType uint32
		state    uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name:   "TestEntryMysql_SetChanceGameSwitch",
			fields: fields{db: db},
			args: args{
				ctx:      context.Background(),
				gameType: gameType,
				state:    0,
			},
			wantErr: false,
		},
		{
			name:   "TestEntryMysql_SetChanceGameSwitch 重复插入",
			fields: fields{db: db},
			args: args{
				ctx:      context.Background(),
				gameType: gameType,
				state:    0,
			},
			wantErr: false,
		}, {
			name:   "TestEntryMysql_SetChanceGameSwitch 更新状态",
			fields: fields{db: db},
			args: args{
				ctx:      context.Background(),
				gameType: gameType,
				state:    1,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &EntryMysql{
				db: tt.fields.db,
			}
			if err := c.SetChanceGameSwitch(tt.args.ctx, tt.args.gameType, tt.args.state); (err != nil) != tt.wantErr {
				t.Errorf("SetChanceGameSwitch() error = %v, wantErr %v, tt.args:%v", err, tt.wantErr, tt.args)
			}
		})
	}
}

func TestEntryMysql_GetAllChanceGameOpenTime(t *testing.T) {
	mock, db := GetMockMysql()
	defer func() {
		_ = db.Close()
	}()

	want := []*ChanceGameOpenTime{
		{
			GameType:  1,
			BeginTime: time.Now(),
			EndTime:   time.Now().Add(time.Hour),
			OpUser:    "111",
			Ctime:     time.Now().Add(-10 * time.Second),
			Mtime:     time.Now().Add(-10 * time.Second),
		},
		{
			GameType:  2,
			BeginTime: time.Now(),
			EndTime:   time.Now().Add(time.Hour),
			OpUser:    "111",
			Ctime:     time.Now().Add(-10 * time.Second),
			Mtime:     time.Now().Add(-10 * time.Second),
		},
	}

	type fields struct {
		db *sqlx.DB
	}
	type args struct {
		ctx context.Context
	}
	tests := []struct {
		name     string
		fields   fields
		initFunc func()
		args     args
		want     []*ChanceGameOpenTime
		wantErr  bool
	}{
		{
			name:   "common",
			fields: fields{db: db},
			initFunc: func() {
				query := fmt.Sprintf("select game_type,begin_time,end_time,op_user,ctime,mtime from %s", ChanceGameOpenTimeTbl)
				rows := sqlxmock.NewRows([]string{"game_type", "begin_time", "end_time", "op_user", "ctime", "mtime"}).
					AddRow(want[0].GameType, want[0].BeginTime, want[0].EndTime, want[0].OpUser, want[0].Ctime, want[0].Mtime).
					AddRow(want[1].GameType, want[1].BeginTime, want[1].EndTime, want[1].OpUser, want[1].Ctime, want[1].Mtime)
				mock.ExpectQuery(regexp.QuoteMeta(query)).WillReturnRows(rows)
			},
			args: args{
				ctx: context.Background(),
			},
			want:    want,
			wantErr: false,
		},
		{
			name:   "some err",
			fields: fields{db: db},
			initFunc: func() {
				query := fmt.Sprintf("select game_type,begin_time,end_time,op_user,ctime,mtime from %s", ChanceGameOpenTimeTbl)
				//rows := sqlxmock.NewRows([]string{"game_type", "begin_time", "end_time", "op_user", "ctime", "mtime"}).
				//	AddRow(want[0].GameType, want[0].BeginTime, want[0].EndTime, want[0].OpUser, want[0].Ctime, want[0].Mtime).
				//	AddRow(want[1].GameType, want[1].BeginTime, want[1].EndTime, want[1].OpUser, want[1].Ctime, want[1].Mtime)
				mock.ExpectQuery(regexp.QuoteMeta(query)).WillReturnError(errors.New("some err"))
			},
			args: args{
				ctx: context.Background(),
			},
			want:    []*ChanceGameOpenTime{},
			wantErr: true,
		},
		{
			name:   "no rows",
			fields: fields{db: db},
			initFunc: func() {
				query := fmt.Sprintf("select game_type,begin_time,end_time,op_user,ctime,mtime from %s", ChanceGameOpenTimeTbl)
				//rows := sqlxmock.NewRows([]string{"game_type", "begin_time", "end_time", "op_user", "ctime", "mtime"}).
				//	AddRow(want[0].GameType, want[0].BeginTime, want[0].EndTime, want[0].OpUser, want[0].Ctime, want[0].Mtime).
				//	AddRow(want[1].GameType, want[1].BeginTime, want[1].EndTime, want[1].OpUser, want[1].Ctime, want[1].Mtime)
				mock.ExpectQuery(regexp.QuoteMeta(query)).WillReturnError(sql.ErrNoRows)
			},
			args: args{
				ctx: context.Background(),
			},
			want:    []*ChanceGameOpenTime{},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &EntryMysql{
				db: tt.fields.db,
			}
			if tt.initFunc != nil {
				tt.initFunc()
			}
			got, err := c.GetAllChanceGameOpenTime(tt.args.ctx)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetAllChanceGameOpenTime() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			assert.Equalf(t, tt.want, got, "GetAllChanceGameOpenTime(%v)", tt.args.ctx)
		})
	}
}

func TestEntryMysql_GetChanceGameOpenTime(t *testing.T) {
	mock, db := GetMockMysql()
	defer func() {
		_ = db.Close()
	}()

	want := &ChanceGameOpenTime{
		GameType:  1,
		BeginTime: time.Now(),
		EndTime:   time.Now().Add(time.Hour),
		OpUser:    "111",
		Ctime:     time.Now().Add(-10 * time.Second),
		Mtime:     time.Now().Add(-10 * time.Second),
	}

	type fields struct {
		db *sqlx.DB
	}
	type args struct {
		ctx      context.Context
		gameType uint32
	}
	tests := []struct {
		name     string
		fields   fields
		initFunc func()
		args     args
		want     *ChanceGameOpenTime
		wantErr  bool
	}{
		{
			name:   "common",
			fields: fields{db: db},
			initFunc: func() {
				query := fmt.Sprintf("select game_type,begin_time,end_time,op_user,ctime,mtime from %s where game_type=?", ChanceGameOpenTimeTbl)
				rows := sqlxmock.NewRows([]string{"game_type", "begin_time", "end_time", "op_user", "ctime", "mtime"}).
					AddRow(want.GameType, want.BeginTime, want.EndTime, want.OpUser, want.Ctime, want.Mtime)
				mock.ExpectQuery(regexp.QuoteMeta(query)).WithArgs(want.GameType).WillReturnRows(rows)
			},
			args: args{
				ctx:      context.Background(),
				gameType: want.GameType,
			},
			want:    want,
			wantErr: false,
		},
		{
			name:   "some err",
			fields: fields{db: db},
			initFunc: func() {
				query := fmt.Sprintf("select game_type,begin_time,end_time,op_user,ctime,mtime from %s where game_type=?", ChanceGameOpenTimeTbl)
				//rows := sqlxmock.NewRows([]string{ "game_type", "begin_time", "end_time", "op_user", "ctime","mtime"}).
				//AddRow(want.GameType, want.BeginTime, want.EndTime, want.OpUser, want.Ctime,want.Mtime)
				mock.ExpectQuery(regexp.QuoteMeta(query)).WithArgs(want.GameType).WillReturnError(errors.New("some err"))
			},
			args: args{
				ctx:      context.Background(),
				gameType: want.GameType,
			},
			want:    &ChanceGameOpenTime{},
			wantErr: true,
		},
		{
			name:   "no rows",
			fields: fields{db: db},
			initFunc: func() {
				query := fmt.Sprintf("select game_type,begin_time,end_time,op_user,ctime,mtime from %s where game_type=?", ChanceGameOpenTimeTbl)
				//rows := sqlxmock.NewRows([]string{ "game_type", "begin_time", "end_time", "op_user", "ctime","mtime"}).
				//AddRow(want.GameType, want.BeginTime, want.EndTime, want.OpUser, want.Ctime,want.Mtime)
				mock.ExpectQuery(regexp.QuoteMeta(query)).WithArgs(want.GameType).WillReturnError(sql.ErrNoRows)
			},
			args: args{
				ctx:      context.Background(),
				gameType: want.GameType,
			},
			want:    nil,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &EntryMysql{
				db: tt.fields.db,
			}
			if tt.initFunc != nil {
				tt.initFunc()
			}
			got, err := c.GetChanceGameOpenTime(tt.args.ctx, tt.args.gameType)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetChanceGameOpenTime() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			assert.Equalf(t, tt.want, got, "GetChanceGameOpenTime(%v, %v)", tt.args.ctx, tt.args.gameType)
		})
	}
}

func TestEntryMysql_GetChanceGameOpenTimeMaxMtime(t *testing.T) {
	mock, db := GetMockMysql()
	defer func() {
		_ = db.Close()
	}()

	mtime := time.Now()

	type fields struct {
		db *sqlx.DB
	}
	type args struct {
		ctx context.Context
	}
	tests := []struct {
		name     string
		fields   fields
		initFunc func()
		args     args
		want     uint32
		wantErr  bool
	}{
		{
			name:   "common",
			fields: fields{db: db},
			initFunc: func() {
				query := fmt.Sprintf("select IFNULL(max(UNIX_TIMESTAMP(mtime)), 0) as mtime from %s", ChanceGameOpenTimeTbl)
				rows1 := sqlxmock.NewRows([]string{"max(mtime)"}).AddRow(mtime)
				mock.ExpectQuery(regexp.QuoteMeta(query)).WillReturnRows(rows1)
			},
			args: args{
				ctx: context.Background(),
			},
			want:    uint32(mtime.Unix()),
			wantErr: false,
		},
		{
			name:   "some err",
			fields: fields{db: db},
			initFunc: func() {
				query := fmt.Sprintf("select IFNULL(max(UNIX_TIMESTAMP(mtime)), 0) as mtime from %s", ChanceGameOpenTimeTbl)
				mock.ExpectQuery(regexp.QuoteMeta(query)).WillReturnError(errors.New("some err"))
			},
			args: args{
				ctx: context.Background(),
			},
			want:    0,
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &EntryMysql{
				db: tt.fields.db,
			}
			if tt.initFunc != nil {
				tt.initFunc()
			}
			got, err := c.GetChanceGameOpenTimeMaxMtime(tt.args.ctx)
			if (err != nil) != tt.wantErr {
				return
			}
			assert.Equalf(t, tt.want, got, "GetChanceGameOpenTimeMaxMtime(%v)", tt.args.ctx)
		})
	}
}

func TestEntryMysql_SetChanceGameOpenTime(t *testing.T) {
	mock, db := GetMockMysql()
	defer func() {
		_ = db.Close()
	}()

	gameType := uint32(1)
	beingTime := time.Now()
	endTime := beingTime.Add(10 * time.Second)
	opUser := "test"

	err := errors.New("error")
	query := fmt.Sprintf("insert into %s (game_type,begin_time,end_time,op_user) VALUES (?,?,?,?) ON DUPLICATE KEY UPDATE begin_time=?,end_time=?,op_user=?",
		ChanceGameOpenTimeTbl)

	type fields struct {
		db *sqlx.DB
	}
	type args struct {
		ctx       context.Context
		gameType  uint32
		beginTime time.Time
		endTime   time.Time
		opUser    string
	}
	tests := []struct {
		name     string
		fields   fields
		initFunc func()
		args     args
		wantErr  bool
	}{
		{
			name:   "common",
			fields: fields{db: db},
			initFunc: func() {
				mock.ExpectExec(regexp.QuoteMeta(query)).WithArgs(gameType, beingTime, endTime, opUser, beingTime, endTime, opUser).WillReturnResult(sqlxmock.NewResult(1, 1))
			},
			args: args{
				ctx:       context.Background(),
				gameType:  gameType,
				beginTime: beingTime,
				endTime:   endTime,
				opUser:    opUser,
			},
			wantErr: false,
		},
		{
			name:   "exec err",
			fields: fields{db: db},
			initFunc: func() {
				mock.ExpectExec(regexp.QuoteMeta(query)).WithArgs(gameType, beingTime, endTime, opUser, beingTime, endTime, opUser).WillReturnError(err)
			},
			args: args{
				ctx:       context.Background(),
				gameType:  gameType,
				beginTime: beingTime,
				endTime:   endTime,
				opUser:    opUser,
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &EntryMysql{
				db: tt.fields.db,
			}
			if tt.initFunc != nil {
				tt.initFunc()
			}
			err = c.SetChanceGameOpenTime(tt.args.ctx, tt.args.gameType, tt.args.beginTime, tt.args.endTime, tt.args.opUser)
			if (err != nil) != tt.wantErr {
				t.Errorf("SetChanceGameOpenTime() error = %v, wantErr %v", err, tt.wantErr)
			}
			//tt.wantErr(t, c.SetChanceGameOpenTime(tt.args.ctx, tt.args.gameType, tt.args.beginTime, tt.args.endTime, tt.args.opUser), fmt.Sprintf("SetChanceGameOpenTime(%v, %v, %v, %v, %v)", tt.args.ctx, tt.args.gameType, tt.args.beginTime, tt.args.endTime, tt.args.opUser))
		})
	}
}
