package main

import (
    "context"
    "crypto/tls"
    "encoding/json"
    "errors"
    "fmt"
    "github.com/go-gomail/gomail"
    "github.com/tealeg/xlsx"
    backpackBase "golang.52tt.com/clients/backpack-base"
    userPresent "golang.52tt.com/clients/userpresent"
    "golang.52tt.com/pkg/config"
    "golang.52tt.com/pkg/log"
    "golang.52tt.com/services/tt-rev/common/feishu"
    "golang.52tt.com/services/tt-rev/present-week-card/internal/store"
    //"google.golang.org/grpc"
    "io/ioutil"
    "os"
    "strconv"
    "time"
)

var presentCli *userPresent.Client
var backpackCli *backpackBase.Client

var pack2Gift = make(map[uint32]*Gift)

type Gift struct {
    Id    uint32
    Name  string
    Count uint32
    Worth uint32
    Type  string
}

type ServiceConfigT struct {
    MysqlConfig         *config.MysqlConfig `json:"mysql"`
    MailsTo             []string            `json:"mails_to"`
    MysqlReadOnlyConfig *config.MysqlConfig `json:"readonly_mysql"`
}

var st *Store

func (sc *ServiceConfigT) Parse(configFile string) (err error) {
    defer func() {
        if e := recover(); e != nil {
            err = fmt.Errorf("Failed to parse config: %v \n", e)
        }
    }()

    data, err := ioutil.ReadFile(configFile)
    if err != nil {
        return err
    }
    err = json.Unmarshal(data, &sc)
    if err != nil {
        return err
    }

    log.Infof("ServiceConfigT:MysqlConfig:%+v\n", sc.MysqlConfig)
    return
}

func main() {
    sc := &ServiceConfigT{}
    err := sc.Parse("/home/<USER>/present-week-card/present-week-card.json")
    if err != nil {
        log.Errorf("Parse fail. err:%v", err)
        return
    }

    now := time.Now()
    if len(os.Args) >= 2 {
        ts, err := strconv.ParseUint(os.Args[1], 10, 64)
        if err == nil {
            now = time.Unix(int64(ts), 0)
        } else {
            log.Errorf("ParseUint fail. %s, err:%v", os.Args[1], err)
        }
    }
    log.Infof("now:%v", now)

    monthBegin := time.Date(now.Year(), now.Month()-1, 1, 0, 0, 0, 0, time.Local)
    monthEnd := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, time.Local)
    ctx, cancel := context.WithTimeout(context.Background(), 10*time.Minute)
    defer cancel()

    st, err = NewMysql(sc.MysqlConfig, sc.MysqlReadOnlyConfig)
    if err != nil {
        log.Errorf("NewMysql fail. err:%v", err)
        return
    }

    //presentCli = userPresent.NewClient(grpc.WithBlock())
    //backpackCli, _ = backpackBase.NewClient()

    // 创建报表
    file := xlsx.NewFile()
    err = genSummarySheet(ctx, file, monthBegin, monthEnd)
    if err != nil {
        log.Errorf("genSummarySheet fail. err:%v", err)
        return
    }

    //err = genAwardSheet(ctx, file, monthBegin, monthEnd)
    //if err != nil {
    //	log.Errorf("genAwardSheet fail. err:%v", err)
    //	return
    //}

    filePath := fmt.Sprintf("/home/<USER>/present-week-card/周卡财务汇总数据_%04d%02d.xlsx", monthBegin.Year(), monthBegin.Month())
    err = file.Save(filePath)
    if err != nil {
        fmt.Printf("file save fail, err: %v\n", err)
        return
    }

    // send email
    subject := fmt.Sprintf("周卡财务数据 %s-%s", monthBegin.Format("2006-01-02 15点"), monthEnd.Format("2006-01-02 15点"))
    err = SendMail(filePath, subject, sc.MailsTo)
    if err != nil {
        fmt.Printf("Failed to SendMail %v\n", err)
        return
    }
}

/*
本月购买总可领取：status : all;  revTime: 无限制
本月已购买，已领取：status: status in (1,2) ; revTime: 无限制
本月购买，未领取：status ：status=0; revTime :  包括已过期的和尚未可领的，revTime < 下个月的第一天0时
本月购买，剩余待领取奖励价值： status = 0; revTime > 本月的最后一天。

上月的数据
上月购买，本月可领取奖励价值： status = all; revTime > 上月的最后一天。
上月购买，本月已领取的奖励价值：status in (1,2) ; revTime > 上月的最后一天
上月购买，本月未领取的奖励价值： 两个相减即可？ 或者用sql查 ：status = 0, and revTime > 上月最后一天
*/
func genSummarySheet(ctx context.Context, file *xlsx.File, monthBegin, monthEnd time.Time) error {
    tblTime := monthBegin

    lastMonthBegin := monthBegin.AddDate(0, -1, 0)
    lastMonthEnd := monthBegin

    // 本月购买周卡价值
    consumeStatMap, err := st.GetMonthConsumeStat(ctx, monthBegin)
    if err != nil {
        log.Errorf("GetMonthConsumeStat fail. err:%v", err)
        return err
    }

    // 本月购买总可领取奖励价值
    totalAwardMap, err := st.GetMonthAwardStatByStatus(ctx, false, false, tblTime, tblTime, tblTime, []uint32{})
    if err != nil {
        log.Errorf("GetMonthAwardStatByStatus fail. err:%v", err)
        return err
    }

    // 本月已领取奖励价值
    recvedAwardMap, err := st.GetCurMonthAwardStatRecved(ctx, monthEnd, tblTime)
    if err != nil {
        log.Errorf("GetMonthAwardStatByStatus fail. err:%v", err)
        return err
    }

    // 本月未领取
    notRevAwardMap, err := st.GetCurMonthAwardStatNotRecved(ctx, monthEnd, tblTime)
    if err != nil {
        log.Errorf("GetMonthAwardStatByStatus fail. err:%v", err)
        return err
    }

    // 本月未领，待领
    waitToRevMap, err := st.GetMonthAwardStatByStatus(ctx, false, true, tblTime, monthEnd, tblTime,
        []uint32{store.AwardStatusInit})
    if err != nil {
        log.Errorf("GetMonthAwardStatByStatus fail. err:%v", err)
        return err
    }

    // 上月的本月可领取奖励价值
    lastMonthAwardStatMap, err := st.GetMonthAwardStatByStatus(ctx, false, true, lastMonthBegin, lastMonthEnd, lastMonthBegin,
        []uint32{store.AwardStatusInit})
    if err != nil {
        log.Errorf("GetMonthAwardStatByStatus fail. err:%v", err)
        return err
    }

    // 上月的本月已领取奖励价值
    lastMonthRecvedAwardMap, err := st.GetMonthAwardStatRecved(ctx, lastMonthEnd, lastMonthBegin)
    if err != nil {
        log.Errorf("GetMonthAwardStatByStatus fail. err:%v", err)
        return err
    }

    // 上月的本月未领取奖励价值
    lastMonthNotRecvAwardMap, err := st.GetMonthAwardStatNotRecved(ctx, lastMonthEnd, lastMonthBegin)
    if err != nil {
        log.Errorf("GetMonthAwardStatByStatus fail. err:%v", err)
        return err
    }

    sheet, err := file.AddSheet("汇总表")
    if err != nil {
        log.Errorf("genSummarySheet fail to AddSheet. err:%v", err)
        return err
    }

    // 添加标题
    var colNames = []string{"月份", "周卡id", "周卡单价", "本月购买周卡价值", "本月购买总可领取奖励价值", "本月购买已领取奖励价值", "本月购买未领取奖励价值",
        "本月购买剩余可领取奖励价值", "上月购买本月可领取奖励价值", "上月购买本月已领取奖励价值", "上月购买本月未领取奖励价值"}

    row := sheet.AddRow()
    row.WriteSlice(&colNames, -1)

    date := monthBegin.Format("2006-01")

    for _, v := range consumeStatMap {
        row := sheet.AddRow()
        row.AddCell().SetString(date)

        row.AddCell().SetString(fmt.Sprint(v.CardId))
        row.AddCell().SetString(fmt.Sprint(v.Fee))
        row.AddCell().SetString(fmt.Sprint(v.TotalFee))

        // 本月总可领
        row.AddCell().SetString(fmt.Sprint(getTotalFeeFromAwardMap(totalAwardMap, v.CardId)))
        // 本月已领取
        row.AddCell().SetString(fmt.Sprint(getTotalFeeFromAwardMap(recvedAwardMap, v.CardId)))
        // 本月未领取
        row.AddCell().SetString(fmt.Sprint(getTotalFeeFromAwardMap(notRevAwardMap, v.CardId)))
        // 本月剩余可领取
        row.AddCell().SetString(fmt.Sprint(getTotalFeeFromAwardMap(waitToRevMap, v.CardId)))
        // 上月购买本月可领取
        row.AddCell().SetString(fmt.Sprint(getTotalFeeFromAwardMap(lastMonthAwardStatMap, v.CardId)))
        // 上月已领取
        row.AddCell().SetString(fmt.Sprint(getTotalFeeFromAwardMap(lastMonthRecvedAwardMap, v.CardId)))
        // 上月未领取
        row.AddCell().SetString(fmt.Sprint(getTotalFeeFromAwardMap(lastMonthNotRecvAwardMap, v.CardId)))
    }

    return nil
}

func getTotalFeeFromAwardMap(awardMap map[uint32]*AwardStat, cardId uint32) uint32 {
    if stat, ok := awardMap[cardId]; ok {
        return stat.TotalFee
    }

    return 0
}

func FeiShuWarn(url string) {
    lineList := make([][]*feishu.LineMem, 0)
    line := []*feishu.LineMem{
        {Tag: "text", Text: "check 项异常"},
    }
    lineList = append(lineList, line)

    line = []*feishu.LineMem{
        {Tag: "at", UserId: "6788876840648851726"},
    }
    lineList = append(lineList, line)

    feishu.SendFeiShuRichMsg(url, "周卡财务数据对账异常", lineList)
}

func genAwardSheet(ctx context.Context, file *xlsx.File, monthBegin, monthEnd time.Time) error {

    lastMonthBegin := monthBegin.AddDate(0, -1, 0)
    lastMonthEnd := monthBegin

    // 获取本月和上月购买数据
    consumeStatMap, err := st.GetMonthConsumeStat(ctx, monthBegin)
    if err != nil {
        log.Errorf("GetMonthConsumeStat fail. monthBegin:%v,err:%v", monthBegin, err)
        return err
    }
    lastConsumeStatMap, err := st.GetMonthConsumeStat(ctx, lastMonthBegin)
    if err != nil {
        log.Errorf("GetMonthConsumeStat fail. lastMonthBegin:%v, err:%v", lastMonthBegin, err)
        return err
    }

    cardInfoMap := make(map[uint32]*ConsumeStat)
    for _, v := range consumeStatMap {
        cardInfoMap[v.CardId] = v
    }
    for _, v := range lastConsumeStatMap {
        cardInfoMap[v.CardId] = v
    }

    awardRecordList, err := st.GetMonthAwardRecord(ctx, false, monthBegin, monthBegin)
    if err != nil {
        log.Errorf("GetMonthAwardRecord fail. monthBegin:%v,err:%v", monthBegin, err)
        return err
    }

    lastMonAwardList, err := st.GetMonthAwardRecord(ctx, true, lastMonthBegin, lastMonthEnd)
    if err != nil {
        log.Errorf("GetMonthAwardRecord fail. monthBegin:%v,err:%v", lastMonthBegin, err)
        return err
    }

    awardRecordList = append(awardRecordList, lastMonAwardList...)

    // 生成中奖数据报表
    name := "发奖明细表"
    sheet, err := file.AddSheet(name)
    if err != nil {
        log.Errorf("genAwardSheet fail to AddSheet. err:%v", err)
        return err
    }
    // 添加标题
    var colNames = []string{"发奖订单id", "购买订单id", "uid", "购买日期", "购买价值", "周卡id", "发奖日期", "奖品id(包裹id)", "奖品序号", "发放数量", "发放价值", "奖励状态"}

    row := sheet.AddRow()
    row.WriteSlice(&colNames, -1)

    for _, v := range awardRecordList {
        row := sheet.AddRow()
        row.AddCell().SetString(v.OrderId)
        row.AddCell().SetString(v.ConsumeOrder)
        row.AddCell().SetString(fmt.Sprint(v.Uid))
        row.AddCell().SetString(fmt.Sprint(v.CreateTime.Format("2006-01-02")))

        // 购买价值
        if card, ok := cardInfoMap[v.CardId]; ok {
            row.AddCell().SetString(fmt.Sprint(card.Fee))
        } else {
            log.Errorf("cardInfoMap not found. cardId:%v", v.CardId)
            return errors.New(fmt.Sprintf("cardInfoMap not found,cardId:%v", v.CardId))
        }

        row.AddCell().SetString(fmt.Sprint(v.CardId))
        if v.Status == store.AwardStatusInit {
            row.AddCell().SetString("-")
        } else {
            row.AddCell().SetString(fmt.Sprint(v.AwardTime.Format("2006-01-02")))
        }
        row.AddCell().SetString(fmt.Sprint(v.GiftId))
        row.AddCell().SetString(fmt.Sprint(v.SeqId))
        row.AddCell().SetString(fmt.Sprint(v.Amount))
        row.AddCell().SetString(fmt.Sprint(v.GiftWorth * v.Amount))
        row.AddCell().SetString(genStatusDescribe(v.ReceivableBegin, monthEnd, v.Status))
    }

    return nil
}

func genStatusDescribe(recvBeing, monthEnd time.Time, status uint32) string {
    switch status {
    case store.AwardStatusInit:
        if recvBeing.After(monthEnd) {
            return "待领取"
        } else {
            return "未领取"
        }
    case store.AwardStatusReceiveSuccess:
    case store.AwardStatusDone:
        return "已领取"
    default:
        return "未知"
    }
    return "未知"
}

func SendMail(filePath, subject string, to []string) error {
    m := gomail.NewMessage()
    m.SetHeader("From", "<EMAIL>")

    m.SetHeader("To", to...)
    m.SetHeader("Subject", subject)

    m.SetBody("text/html", "见附件")
    m.Attach(filePath) //附件

    d := gomail.NewDialer("mail.52tt.com", 465, "<EMAIL>", "V7.D.sTy@%bDB50t#n.r$A4h+-FnXA")

    d.TLSConfig = &tls.Config{InsecureSkipVerify: true}
    if err := d.DialAndSend(m); err != nil {
        fmt.Println(err)
        return err
    }
    return nil
}

//
//func loadGiftInfo(ctx context.Context, packId uint32) (gift *Gift, err error) {
//	cfg, err := backpackCli.GetPackageItemCfg(ctx, packId)
//	if err != nil {
//		return nil, err
//	}
//
//	// 包裹中只能有一种物品
//	if len(cfg.GetItemCfgList()) != 1 {
//		log.Errorf("loadGiftInfo fail.  packId:%d, len(items) != 1 ", packId)
//		return gift, errors.New("invalid package item type")
//	}
//
//	pkgItemCfg := cfg.GetItemCfgList()[0]
//
//	switch backpackPB.PackageItemType(pkgItemCfg.ItemType) {
//	case backpackPB.PackageItemType_BACKPACK_PRESENT:
//		presentCfg, err := presentCli.GetPresentConfigById(ctx, pkgItemCfg.SourceId)
//		if err != nil {
//			return nil, err
//		}
//		giftType := "红钻礼物"
//		if presentCfg.GetItemConfig().GetPriceType() == 2 {
//			giftType = "T豆礼物"
//		}
//		gift = &Gift{
//			Id:    presentCfg.GetItemConfig().GetItemId(),
//			Name:  presentCfg.GetItemConfig().GetName(),
//			Count: pkgItemCfg.GetItemCount(),
//			Worth: presentCfg.GetItemConfig().GetPrice(),
//			Type:  giftType,
//		}
//	//case backpackPB.PackageItemType_BACKPACK_LOTTERY_FRAGMENT:
//	//	itemCfgs, err := backpackCli.GetItemCfg(ctx,  &backpackPB.GetItemCfgReq{
//	//		ItemType:         uint32(backpackPB.PackageItemType_BACKPACK_LOTTERY_FRAGMENT),
//	//		ItemSourceIdList: []uint32{pkgItemCfg.GetSourceId()},
//	//		GetAll:           false,
//	//	})
//	//	if err != nil {
//	//		return nil, err
//	//	}
//	//	if nil == itemCfgs.ItemCfgList || 1 != len(itemCfgs.ItemCfgList) {
//	//		return nil, errors.New("invalid fragment cfg")
//	//	}
//	//
//	//	fragment := backpackPB.LotteryFragmentCfg{}
//	//	err = fragment.Unmarshal()
//	//	if err != nil {
//	//		return nil, err
//	//	}
//	//	gift = &Gift{
//	//		Id:    fragment.GetFragmentId(),
//	//		Name:  fragment.GetFragmentName(),
//	//		Count: pkgItemCfg.GetItemCount(),
//	//		Worth: fragment.GetFragmentPrice(),
//	//		Type:  "碎片",
//	//	}
//	default:
//		log.Errorf("loadGiftInfo fail.  packId:%d, invalid package item type", packId)
//		return nil, errors.New("invalid package item type")
//	}
//
//	return gift, nil
//}
