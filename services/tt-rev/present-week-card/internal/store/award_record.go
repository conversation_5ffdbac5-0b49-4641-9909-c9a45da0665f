package store

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"github.com/go-sql-driver/mysql"
	mysql1 "gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/mysql"
	"golang.52tt.com/pkg/log"
	"strings"
	"time"
)

const (
	AwardStatusInit           = uint32(iota) // 初始化
	AwardStatusReceiveSuccess                // 领取成功
	AwardStatusDone                          // 发放完成

)

var awardRecordFields = strings.Join([]string{
	"id", "order_id", "card_order_id", "uid", "card_id", "gift_id", "gift_type", "gift_worth", "is_extra_gift", "seq_id", "amount", "status",
	"receivable_begin", "receivable_end", "award_time", "create_time",
}, ",")

//// award_tbl as a
//var joinAwardRecordFields = strings.Join([]string{
//	"a.id", "a.order_id", "a.card_order_id", "a.uid", "a.card_id", "a.gift_id", "a.gift_type", "a.amount", "a.status",
//	"a.receivable_begin", "a.receivable_end", "a.outside_time", "a.create_time",
//}, ",")

var CreateAwardLogTbl = `
CREATE TABLE IF NOT EXISTS %s (  
  id int(10) NOT NULL AUTO_INCREMENT COMMENT '主键',  
  order_id varchar(255) NOT NULL DEFAULT '' COMMENT '订单ID',
  card_order_id varchar(255) NOT NULL DEFAULT '' COMMENT '周卡T豆消费记录表中的order_id',
  uid int(10) NOT NULL COMMENT '领奖人用户ID',  
  card_id int(10) NOT NULL COMMENT '周卡ID',
  gift_id varchar(255) NOT NULL COMMENT '奖品ID,包裹奖励则为packId,装扮奖励为对应装扮id',
  gift_type int(10) NOT NULL DEFAULT 0 COMMENT '奖品类型',
  gift_worth int(10) NOT NULL DEFAULT 0 COMMENT '奖励价值，T豆',
  is_extra_gift int(10) NOT NULL DEFAULT 0 COMMENT '是否是额外奖励，0-不是，1-是',
  seq_id int(10) NOT NULL DEFAULT 0 COMMENT '奖品序号',
  amount int(10) NOT NULL COMMENT '发放数量/装扮-天', 
  status int(10) NOT NULL DEFAULT 0 COMMENT '奖励状态，0-初始化，1-领取成功, 2-发放完成',
  
  receivable_begin TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '可领取开始时间', 
  receivable_end TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '可领取结束时间', 
  
  award_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '实际发放的系统时间',
  create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间,outside_time',  
  update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',  
  
  PRIMARY KEY (id),  
  UNIQUE INDEX order_id_index (order_id),
  INDEX idx_uid (uid),
  INDEX card_order_index (card_order_id),
  INDEX recv_begin_idx (receivable_begin),
  INDEX recv_end_idx (receivable_end),
  INDEX award_time_index (award_time),
  INDEX create_time_index (create_time)
  
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT "周卡奖励记录表";`

func GenAwardLogTblIdx(t time.Time) string {
	return fmt.Sprintf("present_week_card_award_%04d%02d", t.Year(), t.Month())
}

func (s *Store) CreateAwardLogTbl(ctx context.Context, tx mysql1.Txx, t time.Time) error {
	query := fmt.Sprintf(CreateAwardLogTbl, GenAwardLogTblIdx(t))
	_, err := tx.ExecContext(ctx, query)
	return err
}

type AwardRecord struct {
	Id           uint32 `db:"id"`
	OrderId      string `db:"order_id"`
	CardOrderId  string `db:"card_order_id"`
	Uid          uint32 `db:"uid"`
	CardId       uint32 `db:"card_id"`
	GiftId       string `db:"gift_id"`
	GiftType     uint32 `db:"gift_type"`
	GiftWorth    uint32 `db:"gift_worth"`
	IsExtraAward bool   `db:"is_extra_gift"`
	SeqId        uint32 `db:"seq_id"`
	Amount       uint32 `db:"amount"`
	Status       uint32 `db:"status"`

	ReceivableBegin time.Time `db:"receivable_begin"`
	ReceivableEnd   time.Time `db:"receivable_end"`

	AwardTime  time.Time `db:"award_time"`
	CreateTime time.Time `db:"create_time"`
	UpdateTime time.Time `db:"update_time"`
}

func (s *Store) BatInsertAwardRecords(ctx context.Context, tx mysql1.Txx, outsideTime time.Time, uid uint32, records []*AwardRecord) error {
	if len(records) == 0 {
		return nil
	}

	placeholder := make([]string, 0, len(records))
	params := make([]interface{}, 0)
	for _, r := range records {
		placeholder = append(placeholder, "(?,?,?,?,?,?,?,?,?,?,?,?,?,?)")
		params = append(params, r.OrderId, r.CardOrderId, uid, r.CardId, r.GiftId, r.GiftType, r.GiftWorth, r.IsExtraAward, r.SeqId, r.Amount, r.ReceivableBegin, r.ReceivableEnd,
			outsideTime, outsideTime)
	}

	query := fmt.Sprintf("INSERT INTO %s (order_id, card_order_id, uid, card_id, gift_id, gift_type,gift_worth,is_extra_gift,seq_id,amount, receivable_begin, "+
		"receivable_end, award_time, create_time) VALUES %s", GenAwardLogTblIdx(outsideTime), strings.Join(placeholder, ","))

	_, err := tx.ExecContext(ctx, query, params...)
	if err != nil {
		if mysqlErr, ok := err.(*mysql.MySQLError); ok && mysqlErr.Number == 1146 {
			// 表不存在，建表
			err = s.CreateAwardLogTbl(ctx, tx, outsideTime)
			if err != nil {
				log.ErrorWithCtx(ctx, "InsertAwardRecords fail to CreateAwardLogTbl. uid:%d, err:%v", uid, err)
				return err
			}

			_, err = tx.ExecContext(ctx, query, params...)
			if err == nil {
				return nil
			}
		}
		log.ErrorWithCtx(ctx, "InsertAwardRecords fail to insert. uid:%d, err:%v", uid, err)
		return err
	}

	return nil
}

func (s *Store) ChangeAwardRecordStatus(ctx context.Context, t, awardTime time.Time, orderIdList []string, oldStatus, status uint32) (bool, error) {
	strOrderList := make([]string, len(orderIdList))
	for i, item := range orderIdList {
		strOrderList[i] = fmt.Sprintf(`"%s"`, item)
	}

	query := fmt.Sprintf(`UPDATE %s SET status=?,award_time=? WHERE order_id in (%s) and status= ?`, GenAwardLogTblIdx(t), strings.Join(strOrderList, ","))
	r, err := s.db.ExecContext(ctx, query, status, awardTime, oldStatus)
	if err != nil {
		log.ErrorWithCtx(ctx, "ChangeAwardRecordStatus db err:%s", err.Error())
		return false, err
	}

	rowAffect, err := r.RowsAffected()
	return rowAffect > 0, err
}

// TxChangeAwardRecordStatus 事务更新奖励状态
func (s *Store) TxChangeAwardRecordStatus(ctx context.Context, tx mysql1.Txx, t, awardTime time.Time, orderIdList []string, oldStatus, status uint32) (bool, error) {
	strOrderList := make([]string, len(orderIdList))
	for i, item := range orderIdList {
		strOrderList[i] = fmt.Sprintf(`"%s"`, item)
	}

	query := fmt.Sprintf(`UPDATE %s SET status=?,award_time=? WHERE order_id in (%s) and status= ?`, GenAwardLogTblIdx(t), strings.Join(strOrderList, ","))
	r, err := tx.ExecContext(ctx, query, status, awardTime, oldStatus)
	if err != nil {
		log.ErrorWithCtx(ctx, "ChangeAwardRecordStatus db err:%s", err.Error())
		return false, err
	}

	rowAffect, err := r.RowsAffected()
	return rowAffect > 0, err
}

// GetAwardRecordByConsumeOrderId 获取用户周卡当日奖励
func (s *Store) GetAwardRecordByConsumeOrderId(ctx context.Context, uid uint32, consumeOrderId string, t time.Time) ([]*AwardRecord, error) {
	query := fmt.Sprintf(`SELECT %s FROM %s WHERE card_order_id=? and status = 0 and receivable_end > ? and receivable_begin<= ?`,
		awardRecordFields, GenAwardLogTblIdx(t))

	now := time.Now()

	infos := make([]*AwardRecord, 0)
	err := s.db.SelectContext(ctx, &infos, query, consumeOrderId, now, now)
	if err != nil {
		if mysqlErr, ok := err.(*mysql.MySQLError); ok && mysqlErr.Number == 1146 {
			// 表不存在 do nothing
			return infos, nil
		} else if errors.Is(err, sql.ErrNoRows) {
			return infos, nil

		} else {
			log.ErrorWithCtx(ctx, "GetAwardRecordByUidAndCardId db err:%s", err.Error())
			return nil, err
		}
	}
	return infos, nil
}

func (s *Store) GetAwardRecordByStatus(ctx context.Context, status, limit uint32, beginTime, endTime time.Time) ([]*AwardRecord, error) {
	query := fmt.Sprintf(`SELECT %s FROM %s WHERE status=? AND award_time>=? AND award_time<=? LIMIT ?`, awardRecordFields, GenAwardLogTblIdx(beginTime))

	infos := make([]*AwardRecord, 0)
	err := s.readOnlyDB.SelectContext(ctx, &infos, query, status, beginTime, endTime, limit)
	if err != nil {
		if mysqlErr, ok := err.(*mysql.MySQLError); ok && mysqlErr.Number == 1146 {
			// 表不存在 do nothing
			return infos, nil
		} else if errors.Is(err, sql.ErrNoRows) {
			return infos, nil
		} else {
			log.ErrorWithCtx(ctx, "GetAwardRecordByStatus db err:%s", err.Error())
			return infos, err
		}
	}
	return infos, nil
}

// BatGetAwardRecordsByCardOrderId 根据card_order_id批量获取用户的奖励记录
func (s *Store) BatGetAwardRecordsByCardOrderId(ctx context.Context, uid uint32, cardOrderIds []string) ([]*AwardRecord, error) {
	query := fmt.Sprintf(`SELECT %s FROM %s WHERE uid =? and card_order_id in (%s)`, awardRecordFields, GenAwardLogTblIdx(time.Now()),
		strings.Join(cardOrderIds, ","))

	infos := make([]*AwardRecord, 0)
	err := s.db.SelectContext(ctx, &infos, query, uid)
	if err != nil {
		if mysqlErr, ok := err.(*mysql.MySQLError); ok && mysqlErr.Number == 1146 {
			// 表不存在 do nothing
			return infos, nil
		} else if errors.Is(err, sql.ErrNoRows) {
			return infos, nil
		} else {
			log.ErrorWithCtx(ctx, "GetAwardRecordsByCardOrderId db err:%s", err.Error())
			return nil, err
		}
	}
	return infos, nil
}

// BatGetUserAwardRecords 联表根据card_order_id批量获取用户的奖励记录
func (s *Store) BatGetUserAwardRecords(ctx context.Context, tableTime, expireTime time.Time, uid uint32) ([]*AwardRecord, error) {

	// 子查询方式
	subQuery := fmt.Sprintf("select order_id as card_order_id from %s where uid = ? and expired_time >= ?", GenConsumeLogTblIdx(tableTime))
	query := fmt.Sprintf(`SELECT %s FROM %s WHERE uid = ? and card_order_id in (%s) order by card_id, seq_id`, awardRecordFields, GenAwardLogTblIdx(tableTime), subQuery)

	//// 左外连接
	//joinQuery := fmt.Sprintf("select %s, c.card_id from %s a left join (select card_id, order_id from %s where uid=? and expire_time >= ?) c on "+
	//	" a.card_order_id = c.order_id", joinAwardRecordFields, GenConsumeLogTblIdx(t), GenAwardLogTblIdx(t, uid))

	infos := make([]*AwardRecord, 0)

	err := s.db.SelectContext(ctx, &infos, query, uid, uid, expireTime)
	if err != nil {
		if mysqlErr, ok := err.(*mysql.MySQLError); ok && mysqlErr.Number == 1146 {
			// 表不存在 do nothing
			return infos, nil
		} else if errors.Is(err, sql.ErrNoRows) {
			return infos, nil
		} else {
			log.ErrorWithCtx(ctx, "GetAwardRecordsByCardOrderId db err:%s", err.Error())
			return infos, err
		}
	}

	return infos, nil
}

func (s *Store) GetAwardOrderIds(ctx context.Context, beginTime, endTime, tblTime time.Time) ([]*AwardRecord, error) {
	list := make([]*AwardRecord, 0)

	query := fmt.Sprintf("SELECT order_id,gift_id,amount FROM %s WHERE award_time >= ? AND award_time < ? AND gift_type= ? AND status= ?",
		GenAwardLogTblIdx(tblTime))

	err := s.readOnlyDB.SelectContext(ctx, &list, query, beginTime, endTime, 1, AwardStatusDone) // awardType:1 包裹礼物
	if err != nil {
		mysqlErr, ok := err.(*mysql.MySQLError)
		if !ok || mysqlErr.Number != 1146 {
			log.ErrorWithCtx(ctx, "GetAwardOrderIds fail. queryMonthTime:%v, err:%v", beginTime, err)
			return list, err
		}
	}

	return list, nil
}

func (s *Store) GetAwardTotalCountInfo(ctx context.Context, beginTime, endTime, tblTime time.Time) (*StCount, error) {
	out := &StCount{}
	temp := "SELECT COUNT(1) as count, IF(SUM(gift_worth*amount),SUM(gift_worth*amount),0) as worth FROM %s WHERE award_time >= ? AND award_time < ? " +
		"AND gift_type= ? AND status= ?"

	query := fmt.Sprintf(temp, GenAwardLogTblIdx(tblTime))
	err := s.readOnlyDB.GetContext(ctx, out, query, beginTime, endTime, 1, AwardStatusDone) // 1:包裹礼物
	if err != nil {
        if errors.Is(err, sql.ErrNoRows) || mysql1.IsMySQLError(err, 1146) {
			log.ErrorWithCtx(ctx, "GetAwardTotalCountInfo fail. queryMonthTime:%v, err:%v", beginTime, err)
			return out, err
		}
	}

	return out, nil
}
