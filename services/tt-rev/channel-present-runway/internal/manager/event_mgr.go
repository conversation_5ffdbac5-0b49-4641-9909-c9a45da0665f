package manager

import (
    "context"
    "fmt"
    kfkChannelPresentRunway "golang.52tt.com/protocol/services/minToolkit/kafka/pb/kafka_channel_present_runway"
    "time"

    "github.com/pkg/errors"
    "gitlab.ttyuyin.com/tt-infra/tyr/log"
    pb "golang.52tt.com/protocol/services/channel-present-runway"
)

// 送礼事件
func (mgr *ChannelPresentRunwayMgr) HandlePresentEvent(ctx context.Context, uid, targetUid, channelId, itemId, itemCount, totalPrice uint32, orderId string) (err error) {
	log.DebugWithCtx(ctx, "HandlePresentEvent args in: uid:%d, channelId:%d, itemId:%d, itemCount:%d, "+
		"totalPrice: %d, orderId:%v", uid, channelId, itemId, itemCount, totalPrice, orderId)

	addPresentValue := totalPrice

	var userRunway *pb.UserRunwayBrief
	userRunway, err = mgr.GetUserRunway(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "HandlePresentEvent GetUserRunway fail, uid: %d, channelId: %d, totalPrice: %d, "+
			"orderId:%v err: %v", uid, channelId, totalPrice, orderId, err)
		return err
	}
	log.DebugWithCtx(ctx, "HandlePresentEvent uid:%d,cid:%d GetUserRunway: %+v", uid, channelId, userRunway)

	if userRunway.GetRunwayLevel() == 0 {

		nowTimeMicroS := uint64(time.Now().UnixNano() / 1000)
		err = mgr.cacheRunway.AddCprUSerPresentRecord(ctx, uid, totalPrice, nowTimeMicroS)
		if err != nil {
			log.ErrorWithCtx(ctx, "HandlePresentEvent AddCprUSerPresentRecord fail, uid:%d, cid:%d,totalPrice: %d, orderId:%d",
				uid, channelId, totalPrice, orderId)
		}

		validBeginTime := nowTimeMicroS - uint64(mgr.sc.GetChangeRule().ReadySecs*1000000)
		totalPresentValue, err := mgr.cacheRunway.GetCprUserTotalPresentValue(ctx, uid, totalPrice, validBeginTime, nowTimeMicroS)
		if err != nil {
			log.ErrorWithCtx(ctx, "HandlePresentEvent GetCprUserTotalPresentValue-redis, uid: %d, totalPrice: %d,"+
				" orderId:%v,validBeginTime: %v,nowTimeMicroS:%v,err:%v", uid, totalPrice, orderId, validBeginTime, nowTimeMicroS, err)
			return err
		}

		minRunwayValue := mgr.sc.GetRunwayLevelCfgList()[0].BeginValue
		if totalPresentValue < minRunwayValue {
			return nil
		}

		addPresentValue = totalPresentValue
		userRunway = &pb.UserRunwayBrief{}
		userRunway.Uid = uid
	}

	nowTimeS := uint32(time.Now().Unix())
	err = mgr.CheckRunwayChangeEvent(ctx, channelId, userRunway, addPresentValue, nowTimeS, itemId, totalPrice, targetUid, orderId)
	if err != nil {
		log.ErrorWithCtx(ctx, "CheckRunwayChangeEvent fail, uid:%d, cid:%d, totalPrice:%d, orderId:%v, err: %v",
			uid, channelId, totalPrice, orderId, err)
	}

	return err
}

// 进房事件
func (mgr *ChannelPresentRunwayMgr) OnUserEnterChannelEvent(ctx context.Context, userId, channelId uint32) (err error) {
	var userRunway *pb.UserRunwayBrief
	userRunway, err = mgr.cacheRunway.GetUserRunwayInfo(ctx, userId)
	if err != nil {
		log.ErrorWithCtx(ctx, "OnUserEnterChannelEvent GetUserRunwayInfo fail,err:%+v,userRunway:%+v", err, userRunway)
		return err
	}
	log.DebugWithCtx(ctx, "OnUserEnterChannelEvent GetUserRunwayInfo:%+v", userRunway)
	if userRunway == nil || userRunway.GetRunwayLevel() == 0 {
		return nil
	}

	//ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	//defer cancel()

	nobleInfo, err := mgr.nobilityCli.GetNobilityInfo(ctx, userId, true)
	if err != nil {
		log.ErrorWithCtx(ctx, "nobilityCli.GetNobilityInfo Error, uid:%d,cid:%d err:%v", userId, channelId, err)
		return nil
	}
	if nobleInfo != nil && nobleInfo.GetInvisible() {
		log.InfoWithCtx(ctx, "uid:%d,cid:%d is a nobility and in a invisible mode.", userId, channelId)
		return nil
	}

	err = mgr.UpdateUserChannel(ctx, userId, channelId, 0, userRunway.GetRunwayLevel())
	if err != nil {
		log.ErrorWithCtx(ctx, "UpdateUserChannel fail, uid:%d,cid:%d err: %v", userId, channelId, err)
		return err
	}

	userRunwayCfg := mgr.sc.GetRunwayLevelInfoByLevel(userRunway.GetRunwayLevel())
	if userRunwayCfg == nil {
		userRunwayCfg = &pb.RunwayConfig{}
	}

	err = mgr.AddRocketEvent(ctx, RocketReappear, channelId, 0, 0, 0, 0, userRunway, userRunwayCfg)
	if err != nil {
		log.Errorf("OnUserEnterChannelEvent fail at AddRocketEvent, err: %v", err)
		return err
	}

	return err
}

// 退房事件
func (mgr *ChannelPresentRunwayMgr) OnUserLeaveChannelEvent(ctx context.Context, userId, channelId uint32) (err error) {
	var userRunway *pb.UserRunwayBrief
	userRunway, err = mgr.cacheRunway.GetUserRunwayInfo(ctx, userId)
	if err != nil {
		log.ErrorWithCtx(ctx, "OnUserLeaveChannelEvent GetUserRunwayInfo fail,err:%v,cid:%d,uid:%d userRunway:%+v", err, channelId, userId, userRunway)
		return err
	}
	if userRunway == nil || userRunway.GetRunwayLevel() == 0 {
		return nil
	}

	err = mgr.UpdateUserChannel(ctx, userId, 0, channelId, userRunway.GetRunwayLevel())
	if err != nil {
		log.ErrorWithCtx(ctx, "UpdateUserChannel fail,uid:%d.cid:%d err: %v", userId, channelId, err)
		return err
	}

	userRunwayCfg := mgr.sc.GetRunwayLevelInfoByLevel(userRunway.GetRunwayLevel())
	if userRunwayCfg == nil {
		userRunwayCfg = &pb.RunwayConfig{}
	}

	err = mgr.AddRocketEvent(ctx, RocketDismiss, channelId, 0, 0, 0, 0, userRunway, userRunwayCfg)
	if err != nil {
		log.ErrorWithCtx(ctx, "OnUserEnterChannelEvent fail at AddRocketEvent, uid:%d,cid:%d err: %v", userId, channelId, err)
		return err
	}

	// 退房删除神秘人缓存信息, 多判断一次当前是否在房防止进退房时间消费次序不当导致清楚在房用户缓存
	if curCid, _ := mgr.channelOlCli.GetUsersChannelId(ctx, userId, userId); curCid.GetChannelId() == 0 {
		_ = mgr.cacheRunway.DelUkwUserRunwayFakeUid([]uint32{userId})
	}

	return err
}

// 获取用户跑道信息
func (mgr *ChannelPresentRunwayMgr) GetUserRunway(ctx context.Context, userId uint32) (*pb.UserRunwayBrief, error) {
	runwayNil := &pb.UserRunwayBrief{}

	runwayInfo, err := mgr.cacheRunway.GetUserRunwayInfo(ctx, userId)
	if err != nil {
		return runwayNil, err
	}

	// 检查跑道是否过期
	expiredFlag, err := mgr.CheckRunwayExpired(userId, runwayInfo, 0)
	if err != nil {
		return runwayNil, err
	}
	if expiredFlag {
		runwayInfo = &pb.UserRunwayBrief{}
		log.Debugf("CheckRunwayExpired runway is expired,userId: %d, del from redis.", userId)
	}

	return runwayInfo, err
}

// 检查跑道是否过期
func (mgr *ChannelPresentRunwayMgr) CheckRunwayExpired(userId uint32, userRunway *pb.UserRunwayBrief, maxTimeS uint32) (bool, error) {
	if 0 == userRunway.GetRunwayLevel() {
		return false, nil
	}

	// 如果maxTimeS不为0，说明expiredTime已经过期了，那么就一定要清理其他的相关数据
	if maxTimeS == 0 {
		maxTimeS = uint32(time.Now().Unix())
		log.Debugf("userRunway.GetExpiredTime():%v", userRunway.GetExpiredTime())
		if userRunway.GetExpiredTime() == 0 || userRunway.GetExpiredTime() > (maxTimeS-mgr.sc.GetChangeRule().BonusSecs) {
			// 未过期
			return false, nil
		}
	}

	// 已过期，清理相关数据
	err := mgr.cacheRunway.DelUserRunway(userId)
	if err != nil {
		log.Errorf("DelUserRunway fail, userId: %d, err: %v", userId, err)
		return true, err
	}

	userIdList := make([]uint32, 0)
	userIdList = append(userIdList, userId)
	channelIdList, err := mgr.cacheRunway.GetUserChannelIdList(userIdList)
	if err != nil {
		log.Errorf("GetUserChannelIdList fail, userId: %d, err: %v", userId, err)
		return true, nil
	}
	if len(channelIdList) == 0 {
		return true, nil
	}
	channelId := channelIdList[0]

	err = mgr.cacheRunway.DelChannelUser(channelId, userIdList)
	if err != nil {
		log.Errorf("DelChannelUser fail, userId: %d, err: %v", userId, err)
		return true, nil
	}

	err = mgr.cacheRunway.DelCprUserPresentRecord(userId)
	if err != nil {
		log.Errorf("DelCprUserPresentRecord fail, userId: %d, err: %v", userId, err)
		return true, nil
	}

	return true, err
}

// 火箭跑道变化Event
func (mgr *ChannelPresentRunwayMgr) CheckRunwayChangeEvent(ctx context.Context, channelId uint32,
	userRunway *pb.UserRunwayBrief, addPresentValue, nowTimeS, itemId, totalPrice, targetUid uint32, orderId string) (err error) {

	uid := userRunway.GetUid()

	newRunwayValue := userRunway.GetCurValue() + addPresentValue
	newRunwayLevelCfg := mgr.sc.GetRunwayLevel(newRunwayValue)
	log.DebugWithCtx(ctx, "CheckRunwayChangeEvent newRunwayValue:%v, newRunwayLevel:%+v,uid:%d, cid:%d", newRunwayValue, newRunwayLevelCfg, userRunway.GetUid(), channelId)

	if newRunwayLevelCfg == nil || newRunwayLevelCfg.Level == 0 {
		log.ErrorWithCtx(ctx, "CheckRunwayChangeEvent at a wrong level,newRunwayValue = %d, uid:%d, cid:%d", newRunwayValue, userRunway.GetUid(), channelId)
		err = errors.New("火箭跑道等级异常")
		return err
	}

	preRunwayLevel := userRunway.GetRunwayLevel()
	// 1.开启新跑道
	if preRunwayLevel == 0 {
		log.Debugf("uid:%d,cid:%d开启新跑道。", userRunway.GetUid(), channelId)
		userRunway.StartTime = nowTimeS
		userRunway.ExpiredTime = nowTimeS + mgr.sc.GetChangeRule().CountDownSecs
		userRunway.RunwayLevel = newRunwayLevelCfg.GetLevel()
		userRunway.CurValue = newRunwayValue

		err = mgr.cacheRunway.DelCprUserPresentRecord(userRunway.GetUid())
		if err != nil {
			log.ErrorWithCtx(ctx, "DelCprUserPresentRecord fail, userId: %d, err: %v", uid, err)
			return err
		}

		err = mgr.UpdateUserChannel(ctx, userRunway.GetUid(), channelId, 0, userRunway.GetRunwayLevel())
		if err != nil {
			log.ErrorWithCtx(ctx, "UpdateUserChannel fail, uid:%d, cid:%d,err: %v", uid, channelId, err)
			return err
		}
	}

	// 单次送礼值达到重置当前阶段倒计时
	if addPresentValue >= mgr.sc.GetChangeRule().ExpiredResetLmt {
		userRunway.ExpiredTime = nowTimeS + mgr.sc.GetChangeRule().CountDownSecs
	}

	// 2.未升级
	if preRunwayLevel == newRunwayLevelCfg.GetLevel() {
		log.DebugWithCtx(ctx, "uid:%d, cid:%d, 跑道值变化。", uid, channelId)
		userRunway.CurValue = userRunway.GetCurValue() + addPresentValue

		err = mgr.SetUserRunwayInfo(ctx, userRunway.GetUid(), userRunway)
		if err != nil {
			log.ErrorWithCtx(ctx, "SetUserRunway fail, uid:%d, cid:%d, err: %v", uid, channelId, err)
			return err
		}

		err = mgr.AddRocketEvent(ctx, RocketValueChange, channelId, itemId, addPresentValue, 0, 0, userRunway, newRunwayLevelCfg)
		if err != nil {
			log.ErrorWithCtx(ctx, "CheckRunwayChangeEvent fail at AddRocketEvent,uid:%d,cid:%d, err: %v", uid, channelId, err)
			return err
		}

		return err
	}

	// 获取最高等级配置
	maxLevelCfg := mgr.sc.GetMaxLevelCfg()
	maxLevelValue := maxLevelCfg.GetBeginValue()

	var launchTimes uint32
	var beforeLaunchTimes uint32
	launchRunwayInfo := &pb.UserRunwayBrief{}

	// 火箭升空后，用户的跑道等级
	levelCfgAfterLaunch := mgr.sc.GetRunwayLevelInfoByLevel(mgr.sc.GetChangeRule().LaunchBonusLevel)
	if levelCfgAfterLaunch == nil {
		levelCfgAfterLaunch = &pb.RunwayConfig{}
		log.ErrorWithCtx(ctx, "CheckRunwayChangeEvent After a rocket launch, return to no level,init it to 0 level.uid:%d,cid:%d", uid, channelId)
	}
	BonusValueAftLaunch := levelCfgAfterLaunch.GetBeginValue()
	log.DebugWithCtx(ctx, "CheckRunwayChangeEvent levelCfgAfterLaunch.BeginValue:%v, uid:%d,cid%d", BonusValueAftLaunch, uid, channelId)

	if newRunwayValue < maxLevelValue {
		// 3.等级变化，未达到升空等级
		if preRunwayLevel != 0 {
			userRunway.CurValue = userRunway.GetCurValue() + addPresentValue
			userRunway.RunwayLevel = newRunwayLevelCfg.GetLevel()
		}
	} else {
		// 4.最高等级，火箭升空
		launchRunwayInfo = &pb.UserRunwayBrief{
			Uid:             userRunway.GetUid(),
			CurValue:        maxLevelValue,
			RunwayLevel:     maxLevelCfg.GetLevel(),
			ExpiredTime:     userRunway.GetExpiredTime(),
			StartTime:       userRunway.GetStartTime(),
			ContLaunchTimes: 0,
		}

		remainValue := newRunwayValue
		for {
			if remainValue < maxLevelValue {
				break
			}
			launchTimes = launchTimes + 1
			remainValue = remainValue - maxLevelValue
			remainValue = remainValue + BonusValueAftLaunch
		}

		// 火箭升空后新的火箭跑道
		userRunway.CurValue = remainValue
		userRunway.StartTime = nowTimeS

		beforeLaunchTimes = userRunway.GetContLaunchTimes()
		userRunway.ContLaunchTimes = userRunway.GetContLaunchTimes() + launchTimes
		log.InfoWithCtx(ctx, "after the runway launch, start a new trip：%+v, uid:%d.cid:%d", userRunway, uid, channelId)
	}

	// 获取当前火箭跑道配置
	userRunwayCfg := mgr.sc.GetRunwayLevel(userRunway.GetCurValue())
	if userRunwayCfg == nil {
		userRunwayCfg = &pb.RunwayConfig{}
	}
	userRunway.RunwayLevel = userRunwayCfg.GetLevel()

	err = mgr.SetUserRunwayInfo(ctx, userRunway.GetUid(), userRunway)
	if err != nil {
		log.ErrorWithCtx(ctx, "SetUserRunway fail, uid:%d,cid:%d,err: %v", uid, channelId, err)
		return err
	}

	rocketEvent := &kfkChannelPresentRunway.ChannelPresentRocketEvent{
		LaunchTime:  time.Now().Unix(),
		Uid:         uid,
		TargetUid:   targetUid,
		ChannelId:   channelId,
		ChannelType: 0,
		ItemId:      itemId,
	}
	channelInfo, err := mgr.channelCli.GetChannelSimpleInfo(ctx, uid, channelId)
	if err != nil {
		log.WarnWithCtx(ctx, "GetChannelSimpleInfo fail, uid:%d,cid:%d,err: %v", uid, channelId, err)
	} else {
		rocketEvent.ChannelType = channelInfo.GetChannelType()
	}

	//以下三个火箭推送事件非互斥
	if preRunwayLevel == 0 {
		err = mgr.AddRocketEvent(ctx, RocketStart, channelId, itemId, totalPrice, 0, launchTimes, userRunway, userRunwayCfg)
		if err != nil {
			log.ErrorWithCtx(ctx, "CheckRunwayChangeEvent [RocketStart] fail at AddRocketEvent, uid:%d,cid:%d,err: %v", uid, channelId, err)
			return err
		}
		log.InfoWithCtx(ctx, "CheckRunwayChangeEvent uid:%d,cid:%d start a new trip.", uid, channelId)
	}

	if launchTimes > 0 {
		err = mgr.AddRocketEvent(ctx, RocketLaunch, channelId, itemId, totalPrice, beforeLaunchTimes, launchTimes, launchRunwayInfo, maxLevelCfg)
		if err != nil {
			log.ErrorWithCtx(ctx, "CheckRunwayChangeEvent [RocketLaunch] fail at AddRocketEvent,uid:%d,cid:%d, err: %v", uid, channelId, err)
			return err
		}
		// 火箭升空，使用等级5
		rocketEvent.RocketRank = 5
		rocketEvent.OrderId = fmt.Sprintf("%s_%s", orderId, "launch")
		mgr.channelPresentRocketProducer.SendMsg(ctx, rocketEvent)
		log.InfoWithCtx(ctx, "CheckRunwayChangeEvent uid:%d,cid:%d launch a rocket.", uid, channelId)
	}

	err = mgr.AddRocketEvent(ctx, RocketValueChange, channelId, itemId, totalPrice, beforeLaunchTimes, launchTimes, userRunway, userRunwayCfg)
	if err != nil {
		log.ErrorWithCtx(ctx, "CheckRunwayChangeEvent [RocketValueChange] fail at AddRocketEvent,uid:%d,cid:%d, err: %v", uid, channelId, err)
		return err
	}

	rocketEvent.RocketRank = userRunway.GetRunwayLevel()
	rocketEvent.OrderId = fmt.Sprintf("%s_%s", orderId, "rankUp")
	mgr.channelPresentRocketProducer.SendMsg(ctx, rocketEvent)
	log.InfoWithCtx(ctx, "CheckRunwayChangeEvent uid:%d,cid:%d rocket value change. rank:%d", uid, channelId, userRunway.GetRunwayLevel())

	return err
}

// redis更新进退房事件 及 跑道用户与房间关联关系
func (mgr *ChannelPresentRunwayMgr) UpdateUserChannel(ctx context.Context, userId, newChannelId, oldChannelId, runwayLevel uint32) error {

	userIdList := make([]uint32, 0)
	userIdList = append(userIdList, userId)

	curChannelId := uint32(0)
	if newChannelId == 0 {
		curChannelIdList, err := mgr.cacheRunway.GetUserChannelIdList(userIdList)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetUserChannelIdList fail, uid:%d, err:%v", userId, err)
			return err
		}
		if len(curChannelIdList) > 0 {
			curChannelId = curChannelIdList[0]
		}
	}

	// enterEvent or Start a newRunway
	if newChannelId > 0 && runwayLevel > 0 {
		err := mgr.cacheRunway.AddChannelUserWithRunway(ctx, newChannelId, userId)
		if err != nil {
			log.ErrorWithCtx(ctx, "AddChannelUserWithRunway fail, uid:%d, newCid:%d, err:%v", userId, newChannelId, err)
			return err
		}

		// 有火箭则建立神秘人信息缓存, 避免退房时候神秘人关闭获取不到神秘人信息
		userProfile, _ := mgr.userProfileCli.GetUserProfileV2(ctx, userId, true)
		err = mgr.cacheRunway.SetUkwUserRunwayFakeUid(userId, userProfile)
		if err != nil {
			log.ErrorWithCtx(ctx, "SetUkwUserRunwayFakeUid fail, uid: %d, newCid: %d, err: %v", userId, newChannelId, err)
		}
	}

	// LeaveEvent
	if oldChannelId > 0 {
		err := mgr.cacheRunway.DelChannelUser(oldChannelId, userIdList)
		if err != nil {
			log.ErrorWithCtx(ctx, "DelChannelUser fail, uid:%d, oldCid:%d, err:%v", userId, newChannelId, err)
			return err
		}
	}

	// 用户从房间A进房间B时，A的退房事件可能在B的进房事件后面，退房时需要检查下(kafka消费问题)
	if newChannelId > 0 || (newChannelId == 0 && oldChannelId == curChannelId) {
		err := mgr.cacheRunway.SetUserChannelId(userId, newChannelId)
		if err != nil {
			log.ErrorWithCtx(ctx, "SetUserChannelId fail, uid:%d, newCid:%d, err:%v", userId, newChannelId, err)
			return err
		}
	}
	return nil
}

// 订单号排重
func (mgr *ChannelPresentRunwayMgr) CheckOrderId(ctx context.Context, uid, cid uint32, orderId string) (error, bool) {
	err, ok := mgr.cacheOrderId.CheckOrderId(ctx, orderId)
	if err != nil {
		log.ErrorWithCtx(ctx, "CheckOrderId fail,err:%v,orderId:%v,uid:%d,cid:%d", err, orderId, uid, cid)
		return err, false
	}
	return err, ok
}

// 火箭跑道变化
func (mgr *ChannelPresentRunwayMgr) SetUserRunwayInfo(ctx context.Context, userId uint32, runway *pb.UserRunwayBrief) error {

	err := mgr.cacheRunway.SetUserRunway(ctx, userId, runway)
	if err != nil {
		log.ErrorWithCtx(ctx, "SetUserRunway fail,uid:%d err: %v", userId, err)
		return err
	}

	// 火箭跑道过期时间入库
	err = mgr.cacheRunway.SetRunwayExpiredTime(ctx, userId, runway.ExpiredTime)
	if err != nil {
		log.ErrorWithCtx(ctx, "SetRunwayExpiredTime fail, err: %v, uid:%d, expiredTime:%d", err, userId, runway.ExpiredTime)
		return err
	}

	return nil
}

// PopExpiredRunwayList 过期火箭跑道清理
func (mgr *ChannelPresentRunwayMgr) PopExpiredRunwayList() {

	// 允许有误差BonusSeconds
	maxTimeS := uint32(time.Now().Unix()) - mgr.sc.GetChangeRule().BonusSecs

	userIdList, err := mgr.cacheRunway.PopExpiredRunwayTimeList(maxTimeS, 200)
	if err != nil {
		log.Errorf("PopExpiredTimeUidList fail at redis.PopExpiredRunwayList, err: %v, maxTimeS:%d", err, maxTimeS)
		return
	}
	if len(userIdList) == 0 {
		return
	}

	for _, uid := range userIdList {
		_, err := mgr.GetUserRunway(context.Background(), uid)
		if err != nil {
			log.Errorf("PopExpiredRunwayList GetUserRunway error.uid:%d, err:%v", uid, err)
			return
		}
	}
}

// 神秘人现身
func (mgr *ChannelPresentRunwayMgr) UKWAppearReal(ctx context.Context, uid uint32) error {
	userRunway, err := mgr.cacheRunway.GetUserRunwayInfo(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "OnUserEnterChannelEvent GetUserRunwayInfo fail,err:%+v,userRunway:%+v", err, userRunway)
		return err
	}
	if userRunway == nil || userRunway.GetRunwayLevel() == 0 {
		return nil
	}
	log.DebugWithCtx(ctx, "UKWAppearReal GetUserRunwayInfo:%+v", userRunway)
	userRunwayCfg := mgr.sc.GetRunwayLevelInfoByLevel(userRunway.GetRunwayLevel())
	if userRunwayCfg == nil {
		userRunwayCfg = &pb.RunwayConfig{}
	}

	cidResp, err := mgr.channelOlCli.GetUsersChannelId(ctx, uid, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "UKWAppearReal GetUsersChannelId error: %v, uid: %d", err, uid)
		return err
	}
	channelId := cidResp.GetChannelId()
	if channelId == 0 {
		return nil
	}

	err = mgr.AddRocketEvent(ctx, RocketDismiss, channelId, 0, 0, 0, 0, userRunway, userRunwayCfg) // 移除神秘人火箭
	if err != nil {
		log.ErrorWithCtx(ctx, "UKWAppearReal AddRocketEvent RocketDismiss error: %v, channelId: %d, uid: %d", err, channelId, uid)
		return err
	}
	err = mgr.cacheRunway.DelUkwUserRunwayFakeUid([]uint32{userRunway.GetUid()})
	if err != nil {
		log.ErrorWithCtx(ctx, "UKWAppearReal DelUkwUserRunwayFakeUid error: %v, channelId: %d, uid: %d", err, channelId, uid)
		return err
	}
	err = mgr.AddRocketEvent(ctx, RocketReappear, channelId, 0, 0, 0, 0, userRunway, userRunwayCfg) // 真实身份火箭再现
	if err != nil {
		log.ErrorWithCtx(ctx, "UKWAppearReal AddRocketEvent RocketReappear error: %v, channelId: %d, uid: %d", err, channelId, uid)
		return err
	}
	return nil
}
