package conf

import (
    "encoding/json"
    "fmt"
    "io/ioutil"

    "golang.52tt.com/pkg/config"
    mysqlConnect "gitlab.ttyuyin.com/avengers/tyr/core/middleware/mysql/connect"
    redisConnect "gitlab.ttyuyin.com/avengers/tyr/core/middleware/redis/connect"
)

// StartConfig 服务器配置.
type StartConfig struct {
    Environment            string                    `json:"environment"`
    Addr                   string                    `json:"addr"`
    MysqlConfig            *mysqlConnect.MysqlConfig `json:"mysql_config"`
    RedisConfig            *redisConnect.RedisConfig `json:"redis_config"`
    PresentKFK             *config.KafkaConfig       `json:"present_kafka"`             //present_kafka
    UKWChangeKFK           *config.KafkaConfig       `json:"ukw_change_kafka"`          //神秘人变更 kafka
    MicChangeKFK           *config.KafkaConfig       `json:"mic_change_kafka"`          //mic_change_kafka kafka
    PkEventKFK             *config.KafkaConfig       `json:"pk_event_kafka"`            //pk 送礼&开始结束事件 kafka
    SwitchChannelSchemeKFK *config.KafkaConfig       `json:"switch_channel_scheme"`     // 房间模式变更事件
    ChannelLiveStatusKFK   *config.KafkaConfig       `json:"channel_live_status_kafka"` // 开播状态变更事件
}

// IsProduction 是否正式环境
func (sc *StartConfig) IsProduction() bool {
    return sc.Environment == "production"
}

func (sc *StartConfig) Parse(configFile string) (err error) {
    defer func() {
        if e := recover(); e != nil {
            err = fmt.Errorf("Failed to parse config: %v \n", e)
        }
    }()

    data, err := ioutil.ReadFile(configFile)
    if err != nil {
        return err
    }
    err = json.Unmarshal(data, &sc)
    if err != nil {
        return err
    }
    return
}

func (sc *StartConfig) GetRedisConfig() *redisConnect.RedisConfig {
    if sc.RedisConfig == nil {
        return &redisConnect.RedisConfig{}
    }

    return sc.RedisConfig
}

func (sc *StartConfig) GetMysqlConfig() *mysqlConnect.MysqlConfig {
    if sc.MysqlConfig == nil {
        return &mysqlConnect.MysqlConfig{}
    }

    return sc.MysqlConfig
}
