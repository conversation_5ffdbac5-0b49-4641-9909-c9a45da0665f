package mgr

import (
	"context"
	"fmt"
	"reflect"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	protogrpc "golang.52tt.com/pkg/protocol/grpc"
	pbApp "golang.52tt.com/protocol/app"
	channelSchemePb "golang.52tt.com/protocol/services/channel-scheme"
	channel_scheme_conf_mgr "golang.52tt.com/protocol/services/channel-scheme-conf-mgr"
	channelmicPB "golang.52tt.com/protocol/services/channelmicsvr"
	channelsvr "golang.52tt.com/protocol/services/channelsvr"
	entertainmentRecommendBack "golang.52tt.com/protocol/services/entertainmentRecommendBackSvr"
	guildpb "golang.52tt.com/protocol/services/guildsvr"
	masked_pk_svr "golang.52tt.com/protocol/services/masked-pk-svr"
	pgcchannelgamepb "golang.52tt.com/protocol/services/pgc-channel-game"
	pgc_channel_pk "golang.52tt.com/protocol/services/pgc-channel-pk"
)

var (
	uid         = uint32(2465835)
	ctx         = protogrpc.WithServiceInfo(context.TODO(), &protogrpc.ServiceInfo{UserID: uid})
	channelId   = uint32(2255334)
	toChannelId = uint32(2013681)
	guildId     = uint32(153380)
)

func TestMgr_GetPKChannelList(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	type args struct {
		ctx       context.Context
		channelId uint32
		guildId   uint32
	}
	tests := []struct {
		name    string
		args    args
		mock    func()
		want    []*pgc_channel_pk.PgcChannelPKChannelInfo
		want1   uint32
		wantErr bool
	}{
		{
			args: args{
				ctx:       ctx,
				channelId: channelId,
				guildId:   guildId,
			},
			mock: func() {
				tmp := "test"
				mockchannelGuildCli.EXPECT().GetChannelGuildList(gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes().Return([]uint32{channelId}, nil)
				mockchannelStatsCli.EXPECT().BatchGetChannelHotValue(gomock.Any(), gomock.Any()).AnyTimes().Return(map[uint32]int64{channelId: 100}, nil)
				mockentertaimentCli.EXPECT().BatchGetChannelTag(gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes().Return(&entertainmentRecommendBack.BatchGetChannelTagResp{
					ChannelTagList: []*entertainmentRecommendBack.ChannelTagConfigInfo{
						{
							Name:       &tmp,
							MultiColor: []string{"1", "2"},
						},
					},
				}, nil)
				mockchannelCli.EXPECT().BatchGetChannelSimpleInfo(gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes().Return(map[uint32]*channelsvr.ChannelSimpleInfo{
					channelId: {
						ChannelId: &channelId,
						DisplayId: &channelId,
						Name:      &tmp,
					},
				}, nil)
				mockGuildCli.EXPECT().GetGuild(gomock.Any(), gomock.Any()).Return(&guildpb.GuildResp{GuildId: guildId}, nil)
				mockcache.EXPECT().GetChannelPKSwitchMap(gomock.Any(), gomock.Any()).Return(map[uint32]uint32{channelId: 0}, nil)
				mockuserProfileCli.EXPECT().BatchGetUserProfile(gomock.Any(), gomock.Any()).Return(map[uint32]*pbApp.UserProfile{
					uid: {},
				}, nil)
				mockchannelSchemeCli.EXPECT().GetCurChannelSchemeInfo(gomock.Any(), gomock.Any(), gomock.Any()).Return(&channelSchemePb.GetCurChannelSchemeInfoResp{
					Cid: channelId,
					SchemeInfo: &channelSchemePb.ChannelSchemeInfo{
						SchemeSvrDetailType: uint32(channel_scheme_conf_mgr.SchemeSvrDetailType_SCHEME_SVR_DETAIL_TYPE_FUN),
					},
				}, nil).AnyTimes()
				bc.EXPECT().GetPKWhiteList().Return([]uint32{}).AnyTimes()
				mockIsApplicableChannel()
				mockTTRevOperationCli.EXPECT().HasChannelPKPerm(gomock.Any(), gomock.Any()).Return(map[uint32]bool{channelId: true}, nil)
			},
			want:    make([]*pgc_channel_pk.PgcChannelPKChannelInfo, 0, 8),
			want1:   guildId,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mgr := NewTestMgr(ctrl)
			if tt.mock != nil {
				tt.mock()
			}
			got, _, got1, err := mgr.GetPKChannelList(tt.args.ctx, tt.args.channelId, tt.args.guildId)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetPKChannelList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetPKChannelList() got = %v, want %v", got, tt.want)
			}
			if got1 != tt.want1 {
				t.Errorf("GetPKChannelList() got1 = %v, want %v", got1, tt.want1)
			}
		})
	}
}

func TestMgr_GetPKEntry(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	type args struct {
		ctx       context.Context
		channelId uint32
	}
	tests := []struct {
		name    string
		args    args
		mock    func()
		want    bool
		wantErr bool
	}{
		{
			args: args{
				ctx:       ctx,
				channelId: channelId,
			},
			mock: func() {
				bc.EXPECT().GetPKWhiteList().Return([]uint32{}).AnyTimes()
				bc.EXPECT().GetPKEntry().Return(true).AnyTimes()
				mockchannelSchemeCli.EXPECT().GetCurChannelSchemeInfo(gomock.Any(), gomock.Any(), gomock.Any()).Return(&channelSchemePb.GetCurChannelSchemeInfoResp{
					Cid: channelId,
					SchemeInfo: &channelSchemePb.ChannelSchemeInfo{
						SchemeSvrDetailType: uint32(channel_scheme_conf_mgr.SchemeSvrDetailType_SCHEME_SVR_DETAIL_TYPE_FUN),
					},
				}, nil).AnyTimes()
				mockchannelMicCli.EXPECT().GetMicrList(gomock.Any(), gomock.Any(), gomock.Any()).Return(&channelmicPB.GetMicrListResp{
					ChannelId: channelId,
					AllMicList: []*channelmicPB.MicrSpaceInfo{
						{
							MicId:  1,
							MicUid: uid,
						},
					},
					MicrMode:     0,
					ServerTimeMs: 0,
				}, nil).AnyTimes()
				role := uint32(channelsvr.ChannelAdminRole_CHANNEL_OWNER)
				mockchannelCli.EXPECT().GetChannelAdmin(gomock.Any(), gomock.Any(), gomock.Any()).Return([]*channelsvr.ChannelAdmin{
					{
						Uid:       &uid,
						AdminRole: &role,
					},
				}, nil).AnyTimes()
				mockTTRevOperationCli.EXPECT().HasChannelPKPerm(gomock.Any(), gomock.Any()).Return(map[uint32]bool{channelId: true}, nil)
			},
			want:    true,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mgr := NewTestMgr(ctrl)
			if tt.mock != nil {
				tt.mock()
			}
			got, err := mgr.GetPKEntry(tt.args.ctx, tt.args.channelId)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetPKEntry() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("GetPKEntry() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestMgr_GetPKSwitch(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	mgr := NewTestMgr(ctrl)

	type args struct {
		ctx       context.Context
		channelId uint32
	}
	tests := []struct {
		name    string
		args    args
		want    uint32
		wantErr bool
	}{
		{
			args: args{
				ctx:       ctx,
				channelId: channelId,
			},
			want:    0,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {

			gomock.InOrder(
				mockcache.EXPECT().GetChannelPKSwitch(gomock.Any(), gomock.Any()).AnyTimes().Return(uint32(0), nil),
			)
			got, err := mgr.GetPKSwitch(tt.args.ctx, tt.args.channelId)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetPKSwitch() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("GetPKSwitch() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestMgr_GetPKTimeLimitToast(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	tests := []struct {
		name string
		want string
	}{
		{
			want: fmt.Sprintf("注意：每厅每天%02d:%02d-%02d:%02d期间仅可pk2次", 20, 0, 22, 0),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mgr := NewTestMgr(ctrl)

			bc.EXPECT().GetLimitPKTimes().Return(uint32(2)).AnyTimes()
			bc.EXPECT().GetLimitTimeFrame().Return([]uint32{72000, 79200}).AnyTimes()
			if got := mgr.GetPKTimeLimitToast(); got != tt.want {
				t.Errorf("GetPKTimeLimitToast() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestMgr_IsChannelLimitPK(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	type args struct {
		ctx       context.Context
		channelId uint32
	}
	tests := []struct {
		name string
		args args
		want bool
	}{
		{
			args: args{
				ctx:       ctx,
				channelId: channelId,
			},
			want: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mgr := NewTestMgr(ctrl)
			bc.EXPECT().GetLimitPKTimes().Return(uint32(2)).AnyTimes()
			bc.EXPECT().GetLimitTimeFrame().Return([]uint32{0, 86400}).AnyTimes()
			mockcache.EXPECT().BatchGetLimitTimeFramePKTimes(gomock.Any(), gomock.Any()).Return(map[uint32]uint32{
				channelId: 1,
			}, nil).AnyTimes()
			if got := mgr.IsChannelLimitPK(tt.args.ctx, tt.args.channelId); got != tt.want {
				t.Errorf("IsChannelLimitPK() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestMgr_SetPKSwitch(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	type args struct {
		ctx       context.Context
		channelId uint32
		pkSwitch  uint32
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			args: args{
				ctx:       ctx,
				channelId: channelId,
				pkSwitch:  1,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mgr := NewTestMgr(ctrl)
			mockchannelSchemeCli.EXPECT().GetCurChannelSchemeInfo(gomock.Any(), gomock.Any(), gomock.Any()).Return(&channelSchemePb.GetCurChannelSchemeInfoResp{
				Cid: channelId,
				SchemeInfo: &channelSchemePb.ChannelSchemeInfo{
					SchemeSvrDetailType: uint32(channel_scheme_conf_mgr.SchemeSvrDetailType_SCHEME_SVR_DETAIL_TYPE_FUN),
				},
			}, nil).AnyTimes()
			role := uint32(1)
			mockchannelCli.EXPECT().GetChannelAdmin(gomock.Any(), gomock.Any(), gomock.Any()).Return([]*channelsvr.ChannelAdmin{
				{
					Uid:       &uid,
					AdminRole: &role,
				},
			}, nil).AnyTimes()
			mockStore.EXPECT().UpdateChannelPKSwitch(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
			mockcache.EXPECT().SetChannelPKSwitch(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
			if err := mgr.SetPKSwitch(tt.args.ctx, tt.args.channelId, tt.args.pkSwitch); (err != nil) != tt.wantErr {
				t.Errorf("SetPKSwitch() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestMgr_StartPgcChannelPK(t *testing.T) {
	/*
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		f := initial(ctrl)
		type args struct {
			ctx     context.Context
			fromCid uint32
			toCid   uint32
		}
		tests := []struct {
			name    string
			fields  fields
			args    args
			wantErr bool
		}{
			{
				fields: f,
				args: args{
					ctx:     ctx,
					fromCid: channelId,
					toCid:   toChannelId,
				},
				wantErr: false,
			},
		}
		for _, tt := range tests {
			t.Run(tt.name, func(t *testing.T) {
				mgr := &Mgr{
					sc:               tt.fields.sc,
					bc:               tt.fields.bc,
					store:            tt.fields.store,
					cache:            tt.fields.cache,
					PushCli:          tt.fields.PushCli,
					channelCli:       tt.fields.channelCli,
					channelMicCli:    tt.fields.channelMicCli,
					channelSchemeCli: tt.fields.channelSchemeCli,
					channelGuildCli:  tt.fields.channelGuildCli,
					userProfileCli:   tt.fields.userProfileCli,
					channelStatsCli:  tt.fields.channelStatsCli,
					entertaimentCli:  tt.fields.entertaimentCli,
					SeqGenClient:     tt.fields.SeqGenClient,
					TimelineClient:   tt.fields.TimelineClient,
					GuildCli:         tt.fields.GuildCli,
					maskedPKCli:      tt.fields.maskedPKCli,
				}
				mockIsApplicableChannel()
				mockPushCli.EXPECT().PushMulticasts(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				mockchannelCli.EXPECT().GetChannelSimpleInfo(gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes().Return(&channelsvr.ChannelSimpleInfo{}, nil)
				bc.EXPECT().GetInviteExpireSec().Return(uint32(30)).AnyTimes()
				bc.EXPECT().GetPKWhiteList().Return([]uint32{}).AnyTimes()
				if err := mgr.StartPgcChannelPK(tt.args.ctx, tt.args.fromCid, tt.args.toCid); (err != nil) != tt.wantErr {
					//t.Errorf("StartPgcChannelPK() error = %v, wantErr %v", err, tt.wantErr)
				}
				//time.Sleep(2 * time.Second) // 预留协程test时间
			})
		}*/
}

func mockIsApplicableChannel() {
	mockcache.EXPECT().BatchGetChannelPKId(gomock.Any(), gomock.Any()).Return(map[uint32]uint32{
		channelId:   0,
		toChannelId: 0,
	}, nil).AnyTimes()
	mockcache.EXPECT().BatchGetPKInvite(gomock.Any(), gomock.Any()).Return(map[uint32]uint32{
		channelId:   0,
		toChannelId: 0,
	}, nil).AnyTimes()
	mockcache.EXPECT().HasRefusePKInvite(gomock.Any(), gomock.Any(), gomock.Any()).Return(false, nil).AnyTimes()
	mockcache.EXPECT().BatchGetLimitTimeFramePKTimes(gomock.Any(), gomock.Any()).Return(map[uint32]uint32{
		channelId:   0,
		toChannelId: 0,
	}, nil).AnyTimes()
	bc.EXPECT().GetLimitPKTimes().Return(uint32(2)).AnyTimes()
	//mockcache.EXPECT().GetChannelAnchorMap(gomock.Any()).Return(map[uint32]uint32{
	//    channelId:   1,
	//    toChannelId: 1,
	//}, nil).AnyTimes()
	mockcache.EXPECT().GetChannelPKSwitchMap(gomock.Any(), gomock.Any()).Return(map[uint32]uint32{
		channelId:   0,
		toChannelId: 0,
	}, nil).AnyTimes()
	mockuserProfileCli.EXPECT().BatchGetUserProfile(gomock.Any(), gomock.Any()).Return(map[uint32]*pbApp.UserProfile{
		channelId: {
			Privilege: &pbApp.UserPrivilege{Type: 1},
		},
		toChannelId: {
			Privilege: &pbApp.UserPrivilege{Type: 1},
		},
	}, nil).AnyTimes()
	mockchannelSchemeCli.EXPECT().GetCurChannelSchemeInfo(gomock.Any(), gomock.Any(), gomock.Any()).Return(&channelSchemePb.GetCurChannelSchemeInfoResp{
		Cid: channelId,
		SchemeInfo: &channelSchemePb.ChannelSchemeInfo{
			SchemeSvrDetailType: uint32(channel_scheme_conf_mgr.SchemeSvrDetailType_SCHEME_SVR_DETAIL_TYPE_FUN),
		},
	}, nil).AnyTimes()
	mockuserProfileCli.EXPECT().GetUserProfile(gomock.Any(), gomock.Any()).Return(&pbApp.UserProfile{Privilege: &pbApp.UserPrivilege{Type: 1}}, nil).AnyTimes()
	//mockcache.EXPECT().GetChannelAnchor(gomock.Any()).Return(uid, nil).AnyTimes()
	mockcache.EXPECT().SetPKInvite(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
	mockcache.EXPECT().SetChannelPKSuccess(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
	mockMaskedPKCli.EXPECT().GetChannelMaskedPKCurrConf(gomock.Any(), gomock.Any()).Return(&masked_pk_svr.GetChannelMaskedPKCurrConfResp{Conf: &masked_pk_svr.ChannelMaskedPKConf{}}, nil).AnyTimes()
	mockchannelMicCli.EXPECT().GetMicrList(gomock.Any(), gomock.Any(), gomock.Any()).Return(&channelmicPB.GetMicrListResp{AllMicList: []*channelmicPB.MicrSpaceInfo{{MicId: 1, MicUid: uid}}}, nil).AnyTimes()
	mockPgcChannelGameCli.EXPECT().GetChannelGameInfo(gomock.Any(), gomock.Any(), gomock.Any()).Return(&pgcchannelgamepb.GetChannelGameInfoResp{
		ChannelId:       0,
		CurrentGameId:   0,
		Phase:           0,
		Info:            nil,
		DigitalBombInfo: nil,
	}, nil).AnyTimes()

	mockOfferRoomCli.EXPECT().Check(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()

}

func TestMgr_getLimitTimeFrame(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	tests := []struct {
		name  string
		want  time.Time
		want1 time.Time
	}{
		{
			want:  time.Date(time.Now().Year(), time.Now().Month(), time.Now().Day(), 20, 0, 0, 0, time.Local),
			want1: time.Date(time.Now().Year(), time.Now().Month(), time.Now().Day(), 22, 0, 0, 0, time.Local),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mgr := NewTestMgr(ctrl)
			bc.EXPECT().GetLimitTimeFrame().Return([]uint32{72000, 79200}).AnyTimes()
			got, got1 := mgr.getLimitTimeFrame()
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("getLimitTimeFrame() got = %v, want %v", got, tt.want)
			}
			if !reflect.DeepEqual(got1, tt.want1) {
				t.Errorf("getLimitTimeFrame() got1 = %v, want %v", got1, tt.want1)
			}
		})
	}
}

func TestMgr_isCompere(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	type args struct {
		ctx       context.Context
		uid       uint32
		channelId uint32
	}
	tests := []struct {
		name    string
		args    args
		want    bool
		wantErr bool
	}{
		{
			args: args{
				ctx:       ctx,
				uid:       uid,
				channelId: channelId,
			},
			want:    true,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mgr := NewTestMgr(ctrl)
			mockchannelMicCli.EXPECT().GetMicrList(gomock.Any(), gomock.Any(), gomock.Any()).Return(&channelmicPB.GetMicrListResp{
				ChannelId: channelId,
				AllMicList: []*channelmicPB.MicrSpaceInfo{
					{
						MicId:  1,
						MicUid: uid,
					},
				},
				MicrMode:     0,
				ServerTimeMs: 0,
			}, nil).AnyTimes()
			got, err := mgr.isCompere(tt.args.ctx, tt.args.uid, tt.args.channelId)
			if (err != nil) != tt.wantErr {
				t.Errorf("isCompere() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("isCompere() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestMgr_isApplicableChannel(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	type args struct {
		ctx         context.Context
		channelId   uint32
		toChannelId uint32
	}
	tests := []struct {
		name    string
		args    args
		want    bool
		wantErr bool
	}{
		{
			args: args{
				ctx:         ctx,
				channelId:   channelId,
				toChannelId: toChannelId,
			},
			want:    true,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mgr := NewTestMgr(ctrl)
			mockIsApplicableChannel()
			got, err := mgr.isApplicableChannel(tt.args.ctx, tt.args.channelId, tt.args.toChannelId, false)
			if (err != nil) != tt.wantErr {
				t.Errorf("isApplicableChannel() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("isApplicableChannel() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestMgr_isPKChannelScheme(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	type args struct {
		ctx       context.Context
		channelId uint32
	}
	tests := []struct {
		name    string
		args    args
		want    bool
		wantErr bool
	}{
		{
			args: args{
				ctx:       ctx,
				channelId: channelId,
			},
			want:    true,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mgr := NewTestMgr(ctrl)
			mockchannelSchemeCli.EXPECT().GetCurChannelSchemeInfo(gomock.Any(), gomock.Any(), gomock.Any()).Return(&channelSchemePb.GetCurChannelSchemeInfoResp{
				Cid: channelId,
				SchemeInfo: &channelSchemePb.ChannelSchemeInfo{
					SchemeSvrDetailType: uint32(channel_scheme_conf_mgr.SchemeSvrDetailType_SCHEME_SVR_DETAIL_TYPE_FUN),
				},
			}, nil).AnyTimes()
			got, err := mgr.isPKChannelScheme(tt.args.ctx, tt.args.channelId)
			if (err != nil) != tt.wantErr {
				t.Errorf("isPKChannelScheme() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("isPKChannelScheme() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestMgr_isPKSwitchOperator(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	type args struct {
		ctx       context.Context
		uid       uint32
		channelId uint32
	}
	tests := []struct {
		name    string
		args    args
		want    bool
		wantErr bool
	}{
		{
			args: args{
				ctx:       ctx,
				uid:       uid,
				channelId: channelId,
			},
			want:    true,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mgr := NewTestMgr(ctrl)
			role := uint32(1)
			mockchannelCli.EXPECT().GetChannelAdmin(gomock.Any(), gomock.Any(), gomock.Any()).Return([]*channelsvr.ChannelAdmin{
				{
					Uid:       &uid,
					AdminRole: &role,
				},
			}, nil).AnyTimes()
			got, err := mgr.isPKSwitchOperator(tt.args.ctx, tt.args.uid, tt.args.channelId)
			if (err != nil) != tt.wantErr {
				t.Errorf("isPKSwitchOperator() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("isPKSwitchOperator() got = %v, want %v", got, tt.want)
			}
		})
	}
}

/*
func TestMgr_pushHandlePKInvite(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	f := initial(ctrl)
	type args struct {
		ctx     context.Context
		fromCid uint32
		toCid   uint32
		accept  bool
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name:   "",
			fields: f,
			args: args{
				ctx:     ctx,
				fromCid: channelId,
				toCid:   toChannelId,
				accept:  true,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mgr := &Mgr{
				sc:               tt.fields.sc,
				bc:               tt.fields.bc,
				store:            tt.fields.store,
				cache:            tt.fields.cache,
				PushCli:          tt.fields.PushCli,
				channelCli:       tt.fields.channelCli,
				channelMicCli:    tt.fields.channelMicCli,
				channelSchemeCli: tt.fields.channelSchemeCli,
				channelGuildCli:  tt.fields.channelGuildCli,
				userProfileCli:   tt.fields.userProfileCli,
				channelStatsCli:  tt.fields.channelStatsCli,
				entertaimentCli:  tt.fields.entertaimentCli,
				SeqGenClient:     tt.fields.SeqGenClient,
				TimelineClient:   tt.fields.TimelineClient,
				GuildCli:         tt.fields.GuildCli,
			}
			if err := mgr.pushHandlePKInvite(tt.args.ctx, tt.args.fromCid, tt.args.toCid, tt.args.accept); (err != nil) != tt.wantErr {
				t.Errorf("pushHandlePKInvite() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}*/

func TestMgr_pushPKInvite(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	type args struct {
		ctx             context.Context
		fromCid         uint32
		toCid           uint32
		senderProfile   *pbApp.UserProfile
		receiverProfile *pbApp.UserProfile
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mgr := NewTestMgr(ctrl)
			if err := mgr.pushPKInvite(tt.args.ctx, tt.args.fromCid, tt.args.toCid, tt.args.senderProfile, tt.args.receiverProfile); (err != nil) != tt.wantErr {
				t.Errorf("pushPKInvite() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestMgr_AcceptPgcChannelPK(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	type args struct {
		ctx     context.Context
		myCid   uint32
		fromCid uint32
		accept  bool
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "",
			args: args{
				ctx:     ctx,
				myCid:   channelId,
				fromCid: toChannelId,
				accept:  true,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mgr := NewTestMgr(ctrl)
			mockIsApplicableChannel()
			bc.EXPECT().GetLimitTimeFrame().Return([]uint32{72000, 79200}).AnyTimes()
			mockcache.EXPECT().AddLimitTimeFramePKTimes(gomock.Any(), gomock.Any(), gomock.Any()).Return(uint32(0), nil).AnyTimes()
			mockcache.EXPECT().SetChannelPKSuccess(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
			mockcache.EXPECT().DelPKInvite(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
			name := "test"
			mockchannelCli.EXPECT().BatchGetChannelSimpleInfo(gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes().Return(map[uint32]*channelsvr.ChannelSimpleInfo{
				channelId: {
					ChannelId: &channelId,
					DisplayId: &channelId,
					Name:      &name,
				},
				toChannelId: {
					ChannelId: &channelId,
					DisplayId: &channelId,
					Name:      &name,
				},
			}, nil)
			mockcache.EXPECT().GetPKInviteSender(gomock.Any(), gomock.Any()).Return(uint32(uid)).AnyTimes()
			mockuserProfileCli.EXPECT().GetUserProfile(gomock.Any(), gomock.Any()).Return(&pbApp.UserProfile{Privilege: &pbApp.UserPrivilege{Type: 1}}, nil).AnyTimes()
			mockPushCli.EXPECT().PushMulticasts(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()

			if err := mgr.AcceptPgcChannelPK(tt.args.ctx, tt.args.myCid, tt.args.fromCid, tt.args.accept); (err != nil) != tt.wantErr {
				t.Errorf("AcceptPgcChannelPK() error = %v, wantErr %v", err, tt.wantErr)
			}
			time.Sleep(2 * time.Second) // 异步推送
		})
	}
}

func TestMgr_filterNotApplicableChannelBatch(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	type args struct {
		ctx        context.Context
		channelIds []uint32
	}
	tests := []struct {
		name    string
		args    args
		want    []uint32
		wantErr bool
	}{
		{
			name:    "",
			args:    args{},
			want:    nil,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mgr := NewTestMgr(ctrl)
			mockIsApplicableChannel()
			_, err := mgr.filterNotApplicableChannelBatch(tt.args.ctx, tt.args.channelIds)
			if (err != nil) != tt.wantErr {
				t.Errorf("filterNotApplicableChannelBatch() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}

func TestMgr_getChannelAnchorUserprofileMap(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	type args struct {
		ctx        context.Context
		channelIds []uint32
	}
	tests := []struct {
		name    string
		args    args
		want    map[uint32]*pbApp.UserProfile
		wantErr bool
	}{
		{
			name: "",
			args: args{
				ctx:        ctx,
				channelIds: []uint32{channelId},
			},
			want: map[uint32]*pbApp.UserProfile{
				channelId: &pbApp.UserProfile{},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mgr := NewTestMgr(ctrl)

			mockuserProfileCli.EXPECT().BatchGetUserProfile(gomock.Any(), gomock.Any()).Return(map[uint32]*pbApp.UserProfile{
				uid: {},
			}, nil)
			mockchannelMicCli.EXPECT().GetMicrList(gomock.Any(), gomock.Any(), gomock.Any()).Return(&channelmicPB.GetMicrListResp{AllMicList: []*channelmicPB.MicrSpaceInfo{{MicId: 1, MicUid: uid}}}, nil).AnyTimes()
			got, err := mgr.getChannelAnchorUserprofileMap(tt.args.ctx, tt.args.channelIds)
			if (err != nil) != tt.wantErr {
				t.Errorf("getChannelAnchorUserprofileMap() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("getChannelAnchorUserprofileMap() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestMgr_isPKMap(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	type args struct {
		channelIds []uint32
	}
	tests := []struct {
		name    string
		args    args
		want    map[uint32]uint32
		wantErr bool
	}{
		{
			name: "",
			args: args{
				[]uint32{channelId},
			},
			want:    map[uint32]uint32{},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mgr := NewTestMgr(ctrl)
			mockcache.EXPECT().BatchGetPKInvite(gomock.Any(), gomock.Any()).Return(map[uint32]uint32{channelId: 0}, nil)
			mockcache.EXPECT().BatchGetChannelPKId(gomock.Any(), gomock.Any()).Return(map[uint32]uint32{channelId: 0}, nil)
			got, err := mgr.isPKMap(ctx, tt.args.channelIds)
			if (err != nil) != tt.wantErr {
				t.Errorf("isPKMap() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("isPKMap() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_privilegeTransform(t *testing.T) {
	type args struct {
		pr *pbApp.UserPrivilege
	}
	tests := []struct {
		name string
		args args
		want *pgc_channel_pk.UserPrivilege
	}{
		{
			name: "",
			args: args{
				pr: &pbApp.UserPrivilege{Type: 1},
			},
			want: &pgc_channel_pk.UserPrivilege{Type: 1},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := privilegeTransform(tt.args.pr); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("privilegeTransform() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_userprofileTransform(t *testing.T) {
	type args struct {
		up *pbApp.UserProfile
	}
	tests := []struct {
		name string
		args args
		want *pgc_channel_pk.UserProfile
	}{
		{
			name: "",
			args: args{
				&pbApp.UserProfile{
					Account: "hello",
				},
			},
			want: &pgc_channel_pk.UserProfile{
				Account:   "hello",
				Privilege: &pgc_channel_pk.UserPrivilege{},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := userprofileTransform(tt.args.up); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("userprofileTransform() = %v, want %v", got, tt.want)
			}
		})
	}
}
