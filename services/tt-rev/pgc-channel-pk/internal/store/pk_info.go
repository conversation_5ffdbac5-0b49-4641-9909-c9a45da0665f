package store

import (
	"context"
	"fmt"
	"time"

	"golang.52tt.com/pkg/log"
	logicPb "golang.52tt.com/protocol/app/pgc_channel-pk-logic"
)

const (
	PGCChannelPKInfoTableName = "pgc_channel_pk_info"
)

func createPgcChannelPkInfoTableSQL() string {
	sql := `CREATE TABLE IF NOT EXISTS %s (
		id int unsigned NOT NULL AUTO_INCREMENT,
		channel_id int unsigned NOT NULL,
		pk_id int unsigned NOT NULL,
		score int unsigned NOT NULL,
		pk_result int unsigned NOT NULL,
    	start_time timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '开始时间',
    	end_time timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '结束时间',
		update_time timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
 		PRIMARY KEY (id),
    	KEY (start_time),
		KEY (end_time),
		UNIQUE (channel_id, pk_id)
	  ) ENGINE=InnoDB DEFAULT CHARSET=utf8`

	return fmt.Sprintf(sql, PGCChannelPKInfoTableName)
}

type PgcChannelPkInfo struct {
	ID         uint32    `db:"id"`
	ChannelID  uint32    `db:"channel_id"`
	PKId       uint32    `db:"pk_id"`
	Score      uint32    `db:"score"`      //送礼值
	PKResult   uint32    `db:"pk_result"`  //PK结果
	StartTime  time.Time `db:"start_time"` //开始时间
	EndTime    time.Time `db:"end_time"`   //结束时间
	UpdateTime time.Time `db:"update_time"`
}

func (pk *PgcChannelPkInfo) IsEnd() bool {
	return pk.PKResult != 0
}

func (pk *PgcChannelPkInfo) GetPKResultType(otherScore uint32) uint32 {
	if pk.Score > otherScore {
		return uint32(logicPb.PgcChannelPKMemResult_Win)
	} else if pk.Score < otherScore {
		return uint32(logicPb.PgcChannelPKMemResult_Loss)
	}
	return uint32(logicPb.PgcChannelPKMemResult_Draw)
}

func (st *Store) AddPgcChannelPKInfo(ctx context.Context, info *PgcChannelPkInfo) error {
	sql := fmt.Sprintf("INSERT INTO %s (channel_id, pk_id, score, start_time, end_time, update_time) VALUES (?,?,?,?,?,?)", PGCChannelPKInfoTableName)
	_, err := st.db.ExecContext(ctx, sql, info.ChannelID, info.PKId, 0, info.StartTime, info.EndTime, info.UpdateTime)
	if err != nil {
		return err
	}
	return nil
}

func (st *Store) GetPgcChannelPKInfo(ctx context.Context, pkId uint32) ([]*PgcChannelPkInfo, error) {
	data := make([]*PgcChannelPkInfo, 0)

	sql := fmt.Sprintf("select * from %s where pk_id = ?", PGCChannelPKInfoTableName)
	rows, err := st.db.QueryxContext(ctx, sql, pkId)
	if err != nil {
		return data, err
	}
	defer rows.Close()

	for rows.Next() {
		item := &PgcChannelPkInfo{}
		err = rows.StructScan(item)
		if err != nil {
			return data, err
		}
		data = append(data, item)
	}

	return data, nil
}

func (st *Store) UpdatePgcChannelPKEndInfo(ctx context.Context, channelId, pkId, pkResult uint32, endTime time.Time) (bool, error) {
	sql := fmt.Sprintf("update %s set end_time=?, update_time=?,pk_result=? where channel_id=? and pk_id=?", PGCChannelPKInfoTableName)
	ret, err := st.db.ExecContext(ctx, sql, endTime, endTime, pkResult, channelId, pkId)
	if err != nil {
		log.Errorf("UpdatePgcChannelPKEndTime fail. channelId:%d, pkId:%d, endTime:%v, err:%v",
			channelId, pkId, endTime, err)
		return false, err
	}
	rowsAffected, _ := ret.RowsAffected()
	return rowsAffected > 0, nil
}

func (st *Store) UpdatePgcChannelPKScore(ctx context.Context, channelId, pkId, score uint32, currentTime time.Time) (bool, error) {
	sql := fmt.Sprintf("update %s set score = score + ? where channel_id=? and pk_id=?", PGCChannelPKInfoTableName)
	ret, err := st.db.ExecContext(ctx, sql, score, channelId, pkId)
	if err != nil {
		log.Errorf("UpdatePgcChannelPKScore fail. channelId:%d, pkId:%d, score:%d, err:%v",
			channelId, pkId, score, err)
		return false, err
	}
	rowsAffected, _ := ret.RowsAffected()
	return rowsAffected > 0, nil
}

func (st *Store) InitTable(ctx context.Context) error {
	sql := createPgcChannelPkInfoTableSQL()
	_, err := st.db.ExecContext(ctx, sql)
	if err != nil {
		log.Errorf("InitTable fail to createPgcChannelPkInfoTable")
	}

	_, err = st.db.ExecContext(ctx, createQuickKillRecordTableSQL())
	if err != nil {
		log.Errorf("InitTable fail to createQuickKillRecordTable")
	}

	_, err = st.db.ExecContext(ctx, createPeakRecordTableSQL())
	if err != nil {
		log.Errorf("InitTable fail to createPeakRecordTable")
	}

	_, err = st.db.ExecContext(ctx, createPgcChannelPKSwitchTableSQL())
	if err != nil {
		log.Errorf("InitTable fail to createPgcChannelPKSwitchTable")

	}

	return nil
}
