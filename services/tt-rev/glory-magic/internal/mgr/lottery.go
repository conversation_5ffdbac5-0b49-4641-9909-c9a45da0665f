package mgr

import (
	"context"
	"fmt"
	"math"
	"strings"
	"time"

	"gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/mysql"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	protogrpc "golang.52tt.com/pkg/protocol/grpc"
	pbApp "golang.52tt.com/protocol/app"
	imPB "golang.52tt.com/protocol/app/im"
	"golang.52tt.com/protocol/common/status"
	backpackBasePb "golang.52tt.com/protocol/services/backpack-base"
	backpackpb "golang.52tt.com/protocol/services/backpacksvr"
	chanceGamePb "golang.52tt.com/protocol/services/chance-game-entry"
	pb "golang.52tt.com/protocol/services/glory-magic"
	imApiPB "golang.52tt.com/protocol/services/im-api"
	"golang.52tt.com/services/tt-rev/glory-magic/internal/mgr/award_helper"
	"golang.52tt.com/services/tt-rev/glory-magic/internal/store"
)

const (
	lotteryAccuracy = 10000
)

func (mgr *Mgr) CheckChanceGameIsOpen(ctx context.Context) (bool, bool, error) {
	log.DebugWithCtx(ctx, "CheckChanceGameIsOpen, x-qw-traffic-mark:%v", ctx.Value("x-qw-traffic-mark"))

	resp, err := mgr.rpcClient.ChanceEntryCli.CheckChanceGameIsOpen(ctx, &chanceGamePb.CheckChanceGameIsOpenReq{
		GameType: uint32(chanceGamePb.NewChanceGameType_NewChanceGameType_GloryMagic),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "CheckChanceGameIsOpen, err: %v", err)
	}

	commonCfg, err := mgr.GetLocalCurrentChanceStock(ctx, uint32(pb.StockRankType_STOCK_RANK_TYPE_COMMON))
	if err != nil {
		log.ErrorWithCtx(ctx, "CheckChanceGameIsOpen, err: %v", err)
		return false, false, err
	}
	seniorCfg, err := mgr.GetLocalCurrentChanceStock(ctx, uint32(pb.StockRankType_STOCK_RANK_TYPE_SENIOR))
	if err != nil {
		log.ErrorWithCtx(ctx, "CheckChanceGameIsOpen, err: %v", err)
		return false, false, err
	}

	log.DebugWithCtx(ctx, "CheckChanceGameIsOpen, resp: %+v, commonCfg: %+v, seniorCfg: %+v", resp, len(commonCfg), len(seniorCfg))
	return resp.GetSwitchStatus() && len(commonCfg) > 0, resp.GetSwitchStatus() && len(seniorCfg) > 0, nil
}

func (mgr *Mgr) GetPreInfo(ctx context.Context, req *pb.GetPreInfoReq) (*pb.GetPreInfoResp, error) {
	resp := &pb.GetPreInfoResp{}
	consumeVal, err := mgr.cache.GetUserConsumeValThisWeek(ctx, req.GetUid())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetPreInfo.GetUserConsumeValThisWeek uid: %d, err: %v", req.GetUid(), err)
		return resp, err
	}
	resp.RemainLotteryTimes = mgr.getUserLotteryTimes(ctx, req.GetUid())
	resp.SingleLotteryCost = mgr.bc.GetLotteryCost(req.GetPondType())
	resp.IncrLotteryTbean, resp.IncrLotteryTimes = mgr.bc.GetNextLvExtendLotteryTimes(consumeVal)

	return resp, nil
}

func (mgr *Mgr) GetWinningCarousel(ctx context.Context, req *pb.GetWinningCarouselReq) (*pb.GetWinningCarouselResp, error) {
	resp := &pb.GetWinningCarouselResp{}

	winningCarousel, err := mgr.cache.GetWinningCarousel(ctx, req.GetPondType())
	if err != nil {
		return resp, err
	}
	uids := make([]uint32, 0, len(winningCarousel))
	for _, item := range winningCarousel {
		uids = append(uids, item.Uid)
	}
	userProfileMap, err := mgr.rpcClient.UserProfileCli.BatchGetUserProfileV2(ctx, uids, true)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetWinningCarousel.BatchGetUserProfileV2, uid: %d, err: %v", req.GetUid(), err)
		return resp, err
	}
	for _, item := range winningCarousel {
		if item.Uid == req.GetUid() &&
			userProfileMap[item.GetUid()].GetPrivilege().GetType() == uint32(pbApp.EUserPrivilegeType_ENUM_USER_PRIVILEGE_UKW) {
			item.Nickname = "神秘人（我本人）"
		}
	}

	resp.WinningInfoList = winningCarousel
	return resp, nil
}

func (mgr *Mgr) Lottery(ctx context.Context, pondType, times, uid uint32) (*pb.LotteryResp, error) {
	emptyResp := &pb.LotteryResp{}

	// 判断活动是否开启
	isNormalOpen, isGrandOpen, err := mgr.CheckChanceGameIsOpen(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "Lottery.CheckChanceGameIsOpen, uid: %d, err: %v", uid, err)
		return emptyResp, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "服务器繁忙，请稍后再试")
	}
	if (!isNormalOpen && pondType == uint32(pb.PondType_POND_TYPE_NORMAL)) ||
		(!isGrandOpen && pondType == uint32(pb.PondType_POND_TYPE_GRAND)) {
		return emptyResp, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "活动未开启")
	}

	err = mgr.cache.LockUserLottery(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "Lottery.LockUserLottery, uid: %d, err: %v", uid, err)
		return emptyResp, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "服务器繁忙，请稍后再试")
	} else {
		defer mgr.cache.UnlockUserLottery(ctx, uid)
	}

	userProfile, err := mgr.rpcClient.UserProfileCli.GetUserProfile(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "Lottery.GetUserProfile, uid: %d, err: %v", uid, err)
		return emptyResp, err
	}

	// 检查碎片是否足够
	if !mgr.isEnoughChip(ctx, pondType, times, uid) {
		log.ErrorWithCtx(ctx, "Lottery.isEnoughChip fail, uid: %d, times: %d", uid, times)
		return emptyResp, protocol.NewExactServerError(nil, status.ErrGloryMagicOutOfPrestige)
	}

	// 检查抽奖次数
	lotteryTimes := mgr.getUserLotteryTimes(ctx, uid)
	if lotteryTimes < times {
		log.ErrorWithCtx(ctx, "Lottery. fail, uid: %d, err: no enough lottery times", uid, err)
		return emptyResp, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "次数不足")
	}

	payOrderId := fmt.Sprintf("GLORY_MAGIC_%d_%d", uid, nowMilli())
	outsideTime := uint32(time.Now().Unix())

	// 支付
	err = mgr.pay(ctx, payOrderId, pondType, uid, times, outsideTime)
	if err != nil {
		log.ErrorWithCtx(ctx, "Lottery.pay, uid: %d, err: %v", uid, err)
		return emptyResp, err
	}

	// 抽取奖品
	prizeList, err := mgr.multiDraw(ctx, pondType, uid, times)
	if err != nil {
		log.ErrorWithCtx(ctx, "Lottery.multiDraw, uid: %d, err: %v", uid, err)
		return emptyResp, err
	}

	// 扣减库存
	prizeList, reduceStockRollback, err := mgr.reduceStock(ctx, pondType, uid, prizeList)
	defer func() {
		if err != nil {
			reduceStockRollback() // 如果后续发生错误, 回滚库存
		}
	}()
	if err != nil {
		log.ErrorWithCtx(ctx, "Lottery.reduceStock, uid: %d, err: %v", uid, err)
		return emptyResp, err
	}

	// 处理保底情况
	handleLotteryCompRollBack := mgr.handleLotteryCompensation(ctx, pondType, uid, prizeList)
	defer func() {
		if err != nil {
			handleLotteryCompRollBack() // 回滚保底设置
		}
	}()

	// 发奖 写抽奖记录
	lotteryRecordList := mgr.prize2LotteryRecord(ctx, pondType, uid, outsideTime, payOrderId, prizeList)
	err = mgr.sendAward(ctx, payOrderId, lotteryRecordList, outsideTime)
	if err != nil {
		log.ErrorWithCtx(ctx, "Lottery.sendAward, uid: %d, err: %v", uid, err)
		return emptyResp, err
	}

	resp, awardInfoList, tErr := mgr.toLotteryResp(ctx, lotteryRecordList)
	if tErr != nil {
		log.ErrorWithCtx(ctx, "Lottery.toLotteryResp, uid: %d, err: %v", uid, tErr)
		return emptyResp, tErr
	}

	go func() {
		asyncCtx, cancel := protogrpc.NewContextWithInfoTimeout(ctx, time.Second*5)
		defer cancel()
		mgr.SendTTHelper(asyncCtx, uid, awardInfoList)
	}()

	// 中奖轮播
	for i, item := range resp.AwardInfo {
		if i < len(lotteryRecordList) &&
			lotteryRecordList[i].ItemType == uint32(pb.LotteryItemType_LOTTERY_ITEM_DRESSUP_DEBRIS) { //碎片不要轮播
			continue
		}
		mgr.cache.AddWinningCarousel(ctx, pondType, &pb.WinningInfo{
			Uid:       uid,
			Nickname:  userProfile.GetNickname(),
			AwardInfo: item,
		})
	}

	return resp, nil
}

func (mgr *Mgr) toLotteryResp(ctx context.Context, lotteryRecordList []*store.LotteryRecord) (*pb.LotteryResp, []*award_helper.AwardInfo, error) {
	resp := &pb.LotteryResp{}
	awardInfoList := make([]*award_helper.AwardInfo, 0, len(lotteryRecordList))
	resp.WinningType = uint32(pb.LotteryResp_WINNING_TYPE_NONE)
	for _, item := range lotteryRecordList {
		if resp.WinningType > item.WinningType {
			resp.WinningType = item.WinningType // 记录最大的奖品类型
		}
		awardInfo, err := mgr.GetAwardInfo(ctx, item.ItemType, item.ItemId)
		if err != nil {
			log.ErrorWithCtx(ctx, "Lottery.GetAwardInfo, uid: %d, err: %v", item.Uid, err)
			return resp, nil, err
		}
		awardInfoList = append(awardInfoList, &awardInfo)
		resp.AwardInfo = append(resp.AwardInfo, &pb.AwardInfo{
			AwardName:  awardInfo.Name,
			AwardIcon:  awardInfo.Icon,
			AwardWorth: awardInfo.Price,
			AwardNum:   awardInfo.Num,
			AwardDays:  item.DayCnt,
		})
	}
	return resp, awardInfoList, nil
}

type prize struct {
	*store.ChanceConf        // 奖品配置
	noPrizeCnt        uint32 // 连黑次数
	noFlashCnt        uint32 // 出光次数
	isFlashGuarantee  bool   // 是否保底
}

func (mgr *Mgr) multiDraw(ctx context.Context, pondType, uid, times uint32) ([]*prize, error) {
	rs := make([]*prize, 0, times)

	// 当前出光保底
	nowNoFlashCnt, err := mgr.cache.GetNoFlashCount(ctx, pondType, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "multiDraw.GetNoFlashCount, uid: %d, err: %v", uid, err)
		return rs, err
	}

	// 当前连黑保底
	nowNoPrizeCnt, err := mgr.cache.GetNoPrizeCount(ctx, pondType, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "multiDraw.GetNoPrizeCount, uid: %d, err: %v", uid, err)
		return rs, err
	}

	// 30天内是否中过大奖
	isWinningBigPrizeRecent30Day, err := mgr.cache.IsWinningBigPrizeRecent30Day(ctx, pondType, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "multiDraw.IsWinningBigPrizeRecent30Day, uid: %d, err: %v", uid, err)
		return rs, err
	}

	// 获取各类奖品
	bigPrize, flashPrize, floatPrizePool, err := mgr.getAllKindChance(ctx, pondType)
	if err != nil {
		log.ErrorWithCtx(ctx, "draw.getAllKindChance, uid: %d, err: %v", uid, err)
		return rs, err
	}
	totalWeight := bigPrize.Weight + flashPrize.Weight
	for _, item := range floatPrizePool {
		totalWeight += item.Weight
	}

	bigPrizeChance := bigPrize.Weight * lotteryAccuracy / totalWeight // 大奖概率, 换算成 x/10000 形式的概率
	for i := 0; i < int(times); i++ {
		// 加上当前抽数
		nowNoFlashCnt++
		nowNoPrizeCnt++

		flashChance := mgr.bc.GetFlashPrizeChance(pondType, nowNoFlashCnt)
		if flashChance >= lotteryAccuracy-bigPrizeChance {
			flashChance = lotteryAccuracy - bigPrizeChance
		}

		// 第一轮随机数 用于选择  大奖/出光/浮动奖池
		rv := randUint32(lotteryAccuracy)
		log.Infof(fmt.Sprintf("LotteryDebug ====== uid: %d, 奖池:|---大奖(%d)---|---闪光(%d)---|---浮动(%d)---|, 出奖号码: %d",
			uid, bigPrizeChance, flashChance, lotteryAccuracy-bigPrizeChance-flashChance, rv))

		// 中了大奖概率
		if rv < bigPrizeChance && !isWinningBigPrizeRecent30Day {
			rs = append(rs, &prize{ChanceConf: bigPrize})
			isWinningBigPrizeRecent30Day = true
			continue
		}

		// 判断是否出光
		if rv < bigPrizeChance+flashChance {
			rs = append(rs, &prize{ChanceConf: flashPrize, isFlashGuarantee: bigPrizeChance+flashChance >= lotteryAccuracy})
			nowNoFlashCnt = 0
			nowNoPrizeCnt = 0
			continue
		}

		// 落入浮动奖池
		blackPool := make([]*store.ChanceConf, 0)
		blackPrizeWeight := uint32(0)
		prizePool := make([]*store.ChanceConf, 0)
		prizeWeight := uint32(0)

		// 拆分奖池 将完整的奖池拆分成 连黑保底奖池/普通奖池
		for _, item := range floatPrizePool {
			if item.ItemType == uint32(pb.LotteryItemType_LOTTERY_ITEM_DRESSUP_DEBRIS) { // 当前只有抽出碎片会叠加保底
				blackPrizeWeight += item.Weight
				blackPool = append(blackPool, item)
			} else {
				prizeWeight += item.Weight
				prizePool = append(prizePool, item)
			}
		}

		// 根据当前脸黑次数计算,脸黑奖下调概率
		blackChanceDownTimes := int(nowNoPrizeCnt) - int(mgr.bc.GetBlackChanceDownStart(pondType))
		if blackChanceDownTimes < 0 {
			blackChanceDownTimes = 0
		}

		// 连黑保底机制下 新的脸黑中奖率
		blackPrizeChance := int(blackPrizeWeight) * lotteryAccuracy / int(blackPrizeWeight+prizeWeight)               // 计算无奖的基准概率
		dedBlackPrizeChance := blackPrizeChance - int(mgr.bc.GetNoPrizeChanceDownStep(pondType))*blackChanceDownTimes // 新的无奖概率
		if dedBlackPrizeChance < 0 {
			dedBlackPrizeChance = 0
		}

		// 第二轮随机数 用于选择  脸黑奖池/普通奖池
		rv2 := randUint32(lotteryAccuracy)
		log.Infof(fmt.Sprintf("LotteryDebug ====== uid: %d, 浮动奖池:|---脸黑奖池(%d)---|---普通奖池(%d)---|, 出奖号码: %d",
			uid, dedBlackPrizeChance, lotteryAccuracy-dedBlackPrizeChance, rv2))

		var resultPool []*store.ChanceConf
		var resultWeight uint32
		var isBlack bool
		if rv2 < uint32(dedBlackPrizeChance) {
			resultPool = blackPool
			resultWeight = blackPrizeWeight
			isBlack = true
		} else {
			resultPool = prizePool
			resultWeight = prizeWeight
		}

		// 第二轮随机数，用于基于对应奖池数据 抽取对应奖池里面的奖品
		rv3 := randUint32(resultWeight)

		curChance := uint32(0)
		for _, item := range resultPool {
			curChance += item.Weight
			if curChance > rv3 {
				rs = append(rs, &prize{ChanceConf: item}) // 中了普通奖
				break
			}
		}

		if isBlack {
			log.Infof(fmt.Sprintf("LotteryDebug ====== uid: %d, 浮动奖池:|---脸黑奖池---|, 出奖号码: %d", uid, rv3))
		} else {
			nowNoPrizeCnt = 0 // 中奖清除连黑保底
			logInfo := strings.Builder{}
			logInfo.WriteString(fmt.Sprintf("LotteryDebug ====== uid: %d, 浮动奖池:|", uid))
			for _, item := range resultPool {
				logInfo.WriteString(fmt.Sprintf("---%s(%d)---|", item.ItemName, item.Weight*lotteryAccuracy/resultWeight))
			}
			logInfo.WriteString(fmt.Sprintf(", 出奖号码: %d", rv3))
			log.Infof(logInfo.String())
		}

	}

	return rs, nil
}

func (mgr *Mgr) reduceStock(ctx context.Context, pondType, uid uint32, prizeList []*prize) ([]*prize, func(), error) {
	actualPrizeList := make([]*prize, 0, len(prizeList))

	// 已扣除的库存
	reducedStock := make([]*store.ChanceConf, 0, len(prizeList))
	rollback := func() {
		for _, item := range reducedStock {
			mgr.reduceStockDegrandable(ctx, item, pondType, -1) // 回滚方法
		}
	}

	for _, item := range prizeList {
		if item.IsChip() ||
			(item.IsGuarantee() && item.isFlashGuarantee) {
			actualPrizeList = append(actualPrizeList, item) // 碎片, 保底出光不需扣减库存
			continue
		}

		// 扣减库存
		actualPrize, err := mgr.reduceStockDegrandable(ctx, item.ChanceConf, pondType, 1)
		if err != nil {
			log.ErrorWithCtx(ctx, "reduceStock.reduceStock, uid: %d, err: %v", uid, err)
			return actualPrizeList, rollback, err
		}
		reducedStock = append(reducedStock, actualPrize)
		actualPrizeList = append(actualPrizeList, &prize{ChanceConf: actualPrize})
	}

	return actualPrizeList, rollback, nil
}

func (mgr *Mgr) draw(ctx context.Context, pondType, uid uint32) (*store.ChanceConf, error) {
	// 获取各类奖品
	bigPrize, flashPrize, floatPrizePool, err := mgr.getAllKindChance(ctx, pondType)
	if err != nil {
		log.ErrorWithCtx(ctx, "draw.getAllKindChance, uid: %d, err: %v", uid, err)
		return nil, err
	}
	totalWeight := bigPrize.Weight + flashPrize.Weight
	for _, item := range floatPrizePool {
		totalWeight += item.Weight
	}

	bigPrizeChance := bigPrize.Weight * lotteryAccuracy / totalWeight // 换算成 x/10000 形式的概率

	// flash奖品的 保底 概率
	noFlashCount, err := mgr.cache.GetNoFlashCount(ctx, pondType, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "draw.GetNoFlashCount, uid: %d, err: %v", uid, err)
		return nil, err
	}

	flashChance := mgr.bc.GetFlashPrizeChance(pondType, noFlashCount)
	if flashChance >= lotteryAccuracy-bigPrizeChance {
		flashChance = lotteryAccuracy - bigPrizeChance
	}

	// 先在简化奖池抽一次 |---大奖(a%)---|---闪光(b%)---|---浮动(1-a-b %)---|
	rv := randUint32(lotteryAccuracy)

	log.Infof(fmt.Sprintf("LotteryDebug ====== uid: %d, 奖池:|---大奖(%d)---|---闪光(%d)---|---浮动(%d)---|, 出奖号码: %d",
		uid, bigPrizeChance, flashChance, lotteryAccuracy-bigPrizeChance-flashChance, rv))

	// 中了大奖概率
	if rv < bigPrizeChance {
		yes, err := mgr.cache.IsWinningBigPrizeRecent30Day(ctx, pondType, uid)
		if err != nil {
			return nil, err
		}
		// 如果30天没中过大奖
		if !yes {
			return bigPrize, nil
		}
	}

	// 判断是否出光
	if rv < bigPrizeChance+flashChance {
		return flashPrize, nil
	} else {
		// 落入浮动奖池
		return mgr.floatPrizeDraw(ctx, pondType, uid, floatPrizePool)
	}
}

// 获取各类奖池
func (mgr *Mgr) getAllKindChance(ctx context.Context, pondType uint32) (*store.ChanceConf, *store.ChanceConf, []*store.ChanceConf, error) {
	bigPrize, flashPrize := &store.ChanceConf{}, &store.ChanceConf{}
	floatPrize := make([]*store.ChanceConf, 0, 8)

	allChance, err := mgr.store.GetCurrentChanceStock(ctx, pondType)
	if err != nil {
		log.ErrorWithCtx(ctx, "draw.GetCurrentChanceStock, uid: %d, err: %v", err)
		return bigPrize, flashPrize, floatPrize, err
	}
	for _, item := range allChance {
		if item.IsTop() {
			bigPrize = item
		}
		if item.IsGuarantee() {
			flashPrize = item
		}
		if item.IsUnstable() {
			floatPrize = append(floatPrize, item)
		}
	}

	return bigPrize, flashPrize, floatPrize, nil
}

// 浮动奖池抽奖
func (mgr *Mgr) floatPrizeDraw(ctx context.Context, pondType, uid uint32, pool []*store.ChanceConf) (*store.ChanceConf, error) {
	blackPool := make([]*store.ChanceConf, 0)
	blackPrizeWeight := uint32(0)
	prizePool := make([]*store.ChanceConf, 0)
	prizeWeight := uint32(0)

	// 拆分奖池 将完整的浮动奖励奖池, 脸黑奖和普通奖
	for _, item := range pool {
		if item.ItemType == uint32(pb.LotteryItemType_LOTTERY_ITEM_DRESSUP_DEBRIS) { // 只有抽出碎片会叠加保底
			blackPrizeWeight += item.Weight
			blackPool = append(blackPool, item)
		} else {
			prizeWeight += item.Weight
			prizePool = append(prizePool, item)
		}
	}

	// 计算脸黑奖次数 保底下调次数
	blackCount, err := mgr.cache.GetNoPrizeCount(ctx, pondType, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "draw.GetNoPrizeCount, uid: %d, err: %v", uid, err)
		return nil, err
	}
	// 根据当前脸黑次数计算,脸黑奖下调概率
	blackChanceDownTimes := int(blackCount) - int(mgr.bc.GetBlackChanceDownStart(pondType)) + 1 // 加上本次
	if blackChanceDownTimes < 0 {
		blackChanceDownTimes = 0
	}

	// 连黑保底机制下 新的脸黑中奖率
	blackPrizeChance := int(blackPrizeWeight) * lotteryAccuracy / int(blackPrizeWeight+prizeWeight)               // 计算不中奖的基准概率
	dedBlackPrizeChance := blackPrizeChance - int(mgr.bc.GetNoPrizeChanceDownStep(pondType))*blackChanceDownTimes // 新的 不中奖概率
	if dedBlackPrizeChance < 0 {
		dedBlackPrizeChance = 0
	}

	// 第一轮随机数 用于选择  脸黑奖池/普通奖池
	rv := randUint32(lotteryAccuracy)

	log.Infof(fmt.Sprintf("LotteryDebug ====== uid: %d, 浮动奖池:|---脸黑奖池(%d)---|---普通奖池(%d)---|, 出奖号码: %d",
		uid, dedBlackPrizeChance, lotteryAccuracy-dedBlackPrizeChance, rv))

	var resultPool []*store.ChanceConf
	var resultWeight uint32
	var isBlack bool
	if rv < uint32(dedBlackPrizeChance) {
		resultPool = blackPool
		resultWeight = blackPrizeWeight
		isBlack = true
	} else {
		resultPool = prizePool
		resultWeight = prizeWeight
	}

	// 第二轮随机数，用于基于对应奖池数据 抽取对应奖池里面的奖品
	rv2 := randUint32(resultWeight)

	// debug log
	if isBlack {
		log.Infof(fmt.Sprintf("LotteryDebug ====== uid: %d, 浮动奖池:|---脸黑奖池---|, 出奖号码: %d", uid, rv2))
	} else {
		logInfo := strings.Builder{}
		logInfo.WriteString(fmt.Sprintf("LotteryDebug ====== uid: %d, 浮动奖池:|", uid))
		for _, item := range resultPool {
			logInfo.WriteString(fmt.Sprintf("---%s(%d)---|", item.ItemName, item.Weight*lotteryAccuracy/resultWeight))
		}
		logInfo.WriteString(fmt.Sprintf(", 出奖号码: %d", rv2*lotteryAccuracy/resultWeight))
		log.Infof(logInfo.String())
	}

	curChance := uint32(0)
	for _, item := range resultPool {
		curChance += item.Weight
		if curChance > rv2 {
			return item, nil
		}
	}

	return nil, nil
}

// 处理保底次数
func (mgr *Mgr) handleLotteryCompensation(ctx context.Context, pondType, uid uint32, prizeList []*prize) func() {
	originNoFlashCnt, _ := mgr.cache.GetNoFlashCount(ctx, pondType, uid)
	originNoPrizeCnt, _ := mgr.cache.GetNoPrizeCount(ctx, pondType, uid)
	markBigPrize := false

	rollBack := func() {
		err := mgr.cache.SetNoFlashCount(ctx, pondType, uid, originNoFlashCnt)
		if err != nil {
			log.ErrorWithCtx(ctx, "handleLotteryCompensation.rollBack.SetNoFlashCount, uid: %d, err: %v", uid, err)
		}
		err = mgr.cache.SetNoPrizeCount(ctx, pondType, uid, originNoPrizeCnt)
		if err != nil {
			log.ErrorWithCtx(ctx, "handleLotteryCompensation.rollBack.SetNoPrizeCount, uid: %d, err: %v", uid, err)
		}
		if markBigPrize {
			err = mgr.cache.DelWinningBigPrizeRecent30Day(ctx, pondType, uid)
			if err != nil {
				log.ErrorWithCtx(ctx, "handleLotteryCompensation.rollBack.DelWinningBigPrizeRecent30Day, uid: %d, err: %v", uid, err)
			}
		}
		log.InfoWithCtx(ctx, "handleLotteryCompensation.rollBack, pondType: %d, uid: %d", pondType, uid)
	}

	nowNoFlashCnt := originNoFlashCnt
	nowNoPrizeCnt := originNoPrizeCnt
	for _, item := range prizeList {
		nowNoFlashCnt++
		nowNoPrizeCnt++
		item.noFlashCnt = nowNoFlashCnt // 记录当前抽数
		item.noPrizeCnt = nowNoPrizeCnt
		if item.IsTop() {
			markBigPrize = true
			err := mgr.cache.MarkWinningBigPrizeRecent30Day(ctx, pondType, uid) // 记录最近30天中过大奖
			if err != nil {
				log.ErrorWithCtx(ctx, "handleLotteryCompensation.MarkWinningBigPrizeRecent30Day, uid: %d, err: %v", uid, err)
			}
			nowNoFlashCnt = 0
			nowNoPrizeCnt = 0
			continue
		}
		if item.IsGuarantee() {
			nowNoFlashCnt = 0
			nowNoPrizeCnt = 0
			continue
		}
		if !item.IsChip() {
			nowNoPrizeCnt = 0
		}
	}

	err := mgr.cache.SetNoFlashCount(ctx, pondType, uid, nowNoFlashCnt)
	if err != nil {
		log.ErrorWithCtx(ctx, "handleLotteryCompensation.SetNoFlashCount, uid: %d, err: %v", uid, err)
	}
	err = mgr.cache.SetNoPrizeCount(ctx, pondType, uid, nowNoPrizeCnt)
	if err != nil {
		log.ErrorWithCtx(ctx, "handleLotteryCompensation.SetNoPrizeCount, uid: %d, err: %v", uid, err)
	}

	return rollBack
}

// isEnoughChip 判断是否有足够的星钻
func (mgr *Mgr) isEnoughChip(ctx context.Context, pondType, times, uid uint32) bool {
	backPackItemResp, err := mgr.rpcClient.BackpackBaseCli.GetUserBackpackByItem(ctx, uid, uint32(backpackBasePb.PackageItemType_BACKPACK_LOTTERY_FRAGMENT), 0)
	if err != nil {
		log.ErrorWithCtx(ctx, "isEnoughChip GetUserBackpackByItem, uid: %d, err: %v", uid, err)
		return false
	}

	var fragGloryCnt uint32
	for _, item := range backPackItemResp.GetUserItemList() {
		if item.GetFinTime() != 0 && item.GetFinTime() < uint32(time.Now().Unix()) {
			continue
		}

		if item.GetSourceId() == mgr.bc.GetGloryFragmentItemId() {
			fragGloryCnt += item.GetItemCount()
		}
	}

	if mgr.bc.GetLotteryCost(pondType)*times <= fragGloryCnt {
		return true
	}

	return false
}

func (mgr *Mgr) pay(ctx context.Context, payOrderId string, pondType, uid, times, outsideTime uint32) error {
	if err := mgr.store.Transaction(ctx, func(tx mysql.Tx) error {
		// 初始化订单
		err := mgr.store.AddConsumeOrder(ctx, &store.ConsumeOrder{
			OrderId:      payOrderId,
			Uid:          uid,
			Cost:         mgr.bc.GetLotteryCost(pondType) * times,
			LotteryTimes: times,
			Status:       store.ConsumeOrderStatusInit,
			OutsideTime:  outsideTime,
			CreateTime:   time.Now(),
		})
		if err != nil {
			log.ErrorWithCtx(ctx, "Lottery.pay, uid: %d, err: %v", uid, err)
			return err
		}

		// 扣减抽奖次数
		err = mgr.cache.ReduceUserLotteryTimes(ctx, times, uid)
		if err != nil {
			log.ErrorWithCtx(ctx, "Lottery.ReduceUserLotteryTimes, uid: %d, err: %v", uid, err)
			return err
		}
		return nil
	}); err != nil {
		log.ErrorWithCtx(ctx, "Lottery.pay, uid: %d, err: %v", uid, err)
		return err
	}

	// 扣除碎片
	_, err := mgr.rpcClient.BackpackBaseCli.UseBackpackItem(ctx, &backpackBasePb.UseBackpackItemReq{
		Uid:            uid,
		ItemType:       uint32(backpackpb.PackageItemType_BACKPACK_LOTTERY_FRAGMENT),
		SourceId:       mgr.bc.GetGloryFragmentItemId(),
		OrderId:        payOrderId,
		UseCount:       mgr.bc.GetLotteryCost(pondType) * times,
		OutsideTime:    outsideTime,
		UseReasionType: uint32(backpackBasePb.LogType_LOG_TYPE_GLORY_WORLD_MAGIC_USE),
	})
	if err != nil && protocol.ToServerError(err).Code() == status.ErrBackpackUserNotEnoughItem {
		err := mgr.store.UpdateConsumerOrderStatus(ctx, payOrderId, store.ConsumeOrderStatusFail, outsideTime)
		if err != nil {
			log.ErrorWithCtx(ctx, "Lottery.UpdateConsumerOrderStatus, orderIe: %d, err: %v", payOrderId, err)
		}
		mgr.cache.AddUserLotteryTimes(ctx, times, uid) // 回退抽奖次数
		return protocol.NewExactServerError(nil, status.ErrGloryMagicOutOfPrestige)
	}
	if err != nil {
		log.ErrorWithCtx(ctx, "Lottery.pay, uid: %d, err: %v", uid, err)
		return err
	}
	mErr := mgr.store.UpdateConsumerOrderStatus(ctx, payOrderId, store.ConsumeOrderStatusPaySuccess, outsideTime)
	if mErr != nil {
		log.ErrorWithCtx(ctx, "Lottery.UpdateConsumerOrderStatus, orderIe: %d, err: %v", payOrderId, mErr)
		return mErr
	}
	return nil
}

func (mgr *Mgr) sendAward(ctx context.Context, payOrderId string, lotteryRecordList []*store.LotteryRecord, outsideTime uint32) error {
	err := mgr.store.Transaction(ctx, func(tx mysql.Tx) error {
		err := mgr.store.BatchAddLotteryRecordTx(ctx, tx, lotteryRecordList)
		if err != nil {
			log.ErrorWithCtx(ctx, "Lottery.BatchAddLotteryRecord,payOrderId: %d, err: %v", payOrderId, err)
			return err
		}
		// 发奖记录入库并标记订单完成, 依靠补单保障发送效果
		err = mgr.store.UpdateConsumerOrderStatusTx(ctx, tx, payOrderId, store.ConsumeOrderStatusFinish, outsideTime)
		if err != nil {
			log.ErrorWithCtx(ctx, "Lottery.UpdateConsumerOrderStatusTx, orderIe: %d, err: %v", payOrderId, err)
			return err
		}
		return nil
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "Lottery.sendAward insert db fail, orderIe: %d, err: %v", payOrderId, err)
		return err
	}

	// 发奖
	go func() {
		ctx, cancel := protogrpc.NewContextWithInfoTimeout(ctx, time.Second*10)
		defer cancel()
		for _, item := range lotteryRecordList {
			err := mgr.awardHelper.GetHelper(item.ItemType).Send(ctx, item.Uid, item.OrderId, &pb.ChanceConf{
				ItemType: item.ItemType,
				ItemId:   item.ItemId,
				DayCount: item.DayCnt,
			}, item.OutsideTime)
			if err != nil {
				log.ErrorWithCtx(ctx, "Lottery.sendPrize,payOrderId: %d, err: %v", payOrderId, err)
			} else {
				mgr.store.UpdateRecordStatus(ctx, item.OrderId, store.LotteryRecordSent)
			}
		}
	}()

	return nil
}

func (mgr *Mgr) prize2LotteryRecord(ctx context.Context, pondType, uid, outsideTime uint32, orderId string, prizeList []*prize) []*store.LotteryRecord {
	rs := make([]*store.LotteryRecord, 0, len(prizeList))

	for i, item := range prizeList {
		awardInfo, err := mgr.GetAwardInfo(ctx, item.ItemType, item.ItemId)
		if err != nil { // 这里错误忽略
			log.ErrorWithCtx(ctx, "prize2LotteryRecord.GetAwardInfo, uid: %d, itemType: %d, itemId: %s, err: %v",
				uid, item.ItemType, item.ItemId, err)
		}
		isMiniGuarantee := uint32(0)
		if item.isFlashGuarantee {
			isMiniGuarantee = 1
		}
		rs = append(rs, &store.LotteryRecord{
			PondType:        pondType,
			Uid:             uid,
			OrderId:         fmt.Sprintf("%s_%d", orderId, i),
			PayOrderId:      orderId,
			ItemId:          item.ItemId,
			ItemType:        item.ItemType,
			WinningType:     item.GetWinningType(),
			Cost:            mgr.bc.GetLotteryCost(pondType),
			Worth:           awardInfo.GetPrice(),
			DayCnt:          item.DayCount,
			NoFlashCnt:      item.noFlashCnt,
			IsMiniGuarantee: isMiniGuarantee,
			ItemName:        item.ItemName,
			OutsideTime:     outsideTime,
			CreateTime:      time.Now(),
		})
	}

	return rs
}

func (mgr *Mgr) getUserLotteryTimes(ctx context.Context, uid uint32) uint32 {
	times, err := mgr.cache.GetUserLotteryTimes(ctx, uid, mgr.bc.GetDefaultLotteryTimes())
	if err != nil {
		log.ErrorWithCtx(ctx, "Lottery.GetUserLotteryTimes fail, uid: %d, err: %v", uid, err)
		return 0
	}
	return times
}

func (mgr *Mgr) SendTTHelper(ctx context.Context, uid uint32, awardInfoList []*award_helper.AwardInfo) error {
	var awardInfoText, expireTimeText string
	maxAwardExpireTime := uint32(math.MaxUint32)
	awardInfoMergeMap := make(map[string]*award_helper.AwardInfo)
	for _, item := range awardInfoList {
		if maxAwardExpireTime > item.GetExpireTime() { // 取最小的过期时间
			maxAwardExpireTime = item.GetExpireTime()
		}
		present, ok := awardInfoMergeMap[item.GetName()] // 合并同类奖品
		if !ok {
			awardInfoMergeMap[item.GetName()] = item
			continue
		}
		present.Num += item.GetNum()
	}
	awardInfoItemText := make([]string, 0, len(awardInfoList))
	for k, v := range awardInfoMergeMap {
		awardInfoItemText = append(awardInfoItemText, fmt.Sprintf("%s*%d", k, v.GetNum()))
	}

	awardInfoText = strings.Join(awardInfoItemText, ",")
	expireTimeText = time.Unix(int64(maxAwardExpireTime), 0).Format("2006年01月02日")

	sendReq := &imApiPB.Send1V1ExtMsgReq{
		From: &imApiPB.User{
			Uid: 10000,
		},
		To: &imApiPB.User{
			Uid: uid,
		},
		Msg: &imApiPB.ExtMsg{
			MsgType: uint32(imPB.IM_MSG_TYPE_TEXT_MSG),
			Content: fmt.Sprintf("恭喜您在荣耀世界-魔法祈愿中获得%s，奖励已发放至背包，请于%s前使用", awardInfoText, expireTimeText),
		},
		Opt: &imApiPB.SendOption{
			IgnoreFrom: true,
		},
		Namespace: "USER_RECALL_AWARD",
	}
	_, err := mgr.rpcClient.ImApiClient.Send1V1ExtMsg(ctx, sendReq)
	if err != nil {
		log.ErrorWithCtx(ctx, "imApiClient.Send1V1ExtMsg err=%v", err) //消息推送报错，就只打日志了
	}
	return err

	return nil
}
