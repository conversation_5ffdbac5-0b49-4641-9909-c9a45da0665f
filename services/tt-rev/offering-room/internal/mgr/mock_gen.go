// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/services/tt-rev/offering-room/internal/mgr (interfaces: ApplyListMgr,ApplyListCache,Convertor,GameCache,GameStore,GameMgr,IOfferingConfig,RecordHostLeaveMic,OfferingConfigMgr,OrderStore,OrderMgr,PresentOrderStore,PresentOrderMgr,RelationshipMgr,RelationshipStore,Transaction,CacheAside,RelationshipNotExistMarkerCache)

// Package mgr is a generated GoMock package.
package mgr

import (
	context "context"
	reflect "reflect"
	time "time"

	gomock "github.com/golang/mock/gomock"
	info "gitlab.ttyuyin.com/avengers/tyr/core/service/basepb/info"
	offer_room "golang.52tt.com/protocol/services/offer-room"
)

// MockApplyListMgr is a mock of ApplyListMgr interface.
type MockApplyListMgr struct {
	ctrl     *gomock.Controller
	recorder *MockApplyListMgrMockRecorder
}

// MockApplyListMgrMockRecorder is the mock recorder for MockApplyListMgr.
type MockApplyListMgrMockRecorder struct {
	mock *MockApplyListMgr
}

// NewMockApplyListMgr creates a new mock instance.
func NewMockApplyListMgr(ctrl *gomock.Controller) *MockApplyListMgr {
	mock := &MockApplyListMgr{ctrl: ctrl}
	mock.recorder = &MockApplyListMgrMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockApplyListMgr) EXPECT() *MockApplyListMgrMockRecorder {
	return m.recorder
}

// Add mocks base method.
func (m *MockApplyListMgr) Add(arg0 context.Context, arg1, arg2 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Add", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// Add indicates an expected call of Add.
func (mr *MockApplyListMgrMockRecorder) Add(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Add", reflect.TypeOf((*MockApplyListMgr)(nil).Add), arg0, arg1, arg2)
}

// Clear mocks base method.
func (m *MockApplyListMgr) Clear(arg0 context.Context, arg1 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Clear", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// Clear indicates an expected call of Clear.
func (mr *MockApplyListMgrMockRecorder) Clear(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Clear", reflect.TypeOf((*MockApplyListMgr)(nil).Clear), arg0, arg1)
}

// GetList mocks base method.
func (m *MockApplyListMgr) GetList(arg0 context.Context, arg1 uint32) (*ApplyList, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetList", arg0, arg1)
	ret0, _ := ret[0].(*ApplyList)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetList indicates an expected call of GetList.
func (mr *MockApplyListMgrMockRecorder) GetList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetList", reflect.TypeOf((*MockApplyListMgr)(nil).GetList), arg0, arg1)
}

// Remove mocks base method.
func (m *MockApplyListMgr) Remove(arg0 context.Context, arg1, arg2 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Remove", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// Remove indicates an expected call of Remove.
func (mr *MockApplyListMgrMockRecorder) Remove(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Remove", reflect.TypeOf((*MockApplyListMgr)(nil).Remove), arg0, arg1, arg2)
}

// MockApplyListCache is a mock of ApplyListCache interface.
type MockApplyListCache struct {
	ctrl     *gomock.Controller
	recorder *MockApplyListCacheMockRecorder
}

// MockApplyListCacheMockRecorder is the mock recorder for MockApplyListCache.
type MockApplyListCacheMockRecorder struct {
	mock *MockApplyListCache
}

// NewMockApplyListCache creates a new mock instance.
func NewMockApplyListCache(ctrl *gomock.Controller) *MockApplyListCache {
	mock := &MockApplyListCache{ctrl: ctrl}
	mock.recorder = &MockApplyListCacheMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockApplyListCache) EXPECT() *MockApplyListCacheMockRecorder {
	return m.recorder
}

// Append mocks base method.
func (m *MockApplyListCache) Append(arg0 context.Context, arg1 uint32, arg2 *ApplyListItem) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Append", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// Append indicates an expected call of Append.
func (mr *MockApplyListCacheMockRecorder) Append(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Append", reflect.TypeOf((*MockApplyListCache)(nil).Append), arg0, arg1, arg2)
}

// Clear mocks base method.
func (m *MockApplyListCache) Clear(arg0 context.Context, arg1 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Clear", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// Clear indicates an expected call of Clear.
func (mr *MockApplyListCacheMockRecorder) Clear(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Clear", reflect.TypeOf((*MockApplyListCache)(nil).Clear), arg0, arg1)
}

// Count mocks base method.
func (m *MockApplyListCache) Count(arg0 context.Context, arg1 uint32) (uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Count", arg0, arg1)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Count indicates an expected call of Count.
func (mr *MockApplyListCacheMockRecorder) Count(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Count", reflect.TypeOf((*MockApplyListCache)(nil).Count), arg0, arg1)
}

// DeleteItem mocks base method.
func (m *MockApplyListCache) DeleteItem(arg0 context.Context, arg1, arg2 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteItem", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteItem indicates an expected call of DeleteItem.
func (mr *MockApplyListCacheMockRecorder) DeleteItem(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteItem", reflect.TypeOf((*MockApplyListCache)(nil).DeleteItem), arg0, arg1, arg2)
}

// FetchByChannelId mocks base method.
func (m *MockApplyListCache) FetchByChannelId(arg0 context.Context, arg1 uint32) ([]*ApplyListItem, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FetchByChannelId", arg0, arg1)
	ret0, _ := ret[0].([]*ApplyListItem)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FetchByChannelId indicates an expected call of FetchByChannelId.
func (mr *MockApplyListCacheMockRecorder) FetchByChannelId(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FetchByChannelId", reflect.TypeOf((*MockApplyListCache)(nil).FetchByChannelId), arg0, arg1)
}

// MockConvertor is a mock of Convertor interface.
type MockConvertor struct {
	ctrl     *gomock.Controller
	recorder *MockConvertorMockRecorder
}

// MockConvertorMockRecorder is the mock recorder for MockConvertor.
type MockConvertorMockRecorder struct {
	mock *MockConvertor
}

// NewMockConvertor creates a new mock instance.
func NewMockConvertor(ctrl *gomock.Controller) *MockConvertor {
	mock := &MockConvertor{ctrl: ctrl}
	mock.recorder = &MockConvertorMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockConvertor) EXPECT() *MockConvertorMockRecorder {
	return m.recorder
}

// ApplyListToPb mocks base method.
func (m *MockConvertor) ApplyListToPb(arg0 *ApplyList) *offer_room.ApplyList {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ApplyListToPb", arg0)
	ret0, _ := ret[0].(*offer_room.ApplyList)
	return ret0
}

// ApplyListToPb indicates an expected call of ApplyListToPb.
func (mr *MockConvertorMockRecorder) ApplyListToPb(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ApplyListToPb", reflect.TypeOf((*MockConvertor)(nil).ApplyListToPb), arg0)
}

// ConfigGiftInfoToPb mocks base method.
func (m *MockConvertor) ConfigGiftInfoToPb(arg0 *Gift) *offer_room.GiftInfo {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ConfigGiftInfoToPb", arg0)
	ret0, _ := ret[0].(*offer_room.GiftInfo)
	return ret0
}

// ConfigGiftInfoToPb indicates an expected call of ConfigGiftInfoToPb.
func (mr *MockConvertorMockRecorder) ConfigGiftInfoToPb(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ConfigGiftInfoToPb", reflect.TypeOf((*MockConvertor)(nil).ConfigGiftInfoToPb), arg0)
}

// GameGiftInfoToPb mocks base method.
func (m *MockConvertor) GameGiftInfoToPb(arg0 *GameGiftInfo) *offer_room.GiftInfo {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GameGiftInfoToPb", arg0)
	ret0, _ := ret[0].(*offer_room.GiftInfo)
	return ret0
}

// GameGiftInfoToPb indicates an expected call of GameGiftInfoToPb.
func (mr *MockConvertorMockRecorder) GameGiftInfoToPb(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GameGiftInfoToPb", reflect.TypeOf((*MockConvertor)(nil).GameGiftInfoToPb), arg0)
}

// GameInfoToPb mocks base method.
func (m *MockConvertor) GameInfoToPb(arg0 *Game) *offer_room.CurOfferingGameInfo {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GameInfoToPb", arg0)
	ret0, _ := ret[0].(*offer_room.CurOfferingGameInfo)
	return ret0
}

// GameInfoToPb indicates an expected call of GameInfoToPb.
func (mr *MockConvertorMockRecorder) GameInfoToPb(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GameInfoToPb", reflect.TypeOf((*MockConvertor)(nil).GameInfoToPb), arg0)
}

// GamePhaseToPb mocks base method.
func (m *MockConvertor) GamePhaseToPb(arg0 GamePhase) offer_room.GamePhase {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GamePhaseToPb", arg0)
	ret0, _ := ret[0].(offer_room.GamePhase)
	return ret0
}

// GamePhaseToPb indicates an expected call of GamePhaseToPb.
func (mr *MockConvertorMockRecorder) GamePhaseToPb(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GamePhaseToPb", reflect.TypeOf((*MockConvertor)(nil).GamePhaseToPb), arg0)
}

// GameResultToPb mocks base method.
func (m *MockConvertor) GameResultToPb(arg0 *GameSettleResult) *offer_room.SettleRelationship {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GameResultToPb", arg0)
	ret0, _ := ret[0].(*offer_room.SettleRelationship)
	return ret0
}

// GameResultToPb indicates an expected call of GameResultToPb.
func (mr *MockConvertorMockRecorder) GameResultToPb(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GameResultToPb", reflect.TypeOf((*MockConvertor)(nil).GameResultToPb), arg0)
}

// GameSettingToPb mocks base method.
func (m *MockConvertor) GameSettingToPb(arg0 *GameSetting) *offer_room.GameSetting {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GameSettingToPb", arg0)
	ret0, _ := ret[0].(*offer_room.GameSetting)
	return ret0
}

// GameSettingToPb indicates an expected call of GameSettingToPb.
func (mr *MockConvertorMockRecorder) GameSettingToPb(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GameSettingToPb", reflect.TypeOf((*MockConvertor)(nil).GameSettingToPb), arg0)
}

// LimitedRelationshipConfigListPbToBiz mocks base method.
func (m *MockConvertor) LimitedRelationshipConfigListPbToBiz(arg0 []*offer_room.LimitedRelationshipsConfig) []*LimitedRelationshipsConfig {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "LimitedRelationshipConfigListPbToBiz", arg0)
	ret0, _ := ret[0].([]*LimitedRelationshipsConfig)
	return ret0
}

// LimitedRelationshipConfigListPbToBiz indicates an expected call of LimitedRelationshipConfigListPbToBiz.
func (mr *MockConvertorMockRecorder) LimitedRelationshipConfigListPbToBiz(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "LimitedRelationshipConfigListPbToBiz", reflect.TypeOf((*MockConvertor)(nil).LimitedRelationshipConfigListPbToBiz), arg0)
}

// LimitedRelationshipConfigListToPb mocks base method.
func (m *MockConvertor) LimitedRelationshipConfigListToPb(arg0 []*LimitedRelationshipsConfig) []*offer_room.LimitedRelationshipsConfig {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "LimitedRelationshipConfigListToPb", arg0)
	ret0, _ := ret[0].([]*offer_room.LimitedRelationshipsConfig)
	return ret0
}

// LimitedRelationshipConfigListToPb indicates an expected call of LimitedRelationshipConfigListToPb.
func (mr *MockConvertorMockRecorder) LimitedRelationshipConfigListToPb(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "LimitedRelationshipConfigListToPb", reflect.TypeOf((*MockConvertor)(nil).LimitedRelationshipConfigListToPb), arg0)
}

// LimitedRelationshipConfigPbToBiz mocks base method.
func (m *MockConvertor) LimitedRelationshipConfigPbToBiz(arg0 *offer_room.LimitedRelationshipsConfig) *LimitedRelationshipsConfig {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "LimitedRelationshipConfigPbToBiz", arg0)
	ret0, _ := ret[0].(*LimitedRelationshipsConfig)
	return ret0
}

// LimitedRelationshipConfigPbToBiz indicates an expected call of LimitedRelationshipConfigPbToBiz.
func (mr *MockConvertorMockRecorder) LimitedRelationshipConfigPbToBiz(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "LimitedRelationshipConfigPbToBiz", reflect.TypeOf((*MockConvertor)(nil).LimitedRelationshipConfigPbToBiz), arg0)
}

// LimitedRelationshipConfigToPb mocks base method.
func (m *MockConvertor) LimitedRelationshipConfigToPb(arg0 *LimitedRelationshipsConfig) *offer_room.LimitedRelationshipsConfig {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "LimitedRelationshipConfigToPb", arg0)
	ret0, _ := ret[0].(*offer_room.LimitedRelationshipsConfig)
	return ret0
}

// LimitedRelationshipConfigToPb indicates an expected call of LimitedRelationshipConfigToPb.
func (mr *MockConvertorMockRecorder) LimitedRelationshipConfigToPb(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "LimitedRelationshipConfigToPb", reflect.TypeOf((*MockConvertor)(nil).LimitedRelationshipConfigToPb), arg0)
}

// RelationListToPb mocks base method.
func (m *MockConvertor) RelationListToPb(arg0, arg1 uint32, arg2 []*RelationInfo) []*offer_room.OfferingRelationshipInfo {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RelationListToPb", arg0, arg1, arg2)
	ret0, _ := ret[0].([]*offer_room.OfferingRelationshipInfo)
	return ret0
}

// RelationListToPb indicates an expected call of RelationListToPb.
func (mr *MockConvertorMockRecorder) RelationListToPb(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RelationListToPb", reflect.TypeOf((*MockConvertor)(nil).RelationListToPb), arg0, arg1, arg2)
}

// TopListToPb mocks base method.
func (m *MockConvertor) TopListToPb(arg0 TopList) *offer_room.TopList {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TopListToPb", arg0)
	ret0, _ := ret[0].(*offer_room.TopList)
	return ret0
}

// TopListToPb indicates an expected call of TopListToPb.
func (mr *MockConvertorMockRecorder) TopListToPb(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TopListToPb", reflect.TypeOf((*MockConvertor)(nil).TopListToPb), arg0)
}

// MockGameCache is a mock of GameCache interface.
type MockGameCache struct {
	ctrl     *gomock.Controller
	recorder *MockGameCacheMockRecorder
}

// MockGameCacheMockRecorder is the mock recorder for MockGameCache.
type MockGameCacheMockRecorder struct {
	mock *MockGameCache
}

// NewMockGameCache creates a new mock instance.
func NewMockGameCache(ctrl *gomock.Controller) *MockGameCache {
	mock := &MockGameCache{ctrl: ctrl}
	mock.recorder = &MockGameCacheMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockGameCache) EXPECT() *MockGameCacheMockRecorder {
	return m.recorder
}

// BatchGetChannelOfferPhase mocks base method.
func (m *MockGameCache) BatchGetChannelOfferPhase(arg0 context.Context, arg1 []uint32) (map[uint32]*GameBaseInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetChannelOfferPhase", arg0, arg1)
	ret0, _ := ret[0].(map[uint32]*GameBaseInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetChannelOfferPhase indicates an expected call of BatchGetChannelOfferPhase.
func (mr *MockGameCacheMockRecorder) BatchGetChannelOfferPhase(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetChannelOfferPhase", reflect.TypeOf((*MockGameCache)(nil).BatchGetChannelOfferPhase), arg0, arg1)
}

// BatchGetSetting mocks base method.
func (m *MockGameCache) BatchGetSetting(arg0 context.Context, arg1 map[uint32]int64) (map[uint32]*GameSetting, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetSetting", arg0, arg1)
	ret0, _ := ret[0].(map[uint32]*GameSetting)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetSetting indicates an expected call of BatchGetSetting.
func (mr *MockGameCacheMockRecorder) BatchGetSetting(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetSetting", reflect.TypeOf((*MockGameCache)(nil).BatchGetSetting), arg0, arg1)
}

// DeleteBaseInfo mocks base method.
func (m *MockGameCache) DeleteBaseInfo(arg0 context.Context, arg1 uint32, arg2 int64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteBaseInfo", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteBaseInfo indicates an expected call of DeleteBaseInfo.
func (mr *MockGameCacheMockRecorder) DeleteBaseInfo(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteBaseInfo", reflect.TypeOf((*MockGameCache)(nil).DeleteBaseInfo), arg0, arg1, arg2)
}

// DeleteNamePrice mocks base method.
func (m *MockGameCache) DeleteNamePrice(arg0 context.Context, arg1 uint32, arg2 int64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteNamePrice", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteNamePrice indicates an expected call of DeleteNamePrice.
func (mr *MockGameCacheMockRecorder) DeleteNamePrice(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteNamePrice", reflect.TypeOf((*MockGameCache)(nil).DeleteNamePrice), arg0, arg1, arg2)
}

// DeleteNamePriceById mocks base method.
func (m *MockGameCache) DeleteNamePriceById(arg0 context.Context, arg1 uint32, arg2 int64, arg3 NamePriceUid) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteNamePriceById", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteNamePriceById indicates an expected call of DeleteNamePriceById.
func (mr *MockGameCacheMockRecorder) DeleteNamePriceById(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteNamePriceById", reflect.TypeOf((*MockGameCache)(nil).DeleteNamePriceById), arg0, arg1, arg2, arg3)
}

// DeleteResult mocks base method.
func (m *MockGameCache) DeleteResult(arg0 context.Context, arg1 uint32, arg2 int64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteResult", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteResult indicates an expected call of DeleteResult.
func (mr *MockGameCacheMockRecorder) DeleteResult(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteResult", reflect.TypeOf((*MockGameCache)(nil).DeleteResult), arg0, arg1, arg2)
}

// DeleteSetting mocks base method.
func (m *MockGameCache) DeleteSetting(arg0 context.Context, arg1 uint32, arg2 int64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteSetting", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteSetting indicates an expected call of DeleteSetting.
func (mr *MockGameCacheMockRecorder) DeleteSetting(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteSetting", reflect.TypeOf((*MockGameCache)(nil).DeleteSetting), arg0, arg1, arg2)
}

// GetBaseInfo mocks base method.
func (m *MockGameCache) GetBaseInfo(arg0 context.Context, arg1 uint32, arg2 int64) (*GameBaseInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBaseInfo", arg0, arg1, arg2)
	ret0, _ := ret[0].(*GameBaseInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBaseInfo indicates an expected call of GetBaseInfo.
func (mr *MockGameCacheMockRecorder) GetBaseInfo(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBaseInfo", reflect.TypeOf((*MockGameCache)(nil).GetBaseInfo), arg0, arg1, arg2)
}

// GetCurBaseInfo mocks base method.
func (m *MockGameCache) GetCurBaseInfo(arg0 context.Context, arg1 uint32) (*GameBaseInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCurBaseInfo", arg0, arg1)
	ret0, _ := ret[0].(*GameBaseInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCurBaseInfo indicates an expected call of GetCurBaseInfo.
func (mr *MockGameCacheMockRecorder) GetCurBaseInfo(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCurBaseInfo", reflect.TypeOf((*MockGameCache)(nil).GetCurBaseInfo), arg0, arg1)
}

// GetCurTotalInfo mocks base method.
func (m *MockGameCache) GetCurTotalInfo(arg0 context.Context, arg1 uint32) (*Game, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCurTotalInfo", arg0, arg1)
	ret0, _ := ret[0].(*Game)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCurTotalInfo indicates an expected call of GetCurTotalInfo.
func (mr *MockGameCacheMockRecorder) GetCurTotalInfo(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCurTotalInfo", reflect.TypeOf((*MockGameCache)(nil).GetCurTotalInfo), arg0, arg1)
}

// GetNamePrice mocks base method.
func (m *MockGameCache) GetNamePrice(arg0 context.Context, arg1 uint32, arg2 int64, arg3 NamePriceUid) (*NamePrice, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetNamePrice", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*NamePrice)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNamePrice indicates an expected call of GetNamePrice.
func (mr *MockGameCacheMockRecorder) GetNamePrice(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNamePrice", reflect.TypeOf((*MockGameCache)(nil).GetNamePrice), arg0, arg1, arg2, arg3)
}

// GetNamePriceList mocks base method.
func (m *MockGameCache) GetNamePriceList(arg0 context.Context, arg1 uint32, arg2 int64) ([]*NamePrice, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetNamePriceList", arg0, arg1, arg2)
	ret0, _ := ret[0].([]*NamePrice)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNamePriceList indicates an expected call of GetNamePriceList.
func (mr *MockGameCacheMockRecorder) GetNamePriceList(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNamePriceList", reflect.TypeOf((*MockGameCache)(nil).GetNamePriceList), arg0, arg1, arg2)
}

// GetResult mocks base method.
func (m *MockGameCache) GetResult(arg0 context.Context, arg1 uint32, arg2 int64) (*GameSettleResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetResult", arg0, arg1, arg2)
	ret0, _ := ret[0].(*GameSettleResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetResult indicates an expected call of GetResult.
func (mr *MockGameCacheMockRecorder) GetResult(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetResult", reflect.TypeOf((*MockGameCache)(nil).GetResult), arg0, arg1, arg2)
}

// GetSetting mocks base method.
func (m *MockGameCache) GetSetting(arg0 context.Context, arg1 uint32, arg2 int64) (*GameSetting, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSetting", arg0, arg1, arg2)
	ret0, _ := ret[0].(*GameSetting)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSetting indicates an expected call of GetSetting.
func (mr *MockGameCacheMockRecorder) GetSetting(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSetting", reflect.TypeOf((*MockGameCache)(nil).GetSetting), arg0, arg1, arg2)
}

// SaveBaseInfo mocks base method.
func (m *MockGameCache) SaveBaseInfo(arg0 context.Context, arg1 *GameBaseInfo) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SaveBaseInfo", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// SaveBaseInfo indicates an expected call of SaveBaseInfo.
func (mr *MockGameCacheMockRecorder) SaveBaseInfo(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SaveBaseInfo", reflect.TypeOf((*MockGameCache)(nil).SaveBaseInfo), arg0, arg1)
}

// SaveNamePrice mocks base method.
func (m *MockGameCache) SaveNamePrice(arg0 context.Context, arg1 *NamePrice) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SaveNamePrice", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// SaveNamePrice indicates an expected call of SaveNamePrice.
func (mr *MockGameCacheMockRecorder) SaveNamePrice(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SaveNamePrice", reflect.TypeOf((*MockGameCache)(nil).SaveNamePrice), arg0, arg1)
}

// SaveResult mocks base method.
func (m *MockGameCache) SaveResult(arg0 context.Context, arg1 uint32, arg2 int64, arg3 *GameSettleResult) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SaveResult", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// SaveResult indicates an expected call of SaveResult.
func (mr *MockGameCacheMockRecorder) SaveResult(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SaveResult", reflect.TypeOf((*MockGameCache)(nil).SaveResult), arg0, arg1, arg2, arg3)
}

// SaveSetting mocks base method.
func (m *MockGameCache) SaveSetting(arg0 context.Context, arg1 uint32, arg2 int64, arg3 *GameSetting) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SaveSetting", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// SaveSetting indicates an expected call of SaveSetting.
func (mr *MockGameCacheMockRecorder) SaveSetting(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SaveSetting", reflect.TypeOf((*MockGameCache)(nil).SaveSetting), arg0, arg1, arg2, arg3)
}

// MockGameStore is a mock of GameStore interface.
type MockGameStore struct {
	ctrl     *gomock.Controller
	recorder *MockGameStoreMockRecorder
}

// MockGameStoreMockRecorder is the mock recorder for MockGameStore.
type MockGameStoreMockRecorder struct {
	mock *MockGameStore
}

// NewMockGameStore creates a new mock instance.
func NewMockGameStore(ctrl *gomock.Controller) *MockGameStore {
	mock := &MockGameStore{ctrl: ctrl}
	mock.recorder = &MockGameStoreMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockGameStore) EXPECT() *MockGameStoreMockRecorder {
	return m.recorder
}

// GetGameIdsByTime mocks base method.
func (m *MockGameStore) GetGameIdsByTime(arg0 context.Context, arg1, arg2 time.Time) ([]*Game, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGameIdsByTime", arg0, arg1, arg2)
	ret0, _ := ret[0].([]*Game)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGameIdsByTime indicates an expected call of GetGameIdsByTime.
func (mr *MockGameStoreMockRecorder) GetGameIdsByTime(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGameIdsByTime", reflect.TypeOf((*MockGameStore)(nil).GetGameIdsByTime), arg0, arg1, arg2)
}

// GetGameStartTimeById mocks base method.
func (m *MockGameStore) GetGameStartTimeById(arg0 context.Context, arg1 int64) (time.Time, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGameStartTimeById", arg0, arg1)
	ret0, _ := ret[0].(time.Time)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGameStartTimeById indicates an expected call of GetGameStartTimeById.
func (mr *MockGameStoreMockRecorder) GetGameStartTimeById(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGameStartTimeById", reflect.TypeOf((*MockGameStore)(nil).GetGameStartTimeById), arg0, arg1)
}

// Insert mocks base method.
func (m *MockGameStore) Insert(arg0 context.Context, arg1 *Game) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Insert", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// Insert indicates an expected call of Insert.
func (mr *MockGameStoreMockRecorder) Insert(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Insert", reflect.TypeOf((*MockGameStore)(nil).Insert), arg0, arg1)
}

// Update mocks base method.
func (m *MockGameStore) Update(arg0 context.Context, arg1 *Game) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Update", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// Update indicates an expected call of Update.
func (mr *MockGameStoreMockRecorder) Update(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockGameStore)(nil).Update), arg0, arg1)
}

// MockGameMgr is a mock of GameMgr interface.
type MockGameMgr struct {
	ctrl     *gomock.Controller
	recorder *MockGameMgrMockRecorder
}

// MockGameMgrMockRecorder is the mock recorder for MockGameMgr.
type MockGameMgrMockRecorder struct {
	mock *MockGameMgr
}

// NewMockGameMgr creates a new mock instance.
func NewMockGameMgr(ctrl *gomock.Controller) *MockGameMgr {
	mock := &MockGameMgr{ctrl: ctrl}
	mock.recorder = &MockGameMgrMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockGameMgr) EXPECT() *MockGameMgrMockRecorder {
	return m.recorder
}

// BatchGetChannelOfferPhase mocks base method.
func (m *MockGameMgr) BatchGetChannelOfferPhase(arg0 context.Context, arg1 []uint32) (map[uint32]*offer_room.OfferRoomPhaseInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetChannelOfferPhase", arg0, arg1)
	ret0, _ := ret[0].(map[uint32]*offer_room.OfferRoomPhaseInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetChannelOfferPhase indicates an expected call of BatchGetChannelOfferPhase.
func (mr *MockGameMgrMockRecorder) BatchGetChannelOfferPhase(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetChannelOfferPhase", reflect.TypeOf((*MockGameMgr)(nil).BatchGetChannelOfferPhase), arg0, arg1)
}

// BatchGetUserConsumptionByGameRoundId mocks base method.
func (m *MockGameMgr) BatchGetUserConsumptionByGameRoundId(arg0 context.Context, arg1 int64, arg2 time.Time) (*offer_room.BatchGetUserConsumptionByGameRoundIdResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetUserConsumptionByGameRoundId", arg0, arg1, arg2)
	ret0, _ := ret[0].(*offer_room.BatchGetUserConsumptionByGameRoundIdResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetUserConsumptionByGameRoundId indicates an expected call of BatchGetUserConsumptionByGameRoundId.
func (mr *MockGameMgrMockRecorder) BatchGetUserConsumptionByGameRoundId(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetUserConsumptionByGameRoundId", reflect.TypeOf((*MockGameMgr)(nil).BatchGetUserConsumptionByGameRoundId), arg0, arg1, arg2)
}

// End mocks base method.
func (m *MockGameMgr) End(arg0 context.Context, arg1 *Game) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "End", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// End indicates an expected call of End.
func (mr *MockGameMgrMockRecorder) End(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "End", reflect.TypeOf((*MockGameMgr)(nil).End), arg0, arg1)
}

// GetCurGame mocks base method.
func (m *MockGameMgr) GetCurGame(arg0 context.Context, arg1 uint32) (*Game, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCurGame", arg0, arg1)
	ret0, _ := ret[0].(*Game)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCurGame indicates an expected call of GetCurGame.
func (mr *MockGameMgrMockRecorder) GetCurGame(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCurGame", reflect.TypeOf((*MockGameMgr)(nil).GetCurGame), arg0, arg1)
}

// GetGameStartTimeById mocks base method.
func (m *MockGameMgr) GetGameStartTimeById(arg0 context.Context, arg1 int64) (time.Time, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGameStartTimeById", arg0, arg1)
	ret0, _ := ret[0].(time.Time)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGameStartTimeById indicates an expected call of GetGameStartTimeById.
func (mr *MockGameMgrMockRecorder) GetGameStartTimeById(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGameStartTimeById", reflect.TypeOf((*MockGameMgr)(nil).GetGameStartTimeById), arg0, arg1)
}

// GetTopList mocks base method.
func (m *MockGameMgr) GetTopList(arg0 context.Context, arg1 uint32, arg2 int64) (TopList, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTopList", arg0, arg1, arg2)
	ret0, _ := ret[0].(TopList)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTopList indicates an expected call of GetTopList.
func (mr *MockGameMgrMockRecorder) GetTopList(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTopList", reflect.TypeOf((*MockGameMgr)(nil).GetTopList), arg0, arg1, arg2)
}

// GetUserNamePrice mocks base method.
func (m *MockGameMgr) GetUserNamePrice(arg0 context.Context, arg1 uint32, arg2 int64, arg3 NamePriceUid) (uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserNamePrice", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserNamePrice indicates an expected call of GetUserNamePrice.
func (mr *MockGameMgrMockRecorder) GetUserNamePrice(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserNamePrice", reflect.TypeOf((*MockGameMgr)(nil).GetUserNamePrice), arg0, arg1, arg2, arg3)
}

// InitNewGame mocks base method.
func (m *MockGameMgr) InitNewGame(arg0 context.Context, arg1, arg2 uint32) (*Game, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InitNewGame", arg0, arg1, arg2)
	ret0, _ := ret[0].(*Game)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// InitNewGame indicates an expected call of InitNewGame.
func (mr *MockGameMgrMockRecorder) InitNewGame(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InitNewGame", reflect.TypeOf((*MockGameMgr)(nil).InitNewGame), arg0, arg1, arg2)
}

// LockPrice mocks base method.
func (m *MockGameMgr) LockPrice(arg0 context.Context, arg1 uint32, arg2 int64, arg3 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "LockPrice", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// LockPrice indicates an expected call of LockPrice.
func (mr *MockGameMgrMockRecorder) LockPrice(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "LockPrice", reflect.TypeOf((*MockGameMgr)(nil).LockPrice), arg0, arg1, arg2, arg3)
}

// RemoveNamePrice mocks base method.
func (m *MockGameMgr) RemoveNamePrice(arg0 context.Context, arg1 uint32, arg2 int64, arg3 NamePriceUid) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RemoveNamePrice", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// RemoveNamePrice indicates an expected call of RemoveNamePrice.
func (mr *MockGameMgrMockRecorder) RemoveNamePrice(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RemoveNamePrice", reflect.TypeOf((*MockGameMgr)(nil).RemoveNamePrice), arg0, arg1, arg2, arg3)
}

// Settle mocks base method.
func (m *MockGameMgr) Settle(arg0 context.Context, arg1 *Game, arg2 *NamePrice) (*Game, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Settle", arg0, arg1, arg2)
	ret0, _ := ret[0].(*Game)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Settle indicates an expected call of Settle.
func (mr *MockGameMgrMockRecorder) Settle(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Settle", reflect.TypeOf((*MockGameMgr)(nil).Settle), arg0, arg1, arg2)
}

// SubmitSetting mocks base method.
func (m *MockGameMgr) SubmitSetting(arg0 context.Context, arg1 uint32, arg2 int64, arg3 *GameSetting) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SubmitSetting", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// SubmitSetting indicates an expected call of SubmitSetting.
func (mr *MockGameMgrMockRecorder) SubmitSetting(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SubmitSetting", reflect.TypeOf((*MockGameMgr)(nil).SubmitSetting), arg0, arg1, arg2, arg3)
}

// UpdateUserNamePrice mocks base method.
func (m *MockGameMgr) UpdateUserNamePrice(arg0 context.Context, arg1 uint32, arg2 int64, arg3 NamePriceUid, arg4 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateUserNamePrice", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateUserNamePrice indicates an expected call of UpdateUserNamePrice.
func (mr *MockGameMgrMockRecorder) UpdateUserNamePrice(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateUserNamePrice", reflect.TypeOf((*MockGameMgr)(nil).UpdateUserNamePrice), arg0, arg1, arg2, arg3, arg4)
}

// MockIOfferingConfig is a mock of IOfferingConfig interface.
type MockIOfferingConfig struct {
	ctrl     *gomock.Controller
	recorder *MockIOfferingConfigMockRecorder
}

// MockIOfferingConfigMockRecorder is the mock recorder for MockIOfferingConfig.
type MockIOfferingConfigMockRecorder struct {
	mock *MockIOfferingConfig
}

// NewMockIOfferingConfig creates a new mock instance.
func NewMockIOfferingConfig(ctrl *gomock.Controller) *MockIOfferingConfig {
	mock := &MockIOfferingConfig{ctrl: ctrl}
	mock.recorder = &MockIOfferingConfigMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIOfferingConfig) EXPECT() *MockIOfferingConfigMockRecorder {
	return m.recorder
}

// AddConfig mocks base method.
func (m *MockIOfferingConfig) AddConfig(arg0 context.Context, arg1 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddConfig", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddConfig indicates an expected call of AddConfig.
func (mr *MockIOfferingConfigMockRecorder) AddConfig(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddConfig", reflect.TypeOf((*MockIOfferingConfig)(nil).AddConfig), arg0, arg1)
}

// GetConfig mocks base method.
func (m *MockIOfferingConfig) GetConfig(arg0 context.Context, arg1 uint32) (string, uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetConfig", arg0, arg1)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(uint32)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetConfig indicates an expected call of GetConfig.
func (mr *MockIOfferingConfigMockRecorder) GetConfig(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetConfig", reflect.TypeOf((*MockIOfferingConfig)(nil).GetConfig), arg0, arg1)
}

// MockRecordHostLeaveMic is a mock of RecordHostLeaveMic interface.
type MockRecordHostLeaveMic struct {
	ctrl     *gomock.Controller
	recorder *MockRecordHostLeaveMicMockRecorder
}

// MockRecordHostLeaveMicMockRecorder is the mock recorder for MockRecordHostLeaveMic.
type MockRecordHostLeaveMicMockRecorder struct {
	mock *MockRecordHostLeaveMic
}

// NewMockRecordHostLeaveMic creates a new mock instance.
func NewMockRecordHostLeaveMic(ctrl *gomock.Controller) *MockRecordHostLeaveMic {
	mock := &MockRecordHostLeaveMic{ctrl: ctrl}
	mock.recorder = &MockRecordHostLeaveMicMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockRecordHostLeaveMic) EXPECT() *MockRecordHostLeaveMicMockRecorder {
	return m.recorder
}

// Add mocks base method.
func (m *MockRecordHostLeaveMic) Add(arg0 context.Context, arg1 uint32, arg2 int64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Add", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// Add indicates an expected call of Add.
func (mr *MockRecordHostLeaveMicMockRecorder) Add(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Add", reflect.TypeOf((*MockRecordHostLeaveMic)(nil).Add), arg0, arg1, arg2)
}

// GetExpiredChannels mocks base method.
func (m *MockRecordHostLeaveMic) GetExpiredChannels(arg0 context.Context, arg1 int64) ([]uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetExpiredChannels", arg0, arg1)
	ret0, _ := ret[0].([]uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetExpiredChannels indicates an expected call of GetExpiredChannels.
func (mr *MockRecordHostLeaveMicMockRecorder) GetExpiredChannels(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetExpiredChannels", reflect.TypeOf((*MockRecordHostLeaveMic)(nil).GetExpiredChannels), arg0, arg1)
}

// Remove mocks base method.
func (m *MockRecordHostLeaveMic) Remove(arg0 context.Context, arg1 []uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Remove", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// Remove indicates an expected call of Remove.
func (mr *MockRecordHostLeaveMicMockRecorder) Remove(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Remove", reflect.TypeOf((*MockRecordHostLeaveMic)(nil).Remove), arg0, arg1)
}

// MockOfferingConfigMgr is a mock of OfferingConfigMgr interface.
type MockOfferingConfigMgr struct {
	ctrl     *gomock.Controller
	recorder *MockOfferingConfigMgrMockRecorder
}

// MockOfferingConfigMgrMockRecorder is the mock recorder for MockOfferingConfigMgr.
type MockOfferingConfigMgrMockRecorder struct {
	mock *MockOfferingConfigMgr
}

// NewMockOfferingConfigMgr creates a new mock instance.
func NewMockOfferingConfigMgr(ctrl *gomock.Controller) *MockOfferingConfigMgr {
	mock := &MockOfferingConfigMgr{ctrl: ctrl}
	mock.recorder = &MockOfferingConfigMgrMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockOfferingConfigMgr) EXPECT() *MockOfferingConfigMgrMockRecorder {
	return m.recorder
}

// GetConfigWithSpecialGift mocks base method.
func (m *MockOfferingConfigMgr) GetConfigWithSpecialGift(arg0 context.Context, arg1, arg2, arg3 uint32) (*OfferingConfig, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetConfigWithSpecialGift", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*OfferingConfig)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetConfigWithSpecialGift indicates an expected call of GetConfigWithSpecialGift.
func (mr *MockOfferingConfigMgrMockRecorder) GetConfigWithSpecialGift(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetConfigWithSpecialGift", reflect.TypeOf((*MockOfferingConfigMgr)(nil).GetConfigWithSpecialGift), arg0, arg1, arg2, arg3)
}

// GetOfferGeneralConfig mocks base method.
func (m *MockOfferingConfigMgr) GetOfferGeneralConfig(arg0 context.Context, arg1 *offer_room.GetOfferGeneralConfigRequest) (*offer_room.GetOfferGeneralConfigResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOfferGeneralConfig", arg0, arg1)
	ret0, _ := ret[0].(*offer_room.GetOfferGeneralConfigResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOfferGeneralConfig indicates an expected call of GetOfferGeneralConfig.
func (mr *MockOfferingConfigMgrMockRecorder) GetOfferGeneralConfig(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOfferGeneralConfig", reflect.TypeOf((*MockOfferingConfigMgr)(nil).GetOfferGeneralConfig), arg0, arg1)
}

// SetOfferGeneralConfig mocks base method.
func (m *MockOfferingConfigMgr) SetOfferGeneralConfig(arg0 context.Context, arg1 *offer_room.SetOfferGeneralConfigRequest) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetOfferGeneralConfig", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetOfferGeneralConfig indicates an expected call of SetOfferGeneralConfig.
func (mr *MockOfferingConfigMgrMockRecorder) SetOfferGeneralConfig(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetOfferGeneralConfig", reflect.TypeOf((*MockOfferingConfigMgr)(nil).SetOfferGeneralConfig), arg0, arg1)
}

// MockOrderStore is a mock of OrderStore interface.
type MockOrderStore struct {
	ctrl     *gomock.Controller
	recorder *MockOrderStoreMockRecorder
}

// MockOrderStoreMockRecorder is the mock recorder for MockOrderStore.
type MockOrderStoreMockRecorder struct {
	mock *MockOrderStore
}

// NewMockOrderStore creates a new mock instance.
func NewMockOrderStore(ctrl *gomock.Controller) *MockOrderStore {
	mock := &MockOrderStore{ctrl: ctrl}
	mock.recorder = &MockOrderStoreMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockOrderStore) EXPECT() *MockOrderStoreMockRecorder {
	return m.recorder
}

// BatchUpdateStatusByGameId mocks base method.
func (m *MockOrderStore) BatchUpdateStatusByGameId(arg0 context.Context, arg1 time.Time, arg2 int64, arg3, arg4 OrderStatus) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchUpdateStatusByGameId", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchUpdateStatusByGameId indicates an expected call of BatchUpdateStatusByGameId.
func (mr *MockOrderStoreMockRecorder) BatchUpdateStatusByGameId(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchUpdateStatusByGameId", reflect.TypeOf((*MockOrderStore)(nil).BatchUpdateStatusByGameId), arg0, arg1, arg2, arg3, arg4)
}

// BatchUpdateStatusByIds mocks base method.
func (m *MockOrderStore) BatchUpdateStatusByIds(arg0 context.Context, arg1 time.Time, arg2 []int64, arg3, arg4 OrderStatus) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchUpdateStatusByIds", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchUpdateStatusByIds indicates an expected call of BatchUpdateStatusByIds.
func (mr *MockOrderStoreMockRecorder) BatchUpdateStatusByIds(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchUpdateStatusByIds", reflect.TypeOf((*MockOrderStore)(nil).BatchUpdateStatusByIds), arg0, arg1, arg2, arg3, arg4)
}

// CountOrderNumAndValueByGameRoundIdsAndStatus mocks base method.
func (m *MockOrderStore) CountOrderNumAndValueByGameRoundIdsAndStatus(arg0 context.Context, arg1 []time.Time, arg2 []int64, arg3 OrderStatus, arg4, arg5 time.Time) (uint32, uint64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CountOrderNumAndValueByGameRoundIdsAndStatus", arg0, arg1, arg2, arg3, arg4, arg5)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(uint64)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// CountOrderNumAndValueByGameRoundIdsAndStatus indicates an expected call of CountOrderNumAndValueByGameRoundIdsAndStatus.
func (mr *MockOrderStoreMockRecorder) CountOrderNumAndValueByGameRoundIdsAndStatus(arg0, arg1, arg2, arg3, arg4, arg5 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CountOrderNumAndValueByGameRoundIdsAndStatus", reflect.TypeOf((*MockOrderStore)(nil).CountOrderNumAndValueByGameRoundIdsAndStatus), arg0, arg1, arg2, arg3, arg4, arg5)
}

// CountUserConsumptionByGameRoundIdAndStatus mocks base method.
func (m *MockOrderStore) CountUserConsumptionByGameRoundIdAndStatus(arg0 context.Context, arg1 time.Time, arg2 int64, arg3 []OrderStatus) (map[uint32]uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CountUserConsumptionByGameRoundIdAndStatus", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(map[uint32]uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CountUserConsumptionByGameRoundIdAndStatus indicates an expected call of CountUserConsumptionByGameRoundIdAndStatus.
func (mr *MockOrderStoreMockRecorder) CountUserConsumptionByGameRoundIdAndStatus(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CountUserConsumptionByGameRoundIdAndStatus", reflect.TypeOf((*MockOrderStore)(nil).CountUserConsumptionByGameRoundIdAndStatus), arg0, arg1, arg2, arg3)
}

// GetOrderByOrderSn mocks base method.
func (m *MockOrderStore) GetOrderByOrderSn(arg0 context.Context, arg1 time.Time, arg2 string) (*Order, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOrderByOrderSn", arg0, arg1, arg2)
	ret0, _ := ret[0].(*Order)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOrderByOrderSn indicates an expected call of GetOrderByOrderSn.
func (mr *MockOrderStoreMockRecorder) GetOrderByOrderSn(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOrderByOrderSn", reflect.TypeOf((*MockOrderStore)(nil).GetOrderByOrderSn), arg0, arg1, arg2)
}

// GetOrderListByGameAndUser mocks base method.
func (m *MockOrderStore) GetOrderListByGameAndUser(arg0 context.Context, arg1 time.Time, arg2 uint32, arg3 int64, arg4 OrderStatus) (OrderList, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOrderListByGameAndUser", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(OrderList)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOrderListByGameAndUser indicates an expected call of GetOrderListByGameAndUser.
func (mr *MockOrderStoreMockRecorder) GetOrderListByGameAndUser(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOrderListByGameAndUser", reflect.TypeOf((*MockOrderStore)(nil).GetOrderListByGameAndUser), arg0, arg1, arg2, arg3, arg4)
}

// GetOrderListByGameIdAndStatusWithPage mocks base method.
func (m *MockOrderStore) GetOrderListByGameIdAndStatusWithPage(arg0 context.Context, arg1 time.Time, arg2 int64, arg3 OrderStatus, arg4, arg5 uint32) (OrderList, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOrderListByGameIdAndStatusWithPage", arg0, arg1, arg2, arg3, arg4, arg5)
	ret0, _ := ret[0].(OrderList)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOrderListByGameIdAndStatusWithPage indicates an expected call of GetOrderListByGameIdAndStatusWithPage.
func (mr *MockOrderStoreMockRecorder) GetOrderListByGameIdAndStatusWithPage(arg0, arg1, arg2, arg3, arg4, arg5 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOrderListByGameIdAndStatusWithPage", reflect.TypeOf((*MockOrderStore)(nil).GetOrderListByGameIdAndStatusWithPage), arg0, arg1, arg2, arg3, arg4, arg5)
}

// GetOrderListByStatusWithPage mocks base method.
func (m *MockOrderStore) GetOrderListByStatusWithPage(arg0 context.Context, arg1 time.Time, arg2 OrderStatus, arg3, arg4 uint32) (OrderList, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOrderListByStatusWithPage", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(OrderList)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOrderListByStatusWithPage indicates an expected call of GetOrderListByStatusWithPage.
func (mr *MockOrderStoreMockRecorder) GetOrderListByStatusWithPage(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOrderListByStatusWithPage", reflect.TypeOf((*MockOrderStore)(nil).GetOrderListByStatusWithPage), arg0, arg1, arg2, arg3, arg4)
}

// GetOrderSnsByGameRoundIdsAndStatus mocks base method.
func (m *MockOrderStore) GetOrderSnsByGameRoundIdsAndStatus(arg0 context.Context, arg1 []time.Time, arg2 []int64, arg3 OrderStatus, arg4, arg5 time.Time) ([]string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOrderSnsByGameRoundIdsAndStatus", arg0, arg1, arg2, arg3, arg4, arg5)
	ret0, _ := ret[0].([]string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOrderSnsByGameRoundIdsAndStatus indicates an expected call of GetOrderSnsByGameRoundIdsAndStatus.
func (mr *MockOrderStoreMockRecorder) GetOrderSnsByGameRoundIdsAndStatus(arg0, arg1, arg2, arg3, arg4, arg5 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOrderSnsByGameRoundIdsAndStatus", reflect.TypeOf((*MockOrderStore)(nil).GetOrderSnsByGameRoundIdsAndStatus), arg0, arg1, arg2, arg3, arg4, arg5)
}

// Insert mocks base method.
func (m *MockOrderStore) Insert(arg0 context.Context, arg1 time.Time, arg2 *Order) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Insert", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// Insert indicates an expected call of Insert.
func (mr *MockOrderStoreMockRecorder) Insert(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Insert", reflect.TypeOf((*MockOrderStore)(nil).Insert), arg0, arg1, arg2)
}

// UpdateStatusAndTBeanSysAndDealTokenTimeById mocks base method.
func (m *MockOrderStore) UpdateStatusAndTBeanSysAndDealTokenTimeById(arg0 context.Context, arg1 time.Time, arg2 int64, arg3, arg4 OrderStatus, arg5 time.Time, arg6 string) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateStatusAndTBeanSysAndDealTokenTimeById", arg0, arg1, arg2, arg3, arg4, arg5, arg6)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateStatusAndTBeanSysAndDealTokenTimeById indicates an expected call of UpdateStatusAndTBeanSysAndDealTokenTimeById.
func (mr *MockOrderStoreMockRecorder) UpdateStatusAndTBeanSysAndDealTokenTimeById(arg0, arg1, arg2, arg3, arg4, arg5, arg6 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateStatusAndTBeanSysAndDealTokenTimeById", reflect.TypeOf((*MockOrderStore)(nil).UpdateStatusAndTBeanSysAndDealTokenTimeById), arg0, arg1, arg2, arg3, arg4, arg5, arg6)
}

// UpdateStatusById mocks base method.
func (m *MockOrderStore) UpdateStatusById(arg0 context.Context, arg1 time.Time, arg2 int64, arg3, arg4 OrderStatus) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateStatusById", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateStatusById indicates an expected call of UpdateStatusById.
func (mr *MockOrderStoreMockRecorder) UpdateStatusById(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateStatusById", reflect.TypeOf((*MockOrderStore)(nil).UpdateStatusById), arg0, arg1, arg2, arg3, arg4)
}

// MockOrderMgr is a mock of OrderMgr interface.
type MockOrderMgr struct {
	ctrl     *gomock.Controller
	recorder *MockOrderMgrMockRecorder
}

// MockOrderMgrMockRecorder is the mock recorder for MockOrderMgr.
type MockOrderMgrMockRecorder struct {
	mock *MockOrderMgr
}

// NewMockOrderMgr creates a new mock instance.
func NewMockOrderMgr(ctrl *gomock.Controller) *MockOrderMgr {
	mock := &MockOrderMgr{ctrl: ctrl}
	mock.recorder = &MockOrderMgrMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockOrderMgr) EXPECT() *MockOrderMgrMockRecorder {
	return m.recorder
}

// AsyncProcessOrder mocks base method.
func (m *MockOrderMgr) AsyncProcessOrder(arg0 int64, arg1 time.Time) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "AsyncProcessOrder", arg0, arg1)
}

// AsyncProcessOrder indicates an expected call of AsyncProcessOrder.
func (mr *MockOrderMgrMockRecorder) AsyncProcessOrder(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AsyncProcessOrder", reflect.TypeOf((*MockOrderMgr)(nil).AsyncProcessOrder), arg0, arg1)
}

// CommitOrder mocks base method.
func (m *MockOrderMgr) CommitOrder(arg0 context.Context, arg1 time.Time, arg2 *Order) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CommitOrder", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// CommitOrder indicates an expected call of CommitOrder.
func (mr *MockOrderMgrMockRecorder) CommitOrder(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CommitOrder", reflect.TypeOf((*MockOrderMgr)(nil).CommitOrder), arg0, arg1, arg2)
}

// ConfirmByUserAndGame mocks base method.
func (m *MockOrderMgr) ConfirmByUserAndGame(arg0 context.Context, arg1, arg2 uint32, arg3 int64, arg4 time.Time) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ConfirmByUserAndGame", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(error)
	return ret0
}

// ConfirmByUserAndGame indicates an expected call of ConfirmByUserAndGame.
func (mr *MockOrderMgrMockRecorder) ConfirmByUserAndGame(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ConfirmByUserAndGame", reflect.TypeOf((*MockOrderMgr)(nil).ConfirmByUserAndGame), arg0, arg1, arg2, arg3, arg4)
}

// CountOrderNumAndValueByTime mocks base method.
func (m *MockOrderMgr) CountOrderNumAndValueByTime(arg0 context.Context, arg1 *ReconcileParam) (uint32, uint64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CountOrderNumAndValueByTime", arg0, arg1)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(uint64)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// CountOrderNumAndValueByTime indicates an expected call of CountOrderNumAndValueByTime.
func (mr *MockOrderMgrMockRecorder) CountOrderNumAndValueByTime(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CountOrderNumAndValueByTime", reflect.TypeOf((*MockOrderMgr)(nil).CountOrderNumAndValueByTime), arg0, arg1)
}

// Create mocks base method.
func (m *MockOrderMgr) Create(arg0 context.Context, arg1 time.Time, arg2 *Order) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// Create indicates an expected call of Create.
func (mr *MockOrderMgrMockRecorder) Create(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockOrderMgr)(nil).Create), arg0, arg1, arg2)
}

// GetOrderIdsByTime mocks base method.
func (m *MockOrderMgr) GetOrderIdsByTime(arg0 context.Context, arg1 *ReconcileParam) ([]string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOrderIdsByTime", arg0, arg1)
	ret0, _ := ret[0].([]string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOrderIdsByTime indicates an expected call of GetOrderIdsByTime.
func (mr *MockOrderMgrMockRecorder) GetOrderIdsByTime(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOrderIdsByTime", reflect.TypeOf((*MockOrderMgr)(nil).GetOrderIdsByTime), arg0, arg1)
}

// GetOrderInfoByOrderSn mocks base method.
func (m *MockOrderMgr) GetOrderInfoByOrderSn(arg0 context.Context, arg1 time.Time, arg2 string) (*Order, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOrderInfoByOrderSn", arg0, arg1, arg2)
	ret0, _ := ret[0].(*Order)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOrderInfoByOrderSn indicates an expected call of GetOrderInfoByOrderSn.
func (mr *MockOrderMgrMockRecorder) GetOrderInfoByOrderSn(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOrderInfoByOrderSn", reflect.TypeOf((*MockOrderMgr)(nil).GetOrderInfoByOrderSn), arg0, arg1, arg2)
}

// ProcessOrderForTimer mocks base method.
func (m *MockOrderMgr) ProcessOrderForTimer(arg0 context.Context, arg1 time.Time) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ProcessOrderForTimer", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// ProcessOrderForTimer indicates an expected call of ProcessOrderForTimer.
func (mr *MockOrderMgrMockRecorder) ProcessOrderForTimer(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessOrderForTimer", reflect.TypeOf((*MockOrderMgr)(nil).ProcessOrderForTimer), arg0, arg1)
}

// RollBackByGame mocks base method.
func (m *MockOrderMgr) RollBackByGame(arg0 context.Context, arg1 int64, arg2 time.Time) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RollBackByGame", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// RollBackByGame indicates an expected call of RollBackByGame.
func (mr *MockOrderMgrMockRecorder) RollBackByGame(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RollBackByGame", reflect.TypeOf((*MockOrderMgr)(nil).RollBackByGame), arg0, arg1, arg2)
}

// RollBackOrder mocks base method.
func (m *MockOrderMgr) RollBackOrder(arg0 context.Context, arg1 time.Time, arg2 *Order) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RollBackOrder", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// RollBackOrder indicates an expected call of RollBackOrder.
func (mr *MockOrderMgrMockRecorder) RollBackOrder(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RollBackOrder", reflect.TypeOf((*MockOrderMgr)(nil).RollBackOrder), arg0, arg1, arg2)
}

// MockPresentOrderStore is a mock of PresentOrderStore interface.
type MockPresentOrderStore struct {
	ctrl     *gomock.Controller
	recorder *MockPresentOrderStoreMockRecorder
}

// MockPresentOrderStoreMockRecorder is the mock recorder for MockPresentOrderStore.
type MockPresentOrderStoreMockRecorder struct {
	mock *MockPresentOrderStore
}

// NewMockPresentOrderStore creates a new mock instance.
func NewMockPresentOrderStore(ctrl *gomock.Controller) *MockPresentOrderStore {
	mock := &MockPresentOrderStore{ctrl: ctrl}
	mock.recorder = &MockPresentOrderStoreMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockPresentOrderStore) EXPECT() *MockPresentOrderStoreMockRecorder {
	return m.recorder
}

// CountOrderNumAndValueByTime mocks base method.
func (m *MockPresentOrderStore) CountOrderNumAndValueByTime(arg0 context.Context, arg1 []time.Time, arg2, arg3 time.Time) (uint32, uint64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CountOrderNumAndValueByTime", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(uint64)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// CountOrderNumAndValueByTime indicates an expected call of CountOrderNumAndValueByTime.
func (mr *MockPresentOrderStoreMockRecorder) CountOrderNumAndValueByTime(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CountOrderNumAndValueByTime", reflect.TypeOf((*MockPresentOrderStore)(nil).CountOrderNumAndValueByTime), arg0, arg1, arg2, arg3)
}

// Create mocks base method.
func (m *MockPresentOrderStore) Create(arg0 context.Context, arg1 *PresentOrder) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// Create indicates an expected call of Create.
func (mr *MockPresentOrderStoreMockRecorder) Create(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockPresentOrderStore)(nil).Create), arg0, arg1)
}

// GetOrderIdsByTime mocks base method.
func (m *MockPresentOrderStore) GetOrderIdsByTime(arg0 context.Context, arg1, arg2 time.Time) ([]string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOrderIdsByTime", arg0, arg1, arg2)
	ret0, _ := ret[0].([]string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOrderIdsByTime indicates an expected call of GetOrderIdsByTime.
func (mr *MockPresentOrderStoreMockRecorder) GetOrderIdsByTime(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOrderIdsByTime", reflect.TypeOf((*MockPresentOrderStore)(nil).GetOrderIdsByTime), arg0, arg1, arg2)
}

// GetOrderInfoByOrderSn mocks base method.
func (m *MockPresentOrderStore) GetOrderInfoByOrderSn(arg0 context.Context, arg1 string) (*PresentOrder, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOrderInfoByOrderSn", arg0, arg1)
	ret0, _ := ret[0].(*PresentOrder)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOrderInfoByOrderSn indicates an expected call of GetOrderInfoByOrderSn.
func (mr *MockPresentOrderStoreMockRecorder) GetOrderInfoByOrderSn(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOrderInfoByOrderSn", reflect.TypeOf((*MockPresentOrderStore)(nil).GetOrderInfoByOrderSn), arg0, arg1)
}

// MockPresentOrderMgr is a mock of PresentOrderMgr interface.
type MockPresentOrderMgr struct {
	ctrl     *gomock.Controller
	recorder *MockPresentOrderMgrMockRecorder
}

// MockPresentOrderMgrMockRecorder is the mock recorder for MockPresentOrderMgr.
type MockPresentOrderMgrMockRecorder struct {
	mock *MockPresentOrderMgr
}

// NewMockPresentOrderMgr creates a new mock instance.
func NewMockPresentOrderMgr(ctrl *gomock.Controller) *MockPresentOrderMgr {
	mock := &MockPresentOrderMgr{ctrl: ctrl}
	mock.recorder = &MockPresentOrderMgrMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockPresentOrderMgr) EXPECT() *MockPresentOrderMgrMockRecorder {
	return m.recorder
}

// AsyncSendPresent mocks base method.
func (m *MockPresentOrderMgr) AsyncSendPresent(arg0 *PresentOrder, arg1 uint32, arg2 *info.ServiceInfo) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "AsyncSendPresent", arg0, arg1, arg2)
}

// AsyncSendPresent indicates an expected call of AsyncSendPresent.
func (mr *MockPresentOrderMgrMockRecorder) AsyncSendPresent(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AsyncSendPresent", reflect.TypeOf((*MockPresentOrderMgr)(nil).AsyncSendPresent), arg0, arg1, arg2)
}

// CountOrderNumAndValueByTime mocks base method.
func (m *MockPresentOrderMgr) CountOrderNumAndValueByTime(arg0 context.Context, arg1 *ReconcileParam) (uint32, uint64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CountOrderNumAndValueByTime", arg0, arg1)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(uint64)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// CountOrderNumAndValueByTime indicates an expected call of CountOrderNumAndValueByTime.
func (mr *MockPresentOrderMgrMockRecorder) CountOrderNumAndValueByTime(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CountOrderNumAndValueByTime", reflect.TypeOf((*MockPresentOrderMgr)(nil).CountOrderNumAndValueByTime), arg0, arg1)
}

// CreateOrderByGame mocks base method.
func (m *MockPresentOrderMgr) CreateOrderByGame(arg0 context.Context, arg1 *Game, arg2 *NamePrice) ([]*PresentOrder, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateOrderByGame", arg0, arg1, arg2)
	ret0, _ := ret[0].([]*PresentOrder)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateOrderByGame indicates an expected call of CreateOrderByGame.
func (mr *MockPresentOrderMgrMockRecorder) CreateOrderByGame(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateOrderByGame", reflect.TypeOf((*MockPresentOrderMgr)(nil).CreateOrderByGame), arg0, arg1, arg2)
}

// GetOrderIdsByTime mocks base method.
func (m *MockPresentOrderMgr) GetOrderIdsByTime(arg0 context.Context, arg1 *ReconcileParam) ([]string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOrderIdsByTime", arg0, arg1)
	ret0, _ := ret[0].([]string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOrderIdsByTime indicates an expected call of GetOrderIdsByTime.
func (mr *MockPresentOrderMgrMockRecorder) GetOrderIdsByTime(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOrderIdsByTime", reflect.TypeOf((*MockPresentOrderMgr)(nil).GetOrderIdsByTime), arg0, arg1)
}

// ResendPresent mocks base method.
func (m *MockPresentOrderMgr) ResendPresent(arg0 context.Context, arg1 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ResendPresent", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// ResendPresent indicates an expected call of ResendPresent.
func (mr *MockPresentOrderMgrMockRecorder) ResendPresent(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ResendPresent", reflect.TypeOf((*MockPresentOrderMgr)(nil).ResendPresent), arg0, arg1)
}

// MockRelationshipMgr is a mock of RelationshipMgr interface.
type MockRelationshipMgr struct {
	ctrl     *gomock.Controller
	recorder *MockRelationshipMgrMockRecorder
}

// MockRelationshipMgrMockRecorder is the mock recorder for MockRelationshipMgr.
type MockRelationshipMgrMockRecorder struct {
	mock *MockRelationshipMgr
}

// NewMockRelationshipMgr creates a new mock instance.
func NewMockRelationshipMgr(ctrl *gomock.Controller) *MockRelationshipMgr {
	mock := &MockRelationshipMgr{ctrl: ctrl}
	mock.recorder = &MockRelationshipMgrMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockRelationshipMgr) EXPECT() *MockRelationshipMgrMockRecorder {
	return m.recorder
}

// BatchAddLimitedRelationship mocks base method.
func (m *MockRelationshipMgr) BatchAddLimitedRelationship(arg0 context.Context, arg1 []*LimitedRelationshipsConfig) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchAddLimitedRelationship", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// BatchAddLimitedRelationship indicates an expected call of BatchAddLimitedRelationship.
func (mr *MockRelationshipMgrMockRecorder) BatchAddLimitedRelationship(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchAddLimitedRelationship", reflect.TypeOf((*MockRelationshipMgr)(nil).BatchAddLimitedRelationship), arg0, arg1)
}

// BatchDelLimitedRelationship mocks base method.
func (m *MockRelationshipMgr) BatchDelLimitedRelationship(arg0 context.Context, arg1 []uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchDelLimitedRelationship", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// BatchDelLimitedRelationship indicates an expected call of BatchDelLimitedRelationship.
func (mr *MockRelationshipMgrMockRecorder) BatchDelLimitedRelationship(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchDelLimitedRelationship", reflect.TypeOf((*MockRelationshipMgr)(nil).BatchDelLimitedRelationship), arg0, arg1)
}

// BatchDeleteRelationshipsByUid mocks base method.
func (m *MockRelationshipMgr) BatchDeleteRelationshipsByUid(arg0 context.Context, arg1 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchDeleteRelationshipsByUid", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// BatchDeleteRelationshipsByUid indicates an expected call of BatchDeleteRelationshipsByUid.
func (mr *MockRelationshipMgrMockRecorder) BatchDeleteRelationshipsByUid(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchDeleteRelationshipsByUid", reflect.TypeOf((*MockRelationshipMgr)(nil).BatchDeleteRelationshipsByUid), arg0, arg1)
}

// CheckRelationNameAvailability mocks base method.
func (m *MockRelationshipMgr) CheckRelationNameAvailability(arg0 context.Context, arg1 *offer_room.CheckRelationNameAvailabilityRequest) (*offer_room.CheckRelationNameAvailabilityResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckRelationNameAvailability", arg0, arg1)
	ret0, _ := ret[0].(*offer_room.CheckRelationNameAvailabilityResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckRelationNameAvailability indicates an expected call of CheckRelationNameAvailability.
func (mr *MockRelationshipMgrMockRecorder) CheckRelationNameAvailability(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckRelationNameAvailability", reflect.TypeOf((*MockRelationshipMgr)(nil).CheckRelationNameAvailability), arg0, arg1)
}

// CreateRelationship mocks base method.
func (m *MockRelationshipMgr) CreateRelationship(arg0 context.Context, arg1 *offer_room.CreateRelationshipRequest) (*offer_room.CreateRelationshipResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateRelationship", arg0, arg1)
	ret0, _ := ret[0].(*offer_room.CreateRelationshipResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateRelationship indicates an expected call of CreateRelationship.
func (mr *MockRelationshipMgrMockRecorder) CreateRelationship(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateRelationship", reflect.TypeOf((*MockRelationshipMgr)(nil).CreateRelationship), arg0, arg1)
}

// DeleteRelationship mocks base method.
func (m *MockRelationshipMgr) DeleteRelationship(arg0 context.Context, arg1 *offer_room.DeleteRelationshipRequest) (*offer_room.DeleteRelationshipResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteRelationship", arg0, arg1)
	ret0, _ := ret[0].(*offer_room.DeleteRelationshipResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteRelationship indicates an expected call of DeleteRelationship.
func (mr *MockRelationshipMgrMockRecorder) DeleteRelationship(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteRelationship", reflect.TypeOf((*MockRelationshipMgr)(nil).DeleteRelationship), arg0, arg1)
}

// GetExpiredRelationList mocks base method.
func (m *MockRelationshipMgr) GetExpiredRelationList(arg0 context.Context, arg1, arg2 uint32) ([]*RelationInfo, uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetExpiredRelationList", arg0, arg1, arg2)
	ret0, _ := ret[0].([]*RelationInfo)
	ret1, _ := ret[1].(uint32)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetExpiredRelationList indicates an expected call of GetExpiredRelationList.
func (mr *MockRelationshipMgrMockRecorder) GetExpiredRelationList(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetExpiredRelationList", reflect.TypeOf((*MockRelationshipMgr)(nil).GetExpiredRelationList), arg0, arg1, arg2)
}

// GetLimitedRelationshipDetail mocks base method.
func (m *MockRelationshipMgr) GetLimitedRelationshipDetail(arg0 context.Context, arg1 uint32) (*LimitedRelationshipsConfig, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLimitedRelationshipDetail", arg0, arg1)
	ret0, _ := ret[0].(*LimitedRelationshipsConfig)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLimitedRelationshipDetail indicates an expected call of GetLimitedRelationshipDetail.
func (mr *MockRelationshipMgrMockRecorder) GetLimitedRelationshipDetail(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLimitedRelationshipDetail", reflect.TypeOf((*MockRelationshipMgr)(nil).GetLimitedRelationshipDetail), arg0, arg1)
}

// GetLimitedRelationshipsByPage mocks base method.
func (m *MockRelationshipMgr) GetLimitedRelationshipsByPage(arg0 context.Context, arg1, arg2 uint32, arg3 string) (*GetLimitedRelationshipsByPageResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLimitedRelationshipsByPage", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*GetLimitedRelationshipsByPageResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLimitedRelationshipsByPage indicates an expected call of GetLimitedRelationshipsByPage.
func (mr *MockRelationshipMgrMockRecorder) GetLimitedRelationshipsByPage(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLimitedRelationshipsByPage", reflect.TypeOf((*MockRelationshipMgr)(nil).GetLimitedRelationshipsByPage), arg0, arg1, arg2, arg3)
}

// GetNameplateConfig mocks base method.
func (m *MockRelationshipMgr) GetNameplateConfig(arg0 context.Context) (*offer_room.GetNameplateConfigResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetNameplateConfig", arg0)
	ret0, _ := ret[0].(*offer_room.GetNameplateConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNameplateConfig indicates an expected call of GetNameplateConfig.
func (mr *MockRelationshipMgrMockRecorder) GetNameplateConfig(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNameplateConfig", reflect.TypeOf((*MockRelationshipMgr)(nil).GetNameplateConfig), arg0)
}

// GetNameplateInfo mocks base method.
func (m *MockRelationshipMgr) GetNameplateInfo(arg0 context.Context, arg1, arg2 uint32) (*offer_room.GetUserNameplateInfoResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetNameplateInfo", arg0, arg1, arg2)
	ret0, _ := ret[0].(*offer_room.GetUserNameplateInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNameplateInfo indicates an expected call of GetNameplateInfo.
func (mr *MockRelationshipMgrMockRecorder) GetNameplateInfo(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNameplateInfo", reflect.TypeOf((*MockRelationshipMgr)(nil).GetNameplateInfo), arg0, arg1, arg2)
}

// OfferRoomCardInfo mocks base method.
func (m *MockRelationshipMgr) OfferRoomCardInfo(arg0 context.Context, arg1 *offer_room.OfferRoomCardInfoRequest) (*offer_room.OfferRoomCardInfoResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "OfferRoomCardInfo", arg0, arg1)
	ret0, _ := ret[0].(*offer_room.OfferRoomCardInfoResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// OfferRoomCardInfo indicates an expected call of OfferRoomCardInfo.
func (mr *MockRelationshipMgrMockRecorder) OfferRoomCardInfo(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "OfferRoomCardInfo", reflect.TypeOf((*MockRelationshipMgr)(nil).OfferRoomCardInfo), arg0, arg1)
}

// OfferingRelationships mocks base method.
func (m *MockRelationshipMgr) OfferingRelationships(arg0 context.Context, arg1 *offer_room.OfferingRelationshipsRequest) (*offer_room.OfferingRelationshipsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "OfferingRelationships", arg0, arg1)
	ret0, _ := ret[0].(*offer_room.OfferingRelationshipsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// OfferingRelationships indicates an expected call of OfferingRelationships.
func (mr *MockRelationshipMgrMockRecorder) OfferingRelationships(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "OfferingRelationships", reflect.TypeOf((*MockRelationshipMgr)(nil).OfferingRelationships), arg0, arg1)
}

// RecallRelationshipName mocks base method.
func (m *MockRelationshipMgr) RecallRelationshipName(arg0 context.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RecallRelationshipName", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// RecallRelationshipName indicates an expected call of RecallRelationshipName.
func (mr *MockRelationshipMgrMockRecorder) RecallRelationshipName(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RecallRelationshipName", reflect.TypeOf((*MockRelationshipMgr)(nil).RecallRelationshipName), arg0)
}

// RemoveNotExistMarker mocks base method.
func (m *MockRelationshipMgr) RemoveNotExistMarker(arg0 context.Context, arg1 ...uint32) error {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0}
	for _, a := range arg1 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "RemoveNotExistMarker", varargs...)
	ret0, _ := ret[0].(error)
	return ret0
}

// RemoveNotExistMarker indicates an expected call of RemoveNotExistMarker.
func (mr *MockRelationshipMgrMockRecorder) RemoveNotExistMarker(arg0 interface{}, arg1 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0}, arg1...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RemoveNotExistMarker", reflect.TypeOf((*MockRelationshipMgr)(nil).RemoveNotExistMarker), varargs...)
}

// SendTTAssistantText mocks base method.
func (m *MockRelationshipMgr) SendTTAssistantText(arg0 context.Context, arg1 uint32, arg2, arg3, arg4 string) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "SendTTAssistantText", arg0, arg1, arg2, arg3, arg4)
}

// SendTTAssistantText indicates an expected call of SendTTAssistantText.
func (mr *MockRelationshipMgrMockRecorder) SendTTAssistantText(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendTTAssistantText", reflect.TypeOf((*MockRelationshipMgr)(nil).SendTTAssistantText), arg0, arg1, arg2, arg3, arg4)
}

// UnfreezeRelationshipRecord mocks base method.
func (m *MockRelationshipMgr) UnfreezeRelationshipRecord(arg0 context.Context, arg1 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UnfreezeRelationshipRecord", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// UnfreezeRelationshipRecord indicates an expected call of UnfreezeRelationshipRecord.
func (mr *MockRelationshipMgrMockRecorder) UnfreezeRelationshipRecord(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UnfreezeRelationshipRecord", reflect.TypeOf((*MockRelationshipMgr)(nil).UnfreezeRelationshipRecord), arg0, arg1)
}

// UpdateLimitedRelationship mocks base method.
func (m *MockRelationshipMgr) UpdateLimitedRelationship(arg0 context.Context, arg1 *LimitedRelationshipsConfig) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateLimitedRelationship", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateLimitedRelationship indicates an expected call of UpdateLimitedRelationship.
func (mr *MockRelationshipMgrMockRecorder) UpdateLimitedRelationship(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateLimitedRelationship", reflect.TypeOf((*MockRelationshipMgr)(nil).UpdateLimitedRelationship), arg0, arg1)
}

// UpdateNameplateInfo mocks base method.
func (m *MockRelationshipMgr) UpdateNameplateInfo(arg0 context.Context, arg1 []uint32, arg2 int32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateNameplateInfo", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateNameplateInfo indicates an expected call of UpdateNameplateInfo.
func (mr *MockRelationshipMgrMockRecorder) UpdateNameplateInfo(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateNameplateInfo", reflect.TypeOf((*MockRelationshipMgr)(nil).UpdateNameplateInfo), arg0, arg1, arg2)
}

// MockRelationshipStore is a mock of RelationshipStore interface.
type MockRelationshipStore struct {
	ctrl     *gomock.Controller
	recorder *MockRelationshipStoreMockRecorder
}

// MockRelationshipStoreMockRecorder is the mock recorder for MockRelationshipStore.
type MockRelationshipStoreMockRecorder struct {
	mock *MockRelationshipStore
}

// NewMockRelationshipStore creates a new mock instance.
func NewMockRelationshipStore(ctrl *gomock.Controller) *MockRelationshipStore {
	mock := &MockRelationshipStore{ctrl: ctrl}
	mock.recorder = &MockRelationshipStoreMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockRelationshipStore) EXPECT() *MockRelationshipStoreMockRecorder {
	return m.recorder
}

// AddRelationship mocks base method.
func (m *MockRelationshipStore) AddRelationship(arg0 context.Context, arg1 *AddRelationInfo) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddRelationship", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddRelationship indicates an expected call of AddRelationship.
func (mr *MockRelationshipStoreMockRecorder) AddRelationship(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddRelationship", reflect.TypeOf((*MockRelationshipStore)(nil).AddRelationship), arg0, arg1)
}

// BatchAddLimitedRelationship mocks base method.
func (m *MockRelationshipStore) BatchAddLimitedRelationship(arg0 context.Context, arg1 []*LimitedRelationshipsConfig) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchAddLimitedRelationship", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// BatchAddLimitedRelationship indicates an expected call of BatchAddLimitedRelationship.
func (mr *MockRelationshipStoreMockRecorder) BatchAddLimitedRelationship(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchAddLimitedRelationship", reflect.TypeOf((*MockRelationshipStore)(nil).BatchAddLimitedRelationship), arg0, arg1)
}

// BatchDelLimitedRelationship mocks base method.
func (m *MockRelationshipStore) BatchDelLimitedRelationship(arg0 context.Context, arg1 []uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchDelLimitedRelationship", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// BatchDelLimitedRelationship indicates an expected call of BatchDelLimitedRelationship.
func (mr *MockRelationshipStoreMockRecorder) BatchDelLimitedRelationship(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchDelLimitedRelationship", reflect.TypeOf((*MockRelationshipStore)(nil).BatchDelLimitedRelationship), arg0, arg1)
}

// BatchDeleteRelationshipsByUid mocks base method.
func (m *MockRelationshipStore) BatchDeleteRelationshipsByUid(arg0 context.Context, arg1 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchDeleteRelationshipsByUid", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// BatchDeleteRelationshipsByUid indicates an expected call of BatchDeleteRelationshipsByUid.
func (mr *MockRelationshipStoreMockRecorder) BatchDeleteRelationshipsByUid(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchDeleteRelationshipsByUid", reflect.TypeOf((*MockRelationshipStore)(nil).BatchDeleteRelationshipsByUid), arg0, arg1)
}

// BatchGetUserNameplateInfo mocks base method.
func (m *MockRelationshipStore) BatchGetUserNameplateInfo(arg0 context.Context, arg1 []uint32) ([]*NameplateInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetUserNameplateInfo", arg0, arg1)
	ret0, _ := ret[0].([]*NameplateInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetUserNameplateInfo indicates an expected call of BatchGetUserNameplateInfo.
func (mr *MockRelationshipStoreMockRecorder) BatchGetUserNameplateInfo(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetUserNameplateInfo", reflect.TypeOf((*MockRelationshipStore)(nil).BatchGetUserNameplateInfo), arg0, arg1)
}

// DeleteRelationship mocks base method.
func (m *MockRelationshipStore) DeleteRelationship(arg0 context.Context, arg1, arg2 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteRelationship", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteRelationship indicates an expected call of DeleteRelationship.
func (mr *MockRelationshipStoreMockRecorder) DeleteRelationship(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteRelationship", reflect.TypeOf((*MockRelationshipStore)(nil).DeleteRelationship), arg0, arg1, arg2)
}

// GetAllValidRelationshipByPage mocks base method.
func (m *MockRelationshipStore) GetAllValidRelationshipByPage(arg0 context.Context, arg1 uint32, arg2 string) (string, []*RelationInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllValidRelationshipByPage", arg0, arg1, arg2)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].([]*RelationInfo)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetAllValidRelationshipByPage indicates an expected call of GetAllValidRelationshipByPage.
func (mr *MockRelationshipStoreMockRecorder) GetAllValidRelationshipByPage(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllValidRelationshipByPage", reflect.TypeOf((*MockRelationshipStore)(nil).GetAllValidRelationshipByPage), arg0, arg1, arg2)
}

// GetExpiredRelationList mocks base method.
func (m *MockRelationshipStore) GetExpiredRelationList(arg0 context.Context, arg1, arg2 uint32) ([]*RelationInfo, uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetExpiredRelationList", arg0, arg1, arg2)
	ret0, _ := ret[0].([]*RelationInfo)
	ret1, _ := ret[1].(uint32)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetExpiredRelationList indicates an expected call of GetExpiredRelationList.
func (mr *MockRelationshipStoreMockRecorder) GetExpiredRelationList(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetExpiredRelationList", reflect.TypeOf((*MockRelationshipStore)(nil).GetExpiredRelationList), arg0, arg1, arg2)
}

// GetFreezePartnerUidList mocks base method.
func (m *MockRelationshipStore) GetFreezePartnerUidList(arg0 context.Context, arg1 uint32) ([]uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetFreezePartnerUidList", arg0, arg1)
	ret0, _ := ret[0].([]uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetFreezePartnerUidList indicates an expected call of GetFreezePartnerUidList.
func (mr *MockRelationshipStoreMockRecorder) GetFreezePartnerUidList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFreezePartnerUidList", reflect.TypeOf((*MockRelationshipStore)(nil).GetFreezePartnerUidList), arg0, arg1)
}

// GetLatestRelationshipName mocks base method.
func (m *MockRelationshipStore) GetLatestRelationshipName(arg0 context.Context, arg1, arg2 uint32) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLatestRelationshipName", arg0, arg1, arg2)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLatestRelationshipName indicates an expected call of GetLatestRelationshipName.
func (mr *MockRelationshipStoreMockRecorder) GetLatestRelationshipName(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLatestRelationshipName", reflect.TypeOf((*MockRelationshipStore)(nil).GetLatestRelationshipName), arg0, arg1, arg2)
}

// GetLimitedRelationshipByName mocks base method.
func (m *MockRelationshipStore) GetLimitedRelationshipByName(arg0 context.Context, arg1 string) (*LimitedRelationshipsConfig, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLimitedRelationshipByName", arg0, arg1)
	ret0, _ := ret[0].(*LimitedRelationshipsConfig)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLimitedRelationshipByName indicates an expected call of GetLimitedRelationshipByName.
func (mr *MockRelationshipStoreMockRecorder) GetLimitedRelationshipByName(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLimitedRelationshipByName", reflect.TypeOf((*MockRelationshipStore)(nil).GetLimitedRelationshipByName), arg0, arg1)
}

// GetLimitedRelationshipDetail mocks base method.
func (m *MockRelationshipStore) GetLimitedRelationshipDetail(arg0 context.Context, arg1 uint32) (*LimitedRelationshipsConfig, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLimitedRelationshipDetail", arg0, arg1)
	ret0, _ := ret[0].(*LimitedRelationshipsConfig)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLimitedRelationshipDetail indicates an expected call of GetLimitedRelationshipDetail.
func (mr *MockRelationshipStoreMockRecorder) GetLimitedRelationshipDetail(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLimitedRelationshipDetail", reflect.TypeOf((*MockRelationshipStore)(nil).GetLimitedRelationshipDetail), arg0, arg1)
}

// GetLimitedRelationshipsByPage mocks base method.
func (m *MockRelationshipStore) GetLimitedRelationshipsByPage(arg0 context.Context, arg1, arg2 uint32, arg3 string) (*GetLimitedRelationshipsByPageResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLimitedRelationshipsByPage", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*GetLimitedRelationshipsByPageResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLimitedRelationshipsByPage indicates an expected call of GetLimitedRelationshipsByPage.
func (mr *MockRelationshipStoreMockRecorder) GetLimitedRelationshipsByPage(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLimitedRelationshipsByPage", reflect.TypeOf((*MockRelationshipStore)(nil).GetLimitedRelationshipsByPage), arg0, arg1, arg2, arg3)
}

// GetPartnerUidList mocks base method.
func (m *MockRelationshipStore) GetPartnerUidList(arg0 context.Context, arg1 uint32) ([]uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPartnerUidList", arg0, arg1)
	ret0, _ := ret[0].([]uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPartnerUidList indicates an expected call of GetPartnerUidList.
func (mr *MockRelationshipStoreMockRecorder) GetPartnerUidList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPartnerUidList", reflect.TypeOf((*MockRelationshipStore)(nil).GetPartnerUidList), arg0, arg1)
}

// GetRelationshipByPage mocks base method.
func (m *MockRelationshipStore) GetRelationshipByPage(arg0 context.Context, arg1, arg2 uint32, arg3 string) (string, []*RelationInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRelationshipByPage", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].([]*RelationInfo)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetRelationshipByPage indicates an expected call of GetRelationshipByPage.
func (mr *MockRelationshipStoreMockRecorder) GetRelationshipByPage(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRelationshipByPage", reflect.TypeOf((*MockRelationshipStore)(nil).GetRelationshipByPage), arg0, arg1, arg2, arg3)
}

// GetRelationshipByRecordId mocks base method.
func (m *MockRelationshipStore) GetRelationshipByRecordId(arg0 context.Context, arg1 uint32) (*RelationInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRelationshipByRecordId", arg0, arg1)
	ret0, _ := ret[0].(*RelationInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRelationshipByRecordId indicates an expected call of GetRelationshipByRecordId.
func (mr *MockRelationshipStoreMockRecorder) GetRelationshipByRecordId(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRelationshipByRecordId", reflect.TypeOf((*MockRelationshipStore)(nil).GetRelationshipByRecordId), arg0, arg1)
}

// GetRelationshipCnt mocks base method.
func (m *MockRelationshipStore) GetRelationshipCnt(arg0 context.Context, arg1 uint32) (uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRelationshipCnt", arg0, arg1)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRelationshipCnt indicates an expected call of GetRelationshipCnt.
func (mr *MockRelationshipStoreMockRecorder) GetRelationshipCnt(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRelationshipCnt", reflect.TypeOf((*MockRelationshipStore)(nil).GetRelationshipCnt), arg0, arg1)
}

// GetRelationshipRecordByUidAndNameSince mocks base method.
func (m *MockRelationshipStore) GetRelationshipRecordByUidAndNameSince(arg0 context.Context, arg1 uint32, arg2 string, arg3 time.Time) ([]*RelationInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRelationshipRecordByUidAndNameSince", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].([]*RelationInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRelationshipRecordByUidAndNameSince indicates an expected call of GetRelationshipRecordByUidAndNameSince.
func (mr *MockRelationshipStoreMockRecorder) GetRelationshipRecordByUidAndNameSince(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRelationshipRecordByUidAndNameSince", reflect.TypeOf((*MockRelationshipStore)(nil).GetRelationshipRecordByUidAndNameSince), arg0, arg1, arg2, arg3)
}

// UnfreezeRelationshipRecord mocks base method.
func (m *MockRelationshipStore) UnfreezeRelationshipRecord(arg0 context.Context, arg1 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UnfreezeRelationshipRecord", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// UnfreezeRelationshipRecord indicates an expected call of UnfreezeRelationshipRecord.
func (mr *MockRelationshipStoreMockRecorder) UnfreezeRelationshipRecord(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UnfreezeRelationshipRecord", reflect.TypeOf((*MockRelationshipStore)(nil).UnfreezeRelationshipRecord), arg0, arg1)
}

// UpdateLimitedRelationship mocks base method.
func (m *MockRelationshipStore) UpdateLimitedRelationship(arg0 context.Context, arg1 *LimitedRelationshipsConfig) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateLimitedRelationship", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateLimitedRelationship indicates an expected call of UpdateLimitedRelationship.
func (mr *MockRelationshipStoreMockRecorder) UpdateLimitedRelationship(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateLimitedRelationship", reflect.TypeOf((*MockRelationshipStore)(nil).UpdateLimitedRelationship), arg0, arg1)
}

// UpdateNameplateInfo mocks base method.
func (m *MockRelationshipStore) UpdateNameplateInfo(arg0 context.Context, arg1 []uint32, arg2 int32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateNameplateInfo", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateNameplateInfo indicates an expected call of UpdateNameplateInfo.
func (mr *MockRelationshipStoreMockRecorder) UpdateNameplateInfo(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateNameplateInfo", reflect.TypeOf((*MockRelationshipStore)(nil).UpdateNameplateInfo), arg0, arg1, arg2)
}

// UpdateRelationName mocks base method.
func (m *MockRelationshipStore) UpdateRelationName(arg0 context.Context, arg1 string, arg2 ...uint32) error {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateRelationName", varargs...)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateRelationName indicates an expected call of UpdateRelationName.
func (mr *MockRelationshipStoreMockRecorder) UpdateRelationName(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateRelationName", reflect.TypeOf((*MockRelationshipStore)(nil).UpdateRelationName), varargs...)
}

// MockTransaction is a mock of Transaction interface.
type MockTransaction struct {
	ctrl     *gomock.Controller
	recorder *MockTransactionMockRecorder
}

// MockTransactionMockRecorder is the mock recorder for MockTransaction.
type MockTransactionMockRecorder struct {
	mock *MockTransaction
}

// NewMockTransaction creates a new mock instance.
func NewMockTransaction(ctrl *gomock.Controller) *MockTransaction {
	mock := &MockTransaction{ctrl: ctrl}
	mock.recorder = &MockTransactionMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockTransaction) EXPECT() *MockTransactionMockRecorder {
	return m.recorder
}

// Wrap mocks base method.
func (m *MockTransaction) Wrap(arg0 context.Context, arg1 func(context.Context) (interface{}, error)) (interface{}, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Wrap", arg0, arg1)
	ret0, _ := ret[0].(interface{})
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Wrap indicates an expected call of Wrap.
func (mr *MockTransactionMockRecorder) Wrap(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Wrap", reflect.TypeOf((*MockTransaction)(nil).Wrap), arg0, arg1)
}

// MockCacheAside is a mock of CacheAside interface.
type MockCacheAside struct {
	ctrl     *gomock.Controller
	recorder *MockCacheAsideMockRecorder
}

// MockCacheAsideMockRecorder is the mock recorder for MockCacheAside.
type MockCacheAsideMockRecorder struct {
	mock *MockCacheAside
}

// NewMockCacheAside creates a new mock instance.
func NewMockCacheAside(ctrl *gomock.Controller) *MockCacheAside {
	mock := &MockCacheAside{ctrl: ctrl}
	mock.recorder = &MockCacheAsideMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockCacheAside) EXPECT() *MockCacheAsideMockRecorder {
	return m.recorder
}

// Del mocks base method.
func (m *MockCacheAside) Del(arg0 context.Context, arg1 ...string) error {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0}
	for _, a := range arg1 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "Del", varargs...)
	ret0, _ := ret[0].(error)
	return ret0
}

// Del indicates an expected call of Del.
func (mr *MockCacheAsideMockRecorder) Del(arg0 interface{}, arg1 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0}, arg1...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Del", reflect.TypeOf((*MockCacheAside)(nil).Del), varargs...)
}

// DelByPatten mocks base method.
func (m *MockCacheAside) DelByPatten(arg0 context.Context, arg1 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelByPatten", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// DelByPatten indicates an expected call of DelByPatten.
func (mr *MockCacheAsideMockRecorder) DelByPatten(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelByPatten", reflect.TypeOf((*MockCacheAside)(nil).DelByPatten), arg0, arg1)
}

// Get mocks base method.
func (m *MockCacheAside) Get(arg0 context.Context, arg1 string, arg2 interface{}, arg3 func() (interface{}, time.Duration, error)) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Get", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// Get indicates an expected call of Get.
func (mr *MockCacheAsideMockRecorder) Get(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Get", reflect.TypeOf((*MockCacheAside)(nil).Get), arg0, arg1, arg2, arg3)
}

// Set mocks base method.
func (m *MockCacheAside) Set(arg0 context.Context, arg1 string, arg2 interface{}, arg3 time.Duration) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Set", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// Set indicates an expected call of Set.
func (mr *MockCacheAsideMockRecorder) Set(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Set", reflect.TypeOf((*MockCacheAside)(nil).Set), arg0, arg1, arg2, arg3)
}

// MockRelationshipNotExistMarkerCache is a mock of RelationshipNotExistMarkerCache interface.
type MockRelationshipNotExistMarkerCache struct {
	ctrl     *gomock.Controller
	recorder *MockRelationshipNotExistMarkerCacheMockRecorder
}

// MockRelationshipNotExistMarkerCacheMockRecorder is the mock recorder for MockRelationshipNotExistMarkerCache.
type MockRelationshipNotExistMarkerCacheMockRecorder struct {
	mock *MockRelationshipNotExistMarkerCache
}

// NewMockRelationshipNotExistMarkerCache creates a new mock instance.
func NewMockRelationshipNotExistMarkerCache(ctrl *gomock.Controller) *MockRelationshipNotExistMarkerCache {
	mock := &MockRelationshipNotExistMarkerCache{ctrl: ctrl}
	mock.recorder = &MockRelationshipNotExistMarkerCacheMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockRelationshipNotExistMarkerCache) EXPECT() *MockRelationshipNotExistMarkerCacheMockRecorder {
	return m.recorder
}

// Check mocks base method.
func (m *MockRelationshipNotExistMarkerCache) Check(arg0 context.Context, arg1 uint32) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Check", arg0, arg1)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Check indicates an expected call of Check.
func (mr *MockRelationshipNotExistMarkerCacheMockRecorder) Check(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Check", reflect.TypeOf((*MockRelationshipNotExistMarkerCache)(nil).Check), arg0, arg1)
}

// Mark mocks base method.
func (m *MockRelationshipNotExistMarkerCache) Mark(arg0 context.Context, arg1 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Mark", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// Mark indicates an expected call of Mark.
func (mr *MockRelationshipNotExistMarkerCacheMockRecorder) Mark(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Mark", reflect.TypeOf((*MockRelationshipNotExistMarkerCache)(nil).Mark), arg0, arg1)
}

// Unmark mocks base method.
func (m *MockRelationshipNotExistMarkerCache) Unmark(arg0 context.Context, arg1 ...uint32) error {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0}
	for _, a := range arg1 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "Unmark", varargs...)
	ret0, _ := ret[0].(error)
	return ret0
}

// Unmark indicates an expected call of Unmark.
func (mr *MockRelationshipNotExistMarkerCacheMockRecorder) Unmark(arg0 interface{}, arg1 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0}, arg1...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Unmark", reflect.TypeOf((*MockRelationshipNotExistMarkerCache)(nil).Unmark), varargs...)
}
