package store

import (
	"context"
	"fmt"
	"testing"
	"time"

	"github.com/jmoiron/sqlx"
	"github.com/stretchr/testify/assert"
	"golang.52tt.com/services/tt-rev/offering-room/internal/mgr"
)

func Test_order_Insert(t *testing.T) {
	t.Skip()
	db, err := sqlx.Open("mysql", "root:root@tcp(127.0.0.1:3306)/pgc_offering_room?charset=utf8&parseTime=true&loc=Local")
	if err != nil {
		t.Fatal("open db error")
	}
	dbx, err := NewDBX(db)
	assert.NoError(t, err)
	o := &order{
		db: dbx,
	}

	m := &mgr.Order{
		Sn:          "123123",
		GameRoundId: 123,
		//	TBeanOrderId: 123,
		TBeanNum: 123,
		GiftId:   123,
		GiftNum:  123,
		Uid:      123,
		Status:   mgr.OrderStatusWaitToPay,
	}
	err = o.Insert(context.Background(), time.Now(), m)

	assert.NoError(t, err)
	assert.Condition(t, func() (success bool) {
		return m.Id > 0
	}, "id should be greater than 0")
	assert.Condition(t, func() (success bool) {
		return !m.CreateTime.IsZero() && m.CreateTime.Unix() > 0
	}, "create time should be greater than 0")

}

func Test_order_UpdateStatus(t *testing.T) {
	// db, err := sqlx.Open("mysql", "root:root@tcp(127.0.0.1:3306)/pgc_offering_room?charset=utf8&parseTime=true&loc=Local")
	// if err != nil {
	// 	t.Fatal("open db error")
	// }
	// o := &order{
	// 	db: db,
	// }

	// err = o.UpdateStatus(context.Background(), time.Now(), 2, mgr.OrderStatusFreezing)
	// assert.Condition(t, func() (success bool) {
	// 	return err != nil
	// }, "should be error")

}

func Test_order_BatchUpdateStatusByIds(t *testing.T) {
	//db, err := sqlx.Open("mysql", "root:root@tcp(127.0.0.1:3306)/pgc_offering_room?charset=utf8&parseTime=true&loc=Local")
	//if err != nil {
	//	t.Fatal("open db error")
	//}
	//o := &order{
	//	db: db,
	//}
	//now := time.Now()
	//err = o.createTable(context.Background(), o.getTableName(now))
	//assert.NoError(t, err)
	//
	//_, err = o.innerInsert(context.Background(), o.getTableName(now), &mgr.Order{
	//	Sn:          "123",
	//	GameRoundId: 1,
	//	TBeanNum:    2,
	//	GiftId:      3,
	//	GiftNum:     4,
	//	GiftTBean:   5,
	//	GiftName:    "123",
	//	Uid:         123,
	//	Username:    "123",
	//	Status:      1,
	//})
	//assert.NoError(t, err)
	//
	//_, err = o.innerInsert(context.Background(), o.getTableName(now), &mgr.Order{
	//	Sn:          "321",
	//	GameRoundId: 1,
	//	TBeanNum:    2,
	//	GiftId:      3,
	//	GiftNum:     4,
	//	GiftTBean:   5,
	//	GiftName:    "123",
	//	Uid:         123,
	//	Username:    "123",
	//	Status:      1,
	//})
	//assert.NoError(t, err)
	//
	//list, err := o.GetOrderListByGameAndUser(context.Background(), now, uint32(123), int64(1))
	//assert.NoError(t, err)
	//
	//_, err = o.BatchUpdateStatusByIds(context.Background(), now, list.GetIdList(), mgr.OrderStatusFreezing, mgr.OrderStatusWaitToConfirm)
	//assert.NoError(t, err)

}

func Test_order_CountUserConsumptionByGameRoundIdAndStatus(t *testing.T) {
	//db, err := sqlx.Open("mysql", "godman:thegodofman@tcp(************:3306)/pgc_offering_room?charset=utf8&parseTime=true&loc=Local")
	//if err != nil {
	//	t.Fatal("open db error")
	//}
	//o := &order{
	//	readOnly: db,
	//}
	//
	//status, err := o.CountUserConsumptionByGameRoundIdAndStatus(context.Background(), time.Now(), 667, []mgr.OrderStatus{mgr.OrderStatusConfirm, mgr.OrderStatusWaitToConfirm})
	//assert.NoError(t, err)
	//fmt.Printf("status: %v\n", status)

}

func Test_order_GetOrderSnsByGameRoundIds(t *testing.T) {
	t.Skip()
	db, err := sqlx.Open("mysql", "godman:thegodofman@tcp(************:3306)/pgc_offering_room?charset=utf8&parseTime=true&loc=Local")
	if err != nil {
		t.Fatal("open db error")
	}
	o := &order{
		readOnly: db,
	}

	ids, err := o.GetOrderSnsByGameRoundIdsAndStatus(context.Background(), []time.Time{time.Now()}, []int64{884}, mgr.OrderStatusConfirm, time.Unix(1687358460, 0), time.Unix(1687358760, 0))
	assert.NoError(t, err)
	fmt.Printf("ids: %+v\n", ids)

}

func Test_order_GetOrderByOrderSn(t *testing.T) {
	t.Skip()
	db, err := sqlx.Open("mysql", "godman:thegodofman@tcp(************:3306)/pgc_offering_room?charset=utf8&parseTime=true&loc=Local")
	if err != nil {
		t.Fatal("open db error")
	}
	o := &order{
		readOnly: db,
	}
	sn, err := o.GetOrderByOrderSn(context.Background(), time.Unix(1688695953, 0), "TT_OFRM_1801_2523781_1688353006989695265")
	assert.NoError(t, err)
	fmt.Printf("sn: %+v\n", sn)
}
