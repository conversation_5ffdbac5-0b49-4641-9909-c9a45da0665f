package store

import (
	"push/pkg/log"
	"time"

	"github.com/globalsign/mgo"
	"github.com/globalsign/mgo/bson"
	"golang.52tt.com/pkg/protocol"
	errStatus "golang.52tt.com/protocol/common/status"
	pb "golang.52tt.com/protocol/services/fellow-svr"
	comStore "golang.52tt.com/services/common/store"
)

func (st *Store) getFellowInviteInfoTable() *comStore.MongoHelper {
	return st.mMgo.GetHelper(FellowDB, FellowInviteInfoCollection)
}

func (st *Store) getFellowUnlockInfoTable() *comStore.MongoHelper {
	return st.mMgo.GetHelper(FellowDB, FellowUserInfoCollection)
}

func (st *Store) getFellowUnboundOrderTable() *comStore.MongoHelper {
	return st.mMgo.GetHelper(FellowDB, FellowUnboundOrderCollection)
}

func (st *Store) AddFellowInviteInfo(fellowInvite *FellowInviteInfo) (id bson.ObjectId, err error) {

	mErr := st.getFellowInviteInfoTable().Insert(fellowInvite)
	if mErr != nil {
		log.Errorf("AddFellowInviteInfo fail. uid:%d, target uid:%d, err:%v", fellowInvite.InviteUid, fellowInvite.TargetUid, mErr)
		return id, err
	}

	log.Debugf("AddFellowInviteInfo done. uid:%d, target uid:%d, err:%v", fellowInvite.InviteUid, fellowInvite.TargetUid, err)
	return id, nil
}

func (st *Store) UpdateFellowInviteInfo(id string, status uint32, nowTime time.Time) (res FellowInviteInfo, err error) {

	query := mgo.Change{
		Update:    bson.M{"$set": bson.M{"invite_status": status, "update_time": time.Now()}},
		ReturnNew: true,
	}

	var ret FellowInviteInfo
	// 限制条件：只有状态为邀请的邀请函才能进行update
	changed, err := st.getFellowInviteInfoTable().FindAndModify(bson.M{"_id": bson.ObjectIdHex(id), "invite_status": uint32(pb.InviteStatus_ENUM_INVITE_STATUS_INVITED)}, query, &ret)

	// 找不到/一行都没有修改，说明邀请函状态已经改了，报错返回
	if err == mgo.ErrNotFound {
		return ret, protocol.NewExactServerError(nil, errStatus.ErrFellowInviteNotExist)
	}

	if err != nil {
		log.Errorf("UpdateFellowInviteInfo fail. invite id :%d, err:%v", id, err)
		return ret, err
	}

	if changed.Updated == 0 {
		return ret, protocol.NewExactServerError(nil, errStatus.ErrFellowInviteNotExist)
	}

	log.Debugf("UpdateFellowInviteInfo done. invite id: %d", id)
	return ret, nil
}

func (st *Store) GetFellowInviteInfoByInviteUid(uid uint32, status uint32) (inviteInfo []*FellowInviteInfo, err error) {

	inviteInfo = make([]*FellowInviteInfo, 0)
	query := bson.M{"invite_uid": uid}
	if status != 0 {
		query = bson.M{"$and": []bson.M{
			{"invite_uid": uid}, {"invite_status": status},
		}}
	}

	err = st.getFellowInviteInfoTable().All(query, "-update_time", &inviteInfo)
	if err != nil {
		log.Errorf("GetFellowInviteInfoByUid fail. uid :%d, err:%v", uid, err)
		return inviteInfo, err
	}

	log.Debugf("GetFellowInviteInfoByUid done. uid :%d", uid)
	return inviteInfo, nil
}

func (st *Store) GetFellowImInviteCountByInviteUid(uid uint32, status uint32) (count uint32, err error) {

	query := bson.M{"invite_uid": uid}
	if status != 0 {
		query = bson.M{"$and": []bson.M{
			{"invite_uid": uid}, {"invite_status": status}, {"invite_type": 0},
		}}
	}

	cnt, err := st.getFellowInviteInfoTable().Count(query)
	if err != nil {
		log.Errorf("GetFellowInviteInfoByUid fail. uid :%d, err:%v", uid, err)
		return 0, err
	}

	log.Debugf("GetFellowInviteCountByInviteUid done. uid :%d", uid)
	return uint32(cnt), nil
}

func (st *Store) GetFellowInviteInfoByUid(uid uint32, status uint32) (inviteInfo []*FellowInviteInfo, err error) {

	inviteInfo = make([]*FellowInviteInfo, 0)
	query := bson.M{"invite_uid": uid}
	if status != 0 {
		query = bson.M{"$or": []bson.M{
			{"$and": []bson.M{{"invite_uid": uid}, {"invite_status": status}}},
			{"$and": []bson.M{{"target_uid": uid}, {"invite_status": status}}},
		}}
	}

	err = st.getFellowInviteInfoTable().All(query, "-update_time", &inviteInfo)
	if err != nil {
		log.Errorf("GetFellowInviteInfoByUid fail. uid :%d, err:%v", uid, err)
		return inviteInfo, err
	}

	log.Debugf("GetFellowInviteInfoByUid done. uid :%d", uid)
	return inviteInfo, nil
}

func (st *Store) GetFellowInviteInfoByUidAndChannelId(uid uint32, channelId, status uint32) (inviteInfo []*FellowInviteInfo, err error) {
	inviteInfo = make([]*FellowInviteInfo, 0)
	query := bson.M{"$and": []bson.M{
		{"$or": []bson.M{
			{"invite_uid": uid}, {"target_uid": uid},
		}}, {"channel_id": channelId},
	}}
	if status != 0 {
		query = bson.M{"$and": []bson.M{
			{"$or": []bson.M{
				{"invite_uid": uid}, {"target_uid": uid},
			}}, {"channel_id": channelId}, {"invite_status": status},
		}}
	}

	err = st.getFellowInviteInfoTable().All(query, "-update_time", &inviteInfo)
	if err != nil {
		log.Errorf("GetFellowInviteInfoByUid fail. uid :%d, err:%v", uid, err)
		return inviteInfo, err
	}

	log.Debugf("GetFellowInviteInfoByUid done. uid :%d", uid)
	return inviteInfo, nil
}

func (st *Store) GetFellowInviteInfoByTargetUid(targetUid uint32, status uint32) (inviteInfo []*FellowInviteInfo, err error) {

	inviteInfo = make([]*FellowInviteInfo, 0)
	query := bson.M{"target_uid": targetUid}
	if status != 0 {
		query = bson.M{"$and": []bson.M{
			{"target_uid": targetUid}, {"invite_status": status},
		}}
	}

	err = st.getFellowInviteInfoTable().All(query, "update_time", &inviteInfo)
	if err != nil {
		log.Errorf("GetFellowInviteInfoByTargetUid fail. uid :%d, err:%v", targetUid, err)
		return inviteInfo, err
	}

	log.Debugf("GetFellowInviteInfoByTargetUid done. uid :%d , resp : %v", targetUid, inviteInfo)
	return inviteInfo, nil
}

func (st *Store) GetPendingInviteCountByTargetUid(targetUid uint32, status uint32) (count uint32, err error) {

	// 注意这里不管房间申请
	query := bson.M{"target_uid": targetUid}
	if status != 0 {
		query = bson.M{"$and": []bson.M{
			{"target_uid": targetUid}, {"invite_status": status}, {"invite_type": Invite_Type_Normal},
		}}
	}

	fellowCount, err := st.getFellowInviteInfoTable().Count(query)
	if err != nil {
		log.Errorf("GetFellowInviteInfoByTargetUid fail. uid :%d, err:%v", targetUid, err)
		return uint32(fellowCount), err
	}

	log.Debugf("GetFellowInviteInfoByTargetUid done. uid :%d , count : %d", targetUid, fellowCount)
	return uint32(fellowCount), nil
}

func (st *Store) GetFellowInviteInfoByIdPair(uid uint32, targetUid uint32) (inviteInfo FellowInviteInfo, err error) {

	inviteInfo = FellowInviteInfo{}

	query := bson.M{"$or": []bson.M{
		{"$and": []bson.M{
			{"invite_uid": uid},
			{"target_uid": targetUid}},
		},
		{"$and": []bson.M{
			{"invite_uid": targetUid},
			{"target_uid": uid}},
		},
	}}

	err = st.getFellowInviteInfoTable().OneSort(query, "-update_time", &inviteInfo)
	if err != nil {
		if err == mgo.ErrNotFound {
			log.Debugf("GetFellowInviteInfoByUid no res. uid :%d, err:%v", uid, err)
			return inviteInfo, nil
		}
		log.Errorf("GetFellowInviteInfoByUid fail. uid :%d, err:%v", uid, err)
		return inviteInfo, err
	}

	log.Debugf("GetFellowInviteInfoByUid done. uid :%d", uid)
	return inviteInfo, nil
}

func (st *Store) GetAllFellowInviteInfoByUid(uid uint32) (inviteInfo []*FellowInviteInfo, err error) {
	inviteInfo = make([]*FellowInviteInfo, 0)
	query := bson.M{
		"$or": []bson.M{
			{"invite_uid": uid},
			{"target_uid": uid}},
		"$and": []bson.M{
			{"invite_status": int(pb.InviteStatus_ENUM_INVITE_STATUS_INVITED)},
		},
	}

	err = st.getFellowInviteInfoTable().All(query, "update_time", &inviteInfo)
	if err != nil {
		log.Errorf("GetAllFellowInviteInfoByUid fail. uid :%d, err:%v", uid, err)
		return inviteInfo, err
	}

	log.Debugf("GetAllFellowInviteInfoByUid done. uid :%d , resp : %v", uid, inviteInfo)
	return inviteInfo, nil
}

func (st *Store) GetFellowUnlockInfoByUid(uid uint32) (siteNum uint32, err error) {

	info := FellowUserInfo{}
	query := bson.M{"uid": uid}

	err = st.getFellowUnlockInfoTable().One(query, &info)
	if err != nil {
		log.Errorf("GetFellowInviteInfoByTargetUid fail. uid :%d, err:%v", uid, err)
		return siteNum, err
	}

	log.Debugf("GetFellowInviteInfoByTargetUid done. uid :%d", uid)
	return info.SiteUnlocked, nil
}

func (st *Store) BatchGetFellowInviteByUid(uid uint32, status uint32) (infoMap map[uint32]*FellowInviteInfo, err error) {

	infoList := make([]*FellowInviteInfo, 0)
	infoMap = make(map[uint32]*FellowInviteInfo, 0)
	query := bson.M{
		"$and": []bson.M{
			{"invite_uid": uid},
			{"invite_status": status},
		},
	}

	err = st.getFellowInviteInfoTable().All(query, "-update_time", &infoList)
	if err != nil {
		log.Errorf("GetFellowInviteInfoByTargetUid fail. uid :%v, err:%v", uid, err)
		return infoMap, err
	}

	for _, info := range infoList {
		infoMap[info.TargetUid] = info
	}

	log.Debugf("GetFellowInviteInfoByTargetUid done. uid :%v", uid)
	return infoMap, nil
}

func (st *Store) GetFellowInviteInfoById(inviteId string) (info FellowInviteInfo, err error) {

	info = FellowInviteInfo{}

	if inviteId == "" {
		return info, protocol.NewExactServerError(nil, errStatus.ErrFellowInviteNotExist)
	}

	if !bson.IsObjectIdHex(inviteId) {
		return info, protocol.NewExactServerError(nil, errStatus.ErrFellowInviteNotExist)
	}

	objectId := bson.ObjectIdHex(inviteId)

	query := bson.M{
		"_id": objectId,
	}

	err = st.getFellowInviteInfoTable().One(query, &info)
	if err != nil {
		log.Errorf("GetFellowInviteInfoById fail. inviteId :%v, err:%v", inviteId, err)
		return info, err
	}

	log.Debugf("GetFellowInviteInfoById done. inviteId :%v , info: %v", inviteId, info)
	return info, nil
}

func (st *Store) GetAllPendingInvite7DaysAgo() (infoList []*FellowInviteInfo, err error) {

	infoList = make([]*FellowInviteInfo, 0)

	// 提前十分钟，避免伟潮那边的回调先到
	pastTimeEnd := time.Now().AddDate(0, 0, -7).Add(10 * time.Minute)
	pastTimeStart := pastTimeEnd.Add(-5 * time.Minute)

	query := bson.M{
		"$and": []bson.M{
			{"create_time": bson.M{"$gte": pastTimeStart, "$lt": pastTimeEnd}},
			{"invite_status": int(pb.InviteStatus_ENUM_INVITE_STATUS_INVITED)},
		},
	}

	err = st.getFellowInviteInfoTable().All(query, "-create_time", &infoList)
	if err != nil {
		log.Errorf("GetAllPendingInvite7DaysAgo fail.  err:%v", err)
		return infoList, err
	}

	log.Errorf("GetAllPendingInvite7DaysAgo done. infoList :%v ", infoList)
	return infoList, nil
}

func (st *Store) GetAllPendingChannelInvite() (infoList []*FellowInviteInfo, err error) {

	infoList = make([]*FellowInviteInfo, 0)

	// 这里可以直接看end_time有没有过期
	nowTime := time.Now()
	_1MinAgo := time.Now().Add(-1 * time.Minute)
	//pastTime := time.Now().Add(-5*time.Minute)

	query := bson.M{
		"$and": []bson.M{
			{"end_time": bson.M{"$gte": _1MinAgo, "$lt": nowTime}},
			{"invite_status": int(pb.InviteStatus_ENUM_INVITE_STATUS_INVITED)},
			{"invite_type": int(Invite_Type_Channel)},
		},
	}

	err = st.getFellowInviteInfoTable().All(query, "-create_time", &infoList)
	if err != nil {
		log.Errorf("GetAllPendingChannelInvite fail.  err:%v", err)
		return infoList, err
	}

	log.Errorf("GetAllPendingChannelInvite done. infoList :%v ", infoList)
	return infoList, nil
}

func (st *Store) AddFellowUnboundOrder(unboundOrder *FellowUnboundOrder) (err error) {

	mErr := st.getFellowUnboundOrderTable().Insert(unboundOrder)
	if mErr != nil {
		log.Errorf("AddFellowUnboundOrder fail. uid:%d, target uid:%d, err:%v", unboundOrder.FromUid, unboundOrder.ToUid, mErr)
		return err
	}

	log.Debugf("AddFellowUnboundOrder done. uid:%d, target uid:%d, err:%v", unboundOrder.FromUid, unboundOrder.ToUid, err)
	return nil
}

func (st *Store) UpdateFellowUnboundOrder(fromUid, toUid uint32, status uint32) (err error) {

	update := bson.M{"$set": bson.M{"status": status, "update_time": time.Now()}}

	query := bson.M{"$and": []bson.M{
		{"from_uid": fromUid}, {"to_uid": toUid}, {"status": 1},
	}}

	err = st.getFellowUnboundOrderTable().UpdateAll(query, update)
	if err != nil {
		log.Errorf("UpdateFellowUnboundOrder fail. resp %v,fromUid :%d, err:%v", fromUid, err)
		return err
	}

	log.Debugf("UpdateFellowUnboundOrder done. fromUid :%d to %d", fromUid, toUid)
	return nil
}

func (st *Store) GetFellowUnboundOrderByUidPair(fromUid, toUid uint32, status uint32) (unboundOrder *FellowUnboundOrder, exist bool, err error) {

	unboundOrder = &FellowUnboundOrder{}
	query := bson.M{"$and": []bson.M{
		{"from_uid": fromUid}, {"status": status}, {"to_uid": toUid},
	}}

	err = st.getFellowUnboundOrderTable().One(query, &unboundOrder)
	if err == mgo.ErrNotFound {
		return unboundOrder, false, nil
	}

	if err != nil {
		log.Errorf("GetFellowUnboundOrderByUidPair fail.  fromUid, toUid %d %d err:%v", fromUid, toUid, err)
		return unboundOrder, exist, err
	}

	return unboundOrder, true, nil
}

func (st *Store) GetAllFellowUnboundOrder3DaysAgo(status uint32) (unboundOrder []*FellowUnboundOrder, err error) {

	unboundOrderList := make([]*FellowUnboundOrder, 0)

	pastTime := time.Now().AddDate(0, 0, -3)
	_5MinPastTimeAgo := pastTime.Add(-5 * time.Minute)

	query := bson.M{
		"$and": []bson.M{
			{"create_time": bson.M{"$gte": _5MinPastTimeAgo, "$lt": pastTime}},
			{"status": status},
		},
	}

	err = st.getFellowUnboundOrderTable().All(query, "create_time", &unboundOrderList)
	if err != nil {
		log.Errorf("GetAllFellowUnboundOrder3DaysAgo fail. err:%v", err)
		return unboundOrder, err
	}

	log.Errorf("GetAllFellowUnboundOrder3DaysAgo done, unboundOrderList: %v", unboundOrderList)
	return unboundOrderList, nil
}

func (st *Store) GetUnboundList(uid uint32) (unboundOrder []FellowUnboundOrder, err error) {
	query := bson.M{
		"$or": []bson.M{
			{"from_uid": uid},
			{"to_uid": uid},
		},
		"$and": []bson.M{
			{"status": Unbound_Status_Pending},
		},
	}

	err = st.getFellowUnboundOrderTable().All(query, "", &unboundOrder)
	if err == mgo.ErrNotFound {
		return unboundOrder, nil
	}

	if err != nil {
		log.Errorf("GetFellowUnboundList fail.  uid %d, err:%v", uid, err)
		return unboundOrder, err
	}
	return unboundOrder, nil
}

func (st *Store) GetFellowInviteInfoByOutTradeNo(outTradeInfo string) (info FellowInviteInfo, err error) {

	info = FellowInviteInfo{}

	if outTradeInfo == "" {
		return info, protocol.NewExactServerError(nil, errStatus.ErrFellowInviteNotExist)
	}

	query := bson.M{
		"out_trade_no": outTradeInfo,
	}

	err = st.getFellowInviteInfoTable().One(query, &info)
	if err != nil {
		log.Errorf("GetFellowInviteInfoById fail. outTradeInfo :%v, err:%v", outTradeInfo, err)
		return info, err
	}

	return info, nil
}

func (st *Store) GetHistoryInvite(uid, targetUid uint32, inviteTimeStr string) (info *FellowInviteInfo, err error) {
	infoList := make([]*FellowInviteInfo, 0)
	inviteTime, _ := time.ParseInLocation("2006-01-02 15:04:05", inviteTimeStr, time.Local)
	inviteTimeEnd := inviteTime.AddDate(0, 0, 1)

	query := bson.M{"$or": []bson.M{
		{"$and": []bson.M{{"invite_uid": uid}, {"target_uid": targetUid}, {"create_time": bson.M{"$gte": inviteTime, "$lt": inviteTimeEnd}}, {"invite_status": int(pb.InviteStatus_ENUM_INVITE_STATUS_SUCCESS)}}},
		{"$and": []bson.M{{"target_uid": uid}, {"invite_uid": targetUid}, {"create_time": bson.M{"$gte": inviteTime, "$lt": inviteTimeEnd}}, {"invite_status": int(pb.InviteStatus_ENUM_INVITE_STATUS_SUCCESS)}}},
	}}

	err = st.getFellowInviteInfoTable().All(query, "-create_time", &infoList)
	if err != nil {
		log.Errorf("GetAllPendingInvite7DaysAgo fail.  err:%v", err)
		return nil, err
	}

	log.Infof("GetHistoryInvite done. infoList uid:%d, targetUid:%d  list:%v start:%v, end:%v ", uid, targetUid, infoList, inviteTime, inviteTimeEnd)
	for _, invite := range infoList {
		if invite.IsNewBind() {
			return invite, nil
		}
	}
	return nil, nil
}
