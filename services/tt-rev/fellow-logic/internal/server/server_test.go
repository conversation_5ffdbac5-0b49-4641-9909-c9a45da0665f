package server

import (
	"context"
	fellow_level_award "golang.52tt.com/protocol/services/fellow-level-award"
	"time"

	"github.com/golang/mock/gomock"
	"github.com/opentracing/opentracing-go"
	"golang.52tt.com/clients/account"
	"golang.52tt.com/clients/channel"
	"golang.52tt.com/clients/channelol"
	fellowsvr "golang.52tt.com/clients/fellow-svr"
	iop_proxy "golang.52tt.com/clients/iop-proxy"
	mockAccount "golang.52tt.com/clients/mocks/account"
	mockChannel "golang.52tt.com/clients/mocks/channel"
	mockChannelOl "golang.52tt.com/clients/mocks/channelol"
	mockFellowSvr "golang.52tt.com/clients/mocks/fellow-svr"
	mockIop "golang.52tt.com/clients/mocks/iop-proxy"
	mockRealName "golang.52tt.com/clients/mocks/realnameauth"
	mockProfile "golang.52tt.com/clients/mocks/user-profile-api"
	mockPresent "golang.52tt.com/clients/mocks/userpresent"
	mockDevice "golang.52tt.com/clients/mocks/usual-device"
	mockYouKnowWho "golang.52tt.com/clients/mocks/you-know-who"
	"golang.52tt.com/protocol/services/mocks"
	"golang.52tt.com/services/tt-rev/fellow-logic/internal/conf"

	"golang.52tt.com/clients/realnameauth"
	user_profile_api "golang.52tt.com/clients/user-profile-api"
	userPresent "golang.52tt.com/clients/userpresent"
	verifycode "golang.52tt.com/clients/usual-device"
	youknowwho "golang.52tt.com/clients/you-know-who"
	protogrpc "golang.52tt.com/pkg/protocol/grpc"
	pbApp "golang.52tt.com/protocol/app"
	pb "golang.52tt.com/protocol/app/fellow-logic"
	pbChannel "golang.52tt.com/protocol/services/channelsvr"
	pbFellowSvr "golang.52tt.com/protocol/services/fellow-svr"

	"reflect"
	"testing"

	super_player_privilege "golang.52tt.com/protocol/services/super-player-privilege"
	pbUserPresent "golang.52tt.com/protocol/services/userpresent"
	pbUsualDevice "golang.52tt.com/protocol/services/usual-device-svr"

	_ "golang.52tt.com/services/recommend-dialog/tools/grpc_proxy/enable"
)

var (
	fellowSvrClient         *mockFellowSvr.MockIClient
	accountCli              *mockAccount.MockIClient
	usualDeviceClient       *mockDevice.MockIClient
	realNameClient          *mockRealName.MockIClient
	presentClient           *mockPresent.MockIClient
	iopCli                  *mockIop.MockIClient
	userProfileApiCli       *mockProfile.MockIClient
	ukwCli                  *mockYouKnowWho.MockIClient
	channelOlCli            *mockChannelOl.MockIClient
	channelCli              *mockChannel.MockIClient
	uid                     uint32 = 1000
	cid                     uint32 = 2000
	ctx                            = protogrpc.WithServiceInfo(context.Background(), &protogrpc.ServiceInfo{UserID: uid})
	superPlayerPrivilegeCli *mocks.MockSuperPlayerPrivilegeClient
	fellowLevelAwardClient  *mocks.MockFellowLevelAwardClient
)

func initClient(ctl *gomock.Controller) {
	fellowSvrClient = mockFellowSvr.NewMockIClient(ctl)
	accountCli = mockAccount.NewMockIClient(ctl)
	usualDeviceClient = mockDevice.NewMockIClient(ctl)
	realNameClient = mockRealName.NewMockIClient(ctl)
	presentClient = mockPresent.NewMockIClient(ctl)
	iopCli = mockIop.NewMockIClient(ctl)
	userProfileApiCli = mockProfile.NewMockIClient(ctl)
	ukwCli = mockYouKnowWho.NewMockIClient(ctl)
	channelOlCli = mockChannelOl.NewMockIClient(ctl)
	channelCli = mockChannel.NewMockIClient(ctl)
	superPlayerPrivilegeCli = mocks.NewMockSuperPlayerPrivilegeClient(ctl)
	fellowLevelAwardClient = mocks.NewMockFellowLevelAwardClient(ctl)
}

func TestFellowLogic_CancelFellowInvite(t *testing.T) {
	ctl := gomock.NewController(t)
	initClient(ctl)
	defer ctl.Finish()

	ctx := context.Background()
	ctx = protogrpc.WithServiceInfo(ctx, &protogrpc.ServiceInfo{UserID: 2202086})

	out := &pb.CancelFellowInviteResp{}

	gomock.InOrder(
		fellowSvrClient.EXPECT().CancelFellowInvite(ctx, gomock.Any()).Return(nil, nil),
	)

	type fields struct {
		fellowSvrClient   fellowsvr.IClient
		accountClient     account.IClient
		usualDeviceClient verifycode.IClient
		realNameClient    realnameauth.IClient
		presentClient     userPresent.IClient
		iopClient         iop_proxy.IClient
		userProfileApiCli user_profile_api.IClient
		ukwCli            youknowwho.IClient
	}
	type args struct {
		ctx context.Context
		in  *pb.CancelFellowInviteReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.CancelFellowInviteResp
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "CancelFellowInvite",
			fields: fields{
				fellowSvrClient: fellowSvrClient,
			},
			args: args{
				ctx: ctx,
				in: &pb.CancelFellowInviteReq{
					InviteId: "test",
				},
			},
			want:    out,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			svr := &Server{
				fellowSvrClient:   tt.fields.fellowSvrClient,
				accountClient:     tt.fields.accountClient,
				usualDeviceClient: tt.fields.usualDeviceClient,
				realNameClient:    tt.fields.realNameClient,
				presentClient:     tt.fields.presentClient,
				iopClient:         tt.fields.iopClient,
				userProfileApiCli: tt.fields.userProfileApiCli,
				ukwCli:            tt.fields.ukwCli,
			}
			got, err := svr.CancelFellowInvite(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("CancelFellowInvite() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("CancelFellowInvite() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestFellowLogic_ChannelSendFellowPresent(t *testing.T) {
	ctl := gomock.NewController(t)
	initClient(ctl)
	defer ctl.Finish()

	var uid uint32 = 100
	ctx := context.Background()
	ctx = protogrpc.WithServiceInfo(ctx, &protogrpc.ServiceInfo{UserID: uid})
	out := &pb.ChannelSendFellowPresentResp{
		RemainCurrency: 10,
		RemainTbean:    10,
	}

	gomock.InOrder(
		ukwCli.EXPECT().GetTrueUidByFake(ctx, gomock.Any()).Return(uid, nil),
		presentClient.EXPECT().GetPresentConfigById(ctx, gomock.Any()).Return(&pbUserPresent.GetPresentConfigByIdResp{
			ItemConfig: &pbUserPresent.StPresentItemConfig{
				ItemId:      100,
				Name:        "",
				IconUrl:     "",
				Price:       10,
				Score:       0,
				Charm:       0,
				Rank:        0,
				EffectBegin: 0,
				EffectEnd:   0,
				UpdateTime:  0,
				CreateTime:  0,
				IsDel:       false,
				PriceType:   2,
				RichValue:   0,
				Extend:      nil,
			},
		}, nil),
		iopCli.EXPECT().FaceAuthBeforeConsumeChecker(ctx, gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil),
		usualDeviceClient.EXPECT().CheckUsualDevice(ctx, gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&pbUsualDevice.CheckUsualDeviceResp{Result: false}, nil),
		usualDeviceClient.EXPECT().GetDeviceAuthError(ctx, gomock.Any(), gomock.Any(), gomock.Any()).Return(nil),
		fellowSvrClient.EXPECT().ChannelSendFellowPresent(ctx, gomock.Any()).Return(&pbFellowSvr.ChannelSendFellowPresentResp{
			RemainCurrency: 10,
			RemainTbean:    10,
		}, nil),
	)

	type fields struct {
		fellowSvrClient   fellowsvr.IClient
		accountClient     account.IClient
		usualDeviceClient verifycode.IClient
		realNameClient    realnameauth.IClient
		presentClient     userPresent.IClient
		iopClient         iop_proxy.IClient
		userProfileApiCli user_profile_api.IClient
		ukwCli            youknowwho.IClient
	}
	type args struct {
		ctx context.Context
		in  *pb.ChannelSendFellowPresentReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.ChannelSendFellowPresentResp
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "ChannelSendFellowPresent",
			fields: fields{
				fellowSvrClient:   fellowSvrClient,
				accountClient:     accountCli,
				usualDeviceClient: usualDeviceClient,
				realNameClient:    realNameClient,
				presentClient:     presentClient,
				iopClient:         iopCli,
				userProfileApiCli: userProfileApiCli,
				ukwCli:            ukwCli,
			},
			args: args{
				ctx: ctx,
				in: &pb.ChannelSendFellowPresentReq{
					BaseReq: &pbApp.BaseReq{
						AppId:    0,
						MarketId: 0,
					},
					TargetUid: 0,
					PresentId: 0,
					ChannelId: 0,
				},
			},
			want:    out,
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			svr := &Server{
				fellowSvrClient:   tt.fields.fellowSvrClient,
				accountClient:     tt.fields.accountClient,
				usualDeviceClient: tt.fields.usualDeviceClient,
				realNameClient:    tt.fields.realNameClient,
				presentClient:     tt.fields.presentClient,
				iopClient:         tt.fields.iopClient,
				userProfileApiCli: tt.fields.userProfileApiCli,
				ukwCli:            tt.fields.ukwCli,
			}
			got, err := svr.ChannelSendFellowPresent(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("ChannelSendFellowPresent() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("ChannelSendFellowPresent() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestFellowLogic_CheckFellowInvite(t *testing.T) {
	ctl := gomock.NewController(t)
	initClient(ctl)
	defer ctl.Finish()

	var uid uint32 = 100
	ctx := context.Background()
	ctx = protogrpc.WithServiceInfo(ctx, &protogrpc.ServiceInfo{UserID: uid})

	res := &pbFellowSvr.CheckFellowInviteResp{
		ReachLimit: false,
		CpName:     "tt",
		CpSex:      0,
		UnlockInfo: &pbFellowSvr.UnlockInfo{
			UnlockPrice: 1,
			UnlockLevel: 2,
			UnlockSite:  3,
		},
	}

	gomock.InOrder(
		fellowSvrClient.EXPECT().CheckFellowInvite(gomock.Any(), gomock.Any()).Return(res, nil),
	)

	type fields struct {
		fellowSvrClient   fellowsvr.IClient
		accountClient     account.IClient
		usualDeviceClient verifycode.IClient
		realNameClient    realnameauth.IClient
		presentClient     userPresent.IClient
		iopClient         iop_proxy.IClient
		userProfileApiCli user_profile_api.IClient
		ukwCli            youknowwho.IClient
	}
	type args struct {
		ctx context.Context
		in  *pb.CheckFellowInviteReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.CheckFellowInviteResp
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "CheckFellowInvite",
			fields: fields{
				fellowSvrClient:   fellowSvrClient,
				accountClient:     accountCli,
				usualDeviceClient: usualDeviceClient,
				realNameClient:    realNameClient,
				presentClient:     presentClient,
				iopClient:         iopCli,
				userProfileApiCli: userProfileApiCli,
				ukwCli:            ukwCli,
			},
			args: args{
				ctx: ctx,
				in: &pb.CheckFellowInviteReq{
					BaseReq:  nil,
					BindType: 1,
				},
			},
			want: &pb.CheckFellowInviteResp{
				ReachLimit: false,
				CpName:     "tt",
				CpSex:      0,
				UnlockInfo: &pb.UnlockInfo{
					UnlockPrice: 1,
					UnlockLevel: 2,
					UnlockSite:  3,
				},
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			svr := &Server{
				fellowSvrClient:   tt.fields.fellowSvrClient,
				accountClient:     tt.fields.accountClient,
				usualDeviceClient: tt.fields.usualDeviceClient,
				realNameClient:    tt.fields.realNameClient,
				presentClient:     tt.fields.presentClient,
				iopClient:         tt.fields.iopClient,
				userProfileApiCli: tt.fields.userProfileApiCli,
				ukwCli:            tt.fields.ukwCli,
			}
			got, err := svr.CheckFellowInvite(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("CheckFellowInvite() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("CheckFellowInvite() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestFellowLogic_DelRare(t *testing.T) {
	ctl := gomock.NewController(t)
	initClient(ctl)
	defer ctl.Finish()

	var uid uint32 = 100
	ctx := context.Background()
	ctx = protogrpc.WithServiceInfo(ctx, &protogrpc.ServiceInfo{UserID: uid})

	gomock.InOrder(
		fellowSvrClient.EXPECT().DelRare(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil),
	)
	out := &pb.DelRareResp{}
	type fields struct {
		fellowSvrClient   fellowsvr.IClient
		accountClient     account.IClient
		usualDeviceClient verifycode.IClient
		realNameClient    realnameauth.IClient
		presentClient     userPresent.IClient
		iopClient         iop_proxy.IClient
		userProfileApiCli user_profile_api.IClient
		ukwCli            youknowwho.IClient
	}
	type args struct {
		ctx context.Context
		req *pb.DelRareReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.DelRareResp
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "CheckFellowInvite",
			fields: fields{
				fellowSvrClient:   fellowSvrClient,
				accountClient:     accountCli,
				usualDeviceClient: usualDeviceClient,
				realNameClient:    realNameClient,
				presentClient:     presentClient,
				iopClient:         iopCli,
				userProfileApiCli: userProfileApiCli,
				ukwCli:            ukwCli,
			},
			args: args{
				ctx: ctx,
				req: &pb.DelRareReq{
					BaseReq:   nil,
					Uid:       10,
					ToUid:     20,
					RareId:    10,
					SubRareId: 1,
				},
			},
			want:    out,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			svr := &Server{
				fellowSvrClient:   tt.fields.fellowSvrClient,
				accountClient:     tt.fields.accountClient,
				usualDeviceClient: tt.fields.usualDeviceClient,
				realNameClient:    tt.fields.realNameClient,
				presentClient:     tt.fields.presentClient,
				iopClient:         tt.fields.iopClient,
				userProfileApiCli: tt.fields.userProfileApiCli,
				ukwCli:            tt.fields.ukwCli,
			}
			got, err := svr.DelRare(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("DelRare() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("DelRare() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestFellowLogic_GetAllChannelFellowInvite(t *testing.T) {

	ctl := gomock.NewController(t)
	initClient(ctl)
	defer ctl.Finish()

	var uid uint32 = 100
	ctx := context.Background()
	ctx = protogrpc.WithServiceInfo(ctx, &protogrpc.ServiceInfo{UserID: uid})
	out := &pb.GetAllChannelFellowInviteResp{
		SendInviteList: []*pb.FellowInviteInfo{
			{
				InviteId:    "11",
				FromUid:     1,
				PresentInfo: &pb.FellowPresentInfo{},
			},
		},
		ReceivedInviteList: []*pb.FellowInviteInfo{
			{
				InviteId:    "22",
				FromUid:     2,
				PresentInfo: &pb.FellowPresentInfo{},
			},
		},
	}

	gomock.InOrder(
		fellowSvrClient.EXPECT().GetAllChannelFellowInvite(gomock.Any(), gomock.Any()).Return(&pbFellowSvr.GetAllChannelFellowInviteResp{
			SendInviteList: []*pbFellowSvr.FellowInviteInfo{
				{
					InviteId: "11",
					FromUid:  1,
				},
			},
			ReceivedInviteList: []*pbFellowSvr.FellowInviteInfo{
				{
					InviteId: "22",
					FromUid:  2,
				},
			},
		}, nil),
	)

	type fields struct {
		fellowSvrClient   fellowsvr.IClient
		accountClient     account.IClient
		usualDeviceClient verifycode.IClient
		realNameClient    realnameauth.IClient
		presentClient     userPresent.IClient
		iopClient         iop_proxy.IClient
		userProfileApiCli user_profile_api.IClient
		ukwCli            youknowwho.IClient
	}
	type args struct {
		ctx context.Context
		in  *pb.GetAllChannelFellowInviteReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.GetAllChannelFellowInviteResp
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "GetAllChannelFellowInvite",
			fields: fields{
				fellowSvrClient: fellowSvrClient,
			},
			args: args{
				ctx: ctx,
				in: &pb.GetAllChannelFellowInviteReq{
					BaseReq:   nil,
					ChannelId: 100,
				},
			},
			want:    out,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			svr := &Server{
				fellowSvrClient:   tt.fields.fellowSvrClient,
				accountClient:     tt.fields.accountClient,
				usualDeviceClient: tt.fields.usualDeviceClient,
				realNameClient:    tt.fields.realNameClient,
				presentClient:     tt.fields.presentClient,
				iopClient:         tt.fields.iopClient,
				userProfileApiCli: tt.fields.userProfileApiCli,
				ukwCli:            tt.fields.ukwCli,
			}
			got, err := svr.GetAllChannelFellowInvite(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetAllChannelFellowInvite() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetAllChannelFellowInvite() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestFellowLogic_GetChannelFellowCandidateInfo(t *testing.T) {
	ctl := gomock.NewController(t)
	initClient(ctl)
	defer ctl.Finish()

	var uid uint32 = 1000
	ctx := context.Background()
	ctx = protogrpc.WithServiceInfo(ctx, &protogrpc.ServiceInfo{UserID: uid})

	req := &pbFellowSvr.GetChannelFellowCandidateInfoReq{
		Uid:       uid,
		TargetUid: uid,
		ItemId:    10,
	}

	out := &pb.GetChannelFellowCandidateInfoResp{}

	resp := &pbFellowSvr.GetChannelFellowCandidateInfoResp{
		PresentInfo: &pbFellowSvr.FellowPresentInfo{
			ItemId:           10,
			Name:             "11",
			Value:            10,
			Icon:             "11",
			PriceType:        10,
			UniqueBackground: nil,
			MultiBackground:  nil,
			AlreadyOwn:       0,
		},
		UserInfo: &pbFellowSvr.FellowInviteUser{
			Uid:          0,
			Account:      "",
			NickName:     "",
			FellowPoint:  0,
			InviteStatus: 0,
			Sex:          0,
			InviteId:     "",
		},
		FellowOptionList:    nil,
		HasMultiFellowField: true,
		UnlockPrice:         100,
		HasCpField:          true,
	}

	out.FellowOptionList = make([]*pb.FellowOptionInfo, 0)

	out.PresentInfo = &pb.FellowPresentInfo{
		ItemId:    resp.GetPresentInfo().GetItemId(),
		PriceType: resp.GetPresentInfo().GetPriceType(),
		Name:      resp.GetPresentInfo().GetName(),
		Value:     resp.GetPresentInfo().GetValue(),
		Icon:      resp.GetPresentInfo().GetIcon(),
		UniqueBackground: &pb.FellowBackground{
			BackgroundUrl: resp.GetPresentInfo().GetUniqueBackground().GetBackgroundUrl(),
			Md5:           resp.GetPresentInfo().GetUniqueBackground().GetMd5(),
			SourceType:    resp.GetPresentInfo().GetUniqueBackground().GetSourceType(),
			BackgroundImg: resp.GetPresentInfo().GetUniqueBackground().GetBackgroundImg(),
		},
		MultiBackground: &pb.FellowBackground{
			BackgroundUrl: resp.GetPresentInfo().GetMultiBackground().GetBackgroundUrl(),
			Md5:           resp.GetPresentInfo().GetMultiBackground().GetMd5(),
			SourceType:    resp.GetPresentInfo().GetMultiBackground().GetSourceType(),
			BackgroundImg: resp.GetPresentInfo().GetUniqueBackground().GetBackgroundImg(),
		},
	}

	for _, item := range resp.GetFellowOptionList() {
		out.FellowOptionList = append(out.FellowOptionList, &pb.FellowOptionInfo{
			BindType:   item.GetBindType(),
			FellowType: item.GetFellowType(),
			FellowName: item.GetFellowName(),
		})
	}

	out.UserInfo = &pb.FellowInviteUser{
		Uid:          resp.GetUserInfo().GetUid(),
		Account:      resp.GetUserInfo().GetAccount(),
		NickName:     resp.GetUserInfo().GetNickName(),
		Sex:          resp.GetUserInfo().GetSex(),
		InviteStatus: resp.GetUserInfo().GetInviteStatus(),
		FellowPoint:  resp.GetUserInfo().GetFellowPoint(),
	}

	out.UnlockPrice = resp.GetUnlockPrice()
	out.HasMultiFellowField = resp.GetHasMultiFellowField()
	out.HasCpField = resp.GetHasCpField()

	gomock.InOrder(
		ukwCli.EXPECT().GetTrueUidByFake(ctx, gomock.Any()).Return(uid, nil),
		fellowSvrClient.EXPECT().GetChannelFellowCandidateInfo(ctx, req).Return(resp, nil),
	)
	type fields struct {
		fellowSvrClient   fellowsvr.IClient
		accountClient     account.IClient
		usualDeviceClient verifycode.IClient
		realNameClient    realnameauth.IClient
		presentClient     userPresent.IClient
		iopClient         iop_proxy.IClient
		userProfileApiCli user_profile_api.IClient
		ukwCli            youknowwho.IClient
	}
	type args struct {
		ctx context.Context
		in  *pb.GetChannelFellowCandidateInfoReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.GetChannelFellowCandidateInfoResp
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "GetChannelFellowCandidateInfo",
			fields: fields{
				fellowSvrClient: fellowSvrClient,
				ukwCli:          ukwCli,
			},
			args: args{
				ctx: ctx,
				in: &pb.GetChannelFellowCandidateInfoReq{
					TargetUid: uid,
					ItemId:    10,
				},
			},
			want:    out,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			svr := &Server{
				fellowSvrClient:   tt.fields.fellowSvrClient,
				accountClient:     tt.fields.accountClient,
				usualDeviceClient: tt.fields.usualDeviceClient,
				realNameClient:    tt.fields.realNameClient,
				presentClient:     tt.fields.presentClient,
				iopClient:         tt.fields.iopClient,
				userProfileApiCli: tt.fields.userProfileApiCli,
				ukwCli:            tt.fields.ukwCli,
			}
			got, err := svr.GetChannelFellowCandidateInfo(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetChannelFellowCandidateInfo() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetChannelFellowCandidateInfo() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestFellowLogic_GetChannelRareConfig(t *testing.T) {
	ctl := gomock.NewController(t)
	initClient(ctl)
	defer ctl.Finish()
	out := &pb.GetChannelRareConfigResp{}

	resp := &pbFellowSvr.GetChannelRareConfigResp{
		ChannelId: 100,
		IntroUrl:  "111",
		RareList: []*pbFellowSvr.RareConfig{
			{
				RareId: 1,
				Name:   "1",
				SubRareCfg: []*pbFellowSvr.SubRareConfig{
					{
						SubRareId: 1,
						Name:      "sub1",
						RareFlag:  "1",
					},
					{
						SubRareId: 2,
						Name:      "sub2",
						RareFlag:  "2",
					},
				},
				CpAnimation: &pbFellowSvr.AnimationConfig{
					ResourceUrl: "1",
					Md5:         "1",
				},
				MicConnected: &pbFellowSvr.ConnectedForMic{
					Left:  "Left",
					Right: "Right",
				},
				RareFlag:     "11",
				CardColor:    "1",
				CpBg:         nil,
				MidBg:        nil,
				MsgPictrures: nil,
			},
		},
		EntranceUrl: "1123",
	}

	out.ChannelId = 100
	out.IntroUrl = resp.GetIntroUrl()
	out.EntranceUrl = resp.GetEntranceUrl()
	for _, rare := range resp.GetRareList() {
		outRare := &pb.RareConfig{
			RareId: rare.GetRareId(),
			Name:   rare.GetName(),
			MicConnected: &pb.ConnectedForMic{
				Left:  rare.GetMicConnected().GetLeft(),
				Right: rare.GetMicConnected().GetRight(),
			},
			RareFlag: rare.GetRareFlag(),
			CpAnimation: &pb.AnimationConfig{
				ResourceUrl: rare.GetCpAnimation().GetResourceUrl(),
				Md5:         rare.GetCpAnimation().GetMd5(),
			},
			MsgPictrures: &pb.MsgNotifyPictures{
				Origin:    rare.GetMsgPictrures().GetOrigin(),
				Thumbnail: rare.GetMsgPictrures().GetThumbnail(),
			},
			MidBg: &pb.FellowBackground{
				BackgroundUrl: rare.GetMidBg().GetBackgroundUrl(),
				SourceType:    rare.GetMidBg().GetSourceType(),
				Md5:           rare.GetMidBg().GetMd5(),
				BackgroundImg: rare.GetMidBg().GetBackgroundImg(),
			},
			CpBg: &pb.FellowBackground{
				BackgroundUrl: rare.GetCpBg().GetBackgroundUrl(),
				SourceType:    rare.GetCpBg().GetSourceType(),
				Md5:           rare.GetCpBg().GetMd5(),
				BackgroundImg: rare.GetCpBg().GetBackgroundImg(),
			},
		}

		for _, v := range rare.GetSubRareCfg() {
			subRare := &pb.SubRareConfig{
				SubRareId: v.GetSubRareId(),
				Name:      v.GetName(),
			}
			outRare.SubRareCfg = append(outRare.SubRareCfg, subRare)
		}
		out.RareList = append(out.RareList, outRare)
	}

	gomock.InOrder(
		fellowSvrClient.EXPECT().GetChannelRareConfig(ctx, uint32(100)).Return(resp, nil),
	)

	type fields struct {
		fellowSvrClient   fellowsvr.IClient
		accountClient     account.IClient
		usualDeviceClient verifycode.IClient
		realNameClient    realnameauth.IClient
		presentClient     userPresent.IClient
		iopClient         iop_proxy.IClient
		userProfileApiCli user_profile_api.IClient
		ukwCli            youknowwho.IClient
	}
	type args struct {
		ctx context.Context
		req *pb.GetChannelRareConfigReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.GetChannelRareConfigResp
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "GetChannelRareConfig",
			fields: fields{
				fellowSvrClient: fellowSvrClient,
				ukwCli:          ukwCli,
			},
			args: args{
				ctx: ctx,
				req: &pb.GetChannelRareConfigReq{
					ChannelId: 100,
				},
			},
			want:    out,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			svr := &Server{
				fellowSvrClient:   tt.fields.fellowSvrClient,
				accountClient:     tt.fields.accountClient,
				usualDeviceClient: tt.fields.usualDeviceClient,
				realNameClient:    tt.fields.realNameClient,
				presentClient:     tt.fields.presentClient,
				iopClient:         tt.fields.iopClient,
				userProfileApiCli: tt.fields.userProfileApiCli,
				ukwCli:            tt.fields.ukwCli,
			}
			got, err := svr.GetChannelRareConfig(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetChannelRareConfig() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetChannelRareConfig() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestFellowLogic_GetFellowCandidateInfo(t *testing.T) {

	ctl := gomock.NewController(t)
	initClient(ctl)
	defer ctl.Finish()

	resp := &pbFellowSvr.GetFellowCandidateInfoResp{
		PresentInfo: []*pbFellowSvr.FellowPresentInfo{},
		UserInfo: &pbFellowSvr.FellowInviteUser{
			Uid:          10,
			Account:      "Account",
			NickName:     "NickName",
			FellowPoint:  100,
			InviteStatus: 10,
			Sex:          10,
			InviteId:     "",
		},
		MultiFellowOptionList: []*pbFellowSvr.FellowTypeInfo{
			{
				MultiFellowType: 1,
				MultiFellowName: "test",
			},
		},
		HasMultiFellowField: false,
		UnlockPrice:         10,
	}

	out := &pb.GetFellowCandidateInfoResp{}
	for _, item := range resp.PresentInfo {
		out.PresentInfo = append(out.PresentInfo, &pb.FellowPresentInfo{
			ItemId:    item.ItemId,
			PriceType: item.PriceType,
			Name:      item.Name,
			Value:     item.Value,
			Icon:      item.Icon,
			UniqueBackground: &pb.FellowBackground{
				BackgroundUrl: item.GetUniqueBackground().GetBackgroundUrl(),
				Md5:           item.GetUniqueBackground().GetMd5(),
				SourceType:    item.GetUniqueBackground().GetSourceType(),
				BackgroundImg: item.GetUniqueBackground().GetBackgroundImg(),
			},
			MultiBackground: &pb.FellowBackground{
				BackgroundUrl: item.GetMultiBackground().GetBackgroundUrl(),
				Md5:           item.GetMultiBackground().GetMd5(),
				SourceType:    item.GetMultiBackground().GetSourceType(),
				BackgroundImg: item.GetUniqueBackground().GetBackgroundImg(),
			},
		})
	}

	for _, item := range resp.MultiFellowOptionList {
		out.MultiFellowOptionList = append(out.MultiFellowOptionList, &pb.FellowTypeInfo{
			MultiFellowName: item.MultiFellowName,
			MultiFellowType: item.MultiFellowType,
		})
	}

	out.UserInfo = &pb.FellowInviteUser{
		Uid:          resp.UserInfo.Uid,
		Account:      resp.UserInfo.Account,
		NickName:     resp.UserInfo.NickName,
		Sex:          resp.UserInfo.Sex,
		InviteStatus: resp.UserInfo.InviteStatus,
		FellowPoint:  resp.UserInfo.FellowPoint,
	}

	out.UnlockPrice = resp.UnlockPrice
	out.HasMultiFellowField = resp.HasMultiFellowField

	gomock.InOrder(
		ukwCli.EXPECT().GetTrueUidByFake(gomock.Any(), gomock.Any()).Return(uint32(10), nil).AnyTimes(),
		fellowSvrClient.EXPECT().GetFellowCandidateInfo(gomock.Any(), gomock.Any()).Return(resp, nil).AnyTimes(),
	)

	type fields struct {
		fellowSvrClient   fellowsvr.IClient
		accountClient     account.IClient
		usualDeviceClient verifycode.IClient
		realNameClient    realnameauth.IClient
		presentClient     userPresent.IClient
		iopClient         iop_proxy.IClient
		userProfileApiCli user_profile_api.IClient
		ukwCli            youknowwho.IClient
	}
	type args struct {
		ctx context.Context
		in  *pb.GetFellowCandidateInfoReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.GetFellowCandidateInfoResp
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "GetFellowCandidateInfo",
			fields: fields{
				fellowSvrClient: fellowSvrClient,
				ukwCli:          ukwCli,
			},
			args: args{
				ctx: ctx,
				in: &pb.GetFellowCandidateInfoReq{
					BaseReq:   nil,
					TargetUid: 10,
				},
			},
			want:    out,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			svr := &Server{
				fellowSvrClient:   tt.fields.fellowSvrClient,
				accountClient:     tt.fields.accountClient,
				usualDeviceClient: tt.fields.usualDeviceClient,
				realNameClient:    tt.fields.realNameClient,
				presentClient:     tt.fields.presentClient,
				iopClient:         tt.fields.iopClient,
				userProfileApiCli: tt.fields.userProfileApiCli,
				ukwCli:            tt.fields.ukwCli,
			}
			got, err := svr.GetFellowCandidateInfo(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetFellowCandidateInfo() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got.GetUserInfo().GetUid(), tt.want.GetUserInfo().GetUid()) {
				t.Errorf("GetFellowCandidateInfo() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestFellowLogic_GetFellowCandidateList(t *testing.T) {
	ctl := gomock.NewController(t)
	initClient(ctl)
	defer ctl.Finish()
	out := &pb.GetFellowCandidateListResp{}

	resp := &pbFellowSvr.GetFellowCandidateListResp{
		FellowList: []*pbFellowSvr.FellowInviteUser{
			{
				Uid:          int64(uid),
				Account:      "Account",
				NickName:     "NickName",
				FellowPoint:  11,
				InviteStatus: 10,
				Sex:          1,
				InviteId:     "InviteId",
			},
		},
		IsReachEnd: false,
	}

	for _, item := range resp.FellowList {
		out.FellowList = append(out.FellowList, &pb.FellowInviteUser{
			Uid:          item.Uid,
			Account:      item.Account,
			NickName:     item.NickName,
			Sex:          item.Sex,
			InviteStatus: item.InviteStatus,
			FellowPoint:  item.FellowPoint,
			InviteId:     item.InviteId,
		})
	}
	out.IsReachEnd = resp.IsReachEnd

	gomock.InOrder(
		fellowSvrClient.EXPECT().GetFellowCandidateList(ctx, &pbFellowSvr.GetFellowCandidateListReq{
			Uid:      uid,
			BindType: 1,
			Page:     0,
			Count:    10,
		}).Return(resp, nil),
	)

	type fields struct {
		fellowSvrClient   fellowsvr.IClient
		accountClient     account.IClient
		usualDeviceClient verifycode.IClient
		realNameClient    realnameauth.IClient
		presentClient     userPresent.IClient
		iopClient         iop_proxy.IClient
		userProfileApiCli user_profile_api.IClient
		ukwCli            youknowwho.IClient
	}
	type args struct {
		ctx context.Context
		in  *pb.GetFellowCandidateListReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.GetFellowCandidateListResp
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "GetFellowCandidateList",
			fields: fields{
				fellowSvrClient: fellowSvrClient,
				ukwCli:          ukwCli,
			},
			args: args{
				ctx: ctx,
				in: &pb.GetFellowCandidateListReq{
					BaseReq:  nil,
					BindType: 1,
					Page:     0,
					Count:    10,
				},
			},
			want:    out,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			svr := &Server{
				fellowSvrClient:   tt.fields.fellowSvrClient,
				accountClient:     tt.fields.accountClient,
				usualDeviceClient: tt.fields.usualDeviceClient,
				realNameClient:    tt.fields.realNameClient,
				presentClient:     tt.fields.presentClient,
				iopClient:         tt.fields.iopClient,
				userProfileApiCli: tt.fields.userProfileApiCli,
				ukwCli:            tt.fields.ukwCli,
			}
			got, err := svr.GetFellowCandidateList(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetFellowCandidateList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetFellowCandidateList() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestFellowLogic_GetFellowInfoByUid(t *testing.T) {
	ctl := gomock.NewController(t)
	initClient(ctl)
	defer ctl.Finish()
	out := &pb.GetFellowInfoByUidResp{}
	resp := &pbFellowSvr.GetFellowInfoByUidResp{
		FellowInfo: &pbFellowSvr.FellowInfo{
			Uid:         int64(uid),
			Account:     "Account",
			NickName:    "NickName",
			FellowLevel: 0,
			FellowType:  0,
			DayCnt:      0,
			FellowName:  "FellowName",
			BindType:    0,
			FellowPoint: 0,
			Background: &pbFellowSvr.FellowBackground{
				BackgroundUrl: "BackgroundUrl",
				SourceType:    0,
				Md5:           "",
				BackgroundImg: "",
			},
			CpPresentIcon: "CpPresentIcon",
			PlateUrl:      nil,
			BindStatus:    0,
			BanType:       0,
			BindRare:      nil,
		},
	}

	out.FellowInfo = &pb.FellowInfo{
		Uid:           resp.GetFellowInfo().GetUid(),
		Account:       resp.GetFellowInfo().GetAccount(),
		NickName:      resp.GetFellowInfo().GetNickName(),
		FellowLevel:   resp.GetFellowInfo().GetFellowLevel(),
		FellowType:    resp.GetFellowInfo().GetFellowType(),
		DayCnt:        resp.GetFellowInfo().GetDayCnt(),
		FellowName:    resp.GetFellowInfo().GetFellowName(),
		BindType:      resp.GetFellowInfo().GetBindType(),
		FellowPoint:   resp.GetFellowInfo().GetFellowPoint(),
		CpPresentIcon: resp.GetFellowInfo().GetCpPresentIcon(),
		BindStatus:    resp.GetFellowInfo().GetBindStatus(),
		PlateUrl: &pb.PlateUrl{
			CpUnique:   resp.GetFellowInfo().GetPlateUrl().GetCpUnique(),
			DateUnique: resp.GetFellowInfo().GetPlateUrl().GetDateUnique(),
		},
		Background: &pb.FellowBackground{
			SourceType:    resp.GetFellowInfo().GetBackground().GetSourceType(),
			Md5:           resp.GetFellowInfo().GetBackground().GetMd5(),
			BackgroundUrl: resp.GetFellowInfo().GetBackground().GetBackgroundUrl(),
			BackgroundImg: resp.GetFellowInfo().GetBackground().GetBackgroundImg(),
		},
	}
	out.GetFellowInfo().Uid = int64(uid)
	gomock.InOrder(
		ukwCli.EXPECT().GetTrueUidByFake(gomock.Any(), gomock.Any()).Return(uint32(uid), nil).AnyTimes(),
		fellowSvrClient.EXPECT().GetFellowInfoByUid(gomock.Any(), gomock.Any()).AnyTimes().Return(resp, nil),
		userProfileApiCli.EXPECT().GetUserProfile(gomock.Any(), gomock.Any()).AnyTimes().Return(&pbApp.UserProfile{
			Uid:          uid,
			Account:      "Account",
			Nickname:     "Nickname",
			AccountAlias: "",
			Sex:          0,
			Privilege:    nil,
		}, nil),
	)

	type fields struct {
		fellowSvrClient   fellowsvr.IClient
		accountClient     account.IClient
		usualDeviceClient verifycode.IClient
		realNameClient    realnameauth.IClient
		presentClient     userPresent.IClient
		iopClient         iop_proxy.IClient
		userProfileApiCli user_profile_api.IClient
		ukwCli            youknowwho.IClient
	}
	type args struct {
		ctx context.Context
		in  *pb.GetFellowInfoByUidReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.GetFellowInfoByUidResp
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "GetFellowInfoByUid",
			fields: fields{
				fellowSvrClient:   fellowSvrClient,
				userProfileApiCli: userProfileApiCli,
				ukwCli:            ukwCli,
			},
			args: args{
				ctx: ctx,
				in: &pb.GetFellowInfoByUidReq{
					TargetUid: 110,
				},
			},
			want:    out,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			svr := &Server{
				fellowSvrClient:   tt.fields.fellowSvrClient,
				accountClient:     tt.fields.accountClient,
				usualDeviceClient: tt.fields.usualDeviceClient,
				realNameClient:    tt.fields.realNameClient,
				presentClient:     tt.fields.presentClient,
				iopClient:         tt.fields.iopClient,
				userProfileApiCli: tt.fields.userProfileApiCli,
				ukwCli:            tt.fields.ukwCli,
			}
			got, err := svr.GetFellowInfoByUid(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetFellowInfoByUid() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetFellowInfoByUid() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestFellowLogic_GetFellowInviteInfoById(t *testing.T) {

	ctl := gomock.NewController(t)
	initClient(ctl)
	defer ctl.Finish()
	out := &pb.GetFellowInviteInfoByIdResp{}

	resp := &pbFellowSvr.GetFellowInviteInfoByIdResp{
		InviteList: &pbFellowSvr.FellowInviteInfo{
			InviteId:     "111",
			FromUid:      10,
			FromAccount:  "FromAccount",
			FromNickname: "FromNickname",
			BindType:     1,
			FellowType:   1,
			FellowPoint:  110,
			CreateTime:   uint32(time.Now().Unix()),
			FellowName:   "tt",
			Status:       1,
			ToUid:        120,
			ToAccount:    "ToAccount",
			ToNickname:   "ToNickname",
			WithUnlock:   false,
			FromSex:      0,
			ToSex:        0,
			PresentInfo: &pbFellowSvr.FellowPresentInfo{
				ItemId:           10,
				Name:             "Name",
				Value:            180,
				Icon:             "Icon",
				PriceType:        0,
				UniqueBackground: nil,
				MultiBackground:  nil,
				AlreadyOwn:       0,
			},
			UnlockPrice: 0,
			ChannelId:   0,
			EndTime:     0,
		},
	}

	out.InviteInfo = &pb.FellowInviteInfo{
		InviteId:     resp.InviteList.InviteId,
		FromUid:      resp.InviteList.FromUid,
		FromAccount:  resp.InviteList.FromAccount,
		FromNickname: resp.InviteList.FromNickname,
		FromSex:      resp.InviteList.FromSex,
		BindType:     resp.InviteList.BindType,
		FellowType:   resp.InviteList.FellowType,
		FellowPoint:  resp.InviteList.FellowPoint,
		FellowName:   resp.InviteList.FellowName,
		CreateTime:   resp.InviteList.CreateTime,
		ToUid:        resp.InviteList.ToUid,
		ToSex:        resp.InviteList.ToSex,
		ToAccount:    resp.InviteList.ToAccount,
		ToNickname:   resp.InviteList.ToNickname,
		WithUnlock:   resp.InviteList.WithUnlock,
		Status:       resp.InviteList.Status,
		UnlockPrice:  resp.InviteList.UnlockPrice,
		PresentInfo: &pb.FellowPresentInfo{
			ItemId:    resp.InviteList.PresentInfo.ItemId,
			Name:      resp.InviteList.PresentInfo.Name,
			Icon:      resp.InviteList.PresentInfo.Icon,
			Value:     resp.InviteList.PresentInfo.Value,
			PriceType: resp.InviteList.PresentInfo.PriceType,
		},
	}

	gomock.InOrder(
		fellowSvrClient.EXPECT().GetFellowInviteInfoById(gomock.Any(), gomock.Any()).Return(resp, nil).AnyTimes(),
	)

	type fields struct {
		fellowSvrClient   fellowsvr.IClient
		accountClient     account.IClient
		usualDeviceClient verifycode.IClient
		realNameClient    realnameauth.IClient
		presentClient     userPresent.IClient
		iopClient         iop_proxy.IClient
		userProfileApiCli user_profile_api.IClient
		ukwCli            youknowwho.IClient
	}
	type args struct {
		ctx context.Context
		in  *pb.GetFellowInviteInfoByIdReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.GetFellowInviteInfoByIdResp
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "GetFellowInviteInfoById",
			fields: fields{
				fellowSvrClient: fellowSvrClient,
			},
			args: args{
				ctx: ctx,
				in: &pb.GetFellowInviteInfoByIdReq{
					BaseReq:  nil,
					InviteId: "xxx-xx",
				},
			},
			want:    out,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			svr := &Server{
				fellowSvrClient:   tt.fields.fellowSvrClient,
				accountClient:     tt.fields.accountClient,
				usualDeviceClient: tt.fields.usualDeviceClient,
				realNameClient:    tt.fields.realNameClient,
				presentClient:     tt.fields.presentClient,
				iopClient:         tt.fields.iopClient,
				userProfileApiCli: tt.fields.userProfileApiCli,
				ukwCli:            tt.fields.ukwCli,
			}
			got, err := svr.GetFellowInviteInfoById(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetFellowInviteInfoById() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetFellowInviteInfoById() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestFellowLogic_GetFellowInviteList(t *testing.T) {
	ctl := gomock.NewController(t)
	initClient(ctl)
	defer ctl.Finish()
	out := &pb.GetFellowInviteListResp{}
	resp := &pbFellowSvr.GetFellowInviteListResp{
		InviteList: []*pbFellowSvr.FellowInviteInfo{
			{
				InviteId:     "InviteId",
				FromUid:      0,
				FromAccount:  "",
				FromNickname: "",
				BindType:     0,
				FellowType:   0,
				FellowPoint:  0,
				CreateTime:   0,
				FellowName:   "",
				Status:       0,
				ToUid:        0,
				ToAccount:    "ToAccount",
				ToNickname:   "ToNickname",
				WithUnlock:   false,
				FromSex:      1,
				ToSex:        1,
				PresentInfo: &pbFellowSvr.FellowPresentInfo{
					ItemId:           10,
					Name:             "Name",
					Value:            0,
					Icon:             "",
					PriceType:        0,
					UniqueBackground: nil,
					MultiBackground:  nil,
					AlreadyOwn:       0,
				},
				UnlockPrice: 0,
				ChannelId:   0,
				EndTime:     0,
			},
		},
	}

	for _, item := range resp.InviteList {
		out.InviteList = append(out.InviteList, &pb.FellowInviteInfo{
			InviteId:     item.InviteId,
			FromUid:      item.FromUid,
			FromAccount:  item.FromAccount,
			FromNickname: item.FromNickname,
			FromSex:      item.FromSex,
			BindType:     item.BindType,
			FellowType:   item.FellowType,
			FellowPoint:  item.FellowPoint,
			FellowName:   item.FellowName,
			CreateTime:   item.CreateTime,
			ToUid:        item.ToUid,
			ToSex:        item.ToSex,
			ToAccount:    item.ToAccount,
			ToNickname:   item.ToNickname,
			WithUnlock:   item.WithUnlock,
			Status:       item.Status,
			PresentInfo: &pb.FellowPresentInfo{
				ItemId:    item.PresentInfo.ItemId,
				Name:      item.PresentInfo.Name,
				Icon:      item.PresentInfo.Icon,
				Value:     item.PresentInfo.Value,
				PriceType: item.PresentInfo.PriceType,
			},
		})
	}

	gomock.InOrder(
		fellowSvrClient.EXPECT().GetFellowInviteList(gomock.Any(), gomock.Any()).Return(resp, nil).AnyTimes(),
	)

	type fields struct {
		fellowSvrClient   fellowsvr.IClient
		accountClient     account.IClient
		usualDeviceClient verifycode.IClient
		realNameClient    realnameauth.IClient
		presentClient     userPresent.IClient
		iopClient         iop_proxy.IClient
		userProfileApiCli user_profile_api.IClient
		ukwCli            youknowwho.IClient
	}
	type args struct {
		ctx context.Context
		in  *pb.GetFellowInviteListReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.GetFellowInviteListResp
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "GetFellowInviteList",
			fields: fields{
				fellowSvrClient: fellowSvrClient,
			},
			args: args{
				ctx: ctx,
				in: &pb.GetFellowInviteListReq{
					BaseReq: nil,
				},
			},
			want:    out,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			svr := &Server{
				fellowSvrClient:   tt.fields.fellowSvrClient,
				accountClient:     tt.fields.accountClient,
				usualDeviceClient: tt.fields.usualDeviceClient,
				realNameClient:    tt.fields.realNameClient,
				presentClient:     tt.fields.presentClient,
				iopClient:         tt.fields.iopClient,
				userProfileApiCli: tt.fields.userProfileApiCli,
				ukwCli:            tt.fields.ukwCli,
			}
			got, err := svr.GetFellowInviteList(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetFellowInviteList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetFellowInviteList() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestFellowLogic_GetFellowList(t *testing.T) {
	ctl := gomock.NewController(t)
	initClient(ctl)
	defer ctl.Finish()
	out := &pb.GetFellowListResp{
		Uid: int64(uid),
	}
	resp := &pbFellowSvr.GetFellowListResp{
		Uid: int64(uid),
		UniqueFellow: &pbFellowSvr.FellowInfo{
			Uid:           10,
			Account:       "11",
			NickName:      "22",
			FellowLevel:   1,
			FellowType:    2,
			DayCnt:        3,
			FellowName:    "ff",
			BindType:      1,
			FellowPoint:   0,
			Background:    nil,
			CpPresentIcon: "",
			PlateUrl:      nil,
			BindStatus:    0,
			BanType:       0,
			BindRare:      nil,
		},
		MultiFellow: []*pbFellowSvr.FellowInfo{
			{
				Uid:           20,
				Account:       "2",
				NickName:      "1",
				FellowLevel:   10,
				FellowType:    10,
				DayCnt:        10,
				FellowName:    "",
				BindType:      0,
				FellowPoint:   0,
				Background:    nil,
				CpPresentIcon: "",
				PlateUrl:      nil,
				BindStatus:    0,
				BanType:       0,
				BindRare:      nil,
			},
		},
		FellowPositionCnt: 0,
		PendingInviteCnt:  0,
		UnlockInfo: &pbFellowSvr.UnlockInfo{
			UnlockPrice: 10,
			UnlockLevel: 10,
			UnlockSite:  10,
		},
		SendInviteCount: 0,
		RareTabList: []*pbFellowSvr.RareTabInfo{
			{
				ToUid:      10,
				ToAccount:  "2",
				ToNickName: "3",
				BindType:   1,
				FellowType: 2,
				FellowName: "",
				OutRare: &pbFellowSvr.RareInfo{
					RareId:      20,
					SubRareId:   30,
					DayCount:    40,
					RemainCount: 50,
					BindStatus:  false,
				},
				PresentBg: nil,
				RareCount: 0,
			},
		},
	}

	if resp.UniqueFellow != nil {
		fellow := resp.UniqueFellow
		out.UniqueFellow = fellowInfoSvrPB2LogicPB(fellow)
	}

	for _, fellow := range resp.MultiFellow {
		multiFellow := fellowInfoSvrPB2LogicPB(fellow)
		out.MultiFellow = append(out.MultiFellow, multiFellow)
	}

	out.FellowPositionCnt = resp.FellowPositionCnt
	out.PendingInviteCnt = resp.PendingInviteCnt
	out.UnlockInfo = &pb.UnlockInfo{
		UnlockPrice: resp.GetUnlockInfo().GetUnlockPrice(),
		UnlockLevel: resp.GetUnlockInfo().GetUnlockLevel(),
		UnlockSite:  resp.GetUnlockInfo().GetUnlockSite(),
	}
	out.SendInviteCount = resp.GetSendInviteCount()

	for _, rare := range resp.GetRareTabList() {
		rareTab := &pb.RareTabInfo{
			ToUid:      rare.GetToUid(),
			ToAccount:  rare.GetToAccount(),
			ToNickName: rare.GetToNickName(),
			BindType:   rare.GetBindType(),
			FellowType: rare.GetFellowType(),
			FellowName: rare.GetFellowName(),
		}

		outRare := rare.GetOutRare()
		if outRare.GetRareId() > 0 {
			rareTab.OutRare = &pb.RareInfo{
				RareId:      outRare.GetRareId(),
				SubRareId:   outRare.GetSubRareId(),
				DayCount:    outRare.GetDayCount(),
				RemainCount: outRare.GetRemainCount(),
				BindStatus:  outRare.GetBindStatus(),
			}
		}

		if rare.GetPresentBg() != nil {
			rareTab.PresentBg = &pb.FellowBackground{
				BackgroundUrl: rare.GetPresentBg().GetBackgroundUrl(),
				SourceType:    rare.GetPresentBg().GetSourceType(),
				Md5:           rare.GetPresentBg().GetMd5(),
				BackgroundImg: rare.GetPresentBg().GetBackgroundImg(),
			}
		}
		rareTab.RareCount = rare.RareCount
		out.RareTabList = append(out.RareTabList, rareTab)
	}

	gomock.InOrder(
		ukwCli.EXPECT().GetTrueUidByFake(gomock.Any(), gomock.Any()).Return(uid, nil).AnyTimes(),
		fellowSvrClient.EXPECT().GetFellowList(gomock.Any(), gomock.Any()).Return(resp, nil).AnyTimes(),
		fellowLevelAwardClient.EXPECT().BatchGetUserCurrentAwardInfo(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes(),
		userProfileApiCli.EXPECT().BatchGetUserProfile(gomock.Any(), gomock.Any()).Return(map[uint32]*pbApp.UserProfile{
			1: &pbApp.UserProfile{
				Uid:          1,
				Account:      "123",
				Nickname:     "123",
				AccountAlias: "123",
				Sex:          0,
				Privilege:    nil,
			},
		}, nil).AnyTimes(),
		channelOlCli.EXPECT().BatchGetUserChannelId(gomock.Any(), gomock.Any(), gomock.Any()).Return(map[uint32]uint32{1: 1, 2: 2}, nil).AnyTimes(),
		channelCli.EXPECT().BatchGetChannelSimpleInfo(gomock.Any(), gomock.Any(), gomock.Any()).Return(map[uint32]*pbChannel.ChannelSimpleInfo{
			1: &pbChannel.ChannelSimpleInfo{
				ChannelId:        &cid,
				ChannelType:      &cid,
				BindId:           &cid,
				EnterControlType: nil,
			},
		}, nil).AnyTimes(),

		superPlayerPrivilegeCli.EXPECT().BatchGetUserFellowVisibleProfile(gomock.Any(), gomock.Any()).Return(&super_player_privilege.BatchGetFellowVisibleProfilesResp{
			UidFellowVisibleMap: map[uint32]super_player_privilege.FellowVisible{1: super_player_privilege.FellowVisible_ENUM_FELLOW_VISIBLE_ME},
		}, nil).AnyTimes(),
	)

	type fields struct {
		fellowSvrClient     fellowsvr.IClient
		accountClient       account.IClient
		usualDeviceClient   verifycode.IClient
		realNameClient      realnameauth.IClient
		presentClient       userPresent.IClient
		iopClient           iop_proxy.IClient
		userProfileApiCli   user_profile_api.IClient
		ukwCli              youknowwho.IClient
		channelOlCli        channelol.IClient
		channelCLi          channel.IClient
		fellowLevelAwardCli fellow_level_award.FellowLevelAwardClient
	}
	type args struct {
		ctx context.Context
		in  *pb.GetFellowListReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.GetFellowListResp
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "GetFellowList",
			fields: fields{
				fellowSvrClient:     fellowSvrClient,
				realNameClient:      nil,
				presentClient:       nil,
				iopClient:           nil,
				userProfileApiCli:   userProfileApiCli,
				ukwCli:              ukwCli,
				channelOlCli:        channelOlCli,
				channelCLi:          channelCli,
				fellowLevelAwardCli: fellowLevelAwardClient,
			},
			args: args{
				ctx: ctx,
				in: &pb.GetFellowListReq{
					BaseReq: nil,
					Uid:     int64(uid),
				},
			},
			want:    out,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			svr := &Server{
				fellowSvrClient:     tt.fields.fellowSvrClient,
				accountClient:       tt.fields.accountClient,
				usualDeviceClient:   tt.fields.usualDeviceClient,
				realNameClient:      tt.fields.realNameClient,
				presentClient:       tt.fields.presentClient,
				iopClient:           tt.fields.iopClient,
				userProfileApiCli:   tt.fields.userProfileApiCli,
				ukwCli:              tt.fields.ukwCli,
				channelOlCli:        tt.fields.channelOlCli,
				channelCLi:          tt.fields.channelCLi,
				fellowLevelAwardCli: tt.fields.fellowLevelAwardCli,
			}
			got, err := svr.GetFellowList(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetFellowList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got.GetUid(), tt.want.GetUid()) {
				t.Errorf("GetFellowList() got = %v, want %v", got.GetUid(), tt.want.GetUid())
			}
		})
	}
}

func TestFellowLogic_GetFellowPoint(t *testing.T) {
	t.Skip()
	ctl := gomock.NewController(t)
	initClient(ctl)
	defer ctl.Finish()
	out := &pb.GetFellowPointResp{}

	resp := &pbFellowSvr.GetFellowPointResp{
		Uid:               uid,
		FellowUid:         10,
		FellowPoint:       100,
		FellowLevel:       1,
		CurrentLevelPoint: 200,
		NextLevelPoint:    10000,
	}

	out.FellowUid = uid
	out.FellowPoint = resp.GetFellowPoint()
	out.FellowLevel = resp.GetFellowLevel()
	out.CurrentLevelPoint = resp.GetCurrentLevelPoint()
	out.NextLevelPoint = resp.GetNextLevelPoint()

	gomock.InOrder(
		accountCli.EXPECT().GetUidByName(gomock.Any(), gomock.Any()).Return(uid, "", nil).AnyTimes(),
		fellowSvrClient.EXPECT().GetFellowPoint(gomock.Any(), gomock.Any()).Return(resp, nil).AnyTimes(),
		fellowLevelAwardClient.EXPECT().GetFellowIMAwardMsg(gomock.Any(), gomock.Any()).Return(&fellow_level_award.GetFellowIMAwardMsgResp{}, nil).AnyTimes(),
	)

	type fields struct {
		fellowSvrClient     fellowsvr.IClient
		accountClient       account.IClient
		usualDeviceClient   verifycode.IClient
		realNameClient      realnameauth.IClient
		presentClient       userPresent.IClient
		iopClient           iop_proxy.IClient
		userProfileApiCli   user_profile_api.IClient
		ukwCli              youknowwho.IClient
		fellowLevelAwardCli *fellow_level_award.Client
	}
	type args struct {
		ctx context.Context
		in  *pb.GetFellowPointReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.GetFellowPointResp
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "GetFellowPoint",
			fields: fields{
				fellowSvrClient: fellowSvrClient,
				accountClient:   accountCli,
				//fellowLevelAwardCli : fellowLevelAwardClient,
			},
			args: args{
				ctx: ctx,
				in: &pb.GetFellowPointReq{
					BaseReq:       nil,
					FellowAccount: "account",
				},
			},
			want:    out,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			svr := &Server{
				fellowSvrClient:   tt.fields.fellowSvrClient,
				accountClient:     tt.fields.accountClient,
				usualDeviceClient: tt.fields.usualDeviceClient,
				realNameClient:    tt.fields.realNameClient,
				presentClient:     tt.fields.presentClient,
				iopClient:         tt.fields.iopClient,
				userProfileApiCli: tt.fields.userProfileApiCli,
				ukwCli:            tt.fields.ukwCli,
			}
			got, err := svr.GetFellowPoint(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetFellowPoint() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetFellowPoint() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestFellowLogic_GetFellowPresentDetail(t *testing.T) {

	ctl := gomock.NewController(t)
	initClient(ctl)
	defer ctl.Finish()
	out := &pb.GetFellowPresentDetailResp{}

	resp := &pbFellowSvr.GetFellowPresentDetailResp{
		PresentList: []*pbFellowSvr.FellowPresentInfo{
			{
				ItemId:    1,
				Name:      "name1",
				Value:     10,
				Icon:      "icon",
				PriceType: 1,
				UniqueBackground: &pbFellowSvr.FellowBackground{
					BackgroundUrl: "BackgroundUrl",
					SourceType:    1,
					Md5:           "xxxxxxxxffffffffffffff",
					BackgroundImg: "BackgroundImg",
				},
				MultiBackground: &pbFellowSvr.FellowBackground{
					BackgroundUrl: "BackgroundUrl",
					SourceType:    1,
					Md5:           "xxxxxxxxxxxttttttttttttt",
					BackgroundImg: "BackgroundImg",
				},
				AlreadyOwn: 1,
			},
		},
		FellowPresent: &pbFellowSvr.FellowPresentInfo{
			ItemId:    2,
			Name:      "FellowPresent",
			Value:     1000,
			Icon:      "Icon",
			PriceType: 2,
			UniqueBackground: &pbFellowSvr.FellowBackground{
				BackgroundUrl: "BackgroundUrl",
				SourceType:    1,
				Md5:           "Md5",
				BackgroundImg: "BackgroundImg",
			},
			MultiBackground: &pbFellowSvr.FellowBackground{
				BackgroundUrl: "MultiBackground",
				SourceType:    1,
				Md5:           "MultiBackgroundMd5",
				BackgroundImg: "MultiBackground",
			},
			AlreadyOwn: 0,
		},
		FellowLevel: 1,
		FellowName:  "",
		DayCnt:      1,
		BindType:    0,
	}

	for _, item := range resp.GetPresentList() {
		out.PresentList = append(out.PresentList, &pb.FellowPresentInfo{
			ItemId:     item.ItemId,
			PriceType:  item.PriceType,
			Name:       item.Name,
			Value:      item.Value,
			Icon:       item.Icon,
			AlreadyOwn: item.AlreadyOwn,
			UniqueBackground: &pb.FellowBackground{
				BackgroundUrl: item.GetUniqueBackground().GetBackgroundUrl(),
				Md5:           item.GetUniqueBackground().GetMd5(),
				SourceType:    item.GetUniqueBackground().GetSourceType(),
				BackgroundImg: item.GetUniqueBackground().GetBackgroundImg(),
			},
			MultiBackground: &pb.FellowBackground{
				BackgroundUrl: item.GetMultiBackground().GetBackgroundUrl(),
				Md5:           item.GetMultiBackground().GetMd5(),
				SourceType:    item.GetMultiBackground().GetSourceType(),
				BackgroundImg: item.GetUniqueBackground().GetBackgroundImg(),
			},
		})
	}

	if resp.GetFellowPresent() != nil {
		out.FellowPresent = &pb.FellowPresentInfo{
			ItemId:    resp.FellowPresent.ItemId,
			PriceType: resp.FellowPresent.PriceType,
			Name:      resp.FellowPresent.Name,
			Value:     resp.FellowPresent.Value,
			Icon:      resp.FellowPresent.Icon,
			UniqueBackground: &pb.FellowBackground{
				BackgroundUrl: resp.FellowPresent.GetUniqueBackground().GetBackgroundUrl(),
				Md5:           resp.FellowPresent.GetUniqueBackground().GetMd5(),
				SourceType:    resp.FellowPresent.GetUniqueBackground().GetSourceType(),
				BackgroundImg: resp.FellowPresent.GetUniqueBackground().GetBackgroundImg(),
			},
			MultiBackground: &pb.FellowBackground{
				BackgroundUrl: resp.FellowPresent.GetMultiBackground().GetBackgroundUrl(),
				Md5:           resp.FellowPresent.GetMultiBackground().GetMd5(),
				SourceType:    resp.FellowPresent.GetMultiBackground().GetSourceType(),
				BackgroundImg: resp.FellowPresent.GetUniqueBackground().GetBackgroundImg(),
			},
		}
	}

	out.FellowName = resp.FellowName
	out.FellowLevel = resp.FellowLevel
	out.DayCnt = resp.DayCnt
	out.BindType = resp.BindType

	gomock.InOrder(
		fellowSvrClient.EXPECT().GetFellowPresentDetail(gomock.Any(), gomock.Any()).Return(resp, nil).AnyTimes(),
	)

	type fields struct {
		fellowSvrClient   fellowsvr.IClient
		accountClient     account.IClient
		usualDeviceClient verifycode.IClient
		realNameClient    realnameauth.IClient
		presentClient     userPresent.IClient
		iopClient         iop_proxy.IClient
		userProfileApiCli user_profile_api.IClient
		ukwCli            youknowwho.IClient
	}
	type args struct {
		ctx context.Context
		in  *pb.GetFellowPresentDetailReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.GetFellowPresentDetailResp
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "GetFellowPresentDetail",
			fields: fields{
				fellowSvrClient: fellowSvrClient,
			},
			args: args{
				ctx: ctx,
				in: &pb.GetFellowPresentDetailReq{
					BaseReq:   nil,
					TargetUid: uid,
				},
			},
			want:    out,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			svr := &Server{
				fellowSvrClient:   tt.fields.fellowSvrClient,
				accountClient:     tt.fields.accountClient,
				usualDeviceClient: tt.fields.usualDeviceClient,
				realNameClient:    tt.fields.realNameClient,
				presentClient:     tt.fields.presentClient,
				iopClient:         tt.fields.iopClient,
				userProfileApiCli: tt.fields.userProfileApiCli,
				ukwCli:            tt.fields.ukwCli,
			}
			got, err := svr.GetFellowPresentDetail(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetFellowPresentDetail() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetFellowPresentDetail() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestFellowLogic_GetOnMicFellowList(t *testing.T) {
	t.Skip()
	ctl := gomock.NewController(t)
	initClient(ctl)
	defer ctl.Finish()
	out := &pb.GetOnMicFellowListResp{}
	resp := &pbFellowSvr.GetOnMicFellowListResp{
		ChannelId: cid,
		MicFellow: []*pbFellowSvr.MicFellowInfo{
			{
				Uid:         uid,
				FellowUid:   11,
				FellowLevel: 1,
				FellowType:  2,
				BindType:    1,
				CurrentRare: &pbFellowSvr.RareInfo{
					RareId:      1,
					SubRareId:   2,
					DayCount:    10,
					RemainCount: 10,
					BindStatus:  true,
				},
			},
		},
	}

	for _, fellow := range resp.GetMicFellow() {
		micFellow := &pb.MicFellowInfo{
			Uid:         fellow.Uid,
			FellowUid:   fellow.FellowUid,
			FellowLevel: fellow.FellowLevel,
			FellowType:  fellow.FellowType,
			BindType:    fellow.BindType,
			CurrentRare: &pb.RareInfo{
				RareId:     fellow.GetCurrentRare().GetRareId(),
				SubRareId:  fellow.GetCurrentRare().GetSubRareId(),
				BindStatus: fellow.GetCurrentRare().BindStatus,
			},
		}
		out.MicFellow = append(out.MicFellow, micFellow)
	}

	gomock.InOrder(
		fellowSvrClient.EXPECT().GetOnMicFellowList(gomock.Any(), gomock.Any()).Return(resp, nil).AnyTimes(),
	)

	type fields struct {
		fellowSvrClient   fellowsvr.IClient
		accountClient     account.IClient
		usualDeviceClient verifycode.IClient
		realNameClient    realnameauth.IClient
		presentClient     userPresent.IClient
		iopClient         iop_proxy.IClient
		userProfileApiCli user_profile_api.IClient
		ukwCli            youknowwho.IClient
	}
	type args struct {
		ctx context.Context
		req *pb.GetOnMicFellowListReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.GetOnMicFellowListResp
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "GetOnMicFellowList",
			fields: fields{
				fellowSvrClient: fellowSvrClient,
			},
			args: args{
				ctx: ctx,
				req: &pb.GetOnMicFellowListReq{
					BaseReq:   nil,
					ChannelId: cid,
				},
			},
			want:    out,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			svr := &Server{
				fellowSvrClient:   tt.fields.fellowSvrClient,
				accountClient:     tt.fields.accountClient,
				usualDeviceClient: tt.fields.usualDeviceClient,
				realNameClient:    tt.fields.realNameClient,
				presentClient:     tt.fields.presentClient,
				iopClient:         tt.fields.iopClient,
				userProfileApiCli: tt.fields.userProfileApiCli,
				ukwCli:            tt.fields.ukwCli,
			}
			got, err := svr.GetOnMicFellowList(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetOnMicFellowList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetOnMicFellowList() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestFellowLogic_GetRareConfig(t *testing.T) {
	ctl := gomock.NewController(t)
	initClient(ctl)
	defer ctl.Finish()
	out := &pb.GetRareConfigResp{}
	resp := &pbFellowSvr.GetRareConfigResp{
		RareList: []*pbFellowSvr.RareConfig{
			{
				RareId: 1,
				Name:   "Name",
				SubRareCfg: []*pbFellowSvr.SubRareConfig{
					{
						SubRareId: 1,
						Name:      "SubName",
						RareFlag:  "t",
					},
				},
				CpAnimation: &pbFellowSvr.AnimationConfig{
					ResourceUrl: "ResourceUrl",
					Md5:         "Md5",
				},
				MicConnected: nil,
				RareFlag:     "",
				CardColor:    "",
				CpBg:         nil,
				MidBg:        nil,
				MsgPictrures: nil,
			},
		},
		DayConfig: []*pbFellowSvr.RareDayConfig{
			{
				Day:     1,
				CpValue: 10000,
				Animation: &pbFellowSvr.AnimationConfig{
					ResourceUrl: "ResourceUrl",
					Md5:         "Md5",
				},
				BackgroundUrl: "BackgroundUrl",
			},
		},
	}

	for _, rare := range resp.GetRareList() {
		outRare := &pb.RareConfig{
			RareId: rare.GetRareId(),
			Name:   rare.GetName(),
			MicConnected: &pb.ConnectedForMic{
				Left:  rare.GetMicConnected().GetLeft(),
				Right: rare.GetMicConnected().GetRight(),
			},
			RareFlag:  rare.GetRareFlag(),
			CardColor: rare.GetCardColor(),
			CpAnimation: &pb.AnimationConfig{
				ResourceUrl: rare.GetCpAnimation().GetResourceUrl(),
				Md5:         rare.GetCpAnimation().GetMd5(),
			},
			MsgPictrures: &pb.MsgNotifyPictures{
				Origin:    rare.GetMsgPictrures().GetOrigin(),
				Thumbnail: rare.GetMsgPictrures().GetThumbnail(),
			},
			MidBg: &pb.FellowBackground{
				BackgroundUrl: rare.GetMidBg().GetBackgroundUrl(),
				SourceType:    rare.GetMidBg().GetSourceType(),
				Md5:           rare.GetMidBg().GetMd5(),
				BackgroundImg: rare.GetMidBg().GetBackgroundImg(),
			},
			CpBg: &pb.FellowBackground{
				BackgroundUrl: rare.GetCpBg().GetBackgroundUrl(),
				SourceType:    rare.GetCpBg().GetSourceType(),
				Md5:           rare.GetCpBg().GetMd5(),
				BackgroundImg: rare.GetCpBg().GetBackgroundImg(),
			},
		}

		for _, v := range rare.GetSubRareCfg() {
			subRare := &pb.SubRareConfig{
				SubRareId: v.GetSubRareId(),
				Name:      v.GetName(),
				RareFlag:  v.GetRareFlag(),
			}
			outRare.SubRareCfg = append(outRare.SubRareCfg, subRare)
		}
		out.RareList = append(out.RareList, outRare)
	}

	gomock.InOrder(
		fellowSvrClient.EXPECT().GetRareConfig(gomock.Any()).Return(resp, nil).AnyTimes(),
	)

	type fields struct {
		fellowSvrClient   fellowsvr.IClient
		accountClient     account.IClient
		usualDeviceClient verifycode.IClient
		realNameClient    realnameauth.IClient
		presentClient     userPresent.IClient
		iopClient         iop_proxy.IClient
		userProfileApiCli user_profile_api.IClient
		ukwCli            youknowwho.IClient
	}
	type args struct {
		ctx context.Context
		req *pb.GetRareConfigReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.GetRareConfigResp
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "GetRareConfig",
			fields: fields{
				fellowSvrClient: fellowSvrClient,
			},
			args: args{
				ctx: ctx,
				req: &pb.GetRareConfigReq{
					BaseReq: nil,
				},
			},
			want:    out,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			svr := &Server{
				fellowSvrClient:   tt.fields.fellowSvrClient,
				accountClient:     tt.fields.accountClient,
				usualDeviceClient: tt.fields.usualDeviceClient,
				realNameClient:    tt.fields.realNameClient,
				presentClient:     tt.fields.presentClient,
				iopClient:         tt.fields.iopClient,
				userProfileApiCli: tt.fields.userProfileApiCli,
				ukwCli:            tt.fields.ukwCli,
			}
			got, err := svr.GetRareConfig(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetRareConfig() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetRareConfig() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestFellowLogic_GetRareList(t *testing.T) {
	ctl := gomock.NewController(t)
	initClient(ctl)
	defer ctl.Finish()
	out := &pb.GetRareListResp{}
	resp := &pbFellowSvr.GetRareListResp{
		Uid:        uid,
		ToUid:      uid,
		ToAccount:  "ToAccount",
		ToNickName: "ToNickName",
		BindType:   1,
		FellowType: 1,
		FellowName: "FellowName",
		RareList: []*pbFellowSvr.RareInfo{
			{
				RareId:      1,
				SubRareId:   2,
				DayCount:    10,
				RemainCount: 10,
				BindStatus:  false,
			},
		},
		PresentBg: nil,
	}

	out.Uid = uid
	out.ToUid = uid
	out.ToNickName = resp.GetToNickName()
	out.ToAccount = resp.GetToAccount()
	out.FellowName = resp.GetFellowName()
	out.BindType = resp.GetBindType()
	out.FellowType = resp.GetFellowType()
	out.PresentBg = &pb.FellowBackground{
		BackgroundUrl: resp.GetPresentBg().GetBackgroundUrl(),
		SourceType:    resp.GetPresentBg().GetSourceType(),
		Md5:           resp.GetPresentBg().GetMd5(),
		BackgroundImg: resp.GetPresentBg().GetBackgroundImg(),
	}

	for _, rare := range resp.GetRareList() {
		outRare := &pb.RareInfo{
			RareId:      rare.GetRareId(),
			SubRareId:   rare.GetSubRareId(),
			DayCount:    rare.GetDayCount(),
			RemainCount: rare.GetRemainCount(),
			BindStatus:  rare.GetBindStatus(),
		}
		out.RareList = append(out.RareList, outRare)
	}

	gomock.InOrder(
		ukwCli.EXPECT().GetTrueUidByFake(gomock.Any(), gomock.Any()).Return(uid, nil).AnyTimes(),
		fellowSvrClient.EXPECT().GetRareList(gomock.Any(), gomock.Any(), gomock.Any()).Return(resp, nil).AnyTimes(),
	)
	type fields struct {
		fellowSvrClient   fellowsvr.IClient
		accountClient     account.IClient
		usualDeviceClient verifycode.IClient
		realNameClient    realnameauth.IClient
		presentClient     userPresent.IClient
		iopClient         iop_proxy.IClient
		userProfileApiCli user_profile_api.IClient
		ukwCli            youknowwho.IClient
	}
	type args struct {
		ctx context.Context
		req *pb.GetRareListReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.GetRareListResp
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "GetRareList",
			fields: fields{
				fellowSvrClient: fellowSvrClient,
				ukwCli:          ukwCli,
			},
			args: args{
				ctx: ctx,
				req: &pb.GetRareListReq{
					BaseReq: nil,
					Uid:     uid,
					ToUid:   uid,
				},
			},
			want:    out,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			svr := &Server{
				fellowSvrClient:   tt.fields.fellowSvrClient,
				accountClient:     tt.fields.accountClient,
				usualDeviceClient: tt.fields.usualDeviceClient,
				realNameClient:    tt.fields.realNameClient,
				presentClient:     tt.fields.presentClient,
				iopClient:         tt.fields.iopClient,
				userProfileApiCli: tt.fields.userProfileApiCli,
				ukwCli:            tt.fields.ukwCli,
			}
			got, err := svr.GetRareList(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetRareList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetRareList() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestFellowLogic_GetRoomFellowList(t *testing.T) {
	type fields struct {
		fellowSvrClient   fellowsvr.IClient
		accountClient     account.IClient
		usualDeviceClient verifycode.IClient
		realNameClient    realnameauth.IClient
		presentClient     userPresent.IClient
		iopClient         iop_proxy.IClient
		userProfileApiCli user_profile_api.IClient
		ukwCli            youknowwho.IClient
	}
	type args struct {
		ctx context.Context
		in  *pb.GetRoomFellowListReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.GetRoomFellowListResp
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			svr := &Server{
				fellowSvrClient:   tt.fields.fellowSvrClient,
				accountClient:     tt.fields.accountClient,
				usualDeviceClient: tt.fields.usualDeviceClient,
				realNameClient:    tt.fields.realNameClient,
				presentClient:     tt.fields.presentClient,
				iopClient:         tt.fields.iopClient,
				userProfileApiCli: tt.fields.userProfileApiCli,
				ukwCli:            tt.fields.ukwCli,
			}
			got, err := svr.GetRoomFellowList(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetRoomFellowList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetRoomFellowList() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestFellowLogic_GetSendInviteList(t *testing.T) {
	type fields struct {
		fellowSvrClient   fellowsvr.IClient
		accountClient     account.IClient
		usualDeviceClient verifycode.IClient
		realNameClient    realnameauth.IClient
		presentClient     userPresent.IClient
		iopClient         iop_proxy.IClient
		userProfileApiCli user_profile_api.IClient
		ukwCli            youknowwho.IClient
	}
	type args struct {
		ctx context.Context
		req *pb.GetSendInviteListReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.GetSendInviteListResp
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			svr := &Server{
				fellowSvrClient:   tt.fields.fellowSvrClient,
				accountClient:     tt.fields.accountClient,
				usualDeviceClient: tt.fields.usualDeviceClient,
				realNameClient:    tt.fields.realNameClient,
				presentClient:     tt.fields.presentClient,
				iopClient:         tt.fields.iopClient,
				userProfileApiCli: tt.fields.userProfileApiCli,
				ukwCli:            tt.fields.ukwCli,
			}
			got, err := svr.GetSendInviteList(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetSendInviteList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetSendInviteList() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestFellowLogic_HandleChannelFellowInvite(t *testing.T) {
	type fields struct {
		fellowSvrClient   fellowsvr.IClient
		accountClient     account.IClient
		usualDeviceClient verifycode.IClient
		realNameClient    realnameauth.IClient
		presentClient     userPresent.IClient
		iopClient         iop_proxy.IClient
		userProfileApiCli user_profile_api.IClient
		ukwCli            youknowwho.IClient
	}
	type args struct {
		ctx context.Context
		in  *pb.HandleChannelFellowInviteReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.HandleChannelFellowInviteResp
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			svr := &Server{
				fellowSvrClient:   tt.fields.fellowSvrClient,
				accountClient:     tt.fields.accountClient,
				usualDeviceClient: tt.fields.usualDeviceClient,
				realNameClient:    tt.fields.realNameClient,
				presentClient:     tt.fields.presentClient,
				iopClient:         tt.fields.iopClient,
				userProfileApiCli: tt.fields.userProfileApiCli,
				ukwCli:            tt.fields.ukwCli,
			}
			got, err := svr.HandleChannelFellowInvite(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("HandleChannelFellowInvite() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("HandleChannelFellowInvite() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestFellowLogic_HandleFellowInvite(t *testing.T) {
	type fields struct {
		fellowSvrClient   fellowsvr.IClient
		accountClient     account.IClient
		usualDeviceClient verifycode.IClient
		realNameClient    realnameauth.IClient
		presentClient     userPresent.IClient
		iopClient         iop_proxy.IClient
		userProfileApiCli user_profile_api.IClient
		ukwCli            youknowwho.IClient
	}
	type args struct {
		ctx context.Context
		in  *pb.HandleFellowInviteReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.HandleFellowInviteResp
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			svr := &Server{
				fellowSvrClient:   tt.fields.fellowSvrClient,
				accountClient:     tt.fields.accountClient,
				usualDeviceClient: tt.fields.usualDeviceClient,
				realNameClient:    tt.fields.realNameClient,
				presentClient:     tt.fields.presentClient,
				iopClient:         tt.fields.iopClient,
				userProfileApiCli: tt.fields.userProfileApiCli,
				ukwCli:            tt.fields.ukwCli,
			}
			got, err := svr.HandleFellowInvite(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("HandleFellowInvite() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("HandleFellowInvite() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestFellowLogic_SendChannelFellowInvite(t *testing.T) {
	type fields struct {
		fellowSvrClient   fellowsvr.IClient
		accountClient     account.IClient
		usualDeviceClient verifycode.IClient
		realNameClient    realnameauth.IClient
		presentClient     userPresent.IClient
		iopClient         iop_proxy.IClient
		userProfileApiCli user_profile_api.IClient
		ukwCli            youknowwho.IClient
	}
	type args struct {
		ctx context.Context
		in  *pb.SendChannelFellowInviteReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.SendChannelFellowInviteResp
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			svr := &Server{
				fellowSvrClient:   tt.fields.fellowSvrClient,
				accountClient:     tt.fields.accountClient,
				usualDeviceClient: tt.fields.usualDeviceClient,
				realNameClient:    tt.fields.realNameClient,
				presentClient:     tt.fields.presentClient,
				iopClient:         tt.fields.iopClient,
				userProfileApiCli: tt.fields.userProfileApiCli,
				ukwCli:            tt.fields.ukwCli,
			}
			got, err := svr.SendChannelFellowInvite(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("SendChannelFellowInvite() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("SendChannelFellowInvite() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestFellowLogic_SendFellowInvite(t *testing.T) {
	type fields struct {
		fellowSvrClient   fellowsvr.IClient
		accountClient     account.IClient
		usualDeviceClient verifycode.IClient
		realNameClient    realnameauth.IClient
		presentClient     userPresent.IClient
		iopClient         iop_proxy.IClient
		userProfileApiCli user_profile_api.IClient
		ukwCli            youknowwho.IClient
	}
	type args struct {
		ctx context.Context
		in  *pb.SendFellowInviteReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.SendFellowInviteResp
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			svr := &Server{
				fellowSvrClient:   tt.fields.fellowSvrClient,
				accountClient:     tt.fields.accountClient,
				usualDeviceClient: tt.fields.usualDeviceClient,
				realNameClient:    tt.fields.realNameClient,
				presentClient:     tt.fields.presentClient,
				iopClient:         tt.fields.iopClient,
				userProfileApiCli: tt.fields.userProfileApiCli,
				ukwCli:            tt.fields.ukwCli,
			}
			got, err := svr.SendFellowInvite(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("SendFellowInvite() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("SendFellowInvite() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestFellowLogic_SendFellowPresent(t *testing.T) {
	type fields struct {
		fellowSvrClient   fellowsvr.IClient
		accountClient     account.IClient
		usualDeviceClient verifycode.IClient
		realNameClient    realnameauth.IClient
		presentClient     userPresent.IClient
		iopClient         iop_proxy.IClient
		userProfileApiCli user_profile_api.IClient
		ukwCli            youknowwho.IClient
	}
	type args struct {
		ctx context.Context
		in  *pb.SendFellowPresentReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.SendFellowPresentResp
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			svr := &Server{
				fellowSvrClient:   tt.fields.fellowSvrClient,
				accountClient:     tt.fields.accountClient,
				usualDeviceClient: tt.fields.usualDeviceClient,
				realNameClient:    tt.fields.realNameClient,
				presentClient:     tt.fields.presentClient,
				iopClient:         tt.fields.iopClient,
				userProfileApiCli: tt.fields.userProfileApiCli,
				ukwCli:            tt.fields.ukwCli,
			}
			got, err := svr.SendFellowPresent(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("SendFellowPresent() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("SendFellowPresent() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestFellowLogic_SetBindRelation(t *testing.T) {
	type fields struct {
		fellowSvrClient   fellowsvr.IClient
		accountClient     account.IClient
		usualDeviceClient verifycode.IClient
		realNameClient    realnameauth.IClient
		presentClient     userPresent.IClient
		iopClient         iop_proxy.IClient
		userProfileApiCli user_profile_api.IClient
		ukwCli            youknowwho.IClient
	}
	type args struct {
		ctx context.Context
		req *pb.SetBindRelationReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.SetBindRelationResp
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			svr := &Server{
				fellowSvrClient:   tt.fields.fellowSvrClient,
				accountClient:     tt.fields.accountClient,
				usualDeviceClient: tt.fields.usualDeviceClient,
				realNameClient:    tt.fields.realNameClient,
				presentClient:     tt.fields.presentClient,
				iopClient:         tt.fields.iopClient,
				userProfileApiCli: tt.fields.userProfileApiCli,
				ukwCli:            tt.fields.ukwCli,
			}
			got, err := svr.SetBindRelation(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("SetBindRelation() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("SetBindRelation() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestFellowLogic_UnlockFellowPosition(t *testing.T) {
	type fields struct {
		fellowSvrClient   fellowsvr.IClient
		accountClient     account.IClient
		usualDeviceClient verifycode.IClient
		realNameClient    realnameauth.IClient
		presentClient     userPresent.IClient
		iopClient         iop_proxy.IClient
		userProfileApiCli user_profile_api.IClient
		ukwCli            youknowwho.IClient
	}
	type args struct {
		ctx context.Context
		in  *pb.UnlockFellowPositionReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.UnlockFellowPositionResp
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			svr := &Server{
				fellowSvrClient:   tt.fields.fellowSvrClient,
				accountClient:     tt.fields.accountClient,
				usualDeviceClient: tt.fields.usualDeviceClient,
				realNameClient:    tt.fields.realNameClient,
				presentClient:     tt.fields.presentClient,
				iopClient:         tt.fields.iopClient,
				userProfileApiCli: tt.fields.userProfileApiCli,
				ukwCli:            tt.fields.ukwCli,
			}
			got, err := svr.UnlockFellowPosition(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("UnlockFellowPosition() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("UnlockFellowPosition() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestFellowLogic_checkUsualDevice(t *testing.T) {
	type fields struct {
		fellowSvrClient   fellowsvr.IClient
		accountClient     account.IClient
		usualDeviceClient verifycode.IClient
		realNameClient    realnameauth.IClient
		presentClient     userPresent.IClient
		iopClient         iop_proxy.IClient
		userProfileApiCli user_profile_api.IClient
		ukwCli            youknowwho.IClient
	}
	type args struct {
		ctx         context.Context
		serviceInfo *protogrpc.ServiceInfo
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			svr := &Server{
				fellowSvrClient:   tt.fields.fellowSvrClient,
				accountClient:     tt.fields.accountClient,
				usualDeviceClient: tt.fields.usualDeviceClient,
				realNameClient:    tt.fields.realNameClient,
				presentClient:     tt.fields.presentClient,
				iopClient:         tt.fields.iopClient,
				userProfileApiCli: tt.fields.userProfileApiCli,
				ukwCli:            tt.fields.ukwCli,
			}
			if err := svr.checkUsualDevice(tt.args.ctx, tt.args.serviceInfo); (err != nil) != tt.wantErr {
				t.Errorf("checkUsualDevice() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestNewServer(t *testing.T) {
	type args struct {
		ctx    context.Context
		conf   *conf.StartConfig
		tracer opentracing.Tracer
	}
	tests := []struct {
		name    string
		args    args
		want    *Server
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := NewServer(tt.args.ctx, tt.args.conf)
			if (err != nil) != tt.wantErr {
				t.Errorf("NewServer() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("NewServer() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_handleFellowInfo(t *testing.T) {
	type args struct {
		fellowInfo *pb.FellowInfo
		up         *pbApp.UserProfile
	}
	tests := []struct {
		name string
		args args
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
		})
	}
}
