package mgr

import (
    "context"
    "errors"
    "fmt"
    "github.com/golang/mock/gomock"
    "github.com/stretchr/testify/assert"
    "golang.52tt.com/services/tt-rev/esport/esport-customer/internal/rpc"
    "testing"
    "time"
)

type helperForTest struct {
    *Mgr
}

func newHelperForTest(t *testing.T) *helperForTest {
    controller := gomock.NewController(t)
    return &helperForTest{
        &Mgr{
            hostingStatusStore:   NewMockHostingStatusStore(controller),
            customerAccountStore: NewMockCustomerAccountStore(controller),
            byLink:               rpc.NewMockByLink(controller),
        },
    }
}

func (m *helperForTest) getHostingStatusStore() *MockHostingStatusStore {
    return m.hostingStatusStore.(*MockHostingStatusStore)
}

func (m *helperForTest) getCustomerAccountStore() *MockCustomerAccountStore {
    return m.customerAccountStore.(*MockCustomerAccountStore)
}

func (m *helperForTest) getByLink() *rpc.MockByLink {
    return m.byLink.(*rpc.MockByLink)
}

func Test_AddHostingStatus_Success(t *testing.T) {
    ctrl := gomock.NewController(t)
    defer ctrl.Finish()

    helper := newHelperForTest(t)
    mockStore := helper.getHostingStatusStore()

    ctx := context.Background()
    customerUid := uint32(1)
    coachUids := []uint32{2, 3, 4}

    // Expectations
    mockStore.EXPECT().AddHostingStatus(ctx, customerUid, coachUids).Return(nil)
    helper.getByLink().EXPECT().AsynReportCoachEnableCustomer(gomock.Any(), gomock.Any(), gomock.Any())

    // Call the method
    success, err := helper.AddHostingStatus(ctx, customerUid, coachUids)

    // Assertions
    assert.NoError(t, err)
    assert.True(t, success)
}

func Test_AddHostingStatus_ContextCancelled(t *testing.T) {
    // Create a new mock controller
    ctrl := gomock.NewController(t)
    defer ctrl.Finish()

    // Create a new helperForTest instance
    helper := newHelperForTest(t)

    // Create a context that gets canceled
    ctx, cancel := context.WithCancel(context.Background())
    cancel() // Cancel the context immediately

    // Define test parameters
    customerUid := uint32(1)
    coachUids := []uint32{2, 3, 4}

    // Expect AddHostingStatus to be called on the mock store and return a context canceled error
    helper.getHostingStatusStore().EXPECT().AddHostingStatus(ctx, customerUid, coachUids).Return(context.Canceled)

    // Call the method
    success, err := helper.AddHostingStatus(ctx, customerUid, coachUids)

    // Validate the results
    assert.False(t, success)
    assert.ErrorIs(t, err, context.Canceled)
    assert.EqualError(t, err, "添加托管大神失败: context canceled")
}

func Test_AddHostingStatus_EmptyCoachUids(t *testing.T) {
    // Create a new mock controller for the test
    ctrl := gomock.NewController(t)
    defer ctrl.Finish()

    // Create a new helper for the test
    helper := newHelperForTest(t)

    // Define the input parameters
    ctx := context.Background()
    customerUid := uint32(1)
    coachUids := []uint32{}

    // Set up the expectations on the mock objects
    helper.getHostingStatusStore().EXPECT().AddHostingStatus(ctx, customerUid, coachUids).Return(nil)
    helper.getByLink().EXPECT().AsynReportCoachEnableCustomer(gomock.Any(), gomock.Any(), gomock.Any())

    // Call the method under test
    result, err := helper.AddHostingStatus(ctx, customerUid, coachUids)

    // Assert the expected results
    assert.Nil(t, err)
    assert.True(t, result)
}

func Test_AddHostingStatus_InvalidCustomerUid(t *testing.T) {
    ctrl := gomock.NewController(t)
    defer ctrl.Finish()

    // Create a helper for the test
    helper := newHelperForTest(t)

    // Mock context and input parameters
    ctx := context.Background()
    invalidCustomerUid := uint32(0) // Assuming 0 is an invalid UID
    coachUids := []uint32{1, 2, 3}

    // Set up expected behavior for the mock ManagedGodStore
    expectedError := errors.New("invalid customer UID")
    helper.getHostingStatusStore().EXPECT().AddHostingStatus(ctx, invalidCustomerUid, coachUids).Return(expectedError)

    // Call the method under test
    mgr := helper.Mgr
    success, err := mgr.AddHostingStatus(ctx, invalidCustomerUid, coachUids)

    // Assert the results
    assert.False(t, success)
    assert.EqualError(t, err, "添加托管大神失败: invalid customer UID")
}

func Test_AddHostingStatus_Failure_StoreError(t *testing.T) {
    // Create a new gomock controller.
    ctrl := gomock.NewController(t)
    defer ctrl.Finish()

    // Create a new instance of helperForTest.
    helper := newHelperForTest(t)

    // Define the input values.
    ctx := context.Background()
    customerUid := uint32(1)
    coachUids := []uint32{2, 3, 4}

    // Define the expected error from the store.
    expectedErr := errors.New("store error")

    // Set up the expectation on the mock store.
    helper.getHostingStatusStore().EXPECT().AddHostingStatus(ctx, customerUid, coachUids).Return(expectedErr)

    // Call the method under test.
    success, err := helper.AddHostingStatus(ctx, customerUid, coachUids)

    // Check the results.
    assert.False(t, success)
    assert.EqualError(t, err, "添加托管大神失败: store error")
}

func TestMgr_GetHostingStatusList_ValidCoachUids(t *testing.T) {
    // Arrange
    helper := newHelperForTest(t)
    ctx := context.Background()
    coachUids := []uint32{1, 2, 3}
    expectedManagedGods := []*HostingStatus{
        {CoachUID: 1, CustomerUID: 1},
        {CoachUID: 2, CustomerUID: 2},
        {CoachUID: 3, CustomerUID: 3},
    }

    helper.getHostingStatusStore().EXPECT().GetHostingStatusList(ctx, coachUids).Return(expectedManagedGods, nil)

    // Act
    managedGods, err := helper.GetHostingStatusList(ctx, coachUids)

    // Assert
    assert.NoError(t, err)
    assert.Equal(t, expectedManagedGods, managedGods)
}

func TestMgr_GetHostingStatusList_EmptyCoachUids(t *testing.T) {
    // Initialize the gomock controller and finish it at the end of the test
    ctrl := gomock.NewController(t)
    defer ctrl.Finish()

    // Create a helper for the test
    helper := newHelperForTest(t)

    // Prepare the context and input for the test case
    ctx := context.Background()
    coachUids := []uint32{}

    // Set up the expected behavior of the mockManagedGodStore
    helper.getHostingStatusStore().EXPECT().GetHostingStatusList(ctx, coachUids).Return([]*HostingStatus{}, nil)

    // Call the function under test
    managedGods, err := helper.GetHostingStatusList(ctx, coachUids)

    // Assert that the function returns the expected results
    assert.NoError(t, err)
    assert.Empty(t, managedGods)
}

func TestMgr_GetHostingStatusList_InvalidCoachUids(t *testing.T) {
    t.Helper()
    ctrl := gomock.NewController(t)
    defer ctrl.Finish()

    h := newHelperForTest(t)
    ctx := context.Background()
    invalidCoachUids := []uint32{9999, 8888}

    expectedErr := errors.New("查询大神托管的客户号失败: some error")

    h.getHostingStatusStore().EXPECT().GetHostingStatusList(ctx, invalidCoachUids).Return(nil, expectedErr)

    managedGods, err := h.GetHostingStatusList(ctx, invalidCoachUids)

    assert.Nil(t, managedGods)
    assert.Error(t, err)
}

func TestMgr_GetHostingStatusList_PartialFailure(t *testing.T) {
    ctrl := gomock.NewController(t)
    defer ctrl.Finish()

    h := newHelperForTest(t)
    mgr := h.Mgr

    ctx := context.Background()
    coachUids := []uint32{1, 2, 3}

    // Simulate partial failure
    h.getHostingStatusStore().EXPECT().GetHostingStatusList(ctx, coachUids).Return(nil, errors.New("partial failure"))

    managedGods, err := mgr.GetHostingStatusList(ctx, coachUids)

    assert.Nil(t, managedGods)
    assert.NotNil(t, err)
    assert.EqualError(t, err, "查询大神托管的客户号失败: partial failure")
}

func TestMgr_GetHostingStatusList_ContextTimeout(t *testing.T) {
    // Initialize gomock controller
    ctrl := gomock.NewController(t)
    defer ctrl.Finish()

    // Create a helper for test
    helper := newHelperForTest(t)

    // Create a context that will timeout
    ctx, cancel := context.WithTimeout(context.Background(), 1*time.Millisecond)
    defer cancel()

    // Wait for the timeout to occur
    time.Sleep(2 * time.Millisecond)

    // Define the coach UIDs to query
    coachUids := []uint32{1, 2, 3}

    // Set up the mock to return a context timeout error
    helper.getHostingStatusStore().EXPECT().GetHostingStatusList(gomock.Any(), coachUids).Return(nil, context.DeadlineExceeded)

    // Call the method under test
    managedGods, err := helper.Mgr.GetHostingStatusList(ctx, coachUids)

    // Check the results
    assert.Nil(t, managedGods)
    assert.NotNil(t, err)
    assert.Equal(t, "查询大神托管的客户号失败: context deadline exceeded", err.Error())
}

func TestMgr_RemoveHostingStatus_EmptyCoachUids(t *testing.T) {
    ctrl := gomock.NewController(t)
    defer ctrl.Finish()

    // Initialize the helper for test
    helper := newHelperForTest(t)

    // Set up the context and input parameters
    ctx := context.Background()
    customerUid := uint32(1)
    var coachUids []uint32

    // Set up the mock expectations
    helper.getHostingStatusStore().EXPECT().RemoveHostingStatus(ctx, customerUid, coachUids).Return(nil)
    helper.getByLink().EXPECT().AsynReportCoachDisableCustomer(gomock.Any(), gomock.Any(), gomock.Any())

    // Call the function
    result, err := helper.Mgr.RemoveHostingStatus(ctx, customerUid, coachUids)

    // Assertions
    assert.NoError(t, err)
    assert.True(t, result)
}

func TestMgr_RemoveHostingStatus_ValidInputs(t *testing.T) {
    ctrl := gomock.NewController(t)
    defer ctrl.Finish()

    // Create the helperForTest
    h := newHelperForTest(t)

    // Define the inputs
    ctx := context.Background()
    customerUid := uint32(1)
    coachUids := []uint32{2, 3, 4}

    // Set the expectation on the mock ManagedGodStore
    h.getHostingStatusStore().EXPECT().RemoveHostingStatus(ctx, customerUid, coachUids).Return(nil)
    h.getByLink().EXPECT().AsynReportCoachDisableCustomer(gomock.Any(), gomock.Any(), gomock.Any())

    // Call the function
    result, err := h.RemoveHostingStatus(ctx, customerUid, coachUids)

    // Assert the results
    assert.NoError(t, err)
    assert.True(t, result)
}

func TestMgr_RemoveHostingStatus_InvalidCustomerUid(t *testing.T) {
    ctrl := gomock.NewController(t)
    defer ctrl.Finish()

    // Create a helper for test
    helper := newHelperForTest(t)

    ctx := context.Background()
    invalidCustomerUid := uint32(0)
    coachUids := []uint32{1, 2, 3}

    expectedError := errors.New("invalid customer UID")
    helper.getHostingStatusStore().EXPECT().RemoveHostingStatus(ctx, invalidCustomerUid, coachUids).Return(expectedError)

    result, err := helper.RemoveHostingStatus(ctx, invalidCustomerUid, coachUids)

    assert.False(t, result)
    assert.Error(t, err)
    assert.Equal(t, "移除托管大神失败: invalid customer UID", err.Error())
}

func TestMgr_RemoveHostingStatus_NilContext(t *testing.T) {
    // Create a new gomock controller
    ctrl := gomock.NewController(t)
    defer ctrl.Finish()

    // Create a helper for test that sets up Mgr with mocks
    helper := newHelperForTest(t)

    // Nil context
    ctx := context.Background()

    // Setting up mock expectations
    expectedErr := errors.New("context is nil")
    helper.getHostingStatusStore().EXPECT().RemoveHostingStatus(ctx, uint32(1), []uint32{1, 2, 3}).Return(expectedErr)

    // Calling the method
    success, err := helper.RemoveHostingStatus(ctx, 1, []uint32{1, 2, 3})

    // Asserting the results
    assert.False(t, success)
    assert.EqualError(t, err, "移除托管大神失败: context is nil")
}

func TestMgr_RemoveHostingStatus_NonExistingCoachUids(t *testing.T) {
    ctrl := gomock.NewController(t)
    defer ctrl.Finish()

    h := newHelperForTest(t)

    ctx := context.Background()
    customerUid := uint32(1)
    nonExistingCoachUids := []uint32{10, 20, 30}

    expectedError := errors.New("coach UIDs do not exist")

    h.getHostingStatusStore().EXPECT().RemoveHostingStatus(ctx, customerUid, nonExistingCoachUids).Return(expectedError)

    success, err := h.RemoveHostingStatus(ctx, customerUid, nonExistingCoachUids)

    assert.False(t, success)
    assert.Error(t, err)
    assert.Equal(t, "移除托管大神失败: coach UIDs do not exist", err.Error())
}

func TestSaveCustomerAccount_Success(t *testing.T) {
    // Create a new controller for gomock
    ctrl := gomock.NewController(t)
    defer ctrl.Finish()

    // Create a new helper for the test
    helper := newHelperForTest(t)

    // Define the context and the customer account to be saved
    ctx := context.Background()
    customerAccount := &CustomerAccount{
        CustomerUID: 1,
        GuildID:     2,
        Creator:     "2",
        Password:    "3123",
        Status:      1,
        CreateTime:  time.Now(),
        UpdateTime:  time.Now(),
    }

    // Mock the SaveCustomerAccount method to return nil error
    helper.getCustomerAccountStore().EXPECT().SaveCustomerAccount(ctx, customerAccount).Return(nil)

    // Call the method under test
    success, err := helper.SaveCustomerAccount(ctx, customerAccount)

    // Validate the results
    assert.NoError(t, err)
    assert.True(t, success)
}

func TestSaveCustomerAccount_Error(t *testing.T) {
    // Arrange
    helper := newHelperForTest(t)

    ctx := context.Background()
    customerAccount := &CustomerAccount{
        // Fill in the necessary fields
    }

    // Mock the behavior of the CustomerAccountStore to return an error
    expectedError := errors.New("database error")
    helper.getCustomerAccountStore().EXPECT().SaveCustomerAccount(ctx, customerAccount).Return(expectedError)

    // Act
    success, err := helper.SaveCustomerAccount(ctx, customerAccount)

    // Assert
    assert.False(t, success)
    assert.Error(t, err)
    assert.Equal(t, "保存客服账号信息失败: database error", err.Error())
}

func TestMgr_GetCustomerAccounts_InvalidCustomerUid(t *testing.T) {
    ctrl := gomock.NewController(t)
    defer ctrl.Finish()

    // Create a helper for the test
    helper := newHelperForTest(t)

    // Set up the context
    ctx := context.Background()

    // Define the input parameters
    page, size := int32(1), int32(10)
    guildId, customerUid := uint32(1), uint32(0) // Here, customerUid is invalid (set to 0)

    // Expected error
    expectedErr := errors.New("invalid customerUid")

    // Mock the behavior of the customerAccountStore to return an error for invalid customerUid
    helper.getCustomerAccountStore().EXPECT().GetCustomerAccounts(ctx, page, size, guildId, gomock.Any()).Return(nil, int32(0), expectedErr)

    // Call the function under test
    accounts, total, err := helper.GetCustomerAccounts(ctx, page, size, guildId, []uint32{customerUid})

    // Assert the results
    assert.Nil(t, accounts)
    assert.Equal(t, int32(0), total)
    assert.EqualError(t, err, "获取客服账号列表失败: invalid customerUid")
}

func TestMgr_GetCustomerAccounts_ErrorDueToInvalidGuildId(t *testing.T) {
    // Create a new mock controller
    ctrl := gomock.NewController(t)
    defer ctrl.Finish()

    // Create a new helper for the test
    helper := newHelperForTest(t)

    // Define the context
    ctx := context.Background()

    // Define the input parameters
    page := int32(1)
    size := int32(10)
    guildId := uint32(0) // Assuming 0 is an invalid guildId
    customerUid := uint32(123)

    // Define the expected error
    expectedError := errors.New("获取客服账号列表失败: invalid guildId")

    // Mock the GetCustomerAccounts method to return an error
    helper.getCustomerAccountStore().EXPECT().GetCustomerAccounts(ctx, page, size, guildId, gomock.Any()).Return(nil, int32(0), errors.New("invalid guildId"))

    // Call the method
    customerAccounts, total, err := helper.Mgr.GetCustomerAccounts(ctx, page, size, guildId, []uint32{customerUid})

    // Assert the results
    assert.Nil(t, customerAccounts)
    assert.Equal(t, int32(0), total)
    assert.EqualError(t, err, expectedError.Error())
}

func TestMgr_GetCustomerAccounts_ErrorDueToInternalStoreFailure(t *testing.T) {
    // Initialize the mock controller and defer its finish
    ctrl := gomock.NewController(t)
    defer ctrl.Finish()

    // Create a helper for test
    helper := newHelperForTest(t)
    mgr := helper.Mgr

    // Define the input parameters
    ctx := context.Background()
    page := int32(1)
    size := int32(10)
    guildId := uint32(123)
    customerUid := uint32(456)

    // Setup expected error from the mock store
    expectedErr := errors.New("internal store failure")

    helper.getCustomerAccountStore().EXPECT().
        GetCustomerAccounts(gomock.Any(), page, size, guildId, gomock.Any()).
        Return(nil, int32(0), expectedErr)

    // Call the function
    customerAccounts, total, err := mgr.GetCustomerAccounts(ctx, page, size, guildId, []uint32{customerUid})

    // Assertions
    assert.Nil(t, customerAccounts)
    assert.Equal(t, int32(0), total)
    assert.NotNil(t, err)
    assert.Contains(t, err.Error(), "获取客服账号列表失败")
    assert.Contains(t, err.Error(), expectedErr.Error())
}

func TestMgr_GetCustomerAccounts_InvalidPageSize(t *testing.T) {
    helper := newHelperForTest(t)

    ctx := context.Background()
    page := int32(1)
    size := int32(-1) // Invalid page size
    guildId := uint32(123)
    customerUid := uint32(456)

    expectedErr := errors.New("invalid page size")

    helper.getCustomerAccountStore().EXPECT().
        GetCustomerAccounts(ctx, page, size, guildId, gomock.Any()).
        Return(nil, int32(0), expectedErr)

    customerAccounts, total, err := helper.Mgr.GetCustomerAccounts(ctx, page, size, guildId, []uint32{customerUid})

    assert.Nil(t, customerAccounts)
    assert.Equal(t, int32(0), total)
    assert.EqualError(t, err, "获取客服账号列表失败: invalid page size")
}

func TestMgr_GetCustomerAccounts_InvalidPageNumber(t *testing.T) {
    // Setup the gomock controller and finish it after the test
    ctrl := gomock.NewController(t)
    defer ctrl.Finish()

    // Create an instance of helperForTest
    helper := newHelperForTest(t)

    // Define the context
    ctx := context.Background()

    // Mock the behavior of the customerAccountStore to return an error for invalid page number
    expectedError := errors.New("invalid page number")
    helper.getCustomerAccountStore().EXPECT().GetCustomerAccounts(ctx, int32(-1), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, int32(0), expectedError)

    // Call the method under test
    customerAccounts, total, err := helper.GetCustomerAccounts(ctx, int32(-1), int32(10), uint32(1), []uint32{uint32(1)})

    // Assert the results
    assert.Nil(t, customerAccounts)
    assert.Equal(t, int32(0), total)
    assert.NotNil(t, err)
    assert.Equal(t, "获取客服账号列表失败: invalid page number", err.Error())
}

func TestMgr_GetCustomerAccounts_Success(t *testing.T) {
    ctrl := gomock.NewController(t)
    defer ctrl.Finish()

    // Create a helper for the test
    helper := newHelperForTest(t)

    // Mock data
    ctx := context.Background()
    page := int32(1)
    size := int32(10)
    guildId := uint32(100)
    customerUid := uint32(200)

    expectedAccounts := []*CustomerAccount{
        {
            CustomerUID: 1,
            GuildID:     2,
            Creator:     "1",
            Password:    "123",
            Status:      1,
            CreateTime:  time.Now(),
            UpdateTime:  time.Now(),
        },
        {
            CustomerUID: 2,
            GuildID:     2,
            Creator:     "1",
            Password:    "123",
            Status:      1,
            CreateTime:  time.Now(),
            UpdateTime:  time.Now(),
        },
    }
    expectedTotal := int32(2)

    // Setup expectations
    helper.getCustomerAccountStore().EXPECT().GetCustomerAccounts(ctx, page, size, guildId, gomock.Any()).Return(expectedAccounts, expectedTotal, nil)

    // Call the method
    customerAccounts, total, err := helper.GetCustomerAccounts(ctx, page, size, guildId, []uint32{customerUid})

    // Assertions
    assert.NoError(t, err)
    assert.Equal(t, expectedAccounts, customerAccounts)
    assert.Equal(t, expectedTotal, total)
}

func TestBanCustomerAccount_Success(t *testing.T) {
    ctrl := gomock.NewController(t)
    defer ctrl.Finish()

    // Initialize the helper for test
    helper := newHelperForTest(t)

    ctx := context.Background()
    customerUid := uint32(12345)

    // Set up expectations
    helper.getCustomerAccountStore().EXPECT().SetCustomerAccountStatus(ctx, customerUid, gomock.Any()).Return(nil)
    helper.getHostingStatusStore().EXPECT().ClearHostingStatusByCustomerUid(ctx, customerUid).Return(nil)
    helper.getHostingStatusStore().EXPECT().GetHostingStatusByCustomerUid(gomock.Any(), gomock.Any()).Return(nil, nil)

    // Call the function to test
    success, err := helper.Mgr.SetCustomerAccountStatus(ctx, customerUid, CUSTOMER_ACCOUNT_STATUS_BANNED)

    // Assertions
    assert.NoError(t, err)
    assert.True(t, success)
}

func TestBanCustomerAccount_Error(t *testing.T) {
    // Create a new controller for gomock
    ctrl := gomock.NewController(t)
    defer ctrl.Finish()

    // Create a new helper for test
    h := newHelperForTest(t)

    // Create a new context
    ctx := context.Background()

    // Define the inputs
    customerUid := uint32(12345)

    // Create the expected error
    expectedErr := errors.New("some error occurred")

    // Set up the expectation
    h.getCustomerAccountStore().EXPECT().SetCustomerAccountStatus(ctx, customerUid, gomock.Any()).Return(expectedErr)

    // Call the method under test
    success, err := h.SetCustomerAccountStatus(ctx, customerUid, CUSTOMER_ACCOUNT_STATUS_UNREG)

    // Assert the results
    assert.False(t, success)
    assert.EqualError(t, err, "封禁客服账号失败: some error occurred")
}

func TestBanCustomerAccount_Clear_Error(t *testing.T) {
    // Create a new controller for gomock
    ctrl := gomock.NewController(t)
    defer ctrl.Finish()

    // Create a new helper for test
    h := newHelperForTest(t)

    // Create a new context
    ctx := context.Background()

    // Define the inputs
    customerUid := uint32(12345)

    // Create the expected error
    expectedErr := errors.New("some error occurred")

    // Set up the expectation
    h.getCustomerAccountStore().EXPECT().SetCustomerAccountStatus(ctx, customerUid, gomock.Any()).Return(nil)
    h.getHostingStatusStore().EXPECT().GetHostingStatusByCustomerUid(gomock.Any(), gomock.Any()).Return(nil, nil)
    h.getHostingStatusStore().EXPECT().ClearHostingStatusByCustomerUid(ctx, customerUid).Return(expectedErr)

    // Call the method under test
    success, err := h.SetCustomerAccountStatus(ctx, customerUid, CUSTOMER_ACCOUNT_STATUS_BANNED)

    // Assert the results
    assert.False(t, success)
    assert.EqualError(t, err, "清除托管大神失败: some error occurred")
}

func Test_GetCustomerAccountDetails_AccountNotFound(t *testing.T) {
    // Create a new mock controller for managing mock object lifecycle
    ctrl := gomock.NewController(t)
    defer ctrl.Finish()

    // Create a helper which initializes the Mgr struct with mock dependencies
    helper := newHelperForTest(t)

    // Define the context and customer UID for the test
    ctx := context.Background()
    customerUid := uint32(12345)

    // Define the expected error when account is not found
    expectedErr := errors.New("account not found")

    // Set up the expectation on the mock CustomerAccountStore
    helper.getCustomerAccountStore().EXPECT().GetCustomerAccountDetails(ctx, customerUid).Return(nil, expectedErr)

    // Call the method under test
    account, err := helper.GetCustomerAccountDetails(ctx, customerUid)

    // Assert the results
    assert.Nil(t, account)
    assert.NotNil(t, err)
}

func Test_GetCustomerAccountDetails_StoreError(t *testing.T) {
    // Initialize the test helper
    helper := newHelperForTest(t)

    // Define the test context and customer UID
    ctx := context.Background()
    customerUid := uint32(12345)

    // Expected error from the store
    expectedErr := fmt.Errorf("store error")

    // Set up the mock to return the expected error
    helper.getCustomerAccountStore().EXPECT().
        GetCustomerAccountDetails(ctx, customerUid).
        Return(nil, expectedErr)

    // Call the method under test
    actualCustomerAccount, actualErr := helper.GetCustomerAccountDetails(ctx, customerUid)

    // Validate the results
    assert.Nil(t, actualCustomerAccount)
    assert.NotNil(t, actualErr)
    assert.EqualError(t, actualErr, fmt.Sprintf("获取客服账号详细信息失败: %v", expectedErr))
}

func Test_GetCustomerAccountDetails_Success(t *testing.T) {
    // Arrange
    ctrl := gomock.NewController(t)
    defer ctrl.Finish()

    // Create a helper for the test
    helper := newHelperForTest(t)

    // Context and test data
    ctx := context.Background()
    customerUid := uint32(1)
    expectedCustomerAccount := &CustomerAccount{
        // Populate with expected details
    }

    // Setup expectations for the mocks
    helper.getCustomerAccountStore().EXPECT().
        GetCustomerAccountDetails(ctx, customerUid).
        Return(expectedCustomerAccount, nil)

    // Act
    actualCustomerAccount, err := helper.Mgr.GetCustomerAccountDetails(ctx, customerUid)

    // Assert
    assert.NoError(t, err)
    assert.Equal(t, expectedCustomerAccount, actualCustomerAccount)
}
