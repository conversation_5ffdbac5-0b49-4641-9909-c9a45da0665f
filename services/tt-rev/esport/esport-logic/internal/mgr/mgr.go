package mgr

import (
	"context"
	"encoding/hex"
	"fmt"
	"golang.52tt.com/clients/account"
	censoringProxy "golang.52tt.com/clients/censoring-proxy"
	game_card "golang.52tt.com/clients/game-card"
	"golang.52tt.com/clients/guild"
	headdynamicimage "golang.52tt.com/clients/head-dynamic-image-logic"
	headImageCli "golang.52tt.com/clients/headimage"
	iop_proxy "golang.52tt.com/clients/iop-proxy"
	presenceV2 "golang.52tt.com/clients/presence/v2"
	risk_mng_api "golang.52tt.com/clients/risk-mng-api"
	topic_channel "golang.52tt.com/clients/topic-channel/channel"
	"golang.52tt.com/clients/topic-channel/tab"
	userBlackList "golang.52tt.com/clients/user-black-list"
	user_online "golang.52tt.com/clients/user-online"
	userprofileapi "golang.52tt.com/clients/user-profile-api"
	auditTypes "golang.52tt.com/pkg/audit"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	protogrpc "golang.52tt.com/pkg/protocol/grpc"
	"golang.52tt.com/protocol/app"
	"golang.52tt.com/protocol/app/esport_logic"
	errCode "golang.52tt.com/protocol/common/status"
	v2 "golang.52tt.com/protocol/services/cybros/arbiter/v2"
	esport_customer "golang.52tt.com/protocol/services/esport-customer"
	esportSkill "golang.52tt.com/protocol/services/esport-skill"
	esport_statistics "golang.52tt.com/protocol/services/esport-statistics"
	esport_trade "golang.52tt.com/protocol/services/esport-trade"
	"golang.52tt.com/protocol/services/esport_grab_order"
	"golang.52tt.com/protocol/services/esport_hall"
	"golang.52tt.com/protocol/services/esport_internal"
	"golang.52tt.com/protocol/services/esport_rcmd"
	"golang.52tt.com/protocol/services/esport_role"
	guild_cooperation "golang.52tt.com/protocol/services/guild-cooperation"
	pb "golang.52tt.com/protocol/services/risk-mng-api"
	"golang.52tt.com/services/tt-rev/esport/esport-logic/internal/conf"
)

var _ Mgr = (*MgrImpl)(nil)

//go:generate mockgen -destination=mgr_mock.go -package=mgr golang.52tt.com/services/tt-rev/esport/esport-logic/internal/mgr Mgr
type Mgr interface {
	//CheckBeforePay 支付前检查
	CheckBeforePay(ctx context.Context, opUid, coachUid, gameId uint32) error
	// CheckOrderRemark 检查订单备注
	CheckOrderRemark(ctx context.Context, serviceInfo *protogrpc.ServiceInfo, uid uint32, remark string) error
	// CheckSwitch 检查开关
	CheckSwitch(ctx context.Context, uid, otherUid uint32, needMainSwitch, needHomePageSwitch bool) bool
	// CheckIsShowCustomerEntOnOrderByCoachUids 通过大神的uid，判断订单是否需要展示客服入口
	CheckIsShowCustomerEntOnOrderByCoachUids(ctx context.Context, coachUids []uint32) (map[uint32]bool, error)
	// CanUserVisitOrderDetail 判断用户是否可以查询订单的详情
	CanUserVisitOrderDetail(ctx context.Context, uid uint32, orderDetail *esport_trade.SkillProductOrderDetail) error
	// GetUserGameCardOnUGC 获取用户在UGC房间的游戏资料卡
	GetUserGameCardOnUGC(ctx context.Context, uid uint32, cid uint32) (*UserGameCardOnUgc, error)
	// IsUserMatchCrowGroup 判断用户是否匹配人群包
	IsUserMatchCrowGroup(ctx context.Context, uid uint32, crowdGroupId string) (bool, error)
	// AssembleRecommendList 组装推荐列表
	AssembleRecommendList(ctx context.Context, coachList []*esport_logic.EsportAreaCoachInfo, gameId uint32, currentUid uint32) ([]*esport_logic.EsportAreaCoachInfo, error)
	// GetUGCRoomGameId 获取UGC房间的游戏ID
	GetUGCRoomGameId(ctx context.Context, cid uint32) (uint32, error)
	// CheckUserCanSendCouponPackageByRiskSys 通过风控系统检测用户能否发放优惠券礼包
	CheckUserCanSendCouponPackageByRiskSys(ctx context.Context, baseReq *app.BaseReq, amount, guildId uint32, orderId, sourceType string) (bool, error)
	// CheckUserCanOrderByRiskSys 通过风控系统检查用户是否能下单消费
	CheckUserCanOrderByRiskSys(ctx context.Context, baseReq *app.BaseReq, coachUid uint32, amount uint64) (bool, *pb.CheckResp, error)
	// AsyncReportByLinkOrderFinish 上报订单完成给百灵
	AsyncReportByLinkOrderFinish(ctx context.Context, orderId string, source int32)
	// AsyncReportByLinkIssueCoupon 上报发放优惠券给百灵
	AsyncReportByLinkIssueCoupon(oldCtx context.Context, uid uint32, couponIds []string, orderId string)
	// AsyncReportByLinkUseCoupon 上报使用优惠券给百灵
	AsyncReportByLinkUseCoupon(ctx context.Context, uid uint32, coachId uint32, orderId string, couponId string)
	// GetSkillProductList 获取技能列表
	GetSkillProductList(ctx context.Context, uid, playerUid uint32) ([]*esport_logic.SkillProduct, error)
	// GetRcmdSkillProduct 获取推荐技能列表
	GetRcmdSkillProduct(ctx context.Context, uid uint32, req *esport_rcmd.GetEsportRcmdSkillProductReq, extraCoachUids ...uint32) ([]*esport_logic.EsportAreaCoachInfo, uint32, error)
	// CheckUserFirstRoundByRiskSys 通过风控系统检查用户是否能使用首单下单消费
	CheckUserFirstRoundByRiskSys(ctx context.Context, baseReq *app.BaseReq, uid, coachUid, totalPrice uint32) (bool, error)
}

func NewMgr(
	eSportSkillService esportSkill.EsportSkillClient,
	eSportRoleCli esport_role.ESportRoleClient,
	userBlacklistCli userBlackList.IClient,
	censoringProxyCli censoringProxy.IClient,
	userProfileCli userprofileapi.IClient,
	customerCli esport_customer.CustomerServiceClient,
	topicChannelCli topic_channel.IClient,
	tabCli tab.IClient,
	gameCardCli game_card.IClient,
	bc conf.IBusinessConfManager,
	iopProxy iop_proxy.IClient,
	eSportHallService esport_hall.EsportHallClient,
	headDynamicImageCli headdynamicimage.IClient,
	headImageCli headImageCli.IClient,
	orderService esport_trade.EsportTradeClient,
	eSportStatCli esport_statistics.EsportStatisticsClient,
	presenceV2Cli presenceV2.IClient,
	guildCli guild.IClient,
	riskMngApiCli risk_mng_api.IClient,
	userOnlineCli user_online.IClient,
	guildCopeCli guild_cooperation.GuildCooperationClient,
	accountCli account.IClient,
	esportInternalCli esport_internal.EsportInternalClient,
	esportRcmdCli esport_rcmd.EsportRcmdServiceClient,
	esportGrabOrderCli esport_grab_order.EsportGrabOrderClient,

) *MgrImpl {
	return &MgrImpl{
		eSportSkillService:  eSportSkillService,
		eSportRoleCli:       eSportRoleCli,
		userBlacklistCli:    userBlacklistCli,
		censoringProxyCli:   censoringProxyCli,
		userProfileCli:      userProfileCli,
		customerCli:         customerCli,
		topicChannelCli:     topicChannelCli,
		tabCli:              tabCli,
		gameCardCli:         gameCardCli,
		bc:                  bc,
		iopProxy:            iopProxy,
		eSportHallService:   eSportHallService,
		headDynamicImageCli: headDynamicImageCli,
		headImageCli:        headImageCli,
		orderService:        orderService,
		eSportStatCli:       eSportStatCli,
		presenceV2Cli:       presenceV2Cli,
		guildCli:            guildCli,
		riskMngApiCli:       riskMngApiCli,
		userOnlineCli:       userOnlineCli,
		guildCopeCli:        guildCopeCli,
		accountCli:          accountCli,
		esportInternalCli:   esportInternalCli,
		esportRcmdCli:       esportRcmdCli,
		esportGrabOrderCli:  esportGrabOrderCli,
	}
}

type MgrImpl struct {
	eSportSkillService  esportSkill.EsportSkillClient
	eSportRoleCli       esport_role.ESportRoleClient
	userBlacklistCli    userBlackList.IClient
	censoringProxyCli   censoringProxy.IClient
	userProfileCli      userprofileapi.IClient
	customerCli         esport_customer.CustomerServiceClient
	topicChannelCli     topic_channel.IClient
	tabCli              tab.IClient
	gameCardCli         game_card.IClient
	bc                  conf.IBusinessConfManager
	iopProxy            iop_proxy.IClient
	eSportHallService   esport_hall.EsportHallClient
	headDynamicImageCli headdynamicimage.IClient
	headImageCli        headImageCli.IClient
	orderService        esport_trade.EsportTradeClient
	eSportStatCli       esport_statistics.EsportStatisticsClient
	presenceV2Cli       presenceV2.IClient
	riskMngApiCli       risk_mng_api.IClient
	guildCli            guild.IClient
	userOnlineCli       user_online.IClient
	guildCopeCli        guild_cooperation.GuildCooperationClient
	accountCli          account.IClient
	esportInternalCli   esport_internal.EsportInternalClient
	esportRcmdCli       esport_rcmd.EsportRcmdServiceClient
	esportGrabOrderCli  esport_grab_order.EsportGrabOrderClient
}

func (m *MgrImpl) IsUserMatchCrowGroup(ctx context.Context, uid uint32, crowdGroupId string) (bool, error) {

	// 调用 iop-proxy 获取人群包信息
	crowdResults, err := m.iopProxy.MatchGroup(ctx, []uint64{uint64(uid)}, []string{crowdGroupId})
	if err != nil {
		return false, fmt.Errorf("fail to MatchGroup. err:%w", err)
	}
	log.DebugWithCtx(ctx, "IsUserMatchCrowGroup MatchGroup result: %+v, uid: %d", crowdResults, uid)
	if len(crowdResults) > 0 && crowdResults[0].GetResult()[crowdGroupId] == "1" {
		return true, nil
	}
	return false, nil
}

func (m *MgrImpl) CanUserVisitOrderDetail(ctx context.Context, uid uint32, orderDetail *esport_trade.SkillProductOrderDetail) error {
	coachUid := orderDetail.GetProductOrder().GetCoachUid()
	playerUid := orderDetail.GetPlayerUid()
	if uid == coachUid {
		if orderDetail.GetCoachDel() {
			return protocol.NewExactServerError(nil, errCode.ErrEsportsOrderIsDeleted)
		}
		return nil
	}
	if uid == playerUid {
		// 如果是用户，可以查询
		if orderDetail.GetPlayerDel() {
			return protocol.NewExactServerError(nil, errCode.ErrEsportsOrderIsDeleted)
		}
		return nil
	}
	// 判断是否是客服
	customerAccountDetails, err := m.customerCli.GetCustomerAccountDetails(ctx, &esport_customer.GetCustomerAccountDetailsRequest{
		CustomerUid: uid,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "CanUserVisitOrderDetail fail to GetCustomerAccountDetails. uid:%d, err:%v", uid, err)
		return protocol.NewExactServerError(nil, errCode.ErrRequestParamInvalid, "无权查看")
	}
	if customerAccountDetails.GetCustomerAccount().GetCustomerUid() == uid {
		// 如果是客服，可以查询
		return nil
	}

	return protocol.NewExactServerError(nil, errCode.ErrRequestParamInvalid, "无权查看")
}

// CheckIsShowCustomerEntOnOrderByCoachUids 通过大神的uid，判断订单是否需要展示客服入口
// 判断标准是：大神所在的公会存在客服就行
// 1. 批量获取大神所在的公会
func (m *MgrImpl) CheckIsShowCustomerEntOnOrderByCoachUids(ctx context.Context, coachUids []uint32) (map[uint32]bool, error) {

	// 1. 批量获取大神所在的公会
	coachUidGuildIdMap := make(map[uint32]uint32)
	if resp, err := m.eSportRoleCli.BatchGetGuildIdByCoachUids(ctx, &esport_role.BatchGetGuildIdByCoachUidsRequest{
		CoachUids: coachUids,
	}); err != nil {
		return map[uint32]bool{}, fmt.Errorf("fail to BatchGetGuildIdByCoachUids. err:%w", err)
	} else {
		coachUidGuildIdMap = resp.GetCoachUidGuildIdMap()
	}

	// 2. 抽取公会ID
	guildIds := make([]uint32, 0, len(coachUidGuildIdMap))
	for _, guildId := range coachUidGuildIdMap {
		guildIds = append(guildIds, guildId)
	}

	// 3. 批量判断公会是否有客服
	resp, err := m.customerCli.BatchCheckGuildHasCustomer(ctx, &esport_customer.BatchCheckGuildHasCustomerRequest{
		GuildIds: guildIds,
	})
	if err != nil {
		return map[uint32]bool{}, fmt.Errorf("fail to BatchCheckGuildHasCustomer. err:%w", err)
	}

	// 4. 组装结果，形成大神id -> 是否有客服的映射
	coachUidHasCustomerMap := make(map[uint32]bool)
	for _, coachUid := range coachUids {
		guildId := coachUidGuildIdMap[coachUid]
		coachUidHasCustomerMap[coachUid] = resp.GetGuildHasCustomer()[guildId]
	}
	return coachUidHasCustomerMap, nil
}

func (m *MgrImpl) CheckSwitch(ctx context.Context, uid, otherUid uint32, needMainSwitch, needHomePageSwitch bool) bool {
	if uid != 0 {
		switchResp, err := m.eSportSkillService.GetSwitch(ctx, &esportSkill.GetSwitchRequest{
			Uid: uid,
		})
		if err != nil {
			log.ErrorWithCtx(ctx, "GetSwitch fail. err:%v", err)
			return true
		}
		if needMainSwitch && switchResp.GetSwitchStatus().GetMainSwitchStatus() == esportSkill.EsportSwitchStatus_SWITCH_STATUS_OFF {
			return false
		}
		if needHomePageSwitch && switchResp.GetSwitchStatus().GetHomepageSwitchStatus() == esportSkill.EsportSwitchStatus_SWITCH_STATUS_OFF {
			return false
		}
	}

	if otherUid != 0 {
		switchResp, err := m.eSportSkillService.GetSwitch(ctx, &esportSkill.GetSwitchRequest{
			Uid: otherUid,
		})
		if err != nil {
			log.ErrorWithCtx(ctx, "GetSwitch fail. err:%v", err)
			return true
		}
		if needMainSwitch && switchResp.GetSwitchStatus().GetMainSwitchStatus() == esportSkill.EsportSwitchStatus_SWITCH_STATUS_OFF {
			return false
		}
		if needHomePageSwitch && switchResp.GetSwitchStatus().GetHomepageSwitchStatus() == esportSkill.EsportSwitchStatus_SWITCH_STATUS_OFF {
			return false
		}
	}

	return true
}

func (m *MgrImpl) CheckBeforePay(ctx context.Context, opUid, coachUid, gameId uint32) error {

	// 检查电竞陪玩身份
	roleResp, err := m.eSportRoleCli.GetUserESportRole(ctx, &esport_role.GetUserESportRoleReq{
		Uid: coachUid,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "checkBeforePay fail to GetUserESportRole. uid:%d, coachUid:%d, err:%v", opUid, coachUid, err)
		return err
	}

	if roleResp.GetEsportRole() == 0 {
		return protocol.NewExactServerError(nil, errCode.ErrEsportsOrderCannotPayCoachRoleReclaimed, "大神当前不可接单该游戏")
	}

	// 检查技能是否被冻结
	freezeRsp, err := m.eSportSkillService.GetUserSkillFreezeStatus(ctx, &esportSkill.GetUserSkillFreezeStatusRequest{Uid: coachUid})
	if err != nil {
		log.ErrorWithCtx(ctx, "checkBeforePay fail to GetUserSkillFreezeStatus. uid:%d, coachUid:%d, err:%v", opUid, coachUid, err)
		return err
	}
	freezeMap := freezeRsp.GetGameStatusMap()
	if freezeMap[gameId] != nil {
		return protocol.NewExactServerError(nil, errCode.ErrEsportSkillFreeze, "大神技能已冻结，不可下单")
	}

	resp, err := m.userBlacklistCli.CheckIsInBlackList(ctx, opUid, coachUid)
	if err != nil {
		log.ErrorWithCtx(ctx, "checkBeforePay fail to CheckIsInBlackList. uid:%d, coachUid:%d, err:%v", opUid, coachUid, err)
	}

	if resp.GetBIn() {
		return protocol.NewExactServerError(nil, errCode.ErrEsportsOrderCannotPay, "您已拉黑对方，不可下单")
	}

	resp, err = m.userBlacklistCli.CheckIsInBlackList(ctx, coachUid, opUid)
	if err != nil {
		log.ErrorWithCtx(ctx, "checkBeforePay fail to CheckIsInBlackList. uid:%d, coachUid:%d, err:%v", opUid, coachUid, err)
	}

	if resp.GetBIn() {
		return protocol.NewExactServerError(nil, errCode.ErrEsportsOrderCannotPay, "您已被对方拉黑，不可下单")
	}

	return nil
}

func (m *MgrImpl) CheckOrderRemark(ctx context.Context, serviceInfo *protogrpc.ServiceInfo, uid uint32, remark string) error {
	if remark == "" {
		return nil
	}

	userInfo, err := m.userProfileCli.GetUserProfileV2(ctx, uid, false)
	if err != nil {
		log.ErrorWithCtx(ctx, "checkOrderRemark failed to GetUserProfileV2. uid %d err %v", uid, err)
		return err
	}

	verifyRes, serr := m.censoringProxyCli.Text().SyncScanText(ctx, &v2.SyncTextCheckReq{
		Context: &v2.TaskContext{
			SceneCode: string(auditTypes.SCENE_CODE_ESPORT_ORDER_REMARK),
			AppId:     string(auditTypes.APP_ID_QUICKSILVER),
			Scenes:    []v2.Scene{v2.Scene_SCENE_DEFAULT},
			UserInfo: &v2.User{
				Id:       uint64(uid),
				Nickname: userInfo.GetNickname(),
				Alias:    userInfo.GetAccountAlias(),
			},
			DeviceInfo: &v2.Device{
				Id: hex.EncodeToString(serviceInfo.DeviceID),
				Ip: serviceInfo.ClientIPAddr().String(),
			},
		},
		Text:  remark,
		Async: false,
	})
	if serr != nil {
		log.ErrorWithCtx(ctx, "checkOrderRemark fail to SyncScanText. uid:%d, err:%+v", uid, serr)
		return serr
	}

	// 机审结果REJECT
	if v2.Suggestion_REJECT == v2.Suggestion(verifyRes.GetResult()) {
		log.ErrorWithCtx(ctx, "checkOrderRemark SyncScanText REJECT. content:%s uid:%d", remark, uid)
		return protocol.NewExactServerError(nil, errCode.ErrEsportsOrderPayRemarkReviewReject)
	}

	return nil
}

func buildPriceInfo(price, maxOrderCnt uint32, unitType esportSkill.GAME_PRICING_UNIT_TYPE,
	firstRoundInfo *esport_hall.FirstRoundLabel, couponPriceInfo *esport_internal.CouponPriceResult) *esport_logic.PriceInfo {
	priceInfo := &esport_logic.PriceInfo{
		Price:                 price,
		PriceUnit:             "豆",
		MeasureCnt:            30,
		MeasureUnit:           "分钟",
		MaxOrderCnt:           maxOrderCnt,
		HasFirstRoundDiscount: firstRoundInfo.GetHasFirstRoundDiscount(),
		FirstRoundPrice:       firstRoundInfo.GetFirstRoundPrice(),
	}
	if unitType == esportSkill.GAME_PRICING_UNIT_TYPE_GAME_PRICING_UNIT_TYPE_PER_GAME {
		priceInfo.MeasureCnt = 1
		priceInfo.MeasureUnit = "局"
	}

	// 填充新的通用优惠信息
	if couponPriceInfo.GetHasPriceWithCoupon() {
		priceInfo.HasDiscount = true
		priceInfo.DiscountPrice = couponPriceInfo.GetPriceWithCoupon()
		priceInfo.DiscountType = uint32(esport_logic.DiscountType_DISCOUNT_TYPE_COUPON)
		priceInfo.DiscountDesc = "券后价"
	}
	if firstRoundInfo.GetHasFirstRoundDiscount() {
		priceInfo.HasDiscount = true
		priceInfo.DiscountPrice = firstRoundInfo.GetFirstRoundPrice()
		priceInfo.DiscountType = uint32(esport_logic.DiscountType_DISCOUNT_TYPE_FIRST_ROUND)
		priceInfo.DiscountDesc = "首局价"
	}
	return priceInfo
}
