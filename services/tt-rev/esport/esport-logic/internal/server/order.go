package server

import (
	"context"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"golang.52tt.com/protocol/services/esport_grab_order"
	"golang.52tt.com/protocol/services/esport_internal"
	"golang.52tt.com/services/tt-rev/common/financial_security"
	"sort"
	"strings"
	"time"

	"golang.52tt.com/services/tt-rev/common/goroutineex"
	"golang.52tt.com/services/tt-rev/esport/common"

	auditTypes "golang.52tt.com/pkg/audit"
	v2 "golang.52tt.com/protocol/services/cybros/arbiter/v2"

	"golang.52tt.com/protocol/services/esport_role"
	riskMngApiPb "golang.52tt.com/protocol/services/risk-mng-api"
	"google.golang.org/grpc/codes"

	"gitlab.ttyuyin.com/tt-infra/tyr/log"
	"golang.52tt.com/pkg/protocol"
	protogrpc "golang.52tt.com/pkg/protocol/grpc"
	"golang.52tt.com/protocol/app"
	"golang.52tt.com/protocol/app/esport_logic"
	errCode "golang.52tt.com/protocol/common/status"
	skillpb "golang.52tt.com/protocol/services/esport-skill"
	esport_trade "golang.52tt.com/protocol/services/esport-trade"
	esport_trade_appeal "golang.52tt.com/protocol/services/esport-trade-appeal"
	"golang.52tt.com/protocol/services/esport_hall"
)

// PlayerPayOrder 玩家下单
func (s *Server) PlayerPayOrder(ctx context.Context, in *esport_logic.PlayerPayOrderRequest) (*esport_logic.PlayerPayOrderResponse, error) {
	out, err := s.playerPayOrder(ctx, in, false)
	log.InfoWithCtx(ctx, "PlayerPayOrder in:%+v out:%+v err:%v", in, out, err)
	return out, err
}

// PlayerPayOrderPreCheck 玩家下单前置检查
func (s *Server) PlayerPayOrderPreCheck(ctx context.Context, in *esport_logic.PlayerPayOrderRequest) (*esport_logic.PlayerPayOrderResponse, error) {
	out, err := s.playerPayOrder(ctx, in, true)
	log.InfoWithCtx(ctx, "PlayerPayOrder PreCheck in:%+v out:%+v err:%v", in, out, err)
	return out, err
}

func (s *Server) playerPayOrder(ctx context.Context, in *esport_logic.PlayerPayOrderRequest, isPreCheck bool) (*esport_logic.PlayerPayOrderResponse, error) {
	out := &esport_logic.PlayerPayOrderResponse{}

	svrInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
	if !ok {
		log.ErrorWithCtx(ctx, "PlayerPayOrder ServiceInfoFromContext fail. in:%+v", in)
		return out, protocol.NewExactServerError(nil, errCode.ErrRequestParamInvalid)
	}
	opUid := svrInfo.UserID

	if in.GetCount() == 0 || in.GetCount() > 10000 || in.GetTotalPrice() == 0 {
		log.ErrorWithCtx(ctx, "PlayerPayOrder fail. in:%+v", in)
		return out, protocol.NewExactServerError(nil, errCode.ErrRequestParamInvalid)
	}

	// 获取商品信息
	productResp, err := s.eSportHallService.GetSkillProductInfo(ctx, &esport_hall.GetSkillProductInfoRequest{
		ProductId: uint32(in.GetProductId()),
		InviteId:  in.GetInviteId(),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "PlayerPayOrder fail to GetSkillProductInfo. in:%+v, err:%v", in, err)
		return out, err
	}
	product := productResp.GetProduct()

	// 内部账号下单检测
	err = financial_security.CheckInternalFinancialSecurity(ctx, opUid, product.GetUid())
	if err != nil {
		log.ErrorWithCtx(ctx, "PlayerPayOrder fail to CheckInternalFinancialSecurity. in:%+v, err:%v", in, err)
		return out, err
	}

	discountInfo := &esport_logic.OrderDiscountInfo{}
	if len(in.GetDiscountInfo()) > 0 {
		err = json.Unmarshal([]byte(in.GetDiscountInfo()), discountInfo)
		if err != nil {
			log.ErrorWithCtx(ctx, "fail to Unmarshal DiscountInfo:%s, err:%v", in.GetDiscountInfo(), err)
			return out, err
		}
	}

	// 检查优惠是否叠加
	discountCount := 0
	if discountInfo.UseFirstRoundDiscount {
		discountCount++
	}
	if discountInfo.UseNewCustomerDiscount {
		discountCount++
	}
	if len(discountInfo.CouponIds) > 0 {
		discountCount++
	}
	if discountCount > 1 {
		return out, protocol.NewExactServerError(nil, errCode.ErrRequestParamInvalid, "不能同时使用多种优惠")
	}

	// 检查是否能享受优惠
	newCustomerPrice := &esport_hall.NewCustomerPriceInfo{}
	firstRoundPrice := uint32(0)
	couponMoney := uint32(0)
	if discountInfo.GetUseNewCustomerDiscount() {
		var newCustomerRsp *esport_hall.GetNewCustomerPriceBySkillResponse
		newCustomerRsp, err = s.eSportHallService.GetNewCustomerPriceBySkill(ctx, &esport_hall.GetNewCustomerPriceBySkillRequest{
			PlayerUid: opUid,
			CoachUid:  product.Uid,
		})
		if err != nil {
			log.ErrorWithCtx(ctx, "PlayerPayOrder fail to GetNewCustomerPriceBySkill. in:%+v, product:%+v, err:%v", in, product, err)
			return out, err
		}
		newCustomerPrice = newCustomerRsp.GetPriceMap()[product.GetGameId()]
		if newCustomerPrice == nil || !newCustomerPrice.GetHasNewCustomerDiscount() {
			log.ErrorWithCtx(ctx, "PlayerPayOrder do not has newCustomerPrice. in:%+v, product:%+v", in, product)
			return out, protocol.NewExactServerError(nil, errCode.ErrEsportsOrderCannotPay, "新客优惠已更新，请确认后支付")
		}
		if in.GetCount() > 1 {
			return out, protocol.NewExactServerError(nil, errCode.ErrRequestParamInvalid, "新客价只能下1局")
		}
	}
	if discountInfo.GetUseFirstRoundDiscount() {
		var rightRsp *esport_hall.CheckFirstRoundOrderRightResponse
		rightRsp, err = s.eSportHallService.CheckFirstRoundOrderRight(ctx, &esport_hall.CheckFirstRoundOrderRightRequest{
			PlayerUid:     opUid,
			CoachUid:      product.Uid,
			GameId:        product.GameId,
			CheckDayLimit: true,
		})
		if err != nil {
			log.ErrorWithCtx(ctx, "PlayerPayOrder fail to CheckFirstRoundOrderRight. in:%+v, product:%+v, err:%v", in, product, err)
			return out, err
		}
		pass, err := s.mgr.CheckUserFirstRoundByRiskSys(ctx, in.GetBaseReq(), opUid, product.Uid, in.TotalPrice)
		if err != nil {
			log.ErrorWithCtx(ctx, "PlayerPayOrder fail to CheckUserFirstRoundByRiskSys. in:%+v, product:%+v, err:%v", in, product, err)
			return out, err
		}
		if !pass {
			log.ErrorWithCtx(ctx, "PlayerPayOrder CheckUserFirstRoundByRiskSys not pass. in:%+v, product:%+v", in, product)
			return out, protocol.NewExactServerError(nil, errCode.ErrEsportsOrderCannotPay, "今日无法继续享受首局优惠啦，明天再来吧~")
		}
		firstRoundPrice = rightRsp.GetFirstRoundPrice()
	}
	if len(discountInfo.CouponIds) > 0 {
		var classifyRsp *esport_internal.BatClassifyCouponResponse
		classifyRsp, err = s.esportInternalCli.BatClassifyCoupon(ctx, &esport_internal.BatClassifyCouponRequest{
			PlayerUid:     opUid,
			CoachUid:      product.Uid,
			GameId:        product.GameId,
			OriginalPrice: product.Price,
			MinBuyAmount:  in.Count,
			MaxBuyAmount:  in.Count,
			QueryOption:   nil,
		})
		if err != nil {
			log.ErrorWithCtx(ctx, "PlayerPayOrder fail to BatClassifyCoupon. in:%+v, product:%+v, err:%v", in, product, err)
			return out, protocol.NewExactServerError(nil, errCode.ErrEsportsOrderCannotPay, "优惠券已更新，请确认后支付")
		}

		chosenCoupon := getChosenCoupon(classifyRsp.GetResultMap()[in.Count], discountInfo.CouponIds[0])
		if chosenCoupon == nil {
			log.ErrorWithCtx(ctx, "PlayerPayOrder can not getChosenCoupon. in:%+v, product:%+v", in, product)
			return out, protocol.NewExactServerError(nil, errCode.ErrEsportsOrderCannotPay, "优惠券已更新，请确认后支付")
		}
		couponMoney = chosenCoupon.GetReducePrice()
	}

	// 检查订单总价是否正确
	{
		count := uint64(in.GetCount())
		unitPrice := uint64(product.GetPrice())
		total := uint64(in.GetTotalPrice())
		if discountInfo.GetUseNewCustomerDiscount() {
			if uint64(newCustomerPrice.GetNewCustomerPrice()) != total {
				return out, protocol.NewExactServerError(nil, errCode.ErrEsportsOrderCannotPay, "价格已更新，请确认后支付")
			}
		} else if discountInfo.GetUseFirstRoundDiscount() {
			if unitPrice*(count-1)+uint64(firstRoundPrice) != total {
				return out, protocol.NewExactServerError(nil, errCode.ErrEsportsOrderCannotPay, "价格已更新，请确认后支付")
			}
		} else if len(discountInfo.CouponIds) > 0 {
			if unitPrice*count != total+uint64(couponMoney) {
				return out, protocol.NewExactServerError(nil, errCode.ErrEsportsOrderCannotPay, "价格已更新，请确认后支付")
			}
		} else {
			if unitPrice*count != total {
				return out, protocol.NewExactServerError(nil, errCode.ErrEsportsOrderCannotPay, "价格已更新，请确认后支付")
			}
		}
	}

	coachUid := product.GetUid()
	if coachUid == opUid {
		return out, protocol.NewExactServerError(nil, errCode.ErrEsportsOrderCannotPay, "不能给自己下单哦")
	}

	// 每单最大局数限制检查
	if in.GetCount() > productResp.GetProduct().GetMaxOrderCnt() {
		return out, protocol.NewExactServerError(nil, errCode.ErrEsportsOrderCannotPay, "已达到上限")
	}

	// 防刷单检查
	if _, err := s.orderService.CheckUserOrderCntLimit(ctx, &esport_trade.CheckUserOrderCntLimitRequest{
		Uid:      opUid,
		CoachUid: coachUid,
	}); err != nil {
		log.ErrorWithCtx(ctx, "PlayerPayOrder fail to CheckUserOrderCntLimit. in:%+v, product:%+v, err:%v", in, product, err)
		return out, err
	}

	// 判断开关, 任意一方非白名单都不可下单
	if !s.mgr.CheckSwitch(ctx, opUid, coachUid, true, false) {
		log.ErrorWithCtx(ctx, "PlayerPayOrder fail to CheckSwitch. in:%+v, product:%+v, err:%v", in, product, err)
		return out, protocol.NewExactServerError(codes.OK, errCode.ErrEsportHallProductNotFound)
	}

	// 支付检查
	err = s.mgr.CheckBeforePay(ctx, opUid, coachUid, product.GetGameId())
	if err != nil {
		log.ErrorWithCtx(ctx, "PlayerPayOrder fail to checkBeforePay. in:%+v, product:%+v, err:%v", in, product, err)
		return out, err
	}

	// 订单备注检查
	err = s.mgr.CheckOrderRemark(ctx, svrInfo, opUid, in.GetOrderRemark())
	if err != nil {
		log.ErrorWithCtx(ctx, "PlayerPayOrder fail to checkOrderRemark. in:%+v, product:%+v, err:%v", in, product, err)
		return out, err
	}

	if isPreCheck { // 预检查就到此为止了
		return out, nil
	}

	// 消费风控检查
	can, checkErr, err := s.mgr.CheckUserCanOrderByRiskSys(ctx, in.GetBaseReq(), product.GetUid(), uint64(in.GetTotalPrice()))
	if err != nil {
		log.ErrorWithCtx(ctx, "PlayerPayOrder fail to CheckUserCanOrderByRiskSys. in:%+v, product:%+v, err:%v", in, product, err)
		return out, protocol.NewExactServerError(nil, errCode.ErrEsportsOrderCannotPay, "当前交易存在风险")
	}
	if !can {
		log.ErrorWithCtx(ctx, "PlayerPayOrder CheckUserCanOrderByRiskSys not pass. in:%+v, product:%+v, result:%v", in, product, can)
		out.BaseResp = &app.BaseResp{ErrInfo: checkErr.GetErrInfo()}
		return out, protocol.NewExactServerError(nil, int(checkErr.GetErrCode()), checkErr.GetErrMsg())
	}

	// 获取技能信息
	skillResp, err := s.eSportSkillService.GetGameDetailById(ctx, &skillpb.GetGameDetailByIdRequest{
		GameId: product.GetGameId(),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "PlayerPayOrder fail to GetGameDetailById. in:%+v, product:%+v, err:%v", in, product, err)
		return out, err
	}

	// 构造订单价格信息
	priceInfo := &esport_trade.PriceInfo{
		Price:                 product.GetPrice(),
		PriceUnit:             "豆",
		MeasureCnt:            30,
		MeasureUnit:           "分钟",
		HasFirstRoundDiscount: discountInfo.GetUseFirstRoundDiscount(),
		FirstRoundPrice:       firstRoundPrice,
	}
	if product.GetUintType() == uint32(skillpb.GAME_PRICING_UNIT_TYPE_GAME_PRICING_UNIT_TYPE_PER_GAME) {
		priceInfo.MeasureCnt = 1
		priceInfo.MeasureUnit = "局"
	}
	if discountInfo.GetUseNewCustomerDiscount() {
		priceInfo.HasDiscount = true
		priceInfo.DiscountPrice = newCustomerPrice.GetNewCustomerPrice()
		priceInfo.DiscountType = uint32(esport_logic.DiscountType_DISCOUNT_TYPE_NEW_CUSTOMER)
		priceInfo.DiscountDesc = "新客价"
	}
	if discountInfo.GetUseFirstRoundDiscount() {
		priceInfo.HasDiscount = true
		priceInfo.DiscountPrice = firstRoundPrice
		priceInfo.DiscountType = uint32(esport_logic.DiscountType_DISCOUNT_TYPE_FIRST_ROUND)
		priceInfo.DiscountDesc = "首局价"
	}
	if len(discountInfo.CouponIds) > 0 {
		priceInfo.HasDiscount = true
		priceInfo.DiscountPrice = product.GetPrice() - couponMoney
		priceInfo.DiscountType = uint32(esport_logic.DiscountType_DISCOUNT_TYPE_COUPON)
		priceInfo.DiscountDesc = "券后价"
	}

	// 构造大神总价
	coachTotalPrice := in.GetTotalPrice()
	if discountInfo.GetUseNewCustomerDiscount() {
		coachTotalPrice += newCustomerPrice.GetPlatBonusFee()
	}
	if len(discountInfo.CouponIds) > 0 {
		coachTotalPrice += couponMoney
	}

	// 下单
	tradeResp, err := s.orderService.PlayerPayOrder(ctx, &esport_trade.PlayerPayOrderRequest{
		Uid:     opUid,
		Remark:  in.GetOrderRemark(),
		Source:  in.GetSource(),
		PayType: in.GetPayType(),
		Order: &esport_trade.ProductOrder{
			ProductId:         in.GetProductId(),
			TotalPrice:        in.GetTotalPrice(),
			Count:             in.GetCount(),
			CoachUid:          coachUid,
			GameId:            skillResp.GetConfig().GetGameId(),
			Name:              skillResp.GetConfig().GetName(),
			Icon:              skillResp.GetConfig().GetGameIcon(),
			Tag:               product.GetTag(),
			PriceInfo:         priceInfo,
			IsGuaranteeWin:    product.GetIsGuaranteeWin(),
			GuaranteeWinTexts: product.GetGuaranteeWinTexts(),
			CoachTotalPrice:   coachTotalPrice,
			CouponUseDetail: &esport_trade.CouponUseDetail{
				UseCoupon:   len(discountInfo.CouponIds) > 0,
				CouponMoney: couponMoney,
				CouponIds:   discountInfo.GetCouponIds(),
			},
			NewCustomerUseDetail: &esport_trade.NewCustomerUseDetail{
				UseNewCustomerDiscount: newCustomerPrice.GetHasNewCustomerDiscount(),
				NewCustomerPrice:       newCustomerPrice.GetNewCustomerPrice(),
				PlatBonusFee:           newCustomerPrice.GetPlatBonusFee(),
			},
		},
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "PlayerPayOrder fail to PlayerPayOrder. in:%+v, product:%+v, err:%v", in, product, err)
		return out, err
	}

	out.Balance = tradeResp.GetBalance()

	if in.GetInviteId() > 0 {
		// 更新邀请下单状态
		_, _ = s.eSportHallService.HandleInviteOrder(ctx, &esport_hall.HandleInviteOrderRequest{
			InviteId: in.GetInviteId(),
			Status:   uint32(esport_logic.InviteOrderStatus_INVITE_ORDER_STATUS_PAYED),
		})
	}

	// 异步推送下单离线通知
	goroutineex.GoroutineWithTimeoutCtx(ctx, 5*time.Second, func(ctx context.Context) {
		up, err := s.userProfileCli.GetUserProfileV2(ctx, opUid, false)
		if err != nil {
			log.ErrorWithCtx(ctx, "PlayerPayOrder fail to GetUserProfileV2. in:%+v, product:%+v, err:%v", in, product, err)
			return
		}

		s.extPushCLi.WriteOfflineMsg(ctx, coachUid,
			"用户下单通知", fmt.Sprintf("%s给你下单了，快去接单吧~", up.GetNickname()), "")
		// fmt.Sprintf("tt://m.52tt.com/esport_order_detail?order_id=%s", tradeResp.GetOrderId())
	})

	goroutineex.GoroutineWithTimeoutCtx(ctx, 5*time.Second, func(ctx context.Context) {
		// 一键找人下单
		if in.GetSource() == uint32(esport_logic.PayOrderSource_PAY_ORDER_SOURCE_ONE_KEY_FIND_COACH) {
			_, err = s.esportGrabOrderCli.FinishChoseCoach(ctx, &esport_grab_order.FinishChoseCoachRequest{
				Uid:       opUid,
				TargetUid: coachUid,
			})
			if err != nil {
				log.ErrorWithCtx(ctx, "PlayerPayOrder fail to FinishChoseCoach. in:%+v, product:%+v, err:%v", in, product, err)
			}
		}
	})

	// 数分上报，如果使用了优惠券
	if len(discountInfo.CouponIds) > 0 {
		s.mgr.AsyncReportByLinkUseCoupon(ctx, opUid, coachUid, tradeResp.GetOrderId(), discountInfo.CouponIds[0])
	}

	// 检查完成订单是否能自动发优惠券
	checkRsp, err := s.orderService.AutoGrantCoupon(ctx, &esport_trade.AutoGrantCouponRequest{
		BaseReq:         in.BaseReq,
		Uid:             opUid,
		OrderId:         tradeResp.GetOrderId(),
		Source:          in.Source,
		PreCheck:        true,
		CoachUid:        coachUid,
		OrderTotalPrice: in.TotalPrice,
	})
	if err != nil {
		log.WarnWithCtx(ctx, "PlayerPayOrder fail to AutoGrantCoupon. in:%+v, product:%+v, err:%v", in, product, err)
	} else {
		if checkRsp.CanGrant {
			// 构造用户提示文案
			hintRsp, err := s.orderService.GetOrderHint(ctx, &esport_trade.GetOrderHintRequest{
				Uid:       opUid,
				Scene:     esport_trade.GetOrderHintRequest_SCENE_PAY_ORDER_PLAYER_IM,
				OrderType: uint32(esport_logic.DiscountType_DISCOUNT_TYPE_COUPON),
			})
			if err != nil {
				log.WarnWithCtx(ctx, "PlayerPayOrder fail to GetOrderHint. in:%+v, product:%+v, err:%v", in, product, err)
			} else {
				out.HintTitle = hintRsp.GetHintTitle()
				out.HintContent = hintRsp.GetHintContent()
			}
		}
	}

	log.InfoWithCtx(ctx, "PlayerPayOrder success. in:%+v, product:%+v, out:%+v", in, product, out)
	return out, nil
}

func (s *Server) checkOrderRemark(ctx context.Context, serviceInfo *protogrpc.ServiceInfo, uid uint32, remark string) error {
	if remark == "" {
		return nil
	}

	userInfo, err := s.userProfileCli.GetUserProfileV2(ctx, uid, false)
	if err != nil {
		log.ErrorWithCtx(ctx, "checkOrderRemark failed to GetUserProfileV2. uid %d err %v", uid, err)
		return err
	}

	verifyRes, serr := s.censoringProxyCli.Text().SyncScanText(ctx, &v2.SyncTextCheckReq{
		Context: &v2.TaskContext{
			SceneCode: string(auditTypes.SCENE_CODE_ESPORT_ORDER_REMARK),
			AppId:     string(auditTypes.APP_ID_QUICKSILVER),
			Scenes:    []v2.Scene{v2.Scene_SCENE_DEFAULT},
			UserInfo: &v2.User{
				Id:       uint64(uid),
				Nickname: userInfo.GetNickname(),
				Alias:    userInfo.GetAccountAlias(),
			},
			DeviceInfo: &v2.Device{
				Id: hex.EncodeToString(serviceInfo.DeviceID),
				Ip: serviceInfo.ClientIPAddr().String(),
			},
		},
		Text:  remark,
		Async: false,
	})
	if serr != nil {
		log.ErrorWithCtx(ctx, "checkOrderRemark fail to SyncScanText. uid:%d, err:%+v", uid, serr)
		return serr
	}

	// 机审结果REJECT
	if v2.Suggestion_REJECT == v2.Suggestion(verifyRes.GetResult()) {
		log.ErrorWithCtx(ctx, "checkOrderRemark SyncScanText REJECT. content:%s uid:%d", remark, uid)
		return protocol.NewExactServerError(nil, errCode.ErrEsportsOrderPayRemarkReviewReject)
	}

	return nil
}

func (s *Server) checkBeforePay(ctx context.Context, opUid, coachUid uint32) error {
	// 判断开关, 任意一方非白名单都不可下单
	if !s.checkSwitch(ctx, opUid, coachUid, true, false) {
		return protocol.NewExactServerError(codes.OK, errCode.ErrEsportHallProductNotFound)
	}

	// 检查电竞陪玩身份
	roleResp, err := s.eSportRoleCli.GetUserESportRole(ctx, &esport_role.GetUserESportRoleReq{
		Uid: coachUid,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "checkBeforePay fail to GetUserESportRole. uid:%d, coachUid:%d, err:%v", opUid, coachUid, err)
		return err
	}

	if roleResp.GetEsportRole() == 0 {
		return protocol.NewExactServerError(nil, errCode.ErrEsportsOrderCannotPayCoachRoleReclaimed, "大神当前不可接单该游戏")
	}

	resp, err := s.userBlacklistCli.CheckIsInBlackList(ctx, opUid, coachUid)
	if err != nil {
		log.ErrorWithCtx(ctx, "checkBeforePay fail to CheckIsInBlackList. uid:%d, coachUid:%d, err:%v", opUid, coachUid, err)
	}

	if resp.GetBIn() {
		return protocol.NewExactServerError(nil, errCode.ErrEsportsOrderCannotPay, "您已拉黑对方，不可下单")
	}

	resp, err = s.userBlacklistCli.CheckIsInBlackList(ctx, coachUid, opUid)
	if err != nil {
		log.ErrorWithCtx(ctx, "checkBeforePay fail to CheckIsInBlackList. uid:%d, coachUid:%d, err:%v", opUid, coachUid, err)
	}

	if resp.GetBIn() {
		return protocol.NewExactServerError(nil, errCode.ErrEsportsOrderCannotPay, "您已被对方拉黑，不可下单")
	}

	return nil
}

// PlayerCancelOrder 玩家取消订单
func (s *Server) PlayerCancelOrder(ctx context.Context, in *esport_logic.PlayerCancelOrderRequest) (*esport_logic.PlayerCancelOrderResponse, error) {
	out := &esport_logic.PlayerCancelOrderResponse{}

	svrInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
	if !ok {
		log.ErrorWithCtx(ctx, "PlayerCancelOrder ServiceInfoFromContext fail. in:%+v", in)
		return out, protocol.NewExactServerError(nil, errCode.ErrRequestParamInvalid)
	}
	opUid := svrInfo.UserID

	if in.GetOrderId() == "" {
		return out, protocol.NewExactServerError(nil, errCode.ErrRequestParamInvalid)
	}

	// 取消订单
	_, err := s.orderService.PlayerCancelOrder(ctx, &esport_trade.PlayerCancelOrderRequest{
		Uid:     opUid,
		OrderId: in.GetOrderId(),
		Reason:  in.GetReason(),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "PlayerCancelOrder fail to PlayerCancelOrder. in:%+v, err:%v", in, err)
		return out, err
	}

	log.InfoWithCtx(ctx, "PlayerCancelOrder success. in:%+v, out:%+v", in, out)
	return out, nil
}

// PlayerFinishOrder 玩家完成订单
func (s *Server) PlayerFinishOrder(ctx context.Context, in *esport_logic.PlayerFinishOrderRequest) (*esport_logic.PlayerFinishOrderResponse, error) {
	out := &esport_logic.PlayerFinishOrderResponse{}

	svrInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
	if !ok {
		log.ErrorWithCtx(ctx, "PlayerFinishOrder ServiceInfoFromContext fail. in:%+v", in)
		return out, protocol.NewExactServerError(nil, errCode.ErrRequestParamInvalid)
	}
	opUid := svrInfo.UserID

	if in.GetOrderId() == "" {
		return out, protocol.NewExactServerError(nil, errCode.ErrRequestParamInvalid)
	}

	_, err := s.orderService.PlayerFinishOrder(ctx, &esport_trade.PlayerFinishOrderRequest{
		Uid:     opUid,
		OrderId: in.GetOrderId(),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "PlayerFinishOrder fail to PlayerFinishOrder. in:%+v, err:%v", in, err)
		return out, err
	}

	out.EvaluateEntry = s.bc.GetOrderFinishEvaluateEntry()

	log.InfoWithCtx(ctx, "PlayerFinishOrder success. in:%+v, out:%+v", in, out)

	// 尝试领取优惠券
	out.CouponPopupInfo, err = s.GainCoupon(ctx, in.GetBaseReq(), svrInfo.UserID, in.GetOrderId(), uint32(in.GetEntranceSourceType()))
	if err != nil {
		log.WarnWithCtx(ctx, "PlayerFinishOrder fail to GainCoupon. in:%+v, err:%v", in, err)
	}

	// 无优惠券则尝试展示剩余优惠券
	if out.CouponPopupInfo == nil {
		out.CouponPopupInfo, err = s.ShowRemainCoupon(ctx)
		if err != nil {
			log.WarnWithCtx(ctx, "PlayerFinishOrder fail to ShowRemainCoupon. in:%+v, err:%v", in, err)
		}
	}

	if out.CouponPopupInfo != nil {
		out.ShowCouponPopup = true
	}

	// 数分上报
	s.mgr.AsyncReportByLinkOrderFinish(ctx, strings.Clone(in.GetOrderId()), int32(in.GetEntranceSourceType()))

	return out, nil
}

// CoachReceiveOrder 电竞陪玩接单
func (s *Server) CoachReceiveOrder(ctx context.Context, in *esport_logic.CoachReceiveOrderRequest) (*esport_logic.CoachReceiveOrderResponse, error) {
	out := &esport_logic.CoachReceiveOrderResponse{}

	svrInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
	if !ok {
		log.ErrorWithCtx(ctx, "CoachReceiveOrder ServiceInfoFromContext fail. in:%+v", in)
		return out, protocol.NewExactServerError(nil, errCode.ErrRequestParamInvalid)
	}
	opUid := svrInfo.UserID

	if in.GetOrderId() == "" {
		return out, protocol.NewExactServerError(nil, errCode.ErrRequestParamInvalid)
	}

	var err error
	// 风控检查
	out.BaseResp, err = s.riskCheckBeforeReceiveOrder(ctx, in.GetBaseReq())
	if err != nil {
		log.ErrorWithCtx(ctx, "CoachReceiveOrder fail to riskCheckBeforeReceiveOrder. in:%+v, err:%v", in, err)
		return out, err
	}

	// 接单
	_, err = s.orderService.CoachReceiveOrder(ctx, &esport_trade.CoachReceiveOrderRequest{
		Uid:     opUid,
		OrderId: in.GetOrderId(),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "CoachReceiveOrder fail to CoachReceiveOrder. in:%+v, err:%v", in, err)
		return out, err
	}

	log.InfoWithCtx(ctx, "CoachReceiveOrder success. in:%+v, out:%+v", in, out)
	return out, nil
}

// CoachRefuseOrder 电竞陪玩拒绝订单
func (s *Server) CoachRefuseOrder(ctx context.Context, in *esport_logic.CoachRefuseOrderRequest) (*esport_logic.CoachRefuseOrderResponse, error) {
	out := &esport_logic.CoachRefuseOrderResponse{}

	svrInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
	if !ok {
		log.ErrorWithCtx(ctx, "CoachRefuseOrder ServiceInfoFromContext fail. in:%+v", in)
		return out, protocol.NewExactServerError(nil, errCode.ErrRequestParamInvalid)
	}
	opUid := svrInfo.UserID

	if in.GetOrderId() == "" {
		return out, protocol.NewExactServerError(nil, errCode.ErrRequestParamInvalid)
	}

	// 拒绝订单
	_, err := s.orderService.CoachRefuseOrder(ctx, &esport_trade.CoachRefuseOrderRequest{
		Uid:     opUid,
		OrderId: in.GetOrderId(),
		Reason:  in.GetReason(),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "CoachRefuseOrder fail to CoachReceiveOrder. in:%+v, err:%v", in, err)
		return out, err
	}

	log.InfoWithCtx(ctx, "CoachRefuseOrder success. in:%+v, out:%+v", in, out)
	return out, nil
}

// GetOrderDetail 获取订单详情
func (s *Server) GetOrderDetail(ctx context.Context, in *esport_logic.GetOrderDetailRequest) (*esport_logic.GetOrderDetailResponse, error) {
	out := &esport_logic.GetOrderDetailResponse{}

	svrInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
	if !ok {
		log.ErrorWithCtx(ctx, "GetOrderDetail ServiceInfoFromContext fail. in:%+v", in)
		return out, protocol.NewExactServerError(nil, errCode.ErrRequestParamInvalid)
	}
	opUid := svrInfo.UserID

	if in.GetOrderId() == "" {
		return out, protocol.NewExactServerError(nil, errCode.ErrRequestParamInvalid)
	}

	tradeResp, err := s.orderService.GetOrderDetail(ctx, &esport_trade.GetOrderDetailRequest{
		Uid:               opUid,
		OrderId:           in.GetOrderId(),
		NeedAllStatusTime: true,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetOrderDetail fail to GetOrderDetail. in:%+v, err:%v", in, err)
		return out, err
	}

	productOrder := tradeResp.GetOrder().GetProductOrder()
	coachUid := productOrder.GetCoachUid()
	playerUid := tradeResp.GetOrder().GetPlayerUid()

	// 判断当前用户是否有权查询订单的信息
	err = s.mgr.CanUserVisitOrderDetail(ctx, opUid, tradeResp.GetOrder())
	if err != nil {
		log.InfoWithCtx(ctx, "GetOrderDetail 当前用户[%d] 无权查询订单[%s][%d]详情, err: %v", opUid, tradeResp.GetOrder().GetOrderNumber(), tradeResp.GetOrder().GetOrderId(), err)
		return out, err
	}

	upMap, err := s.userProfileCli.BatchGetUserProfileV2(ctx, []uint32{coachUid, playerUid}, false)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetOrderDetail fail to GetUserProfileV2. in:%+v, coachUid:%d, err:%v", in, coachUid, err)
		return out, err
	}

	out.Order = fillOrderDetailFromTradePb(opUid, tradeResp.GetOrder(), upMap)

	if out.GetOrder().GetStatus() == uint32(esport_trade.OrderStatus_ORDER_STATUS_IN_REFUNDING) ||
		out.GetOrder().GetStatus() == uint32(esport_trade.OrderStatus_ORDER_STATUS_REFUNDED) {
		// 获取退款申诉信息
		refundResp, err := s.refundService.GetOrderRefundView(ctx, &esport_trade_appeal.GetOrderRefundViewRequest{
			OrderId: out.GetOrder().GetOrderId(),
		})
		if err != nil {
			log.ErrorWithCtx(ctx, "GetOrderDetail fail to GetOrderRefundView. in:%+v, coachUid:%d, err:%v", in, coachUid, err)
			return out, err
		}

		refundInfo := refundResp.GetOrderRefundView()
		out.Refund = fillRefundPbInfo(refundInfo)
		out.GetOrder().SubStatus = refundInfo.GetStatus() // 将子状态设置为退款申诉系统的状态
	}
	// 判断能否显示举报入口
	out.ShowReportEntrance = shouldShowReportEntrance(opUid, s.bc.GetReportEntTimeOut(), out.Order, out.Refund)
	// 判断是否显示客服入口
	coachUid = out.GetOrder().GetProductOrder().GetCoach().GetUid()
	checkMap, err := s.mgr.CheckIsShowCustomerEntOnOrderByCoachUids(ctx, []uint32{coachUid})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetOrderDetail fail to CheckIsShowCustomerEntOnOrderByCoachUids. in:%+v, coachUid:%d, err:%v", in, coachUid, err)
	} else {
		out.GetOrder().GetProductOrder().CanCallCustomer = checkMap[coachUid]
	}

	// 如果是大神请求的话，获取提示文案
	if opUid == coachUid {
		hintRsp, err := s.orderService.GetOrderHint(ctx, &esport_trade.GetOrderHintRequest{
			Uid:       opUid,
			Scene:     esport_trade.GetOrderHintRequest_SCENE_PAY_ORDER_COACH_GLOBAL,
			OrderType: productOrder.GetPriceInfo().GetDiscountType(),
		})
		if err != nil {
			log.ErrorWithCtx(ctx, "GetOrderDetail fail to GetOrderHint. in:%+v, coachUid:%d, err:%v", in, coachUid, err)
		} else {
			out.HintText = hintRsp.GetHintText()
		}
	}

	return out, nil
}

func fillRefundPbInfo(info *esport_trade_appeal.OrderRefundView) *esport_logic.OrderRefund {
	return &esport_logic.OrderRefund{
		RefundId:                  info.GetRefundId(),
		Status:                    info.GetStatus(),
		RefundType:                esport_logic.RefundType(info.GetRefundType()),
		RefundReason:              info.GetRefundReason(),
		RefundPrice:               info.GetRefundPrice(),
		RefundCount:               info.GetRefundCount(),
		RefundDesc:                info.GetRefundDesc(),
		EndTime:                   info.GetEndTime(),
		AppealDesc:                info.GetAppealDesc(),
		RefundRejectReason:        info.GetRefundRejectReason(),
		RefundRejectDesc:          info.GetRefundRejectDesc(),
		AppealReason:              info.GetAppealReason(),
		CanCoachUploadAppeal:      info.GetCanCoachUploadAppeal(),
		AppealId:                  info.GetAppealId(),
		CoachUploadAppealDeadline: info.GetCoachUploadAppealDeadline(),
		CanAppeal:                 info.GetCanAppeal(),
		RefundWay:                 esport_logic.RefundWay(info.GetRefundWay()),
	}
}

// checkOneIfHasRole 检查是否有一方是电竞陪玩身份
func (s *Server) checkOneIfHasRole(ctx context.Context, uidList []uint32) (bool, error) {
	// 检查是否有一方是电竞陪玩身份，否则直接结束
	roleResp, err := s.eSportRoleCli.BatchGetUserESportRole(ctx, &esport_role.BatchGetUserESportRoleReq{
		Uid: uidList,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "checkOneIfHasRole fail to BatchGetUserESportRole. uidList:%+v, err:%v", uidList, err)
		return false, err
	}

	for _, role := range roleResp.GetRoleList() {
		if role.GetEsportRole() > 0 {
			return true, nil
		}
	}

	return false, nil
}

// GetIMFloatWindowInfo 获取IM页上方悬浮窗，包含进行中的订单和技能商品信息
func (s *Server) GetIMFloatWindowInfo(ctx context.Context, in *esport_logic.GetIMFloatWindowInfoRequest) (*esport_logic.GetIMFloatWindowInfoResponse, error) {
	out := &esport_logic.GetIMFloatWindowInfoResponse{
		OrderList:   make([]*esport_logic.OrderSimpleInfo, 0),
		ProductList: make([]*esport_logic.SkillProduct, 0),
	}

	svrInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
	if !ok {
		log.ErrorWithCtx(ctx, "GetIMFloatWindowInfo ServiceInfoFromContext fail. in:%+v", in)
		return out, protocol.NewExactServerError(nil, errCode.ErrRequestParamInvalid)
	}
	opUid := svrInfo.UserID

	if in.GetTargetUid() == 0 {
		return out, protocol.NewExactServerError(nil, errCode.ErrRequestParamInvalid)
	}

	if in.GetTargetUid() == opUid {
		// 自己和自己聊天不返回数据
		return out, nil
	}

	// 获取双方正在进行的订单
	tradeResp, err := s.orderService.GetImOngoingOrderList(ctx, &esport_trade.GetImOngoingOrderListRequest{
		Uid:       opUid,
		TargetUid: in.GetTargetUid(),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetIMFloatWindowInfo fail to GetImOngoingOrderList. in:%+v, err:%v", in, err)
		return out, err
	}

	upMap, err := s.userProfileCli.BatchGetUserProfileV2(ctx, []uint32{opUid, in.GetTargetUid()}, false)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetIMFloatWindowInfo fail to GetUserProfileV2. in:%+v, err:%v", in, err)
		return out, err
	}

	orderList := make([]*esport_logic.OrderSimpleInfo, 0)
	for _, order := range tradeResp.GetOrderList() {
		info := fillOrderSimpleFromTradePb(order, upMap)
		orderList = append(orderList, info)
	}

	err = s.fillOrderRefundStatusInfo(ctx, orderList)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetIMFloatWindowInfo fail to fillOrderRefundStatusInfo. in:%+v, err:%v", in, err)
		return out, err
	}

	for _, info := range orderList {
		// 申诉有结果的订单不返回
		if info.GetStatus() == uint32(esport_logic.OrderStatus_ORDER_STATUS_IN_REFUNDING) &&
			(info.GetSubStatus() == uint32(esport_logic.RefundStatus_REFUND_STATUS_APPEALING_REJECT) ||
				info.GetSubStatus() == uint32(esport_logic.RefundStatus_REFUND_STATUS_APPEALING_ACCEPT)) {
			continue
		}
		out.OrderList = append(out.OrderList, info)
	}

	// 检查是否有一方是电竞陪玩身份，都不是陪玩则直接结束
	if ok, err := s.checkOneIfHasRole(ctx, []uint32{opUid, in.GetTargetUid()}); err != nil {
		log.ErrorWithCtx(ctx, "GetIMFloatWindowInfo fail to checkOneIfHasRole. in:%+v, err:%v", in, err)
		return out, err
	} else {
		if !ok {
			// 都不是陪玩
			return out, nil
		}
	}

	if !s.checkSwitch(ctx, svrInfo.UserID, in.GetTargetUid(), true, false) { // 如果开关关闭则提前返回
		log.WarnWithCtx(ctx, "GetHomePageSkillProductList fail. req: +%v, err: switch close", in)
		return out, nil
	}

	// 获取技能商品信息
	out.ProductList, err = s.getSkillProductList(ctx, in.GetTargetUid(), opUid)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetIMFloatWindowInfo fail to getSkillProductList. in:%+v, err:%v", in, err)
		return out, err
	}

	return out, nil
}

func (s *Server) fillOrderRefundStatusInfo(ctx context.Context, list []*esport_logic.OrderSimpleInfo) error {
	refundOrderList := make([]string, 0)
	for _, order := range list {
		if order.GetStatus() == uint32(esport_logic.OrderStatus_ORDER_STATUS_IN_REFUNDING) ||
			order.GetStatus() == uint32(esport_logic.OrderStatus_ORDER_STATUS_REFUNDED) {
			refundOrderList = append(refundOrderList, order.GetOrderId())
		}
	}

	if len(refundOrderList) == 0 {
		return nil
	}

	// 获取退款申诉信息
	refundResp, err := s.refundService.BatchGetOrderRefundStatus(ctx, &esport_trade_appeal.BatchGetOrderRefundStatusRequest{
		OrderIds: refundOrderList,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "fillOrderRefundStatusInfo fail to BatchGetOrderRefundStatus. list:%+v, err:%v", list, err)
		return err
	}

	refundMap := make(map[string]*esport_trade_appeal.BatchGetOrderRefundStatusResponse_OrderRefundStatus)
	for _, refundInfo := range refundResp.GetOrderRefundStatus() {
		refundMap[refundInfo.GetOrderId()] = refundInfo
	}

	for _, info := range list {
		refund, ok := refundMap[info.GetOrderId()]
		if ok {
			info.SubStatus = refund.GetStatus()
			info.OrderEndTime = refund.GetEndTime()
			info.CanCoachUploadAppeal = refund.GetCanCoachUploadAppeal()
			info.AppealId = refund.GetAppealId()
			info.RefundId = refund.GetRefundId()
			info.CanAppeal = refund.GetCanAppeal()
			info.RefundType = esport_logic.RefundType(refund.GetRefundType())
			info.CoachUploadAppealDeadline = refund.GetCoachUploadAppealDeadline()
		}
	}

	return nil
}

// GetOrderList 获取订单列表
func (s *Server) GetOrderList(ctx context.Context, in *esport_logic.GetOrderListRequest) (*esport_logic.GetOrderListResponse, error) {
	out := &esport_logic.GetOrderListResponse{}
	svrInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
	if !ok {
		log.ErrorWithCtx(ctx, "GetOrderList ServiceInfoFromContext fail. in:%+v", in)
		return out, protocol.NewExactServerError(nil, errCode.ErrRequestParamInvalid)
	}
	opUid := svrInfo.UserID

	log.DebugWithCtx(ctx, "GetOrderList. in:%+v", in)

	if in.GetLimit() == 0 {
		return out, nil
	}

	// 获取双方正在进行的订单
	tradeResp, err := s.orderService.GetOrderList(ctx, &esport_trade.GetOrderListRequest{
		Uid:        opUid,
		OrderType:  in.GetOrderType(),
		StatusType: in.GetStatusType(),
		Offset:     in.GetOffset(),
		Limit:      in.GetLimit(),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetOrderList fail to GetOrderDetail. in:%+v, err:%v", in, err)
		return out, err
	}

	uidList := []uint32{opUid}
	for _, order := range tradeResp.GetOrderList() {
		if in.GetOrderType() == uint32(esport_logic.GetOrderListRequest_ORDER_TYPE_COACH_ORDER) {
			// 查电竞陪玩接单列表，则对方都是玩家
			uidList = append(uidList, order.GetPlayerUid())
		} else {
			uidList = append(uidList, order.GetProductOrder().GetCoachUid())
		}
	}

	upMap, err := s.userProfileCli.BatchGetUserProfileV2(ctx, uidList, false)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetOrderList fail to GetUserProfileV2. in:%+v, err:%v", in, err)
		return out, err
	}

	for _, order := range tradeResp.GetOrderList() {
		info := fillOrderSimpleFromTradePb(order, upMap)
		out.OrderList = append(out.OrderList, info)
	}

	err = s.fillOrderRefundStatusInfo(ctx, out.GetOrderList())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetOrderList fail to fillOrderRefundStatusInfo. in:%+v, err:%v", in, err)
		return out, err
	}

	// 判断能否联系客服
	// 抽取大神的id
	coachUids := make([]uint32, 0)
	for _, item := range out.GetOrderList() {
		coachUids = append(coachUids, item.GetProductOrder().GetCoach().GetUid())
	}
	checkMap, err := s.mgr.CheckIsShowCustomerEntOnOrderByCoachUids(ctx, coachUids)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetOrderList fail to CheckIsShowCustomerEntOnOrderByCoachUids. in:%+v, err:%v", in, err)
	} else {
		for _, item := range out.GetOrderList() {
			item.GetProductOrder().CanCallCustomer = checkMap[item.GetProductOrder().GetCoach().GetUid()]
		}
	}

	return out, nil
}

// DelOrderRecord 删除订单
func (s *Server) DelOrderRecord(ctx context.Context, in *esport_logic.DelOrderRecordRequest) (*esport_logic.DelOrderRecordResponse, error) {
	out := &esport_logic.DelOrderRecordResponse{}

	svrInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
	if !ok {
		log.ErrorWithCtx(ctx, "DelOrderRecord ServiceInfoFromContext fail. in:%+v", in)
		return out, protocol.NewExactServerError(nil, errCode.ErrRequestParamInvalid)
	}
	opUid := svrInfo.UserID

	if in.GetOrderId() == "" {
		return out, protocol.NewExactServerError(nil, errCode.ErrRequestParamInvalid)
	}

	_, err := s.orderService.DelOrderRecord(ctx, &esport_trade.DelOrderRecordRequest{
		Uid:     opUid,
		OrderId: in.GetOrderId(),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "DelOrderRecord fail to DelOrderRecord. in:%+v, err:%v", in, err)
		return out, err
	}

	log.InfoWithCtx(ctx, "DelOrderRecord success. in:%+v, out:%+v", in, out)
	return out, nil
}

// CoachNotifyFinishOrder 电竞陪玩通知玩家去完成订单
func (s *Server) CoachNotifyFinishOrder(ctx context.Context, in *esport_logic.CoachNotifyFinishOrderRequest) (*esport_logic.CoachNotifyFinishOrderResponse, error) {
	out := &esport_logic.CoachNotifyFinishOrderResponse{}

	svrInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
	if !ok {
		log.ErrorWithCtx(ctx, "CoachNotifyFinishOrder ServiceInfoFromContext fail. in:%+v", in)
		return out, protocol.NewExactServerError(nil, errCode.ErrRequestParamInvalid)
	}
	opUid := svrInfo.UserID

	if in.GetOrderId() == "" {
		return out, protocol.NewExactServerError(nil, errCode.ErrRequestParamInvalid)
	}

	// 拒绝订单
	_, err := s.orderService.CoachNotifyFinishOrder(ctx, &esport_trade.CoachNotifyFinishOrderRequest{
		Uid:     opUid,
		OrderId: in.GetOrderId(),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "CoachNotifyFinishOrder fail to CoachReceiveOrder. in:%+v, err:%v", in, err)
		return out, err
	}

	log.InfoWithCtx(ctx, "CoachNotifyFinishOrder success. in:%+v, out:%+v", in, out)
	return out, nil
}

// Evaluate 评价
func (s *Server) Evaluate(ctx context.Context, in *esport_logic.EvaluateRequest) (*esport_logic.EvaluateResponse, error) {
	out := &esport_logic.EvaluateResponse{}
	svrInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
	if !ok {
		log.ErrorWithCtx(ctx, "Evaluate ServiceInfoFromContext fail. in:%+v", in)
		return out, protocol.NewExactServerError(nil, errCode.ErrRequestParamInvalid)
	}

	opUid := svrInfo.UserID
	resp, err := s.orderService.Evaluate(ctx, &esport_trade.EvaluateRequest{
		Uid:         opUid,
		OrderId:     in.GetOrderId(),
		WordList:    in.GetWordList(),
		Content:     in.GetContent(),
		IsAnonymous: in.GetIsAnonymous(),
		ScoreInfo: &esport_trade.EvaluateScore{
			AvgScore:     in.GetScoreInfo().GetAvgScore(),
			ServiceScore: in.GetScoreInfo().GetServiceScore(),
			SkillScore:   in.GetScoreInfo().GetSkillScore(),
			VoiceScore:   in.GetScoreInfo().GetVoiceScore(),
		},
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "Evaluate fail to Evaluate. in:%+v, err:%v", in, err)
		return out, err
	}

	out.Toast = resp.GetToast()

	log.InfoWithCtx(ctx, "Evaluate success. in:%+v, out:%+v", in, out)
	return out, nil
}

// GetEvaluateWordList 获取快捷评价词列表
func (s *Server) GetEvaluateWordList(ctx context.Context, in *esport_logic.GetEvaluateWordListRequest) (*esport_logic.GetEvaluateWordListResponse, error) {
	out := &esport_logic.GetEvaluateWordListResponse{
		List: make([]*esport_logic.EvaluateWord, 0),
	}

	if in.GetOrderId() == "" {
		return out, nil
	}

	resp, err := s.orderService.GetOrderDetail(ctx, &esport_trade.GetOrderDetailRequest{
		OrderId: in.GetOrderId(),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetEvaluateWordList fail to GetOrderDetail. in:%+v, err:%v", in, err)
		return out, err
	}

	gameId := resp.GetOrder().GetProductOrder().GetGameId()
	list := s.bc.GetEvaluateQuickWords(gameId)
	for _, v := range list {
		out.List = append(out.List, &esport_logic.EvaluateWord{
			Words: v.Words,
			Star: &esport_logic.StarEvaluate{
				Dimension: v.Dimension,
				Score:     v.Score,
			},
		})
	}

	return out, nil
}

func fillOrderSimpleFromTradePb(info *esport_trade.OrderSimpleInfo, upMap map[uint32]*app.UserProfile) *esport_logic.OrderSimpleInfo {
	out := &esport_logic.OrderSimpleInfo{
		Player:        upMap[info.GetPlayerUid()],
		ProductOrder:  fillProductOrderFromTradePb(info.GetProductOrder(), upMap[info.GetProductOrder().GetCoachUid()]),
		OrderId:       info.GetOrderId(),
		Status:        info.GetStatus(),
		SubStatus:     info.GetSubStatus(),
		PayTime:       info.GetPayTime(),
		OrderEndTime:  info.GetOrderEndTime(),
		StatusDesc:    info.GetDesc(),
		UpdateTime:    info.GetUpdateTime(),
		OffsetId:      info.GetOffsetId(),
		EvaluateEntry: info.GetEvaluateEntry(),
	}

	if info.GetStatus() == uint32(esport_trade.OrderStatus_ORDER_STATUS_RECEIVED) {
		out.IsNotifyFinish = info.GetSubStatus() == uint32(esport_trade.ReceivedOrderSubStatus_RECEIVED_ORDER_SUB_STATUS_NOTIFY_FINISH)
		out.SubStatus = 0
	}

	return out
}

func fillOrderDetailFromTradePb(opUid uint32, info *esport_trade.SkillProductOrderDetail, upMap map[uint32]*app.UserProfile) *esport_logic.SkillProductOrderDetail {
	out := &esport_logic.SkillProductOrderDetail{
		Player:       upMap[info.GetPlayerUid()],
		ProductOrder: fillProductOrderFromTradePb(info.GetProductOrder(), upMap[info.GetProductOrder().GetCoachUid()]),
		OrderId:      info.GetOrderId(),
		Status:       info.GetStatus(),
		SubStatus:    info.GetSubStatus(),
		PayTime:      info.GetPayTime(),
		ReceiveTime:  info.GetReceiveTime(),
		FinishTime:   info.GetFinishTime(),
		CancelTime:   info.GetCancelTime(),
		OrderEndTime: info.GetOrderEndTime(),
		StatusDesc:   info.GetDesc(),
		OrderNumber:  info.GetOrderNumber(),
		OrderRemark:  info.GetRemark(),
		UpdateTime:   info.GetUpdateTime(),
	}

	// 在订单详情里，仅玩家可见评论
	if opUid == info.GetPlayerUid() {
		out.Evaluate = &esport_logic.OrderEvaluateInfo{
			EvaluateEntry: info.GetEvaluateEntry(),
		}

		evaluateInfo := info.GetEvaluateInfo()
		if evaluateInfo != nil && evaluateInfo.GetOrderId() != "" {
			out.Evaluate.EvaluateInfo = &esport_logic.EvaluateInfo{
				EvaluateTime: evaluateInfo.GetEvaluateTime(),
				WordList:     evaluateInfo.GetWordList(),
				Content:      evaluateInfo.GetContent(),
				IsAnonymous:  evaluateInfo.GetIsAnonymous(),
				ScoreInfo: &esport_logic.EvaluateScore{
					AvgScore:     evaluateInfo.GetScoreInfo().GetAvgScore(),
					ServiceScore: evaluateInfo.GetScoreInfo().GetServiceScore(),
					SkillScore:   evaluateInfo.GetScoreInfo().GetSkillScore(),
					VoiceScore:   evaluateInfo.GetScoreInfo().GetVoiceScore(),
				},
			}
		}
	}

	if info.GetStatus() == uint32(esport_trade.OrderStatus_ORDER_STATUS_RECEIVED) {
		out.IsNotifyFinish = info.GetSubStatus() == uint32(esport_trade.ReceivedOrderSubStatus_RECEIVED_ORDER_SUB_STATUS_NOTIFY_FINISH)
		out.SubStatus = 0
	}

	return out
}

func fillPriceInfoFromTradePb(info *esport_trade.PriceInfo) *esport_logic.PriceInfo {
	return &esport_logic.PriceInfo{
		Price:                 info.GetPrice(),
		PriceUnit:             info.GetPriceUnit(),
		MeasureCnt:            info.GetMeasureCnt(),
		MeasureUnit:           info.GetMeasureUnit(),
		HasFirstRoundDiscount: info.GetHasFirstRoundDiscount(),
		FirstRoundPrice:       info.GetFirstRoundPrice(),
		HasDiscount:           info.GetHasDiscount(),
		DiscountType:          info.GetDiscountType(),
		DiscountDesc:          info.GetDiscountDesc(),
		DiscountPrice:         info.GetDiscountPrice(),
	}
}

func fillProductOrderFromTradePb(order *esport_trade.ProductOrder, coachInfo *app.UserProfile) *esport_logic.ProductOrder {
	product := &esport_logic.ProductOrder{
		Coach:            coachInfo,
		ProductId:        order.GetProductId(),
		Name:             order.GetName(),
		Icon:             order.GetIcon(),
		PriceInfo:        fillPriceInfoFromTradePb(order.GetPriceInfo()),
		Count:            order.GetCount(),
		TotalPrice:       order.GetTotalPrice(),
		GameId:           order.GetGameId(),
		GuaranteeWinText: common.GetGranteeWinText(order.GetIsGuaranteeWin(), order.GetGuaranteeWinTexts()),
		CoachTotalPrice:  order.GetCoachTotalPrice(),
		CouponUseDetail: &esport_logic.CouponUseDetail{
			UseCoupon:   order.GetCouponUseDetail().GetUseCoupon(),
			CouponMoney: order.GetCouponUseDetail().GetCouponMoney(),
		},
		NewCustomerUseDetail: &esport_logic.NewCustomerUseDetail{
			UseNewCustomerDiscount: order.GetNewCustomerUseDetail().GetUseNewCustomerDiscount(),
			NewCustomerPrice:       order.GetNewCustomerUseDetail().GetNewCustomerPrice(),
			PlatBonusFee:           order.GetNewCustomerUseDetail().GetPlatBonusFee(),
		},
	}
	return product
}

func (s *Server) riskCheckBeforeReceiveOrder(ctx context.Context, baseReq *app.BaseReq) (*app.BaseResp, error) {
	// 返回给客户端的 BaseResp
	baseResp := &app.BaseResp{}
	serviceInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
	if !ok {
		return baseResp, protocol.NewExactServerError(codes.OK, errCode.ErrRequestParamInvalid)
	}
	checkReq := &riskMngApiPb.CheckReq{
		Scene: "ESPORT_COACH_RECEIVE_ORDER",
		SourceEntity: &riskMngApiPb.Entity{
			// 必选
			Uid: serviceInfo.UserID,
			// 设备信息，如果 ctx 里面有 servceInfo，可以不填，风控媏自行查 ctx
			DeviceIdRaw:  serviceInfo.DeviceID,
			ClientIp:     serviceInfo.ClientIPAddr().String(),
			TerminalType: serviceInfo.TerminalType,
			// 可选，用户详细信息，若有现成的数据，建议填写，未来可能有用
			Phone: "",
			// 可选， 房间信息，若有现成的数据，建议填写，未来可能有用
			ChannelId:     0,
			ChannelViewId: "",
		},
	}
	checkResp, err := s.riskCli.CheckHelper(ctx, checkReq, baseReq)
	if err != nil {
		// 系统错误，风控非关键路径，可忽略系统错误
		log.ErrorWithCtx(ctx, "riskCheckBeforeReceiveOrder risk-mng-api.Check failed, err:%v, req:%+v", err, checkReq)
		return baseResp, nil
	}
	// 命中风控拦截
	if checkResp.GetErrCode() < 0 {
		// 建议打个 info 拦截日志，方便排查，风控拦截日志不会很多
		log.InfoWithCtx(ctx, "riskCheckBeforeReceiveOrder risk-mng-api.Check hit, req:%+v, resp:%+v", checkReq, checkResp)
		// 需要返回 ErrInfo 给客户端
		baseResp.ErrInfo = checkResp.GetErrInfo()
		// 返回错误码给客户端，并设置 gRPC 错误码为 OK
		return baseResp, protocol.NewExactServerError(codes.OK, int(checkResp.GetErrCode()), checkResp.GetErrMsg())
	}
	// 无拦截
	return baseResp, nil
}

// shouldShowReportEntrance 订单详情页中能否展示举报入口
// 首先判断 opUid 是否是教练，如果是，则不展示入口
// 如果订单状态是 ORDER_STATUS_RECEIVED，则展示入口。
// 如果订单的状态是 OrderStatus_ORDER_STATUS_FINISHED，则判断完成时间距今是否超过了24小时，如果不是，则展示入口
// 如果订单状态是 OrderStatus_ORDER_STATUS_IN_REFUNDING，则判断退款状态是否是 RefundStatus_REFUND_STATUS_REFUNDING, RefundStatus_REFUND_STATUS_REFUND_REJECT, RefundStatus_REFUND_STATUS_APPEALING, 如果是，则展示入口。
// 默认不展示入口
func shouldShowReportEntrance(opUid uint32, reportEntTimeout int64, orderInfo *esport_logic.SkillProductOrderDetail, refundInfo *esport_logic.OrderRefund) bool {
	// 判断 opUid 是否是教练
	if opUid == orderInfo.GetProductOrder().GetCoach().GetUid() {
		log.Infof("shouldShowReportEntrance fail. opUid is coach. opUid:%d, orderId:%s, coachId:%d", opUid, orderInfo.GetOrderId(), orderInfo.GetProductOrder().GetCoach().GetUid())
		return false
	}

	// 判断订单状态
	switch esport_logic.OrderStatus(orderInfo.GetStatus()) {
	case esport_logic.OrderStatus_ORDER_STATUS_RECEIVED:
		return true
	case esport_logic.OrderStatus_ORDER_STATUS_FINISHED:
		// 判断完成时间距今是否超过了规定的时间
		if time.Since(time.Unix(orderInfo.GetFinishTime(), 0)) < time.Duration(reportEntTimeout)*time.Second {
			return true
		}
	case esport_logic.OrderStatus_ORDER_STATUS_IN_REFUNDING:
		// 判断退款状态
		switch esport_logic.RefundStatus(refundInfo.GetStatus()) {
		case esport_logic.RefundStatus_REFUND_STATUS_REFUNDING, esport_logic.RefundStatus_REFUND_STATUS_REFUND_REJECT, esport_logic.RefundStatus_REFUND_STATUS_APPEALING:
			return true
		}
	}
	log.Infof("shouldShowReportEntrance fail. opUid:%d, orderId:%s, orderStatus:%d, refundStatus:%d", opUid, orderInfo.GetOrderId(), orderInfo.GetStatus(), refundInfo.GetStatus())
	// 默认不展示入口
	return false
}

func (s *Server) EstimateOrderTotalPrice(ctx context.Context, in *esport_logic.EstimateOrderTotalPriceRequest) (*esport_logic.EstimateOrderTotalPriceResponse, error) {
	out := &esport_logic.EstimateOrderTotalPriceResponse{}
	defer func() {
		log.DebugWithCtx(ctx, "EstimateOrderTotalPrice in:%+v, out:%+v", in, out)
	}()

	// 参数校验
	if in.GetPlayerUid() == 0 || in.GetCoachUid() == 0 || in.GetMinAmount() == 0 || in.GetMaxAmount() > 9999 || in.GetMinAmount() > in.GetMaxAmount() {
		return out, protocol.NewExactServerError(nil, errCode.ErrRequestParamInvalid)
	}
	svrInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
	if !ok {
		return out, protocol.NewExactServerError(nil, errCode.ErrRequestParamInvalid)
	}
	opUid := svrInfo.UserID

	isInviteScene := false // 是否是邀请场景
	if opUid == in.GetCoachUid() {
		isInviteScene = true
	}

	canManualChooseCoupon := false // 是否可以手动选择优惠券
	if couponManualChooseMinVer.Atleast(svrInfo.ClientType, svrInfo.ClientVersion) {
		canManualChooseCoupon = true
	}

	// 获取商品详情
	productResp, err := s.eSportHallService.GetSkillProductInfo(ctx, &esport_hall.GetSkillProductInfoRequest{
		ProductId: uint32(in.GetProductId()),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetSkillProductInfo err: %v", err)
		return out, err
	}
	product := productResp.GetProduct()

	// 获取新客优惠使用情况
	newCustomerRsp, err := s.eSportHallService.GetNewCustomerPriceBySkill(ctx, &esport_hall.GetNewCustomerPriceBySkillRequest{
		PlayerUid: in.GetPlayerUid(),
		CoachUid:  in.GetCoachUid(),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetNewCustomerPriceBySkill err: %v", err)
		return out, err
	}
	newCustomerPrice := newCustomerRsp.GetPriceMap()[product.GetGameId()]
	if newCustomerPrice == nil {
		newCustomerPrice = &esport_hall.NewCustomerPriceInfo{}
	}

	// 获取首局优惠使用情况
	rightRsp, err := s.eSportHallService.GetFirstRoundOrderRight(ctx, &esport_hall.GetFirstRoundOrderRightRequest{
		PlayerUid: in.GetPlayerUid(),
		CoachUid:  in.GetCoachUid(),
		GameId:    product.GetGameId(),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetFirstRoundOrderRight err: %v", err)
		return out, err
	}
	// 首单风控检查
	pass, err := s.mgr.CheckUserFirstRoundByRiskSys(ctx, in.GetBaseReq(), in.GetPlayerUid(), in.GetCoachUid(), rightRsp.GetFirstRoundPrice())
	if err != nil || !pass {
		rightRsp.TodayCanUseFirstRoundDiscount = false
	}

	// 查询可用优惠券列表
	couponBatRsp := &esport_internal.BatClassifyCouponResponse{}
	if !isInviteScene { // 主动下单，才查询可用优惠券
		couponBatRsp, err = s.esportInternalCli.BatClassifyCoupon(ctx, &esport_internal.BatClassifyCouponRequest{
			PlayerUid:     opUid,
			CoachUid:      product.Uid,
			GameId:        product.GameId,
			OriginalPrice: product.Price,
			MinBuyAmount:  in.MinAmount,
			MaxBuyAmount:  in.MaxAmount,
			QueryOption:   nil,
		})
		if err != nil {
			log.ErrorWithCtx(ctx, "EstimateOrderTotalPrice BatClassifyCoupon err: %v", err)
			return out, err
		}
	}

	// 组装返回
	out = s.fillTotalPrice(ctx, in, isInviteScene, canManualChooseCoupon, product, newCustomerPrice, rightRsp, couponBatRsp)
	return out, nil
}

func (s *Server) fillTotalPrice(ctx context.Context, in *esport_logic.EstimateOrderTotalPriceRequest, isInviteScene, canManualChooseCoupon bool,
	product *esport_hall.SkillProduct, newCustomerPrice *esport_hall.NewCustomerPriceInfo, firstRoundRsp *esport_hall.GetFirstRoundOrderRightResponse,
	couponBatRsp *esport_internal.BatClassifyCouponResponse) *esport_logic.EstimateOrderTotalPriceResponse {
	out := &esport_logic.EstimateOrderTotalPriceResponse{}
	out.ShowCouponEntrance = couponBatRsp.GetCanUseCouponSwitch()
	useNewCustomerDiscount := newCustomerPrice.GetHasNewCustomerDiscount()

	useFirstRoundDiscount := false
	if !useNewCustomerDiscount {
		if isInviteScene {
			useFirstRoundDiscount = firstRoundRsp.GetHasFirstRoundDiscount()
		} else {
			useFirstRoundDiscount = firstRoundRsp.GetTodayCanUseFirstRoundDiscount()
		}
	}

	useOutToday := false
	if !isInviteScene && !useNewCustomerDiscount && firstRoundRsp.HasFirstRoundDiscount && !firstRoundRsp.TodayCanUseFirstRoundDiscount {
		useOutToday = true
		if in.GetMinAmount() == 1 { // 只有首次进入才弹
			out.EnterPageToast = "今日无法继续享受首局优惠啦\n明天再来吧~"
		}
	}

	for amount := in.GetMinAmount(); amount <= in.GetMaxAmount(); amount++ {

		// 共用变量
		couponRsp := couponBatRsp.GetResultMap()[amount]
		var chosenCouponIds []string
		var chosenCoupon *esport_trade.Coupon
		// 新版本变量
		couponHint := &esport_logic.TotalPriceInfo_CouponHint{CanEnterChosenPage: true}
		// 老版本变量
		var couponPrice uint32
		var couponNoUseReason string

		if canManualChooseCoupon { // 新版本的选择逻辑
			if useNewCustomerDiscount {
				couponHint.CouponHintStyle = uint32(esport_logic.TotalPriceInfo_COUPON_HINT_STYLE_GRAY)
				couponHint.CouponHintStr = "新客优惠，无法使用优惠券~"
				couponHint.CanEnterChosenPage = false
			} else if useFirstRoundDiscount {
				couponHint.CouponHintStyle = uint32(esport_logic.TotalPriceInfo_COUPON_HINT_STYLE_GRAY)
				couponHint.CouponHintStr = "首单优惠，无法使用优惠券~"
				couponHint.CanEnterChosenPage = false
			} else {
				if len(in.GetChosenCoupons()) > 0 { // 用户选了某张券
					chosenCoupon = getChosenCoupon(couponRsp, in.GetChosenCoupons()[0])
					if chosenCoupon != nil {
						chosenCouponIds = []string{chosenCoupon.GetCouponId()}
						couponHint.ChosenCoupons = chosenCouponIds
						couponHint.CouponHintStyle = uint32(esport_logic.TotalPriceInfo_COUPON_HINT_STYLE_RED)
						couponHint.CouponHintStr = fmt.Sprintf("-%d豆", chosenCoupon.GetReducePrice())
					}
				}

				if chosenCoupon == nil { // 用户没选券，或者选的券当前不可用
					if len(couponRsp.GetAvailableList()) == 0 {
						if len(couponRsp.GetUnavailableList()) > 0 && (couponRsp.GetUnavailableList()[0].GetReason() == "coach everyday limit" || couponRsp.GetUnavailableList()[0].GetReason() == "player everyday limit") {
							couponHint.CouponHintStyle = uint32(esport_logic.TotalPriceInfo_COUPON_HINT_STYLE_GRAY)
							couponHint.CouponHintStr = "今日无法对该大神使用优惠券"
							couponHint.CanEnterChosenPage = false
						} else {
							couponHint.CouponHintStyle = uint32(esport_logic.TotalPriceInfo_COUPON_HINT_STYLE_GRAY)
							couponHint.CouponHintStr = "暂无可用"
							couponHint.CanEnterChosenPage = false
						}
					} else {
						couponHint.CouponHintStyle = uint32(esport_logic.TotalPriceInfo_COUPON_HINT_STYLE_NORMAL)
						couponHint.CouponHintStr = fmt.Sprintf("%d张可用", len(couponRsp.GetAvailableList()))
					}
				}
			}
		} else { // 老版本的选择逻辑
			if useNewCustomerDiscount {
				couponNoUseReason = "新客优惠，无法使用优惠券~"
			} else if useFirstRoundDiscount {
				couponNoUseReason = "首单优惠，无法使用优惠券~"
			} else {
				chosenCoupon = getBestCoupon(couponRsp)
				if chosenCoupon != nil {
					chosenCouponIds = []string{chosenCoupon.GetCouponId()}
					couponPrice = chosenCoupon.GetReducePrice()
				} else {
					couponNoUseReason = "暂无可用"
				}
			}
		}

		discountInfo := &esport_logic.OrderDiscountInfo{
			UseFirstRoundDiscount:  useFirstRoundDiscount,
			CouponIds:              chosenCouponIds,
			UseNewCustomerDiscount: useNewCustomerDiscount,
		}
		discountInfoStr, _ := json.Marshal(discountInfo)

		totalPrice := product.GetPrice() * amount
		if useFirstRoundDiscount {
			totalPrice = firstRoundRsp.GetFirstRoundPrice() + product.GetPrice()*(amount-1)
		}
		if chosenCoupon != nil {
			totalPrice = product.GetPrice()*amount - chosenCoupon.GetReducePrice()
		}
		if useNewCustomerDiscount {
			totalPrice = newCustomerPrice.GetNewCustomerPrice() + product.GetPrice()*(amount-1) // 新客价只能下一局，但保险一点
		}

		redHint := &esport_logic.TotalPriceInfo_RedHint{}
		if useFirstRoundDiscount && amount != 1 {
			redHint.RedHintStr = "*首局优惠，第二局后按原价计算"
			redHint.RedHintCond = uint32(esport_logic.TotalPriceInfo_RED_HINT_COND_SHOW)
		}
		if useOutToday {
			redHint.RedHintStr = "*今日无法继续享受首局优惠，已恢复正常下单价格"
			redHint.RedHintCond = uint32(esport_logic.TotalPriceInfo_RED_HINT_COND_SHOW)
		}

		toastHint := &esport_logic.TotalPriceInfo_ToastHint{}
		if useFirstRoundDiscount && amount == 1 {
			toastHint.ToastHintStr = "已含第一局优惠"
			toastHint.ToastHintCond = uint32(esport_logic.TotalPriceInfo_TOAST_HINT_COND_ADD_AMOUNT)
		}

		out.PriceList = append(out.PriceList, &esport_logic.TotalPriceInfo{
			Amount:       amount,
			TotalPrice:   totalPrice,
			DiscountInfo: string(discountInfoStr),
			RedHint:      redHint,
			ToastHint:    toastHint,
			CouponPrice:  couponPrice,
			CouponReason: couponNoUseReason,
			CouponHint:   couponHint,
		})
	}

	return out
}

func getChosenCoupon(resp *esport_internal.ClassifyCouponResponse, couponId string) *esport_trade.Coupon {
	if resp == nil || len(resp.GetAvailableList()) == 0 {
		return nil
	}
	for _, item := range resp.GetAvailableList() {
		if item.GetCoupon().GetCouponId() == couponId {
			return item.GetCoupon()
		}
	}
	return nil
}

func getBestCoupon(resp *esport_internal.ClassifyCouponResponse) *esport_trade.Coupon {
	if resp == nil || len(resp.GetAvailableList()) == 0 {
		return nil
	}

	coupons := make([]*esport_trade.Coupon, len(resp.GetAvailableList()))
	for i, item := range resp.GetAvailableList() {
		coupons[i] = item.GetCoupon()
	}

	// 排序，过期时间早的在前，满减额度小的在前
	sort.Slice(coupons, func(i, j int) bool {
		if coupons[i].GetExpireTime() == coupons[j].GetExpireTime() {
			return coupons[i].GetReducePrice() < coupons[j].GetReducePrice()
		}
		return coupons[i].GetExpireTime() < coupons[j].GetExpireTime()
	})

	return coupons[0]
}
