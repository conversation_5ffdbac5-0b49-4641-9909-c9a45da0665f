package mgr

import (
	"context"
	"errors"
	"github.com/golang/mock/gomock"
	"golang.52tt.com/clients/account"
	mockaccount "golang.52tt.com/clients/mocks/account"
	mockAnchorcontract "golang.52tt.com/clients/mocks/anchorcontract-go"
	mockApiCenter "golang.52tt.com/clients/mocks/apicenter/apiserver"
	mockGuild "golang.52tt.com/clients/mocks/guild"
	mockImApi "golang.52tt.com/clients/mocks/im-api"
	mockTtcProxy "golang.52tt.com/clients/mocks/ttc-proxy"
	mockUserOl "golang.52tt.com/clients/mocks/user-online"
	"golang.52tt.com/pkg/protocol"
	accountPb "golang.52tt.com/protocol/services/accountsvr"
	"golang.52tt.com/protocol/services/esport_role"
	clientmocks "golang.52tt.com/protocol/services/mocks"
	"golang.52tt.com/services/account-appeal-http-logic/models"
	"golang.52tt.com/services/tt-rev/esport/esport-role/internal/mocks"
	"golang.52tt.com/services/tt-rev/esport/esport-role/internal/rpc"
	"golang.52tt.com/services/tt-rev/esport/esport-role/internal/store"
	"google.golang.org/grpc/codes"
	"reflect"
	"sort"
	"testing"

	"time"
)

func Test_genTTidStr(t *testing.T) {
	type args struct {
		ttidList []string
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{
			name: "test1",
			args: args{
				ttidList: []string{"1", "2", "3"},
			},
			want: "\"1\"、\"2\"、\"3\"",
		},
		{
			name: "test2",
			args: args{
				ttidList: []string{"1"},
			},
			want: "\"1\"",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := genTTidStr(tt.args.ttidList); got != tt.want {
				t.Errorf("genTTidStr() = %v, want %v", got, tt.want)
			}
		})
	}
}

type mgrHelperForTest struct {
	*Mgr
}

func newMgrHelperForTest(t *testing.T) *mgrHelperForTest {
	controller := gomock.NewController(t)
	defer controller.Finish()
	return &mgrHelperForTest{
		Mgr: &Mgr{
			store: mocks.NewMockIStore(controller),
			rpcCli: &rpc.Client{
				AccountCli:         mockaccount.NewMockIClient(controller),
				TBeanCli:           models.InitTBean("http://tbean-service.quicksilver.svc/portal/", "123456", "tt_server"),
				ApiClient:          mockApiCenter.NewMockIClient(controller),
				ImApiCli:           mockImApi.NewMockIClient(controller),
				UserOlCli:          mockUserOl.NewMockIClient(controller),
				AnchorContractCli:  mockAnchorcontract.NewMockIClient(controller),
				ESportSkillCli:     clientmocks.NewMockEsportSkillClient(controller),
				GuildCli:           mockGuild.NewMockIClient(controller),
				TtcProxyCli:        mockTtcProxy.NewMockIClient(controller),
				ESportStatisticCli: clientmocks.NewMockEsportStatisticsClient(controller),
				ESportHallCli:      clientmocks.NewMockEsportHallClient(controller),
			},
			cache: mocks.NewMockICache(controller),
		},
	}

}

func (m *mgrHelperForTest) getAccountCli() *mockaccount.MockIClient {
	return m.rpcCli.AccountCli.(*mockaccount.MockIClient)
}

func (m *mgrHelperForTest) getStore() *mocks.MockIStore {
	return m.store.(*mocks.MockIStore)
}

func (m *mgrHelperForTest) getCache() *mocks.MockICache {
	return m.cache.(*mocks.MockICache)
}

func (m *mgrHelperForTest) getTBeanCli() *models.TBean {
	return m.rpcCli.TBeanCli
}

func (m *mgrHelperForTest) getApiCenterCli() *mockApiCenter.MockIClient {
	return m.rpcCli.ApiClient.(*mockApiCenter.MockIClient)
}

// 生成mgrHelperForTest的全部get方法
func (m *mgrHelperForTest) getImApiCli() *mockImApi.MockIClient {
	return m.rpcCli.ImApiCli.(*mockImApi.MockIClient)
}

func (m *mgrHelperForTest) getUserOlCli() *mockUserOl.MockIClient {
	return m.rpcCli.UserOlCli.(*mockUserOl.MockIClient)
}

func (m *mgrHelperForTest) getAnchorContractCli() *mockAnchorcontract.MockIClient {
	return m.rpcCli.AnchorContractCli.(*mockAnchorcontract.MockIClient)
}

func (m *mgrHelperForTest) getESportSkillCli() *clientmocks.MockEsportSkillClient {
	return m.rpcCli.ESportSkillCli.(*clientmocks.MockEsportSkillClient)
}

func (m *mgrHelperForTest) getGuildCli() *mockGuild.MockIClient {
	return m.rpcCli.GuildCli.(*mockGuild.MockIClient)
}

func (m *mgrHelperForTest) getTtcProxyCli() *mockTtcProxy.MockIClient {
	return m.rpcCli.TtcProxyCli.(*mockTtcProxy.MockIClient)
}

func (m *mgrHelperForTest) getESportStatisticCli() *clientmocks.MockEsportStatisticsClient {
	return m.rpcCli.ESportStatisticCli.(*clientmocks.MockEsportStatisticsClient)
}

func (m *mgrHelperForTest) getESportHallCli() *clientmocks.MockEsportHallClient {
	return m.rpcCli.ESportHallCli.(*clientmocks.MockEsportHallClient)
}

func TestMgr_BatchGrantCoachLabel(t *testing.T) {
	// 用于测试时间冲突
	tsNow := time.Now()
	start := time.Date(tsNow.Year(), tsNow.Month(), tsNow.Day(), 0, 0, 0, 0, tsNow.Location())
	end := time.Date(tsNow.Year(), tsNow.Month(), tsNow.Day(), 23, 59, 59, 0, tsNow.Location())

	type args struct {
		c       context.Context
		request *esport_role.BatchGrantCoachLabelRequest
	}
	tests := []struct {
		name     string
		initFunc func(m *mgrHelperForTest)
		args     args
		wantErr  bool
	}{
		{
			name: "正常",
			initFunc: func(m *mgrHelperForTest) {
				//tsNow := time.Now()
				//start := time.Date(tsNow.Year(), tsNow.Month(), tsNow.Day(), 0, 0, 0, 0, tsNow.Location())
				//end := time.Date(tsNow.Year(), tsNow.Month(), tsNow.Day(), 23, 59, 59, 0, tsNow.Location())
				roleType := []uint32{uint32(esport_role.ESportErType_ESPORT_TYPE_PERSONAL), uint32(esport_role.ESportErType_ESPORT_TYPE_GUILD)}
				m.getStore().EXPECT().GetESportRoleInfosByUids(gomock.Any(), []uint32{1, 2}, roleType, gomock.Any(), uint32(100), uint32(0)).Return(
					[]*store.ESportCoachInfo{
						&store.ESportCoachInfo{
							Uid:        1,
							ESportType: 1,
						},
						&store.ESportCoachInfo{
							Uid:        2,
							ESportType: 2,
						},
					},
					nil)
				m.getStore().EXPECT().BatchGetUserLabelDuration(gomock.Any(), []uint32{1, 2}).Return(
					nil, nil)

				m.getStore().EXPECT().BatchGrantCoachLabel(gomock.Any(), []uint32{1, 2}, "http://www.baidu.com",
					uint32(1), int64(100), int64(200)).Return(nil)
				m.getAccountCli().EXPECT().GetUsersMap(gomock.Any(), []uint32{1, 2}).Return(
					map[uint32]*account.User{
						1: &account.User{
							Uid:      1,
							Alias:    "test1",
							Username: "test1",
						},
						2: &account.User{
							Uid:      2,
							Alias:    "test2",
							Username: "test2",
						},
					}, nil)
				m.getCache().EXPECT().BatchDelCoachLabelCache(gomock.Any(), []uint32{1, 2}).Return(nil)

			},
			args: args{
				c: context.Background(),
				request: &esport_role.BatchGrantCoachLabelRequest{
					Uid: []uint32{1, 2},
					Label: &esport_role.CoachLabel{
						Type:      esport_role.LabelSourceType_LABEL_SOURCE_TYPE_PNG,
						SourceUrl: "http://www.baidu.com",
					},
					BeginTime: 100,
					EndTime:   200,
				},
			},
			wantErr: false,
		},
		{
			name: "checkIfRole exist error",
			initFunc: func(m *mgrHelperForTest) {
				roleType := []uint32{uint32(esport_role.ESportErType_ESPORT_TYPE_PERSONAL), uint32(esport_role.ESportErType_ESPORT_TYPE_GUILD)}
				m.getStore().EXPECT().GetESportRoleInfosByUids(gomock.Any(), []uint32{1, 2}, roleType, gomock.Any(), uint32(100), uint32(0)).Return(
					nil, errors.New("error"))
			},
			args: args{
				c: context.Background(),
				request: &esport_role.BatchGrantCoachLabelRequest{
					Uid: []uint32{1, 2},
					Label: &esport_role.CoachLabel{
						Type:      esport_role.LabelSourceType_LABEL_SOURCE_TYPE_PNG,
						SourceUrl: "http://www.baidu.com",
					},
					BeginTime: 100,
					EndTime:   200,
				},
			},
			wantErr: true,
		},
		{
			name: "BatchGetUserLabelDuration error",
			initFunc: func(m *mgrHelperForTest) {
				roleType := []uint32{uint32(esport_role.ESportErType_ESPORT_TYPE_PERSONAL), uint32(esport_role.ESportErType_ESPORT_TYPE_GUILD)}
				m.getStore().EXPECT().GetESportRoleInfosByUids(gomock.Any(), []uint32{1, 2}, roleType, gomock.Any(), uint32(100), uint32(0)).Return(
					[]*store.ESportCoachInfo{
						&store.ESportCoachInfo{
							Uid:        1,
							ESportType: 1,
						},
						&store.ESportCoachInfo{
							Uid:        2,
							ESportType: 2,
						},
					},
					nil)
				m.getAccountCli().EXPECT().GetUsersMap(gomock.Any(), []uint32{1, 2}).Return(
					map[uint32]*account.User{
						1: &account.User{
							Uid:   1,
							Alias: "test1",
						},
						2: &account.User{
							Uid:   2,
							Alias: "test2",
						},
					}, nil)

				m.getStore().EXPECT().BatchGetUserLabelDuration(gomock.Any(), []uint32{1, 2}).Return(
					nil, errors.New("error"))
			},
			args: args{
				c: context.Background(),
				request: &esport_role.BatchGrantCoachLabelRequest{
					Uid: []uint32{1, 2},
					Label: &esport_role.CoachLabel{
						Type:      esport_role.LabelSourceType_LABEL_SOURCE_TYPE_PNG,
						SourceUrl: "http://www.baidu.com",
					},
					BeginTime: 100,
					EndTime:   200,
				},
			},
			wantErr: true,
		},
		{
			name: "BatchGrantCoachLabel error",
			initFunc: func(m *mgrHelperForTest) {
				roleType := []uint32{uint32(esport_role.ESportErType_ESPORT_TYPE_PERSONAL), uint32(esport_role.ESportErType_ESPORT_TYPE_GUILD)}
				m.getStore().EXPECT().GetESportRoleInfosByUids(gomock.Any(), []uint32{1, 2}, roleType, gomock.Any(), uint32(100), uint32(0)).Return(
					[]*store.ESportCoachInfo{
						&store.ESportCoachInfo{
							Uid:        1,
							ESportType: 1,
						},
						&store.ESportCoachInfo{
							Uid:        2,
							ESportType: 2,
						},
					},
					nil)
				m.getStore().EXPECT().BatchGetUserLabelDuration(gomock.Any(), []uint32{1, 2}).Return(
					nil, nil)

				m.getStore().EXPECT().BatchGrantCoachLabel(gomock.Any(), []uint32{1, 2}, "http://www.baidu.com",
					uint32(1), int64(100), int64(200)).Return(errors.New("error"))
				m.getAccountCli().EXPECT().GetUsersMap(gomock.Any(), []uint32{1, 2}).Return(
					map[uint32]*account.User{
						1: &account.User{
							Uid:      1,
							Alias:    "test1",
							Username: "test1",
						},
						2: &account.User{
							Uid:      2,
							Alias:    "test2",
							Username: "test2",
						},
					}, nil)
			},
			args: args{
				c: context.Background(),
				request: &esport_role.BatchGrantCoachLabelRequest{
					Uid: []uint32{1, 2},
					Label: &esport_role.CoachLabel{
						Type:      esport_role.LabelSourceType_LABEL_SOURCE_TYPE_PNG,
						SourceUrl: "http://www.baidu.com",
					},
					BeginTime: 100,
					EndTime:   200,
				},
			},
			wantErr: true,
		},
		{
			name: "GetUsersMap error",
			initFunc: func(m *mgrHelperForTest) {
				roleType := []uint32{uint32(esport_role.ESportErType_ESPORT_TYPE_PERSONAL), uint32(esport_role.ESportErType_ESPORT_TYPE_GUILD)}
				m.getStore().EXPECT().GetESportRoleInfosByUids(gomock.Any(), []uint32{1, 2}, roleType, gomock.Any(), uint32(100), uint32(0)).Return(
					[]*store.ESportCoachInfo{
						&store.ESportCoachInfo{
							Uid:        1,
							ESportType: 1,
						},
						&store.ESportCoachInfo{
							Uid:        2,
							ESportType: 2,
						},
					},
					nil)
				m.getStore().EXPECT().BatchGetUserLabelDuration(gomock.Any(), []uint32{1, 2}).Return(
					nil, nil)

				m.getStore().EXPECT().BatchGrantCoachLabel(gomock.Any(), []uint32{1, 2}, "http://www.baidu.com",
					uint32(1), int64(100), int64(200)).Return(nil)
				m.getAccountCli().EXPECT().GetUsersMap(gomock.Any(), []uint32{1, 2}).Return(
					nil, protocol.NewExactServerError(codes.OK, -2))
			},
			args: args{
				c: context.Background(),
				request: &esport_role.BatchGrantCoachLabelRequest{
					Uid: []uint32{1, 2},
					Label: &esport_role.CoachLabel{
						Type:      esport_role.LabelSourceType_LABEL_SOURCE_TYPE_PNG,
						SourceUrl: "http://www.baidu.com",
					},
					BeginTime: 100,
					EndTime:   200,
				},
			},
			wantErr: true,
		},
		{
			name: "存在用户不是电竞指导",
			initFunc: func(m *mgrHelperForTest) {
				roleType := []uint32{uint32(esport_role.ESportErType_ESPORT_TYPE_PERSONAL), uint32(esport_role.ESportErType_ESPORT_TYPE_GUILD)}
				m.getStore().EXPECT().GetESportRoleInfosByUids(gomock.Any(), []uint32{1, 2}, roleType, gomock.Any(), uint32(100), uint32(0)).Return(
					[]*store.ESportCoachInfo{
						&store.ESportCoachInfo{
							Uid:        1,
							ESportType: 1,
						},
					}, nil)
				m.getAccountCli().EXPECT().GetUsersMap(gomock.Any(), []uint32{1, 2}).Return(
					map[uint32]*account.User{
						1: &account.User{
							Uid:   1,
							Alias: "test1",
						},
						2: &account.User{
							Uid:   2,
							Alias: "test2",
						},
					}, nil)
			},
			args: args{
				c: context.Background(),
				request: &esport_role.BatchGrantCoachLabelRequest{
					Uid: []uint32{1, 2},
				},
			},
			wantErr: true,
		},
		{
			name: "时间存在冲突",
			initFunc: func(m *mgrHelperForTest) {

				roleType := []uint32{uint32(esport_role.ESportErType_ESPORT_TYPE_PERSONAL), uint32(esport_role.ESportErType_ESPORT_TYPE_GUILD)}
				m.getStore().EXPECT().GetESportRoleInfosByUids(gomock.Any(), []uint32{1, 2}, roleType, gomock.Any(), uint32(100), uint32(0)).Return(
					[]*store.ESportCoachInfo{
						&store.ESportCoachInfo{
							Uid:        1,
							ESportType: 1,
						},
						&store.ESportCoachInfo{
							Uid:        2,
							ESportType: 2,
						},
					},
					nil)
				m.getAccountCli().EXPECT().GetUsersMap(gomock.Any(), []uint32{1, 2}).Return(
					map[uint32]*account.User{
						1: &account.User{
							Uid:   1,
							Alias: "test1",
						},
						2: &account.User{
							Uid:   2,
							Alias: "test2",
						},
					}, nil)

				m.getStore().EXPECT().BatchGetUserLabelDuration(gomock.Any(), []uint32{1, 2}).Return(
					map[uint32][]*store.TimeDuration{
						1: {
							&store.TimeDuration{
								BeginTime: start,
								EndTime:   end,
							},
						},
						2: {
							&store.TimeDuration{
								BeginTime: start,
								EndTime:   end,
							},
						},
					}, nil)
			},
			args: args{
				c: context.Background(),
				request: &esport_role.BatchGrantCoachLabelRequest{
					Uid: []uint32{1, 2},
					Label: &esport_role.CoachLabel{
						Type:      esport_role.LabelSourceType_LABEL_SOURCE_TYPE_PNG,
						SourceUrl: "http://www.baidu.com",
					},
					BeginTime: start.Unix(),
					EndTime:   end.Unix(),
				},
			},
			wantErr: true,
		},
		{
			name: "用户不存在",
			initFunc: func(m *mgrHelperForTest) {
				roleType := []uint32{uint32(esport_role.ESportErType_ESPORT_TYPE_PERSONAL), uint32(esport_role.ESportErType_ESPORT_TYPE_GUILD)}
				m.getStore().EXPECT().GetESportRoleInfosByUids(gomock.Any(), []uint32{1, 2}, roleType, gomock.Any(), uint32(100), uint32(0)).Return(
					[]*store.ESportCoachInfo{
						&store.ESportCoachInfo{
							Uid:        1,
							ESportType: 1,
						},
					}, nil)
				m.getAccountCli().EXPECT().GetUsersMap(gomock.Any(), []uint32{1, 2}).Return(
					map[uint32]*account.User{
						1: &account.User{
							Uid:   1,
							Alias: "test1",
						},
					}, nil)
			},
			args: args{
				c: context.Background(),
				request: &esport_role.BatchGrantCoachLabelRequest{
					Uid: []uint32{1, 2},
				},
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := newMgrHelperForTest(t)
			if tt.initFunc != nil {
				tt.initFunc(m)
			}
			if err := m.BatchGrantCoachLabel(tt.args.c, tt.args.request); (err != nil) != tt.wantErr {
				t.Errorf("BatchGrantCoachLabel() error = %v, wantErr %v", err, tt.wantErr)
			}
			time.Sleep(time.Millisecond * 200)
		})
	}
}

func TestMgr_RecallCoachLabel(t *testing.T) {
	type args struct {
		c       context.Context
		request *esport_role.RecallCoachLabelRequest
	}
	tests := []struct {
		name     string
		initFunc func(m *mgrHelperForTest)
		args     args
		wantErr  bool
	}{
		{
			name: "正常",
			initFunc: func(m *mgrHelperForTest) {
				m.getStore().EXPECT().RecallCoachLabel(gomock.Any(), uint32(1)).Return(nil)
				m.getCache().EXPECT().BatchDelCoachLabelCache(gomock.Any(), []uint32{1}).Return(nil)
				m.getStore().EXPECT().GetCoachLabelByRecordId(gomock.Any(), uint32(1)).Return(uint32(1), &esport_role.CoachLabel{}, nil)
			},
			args: args{
				c: context.Background(),
				request: &esport_role.RecallCoachLabelRequest{
					RecordId: 1,
				},
			},
			wantErr: false,
		},
		{
			name: "RecallCoachLabel error",
			initFunc: func(m *mgrHelperForTest) {
				m.getStore().EXPECT().RecallCoachLabel(gomock.Any(), uint32(1)).Return(errors.New("error"))
			},
			args: args{
				c: context.Background(),
				request: &esport_role.RecallCoachLabelRequest{
					RecordId: 1,
				},
			},
			wantErr: true,
		},
		{
			name: "GetCoachLabelByRecordId error",
			initFunc: func(m *mgrHelperForTest) {
				m.getStore().EXPECT().RecallCoachLabel(gomock.Any(), uint32(1)).Return(nil)
				m.getStore().EXPECT().GetCoachLabelByRecordId(gomock.Any(), uint32(1)).Return(uint32(1), &esport_role.CoachLabel{}, errors.New("error"))
			},
			args: args{
				c: context.Background(),
				request: &esport_role.RecallCoachLabelRequest{
					RecordId: 1,
				},
			},
			wantErr: false,
		},
		{
			name: "BatchDelCoachLabelCache error",
			initFunc: func(m *mgrHelperForTest) {
				m.getStore().EXPECT().RecallCoachLabel(gomock.Any(), uint32(1)).Return(nil)
				m.getCache().EXPECT().BatchDelCoachLabelCache(gomock.Any(), []uint32{1}).Return(errors.New("error"))
				m.getStore().EXPECT().GetCoachLabelByRecordId(gomock.Any(), uint32(1)).Return(uint32(1), &esport_role.CoachLabel{}, nil)
			},
			args: args{
				c: context.Background(),
				request: &esport_role.RecallCoachLabelRequest{
					RecordId: 1,
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := newMgrHelperForTest(t)
			if tt.initFunc != nil {
				tt.initFunc(m)
			}
			if err := m.RecallCoachLabel(tt.args.c, tt.args.request); (err != nil) != tt.wantErr {
				t.Errorf("RecallCoachLabel() error = %v, wantErr %v", err, tt.wantErr)
			}
			time.Sleep(time.Millisecond * 200)
		})
	}
}

func TestMgr_GetCoachLabelRecordByPage(t *testing.T) {
	type args struct {
		c       context.Context
		request *esport_role.GetCoachLabelRecordByPageRequest
	}
	tests := []struct {
		name     string
		initFunc func(m *mgrHelperForTest)
		args     args
		wantErr  bool
	}{
		{
			name: "正常",
			initFunc: func(m *mgrHelperForTest) {
				m.getStore().EXPECT().GetCoachLabelRecordByPage(gomock.Any(), uint32(1), esport_role.CoachLabelStatus_COACH_LABEL_STATUS_ALL, uint32(0), uint32(10)).Return(
					&esport_role.GetCoachLabelRecordByPageResponse{
						RecordList: []*esport_role.GetCoachLabelRecordByPageResponse_CoachLabelRecord{
							&esport_role.GetCoachLabelRecordByPageResponse_CoachLabelRecord{
								RecordId: 1,
								Label: &esport_role.CoachLabel{
									Type:      esport_role.LabelSourceType_LABEL_SOURCE_TYPE_PNG,
									SourceUrl: "http://www.baidu.com",
								},
								Uid: 1,
							},
						},
					}, nil)
				m.getAccountCli().EXPECT().GetUsersMap(gomock.Any(), []uint32{1}).Return(
					map[uint32]*account.User{
						1: &account.User{
							Uid:      1,
							Alias:    "test1",
							Nickname: "test1",
						},
					}, nil)
				m.getStore().EXPECT().GetCoachLabelRecordCnt(gomock.Any(), uint32(1), esport_role.CoachLabelStatus_COACH_LABEL_STATUS_ALL).Return(uint32(1), nil)
			},
			args: args{
				c: context.Background(),
				request: &esport_role.GetCoachLabelRecordByPageRequest{
					Uid:      1,
					Status:   esport_role.CoachLabelStatus_COACH_LABEL_STATUS_ALL,
					PageSize: 10,
				},
			},
			wantErr: false,
		},
		{
			name: "GetCoachLabelRecordByPage error",
			initFunc: func(m *mgrHelperForTest) {
				m.getStore().EXPECT().GetCoachLabelRecordByPage(gomock.Any(), uint32(1), esport_role.CoachLabelStatus_COACH_LABEL_STATUS_ALL, uint32(0), uint32(10)).Return(
					nil, errors.New("error"))
			},
			args: args{
				c: context.Background(),
				request: &esport_role.GetCoachLabelRecordByPageRequest{
					Uid:      1,
					Status:   esport_role.CoachLabelStatus_COACH_LABEL_STATUS_ALL,
					PageSize: 10,
				},
			},
			wantErr: true,
		},
		{
			name: "GetUsersMap error",
			initFunc: func(m *mgrHelperForTest) {
				m.getStore().EXPECT().GetCoachLabelRecordByPage(gomock.Any(), uint32(1), esport_role.CoachLabelStatus_COACH_LABEL_STATUS_ALL, uint32(0), uint32(10)).Return(
					&esport_role.GetCoachLabelRecordByPageResponse{
						RecordList: []*esport_role.GetCoachLabelRecordByPageResponse_CoachLabelRecord{
							&esport_role.GetCoachLabelRecordByPageResponse_CoachLabelRecord{
								RecordId: 1,
								Label: &esport_role.CoachLabel{
									Type:      esport_role.LabelSourceType_LABEL_SOURCE_TYPE_PNG,
									SourceUrl: "http://www.baidu.com",
								},
								Uid: 1,
							},
						},
					}, nil)

				m.getStore().EXPECT().GetCoachLabelRecordCnt(gomock.Any(), uint32(1), esport_role.CoachLabelStatus_COACH_LABEL_STATUS_ALL).Return(uint32(1), nil)
				m.getAccountCli().EXPECT().GetUsersMap(gomock.Any(), []uint32{1}).Return(
					nil, protocol.NewExactServerError(codes.OK, -2))
			},
			args: args{
				c: context.Background(),
				request: &esport_role.GetCoachLabelRecordByPageRequest{
					Uid:      1,
					Status:   esport_role.CoachLabelStatus_COACH_LABEL_STATUS_ALL,
					PageSize: 10,
				},
			},
			wantErr: true,
		},
		{
			name: "GetCoachLabelRecordCnt error",
			initFunc: func(m *mgrHelperForTest) {
				m.getStore().EXPECT().GetCoachLabelRecordByPage(gomock.Any(), uint32(1), esport_role.CoachLabelStatus_COACH_LABEL_STATUS_ALL, uint32(0), uint32(10)).Return(
					&esport_role.GetCoachLabelRecordByPageResponse{
						RecordList: []*esport_role.GetCoachLabelRecordByPageResponse_CoachLabelRecord{
							&esport_role.GetCoachLabelRecordByPageResponse_CoachLabelRecord{
								RecordId: 1,
								Label: &esport_role.CoachLabel{
									Type:      esport_role.LabelSourceType_LABEL_SOURCE_TYPE_PNG,
									SourceUrl: "http://www.baidu.com",
								},
								Uid: 1,
							},
						},
					}, nil)
				m.getAccountCli().EXPECT().GetUsersMap(gomock.Any(), []uint32{1}).Return(
					map[uint32]*account.User{
						1: &account.User{
							Uid:      1,
							Alias:    "test1",
							Nickname: "test1",
						},
					}, nil)
				m.getStore().EXPECT().GetCoachLabelRecordCnt(gomock.Any(), uint32(1), esport_role.CoachLabelStatus_COACH_LABEL_STATUS_ALL).Return(uint32(1), errors.New("error"))
			},
			args: args{
				c: context.Background(),
				request: &esport_role.GetCoachLabelRecordByPageRequest{
					Uid:      1,
					Status:   esport_role.CoachLabelStatus_COACH_LABEL_STATUS_ALL,
					PageSize: 10,
				},
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := newMgrHelperForTest(t)
			if tt.initFunc != nil {
				tt.initFunc(m)
			}
			_, err := m.GetCoachLabelRecordByPage(tt.args.c, tt.args.request)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetCoachLabelRecordByPage() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestMgr_UpdateCoachLabel(t *testing.T) {

	type args struct {
		c       context.Context
		request *esport_role.UpdateCoachLabelRequest
	}
	tests := []struct {
		name     string
		args     args
		initFunc func(m *mgrHelperForTest)
		wantErr  bool
	}{
		{
			name: "正常",
			initFunc: func(m *mgrHelperForTest) {
				m.getStore().EXPECT().UpdateCoachLabel(gomock.Any(), uint32(1), "http://baidu.com", uint32(1)).Return(nil)
				m.getStore().EXPECT().GetCoachLabelByRecordId(gomock.Any(), uint32(1)).Return(uint32(1), &esport_role.CoachLabel{}, nil)
				m.getCache().EXPECT().BatchDelCoachLabelCache(gomock.Any(), []uint32{1}).Return(nil)
			},
			args: args{
				c: context.Background(),
				request: &esport_role.UpdateCoachLabelRequest{
					RecordId: 1,
					Label: &esport_role.CoachLabel{
						Type:      esport_role.LabelSourceType_LABEL_SOURCE_TYPE_PNG,
						SourceUrl: "http://baidu.com",
					},
				},
			},
			wantErr: false,
		},
		{
			name: "UpdateCoachLabel error",
			initFunc: func(m *mgrHelperForTest) {
				m.getStore().EXPECT().UpdateCoachLabel(gomock.Any(), uint32(1), "http://baidu.com", uint32(1)).Return(errors.New("error"))
			},
			args: args{
				c: context.Background(),
				request: &esport_role.UpdateCoachLabelRequest{
					RecordId: 1,
					Label: &esport_role.CoachLabel{
						Type:      esport_role.LabelSourceType_LABEL_SOURCE_TYPE_PNG,
						SourceUrl: "http://baidu.com",
					},
				},
			},
			wantErr: true,
		},
		{
			name: "GetCoachLabelByRecordId error",
			initFunc: func(m *mgrHelperForTest) {
				m.getStore().EXPECT().UpdateCoachLabel(gomock.Any(), uint32(1), "http://baidu.com", uint32(1)).Return(nil)
				m.getStore().EXPECT().GetCoachLabelByRecordId(gomock.Any(), uint32(1)).Return(uint32(1), &esport_role.CoachLabel{}, errors.New("error"))
			},
			args: args{
				c: context.Background(),
				request: &esport_role.UpdateCoachLabelRequest{
					RecordId: 1,
					Label: &esport_role.CoachLabel{
						Type:      esport_role.LabelSourceType_LABEL_SOURCE_TYPE_PNG,
						SourceUrl: "http://baidu.com",
					},
				},
			},
			wantErr: false,
		},
		{
			name: "BatchDelCoachLabelCache error",
			initFunc: func(m *mgrHelperForTest) {
				m.getStore().EXPECT().UpdateCoachLabel(gomock.Any(), uint32(1), "http://baidu.com", uint32(1)).Return(nil)
				m.getStore().EXPECT().GetCoachLabelByRecordId(gomock.Any(), uint32(1)).Return(uint32(1), &esport_role.CoachLabel{}, nil)
				m.getCache().EXPECT().BatchDelCoachLabelCache(gomock.Any(), []uint32{1}).Return(errors.New("error"))
			},
			args: args{
				c: context.Background(),
				request: &esport_role.UpdateCoachLabelRequest{
					RecordId: 1,
					Label: &esport_role.CoachLabel{
						Type:      esport_role.LabelSourceType_LABEL_SOURCE_TYPE_PNG,
						SourceUrl: "http://baidu.com",
					},
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := newMgrHelperForTest(t)
			if tt.initFunc != nil {
				tt.initFunc(m)
			}
			if err := m.UpdateCoachLabel(tt.args.c, tt.args.request); (err != nil) != tt.wantErr {
				t.Errorf("UpdateCoachLabel() error = %v, wantErr %v", err, tt.wantErr)
			}
			time.Sleep(time.Millisecond * 200)
		})
	}
}

func TestMgr_BatchGetCoachInfoByTTid(t *testing.T) {

	type args struct {
		c        context.Context
		ttidList []string
	}
	tests := []struct {
		name     string
		args     args
		initFunc func(m *mgrHelperForTest)
		want     *esport_role.BatchGetCoachInfoByTTidResponse
		wantErr  bool
	}{
		{
			name: "ttidList长度大于100",
			args: args{
				c:        context.Background(),
				ttidList: make([]string, 101),
			},
			want:    &esport_role.BatchGetCoachInfoByTTidResponse{},
			wantErr: true,
		},
		{
			name: "ttidList为空",
			args: args{
				c:        context.Background(),
				ttidList: []string{},
			},
			want:    &esport_role.BatchGetCoachInfoByTTidResponse{},
			wantErr: true,
		},

		{
			name: "BatchQueryUidListByAlias error",
			initFunc: func(m *mgrHelperForTest) {
				m.getAccountCli().EXPECT().BatchQueryUidListByAlias(gomock.Any(), []string{"1", "2"}).Return(nil,
					protocol.NewExactServerError(codes.OK, -2))
			},
			args: args{
				c:        context.Background(),
				ttidList: []string{"1", "2"},
			},
			want:    &esport_role.BatchGetCoachInfoByTTidResponse{},
			wantErr: true,
		},
		{
			name: "正常",
			initFunc: func(m *mgrHelperForTest) {
				m.getAccountCli().EXPECT().BatchQueryUidListByAlias(gomock.Any(), []string{"1", "2"}).Return(
					[]*accountPb.KeyToUidPair{
						&accountPb.KeyToUidPair{
							Uid: 1,
							Key: "1",
						},
						&accountPb.KeyToUidPair{
							Key: "2",
							Uid: 2,
						},
					}, nil)

				m.getAccountCli().EXPECT().GetUsersMap(gomock.Any(), []uint32{1, 2}).Return(
					map[uint32]*account.User{
						1: &account.User{
							Uid:      1,
							Alias:    "test1",
							Nickname: "test1",
						},
						2: &account.User{
							Uid:      2,
							Alias:    "test2",
							Nickname: "test2",
						},
					}, nil)

				m.getStore().EXPECT().GetESportRoleInfosByUids(gomock.Any(), []uint32{1, 2},
					[]uint32{uint32(esport_role.ESportErType_ESPORT_TYPE_PERSONAL),
						uint32(esport_role.ESportErType_ESPORT_TYPE_GUILD)},
					gomock.Any(), uint32(100), uint32(0)).Return(
					[]*store.ESportCoachInfo{
						&store.ESportCoachInfo{
							Uid:        1,
							ESportType: 1,
						},
						&store.ESportCoachInfo{
							Uid:        2,
							ESportType: 2,
						},
					}, nil)
			},
			args: args{
				c:        context.Background(),
				ttidList: []string{"1", "2"},
			},
			want: &esport_role.BatchGetCoachInfoByTTidResponse{
				InvalidIdList: make([]string, 0),
				NotCoachList:  make([]string, 0),
				InfoMap: map[string]*esport_role.BatchGetCoachInfoByTTidResponse_CoachInfo{
					"test1": &esport_role.BatchGetCoachInfoByTTidResponse_CoachInfo{
						EsportRole: 1,
						NickName:   "test1",
					},
					"test2": &esport_role.BatchGetCoachInfoByTTidResponse_CoachInfo{
						EsportRole: 2,
						NickName:   "test2",
					},
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := newMgrHelperForTest(t)
			if tt.initFunc != nil {
				tt.initFunc(m)
			}
			got, err := m.BatchGetCoachInfoByTTid(tt.args.c, tt.args.ttidList)
			if (err != nil) != tt.wantErr {
				t.Errorf("BatchGetCoachInfoByTTid() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("BatchGetCoachInfoByTTid() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestMgr_CheckTimeOverlapped(t *testing.T) {
	// 用于测试时间冲突
	tsNow := time.Now()
	start := time.Date(tsNow.Year(), tsNow.Month(), tsNow.Day(), 0, 0, 0, 0, tsNow.Location())
	end := time.Date(tsNow.Year(), tsNow.Month(), tsNow.Day(), 23, 59, 59, 0, tsNow.Location())

	type args struct {
		c       context.Context
		request *esport_role.CheckTimeOverlappedRequest
	}
	tests := []struct {
		name     string
		args     args
		initFunc func(m *mgrHelperForTest)
		want     *esport_role.CheckTimeOverlappedResponse
		wantErr  bool
	}{
		{
			name: "BatchGetUserLabelDuration error",
			initFunc: func(m *mgrHelperForTest) {
				m.getStore().EXPECT().BatchGetUserLabelDuration(gomock.Any(), []uint32{1, 2}).Return(
					nil, errors.New("error"))
			},
			args: args{
				c: context.Background(),
				request: &esport_role.CheckTimeOverlappedRequest{
					UidList:   []uint32{1, 2},
					BeginTime: 100,
					EndTime:   200,
				},
			},
			wantErr: true,
			want:    &esport_role.CheckTimeOverlappedResponse{},
		},
		{
			name: "GetUsersMap error",
			initFunc: func(m *mgrHelperForTest) {
				m.getStore().EXPECT().BatchGetUserLabelDuration(gomock.Any(), []uint32{1, 2}).Return(
					map[uint32][]*store.TimeDuration{
						1: {
							&store.TimeDuration{
								BeginTime: start,
								EndTime:   end,
							},
						},
						2: {
							&store.TimeDuration{
								BeginTime: start,
								EndTime:   end,
							},
						},
					}, nil)
				m.getAccountCli().EXPECT().GetUsersMap(gomock.Any(), gomock.Any()).Return(
					nil, protocol.NewExactServerError(codes.OK, -2))
			},
			args: args{
				c: context.Background(),
				request: &esport_role.CheckTimeOverlappedRequest{
					UidList:   []uint32{1, 2},
					BeginTime: start.Unix(),
					EndTime:   end.Unix(),
				},
			},
			wantErr: true,
			want:    &esport_role.CheckTimeOverlappedResponse{},
		},
		{
			name: "正常",
			initFunc: func(m *mgrHelperForTest) {
				m.getStore().EXPECT().BatchGetUserLabelDuration(gomock.Any(), []uint32{1, 2}).Return(
					map[uint32][]*store.TimeDuration{
						1: {
							&store.TimeDuration{
								BeginTime: start,
								EndTime:   end,
							},
						},
						2: {
							&store.TimeDuration{
								BeginTime: start,
								EndTime:   end,
							},
						},
					}, nil)
				m.getAccountCli().EXPECT().GetUsersMap(gomock.Any(), gomock.Any()).Return(
					map[uint32]*account.User{
						1: &account.User{
							Uid:   1,
							Alias: "test1",
						},
						2: &account.User{
							Uid:   2,
							Alias: "test2",
						},
					}, nil)
			},
			args: args{
				c: context.Background(),
				request: &esport_role.CheckTimeOverlappedRequest{
					UidList:   []uint32{1, 2},
					BeginTime: start.Unix(),
					EndTime:   end.Unix(),
				},
			},
			wantErr: false,
			want: &esport_role.CheckTimeOverlappedResponse{
				ItemList: []*esport_role.CheckTimeOverlappedResponse_OverlappedItem{

					&esport_role.CheckTimeOverlappedResponse_OverlappedItem{
						Ttid:      "test1",
						BeginTime: start.Unix(),
						EndTime:   end.Unix(),
					},
					&esport_role.CheckTimeOverlappedResponse_OverlappedItem{
						Ttid:      "test2",
						BeginTime: start.Unix(),
						EndTime:   end.Unix(),
					},
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := newMgrHelperForTest(t)
			if tt.initFunc != nil {
				tt.initFunc(m)
			}
			got, err := m.CheckTimeOverlapped(tt.args.c, tt.args.request)
			if (err != nil) != tt.wantErr {
				t.Errorf("CheckTimeOverlapped() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			// 对列表进行排序
			sort.Slice(got.ItemList, func(i, j int) bool { return got.ItemList[i].Ttid < got.ItemList[j].Ttid })
			sort.Slice(tt.want.ItemList, func(i, j int) bool { return tt.want.ItemList[i].Ttid < tt.want.ItemList[j].Ttid })

			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("CheckTimeOverlapped() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestMgr_BatchGetCoachLabel(t *testing.T) {
	commonResp := &esport_role.BatchGetCoachLabelResponse{
		CoachLabelMap: map[uint32]*esport_role.CoachLabel{
			1: &esport_role.CoachLabel{
				Type:      esport_role.LabelSourceType_LABEL_SOURCE_TYPE_PNG,
				SourceUrl: "http://www.baidu.com",
			},
			2: &esport_role.CoachLabel{
				Type:      esport_role.LabelSourceType_LABEL_SOURCE_TYPE_PNG,
				SourceUrl: "http://www.baidu.com",
			},
		},
	}
	type args struct {
		c       context.Context
		request *esport_role.BatchGetCoachLabelRequest
	}
	tests := []struct {
		name     string
		initFunc func(m *mgrHelperForTest)
		args     args
		want     *esport_role.BatchGetCoachLabelResponse
		wantErr  bool
	}{
		{
			name: "全部从cache中获取",
			initFunc: func(m *mgrHelperForTest) {
				m.getCache().EXPECT().BatchGetCoachLabel(gomock.Any(), []uint32{1, 2}).Return(
					map[uint32]*esport_role.CoachLabel{
						1: &esport_role.CoachLabel{
							Type:      esport_role.LabelSourceType_LABEL_SOURCE_TYPE_PNG,
							SourceUrl: "http://www.baidu.com",
						},
						2: &esport_role.CoachLabel{
							Type:      esport_role.LabelSourceType_LABEL_SOURCE_TYPE_PNG,
							SourceUrl: "http://www.baidu.com",
						},
					}, nil)
			},
			args: args{
				c: context.Background(),
				request: &esport_role.BatchGetCoachLabelRequest{
					UidList: []uint32{1, 2},
				},
			},
			want:    commonResp,
			wantErr: false,
		},
		{
			name: "从cache中获取error",
			initFunc: func(m *mgrHelperForTest) {
				m.getCache().EXPECT().BatchGetCoachLabel(gomock.Any(), []uint32{1, 2}).Return(nil, errors.New("error"))
				m.getStore().EXPECT().BatchGetCoachLabel(gomock.Any(), []uint32{1, 2}).Return(
					map[uint32]*esport_role.CoachLabel{
						1: &esport_role.CoachLabel{
							Type:      esport_role.LabelSourceType_LABEL_SOURCE_TYPE_PNG,
							SourceUrl: "http://www.baidu.com",
						},
						2: &esport_role.CoachLabel{
							Type:      esport_role.LabelSourceType_LABEL_SOURCE_TYPE_PNG,
							SourceUrl: "http://www.baidu.com",
						},
					}, nil)
				m.getCache().EXPECT().BatchInsertCoachLabel(gomock.Any(), map[uint32]*esport_role.CoachLabel{
					1: &esport_role.CoachLabel{
						Type:      esport_role.LabelSourceType_LABEL_SOURCE_TYPE_PNG,
						SourceUrl: "http://www.baidu.com",
					},
					2: &esport_role.CoachLabel{
						Type:      esport_role.LabelSourceType_LABEL_SOURCE_TYPE_PNG,
						SourceUrl: "http://www.baidu.com",
					},
				}).Return(nil)
			},
			args: args{
				c: context.Background(),
				request: &esport_role.BatchGetCoachLabelRequest{
					UidList: []uint32{1, 2},
				},
			},
			want:    commonResp,
			wantErr: false,
		},
		{
			name: "部分从cache中获取",
			initFunc: func(m *mgrHelperForTest) {
				m.getCache().EXPECT().BatchGetCoachLabel(gomock.Any(), []uint32{1, 2}).Return(
					map[uint32]*esport_role.CoachLabel{
						1: &esport_role.CoachLabel{
							Type:      esport_role.LabelSourceType_LABEL_SOURCE_TYPE_PNG,
							SourceUrl: "http://www.baidu.com",
						},
					}, nil)
				m.getStore().EXPECT().BatchGetCoachLabel(gomock.Any(), []uint32{2}).Return(
					map[uint32]*esport_role.CoachLabel{
						2: &esport_role.CoachLabel{
							Type:      esport_role.LabelSourceType_LABEL_SOURCE_TYPE_PNG,
							SourceUrl: "http://www.baidu.com",
						},
					}, nil)
				m.getCache().EXPECT().BatchInsertCoachLabel(gomock.Any(), map[uint32]*esport_role.CoachLabel{
					2: &esport_role.CoachLabel{
						Type:      esport_role.LabelSourceType_LABEL_SOURCE_TYPE_PNG,
						SourceUrl: "http://www.baidu.com",
					},
				}).Return(nil)
			},
			args: args{
				c: context.Background(),
				request: &esport_role.BatchGetCoachLabelRequest{
					UidList: []uint32{1, 2},
				},
			},
			want: commonResp,
		},
		{
			name: "从store获取error",
			initFunc: func(m *mgrHelperForTest) {
				m.getCache().EXPECT().BatchGetCoachLabel(gomock.Any(), []uint32{1, 2}).Return(nil, errors.New("error"))
				m.getStore().EXPECT().BatchGetCoachLabel(gomock.Any(), []uint32{1, 2}).Return(nil, errors.New("error"))
			},
			args: args{
				c: context.Background(),
				request: &esport_role.BatchGetCoachLabelRequest{
					UidList: []uint32{1, 2},
				},
			},
			want:    &esport_role.BatchGetCoachLabelResponse{CoachLabelMap: make(map[uint32]*esport_role.CoachLabel)},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := newMgrHelperForTest(t)
			if tt.initFunc != nil {
				tt.initFunc(m)
			}
			got, err := m.BatchGetCoachLabel(tt.args.c, tt.args.request)
			if (err != nil) != tt.wantErr {
				t.Errorf("BatchGetCoachLabel() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("BatchGetCoachLabel() got = %v, want %v", got, tt.want)
			}
			time.Sleep(time.Millisecond * 200)
		})
	}
}
