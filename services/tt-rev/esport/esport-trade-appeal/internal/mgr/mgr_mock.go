// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/services/tt-rev/esport/esport-trade-appeal/internal/mgr (interfaces: Mgr,Transaction,IM)

// Package mgr is a generated GoMock package.
package mgr

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	esport_trade "golang.52tt.com/protocol/services/esport-trade"
	esport_trade_appeal "golang.52tt.com/protocol/services/esport-trade-appeal"
)

// MockMgr is a mock of Mgr interface.
type MockMgr struct {
	ctrl     *gomock.Controller
	recorder *MockMgrMockRecorder
}

// MockMgrMockRecorder is the mock recorder for MockMgr.
type MockMgrMockRecorder struct {
	mock *MockMgr
}

// NewMockMgr creates a new mock instance.
func NewMockMgr(ctrl *gomock.Controller) *MockMgr {
	mock := &MockMgr{ctrl: ctrl}
	mock.recorder = &MockMgrMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockMgr) EXPECT() *MockMgrMockRecorder {
	return m.recorder
}

// AcceptFastRefund mocks base method.
func (m *MockMgr) AcceptFastRefund(arg0 context.Context, arg1 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AcceptFastRefund", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// AcceptFastRefund indicates an expected call of AcceptFastRefund.
func (mr *MockMgrMockRecorder) AcceptFastRefund(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AcceptFastRefund", reflect.TypeOf((*MockMgr)(nil).AcceptFastRefund), arg0, arg1)
}

// AcceptRefund mocks base method.
func (m *MockMgr) AcceptRefund(arg0 context.Context, arg1 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AcceptRefund", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// AcceptRefund indicates an expected call of AcceptRefund.
func (mr *MockMgrMockRecorder) AcceptRefund(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AcceptRefund", reflect.TypeOf((*MockMgr)(nil).AcceptRefund), arg0, arg1)
}

// BatchGetOrderRefundStatus mocks base method.
func (m *MockMgr) BatchGetOrderRefundStatus(arg0 context.Context, arg1 []string) ([]*esport_trade_appeal.BatchGetOrderRefundStatusResponse_OrderRefundStatus, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetOrderRefundStatus", arg0, arg1)
	ret0, _ := ret[0].([]*esport_trade_appeal.BatchGetOrderRefundStatusResponse_OrderRefundStatus)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetOrderRefundStatus indicates an expected call of BatchGetOrderRefundStatus.
func (mr *MockMgrMockRecorder) BatchGetOrderRefundStatus(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetOrderRefundStatus", reflect.TypeOf((*MockMgr)(nil).BatchGetOrderRefundStatus), arg0, arg1)
}

// CheckFastRefundEligibility mocks base method.
func (m *MockMgr) CheckFastRefundEligibility(arg0 context.Context, arg1 uint32, arg2 int64, arg3 string) bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckFastRefundEligibility", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(bool)
	return ret0
}

// CheckFastRefundEligibility indicates an expected call of CheckFastRefundEligibility.
func (mr *MockMgrMockRecorder) CheckFastRefundEligibility(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckFastRefundEligibility", reflect.TypeOf((*MockMgr)(nil).CheckFastRefundEligibility), arg0, arg1, arg2, arg3)
}

// CreateFastRefund mocks base method.
func (m *MockMgr) CreateFastRefund(arg0 context.Context, arg1 *esport_trade_appeal.CreateRefundRequest, arg2 *esport_trade.SkillProductOrderDetail) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateFastRefund", arg0, arg1, arg2)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateFastRefund indicates an expected call of CreateFastRefund.
func (mr *MockMgrMockRecorder) CreateFastRefund(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateFastRefund", reflect.TypeOf((*MockMgr)(nil).CreateFastRefund), arg0, arg1, arg2)
}

// CreateRefund mocks base method.
func (m *MockMgr) CreateRefund(arg0 context.Context, arg1 *esport_trade_appeal.CreateRefundRequest) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateRefund", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateRefund indicates an expected call of CreateRefund.
func (mr *MockMgrMockRecorder) CreateRefund(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateRefund", reflect.TypeOf((*MockMgr)(nil).CreateRefund), arg0, arg1)
}

// GetAppealDetail mocks base method.
func (m *MockMgr) GetAppealDetail(arg0 context.Context, arg1 string) (*esport_trade_appeal.AppealDetail, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAppealDetail", arg0, arg1)
	ret0, _ := ret[0].(*esport_trade_appeal.AppealDetail)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAppealDetail indicates an expected call of GetAppealDetail.
func (mr *MockMgrMockRecorder) GetAppealDetail(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAppealDetail", reflect.TypeOf((*MockMgr)(nil).GetAppealDetail), arg0, arg1)
}

// GetAppealList mocks base method.
func (m *MockMgr) GetAppealList(arg0 context.Context, arg1 *esport_trade_appeal.GetAppealListRequest) (*esport_trade_appeal.GetAppealListResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAppealList", arg0, arg1)
	ret0, _ := ret[0].(*esport_trade_appeal.GetAppealListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAppealList indicates an expected call of GetAppealList.
func (mr *MockMgrMockRecorder) GetAppealList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAppealList", reflect.TypeOf((*MockMgr)(nil).GetAppealList), arg0, arg1)
}

// GetOrderRefundView mocks base method.
func (m *MockMgr) GetOrderRefundView(arg0 context.Context, arg1 string) (*esport_trade_appeal.OrderRefundView, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOrderRefundView", arg0, arg1)
	ret0, _ := ret[0].(*esport_trade_appeal.OrderRefundView)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOrderRefundView indicates an expected call of GetOrderRefundView.
func (mr *MockMgrMockRecorder) GetOrderRefundView(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOrderRefundView", reflect.TypeOf((*MockMgr)(nil).GetOrderRefundView), arg0, arg1)
}

// InitiateAppeal mocks base method.
func (m *MockMgr) InitiateAppeal(arg0 context.Context, arg1 *esport_trade_appeal.InitiateAppealRequest) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InitiateAppeal", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// InitiateAppeal indicates an expected call of InitiateAppeal.
func (mr *MockMgrMockRecorder) InitiateAppeal(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InitiateAppeal", reflect.TypeOf((*MockMgr)(nil).InitiateAppeal), arg0, arg1)
}

// PassAppeal mocks base method.
func (m *MockMgr) PassAppeal(arg0 context.Context, arg1, arg2 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PassAppeal", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// PassAppeal indicates an expected call of PassAppeal.
func (mr *MockMgrMockRecorder) PassAppeal(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PassAppeal", reflect.TypeOf((*MockMgr)(nil).PassAppeal), arg0, arg1, arg2)
}

// RejectAppeal mocks base method.
func (m *MockMgr) RejectAppeal(arg0 context.Context, arg1, arg2 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RejectAppeal", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// RejectAppeal indicates an expected call of RejectAppeal.
func (mr *MockMgrMockRecorder) RejectAppeal(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RejectAppeal", reflect.TypeOf((*MockMgr)(nil).RejectAppeal), arg0, arg1, arg2)
}

// RejectRefund mocks base method.
func (m *MockMgr) RejectRefund(arg0 context.Context, arg1, arg2, arg3 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RejectRefund", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// RejectRefund indicates an expected call of RejectRefund.
func (mr *MockMgrMockRecorder) RejectRefund(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RejectRefund", reflect.TypeOf((*MockMgr)(nil).RejectRefund), arg0, arg1, arg2, arg3)
}

// SubmitGuideAppealInfo mocks base method.
func (m *MockMgr) SubmitGuideAppealInfo(arg0 context.Context, arg1 *esport_trade_appeal.SubmitGuideAppealInfoRequest) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SubmitGuideAppealInfo", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// SubmitGuideAppealInfo indicates an expected call of SubmitGuideAppealInfo.
func (mr *MockMgrMockRecorder) SubmitGuideAppealInfo(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SubmitGuideAppealInfo", reflect.TypeOf((*MockMgr)(nil).SubmitGuideAppealInfo), arg0, arg1)
}

// TimerTaskAppealAuditFailure mocks base method.
func (m *MockMgr) TimerTaskAppealAuditFailure(arg0 context.Context) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "TimerTaskAppealAuditFailure", arg0)
}

// TimerTaskAppealAuditFailure indicates an expected call of TimerTaskAppealAuditFailure.
func (mr *MockMgrMockRecorder) TimerTaskAppealAuditFailure(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TimerTaskAppealAuditFailure", reflect.TypeOf((*MockMgr)(nil).TimerTaskAppealAuditFailure), arg0)
}

// TimerTaskAppealAuditTimeout mocks base method.
func (m *MockMgr) TimerTaskAppealAuditTimeout(arg0 context.Context) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "TimerTaskAppealAuditTimeout", arg0)
}

// TimerTaskAppealAuditTimeout indicates an expected call of TimerTaskAppealAuditTimeout.
func (mr *MockMgrMockRecorder) TimerTaskAppealAuditTimeout(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TimerTaskAppealAuditTimeout", reflect.TypeOf((*MockMgr)(nil).TimerTaskAppealAuditTimeout), arg0)
}

// TimerTaskAutomaticallyAddAppealAsPending mocks base method.
func (m *MockMgr) TimerTaskAutomaticallyAddAppealAsPending(arg0 context.Context) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "TimerTaskAutomaticallyAddAppealAsPending", arg0)
}

// TimerTaskAutomaticallyAddAppealAsPending indicates an expected call of TimerTaskAutomaticallyAddAppealAsPending.
func (mr *MockMgrMockRecorder) TimerTaskAutomaticallyAddAppealAsPending(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TimerTaskAutomaticallyAddAppealAsPending", reflect.TypeOf((*MockMgr)(nil).TimerTaskAutomaticallyAddAppealAsPending), arg0)
}

// TimerTaskCoachRefundFallback mocks base method.
func (m *MockMgr) TimerTaskCoachRefundFallback(arg0 context.Context) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "TimerTaskCoachRefundFallback", arg0)
}

// TimerTaskCoachRefundFallback indicates an expected call of TimerTaskCoachRefundFallback.
func (mr *MockMgrMockRecorder) TimerTaskCoachRefundFallback(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TimerTaskCoachRefundFallback", reflect.TypeOf((*MockMgr)(nil).TimerTaskCoachRefundFallback), arg0)
}

// TimerTaskFastRefundFallback mocks base method.
func (m *MockMgr) TimerTaskFastRefundFallback(arg0 context.Context) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "TimerTaskFastRefundFallback", arg0)
}

// TimerTaskFastRefundFallback indicates an expected call of TimerTaskFastRefundFallback.
func (mr *MockMgrMockRecorder) TimerTaskFastRefundFallback(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TimerTaskFastRefundFallback", reflect.TypeOf((*MockMgr)(nil).TimerTaskFastRefundFallback), arg0)
}

// TimerTaskHandleCoachRefundTimeout mocks base method.
func (m *MockMgr) TimerTaskHandleCoachRefundTimeout(arg0 context.Context) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "TimerTaskHandleCoachRefundTimeout", arg0)
}

// TimerTaskHandleCoachRefundTimeout indicates an expected call of TimerTaskHandleCoachRefundTimeout.
func (mr *MockMgrMockRecorder) TimerTaskHandleCoachRefundTimeout(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TimerTaskHandleCoachRefundTimeout", reflect.TypeOf((*MockMgr)(nil).TimerTaskHandleCoachRefundTimeout), arg0)
}

// TimerTaskHandleUserRefundFailureTimeout mocks base method.
func (m *MockMgr) TimerTaskHandleUserRefundFailureTimeout(arg0 context.Context) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "TimerTaskHandleUserRefundFailureTimeout", arg0)
}

// TimerTaskHandleUserRefundFailureTimeout indicates an expected call of TimerTaskHandleUserRefundFailureTimeout.
func (mr *MockMgrMockRecorder) TimerTaskHandleUserRefundFailureTimeout(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TimerTaskHandleUserRefundFailureTimeout", reflect.TypeOf((*MockMgr)(nil).TimerTaskHandleUserRefundFailureTimeout), arg0)
}

// MockTransaction is a mock of Transaction interface.
type MockTransaction struct {
	ctrl     *gomock.Controller
	recorder *MockTransactionMockRecorder
}

// MockTransactionMockRecorder is the mock recorder for MockTransaction.
type MockTransactionMockRecorder struct {
	mock *MockTransaction
}

// NewMockTransaction creates a new mock instance.
func NewMockTransaction(ctrl *gomock.Controller) *MockTransaction {
	mock := &MockTransaction{ctrl: ctrl}
	mock.recorder = &MockTransactionMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockTransaction) EXPECT() *MockTransactionMockRecorder {
	return m.recorder
}

// Wrap mocks base method.
func (m *MockTransaction) Wrap(arg0 context.Context, arg1 func(context.Context) (interface{}, error)) (interface{}, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Wrap", arg0, arg1)
	ret0, _ := ret[0].(interface{})
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Wrap indicates an expected call of Wrap.
func (mr *MockTransactionMockRecorder) Wrap(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Wrap", reflect.TypeOf((*MockTransaction)(nil).Wrap), arg0, arg1)
}

// MockIM is a mock of IM interface.
type MockIM struct {
	ctrl     *gomock.Controller
	recorder *MockIMMockRecorder
}

// MockIMMockRecorder is the mock recorder for MockIM.
type MockIMMockRecorder struct {
	mock *MockIM
}

// NewMockIM creates a new mock instance.
func NewMockIM(ctrl *gomock.Controller) *MockIM {
	mock := &MockIM{ctrl: ctrl}
	mock.recorder = &MockIMMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIM) EXPECT() *MockIMMockRecorder {
	return m.recorder
}

// AsyncSendAppealApplyIMMsg mocks base method.
func (m *MockIM) AsyncSendAppealApplyIMMsg(arg0 context.Context, arg1 string) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "AsyncSendAppealApplyIMMsg", arg0, arg1)
}

// AsyncSendAppealApplyIMMsg indicates an expected call of AsyncSendAppealApplyIMMsg.
func (mr *MockIMMockRecorder) AsyncSendAppealApplyIMMsg(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AsyncSendAppealApplyIMMsg", reflect.TypeOf((*MockIM)(nil).AsyncSendAppealApplyIMMsg), arg0, arg1)
}

// AsyncSendAppealCoachUploadIMMsg mocks base method.
func (m *MockIM) AsyncSendAppealCoachUploadIMMsg(arg0 context.Context, arg1 string) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "AsyncSendAppealCoachUploadIMMsg", arg0, arg1)
}

// AsyncSendAppealCoachUploadIMMsg indicates an expected call of AsyncSendAppealCoachUploadIMMsg.
func (mr *MockIMMockRecorder) AsyncSendAppealCoachUploadIMMsg(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AsyncSendAppealCoachUploadIMMsg", reflect.TypeOf((*MockIM)(nil).AsyncSendAppealCoachUploadIMMsg), arg0, arg1)
}

// AsyncSendAppealFailIMMsg mocks base method.
func (m *MockIM) AsyncSendAppealFailIMMsg(arg0 context.Context, arg1 string) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "AsyncSendAppealFailIMMsg", arg0, arg1)
}

// AsyncSendAppealFailIMMsg indicates an expected call of AsyncSendAppealFailIMMsg.
func (mr *MockIMMockRecorder) AsyncSendAppealFailIMMsg(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AsyncSendAppealFailIMMsg", reflect.TypeOf((*MockIM)(nil).AsyncSendAppealFailIMMsg), arg0, arg1)
}

// AsyncSendAppealPendingAuditIMMsg mocks base method.
func (m *MockIM) AsyncSendAppealPendingAuditIMMsg(arg0 context.Context, arg1 string) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "AsyncSendAppealPendingAuditIMMsg", arg0, arg1)
}

// AsyncSendAppealPendingAuditIMMsg indicates an expected call of AsyncSendAppealPendingAuditIMMsg.
func (mr *MockIMMockRecorder) AsyncSendAppealPendingAuditIMMsg(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AsyncSendAppealPendingAuditIMMsg", reflect.TypeOf((*MockIM)(nil).AsyncSendAppealPendingAuditIMMsg), arg0, arg1)
}

// AsyncSendAppealSuccessIMMsg mocks base method.
func (m *MockIM) AsyncSendAppealSuccessIMMsg(arg0 context.Context, arg1 string) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "AsyncSendAppealSuccessIMMsg", arg0, arg1)
}

// AsyncSendAppealSuccessIMMsg indicates an expected call of AsyncSendAppealSuccessIMMsg.
func (mr *MockIMMockRecorder) AsyncSendAppealSuccessIMMsg(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AsyncSendAppealSuccessIMMsg", reflect.TypeOf((*MockIM)(nil).AsyncSendAppealSuccessIMMsg), arg0, arg1)
}

// AsyncSendFastRefundAcceptIMMsg mocks base method.
func (m *MockIM) AsyncSendFastRefundAcceptIMMsg(arg0 context.Context, arg1 string) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "AsyncSendFastRefundAcceptIMMsg", arg0, arg1)
}

// AsyncSendFastRefundAcceptIMMsg indicates an expected call of AsyncSendFastRefundAcceptIMMsg.
func (mr *MockIMMockRecorder) AsyncSendFastRefundAcceptIMMsg(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AsyncSendFastRefundAcceptIMMsg", reflect.TypeOf((*MockIM)(nil).AsyncSendFastRefundAcceptIMMsg), arg0, arg1)
}

// AsyncSendRefundAcceptIMMsg mocks base method.
func (m *MockIM) AsyncSendRefundAcceptIMMsg(arg0 context.Context, arg1 string) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "AsyncSendRefundAcceptIMMsg", arg0, arg1)
}

// AsyncSendRefundAcceptIMMsg indicates an expected call of AsyncSendRefundAcceptIMMsg.
func (mr *MockIMMockRecorder) AsyncSendRefundAcceptIMMsg(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AsyncSendRefundAcceptIMMsg", reflect.TypeOf((*MockIM)(nil).AsyncSendRefundAcceptIMMsg), arg0, arg1)
}

// AsyncSendRefundApplyIMMsg mocks base method.
func (m *MockIM) AsyncSendRefundApplyIMMsg(arg0 context.Context, arg1 string) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "AsyncSendRefundApplyIMMsg", arg0, arg1)
}

// AsyncSendRefundApplyIMMsg indicates an expected call of AsyncSendRefundApplyIMMsg.
func (mr *MockIMMockRecorder) AsyncSendRefundApplyIMMsg(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AsyncSendRefundApplyIMMsg", reflect.TypeOf((*MockIM)(nil).AsyncSendRefundApplyIMMsg), arg0, arg1)
}

// AsyncSendRefundRejectIMMsg mocks base method.
func (m *MockIM) AsyncSendRefundRejectIMMsg(arg0 context.Context, arg1 string) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "AsyncSendRefundRejectIMMsg", arg0, arg1)
}

// AsyncSendRefundRejectIMMsg indicates an expected call of AsyncSendRefundRejectIMMsg.
func (mr *MockIMMockRecorder) AsyncSendRefundRejectIMMsg(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AsyncSendRefundRejectIMMsg", reflect.TypeOf((*MockIM)(nil).AsyncSendRefundRejectIMMsg), arg0, arg1)
}
