package cache

import (
	"context"
	"encoding/json"
	"fmt"
	"github.com/stretchr/testify/assert"
	redisConnect "gitlab.ttyuyin.com/avengers/tyr/core/middleware/redis/connect"
	trade_im_notify "golang.52tt.com/services/tt-rev/esport/common/trade-im-notify"
	"golang.52tt.com/services/tt-rev/esport/esport-trade-appeal/internal/conf"
	"io/ioutil"
	"os"
	"testing"
	"time"
)

type helperForTest struct {
	*CacheImpl
}

func newHelperForTest(t *testing.T) (*helperForTest, func()) {
	//controller := gomock.NewController(t)
	// 1. 读取配置文件
	sc := &conf.ServerConfig{}
	getenv := os.Getenv("TESTING_LOCALLY")
	if getenv == "true" {
		t.Logf("testing locally")
		// docker run -d --name redis -p 6379:6379 redis:7.0.5-alpine3.16
		sc.Redis = &redisConnect.RedisConfig{
			Host: "localhost",
			Port: 6379,
		}
	} else {
		t.Logf("testing in dev")
		bytes, err := ioutil.ReadFile("../../service-config.json")
		if err != nil {
			t.Errorf("read config file error: %v", err)
		}
		err = json.Unmarshal(bytes, sc)
		if err != nil {
			t.Errorf("unmarshal config file error: %v", err)
		}

	}
	t.Logf("cache testing config: %v", sc)

	cache, f, err := NewCache(context.Background(), sc)
	if err != nil {
		t.Fatalf("new cache error: %v", err)
	}

	return &helperForTest{
		CacheImpl: cache,
	}, f
}

func TestCacheImpl_GetLastRefundIMMsg(t *testing.T) {
	cli, f := newHelperForTest(t)
	defer f()

	// 定义测试数据
	userID := uint32(1)
	refundID := "test_refund_id"
	expectedMsg := &trade_im_notify.UserOrderImMsg{
		// 填充预期的消息内容
	}

	// 首先设置一条消息
	err := cli.SetLeastRefundIMMsg(context.Background(), userID, refundID, expectedMsg)
	if err != nil {
		t.Fatalf("set message error: %v", err)
	}
	defer func() {
		// 清理数据
		err := cli.GetRedisClient().Del(context.Background(), cli.getRefundKey(userID, refundID)).Err()
		if err != nil {
			t.Fatalf("clean up data error: %v", err)
		}
	}()

	// 使用 GetLastRefundIMMsg 方法获取数据
	msg, err := cli.GetLastRefundIMMsg(context.Background(), userID, refundID)
	if err != nil {
		t.Fatalf("get message error: %v", err)
	}

	// 检查返回的结果是否符合预期
	assert.Equal(t, expectedMsg, msg)
}

func TestCacheImpl_getFastRefundKey(t *testing.T) {
	cli, f := newHelperForTest(t)
	defer f()

	// Define test data
	userID := uint32(1)
	now := time.Now()
	year, month, _ := now.Date()
	expectedKey := fmt.Sprintf("fast_refund_count_%d_%d_%d", userID, year, month)

	// Invoke the tested method
	key := cli.getFastRefundKey(userID)

	// Check the result
	if key != expectedKey {
		t.Errorf("Expected key to be '%s', but got '%s'", expectedKey, key)
	}
}

func TestCacheImpl_getRefundKey(t *testing.T) {
	cli, f := newHelperForTest(t)
	defer f()

	// Test cases with different input data
	testCases := []struct {
		userID   uint32
		refundID string
		expected string
	}{
		{userID: 1, refundID: "refund123", expected: "user:1:refund:refund123"},
		{userID: 42, refundID: "refundABC", expected: "user:42:refund:refundABC"},
		{userID: 0, refundID: "", expected: "user:0:refund:"},
	}

	for _, tc := range testCases {
		t.Run(fmt.Sprintf("UserID%d_RefundID%s", tc.userID, tc.refundID), func(t *testing.T) {
			// Invoke the tested method
			key := cli.getRefundKey(tc.userID, tc.refundID)

			// Check the result
			if key != tc.expected {
				t.Errorf("getRefundKey(%d, %s) = %s; want %s", tc.userID, tc.refundID, key, tc.expected)
			}
		})
	}
}

func TestCacheImpl_getAppealKey(t *testing.T) {
	cli, f := newHelperForTest(t)
	defer f()

	// Test cases
	tests := []struct {
		userID   uint32
		appealID string
		expected string
	}{
		{userID: 1, appealID: "appeal123", expected: "user:1:appeal:appeal123"},
		{userID: 42, appealID: "test42", expected: "user:42:appeal:test42"},
		{userID: 0, appealID: "zero", expected: "user:0:appeal:zero"},
		// Add more test cases if necessary
	}

	for _, tc := range tests {
		t.Run(fmt.Sprintf("UserID:%d/AppealID:%s", tc.userID, tc.appealID), func(t *testing.T) {
			// Invoke the tested method
			key := cli.getAppealKey(tc.userID, tc.appealID)

			// Check the result
			if key != tc.expected {
				t.Errorf("getAppealKey(%d, %s) = %s; want %s", tc.userID, tc.appealID, key, tc.expected)
			}
		})
	}
}

func TestCacheImpl_SetLeastRefundIMMsg(t *testing.T) {
	cli, f := newHelperForTest(t)
	defer f()

	// Define test data
	userID := uint32(1)
	refundID := "test_refund_id"
	expectedMsg := &trade_im_notify.UserOrderImMsg{}

	// Test for successful set message
	t.Run("Set Message Successfully", func(t *testing.T) {
		err := cli.SetLeastRefundIMMsg(context.Background(), userID, refundID, expectedMsg)
		assert.NoError(t, err)

		defer func() {
			// Clean up data
			err := cli.GetRedisClient().Del(context.Background(), cli.getRefundKey(userID, refundID)).Err()
			assert.NoError(t, err)
		}()

		// Assert that the message was set correctly by retrieving it directly from Redis
		data, err := cli.GetRedisClient().Get(context.Background(), cli.getRefundKey(userID, refundID)).Bytes()
		assert.NoError(t, err)

		var msg trade_im_notify.UserOrderImMsg
		err = json.Unmarshal(data, &msg)
		assert.NoError(t, err)
		assert.Equal(t, expectedMsg, &msg)
	})

}

func TestCacheImpl_IncrUserRefundCount(t *testing.T) {
	cli, f := newHelperForTest(t)
	defer f()

	// init data
	userID := uint32(1)
	key := cli.getFastRefundKey(userID)

	// ensure the key does not exist before testing
	err := cli.cmder.Del(context.Background(), key).Err()
	if err != nil {
		t.Fatalf("unable to delete key before test: %v", err)
	}

	defer func() {
		// clean up data
		err := cli.cmder.Del(context.Background(), key).Err()
		if err != nil {
			t.Fatalf("clean up data error: %v", err)
		}
	}()

	// invoke the tested method for the first time
	count, err := cli.IncrUserRefundCount(context.Background(), userID)
	if err != nil {
		t.Fatalf("IncrUserRefundCount error: %v", err)
	}

	// check the result of the first increment
	assert.Equal(t, 1, count, "the count should start at 1 after the first increment")

	// check if the key expiration is set correctly
	ttl, err := cli.cmder.TTL(context.Background(), key).Result()
	if err != nil {
		t.Fatalf("failed to get TTL for key: %v, error: %v", key, err)
	}
	assert.InDelta(t, 32*24*time.Hour, ttl, float64(5*time.Minute), "key expiration should be set to 32 days")

	// invoke the tested method again to increment the count
	newCount, err := cli.IncrUserRefundCount(context.Background(), userID)
	if err != nil {
		t.Fatalf("IncrUserRefundCount error: %v", err)
	}

	// check the result of the second increment
	assert.Equal(t, 2, newCount, "the count should increment to 2 after the second increment")
}

func TestCacheImpl_GetUserFastRefundCount(t *testing.T) {
	cli, cleanup := newHelperForTest(t)
	defer cleanup()

	// Define test data
	userID := uint32(1)
	key := cli.getFastRefundKey(userID)
	expectedCount := 3

	// Set initial data in the cache
	err := cli.GetRedisClient().Set(context.Background(), key, expectedCount, 0).Err()
	if err != nil {
		t.Fatalf("Error setting up test data: %v", err)
	}

	defer func() {
		// Clean up test data
		err := cli.GetRedisClient().Del(context.Background(), key).Err()
		if err != nil {
			t.Fatalf("Error cleaning up test data: %v", err)
		}
	}()

	// Test case where count is present
	count, err := cli.GetUserFastRefundCount(context.Background(), userID)
	if err != nil {
		t.Fatalf("Error getting user fast refund count: %v", err)
	}
	assert.Equal(t, expectedCount, count, "Count should match the expected value")

	// Test case where count is not present (redis.Nil)
	cli.GetRedisClient().Del(context.Background(), key)
	count, err = cli.GetUserFastRefundCount(context.Background(), userID)
	if err != nil {
		t.Fatalf("Error should be nil when key is not present, got: %v", err)
	}
	assert.Equal(t, 0, count, "Count should be 0 when key is not present")

	// Test case where there is an error other than redis.Nil
	cli.GetRedisClient().Set(context.Background(), key, "invalid", 0) // Set invalid data to simulate strconv.Atoi error
	_, err = cli.GetUserFastRefundCount(context.Background(), userID)
	if assert.Error(t, err) {
		assert.Contains(t, err.Error(), "convert user fast refund count to int err:", "Error should indicate conversion problem")
	}
}

func TestCacheImpl_GetLastAppealIMMsg(t *testing.T) {
	cli, f := newHelperForTest(t)
	defer f()

	// Define test data
	userID := uint32(1)
	appealID := "test_appeal_id"
	expectedMsg := &trade_im_notify.UserOrderImMsg{}

	// Serialize the expected message to store in the cache
	data, err := json.Marshal(expectedMsg)
	if err != nil {
		t.Fatalf("error marshalling expected message: %v", err)
	}

	// Set the message in the cache
	err = cli.GetRedisClient().Set(context.Background(), cli.getAppealKey(userID, appealID), data, 0).Err()
	if err != nil {
		t.Fatalf("error setting message in cache: %v", err)
	}

	defer func() {
		// Clean up data
		err := cli.GetRedisClient().Del(context.Background(), cli.getAppealKey(userID, appealID)).Err()
		if err != nil {
			t.Fatalf("error cleaning up data: %v", err)
		}
	}()

	// Test getting the message successfully
	t.Run("get message successfully", func(t *testing.T) {
		msg, err := cli.GetLastAppealIMMsg(context.Background(), userID, appealID)
		if err != nil {
			t.Fatalf("error getting message: %v", err)
		}
		assert.Equal(t, expectedMsg, msg)
	})

	// Test getting a message that does not exist
	t.Run("message does not exist", func(t *testing.T) {
		msg, err := cli.GetLastAppealIMMsg(context.Background(), userID, "non_existing_appeal_id")
		assert.Nil(t, msg)
		assert.Equal(t, ErrNotFound, err)
	})

}

func TestCacheImpl_SetLeastAppealIMMsg(t *testing.T) {
	cli, f := newHelperForTest(t)
	defer f()

	// Define test data
	userID := uint32(1)
	appealID := "test_appeal_id"
	expectedMsg := &trade_im_notify.UserOrderImMsg{
		// Populate the expected message content
	}

	// Serialize the expected message for comparison purposes
	expectedData, _ := json.Marshal(expectedMsg)

	// Invoke SetLeastAppealIMMsg method with proper data
	err := cli.SetLeastAppealIMMsg(context.Background(), userID, appealID, expectedMsg)
	assert.NoError(t, err)

	defer func() {
		// Clean up data
		err := cli.GetRedisClient().Del(context.Background(), cli.getAppealKey(userID, appealID)).Err()
		if err != nil {
			t.Fatalf("clean up data error: %v", err)
		}
	}()

	// Fetch the data from Redis to check if it was properly set
	actualData, err := cli.GetRedisClient().Get(context.Background(), cli.getAppealKey(userID, appealID)).Bytes()
	assert.NoError(t, err)
	assert.Equal(t, expectedData, actualData, "Data in Redis does not match the expected message")

}
