package mgr

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"time"

	"go.opentelemetry.io/otel/codes"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/protocol/common/status"
	pb "golang.52tt.com/protocol/services/esport-skill"
	"golang.52tt.com/protocol/services/esport_hall"
)

const (
	defaultSkillDesc = "能清晰展示可以证明当前段位或者游戏能力的截图"
)

type GameConfigService interface {
	// GetGameListByGameNameFuzzy 根据游戏名称模糊获取游戏列表
	GetGameListByGameNameFuzzy(c context.Context, name string) ([]*pb.SimpleGameInfo, error)
	// GetGameDetailByIds 批量获取游戏详情
	GetGameDetailByIds(c context.Context, ids []uint32) (*pb.GetGameDetailByIdsResponse, error)
	// GetGameDetailById 获取游戏详情
	GetGameDetailById(c context.Context, id uint32) (*pb.GetGameDetailByIdResponse, error)
	// GetEsportGameConfigListByPage 获取游戏配置列表
	GetEsportGameConfigListByPage(ctx context.Context, req *pb.GetEsportGameConfigListByPageRequest) (*pb.GetEsportGameConfigListByPageResponse, error)
	AddEsportGameConfig(ctx context.Context, req *pb.AddEsportGameConfigRequest) error
}

func (m *Manager) GetTopGameList(ctx context.Context, uid uint32, ty pb.GAME_TYPE) (*pb.GetTopGameListResponse, error) {
	out := &pb.GetTopGameListResponse{}
	out, err := m.cache.GetTopGameList(ctx, ty)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetTopGameList failed to get from cache, ty:%d, err:%v", ty, err)
	} else {
		log.DebugWithCtx(ctx, "GetTopGameList get info from cache, ty:%d, uid:%d", ty, uid)
		return out, nil
	}
	itemList, err := m.store.GetTopGameList(ctx, ty)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetTopGameList failed, uid:%d, ty:%d, err:%v", uid, ty, err)
		return out, err
	}
	out.ItemList = itemList

	go func() {
		goCtx, cancel := context.WithTimeout(context.Background(), time.Second*5)
		defer cancel()
		if err := m.cache.SetTopGameList(goCtx, ty, out); err != nil {
			log.ErrorWithCtx(goCtx, "GetTopGameList failed to SetTopGameList, uid:%d, ty:%d, err:%v", uid, ty, err)
			return
		}
	}()
	log.DebugWithCtx(ctx, "GetTopGameList get from store, uid:%d, ty:%d", uid, ty)
	return out, nil
}

func (m *Manager) GetAllGameSimpleInfo(ctx context.Context) (*pb.GetAllGameSimpleInfoResponse, error) {
	resp, err := m.cache.GetAllGameSimpleInfo(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAllGameSimpleInfo failed, err:%v", err)
	} else {
		log.DebugWithCtx(ctx, "GetAllGameSimpleInfo get info from cache")
		return resp, nil
	}
	resp, err = m.store.GetAllGameSimpleInfo(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAllGameSimpleInfo failed, err:%v", err)
		return resp, err
	}
	go func() {
		goCtx, cancel := context.WithTimeout(context.Background(), time.Second*5)
		defer cancel()
		if err := m.cache.SetAllGameSimpleInfo(goCtx, resp); err != nil {
			log.ErrorWithCtx(goCtx, "GetAllGameSimpleInfo faild to SetAllGameSimpleInfo, err:%v", err)
		}
	}()
	log.DebugWithCtx(ctx, "GetAllGameSimpleInfo get from store")
	return resp, nil
}

func (m *Manager) GetGameList(ctx context.Context, ty pb.GAME_TYPE, pageToken string) (*pb.GetGameListResponse, error) {
	out := &pb.GetGameListResponse{}
	token := newPageToken(pageToken)
	pageSize := m.bc.GetGameListPageSize()
	pageNum := token.PageNum
	out, err := m.cache.GetGameList(ctx, ty, pageNum, pageSize)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetGameList failed to get from cache, ty:%d, pageNum:%d, err:%v", ty, pageNum, err)
	} else {
		log.DebugWithCtx(ctx, "GetGameList get from cache, ty:%d, pageNum:%d", ty, pageNum)
		return out, nil
	}

	itemList, err := m.store.GetGameList(ctx, ty, pageNum, pageSize)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetGameList failed, ty:%d, pageToken:%s, err:%v", ty, pageToken, err)
		return out, err
	}
	out.ItemList = itemList
	var nextPageToken string
	// 分页未结束，更新为新的token
	if len(itemList) == int(pageSize) {
		token.PageNum += 1
		nextPageToken = token.String()
	}
	out.NextPageToken = nextPageToken

	go func() {
		goCtx, cancel := context.WithTimeout(context.Background(), time.Second*5)
		defer cancel()
		if err := m.cache.SetGameList(goCtx, ty, pageNum, pageSize, out); err != nil {
			log.ErrorWithCtx(goCtx, "GetGameList failed to SetGameList, ty:%d, pageNum:%d, err:%v", ty, pageNum, err)
			return
		}
	}()
	log.DebugWithCtx(ctx, "GetGameList done, get from store, ty:%d, pageNum:%d", ty, pageNum)
	return out, nil
}

type pageToken struct {
	PageNum uint32 `json:"page_num"`
}

func newPageToken(token string) *pageToken {
	out := &pageToken{}
	if token == "" {
		return out
	}
	b, err := base64.StdEncoding.DecodeString(token)
	if err != nil {
		log.Errorf("NewPageToken failed to DecodeString, token:%s, err:%v", token, err)
		return out
	}
	err = json.Unmarshal(b, out)
	if err != nil {
		log.Errorf("NewPageToken failed to Unmarshal, token:%s, err:%v", token, err)
		return out
	}
	return out
}

func (t *pageToken) String() string {
	b, err := json.Marshal(t)
	if err != nil {
		log.Errorf("String Marshal failed, t:%+v, err:%v", t, err)
		return ""
	}
	return base64.StdEncoding.EncodeToString(b)
}

func (m *Manager) GetSwitchStatus(ctx context.Context, uid uint32) (*pb.GetSwitchResponse, error) {
	out := &pb.GetSwitchResponse{
		SwitchStatus: m.bc.GetSwitch(ctx, uid),
	}
	log.DebugWithCtx(ctx, "GetSwitchStatus uid:%d, switch:%+v", uid, out.SwitchStatus)
	return out, nil
}

func (m *Manager) AddEsportGameConfig(ctx context.Context, req *pb.AddEsportGameConfigRequest) error {
	id, err := m.store.AddEsportGameConfig(ctx, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "AddEsportGameConfig failed, req:%+v, err:%v", req, err)
		return err
	}
	go func() {
		goCtx, cancel := context.WithTimeout(context.Background(), time.Second*5)
		defer cancel()
		if err := m.cache.DelGameDetailById(goCtx, id); err != nil {
			log.ErrorWithCtx(goCtx, "AddEsportGameConfig failed to DelGameDetailById, id:%d, err:%v", id, err)
		}
	}()

	log.InfoWithCtx(ctx, "AddEsportGameConfig success, req:%+v, id:%d", req, id)
	return nil
}

func slicesEqual(a, b []string) bool {
	if len(a) != len(b) {
		return false
	}
	for i, v := range a {
		if v != b[i] {
			return false
		}
	}
	return true
}

func (m *Manager) checkGuaranteeSectionChange(currentSection, preSection *pb.GameInformation) bool {
	if currentSection == nil {
		if preSection == nil {
			return false
		} else {
			return true
		}
	}
	if preSection == nil {
		return true
	}
	return !slicesEqual(currentSection.GetItemList(), preSection.GetItemList())
}

func (m *Manager) UpdateEsportGameConfig(ctx context.Context, req *pb.UpdateEsportGameConfigRequest) error {
	preGame, err := m.GetGameDetailById(ctx, req.GetConfig().GetGameId())
	if err != nil {
		log.ErrorWithCtx(ctx, "UpdateEsportGameConfig failed to GetGameDetailById, req:%+v, err:%v", req, err)
		return err
	}

	var preSection, currentSection *pb.GameInformation
	for _, section := range req.GetConfig().GetGameInformationList() {
		if section.SectionName == guaranteeSection {
			currentSection = section
			break
		}
	}
	for _, section := range preGame.GetConfig().GetGameInformationList() {
		if section.SectionName == guaranteeSection {
			preSection = section
			break
		}
	}

	change := m.checkGuaranteeSectionChange(currentSection, preSection)

	if change {
		log.InfoWithCtx(ctx, "gameCfg change, gameId:%d", req.GetConfig().GetGameId())
		go func() {
			bgCtx := context.Background()
			// 传入最新配置
			_ = m.handleGameUpdate(bgCtx, req.GetConfig())
		}()
	}

	err = m.store.UpdateEsportGameConfig(ctx, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "UpdateEsportGameConfig failed, req:%+v, err:%v", req, err)
		return err
	}
	go func() {
		goCtx, cancel := context.WithTimeout(context.Background(), time.Second*5)
		defer cancel()
		if err := m.cache.DelGameDetailById(goCtx, req.GetConfig().GetGameId()); err != nil {
			log.ErrorWithCtx(goCtx, "UpdateEsportGameConfig failed to DelGameDetailById, id:%d, err:%v", req.GetConfig().GetGameId(), err)
		}
	}()
	log.InfoWithCtx(ctx, "UpdateEsportGameConfig success, req:%+v", req)
	return nil
}

func (m *Manager) DeleteEsportGameConfig(ctx context.Context, req *pb.DeleteEsportGameConfigRequest) error {
	err := m.store.DeleteEsportGameConfig(ctx, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "DeleteEsportGameConfig failed, req:%+v, err:%v", req, err)
		return err
	}
	// 删除技能
	err = m.store.DelSkillByGameId(ctx, req.GetGameId())
	if err != nil {
		log.ErrorWithCtx(ctx, "DeleteEsportGameConfig failed to DelSkillByGameId, req:%+v, err:%v", req, err)
	}

	// 删除接单侧技能
	_, err = m.rpc.EsportHallCli.DelSkillProduct(ctx, &esport_hall.DelSkillProductRequest{
		SkillId: []uint32{req.GetGameId()},
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "DeleteEsportGameConfig failed, req:%+v, err:%v", req, err)
	}
	go func() {
		goCtx, cancel := context.WithTimeout(context.Background(), time.Second*5)
		defer cancel()
		if err := m.cache.DelGameDetailById(goCtx, req.GetGameId()); err != nil {
			log.ErrorWithCtx(goCtx, "DeleteEsportGameConfig failed to DelGameDetailById, id:%d, err:%v", req.GetGameId(), err)
		}
	}()

	log.InfoWithCtx(ctx, "DeleteEsportGameConfig success, req:%+v", req)
	return nil
}

func (m *Manager) GetEsportGameConfigListByPage(ctx context.Context, req *pb.GetEsportGameConfigListByPageRequest) (*pb.GetEsportGameConfigListByPageResponse, error) {
	out := &pb.GetEsportGameConfigListByPageResponse{}
	configList, err := m.store.GetEsportGameConfigListByPage(ctx, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetEsportGameConfigListByPage failed, req:%+v, err:%v", req, err)
		return out, err
	}
	cnt, err := m.store.GetEsportGameConfigCnt(ctx, req.GetGameName(), req.GetGameType())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetEsportGameConfigListByPage failed to get cnt, req:%+v, err:%v", req, err)
		return out, err
	}
	out.ItemList = configList
	out.TotalCnt = uint32(cnt)
	return out, nil
}

func (m *Manager) BatchGetGameInfoByNames(c context.Context, nameList []string) (*pb.BatchGetGameInfoByNamesResponse, error) {
	out := &pb.BatchGetGameInfoByNamesResponse{}
	list, err := m.store.BatchGetGameInfoByNames(c, nameList)
	if err != nil {
		log.ErrorWithCtx(c, "BatchGetGameInfoByNames failed, nameList:%v, err:%v", nameList, err)
		return out, err
	}
	out.GameList = list
	return out, nil
}

// 根据游戏名模糊搜索游戏
func (m *Manager) GetGameListByGameNameFuzzy(c context.Context, name string) ([]*pb.SimpleGameInfo, error) {
	out := make([]*pb.SimpleGameInfo, 0)
	list, err := m.store.GetGameListByGameNameFuzzy(c, name)
	if err != nil {
		log.ErrorWithCtx(c, "GetGameListByGameName failed, name:%s, err:%v", name, err)
		return out, err
	}
	return list, nil
}

// GetGameDetailById 获取游戏详情
func (m *Manager) GetGameDetailById(c context.Context, id uint32) (*pb.GetGameDetailByIdResponse, error) {
	resp := &pb.GetGameDetailByIdResponse{}
	resp.MinimumPrice = m.bc.GetMinimumPrice()

	gameInfo, err := m.cache.GetGameDetailById(c, id)
	if err != nil {
		log.ErrorWithCtx(c, "GetGameDetailById failed to get from cache, id:%d, err:%v", id, err)
	} else {
		log.DebugWithCtx(c, "GetGameDetailById get from cache, id:%d", id)
		if gameInfo.GetName() == "" {
			log.ErrorWithCtx(c, "GetGameDetailById invalid game, id:%d", id)
			return resp, protocol.NewExactServerError(codes.Ok, status.ErrRequestParamInvalid, "游戏配置不存在")
		}
		resp.Config = gameInfo
		return resp, nil
	}

	info, err := m.store.GetGameDetailById(c, id)
	if err != nil {
		log.ErrorWithCtx(c, "GetGameDetailById failed, id:%d, err:%v", id, err)
		return resp, err
	}
	// 填充默认文案
	if info.GetSkillDesc() == "" {
		info.SkillDesc = defaultSkillDesc
	}

	// 读取成功设置cache
	go func() {
		goCtx, cancel := context.WithTimeout(context.Background(), time.Second*5)
		defer cancel()
		if err := m.cache.SetGameDetail(goCtx, info); err != nil {
			log.ErrorWithCtx(goCtx, "GetGameDetailById failed to SetGameDetailById, id:%d, err:%v", id, err)
			return
		}
	}()

	if info.GetName() == "" {
		log.ErrorWithCtx(c, "GetGameDetailById invalid game, id:%d", id)
		return resp, protocol.NewExactServerError(codes.Ok, status.ErrRequestParamInvalid, "游戏配置不存在")
	} else {
		resp.Config = info
		return resp, nil
	}

}

func (m *Manager) GetMinimumPrice() uint32 {
	return m.bc.GetMinimumPrice()
}

func (m *Manager) GetGameDetailByIds(c context.Context, ids []uint32) (*pb.GetGameDetailByIdsResponse, error) {
	resp := &pb.GetGameDetailByIdsResponse{}
	resp.MinimumPrice = m.bc.GetMinimumPrice()
	if len(ids) == 0 {
		return resp, protocol.NewExactServerError(codes.Ok, status.ErrRequestParamInvalid, "请求列表为空")
	}
	// get from cache
	needToReloadList := make([]uint32, 0)
	infoMap, err := m.cache.BatchGetGameDetailByIds(c, ids)
	if err != nil {
		log.ErrorWithCtx(c, "GetGameDetailByIds failed to get from cache, ids:%+v, err:%v", ids, err)
		needToReloadList = append(needToReloadList, ids...)
	} else {
		for _, id := range ids {
			if infoMap[id] == nil {
				needToReloadList = append(needToReloadList, id)
			} else if infoMap[id].Name != "" { // 跳过空缓存
				resp.ConfigList = append(resp.ConfigList, infoMap[id])
			} else {
				log.ErrorWithCtx(c, "GetGameDetailByIds 游戏配置不存在, id:%d", id)
			}
		}
	}

	if len(needToReloadList) == 0 {
		log.DebugWithCtx(c, "GetGameDetailByIds get from cache, ids:%+v", ids)
		return resp, nil
	}

	log.DebugWithCtx(c, "GetGameDetailByIds need to reload, ids:%+v", needToReloadList)
	configList, err := m.store.GetGameDetailByIds(c, needToReloadList)
	if err != nil {
		log.ErrorWithCtx(c, "GetGameDetailByIds failed, id:%+v, err:%v", needToReloadList, err)
		return resp, err
	}

	// 默认文案
	for _, item := range configList {
		if item.GetSkillDesc() == "" {
			item.SkillDesc = defaultSkillDesc
		}
	}

	resp.ConfigList = append(resp.ConfigList, configList...)

	go func() {
		goCtx, cancel := context.WithTimeout(context.Background(), time.Second*5)
		defer cancel()
		idMap := make(map[uint32]bool)
		for _, item := range configList {
			idMap[item.GetGameId()] = true
		}
		for _, id := range needToReloadList {
			if _, ok := idMap[id]; !ok {
				// 不存在也缓存
				configList = append(configList, &pb.EsportGameConfig{GameId: id})
			}
		}
		if err := m.cache.BatchSetGameDetail(goCtx, configList); err != nil {
			log.ErrorWithCtx(goCtx, "GetGameDetailByIds failed to BatchSetGameDetail, ids:%+v, err:%v", needToReloadList, err)
			return
		}
	}()

	return resp, nil
}

func (m *Manager) GetAllGameCardConfig(ctx context.Context) (*pb.GetAllGameCardConfigResponse, error) {
	allGameConfig, err := m.store.GetAllGameConfig(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAllGameCardConfig failed, err:%v", err)
		return nil, err
	}

	resp := &pb.GetAllGameCardConfigResponse{}
	for _, config := range allGameConfig {
		cardInfoItemList := make([]*pb.GameCardInfoItem, 0, len(config.GameCardInfoList))
		for _, item := range config.GameCardInfoList {
			cardInfoItemList = append(cardInfoItemList, &pb.GameCardInfoItem{
				ItemName: item.ItemName,
				ItemList: item.ItemList,
				Tips:     item.Tips,
			})
		}
		resp.GameCardConfigList = append(resp.GameCardConfigList, &pb.GameCardConfig{
			GameId:               config.GameId,
			GameName:             config.GameName,
			GameCardInfoItemList: cardInfoItemList,
		})
	}

	return resp, nil
}
