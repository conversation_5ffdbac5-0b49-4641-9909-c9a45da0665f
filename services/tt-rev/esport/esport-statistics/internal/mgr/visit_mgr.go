package mgr

import (
	"context"
	"encoding/json"
	"fmt"
	"golang.52tt.com/pkg/log"
	pb "golang.52tt.com/protocol/services/esport-statistics"
	"golang.52tt.com/services/tt-rev/esport/esport-statistics/internal/store"
	"io/ioutil"
	"net/http"
	"strconv"
	"time"
)

// GetUserEnterCoachImPageCount 获取用户访问大神IM页次数
func (m *Manager) GetUserEnterCoachImPageCount(ctx context.Context, req *pb.ReportUserVisitIMPageRequest) (*pb.ReportUserVisitIMPageResponse, error) {
	out := &pb.ReportUserVisitIMPageResponse{}
	now := time.Now()

	visitCnt, err := m.cache.GetUserEnterCoachImPageFlag(ctx, req.GetUid(), uint32(now.Day()), req.GetCoachUid(), m.bc.GetIMVisitCacheNotIncludeDay())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetUserEnterCoachImPageCount failed, uid:%d, coachUid:%d, err:%v", req.GetUid(), req.GetCoachUid(), err)
		return out, err
	}

	out.VisitCnt = uint32(visitCnt)
	return out, nil
}

// IncrUserEnterCoachImPageFlag 记录用户访问大神IM页次数
func (m *Manager) IncrUserEnterCoachImPageFlag(ctx context.Context, req *pb.ReportUserVisitIMPageRequest) (*pb.ReportUserVisitIMPageResponse, error) {
	now := time.Now()

	exp := time.Duration(m.bc.GetIMVisitCacheExpSec()) * time.Second
	visitCnt, err := m.cache.IncrUserEnterCoachImPageFlag(ctx, req.GetUid(), uint32(now.Day()), req.GetCoachUid(), m.bc.GetIMVisitCacheNotIncludeDay(), exp)
	if err != nil {
		log.ErrorWithCtx(ctx, "IncrUserEnterCoachImPageFlag failed, uid:%d, coachUid:%d, err:%v", req.GetUid(), req.GetCoachUid(), err)
		return &pb.ReportUserVisitIMPageResponse{}, err
	}

	log.DebugWithCtx(ctx, "IncrUserEnterCoachImPageFlag OK, req:%+v", req)
	return &pb.ReportUserVisitIMPageResponse{VisitCnt: uint32(visitCnt)}, nil
}

// ReportUserVisitCoachGameCard 用户访问大神游戏卡片信息上报
func (m *Manager) ReportUserVisitCoachGameCard(ctx context.Context, req *pb.ReportUserVisitGameCardRequest) (*pb.ReportUserVisitGameCardResponse, error) {
	out := &pb.ReportUserVisitGameCardResponse{}

	if req.GetUid() == req.GetCoachUid() {
		return out, nil
	}

	record := &store.CoachVisitRecord{
		CoachId:    req.GetCoachUid(),
		UserId:     req.GetUid(),
		VisitCount: 1,
		GameId:     req.GetGameId(),
		VisitTime:  time.Now(),
	}
	// 更新访问次数
	rowsEffected, err := m.store.UpdateCoachVisitRecord(ctx, record)
	if err != nil {
		log.ErrorWithCtx(ctx, "ReportUserVisitCoachGameCard failed, record:%+v, err:%v", record, err)
		return out, err
	}

	if rowsEffected == 0 {
		err = m.store.InsertCoachVisitRecord(ctx, record)
		if err != nil {
			log.ErrorWithCtx(ctx, "ReportUserVisitCoachGameCard failed, record:%+v, err:%v", record, err)
			return out, err
		}

		exist, _, err := m.cache.GetCoachGameDetailUV(ctx, req.GetCoachUid())
		if err != nil {
			log.ErrorWithCtx(ctx, "GetCoachBeVisitUserCount failed, coachUid:%d, err:%v", req.GetCoachUid(), err)
			return out, err
		}

		if exist {
			_ = m.cache.IncrCoachGameDetailUV(ctx, req.GetCoachUid())
		}
	}

	log.DebugWithCtx(ctx, "ReportUserVisitCoachGameCard OK, record:%+v", record)
	return out, nil
}

// GetCoachBeVisitUserCount 获取大神谁看过我UV
func (m *Manager) GetCoachBeVisitUserCount(ctx context.Context, req *pb.GetBeVisitorRecordCountRequest) (*pb.GetBeVisitorRecordCountResponse, error) {
	out := &pb.GetBeVisitorRecordCountResponse{}

	cnt, err := m.getCoachBeVisitUserCountWithCache(ctx, req.GetCoachUid())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetCoachBeVisitUserCount failed, coachUid:%d, err:%v", req.GetCoachUid(), err)
		return out, err
	}
	out.Count = cnt
	// 计算增量
	lastCnt, err := m.cache.GetLastVisitCnt(ctx, req.GetCoachUid())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetCoachBeVisitUserCount failed, coachUid:%d, err:%v", req.GetCoachUid(), err)
	} else {
		out.Growth = cnt - uint32(lastCnt)
	}

	return out, nil
}

func (m *Manager) getCoachBeVisitUserCountWithCache(ctx context.Context, uid uint32) (uint32, error) {
	// load from cache
	exist, cnt, err := m.cache.GetCoachGameDetailUV(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetCoachBeVisitUserCount failed, coachUid:%d, err:%v", uid, err)
		return 0, err
	}

	if exist {
		return cnt, nil
	}
	// load from store
	cnt, err = m.loadCoachUVAndUpdateCache(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetCoachBeVisitUserCount failed, coachUid:%d, err:%v", uid, err)
		return 0, err
	}

	return cnt, nil
}

// GetCoachVisitRecordByPage 分页获取大神谁看过我记录
func (m *Manager) GetCoachVisitRecordByPage(ctx context.Context, req *pb.GetBeVisitorRecordListRequest) (*pb.GetBeVisitorRecordListResponse, error) {
	out := &pb.GetBeVisitorRecordListResponse{}

	limit := req.GetLimit()
	offset := req.GetOffset()
	if limit > 30 {
		limit = 30
	}
	// 首页才更新
	if req.GetOffset() == 0 {
		cnt, err := m.getCoachBeVisitUserCountWithCache(ctx, req.GetCoachUid())
		if err != nil {
			log.ErrorWithCtx(ctx, "GetCoachVisitRecordByPage GetCoachBeVisitUserCount failed, coachUid:%d, err:%v", req.GetCoachUid(), err)
			return out, nil
		}
		// 更新上次查看时的人数
		_ = m.cache.SetLastVisitCnt(ctx, req.GetCoachUid(), cnt)
	}

	list, err := m.store.GetCoachVisitRecordByPage(ctx, req.GetCoachUid(), offset, limit)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetCoachVisitRecordByPage failed, coachUid:%d, err:%v", req.GetCoachUid(), err)
		return out, err
	}
	if len(list) == 0 {
		out.RecordList = make([]*pb.VisitRecord, 0)
		out.IsEnd = true
		return out, nil
	}

	out.NextOffset = offset + uint32(len(list))
	out.IsEnd = len(list) < int(limit)

	playerList := make([]uint32, 0, len(list))
	for _, player := range list {
		playerList = append(playerList, player.UserId)
	}

	tradeMap, err := m.cache.BatchGetCoachPlayerList(ctx, req.GetCoachUid(), playerList)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetCoachVisitRecordByPage BatchGetCoachPlayerList failed, coachUid:%d, err:%v", req.GetCoachUid(), err)
		return out, err
	}

	out.RecordList = visitRecordStoreToPb(list, tradeMap)

	return out, nil
}

// transform store to pb
func visitRecordStoreToPb(recordList []*store.CoachVisitRecord, tradeMap map[uint32]uint32) []*pb.VisitRecord {
	list := make([]*pb.VisitRecord, 0)

	for _, v := range recordList {
		list = append(list, &pb.VisitRecord{
			Uid:         v.UserId,
			Count:       v.VisitCount,
			UpdateTime:  v.VisitTime.Unix(),
			GameId:      v.GameId,
			LastOrderTs: int64(tradeMap[v.UserId]),
		})
	}

	return list
}

// load and update cache
func (m *Manager) loadCoachUVAndUpdateCache(ctx context.Context, coachUid uint32) (uint32, error) {
	cnt := uint32(0)

	cnt, err := m.store.GetCoachVisitorCount(ctx, coachUid)
	if err != nil {
		log.ErrorWithCtx(ctx, "loadCoachUVAndUpdateCache failed, coachUid:%d, err:%v", coachUid, err)
		return 0, err
	}

	// update cache
	err = m.cache.SetCoachGameDetailUV(ctx, coachUid, cnt)
	if err != nil {
		log.ErrorWithCtx(ctx, "loadCoachUVAndUpdateCache failed, coachUid:%d, err:%v", coachUid, err)
		// return cnt, err
	}

	return cnt, nil
}

type dataItem struct {
	ExpoUv string `json:"expo_uv"`
}

type pageData struct {
	DataList []*dataItem `json:"data"`
}

type uvDataResponse struct {
	Code     uint32    `json:"code"`
	Success  bool      `json:"success"`
	PageData *pageData `json:"data"`
	Msg      string    `json:"msg"`
}

func (m *Manager) GetCoachTodayUv(ctx context.Context, uid uint32) (uint32, error) {
	todayStr := time.Now().Format("2006-01-02")
	cfg := m.bc.GetRemoteUvDataSource()
	url := fmt.Sprintf("%s?page=1&pageSize=100&start_date=%s&end_date=%s&user_id=%d&apiToken=%s",
		cfg.Url, todayStr, todayStr, uid, cfg.ApiToken)

	resp, err := http.Get(url)
	if err != nil {
		log.ErrorWithCtx(ctx, "refreshFromRemotePlatform Error making GET request: %v, url:%s", err, url)
		return 0, err
	}
	defer resp.Body.Close()

	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		log.ErrorWithCtx(ctx, "refreshFromRemotePlatform Error reading response body: %v, url:%s", err, url)
		return 0, err
	}

	var data *uvDataResponse
	// unmarshal json 失败返回空数据，不返回err
	err = json.Unmarshal(body, &data)
	if err != nil {
		log.ErrorWithCtx(ctx, "refreshFromRemotePlatform Error parsing JSON response: %v, url:%s", err, url)
		return 0, nil
	}
	successCode := uint32(200)
	if data.Code != successCode || !data.Success {
		log.ErrorWithCtx(ctx, "refreshFromRemotePlatform Error in response:  msg:%v, code:%v, success:%v, url:%s", data.Msg, data.Code, data.Success, url)
		return 0, err
	}
	if len(data.PageData.DataList) == 0 {
		return 0, nil
	}

	uvStr := data.PageData.DataList[0].ExpoUv
	uv, err := strconv.ParseUint(uvStr, 10, 32)
	if err != nil {
		log.ErrorWithCtx(ctx, "refreshFromRemotePlatform Error parsing expo_uv: %v, url:%s", err, url)
		return 0, nil
	}
	return uint32(uv), nil
}
