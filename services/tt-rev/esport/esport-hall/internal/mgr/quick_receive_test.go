package mgr

import (
	"context"
	"testing"

	"github.com/golang/mock/gomock"
)

func TestMgr_UpdateLocalQuickReceive(t *testing.T) {
	m := NewTestMockMgr(gomock.NewController(t))
	MockCache.EXPECT().GetQuickReceiveUser(gomock.Any()).Return(make([]uint32, 0, 8), nil)
	m.UpdateLocalQuickReceive()
}

func TestMgr_AddUnReplyUser(t *testing.T) {
	m := NewTestMockMgr(gomock.NewController(t))
	MockCache.EXPECT().GetUserToRealCoach(gomock.Any(), gomock.Any(), gomock.Any()).Return(uint32(0), nil)
	MockCache.EXPECT().IsReplyedUser(gomock.Any(), gomock.Any(), gomock.Any()).Return(false, nil)
	MockCache.EXPECT().AddUnReplyUser(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
	err := m.AddUnReplyUser(context.Background(), 1, 2)
	if err != nil {
		t.Errorf("AddUnReplyUser() error = %v", err)
	}
}

func TestMgr_RemUnReplyUser(t *testing.T) {
	m := NewTestMockMgr(gomock.NewController(t))
	MockCache.EXPECT().RemUnReplyUser(gomock.Any(), gomock.Any(), gomock.Any()).Return(int64(1), nil)
	err := m.RemUnReplyUser(context.Background(), 1, 2)
	if err != nil {
		t.Errorf("RemUnReplyUser() error = %v", err)
	}
}

func TestMgr_AddUnReceiveOrder(t *testing.T) {
	m := NewTestMockMgr(gomock.NewController(t))
	MockCache.EXPECT().AddUnReceiveOrder(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
	err := m.AddUnReceiveOrder(context.Background(), 1, "order1")
	if err != nil {
		t.Errorf("AddUnReceiveOrder() error = %v", err)
	}
}
