package store

import (
    "context"
    "testing"

    "github.com/stretchr/testify/assert"
)

func TestStore_InsertUserGameCard(t *testing.T) {
    card := &UserGameCard{
        UID:    1,
        GameId: 1,
        CardInfo: &CardInfo{
            ItemList: []*UserGameCardInfoItem{
                {
                    ItemName: "test item",
                    Content:  "test content",
                },
            },
        },
    }

    id, err := testStore.InsertUserGameCard(context.Background(), card)
    assert.NoError(t, err)
    assert.NotZero(t, id)

    // Defer a cleanup function to delete the test data
    defer func() {
        err := testStore.DelUserGameCard(context.Background(), card.UID, id)
        assert.NoError(t, err)
    }()
}

func TestStore_UpdateUserGameCard(t *testing.T) {
    card := &UserGameCard{
        ID:     1,
        UID:    1,
        GameId: 1,
        CardInfo: &CardInfo{
            ItemList: []*UserGameCardInfoItem{
                {
                    ItemName: "test item updated",
                    Content:  "test content updated",
                },
            },
        },
    }

    err := testStore.UpdateUserGameCard(context.Background(), card)
    assert.NoError(t, err)
}

func TestStore_DelUserGameCard(t *testing.T) {
    err := testStore.DelUserGameCard(context.Background(), 1, 1)
    assert.NoError(t, err)
}

func TestStore_GetUserGameCardById(t *testing.T) {
    card, err := testStore.GetUserGameCardById(context.Background(), 1)
    assert.NoError(t, err)
    assert.NotNil(t, card)
}

func TestStore_GetUserGameCardList(t *testing.T) {
    cards, err := testStore.GetUserGameCardList(context.Background(), 1)
    assert.NoError(t, err)
    assert.NotNil(t, cards)
}

func TestStore_HasUserGameCard(t *testing.T) {
    hasCard, err := testStore.HadUserGameCard(context.Background(), 1, 1)
    assert.NoError(t, err)
    assert.NotNil(t, hasCard)
}

func TestStore_GetUserGameCardByGameIdLatest(t *testing.T) {
    card, err := testStore.GetUserGameCardByGameIdLatest(context.Background(), 1, 1)
    assert.NoError(t, err)
    assert.NotNil(t, card)
}

func TestStore_BatchInsertUserGameCard(t *testing.T) {
    cards := []*UserGameCard{
        {
            UID:    1,
            GameId: 1,
            CardInfo: &CardInfo{
                ItemList: []*UserGameCardInfoItem{
                    {
                        ItemName: "test item",
                        Content:  "test content",
                    },
                },
            },
        },
    }

    err := testStore.BatchInsertUserGameCard(context.Background(), cards)
    assert.NoError(t, err)

    // Defer a cleanup function to delete the test data
    defer func() {
        for _, card := range cards {
            err := testStore.DelUserGameCard(context.Background(), card.UID, card.ID)
            assert.NoError(t, err)
        }
    }()
}

func TestStore_GenUserGameCardID(t *testing.T) {
    id := testStore.GenUserGameCardID(context.Background())
    assert.NotZero(t, id)
}
