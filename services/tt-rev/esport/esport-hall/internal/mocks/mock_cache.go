// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/services/tt-rev/esport/esport-hall/internal/cache (interfaces: ICache)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"
	time "time"

	redis "github.com/go-redis/redis/v8"
	gomock "github.com/golang/mock/gomock"
	store "golang.52tt.com/services/tt-rev/esport/esport-hall/internal/store"
)

// MockICache is a mock of ICache interface.
type MockICache struct {
	ctrl     *gomock.Controller
	recorder *MockICacheMockRecorder
}

// MockICacheMockRecorder is the mock recorder for MockICache.
type MockICacheMockRecorder struct {
	mock *MockICache
}

// NewMockICache creates a new mock instance.
func NewMockICache(ctrl *gomock.Controller) *MockICache {
	mock := &MockICache{ctrl: ctrl}
	mock.recorder = &MockICacheMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockICache) EXPECT() *MockICacheMockRecorder {
	return m.recorder
}

// AddChatCoach mocks base method.
func (m *MockICache) AddChatCoach(arg0 context.Context, arg1, arg2 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddChatCoach", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddChatCoach indicates an expected call of AddChatCoach.
func (mr *MockICacheMockRecorder) AddChatCoach(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddChatCoach", reflect.TypeOf((*MockICache)(nil).AddChatCoach), arg0, arg1, arg2)
}

// AddCoachExposeGradingCount mocks base method.
func (m *MockICache) AddCoachExposeGradingCount(arg0 context.Context, arg1, arg2, arg3 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddCoachExposeGradingCount", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddCoachExposeGradingCount indicates an expected call of AddCoachExposeGradingCount.
func (mr *MockICacheMockRecorder) AddCoachExposeGradingCount(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddCoachExposeGradingCount", reflect.TypeOf((*MockICache)(nil).AddCoachExposeGradingCount), arg0, arg1, arg2, arg3)
}

// AddFirstRoundSwitchCnt mocks base method.
func (m *MockICache) AddFirstRoundSwitchCnt(arg0 context.Context, arg1, arg2 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddFirstRoundSwitchCnt", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddFirstRoundSwitchCnt indicates an expected call of AddFirstRoundSwitchCnt.
func (mr *MockICacheMockRecorder) AddFirstRoundSwitchCnt(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddFirstRoundSwitchCnt", reflect.TypeOf((*MockICache)(nil).AddFirstRoundSwitchCnt), arg0, arg1, arg2)
}

// AddIgnoreRecommendCoach mocks base method.
func (m *MockICache) AddIgnoreRecommendCoach(arg0 context.Context, arg1, arg2 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddIgnoreRecommendCoach", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddIgnoreRecommendCoach indicates an expected call of AddIgnoreRecommendCoach.
func (mr *MockICacheMockRecorder) AddIgnoreRecommendCoach(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddIgnoreRecommendCoach", reflect.TypeOf((*MockICache)(nil).AddIgnoreRecommendCoach), arg0, arg1, arg2)
}

// AddNewSkillProductOrderLy mocks base method.
func (m *MockICache) AddNewSkillProductOrderLy(arg0 context.Context, arg1 uint32, arg2 []*store.SkillProduct) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddNewSkillProductOrderLy", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddNewSkillProductOrderLy indicates an expected call of AddNewSkillProductOrderLy.
func (mr *MockICacheMockRecorder) AddNewSkillProductOrderLy(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddNewSkillProductOrderLy", reflect.TypeOf((*MockICache)(nil).AddNewSkillProductOrderLy), arg0, arg1, arg2)
}

// AddOldSkillProductOrderly mocks base method.
func (m *MockICache) AddOldSkillProductOrderly(arg0 context.Context, arg1 uint32, arg2 []*store.SkillProduct) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddOldSkillProductOrderly", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddOldSkillProductOrderly indicates an expected call of AddOldSkillProductOrderly.
func (mr *MockICacheMockRecorder) AddOldSkillProductOrderly(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddOldSkillProductOrderly", reflect.TypeOf((*MockICache)(nil).AddOldSkillProductOrderly), arg0, arg1, arg2)
}

// AddReceiveOrder mocks base method.
func (m *MockICache) AddReceiveOrder(arg0 context.Context, arg1 uint32, arg2 string, arg3 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddReceiveOrder", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddReceiveOrder indicates an expected call of AddReceiveOrder.
func (mr *MockICacheMockRecorder) AddReceiveOrder(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddReceiveOrder", reflect.TypeOf((*MockICache)(nil).AddReceiveOrder), arg0, arg1, arg2, arg3)
}

// AddRecommendTabExpose mocks base method.
func (m *MockICache) AddRecommendTabExpose(arg0 context.Context, arg1, arg2, arg3, arg4 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddRecommendTabExpose", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddRecommendTabExpose indicates an expected call of AddRecommendTabExpose.
func (mr *MockICacheMockRecorder) AddRecommendTabExpose(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddRecommendTabExpose", reflect.TypeOf((*MockICache)(nil).AddRecommendTabExpose), arg0, arg1, arg2, arg3, arg4)
}

// AddReplyedUser mocks base method.
func (m *MockICache) AddReplyedUser(arg0 context.Context, arg1, arg2 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddReplyedUser", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddReplyedUser indicates an expected call of AddReplyedUser.
func (mr *MockICacheMockRecorder) AddReplyedUser(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddReplyedUser", reflect.TypeOf((*MockICache)(nil).AddReplyedUser), arg0, arg1, arg2)
}

// AddToDailyTotalBonus mocks base method.
func (m *MockICache) AddToDailyTotalBonus(arg0 context.Context, arg1 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddToDailyTotalBonus", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddToDailyTotalBonus indicates an expected call of AddToDailyTotalBonus.
func (mr *MockICacheMockRecorder) AddToDailyTotalBonus(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddToDailyTotalBonus", reflect.TypeOf((*MockICache)(nil).AddToDailyTotalBonus), arg0, arg1)
}

// AddUnReceiveOrder mocks base method.
func (m *MockICache) AddUnReceiveOrder(arg0 context.Context, arg1 uint32, arg2 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddUnReceiveOrder", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddUnReceiveOrder indicates an expected call of AddUnReceiveOrder.
func (mr *MockICacheMockRecorder) AddUnReceiveOrder(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddUnReceiveOrder", reflect.TypeOf((*MockICache)(nil).AddUnReceiveOrder), arg0, arg1, arg2)
}

// AddUnReplyUser mocks base method.
func (m *MockICache) AddUnReplyUser(arg0 context.Context, arg1, arg2 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddUnReplyUser", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddUnReplyUser indicates an expected call of AddUnReplyUser.
func (mr *MockICacheMockRecorder) AddUnReplyUser(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddUnReplyUser", reflect.TypeOf((*MockICache)(nil).AddUnReplyUser), arg0, arg1, arg2)
}

// AddUserExposedCoach mocks base method.
func (m *MockICache) AddUserExposedCoach(arg0 context.Context, arg1, arg2, arg3, arg4 uint32, arg5 int64, arg6 []uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddUserExposedCoach", arg0, arg1, arg2, arg3, arg4, arg5, arg6)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddUserExposedCoach indicates an expected call of AddUserExposedCoach.
func (mr *MockICacheMockRecorder) AddUserExposedCoach(arg0, arg1, arg2, arg3, arg4, arg5, arg6 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddUserExposedCoach", reflect.TypeOf((*MockICache)(nil).AddUserExposedCoach), arg0, arg1, arg2, arg3, arg4, arg5, arg6)
}

// AddUserExposedTopCoach mocks base method.
func (m *MockICache) AddUserExposedTopCoach(arg0 context.Context, arg1, arg2 uint32, arg3 ...uint32) error {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1, arg2}
	for _, a := range arg3 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AddUserExposedTopCoach", varargs...)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddUserExposedTopCoach indicates an expected call of AddUserExposedTopCoach.
func (mr *MockICacheMockRecorder) AddUserExposedTopCoach(arg0, arg1, arg2 interface{}, arg3 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1, arg2}, arg3...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddUserExposedTopCoach", reflect.TypeOf((*MockICache)(nil).AddUserExposedTopCoach), varargs...)
}

// BatGetUserToRealCoach mocks base method.
func (m *MockICache) BatGetUserToRealCoach(arg0 context.Context, arg1 uint32, arg2 []uint32) ([]uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatGetUserToRealCoach", arg0, arg1, arg2)
	ret0, _ := ret[0].([]uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatGetUserToRealCoach indicates an expected call of BatGetUserToRealCoach.
func (mr *MockICacheMockRecorder) BatGetUserToRealCoach(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatGetUserToRealCoach", reflect.TypeOf((*MockICache)(nil).BatGetUserToRealCoach), arg0, arg1, arg2)
}

// BatSetSkillProductByUidGameId mocks base method.
func (m *MockICache) BatSetSkillProductByUidGameId(arg0 context.Context, arg1 map[uint32]*store.SkillProduct, arg2 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatSetSkillProductByUidGameId", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// BatSetSkillProductByUidGameId indicates an expected call of BatSetSkillProductByUidGameId.
func (mr *MockICacheMockRecorder) BatSetSkillProductByUidGameId(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatSetSkillProductByUidGameId", reflect.TypeOf((*MockICache)(nil).BatSetSkillProductByUidGameId), arg0, arg1, arg2)
}

// BatchGetCoachExposeGradingCount mocks base method.
func (m *MockICache) BatchGetCoachExposeGradingCount(arg0 context.Context, arg1 []uint32, arg2, arg3 uint32) (map[uint32]uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetCoachExposeGradingCount", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(map[uint32]uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetCoachExposeGradingCount indicates an expected call of BatchGetCoachExposeGradingCount.
func (mr *MockICacheMockRecorder) BatchGetCoachExposeGradingCount(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetCoachExposeGradingCount", reflect.TypeOf((*MockICache)(nil).BatchGetCoachExposeGradingCount), arg0, arg1, arg2, arg3)
}

// BatchGetProductByProductIds mocks base method.
func (m *MockICache) BatchGetProductByProductIds(arg0 context.Context, arg1 []uint32) (map[uint32]*store.SkillProduct, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetProductByProductIds", arg0, arg1)
	ret0, _ := ret[0].(map[uint32]*store.SkillProduct)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetProductByProductIds indicates an expected call of BatchGetProductByProductIds.
func (mr *MockICacheMockRecorder) BatchGetProductByProductIds(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetProductByProductIds", reflect.TypeOf((*MockICache)(nil).BatchGetProductByProductIds), arg0, arg1)
}

// BatchGetQuickReceiveSwitchStartTime mocks base method.
func (m *MockICache) BatchGetQuickReceiveSwitchStartTime(arg0 context.Context, arg1 []uint32) (map[uint32]uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetQuickReceiveSwitchStartTime", arg0, arg1)
	ret0, _ := ret[0].(map[uint32]uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetQuickReceiveSwitchStartTime indicates an expected call of BatchGetQuickReceiveSwitchStartTime.
func (mr *MockICacheMockRecorder) BatchGetQuickReceiveSwitchStartTime(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetQuickReceiveSwitchStartTime", reflect.TypeOf((*MockICache)(nil).BatchGetQuickReceiveSwitchStartTime), arg0, arg1)
}

// BatchGetRecommendTimes mocks base method.
func (m *MockICache) BatchGetRecommendTimes(arg0 context.Context, arg1 uint32, arg2 []uint32) (map[uint32]uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetRecommendTimes", arg0, arg1, arg2)
	ret0, _ := ret[0].(map[uint32]uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetRecommendTimes indicates an expected call of BatchGetRecommendTimes.
func (mr *MockICacheMockRecorder) BatchGetRecommendTimes(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetRecommendTimes", reflect.TypeOf((*MockICache)(nil).BatchGetRecommendTimes), arg0, arg1, arg2)
}

// BatchGetSkillProductByUidGameId mocks base method.
func (m *MockICache) BatchGetSkillProductByUidGameId(arg0 context.Context, arg1 []uint32, arg2 uint32) (map[uint32]*store.SkillProduct, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetSkillProductByUidGameId", arg0, arg1, arg2)
	ret0, _ := ret[0].(map[uint32]*store.SkillProduct)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetSkillProductByUidGameId indicates an expected call of BatchGetSkillProductByUidGameId.
func (mr *MockICacheMockRecorder) BatchGetSkillProductByUidGameId(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetSkillProductByUidGameId", reflect.TypeOf((*MockICache)(nil).BatchGetSkillProductByUidGameId), arg0, arg1, arg2)
}

// BatchGetUSerSetting mocks base method.
func (m *MockICache) BatchGetUSerSetting(arg0 context.Context, arg1 []uint32) (map[uint32]*store.UserSetting, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetUSerSetting", arg0, arg1)
	ret0, _ := ret[0].(map[uint32]*store.UserSetting)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetUSerSetting indicates an expected call of BatchGetUSerSetting.
func (mr *MockICacheMockRecorder) BatchGetUSerSetting(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetUSerSetting", reflect.TypeOf((*MockICache)(nil).BatchGetUSerSetting), arg0, arg1)
}

// BatchGetUserOnline mocks base method.
func (m *MockICache) BatchGetUserOnline(arg0 context.Context, arg1 []uint32) (map[uint32]bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetUserOnline", arg0, arg1)
	ret0, _ := ret[0].(map[uint32]bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetUserOnline indicates an expected call of BatchGetUserOnline.
func (mr *MockICacheMockRecorder) BatchGetUserOnline(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetUserOnline", reflect.TypeOf((*MockICache)(nil).BatchGetUserOnline), arg0, arg1)
}

// BatchGetUserSex mocks base method.
func (m *MockICache) BatchGetUserSex(arg0 context.Context, arg1 []uint32) (map[uint32]uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetUserSex", arg0, arg1)
	ret0, _ := ret[0].(map[uint32]uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetUserSex indicates an expected call of BatchGetUserSex.
func (mr *MockICacheMockRecorder) BatchGetUserSex(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetUserSex", reflect.TypeOf((*MockICache)(nil).BatchGetUserSex), arg0, arg1)
}

// BatchIncrRecommendTimes mocks base method.
func (m *MockICache) BatchIncrRecommendTimes(arg0 context.Context, arg1 uint32, arg2 []uint32, arg3 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchIncrRecommendTimes", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// BatchIncrRecommendTimes indicates an expected call of BatchIncrRecommendTimes.
func (mr *MockICacheMockRecorder) BatchIncrRecommendTimes(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchIncrRecommendTimes", reflect.TypeOf((*MockICache)(nil).BatchIncrRecommendTimes), arg0, arg1, arg2, arg3)
}

// BatchSetProductByProductIds mocks base method.
func (m *MockICache) BatchSetProductByProductIds(arg0 context.Context, arg1 map[uint32]*store.SkillProduct) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchSetProductByProductIds", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// BatchSetProductByProductIds indicates an expected call of BatchSetProductByProductIds.
func (mr *MockICacheMockRecorder) BatchSetProductByProductIds(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchSetProductByProductIds", reflect.TypeOf((*MockICache)(nil).BatchSetProductByProductIds), arg0, arg1)
}

// BatchSetUSerSetting mocks base method.
func (m *MockICache) BatchSetUSerSetting(arg0 context.Context, arg1 map[uint32]*store.UserSetting) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchSetUSerSetting", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// BatchSetUSerSetting indicates an expected call of BatchSetUSerSetting.
func (mr *MockICacheMockRecorder) BatchSetUSerSetting(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchSetUSerSetting", reflect.TypeOf((*MockICache)(nil).BatchSetUSerSetting), arg0, arg1)
}

// ClearQuickReceiveCacheRound mocks base method.
func (m *MockICache) ClearQuickReceiveCacheRound(arg0 context.Context, arg1 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ClearQuickReceiveCacheRound", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// ClearQuickReceiveCacheRound indicates an expected call of ClearQuickReceiveCacheRound.
func (mr *MockICacheMockRecorder) ClearQuickReceiveCacheRound(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ClearQuickReceiveCacheRound", reflect.TypeOf((*MockICache)(nil).ClearQuickReceiveCacheRound), arg0, arg1)
}

// Close mocks base method.
func (m *MockICache) Close() error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Close")
	ret0, _ := ret[0].(error)
	return ret0
}

// Close indicates an expected call of Close.
func (mr *MockICacheMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockICache)(nil).Close))
}

// CloseQuickReceiveSwitch mocks base method.
func (m *MockICache) CloseQuickReceiveSwitch(arg0 context.Context, arg1 uint32) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CloseQuickReceiveSwitch", arg0, arg1)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CloseQuickReceiveSwitch indicates an expected call of CloseQuickReceiveSwitch.
func (mr *MockICacheMockRecorder) CloseQuickReceiveSwitch(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CloseQuickReceiveSwitch", reflect.TypeOf((*MockICache)(nil).CloseQuickReceiveSwitch), arg0, arg1)
}

// CntTagKey mocks base method.
func (m *MockICache) CntTagKey(arg0 context.Context, arg1 string) (uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CntTagKey", arg0, arg1)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CntTagKey indicates an expected call of CntTagKey.
func (mr *MockICacheMockRecorder) CntTagKey(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CntTagKey", reflect.TypeOf((*MockICache)(nil).CntTagKey), arg0, arg1)
}

// CountUnReceiveOrder mocks base method.
func (m *MockICache) CountUnReceiveOrder(arg0 context.Context, arg1, arg2 uint32) (uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CountUnReceiveOrder", arg0, arg1, arg2)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CountUnReceiveOrder indicates an expected call of CountUnReceiveOrder.
func (mr *MockICacheMockRecorder) CountUnReceiveOrder(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CountUnReceiveOrder", reflect.TypeOf((*MockICache)(nil).CountUnReceiveOrder), arg0, arg1, arg2)
}

// CountUnReplyUser mocks base method.
func (m *MockICache) CountUnReplyUser(arg0 context.Context, arg1, arg2 uint32) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CountUnReplyUser", arg0, arg1, arg2)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CountUnReplyUser indicates an expected call of CountUnReplyUser.
func (mr *MockICacheMockRecorder) CountUnReplyUser(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CountUnReplyUser", reflect.TypeOf((*MockICache)(nil).CountUnReplyUser), arg0, arg1, arg2)
}

// DecreaseToDailyTotalBonus mocks base method.
func (m *MockICache) DecreaseToDailyTotalBonus(arg0 context.Context, arg1 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DecreaseToDailyTotalBonus", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// DecreaseToDailyTotalBonus indicates an expected call of DecreaseToDailyTotalBonus.
func (mr *MockICacheMockRecorder) DecreaseToDailyTotalBonus(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DecreaseToDailyTotalBonus", reflect.TypeOf((*MockICache)(nil).DecreaseToDailyTotalBonus), arg0, arg1)
}

// DelCantOpenQuickReceiveFlag mocks base method.
func (m *MockICache) DelCantOpenQuickReceiveFlag(arg0 context.Context, arg1 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelCantOpenQuickReceiveFlag", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// DelCantOpenQuickReceiveFlag indicates an expected call of DelCantOpenQuickReceiveFlag.
func (mr *MockICacheMockRecorder) DelCantOpenQuickReceiveFlag(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelCantOpenQuickReceiveFlag", reflect.TypeOf((*MockICache)(nil).DelCantOpenQuickReceiveFlag), arg0, arg1)
}

// DelIncentiveTaskLastViewTime mocks base method.
func (m *MockICache) DelIncentiveTaskLastViewTime(arg0 context.Context, arg1 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelIncentiveTaskLastViewTime", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// DelIncentiveTaskLastViewTime indicates an expected call of DelIncentiveTaskLastViewTime.
func (mr *MockICacheMockRecorder) DelIncentiveTaskLastViewTime(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelIncentiveTaskLastViewTime", reflect.TypeOf((*MockICache)(nil).DelIncentiveTaskLastViewTime), arg0, arg1)
}

// DelProductByProductId mocks base method.
func (m *MockICache) DelProductByProductId(arg0 context.Context, arg1 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelProductByProductId", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// DelProductByProductId indicates an expected call of DelProductByProductId.
func (mr *MockICacheMockRecorder) DelProductByProductId(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelProductByProductId", reflect.TypeOf((*MockICache)(nil).DelProductByProductId), arg0, arg1)
}

// DelQuickReceiveOffline mocks base method.
func (m *MockICache) DelQuickReceiveOffline(arg0 context.Context, arg1 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelQuickReceiveOffline", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// DelQuickReceiveOffline indicates an expected call of DelQuickReceiveOffline.
func (mr *MockICacheMockRecorder) DelQuickReceiveOffline(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelQuickReceiveOffline", reflect.TypeOf((*MockICache)(nil).DelQuickReceiveOffline), arg0, arg1)
}

// DelSkillProductOrderly mocks base method.
func (m *MockICache) DelSkillProductOrderly(arg0 context.Context, arg1 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelSkillProductOrderly", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// DelSkillProductOrderly indicates an expected call of DelSkillProductOrderly.
func (mr *MockICacheMockRecorder) DelSkillProductOrderly(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelSkillProductOrderly", reflect.TypeOf((*MockICache)(nil).DelSkillProductOrderly), arg0, arg1)
}

// DelUserSkillProduct mocks base method.
func (m *MockICache) DelUserSkillProduct(arg0 context.Context, arg1 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelUserSkillProduct", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// DelUserSkillProduct indicates an expected call of DelUserSkillProduct.
func (mr *MockICacheMockRecorder) DelUserSkillProduct(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelUserSkillProduct", reflect.TypeOf((*MockICache)(nil).DelUserSkillProduct), arg0, arg1)
}

// DelUserToRealCoach mocks base method.
func (m *MockICache) DelUserToRealCoach(arg0 context.Context, arg1, arg2 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelUserToRealCoach", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// DelUserToRealCoach indicates an expected call of DelUserToRealCoach.
func (mr *MockICacheMockRecorder) DelUserToRealCoach(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelUserToRealCoach", reflect.TypeOf((*MockICache)(nil).DelUserToRealCoach), arg0, arg1, arg2)
}

// GetCantOpenQuickReceiveFlag mocks base method.
func (m *MockICache) GetCantOpenQuickReceiveFlag(arg0 context.Context, arg1 uint32) (uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCantOpenQuickReceiveFlag", arg0, arg1)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCantOpenQuickReceiveFlag indicates an expected call of GetCantOpenQuickReceiveFlag.
func (mr *MockICacheMockRecorder) GetCantOpenQuickReceiveFlag(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCantOpenQuickReceiveFlag", reflect.TypeOf((*MockICache)(nil).GetCantOpenQuickReceiveFlag), arg0, arg1)
}

// GetChatCoachesByTime mocks base method.
func (m *MockICache) GetChatCoachesByTime(arg0 context.Context, arg1 uint32, arg2, arg3 int64) (map[uint32]int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChatCoachesByTime", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(map[uint32]int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChatCoachesByTime indicates an expected call of GetChatCoachesByTime.
func (mr *MockICacheMockRecorder) GetChatCoachesByTime(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChatCoachesByTime", reflect.TypeOf((*MockICache)(nil).GetChatCoachesByTime), arg0, arg1, arg2, arg3)
}

// GetCoachExposeGradingCount mocks base method.
func (m *MockICache) GetCoachExposeGradingCount(arg0 context.Context, arg1, arg2, arg3 uint32) (uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCoachExposeGradingCount", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCoachExposeGradingCount indicates an expected call of GetCoachExposeGradingCount.
func (mr *MockICacheMockRecorder) GetCoachExposeGradingCount(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCoachExposeGradingCount", reflect.TypeOf((*MockICache)(nil).GetCoachExposeGradingCount), arg0, arg1, arg2, arg3)
}

// GetCoachLeastOnlineTime mocks base method.
func (m *MockICache) GetCoachLeastOnlineTime(arg0 context.Context, arg1 uint32) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCoachLeastOnlineTime", arg0, arg1)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCoachLeastOnlineTime indicates an expected call of GetCoachLeastOnlineTime.
func (mr *MockICacheMockRecorder) GetCoachLeastOnlineTime(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCoachLeastOnlineTime", reflect.TypeOf((*MockICache)(nil).GetCoachLeastOnlineTime), arg0, arg1)
}

// GetCoachRecommendInEffect mocks base method.
func (m *MockICache) GetCoachRecommendInEffect(arg0 context.Context, arg1 uint32) ([]*store.CoachRecommend, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCoachRecommendInEffect", arg0, arg1)
	ret0, _ := ret[0].([]*store.CoachRecommend)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCoachRecommendInEffect indicates an expected call of GetCoachRecommendInEffect.
func (mr *MockICacheMockRecorder) GetCoachRecommendInEffect(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCoachRecommendInEffect", reflect.TypeOf((*MockICache)(nil).GetCoachRecommendInEffect), arg0, arg1)
}

// GetDailyTotalBonus mocks base method.
func (m *MockICache) GetDailyTotalBonus(arg0 context.Context) (uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetDailyTotalBonus", arg0)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetDailyTotalBonus indicates an expected call of GetDailyTotalBonus.
func (mr *MockICacheMockRecorder) GetDailyTotalBonus(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDailyTotalBonus", reflect.TypeOf((*MockICache)(nil).GetDailyTotalBonus), arg0)
}

// GetFirstRoundCoachUid mocks base method.
func (m *MockICache) GetFirstRoundCoachUid(arg0 context.Context, arg1 uint32) []uint32 {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetFirstRoundCoachUid", arg0, arg1)
	ret0, _ := ret[0].([]uint32)
	return ret0
}

// GetFirstRoundCoachUid indicates an expected call of GetFirstRoundCoachUid.
func (mr *MockICacheMockRecorder) GetFirstRoundCoachUid(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFirstRoundCoachUid", reflect.TypeOf((*MockICache)(nil).GetFirstRoundCoachUid), arg0, arg1)
}

// GetFirstRoundSwitchCnt mocks base method.
func (m *MockICache) GetFirstRoundSwitchCnt(arg0 context.Context, arg1, arg2 uint32) (uint64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetFirstRoundSwitchCnt", arg0, arg1, arg2)
	ret0, _ := ret[0].(uint64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetFirstRoundSwitchCnt indicates an expected call of GetFirstRoundSwitchCnt.
func (mr *MockICacheMockRecorder) GetFirstRoundSwitchCnt(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFirstRoundSwitchCnt", reflect.TypeOf((*MockICache)(nil).GetFirstRoundSwitchCnt), arg0, arg1, arg2)
}

// GetIgnoreRecommendCoaches mocks base method.
func (m *MockICache) GetIgnoreRecommendCoaches(arg0 context.Context, arg1 uint32, arg2 time.Time) ([]uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetIgnoreRecommendCoaches", arg0, arg1, arg2)
	ret0, _ := ret[0].([]uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetIgnoreRecommendCoaches indicates an expected call of GetIgnoreRecommendCoaches.
func (mr *MockICacheMockRecorder) GetIgnoreRecommendCoaches(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetIgnoreRecommendCoaches", reflect.TypeOf((*MockICache)(nil).GetIgnoreRecommendCoaches), arg0, arg1, arg2)
}

// GetIncentiveTaskAddition mocks base method.
func (m *MockICache) GetIncentiveTaskAddition(arg0 context.Context, arg1 time.Time, arg2 uint32) (uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetIncentiveTaskAddition", arg0, arg1, arg2)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetIncentiveTaskAddition indicates an expected call of GetIncentiveTaskAddition.
func (mr *MockICacheMockRecorder) GetIncentiveTaskAddition(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetIncentiveTaskAddition", reflect.TypeOf((*MockICache)(nil).GetIncentiveTaskAddition), arg0, arg1, arg2)
}

// GetIncentiveTaskLastViewTime mocks base method.
func (m *MockICache) GetIncentiveTaskLastViewTime(arg0 context.Context, arg1 uint32) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetIncentiveTaskLastViewTime", arg0, arg1)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetIncentiveTaskLastViewTime indicates an expected call of GetIncentiveTaskLastViewTime.
func (mr *MockICacheMockRecorder) GetIncentiveTaskLastViewTime(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetIncentiveTaskLastViewTime", reflect.TypeOf((*MockICache)(nil).GetIncentiveTaskLastViewTime), arg0, arg1)
}

// GetNewSkillProductOrderly mocks base method.
func (m *MockICache) GetNewSkillProductOrderly(arg0 context.Context, arg1 uint32, arg2, arg3 int64) ([]*store.SkillProduct, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetNewSkillProductOrderly", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].([]*store.SkillProduct)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNewSkillProductOrderly indicates an expected call of GetNewSkillProductOrderly.
func (mr *MockICacheMockRecorder) GetNewSkillProductOrderly(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNewSkillProductOrderly", reflect.TypeOf((*MockICache)(nil).GetNewSkillProductOrderly), arg0, arg1, arg2, arg3)
}

// GetOldSkillProductOrderly mocks base method.
func (m *MockICache) GetOldSkillProductOrderly(arg0 context.Context, arg1 uint32, arg2, arg3 int64) ([]*store.SkillProduct, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOldSkillProductOrderly", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].([]*store.SkillProduct)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOldSkillProductOrderly indicates an expected call of GetOldSkillProductOrderly.
func (mr *MockICacheMockRecorder) GetOldSkillProductOrderly(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOldSkillProductOrderly", reflect.TypeOf((*MockICache)(nil).GetOldSkillProductOrderly), arg0, arg1, arg2, arg3)
}

// GetProductByProductId mocks base method.
func (m *MockICache) GetProductByProductId(arg0 context.Context, arg1 uint32) (*store.SkillProduct, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetProductByProductId", arg0, arg1)
	ret0, _ := ret[0].(*store.SkillProduct)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetProductByProductId indicates an expected call of GetProductByProductId.
func (mr *MockICacheMockRecorder) GetProductByProductId(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetProductByProductId", reflect.TypeOf((*MockICache)(nil).GetProductByProductId), arg0, arg1)
}

// GetQuickReceiveOffline mocks base method.
func (m *MockICache) GetQuickReceiveOffline(arg0 context.Context, arg1 uint32) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetQuickReceiveOffline", arg0, arg1)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetQuickReceiveOffline indicates an expected call of GetQuickReceiveOffline.
func (mr *MockICacheMockRecorder) GetQuickReceiveOffline(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetQuickReceiveOffline", reflect.TypeOf((*MockICache)(nil).GetQuickReceiveOffline), arg0, arg1)
}

// GetQuickReceiveSwitchStartTime mocks base method.
func (m *MockICache) GetQuickReceiveSwitchStartTime(arg0 context.Context, arg1 uint32) (uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetQuickReceiveSwitchStartTime", arg0, arg1)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetQuickReceiveSwitchStartTime indicates an expected call of GetQuickReceiveSwitchStartTime.
func (mr *MockICacheMockRecorder) GetQuickReceiveSwitchStartTime(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetQuickReceiveSwitchStartTime", reflect.TypeOf((*MockICache)(nil).GetQuickReceiveSwitchStartTime), arg0, arg1)
}

// GetQuickReceiveUser mocks base method.
func (m *MockICache) GetQuickReceiveUser(arg0 context.Context) ([]uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetQuickReceiveUser", arg0)
	ret0, _ := ret[0].([]uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetQuickReceiveUser indicates an expected call of GetQuickReceiveUser.
func (mr *MockICacheMockRecorder) GetQuickReceiveUser(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetQuickReceiveUser", reflect.TypeOf((*MockICache)(nil).GetQuickReceiveUser), arg0)
}

// GetReceiveOrder mocks base method.
func (m *MockICache) GetReceiveOrder(arg0 context.Context, arg1 uint32) string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetReceiveOrder", arg0, arg1)
	ret0, _ := ret[0].(string)
	return ret0
}

// GetReceiveOrder indicates an expected call of GetReceiveOrder.
func (mr *MockICacheMockRecorder) GetReceiveOrder(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetReceiveOrder", reflect.TypeOf((*MockICache)(nil).GetReceiveOrder), arg0, arg1)
}

// GetRecommendTabExpose mocks base method.
func (m *MockICache) GetRecommendTabExpose(arg0 context.Context, arg1, arg2 uint32) ([]uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRecommendTabExpose", arg0, arg1, arg2)
	ret0, _ := ret[0].([]uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRecommendTabExpose indicates an expected call of GetRecommendTabExpose.
func (mr *MockICacheMockRecorder) GetRecommendTabExpose(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRecommendTabExpose", reflect.TypeOf((*MockICache)(nil).GetRecommendTabExpose), arg0, arg1, arg2)
}

// GetRedisClient mocks base method.
func (m *MockICache) GetRedisClient() redis.Cmdable {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRedisClient")
	ret0, _ := ret[0].(redis.Cmdable)
	return ret0
}

// GetRedisClient indicates an expected call of GetRedisClient.
func (mr *MockICacheMockRecorder) GetRedisClient() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRedisClient", reflect.TypeOf((*MockICache)(nil).GetRedisClient))
}

// GetRefuseReceiveOrderCnt mocks base method.
func (m *MockICache) GetRefuseReceiveOrderCnt(arg0 context.Context, arg1 uint32) (uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRefuseReceiveOrderCnt", arg0, arg1)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRefuseReceiveOrderCnt indicates an expected call of GetRefuseReceiveOrderCnt.
func (mr *MockICacheMockRecorder) GetRefuseReceiveOrderCnt(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRefuseReceiveOrderCnt", reflect.TypeOf((*MockICache)(nil).GetRefuseReceiveOrderCnt), arg0, arg1)
}

// GetSkillProductOrderNum mocks base method.
func (m *MockICache) GetSkillProductOrderNum(arg0 context.Context, arg1, arg2 uint32) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSkillProductOrderNum", arg0, arg1, arg2)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSkillProductOrderNum indicates an expected call of GetSkillProductOrderNum.
func (mr *MockICacheMockRecorder) GetSkillProductOrderNum(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSkillProductOrderNum", reflect.TypeOf((*MockICache)(nil).GetSkillProductOrderNum), arg0, arg1, arg2)
}

// GetUnReplyUser mocks base method.
func (m *MockICache) GetUnReplyUser(arg0 context.Context, arg1, arg2 uint32) ([]uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUnReplyUser", arg0, arg1, arg2)
	ret0, _ := ret[0].([]uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUnReplyUser indicates an expected call of GetUnReplyUser.
func (mr *MockICacheMockRecorder) GetUnReplyUser(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUnReplyUser", reflect.TypeOf((*MockICache)(nil).GetUnReplyUser), arg0, arg1, arg2)
}

// GetUserExposedCoach mocks base method.
func (m *MockICache) GetUserExposedCoach(arg0 context.Context, arg1, arg2, arg3 uint32) ([]uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserExposedCoach", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].([]uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserExposedCoach indicates an expected call of GetUserExposedCoach.
func (mr *MockICacheMockRecorder) GetUserExposedCoach(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserExposedCoach", reflect.TypeOf((*MockICache)(nil).GetUserExposedCoach), arg0, arg1, arg2, arg3)
}

// GetUserExposedTopCoach mocks base method.
func (m *MockICache) GetUserExposedTopCoach(arg0 context.Context, arg1, arg2 uint32) (map[uint32]int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserExposedTopCoach", arg0, arg1, arg2)
	ret0, _ := ret[0].(map[uint32]int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserExposedTopCoach indicates an expected call of GetUserExposedTopCoach.
func (mr *MockICacheMockRecorder) GetUserExposedTopCoach(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserExposedTopCoach", reflect.TypeOf((*MockICache)(nil).GetUserExposedTopCoach), arg0, arg1, arg2)
}

// GetUserFirstRoundAvailableCoachUids mocks base method.
func (m *MockICache) GetUserFirstRoundAvailableCoachUids(arg0 context.Context, arg1, arg2 uint32) ([]uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserFirstRoundAvailableCoachUids", arg0, arg1, arg2)
	ret0, _ := ret[0].([]uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserFirstRoundAvailableCoachUids indicates an expected call of GetUserFirstRoundAvailableCoachUids.
func (mr *MockICacheMockRecorder) GetUserFirstRoundAvailableCoachUids(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserFirstRoundAvailableCoachUids", reflect.TypeOf((*MockICache)(nil).GetUserFirstRoundAvailableCoachUids), arg0, arg1, arg2)
}

// GetUserSex mocks base method.
func (m *MockICache) GetUserSex(arg0 context.Context, arg1 uint32) (uint32, bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserSex", arg0, arg1)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(bool)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetUserSex indicates an expected call of GetUserSex.
func (mr *MockICacheMockRecorder) GetUserSex(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserSex", reflect.TypeOf((*MockICache)(nil).GetUserSex), arg0, arg1)
}

// GetUserSkillProduct mocks base method.
func (m *MockICache) GetUserSkillProduct(arg0 context.Context, arg1 uint32) ([]*store.SkillProduct, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserSkillProduct", arg0, arg1)
	ret0, _ := ret[0].([]*store.SkillProduct)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserSkillProduct indicates an expected call of GetUserSkillProduct.
func (mr *MockICacheMockRecorder) GetUserSkillProduct(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserSkillProduct", reflect.TypeOf((*MockICache)(nil).GetUserSkillProduct), arg0, arg1)
}

// GetUserToRealCoach mocks base method.
func (m *MockICache) GetUserToRealCoach(arg0 context.Context, arg1, arg2 uint32) (uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserToRealCoach", arg0, arg1, arg2)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserToRealCoach indicates an expected call of GetUserToRealCoach.
func (mr *MockICacheMockRecorder) GetUserToRealCoach(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserToRealCoach", reflect.TypeOf((*MockICache)(nil).GetUserToRealCoach), arg0, arg1, arg2)
}

// GetUserTopGamePreSelectConfig mocks base method.
func (m *MockICache) GetUserTopGamePreSelectConfig(arg0 context.Context, arg1 uint32) ([]uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserTopGamePreSelectConfig", arg0, arg1)
	ret0, _ := ret[0].([]uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserTopGamePreSelectConfig indicates an expected call of GetUserTopGamePreSelectConfig.
func (mr *MockICacheMockRecorder) GetUserTopGamePreSelectConfig(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserTopGamePreSelectConfig", reflect.TypeOf((*MockICache)(nil).GetUserTopGamePreSelectConfig), arg0, arg1)
}

// GetUserUnderlayCoach mocks base method.
func (m *MockICache) GetUserUnderlayCoach(arg0 context.Context, arg1, arg2 uint32) ([]uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserUnderlayCoach", arg0, arg1, arg2)
	ret0, _ := ret[0].([]uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserUnderlayCoach indicates an expected call of GetUserUnderlayCoach.
func (mr *MockICacheMockRecorder) GetUserUnderlayCoach(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserUnderlayCoach", reflect.TypeOf((*MockICache)(nil).GetUserUnderlayCoach), arg0, arg1, arg2)
}

// IncrQuickReceiveOfflineCnt mocks base method.
func (m *MockICache) IncrQuickReceiveOfflineCnt(arg0 context.Context, arg1 uint32) (uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IncrQuickReceiveOfflineCnt", arg0, arg1)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IncrQuickReceiveOfflineCnt indicates an expected call of IncrQuickReceiveOfflineCnt.
func (mr *MockICacheMockRecorder) IncrQuickReceiveOfflineCnt(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IncrQuickReceiveOfflineCnt", reflect.TypeOf((*MockICache)(nil).IncrQuickReceiveOfflineCnt), arg0, arg1)
}

// IncrRefuseReceiveOrderCnt mocks base method.
func (m *MockICache) IncrRefuseReceiveOrderCnt(arg0 context.Context, arg1 uint32) (uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IncrRefuseReceiveOrderCnt", arg0, arg1)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IncrRefuseReceiveOrderCnt indicates an expected call of IncrRefuseReceiveOrderCnt.
func (mr *MockICacheMockRecorder) IncrRefuseReceiveOrderCnt(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IncrRefuseReceiveOrderCnt", reflect.TypeOf((*MockICache)(nil).IncrRefuseReceiveOrderCnt), arg0, arg1)
}

// IsReplyedUser mocks base method.
func (m *MockICache) IsReplyedUser(arg0 context.Context, arg1, arg2 uint32) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IsReplyedUser", arg0, arg1, arg2)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IsReplyedUser indicates an expected call of IsReplyedUser.
func (mr *MockICacheMockRecorder) IsReplyedUser(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsReplyedUser", reflect.TypeOf((*MockICache)(nil).IsReplyedUser), arg0, arg1, arg2)
}

// IsUserOnline mocks base method.
func (m *MockICache) IsUserOnline(arg0 context.Context, arg1 uint32) bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IsUserOnline", arg0, arg1)
	ret0, _ := ret[0].(bool)
	return ret0
}

// IsUserOnline indicates an expected call of IsUserOnline.
func (mr *MockICacheMockRecorder) IsUserOnline(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsUserOnline", reflect.TypeOf((*MockICache)(nil).IsUserOnline), arg0, arg1)
}

// OpenQuickReceiveSwitch mocks base method.
func (m *MockICache) OpenQuickReceiveSwitch(arg0 context.Context, arg1 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "OpenQuickReceiveSwitch", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// OpenQuickReceiveSwitch indicates an expected call of OpenQuickReceiveSwitch.
func (mr *MockICacheMockRecorder) OpenQuickReceiveSwitch(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "OpenQuickReceiveSwitch", reflect.TypeOf((*MockICache)(nil).OpenQuickReceiveSwitch), arg0, arg1)
}

// RemReceiveOrder mocks base method.
func (m *MockICache) RemReceiveOrder(arg0 context.Context, arg1 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RemReceiveOrder", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// RemReceiveOrder indicates an expected call of RemReceiveOrder.
func (mr *MockICacheMockRecorder) RemReceiveOrder(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RemReceiveOrder", reflect.TypeOf((*MockICache)(nil).RemReceiveOrder), arg0, arg1)
}

// RemTimeoutQuickReceiveUser mocks base method.
func (m *MockICache) RemTimeoutQuickReceiveUser(arg0 context.Context, arg1 int) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RemTimeoutQuickReceiveUser", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// RemTimeoutQuickReceiveUser indicates an expected call of RemTimeoutQuickReceiveUser.
func (mr *MockICacheMockRecorder) RemTimeoutQuickReceiveUser(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RemTimeoutQuickReceiveUser", reflect.TypeOf((*MockICache)(nil).RemTimeoutQuickReceiveUser), arg0, arg1)
}

// RemUnReceiveOrder mocks base method.
func (m *MockICache) RemUnReceiveOrder(arg0 context.Context, arg1 uint32, arg2 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RemUnReceiveOrder", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// RemUnReceiveOrder indicates an expected call of RemUnReceiveOrder.
func (mr *MockICacheMockRecorder) RemUnReceiveOrder(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RemUnReceiveOrder", reflect.TypeOf((*MockICache)(nil).RemUnReceiveOrder), arg0, arg1, arg2)
}

// RemUnReplyUser mocks base method.
func (m *MockICache) RemUnReplyUser(arg0 context.Context, arg1, arg2 uint32) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RemUnReplyUser", arg0, arg1, arg2)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RemUnReplyUser indicates an expected call of RemUnReplyUser.
func (mr *MockICacheMockRecorder) RemUnReplyUser(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RemUnReplyUser", reflect.TypeOf((*MockICache)(nil).RemUnReplyUser), arg0, arg1, arg2)
}

// SaveFirstRoundCoachUid mocks base method.
func (m *MockICache) SaveFirstRoundCoachUid(arg0 context.Context, arg1 uint32, arg2 []uint32) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "SaveFirstRoundCoachUid", arg0, arg1, arg2)
}

// SaveFirstRoundCoachUid indicates an expected call of SaveFirstRoundCoachUid.
func (mr *MockICacheMockRecorder) SaveFirstRoundCoachUid(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SaveFirstRoundCoachUid", reflect.TypeOf((*MockICache)(nil).SaveFirstRoundCoachUid), arg0, arg1, arg2)
}

// SaveUserTopGamePreSelectConfig mocks base method.
func (m *MockICache) SaveUserTopGamePreSelectConfig(arg0 context.Context, arg1 uint32, arg2 []uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SaveUserTopGamePreSelectConfig", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// SaveUserTopGamePreSelectConfig indicates an expected call of SaveUserTopGamePreSelectConfig.
func (mr *MockICacheMockRecorder) SaveUserTopGamePreSelectConfig(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SaveUserTopGamePreSelectConfig", reflect.TypeOf((*MockICache)(nil).SaveUserTopGamePreSelectConfig), arg0, arg1, arg2)
}

// SearchCoachTagUidMap mocks base method.
func (m *MockICache) SearchCoachTagUidMap(arg0 context.Context, arg1 []string) (map[string][]uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SearchCoachTagUidMap", arg0, arg1)
	ret0, _ := ret[0].(map[string][]uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SearchCoachTagUidMap indicates an expected call of SearchCoachTagUidMap.
func (mr *MockICacheMockRecorder) SearchCoachTagUidMap(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SearchCoachTagUidMap", reflect.TypeOf((*MockICache)(nil).SearchCoachTagUidMap), arg0, arg1)
}

// SetCantOpenQuickReceiveFlag mocks base method.
func (m *MockICache) SetCantOpenQuickReceiveFlag(arg0 context.Context, arg1, arg2, arg3 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetCantOpenQuickReceiveFlag", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetCantOpenQuickReceiveFlag indicates an expected call of SetCantOpenQuickReceiveFlag.
func (mr *MockICacheMockRecorder) SetCantOpenQuickReceiveFlag(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetCantOpenQuickReceiveFlag", reflect.TypeOf((*MockICache)(nil).SetCantOpenQuickReceiveFlag), arg0, arg1, arg2, arg3)
}

// SetCoachLeastOnlineTime mocks base method.
func (m *MockICache) SetCoachLeastOnlineTime(arg0 context.Context, arg1 uint32, arg2 int64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetCoachLeastOnlineTime", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetCoachLeastOnlineTime indicates an expected call of SetCoachLeastOnlineTime.
func (mr *MockICacheMockRecorder) SetCoachLeastOnlineTime(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetCoachLeastOnlineTime", reflect.TypeOf((*MockICache)(nil).SetCoachLeastOnlineTime), arg0, arg1, arg2)
}

// SetCoachRecommendInEffect mocks base method.
func (m *MockICache) SetCoachRecommendInEffect(arg0 context.Context, arg1 uint32, arg2 []*store.CoachRecommend) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetCoachRecommendInEffect", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetCoachRecommendInEffect indicates an expected call of SetCoachRecommendInEffect.
func (mr *MockICacheMockRecorder) SetCoachRecommendInEffect(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetCoachRecommendInEffect", reflect.TypeOf((*MockICache)(nil).SetCoachRecommendInEffect), arg0, arg1, arg2)
}

// SetIncentiveTaskAddition mocks base method.
func (m *MockICache) SetIncentiveTaskAddition(arg0 context.Context, arg1 time.Time, arg2, arg3 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetIncentiveTaskAddition", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetIncentiveTaskAddition indicates an expected call of SetIncentiveTaskAddition.
func (mr *MockICacheMockRecorder) SetIncentiveTaskAddition(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetIncentiveTaskAddition", reflect.TypeOf((*MockICache)(nil).SetIncentiveTaskAddition), arg0, arg1, arg2, arg3)
}

// SetIncentiveTaskLastViewTime mocks base method.
func (m *MockICache) SetIncentiveTaskLastViewTime(arg0 context.Context, arg1 uint32, arg2 int64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetIncentiveTaskLastViewTime", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetIncentiveTaskLastViewTime indicates an expected call of SetIncentiveTaskLastViewTime.
func (mr *MockICacheMockRecorder) SetIncentiveTaskLastViewTime(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetIncentiveTaskLastViewTime", reflect.TypeOf((*MockICache)(nil).SetIncentiveTaskLastViewTime), arg0, arg1, arg2)
}

// SetProductByProductId mocks base method.
func (m *MockICache) SetProductByProductId(arg0 context.Context, arg1 uint32, arg2 *store.SkillProduct) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetProductByProductId", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetProductByProductId indicates an expected call of SetProductByProductId.
func (mr *MockICacheMockRecorder) SetProductByProductId(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetProductByProductId", reflect.TypeOf((*MockICache)(nil).SetProductByProductId), arg0, arg1, arg2)
}

// SetQuickReceiveOffline mocks base method.
func (m *MockICache) SetQuickReceiveOffline(arg0 context.Context, arg1 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetQuickReceiveOffline", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetQuickReceiveOffline indicates an expected call of SetQuickReceiveOffline.
func (mr *MockICacheMockRecorder) SetQuickReceiveOffline(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetQuickReceiveOffline", reflect.TypeOf((*MockICache)(nil).SetQuickReceiveOffline), arg0, arg1)
}

// SetUnReceiveOrderCnt mocks base method.
func (m *MockICache) SetUnReceiveOrderCnt(arg0 context.Context, arg1, arg2 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetUnReceiveOrderCnt", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetUnReceiveOrderCnt indicates an expected call of SetUnReceiveOrderCnt.
func (mr *MockICacheMockRecorder) SetUnReceiveOrderCnt(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetUnReceiveOrderCnt", reflect.TypeOf((*MockICache)(nil).SetUnReceiveOrderCnt), arg0, arg1, arg2)
}

// SetUserFirstRoundAvailableCoachUids mocks base method.
func (m *MockICache) SetUserFirstRoundAvailableCoachUids(arg0 context.Context, arg1, arg2 uint32, arg3 []uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetUserFirstRoundAvailableCoachUids", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetUserFirstRoundAvailableCoachUids indicates an expected call of SetUserFirstRoundAvailableCoachUids.
func (mr *MockICacheMockRecorder) SetUserFirstRoundAvailableCoachUids(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetUserFirstRoundAvailableCoachUids", reflect.TypeOf((*MockICache)(nil).SetUserFirstRoundAvailableCoachUids), arg0, arg1, arg2, arg3)
}

// SetUserOnline mocks base method.
func (m *MockICache) SetUserOnline(arg0 context.Context, arg1 uint32, arg2 bool) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetUserOnline", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetUserOnline indicates an expected call of SetUserOnline.
func (mr *MockICacheMockRecorder) SetUserOnline(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetUserOnline", reflect.TypeOf((*MockICache)(nil).SetUserOnline), arg0, arg1, arg2)
}

// SetUserSex mocks base method.
func (m *MockICache) SetUserSex(arg0 context.Context, arg1, arg2 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetUserSex", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetUserSex indicates an expected call of SetUserSex.
func (mr *MockICacheMockRecorder) SetUserSex(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetUserSex", reflect.TypeOf((*MockICache)(nil).SetUserSex), arg0, arg1, arg2)
}

// SetUserSkillProduct mocks base method.
func (m *MockICache) SetUserSkillProduct(arg0 context.Context, arg1 uint32, arg2 []*store.SkillProduct) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetUserSkillProduct", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetUserSkillProduct indicates an expected call of SetUserSkillProduct.
func (mr *MockICacheMockRecorder) SetUserSkillProduct(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetUserSkillProduct", reflect.TypeOf((*MockICache)(nil).SetUserSkillProduct), arg0, arg1, arg2)
}

// SetUserToRealCoach mocks base method.
func (m *MockICache) SetUserToRealCoach(arg0 context.Context, arg1, arg2, arg3 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetUserToRealCoach", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetUserToRealCoach indicates an expected call of SetUserToRealCoach.
func (mr *MockICacheMockRecorder) SetUserToRealCoach(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetUserToRealCoach", reflect.TypeOf((*MockICache)(nil).SetUserToRealCoach), arg0, arg1, arg2, arg3)
}

// SetUserUnderlayCoach mocks base method.
func (m *MockICache) SetUserUnderlayCoach(arg0 context.Context, arg1, arg2 uint32, arg3 []uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetUserUnderlayCoach", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetUserUnderlayCoach indicates an expected call of SetUserUnderlayCoach.
func (mr *MockICacheMockRecorder) SetUserUnderlayCoach(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetUserUnderlayCoach", reflect.TypeOf((*MockICache)(nil).SetUserUnderlayCoach), arg0, arg1, arg2, arg3)
}

// UpdateSkillProductSearchIndex mocks base method.
func (m *MockICache) UpdateSkillProductSearchIndex(arg0 context.Context, arg1 map[string][]uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateSkillProductSearchIndex", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateSkillProductSearchIndex indicates an expected call of UpdateSkillProductSearchIndex.
func (mr *MockICacheMockRecorder) UpdateSkillProductSearchIndex(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateSkillProductSearchIndex", reflect.TypeOf((*MockICache)(nil).UpdateSkillProductSearchIndex), arg0, arg1)
}
