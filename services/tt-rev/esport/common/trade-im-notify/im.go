//go:generate quicksilver-cli.exe test interface ../trade-im-notify
//go:generate mockgen -destination=../mocks/mock_im_notify.go -package=mocks golang.52tt.com/services/tt-rev/esport/common/trade-im-notify ITradeImNotify
package trade_im_notify

import (
	"context"
	"time"

	push "golang.52tt.com/clients/push-notification/v2"
	"golang.52tt.com/pkg/protocol"
	pushPb "golang.52tt.com/protocol/app/push"
	push_notification "golang.52tt.com/protocol/services/push-notification/v2"

	"gitlab.ttyuyin.com/avengers/tyr/compatible/proto"
	"gitlab.ttyuyin.com/tt-infra/tyr/log"
	"golang.52tt.com/clients/account"
	im_api "golang.52tt.com/clients/im-api"
	"golang.52tt.com/clients/seqgen/v2"
	timeline "golang.52tt.com/clients/timeline"
	"golang.52tt.com/protocol/app/esport_logic"
	imPB "golang.52tt.com/protocol/app/im"
	syncPB "golang.52tt.com/protocol/app/sync"
	impb "golang.52tt.com/protocol/services/im-api"
	timelinesvr "golang.52tt.com/protocol/services/timelinesvr"
	"golang.52tt.com/services/helper-from-cpp/immsghelper"
	"golang.52tt.com/services/notify"
)

var _ ITradeImNotify = (*TradeImNotify)(nil)

type TradeImNotify struct {
	accountCli  account.IClient
	seqgenCli   seqgen.IClient
	timelineCli timeline.IClient
	imApiCli    im_api.IClient
	pushCli     push.IClient
}

func NewTradeImNotify(accountCli account.IClient, seqgenCli seqgen.IClient, timelineCli timeline.IClient, imApiCli im_api.IClient) *TradeImNotify {
	pushCli, _ := push.NewClient()

	return &TradeImNotify{
		accountCli:  accountCli,
		seqgenCli:   seqgenCli,
		timelineCli: timelineCli,
		imApiCli:    imApiCli,
		pushCli:     pushCli,
	}
}

type UserOrderImMsg struct {
	Uid         uint32 `json:"uid"`
	ServerMsgId uint64 `json:"serverMsgId"`
	Ext         *esport_logic.EsportOrderImMsg
}

type UserOrderImSysMsg struct {
	Uid         uint32
	ServerMsgId uint64
	Ext         *esport_logic.EsportImSysMsg
}

type UserMsg struct {
	Uid         uint32
	ServerMsgId uint64
	ExtMsg      []byte
	Content     string
}

type ImMsg struct {
	ServerMsgId uint64
	ExtMsg      []byte
	Content     string
}

// SendOrderImMsg 发送订单IM消息
// from 和 to 是两个用户，消息挂在用户的头像上
// UserOrderImSysMsg.ServerMsgId为0时，会自动生成，并且返回
func (s *TradeImNotify) SendOrderImMsg(ctx context.Context, from, to *UserOrderImMsg) (fromMsgId, toMsgId uint64, err error) {
	if from == nil || to == nil {
		return
	}

	fromExtMsg, _ := proto.Marshal(from.Ext)
	toExtMsg, _ := proto.Marshal(to.Ext)
	log.DebugWithCtx(ctx, "SendOrderImMsg - from: %+v, to: %+v", from, to)

	return s.SendImMsg(ctx, uint32(imPB.IM_MSG_TYPE_ESPORT_ORDER_MSG),
		&UserMsg{Uid: from.Uid, ServerMsgId: from.ServerMsgId, ExtMsg: fromExtMsg},
		&UserMsg{Uid: to.Uid, ServerMsgId: to.ServerMsgId, ExtMsg: toExtMsg},
	)
}

// SendOrderImSysMsg 发送订单系统消息
// from 和 to 是两个用户，消息挂不挂在用户的头像上，直接显示在聊天框中
// UserOrderImSysMsg.ServerMsgId为0时，会自动生成，并且返回
func (s *TradeImNotify) SendOrderImSysMsg(ctx context.Context, from, to *UserOrderImSysMsg) (fromMsgId, toMsgId uint64, err error) {
	if from == nil || to == nil {
		return
	}

	fromExtMsg, _ := proto.Marshal(from.Ext)
	toExtMsg, _ := proto.Marshal(to.Ext)

	return s.SendImMsg(ctx, uint32(imPB.IM_MSG_TYPE_ESPORT_SYS_MSG),
		&UserMsg{Uid: from.Uid, ServerMsgId: from.ServerMsgId, ExtMsg: fromExtMsg},
		&UserMsg{Uid: to.Uid, ServerMsgId: to.ServerMsgId, ExtMsg: toExtMsg},
	)
}

// SendImMsg 发送IM消息
func (s *TradeImNotify) SendImMsg(ctx context.Context, cmd uint32, from, to *UserMsg) (fromMsgId, toMsgId uint64, err error) {
	if from == nil || to == nil {
		return
	}

	if from.ServerMsgId == 0 {
		svrMsgID, err := s.seqgenCli.GenerateSequence(ctx, from.Uid, seqgen.NamespaceUser, seqgen.KeySvrMsgId, 1)
		if err != nil {
			log.ErrorWithCtx(ctx, "SendImMsg - Failed to GenerateSequence svr msg id: %v", err)
			return 0, 0, err
		}
		from.ServerMsgId = svrMsgID
	}
	if to.ServerMsgId == 0 {
		svrMsgID, err := s.seqgenCli.GenerateSequence(ctx, to.Uid, seqgen.NamespaceUser, seqgen.KeySvrMsgId, 1)
		if err != nil {
			log.ErrorWithCtx(ctx, "SendImMsg - Failed to GenerateSequence svr msg id: %v", err)
			return 0, 0, err
		}
		to.ServerMsgId = svrMsgID
	}

	userMap, err := s.accountCli.BatGetUserByUid(ctx, from.Uid, to.Uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "SendImMsg - WriteMsgToUidWithId fail , from: %+v, to:%+v, err: %v", from, to, err)
		return 0, 0, err
	}

	fromUser, toUser := userMap[from.Uid], userMap[to.Uid]

	msg := &timelinesvr.ImMsg{
		FromId:          fromUser.GetUid(),
		ToId:            toUser.GetUid(),
		FromName:        fromUser.GetUsername(),
		ToName:          toUser.GetUsername(),
		FromNick:        fromUser.GetNickname(),
		ToNick:          toUser.GetNickname(),
		Content:         "",
		Type:            cmd,
		ClientMsgTime:   uint32(time.Now().Unix()),
		Status:          uint32(syncPB.NewMessageSync_UN_READ),
		ServerMsgTime:   uint32(time.Now().Unix()),
		HasAttachment:   false,
		MsgSourceType:   uint32(imPB.MsgSourceType_MSG_SOURCE_FROM_ESPORT),
		Platform:        uint32(timelinesvr.Platform_UNSPECIFIED), // 没有平台限制必须显式指定
		MsgExposureFlag: uint32(syncPB.NewMessageSync_FLAG_ON),
		MsgRedpointFlag: uint32(syncPB.NewMessageSync_FLAG_ON),
	}

	msg.ServerMsgId = uint32(from.ServerMsgId)
	msg.TargetMsgId = uint32(to.ServerMsgId)
	msg.Ext = from.ExtMsg
	msg.Content = from.Content

	imErr := immsghelper.WriteMsgToUidWithId(ctx, fromUser.GetUid(), msg, s.seqgenCli, s.timelineCli)
	if imErr != nil {
		log.ErrorWithCtx(ctx, "SendImMsg - WriteMsgToUidWithId fail , msg: %v , err: %v", msg, imErr)
		return 0, 0, imErr
	}

	msg.ServerMsgId = uint32(to.ServerMsgId)
	msg.TargetMsgId = uint32(from.ServerMsgId)
	msg.Ext = to.ExtMsg
	msg.Content = to.Content

	imErr = immsghelper.WriteMsgToUidWithId(ctx, toUser.GetUid(), msg, s.seqgenCli, s.timelineCli)
	if imErr != nil {
		log.ErrorWithCtx(ctx, "SendImMsg - WriteMsgToUidWithId fail , msg: %v , err: %v", msg, imErr)
		return 0, 0, imErr
	}

	imErr = notify.NotifySyncX(ctx, []uint32{fromUser.GetUid(), toUser.GetUid()}, notify.ImMsg)
	if imErr != nil {
		log.ErrorWithCtx(ctx, "SendImMsg - NotifySyncX fail , msg: %v , err: %v", msg, imErr)
	}

	log.InfoWithCtx(ctx, "SendImMsg success, msg:%+v", msg)

	return from.ServerMsgId, to.ServerMsgId, nil
}

// SendOneSideImMsg 发送单边IM消息, 那一边能看到whichSide: 0.from 1.to
func (s *TradeImNotify) SendOneSideImMsg(ctx context.Context, cmd uint32, fromUid, toUid, whichSide uint32, rawMsg *ImMsg) (msgId uint64, err error) {
	targetUid := fromUid
	if whichSide == 1 {
		targetUid = toUid
	}

	if rawMsg.ServerMsgId == 0 {
		svrMsgID, err := s.seqgenCli.GenerateSequence(ctx, targetUid, seqgen.NamespaceUser, seqgen.KeySvrMsgId, 1)
		if err != nil {
			log.ErrorWithCtx(ctx, "SendOneSideImMsg - Failed to GenerateSequence svr msg id: %v", err)
			return 0, err
		}
		rawMsg.ServerMsgId = svrMsgID
	}

	userMap, err := s.accountCli.BatGetUserByUid(ctx, fromUid, toUid)
	if err != nil {
		log.ErrorWithCtx(ctx, "SendOneSideImMsg - WriteMsgToUidWithId fail , fromUid: %d, toUid: %d, err: %v", fromUid, toUid, err)
		return 0, err
	}

	fromUser, toUser := userMap[fromUid], userMap[toUid]

	msg := &timelinesvr.ImMsg{
		FromId:          fromUser.GetUid(),
		ToId:            toUser.GetUid(),
		FromName:        fromUser.GetUsername(),
		ToName:          toUser.GetUsername(),
		FromNick:        fromUser.GetNickname(),
		ToNick:          toUser.GetNickname(),
		Content:         "",
		Type:            cmd,
		ClientMsgTime:   uint32(time.Now().Unix()),
		Status:          uint32(syncPB.NewMessageSync_UN_READ),
		ServerMsgTime:   uint32(time.Now().Unix()),
		HasAttachment:   false,
		MsgSourceType:   uint32(imPB.MsgSourceType_MSG_SOURCE_FROM_ESPORT),
		Platform:        uint32(timelinesvr.Platform_UNSPECIFIED), // 没有平台限制必须显式指定
		MsgExposureFlag: uint32(syncPB.NewMessageSync_FLAG_ON),
		MsgRedpointFlag: uint32(syncPB.NewMessageSync_FLAG_ON),
	}

	msg.ServerMsgId = uint32(rawMsg.ServerMsgId)
	msg.Ext = rawMsg.ExtMsg
	msg.Content = rawMsg.Content

	imErr := immsghelper.WriteMsgToUidWithId(ctx, targetUid, msg, s.seqgenCli, s.timelineCli)
	if imErr != nil {
		log.ErrorWithCtx(ctx, "SendOneSideImMsg - WriteMsgToUidWithId fail , msg: %v , err: %v", msg, imErr)
		return 0, imErr
	}

	imErr = notify.NotifySyncX(ctx, []uint32{targetUid}, notify.ImMsg)
	if imErr != nil {
		log.ErrorWithCtx(ctx, "SendOneSideImMsg - NotifySyncX fail , msg: %v , err: %v", msg, imErr)
	}

	log.InfoWithCtx(ctx, "SendOneSideImMsg success, msg:%+v", msg)

	return rawMsg.ServerMsgId, nil
}

// SendOfficialAccountMsg 发送公众号消息 ps: hightlight字段内容要在content中出现才会有高亮效果
func (s *TradeImNotify) SendOfficialAccountMsg(ctx context.Context, namespace string, toUid uint32, text *impb.Text) error {
	sendReq := &impb.SendPublicAccountTextReq{
		Namespace: namespace,
		PublicAccount: &impb.PublicAccount{
			PublicType: impb.PublicAccount_SYSTEM,
			BindedId:   9991, // 电竞助手绑定id
		},
		ToUid: toUid,
		Text:  text,
	}
	accountText, err := s.imApiCli.SendPublicAccountText(ctx, sendReq)
	if err != nil {
		log.ErrorWithCtx(ctx, "SendOfficialAccountMsg - SendPublicAccountText fail , err: %v, namespace: %v, toUid: %v, text: %v", err, namespace, toUid, text)
		return err
	}
	log.InfoWithCtx(ctx, "SendOfficialAccountMsg - SendPublicAccountText success , accountText: %v, namespace: %v, toUid: %v, text: %v", accountText, namespace, toUid, text)
	return err
}

func (s *TradeImNotify) PushOrderChangeNotify(ctx context.Context, orderId string, uidList []uint32) error {
	if len(uidList) == 0 {
		log.ErrorWithCtx(ctx, "PushOrderChangeNotify uidList empty orderId:%s, uidList:%v", orderId, uidList)
		return nil
	}

	opt := &esport_logic.OrderChangeNotify{
		OrderId:    orderId,
		NotifyTime: time.Now().Unix(),
	}

	msg, _ := proto.Marshal(opt)

	pushMessage := &pushPb.PushMessage{
		Cmd:     uint32(pushPb.PushMessage_ESPORT_ORDER_CHANGE_PUSH),
		Content: msg,
	}
	pushMessageBytes, _ := proto.Marshal(pushMessage)

	notification := &push_notification.CompositiveNotification{
		Sequence:           uint32(time.Now().Unix()),
		TerminalTypeList:   []uint32{protocol.MobileAndroidTT, protocol.MobileIPhoneTT},
		TerminalTypePolicy: push.DefaultPolicy,
		AppId:              0,
		ProxyNotification: &push_notification.ProxyNotification{
			Type:    uint32(push_notification.ProxyNotification_PUSH),
			Payload: pushMessageBytes,
		},
	}

	err := s.pushCli.PushToUsers(ctx, uidList, notification)
	if err != nil {
		log.ErrorWithCtx(ctx, "PushOrderChangeNotify fail to PushToUsers. orderId:%s, uidList:%v, err:%v", orderId, uidList, err)
		return err
	}

	log.DebugWithCtx(ctx, "PushOrderChangeNotify success. orderId:%s, uidList:%v", orderId, uidList)
	return nil
}