package internal

import (
    "context"
    "golang.52tt.com/pkg/log"
    "golang.52tt.com/pkg/protocol"
    "golang.52tt.com/protocol/common/status"
    pb "golang.52tt.com/protocol/services/esport_rcmd"
)

func (s *Server) ListStrategies(c context.Context, request *pb.ListStrategiesRequest) (*pb.ListStrategiesResponse, error) {
    out := &pb.ListStrategiesResponse{}
    var err error
    defer func() {
        log.DebugWithCtx(c, "ListStrategies, req: %+v, resp:%+v", request, out)
    }()
    // 校验参数
    if request.GetPageSize() == 0 {
        request.PageSize = 10
    }
    if request.GetPageNumber() == 0 {
        request.PageNumber = 1
    }

    out, err = s.strategyMgr.ListStrategies(c, request)
    if err != nil {
        log.ErrorWithCtx(c, "ListStrategies error: %v", err)
        return out, protocol.NewExactServerError(nil, status.ErrEsportsCommonErr, "ListStrategies error")
    }
    return out, nil
}

func (s *Server) GetStrategy(c context.Context, request *pb.GetStrategyRequest) (*pb.GetStrategyResponse, error) {
    out := &pb.GetStrategyResponse{}
    defer func() {
        log.DebugWithCtx(c, "GetStrategy, req: %+v, resp:%+v", request, out)
    }()
    // 校验参数
    if request.GetId() == 0 {
        log.ErrorWithCtx(c, "GetStrategy id is 0")
        return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "id is 0")
    }
    target, err := s.strategyMgr.GetStrategy(c, request.GetId())
    if err != nil {
        log.ErrorWithCtx(c, "GetStrategy error: %v", err)
        return out, protocol.NewExactServerError(nil, status.ErrEsportsCommonErr, "GetStrategy error")
    }
    out.Strategy = target
    return out, nil
}

func (s *Server) CreateStrategy(c context.Context, request *pb.CreateStrategyRequest) (*pb.CreateStrategyResponse, error) {
    out := &pb.CreateStrategyResponse{}
    defer func() {
        log.DebugWithCtx(c, "CreateStrategy, req: %+v, resp:%+v", request, out)
    }()
    // 判断名称是否已经存在
    exist, err := s.strategyMgr.CheckNameExist(c, request.GetStrategy().GetSceneType(), request.GetStrategy().GetName(), 0)
    if err != nil {
        log.ErrorWithCtx(c, "CheckNameExist error: %v", err)
        return out, protocol.NewExactServerError(nil, status.ErrEsportsCommonErr, "CheckNameExist error")
    }
    if exist {
        log.ErrorWithCtx(c, "strategy name already exist")
        return out, protocol.NewExactServerError(nil, status.ErrEsportsCommonErr, "策略名称已经存在")
    }

    // 保存策略
    id, err := s.strategyMgr.CreateStrategy(c, request.Strategy)
    if err != nil {
        log.ErrorWithCtx(c, "CreateStrategy error: %v", err)
        return out, protocol.NewExactServerError(nil, status.ErrEsportsCommonErr, "CreateStrategy error")
    }
    out.Id = id
    return out, nil
}

func (s *Server) UpdateStrategy(c context.Context, request *pb.UpdateStrategyRequest) (*pb.UpdateStrategyResponse, error) {
    out := &pb.UpdateStrategyResponse{}
    defer func() {
        log.DebugWithCtx(c, "UpdateStrategy, req: %+v, resp:%+v", request, out)
    }()

    // 判断id是否合法
    if request.GetStrategy().GetId() == 0 {
        log.ErrorWithCtx(c, "UpdateStrategy id is 0")
        return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "id is 0")
    }

    // 判断名称是否已经存在
    exist, err := s.strategyMgr.CheckNameExist(c, request.GetStrategy().GetSceneType(), request.GetStrategy().GetName(), request.GetStrategy().GetId())
    if err != nil {
        log.ErrorWithCtx(c, "CheckNameExist error: %v", err)
        return out, protocol.NewExactServerError(nil, status.ErrEsportsCommonErr, "CheckNameExist error")
    }
    if exist {
        log.ErrorWithCtx(c, "strategy name already exist")
        return out, protocol.NewExactServerError(nil, status.ErrEsportsCommonErr, "策略名称已经存在")
    }

    // Update the strategy
    err = s.strategyMgr.UpdateStrategy(c, request.GetStrategy())
    if err != nil {
        log.ErrorWithCtx(c, "UpdateStrategy error: %v", err)
        return out, protocol.NewExactServerError(nil, status.ErrEsportsCommonErr, "UpdateStrategy error")
    }

    return out, nil
}

func (s *Server) PauseStrategy(c context.Context, request *pb.PauseStrategyRequest) (*pb.PauseStrategyResponse, error) {
    out := &pb.PauseStrategyResponse{}
    defer func() {
        log.DebugWithCtx(c, "PauseStrategy, req: %+v, resp:%+v", request, out)
    }()

    // Validate the request
    if request.GetId() == 0 {
        log.ErrorWithCtx(c, "PauseStrategy id is 0")
        return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "id is 0")
    }

    // Pause the strategy
    err := s.strategyMgr.PauseStrategy(c, request.GetId())
    if err != nil {
        log.ErrorWithCtx(c, "PauseStrategy error: %v", err)
        return out, protocol.NewExactServerError(nil, status.ErrEsportsCommonErr, "PauseStrategy error")
    }

    return out, nil
}

func (s *Server) StartStrategy(c context.Context, request *pb.StartStrategyRequest) (*pb.StartStrategyResponse, error) {
    out := &pb.StartStrategyResponse{}
    defer func() {
        log.DebugWithCtx(c, "StartStrategy, req: %+v, resp:%+v", request, out)
    }()

    // Validate the request
    if request.GetId() == 0 {
        log.ErrorWithCtx(c, "StartStrategy id is 0")
        return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "id is 0")
    }

    // Start the strategy
    err := s.strategyMgr.StartStrategy(c, request.GetId())
    if err != nil {
        log.ErrorWithCtx(c, "StartStrategy error: %v", err)
        return out, protocol.NewExactServerError(nil, status.ErrEsportsCommonErr, "StartStrategy error")
    }

    return out, nil
}

func (s *Server) DeleteStrategy(c context.Context, request *pb.DeleteStrategyRequest) (*pb.DeleteStrategyResponse, error) {
    out := &pb.DeleteStrategyResponse{}
    defer func() {
        log.DebugWithCtx(c, "DeleteStrategy, req: %+v, resp:%+v", request, out)
    }()

    // Validate the request
    if request.GetId() == 0 {
        log.ErrorWithCtx(c, "DeleteStrategy id is 0")
        return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "id is 0")
    }

    // Delete the strategy
    err := s.strategyMgr.DeleteStrategy(c, request.GetId())
    if err != nil {
        log.ErrorWithCtx(c, "DeleteStrategy error: %v", err)
        return out, protocol.NewExactServerError(nil, status.ErrEsportsCommonErr, "DeleteStrategy error")
    }

    return out, nil
}

func (s *Server) GetFilterRules(c context.Context, request *pb.GetFilterRulesRequest) (*pb.GetFilterRulesResponse, error) {
    out := &pb.GetFilterRulesResponse{}
    defer func() {
        log.DebugWithCtx(c, "GetFilterRules, req: %+v, resp:%+v", request, out)
    }()
    out.FilterRules = s.bc.GetConfig().FilterRules
    return out, nil
}

func (s *Server) GetScenes(c context.Context, request *pb.GetScenesRequest) (*pb.GetScenesResponse, error) {
    out := &pb.GetScenesResponse{}
    defer func() {
        log.DebugWithCtx(c, "GetScenes, req: %+v, resp:%+v", request, out)
    }()

    if s.bc.GetConfig().Scenes == nil {
        log.InfoWithCtx(c, "GetScenes scenes config is nil")
    } else {
        out.List = s.bc.GetConfig().Scenes
    }
    return out, nil
}

func (s *Server) ListRecallSources(c context.Context, request *pb.ListRecallSourcesRequest) (*pb.ListRecallSourcesResponse, error) {
    out := &pb.ListRecallSourcesResponse{}
    defer func() {
        log.DebugWithCtx(c, "ListRecallSources, req: %+v, resp:%+v", request, out)
    }()
    // 校验参数
    if request.GetPageSize() == 0 {
        request.PageSize = 10
    }
    if request.GetPageNumber() == 0 {
        request.PageNumber = 1
    }

    sources, err := s.recallSourceMgr.ListRecallSources(c, request)
    if err != nil {
        log.ErrorWithCtx(c, "ListRecallSources error: %v", err)
        return out, protocol.NewExactServerError(nil, status.ErrEsportsCommonErr, "ListRecallSources error")
    }
    return sources, nil
}

func (s *Server) GetRecallSourceDetails(c context.Context, request *pb.GetRecallSourceDetailsRequest) (*pb.GetRecallSourceDetailsResponse, error) {
    out := &pb.GetRecallSourceDetailsResponse{}
    defer func() {
        log.DebugWithCtx(c, "GetRecallSourceDetails, req: %+v, resp:%+v", request, out)
    }()
    // 校验参数
    if request.GetRecallSourceId() == 0 {
        log.ErrorWithCtx(c, "GetRecallSourceDetails id is 0")
        return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "id is 0")
    }

    source, err := s.recallSourceMgr.GetRecallSource(c, request.GetRecallSourceId())
    if err != nil {
        log.ErrorWithCtx(c, "GetRecallSourceDetails error: %v", err)
        return out, protocol.NewExactServerError(nil, status.ErrEsportsCommonErr, "GetRecallSourceDetails error")
    }
    out.RecallSource = source
    return out, nil
}

func (s *Server) CreateRecallSource(c context.Context, request *pb.CreateRecallSourceRequest) (*pb.CreateRecallSourceResponse, error) {
    out := &pb.CreateRecallSourceResponse{}
    defer func() {
        log.DebugWithCtx(c, "CreateRecallSource, req: %+v, resp:%+v", request, out)
    }()
    // 校验参数

    id, err := s.recallSourceMgr.CreateRecallSource(c, request.GetRecallSource())
    if err != nil {
        log.ErrorWithCtx(c, "CreateRecallSource error: %v", err)
        return out, protocol.NewExactServerError(nil, status.ErrEsportsCommonErr, "CreateRecallSource error")
    }
    out.RecallSourceId = id
    return out, nil
}

func (s *Server) UpdateRecallSource(c context.Context, request *pb.UpdateRecallSourceRequest) (*pb.UpdateRecallSourceResponse, error) {
    out := &pb.UpdateRecallSourceResponse{}
    defer func() {
        log.DebugWithCtx(c, "UpdateRecallSource, req: %+v, resp:%+v", request, out)
    }()
    // 校验参数
    if request.GetRecallSource() == nil {
        log.ErrorWithCtx(c, "UpdateRecallSource request is nil")
        return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "request is nil")
    }
    if request.GetRecallSource().GetId() == 0 {
        log.ErrorWithCtx(c, "UpdateRecallSource id is 0")
        return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "id is 0")
    }

    err := s.recallSourceMgr.UpdateRecallSource(c, request.GetRecallSource())
    if err != nil {
        log.ErrorWithCtx(c, "UpdateRecallSource error: %v", err)
        return out, protocol.NewExactServerError(nil, status.ErrEsportsCommonErr, "UpdateRecallSource error")
    }
    return out, nil
}

func (s *Server) DeleteRecallSource(c context.Context, request *pb.DeleteRecallSourceRequest) (*pb.DeleteRecallSourceResponse, error) {
    out := &pb.DeleteRecallSourceResponse{}
    defer func() {
        log.DebugWithCtx(c, "DeleteRecallSource, req: %+v, resp:%+v", request, out)
    }()
    // 校验参数
    if request.GetRecallSourceId() == 0 {
        log.ErrorWithCtx(c, "DeleteRecallSource id is 0")
        return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "id is 0")
    }

    err := s.recallSourceMgr.DeleteRecallSource(c, request.GetRecallSourceId())
    if err != nil {
        log.ErrorWithCtx(c, "DeleteRecallSource error: %v", err)
        return out, protocol.NewExactServerError(nil, status.ErrEsportsCommonErr, err.Error())
    }
    return out, nil
}

func (s *Server) GetRecallSourceRuleList(c context.Context, request *pb.GetRecallSourceRuleListRequest) (*pb.GetRecallSourceRuleListResponse, error) {
    out := &pb.GetRecallSourceRuleListResponse{}
    defer func() {
        log.DebugWithCtx(c, "GetRecallSourceRuleList, req: %+v, resp:%+v", request, out)
    }()
    out.Rules = s.bc.GetConfig().RecallSourceRules
    return out, nil
}

func (s *Server) GetDefaultSortParameterTable(c context.Context, request *pb.GetDefaultSortParameterTableRequest) (*pb.GetDefaultSortParameterTableResponse, error) {
    out := &pb.GetDefaultSortParameterTableResponse{}
    defer func() {
        log.DebugWithCtx(c, "GetDefaultSortParameterTable, req: %+v, resp:%+v", request, out)
    }()
    sortParameterTable, err := s.sortParameterTableMgr.GetDefaultSortParameterTable(c)
    if err != nil {
        log.ErrorWithCtx(c, "GetDefaultSortParameterTable error: %v", err)
        return out, protocol.NewExactServerError(nil, status.ErrEsportsCommonErr, "GetDefaultSortParameterTable error")
    }
    out.SortParameterTable = sortParameterTable
    return out, nil
}

func (s *Server) UpdateDefaultSortParameterTable(c context.Context, request *pb.UpdateDefaultSortParameterTableRequest) (*pb.UpdateDefaultSortParameterTableResponse, error) {
    out := &pb.UpdateDefaultSortParameterTableResponse{}
    defer func() {
        log.DebugWithCtx(c, "UpdateDefaultSortParameterTable, req: %+v, resp:%+v", request, out)
    }()
    err := s.sortParameterTableMgr.UpdateDefaultSortParameterTable(c, request.SortParameterTable)
    if err != nil {
        log.ErrorWithCtx(c, "UpdateDefaultSortParameterTable error: %v", err)
        return out, protocol.NewExactServerError(nil, status.ErrEsportsCommonErr, "UpdateDefaultSortParameterTable error")
    }
    return out, nil
}

func (s *Server) GetRecallSourceQueueRule(c context.Context, request *pb.GetRecallSourceQueueRuleRequest) (*pb.GetRecallSourceQueueRuleResponse, error) {
    out := &pb.GetRecallSourceQueueRuleResponse{}
    defer func() {
        log.DebugWithCtx(c, "GetRecallSourceQueueRule, req: %+v, resp:%+v", request, out)
    }()
    out.List = s.bc.GetConfig().RecallSourceQueueRules
    return out, nil
}

// AddSortParameterTableItem 添加排序参数表项，这是个内部的接口，会添加到全局的排序参数表，并同步到其他自定义的排序参数表
func (s *Server) AddSortParameterTableItem(c context.Context, request *pb.AddSortParameterTableItemRequest) (*pb.AddSortParameterTableItemResponse, error) {
    out := &pb.AddSortParameterTableItemResponse{}
    defer func() {
        log.DebugWithCtx(c, "AddSortParameterTableItem, req: %+v, resp:%+v", request, out)
    }()

    // 获取默认的排序参数
    item, err := s.sortParameterTableMgr.AddSortParameterTableItem(c, request)
    if err != nil {
        log.ErrorWithCtx(c, "AddSortParameterTableItem error: %v", err)
        return out, protocol.NewExactServerError(nil, status.ErrEsportsCommonErr, "AddSortParameterTableItem error")
    }
    out.Count = item
    return out, nil
}
