package mongo

import (
	"context"
	"golang.52tt.com/pkg/config"
	"testing"
	"time"
)

var mDao *MongoDao
var coachUid uint32 = 1
var playerUid uint32 = 2

func init() {
	var err error
	cfg := &config.MongoConfig{
		Addrs:       "10.112.213.23:27017",
		Database:    "esport",
		MaxPoolSize: 16,
		UserName:    "godman",
		Password:    "TT8thegodofman",
	}
	mDao, err = NewMongoDao(context.Background(), cfg)
	if err != nil {
		panic(err)
	}
}

func TestMongoDao_CreateEvaluate(t *testing.T) {
	orderId := time.Now().String()
	_, exist, err := mDao.GetEvaluateByOrderId(context.Background(), orderId)
	if err != nil {
		t.Error(err)
	}
	if exist {
		t.Error("exist")
	}

	err = mDao.CreateEvaluate(context.Background(), &Evaluate{
		OrderId:      orderId,
		CoachUid:     coachUid,
		PlayerUid:    playerUid,
		QuickWords:   []string{"服务一般", "技术不错", "声音一般"},
		IsAnonymous:  false,
		ServiceScore: 50,
		SkillScore:   50,
		VoiceScore:   40,
		Content:      "呵呵",
		ReviewStatus: 0,
		CreateTime:   time.Now(),
		UpdateTime:   time.Now(),
	})
	if err != nil {
		t.Error(err)
	}

	info, exist, err := mDao.GetEvaluateByOrderId(context.Background(), orderId)
	if err != nil {
		t.Error(err)
	}
	if !exist {
		t.Error("not exist")
	}
	t.Logf("%+v", info)

	err = mDao.UpdateEvaluateReviewStatus(context.Background(), orderId, 1)
	if err != nil {
		t.Error(err)
	}

	info, exist, err = mDao.GetEvaluateByOrderId(context.Background(), orderId)
	if err != nil {
		t.Error(err)
	}
	if !exist {
		t.Error("not exist")
	}
	t.Logf("%+v", info)
}

func TestMongoDao_GetEvaluateSummary(t *testing.T) {
	info, err := mDao.GetEvaluateSummary(context.Background(), coachUid, 0)
	if err != nil {
		t.Error(err)
	}

	t.Logf("%+v", info)
	for _, v := range info.QuickWordCntList {
		t.Logf("%+v", v)
	}
}

func TestMongoDao_GetEvaluateList(t *testing.T) {
	list, err := mDao.GetEvaluateList(context.Background(), coachUid, 0, 0, 10, "服务一般")
	if err != nil {
		t.Error(err)
	}

	if len(list) == 0 {
		t.Error("not exist")
	}

	for _, v := range list {
		t.Logf("%+v", v)
	}
}

func TestMongoDao_BatGetEvaluateAvgScore(t *testing.T) {
	err := mDao.CreateEvaluate(context.Background(), &Evaluate{
		OrderId:      time.Now().String(),
		CoachUid:     coachUid,
		PlayerUid:    playerUid,
		QuickWords:   []string{"服务一般", "技术不错", "声音一般"},
		IsAnonymous:  false,
		ServiceScore: 50,
		SkillScore:   50,
		VoiceScore:   40,
		Content:      "呵呵",
		ReviewStatus: 0,
		CreateTime:   time.Now(),
		UpdateTime:   time.Now(),
	})
	if err != nil {
		t.Error(err)
	}

	err = mDao.CreateEvaluate(context.Background(), &Evaluate{
		OrderId:      time.Now().String(),
		CoachUid:     coachUid + 1,
		PlayerUid:    playerUid,
		QuickWords:   []string{"服务一般", "技术不错", "声音一般"},
		IsAnonymous:  false,
		ServiceScore: 10,
		SkillScore:   20,
		VoiceScore:   30,
		Content:      "呵呵",
		ReviewStatus: 0,
		CreateTime:   time.Now(),
		UpdateTime:   time.Now(),
	})
	if err != nil {
		t.Error(err)
	}
	list, err := mDao.BatGetEvaluateAvgScore(context.Background(), []uint32{coachUid, coachUid + 1}, 0)
	if err != nil {
		t.Error(err)
	}

	if len(list) == 0 {
		t.Error("not exist")
	}

	for _, v := range list {
		t.Logf("%+v", v)
	}
}
