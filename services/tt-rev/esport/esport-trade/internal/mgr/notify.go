package mgr

import (
	"context"
	"fmt"
	esport_trade "golang.52tt.com/protocol/services/esport-trade"
	impb "golang.52tt.com/protocol/services/im-api"
	"golang.52tt.com/services/tt-rev/esport/common"
	"time"

	"gitlab.ttyuyin.com/avengers/tyr/compatible/proto"
	"gitlab.ttyuyin.com/tt-infra/tyr/log"
	push "golang.52tt.com/clients/push-notification/v2"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/protocol/app/esport_logic"
	imPB "golang.52tt.com/protocol/app/im"
	pushPb "golang.52tt.com/protocol/app/push"
	esport_wechat "golang.52tt.com/protocol/services/esport-wechat"
	kfk_esport_trade "golang.52tt.com/protocol/services/minToolkit/kafka/pb/kafka-esport-trade"
	push_notification "golang.52tt.com/protocol/services/push-notification/v2"
	trade_im_notify "golang.52tt.com/services/tt-rev/esport/common/trade-im-notify"
	"golang.52tt.com/services/tt-rev/esport/esport-trade/internal/store"
)

const (
	ImTypeComm = uint32(iota)
	ImTypeSys
)

func (m *Mgr) PushOrderChangeNotify(ctx context.Context, orderId string, uidList []uint32) error {
	if len(uidList) == 0 {
		log.ErrorWithCtx(ctx, "PushOrderChangeNotify uidList empty orderId:%s, uidList:%v", orderId, uidList)
		return nil
	}

	opt := &esport_logic.OrderChangeNotify{
		OrderId:    orderId,
		NotifyTime: time.Now().Unix(),
	}

	msg, _ := proto.Marshal(opt)

	pushMessage := &pushPb.PushMessage{
		Cmd:     uint32(pushPb.PushMessage_ESPORT_ORDER_CHANGE_PUSH),
		Content: msg,
	}
	pushMessageBytes, _ := proto.Marshal(pushMessage)

	notification := &push_notification.CompositiveNotification{
		Sequence:           uint32(time.Now().Unix()),
		TerminalTypeList:   []uint32{protocol.MobileAndroidTT, protocol.MobileIPhoneTT},
		TerminalTypePolicy: push.DefaultPolicy,
		AppId:              0,
		ProxyNotification: &push_notification.ProxyNotification{
			Type:    uint32(push_notification.ProxyNotification_PUSH),
			Payload: pushMessageBytes,
		},
	}

	err := m.rpcCli.PushCli.PushToUsers(ctx, uidList, notification)
	if err != nil {
		log.ErrorWithCtx(ctx, "PushOrderChangeNotify fail to PushToUsers. orderId:%s, uidList:%v, err:%v", orderId, uidList, err)
		return err
	}

	log.DebugWithCtx(ctx, "PushOrderChangeNotify success. orderId:%s, uidList:%v", orderId, uidList)
	return nil
}

// OrderNotify 订单通知
func (m *Mgr) OrderNotify(ctx context.Context, order *store.OrderRecord) error {
	// 推送通知
	err := m.PushOrderChangeNotify(ctx, order.OrderId, []uint32{order.CoachUid, order.PlayerUid})
	if err != nil {
		log.ErrorWithCtx(ctx, "OrderNotify fail to PushOrderChangeNotify. order:%+v, err:%v", order, err)
	}

	product := order.GetProductPbInfo()

	// kafka事件
	m.orderKfk.SendStatusChangeMsg(ctx, &kfk_esport_trade.EsportTradeStatusChangeEvent{
		OrderId:               order.OrderId,
		PlayerUid:             order.PlayerUid,
		CoachUid:              order.CoachUid,
		PayTime:               order.CreateTime.Unix(),
		Status:                order.Status,
		TotalPrice:            product.GetTotalPrice(),
		ProductId:             product.GetProductId(),
		GameId:                product.GetGameId(),
		EventTime:             time.Now().Unix(),
		UseFirstRoundDiscount:  product.GetPriceInfo().GetHasFirstRoundDiscount(),
		UseNewCustomerDiscount: product.GetNewCustomerUseDetail().GetUseNewCustomerDiscount(),
		CoachTotalPrice:        product.GetCoachTotalPrice(),
	})

	return nil
}

// sendWaitReceiveImMsg 下单后等待接单的im卡片消息
func (m *Mgr) sendWaitReceiveImMsg(ctx context.Context, orderRecord *store.OrderRecord, status uint32) error {
	if orderRecord == nil {
		return nil
	}

	eventType := uint32(esport_logic.EsportOrderImMsg_EVENT_TYPE_WAIT_RECEIVE)
	imType := ImTypeComm
	var uid2MsgId map[uint32]uint64
	var err error
	fromUid, toUid := orderRecord.PlayerUid, orderRecord.CoachUid

	if status != uint32(esport_logic.WaitReceiveOrderStatus_WAIT_RECEIVE_ORDER_STATUS_WAIT) {
		// 消息id，后续状态使用
		uid2MsgId, err = m.cache.GetImSvrMsgId(ctx, orderRecord.OrderId, imType, eventType, []uint32{fromUid, toUid})
		if err != nil {
			log.ErrorWithCtx(ctx, "sendWaitReceiveImMsg fail to GetImSvrMsgId. orderRecord:%+v, err:%v", orderRecord, err)
		}
	}

	productOrder := fillProductOrderPb(orderRecord.GetProductPbInfo())

	// send im msg
	fromMsgId, toMsgId, err := m.tradeImNotify.SendOrderImMsg(ctx,
		&trade_im_notify.UserOrderImMsg{
			Uid:         fromUid,
			ServerMsgId: uid2MsgId[fromUid],
			Ext: &esport_logic.EsportOrderImMsg{
				MsgTitle:     "我已支付，等你接单哦～",
				EventType:    eventType,
				Status:       status,
				OrderId:      orderRecord.OrderId,
				CoachUid:     orderRecord.CoachUid,
				PlayerUid:    orderRecord.PlayerUid,
				EndTime:      orderRecord.EndTime.Unix(),
				ProductOrder: productOrder,
				OrderStatus:  orderRecord.Status,
				Remark:       orderRecord.Remark,
			},
		},
		&trade_im_notify.UserOrderImMsg{
			Uid:         toUid,
			ServerMsgId: uid2MsgId[toUid],
			Ext: &esport_logic.EsportOrderImMsg{
				MsgTitle:     "我已支付，等你接单哦～",
				EventType:    eventType,
				Status:       status,
				OrderId:      orderRecord.OrderId,
				CoachUid:     orderRecord.CoachUid,
				PlayerUid:    orderRecord.PlayerUid,
				EndTime:      orderRecord.EndTime.Unix(),
				ProductOrder: productOrder,
				OrderStatus:  orderRecord.Status,
				Remark:       orderRecord.Remark,
			},
		},
	)
	if err != nil {
		log.ErrorWithCtx(ctx, "sendWaitReceiveImMsg fail to SendOrderImMsg. orderRecord:%+v, err:%v", orderRecord, err)
		return err
	}
	// 标记接触陌生人限制
	_, err = m.rpcCli.ImStrangerGoCli.IncrStrangerMsgLimit(ctx, fromUid, toUid, uint32(imPB.MsgSourceType_MSG_SOURCE_FROM_ESPORT), "", "esport_role", true)
	if err != nil {
		log.ErrorWithCtx(ctx, "sendWaitReceiveImMsg fail to IncrStrangerMsgLimit. fromUid: %d, toUid: %d, err: %v", fromUid, toUid, err)
	}

	// 记录此次的消息id，下次要用
	err = m.cache.SetImSvrMsgId(ctx, orderRecord.OrderId, imType, eventType, map[uint32]uint64{
		fromUid: fromMsgId,
		toUid:   toMsgId,
	}, 30*time.Minute)
	if err != nil {
		log.ErrorWithCtx(ctx, "sendWaitReceiveImMsg fail to SetImSvrMsgId. orderRecord:%+v, err:%v", orderRecord, err)
	}

	if status == uint32(esport_logic.WaitReceiveOrderStatus_WAIT_RECEIVE_ORDER_STATUS_WAIT) {
		// 发普通的系统消息
		_, _, err = m.tradeImNotify.SendImMsg(ctx, uint32(imPB.IM_MSG_TYPE_SYSTEM_NOTIFY),
			&trade_im_notify.UserMsg{Uid: fromUid, Content: "支付成功，等待大神接单"},
			&trade_im_notify.UserMsg{Uid: toUid, Content: "老板已付款，请尽快接单"},
		)
		if err != nil {
			log.ErrorWithCtx(ctx, "sendWaitReceiveImMsg fail to SendImMsg. orderRecord:%+v, err:%v", orderRecord, err)
		}

		m.rpcCli.EsportWechatClint.SendWechatMsg(ctx, &esport_wechat.SendWechatMsgRequest{
			FromUid: fromUid,
			ToUid:   toUid,
			Msg:     "我已支付，等你接单哦",
		})
	}

	return nil
}

// sendOrderCancelImMsg 发送订单取消时的im消息
func (m *Mgr) sendOrderCancelImMsg(ctx context.Context, orderRecord *store.OrderRecord, cancelTy uint32) error {
	var sysEventTy, imStatus uint32
	var playerTitle, playerContent, coachTitle, coachContent string
	product := orderRecord.GetProductPbInfo()

	switch esport_logic.CanceledOrderSubStatus(cancelTy) {
	case esport_logic.CanceledOrderSubStatus_CANCELED_ORDER_SUB_STATUS_PLAYER_CANCEL: // 玩家取消
		sysEventTy = uint32(esport_logic.EsportImSysMsg_EVENT_TYPE_ORDER_CANCELED)
		coachTitle = "老板已取消订单"
		coachContent = fmt.Sprintf("%d 豆已原路返还，可重新沟通后邀请老板下单哦～", product.GetTotalPrice())
		playerTitle = "订单已取消"
		playerContent = fmt.Sprintf("%d 豆已原路返还，可重新下单哦～", product.GetTotalPrice())
		imStatus = uint32(esport_logic.WaitReceiveOrderStatus_WAIT_RECEIVE_ORDER_STATUS_TIMEOUT)

	case esport_logic.CanceledOrderSubStatus_CANCELED_ORDER_SUB_STATUS_COACH_REFUSE: // 电竞陪玩拒绝
		sysEventTy = uint32(esport_logic.EsportImSysMsg_EVENT_TYPE_ORDER_NOT_RECEIVE)
		coachTitle = "已拒绝该订单"
		coachContent = fmt.Sprintf("您已拒绝该订单，%d 豆已原路返还", product.GetTotalPrice())
		playerTitle = "大神暂时无法接单"
		playerContent = fmt.Sprintf("大神已拒绝该订单，%d 豆已原路返还", product.GetTotalPrice())
		imStatus = uint32(esport_logic.WaitReceiveOrderStatus_WAIT_RECEIVE_ORDER_STATUS_REFUSE)

	case esport_logic.CanceledOrderSubStatus_CANCELED_ORDER_SUB_STATUS_COACH_TIMEOUT: // 超时未处理
		sysEventTy = uint32(esport_logic.EsportImSysMsg_EVENT_TYPE_ORDER_NOT_RECEIVE)
		coachTitle = "超时未接单"
		coachContent = fmt.Sprintf("%d 豆已原路返还", product.GetTotalPrice())
		playerTitle = "大神未接单"
		playerContent = fmt.Sprintf("订单已失效，%d 豆已原路返还", product.GetTotalPrice())
		imStatus = uint32(esport_logic.WaitReceiveOrderStatus_WAIT_RECEIVE_ORDER_STATUS_TIMEOUT)

	default:
		log.ErrorWithCtx(ctx, "sendOrderCancelImMsg fail to SendOrderImSysMsg. unknown cancelType. orderRecord:%+v, cancelType:%d",
			orderRecord, cancelTy)
		return nil
	}

	// im卡片消息更新
	err := m.sendWaitReceiveImMsg(ctx, orderRecord, imStatus)
	if err != nil {
		log.ErrorWithCtx(ctx, "sendOrderCancelImMsg fail to sendWaitReceiveImMsg. orderRecord:%+v, cancelType:%d, err:%v",
			orderRecord, cancelTy, err)
	}

	_, _, err = m.tradeImNotify.SendOrderImSysMsg(ctx,
		&trade_im_notify.UserOrderImSysMsg{Uid: orderRecord.PlayerUid, Ext: &esport_logic.EsportImSysMsg{
			OrderId:     orderRecord.OrderId,
			EventType:   sysEventTy,
			MsgTitle:    playerTitle,
			MsgContent:  playerContent,
			CoachUid:    orderRecord.CoachUid,
			PlayerUid:   orderRecord.PlayerUid,
			OrderStatus: orderRecord.Status,
		}},
		&trade_im_notify.UserOrderImSysMsg{Uid: orderRecord.CoachUid, Ext: &esport_logic.EsportImSysMsg{
			OrderId:     orderRecord.OrderId,
			EventType:   sysEventTy,
			MsgTitle:    coachTitle,
			MsgContent:  coachContent,
			CoachUid:    orderRecord.CoachUid,
			PlayerUid:   orderRecord.PlayerUid,
			OrderStatus: orderRecord.Status,
		}},
	)
	if err != nil {
		log.ErrorWithCtx(ctx, "sendOrderCancelImMsg fail to SendOrderImSysMsg. orderRecord:%+v, cancelType:%d, err:%v",
			orderRecord, cancelTy, err)
	}

	return nil
}

// sendNotifyFinishImMsg 提醒完成的im卡片消息
func (m *Mgr) sendNotifyFinishImMsg(ctx context.Context, orderRecord *store.OrderRecord, status uint32) error {
	if orderRecord == nil {
		return nil
	}

	eventType := uint32(esport_logic.EsportOrderImMsg_EVENT_TYPE_NOTIFY_TO_FINISH)
	imType := ImTypeComm
	var uid2MsgId map[uint32]uint64
	var err error
	fromUid, toUid := orderRecord.CoachUid, orderRecord.PlayerUid

	if status != uint32(esport_logic.NotifyToFinishStatus_NOTIFY_TO_FINISH_STATUS_WAIT) {
		// 消息id，后续状态使用
		uid2MsgId, err = m.cache.GetImSvrMsgId(ctx, orderRecord.OrderId, imType, eventType, []uint32{fromUid, toUid})
		if err != nil {
			log.ErrorWithCtx(ctx, "sendNotifyFinishImMsg fail to GetImSvrMsgId. orderRecord:%+v, err:%v", orderRecord, err)
		}

		if len(uid2MsgId) == 0 {
			// 若不存在，说明之前没有发过提醒完成消息，直接返回
			return nil
		}
	}

	ext := &esport_logic.EsportOrderImMsg{
		MsgTitle:     "服务已完成，记得及时确认哦",
		EventType:    eventType,
		Status:       status,
		OrderId:      orderRecord.OrderId,
		CoachUid:     orderRecord.CoachUid,
		PlayerUid:    orderRecord.PlayerUid,
		EndTime:      orderRecord.EndTime.Unix(),
		ProductOrder: fillProductOrderPb(orderRecord.GetProductPbInfo()),
		OrderStatus:  orderRecord.Status,
	}

	// send im msg
	fromMsgId, toMsgId, err := m.tradeImNotify.SendOrderImMsg(ctx,
		&trade_im_notify.UserOrderImMsg{
			Uid:         fromUid,
			ServerMsgId: uid2MsgId[fromUid],
			Ext:         ext,
		},
		&trade_im_notify.UserOrderImMsg{
			Uid:         toUid,
			ServerMsgId: uid2MsgId[toUid],
			Ext:         ext,
		},
	)
	if err != nil {
		log.ErrorWithCtx(ctx, "sendNotifyFinishImMsg fail to SendOrderImMsg. orderRecord:%+v, err:%v", orderRecord, err)
		return err
	}

	// 记录此次的消息id，下次要用
	err = m.cache.SetImSvrMsgId(ctx, orderRecord.OrderId, imType, eventType, map[uint32]uint64{
		fromUid: fromMsgId,
		toUid:   toMsgId,
	}, 30*time.Minute)
	if err != nil {
		log.ErrorWithCtx(ctx, "sendNotifyFinishImMsg fail to SetImSvrMsgId. orderRecord:%+v, err:%v", orderRecord, err)
	}
	return nil
}

// sendOrderFinishImMsg 发送订单完成时的im消息
func (m *Mgr) sendOrderFinishImMsg(ctx context.Context, orderRecord *store.OrderRecord) error {
	// im卡片消息更新
	err := m.sendNotifyFinishImMsg(ctx, orderRecord, uint32(esport_logic.NotifyToFinishStatus_NOTIFY_TO_FINISH_STATUS_FINISH))
	if err != nil {
		log.ErrorWithCtx(ctx, "sendOrderFinishImMsg fail to sendNotifyFinishImMsg. orderRecord:%+v, err:%v",
			orderRecord, err)
	}

	// 发送订单完成时的im系统消息，未评价状态
	err = m.sendOrderFinishImSysMsg(ctx, orderRecord, uint32(esport_logic.ImSysMsgFinishStatus_IM_SYS_MSG_FINISH_STATUS_NOT_EVALUATE))
	if err != nil {
		log.ErrorWithCtx(ctx, "sendOrderFinishImMsg fail to SendOrderImSysMsg. orderRecord:%+v, err:%v",
			orderRecord, err)
	}

	// 获取订单完成的提示文案
	hintRsp, err := m.GetOrderHint(ctx, &esport_trade.GetOrderHintRequest{
		Uid:       orderRecord.CoachUid,
		Scene:     esport_trade.GetOrderHintRequest_SCENE_FINISH_ORDER_COACH_IM,
		OrderType: orderRecord.GetProductPbInfo().GetPriceInfo().GetDiscountType(),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "sendOrderFinishImMsg fail to GetOrderHint. orderRecord:%+v, err:%v", orderRecord, err)
		return nil
	}
	if hintRsp.GetHintText() == "" {
		return nil
	}

	// 发送系统IM消息
	_, err = m.tradeImNotify.SendOneSideImMsg(ctx, uint32(imPB.IM_MSG_TYPE_SYSTEM_NOTIFY), orderRecord.PlayerUid, orderRecord.CoachUid, 1, &trade_im_notify.ImMsg{Content: hintRsp.GetHintText()})
	if err != nil {
		log.ErrorWithCtx(ctx, "sendOrderFinishImMsg fail to SendOrderHint. orderRecord:%+v, err:%v", orderRecord, err)
	} else {
		log.InfoWithCtx(ctx, "sendOrderFinishImMsg success to SendOrderHint. orderRecord:%+v, hint:%s", orderRecord, hintRsp.GetHintText())
	}

	return nil
}

// sendOrderFinishImSysMsg 发送订单完成时的im系统消息(带评价状态)
func (m *Mgr) sendOrderFinishImSysMsg(ctx context.Context, orderRecord *store.OrderRecord, status uint32) error {
	if orderRecord == nil {
		return nil
	}

	eventType := uint32(esport_logic.EsportImSysMsg_EVENT_TYPE_ORDER_FINISH)
	imType := ImTypeSys
	var uid2MsgId map[uint32]uint64
	var err error
	playerContent := "请为大神的服务做出评价"
	coachContent := "本次订单获得的积分已到账，请查收"
	fromUid, toUid := orderRecord.PlayerUid, orderRecord.CoachUid

	if status == uint32(esport_logic.ImSysMsgFinishStatus_IM_SYS_MSG_FINISH_STATUS_HAS_EVALUATE) {
		// 消息id，后续状态使用
		uid2MsgId, err = m.cache.GetImSvrMsgId(ctx, orderRecord.OrderId, imType, eventType, []uint32{fromUid, toUid})
		if err != nil {
			log.ErrorWithCtx(ctx, "sendOrderFinishImSysMsg fail to GetImSvrMsgId. orderRecord:%+v, err:%v", orderRecord, err)
		}

		if len(uid2MsgId) == 0 {
			// 若不存在，说明之前没有发过提醒完成消息，直接返回
			return nil
		}
		playerContent = "平台下单服务保障，不满意可以右上角点击举报"
		coachContent = "引导老板再次下单有助于提升曝光量哦"
	}

	sysEventTy := uint32(esport_logic.EsportImSysMsg_EVENT_TYPE_ORDER_FINISH)
	fromMsgId, toMsgId, err := m.tradeImNotify.SendOrderImSysMsg(ctx,
		&trade_im_notify.UserOrderImSysMsg{Uid: fromUid, ServerMsgId: uid2MsgId[fromUid], Ext: &esport_logic.EsportImSysMsg{
			OrderId:     orderRecord.OrderId,
			EventType:   sysEventTy,
			Status:      status,
			MsgTitle:    "订单已完成",
			MsgContent:  playerContent,
			CoachUid:    orderRecord.CoachUid,
			PlayerUid:   orderRecord.PlayerUid,
			OrderStatus: orderRecord.Status,
		}},
		&trade_im_notify.UserOrderImSysMsg{Uid: toUid, ServerMsgId: uid2MsgId[toUid], Ext: &esport_logic.EsportImSysMsg{
			OrderId:     orderRecord.OrderId,
			EventType:   sysEventTy,
			Status:      status,
			MsgTitle:    "订单已完成",
			MsgContent:  coachContent,
			CoachUid:    orderRecord.CoachUid,
			PlayerUid:   orderRecord.PlayerUid,
			OrderStatus: orderRecord.Status,
		}},
	)
	if err != nil {
		log.ErrorWithCtx(ctx, "sendOrderFinishImSysMsg fail to SendOrderImSysMsg. orderRecord:%+v, err:%v",
			orderRecord, err)
	}

	// 记录此次的消息id，下次要用
	_ = m.cache.SetImSvrMsgId(ctx, orderRecord.OrderId, imType, eventType, map[uint32]uint64{
		fromUid: fromMsgId,
		toUid:   toMsgId,
	}, 72*time.Hour)
	return nil
}

func (m *Mgr) NotifyCouponChange(ctx context.Context, uid uint32) error {
	opt := &esport_logic.CouponChangePushMsg{}
	msg, _ := proto.Marshal(opt)

	pushMessage := &pushPb.PushMessage{
		Cmd:     uint32(pushPb.PushMessage_ESPORT_COUPON_PUSH),
		Content: msg,
	}
	pushMessageBytes, _ := proto.Marshal(pushMessage)

	notification := &push_notification.CompositiveNotification{
		Sequence:           uint32(time.Now().Unix()),
		TerminalTypeList:   []uint32{protocol.MobileAndroidTT, protocol.MobileIPhoneTT},
		TerminalTypePolicy: push.DefaultPolicy,
		AppId:              0,
		ProxyNotification: &push_notification.ProxyNotification{
			Type:    uint32(push_notification.ProxyNotification_PUSH),
			Payload: pushMessageBytes,
		},
	}

	err := m.rpcCli.PushCli.PushToUsers(ctx, []uint32{uid}, notification)
	if err != nil {
		log.ErrorWithCtx(ctx, "NotifyCouponChange err: %v, uid: %d", err, uid)
		return err
	}

	log.DebugWithCtx(ctx, "NotifyCouponChange success, uid: %d", uid)
	return nil
}

func fillProductOrderPb(product *esport_trade.ProductOrder) *esport_logic.ProductOrder {
	return &esport_logic.ProductOrder{
		ProductId:  product.GetProductId(),
		Name:       product.GetName(),
		Icon:       product.GetIcon(),
		Count:      product.GetCount(),
		TotalPrice: product.GetTotalPrice(),
		PriceInfo: &esport_logic.PriceInfo{
			Price:                 product.GetPriceInfo().GetPrice(),
			PriceUnit:             product.GetPriceInfo().GetPriceUnit(),
			MeasureCnt:            product.GetPriceInfo().GetMeasureCnt(),
			MeasureUnit:           product.GetPriceInfo().GetMeasureUnit(),
			HasFirstRoundDiscount: product.GetPriceInfo().GetHasFirstRoundDiscount(),
			FirstRoundPrice:       product.GetPriceInfo().GetFirstRoundPrice(),
			HasDiscount:           product.GetPriceInfo().GetHasDiscount(),
			DiscountType:          product.GetPriceInfo().GetDiscountType(),
			DiscountDesc:          product.GetPriceInfo().GetDiscountDesc(),
			DiscountPrice:         product.GetPriceInfo().GetDiscountPrice(),
		},
		GuaranteeWinText: common.GetGranteeWinText(product.GetIsGuaranteeWin(), product.GetGuaranteeWinTexts()),
		CoachTotalPrice:  product.GetCoachTotalPrice(),
		CouponUseDetail: &esport_logic.CouponUseDetail{
			UseCoupon:   product.GetCouponUseDetail().GetUseCoupon(),
			CouponMoney: product.GetCouponUseDetail().GetCouponMoney(),
		},
		NewCustomerUseDetail: &esport_logic.NewCustomerUseDetail{
			UseNewCustomerDiscount: product.GetNewCustomerUseDetail().GetUseNewCustomerDiscount(),
			NewCustomerPrice:       product.GetNewCustomerUseDetail().GetNewCustomerPrice(),
			PlatBonusFee:           product.GetNewCustomerUseDetail().GetPlatBonusFee(),
		},
	}
}

func (m *Mgr) sendOfficialAccountMsg(ctx context.Context, namespace string, toUid uint32, text *impb.Text) (msgId uint32, err error) {
	sendReq := &impb.SendPublicAccountTextReq{
		Namespace: namespace,
		PublicAccount: &impb.PublicAccount{
			PublicType: impb.PublicAccount_SYSTEM,
			BindedId:   9991, // 电竞助手绑定id
		},
		ToUid: toUid,
		Text:  text,
	}
	sendRsp, err := m.rpcCli.ImApiClient.SendPublicAccountText(ctx, sendReq)
	if err != nil {
		log.ErrorWithCtx(ctx, "sendOfficialAccountMsg fail, req:%+v, err:%v", sendReq, err)
		return
	}
	log.InfoWithCtx(ctx, "sendOfficialAccountMsg success, req:%+v, rsp:%+v", sendReq, sendRsp)

	if len(sendRsp.TaskList) > 0 {
		msgId = uint32(sendRsp.TaskList[0].GetToSeq().GetSvrMsgId())
	}
	return
}

func (m *Mgr) revokeOfficialAccountMsg(ctx context.Context, namespace string, toUid, msgId uint32) error {
	cancelReq := &impb.CancelMsgRequest{
		Namespace:     namespace,
		CancelMsgType: uint32(impb.CancelMsgType_CANCEL_MSG_TYPE_DELETE),
		From: &impb.Entity{
			Type: impb.Entity_PUBLIC,
			Id:   m.bc.GetOfficialAccountPublicId(),
		},
		To: &impb.Entity{
			Type: impb.Entity_USER,
			Id:   toUid,
		},
		CancelMsgInfo: &impb.CancelMsgInfo{TargetMsgId: msgId},
	}
	cancelRsp, err := m.rpcCli.ImApiClient.CancelMsg(ctx, cancelReq)
	if err != nil {
		log.ErrorWithCtx(ctx, "revokeOfficialAccountMsg fail, req:%+v, err:%v", cancelReq, err)
		return err
	}
	log.InfoWithCtx(ctx, "revokeOfficialAccountMsg success, req:%+v, rsp:%+v", cancelReq, cancelRsp)
	return nil
}
