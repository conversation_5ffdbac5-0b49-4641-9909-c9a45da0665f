package mgr

import (
	"context"
	"errors"
	"fmt"
	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"
	account_client "golang.52tt.com/clients/account"
	"golang.52tt.com/pkg/protocol"
	accountpb "golang.52tt.com/protocol/services/accountsvr"
	esport_customer "golang.52tt.com/protocol/services/esport-customer"
	"golang.52tt.com/protocol/services/esport_admin_logic"
	"golang.52tt.com/protocol/services/esport_role"
	guild_pb "golang.52tt.com/protocol/services/guildsvr"
	guildsvr "golang.52tt.com/protocol/services/guildsvr"
	"testing"
)

func Test_GetCustomerAccounts_Failure_GuildServiceError(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	// Setup the helper
	helper := newHelperForTest(t)

	ctx := context.Background()

	// Prepare the request
	req := &esport_admin_logic.GetCustomerAccountsRequest{
		Page:        1,
		Size:        10,
		GuildId:     2,
		CustomerUid: []uint32{3},
	}

	// Mock data for customer service
	customerAccounts := []*esport_customer.CustomerAccount{
		{CustomerUid: 3, GuildId: 2, Creator: "creator1", CreateTime: **********, Password: "password1", Status: esport_customer.CustomerAccountStatus(1)},
	}
	customerResp := &esport_customer.GetCustomerAccountsResponse{
		CustomerAccounts: customerAccounts,
		Total:            1,
	}
	helper.getCustomerCli().EXPECT().GetCustomerAccounts(gomock.Any(), gomock.Any()).Return(customerResp, nil)

	// Mock the guild service to return an error
	expectedError := protocol.NewExactServerError(nil, -2, "guild service error")
	helper.getGuildCli().EXPECT().GetGuildBat(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, expectedError)

	// Call the target function
	manager := helper.Manager
	resp, err := manager.GetCustomerAccounts(ctx, req)

	// Assertions
	assert.Nil(t, resp)
	assert.EqualError(t, err, fmt.Sprintf("failed to get guild details from guildsvr: %s", expectedError))
}

func Test_GetCustomerAccounts_Failure_AccountServiceError(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	h := newHelperForTest(t)

	ctx := context.Background()
	req := &esport_admin_logic.GetCustomerAccountsRequest{
		Page:        1,
		Size:        10,
		GuildId:     123,
		CustomerUid: []uint32{456},
	}

	customerAccounts := []*esport_customer.CustomerAccount{
		{
			CustomerUid: 123,
			GuildId:     123,
			Creator:     "creator",
			CreateTime:  *********,
			Password:    "password",
			Status:      esport_customer.CustomerAccountStatus(1),
		},
	}

	customerResponse := &esport_customer.GetCustomerAccountsResponse{
		CustomerAccounts: customerAccounts,
		Total:            1,
	}
	expectedErr := protocol.NewExactServerError(nil, -2, "account service error")

	h.getCustomerCli().EXPECT().GetCustomerAccounts(gomock.Any(), gomock.Any()).Return(customerResponse, nil)
	h.getAccountCli().EXPECT().GetUsersMap(gomock.Any(), gomock.Any()).Return(nil, expectedErr)
	h.getGuildCli().EXPECT().GetGuildBat(gomock.Any(), gomock.Any(), gomock.Any()).Return(&guild_pb.GetGuildBatResp{}, nil)

	resp, err := h.Manager.GetCustomerAccounts(ctx, req)

	assert.Nil(t, resp)
	assert.Error(t, err)
}

func Test_GetCustomerAccounts_Failure_CustomerServiceError(t *testing.T) {
	// Set up the mock controller
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	// Create a new helper for the test
	s := newHelperForTest(t)

	// Create a context
	ctx := context.Background()

	// Create the request
	req := &esport_admin_logic.GetCustomerAccountsRequest{
		Page:        1,
		Size:        10,
		GuildId:     123,
		CustomerUid: []uint32{456},
	}

	// Set up the expected error
	expectedErr := errors.New("customer service error")

	// Set up the mock to return an error
	s.getCustomerCli().EXPECT().GetCustomerAccounts(ctx, gomock.Any()).Return(nil, expectedErr)

	// Call the function
	resp, err := s.GetCustomerAccounts(ctx, req)

	// Validate the results
	assert.Nil(t, resp)
	assert.EqualError(t, err, "failed to get customer accounts from esport-customer: customer service error")
}

func Test_GetCustomerAccounts_Success(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	helper := newHelperForTest(t)

	ctx := context.Background()
	req := &esport_admin_logic.GetCustomerAccountsRequest{
		Page:        1,
		Size:        10,
		GuildId:     1234,
		CustomerUid: []uint32{5678},
	}

	customerAccounts := []*esport_customer.CustomerAccount{
		{CustomerUid: 5678, GuildId: 1234, Creator: "creator1", CreateTime: **********, Password: "password", Status: esport_customer.CustomerAccountStatus(1)},
	}
	customerResp := &esport_customer.GetCustomerAccountsResponse{
		CustomerAccounts: customerAccounts,
		Total:            1,
	}

	accountUsers := map[uint32]*accountpb.UserResp{
		5678: {Alias: "username1", Nickname: "nickname1"},
	}
	accountResp := accountUsers

	guildList := []*guild_pb.GuildResp{
		{GuildId: 1234, ShortId: 4321, Name: "guild1"},
	}
	guildResp := &guild_pb.GetGuildBatResp{
		GuildList: guildList,
	}

	helper.getCustomerCli().EXPECT().GetCustomerAccounts(gomock.Any(), gomock.Any()).Return(customerResp, nil)
	helper.getAccountCli().EXPECT().GetUsersMap(gomock.Any(), gomock.Any()).Return(accountResp, nil)
	helper.getGuildCli().EXPECT().GetGuildBat(gomock.Any(), gomock.Any(), gomock.Any()).Return(guildResp, nil)

	mgr := helper.Manager
	response, err := mgr.GetCustomerAccounts(ctx, req)
	assert.NoError(t, err)
	assert.NotNil(t, response)
	assert.Equal(t, 1, len(response.CustomerAccounts))
	assert.Equal(t, "username1", response.CustomerAccounts[0].CustomerTtid)
	assert.Equal(t, "nickname1", response.CustomerAccounts[0].CustomerNickname)
	assert.Equal(t, uint32(1234), response.CustomerAccounts[0].GuildId)
	assert.Equal(t, uint32(4321), response.CustomerAccounts[0].ShortGuildId)
	assert.Equal(t, "guild1", response.CustomerAccounts[0].GuildName)
	assert.Equal(t, "creator1", response.CustomerAccounts[0].Creator)
	assert.Equal(t, int64(**********), response.CustomerAccounts[0].CreateTime)
	assert.Equal(t, "password", response.CustomerAccounts[0].Password)
	assert.Equal(t, esport_admin_logic.CustomerAccountStatus(1), response.CustomerAccounts[0].Status)
	assert.Equal(t, int32(1), response.Total)
}

func Test_GetCustomerAccounts_EmptyResponse(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	helper := newHelperForTest(t)

	ctx := context.Background()

	// Mock the responses for the customer, account, and guild clients
	helper.getCustomerCli().EXPECT().GetCustomerAccounts(gomock.Any(), gomock.Any()).Return(&esport_customer.GetCustomerAccountsResponse{
		CustomerAccounts: []*esport_customer.CustomerAccount{},
		Total:            0,
	}, nil)

	helper.getAccountCli().EXPECT().GetUsersMap(gomock.Any(), gomock.Any()).Return(map[uint32]*account_client.User{}, nil)

	helper.getGuildCli().EXPECT().GetGuildBat(gomock.Any(), uint32(0), gomock.Any()).Return(&guildsvr.GetGuildBatResp{
		GuildList: []*guildsvr.GuildResp{},
	}, nil)

	// Create a request
	req := &esport_admin_logic.GetCustomerAccountsRequest{
		Page:        1,
		Size:        10,
		GuildId:     123,
		CustomerUid: []uint32{456},
	}

	// Call the function under test
	resp, err := helper.GetCustomerAccounts(ctx, req)

	// Assert the results
	assert.NoError(t, err)
	assert.NotNil(t, resp)
	assert.Equal(t, int32(0), resp.Total)
	assert.Empty(t, resp.CustomerAccounts)
}

func TestManager_GetManagedGods_RoleRespTotalZero(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	// Initializing the helper for the test
	s := newHelperForTest(t)

	// Mocking the inputs
	ctx := context.Background()
	req := &esport_admin_logic.GetManagedGodsRequest{
		Page:     1,
		Size:     10,
		GuildId:  1,
		CoachUid: 2,
	}

	// Mocking the expected responses
	roleResp := &esport_role.GetCoachByGuildResp{
		CoachUids: []uint32{},
		Total:     0,
	}

	// Setting up the expectations
	s.getRoleCli().EXPECT().GetCoachByGuild(gomock.Any(), gomock.Any()).Return(roleResp, nil)

	// Calling the function under test
	resp, err := s.GetManagedGods(ctx, req)

	// Asserting the results
	expectedResp := &esport_admin_logic.GetManagedGodsResponse{
		ManagedGods: []*esport_admin_logic.ManagedGod{},
		Total:       0,
	}

	assert.NoError(t, err)
	assert.Equal(t, expectedResp, resp)
}

func TestManager_GetManagedGods_AccountClientError(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	helper := newHelperForTest(t)

	ctx := context.Background()
	req := &esport_admin_logic.GetManagedGodsRequest{
		Page:     1,
		Size:     10,
		GuildId:  42,
		CoachUid: 1234,
	}

	// Mock responses
	roleResp := &esport_role.GetCoachByGuildResp{
		CoachUids: []uint32{1234, 5678},
		Total:     2,
	}

	helper.getRoleCli().EXPECT().GetCoachByGuild(ctx, gomock.Any()).Return(roleResp, nil)

	customerResp := &esport_customer.GetManagedGodsResponse{
		ManagedGods: []*esport_customer.ManagedGod{
			{Uid: 1234},
		},
	}

	helper.getCustomerCli().EXPECT().GetManagedGods(ctx, gomock.Any()).Return(customerResp, nil)

	expectedErr := errors.New("failed to get account info")
	helper.getAccountCli().EXPECT().GetUsersMap(ctx, gomock.Any()).Return(nil, protocol.NewExactServerError(nil, -2, expectedErr.Error()))

	manager := helper.Manager

	// Call the method under test
	resp, err := manager.GetManagedGods(ctx, req)

	// Assertions
	assert.Nil(t, resp)
	assert.Error(t, err)
}

func TestManager_GetManagedGods_CustomerCliError(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	s := newHelperForTest(t)

	ctx := context.Background()

	req := &esport_admin_logic.GetManagedGodsRequest{
		Page:     1,
		Size:     10,
		GuildId:  100,
		CoachUid: 200,
	}

	roleResp := &esport_role.GetCoachByGuildResp{
		CoachUids: []uint32{300, 400},
		Total:     2,
	}

	s.getRoleCli().EXPECT().GetCoachByGuild(gomock.Any(), gomock.Any()).Return(roleResp, nil)

	expectedErr := errors.New("mock customer service error")
	s.getCustomerCli().EXPECT().GetManagedGods(gomock.Any(), gomock.Any()).Return(nil, expectedErr)

	resp, err := s.Manager.GetManagedGods(ctx, req)

	assert.Nil(t, resp)
	assert.Error(t, err)
}

func TestManager_GetManagedGods_RoleCliError(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	helper := newHelperForTest(t)

	ctx := context.Background()

	req := &esport_admin_logic.GetManagedGodsRequest{
		Page:     1,
		Size:     10,
		GuildId:  123,
		CoachUid: 0,
	}

	expectedErr := errors.New("failed to get coach by guild")

	helper.getRoleCli().EXPECT().GetCoachByGuild(gomock.Any(), gomock.Any()).Return(nil, expectedErr)

	resp, err := helper.Manager.GetManagedGods(ctx, req)

	assert.Nil(t, resp)
	assert.EqualError(t, err, "failed to get coach by guild: failed to get coach by guild")
}

func TestManager_GetManagedGods_Success(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	helper := newHelperForTest(t)
	ctx := context.Background()

	req := &esport_admin_logic.GetManagedGodsRequest{
		Page:     1,
		Size:     10,
		GuildId:  1,
		CoachUid: 123,
	}

	// Mock responses for dependencies
	roleResp := &esport_role.GetCoachByGuildResp{
		CoachUids: []uint32{123, 456},
		Total:     2,
	}
	customerResp := &esport_customer.GetManagedGodsResponse{
		ManagedGods: []*esport_customer.ManagedGod{
			{Uid: 123, CustomerAccount: &esport_customer.CustomerAccount{
				CustomerUid: 789,
			}},
			{Uid: 456, CustomerAccount: &esport_customer.CustomerAccount{
				CustomerUid: 101,
			}},
		},
	}
	accountResp := map[uint32]*accountpb.UserResp{
		123: {Alias: "coach1", Nickname: "Coach One"},
		456: {Alias: "coach2", Nickname: "Coach Two"},
		789: {Nickname: "Customer One"},
		101: {Nickname: "Customer Two"},
	}

	// Setting up the expectations
	helper.getRoleCli().EXPECT().GetCoachByGuild(gomock.Any(), gomock.Any()).Return(roleResp, nil)
	helper.getCustomerCli().EXPECT().GetManagedGods(gomock.Any(), gomock.Any()).Return(customerResp, nil)
	helper.getAccountCli().EXPECT().GetUsersMap(gomock.Any(), gomock.Any()).Return(accountResp, nil)

	// Actual call
	out, err := helper.GetManagedGods(ctx, req)

	// Expected output
	expectedOut := &esport_admin_logic.GetManagedGodsResponse{
		ManagedGods: []*esport_admin_logic.ManagedGod{
			{Ttid: "coach1", Uid: 123, Nickname: "Coach One", Status: esport_admin_logic.ManagedGodStatus_MANAGED_GOD_STATUS_MANAGED, CustomerUid: 789, CustomerNickname: "Customer One"},
			{Ttid: "coach2", Uid: 456, Nickname: "Coach Two", Status: esport_admin_logic.ManagedGodStatus_MANAGED_GOD_STATUS_MANAGED, CustomerUid: 101, CustomerNickname: "Customer Two"},
		},
		Total: 2,
	}

	// Assertions
	assert.NoError(t, err)
	assert.Equal(t, expectedOut, out)
}

func TestManager_GetManagedGods_MissingAccountInfo(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	helper := newHelperForTest(t)

	ctx := context.Background()
	req := &esport_admin_logic.GetManagedGodsRequest{
		Page:     1,
		Size:     10,
		GuildId:  1234,
		CoachUid: 5678,
	}

	roleResp := &esport_role.GetCoachByGuildResp{
		CoachUids: []uint32{1, 2, 3},
		Total:     3,
	}

	managedGodsResp := &esport_customer.GetManagedGodsResponse{
		ManagedGods: []*esport_customer.ManagedGod{
			{Uid: 2},
		},
	}

	accountResp := map[uint32]*accountpb.UserResp{
		1: {Username: "User1", Nickname: "Nickname1"},
		2: {Username: "User2", Nickname: "Nickname2"},
		// 3 is missing in this response to simulate missing account info
	}

	helper.getRoleCli().EXPECT().GetCoachByGuild(ctx, gomock.Any()).Return(roleResp, nil)
	helper.getCustomerCli().EXPECT().GetManagedGods(ctx, gomock.Any()).Return(managedGodsResp, nil)
	helper.getAccountCli().EXPECT().GetUsersMap(ctx, gomock.Any()).Return(accountResp, nil)

	mgr := helper.Manager
	resp, err := mgr.GetManagedGods(ctx, req)

	assert.NoError(t, err)
	assert.NotNil(t, resp)
	assert.Equal(t, 2, len(resp.ManagedGods))

	// Check that the managed god with UID 3 has no account info
	for _, managedGod := range resp.ManagedGods {
		if managedGod.Uid == 3 {
			assert.Empty(t, managedGod.Ttid)
			assert.Empty(t, managedGod.Nickname)
		}
	}
}

func TestRemoveManagedGod_CustomerServiceError(t *testing.T) {
	// Set up the gomock controller and defer its finish method
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	// Create a helperForTest instance
	helper := newHelperForTest(t)

	// Define the inputs for the test
	ctx := context.Background()
	customerUid := uint32(12345)
	coachUids := []uint32{1, 2, 3}

	// Expected error from the mock CustomerServiceClient
	expectedErr := errors.New("customer service error")

	// Set up the expectation on the mock CustomerServiceClient
	helper.getCustomerCli().EXPECT().
		RemoveManagedGod(gomock.Any(), &esport_customer.RemoveManagedGodRequest{
			CustomerUid: customerUid,
			CoachUid:    coachUids,
		}).
		Return(nil, expectedErr)

	// Call the method under test
	err := helper.Manager.RemoveManagedGod(ctx, customerUid, coachUids)

	// Assert that the error matches the expected error
	assert.EqualError(t, err, "failed to remove managed god: customer service error")
}

func TestRemoveManagedGod_Success(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	// Create a new helper for the test
	helper := newHelperForTest(t)

	ctx := context.Background()
	customerUid := uint32(1)
	coachUids := []uint32{2, 3}

	// Mock the expected call and return
	helper.getCustomerCli().EXPECT().RemoveManagedGod(
		gomock.Any(),
		&esport_customer.RemoveManagedGodRequest{CustomerUid: customerUid, CoachUid: coachUids},
	).Return(&esport_customer.RemoveManagedGodResponse{Success: true}, nil)

	// Call the method
	err := helper.Manager.RemoveManagedGod(ctx, customerUid, coachUids)

	// Assert no error
	assert.NoError(t, err)
}

func TestRemoveManagedGod_EmptyCoachUids(t *testing.T) {
	// Create a new gomock controller
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	// Create a new helper for testing
	helper := newHelperForTest(t)

	// Create a new context
	ctx := context.Background()

	// Define the inputs
	customerUid := uint32(1)
	coachUids := []uint32{}

	// Mock the RemoveManagedGod method to return a successful response
	helper.getCustomerCli().EXPECT().RemoveManagedGod(ctx, &esport_customer.RemoveManagedGodRequest{
		CustomerUid: customerUid,
		CoachUid:    coachUids,
	}).Return(&esport_customer.RemoveManagedGodResponse{Success: true}, nil)

	// Call the target method
	err := helper.RemoveManagedGod(ctx, customerUid, coachUids)

	// Assert that there was no error
	assert.NoError(t, err)
}

func TestAddManagedGod_InvalidCustomerUid(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	// Setup helper
	h := newHelperForTest(t)

	// Define context
	ctx := context.Background()

	// Mock the expected behavior
	expectedErr := fmt.Errorf("invalid customer uid")
	h.getCustomerCli().EXPECT().AddManagedGod(ctx, gomock.Any()).Return(nil, expectedErr)

	// Call the actual method
	err := h.Manager.AddManagedGod(ctx, 0, []uint32{1, 2, 3}) // Invalid customerUid

	// Assert
	assert.Error(t, err)
	assert.Equal(t, "failed to add managed god: invalid customer uid", err.Error())
}

func TestAddManagedGod_Failure(t *testing.T) {
	// Setup the gomock controller
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	// Create the helper for test
	helper := newHelperForTest(t)

	// Create a context
	ctx := context.Background()

	// Define the input parameters
	customerUid := uint32(1)
	coachUids := []uint32{2, 3}

	// Create the expected request
	req := &esport_customer.AddManagedGodRequest{
		CustomerUid: customerUid,
		CoachUid:    coachUids,
	}

	// Define the error to be returned
	expectedErr := errors.New("some error")

	// Setup the mock expectation
	helper.getCustomerCli().EXPECT().AddManagedGod(ctx, req).Return(nil, expectedErr)

	// Call the function
	err := helper.Manager.AddManagedGod(ctx, customerUid, coachUids)

	// Assert the error
	assert.EqualError(t, err, "failed to add managed god: some error")
}

func TestAddManagedGod_ContextCancelled(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	helper := newHelperForTest(t)
	mgr := &Manager{customerCli: helper.getCustomerCli()}

	ctx, cancel := context.WithCancel(context.Background())
	cancel() // Cancel the context immediately

	customerUid := uint32(1)
	coachUids := []uint32{2, 3}

	helper.getCustomerCli().EXPECT().AddManagedGod(gomock.Any(), gomock.Any()).
		Return(nil, errors.New("context cancelled"))

	err := mgr.AddManagedGod(ctx, customerUid, coachUids)
	expectedError := "failed to add managed god: context cancelled"

	assert.EqualError(t, err, expectedError)
}

func TestAddManagedGod_Success(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	// Create a helperForTest instance
	helper := newHelperForTest(t)
	manager := helper.Manager

	// Define the input parameters
	ctx := context.Background()
	customerUid := uint32(12345)
	coachUids := []uint32{111, 222, 333}

	// Set up the expected calls and return values
	expectedReq := &esport_customer.AddManagedGodRequest{
		CustomerUid: customerUid,
		CoachUid:    coachUids,
	}

	helper.getCustomerCli().EXPECT().AddManagedGod(ctx, expectedReq).Return(&esport_customer.AddManagedGodResponse{Success: true}, nil)

	// Call the method
	err := manager.AddManagedGod(ctx, customerUid, coachUids)

	// Assert the expectations
	assert.NoError(t, err)
}

func TestAddManagedGod_EmptyCoachUids(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	helper := newHelperForTest(t)

	ctx := context.Background()
	customerUid := uint32(123)
	coachUids := []uint32{}

	helper.getCustomerCli().EXPECT().AddManagedGod(ctx, &esport_customer.AddManagedGodRequest{
		CustomerUid: customerUid,
		CoachUid:    coachUids,
	}).Return(&esport_customer.AddManagedGodResponse{}, nil)

	err := helper.Manager.AddManagedGod(ctx, customerUid, coachUids)

	assert.NoError(t, err)
}

func TestCreateCustomerAccount_FailToCreateAccount(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	// Create a helper instance for testing
	s := newHelperForTest(t)
	ctx := context.Background()
	guildId := uint32(123)
	password := "testPassword"
	creator := "testCreator"

	// Mock the account creation to return an error
	expectedErr := fmt.Errorf("failed to create account")
	s.getAccountCli().EXPECT().AutoBatchCreateUser(ctx, uint32(1), uint32(accountpb.USER_SOURCE_USER_SOURCE_TT), gomock.Any()).Return(nil, expectedErr)

	// Call the function
	customerAccount, err := s.CreateCustomerAccount(ctx, guildId, password, creator)

	// Assertions
	assert.Nil(t, customerAccount)
	assert.EqualError(t, err, "failed to create account: failed to create account")
}

func TestCreateCustomerAccount_FailToGetGuildInfo(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	// Create a mock manager with the helperForTest struct
	mgr := newHelperForTest(t)

	ctx := context.Background()
	guildId := uint32(1)
	password := "testpassword"
	creator := "testcreator"

	// Mock the AutoBatchCreateUser response
	accountResp := []*accountpb.UserResp{
		{
			Uid:      12345,
			Username: "testuser",
			Nickname: "testnickname",
		},
	}
	mgr.getAccountCli().EXPECT().AutoBatchCreateUser(gomock.Any(), uint32(1), uint32(accountpb.USER_SOURCE_USER_SOURCE_TT), gomock.Any()).Return(accountResp, nil)

	// Mock the GetGuild response to return an error
	serverError := protocol.NewExactServerError(nil, -2, "Failed to get guild info")
	mgr.getGuildCli().EXPECT().GetGuild(gomock.Any(), guildId).Return(nil, serverError)

	// Call the target function
	result, err := mgr.CreateCustomerAccount(ctx, guildId, password, creator)

	// Assert the results
	assert.Nil(t, result)
	assert.Error(t, err)
}

func TestCreateCustomerAccount_FailToSaveCustomerAccount(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	helper := newHelperForTest(t)

	ctx := context.Background()

	guildId := uint32(1)
	password := "password123"
	creator := "creator123"

	accountResp := []*accountpb.UserResp{
		{
			Username: "testuser",
			Uid:      12345,
			Nickname: "testnickname",
		},
	}

	guildResp := &guild_pb.GuildResp{
		Name: "testguild",
	}

	expectedErr := errors.New("failed to save customer account")

	helper.getAccountCli().EXPECT().AutoBatchCreateUser(ctx, uint32(1), uint32(accountpb.USER_SOURCE_USER_SOURCE_TT), gomock.Any()).Return(accountResp, nil)
	helper.getGuildCli().EXPECT().GetGuild(ctx, guildId).Return(guildResp, nil)
	helper.getCustomerCli().EXPECT().SaveCustomerAccount(ctx, gomock.Any()).Return(nil, expectedErr)

	manager := &Manager{
		accountCli:  helper.getAccountCli(),
		customerCli: helper.getCustomerCli(),
		guildCli:    helper.getGuildCli(),
	}

	_, err := manager.CreateCustomerAccount(ctx, guildId, password, creator)

	assert.Error(t, err)
}

func TestCreateCustomerAccount_Success(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	// Initialize helperForTest with mocked clients
	s := newHelperForTest(t)

	ctx := context.Background()
	guildId := uint32(12345)
	password := "examplePassword"
	creator := "creator"

	// Mock responses
	accountResp := []*accountpb.UserResp{
		{
			Uid:      1000,
			Username: "user123",
			Nickname: "nickname123",
		},
	}
	guildResp := &guild_pb.GuildResp{
		Name:    "TestGuild",
		GuildId: guildId,
	}

	// Setup expectations
	s.getAccountCli().EXPECT().AutoBatchCreateUser(ctx, uint32(1), uint32(accountpb.USER_SOURCE_USER_SOURCE_TT), gomock.Any()).Return(accountResp, nil)
	s.getGuildCli().EXPECT().GetGuild(ctx, guildId).Return(guildResp, nil)
	s.getCustomerCli().EXPECT().SaveCustomerAccount(ctx, gomock.Any()).Return(&esport_customer.SaveCustomerAccountResponse{Success: true}, nil)

	// Call the method
	customerAccount, err := s.Manager.CreateCustomerAccount(ctx, guildId, password, creator)

	// Assertions
	assert.NoError(t, err)
	assert.NotNil(t, customerAccount)
	assert.Equal(t, accountResp[0].Username, customerAccount.CustomerTtid)
	assert.Equal(t, accountResp[0].Uid, customerAccount.CustomerUid)
	assert.Equal(t, accountResp[0].Nickname, customerAccount.CustomerNickname)
	assert.Equal(t, guildId, customerAccount.GuildId)
	assert.Equal(t, password, customerAccount.Password)
	assert.Equal(t, guildResp.Name, customerAccount.GuildName)
}
