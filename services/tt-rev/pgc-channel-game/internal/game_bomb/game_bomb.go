package game_bomb

import (
	"context"
	"crypto/rand"
	"errors"
	"fmt"
	"golang.52tt.com/services/tt-rev/common/goroutineex"
	"math/big"
	"strconv"
	"time"

	protogrpc "golang.52tt.com/pkg/protocol/grpc"
	comctx "golang.52tt.com/services/tt-rev/common/ctx"
	"golang.52tt.com/services/tt-rev/pgc-channel-game/internal/push"

	"golang.52tt.com/pkg/protocol"
	pbApp "golang.52tt.com/protocol/app"
	"golang.52tt.com/protocol/common/status"

	"golang.52tt.com/pkg/log"
	pbChannel "golang.52tt.com/protocol/app/channel"
	pbLogic "golang.52tt.com/protocol/app/pgc_channel_game_logic"
	pbSvr "golang.52tt.com/protocol/services/pgc-channel-game"

	"gitlab.ttyuyin.com/avengers/tyr/compatible/proto"
)

const (
	MicHost              = 1
	MinGameBombUserLimit = 2

	GameBombType            = 0
	GameBombTypeWithHost    = 1 //算主持人
	GameBombTypeWithoutHost = 2 //不算主持人
)

func (mgr *GameBombMgr) GenAutoThrow() {
	ctx, cancel := comctx.WithTimeout(5 * time.Second)
	defer cancel()
	list, err := mgr.cache.GetTimeoutAutoThrowChannel(ctx, time.Now().Unix())
	if err != nil {
		log.ErrorWithCtx(ctx, "GenAutoThrow failed to GetTimeoutAutoThrowChannel, err:%v", err)
		return
	}

	for _, info := range list {
		goroutineex.GoroutineWithTimeoutCtx(ctx, 30*time.Second, func(ctx context.Context) {
			mgr.ThrowBomb(ctx, info.ChannelId, 0)
		})
		log.Infof("GenAutoThrow channelId:%d", info.ChannelId)
	}
}

func (mgr *GameBombMgr) GenAutoBomb() {
	ctx, cancel := comctx.WithTimeout(5 * time.Second)
	defer cancel()
	list, err := mgr.cache.GetTimeoutBombChannel(ctx, time.Now().Unix())
	if err != nil {
		log.ErrorWithCtx(ctx, "GenAutoBomb failed to GetTimeoutBombChannel, err:%v", err)
		return
	}

	for _, info := range list {
		goroutineex.GoroutineWithTimeoutCtx(ctx, 30*time.Second, func(ctx context.Context) {
			mgr.HandleGameBombEnd(ctx, info.ChannelId, info.BombUid, 0)
		})
		log.Infof("GenAutoBomb channelId:%d", info.ChannelId)
	}
}

func (mgr *GameBombMgr) InitThrowBombGame(ctx context.Context, channelId, uid, gameId, gameType uint32) (err error) {
	_, found, err := mgr.cache.GetChannelCurrentGameInfo(ctx, channelId)
	if found || err != nil {
		log.ErrorWithCtx(ctx, "InitThrowBombGame failed, channelId:%d, found:%v， err:%v", channelId, found, err)
		return err
	}

	//获取麦上所有用户
	micResp, err := mgr.rpcClient.ChannelMicCli.GetMicrList(ctx, channelId, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "InitGameUsers Get Mic List failed, cid:%d, err:%v", channelId, err)
		return err
	}

	uidList := make([]uint32, 0)
	for _, mic := range micResp.GetAllMicList() {
		uidList = append(uidList, mic.GetMicUid())
	}

	profileMap, err := mgr.rpcClient.UserProfileCli.BatchGetUserProfileV2(ctx, uidList, true)
	if err != nil {
		log.ErrorWithCtx(ctx, "BatchGetUserProfileV2 failed, channelId:%d, err:%v", channelId, err)
		return err
	}

	//获取参与游戏的用户
	gameUidList := make([]uint32, 0, len(uidList))
	for idx, profile := range profileMap {
		if gameType == GameBombTypeWithoutHost && uid == idx {
			continue
		}

		gameUidList = append(gameUidList, profile.GetUid())
	}

	if len(gameUidList) < MinGameBombUserLimit {
		log.ErrorWithCtx(ctx, "InitThrowBombGame failed, channelId:%d, MinGameBombUserLimit", channelId)
		return protocol.NewExactServerError(nil, status.ErrPgcChannelGameNotEnoughPlayer)
	}

	//随机一个  甩雷
	bombUid := mgr.getRandomBombUid(gameUidList)
	bombTime := uint32(time.Now().Unix()) + mgr.bc.GetInitWaitSec()
	err = mgr.cache.InitThrowBombGame(ctx, channelId, gameId, gameType, bombUid, bombTime, gameUidList)
	if err != nil {
		log.ErrorWithCtx(ctx, "InitThrowBombGame failed, channelId:%d, err:%v", channelId, err)
		return err
	}
	log.InfoWithCtx(ctx, "InitThrowBombGame channelId:%d, bombUid:%d, bombTimeOut:%d, gameUidList:%v", channelId, bombUid, bombTime, gameUidList)

	//push 通知异步处理
	go func() {
		// ctx with timeout
		goCtx, cancel := context.WithTimeout(context.Background(), time.Second*5)
		defer cancel()

		for gameUid, profile := range profileMap {
			mgr.cache.AddUserProfile(goCtx, channelId, gameUid, profile)
		}

		err = mgr.cache.DelBombGamePunishInfo(goCtx, uidList)
		if err != nil {
			log.ErrorWithCtx(goCtx, "InitThrowBombGame DelBombGamePunishInfo, channelId:%d, err:%v", channelId, err)
		}

		err = mgr.cache.DelChannelAllPunishInfo(goCtx, channelId)
		if err != nil {
			log.ErrorWithCtx(goCtx, "InitThrowBombGame DelChannelAllPunishInfo, channelId:%d, err:%v", channelId, err)
		}

		//开场动画
		phaseOpt := &pbLogic.GamePhaseChangeNotifyOpt{
			ChannelId:    channelId,
			GameId:       gameId,
			TargetPhase:  uint32(pbLogic.SetGamePhaseReq_GAME_PHASE_START),
			GameBombType: gameType,
			GameMinTime:  uint32(time.Now().Unix()) + mgr.bc.GetBombMinSec(),
		}
		startOpt, _ := proto.Marshal(phaseOpt)
		_ = push.PushMsgToChannel(goCtx, mgr.rpcClient.PushCli, channelId, uint32(pbChannel.ChannelMsgType_PGC_GAME_PHASE_CHANGE), "", startOpt)

		time.Sleep(time.Second)
		//更新雷用户信息
		_ = mgr.updateBombUser(goCtx, channelId, bombUid, bombTime, 0, true)
	}()

	// 上报开始
	mgr.ReportBombEvent(ctx, &BombEventData{
		Uid:          bombUid,
		RoomId:       strconv.Itoa(int(channelId)),
		Action:       ReportActionBombStart,
		BehaviorType: ReportBehaviorTypeSysThrow,
	})
	return nil
}

func (mgr *GameBombMgr) getIsHost(ctx context.Context, channelId, uid uint32) (bool, error) {
	micResp, err := mgr.rpcClient.ChannelMicCli.GetMicrList(ctx, channelId, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "InitGameUsers Get Mic List failed, cid:%d, err:%v", channelId, err)
		return false, err
	}

	isCompere := false
	for _, mic := range micResp.AllMicList {
		if mic.GetMicId() == 1 && mic.GetMicUid() == uid {
			isCompere = true
			break
		}
	}
	return isCompere, err
}

func (mgr *GameBombMgr) getRandomBombUid(uidList []uint32) uint32 {
	uidSize := len(uidList)
	if uidSize == 0 {
		return 0
	}

	ret, _ := rand.Int(rand.Reader, big.NewInt(int64(uidSize)))
	return uidList[ret.Uint64()]
}

func (mgr *GameBombMgr) handleRescue(channelId, fromUid, uid, giftId, giftValue uint32, orderId string) (err error) {
	ctx, cancel := context.WithTimeout(context.Background(), time.Second*5)
	defer cancel()
	//非拯救礼物
	if giftId != mgr.bc.GetRescueGiftId() {
		return nil
	}

	//检查惩罚信息
	punishInfo, err := mgr.cache.GetBombGamePunishInfo(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "handleRescue failed to GetBombGamePunishInfo, channelId:%d, uid:%d, err:%v", channelId, uid, err)
		return err
	}

	if punishInfo == nil {
		return nil
	}

	// 上报解救
	go mgr.ReportBombEvent(ctx, &BombEventData{
		Uid:       uid,
		RoomId:    strconv.Itoa(int(channelId)),
		Action:    ReportActionSave,
		SendUid:   fromUid,
		GiftValue: giftValue,
		ItemId:    giftId,
		OrderId:   orderId,
	})

	err = mgr.cache.DelBombGamePunishInfo(ctx, []uint32{uid})
	if err != nil {
		log.ErrorWithCtx(ctx, "handleRescue DelBombGamePunishInfo failed channelId:%d, err:%v", channelId, err)
		return err
	}
	err = mgr.cache.DelChannelPunishInfo(ctx, channelId, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "handleRescue DelChannelPunishInfo failed channelId:%d, err:%v", channelId, err)
		return err
	}

	//解除公屏广播
	bombOpt := &pbLogic.GameRescueNotifyOPt{
		Toast:          "解除了甩雷惩罚",
		RescueGiftName: mgr.bc.GetRescueGiftName(),
		RescueGiftIcon: mgr.bc.GetRescueGiftIcon(),
	}
	bombMsg, _ := proto.Marshal(bombOpt)
	fromProfile, _ := mgr.rpcClient.UserProfileCli.GetUserProfileV2(ctx, fromUid, true)
	err = push.PushMsgToChannelWithUsers(ctx, mgr.rpcClient.PushCli, channelId,
		uint32(pbChannel.ChannelMsgType_PGC_GAME_BOMB_RESCUE), bombMsg, fromProfile, punishInfo.GetUserProfile())
	if err != nil {
		log.ErrorWithCtx(ctx, "handleRescue pushMsgToChannelWithUsers failed channelId:%d, err:%v", channelId, err)
	}

	//清除惩罚信息
	opt := &pbLogic.GameBombPunishNotifyOPt{Punish: &pbLogic.GameBombPunishInfo{
		UserProfile: punishInfo.GetUserProfile(),
		EndTime:     0,
	}}
	msg, _ := proto.Marshal(opt)
	err = push.PushMsgToChannel(ctx, mgr.rpcClient.PushCli, channelId, uint32(pbChannel.ChannelMsgType_PGC_GAME_BOMB_PUNISH_INFO), "", msg)
	if err != nil {
		log.ErrorWithCtx(ctx, "handleRescue pushMsgToChannelWithUsers failed channelId:%d, err:%v", channelId, err)
	}
	log.InfoWithCtx(ctx, "handleRescue channelId:%d, fromUid:%d, uid：%d", channelId, fromUid, uid)
	return nil
}

func (mgr *GameBombMgr) genNextBombUid(ctx context.Context, channelId, bombUid uint32) (uint32, error) {
	bombGift, found, err := mgr.cache.GetGameOnMicUserGift(ctx, channelId, bombUid)
	if !found || err != nil {
		log.Errorf("GetGameOnMicUserGift channelId:%d, err:%v", channelId, err)
		return 0, err
	}

	throwList, err := mgr.cache.GetGameBombThrowUidList(ctx, channelId, bombGift)
	if err != nil {
		log.Errorf("GetGameBombThrowUidList channelId:%d, err:%v", channelId, err)
		return 0, err
	}

	uidList := make([]uint32, 0, len(throwList))
	for uid := range throwList {
		if uid == bombUid {
			continue
		}

		uidList = append(uidList, uid)
	}

	if len(uidList) == 0 {
		return 0, errors.New("no uid valued")
	}
	return mgr.getRandomBombUid(uidList), nil
}

// ThrowBomb 甩给下一个玩家
func (mgr *GameBombMgr) ThrowBomb(ctx context.Context, channelId, throwUid uint32) error {
	log.InfoWithCtx(ctx, "ThrowBomb channelId:%d, throwUid:%d ", channelId, throwUid)
	bombUid, found, err := mgr.cache.GetBombUser(ctx, channelId)
	if !found || err != nil {
		log.ErrorWithCtx(ctx, "GetBombUser channelId:%d, err:%v", channelId, err)
		return err
	}

	nextBombUid := throwUid
	if throwUid == 0 {
		nextBombUid, err = mgr.genNextBombUid(ctx, channelId, bombUid)
		log.InfoWithCtx(ctx, "genNextBombUid cid:%d, nextBomb:%d", channelId, nextBombUid)
		if err != nil {
			return err
		}
	} else {
		//校验是否是雷所在用户发起的请求
		serviceInfo, err := getServiceInfo(ctx)
		if err != nil {
			log.ErrorWithCtx(ctx, "ThrowBomb getServiceInfo fail")
			return err
		}
		// 只有当前请求用户和雷用户不一致才需要使用神秘人信息再次校验
		if serviceInfo.UserID != bombUid {
			trueUid, err := mgr.rpcClient.YouKnowWhoCli.GetTrueUidByFake(ctx, bombUid)
			if err != nil {
				log.ErrorWithCtx(ctx, "GetTrueUidByFake uid:%d, err:%v", bombUid, err)
				return err
			}

			if serviceInfo.UserID != trueUid {
				log.ErrorWithCtx(ctx, "ThrowBomb serviceInfo.Uid:%d, trueUid:%d, bombUid:%d", serviceInfo.UserID, trueUid, bombUid)
				return protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
			}
		}
	}

	if nextBombUid == 0 {
		log.ErrorWithCtx(ctx, "ThrowBomb channelId:%d, nextBombUid is 0", channelId)
		return nil
	}

	bombTime := uint32(time.Now().Unix()) + mgr.bc.GetInitWaitSec()
	newBombGift, found, err := mgr.cache.GetGameOnMicUserGift(ctx, channelId, nextBombUid)
	log.InfoWithCtx(ctx, "ThrowBomb channelId：%d, throwUid:%d, nextBombTime:%d", channelId, throwUid, bombTime)
	if !found || err != nil {
		log.ErrorWithCtx(ctx, "GetGameOnMicUserGift channelId:%d, err:%v", channelId, err)
		return err
	}

	newThrowList, err := mgr.cache.GetGameBombThrowUidList(ctx, channelId, newBombGift)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetGameBombThrowUidList failed, cid:%d, uid:%d,err:%v", channelId, nextBombUid, err)
		return err
	}

	// 重置自动甩雷时间
	err = mgr.cache.DelBomb(ctx, channelId, bombUid)
	if err != nil {
		log.ErrorWithCtx(ctx, "DelBomb failed, cid:%d, uid:%d,err:%v", channelId, nextBombUid, err)
		return err
	}

	err = mgr.cache.SetBombTime(ctx, channelId, nextBombUid, bombTime)
	if err != nil {
		log.ErrorWithCtx(ctx, "SetBombTime failed, cid:%d, uid:%d,err:%v", channelId, nextBombUid, err)
		return err
	}

	var autoThrowTime uint32
	if len(newThrowList) > 0 {
		autoThrowTime = mgr.bc.GetAutoThrowSec() + uint32(time.Now().Unix())
		mgr.cache.AddAutoThrowTime(ctx, channelId, nextBombUid, autoThrowTime)
	}

	mgr.updateBombUser(ctx, channelId, nextBombUid, bombTime, autoThrowTime, len(newThrowList) == 0)

	//更新所有人的可甩状态
	onMicUserList, err := mgr.cache.GetGameBombOnMicUidList(ctx, channelId)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetGameBombOnMicUidList failed, cid:%d, uid:%d,err:%v", channelId, nextBombUid, err)
		return err
	}

	mgr.updateAllThrowUserInfo(ctx, channelId, newBombGift, onMicUserList)

	// 上报甩雷
	behaviorType := ReportBehaviorTypeUserThrow
	if throwUid == 0 {
		behaviorType = ReportBehaviorTypeSysThrow
	}
	go mgr.ReportBombEvent(context.Background(), &BombEventData{
		Uid:          nextBombUid,
		RoomId:       strconv.Itoa(int(channelId)),
		BehaviorType: behaviorType,
		Action:       ReportActionThrow,
	})

	return nil
}

func (mgr *GameBombMgr) getGameBombEndType(ctx context.Context, channelId, targetMicId uint32) (uint32, string, error) {
	gameInfo, _, _ := mgr.cache.GetChannelCurrentGameInfo(ctx, channelId)
	endType := uint32(pbLogic.GameBombResultOpt_Common)
	var toast string

	//过早结束
	gameTime := uint32(time.Now().Unix()) - gameInfo.StartTime
	log.Infof("getGameBombEndType channelId:%d, gameInfo:%v,gameTime:%d", channelId, gameInfo, gameTime)
	if gameTime <= mgr.bc.GetBombMinSec() {
		endType = uint32(pbLogic.GameBombResultOpt_EndWithNoBomb)
		toast = "参与嘉宾未准备就续，游戏未能正常开始"
		return endType, toast, nil
	}

	//麦上无人可甩
	gameList, err := mgr.cache.GetGameBombOnMicUidList(ctx, channelId)
	log.Debugf("GetGameBombOnMicUidList channelId:%d,gameList:%v", channelId, gameList)
	if err != nil || len(gameList) <= 1 {
		endType = uint32(pbLogic.GameBombResultOpt_EndWithNoBomb)
		toast = "由于麦上人数不够, 无法正常结束游戏, 雷未被引爆"
		return endType, toast, nil
	}

	//自己下麦或者退房
	if targetMicId > 0 {
		endType = uint32(pbLogic.GameBombResultOpt_Abnormal)
		toast = fmt.Sprintf("玩家在雷甩出前下麦，地雷即刻引爆！该玩家需佩戴%d分钟失败特效，可赠送【%s】为其解除哦～", mgr.bc.GetPunishSec()/60, mgr.bc.GetRescueGiftName())
	}
	return endType, toast, nil
}

// 处理游戏结果
func (mgr *GameBombMgr) HandleGameBombEnd(ctx context.Context, channelId, uid, targetMicId uint32) (err error) {
	endType, toast, err := mgr.getGameBombEndType(ctx, channelId, targetMicId)
	if err != nil {
		log.Errorf("getGameBombEndType uid:%d, err:%v", uid, err)
		return err
	}

	log.Infof("handleGameBombEnd channelId:%d, uid;%d, boomUserOffMic:%v,endType：%d",
		channelId, uid, targetMicId, endType)

	resOpt := &pbLogic.GameBombResultOpt{
		EndType:   endType,
		Toast:     toast,
		BombMicId: targetMicId,
	}

	ctx, cancel := context.WithTimeout(context.Background(), time.Second*5)
	defer cancel()

	trueUid, _ := mgr.rpcClient.YouKnowWhoCli.GetTrueUidByFake(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetTrueUidByFake uid:%d, err:%v", uid, err)
		return err
	}

	profile, err := mgr.rpcClient.UserProfileCli.GetUserProfileV2(ctx, trueUid, true)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetUserProfileV2 uid:%d, err:%v", uid, err)
		return err
	}

	//结束
	gameInfo, _, err := mgr.cache.GetChannelCurrentGameInfo(ctx, channelId)
	phaseOpt := &pbLogic.GamePhaseChangeNotifyOpt{
		ChannelId:    channelId,
		GameId:       gameInfo.GameId,
		TargetPhase:  uint32(pbLogic.SetGamePhaseReq_GAME_PHASE_FIN),
		GameBombType: gameInfo.GameType,
	}
	startOpt, _ := proto.Marshal(phaseOpt)
	push.PushMsgToChannel(ctx, mgr.rpcClient.PushCli, channelId, uint32(pbChannel.ChannelMsgType_PGC_GAME_PHASE_CHANGE), "", startOpt)

	if endType != uint32(pbLogic.GameBombResultOpt_EndWithNoBomb) {
		punishEndTime := uint32(time.Now().Unix()) + mgr.bc.GetPunishSec()
		resOpt.Punish = &pbLogic.GameBombPunishInfo{
			UserProfile:    profile,
			EndTime:        punishEndTime,
			RescueGiftId:   mgr.bc.GetRescueGiftId(),
			RescueGiftName: mgr.bc.GetRescueGiftName(),
			IconUrl:        mgr.bc.GetBombPunishUrl(),
			IconMd5:        mgr.bc.GetBombPunishMd5(),
			ServerTime:     uint32(time.Now().Unix()),
		}

		if targetMicId == 0 {
			micList, serr := mgr.rpcClient.ChannelMicCli.GetMicrList(ctx, channelId, uid)
			if serr != nil {
				log.ErrorWithCtx(ctx, "GetMicrList cid:%d, err:%v", channelId, err)
			}

			for _, mic := range micList.GetAllMicList() {
				if mic.GetMicUid() == uid || mic.GetMicUid() == trueUid {
					resOpt.BombMicId = mic.GetMicId()
					break
				}
			}
		}

		err = mgr.cache.SetBombGamePunishInfo(ctx, channelId, targetMicId, resOpt.Punish)
		if err != nil {
			log.ErrorWithCtx(ctx, "SetBombGamePunishInfo uid:%d, err:%v", uid, err)
			return err
		}
	}

	log.InfoWithCtx(ctx, "handleGameBombEnd cid:%d,trueUid:%d, resOpt:%v", channelId, trueUid, resOpt)

	// 上报甩雷结束事件
	mgr.ReportBombEvent(ctx, &BombEventData{
		Uid:    uid,
		RoomId: strconv.Itoa(int(channelId)),
		Action: ReportActionEnd,
	})

	resMsg, _ := proto.Marshal(resOpt)
	mgr.cache.CleanGame(ctx, channelId)
	mgr.cache.DelBomb(ctx, channelId, uid)
	return push.PushMsgToChannel(ctx, mgr.rpcClient.PushCli, channelId, uint32(pbChannel.ChannelMsgType_PGC_GAME_BOMB_RESULT), "", resMsg)
}

func (mgr *GameBombMgr) handlePunishInfo(ctx context.Context, channelId, realUid, oldFakeUid uint32) error {
	//如果有惩罚信息，重置惩罚信息
	punishInfo, _ := mgr.cache.GetBombGamePunishInfo(ctx, oldFakeUid)
	mgr.cache.DelBombGamePunishInfo(ctx, []uint32{oldFakeUid})
	if punishInfo != nil {
		profile, _ := mgr.rpcClient.UserProfileCli.GetUserProfileV2(ctx, realUid, true)
		newPunish := &pbLogic.GameBombPunishInfo{
			UserProfile:    profile,
			EndTime:        punishInfo.GetEndTime(),
			RescueGiftId:   punishInfo.GetRescueGiftId(),
			RescueGiftName: punishInfo.GetRescueGiftName(),
			IconUrl:        punishInfo.GetIconUrl(),
			IconMd5:        punishInfo.GetIconMd5(),
			ServerTime:     uint32(time.Now().Unix()),
		}
		mgr.cache.SetBombGamePunishInfo(ctx, channelId, 1, newPunish)

		if channelId > 0 {
			err := mgr.cache.DelChannelPunishInfo(ctx, channelId, oldFakeUid)
			if err != nil {
				log.ErrorWithCtx(ctx, "handlePunishInfo failed to DelChannelPunishInfo, channelId:%d, err:%v", channelId, err)
				return err
			}

			err = mgr.cache.SetChannelPunishInfo(ctx, channelId, newPunish)
			if err != nil {
				log.ErrorWithCtx(ctx, "handlePunishInfo failed to SetChannelPunishInfo, channelId:%d, err:%v", channelId, err)
				return err
			}

			opt := &pbLogic.GameBombPunishNotifyOPt{Punish: newPunish}
			msg, _ := proto.Marshal(opt)
			err = push.PushMsgToChannel(ctx, mgr.rpcClient.PushCli, channelId, uint32(pbChannel.ChannelMsgType_PGC_GAME_BOMB_PUNISH_INFO), "", msg)
			if err != nil {
				log.ErrorWithCtx(ctx, "handlePunishInfo failed to PushMsgToChannel, channelId:%d, err:%v", channelId, err)
				return err
			}
		}
	}
	return nil
}

func (mgr *GameBombMgr) GetChannelGameInfo(ctx context.Context, channelId uint32) (out *pbSvr.GetChannelGameInfoResp, err error) {
	out = &pbSvr.GetChannelGameInfoResp{
		ChannelId:     channelId,
		CurrentGameId: 0,
		Phase:         uint32(pbLogic.SetGamePhaseReq_GAME_PHASE_NOT_START),
		Info:          &pbSvr.GameThrowBombInfo{},
	}

	gameInfo, found, err := mgr.cache.GetChannelCurrentGameInfo(ctx, channelId)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetChannelGameInfo failed to GetChannelCurrentGameInfo, channelId:%d, err:%v", channelId, err)
		return out, err
	}

	//不存在 查找惩罚信息
	if !found {
		punishMap, _ := mgr.cache.GetChannelPunishInfos(ctx, channelId)
		for _, v := range punishMap {
			out.Info.Punish = append(out.Info.Punish, mgr.transPunishInfo(v))
		}
		return out, nil
	}

	out.Phase = uint32(pbLogic.SetGamePhaseReq_GAME_PHASE_START)
	out.CurrentGameId = gameInfo.GameId

	log.DebugWithCtx(ctx, "GetChannelCurrentGameInfo channelId:%d, gameInfo:%+v", channelId, gameInfo)
	if gameInfo.GameId == uint32(pbLogic.PgcChannelGameId_GAME_THROW_BOMB) {
		// bombUser
		bombUid, found, err := mgr.cache.GetBombUser(ctx, channelId)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetChannelGameInfo failed to GetBombUser, channelId:%d, err:%v", channelId, err)
			return out, err
		}

		if !found {
			return out, nil
		}

		//存在 赋值游戏信息
		endTime, err := mgr.cache.GetBombTime(ctx, channelId, bombUid)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetChannelGameInfo failed to GetBombTime, channelId:%d, bombUid:%d,  err:%v", channelId, bombUid, err)
			return out, err
		}

		bombUser := &pbSvr.GameBombUserInfo{
			Uid:        bombUid,
			EndTime:    endTime,
			NeedRescue: false,
			ServerTime: uint32(time.Now().Unix()),
		}

		gameUserMap, err := mgr.cache.GetGameBombOnMicUidList(ctx, channelId)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetChannelGameInfo failed to GetGameBombOnMicUidList, channelId:%d,  err:%v", channelId, err)
			return out, err
		}

		bombGift := gameUserMap[bombUid]
		needRescue := mgr.checkRescue(bombGift, gameUserMap)
		for gameUid, gift := range gameUserMap {
			out.Info.ThrowList = append(out.Info.ThrowList, &pbSvr.GameThrowInfo{
				Uid:         gameUid,
				GiftVal:     gift,
				ThrowStatus: gift < bombGift,
			})
		}

		bombUser.NeedRescue = needRescue
		out.Info.Bomb = bombUser
		out.Info.GameBombType = gameInfo.GameType
	}

	return out, nil
}

func (mgr *GameBombMgr) BatchGetChannelThrowBombPhase(ctx context.Context, channelIds []uint32) (phaseMap map[uint32]uint32, err error) {
	phaseMap = make(map[uint32]uint32)
	gameInfoMap, err := mgr.cache.BatchGetChannelCurrentGameInfo(ctx, channelIds)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetChannelGameInfo failed to GetChannelCurrentGameInfo, channelIds:%+v, err:%v", channelIds, err)
		return phaseMap, err
	}
	for channelId, gameInfo := range gameInfoMap {
		if gameInfo.GameId == uint32(pbLogic.PgcChannelGameId_GAME_THROW_BOMB) {
			phaseMap[channelId] = uint32(pbLogic.SetGamePhaseReq_GAME_PHASE_START)
		}
	}
	return phaseMap, nil
}

func (mgr *GameBombMgr) transUserProfile(appProfile *pbApp.UserProfile) *pbSvr.UserProfile {
	out := &pbSvr.UserProfile{
		Uid:          appProfile.GetUid(),
		Account:      appProfile.GetAccount(),
		Nickname:     appProfile.GetNickname(),
		AccountAlias: appProfile.GetAccountAlias(),
		Sex:          appProfile.GetSex(),
	}

	if len(appProfile.GetPrivilege().GetAccount()) > 0 {
		out.Privilege = &pbSvr.UserPrivilege{
			Account:  appProfile.GetPrivilege().GetAccount(),
			Nickname: appProfile.GetPrivilege().GetNickname(),
			Type:     appProfile.GetPrivilege().GetType(),
			Options:  appProfile.GetPrivilege().GetOptions(),
		}
	}
	return out
}

func (mgr *GameBombMgr) transPunishInfo(info *pbLogic.GameBombPunishInfo) *pbSvr.GameBombPunishInfo {
	return &pbSvr.GameBombPunishInfo{
		UserProfile:    mgr.transUserProfile(info.UserProfile),
		EndTime:        info.GetEndTime(),
		RescueGiftId:   info.GetRescueGiftId(),
		RescueGiftName: info.GetRescueGiftName(),
		IconUrl:        info.GetIconUrl(),
		IconMd5:        info.GetIconMd5(),
		ServerTime:     uint32(time.Now().Unix()),
	}
}

func (mgr *GameBombMgr) ClearUserBombInfo(ctx context.Context, channelId, uid uint32) error {
	log.InfoWithCtx(ctx, "you know who change ClearUserBombInfo channelId:%d, uid:%d", channelId, uid)
	punishInfo, _ := mgr.cache.GetBombGamePunishInfo(ctx, uid)
	if punishInfo != nil {
		err := mgr.cache.DelBombGamePunishInfo(ctx, []uint32{uid})
		if err != nil {
			log.ErrorWithCtx(ctx, "ClearUserBombInfo failed to DelBombGamePunishInfo, uid:%d, err:%v", uid, err)
			return err
		}
		log.InfoWithCtx(ctx, "ClearUserBombInfo DelBombGamePunishInfo channelId:%d, uid:%d", channelId, uid)
	}

	gift, _, _ := mgr.cache.GetOffMicUserGift(ctx, channelId, uid)
	if gift > 0 {
		err := mgr.cache.DelOffMicUserGift(ctx, channelId, uid)
		if err != nil {
			log.ErrorWithCtx(ctx, "ClearUserBombInfo failed to DelOffMicUserGift, channelId:%d, uid:%d, err:%v", channelId, uid, err)
			return err
		}
		log.InfoWithCtx(ctx, "ClearUserBombInfo DelOffMicUserGift channelId:%d, uid:%d", channelId, uid)
	}
	return nil
}

func (mgr *GameBombMgr) updateBombUser(ctx context.Context, channelId, bombUid, bombTime, autoThrowTime uint32, needRescue bool) error {
	bombOpt := &pbLogic.GameBombUserNotifyOPt{
		Bomb: &pbLogic.GameBombUserInfo{
			Uid:           bombUid,
			EndTime:       bombTime,
			NeedRescue:    needRescue,
			AutoThrowTime: autoThrowTime,
			ServerTime:    uint32(time.Now().Unix()),
		},
	}
	log.DebugWithCtx(ctx, "updateBombUser channelID:%d,bomb:%+v", channelId, bombOpt)
	bombMsg, _ := proto.Marshal(bombOpt)
	return push.PushMsgToChannel(ctx, mgr.rpcClient.PushCli, channelId, uint32(pbChannel.ChannelMsgType_PGC_GAME_BOMB_USER_INFO), "", bombMsg)
}

func (mgr *GameBombMgr) checkRescue(boomGift uint32, onMicUserGiftMap map[uint32]uint32) bool {
	needRescue := true
	if len(onMicUserGiftMap) == 1 {
		return false
	}

	for _, userGift := range onMicUserGiftMap {
		if boomGift > userGift {
			needRescue = false
			break
		}
	}
	return needRescue
}

func getServiceInfo(ctx context.Context) (*protogrpc.ServiceInfo, error) {
	serviceInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
	if !ok {
		return nil, fmt.Errorf("StartGame getServiceInfo fail")
	}
	return serviceInfo, nil
}

func (mgr *GameBombMgr) updateSingleThrowUserInfo(ctx context.Context, channelId, uid, gift, bombGift uint32) {
	throwOpt := &pbLogic.GameThrowUserNotifyOPt{}
	throwOpt.ThrowList = append(throwOpt.ThrowList, &pbLogic.GameThrowInfo{
		Uid:         uid,
		GiftVal:     gift,
		ThrowStatus: gift < bombGift,
	})
	throwMsg, _ := proto.Marshal(throwOpt)
	_ = push.PushMsgToChannel(ctx, mgr.rpcClient.PushCli, channelId, uint32(pbChannel.ChannelMsgType_PGC_GAME_THROW_USER_INFO), "", throwMsg)
}

func (mgr *GameBombMgr) updateAllThrowUserInfo(ctx context.Context, channelId, bombGift uint32, onMicGameUser map[uint32]uint32) {
	throwOpt := &pbLogic.GameThrowUserNotifyOPt{}
	for micUid, gift := range onMicGameUser {
		throwOpt.ThrowList = append(throwOpt.ThrowList, &pbLogic.GameThrowInfo{
			Uid:         micUid,
			GiftVal:     gift,
			ThrowStatus: gift < bombGift,
		})
	}
	throwMsg, _ := proto.Marshal(throwOpt)
	_ = push.PushMsgToChannel(ctx, mgr.rpcClient.PushCli, channelId, uint32(pbChannel.ChannelMsgType_PGC_GAME_THROW_USER_INFO), "", throwMsg)
}
