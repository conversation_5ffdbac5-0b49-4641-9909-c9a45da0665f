package store

import (
	context "context"
)

type IStore interface {
	BatchAddChannelGameplayPerm(ctx context.Context, perms []*GameplayPerm) error
	BatchBanChannelPKPerm(ctx context.Context, channelIdList []uint32, effectTimeStart, effectTimeEnd uint32) error
	BatchDelChannelPKPerm(ctx context.Context, channelIdList []uint32, permType uint32) error
	BatchDeleteChannelGameplayPerm(ctx context.Context, perms []*GameplayPerm) error
	BatchGetChannelGameplayPerm(ctx context.Context, searchOptions []*GameplayPerm) (map[string]bool, error)
	BatchGetChannelPKPerm(ctx context.Context, channelIdList []uint32) ([]*ChannelPKPerm, error)
	BatchGrantGetChannelPKPerm(ctx context.Context, channelIdList []uint32, effectTimeStart, effectTimeEnd uint32) error
	GetAllDistinctChannelId(ctx context.Context) ([]uint32, error)
	GetChannelAllGameplay(ctx context.Context, channelId, tagId uint32) ([]uint32, error)
	RemoveExpireChannelGameplayPerm(ctx context.Context, channelId, nowTagId uint32) error
	BatchAddPerfectMatchPerm(ctx context.Context, channelIdList []uint32) error
	BatchDeletePerfectMatchPerm(ctx context.Context, channelIdList []uint32) error
	BatchGetPerfectMatchPerm(ctx context.Context, channelIdList []uint32) (map[uint32]bool, error)
	AddRecommendedChannel(ctx context.Context, channelItemList []*RecommendChannelItem) error
	RemoveRecommendedChannel(ctx context.Context, cidList []uint32) error
	GetRecommendedChannelRecordByChannelIdList(ctx context.Context, channelIdList []uint32) ([]*RecommendChannelItem, error)
	GetRecommendedChannelRecordByChannelId(ctx context.Context, channelId uint32) (*RecommendChannelItem, error)
	GetAllRecommendedChannelRecords(ctx context.Context) ([]*RecommendChannelItem, error)
	GetRecommendedChannelRecordsByPage(ctx context.Context, pageNum, pageSize uint32) ([]*RecommendChannelItem, error)
	GetRecommendedChannelRecordsCount(ctx context.Context) (uint32, error)
}
