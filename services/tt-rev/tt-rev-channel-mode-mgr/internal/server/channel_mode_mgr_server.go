package server

import (
    "context"
    "encoding/json"
    "golang.52tt.com/pkg/config"
    "golang.52tt.com/pkg/log"
    "golang.52tt.com/pkg/protocol"
    "golang.52tt.com/pkg/protocol/grpc"
    "golang.52tt.com/protocol/common/status"
    "golang.52tt.com/protocol/services/demo/echo"
    reconcile_v2 "golang.52tt.com/protocol/services/reconcile-v2"
    pb "golang.52tt.com/protocol/services/tt-rev-channel-mode-mgr"
    UnifiedPayCallback "golang.52tt.com/protocol/services/unified_pay/cb"
    "golang.52tt.com/services/tt-rev/tt-rev-channel-mode-mgr/internal/conf"
    "golang.52tt.com/services/tt-rev/tt-rev-channel-mode-mgr/internal/event"
    "golang.52tt.com/services/tt-rev/tt-rev-channel-mode-mgr/internal/mgr"
    "golang.52tt.com/services/tt-rev/tt-rev-channel-mode-mgr/internal/rpc"
    "golang.52tt.com/services/tt-rev/tt-rev-channel-mode-mgr/internal/store"
    "time"
)

type Server struct {
    mgr         mgr.IMgr
    ev          *event.KafkaEvent
    rpc         *rpc.RpcClients
    CallbackSvr UnifiedPayCallback.PayCallbackServer
}

func (c *Server) BatchGetChannelIdByLayoutType(ctx context.Context, req *pb.BatchGetChannelIdByLayoutTypeReq) (*pb.BatchGetChannelIdByLayoutTypeResp, error) {
    resp := &pb.BatchGetChannelIdByLayoutTypeResp{}
    cids, err := c.mgr.BatchGetChannelIdByLayout(ctx, req.GetLayoutType())
    if err != nil {
        log.ErrorWithCtx(ctx, "BatchGetChannelModeByLayoutType fail to BatchGetChannelIdByLayout. layoutType:%v, err:%v", req.GetLayoutType(), err)
        return resp, err
    }

    resp.ChannelId = cids
    return resp, nil
}

type WerwolfConsumeType struct {
    WerwolfType uint32 `json:"werwolf_type"` //1-T豆  : 2-礼物积分
}

func (c *Server) GetConsumeTotalCount(ctx context.Context, req *reconcile_v2.TimeRangeReq) (*reconcile_v2.CountResp, error) {
    out := &reconcile_v2.CountResp{}
    var consumeType WerwolfConsumeType
    err := json.Unmarshal([]byte(req.GetParams()), &consumeType)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetConsumeTotalCount req:%v, err:%v", req, err)
        return out, err
    }

    log.DebugfWithCtx(ctx, "GetConsumeTotalCount req:%v", req)
    count, price, err := c.mgr.GetConsumeTotalCount(time.Unix(req.GetBeginTime(), 0), time.Unix(req.GetEndTime(), 0), consumeType.WerwolfType)
    if err != nil {
        return out, err
    }

    out.Count = count
    out.Value = price
    if consumeType.WerwolfType == store.WerwolfTypeScoreCommitTime {
        out.Value = out.Value / 2
    }

    return out, nil
}

func (c *Server) GetConsumeOrderIds(ctx context.Context, req *reconcile_v2.TimeRangeReq) (*reconcile_v2.OrderIdsResp, error) {
    out := &reconcile_v2.OrderIdsResp{}
    var consumeType WerwolfConsumeType
    err := json.Unmarshal([]byte(req.GetParams()), &consumeType)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetConsumeOrderIds req:%v, err:%v", req, err)
        return out, err
    }

    list, err := c.mgr.GetConsumeOrderIds(time.Unix(req.GetBeginTime(), 0), time.Unix(req.GetEndTime(), 0), consumeType.WerwolfType)
    if err != nil {
        return out, err
    }

    out.OrderIds = append(out.OrderIds, list...)
    log.DebugfWithCtx(ctx, "GetConsumeOrderIds req:%v, out:%+v", req, out)
    return out, nil
}

func (c *Server) GetOrderLogByOrderIds(ctx context.Context, req *pb.GetOrderLogByOrderIdsReq) (*pb.GetOrderLogByOrderIdsResp, error) {
    out := &pb.GetOrderLogByOrderIdsResp{}
    orders, err := c.mgr.GetOrderLogByOrderIds(req.GetOrderIdList(), time.Now())
    if len(orders) == 0 || err != nil {
        lastOrder, _ := c.mgr.GetOrderLogByOrderIds(req.GetOrderIdList(), time.Now().Add(-20*24*time.Hour))
        orders = lastOrder
    }

    for _, v := range orders {
        order := &pb.WerwolfOrderLog{
            FromUid:     v.Uid,
            TargetUid:   v.TargetUid,
            ChangeScore: v.Price / 2,
            OrderId:     v.OrderId,
            CreateTime:  uint32(v.UpdateTime.Unix()),
            DealToken:   v.Token,
            ChannelId:   v.ChannelId,
            Price:       v.Price,
        }
        out.OrderLogList = append(out.OrderLogList, order)
    }

    log.DebugfWithCtx(ctx, "GetOrderLogByOrderIds req:%v,out:%v", req, out)
    return out, nil
}

func (c *Server) BuyWerwolfItem(ctx context.Context, req *pb.BuyWerwolfItemReq) (*pb.BuyWerwolfItemResp, error) {
    return c.mgr.BuyWerwolfItem(ctx, req)
}

func (c *Server) GetChannelMode(ctx context.Context, req *pb.GetChannelModeReq) (*pb.GetChannelModeResp, error) {
    return c.mgr.GetChannelMode(ctx, req.GetChannelId())
}

func (c *Server) SetChannelMode(ctx context.Context, req *pb.SetChannelModeReq) (*pb.SetChannelModeResp, error) {
    out := &pb.SetChannelModeResp{}
    serviceInfo, ok := grpc.ServiceInfoFromContext(ctx)
    if !ok {
        return out, protocol.NewExactServerError(nil, status.ErrSystemError, "获取用户信息失败")
    }

    uid := serviceInfo.UserID
    err := c.mgr.SetChannelMode(ctx, uid, req.GetChannelId(), req.GetSchemeId())
    return out, err
}

func (c *Server) GetChannelOperator(ctx context.Context, req *pb.GetChannelOperatorReq) (*pb.GetChannelOperatorResp, error) {
    out := &pb.GetChannelOperatorResp{}
    out.ChannelId = req.ChannelId

    micList, err := c.rpc.ChannelMicCli.GetMicrList(ctx, req.GetChannelId(), 0)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetChannelOperator fail to GetMicrList. channelId:%v, err:%v", req.GetChannelId(), err)
        return out, err
    }

    if len(micList.GetAllMicList()) == 0 {
        return out, nil
    }

    out.Operator = micList.GetAllMicList()[0].GetMicUid()
    log.DebugfWithCtx(ctx, "GetChannelOperator cid:%d, out:%v", req.GetChannelId(), out)
    return out, nil
}

func (c *Server) GenFinancialFile(ctx context.Context, req *reconcile_v2.GenFinancialFileReq) (*reconcile_v2.GenFinancialFileResp, error) {
    out := &reconcile_v2.GenFinancialFileResp{}
    err := c.mgr.GenFinancialFile(ctx, time.Unix(req.GetBeginTime(), 0), time.Unix(req.GetEndTime(), 0), uint32(req.GetTbeanPrice()))
    return out, err
}

func (c *Server) Echo(ctx context.Context, req *echo.StringMessage) (*echo.StringMessage, error) {
    return req, nil
}

func (c *Server) ShutDown() {
    c.ev.Shutdown()
}

type StartConfig struct {
    // [optional] from startup arguments

    // from config file
    MysqlConfig             *config.MysqlConfig `json:"mysql"`
    GameEventKafkaConfig    *config.KafkaConfig `json:"game_event_kafka"`
    TbeanConsumeKafkaConfig *config.KafkaConfig `json:"tbean_consume_kafka"`
}

func NewServer(ctx context.Context, cfg *StartConfig) (*Server, error) {

    bc := conf.NewBusinessConfManager()

    st, err := store.NewStore(cfg.MysqlConfig)
    if err != nil {
        return nil, err
    }

    rpcClients := rpc.NewRpcClients()
    mgr := mgr.NewMgr(cfg.TbeanConsumeKafkaConfig, st, bc, rpcClients)

    kfkSub, err := event.NewKafkaEvent(cfg.TbeanConsumeKafkaConfig, mgr)
    if err != nil {
        return nil, err
    }
    callbackSvr := NewUnifiedPayNotifyServer(mgr)

    c := &Server{
        mgr:         mgr,
        ev:          kfkSub,
        CallbackSvr: callbackSvr,
        rpc:         rpcClients,
    }
    return c, nil
}


