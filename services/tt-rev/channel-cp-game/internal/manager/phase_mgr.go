package manager

import (
	"context"
	"fmt"
	"golang.52tt.com/pkg/bylink"
	"time"

	"golang.52tt.com/services/tt-rev/channel-cp-game/internal/cache"
	"golang.52tt.com/services/tt-rev/channel-cp-game/internal/conf"

	"gitlab.ttyuyin.com/avengers/tyr/compatible/proto"
	"github.com/jinzhu/gorm"
	"golang.52tt.com/clients/account"
	seqgenClient "golang.52tt.com/clients/seqgen/v2"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	pbApp "golang.52tt.com/protocol/app"
	channelPb "golang.52tt.com/protocol/app/channel"
	channel_cp_game_logic "golang.52tt.com/protocol/app/channel-cp-game-logic"
	pushPb "golang.52tt.com/protocol/app/push"
	"golang.52tt.com/protocol/common/status"
	pb "golang.52tt.com/protocol/services/channel-cp-game"
	channelmicPB "golang.52tt.com/protocol/services/channelmicsvr"
	friendshipPB "golang.52tt.com/protocol/services/ugc/friendship"
	yswfukwdelaypb "golang.52tt.com/protocol/services/yswfukwdelay"
	"golang.52tt.com/services/notify"
)

const BreakingNewsTbanBig = 1000000
const BreakingNewsTbanSuper = 2000000
const BreakingNewsTbanTop = 3000000
const TimeFormat = "2006-01-02 15:04:05"

func (m *ChannelCpGameMgr) createGameId(ctx context.Context, channelId uint32) (uint32, error) {
	gameId := uint32(0)
	err := m.Store.Transaction(ctx, func(tx *gorm.DB) error {
		err := m.Store.AddChannelCpGameId(tx, channelId)
		if err != nil {
			log.ErrorWithCtx(ctx, "createGameId fail to AddChannelCpGameId. channelId:%v, err:%v", channelId, err)
			return err
		}

		gameId, err = m.Store.GetChannelCpGameId(ctx, tx, channelId)
		if err != nil {
			log.ErrorWithCtx(ctx, "createGameId fail to GetChannelCpGameId. channelId:%v, err:%v", channelId, err)
			return err
		}

		return nil
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "createGameId fail to Transaction. channelId:%v, err:%v", channelId, err)
		return gameId, err
	}

	err = m.Cache.SetChannelCpGameId(channelId, gameId)
	if err != nil {
		log.ErrorWithCtx(ctx, "createGameId fail to SetChannelCpGameId. channelId:%v, gameId:%v, err:%v", channelId, gameId, err)
		return gameId, err
	}

	log.Infof("createGameId channelId:%v, gameId:%v", channelId, gameId)
	return gameId, nil
}

func (m *ChannelCpGameMgr) SetChannelCpGamePhase(_ context.Context, opUid, channelId, targetPhase uint32, isAuto bool) error {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	log.Infof("SetChannelCpGamePhase uid:%d, channelID:%d,targetPhase:%d,isAuto:%v", opUid, channelId, targetPhase, isAuto)
	currPhase, _, err := m.Cache.GetChannelCpGamePhase(ctx, channelId)
	if err != nil {
		log.ErrorWithCtx(ctx, "SetChannelCpGamePhase fail to GetChannelCpGamePhase. opUid:%v, channelId:%v, targetPhase:%v, err:%v",
			opUid, channelId, targetPhase, err)
		return err
	}

	err = m.checkIfCanChangePhase(ctx, channelId, currPhase, targetPhase)
	if err != nil {
		log.ErrorWithCtx(ctx, "SetChannelCpGamePhase fail to checkIfCanChangePhase. opUid:%v, channelId:%v, targetPhase:%v, err:%v",
			opUid, channelId, targetPhase, err)
		return err
	}

	toast, needResult, err := m.SetPhasePreHandle(ctx, opUid, channelId, currPhase, targetPhase)
	if err != nil {
		log.ErrorWithCtx(ctx, "SetChannelCpGamePhase fail to checkIfCanChangePhase. opUid:%v, channelId:%v, targetPhase:%v, err:%v",
			opUid, channelId, targetPhase, err)
		return err
	}

	err = m.Cache.SetChannelCpGamePhase(channelId, targetPhase, 24*time.Hour)
	if err != nil {
		log.ErrorWithCtx(ctx, "SetChannelCpGamePhase fail to SetChannelCpGamePhase. opUid:%v, channelId:%v, targetPhase:%v, err:%v",
			opUid, channelId, targetPhase, err)
		return err
	}

	if isAuto {
		// 系统自动切换的不需要toast提示
		toast = ""
	}

	go func() {
		pushCtx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
		defer cancel()

		if needResult {
			cpGameInfo, err := m.GetCurrCpGameInfo(pushCtx, channelId)
			if err != nil {
				log.ErrorWithCtx(pushCtx, "SetChannelCpGamePhase fail to GetCurrCpGameInfo. channelId:%v, err:%v", channelId, err)
				return
			}

			// 需先结算再进行 ChannelCpGameInfoChangeNotify
			err = m.ChannelCpGamePkResult(pushCtx, cpGameInfo)
			if err != nil {
				log.ErrorWithCtx(pushCtx, "SetChannelCpGamePhase fail to ChannelCpGamePkResult. opUid:%v, channelId:%v, targetPhase:%v, err:%v",
					opUid, channelId, targetPhase, err)
				return
			}
		}

		_, err = m.ChannelCpGameInfoChangeNotify(pushCtx, channelId, toast)
		if err != nil {
			log.ErrorWithCtx(pushCtx, "SetChannelCpGamePhase fail to ChannelCpGameInfoChangeNotify. opUid:%v, channelId:%v, targetPhase:%v, err:%v",
				opUid, channelId, targetPhase, err)
			return
		}
	}()

	m.dataReportCpGamePhaseLog(ctx, opUid, channelId, targetPhase)
	log.Infof("SetChannelCpGamePhase opUid:%v, channelId:%v, currPhase:%v, targetPhase:%v", opUid, channelId, currPhase, targetPhase)
	return nil
}

func (m *ChannelCpGameMgr) SetPhasePreHandle(ctx context.Context, opUid, channelId, currPhase, targetPhase uint32) (toast string, needResult bool, err error) {
	switch pb.CpGamePhaseType(targetPhase) {
	case pb.CpGamePhaseType_CP_GAME_PHASE_CLOSE: // 结束本轮
		toast = "主持人提前结束本轮cp战"
		if currPhase == uint32(pb.CpGamePhaseType_CP_GAME_PHASE_CHOSE_RARE) {
			toast = "主持人已结束本轮cp战"
		}

		err = m.CloseCpGame(ctx, opUid, channelId, currPhase)
		if err != nil {
			log.ErrorWithCtx(ctx, "SetPhasePreHandle fail to CloseCpGame. channelId:%v, targetPhase:%v, err:%v", channelId, targetPhase, err)
			return
		}

	case pb.CpGamePhaseType_CP_GAME_PHASE_CREATE_CP: // 开始组队阶段
		toast = "主持人开启了新的一轮cp战，快来参加吧~"

		err = m.BeginCpGame(ctx, opUid, channelId)
		if err != nil {
			log.ErrorWithCtx(ctx, "SetPhasePreHandle fail to BeginCpGame. channelId:%v, targetPhase:%v, err:%v", channelId, targetPhase, err)
			return
		}

	case pb.CpGamePhaseType_CP_GAME_PHASE_PK: // 组队完成， 开始pk
		toast = "主持人结束组队阶段，进入pk阶段"
		// 创建队伍
		if err = m.CreateCpTeamList(ctx, channelId); err != nil {
			log.ErrorWithCtx(ctx, "SetPhasePreHandle fail to CreateCpTeamList. channelId:%v, targetPhase:%v, err:%v", channelId, targetPhase, err)
			return
		}

		// 加入pk
		pkDuration := time.Duration(m.businessConf.GetPkDurationSec()) * time.Second
		if err = m.Cache.AddChannelInPk(channelId, uint32(time.Now().Add(pkDuration).Unix())); err != nil {
			log.ErrorWithCtx(ctx, "SetPhasePreHandle fail to AddChannelInPk. channelId:%v, targetPhase:%v, err:%v", channelId, targetPhase, err)
			return
		}

	case pb.CpGamePhaseType_CP_GAME_PHASE_PUNISH: // pk结束，进入结算及惩罚
		toast = "主持人结束pk阶段，公布最终结果"

		// 移出倒计时队列
		err = m.Cache.RemoveChannelPkEndTime(channelId)
		if err != nil {
			log.ErrorWithCtx(ctx, "SetPhasePreHandle fail to RemoveChannelPkEndTime. channelId:%v, targetPhase:%v, err:%v", channelId, targetPhase, err)
			return
		}

		needResult = true
	case pb.CpGamePhaseType_CP_GAME_PHASE_CHOSE_RARE: // 结算及惩罚结束 进入选择关系环节
		toast = "主持人结束公布结果阶段，进入选择关系阶段"
	default:
	}
	return
}

func (m *ChannelCpGameMgr) ChannelCpGameInfoChangeNotify(ctx context.Context, channelId uint32, toast string) (*pb.CpGameInfo, error) {
	cpGameInfo, err := m.UpdateCurrCpGameCacheInfo(ctx, channelId, 5*time.Minute, false)
	if err != nil {
		log.ErrorWithCtx(ctx, "ChannelCpGameInfoChangeNotify fail to UpdateCurrCpGameCacheInfo. channelId:%v, err:%v", channelId, err)
		return cpGameInfo, err
	}

	err = m.pushChannelCpGameInfoChange(ctx, cpGameInfo, toast)
	if err != nil {
		log.ErrorWithCtx(ctx, "ChannelCpGameInfoChangeNotify fail to pushChannelCpGameInfoChange. channelId:%v, err:%v", channelId, err)
		return cpGameInfo, err
	}

	return cpGameInfo, nil
}

func (m *ChannelCpGameMgr) BeginCpGame(ctx context.Context, opUid, channelId uint32) error {
	gameId, err := m.createGameId(ctx, channelId)
	if err != nil {
		log.ErrorWithCtx(ctx, "BeginCpGame fail to createGameId. channelId:%v, err:%v", channelId, err)
		return err
	}

	_, micId2Uid, err := m.GetMicUid(ctx, channelId)
	if err != nil {
		log.ErrorWithCtx(ctx, "BeginCpGame fail to createGameId. channelId:%v, err:%v", channelId, err)
		return err
	}

	log.Debugf("BeginCpGame SetChannelMicPKUser channelId:%d, micId2Uid:%v", channelId, micId2Uid)
	err = m.Cache.SetChannelMicPKUser(channelId, micId2Uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "BeginCpGame fail to SetChannelMicPKUser. channelId:%v, err:%v", channelId, err)
		return err
	}

	channelCfg, err := m.FellowCli.GetChannelRareConfig(ctx, channelId)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetChannelRareConfig failed, channelId:%d, err:%v", channelId, err)
		return err
	}

	if len(channelCfg.EntranceUrl) != 0 && len(channelCfg.RareList) > 0 {
		err = m.Cache.SetChannelGameOpenRare(channelId, gameId, 1)
		if err != nil {
			log.ErrorWithCtx(ctx, "SetChannelGameOpenRare failed opUid:%v, channelId:%v, channelCfg:%+v, err:%+v", opUid, channelId, channelCfg, err)
		}
	} else {
		log.ErrorWithCtx(ctx, "BeginCpGame SetChannelGameOpenRare failed opUid:%v, channelId:%v, channelCfg:%+v, err:%+v", opUid, channelId, channelCfg, err)
	}
	log.Infof("BeginCpGame opUid:%v, channelId:%v, gameId:%v, micId2Uid:%+v, channelCfg:%+v", opUid, channelId, gameId, micId2Uid)
	return nil
}

func (m *ChannelCpGameMgr) CloseCpGame(ctx context.Context, opUid, channelId, currPhrase uint32) error {
	// 提前结束在此上报数据
	if currPhrase < uint32(pb.CpGamePhaseType_CP_GAME_PHASE_PUNISH) {
		cpGameInfo, err := m.GetCurrCpGameInfo(ctx, channelId)
		if err != nil {
			log.ErrorWithCtx(ctx, "CloseCpGame fail to GetCurrCpGameInfo. channelId:%v, err:%v", channelId, err)
		}

		// 数据上报
		m.ReportCpGameResult(ctx, cpGameInfo, currPhrase, 0)
	}

	err := m.Cache.ClearChannelMic2Uid(channelId)
	if err != nil {
		log.ErrorWithCtx(ctx, "CloseCpGame fail to ClearChannelMic2Uid. channelId:%v, err:%v", channelId, err)
	}

	m.Cache.ClearChannelMicPKUser(channelId)

	// 移出倒计时队列
	err = m.Cache.RemoveChannelPkEndTime(channelId)
	if err != nil {
		log.ErrorWithCtx(ctx, "CloseCpGame fail to RemoveChannelPkEndTime. channelId:%v, opUid:%v, err:%v", channelId, opUid, err)
		return err
	}

	resp, err := m.ChannelMicCli.GetMicrList(ctx, channelId, opUid)
	if err != nil {
		log.ErrorWithCtx(ctx, "CloseCpGame fail to GetMicrList err:%v", err)
		return err
	}

	targetUidList := make([]uint32, 0)
	for _, mic := range resp.GetAllMicList() {
		if mic.GetMicId() == 1 || mic.GetMicUid() == 0 {
			// 主持麦
			continue
		}
		targetUidList = append(targetUidList, mic.MicUid)
	}

	go func() {
		ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
		defer cancel()

		err := m.KickoutChannelMic(ctx, opUid, channelId, resp.GetMicrMode(), targetUidList)
		if err != nil {
			log.ErrorWithCtx(ctx, "CloseCpGame fail to KickoutChannelMic. channelId:%v, err:%v", channelId, err)
		}
	}()

	// Mvp用户清理
	//m.mapPreConsumeMvp.Delete(channelId)
	//m.mapHostAutoHoldMvpMicFlag.Delete(channelId)
	_ = m.Cache.ClearPreConsumeMvp(ctx, channelId)
	_ = m.Cache.ClearAutoHoldMicHost(ctx, channelId)

	// 清理当前游戏限定关系发放状态
	gameId, _, err := m.Cache.GetChannelCpGamePhase(ctx, channelId)
	if err != nil {
		log.ErrorWithCtx(ctx, "CloseCpGame fail to GetChannelCpGamePhase, channelId: %d", channelId)
	}
	err = m.Cache.DelChoseRareStatus(gameId)
	if err != nil {
		log.ErrorWithCtx(ctx, "CloseCpGame fail to DelChoseRareStatus, gameId:%d", gameId)
	}
	_ = m.Cache.DelChannelCpGameInfo(channelId)
	log.Infof("CloseCpGame opUid:%v, channelId:%v KickoutUidList:%v", opUid, channelId, targetUidList)
	return nil
}

func (m *ChannelCpGameMgr) KickoutChannelMic(ctx context.Context, opUid, channelId, currMicMode uint32, targetUidList []uint32) error {
	// 踢嘉宾下麦
	kresp, err := m.ChannelMicCli.KickoutChannelMic(ctx, 0, &channelmicPB.KickoutChannelMicReq{
		OpUid:         opUid,
		ChannelId:     channelId,
		TargetUidList: targetUidList,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "KickoutChannelMic fail to KickoutChannelMic. channelId:%v, err:%v", channelId, err)
	}

	opt := &channelPb.ChannelMicOpt{
		AllMicList: make([]*channelPb.SimpleMicrSpace, 0),
	}

	// 神秘人fakeUid
	uidList := make([]uint32, 0, len(kresp.GetAllMicList()))
	for _, r := range kresp.GetAllMicList() {
		uidList = append(uidList, r.GetMicUid())
	}
	ups, err := m.UserProfileCli.BatchGetUserProfile(ctx, uidList)
	if err != nil {
		log.ErrorWithCtx(ctx, "KickoutChannelMic.BatchGetUserProfile, error: %v, channelId: %d", err, channelId)
		return err
	}

	for _, r := range kresp.GetAllMicList() {
		opt.AllMicList = append(opt.AllMicList, &channelPb.SimpleMicrSpace{
			MicId:    r.MicId,
			MicState: r.MicState,
			Uid:      ups[r.GetMicUid()].GetUid(),
		})
	}

	aResp, err := m.UserProfileCli.BatchGetUserProfile(ctx, targetUidList)
	if nil != err {
		log.ErrorWithCtx(ctx, "KickoutChannelMic fail to GetUsersMap channelID:%v err:%v", channelId, err)
		return err
	}

	for _, r := range kresp.GetKickoutMicList() {
		user, ok := aResp[r.MicUid]
		if !ok {
			continue
		}

		opt.OpMicid = r.GetMicId()
		opt.OpMicUid = user.GetUid()
		opt.OpMicSex = int32(user.GetSex())
		opt.OpTimeMs = kresp.GetServerTimeMs()
		opt.CurrMicmode = currMicMode

		perr := m.pushKickoutChannelMic(ctx, channelId, user, opt)
		if nil != perr {
			log.ErrorWithCtx(ctx, "KickoutChannelMic fail to pushKickoutChannelMic channelID:%v err:%v", channelId, perr)
			return perr
		}
	}

	return nil
}

func (m *ChannelCpGameMgr) GetInPlayerMicUid(ctx context.Context, channelId uint32) ([]uint32, map[uint32]uint32, error) {
	inMicUidList := make([]uint32, 0) // 上麦的uid列表

	micId2Uid, err := m.Cache.GetChannelMic2Uid(ctx, channelId, CpGamePlayerMicList)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetInPlayerMicUidList fail to GetChannelMic2Uid. channelId:%v, err:%v", channelId, err)
		return inMicUidList, micId2Uid, err
	}

	for _, uid := range micId2Uid {
		if uid != 0 {
			inMicUidList = append(inMicUidList, uid)
		}
	}
	return inMicUidList, micId2Uid, nil
}

func (m *ChannelCpGameMgr) CreateCpTeamList(ctx context.Context, channelId uint32) error {
	gameId, _, err := m.Cache.GetChannelCpGameId(ctx, channelId)
	if err != nil {
		log.ErrorWithCtx(ctx, "CreateCpTeamList fail to GetChannelCpGameId. channelId:%v, err:%v", channelId, err)
		return err
	}

	cpGameInfo, err := m.GetCpGameInfoWhenCreateTeam(ctx, channelId, gameId)
	if err != nil {
		log.ErrorWithCtx(ctx, "CreateCpTeamList fail to GetCpGameInfoWhenCreateTeam. channelId:%v, err:%v", channelId, err)
		return err
	}

	inMicUidList, micId2Uid, err := m.GetMicUid(ctx, channelId)
	if err != nil {
		log.ErrorWithCtx(ctx, "CreateCpTeamList fail to GetInPlayerMicUidList. channelId:%v, err:%v", channelId, err)
		return err
	}
	log.Debugf("CreateCpTeamList mic uid len:%d, min user cnt:%d", len(inMicUidList), m.businessConf.GetPkMicUserMinCnt())
	if uint32(len(inMicUidList)) < m.businessConf.GetPkMicUserMinCnt() {
		log.ErrorWithCtx(ctx, "CreateCpTeamList fail . channelId:%v,inMicUidList:%v, micId2Uid:%v", channelId, inMicUidList, micId2Uid)
		return protocol.NewExactServerError(nil, status.ErrChannelCpGameCannotEnterNextPhase,
			fmt.Sprintf("麦上用户不足%d人，无法进行下一阶段", m.businessConf.GetPkMicUserMinCnt()))
	}

	teamedUpMicMap := make(map[uint32]bool) // 已组队的麦
	cpMicList := make([][2]uint32, 0, 3)    // 组队的cp麦列表

	for _, info := range cpGameInfo.GetCpList() {
		memList := info.GetMemList()
		if len(memList) != 2 {
			continue
		}
		cpMicList = append(cpMicList, [2]uint32{memList[0].GetMicId(), memList[1].GetMicId()})
		teamedUpMicMap[memList[0].GetMicId()] = true
		teamedUpMicMap[memList[1].GetMicId()] = true
	}

	// 队伍不足
	if len(cpMicList) < 3 {
		tmpCpMicList, err := m.mergeOppositeSex(ctx, channelId, teamedUpMicMap, inMicUidList, micId2Uid)
		if err != nil {
			log.ErrorWithCtx(ctx, "CreateCpTeamList fail to mergeOppositeSex. channelId:%v, err:%v", channelId, err)
			return err
		}
		cpMicList = append(cpMicList, tmpCpMicList...)
	}

	err = m.Cache.CreateChannelCpGameTeam(ctx, channelId, gameId, cpMicList)
	if err != nil {
		log.ErrorWithCtx(ctx, "CreateCpTeamList fail to CreateChannelCpGameTeam. channelId:%v, err:%v", channelId, err)
		return err
	}

	/*err = m.cpGameEventProducer.ProduceCpGameEvent(uint32(kfk_channel_cp_game.ChannelCpGameEvent_CreateTeam), 0, cpGameInfo)
	  if err != nil {
	  	log.ErrorWithCtx(ctx, "CreateCpTeamList fail to ProduceCpGameEvent. cpGameInfo:%+v err:%v", cpGameInfo, err)
	  }*/

	log.Infof("CreateCpTeamList, channelId:%v, gameId:%v, cpMicList:%+v", channelId, gameId, cpMicList)
	return nil
}

func (m *ChannelCpGameMgr) ChannelCpGamePkResult(ctx context.Context, cpGameInfo *pb.CpGameInfo) error {
	channelId := cpGameInfo.GetChannelId()

	if len(cpGameInfo.GetCpList()) != 3 {
		log.ErrorWithCtx(ctx, "ChannelCpGamePkResult fail. cpGameInfo:%+v err:len(CpList()) != 3", cpGameInfo)
		return nil
	}

	for idx, info := range cpGameInfo.GetCpList() {
		memList := info.GetMemList()
		if len(memList) != 2 {
			continue
		}

		m.PushBreakingNews(ctx, memList[0].GetUid(), memList[1].GetUid(), channelId, info.Score)

		//第一名 增加CP值  头像框
		if idx == 0 {
			err := m.AddStrength(ctx, uint32(TaskTypeWin), memList[0].GetUid(), memList[1].GetUid(), info.Score)
			if err != nil {
				continue
			}

			m.AddWinHeadWare(ctx, memList[0].GetUid(), channelId, cpGameInfo.GetGameId(), info.Score)
			m.AddWinHeadWare(ctx, memList[1].GetUid(), channelId, cpGameInfo.GetGameId(), info.Score)
		}

		if info.GetIsDangerous() {
			m.AddLoseInfo(ctx, memList[0].GetUid(), info.Score, cpGameInfo)
			m.AddLoseInfo(ctx, memList[1].GetUid(), info.Score, cpGameInfo)
		}

		err := m.AddStrength(ctx, uint32(TaskTypeGetScore), memList[0].GetUid(), memList[1].GetUid(), info.Score)
		if err != nil {
			continue
		}

		err = m.AddStrength(ctx, uint32(TaskTypeCreateTeam), memList[0].GetUid(), memList[1].GetUid(), 0)
		if err != nil {
			continue
		}
	}

	//设置获得稀缺关系队伍相关信息
	m.setChoseRareTeam(ctx, cpGameInfo)

	gameId, _, err := m.Cache.GetChannelCpGameId(ctx, channelId)
	if err != nil {
		log.ErrorWithCtx(ctx, "ChannelCpGamePkResult fail to GetChannelCpGameId. cpGameInfo:%+v err:%v", cpGameInfo, err)
	}
	_ = m.Cache.SetChannelCpGameResultInfo(gameId, cpGameInfo)

	err = m.Store.RecordChannelCpGameResult(ctx, channelId, gameId, cpGameInfo.GetCpList())
	if err != nil {
		log.ErrorWithCtx(ctx, "ChannelCpGamePkResult fail to RecordChannelCpGameResult. cpGameInfo:%+v err:%v", cpGameInfo, err)
	}

	err = m.PushCpGameResult(ctx, gameId, cpGameInfo)
	if err != nil {
		log.ErrorWithCtx(ctx, "ChannelCpGamePkResult fail to PushCpGameResult. cpGameInfo:%+v err:%v", cpGameInfo, err)
		//return err
	}

	log.Infof("ChannelCpGamePkResult cpGameInfo:%+v", cpGameInfo)
	return nil
}

func (m *ChannelCpGameMgr) PushCpGameResult(ctx context.Context, gameId uint32, cpGameInfo *pb.CpGameInfo) error {
	channelId := cpGameInfo.GetChannelId()
	topTeamInfo := cpGameInfo.GetCpList()[0]

	if len(topTeamInfo.GetMemList()) != 2 {
		log.ErrorWithCtx(ctx, "PushCpGameResult fail. cpGameInfo:%+v err: len(topTeamInfo.GetMemList()) != 2", cpGameInfo)
		return nil
	}

	// 互相关注
	m.FollowEachOther(ctx, topTeamInfo.GetMemList()[0].GetUid(), topTeamInfo.GetMemList()[1].GetUid())

	cardConf, levelScore, _, err := m.FindCardConfLevel(ctx, channelId, topTeamInfo.GetScore())
	if err != nil {
		log.ErrorWithCtx(ctx, "PushCpGameResult fail to FindCardConfLevel. cpGameInfo:%+v err:%v", cpGameInfo, err)
		return err
	}

	/*if cardConf.Id == 0 {
		log.ErrorWithCtx(ctx, "PushCpGameResult fail. cpGameInfo:%+v cardConf:%v, err: cardConf is empty", cpGameInfo, cardConf)
		return nil
	}*/

	//punishRank := m.FindPunishRank(topTeamInfo.GetScore())
	//punishTeamId := m.FindPunishTeamId(cpGameInfo)

	err = m.pushCpGameResult(ctx, channelId, gameId, cardConf, cpGameInfo)
	if err != nil {
		log.ErrorWithCtx(ctx, "PushCpGameResult fail to pushCpGameResult. cpGameInfo:%+v err:%v", cpGameInfo, err)
		return err
	}

	/*err = m.cpGameEventProducer.ProduceCpGameEvent(uint32(kfk_channel_cp_game.ChannelCpGameEvent_FinishGame), levelScore, cpGameInfo)
	  if err != nil {
	  	log.ErrorWithCtx(ctx, "PushCpGameResult fail to ProduceCpGameEvent. cpGameInfo:%+v err:%v", cpGameInfo, err)
	  }*/

	// 数据上报
	m.ReportCpGameResult(ctx, cpGameInfo, uint32(pb.CpGamePhaseType_CP_GAME_PHASE_PUNISH), levelScore)

	return nil
}

func (m *ChannelCpGameMgr) FollowUser(ctx context.Context, fromUid, toUid uint32) error {
	seq, err := m.SeqGenClient.GenerateSequence(ctx, fromUid, seqgenClient.NamespaceUser, seqgenClient.KeyUgc, 1)
	if err != nil {
		log.ErrorWithCtx(ctx, "FollowUser Failed, GenerateSequence: %v", err)
		return err
	}

	affected, firstTime, err := m.FriendShipCli.FollowUser(ctx, &friendshipPB.FollowUserReq{
		Uid:          fromUid,
		FollowingUid: toUid,
		SequenceId:   seq,
		Source:       friendshipPB.Source_USER_OPERATE,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "FollowUser Failed, FollowUser %d %d %d: %v", fromUid, toUid, seq, err)
		return err
	}

	log.DebugfWithCtx(ctx, "FollowUser %d->%d Affected: %d FirstTime: %t", fromUid, toUid, seq, firstTime)

	if affected {
		// notify ugc sync
		_, _ = notify.NotifySync(fromUid, notify.UGC)
	}

	return nil
}

func (m *ChannelCpGameMgr) FollowEachOther(_ context.Context, uidA, uidB uint32) {
	if uidA == 0 || uidB == 0 {
		return
	}

	go func() {
		ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
		defer cancel()

		cancelCtx, cancel := context.WithTimeout(ctx, time.Millisecond*500)
		defer cancel()
		youKnowWho, serr := m.YouKnowWhoCli.BatchGetUKWInfo(cancelCtx, []uint32{uidA, uidB})
		if serr != nil {
			log.ErrorWithCtx(ctx, "BatchGetUKWInfo fail to pushChannelResult. uidA:%v, err:%v", uidA, serr)
		}

		for _, info := range youKnowWho {
			if info.GetUkwPersonInfo().GetLevel() > 0 {
				log.Debugf("GetUkwPersonInfo level > 0, uid:%d", info.GetUid())
				return
			}
		}

		err := m.FollowUser(ctx, uidA, uidB)
		if err != nil {
			log.ErrorWithCtx(ctx, "FollowEachOther fail. fromUid:%d, toUid:%d, err:%v", uidA, uidB, err)
		}

		err = m.FollowUser(ctx, uidB, uidA)
		if err != nil {
			log.ErrorWithCtx(ctx, "FollowEachOther fail. fromUid:%d, toUid:%d, err:%v", uidB, uidA, err)
		}
	}()
}

func (m *ChannelCpGameMgr) TestPushCpGameResult(ctx context.Context, channelId, uidA, uidB, score, resultTs uint32) error {
	teamInfo := &pb.CpTeamInfo{
		MemList: []*pb.CpMemberInfo{{Uid: uidA}, {Uid: uidB}},
		Score:   score,
	}

	return m.PushCpGameResult(ctx, 0, &pb.CpGameInfo{ChannelId: channelId, CpList: []*pb.CpTeamInfo{teamInfo}})
}

// 异性随机组队
func (m *ChannelCpGameMgr) mergeOppositeSex(ctx context.Context,
	channelId uint32,
	teamedUpMicMap map[uint32]bool,
	inMicUidList []uint32,
	micId2Uid map[uint32]uint32) ([][2]uint32, error) {
	cpMicList := make([][2]uint32, 0, 3) // 组好队的cp麦列表
	noTeamMicList := make([]uint32, 0)   // 未组队的麦列表

	for _, micId := range CpGamePlayerMicList {
		if isTeamed, ok := teamedUpMicMap[micId]; !ok || !isTeamed {
			noTeamMicList = append(noTeamMicList, micId)
		}
	}

	if (len(noTeamMicList) % 2) != 0 {
		log.ErrorWithCtx(ctx, "mergeOppositeSex fail to GetUsersMap. channelId:%v, noTeamMicList:%v, err:len(noTeamMicList) % 2) != 0", channelId, noTeamMicList)
		return cpMicList, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}

	userMap, err := m.AccountCli.GetUsersMap(ctx, inMicUidList)
	if err != nil {
		log.ErrorWithCtx(ctx, "mergeOppositeSex fail to GetUsersMap. channelId:%v, err:%v", channelId, err)
		//return cpMicList, err
	}

	maleMicList := make([]uint32, 0)   // 男性的麦列表
	femaleMicList := make([]uint32, 0) // 女性的麦列表
	nobodyMicList := make([]uint32, 0) // 没人的麦列表

	for _, micId := range noTeamMicList {
		user, ok := userMap[micId2Uid[micId]]
		if !ok {
			nobodyMicList = append(nobodyMicList, micId)

		} else if user.GetSex() == account.Male {
			maleMicList = append(maleMicList, micId)

		} else {
			femaleMicList = append(femaleMicList, micId)
		}
	}

	tmpList := mergeList(maleMicList, femaleMicList, nobodyMicList)

	for i := 1; i < len(tmpList); i += 2 {
		cpMicList = append(cpMicList, [2]uint32{tmpList[i-1], tmpList[i]})
	}

	return cpMicList, nil
}

func mergeList(maleMicList, femaleMicList, nobodyMicList []uint32) []uint32 {
	tmpList := make([]uint32, 0)
	i := 0
	for {
		if i < len(maleMicList) {
			tmpList = append(tmpList, maleMicList[i])
		}
		if i < len(femaleMicList) {
			tmpList = append(tmpList, femaleMicList[i])
		}

		i++
		if i >= len(maleMicList) && i >= len(femaleMicList) {
			break
		}
	}
	tmpList = append(tmpList, nobodyMicList...)

	return tmpList
}

func (m *ChannelCpGameMgr) checkIfCanChangePhase(ctx context.Context, channelId, currPhase, targetPhase uint32) error {

	if targetPhase == currPhase {
		log.ErrorWithCtx(ctx, "checkIfCanChangePhase fail. currPhase:%d, targetPhase:%d", currPhase, targetPhase)
		return protocol.NewExactServerError(nil, status.ErrChannelCpGameCannotChangePhase, "已位于该阶段")
	}

	var err error
	switch pb.CpGamePhaseType(targetPhase) {
	case pb.CpGamePhaseType_CP_GAME_PHASE_CLOSE:
		// do nothing

	case pb.CpGamePhaseType_CP_GAME_PHASE_CREATE_CP,
		pb.CpGamePhaseType_CP_GAME_PHASE_PK,
		pb.CpGamePhaseType_CP_GAME_PHASE_PUNISH,
		pb.CpGamePhaseType_CP_GAME_PHASE_CHOSE_RARE:

		// 必须按阶段顺序切换
		if currPhase+1 != targetPhase {
			err = protocol.NewExactServerError(nil, status.ErrChannelCpGameCannotChangePhase, "无法切换至该阶段")
		}

	default:
		err = protocol.NewExactServerError(nil, status.ErrChannelCpGameCannotChangePhase, "无法切换至该阶段")
	}

	if err != nil {
		log.ErrorWithCtx(ctx, "checkIfCanChangePhase fail. currPhase:%d, targetPhase:%d, err:%v", currPhase, targetPhase, err)
		return err
	}

	if pb.CpGamePhaseType(targetPhase) == pb.CpGamePhaseType_CP_GAME_PHASE_PUNISH {
		inMicUidList, _, err := m.GetInPlayerMicUid(ctx, channelId)
		if err != nil {
			log.ErrorWithCtx(ctx, "checkIfCanChangePhase fail to GetInPlayerMicUidList. channelId:%v, err:%v", channelId, err)
			return err
		}

		// 无人在麦上
		if len(inMicUidList) == 0 {
			log.ErrorWithCtx(ctx, "checkIfCanChangePhase fail . channelId:%v, err:len(inMicUidList):%d", channelId, len(inMicUidList))
			return protocol.NewExactServerError(nil, status.ErrChannelCpGameCannotEnterNextPhase)
		}
	}

	return nil
}

func (m *ChannelCpGameMgr) AddCpGamePhaseEndTime(ctx context.Context, channelId uint32) error {
	incrSec := m.businessConf.GetAddTimeSecOption()
	gameId, _, err := m.Cache.GetChannelCpGameId(ctx, channelId)
	if err != nil {
		log.ErrorWithCtx(ctx, "AddCpGamePhaseEndTime fail to GetChannelCpGameId. channelId:%d, incrSec:%d, err:%v", channelId, incrSec, err)
		return err
	}

	addedSec, err := m.Cache.GetAddedTimeSec(ctx, channelId, gameId)
	if err != nil {
		log.ErrorWithCtx(ctx, "AddCpGamePhaseEndTime fail to GetAddedTimeSec. channelId:%d, incrSec:%d, err:%v", channelId, incrSec, err)
		return err
	}

	maxTotalAddTimeSec := m.businessConf.GetMaxTotalAddTimeSec()
	if addedSec+incrSec > maxTotalAddTimeSec {
		log.ErrorWithCtx(ctx, "AddCpGamePhaseEndTime fail. channelId:%d, incrSec:%d, err:over added sec limit", channelId, incrSec, err)
		//return protocol.NewExactServerError(nil,status.ErrChannelCpGameCannotAddTime, fmt.Sprintf("此阶段最多只能加时%d分钟哦~不能再加了", maxTotalAddTimeSec/60))
		return protocol.NewExactServerError(nil, status.ErrChannelCpGameCannotAddTime, fmt.Sprintf("此阶段最多只能加时%d次哦~不能再加了", maxTotalAddTimeSec/incrSec))
	}

	subPhase, _, err := m.Cache.GetChannelPkSubPhase(ctx, channelId, gameId)
	if err != nil {
		log.ErrorWithCtx(ctx, "AddCpGamePhaseEndTime fail to GetChannelPkSubPhase. channelId:%d, incrSec:%d, err:%v", channelId, incrSec, err)
		return err
	}

	if subPhase == uint32(pb.CpGameInfo_WaitToAddTime) {
		// 在加时冷却期阶段需重新开始计时
		err = m.Cache.AddChannelInPk(channelId, uint32(time.Now().Add(time.Duration(incrSec)*time.Second).Unix()))
		if err != nil {
			log.ErrorWithCtx(ctx, "AddCpGamePhaseEndTime fail to AddChannelInPk. channelId:%d, incrSec:%d, err:%v", channelId, incrSec, err)
			return err
		}

	} else {
		_, ok, err := m.Cache.AddChannelPkEndTime(ctx, channelId, incrSec)
		if err != nil {
			log.ErrorWithCtx(ctx, "AddCpGamePhaseEndTime fail to AddChannelPkEndTime. channelId:%d, incrSec:%d, err:%v", channelId, incrSec, err)
			return err
		}

		if !ok {
			log.ErrorWithCtx(ctx, "AddCpGamePhaseEndTime fail. channelId:%d, incrSec:%d, err:not found pk", channelId, incrSec, err)
			return protocol.NewExactServerError(nil, status.ErrChannelCpGameCannotAddTime, "pk已结束")
		}
	}

	err = m.Cache.IncrAddTimeSec(channelId, gameId, incrSec)
	if err != nil {
		log.ErrorWithCtx(ctx, "AddCpGamePhaseEndTime fail to IncrAddTimeSec. channelId:%d, incrSec:%d, err:%v", channelId, incrSec, err)
	}

	// 退出加时冷却期
	err = m.Cache.SetChannelPkSubPhase(channelId, gameId, uint32(pb.CpGameInfo_Common), 24*time.Hour)
	if err != nil {
		log.ErrorWithCtx(ctx, "AddCpGamePhaseEndTime fail to SetChannelPkSubPhase. channelId:%d, incrSec:%d, err:%v", channelId, incrSec, err)
	}

	_, err = m.ChannelCpGameInfoChangeNotify(ctx, channelId, fmt.Sprintf("加时%d分钟", incrSec/60))
	if err != nil {
		log.ErrorWithCtx(ctx, "AddCpGamePhaseEndTime fail to ChannelCpGameInfoChangeNotify. channelId:%d, incrSec:%d, err:%v", channelId, incrSec, err)
	}

	log.Infof("AddCpGamePhaseEndTime channelId:%d, incrSec:%d", channelId, incrSec)
	return nil
}

func (m *ChannelCpGameMgr) ReportCpGameResult(ctx context.Context, cpGameInfo *pb.CpGameInfo, sourcePhrase, topOneLevelScore uint32) {

	hasTeam := false
	appendTimeSec, err := m.Cache.GetAddedTimeSec(ctx, cpGameInfo.GetChannelId(), cpGameInfo.GetGameId())
	if err != nil {
		log.ErrorWithCtx(ctx, "ReportCpGameResult fail to GetAddedTimeSec. %+v, err:%v", cpGameInfo, err)
	}

	userList := make([]uint32, 0, 8)
	for _, info := range cpGameInfo.GetCpList() {
		if len(info.GetMemList()) < 2 {
			continue
		}
		userList = append(userList, info.GetMemList()[0].GetUid(), info.GetMemList()[1].GetUid())
	}
	userProfiles, err := m.UserProfileCli.BatchGetUserProfile(ctx, userList)
	if err != nil {
		log.ErrorWithCtx(ctx, "ReportCpGameResult.BatchGetUserProfile, userList: %+v, err: %v", userList, err)
	}

	for i, info := range cpGameInfo.GetCpList() {
		if len(info.GetMemList()) != 2 {
			continue
		}

		memList := info.GetMemList()
		if memList[0].GetUid() == 0 && memList[1].GetUid() == 0 {
			continue
		}

		levelScore := uint32(0)
		if i == 0 {
			// 第一名才需要填
			levelScore = topOneLevelScore
		}
		hasTeam = true
		// 其中一方为神秘人则延迟上报
		if userProfiles[memList[0].GetUid()].GetPrivilege().GetType() == uint32(pbApp.EUserPrivilegeType_ENUM_USER_PRIVILEGE_UKW) ||
			userProfiles[memList[1].GetUid()].GetPrivilege().GetType() == uint32(pbApp.EUserPrivilegeType_ENUM_USER_PRIVILEGE_UKW) {
			data := &pb.ReportCpGameInfoDelayReq{
				CpGameInfo:    cpGameInfo,
				CpMemberInfo:  memList,
				Score:         info.GetScore(),
				LevelScore:    levelScore,
				Rank:          uint32(i + 1),
				SourcePhrase:  sourcePhrase,
				AppendTimeSec: appendTimeSec,
			}
			dataBytes, mErr := proto.Marshal(data)
			if mErr != nil {
				log.ErrorWithCtx(ctx, "ReportCpGameResult marshal data, data: %+v, err: %v", data, err)
			}
			req := &yswfukwdelaypb.AddYswfUkwDelayDataReq{
				UidA:    memList[0].GetUid(),
				UidB:    memList[1].GetUid(),
				BizType: uint32(yswfukwdelaypb.YswfBizType_CPReport),
				Data:    dataBytes,
			}
			_, err = m.yswfUkwDelayCli.AddYswfUkwDelayData(ctx, req)
			if mErr != nil || err != nil {
				log.ErrorWithCtx(ctx, "ReportCpGameResult.AddYswfUkwDelayData, req: %+v, err: %v", req, err)
			} else {
				log.InfoWithCtx(ctx, "ReportCpGameResult.AddYswfUkwDelayData, uid: %d, toUid: %d", memList[0].GetUid(), memList[1].GetUid())
				return // 如果调用延迟服务成功则提前返回
			}
		}
		log.InfoWithCtx(ctx, "ReportCpGameResult.DataReportCpGameResult, uid: %d, toUid: %d", memList[0].GetUid(), memList[1].GetUid())
		DataReportCpGameResult(ctx, cpGameInfo, memList, info.GetScore(), levelScore, uint32(i+1), sourcePhrase, appendTimeSec)
	}

	if !hasTeam {
		// 没有组队的，直接上报一条默认数据
		dataReportCpGameEmptyResult(ctx, cpGameInfo, sourcePhrase, appendTimeSec)
	}
}

func dataReportCpGameEmptyResult(ctx context.Context, cpGameInfo *pb.CpGameInfo, sourcePhrase, appendTimeSec uint32) {
	DataReportCpGameResult(ctx, cpGameInfo, nil, 0, 0, 0, sourcePhrase, appendTimeSec)
}

type OSSCpGameResultLog struct {
	Id         string `json:"round_id"`
	Uid        uint32 `json:"uid"`
	ChannelId  string `json:"room_id"`
	Category   string `json:"category"`
	CpUid      uint32 `json:"cp_uid"`
	ExtraTime  uint32 `json:"extra_time"`
	CpValueSum uint32 `json:"cp_value_sum"`
	LevelScore string `json:"level_score"`
	MakeGroup  string `json:"make_group"`
	Rand       string `json:"rand"`
}

func DataReportCpGameResult(ctx context.Context, cpGameInfo *pb.CpGameInfo, memList []*pb.CpMemberInfo, cpScore, levelScore, rank, sourcePhrase, appendTimeSec uint32) {
	makeGroup := 0
	if sourcePhrase > uint32(pb.CpGamePhaseType_CP_GAME_PHASE_CREATE_CP) {
		makeGroup = 1
	}

	publicResult := 0
	if sourcePhrase == uint32(pb.CpGamePhaseType_CP_GAME_PHASE_PUNISH) {
		publicResult = 1
	}
	uid, cpUid := uint32(0), uint32(0)
	if len(memList) >= 2 {
		uid = memList[0].GetUid()
		cpUid = memList[1].GetUid()
	}

	scoreLog := &OSSCpGameResultLog{
		Id:         fmt.Sprintf("%d", cpGameInfo.GetGameId()),
		Uid:        uid,
		ChannelId:  fmt.Sprintf("%d", cpGameInfo.GetChannelId()),
		Category:   fmt.Sprintf("%d", publicResult),
		CpUid:      cpUid,
		ExtraTime:  appendTimeSec / 60,
		CpValueSum: cpScore,
		LevelScore: fmt.Sprintf("%d", levelScore),
		MakeGroup:  fmt.Sprintf("%d", makeGroup),
		Rand:       fmt.Sprintf("%d", rank),
	}
	err := bylink.Track(ctx, uint64(uid), "cp_war_result_log", scoreLog, true)
	if err != nil {
		log.ErrorWithCtx(ctx, "Track err: %v", err)
	}

}

type OSSCpGameScoreLog struct {
	Id        string `json:"round_id"`
	SendUid   uint32 `json:"send_uid"`
	ChannelId string `json:"room_id"`
	OrderId   string `json:"order_id"`
	TargetUid uint32 `json:"target_uid"`
	Stage     string `json:"stage"`
	Value     string `json:"value"`
}

func dataReportCpGameScore(ctx context.Context, orderId string, cid, gameId, sendUid, targetUid, score, phrase uint32) {
	scoreLog := &OSSCpGameScoreLog{
		Id:        fmt.Sprintf("%d", gameId),
		SendUid:   sendUid,
		TargetUid: targetUid,
		ChannelId: fmt.Sprintf("%d", cid),
		Stage:     fmt.Sprintf("%d", phrase),
		Value:     fmt.Sprintf("%d", score),
		OrderId:   orderId,
	}
	err := bylink.Track(ctx, uint64(sendUid), "cp_war_send_gift_log", scoreLog, true)
	if err != nil {
		log.ErrorWithCtx(ctx, "Track err: %v", err)
	}
}

func (m *ChannelCpGameMgr) PushBreakingNews(ctx context.Context, uid, toUid, channelId, score uint32) error {
	if score < BreakingNewsTbanBig || (uid == 0 && toUid == 0) {
		return nil
	}
	log.Infof("PushBreakingNews uid:%d, toUid:%d, channel：%d, score:%d", uid, toUid, channelId, score)

	if uid == 0 {
		uid = toUid
		toUid = 0
	}

	//全服推送
	err := m.PushBreakingNewsV3(uid, toUid, channelId, score)
	if nil != err {
		log.ErrorWithCtx(ctx, "PushBreakingNewsV3 err:%v", err)
		return err
	}
	log.InfoWithCtx(ctx, "PushBreakingNews uid:%d, toUid:%d, channelID:%d, score:%d ", uid, toUid, channelId, score)
	return nil
}

// 构造全服公告前缀和内容
func (m *ChannelCpGameMgr) GetBreakingContent(targetUid, triggerType uint32) (string, string) {
	var strNewsPrefix, strNewsContent string
	if triggerType == uint32(pushPb.CommBreakingNewsBaseOpt_CHANNEL_CP_GAME_SUPER_SCORE) {
		strNewsPrefix = "九州同乐！"
		if targetUid == 0 {
			strNewsContent = "在cp战中出类拔萃！"
		} else {
			strNewsContent = "在cp战中携手并进，出类拔萃！"
		}
	} else {
		strNewsPrefix = "普天同庆！"
		if targetUid == 0 {
			strNewsContent = "在cp战中卓尔不凡！"
		} else {
			strNewsContent = "在cp战中同心协力，卓尔不凡！"
		}
	}
	return strNewsPrefix, strNewsContent
}

func (m *ChannelCpGameMgr) AddLoseInfo(ctx context.Context, uid, score uint32, info *pb.CpGameInfo) error {
	log.DebugfWithCtx(ctx, "AddLoseInfo uid:%d, score:%d, channelId:%d", uid, score, info.GetChannelId())
	if uid == 0 {
		return nil
	}

	_, err := m.AddLoseHeadWare(ctx, uid, info.GetChannelId(), info.GetGameId(), score)
	if err != nil {
		log.ErrorWithCtx(ctx, "AddLoseHeadWare failed, uid:%d, err:%d", uid, err)
		return err
	}

	_, lastId, _ := m.Cache.GetLoseHeadWear(ctx, uid)
	if lastId == 0 {
		return nil
	}

	headWear := m.businessConf.GetHeadWareById(lastId)
	if headWear == nil {
		return nil
	}

	log.DebugfWithCtx(ctx, "AddLoseInfo uid:%d, url:%s, channelId:%d", uid, headWear.LoseUrl, info.GetChannelId())
	if len(headWear.LoseUrl) > 0 {
		for idx, lose := range info.GetLoseList() {
			if lose.Uid == uid {
				info.LoseList[idx].IconUrl = headWear.LoseUrl
				return nil
			}
		}

		info.LoseList = append(info.LoseList, &pb.LoseInfo{
			Uid:     uid,
			IconUrl: headWear.LoseUrl,
		})
	}
	return nil
}

// 设置选择限定关系队伍
func (m *ChannelCpGameMgr) setChoseRareTeam(ctx context.Context, cpGameInfo *pb.CpGameInfo) (err error) {
	//房间没配置 不用设置
	channelId := cpGameInfo.GetChannelId()
	isOpen, _ := m.Cache.GetChannelGameOpenRare(ctx, channelId, cpGameInfo.GetGameId())
	if !isOpen {
		log.Infof("GetChannelRareConfig no open channel cfg,channelId:%d, gameId:%+v", channelId, cpGameInfo.GetGameId())
		return nil
	}

	rareConfig := m.businessConf.GetRareDayConfigList()
	if len(rareConfig) == 0 {
		log.Warnf("checkNeedChoseRare no day config")
		return nil
	}

	//可选限定关系环节  增加可选限定关系的队伍列表
	for _, cp := range cpGameInfo.GetCpList() {
		if len(cp.GetMemList()) != 2 {
			continue
		}
		userA := cp.GetMemList()[0]
		userB := cp.GetMemList()[1]

		if userA.GetUid() == 0 || userB.GetUid() == 0 {
			continue
		}

		var currentCfg *conf.RareDayConfig
		for _, dayCfg := range rareConfig {
			if cp.Score >= dayCfg.CpValue {
				currentCfg = dayCfg
			}
		}

		if currentCfg != nil && currentCfg.Day > 0 {
			rareTeam := &pb.RareTeamInfo{
				Day:    currentCfg.Day,
				TeamId: cp.GetTeamId(),
				Score:  cp.GetScore(),
			}

			rareTeam.MemList = append(rareTeam.MemList, &pb.CpMemberInfo{Uid: userA.Uid, MicId: userA.MicId})
			rareTeam.MemList = append(rareTeam.MemList, &pb.CpMemberInfo{Uid: userB.Uid, MicId: userB.MicId})
			cpGameInfo.RareList = append(cpGameInfo.RareList, rareTeam)
			log.DebugfWithCtx(ctx, "set current channelId：%d, rareTeam:%+v", cpGameInfo.ChannelId, rareTeam)
		}
	}
	return nil
}

func (m *ChannelCpGameMgr) ChoseCpRare(ctx context.Context, channelId, rareId uint32,
	teamId string, confirm bool, choseRare []*pb.ChoseRare) error {

	// 构造推送信息
	cpChoseRareResult := &channel_cp_game_logic.CpChoseRareResult{
		ChannelId: channelId,
		RareId:    rareId,
		TeamId:    teamId,
		Confirm:   confirm,
	}

	// 获取用户信息
	userMap, uidList, err := m.getUserProfile(ctx, teamId, channelId, choseRare)
	if err != nil {
		log.ErrorWithCtx(ctx, "ChoseCpRare.getUserProfile, error: %v, teamId: %s, channelId: %d", err, teamId, channelId)
		return err
	}

	if len(uidList) != 2 {
		log.ErrorWithCtx(ctx, "ChoseCpRare teamId: %s, channelId: %d, uidList: %v,", teamId, channelId, uidList)
		return err
	}

	uidA := uidList[0]
	uidB := uidList[1]

	// 组合关系 需要推送对应uid关系选择的关系
	for _, c := range choseRare {
		cr := &channel_cp_game_logic.ChoseRare{
			Uid:         userMap[c.Uid].GetUid(),
			SubRareId:   c.SubRareId,
			SubRareName: c.SubRareName,
		}
		cpChoseRareResult.RareList = append(cpChoseRareResult.RareList, cr)
	}

	gameId, _, err := m.Cache.GetChannelCpGameId(ctx, channelId)
	if err != nil {
		log.ErrorWithCtx(ctx, "ChoseCpRare fail to GetChannelCpGameId. channelId:%d, err:%v", channelId, err)
		return err
	}

	// 记录本次选择
	cacheCpChoseRareResult := &pb.CpChoseRareResult{
		ChannelId: channelId,
		TeamId:    teamId,
		RareId:    rareId,
		RareList:  choseRare,
		Confirm:   confirm,
	}

	err = m.Cache.SetChoseRareResult(gameId, cacheCpChoseRareResult)
	if err != nil {
		log.ErrorWithCtx(ctx, "ChoseCpRare fail to SetChoseRareResult, cacheCpChoseRareResult:%v, err:%v", cacheCpChoseRareResult, err)
		return err
	}

	// 确认时 需要调用 Fellosvr AddRare 接口 和构造公屏消息
	var toast string
	if confirm {
		relationName, day, err := m.confirmChoseRare(ctx, gameId, uidA, uidB, cacheCpChoseRareResult)
		if err != nil {
			log.ErrorWithCtx(ctx, "ChoseCpRare fail to confirmChoseRare, channelId: %d, teamId:%s,rareId:%d, choseRare: %+v,"+
				" err: %v", channelId, teamId, rareId, choseRare, err)
			return err
		}
		toast = fmt.Sprintf("恭喜：%s与%s达成“%s”关系%d天", userMap[uidA].GetNickname(), userMap[uidB].GetNickname(), relationName, day)
	}

	go func() {
		newCtx, cancel := context.WithTimeout(context.Background(), time.Second*5)
		defer cancel()

		//发送当前选择结果
		err = m.pushCpChoseRareResult(newCtx, cpChoseRareResult, toast)
		if err != nil {
			log.ErrorWithCtx(ctx, "ChoseCpRare fail to pushCpChoseRareResult. cpChoseRareResult:%v, toast:%s, err:%v", cpChoseRareResult, err)
		}

		// 更新缓存 并且更新客户端信息
		currGameInfo, _ := m.ChannelCpGameInfoChangeNotify(newCtx, channelId, "")

		if confirm {
			// 清理选择结果
			if err = m.clearChose(ctx, currGameInfo, gameId); err != nil {
				log.ErrorWithCtx(ctx, "ChoseCpRare.ChoseCpRare, error: %v", err)
			}
		}
	}()

	return nil
}

func (m *ChannelCpGameMgr) confirmChoseRare(ctx context.Context, gameId, uidA, uidB uint32, result *pb.CpChoseRareResult) (rareName string, day uint32, err error) {
	teamId := result.GetTeamId()
	channelId := result.GetChannelId()
	rareId := result.GetRareId()
	choseRare := result.GetRareList()

	// 判断是否已经发放, 已发放提前返回
	if statusMap, _ := m.Cache.GetChoseRareStatus(ctx, gameId, []string{teamId}); statusMap[teamId] == 1 {
		log.Warnf("confirmChoseRare already chose gameId:%d, channelId:%d, teamID:%s",
			gameId, channelId, teamId)
		return rareName, day, err
	}

	// 更新关系发放状态 1: 已发放
	err = m.Cache.SetChoseRareStatus(gameId, 1, teamId)
	if err != nil {
		log.ErrorWithCtx(ctx, "ChoseCpRare fail to SetChoseRareStatus, gameId:%d, teamId:%d, err:%v", gameId, teamId, err)
		return rareName, day, err
	}

	// 获取当前天数
	currGameInfo, err := m.GetCurrCpGameCacheInfo(ctx, channelId)
	if err != nil {
		log.ErrorWithCtx(ctx, "ChoseCpRare fail to ChannelCpGameInfoChangeNotify. channelId:%v, err:%v", channelId, err)
		return
	}

	for _, rareInfo := range currGameInfo.GetRareList() {
		if rareInfo.GetTeamId() == teamId {
			day = rareInfo.GetDay()
			break
		}
	}

	// 发放稀缺关系
	rareResp, err := m.FellowCli.AddRare(ctx, uidA, uidB, rareId, gameId, channelId, day*3600*24, choseRare)
	if err != nil {
		redisErr := m.Cache.DelTeamChoseRareStatus(gameId, teamId)
		log.ErrorWithCtx(ctx, "ChoseCpRare fail to FellowCli.AddRare. teamId:%s, rareId:%d, channelId:%d,"+
			" subRare:%v, err:%v, redisErr:%v, uid:%d, toUid:%d",
			teamId, rareId, channelId, err, redisErr, uidA, uidB)
		return
	}
	return rareResp.GetRareName(), day, err
}

func (m *ChannelCpGameMgr) getUserProfile(ctx context.Context, teamId string, channelId uint32, choseRare []*pb.ChoseRare) (map[uint32]*pbApp.UserProfile, []uint32, error) {
	uidList := make([]uint32, 0, 2)

	for _, rare := range choseRare {
		if rare.GetUid() > 0 {
			uidList = append(uidList, rare.GetUid())
		}
	}

	if len(uidList) != 2 {
		micA, micB := cache.ParseCpGameTeamId(teamId)
		mic2Uid, err := m.Cache.GetChannelMicPKUser(ctx, channelId, []uint32{micA, micB})
		if err != nil {
			return nil, []uint32{0, 0}, err
		}

		for _, uid := range mic2Uid {
			if uid > 0 {
				uidList = append(uidList, uid)
			}
		}
		log.Debugf("getUserProfile from team channelId:%d, teamId:%s", channelId, teamId)
	}

	userMap, aErr := m.UserProfileCli.BatchGetUserProfileV2(ctx, uidList, true)
	if aErr != nil {
		log.ErrorWithCtx(ctx, "ChoseCpRare fail to GetUsersMap, uidList:%v, aErr:%v", uidList, aErr)
		return nil, []uint32{0, 0}, aErr
	}

	return userMap, uidList, aErr
}

func (m *ChannelCpGameMgr) clearChose(ctx context.Context, currGameInfo *pb.CpGameInfo, gameId uint32) error {
	// 三个队伍选择完毕时，清除选择结果
	for _, rareInfo := range currGameInfo.RareList {
		if rareInfo.ChoseStatus == 0 {
			return nil
		}
	}

	err := m.Cache.DelChoseRareResult(gameId)
	if err != nil {
		log.ErrorWithCtx(ctx, "ChoseCpRare fail to DelChoseRareResult, gameId:%d, err%v", gameId, err)
		return err
	}

	return nil
}

func (m *ChannelCpGameMgr) GetMicUid(ctx context.Context, channelId uint32) ([]uint32, map[uint32]uint32, error) {
	inMicUidList := make([]uint32, 0) // 上麦的uid列表
	micId2Uid := make(map[uint32]uint32)

	resp, err := m.ChannelMicCli.GetMicrList(ctx, channelId, 0)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetMicUid fail to GetMicrList. channelId:%v, err:%v", channelId, err)
		return inMicUidList, micId2Uid, err
	}

	for _, info := range resp.GetAllMicList() {
		if info.GetMicId() <= 1 || info.GetMicUid() == 0 {
			continue
		}
		micId2Uid[info.GetMicId()] = info.GetMicUid()
		inMicUidList = append(inMicUidList, info.GetMicUid())
	}

	serr := m.Cache.SetChannelMic2Uid(channelId, micId2Uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetMicUid fail to SetChannelMic2Uid. channelId:%v, err:%v", channelId, serr)
		return inMicUidList, micId2Uid, serr
	}

	log.Infof("GetMicUid channelId:%d, inMicUidList:%v, micId2Uid:%+v", channelId, inMicUidList, micId2Uid)
	return inMicUidList, micId2Uid, nil
}

func (m *ChannelCpGameMgr) GetUidMic(ctx context.Context, channelId uint32) (map[uint32]uint32, error) {
	uidMic := make(map[uint32]uint32)

	_, micUid, err := m.GetMicUid(ctx, channelId)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetMicUid, channelId: %d, error: %v", channelId, err)
		return uidMic, err
	}

	for k, v := range micUid {
		uidMic[v] = k
	}

	return uidMic, nil
}

type OSSCpGamePhraseLog struct {
	Id        string `json:"round_id"`
	ChannelId string `json:"room_id"`
	Status    string `json:"status"`
}

func (m *ChannelCpGameMgr) dataReportCpGamePhaseLog(ctx context.Context, uid, channelId, targetPhrase uint32) {
	gameId, _, _ := m.Cache.GetChannelCpGameId(ctx, channelId)
	phaseLog := &OSSCpGamePhraseLog{
		Id:        fmt.Sprintf("%d", gameId),
		ChannelId: fmt.Sprintf("%d", channelId),
		Status:    fmt.Sprintf("%d", targetPhrase),
	}
	err := bylink.Track(ctx, uint64(uid), "cp_war_status_log", phaseLog, true)
	if err != nil {
		log.ErrorWithCtx(ctx, "Track err: %v", err)
	}
}
