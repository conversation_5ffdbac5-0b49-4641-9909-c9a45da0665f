// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/services/channel-cp-game/manager (interfaces: IChannelCpGameMgr)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	"golang.52tt.com/services/tt-rev/channel-cp-game/internal/mysql"
	reflect "reflect"
	time "time"

	gomock "github.com/golang/mock/gomock"
	app "golang.52tt.com/protocol/app"
	channel_cp_game "golang.52tt.com/protocol/services/channel-cp-game"
	ChannelMic "golang.52tt.com/protocol/services/channelmicsvr"
	channelsvr "golang.52tt.com/protocol/services/channelsvr"
	kafkachannalevent "golang.52tt.com/protocol/services/minToolkit/kafka/pb/kafkachannalevent"
	kafkapresent "golang.52tt.com/protocol/services/minToolkit/kafka/pb/kafkapresent"
	kafkasimplemic "golang.52tt.com/protocol/services/minToolkit/kafka/pb/kafkasimplemic"
)

// MockIChannelCpGameMgr is a mock of IChannelCpGameMgr interface.
type MockIChannelCpGameMgr struct {
	ctrl     *gomock.Controller
	recorder *MockIChannelCpGameMgrMockRecorder
}

// MockIChannelCpGameMgrMockRecorder is the mock recorder for MockIChannelCpGameMgr.
type MockIChannelCpGameMgrMockRecorder struct {
	mock *MockIChannelCpGameMgr
}

// NewMockIChannelCpGameMgr creates a new mock instance.
func NewMockIChannelCpGameMgr(ctrl *gomock.Controller) *MockIChannelCpGameMgr {
	mock := &MockIChannelCpGameMgr{ctrl: ctrl}
	mock.recorder = &MockIChannelCpGameMgrMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIChannelCpGameMgr) EXPECT() *MockIChannelCpGameMgrMockRecorder {
	return m.recorder
}

// AddCpCardConf mocks base method.
func (m *MockIChannelCpGameMgr) AddCpCardConf(arg0 context.Context, arg1 *channel_cp_game.CpCardConf) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddCpCardConf", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddCpCardConf indicates an expected call of AddCpCardConf.
func (mr *MockIChannelCpGameMgrMockRecorder) AddCpCardConf(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddCpCardConf", reflect.TypeOf((*MockIChannelCpGameMgr)(nil).AddCpCardConf), arg0, arg1)
}

// AddCpGameGodRankList mocks base method.
func (m *MockIChannelCpGameMgr) AddCpGameGodRankList(arg0 context.Context, arg1 uint32, arg2 []*mysql.ChannelCpGameGodRank) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddCpGameGodRankList", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddCpGameGodRankList indicates an expected call of AddCpGameGodRankList.
func (mr *MockIChannelCpGameMgrMockRecorder) AddCpGameGodRankList(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddCpGameGodRankList", reflect.TypeOf((*MockIChannelCpGameMgr)(nil).AddCpGameGodRankList), arg0, arg1, arg2)
}

// AddCpGamePhaseEndTime mocks base method.
func (m *MockIChannelCpGameMgr) AddCpGamePhaseEndTime(arg0 context.Context, arg1 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddCpGamePhaseEndTime", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddCpGamePhaseEndTime indicates an expected call of AddCpGamePhaseEndTime.
func (mr *MockIChannelCpGameMgrMockRecorder) AddCpGamePhaseEndTime(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddCpGamePhaseEndTime", reflect.TypeOf((*MockIChannelCpGameMgr)(nil).AddCpGamePhaseEndTime), arg0, arg1)
}

// AddCpUserScore mocks base method.
func (m *MockIChannelCpGameMgr) AddCpUserScore(arg0 context.Context, arg1, arg2, arg3, arg4 uint32, arg5 map[uint32]uint32) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddCpUserScore", arg0, arg1, arg2, arg3, arg4, arg5)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddCpUserScore indicates an expected call of AddCpUserScore.
func (mr *MockIChannelCpGameMgrMockRecorder) AddCpUserScore(arg0, arg1, arg2, arg3, arg4, arg5 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddCpUserScore", reflect.TypeOf((*MockIChannelCpGameMgr)(nil).AddCpUserScore), arg0, arg1, arg2, arg3, arg4, arg5)
}

// AddEventScore mocks base method.
func (m *MockIChannelCpGameMgr) AddEventScore(arg0, arg1 uint32, arg2 *kafkapresent.PresentEvent) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddEventScore", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddEventScore indicates an expected call of AddEventScore.
func (mr *MockIChannelCpGameMgrMockRecorder) AddEventScore(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddEventScore", reflect.TypeOf((*MockIChannelCpGameMgr)(nil).AddEventScore), arg0, arg1, arg2)
}

// AddLoseHeadWare mocks base method.
func (m *MockIChannelCpGameMgr) AddLoseHeadWare(arg0 context.Context, arg1, arg2, arg3 uint32) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddLoseHeadWare", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddLoseHeadWare indicates an expected call of AddLoseHeadWare.
func (mr *MockIChannelCpGameMgrMockRecorder) AddLoseHeadWare(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddLoseHeadWare", reflect.TypeOf((*MockIChannelCpGameMgr)(nil).AddLoseHeadWare), arg0, arg1, arg2, arg3)
}

// AddLoseInfo mocks base method.
func (m *MockIChannelCpGameMgr) AddLoseInfo(arg0 context.Context, arg1, arg2 uint32, arg3 *channel_cp_game.CpGameInfo) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddLoseInfo", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddLoseInfo indicates an expected call of AddLoseInfo.
func (mr *MockIChannelCpGameMgrMockRecorder) AddLoseInfo(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddLoseInfo", reflect.TypeOf((*MockIChannelCpGameMgr)(nil).AddLoseInfo), arg0, arg1, arg2, arg3)
}

// AddRelationScore mocks base method.
func (m *MockIChannelCpGameMgr) AddRelationScore(arg0 context.Context, arg1, arg2, arg3, arg4, arg5 uint32, arg6 map[uint32]uint32) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddRelationScore", arg0, arg1, arg2, arg3, arg4, arg5, arg6)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddRelationScore indicates an expected call of AddRelationScore.
func (mr *MockIChannelCpGameMgrMockRecorder) AddRelationScore(arg0, arg1, arg2, arg3, arg4, arg5, arg6 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddRelationScore", reflect.TypeOf((*MockIChannelCpGameMgr)(nil).AddRelationScore), arg0, arg1, arg2, arg3, arg4, arg5, arg6)
}

// AddStrength mocks base method.
func (m *MockIChannelCpGameMgr) AddStrength(arg0 context.Context, arg1, arg2, arg3, arg4 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddStrength", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddStrength indicates an expected call of AddStrength.
func (mr *MockIChannelCpGameMgrMockRecorder) AddStrength(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddStrength", reflect.TypeOf((*MockIChannelCpGameMgr)(nil).AddStrength), arg0, arg1, arg2, arg3, arg4)
}

// AddWinHeadWare mocks base method.
func (m *MockIChannelCpGameMgr) AddWinHeadWare(arg0 context.Context, arg1, arg2 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddWinHeadWare", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddWinHeadWare indicates an expected call of AddWinHeadWare.
func (mr *MockIChannelCpGameMgrMockRecorder) AddWinHeadWare(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddWinHeadWare", reflect.TypeOf((*MockIChannelCpGameMgr)(nil).AddWinHeadWare), arg0, arg1, arg2)
}

// AutoHoldMicPushMsg mocks base method.
func (m *MockIChannelCpGameMgr) AutoHoldMicPushMsg(arg0 context.Context, arg1 uint32, arg2 *channelsvr.ChannelSimpleInfo, arg3 uint32, arg4 *ChannelMic.SimpleHoldMicrSpaceResp, arg5 *app.UserProfile) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AutoHoldMicPushMsg", arg0, arg1, arg2, arg3, arg4, arg5)
	ret0, _ := ret[0].(error)
	return ret0
}

// AutoHoldMicPushMsg indicates an expected call of AutoHoldMicPushMsg.
func (mr *MockIChannelCpGameMgrMockRecorder) AutoHoldMicPushMsg(arg0, arg1, arg2, arg3, arg4, arg5 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AutoHoldMicPushMsg", reflect.TypeOf((*MockIChannelCpGameMgr)(nil).AutoHoldMicPushMsg), arg0, arg1, arg2, arg3, arg4, arg5)
}

// BatchGetCpGameEntry mocks base method.
func (m *MockIChannelCpGameMgr) BatchGetCpGameEntry(arg0 context.Context, arg1, arg2, arg3 uint32) (*channel_cp_game.BatchGetCpGameEntryResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetCpGameEntry", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*channel_cp_game.BatchGetCpGameEntryResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetCpGameEntry indicates an expected call of BatchGetCpGameEntry.
func (mr *MockIChannelCpGameMgrMockRecorder) BatchGetCpGameEntry(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetCpGameEntry", reflect.TypeOf((*MockIChannelCpGameMgr)(nil).BatchGetCpGameEntry), arg0, arg1, arg2, arg3)
}

// BeginCpGame mocks base method.
func (m *MockIChannelCpGameMgr) BeginCpGame(arg0 context.Context, arg1, arg2 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BeginCpGame", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// BeginCpGame indicates an expected call of BeginCpGame.
func (mr *MockIChannelCpGameMgrMockRecorder) BeginCpGame(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BeginCpGame", reflect.TypeOf((*MockIChannelCpGameMgr)(nil).BeginCpGame), arg0, arg1, arg2)
}

// ChannelCpGameInfoChangeNotify mocks base method.
func (m *MockIChannelCpGameMgr) ChannelCpGameInfoChangeNotify(arg0 context.Context, arg1 uint32, arg2 string) (*channel_cp_game.CpGameInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ChannelCpGameInfoChangeNotify", arg0, arg1, arg2)
	ret0, _ := ret[0].(*channel_cp_game.CpGameInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ChannelCpGameInfoChangeNotify indicates an expected call of ChannelCpGameInfoChangeNotify.
func (mr *MockIChannelCpGameMgrMockRecorder) ChannelCpGameInfoChangeNotify(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ChannelCpGameInfoChangeNotify", reflect.TypeOf((*MockIChannelCpGameMgr)(nil).ChannelCpGameInfoChangeNotify), arg0, arg1, arg2)
}

// ChannelCpGamePkResult mocks base method.
func (m *MockIChannelCpGameMgr) ChannelCpGamePkResult(arg0 context.Context, arg1 *channel_cp_game.CpGameInfo) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ChannelCpGamePkResult", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// ChannelCpGamePkResult indicates an expected call of ChannelCpGamePkResult.
func (mr *MockIChannelCpGameMgrMockRecorder) ChannelCpGamePkResult(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ChannelCpGamePkResult", reflect.TypeOf((*MockIChannelCpGameMgr)(nil).ChannelCpGamePkResult), arg0, arg1)
}

// ChoseCpRare mocks base method.
func (m *MockIChannelCpGameMgr) ChoseCpRare(arg0 context.Context, arg1, arg2 uint32, arg3 string, arg4 bool, arg5 []*channel_cp_game.ChoseRare) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ChoseCpRare", arg0, arg1, arg2, arg3, arg4, arg5)
	ret0, _ := ret[0].(error)
	return ret0
}

// ChoseCpRare indicates an expected call of ChoseCpRare.
func (mr *MockIChannelCpGameMgrMockRecorder) ChoseCpRare(arg0, arg1, arg2, arg3, arg4, arg5 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ChoseCpRare", reflect.TypeOf((*MockIChannelCpGameMgr)(nil).ChoseCpRare), arg0, arg1, arg2, arg3, arg4, arg5)
}

// CloseCpGame mocks base method.
func (m *MockIChannelCpGameMgr) CloseCpGame(arg0 context.Context, arg1, arg2, arg3 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CloseCpGame", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// CloseCpGame indicates an expected call of CloseCpGame.
func (mr *MockIChannelCpGameMgrMockRecorder) CloseCpGame(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CloseCpGame", reflect.TypeOf((*MockIChannelCpGameMgr)(nil).CloseCpGame), arg0, arg1, arg2, arg3)
}

// CpGameMvpAutoHoldMic mocks base method.
func (m *MockIChannelCpGameMgr) CpGameMvpAutoHoldMic(arg0 context.Context, arg1, arg2 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CpGameMvpAutoHoldMic", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// CpGameMvpAutoHoldMic indicates an expected call of CpGameMvpAutoHoldMic.
func (mr *MockIChannelCpGameMgrMockRecorder) CpGameMvpAutoHoldMic(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CpGameMvpAutoHoldMic", reflect.TypeOf((*MockIChannelCpGameMgr)(nil).CpGameMvpAutoHoldMic), arg0, arg1, arg2)
}

// CpScore2Strength mocks base method.
func (m *MockIChannelCpGameMgr) CpScore2Strength(arg0 context.Context, arg1, arg2, arg3, arg4 uint32) uint32 {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CpScore2Strength", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(uint32)
	return ret0
}

// CpScore2Strength indicates an expected call of CpScore2Strength.
func (mr *MockIChannelCpGameMgrMockRecorder) CpScore2Strength(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CpScore2Strength", reflect.TypeOf((*MockIChannelCpGameMgr)(nil).CpScore2Strength), arg0, arg1, arg2, arg3, arg4)
}

// CreateCpTeamList mocks base method.
func (m *MockIChannelCpGameMgr) CreateCpTeamList(arg0 context.Context, arg1 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateCpTeamList", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateCpTeamList indicates an expected call of CreateCpTeamList.
func (mr *MockIChannelCpGameMgrMockRecorder) CreateCpTeamList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateCpTeamList", reflect.TypeOf((*MockIChannelCpGameMgr)(nil).CreateCpTeamList), arg0, arg1)
}

// DelCpCardConf mocks base method.
func (m *MockIChannelCpGameMgr) DelCpCardConf(arg0 context.Context, arg1 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelCpCardConf", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// DelCpCardConf indicates an expected call of DelCpCardConf.
func (mr *MockIChannelCpGameMgrMockRecorder) DelCpCardConf(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelCpCardConf", reflect.TypeOf((*MockIChannelCpGameMgr)(nil).DelCpCardConf), arg0, arg1)
}

// FillGodRankTopInfo mocks base method.
func (m *MockIChannelCpGameMgr) FillGodRankTopInfo(arg0 context.Context, arg1 uint32, arg2 *channel_cp_game.CpGameInfo) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FillGodRankTopInfo", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// FillGodRankTopInfo indicates an expected call of FillGodRankTopInfo.
func (mr *MockIChannelCpGameMgrMockRecorder) FillGodRankTopInfo(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FillGodRankTopInfo", reflect.TypeOf((*MockIChannelCpGameMgr)(nil).FillGodRankTopInfo), arg0, arg1, arg2)
}

// FillLevelInfo mocks base method.
func (m *MockIChannelCpGameMgr) FillLevelInfo(arg0 context.Context, arg1 uint32, arg2 *channel_cp_game.CpGameInfo) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FillLevelInfo", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// FillLevelInfo indicates an expected call of FillLevelInfo.
func (mr *MockIChannelCpGameMgrMockRecorder) FillLevelInfo(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FillLevelInfo", reflect.TypeOf((*MockIChannelCpGameMgr)(nil).FillLevelInfo), arg0, arg1, arg2)
}

// FillLoseInfo mocks base method.
func (m *MockIChannelCpGameMgr) FillLoseInfo(arg0 context.Context, arg1 uint32, arg2 *channel_cp_game.CpGameInfo) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FillLoseInfo", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// FillLoseInfo indicates an expected call of FillLoseInfo.
func (mr *MockIChannelCpGameMgrMockRecorder) FillLoseInfo(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FillLoseInfo", reflect.TypeOf((*MockIChannelCpGameMgr)(nil).FillLoseInfo), arg0, arg1, arg2)
}

// FillPunishTeam mocks base method.
func (m *MockIChannelCpGameMgr) FillPunishTeam(arg0 context.Context, arg1 uint32, arg2 *channel_cp_game.CpGameInfo) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FillPunishTeam", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// FillPunishTeam indicates an expected call of FillPunishTeam.
func (mr *MockIChannelCpGameMgrMockRecorder) FillPunishTeam(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FillPunishTeam", reflect.TypeOf((*MockIChannelCpGameMgr)(nil).FillPunishTeam), arg0, arg1, arg2)
}

// FillRareInfo mocks base method.
func (m *MockIChannelCpGameMgr) FillRareInfo(arg0 context.Context, arg1, arg2, arg3 uint32, arg4 *channel_cp_game.CpGameInfo) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FillRareInfo", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(error)
	return ret0
}

// FillRareInfo indicates an expected call of FillRareInfo.
func (mr *MockIChannelCpGameMgrMockRecorder) FillRareInfo(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FillRareInfo", reflect.TypeOf((*MockIChannelCpGameMgr)(nil).FillRareInfo), arg0, arg1, arg2, arg3, arg4)
}

// FindCardConfLevel mocks base method.
func (m *MockIChannelCpGameMgr) FindCardConfLevel(arg0 context.Context, arg1, arg2 uint32) (*channel_cp_game.CpCardConf, uint32, string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FindCardConfLevel", arg0, arg1, arg2)
	ret0, _ := ret[0].(*channel_cp_game.CpCardConf)
	ret1, _ := ret[1].(uint32)
	ret2, _ := ret[2].(string)
	ret3, _ := ret[3].(error)
	return ret0, ret1, ret2, ret3
}

// FindCardConfLevel indicates an expected call of FindCardConfLevel.
func (mr *MockIChannelCpGameMgrMockRecorder) FindCardConfLevel(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindCardConfLevel", reflect.TypeOf((*MockIChannelCpGameMgr)(nil).FindCardConfLevel), arg0, arg1, arg2)
}

// FindMvpUid mocks base method.
func (m *MockIChannelCpGameMgr) FindMvpUid(arg0 context.Context, arg1, arg2 uint32) (uint32, uint32) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FindMvpUid", arg0, arg1, arg2)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(uint32)
	return ret0, ret1
}

// FindMvpUid indicates an expected call of FindMvpUid.
func (mr *MockIChannelCpGameMgrMockRecorder) FindMvpUid(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindMvpUid", reflect.TypeOf((*MockIChannelCpGameMgr)(nil).FindMvpUid), arg0, arg1, arg2)
}

// FindPunishRank mocks base method.
func (m *MockIChannelCpGameMgr) FindPunishRank(arg0 uint32) int {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FindPunishRank", arg0)
	ret0, _ := ret[0].(int)
	return ret0
}

// FindPunishRank indicates an expected call of FindPunishRank.
func (mr *MockIChannelCpGameMgrMockRecorder) FindPunishRank(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindPunishRank", reflect.TypeOf((*MockIChannelCpGameMgr)(nil).FindPunishRank), arg0)
}

// FindPunishTeamId mocks base method.
func (m *MockIChannelCpGameMgr) FindPunishTeamId(arg0 *channel_cp_game.CpGameInfo) string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FindPunishTeamId", arg0)
	ret0, _ := ret[0].(string)
	return ret0
}

// FindPunishTeamId indicates an expected call of FindPunishTeamId.
func (mr *MockIChannelCpGameMgrMockRecorder) FindPunishTeamId(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindPunishTeamId", reflect.TypeOf((*MockIChannelCpGameMgr)(nil).FindPunishTeamId), arg0)
}

// FollowEachOther mocks base method.
func (m *MockIChannelCpGameMgr) FollowEachOther(arg0 context.Context, arg1, arg2 uint32) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "FollowEachOther", arg0, arg1, arg2)
}

// FollowEachOther indicates an expected call of FollowEachOther.
func (mr *MockIChannelCpGameMgrMockRecorder) FollowEachOther(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FollowEachOther", reflect.TypeOf((*MockIChannelCpGameMgr)(nil).FollowEachOther), arg0, arg1, arg2)
}

// FollowUser mocks base method.
func (m *MockIChannelCpGameMgr) FollowUser(arg0 context.Context, arg1, arg2 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FollowUser", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// FollowUser indicates an expected call of FollowUser.
func (mr *MockIChannelCpGameMgrMockRecorder) FollowUser(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FollowUser", reflect.TypeOf((*MockIChannelCpGameMgr)(nil).FollowUser), arg0, arg1, arg2)
}

// GetAllCpCardConf mocks base method.
func (m *MockIChannelCpGameMgr) GetAllCpCardConf(arg0 context.Context) ([]*channel_cp_game.CpCardConf, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllCpCardConf", arg0)
	ret0, _ := ret[0].([]*channel_cp_game.CpCardConf)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllCpCardConf indicates an expected call of GetAllCpCardConf.
func (mr *MockIChannelCpGameMgrMockRecorder) GetAllCpCardConf(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllCpCardConf", reflect.TypeOf((*MockIChannelCpGameMgr)(nil).GetAllCpCardConf), arg0)
}

// GetBreakingContent mocks base method.
func (m *MockIChannelCpGameMgr) GetBreakingContent(arg0, arg1 uint32) (string, string) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBreakingContent", arg0, arg1)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(string)
	return ret0, ret1
}

// GetBreakingContent indicates an expected call of GetBreakingContent.
func (mr *MockIChannelCpGameMgrMockRecorder) GetBreakingContent(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBreakingContent", reflect.TypeOf((*MockIChannelCpGameMgr)(nil).GetBreakingContent), arg0, arg1)
}

// GetChannelCpGameEntryLevel mocks base method.
func (m *MockIChannelCpGameMgr) GetChannelCpGameEntryLevel(arg0 context.Context, arg1 uint32) (uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChannelCpGameEntryLevel", arg0, arg1)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChannelCpGameEntryLevel indicates an expected call of GetChannelCpGameEntryLevel.
func (mr *MockIChannelCpGameMgrMockRecorder) GetChannelCpGameEntryLevel(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChannelCpGameEntryLevel", reflect.TypeOf((*MockIChannelCpGameMgr)(nil).GetChannelCpGameEntryLevel), arg0, arg1)
}

// GetCpGameGodRankList mocks base method.
func (m *MockIChannelCpGameMgr) GetCpGameGodRankList(arg0 context.Context, arg1 uint32) ([]*channel_cp_game.CpScoreInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCpGameGodRankList", arg0, arg1)
	ret0, _ := ret[0].([]*channel_cp_game.CpScoreInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCpGameGodRankList indicates an expected call of GetCpGameGodRankList.
func (mr *MockIChannelCpGameMgrMockRecorder) GetCpGameGodRankList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCpGameGodRankList", reflect.TypeOf((*MockIChannelCpGameMgr)(nil).GetCpGameGodRankList), arg0, arg1)
}

// GetCpGameInfoWhenCreateTeam mocks base method.
func (m *MockIChannelCpGameMgr) GetCpGameInfoWhenCreateTeam(arg0 context.Context, arg1, arg2 uint32) (*channel_cp_game.CpGameInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCpGameInfoWhenCreateTeam", arg0, arg1, arg2)
	ret0, _ := ret[0].(*channel_cp_game.CpGameInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCpGameInfoWhenCreateTeam indicates an expected call of GetCpGameInfoWhenCreateTeam.
func (mr *MockIChannelCpGameMgrMockRecorder) GetCpGameInfoWhenCreateTeam(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCpGameInfoWhenCreateTeam", reflect.TypeOf((*MockIChannelCpGameMgr)(nil).GetCpGameInfoWhenCreateTeam), arg0, arg1, arg2)
}

// GetCpGameInfoWhenPk mocks base method.
func (m *MockIChannelCpGameMgr) GetCpGameInfoWhenPk(arg0 context.Context, arg1, arg2 uint32) (*channel_cp_game.CpGameInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCpGameInfoWhenPk", arg0, arg1, arg2)
	ret0, _ := ret[0].(*channel_cp_game.CpGameInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCpGameInfoWhenPk indicates an expected call of GetCpGameInfoWhenPk.
func (mr *MockIChannelCpGameMgrMockRecorder) GetCpGameInfoWhenPk(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCpGameInfoWhenPk", reflect.TypeOf((*MockIChannelCpGameMgr)(nil).GetCpGameInfoWhenPk), arg0, arg1, arg2)
}

// GetCpGameInfoWhenPunish mocks base method.
func (m *MockIChannelCpGameMgr) GetCpGameInfoWhenPunish(arg0 context.Context, arg1, arg2 uint32) (*channel_cp_game.CpGameInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCpGameInfoWhenPunish", arg0, arg1, arg2)
	ret0, _ := ret[0].(*channel_cp_game.CpGameInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCpGameInfoWhenPunish indicates an expected call of GetCpGameInfoWhenPunish.
func (mr *MockIChannelCpGameMgrMockRecorder) GetCpGameInfoWhenPunish(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCpGameInfoWhenPunish", reflect.TypeOf((*MockIChannelCpGameMgr)(nil).GetCpGameInfoWhenPunish), arg0, arg1, arg2)
}

// GetCpGameMvpCharmVal mocks base method.
func (m *MockIChannelCpGameMgr) GetCpGameMvpCharmVal(arg0 context.Context, arg1, arg2, arg3 uint32) (uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCpGameMvpCharmVal", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCpGameMvpCharmVal indicates an expected call of GetCpGameMvpCharmVal.
func (mr *MockIChannelCpGameMgrMockRecorder) GetCpGameMvpCharmVal(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCpGameMvpCharmVal", reflect.TypeOf((*MockIChannelCpGameMgr)(nil).GetCpGameMvpCharmVal), arg0, arg1, arg2, arg3)
}

// GetCpGameMvpMic2Uid mocks base method.
func (m *MockIChannelCpGameMgr) GetCpGameMvpMic2Uid(arg0 context.Context, arg1 uint32) (uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCpGameMvpMic2Uid", arg0, arg1)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCpGameMvpMic2Uid indicates an expected call of GetCpGameMvpMic2Uid.
func (mr *MockIChannelCpGameMgrMockRecorder) GetCpGameMvpMic2Uid(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCpGameMvpMic2Uid", reflect.TypeOf((*MockIChannelCpGameMgr)(nil).GetCpGameMvpMic2Uid), arg0, arg1)
}

// GetCurrCpGameCacheInfo mocks base method.
func (m *MockIChannelCpGameMgr) GetCurrCpGameCacheInfo(arg0 context.Context, arg1 uint32) (*channel_cp_game.CpGameInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCurrCpGameCacheInfo", arg0, arg1)
	ret0, _ := ret[0].(*channel_cp_game.CpGameInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCurrCpGameCacheInfo indicates an expected call of GetCurrCpGameCacheInfo.
func (mr *MockIChannelCpGameMgrMockRecorder) GetCurrCpGameCacheInfo(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCurrCpGameCacheInfo", reflect.TypeOf((*MockIChannelCpGameMgr)(nil).GetCurrCpGameCacheInfo), arg0, arg1)
}

// GetCurrCpGameInfo mocks base method.
func (m *MockIChannelCpGameMgr) GetCurrCpGameInfo(arg0 context.Context, arg1 uint32) (*channel_cp_game.CpGameInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCurrCpGameInfo", arg0, arg1)
	ret0, _ := ret[0].(*channel_cp_game.CpGameInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCurrCpGameInfo indicates an expected call of GetCurrCpGameInfo.
func (mr *MockIChannelCpGameMgrMockRecorder) GetCurrCpGameInfo(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCurrCpGameInfo", reflect.TypeOf((*MockIChannelCpGameMgr)(nil).GetCurrCpGameInfo), arg0, arg1)
}

// GetInPlayerMicUid mocks base method.
func (m *MockIChannelCpGameMgr) GetInPlayerMicUid(arg0 context.Context, arg1 uint32) ([]uint32, map[uint32]uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetInPlayerMicUid", arg0, arg1)
	ret0, _ := ret[0].([]uint32)
	ret1, _ := ret[1].(map[uint32]uint32)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetInPlayerMicUid indicates an expected call of GetInPlayerMicUid.
func (mr *MockIChannelCpGameMgrMockRecorder) GetInPlayerMicUid(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetInPlayerMicUid", reflect.TypeOf((*MockIChannelCpGameMgr)(nil).GetInPlayerMicUid), arg0, arg1)
}

// GetNameplateInfo mocks base method.
func (m *MockIChannelCpGameMgr) GetNameplateInfo(arg0 context.Context, arg1, arg2 uint32) (*channel_cp_game.GetNameplateInfoResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetNameplateInfo", arg0, arg1, arg2)
	ret0, _ := ret[0].(*channel_cp_game.GetNameplateInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNameplateInfo indicates an expected call of GetNameplateInfo.
func (mr *MockIChannelCpGameMgrMockRecorder) GetNameplateInfo(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNameplateInfo", reflect.TypeOf((*MockIChannelCpGameMgr)(nil).GetNameplateInfo), arg0, arg1, arg2)
}

// GetReasonByType mocks base method.
func (m *MockIChannelCpGameMgr) GetReasonByType(arg0 uint32, arg1 int32) (string, int32) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetReasonByType", arg0, arg1)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(int32)
	return ret0, ret1
}

// GetReasonByType indicates an expected call of GetReasonByType.
func (mr *MockIChannelCpGameMgrMockRecorder) GetReasonByType(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetReasonByType", reflect.TypeOf((*MockIChannelCpGameMgr)(nil).GetReasonByType), arg0, arg1)
}

// GetStrength mocks base method.
func (m *MockIChannelCpGameMgr) GetStrength(arg0 context.Context, arg1, arg2 uint32) (uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetStrength", arg0, arg1, arg2)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetStrength indicates an expected call of GetStrength.
func (mr *MockIChannelCpGameMgrMockRecorder) GetStrength(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetStrength", reflect.TypeOf((*MockIChannelCpGameMgr)(nil).GetStrength), arg0, arg1, arg2)
}

// HandleCpGamePkEnd mocks base method.
func (m *MockIChannelCpGameMgr) HandleCpGamePkEnd() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "HandleCpGamePkEnd")
}

// HandleCpGamePkEnd indicates an expected call of HandleCpGamePkEnd.
func (mr *MockIChannelCpGameMgrMockRecorder) HandleCpGamePkEnd() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HandleCpGamePkEnd", reflect.TypeOf((*MockIChannelCpGameMgr)(nil).HandleCpGamePkEnd))
}

// HandleCpGameScoreUpdate mocks base method.
func (m *MockIChannelCpGameMgr) HandleCpGameScoreUpdate() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "HandleCpGameScoreUpdate")
}

// HandleCpGameScoreUpdate indicates an expected call of HandleCpGameScoreUpdate.
func (mr *MockIChannelCpGameMgrMockRecorder) HandleCpGameScoreUpdate() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HandleCpGameScoreUpdate", reflect.TypeOf((*MockIChannelCpGameMgr)(nil).HandleCpGameScoreUpdate))
}

// HandleEnterChannelEvent mocks base method.
func (m *MockIChannelCpGameMgr) HandleEnterChannelEvent(arg0 *kafkachannalevent.ChSimpleEvent) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "HandleEnterChannelEvent", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// HandleEnterChannelEvent indicates an expected call of HandleEnterChannelEvent.
func (mr *MockIChannelCpGameMgrMockRecorder) HandleEnterChannelEvent(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HandleEnterChannelEvent", reflect.TypeOf((*MockIChannelCpGameMgr)(nil).HandleEnterChannelEvent), arg0)
}

// HandleMicEvent mocks base method.
func (m *MockIChannelCpGameMgr) HandleMicEvent(arg0 *kafkasimplemic.SimpleMicEvent) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "HandleMicEvent", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// HandleMicEvent indicates an expected call of HandleMicEvent.
func (mr *MockIChannelCpGameMgrMockRecorder) HandleMicEvent(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HandleMicEvent", reflect.TypeOf((*MockIChannelCpGameMgr)(nil).HandleMicEvent), arg0)
}

// HandlePresentEvent mocks base method.
func (m *MockIChannelCpGameMgr) HandlePresentEvent(arg0 *kafkapresent.PresentEvent) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "HandlePresentEvent", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// HandlePresentEvent indicates an expected call of HandlePresentEvent.
func (mr *MockIChannelCpGameMgrMockRecorder) HandlePresentEvent(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HandlePresentEvent", reflect.TypeOf((*MockIChannelCpGameMgr)(nil).HandlePresentEvent), arg0)
}

// HandleQuitChannelEvent mocks base method.
func (m *MockIChannelCpGameMgr) HandleQuitChannelEvent(arg0 *kafkachannalevent.ChSimpleEvent) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "HandleQuitChannelEvent", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// HandleQuitChannelEvent indicates an expected call of HandleQuitChannelEvent.
func (mr *MockIChannelCpGameMgrMockRecorder) HandleQuitChannelEvent(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HandleQuitChannelEvent", reflect.TypeOf((*MockIChannelCpGameMgr)(nil).HandleQuitChannelEvent), arg0)
}

// KickoutChannelMic mocks base method.
func (m *MockIChannelCpGameMgr) KickoutChannelMic(arg0 context.Context, arg1, arg2, arg3 uint32, arg4 []uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "KickoutChannelMic", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(error)
	return ret0
}

// KickoutChannelMic indicates an expected call of KickoutChannelMic.
func (mr *MockIChannelCpGameMgrMockRecorder) KickoutChannelMic(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "KickoutChannelMic", reflect.TypeOf((*MockIChannelCpGameMgr)(nil).KickoutChannelMic), arg0, arg1, arg2, arg3, arg4)
}

// PushBreakingNews mocks base method.
func (m *MockIChannelCpGameMgr) PushBreakingNews(arg0 context.Context, arg1, arg2, arg3, arg4 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PushBreakingNews", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(error)
	return ret0
}

// PushBreakingNews indicates an expected call of PushBreakingNews.
func (mr *MockIChannelCpGameMgrMockRecorder) PushBreakingNews(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PushBreakingNews", reflect.TypeOf((*MockIChannelCpGameMgr)(nil).PushBreakingNews), arg0, arg1, arg2, arg3, arg4)
}

// PushBreakingNewsV3 mocks base method.
func (m *MockIChannelCpGameMgr) PushBreakingNewsV3(arg0, arg1, arg2, arg3 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PushBreakingNewsV3", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// PushBreakingNewsV3 indicates an expected call of PushBreakingNewsV3.
func (mr *MockIChannelCpGameMgrMockRecorder) PushBreakingNewsV3(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PushBreakingNewsV3", reflect.TypeOf((*MockIChannelCpGameMgr)(nil).PushBreakingNewsV3), arg0, arg1, arg2, arg3)
}

// PushCpGameResult mocks base method.
func (m *MockIChannelCpGameMgr) PushCpGameResult(arg0 context.Context, arg1 uint32, arg2 *channel_cp_game.CpGameInfo, arg3 time.Time) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PushCpGameResult", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// PushCpGameResult indicates an expected call of PushCpGameResult.
func (mr *MockIChannelCpGameMgrMockRecorder) PushCpGameResult(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PushCpGameResult", reflect.TypeOf((*MockIChannelCpGameMgr)(nil).PushCpGameResult), arg0, arg1, arg2, arg3)
}

// ReloadCpGameGodRankList mocks base method.
func (m *MockIChannelCpGameMgr) ReloadCpGameGodRankList(arg0 context.Context, arg1 uint32) ([]*channel_cp_game.CpScoreInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ReloadCpGameGodRankList", arg0, arg1)
	ret0, _ := ret[0].([]*channel_cp_game.CpScoreInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ReloadCpGameGodRankList indicates an expected call of ReloadCpGameGodRankList.
func (mr *MockIChannelCpGameMgrMockRecorder) ReloadCpGameGodRankList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReloadCpGameGodRankList", reflect.TypeOf((*MockIChannelCpGameMgr)(nil).ReloadCpGameGodRankList), arg0, arg1)
}

// ReportCpGameResult mocks base method.
func (m *MockIChannelCpGameMgr) ReportCpGameResult(arg0 context.Context, arg1 *channel_cp_game.CpGameInfo, arg2, arg3 uint32) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "ReportCpGameResult", arg0, arg1, arg2, arg3)
}

// ReportCpGameResult indicates an expected call of ReportCpGameResult.
func (mr *MockIChannelCpGameMgrMockRecorder) ReportCpGameResult(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReportCpGameResult", reflect.TypeOf((*MockIChannelCpGameMgr)(nil).ReportCpGameResult), arg0, arg1, arg2, arg3)
}

// SetChannelCpGameEntryLevel mocks base method.
func (m *MockIChannelCpGameMgr) SetChannelCpGameEntryLevel(arg0 context.Context, arg1, arg2 uint32, arg3 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetChannelCpGameEntryLevel", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetChannelCpGameEntryLevel indicates an expected call of SetChannelCpGameEntryLevel.
func (mr *MockIChannelCpGameMgrMockRecorder) SetChannelCpGameEntryLevel(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetChannelCpGameEntryLevel", reflect.TypeOf((*MockIChannelCpGameMgr)(nil).SetChannelCpGameEntryLevel), arg0, arg1, arg2, arg3)
}

// SetChannelCpGamePhase mocks base method.
func (m *MockIChannelCpGameMgr) SetChannelCpGamePhase(arg0 context.Context, arg1, arg2, arg3 uint32, arg4 bool) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetChannelCpGamePhase", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetChannelCpGamePhase indicates an expected call of SetChannelCpGamePhase.
func (mr *MockIChannelCpGameMgrMockRecorder) SetChannelCpGamePhase(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetChannelCpGamePhase", reflect.TypeOf((*MockIChannelCpGameMgr)(nil).SetChannelCpGamePhase), arg0, arg1, arg2, arg3, arg4)
}

// SetPhasePreHandle mocks base method.
func (m *MockIChannelCpGameMgr) SetPhasePreHandle(arg0 context.Context, arg1, arg2, arg3, arg4 uint32) (string, bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetPhasePreHandle", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(bool)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// SetPhasePreHandle indicates an expected call of SetPhasePreHandle.
func (mr *MockIChannelCpGameMgrMockRecorder) SetPhasePreHandle(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetPhasePreHandle", reflect.TypeOf((*MockIChannelCpGameMgr)(nil).SetPhasePreHandle), arg0, arg1, arg2, arg3, arg4)
}

// ShutDown mocks base method.
func (m *MockIChannelCpGameMgr) ShutDown() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "ShutDown")
}

// ShutDown indicates an expected call of ShutDown.
func (mr *MockIChannelCpGameMgrMockRecorder) ShutDown() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ShutDown", reflect.TypeOf((*MockIChannelCpGameMgr)(nil).ShutDown))
}

// StartTimer mocks base method.
func (m *MockIChannelCpGameMgr) StartTimer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "StartTimer")
}

// StartTimer indicates an expected call of StartTimer.
func (mr *MockIChannelCpGameMgrMockRecorder) StartTimer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "StartTimer", reflect.TypeOf((*MockIChannelCpGameMgr)(nil).StartTimer))
}

// TestPushCpGameResult mocks base method.
func (m *MockIChannelCpGameMgr) TestPushCpGameResult(arg0 context.Context, arg1, arg2, arg3, arg4, arg5 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TestPushCpGameResult", arg0, arg1, arg2, arg3, arg4, arg5)
	ret0, _ := ret[0].(error)
	return ret0
}

// TestPushCpGameResult indicates an expected call of TestPushCpGameResult.
func (mr *MockIChannelCpGameMgrMockRecorder) TestPushCpGameResult(arg0, arg1, arg2, arg3, arg4, arg5 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TestPushCpGameResult", reflect.TypeOf((*MockIChannelCpGameMgr)(nil).TestPushCpGameResult), arg0, arg1, arg2, arg3, arg4, arg5)
}

// TimerHandle mocks base method.
func (m *MockIChannelCpGameMgr) TimerHandle(arg0 time.Duration, arg1 func()) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "TimerHandle", arg0, arg1)
}

// TimerHandle indicates an expected call of TimerHandle.
func (mr *MockIChannelCpGameMgrMockRecorder) TimerHandle(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TimerHandle", reflect.TypeOf((*MockIChannelCpGameMgr)(nil).TimerHandle), arg0, arg1)
}

// UpdateCpCardConf mocks base method.
func (m *MockIChannelCpGameMgr) UpdateCpCardConf(arg0 context.Context, arg1 *channel_cp_game.CpCardConf) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateCpCardConf", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateCpCardConf indicates an expected call of UpdateCpCardConf.
func (mr *MockIChannelCpGameMgrMockRecorder) UpdateCpCardConf(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateCpCardConf", reflect.TypeOf((*MockIChannelCpGameMgr)(nil).UpdateCpCardConf), arg0, arg1)
}

// UpdateCurrCpGameCacheInfo mocks base method.
func (m *MockIChannelCpGameMgr) UpdateCurrCpGameCacheInfo(arg0 context.Context, arg1 uint32, arg2 time.Duration, arg3 bool) (*channel_cp_game.CpGameInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateCurrCpGameCacheInfo", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*channel_cp_game.CpGameInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateCurrCpGameCacheInfo indicates an expected call of UpdateCurrCpGameCacheInfo.
func (mr *MockIChannelCpGameMgrMockRecorder) UpdateCurrCpGameCacheInfo(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateCurrCpGameCacheInfo", reflect.TypeOf((*MockIChannelCpGameMgr)(nil).UpdateCurrCpGameCacheInfo), arg0, arg1, arg2, arg3)
}
