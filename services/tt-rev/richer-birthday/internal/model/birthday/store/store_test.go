package store

import (
	"context"
	"fmt"
	mysqlConnect "gitlab.ttyuyin.com/avengers/tyr/core/middleware/mysql/connect"
	"testing"
	"time"
)

const (
	uid = 123
)

func getTestStore() *Store {
	db, err := mysqlConnect.NewClient(context.Background(), &mysqlConnect.MysqlConfig{
		Host:     "************",
		Port:     3306,
		Database: "appsvr",
		UserName: "godman",
		Password: "thegodofman",
	})
	if err != nil {
		panic(err)
	}
	store, err := NewStore(db, db)
	if err != nil {
		panic(err)
	}
	return store
}

func TestStore_UpsertRicherBirthday(t *testing.T) {
	store := getTestStore()
	err := store.UpsertRicherBirthday(context.Background(), 123, time.Date(1996, 2, 29, 0, 0, 0, 0, time.Local).Unix())
	if err != nil {
		fmt.Println(err)
		return
	}
	err = store.UpsertRicherBirthday(context.Background(), 2465920, time.Now().Add(time.Hour*24).Unix())
	fmt.Println(err)
}

func TestStore_GetRicherBirthday(t *testing.T) {
	store := getTestStore()
	birthday, err := store.GetRicherBirthday(context.Background(), 223)
	fmt.Println(birthday, err)
}

func TestStore_GetRecentBirthday(t *testing.T) {
	store := getTestStore()
	birthday, err := store.GetRecentBirthday(context.Background(), time.Date(2024, 12, 31, 0, 0, 0, 0, time.Local))
	fmt.Println(birthday, err, len(birthday))
}

func TestStore_BathGetRicherBirthdayInfo(t *testing.T) {
	store := getTestStore()
	birthday, err := store.BathGetRicherBirthdayInfo(context.Background(), []uint32{123, 2465920})
	fmt.Println(birthday, err)
}

func Test_parseBirthday(t *testing.T) {
	now := time.Now()
	type args struct {
		birthday uint32
		now      time.Time
	}
	tests := []struct {
		name string
		args args
		want int64
	}{
		{
			name: "01-01 当天",
			args: args{
				birthday: 101,
				now:      time.Date(now.Year(), 1, 1, 0, 0, 0, 0, time.Local),
			},
			want: time.Date(now.Year(), 1, 1, 0, 0, 0, 0, time.Local).Unix(),
		},
		{
			name: "02-10 号 明天生日",
			args: args{
				birthday: 210,
				now:      time.Date(now.Year(), 2, 9, 0, 0, 0, 0, time.Local),
			},
			want: time.Date(now.Year(), 2, 10, 0, 0, 0, 0, time.Local).Unix(),
		},
		{
			name: "02-10 号 昨天生日",
			args: args{
				birthday: 210,
				now:      time.Date(now.Year(), 2, 11, 0, 0, 0, 0, time.Local),
			},
			want: time.Date(now.Year()+1, 2, 10, 0, 0, 0, 0, time.Local).Unix(),
		},
		{
			name: "02-29 号 今年是闰年",
			args: args{
				birthday: 229,
				now:      time.Date(1996, 2, 28, 0, 0, 0, 0, time.Local),
			},
			want: time.Date(1996, 2, 29, 0, 0, 0, 0, time.Local).Unix(),
		},
		{
			name: "02-29 号 今年不是闰年",
			args: args{
				birthday: 229,
				now:      time.Date(1998, 2, 28, 0, 0, 0, 0, time.Local),
			},
			want: time.Date(2000, 2, 29, 0, 0, 0, 0, time.Local).Unix(),
		},
		{
			name: "02-29 号 不是闰年的世纪年",
			args: args{
				birthday: 229,
				now:      time.Date(1900, 2, 28, 0, 0, 0, 0, time.Local),
			},
			want: time.Date(1904, 2, 29, 0, 0, 0, 0, time.Local).Unix(),
		},
		{
			name: "04-31 号",
			args: args{
				birthday: 431,
				now:      time.Date(now.Year(), 4, 30, 0, 0, 0, 0, time.Local),
			},
			want: 0,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := parseBirthday(tt.args.birthday, tt.args.now); got != tt.want {
				t.Errorf("parseBirthday() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_formatBirthday(t *testing.T) {
	type args struct {
		birthday int64
	}
	tests := []struct {
		name string
		args args
		want uint32
	}{
		{
			name: "0",
			args: args{
				birthday: 0,
			},
			want: 0,
		},
		{
			name: "01-01",
			args: args{
				birthday: time.Date(time.Now().Year(), 1, 1, 0, 0, 0, 0, time.Local).Unix(),
			},
			want: 101,
		},
		{
			name: "12-31",
			args: args{
				birthday: time.Date(time.Now().Year(), 12, 31, 0, 0, 0, 0, time.Local).Unix(),
			},
			want: 1231,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := formatBirthday(tt.args.birthday); got != tt.want {
				t.Errorf("formatBirthday() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_getTodayAndTwoDaysLater(t *testing.T) {
	type args struct {
		now time.Time
	}
	tests := []struct {
		name  string
		args  args
		want  uint32
		want1 uint32
	}{
		{
			name: "01-01",
			args: args{
				now: time.Date(time.Now().Year(), 1, 1, 0, 0, 0, 0, time.Local),
			},
			want:  101,
			want1: 103,
		},
		{
			name: "12-31 跨年",
			args: args{
				now: time.Date(time.Now().Year(), 12, 31, 0, 0, 0, 0, time.Local),
			},
			want:  1231,
			want1: 102,
		},
		{
			name: "02-28 闰年",
			args: args{
				now: time.Date(1996, 2, 28, 0, 0, 0, 0, time.Local),
			},
			want:  228,
			want1: 301,
		},
		{
			name: "02-28 不是闰年,多加一天",
			args: args{
				now: time.Date(1998, 2, 28, 0, 0, 0, 0, time.Local),
			},
			want:  228,
			want1: 303,
		},
		{
			name: "02-29 闰年",
			args: args{
				now: time.Date(1996, 2, 29, 0, 0, 0, 0, time.Local),
			},
			want:  229,
			want1: 302,
		},
		{
			name: "03-31 普通月份跨月",
			args: args{
				now: time.Date(time.Now().Year(), 3, 31, 0, 0, 0, 0, time.Local),
			},
			want:  331,
			want1: 402,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, got1 := getTodayAndTwoDaysLater(tt.args.now)
			if got != tt.want {
				t.Errorf("getTodayAndTwoDaysLater() got = %v, want %v", got, tt.want)
			}
			if got1 != tt.want1 {
				t.Errorf("getTodayAndTwoDaysLater() got1 = %v, want %v", got1, tt.want1)
			}
		})
	}
}

func Test_isLeapYear(t *testing.T) {
	type args struct {
		now time.Time
	}
	tests := []struct {
		name string
		args args
		want bool
	}{
		{
			name: "1996 闰年",
			args: args{
				now: time.Date(1996, 1, 1, 0, 0, 0, 0, time.Local),
			},
			want: true,
		},
		{
			name: "1998 不是闰年",
			args: args{
				now: time.Date(1998, 1, 1, 0, 0, 0, 0, time.Local),
			},
			want: false,
		},
		{
			name: "1900 世纪年 不是闰年",
			args: args{
				now: time.Date(1900, 1, 1, 0, 0, 0, 0, time.Local),
			},
			want: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := isLeapYear(tt.args.now); got != tt.want {
				t.Errorf("isLeapYear() = %v, want %v", got, tt.want)
			}
		})
	}
}
