package pk_game

//go:generate quicksilver-cli test interface ../pk-game
//go:generate mockgen -destination=./mocks/pk_game.go -package=mocks golang.52tt.com/services/tt-rev/channel-gift-pk/internal/model/pk-game IMgr

import (
	"context"
	"fmt"
	"gitlab.ttyuyin.com/avengers/tyr/pkg/cluster/timer"
	"gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/mysql"
	"gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/redis"
	"gitlab.ttyuyin.com/tt-infra/tyr/log"
	"golang.52tt.com/pkg/bylink"
	"golang.52tt.com/pkg/protocol"
	logicPb "golang.52tt.com/protocol/app/channel_gift_pk"
	"golang.52tt.com/protocol/common/status"
	"golang.52tt.com/protocol/services/channel_gift_pk"
	"golang.52tt.com/services/tt-rev/channel-gift-pk/internal/conf"
	"golang.52tt.com/services/tt-rev/channel-gift-pk/internal/model/anti_corruption_layer"
	"golang.52tt.com/services/tt-rev/channel-gift-pk/internal/model/pk-game/cache"
	"golang.52tt.com/services/tt-rev/channel-gift-pk/internal/model/pk-game/store"
	"sync"
	"time"
)

// Mgr 主流程管理
type Mgr struct {
	store   store.IStore
	cache   cache.ICache
	bc      conf.IBusinessConfManager
	acLayer anti_corruption_layer.IMgr

	timerD   *timer.Timer
	wg       sync.WaitGroup
	shutDown chan struct{}
}

func NewMgr(db, roDb mysql.DBx, cacheClient redis.Cmdable, bc conf.IBusinessConfManager, acLayer anti_corruption_layer.IMgr) (*Mgr, error) {
	mysqlStore := store.NewStore(db, roDb)
	redisCli := cache.NewCache(cacheClient)

	m := &Mgr{
		store:    mysqlStore,
		cache:    redisCli,
		bc:       bc,
		acLayer:  acLayer,
		shutDown: make(chan struct{}),
	}

	err := m.setupTimer(context.Background())
	if err != nil {
		log.Errorf("NewMgr fail to setupTimer, err:%v", err)
		return nil, err
	}

	return m, nil
}

func (m *Mgr) Stop() {
	close(m.shutDown)
	_ = m.cache.Close()
	_ = m.store.Close()
}

// GetChannelGiftPkCfg 获取对战配置
func (m *Mgr) GetChannelGiftPkCfg(_ context.Context) (*channel_gift_pk.GiftPkCfg, error) {
	out := &channel_gift_pk.GiftPkCfg{
		PkLvGifts: make([]*channel_gift_pk.PkLvGiftCfg, 0),
		PkSprites: make([]*channel_gift_pk.PkSpriteCfg, 0),
	}

	for _, cfg := range m.bc.GetLevelGiftCfgList() {
		out.PkLvGifts = append(out.PkLvGifts, fillPbPkLvGiftCfg(cfg))
	}

	for _, cfg := range m.bc.GetSpriteCfgList() {
		out.PkSprites = append(out.PkSprites, &channel_gift_pk.PkSpriteCfg{
			Id:   cfg.SpriteId,
			Name: cfg.Name,
			Res:  fillPbChannelGiftPkResource(cfg.Res),
		})
	}

	out.CommonRes = fillPbChannelGiftPkResource(m.bc.GetCommonRes())
	return out, nil
}

// GetChannelGiftPkInfo 获取对战信息
func (m *Mgr) GetChannelGiftPkInfo(ctx context.Context, uid, cid uint32) (*channel_gift_pk.GiftPkInfo, error) {
	out := &channel_gift_pk.GiftPkInfo{
		Status:   uint32(channel_gift_pk.GiftPkStatus_GIFT_PK_STATUS_NOT_START),
		ServerMs: time.Now().UnixMilli(),
	}

	// 检查该用户是否已经发起对战
	userPkStatus, ok, err := m.cache.GetUserPkStatusInfo(ctx, uid, cid)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetChannelGiftPkInfo fail to GetUserPkStatusInfo, uid:%d, cid:%d, err:%v", uid, cid, err)
		return out, err
	}

	if !ok || userPkStatus == nil {
		return out, nil
	}

	return m.fillPbGiftPkInfo(userPkStatus), nil
}

// SponsorChannelGiftPk 发起对战
func (m *Mgr) SponsorChannelGiftPk(ctx context.Context, uid, cid, toUid, giftId, price uint32) (uint32, error) {
	if uid == 0 || cid == 0 || giftId == 0 || toUid == 0 || price == 0 {
		return 0, protocol.NewExactServerError(ctx, status.ErrRequestParamInvalid, "invalid params")
	}

	now := time.Now()

	// 检查该用户玩法次数限制
	dailyCnt, err := m.cache.GetDailyMatchCnt(ctx, uid, now.Day())
	if err != nil {
		log.ErrorWithCtx(ctx, "SponsorChannelGiftPk fail to GetDailyMatchCnt, uid:%d, err:%v", uid, err)
		return 0, err
	}
	if dailyCnt+1 > m.bc.GetUserDailyMatchLimit() {
		log.ErrorWithCtx(ctx, "SponsorChannelGiftPk daily match cnt limit, uid:%d", uid)
		return 0, protocol.NewExactServerError(ctx, status.ErrChannelGiftPkCannotMatch, "今天的对战次数已经用完啦~明天再来玩吧")
	}

	// 检查该用户是否已经发起对战
	userPkStatus, ok, err := m.cache.GetUserPkStatusInfo(ctx, uid, cid)
	if err != nil {
		log.ErrorWithCtx(ctx, "SponsorChannelGiftPk fail to GetUserPkStatusInfo, uid:%d, cid:%d, err:%v", uid, cid, err)
		return 0, err
	}
	if ok && userPkStatus.Status != uint32(channel_gift_pk.GiftPkStatus_GIFT_PK_STATUS_NOT_START) {
		log.ErrorWithCtx(ctx, "SponsorChannelGiftPk user has already sponsor pk, uid:%d, cid:%d", uid, cid)
		return 0, protocol.NewExactServerError(ctx, status.ErrChannelGiftPkCommonErr, "已有进行中的对战了")
	}

	// 支付T豆
	restBalance, orderId, err := m.payForSponsorPk(ctx, uid, cid, toUid, giftId, price, now.Unix())
	if err != nil {
		log.ErrorWithCtx(ctx, "SponsorChannelGiftPk fail to payForSponsorPk, uid:%d, cid:%d, giftId:%d, price:%d, err:%v",
			uid, cid, giftId, price, err)
		return 0, err
	}

	matchMaxDuration := time.Duration(m.bc.GetMatchMaxSec()) * time.Second
	statusInfo := &cache.UserPkStatus{
		MyInfo: &cache.MemInfo{
			Uid:   uid,
			Cid:   cid,
			ToUid: toUid,
		},
		GiftId:     giftId,
		OrderId:    orderId,
		Status:     uint32(channel_gift_pk.GiftPkStatus_GIFT_PK_STATUS_MATCHING),
		BeginTime:  now,
		ExpireTime: now.Add(matchMaxDuration),
	}

	// 加入匹配
	statusInfoTtl := matchMaxDuration + 5*time.Second
	err = m.cache.AddMatch(ctx, orderId, giftId, now.Unix(), statusInfo, statusInfoTtl)
	if err != nil {
		log.ErrorWithCtx(ctx, "SponsorChannelGiftPk fail to AddMatch, orderId:%s, giftId:%d, err:%v", orderId, giftId, err)
		return 0, err
	}

	// 推送通知
	err = m.acLayer.PushUserPkInfoChangeNotify(ctx, uid, cid, 0, m.fillPbGiftPkInfo(statusInfo), "")
	if err != nil {
		log.WarnWithCtx(ctx, "SponsorChannelGiftPk fail to PushUserPkInfoChangeNotify, uid:%d, cid:%d, err:%v", uid, cid, err)
	}

	return restBalance, nil
}

func (m *Mgr) payForSponsorPk(ctx context.Context, uid, cid, toUid, giftId, price uint32, outsideTs int64) (uint32, string, error) {
	outsideTime := time.Unix(outsideTs, 0)
	orderId := fmt.Sprintf("C_GIFT_PK_%d_%d", uid, outsideTs)

	order := &store.ConsumeRecord{
		OrderId:     orderId,
		Uid:         uid,
		ChannelId:   cid,
		ToUid:       toUid,
		GiftId:      giftId,
		Price:       price,
		OrderStatus: store.ConsumeOrderStatusFreezing,
		Ctime:       outsideTime,
	}

	// 先插入消费记录
	err := m.store.InsertConsumeRecord(ctx, order)
	if err != nil {
		log.ErrorWithCtx(ctx, "payForSponsorPk fail to InsertConsumeRecord, %+v, err:%v", order, err)
		return 0, orderId, err
	}

	// 冻结T豆
	restBalance, err := m.acLayer.PayFreeze(ctx, orderId, uid, giftId, price, outsideTime)
	if err != nil {
		log.ErrorWithCtx(ctx, "payForSponsorPk fail to PayFreeze, %+v, err:%v", order, err)
		return restBalance, orderId, err
	}

	return restBalance, orderId, nil
}

// CancelChannelGiftPkMatch 取消匹配
func (m *Mgr) CancelChannelGiftPkMatch(ctx context.Context, uid, cid uint32, matchTimeout bool) (bool, error) {
	// 检查该用户是否已经发起对战
	userPkStatus, ok, err := m.cache.GetUserPkStatusInfo(ctx, uid, cid)
	if err != nil {
		log.ErrorWithCtx(ctx, "CancelChannelGiftPkMatch fail to GetUserPkStatusInfo, uid:%d, cid:%d, err:%v", uid, cid, err)
		return false, err
	}
	if !ok {
		return false, nil
	}

	// 检查是否在匹配阶段
	if userPkStatus.Status != uint32(channel_gift_pk.GiftPkStatus_GIFT_PK_STATUS_MATCHING) {
		log.ErrorWithCtx(ctx, "CancelChannelGiftPkMatch not in matching status, uid:%d, cid:%d", uid, cid)
		return false, protocol.NewExactServerError(ctx, status.ErrChannelGiftPkCommonErr, "不在匹配中")
	}

	// 取消匹配
	uidType := m.cache.GetUidType(ctx, uid)
	rmCnt, err := m.cache.RemoveMatchOrders(ctx, userPkStatus.GiftId, uidType, []string{userPkStatus.OrderId})
	if err != nil {
		log.ErrorWithCtx(ctx, "CancelChannelGiftPkMatch fail to RemoveMatchOrders, uid:%d, cid:%d, err:%v", uid, cid, err)
		return false, err
	}

	if rmCnt == 0 {
		log.WarnWithCtx(ctx, "CancelChannelGiftPkMatch fail to RemoveMatchOrders, uid:%d, cid:%d, orderId:%s", uid, cid, userPkStatus.OrderId)
		return false, nil
	}

	matchResult := "cancel"
	if matchTimeout {
		matchResult = "fail"
	}
	matchDataReport(ctx, userPkStatus, userPkStatus.BeginTime, time.Now(), matchResult)

	userStatus := &cache.UserPkStatus{
		Status: uint32(channel_gift_pk.GiftPkStatus_GIFT_PK_STATUS_NOT_START),
	}
	err = m.cache.SetUserPkStatusInfo(ctx, uid, cid, userStatus, time.Minute)
	if err != nil {
		log.ErrorWithCtx(ctx, "CancelChannelGiftPkMatch fail to SetUserPkStatusInfo, uid:%d, cid:%d, err:%v", uid, cid, err)
		return false, err
	}

	err = m.payRollback(ctx, userPkStatus.OrderId, uid, userPkStatus.BeginTime, true)
	if err != nil {
		log.ErrorWithCtx(ctx, "CancelChannelGiftPkMatch fail to PayRollback, uid:%d, cid:%d, err:%v", uid, cid, err)
		return false, err
	}

	// 推送通知
	var changeReason uint32
	if matchTimeout {
		changeReason = uint32(logicPb.ChannelGiftPkInfoChangeOpt_CHANGE_REASON_MATCH_TIMEOUT)
	}
	err = m.acLayer.PushUserPkInfoChangeNotify(ctx, uid, cid, changeReason, m.fillPbGiftPkInfo(userStatus), "")
	if err != nil {
		log.WarnWithCtx(ctx, "CancelChannelGiftPkMatch fail to PushUserPkInfoChangeNotify, uid:%d, cid:%d, err:%v", uid, cid, err)
	}

	log.InfoWithCtx(ctx, "CancelChannelGiftPkMatch success. uid:%d, cid:%d", uid, cid)
	return true, nil
}

// ChooseChannelGiftPkSprite 选择精灵卡牌
func (m *Mgr) ChooseChannelGiftPkSprite(ctx context.Context, uid, cid, spriteId uint32, isPreselection bool) error {
	if uid == 0 || cid == 0 || spriteId == 0 || m.bc.GetSpriteCfgBySpriteId(spriteId) == nil {
		return protocol.NewExactServerError(ctx, status.ErrRequestParamInvalid, "invalid params")
	}

	// 检查该用户是否已经发起对战
	userPkStatus, ok, err := m.cache.GetUserPkStatusInfo(ctx, uid, cid)
	if err != nil {
		log.ErrorWithCtx(ctx, "ChooseChannelGiftPkSprite fail to GetUserPkStatusInfo, uid:%d, cid:%d, err:%v", uid, cid, err)
		return err
	}

	// 检查是否在选择精灵卡牌阶段
	if !ok || userPkStatus.Status != uint32(channel_gift_pk.GiftPkStatus_GIFT_PK_STATUS_CHOOSE_SPRITE) {
		log.ErrorWithCtx(ctx, "ChooseChannelGiftPkSprite not in processing status, uid:%d, cid:%d", uid, cid)
		return protocol.NewExactServerError(ctx, status.ErrChannelGiftPkCommonErr, "不在选择精灵卡牌阶段")
	}
	if time.Now().Unix() >= userPkStatus.ExpireTime.Unix() {
		log.ErrorWithCtx(ctx, "ChooseChannelGiftPkSprite expire time, uid:%d, cid:%d", uid, cid)
		return protocol.NewExactServerError(ctx, status.ErrChannelGiftPkCommonErr, "选择超时")
	}

	// 预选精灵
	if isPreselection {
		userPkStatus.PreSelectSpriteId = spriteId
		err = m.cache.SetUserPkStatusInfo(ctx, uid, cid, userPkStatus, time.Minute)
		if err != nil {
			log.ErrorWithCtx(ctx, "ChooseChannelGiftPkSprite fail to SetUserPkStatusInfo, uid:%d, cid:%d, err:%v", uid, cid, err)
			return err
		}

		log.InfoWithCtx(ctx, "ChooseChannelGiftPkSprite success. uid:%d, cid:%d, spriteId:%d, isPreselection:%v", uid, cid, spriteId, isPreselection)
		return nil
	}

	// 选择精灵卡牌
	err = m.store.Transaction(ctx, func(tx mysql.Txx) error {
		ok, err := m.store.UpdatePkInfo(ctx, tx, store.PkStatusSelectingSprite, &store.PkInfo{
			PkId:      userPkStatus.PkId,
			ChannelId: cid,
			Uid:       uid,
			Status:    store.PkStatusWaitResult,
			PkSprite:  spriteId,
		}, userPkStatus.BeginTime)
		if err != nil {
			log.ErrorWithCtx(ctx, "ChooseChannelGiftPkSprite fail to UpdatePkInfo, uid:%d, cid:%d, err:%v", uid, cid, err)
			return err
		}
		if !ok {
			log.ErrorWithCtx(ctx, "ChooseChannelGiftPkSprite fail to UpdatePkInfo, uid:%d, cid:%d", uid, cid)
			return protocol.NewExactServerError(ctx, status.ErrChannelGiftPkCommonErr, "不在选择精灵卡牌阶段")
		}

		userPkStatus.Status = uint32(channel_gift_pk.GiftPkStatus_GIFT_PK_STATUS_WAIT_PK)
		err = m.cache.SetUserPkStatusInfo(ctx, uid, cid, userPkStatus, time.Minute)
		if err != nil {
			log.ErrorWithCtx(ctx, "ChooseChannelGiftPkSprite fail to SetUserPkStatusInfo, uid:%d, cid:%d, err:%v", uid, cid, err)
			return err
		}

		return nil
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "ChooseChannelGiftPkSprite fail to Transaction, uid:%d, cid:%d, err:%v", uid, cid, err)
		return err
	}

	// 推送通知
	err = m.acLayer.PushUserPkInfoChangeNotify(ctx, uid, cid, 0, m.fillPbGiftPkInfo(userPkStatus), "")
	if err != nil {
		log.WarnWithCtx(ctx, "ChooseChannelGiftPkSprite fail to PushUserPkInfoChangeNotify, uid:%d, cid:%d, err:%v", uid, cid, err)
	}

	log.InfoWithCtx(ctx, "ChooseChannelGiftPkSprite success. uid:%d, cid:%d, spriteId:%d, isPreselection:%v", uid, cid, spriteId, isPreselection)
	return nil
}

// StartChannelGiftPk 开始对战
func (m *Mgr) startPk(ctx context.Context, orders []*cache.MatchPoolOrder) error {
	if len(orders) != 2 {
		return nil
	}

	consumeRecordList := make([]*store.ConsumeRecord, 0)
	for _, order := range orders {
		consume, exist, err := m.store.GetConsumeRecordByPayOrderId(ctx, time.Unix(order.Ts, 0), order.OrderId)
		if err != nil {
			log.ErrorWithCtx(ctx, "startPk fail to GetConsumeRecordByPayOrderId, order:%+v, err:%v", order, err)
			return err
		}

		if !exist {
			log.ErrorWithCtx(ctx, "startPk fail to GetConsumeRecordByPayOrderId, consume record not exist, order:%+v", order)
			return protocol.NewExactServerError(ctx, status.ErrChannelGiftPkOrderNotExist)
		}

		consumeRecordList = append(consumeRecordList, consume)
	}
	if len(consumeRecordList) != 2 {
		log.WarnWithCtx(ctx, "startPk consumeRecordList len not equal 2, consumeRecordList:%+v", consumeRecordList)
		return nil
	}

	pkId, err := m.cache.NewPkId(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "startPk fail to NewPkId, orders:%+v, err:%v", orders, err)
		return err
	}

	now := time.Now()
	pkDuration := time.Duration(m.bc.GetPkSec()) * time.Second
	endTime := now.Add(pkDuration)
	pkInfoList := make([]*store.PkInfo, 0)

	for _, consume := range consumeRecordList {
		// get lvCfg
		pkLevel := uint32(0)
		lvCfg := m.bc.GetLevelGiftCfgByGiftId(consume.GiftId)
		if lvCfg != nil {
			pkLevel = lvCfg.LevelId
		}
		pkInfo := &store.PkInfo{
			OrderId:     consume.OrderId,
			PkId:        pkId,
			PkLevel:     pkLevel,
			Uid:         consume.Uid,
			ChannelId:   consume.ChannelId,
			ToUid:       consume.ToUid,
			GiftId:      consume.GiftId,
			Price:       consume.Price,
			OutsideTime: now,
			EndTime:     endTime,
		}
		pkInfoList = append(pkInfoList, pkInfo)
	}

	err = m.store.Transaction(ctx, func(tx mysql.Txx) error {
		// 插入对战信息
		err = m.store.BatInsertPkInfos(ctx, tx, now, pkInfoList)
		if err != nil {
			log.ErrorWithCtx(ctx, "startPk fail to BatInsertPkInfos, pkInfoList:%+v, %+v, err:%v", pkInfoList[0], pkInfoList[1], err)
			return err
		}

		preStatus := []uint32{store.ConsumeOrderStatusFreezing}
		toStatus := store.ConsumeOrderStatusWaitCommit

		for _, consume := range consumeRecordList {
			// 修改消费记录状态
			ok, err := m.store.ChangeConsumeRecordStatus(ctx, tx, consume.OrderId, consume.Ctime, preStatus, toStatus)
			if err != nil {
				log.ErrorWithCtx(ctx, "startPk fail to ChangeConsumeRecordStatus, consume:%+v, err:%v", consume, err)
				return err
			}
			if !ok {
				log.WarnWithCtx(ctx, "startPk fail to ChangeConsumeRecordStatus, consume:%+v", consume)
				return protocol.NewExactServerError(ctx, status.ErrChannelGiftPkCommonErr, "change consume record status fail")
			}
		}

		return nil
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "startPk fail to Transaction, pkInfoList:%+v, %+v, err:%v", pkInfoList[0], pkInfoList[1], err)
		return err
	}

	// 更新双方对战状态
	for i, pkInfo := range pkInfoList {
		enemyInfo := pkInfoList[1-i]
		userStatus := &cache.UserPkStatus{
			Status:     uint32(channel_gift_pk.GiftPkStatus_GIFT_PK_STATUS_CHOOSE_SPRITE),
			GiftId:     pkInfo.GiftId,
			OrderId:    pkInfo.OrderId,
			BeginTime:  pkInfo.OutsideTime,
			ExpireTime: pkInfo.EndTime,
			PkId:       pkInfo.PkId,
			MyInfo: &cache.MemInfo{
				Uid:   pkInfo.Uid,
				Cid:   pkInfo.ChannelId,
				ToUid: pkInfo.ToUid,
			},
			EnemyInfo: &cache.MemInfo{
				Uid:   enemyInfo.Uid,
				Cid:   enemyInfo.ChannelId,
				ToUid: enemyInfo.ToUid,
			},
		}
		err = m.cache.SetUserPkStatusInfo(ctx, pkInfo.Uid, pkInfo.ChannelId, userStatus, pkDuration+time.Minute)
		if err != nil {
			log.WarnWithCtx(ctx, "startPk fail to SetUserPkStatusInfo, pkInfo:%+v, err:%v", pkInfo, err)
			continue
		}

		// 推送通知
		err = m.acLayer.PushUserPkInfoChangeNotify(ctx, pkInfo.Uid, pkInfo.ChannelId, 0, m.fillPbGiftPkInfo(userStatus), "")
		if err != nil {
			log.WarnWithCtx(ctx, "startPk fail to PushUserPkInfoChangeNotify, pkInfo:%+v, err:%v", pkInfo, err)
			continue
		}

		var matchBeginTime time.Time
		for _, order := range orders {
			if order.OrderId == userStatus.OrderId {
				matchBeginTime = time.Unix(order.Ts, 0)
				break
			}
		}
		matchDataReport(ctx, userStatus, matchBeginTime, now, "success")
	}

	log.InfoWithCtx(ctx, "startPk success. pkId:%d, pkInfo:%+v, pkInfo:%+v", pkId, pkInfoList[0], pkInfoList[1])
	return nil
}

func (m *Mgr) fillPbGiftPkInfo(userPkStatus *cache.UserPkStatus) *channel_gift_pk.GiftPkInfo {
	lvGiftCfg := m.bc.GetLevelGiftCfgByGiftId(userPkStatus.GiftId)
	out := &channel_gift_pk.GiftPkInfo{
		PkId:         userPkStatus.PkId,
		PkGift:       fillPbPkLvGiftCfg(lvGiftCfg),
		Status:       userPkStatus.Status,
		MyPkInfos:    fillPbGiftPkChannelInfo(userPkStatus.MyInfo),
		EnemyPkInfos: fillPbGiftPkChannelInfo(userPkStatus.EnemyInfo),
		StartTime:    userPkStatus.BeginTime.Unix(),
		EndTime:      userPkStatus.ExpireTime.Unix(),
		ServerMs:     time.Now().UnixMilli(),
	}

	return out
}

func fillPbPkLvGiftCfg(cfg *conf.LevelGiftCfg) *channel_gift_pk.PkLvGiftCfg {
	if cfg == nil {
		return nil
	}
	return &channel_gift_pk.PkLvGiftCfg{
		GiftId: cfg.GiftId,
		LvCfg: &channel_gift_pk.PkLevelCfg{
			PkLevelName: cfg.LevelName,
			PkLevel:     cfg.LevelId,
		},
	}
}

func fillPbChannelGiftPkResource(cfg *conf.ResourceCfg) *channel_gift_pk.ChannelGiftPkResource {
	if cfg == nil {
		return nil
	}
	return &channel_gift_pk.ChannelGiftPkResource{
		Url: cfg.Url,
		Md5: cfg.Md5,
	}
}

func fillPbGiftPkChannelInfo(info *cache.MemInfo) *channel_gift_pk.GiftPkChannelInfo {
	if info == nil {
		return nil
	}
	return &channel_gift_pk.GiftPkChannelInfo{
		SponsorUid: info.Uid,
		ChannelId:  info.Cid,
		AnchorUid:  info.ToUid,
	}
}

func matchDataReport(ctx context.Context, info *cache.UserPkStatus, start, end time.Time, result string) {
	if info == nil || info.MyInfo == nil {
		return
	}

	myInfo := info.MyInfo
	data := map[string]interface{}{
		"uid":        myInfo.Uid,
		"room_id":    myInfo.Cid,
		"target_uid": myInfo.ToUid,
		"round_id":   info.PkId,
		"item_id":    info.GiftId,
		"start_time": start.Format("2006-01-02 15:04:05"),
		"end_time":   end.Format("2006-01-02 15:04:05"),
		"result":     result,
	}

	byErr := bylink.Track(ctx, uint64(myInfo.Uid), "gift_pk_match_log", data, false)
	if byErr != nil {
		log.Errorf("Fail to bylink.Track error: %v", byErr)
	}
	bylink.Flush()
}
