package store

import (
	"context"
	"fmt"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"golang.52tt.com/pkg/log"
	"time"
)

type RewardRecord struct {
	Id         int64     `bson:"_id"`         // 记录id
	Uid        uint32    `bson:"uid"`         // 发奖用户
	GodId      string    `bson:"god_id"`      // 财神id
	BoxId      uint32    `bson:"box_id"`      // 宝箱配置id
	RewardId   uint32    `bson:"reward_id"`   // 奖励配置id
	RewardType uint32    `bson:"reward_type"` // 奖励类型
	ItemId     string    `bson:"item_id"`     // 奖品id
	ItemName   string    `bson:"item_name"`   // 奖品名称
	TbeanValue uint64    `bson:"tbean_value"` // T豆价值
	OrderId    string    `bson:"order_id"`    // 订单id
	Status     uint32    `bson:"status"`      // 状态
	CreateAt   time.Time `bson:"create_at"`   // 创建时间
	UpdateAt   time.Time `bson:"update_at"`   // 更新时间
}

func (r *RewardRecord) GenOrderId() string {
	// 截取GodId后6位
	return fmt.Sprintf("csjl_%d_%s_%d", r.Uid, r.GodId[len(r.GodId)-6:], r.Id)
}

const (
	RewardRecordStatusInit     = 0 // 初始化
	RewardRecordStatusRewarded = 1 // 已发奖
)

func (s *Store) ensureIndex4RewardRecord(ctx context.Context) {
	err := s.rewardColl.CreateIndexes(ctx, []mongo.IndexModel{
		{
			Keys: bson.D{
				{Key: "god_id", Value: 1},
			},
		},
		{
			Keys: bson.D{
				{Key: "uid", Value: 1},
				{Key: "create_at", Value: -1},
			},
		},
		{
			Keys: bson.D{
				{Key: "status", Value: 1},
				{Key: "reward_type", Value: 1},
				{Key: "_id", Value: 1},
			},
		},
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "ensureIndex4RewardRecord err: %v", err)
	}
}

func (s *Store) InsertRewardRecord(ctx context.Context, record *RewardRecord) error {
	_, err := s.rewardColl.InsertOne(ctx, record)
	return err
}

func (s *Store) UpdateRewardRecordStatus(ctx context.Context, id int64, newStatus uint32) error {
	filter := bson.M{"_id": id}
	update := bson.M{"$set": bson.M{"status": newStatus, "update_at": time.Now()}}
	_, err := s.rewardColl.Collection.UpdateOne(ctx, filter, update)
	return err
}

func (s *Store) GetRewardRecord(ctx context.Context, id int64) (*RewardRecord, error) {
	filter := bson.M{"_id": id}
	var record RewardRecord
	err := s.rewardColl.Collection.FindOne(ctx, filter).Decode(&record)
	if err != nil {
		return nil, err
	}
	return &record, nil
}

func (s *Store) GetUserRewardRecords(ctx context.Context, uid uint32, limit, offset int64) ([]*RewardRecord, error) {
	filter := bson.M{"uid": uid}
	opts := options.Find().SetLimit(limit).SetSkip(offset).SetSort(bson.M{"create_at": -1})

	cursor, err := s.rewardColl.Collection.Find(ctx, filter, opts)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	var records []*RewardRecord
	for cursor.Next(ctx) {
		var record RewardRecord
		if err := cursor.Decode(&record); err != nil {
			return nil, err
		}
		records = append(records, &record)
	}
	return records, nil
}

func (s *Store) GetUnDeliverRewardRecords(ctx context.Context, rewardType uint32, minId, limit int64) (records []*RewardRecord, nextId int64, hasMore bool, err error) {
	filter := bson.M{
		"_id":         bson.M{"$gt": minId},
		"status":      RewardRecordStatusInit,
		"reward_type": rewardType,
	}
	opts := options.Find().SetLimit(limit).SetSort(bson.M{"_id": 1})

	cursor, err := s.rewardColl.Collection.Find(ctx, filter, opts)
	if err != nil {
		return
	}
	defer cursor.Close(ctx)

	for cursor.Next(ctx) {
		var record RewardRecord
		if err = cursor.Decode(&record); err != nil {
			return
		}
		records = append(records, &record)
	}

	if len(records) > int(limit) {
		nextId = records[len(records)-1].Id
		hasMore = true
	}
	return
}

type GodRewardStat struct {
	UidUv         int64 `bson:"uid_uv"`
	TbeanValueSum int64 `bson:"tbean_value_sum"`
}

func (s *Store) GetGodRewardStat(ctx context.Context, godId string) (*GodRewardStat, error) {
	pipeline := mongo.Pipeline{
		{{
			Key: "$match",
			Value: bson.M{
				"god_id": godId,
			},
		}},
		{{
			Key: "$group",
			Value: bson.M{
				"_id":             nil,
				"uid_set":          bson.M{"$addToSet": "$uid"},
				"tbean_value_sum": bson.M{"$sum": "$tbean_value"},
			},
		}},
		{{
			Key: "$project",
			Value: bson.M{
				"uid_uv":          bson.M{"$size": "$uid_set"},
				"tbean_value_sum": 1,
			},
		}},
	}

	cursor, err := s.rewardColl.Collection.Aggregate(ctx, pipeline)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	if cursor.Next(ctx) {
		var result GodRewardStat
		if err = cursor.Decode(&result); err != nil {
			return nil, err
		}
		return &result, nil
	}
	return &GodRewardStat{}, nil
}

type TimeRewardStat struct {
	UidUv         int64 `bson:"uid_uv"`
	UidPv         int64 `bson:"uid_pv"`
	TbeanValueSum int64 `bson:"tbean_value_sum"`
}

func (s *Store) GetTimeRewardStat(ctx context.Context, startTime, endTime time.Time) (*TimeRewardStat, error) {
	pipeline := mongo.Pipeline{
		{{
			Key: "$match",
			Value: bson.M{
				"create_at": bson.M{
					"$gte": startTime,
					"$lt":  endTime,
				},
			},
		}},
		{{
			Key: "$group",
			Value: bson.M{
				"_id":             nil,
				"uid_set":         bson.M{"$addToSet": "$uid"},
				"uid_pv":          bson.M{"$sum": 1},
				"tbean_value_sum": bson.M{"$sum": "$tbean_value"},
			},
		}},
		{{
			Key: "$project",
			Value: bson.M{
				"uid_uv":          bson.M{"$size": "$uid_set"},
				"uid_pv":          1,
				"tbean_value_sum": 1,
			},
		}},
	}

	cursor, err := s.rewardColl.Collection.Aggregate(ctx, pipeline)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	if cursor.Next(ctx) {
		var result TimeRewardStat
		if err = cursor.Decode(&result); err != nil {
			return nil, err
		}
		return &result, nil
	}
	return &TimeRewardStat{}, nil
}

type SimpleRewardStat struct {
	TbeanValueSum int64 `bson:"tbean_value_sum"`
}

func (s *Store) GetSimpleRewardStat(ctx context.Context, startTime, endTime time.Time) (*SimpleRewardStat, error) {
	pipeline := mongo.Pipeline{
		{{
			Key: "$match",
			Value: bson.M{
				"create_at": bson.M{
					"$gte": startTime,
					"$lt":  endTime,
				},
			},
		}},
		{{
			Key: "$group",
			Value: bson.M{
				"_id":             nil,
				"tbean_value_sum": bson.M{"$sum": "$tbean_value"},
			},
		}},
		{{
			Key: "$project",
			Value: bson.M{
				"tbean_value_sum": 1,
			},
		}},
	}

	cursor, err := s.rewardColl.Collection.Aggregate(ctx, pipeline)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	if cursor.Next(ctx) {
		var result SimpleRewardStat
		if err = cursor.Decode(&result); err != nil {
			return nil, err
		}
		return &result, nil
	}
	return &SimpleRewardStat{}, nil
}
