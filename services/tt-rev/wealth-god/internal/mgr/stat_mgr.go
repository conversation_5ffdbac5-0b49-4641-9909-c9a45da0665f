package mgr

import (
	"context"
	"fmt"
	"golang.52tt.com/pkg/log"
	pb "golang.52tt.com/protocol/services/wealth-god"
	"golang.52tt.com/services/tt-rev/wealth-god/internal/config"
	"golang.52tt.com/services/tt-rev/wealth-god/internal/store"
	"time"
)

func (m *Mgr) handleHourReport(ctx context.Context) {
	ctx, cancel := context.WithTimeout(context.Background(), time.Minute*3)
	defer cancel()
	now := time.Now()

	endTime := time.Date(now.Year(), now.Month(), now.Day(), now.Hour(), 0, 0, 0, time.Local)
	startTime := endTime.Add(-time.Hour)
	m.handleTimeReport(ctx, startTime, endTime, false)
}

func (m *Mgr) handleDayReport(ctx context.Context) {
	ctx, cancel := context.WithTimeout(context.Background(), time.Minute*3)
	defer cancel()
	now := time.Now()

	endTime := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, time.Local)
	startTime := endTime.Add(-time.Hour * 24)
	m.handleTimeReport(ctx, startTime, endTime, true)
}

func (m *Mgr) handleTimeReport(ctx context.Context, startTime, endTime time.Time, isDayReport bool) {
	title := "按小时播报"
	timeStr := fmt.Sprintf("%s-%s", startTime.Format("2006年01月02日 15:04"), endTime.Format("15:04"))
	if isDayReport {
		title = "按天播报"
		timeStr = fmt.Sprintf("%s", startTime.Format("2006年01月02日"))
	}

	boxStatList, err := m.store.GetBoxStat(ctx, startTime, endTime)
	if err != nil {
		log.ErrorWithCtx(ctx, "handleTimeReport GetBoxStat err: %v", err)
		return
	}

	timeRewardStat, err := m.store.GetTimeRewardStat(ctx, startTime, endTime)
	if err != nil {
		log.ErrorWithCtx(ctx, "handleTimeReport GetTimeRewardStat err: %v", err)
		return
	}

	dayStartTime := time.Date(startTime.Year(), startTime.Month(), startTime.Day(), 0, 0, 0, 0, time.Local)
	dayRewardStat, err := m.store.GetSimpleRewardStat(ctx, dayStartTime, endTime)
	if err != nil {
		log.ErrorWithCtx(ctx, "handleTimeReport get dayRewardStat err: %v", err)
		return
	}

	allStartTime := time.Unix(config.GetDynamicConfig().ActivityStartTs, 0)
	allRewardStat, err := m.store.GetSimpleRewardStat(ctx, allStartTime, endTime)
	if err != nil {
		log.ErrorWithCtx(ctx, "handleTimeReport get allRewardStat err: %v", err)
		return
	}

	lines := []string{
		fmt.Sprintf("时间段: %s", timeStr),
		fmt.Sprintf("本时段领奖人数uv: %d", timeRewardStat.UidUv),
		fmt.Sprintf("本时段领奖人数pv: %d", timeRewardStat.UidPv),
		fmt.Sprintf("本时段发放T豆礼物金额: %.2f", float64(timeRewardStat.TbeanValueSum)/100),
		fmt.Sprintf("今日发放T豆礼物金额: %.2f", float64(dayRewardStat.TbeanValueSum)/100),
		fmt.Sprintf("活动发放T豆礼物金额: %.2f", float64(allRewardStat.TbeanValueSum)/100),
	}
	for _, boxStat := range boxStatList {
		lines = append(lines, fmt.Sprintf("宝箱等级: %s, 宝箱个数: %d, 触发宝箱用户uv: %d", getBoxTypeDesc(boxStat.BoxType), boxStat.BoxPv, boxStat.UidUv))
	}

	m.sendFeishuMsg(title, lines, "")
}

func (m *Mgr) handleGodReport(ctx context.Context, godInfo *store.WealthGodRecord) {
	// 查询触发用户信息
	userInfo, err := m.rpcCli.ProfileCli.GetUserProfileV2(ctx, godInfo.Uid, false)
	if err != nil {
		log.ErrorWithCtx(ctx, "handleGodReport GetUserProfileV2 err: %v", err)
		return
	}
	// 查询触发房间信息
	channelInfo, err := m.rpcCli.ChannelCli.GetChannelDetailInfo(ctx, godInfo.Uid, godInfo.Cid)
	if err != nil {
		log.ErrorWithCtx(ctx, "handleGodReport GetChannelDetailInfo err: %v", err)
		return
	}
	// 查询统计信息
	godRewardStat, err1 := m.store.GetGodRewardStat(ctx, godInfo.GodId)
	if err1 != nil {
		log.ErrorWithCtx(ctx, "handleGodReport GetGodRewardStat err: %v", err1)
		return
	}

	lines := []string{
		fmt.Sprintf("财神id: %s", godInfo.GodId),
		fmt.Sprintf("财神触发时间: %s", godInfo.CreateAt.In(time.Local).Format("2006年01月02日 15:04:03 ")),
		fmt.Sprintf("宝箱等级: %s", getBoxTypeDesc(godInfo.BoxType)),
		fmt.Sprintf("触发用户uid: %d", userInfo.Uid),
		fmt.Sprintf("触发用户ttid: %s", userInfo.AccountAlias),
		fmt.Sprintf("触发用户昵称: %s", userInfo.Nickname),
		fmt.Sprintf("触发房间cid: %d", godInfo.Cid),
		fmt.Sprintf("触发房间display_id: %d", channelInfo.GetChannelBaseinfo().GetDisplayId()),
		fmt.Sprintf("领奖人数: %d", godRewardStat.UidUv),
		fmt.Sprintf("T豆礼物金额: %.2f", float64(godRewardStat.TbeanValueSum)/100),
	}
	m.sendFeishuMsg("场次播报", lines, "")
}

func getBoxTypeDesc(boxType uint32) string {
	switch boxType {
	case uint32(pb.WealthGodBoxType_WEALTH_GOD_BOX_TYPE_S):
		return "S"
	case uint32(pb.WealthGodBoxType_WEALTH_GOD_BOX_TYPE_A_PLUS):
		return "A+"
	case uint32(pb.WealthGodBoxType_WEALTH_GOD_BOX_TYPE_A):
		return "A"
	default:
		return "未知"
	}
}
