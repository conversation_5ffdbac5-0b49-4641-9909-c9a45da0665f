package manager

import (
	"context"
	"fmt"
	"golang.52tt.com/clients/account"
	"golang.52tt.com/clients/channel"
	PushNotification "golang.52tt.com/clients/push-notification/v2"
	"golang.52tt.com/clients/reconcile-v2-svr/reconcile-present"
	userprofileapi "golang.52tt.com/clients/user-profile-api"
	userPresent "golang.52tt.com/clients/userpresent"
	youknowwho "golang.52tt.com/clients/you-know-who"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	channelPB "golang.52tt.com/protocol/app/channel"
	"golang.52tt.com/protocol/common/status"
	pb "golang.52tt.com/protocol/services/present-count-go"
	ykwPb "golang.52tt.com/protocol/services/youknowwho"
	"golang.52tt.com/services/tt-rev/present-count/internal/cache"
	"golang.52tt.com/services/tt-rev/present-count/internal/conf"
	"golang.52tt.com/services/tt-rev/present-count/internal/store"
	"google.golang.org/grpc/codes"
	"strconv"
	"strings"
	"sync"
	"time"
    "gitlab.ttyuyin.com/avengers/tyr/pkg/cluster/timer"
)

type PresentCountMgr struct {
	shutDown     chan interface{}
	wg           sync.WaitGroup
	Sc           conf.IServiceConfig
	Bc           conf.IBusinessConfManager
	countRedis   cache.IPresentCountCache
	orderIdRedis cache.IPresentCountCache
	Store        store.IStore

    timerD *timer.Timer

	accountCli     account.IClient
	channelCli     channel.IClient
	pushCli        PushNotification.IClient
	presentCli     userPresent.IClient
	userProfileCli userprofileapi.IClient
	ykwCli         youknowwho.IClient
	reconcileCli   reconcile_present.IClient
}

func NewPresentCountMgr(sc *conf.ServiceConfig, bcFilePath string, countCache *cache.PresentCountCache,
	mysqlStore store.IStore, orderCache *cache.PresentCountCache) (*PresentCountMgr, error) {

	bc, err := conf.NewBusinessConfManager(bcFilePath)
	if err != nil {
		log.Errorf("Failed to NewBusinessConfManager %v", err)
		return nil, err
	}

	accountCli, _ := account.NewClient()
	channelCli := channel.NewClient()
	pushCli, _ := PushNotification.NewClient()
	presentCli := userPresent.NewClient()
	userProfileCli, _ := userprofileapi.NewClient()
	ykwCli, _ := youknowwho.NewClient()
	reconcileCli := reconcile_present.NewIClient()

	mgr := &PresentCountMgr{
		shutDown:     make(chan interface{}),
		Sc:           sc,
		Bc:           bc,
		countRedis:   countCache,
		orderIdRedis: orderCache,
		Store:        mysqlStore,

		pushCli:        pushCli,
		accountCli:     accountCli,
		presentCli:     presentCli,
		channelCli:     channelCli,
		userProfileCli: userProfileCli,
		ykwCli:         ykwCli,
		reconcileCli:   reconcileCli,
	}
    
    // 创建定时器
    err = mgr.StartTimer(sc.GetRedisOrderIdConfig())
    if err != nil {
        log.Errorf("Failed to StartTimer %v", err)
        return nil, err
    }

	return mgr, nil
}

func (m *PresentCountMgr) ShutDown() {
	close(m.shutDown)
	m.wg.Wait()

	m.pushCli.Close()
	m.accountCli.Close()
	m.channelCli.Close()
	m.presentCli.Close()
}

func (m *PresentCountMgr) CheckOrderId(orderId string) (bool, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
	defer cancel()
	return m.orderIdRedis.OrderIdCheck(ctx, orderId)
}

func (m *PresentCountMgr) RollbackOrderId(orderId string) error {
	ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
	defer cancel()
	return m.orderIdRedis.OrderIdDealFailRollBack(ctx, orderId)
}

func (m *PresentCountMgr) TurnOnCounting(ctx context.Context, channelId uint32, micUserS []uint32, opUid, channelType uint32) error {
	var err error

	if channelType == 0 {
		log.ErrorWithCtx(ctx, "TurnOnCounting fail,channelType==0,invalid")
		return protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "参数错误")
	}

	// ugc房后面大概率不会做榜单的逻辑，所以这里直接写死
	if channelType == uint32(channelPB.ChannelType_GUILD_PUBLIC_FUN_CHANNEL_TYPE) {

		err = m.countRedis.SetCounterRankTime(channelId)
		if err != nil {
			log.ErrorWithCtx(ctx, "TurnOnCounting fail to SetCounterRankTime,opUid:%d, channelId:%d, err:%v", opUid, channelId, err)
			return err
		}
	}

	err = m.countRedis.AddChannelToCountingSet(ctx, channelId)
	if err != nil {
		log.ErrorWithCtx(ctx, "TurnOnCounting fail to AddChannelToCountingSet,opUid:%d, channelId:%d, err:%v", opUid, channelId, err)
		return err
	}

	// 开启计数器，房间麦位set初始化
	err = m.countRedis.AddToChannelMicSet(ctx, channelId, micUserS)
	if err != nil {
		log.ErrorWithCtx(ctx, "TurnOnCounting fail to AddToChannelMicSet,opUid:%d, channelId:%d,micUserS:%v, err:%v", opUid, channelId, micUserS, err)
		return err
	}

	// 推送
	err = m.PushSwitchOn(ctx, channelId, opUid)
	if err != nil {
		log.ErrorWithCtx(ctx, "TurnOnCounting fail to PushSwitchOn, channelId(%d), opUid(%d), err:%v", channelId, opUid, err)
	}

	return nil
}

func (m *PresentCountMgr) TurnOffCounting(ctx context.Context, opUid, channelId uint32) error {

	err := m.countRedis.DelChannelFromCountingSet(ctx, channelId)
	if err != nil {
		log.ErrorWithCtx(ctx, "TurnOffCounting fail to DelChannelFromCountingSet,opUid:%d, channelId:%d,err:%v", opUid, channelId, err)
		return err
	}

	// 清理房间计数、麦位列表
	err = m.countRedis.DelPresentCntAndMicList(ctx, channelId)
	if err != nil {
		log.ErrorWithCtx(ctx, "TurnOffCounting fail to DelPresentCntAndMicList,opUid:%d, channelId:%d, err:%v", opUid, channelId, err)
		return err
	}

	//房间计数榜单清理
	roundTime, err := m.countRedis.GetCounterRankTime(channelId)
	if err != nil {
		log.ErrorWithCtx(ctx, "TurnOffCounting fail to GetCounterRankTime,opUid:%d, channelId:%d, err:%v", opUid, channelId, err)
		return err
	}
	_ = m.countRedis.DelChannelCounterRankTime(channelId)
	_ = m.countRedis.DelChannelLevelAnimateKey(channelId)
	_ = m.countRedis.SetRankKeysSetExpired(channelId, roundTime)

	if m.Bc.GetUseZSetKeyFlag() {
		rt, err := m.countRedis.ZScoreCounterRankTime(channelId)
		if err != nil {
			log.ErrorWithCtx(ctx, "TurnOffCounting fail to ZScoreCounterRankTime,opUid:%d, channelId:%d, err:%v", opUid, channelId, err)
			return err
		}
		if rt != 0 {
			_ = m.countRedis.ZRemCounterRankTime(channelId)
			_ = m.countRedis.SetRankKeysSetExpired(channelId, rt)
		}
	}

	// 推送
	err = m.PushSwitchOff(ctx, channelId, opUid)
	if err != nil {
		log.ErrorWithCtx(ctx, "TurnOffCounting fail to PushSwitchOff, channelId(%d), opUid(%d), err:%v", channelId, opUid, err)
	}

	return nil
}

func (m *PresentCountMgr) getStrUidInfo(ukwInfo *pb.UkwInfo) string {
	log.Debugf("getStrUidInfo begin ukwInfo:%v", ukwInfo)

	if ukwInfo.GetLevel() == 0 {
		return fmt.Sprintf("%d", ukwInfo.GetUid())
	} else {
		return fmt.Sprintf("%d-%s", ukwInfo.GetUid(), ukwInfo.GetAccount())
	}
}
func (m *PresentCountMgr) ParseStrUidInfo(cid uint32, strUidInfo string) *pb.UkwInfo {
	log.Debugf("parseStrUidInfo begin cid:%d strUidInfo:%s", cid, strUidInfo)

	ukwInfo := &pb.UkwInfo{}
	resList := strings.Split(strUidInfo, "-")

	if strUidInfo == "" || len(resList) <= 0 {
		return ukwInfo
	}

	uid, err := strconv.Atoi(resList[0])
	if err != nil {
		log.Errorf("ParseStrUidInfo failed cid:%d info:%v res:%s err:%v", cid, strUidInfo, resList[0], err)
		return ukwInfo
	}

	if len(resList) > 1 {
		// 神秘人
		ukwInfo, err = m.countRedis.GetUkwInfo(cid, strUidInfo)
		if err != nil {
			log.Errorf("ParseStrUidInfo GetUkwInfo failed cid:%d info:%v err:%v", cid, strUidInfo, err)
			return ukwInfo
		}
	}

	ukwInfo.Uid = uint32(uid)

	log.Debugf("ParseStrUidInfo end cid:%d strUidInfo:%s uwkInfo:%v", cid, strUidInfo, ukwInfo)
	return ukwInfo
}

func (m *PresentCountMgr) GetUserUkwInfo(ctx context.Context, uid uint32) (*pb.UkwInfo, error) {
	ukwInfo := &pb.UkwInfo{
		Uid: uid,
	}

	ctxN, cancel := context.WithTimeout(ctx, 2*time.Second)
	defer cancel()
	ukwInfoResp, err := m.ykwCli.GetUKWInfo(ctxN, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetUserCounterRank fail GetUKWInfo,uid:%d,err:%v", uid, err)
		return ukwInfo, err
	}

	if ukwInfoResp.GetUkwPermissionInfo().GetSwitch() == ykwPb.UKWSwitchType_UKW_SWITCH_ON {
		ukwInfo.Account = ukwInfoResp.GetUkwPersonInfo().GetAccount()
		ukwInfo.Nickname = ukwInfoResp.GetUkwPersonInfo().GetNickname()
		ukwInfo.Level = ukwInfoResp.GetUkwPersonInfo().GetLevel()
		ukwInfo.Medal = ukwInfoResp.GetUkwPersonInfo().GetMedal()
		ukwInfo.HeadFrame = ukwInfoResp.GetUkwPersonInfo().GetHeadFrame()
		ukwInfo.FakeUid = ukwInfoResp.GetUkwPersonInfo().GetFakeUid()
	}
	return ukwInfo, nil
}

func (m *PresentCountMgr) AddToChannelCounterRank(ctx context.Context, channelId, targetUid, uid, price uint32) {
	roundTime, err := m.countRedis.GetCounterRankTime(channelId)
	if err != nil {
		log.Errorf("AddToChannelCounterRank fail to GetCounterRankTime,channelId:%d, err%v", channelId, err)
	}

	if m.Bc.GetUseZSetKeyFlag() {
		roundTime2, err := m.countRedis.ZScoreCounterRankTime(channelId)
		if err != nil {
			log.ErrorWithCtx(ctx, "AddToChannelCounterRank fail to ZScoreCounterRankTime,uid:%d, channelId:%d, err:%v", uid, channelId, err)
			return
		}
		if roundTime == 0 {
			roundTime = roundTime2

		} else {
			if roundTime2 != 0 && roundTime2 < roundTime {
				// 如果有旧的roundTime2，而且roundTime2< roundTime, 那就用以前的时间戳
				roundTime = roundTime2
			}
		}
	}

	// 未开启计数的房间，操作结束
	if roundTime == 0 {
		return
	}

	// 获取用户神秘人信息
	fromUkwInfo, err := m.GetUserUkwInfo(ctx, uid)
	if err != nil {
		log.Errorf("AddToChannelCounterRank fail at GetUserUkwInfo,channelId:%d,uid:%d", channelId, uid)
		return
	}

	if fromUkwInfo.GetLevel() > 0 {
		// 需要记录神秘人实时信息
		err = m.countRedis.SetUkwInfo(channelId, m.getStrUidInfo(fromUkwInfo), fromUkwInfo)
		if err != nil {
			log.Errorf("AddToChannelCounterRank SetUkwInfo failed sendUid:%d targetUid:%d channelId:%d ukwInfo:%v err:%v", uid, targetUid, channelId, fromUkwInfo, err)
		}
	}

	err = m.countRedis.IncrCounterUserRankLoveValue(roundTime, channelId, targetUid, price, m.getStrUidInfo(fromUkwInfo), "")
	if err != nil {
		log.Errorf("AddToChannelCounterRank fail to IncrCounterUserRankLoveValue,channelId:%d, err%v", channelId, err)
	}
}

func (m *PresentCountMgr) GetChannelCounterStatus(ctx context.Context, channelId uint32) (bool, error) {
	var state bool
	score, err := m.countRedis.GetChannelStartTimeSec(ctx, channelId)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetChannelCountingState fail to GetChannelStartTimeSec, channelId:%d, err:%v", channelId, err)
		return state, err
	}

	now := time.Now()
	if now.Unix() < int64(m.Sc.GetPresentCountTTL())+score {
		state = true
	} else {
		state = false
	}
	return state, nil
}

func (m *PresentCountMgr) GetChannelCountingState(ctx context.Context, channelId uint32) (*pb.GetPresentCountStateResp, error) {
	out := &pb.GetPresentCountStateResp{}

	styleInfo := m.Bc.GetStyleInfoByValue(0)
	out.StylePrefix = m.Bc.GetStylePrefix()
	out.UncompiledPrefix = m.Bc.GetUncompiledPrefix()
	out.BgSuffix = styleInfo.BgSuffix
	out.IcSuffic = styleInfo.IcSuffix

	score, err := m.countRedis.GetChannelStartTimeSec(ctx, channelId)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetChannelCountingState fail to GetChannelStartTimeSec, channelId:%d, err:%v", channelId, err)
		return out, nil
	}

	now := time.Now()

	if now.Unix() >= int64(m.Sc.GetPresentCountTTL())+score {
		return out, nil
	} else {
		out.State = true
	}

	info, err := m.countRedis.GetChannelAllPresentCnt(ctx, channelId)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetChannelCountingState fail to GetChannelAllPresentCnt, channelId:%d, err:%v", channelId, err)
		return out, nil
	}

	for _, item := range info {
		styleInfo := m.Bc.GetStyleInfoByValue(item.Price)
		out.MicsPresentCount = append(out.MicsPresentCount, &pb.MicsPresentCountInfo{
			Uid:      item.Uid,
			Price:    item.Price,
			BgSuffix: styleInfo.BgSuffix,
			IcSuffic: styleInfo.IcSuffix,
		})
	}

	return out, nil
}

func (m *PresentCountMgr) GetPresentCountByUid(ctx context.Context, channelId, uid uint32) (uint32, error) {

	ok, err := m.GetChannelCounterStatus(ctx, channelId)
	if err != nil {
		log.ErrorWithCtx(ctx, "HandlePresentEventMgr fail to GetChannelCountingState,channelId:%d, err:%v", channelId, err)
		return uint32(0), err
	}
	if !ok {
		return uint32(0), nil
	}

	ok, err = m.countRedis.CheckIfMemberInSet(ctx, channelId, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "HandlePresentEventMgr fail to CheckIfMemberInSet,channelId:%d, uid:%d, err:%v", channelId, uid, err)
		return uint32(0), err
	}
	if !ok {
		return uint32(0), nil
	}

	price, err := m.countRedis.GetChannelUserPresentCnt(ctx, channelId, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "HandlePresentEventMgr fail to GetChannelUserPresentCnt,channelId:%d, uid:%d, err:%v", channelId, uid, err)
		return uint32(0), err
	}

	return uint32(price), nil
}

func (m *PresentCountMgr) ClearChannelUserPresentCnt(ctx context.Context, channelId, uid uint32) error {
	// Clear用户计数
	err := m.countRedis.ClearChannelUserPresentCnt(ctx, channelId, uid)
	if err != nil {
		log.Errorf("ClearChannelUserPresentCnt fail at ClearChannelUserPresentCnt")
		return err
	}

	// 切换身份清除升级动画播放记录
	m.countRedis.SRemUserChannelLevelAnimateFlag(channelId, uid, m.Bc.GetLevelAnimateLevelList())

	// 榜单用户神秘人处理
	roundTime, err := m.countRedis.GetCounterRankTime(channelId)
	if err != nil {
		log.Errorf("ClearChannelUserPresentCnt fail at GetCounterRankTime,channelId%d,err:%v", channelId, err)
		return err
	}
	if m.Bc.GetUseZSetKeyFlag() {
		roundTime2, err := m.countRedis.ZScoreCounterRankTime(channelId)
		if err != nil {
			log.ErrorWithCtx(ctx, "ClearChannelUserPresentCnt fail to ZScoreCounterRankTime,uid:%d, channelId:%d, err:%v", uid, channelId, err)
			return err
		}
		if roundTime == 0 {
			roundTime = roundTime2

		} else {
			if roundTime2 != 0 && roundTime2 < roundTime {
				// 如果有旧的roundTime2，而且roundTime2< roundTime, 那就用以前的时间戳
				roundTime = roundTime2
			}
		}
	}

	if roundTime != 0 {
		// 清除用户自己的榜单
		err = m.countRedis.ClearCounterUserRank(roundTime, channelId, uid)
		if err != nil {
			log.Errorf("ClearChannelUserPresentCnt fail at ClearCounterUserRank")
			return err
		}
	}

	log.InfoWithCtx(ctx, "ClearChannelUserPresentCnt you-know-who channelId:%d,uid:%d", channelId, uid)
	return err
}

func (m *PresentCountMgr) ClearUkwUserCache(ctx context.Context, channelId, uid uint32) error {
	up, tErr := m.userProfileCli.GetUserProfile(ctx, uid)
	if tErr != nil {
		log.ErrorWithCtx(ctx, "ClearUkwUserCache fail to GetUserProfile, channelId: %d, uid: %d, error: %v", channelId, uid, tErr)
		return tErr
	}

	success, err := m.countRedis.SetNewUkwFakeUid(channelId, uid, up.GetUid())
	if err != nil {
		log.ErrorWithCtx(ctx, "ClearUkwUserCache fail to SetNewUkwFakeUid, channelId: %d, uid: %d, error: %v", channelId,
			uid, err)
		return err
	}
	if !success {
		log.DebugWithCtx(ctx, "ClearUkwUserCache, channelId: %d, uid: %d, old fakeuid: %d", channelId, uid, up.GetUid())
		return nil
	}

	return m.countRedis.ClearChannelUserPresentCnt(ctx, channelId, uid)
}

func (m *PresentCountMgr) SetNewUkwFakeUid(channelId, uid, fakeUid uint32) (bool, error) {
	return m.countRedis.SetNewUkwFakeUid(channelId, uid, fakeUid)
}

func (m *PresentCountMgr) GetMyRankInfo(ctx context.Context, uid, channelId, roundTime, targetUid, rankSize uint32) (*pb.PresentCountRankUser, error) {
	out := &pb.PresentCountRankUser{}

	myUkwInfo, err := m.GetUserUkwInfo(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetMyRankInfo fail GetUKWInfo,channelId%d,uid:%d,err:%v", channelId, uid, err)
		return out, err
	}
	myUidInfoStr := m.getStrUidInfo(myUkwInfo)

	myRank, exist, err := m.countRedis.GetCounterRankByUid(roundTime, channelId, targetUid, myUidInfoStr)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetMyRankInfo fail at GetCounterRankByUid,err:%v", err)
		return out, err
	}

	myValue, err := m.countRedis.GetCounterRankScoreByUid(roundTime, channelId, targetUid, myUidInfoStr)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetMyRankInfo fail GetCounterRankScoreByUid,uid:%d,err:%v", uid, err)
		return out, err
	}

	if !exist {
		// 贡献值为0
		dValue := uint32(0)

		targetValue, err := m.countRedis.GetCounterUserRankByScore(roundTime, channelId, targetUid, rankSize-1, 1)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetMyRankInfo fail GetCounterUserRankByScore,channelId%d，roundTime:%v,err:%v", channelId, roundTime, err)
			return out, err
		}
		if len(targetValue) == 0 {
			dValue = 0
		} else {
			dValue = targetValue[0].Value
		}

		out = m.fillMyRankInfo(myUkwInfo, channelId, 0, 0, dValue)

	} else if myRank == 0 {
		// 第一名特殊处理,比较第二名
		dValue := myValue
		valueList, err := m.countRedis.GetCounterUserRankByScore(roundTime, channelId, targetUid, 1, 1)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetMyRankInfo fail GetCounterUserRankByScore,channelId%d，targetUid:%d,err:%v", channelId, targetUid, err)
			return out, err
		}
		if len(valueList) == 1 {
			dValue = myValue - valueList[0].Value
		}
		out = m.fillMyRankInfo(myUkwInfo, channelId, myValue, 1, dValue)

	} else {
		// 有贡献值的其他情况
		dValue := uint32(0)
		targetOffset := myRank - 1

		if myRank+1 > rankSize {
			// 不在榜内，比较自己与榜单最后一名的送礼值
			targetOffset = rankSize - 1
		}

		valueList, err := m.countRedis.GetCounterUserRankByScore(roundTime, channelId, targetUid, targetOffset, 1)
		if err != nil || len(valueList) == 0 {
			log.ErrorWithCtx(ctx, "GetMyRankInfo fail GetCounterUserRankByScore,channelId%d，err:%v", channelId, err)
			return out, err
		}
		dValue = valueList[0].Value - myValue
		out = m.fillMyRankInfo(myUkwInfo, channelId, myValue, myRank+1, dValue)
	}
	return out, nil
}

// 获取用户计数榜单
func (m *PresentCountMgr) GetUserCounterRank(ctx context.Context, channelId, targetUid, uid, offset, limit uint32) (*pb.GetPresentCountRankResp, error) {
	out := &pb.GetPresentCountRankResp{}
	var queryOffset uint32

	rankSize := m.Bc.GetTestRankSize()
	if offset+limit >= rankSize {
		limit = rankSize - offset
	}

	if offset != 0 {
		queryOffset = offset - 1 //往前多查一个名次
		limit = limit + 1
	}
	log.Debugf("GetUserCounterRank,queryOffset:%d,offset:%d,limit:%d", queryOffset, offset, limit)

	roundTime, err := m.countRedis.GetCounterRankTime(channelId)
	if m.Bc.GetUseZSetKeyFlag() {
		roundTime2, e := m.countRedis.ZScoreCounterRankTime(channelId)
		if e != nil {
			log.ErrorWithCtx(ctx, "GetUserCounterRank fail to ZScoreCounterRankTime,uid:%d, channelId:%d, err:%v", uid, channelId, err)
			return out, protocol.NewExactServerError(codes.OK, status.ErrCountRankNotSupport, "爱意榜维护中")
		}
		if roundTime == 0 {
			roundTime = roundTime2

		} else {
			if roundTime2 != 0 && roundTime2 < roundTime {
				// 如果有旧的roundTime2，而且roundTime2< roundTime, 那就用以前的时间戳
				roundTime = roundTime2
			}
		}
	}

	if err != nil || roundTime == 0 {
		log.ErrorWithCtx(ctx, "GetUserCounterRank fail ZScoreCounterRankTime,channelId:%d,err:%v", channelId, err)
		return out, protocol.NewExactServerError(codes.OK, status.ErrCountRankNotSupport, "爱意榜维护中")
	}

	// 获取目标用户收礼值
	totalPrice, err := m.countRedis.GetChannelUserPresentCnt(ctx, channelId, targetUid)
	if err != nil {
		log.Errorf("GetUserCounterRank fail to GetChannelUserPresentCnt,channelId:%d, targetUid:%d, err:%v", channelId, targetUid, err)
		return &pb.GetPresentCountRankResp{}, err
	}

	// 获取计数榜单列表
	rankList, err := m.countRedis.GetCounterUserRankByScore(roundTime, channelId, targetUid, queryOffset, limit)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetUserCounterRank fail GetCounterUserRankByScore,channelId%d,err:%v", channelId, err)
		return out, err
	}

	// 填充目标用户信息
	curStyle := m.Bc.GetStyleInfoByValue(uint32(totalPrice))
	nextLevelStyle := m.Bc.GetNextLevelStyleByLevel(curStyle.Level)

	out.ChannelId = channelId
	out.TargetUser = &pb.PresentCountTargetUser{
		Uid:            targetUid,
		Receive:        uint32(totalPrice),
		NextLevelValue: nextLevelStyle.BeginValue,
		NextLevelUrl:   nextLevelStyle.SampleUrl,
	}

	rankOffset := offset
	if offset == 0 {
		rankOffset = 1
	}

	// 填充榜单信息
	for i, v := range rankList {
		if i == 0 && offset != 0 {
			continue
		}

		dValue := uint32(0)
		userRank := uint32(i) + rankOffset

		if userRank == 1 {
			//第一名计算与第二名的差值
			if len(rankList) >= 2 {
				dValue = rankList[i].Value - rankList[i+1].Value
			} else {
				dValue = v.Value
			}

		} else {
			// 计算与上一名的差距
			dValue = rankList[i-1].Value - rankList[i].Value
		}

		out.RankList = append(out.RankList, m.fillPresentCounterRankUser(v.StrUidInfo, channelId, v.Value, userRank, dValue))
	}

	// 仅在第一页返回“我的信息”
	if offset == 0 {
		out.MyInfo, err = m.GetMyRankInfo(ctx, uid, channelId, roundTime, targetUid, rankSize)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetUserCounterRank fail GetMyRankInfo")
			return &pb.GetPresentCountRankResp{}, err
		}
	}

	out.ViewCnt = rankSize
	return out, nil
}

func If(isTrue bool, a, b uint32) uint32 {
	if isTrue {
		return a
	}
	return b
}

func (m *PresentCountMgr) fillPresentCounterRankUser(strUidInfo string, channelId, sendValue, rank, dValue uint32) *pb.PresentCountRankUser {
	ukwInfo := m.ParseStrUidInfo(channelId, strUidInfo)

	return &pb.PresentCountRankUser{
		Uid:       ukwInfo.GetUid(),
		SendValue: sendValue,
		Rank:      If(rank == 0, 1, rank),
		DValue:    If(dValue == 0, 10, dValue),
		UkwInfo:   ukwInfo,
	}
}

func (m *PresentCountMgr) fillMyRankInfo(myUkwInfo *pb.UkwInfo, channelId, sendValue, rank, dValue uint32) *pb.PresentCountRankUser {
	return &pb.PresentCountRankUser{
		Uid:       myUkwInfo.GetUid(),
		SendValue: sendValue,
		Rank:      rank,
		DValue:    If(dValue == 0, 10, dValue),
		UkwInfo:   myUkwInfo,
	}
}
