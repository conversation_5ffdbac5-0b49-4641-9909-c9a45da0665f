package conf

//go:generate quicksilver-cli test interface ../conf
//go:generate mockgen -destination=./mocks/business_config.go -package=mocks golang.52tt.com/services/tt-rev/fellow-house/internal/conf IBusinessConfManager

import (
    "crypto/md5"
    "encoding/json"
    "fmt"
    "io/ioutil"
    "os"
    "time"

    "gitlab.ttyuyin.com/tt-infra/tyr/log"
)

const (
    BusinessConfPath = "/data/oss/conf-center/tt/"
    BusinessConfFile = "fellow-house.json"
)

var LastConfMd5Sum [md5.Size]byte

type BusinessConf struct {
    PayAppId       string   `json:"pay_app_id"`        // 支付系统appId
    TestPayCallBackCommitList []uint32 `json:"test_pay_call_back_commit_list"` // 测试支付回调用户列表
    TestPayRollBackList       []uint32 `json:"test_pay_roll_back_list"` // 测试支付回滚用户列表

    // 购买小屋触发全服idMap
    BuyHouseIdBreakingNewsIdMap map[uint32]uint32 `json:"buy_house_id_break_news_id_map"`
}


func (c *BusinessConf) Parse(configFile string) (isChange bool, err error) {
    defer func() {
        if e := recover(); e != nil {
            err = fmt.Errorf("Failed to parse config: %v \n", e)
        }
    }()

    data, err := ioutil.ReadFile(configFile)
    if err != nil {
        return false, err
    }

    md5Sum := md5.Sum(data)
    if md5Sum == LastConfMd5Sum {
        isChange = false
        return
    }

    err = json.Unmarshal(data, &c)
    if err != nil {
        return false, err
    }

    err = c.CheckConf()
    if err != nil {
        return false, err
    }

    LastConfMd5Sum = md5Sum

    log.Infof("BusinessConf : %+v", c)
    return true, nil
}

type BusinessConfManager struct {
    Done chan interface{}
    //mutex sync.RWMutex
    conf *BusinessConf
}

func NewBusinessConfManager() (*BusinessConfManager, error) {
    businessConf := &BusinessConf{}

    businessConfFilePath := BusinessConfPath + BusinessConfFile
    if devBusinessConfPath := os.Getenv("DEV_BUSINESS_CONF_PATH"); devBusinessConfPath != "" {
        businessConfFilePath = devBusinessConfPath + BusinessConfFile
    }
    _, err := businessConf.Parse(businessConfFilePath)
    if err != nil {
        log.Errorf("NewBusinessConfManager fail to Parse BusinessConf err:%v", err)
        return nil, err
    }

    confMgr := &BusinessConfManager{
        conf: businessConf,
        Done: make(chan interface{}),
    }

    go confMgr.Watch(businessConfFilePath)

    return confMgr, nil
}

func (bm *BusinessConfManager) Reload(file string) error {
    businessConf := &BusinessConf{}

    isChange, err := businessConf.Parse(file)
    if err != nil {
        log.Errorf("NewBusinessConfManager fail to Parse BusinessConf err:%v", err)
        return err
    }

    if isChange {
        //bm.mutex.Lock()
        bm.conf = businessConf
        //bm.mutex.Unlock()

        log.Infof("Reload %+v", businessConf)
    }

    return nil
}

func (bm *BusinessConfManager) Watch(file string) {
    log.Infof("Watch start. file:%s", file)

    for {
        select {
        case _, ok := <-bm.Done:
            if !ok {
                log.Infof("Watch done")
                return
            }

        case <-time.After(30 * time.Second):
            log.Debugf("Watch check change")

            err := bm.Reload(file)
            if err != nil {
                log.Errorf("Watch Reload fail. file:%s, err:%v", file, err)
            }
        }
    }
}

func (bm *BusinessConfManager) Close() {
    close(bm.Done)
}

func (c *BusinessConf) CheckConf() error {
    if c.PayAppId == "" {
        return fmt.Errorf("PayAppId is empty")
    }
    return nil
}

func (bm *BusinessConfManager) GetPayAppId() string {
    return bm.conf.PayAppId
}

func (bm *BusinessConfManager) TestPayCallBackCommitList(uid uint32) bool {
    for _, v := range bm.conf.TestPayCallBackCommitList {
        if v == uid {
            return true
        }
    }
    return false
}

func (bm *BusinessConfManager) TestPayRollBackList(uid uint32) bool {
    for _, v := range bm.conf.TestPayRollBackList {
        if v == uid {
            return true
        }
    }
    return false
}

func (bm *BusinessConfManager) GetBuyHouseIdBreakingNewsId(houseId uint32) uint32 {
    if bm.conf == nil || bm.conf.BuyHouseIdBreakingNewsIdMap == nil {
        return 0
    }
    return bm.conf.BuyHouseIdBreakingNewsIdMap[houseId]
}
