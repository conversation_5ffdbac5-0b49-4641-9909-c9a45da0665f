package interceptor

import (
	"github.com/google/wire"
	"golang.52tt.com/services/tt-rev/offering-room-logic/internal/rpc"
	"google.golang.org/grpc"
)

var ProviderSetForInterceptorGen = wire.NewSet(
	NewManager,
)

type Interceptor interface {
	GetInterceptor() []grpc.UnaryServerInterceptor
}

type manager struct {
	interceptors []grpc.UnaryServerInterceptor
}

func NewManager(rpcCli *rpc.ClientWrapper) Interceptor {
	return &manager{
		interceptors: []grpc.UnaryServerInterceptor{
			WithPayCheckInterceptor(rpcCli.IopProxyCli, rpcCli.UsualDeviceCli),
		},
	}
}

func (m *manager) GetInterceptor() []grpc.UnaryServerInterceptor {
	return m.interceptors
}
