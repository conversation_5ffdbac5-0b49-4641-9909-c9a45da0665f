// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/services/tt-rev/offering-room-logic/internal/mgr (interfaces: ApplyListMgr,GameMgr,PushService,RelationshipMgr)

// Package mgr is a generated GoMock package.
package mgr

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	proto "gitlab.ttyuyin.com/avengers/tyr/compatible/proto"
	grpc "gitlab.ttyuyin.com/avengers/tyr/core/service/basepb/info"
	app "golang.52tt.com/protocol/app"
	channel "golang.52tt.com/protocol/app/channel"
	offer_room "golang.52tt.com/protocol/app/offer_room"
	push "golang.52tt.com/protocol/app/push"
	offer_room0 "golang.52tt.com/protocol/services/offer-room"
)

// MockApplyListMgr is a mock of ApplyListMgr interface.
type MockApplyListMgr struct {
	ctrl     *gomock.Controller
	recorder *MockApplyListMgrMockRecorder
}

// MockApplyListMgrMockRecorder is the mock recorder for MockApplyListMgr.
type MockApplyListMgrMockRecorder struct {
	mock *MockApplyListMgr
}

// NewMockApplyListMgr creates a new mock instance.
func NewMockApplyListMgr(ctrl *gomock.Controller) *MockApplyListMgr {
	mock := &MockApplyListMgr{ctrl: ctrl}
	mock.recorder = &MockApplyListMgrMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockApplyListMgr) EXPECT() *MockApplyListMgrMockRecorder {
	return m.recorder
}

// GetApplyList mocks base method.
func (m *MockApplyListMgr) GetApplyList(arg0 context.Context, arg1 uint32) (*offer_room.OfferRoomGetApplyListResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetApplyList", arg0, arg1)
	ret0, _ := ret[0].(*offer_room.OfferRoomGetApplyListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetApplyList indicates an expected call of GetApplyList.
func (mr *MockApplyListMgrMockRecorder) GetApplyList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetApplyList", reflect.TypeOf((*MockApplyListMgr)(nil).GetApplyList), arg0, arg1)
}

// PushLatestList mocks base method.
func (m *MockApplyListMgr) PushLatestList(arg0 context.Context, arg1 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PushLatestList", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// PushLatestList indicates an expected call of PushLatestList.
func (mr *MockApplyListMgrMockRecorder) PushLatestList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PushLatestList", reflect.TypeOf((*MockApplyListMgr)(nil).PushLatestList), arg0, arg1)
}

// ReportHoldMicByLink mocks base method.
func (m *MockApplyListMgr) ReportHoldMicByLink(arg0, arg1 uint32) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "ReportHoldMicByLink", arg0, arg1)
}

// ReportHoldMicByLink indicates an expected call of ReportHoldMicByLink.
func (mr *MockApplyListMgrMockRecorder) ReportHoldMicByLink(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReportHoldMicByLink", reflect.TypeOf((*MockApplyListMgr)(nil).ReportHoldMicByLink), arg0, arg1)
}

// UserApply mocks base method.
func (m *MockApplyListMgr) UserApply(arg0 context.Context, arg1, arg2 uint32) (*offer_room.OfferRoomUserApplyResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UserApply", arg0, arg1, arg2)
	ret0, _ := ret[0].(*offer_room.OfferRoomUserApplyResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UserApply indicates an expected call of UserApply.
func (mr *MockApplyListMgrMockRecorder) UserApply(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UserApply", reflect.TypeOf((*MockApplyListMgr)(nil).UserApply), arg0, arg1, arg2)
}

// UserCancelApply mocks base method.
func (m *MockApplyListMgr) UserCancelApply(arg0 context.Context, arg1, arg2 uint32) (*offer_room.OfferRoomUserCancelApplyResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UserCancelApply", arg0, arg1, arg2)
	ret0, _ := ret[0].(*offer_room.OfferRoomUserCancelApplyResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UserCancelApply indicates an expected call of UserCancelApply.
func (mr *MockApplyListMgrMockRecorder) UserCancelApply(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UserCancelApply", reflect.TypeOf((*MockApplyListMgr)(nil).UserCancelApply), arg0, arg1, arg2)
}

// MockGameMgr is a mock of GameMgr interface.
type MockGameMgr struct {
	ctrl     *gomock.Controller
	recorder *MockGameMgrMockRecorder
}

// MockGameMgrMockRecorder is the mock recorder for MockGameMgr.
type MockGameMgrMockRecorder struct {
	mock *MockGameMgr
}

// NewMockGameMgr creates a new mock instance.
func NewMockGameMgr(ctrl *gomock.Controller) *MockGameMgr {
	mock := &MockGameMgr{ctrl: ctrl}
	mock.recorder = &MockGameMgrMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockGameMgr) EXPECT() *MockGameMgrMockRecorder {
	return m.recorder
}

// Abort mocks base method.
func (m *MockGameMgr) Abort(arg0 context.Context, arg1 uint32, arg2 int64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Abort", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// Abort indicates an expected call of Abort.
func (mr *MockGameMgrMockRecorder) Abort(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Abort", reflect.TypeOf((*MockGameMgr)(nil).Abort), arg0, arg1, arg2)
}

// End mocks base method.
func (m *MockGameMgr) End(arg0 context.Context, arg1 uint32, arg2 int64, arg3 bool) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "End", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// End indicates an expected call of End.
func (mr *MockGameMgrMockRecorder) End(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "End", reflect.TypeOf((*MockGameMgr)(nil).End), arg0, arg1, arg2, arg3)
}

// GetCurGameInfo mocks base method.
func (m *MockGameMgr) GetCurGameInfo(arg0 context.Context, arg1 uint32) (*offer_room.OfferRoomCurOfferingGameInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCurGameInfo", arg0, arg1)
	ret0, _ := ret[0].(*offer_room.OfferRoomCurOfferingGameInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCurGameInfo indicates an expected call of GetCurGameInfo.
func (mr *MockGameMgrMockRecorder) GetCurGameInfo(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCurGameInfo", reflect.TypeOf((*MockGameMgr)(nil).GetCurGameInfo), arg0, arg1)
}

// GetMyPrice mocks base method.
func (m *MockGameMgr) GetMyPrice(arg0 context.Context, arg1 uint32, arg2 int64, arg3 uint32) (uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMyPrice", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMyPrice indicates an expected call of GetMyPrice.
func (mr *MockGameMgrMockRecorder) GetMyPrice(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMyPrice", reflect.TypeOf((*MockGameMgr)(nil).GetMyPrice), arg0, arg1, arg2, arg3)
}

// GetOfferingConfig mocks base method.
func (m *MockGameMgr) GetOfferingConfig(arg0 context.Context) (*offer_room.OfferRoomGetOfferingConfigResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOfferingConfig", arg0)
	ret0, _ := ret[0].(*offer_room.OfferRoomGetOfferingConfigResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOfferingConfig indicates an expected call of GetOfferingConfig.
func (mr *MockGameMgrMockRecorder) GetOfferingConfig(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOfferingConfig", reflect.TypeOf((*MockGameMgr)(nil).GetOfferingConfig), arg0)
}

// InitNewGame mocks base method.
func (m *MockGameMgr) InitNewGame(arg0 context.Context, arg1, arg2 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InitNewGame", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// InitNewGame indicates an expected call of InitNewGame.
func (mr *MockGameMgrMockRecorder) InitNewGame(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InitNewGame", reflect.TypeOf((*MockGameMgr)(nil).InitNewGame), arg0, arg1, arg2)
}

// NamePrice mocks base method.
func (m *MockGameMgr) NamePrice(arg0 context.Context, arg1 uint32, arg2 int64, arg3 uint32, arg4 offer_room.OfferRoomNamePriceOperationType) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "NamePrice", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(error)
	return ret0
}

// NamePrice indicates an expected call of NamePrice.
func (mr *MockGameMgrMockRecorder) NamePrice(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "NamePrice", reflect.TypeOf((*MockGameMgr)(nil).NamePrice), arg0, arg1, arg2, arg3, arg4)
}

// Settle mocks base method.
func (m *MockGameMgr) Settle(arg0 context.Context, arg1 uint32, arg2 int64, arg3 uint32, arg4 string) (*offer_room0.CurOfferingGameInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Settle", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(*offer_room0.CurOfferingGameInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Settle indicates an expected call of Settle.
func (mr *MockGameMgrMockRecorder) Settle(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Settle", reflect.TypeOf((*MockGameMgr)(nil).Settle), arg0, arg1, arg2, arg3, arg4)
}

// SubmitOfferingSetting mocks base method.
func (m *MockGameMgr) SubmitOfferingSetting(arg0 context.Context, arg1 *offer_room0.SubmitOfferingSettingRequest) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SubmitOfferingSetting", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// SubmitOfferingSetting indicates an expected call of SubmitOfferingSetting.
func (mr *MockGameMgrMockRecorder) SubmitOfferingSetting(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SubmitOfferingSetting", reflect.TypeOf((*MockGameMgr)(nil).SubmitOfferingSetting), arg0, arg1)
}

// TShellCheck mocks base method.
func (m *MockGameMgr) TShellCheck(arg0 context.Context, arg1 *grpc.ServiceInfo, arg2 uint32, arg3 string) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TShellCheck", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// TShellCheck indicates an expected call of TShellCheck.
func (mr *MockGameMgrMockRecorder) TShellCheck(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TShellCheck", reflect.TypeOf((*MockGameMgr)(nil).TShellCheck), arg0, arg1, arg2, arg3)
}

// YkwShowUpThenMergeNamePrice mocks base method.
func (m *MockGameMgr) YkwShowUpThenMergeNamePrice(arg0 context.Context, arg1, arg2, arg3 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "YkwShowUpThenMergeNamePrice", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// YkwShowUpThenMergeNamePrice indicates an expected call of YkwShowUpThenMergeNamePrice.
func (mr *MockGameMgrMockRecorder) YkwShowUpThenMergeNamePrice(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "YkwShowUpThenMergeNamePrice", reflect.TypeOf((*MockGameMgr)(nil).YkwShowUpThenMergeNamePrice), arg0, arg1, arg2, arg3)
}

// MockPushService is a mock of PushService interface.
type MockPushService struct {
	ctrl     *gomock.Controller
	recorder *MockPushServiceMockRecorder
}

// MockPushServiceMockRecorder is the mock recorder for MockPushService.
type MockPushServiceMockRecorder struct {
	mock *MockPushService
}

// NewMockPushService creates a new mock instance.
func NewMockPushService(ctrl *gomock.Controller) *MockPushService {
	mock := &MockPushService{ctrl: ctrl}
	mock.recorder = &MockPushServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockPushService) EXPECT() *MockPushServiceMockRecorder {
	return m.recorder
}

// AsyncSendRollbackImMsg mocks base method.
func (m *MockPushService) AsyncSendRollbackImMsg(arg0 int64) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "AsyncSendRollbackImMsg", arg0)
}

// AsyncSendRollbackImMsg indicates an expected call of AsyncSendRollbackImMsg.
func (mr *MockPushServiceMockRecorder) AsyncSendRollbackImMsg(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AsyncSendRollbackImMsg", reflect.TypeOf((*MockPushService)(nil).AsyncSendRollbackImMsg), arg0)
}

// PushChannelText mocks base method.
func (m *MockPushService) PushChannelText(arg0 context.Context, arg1 uint32, arg2 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PushChannelText", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// PushChannelText indicates an expected call of PushChannelText.
func (mr *MockPushServiceMockRecorder) PushChannelText(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PushChannelText", reflect.TypeOf((*MockPushService)(nil).PushChannelText), arg0, arg1, arg2)
}

// PushGameInfo mocks base method.
func (m *MockPushService) PushGameInfo(arg0 context.Context, arg1 *offer_room0.CurOfferingGameInfo) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PushGameInfo", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// PushGameInfo indicates an expected call of PushGameInfo.
func (mr *MockPushServiceMockRecorder) PushGameInfo(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PushGameInfo", reflect.TypeOf((*MockPushService)(nil).PushGameInfo), arg0, arg1)
}

// PushGameInfoWithNameplateUpdateInfo mocks base method.
func (m *MockPushService) PushGameInfoWithNameplateUpdateInfo(arg0 context.Context, arg1 *offer_room0.CurOfferingGameInfo, arg2 map[uint32]uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PushGameInfoWithNameplateUpdateInfo", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// PushGameInfoWithNameplateUpdateInfo indicates an expected call of PushGameInfoWithNameplateUpdateInfo.
func (mr *MockPushServiceMockRecorder) PushGameInfoWithNameplateUpdateInfo(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PushGameInfoWithNameplateUpdateInfo", reflect.TypeOf((*MockPushService)(nil).PushGameInfoWithNameplateUpdateInfo), arg0, arg1, arg2)
}

// PushMsgToChannel mocks base method.
func (m *MockPushService) PushMsgToChannel(arg0 context.Context, arg1 uint32, arg2 channel.ChannelMsgType, arg3 string, arg4 []byte) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PushMsgToChannel", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(error)
	return ret0
}

// PushMsgToChannel indicates an expected call of PushMsgToChannel.
func (mr *MockPushServiceMockRecorder) PushMsgToChannel(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PushMsgToChannel", reflect.TypeOf((*MockPushService)(nil).PushMsgToChannel), arg0, arg1, arg2, arg3, arg4)
}

// PushMsgToUsers mocks base method.
func (m *MockPushService) PushMsgToUsers(arg0 context.Context, arg1 []uint32, arg2 push.PushMessage_CMD_TYPE, arg3 string, arg4 proto.MessageV1) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PushMsgToUsers", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(error)
	return ret0
}

// PushMsgToUsers indicates an expected call of PushMsgToUsers.
func (mr *MockPushServiceMockRecorder) PushMsgToUsers(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PushMsgToUsers", reflect.TypeOf((*MockPushService)(nil).PushMsgToUsers), arg0, arg1, arg2, arg3, arg4)
}

// PushNamePriceMsg mocks base method.
func (m *MockPushService) PushNamePriceMsg(arg0 context.Context, arg1, arg2 uint32, arg3 *app.UserProfile, arg4 *offer_room0.NamePriceResponse, arg5 offer_room.OfferRoomNamePriceOperationType) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PushNamePriceMsg", arg0, arg1, arg2, arg3, arg4, arg5)
	ret0, _ := ret[0].(error)
	return ret0
}

// PushNamePriceMsg indicates an expected call of PushNamePriceMsg.
func (mr *MockPushServiceMockRecorder) PushNamePriceMsg(arg0, arg1, arg2, arg3, arg4, arg5 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PushNamePriceMsg", reflect.TypeOf((*MockPushService)(nil).PushNamePriceMsg), arg0, arg1, arg2, arg3, arg4, arg5)
}

// Send1V1ImMsg mocks base method.
func (m *MockPushService) Send1V1ImMsg(arg0 context.Context, arg1, arg2 *app.UserProfile, arg3, arg4, arg5 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Send1V1ImMsg", arg0, arg1, arg2, arg3, arg4, arg5)
	ret0, _ := ret[0].(error)
	return ret0
}

// Send1V1ImMsg indicates an expected call of Send1V1ImMsg.
func (mr *MockPushServiceMockRecorder) Send1V1ImMsg(arg0, arg1, arg2, arg3, arg4, arg5 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Send1V1ImMsg", reflect.TypeOf((*MockPushService)(nil).Send1V1ImMsg), arg0, arg1, arg2, arg3, arg4, arg5)
}

// MockRelationshipMgr is a mock of RelationshipMgr interface.
type MockRelationshipMgr struct {
	ctrl     *gomock.Controller
	recorder *MockRelationshipMgrMockRecorder
}

// MockRelationshipMgrMockRecorder is the mock recorder for MockRelationshipMgr.
type MockRelationshipMgrMockRecorder struct {
	mock *MockRelationshipMgr
}

// NewMockRelationshipMgr creates a new mock instance.
func NewMockRelationshipMgr(ctrl *gomock.Controller) *MockRelationshipMgr {
	mock := &MockRelationshipMgr{ctrl: ctrl}
	mock.recorder = &MockRelationshipMgrMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockRelationshipMgr) EXPECT() *MockRelationshipMgrMockRecorder {
	return m.recorder
}

// CardInfo mocks base method.
func (m *MockRelationshipMgr) CardInfo(arg0 context.Context, arg1 *offer_room.OfferRoomCardInfoRequest, arg2 uint32) (*offer_room.OfferRoomCardInfoResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CardInfo", arg0, arg1, arg2)
	ret0, _ := ret[0].(*offer_room.OfferRoomCardInfoResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CardInfo indicates an expected call of CardInfo.
func (mr *MockRelationshipMgrMockRecorder) CardInfo(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CardInfo", reflect.TypeOf((*MockRelationshipMgr)(nil).CardInfo), arg0, arg1, arg2)
}

// Create mocks base method.
func (m *MockRelationshipMgr) Create(arg0 context.Context, arg1 *offer_room0.CurOfferingGameInfo) (map[uint32]uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", arg0, arg1)
	ret0, _ := ret[0].(map[uint32]uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Create indicates an expected call of Create.
func (mr *MockRelationshipMgrMockRecorder) Create(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockRelationshipMgr)(nil).Create), arg0, arg1)
}

// DeleteRelationship mocks base method.
func (m *MockRelationshipMgr) DeleteRelationship(arg0 context.Context, arg1 *offer_room.OfferRoomDeleteRelationshipRequest, arg2 uint32) (*offer_room.OfferRoomDeleteRelationshipResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteRelationship", arg0, arg1, arg2)
	ret0, _ := ret[0].(*offer_room.OfferRoomDeleteRelationshipResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteRelationship indicates an expected call of DeleteRelationship.
func (mr *MockRelationshipMgrMockRecorder) DeleteRelationship(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteRelationship", reflect.TypeOf((*MockRelationshipMgr)(nil).DeleteRelationship), arg0, arg1, arg2)
}

// GetNameplateConfig mocks base method.
func (m *MockRelationshipMgr) GetNameplateConfig(arg0 context.Context) (*offer_room.OfferRoomNameplateConfigResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetNameplateConfig", arg0)
	ret0, _ := ret[0].(*offer_room.OfferRoomNameplateConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNameplateConfig indicates an expected call of GetNameplateConfig.
func (mr *MockRelationshipMgrMockRecorder) GetNameplateConfig(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNameplateConfig", reflect.TypeOf((*MockRelationshipMgr)(nil).GetNameplateConfig), arg0)
}

// GetUserNameplateInfo mocks base method.
func (m *MockRelationshipMgr) GetUserNameplateInfo(arg0 context.Context, arg1, arg2, arg3 uint32) (*offer_room.OfferRoomGetUserNameplateInfoResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserNameplateInfo", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*offer_room.OfferRoomGetUserNameplateInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserNameplateInfo indicates an expected call of GetUserNameplateInfo.
func (mr *MockRelationshipMgrMockRecorder) GetUserNameplateInfo(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserNameplateInfo", reflect.TypeOf((*MockRelationshipMgr)(nil).GetUserNameplateInfo), arg0, arg1, arg2, arg3)
}

// OfferingRelationships mocks base method.
func (m *MockRelationshipMgr) OfferingRelationships(arg0 context.Context, arg1 *offer_room.OfferRoomOfferingRelationshipsRequest, arg2 uint32) (*offer_room.OfferRoomOfferingRelationshipsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "OfferingRelationships", arg0, arg1, arg2)
	ret0, _ := ret[0].(*offer_room.OfferRoomOfferingRelationshipsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// OfferingRelationships indicates an expected call of OfferingRelationships.
func (mr *MockRelationshipMgrMockRecorder) OfferingRelationships(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "OfferingRelationships", reflect.TypeOf((*MockRelationshipMgr)(nil).OfferingRelationships), arg0, arg1, arg2)
}

// ValidateRelationshipName mocks base method.
func (m *MockRelationshipMgr) ValidateRelationshipName(arg0 context.Context, arg1 *grpc.ServiceInfo, arg2 string) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ValidateRelationshipName", arg0, arg1, arg2)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ValidateRelationshipName indicates an expected call of ValidateRelationshipName.
func (mr *MockRelationshipMgrMockRecorder) ValidateRelationshipName(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ValidateRelationshipName", reflect.TypeOf((*MockRelationshipMgr)(nil).ValidateRelationshipName), arg0, arg1, arg2)
}
