// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/services/tt-rev/cat-canteen/internal/conf (interfaces: IBusinessConfManager)

// Package mocks is a generated GoMock package.
package mocks

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	conf "golang.52tt.com/services/tt-rev/cat-canteen/internal/conf"
)

// MockIBusinessConfManager is a mock of IBusinessConfManager interface.
type MockIBusinessConfManager struct {
	ctrl     *gomock.Controller
	recorder *MockIBusinessConfManagerMockRecorder
}

// MockIBusinessConfManagerMockRecorder is the mock recorder for MockIBusinessConfManager.
type MockIBusinessConfManagerMockRecorder struct {
	mock *MockIBusinessConfManager
}

// NewMockIBusinessConfManager creates a new mock instance.
func NewMockIBusinessConfManager(ctrl *gomock.Controller) *MockIBusinessConfManager {
	mock := &MockIBusinessConfManager{ctrl: ctrl}
	mock.recorder = &MockIBusinessConfManagerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIBusinessConfManager) EXPECT() *MockIBusinessConfManagerMockRecorder {
	return m.recorder
}

// Close mocks base method.
func (m *MockIBusinessConfManager) Close() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Close")
}

// Close indicates an expected call of Close.
func (mr *MockIBusinessConfManagerMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockIBusinessConfManager)(nil).Close))
}

// GetAwardCenterAppId mocks base method.
func (m *MockIBusinessConfManager) GetAwardCenterAppId() uint32 {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAwardCenterAppId")
	ret0, _ := ret[0].(uint32)
	return ret0
}

// GetAwardCenterAppId indicates an expected call of GetAwardCenterAppId.
func (mr *MockIBusinessConfManagerMockRecorder) GetAwardCenterAppId() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAwardCenterAppId", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetAwardCenterAppId))
}

// GetBackSenderInfo mocks base method.
func (m *MockIBusinessConfManager) GetBackSenderInfo() (uint32, string) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBackSenderInfo")
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(string)
	return ret0, ret1
}

// GetBackSenderInfo indicates an expected call of GetBackSenderInfo.
func (mr *MockIBusinessConfManagerMockRecorder) GetBackSenderInfo() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBackSenderInfo", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetBackSenderInfo))
}

// GetBingoFormatTextList mocks base method.
func (m *MockIBusinessConfManager) GetBingoFormatTextList() []string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBingoFormatTextList")
	ret0, _ := ret[0].([]string)
	return ret0
}

// GetBingoFormatTextList indicates an expected call of GetBingoFormatTextList.
func (mr *MockIBusinessConfManagerMockRecorder) GetBingoFormatTextList() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBingoFormatTextList", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetBingoFormatTextList))
}

// GetBingoTtMsg mocks base method.
func (m *MockIBusinessConfManager) GetBingoTtMsg() string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBingoTtMsg")
	ret0, _ := ret[0].(string)
	return ret0
}

// GetBingoTtMsg indicates an expected call of GetBingoTtMsg.
func (mr *MockIBusinessConfManagerMockRecorder) GetBingoTtMsg() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBingoTtMsg", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetBingoTtMsg))
}

// GetBingoUserSearchAble mocks base method.
func (m *MockIBusinessConfManager) GetBingoUserSearchAble() bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBingoUserSearchAble")
	ret0, _ := ret[0].(bool)
	return ret0
}

// GetBingoUserSearchAble indicates an expected call of GetBingoUserSearchAble.
func (mr *MockIBusinessConfManagerMockRecorder) GetBingoUserSearchAble() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBingoUserSearchAble", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetBingoUserSearchAble))
}

// GetBreakingNewsLayBackSec mocks base method.
func (m *MockIBusinessConfManager) GetBreakingNewsLayBackSec() uint32 {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBreakingNewsLayBackSec")
	ret0, _ := ret[0].(uint32)
	return ret0
}

// GetBreakingNewsLayBackSec indicates an expected call of GetBreakingNewsLayBackSec.
func (mr *MockIBusinessConfManagerMockRecorder) GetBreakingNewsLayBackSec() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBreakingNewsLayBackSec", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetBreakingNewsLayBackSec))
}

// GetCanLevel mocks base method.
func (m *MockIBusinessConfManager) GetCanLevel(arg0 float32) uint32 {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCanLevel", arg0)
	ret0, _ := ret[0].(uint32)
	return ret0
}

// GetCanLevel indicates an expected call of GetCanLevel.
func (mr *MockIBusinessConfManagerMockRecorder) GetCanLevel(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCanLevel", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetCanLevel), arg0)
}

// GetCanResourcesCfg mocks base method.
func (m *MockIBusinessConfManager) GetCanResourcesCfg() *conf.ResourcesCfg {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCanResourcesCfg")
	ret0, _ := ret[0].(*conf.ResourcesCfg)
	return ret0
}

// GetCanResourcesCfg indicates an expected call of GetCanResourcesCfg.
func (mr *MockIBusinessConfManagerMockRecorder) GetCanResourcesCfg() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCanResourcesCfg", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetCanResourcesCfg))
}

// GetChancePackId mocks base method.
func (m *MockIBusinessConfManager) GetChancePackId() uint32 {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChancePackId")
	ret0, _ := ret[0].(uint32)
	return ret0
}

// GetChancePackId indicates an expected call of GetChancePackId.
func (mr *MockIBusinessConfManagerMockRecorder) GetChancePackId() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChancePackId", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetChancePackId))
}

// GetEncourageTextList mocks base method.
func (m *MockIBusinessConfManager) GetEncourageTextList() []string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetEncourageTextList")
	ret0, _ := ret[0].([]string)
	return ret0
}

// GetEncourageTextList indicates an expected call of GetEncourageTextList.
func (mr *MockIBusinessConfManagerMockRecorder) GetEncourageTextList() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetEncourageTextList", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetEncourageTextList))
}

// GetExpirePropNotifyEnable mocks base method.
func (m *MockIBusinessConfManager) GetExpirePropNotifyEnable() bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetExpirePropNotifyEnable")
	ret0, _ := ret[0].(bool)
	return ret0
}

// GetExpirePropNotifyEnable indicates an expected call of GetExpirePropNotifyEnable.
func (mr *MockIBusinessConfManagerMockRecorder) GetExpirePropNotifyEnable() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetExpirePropNotifyEnable", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetExpirePropNotifyEnable))
}

// GetFeiShuRobotUrl mocks base method.
func (m *MockIBusinessConfManager) GetFeiShuRobotUrl() string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetFeiShuRobotUrl")
	ret0, _ := ret[0].(string)
	return ret0
}

// GetFeiShuRobotUrl indicates an expected call of GetFeiShuRobotUrl.
func (mr *MockIBusinessConfManagerMockRecorder) GetFeiShuRobotUrl() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFeiShuRobotUrl", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetFeiShuRobotUrl))
}

// GetGuaranteedExpiredSec mocks base method.
func (m *MockIBusinessConfManager) GetGuaranteedExpiredSec() uint32 {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGuaranteedExpiredSec")
	ret0, _ := ret[0].(uint32)
	return ret0
}

// GetGuaranteedExpiredSec indicates an expected call of GetGuaranteedExpiredSec.
func (mr *MockIBusinessConfManagerMockRecorder) GetGuaranteedExpiredSec() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGuaranteedExpiredSec", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetGuaranteedExpiredSec))
}

// GetHistoryProfitExtract mocks base method.
func (m *MockIBusinessConfManager) GetHistoryProfitExtract() int64 {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetHistoryProfitExtract")
	ret0, _ := ret[0].(int64)
	return ret0
}

// GetHistoryProfitExtract indicates an expected call of GetHistoryProfitExtract.
func (mr *MockIBusinessConfManagerMockRecorder) GetHistoryProfitExtract() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetHistoryProfitExtract", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetHistoryProfitExtract))
}

// GetHistoryProfitFusingMin mocks base method.
func (m *MockIBusinessConfManager) GetHistoryProfitFusingMin() int64 {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetHistoryProfitFusingMin")
	ret0, _ := ret[0].(int64)
	return ret0
}

// GetHistoryProfitFusingMin indicates an expected call of GetHistoryProfitFusingMin.
func (mr *MockIBusinessConfManagerMockRecorder) GetHistoryProfitFusingMin() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetHistoryProfitFusingMin", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetHistoryProfitFusingMin))
}

// GetHistoryProfitUpperLimit mocks base method.
func (m *MockIBusinessConfManager) GetHistoryProfitUpperLimit() int64 {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetHistoryProfitUpperLimit")
	ret0, _ := ret[0].(int64)
	return ret0
}

// GetHistoryProfitUpperLimit indicates an expected call of GetHistoryProfitUpperLimit.
func (mr *MockIBusinessConfManagerMockRecorder) GetHistoryProfitUpperLimit() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetHistoryProfitUpperLimit", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetHistoryProfitUpperLimit))
}

// GetHistoryProfitWarnMin mocks base method.
func (m *MockIBusinessConfManager) GetHistoryProfitWarnMin() int64 {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetHistoryProfitWarnMin")
	ret0, _ := ret[0].(int64)
	return ret0
}

// GetHistoryProfitWarnMin indicates an expected call of GetHistoryProfitWarnMin.
func (mr *MockIBusinessConfManagerMockRecorder) GetHistoryProfitWarnMin() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetHistoryProfitWarnMin", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetHistoryProfitWarnMin))
}

// GetHourProfitFusingMin mocks base method.
func (m *MockIBusinessConfManager) GetHourProfitFusingMin() int64 {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetHourProfitFusingMin")
	ret0, _ := ret[0].(int64)
	return ret0
}

// GetHourProfitFusingMin indicates an expected call of GetHourProfitFusingMin.
func (mr *MockIBusinessConfManagerMockRecorder) GetHourProfitFusingMin() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetHourProfitFusingMin", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetHourProfitFusingMin))
}

// GetHourProfitWarnMin mocks base method.
func (m *MockIBusinessConfManager) GetHourProfitWarnMin() int64 {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetHourProfitWarnMin")
	ret0, _ := ret[0].(int64)
	return ret0
}

// GetHourProfitWarnMin indicates an expected call of GetHourProfitWarnMin.
func (mr *MockIBusinessConfManagerMockRecorder) GetHourProfitWarnMin() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetHourProfitWarnMin", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetHourProfitWarnMin))
}

// GetInternalSecret mocks base method.
func (m *MockIBusinessConfManager) GetInternalSecret() string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetInternalSecret")
	ret0, _ := ret[0].(string)
	return ret0
}

// GetInternalSecret indicates an expected call of GetInternalSecret.
func (mr *MockIBusinessConfManagerMockRecorder) GetInternalSecret() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetInternalSecret", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetInternalSecret))
}

// GetLevelCfg mocks base method.
func (m *MockIBusinessConfManager) GetLevelCfg(arg0 uint32) *conf.LevelCfg {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLevelCfg", arg0)
	ret0, _ := ret[0].(*conf.LevelCfg)
	return ret0
}

// GetLevelCfg indicates an expected call of GetLevelCfg.
func (mr *MockIBusinessConfManagerMockRecorder) GetLevelCfg(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLevelCfg", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetLevelCfg), arg0)
}

// GetLevelCfgList mocks base method.
func (m *MockIBusinessConfManager) GetLevelCfgList() []*conf.LevelCfg {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLevelCfgList")
	ret0, _ := ret[0].([]*conf.LevelCfg)
	return ret0
}

// GetLevelCfgList indicates an expected call of GetLevelCfgList.
func (mr *MockIBusinessConfManagerMockRecorder) GetLevelCfgList() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLevelCfgList", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetLevelCfgList))
}

// GetLotteryDataReportEnable mocks base method.
func (m *MockIBusinessConfManager) GetLotteryDataReportEnable() bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLotteryDataReportEnable")
	ret0, _ := ret[0].(bool)
	return ret0
}

// GetLotteryDataReportEnable indicates an expected call of GetLotteryDataReportEnable.
func (mr *MockIBusinessConfManagerMockRecorder) GetLotteryDataReportEnable() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLotteryDataReportEnable", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetLotteryDataReportEnable))
}

// GetMaxNShowEnable mocks base method.
func (m *MockIBusinessConfManager) GetMaxNShowEnable() bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMaxNShowEnable")
	ret0, _ := ret[0].(bool)
	return ret0
}

// GetMaxNShowEnable indicates an expected call of GetMaxNShowEnable.
func (mr *MockIBusinessConfManagerMockRecorder) GetMaxNShowEnable() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMaxNShowEnable", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetMaxNShowEnable))
}

// GetMinAvgProfit mocks base method.
func (m *MockIBusinessConfManager) GetMinAvgProfit() int {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMinAvgProfit")
	ret0, _ := ret[0].(int)
	return ret0
}

// GetMinAvgProfit indicates an expected call of GetMinAvgProfit.
func (mr *MockIBusinessConfManagerMockRecorder) GetMinAvgProfit() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMinAvgProfit", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetMinAvgProfit))
}

// GetNeedBreakingNewsValueLimit mocks base method.
func (m *MockIBusinessConfManager) GetNeedBreakingNewsValueLimit() uint32 {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetNeedBreakingNewsValueLimit")
	ret0, _ := ret[0].(uint32)
	return ret0
}

// GetNeedBreakingNewsValueLimit indicates an expected call of GetNeedBreakingNewsValueLimit.
func (mr *MockIBusinessConfManagerMockRecorder) GetNeedBreakingNewsValueLimit() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNeedBreakingNewsValueLimit", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetNeedBreakingNewsValueLimit))
}

// GetPayAppId mocks base method.
func (m *MockIBusinessConfManager) GetPayAppId() string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPayAppId")
	ret0, _ := ret[0].(string)
	return ret0
}

// GetPayAppId indicates an expected call of GetPayAppId.
func (mr *MockIBusinessConfManagerMockRecorder) GetPayAppId() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPayAppId", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetPayAppId))
}

// GetPoolSimulateCnt mocks base method.
func (m *MockIBusinessConfManager) GetPoolSimulateCnt() uint32 {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPoolSimulateCnt")
	ret0, _ := ret[0].(uint32)
	return ret0
}

// GetPoolSimulateCnt indicates an expected call of GetPoolSimulateCnt.
func (mr *MockIBusinessConfManagerMockRecorder) GetPoolSimulateCnt() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPoolSimulateCnt", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetPoolSimulateCnt))
}

// GetPropCfgMap mocks base method.
func (m *MockIBusinessConfManager) GetPropCfgMap() map[uint32]conf.PropCfg {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPropCfgMap")
	ret0, _ := ret[0].(map[uint32]conf.PropCfg)
	return ret0
}

// GetPropCfgMap indicates an expected call of GetPropCfgMap.
func (mr *MockIBusinessConfManagerMockRecorder) GetPropCfgMap() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPropCfgMap", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetPropCfgMap))
}

// GetPropDurationDay mocks base method.
func (m *MockIBusinessConfManager) GetPropDurationDay() uint32 {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPropDurationDay")
	ret0, _ := ret[0].(uint32)
	return ret0
}

// GetPropDurationDay indicates an expected call of GetPropDurationDay.
func (mr *MockIBusinessConfManagerMockRecorder) GetPropDurationDay() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPropDurationDay", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetPropDurationDay))
}

// GetReissueAwardHour mocks base method.
func (m *MockIBusinessConfManager) GetReissueAwardHour() uint32 {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetReissueAwardHour")
	ret0, _ := ret[0].(uint32)
	return ret0
}

// GetReissueAwardHour indicates an expected call of GetReissueAwardHour.
func (mr *MockIBusinessConfManagerMockRecorder) GetReissueAwardHour() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetReissueAwardHour", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetReissueAwardHour))
}

// GetTestMode mocks base method.
func (m *MockIBusinessConfManager) GetTestMode() bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTestMode")
	ret0, _ := ret[0].(bool)
	return ret0
}

// GetTestMode indicates an expected call of GetTestMode.
func (mr *MockIBusinessConfManagerMockRecorder) GetTestMode() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTestMode", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetTestMode))
}

// GetTestPayUidList mocks base method.
func (m *MockIBusinessConfManager) GetTestPayUidList() []uint32 {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTestPayUidList")
	ret0, _ := ret[0].([]uint32)
	return ret0
}

// GetTestPayUidList indicates an expected call of GetTestPayUidList.
func (mr *MockIBusinessConfManagerMockRecorder) GetTestPayUidList() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTestPayUidList", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetTestPayUidList))
}

// GetUserInvestFlagExpireDay mocks base method.
func (m *MockIBusinessConfManager) GetUserInvestFlagExpireDay() uint32 {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserInvestFlagExpireDay")
	ret0, _ := ret[0].(uint32)
	return ret0
}

// GetUserInvestFlagExpireDay indicates an expected call of GetUserInvestFlagExpireDay.
func (mr *MockIBusinessConfManagerMockRecorder) GetUserInvestFlagExpireDay() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserInvestFlagExpireDay", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetUserInvestFlagExpireDay))
}

// NeedBingoBreakingNews mocks base method.
func (m *MockIBusinessConfManager) NeedBingoBreakingNews() bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "NeedBingoBreakingNews")
	ret0, _ := ret[0].(bool)
	return ret0
}

// NeedBingoBreakingNews indicates an expected call of NeedBingoBreakingNews.
func (mr *MockIBusinessConfManagerMockRecorder) NeedBingoBreakingNews() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "NeedBingoBreakingNews", reflect.TypeOf((*MockIBusinessConfManager)(nil).NeedBingoBreakingNews))
}

// Reload mocks base method.
func (m *MockIBusinessConfManager) Reload(arg0 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Reload", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// Reload indicates an expected call of Reload.
func (mr *MockIBusinessConfManagerMockRecorder) Reload(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Reload", reflect.TypeOf((*MockIBusinessConfManager)(nil).Reload), arg0)
}

// Watch mocks base method.
func (m *MockIBusinessConfManager) Watch(arg0 string) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Watch", arg0)
}

// Watch indicates an expected call of Watch.
func (mr *MockIBusinessConfManagerMockRecorder) Watch(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Watch", reflect.TypeOf((*MockIBusinessConfManager)(nil).Watch), arg0)
}
