package lottery

import (
	"context"
	"github.com/golang/mock/gomock"
	"golang.52tt.com/services/tt-rev/cat-canteen/internal/mocks"
	"golang.52tt.com/services/tt-rev/cat-canteen/internal/mysql"
	"testing"
)

var mockStore *mocks.MockIStore

//var mockBc *mocks.MockIBusinessConfManager

func newTestLottery(t *testing.T) *Lottery {
	ctl := gomock.NewController(t)
	mockStore = mocks.NewMockIStore(ctl)
	//mockBc = mocks.NewMockIBusinessConfManager(ctl)

	return &Lottery{
		store:           mockStore,
		poolList:        make([]Pool, 0),
		historyPrizeMap: make(map[uint32]*Prize),
	}
}

func TestLottery_PrizeDraw(t *testing.T) {
	l := newTestLottery(t)

	LevelConfList := []*mysql.LevelConf{
		{
			Id:          1,
			LevelId:     1,
			LevelName:   "test1",
			ChancePerOp: 1,
			MountsId:    "1",
			MaxN:        50,
		},
		{
			Id:          2,
			LevelId:     2,
			LevelName:   "test2",
			ChancePerOp: 2,
			MountsId:    "2",
		},
	}

	prizeList := []*mysql.Prize{
		{
			LevelId:        1,
			ResultType:     1,
			PackId:         1,
			PackWorth:      100,
			PackName:       "test",
			PackGiftAmount: 1,
			GiftId:         1,
			Weight:         1,
		},
		{
			LevelId:        1,
			ResultType:     2,
			PackId:         2,
			PackWorth:      100,
			PackName:       "test",
			PackGiftAmount: 1,
			GiftId:         2,
			Weight:         10,
		},
		{
			LevelId:        2,
			ResultType:     2,
			PackId:         1,
			PackWorth:      100,
			PackName:       "test",
			PackGiftAmount: 1,
			GiftId:         1,
			Weight:         1,
		},
		{
			LevelId:        2,
			ResultType:     3,
			PackId:         2,
			PackWorth:      100,
			PackName:       "test",
			PackGiftAmount: 1,
			GiftId:         2,
			Weight:         10,
		},
	}

	gomock.InOrder(
		mockStore.EXPECT().GetPrizeUpdateVersion(gomock.Any(), gomock.Any()).Return(int64(1), nil),
		mockStore.EXPECT().GetLevelInfoUpdateVersion(gomock.Any(), gomock.Any()).Return(int64(1), nil),
		mockStore.EXPECT().GetLevelInfoEffectiveInOrder(gomock.Any(), gomock.Any(), gomock.Any()).Return(LevelConfList, nil),
		mockStore.EXPECT().GetEffectPrizeList(gomock.Any(), gomock.Any()).Return(prizeList, nil),
		//mockBc.EXPECT().GetLevelCfg(gomock.Any()).Return(&conf.LevelCfg{}).AnyTimes(),
		mockStore.EXPECT().GetAllPrizeList(gomock.Any()).Return(prizeList, nil),
	)
	t.Run("CheckLotteryUpdate", func(t *testing.T) {
		err := l.CheckLotteryUpdate()
		if err != nil {
			t.Errorf("CheckLotteryUpdate fail. err:%v", err)
		}

		cfgList := l.GetLevelCfgList()
		for _, info := range cfgList {
			t.Logf("PrizeDraw %+v", info)
		}

		list, val, goalVal, err := l.PrizeDraw(context.Background(), PrizeDrawReq{LevelId: 2, Step: 1}, 1)
		if err != nil {
			t.Errorf("PrizeDraw fail. err:%v", err)
		}

		for _, info := range list {
			t.Logf("PrizeDraw %+v %+v", info.Pack, info.Mount)
		}

		t.Logf("PrizeDraw %d, %d", val, goalVal)
	})

}
