package entity

import (
    "testing"
    "time"
)

func Test_getNextSevenDay(t *testing.T) {
    now := time.Now()
    timeMap := getNextSevenDay(now)

    for k, v := range timeMap {
        t.Logf("k=%v,v=%v", k, v)
    }

    for i := 0; i < 7; i++ {
        t.Logf("i=%v,now=%v, weekday:%v", i, now.AddDate(0, 0, i), uint32(now.AddDate(0, 0, i).Weekday()))

    }
}

func Test_getSixDayBefore(t *testing.T) {
    now := time.Now()
    t.Log("now:", now)
    // 获取60天前的时间
    t.Log("60天前的时间:", now.AddDate(0, 0, -60))
}

func TestActivityConf_GetActivityTime(t *testing.T) {
    now := time.Now()

    test1DayTime := now
    test1DayBeginSec := uint32(now.Hour()*3600+now.Minute()*60+now.Second()+10) + 10
    test1DayEndSec := uint32(now.Hour()*3600+now.Minute()*60+now.Second()+10) + 100

    test2DayTime := now
    test2DayBeginSec := uint32(now.Hour()*3600+now.Minute()*60+now.Second()+10) - 10
    test2DayEndSec := uint32(now.Hour()*3600+now.Minute()*60+now.Second()+10) + 100

    test3DayTime := now.AddDate(0, 0, 1) // 假设后一天有活动
    test3DayBeginSec := uint32(now.Hour()*3600+now.Minute()*60+now.Second()+10) - 100
    test3DayEndSec := uint32(now.Hour()*3600+now.Minute()*60+now.Second()+10) - 10

    // 1. 当天是开启日，活动已结束，下一个开启日为下一个相同的weekDay的相同时间段
    test4DayTime := now.AddDate(0, 0, 7)
    t.Log("test4DayTime:", test4DayTime)

    type fields struct {
        ActivityId     uint32
        ActivityName   string
        ActivityBegin  int64
        ActivityEnd    int64
        DayTimeList    []*DayTimeRange
        TrainList      []*TrainInfo
        ArrangeVersion string
    }
    type args struct {
        now time.Time
    }
    tests := []struct {
        name              string
        fields            fields
        args              args
        wantInBigActivity bool
        wantActivityBegin int64
        wantActivityEnd   int64
    }{
        {
            name: "test1 当天是开启日， 且在开启时间段前",
            fields: fields{
                ActivityId:    1,
                ActivityName:  "111",
                ActivityBegin: now.Add(-time.Hour * 24).Unix(),
                ActivityEnd:   now.Add(time.Hour * 24).Unix(),
                DayTimeList: []*DayTimeRange{
                    {
                        DayBeginSec: test1DayBeginSec,
                        DayEndSec:   test1DayEndSec,
                        WDayList:    []uint32{uint32(time.Now().Weekday())},
                    },
                    {
                        DayBeginSec: uint32(now.Hour()*3600+now.Minute()*60+now.Second()+10) + 10,
                        DayEndSec:   uint32(now.Hour()*3600+now.Minute()*60+now.Second()+10) + 100,
                        WDayList:    []uint32{uint32(time.Now().Weekday()) - 1},
                    },
                    {
                        DayBeginSec: uint32(now.Hour()*3600+now.Minute()*60+now.Second()+10) + 10,
                        DayEndSec:   uint32(now.Hour()*3600+now.Minute()*60+now.Second()+10) + 100,
                        WDayList:    []uint32{uint32(time.Now().Weekday()) + 1},
                    },
                },
            },
            args: args{
                now: now,
            },
            wantInBigActivity: true,
            wantActivityBegin: time.Date(test1DayTime.Year(), test1DayTime.Month(), test1DayTime.Day(), 0, 0, int(test1DayBeginSec), 0, time.Local).Unix(),
            wantActivityEnd:   time.Date(test1DayTime.Year(), test1DayTime.Month(), test1DayTime.Day(), 0, 0, int(test1DayEndSec), 0, time.Local).Unix(),
        },
        {
            name: "test2 当天是开启日， 且在开启时间段内",
            fields: fields{
                ActivityId:    1,
                ActivityName:  "111",
                ActivityBegin: now.Add(-time.Hour * 24).Unix(),
                ActivityEnd:   now.Add(time.Hour * 24).Unix(),
                DayTimeList: []*DayTimeRange{
                    {
                        DayBeginSec: test2DayBeginSec,
                        DayEndSec:   test2DayEndSec,
                        WDayList:    []uint32{uint32(time.Now().Weekday() - 1)},
                    },
                    {
                        DayBeginSec: test2DayBeginSec,
                        DayEndSec:   test2DayEndSec,
                        WDayList:    []uint32{uint32(time.Now().Weekday())},
                    },
                    {
                        DayBeginSec: test2DayBeginSec,
                        DayEndSec:   test2DayEndSec,
                        WDayList:    []uint32{uint32(time.Now().Weekday() + 1)},
                    },
                },
            },
            args: args{
                now: now,
            },
            wantInBigActivity: true,
            wantActivityBegin: time.Date(test2DayTime.Year(), test2DayTime.Month(), test2DayTime.Day(), 0, 0, int(test2DayBeginSec), 0, time.Local).Unix(),
            wantActivityEnd:   time.Date(test2DayTime.Year(), test2DayTime.Month(), test2DayTime.Day(), 0, 0, int(test2DayEndSec), 0, time.Local).Unix(),
        },
        {
            name: "test3 当天是开启日， 且在开启时间段后",
            fields: fields{
                ActivityId:    1,
                ActivityName:  "111",
                ActivityBegin: now.Add(-time.Hour * 24).Unix(),
                ActivityEnd:   now.Add(time.Hour * 48).Unix(),
                DayTimeList: []*DayTimeRange{
                    {
                        DayBeginSec: test3DayBeginSec,
                        DayEndSec:   test3DayEndSec,
                        WDayList:    []uint32{uint32(time.Now().Weekday() - 1)},
                    },
                    {
                        DayBeginSec: test3DayBeginSec,
                        DayEndSec:   test3DayEndSec,
                        WDayList:    []uint32{uint32(time.Now().Weekday())},
                    },
                    {
                        DayBeginSec: test3DayBeginSec,
                        DayEndSec:   test3DayEndSec,
                        WDayList:    []uint32{uint32(time.Now().Weekday() + 1)},
                    },
                },
            },
            args: args{
                now: now,
            },
            wantInBigActivity: true,
            wantActivityBegin: time.Date(test3DayTime.Year(), test3DayTime.Month(), test3DayTime.Day(), 0, 0, int(test3DayBeginSec), 0, time.Local).Unix(),
            wantActivityEnd:   time.Date(test3DayTime.Year(), test3DayTime.Month(), test3DayTime.Day(), 0, 0, int(test3DayEndSec), 0, time.Local).Unix(),
        },
        {
            name: "test4 下个周期的当前开启下一轮",
            fields: fields{
                ActivityId:    1,
                ActivityName:  "111",
                ActivityBegin: now.Add(-time.Hour * 24).Unix(),
                ActivityEnd:   now.AddDate(0, 0, 8).Unix(),
                DayTimeList: []*DayTimeRange{
                    {
                        DayBeginSec: test3DayBeginSec,
                        DayEndSec:   test3DayEndSec,
                        WDayList:    []uint32{uint32(time.Now().Weekday())},
                    },
                },
            },
            args: args{
                now: now,
            },
            wantInBigActivity: true,
            wantActivityBegin: time.Date(test4DayTime.Year(), test4DayTime.Month(), test4DayTime.Day(), 0, 0, int(test3DayBeginSec), 0, time.Local).Unix(),
            wantActivityEnd:   time.Date(test4DayTime.Year(), test4DayTime.Month(), test4DayTime.Day(), 0, 0, int(test3DayEndSec), 0, time.Local).Unix(),
        },
        {
            name: "test5 当前大周期内，没有下一场活动了",
            fields: fields{
                ActivityId:    1,
                ActivityName:  "111",
                ActivityBegin: now.Add(-time.Hour * 24).Unix(),
                ActivityEnd:   now.AddDate(0, 0, 6).Unix(),
                DayTimeList: []*DayTimeRange{
                    {
                        DayBeginSec: test3DayBeginSec,
                        DayEndSec:   test3DayEndSec,
                        WDayList:    []uint32{uint32(time.Now().Weekday())},
                    },
                },
            },
            args: args{
                now: now,
            },
            wantInBigActivity: true,
            wantActivityBegin: 0,
            wantActivityEnd:   0,
        },
    }
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            a := &ActivityConf{
                ActivityId:     tt.fields.ActivityId,
                ActivityName:   tt.fields.ActivityName,
                ActivityBegin:  tt.fields.ActivityBegin,
                ActivityEnd:    tt.fields.ActivityEnd,
                DayTimeList:    tt.fields.DayTimeList,
                TrainList:      tt.fields.TrainList,
                ArrangeVersion: tt.fields.ArrangeVersion,
            }
            gotInBigActivity, gotActivityBegin, gotActivityEnd := a.GetActivityTime(tt.args.now)
            if gotInBigActivity != tt.wantInBigActivity {
                t.Errorf("GetActivityTime() gotInBigActivity = %v, want %v", gotInBigActivity, tt.wantInBigActivity)
            }
            if gotActivityBegin != tt.wantActivityBegin {
                t.Errorf("GetActivityTime() gotActivityBegin = %v, want %v", gotActivityBegin, tt.wantActivityBegin)
            }
            if gotActivityEnd != tt.wantActivityEnd {
                t.Errorf("GetActivityTime() gotActivityEnd = %v, want %v", gotActivityEnd, tt.wantActivityEnd)
            }
        })
    }
}
