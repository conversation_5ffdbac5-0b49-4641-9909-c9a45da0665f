// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/services/tt-rev/star-train/internal/model/activity_conf/cache (interfaces: ICache)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	redis "github.com/go-redis/redis/v8"
	gomock "github.com/golang/mock/gomock"
	entity "golang.52tt.com/services/tt-rev/star-train/internal/entity"
)

// MockICache is a mock of ICache interface.
type MockICache struct {
	ctrl     *gomock.Controller
	recorder *MockICacheMockRecorder
}

// MockICacheMockRecorder is the mock recorder for MockICache.
type MockICacheMockRecorder struct {
	mock *MockICache
}

// NewMockICache creates a new mock instance.
func NewMockICache(ctrl *gomock.Controller) *MockICache {
	mock := &MockICache{ctrl: ctrl}
	mock.recorder = &MockICacheMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockICache) EXPECT() *MockICacheMockRecorder {
	return m.recorder
}

// Close mocks base method.
func (m *MockICache) Close() error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Close")
	ret0, _ := ret[0].(error)
	return ret0
}

// Close indicates an expected call of Close.
func (mr *MockICacheMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockICache)(nil).Close))
}

// DelCurrentActivityInfo mocks base method.
func (m *MockICache) DelCurrentActivityInfo(arg0 context.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelCurrentActivityInfo", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// DelCurrentActivityInfo indicates an expected call of DelCurrentActivityInfo.
func (mr *MockICacheMockRecorder) DelCurrentActivityInfo(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelCurrentActivityInfo", reflect.TypeOf((*MockICache)(nil).DelCurrentActivityInfo), arg0)
}

// GetCurrentActivityInfo mocks base method.
func (m *MockICache) GetCurrentActivityInfo(arg0 context.Context) (*entity.ActivityConf, bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCurrentActivityInfo", arg0)
	ret0, _ := ret[0].(*entity.ActivityConf)
	ret1, _ := ret[1].(bool)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetCurrentActivityInfo indicates an expected call of GetCurrentActivityInfo.
func (mr *MockICacheMockRecorder) GetCurrentActivityInfo(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCurrentActivityInfo", reflect.TypeOf((*MockICache)(nil).GetCurrentActivityInfo), arg0)
}

// GetRedisClient mocks base method.
func (m *MockICache) GetRedisClient() redis.Cmdable {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRedisClient")
	ret0, _ := ret[0].(redis.Cmdable)
	return ret0
}

// GetRedisClient indicates an expected call of GetRedisClient.
func (mr *MockICacheMockRecorder) GetRedisClient() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRedisClient", reflect.TypeOf((*MockICache)(nil).GetRedisClient))
}

// SetCurrentActivityInfo mocks base method.
func (m *MockICache) SetCurrentActivityInfo(arg0 context.Context, arg1 *entity.ActivityConf) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetCurrentActivityInfo", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetCurrentActivityInfo indicates an expected call of SetCurrentActivityInfo.
func (mr *MockICacheMockRecorder) SetCurrentActivityInfo(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetCurrentActivityInfo", reflect.TypeOf((*MockICache)(nil).SetCurrentActivityInfo), arg0, arg1)
}
