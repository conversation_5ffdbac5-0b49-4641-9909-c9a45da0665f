package cache

import (
    "context"
    "encoding/json"
    "fmt"
    "time"

    "gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/redis"
    "golang.52tt.com/pkg/log"
    pb "golang.52tt.com/protocol/services/glory-lottery"
)

const (
    noFlashCountKey      = "no_flash_count:%d"
    noPrizeCountKey      = "no_prize_count:%d"
    userLotteryRecordKey = "user_lottery_record:%d" // 用户前50条抽奖记录
    winningCarouselKey   = "winning_carousel"
    userConsumeValToday  = "user_consumer_val_today:%d"
    userLotteryTimesKey  = "user_lottery_times:%d"
    userBigPrizeMark     = "user_big_prize_mark:%d"
)

func (c *Cache) IncrNoPrizeCount(ctx context.Context, uid uint32) error {
    key := fmt.Sprintf(noPrizeCountKey, uid)
    _, err := c.cmder.Pipelined(ctx, func(pipe redis.Pipeliner) error {
        err := pipe.Incr(ctx, key).Err()
        if err != nil {
            return err
        }
        return pipe.Expire(ctx, key, 90*24*time.Hour).Err()
    })
    return err
}

func (c *Cache) GetNoPrizeCount(ctx context.Context, uid uint32) (uint32, error) {
    key := fmt.Sprintf(noPrizeCountKey, uid)
    noPrizeCount, err := c.cmder.Get(ctx, key).Int()
    if err != nil && err.Error() != redis.Nil.Error() {
        return 0, err
    }
    return uint32(noPrizeCount), nil
}

func (c *Cache) DelNoPrizeCount(ctx context.Context, uid uint32) error {
    return c.cmder.Del(ctx, fmt.Sprintf(noPrizeCountKey, uid)).Err()
}

func (c *Cache) IncrNoFlashCount(ctx context.Context, uid uint32) error {
    key := fmt.Sprintf(noFlashCountKey, uid)
    _, err := c.cmder.Pipelined(ctx, func(pipe redis.Pipeliner) error {
        err := pipe.Incr(ctx, key).Err()
        if err != nil {
            return err
        }
        return pipe.Expire(ctx, key, 90*24*time.Hour).Err()
    })
    return err
}

func (c *Cache) GetNoFlashCount(ctx context.Context, uid uint32) (uint32, error) {
    key := fmt.Sprintf(noFlashCountKey, uid)
    noFlashCount, err := c.cmder.Get(ctx, key).Int()
    if err != nil && err.Error() != redis.Nil.Error() {
        return 0, err
    }
    return uint32(noFlashCount), nil
}

func (c *Cache) DelNoFlashCount(ctx context.Context, uid uint32) error {
    return c.cmder.Del(ctx, fmt.Sprintf(noFlashCountKey, uid)).Err()
}

func (c *Cache) SetMyLotteryRecord(ctx context.Context, uid uint32, data *pb.GetMyLotteryRecordResp) error {
    key := fmt.Sprintf(userLotteryRecordKey, uid)
    dataBytes, err := json.Marshal(data)
    if err != nil {
        return err
    }
    return c.cmder.Set(ctx, key, string(dataBytes), 30*time.Minute).Err()
}

func (c *Cache) DelMyLotteryRecord(ctx context.Context, uid uint32) error {
    return c.cmder.Del(ctx, fmt.Sprintf(userLotteryRecordKey, uid)).Err()
}

const (
    winningCarouselLen = 10
)

func (c *Cache) GetWinningCarousel(ctx context.Context) ([]*pb.WinningInfo, error) {
    rs := make([]*pb.WinningInfo, 0, 10)
    data, err := c.cmder.ZRange(ctx, winningCarouselKey, 0, winningCarouselLen).Result()
    if err != nil {
        return rs, err
    }
    for i := len(data) - 1; i >= 0; i-- {
        tmp := &pb.WinningInfo{}
        err = json.Unmarshal([]byte(data[i]), tmp)
        if err != nil {
            log.ErrorWithCtx(ctx, "GetWinningCarousel.Unmarshal fail, data: %s err: %v", data[i], err)
            continue
        }
        rs = append(rs, tmp)
    }

    return rs, nil
}

func (c *Cache) AddWinningCarousel(ctx context.Context, data *pb.WinningInfo) error {
    dataBytes, err := json.Marshal(data)
    if err != nil {
        return err
    }
    delKeys, err := c.cmder.ZRange(ctx, winningCarouselKey, 0, -winningCarouselLen).Result()
    if err != nil {
        return err
    }
    delKeysArgs := make([]interface{}, len(delKeys))
    for i, item := range delKeys {
        delKeysArgs[i] = item
    }
    if len(delKeys) > 0 {
        c.cmder.ZRem(ctx, winningCarouselKey, delKeysArgs...)
    }

    return c.cmder.ZAdd(ctx, winningCarouselKey, &redis.Z{
        Score:  float64(time.Now().Unix()),
        Member: string(dataBytes),
    }).Err()
}

func (c *Cache) GetUserConsumeValToday(ctx context.Context, uid uint32) (uint32, error) {
    consumeVal, err := c.cmder.Get(ctx, fmt.Sprintf(userConsumeValToday, uid)).Int64()
    if err != nil && err.Error() == redis.Nil.Error() {
        return 0, nil
    }
    if err != nil {
        return 0, err
    }

    return uint32(consumeVal), nil
}

func (c *Cache) AddUserConsumeValToday(ctx context.Context, uid, val uint32) error {
    _, err := c.cmder.Pipelined(ctx, func(pipe redis.Pipeliner) error {
        key := fmt.Sprintf(userConsumeValToday, uid)
        err := pipe.IncrBy(ctx, key, int64(val)).Err()
        if err != nil {
            return err
        }

        return pipe.Expire(ctx, key, getTodayEndingDuration()).Err()
    })
    return err
}

func (c *Cache) EnsureInitUserLotteryTimes(ctx context.Context, uid, defaultTimes uint32) (uint32, error) {
    key := fmt.Sprintf(userLotteryTimesKey, uid)
    remainTimes, err := c.cmder.Get(ctx, key).Int64()
    if err != nil && err.Error() == redis.Nil.Error() {
        // 如果当前没有初始化次数
        return defaultTimes, c.cmder.Set(ctx, key, defaultTimes, getTodayEndingDuration()).Err()
    }
    return uint32(remainTimes), err
}

func (c *Cache) ReduceUserLotteryTimes(ctx context.Context, uid uint32) error {
    nowTimes, err := c.cmder.IncrBy(ctx, fmt.Sprintf(userLotteryTimesKey, uid), -1).Result()
    if err != nil {
        return err
    }
    if nowTimes < 0 {
        return fmt.Errorf("no times")
    }
    return nil
}

func (c *Cache) GetUserLotteryTimes(ctx context.Context, uid uint32) (bool, uint32, error) {
    times, err := c.cmder.Get(ctx, fmt.Sprintf(userLotteryTimesKey, uid)).Int64()
    if err != nil {
        if err.Error() == redis.Nil.Error() {
            return false, 0, nil
        }
        return false, 0, err
    }
    return true, uint32(times), nil
}

func (c *Cache) AddUserLotteryTimes(ctx context.Context, uid, addTimes uint32) error {
    key := fmt.Sprintf(userLotteryTimesKey, uid)
    _, err := c.cmder.Pipelined(ctx, func(pipe redis.Pipeliner) error {
        err := pipe.IncrBy(ctx, key, int64(addTimes)).Err()
        if err != nil {
            return err
        }
        return pipe.Expire(ctx, key, getTodayEndingDuration()).Err()
    })
    return err
}

func (c *Cache) IsWinningBigPrizeRecent30Day(ctx context.Context, uid uint32) (bool, error) {
    _, err := c.cmder.Get(ctx, fmt.Sprintf(userBigPrizeMark, uid)).Result()
    if err != nil {
        if err.Error() == redis.Nil.Error() {
            return false, nil
        }
        return false, err
    }
    return true, nil
}

func (c *Cache) MarkWinningBigPrizeRecent30Day(ctx context.Context, uid uint32) error {
    return c.cmder.SetNX(ctx, fmt.Sprintf(userBigPrizeMark, uid), "1", 30*24*time.Hour).Err()
}
