package push

import (
	"context"
	"fmt"
	"time"

	"gitlab.ttyuyin.com/tt-infra/tyr/log"
	PushNotification "golang.52tt.com/clients/push-notification/v2"
	push "golang.52tt.com/clients/push-notification/v2"
	"golang.52tt.com/pkg/protocol"
	pbApp "golang.52tt.com/protocol/app"
	channelPB "golang.52tt.com/protocol/app/channel"
	gaPush "golang.52tt.com/protocol/app/push"
	pushPB "golang.52tt.com/protocol/services/push-notification/v2"
)

func PushMsgToChannelWithUsers(ctx context.Context, pushCli push.IClient, channelId, msgType uint32, data []byte, fromUser, toUser *pbApp.UserProfile) error {
	msg := &channelPB.ChannelBroadcastMsg{
		FromUid:     fromUser.GetUid(),
		FromAccount: fromUser.GetAccount(),
		FromNick:    fromUser.GetNickname(),
		Time:        uint64(time.Now().Unix()),
		ToChannelId: channelId,
		Type:        msgType,
		//Content:           []byte(content),
		PbOptContent:      data,
		TargetUid:         toUser.GetUid(),
		TargetAccount:     toUser.GetAccount(),
		TargetNick:        toUser.GetNickname(),
		TargetSex:         int32(toUser.GetSex()),
		FromUserProfile:   fromUser,
		TargetUserProfile: toUser,
	}

	if len(fromUser.GetPrivilege().GetAccount()) > 0 {
		msg.FromAccount = fromUser.GetPrivilege().GetAccount()
		msg.FromNick = fromUser.GetPrivilege().GetNickname()
	}

	if len(toUser.GetPrivilege().GetAccount()) > 0 {
		msg.TargetAccount = toUser.GetPrivilege().GetAccount()
		msg.TargetNick = toUser.GetPrivilege().GetNickname()
	}

	channelMsgBin, err := msg.Marshal()
	if err != nil {
		log.ErrorWithCtx(ctx, "pushMsgToChannelWithUser marshal err: %s, %+v", err.Error(), msg)
		return err
	}

	err = PushChannelBroMsgToChannels(ctx, pushCli, []uint32{channelId}, channelMsgBin)
	if err != nil {
		log.ErrorWithCtx(ctx, "pushMsgToChannelWithUser fail to SendChannelBroadcastMsg. channelId:%d err:%v", channelId, err)
		return err
	}

	log.DebugWithCtx(ctx, "pushMsgToChannelWithUser msgType:%v, channelId:%d", msgType, channelId)
	return nil
}

func PushMsgToChannel(ctx context.Context, pushCli push.IClient, channelId, msgType uint32, content string, data []byte) error {
	msg := &channelPB.ChannelBroadcastMsg{
		Time:         uint64(time.Now().Unix()),
		ToChannelId:  channelId,
		Type:         msgType,
		Content:      []byte(content),
		PbOptContent: data,
	}

	channelMsgBin, err := msg.Marshal()
	if err != nil {
		log.ErrorWithCtx(ctx, "pushMsgToChannel marshal err: %s, %+v", err.Error(), msg)
		return err
	}

	err = PushChannelBroMsgToChannels(ctx, pushCli, []uint32{channelId}, channelMsgBin)
	if err != nil {
		log.ErrorWithCtx(ctx, "pushMsgToChannel fail to SendChannelBroadcastMsg. channelId:%d err:%v", channelId, err)
		return err
	}

	log.DebugWithCtx(ctx, "pushMsgToChannel msgType:%v, channelId:%d, content:%s", msgType, channelId, content)
	return nil
}

// 房间广播消息
func PushChannelBroMsgToChannels(ctx context.Context, pushCli push.IClient, channelIds []uint32, channelMsgBin []byte) error {
	pushMessage := &gaPush.PushMessage{
		Cmd:     uint32(gaPush.PushMessage_CHANNEL_MSG_BRO),
		Content: channelMsgBin,
		SeqId:   uint32(time.Now().Unix()),
	}
	pushMessageBytes, e := pushMessage.Marshal()
	if e != nil {
		log.ErrorWithCtx(ctx, "pushChannelBroMsgToChannels Marshal channelIds:%v, err: %v", channelIds, e)
		return e
	}

	notification := &pushPB.CompositiveNotification{
		Sequence: uint32(time.Now().Unix()),
		TerminalTypeList: []uint32{
			protocol.MobileAndroidTT,
			protocol.MobileIPhoneTT,
		},
		TerminalTypePolicy: PushNotification.DefaultPolicy,
		AppId:              uint32(protocol.TT),
		ProxyNotification: &pushPB.ProxyNotification{
			Type:       uint32(pushPB.ProxyNotification_PUSH),
			Payload:    pushMessageBytes,
			Policy:     pushPB.ProxyNotification_DEFAULT,
			ExpireTime: 60,
		},
	}

	multicastMap := map[uint64]string{}
	for _, channelId := range channelIds {
		if channelId == 0 {
			continue
		}
		multicastMap[uint64(channelId)] = fmt.Sprintf("%d@channel", channelId)
	}

	if len(multicastMap) == 0 {
		return nil
	}

	err := pushCli.PushMulticasts(ctx, multicastMap, []uint32{}, notification)
	if err != nil {
		log.ErrorWithCtx(ctx, "pushChannelBroMsgToChannels fail to PushMulticasts channelIds:%v, err: %s", channelIds, err.Error())
		return err
	}

	return nil
}
