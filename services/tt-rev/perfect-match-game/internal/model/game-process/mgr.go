package game_process

import (
	"context"
	"fmt"
	"time"

	"gitlab.ttyuyin.com/avengers/tyr/compatible/proto"
	"golang.52tt.com/clients/account"
	seqgenClient "golang.52tt.com/clients/seqgen/v2"
	channelMsgClient "golang.52tt.com/pkg/channel-msg"
	"golang.52tt.com/pkg/decoration"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/store/redis"
	pbApp "golang.52tt.com/protocol/app"
	"golang.52tt.com/protocol/app/channel"
	imPB "golang.52tt.com/protocol/app/im"
	logicPb "golang.52tt.com/protocol/app/perfect-couple-match-logic"
	imApiPB "golang.52tt.com/protocol/services/im-api"
	pb "golang.52tt.com/protocol/services/perfect-match-game"
	pbRCMDPM "golang.52tt.com/protocol/services/rcmd/perfect_match"
	friendshipPB "golang.52tt.com/protocol/services/ugc/friendship"
	"golang.52tt.com/services/notify"
	"golang.52tt.com/services/tt-rev/perfect-match-game/internal/conf"
	"golang.52tt.com/services/tt-rev/perfect-match-game/internal/entity"
	"golang.52tt.com/services/tt-rev/perfect-match-game/internal/model"
	"golang.52tt.com/services/tt-rev/perfect-match-game/internal/model/game-process/cache"
	"golang.52tt.com/services/tt-rev/perfect-match-game/internal/model/game-process/phase"
	"golang.52tt.com/services/tt-rev/perfect-match-game/internal/rpc"
)

type GameProcessModel struct {
	bc       conf.IBusinessConfManager
	rpcCli   *rpc.Client
	cache    cache.IRedisCli
	cluesMgr model.ICluesModel
}

func NewGameProcessModel(rpcCli *rpc.Client, cmder redis.CmdableV2, bc conf.IBusinessConfManager, cluesMgr model.ICluesModel) (*GameProcessModel, error) {
	redisCli := cache.NewRedisCli(cmder)

	err := phase.InitPhaseHandler(bc, redisCli, rpcCli, cluesMgr)
	if err != nil {
		return nil, err
	}

	return &GameProcessModel{
		bc:       bc,
		rpcCli:   rpcCli,
		cache:    redisCli,
		cluesMgr: cluesMgr,
	}, nil
}

func (m *GameProcessModel) GameResultPubTimerHandle() error {
	ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
	defer cancel()

	list, err := m.cache.GetTimeoutResultNextPubObj(ctx, 5, time.Now())
	if err != nil {
		log.ErrorWithCtx(ctx, "GameResultPubTimerHandle fail to GetTimeoutResultNextPubObj. err:%v", err)
		return err
	}

	for _, obj := range list {
		err = m.GameResultPub(obj)
		if err != nil {
			log.ErrorWithCtx(ctx, "GameResultPubTimerHandle fail to GameResultPub. %+v, err:%v", obj, err)
			continue
		}
	}

	return nil
}

func (m *GameProcessModel) GameResultPub(obj *cache.GameResultObj) error {
	if obj == nil || obj.Cid == 0 {
		return nil
	}
	cid := obj.Cid

	ttl := 5 * time.Second
	ctx, cancel := context.WithTimeout(context.Background(), ttl)
	defer cancel()

	ok, err := m.cache.LockResultPubChannel(ctx, cid, ttl)
	if err != nil {
		log.ErrorWithCtx(ctx, "GameResultPubTimerHandle fail to LockResultPubChannel. %+v, err:%v", obj, err)
		return err
	}

	if !ok {
		return nil
	}
	defer func(cache cache.IRedisCli, ctx context.Context, cid uint32) {
		err := cache.RemoveResultNextPubObj(ctx, obj)
		if err != nil {
			log.ErrorWithCtx(ctx, "GameResultPubTimerHandle fail to RemoveResultNextPubObj. %+v, err:%v", obj, err)
		}

		err = cache.UnlockResultPubChannel(ctx, cid)
		if err != nil {
			log.ErrorWithCtx(ctx, "GameResultPubTimerHandle fail to UnlockResultPubChannel. %+v, err:%v", obj, err)
		}
	}(m.cache, ctx, cid)

	info, err := m.GetGameInfoEntity(ctx, cid)
	if err != nil {
		log.ErrorWithCtx(ctx, "GameResultPubTimerHandle fail to GetGameInfoEntity. %+v, err:%v", obj, err)
		return err
	}

	if info.Phase != uint32(logicPb.PerfectCpGamePhaseType_PERFECT_CP_GAME_PHASE_TYPE_ANNOUNCE_RESULT) {
		return nil
	}

	err = m.gameResultPubNotify(ctx, info, obj)
	if err != nil {
		log.ErrorWithCtx(ctx, "GameResultPubTimerHandle fail to gameResultPubNotify. %+v, err:%v", obj, err)
		return err
	}

	log.InfoWithCtx(ctx, "GameResultPubTimerHandle obj:%+v", obj)
	return nil
}

const (
	userKeyColor      = "#ffe2a9"
	userKeyFontSize   = 24
	leftUserKey       = "img_2.png"
	rightUserKey      = "img_3.png"
	aiPicKey          = "img_7.png"
	matchTextKey      = "img_8.png"
	matchTextFontSize = 22
	matchDateKey      = "img_9.png"
	fromAccountKey    = "img_10.png"
	fromNicknameKey   = "img_11.png"
	toAccountKey      = "img_12.png"
	toNicknameKey     = "img_13.png"
	aiPiccycleRadius  = 1024 // ai生成图画圆半径
	defaultFontColor  = "#ffffff"
	defaultCharLimit  = 0
	defaultFontSize   = 26
)

func (m *GameProcessModel) gameResultPubNotify(ctx context.Context, gameInfo *entity.GameInfo, obj *cache.GameResultObj) error {
	if obj == nil {
		return nil
	}
	var err error
	cid := obj.Cid

	if obj.NotPerfect {
		// 发公屏
		return m.rpcCli.ChannelMsgSender.MultiCast(ctx, &channelMsgClient.PushMessage{
			ChannelId: cid,
			MsgType:   uint32(channel.ChannelMsgType_PERFECT_COUPLE_GAME_SYSTEM_MSG),
			Content:   "很可惜~本场无人选中天配",
		}, true, nil)
	}

	userMap, err := m.rpcCli.UserProfileCli.BatchGetUserProfileV2(ctx, []uint32{obj.UidA, obj.UidB}, false)
	if err != nil {
		log.ErrorWithCtx(ctx, "gameResultPubNotify fail to BatchGetUserProfileV2. %+v, err:%v", obj, err)
		return err
	}
	userA, userB := userMap[obj.UidA], userMap[obj.UidB]
	maleUser, femaleUser := userA, userB
	if userA.GetSex() == uint32(account.Female) {
		maleUser, femaleUser = userB, userA
	}

	// 如果达成p1-p6从推荐获取结算背景
	var aiPic, maleText, femaleText string
	if obj.Type != uint32(pb.PerfectCpResultType_PERFECT_CP_RESULT_TYPE_UNSPECIFIED) {
		aiPic, maleText, femaleText = m.getAIResult(ctx, gameInfo, maleUser.GetUid(), femaleUser.GetUid())
	}

	// 获取结算页资源
	resultResource, aiPic, lottieSource := m.getResultResource(obj.Type, maleUser, femaleUser, aiPic,
		fmt.Sprintf("我们在天配房间 ID:%d相识相知", m.getChannelDisplayId(ctx, cid)), maleText, femaleText)

	title := "千千万万个人中我们遇见"
	content := fmt.Sprintf("%s 我们在天配房中相识", time.Now().Format("2006.01.02"))
	isPerfectMatch := (obj.BitVal & (1 << phase.PriorityPerfectMatch)) > 0
	opt := &logicPb.PerfectCpGameResultOpt{
		ChannelId:    cid,
		GameId:       gameInfo.GameId,
		User:         userA,
		ChooseUser:   userB,
		IsChooseEach: (obj.BitVal & (1 << phase.PriorityChooseEach)) > 0,
		IsNeedLink:   obj.IsNeedLink,
		EffectSource: &logicPb.EffectSource{
			EffectUrl:  lottieSource.Url,
			EffectMd5:  lottieSource.MD5,
			EffectJson: resultResource.GetDecorationJson(),
		},
		VideoPrefix:  m.bc.GetCdnPrefix(),
		ShareTitle:   title,
		ShareContent: content,
		ShareTopPic:  m.bc.GetImTopPicUrl(isPerfectMatch),
		ShareMidPic:  aiPic,
		ShareBasePic: m.bc.GetImBasePicUrl(isPerfectMatch),
	}
	if obj.Type != uint32(pb.PerfectCpResultType_PERFECT_CP_RESULT_TYPE_UNSPECIFIED) { // 达成p1-p6 触发互关
		_ = m.FollowUser(ctx, userA.GetUid(), userB.GetUid())
		_ = m.FollowUser(ctx, userB.GetUid(), userA.GetUid())
	}

	log.Infof("gameResultPubNotify, opt: %+v", opt)
	b, err := proto.Marshal(opt)
	if err != nil {
		log.ErrorWithCtx(ctx, "gameResultPubNotify proto.Marshal fail, obj:%v, err:%v", obj, err)
		return err
	}
	// 房间广播
	err = m.rpcCli.ChannelMsgSender.MultiCast(ctx, &channelMsgClient.PushMessage{
		ChannelId: cid,
		MsgType:   uint32(channel.ChannelMsgType_PERFECT_COUPLE_ANNOUNCE_RESULT),
		PBContent: b,
	}, false, nil)
	if err != nil {
		log.ErrorWithCtx(ctx, "gameResultPubNotify MultiCast fail, obj:%v, err:%v", obj, err)
	}

	if obj.Type != uint32(pb.PerfectCpResultType_PERFECT_CP_RESULT_TYPE_UNSPECIFIED) {
		// 发im消息
		imOpt := &logicPb.PerfectCpGameResultImOpt{
			ChannelId:     cid,
			GameId:        gameInfo.GameId,
			TopPic:        m.bc.GetImTopPicUrl(isPerfectMatch),
			MidPic:        aiPic,
			BasePic:       m.bc.GetImBasePicUrl(isPerfectMatch),
			Title:         title,
			Content:       content,
			BindId:        m.getChannelDisplayId(ctx, cid),
			ResultTimeSec: time.Now().Unix(),
			UserA:         maleUser,
			UserB:         femaleUser,
			EffectSource: &logicPb.EffectSource{
				EffectUrl:  lottieSource.Url,
				EffectMd5:  lottieSource.MD5,
				EffectJson: resultResource.GetDecorationJson(),
			},
			VideoPrefix: m.bc.GetCdnPrefix(),
			IsForward:   false,
		}
		err = m.sendResultImMsg(ctx, imOpt)
		if err != nil {
			log.ErrorWithCtx(ctx, "gameResultPubNotify sendResultImMsg fail, obj:%v, err:%v", obj, err)
		}

		// 发公屏
		userAMicId := uint32(0)
		userBMicId := uint32(0)
		for _, player := range gameInfo.PlayerList {
			if player.Uid == userA.GetUid() {
				userAMicId = player.MicId - 1
			}
			if player.Uid == userB.GetUid() {
				userBMicId = player.MicId - 1
			}
		}
		err = m.rpcCli.ChannelMsgSender.MultiCast(ctx, &channelMsgClient.PushMessage{
			ChannelId: cid,
			MsgType:   uint32(channel.ChannelMsgType_PERFECT_COUPLE_GAME_SYSTEM_MSG),
			Content:   fmt.Sprintf(phase.ResultType2PublicMsg[obj.Type], userAMicId, userA.GetNickname(), userBMicId, userB.GetNickname()),
		}, true, nil)
		if err != nil {
			log.ErrorWithCtx(ctx, "gameResultPubNotify MultiCast fail, obj:%v, err:%v", obj, err)
		}
	}

	return nil
}

func (m *GameProcessModel) getResultResource(ttype uint32, maleUser, femaleUser *pbApp.UserProfile,
	aiPic, matchText, maleText, femaleText string) (*decoration.Extend, string, *conf.ResultSourceLottie) {
	resource := m.bc.GetResultSource(ttype)
	resultResource := decoration.NewDecoration()
	if resource.Type != 0 { // 有获取到配置则封装json
		leftUser, rightUser := userProfile2User(maleUser), userProfile2User(femaleUser)
		resultResource.AddDecorationConfig(decoration.NewLottieCustomTextWithOpt(leftUserKey, maleText,
			decoration.WithTextColor(userKeyColor), decoration.WithFontSize(userKeyFontSize)))
		resultResource.AddDecorationConfig(decoration.NewLottieCustomTextWithOpt(rightUserKey,
			femaleText, decoration.WithTextColor(userKeyColor), decoration.WithFontSize(userKeyFontSize)))
		resultResource.AddDecorationConfig(decoration.NewLottieAccountWithOpt(fromAccountKey, fromNicknameKey, toAccountKey, toNicknameKey, leftUser, rightUser,
			decoration.WithFontSize(defaultFontSize), decoration.WithTextColor(defaultFontColor)))
		resultResource.AddDecorationConfig(decoration.NewLottieCustomText(matchTextKey, matchText, defaultFontColor, defaultCharLimit, matchTextFontSize, false))
		resultResource.AddDecorationConfig(decoration.NewLottieCustomText(matchDateKey, time.Now().Format("2006.01.02"),
			defaultFontColor, defaultCharLimit, defaultFontSize, false))
		for key, url := range resource.Images {
			if key == aiPicKey {
				if aiPic == "" {
					aiPic = url
				}
				resultResource.AddDecorationConfig(decoration.NewLottieImgWithOpt(key, aiPic, decoration.WithRadius(aiPiccycleRadius)))
			} else {
				resultResource.AddDecorationConfig(decoration.NewLottieImg(key, url))
			}
		}
	}

	return resultResource, aiPic, resource.Lottie
}

func (m *GameProcessModel) getAIResult(ctx context.Context, gameInfo *entity.GameInfo, maleUid, femaleUid uint32) (string, string, string) {
	matchList := make([]*pbRCMDPM.MatchedUsers, 0, 4)
	for _, player := range gameInfo.PlayerList {
		if player.CoupleMatchId == "" { // 初始化时保证有coupleMatchId的 uid为男, cpUid为女
			continue
		}
		matchList = append(matchList, &pbRCMDPM.MatchedUsers{
			Id:        player.CoupleMatchId,
			MaleUid:   int64(player.Uid),
			FemaleUid: int64(player.CpUid),
		})
	}
	rcmdCtx, cancel := context.WithTimeout(ctx, 3*time.Second)
	defer cancel()
	rcmdResultReq := pbRCMDPM.GetResultReq{
		MatchId:     gameInfo.MatchId,
		MatchedList: matchList,
	}
	rcmdResult, err := m.rpcCli.RCMDPerfectMatchCli.GetResult(rcmdCtx, rcmdResultReq)
	if err != nil {
		log.ErrorWithCtx(ctx, "gameResultPubNotify.RCMDPerfectMatchCli.GetResult, req: %+v, err: %v", rcmdResultReq, err)
	}
	log.InfoWithCtx(ctx, "gameResultPubNotify.RCMDPerfectMatchCli.GetResult, rcmdResult: %+v", rcmdResult)
	for _, content := range rcmdResult.GetContentList() {
		if uint32(content.GetMaleUid()) == maleUid && uint32(content.GetFemaleUid()) == femaleUid {
			return content.Url, content.MaleText, content.FemaleText
		}
	}
	return "", "", ""
}

func (m *GameProcessModel) FollowUser(ctx context.Context, fromUid, toUid uint32) error {
	seq, err := m.rpcCli.SeqGenClient.GenerateSequence(ctx, fromUid, seqgenClient.NamespaceUser, seqgenClient.KeyUgc, 1)
	if err != nil {
		log.Errorf("FollowUser Failed, GenerateSequence: %v", err)
		return err
	}

	affected, firstTime, err := m.rpcCli.FriendShipCli.FollowUser(ctx, &friendshipPB.FollowUserReq{
		Uid:          fromUid,
		FollowingUid: toUid,
		SequenceId:   seq,
		Source:       friendshipPB.Source_USER_OPERATE,
	})
	if err != nil {
		log.Errorf("FollowUser Failed, FollowUser %d %d %d: %v", fromUid, toUid, seq, err)
		return err
	}

	log.Debugf("FollowUser %d->%d Affected: %d FirstTime: %t", fromUid, toUid, seq, firstTime)

	if affected {
		// notify ugc sync
		_, _ = notify.NotifySync(fromUid, notify.UGC)
	}

	return nil
}

func userProfile2User(userProfile *pbApp.UserProfile) *account.User {
	return &account.User{
		Uid:      userProfile.GetUid(),
		Username: userProfile.GetAccount(),
		Alias:    userProfile.GetAccountAlias(),
		Nickname: userProfile.GetNickname(),
		Sex:      int32(userProfile.GetSex()),
	}
}

func (m *GameProcessModel) sendResultImMsg(ctx context.Context, opt *logicPb.PerfectCpGameResultImOpt) error {
	if opt.UserA.GetUid() == 0 || opt.UserB.GetUid() == 0 {
		log.ErrorWithCtx(ctx, "sendResultImMsg failed to generate svr msg id.uid:%v, toUid:%v, err:uid==0", opt.UserA.GetUid(), opt.UserB.GetUid())
		return nil
	}

	fromUser, toUser := opt.UserA, opt.UserB
	// 由男方发给女方
	if opt.UserA.GetSex() == uint32(account.Female) {
		fromUser, toUser = opt.UserB, opt.UserA
	}

	imMsg, _ := proto.Marshal(opt)

	sendReq := &imApiPB.Send1V1ExtMsgReq{
		From: &imApiPB.User{
			Uid:      fromUser.GetUid(),
			Username: fromUser.GetAccount(),
			Nickname: fromUser.GetNickname(),
		},
		To: &imApiPB.User{
			Uid:      toUser.GetUid(),
			Username: toUser.GetAccount(),
			Nickname: toUser.GetNickname(),
		},
		Msg: &imApiPB.ExtMsg{
			MsgType: uint32(imPB.IM_MSG_TYPE_PERFECT_MATCH_GAME_RESULT_USER_NOTIFY),
			Content: "",
			Ext:     imMsg,
		},
	}
	_, err := m.rpcCli.ImApiClient.Send1V1ExtMsg(ctx, sendReq)
	if err != nil {
		log.ErrorWithCtx(ctx, "sendResultImMsg fail to WriteMsgToUidWithId uid:%v, toUid:%v, err:%v", fromUser.GetUid(), toUser.GetUid(), err)
		return err
	}

	log.InfoWithCtx(ctx, "sendResultImMsg success, opt:%+v", opt)
	return nil
}

func (m *GameProcessModel) getChannelDisplayId(ctx context.Context, channelId uint32) uint32 {
	channelResp, err := m.rpcCli.ChannelCli.GetChannelSimpleInfo(ctx, 0, channelId)
	if err != nil {
		log.ErrorWithCtx(ctx, "getChannelDisplayId fail to GetChannelSimpleInfo. channelId:%v, err:%v", channelId, err)
		return 0
	}

	displayId := channelResp.GetDisplayId()

	if channelResp.GetChannelType() == uint32(channel.ChannelType_GUILD_PUBLIC_FUN_CHANNEL_TYPE) {
		guildResp, err := m.rpcCli.GuildCli.GetGuild(ctx, channelResp.GetBindId())
		if err != nil {
			log.ErrorWithCtx(ctx, "getChannelDisplayId fail to GetGuild. channelId:%v, err:%v", channelId, err)
			return 0
		}

		displayId = channelResp.GetBindId()
		if guildResp.GetShortId() > 0 {
			displayId = guildResp.GetShortId()
		}
	}

	return displayId
}
