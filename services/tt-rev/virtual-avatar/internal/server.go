package internal

import (
	"context"
	"errors"
	"gitlab.ttyuyin.com/avengers/tyr/core/log"
	mysqlConnect "gitlab.ttyuyin.com/avengers/tyr/core/middleware/mysql/connect"
	redisConnect "gitlab.ttyuyin.com/avengers/tyr/core/middleware/redis/connect"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/protocol/common/status"
	"golang.52tt.com/protocol/services/demo/echo"
	pb "golang.52tt.com/protocol/services/virtual-avatar"
	"golang.52tt.com/services/tt-rev/common/goroutineex"
	"golang.52tt.com/services/tt-rev/virtual-avatar/internal/conf"
	"golang.52tt.com/services/tt-rev/virtual-avatar/internal/entity"
	anti_corruption_layer "golang.52tt.com/services/tt-rev/virtual-avatar/internal/model/anti_corruption_layer"
	"golang.52tt.com/services/tt-rev/virtual-avatar/internal/model/resource-cfg"
	user_relation "golang.52tt.com/services/tt-rev/virtual-avatar/internal/model/user-relation"
	user_virtual_avatar "golang.52tt.com/services/tt-rev/virtual-avatar/internal/model/user-virtual-avatar"
	context0 "golang.org/x/net/context"
	"time"
)

func NewServer(ctx context.Context, cfg *conf.StartConfig) (*Server, error) {
    log.Infof("server startup with cfg: %+v", *cfg)

    redisClient, err := redisConnect.NewClient(ctx, cfg.RedisConfig)
    if err != nil {
        log.ErrorWithCtx(ctx, "NewServer fail to redisConnect.NewClient, %+v, err:%v", cfg.RedisConfig, err)
        return nil, err
    }

    mysqlDBCli, err := mysqlConnect.NewClient(ctx, cfg.MysqlConfig)
    if err != nil {
        log.ErrorWithCtx(ctx, "NewServer fail to mysqlConnect.NewClient, %+v, err:%v", cfg.MysqlConfig, err)
        return nil, err
    }

    //mysqlROCli, err := mysqlConnect.NewClient(ctx, cfg.MysqlReadOnlyConfig)
    //if err != nil {
    //    return nil, err
    //}

    bc, err := conf.NewBusinessConfManager()
    if err != nil {
        return nil, err
    }

    acLayer, err := anti_corruption_layer.NewMgr()
    if err != nil {
        log.ErrorWithCtx(ctx, "NewServer fail to anti_corruption_layer.NewMgr, %+v, err:%v", cfg.MysqlConfig, err)
        return nil, err
    }


    cfgMgr, err := resource_cfg.NewMgr(mysqlDBCli, redisClient, bc)
    if err != nil {
        log.ErrorWithCtx(ctx, "NewServer fail to resource_cfg.NewMgr, %+v, err:%v", cfg.MysqlConfig, err)
        return nil, err
    }

    relationMgr, err := user_relation.NewMgr(mysqlDBCli, redisClient, bc)
    if err != nil {
        log.ErrorWithCtx(ctx, "NewServer fail to user_relation.NewMgr, %+v, err:%v", cfg.MysqlConfig, err)
        return nil, err
    }

    userVaMgr, err := user_virtual_avatar.NewMgr(mysqlDBCli, redisClient, acLayer, cfgMgr)
    if err != nil {
        log.ErrorWithCtx(ctx, "NewServer fail to user_virtual_avatar.NewMgr, %+v, err:%v", cfg.MysqlConfig, err)
        return nil, err
    }


    s := &Server{
        bc:       bc,
        cfgMgr:   cfgMgr,
        relationMgr: relationMgr,
        userVaMgr: userVaMgr,
        acLayer:     acLayer,
    }

    return s, nil
}

type Server struct {
    bc       conf.IBusinessConfManager
    cfgMgr   resource_cfg.IMgr
    userVaMgr user_virtual_avatar.IMgr
    relationMgr user_relation.IMgr
    acLayer anti_corruption_layer.IMgr
}

func (s *Server) ShutDown() {

}

func (s *Server) Echo(ctx context.Context, req *echo.StringMessage) (*echo.StringMessage, error) {
    return req, nil
}

// SetVirtualAvatarConfig 设置虚拟形象配置/存在则更新
func (s *Server) SetVirtualAvatarConfig(ctx context.Context, req *pb.SetVirtualAvatarConfigReq) (*pb.SetVirtualAvatarConfigResp, error) {
    resp := &pb.SetVirtualAvatarConfigResp{}
    err := s.cfgMgr.SetVirtualAvatarConfig(ctx, req.GetVaConfig())
    if err != nil {
        log.ErrorWithCtx(ctx, "SetVirtualAvatarConfig failed. %+v, err:%v", req, err)
        return resp, err
    }

    log.InfoWithCtx(ctx, "SetVirtualAvatarConfig success. %+v", req)
    return resp, nil
}

// GetAllVirtualAvatarConfig 全量获取虚拟形象配置
func (s *Server) GetAllVirtualAvatarConfig(ctx context.Context, req *pb.GetAllVirtualAvatarConfigReq) (*pb.GetAllVirtualAvatarConfigResp, error) {
    resp := &pb.GetAllVirtualAvatarConfigResp{}
    list, err := s.cfgMgr.GetAllVirtualAvatarConfig(ctx)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetAllVirtualAvatarConfig failed. %+v, err:%v", req, err)
        return resp, err
    }

    resp.VaList = list
    return resp, nil
}

// GetVirtualAvatarConfByVaId 获取虚拟形象配置
func (s *Server) GetVirtualAvatarConfByVaId(ctx context.Context, req *pb.GetVirtualAvatarConfByVaIdReq) (*pb.GetVirtualAvatarConfByVaIdResp, error) {
    resp := &pb.GetVirtualAvatarConfByVaIdResp{}
    cfg, ok, err := s.cfgMgr.GetVirtualAvatarConfByVaId(ctx, req.GetVaId())
    if err != nil {
        log.ErrorWithCtx(ctx, "GetVirtualAvatarConfByVaId failed. %+v, err:%v", req, err)
        return resp, err
    }

    if !ok {
        return resp, nil
    }

    resp.VaInfo = cfg
    return resp, nil
}

// DelVirtualAvatarConfig 下架虚拟形象配置
func (s *Server) DelVirtualAvatarConfig(ctx context.Context, req *pb.DelVirtualAvatarConfigReq) (*pb.DelVirtualAvatarConfigResp, error) {
    resp := &pb.DelVirtualAvatarConfigResp{}
    err := s.cfgMgr.DelVirtualAvatarConfig(ctx, req.GetVaId())
    if err != nil {
        log.ErrorWithCtx(ctx, "DelVirtualAvatarConfig failed. %+v, err:%v", req, err)
        return resp, err
    }

    log.InfoWithCtx(ctx, "DelVirtualAvatarConfig success. %+v", req)
    return resp, nil
}

// GiveVirtualAvatarToUser 发放虚拟形象
func (s *Server) GiveVirtualAvatarToUser(ctx context.Context, req *pb.GiveVirtualAvatarToUserReq) (*pb.GiveVirtualAvatarToUserResp, error) {
    resp := &pb.GiveVirtualAvatarToUserResp{}

	// 获取虚拟形象配置
	vaConf, exist, err := s.cfgMgr.GetVirtualAvatarConfByVaId(ctx, req.GetVaId())
	if err != nil {
		log.ErrorWithCtx(ctx, "GiveVirtualAvatarToUser failed. %+v, err:%v", req, err)
		return resp, err
	}
	if !exist {
		log.ErrorWithCtx(ctx, "GiveVirtualAvatarToUser failed. %+v, err: va conf not exist", req)
		return resp, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "虚拟形象配置不存在")
	}

    isNewOne, err := s.userVaMgr.GiveVirtualAvatarToUser(ctx, req, vaConf)
    if err != nil {
        log.ErrorWithCtx(ctx, "GiveVirtualAvatarToUser failed. %+v, err:%v", req, err)
        return resp, err
    }

    if isNewOne && req.GetDurationSec() > 0 {
        // 自动佩戴
        goroutineex.GoroutineWithTimeoutCtx(ctx, 5*time.Second, func(ctx context.Context) {
            err := s.AutoWearVirtualAvatar(ctx, req.GetUid(), req.GetVaId())
            if err != nil {
                log.WarnWithCtx(ctx, "AutoSetVirtualAvatarInUse failed. %+v, err:%v", req, err)
            }
        })

	    // 推送通知
	    goroutineex.GoroutineWithTimeoutCtx(ctx, 3*time.Second, func(ctx context.Context) {
			err := s.gainVaNotify(ctx, req.GetUid(), req.GetVaId(), vaConf)
		    if err != nil {
			    log.WarnWithCtx(ctx, "gainVaNotify failed. %+v, err:%v", req, err)
		    }
	    })
    }

	log.InfoWithCtx(ctx, "GiveVirtualAvatarToUser success. %+v", req)
    return resp, nil
}

// AutoWearVirtualAvatar 自动佩戴虚拟形象(有绑定关系的有限佩戴双人形象)
func (s *Server) AutoWearVirtualAvatar(ctx context.Context, uid, vaId uint32) error {
	// 获取用户指定虚拟形象状态
	userVAStatus, err := s.userVaMgr.GetUserVirtualAvatarInfoByVaId(ctx, uid, vaId)
	if err != nil {
		log.ErrorWithCtx(ctx, "AutoWearVirtualAvatar fail to GetUserVirtualAvatarInfoByVaId. uid:%v, vaId:%v, err:%v", uid, vaId, err)
		return err
	}

	if userVAStatus.ItemId == 0 || userVAStatus.ExpireTime.Before(time.Now()) {
		log.ErrorWithCtx(ctx, "AutoWearVirtualAvatar fail to GetUserVirtualAvatarInfoByVaId. uid:%v, vaId:%v, err:%v", uid, vaId, err)
		return errors.New("user virtual avatar not exist")
	}

	relationMap, err := s.relationMgr.GetUserRelationMap(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "AutoWearVirtualAvatar fail to GetUserRelationMap. uid:%v, err:%v", uid, err)
		return err
	}

	relation := relationMap[userVAStatus.RelationId]
	displayTy := entity.DisplayTypeCommon
	if relation != nil && relation.CpUid > 0 {
		displayTy = entity.DisplayTypeCP
	}

	// 设置用户使用中的虚拟形象
	err = s.userVaMgr.SetUserVirtualAvatarInUse(ctx, uid, userVAStatus.ItemId, displayTy)
	if err != nil {
		log.ErrorWithCtx(ctx, "AutoWearVirtualAvatar fail to SetUserVirtualAvatarInUse. uid:%v, userItemId:%v, displayType:%v, err:%v",
			uid, userVAStatus.ItemId, entity.DisplayTypeCommon, err)
		return err
	}

	// 麦位资源变更通知
	s.micAvatarChangeNotify(ctx, uid, false)

	log.InfoWithCtx(ctx, "AutoWearVirtualAvatar success. uid:%v, vaId:%v, displayTy:%d", uid, vaId, displayTy)
	return nil
}

func (s *Server) gainVaNotify(ctx context.Context, uid, vaId uint32, vaConf *pb.VirtualAvatarConfig) error {
	vaInfo, err := s.userVaMgr.GetUserVirtualAvatarInfoByVaId(ctx,  uid, vaId)
	if err != nil {
		log.ErrorWithCtx(ctx, "gainVaNotify failed to GetUserVirtualAvatarInfoByVaId. uid:%d, vaId:%d, err:%v", uid, vaId, err)
		return err
	}
	if vaInfo.ItemId == 0 {
		return nil
	}

	vaPbInfo := fillStatusPbInfo(vaInfo)

	err = s.acLayer.PushUserGainNewNotify(ctx, uid, userVaStatus2UserVaInfo(vaPbInfo,nil, vaConf, nil))
	if err != nil {
		log.ErrorWithCtx(ctx, "gainVaNotify failed to PushUserGainNewNotify. uid:%d, vaId:%d, err:%v", uid, vaId, err)
		return err
	}

	return nil
}

// GetUserVirtualAvatarList 获取用户虚拟形象列表
func (s *Server) GetUserVirtualAvatarList(ctx context.Context, req *pb.GetUserVirtualAvatarListReq) (*pb.GetUserVirtualAvatarListResp, error) {
	resp := &pb.GetUserVirtualAvatarListResp{
        AutoPlayDurationSec: s.bc.GetAutoPlayDurationSec(),
	}
	uid := req.GetUid()

	// 获取用户虚拟形象列表
	list, err := s.userVaMgr.GetUserVirtualAvatarList(ctx, uid)
	if err != nil {
	   log.ErrorWithCtx(ctx, "GetUserVirtualAvatarList failed to GetUserVirtualAvatarList. %+v, err:%v", req, err)
	   return resp, err
	}

	if len(list) == 0 {
	   return resp, nil
	}

    now := time.Now()
	vaIdList := make([]uint32, 0, len(list))
	for _, item := range list {
        if item.ExpireTime.Before(now) {
            continue
        }
		vaIdList = append(vaIdList, item.VAId)
	}

	// 获取虚拟形象配置
	vaConfMap, err := s.cfgMgr.BatchGetVirtualAvatarConfig(ctx, vaIdList)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetUserVirtualAvatarList failed to BatchGetVirtualAvatarConfig. %+v, err:%v", req, err)
		return resp, err
	}

	// 获取用户关系
	relationMap, err := s.relationMgr.GetUserRelationMap(ctx, uid)
	if err != nil {
	   log.ErrorWithCtx(ctx, "GetUserVirtualAvatarList failed to GetUserRelationMap. %+v, err:%v", req, err)
	   return resp, err
	}

    possibleCpUids := make([]uint32, 0, len(relationMap))
	for _, va := range list {
		relation, ok := relationMap[va.RelationId]
		if ok {
            possibleCpUids = append(possibleCpUids, relation.CpUid)
		}
	}

    //// 批量获取用户关系
    //cpUserRelationMap, err := s.relationMgr.BatchGetUserRelationMap(ctx, possibleCpUids)
    //if err != nil {
    //    log.ErrorWithCtx(ctx, "GetUserVirtualAvatarList failed to BatchGetUserRelationMap. %+v, err:%v", req, err)
    //    return resp, err
    //}
    //
    //confirmedCpUids := make([]uint32, 0, len(possibleCpUids))
    //// 检查cp关系
    //for _, va := range list {
    //    relation, ok := relationMap[va.RelationId]
    //    if !ok {
    //        continue
    //    }
    //    cpRelation, ok := cpUserRelationMap[relation.CpUid]
    //    if !ok {
    //        continue
    //    }
    //    if cpRelation[va.RelationId].CpUid != uid { // 可能cp用户已与其他用户重新绑定对应关系
    //        continue
    //    }
    //
    //    confirmedCpUids = append(confirmedCpUids, relation.CpUid)
    //}

	// 获取cp用户虚拟形象列表
    cpVaListMap, err := s.userVaMgr.BatchGetUserVirtualAvatarList(ctx, possibleCpUids)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetUserVirtualAvatarList failed to BatchGetUserVirtualAvatarList. %+v, err:%v", req, err)
		return resp, err
	}

	// 获取用户正在使用中的虚拟形象
	inUseInfo, err := s.userVaMgr.GetUserInUseWithCache(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetUserVirtualAvatarList failed to GetUserInUseWithCache. %+v, err:%v", req, err)
		return resp, err
	}

	resp.VaList = fillVirtualAvatarPbList(list, vaConfMap, cpVaListMap, relationMap, inUseInfo)
	return resp, nil
}

func fillVirtualAvatarPbList(list []*entity.UserVAStatus, vaConfMap map[uint32]*pb.VirtualAvatarConfig,
	cpVaListMap map[uint32][]*entity.UserVAStatus, relationMap map[string]*entity.UserRelation, inUseInfo *pb.UserVAStatusInfo) []*pb.UserVirtualAvatarInfo {

	resp := make([]*pb.UserVirtualAvatarInfo, 0, len(list))

	for _, va := range list {
		cfg, ok := vaConfMap[va.VAId]
		if !ok {
			continue
		}

		var cpVaInfo *pb.UserVAStatusInfo
        for _, cpVaList := range cpVaListMap {
            for _, cpVa := range cpVaList {
                if relate, ok := relationMap[va.RelationId]; ok {
                    if cpVa.RelationId == va.RelationId && cpVa.Uid == relate.CpUid { // 判断是否是对应的cp用户
                        cpVaInfo = fillStatusPbInfo(cpVa)
                        break
                    }
                }
            }
		}

		vaInfo := fillStatusPbInfo(va)
		if inUseInfo.GetVaId() == va.VAId {
			vaInfo = inUseInfo
		}

		resp = append(resp, userVaStatus2UserVaInfo(vaInfo, cpVaInfo, cfg, relationMap[va.RelationId]))
	}

	return resp
}

func (s *Server) micAvatarChangeNotify(ctx context.Context, uid uint32, isCancel bool) {
    if isCancel {
        s.acLayer.UserMicStatusChangePush(ctx, uid, entity.MicAvaTypeNormal, &pb.VAResourceConf{})
        return
    }
    // 获取用户使用中的虚拟形象信息
    vaInfo, err := s.userVaMgr.GetUserInUseWithCache(ctx, uid)
    if err != nil {
        log.ErrorWithCtx(ctx, "micAvatarChangeNotify failed. %+v, err:%v", uid, err)
        return
    }

    if vaInfo.GetItemId() == 0 {
        return
    }

    scopeMap, err := s.userVaMgr.BatGetUserUseScope(ctx, []uint32{uid})
    if err != nil || scopeMap == nil {
        log.ErrorWithCtx(ctx, "micAvatarChangeNotify failed. uid:%d, err:%v", uid, err)
        return
    }

    newAvaType := entity.MicAvaTypeNormal
    if scopeMap[uid].MicSpace {
        newAvaType = entity.MicAvaTypeVirtual
    }

    // 获取虚拟形象配置
    vaConf, ok, err := s.cfgMgr.GetVirtualAvatarConfByVaId(ctx, vaInfo.GetVaId())
    if err != nil {
        log.ErrorWithCtx(ctx, "micAvatarChangeNotify failed. %+v, err:%v", uid, err)
        return
    }

    if !ok {
        log.ErrorWithCtx(ctx, "micAvatarChangeNotify failed vaConf is nil. %+v, err:%v", uid, err)
        return
    }

    resConf := &pb.VAResourceConf{
        EffectRes:    vaConf.GetMicSpaceAEffect(),
        EffectResMd5: vaConf.GetMicSpaceAEffectMd5(),
        ResType:      entity.ResourceTypeMicSpace,
        VaId: vaConf.GetVaId(),
    }
    if vaInfo.GetCharType() == entity.CharTypeB {
        resConf = &pb.VAResourceConf{
            EffectRes:    vaConf.GetMicSpaceBEffect(),
            EffectResMd5: vaConf.GetMicSpaceBEffectMd5(),
            ResType:      entity.ResourceTypeMicSpace,
            VaId: vaConf.GetVaId(),
        }
    }

    s.acLayer.UserMicStatusChangePush(ctx, uid, newAvaType, resConf)
}

// SetVirtualAvatarInUse 用户佩戴虚拟形象
func (s *Server) SetVirtualAvatarInUse(ctx context.Context, req *pb.SetVirtualAvatarInUseReq) (*pb.SetVirtualAvatarInUseResp, error) {
    out := &pb.SetVirtualAvatarInUseResp{}

    if req.GetUid() == 0 {
        log.ErrorWithCtx(ctx, "SetVirtualAvatarInUse failed. %+v, err: uid or displayType is 0", req)
        return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "参数错误")
    }

    if req.GetUserItemId() != 0 {
        if req.GetDisplayType() == 0 {
            log.ErrorWithCtx(ctx, "SetVirtualAvatarInUse failed. %+v, err: displayType is 0", req)
            return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "参数错误")
        }

        // 检查用户虚拟形象状态
        vaStatus, err := s.userVaMgr.GetUserVirtualAvatarInfoByItemId(ctx, req.GetUid(), req.GetUserItemId())
        if err != nil {
            log.ErrorWithCtx(ctx, "SetVirtualAvatarInUse failed. %+v, err:%v", req, err)
            return out, err
        }

        if vaStatus.VAId == 0 {
            log.ErrorWithCtx(ctx, "SetVirtualAvatarInUse failed. %+v, err: vaStatus is nil or expired", req)
            return out, protocol.NewExactServerError(nil, status.ErrVirtualAvatarExpired, "形象已过期，无法继续使用")
        }

        // 检查虚拟形象配置
        _, exist, err := s.cfgMgr.GetVirtualAvatarConfByVaId(ctx, vaStatus.VAId)
        if err != nil {
            log.ErrorWithCtx(ctx, "GiveVirtualAvatarToUser failed. %+v, err:%v", req, err)
            return out, err
        }

        if !exist {
            log.ErrorWithCtx(ctx, "GiveVirtualAvatarToUser failed. %+v, err: va conf not exist", req)
            return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "形象已下架，无法继续使用")
        }

        if req.GetDisplayType() == entity.DisplayTypeCP {
            // 检查用户形象的绑定关系
            relationMap, err := s.relationMgr.GetUserRelationMap(ctx, req.GetUid())
            if err != nil {
                log.ErrorWithCtx(ctx, "SetVirtualAvatarInUse failed. %+v, err:%v", req, err)
                return out, err
            }

            relation, ok := relationMap[vaStatus.RelationId]
            if !ok {
                log.ErrorWithCtx(ctx, "SetVirtualAvatarInUse failed. %+v, err: relation is nil", req)
                return out, protocol.NewExactServerError(nil, status.ErrVirtualAvatarHaveNotRelation, "形象关系不存在")
            }
            if relation.ExpireTime.Before(time.Now()) {
                log.ErrorWithCtx(ctx, "SetVirtualAvatarInUse failed. %+v, err: relation is expired", req)
                return out, protocol.NewExactServerError(nil, status.ErrVirtualAvatarHaveNotRelation, "关系形象已过期，无法继续使用")
            }
        }
    }

    // 设置用户使用中的虚拟形象
    err := s.userVaMgr.SetUserVirtualAvatarInUse(ctx, req.GetUid(), req.GetUserItemId(), req.GetDisplayType())
    if err != nil {
        log.ErrorWithCtx(ctx, "SetVirtualAvatarInUse failed. %+v, err:%v", req, err)
        return out, err
    }

    goroutineex.GoroutineWithTimeoutCtx(ctx, 3*time.Second, func(ctx context.Context) {
        // 麦位资源变更通知
        s.micAvatarChangeNotify(ctx, req.GetUid(), req.GetUserItemId() == 0)
    })

    return out, nil
}

// GetVirtualAvatarInUse 获取用户使用中的虚拟形象
func (s *Server) GetVirtualAvatarInUse(ctx context.Context, req *pb.GetVirtualAvatarInUseReq) (*pb.GetVirtualAvatarInUseResp, error) {
    targetUid := req.GetUid()
    out := &pb.GetVirtualAvatarInUseResp{}

    // 获取用户使用中的虚拟形象信息
    vaInfo, err := s.getVirtualAvatarInUseVaInfo(ctx, targetUid)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetVirtualAvatarInUse failed. %+v, err:%v", targetUid, err)
        return out, err
    }

    out.VaInfo = vaInfo

    if vaInfo.GetUid() == 0 {
        return out, nil
    }

    // 获取用户使用范围
    useScopeMap, err := s.userVaMgr.BatGetUserUseScope(ctx, []uint32{targetUid})
    if err != nil {
        log.ErrorWithCtx(ctx, "GetUserInUse fail to useMgr.BatGetUserUseScope, uid:%d, err:%v", targetUid, err)
        return out, err
    }

    out.UseScopeList = entityScopeToPb(useScopeMap[targetUid])
    return out, nil
}

func (s *Server) getVirtualAvatarInUseVaInfo(ctx context.Context, uid uint32) (*pb.UserVirtualAvatarInfo, error) {
    out := &pb.UserVirtualAvatarInfo{}

    // 获取用户使用中的虚拟形象信息
    vaInfo, err := s.userVaMgr.GetUserInUseWithCache(ctx, uid)
    if err != nil {
        log.ErrorWithCtx(ctx, "getVirtualAvatarInUseVaInfo failed. %+v, err:%v", uid, err)
        return out, err
    }

    if vaInfo.GetItemId() == 0 {
        return out, nil
    }

    // 获取虚拟形象配置
    vaConf, ok, err := s.cfgMgr.GetVirtualAvatarConfByVaId(ctx, vaInfo.GetVaId())
    if err != nil {
        log.ErrorWithCtx(ctx, "getVirtualAvatarInUseVaInfo failed. uid:%+d, err:%v", uid, err)
        return out, err
    }
    if !ok {
        log.WarnWithCtx(ctx, "getVirtualAvatarInUseVaInfo failed vaConf(id:%d) is nil. %+v, err:%v", vaInfo.GetVaId(), uid, err)
        return out, nil
    }

    relationMap, err := s.relationMgr.GetUserRelationMap(ctx, uid)
    if err != nil {
        log.WarnWithCtx(ctx, "getVirtualAvatarInUseVaInfo failed to GetUserRelationMap. uid %d, err:%v", uid, err)
    }

    var cpVaInfo *pb.UserVAStatusInfo
    relation, ok := relationMap[vaConf.RelationId]
    if ok {
        cpUid := relationMap[vaConf.RelationId].CpUid

        cpVaList, err := s.userVaMgr.GetUserVirtualAvatarList(ctx, cpUid)
        if err != nil {
            log.WarnWithCtx(ctx, "getVirtualAvatarInUseVaInfo failed to GetUserVirtualAvatarList. uid %d, err:%v", uid, err)
        }
        for _, cpVa := range cpVaList {
            if cpVa.RelationId == vaConf.RelationId {
                cpVaInfo = fillStatusPbInfo(cpVa)
                break
            }
        }
    }

    out = userVaStatus2UserVaInfo(vaInfo, cpVaInfo, vaConf, relation)
    return out, nil
}

func entityScopeToPb(useScope *entity.UseScopeSwitch) []uint32 {
    scopeInUse := make([]uint32, 0, 3)
    if useScope.MicSpace {
        scopeInUse = append(scopeInUse, entity.UseScopeTypeMicSpace)
    }
    if useScope.PersonPage {
        scopeInUse = append(scopeInUse, entity.UseScopeTypePersonPage)
    }
    if useScope.ProfileCard {
        scopeInUse = append(scopeInUse, entity.UseScopeTypeProfileCard)
    }
    return scopeInUse
}

func fillStatusPbInfo(vaInfo *entity.UserVAStatus) *pb.UserVAStatusInfo {
	vaPbInfo := &pb.UserVAStatusInfo{
		Uid:      vaInfo.Uid,
		ItemId:   vaInfo.ItemId,
		VaId:     vaInfo.VAId,
		CharType: vaInfo.CharType,
		ExpireTs: vaInfo.ExpireTime.Unix(),
	}
	return vaPbInfo
}

func userVaStatus2UserVaInfo(status, cpStatus *pb.UserVAStatusInfo, vaConf *pb.VirtualAvatarConfig, relation *entity.UserRelation) *pb.UserVirtualAvatarInfo {
    micSpaceRes, basePic := getMicSpaceRes(status.GetCharType(), vaConf)

    resCfgList := make([]*pb.VAResourceConf, 0, 2)
    resCfgList = append(resCfgList, micSpaceRes)
    resCfgList = append(resCfgList, &pb.VAResourceConf{
        EffectRes:    vaConf.GetPersonalPageEffect(),
        EffectResMd5: vaConf.GetPersonalPageEffectMd5(),
        ResType:      entity.ResourceTypePersonalPageProfile,
    }, &pb.VAResourceConf{
        EffectRes:    vaConf.GetEnterChannelEffect(),
        EffectResMd5: vaConf.GetEnterChannelEffectMd5(),
        ResType:      entity.ResourceTypeEnterChannel,
    })

    resSet := &pb.VAResourceSet{
        UserItemId:  status.GetItemId(),
        DisplayType: status.GetDisplayType(),
        ResList:     resCfgList,
        InUse:       status.GetInUse(),
        VaId:        status.GetVaId(),
    }

    out := &pb.UserVirtualAvatarInfo{
        Uid:        status.GetUid(),
        Name:       vaConf.GetName(),
        BasePic:    basePic,
        ResSet:     resSet,
        UserChar:   status.GetCharType(),
        ExpireTs:   status.GetExpireTs(),
		RelationId: vaConf.GetRelationId(),
    }

	if cpStatus != nil && relation != nil {
        out.CpUid = cpStatus.GetUid()
        out.CpUserChar = cpStatus.GetCharType()
        out.RelateName = relation.RelationName

	} else {
		if status.GetDisplayType() == entity.DisplayTypeCP {
			// 无cp关系，展示普通形象
			out.ResSet.DisplayType = entity.DisplayTypeCommon
		}
	}

	return out
}

func getMicSpaceRes(charType uint32, vaConf *pb.VirtualAvatarConfig) (*pb.VAResourceConf, string) {
    if charType == entity.CharTypeB {
        return &pb.VAResourceConf{
            EffectRes:    vaConf.GetMicSpaceBEffect(),
            EffectResMd5: vaConf.GetMicSpaceBEffectMd5(),
            ResType:      entity.ResourceTypeMicSpace,
            VaId: vaConf.GetVaId(),
        }, vaConf.GetBasePicB()
    }

    return &pb.VAResourceConf{
        EffectRes:    vaConf.GetMicSpaceAEffect(),
        EffectResMd5: vaConf.GetMicSpaceAEffectMd5(),
        ResType:      entity.ResourceTypeMicSpace,
        VaId: vaConf.GetVaId(),
    }, vaConf.GetBasePicA()
}

// SetUserVirtualAvatarUseScope 设置虚拟形象使用范围
func (s *Server) SetUserVirtualAvatarUseScope(ctx context.Context, req *pb.SetUserVirtualAvatarUseScopeReq) (*pb.SetUserVirtualAvatarUseScopeResp, error) {
    out := &pb.SetUserVirtualAvatarUseScopeResp{}

    // 设置用户虚拟形象使用范围
    err := s.userVaMgr.SetUserVirtualAvatarUseScope(ctx, req.GetUid(), req.GetUseScope(), req.GetOpType())
    if err != nil {
        log.ErrorWithCtx(ctx, "SetUserVirtualAvatarUseScope failed. %+v, err:%v", req, err)
        return out, err
    }

    if req.GetUseScope() == entity.UseScopeTypeMicSpace {
        // 麦位状态变更推送
        goroutineex.GoroutineWithTimeoutCtx(ctx, 3*time.Second, func(ctx context.Context) {
            s.micAvatarChangeNotify(ctx, req.GetUid(), req.GetOpType() == entity.OpTypeRemove)
        })
    }

    return out, nil
}

// GetVirtualAvatarUseScope 获取虚拟形象使用范围
func (s *Server) GetVirtualAvatarUseScope(ctx context.Context, req *pb.GetVirtualAvatarUseScopeReq) (*pb.GetVirtualAvatarUseScopeResp, error) {
    out := &pb.GetVirtualAvatarUseScopeResp{}

    // 获取虚拟形象使用范围
    scopeMap, err := s.userVaMgr.BatGetUserUseScope(ctx, []uint32{req.GetUid()})
    if err != nil || scopeMap == nil {
        log.ErrorWithCtx(ctx, "GetVirtualAvatarUseScope failed. %+v, err:%v", req, err)
        return out, err
    }

    out.UseScope = entityScopeToPb(scopeMap[req.GetUid()])
    return out, nil
}

// GetUserRelationList 获取用户关系列表
func (s *Server) GetUserRelationList(ctx context.Context, req *pb.GetUserRelationListReq) (*pb.GetUserRelationListResp, error) {
	resp := &pb.GetUserRelationListResp{
		RelationList: make([]*pb.UserRelation, 0),
	}

	relationMap, err := s.relationMgr.GetUserRelationMap(ctx, req.GetUid())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetUserRelationList failed to GetUserRelationMap. %+v, err:%v", req, err)
		return resp, err
	}

	for _, relation := range relationMap {
		resp.RelationList = append(resp.RelationList, &pb.UserRelation{
			Uid:          req.GetUid(),
			CpUid:        relation.CpUid,
			RelationId:   relation.RelationId,
			RelateName:   relation.RelationName,
			ExpireTs:     relation.ExpireTime.Unix(),
		})
	}

	return resp, nil
}

// BindUserRelation 绑定用户关系
func (s *Server) BindUserRelation(ctx context.Context, req *pb.BindUserRelationReq) (*pb.BindUserRelationResp, error) {
    resp := &pb.BindUserRelationResp{}
	err := s.relationMgr.BindUserRelation(ctx, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "BindUserRelation failed to BindUserRelation. %+v, err:%v", req, err)
		return resp, err
	}

	// 若正在佩戴该形象，自动切换双人形象
	goroutineex.GoroutineWithTimeoutCtx(ctx, 3*time.Second, func(ctx context.Context) {
		for _, uid := range []uint32{req.GetUidA(), req.GetUidB()} {
			va, err := s.getVirtualAvatarInUseVaInfo(ctx, uid)
			if err != nil {
				log.WarnWithCtx(ctx, "AutoSetVirtualAvatarInUse failed to getVirtualAvatarInUseVaInfo. %+v, err:%v", req, err)
				continue
			}

			if va.RelationId != req.GetRelationId() {
				continue
			}

            now := time.Now()
            if va.ExpireTs <= now.Unix() {
                continue
            }

			err = s.AutoWearVirtualAvatar(ctx, uid, va.GetResSet().GetVaId())
			if err != nil {
				log.WarnWithCtx(ctx, "AutoSetVirtualAvatarInUse failed to AutoWearVirtualAvatar. %+v, err:%v", req, err)
			}
		}
	})

	log.InfoWithCtx(ctx, "BindUserRelation success. %+v", req)
	return resp, nil
}

// UnBindUserRelation 解绑用户关系
func (s *Server) UnBindUserRelation(ctx context.Context, req *pb.UnBindUserRelationReq) (*pb.UnBindUserRelationResp, error) {
	resp := &pb.UnBindUserRelationResp{}
	return resp, s.relationMgr.UnBindUserRelation(ctx, req.GetUidA(), req.GetUidB())
}

func (s *Server) BatGetUserMicSpaceRes(ctx context0.Context, req *pb.BatGetUserMicSpaceResReq) (*pb.BatGetUserMicSpaceResResp, error) {
    out := &pb.BatGetUserMicSpaceResResp{}
    outMap := make(map[uint32]*pb.VAResourceConf, len(req.GetUidList()))

    if len(req.GetUidList()) == 0 {
        return out, nil
    }

    // 获取用户使用中的虚拟形象信息
    vaInfoMap, err := s.userVaMgr.BatUserInUseWithCache(ctx, req.GetUidList())
    if err != nil {
        log.ErrorWithCtx(ctx, "BatGetUserMicSpaceRes failed. %+v, err:%v", req, err)
        return out, err
    }

    // 获取用户使用范围
    useScopeMap, err := s.userVaMgr.BatGetUserUseScope(ctx, req.GetUidList())
    if err != nil {
        log.ErrorWithCtx(ctx, "BatGetUserMicSpaceRes failed. %+v, err:%v", req, err)
        return out, err
    }

    vaIdList := make([]uint32, 0, len(req.GetUidList()))
    for _, vaInfo := range vaInfoMap {
        if vaInfo.GetVaId() == 0 {
            continue
        }
        vaIdList = append(vaIdList, vaInfo.GetVaId())
    }

    // 获取虚拟形象配置
    vaConfMap, err := s.cfgMgr.BatchGetVirtualAvatarConfig(ctx, vaIdList)
    if err != nil {
        log.ErrorWithCtx(ctx, "BatGetUserMicSpaceRes failed. %+v, err:%v", req, err)
        return out, err
    }

    for _, uid := range req.GetUidList() {
        if useScopeMap[uid].MicSpace && vaInfoMap[uid].GetItemId() != 0 {
            micSpaceRes, _ := getMicSpaceRes(vaInfoMap[uid].GetCharType(), vaConfMap[vaInfoMap[uid].GetVaId()])
            outMap[uid] = micSpaceRes
        }
    }

    out.MapUidInfo = outMap
    return out, nil
}

func (s *Server) GetUserEnterChannelRes(ctx context.Context, req *pb.GetUserEnterChannelResReq) (*pb.GetUserEnterChannelResResp, error) {
    out := &pb.GetUserEnterChannelResResp{}

    // 获取用户使用中的虚拟形象信息
    userInUseInfo, err := s.getVirtualAvatarInUseVaInfo(ctx, req.GetUid())
    if err != nil {
        log.ErrorWithCtx(ctx, "GetUserEnterChannelRes failed. %+v, err:%v", req, err)
        return out, err
    }

    if userInUseInfo.GetResSet().GetUserItemId() == 0 || userInUseInfo.GetResSet().GetDisplayType() != entity.DisplayTypeCP ||
        userInUseInfo.GetCpUid() != req.GetFollowUid() {
        return out, nil
    }

    followUserVaInfo, err := s.getVirtualAvatarInUseVaInfo(ctx, req.GetFollowUid())
    if err != nil {
        log.ErrorWithCtx(ctx, "GetUserEnterChannelRes failed. %+v, err:%v", req, err)
        return out, err
    }

    if followUserVaInfo.GetResSet().GetUserItemId() == 0 ||
        followUserVaInfo.GetResSet().GetDisplayType() != entity.DisplayTypeCP ||
        followUserVaInfo.GetResSet().GetVaId() != userInUseInfo.GetResSet().GetVaId() || // 非使用同一套资源
        followUserVaInfo.GetCpUid() != req.GetUid() {
        return out, nil
    }

    // 获取虚拟形象配置
    vaConf, ok, err := s.cfgMgr.GetVirtualAvatarConfByVaId(ctx, userInUseInfo.GetResSet().GetVaId())
    if err != nil {
        log.ErrorWithCtx(ctx, "GetUserEnterChannelRes failed. %+v, err:%v", req, err)
        return out, err
    }

    if !ok {
        log.WarnWithCtx(ctx, "GetUserEnterChannelRes failed vaConf is nil. %+v, err:%v", req, err)
        return out, err
    }

    out = &pb.GetUserEnterChannelResResp{
        EffectRes:    vaConf.GetEnterChannelEffect(),
        EffectResMd5: vaConf.GetEnterChannelEffectMd5(),
        UserChar:     userInUseInfo.GetUserChar(),
        CpUserChar:   followUserVaInfo.GetUserChar(),
    }

    return out, nil
}