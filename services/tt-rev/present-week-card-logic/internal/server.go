package internal

import (
	"context"
	"fmt"
	"gitlab.ttyuyin.com/tt-infra/tyr/log"
	"go.opentelemetry.io/otel/codes"
	"golang.52tt.com/pkg/protocol"
	protogrpc "golang.52tt.com/pkg/protocol/grpc"
	channelPb "golang.52tt.com/protocol/app/channel"
	"golang.52tt.com/protocol/common/status"
	darkPB "golang.52tt.com/protocol/services/darkserver"
	"golang.52tt.com/protocol/services/demo/echo"
	"golang.52tt.com/services/tt-rev/present-week-card-logic/internal/conf"
	"golang.52tt.com/services/tt-rev/present-week-card-logic/internal/mgr"
	"golang.52tt.com/services/tt-rev/present-week-card-logic/internal/rpc"
	"sort"
	"strconv"
	"time"

	pb "golang.52tt.com/protocol/app/present-week-card-logic"
	svrPb "golang.52tt.com/protocol/services/present-week-card"
	nameplatePb "golang.52tt.com/protocol/services/revenuenameplate"
)

type StartConfig struct {
}

const (
	weekCardBuyStatusNotBuy     = uint32(pb.PresentWeekCardInfo_BUY_STATUS_NOT_BUY)
	weekCardBuyStatusAlreadyBuy = uint32(pb.PresentWeekCardInfo_BUY_STATUS_ALREADY_BUY)
	weekCardBuyStatusLocking    = uint32(pb.PresentWeekCardInfo_BUY_STATUS_LOCKING)

	dailyAwardStatusWaitForRecv = uint32(pb.AwardStatusType_AWARD_STATUS_TYPE_WAIT_TO_RECV)
	dailyAwardStatusExpired     = uint32(pb.AwardStatusType_AWARD_STATUS_TYPE_EXPIRED)
	dailyAwardStatusReceived    = uint32(pb.AwardStatusType_AWARD_STATUS_TYPE_ALREADY_RECEIVED)
)

func NewServer(ctx context.Context, cfg *StartConfig) (*Server, error) {
	log.Infof("server startup with cfg: %+v", *cfg)

	rpcCli, err := rpc.NewClient()
	if err != nil {
		log.ErrorWithCtx(ctx, "Failed to NewClient err: (%v)", err)
		return nil, err
	}

	bc, err := conf.NewBusinessConfManager()
	if err != nil {
		log.ErrorWithCtx(ctx, "NewOnePieceLogic fail to NewBusinessConfManager. err:%v", err)
		return nil, err
	}

	mgr, err := mgr.NewMgr(ctx, rpcCli, bc)
	if err != nil {
		log.ErrorWithCtx(ctx, "Failed to NewMgr err: (%v)", err)
		return nil, err
	}

	svr := &Server{
		bc:  bc,
		mgr: mgr,
		rpc: rpcCli,
	}
	return svr, nil
}

type Server struct {
	bc  conf.IBusinessConfManager
	mgr mgr.IMgr
	rpc *rpc.Client
}

func (s *Server) ShutDown() {
	s.rpc.Close()
}

func (s *Server) Echo(ctx context.Context, req *echo.StringMessage) (*echo.StringMessage, error) {
	return req, nil
}

func (s *Server) checkIfPgcChannel(ctx context.Context, opUid, channelId uint32) (bool, error) {
	resp, err := s.rpc.ChannelCli.GetChannelSimpleInfo(ctx, opUid, channelId)
	if err != nil {
		log.ErrorWithCtx(ctx, "checkIfPgcChannel getChannelSimpleInfo fail. opUid:%d, channelId:%d, err:%v", opUid, channelId, err)
		return false, err
	}

	if resp.GetChannelType() == uint32(channelPb.ChannelType_RADIO_LIVE_CHANNEL_TYPE) ||
		resp.GetChannelType() == uint32(channelPb.ChannelType_GUILD_PUBLIC_FUN_CHANNEL_TYPE) {
		return true, nil
	}

	return false, nil
}

// GetPresentWeekCardEntry 获取礼物周卡入口信息
func (s *Server) GetPresentWeekCardEntry(ctx context.Context, req *pb.GetPresentWeekCardEntryRequest) (*pb.GetPresentWeekCardEntryResponse, error) {
	out := &pb.GetPresentWeekCardEntryResponse{}

	svrInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
	if !ok {
		log.ErrorWithCtx(ctx, "GetPresentWeekCardEntry ServiceInfoFromContext fail. in:%+v", req)
		return out, protocol.NewExactServerError(codes.Ok, status.ErrRequestParamInvalid)
	}
	opUid := svrInfo.UserID

	if !s.bc.CheckIfFeatureVersion(ctx, uint32(svrInfo.ClientType), svrInfo.ClientVersion, svrInfo.MarketID) {
		return out, nil
	}

	if req.GetChannelId() > 0 && s.bc.OnlyPgcEnableEntry() {
		isPgc, err := s.checkIfPgcChannel(ctx, opUid, req.GetChannelId())
		if err != nil {
			log.ErrorWithCtx(ctx, "GetPresentWeekCardEntry fail to checkIfPgcChannel. req:%v,err:%v", req, err)
			return out, err
		}
		if !isPgc {
			// 非pgc房
			return out, nil
		}
	}

	dark, err := s.checkIfDarkUser(ctx, opUid)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetPresentWeekCardEntry fail to checkIfDarkUser. req:%v,err:%v", req, err)
		return out, err
	}

	if dark {
		// 黑产用户
		return out, nil
	}

	resp, err := s.rpc.PresentWeekCardCli.GetPresentWeekCardAccess(ctx, &svrPb.GetPresentWeekCardEntryReq{
		Uid:       opUid,
		ChannelId: req.GetChannelId(),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetPresentWeekCardEntry fail. req:%v,err:%v", req, err)
		return out, err
	}

	out = &pb.GetPresentWeekCardEntryResponse{
        HaveAccess:     resp.GetHaveAccess(),
        ReceiveStatus:  resp.GetReceiveStatus(),
        NeedToAutoShow: resp.GetNeedToAutoShow(),
        StaySeconds:    resp.GetStaySeconds(),
        EveryNDay:      resp.GetEveryNDay(),
        AdIdx:          s.bc.GetAdAccessIdx(),
    }

	log.DebugWithCtx(ctx, "GetPresentWeekCardEntry out:%+v", out)
	return out, nil
}

func (s *Server) getRevenueNameplateCfg(ctx context.Context, cardList []*svrPb.PresentWeekCardInfo) (map[uint32]*nameplatePb.RevenueNameplateInfo, error) {
	nameplateIds := make([]uint32, 0)
	for _, v := range cardList {
		for _, award := range v.GetAwardList() {
			if len(award.GetAwardList()) == 0 {
				continue
			}
			for _, item := range award.GetAwardList() {
				if item.GetAwardType() != uint32(svrPb.AwardItemType_AWARD_ITEM_TYPE_NAMEPLATE) {
					continue
				}
				// 将字符串转为整形
				id, _ := strconv.Atoi(item.GetAwardId())
				nameplateIds = append(nameplateIds, uint32(id))
			}
		}
	}

	nameplateCfgMap := make(map[uint32]*nameplatePb.RevenueNameplateInfo)
	if len(nameplateIds) > 0 {
		nameplateList, err := s.rpc.RevenueNameplateCli.GetNamePlate(ctx, nameplateIds)
		if err != nil {
			log.ErrorWithCtx(ctx, "getRevenueNameplateCfg fail to GetNamePlate. cardList:%v,err:%v", cardList, err)
			return nameplateCfgMap, err
		}

		for _, v := range nameplateList {
			nameplateCfgMap[v.GetId()] = v
		}
	}

	return nameplateCfgMap, nil
}

// GetPresentWeekCardInfo 获取礼物周卡详情
func (s *Server) GetPresentWeekCardInfo(ctx context.Context, req *pb.GetPresentWeekCardInfoRequest) (*pb.GetPresentWeekCardInfoResponse, error) {
	out := &pb.GetPresentWeekCardInfoResponse{}

	svrInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
	if !ok {
		log.ErrorWithCtx(ctx, "GetPresentWeekCardInfo ServiceInfoFromContext fail. in:%+v", req)
		return out, protocol.NewExactServerError(codes.Ok, status.ErrRequestParamInvalid)
	}
	opUid := svrInfo.UserID

	if !s.bc.CheckIfFeatureVersion(ctx, uint32(svrInfo.ClientType), svrInfo.ClientVersion, svrInfo.MarketID) {
		return out, nil
	}

	dark, err := s.checkIfDarkUser(ctx, opUid)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetPresentWeekCardEntry fail to checkIfDarkUser. req:%v,err:%v", req, err)
		return out, err
	}

	if dark {
		// 黑产用户
		return out, nil
	}

	svrResp, err := s.rpc.PresentWeekCardCli.GetPresentWeekCardInfo(ctx, &svrPb.GetPresentWeekCardInfoReq{
		Uid: opUid,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetPresentWeekCardInfo fail. req:%v,err:%v", req, err)
		return out, err
	}

	countDown := 0
	for _, v := range svrResp.GetCardList() {
		if v.GetBuyStatus() == weekCardBuyStatusAlreadyBuy {
			countDown = getCountdown(countDown, int(v.GetCycleCnt()), time.Unix(int64(v.GetExpireTime()), 0), v.GetCycleCnt() > 0)
		}
	}

	//nameplateCfgMap, err := s.getRevenueNameplateCfg(ctx, svrResp.GetCardList())
	//if err != nil {
	//	log.ErrorWithCtx(ctx, "GetPresentWeekCardInfo fail to getRevenueNameplateCfg. req:%v,err:%v", req, err)
	//	return out, err
	//}

	for _, v := range svrResp.GetCardList() {
		awardList := make([]*pb.DailyAwardInfo, 0, len(v.GetAwardList()))
		waitRecvFlag := false

		for _, award := range v.GetAwardList() {
			if len(award.GetAwardList()) == 0 {
				continue
			}
			awardStatus := award.GetStatus()
			itemList := make([]*pb.WeekCardAwardInfo, 0, len(award.GetAwardList()))

			for _, item := range award.GetAwardList() {
				info, err := s.mgr.GetAwardConfHandler(ctx, item.GetAwardType(), item.GetAmount(), item.GetAwardId())
				if err != nil {
					log.ErrorWithCtx(ctx, "GetPresentWeekCardInfo fail to GetAwardConfHandler. req:%v,err:%v", req, err)
					continue
				}
				itemList = append(itemList, info)
				//if item.GetAwardType() != uint32(svrPb.AwardItemType_AWARD_ITEM_TYPE_NAMEPLATE) {
				//	info, err := s.mgr.GetAwardConfHandler(ctx, item.GetAwardType(), item.GetAmount(), item.GetAwardId())
				//	if err != nil {
				//		log.ErrorWithCtx(ctx, "GetPresentWeekCardInfo fail to GetAwardConfHandler. req:%v,err:%v", req, err)
				//		continue
				//	}
				//	itemList = append(itemList, info)
				//
				//} else {
				//	id, _ := strconv.Atoi(item.GetAwardId())
				//	cfg := nameplateCfgMap[uint32(id)]
				//	itemList = append(itemList, &pb.WeekCardAwardInfo{
				//		AwardIcon:  cfg.GetBaseUrl(),
				//		AwardName:  cfg.GetName() + "个人铭牌",
				//		VaildTimeS: item.GetAmount() * 24 * 60 * 60,
				//	})
				//}
			}

			if award.Status == dailyAwardStatusWaitForRecv {
				waitRecvFlag = true
			} else {
				if countDown == 1 && awardStatus != dailyAwardStatusReceived {
					awardStatus = dailyAwardStatusExpired
				}
			}

			awardList = append(awardList, &pb.DailyAwardInfo{
				Status:   awardStatus,
				GiftList: itemList,
				AwardIdx: award.GetAwardOrder(),
			})
		}

		sort.Slice(awardList, func(i, j int) bool {
			if awardList[i].GetAwardIdx() == 0 {
				return false
			} else if awardList[j].GetAwardIdx() == 0 {
				return true
			} else {
				return awardList[i].GetAwardIdx() < awardList[j].GetAwardIdx()
			}
		})

		info := &pb.PresentWeekCardInfo{
			CardId:           fmt.Sprintf("%d", v.GetCardId()),
			TierName:         v.GetTierName(),
			Price:            v.GetPrice(),
			BuyStatus:        v.GetBuyStatus(),
			AwardList:        awardList,
			OriginPrice:      v.GetOriginPrice(),
			SellingPointText: v.GetSellingPointText(),
			ExpireTime:       v.GetExpireTime(),
		}

		info.BottomText = s.getBottomText(waitRecvFlag, v.GetBuyStatus(), countDown)
		out.CardList = append(out.CardList, info)
	}

	return out, nil
}

// 向下取整5分的时间
func getRound10MinTime(t time.Time, cycleCnt int) time.Time {
	return t.Truncate(time.Duration(cycleCnt) * time.Minute)
}

// 计算cycleDuration
func getMinuteCycleDuration(beginTime, endTime time.Time, cycleCnt int) int {
	if beginTime.After(endTime) {
		return 0
	}

	// 计算时间差值
	duration := getRound10MinTime(endTime, cycleCnt).Sub(getRound10MinTime(beginTime, cycleCnt))
	minutes := int(duration.Minutes())

	return minutes / cycleCnt
}

// 获取当天0点时间
func getZeroDayTime(t time.Time) time.Time {
	return time.Date(t.Year(), t.Month(), t.Day(), 0, 0, 0, 0, time.Local)
}

func getDayDuration(beginTime, endTime time.Time) int {
	if beginTime.After(endTime) {
		return 0
	}
	// 计算时间差值
	duration := getZeroDayTime(endTime).Sub(getZeroDayTime(beginTime))
	day := int(duration.Hours() / 24)

	return day
}

func getCountdown(oldCountdown, cycleCnt int, expireTime time.Time, useTestMode bool) (newCountDown int) {
	now := time.Now()
	if useTestMode {
		newCountDown = getMinuteCycleDuration(now, expireTime, cycleCnt)
	} else {
		newCountDown = getDayDuration(now, expireTime)
	}

	if oldCountdown > 0 && oldCountdown < newCountDown {
		newCountDown = oldCountdown
	}

	if newCountDown == 0 {
		newCountDown = 1
	}

	return newCountDown
}

func (s *Server) getBottomText(waitRecvFlag bool, buyStatus uint32, countDown int) string {
	if buyStatus == weekCardBuyStatusNotBuy {
		return "同时期仅可购买1个档位哦"

	} else if buyStatus == weekCardBuyStatusAlreadyBuy {
		if countDown == 1 && !waitRecvFlag {
			return "1天后可再次购买"
		}

		return s.bc.GetSoldWeekCardBtnText()
	} else if buyStatus == weekCardBuyStatusLocking {
		return fmt.Sprintf("%d天后可购买", countDown)
	}

	return ""
}

// BuyPresentWeekCard 购买礼物周卡
func (s *Server) BuyPresentWeekCard(ctx context.Context, req *pb.BuyPresentWeekCardRequest) (*pb.BuyPresentWeekCardResponse, error) {
	out := &pb.BuyPresentWeekCardResponse{}

	svrInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
	if !ok {
		log.ErrorWithCtx(ctx, "BuyPresentWeekCard ServiceInfoFromContext fail. in:%+v", req)
		return out, protocol.NewExactServerError(codes.Ok, status.ErrRequestParamInvalid)
	}
	opUid := svrInfo.UserID

	if !s.bc.CheckIfFeatureVersion(ctx, uint32(svrInfo.ClientType), svrInfo.ClientVersion, svrInfo.MarketID) {
		return out, nil
	}

	cardId, _ := strconv.Atoi(req.GetCardId())
	if cardId == 0 {
		return out, protocol.NewExactServerError(codes.Ok, status.ErrRequestParamInvalid)
	}

	dark, err := s.checkIfDarkUser(ctx, opUid)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetPresentWeekCardEntry fail to checkIfDarkUser. req:%v,err:%v", req, err)
		return out, err
	}

	if dark {
		// 黑产用户
		return out, protocol.NewExactServerError(codes.Ok, status.ErrPresentWeekCardReceiveFailExpired, "购买失败")
	}

	// 首次消费验证检查
	e := s.checkUsualDevice(ctx, svrInfo)
	if e != nil {
		log.ErrorWithCtx(ctx, "BuyPresentWeekCard checkUsualDevice fail. uid:%v, err:%v", opUid, e)
		return out, e
	}

	svrResp, err := s.rpc.PresentWeekCardCli.BuyPresentWeekCard(ctx, &svrPb.BuyPresentWeekCardReq{
		Uid:         opUid,
		CardId:      uint32(cardId),
		OutsideTime: time.Now().Unix(),
	})

	if err != nil {
		log.ErrorWithCtx(ctx, "BuyPresentWeekCard fail. req:%v,err:%v", req, err)
		return out, err
	}

	out.Balance = uint64(svrResp.GetBalance())

	log.InfoWithCtx(ctx, "BuyPresentWeekCard success. req:%v,resp:%v", req, out)
	return out, nil
}

// ReceivePresentWeekCardReward 领取周卡奖励
func (s *Server) ReceivePresentWeekCardReward(ctx context.Context, req *pb.ReceivePresentWeekCardRewardRequest) (*pb.ReceivePresentWeekCardRewardResponse, error) {
	out := &pb.ReceivePresentWeekCardRewardResponse{}

	svrInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
	if !ok {
		log.ErrorWithCtx(ctx, "ReceivePresentWeekCardReward ServiceInfoFromContext fail. in:%+v", req)
		return out, protocol.NewExactServerError(codes.Ok, status.ErrRequestParamInvalid)
	}
	opUid := svrInfo.UserID

	if !s.bc.CheckIfFeatureVersion(ctx, uint32(svrInfo.ClientType), svrInfo.ClientVersion, svrInfo.MarketID) {
		return out, nil
	}

	cardId, _ := strconv.Atoi(req.GetCardId())
	if cardId == 0 {
		return out, protocol.NewExactServerError(codes.Ok, status.ErrRequestParamInvalid)
	}

	dark, err := s.checkIfDarkUser(ctx, opUid)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetPresentWeekCardEntry fail to checkIfDarkUser. req:%v,err:%v", req, err)
		return out, err
	}

	if dark {
		// 黑产用户
		return out, protocol.NewExactServerError(codes.Ok, status.ErrPresentWeekCardReceiveFailExpired, "购买失败")
	}

	resp, err := s.rpc.PresentWeekCardCli.ReceivePresentWeekCardReward(ctx, &svrPb.ReceivePresentWeekCardRewardReq{
		Uid:         opUid,
		CardId:      uint32(cardId),
		OutsideTime: time.Now().Unix(),
		RecvIdx:     req.GetAwardIdx(),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "ReceivePresentWeekCardReward fail. req:%v,err:%v", req, err)
		return out, err
	}

	if len(resp.GetAwardList()) == 0 {
		return out, nil
	}

	//nameplateCfgMap, err := s.getRevenueNameplateCfg(ctx, []*svrPb.PresentWeekCardInfo{{AwardList: resp.GetAwardList()}})
	//if err != nil {
	//	log.ErrorWithCtx(ctx, "ReceivePresentWeekCardReward fail to getRevenueNameplateCfg. req:%v,err:%v", req, err)
	//	return out, err
	//}

	awardList := make([]*pb.DailyAwardInfo, 0)
	for _, award := range resp.GetAwardList() {
		if len(award.GetAwardList()) == 0 {
			continue
		}
		itemList := make([]*pb.WeekCardAwardInfo, 0, len(award.GetAwardList()))

		for _, item := range award.GetAwardList() {
			info, err := s.mgr.GetAwardConfHandler(ctx, item.GetAwardType(), item.GetAmount(), item.GetAwardId())
			if err != nil {
				log.ErrorWithCtx(ctx, "ReceivePresentWeekCardReward fail to GetAwardConfHandler. req:%v,err:%v", req, err)
				continue
			}
			itemList = append(itemList, info)
			//if item.GetAwardType() != uint32(svrPb.AwardItemType_AWARD_ITEM_TYPE_NAMEPLATE) {
			//	info, err := s.mgr.GetAwardConfHandler(ctx, item.GetAwardType(), item.GetAmount(), item.GetAwardId())
			//	if err != nil {
			//		log.ErrorWithCtx(ctx, "ReceivePresentWeekCardReward fail to GetAwardConfHandler. req:%v,err:%v", req, err)
			//		continue
			//	}
			//	itemList = append(itemList, info)
			//
			//} else {
			//	id, _ := strconv.Atoi(item.GetAwardId())
			//	cfg := nameplateCfgMap[uint32(id)]
			//	itemList = append(itemList, &pb.WeekCardAwardInfo{
			//		AwardIcon:  cfg.GetBaseUrl(),
			//		AwardName:  cfg.GetName() + "个人铭牌",
			//		VaildTimeS: item.GetAmount() * 24 * 60 * 60,
			//	})
			//}
		}

		awardList = append(awardList, &pb.DailyAwardInfo{
			Status:   award.GetStatus(),
			GiftList: itemList,
			AwardIdx: award.GetAwardOrder(),
		})
	}

	sort.SliceStable(awardList, func(i, j int) bool {
		if awardList[i].GetAwardIdx() == 0 {
			return false
		} else if awardList[j].GetAwardIdx() == 0 {
			return true
		} else {
			return awardList[i].GetAwardIdx() < awardList[j].GetAwardIdx()
		}
	})

	out.AwardList = awardList

	log.InfoWithCtx(ctx, "ReceivePresentWeekCardReward req:%v resp:%v", req, out)
	return out, nil
}

func (s *Server) checkUsualDevice(ctx context.Context, serviceInfo *protogrpc.ServiceInfo) error {
	uid := serviceInfo.UserID
	res, sErr := s.rpc.UsualDeviceClient.CheckUsualDevice(ctx, string(serviceInfo.DeviceID), uid, 1, uint32(serviceInfo.ClientType))
	if sErr != nil {
		log.ErrorWithCtx(ctx, "checkUsualDevice Fail to CheckUsualDevice err(%v)", sErr)
		return sErr
	}
	if !res.Result {
		err := s.rpc.UsualDeviceClient.GetDeviceAuthError(ctx, uint64(uid), serviceInfo.ClientType, serviceInfo.ClientVersion)
		if err != nil {
			return err
		}
	}

	return nil
}

// checkIfDarkUser 检查用户是否黑产
func (s *Server) checkIfDarkUser(ctx context.Context, uid uint32) (bool, error) {
	resp, err := s.rpc.DarkCli.BlackUserCheck(ctx, darkPB.GetBlackUserReq{
		Uid: uid,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "checkIfDarkUser fail to BlackUserCheck. uid:%v,err:%v", uid, err)
		return false, err
	}

	return resp.GetBlackFlag(), nil
}
