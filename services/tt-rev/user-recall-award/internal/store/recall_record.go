package store

import (
    "context"
    "database/sql"
    "errors"
    "fmt"
    "strings"
    "time"

    "golang.52tt.com/pkg/log"
)

var createRecallRecordTableSql = `

CREATE TABLE IF NOT EXISTS recall_record (
  id int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',
  sn varchar(255) NOT NULL DEFAULT '' COMMENT '批次号',
  recalled_uid int(10) NOT NULL COMMENT '回归人ID',
  recall_consume_val int(10) NOT NULL DEFAULT 0 COMMENT '回归累计消费',
  inc_consume_val int(10) NOT NULL DEFAULT 0 COMMENT '增加的消费',
  got_consecutive_login_award int(1) NOT NULL DEFAULT 0 COMMENT '是否领取连续登录奖励',
  got_consume_val_award_lv  int(10) NOT NULL DEFAULT 0 COMMENT '已经领取的消费奖励等级, 按位对应奖励等级',
  status int(1) NOT NULL DEFAULT 0 COMMENT '状态 0: 进行中 1: 已过期 2. 已完成',
  create_time timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  update_time timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (id),
  UNIQUE sn_unique (sn),
  INDEX recalled_uid_index (recalled_uid)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 COMMENT '回归记录表';
`

func (s *Store) createRecallRecordTable(ctx context.Context) {
    _, err := s.db.ExecContext(ctx, createRecallRecordTableSql)
    if err != nil {
        log.Errorf("create table err: %v", err)
    }
}

// RecallRecord 回归记录表的结构体, 用于记录回归任务进度
type RecallRecord struct {
    ID                       uint32    `db:"id"`
    SN                       string    `db:"sn"`                          // 批次号
    RecalledUid              uint32    `db:"recalled_uid"`                // 回归人ID
    RecallConsumeVal         uint32    `db:"recall_consume_val"`          // 回归消费
    IncConsumeVal            uint32    `db:"inc_consume_val"`             // 增加的消费
    GotConsecutiveLoginAward uint32    `db:"got_consecutive_login_award"` // 是否领取连续登录奖励
    GotConsumeValAwardLv     uint32    `db:"got_consume_val_award_lv"`    // 已经领取的消费奖励等级, 按位对应奖励等级
    Status                   uint32    `db:"status"`                      // 状态 0: 进行中 1: 已过期 2. 已完成
    CreateTime               time.Time `db:"create_time"`                 // 创建时间
    UpdateTime               time.Time `db:"update_time"`                 // 更新时间
}

func (r *RecallRecord) IsGotConsumeValAward(idx uint32) bool {
    return r.GotConsumeValAwardLv&(1<<idx) != 0
}

// AddRecallRecord 添加回归记录
func (s *Store) AddRecallRecord(ctx context.Context, record *RecallRecord) error {
    _, err := s.db.ExecContext(ctx, `
    INSERT INTO recall_record
    (
    sn,
    recalled_uid,
    recall_consume_val
    )
    VALUES (?, ?, ?)
    `, record.SN, record.RecalledUid, record.RecallConsumeVal)
    return err
}

// GetRecallRecordByUid 获取回归记录
func (s *Store) GetRecallRecordByUid(ctx context.Context, uid uint32) (*RecallRecord, error) {
    records, err := s.GetRecallRecordByUids(ctx, []uint32{uid})
    if err != nil {
        return nil, err
    }
    if len(records) < 1 {
        log.WarnWithCtx(ctx, "GetRecallRecordByUid no record")
        return nil, nil
    }
    return records[0], nil
}

// GetRecallRecordByUids 批量获取回归记录, 回归周期30天 只取30天内的数据
func (s *Store) GetRecallRecordByUids(ctx context.Context, uid []uint32) ([]*RecallRecord, error) {
    record := make([]*RecallRecord, 0, len(uid))
    time.Now().AddDate(0, 0, -30)

    if len(uid) == 0 {
        return record, nil
    }

    uidArgs := make([]interface{}, 0, len(uid))
    holder := make([]string, 0, len(uid))
    for _, id := range uid {
        uidArgs = append(uidArgs, id)
        holder = append(holder, "?")
    }
    err := s.db.SelectContext(ctx, &record, fmt.Sprintf(`
	SELECT
	*
	FROM
	recall_record
	WHERE
	recalled_uid in (%s)
    AND create_time > DATE_SUB(CURRENT_TIMESTAMP, INTERVAL 30 DAY)
	`, strings.Join(holder, ",")), uidArgs...)
    if errors.Is(err, sql.ErrNoRows) {
        return nil, nil
    }
    return record, err
}

// AddConsumeVal 添加回归累计消费
func (s *Store) AddConsumeVal(ctx context.Context, recallUid, addVal uint32) error {
    _, err := s.db.ExecContext(ctx,
        "UPDATE recall_record SET inc_consume_val = inc_consume_val + ? WHERE recalled_uid = ?"+
            " AND create_time > DATE_SUB(CURRENT_TIMESTAMP, INTERVAL 30 DAY)", addVal, recallUid)
    if err != nil {
        log.ErrorWithCtx(ctx, "MarkConsecutiveLogin ExecContext err: %v", err)
    }

    return err
}

// MarkGotConsecutiveLoginAward 标记已领取连续登录奖励
func (s *Store) MarkGotConsecutiveLoginAward(ctx context.Context, sn string) error {
    _, err := s.db.ExecContext(ctx, "UPDATE recall_record SET got_consecutive_login_award = 1 WHERE sn = ?", sn)
    if err != nil {
        log.ErrorWithCtx(ctx, "MarkGotConsecutiveLoginAward ExecContext err: %v", err)
    }
    return err
}

// MarkGotConsumeValAward 标记已领取消费奖励
func (s *Store) MarkGotConsumeValAward(ctx context.Context, sn string, lv uint32) error {
    _, err := s.db.ExecContext(ctx, "UPDATE recall_record SET got_consume_val_award_lv = got_consume_val_award_lv + ? WHERE sn = ?", 1<<lv, sn)
    if err != nil {
        log.ErrorWithCtx(ctx, "MarkGotConsumeValAward ExecContext err: %v", err)
    }
    return err
}
