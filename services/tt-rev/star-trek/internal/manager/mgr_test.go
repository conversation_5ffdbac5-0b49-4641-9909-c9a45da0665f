package manager

import (
	"context"
	"errors"
	"github.com/golang/mock/gomock"
	redisConnect "gitlab.ttyuyin.com/avengers/tyr/core/middleware/redis/connect"
	"gitlab.ttyuyin.com/tt-infra/tyr/log"
	"golang.52tt.com/clients/mocks/account"
	"golang.52tt.com/clients/mocks/backpack"
	backpacksender "golang.52tt.com/clients/mocks/backpack-sender"
	publicNotice "golang.52tt.com/clients/mocks/public-notice"
	pushV2 "golang.52tt.com/clients/mocks/push-notification/v2"
	"golang.52tt.com/clients/mocks/sendim"
	unifiedSearch "golang.52tt.com/clients/mocks/unified-search"
	userPresent "golang.52tt.com/clients/mocks/userpresent"
	backpacksenderPb "golang.52tt.com/protocol/services/backpacksender"
	backpackPB "golang.52tt.com/protocol/services/backpacksvr"
	reconcile_v2 "golang.52tt.com/protocol/services/reconcile-v2"
	pb "golang.52tt.com/protocol/services/star-trek"
	_ "golang.52tt.com/services/recommend-dialog/tools/grpc_proxy/enable"
	"golang.52tt.com/services/tt-rev/star-trek/internal/cache"
	"golang.52tt.com/services/tt-rev/star-trek/internal/conf"
	"golang.52tt.com/services/tt-rev/star-trek/internal/mysql"
	"golang.52tt.com/services/tt-rev/star-trek/mocks"
	"k8s.io/apimachinery/pkg/util/rand"
	"reflect"
	"testing"
	"time"
)

var mgr *Mgr

func init() {
	log.SetLevel(log.DebugLevel)
	sc := &conf.ServiceConfigT{}
	err := sc.Parse("../../star-trek.json")
	if err != nil {
		log.Errorf("config Parse fail err:%v", err)
		return
	}

	ctx := context.Background()

	redisConfig := &redisConnect.RedisConfig{
		//Name:     sc.GetRedisConfig().Host,
		Host:     sc.GetRedisConfig().Host,
		Port:     uint32(sc.GetRedisConfig().Port),
		Idc:      "test",
		PoolSize: sc.GetRedisConfig().PoolSize,
		Password: sc.GetRedisConfig().Password,
		DB:       sc.GetRedisConfig().DB,
	}

	cacheClient, err := cache.NewCache(ctx, redisConfig)
	if err != nil {
		log.Errorf("NewCache fail %+v, err:%v", sc.GetRedisConfig(), err)
		return
	}

	mysqlStore, err := mysql.NewMysql(sc.GetMysqlConfig(), sc.GetMysqlReadOnlyConfig())
	if err != nil {
		log.Errorf("NewMysql fail %+v, %+v, err:%v", sc.GetMysqlConfig(), sc.GetMysqlReadOnlyConfig(), err)
		return
	}

	bc, _ := conf.NewBusinessConfManager("../../star-trek-bc.json")
	mgr = &Mgr{
		store:      mysqlStore,
		redisCache: cacheClient,
		bc:         bc,
	}
}

func TestNewMgr(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	log.SetLevel(log.DebugLevel)
	sc := &conf.ServiceConfigT{}
	err := sc.Parse("../../star-trek.json")
	if err != nil {
		log.Errorf("config Parse fail err:%v", err)
		return
	}

	ctx := context.Background()

	redisConfig := &redisConnect.RedisConfig{
		//Name:     sc.GetRedisConfig().Host,
		Host:     sc.GetRedisConfig().Host,
		Port:     uint32(sc.GetRedisConfig().Port),
		Idc:      "test",
		PoolSize: sc.GetRedisConfig().PoolSize,
		Password: sc.GetRedisConfig().Password,
		DB:       sc.GetRedisConfig().DB,
	}

	cacheClient, err := cache.NewCache(ctx, redisConfig)
	if err != nil {
		log.Errorf("NewCache fail %+v, err:%v", sc.GetRedisConfig(), err)
		return
	}

	mysqlStore, err := mysql.NewMysql(sc.GetMysqlConfig(), sc.GetMysqlReadOnlyConfig())
	if err != nil {
		log.Errorf("NewMysql fail %+v, %+v, err:%v", sc.GetMysqlConfig(), sc.GetMysqlReadOnlyConfig(), err)
		return
	}

	_, err = NewMgr(mysqlStore, cacheClient, "../star-trek-bc.json")
	if err != nil {
		log.Errorf("NewMgr fail %+v, %+v, err:%v", sc.GetMysqlConfig(), sc.GetMysqlReadOnlyConfig(), err)
		return
	}
}

func TestMgr_SetStarTrekSupply(t *testing.T) {
	testSupplys := make([]*pb.SupplyConf, 0)
	testSupplys = append(testSupplys, &pb.SupplyConf{
		GiftId:    1,
		GiftType:  1,
		GiftName:  "fdssd",
		GiftPrice: 100,
		GiftPic:   "gsgsdgsg",
	})
	testSupplys = append(testSupplys, &pb.SupplyConf{
		GiftId:    2,
		GiftType:  4,
		GiftName:  "fdffa",
		GiftPrice: 100,
		GiftPic:   "gsggsg",
	})

	t.Log(mgr.SetStarTrekSupply(context.Background(), &pb.SetStarTrekSupplyReq{
		SupplyList: testSupplys,
	}))
}

func TestMgr_SetSupplyConfCache(t *testing.T) {
	sups := make([]*mysql.SupplyInfo, 0)
	t.Log(mgr.SetSupplyConfCache(context.Background(), sups))
}

func TestMgr_GetSupplys(t *testing.T) {
	t.Log(mgr.GetSupplys(context.Background(), &pb.GetStarTrekSupplyReq{}))
}

func TestMgr_GetSupplys2(t *testing.T) {
	t.Log(mgr.GetSupplys(context.Background(), &pb.GetStarTrekSupplyReq{}))
}

func TestMgr_DelSupplyBySupplyId(t *testing.T) {
	t.Log(mgr.DelSupplyBySupplyId(context.Background(), 1))
}

func TestMgr_UpdateStarTrekConf(t *testing.T) {
	openDatList := make([]uint32, 0)
	openDatList = append(openDatList, uint32(1))
	openDatList = append(openDatList, uint32(6))

	prizes := make([]*pb.StarTrekPrizeInfo, 0)
	prizes = append(prizes, &pb.StarTrekPrizeInfo{
		BgId:      34,
		Weight:    12,
		GiftId:    23,
		GiftPrice: 1000000,
		GiftName:  "fsdgsg",
		GiftPic:   "gsdgsdfgh",
		GiftNum:   1,
	})
	prizes = append(prizes, &pb.StarTrekPrizeInfo{
		BgId:      3,
		Weight:    90,
		GiftId:    24,
		GiftPrice: 1000000,
		GiftName:  "fsdgsdsfg",
		GiftPic:   "gsdgsdgfsdfgh",
		GiftNum:   1,
	})
	prizes = append(prizes, &pb.StarTrekPrizeInfo{
		BgId:      89,
		Weight:    100,
		GiftId:    22,
		GiftPrice: 1000000,
		GiftName:  "fsdgsdsfg",
		GiftPic:   "gsdgsdgfsdfgh",
		GiftNum:   1,
	})

	mConf := &pb.StarTrekConf{
		ConfId:        1,
		ActivityBegin: 1656904630,
		ActivityEnd:   1656904780,
		DayBegin:      28800,
		DayEnd:        79200,
		WDayList:      openDatList,
		PrizeList:     prizes,
	}

	t.Log(mgr.UpdateStarTrekConf(context.Background(), &pb.UpdateStarTrekConfReq{
		ConfId: 1,
		Conf:   mConf,
	}))
}

func TestMgr_UpdateStarTrekConf2(t *testing.T) {
	openDatList := make([]uint32, 0)
	openDatList = append(openDatList, uint32(1))
	openDatList = append(openDatList, uint32(6))

	prizes := make([]*pb.StarTrekPrizeInfo, 0)
	prizes = append(prizes, &pb.StarTrekPrizeInfo{
		BgId:      34,
		Weight:    12,
		GiftId:    23,
		GiftPrice: 1000000,
		GiftName:  "fsdgsg",
		GiftPic:   "gsdgsdfgh",
		GiftNum:   1,
	})
	prizes = append(prizes, &pb.StarTrekPrizeInfo{
		BgId:      3,
		Weight:    90,
		GiftId:    24,
		GiftPrice: 1000000,
		GiftName:  "fsdgsdsfg",
		GiftPic:   "gsdgsdgfsdfgh",
		GiftNum:   1,
	})
	prizes = append(prizes, &pb.StarTrekPrizeInfo{
		BgId:      89,
		Weight:    100,
		GiftId:    22,
		GiftPrice: 1000000,
		GiftName:  "fsdgsdsfg",
		GiftPic:   "gsdgsdgfsdfgh",
		GiftNum:   1,
	})

	mConf := &pb.StarTrekConf{
		ActivityBegin: 1657258900,
		ActivityEnd:   1657258999,
		DayBegin:      28800,
		DayEnd:        79200,
		WDayList:      openDatList,
		PrizeList:     prizes,
	}

	t.Log(mgr.UpdateStarTrekConf(context.Background(), &pb.UpdateStarTrekConfReq{
		ConfId: 2,
		Conf:   mConf,
	}))
}

func TestMgr_DelActivityByActivityId(t *testing.T) {
	t.Log(mgr.DelActivityByActivityId(context.Background(), 1))
}

func TestMgr_GetStarTrekConf(t *testing.T) {
	t.Log(mgr.GetStarTrekConf(context.Background(), &pb.GetStarTrekConfReq{
		Status: 2,
		Limit:  10,
		Offset: 0,
	}))
}

func TestMgr_GetActivityById(t *testing.T) {
	ids := make([]uint32, 0)
	ids = append(ids, 1)
	ids = append(ids, 2)
	t.Log(mgr.GetActivityById(context.Background(), ids))
}

func TestMgr_ChoseBingo(t *testing.T) {
	investList := []*mysql.UserInvestVal{
		{Uid: 1, InvestVal: 1000},
		{Uid: 2, InvestVal: 1},
	}
	totalInvest := uint32(0)
	for _, info := range investList {
		totalInvest += info.InvestVal
	}
	// 生成随机数
	randomVal := uint32(rand.Intn(int(totalInvest)))
	currVal := uint32(0)
	bingoUid := uint32(0)

	// chose bingo uid
	for _, info := range investList {
		currVal += info.InvestVal
		if currVal > randomVal {
			bingoUid = info.Uid
			break
		}
	}

	t.Log(totalInvest, randomVal, bingoUid)
}

func TestMgr_SetStarTrekConf(t *testing.T) {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	redisMock := mocks.NewMockICache(ctrl)
	mysqlMock := mocks.NewMockIStore(ctrl)
	bcMock := mocks.NewMockIBusinessConfManager(ctrl)

	mgr := &Mgr{
		bc:         bcMock,
		store:      mysqlMock,
		redisCache: redisMock,
	}

	cases := []struct {
		Name    string
		conf    *pb.StarTrekConf
		wantErr bool
	}{
		{"conf actbegin > actend", &pb.StarTrekConf{
			ActivityName:  "anyName",
			ActivityBegin: 100,
			ActivityEnd:   99,
		}, true},
		{"conf actbegin > actend", &pb.StarTrekConf{
			ActivityBegin: 100,
			ActivityEnd:   101,
			PrizeList: []*pb.StarTrekPrizeInfo{{
				BgId:      1,
				Weight:    1,
				GiftId:    1,
				GiftPrice: 49999,
				GiftName:  "礼物",
				GiftPic:   "picture",
				GiftNum:   1,
			}},
		}, true},
		{"conf actTime conflict", &pb.StarTrekConf{
			ActivityName:  "anyName",
			ActivityBegin: 1663664204,
			ActivityEnd:   1663665204,
			PrizeList: []*pb.StarTrekPrizeInfo{{
				BgId:      1,
				Weight:    1,
				GiftId:    1,
				GiftPrice: 50000,
				GiftName:  "礼物",
				GiftPic:   "picture",
				GiftNum:   1,
			}},
			TimeList: nil,
		}, true},
		{"conf common", &pb.StarTrekConf{
			ActivityName:  "anyName",
			ActivityBegin: uint32(time.Now().Add(-10 * time.Second).Unix()),
			ActivityEnd:   uint32(time.Date(time.Now().Year(), time.Now().Month(), time.Now().Day(), 23, 59, 59, 0, time.Local).Unix()),
			PrizeList: []*pb.StarTrekPrizeInfo{{
				BgId:      1,
				Weight:    1,
				GiftId:    1,
				GiftPrice: 50000,
				GiftName:  "礼物",
				GiftPic:   "picture",
				GiftNum:   1,
			}},
			TimeList: []*pb.DayTimeRangeInfo{
				{
					Selected: true,
					DayBegin: 0,
					DayEnd:   86399,
					WDayList: []uint32{0, 1, 2, 3, 4, 5, 6},
				},
				{
					Selected: false,
					DayBegin: 84600,
					DayEnd:   86399,
					WDayList: []uint32{0, 1, 2, 3, 4, 5, 6},
				},
			},
		}, false},
	}

	for _, testCase := range cases {
		gomock.InOrder(
			bcMock.EXPECT().GetAwardPriceFloor().Return(uint32(50000)).AnyTimes(),
			bcMock.EXPECT().GetTesting().Return(false).AnyTimes(),
			mysqlMock.EXPECT().GetRoundInfoByStatus(ctx, uint32(mysql.RoundStatusPending)).Return(&mysql.RoundInfo{
				RoundId: 1,
				ActId:   1,
			}, true, nil).AnyTimes(),
			mysqlMock.EXPECT().GetActivityTimeForCheck(ctx).Return([]*mysql.ActivityTime{
				{
					Id:            1,
					ActivityName:  "anyName2",
					ActivityBegin: time.Unix(1663664304, 0),
					ActivityEnd:   time.Unix(1663665204, 0),
				}}, nil).AnyTimes(),
			mysqlMock.EXPECT().Transaction(ctx, gomock.Any()).Return(nil).AnyTimes(),
			mysqlMock.EXPECT().DelPrizesByActivityId(ctx, nil, gomock.Any()).Return(nil).AnyTimes(),
			mysqlMock.EXPECT().InsertOrUpdatePrizeConf(ctx, nil, gomock.Any()).Return(nil).AnyTimes(),
			redisMock.EXPECT().DelActivityConfCache(ctx).Return(nil).AnyTimes(),
		)

		t.Run(testCase.Name, func(t *testing.T) {
			_, err := mgr.SetStarTrekConf(ctx, &pb.SetStarTrekConfReq{
				Conf: testCase.conf,
			})
			if (err != nil) != testCase.wantErr {
				t.Errorf("SetStarTrekConf error = %v, wantErr = %v", err, testCase.wantErr)
				return
			}

			if err == nil {
				time.Sleep(300 * time.Millisecond) // 等待协程执行完
			}
		})
	}
}

func TestMgr_DoInvest(t *testing.T) {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	roundId := uint32(1)
	uid := uint32(1)
	roundBegin := time.Now().Add(-30 * time.Minute)

	req := &pb.DoInvestReq{
		Uid:       1,
		ChannelId: 1,
		RoundId:   1,
		SupplyList: []*pb.CostSupplyInfo{
			{
				GiftId:   1,
				GiftType: uint32(pb.PackageItemType_BACKPACK_LOTTERY_FRAGMENT),
				GiftNum:  1,
			},
			{
				GiftId:   2,
				GiftType: uint32(pb.PackageItemType_BACKPACK_PRESENT),
				GiftNum:  1,
			},
		},
		Cost: uint32(200),
	}
	wantResp := &pb.DoInvestResp{
		RoundId:          1,
		MySupply:         300,
		RoundSupplyLimit: 10000,
		ServerSupply:     0,
		DailyInvest:      300,
		GoalServerSupply: 1000,
	}

	redisMock := mocks.NewMockICache(ctrl)
	mysqlMock := mocks.NewMockIStore(ctrl)
	bcMock := mocks.NewMockIBusinessConfManager(ctrl)
	backpackMock := backpack.NewMockIClient(ctrl)

	mgr := &Mgr{
		bc:          bcMock,
		store:       mysqlMock,
		redisCache:  redisMock,
		backpackCli: backpackMock,
	}
	mgr.userDailyLimit = uint32(100000)

	redisMock.EXPECT().GetSupplyConfCache(gomock.Any()).Return(true, []*cache.SupplyInfo{
		{
			SupplyId:  1,
			GiftId:    "1",
			GiftType:  uint32(pb.PackageItemType_BACKPACK_LOTTERY_FRAGMENT),
			GiftPrice: 100,
			GiftName:  "111",
			GiftPic:   "111",
		},
		{
			SupplyId:  1,
			GiftId:    "2",
			GiftType:  uint32(pb.PackageItemType_BACKPACK_PRESENT),
			GiftPrice: 100,
			GiftName:  "222",
			GiftPic:   "222",
		},
	}, nil)

	mysqlMock.EXPECT().GetRoundInfo(gomock.Any(), roundId).Return(&mysql.RoundInfo{
		RoundId:     1,
		ActId:       1,
		PackId:      1,
		GiftId:      100,
		GiftName:    "100100100",
		GiftUrl:     "100100100",
		GiftPrice:   1000,
		GiftNum:     1,
		TotalInvest: 100,
		BingoUid:    0,
		Status:      0,
		IsHide:      false,
		BeginTime:   roundBegin,
		EndTime:     roundBegin.Add(60 * time.Minute),
		CreateTime:  roundBegin,
		UpdateTime:  roundBegin,
	}, true, nil)

	bcMock.EXPECT().GetRoundMutiBingoEnable().Return(false)

	redisMock.EXPECT().LockUserInvest(gomock.Any(), roundId, uid, 8*time.Second).Return(true, nil)
	mysqlMock.EXPECT().GetUserInvestInfo(ctx, roundId, uid, roundBegin).Return(&mysql.UserInvestInfo{
		Id:         1,
		RoundId:    1,
		Uid:        1,
		InvestVal:  100,
		Status:     0,
		RoundTime:  roundBegin.Add(60 * time.Minute),
		CreateTime: roundBegin.Add(1 * time.Minute),
		UpdateTime: roundBegin.Add(1 * time.Minute),
	}, true, nil)

	// 每日限制检查
	redisMock.EXPECT().GetUserDailyPrice(gomock.Any(), gomock.Any(), uid).Return(uint32(100), nil)

	bcMock.EXPECT().GetUserMaxInvestPerRound().Return(uint32(10000))
	roundTime := roundBegin
	//mgrMock.EXPECT().InvestCostBackpackItem(gomock.Any(), uid, roundBegin, gomock.Any()).Return(nil)
	mysqlMock.EXPECT().InsertInvestLogs(gomock.Any(), roundTime, uid, gomock.Any()).Return(nil)
	backpackMock.EXPECT().UseBackpackItem(gomock.Any(), gomock.Any()).AnyTimes() // 实际上还是关心是否有报错的

	// 事务的覆盖率另外写
	mysqlMock.EXPECT().Transaction(ctx, gomock.Any()).Return(nil).AnyTimes()

	// 清缓存
	redisMock.EXPECT().DelUserInvest(gomock.Any(), roundId, uid).Return(nil)
	redisMock.EXPECT().DelPlatformInvest(gomock.Any(), roundId)

	redisMock.EXPECT().AddInvestUser(gomock.Any(), roundId, uid, gomock.Any()).Return(nil)
	redisMock.EXPECT().IncrUserDailyPrice(gomock.Any(), gomock.Any(), uid, gomock.Any()).Return(nil)
	bcMock.EXPECT().GetUserInvestFlagExpireDay().Return(uint32(30))
	redisMock.EXPECT().SetUserInvestFlag(gomock.Any(), uid, time.Duration(30*24*3600)*time.Second).Return(nil)
	bcMock.EXPECT().GetUserMaxInvestPerRound().Return(uint32(10000))

	// 最后
	redisMock.EXPECT().UnlockUserInvest(gomock.Any(), roundId, uid).Return(nil)

	resp, err := mgr.DoInvest(ctx, req)
	if err != nil {
		t.Errorf("Test DoInvest fail, err:%v, wantErr:%v", err, false)
	}
	if !reflect.DeepEqual(resp, wantResp) {
		t.Errorf("wantResp=%v, resp=%v, doesnt eqaul", wantResp, resp)
	}
}

func TestMgr_GetCurStarTrekInfo(t *testing.T) {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	//mgrMock := mocks.NewMockIMgr(ctrl)
	redisMock := mocks.NewMockICache(ctrl)
	mysqlMock := mocks.NewMockIStore(ctrl)
	bcMock := mocks.NewMockIBusinessConfManager(ctrl)

	mgr := &Mgr{
		bc:         bcMock,
		store:      mysqlMock,
		redisCache: redisMock,
	}

	uid := uint32(1)
	beginTime := time.Now().Add(10 * time.Minute)
	endTime := beginTime.Add(60 * time.Minute)
	FinTime := uint32(time.Now().Add(500 * time.Minute).Unix())
	wantResp := &pb.GetStatTrekInfoResp{
		RoundId:   2,
		BeginTime: uint32(beginTime.Unix()),
		EndTime:   uint32(endTime.Unix()),
		AwardPack: &pb.StarTAwardInfo{
			PackId:     2,
			PackName:   "222",
			PackPic:    "222",
			PackAmount: 1,
			UnitPrice:  100000,
			FinTime:    FinTime,
			GiftId:     2,
		},
		InvestProgress: 100,
		UserCount:      1,
		LastS: &pb.LastRoundReview{
			RoundId:    1,
			RoundTime:  uint32(beginTime.Add(-15 * time.Second).Unix()),
			TrekResult: true,
			AwardPack: &pb.StarTAwardInfo{
				PackId:     1,
				PackName:   "111",
				PackPic:    "111",
				PackAmount: 1,
				UnitPrice:  100000,
				FinTime:    FinTime,
				GiftId:     1,
			},
			UserInfo:     &pb.StarTUserInfo{Uid: 1},
			UserList:     []uint32{1, 2, 4},
			BingoUserNum: 3,
		},
		NextS: &pb.NextRoundForecast{
			AwardPack: &pb.StarTAwardInfo{
				PackId:     3,
				PackName:   "333",
				PackPic:    "333",
				PackAmount: 1,
				UnitPrice:  100000,
				FinTime:    FinTime,
				GiftId:     3,
			},
		},
		Records: []*pb.RollingAwardInfo{
			{
				Uid:          1,
				PackPic:      "111",
				PackAmount:   1,
				PackName:     "111",
				BingoUserNum: 2,
			}},
		LatestPartitions: []*pb.StarTUserInfo{{Uid: 1}},
		MySupply:         100,
		RoundSupplyLimit: 10000,
		MinInvest:        100,
		UserDailyInvest:  10000,
	}

	// 获取相对静态配置, 从缓存获取
	redisMock.EXPECT().GetCurStarTrekInfoCache(gomock.Any()).Return(true, &cache.CurStarTrekInfo{
		RoundId:   2,
		BeginTime: uint32(beginTime.Unix()),
		EndTime:   uint32(endTime.Unix()),
		AwardPack: &pb.StarTAwardInfo{
			PackId:     2,
			PackName:   "222",
			PackPic:    "222",
			PackAmount: 1,
			UnitPrice:  100000,
			FinTime:    FinTime,
			GiftId:     2,
		},
		LastRound: &pb.LastRoundReview{
			RoundId:    1,
			RoundTime:  uint32(beginTime.Add(-15 * time.Second).Unix()),
			TrekResult: true,
			AwardPack: &pb.StarTAwardInfo{
				PackId:     1,
				PackName:   "111",
				PackPic:    "111",
				PackAmount: 1,
				UnitPrice:  100000,
				FinTime:    FinTime,
				GiftId:     1,
			},
			UserInfo:     &pb.StarTUserInfo{Uid: 1},
			BingoUserNum: 3,
		},
		NextRound: &pb.NextRoundForecast{
			AwardPack: &pb.StarTAwardInfo{
				PackId:     3,
				PackName:   "333",
				PackPic:    "333",
				PackAmount: 1,
				UnitPrice:  100000,
				FinTime:    FinTime,
				GiftId:     3,
			},
		},
		RollRecord: &pb.RollingAwardInfo{
			Uid:          1,
			PackPic:      "111",
			PackAmount:   1,
			PackName:     "111",
			BingoUserNum: 2,
		},
	}, nil)

	redisMock.EXPECT().IsRoundBingoListMember(ctx, uint32(1), uint32(1)).Return(true, nil)
	redisMock.EXPECT().GetRoundRandBingoList(ctx, uint32(1), uint32(15)).Return([]uint32{1, 2, 4}, nil)
	redisMock.EXPECT().IsRoundBingoListMember(ctx, gomock.Any(), gomock.Any()).Return(true, nil)
	// 动态配置
	bcMock.EXPECT().GetUserMinPerInvest().Return(uint32(100))
	bcMock.EXPECT().GetUserMaxInvestPerRound().Return(uint32(10000))
	// 投入值，redis缓存命中情况
	redisMock.EXPECT().GetUserInvest(gomock.Any(), uint32(2), uid).Return(uint32(100), true, nil)
	redisMock.EXPECT().GetPlatformInvest(gomock.Any(), uint32(2)).Return(uint32(100), true, nil)
	// 最新参与列表
	redisMock.EXPECT().GetInvestUserList(gomock.Any(), uint32(2), uint32(15)).Return([]uint32{uint32(1)}, nil)
	redisMock.EXPECT().GetInvestUserLen(gomock.Any(), uint32(2)).Return(uint32(1), nil)

	redisMock.EXPECT().GetUserDailyPrice(gomock.Any(), gomock.Any(), uint32(1)).Return(uint32(10000), nil)

	resp, err := mgr.GetCurStarTrekInfo(ctx, uid)
	if err != nil {
		t.Errorf("Test DoInvest fail, err:%v, wantErr:%v", err, false)
	}
	if !reflect.DeepEqual(resp, wantResp) {
		t.Errorf("wantResp=%v, \nresp=%v, doesnt eqaul", wantResp, resp)
	}
}

//// 所有缓存都失效的情况
//func TestMgr_GetCurStarTrekInfo2(t *testing.T) {
//	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
//	defer cancel()
//
//	ctrl := gomock.NewController(t)
//	defer ctrl.Finish()
//
//	//mgrMock := mocks.NewMockIMgr(ctrl)
//	redisMock := mocks.NewMockICache(ctrl)
//	mysqlMock := mocks.NewMockIStore(ctrl)
//	bcMock := mocks.NewMockIBusinessConfManager(ctrl)
//
//	mgr := &Mgr{
//		bc:         bcMock,
//		store:      mysqlMock,
//		redisCache: redisMock,
//	}
//
//	uid := uint32(1)
//	beginTime := time.Now().Add(10 * time.Minute)
//	endTime := beginTime.Add(60 * time.Minute)
//	//FinTime := uint32(time.Now().Add(500 * time.Minute).Unix())
//	wantResp := &pb.GetStatTrekInfoResp{
//		RoundId:   2,
//		BeginTime: uint32(beginTime.Unix()),
//		EndTime:   uint32(endTime.Unix()),
//		AwardPack: &pb.StarTAwardInfo{
//			PackId:     2,
//			PackName:   "222",
//			PackPic:    "222",
//			PackAmount: 1,
//			UnitPrice:  100000,
//			GiftId:     2,
//		},
//		InvestProgress: 100,
//		UserCount:      1,
//		LastS: &pb.LastRoundReview{
//			RoundId:    1,
//			RoundTime:  uint32(beginTime.Add(-15 * time.Second).Unix()),
//			TrekResult: true,
//			AwardPack: &pb.StarTAwardInfo{
//				PackId:     1,
//				PackName:   "111",
//				PackPic:    "111",
//				PackAmount: 1,
//				UnitPrice:  100000,
//				GiftId:     1,
//			},
//			UserInfo: &pb.StarTUserInfo{Uid: 1},
//		},
//		NextS: &pb.NextRoundForecast{
//			AwardPack: &pb.StarTAwardInfo{
//				PackId:     3,
//				PackName:   "333",
//				PackPic:    "333",
//				PackAmount: 1,
//				UnitPrice:  100000,
//				GiftId:     3,
//			},
//		},
//		Records: []*pb.RollingAwardInfo{
//			{
//				Uid:        1,
//				PackPic:    "111",
//				PackAmount: 1,
//				PackName:   "111",
//			}},
//		LatestPartitions: []*pb.StarTUserInfo{{Uid: 1}},
//		MySupply:         100,
//		RoundSupplyLimit: 10000,
//		MinInvest:        100,
//	}
//
//	// 获取相对静态配置,缓存未命中
//	redisMock.EXPECT().GetCurStarTrekInfoCache(gomock.Any()).Return(false, &cache.CurStarTrekInfo{}, nil)
//	// 获取当前轮次
//	mysqlMock.EXPECT().GetPreviousRoundInfo(gomock.Any(), uint32(1)).Return([]*mysql.RoundInfo{
//		{
//			RoundId:     2,
//			ActId:       1,
//			PackId:      2,
//			GiftId:      2,
//			GiftName:    "222",
//			GiftUrl:     "222",
//			GiftPrice:   100000,
//			GiftNum:     1,
//			TotalInvest: 100,
//			Status:      0,
//			BeginTime:   beginTime,
//			EndTime:     endTime,
//		},
//	}, nil)
//	// 上期回顾
//	mysqlMock.EXPECT().GetPreviousRoundInfo(gomock.Any(), uint32(2)).Return([]*mysql.RoundInfo{
//		{
//			RoundId:     1,
//			ActId:       1,
//			PackId:      1,
//			GiftId:      1,
//			GiftName:    "111",
//			GiftUrl:     "111",
//			GiftPrice:   100000,
//			GiftNum:     1,
//			BingoUid:    1,
//			TotalInvest: 100000,
//			Status:      mysql.RoundStatusSuccess,
//			EndTime:     beginTime.Add(-15 * time.Second),
//		},
//	}, nil)
//	// 下期回顾
//	redisMock.EXPECT().GetNextRoundPrizeCache(gomock.Any()).Return(true, &pb.StarTAwardInfo{
//		PackId:     3,
//		PackName:   "333",
//		PackPic:    "333",
//		PackAmount: 1,
//		UnitPrice:  100000,
//		GiftId:     3,
//	}, nil)
//	// 轮播记录
//	mysqlMock.EXPECT().GetLastSuccessRoundInfo(gomock.Any(), uint32(1)).Return(true, &mysql.RoundInfo{
//		RoundId:     1,
//		ActId:       1,
//		PackId:      1,
//		GiftId:      1,
//		GiftName:    "111",
//		GiftUrl:     "111",
//		GiftPrice:   100000,
//		BingoUid:    1,
//		GiftNum:     1,
//		TotalInvest: 100000,
//		Status:      mysql.RoundStatusSuccess,
//		EndTime:     beginTime.Add(-15 * time.Second),
//	}, nil)
//
//	// 设置缓存
//	redisMock.EXPECT().SetCurStarTrekInfoCache(gomock.Any(), gomock.Any(), gomock.Any())
//	bcMock.EXPECT().GetEveryRoundLongSc() // 返回any
//
//	// 动态配置
//	bcMock.EXPECT().GetUserMinPerInvest().Return(uint32(100))
//	bcMock.EXPECT().GetUserMaxInvestPerRound().Return(uint32(10000))
//	// 投入值，redis缓存命中情况
//	redisMock.EXPECT().GetUserInvest(uint32(2), uid).Return(uint32(100), true, nil)
//	redisMock.EXPECT().GetPlatformInvest(uint32(2)).Return(uint32(100), true, nil)
//	// 最新参与列表
//	redisMock.EXPECT().GetInvestUserList(uint32(2), uint32(20)).Return([]uint32{uint32(1)}, nil)
//	redisMock.EXPECT().GetInvestUserLen(uint32(2)).Return(uint32(1), nil)
//
//	resp, err := mgr.GetCurStarTrekInfo(ctx, uid)
//	if err != nil {
//		t.Errorf("Test DoInvest fail, err:%v, wantErr:%v", err, false)
//	}
//	if !reflect.DeepEqual(resp, wantResp) {
//		t.Errorf("wantResp=%+v, resp=%+v, doesnt eqaul", wantResp, resp)
//	}
//}

// 我的记录
func TestMgr_GetMyTrekRecords(t *testing.T) {

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	//mgrMock := mocks.NewMockIMgr(ctrl)
	redisMock := mocks.NewMockICache(ctrl)
	mysqlMock := mocks.NewMockIStore(ctrl)
	bcMock := mocks.NewMockIBusinessConfManager(ctrl)

	mgr := &Mgr{
		bc:         bcMock,
		store:      mysqlMock,
		redisCache: redisMock,
	}

	roundTime := time.Now().Add(-15 * time.Second)
	uid := uint32(1)
	_ = &pb.GetMyTrekRecordResp{
		MyRecordList: []*pb.MyTrekRecord{
			{
				Id:          "2022-08-1",
				Uid:         1,
				RoundId:     1,
				RoundTime:   uint32(roundTime.Unix()),
				TrekResult:  true,
				PrizeName:   "111",
				PrizePic:    "111",
				LuckyUid:    1,
				MyInvest:    100000,
				MySupplyDec: "111*1000",
				MyAwardDec:  "大奖*1",
			},
		},
	}

	mysqlMock.EXPECT().GetUserInvestRecords(gomock.Any(), uid, uint32(10), "", []uint32{mysql.UserInvestDone, mysql.UserInvestFail}, false).Return(
		[]*mysql.InvestRecord{
			{
				Id:        1,
				RoundId:   1,
				Uid:       1,
				InvestVal: 100000,
				Status:    mysql.UserInvestDone,
				RoundTime: roundTime,
				CostDesc:  "111*1000",
			},
		}, nil)
	mysqlMock.EXPECT().BatchGetRoundRecords(gomock.Any(), []uint32{1}).Return(map[uint32]*mysql.RoundInfo{
		uint32(1): {
			RoundId:     1,
			ActId:       1,
			PackId:      1,
			GiftId:      1,
			GiftName:    "111",
			GiftUrl:     "111",
			GiftPrice:   100000,
			GiftNum:     1,
			TotalInvest: 100000,
			BingoUid:    1,
			Status:      mysql.RoundStatusSuccess,
			EndTime:     roundTime,
		}}, nil)
	mysqlMock.EXPECT().GetUserAwardRecordList(gomock.Any(), uid, []uint32{1}, gomock.Any()).Return([]*mysql.AwardRecord{
		{
			RoundId:    1,
			Uid:        1,
			Status:     1, // 发奖成功
			BingoType:  mysql.BingoTypeComm,
			GiftId:     1,
			PackId:     1,
			PackDesc:   "大奖",
			PackWorth:  100000,
			PackAmount: 1,
			AwardTime:  roundTime.Add(3 * time.Second),
			RoundTime:  roundTime,
		}}, nil)

	_, err := mgr.GetMyTrekRecords(ctx, uid, 0, 10, "")
	if err != nil {
		t.Errorf("Test GetMyTrekRecords fail, err:%v, wantErr:%v", err, false)
	}
}

func TestMgr_GetCurAtcTimeAndRoundTime(t *testing.T) {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	//mgrMock := mocks.NewMockIMgr(ctrl)
	redisMock := mocks.NewMockICache(ctrl)
	mysqlMock := mocks.NewMockIStore(ctrl)
	bcMock := mocks.NewMockIBusinessConfManager(ctrl)

	mgr := &Mgr{
		bc:         bcMock,
		store:      mysqlMock,
		redisCache: redisMock,
	}

	now := time.Now()
	dayBegin := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())
	roundBegin := now.Add(-30 * time.Minute)
	req := &pb.GetCurAtcTimeAndRoundTimeReq{
		Uid: 1,
	}
	wantResp := &pb.GetCurAtcTimeAndRoundTimeResp{
		ActId:      1,
		DayBeing:   uint32(dayBegin.Unix()),
		DayEnd:     uint32(dayBegin.Add(23*time.Hour + 59*time.Minute + 59*time.Second).Unix()),
		RoundId:    1,
		RoundBegin: uint32(roundBegin.Unix()),
		RoundEnd:   uint32(roundBegin.Add(60*time.Minute + 1800*time.Second).Unix()),
	}

	redisMock.EXPECT().GetCurStarTrekInfoCache(gomock.Any()).Return(true, &cache.CurStarTrekInfo{
		RoundId:   1,
		BeginTime: uint32(roundBegin.Unix()),
		EndTime:   uint32(roundBegin.Add(60 * time.Minute).Unix()),
		AwardPack: &pb.StarTAwardInfo{
			PackId:     2,
			PackName:   "222",
			PackPic:    "222",
			PackAmount: 1,
			UnitPrice:  100000,
			GiftId:     2,
		},
		LastRound: &pb.LastRoundReview{},
		NextRound: &pb.NextRoundForecast{
			AwardPack: &pb.StarTAwardInfo{
				PackId:     3,
				PackName:   "333",
				PackPic:    "333",
				PackAmount: 1,
				UnitPrice:  100000,
				GiftId:     3,
			},
		},
		RollRecord: &pb.RollingAwardInfo{},
	}, nil)

	bcMock.EXPECT().GetFinalRoundDelaySec().Return(uint32(1800))

	redisMock.EXPECT().GetActivityConfCache(gomock.Any()).Return(true, &cache.ActivityConf{ActivityTime: &mysql.ActivityTime{
		Id:            1,
		ActivityName:  "111",
		ActivityBegin: dayBegin.Add(-24 * time.Hour),
		ActivityEnd:   dayBegin.Add(24 * time.Hour),
		TimeRange:     "[{\"Selected\":true, \"DayBegin\":0, \"DayEnd\":86399, \"WDayList\":[0,1,2,3,4,5,6]}]",
		Status:        uint32(pb.StarTrekStatus_Using),
	}}, nil)

	resp, err := mgr.GetCurAtcTimeAndRoundTime(ctx, req)
	if err != nil {
		t.Errorf("Test DoInvest fail, err:%v, wantErr:%v", err, false)
	}
	if !reflect.DeepEqual(resp, wantResp) {
		t.Errorf("wantResp=%+v, \nresp=%+v, doesnt eqaul", wantResp, resp)
	}
}

func TestMgr_GenerateNewRoundInfo(t *testing.T) {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	//mgrMock := mocks.NewMockIMgr(ctrl)
	redisMock := mocks.NewMockICache(ctrl)
	mysqlMock := mocks.NewMockIStore(ctrl)
	bcMock := mocks.NewMockIBusinessConfManager(ctrl)

	mgr := &Mgr{
		bc:         bcMock,
		store:      mysqlMock,
		redisCache: redisMock,
	}

	actId := uint32(1)
	wantRet := &mysql.RoundInfo{
		ActId:     1,
		PackId:    2,
		GiftId:    2,
		GiftName:  "222",
		GiftUrl:   "222",
		GiftPrice: 100000,
		GiftNum:   1,
	}

	mysqlMock.EXPECT().GetPrizesLastMtimeByActId(gomock.Any(), actId).Return(uint32(time.Now().Unix()), nil)
	mysqlMock.EXPECT().GetPrizesByActivityId(gomock.Any(), actId).Return([]*mysql.Prize{
		{
			ActivityId: 1,
			BgId:       2,
			GiftId:     2,
			GiftPrice:  100000,
			GiftNum:    1,
			GiftName:   "222",
			GiftPic:    "222",
			Weight:     1,
		}}, nil)

	// 实际上只是从奖池随记抽取一个星际礼物而已，实际并未操作数据库
	Ret, err := mgr.GenerateNewRoundInfo(ctx, actId)
	if err != nil {
		t.Errorf("Test GenerateNewRoundInfo fail, err:%v, wantErr:%v", err, false)
	}
	if !reflect.DeepEqual(Ret, wantRet) {
		t.Errorf("wantRet=%+v, resp=%+v, doesnt eqaul", wantRet, Ret)
	}
}

func TestMgr_GetValueChange(t *testing.T) {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	//mgrMock := mocks.NewMockIMgr(ctrl)
	redisMock := mocks.NewMockICache(ctrl)
	mysqlMock := mocks.NewMockIStore(ctrl)
	bcMock := mocks.NewMockIBusinessConfManager(ctrl)

	mgr := &Mgr{
		bc:         bcMock,
		store:      mysqlMock,
		redisCache: redisMock,
	}

	uid := uint32(1)
	roundId := uint32(1)
	BeginTime := time.Now().Add(-10 * time.Minute)
	wantResp := &pb.GetSupplyValueChangeResp{
		MySupply:         1000,
		MinInvest:        100,
		RoundSupplyLimit: 10000,
		ServerSupply:     1000,
	}

	bcMock.EXPECT().GetUserMinPerInvest().Return(uint32(100))
	bcMock.EXPECT().GetUserMaxInvestPerRound().Return(uint32(10000))
	mysqlMock.EXPECT().GetRoundInfoByStatus(gomock.Any(), []uint32{mysql.RoundStatusPending}).Return(&mysql.RoundInfo{
		RoundId:     1,
		ActId:       1,
		TotalInvest: 1000,
		BeginTime:   BeginTime,
	}, true, nil)
	redisMock.EXPECT().GetUserInvest(gomock.Any(), gomock.Any(), uid).Return(uint32(0), false, nil)
	mysqlMock.EXPECT().GetUserInvestInfo(gomock.Any(), roundId, uid, BeginTime).Return(&mysql.UserInvestInfo{
		RoundId:   roundId,
		Uid:       uid,
		InvestVal: 1000,
		Status:    mysql.UserInvestDone,
	}, true, nil)
	redisMock.EXPECT().SetUserInvest(gomock.Any(), gomock.Any(), uid, uint32(1000)).Return(nil)
	// 平台投入值
	redisMock.EXPECT().GetPlatformInvest(gomock.Any(), roundId).Return(uint32(0), false, nil)
	mysqlMock.EXPECT().GetRoundInfo(gomock.Any(), roundId).Return(&mysql.RoundInfo{
		RoundId:     1,
		ActId:       1,
		TotalInvest: 1000,
		BeginTime:   BeginTime,
	}, true, nil).AnyTimes()
	redisMock.EXPECT().SetPlatformInvest(gomock.Any(), roundId, gomock.Any()).Return(nil)

	resp, err := mgr.GetValueChange(ctx, uid)
	if err != nil {
		t.Errorf("Test DoInvest fail, err:%v, wantErr:%v", err, false)
	}
	if !reflect.DeepEqual(resp, wantResp) {
		t.Errorf("wantResp=%+v, resp=%+v, doesnt eqaul", wantResp, resp)
	}
}

func TestMgr_SupplyCheck(t *testing.T) {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	//mgrMock := mocks.NewMockIMgr(ctrl)
	redisMock := mocks.NewMockICache(ctrl)
	mysqlMock := mocks.NewMockIStore(ctrl)
	bcMock := mocks.NewMockIBusinessConfManager(ctrl)

	mgr := &Mgr{
		bc:         bcMock,
		store:      mysqlMock,
		redisCache: redisMock,
	}

	sups := []*pb.CostSupplyInfo{
		{
			GiftId:   1,
			GiftType: uint32(pb.PackageItemType_BACKPACK_PRESENT),
			GiftNum:  10,
		},
		{
			GiftId:   2,
			GiftType: uint32(pb.PackageItemType_BACKPACK_LOTTERY_FRAGMENT),
			GiftNum:  10,
		},
	}

	wantPass := true
	wantRet := map[uint32]map[uint32]*pb.SupplyConf{
		2: {2: &pb.SupplyConf{
			Id:        2,
			GiftId:    2,
			GiftType:  uint32(pb.PackageItemType_BACKPACK_LOTTERY_FRAGMENT),
			GiftPrice: 100,
			GiftName:  "222",
			GiftPic:   "222",
		}},
		1: {1: &pb.SupplyConf{
			Id:        1,
			GiftId:    1,
			GiftType:  uint32(pb.PackageItemType_BACKPACK_PRESENT),
			GiftPrice: 100,
			GiftName:  "111",
			GiftPic:   "111",
		}}}

	now := time.Now()
	redisMock.EXPECT().GetSupplyConfCache(ctx).Return(false, nil, nil)
	mysqlMock.EXPECT().GetAllSupplys(gomock.Any()).Return([]*mysql.SupplyInfo{
		{
			SupplyId:   1,
			GiftId:     "1",
			GiftType:   uint32(pb.PackageItemType_BACKPACK_PRESENT),
			GiftPrice:  100,
			GiftName:   "111",
			GiftPic:    "111",
			UpdateTime: now.Add(-10 * time.Second),
		},
		{
			SupplyId:   2,
			GiftId:     "2",
			GiftType:   uint32(pb.PackageItemType_BACKPACK_LOTTERY_FRAGMENT),
			GiftPrice:  100,
			GiftName:   "222",
			GiftPic:    "222",
			UpdateTime: now,
		}}, nil)
	redisMock.EXPECT().SetSupplyConfCache(gomock.Any(), gomock.Any()).Return(nil)

	pass, resp, err := mgr.SupplyCheck(ctx, sups)
	if err != nil {
		t.Errorf("Test SupplyCheck fail, err:%v, wantErr:%v", err, false)
	}
	if pass != wantPass && !reflect.DeepEqual(resp, wantRet) {
		t.Errorf("wantPass=%v， pass：%v wantResp=%+v, resp=%+v, doesnt eqaul", wantPass, pass, wantRet, resp)
	}
	time.Sleep(2 * time.Second)
}

func TestMgr_SortSupplyList(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	//mgrMock := mocks.NewMockIMgr(ctrl)
	redisMock := mocks.NewMockICache(ctrl)
	mysqlMock := mocks.NewMockIStore(ctrl)
	bcMock := mocks.NewMockIBusinessConfManager(ctrl)

	mgr := &Mgr{
		bc:         bcMock,
		store:      mysqlMock,
		redisCache: redisMock,
	}
	now := time.Now()
	sups := []*mysql.SupplyInfo{
		{
			SupplyId:   1,
			GiftId:     "1",
			GiftType:   uint32(pb.PackageItemType_BACKPACK_PRESENT),
			GiftPrice:  100,
			GiftName:   "111",
			GiftPic:    "111",
			UpdateTime: now.Add(-10 * time.Second),
		},
		{
			SupplyId:   2,
			GiftId:     "2",
			GiftType:   uint32(pb.PackageItemType_BACKPACK_LOTTERY_FRAGMENT),
			GiftPrice:  100,
			GiftName:   "222",
			GiftPic:    "222",
			UpdateTime: now,
		}}

	wantRet := []*mysql.SupplyInfo{
		{
			SupplyId:   2,
			GiftId:     "2",
			GiftType:   uint32(pb.PackageItemType_BACKPACK_LOTTERY_FRAGMENT),
			GiftPrice:  100,
			GiftName:   "222",
			GiftPic:    "222",
			UpdateTime: now,
		},
		{
			SupplyId:   1,
			GiftId:     "1",
			GiftType:   uint32(pb.PackageItemType_BACKPACK_PRESENT),
			GiftPrice:  100,
			GiftName:   "111",
			GiftPic:    "111",
			UpdateTime: now.Add(-10 * time.Second),
		}}

	resp := mgr.SortSupplyList(sups)
	if !reflect.DeepEqual(resp, wantRet) {
		t.Errorf("wantResp=%+v, resp=%+v, doesnt eqaul", wantRet, resp)
	}
}

func TestMgr_RoundSettle(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	//mgrMock := mocks.NewMockIMgr(ctrl)
	redisMock := mocks.NewMockICache(ctrl)
	mysqlMock := mocks.NewMockIStore(ctrl)
	bcMock := mocks.NewMockIBusinessConfManager(ctrl)
	pushMock := pushV2.NewMockIClient(ctrl)

	mgr := &Mgr{
		bc:         bcMock,
		store:      mysqlMock,
		redisCache: redisMock,
		pushCli:    pushMock,
	}

	roundInfo1 := &mysql.RoundInfo{
		RoundId:     1,
		ActId:       1,
		PackId:      1,
		GiftId:      1,
		GiftName:    "111",
		GiftUrl:     "111",
		GiftPrice:   10000,
		GiftNum:     1,
		TotalInvest: 10000,
		BingoUid:    1,
		Status:      mysql.RoundStatusSuccess,
		IsHide:      false,
		BeginTime:   time.Now().Add(-1 * time.Hour),
		EndTime:     time.Now().Add(-30 * time.Minute),
	}

	// 1 已结束的round数据测试
	err := mgr.RoundSettle(ctx, roundInfo1)
	if err == nil {
		log.Errorf("RoundSettle test fail, err :%v, wantErr:%v", err, true)
	}

	roundInfo2 := &mysql.RoundInfo{
		RoundId:     2,
		ActId:       1,
		PackId:      1,
		GiftId:      1,
		GiftName:    "111",
		GiftUrl:     "111",
		GiftPrice:   10000,
		GiftNum:     1,
		TotalInvest: 20000,
		BingoUid:    0,
		Status:      mysql.RoundStatusPending,
		IsHide:      false,
		BeginTime:   time.Now().Add(-30 * time.Hour),
		EndTime:     time.Now().Add(30 * time.Minute),
	}

	mysqlMock.EXPECT().GetUserInvestValList(ctx, roundInfo2.RoundId, roundInfo2.BeginTime).Return([]*mysql.UserInvestVal{
		{Uid: 1, InvestVal: 10000},
		{Uid: 2, InvestVal: 10000},
	}, nil)
	mysqlMock.EXPECT().Transaction(gomock.Any(), gomock.Any()).Return(nil)

	// 巡航成功结算
	err2 := mgr.RoundSettle(ctx, roundInfo2)
	if err2 != nil {
		log.Errorf("RoundSettle test fail, err :%v, wantErr:%v", err, false)
	}

	roundInfo3 := &mysql.RoundInfo{
		RoundId:     3,
		ActId:       1,
		PackId:      1,
		GiftId:      1,
		GiftName:    "111",
		GiftUrl:     "111",
		GiftPrice:   10000,
		GiftNum:     1,
		TotalInvest: 1000,
		BingoUid:    0,
		Status:      mysql.RoundStatusPending,
		IsHide:      false,
		BeginTime:   time.Now().Add(-60 * time.Hour),
		EndTime:     time.Now(),
	}

	mysqlMock.EXPECT().GetUserInvestValList(ctx, roundInfo3.RoundId, roundInfo3.BeginTime).Return([]*mysql.UserInvestVal{
		{Uid: 1, InvestVal: 1000},
	}, nil)
	mysqlMock.EXPECT().Transaction(gomock.Any(), gomock.Any()).Return(nil)
	bcMock.EXPECT().GetPushCntLimit().Return(uint32(200))
	pushMock.EXPECT().PushToUsers(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
	redisMock.EXPECT().BatchIncrUserDailyPrice(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)

	// 巡航成功结算
	err3 := mgr.RoundSettle(ctx, roundInfo3)
	if err3 != nil {
		log.Errorf("RoundSettle test fail, err :%v, wantErr:%v", err, false)
	}
}

func TestMgr_handleSuccessRoundSettle(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	//mgrMock := mocks.NewMockIMgr(ctrl)
	redisMock := mocks.NewMockICache(ctrl)
	mysqlMock := mocks.NewMockIStore(ctrl)
	bcMock := mocks.NewMockIBusinessConfManager(ctrl)

	mgr := &Mgr{
		bc:         bcMock,
		store:      mysqlMock,
		redisCache: redisMock,
	}

	gomock.InOrder(
		mysqlMock.EXPECT().Transaction(gomock.Any(), gomock.Any()).Return(nil),
	)

	err := mgr.handleSuccessRoundSettle(context.Background(),
		&mysql.RoundInfo{RoundId: 1, ActId: 1, PackId: 1, GiftId: 1, GiftNum: 1, TotalInvest: 1000, GiftPrice: 1000},
		[]*mysql.UserInvestVal{{Uid: 1, InvestVal: 1000}})
	if err != nil {
		t.Errorf("transactionSuccessRoundEnd fail %v", err)
	}
}

func TestMgr_transactionSuccessRoundSettle(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	//mgrMock := mocks.NewMockIMgr(ctrl)
	redisMock := mocks.NewMockICache(ctrl)
	mysqlMock := mocks.NewMockIStore(ctrl)
	bcMock := mocks.NewMockIBusinessConfManager(ctrl)

	mgr := &Mgr{
		bc:         bcMock,
		store:      mysqlMock,
		redisCache: redisMock,
	}

	gomock.InOrder(
		mysqlMock.EXPECT().UpdateRoundStatus(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(true, nil),
		mysqlMock.EXPECT().UpdateRoundEnd(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(true, nil),
		mysqlMock.EXPECT().UpdateUserBingo(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(true, nil),
		mysqlMock.EXPECT().UpdateUserInvestStatusByRound(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(true, nil),
	)

	err := mgr.transactionSuccessRoundSettle(context.Background(), nil, &mysql.RoundInfo{
		RoundId: 1,
		ActId:   1,
		PackId:  1,
		GiftId:  1}, []uint32{1})
	if err != nil {
		t.Errorf("transactionSuccessRoundEnd fail %v", err)
	}
}

func TestMgr_transactionSuccessRoundEnd(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	//mgrMock := mocks.NewMockIMgr(ctrl)
	redisMock := mocks.NewMockICache(ctrl)
	mysqlMock := mocks.NewMockIStore(ctrl)
	bcMock := mocks.NewMockIBusinessConfManager(ctrl)

	mgr := &Mgr{
		bc:         bcMock,
		store:      mysqlMock,
		redisCache: redisMock,
	}

	gomock.InOrder(
		mysqlMock.EXPECT().UpdateRoundStatus(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(true, nil),
		mysqlMock.EXPECT().UpdateUserInvestStatusByRound(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(true, nil),
		mysqlMock.EXPECT().UpdateInvestLogStatusByRound(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(true, nil),
		mysqlMock.EXPECT().UpdateUserInvestStatus(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(true, nil),
		bcMock.EXPECT().GetBackSenderInfo().Return(uint32(1), "test"),
		bcMock.EXPECT().GetPushCntLimit().Return(uint32(10)),
		mysqlMock.EXPECT().InsertAwardRecords(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes(),
	)

	err := mgr.transactionSuccessRoundEnd(context.Background(), nil, &mysql.RoundInfo{
		RoundId: 1,
		ActId:   1,
		PackId:  1,
		GiftId:  1}, []uint32{1})
	if err != nil {
		t.Errorf("transactionSuccessRoundEnd fail %v", err)
	}
}

func TestMgr_handleSuccessRoundEnd(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	//mgrMock := mocks.NewMockIMgr(ctrl)
	redisMock := mocks.NewMockICache(ctrl)
	mysqlMock := mocks.NewMockIStore(ctrl)
	bcMock := mocks.NewMockIBusinessConfManager(ctrl)
	backpackMock := backpack.NewMockIClient(ctrl)
	pushMock := pushV2.NewMockIClient(ctrl)
	unifiedSearchMock := unifiedSearch.NewMockIUnifiedSearchClient(ctrl)
	publicNoticeMock := publicNotice.NewMockIClient(ctrl)

	mgr := &Mgr{
		bc:                  bcMock,
		store:               mysqlMock,
		redisCache:          redisMock,
		backpackCli:         backpackMock,
		pushCli:             pushMock,
		unifiedSearchClient: unifiedSearchMock,
		publicNoticeCli:     publicNoticeMock,
	}

	gomock.InOrder(
		mysqlMock.EXPECT().GetRoundBingoUidList(gomock.Any(), gomock.Any(), gomock.Any()).Return([]uint32{1, 2}, nil),
		mysqlMock.EXPECT().Transaction(gomock.Any(), gomock.Any()).Return(nil),

		backpackMock.EXPECT().GetPackageItemCfg(gomock.Any(), uint32(0), gomock.Any(), gomock.Any()).Return(&backpackPB.GetPackageItemCfgResp{ItemCfgList: []*backpackPB.PackageItemCfg{
			{
				BgItemId:  1,
				BgId:      1,
				ItemType:  uint32(backpackPB.PackageItemType_BACKPACK_PRESENT),
				ItemCount: 1,
				SourceId:  12,
			}}}, nil).AnyTimes(),
		bcMock.EXPECT().GetPushCntLimit().Return(uint32(200)).AnyTimes(),
		pushMock.EXPECT().PushToUsers(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes(),
		unifiedSearchMock.EXPECT().AddRisky(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes(),
		bcMock.EXPECT().GetBreakingNewsPriceLimit().Return(uint32(5000)).AnyTimes(),
		publicNoticeMock.EXPECT().PushBreakingNews(gomock.Any(), gomock.Any()).AnyTimes().AnyTimes(),

		mysqlMock.EXPECT().GetUserInvestValList(gomock.Any(), gomock.Any(), gomock.Any()).Return([]*mysql.UserInvestVal{}, errors.New("test")).AnyTimes(),
	)

	err := mgr.handleSuccessRoundEnd(context.Background(), &mysql.RoundInfo{
		RoundId: 1,
		ActId:   1,
		PackId:  1,
		GiftId:  1,
		Status:  mysql.RoundStatusSettling,
	})
	if err != nil {
		t.Errorf("handleSuccessRoundEnd fail %v", err)
	}
}

func TestMgr_RoundEndReport(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	//mgrMock := mocks.NewMockIMgr(ctrl)
	redisMock := mocks.NewMockICache(ctrl)
	mysqlMock := mocks.NewMockIStore(ctrl)
	bcMock := mocks.NewMockIBusinessConfManager(ctrl)
	accountMock := account.NewMockIClient(ctrl)

	mgr := &Mgr{
		bc:         bcMock,
		store:      mysqlMock,
		redisCache: redisMock,
		accountCli: accountMock,
	}
	mgr.consolationAward = &AwardInfo{
		PackId:      595,
		GiftId:      2,
		GiftType:    uint32(pb.PackageItemType_BACKPACK_LOTTERY_FRAGMENT),
		GiftIconUrl: "111111",
		GiftName:    "装扮碎片",
		GiftWorth:   0,
	}

	bcMock.EXPECT().GetLotteryDataReportEnable().Return(true)
	beginTime := time.Now().Add(-10 * time.Minute)
	redisMock.EXPECT().PopRoundEndQueue(gomock.Any()).Return(uint32(1), nil)
	mysqlMock.EXPECT().GetRoundInfo(gomock.Any(), uint32(1)).Return(&mysql.RoundInfo{
		RoundId:     1,
		ActId:       1,
		PackId:      1,
		GiftId:      1,
		GiftName:    "111",
		GiftUrl:     "111",
		GiftPrice:   100000,
		GiftNum:     1,
		TotalInvest: 100000,
		BingoUid:    1,
		Status:      mysql.RoundStatusSuccess,
		IsHide:      false,
		BeginTime:   beginTime,
		EndTime:     time.Now().Add(-2 * time.Second),
	}, true, nil)
	mysqlMock.EXPECT().GetRoundInvestSummary(gomock.Any(), uint32(1), uint32(1), uint32(ConsolationBase), beginTime).Return(&mysql.RoundInvestSummary{
		RoundId:        1,
		UserCnt:        100,
		TotalInvest:    120000,
		ConsolationCnt: 1000,
	}, nil)
	bcMock.EXPECT().GetFeiShuRobotUrl().Return("***********")

	// 轮次数据播报，正常
	err := mgr.RoundEndReport()
	if err == nil {
		log.Errorf("RoundSettle test fail, err :%v, wantErr:%v", err, true)
	}
}

func TestMgr_GetAwardTotalCount(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	//mgrMock := mocks.NewMockIMgr(ctrl)
	redisMock := mocks.NewMockICache(ctrl)
	mysqlMock := mocks.NewMockIStore(ctrl)
	bcMock := mocks.NewMockIBusinessConfManager(ctrl)
	accountMock := account.NewMockIClient(ctrl)

	mgr := &Mgr{
		bc:         bcMock,
		store:      mysqlMock,
		redisCache: redisMock,
		accountCli: accountMock,
	}
	now := time.Now()
	beginTime := now.Add(-3 * time.Minute).Unix()
	endTime := now.Unix()
	mysqlMock.EXPECT().GetAwardTotalCountInfo(gomock.Any(), time.Unix(beginTime, 0), time.Unix(endTime, 0)).Return(&mysql.StCount{
		Count: 100,
		Value: 100,
	}, nil)

	// 轮次数据播报，正常
	_, err := mgr.GetAwardTotalCount(ctx, &reconcile_v2.TimeRangeReq{
		BeginTime: beginTime,
		EndTime:   endTime,
	})
	if err != nil {
		log.Errorf("RoundSettle test fail, err :%v, wantErr:%v", err, false)
	}
}

func TestMgr_GetAwardOrderIds(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	//mgrMock := mocks.NewMockIMgr(ctrl)
	redisMock := mocks.NewMockICache(ctrl)
	mysqlMock := mocks.NewMockIStore(ctrl)
	bcMock := mocks.NewMockIBusinessConfManager(ctrl)

	mgr := &Mgr{
		bc:         bcMock,
		store:      mysqlMock,
		redisCache: redisMock,
	}
	now := time.Now()
	beginTime := now.Add(-3 * time.Minute).Unix()
	endTime := now.Unix()
	mysqlMock.EXPECT().GetAwardOrderIds(gomock.Any(), time.Unix(beginTime, 0), time.Unix(endTime, 0)).Return([]string{"order1", "order2"}, nil)

	// 轮次数据播报，正常
	_, err := mgr.GetAwardOrderIds(ctx, &reconcile_v2.TimeRangeReq{
		BeginTime: beginTime,
		EndTime:   endTime,
	})
	if err != nil {
		log.Errorf("RoundSettle test fail, err :%v, wantErr:%v", err, false)
	}
}

func TestMgr_GetCostTotalCount(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	//mgrMock := mocks.NewMockIMgr(ctrl)
	redisMock := mocks.NewMockICache(ctrl)
	mysqlMock := mocks.NewMockIStore(ctrl)
	bcMock := mocks.NewMockIBusinessConfManager(ctrl)

	mgr := &Mgr{
		bc:         bcMock,
		store:      mysqlMock,
		redisCache: redisMock,
	}
	now := time.Now()
	beginTime := now.Add(-3 * time.Minute).Unix()
	endTime := now.Unix()
	mysqlMock.EXPECT().GetCostTotalCountInfo(gomock.Any(), time.Unix(beginTime, 0), time.Unix(endTime, 0)).Return(&mysql.StCount{
		Count: 100,
		Value: 100,
	}, nil)

	// 轮次数据播报，正常
	_, err := mgr.GetCostTotalCount(ctx, &reconcile_v2.TimeRangeReq{
		BeginTime: beginTime,
		EndTime:   endTime,
	})
	if err != nil {
		log.Errorf("RoundSettle test fail, err :%v, wantErr:%v", err, false)
	}
}

func TestMgr_GetCostOrderIds(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	//mgrMock := mocks.NewMockIMgr(ctrl)
	redisMock := mocks.NewMockICache(ctrl)
	mysqlMock := mocks.NewMockIStore(ctrl)
	bcMock := mocks.NewMockIBusinessConfManager(ctrl)

	mgr := &Mgr{
		bc:         bcMock,
		store:      mysqlMock,
		redisCache: redisMock,
	}
	now := time.Now()
	beginTime := now.Add(-3 * time.Minute).Unix()
	endTime := now.Unix()
	mysqlMock.EXPECT().GetCostOrderIds(gomock.Any(), time.Unix(beginTime, 0), time.Unix(endTime, 0)).Return([]string{"order1", "order2"}, nil)

	// 轮次数据播报，正常
	_, err := mgr.GetCostOrderIds(ctx, &reconcile_v2.TimeRangeReq{
		BeginTime: beginTime,
		EndTime:   endTime,
	})
	if err != nil {
		log.Errorf("RoundSettle test fail, err :%v, wantErr:%v", err, false)
	}
}

func TestMgr_GetLastRoundReview(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	//mgrMock := mocks.NewMockIMgr(ctrl)
	redisMock := mocks.NewMockICache(ctrl)
	mysqlMock := mocks.NewMockIStore(ctrl)
	bcMock := mocks.NewMockIBusinessConfManager(ctrl)

	mgr := &Mgr{
		bc:         bcMock,
		store:      mysqlMock,
		redisCache: redisMock,
	}
	now := time.Now()
	beginTime := now.Add(-10 * time.Minute)

	mysqlMock.EXPECT().GetPreviousRoundInfo(gomock.Any(), uint32(2)).Return([]*mysql.RoundInfo{
		{
			RoundId:     2,
			ActId:       1,
			PackId:      1,
			GiftId:      1,
			GiftName:    "111",
			GiftUrl:     "111",
			GiftPrice:   1000000,
			GiftNum:     1,
			TotalInvest: 10000,
			BeginTime:   beginTime,
			EndTime:     beginTime.Add(60 * time.Minute),
		},
		{
			RoundId:     1,
			ActId:       1,
			PackId:      1,
			GiftId:      1,
			GiftName:    "111",
			GiftUrl:     "111",
			GiftPrice:   1000000,
			GiftNum:     1,
			TotalInvest: 10000,
			Status:      mysql.RoundStatusSuccess,
			BeginTime:   beginTime.Add(-10 * time.Minute),
			EndTime:     beginTime,
		}}, nil)

	// 轮次数据播报，正常
	_, err := mgr.GetLastRoundReview(ctx, uint32(1), uint32(1), true)
	if err != nil {
		log.Errorf("RoundSettle test fail, err :%v, wantErr:%v", err, false)
	}

	mysqlMock.EXPECT().GetPreviousRoundInfo(gomock.Any(), uint32(2)).Return([]*mysql.RoundInfo{
		{
			RoundId:     2,
			ActId:       1,
			PackId:      1,
			GiftId:      1,
			GiftName:    "111",
			GiftUrl:     "111",
			GiftPrice:   1000000,
			GiftNum:     1,
			TotalInvest: 10000,
			BeginTime:   beginTime,
			EndTime:     beginTime.Add(60 * time.Minute),
		},
		{
			RoundId:     1,
			ActId:       1,
			PackId:      1,
			GiftId:      1,
			GiftName:    "111",
			GiftUrl:     "111",
			GiftPrice:   1000000,
			GiftNum:     1,
			TotalInvest: 10000,
			Status:      mysql.RoundStatusSuccess,
			BeginTime:   beginTime.Add(-10 * time.Minute),
			EndTime:     beginTime,
		}}, nil)

	// 轮次数据播报，正常
	_, err2 := mgr.GetLastRoundReview(ctx, uint32(1), uint32(1), false)
	if err2 != nil {
		log.Errorf("RoundSettle test fail, err :%v, wantErr:%v", err, false)
	}
}

func TestMgr_RollbackLostOrder(t *testing.T) {

	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	redisMock := mocks.NewMockICache(ctrl)
	mysqlMock := mocks.NewMockIStore(ctrl)
	bcMock := mocks.NewMockIBusinessConfManager(ctrl)
	backpackMock := backpack.NewMockIClient(ctrl)

	mgr := &Mgr{
		bc:          bcMock,
		store:       mysqlMock,
		redisCache:  redisMock,
		backpackCli: backpackMock,
	}

	order := &mysql.InvestCostLog{
		Id:        1,
		OrderId:   "order_id",
		RoundId:   1,
		Uid:       1,
		Status:    mysql.InvestCostPrepare,
		GiftId:    1,
		GiftName:  "111",
		GiftPrice: 1000,
		GiftNum:   1,
		GiftType:  4,
	}

	gomock.InOrder(
		// 查订单
		mysqlMock.EXPECT().GetInvestLogByOrder(ctx, gomock.Any(), order.OrderId).Return(order, true, nil),
		mysqlMock.EXPECT().Transaction(ctx, gomock.Any()).Return(nil),

		// 回滚物品
		backpackMock.EXPECT().RollBackUserItem(ctx, order.Uid, gomock.Any(), order.OrderId),
	)

	t.Run("RollbackLostOrder", func(t *testing.T) {
		err := mgr.RollbackLostOrder(ctx, &reconcile_v2.ReplaceOrderReq{
			OrderId: order.OrderId,
			Params:  "",
		})
		if err != nil {
			t.Errorf("RollbackLostOrder wantERR:false, but got no err(%v)", err)
		}
	})

	order2 := &mysql.InvestCostLog{}

	// test2
	gomock.InOrder(
		// 查订单
		mysqlMock.EXPECT().GetInvestLogByOrder(ctx, gomock.Any(), "order_id2").Return(order2, false, nil),
		mysqlMock.EXPECT().GetInvestLogByOrder(ctx, gomock.Any(), "order_id2").Return(order2, false, nil),
	)

	t.Run("RollbackLostOrder", func(t *testing.T) {
		err := mgr.RollbackLostOrder(ctx, &reconcile_v2.ReplaceOrderReq{
			OrderId: "order_id2",
			Params:  "",
		})
		if err == nil {
			t.Errorf("RollbackLostOrder wantERR:true, but got no err(%v)", err)
		}
	})
}

func Test_sendIMMsg(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	mockSendIM := sendim.NewMockIClient(ctrl)

	mgr := &Mgr{
		sendImCli: mockSendIM,
	}

	msg := "dsgsdg"
	toUidList := []uint32{1, 2, 3}

	gomock.InOrder(
		mockSendIM.EXPECT().SendSync(gomock.Any(), gomock.Any()),
	)

	t.Run("sendIMMsg", func(t *testing.T) {
		err := mgr.sendIMMsg(ctx, toUidList, msg)
		if err != nil {
			t.Errorf("RollbackLostOrder wantERR:false, but got no err(%v)", err)
		}
	})
}

func Test_initConsolationPackInfo(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockBackpackCli := backpack.NewMockIClient(ctrl)
	mockBusinessConf := mocks.NewMockIBusinessConfManager(ctrl)
	mockUserPresent := userPresent.NewMockIClient(ctrl)

	mgr := &Mgr{
		userPresentCli: mockUserPresent,
		backpackCli:    mockBackpackCli,
		bc:             mockBusinessConf,
	}

	gomock.InOrder(
		mockBusinessConf.EXPECT().GetConsolationAwardPackId().Return(uint32(125)),
		mockBackpackCli.EXPECT().GetPackageItemCfg(gomock.Any(), uint32(0), gomock.Any(), gomock.Any()).Return(&backpackPB.GetPackageItemCfgResp{ItemCfgList: []*backpackPB.PackageItemCfg{
			{
				BgItemId:  1,
				BgId:      1,
				ItemType:  uint32(backpackPB.PackageItemType_BACKPACK_PRESENT),
				ItemCount: 1,
				SourceId:  1,
			}}}, nil),
		mockUserPresent.EXPECT().GetPresentConfigById(gomock.Any(), uint32(1)),
	)

	t.Run("initConsolationPackInfo", func(t *testing.T) {
		err := mgr.initConsolationPackInfo()
		if err != nil {
			t.Errorf("initConsolationPackInfo wantERR:false, but got no err(%v)", err)
		}
	})

	gomock.InOrder(
		mockBusinessConf.EXPECT().GetConsolationAwardPackId().Return(uint32(125)),
		mockBackpackCli.EXPECT().GetPackageItemCfg(gomock.Any(), uint32(0), gomock.Any(), gomock.Any()).Return(&backpackPB.GetPackageItemCfgResp{ItemCfgList: []*backpackPB.PackageItemCfg{
			{
				BgItemId:  1,
				BgId:      1,
				ItemType:  uint32(backpackPB.PackageItemType_BACKPACK_LOTTERY_FRAGMENT),
				ItemCount: 1,
				SourceId:  1,
			}}}, nil),
		mockBackpackCli.EXPECT().GetItemCfg(gomock.Any(), uint32(0), gomock.Any()),
	)

	t.Run("initConsolationPackInfo", func(t *testing.T) {
		err := mgr.initConsolationPackInfo()
		if err == nil {
			t.Errorf("initConsolationPackInfo wantERR:true, but got no err(%v)", err)
		}
	})
}

func Test_handleCreateRound(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	ctx := context.Background()

	mockRedis := mocks.NewMockICache(ctrl)
	mockStore := mocks.NewMockIStore(ctrl)
	mockBusiness := mocks.NewMockIBusinessConfManager(ctrl)

	mgr := &Mgr{
		bc:         mockBusiness,
		redisCache: mockRedis,
		store:      mockStore,
	}

	actId := uint32(1)
	beginTime := time.Now().Add(-2 * time.Minute)
	endTime := beginTime.Add(1 * time.Hour)
	dayEnd := time.Date(beginTime.Year(), beginTime.Month(), beginTime.Day(), 23, 59, 59, 0, time.Local)
	curR := &mysql.RoundInfo{
		RoundId:     1,
		ActId:       1,
		PackId:      1,
		GiftId:      1,
		GiftName:    "111",
		GiftUrl:     "111",
		GiftPrice:   1000,
		GiftNum:     1,
		TotalInvest: 1000,
		BingoUid:    0,
		Status:      mysql.RoundStatusPending,
		IsHide:      false,
		BeginTime:   beginTime,
		EndTime:     endTime,
	}

	mgr.prizePool = &PrizePool{}
	mgr.prizePool.Version = uint32(0)

	gomock.InOrder(
		mockRedis.EXPECT().GetNextRoundPrizeCache(ctx).Return(false, &pb.StarTAwardInfo{}, nil),
		mockStore.EXPECT().GetPrizesLastMtimeByActId(gomock.Any(), actId).Return(uint32(10), nil),
		mockStore.EXPECT().GetPrizesByActivityId(gomock.Any(), actId).Return([]*mysql.Prize{
			{
				Id:         1,
				ActivityId: 1,
				BgId:       1,
				GiftId:     1,
				GiftPrice:  1,
				GiftNum:    1,
				GiftName:   "111",
				GiftPic:    "111",
				Weight:     1,
				IsDel:      false,
			}}, nil),

		mockBusiness.EXPECT().GetEveryRoundLongSc().Return(uint32(1800)).AnyTimes(),

		//插入下期活动
		mockStore.EXPECT().InsertNewRoundInfo(gomock.Any(), gomock.Any()),

		mockBusiness.EXPECT().GetEveryRoundLongSc().Return(uint32(1800)).AnyTimes(),
		//生成下期预告
		mockStore.EXPECT().GetPrizesLastMtimeByActId(gomock.Any(), actId).Return(uint32(20), nil),
		mockStore.EXPECT().GetPrizesByActivityId(gomock.Any(), actId).Return([]*mysql.Prize{
			{
				Id:         1,
				ActivityId: 1,
				BgId:       1,
				GiftId:     1,
				GiftPrice:  1,
				GiftNum:    1,
				GiftName:   "111",
				GiftPic:    "111",
				Weight:     1,
				IsDel:      false,
			}}, nil),
		mockRedis.EXPECT().SetNextRoundPrizeCache(gomock.Any(), gomock.Any()).Return(nil),
	)

	t.Run("handleCreateRound", func(t *testing.T) {
		err := mgr.handleCreateRound(ctx, actId, curR, dayEnd)
		if err != nil {
			t.Errorf("handleCreateRound wantERR:false, but got no err(%v)", err)
		}
	})
}

func Test_fillActivityPb(t *testing.T) {

	ActivityBegin := time.Now()
	ActivityEnd := time.Now().Add(24 * time.Hour)

	activityTime := &mysql.ActivityTime{
		Id:            1,
		ActivityName:  "111",
		ActivityBegin: ActivityBegin,
		ActivityEnd:   ActivityEnd,
		TimeRange:     "[{\"Selected\":true, \"DayBegin\":64800, \"DayEnd\":86400, \"WDayList\":[0,5,6]}]",
		Status:        1,
		IsDel:         false,
	}

	prize := []*mysql.Prize{
		{
			Id:         1,
			ActivityId: 1,
			BgId:       1,
			GiftId:     1,
			GiftPrice:  1,
			GiftNum:    1,
			GiftName:   "111",
			GiftPic:    "111",
			Weight:     1,
			IsDel:      false,
		},
	}

	wantResp := &pb.StarTrekConf{
		ConfId:        1,
		ActivityName:  "111",
		ActivityBegin: uint32(ActivityBegin.Unix()),
		ActivityEnd:   uint32(ActivityEnd.Unix()),
		PrizeList: []*pb.StarTrekPrizeInfo{
			{
				BgId:      1,
				Weight:    1,
				GiftId:    1,
				GiftPrice: 1,
				GiftName:  "111",
				GiftPic:   "111",
				GiftNum:   1,
			},
		},
		TimeList: []*pb.DayTimeRangeInfo{
			{
				Selected: true,
				DayBegin: 64800,
				DayEnd:   86400,
				WDayList: []uint32{0, 5, 6},
			},
		},
	}

	t.Run("fillActivityPb", func(t *testing.T) {
		resp := mgr.fillActivityPb(activityTime, prize)
		if !reflect.DeepEqual(wantResp, resp) {
			t.Errorf("fillActivityPb \nwant:%+v, \nresp:%+v", wantResp, resp)
		}
	})
}

func TestMgr_SetUserDailyInvestLimit(t *testing.T) {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mysqlMock := mocks.NewMockIStore(ctrl)
	mgr := &Mgr{
		store: mysqlMock,
	}

	cases := []struct {
		Name       string
		dailyLimit uint32
		wantErr    bool
	}{
		{
			Name:       "req common",
			dailyLimit: 100000,
			wantErr:    false,
		},
	}

	for _, testCase := range cases {
		gomock.InOrder(
			mysqlMock.EXPECT().GetLatestUserDailyLimit(ctx).Return(uint32(0), nil).AnyTimes(),
			mysqlMock.EXPECT().SetUserDailyLimit(ctx, testCase.dailyLimit).Return(nil).AnyTimes(),
		)

		t.Run(testCase.Name, func(t *testing.T) {
			err := mgr.SetUserDailyInvestLimit(ctx, testCase.dailyLimit)
			if (err != nil) != testCase.wantErr {
				t.Errorf("SetUserDailyInvestLimit error = %v, wantErr = %v", err, testCase.wantErr)
				return
			}
		})
	}
}

func TestMgr_GetUserDailyInvestLimit(t *testing.T) {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mysqlMock := mocks.NewMockIStore(ctrl)
	mgr := &Mgr{
		store: mysqlMock,
	}

	cases := []struct {
		Name    string
		wantErr bool
		wantRet uint32
	}{
		{
			Name:    "req common",
			wantRet: 100000,
			wantErr: false,
		},
	}

	for _, testCase := range cases {
		gomock.InOrder(
			mysqlMock.EXPECT().GetLatestUserDailyLimit(ctx).Return(testCase.wantRet, nil).AnyTimes(),
		)

		t.Run(testCase.Name, func(t *testing.T) {
			limit, err := mgr.GetUserDailyInvestLimit(ctx)
			if (err != nil) != testCase.wantErr {
				t.Errorf("GetUserDailyInvestLimit error = %v, wantErr = %v", err, testCase.wantErr)
				return
			}
			if limit != testCase.wantRet {
				t.Errorf("GetUserDailyInvestLimit limit = %v, wantRet = %v", limit, testCase.wantRet)
				return
			}
		})
	}
}

func Test_checkActivityChangeHandle(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	redisMock := mocks.NewMockICache(ctrl)
	mysqlMock := mocks.NewMockIStore(ctrl)
	bcMock := mocks.NewMockIBusinessConfManager(ctrl)

	mgr := &Mgr{
		bc:         bcMock,
		store:      mysqlMock,
		redisCache: redisMock,
	}

	mysqlMock.EXPECT().GetRecentActByStatus(gomock.Any(), uint32(pb.StarTrekStatus_Using), uint32(pb.StarTrekStatus_Unused)).Return(
		&mysql.ActivityTime{
			Id:            1,
			ActivityName:  "111",
			ActivityBegin: time.Now().Add(-3 * time.Hour),
			ActivityEnd:   time.Now().Add(-10 * time.Minute),
			TimeRange:     "[{\"Selected\":true, \"DayBegin\":0, \"DayEnd\":86399, \"WDayList\":[0,1,2,3,4,5,6]}]",
			Status:        uint32(pb.StarTrekStatus_Using),
		}, true, nil)
	mysqlMock.EXPECT().GetRoundInfoByActId(gomock.Any(), uint32(1), uint32(mysql.RoundStatusPending), uint32(mysql.RoundStatusSettling)).Return(
		&mysql.RoundInfo{}, false, nil)
	mysqlMock.EXPECT().UpdateActivityStatusById(gomock.Any(), uint32(1), uint32(pb.StarTrekStatus_Finished)).Return(nil)
	redisMock.EXPECT().DelActivityConfCache(gomock.Any())

	err := checkActivityChangeHandle(context.Background(), mgr)
	if err != nil {
		t.Errorf("Test checkActivityChangeHandle fail, err:%v, wantErr:%v", err, false)
	}
}

func TestMgr_CheckActivityChange2(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	redisMock := mocks.NewMockICache(ctrl)
	mysqlMock := mocks.NewMockIStore(ctrl)
	bcMock := mocks.NewMockIBusinessConfManager(ctrl)

	mgr := &Mgr{
		bc:         bcMock,
		store:      mysqlMock,
		redisCache: redisMock,
	}

	mysqlMock.EXPECT().GetRecentActByStatus(gomock.Any(), uint32(pb.StarTrekStatus_Using), uint32(pb.StarTrekStatus_Unused)).Return(
		&mysql.ActivityTime{
			Id:            1,
			ActivityName:  "111",
			ActivityBegin: time.Now().Add(-3 * time.Hour),
			ActivityEnd:   time.Now().Add(100 * time.Minute),
			TimeRange:     "[{\"Selected\":true, \"DayBegin\":0, \"DayEnd\":86399, \"WDayList\":[0,1,2,3,4,5,6]}]",
			Status:        uint32(pb.StarTrekStatus_Unused),
		}, true, nil)
	mysqlMock.EXPECT().UpdateActivityStatusById(gomock.Any(), uint32(1), uint32(pb.StarTrekStatus_Using)).Return(nil)
	redisMock.EXPECT().DelActivityConfCache(gomock.Any())

	err := checkActivityChangeHandle(context.Background(), mgr)
	if err != nil {
		t.Errorf("Test checkActivityChangeHandle fail, err:%v, wantErr:%v", err, false)
	}
}

func Test_calcConsolationAwardNum(t *testing.T) {

	_ = calcConsolationAwardNum(uint32(1000))
}

func Test_checkAndAward(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	redisMock := mocks.NewMockICache(ctrl)
	mysqlMock := mocks.NewMockIStore(ctrl)
	bcMock := mocks.NewMockIBusinessConfManager(ctrl)
	backpackSenderMock := backpacksender.NewMockIClient(ctrl)
	backpackMock := backpack.NewMockIClient(ctrl)
	sendImMock := sendim.NewMockIClient(ctrl)
	unifiedSearchMock := unifiedSearch.NewMockIUnifiedSearchClient(ctrl)

	mgr := &Mgr{
		bc:                  bcMock,
		store:               mysqlMock,
		redisCache:          redisMock,
		backpackCli:         backpackMock,
		backpackSenderCli:   backpackSenderMock,
		sendImCli:           sendImMock,
		unifiedSearchClient: unifiedSearchMock,
	}

	cases := []struct {
		Name      string
		bingoType uint32
		wantErr   bool
	}{
		{
			Name:      "req BingoTypeNone",
			bingoType: mysql.BingoTypeNone,
			wantErr:   false,
		},
		{
			Name:      "req BingoTypeComm",
			bingoType: mysql.BingoTypeComm,
			wantErr:   false,
		},
	}

	for _, testCase := range cases {
		gomock.InOrder(
			bcMock.EXPECT().GetAwardRetryHour().Return(uint32(1)),
			mysqlMock.EXPECT().GetAwardRecordList(gomock.Any(), uint32(0), uint32(100), testCase.bingoType, gomock.Any(), gomock.Any()).
				Return([]*mysql.AwardRecord{{
					Id:         1,
					OrderId:    "123_star_trek_822_2266893_1663039238",
					RoundId:    1,
					Uid:        1111,
					Status:     0,
					BingoType:  testCase.bingoType,
					GiftId:     1,
					PackId:     1,
					PackDesc:   "111",
					PackWorth:  100000,
					PackAmount: 1,
				}}, nil),

			redisMock.EXPECT().LockAward(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(true, nil),
			// 发包裹
			bcMock.EXPECT().GetBackSenderInfo().Return(uint32(123), "wyousnbzjqiygkyi"),
			backpackSenderMock.EXPECT().SendBackpackWithRiskControl(gomock.Any(), gomock.Any()).Return(&backpacksenderPb.SendBackpackWithRiskControlResp{}, nil),

			// 更新状态
			mysqlMock.EXPECT().UpdateAwardRecordStatus(gomock.Any(), gomock.Any(), "123_star_trek_822_2266893_1663039238", uint32(1)).Return(true, nil),

			// 过期时间
			backpackMock.EXPECT().GetPackageItemCfg(gomock.Any(), uint32(0), gomock.Any()).Return(&backpackPB.GetPackageItemCfgResp{ItemCfgList: []*backpackPB.PackageItemCfg{
				{
					BgItemId: 1,
					BgId:     1,
					SourceId: 1,
					Months:   3,
				}}}, nil).AnyTimes(),

			unifiedSearchMock.EXPECT().AddRisky(gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes().Return(nil),
			// 发im
			sendImMock.EXPECT().SendSync(gomock.Any(), gomock.Any()),
		)

		t.Run(testCase.Name, func(t *testing.T) {
			err := mgr.checkAndAward(testCase.bingoType)
			if (err != nil) != testCase.wantErr {
				t.Errorf("checkAndAward error = %v, wantErr = %v", err, testCase.wantErr)
				return
			}
		})
	}
}

func TestMgr_CheckUserDailyLimitUpdate(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mysqlMock := mocks.NewMockIStore(ctrl)

	mgr := &Mgr{
		store: mysqlMock,
	}
	mgr.userDailyLimit = 0

	cases := []struct {
		Name    string
		wantErr error
	}{
		{
			Name:    "common",
			wantErr: nil,
		}, {
			Name:    "error",
			wantErr: errors.New("some err"),
		},
	}

	for _, testCase := range cases {
		gomock.InOrder(
			mysqlMock.EXPECT().GetLatestUserDailyLimit(gomock.Any()).Return(uint32(10000), testCase.wantErr),
		)

		t.Run(testCase.Name, func(t *testing.T) {
			err := mgr.CheckUserDailyLimitUpdate()
			if err != testCase.wantErr {
				t.Errorf("CheckUserDailyLimitUpdate error = %v, wantErr = %v", err, testCase.wantErr)
				return
			}
		})
	}
}

func Test_consolationNotify(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	mysqlMock := mocks.NewMockIStore(ctrl)
	bcMock := mocks.NewMockIBusinessConfManager(ctrl)
	backpackMock := backpack.NewMockIClient(ctrl)
	pushMock := pushV2.NewMockIClient(ctrl)

	mgr := &Mgr{
		store:       mysqlMock,
		bc:          bcMock,
		backpackCli: backpackMock,
		pushCli:     pushMock,
	}
	mgr.consolationAward = &AwardInfo{
		PackId:      1,
		GiftId:      12,
		GiftType:    uint32(backpackPB.PackageItemType_BACKPACK_LOTTERY_FRAGMENT),
		GiftIconUrl: "safasfa",
		GiftName:    "asdgasdgas",
		GiftWorth:   10000,
	}

	cases := []struct {
		Name    string
		wantErr error
		info    *mysql.RoundInfo
	}{
		{
			Name: "common",
			info: &mysql.RoundInfo{
				RoundId:     1,
				ActId:       1,
				PackId:      1,
				GiftId:      1,
				GiftName:    "111",
				GiftUrl:     "111",
				GiftPrice:   10000,
				GiftNum:     1,
				TotalInvest: 12000,
				BingoUid:    1111111,
				Status:      mysql.RoundStatusSuccess,
				IsHide:      false,
				BeginTime:   time.Now().Add(-10 * time.Second),
			},
		},
	}

	for _, testCase := range cases {
		gomock.InOrder(
			mysqlMock.EXPECT().GetUserInvestValList(ctx, testCase.info.RoundId, testCase.info.BeginTime).Return([]*mysql.UserInvestVal{
				{
					Uid:       2,
					InvestVal: 23,
				},
				{
					Uid:       1111111,
					InvestVal: 10000,
				},
			}, testCase.wantErr),
			bcMock.EXPECT().GetTrekCommAwardNotifyText().Return("安慰奖文案"),
			backpackMock.EXPECT().GetPackageItemCfg(gomock.Any(), uint32(0), gomock.Any(), gomock.Any()).Return(&backpackPB.GetPackageItemCfgResp{ItemCfgList: []*backpackPB.PackageItemCfg{
				{
					BgItemId:  1,
					BgId:      1,
					ItemType:  uint32(backpackPB.PackageItemType_BACKPACK_LOTTERY_FRAGMENT),
					ItemCount: 1,
					SourceId:  12,
				}}}, nil),
			bcMock.EXPECT().GetPushCntLimit().Return(uint32(200)),
			pushMock.EXPECT().PushToUsers(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil),
		)

		t.Run(testCase.Name, func(t *testing.T) {
			mgr.consolationNotify(ctx, testCase.info, []uint32{1})
		})
	}
}

func Test_bingoNotify(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	mysqlMock := mocks.NewMockIStore(ctrl)
	bcMock := mocks.NewMockIBusinessConfManager(ctrl)
	backpackMock := backpack.NewMockIClient(ctrl)
	pushMock := pushV2.NewMockIClient(ctrl)
	unifiedSearchMock := unifiedSearch.NewMockIUnifiedSearchClient(ctrl)
	publicNoticeMock := publicNotice.NewMockIClient(ctrl)

	mgr := &Mgr{
		store:               mysqlMock,
		bc:                  bcMock,
		backpackCli:         backpackMock,
		pushCli:             pushMock,
		unifiedSearchClient: unifiedSearchMock,
		publicNoticeCli:     publicNoticeMock,
	}

	mgr.gameSwitch = true

	cases := []struct {
		Name    string
		wantErr error
		info    *mysql.RoundInfo
	}{
		{
			Name: "common",
			info: &mysql.RoundInfo{
				RoundId:     1,
				ActId:       1,
				PackId:      1,
				GiftId:      1,
				GiftName:    "111",
				GiftUrl:     "111",
				GiftPrice:   10000,
				GiftNum:     1,
				TotalInvest: 12000,
				BingoUid:    1111111,
				Status:      mysql.RoundStatusSuccess,
				IsHide:      false,
				BeginTime:   time.Now().Add(-10 * time.Second),
			},
		},
	}

	for _, testCase := range cases {
		gomock.InOrder(
			backpackMock.EXPECT().GetPackageItemCfg(gomock.Any(), uint32(0), gomock.Any(), gomock.Any()).Return(&backpackPB.GetPackageItemCfgResp{ItemCfgList: []*backpackPB.PackageItemCfg{
				{
					BgItemId:  1,
					BgId:      1,
					ItemType:  uint32(backpackPB.PackageItemType_BACKPACK_PRESENT),
					ItemCount: 1,
					SourceId:  12,
				}}}, nil),
			bcMock.EXPECT().GetPushCntLimit().Return(uint32(200)),
			pushMock.EXPECT().PushToUsers(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil),
			//unifiedSearchMock.EXPECT().AddRisky(ctx, gomock.Any(), gomock.Any()).Return(nil),
			bcMock.EXPECT().GetBreakingNewsPriceLimit().Return(uint32(5000)),
			bcMock.EXPECT().GetPublicNewOnlySelfVisible().Return(false),
			publicNoticeMock.EXPECT().PushBreakingNews(ctx, gomock.Any()).AnyTimes(),
		)

		t.Run(testCase.Name, func(t *testing.T) {
			mgr.bingoNotify(ctx, testCase.info, []uint32{1})
		})
	}
}

func Test_doRoundDateReport(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	mysqlMock := mocks.NewMockIStore(ctrl)
	bcMock := mocks.NewMockIBusinessConfManager(ctrl)

	mgr := &Mgr{
		store: mysqlMock,
		bc:    bcMock,
	}
	tNow := time.Now()
	cases := []struct {
		Name    string
		wantErr bool
		now     time.Time
	}{
		{
			Name:    "common",
			now:     time.Date(tNow.Year(), tNow.Month(), tNow.Day(), tNow.Hour(), 0, 0, 0, time.Local),
			wantErr: true,
		},
	}

	for _, testCase := range cases {
		gomock.InOrder(
			bcMock.EXPECT().GetTesting().Return(true).AnyTimes(),
			mysqlMock.EXPECT().GetRoundRecordSummary(ctx, gomock.Any(), gomock.Any()).Return(&mysql.RoundRecordSummary{
				BeginRoundCnt:      10,
				EndRoundCnt:        100,
				SuccessRoundCnt:    90,
				AwardTotalPrice:    5051212,
				CostTotalPrice:     6054541,
				RollbackTotalPrice: 4564,
				BingoUserCnt:       565,
			}, nil),
			mysqlMock.EXPECT().GetJoinUserCnt(ctx, gomock.Any(), gomock.Any()).Return(uint32(2510), nil),
			mysqlMock.EXPECT().GetRoundRecordSummary(ctx, gomock.Any(), gomock.Any()).Return(&mysql.RoundRecordSummary{
				BeginRoundCnt:      10,
				EndRoundCnt:        4012,
				SuccessRoundCnt:    4002,
				AwardTotalPrice:    454545545,
				CostTotalPrice:     488878744,
				RollbackTotalPrice: 4454545,
				BingoUserCnt:       1202,
			}, nil),
			mysqlMock.EXPECT().GetJoinUserCnt(ctx, gomock.Any(), gomock.Any()).Return(uint32(76102), nil),
			bcMock.EXPECT().GetFeiShuRobotUrl().Return("***********"),
		)

		t.Run(testCase.Name, func(t *testing.T) {
			err := doRoundDateReport(ctx, mgr, testCase.now)
			if (err != nil) != testCase.wantErr {
				t.Errorf("doRoundDateReport error = %v, wantErr = %v", err, testCase.wantErr)
				return
			}
		})
	}
}

func Test_handleWithPanicCatch(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mysqlMock := mocks.NewMockIStore(ctrl)
	bcMock := mocks.NewMockIBusinessConfManager(ctrl)

	mgr := &Mgr{
		store: mysqlMock,
		bc:    bcMock,
	}

	tNow := time.Now()
	cases := []struct {
		Name string
		now  time.Time
	}{
		{
			Name: "Test_handleWithPanicCatch",
			now:  time.Date(tNow.Year(), tNow.Month(), tNow.Day(), tNow.Hour(), 0, 0, 0, time.Local),
		},
	}

	for _, testCase := range cases {

		t.Run(testCase.Name, func(t *testing.T) {
			mgr.handleWithPanicCatch(func() error {
				err := errors.New("some err")
				panic(err)
			})
		})
	}
}

func TestMgr_CheckConsolationPackConfUpdate(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mysqlMock := mocks.NewMockIStore(ctrl)
	bcMock := mocks.NewMockIBusinessConfManager(ctrl)
	mockBackpackCli := backpack.NewMockIClient(ctrl)
	mockUserPresent := userPresent.NewMockIClient(ctrl)

	mgr := &Mgr{
		store:            mysqlMock,
		bc:               bcMock,
		userPresentCli:   mockUserPresent,
		backpackCli:      mockBackpackCli,
		consolationAward: nil,
	}

	tNow := time.Now()
	cases := []struct {
		Name    string
		wantErr bool
		now     time.Time
	}{
		{
			Name:    "TestMgr_CheckConsolationPackConfUpdate",
			wantErr: false,
			now:     time.Date(tNow.Year(), tNow.Month(), tNow.Day(), tNow.Hour(), 0, 0, 0, time.Local),
		},
	}

	for _, testCase := range cases {
		gomock.InOrder(
			bcMock.EXPECT().GetConsolationAwardPackId().Return(uint32(1)),
			bcMock.EXPECT().GetConsolationAwardPackId().Return(uint32(1)),
			mockBackpackCli.EXPECT().GetPackageItemCfg(gomock.Any(), uint32(0), gomock.Any(), gomock.Any()).Return(&backpackPB.GetPackageItemCfgResp{ItemCfgList: []*backpackPB.PackageItemCfg{
				{
					BgItemId:  1,
					BgId:      1,
					ItemType:  uint32(backpackPB.PackageItemType_BACKPACK_PRESENT),
					ItemCount: 1,
					SourceId:  1,
				}}}, nil),
			mockUserPresent.EXPECT().GetPresentConfigById(gomock.Any(), uint32(1)),
		)

		t.Run(testCase.Name, func(t *testing.T) {
			err := mgr.CheckConsolationPackConfUpdate()
			if (err != nil) != testCase.wantErr {
				t.Errorf("CheckConsolationPackConfUpdate error = %v, wantErr = %v", err, testCase.wantErr)
				return
			}
		})
	}
}

func TestMgr_GetPreviousRoundRecords(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	mysqlMock := mocks.NewMockIStore(ctrl)
	bcMock := mocks.NewMockIBusinessConfManager(ctrl)

	mgr := &Mgr{
		store: mysqlMock,
		bc:    bcMock,
	}
	tNow := time.Now()
	cases := []struct {
		Name    string
		wantErr bool
		now     time.Time
		limit   uint32
		offset  uint32
	}{
		{
			Name:    "common",
			now:     time.Date(tNow.Year(), tNow.Month(), tNow.Day(), tNow.Hour(), 0, 0, 0, time.Local),
			wantErr: false,
			limit:   10,
			offset:  0,
		},
	}

	for _, testCase := range cases {
		gomock.InOrder(
			mysqlMock.EXPECT().GetPreviousRecordsPaging(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(
				[]*mysql.RoundInfo{
					{
						RoundId:      1,
						ActId:        1,
						PackId:       1,
						GiftId:       1,
						GiftName:     "111",
						GiftUrl:      "111",
						GiftPrice:    10,
						GiftNum:      1,
						TotalInvest:  10,
						BingoUid:     1,
						BingoUserCnt: 1,
						Status:       mysql.RoundStatusSuccess,
						IsHide:       false,
					}}, nil),
		)

		t.Run(testCase.Name, func(t *testing.T) {
			_, err := mgr.GetPreviousRoundRecords(ctx, testCase.limit, testCase.offset)
			if (err != nil) != testCase.wantErr {
				t.Errorf("GetPreviousRoundRecords error = %v, wantErr = %v", err, testCase.wantErr)
				return
			}
		})
	}
}

func TestMgr_GetNextRoundPrizeCache(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	redisMock := mocks.NewMockICache(ctrl)
	bcMock := mocks.NewMockIBusinessConfManager(ctrl)

	mgr := &Mgr{
		redisCache: redisMock,
		bc:         bcMock,
	}
	tNow := time.Now()
	cases := []struct {
		Name    string
		wantErr bool
		now     time.Time
		actId   uint32
	}{
		{
			Name:    "common",
			now:     time.Date(tNow.Year(), tNow.Month(), tNow.Day(), tNow.Hour(), 0, 0, 0, time.Local),
			wantErr: false,
			actId:   1,
		},
	}

	for _, testCase := range cases {
		gomock.InOrder(
			redisMock.EXPECT().GetNextRoundPrizeCache(gomock.Any()).Return(true, &pb.StarTAwardInfo{
				PackId:     1,
				PackName:   "1",
				PackPic:    "11",
				PackAmount: 1,
				UnitPrice:  1,
				FinTime:    1,
				GiftId:     1,
			}, nil),
		)

		t.Run(testCase.Name, func(t *testing.T) {
			_, _, err := mgr.GetNextRoundPrizeCache(ctx, testCase.actId)
			if (err != nil) != testCase.wantErr {
				t.Errorf("GetNextRoundPrizeCache error = %v, wantErr = %v", err, testCase.wantErr)
				return
			}
		})
	}
}

func TestMgr_GetRollingRecord(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	mysqlMock := mocks.NewMockIStore(ctrl)
	bcMock := mocks.NewMockIBusinessConfManager(ctrl)

	mgr := &Mgr{
		store: mysqlMock,
		bc:    bcMock,
	}
	tNow := time.Now()
	cases := []struct {
		Name    string
		wantErr bool
		now     time.Time
		actId   uint32
	}{
		{
			Name:    "common",
			now:     time.Date(tNow.Year(), tNow.Month(), tNow.Day(), tNow.Hour(), 0, 0, 0, time.Local),
			wantErr: false,
			actId:   1,
		},
	}

	for _, testCase := range cases {
		gomock.InOrder(
			mysqlMock.EXPECT().GetLastSuccessRoundInfo(gomock.Any(), gomock.Any()).Return(true, &mysql.RoundInfo{
				RoundId:      1,
				ActId:        1,
				PackId:       1,
				GiftId:       1,
				GiftName:     "",
				GiftUrl:      "",
				GiftPrice:    0,
				GiftNum:      0,
				TotalInvest:  0,
				BingoUid:     1,
				BingoUserCnt: 1,
				Status:       mysql.RoundStatusSuccess,
				IsHide:       false,
				BeginTime:    time.Time{},
				EndTime:      time.Time{},
				CreateTime:   time.Time{},
				UpdateTime:   time.Time{},
			}, nil),
		)

		t.Run(testCase.Name, func(t *testing.T) {
			_, err := mgr.GetRollingRecord(ctx, testCase.actId)
			if (err != nil) != testCase.wantErr {
				t.Errorf("GetRollingRecord error = %v, wantErr = %v", err, testCase.wantErr)
				return
			}
		})
	}
}

func TestMgr_pushBreakingNews(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	mysqlMock := mocks.NewMockIStore(ctrl)
	bcMock := mocks.NewMockIBusinessConfManager(ctrl)
	publicNoticeMock := publicNotice.NewMockIClient(ctrl)

	mgr := &Mgr{
		store:           mysqlMock,
		bc:              bcMock,
		publicNoticeCli: publicNoticeMock,
	}

	type args struct {
		ctx          context.Context
		info         *mysql.RoundInfo
		bingoUidList []uint32
	}
	tests := []struct {
		name     string
		initFunc func()
		args     args
		wantErr  bool
	}{
		{
			name: "only self visible",
			initFunc: func() {
				bcMock.EXPECT().GetPublicNewOnlySelfVisible().Return(true)
				publicNoticeMock.EXPECT().PushBreakingNews(gomock.Any(), gomock.Any()).AnyTimes()
			},
			args: args{
				ctx:          ctx,
				info:         &mysql.RoundInfo{},
				bingoUidList: []uint32{1, 2},
			},
			wantErr: false,
		},
		{
			name: "more than third bingo",
			initFunc: func() {
				bcMock.EXPECT().GetPublicNewOnlySelfVisible().Return(false)
				publicNoticeMock.EXPECT().PushBreakingNews(gomock.Any(), gomock.Any()).AnyTimes()
			},
			args: args{
				ctx:          ctx,
				info:         &mysql.RoundInfo{},
				bingoUidList: []uint32{1, 2},
			},
			wantErr: false,
		},
		{
			name: "more than third bingo",
			initFunc: func() {
				bcMock.EXPECT().GetPublicNewOnlySelfVisible().Return(false)
				publicNoticeMock.EXPECT().PushBreakingNews(gomock.Any(), gomock.Any()).AnyTimes()
			},
			args: args{
				ctx:          ctx,
				info:         &mysql.RoundInfo{},
				bingoUidList: []uint32{1, 2, 4, 5},
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := mgr
			if tt.initFunc != nil {
				tt.initFunc()
			}
			if err := m.pushBreakingNews(tt.args.ctx, tt.args.info, tt.args.bingoUidList); (err != nil) != tt.wantErr {
				t.Errorf("pushBreakingNews() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestMgr_handleCreateRound2(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	mysqlMock := mocks.NewMockIStore(ctrl)
	cacheMock := mocks.NewMockICache(ctrl)
	bcMock := mocks.NewMockIBusinessConfManager(ctrl)
	publicNoticeMock := publicNotice.NewMockIClient(ctrl)

	mgr := &Mgr{
		store:           mysqlMock,
		bc:              bcMock,
		redisCache:      cacheMock,
		publicNoticeCli: publicNoticeMock,
	}
	type args struct {
		ctx       context.Context
		actId     uint32
		currRound *mysql.RoundInfo
		dayEnd    time.Time
	}
	tests := []struct {
		name     string
		initFunc func()
		args     args
		wantErr  bool
	}{
		{
			name: "have next round",
			initFunc: func() {
				cacheMock.EXPECT().GetNextRoundPrizeCache(gomock.Any()).Return(true, &pb.StarTAwardInfo{
					PackId:     1,
					PackName:   "11",
					PackPic:    "11",
					PackAmount: 1,
					UnitPrice:  1,
					GiftId:     1,
				}, nil)
				bcMock.EXPECT().GetEveryRoundLongSc().Return(uint32(18))
				mysqlMock.EXPECT().InsertNewRoundInfo(gomock.Any(), gomock.Any())
				bcMock.EXPECT().GetEveryRoundLongSc().Return(uint32(18))
				cacheMock.EXPECT().DelNextRoundPrizeCache(gomock.Any()).Return(nil)
			},
			args: args{
				ctx:   ctx,
				actId: 1,
				currRound: &mysql.RoundInfo{
					RoundId: 1,
					ActId:   1,
					Status:  mysql.RoundStatusSuccess,
				},
				dayEnd: time.Now().Add(-time.Hour * 24),
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := mgr
			if tt.initFunc != nil {
				tt.initFunc()
			}
			if err := m.handleCreateRound(tt.args.ctx, tt.args.actId, tt.args.currRound, tt.args.dayEnd); (err != nil) != tt.wantErr {
				t.Errorf("handleCreateRound() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
