package mysql

import (
	"context"
	"database/sql"
	"fmt"
	"github.com/go-sql-driver/mysql"
	"gitlab.ttyuyin.com/tt-infra/tyr/log"
	"strconv"
	"strings"
	"time"
)

const (
	UserInvestPending   = iota // 巡航中
	UserInvestSettling         // 结算中
	UserInvestWaitAward        // 待发奖
	UserInvestDone             // 完成
	UserInvestFail             // 巡航失败
)

// 用户补给信息表
func genUserInvestInfoTblName(roundTime time.Time) string {
	return fmt.Sprintf("star_trek_user_invest_info_%04d%02d", roundTime.Year(), roundTime.Month())
}

var CreateUserInvestInfoTbl = `CREATE TABLE %s(
    id int(10) unsigned NOT NULL AUTO_INCREMENT,
    round_id  int(10) unsigned NOT NULL,
    uid int(10) unsigned NOT NULL COMMENT 'uid',
    invest_val int(10) unsigned NOT NULL default '0' COMMENT '投入补给值',
    status tinyint unsigned NOT NULL COMMENT '状态 0-巡航中 1-结算中 2-待发奖 3-发奖完成 4-巡航失败',
	round_time timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    ctime timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    mtime TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
	bingo_uid int(10) unsigned NOT NULL default '0' COMMENT '大奖获得者',
	is_bingo tinyint unsigned NOT NULL COMMENT '是否中奖',
    
    PRIMARY KEY (id),
    UNIQUE KEY idx_round_uid (round_id, uid),
    INDEX idx_uid (uid),
 	INDEX idx_status (status),
    INDEX idx_create_time (ctime)
)engine=InnoDB default charset=utf8 COMMENT "星际巡航用户补给信息表";`

type UserInvestInfo struct {
	Id         uint32    `db:"id"`
	RoundId    uint32    `db:"round_id"`
	Uid        uint32    `db:"uid"`
	InvestVal  uint32    `db:"invest_val"`
	Status     uint32    `db:"status"`
	IsBingo    bool      `db:"is_bingo"`
	RoundTime  time.Time `db:"round_time"`
	CreateTime time.Time `db:"ctime"`
	UpdateTime time.Time `db:"mtime"`
}

func (s *Store) CreateUserInvestInfoTbl(ctx context.Context, roundTime time.Time) error {
	_, err := s.db.ExecContext(ctx, fmt.Sprintf(CreateUserInvestInfoTbl, genUserInvestInfoTblName(roundTime)))
	return err
}

func (s *Store) InsertUserInvestInfo(ctx context.Context, roundTime time.Time, info *UserInvestInfo) error {
	if info == nil {
		return nil
	}

	query := fmt.Sprintf("INSERT INTO %s(round_id, uid, invest_val, round_time) VALUES (?, ?, ?, ?)", genUserInvestInfoTblName(roundTime))
	params := []interface{}{info.RoundId, info.Uid, info.InvestVal, roundTime}

	_, err := s.db.ExecContext(ctx, query, params...)
	if err != nil {
		if mysqlErr, ok := err.(*mysql.MySQLError); ok && mysqlErr.Number == 1146 {
			// 表不存在，建表
			err = s.CreateUserInvestInfoTbl(ctx, roundTime)
			if err != nil {
				log.ErrorWithCtx(ctx, "InsertUserInvestInfo fail to CreateAwardLogTbl. uid:%d, err:%v", info.Uid, err)
				return err
			}

			_, err = s.db.ExecContext(ctx, query, params...)
			if err == nil {
				return nil
			}
		}
		log.ErrorWithCtx(ctx, "InsertUserInvestInfo fail to insert. uid:%d, err:%v", info.Uid, err)
		return err
	}

	return nil
}

func (s *Store) GetUserInvestInfo(ctx context.Context, roundId, uid uint32, roundTime time.Time) (*UserInvestInfo, bool, error) {
	r := &UserInvestInfo{}

	query := fmt.Sprintf("SELECT id, round_id, uid, is_bingo, invest_val, status, round_time, ctime, mtime FROM %s "+
		"WHERE round_id=? AND uid=?", genUserInvestInfoTblName(roundTime))
	err := s.db.GetContext(ctx, r, query, roundId, uid)
	if err != nil {
		mysqlErr, ok := err.(*mysql.MySQLError)
		if ok && mysqlErr.Number == 1146 {
			return r, false, nil

		} else if err == sql.ErrNoRows {
			return r, false, nil
		}
		log.ErrorWithCtx(ctx, "GetUserInvestInfo fail.uid:%d, roundId:%d, err:%v", uid, roundId, err)
		return r, false, err
	}

	return r, true, nil
}

func (s *Store) GetUserInvestInfos(ctx context.Context, status, limit uint32, expireTime, roundTime time.Time) ([]*UserInvestInfo, error) {
	list := make([]*UserInvestInfo, 0)

	query := fmt.Sprintf("SELECT id, round_id, uid, is_bingo, invest_val, status, round_time, ctime, mtime FROM %s "+
		"WHERE status=? AND ctime>? AND invest_val>0 LIMIT ?", genUserInvestInfoTblName(roundTime))
	err := s.db.SelectContext(ctx, &list, query, status, expireTime, limit)
	if err != nil {
		mysqlErr, ok := err.(*mysql.MySQLError)
		if ok && mysqlErr.Number == 1146 {
			// 表不存在
			return list, nil
		}
		log.ErrorWithCtx(ctx, "GetUserInvestInfos fail. status:%d, err:%v", status, err)
		return list, err
	}

	return list, nil
}

func (s *Store) AddUserInvestVal(ctx context.Context, tx *sql.Tx, roundId, uid, incrVal uint32, roundTime time.Time) (bool, error) {
	query := fmt.Sprintf("UPDATE %s SET invest_val=invest_val+? WHERE round_id=? AND uid=?", genUserInvestInfoTblName(roundTime))
	ret, err := tx.ExecContext(ctx, query, incrVal, roundId, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "AddUserInvestVal fail.uid:%d, roundId:%d, incrVal:%d, err:%v", uid, roundId, incrVal, err)
		return false, err
	}

	ra, _ := ret.RowsAffected()
	return ra > 0, nil
}

func (s *Store) UpdateUserInvestStatus(ctx context.Context, tx *sql.Tx, roundId, toStatus, fromStatus uint32, uidList []uint32, roundTime time.Time) (bool, error) {
	if fromStatus == toStatus || len(uidList) == 0 {
		return false, nil
	}

	query := fmt.Sprintf("UPDATE %s SET status=? WHERE round_id=? AND uid in(%s) AND status=?", genUserInvestInfoTblName(roundTime), genParamJoinStr(uidList))
	ret, err := tx.ExecContext(ctx, query, toStatus, roundId, fromStatus)
	if err != nil {
		log.ErrorWithCtx(ctx, "UpdateUserInvestStatus fail.uid:%v, roundId:%d, toStatus:%d, err:%v", uidList, roundId, toStatus, err)
		return false, err
	}

	ra, _ := ret.RowsAffected()
	return int(ra) == len(uidList), nil
}

func (s *Store) UpdateUserBingo(ctx context.Context, tx *sql.Tx, roundId uint32, uidList []uint32, roundTime time.Time) (bool, error) {
	if len(uidList) == 0 {
		return false, nil
	}

	query := fmt.Sprintf("UPDATE %s SET is_bingo=? WHERE round_id=? AND uid in(%s)", genUserInvestInfoTblName(roundTime), genParamJoinStr(uidList))
	ret, err := tx.ExecContext(ctx, query, true, roundId)
	if err != nil {
		log.ErrorWithCtx(ctx, "UpdateUserBingo fail.uid:%v, roundId:%d, err:%v", uidList, roundId, err)
		return false, err
	}

	ra, _ := ret.RowsAffected()
	return ra > 0, nil
}

func (s *Store) GetRoundBingoUidList(ctx context.Context, roundId uint32, roundTime time.Time) ([]uint32, error) {
	list := make([]uint32, 0)
	query := fmt.Sprintf("SELECT uid FROM %s WHERE round_id=? AND is_bingo>0", genUserInvestInfoTblName(roundTime))

	err := s.db.SelectContext(ctx, &list, query, roundId)
	if err != nil {
		mysqlErr, ok := err.(*mysql.MySQLError)
		if ok && mysqlErr.Number == 1146 {
			// 表不存在
			return list, nil
		}
		log.ErrorWithCtx(ctx, "GetRoundBingoUidList fail. roundId:%d, err:%v", roundId, err)
		return list, err
	}

	return list, nil
}

func (s *Store) UpdateUserInvestStatusByRound(ctx context.Context, tx *sql.Tx, roundId, toStatus uint32, fromStatus []uint32, roundTime time.Time) (bool, error) {
	if len(fromStatus) == 0 {
		return false, nil
	}

	query := fmt.Sprintf("UPDATE %s SET status=? WHERE round_id=? AND status in(%s)", genUserInvestInfoTblName(roundTime), genParamJoinStr(fromStatus))
	ret, err := tx.ExecContext(ctx, query, toStatus, roundId)
	if err != nil {
		log.ErrorWithCtx(ctx, "UpdateUserInvestStatusByRound fail. roundId:%d, toStatus:%d, err:%v", roundId, toStatus, err)
		return false, err
	}

	ra, _ := ret.RowsAffected()
	return ra > 0, nil
}

type UserInvestVal struct {
	Uid       uint32 `db:"uid"`
	InvestVal uint32 `db:"invest_val"`
}

func (s *Store) GetUserInvestValList(ctx context.Context, roundId uint32, roundTime time.Time) ([]*UserInvestVal, error) {
	list := make([]*UserInvestVal, 0)
	query := fmt.Sprintf("SELECT uid, invest_val FROM %s WHERE round_id=? AND invest_val>0 order by invest_val DESC", genUserInvestInfoTblName(roundTime))

	err := s.db.SelectContext(ctx, &list, query, roundId)
	if err != nil {
		mysqlErr, ok := err.(*mysql.MySQLError)
		if ok && mysqlErr.Number == 1146 {
			// 表不存在
			return list, nil
		}
		log.ErrorWithCtx(ctx, "GetUserInvestValList fail. roundId:%d, err:%v", roundId, err)
		return list, err
	}

	return list, nil
}

type RoundInvestSummary struct {
	RoundId         uint32 `db:"round_id"`
	UserCnt         uint32 `db:"user_cnt"`
	TotalInvest     uint32 `db:"total_invest"`
	BingoUserInvest uint32 `db:"bingo_user_invest"`
	ConsolationCnt  uint32 `db:"consolation_cnt"`
}

func (s *Store) GetRoundInvestSummary(ctx context.Context, roundId, bingoUid, consolationBase uint32, roundTime time.Time) (*RoundInvestSummary, error) {
	info := &RoundInvestSummary{}
	query := fmt.Sprintf("SELECT round_id, COUNT(1) as user_cnt, SUM(invest_val) as total_invest, "+
		"SUM(IF(is_bingo=1 OR status=%d, 0, CEILING(invest_val/%d))) as consolation_cnt, "+
		"SUM(IF(is_bingo=1, invest_val, 0)) as bingo_user_invest "+
		"FROM %s WHERE round_id=? GROUP BY round_id", UserInvestFail, consolationBase, genUserInvestInfoTblName(roundTime))

	err := s.readonlyDb.GetContext(ctx, info, query, roundId)
	if err != nil {
		mysqlErr, ok := err.(*mysql.MySQLError)
		if ok && mysqlErr.Number == 1146 {
			return info, nil

		} else if err == sql.ErrNoRows {
			return info, nil
		}
		log.ErrorWithCtx(ctx, "GetRoundInvestSummary fail. bingoUid:%d, roundId:%d, err:%v", bingoUid, roundId, err)
		return info, err
	}

	return info, nil
}

func (s *Store) GetJoinUserCnt(ctx context.Context, begin, end time.Time) (uint32, error) {
	cnt := uint32(0)
	query := fmt.Sprintf("SELECT COUNT(DISTINCT uid) FROM %s WHERE round_time>=? AND round_time<?", genUserInvestInfoTblName(begin))

	err := s.readonlyDb.GetContext(ctx, &cnt, query, begin, end)
	if err != nil {
		mysqlErr, ok := err.(*mysql.MySQLError)
		if ok && mysqlErr.Number == 1146 {
			return cnt, nil

		} else if err == sql.ErrNoRows {
			return cnt, nil
		}
		log.ErrorWithCtx(ctx, "GetJoinUserCnt fail. err:%v", err)
		return cnt, err
	}

	return cnt, nil
}

// 用户补给消耗流水表
func GenInvestLogTblName(roundTime time.Time) string {
	return fmt.Sprintf("star_trek_invest_cost_log_%04d%02d", roundTime.Year(), roundTime.Month())
}

const (
	InvestCostPrepare         = iota // 准备中
	InvestCostAddVal                 // 增加补给成功
	InvestCostWaitRollback           // 等待回滚（巡航失败）
	InvestCostRollbackDone           // 回滚成功（巡航失败）
	InvestCostSuccess                // 巡航完成
	InvestCostFailAndRollback        // 回滚（补给失败）
)

var CreateInvestCostLogTbl = `CREATE TABLE %s(
    id int unsigned NOT NULL AUTO_INCREMENT,
    order_id varchar(1024) NOT NULL default '',
    uid int(10) unsigned NOT NULL default '0',
    round_id int(10) unsigned NOT NULL default '0' COMMENT 'round_id',
    status int NOT NULL default 0 COMMENT '用户补给投入状态,0-prepare 1-增加补给成功 2-等待回滚 3-回滚成功 4-巡航完成',
    cost_gift_id int(10) unsigned NOT NULL default '0' COMMENT '消耗补给物品id',
    cost_gift_name varchar(1024) NOT NULL default '' COMMENT '补给消耗物品名',
    cost_gift_price int(10) unsigned NOT NULL default '0' COMMENT '消耗补给物品单价',
    cost_gift_num int(10) unsigned NOT NULL default '0' COMMENT '消耗补给物品数量',
    cost_gift_type int NOT NULL default 0 COMMENT '礼物类型,see backpack::PackageItemType',
	round_time timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    outside_time timestamp NOT NULL default CURRENT_TIMESTAMP COMMENT '补给投入时间',
    ctime timestamp NOT NULL default CURRENT_TIMESTAMP COMMENT '创建时间',
    mtime timestamp NOT NULL default CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    
    primary key (id),
    INDEX idx_uid(uid),
	INDEX idx_round_id(round_id),
    INDEX idx_create_time(ctime),
    INDEX idx_status (status),
    INDEX idx_invest_time(outside_time),
    UNIQUE KEY order_id(order_id)
)engine=InnoDB default charset=utf8 COMMENT "星际巡航用户投入补给消耗流水";`

type InvestCostLog struct {
	Id          uint32    `db:"id"`
	OrderId     string    `db:"order_id"`
	RoundId     uint32    `db:"round_id"`
	Uid         uint32    `db:"uid"`
	Status      uint32    `db:"status"`
	GiftId      uint32    `db:"cost_gift_id"`
	GiftName    string    `db:"cost_gift_name"`
	GiftPrice   uint32    `db:"cost_gift_price"`
	GiftNum     uint32    `db:"cost_gift_num"`
	GiftType    uint32    `db:"cost_gift_type"` //see backpack::PackageItemType
	RoundTime   time.Time `db:"round_time"`
	OutsideTime time.Time `db:"outside_time"`
	CreateTime  time.Time `db:"ctime"`
	UpdateTime  time.Time `db:"mtime"`
}

func (s *Store) CreateInvestLogTbl(ctx context.Context, roundTime time.Time) error {
	_, err := s.db.ExecContext(ctx, fmt.Sprintf(CreateInvestCostLogTbl, GenInvestLogTblName(roundTime)))
	return err
}

func (s *Store) InsertInvestLogs(ctx context.Context, roundTime time.Time, uid uint32, logs []*InvestCostLog) error {
	if len(logs) == 0 {
		return nil
	}

	placeholder := make([]string, 0, len(logs))
	params := make([]interface{}, 0)
	for _, r := range logs {
		placeholder = append(placeholder, "(?,?,?,?,?,?,?,?,?,?)")
		params = append(params, r.OrderId, r.RoundId, uid, r.GiftId, r.GiftName, r.GiftPrice, r.GiftNum, r.GiftType, r.OutsideTime, roundTime)
	}

	query := fmt.Sprintf("INSERT INTO %s(order_id, round_id, uid, "+
		"cost_gift_id, cost_gift_name, cost_gift_price, cost_gift_num, cost_gift_type, outside_time, round_time) "+
		"VALUES %s", GenInvestLogTblName(roundTime), strings.Join(placeholder, ","))

	_, err := s.db.ExecContext(ctx, query, params...)
	if err != nil {
		if mysqlErr, ok := err.(*mysql.MySQLError); ok && mysqlErr.Number == 1146 {
			// 表不存在，建表
			err = s.CreateInvestLogTbl(ctx, roundTime)
			if err != nil {
				log.ErrorWithCtx(ctx, "InsertInvestLogs fail to CreateAwardLogTbl. uid:%d, err:%v", uid, err)
				return err
			}

			_, err = s.db.ExecContext(ctx, query, params...)
			if err == nil {
				return nil
			}
		}
		log.ErrorWithCtx(ctx, "InsertInvestLogs fail to insert. uid:%d, err:%v", uid, err)
		return err
	}

	return nil
}

func (s *Store) UpdateInvestLogStatus(ctx context.Context, tx *sql.Tx, roundTime time.Time, orderIds []string, toStatus uint32, fromStatusList []uint32) (bool, error) {
	if len(fromStatusList) == 0 || len(orderIds) == 0 {
		return false, nil
	}

	orderIdList := make([]string, 0, len(orderIds))
	for _, str := range orderIds {
		orderIdList = append(orderIdList, fmt.Sprintf("'%s'", str))
	}

	query := fmt.Sprintf("UPDATE %s SET status=? WHERE order_id in (%s) AND status in(%s)",
		GenInvestLogTblName(roundTime), strings.Join(orderIdList, ","), genParamJoinStr(fromStatusList))
	ret, err := tx.ExecContext(ctx, query, toStatus)
	if err != nil {
		log.ErrorWithCtx(ctx, "UpdateInvestLogStatus fail. orderId:%v, toStatus:%d, err:%v", orderIds, toStatus, err)
		return false, err
	}

	ra, _ := ret.RowsAffected()
	return ra > 0, nil
}

func (s *Store) UpdateInvestLogStatusByRound(ctx context.Context, tx *sql.Tx, roundTime time.Time, roundId, toStatus uint32, fromStatusList []uint32) (bool, error) {
	if len(fromStatusList) == 0 {
		return false, nil
	}

	query := fmt.Sprintf("UPDATE %s SET status=? WHERE round_id=? AND status in(%s)", GenInvestLogTblName(roundTime), genParamJoinStr(fromStatusList))
	ret, err := tx.ExecContext(ctx, query, toStatus, roundId)
	if err != nil {
		log.ErrorWithCtx(ctx, "UpdateInvestLogStatusByRound fail. roundId:%d, toStatus:%d, err:%v", roundId, toStatus, err)
		return false, err
	}

	ra, _ := ret.RowsAffected()
	return ra > 0, nil
}

func (s *Store) GetInvestLogsByStatus(ctx context.Context, roundTime time.Time, status, offset, limit uint32) ([]*InvestCostLog, error) {
	list := make([]*InvestCostLog, 0, limit)
	if limit == 0 {
		return list, nil
	}

	query := fmt.Sprintf("SELECT id, order_id, round_id, uid, status, "+
		"cost_gift_id, cost_gift_name, cost_gift_price, cost_gift_num, cost_gift_type, outside_time, round_time "+
		"FROM %s WHERE status=? limit ?,?", GenInvestLogTblName(roundTime))

	err := s.db.SelectContext(ctx, &list, query, status, offset, limit)
	if err != nil {
		mysqlErr, ok := err.(*mysql.MySQLError)
		if ok && mysqlErr.Number == 1146 {
			// 表不存在
			return list, nil
		}
		log.ErrorWithCtx(ctx, "GetInvestLogsByStatus fail. roundTime:%v, status:%d, err:%v", roundTime, status, err)
		return list, err
	}

	return list, nil
}

func (s *Store) GetInvestLogByOrder(ctx context.Context, roundTime time.Time, orderId string) (*InvestCostLog, bool, error) {
	r := &InvestCostLog{}
	query := fmt.Sprintf("SELECT id, order_id, round_id, uid, status, "+
		"cost_gift_id, cost_gift_name, cost_gift_price, cost_gift_num, cost_gift_type, outside_time, round_time "+
		"FROM %s WHERE order_id=? ", GenInvestLogTblName(roundTime))

	err := s.db.GetContext(ctx, r, query, orderId)
	if err != nil {
		mysqlErr, ok := err.(*mysql.MySQLError)
		if ok && mysqlErr.Number == 1146 {
			// 表不存在
			return r, false, nil
		}
		if err == sql.ErrNoRows {
			return r, false, nil
		}
		log.ErrorWithCtx(ctx, "GetInvestLogByOrder fail. orderId:%v, err:%v", orderId, err)
		return r, false, err
	}

	return r, true, nil
}

func (s *Store) GetCostTotalCountInfo(ctx context.Context, beginTime, endTime time.Time) (*StCount, error) {
	out := &StCount{}
	temp := "SELECT COUNT(1) as count, IF(SUM(cost_gift_num),SUM(cost_gift_num),0) as value FROM %s " +
		"WHERE outside_time >= ? AND outside_time < ? AND status > 0"

	tmpCnt := &StCount{}
	query := fmt.Sprintf(temp, GenInvestLogTblName(beginTime))
	err := s.readonlyDb.GetContext(ctx, tmpCnt, query, beginTime, endTime)
	if err != nil {
		mysqlErr, ok := err.(*mysql.MySQLError)
		if !ok || mysqlErr.Number != 1146 {
			log.Errorf("GetCostTotalCountInfo fail. queryMonthTime:%v, err:%v", beginTime, err)
			return out, err
		}
	}

	out.Count += tmpCnt.Count
	out.Value += tmpCnt.Value

	if beginTime.Day() == 1 {
		// 查上月
		tmpCnt := &StCount{}
		query := fmt.Sprintf(temp, GenInvestLogTblName(beginTime.AddDate(0, 0, -1)))
		err := s.readonlyDb.GetContext(ctx, tmpCnt, query, beginTime, endTime)
		if err != nil {
			mysqlErr, ok := err.(*mysql.MySQLError)
			if !ok || mysqlErr.Number != 1146 {
				log.Errorf("GetCostTotalCountInfo fail. queryMonthTime:%v, err:%v", beginTime, err)
				return out, err
			}
		}

		out.Count += tmpCnt.Count
		out.Value += tmpCnt.Value
	}

	return out, nil
}

func (s *Store) GetCostOrderIds(ctx context.Context, beginTime, endTime time.Time) ([]string, error) {
	list := make([]string, 0)
	temp := "SELECT order_id FROM %s WHERE outside_time >= ? AND outside_time < ? AND status > 0"

	query := fmt.Sprintf(temp, GenInvestLogTblName(beginTime))
	err := s.readonlyDb.SelectContext(ctx, &list, query, beginTime, endTime)
	if err != nil {
		mysqlErr, ok := err.(*mysql.MySQLError)
		if !ok || mysqlErr.Number != 1146 {
			log.Errorf("GetCostOrderIds fail. queryMonthTime:%v, err:%v", beginTime, err)
			return list, err
		}
	}

	if beginTime.Day() == 1 {
		// 查上月
		query := fmt.Sprintf(temp, GenInvestLogTblName(beginTime.AddDate(0, 0, -1)))
		err := s.readonlyDb.SelectContext(ctx, &list, query, beginTime, endTime)
		if err != nil {
			mysqlErr, ok := err.(*mysql.MySQLError)
			if !ok || mysqlErr.Number != 1146 {
				log.Errorf("GetCostOrderIds fail. queryMonthTime:%v, err:%v", beginTime, err)
				return list, err
			}
		}
	}

	return list, nil
}

type InvestRecord struct {
	Id        uint32    `db:"id"`
	RoundId   uint32    `db:"round_id"`
	Uid       uint32    `db:"uid"`
	InvestVal uint32    `db:"invest_val"`
	Status    uint32    `db:"status"`
	RoundTime time.Time `db:"round_time"`
	CostDesc  string    `db:"cost_desc"`
	IsBingo   bool      `db:"is_bingo"`
}

func GenRecordOffsetId(t time.Time, id uint32) string {
	return fmt.Sprintf("%04d-%02d-%d", t.Year(), t.Month(), id)
}

func ParseRecordOffsetId(offset string) (t time.Time, id uint32, ok bool) {
	ok = false
	if offset == "" {
		return
	}

	strList := strings.Split(offset, "-")
	if len(strList) != 3 {
		return
	}

	year, _ := strconv.ParseUint(strList[0], 10, 32)
	month, _ := strconv.ParseUint(strList[1], 10, 32)
	logId, _ := strconv.ParseUint(strList[2], 10, 32)

	t = time.Date(int(year), time.Month(month), 1, 0, 0, 0, 0, time.Local)
	return t, uint32(logId), true
}

// 获取用户参与及补给记录
func (s *Store) GetUserInvestRecords(ctx context.Context, uid, limit uint32, offset string, status []uint32, bingo bool) ([]*InvestRecord, error) {
	now := time.Now()
	limitTime := now.AddDate(0, 0, -60)
	limitMonthTime := time.Date(limitTime.Year(), limitTime.Month(), 1, 0, 0, 0, 0, time.Local)

	list := make([]*InvestRecord, 0, limit)
	if limit == 0 {
		return list, nil
	}

	statusLimit := ""
	if len(status) > 0 {
		statusLimit = fmt.Sprintf(" AND status in (%s) ", genParamJoinStr(status))
	}

	restLen := limit
	logTime, logId, ok := ParseRecordOffsetId(offset)
	if !ok {
		logTime = time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, time.Local)
	}

	doneStatusList := []uint32{
		InvestCostAddVal,
		InvestCostWaitRollback,
		InvestCostRollbackDone,
		InvestCostSuccess,
	}

	for { // for true
		params := make([]interface{}, 0)
		query := fmt.Sprintf("SELECT a.id, a.round_id, a.uid, a.is_bingo, a.invest_val, a.status, a.round_time, "+
			"IFNULL(group_concat(concat(b.cost_gift_name,'*',b.cost_gift_num) SEPARATOR '+'), '') as cost_desc FROM %s a "+
			"LEFT JOIN "+
			"(SELECT uid,round_id,cost_gift_name,sum(cost_gift_num) as cost_gift_num FROM %s WHERE uid=? AND status in (%s) GROUP BY round_id,uid,cost_gift_name) b "+
			"ON a.round_id=b.round_id and a.uid=b.uid WHERE a.uid=? AND a.round_time>? AND a.invest_val>0 %s ",
			genUserInvestInfoTblName(logTime), GenInvestLogTblName(logTime), genParamJoinStr(doneStatusList), statusLimit)
		params = append(params, uid, uid, limitTime)

		if logId > 0 {
			query += " AND a.id < ?"
			params = append(params, logId)
		}

		if bingo {
			//query += " AND a.uid=a.bingo_uid"
			query += " AND a.is_bingo = 1"
		}

		query += " GROUP BY a.round_id ORDER BY a.id desc limit ?"
		params = append(params, restLen)

		err := s.readonlyDb.SelectContext(ctx, &list, query, params...)
		if err != nil {
			if mysqlErr, ok := err.(*mysql.MySQLError); ok && mysqlErr.Number == 1146 {
				// 表不存在 do nothing
			} else if err == sql.ErrNoRows {
				// 无数据 do nothing
			} else {
				log.Errorf("GetUserInvestRecords db err:%s", err.Error())
				return nil, err
			}
		}

		if uint32(len(list)) >= limit {
			// 数据取够了
			break
		}

		restLen = limit - uint32(len(list)) // 还需要取多少条数据
		// 查上个月的
		logTime = logTime.AddDate(0, -1, 0)
		logId = 0

		if logTime.Before(limitMonthTime) {
			// 只查近60天的数据
			break
		}
	}

	return list, nil
}
