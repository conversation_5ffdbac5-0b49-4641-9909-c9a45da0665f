//nolint:all
package mgr

import (
    "context"
    "fmt"
    "time"

    "golang.52tt.com/services/tt-rev/channel-dating-game/internal/conf"

    "gitlab.ttyuyin.com/avengers/tyr/compatible/proto"
    "golang.52tt.com/clients/account"
    "golang.52tt.com/clients/seqgen/v2"
    "golang.52tt.com/pkg/datacenter"
    "golang.52tt.com/pkg/log"
    protogrpc "golang.52tt.com/pkg/protocol/grpc"
    gaPB "golang.52tt.com/protocol/app"
    channelpb "golang.52tt.com/protocol/app/channel"
    channelIMPb2 "golang.52tt.com/protocol/app/im"
    pushPb "golang.52tt.com/protocol/app/push"
    syncPB "golang.52tt.com/protocol/app/sync"
    pb "golang.52tt.com/protocol/services/channel-dating-game"
    channelIMPB "golang.52tt.com/protocol/services/channelim"
    kfkPB "golang.52tt.com/protocol/services/minToolkit/kafka/pb/kafka_channel_dating_game"
    publicNoticePb "golang.52tt.com/protocol/services/public-notice"
    pbAward "golang.52tt.com/protocol/services/risk-control/award-center"
    timelinePB "golang.52tt.com/protocol/services/timelinesvr"
    yswfukwdelaypb "golang.52tt.com/protocol/services/yswfukwdelay"
    "golang.52tt.com/services/helper-from-cpp/immsghelper"
    "golang.52tt.com/services/notify"
)

const (
    DrawByBizConf   = "biz_conf_version"
    DrawByStoreConf = "store_conf_version"
)

func (m *Mgr) TestDrawImage(ctx context.Context, cid, uidA, uidB, valueA, valueB uint32, pushChannel bool,
    t time.Time, useStoreConf bool, sceneId uint32) error {
    channelLv, _, err := m.CheckDatingEntry(cid)
    if err != nil {
        log.WarnWithCtx(ctx, "TestDrawImage failed to CheckDatingEntry. cid: %d, err: %v", cid, err)
        return nil
    }

    userProfile, err := m.userProfileCli.BatchGetUserProfile(ctx, []uint32{uidA, uidB})
    if err != nil {
        log.WarnWithCtx(ctx, "TestDrawImage failed to BatchGetUserProfile. cid: %d, err: %v", cid, err)
        return nil
    }
    userA := userProfile[uidA]
    userB := userProfile[uidB]
    hatA, _ := m.getHatConfByTBean(userA.GetSex() == 1, valueA)
    hatB, _ := m.getHatConfByTBean(userB.GetSex() == 1, valueB)

    log.Infof("TestDrawImage  cid:%d,channelLv:%d, uidA: %d, uidB: %d, valueA: %d, valueB:%d", cid, channelLv, uidA, uidB, valueA, valueB)

    if useStoreConf {
        m.dateSuccessHandleV2(ctx, cid, channelLv, uidA, uidB, valueA, valueB, hatA, hatB, pushChannel, t, sceneId)
    } else {
        m.dateSuccessHandle(ctx, cid, channelLv, uidA, uidB, valueA, valueB, hatA, hatB, pushChannel)
    }
    return nil
}

// GetLikeBeatScene 获取对应心动场景配置
func (m *Mgr) GetLikeBeatScene(channelId, channelLv, totalScore uint32) (sceneConf *conf.SceneInfo, exist bool, err error) {
    var drawConf *conf.DrawConf
    drawConf = m.bc.GetDrawConfList()
    // 测试模式
    if m.bc.IsTestChannelId(channelId) {
        drawConf = m.bc.GetActivityDrawConfList() // 直接取活动配置
    }
    if drawConf == nil {
        return
    }

    // drawConf.SceneList 已经按照TBean逆序排列
    for _, scene := range drawConf.SceneList {
        if totalScore >= scene.TBean {
            sceneConf = scene
            break
        }
    }

    // 无符合的场景
    if sceneConf == nil {
        log.Infof("GetLikeBeatLevel fail. channelId:%d, val:%d, not scene match", channelId, totalScore)
        return &conf.SceneInfo{}, false, nil
    }

    // 限时活动活动场景对所有房间开放
    if drawConf.InActivity && sceneConf.OpenAllChannel {
        log.Infof("GetLikeBeatScene sceneConf.OpenAllChannel, channelId:%d, score:%d, sceneConf:%+v", channelId, totalScore, sceneConf)
        return sceneConf, true, nil
    }

    limitLv, ok := m.bc.GetChannelLv2MaxSceneLv()[fmt.Sprint(channelLv)]

    // 送礼限时活动活动场景值，不受房间等级限制，取最大的限时开放等级
    if drawConf.InActivity {
        for _, scene := range drawConf.SceneList {
            if scene.OpenAllChannel &&
                totalScore >= scene.TBean &&
                limitLv < scene.Level {
                log.Infof("GetLikeBeatLevel in activity. channelId:%d, val:%d,  scene:%+v",
                    channelId, totalScore, scene)
                return scene, true, nil
            }
        }
    }

    // 无等级限制 或者 未超过该房间的限制等级，可以返回
    if !ok || sceneConf.Level <= limitLv {
        log.Infof("GetLikeBeatLevel not over channel lev channelId:%d, val:%d,  scene:%+v,limitLv:%d",
            channelId, totalScore, sceneConf, limitLv)
        return sceneConf, true, nil
    }

    // 超过该房间的限制最大等级,按最大等级算
    for _, scene := range drawConf.SceneList {
        if limitLv == scene.Level {
            // 取限制最大等级的场景
            log.Infof("GetLikeBeatLevel channel lev limit channelId:%d, val:%d,  scene:%v, limitLv:%d",
                channelId, totalScore, sceneConf, limitLv)
            return scene, true, nil
        }
    }
    return &conf.SceneInfo{}, false, nil
}

func (m *Mgr) DateSuccessHandle(ctx context.Context, cid, uidA, uidB uint32, pushChannel bool) error {
    ctx, cancel := protogrpc.NewContextWithInfoTimeout(ctx, 30*time.Second)
    defer cancel()

    channelLv, _, err := m.CheckDatingEntry(cid)
    if err != nil {
        log.WarnWithCtx(ctx, "DateSuccessHandle failed to CheckDatingEntry. cid: %d, err: %v", cid, err)
        return err
    }

    valA, err := m.GetUserLikeBeatVal(ctx, uidA, cid)
    if err != nil {
        log.WarnWithCtx(ctx, "DateSuccessHandle failed to GetUserLikeBeatVal. cid: %d, uid: %d", cid, uidA)
        return err
    }

    valB, err := m.GetUserLikeBeatVal(ctx, uidB, cid)
    if err != nil {
        log.WarnWithCtx(ctx, "DateSuccessHandle failed to GetUserLikeBeatVal. cid: %d, uid: %d", cid, uidB)
        return err
    }

    allHatUser, err := m.GetAllHatUser(cid)
    if err != nil {
        log.ErrorWithCtx(ctx, "DateSuccessHandle failed to GetAllHatUser. cid: %d, err: %v", cid, err)
        return err
    }

    var hatA, hatB *pb.DatingGameHatCfg
    for _, item := range allHatUser {
        if item.Uid == uidA {
            hatA = item.GetHatCfg()
        }
        if item.Uid == uidB {
            hatB = item.GetHatCfg()
        }
    }

    if m.bc.GetChooseDrawImgVersion(uidA, uidB) == DrawByStoreConf {
        now := time.Now()
        return m.dateSuccessHandleV2(ctx, cid, channelLv, uidA, uidB, valA, valB, hatA, hatB, pushChannel, now, 0)
    }

    return m.dateSuccessHandle(ctx, cid, channelLv, uidA, uidB, valA, valB, hatA, hatB, pushChannel)
}

func (m *Mgr) dateSuccessHandle(ctx context.Context, cid, channelLv, uidA, uidB, valA, valB uint32, hatA, hatB *pb.DatingGameHatCfg, pushChannel bool) error { //NOSONAR
    // 匹配对应场景
    sceneCfg, exist, err := m.GetLikeBeatScene(cid, channelLv, valA+valB)
    if err != nil {
        log.Errorf("DateSuccessHandle failed to GetLikeBeatScene. cid:%d, uidA:%d, uidB:%d, valA:%d, valB:%d, err:%v",
            cid, uidA, uidB, valA, valB, err)
        return err
    }

    if !exist {
        log.Errorf("DateSuccessHandle failed to GetLikeBeatScene. not match scene, cid:%d, uidA:%d, uidB:%d, valA:%d, valB:%d",
            cid, uidA, uidB, valA, valB)
        return nil
    }

    userMap, err := m.userProfileCli.BatchGetUserProfileV2(ctx, []uint32{uidA, uidB}, true)
    if err != nil {
        log.Errorf("DateSuccessHandle failed to GetUsersMap. cid:%d, uidA:%d, uidB:%d, valA:%d, valB:%d, err:%v",
            cid, uidA, uidB, valA, valB, err)
        return err
    }

    // 生成房间场景牵手图
    channelImageUrl, err := m.CreateChannelImage(ctx, cid, userMap[uidA], userMap[uidB], hatA, hatB, sceneCfg)
    if err != nil {
        log.Errorf("DateSuccessHandle failed to CreateChannelImage. cid:%d, uidA:%d, uidB:%d, valA:%d, valB:%d, err:%v",
            cid, uidA, uidB, valA, valB, err)
        return err
    }

    if pushChannel {
        // 房间推送
        for i := 0; i < 3; i++ {
            err = m.pushChannelSceneMsg(ctx, cid, userMap[uidA], userMap[uidB], channelImageUrl, sceneCfg)
            if err != nil {
                log.Errorf("DateSuccessHandle failed to pushChannelSceneMsg. cid:%d, uidA:%d, uidB:%d, valA:%d, valB:%d, err:%v",
                    cid, uidA, uidB, valA, valB, err)
                continue
            }
            break
        }
    }

    //全服推送
    if pushChannel {
        go m.pushBreakingNewsV3(userMap[uidA], userMap[uidB], cid, sceneCfg.Level)
    }

    // 生成im场景牵手图
    imImageUrl, err := m.CreateImImage(ctx, cid, userMap[uidA], userMap[uidB], hatA, hatB, sceneCfg)
    if err != nil {
        log.Errorf("DateSuccessHandle failed to CreateImImage. cid:%d, uidA:%d, uidB:%d, valA:%d, valB:%d, err:%v",
            cid, uidA, uidB, valA, valB, err)
        return err
    }

    // Im推送
    err = m.pushImSceneMsg(ctx, cid, userMap[uidA], userMap[uidB], channelImageUrl, imImageUrl)
    if err != nil {
        log.Errorf("DateSuccessHandle failed to pushImSceneMsg. cid:%d, uidA:%d, uidB:%d, valA:%d, valB:%d, err:%v",
            cid, uidA, uidB, valA, valB, err)
    }

    //发送头像框奖励
    m.sendLevelReward(ctx, cid, uidA, uidB, valA+valB)

    log.Infof("DateSuccessHandle cid:%d, uidA:%d, uidB:%d, valA:%d, valB:%d, level:%d, levelScore:%d",
        cid, uidA, uidB, valA, valB, sceneCfg.Level, sceneCfg.TBean)
    m.SendOssReport(ctx, uidA, uidB, cid, valA, valB, sceneCfg.Level, sceneCfg.TBean)
    m.sendKFKDatingSuccess(uidA, uidB, cid, valA, valB, sceneCfg.Level, sceneCfg.TBean, uint32(sceneCfg.SceneId), sceneCfg.SceneName)
    return nil
}

func (m *Mgr) pushChannelSceneMsg(ctx context.Context, cid uint32, userA, userB *gaPB.UserProfile, imageUrl string, sceneCfg *conf.SceneInfo) error {

    ShowoffMsg := &channelpb.DatingGameShowoff{
        DsInfo: &gaPB.DownloadSourceInfo{
            SourceType: uint32(gaPB.DownloadSourceInfo_DOWNLOAD_SOURCE_EFFECTS),
            SourceId:   uint32(sceneCfg.SceneId) + uint32(sceneCfg.UpdateTs), // 加上场景更新时间戳变更id
            Url:        sceneCfg.EffectUrl,
            Md5:        sceneCfg.Md5,
        },
        ShowoffImageUrl: imageUrl,
        ServerTime:      uint32(time.Now().Unix()),
    }

    ShowoffMsg.ShowoffUinfos = append(ShowoffMsg.ShowoffUinfos, &channelpb.ShowoffUserInfo{
        Uid:         userA.GetUid(),
        Account:     userA.GetAccount(),
        Nickname:    userA.GetNickname(),
        Sex:         userA.GetSex(),
        UserProfile: userA,
    })
    ShowoffMsg.ShowoffUinfos = append(ShowoffMsg.ShowoffUinfos, &channelpb.ShowoffUserInfo{
        Uid:         userB.GetUid(),
        Account:     userB.GetAccount(),
        Nickname:    userB.GetNickname(),
        Sex:         userB.GetSex(),
        UserProfile: userB,
    })

    b, err := ShowoffMsg.Marshal()
    if err != nil {
        log.Errorf("pushChannelSceneMsg failed to Marshal. cid:%d, uidA:%v, uidB:%v, err:%v", cid, userA.GetUid(), userB.GetUid(), err)
        return err
    }

    msgType := uint32(channelpb.ChannelMsgType_CHANNEL_DATING_SHOWOFF)
    msg := &channelIMPB.ChannelCommonMsg{
        FromUid:      userA.GetUid(),
        FromAccount:  userA.GetAccount(),
        FromNick:     userA.GetNickname(),
        Time:         uint64(time.Now().Unix()),
        ToChannelId:  cid,
        Type:         msgType,
        Content:      "喜结良缘",
        PbOptContent: b,
    }

    seqId, isStoreMsg, err := m.channelImCli.SendCommonMessage(ctx, userA.GetUid(), cid, msg, true, msgType)
    if err != nil {
        log.Errorf("pushChannelSceneMsg failed to SendCommonMessage. cid:%d, uidA:%v, uidB:%v, err:%v", cid, userA.GetUid(), userB.GetUid(), err)
        return err
    }

    log.Debugf("pushChannelSceneMsg cid:%d, uidA:%v, uidB:%v, imageUrl:%s, seqId:%d, isStoreMsg:%v DsInfo：%+v", cid, userA.GetUid(), userB.GetUid(), imageUrl, seqId, isStoreMsg, ShowoffMsg.GetDsInfo())
    return nil
}

func (m *Mgr) pushImSceneMsg(ctx context.Context, cid uint32, userA, userB *gaPB.UserProfile, channelImageUrl, imImageUrl string) error {
    desc := fmt.Sprintf("%s 我们牵手成功", time.Now().Format("2006.01.02"))
    msg := &channelpb.GamePushLikeUserRecord{
        ShowoffImageUrl:     imImageUrl,
        DescFirst:           "糟糕！！！是心动的感觉",
        DescLast:            desc,
        ShowoffFullImageUrl: channelImageUrl,
    }

    b, err := msg.Marshal()
    if err != nil {
        log.Errorf("pushImSceneMsg failed to Marshal. cid:%d, uidA:%v, uidB:%v, err:%v", cid, userA.GetUid(), userB.GetUid(), err)
        return err
    }

    err = m.pushImMsg(ctx, userA, userB, uint32(channelIMPb2.IM_MSG_TYPE_CHANNEL_DATING_GAME_LIKE_USER_NOTIFY), "", b)
    if err != nil {
        log.Errorf("pushImSceneMsg failed to pushImMsg. cid:%d, uid:%v, toUid:%v, err:%v", cid, userA.GetUid(), userB.GetUid(), err)
    }

    err = m.pushImMsg(ctx, userB, userA, uint32(channelIMPb2.IM_MSG_TYPE_CHANNEL_DATING_GAME_LIKE_USER_NOTIFY), "", b)
    if err != nil {
        log.Errorf("pushImSceneMsg failed to pushImMsg. cid:%d, uid:%v, toUid:%v, err:%v", cid, userB.GetUid(), userA.GetUid(), err)
    }

    log.Debugf("pushImSceneMsg cid:%d, uidA:%v, uidB:%v, channelImageUrl:%s, imImageUrl:%s, isStoreMsg:%v",
        cid, userA.GetUid(), userB.GetUid(), channelImageUrl, imImageUrl)
    return nil
}

func (m *Mgr) pushImMsg(ctx context.Context, fromUser, toUser *gaPB.UserProfile, msgType uint32, content string, data []byte) error {
    if fromUser.GetUid() == 0 || toUser.GetUid() == 0 {
        log.Errorf("pushImMsg failed .uid:%d, toUid:%d, err:uid==0", fromUser.GetUid(), toUser.GetUid())
        return nil
    }

    svrMsgID, err := m.seqGenClient.GenerateSequence(ctx, toUser.GetUid(), seqgen.NamespaceUser, seqgen.KeySvrMsgId, 1)
    if err != nil {
        log.Errorf("pushImMsg failed to generate svr msg id.uid:%v, toUid:%v, err:%v", fromUser.GetUid(), toUser.GetUid(), err)
        return err
    }

    imMsg := &timelinePB.ImMsg{
        FromId:   fromUser.GetUid(),
        FromName: fromUser.GetAccount(),
        FromNick: fromUser.GetNickname(),

        ToId:   toUser.GetUid(),
        ToName: toUser.GetAccount(),
        ToNick: toUser.GetNickname(),

        Content:       content,
        Ext:           data,
        Type:          msgType,
        ClientMsgTime: 0,
        Status:        uint32(syncPB.NewMessageSync_UN_READ),
        ServerMsgId:   uint32(svrMsgID),
        ServerMsgTime: uint32(time.Now().Unix()),
        HasAttachment: false,
        Platform:      uint32(timelinePB.Platform_UNSPECIFIED), // 没有平台限制必须显式指定
    }

    serr := immsghelper.WriteMsgToUidWithId(ctx, toUser.GetUid(), imMsg, m.seqGenClient, m.tlCli)
    if serr != nil {
        log.Errorf("pushImMsg fail to WriteMsgToUidWithId uid:%v, toUid:%v, err:%v", fromUser.GetUid(), toUser.GetUid(), serr)
        return serr
    }

    _ = notify.NotifySyncX(ctx, []uint32{fromUser.GetUid(), toUser.GetUid()}, notify.ImMsg)

    log.Debugf("pushImMsg fromUser:%v, toUser:%v, type:%d, content:%v", fromUser, toUser, msgType, content)
    return nil
}

func (m *Mgr) SendOssReport(ctx context.Context, uid, toUid, channelId, uidHeartbeat, handUidHeartbeat, sceneLevel, levelScore uint32) { //NOSONAR
    userProfile, err := m.userProfileCli.BatchGetUserProfile(ctx, []uint32{uid, toUid})
    if err != nil {
        log.ErrorWithCtx(ctx, "SendOssReport fail to get userprofile, uid: %d, toUid: %d, cid: %d ,err: %v", uid, toUid, channelId, err)
    }
    delay := false
    if userProfile[uid].GetPrivilege().GetType() == 1 || userProfile[toUid].GetPrivilege().GetType() == 1 { // 神秘人数据延迟
        data := &yswfukwdelaypb.DatingGameReportData{
            ChannelId:  channelId,
            UidA:       uid,
            UidB:       toUid,
            ValueA:     uidHeartbeat,
            ValueB:     handUidHeartbeat,
            Level:      sceneLevel,
            LevelScore: levelScore,
            CreateTime: uint32(time.Now().Unix()),
        }
        bytes, err := proto.Marshal(data)
        if err != nil {
            log.ErrorWithCtx(ctx, "SendOssReport fail to get userprofile, uid: %d, toUid: %d, cid: %d ,err: %v", uid, toUid, channelId, err)
        } else {
            _, err := m.yswfUkwDelayCli.AddYswfUkwDelayData(ctx, &yswfukwdelaypb.AddYswfUkwDelayDataReq{
                UidA:    uid,
                UidB:    toUid,
                BizType: uint32(yswfukwdelaypb.YswfBizType_DatingVal),
                Data:    bytes,
            })
            if err == nil {
                delay = true
            }
        }
    }
    if !delay {
        m.sendOssReport(uid, toUid, channelId, uidHeartbeat, handUidHeartbeat, sceneLevel, levelScore)
    }
    log.InfoWithCtx(ctx, "SendOssReport, uid: %d, toUid: %d, channelId: %d, delay: %v", uid, toUid, channelId, delay)
}

func (m *Mgr) sendOssReport(uid, toUid, channelId, uidHeartbeat, handUidHeartbeat, sceneLevel, levelScore uint32) {
    datacenter.StdReportKV(context.Background(), "************", map[string]interface{}{
        "totalDate":        time.Now().Format("2006-01-02 15:04:05"),
        "uid":              fmt.Sprintf("%d", uid),
        "handUid":          fmt.Sprintf("%d", toUid),
        "createTime":       time.Now().Format("2006-01-02 15:04:05"),
        "channelID":        fmt.Sprintf("%d", channelId),
        "uidHeartbeat":     fmt.Sprintf("%d", uidHeartbeat),
        "handUidheartbeat": fmt.Sprintf("%d", handUidHeartbeat),
        "sceneLevel":       fmt.Sprintf("%d", sceneLevel),
        "levelScore":       fmt.Sprintf("%d", levelScore),
    })
}

func (m *Mgr) sendKFKDatingSuccess(uid, toUid, channelId, uidHeartbeat, handUidHeartbeat, sceneLevel, levelScore uint32, sceneId uint32, name string) error {
    event := &kfkPB.ChannelDatingSuccessEvent{
        ChannelId:  channelId,
        UidA:       uid,
        ValueA:     uidHeartbeat,
        UidB:       toUid,
        ValueB:     handUidHeartbeat,
        Level:      sceneLevel,
        EventTime:  uint32(time.Now().Unix()),
        LevelScore: levelScore,

        SceneId:   sceneId,
        SceneName: name,
    }

    eventByte, _ := proto.Marshal(event)
    m.kafkaProduce.Produce(m.sc.DatingEvKafkaConfig.Topics, fmt.Sprintf("%d", channelId), eventByte)
    return nil
}

func (m *Mgr) sendLevelReward(ctx context.Context, channelId, uidA, uidB, score uint32) error {
    awardConf := m.bc.GetHeadwearAwardByScore(score)
    log.Infof("sendLevelReward channelId:%d, uidA:%d, uidB:%d, score:%d, award:%v", channelId, uidA, uidB, score, awardConf)
    if awardConf == nil {
        log.Infof("sendLevelReward get award config nil，uidA:%d, uidB:%d, channelId:%d", uidA, uidB, channelId)
        return nil
    }

    userMap, err := m.accountCli.GetUsersMap(ctx, []uint32{uidA, uidB})
    if err != nil {
        log.Infof("sendLevelReward GetUsersMap failed, uidA:%d, uidB:%d, channelId:%d, err:%v", uidA, uidB, channelId, err)
        return err
    }

    userA := userMap[uidA]
    aSuitID := awardConf.MaleSuit
    bSuitID := awardConf.FemaleSuitId
    if userA.GetSex() != account.Male {
        aSuitID = awardConf.FemaleSuitId
        bSuitID = awardConf.MaleSuit
    }

    m.sendReward(ctx, uidA, uidB, aSuitID)
    m.sendReward(ctx, uidB, uidA, bSuitID)
    return nil
}

func (m *Mgr) sendReward(ctx context.Context, uid, toUid, suitId uint32) error {

    pushMsg := "恭喜你牵手成功，"
    req := &pbAward.AwardReq{
        OrderId:    fmt.Sprintf("2_%d_%d", time.Now().Unix(), uid),
        TargetUid:  uid,
        BusinessId: 2,
        GiftId:     fmt.Sprintf("%d", suitId),
        GiftType:   uint32(pbAward.EGiftType_Headwear),
        HoldingDay: 0,
        OutsideTs:  uint32(time.Now().Unix()),
        HeadwearExtra: &pbAward.HeadwearAwardExtra{
            CpUid:         toUid,
            PushMsgPrefix: pushMsg,
        },
    }

    err := m.awardCli.Award(ctx, req)
    if err != nil {
        err = m.awardCli.Award(ctx, req)
        if err != nil {
            log.Errorf("Award failed, uid:%d, toUid:%d, suitId:%d, err:%v", uid, toUid, suitId, err)
            return err
        }
    }
    log.Infof("sendReward success uid:%d, toUid:%d, suitId:%d ", uid, toUid, suitId)
    return nil
}

func (m *Mgr) pushBreakingNewsV3(fromUser, toUser *gaPB.UserProfile, channelId, level uint32) error {
    ctx, cancel := context.WithTimeout(context.Background(), time.Second*5)
    defer cancel()

    mapLevelTriggerType := m.bc.GetChannelLv2BreakType()
    isWhite := m.bc.IsTestChannelId(channelId)
    if isWhite {
        mapLevelTriggerType = m.bc.GetChannelLv2BreakTypeWhiteList()
    }
    levelStr := fmt.Sprintf("%d", level)
    triggerType := mapLevelTriggerType[levelStr]
    newsId := uint32(0)
    if triggerType == 0 {
        log.Infof("pushBreakingNewsV3 level not set channelId:%d, level：%d", channelId, level)
        return nil
    }
    if triggerType == uint32(pushPb.CommBreakingNewsBaseOpt_COMMON_RICH_TEXT_NEWS) { // 如果为富文本全服, 查询等级对应的全服配置id
        if isWhite {
            newsId = m.bc.GetChannelLv2RichTextNewsIdWhiteList()[levelStr]
        } else {
            newsId = m.bc.GetChannelLv2RichTextNewsId()[levelStr]
        }
    }

    prefix := "哇~全服庆贺~ "
    content := "喜结良缘，长相厮守！"
    sceneName := "天作之合"
    var oldContent string
    if triggerType == uint32(pushPb.CommBreakingNewsBaseOpt_TOPEST_DATING_SCENE_LV6) {
        prefix = "哇~绝美爱情~ "
        content = "相爱穿梭，绝恋千年！"
        sceneName = "永恒爱恋"
    }
    if triggerType == uint32(pushPb.CommBreakingNewsBaseOpt_TOPEST_DATING_SCENE_LV7) {
        prefix = ""
        content = "甜蜜牵手，梦中の婚礼邀您前来！"
        sceneName = "梦中の婚礼"
        oldContent = "与ta的cp甜蜜牵手，梦中の婚礼邀您前来！"
    }
    if triggerType == uint32(pushPb.CommBreakingNewsBaseOpt_TOPEST_DATING_SCENE_LV_1314) {
        prefix = "给你永不失联的爱情"
        content = "恩爱秀翻天，就要给ta最完美的爱情！"
        sceneName = "永不失联的爱"
        oldContent = "恩爱秀翻天，就要给ta最完美的爱情！"
    }

    //user & channel
    breakingNewsMessage := &publicNoticePb.CommonBreakingNewsV3{
        FromUid: fromUser.GetUid(),
        FromUserInfo: &publicNoticePb.UserInfo{
            Account: fromUser.GetAccount(),
            Nick:    fromUser.GetNickname(),
        },
        TargetUid: toUser.GetUid(),
        TargetUserInfo: &publicNoticePb.UserInfo{
            Account: toUser.GetAccount(),
            Nick:    toUser.GetNickname(),
        },
        ChannelId:       channelId,
        NewsPrefix:      prefix,
        NewsContent:     content,
        IsOldDeal:       1,
        OldNewsContent:  oldContent,
        DatingSceneName: sceneName,
    }

    //breaking
    breakingNewsMessage.BreakingNewsBaseOpt = &publicNoticePb.CommBreakingNewsBaseOpt{
        TriggerType: triggerType, RollingCount: 2, RollingTime: 10,
        AnnounceScope:    uint32(pushPb.CommBreakingNewsBaseOpt_INSIDE_CHANNEL + pushPb.CommBreakingNewsBaseOpt_OUTSIDE_CHANNEL),
        JumpType:         uint32(pushPb.CommBreakingNewsBaseOpt_JUMP_ClICK_OUTSIDE),
        JumpPosition:     uint32(pushPb.CommBreakingNewsBaseOpt_JUMP_TO_CHANNEL_CLICK),
        AnnouncePosition: uint32(pushPb.CommBreakingNewsBaseOpt_UPPER),
    }
    req := &publicNoticePb.PushBreakingNewsReq{
        CommonBreakingNews: breakingNewsMessage,
    }
    if newsId != 0 {
        req.RichTextNews = &publicNoticePb.RichTextNews{
            NewsId: newsId,
        }
    }

    _, err := m.publicNoticeCli.PushBreakingNews(ctx, req)
    if err != nil {
        log.ErrorWithCtx(ctx, "PushBreakingNewsV3 failed to PushBreakingNews, req:%v, err:%v", req, err)
        return err
    }

    log.Infof("pushBreakingNewsV3 success channelId:%d, level：%d, triggerType:%d", channelId, level, triggerType)
    return nil
}
