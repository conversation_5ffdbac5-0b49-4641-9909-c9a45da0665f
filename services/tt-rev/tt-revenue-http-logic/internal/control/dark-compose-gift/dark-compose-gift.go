package dark_compose_gift

import (
	"context"
	"encoding/json"
	"golang.52tt.com/services/tt-rev/tt-revenue-http-logic/internal/models/gen-go"

	//"fmt"
	"net/http"
	"time"

	"golang.52tt.com/clients/backpack"
	"golang.52tt.com/clients/conversion"
	userPresentCli "golang.52tt.com/clients/userpresent"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/web"
	"golang.52tt.com/protocol/common/status"
	backpackPB "golang.52tt.com/protocol/services/backpacksvr"
	conversePb "golang.52tt.com/protocol/services/conversion"
	"golang.52tt.com/protocol/services/userpresent"
)

type darkComposeGiftMgr struct {
	conversionCli conversion.IClient
	backpackCli   backpack.IClient
	presentCli    userPresentCli.IClient
}

func NewDarkComposeGiftMgr(
	conversionCli conversion.IClient,
	backpackCli backpack.IClient,
	presentCli userPresentCli.IClient,
) *darkComposeGiftMgr {
	return &darkComposeGiftMgr{
		conversionCli: conversionCli,
		backpackCli:   backpackCli,
		presentCli:    presentCli,
	}
}

const rawMaterialsNotFount = "原料礼物配置不存在"

func (m *darkComposeGiftMgr) DarkComposeHandle(authInfo *web.AuthInfo, w http.ResponseWriter, r *http.Request) {
	ctx, cancel := context.WithTimeout(r.Context(), time.Second*3)
	defer cancel()

	var in api.DarkComposeGiftReq
	e := json.Unmarshal(authInfo.Body, &in)
	if e != nil {
		log.Errorf("DarkComposeHandler failed to parse request body [%s], err %+v", string(authInfo.Body), e)
		web.ServeBadReq(w)
		return
	}

	uid := authInfo.UserID
	//num := in.GetNum()
	now := time.Now()
	log.Debugf("DarkComposeHandler uid:%v, %+v", uid, in)

	// 获取合成礼物配置
	getReq := &conversePb.GetDarkComposeGiftConfByIdReq{
		Uid: uid,
		Id:  in.GetComposeId(),
	}
	getResp, Serr := m.conversionCli.GetDarkComposeGiftConfById(ctx, uid, getReq)
	if Serr != nil {
		log.Errorf("DarkComposeHandler fail to GetDarkComposeGiftConfById,  uid(%d) %+v err(%+v)", uid, in, Serr)
		_ = web.ServeAPICodeJson(w, int32(status.ErrConversionComposeGiftConfInvalid), "合成目标礼物配置不存在", nil)
		return
	}
	giftConf := getResp.GetConf()

	bgId := giftConf.GetBgId()
	if bgId == 0 || giftConf.GetGiftType() == uint32(conversePb.ComposeGiftConf_TimeLimit) &&
		(giftConf.GetUpTime() > uint32(now.Unix()) || giftConf.GetDownTime() < uint32(now.Unix())) {

		log.Errorf("DarkComposeHandler fail. compose gift is not found. uid(%d) %+v ", uid, in)
		_ = web.ServeAPICodeJson(w, int32(status.ErrConversionComposeGiftConfInvalid), "无效的合成配置", nil)
		return
	}

	// 获取原材料配置
	mReq := &conversePb.GetDarkGift2MaterialsReq{
		Uid:       uid,
		ComposeId: in.GetComposeId(),
	}
	mResp, Serr := m.conversionCli.GetDarkGift2Materials(ctx, uid, mReq)
	if Serr != nil {
		log.Errorf("DarkComposeHandler fail to GetDarkGift2Materials,  uid(%d) %+v err(%+v)", uid, in, Serr)
		_ = web.ServeAPICodeJson(w, int32(status.ErrConversionComposeGiftConfInvalid), rawMaterialsNotFount, nil)
		return
	}

	// 获取包裹配置
	bgReq := &backpackPB.GetPackageItemCfgReq{
		BgId: giftConf.GetBgId(),
	}

	bgResp, e := m.backpackCli.GetPackageItemCfg(ctx, uid, bgReq)
	if e != nil {
		log.Errorf("DarkComposeHandler fail to GetPackageItemCfg uid(%d) bgId(%d), %+v err(%+v)", uid, bgId, in, e)
		_ = web.ServeAPICodeJson(w, int32(status.ErrConversionComposeGiftConfInvalid), "合成失败，再重新试试吧~", nil)
		return
	}
	log.Debugf("DarkComposeHandler packageRespInfo:%+v", bgResp)

	// 礼物合成包裹中只能是1个礼物
	if len(bgResp.GetItemCfgList()) != 1 || bgResp.GetItemCfgList()[0].GetItemCount() != 1 {
		log.Errorf("DarkComposeHandler fail. compost package conf is not found. uid(%d) %+v ", uid, in)
		_ = web.ServeAPICodeJson(w, int32(status.ErrConversionComposeGiftConfInvalid), "包裹礼物数量配置错误", nil)
		return
	}

	// 检查包裹是否过期
	if expireTs := m.getExpireTimestamp(bgResp); expireTs != 0 && expireTs < uint32(time.Now().Unix()) {
		log.Errorf("DarkComposeHandler fail. compost package conf is expired. uid(%d) %+v ", uid, in)
		_ = web.ServeAPICodeJson(w, int32(status.ErrConversionComposeGiftConfInvalid), "包裹配置已失效", nil)
		return
	}

	composeGiftId := bgResp.GetItemCfgList()[0].GetSourceId()
	if composeGiftId != giftConf.GetGiftId() {
		log.Errorf("DarkComposeHandler fail. compost conf is err. uid(%d) %+v ", uid, in)
		_ = web.ServeAPICodeJson(w, int32(status.ErrConversionComposeGiftConfInvalid), "包裹配置错误", nil)
		return
	}

	// 获取合成礼物配置
	giftIdList := make([]uint32, 0)
	giftIdList = append(giftIdList, composeGiftId)
	presentResp, err := m.presentCli.GetPresentConfigByIdList(ctx, uid, giftIdList,
		uint32(userpresent.ConfigListTypeBitMap_CONFIG_NOT_DELETED))
	if err != nil {
		log.Errorf("ComposeHandler fail to GetPresentConfigByIdList uid(%d) %+v err(%+v)", uid, in, err)
		_ = web.ServeAPICodeJson(w, int32(err.Code()), err.Message(), nil)
		return
	}
	composeGiftInfo := presentResp.GetItemList()[0]

	// gen composeInfo Req
	darkComposeGiftInfo := &conversePb.DarkComposeGiftInfo{
		ComposeId:   in.GetComposeId(),
		BgId:        giftConf.GetBgId(),
		GiftId:      giftConf.GetGiftId(),
		GiftName:    composeGiftInfo.GetName(),
		Price:       composeGiftInfo.GetPrice(),
		GiftIconUrl: composeGiftInfo.GetIconUrl(),
		Num:         in.GetNum(),
	}

	totalMaterialPrice := uint32(0)

	if len(in.GetMixList()) == 0 {
		log.Errorf("DarkComposeHandler fail.in.GetMixList(%v). uid(%d) %+v ", uid, in.GetMixList(), in)
		_ = web.ServeAPICodeJson(w, int32(status.ErrConversionComposeGiftConfInvalid), rawMaterialsNotFount, nil)
		return
	}

	// 检查原材料信息
	materialList, totalMaterialPrice, err := m.getMaterialListAndTotalPrice(mResp.GetConfList(), in.GetMixList())
	if err != nil {
		log.Errorf("DarkComposeHandler fail. material gift conf is not found. uid(%d) %+v ", uid, in)
		_ = web.ServeAPICodeJson(w, int32(err.Code()), err.Message(), nil)
		return
	}
	// t豆消耗值校验
	if totalMaterialPrice < (darkComposeGiftInfo.GetPrice() * darkComposeGiftInfo.GetNum()) {
		log.Errorf("DarkComposeHandler fail to DarkGiftCompose uid(%d) %+v err(%s)", uid, in, "原材料总T豆值不足")
		_ = web.ServeAPICodeJson(w, int32(status.ErrConversionMaterialTotalPriceNotEnough), "所选原料价值不足以合成目标礼物", nil)
		return
	}

	darkComposeReq := &conversePb.DarkGiftComposeReq{
		Uid:             uid,
		ComposeId:       in.GetComposeId(),
		ComposeGiftInfo: darkComposeGiftInfo,
		MaterialList:    materialList,
	}

	_, err = m.conversionCli.DarkGiftCompose(ctx, uid, darkComposeReq)
	if err != nil {
		log.Errorf("DarkComposeHandler fail to DarkGiftCompose uid(%d) %+v err(%+v)", uid, in, err)
		_ = web.ServeAPICodeJson(w, int32(err.Code()), err.Message(), nil)
		return
	}

	// 数据上报？

	out := &api.ComposeGiftResp{}

	log.Debugf("DarkComposeHandler uid(%d) in(%v), out(%+v)", uid, in, out)
	_ = web.ServeAPIJson(w, out)
}

func (m *darkComposeGiftMgr) getExpireTimestamp(bgResp *backpackPB.GetPackageItemCfgResp) uint32 {
	expireTs := uint32(0)
	for _, item := range bgResp.GetItemCfgList() {
		expireTs = item.GetFinTime()
		if item.GetDynamicFinTime() != 0 {
			expireTs = uint32(time.Now().Unix() + int64(item.GetDynamicFinTime()))
		} else if item.GetMonths() > 0 {
			now := time.Now()
			t := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, time.Local)
			expireTs = uint32(t.AddDate(0, int(item.GetMonths()), 0).Unix())
		} else {
			log.Debugf("getExpireTimestamp get zero expireTs")
		}
	}
	return expireTs
}

func (m *darkComposeGiftMgr) getMaterialListAndTotalPrice(materialConfList []*conversePb.DarkComposeMaterialConf,
	costMaterialInfoList []*api.CostMaterialInfo) (materialList []*conversePb.DarkMaterialUseInfo,
	totalMaterialPrice uint32, err protocol.ServerError) {
	// 初始化材料和碎片map
	giftMaterialMap := make(map[uint32]*conversePb.DarkComposeMaterialConf)
	fragmentMaterialMap := make(map[uint32]*conversePb.DarkComposeMaterialConf)
	for _, elem := range materialConfList {
		if elem.GetItemType() == uint32(conversePb.MaterialType_GiftType) {
			giftMaterialMap[elem.GetGiftId()] = elem
		} else {
			fragmentMaterialMap[elem.GetGiftId()] = elem
		}
	}

	for _, elem := range costMaterialInfoList {

		if elem.GetGiftType() == uint32(api.MaterialType_MaterialTypeGiftType) {
			materialConf, ok := giftMaterialMap[elem.GetGiftId()]
			if !ok {
				log.Errorf("DarkComposeHandler fail. material gift conf is not found. elem.giftId:%d, giftMaterialMap:%v", elem.GetGiftId(), giftMaterialMap)
				return materialList, totalMaterialPrice, protocol.NewExactServerError(nil, status.ErrConversionComposeGiftConfInvalid, rawMaterialsNotFount)
			}

			materialList = append(materialList, &conversePb.DarkMaterialUseInfo{
				GiftId:     elem.GetGiftId(),
				Num:        elem.GetGiftNum(),
				UserItemId: elem.GetUserItemId(),
				GiftName:   materialConf.GetGiftName(),
				Price:      materialConf.GetGiftPrice(),
				GiftType:   elem.GetGiftType(),
			})
			totalMaterialPrice += elem.GetGiftNum() * materialConf.GetGiftPrice()

		} else {
			materialConf, ok := fragmentMaterialMap[elem.GetGiftId()]
			if !ok {
				log.Errorf("DarkComposeHandler fail. material gift conf is not found. elem.giftId:%d,fragmentMaterialMap:%v", elem.GetGiftId(), fragmentMaterialMap)
				return materialList, totalMaterialPrice, protocol.NewExactServerError(nil, status.ErrConversionComposeGiftConfInvalid, rawMaterialsNotFount)
			}

			materialList = append(materialList, &conversePb.DarkMaterialUseInfo{
				GiftId:     elem.GetGiftId(),
				Num:        elem.GetGiftNum(),
				UserItemId: elem.GetUserItemId(),
				GiftName:   materialConf.GetGiftName(),
				Price:      materialConf.GetGiftPrice(),
				GiftType:   elem.GetGiftType(),
			})
			totalMaterialPrice += elem.GetGiftNum() * materialConf.GetGiftPrice()
		}
	}
	return materialList, totalMaterialPrice, nil
}
