package fellow

import (
    //tyrhttp "gitlab.ttyuyin.com/avengers/tyr/core/service/http"
    "golang.52tt.com/clients/account"
    "testing"
    fellow_svr "golang.52tt.com/protocol/services/fellow-svr"
    "golang.52tt.com/pkg/web"
    "encoding/json"
    "net/http/httptest"
    "io/ioutil"
    "bytes"
    "net/http"
    "github.com/golang/mock/gomock"
    "golang.52tt.com/protocol/services/fellow_house"
    risk_mng_api "golang.52tt.com/protocol/services/risk-mng-api"
    usual_device_svr "golang.52tt.com/protocol/services/usual-device-svr"
)

func TestBuyCondition_addFellowToList(t *testing.T) {
    type fields struct {
        HouseId          uint32
        FellowTypeLimit  uint32
        FellowLevelLimit uint32
    }
    type args struct {
        isCp             bool
        fellow           *fellow_svr.FellowInfo
        user             *account.User
        selectableList   *[]*SimpleFellowInfo
        unSelectableList *[]*SimpleFellowInfo
    }
    tests := []struct {
        name   string
        fields fields
        args   args
    }{
        {
            name: "test houseId == 0",
            fields: fields{
                HouseId:          0,
                FellowTypeLimit:  0,
                FellowLevelLimit: 0,
            },
            args: args{
                isCp: true,
                fellow: &fellow_svr.FellowInfo{
                    FellowType:  1,
                    FellowLevel: 1,
                },
                user:             &account.User{},
                selectableList:   &[]*SimpleFellowInfo{},
                unSelectableList: &[]*SimpleFellowInfo{},
            },
        },
        {
            name: "test",
            fields: fields{
                HouseId:          1,
                FellowTypeLimit:  1,
                FellowLevelLimit: 1,
            },
            args: args{
                isCp: true,
                fellow: &fellow_svr.FellowInfo{
                    FellowType:  1,
                    FellowLevel: 1,
                },
                user:             &account.User{},
                selectableList:   &[]*SimpleFellowInfo{},
                unSelectableList: &[]*SimpleFellowInfo{},
            },
        },
    }
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            c := &BuyCondition{
                HouseId:          tt.fields.HouseId,
                FellowTypeLimit:  tt.fields.FellowTypeLimit,
                FellowLevelLimit: tt.fields.FellowLevelLimit,
            }
            c.addFellowToList(tt.args.isCp, tt.args.fellow, tt.args.user, tt.args.selectableList, tt.args.unSelectableList)
        })
    }
}

func TestBuyCondition_isFellowSelectable(t *testing.T) {
    type fields struct {
        HouseId          uint32
        FellowTypeLimit  uint32
        FellowLevelLimit uint32
    }
    type args struct {
        isCp   bool
        fellow *fellow_svr.FellowInfo
    }
    tests := []struct {
        name   string
        fields fields
        args   args
        want   bool
    }{
        {
            name: "test houseId == 0",
            fields: fields{
                HouseId:          0,
                FellowTypeLimit:  0,
                FellowLevelLimit: 0,
            },
            args: args{
                isCp: false,
                fellow: &fellow_svr.FellowInfo{
                    FellowType:  1,
                    FellowLevel: 1,
                },
            },
            want: false,
        },
        {
            name: "test true",
            fields: fields{
                HouseId:          1,
                FellowTypeLimit:  1,
                FellowLevelLimit: 1,
            },
            args: args{
                isCp: true,
                fellow: &fellow_svr.FellowInfo{
                    FellowType:  1,
                    FellowLevel: 1,
                },
            },
            want: true,
        },
    }
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            c := &BuyCondition{
                HouseId:          tt.fields.HouseId,
                FellowTypeLimit:  tt.fields.FellowTypeLimit,
                FellowLevelLimit: tt.fields.FellowLevelLimit,
            }
            if got := c.isFellowSelectable(tt.args.isCp, tt.args.fellow); got != tt.want {
                t.Errorf("isFellowSelectable() = %v, want %v", got, tt.want)
            }
        })
    }
}

func Test_fellowMgr_BuyFellowHouse(t *testing.T) {
    testUid := uint32(1)
    tests := []struct {
        name        string
        initFunc    func(m *fellowHelperForTest)
        getBody     func() []byte
        getAuthInfo func(body []byte) *web.AuthInfo
    }{
        {
            name: "参数校验不通过",
            initFunc: func(m *fellowHelperForTest) {
            },
            getBody: func() []byte {
                req := &BuyFellowHouseReq{
                    HouseId:             1,
                    CpUid:               0,
                    Amount:              1,
                    ClientType:          0,
                    DeviceId:            "",
                    FaceAuthResultToken: "",
                }
                body, _ := json.Marshal(req)
                return body
            },
            getAuthInfo: func(body []byte) *web.AuthInfo {
                return &web.AuthInfo{
                    Body:   body,
                    UserID: testUid,
                }
            },
        },
        {
            name: "正常购买",
            initFunc: func(m *fellowHelperForTest) {
                m.GetFellowHouseCli().EXPECT().GetFellowHouseProductById(gomock.Any(), gomock.Any()).Return(&fellow_house.GetFellowHouseProductByIdResp{
                    Cfg: &fellow_house.FellowHouseProduct{
                        HouseCfg: &fellow_house.FellowHouseCfg{
                            HouseId:       1,
                            OriginPrice:   1,
                            DiscountPrice: 1,
                        },
                        Duration:  1,
                        RankOrder: 1,
                    },
                }, nil)
                // 挚友检查
                m.GetFellowClient().EXPECT().GetFellowInfoByUid(gomock.Any(), gomock.Any()).Return(&fellow_svr.GetFellowInfoByUidResp{
                    FellowInfo: &fellow_svr.FellowInfo{
                        Uid:         1,
                        FellowLevel: 1,
                        FellowType:  1,
                    },
                }, nil)
                // 非常用设备检查
                m.GetUsualDeviceCli().EXPECT().CheckUsualDevice(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&usual_device_svr.CheckUsualDeviceResp{
                    Result: false,
                }, nil)
                m.GetUsualDeviceCli().EXPECT().GetDeviceAuthError(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
                // 未命中风控
                m.GetRiskMngApiCli().EXPECT().Check(gomock.Any(), gomock.Any()).Return(&risk_mng_api.CheckResp{
                    ErrCode: 0,
                    ErrMsg:  "",
                    ErrInfo: nil,
                }, nil)
                m.GetFellowHouseCli().EXPECT().BuyFellowHouse(gomock.Any(), gomock.Any()).Return(&fellow_house.BuyFellowHouseResp{}, nil)
            },
            getBody: func() []byte {
                req := &BuyFellowHouseReq{
                    HouseId:             1,
                    CpUid:               1,
                    Amount:              1,
                    ClientType:          0,
                    DeviceId:            "",
                    FaceAuthResultToken: "",
                }
                body, _ := json.Marshal(req)
                return body
            },
            getAuthInfo: func(body []byte) *web.AuthInfo {
                return &web.AuthInfo{
                    Body:   body,
                    UserID: testUid,
                }
            },
        },
        {
            name: "命中风控",
            initFunc: func(m *fellowHelperForTest) {
                m.GetFellowHouseCli().EXPECT().GetFellowHouseProductById(gomock.Any(), gomock.Any()).Return(&fellow_house.GetFellowHouseProductByIdResp{
                    Cfg: &fellow_house.FellowHouseProduct{
                        HouseCfg: &fellow_house.FellowHouseCfg{
                            HouseId:       1,
                            OriginPrice:   1,
                            DiscountPrice: 1,
                        },
                        Duration:  1,
                        RankOrder: 1,
                    },
                }, nil)

                // 挚友检查
                m.GetFellowClient().EXPECT().GetFellowInfoByUid(gomock.Any(), gomock.Any()).Return(&fellow_svr.GetFellowInfoByUidResp{
                    FellowInfo: &fellow_svr.FellowInfo{
                        Uid:         1,
                        FellowLevel: 1,
                        FellowType:  1,
                    },
                }, nil)

                // 非常用设备检查
                m.GetUsualDeviceCli().EXPECT().CheckUsualDevice(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&usual_device_svr.CheckUsualDeviceResp{
                    Result: false,
                }, nil)
                m.GetUsualDeviceCli().EXPECT().GetDeviceAuthError(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)

                // 命中风控
                m.GetRiskMngApiCli().EXPECT().Check(gomock.Any(), gomock.Any()).Return(&risk_mng_api.CheckResp{
                    ErrCode: -1,
                    ErrMsg:  "111",
                    ErrInfo: []byte{11},
                }, nil)
            },
            getBody: func() []byte {
                req := &BuyFellowHouseReq{
                    HouseId:             1,
                    CpUid:               1,
                    Amount:              1,
                    ClientType:          0,
                    DeviceId:            "",
                    FaceAuthResultToken: "",
                }
                body, _ := json.Marshal(req)
                return body
            },
            getAuthInfo: func(body []byte) *web.AuthInfo {
                return &web.AuthInfo{
                    Body:   body,
                    UserID: testUid,
                }
            },
        },
    }
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            m := newFellowHelperForTest(t)

            if tt.initFunc != nil {
                tt.initFunc(m)
            }

            handler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
                r.Body = ioutil.NopCloser(bytes.NewBuffer(tt.getBody()))
                m.BuyFellowHouse(tt.getAuthInfo(tt.getBody()), w, r)
            })
            rr := httptest.NewRecorder()
            req, err := http.NewRequest("POST", "/tt-revenue-http-logic/fellow-house/buy_fellow_house", nil)
            if err != nil {
                t.Fatal(err)
            }
            handler.ServeHTTP(rr, req)

        })
    }
}

func Test_fellowMgr_GetFellowHouseProductList(t *testing.T) {
    product1 := &fellow_house.FellowHouseProduct{
        HouseCfg: &fellow_house.FellowHouseCfg{
            HouseId:       1,
            OriginPrice:   1,
            DiscountPrice: 1,
        },
        Duration:  1,
        RankOrder: 100,
    }

    product2 := &fellow_house.FellowHouseProduct{
        HouseCfg: &fellow_house.FellowHouseCfg{
            HouseId:       2,
            OriginPrice:   2,
            DiscountPrice: 2,
        },
        Duration:  2,
        RankOrder: 2,
    }

    houseResp := &fellow_house.GetFellowHouseProductListResp{
        ProductList: []*fellow_house.FellowHouseProduct{
            product1, product2,
        },
    }

    //wantResp := &GetFellowHouseProductListResp{
    //    HouseList: []*HouseProduct{
    //        productPb2HouseProduct(product2),
    //        productPb2HouseProduct(product1),
    //    },
    //}

    testUid := uint32(1)
    tests := []struct {
        name        string
        initFunc    func(m *fellowHelperForTest)
        getBody     func() []byte
        getAuthInfo func(body []byte) *web.AuthInfo
    }{
        {
            name: "common",
            initFunc: func(m *fellowHelperForTest) {
                m.GetFellowHouseCli().EXPECT().GetFellowHouseProductList(gomock.Any(), gomock.Any()).Return(houseResp, nil)
            },
            getBody: func() []byte {
                req := &GetFellowHouseProductListReq{}
                body, _ := json.Marshal(req)
                return body
            },
            getAuthInfo: func(body []byte) *web.AuthInfo {
                return &web.AuthInfo{
                    Body:   body,
                    UserID: testUid,
                }
            },
        },
    }
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            m := newFellowHelperForTest(t)

            if tt.initFunc != nil {
                tt.initFunc(m)
            }

            handler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
                r.Body = ioutil.NopCloser(bytes.NewBuffer(tt.getBody()))
                m.GetFellowHouseProductList(tt.getAuthInfo(tt.getBody()), w, r)
            })
            rr := httptest.NewRecorder()
            req, err := http.NewRequest("POST", "/tt-revenue-http-logic/fellow-house/get_house_list", nil)
            if err != nil {
                t.Fatal(err)
            }
            handler.ServeHTTP(rr, req)

        })
    }
}

func Test_fellowMgr_GetUserFellowList(t *testing.T) {

    testUid := uint32(1)
    tests := []struct {
        name        string
        initFunc    func(m *fellowHelperForTest)
        getBody     func() []byte
        getAuthInfo func(body []byte) *web.AuthInfo
    }{
        {
            name: "common",
            initFunc: func(m *fellowHelperForTest) {
                m.GetFellowClient().EXPECT().GetFellowList(gomock.Any(), gomock.Any()).Return(&fellow_svr.GetFellowListResp{
                    Uid: int64(testUid),
                    UniqueFellow: &fellow_svr.FellowInfo{
                        Uid:         int64(testUid) + 1,
                        FellowLevel: 1,
                        FellowType:  1,
                    },
                    MultiFellow: []*fellow_svr.FellowInfo{
                        {
                            Uid:         int64(testUid) + 2,
                            FellowLevel: 1,
                            FellowType:  1,
                        },
                    },
                }, nil)
                m.GetAccountCli().EXPECT().GetUsersMap(gomock.Any(), gomock.Any()).Return(map[uint32]*account.User{
                    testUid + 1: {},
                    testUid + 2: {},
                }, nil)
                m.GetFellowHouseCli().EXPECT().GetFellowHouseProductById(gomock.Any(), gomock.Any()).Return(&fellow_house.GetFellowHouseProductByIdResp{
                    Cfg: &fellow_house.FellowHouseProduct{
                        HouseCfg: &fellow_house.FellowHouseCfg{
                            HouseId:       1,
                            OriginPrice:   1,
                            DiscountPrice: 1,
                        },
                        Duration:  1,
                        RankOrder: 100,
                    }}, nil)
            },

            getBody: func() []byte {
                req := &GetUserFellowListReq{
                    1,
                }
                body, _ := json.Marshal(req)
                return body
            },
            getAuthInfo: func(body []byte) *web.AuthInfo {
                return &web.AuthInfo{
                    Body:   body,
                    UserID: testUid,
                }
            },
        },
    }
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            m := newFellowHelperForTest(t)

            if tt.initFunc != nil {
                tt.initFunc(m)
            }

            handler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
                r.Body = ioutil.NopCloser(bytes.NewBuffer(tt.getBody()))
                m.GetUserFellowList(tt.getAuthInfo(tt.getBody()), w, r)
            })
            rr := httptest.NewRecorder()
            req, err := http.NewRequest("POST", "/tt-revenue-http-logic/fellow-house/get_user_fellow_list", nil)
            if err != nil {
                t.Fatal(err)
            }
            handler.ServeHTTP(rr, req)

        })
    }
}

func Test_fellowMgr_GetUserTBeanBalance(t *testing.T) {

    testUid := uint32(1)
    tests := []struct {
        name        string
        initFunc    func(m *fellowHelperForTest)
        getBody     func() []byte
        getAuthInfo func(body []byte) *web.AuthInfo
    }{
        {
            name: "common",
            initFunc: func(m *fellowHelperForTest) {
                m.GetTbeanCli().EXPECT().GetBalance(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(int32(100), nil)
            },

            getBody: func() []byte {
                req := &GetUserTBeanBalanceReq{}
                body, _ := json.Marshal(req)
                return body
            },
            getAuthInfo: func(body []byte) *web.AuthInfo {
                return &web.AuthInfo{
                    Body:   body,
                    UserID: testUid,
                }
            },
        },
    }
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            m := newFellowHelperForTest(t)

            if tt.initFunc != nil {
                tt.initFunc(m)
            }

            handler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
                r.Body = ioutil.NopCloser(bytes.NewBuffer(tt.getBody()))
                m.GetUserTBeanBalance(tt.getAuthInfo(tt.getBody()), w, r)
            })
            rr := httptest.NewRecorder()
            req, err := http.NewRequest("POST", "/tt-revenue-http-logic/fellow-house/get_bean_balance", nil)
            if err != nil {
                t.Fatal(err)
            }
            handler.ServeHTTP(rr, req)

        })
    }
}
