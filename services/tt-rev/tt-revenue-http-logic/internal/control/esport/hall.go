package esport

import (
    "context"
    "encoding/json"
    "errors"
    "fmt"
    "gitlab.ttyuyin.com/avengers/tyr/compatible/proto"
    accountCli "golang.52tt.com/clients/account"
    channelOL "golang.52tt.com/clients/channelol"
    "golang.52tt.com/clients/guild"
    headImageCli "golang.52tt.com/clients/headimage"
    im_api "golang.52tt.com/clients/im-api"
    presenceV2 "golang.52tt.com/clients/presence/v2"
    "golang.52tt.com/clients/seqgen/v2"
    timeline "golang.52tt.com/clients/timeline"
    protoGrpc "golang.52tt.com/pkg/protocol/grpc"
    "golang.52tt.com/protocol/app/esport_logic"
    imPB "golang.52tt.com/protocol/app/im"
    channel_go "golang.52tt.com/protocol/services/channel-go"
    pb "golang.52tt.com/protocol/services/esport-customer"
    "golang.52tt.com/protocol/services/esport_godlevel"
    "golang.52tt.com/protocol/services/esport_internal"
    "golang.52tt.com/protocol/services/esport_rcmd"
    "golang.52tt.com/services/tt-rev/common/goroutineex"
    "golang.52tt.com/services/tt-rev/esport/common/collection/transform"
    trade_im_notify "golang.52tt.com/services/tt-rev/esport/common/trade-im-notify"
    "net/http"
    "sort"
    "strconv"
    "strings"
    "sync"
    "time"

    tyr_http "gitlab.ttyuyin.com/avengers/tyr/core/service/http"
    HeadDynamicImage "golang.52tt.com/clients/head-dynamic-image-logic"
    friendShipCli "golang.52tt.com/clients/ugc/friendship"
    userOnline "golang.52tt.com/clients/user-online"
    userprofileapi "golang.52tt.com/clients/user-profile-api"
    "golang.52tt.com/pkg/log"
    "golang.52tt.com/pkg/mapreduce"
    "golang.52tt.com/pkg/protocol"
    "golang.52tt.com/pkg/web"
    pbApp "golang.52tt.com/protocol/app"
    "golang.52tt.com/protocol/common/status"
    esport_customer "golang.52tt.com/protocol/services/esport-customer"
    esport_skill "golang.52tt.com/protocol/services/esport-skill"
    esport_statistics "golang.52tt.com/protocol/services/esport-statistics"
    esport_trade "golang.52tt.com/protocol/services/esport-trade"
    esport_wechat "golang.52tt.com/protocol/services/esport-wechat"
    "golang.52tt.com/protocol/services/esport_hall"
    "golang.52tt.com/protocol/services/esport_role"
    "golang.52tt.com/services/tt-rev/tt-revenue-http-logic/internal/common"
    "golang.52tt.com/services/tt-rev/tt-revenue-http-logic/internal/conf"
    esport_http "golang.52tt.com/services/tt-rev/tt-revenue-http-logic/internal/models/esport"
)

const (
    GuaranteeWinSectionName = "包赢承诺"
)

type EsportHallMgr struct {
    esportHallCLi       esport_hall.EsportHallClient
    esportSkillCli      esport_skill.EsportSkillClient
    esportTradeCli      esport_trade.EsportTradeClient
    esportRoleCli       esport_role.ESportRoleClient
    esportStatCli       esport_statistics.EsportStatisticsClient
    esportInternalCli   esport_internal.EsportInternalClient
    customerCli         esport_customer.CustomerServiceClient
    headDynamicImageCli HeadDynamicImage.IClient
    userProfileCli      userprofileapi.IClient
    friendShipCli       friendShipCli.IClient
    userOnlineCli       userOnline.IClient
    esportWechatCli     esport_wechat.EsportWechatClient
    godlevel            esport_godlevel.ESportGodLevelServiceClient
    bc                  conf.BusinessConfApi
    presenceV2Cli       presenceV2.IClient
    headImageCli        headImageCli.IClient
    esportImCli         trade_im_notify.ITradeImNotify
    accountCli          accountCli.IClient
    userESService       UserESService
    guildCli            guild.IClient
    esportRcmdCli       esport_rcmd.EsportRcmdServiceClient
    channelOlCli        *channelOL.Client
    channelGoCli        channel_go.ChannelGoClient
}

func (mgr *EsportHallMgr) Register(cfg *conf.ServiceConfig, router *tyr_http.Router) {
    child := router.Child("/esport/hall")
    child.POST("/get_coach_detail", common.HandleWrapperG(cfg, mgr.GetCoachDetailRequest))
    child.POST("/getRecommendCoachList", common.HandleWrapperG(cfg, mgr.GetRecommendCoachList))
    child.POST("/get_new_customer_discount_info", common.HandleWrapperG(cfg, mgr.GetNewCustomerDiscountInfo))
    child.POST("/set_new_customer_switch", common.HandleWrapperG(cfg, mgr.SetNewCustomerSwitch))
    child.POST("/refresh_discount_info", common.HandleWrapperG(cfg, mgr.RefreshDiscountInfo))
    child.POST("/get_cur_pricing_info", common.HandleWrapperG(cfg, mgr.GetCurPricingInfo))
    child.POST("/set_pricing_level", common.HandleWrapperG(cfg, mgr.SetPricingLevel))
    child.POST("/get_applicable_labels", common.HandleWrapperG(cfg, mgr.GetApplicableLabels))
    child.POST("/set_label_price_switch", common.HandleWrapperG(cfg, mgr.SetLabelPriceSwitch))

    child.POST("/customer_hosting_operation", common.HandleWrapperG(cfg, mgr.CustomerHostingOperation))
    child.POST("/get_hosting_status", common.HandleWrapperG(cfg, mgr.GetHostingStatus))
    child.POST("/contact_customer_service", common.HandleWrapperG(cfg, mgr.ContactCustomerService))
    child.POST("/send_coach_skill_card", common.HandleWrapperG(cfg, mgr.SendCoachSkillCard))
    child.POST("/get_coach_list_for_customer", common.HandleWrapperG(cfg, mgr.GetCoachListForCustomer))
    child.POST("/get_first_round_discount_info", common.HandleWrapperG(cfg, mgr.GetFirstRoundDiscountInfo))
    child.POST("/set_first_round_switch", common.HandleWrapperG(cfg, mgr.SetFirstRoundSwitch))
    child.POST("/get_first_round_discount_game_list", common.HandleWrapperG(cfg, mgr.GetFirstRoundDiscountGameList))
    child.POST("/report_expose_coach", common.HandleWrapperG(cfg, mgr.ReportExposeCoach))
    child.POST("/get_activity_game_property", common.HandleWrapperG(cfg, mgr.GetActivityGameProperty))
    // 待改造
    child.POST("/get_receive_time_frame", common.AuthHandleInterceptor(cfg, mgr.GetReceiveTimeFrame))
    child.POST("/set_receive_time_frame", common.AuthHandleInterceptor(cfg, mgr.SetReceiveTimeFrame))
    child.POST("/set_skill_receive_switch", common.AuthHandleInterceptor(cfg, mgr.SetSkillReceiveSwitch))
    child.POST("/set_skill_price", common.AuthHandleInterceptor(cfg, mgr.SetSkillPrice))
    child.POST("/set_quick_receive_switch", common.AuthHandleInterceptor(cfg, mgr.SetQuickReceiveSwitch))
    child.POST("/get_quick_receive_switch", common.AuthHandleInterceptor(cfg, mgr.GetQuickReceiveSwitch))

}

func NewEsportHallMgr(ctx context.Context, bc conf.BusinessConfApi) *EsportHallMgr {
    esportHallCli, err := esport_hall.NewClient(ctx)
    if err != nil {
        log.Errorf("NewEsportHallMgr err: %v", err)
        return nil
    }
    esportSkillCli, err := esport_skill.NewClient(ctx)
    if err != nil {
        log.Errorf("NewEsportHallMgr err: %v", err)
        return nil
    }

    esporTradeCli, err := esport_trade.NewClient(ctx)
    if err != nil {
        log.Errorf("NewEsportHallMgr err: %v", err)
        return nil
    }

    esportRoleCli, err := esport_role.NewClient(ctx)
    if err != nil {
        log.Errorf("NewEsportHallMgr err: %v", err)
        return nil
    }

    headDynamicImageCli := HeadDynamicImage.NewIClient()

    userProfileCli := userprofileapi.NewIClient()

    friendshipCli := friendShipCli.NewIClient()

    esportStatCli, err := esport_statistics.NewClient(ctx)
    if err != nil {
        log.Errorf("NewEsportHallMgr err: %v", err)
        return nil
    }

    userOnlineCli := userOnline.NewIClient()

    golevelCli, err := esport_godlevel.NewClient(ctx)
    if err != nil {
        log.Errorf("NewEsportHallMgr esport_godlevel.NewClient err: %v", err)
        return nil
    }

    esportWechatCli, _ := esport_wechat.NewClient(ctx)
    esportInternalCli, _ := esport_internal.NewClient(ctx)

    presenceV2Cli, err := presenceV2.NewClient()
    if err != nil {
        log.Errorf("NewEsportHallMgr presenceV2.NewClient err: %v", err)
        return nil
    }

    headImageClient := headImageCli.NewIClient()

    customerCli, err := esport_customer.NewClient(ctx)
    if err != nil {
        log.Errorf("NewEsportHallMgr esport_customer.NewClient err: %v", err)
        return nil
    }
    accountCli := accountCli.NewIClient()
    timelineCli := timeline.NewClient()
    seqGenClient := seqgen.NewIClient()
    imApiClient := im_api.NewIClient()
    esportImCli := trade_im_notify.NewTradeImNotify(accountCli, seqGenClient, timelineCli, imApiClient)
    esportRcmdCli, err := esport_rcmd.NewClient(ctx)
    if err != nil {
        log.Errorf("NewEsportHallMgr esport_rcmd.NewClient err: %v", err)
        return nil
    }

    userESService := NewUserESService()

    guildCli := guild.NewIClient()

    channelOlCli := channelOL.NewClient()
    channelGoCli, err := channel_go.NewClient(ctx)
    if err != nil {
        log.Errorf("NewEsportHallMgr channel_go.NewClient err: %v", err)
    }

    return &EsportHallMgr{
        esportHallCLi:       esportHallCli,
        esportSkillCli:      esportSkillCli,
        esportTradeCli:      esporTradeCli,
        esportRoleCli:       esportRoleCli,
        esportStatCli:       esportStatCli,
        esportInternalCli:   esportInternalCli,
        customerCli:         customerCli,
        headDynamicImageCli: headDynamicImageCli,
        userProfileCli:      userProfileCli,
        friendShipCli:       friendshipCli,
        userOnlineCli:       userOnlineCli,
        esportWechatCli:     esportWechatCli,
        godlevel:            golevelCli,
        bc:                  bc,
        presenceV2Cli:       presenceV2Cli,
        headImageCli:        headImageClient,
        esportImCli:         esportImCli,
        accountCli:          accountCli,
        userESService:       userESService,
        guildCli:            guildCli,
        esportRcmdCli:       esportRcmdCli,
        channelOlCli:        channelOlCli,
        channelGoCli:        channelGoCli,
    }
}

func (mgr *EsportHallMgr) GetReceiveTimeFrame(authInfo *web.AuthInfo, w http.ResponseWriter, r *http.Request) {
    ctx, cancel := context.WithTimeout(r.Context(), 5*time.Second)
    defer cancel()
    log.Infof("GetReceiveTimeFrame :%v", r)

    rs, err := mgr.esportHallCLi.GetReceiveTimeFrame(ctx, &esport_hall.GetReceiveTimeFrameRequest{
        Uid: authInfo.UserID,
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "GetReceiveTimeFrame err: %v", err)
        _ = web.ServeAPICodeJson(w, status.ErrSys, err.Error(), nil)
    }

    resp := &esport_http.GetReceiveTimeFrameResponse{
        StartTime: rs.GetStartTime(),
        EndTime:   rs.GetEndTime(),
        DayOfWeek: rs.GetDayOfWeek(),
    }

    log.Debugf("GetReceiveTimeFrame resp: %+v", resp)
    _ = web.ServeAPICodeJson(w, status.Success, "", resp)
}

func (mgr *EsportHallMgr) SetReceiveTimeFrame(authInfo *web.AuthInfo, w http.ResponseWriter, r *http.Request) {
    ctx, cancel := context.WithTimeout(r.Context(), 5*time.Second)
    defer cancel()
    log.Infof("SetReceiveTimeFrame :%v", r)

    var in esport_http.SetReceiveTimeFrameRequest
    err := json.Unmarshal(authInfo.Body, &in)
    if err != nil {
        e := protocol.ToServerError(err)
        log.Errorf("SetReceiveTimeFrame Failed to parse request  body(%s)  err(%v)", string(authInfo.Body), err)
        _ = web.ServeAPICodeJson(w, int32(e.Code()), e.Message(), nil)
        return
    }

    _, err = mgr.esportHallCLi.SetReceiveTimeFrame(ctx, &esport_hall.SetReceiveTimeFrameRequest{
        Uid:       authInfo.UserID,
        StartTime: in.GetStartTime(),
        EndTime:   in.GetEndTime(),
        DayOfWeek: in.GetDayOfWeek(),
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "SetReceiveTimeFrame err: %v", err)
        _ = web.ServeAPICodeJson(w, status.ErrSys, err.Error(), nil)
    }

    _ = web.ServeAPICodeJson(w, status.Success, "", nil)
}

func (mgr *EsportHallMgr) SetSkillReceiveSwitch(authInfo *web.AuthInfo, w http.ResponseWriter, r *http.Request) {
    ctx, cancel := context.WithTimeout(r.Context(), 5*time.Second)
    defer cancel()
    log.Infof("SetSkillReceiveSwitch :%v", r)

    var in esport_http.SetSkillReceiveSwitchRequest
    err := json.Unmarshal(authInfo.Body, &in)
    if err != nil {
        e := protocol.ToServerError(err)
        log.Errorf("SetSkillReceiveSwitch Failed to parse request  body(%s)  err(%v)", string(authInfo.Body), err)
        _ = web.ServeAPICodeJson(w, int32(e.Code()), e.Message(), nil)
        return
    }

    // 技能冻结时，无法打开开关
    freezeRsp, err := mgr.esportSkillCli.GetUserSkillFreezeStatus(ctx, &esport_skill.GetUserSkillFreezeStatusRequest{Uid: authInfo.UserID})
    if err != nil {
        e := protocol.ToServerError(err)
        log.ErrorWithCtx(ctx, "SetSkillReceiveSwitch GetUserSkillFreezeStatus err: %v", err)
        _ = web.ServeAPICodeJson(w, int32(e.Code()), e.Error(), nil)
        return
    }
    freezeMap := freezeRsp.GetGameStatusMap()
    if freezeMap[in.GetSkillId()] != nil && in.GetSwitch() {
        log.InfoWithCtx(ctx, "SetSkillReceiveSwitch skill freezing, uid: %d, req: %v,  ", authInfo.UserID, in)
        _ = web.ServeAPICodeJson(w, status.ErrEsportSkillFreeze, "您的技能已被冻结，请在冻结时间结束后再开启", nil)
        return
    }

    // 有技能认证的人，才需要开启强制包赢
    hasSkill, err := mgr.checkSkillLabel(ctx, authInfo.UserID, in.GetSkillId())
    if err != nil {
        e := protocol.ToServerError(err)
        log.ErrorWithCtx(ctx, "SetSkillReceiveSwitch checkSkillLabel err: %v", err)
        _ = web.ServeAPICodeJson(w, int32(e.Code()), e.Error(), nil)
        return
    }

    // 切换为打开并且必须包赢时, 且有技能认证时，需要判断是否有开启包赢
    if in.GetSwitch() && mgr.bc.GetMustGuaranteeWin() && hasSkill {
        gameDetail, err := mgr.esportSkillCli.GetGameDetailById(ctx, &esport_skill.GetGameDetailByIdRequest{
            GameId: in.GetSkillId(),
        })
        if err != nil {
            e := protocol.ToServerError(err)
            log.ErrorWithCtx(ctx, "SetSkillReceiveSwitch GetGameDetailById err: %v", err)
            _ = web.ServeAPICodeJson(w, int32(e.Code()), e.Error(), nil)
            return
        }
        hasGuaranteeConfig := false
        for _, section := range gameDetail.GetConfig().GetGameInformationList() {
            if section.SectionName == guaranteeSectionName {
                hasGuaranteeConfig = true
                break
            }
        }
        // 游戏带着包赢配置项的需要开启包赢
        if hasGuaranteeConfig {
            resp, err := mgr.esportSkillCli.GetUserSkillByGameId(ctx, &esport_skill.GetUserSkillByGameIdRequest{
                Uid:    authInfo.UserID,
                GameId: in.GetSkillId(),
            })
            if err != nil {
                e := protocol.ToServerError(err)
                log.ErrorWithCtx(ctx, "SetSkillReceiveSwitch GetUserSkillByGameId err: %v", err)
                _ = web.ServeAPICodeJson(w, int32(e.Code()), e.Message(), nil)
                return
            }
            if !resp.GetCurrentSkill().GetIsGuaranteeWin() {
                log.InfoWithCtx(ctx, "SetSkillReceiveSwitch switch off, uid: %d, req: %v,  ", authInfo.UserID, in)
                _ = web.ServeAPICodeJson(w, status.ErrEsportHallGuaranteeSwitchNeedOn, "请先开启包赢", nil)
                return
            }
        } else {
            log.DebugWithCtx(ctx, "SetSkillReceiveSwitch no guarantee config, uid: %d, req: %v,  ", authInfo.UserID, in)
        }
    }

    _, err = mgr.esportHallCLi.SetSkillReceiveSwitch(ctx, &esport_hall.SetSkillReceiveSwitchRequest{
        Uid:     authInfo.UserID,
        SkillId: in.GetSkillId(),
        Switch:  in.GetSwitch(),
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "SetSkillReceiveSwitch err: %v", err)
        _ = web.ServeAPICodeJson(w, status.ErrSys, err.Error(), nil)
        return
    }

    _ = web.ServeAPICodeJson(w, status.Success, "", nil)
}

func (mgr *EsportHallMgr) checkSkillLabel(ctx context.Context, coachUid uint32, gameId uint32) (bool, error) {
    coachLabelResp, err := mgr.esportSkillCli.BatchGetCoachLabelsForGame(ctx, &esport_skill.BatchGetCoachLabelsForGameRequest{
        GameId:   gameId,
        CoachIds: []uint32{coachUid},
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "BatchGetCoachLabelsForGame err: %v", err)
        return false, err
    }

    if len(coachLabelResp.GetLabelList()) > 0 {
        coachLabelInfo := coachLabelResp.GetLabelList()[0]
        skillLabelOriginList, ok := coachLabelInfo.GetLabelMap()[uint32(esport_skill.LabelType_LABEL_TYPE_SKILL)]
        if ok && len(skillLabelOriginList.GetLabelList()) > 0 {
            return true, nil
        }
    }

    return false, nil
}

func (mgr *EsportHallMgr) SetSkillPrice(authInfo *web.AuthInfo, w http.ResponseWriter, r *http.Request) {
    ctx, cancel := context.WithTimeout(r.Context(), 5*time.Second)
    defer cancel()
    log.Infof("SetSkillPrice :%v", r)

    var in esport_http.SetSkillPriceRequest
    err := json.Unmarshal(authInfo.Body, &in)
    if err != nil {
        e := protocol.ToServerError(err)
        log.Errorf("SetSkillPrice Failed to parse request  body(%s)  err(%v)", string(authInfo.Body), err)
        _ = web.ServeAPICodeJson(w, int32(e.Code()), e.Message(), nil)
        return
    }

    allSkillList, err := mgr.esportHallCLi.GetAllSkillList(ctx, &esport_hall.GetAllSkillListRequest{
        Uid: authInfo.UserID,
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "SetSkillPrice GetAllSkillList uid: %d, req: %v, err: %v", authInfo.UserID, in, err)
        _ = web.ServeAPICodeJson(w, status.ErrSys, err.Error(), nil)
        return
    }
    targetSkill := &esport_hall.SkillProduct{}
    for _, item := range allSkillList.GetProductList() {
        if item.GetGameId() == in.GetSkillId() {
            targetSkill = item
            break
        }
    }
    if targetSkill.GetGameId() == 0 {
        log.ErrorWithCtx(ctx, "SetSkillPrice GetAllSkillList uid: %d, req: %v, err: %v", authInfo.UserID, in, err)
        _ = web.ServeAPICodeJson(w, status.ErrSys, "未找到此技能", nil)
        return
    }

    // 限制价格
    gameConfig, err := mgr.esportSkillCli.GetGameDetailById(ctx, &esport_skill.GetGameDetailByIdRequest{
        GameId: in.GetSkillId(),
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "SetSkillPrice GetGameDetailById uid: %d, req: %v, err: %v", authInfo.UserID, in, err)
        _ = web.ServeAPICodeJson(w, status.ErrSys, err.Error(), nil)
        return
    }
    priceInfo := buildPriceInfo(targetSkill, gameConfig.GetConfig(), gameConfig.GetMinimumPrice())
    if priceInfo.GetPrice() == in.GetPriceVal() {
        _ = web.ServeAPICodeJson(w, status.Success, "", nil)
        return
    }

    if in.GetPriceVal() < gameConfig.GetMinimumPrice() || in.GetPriceVal() > priceInfo.GetMaxPrice() {
        _ = web.ServeAPICodeJson(w, status.ErrSys, "超出合理价格范围", nil)
        return
    }

    _, err = mgr.esportHallCLi.SetSkillPrice(ctx, &esport_hall.SetSkillPriceRequest{
        Uid:     authInfo.UserID,
        SkillId: in.GetSkillId(),
        Price:   in.GetPriceVal(),
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "SetSkillPrice err: %v", err)
        _ = web.ServeAPICodeJson(w, status.ErrSys, err.Error(), nil)
        return
    }

    _ = web.ServeAPICodeJson(w, status.Success, "", nil)

}

func (mgr *EsportHallMgr) GetCoachDetailRequest(ctx context.Context, in *esport_http.GetCoachDetailRequest) (*esport_http.GetCoachDetailResponse, error) {
    svrInfo, ok := protoGrpc.ServiceInfoFromContext(ctx)
    if !ok {
        log.ErrorWithCtx(ctx, "GetCoachDetailRequest ServiceInfoFromContext fail. in:%+v", in)
        return nil, fmt.Errorf("参数异常")
    }
    uid := svrInfo.UserID
    isHost := true
    if in.GetCoachUid() != 0 {
        uid = in.GetCoachUid()
        isHost = false
    }

    var (
        userProfile      *pbApp.UserProfile
        userSkillResp    *esport_skill.GetUserSkillByGameIdResponse
        gameCfgResp      *esport_skill.GetGameDetailByIdResponse
        coachScoreResp   *esport_trade.GetEvaluateSummaryResponse
        coachLabelResp   *esport_role.BatchGetCoachLabelResponse
        sp               *esport_hall.GetSkillProductByUidGameIdResponse
        isFollow         bool
        serviceCnt       uint32
        onlineType       uint32
        newCoachLabel    []string
        skillLabel       []string
        skillLabelUrl    []string
        famousPlayer     bool
        priceMap         map[uint32]*esport_internal.PriceInfo
        orderHintText    string
        coachChannelInfo *esport_http.CoachChannelInfo
    )
    err := concurrentExecWithOrderlyErr(
        func() error {
            var err error
            userProfile, err = mgr.userProfileCli.GetUserProfileV2(ctx, uid, false)
            if err != nil {
                log.ErrorWithCtx(ctx, "GetCoachDetailRequest GetUserProfileV2 err: %v", err)
            }
            return err
        },
        func() error {
            var err error
            userSkillResp, err = mgr.esportSkillCli.GetUserSkillByGameId(ctx, &esport_skill.GetUserSkillByGameIdRequest{
                Uid:    uid,
                GameId: in.GetGameId(),
                IsHost: isHost,
            })
            if err != nil {
                log.ErrorWithCtx(ctx, "GetCoachDetailRequest GetUserSkillByGameId err: %v", err)
            }
            return err
        },
        func() error {
            var err error
            gameCfgResp, err = mgr.esportSkillCli.GetGameDetailById(ctx, &esport_skill.GetGameDetailByIdRequest{
                GameId: in.GetGameId(),
            })
            if err != nil {
                log.ErrorWithCtx(ctx, "GetCoachDetailRequest GetGameDetailById err: %v", err)
            }
            return err
        },
        func() error {
            var err error
            coachScoreResp, err = mgr.esportTradeCli.GetEvaluateSummary(ctx, &esport_trade.GetEvaluateSummaryRequest{
                Uid:    uid,
                GameId: in.GetGameId(),
            })
            if err != nil {
                log.ErrorWithCtx(ctx, "GetCoachDetailRequest GetEvaluateSummary err: %v", err)
            }
            return nil
        },
        func() error {
            var err error
            coachLabelResp, err = mgr.esportRoleCli.BatchGetCoachLabel(ctx, &esport_role.BatchGetCoachLabelRequest{
                UidList: []uint32{uid},
            })
            if err != nil {
                log.ErrorWithCtx(ctx, "GetCoachDetailRequest BatchGetCoachLabel err: %v", err)
            }
            return err
        },
        func() error {
            var err error
            sp, err = mgr.esportHallCLi.GetSkillProductByUidGameId(ctx, &esport_hall.GetSkillProductByUidGameIdRequest{
                Uid:    uid,
                GameId: in.GetGameId(),
            })
            if err != nil {
                log.ErrorWithCtx(ctx, "GetCoachDetailRequest GetSkillProductByUidGameId err: %v", err)
            }
            return nil
        },
        func() error {
            if isHost {
                return nil // 主态不显示关注按钮
            }
            a2b, _, err := mgr.friendShipCli.GetBiFollowing(ctx, svrInfo.UserID, uid, true)
            if err != nil {
                log.ErrorWithCtx(ctx, "GetCoachDetailRequest GetBiFollowing err: %v", err)
                return err
            }

            if a2b != nil && !a2b.Dropped {
                isFollow = true
            }
            return nil
        },
        func() error {
            coachStatResp, err := mgr.esportStatCli.GetCoachStatistics(ctx, &esport_statistics.GetCoachStatisticsRequest{
                Uid:    uid,
                GameId: in.GetGameId(),
            })
            if err != nil {
                log.ErrorWithCtx(ctx, "GetCoachDetailRequest GetCoachStatistics err: %v", err)
            }
            serviceCnt = coachStatResp.GetData().GetOrderNum()
            return nil
        },
        func() error {
            if mgr.isUserOnline(ctx, uid) {
                onlineType = 1
            }
            return nil
        },
        func() error {
            newCoachLabel, skillLabel, skillLabelUrl, famousPlayer = mgr.getCoachLabelInfo(ctx, in.GetGameId(), uid)
            return nil
        },
        func() error {
            pricingInfoResponse, err := mgr.esportInternalCli.BatchGetSkillPricingInfo(ctx, &esport_internal.BatchGetSkillPricingInfoRequest{
                Uid:         svrInfo.UserID,
                CoachUid:    uid,
                GameId:      []uint32{in.GetGameId()},
                BuyAmount:   1,
                QueryOption: &esport_internal.CouponQueryOption{DoNotCheckVersion: true},
            })
            if err != nil {
                log.ErrorWithCtx(ctx, "GetCoachDetailRequest BatchGetSkillPricingInfo err: %v", err)
            } else {
                priceMap = pricingInfoResponse.GetPriceMap()
            }
            return nil
        },
        func() error {
            if isHost {
                return nil
            }
            // 检查完成订单是否能自动发优惠券
            checkRsp, err := mgr.esportTradeCli.AutoGrantCoupon(ctx, &esport_trade.AutoGrantCouponRequest{
                Uid:      svrInfo.UserID,
                PreCheck: true,
                CoachUid: in.CoachUid,
            })
            if err != nil {
                log.WarnWithCtx(ctx, "GetCoachDetailRequest AutoGrantCoupon err:%v", err)
            } else {
                if checkRsp.CanGrant {
                    // 构造用户提示文案
                    hintRsp, err := mgr.esportTradeCli.GetOrderHint(ctx, &esport_trade.GetOrderHintRequest{
                        Uid:       svrInfo.UserID,
                        Scene:     esport_trade.GetOrderHintRequest_SCENE_SKILL_DETAIL_ORDER_BUTTON,
                        OrderType: uint32(esport_logic.DiscountType_DISCOUNT_TYPE_COUPON),
                    })
                    if err != nil {
                        log.WarnWithCtx(ctx, "GetCoachDetailRequest GetOrderHint err:%v", err)
                    } else {
                        orderHintText = hintRsp.GetHintText()
                    }
                }
            }
            return nil
        },
        func() error {
            userChannelId, err := mgr.channelOlCli.GetUserChannelId(ctx, uid, uid)
            if err != nil {
                log.ErrorWithCtx(ctx, "GetCoachDetailRequest GetUserChannelId err: %v", err)
                return nil // 不影响主流程
            }
            if userChannelId != 0 {
                channelSimpleInfoResp, err := mgr.channelGoCli.GetChannelSimpleInfo(ctx, &channel_go.GetChannelSimpleInfoReq{
                    ChannelId: userChannelId,
                    OpUid:     uid,
                })
                if err != nil {
                    log.ErrorWithCtx(ctx, "GetCoachDetailRequest GetChannelSimpleInfo err: %v", err)
                    return nil // 不影响主流程
                }
                coachChannelInfo = &esport_http.CoachChannelInfo{
                    ChannelId:   channelSimpleInfoResp.GetChannelSimple().GetChannelId(),
                    ChannelType: channelSimpleInfoResp.GetChannelSimple().GetChannelType(),
                    HasPwd:      channelSimpleInfoResp.GetChannelSimple().GetHasPwd(),
                    CreatorUid:  channelSimpleInfoResp.GetChannelSimple().GetCreaterUid(),
                }
            }

            return nil
        },
    )
    if err != nil {
        return nil, fmt.Errorf("GetCoachDetailRequest 获取数据 err: %v", err)
    }

    gamePropertyMap := make(map[string]*esport_http.GameProperty)
    gameProperty := make([]*esport_http.GameProperty, 0, 8)
    for _, item := range userSkillResp.GetCurrentSkill().GetSectionList() {
        if item.GetSectionName() == "段位信息" {
            continue // 段位不展示再标签区
        }

        valList := make([]*esport_http.GamePropertyVal, 0, len(item.GetItemList()))
        for i, val := range item.GetItemList() {
            valList = append(valList, &esport_http.GamePropertyVal{
                Id:   uint32(i),
                Name: val,
            })
        }
        gamePropertyMap[item.GetSectionName()] = &esport_http.GameProperty{
            Id:      item.SectionId,
            Name:    item.SectionName,
            ValList: valList,
        }
    }
    for _, item := range gameCfgResp.GetConfig().GetGameInformationList() {
        userGameProperty, exist := gamePropertyMap[item.GetSectionName()]
        if !exist {
            continue
        }
        gameProperty = append(gameProperty, userGameProperty)
    }

    coachLabel := coachLabelResp.GetCoachLabelMap()[uid]
    if coachLabel != nil { // 如果有旧的电竞标识, 放在前边
        newCoachLabel = append([]string{coachLabel.GetSourceUrl()}, newCoachLabel...)
    }
    priceInfo := priceMap[in.GetGameId()]
    detail := &esport_http.CoachDetail{
        UserInfo: &esport_http.UserInfo{
            Uid:       userProfile.GetUid(),
            Nickname:  userProfile.GetNickname(),
            Account:   userProfile.GetAccount(),
            Sex:       userProfile.GetSex(),
            HeadDyMd5: mgr.getDynamicHeadMd5(ctx, uid),
        },
        TextDesc:         userSkillResp.GetCurrentSkill().GetTextDesc(),
        VoiceDesc:        userSkillResp.GetCurrentSkill().GetAudio(),
        GameName:         gameCfgResp.GetConfig().GetName(),
        Tag:              sp.GetSkillProduct().GetTag(),
        Score:            coachScoreResp.GetSummary().GetScoreInfo().GetAvgScore(),
        ServiceCnt:       serviceCnt,
        GamePropertyList: gameProperty,
        GameImg:          []string{userSkillResp.GetCurrentSkill().GetSkillEvidence()},
        CoachLabel: &esport_http.CoachLabel{
            Type:      esport_http.LabelSourceType(coachLabel.GetType()),
            SourceUrl: coachLabel.GetSourceUrl(),
        },
        OnlineType:          onlineType,
        VideoDuration:       userSkillResp.GetCurrentSkill().GetAudioDuration(),
        Price:               priceInfo.GetPrice(),
        PriceUnit:           priceInfo.GetUnit(),
        GameBg:              gameCfgResp.GetConfig().GetGameBackground(),
        GameBgColor:         gameCfgResp.GetConfig().GetGameColor(),
        IsFollow:            isFollow,
        IsFamousPlayer:      famousPlayer,
        CoachLabelList:      newCoachLabel,
        SkillLabelList:      skillLabel,
        GranteeWinText:      "",
        SkillLabelUrlList:   skillLabelUrl,
        IsCustomerHosting:   false,
        OrderButtonHintText: orderHintText,
        CoachChannelInfo:    coachChannelInfo,
    }
    detail.GranteeWinText = common.GetGranteeWinText(sp.GetSkillProduct().GetIsGuaranteeWin(), sp.GetSkillProduct().GetGuaranteeWinTexts())

    if len(priceInfo.GetDiscountList()) > 0 {
        // 因为目前业务只允许一个优惠，所以直接取第一个
        // 判断用户使用的客户端版本号，如果不是符合新客价的版本，并且有新客价优惠，则使用顺位第二的优惠
        discountInfo := priceInfo.GetDiscountList()[0]
        detail.HasDiscount = true
        detail.DiscountPrice = discountInfo.GetPrice()
        detail.DiscountType = uint32(discountInfo.GetType())
        detail.DiscountDesc = discountInfo.GetDesc()
        // 额外处理原来的逻辑
        switch discountInfo.GetType() {
        case esport_internal.DiscountType_DISCOUNT_TYPE_FIRST_ORDER:
            detail.HasFirstRoundDiscount = true
            detail.FirstRoundPrice = discountInfo.GetPrice()
        }
    }

    out := &esport_http.GetCoachDetailResponse{
        CoachDetail: detail,
    }
    // 查询大神是否有被托管
    hostingStatus, err := mgr.customerCli.GetHostingStatus(ctx, &esport_customer.GetHostingStatusRequest{
        Uid: in.GetCoachUid(),
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "GetCoachDetailRequest GetHostingStatus err: %v, in: %v", err, in)
    } else {
        // 如果大神有托管并且有开托管
        out.CoachDetail.IsCustomerHosting = hostingStatus.GetCustomerId() != 0 && hostingStatus.GetIsOpen()
    }
    return out, nil
}

func (mgr *EsportHallMgr) getDynamicHeadMd5(ctx context.Context, uid uint32) string {
    dyHeadMap, tErr := mgr.headDynamicImageCli.GetHeadDynamicImageMd5(ctx, []uint32{uid})
    if tErr != nil || dyHeadMap == nil {
        log.ErrorWithCtx(ctx, "getDynamicHeadMd5 uid: %+v, err: %v", uid, tErr)
        return ""
    }
    return dyHeadMap[uid]
}

// 并发执行多任务, 全部执行完之后有序判断异常列表
func concurrentExecWithOrderlyErr(fns ...func() error) error {
    errList := make([]error, len(fns))

    // 部分失败不会提前打断的fn列表
    noInterruptFns := make([]func() error, len(fns))
    for i, fn := range fns {
        thisFn := fn
        thisFnIdx := i
        noInterruptFns[i] = func() error {
            errList[thisFnIdx] = thisFn()
            return nil
        }
    }

    mapreduce.Finish(noInterruptFns...)
    for _, err := range errList {
        if err != nil {
            return err
        }
    }

    return nil
}

func (mgr *EsportHallMgr) isUserOnline(ctx context.Context, uid uint32) bool {

    up, err := mgr.presenceV2Cli.GetUserPres(ctx, uid)
    if err != nil {
        log.ErrorWithCtx(ctx, "isUserOnline uid: %+v, err: %v", uid, err)
        return false
    }

    if len(up.GetInfoList()) > 0 {
        return true
    }

    return false
}

func (mgr *EsportHallMgr) getCoachLabelInfo(ctx context.Context, gameId, uid uint32) ([]string, []string, []string, bool) {
    coachLabel := make([]string, 0, 8)
    skillLabel := make([]string, 0, 8)
    skillLabelUrl := make([]string, 0, 8)
    famousPlayer := false

    coachLabelResp, err := mgr.esportSkillCli.BatchGetCoachLabelsForGame(ctx, &esport_skill.BatchGetCoachLabelsForGameRequest{
        GameId:   gameId,
        CoachIds: []uint32{uid},
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "getCoachLabelInfo uid: %+v, err: %v", uid, err)
        return coachLabel, skillLabel, skillLabelUrl, famousPlayer
    }
    if len(coachLabelResp.GetLabelList()) > 0 {
        for _, item := range coachLabelResp.GetLabelList()[0].GetLabelMap()[uint32(esport_skill.LabelType_LABEL_TYPE_COACH)].GetLabelList() {
            coachLabel = append(coachLabel, item.GetLabelImage())
        }
        for _, item := range coachLabelResp.GetLabelList()[0].GetLabelMap()[uint32(esport_skill.LabelType_LABEL_TYPE_SKILL)].GetLabelList() {
            skillLabel = append(skillLabel, item.GetLabelName())
            if len(item.GetLabelImage()) != 0 {
                skillLabelUrl = append(skillLabelUrl, item.GetLabelImage())
            }
        }
        famousPlayer = coachLabelResp.GetLabelList()[0].GetIsRenowned()
    }

    return coachLabel, skillLabel, skillLabelUrl, famousPlayer
}

func (mgr *EsportHallMgr) SetQuickReceiveSwitch(authInfo *web.AuthInfo, w http.ResponseWriter, r *http.Request) {
    ctx, cancel := context.WithTimeout(r.Context(), 10*time.Second)
    defer cancel()
    log.Infof("SetQuickReceiveSwitch :%v", r)

    var in esport_http.SetQuickReceiveSwitchRequest
    err := json.Unmarshal(authInfo.Body, &in)
    if err != nil {
        e := protocol.ToServerError(err)
        log.Errorf("SetQuickReceiveSwitch Failed to parse request  body(%s)  err(%v)", string(authInfo.Body), err)
        _ = web.ServeAPICodeJson(w, int32(e.Code()), e.Message(), nil)
        return
    }

    // 开启时判断是否绑定小程序/公众号
    if in.GetSwitch() {
        //exCtx := grpcProtocol.WithServiceInfo(ctx, &grpcProtocol.ServiceInfo{UserID: authInfo.UserID})
        //bindWechatResp, err := mgr.esportWechatCli.GetBindWechat(exCtx, &esport_wechat.GetBindWechatRequest{})
        //if err != nil {
        //    log.ErrorWithCtx(ctx, "SetQuickReceiveSwitch GetBindWechat err: %v", err)
        //    _ = web.ServeAPICodeJson(w, status.ErrSys, err.Error(), nil)
        //    return
        //}
        //if !bindWechatResp.GetBindStatus() {
        //    _ = web.ServeAPICodeJson(w, status.ErrEsportHallNeedSubWxgzh, "未绑定小程序/公众号", nil)
        //    return
        //}
    }

    _, err = mgr.esportHallCLi.SetQuickReceiveSwitch(ctx, &esport_hall.SetQuickReceiveSwitchRequest{
        Uid:    authInfo.UserID,
        Switch: in.GetSwitch(),
    })
    if err != nil {
        sErr := protocol.ToServerError(err)
        log.ErrorWithCtx(ctx, "SetQuickReceiveSwitch err: %v", err)
        _ = web.ServeAPICodeJson(w, int32(sErr.Code()), sErr.Message(), nil)
        return
    }

    _ = web.ServeAPICodeJson(w, status.Success, "", nil)
}

func (mgr *EsportHallMgr) GetQuickReceiveSwitch(authInfo *web.AuthInfo, w http.ResponseWriter, r *http.Request) {
    ctx, cancel := context.WithTimeout(r.Context(), 10*time.Second)
    defer cancel()
    log.Infof("GetQuickReceiveSwitch :%v", r)

    serviceInfo, _ := protoGrpc.ServiceInfoFromContext(r.Context())
    log.Debugf("requestId: %d", serviceInfo.RequestID)
    quickReceiveSwitchResp, err := mgr.esportHallCLi.GetQuickReceiveSwitch(ctx, &esport_hall.GetQuickReceiveSwitchRequest{
        Uid: authInfo.UserID,
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "GetQuickReceiveSwitch err: %v", err)
        _ = web.ServeAPICodeJson(w, status.ErrSys, err.Error(), nil)
        return
    }

    _ = web.ServeAPICodeJson(w, status.Success, "", &esport_http.GetQuickReceiveSwitchResponse{
        Switch: quickReceiveSwitchResp.GetSwitch(),
    })
}

// GetCurPricingInfo 获取当前用户的定价等级信息
// 1. 从上下文中获取当前用户的信息
// 2. 查询当前用户的大神等级
// 3. 查询当前用户的定价等级设定
// 4. 查询当前系统配置的等级列表
// 5. 查询当前的价格
// 6. 查询当前用户是否是知名选手
// 7. 组装数据返回
func (mgr *EsportHallMgr) GetCurPricingInfo(ctx context.Context, req *esport_http.GetCurPricingInfoRequest) (*esport_http.GetCurPricingInfoResponse, error) {
    log.DebugWithCtx(ctx, "GetCurPricingInfo req: %+v", req)
    // 校验参数
    if req.GetGameId() == 0 {
        log.ErrorWithCtx(ctx, "GetCurPricingInfo 缺少 GameId 参数")
        return nil, fmt.Errorf("缺少 GameId 参数")
    }
    svrInfo, ok := protoGrpc.ServiceInfoFromContext(ctx)
    if !ok {
        log.ErrorWithCtx(ctx, "GetCurPricingInfo ServiceInfoFromContext fail. in:%+v", req)
        return nil, fmt.Errorf("参数异常")
    }

    // 查询当前用户的大神等级
    godLevel, err := mgr.godlevel.GetGodLevel(ctx, &esport_godlevel.GetGodLevelReq{
        Uid: svrInfo.UserID,
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "GetCurPricingInfo GetGodLevel err: %v", err)
        return nil, err
    }
    // 获取当前的大神等级配置
    godLevelDetail, err := mgr.godlevel.GetGodLevelConfs(ctx, &esport_godlevel.GetGodLevelConfsReq{})
    if err != nil {
        log.ErrorWithCtx(ctx, "GetCurPricingInfo GetGodLevelDetail err: %v", err)
        return nil, err
    }

    // 查询当前用户的定价等级设定
    basePriceSetting, err := mgr.esportSkillCli.GetBasePriceSetting(ctx, &esport_skill.GetBasePriceSettingRequest{
        CoachId: svrInfo.UserID,
        GameId:  req.GetGameId(),
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "GetCurPricingInfo GetBasePriceSetting err: %v", err)
        return nil, err
    }

    // 查询当前的价格
    priceMap, err := mgr.esportSkillCli.CalculatePrice(ctx, &esport_skill.CalculatePriceRequest{
        GameId:   req.GetGameId(),
        CoachIds: []uint32{svrInfo.UserID},
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "GetCurPricingInfo CalculatePrice err: %v", err)
        return nil, err
    }
    price := priceMap.GetPriceMap()[svrInfo.UserID]
    priceUint := "豆/30分钟"
    if price.GetGamePricingUnitType() == esport_skill.GAME_PRICING_UNIT_TYPE_GAME_PRICING_UNIT_TYPE_PER_GAME {
        priceUint = "豆/局"
    }

    // 查询当前用户是否是知名选手
    userRenownedInfo, err := mgr.esportSkillCli.BatchGetUserRenownedInfo(ctx, &esport_skill.BatchGetUserRenownedInfoRequest{
        GameId:  req.GetGameId(),
        UidList: []uint32{svrInfo.UserID},
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "GetCurPricingInfo BatchGetUserRenownedInfo err: %v", err)
        return nil, err
    }
    // 组装数据
    pricingLevels := []*esport_http.PricingLevel{}
    for _, item := range godLevelDetail.GetConfs() {
        pricingLevels = append(pricingLevels, &esport_http.PricingLevel{
            Level:     int32(item.GetLevel()),
            LevelName: item.GetName(),
            Price:     int32(basePriceSetting.GetBasePrice().GetRankPriceMap()[item.GetLevel()]),
        })
    }

    // 比较当前的等级和设置使用等级，取最小值
    if godLevel.GetLevel() < basePriceSetting.GetLevel() {
        basePriceSetting.Level = godLevel.GetLevel()
    }

    return &esport_http.GetCurPricingInfoResponse{
        PricingLevels:     pricingLevels,
        PriceUnit:         priceUint,
        UserLevel:         godLevel.GetLevel(),
        SettingPriceLevel: basePriceSetting.GetLevel(),
        CurrentPrice:      price.GetPrice(),
        IsRenownedPlayer:  userRenownedInfo.GetUserRenownedInfoMap()[svrInfo.UserID] != nil,
    }, nil
}

// SetPricingLevel 设置定价等级
// 1. 校验参数
// 2. 从上下文中获取当前用户的信息
// 3. 获取当前系统的等级配置，校验 level 参数
// 3. 调用 skill 的接口 SetBasePriceSetting 设置定价等级
// 4. 返回
func (mgr *EsportHallMgr) SetPricingLevel(ctx context.Context, req *esport_http.SetPricingLevelRequest) (*esport_http.SetPricingLevelResponse, error) {
    log.DebugWithCtx(ctx, "SetPricingLevel req: %+v", req)
    if req.GetGameId() == 0 {
        log.ErrorWithCtx(ctx, "SetPricingLevel 缺少 GameId 参数")
        return nil, fmt.Errorf("缺少 GameId 参数")
    }
    if req.GetLevel() == 0 {
        log.ErrorWithCtx(ctx, "SetPricingLevel 缺少 Level 参数")
        return nil, fmt.Errorf("缺少 Level 参数")
    }

    svrInfo, ok := protoGrpc.ServiceInfoFromContext(ctx)
    if !ok {
        log.ErrorWithCtx(ctx, "GetCurPricingInfo ServiceInfoFromContext fail. in:%+v", req)
        return nil, fmt.Errorf("参数异常")
    }

    // 获取当前系统的等级配置
    godLevelConfs, err := mgr.godlevel.GetGodLevelConfs(ctx, &esport_godlevel.GetGodLevelConfsReq{})
    if err != nil {
        log.ErrorWithCtx(ctx, "SetPricingLevel GetGodLevelConfs err: %v", err)
        return nil, err
    }
    // 找出最大值和最小值
    minLevel := uint32(0)
    maxLevel := uint32(0)
    for _, item := range godLevelConfs.GetConfs() {
        if item.GetLevel() < minLevel {
            minLevel = item.GetLevel()
        }
        if item.GetLevel() > maxLevel {
            maxLevel = item.GetLevel()
        }
    }
    if req.GetLevel() < minLevel || req.GetLevel() > maxLevel {
        log.ErrorWithCtx(ctx, "SetPricingLevel Level 参数不在范围内")
        return nil, fmt.Errorf("Level 参数不在范围内")
    }

    // 调用 skill 的接口 SetBasePriceSetting 设置定价等级
    _, err = mgr.esportSkillCli.SetBasePriceSetting(ctx, &esport_skill.SetBasePriceSettingRequest{
        CoachId: svrInfo.UserID,
        GameId:  req.GetGameId(),
        Level:   req.GetLevel(),
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "SetPricingLevel SetBasePriceSetting err: %v", err)
        return nil, err
    }

    return &esport_http.SetPricingLevelResponse{}, nil
}

// GetApplicableLabels 获取可申请的标识列表
// 1. 校验参数。如果 gameId 为 0，则查询用户拥有的技能并组装成 gameId 数组。否则，直接使用 gameId 参数
// 2. 从上下文中获取当前用户的信息
// 3. 查询用户可以申请的标识
// 4. 查询用户当前的等级
// 5. 组装数据返回
func (mgr *EsportHallMgr) GetApplicableLabels(ctx context.Context, req *esport_http.GetApplicableLabelsRequest) (*esport_http.GetApplicableLabelsResponse, error) {
    log.DebugWithCtx(ctx, "GetApplicableLabels req: %+v", req)
    svrInfo, ok := protoGrpc.ServiceInfoFromContext(ctx)
    if !ok {
        log.ErrorWithCtx(ctx, "GetCurPricingInfo ServiceInfoFromContext fail. in:%+v", req)
        return nil, fmt.Errorf("参数异常")
    }
    gameIds := []uint32{}
    if req.GetGameId() == 0 {
        skill, err := mgr.esportSkillCli.GetUserCurrentSkill(ctx, &esport_skill.GetUserCurrentSkillRequest{
            Uid: svrInfo.UserID,
        })
        if err != nil {
            log.ErrorWithCtx(ctx, "GetApplicableLabels GetUserCurrentSkill err: %v", err)
            return nil, err
        }
        for _, item := range skill.GetSkill() {
            gameIds = append(gameIds, item.GetGameId())
        }
    } else {
        gameIds = append(gameIds, req.GetGameId())
    }
    // 查询技能信息
    gameInfoList, err := mgr.esportSkillCli.GetGameDetailByIds(ctx, &esport_skill.GetGameDetailByIdsRequest{
        GameIds: gameIds,
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "GetApplicableLabels GetGameDetailByIds err: %v", err)
        return nil, err
    }
    // 如果没有技能信息，则直接返回
    if len(gameInfoList.GetConfigList()) == 0 {
        log.InfoWithCtx(ctx, "GetApplicableLabels GetGameDetailByIds 没有查询到技能信息 gameIds: %v", gameIds)
        return &esport_http.GetApplicableLabelsResponse{}, nil
    }
    // 建立索引
    gameInfoMap := make(map[uint32]*esport_skill.EsportGameConfig)
    for _, item := range gameInfoList.GetConfigList() {
        gameInfoMap[item.GetGameId()] = item
    }

    // 查询用户当前的等级
    godLevelDetail, err := mgr.godlevel.GetGodLevel(ctx, &esport_godlevel.GetGodLevelReq{
        Uid: svrInfo.UserID,
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "GetApplicableLabels GetGodLevelDetail err: %v", err)
        return nil, err
    }
    // 查询用户可申请的列表
    labels, err := mgr.esportSkillCli.GetCoachApplicableLabels(ctx, &esport_skill.GetCoachApplicableLabelsRequest{
        CoachId: svrInfo.UserID,
        GameId:  gameIds,
    })

    // 组装数据
    // 首先对标识数组排序
    sortLabels(labels, gameInfoMap)
    out := &esport_http.GetApplicableLabelsResponse{
        AppliedLabels: make([]*esport_http.Label, 0, len(labels.GetLabelList())),
    }
    for _, item := range labels.GetLabelList() {
        elems := &esport_http.Label{
            LabelId:          item.GetInfo().GetId(),
            LabelName:        item.GetInfo().GetLabelName(),
            LabelIcon:        item.GetInfo().GetLabelImage(),
            AdditionalPrice:  int32(item.GetInfo().GetPricingAmount()),
            LabelDescription: item.GetInfo().GetLabelDescription(),
            LabelType:        esport_http.LabelType(item.GetInfo().GetLabelType()),
            ApplyLevel:       int32(item.GetInfo().GetApplicableLevel()),
            IsApplied:        item.GetIsApplied(),
            ApplyEntry:       item.GetInfo().GetApplyEntry(),
            IsMetCondition:   godLevelDetail.GetLevel() >= item.GetInfo().GetApplicableLevel(),
            GameName:         "",
            Requirement:      item.GetInfo().GetLabelRequirements(),

            PriceAdditionalSwitch: item.GetIsAddToPrice(),
        }
        if item.GetInfo().GetLabelType() == esport_skill.LabelType_LABEL_TYPE_SKILL {
            // 查询技能的信息
            gameInfo, ok := gameInfoMap[item.GetInfo().GetGameId()]
            if !ok {
                // 如果没有对应的技能信息，则跳过
                log.InfoWithCtx(ctx, "GetApplicableLabels 组装数据 缺少技能 game_id: %d", item.GetInfo().GetGameId())
                continue
            }
            elems.GameName = gameInfo.Name
        }
        out.AppliedLabels = append(out.AppliedLabels, elems)
    }

    // 遍历结果列表，将已获得的有加价的标识放在前面，并保证原本在列表中的位置
    tmpList := make([]*esport_http.Label, 0)
    remainingLabels := make([]*esport_http.Label, 0)
    for _, label := range out.AppliedLabels {
        if label.GetIsApplied() && label.GetAdditionalPrice() > 0 {
            tmpList = append(tmpList, label)
        } else {
            remainingLabels = append(remainingLabels, label)
        }
    }

    out.AppliedLabels = append(tmpList, remainingLabels...)
    return out, nil
}

// SetLabelPriceSwitch 设置标识价格开关
func (mgr *EsportHallMgr) SetLabelPriceSwitch(ctx context.Context, req *esport_http.SetLabelPriceSwitchRequest) (*esport_http.SetLabelPriceSwitchResponse, error) {
    out := &esport_http.SetLabelPriceSwitchResponse{}

    log.DebugWithCtx(ctx, "SetLabelPriceSwitch req: %+v", req)
    svrInfo, ok := protoGrpc.ServiceInfoFromContext(ctx)
    if !ok {
        log.ErrorWithCtx(ctx, "SetLabelPriceSwitch ServiceInfoFromContextfail. in:%+v", req)
    }
    if svrInfo.UserID == 0 {
        log.ErrorWithCtx(ctx, "SetLabelPriceSwitch ServiceInfoFromContext fail. Missing req_uid in:%+v", req)
        return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "缺少请求上下文信息")
    }

    if req.GetLabelId() == 0 {
        log.ErrorWithCtx(ctx, "SetLabelPriceSwitch req.LabelId is 0. in:%+v", req)
        return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "缺少请求参数")
    }

    _, err := mgr.esportSkillCli.SetLabelPriceSwitch(ctx, &esport_skill.SetLabelPriceSwitchRequest{
        CoachId:     svrInfo.UserID,
        LabelId:     req.GetLabelId(),
        TargetState: req.GetTargetState(),
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "SetLabelPriceSwitch err: %v", err)
        return out, err
    }

    log.InfoWithCtx(ctx, "SetLabelPriceSwitch success.uid:%d label_id: %d, target_state: %t", svrInfo.UserID, req.GetLabelId(), req.GetTargetState())
    return out, nil
}

// GetRecommendCoachList 获取电竞助手推荐给用户的活动大神列表
func (mgr *EsportHallMgr) GetRecommendCoachList(ctx context.Context, req *esport_http.GetRecommandCoachListRequest) (*esport_http.GetRecommandCoachListResponse, error) {
    if req.GetSceneType() > 0 {
        return mgr.GetActivityRcmdCoachList(ctx, req)
    }

    log.DebugWithCtx(ctx, "GetRecommendCoachList req: %+v", req)
    out := &esport_http.GetRecommandCoachListResponse{}

    svrInfo, ok := protoGrpc.ServiceInfoFromContext(ctx)
    if !ok {
        log.ErrorWithCtx(ctx, "GetCurPricingInfo ServiceInfoFromContext fail. in:%+v", req)
        return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "缺少请求上下文信息")
    }
    if svrInfo.UserID == 0 {
        log.ErrorWithCtx(ctx, "GetRecommendCoachList ServiceInfoFromContext fail. Missing req_uid in:%+v", req)
        return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "缺少请求上下文信息")
    }

    // 先查询有没有配置这个游戏推荐的教练id列表
    reCoachIds := mgr.bc.GetRecommendCoaches(req.GetGameId(), req.GetSeriesId())
    if len(reCoachIds) == 0 {
        log.ErrorWithCtx(ctx, "GetRecommendCoachList 没有配置推荐教练 game_id: %d, series_id: %s", req.GetGameId(), req.GetSeriesId())
        return out, nil
    }
    log.DebugWithCtx(ctx, "%+v, GetRecommendCoachList cfg coaches: %+v", req, reCoachIds)

    coachList, _, err := mgr.getRecommendCoachList(ctx, req.GetGameId(), reCoachIds, 0, uint32(len(reCoachIds)))
    if err != nil {
        log.ErrorWithCtx(ctx, "GetRecommendCoachList GetGameCoachList req: %+v, err: %v", req, err)
        return out, err
    }
    if len(coachList) == 0 {
        log.InfoWithCtx(ctx, "GetRecommendCoachList 没有查询到推荐教练 game_id: %d", req.GetGameId())
        return out, nil
    }

    uidList := make([]uint32, 0, len(coachList))
    for _, item := range coachList {
        uidList = append(uidList, item.GetUserProfile().GetUid())
    }

    // 获取我关注的信息
    isFollowing := make(map[uint32]bool)
    following, _, serverError := mgr.friendShipCli.BatchGetBiFollowing(ctx, svrInfo.UserID, uidList, true, false)
    if serverError != nil {
        log.ErrorWithCtx(ctx, "GetRecommendCoachList GetBiFollowing err: %v", serverError)
    } else {
        log.DebugWithCtx(ctx, "GetRecommendCoachList GetBiFollowing opUid: %d following: %+v", svrInfo.UserID, following)
        for _, item := range following {
            isFollowing[item.ToUid] = true
        }
    }
    for _, item := range coachList {
        item.IsFollowed = isFollowing[item.GetUserProfile().GetUid()]
    }

    // 填充通用的优惠价格
    mgr.fillDiscountPriceInfo(ctx, svrInfo.UserID, req.GetGameId(), coachList)

    // 填充音色标签
    toneIntroMap := mgr.bc.GetEsportHelperRecommendConfig().GetToneIntroMap()
    log.DebugWithCtx(ctx, "GetRecommendCoachList toneIntroMap: %+v", toneIntroMap)
    if gameToneIntroMap, ok := toneIntroMap[req.GetGameId()]; ok {
        for _, item := range coachList {
            if intro, ok := gameToneIntroMap[item.GetUserProfile().GetUid()]; ok {
                item.Tone = intro
            }
        }
    }

    out.CoachList = coachList
    return out, nil
}

// GetActivityRcmdCoachList 从推荐接口拉取活动推荐的大神列表, 替换旧的GetRecommendCoachList
func (mgr *EsportHallMgr) GetActivityRcmdCoachList(ctx context.Context, req *esport_http.GetRecommandCoachListRequest) (
    *esport_http.GetRecommandCoachListResponse, error) {

    log.InfoWithCtx(ctx, "GetActivityRcmdCoachList req: %+v", req)
    out := &esport_http.GetRecommandCoachListResponse{}

    svrInfo, ok := protoGrpc.ServiceInfoFromContext(ctx)
    if !ok {
        log.ErrorWithCtx(ctx, "GetCurPricingInfo ServiceInfoFromContext fail. in:%+v", req)
        return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "缺少请求上下文信息")
    }
    if svrInfo.UserID == 0 {
        log.ErrorWithCtx(ctx, "GetActivityRcmdCoachList ServiceInfoFromContext fail. Missing req_uid in:%+v", req)
        return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "缺少请求上下文信息")
    }

    rcmdSkillProductList, err := mgr.esportRcmdCli.GetEsportRcmdSkillProduct(ctx, &esport_rcmd.GetEsportRcmdSkillProductReq{
        SceneType:    esport_rcmd.SceneType(req.GetSceneType()),
        SkillId:      req.GetGameId(),
        Offset:       req.GetPageNum() * req.GetPageSize(),
        Limit:        req.GetPageSize(),
        GameProperty: req.GetFilterOption(),
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "GetEsportRcmdSkillProduct err: %v", err)
        return nil, err
    }
    coachUidList := transform.FlatMap(rcmdSkillProductList.GetSkillProductList(), func(o *esport_rcmd.EsportRcmdSkillProduct) []uint32 {
        return []uint32{o.GetUid()}
    })
    productIdList := transform.FlatMap(rcmdSkillProductList.GetSkillProductList(), func(o *esport_rcmd.EsportRcmdSkillProduct) []uint32 {
        return []uint32{o.GetSkillProductId()}
    })

    // 并发查询数据
    wg := sync.WaitGroup{}
    userProfileMap := make(map[uint32]*esport_http.UserProfile)
    skillInfoMap := make(map[uint32]*esport_skill.UserSkillInfo)
    famousPlayerMap := make(map[uint32]bool)
    skillLabelMap := make(map[uint32][]string)
    gameDetail := &esport_skill.GetGameDetailByIdResponse{}
    skillProductInfoList := make([]*esport_hall.SkillProduct, 0)
    isFollowing := make(map[uint32]bool)
    specialLabelMap := make(map[uint32][]string)
    specialLabelImgMap := make(map[uint32][]string)
    voicelabelMap := make(map[uint32]string)

    // 查询商品信息
    wg.Add(1)
    go func() {
        defer wg.Done()
        skillProductInfoResp, err := mgr.esportHallCLi.BatchGetSkillProductInfo(ctx, &esport_hall.BatchGetSkillProductInfoRequest{
            ProductIdList: productIdList,
        })
        if err != nil {
            log.ErrorWithCtx(ctx, "GetSkillProductInfo, coachUidList: %+v, err: %v", coachUidList, err)
        }
        skillProductInfoList = skillProductInfoResp.GetProductList()
    }()

    // 查询用户信息
    wg.Add(1)
    go func() {
        defer wg.Done()
        var err error
        userProfileMap, err = mgr.getUserProfileWithDyHead(ctx, coachUidList)
        if err != nil {
            log.ErrorWithCtx(ctx, "GetUserProfileWithDyHead, coachUidList: %+v, err: %v", coachUidList, err)
        }
    }()

    // 查询用户技能信息
    wg.Add(1)
    go func() {
        defer wg.Done()
        skillResp, err := mgr.esportSkillCli.BatchGetUserCurrentSkill(ctx, &esport_skill.BatchGetUserCurrentSkillRequest{
            Uid: coachUidList,
        })
        if err != nil {
            log.ErrorWithCtx(ctx, "BatchGetUserSkillInfo, coachUidList: %+v, err: %v", coachUidList, err)
            return
        }

        for _, item := range skillResp.GetList() {
            for _, skillItem := range item.GetSkill() {
                if skillItem.GetGameId() == req.GetGameId() {
                    skillInfoMap[item.GetUid()] = skillItem
                    break
                }
            }
        }
    }()

    // 获取标签
    wg.Add(1)
    go func() {
        defer wg.Done()
        coachLabelResp, err := mgr.esportSkillCli.BatchGetCoachLabelsForGame(ctx, &esport_skill.BatchGetCoachLabelsForGameRequest{
            GameId:   req.GetGameId(),
            CoachIds: coachUidList,
        })
        if err != nil {
            log.ErrorWithCtx(ctx, "BatchGetCoachLabelsForGame, coachUidList: %+v, err: %v", coachUidList, err)
            return
        }

        for _, item := range coachLabelResp.GetLabelList() {
            famousPlayerMap[item.GetCoachId()] = item.GetIsRenowned()
            labelList := make([]string, 0, 8)
            for _, labelItem := range item.GetLabelMap()[uint32(esport_skill.LabelType_LABEL_TYPE_SKILL)].GetLabelList() {
                labelList = append(labelList, labelItem.GetLabelImage())
            }
            skillLabelMap[item.GetCoachId()] = labelList
        }
    }()

    // 获取游戏详情
    wg.Add(1)
    go func() {
        defer wg.Done()
        var err error
        gameDetail, err = mgr.esportSkillCli.GetGameDetailById(ctx, &esport_skill.GetGameDetailByIdRequest{
            GameId: req.GetGameId(),
        })
        if err != nil {
            log.ErrorWithCtx(ctx, "GetGameDetailByIds, gameId: %d, err: %v", req.GetGameId(), err)
        }
    }()

    // 获取我关注的信息
    wg.Add(1)
    go func() {
        defer wg.Done()
        following, _, serverError := mgr.friendShipCli.BatchGetBiFollowing(ctx, svrInfo.UserID, coachUidList, true, false)
        if serverError != nil {
            log.ErrorWithCtx(ctx, "GetBiFollowing err: %v", serverError)
            return
        }
        for _, item := range following {
            isFollowing[item.ToUid] = true
        }
    }()

    // 获取特色标签
    wg.Add(1)
    go func() {
        defer wg.Done()
        specialLabelResp, err := mgr.esportSkillCli.BatchGetUserGameSpecialLabel(ctx, &esport_skill.BatchGetUserGameSpecialLabelRequest{
            UidList: coachUidList,
            GameId:  req.GetGameId(),
        })
        if err != nil {
            log.ErrorWithCtx(ctx, "BatchGetUserGameSpecialLabel err: %v", err)
            return
        }

        for coachUid, labelInfo := range specialLabelResp.GetUserSpecialLabelMap() {
            specialLabelList := make([]string, 0, len(labelInfo.GetLabelList()))
            specialLabelImgList := make([]string, 0, len(labelInfo.GetLabelList()))
            for _, labelInfoItem := range labelInfo.GetLabelList() {
                if len(labelInfoItem.GetLabelImage()) > 0 {
                    specialLabelImgList = append(specialLabelImgList, labelInfoItem.GetLabelImage())
                } else {
                    specialLabelList = append(specialLabelList, labelInfoItem.GetLabelName()) // 同一个标签有可能有图片，有可能没有
                }
            }
            specialLabelMap[coachUid] = specialLabelList
            specialLabelImgMap[coachUid] = specialLabelImgList
            voicelabelMap[coachUid] = labelInfo.GetVoiceLabel()
        }
    }()

    wg.Wait()
    coachList := make([]*esport_http.EsportAreaCoachInfo, 0, len(coachUidList))
    for _, item := range skillProductInfoList {
        up := userProfileMap[item.GetUid()]
        if up == nil {
            log.WarnWithCtx(ctx, "GetActivityRcmdCoachList userprofile is nil, uid: %d", item.GetUid())
            continue
        }
        coachItem := &esport_http.EsportAreaCoachInfo{
            UserProfile: up,
            TextDesc:    skillInfoMap[item.GetUid()].GetTextDesc(),
            IsFollowed:  isFollowing[item.GetUid()],
            Price: &esport_http.ProductOrderPriceInfo{
                Price:       item.GetPrice(),
                PriceUnit:   "豆",
                MeasureCnt:  30,
                MeasureUnit: "分钟",
            },
            IsFamousPlayer:      famousPlayerMap[item.GetUid()],
            VoiceDesc:           skillInfoMap[item.GetUid()].GetAudio(),
            Tone:                voicelabelMap[item.GetUid()],
            StrategyId:          0,
            RecallSourceId:      0,
            FeatureLabelList:    specialLabelMap[item.GetUid()],
            FeatureLabelImgList: specialLabelImgMap[item.GetUid()],
            SkillLabelList:      skillLabelMap[item.GetUid()],
            Rank:                item.Tag,
        }

        // 调整价格信息
        realPriceType := gameDetail.GetConfig().GetGamePricing().GetGamePricingUnitType()
        if item.GetUintType() > 0 {
            realPriceType = esport_skill.GAME_PRICING_UNIT_TYPE(item.GetUintType())
        }
        if realPriceType == esport_skill.GAME_PRICING_UNIT_TYPE_GAME_PRICING_UNIT_TYPE_PER_GAME {
            coachItem.Price.MeasureCnt = 1
            coachItem.Price.MeasureUnit = "局"
        }
        // 包赢文案
        if skill, ok := skillInfoMap[item.GetUid()]; ok {
            if skill.GetIsGuaranteeWin() {
                for _, section := range skill.GetSectionList() {
                    if section.GetSectionName() == GuaranteeWinSectionName && len(section.GetItemList()) > 0 {
                        coachItem.GuaranteeWinText = section.GetItemList()[0]
                        break
                    }
                }
            }
        }
        coachList = append(coachList, coachItem)
    }

    // 填充通用的优惠价格
    mgr.fillDiscountPriceInfo(ctx, svrInfo.UserID, req.GetGameId(), coachList)
    out.CoachList = coachList
    return out, nil
}

func (mgr *EsportHallMgr) fillDiscountPriceInfo(ctx context.Context, playerUid, gameId uint32, coachList []*esport_http.EsportAreaCoachInfo) {
    uidList := make([]uint32, 0, len(coachList))
    for _, item := range coachList {
        uidList = append(uidList, item.GetUserProfile().GetUid())
    }

    pricingInfoResponse, err := mgr.esportInternalCli.BatchGetCoachPricingInfo(ctx, &esport_internal.BatchGetCoachPricingInfoRequest{
        Uid:         playerUid,
        CoachUid:    uidList,
        GameId:      gameId,
        BuyAmount:   1,
        QueryOption: &esport_internal.CouponQueryOption{DoNotCheckRisk: true, DoNotCheckVersion: true},
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "BatchGetCoachPricingInfo uidList: %+v, err: %v", uidList, err)
        return
    }

    for _, item := range coachList {
        price, ok := pricingInfoResponse.GetPriceMap()[item.GetUserProfile().GetUid()]
        if !ok {
            continue
        }
        item.GetPrice().Price = price.GetPrice()
        item.GetPrice().PriceUnit = price.GetUnit()
        item.GetPrice().MeasureCnt = price.GetMeasureCnt()
        item.GetPrice().MeasureUnit = price.GetMeasureUnit()
        item.GetPrice().HasFirstRoundDiscount = false
        item.GetPrice().FirstRoundPrice = 0
        item.GetPrice().HasDiscount = false
        item.GetPrice().DiscountPrice = 0
        item.GetPrice().DiscountType = 0
        item.GetPrice().DiscountDesc = ""
        if len(price.GetDiscountList()) > 0 {
            // 因为目前业务只允许一个优惠，所以直接取第一个
            // 判断用户使用的客户端版本号，如果不是符合新客价的版本，并且有新客价优惠，则使用顺位第二的优惠
            discountInfo := price.GetDiscountList()[0]
            item.GetPrice().HasDiscount = true
            item.GetPrice().DiscountPrice = discountInfo.GetPrice()
            item.GetPrice().DiscountType = uint32(discountInfo.GetType())
            item.GetPrice().DiscountDesc = discountInfo.GetDesc()
            // 额外处理原来的逻辑
            switch discountInfo.GetType() {
            case esport_internal.DiscountType_DISCOUNT_TYPE_FIRST_ORDER:
                item.GetPrice().HasFirstRoundDiscount = true
                item.GetPrice().FirstRoundPrice = discountInfo.GetPrice()
            }
        }
    }
}

func sortLabels(labels *esport_skill.GetCoachApplicableLabelsResponse, gameInfoMap map[uint32]*esport_skill.EsportGameConfig) {
    sort.SliceStable(labels.GetLabelList(), func(i, j int) bool {
        // 标识类型 > 技能等级 > 标识排序
        labelI := labels.GetLabelList()[i].GetInfo()
        labelJ := labels.GetLabelList()[j].GetInfo()
        // 判断标识类型，类型相同进入下一级判断
        if labelI.GetLabelType() == labelJ.GetLabelType() {
            gameI := gameInfoMap[labelI.GetGameId()]
            gameJ := gameInfoMap[labelJ.GetGameId()]
            // 技能不存在，则按照下一级排序
            if gameI == nil || gameJ == nil || (gameI.GetGameRank() == gameJ.GetGameRank()) {
                // 判断标识排序
                return labelI.GetDisplayOrder() < labelJ.GetDisplayOrder()
            } else {
                return gameI.GetGameRank() < gameJ.GetGameRank()
            }
        } else {
            return labelI.GetLabelType() < labelJ.GetLabelType()
        }
    })
}

func (mgr *EsportHallMgr) getUserProfileWithDyHead(ctx context.Context, uidList []uint32) (map[uint32]*esport_http.UserProfile, error) {
    userprofileMap, err := mgr.userProfileCli.BatchGetUserProfileV2(ctx, uidList, false)
    if err != nil {
        log.ErrorWithCtx(ctx, "BatchGetUserProfile uidList: %+v, err: %v", uidList, err)
        return nil, err
    }

    dyHeadMap, tErr := mgr.headDynamicImageCli.GetHeadDynamicImageMd5(ctx, uidList)
    if tErr != nil {
        log.ErrorWithCtx(ctx, "GetHeadDynamicImageMd5 uidList: %+v, err: %v", uidList, tErr)
        return nil, tErr
    }

    usernames := make([]string, 0, len(userprofileMap))
    invokeUid := uint32(0)
    for _, item := range userprofileMap {
        if invokeUid == 0 {
            invokeUid = item.GetUid()
        }
        usernames = append(usernames, item.GetAccount())
    }

    headImgMap, err := mgr.headImageCli.BatchGetHeadImageMd5(ctx, invokeUid, usernames)
    if err != nil {
        log.ErrorWithCtx(ctx, "getUserProfileWithDyHead.BatchGetHeadImageMd5, uidList: %+v, err: %v", uidList, err)
    }

    for _, item := range userprofileMap {
        item.HeadImgMd5 = headImgMap[item.GetAccount()]
        item.HeadDyImgMd5 = dyHeadMap[item.Uid]
    }

    resultMap := make(map[uint32]*esport_http.UserProfile)

    for _, item := range userprofileMap {
        resultMap[item.GetUid()] = &esport_http.UserProfile{
            Uid:          item.GetUid(),
            Account:      item.GetAccount(),
            Nickname:     item.GetNickname(),
            AccountAlias: item.GetAccountAlias(),
            Sex:          item.GetSex(),
            HeadImgMd5:   item.GetHeadImgMd5(),
            HeadDyImgMd5: item.GetHeadDyImgMd5(),
        }
    }

    return resultMap, nil
}

// CustomerHostingOperation 请求客服托管开关操作
func (mgr *EsportHallMgr) CustomerHostingOperation(ctx context.Context, req *esport_http.HostingOperationRequest) (*esport_http.HostingOperationResponse, error) {
    log.DebugWithCtx(ctx, "CustomerHostingOperation req: %+v", req)

    // 获取当前请求的用户
    svrInfo, ok := protoGrpc.ServiceInfoFromContext(ctx)
    if !ok {
        log.ErrorWithCtx(ctx, "CustomerHostingOperation ServiceInfoFromContext fail. in:%+v", req)
        return nil, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "缺少请求上下文信息")
    }

    // 获取当前的托管状态
    hostingStatus, err := mgr.customerCli.GetHostingStatus(ctx, &esport_customer.GetHostingStatusRequest{
        Uid: svrInfo.UserID,
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "CustomerHostingOperation GetHostingStatus err: %v, req: %+v, uid: %d", err, req, svrInfo.UserID)
        return &esport_http.HostingOperationResponse{}, protocol.NewExactServerError(err, status.ErrEsportsCustomerNotFound, "当前没有托管")
    }

    if hostingStatus.GetCustomerId() == 0 {
        // 如果没有托管的客服，则也不允许操作，返回错误
        log.ErrorWithCtx(ctx, "CustomerHostingOperation customer id 是 0，当前的用户没有客服托管 req: %+v, uid: %d", err, req, svrInfo.UserID)
        return &esport_http.HostingOperationResponse{}, protocol.NewExactServerError(err, status.ErrEsportsCustomerNotFound, "当前没有托管")
    }

    // 调用 rpc 层的 esport-customer 服务进行托管操作
    switch req.GetOperation() {
    case esport_http.HostingOperation_HOSTING_OPERATION_ENABLE:
        _, err := mgr.customerCli.EnableCustomerHosting(ctx, &pb.EnableCustomerHostingRequest{Uid: svrInfo.UserID})
        if err != nil {
            log.ErrorWithCtx(ctx, "CustomerHostingOperation EnableCustomerHosting err: %v", err)
            return &esport_http.HostingOperationResponse{}, protocol.NewExactServerError(err, status.ErrEsportsCommonErr, "开启托管失败")
        }
    case esport_http.HostingOperation_HOSTING_OPERATION_DISABLE:
        _, err := mgr.customerCli.DisableCustomerHosting(ctx, &pb.DisableCustomerHostingRequest{Uid: svrInfo.UserID})
        if err != nil {
            log.ErrorWithCtx(ctx, "CustomerHostingOperation DisableCustomerHosting err: %v", err)
            return &esport_http.HostingOperationResponse{}, protocol.NewExactServerError(err, status.ErrEsportsCommonErr, "关闭托管失败")
        }
    default:
        log.ErrorWithCtx(ctx, "CustomerHostingOperation 未知的操作类型: %d", req.GetOperation())
        return &esport_http.HostingOperationResponse{}, protocol.NewExactServerError(nil, status.ErrEsportsCommonErr, "未知的操作类型")
    }
    return &esport_http.HostingOperationResponse{}, nil
}

// GetHostingStatus 获取托管状态
func (mgr *EsportHallMgr) GetHostingStatus(ctx context.Context, req *esport_http.GetHostingStatusRequest) (*esport_http.GetHostingStatusResponse, error) {
    // 获取当前请求的用户
    svrInfo, ok := protoGrpc.ServiceInfoFromContext(ctx)
    if !ok {
        log.ErrorWithCtx(ctx, "CustomerHostingOperation ServiceInfoFromContext fail. in:%+v", req)
        return &esport_http.GetHostingStatusResponse{}, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "缺少请求上下文信息")
    }
    // 调用 rpc 层的 esport-customer 服务获取托管状态
    resp, err := mgr.customerCli.GetHostingStatus(ctx, &pb.GetHostingStatusRequest{Uid: svrInfo.UserID})
    if err != nil {
        log.ErrorWithCtx(ctx, "GetHostingStatus GetHostingStatus err: %v", err)
        return &esport_http.GetHostingStatusResponse{}, protocol.NewExactServerError(err, status.ErrEsportsCommonErr, "获取托管状态失败")
    }

    // 组装数据并返回
    out := &esport_http.GetHostingStatusResponse{
        IsOpen: resp.GetIsOpen(),
        CustomerInfo: &esport_http.UserProfile{
            Uid:          resp.GetCustomerId(),
            Account:      "",
            Nickname:     "",
            AccountAlias: "",
            Sex:          0,
            HeadImgMd5:   "",
            HeadDyImgMd5: "",
        },
    }
    // 如果有托管，则需要查询客服的账号信息
    if resp.GetCustomerId() != 0 {
        if userProfile, err := mgr.userProfileCli.GetUserProfileV2(ctx, resp.GetCustomerId(), false); err == nil {
            out.CustomerInfo.Account = userProfile.GetAccount()
            out.CustomerInfo.Nickname = userProfile.GetNickname()
            out.CustomerInfo.AccountAlias = userProfile.GetAccountAlias()
            out.CustomerInfo.Sex = userProfile.GetSex()
            out.CustomerInfo.HeadImgMd5 = userProfile.GetHeadImgMd5()
            out.CustomerInfo.HeadDyImgMd5 = userProfile.GetHeadDyImgMd5()
        }
    }
    return out, nil
}

// ContactCustomerService 联系客服
func (mgr *EsportHallMgr) ContactCustomerService(ctx context.Context, req *esport_http.ContactCustomerServiceRequest) (*esport_http.ContactCustomerServiceResponse, error) {
    out := &esport_http.ContactCustomerServiceResponse{}
    log.DebugWithCtx(ctx, "[ContactCustomerService] req: %+v", req)
    // 获取当前请求的用户
    svrInfo, ok := protoGrpc.ServiceInfoFromContext(ctx)
    if !ok {
        log.ErrorWithCtx(ctx, "[ContactCustomerService] ServiceInfoFromContext fail. in:%+v", req)
        return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "缺少请求上下文信息")
    }
    // 参数校验
    if req.GetCoachId() == 0 {
        log.ErrorWithCtx(ctx, "[ContactCustomerService] CoachId is empty")
        return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "CoachId is empty")
    }
    // 这里直接调用获取托管状态的接口
    hostingStatus, err := mgr.customerCli.GetHostingStatus(ctx, &esport_customer.GetHostingStatusRequest{
        Uid: req.GetCoachId(),
    })

    if err != nil {
        // 如果获取不到大神的托管状态，则认为没有客服
        log.ErrorWithCtx(ctx, "[ContactCustomerService] GetHostingStatus failed: %v, coach_id: %d", err, req.GetCoachId())
        return out, nil
    }

    if hostingStatus.GetCustomerId() == 0 || !hostingStatus.GetIsOpen() {
        // 如果没有客服托管，或者托管了，但是没有开启，则认为没有客服
        log.InfoWithCtx(ctx, "[ContactCustomerService] CoachId: %d has no customer service, is_open %v,customer_id: %d", req.GetCoachId(), hostingStatus.GetIsOpen(), hostingStatus.GetCustomerId())
        return out, nil
    }

    // 如果当前请求的这个用户就是托管的客服，则认为没有客服
    if hostingStatus.GetCustomerId() == svrInfo.UserID {
        log.InfoWithCtx(ctx, "[ContactCustomerService] CoachId: %d is hosting by customer service, customer_id: %d which is current user: %d", req.GetCoachId(), hostingStatus.GetCustomerId(), svrInfo.UserID)
        return out, nil
    }

    // 调用 esport-customer 服务获取指定大神的客服信息
    resp, err := mgr.customerCli.GetCustomerByCoachUid(ctx, &esport_customer.GetCustomerByCoachUidRequest{
        CoachUid:   req.GetCoachId(),
        OnlyOnline: false, // 不管客服是否在线
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "[ContactCustomerService] GetCustomerByCoachUid failed: %v", err)
        return out, protocol.NewExactServerError(nil, status.ErrEsportsCommonErr, "GetCustomerByCoachUid failed")
    }
    // 如果大神没有托管的客服，则返回空
    if resp.GetCustomerAccounts().GetCustomer().GetCustomerUid() == 0 {
        log.InfoWithCtx(ctx, "[ContactCustomerService] CoachId: %d has no customer service", req.GetCoachId())
        return out, nil
    }
    // 获取客服的信息
    customerInfo, err := mgr.userProfileCli.GetUserProfileV2(ctx, resp.GetCustomerAccounts().GetCustomer().GetCustomerUid(), false)
    if err != nil {
        log.ErrorWithCtx(ctx, "[ContactCustomerService] GetUserProfileV2 failed: %v", err)
        return out, protocol.NewExactServerError(nil, status.ErrEsportsCommonErr, "GetUserProfileV2 failed")
    }
    // 组装响应数据
    out = &esport_http.ContactCustomerServiceResponse{
        CustomerInfo: &esport_http.UserProfile{
            Uid:          customerInfo.GetUid(),
            Account:      customerInfo.GetAccount(),
            Nickname:     customerInfo.GetNickname(),
            AccountAlias: customerInfo.GetAccountAlias(),
            Sex:          customerInfo.GetSex(),
            HeadImgMd5:   customerInfo.GetHeadImgMd5(),
            HeadDyImgMd5: customerInfo.GetHeadDyImgMd5(),
        },
    }

    // 通知hall服务,记录用户真实联系的大神
    goroutineex.GoroutineWithTimeoutCtx(ctx, 5*time.Second, func(ctx context.Context) {
        _, err = mgr.esportHallCLi.SetUserToRealCoach(ctx, &esport_hall.SetUserToRealCoachRequest{
            Uid:         svrInfo.UserID,
            CustomerUid: customerInfo.GetUid(),
            CoachUid:    req.GetCoachId(),
            GameId:      req.GetGameId(),
        })
        if err != nil {
            log.ErrorWithCtx(ctx, "[ContactCustomerService] SetCustomerToRealCoach failed: %v", err)
        }
    })

    // 组装大神卡片信息
    coachCard, err := mgr.genEsportCoachSelfIntroIMMsg(ctx, svrInfo.UserID, req.GetCoachId(), req.GetGameId())
    if err != nil {
        log.ErrorWithCtx(ctx, "[ContactCustomerService] genEsportCoachSelfIntroIMMsg failed: %v", err)
    } else {
        // 发送im消息
        go func() {
            customerUid := resp.GetCustomerAccounts().GetCustomer().GetCustomerUid()
            guildId := resp.GetCustomerAccounts().GetCustomer().GetGuildId()

            toUserExtMsgByte, _ := proto.Marshal(coachCard)
            toUserMsg := &trade_im_notify.UserMsg{
                Uid:     svrInfo.UserID,
                ExtMsg:  toUserExtMsgByte,
                Content: "",
            }
            toCustomerMsg := &trade_im_notify.UserMsg{
                Uid:    customerUid,
                ExtMsg: toUserExtMsgByte,
            }

            ctxN, cancel := protoGrpc.NewContextWithInfoTimeout(ctx, 5*time.Second)
            defer cancel()
            log.DebugWithCtx(ctxN, "[ContactCustomerService] SendImMsg toUserMsg: %+v, toCustomerMsg: %+v", toUserMsg, toCustomerMsg)
            // 给客服和用户的IM推送大神卡片
            _, _, err := mgr.esportImCli.SendImMsg(ctxN, uint32(imPB.IM_MSG_TYPE_ESPORT_GAME_SELF_INTRO_TO_VISITOR_V2), toCustomerMsg, toUserMsg)
            if err != nil {
                log.ErrorWithCtx(ctxN, "[ContactCustomerService] SendImMsg failed: %v, req:%v, user_id: %d", err, req, svrInfo.UserID)
                return
            }
            // 给用户推送系统消息，提示当前是客服
            // 先获取公会信息
            // 获取公会信息
            guild, perr := mgr.guildCli.GetGuild(ctxN, guildId)
            if perr != nil {
                log.ErrorWithCtx(ctxN, "ContactCustomerService send im GetGuild error: %v,guild_id: %d", perr, guildId)
                return
            }

            toUserSysMsg := &trade_im_notify.ImMsg{
                Content: fmt.Sprintf("%s公会接待为您服务", guild.GetName()),
            }

            _, err = mgr.esportImCli.SendOneSideImMsg(ctxN, uint32(imPB.IM_MSG_TYPE_SYSTEM_NOTIFY), customerUid, svrInfo.UserID, 1, toUserSysMsg)
            if err != nil {
                log.ErrorWithCtx(ctxN, "ContactCustomerService send im SendOneSideImMsg error: %v, user_id: %d, customer_id: %d ", err, svrInfo.UserID, customerUid)
            }

        }()
    }

    // 返回响应
    return out, nil
}

// SendCoachSkillCard 发送大神名片
func (mgr *EsportHallMgr) SendCoachSkillCard(ctx context.Context, req *esport_http.SendCoachSkillCardRequest) (*esport_http.SendCoachSkillCardResponse, error) {
    out := &esport_http.SendCoachSkillCardResponse{}
    log.DebugWithCtx(ctx, "[SendCoachSkillCard] req: %+v", req)
    // 获取当前请求的客服用户
    svrInfo, ok := protoGrpc.ServiceInfoFromContext(ctx)
    if !ok {
        log.ErrorWithCtx(ctx, "[SendCoachSkillCard] ServiceInfoFromContext fail. in:%+v", req)
        return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "缺少请求上下文信息")
    }

    customerUid := svrInfo.UserID

    // 参数校验
    if req.GetCoachId() == 0 {
        log.ErrorWithCtx(ctx, "[SendCoachSkillCard] CoachId is empty")
        return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "CoachId is empty")
    }

    // 组装大神卡片信息
    coachCard, err := mgr.genEsportCoachSelfIntroIMMsg(ctx, req.GetUserId(), req.GetCoachId(), req.GetGameId())
    if err != nil {
        log.ErrorWithCtx(ctx, "[SendCoachSkillCard] genEsportCoachSelfIntroIMMsg failed: %v", err)
        return out, protocol.NewExactServerError(nil, status.ErrEsportsCommonErr, "genEsportCoachSelfIntroIMMsg failed")
    }
    toUserExtMsgByte, _ := proto.Marshal(coachCard)
    toUserMsg := &trade_im_notify.UserMsg{
        Uid:     req.GetUserId(),
        ExtMsg:  toUserExtMsgByte,
        Content: "",
    }
    toCustomerMsg := &trade_im_notify.UserMsg{
        Uid:    customerUid,
        ExtMsg: toUserExtMsgByte,
    }
    log.DebugWithCtx(ctx, "[SendCoachSkillCard] SendImMsg toUserMsg: %+v, toCustomerMsg: %+v", toUserMsg, toCustomerMsg)
    // 给客服和用户的IM推送大神卡片
    _, _, err = mgr.esportImCli.SendImMsg(ctx, uint32(imPB.IM_MSG_TYPE_ESPORT_GAME_SELF_INTRO_TO_VISITOR_V2), toCustomerMsg, toUserMsg)
    if err != nil {
        log.ErrorWithCtx(ctx, "[SendCoachSkillCard] SendImMsg failed: %v, req:%v, user_id: %d", err, req, svrInfo.UserID)
        return out, protocol.NewExactServerError(nil, status.ErrEsportsCommonErr, "SendImMsg failed")
    }
    return out, nil
}

// GetCoachListForCustomer 客服获取公会旗下的大神列表（分页）
func (mgr *EsportHallMgr) GetCoachListForCustomer(ctx context.Context, req *esport_http.GetCoachListForCustomerRequest) (*esport_http.GetCoachListForCustomerResponse, error) {
    out := &esport_http.GetCoachListForCustomerResponse{}
    defer func() {
        log.DebugWithCtx(ctx, "[GetCoachListForCustomer] req: %+v, resp: %v", req, out)
    }()
    // 获取当前请求的客服用户
    svrInfo, ok := protoGrpc.ServiceInfoFromContext(ctx)
    if !ok {
        log.ErrorWithCtx(ctx, "[GetCoachListForCustomer] ServiceInfoFromContext fail. in:%+v", req)
        return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "缺少请求上下文信息")
    }

    // 调用客户服务获取当前客服信息
    customerRes, err := mgr.customerCli.GetCustomerAccountDetails(ctx, &esport_customer.GetCustomerAccountDetailsRequest{
        CustomerUid: svrInfo.UserID,
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "[GetCoachListForCustomer] GetCustomerAccountDetails failed: %v", err)
        return out, protocol.NewExactServerError(err, status.ErrEsportsCommonErr, "GetCustomerAccountDetails failed")
    }

    // 判断是否是客服
    if customerRes.GetCustomerAccount().GetCustomerUid() == 0 {
        log.ErrorWithCtx(ctx, "[GetCoachListForCustomer] customer account is not customer: %d", svrInfo.UserID)
        return out, protocol.NewExactServerError(err, status.ErrEsportsCommonErr, "当前账号不是客服")
    }

    // 判断账号是否封禁了
    if customerRes.GetCustomerAccount().GetStatus() != esport_customer.CustomerAccountStatus_CUSTOMER_ACCOUNT_STATUS_NORMAL {
        log.ErrorWithCtx(ctx, "[GetCoachListForCustomer] customer account is banned")
        return out, protocol.NewExactServerError(nil, status.ErrEsportsCommonErr, "当前账号已被封禁")
    }

    // 调用角色服务获取公会的所有大神
    roleRes, err := mgr.esportRoleCli.GetCoachByGuild(ctx, &esport_role.GetCoachByGuildReq{
        GuildId: customerRes.GetCustomerAccount().GetGuildId(),
        Page:    1,
        Size:    1000,
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "[GetCoachListForCustomer] GetCoachByGuild failed: %v", err)
        return out, protocol.NewExactServerError(err, status.ErrEsportsCommonErr, "GetCoachByGuild failed")
    }
    targetCoachUids := roleRes.GetCoachUids()
    // 如果请求参数中存在已经看过的大神uid列表，则还需要排除掉
    if len(req.GetViewedUid()) > 0 {
        targetCoachUids = exclude(targetCoachUids, req.GetViewedUid())
    }

    // 如果有传查询条件，则还需要根据查询条件查询大神id，然后和公会的大神id做交集
    if req.GetCondition() != "" {
        searchResultsCoachUids := mgr.searchCoach(ctx, req.GetCondition(), targetCoachUids)
        log.InfoWithCtx(ctx, "[GetCoachListForCustomer] searchCoach result: %v", searchResultsCoachUids)
        // 取交集
        targetCoachUids = intersect(targetCoachUids, searchResultsCoachUids)
    }
    // 如果没有大神，则直接返回
    if len(targetCoachUids) == 0 {
        log.InfoWithCtx(ctx, "[GetCoachListForCustomer] searchCoach result is empty, req: %v", req)
        return out, nil
    }

    log.InfoWithCtx(ctx, "[GetCoachListForCustomer] targetCoachUids: %v, customer_id: %d", targetCoachUids, svrInfo.UserID)

    result, nextOffset, err := mgr.getRecommendCoachListV2(ctx, req.GetGameId(), targetCoachUids, req.GetPageOffset(), req.GetPageSize())
    if err != nil {
        log.ErrorWithCtx(ctx, "[GetCoachListForCustomer] getRecommendCoachList failed: %v", err)
        return out, protocol.NewExactServerError(err, status.ErrEsportsCommonErr, "getRecommendCoachList failed")
    }
    // 组装数据并返回
    out.NextPageOffset = nextOffset
    out.CoachList = result
    return out, nil
}

// IM卡片消息组装推送
func (mgr *EsportHallMgr) genEsportCoachSelfIntroIMMsg(ctx context.Context, uid, coachUid, gameId uint32) (*esport_logic.EsportCoachSelfIntroIMMsg, error) {
    out := &esport_logic.EsportCoachSelfIntroIMMsg{}

    // 查询大神的账号信息
    userProfile, serverErr := mgr.userProfileCli.GetUserProfileV2(ctx, coachUid, false)
    if serverErr != nil {
        log.ErrorWithCtx(ctx, "genEsportCoachSelfIntroIMMsg GetUserProfileV2 fail,coachId:%d err:%v", coachUid, serverErr)
        return out, serverErr
    }

    // 根据gameId获取商品信息
    productResp, err := mgr.esportHallCLi.GetSkillProductInfoByGameId(ctx, &esport_hall.GetSkillProductInfoByGameIdRequest{
        GameId:   gameId,
        CoachUid: coachUid,
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "genEsportCoachSelfIntroIMMsg GetSkillProductInfoByGameId fail,gameId:%d,coachId:%d err:%v", gameId, coachUid, err)
        //return out, err
    }

    // 通过gameId获取大神游戏信息
    gameInfoResp, err := mgr.esportSkillCli.GetUserSkillByGameId(ctx, &esport_skill.GetUserSkillByGameIdRequest{
        Uid:    coachUid,
        GameId: gameId,
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "genEsportCoachSelfIntroIMMsg GetUserSkillByGameId fail,gameId:%d,coachId:%d err:%v", gameId, coachUid, err)
        return out, err
    }

    skillInfo := gameInfoResp.GetCurrentSkill()
    if skillInfo == nil {
        log.ErrorWithCtx(ctx, "genEsportCoachSelfIntroIMMsg GetUserSkillByGameId fail,gameId:%d,coachId err:%v", gameId, coachUid, err)
        return out, errors.New("gameId2skillInfo is nil")
    }

    itemConf := mgr.bc.GetImSelfIntroCardConfig()

    // 资料卡片需要展示选项
    sectionMap := make(map[string]struct{})
    for _, v := range itemConf.SectionNameList {
        sectionMap[v] = struct{}{}
    }

    // 包赢文案是否需要展示
    needGuaranteeShow := itemConf.GuaranteeWinningText && skillInfo.GetIsGuaranteeWin()

    itemList := make([]*esport_logic.EsportIMGameSelfIntroItem, 0)
    // 遍历技能信息组装选项信息
    for _, v := range skillInfo.GetSectionList() {
        if len(v.GetItemList()) == 0 {
            continue
        }
        if _, ok := sectionMap[v.GetSectionName()]; ok {
            itemContent := strings.Join(v.GetItemList(), " ")
            itemList = append(itemList, &esport_logic.EsportIMGameSelfIntroItem{
                Title:   v.GetSectionName() + "：",
                Content: itemContent,
            })
            continue
        }

        if needGuaranteeShow && v.GetSectionName() == GuaranteeWinSectionName {
            out.GuaranteeWinText = v.GetItemList()[0]
        }
    }

    // 用户个性签名、语音介绍
    if itemConf.SelfVideoIntro {
        out.AudioIntro = mgr.bc.GetAudioIntroUrlPrefix() + skillInfo.GetAudio() // 拼接完整前缀
        out.AudioDuration = skillInfo.GetAudioDuration()
    }
    out.TextIntro = "“" + skillInfo.GetTextDesc() + "”"
    out.ItemList = itemList
    out.ProductId = uint64(productResp.GetProduct().GetId())
    out.OuterText = "你刚刚查看了这位大神~"
    out.GameId = skillInfo.GetGameId()
    out.CoachProfile = userProfile

    // 卡片可下单过期时间
    out.ExpireTs = 0 // 2024-05-11 -hotfix 改成不过期

    log.DebugWithCtx(ctx, "genEsportCoachSelfIntroIMMsg, coachUid:%dout: %+v", coachUid, out)
    return out, nil
}

func (mgr *EsportHallMgr) searchCoach(ctx context.Context, condition string, targetCoachUids []uint32) []uint32 {
    // 判断condition是否为全数字
    if _, err := strconv.Atoi(condition); err == nil {
        if uid, _, serverError := mgr.accountCli.GetUidByName(ctx, condition); serverError == nil {
            return []uint32{uid}
        } else {
            log.ErrorWithCtx(ctx, "searchCoach GetUidByName err: %v, condition: %s", serverError, condition)
            return []uint32{}
        }
    } else {
        users, err := mgr.userESService.SearchNicknameAndUids(ctx, condition, targetCoachUids)
        if err != nil {
            log.ErrorWithCtx(ctx, "searchCoach SearchNicknameKeyword err: %v, condition: %s", err, condition)
            return []uint32{}
        }
        // 抽取uid
        uids := make([]uint32, 0, len(users))
        for _, item := range users {
            uids = append(uids, uint32(item.Uid))
        }
        return uids
    }
}

// getRecommendCoachList 获取推荐的教练列表
// coachIdFilter 为过滤的教练id列表， 只查询列表中存在的教练
// offset, pageSize 为分页参数
// 返回值为教练列表，下一个offset，错误信息
func (mgr *EsportHallMgr) getRecommendCoachList(ctx context.Context, gameId uint32, coachIdFilter []uint32, offset, pageSize uint32) ([]*esport_http.EsportAreaCoachInfo, uint32, error) {

    // 查询游戏信息
    gameDetail, err := mgr.esportSkillCli.GetGameDetailById(ctx, &esport_skill.GetGameDetailByIdRequest{
        GameId: gameId,
    })
    if err != nil {
        return nil, 0, fmt.Errorf("GetRecommendCoachList GetGameDetailById err: %w", err)
    }
    coachListResp, err := mgr.esportHallCLi.GetGameCoachListByUid(ctx, &esport_hall.GetGameCoachListByUidRequest{
        GameId:       gameId,
        CoachUidList: coachIdFilter,
        Offset:       offset,
        Limit:        pageSize,
        ExposeType:   uint32(esport_logic.ReportExposeCoachRequest_EXPOSE_COACH_TYPE_ESPORT_HELPER_ACTIVITY),
    })
    if err != nil {
        return nil, 0, fmt.Errorf("GetRecommendCoachList GetGameCoachList req: %+v, err: %w", gameId, err)
    }
    if len(coachListResp.GetCoachList()) == 0 {
        return []*esport_http.EsportAreaCoachInfo{}, 0, nil
    }

    uidList := make([]uint32, 0, len(coachListResp.GetCoachList()))
    for _, item := range coachListResp.GetCoachList() {
        uidList = append(uidList, item.GetUid())
    }

    userprofileMap, err := mgr.getUserProfileWithDyHead(ctx, uidList)
    if err != nil {
        return nil, 0, fmt.Errorf("GetRecommendCoachList getUserProfileWithDyHead uidList: %+v, err: %w", uidList, err)
    }

    userSkillInfo, err := mgr.esportSkillCli.BatchGetUserCurrentSkill(ctx, &esport_skill.BatchGetUserCurrentSkillRequest{
        Uid: uidList,
    })
    if err != nil {
        return nil, 0, fmt.Errorf("GetRecommendCoachList getUserProfileWithDyHead uidList: %+v, err: %w", uidList, err)
    }

    skillInfoMap := make(map[uint32]*esport_skill.UserSkillInfo)
    for _, item := range userSkillInfo.GetList() {
        for _, skillItem := range item.GetSkill() {
            if skillItem.GetGameId() != gameId {
                continue
            }
            skillInfoMap[item.GetUid()] = skillItem
            break
        }
    }
    // 获取标签
    famousPlayerMap := make(map[uint32]bool)
    coachLabelResp, err := mgr.esportSkillCli.BatchGetCoachLabelsForGame(ctx, &esport_skill.BatchGetCoachLabelsForGameRequest{
        GameId:   gameId,
        CoachIds: uidList,
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "batchGetCoachLabelInfo BatchGetCoachLabelsForGame, uidList: %+v, gameId: %d, err: %v", uidList, gameId, err)
    } else {
        for _, item := range coachLabelResp.GetLabelList() {
            famousPlayerMap[item.GetCoachId()] = item.GetIsRenowned()
        }
    }
    result := make([]*esport_http.EsportAreaCoachInfo, 0, len(coachListResp.GetCoachList()))
    for _, item := range coachListResp.GetCoachList() {
        up := userprofileMap[item.GetUid()]
        if up == nil {
            log.WarnWithCtx(ctx, "GetRecommendCoachList userprofile is nil, uid: %d", item.GetUid())
            continue
        }
        coachItem := &esport_http.EsportAreaCoachInfo{
            UserProfile:      up,
            TextDesc:         skillInfoMap[item.GetUid()].GetTextDesc(),
            GuaranteeWinText: "",
            Price: &esport_http.ProductOrderPriceInfo{
                Price:       item.GetPrice(),
                PriceUnit:   "豆",
                MeasureCnt:  30,
                MeasureUnit: "分钟",
            },
            IsFamousPlayer: famousPlayerMap[item.GetUid()],
            VoiceDesc:      skillInfoMap[item.GetUid()].GetAudio(),
            Tone:           "",
        }
        // 调整价格信息
        realPriceType := gameDetail.GetConfig().GetGamePricing().GetGamePricingUnitType()
        if item.GetUintType() > 0 {
            realPriceType = esport_skill.GAME_PRICING_UNIT_TYPE(item.GetUintType())
        }
        if realPriceType == esport_skill.GAME_PRICING_UNIT_TYPE_GAME_PRICING_UNIT_TYPE_PER_GAME {
            coachItem.Price.MeasureCnt = 1
            coachItem.Price.MeasureUnit = "局"
        }
        // 包赢文案
        if skill, ok := skillInfoMap[item.GetUid()]; ok {
            if skill.GetIsGuaranteeWin() {
                for _, section := range skill.GetSectionList() {
                    if section.GetSectionName() == GuaranteeWinSectionName && len(section.GetItemList()) > 0 {
                        coachItem.GuaranteeWinText = section.GetItemList()[0]
                        break
                    }
                }
            }
        }
        result = append(result, coachItem)
    }
    return result, coachListResp.GetNextOffset(), nil
}

// getRecommendCoachList 获取推荐的教练列表
// coachIdFilter 为过滤的教练id列表， 只查询列表中存在的教练
// offset, pageSize 为分页参数
// 返回值为教练列表，下一个offset，错误信息
func (mgr *EsportHallMgr) getRecommendCoachListV2(ctx context.Context, gameId uint32, coachIdFilter []uint32, offset, pageSize uint32) ([]*esport_http.EsportAreaCoachInfo, uint32, error) {

    // 查询游戏信息
    gameDetail, err := mgr.esportSkillCli.GetGameDetailById(ctx, &esport_skill.GetGameDetailByIdRequest{
        GameId: gameId,
    })
    if err != nil {
        return nil, 0, fmt.Errorf("GetRecommendCoachList GetGameDetailById err: %w", err)
    }
    coachListResp, err := mgr.esportHallCLi.SearchCoach(ctx, &esport_hall.SearchCoachRequest{
        GameId:       gameId,
        CoachUidList: coachIdFilter,
        Offset:       offset,
        Limit:        pageSize,
    })
    if err != nil {
        return nil, 0, fmt.Errorf("GetRecommendCoachList GetGameCoachList req: %+v, err: %w", gameId, err)
    }
    if len(coachListResp.GetCoachList()) == 0 {
        return []*esport_http.EsportAreaCoachInfo{}, 0, nil
    }

    uidList := make([]uint32, 0, len(coachListResp.GetCoachList()))
    for _, item := range coachListResp.GetCoachList() {
        uidList = append(uidList, item.GetUid())
    }

    userprofileMap, err := mgr.getUserProfileWithDyHead(ctx, uidList)
    if err != nil {
        return nil, 0, fmt.Errorf("GetRecommendCoachList getUserProfileWithDyHead uidList: %+v, err: %w", uidList, err)
    }

    userSkillInfo, err := mgr.esportSkillCli.BatchGetUserCurrentSkill(ctx, &esport_skill.BatchGetUserCurrentSkillRequest{
        Uid: uidList,
    })
    if err != nil {
        return nil, 0, fmt.Errorf("GetRecommendCoachList getUserProfileWithDyHead uidList: %+v, err: %w", uidList, err)
    }

    skillInfoMap := make(map[uint32]*esport_skill.UserSkillInfo)
    for _, item := range userSkillInfo.GetList() {
        for _, skillItem := range item.GetSkill() {
            if skillItem.GetGameId() != gameId {
                continue
            }
            skillInfoMap[item.GetUid()] = skillItem
            break
        }
    }
    // 获取标签
    famousPlayerMap := make(map[uint32]bool)
    coachLabelResp, err := mgr.esportSkillCli.BatchGetCoachLabelsForGame(ctx, &esport_skill.BatchGetCoachLabelsForGameRequest{
        GameId:   gameId,
        CoachIds: uidList,
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "batchGetCoachLabelInfo BatchGetCoachLabelsForGame, uidList: %+v, gameId: %d, err: %v", uidList, gameId, err)
    } else {
        for _, item := range coachLabelResp.GetLabelList() {
            famousPlayerMap[item.GetCoachId()] = item.GetIsRenowned()
        }
    }
    result := make([]*esport_http.EsportAreaCoachInfo, 0, len(coachListResp.GetCoachList()))
    for _, item := range coachListResp.GetCoachList() {
        up := userprofileMap[item.GetUid()]
        if up == nil {
            log.WarnWithCtx(ctx, "GetRecommendCoachList userprofile is nil, uid: %d", item.GetUid())
            continue
        }
        coachItem := &esport_http.EsportAreaCoachInfo{
            UserProfile:      up,
            TextDesc:         skillInfoMap[item.GetUid()].GetTextDesc(),
            GuaranteeWinText: "",
            Price: &esport_http.ProductOrderPriceInfo{
                Price:       item.GetPrice(),
                PriceUnit:   "豆",
                MeasureCnt:  30,
                MeasureUnit: "分钟",
            },
            IsFamousPlayer: famousPlayerMap[item.GetUid()],
        }
        // 调整价格信息
        realPriceType := gameDetail.GetConfig().GetGamePricing().GetGamePricingUnitType()
        if item.GetUintType() > 0 {
            realPriceType = esport_skill.GAME_PRICING_UNIT_TYPE(item.GetUintType())
        }
        if realPriceType == esport_skill.GAME_PRICING_UNIT_TYPE_GAME_PRICING_UNIT_TYPE_PER_GAME {
            coachItem.Price.MeasureCnt = 1
            coachItem.Price.MeasureUnit = "局"
        }
        // 包赢文案
        if skill, ok := skillInfoMap[item.GetUid()]; ok {
            if skill.GetIsGuaranteeWin() {
                for _, section := range skill.GetSectionList() {
                    if section.GetSectionName() == GuaranteeWinSectionName && len(section.GetItemList()) > 0 {
                        coachItem.GuaranteeWinText = section.GetItemList()[0]
                        break
                    }
                }
            }
        }
        result = append(result, coachItem)
    }
    return result, coachListResp.GetNextOffset(), nil
}

// intersect 取两个切片的交集
func intersect(slice1, slice2 []uint32) []uint32 {
    m := make(map[uint32]bool)
    for _, item := range slice1 {
        m[item] = true
    }

    var res []uint32
    for _, item := range slice2 {
        if _, ok := m[item]; ok {
            res = append(res, item)
        }
    }
    return res
}

// exclude 一个切片排除另外一个切片的元素
func exclude(main, exclude []uint32) []uint32 {
    m := make(map[uint32]bool)
    for _, item := range exclude {
        m[item] = true
    }

    var res []uint32
    for _, item := range main {
        if _, ok := m[item]; !ok {
            res = append(res, item)
        }
    }
    return res
}

func (mgr *EsportHallMgr) GetFirstRoundDiscountInfo(ctx context.Context, in *esport_http.GetFirstRoundDiscountInfoRequest) (out *esport_http.GetFirstRoundDiscountInfoResponse, err error) {
    out = &esport_http.GetFirstRoundDiscountInfoResponse{}
    svrInfo, ok := protoGrpc.ServiceInfoFromContext(ctx)
    if !ok {
        err = protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "缺少请求上下文信息")
        return
    }

    hallRsp, err := mgr.esportHallCLi.GetFirstRoundDiscountInfo(ctx, &esport_hall.GetFirstRoundDiscountInfoRequest{
        Uid:    svrInfo.UserID,
        GameId: in.GameId,
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "GetFirstRoundDiscountInfo err: %v", err)
        return
    }

    out.ShowSwitch = hallRsp.GetShowSwitch()
    out.FirstRoundPrice = hallRsp.GetFirstRoundPrice()
    out.IsOpen = hallRsp.GetIsOpen()
    return
}

func (mgr *EsportHallMgr) SetFirstRoundSwitch(ctx context.Context, in *esport_http.SetFirstRoundSwitchRequest) (out *esport_http.SetFirstRoundSwitchResponse, err error) {
    out = &esport_http.SetFirstRoundSwitchResponse{}
    svrInfo, ok := protoGrpc.ServiceInfoFromContext(ctx)
    if !ok {
        err = protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "缺少请求上下文信息")
        return
    }

    _, err = mgr.esportHallCLi.SetFirstRoundSwitch(ctx, &esport_hall.SetFirstRoundSwitchRequest{
        Uid:    svrInfo.UserID,
        GameId: in.GameId,
        IsOpen: in.IsOpen,
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "SetFirstRoundSwitch err: %v", err)
        return
    }

    return
}

func (mgr *EsportHallMgr) GetFirstRoundDiscountGameList(ctx context.Context, in *esport_http.GetFirstRoundDiscountGameListRequest) (out *esport_http.GetFirstRoundDiscountGameListResponse, err error) {
    out = &esport_http.GetFirstRoundDiscountGameListResponse{GameList: make([]*esport_http.FirstRoundDiscountGameInfo, 0)}

    discountRsp, err := mgr.esportHallCLi.GetFirstRoundDiscountGameList(ctx, &esport_hall.GetFirstRoundDiscountGameListRequest{})
    if err != nil {
        log.ErrorWithCtx(ctx, "GetFirstRoundDiscountGameList err: %v", err)
        return
    }
    priceMap := make(map[uint32]uint32)
    for _, item := range discountRsp.GetGameList() {
        priceMap[item.GetGameId()] = item.GetFirstRoundPrice()
    }

    gameRsp, err := mgr.esportSkillCli.GetAllGameSimpleInfo(ctx, &esport_skill.GetAllGameSimpleInfoRequest{})
    if err != nil {
        log.ErrorWithCtx(ctx, "GetAllGameSimpleInfo err: %v", err)
        return
    }

    for _, item := range gameRsp.GetGameList() {
        price, ok := priceMap[item.GetGameId()]
        if !ok {
            continue
        }

        out.GameList = append(out.GameList, &esport_http.FirstRoundDiscountGameInfo{
            GameId:          item.GetGameId(),
            GameName:        item.GetGameName(),
            FirstRoundPrice: price,
        })
    }

    return
}

func (mgr *EsportHallMgr) RefreshDiscountInfo(ctx context.Context, in *esport_http.RefreshDiscountInfoRequest) (out *esport_http.RefreshDiscountInfoResponse, err error) {
    out = &esport_http.RefreshDiscountInfoResponse{}
    svrInfo, ok := protoGrpc.ServiceInfoFromContext(ctx)
    if !ok {
        err = protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "缺少请求上下文信息")
        return
    }

    info, err := mgr.esportInternalCli.BatchGetCoachPricingInfo(ctx, &esport_internal.BatchGetCoachPricingInfoRequest{
        Uid:         svrInfo.UserID,
        CoachUid:    []uint32{in.GetCoachUid()},
        GameId:      in.GetGameId(),
        BuyAmount:   1,
        QueryOption: &esport_internal.CouponQueryOption{DoNotCheckVersion: true},
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "RefreshDiscountInfo BatchGetCoachPricingInfo err: %v", err)
        return
    }
    if price, ok := info.GetPriceMap()[in.GetCoachUid()]; ok {
        if len(price.GetDiscountList()) > 0 {
            // 因为目前业务只允许一个优惠，所以直接取第一个
            discountInfo := price.GetDiscountList()[0]
            out.HasDiscount = true
            out.DiscountPrice = discountInfo.GetPrice()
            out.DiscountType = uint32(discountInfo.GetType())
            out.DiscountDesc = discountInfo.GetDesc()
            // 额外处理原来的逻辑
            switch discountInfo.GetType() {
            case esport_internal.DiscountType_DISCOUNT_TYPE_FIRST_ORDER:
                out.HasFirstRoundDiscount = true
                out.FirstRoundPrice = discountInfo.GetPrice()
            }
        }
    } else {
        log.WarnWithCtx(ctx, "RefreshDiscountInfo BatchGetCoachPricingInfo price not found, uid: %d, coachUid: %d, gameId: %d", svrInfo.UserID, in.GetCoachUid(), in.GetGameId())
    }

    return
}

func (mgr *EsportHallMgr) GetNewCustomerDiscountInfo(ctx context.Context, in *esport_http.GetNewCustomerDiscountInfoRequest) (out *esport_http.GetNewCustomerDiscountInfoResponse, err error) {
    out = &esport_http.GetNewCustomerDiscountInfoResponse{}
    svrInfo, ok := protoGrpc.ServiceInfoFromContext(ctx)
    if !ok {
        err = protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "缺少请求上下文信息")
        return
    }

    hallRsp, err := mgr.esportHallCLi.GetNewCustomerDiscountInfo(ctx, &esport_hall.GetNewCustomerDiscountInfoRequest{
        Uid:    svrInfo.UserID,
        GameId: in.GameId,
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "GetNewCustomerDiscountInfo err: %v", err)
        return
    }

    out.ShowSwitch = hallRsp.GetShowSwitch()
    out.NewCustomerPrice = hallRsp.GetNewCustomerPrice()
    out.PlatBonusFee = hallRsp.GetPlatBonusFee()
    out.IsOpen = hallRsp.GetIsOpen()
    return
}

func (mgr *EsportHallMgr) SetNewCustomerSwitch(ctx context.Context, in *esport_http.SetNewCustomerSwitchRequest) (out *esport_http.SetNewCustomerSwitchResponse, err error) {
    out = &esport_http.SetNewCustomerSwitchResponse{}
    svrInfo, ok := protoGrpc.ServiceInfoFromContext(ctx)
    if !ok {
        err = protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "缺少请求上下文信息")
        return
    }

    _, err = mgr.esportHallCLi.SetNewCustomerSwitch(ctx, &esport_hall.SetNewCustomerSwitchRequest{
        Uid:    svrInfo.UserID,
        GameId: in.GameId,
        IsOpen: in.IsOpen,
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "SetNewCustomerSwitch err: %v", err)
        return
    }

    return
}

func (mgr *EsportHallMgr) ReportExposeCoach(ctx context.Context, request *esport_http.ReportExposeCoachRequest) (*esport_http.ReportExposeCoachResponse, error) {
    resp := &esport_http.ReportExposeCoachResponse{}
    serviceInfo, ok := protoGrpc.ServiceInfoFromContext(ctx)
    if !ok {
        log.ErrorWithCtx(ctx, "ReportExposeCoach fail, err: get serviceInfo fail")
        return resp, nil
    }
    log.InfoWithCtx(ctx, "ReportExposeCoach, uid: %d, gameId: %d, exposeCoachList: %+v", serviceInfo.UserID, request.GetGameId(), request.GetExposeCoachList())

    _, err := mgr.esportHallCLi.ReportExposeCoach(ctx, &esport_hall.ReportExposeCoachRequest{
        Uid:             serviceInfo.UserID,
        ExposeCoachList: request.GetExposeCoachList(),
        GameId:          request.GetGameId(),
        ExposeType:      request.GetExposeType(),
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "ReportExposeCoach fail, request: %+v, err: %d", request, err)
    }
    return resp, err
}

func (mgr *EsportHallMgr) GetActivityGameProperty(ctx context.Context, req *esport_http.GetActivityGamePropertyRequest) (*esport_http.GetActivityGamePropertyResponse, error) {
    resp := &esport_http.GetActivityGamePropertyResponse{}

    // 获取活动配置, 如果没配置则直接打断
    activityGamePropertyConf := mgr.bc.GetEsportActivityGamePropertyConf(req.GetSceneType(), req.GetGameId())
    if activityGamePropertyConf == nil {
        log.WarnWithCtx(ctx, "GetActivityGameProperty, conf is nil, sceneType: %d, gameId: %d", req.GetSceneType(), req.GetGameId())
        return resp, nil
    }

    // 组装音色标签筛选项
    if activityGamePropertyConf.IsNeedVoiceLabel {
        voiceLabel, err := mgr.esportSkillCli.GetSpecialLabelList(ctx, &esport_skill.GetSpecialLabelListRequest{
            LabelType: esport_skill.SpecialLabelType_SPECIAL_LABEL_TYPE_VOICE,
            GameId:    req.GetGameId(),
        })
        if err != nil {
            log.ErrorWithCtx(ctx, "GetSpecialLabelList err: %v", err)
            return resp, err
        }

        valList := make([]*esport_hall.GamePropertyVal, 0)

        for _, item := range voiceLabel.GetLabelList() {
            valList = append(valList, &esport_hall.GamePropertyVal{
                Name: item.GetLabelName(),
            })
        }

        gpItem := &esport_hall.GameProperty{
            Name:         "选择音色",
            ValList:      valList,
            PropertyType: uint32(esport_hall.GameProperty_PROPERTY_TYPE_LABEL),
        }

        resp.PropertyList = append(resp.PropertyList, gpItem)
    }

    // 组装游戏资料筛选项
    if len(activityGamePropertyConf.GamePropertySelection) > 0 {
        gameConf, err := mgr.esportSkillCli.GetGameDetailById(ctx, &esport_skill.GetGameDetailByIdRequest{
            GameId: req.GetGameId(),
        })
        if err != nil {
            log.ErrorWithCtx(ctx, "GetGameDetailById err: %v", err)
            return resp, err
        }
        gameInfoMap := make(map[string]*esport_skill.GameInformation)
        for _, item := range gameConf.GetConfig().GetGameInformationList() {
            gameInfoMap[item.GetSectionName()] = item
        }

        for _, item := range activityGamePropertyConf.GamePropertySelection {
            gameInfoConf, ok := gameInfoMap[item]
            if !ok {
                continue
            }

            valList := make([]*esport_hall.GamePropertyVal, 0)
            for _, valItem := range gameInfoConf.GetItemList() {
                valList = append(valList, &esport_hall.GamePropertyVal{
                    Name: valItem,
                })
            }

            gpItem := &esport_hall.GameProperty{
                Name:         item,
                ValList:      valList,
                PropertyType: uint32(esport_hall.GameProperty_PROPERTY_TYPE_CUSTOM),
            }

            resp.PropertyList = append(resp.PropertyList, gpItem)
        }
    }

    // 组装特色标签筛选项
    if activityGamePropertyConf.IsNeedSpecialLabel {
        specialLabel, err := mgr.esportSkillCli.GetSpecialLabelList(ctx, &esport_skill.GetSpecialLabelListRequest{
            LabelType: esport_skill.SpecialLabelType_SPECIAL_LABEL_TYPE_SPECIAL,
            GameId:    req.GetGameId(),
        })
        if err != nil {
            log.ErrorWithCtx(ctx, "GetSpecialLabelList err: %v", err)
            return resp, err
        }

        valList := make([]*esport_hall.GamePropertyVal, 0)

        for _, item := range specialLabel.GetLabelList() {
            valList = append(valList, &esport_hall.GamePropertyVal{
                Name: item.GetLabelName(),
            })
        }

        gpItem := &esport_hall.GameProperty{
            Name:         "游戏风格",
            ValList:      valList,
            PropertyType: uint32(esport_hall.GameProperty_PROPERTY_TYPE_LABEL),
        }

        resp.PropertyList = append(resp.PropertyList, gpItem)
    }

    return resp, nil
}
