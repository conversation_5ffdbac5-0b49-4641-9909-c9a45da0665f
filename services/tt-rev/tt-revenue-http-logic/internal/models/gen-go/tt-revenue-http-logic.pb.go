// Code generated by protoc-gen-go. DO NOT EDIT.
// source: tt-revenue-http-logic.proto

package api

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// 反馈类型
type FeedbackType int32

const (
	FeedbackType_FEEDBACK_TYPE_INVALID                FeedbackType = 0
	FeedbackType_FEEDBACK_TYPE_LACK_OF_BGM            FeedbackType = 1
	FeedbackType_FEEDBACK_TYPE_TYPESETTING_UNCLEAR    FeedbackType = 2
	FeedbackType_FEEDBACK_TYPE_HOPE_OST               FeedbackType = 3
	FeedbackType_FEEDBACK_TYPE_HOPE_SEQUEL            FeedbackType = 4
	FeedbackType_FEEDBACK_TYPE_LINES_COLOR_GLARING    FeedbackType = 5
	FeedbackType_FEEDBACK_TYPE_WRONG_SOUND_MARK       FeedbackType = 6
	FeedbackType_FEEDBACK_TYPE_BGM_SORTING_ERROR      FeedbackType = 7
	FeedbackType_FEEDBACK_TYPE_WRONG_BGM_SOUND_EFFECT FeedbackType = 8
	FeedbackType_FEEDBACK_TYPE_OTHER                  FeedbackType = 9
)

var FeedbackType_name = map[int32]string{
	0: "FEEDBACK_TYPE_INVALID",
	1: "FEEDBACK_TYPE_LACK_OF_BGM",
	2: "FEEDBACK_TYPE_TYPESETTING_UNCLEAR",
	3: "FEEDBACK_TYPE_HOPE_OST",
	4: "FEEDBACK_TYPE_HOPE_SEQUEL",
	5: "FEEDBACK_TYPE_LINES_COLOR_GLARING",
	6: "FEEDBACK_TYPE_WRONG_SOUND_MARK",
	7: "FEEDBACK_TYPE_BGM_SORTING_ERROR",
	8: "FEEDBACK_TYPE_WRONG_BGM_SOUND_EFFECT",
	9: "FEEDBACK_TYPE_OTHER",
}
var FeedbackType_value = map[string]int32{
	"FEEDBACK_TYPE_INVALID":                0,
	"FEEDBACK_TYPE_LACK_OF_BGM":            1,
	"FEEDBACK_TYPE_TYPESETTING_UNCLEAR":    2,
	"FEEDBACK_TYPE_HOPE_OST":               3,
	"FEEDBACK_TYPE_HOPE_SEQUEL":            4,
	"FEEDBACK_TYPE_LINES_COLOR_GLARING":    5,
	"FEEDBACK_TYPE_WRONG_SOUND_MARK":       6,
	"FEEDBACK_TYPE_BGM_SORTING_ERROR":      7,
	"FEEDBACK_TYPE_WRONG_BGM_SOUND_EFFECT": 8,
	"FEEDBACK_TYPE_OTHER":                  9,
}

func (x FeedbackType) String() string {
	return proto.EnumName(FeedbackType_name, int32(x))
}
func (FeedbackType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_tt_revenue_http_logic_907ee343f90f615b, []int{0}
}

// 原料类型
type MaterialType int32

const (
	MaterialType_MaterialTypeGiftType     MaterialType = 0
	MaterialType_MaterialTypeFragmentType MaterialType = 1
)

var MaterialType_name = map[int32]string{
	0: "MaterialTypeGiftType",
	1: "MaterialTypeFragmentType",
}
var MaterialType_value = map[string]int32{
	"MaterialTypeGiftType":     0,
	"MaterialTypeFragmentType": 1,
}

func (x MaterialType) String() string {
	return proto.EnumName(MaterialType_name, int32(x))
}
func (MaterialType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_tt_revenue_http_logic_907ee343f90f615b, []int{1}
}

type FellowType int32

const (
	FellowType_ENUM_FELLOW_TYPE_UNKNOWN  FellowType = 0
	FellowType_ENUM_FELLOW_TYPE_BRO      FellowType = 1
	FellowType_ENUM_FELLOW_TYPE_LADYBRO  FellowType = 2
	FellowType_ENUM_FELLOW_TYPE_INTIMATE FellowType = 3
)

var FellowType_name = map[int32]string{
	0: "ENUM_FELLOW_TYPE_UNKNOWN",
	1: "ENUM_FELLOW_TYPE_BRO",
	2: "ENUM_FELLOW_TYPE_LADYBRO",
	3: "ENUM_FELLOW_TYPE_INTIMATE",
}
var FellowType_value = map[string]int32{
	"ENUM_FELLOW_TYPE_UNKNOWN":  0,
	"ENUM_FELLOW_TYPE_BRO":      1,
	"ENUM_FELLOW_TYPE_LADYBRO":  2,
	"ENUM_FELLOW_TYPE_INTIMATE": 3,
}

func (x FellowType) String() string {
	return proto.EnumName(FellowType_name, int32(x))
}
func (FellowType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_tt_revenue_http_logic_907ee343f90f615b, []int{2}
}

type FellowBindType int32

const (
	FellowBindType_ENUM_FELLOW_BIND_TYPE_UNKNOWN FellowBindType = 0
	FellowBindType_ENUM_FELLOW_BIND_TYPE_UNIQUE  FellowBindType = 1
	FellowBindType_ENUM_FELLOW_BIND_TYPE_MULTI   FellowBindType = 2
)

var FellowBindType_name = map[int32]string{
	0: "ENUM_FELLOW_BIND_TYPE_UNKNOWN",
	1: "ENUM_FELLOW_BIND_TYPE_UNIQUE",
	2: "ENUM_FELLOW_BIND_TYPE_MULTI",
}
var FellowBindType_value = map[string]int32{
	"ENUM_FELLOW_BIND_TYPE_UNKNOWN": 0,
	"ENUM_FELLOW_BIND_TYPE_UNIQUE":  1,
	"ENUM_FELLOW_BIND_TYPE_MULTI":   2,
}

func (x FellowBindType) String() string {
	return proto.EnumName(FellowBindType_name, int32(x))
}
func (FellowBindType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_tt_revenue_http_logic_907ee343f90f615b, []int{3}
}

// ========================== 荣耀世界祈愿 ==============================
type PondType int32

const (
	PondType_POND_TYPE_NONE   PondType = 0
	PondType_POND_TYPE_NORMAL PondType = 1
	PondType_POND_TYPE_GRAND  PondType = 2
)

var PondType_name = map[int32]string{
	0: "POND_TYPE_NONE",
	1: "POND_TYPE_NORMAL",
	2: "POND_TYPE_GRAND",
}
var PondType_value = map[string]int32{
	"POND_TYPE_NONE":   0,
	"POND_TYPE_NORMAL": 1,
	"POND_TYPE_GRAND":  2,
}

func (x PondType) String() string {
	return proto.EnumName(PondType_name, int32(x))
}
func (PondType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_tt_revenue_http_logic_907ee343f90f615b, []int{4}
}

type UserRoomStatus int32

const (
	UserRoomStatus_USER_ROOM_STATUS_IN_ROOM      UserRoomStatus = 0
	UserRoomStatus_USER_ROOM_STATUS_IN_LIVING    UserRoomStatus = 1
	UserRoomStatus_USER_ROOM_STATUS_IN_PK        UserRoomStatus = 2
	UserRoomStatus_USER_ROOM_STATUS_WATCH_LIVING UserRoomStatus = 3
)

var UserRoomStatus_name = map[int32]string{
	0: "USER_ROOM_STATUS_IN_ROOM",
	1: "USER_ROOM_STATUS_IN_LIVING",
	2: "USER_ROOM_STATUS_IN_PK",
	3: "USER_ROOM_STATUS_WATCH_LIVING",
}
var UserRoomStatus_value = map[string]int32{
	"USER_ROOM_STATUS_IN_ROOM":      0,
	"USER_ROOM_STATUS_IN_LIVING":    1,
	"USER_ROOM_STATUS_IN_PK":        2,
	"USER_ROOM_STATUS_WATCH_LIVING": 3,
}

func (x UserRoomStatus) String() string {
	return proto.EnumName(UserRoomStatus_name, int32(x))
}
func (UserRoomStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_tt_revenue_http_logic_907ee343f90f615b, []int{5}
}

type LotteryResp_WINNING_TYPE int32

const (
	LotteryResp_WINNING_TYPE_UNKNOWN LotteryResp_WINNING_TYPE = 0
	LotteryResp_WINNING_TYPE_BIG     LotteryResp_WINNING_TYPE = 1
	LotteryResp_WINNING_TYPE_FLASH   LotteryResp_WINNING_TYPE = 2
	LotteryResp_WINNING_TYPE_NORMAL  LotteryResp_WINNING_TYPE = 3
	LotteryResp_WINNING_TYPE_NONE    LotteryResp_WINNING_TYPE = 4
)

var LotteryResp_WINNING_TYPE_name = map[int32]string{
	0: "WINNING_TYPE_UNKNOWN",
	1: "WINNING_TYPE_BIG",
	2: "WINNING_TYPE_FLASH",
	3: "WINNING_TYPE_NORMAL",
	4: "WINNING_TYPE_NONE",
}
var LotteryResp_WINNING_TYPE_value = map[string]int32{
	"WINNING_TYPE_UNKNOWN": 0,
	"WINNING_TYPE_BIG":     1,
	"WINNING_TYPE_FLASH":   2,
	"WINNING_TYPE_NORMAL":  3,
	"WINNING_TYPE_NONE":    4,
}

func (x LotteryResp_WINNING_TYPE) String() string {
	return proto.EnumName(LotteryResp_WINNING_TYPE_name, int32(x))
}
func (LotteryResp_WINNING_TYPE) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_tt_revenue_http_logic_907ee343f90f615b, []int{114, 0}
}

// 获取剧本详情
type GetDramaDetailReq struct {
	DramaId              uint32   `protobuf:"varint,1,opt,name=drama_id,json=dramaId,proto3" json:"drama_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetDramaDetailReq) Reset()         { *m = GetDramaDetailReq{} }
func (m *GetDramaDetailReq) String() string { return proto.CompactTextString(m) }
func (*GetDramaDetailReq) ProtoMessage()    {}
func (*GetDramaDetailReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_revenue_http_logic_907ee343f90f615b, []int{0}
}
func (m *GetDramaDetailReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetDramaDetailReq.Unmarshal(m, b)
}
func (m *GetDramaDetailReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetDramaDetailReq.Marshal(b, m, deterministic)
}
func (dst *GetDramaDetailReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetDramaDetailReq.Merge(dst, src)
}
func (m *GetDramaDetailReq) XXX_Size() int {
	return xxx_messageInfo_GetDramaDetailReq.Size(m)
}
func (m *GetDramaDetailReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetDramaDetailReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetDramaDetailReq proto.InternalMessageInfo

func (m *GetDramaDetailReq) GetDramaId() uint32 {
	if m != nil {
		return m.DramaId
	}
	return 0
}

type UserProfile struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid"`
	Account              string   `protobuf:"bytes,2,opt,name=account,proto3" json:"account"`
	Nickname             string   `protobuf:"bytes,3,opt,name=nickname,proto3" json:"nickname"`
	AccountAlias         string   `protobuf:"bytes,4,opt,name=account_alias,json=accountAlias,proto3" json:"account_alias"`
	Sex                  uint32   `protobuf:"varint,5,opt,name=sex,proto3" json:"sex"`
	HeadImgMd5           string   `protobuf:"bytes,6,opt,name=head_img_md5,json=headImgMd5,proto3" json:"head_img_md5"`
	HeadDyImgMd5         string   `protobuf:"bytes,7,opt,name=head_dy_img_md5,json=headDyImgMd5,proto3" json:"head_dy_img_md5"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserProfile) Reset()         { *m = UserProfile{} }
func (m *UserProfile) String() string { return proto.CompactTextString(m) }
func (*UserProfile) ProtoMessage()    {}
func (*UserProfile) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_revenue_http_logic_907ee343f90f615b, []int{1}
}
func (m *UserProfile) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserProfile.Unmarshal(m, b)
}
func (m *UserProfile) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserProfile.Marshal(b, m, deterministic)
}
func (dst *UserProfile) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserProfile.Merge(dst, src)
}
func (m *UserProfile) XXX_Size() int {
	return xxx_messageInfo_UserProfile.Size(m)
}
func (m *UserProfile) XXX_DiscardUnknown() {
	xxx_messageInfo_UserProfile.DiscardUnknown(m)
}

var xxx_messageInfo_UserProfile proto.InternalMessageInfo

func (m *UserProfile) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserProfile) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *UserProfile) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *UserProfile) GetAccountAlias() string {
	if m != nil {
		return m.AccountAlias
	}
	return ""
}

func (m *UserProfile) GetSex() uint32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

func (m *UserProfile) GetHeadImgMd5() string {
	if m != nil {
		return m.HeadImgMd5
	}
	return ""
}

func (m *UserProfile) GetHeadDyImgMd5() string {
	if m != nil {
		return m.HeadDyImgMd5
	}
	return ""
}

type GetDramaDetailResp struct {
	Title                string   `protobuf:"bytes,1,opt,name=title,proto3" json:"title"`
	CoverUrl             string   `protobuf:"bytes,2,opt,name=cover_url,json=coverUrl,proto3" json:"cover_url"`
	Author               string   `protobuf:"bytes,3,opt,name=author,proto3" json:"author"`
	MaleCnt              uint32   `protobuf:"varint,4,opt,name=male_cnt,json=maleCnt,proto3" json:"male_cnt"`
	FemaleCnt            uint32   `protobuf:"varint,5,opt,name=female_cnt,json=femaleCnt,proto3" json:"female_cnt"`
	WordCnt              uint32   `protobuf:"varint,6,opt,name=word_cnt,json=wordCnt,proto3" json:"word_cnt"`
	Tags                 []string `protobuf:"bytes,7,rep,name=tags,proto3" json:"tags"`
	Descr                string   `protobuf:"bytes,8,opt,name=descr,proto3" json:"descr"`
	Content              string   `protobuf:"bytes,9,opt,name=content,proto3" json:"content"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetDramaDetailResp) Reset()         { *m = GetDramaDetailResp{} }
func (m *GetDramaDetailResp) String() string { return proto.CompactTextString(m) }
func (*GetDramaDetailResp) ProtoMessage()    {}
func (*GetDramaDetailResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_revenue_http_logic_907ee343f90f615b, []int{2}
}
func (m *GetDramaDetailResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetDramaDetailResp.Unmarshal(m, b)
}
func (m *GetDramaDetailResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetDramaDetailResp.Marshal(b, m, deterministic)
}
func (dst *GetDramaDetailResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetDramaDetailResp.Merge(dst, src)
}
func (m *GetDramaDetailResp) XXX_Size() int {
	return xxx_messageInfo_GetDramaDetailResp.Size(m)
}
func (m *GetDramaDetailResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetDramaDetailResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetDramaDetailResp proto.InternalMessageInfo

func (m *GetDramaDetailResp) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *GetDramaDetailResp) GetCoverUrl() string {
	if m != nil {
		return m.CoverUrl
	}
	return ""
}

func (m *GetDramaDetailResp) GetAuthor() string {
	if m != nil {
		return m.Author
	}
	return ""
}

func (m *GetDramaDetailResp) GetMaleCnt() uint32 {
	if m != nil {
		return m.MaleCnt
	}
	return 0
}

func (m *GetDramaDetailResp) GetFemaleCnt() uint32 {
	if m != nil {
		return m.FemaleCnt
	}
	return 0
}

func (m *GetDramaDetailResp) GetWordCnt() uint32 {
	if m != nil {
		return m.WordCnt
	}
	return 0
}

func (m *GetDramaDetailResp) GetTags() []string {
	if m != nil {
		return m.Tags
	}
	return nil
}

func (m *GetDramaDetailResp) GetDescr() string {
	if m != nil {
		return m.Descr
	}
	return ""
}

func (m *GetDramaDetailResp) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

// pia戏角色
type PiaRole struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name"`
	Sex                  uint32   `protobuf:"varint,3,opt,name=sex,proto3" json:"sex"`
	Avatar               string   `protobuf:"bytes,4,opt,name=avatar,proto3" json:"avatar"`
	Introduction         string   `protobuf:"bytes,5,opt,name=introduction,proto3" json:"introduction"`
	Color                string   `protobuf:"bytes,6,opt,name=color,proto3" json:"color"`
	DialogueRatio        float64  `protobuf:"fixed64,7,opt,name=dialogue_ratio,json=dialogueRatio,proto3" json:"dialogue_ratio"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PiaRole) Reset()         { *m = PiaRole{} }
func (m *PiaRole) String() string { return proto.CompactTextString(m) }
func (*PiaRole) ProtoMessage()    {}
func (*PiaRole) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_revenue_http_logic_907ee343f90f615b, []int{3}
}
func (m *PiaRole) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PiaRole.Unmarshal(m, b)
}
func (m *PiaRole) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PiaRole.Marshal(b, m, deterministic)
}
func (dst *PiaRole) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PiaRole.Merge(dst, src)
}
func (m *PiaRole) XXX_Size() int {
	return xxx_messageInfo_PiaRole.Size(m)
}
func (m *PiaRole) XXX_DiscardUnknown() {
	xxx_messageInfo_PiaRole.DiscardUnknown(m)
}

var xxx_messageInfo_PiaRole proto.InternalMessageInfo

func (m *PiaRole) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *PiaRole) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *PiaRole) GetSex() uint32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

func (m *PiaRole) GetAvatar() string {
	if m != nil {
		return m.Avatar
	}
	return ""
}

func (m *PiaRole) GetIntroduction() string {
	if m != nil {
		return m.Introduction
	}
	return ""
}

func (m *PiaRole) GetColor() string {
	if m != nil {
		return m.Color
	}
	return ""
}

func (m *PiaRole) GetDialogueRatio() float64 {
	if m != nil {
		return m.DialogueRatio
	}
	return 0
}

// 获取剧本展开页
type GetDramaExpansionReq struct {
	DramaId              uint32   `protobuf:"varint,1,opt,name=drama_id,json=dramaId,proto3" json:"drama_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetDramaExpansionReq) Reset()         { *m = GetDramaExpansionReq{} }
func (m *GetDramaExpansionReq) String() string { return proto.CompactTextString(m) }
func (*GetDramaExpansionReq) ProtoMessage()    {}
func (*GetDramaExpansionReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_revenue_http_logic_907ee343f90f615b, []int{4}
}
func (m *GetDramaExpansionReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetDramaExpansionReq.Unmarshal(m, b)
}
func (m *GetDramaExpansionReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetDramaExpansionReq.Marshal(b, m, deterministic)
}
func (dst *GetDramaExpansionReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetDramaExpansionReq.Merge(dst, src)
}
func (m *GetDramaExpansionReq) XXX_Size() int {
	return xxx_messageInfo_GetDramaExpansionReq.Size(m)
}
func (m *GetDramaExpansionReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetDramaExpansionReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetDramaExpansionReq proto.InternalMessageInfo

func (m *GetDramaExpansionReq) GetDramaId() uint32 {
	if m != nil {
		return m.DramaId
	}
	return 0
}

type GetDramaExpansionResp struct {
	Tags                 []string   `protobuf:"bytes,1,rep,name=tags,proto3" json:"tags"`
	Descr                string     `protobuf:"bytes,2,opt,name=descr,proto3" json:"descr"`
	Content              string     `protobuf:"bytes,3,opt,name=content,proto3" json:"content"`
	Roles                []*PiaRole `protobuf:"bytes,4,rep,name=roles,proto3" json:"roles"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *GetDramaExpansionResp) Reset()         { *m = GetDramaExpansionResp{} }
func (m *GetDramaExpansionResp) String() string { return proto.CompactTextString(m) }
func (*GetDramaExpansionResp) ProtoMessage()    {}
func (*GetDramaExpansionResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_revenue_http_logic_907ee343f90f615b, []int{5}
}
func (m *GetDramaExpansionResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetDramaExpansionResp.Unmarshal(m, b)
}
func (m *GetDramaExpansionResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetDramaExpansionResp.Marshal(b, m, deterministic)
}
func (dst *GetDramaExpansionResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetDramaExpansionResp.Merge(dst, src)
}
func (m *GetDramaExpansionResp) XXX_Size() int {
	return xxx_messageInfo_GetDramaExpansionResp.Size(m)
}
func (m *GetDramaExpansionResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetDramaExpansionResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetDramaExpansionResp proto.InternalMessageInfo

func (m *GetDramaExpansionResp) GetTags() []string {
	if m != nil {
		return m.Tags
	}
	return nil
}

func (m *GetDramaExpansionResp) GetDescr() string {
	if m != nil {
		return m.Descr
	}
	return ""
}

func (m *GetDramaExpansionResp) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *GetDramaExpansionResp) GetRoles() []*PiaRole {
	if m != nil {
		return m.Roles
	}
	return nil
}

// 请求
type PiaAddDramaFeedBackReq struct {
	FeedbackType         FeedbackType `protobuf:"varint,1,opt,name=feedback_type,json=feedbackType,proto3,enum=tt_revenue_http_logic.FeedbackType" json:"feedback_type"`
	Content              string       `protobuf:"bytes,2,opt,name=content,proto3" json:"content"`
	PictureList          []string     `protobuf:"bytes,3,rep,name=picture_list,json=pictureList,proto3" json:"picture_list"`
	DramaId              uint32       `protobuf:"varint,4,opt,name=drama_id,json=dramaId,proto3" json:"drama_id"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *PiaAddDramaFeedBackReq) Reset()         { *m = PiaAddDramaFeedBackReq{} }
func (m *PiaAddDramaFeedBackReq) String() string { return proto.CompactTextString(m) }
func (*PiaAddDramaFeedBackReq) ProtoMessage()    {}
func (*PiaAddDramaFeedBackReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_revenue_http_logic_907ee343f90f615b, []int{6}
}
func (m *PiaAddDramaFeedBackReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PiaAddDramaFeedBackReq.Unmarshal(m, b)
}
func (m *PiaAddDramaFeedBackReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PiaAddDramaFeedBackReq.Marshal(b, m, deterministic)
}
func (dst *PiaAddDramaFeedBackReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PiaAddDramaFeedBackReq.Merge(dst, src)
}
func (m *PiaAddDramaFeedBackReq) XXX_Size() int {
	return xxx_messageInfo_PiaAddDramaFeedBackReq.Size(m)
}
func (m *PiaAddDramaFeedBackReq) XXX_DiscardUnknown() {
	xxx_messageInfo_PiaAddDramaFeedBackReq.DiscardUnknown(m)
}

var xxx_messageInfo_PiaAddDramaFeedBackReq proto.InternalMessageInfo

func (m *PiaAddDramaFeedBackReq) GetFeedbackType() FeedbackType {
	if m != nil {
		return m.FeedbackType
	}
	return FeedbackType_FEEDBACK_TYPE_INVALID
}

func (m *PiaAddDramaFeedBackReq) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *PiaAddDramaFeedBackReq) GetPictureList() []string {
	if m != nil {
		return m.PictureList
	}
	return nil
}

func (m *PiaAddDramaFeedBackReq) GetDramaId() uint32 {
	if m != nil {
		return m.DramaId
	}
	return 0
}

type CpGameGodRankMem struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid"`
	Account              string   `protobuf:"bytes,2,opt,name=account,proto3" json:"account"`
	Nickname             string   `protobuf:"bytes,3,opt,name=nickname,proto3" json:"nickname"`
	SendVal              uint32   `protobuf:"varint,4,opt,name=send_val,json=sendVal,proto3" json:"send_val"`
	IsMvp                bool     `protobuf:"varint,5,opt,name=is_mvp,json=isMvp,proto3" json:"is_mvp"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CpGameGodRankMem) Reset()         { *m = CpGameGodRankMem{} }
func (m *CpGameGodRankMem) String() string { return proto.CompactTextString(m) }
func (*CpGameGodRankMem) ProtoMessage()    {}
func (*CpGameGodRankMem) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_revenue_http_logic_907ee343f90f615b, []int{7}
}
func (m *CpGameGodRankMem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CpGameGodRankMem.Unmarshal(m, b)
}
func (m *CpGameGodRankMem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CpGameGodRankMem.Marshal(b, m, deterministic)
}
func (dst *CpGameGodRankMem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CpGameGodRankMem.Merge(dst, src)
}
func (m *CpGameGodRankMem) XXX_Size() int {
	return xxx_messageInfo_CpGameGodRankMem.Size(m)
}
func (m *CpGameGodRankMem) XXX_DiscardUnknown() {
	xxx_messageInfo_CpGameGodRankMem.DiscardUnknown(m)
}

var xxx_messageInfo_CpGameGodRankMem proto.InternalMessageInfo

func (m *CpGameGodRankMem) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *CpGameGodRankMem) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *CpGameGodRankMem) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *CpGameGodRankMem) GetSendVal() uint32 {
	if m != nil {
		return m.SendVal
	}
	return 0
}

func (m *CpGameGodRankMem) GetIsMvp() bool {
	if m != nil {
		return m.IsMvp
	}
	return false
}

type CpGameGodRankInfo struct {
	MemList              []*CpGameGodRankMem `protobuf:"bytes,1,rep,name=mem_list,json=memList,proto3" json:"mem_list"`
	Value                uint32              `protobuf:"varint,2,opt,name=value,proto3" json:"value"`
	Rank                 uint32              `protobuf:"varint,3,opt,name=rank,proto3" json:"rank"`
	Time                 uint32              `protobuf:"varint,4,opt,name=time,proto3" json:"time"`
	BgUrl                string              `protobuf:"bytes,5,opt,name=bg_url,json=bgUrl,proto3" json:"bg_url"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *CpGameGodRankInfo) Reset()         { *m = CpGameGodRankInfo{} }
func (m *CpGameGodRankInfo) String() string { return proto.CompactTextString(m) }
func (*CpGameGodRankInfo) ProtoMessage()    {}
func (*CpGameGodRankInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_revenue_http_logic_907ee343f90f615b, []int{8}
}
func (m *CpGameGodRankInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CpGameGodRankInfo.Unmarshal(m, b)
}
func (m *CpGameGodRankInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CpGameGodRankInfo.Marshal(b, m, deterministic)
}
func (dst *CpGameGodRankInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CpGameGodRankInfo.Merge(dst, src)
}
func (m *CpGameGodRankInfo) XXX_Size() int {
	return xxx_messageInfo_CpGameGodRankInfo.Size(m)
}
func (m *CpGameGodRankInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_CpGameGodRankInfo.DiscardUnknown(m)
}

var xxx_messageInfo_CpGameGodRankInfo proto.InternalMessageInfo

func (m *CpGameGodRankInfo) GetMemList() []*CpGameGodRankMem {
	if m != nil {
		return m.MemList
	}
	return nil
}

func (m *CpGameGodRankInfo) GetValue() uint32 {
	if m != nil {
		return m.Value
	}
	return 0
}

func (m *CpGameGodRankInfo) GetRank() uint32 {
	if m != nil {
		return m.Rank
	}
	return 0
}

func (m *CpGameGodRankInfo) GetTime() uint32 {
	if m != nil {
		return m.Time
	}
	return 0
}

func (m *CpGameGodRankInfo) GetBgUrl() string {
	if m != nil {
		return m.BgUrl
	}
	return ""
}

type GetChannelCpGameGodRankReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChannelCpGameGodRankReq) Reset()         { *m = GetChannelCpGameGodRankReq{} }
func (m *GetChannelCpGameGodRankReq) String() string { return proto.CompactTextString(m) }
func (*GetChannelCpGameGodRankReq) ProtoMessage()    {}
func (*GetChannelCpGameGodRankReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_revenue_http_logic_907ee343f90f615b, []int{9}
}
func (m *GetChannelCpGameGodRankReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelCpGameGodRankReq.Unmarshal(m, b)
}
func (m *GetChannelCpGameGodRankReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelCpGameGodRankReq.Marshal(b, m, deterministic)
}
func (dst *GetChannelCpGameGodRankReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelCpGameGodRankReq.Merge(dst, src)
}
func (m *GetChannelCpGameGodRankReq) XXX_Size() int {
	return xxx_messageInfo_GetChannelCpGameGodRankReq.Size(m)
}
func (m *GetChannelCpGameGodRankReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelCpGameGodRankReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelCpGameGodRankReq proto.InternalMessageInfo

func (m *GetChannelCpGameGodRankReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetChannelCpGameGodRankResp struct {
	List                 []*CpGameGodRankInfo `protobuf:"bytes,1,rep,name=list,proto3" json:"list"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *GetChannelCpGameGodRankResp) Reset()         { *m = GetChannelCpGameGodRankResp{} }
func (m *GetChannelCpGameGodRankResp) String() string { return proto.CompactTextString(m) }
func (*GetChannelCpGameGodRankResp) ProtoMessage()    {}
func (*GetChannelCpGameGodRankResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_revenue_http_logic_907ee343f90f615b, []int{10}
}
func (m *GetChannelCpGameGodRankResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelCpGameGodRankResp.Unmarshal(m, b)
}
func (m *GetChannelCpGameGodRankResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelCpGameGodRankResp.Marshal(b, m, deterministic)
}
func (dst *GetChannelCpGameGodRankResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelCpGameGodRankResp.Merge(dst, src)
}
func (m *GetChannelCpGameGodRankResp) XXX_Size() int {
	return xxx_messageInfo_GetChannelCpGameGodRankResp.Size(m)
}
func (m *GetChannelCpGameGodRankResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelCpGameGodRankResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelCpGameGodRankResp proto.InternalMessageInfo

func (m *GetChannelCpGameGodRankResp) GetList() []*CpGameGodRankInfo {
	if m != nil {
		return m.List
	}
	return nil
}

type GetH5CPInfoReq struct {
	MyUid                uint32   `protobuf:"varint,1,opt,name=my_uid,json=myUid,proto3" json:"my_uid"`
	FellowUid            uint32   `protobuf:"varint,2,opt,name=fellow_uid,json=fellowUid,proto3" json:"fellow_uid"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetH5CPInfoReq) Reset()         { *m = GetH5CPInfoReq{} }
func (m *GetH5CPInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetH5CPInfoReq) ProtoMessage()    {}
func (*GetH5CPInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_revenue_http_logic_907ee343f90f615b, []int{11}
}
func (m *GetH5CPInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetH5CPInfoReq.Unmarshal(m, b)
}
func (m *GetH5CPInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetH5CPInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetH5CPInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetH5CPInfoReq.Merge(dst, src)
}
func (m *GetH5CPInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetH5CPInfoReq.Size(m)
}
func (m *GetH5CPInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetH5CPInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetH5CPInfoReq proto.InternalMessageInfo

func (m *GetH5CPInfoReq) GetMyUid() uint32 {
	if m != nil {
		return m.MyUid
	}
	return 0
}

func (m *GetH5CPInfoReq) GetFellowUid() uint32 {
	if m != nil {
		return m.FellowUid
	}
	return 0
}

type GetH5CPInfoResp struct {
	MyUid                uint32   `protobuf:"varint,1,opt,name=my_uid,json=myUid,proto3" json:"my_uid"`
	MyNickName           string   `protobuf:"bytes,2,opt,name=my_nick_name,json=myNickName,proto3" json:"my_nick_name"`
	MyIcon               string   `protobuf:"bytes,3,opt,name=my_icon,json=myIcon,proto3" json:"my_icon"`
	FellowUid            uint32   `protobuf:"varint,4,opt,name=fellow_uid,json=fellowUid,proto3" json:"fellow_uid"`
	FellowNickName       string   `protobuf:"bytes,5,opt,name=fellow_nick_name,json=fellowNickName,proto3" json:"fellow_nick_name"`
	FellowIcon           string   `protobuf:"bytes,6,opt,name=fellow_icon,json=fellowIcon,proto3" json:"fellow_icon"`
	CurrentLv            uint32   `protobuf:"varint,7,opt,name=current_lv,json=currentLv,proto3" json:"current_lv"`
	CurrentStrength      uint32   `protobuf:"varint,8,opt,name=current_strength,json=currentStrength,proto3" json:"current_strength"`
	CurrentLvStrength    uint32   `protobuf:"varint,9,opt,name=current_lv_strength,json=currentLvStrength,proto3" json:"current_lv_strength"`
	NextLvStrength       uint32   `protobuf:"varint,10,opt,name=next_lv_strength,json=nextLvStrength,proto3" json:"next_lv_strength"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetH5CPInfoResp) Reset()         { *m = GetH5CPInfoResp{} }
func (m *GetH5CPInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetH5CPInfoResp) ProtoMessage()    {}
func (*GetH5CPInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_revenue_http_logic_907ee343f90f615b, []int{12}
}
func (m *GetH5CPInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetH5CPInfoResp.Unmarshal(m, b)
}
func (m *GetH5CPInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetH5CPInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetH5CPInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetH5CPInfoResp.Merge(dst, src)
}
func (m *GetH5CPInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetH5CPInfoResp.Size(m)
}
func (m *GetH5CPInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetH5CPInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetH5CPInfoResp proto.InternalMessageInfo

func (m *GetH5CPInfoResp) GetMyUid() uint32 {
	if m != nil {
		return m.MyUid
	}
	return 0
}

func (m *GetH5CPInfoResp) GetMyNickName() string {
	if m != nil {
		return m.MyNickName
	}
	return ""
}

func (m *GetH5CPInfoResp) GetMyIcon() string {
	if m != nil {
		return m.MyIcon
	}
	return ""
}

func (m *GetH5CPInfoResp) GetFellowUid() uint32 {
	if m != nil {
		return m.FellowUid
	}
	return 0
}

func (m *GetH5CPInfoResp) GetFellowNickName() string {
	if m != nil {
		return m.FellowNickName
	}
	return ""
}

func (m *GetH5CPInfoResp) GetFellowIcon() string {
	if m != nil {
		return m.FellowIcon
	}
	return ""
}

func (m *GetH5CPInfoResp) GetCurrentLv() uint32 {
	if m != nil {
		return m.CurrentLv
	}
	return 0
}

func (m *GetH5CPInfoResp) GetCurrentStrength() uint32 {
	if m != nil {
		return m.CurrentStrength
	}
	return 0
}

func (m *GetH5CPInfoResp) GetCurrentLvStrength() uint32 {
	if m != nil {
		return m.CurrentLvStrength
	}
	return 0
}

func (m *GetH5CPInfoResp) GetNextLvStrength() uint32 {
	if m != nil {
		return m.NextLvStrength
	}
	return 0
}

type CostInfoMaterial struct {
	GiftId               uint32   `protobuf:"varint,1,opt,name=giftId,proto3" json:"giftId"`
	GiftNum              uint32   `protobuf:"varint,2,opt,name=giftNum,proto3" json:"giftNum"`
	UserItemId           uint32   `protobuf:"varint,3,opt,name=userItemId,proto3" json:"userItemId"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CostInfoMaterial) Reset()         { *m = CostInfoMaterial{} }
func (m *CostInfoMaterial) String() string { return proto.CompactTextString(m) }
func (*CostInfoMaterial) ProtoMessage()    {}
func (*CostInfoMaterial) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_revenue_http_logic_907ee343f90f615b, []int{13}
}
func (m *CostInfoMaterial) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CostInfoMaterial.Unmarshal(m, b)
}
func (m *CostInfoMaterial) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CostInfoMaterial.Marshal(b, m, deterministic)
}
func (dst *CostInfoMaterial) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CostInfoMaterial.Merge(dst, src)
}
func (m *CostInfoMaterial) XXX_Size() int {
	return xxx_messageInfo_CostInfoMaterial.Size(m)
}
func (m *CostInfoMaterial) XXX_DiscardUnknown() {
	xxx_messageInfo_CostInfoMaterial.DiscardUnknown(m)
}

var xxx_messageInfo_CostInfoMaterial proto.InternalMessageInfo

func (m *CostInfoMaterial) GetGiftId() uint32 {
	if m != nil {
		return m.GiftId
	}
	return 0
}

func (m *CostInfoMaterial) GetGiftNum() uint32 {
	if m != nil {
		return m.GiftNum
	}
	return 0
}

func (m *CostInfoMaterial) GetUserItemId() uint32 {
	if m != nil {
		return m.UserItemId
	}
	return 0
}

type ComposeGiftReq struct {
	Uid                  string              `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid"`
	SyntheticId          uint32              `protobuf:"varint,2,opt,name=syntheticId,proto3" json:"syntheticId"`
	Num                  uint32              `protobuf:"varint,3,opt,name=num,proto3" json:"num"`
	MixList              []*CostInfoMaterial `protobuf:"bytes,4,rep,name=mixList,proto3" json:"mixList"`
	Cost                 uint32              `protobuf:"varint,5,opt,name=cost,proto3" json:"cost"`
	OsType               string              `protobuf:"bytes,6,opt,name=os_type,json=osType,proto3" json:"os_type"`
	App                  string              `protobuf:"bytes,7,opt,name=app,proto3" json:"app"`
	MarketId             string              `protobuf:"bytes,8,opt,name=market_id,json=marketId,proto3" json:"market_id"`
	Platform             string              `protobuf:"bytes,9,opt,name=platform,proto3" json:"platform"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *ComposeGiftReq) Reset()         { *m = ComposeGiftReq{} }
func (m *ComposeGiftReq) String() string { return proto.CompactTextString(m) }
func (*ComposeGiftReq) ProtoMessage()    {}
func (*ComposeGiftReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_revenue_http_logic_907ee343f90f615b, []int{14}
}
func (m *ComposeGiftReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ComposeGiftReq.Unmarshal(m, b)
}
func (m *ComposeGiftReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ComposeGiftReq.Marshal(b, m, deterministic)
}
func (dst *ComposeGiftReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ComposeGiftReq.Merge(dst, src)
}
func (m *ComposeGiftReq) XXX_Size() int {
	return xxx_messageInfo_ComposeGiftReq.Size(m)
}
func (m *ComposeGiftReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ComposeGiftReq.DiscardUnknown(m)
}

var xxx_messageInfo_ComposeGiftReq proto.InternalMessageInfo

func (m *ComposeGiftReq) GetUid() string {
	if m != nil {
		return m.Uid
	}
	return ""
}

func (m *ComposeGiftReq) GetSyntheticId() uint32 {
	if m != nil {
		return m.SyntheticId
	}
	return 0
}

func (m *ComposeGiftReq) GetNum() uint32 {
	if m != nil {
		return m.Num
	}
	return 0
}

func (m *ComposeGiftReq) GetMixList() []*CostInfoMaterial {
	if m != nil {
		return m.MixList
	}
	return nil
}

func (m *ComposeGiftReq) GetCost() uint32 {
	if m != nil {
		return m.Cost
	}
	return 0
}

func (m *ComposeGiftReq) GetOsType() string {
	if m != nil {
		return m.OsType
	}
	return ""
}

func (m *ComposeGiftReq) GetApp() string {
	if m != nil {
		return m.App
	}
	return ""
}

func (m *ComposeGiftReq) GetMarketId() string {
	if m != nil {
		return m.MarketId
	}
	return ""
}

func (m *ComposeGiftReq) GetPlatform() string {
	if m != nil {
		return m.Platform
	}
	return ""
}

type ComposeGiftResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ComposeGiftResp) Reset()         { *m = ComposeGiftResp{} }
func (m *ComposeGiftResp) String() string { return proto.CompactTextString(m) }
func (*ComposeGiftResp) ProtoMessage()    {}
func (*ComposeGiftResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_revenue_http_logic_907ee343f90f615b, []int{15}
}
func (m *ComposeGiftResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ComposeGiftResp.Unmarshal(m, b)
}
func (m *ComposeGiftResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ComposeGiftResp.Marshal(b, m, deterministic)
}
func (dst *ComposeGiftResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ComposeGiftResp.Merge(dst, src)
}
func (m *ComposeGiftResp) XXX_Size() int {
	return xxx_messageInfo_ComposeGiftResp.Size(m)
}
func (m *ComposeGiftResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ComposeGiftResp.DiscardUnknown(m)
}

var xxx_messageInfo_ComposeGiftResp proto.InternalMessageInfo

type ComposeGiftConf struct {
	SyntheticId          uint32   `protobuf:"varint,1,opt,name=syntheticId,proto3" json:"syntheticId"`
	SyntheticType        uint32   `protobuf:"varint,2,opt,name=syntheticType,proto3" json:"syntheticType"`
	SyntheticName        string   `protobuf:"bytes,3,opt,name=syntheticName,proto3" json:"syntheticName"`
	SyntheticPrice       uint32   `protobuf:"varint,4,opt,name=syntheticPrice,proto3" json:"syntheticPrice"`
	SyntheticUrl         string   `protobuf:"bytes,5,opt,name=syntheticUrl,proto3" json:"syntheticUrl"`
	Index                uint32   `protobuf:"varint,6,opt,name=index,proto3" json:"index"`
	UpTime               uint32   `protobuf:"varint,7,opt,name=upTime,proto3" json:"upTime"`
	DownTime             uint32   `protobuf:"varint,8,opt,name=downTime,proto3" json:"downTime"`
	FinTime              uint32   `protobuf:"varint,9,opt,name=finTime,proto3" json:"finTime"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ComposeGiftConf) Reset()         { *m = ComposeGiftConf{} }
func (m *ComposeGiftConf) String() string { return proto.CompactTextString(m) }
func (*ComposeGiftConf) ProtoMessage()    {}
func (*ComposeGiftConf) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_revenue_http_logic_907ee343f90f615b, []int{16}
}
func (m *ComposeGiftConf) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ComposeGiftConf.Unmarshal(m, b)
}
func (m *ComposeGiftConf) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ComposeGiftConf.Marshal(b, m, deterministic)
}
func (dst *ComposeGiftConf) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ComposeGiftConf.Merge(dst, src)
}
func (m *ComposeGiftConf) XXX_Size() int {
	return xxx_messageInfo_ComposeGiftConf.Size(m)
}
func (m *ComposeGiftConf) XXX_DiscardUnknown() {
	xxx_messageInfo_ComposeGiftConf.DiscardUnknown(m)
}

var xxx_messageInfo_ComposeGiftConf proto.InternalMessageInfo

func (m *ComposeGiftConf) GetSyntheticId() uint32 {
	if m != nil {
		return m.SyntheticId
	}
	return 0
}

func (m *ComposeGiftConf) GetSyntheticType() uint32 {
	if m != nil {
		return m.SyntheticType
	}
	return 0
}

func (m *ComposeGiftConf) GetSyntheticName() string {
	if m != nil {
		return m.SyntheticName
	}
	return ""
}

func (m *ComposeGiftConf) GetSyntheticPrice() uint32 {
	if m != nil {
		return m.SyntheticPrice
	}
	return 0
}

func (m *ComposeGiftConf) GetSyntheticUrl() string {
	if m != nil {
		return m.SyntheticUrl
	}
	return ""
}

func (m *ComposeGiftConf) GetIndex() uint32 {
	if m != nil {
		return m.Index
	}
	return 0
}

func (m *ComposeGiftConf) GetUpTime() uint32 {
	if m != nil {
		return m.UpTime
	}
	return 0
}

func (m *ComposeGiftConf) GetDownTime() uint32 {
	if m != nil {
		return m.DownTime
	}
	return 0
}

func (m *ComposeGiftConf) GetFinTime() uint32 {
	if m != nil {
		return m.FinTime
	}
	return 0
}

type GetComposeGiftConfListReq struct {
	Uid                  string   `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetComposeGiftConfListReq) Reset()         { *m = GetComposeGiftConfListReq{} }
func (m *GetComposeGiftConfListReq) String() string { return proto.CompactTextString(m) }
func (*GetComposeGiftConfListReq) ProtoMessage()    {}
func (*GetComposeGiftConfListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_revenue_http_logic_907ee343f90f615b, []int{17}
}
func (m *GetComposeGiftConfListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetComposeGiftConfListReq.Unmarshal(m, b)
}
func (m *GetComposeGiftConfListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetComposeGiftConfListReq.Marshal(b, m, deterministic)
}
func (dst *GetComposeGiftConfListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetComposeGiftConfListReq.Merge(dst, src)
}
func (m *GetComposeGiftConfListReq) XXX_Size() int {
	return xxx_messageInfo_GetComposeGiftConfListReq.Size(m)
}
func (m *GetComposeGiftConfListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetComposeGiftConfListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetComposeGiftConfListReq proto.InternalMessageInfo

func (m *GetComposeGiftConfListReq) GetUid() string {
	if m != nil {
		return m.Uid
	}
	return ""
}

type GetComposeGiftConfListResp struct {
	Configs              []*ComposeGiftConf `protobuf:"bytes,1,rep,name=configs,proto3" json:"configs"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *GetComposeGiftConfListResp) Reset()         { *m = GetComposeGiftConfListResp{} }
func (m *GetComposeGiftConfListResp) String() string { return proto.CompactTextString(m) }
func (*GetComposeGiftConfListResp) ProtoMessage()    {}
func (*GetComposeGiftConfListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_revenue_http_logic_907ee343f90f615b, []int{18}
}
func (m *GetComposeGiftConfListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetComposeGiftConfListResp.Unmarshal(m, b)
}
func (m *GetComposeGiftConfListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetComposeGiftConfListResp.Marshal(b, m, deterministic)
}
func (dst *GetComposeGiftConfListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetComposeGiftConfListResp.Merge(dst, src)
}
func (m *GetComposeGiftConfListResp) XXX_Size() int {
	return xxx_messageInfo_GetComposeGiftConfListResp.Size(m)
}
func (m *GetComposeGiftConfListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetComposeGiftConfListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetComposeGiftConfListResp proto.InternalMessageInfo

func (m *GetComposeGiftConfListResp) GetConfigs() []*ComposeGiftConf {
	if m != nil {
		return m.Configs
	}
	return nil
}

// 合成记录
type ComposeLog struct {
	OrderId              string   `protobuf:"bytes,1,opt,name=orderId,proto3" json:"orderId"`
	SyntheticTime        uint32   `protobuf:"varint,2,opt,name=syntheticTime,proto3" json:"syntheticTime"`
	SyntheticName        string   `protobuf:"bytes,3,opt,name=syntheticName,proto3" json:"syntheticName"`
	SyntheticPrice       uint32   `protobuf:"varint,4,opt,name=syntheticPrice,proto3" json:"syntheticPrice"`
	SyntheticNum         uint32   `protobuf:"varint,5,opt,name=syntheticNum,proto3" json:"syntheticNum"`
	SyntheticUrl         string   `protobuf:"bytes,6,opt,name=syntheticUrl,proto3" json:"syntheticUrl"`
	CostDesc             string   `protobuf:"bytes,7,opt,name=costDesc,proto3" json:"costDesc"`
	Cost                 uint32   `protobuf:"varint,8,opt,name=cost,proto3" json:"cost"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ComposeLog) Reset()         { *m = ComposeLog{} }
func (m *ComposeLog) String() string { return proto.CompactTextString(m) }
func (*ComposeLog) ProtoMessage()    {}
func (*ComposeLog) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_revenue_http_logic_907ee343f90f615b, []int{19}
}
func (m *ComposeLog) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ComposeLog.Unmarshal(m, b)
}
func (m *ComposeLog) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ComposeLog.Marshal(b, m, deterministic)
}
func (dst *ComposeLog) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ComposeLog.Merge(dst, src)
}
func (m *ComposeLog) XXX_Size() int {
	return xxx_messageInfo_ComposeLog.Size(m)
}
func (m *ComposeLog) XXX_DiscardUnknown() {
	xxx_messageInfo_ComposeLog.DiscardUnknown(m)
}

var xxx_messageInfo_ComposeLog proto.InternalMessageInfo

func (m *ComposeLog) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *ComposeLog) GetSyntheticTime() uint32 {
	if m != nil {
		return m.SyntheticTime
	}
	return 0
}

func (m *ComposeLog) GetSyntheticName() string {
	if m != nil {
		return m.SyntheticName
	}
	return ""
}

func (m *ComposeLog) GetSyntheticPrice() uint32 {
	if m != nil {
		return m.SyntheticPrice
	}
	return 0
}

func (m *ComposeLog) GetSyntheticNum() uint32 {
	if m != nil {
		return m.SyntheticNum
	}
	return 0
}

func (m *ComposeLog) GetSyntheticUrl() string {
	if m != nil {
		return m.SyntheticUrl
	}
	return ""
}

func (m *ComposeLog) GetCostDesc() string {
	if m != nil {
		return m.CostDesc
	}
	return ""
}

func (m *ComposeLog) GetCost() uint32 {
	if m != nil {
		return m.Cost
	}
	return 0
}

type GetUserComposeLogsReq struct {
	Uid                  string   `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid"`
	Offset               uint32   `protobuf:"varint,2,opt,name=offset,proto3" json:"offset"`
	PageNum              uint32   `protobuf:"varint,3,opt,name=pageNum,proto3" json:"pageNum"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserComposeLogsReq) Reset()         { *m = GetUserComposeLogsReq{} }
func (m *GetUserComposeLogsReq) String() string { return proto.CompactTextString(m) }
func (*GetUserComposeLogsReq) ProtoMessage()    {}
func (*GetUserComposeLogsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_revenue_http_logic_907ee343f90f615b, []int{20}
}
func (m *GetUserComposeLogsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserComposeLogsReq.Unmarshal(m, b)
}
func (m *GetUserComposeLogsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserComposeLogsReq.Marshal(b, m, deterministic)
}
func (dst *GetUserComposeLogsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserComposeLogsReq.Merge(dst, src)
}
func (m *GetUserComposeLogsReq) XXX_Size() int {
	return xxx_messageInfo_GetUserComposeLogsReq.Size(m)
}
func (m *GetUserComposeLogsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserComposeLogsReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserComposeLogsReq proto.InternalMessageInfo

func (m *GetUserComposeLogsReq) GetUid() string {
	if m != nil {
		return m.Uid
	}
	return ""
}

func (m *GetUserComposeLogsReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetUserComposeLogsReq) GetPageNum() uint32 {
	if m != nil {
		return m.PageNum
	}
	return 0
}

type GetUserComposeLogsResp struct {
	Logs                 []*ComposeLog `protobuf:"bytes,1,rep,name=logs,proto3" json:"logs"`
	HasNextPage          bool          `protobuf:"varint,2,opt,name=hasNextPage,proto3" json:"hasNextPage"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetUserComposeLogsResp) Reset()         { *m = GetUserComposeLogsResp{} }
func (m *GetUserComposeLogsResp) String() string { return proto.CompactTextString(m) }
func (*GetUserComposeLogsResp) ProtoMessage()    {}
func (*GetUserComposeLogsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_revenue_http_logic_907ee343f90f615b, []int{21}
}
func (m *GetUserComposeLogsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserComposeLogsResp.Unmarshal(m, b)
}
func (m *GetUserComposeLogsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserComposeLogsResp.Marshal(b, m, deterministic)
}
func (dst *GetUserComposeLogsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserComposeLogsResp.Merge(dst, src)
}
func (m *GetUserComposeLogsResp) XXX_Size() int {
	return xxx_messageInfo_GetUserComposeLogsResp.Size(m)
}
func (m *GetUserComposeLogsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserComposeLogsResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserComposeLogsResp proto.InternalMessageInfo

func (m *GetUserComposeLogsResp) GetLogs() []*ComposeLog {
	if m != nil {
		return m.Logs
	}
	return nil
}

func (m *GetUserComposeLogsResp) GetHasNextPage() bool {
	if m != nil {
		return m.HasNextPage
	}
	return false
}

type MaterialInfo struct {
	GiftId               uint32   `protobuf:"varint,1,opt,name=giftId,proto3" json:"giftId"`
	GiftNum              uint32   `protobuf:"varint,2,opt,name=giftNum,proto3" json:"giftNum"`
	GiftName             string   `protobuf:"bytes,3,opt,name=giftName,proto3" json:"giftName"`
	GiftPrice            uint32   `protobuf:"varint,4,opt,name=giftPrice,proto3" json:"giftPrice"`
	GiftUrl              string   `protobuf:"bytes,5,opt,name=giftUrl,proto3" json:"giftUrl"`
	Index                uint32   `protobuf:"varint,6,opt,name=index,proto3" json:"index"`
	UserItemId           uint32   `protobuf:"varint,7,opt,name=userItemId,proto3" json:"userItemId"`
	FinTime              uint32   `protobuf:"varint,8,opt,name=finTime,proto3" json:"finTime"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MaterialInfo) Reset()         { *m = MaterialInfo{} }
func (m *MaterialInfo) String() string { return proto.CompactTextString(m) }
func (*MaterialInfo) ProtoMessage()    {}
func (*MaterialInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_revenue_http_logic_907ee343f90f615b, []int{22}
}
func (m *MaterialInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MaterialInfo.Unmarshal(m, b)
}
func (m *MaterialInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MaterialInfo.Marshal(b, m, deterministic)
}
func (dst *MaterialInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MaterialInfo.Merge(dst, src)
}
func (m *MaterialInfo) XXX_Size() int {
	return xxx_messageInfo_MaterialInfo.Size(m)
}
func (m *MaterialInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_MaterialInfo.DiscardUnknown(m)
}

var xxx_messageInfo_MaterialInfo proto.InternalMessageInfo

func (m *MaterialInfo) GetGiftId() uint32 {
	if m != nil {
		return m.GiftId
	}
	return 0
}

func (m *MaterialInfo) GetGiftNum() uint32 {
	if m != nil {
		return m.GiftNum
	}
	return 0
}

func (m *MaterialInfo) GetGiftName() string {
	if m != nil {
		return m.GiftName
	}
	return ""
}

func (m *MaterialInfo) GetGiftPrice() uint32 {
	if m != nil {
		return m.GiftPrice
	}
	return 0
}

func (m *MaterialInfo) GetGiftUrl() string {
	if m != nil {
		return m.GiftUrl
	}
	return ""
}

func (m *MaterialInfo) GetIndex() uint32 {
	if m != nil {
		return m.Index
	}
	return 0
}

func (m *MaterialInfo) GetUserItemId() uint32 {
	if m != nil {
		return m.UserItemId
	}
	return 0
}

func (m *MaterialInfo) GetFinTime() uint32 {
	if m != nil {
		return m.FinTime
	}
	return 0
}

type GetUserMaterialListReq struct {
	Uid                  string   `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserMaterialListReq) Reset()         { *m = GetUserMaterialListReq{} }
func (m *GetUserMaterialListReq) String() string { return proto.CompactTextString(m) }
func (*GetUserMaterialListReq) ProtoMessage()    {}
func (*GetUserMaterialListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_revenue_http_logic_907ee343f90f615b, []int{23}
}
func (m *GetUserMaterialListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserMaterialListReq.Unmarshal(m, b)
}
func (m *GetUserMaterialListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserMaterialListReq.Marshal(b, m, deterministic)
}
func (dst *GetUserMaterialListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserMaterialListReq.Merge(dst, src)
}
func (m *GetUserMaterialListReq) XXX_Size() int {
	return xxx_messageInfo_GetUserMaterialListReq.Size(m)
}
func (m *GetUserMaterialListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserMaterialListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserMaterialListReq proto.InternalMessageInfo

func (m *GetUserMaterialListReq) GetUid() string {
	if m != nil {
		return m.Uid
	}
	return ""
}

type GetUserMaterialListResp struct {
	Gifts                []*MaterialInfo `protobuf:"bytes,1,rep,name=gifts,proto3" json:"gifts"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *GetUserMaterialListResp) Reset()         { *m = GetUserMaterialListResp{} }
func (m *GetUserMaterialListResp) String() string { return proto.CompactTextString(m) }
func (*GetUserMaterialListResp) ProtoMessage()    {}
func (*GetUserMaterialListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_revenue_http_logic_907ee343f90f615b, []int{24}
}
func (m *GetUserMaterialListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserMaterialListResp.Unmarshal(m, b)
}
func (m *GetUserMaterialListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserMaterialListResp.Marshal(b, m, deterministic)
}
func (dst *GetUserMaterialListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserMaterialListResp.Merge(dst, src)
}
func (m *GetUserMaterialListResp) XXX_Size() int {
	return xxx_messageInfo_GetUserMaterialListResp.Size(m)
}
func (m *GetUserMaterialListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserMaterialListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserMaterialListResp proto.InternalMessageInfo

func (m *GetUserMaterialListResp) GetGifts() []*MaterialInfo {
	if m != nil {
		return m.Gifts
	}
	return nil
}

// 获取魔幻奖励碎片配置，其中，魔幻奖励中的兑换配置需要根据fragment_id进行过滤展示
type GetMagicFragmentRequest struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetMagicFragmentRequest) Reset()         { *m = GetMagicFragmentRequest{} }
func (m *GetMagicFragmentRequest) String() string { return proto.CompactTextString(m) }
func (*GetMagicFragmentRequest) ProtoMessage()    {}
func (*GetMagicFragmentRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_revenue_http_logic_907ee343f90f615b, []int{25}
}
func (m *GetMagicFragmentRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMagicFragmentRequest.Unmarshal(m, b)
}
func (m *GetMagicFragmentRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMagicFragmentRequest.Marshal(b, m, deterministic)
}
func (dst *GetMagicFragmentRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMagicFragmentRequest.Merge(dst, src)
}
func (m *GetMagicFragmentRequest) XXX_Size() int {
	return xxx_messageInfo_GetMagicFragmentRequest.Size(m)
}
func (m *GetMagicFragmentRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMagicFragmentRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetMagicFragmentRequest proto.InternalMessageInfo

type GetMagicFragmentResponse struct {
	FragmentId    uint32 `protobuf:"varint,1,opt,name=fragment_id,json=fragmentId,proto3" json:"fragment_id"`
	FragmentName  string `protobuf:"bytes,2,opt,name=fragment_name,json=fragmentName,proto3" json:"fragment_name"`
	FragmentPic   string `protobuf:"bytes,3,opt,name=fragment_pic,json=fragmentPic,proto3" json:"fragment_pic"`
	FragmentPrice uint32 `protobuf:"varint,4,opt,name=fragment_price,json=fragmentPrice,proto3" json:"fragment_price"`
	// 规则页资源后缀，根据不同马甲包自行拼接
	CompositeRules       string   `protobuf:"bytes,5,opt,name=composite_rules,json=compositeRules,proto3" json:"composite_rules"`
	DarkCompositeRules   string   `protobuf:"bytes,6,opt,name=dark_composite_rules,json=darkCompositeRules,proto3" json:"dark_composite_rules"`
	DarkEnergyStones     string   `protobuf:"bytes,7,opt,name=dark_energy_stones,json=darkEnergyStones,proto3" json:"dark_energy_stones"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetMagicFragmentResponse) Reset()         { *m = GetMagicFragmentResponse{} }
func (m *GetMagicFragmentResponse) String() string { return proto.CompactTextString(m) }
func (*GetMagicFragmentResponse) ProtoMessage()    {}
func (*GetMagicFragmentResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_revenue_http_logic_907ee343f90f615b, []int{26}
}
func (m *GetMagicFragmentResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMagicFragmentResponse.Unmarshal(m, b)
}
func (m *GetMagicFragmentResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMagicFragmentResponse.Marshal(b, m, deterministic)
}
func (dst *GetMagicFragmentResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMagicFragmentResponse.Merge(dst, src)
}
func (m *GetMagicFragmentResponse) XXX_Size() int {
	return xxx_messageInfo_GetMagicFragmentResponse.Size(m)
}
func (m *GetMagicFragmentResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMagicFragmentResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetMagicFragmentResponse proto.InternalMessageInfo

func (m *GetMagicFragmentResponse) GetFragmentId() uint32 {
	if m != nil {
		return m.FragmentId
	}
	return 0
}

func (m *GetMagicFragmentResponse) GetFragmentName() string {
	if m != nil {
		return m.FragmentName
	}
	return ""
}

func (m *GetMagicFragmentResponse) GetFragmentPic() string {
	if m != nil {
		return m.FragmentPic
	}
	return ""
}

func (m *GetMagicFragmentResponse) GetFragmentPrice() uint32 {
	if m != nil {
		return m.FragmentPrice
	}
	return 0
}

func (m *GetMagicFragmentResponse) GetCompositeRules() string {
	if m != nil {
		return m.CompositeRules
	}
	return ""
}

func (m *GetMagicFragmentResponse) GetDarkCompositeRules() string {
	if m != nil {
		return m.DarkCompositeRules
	}
	return ""
}

func (m *GetMagicFragmentResponse) GetDarkEnergyStones() string {
	if m != nil {
		return m.DarkEnergyStones
	}
	return ""
}

// 检查合成入口是否开启
type CheckGiftComposeEntryReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckGiftComposeEntryReq) Reset()         { *m = CheckGiftComposeEntryReq{} }
func (m *CheckGiftComposeEntryReq) String() string { return proto.CompactTextString(m) }
func (*CheckGiftComposeEntryReq) ProtoMessage()    {}
func (*CheckGiftComposeEntryReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_revenue_http_logic_907ee343f90f615b, []int{27}
}
func (m *CheckGiftComposeEntryReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckGiftComposeEntryReq.Unmarshal(m, b)
}
func (m *CheckGiftComposeEntryReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckGiftComposeEntryReq.Marshal(b, m, deterministic)
}
func (dst *CheckGiftComposeEntryReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckGiftComposeEntryReq.Merge(dst, src)
}
func (m *CheckGiftComposeEntryReq) XXX_Size() int {
	return xxx_messageInfo_CheckGiftComposeEntryReq.Size(m)
}
func (m *CheckGiftComposeEntryReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckGiftComposeEntryReq.DiscardUnknown(m)
}

var xxx_messageInfo_CheckGiftComposeEntryReq proto.InternalMessageInfo

type CheckGiftComposeEntryResp struct {
	IsOpen               bool     `protobuf:"varint,1,opt,name=is_open,json=isOpen,proto3" json:"is_open"`
	MaterialsTolPrice    uint32   `protobuf:"varint,2,opt,name=materials_tol_price,json=materialsTolPrice,proto3" json:"materials_tol_price"`
	PoolFullValue        uint32   `protobuf:"varint,3,opt,name=pool_full_value,json=poolFullValue,proto3" json:"pool_full_value"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckGiftComposeEntryResp) Reset()         { *m = CheckGiftComposeEntryResp{} }
func (m *CheckGiftComposeEntryResp) String() string { return proto.CompactTextString(m) }
func (*CheckGiftComposeEntryResp) ProtoMessage()    {}
func (*CheckGiftComposeEntryResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_revenue_http_logic_907ee343f90f615b, []int{28}
}
func (m *CheckGiftComposeEntryResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckGiftComposeEntryResp.Unmarshal(m, b)
}
func (m *CheckGiftComposeEntryResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckGiftComposeEntryResp.Marshal(b, m, deterministic)
}
func (dst *CheckGiftComposeEntryResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckGiftComposeEntryResp.Merge(dst, src)
}
func (m *CheckGiftComposeEntryResp) XXX_Size() int {
	return xxx_messageInfo_CheckGiftComposeEntryResp.Size(m)
}
func (m *CheckGiftComposeEntryResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckGiftComposeEntryResp.DiscardUnknown(m)
}

var xxx_messageInfo_CheckGiftComposeEntryResp proto.InternalMessageInfo

func (m *CheckGiftComposeEntryResp) GetIsOpen() bool {
	if m != nil {
		return m.IsOpen
	}
	return false
}

func (m *CheckGiftComposeEntryResp) GetMaterialsTolPrice() uint32 {
	if m != nil {
		return m.MaterialsTolPrice
	}
	return 0
}

func (m *CheckGiftComposeEntryResp) GetPoolFullValue() uint32 {
	if m != nil {
		return m.PoolFullValue
	}
	return 0
}

type CostMaterialInfo struct {
	GiftId               uint32   `protobuf:"varint,1,opt,name=gift_id,json=giftId,proto3" json:"gift_id"`
	GiftNum              uint32   `protobuf:"varint,2,opt,name=gift_num,json=giftNum,proto3" json:"gift_num"`
	UserItemId           uint32   `protobuf:"varint,3,opt,name=user_item_id,json=userItemId,proto3" json:"user_item_id"`
	GiftType             uint32   `protobuf:"varint,4,opt,name=gift_type,json=giftType,proto3" json:"gift_type"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CostMaterialInfo) Reset()         { *m = CostMaterialInfo{} }
func (m *CostMaterialInfo) String() string { return proto.CompactTextString(m) }
func (*CostMaterialInfo) ProtoMessage()    {}
func (*CostMaterialInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_revenue_http_logic_907ee343f90f615b, []int{29}
}
func (m *CostMaterialInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CostMaterialInfo.Unmarshal(m, b)
}
func (m *CostMaterialInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CostMaterialInfo.Marshal(b, m, deterministic)
}
func (dst *CostMaterialInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CostMaterialInfo.Merge(dst, src)
}
func (m *CostMaterialInfo) XXX_Size() int {
	return xxx_messageInfo_CostMaterialInfo.Size(m)
}
func (m *CostMaterialInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_CostMaterialInfo.DiscardUnknown(m)
}

var xxx_messageInfo_CostMaterialInfo proto.InternalMessageInfo

func (m *CostMaterialInfo) GetGiftId() uint32 {
	if m != nil {
		return m.GiftId
	}
	return 0
}

func (m *CostMaterialInfo) GetGiftNum() uint32 {
	if m != nil {
		return m.GiftNum
	}
	return 0
}

func (m *CostMaterialInfo) GetUserItemId() uint32 {
	if m != nil {
		return m.UserItemId
	}
	return 0
}

func (m *CostMaterialInfo) GetGiftType() uint32 {
	if m != nil {
		return m.GiftType
	}
	return 0
}

type DarkComposeGiftReq struct {
	Uid                  string              `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid"`
	ComposeId            uint32              `protobuf:"varint,2,opt,name=compose_id,json=composeId,proto3" json:"compose_id"`
	Num                  uint32              `protobuf:"varint,3,opt,name=num,proto3" json:"num"`
	MixList              []*CostMaterialInfo `protobuf:"bytes,4,rep,name=mix_list,json=mixList,proto3" json:"mix_list"`
	Cost                 uint32              `protobuf:"varint,5,opt,name=cost,proto3" json:"cost"`
	OsType               string              `protobuf:"bytes,6,opt,name=os_type,json=osType,proto3" json:"os_type"`
	App                  string              `protobuf:"bytes,7,opt,name=app,proto3" json:"app"`
	MarketId             string              `protobuf:"bytes,8,opt,name=market_id,json=marketId,proto3" json:"market_id"`
	Platform             string              `protobuf:"bytes,9,opt,name=platform,proto3" json:"platform"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *DarkComposeGiftReq) Reset()         { *m = DarkComposeGiftReq{} }
func (m *DarkComposeGiftReq) String() string { return proto.CompactTextString(m) }
func (*DarkComposeGiftReq) ProtoMessage()    {}
func (*DarkComposeGiftReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_revenue_http_logic_907ee343f90f615b, []int{30}
}
func (m *DarkComposeGiftReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DarkComposeGiftReq.Unmarshal(m, b)
}
func (m *DarkComposeGiftReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DarkComposeGiftReq.Marshal(b, m, deterministic)
}
func (dst *DarkComposeGiftReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DarkComposeGiftReq.Merge(dst, src)
}
func (m *DarkComposeGiftReq) XXX_Size() int {
	return xxx_messageInfo_DarkComposeGiftReq.Size(m)
}
func (m *DarkComposeGiftReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DarkComposeGiftReq.DiscardUnknown(m)
}

var xxx_messageInfo_DarkComposeGiftReq proto.InternalMessageInfo

func (m *DarkComposeGiftReq) GetUid() string {
	if m != nil {
		return m.Uid
	}
	return ""
}

func (m *DarkComposeGiftReq) GetComposeId() uint32 {
	if m != nil {
		return m.ComposeId
	}
	return 0
}

func (m *DarkComposeGiftReq) GetNum() uint32 {
	if m != nil {
		return m.Num
	}
	return 0
}

func (m *DarkComposeGiftReq) GetMixList() []*CostMaterialInfo {
	if m != nil {
		return m.MixList
	}
	return nil
}

func (m *DarkComposeGiftReq) GetCost() uint32 {
	if m != nil {
		return m.Cost
	}
	return 0
}

func (m *DarkComposeGiftReq) GetOsType() string {
	if m != nil {
		return m.OsType
	}
	return ""
}

func (m *DarkComposeGiftReq) GetApp() string {
	if m != nil {
		return m.App
	}
	return ""
}

func (m *DarkComposeGiftReq) GetMarketId() string {
	if m != nil {
		return m.MarketId
	}
	return ""
}

func (m *DarkComposeGiftReq) GetPlatform() string {
	if m != nil {
		return m.Platform
	}
	return ""
}

type DarkComposeGiftResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DarkComposeGiftResp) Reset()         { *m = DarkComposeGiftResp{} }
func (m *DarkComposeGiftResp) String() string { return proto.CompactTextString(m) }
func (*DarkComposeGiftResp) ProtoMessage()    {}
func (*DarkComposeGiftResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_revenue_http_logic_907ee343f90f615b, []int{31}
}
func (m *DarkComposeGiftResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DarkComposeGiftResp.Unmarshal(m, b)
}
func (m *DarkComposeGiftResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DarkComposeGiftResp.Marshal(b, m, deterministic)
}
func (dst *DarkComposeGiftResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DarkComposeGiftResp.Merge(dst, src)
}
func (m *DarkComposeGiftResp) XXX_Size() int {
	return xxx_messageInfo_DarkComposeGiftResp.Size(m)
}
func (m *DarkComposeGiftResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DarkComposeGiftResp.DiscardUnknown(m)
}

var xxx_messageInfo_DarkComposeGiftResp proto.InternalMessageInfo

type DarkComposeGiftConf struct {
	ComposeId            uint32   `protobuf:"varint,1,opt,name=compose_id,json=composeId,proto3" json:"compose_id"`
	GiftType             uint32   `protobuf:"varint,2,opt,name=gift_type,json=giftType,proto3" json:"gift_type"`
	GiftName             string   `protobuf:"bytes,3,opt,name=gift_name,json=giftName,proto3" json:"gift_name"`
	GiftPrice            uint32   `protobuf:"varint,4,opt,name=gift_price,json=giftPrice,proto3" json:"gift_price"`
	GiftUrl              string   `protobuf:"bytes,5,opt,name=gift_url,json=giftUrl,proto3" json:"gift_url"`
	SortId               uint32   `protobuf:"varint,6,opt,name=sort_id,json=sortId,proto3" json:"sort_id"`
	UpTime               uint32   `protobuf:"varint,7,opt,name=up_time,json=upTime,proto3" json:"up_time"`
	DownTime             uint32   `protobuf:"varint,8,opt,name=down_time,json=downTime,proto3" json:"down_time"`
	FinTime              uint32   `protobuf:"varint,9,opt,name=fin_time,json=finTime,proto3" json:"fin_time"`
	IsDarkGoddess        bool     `protobuf:"varint,10,opt,name=is_dark_goddess,json=isDarkGoddess,proto3" json:"is_dark_goddess"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DarkComposeGiftConf) Reset()         { *m = DarkComposeGiftConf{} }
func (m *DarkComposeGiftConf) String() string { return proto.CompactTextString(m) }
func (*DarkComposeGiftConf) ProtoMessage()    {}
func (*DarkComposeGiftConf) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_revenue_http_logic_907ee343f90f615b, []int{32}
}
func (m *DarkComposeGiftConf) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DarkComposeGiftConf.Unmarshal(m, b)
}
func (m *DarkComposeGiftConf) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DarkComposeGiftConf.Marshal(b, m, deterministic)
}
func (dst *DarkComposeGiftConf) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DarkComposeGiftConf.Merge(dst, src)
}
func (m *DarkComposeGiftConf) XXX_Size() int {
	return xxx_messageInfo_DarkComposeGiftConf.Size(m)
}
func (m *DarkComposeGiftConf) XXX_DiscardUnknown() {
	xxx_messageInfo_DarkComposeGiftConf.DiscardUnknown(m)
}

var xxx_messageInfo_DarkComposeGiftConf proto.InternalMessageInfo

func (m *DarkComposeGiftConf) GetComposeId() uint32 {
	if m != nil {
		return m.ComposeId
	}
	return 0
}

func (m *DarkComposeGiftConf) GetGiftType() uint32 {
	if m != nil {
		return m.GiftType
	}
	return 0
}

func (m *DarkComposeGiftConf) GetGiftName() string {
	if m != nil {
		return m.GiftName
	}
	return ""
}

func (m *DarkComposeGiftConf) GetGiftPrice() uint32 {
	if m != nil {
		return m.GiftPrice
	}
	return 0
}

func (m *DarkComposeGiftConf) GetGiftUrl() string {
	if m != nil {
		return m.GiftUrl
	}
	return ""
}

func (m *DarkComposeGiftConf) GetSortId() uint32 {
	if m != nil {
		return m.SortId
	}
	return 0
}

func (m *DarkComposeGiftConf) GetUpTime() uint32 {
	if m != nil {
		return m.UpTime
	}
	return 0
}

func (m *DarkComposeGiftConf) GetDownTime() uint32 {
	if m != nil {
		return m.DownTime
	}
	return 0
}

func (m *DarkComposeGiftConf) GetFinTime() uint32 {
	if m != nil {
		return m.FinTime
	}
	return 0
}

func (m *DarkComposeGiftConf) GetIsDarkGoddess() bool {
	if m != nil {
		return m.IsDarkGoddess
	}
	return false
}

// 黑暗礼物合成主页信息
type GetGiftComposeHomePageInfoReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGiftComposeHomePageInfoReq) Reset()         { *m = GetGiftComposeHomePageInfoReq{} }
func (m *GetGiftComposeHomePageInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetGiftComposeHomePageInfoReq) ProtoMessage()    {}
func (*GetGiftComposeHomePageInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_revenue_http_logic_907ee343f90f615b, []int{33}
}
func (m *GetGiftComposeHomePageInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGiftComposeHomePageInfoReq.Unmarshal(m, b)
}
func (m *GetGiftComposeHomePageInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGiftComposeHomePageInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetGiftComposeHomePageInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGiftComposeHomePageInfoReq.Merge(dst, src)
}
func (m *GetGiftComposeHomePageInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetGiftComposeHomePageInfoReq.Size(m)
}
func (m *GetGiftComposeHomePageInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGiftComposeHomePageInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetGiftComposeHomePageInfoReq proto.InternalMessageInfo

type GetGiftComposeHomePageInfoResp struct {
	EnergyStoneNum       uint32                 `protobuf:"varint,1,opt,name=energy_stone_num,json=energyStoneNum,proto3" json:"energy_stone_num"`
	MaterialsTolPrice    uint32                 `protobuf:"varint,2,opt,name=materials_tol_price,json=materialsTolPrice,proto3" json:"materials_tol_price"`
	PoolFullValue        uint32                 `protobuf:"varint,3,opt,name=pool_full_value,json=poolFullValue,proto3" json:"pool_full_value"`
	DarkGoddessPrice     uint32                 `protobuf:"varint,4,opt,name=dark_goddess_price,json=darkGoddessPrice,proto3" json:"dark_goddess_price"`
	ComposeGiftConfList  []*DarkComposeGiftConf `protobuf:"bytes,5,rep,name=compose_gift_conf_list,json=composeGiftConfList,proto3" json:"compose_gift_conf_list"`
	CanComposeIdList     []uint32               `protobuf:"varint,6,rep,packed,name=can_compose_id_list,json=canComposeIdList,proto3" json:"can_compose_id_list"`
	GoddessComposeId     uint32                 `protobuf:"varint,7,opt,name=goddess_compose_id,json=goddessComposeId,proto3" json:"goddess_compose_id"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *GetGiftComposeHomePageInfoResp) Reset()         { *m = GetGiftComposeHomePageInfoResp{} }
func (m *GetGiftComposeHomePageInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetGiftComposeHomePageInfoResp) ProtoMessage()    {}
func (*GetGiftComposeHomePageInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_revenue_http_logic_907ee343f90f615b, []int{34}
}
func (m *GetGiftComposeHomePageInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGiftComposeHomePageInfoResp.Unmarshal(m, b)
}
func (m *GetGiftComposeHomePageInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGiftComposeHomePageInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetGiftComposeHomePageInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGiftComposeHomePageInfoResp.Merge(dst, src)
}
func (m *GetGiftComposeHomePageInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetGiftComposeHomePageInfoResp.Size(m)
}
func (m *GetGiftComposeHomePageInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGiftComposeHomePageInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetGiftComposeHomePageInfoResp proto.InternalMessageInfo

func (m *GetGiftComposeHomePageInfoResp) GetEnergyStoneNum() uint32 {
	if m != nil {
		return m.EnergyStoneNum
	}
	return 0
}

func (m *GetGiftComposeHomePageInfoResp) GetMaterialsTolPrice() uint32 {
	if m != nil {
		return m.MaterialsTolPrice
	}
	return 0
}

func (m *GetGiftComposeHomePageInfoResp) GetPoolFullValue() uint32 {
	if m != nil {
		return m.PoolFullValue
	}
	return 0
}

func (m *GetGiftComposeHomePageInfoResp) GetDarkGoddessPrice() uint32 {
	if m != nil {
		return m.DarkGoddessPrice
	}
	return 0
}

func (m *GetGiftComposeHomePageInfoResp) GetComposeGiftConfList() []*DarkComposeGiftConf {
	if m != nil {
		return m.ComposeGiftConfList
	}
	return nil
}

func (m *GetGiftComposeHomePageInfoResp) GetCanComposeIdList() []uint32 {
	if m != nil {
		return m.CanComposeIdList
	}
	return nil
}

func (m *GetGiftComposeHomePageInfoResp) GetGoddessComposeId() uint32 {
	if m != nil {
		return m.GoddessComposeId
	}
	return 0
}

// 合成记录
type DarkComposeLog struct {
	OrderId              string   `protobuf:"bytes,1,opt,name=order_id,json=orderId,proto3" json:"order_id"`
	ComposeTime          uint32   `protobuf:"varint,2,opt,name=compose_time,json=composeTime,proto3" json:"compose_time"`
	GiftName             string   `protobuf:"bytes,3,opt,name=gift_name,json=giftName,proto3" json:"gift_name"`
	GiftPrice            uint32   `protobuf:"varint,4,opt,name=gift_price,json=giftPrice,proto3" json:"gift_price"`
	GiftNum              uint32   `protobuf:"varint,5,opt,name=gift_num,json=giftNum,proto3" json:"gift_num"`
	GiftUrl              string   `protobuf:"bytes,6,opt,name=gift_url,json=giftUrl,proto3" json:"gift_url"`
	CostDesc             string   `protobuf:"bytes,7,opt,name=cost_desc,json=costDesc,proto3" json:"cost_desc"`
	Cost                 uint32   `protobuf:"varint,8,opt,name=cost,proto3" json:"cost"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DarkComposeLog) Reset()         { *m = DarkComposeLog{} }
func (m *DarkComposeLog) String() string { return proto.CompactTextString(m) }
func (*DarkComposeLog) ProtoMessage()    {}
func (*DarkComposeLog) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_revenue_http_logic_907ee343f90f615b, []int{35}
}
func (m *DarkComposeLog) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DarkComposeLog.Unmarshal(m, b)
}
func (m *DarkComposeLog) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DarkComposeLog.Marshal(b, m, deterministic)
}
func (dst *DarkComposeLog) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DarkComposeLog.Merge(dst, src)
}
func (m *DarkComposeLog) XXX_Size() int {
	return xxx_messageInfo_DarkComposeLog.Size(m)
}
func (m *DarkComposeLog) XXX_DiscardUnknown() {
	xxx_messageInfo_DarkComposeLog.DiscardUnknown(m)
}

var xxx_messageInfo_DarkComposeLog proto.InternalMessageInfo

func (m *DarkComposeLog) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *DarkComposeLog) GetComposeTime() uint32 {
	if m != nil {
		return m.ComposeTime
	}
	return 0
}

func (m *DarkComposeLog) GetGiftName() string {
	if m != nil {
		return m.GiftName
	}
	return ""
}

func (m *DarkComposeLog) GetGiftPrice() uint32 {
	if m != nil {
		return m.GiftPrice
	}
	return 0
}

func (m *DarkComposeLog) GetGiftNum() uint32 {
	if m != nil {
		return m.GiftNum
	}
	return 0
}

func (m *DarkComposeLog) GetGiftUrl() string {
	if m != nil {
		return m.GiftUrl
	}
	return ""
}

func (m *DarkComposeLog) GetCostDesc() string {
	if m != nil {
		return m.CostDesc
	}
	return ""
}

func (m *DarkComposeLog) GetCost() uint32 {
	if m != nil {
		return m.Cost
	}
	return 0
}

type GetUserDarkComposeLogsReq struct {
	Uid                  string   `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid"`
	Offset               uint32   `protobuf:"varint,2,opt,name=offset,proto3" json:"offset"`
	PageNum              uint32   `protobuf:"varint,3,opt,name=pageNum,proto3" json:"pageNum"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserDarkComposeLogsReq) Reset()         { *m = GetUserDarkComposeLogsReq{} }
func (m *GetUserDarkComposeLogsReq) String() string { return proto.CompactTextString(m) }
func (*GetUserDarkComposeLogsReq) ProtoMessage()    {}
func (*GetUserDarkComposeLogsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_revenue_http_logic_907ee343f90f615b, []int{36}
}
func (m *GetUserDarkComposeLogsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserDarkComposeLogsReq.Unmarshal(m, b)
}
func (m *GetUserDarkComposeLogsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserDarkComposeLogsReq.Marshal(b, m, deterministic)
}
func (dst *GetUserDarkComposeLogsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserDarkComposeLogsReq.Merge(dst, src)
}
func (m *GetUserDarkComposeLogsReq) XXX_Size() int {
	return xxx_messageInfo_GetUserDarkComposeLogsReq.Size(m)
}
func (m *GetUserDarkComposeLogsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserDarkComposeLogsReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserDarkComposeLogsReq proto.InternalMessageInfo

func (m *GetUserDarkComposeLogsReq) GetUid() string {
	if m != nil {
		return m.Uid
	}
	return ""
}

func (m *GetUserDarkComposeLogsReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetUserDarkComposeLogsReq) GetPageNum() uint32 {
	if m != nil {
		return m.PageNum
	}
	return 0
}

type GetUserDarkComposeLogsResp struct {
	Logs                 []*DarkComposeLog `protobuf:"bytes,1,rep,name=logs,proto3" json:"logs"`
	HasNextPage          bool              `protobuf:"varint,2,opt,name=hasNextPage,proto3" json:"hasNextPage"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetUserDarkComposeLogsResp) Reset()         { *m = GetUserDarkComposeLogsResp{} }
func (m *GetUserDarkComposeLogsResp) String() string { return proto.CompactTextString(m) }
func (*GetUserDarkComposeLogsResp) ProtoMessage()    {}
func (*GetUserDarkComposeLogsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_revenue_http_logic_907ee343f90f615b, []int{37}
}
func (m *GetUserDarkComposeLogsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserDarkComposeLogsResp.Unmarshal(m, b)
}
func (m *GetUserDarkComposeLogsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserDarkComposeLogsResp.Marshal(b, m, deterministic)
}
func (dst *GetUserDarkComposeLogsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserDarkComposeLogsResp.Merge(dst, src)
}
func (m *GetUserDarkComposeLogsResp) XXX_Size() int {
	return xxx_messageInfo_GetUserDarkComposeLogsResp.Size(m)
}
func (m *GetUserDarkComposeLogsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserDarkComposeLogsResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserDarkComposeLogsResp proto.InternalMessageInfo

func (m *GetUserDarkComposeLogsResp) GetLogs() []*DarkComposeLog {
	if m != nil {
		return m.Logs
	}
	return nil
}

func (m *GetUserDarkComposeLogsResp) GetHasNextPage() bool {
	if m != nil {
		return m.HasNextPage
	}
	return false
}

type DarkMaterialInfo struct {
	GiftId               uint32   `protobuf:"varint,1,opt,name=gift_id,json=giftId,proto3" json:"gift_id"`
	GiftNum              uint32   `protobuf:"varint,2,opt,name=gift_num,json=giftNum,proto3" json:"gift_num"`
	GiftName             string   `protobuf:"bytes,3,opt,name=gift_name,json=giftName,proto3" json:"gift_name"`
	GiftPrice            uint32   `protobuf:"varint,4,opt,name=gift_price,json=giftPrice,proto3" json:"gift_price"`
	GiftUrl              string   `protobuf:"bytes,5,opt,name=gift_url,json=giftUrl,proto3" json:"gift_url"`
	SortId               uint32   `protobuf:"varint,6,opt,name=sort_id,json=sortId,proto3" json:"sort_id"`
	UserItemId           uint32   `protobuf:"varint,7,opt,name=user_item_id,json=userItemId,proto3" json:"user_item_id"`
	FinTime              uint32   `protobuf:"varint,8,opt,name=fin_time,json=finTime,proto3" json:"fin_time"`
	GiftType             uint32   `protobuf:"varint,9,opt,name=gift_type,json=giftType,proto3" json:"gift_type"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DarkMaterialInfo) Reset()         { *m = DarkMaterialInfo{} }
func (m *DarkMaterialInfo) String() string { return proto.CompactTextString(m) }
func (*DarkMaterialInfo) ProtoMessage()    {}
func (*DarkMaterialInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_revenue_http_logic_907ee343f90f615b, []int{38}
}
func (m *DarkMaterialInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DarkMaterialInfo.Unmarshal(m, b)
}
func (m *DarkMaterialInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DarkMaterialInfo.Marshal(b, m, deterministic)
}
func (dst *DarkMaterialInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DarkMaterialInfo.Merge(dst, src)
}
func (m *DarkMaterialInfo) XXX_Size() int {
	return xxx_messageInfo_DarkMaterialInfo.Size(m)
}
func (m *DarkMaterialInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_DarkMaterialInfo.DiscardUnknown(m)
}

var xxx_messageInfo_DarkMaterialInfo proto.InternalMessageInfo

func (m *DarkMaterialInfo) GetGiftId() uint32 {
	if m != nil {
		return m.GiftId
	}
	return 0
}

func (m *DarkMaterialInfo) GetGiftNum() uint32 {
	if m != nil {
		return m.GiftNum
	}
	return 0
}

func (m *DarkMaterialInfo) GetGiftName() string {
	if m != nil {
		return m.GiftName
	}
	return ""
}

func (m *DarkMaterialInfo) GetGiftPrice() uint32 {
	if m != nil {
		return m.GiftPrice
	}
	return 0
}

func (m *DarkMaterialInfo) GetGiftUrl() string {
	if m != nil {
		return m.GiftUrl
	}
	return ""
}

func (m *DarkMaterialInfo) GetSortId() uint32 {
	if m != nil {
		return m.SortId
	}
	return 0
}

func (m *DarkMaterialInfo) GetUserItemId() uint32 {
	if m != nil {
		return m.UserItemId
	}
	return 0
}

func (m *DarkMaterialInfo) GetFinTime() uint32 {
	if m != nil {
		return m.FinTime
	}
	return 0
}

func (m *DarkMaterialInfo) GetGiftType() uint32 {
	if m != nil {
		return m.GiftType
	}
	return 0
}

// 获取用户背包中指定合成礼物对应的合成原料
type GetUserGift2MaterialsListReq struct {
	Uid                  string   `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid"`
	ComposeId            uint32   `protobuf:"varint,2,opt,name=compose_id,json=composeId,proto3" json:"compose_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserGift2MaterialsListReq) Reset()         { *m = GetUserGift2MaterialsListReq{} }
func (m *GetUserGift2MaterialsListReq) String() string { return proto.CompactTextString(m) }
func (*GetUserGift2MaterialsListReq) ProtoMessage()    {}
func (*GetUserGift2MaterialsListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_revenue_http_logic_907ee343f90f615b, []int{39}
}
func (m *GetUserGift2MaterialsListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserGift2MaterialsListReq.Unmarshal(m, b)
}
func (m *GetUserGift2MaterialsListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserGift2MaterialsListReq.Marshal(b, m, deterministic)
}
func (dst *GetUserGift2MaterialsListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserGift2MaterialsListReq.Merge(dst, src)
}
func (m *GetUserGift2MaterialsListReq) XXX_Size() int {
	return xxx_messageInfo_GetUserGift2MaterialsListReq.Size(m)
}
func (m *GetUserGift2MaterialsListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserGift2MaterialsListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserGift2MaterialsListReq proto.InternalMessageInfo

func (m *GetUserGift2MaterialsListReq) GetUid() string {
	if m != nil {
		return m.Uid
	}
	return ""
}

func (m *GetUserGift2MaterialsListReq) GetComposeId() uint32 {
	if m != nil {
		return m.ComposeId
	}
	return 0
}

type GetUserGift2MaterialsListResp struct {
	MaterialList         []*DarkMaterialInfo `protobuf:"bytes,1,rep,name=material_list,json=materialList,proto3" json:"material_list"`
	MergeFragmentIdList  []uint32            `protobuf:"varint,2,rep,packed,name=merge_fragment_id_list,json=mergeFragmentIdList,proto3" json:"merge_fragment_id_list"`
	MergeGiftIdList      []uint32            `protobuf:"varint,3,rep,packed,name=merge_gift_id_list,json=mergeGiftIdList,proto3" json:"merge_gift_id_list"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *GetUserGift2MaterialsListResp) Reset()         { *m = GetUserGift2MaterialsListResp{} }
func (m *GetUserGift2MaterialsListResp) String() string { return proto.CompactTextString(m) }
func (*GetUserGift2MaterialsListResp) ProtoMessage()    {}
func (*GetUserGift2MaterialsListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_revenue_http_logic_907ee343f90f615b, []int{40}
}
func (m *GetUserGift2MaterialsListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserGift2MaterialsListResp.Unmarshal(m, b)
}
func (m *GetUserGift2MaterialsListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserGift2MaterialsListResp.Marshal(b, m, deterministic)
}
func (dst *GetUserGift2MaterialsListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserGift2MaterialsListResp.Merge(dst, src)
}
func (m *GetUserGift2MaterialsListResp) XXX_Size() int {
	return xxx_messageInfo_GetUserGift2MaterialsListResp.Size(m)
}
func (m *GetUserGift2MaterialsListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserGift2MaterialsListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserGift2MaterialsListResp proto.InternalMessageInfo

func (m *GetUserGift2MaterialsListResp) GetMaterialList() []*DarkMaterialInfo {
	if m != nil {
		return m.MaterialList
	}
	return nil
}

func (m *GetUserGift2MaterialsListResp) GetMergeFragmentIdList() []uint32 {
	if m != nil {
		return m.MergeFragmentIdList
	}
	return nil
}

func (m *GetUserGift2MaterialsListResp) GetMergeGiftIdList() []uint32 {
	if m != nil {
		return m.MergeGiftIdList
	}
	return nil
}

// 获取用户拥有的所有原料列表
type GetAllUserMaterialListReq struct {
	Uid                  string   `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAllUserMaterialListReq) Reset()         { *m = GetAllUserMaterialListReq{} }
func (m *GetAllUserMaterialListReq) String() string { return proto.CompactTextString(m) }
func (*GetAllUserMaterialListReq) ProtoMessage()    {}
func (*GetAllUserMaterialListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_revenue_http_logic_907ee343f90f615b, []int{41}
}
func (m *GetAllUserMaterialListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllUserMaterialListReq.Unmarshal(m, b)
}
func (m *GetAllUserMaterialListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllUserMaterialListReq.Marshal(b, m, deterministic)
}
func (dst *GetAllUserMaterialListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllUserMaterialListReq.Merge(dst, src)
}
func (m *GetAllUserMaterialListReq) XXX_Size() int {
	return xxx_messageInfo_GetAllUserMaterialListReq.Size(m)
}
func (m *GetAllUserMaterialListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllUserMaterialListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllUserMaterialListReq proto.InternalMessageInfo

func (m *GetAllUserMaterialListReq) GetUid() string {
	if m != nil {
		return m.Uid
	}
	return ""
}

type GetAllUserMaterialListResp struct {
	MaterialList         []*DarkMaterialInfo `protobuf:"bytes,1,rep,name=material_list,json=materialList,proto3" json:"material_list"`
	MergeFragmentIdList  []uint32            `protobuf:"varint,2,rep,packed,name=merge_fragment_id_list,json=mergeFragmentIdList,proto3" json:"merge_fragment_id_list"`
	MergeGiftIdList      []uint32            `protobuf:"varint,3,rep,packed,name=merge_gift_id_list,json=mergeGiftIdList,proto3" json:"merge_gift_id_list"`
	PresentMaterialList  []*DarkMaterialInfo `protobuf:"bytes,4,rep,name=present_material_list,json=presentMaterialList,proto3" json:"present_material_list"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *GetAllUserMaterialListResp) Reset()         { *m = GetAllUserMaterialListResp{} }
func (m *GetAllUserMaterialListResp) String() string { return proto.CompactTextString(m) }
func (*GetAllUserMaterialListResp) ProtoMessage()    {}
func (*GetAllUserMaterialListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_revenue_http_logic_907ee343f90f615b, []int{42}
}
func (m *GetAllUserMaterialListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllUserMaterialListResp.Unmarshal(m, b)
}
func (m *GetAllUserMaterialListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllUserMaterialListResp.Marshal(b, m, deterministic)
}
func (dst *GetAllUserMaterialListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllUserMaterialListResp.Merge(dst, src)
}
func (m *GetAllUserMaterialListResp) XXX_Size() int {
	return xxx_messageInfo_GetAllUserMaterialListResp.Size(m)
}
func (m *GetAllUserMaterialListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllUserMaterialListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllUserMaterialListResp proto.InternalMessageInfo

func (m *GetAllUserMaterialListResp) GetMaterialList() []*DarkMaterialInfo {
	if m != nil {
		return m.MaterialList
	}
	return nil
}

func (m *GetAllUserMaterialListResp) GetMergeFragmentIdList() []uint32 {
	if m != nil {
		return m.MergeFragmentIdList
	}
	return nil
}

func (m *GetAllUserMaterialListResp) GetMergeGiftIdList() []uint32 {
	if m != nil {
		return m.MergeGiftIdList
	}
	return nil
}

func (m *GetAllUserMaterialListResp) GetPresentMaterialList() []*DarkMaterialInfo {
	if m != nil {
		return m.PresentMaterialList
	}
	return nil
}

type DarkGiftBonusLog struct {
	OrderId              string   `protobuf:"bytes,1,opt,name=order_id,json=orderId,proto3" json:"order_id"`
	GiftId               uint32   `protobuf:"varint,2,opt,name=gift_id,json=giftId,proto3" json:"gift_id"`
	GiftCnt              uint32   `protobuf:"varint,3,opt,name=gift_cnt,json=giftCnt,proto3" json:"gift_cnt"`
	GiftName             string   `protobuf:"bytes,4,opt,name=gift_name,json=giftName,proto3" json:"gift_name"`
	GiftPic              string   `protobuf:"bytes,5,opt,name=gift_pic,json=giftPic,proto3" json:"gift_pic"`
	Source               string   `protobuf:"bytes,6,opt,name=source,proto3" json:"source"`
	CreateTime           uint32   `protobuf:"varint,7,opt,name=create_time,json=createTime,proto3" json:"create_time"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DarkGiftBonusLog) Reset()         { *m = DarkGiftBonusLog{} }
func (m *DarkGiftBonusLog) String() string { return proto.CompactTextString(m) }
func (*DarkGiftBonusLog) ProtoMessage()    {}
func (*DarkGiftBonusLog) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_revenue_http_logic_907ee343f90f615b, []int{43}
}
func (m *DarkGiftBonusLog) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DarkGiftBonusLog.Unmarshal(m, b)
}
func (m *DarkGiftBonusLog) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DarkGiftBonusLog.Marshal(b, m, deterministic)
}
func (dst *DarkGiftBonusLog) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DarkGiftBonusLog.Merge(dst, src)
}
func (m *DarkGiftBonusLog) XXX_Size() int {
	return xxx_messageInfo_DarkGiftBonusLog.Size(m)
}
func (m *DarkGiftBonusLog) XXX_DiscardUnknown() {
	xxx_messageInfo_DarkGiftBonusLog.DiscardUnknown(m)
}

var xxx_messageInfo_DarkGiftBonusLog proto.InternalMessageInfo

func (m *DarkGiftBonusLog) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *DarkGiftBonusLog) GetGiftId() uint32 {
	if m != nil {
		return m.GiftId
	}
	return 0
}

func (m *DarkGiftBonusLog) GetGiftCnt() uint32 {
	if m != nil {
		return m.GiftCnt
	}
	return 0
}

func (m *DarkGiftBonusLog) GetGiftName() string {
	if m != nil {
		return m.GiftName
	}
	return ""
}

func (m *DarkGiftBonusLog) GetGiftPic() string {
	if m != nil {
		return m.GiftPic
	}
	return ""
}

func (m *DarkGiftBonusLog) GetSource() string {
	if m != nil {
		return m.Source
	}
	return ""
}

func (m *DarkGiftBonusLog) GetCreateTime() uint32 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

type GetDarkGiftBonusLogsReq struct {
	Uid                  string   `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid"`
	Offset               uint32   `protobuf:"varint,2,opt,name=offset,proto3" json:"offset"`
	Limit                uint32   `protobuf:"varint,3,opt,name=limit,proto3" json:"limit"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetDarkGiftBonusLogsReq) Reset()         { *m = GetDarkGiftBonusLogsReq{} }
func (m *GetDarkGiftBonusLogsReq) String() string { return proto.CompactTextString(m) }
func (*GetDarkGiftBonusLogsReq) ProtoMessage()    {}
func (*GetDarkGiftBonusLogsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_revenue_http_logic_907ee343f90f615b, []int{44}
}
func (m *GetDarkGiftBonusLogsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetDarkGiftBonusLogsReq.Unmarshal(m, b)
}
func (m *GetDarkGiftBonusLogsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetDarkGiftBonusLogsReq.Marshal(b, m, deterministic)
}
func (dst *GetDarkGiftBonusLogsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetDarkGiftBonusLogsReq.Merge(dst, src)
}
func (m *GetDarkGiftBonusLogsReq) XXX_Size() int {
	return xxx_messageInfo_GetDarkGiftBonusLogsReq.Size(m)
}
func (m *GetDarkGiftBonusLogsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetDarkGiftBonusLogsReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetDarkGiftBonusLogsReq proto.InternalMessageInfo

func (m *GetDarkGiftBonusLogsReq) GetUid() string {
	if m != nil {
		return m.Uid
	}
	return ""
}

func (m *GetDarkGiftBonusLogsReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetDarkGiftBonusLogsReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type GetDarkGiftBonusLogsResp struct {
	List                 []*DarkGiftBonusLog `protobuf:"bytes,1,rep,name=list,proto3" json:"list"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *GetDarkGiftBonusLogsResp) Reset()         { *m = GetDarkGiftBonusLogsResp{} }
func (m *GetDarkGiftBonusLogsResp) String() string { return proto.CompactTextString(m) }
func (*GetDarkGiftBonusLogsResp) ProtoMessage()    {}
func (*GetDarkGiftBonusLogsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_revenue_http_logic_907ee343f90f615b, []int{45}
}
func (m *GetDarkGiftBonusLogsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetDarkGiftBonusLogsResp.Unmarshal(m, b)
}
func (m *GetDarkGiftBonusLogsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetDarkGiftBonusLogsResp.Marshal(b, m, deterministic)
}
func (dst *GetDarkGiftBonusLogsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetDarkGiftBonusLogsResp.Merge(dst, src)
}
func (m *GetDarkGiftBonusLogsResp) XXX_Size() int {
	return xxx_messageInfo_GetDarkGiftBonusLogsResp.Size(m)
}
func (m *GetDarkGiftBonusLogsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetDarkGiftBonusLogsResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetDarkGiftBonusLogsResp proto.InternalMessageInfo

func (m *GetDarkGiftBonusLogsResp) GetList() []*DarkGiftBonusLog {
	if m != nil {
		return m.List
	}
	return nil
}

// 获取挚友信息
type GetFellowInfoReq struct {
	MyUid                uint32   `protobuf:"varint,1,opt,name=my_uid,json=myUid,proto3" json:"my_uid"`
	FellowUid            uint32   `protobuf:"varint,2,opt,name=fellow_uid,json=fellowUid,proto3" json:"fellow_uid"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetFellowInfoReq) Reset()         { *m = GetFellowInfoReq{} }
func (m *GetFellowInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetFellowInfoReq) ProtoMessage()    {}
func (*GetFellowInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_revenue_http_logic_907ee343f90f615b, []int{46}
}
func (m *GetFellowInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetFellowInfoReq.Unmarshal(m, b)
}
func (m *GetFellowInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetFellowInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetFellowInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetFellowInfoReq.Merge(dst, src)
}
func (m *GetFellowInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetFellowInfoReq.Size(m)
}
func (m *GetFellowInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetFellowInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetFellowInfoReq proto.InternalMessageInfo

func (m *GetFellowInfoReq) GetMyUid() uint32 {
	if m != nil {
		return m.MyUid
	}
	return 0
}

func (m *GetFellowInfoReq) GetFellowUid() uint32 {
	if m != nil {
		return m.FellowUid
	}
	return 0
}

type FellowTask struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	AddPoint             float32  `protobuf:"fixed32,2,opt,name=add_point,json=addPoint,proto3" json:"add_point"`
	MaxPoint             uint32   `protobuf:"varint,3,opt,name=max_point,json=maxPoint,proto3" json:"max_point"`
	MaxCnt               uint32   `protobuf:"varint,4,opt,name=max_cnt,json=maxCnt,proto3" json:"max_cnt"`
	CompleteCnt          uint32   `protobuf:"varint,5,opt,name=complete_cnt,json=completeCnt,proto3" json:"complete_cnt"`
	Title                string   `protobuf:"bytes,6,opt,name=title,proto3" json:"title"`
	JumpUrl              string   `protobuf:"bytes,7,opt,name=jump_url,json=jumpUrl,proto3" json:"jump_url"`
	Icon                 string   `protobuf:"bytes,8,opt,name=icon,proto3" json:"icon"`
	Desc                 string   `protobuf:"bytes,9,opt,name=desc,proto3" json:"desc"`
	ButtonText           string   `protobuf:"bytes,10,opt,name=button_text,json=buttonText,proto3" json:"button_text"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *FellowTask) Reset()         { *m = FellowTask{} }
func (m *FellowTask) String() string { return proto.CompactTextString(m) }
func (*FellowTask) ProtoMessage()    {}
func (*FellowTask) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_revenue_http_logic_907ee343f90f615b, []int{47}
}
func (m *FellowTask) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FellowTask.Unmarshal(m, b)
}
func (m *FellowTask) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FellowTask.Marshal(b, m, deterministic)
}
func (dst *FellowTask) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FellowTask.Merge(dst, src)
}
func (m *FellowTask) XXX_Size() int {
	return xxx_messageInfo_FellowTask.Size(m)
}
func (m *FellowTask) XXX_DiscardUnknown() {
	xxx_messageInfo_FellowTask.DiscardUnknown(m)
}

var xxx_messageInfo_FellowTask proto.InternalMessageInfo

func (m *FellowTask) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *FellowTask) GetAddPoint() float32 {
	if m != nil {
		return m.AddPoint
	}
	return 0
}

func (m *FellowTask) GetMaxPoint() uint32 {
	if m != nil {
		return m.MaxPoint
	}
	return 0
}

func (m *FellowTask) GetMaxCnt() uint32 {
	if m != nil {
		return m.MaxCnt
	}
	return 0
}

func (m *FellowTask) GetCompleteCnt() uint32 {
	if m != nil {
		return m.CompleteCnt
	}
	return 0
}

func (m *FellowTask) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *FellowTask) GetJumpUrl() string {
	if m != nil {
		return m.JumpUrl
	}
	return ""
}

func (m *FellowTask) GetIcon() string {
	if m != nil {
		return m.Icon
	}
	return ""
}

func (m *FellowTask) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

func (m *FellowTask) GetButtonText() string {
	if m != nil {
		return m.ButtonText
	}
	return ""
}

type FellowLevelConfig struct {
	Level                uint32   `protobuf:"varint,1,opt,name=level,proto3" json:"level"`
	Point                uint32   `protobuf:"varint,2,opt,name=point,proto3" json:"point"`
	Desc                 string   `protobuf:"bytes,3,opt,name=desc,proto3" json:"desc"`
	Award                string   `protobuf:"bytes,4,opt,name=award,proto3" json:"award"`
	Icon                 string   `protobuf:"bytes,5,opt,name=icon,proto3" json:"icon"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *FellowLevelConfig) Reset()         { *m = FellowLevelConfig{} }
func (m *FellowLevelConfig) String() string { return proto.CompactTextString(m) }
func (*FellowLevelConfig) ProtoMessage()    {}
func (*FellowLevelConfig) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_revenue_http_logic_907ee343f90f615b, []int{48}
}
func (m *FellowLevelConfig) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FellowLevelConfig.Unmarshal(m, b)
}
func (m *FellowLevelConfig) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FellowLevelConfig.Marshal(b, m, deterministic)
}
func (dst *FellowLevelConfig) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FellowLevelConfig.Merge(dst, src)
}
func (m *FellowLevelConfig) XXX_Size() int {
	return xxx_messageInfo_FellowLevelConfig.Size(m)
}
func (m *FellowLevelConfig) XXX_DiscardUnknown() {
	xxx_messageInfo_FellowLevelConfig.DiscardUnknown(m)
}

var xxx_messageInfo_FellowLevelConfig proto.InternalMessageInfo

func (m *FellowLevelConfig) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

func (m *FellowLevelConfig) GetPoint() uint32 {
	if m != nil {
		return m.Point
	}
	return 0
}

func (m *FellowLevelConfig) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

func (m *FellowLevelConfig) GetAward() string {
	if m != nil {
		return m.Award
	}
	return ""
}

func (m *FellowLevelConfig) GetIcon() string {
	if m != nil {
		return m.Icon
	}
	return ""
}

// 在榜信息
type OnAccompanyRankInfo struct {
	RankType             uint32   `protobuf:"varint,1,opt,name=rank_type,json=rankType,proto3" json:"rank_type"`
	Rank                 uint32   `protobuf:"varint,2,opt,name=rank,proto3" json:"rank"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *OnAccompanyRankInfo) Reset()         { *m = OnAccompanyRankInfo{} }
func (m *OnAccompanyRankInfo) String() string { return proto.CompactTextString(m) }
func (*OnAccompanyRankInfo) ProtoMessage()    {}
func (*OnAccompanyRankInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_revenue_http_logic_907ee343f90f615b, []int{49}
}
func (m *OnAccompanyRankInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OnAccompanyRankInfo.Unmarshal(m, b)
}
func (m *OnAccompanyRankInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OnAccompanyRankInfo.Marshal(b, m, deterministic)
}
func (dst *OnAccompanyRankInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OnAccompanyRankInfo.Merge(dst, src)
}
func (m *OnAccompanyRankInfo) XXX_Size() int {
	return xxx_messageInfo_OnAccompanyRankInfo.Size(m)
}
func (m *OnAccompanyRankInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_OnAccompanyRankInfo.DiscardUnknown(m)
}

var xxx_messageInfo_OnAccompanyRankInfo proto.InternalMessageInfo

func (m *OnAccompanyRankInfo) GetRankType() uint32 {
	if m != nil {
		return m.RankType
	}
	return 0
}

func (m *OnAccompanyRankInfo) GetRank() uint32 {
	if m != nil {
		return m.Rank
	}
	return 0
}

// 段位信息 （一个等级范围属于一个段位）
type GradingInfo struct {
	Level                uint32   `protobuf:"varint,1,opt,name=level,proto3" json:"level"`
	GradingName          string   `protobuf:"bytes,2,opt,name=grading_name,json=gradingName,proto3" json:"grading_name"`
	GradingIcon          string   `protobuf:"bytes,3,opt,name=grading_icon,json=gradingIcon,proto3" json:"grading_icon"`
	GradingColor         string   `protobuf:"bytes,4,opt,name=grading_color,json=gradingColor,proto3" json:"grading_color"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GradingInfo) Reset()         { *m = GradingInfo{} }
func (m *GradingInfo) String() string { return proto.CompactTextString(m) }
func (*GradingInfo) ProtoMessage()    {}
func (*GradingInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_revenue_http_logic_907ee343f90f615b, []int{50}
}
func (m *GradingInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GradingInfo.Unmarshal(m, b)
}
func (m *GradingInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GradingInfo.Marshal(b, m, deterministic)
}
func (dst *GradingInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GradingInfo.Merge(dst, src)
}
func (m *GradingInfo) XXX_Size() int {
	return xxx_messageInfo_GradingInfo.Size(m)
}
func (m *GradingInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_GradingInfo.DiscardUnknown(m)
}

var xxx_messageInfo_GradingInfo proto.InternalMessageInfo

func (m *GradingInfo) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

func (m *GradingInfo) GetGradingName() string {
	if m != nil {
		return m.GradingName
	}
	return ""
}

func (m *GradingInfo) GetGradingIcon() string {
	if m != nil {
		return m.GradingIcon
	}
	return ""
}

func (m *GradingInfo) GetGradingColor() string {
	if m != nil {
		return m.GradingColor
	}
	return ""
}

// 获取挚友信息
type GetFellowInfoResp struct {
	Uid                  uint32                 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid"`
	FellowUid            uint32                 `protobuf:"varint,2,opt,name=fellow_uid,json=fellowUid,proto3" json:"fellow_uid"`
	FellowPoint          uint32                 `protobuf:"varint,3,opt,name=fellow_point,json=fellowPoint,proto3" json:"fellow_point"`
	BindType             uint32                 `protobuf:"varint,4,opt,name=bind_type,json=bindType,proto3" json:"bind_type"`
	FellowType           uint32                 `protobuf:"varint,5,opt,name=fellow_type,json=fellowType,proto3" json:"fellow_type"`
	Task                 []*FellowTask          `protobuf:"bytes,6,rep,name=task,proto3" json:"task"`
	LevelConfig          []*FellowLevelConfig   `protobuf:"bytes,7,rep,name=level_config,json=levelConfig,proto3" json:"level_config"`
	BindDay              uint32                 `protobuf:"varint,8,opt,name=bind_day,json=bindDay,proto3" json:"bind_day"`
	PresentUrl           string                 `protobuf:"bytes,9,opt,name=present_url,json=presentUrl,proto3" json:"present_url"`
	UnboundTime          uint32                 `protobuf:"varint,10,opt,name=unbound_time,json=unboundTime,proto3" json:"unbound_time"`
	PresentName          string                 `protobuf:"bytes,11,opt,name=present_name,json=presentName,proto3" json:"present_name"`
	CloseChangeFellow    uint32                 `protobuf:"varint,12,opt,name=close_change_fellow,json=closeChangeFellow,proto3" json:"close_change_fellow"`
	AccompanyValue       int64                  `protobuf:"varint,13,opt,name=accompany_value,json=accompanyValue,proto3" json:"accompany_value"`
	AccompanyValueDesc   string                 `protobuf:"bytes,14,opt,name=accompany_value_desc,json=accompanyValueDesc,proto3" json:"accompany_value_desc"`
	RankInfoList         []*OnAccompanyRankInfo `protobuf:"bytes,15,rep,name=rank_info_list,json=rankInfoList,proto3" json:"rank_info_list"`
	GradingInfo          *GradingInfo           `protobuf:"bytes,16,opt,name=grading_info,json=gradingInfo,proto3" json:"grading_info"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *GetFellowInfoResp) Reset()         { *m = GetFellowInfoResp{} }
func (m *GetFellowInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetFellowInfoResp) ProtoMessage()    {}
func (*GetFellowInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_revenue_http_logic_907ee343f90f615b, []int{51}
}
func (m *GetFellowInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetFellowInfoResp.Unmarshal(m, b)
}
func (m *GetFellowInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetFellowInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetFellowInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetFellowInfoResp.Merge(dst, src)
}
func (m *GetFellowInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetFellowInfoResp.Size(m)
}
func (m *GetFellowInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetFellowInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetFellowInfoResp proto.InternalMessageInfo

func (m *GetFellowInfoResp) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetFellowInfoResp) GetFellowUid() uint32 {
	if m != nil {
		return m.FellowUid
	}
	return 0
}

func (m *GetFellowInfoResp) GetFellowPoint() uint32 {
	if m != nil {
		return m.FellowPoint
	}
	return 0
}

func (m *GetFellowInfoResp) GetBindType() uint32 {
	if m != nil {
		return m.BindType
	}
	return 0
}

func (m *GetFellowInfoResp) GetFellowType() uint32 {
	if m != nil {
		return m.FellowType
	}
	return 0
}

func (m *GetFellowInfoResp) GetTask() []*FellowTask {
	if m != nil {
		return m.Task
	}
	return nil
}

func (m *GetFellowInfoResp) GetLevelConfig() []*FellowLevelConfig {
	if m != nil {
		return m.LevelConfig
	}
	return nil
}

func (m *GetFellowInfoResp) GetBindDay() uint32 {
	if m != nil {
		return m.BindDay
	}
	return 0
}

func (m *GetFellowInfoResp) GetPresentUrl() string {
	if m != nil {
		return m.PresentUrl
	}
	return ""
}

func (m *GetFellowInfoResp) GetUnboundTime() uint32 {
	if m != nil {
		return m.UnboundTime
	}
	return 0
}

func (m *GetFellowInfoResp) GetPresentName() string {
	if m != nil {
		return m.PresentName
	}
	return ""
}

func (m *GetFellowInfoResp) GetCloseChangeFellow() uint32 {
	if m != nil {
		return m.CloseChangeFellow
	}
	return 0
}

func (m *GetFellowInfoResp) GetAccompanyValue() int64 {
	if m != nil {
		return m.AccompanyValue
	}
	return 0
}

func (m *GetFellowInfoResp) GetAccompanyValueDesc() string {
	if m != nil {
		return m.AccompanyValueDesc
	}
	return ""
}

func (m *GetFellowInfoResp) GetRankInfoList() []*OnAccompanyRankInfo {
	if m != nil {
		return m.RankInfoList
	}
	return nil
}

func (m *GetFellowInfoResp) GetGradingInfo() *GradingInfo {
	if m != nil {
		return m.GradingInfo
	}
	return nil
}

type DatingGameRecord struct {
	SceneId              uint32   `protobuf:"varint,1,opt,name=scene_id,json=sceneId,proto3" json:"scene_id"`
	SceneName            string   `protobuf:"bytes,2,opt,name=scene_name,json=sceneName,proto3" json:"scene_name"`
	Nameplate            string   `protobuf:"bytes,3,opt,name=nameplate,proto3" json:"nameplate"`
	Background           string   `protobuf:"bytes,4,opt,name=background,proto3" json:"background"`
	SceneCount           uint32   `protobuf:"varint,5,opt,name=scene_count,json=sceneCount,proto3" json:"scene_count"`
	IsWear               bool     `protobuf:"varint,6,opt,name=is_wear,json=isWear,proto3" json:"is_wear"`
	Sort                 uint32   `protobuf:"varint,7,opt,name=sort,proto3" json:"sort"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DatingGameRecord) Reset()         { *m = DatingGameRecord{} }
func (m *DatingGameRecord) String() string { return proto.CompactTextString(m) }
func (*DatingGameRecord) ProtoMessage()    {}
func (*DatingGameRecord) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_revenue_http_logic_907ee343f90f615b, []int{52}
}
func (m *DatingGameRecord) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DatingGameRecord.Unmarshal(m, b)
}
func (m *DatingGameRecord) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DatingGameRecord.Marshal(b, m, deterministic)
}
func (dst *DatingGameRecord) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DatingGameRecord.Merge(dst, src)
}
func (m *DatingGameRecord) XXX_Size() int {
	return xxx_messageInfo_DatingGameRecord.Size(m)
}
func (m *DatingGameRecord) XXX_DiscardUnknown() {
	xxx_messageInfo_DatingGameRecord.DiscardUnknown(m)
}

var xxx_messageInfo_DatingGameRecord proto.InternalMessageInfo

func (m *DatingGameRecord) GetSceneId() uint32 {
	if m != nil {
		return m.SceneId
	}
	return 0
}

func (m *DatingGameRecord) GetSceneName() string {
	if m != nil {
		return m.SceneName
	}
	return ""
}

func (m *DatingGameRecord) GetNameplate() string {
	if m != nil {
		return m.Nameplate
	}
	return ""
}

func (m *DatingGameRecord) GetBackground() string {
	if m != nil {
		return m.Background
	}
	return ""
}

func (m *DatingGameRecord) GetSceneCount() uint32 {
	if m != nil {
		return m.SceneCount
	}
	return 0
}

func (m *DatingGameRecord) GetIsWear() bool {
	if m != nil {
		return m.IsWear
	}
	return false
}

func (m *DatingGameRecord) GetSort() uint32 {
	if m != nil {
		return m.Sort
	}
	return 0
}

//
type PiecesSummaryData struct {
	HandInHandCount       uint32              `protobuf:"varint,1,opt,name=hand_in_hand_count,json=handInHandCount,proto3" json:"hand_in_hand_count"`
	HandInHandScenes      string              `protobuf:"bytes,2,opt,name=hand_in_hand_scenes,json=handInHandScenes,proto3" json:"hand_in_hand_scenes"`
	CpCount               uint32              `protobuf:"varint,3,opt,name=cp_count,json=cpCount,proto3" json:"cp_count"`
	CpStrength            uint32              `protobuf:"varint,4,opt,name=cp_strength,json=cpStrength,proto3" json:"cp_strength"`
	CpPlateUrl            string              `protobuf:"bytes,5,opt,name=cp_plate_url,json=cpPlateUrl,proto3" json:"cp_plate_url"`
	CpBackground          string              `protobuf:"bytes,6,opt,name=cp_background,json=cpBackground,proto3" json:"cp_background"`
	DateBackground        string              `protobuf:"bytes,7,opt,name=date_background,json=dateBackground,proto3" json:"date_background"`
	RareRelationshipNames []string            `protobuf:"bytes,8,rep,name=rare_relationship_names,json=rareRelationshipNames,proto3" json:"rare_relationship_names"`
	RareRelationshipBg    string              `protobuf:"bytes,9,opt,name=rare_relationship_bg,json=rareRelationshipBg,proto3" json:"rare_relationship_bg"`
	DatingRecord          []*DatingGameRecord `protobuf:"bytes,10,rep,name=dating_record,json=datingRecord,proto3" json:"dating_record"`
	WeddingCertificate    *WeddingCertificate `protobuf:"bytes,11,opt,name=wedding_certificate,json=weddingCertificate,proto3" json:"wedding_certificate"`
	XXX_NoUnkeyedLiteral  struct{}            `json:"-"`
	XXX_unrecognized      []byte              `json:"-"`
	XXX_sizecache         int32               `json:"-"`
}

func (m *PiecesSummaryData) Reset()         { *m = PiecesSummaryData{} }
func (m *PiecesSummaryData) String() string { return proto.CompactTextString(m) }
func (*PiecesSummaryData) ProtoMessage()    {}
func (*PiecesSummaryData) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_revenue_http_logic_907ee343f90f615b, []int{53}
}
func (m *PiecesSummaryData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PiecesSummaryData.Unmarshal(m, b)
}
func (m *PiecesSummaryData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PiecesSummaryData.Marshal(b, m, deterministic)
}
func (dst *PiecesSummaryData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PiecesSummaryData.Merge(dst, src)
}
func (m *PiecesSummaryData) XXX_Size() int {
	return xxx_messageInfo_PiecesSummaryData.Size(m)
}
func (m *PiecesSummaryData) XXX_DiscardUnknown() {
	xxx_messageInfo_PiecesSummaryData.DiscardUnknown(m)
}

var xxx_messageInfo_PiecesSummaryData proto.InternalMessageInfo

func (m *PiecesSummaryData) GetHandInHandCount() uint32 {
	if m != nil {
		return m.HandInHandCount
	}
	return 0
}

func (m *PiecesSummaryData) GetHandInHandScenes() string {
	if m != nil {
		return m.HandInHandScenes
	}
	return ""
}

func (m *PiecesSummaryData) GetCpCount() uint32 {
	if m != nil {
		return m.CpCount
	}
	return 0
}

func (m *PiecesSummaryData) GetCpStrength() uint32 {
	if m != nil {
		return m.CpStrength
	}
	return 0
}

func (m *PiecesSummaryData) GetCpPlateUrl() string {
	if m != nil {
		return m.CpPlateUrl
	}
	return ""
}

func (m *PiecesSummaryData) GetCpBackground() string {
	if m != nil {
		return m.CpBackground
	}
	return ""
}

func (m *PiecesSummaryData) GetDateBackground() string {
	if m != nil {
		return m.DateBackground
	}
	return ""
}

func (m *PiecesSummaryData) GetRareRelationshipNames() []string {
	if m != nil {
		return m.RareRelationshipNames
	}
	return nil
}

func (m *PiecesSummaryData) GetRareRelationshipBg() string {
	if m != nil {
		return m.RareRelationshipBg
	}
	return ""
}

func (m *PiecesSummaryData) GetDatingRecord() []*DatingGameRecord {
	if m != nil {
		return m.DatingRecord
	}
	return nil
}

func (m *PiecesSummaryData) GetWeddingCertificate() *WeddingCertificate {
	if m != nil {
		return m.WeddingCertificate
	}
	return nil
}

type WeddingCertificate struct {
	Groom                *UserProfile `protobuf:"bytes,1,opt,name=groom,proto3" json:"groom"`
	Bride                *UserProfile `protobuf:"bytes,2,opt,name=bride,proto3" json:"bride"`
	WeddingTime          int64        `protobuf:"varint,3,opt,name=wedding_time,json=weddingTime,proto3" json:"wedding_time"`
	PicUrl               string       `protobuf:"bytes,4,opt,name=pic_url,json=picUrl,proto3" json:"pic_url"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *WeddingCertificate) Reset()         { *m = WeddingCertificate{} }
func (m *WeddingCertificate) String() string { return proto.CompactTextString(m) }
func (*WeddingCertificate) ProtoMessage()    {}
func (*WeddingCertificate) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_revenue_http_logic_907ee343f90f615b, []int{54}
}
func (m *WeddingCertificate) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WeddingCertificate.Unmarshal(m, b)
}
func (m *WeddingCertificate) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WeddingCertificate.Marshal(b, m, deterministic)
}
func (dst *WeddingCertificate) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WeddingCertificate.Merge(dst, src)
}
func (m *WeddingCertificate) XXX_Size() int {
	return xxx_messageInfo_WeddingCertificate.Size(m)
}
func (m *WeddingCertificate) XXX_DiscardUnknown() {
	xxx_messageInfo_WeddingCertificate.DiscardUnknown(m)
}

var xxx_messageInfo_WeddingCertificate proto.InternalMessageInfo

func (m *WeddingCertificate) GetGroom() *UserProfile {
	if m != nil {
		return m.Groom
	}
	return nil
}

func (m *WeddingCertificate) GetBride() *UserProfile {
	if m != nil {
		return m.Bride
	}
	return nil
}

func (m *WeddingCertificate) GetWeddingTime() int64 {
	if m != nil {
		return m.WeddingTime
	}
	return 0
}

func (m *WeddingCertificate) GetPicUrl() string {
	if m != nil {
		return m.PicUrl
	}
	return ""
}

type PiecesData struct {
	Date                 string   `protobuf:"bytes,1,opt,name=date,proto3" json:"date"`
	Desc                 string   `protobuf:"bytes,2,opt,name=desc,proto3" json:"desc"`
	Background           string   `protobuf:"bytes,3,opt,name=background,proto3" json:"background"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PiecesData) Reset()         { *m = PiecesData{} }
func (m *PiecesData) String() string { return proto.CompactTextString(m) }
func (*PiecesData) ProtoMessage()    {}
func (*PiecesData) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_revenue_http_logic_907ee343f90f615b, []int{55}
}
func (m *PiecesData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PiecesData.Unmarshal(m, b)
}
func (m *PiecesData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PiecesData.Marshal(b, m, deterministic)
}
func (dst *PiecesData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PiecesData.Merge(dst, src)
}
func (m *PiecesData) XXX_Size() int {
	return xxx_messageInfo_PiecesData.Size(m)
}
func (m *PiecesData) XXX_DiscardUnknown() {
	xxx_messageInfo_PiecesData.DiscardUnknown(m)
}

var xxx_messageInfo_PiecesData proto.InternalMessageInfo

func (m *PiecesData) GetDate() string {
	if m != nil {
		return m.Date
	}
	return ""
}

func (m *PiecesData) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

func (m *PiecesData) GetBackground() string {
	if m != nil {
		return m.Background
	}
	return ""
}

// 关系空间 点滴数据
type GetPiecesInfoReq struct {
	MyUid                uint32   `protobuf:"varint,1,opt,name=my_uid,json=myUid,proto3" json:"my_uid"`
	FellowUid            uint32   `protobuf:"varint,2,opt,name=fellow_uid,json=fellowUid,proto3" json:"fellow_uid"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPiecesInfoReq) Reset()         { *m = GetPiecesInfoReq{} }
func (m *GetPiecesInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetPiecesInfoReq) ProtoMessage()    {}
func (*GetPiecesInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_revenue_http_logic_907ee343f90f615b, []int{56}
}
func (m *GetPiecesInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPiecesInfoReq.Unmarshal(m, b)
}
func (m *GetPiecesInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPiecesInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetPiecesInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPiecesInfoReq.Merge(dst, src)
}
func (m *GetPiecesInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetPiecesInfoReq.Size(m)
}
func (m *GetPiecesInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPiecesInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPiecesInfoReq proto.InternalMessageInfo

func (m *GetPiecesInfoReq) GetMyUid() uint32 {
	if m != nil {
		return m.MyUid
	}
	return 0
}

func (m *GetPiecesInfoReq) GetFellowUid() uint32 {
	if m != nil {
		return m.FellowUid
	}
	return 0
}

type GetPiecesInfoResp struct {
	MyUid                        uint32             `protobuf:"varint,1,opt,name=my_uid,json=myUid,proto3" json:"my_uid"`
	FellowUid                    uint32             `protobuf:"varint,2,opt,name=fellow_uid,json=fellowUid,proto3" json:"fellow_uid"`
	Summary                      *PiecesSummaryData `protobuf:"bytes,3,opt,name=summary,proto3" json:"summary"`
	Pieces                       []*PiecesData      `protobuf:"bytes,4,rep,name=pieces,proto3" json:"pieces"`
	RareRelationshipTags         []string           `protobuf:"bytes,5,rep,name=rare_relationship_tags,json=rareRelationshipTags,proto3" json:"rare_relationship_tags"`
	Is_Wedding                   bool               `protobuf:"varint,6,opt,name=is_Wedding,json=isWedding,proto3" json:"is_Wedding"`
	WeddingElement               string             `protobuf:"bytes,7,opt,name=wedding_element,json=weddingElement,proto3" json:"wedding_element"`
	WeddingBackground            string             `protobuf:"bytes,8,opt,name=wedding_background,json=weddingBackground,proto3" json:"wedding_background"`
	WeddingColor                 string             `protobuf:"bytes,9,opt,name=wedding_color,json=weddingColor,proto3" json:"wedding_color"`
	FellowHouseWeddingBackground string             `protobuf:"bytes,10,opt,name=fellow_house_wedding_background,json=fellowHouseWeddingBackground,proto3" json:"fellow_house_wedding_background"`
	XXX_NoUnkeyedLiteral         struct{}           `json:"-"`
	XXX_unrecognized             []byte             `json:"-"`
	XXX_sizecache                int32              `json:"-"`
}

func (m *GetPiecesInfoResp) Reset()         { *m = GetPiecesInfoResp{} }
func (m *GetPiecesInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetPiecesInfoResp) ProtoMessage()    {}
func (*GetPiecesInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_revenue_http_logic_907ee343f90f615b, []int{57}
}
func (m *GetPiecesInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPiecesInfoResp.Unmarshal(m, b)
}
func (m *GetPiecesInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPiecesInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetPiecesInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPiecesInfoResp.Merge(dst, src)
}
func (m *GetPiecesInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetPiecesInfoResp.Size(m)
}
func (m *GetPiecesInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPiecesInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPiecesInfoResp proto.InternalMessageInfo

func (m *GetPiecesInfoResp) GetMyUid() uint32 {
	if m != nil {
		return m.MyUid
	}
	return 0
}

func (m *GetPiecesInfoResp) GetFellowUid() uint32 {
	if m != nil {
		return m.FellowUid
	}
	return 0
}

func (m *GetPiecesInfoResp) GetSummary() *PiecesSummaryData {
	if m != nil {
		return m.Summary
	}
	return nil
}

func (m *GetPiecesInfoResp) GetPieces() []*PiecesData {
	if m != nil {
		return m.Pieces
	}
	return nil
}

func (m *GetPiecesInfoResp) GetRareRelationshipTags() []string {
	if m != nil {
		return m.RareRelationshipTags
	}
	return nil
}

func (m *GetPiecesInfoResp) GetIs_Wedding() bool {
	if m != nil {
		return m.Is_Wedding
	}
	return false
}

func (m *GetPiecesInfoResp) GetWeddingElement() string {
	if m != nil {
		return m.WeddingElement
	}
	return ""
}

func (m *GetPiecesInfoResp) GetWeddingBackground() string {
	if m != nil {
		return m.WeddingBackground
	}
	return ""
}

func (m *GetPiecesInfoResp) GetWeddingColor() string {
	if m != nil {
		return m.WeddingColor
	}
	return ""
}

func (m *GetPiecesInfoResp) GetFellowHouseWeddingBackground() string {
	if m != nil {
		return m.FellowHouseWeddingBackground
	}
	return ""
}

// CP战 战力值历史记录
type GetCPStrengthHistoryReq struct {
	MyUid                uint32   `protobuf:"varint,1,opt,name=my_uid,json=myUid,proto3" json:"my_uid"`
	FellowUid            uint32   `protobuf:"varint,2,opt,name=fellow_uid,json=fellowUid,proto3" json:"fellow_uid"`
	Offset               uint32   `protobuf:"varint,3,opt,name=offset,proto3" json:"offset"`
	Limit                uint32   `protobuf:"varint,4,opt,name=limit,proto3" json:"limit"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetCPStrengthHistoryReq) Reset()         { *m = GetCPStrengthHistoryReq{} }
func (m *GetCPStrengthHistoryReq) String() string { return proto.CompactTextString(m) }
func (*GetCPStrengthHistoryReq) ProtoMessage()    {}
func (*GetCPStrengthHistoryReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_revenue_http_logic_907ee343f90f615b, []int{58}
}
func (m *GetCPStrengthHistoryReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCPStrengthHistoryReq.Unmarshal(m, b)
}
func (m *GetCPStrengthHistoryReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCPStrengthHistoryReq.Marshal(b, m, deterministic)
}
func (dst *GetCPStrengthHistoryReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCPStrengthHistoryReq.Merge(dst, src)
}
func (m *GetCPStrengthHistoryReq) XXX_Size() int {
	return xxx_messageInfo_GetCPStrengthHistoryReq.Size(m)
}
func (m *GetCPStrengthHistoryReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCPStrengthHistoryReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetCPStrengthHistoryReq proto.InternalMessageInfo

func (m *GetCPStrengthHistoryReq) GetMyUid() uint32 {
	if m != nil {
		return m.MyUid
	}
	return 0
}

func (m *GetCPStrengthHistoryReq) GetFellowUid() uint32 {
	if m != nil {
		return m.FellowUid
	}
	return 0
}

func (m *GetCPStrengthHistoryReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetCPStrengthHistoryReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type StrengthHistory struct {
	Date                 string   `protobuf:"bytes,1,opt,name=date,proto3" json:"date"`
	Strength             int32    `protobuf:"varint,2,opt,name=strength,proto3" json:"strength"`
	Reason               string   `protobuf:"bytes,3,opt,name=reason,proto3" json:"reason"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StrengthHistory) Reset()         { *m = StrengthHistory{} }
func (m *StrengthHistory) String() string { return proto.CompactTextString(m) }
func (*StrengthHistory) ProtoMessage()    {}
func (*StrengthHistory) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_revenue_http_logic_907ee343f90f615b, []int{59}
}
func (m *StrengthHistory) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StrengthHistory.Unmarshal(m, b)
}
func (m *StrengthHistory) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StrengthHistory.Marshal(b, m, deterministic)
}
func (dst *StrengthHistory) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StrengthHistory.Merge(dst, src)
}
func (m *StrengthHistory) XXX_Size() int {
	return xxx_messageInfo_StrengthHistory.Size(m)
}
func (m *StrengthHistory) XXX_DiscardUnknown() {
	xxx_messageInfo_StrengthHistory.DiscardUnknown(m)
}

var xxx_messageInfo_StrengthHistory proto.InternalMessageInfo

func (m *StrengthHistory) GetDate() string {
	if m != nil {
		return m.Date
	}
	return ""
}

func (m *StrengthHistory) GetStrength() int32 {
	if m != nil {
		return m.Strength
	}
	return 0
}

func (m *StrengthHistory) GetReason() string {
	if m != nil {
		return m.Reason
	}
	return ""
}

type GetCPStrengthHistoryResp struct {
	MyUid                uint32             `protobuf:"varint,1,opt,name=my_uid,json=myUid,proto3" json:"my_uid"`
	FellowUid            uint32             `protobuf:"varint,2,opt,name=fellow_uid,json=fellowUid,proto3" json:"fellow_uid"`
	Offset               uint32             `protobuf:"varint,3,opt,name=offset,proto3" json:"offset"`
	Limit                uint32             `protobuf:"varint,4,opt,name=limit,proto3" json:"limit"`
	History              []*StrengthHistory `protobuf:"bytes,5,rep,name=history,proto3" json:"history"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *GetCPStrengthHistoryResp) Reset()         { *m = GetCPStrengthHistoryResp{} }
func (m *GetCPStrengthHistoryResp) String() string { return proto.CompactTextString(m) }
func (*GetCPStrengthHistoryResp) ProtoMessage()    {}
func (*GetCPStrengthHistoryResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_revenue_http_logic_907ee343f90f615b, []int{60}
}
func (m *GetCPStrengthHistoryResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCPStrengthHistoryResp.Unmarshal(m, b)
}
func (m *GetCPStrengthHistoryResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCPStrengthHistoryResp.Marshal(b, m, deterministic)
}
func (dst *GetCPStrengthHistoryResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCPStrengthHistoryResp.Merge(dst, src)
}
func (m *GetCPStrengthHistoryResp) XXX_Size() int {
	return xxx_messageInfo_GetCPStrengthHistoryResp.Size(m)
}
func (m *GetCPStrengthHistoryResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCPStrengthHistoryResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetCPStrengthHistoryResp proto.InternalMessageInfo

func (m *GetCPStrengthHistoryResp) GetMyUid() uint32 {
	if m != nil {
		return m.MyUid
	}
	return 0
}

func (m *GetCPStrengthHistoryResp) GetFellowUid() uint32 {
	if m != nil {
		return m.FellowUid
	}
	return 0
}

func (m *GetCPStrengthHistoryResp) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetCPStrengthHistoryResp) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *GetCPStrengthHistoryResp) GetHistory() []*StrengthHistory {
	if m != nil {
		return m.History
	}
	return nil
}

type UnboundFellowReq struct {
	OpUid                uint32   `protobuf:"varint,1,opt,name=op_uid,json=opUid,proto3" json:"op_uid"`
	TargetUid            uint32   `protobuf:"varint,2,opt,name=target_uid,json=targetUid,proto3" json:"target_uid"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UnboundFellowReq) Reset()         { *m = UnboundFellowReq{} }
func (m *UnboundFellowReq) String() string { return proto.CompactTextString(m) }
func (*UnboundFellowReq) ProtoMessage()    {}
func (*UnboundFellowReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_revenue_http_logic_907ee343f90f615b, []int{61}
}
func (m *UnboundFellowReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UnboundFellowReq.Unmarshal(m, b)
}
func (m *UnboundFellowReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UnboundFellowReq.Marshal(b, m, deterministic)
}
func (dst *UnboundFellowReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UnboundFellowReq.Merge(dst, src)
}
func (m *UnboundFellowReq) XXX_Size() int {
	return xxx_messageInfo_UnboundFellowReq.Size(m)
}
func (m *UnboundFellowReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UnboundFellowReq.DiscardUnknown(m)
}

var xxx_messageInfo_UnboundFellowReq proto.InternalMessageInfo

func (m *UnboundFellowReq) GetOpUid() uint32 {
	if m != nil {
		return m.OpUid
	}
	return 0
}

func (m *UnboundFellowReq) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

type UnboundFellowResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UnboundFellowResp) Reset()         { *m = UnboundFellowResp{} }
func (m *UnboundFellowResp) String() string { return proto.CompactTextString(m) }
func (*UnboundFellowResp) ProtoMessage()    {}
func (*UnboundFellowResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_revenue_http_logic_907ee343f90f615b, []int{62}
}
func (m *UnboundFellowResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UnboundFellowResp.Unmarshal(m, b)
}
func (m *UnboundFellowResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UnboundFellowResp.Marshal(b, m, deterministic)
}
func (dst *UnboundFellowResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UnboundFellowResp.Merge(dst, src)
}
func (m *UnboundFellowResp) XXX_Size() int {
	return xxx_messageInfo_UnboundFellowResp.Size(m)
}
func (m *UnboundFellowResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UnboundFellowResp.DiscardUnknown(m)
}

var xxx_messageInfo_UnboundFellowResp proto.InternalMessageInfo

type CancelUnboundFellowReq struct {
	OpUid                uint32   `protobuf:"varint,1,opt,name=op_uid,json=opUid,proto3" json:"op_uid"`
	TargetUid            uint32   `protobuf:"varint,2,opt,name=target_uid,json=targetUid,proto3" json:"target_uid"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CancelUnboundFellowReq) Reset()         { *m = CancelUnboundFellowReq{} }
func (m *CancelUnboundFellowReq) String() string { return proto.CompactTextString(m) }
func (*CancelUnboundFellowReq) ProtoMessage()    {}
func (*CancelUnboundFellowReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_revenue_http_logic_907ee343f90f615b, []int{63}
}
func (m *CancelUnboundFellowReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CancelUnboundFellowReq.Unmarshal(m, b)
}
func (m *CancelUnboundFellowReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CancelUnboundFellowReq.Marshal(b, m, deterministic)
}
func (dst *CancelUnboundFellowReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CancelUnboundFellowReq.Merge(dst, src)
}
func (m *CancelUnboundFellowReq) XXX_Size() int {
	return xxx_messageInfo_CancelUnboundFellowReq.Size(m)
}
func (m *CancelUnboundFellowReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CancelUnboundFellowReq.DiscardUnknown(m)
}

var xxx_messageInfo_CancelUnboundFellowReq proto.InternalMessageInfo

func (m *CancelUnboundFellowReq) GetOpUid() uint32 {
	if m != nil {
		return m.OpUid
	}
	return 0
}

func (m *CancelUnboundFellowReq) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

type CancelUnboundFellowResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CancelUnboundFellowResp) Reset()         { *m = CancelUnboundFellowResp{} }
func (m *CancelUnboundFellowResp) String() string { return proto.CompactTextString(m) }
func (*CancelUnboundFellowResp) ProtoMessage()    {}
func (*CancelUnboundFellowResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_revenue_http_logic_907ee343f90f615b, []int{64}
}
func (m *CancelUnboundFellowResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CancelUnboundFellowResp.Unmarshal(m, b)
}
func (m *CancelUnboundFellowResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CancelUnboundFellowResp.Marshal(b, m, deterministic)
}
func (dst *CancelUnboundFellowResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CancelUnboundFellowResp.Merge(dst, src)
}
func (m *CancelUnboundFellowResp) XXX_Size() int {
	return xxx_messageInfo_CancelUnboundFellowResp.Size(m)
}
func (m *CancelUnboundFellowResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CancelUnboundFellowResp.DiscardUnknown(m)
}

var xxx_messageInfo_CancelUnboundFellowResp proto.InternalMessageInfo

type DirectUnboundFellowReq struct {
	OpUid                uint32   `protobuf:"varint,1,opt,name=op_uid,json=opUid,proto3" json:"op_uid"`
	TargetUid            uint32   `protobuf:"varint,2,opt,name=target_uid,json=targetUid,proto3" json:"target_uid"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DirectUnboundFellowReq) Reset()         { *m = DirectUnboundFellowReq{} }
func (m *DirectUnboundFellowReq) String() string { return proto.CompactTextString(m) }
func (*DirectUnboundFellowReq) ProtoMessage()    {}
func (*DirectUnboundFellowReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_revenue_http_logic_907ee343f90f615b, []int{65}
}
func (m *DirectUnboundFellowReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DirectUnboundFellowReq.Unmarshal(m, b)
}
func (m *DirectUnboundFellowReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DirectUnboundFellowReq.Marshal(b, m, deterministic)
}
func (dst *DirectUnboundFellowReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DirectUnboundFellowReq.Merge(dst, src)
}
func (m *DirectUnboundFellowReq) XXX_Size() int {
	return xxx_messageInfo_DirectUnboundFellowReq.Size(m)
}
func (m *DirectUnboundFellowReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DirectUnboundFellowReq.DiscardUnknown(m)
}

var xxx_messageInfo_DirectUnboundFellowReq proto.InternalMessageInfo

func (m *DirectUnboundFellowReq) GetOpUid() uint32 {
	if m != nil {
		return m.OpUid
	}
	return 0
}

func (m *DirectUnboundFellowReq) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

type DirectUnboundFellowResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DirectUnboundFellowResp) Reset()         { *m = DirectUnboundFellowResp{} }
func (m *DirectUnboundFellowResp) String() string { return proto.CompactTextString(m) }
func (*DirectUnboundFellowResp) ProtoMessage()    {}
func (*DirectUnboundFellowResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_revenue_http_logic_907ee343f90f615b, []int{66}
}
func (m *DirectUnboundFellowResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DirectUnboundFellowResp.Unmarshal(m, b)
}
func (m *DirectUnboundFellowResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DirectUnboundFellowResp.Marshal(b, m, deterministic)
}
func (dst *DirectUnboundFellowResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DirectUnboundFellowResp.Merge(dst, src)
}
func (m *DirectUnboundFellowResp) XXX_Size() int {
	return xxx_messageInfo_DirectUnboundFellowResp.Size(m)
}
func (m *DirectUnboundFellowResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DirectUnboundFellowResp.DiscardUnknown(m)
}

var xxx_messageInfo_DirectUnboundFellowResp proto.InternalMessageInfo

type SetNameplateInUseReq struct {
	OpUid                uint32   `protobuf:"varint,1,opt,name=op_uid,json=opUid,proto3" json:"op_uid"`
	TargetUid            uint32   `protobuf:"varint,2,opt,name=target_uid,json=targetUid,proto3" json:"target_uid"`
	SceneId              uint32   `protobuf:"varint,3,opt,name=scene_id,json=sceneId,proto3" json:"scene_id"`
	SceneName            string   `protobuf:"bytes,4,opt,name=scene_name,json=sceneName,proto3" json:"scene_name"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetNameplateInUseReq) Reset()         { *m = SetNameplateInUseReq{} }
func (m *SetNameplateInUseReq) String() string { return proto.CompactTextString(m) }
func (*SetNameplateInUseReq) ProtoMessage()    {}
func (*SetNameplateInUseReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_revenue_http_logic_907ee343f90f615b, []int{67}
}
func (m *SetNameplateInUseReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetNameplateInUseReq.Unmarshal(m, b)
}
func (m *SetNameplateInUseReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetNameplateInUseReq.Marshal(b, m, deterministic)
}
func (dst *SetNameplateInUseReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetNameplateInUseReq.Merge(dst, src)
}
func (m *SetNameplateInUseReq) XXX_Size() int {
	return xxx_messageInfo_SetNameplateInUseReq.Size(m)
}
func (m *SetNameplateInUseReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetNameplateInUseReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetNameplateInUseReq proto.InternalMessageInfo

func (m *SetNameplateInUseReq) GetOpUid() uint32 {
	if m != nil {
		return m.OpUid
	}
	return 0
}

func (m *SetNameplateInUseReq) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

func (m *SetNameplateInUseReq) GetSceneId() uint32 {
	if m != nil {
		return m.SceneId
	}
	return 0
}

func (m *SetNameplateInUseReq) GetSceneName() string {
	if m != nil {
		return m.SceneName
	}
	return ""
}

type SetNameplateInUseResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetNameplateInUseResp) Reset()         { *m = SetNameplateInUseResp{} }
func (m *SetNameplateInUseResp) String() string { return proto.CompactTextString(m) }
func (*SetNameplateInUseResp) ProtoMessage()    {}
func (*SetNameplateInUseResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_revenue_http_logic_907ee343f90f615b, []int{68}
}
func (m *SetNameplateInUseResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetNameplateInUseResp.Unmarshal(m, b)
}
func (m *SetNameplateInUseResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetNameplateInUseResp.Marshal(b, m, deterministic)
}
func (dst *SetNameplateInUseResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetNameplateInUseResp.Merge(dst, src)
}
func (m *SetNameplateInUseResp) XXX_Size() int {
	return xxx_messageInfo_SetNameplateInUseResp.Size(m)
}
func (m *SetNameplateInUseResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetNameplateInUseResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetNameplateInUseResp proto.InternalMessageInfo

type ChangeFellowBindTypeReq struct {
	OpUid                uint32   `protobuf:"varint,1,opt,name=op_uid,json=opUid,proto3" json:"op_uid"`
	TargetUid            uint32   `protobuf:"varint,2,opt,name=target_uid,json=targetUid,proto3" json:"target_uid"`
	FromBindType         uint32   `protobuf:"varint,3,opt,name=from_bind_type,json=fromBindType,proto3" json:"from_bind_type"`
	FromFellowType       uint32   `protobuf:"varint,4,opt,name=from_fellow_type,json=fromFellowType,proto3" json:"from_fellow_type"`
	ToBindType           uint32   `protobuf:"varint,5,opt,name=to_bind_type,json=toBindType,proto3" json:"to_bind_type"`
	ToFellowType         uint32   `protobuf:"varint,6,opt,name=to_fellow_type,json=toFellowType,proto3" json:"to_fellow_type"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChangeFellowBindTypeReq) Reset()         { *m = ChangeFellowBindTypeReq{} }
func (m *ChangeFellowBindTypeReq) String() string { return proto.CompactTextString(m) }
func (*ChangeFellowBindTypeReq) ProtoMessage()    {}
func (*ChangeFellowBindTypeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_revenue_http_logic_907ee343f90f615b, []int{69}
}
func (m *ChangeFellowBindTypeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChangeFellowBindTypeReq.Unmarshal(m, b)
}
func (m *ChangeFellowBindTypeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChangeFellowBindTypeReq.Marshal(b, m, deterministic)
}
func (dst *ChangeFellowBindTypeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChangeFellowBindTypeReq.Merge(dst, src)
}
func (m *ChangeFellowBindTypeReq) XXX_Size() int {
	return xxx_messageInfo_ChangeFellowBindTypeReq.Size(m)
}
func (m *ChangeFellowBindTypeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ChangeFellowBindTypeReq.DiscardUnknown(m)
}

var xxx_messageInfo_ChangeFellowBindTypeReq proto.InternalMessageInfo

func (m *ChangeFellowBindTypeReq) GetOpUid() uint32 {
	if m != nil {
		return m.OpUid
	}
	return 0
}

func (m *ChangeFellowBindTypeReq) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

func (m *ChangeFellowBindTypeReq) GetFromBindType() uint32 {
	if m != nil {
		return m.FromBindType
	}
	return 0
}

func (m *ChangeFellowBindTypeReq) GetFromFellowType() uint32 {
	if m != nil {
		return m.FromFellowType
	}
	return 0
}

func (m *ChangeFellowBindTypeReq) GetToBindType() uint32 {
	if m != nil {
		return m.ToBindType
	}
	return 0
}

func (m *ChangeFellowBindTypeReq) GetToFellowType() uint32 {
	if m != nil {
		return m.ToFellowType
	}
	return 0
}

type ChangeFellowBindTypeResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChangeFellowBindTypeResp) Reset()         { *m = ChangeFellowBindTypeResp{} }
func (m *ChangeFellowBindTypeResp) String() string { return proto.CompactTextString(m) }
func (*ChangeFellowBindTypeResp) ProtoMessage()    {}
func (*ChangeFellowBindTypeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_revenue_http_logic_907ee343f90f615b, []int{70}
}
func (m *ChangeFellowBindTypeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChangeFellowBindTypeResp.Unmarshal(m, b)
}
func (m *ChangeFellowBindTypeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChangeFellowBindTypeResp.Marshal(b, m, deterministic)
}
func (dst *ChangeFellowBindTypeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChangeFellowBindTypeResp.Merge(dst, src)
}
func (m *ChangeFellowBindTypeResp) XXX_Size() int {
	return xxx_messageInfo_ChangeFellowBindTypeResp.Size(m)
}
func (m *ChangeFellowBindTypeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ChangeFellowBindTypeResp.DiscardUnknown(m)
}

var xxx_messageInfo_ChangeFellowBindTypeResp proto.InternalMessageInfo

type GetHuntMonsterPropsInfoReq struct {
	Uid                  string   `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetHuntMonsterPropsInfoReq) Reset()         { *m = GetHuntMonsterPropsInfoReq{} }
func (m *GetHuntMonsterPropsInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetHuntMonsterPropsInfoReq) ProtoMessage()    {}
func (*GetHuntMonsterPropsInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_revenue_http_logic_907ee343f90f615b, []int{71}
}
func (m *GetHuntMonsterPropsInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetHuntMonsterPropsInfoReq.Unmarshal(m, b)
}
func (m *GetHuntMonsterPropsInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetHuntMonsterPropsInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetHuntMonsterPropsInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetHuntMonsterPropsInfoReq.Merge(dst, src)
}
func (m *GetHuntMonsterPropsInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetHuntMonsterPropsInfoReq.Size(m)
}
func (m *GetHuntMonsterPropsInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetHuntMonsterPropsInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetHuntMonsterPropsInfoReq proto.InternalMessageInfo

func (m *GetHuntMonsterPropsInfoReq) GetUid() string {
	if m != nil {
		return m.Uid
	}
	return ""
}

func (m *GetHuntMonsterPropsInfoReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetHuntMonsterPropsInfoResp struct {
	Username                  string   `protobuf:"bytes,1,opt,name=username,proto3" json:"username"`
	Nickname                  string   `protobuf:"bytes,2,opt,name=nickname,proto3" json:"nickname"`
	PropsCnt                  uint32   `protobuf:"varint,3,opt,name=props_cnt,json=propsCnt,proto3" json:"props_cnt"`
	StayChannelOneCnt         uint32   `protobuf:"varint,4,opt,name=stay_channel_one_cnt,json=stayChannelOneCnt,proto3" json:"stay_channel_one_cnt"`
	StayChannelFiveCnt        uint32   `protobuf:"varint,5,opt,name=stay_channel_five_cnt,json=stayChannelFiveCnt,proto3" json:"stay_channel_five_cnt"`
	EnterChannelHourRankCnt   uint32   `protobuf:"varint,6,opt,name=enter_channel_hour_rank_cnt,json=enterChannelHourRankCnt,proto3" json:"enter_channel_hour_rank_cnt"`
	SendPresentCnt            uint32   `protobuf:"varint,7,opt,name=send_present_cnt,json=sendPresentCnt,proto3" json:"send_present_cnt"`
	SendOneThousandPresentCnt uint32   `protobuf:"varint,8,opt,name=send_one_thousand_present_cnt,json=sendOneThousandPresentCnt,proto3" json:"send_one_thousand_present_cnt"`
	FirstSendTbeanPresentCnt  uint32   `protobuf:"varint,9,opt,name=first_send_tbean_present_cnt,json=firstSendTbeanPresentCnt,proto3" json:"first_send_tbean_present_cnt"`
	AddFansGroupCnt           uint32   `protobuf:"varint,10,opt,name=add_fans_group_cnt,json=addFansGroupCnt,proto3" json:"add_fans_group_cnt"`
	IsRecommendCh             bool     `protobuf:"varint,11,opt,name=is_recommend_ch,json=isRecommendCh,proto3" json:"is_recommend_ch"`
	XXX_NoUnkeyedLiteral      struct{} `json:"-"`
	XXX_unrecognized          []byte   `json:"-"`
	XXX_sizecache             int32    `json:"-"`
}

func (m *GetHuntMonsterPropsInfoResp) Reset()         { *m = GetHuntMonsterPropsInfoResp{} }
func (m *GetHuntMonsterPropsInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetHuntMonsterPropsInfoResp) ProtoMessage()    {}
func (*GetHuntMonsterPropsInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_revenue_http_logic_907ee343f90f615b, []int{72}
}
func (m *GetHuntMonsterPropsInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetHuntMonsterPropsInfoResp.Unmarshal(m, b)
}
func (m *GetHuntMonsterPropsInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetHuntMonsterPropsInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetHuntMonsterPropsInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetHuntMonsterPropsInfoResp.Merge(dst, src)
}
func (m *GetHuntMonsterPropsInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetHuntMonsterPropsInfoResp.Size(m)
}
func (m *GetHuntMonsterPropsInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetHuntMonsterPropsInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetHuntMonsterPropsInfoResp proto.InternalMessageInfo

func (m *GetHuntMonsterPropsInfoResp) GetUsername() string {
	if m != nil {
		return m.Username
	}
	return ""
}

func (m *GetHuntMonsterPropsInfoResp) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *GetHuntMonsterPropsInfoResp) GetPropsCnt() uint32 {
	if m != nil {
		return m.PropsCnt
	}
	return 0
}

func (m *GetHuntMonsterPropsInfoResp) GetStayChannelOneCnt() uint32 {
	if m != nil {
		return m.StayChannelOneCnt
	}
	return 0
}

func (m *GetHuntMonsterPropsInfoResp) GetStayChannelFiveCnt() uint32 {
	if m != nil {
		return m.StayChannelFiveCnt
	}
	return 0
}

func (m *GetHuntMonsterPropsInfoResp) GetEnterChannelHourRankCnt() uint32 {
	if m != nil {
		return m.EnterChannelHourRankCnt
	}
	return 0
}

func (m *GetHuntMonsterPropsInfoResp) GetSendPresentCnt() uint32 {
	if m != nil {
		return m.SendPresentCnt
	}
	return 0
}

func (m *GetHuntMonsterPropsInfoResp) GetSendOneThousandPresentCnt() uint32 {
	if m != nil {
		return m.SendOneThousandPresentCnt
	}
	return 0
}

func (m *GetHuntMonsterPropsInfoResp) GetFirstSendTbeanPresentCnt() uint32 {
	if m != nil {
		return m.FirstSendTbeanPresentCnt
	}
	return 0
}

func (m *GetHuntMonsterPropsInfoResp) GetAddFansGroupCnt() uint32 {
	if m != nil {
		return m.AddFansGroupCnt
	}
	return 0
}

func (m *GetHuntMonsterPropsInfoResp) GetIsRecommendCh() bool {
	if m != nil {
		return m.IsRecommendCh
	}
	return false
}

// 获取蒙面pk积分 resp
type MaskedPkScore struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid"`
	Score                uint32   `protobuf:"varint,2,opt,name=score,proto3" json:"score"`
	HasWithdraw          bool     `protobuf:"varint,3,opt,name=has_withdraw,json=hasWithdraw,proto3" json:"has_withdraw"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MaskedPkScore) Reset()         { *m = MaskedPkScore{} }
func (m *MaskedPkScore) String() string { return proto.CompactTextString(m) }
func (*MaskedPkScore) ProtoMessage()    {}
func (*MaskedPkScore) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_revenue_http_logic_907ee343f90f615b, []int{73}
}
func (m *MaskedPkScore) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MaskedPkScore.Unmarshal(m, b)
}
func (m *MaskedPkScore) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MaskedPkScore.Marshal(b, m, deterministic)
}
func (dst *MaskedPkScore) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MaskedPkScore.Merge(dst, src)
}
func (m *MaskedPkScore) XXX_Size() int {
	return xxx_messageInfo_MaskedPkScore.Size(m)
}
func (m *MaskedPkScore) XXX_DiscardUnknown() {
	xxx_messageInfo_MaskedPkScore.DiscardUnknown(m)
}

var xxx_messageInfo_MaskedPkScore proto.InternalMessageInfo

func (m *MaskedPkScore) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *MaskedPkScore) GetScore() uint32 {
	if m != nil {
		return m.Score
	}
	return 0
}

func (m *MaskedPkScore) GetHasWithdraw() bool {
	if m != nil {
		return m.HasWithdraw
	}
	return false
}

// 语音直播主播奖励积分流水
type MaskedPkScoreLog struct {
	OrderId              string   `protobuf:"bytes,1,opt,name=order_id,json=orderId,proto3" json:"order_id"`
	Amount               int32    `protobuf:"varint,2,opt,name=amount,proto3" json:"amount"`
	CreateAt             uint32   `protobuf:"varint,3,opt,name=create_at,json=createAt,proto3" json:"create_at"`
	Desc                 string   `protobuf:"bytes,4,opt,name=desc,proto3" json:"desc"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MaskedPkScoreLog) Reset()         { *m = MaskedPkScoreLog{} }
func (m *MaskedPkScoreLog) String() string { return proto.CompactTextString(m) }
func (*MaskedPkScoreLog) ProtoMessage()    {}
func (*MaskedPkScoreLog) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_revenue_http_logic_907ee343f90f615b, []int{74}
}
func (m *MaskedPkScoreLog) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MaskedPkScoreLog.Unmarshal(m, b)
}
func (m *MaskedPkScoreLog) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MaskedPkScoreLog.Marshal(b, m, deterministic)
}
func (dst *MaskedPkScoreLog) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MaskedPkScoreLog.Merge(dst, src)
}
func (m *MaskedPkScoreLog) XXX_Size() int {
	return xxx_messageInfo_MaskedPkScoreLog.Size(m)
}
func (m *MaskedPkScoreLog) XXX_DiscardUnknown() {
	xxx_messageInfo_MaskedPkScoreLog.DiscardUnknown(m)
}

var xxx_messageInfo_MaskedPkScoreLog proto.InternalMessageInfo

func (m *MaskedPkScoreLog) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *MaskedPkScoreLog) GetAmount() int32 {
	if m != nil {
		return m.Amount
	}
	return 0
}

func (m *MaskedPkScoreLog) GetCreateAt() uint32 {
	if m != nil {
		return m.CreateAt
	}
	return 0
}

func (m *MaskedPkScoreLog) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

type GetMaskedPkScoreLogReq struct {
	Page                 uint32   `protobuf:"varint,1,opt,name=page,proto3" json:"page"`
	PageSize             uint32   `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetMaskedPkScoreLogReq) Reset()         { *m = GetMaskedPkScoreLogReq{} }
func (m *GetMaskedPkScoreLogReq) String() string { return proto.CompactTextString(m) }
func (*GetMaskedPkScoreLogReq) ProtoMessage()    {}
func (*GetMaskedPkScoreLogReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_revenue_http_logic_907ee343f90f615b, []int{75}
}
func (m *GetMaskedPkScoreLogReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMaskedPkScoreLogReq.Unmarshal(m, b)
}
func (m *GetMaskedPkScoreLogReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMaskedPkScoreLogReq.Marshal(b, m, deterministic)
}
func (dst *GetMaskedPkScoreLogReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMaskedPkScoreLogReq.Merge(dst, src)
}
func (m *GetMaskedPkScoreLogReq) XXX_Size() int {
	return xxx_messageInfo_GetMaskedPkScoreLogReq.Size(m)
}
func (m *GetMaskedPkScoreLogReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMaskedPkScoreLogReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetMaskedPkScoreLogReq proto.InternalMessageInfo

func (m *GetMaskedPkScoreLogReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *GetMaskedPkScoreLogReq) GetPageSize() uint32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

type GetMaskedPkScoreLogResp struct {
	List                 []*MaskedPkScoreLog `protobuf:"bytes,1,rep,name=list,proto3" json:"list"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *GetMaskedPkScoreLogResp) Reset()         { *m = GetMaskedPkScoreLogResp{} }
func (m *GetMaskedPkScoreLogResp) String() string { return proto.CompactTextString(m) }
func (*GetMaskedPkScoreLogResp) ProtoMessage()    {}
func (*GetMaskedPkScoreLogResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_revenue_http_logic_907ee343f90f615b, []int{76}
}
func (m *GetMaskedPkScoreLogResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMaskedPkScoreLogResp.Unmarshal(m, b)
}
func (m *GetMaskedPkScoreLogResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMaskedPkScoreLogResp.Marshal(b, m, deterministic)
}
func (dst *GetMaskedPkScoreLogResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMaskedPkScoreLogResp.Merge(dst, src)
}
func (m *GetMaskedPkScoreLogResp) XXX_Size() int {
	return xxx_messageInfo_GetMaskedPkScoreLogResp.Size(m)
}
func (m *GetMaskedPkScoreLogResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMaskedPkScoreLogResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetMaskedPkScoreLogResp proto.InternalMessageInfo

func (m *GetMaskedPkScoreLogResp) GetList() []*MaskedPkScoreLog {
	if m != nil {
		return m.List
	}
	return nil
}

// 仅维护跟svr侧不同的协议, 避免增减字段同步修改
// 获取我得记录
type GetMyLotteryRecordReq struct {
	AwardType            uint32   `protobuf:"varint,1,opt,name=award_type,json=awardType,proto3" json:"award_type"`
	Offset               uint32   `protobuf:"varint,2,opt,name=offset,proto3" json:"offset"`
	Limit                uint32   `protobuf:"varint,3,opt,name=limit,proto3" json:"limit"`
	Uid                  string   `protobuf:"bytes,4,opt,name=uid,proto3" json:"uid"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetMyLotteryRecordReq) Reset()         { *m = GetMyLotteryRecordReq{} }
func (m *GetMyLotteryRecordReq) String() string { return proto.CompactTextString(m) }
func (*GetMyLotteryRecordReq) ProtoMessage()    {}
func (*GetMyLotteryRecordReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_revenue_http_logic_907ee343f90f615b, []int{77}
}
func (m *GetMyLotteryRecordReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMyLotteryRecordReq.Unmarshal(m, b)
}
func (m *GetMyLotteryRecordReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMyLotteryRecordReq.Marshal(b, m, deterministic)
}
func (dst *GetMyLotteryRecordReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMyLotteryRecordReq.Merge(dst, src)
}
func (m *GetMyLotteryRecordReq) XXX_Size() int {
	return xxx_messageInfo_GetMyLotteryRecordReq.Size(m)
}
func (m *GetMyLotteryRecordReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMyLotteryRecordReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetMyLotteryRecordReq proto.InternalMessageInfo

func (m *GetMyLotteryRecordReq) GetAwardType() uint32 {
	if m != nil {
		return m.AwardType
	}
	return 0
}

func (m *GetMyLotteryRecordReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetMyLotteryRecordReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *GetMyLotteryRecordReq) GetUid() string {
	if m != nil {
		return m.Uid
	}
	return ""
}

// TT周边商品信息
type PeripheralProduct struct {
	ProductId            uint32   `protobuf:"varint,1,opt,name=product_id,json=productId,proto3" json:"product_id"`
	Title                string   `protobuf:"bytes,2,opt,name=title,proto3" json:"title"`
	HomepageUrl          string   `protobuf:"bytes,3,opt,name=homepage_url,json=homepageUrl,proto3" json:"homepage_url"`
	DetailDesc           string   `protobuf:"bytes,4,opt,name=detail_desc,json=detailDesc,proto3" json:"detail_desc"`
	Price                uint32   `protobuf:"varint,5,opt,name=price,proto3" json:"price"`
	Stock                uint32   `protobuf:"varint,6,opt,name=stock,proto3" json:"stock"`
	LimitPerOrder        uint32   `protobuf:"varint,7,opt,name=limit_per_order,json=limitPerOrder,proto3" json:"limit_per_order"`
	PicUrls              []string `protobuf:"bytes,8,rep,name=pic_urls,json=picUrls,proto3" json:"pic_urls"`
	OriginPrice          uint32   `protobuf:"varint,9,opt,name=origin_price,json=originPrice,proto3" json:"origin_price"`
	OrderedCnt           uint32   `protobuf:"varint,10,opt,name=ordered_cnt,json=orderedCnt,proto3" json:"ordered_cnt"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PeripheralProduct) Reset()         { *m = PeripheralProduct{} }
func (m *PeripheralProduct) String() string { return proto.CompactTextString(m) }
func (*PeripheralProduct) ProtoMessage()    {}
func (*PeripheralProduct) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_revenue_http_logic_907ee343f90f615b, []int{78}
}
func (m *PeripheralProduct) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PeripheralProduct.Unmarshal(m, b)
}
func (m *PeripheralProduct) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PeripheralProduct.Marshal(b, m, deterministic)
}
func (dst *PeripheralProduct) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PeripheralProduct.Merge(dst, src)
}
func (m *PeripheralProduct) XXX_Size() int {
	return xxx_messageInfo_PeripheralProduct.Size(m)
}
func (m *PeripheralProduct) XXX_DiscardUnknown() {
	xxx_messageInfo_PeripheralProduct.DiscardUnknown(m)
}

var xxx_messageInfo_PeripheralProduct proto.InternalMessageInfo

func (m *PeripheralProduct) GetProductId() uint32 {
	if m != nil {
		return m.ProductId
	}
	return 0
}

func (m *PeripheralProduct) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *PeripheralProduct) GetHomepageUrl() string {
	if m != nil {
		return m.HomepageUrl
	}
	return ""
}

func (m *PeripheralProduct) GetDetailDesc() string {
	if m != nil {
		return m.DetailDesc
	}
	return ""
}

func (m *PeripheralProduct) GetPrice() uint32 {
	if m != nil {
		return m.Price
	}
	return 0
}

func (m *PeripheralProduct) GetStock() uint32 {
	if m != nil {
		return m.Stock
	}
	return 0
}

func (m *PeripheralProduct) GetLimitPerOrder() uint32 {
	if m != nil {
		return m.LimitPerOrder
	}
	return 0
}

func (m *PeripheralProduct) GetPicUrls() []string {
	if m != nil {
		return m.PicUrls
	}
	return nil
}

func (m *PeripheralProduct) GetOriginPrice() uint32 {
	if m != nil {
		return m.OriginPrice
	}
	return 0
}

func (m *PeripheralProduct) GetOrderedCnt() uint32 {
	if m != nil {
		return m.OrderedCnt
	}
	return 0
}

// 商品简要信息
type PeripheralBriefInfo struct {
	ProductId            uint32   `protobuf:"varint,1,opt,name=product_id,json=productId,proto3" json:"product_id"`
	Title                string   `protobuf:"bytes,2,opt,name=title,proto3" json:"title"`
	HomepageUrl          string   `protobuf:"bytes,3,opt,name=homepage_url,json=homepageUrl,proto3" json:"homepage_url"`
	Price                uint32   `protobuf:"varint,4,opt,name=price,proto3" json:"price"`
	IsStockOut           bool     `protobuf:"varint,5,opt,name=is_stock_out,json=isStockOut,proto3" json:"is_stock_out"`
	OriginPrice          uint32   `protobuf:"varint,6,opt,name=origin_price,json=originPrice,proto3" json:"origin_price"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PeripheralBriefInfo) Reset()         { *m = PeripheralBriefInfo{} }
func (m *PeripheralBriefInfo) String() string { return proto.CompactTextString(m) }
func (*PeripheralBriefInfo) ProtoMessage()    {}
func (*PeripheralBriefInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_revenue_http_logic_907ee343f90f615b, []int{79}
}
func (m *PeripheralBriefInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PeripheralBriefInfo.Unmarshal(m, b)
}
func (m *PeripheralBriefInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PeripheralBriefInfo.Marshal(b, m, deterministic)
}
func (dst *PeripheralBriefInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PeripheralBriefInfo.Merge(dst, src)
}
func (m *PeripheralBriefInfo) XXX_Size() int {
	return xxx_messageInfo_PeripheralBriefInfo.Size(m)
}
func (m *PeripheralBriefInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_PeripheralBriefInfo.DiscardUnknown(m)
}

var xxx_messageInfo_PeripheralBriefInfo proto.InternalMessageInfo

func (m *PeripheralBriefInfo) GetProductId() uint32 {
	if m != nil {
		return m.ProductId
	}
	return 0
}

func (m *PeripheralBriefInfo) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *PeripheralBriefInfo) GetHomepageUrl() string {
	if m != nil {
		return m.HomepageUrl
	}
	return ""
}

func (m *PeripheralBriefInfo) GetPrice() uint32 {
	if m != nil {
		return m.Price
	}
	return 0
}

func (m *PeripheralBriefInfo) GetIsStockOut() bool {
	if m != nil {
		return m.IsStockOut
	}
	return false
}

func (m *PeripheralBriefInfo) GetOriginPrice() uint32 {
	if m != nil {
		return m.OriginPrice
	}
	return 0
}

// 全量获取首页简略商品信息列表 url:/tt_peripheral/product_list
type GetPeripheralBriefInfoReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPeripheralBriefInfoReq) Reset()         { *m = GetPeripheralBriefInfoReq{} }
func (m *GetPeripheralBriefInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetPeripheralBriefInfoReq) ProtoMessage()    {}
func (*GetPeripheralBriefInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_revenue_http_logic_907ee343f90f615b, []int{80}
}
func (m *GetPeripheralBriefInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPeripheralBriefInfoReq.Unmarshal(m, b)
}
func (m *GetPeripheralBriefInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPeripheralBriefInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetPeripheralBriefInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPeripheralBriefInfoReq.Merge(dst, src)
}
func (m *GetPeripheralBriefInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetPeripheralBriefInfoReq.Size(m)
}
func (m *GetPeripheralBriefInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPeripheralBriefInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPeripheralBriefInfoReq proto.InternalMessageInfo

type GetPeripheralBriefInfoResp struct {
	ItemList             []*PeripheralBriefInfo `protobuf:"bytes,1,rep,name=item_list,json=itemList,proto3" json:"item_list"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *GetPeripheralBriefInfoResp) Reset()         { *m = GetPeripheralBriefInfoResp{} }
func (m *GetPeripheralBriefInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetPeripheralBriefInfoResp) ProtoMessage()    {}
func (*GetPeripheralBriefInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_revenue_http_logic_907ee343f90f615b, []int{81}
}
func (m *GetPeripheralBriefInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPeripheralBriefInfoResp.Unmarshal(m, b)
}
func (m *GetPeripheralBriefInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPeripheralBriefInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetPeripheralBriefInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPeripheralBriefInfoResp.Merge(dst, src)
}
func (m *GetPeripheralBriefInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetPeripheralBriefInfoResp.Size(m)
}
func (m *GetPeripheralBriefInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPeripheralBriefInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPeripheralBriefInfoResp proto.InternalMessageInfo

func (m *GetPeripheralBriefInfoResp) GetItemList() []*PeripheralBriefInfo {
	if m != nil {
		return m.ItemList
	}
	return nil
}

// 获取商品详情  url：/tt_peripheral/product_detail
type GetPeripheralProductDetailReq struct {
	ProductId            uint32   `protobuf:"varint,1,opt,name=product_id,json=productId,proto3" json:"product_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPeripheralProductDetailReq) Reset()         { *m = GetPeripheralProductDetailReq{} }
func (m *GetPeripheralProductDetailReq) String() string { return proto.CompactTextString(m) }
func (*GetPeripheralProductDetailReq) ProtoMessage()    {}
func (*GetPeripheralProductDetailReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_revenue_http_logic_907ee343f90f615b, []int{82}
}
func (m *GetPeripheralProductDetailReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPeripheralProductDetailReq.Unmarshal(m, b)
}
func (m *GetPeripheralProductDetailReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPeripheralProductDetailReq.Marshal(b, m, deterministic)
}
func (dst *GetPeripheralProductDetailReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPeripheralProductDetailReq.Merge(dst, src)
}
func (m *GetPeripheralProductDetailReq) XXX_Size() int {
	return xxx_messageInfo_GetPeripheralProductDetailReq.Size(m)
}
func (m *GetPeripheralProductDetailReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPeripheralProductDetailReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPeripheralProductDetailReq proto.InternalMessageInfo

func (m *GetPeripheralProductDetailReq) GetProductId() uint32 {
	if m != nil {
		return m.ProductId
	}
	return 0
}

type GetPeripheralProductDetailResp struct {
	ItemInfo             *PeripheralProduct `protobuf:"bytes,1,opt,name=item_info,json=itemInfo,proto3" json:"item_info"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *GetPeripheralProductDetailResp) Reset()         { *m = GetPeripheralProductDetailResp{} }
func (m *GetPeripheralProductDetailResp) String() string { return proto.CompactTextString(m) }
func (*GetPeripheralProductDetailResp) ProtoMessage()    {}
func (*GetPeripheralProductDetailResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_revenue_http_logic_907ee343f90f615b, []int{83}
}
func (m *GetPeripheralProductDetailResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPeripheralProductDetailResp.Unmarshal(m, b)
}
func (m *GetPeripheralProductDetailResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPeripheralProductDetailResp.Marshal(b, m, deterministic)
}
func (dst *GetPeripheralProductDetailResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPeripheralProductDetailResp.Merge(dst, src)
}
func (m *GetPeripheralProductDetailResp) XXX_Size() int {
	return xxx_messageInfo_GetPeripheralProductDetailResp.Size(m)
}
func (m *GetPeripheralProductDetailResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPeripheralProductDetailResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPeripheralProductDetailResp proto.InternalMessageInfo

func (m *GetPeripheralProductDetailResp) GetItemInfo() *PeripheralProduct {
	if m != nil {
		return m.ItemInfo
	}
	return nil
}

type SinglePeripheralProductOrder struct {
	ProductId            uint32   `protobuf:"varint,1,opt,name=product_id,json=productId,proto3" json:"product_id"`
	Amount               uint32   `protobuf:"varint,2,opt,name=amount,proto3" json:"amount"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SinglePeripheralProductOrder) Reset()         { *m = SinglePeripheralProductOrder{} }
func (m *SinglePeripheralProductOrder) String() string { return proto.CompactTextString(m) }
func (*SinglePeripheralProductOrder) ProtoMessage()    {}
func (*SinglePeripheralProductOrder) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_revenue_http_logic_907ee343f90f615b, []int{84}
}
func (m *SinglePeripheralProductOrder) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SinglePeripheralProductOrder.Unmarshal(m, b)
}
func (m *SinglePeripheralProductOrder) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SinglePeripheralProductOrder.Marshal(b, m, deterministic)
}
func (dst *SinglePeripheralProductOrder) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SinglePeripheralProductOrder.Merge(dst, src)
}
func (m *SinglePeripheralProductOrder) XXX_Size() int {
	return xxx_messageInfo_SinglePeripheralProductOrder.Size(m)
}
func (m *SinglePeripheralProductOrder) XXX_DiscardUnknown() {
	xxx_messageInfo_SinglePeripheralProductOrder.DiscardUnknown(m)
}

var xxx_messageInfo_SinglePeripheralProductOrder proto.InternalMessageInfo

func (m *SinglePeripheralProductOrder) GetProductId() uint32 {
	if m != nil {
		return m.ProductId
	}
	return 0
}

func (m *SinglePeripheralProductOrder) GetAmount() uint32 {
	if m != nil {
		return m.Amount
	}
	return 0
}

type PeripheralPayReq struct {
	PayChannel           string   `protobuf:"bytes,1,opt,name=pay_channel,json=payChannel,proto3" json:"pay_channel"`
	BundleId             string   `protobuf:"bytes,2,opt,name=bundle_id,json=bundleId,proto3" json:"bundle_id"`
	ProductId            string   `protobuf:"bytes,3,opt,name=product_id,json=productId,proto3" json:"product_id"`
	DeviceId             string   `protobuf:"bytes,4,opt,name=device_id,json=deviceId,proto3" json:"device_id"`
	ClientType           uint32   `protobuf:"varint,5,opt,name=client_type,json=clientType,proto3" json:"client_type"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PeripheralPayReq) Reset()         { *m = PeripheralPayReq{} }
func (m *PeripheralPayReq) String() string { return proto.CompactTextString(m) }
func (*PeripheralPayReq) ProtoMessage()    {}
func (*PeripheralPayReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_revenue_http_logic_907ee343f90f615b, []int{85}
}
func (m *PeripheralPayReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PeripheralPayReq.Unmarshal(m, b)
}
func (m *PeripheralPayReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PeripheralPayReq.Marshal(b, m, deterministic)
}
func (dst *PeripheralPayReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PeripheralPayReq.Merge(dst, src)
}
func (m *PeripheralPayReq) XXX_Size() int {
	return xxx_messageInfo_PeripheralPayReq.Size(m)
}
func (m *PeripheralPayReq) XXX_DiscardUnknown() {
	xxx_messageInfo_PeripheralPayReq.DiscardUnknown(m)
}

var xxx_messageInfo_PeripheralPayReq proto.InternalMessageInfo

func (m *PeripheralPayReq) GetPayChannel() string {
	if m != nil {
		return m.PayChannel
	}
	return ""
}

func (m *PeripheralPayReq) GetBundleId() string {
	if m != nil {
		return m.BundleId
	}
	return ""
}

func (m *PeripheralPayReq) GetProductId() string {
	if m != nil {
		return m.ProductId
	}
	return ""
}

func (m *PeripheralPayReq) GetDeviceId() string {
	if m != nil {
		return m.DeviceId
	}
	return ""
}

func (m *PeripheralPayReq) GetClientType() uint32 {
	if m != nil {
		return m.ClientType
	}
	return 0
}

type PeripheralPayResp struct {
	Token                string   `protobuf:"bytes,1,opt,name=token,proto3" json:"token"`
	PayOrderId           string   `protobuf:"bytes,2,opt,name=pay_order_id,json=payOrderId,proto3" json:"pay_order_id"`
	CliOrderTitle        string   `protobuf:"bytes,3,opt,name=cli_order_title,json=cliOrderTitle,proto3" json:"cli_order_title"`
	Tsk                  string   `protobuf:"bytes,4,opt,name=tsk,proto3" json:"tsk"`
	ChannelMap           string   `protobuf:"bytes,5,opt,name=channel_map,json=channelMap,proto3" json:"channel_map"`
	OrderTime            uint32   `protobuf:"varint,6,opt,name=order_time,json=orderTime,proto3" json:"order_time"`
	TPayOrderNo          string   `protobuf:"bytes,7,opt,name=t_pay_order_no,json=tPayOrderNo,proto3" json:"t_pay_order_no"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PeripheralPayResp) Reset()         { *m = PeripheralPayResp{} }
func (m *PeripheralPayResp) String() string { return proto.CompactTextString(m) }
func (*PeripheralPayResp) ProtoMessage()    {}
func (*PeripheralPayResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_revenue_http_logic_907ee343f90f615b, []int{86}
}
func (m *PeripheralPayResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PeripheralPayResp.Unmarshal(m, b)
}
func (m *PeripheralPayResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PeripheralPayResp.Marshal(b, m, deterministic)
}
func (dst *PeripheralPayResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PeripheralPayResp.Merge(dst, src)
}
func (m *PeripheralPayResp) XXX_Size() int {
	return xxx_messageInfo_PeripheralPayResp.Size(m)
}
func (m *PeripheralPayResp) XXX_DiscardUnknown() {
	xxx_messageInfo_PeripheralPayResp.DiscardUnknown(m)
}

var xxx_messageInfo_PeripheralPayResp proto.InternalMessageInfo

func (m *PeripheralPayResp) GetToken() string {
	if m != nil {
		return m.Token
	}
	return ""
}

func (m *PeripheralPayResp) GetPayOrderId() string {
	if m != nil {
		return m.PayOrderId
	}
	return ""
}

func (m *PeripheralPayResp) GetCliOrderTitle() string {
	if m != nil {
		return m.CliOrderTitle
	}
	return ""
}

func (m *PeripheralPayResp) GetTsk() string {
	if m != nil {
		return m.Tsk
	}
	return ""
}

func (m *PeripheralPayResp) GetChannelMap() string {
	if m != nil {
		return m.ChannelMap
	}
	return ""
}

func (m *PeripheralPayResp) GetOrderTime() uint32 {
	if m != nil {
		return m.OrderTime
	}
	return 0
}

func (m *PeripheralPayResp) GetTPayOrderNo() string {
	if m != nil {
		return m.TPayOrderNo
	}
	return ""
}

// 支付人脸检查相关
type PeripheralPayFaceCheckReq struct {
	FaceAuthToken              string   `protobuf:"bytes,1,opt,name=FaceAuthToken,proto3" json:"FaceAuthToken"`
	FaceAuthProviderCode       string   `protobuf:"bytes,2,opt,name=face_auth_provider_code,json=faceAuthProviderCode,proto3" json:"face_auth_provider_code"`
	FaceAuthProviderResultData string   `protobuf:"bytes,3,opt,name=face_auth_provider_result_data,json=faceAuthProviderResultData,proto3" json:"face_auth_provider_result_data"`
	FaceAuthResultToken        string   `protobuf:"bytes,4,opt,name=face_auth_result_token,json=faceAuthResultToken,proto3" json:"face_auth_result_token"`
	RequestId                  string   `protobuf:"bytes,5,opt,name=request_id,json=requestId,proto3" json:"request_id"`
	XXX_NoUnkeyedLiteral       struct{} `json:"-"`
	XXX_unrecognized           []byte   `json:"-"`
	XXX_sizecache              int32    `json:"-"`
}

func (m *PeripheralPayFaceCheckReq) Reset()         { *m = PeripheralPayFaceCheckReq{} }
func (m *PeripheralPayFaceCheckReq) String() string { return proto.CompactTextString(m) }
func (*PeripheralPayFaceCheckReq) ProtoMessage()    {}
func (*PeripheralPayFaceCheckReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_revenue_http_logic_907ee343f90f615b, []int{87}
}
func (m *PeripheralPayFaceCheckReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PeripheralPayFaceCheckReq.Unmarshal(m, b)
}
func (m *PeripheralPayFaceCheckReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PeripheralPayFaceCheckReq.Marshal(b, m, deterministic)
}
func (dst *PeripheralPayFaceCheckReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PeripheralPayFaceCheckReq.Merge(dst, src)
}
func (m *PeripheralPayFaceCheckReq) XXX_Size() int {
	return xxx_messageInfo_PeripheralPayFaceCheckReq.Size(m)
}
func (m *PeripheralPayFaceCheckReq) XXX_DiscardUnknown() {
	xxx_messageInfo_PeripheralPayFaceCheckReq.DiscardUnknown(m)
}

var xxx_messageInfo_PeripheralPayFaceCheckReq proto.InternalMessageInfo

func (m *PeripheralPayFaceCheckReq) GetFaceAuthToken() string {
	if m != nil {
		return m.FaceAuthToken
	}
	return ""
}

func (m *PeripheralPayFaceCheckReq) GetFaceAuthProviderCode() string {
	if m != nil {
		return m.FaceAuthProviderCode
	}
	return ""
}

func (m *PeripheralPayFaceCheckReq) GetFaceAuthProviderResultData() string {
	if m != nil {
		return m.FaceAuthProviderResultData
	}
	return ""
}

func (m *PeripheralPayFaceCheckReq) GetFaceAuthResultToken() string {
	if m != nil {
		return m.FaceAuthResultToken
	}
	return ""
}

func (m *PeripheralPayFaceCheckReq) GetRequestId() string {
	if m != nil {
		return m.RequestId
	}
	return ""
}

type PeripheralPayFaceCheckResp struct {
	RequestId            string   `protobuf:"bytes,1,opt,name=request_id,json=requestId,proto3" json:"request_id"`
	AuthScene            uint32   `protobuf:"varint,2,opt,name=auth_scene,json=authScene,proto3" json:"auth_scene"`
	FaceAuthContextJson  string   `protobuf:"bytes,3,opt,name=face_auth_context_json,json=faceAuthContextJson,proto3" json:"face_auth_context_json"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PeripheralPayFaceCheckResp) Reset()         { *m = PeripheralPayFaceCheckResp{} }
func (m *PeripheralPayFaceCheckResp) String() string { return proto.CompactTextString(m) }
func (*PeripheralPayFaceCheckResp) ProtoMessage()    {}
func (*PeripheralPayFaceCheckResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_revenue_http_logic_907ee343f90f615b, []int{88}
}
func (m *PeripheralPayFaceCheckResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PeripheralPayFaceCheckResp.Unmarshal(m, b)
}
func (m *PeripheralPayFaceCheckResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PeripheralPayFaceCheckResp.Marshal(b, m, deterministic)
}
func (dst *PeripheralPayFaceCheckResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PeripheralPayFaceCheckResp.Merge(dst, src)
}
func (m *PeripheralPayFaceCheckResp) XXX_Size() int {
	return xxx_messageInfo_PeripheralPayFaceCheckResp.Size(m)
}
func (m *PeripheralPayFaceCheckResp) XXX_DiscardUnknown() {
	xxx_messageInfo_PeripheralPayFaceCheckResp.DiscardUnknown(m)
}

var xxx_messageInfo_PeripheralPayFaceCheckResp proto.InternalMessageInfo

func (m *PeripheralPayFaceCheckResp) GetRequestId() string {
	if m != nil {
		return m.RequestId
	}
	return ""
}

func (m *PeripheralPayFaceCheckResp) GetAuthScene() uint32 {
	if m != nil {
		return m.AuthScene
	}
	return 0
}

func (m *PeripheralPayFaceCheckResp) GetFaceAuthContextJson() string {
	if m != nil {
		return m.FaceAuthContextJson
	}
	return ""
}

// 下单 url: /tt_peripheral/order
type OrderPeripheralProductReq struct {
	List                 []*SinglePeripheralProductOrder `protobuf:"bytes,1,rep,name=list,proto3" json:"list"`
	Addr                 *PeripheralOrderAddr            `protobuf:"bytes,2,opt,name=addr,proto3" json:"addr"`
	PayInfo              *PeripheralPayReq               `protobuf:"bytes,3,opt,name=pay_info,json=payInfo,proto3" json:"pay_info"`
	FaceInfo             *PeripheralPayFaceCheckReq      `protobuf:"bytes,4,opt,name=face_info,json=faceInfo,proto3" json:"face_info"`
	XXX_NoUnkeyedLiteral struct{}                        `json:"-"`
	XXX_unrecognized     []byte                          `json:"-"`
	XXX_sizecache        int32                           `json:"-"`
}

func (m *OrderPeripheralProductReq) Reset()         { *m = OrderPeripheralProductReq{} }
func (m *OrderPeripheralProductReq) String() string { return proto.CompactTextString(m) }
func (*OrderPeripheralProductReq) ProtoMessage()    {}
func (*OrderPeripheralProductReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_revenue_http_logic_907ee343f90f615b, []int{89}
}
func (m *OrderPeripheralProductReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OrderPeripheralProductReq.Unmarshal(m, b)
}
func (m *OrderPeripheralProductReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OrderPeripheralProductReq.Marshal(b, m, deterministic)
}
func (dst *OrderPeripheralProductReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OrderPeripheralProductReq.Merge(dst, src)
}
func (m *OrderPeripheralProductReq) XXX_Size() int {
	return xxx_messageInfo_OrderPeripheralProductReq.Size(m)
}
func (m *OrderPeripheralProductReq) XXX_DiscardUnknown() {
	xxx_messageInfo_OrderPeripheralProductReq.DiscardUnknown(m)
}

var xxx_messageInfo_OrderPeripheralProductReq proto.InternalMessageInfo

func (m *OrderPeripheralProductReq) GetList() []*SinglePeripheralProductOrder {
	if m != nil {
		return m.List
	}
	return nil
}

func (m *OrderPeripheralProductReq) GetAddr() *PeripheralOrderAddr {
	if m != nil {
		return m.Addr
	}
	return nil
}

func (m *OrderPeripheralProductReq) GetPayInfo() *PeripheralPayReq {
	if m != nil {
		return m.PayInfo
	}
	return nil
}

func (m *OrderPeripheralProductReq) GetFaceInfo() *PeripheralPayFaceCheckReq {
	if m != nil {
		return m.FaceInfo
	}
	return nil
}

type OrderPeripheralProductResp struct {
	PayInfo              *PeripheralPayResp          `protobuf:"bytes,1,opt,name=pay_info,json=payInfo,proto3" json:"pay_info"`
	FaceInfo             *PeripheralPayFaceCheckResp `protobuf:"bytes,2,opt,name=face_info,json=faceInfo,proto3" json:"face_info"`
	XXX_NoUnkeyedLiteral struct{}                    `json:"-"`
	XXX_unrecognized     []byte                      `json:"-"`
	XXX_sizecache        int32                       `json:"-"`
}

func (m *OrderPeripheralProductResp) Reset()         { *m = OrderPeripheralProductResp{} }
func (m *OrderPeripheralProductResp) String() string { return proto.CompactTextString(m) }
func (*OrderPeripheralProductResp) ProtoMessage()    {}
func (*OrderPeripheralProductResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_revenue_http_logic_907ee343f90f615b, []int{90}
}
func (m *OrderPeripheralProductResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OrderPeripheralProductResp.Unmarshal(m, b)
}
func (m *OrderPeripheralProductResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OrderPeripheralProductResp.Marshal(b, m, deterministic)
}
func (dst *OrderPeripheralProductResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OrderPeripheralProductResp.Merge(dst, src)
}
func (m *OrderPeripheralProductResp) XXX_Size() int {
	return xxx_messageInfo_OrderPeripheralProductResp.Size(m)
}
func (m *OrderPeripheralProductResp) XXX_DiscardUnknown() {
	xxx_messageInfo_OrderPeripheralProductResp.DiscardUnknown(m)
}

var xxx_messageInfo_OrderPeripheralProductResp proto.InternalMessageInfo

func (m *OrderPeripheralProductResp) GetPayInfo() *PeripheralPayResp {
	if m != nil {
		return m.PayInfo
	}
	return nil
}

func (m *OrderPeripheralProductResp) GetFaceInfo() *PeripheralPayFaceCheckResp {
	if m != nil {
		return m.FaceInfo
	}
	return nil
}

type PeripheralProductOrderInfo struct {
	OrderId              string               `protobuf:"bytes,1,opt,name=order_id,json=orderId,proto3" json:"order_id"`
	ProductList          []*PeripheralProduct `protobuf:"bytes,2,rep,name=product_list,json=productList,proto3" json:"product_list"`
	Addr                 *PeripheralOrderAddr `protobuf:"bytes,3,opt,name=addr,proto3" json:"addr"`
	TotalPrice           uint32               `protobuf:"varint,4,opt,name=total_price,json=totalPrice,proto3" json:"total_price"`
	Timestamp            int64                `protobuf:"varint,5,opt,name=timestamp,proto3" json:"timestamp"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *PeripheralProductOrderInfo) Reset()         { *m = PeripheralProductOrderInfo{} }
func (m *PeripheralProductOrderInfo) String() string { return proto.CompactTextString(m) }
func (*PeripheralProductOrderInfo) ProtoMessage()    {}
func (*PeripheralProductOrderInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_revenue_http_logic_907ee343f90f615b, []int{91}
}
func (m *PeripheralProductOrderInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PeripheralProductOrderInfo.Unmarshal(m, b)
}
func (m *PeripheralProductOrderInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PeripheralProductOrderInfo.Marshal(b, m, deterministic)
}
func (dst *PeripheralProductOrderInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PeripheralProductOrderInfo.Merge(dst, src)
}
func (m *PeripheralProductOrderInfo) XXX_Size() int {
	return xxx_messageInfo_PeripheralProductOrderInfo.Size(m)
}
func (m *PeripheralProductOrderInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_PeripheralProductOrderInfo.DiscardUnknown(m)
}

var xxx_messageInfo_PeripheralProductOrderInfo proto.InternalMessageInfo

func (m *PeripheralProductOrderInfo) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *PeripheralProductOrderInfo) GetProductList() []*PeripheralProduct {
	if m != nil {
		return m.ProductList
	}
	return nil
}

func (m *PeripheralProductOrderInfo) GetAddr() *PeripheralOrderAddr {
	if m != nil {
		return m.Addr
	}
	return nil
}

func (m *PeripheralProductOrderInfo) GetTotalPrice() uint32 {
	if m != nil {
		return m.TotalPrice
	}
	return 0
}

func (m *PeripheralProductOrderInfo) GetTimestamp() int64 {
	if m != nil {
		return m.Timestamp
	}
	return 0
}

// 获取用户历史订单 url：/tt_peripheral/get_history_orders
type GetPeripheralProductOrdersReq struct {
	Offset               uint32   `protobuf:"varint,1,opt,name=offset,proto3" json:"offset"`
	Limit                uint32   `protobuf:"varint,2,opt,name=limit,proto3" json:"limit"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPeripheralProductOrdersReq) Reset()         { *m = GetPeripheralProductOrdersReq{} }
func (m *GetPeripheralProductOrdersReq) String() string { return proto.CompactTextString(m) }
func (*GetPeripheralProductOrdersReq) ProtoMessage()    {}
func (*GetPeripheralProductOrdersReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_revenue_http_logic_907ee343f90f615b, []int{92}
}
func (m *GetPeripheralProductOrdersReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPeripheralProductOrdersReq.Unmarshal(m, b)
}
func (m *GetPeripheralProductOrdersReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPeripheralProductOrdersReq.Marshal(b, m, deterministic)
}
func (dst *GetPeripheralProductOrdersReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPeripheralProductOrdersReq.Merge(dst, src)
}
func (m *GetPeripheralProductOrdersReq) XXX_Size() int {
	return xxx_messageInfo_GetPeripheralProductOrdersReq.Size(m)
}
func (m *GetPeripheralProductOrdersReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPeripheralProductOrdersReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPeripheralProductOrdersReq proto.InternalMessageInfo

func (m *GetPeripheralProductOrdersReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetPeripheralProductOrdersReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type GetPeripheralProductOrdersResp struct {
	List                 []*PeripheralProductOrderInfo `protobuf:"bytes,1,rep,name=list,proto3" json:"list"`
	XXX_NoUnkeyedLiteral struct{}                      `json:"-"`
	XXX_unrecognized     []byte                        `json:"-"`
	XXX_sizecache        int32                         `json:"-"`
}

func (m *GetPeripheralProductOrdersResp) Reset()         { *m = GetPeripheralProductOrdersResp{} }
func (m *GetPeripheralProductOrdersResp) String() string { return proto.CompactTextString(m) }
func (*GetPeripheralProductOrdersResp) ProtoMessage()    {}
func (*GetPeripheralProductOrdersResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_revenue_http_logic_907ee343f90f615b, []int{93}
}
func (m *GetPeripheralProductOrdersResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPeripheralProductOrdersResp.Unmarshal(m, b)
}
func (m *GetPeripheralProductOrdersResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPeripheralProductOrdersResp.Marshal(b, m, deterministic)
}
func (dst *GetPeripheralProductOrdersResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPeripheralProductOrdersResp.Merge(dst, src)
}
func (m *GetPeripheralProductOrdersResp) XXX_Size() int {
	return xxx_messageInfo_GetPeripheralProductOrdersResp.Size(m)
}
func (m *GetPeripheralProductOrdersResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPeripheralProductOrdersResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPeripheralProductOrdersResp proto.InternalMessageInfo

func (m *GetPeripheralProductOrdersResp) GetList() []*PeripheralProductOrderInfo {
	if m != nil {
		return m.List
	}
	return nil
}

// 下单地址
type PeripheralOrderAddr struct {
	Name                 string   `protobuf:"bytes,1,opt,name=name,proto3" json:"name"`
	Phone                string   `protobuf:"bytes,2,opt,name=phone,proto3" json:"phone"`
	Address              string   `protobuf:"bytes,3,opt,name=address,proto3" json:"address"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PeripheralOrderAddr) Reset()         { *m = PeripheralOrderAddr{} }
func (m *PeripheralOrderAddr) String() string { return proto.CompactTextString(m) }
func (*PeripheralOrderAddr) ProtoMessage()    {}
func (*PeripheralOrderAddr) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_revenue_http_logic_907ee343f90f615b, []int{94}
}
func (m *PeripheralOrderAddr) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PeripheralOrderAddr.Unmarshal(m, b)
}
func (m *PeripheralOrderAddr) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PeripheralOrderAddr.Marshal(b, m, deterministic)
}
func (dst *PeripheralOrderAddr) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PeripheralOrderAddr.Merge(dst, src)
}
func (m *PeripheralOrderAddr) XXX_Size() int {
	return xxx_messageInfo_PeripheralOrderAddr.Size(m)
}
func (m *PeripheralOrderAddr) XXX_DiscardUnknown() {
	xxx_messageInfo_PeripheralOrderAddr.DiscardUnknown(m)
}

var xxx_messageInfo_PeripheralOrderAddr proto.InternalMessageInfo

func (m *PeripheralOrderAddr) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *PeripheralOrderAddr) GetPhone() string {
	if m != nil {
		return m.Phone
	}
	return ""
}

func (m *PeripheralOrderAddr) GetAddress() string {
	if m != nil {
		return m.Address
	}
	return ""
}

// 获取用户下单地址 url：/tt_peripheral/order_addr/get
type GetUserPeripheralOrderAddrReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserPeripheralOrderAddrReq) Reset()         { *m = GetUserPeripheralOrderAddrReq{} }
func (m *GetUserPeripheralOrderAddrReq) String() string { return proto.CompactTextString(m) }
func (*GetUserPeripheralOrderAddrReq) ProtoMessage()    {}
func (*GetUserPeripheralOrderAddrReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_revenue_http_logic_907ee343f90f615b, []int{95}
}
func (m *GetUserPeripheralOrderAddrReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserPeripheralOrderAddrReq.Unmarshal(m, b)
}
func (m *GetUserPeripheralOrderAddrReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserPeripheralOrderAddrReq.Marshal(b, m, deterministic)
}
func (dst *GetUserPeripheralOrderAddrReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserPeripheralOrderAddrReq.Merge(dst, src)
}
func (m *GetUserPeripheralOrderAddrReq) XXX_Size() int {
	return xxx_messageInfo_GetUserPeripheralOrderAddrReq.Size(m)
}
func (m *GetUserPeripheralOrderAddrReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserPeripheralOrderAddrReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserPeripheralOrderAddrReq proto.InternalMessageInfo

type GetUserPeripheralOrderAddrResp struct {
	Addr                 *PeripheralOrderAddr `protobuf:"bytes,1,opt,name=addr,proto3" json:"addr"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *GetUserPeripheralOrderAddrResp) Reset()         { *m = GetUserPeripheralOrderAddrResp{} }
func (m *GetUserPeripheralOrderAddrResp) String() string { return proto.CompactTextString(m) }
func (*GetUserPeripheralOrderAddrResp) ProtoMessage()    {}
func (*GetUserPeripheralOrderAddrResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_revenue_http_logic_907ee343f90f615b, []int{96}
}
func (m *GetUserPeripheralOrderAddrResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserPeripheralOrderAddrResp.Unmarshal(m, b)
}
func (m *GetUserPeripheralOrderAddrResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserPeripheralOrderAddrResp.Marshal(b, m, deterministic)
}
func (dst *GetUserPeripheralOrderAddrResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserPeripheralOrderAddrResp.Merge(dst, src)
}
func (m *GetUserPeripheralOrderAddrResp) XXX_Size() int {
	return xxx_messageInfo_GetUserPeripheralOrderAddrResp.Size(m)
}
func (m *GetUserPeripheralOrderAddrResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserPeripheralOrderAddrResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserPeripheralOrderAddrResp proto.InternalMessageInfo

func (m *GetUserPeripheralOrderAddrResp) GetAddr() *PeripheralOrderAddr {
	if m != nil {
		return m.Addr
	}
	return nil
}

// 保存用户下单地址 url：/tt_peripheral/order_addr/set
type SetUserPeripheralOrderAddrReq struct {
	Addr                 *PeripheralOrderAddr `protobuf:"bytes,1,opt,name=addr,proto3" json:"addr"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *SetUserPeripheralOrderAddrReq) Reset()         { *m = SetUserPeripheralOrderAddrReq{} }
func (m *SetUserPeripheralOrderAddrReq) String() string { return proto.CompactTextString(m) }
func (*SetUserPeripheralOrderAddrReq) ProtoMessage()    {}
func (*SetUserPeripheralOrderAddrReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_revenue_http_logic_907ee343f90f615b, []int{97}
}
func (m *SetUserPeripheralOrderAddrReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetUserPeripheralOrderAddrReq.Unmarshal(m, b)
}
func (m *SetUserPeripheralOrderAddrReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetUserPeripheralOrderAddrReq.Marshal(b, m, deterministic)
}
func (dst *SetUserPeripheralOrderAddrReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetUserPeripheralOrderAddrReq.Merge(dst, src)
}
func (m *SetUserPeripheralOrderAddrReq) XXX_Size() int {
	return xxx_messageInfo_SetUserPeripheralOrderAddrReq.Size(m)
}
func (m *SetUserPeripheralOrderAddrReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetUserPeripheralOrderAddrReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetUserPeripheralOrderAddrReq proto.InternalMessageInfo

func (m *SetUserPeripheralOrderAddrReq) GetAddr() *PeripheralOrderAddr {
	if m != nil {
		return m.Addr
	}
	return nil
}

type SetUserPeripheralOrderAddrResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetUserPeripheralOrderAddrResp) Reset()         { *m = SetUserPeripheralOrderAddrResp{} }
func (m *SetUserPeripheralOrderAddrResp) String() string { return proto.CompactTextString(m) }
func (*SetUserPeripheralOrderAddrResp) ProtoMessage()    {}
func (*SetUserPeripheralOrderAddrResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_revenue_http_logic_907ee343f90f615b, []int{98}
}
func (m *SetUserPeripheralOrderAddrResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetUserPeripheralOrderAddrResp.Unmarshal(m, b)
}
func (m *SetUserPeripheralOrderAddrResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetUserPeripheralOrderAddrResp.Marshal(b, m, deterministic)
}
func (dst *SetUserPeripheralOrderAddrResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetUserPeripheralOrderAddrResp.Merge(dst, src)
}
func (m *SetUserPeripheralOrderAddrResp) XXX_Size() int {
	return xxx_messageInfo_SetUserPeripheralOrderAddrResp.Size(m)
}
func (m *SetUserPeripheralOrderAddrResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetUserPeripheralOrderAddrResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetUserPeripheralOrderAddrResp proto.InternalMessageInfo

type AwardInfo struct {
	GiftId               uint32   `protobuf:"varint,1,opt,name=gift_id,json=giftId,proto3" json:"gift_id"`
	GiftName             string   `protobuf:"bytes,2,opt,name=gift_name,json=giftName,proto3" json:"gift_name"`
	GiftCnt              uint32   `protobuf:"varint,3,opt,name=gift_cnt,json=giftCnt,proto3" json:"gift_cnt"`
	GiftIcon             string   `protobuf:"bytes,4,opt,name=gift_icon,json=giftIcon,proto3" json:"gift_icon"`
	GiftVal              uint32   `protobuf:"varint,5,opt,name=gift_val,json=giftVal,proto3" json:"gift_val"`
	ExpireDay            uint32   `protobuf:"varint,6,opt,name=expire_day,json=expireDay,proto3" json:"expire_day"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AwardInfo) Reset()         { *m = AwardInfo{} }
func (m *AwardInfo) String() string { return proto.CompactTextString(m) }
func (*AwardInfo) ProtoMessage()    {}
func (*AwardInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_revenue_http_logic_907ee343f90f615b, []int{99}
}
func (m *AwardInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AwardInfo.Unmarshal(m, b)
}
func (m *AwardInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AwardInfo.Marshal(b, m, deterministic)
}
func (dst *AwardInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AwardInfo.Merge(dst, src)
}
func (m *AwardInfo) XXX_Size() int {
	return xxx_messageInfo_AwardInfo.Size(m)
}
func (m *AwardInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_AwardInfo.DiscardUnknown(m)
}

var xxx_messageInfo_AwardInfo proto.InternalMessageInfo

func (m *AwardInfo) GetGiftId() uint32 {
	if m != nil {
		return m.GiftId
	}
	return 0
}

func (m *AwardInfo) GetGiftName() string {
	if m != nil {
		return m.GiftName
	}
	return ""
}

func (m *AwardInfo) GetGiftCnt() uint32 {
	if m != nil {
		return m.GiftCnt
	}
	return 0
}

func (m *AwardInfo) GetGiftIcon() string {
	if m != nil {
		return m.GiftIcon
	}
	return ""
}

func (m *AwardInfo) GetGiftVal() uint32 {
	if m != nil {
		return m.GiftVal
	}
	return 0
}

func (m *AwardInfo) GetExpireDay() uint32 {
	if m != nil {
		return m.ExpireDay
	}
	return 0
}

type ReturnConsumeAwardInfo struct {
	AwardInfo            *AwardInfo `protobuf:"bytes,1,opt,name=award_info,json=awardInfo,proto3" json:"award_info"`
	ReceiveVal           uint32     `protobuf:"varint,2,opt,name=receive_val,json=receiveVal,proto3" json:"receive_val"`
	ReceiveStatus        uint32     `protobuf:"varint,3,opt,name=receive_status,json=receiveStatus,proto3" json:"receive_status"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *ReturnConsumeAwardInfo) Reset()         { *m = ReturnConsumeAwardInfo{} }
func (m *ReturnConsumeAwardInfo) String() string { return proto.CompactTextString(m) }
func (*ReturnConsumeAwardInfo) ProtoMessage()    {}
func (*ReturnConsumeAwardInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_revenue_http_logic_907ee343f90f615b, []int{100}
}
func (m *ReturnConsumeAwardInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReturnConsumeAwardInfo.Unmarshal(m, b)
}
func (m *ReturnConsumeAwardInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReturnConsumeAwardInfo.Marshal(b, m, deterministic)
}
func (dst *ReturnConsumeAwardInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReturnConsumeAwardInfo.Merge(dst, src)
}
func (m *ReturnConsumeAwardInfo) XXX_Size() int {
	return xxx_messageInfo_ReturnConsumeAwardInfo.Size(m)
}
func (m *ReturnConsumeAwardInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ReturnConsumeAwardInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ReturnConsumeAwardInfo proto.InternalMessageInfo

func (m *ReturnConsumeAwardInfo) GetAwardInfo() *AwardInfo {
	if m != nil {
		return m.AwardInfo
	}
	return nil
}

func (m *ReturnConsumeAwardInfo) GetReceiveVal() uint32 {
	if m != nil {
		return m.ReceiveVal
	}
	return 0
}

func (m *ReturnConsumeAwardInfo) GetReceiveStatus() uint32 {
	if m != nil {
		return m.ReceiveStatus
	}
	return 0
}

type GetReturnAwardInfoResp struct {
	TaskLimitedTime         uint32                    `protobuf:"varint,1,opt,name=task_limited_time,json=taskLimitedTime,proto3" json:"task_limited_time"`
	ConLoginDay             uint32                    `protobuf:"varint,2,opt,name=con_login_day,json=conLoginDay,proto3" json:"con_login_day"`
	MaxLoginDay             uint32                    `protobuf:"varint,3,opt,name=max_login_day,json=maxLoginDay,proto3" json:"max_login_day"`
	LoginAward              []*AwardInfo              `protobuf:"bytes,4,rep,name=login_award,json=loginAward,proto3" json:"login_award"`
	LoginAwardReceiveStatus uint32                    `protobuf:"varint,5,opt,name=login_award_receive_status,json=loginAwardReceiveStatus,proto3" json:"login_award_receive_status"`
	ConConsumeVal           uint32                    `protobuf:"varint,6,opt,name=con_consume_val,json=conConsumeVal,proto3" json:"con_consume_val"`
	MaxConsumeVal           uint32                    `protobuf:"varint,7,opt,name=max_consume_val,json=maxConsumeVal,proto3" json:"max_consume_val"`
	ConsumeAward            []*ReturnConsumeAwardInfo `protobuf:"bytes,8,rep,name=consume_award,json=consumeAward,proto3" json:"consume_award"`
	AwardLimitedTime        uint32                    `protobuf:"varint,9,opt,name=award_limited_time,json=awardLimitedTime,proto3" json:"award_limited_time"`
	XXX_NoUnkeyedLiteral    struct{}                  `json:"-"`
	XXX_unrecognized        []byte                    `json:"-"`
	XXX_sizecache           int32                     `json:"-"`
}

func (m *GetReturnAwardInfoResp) Reset()         { *m = GetReturnAwardInfoResp{} }
func (m *GetReturnAwardInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetReturnAwardInfoResp) ProtoMessage()    {}
func (*GetReturnAwardInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_revenue_http_logic_907ee343f90f615b, []int{101}
}
func (m *GetReturnAwardInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetReturnAwardInfoResp.Unmarshal(m, b)
}
func (m *GetReturnAwardInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetReturnAwardInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetReturnAwardInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetReturnAwardInfoResp.Merge(dst, src)
}
func (m *GetReturnAwardInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetReturnAwardInfoResp.Size(m)
}
func (m *GetReturnAwardInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetReturnAwardInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetReturnAwardInfoResp proto.InternalMessageInfo

func (m *GetReturnAwardInfoResp) GetTaskLimitedTime() uint32 {
	if m != nil {
		return m.TaskLimitedTime
	}
	return 0
}

func (m *GetReturnAwardInfoResp) GetConLoginDay() uint32 {
	if m != nil {
		return m.ConLoginDay
	}
	return 0
}

func (m *GetReturnAwardInfoResp) GetMaxLoginDay() uint32 {
	if m != nil {
		return m.MaxLoginDay
	}
	return 0
}

func (m *GetReturnAwardInfoResp) GetLoginAward() []*AwardInfo {
	if m != nil {
		return m.LoginAward
	}
	return nil
}

func (m *GetReturnAwardInfoResp) GetLoginAwardReceiveStatus() uint32 {
	if m != nil {
		return m.LoginAwardReceiveStatus
	}
	return 0
}

func (m *GetReturnAwardInfoResp) GetConConsumeVal() uint32 {
	if m != nil {
		return m.ConConsumeVal
	}
	return 0
}

func (m *GetReturnAwardInfoResp) GetMaxConsumeVal() uint32 {
	if m != nil {
		return m.MaxConsumeVal
	}
	return 0
}

func (m *GetReturnAwardInfoResp) GetConsumeAward() []*ReturnConsumeAwardInfo {
	if m != nil {
		return m.ConsumeAward
	}
	return nil
}

func (m *GetReturnAwardInfoResp) GetAwardLimitedTime() uint32 {
	if m != nil {
		return m.AwardLimitedTime
	}
	return 0
}

type CheckEntryReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckEntryReq) Reset()         { *m = CheckEntryReq{} }
func (m *CheckEntryReq) String() string { return proto.CompactTextString(m) }
func (*CheckEntryReq) ProtoMessage()    {}
func (*CheckEntryReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_revenue_http_logic_907ee343f90f615b, []int{102}
}
func (m *CheckEntryReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckEntryReq.Unmarshal(m, b)
}
func (m *CheckEntryReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckEntryReq.Marshal(b, m, deterministic)
}
func (dst *CheckEntryReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckEntryReq.Merge(dst, src)
}
func (m *CheckEntryReq) XXX_Size() int {
	return xxx_messageInfo_CheckEntryReq.Size(m)
}
func (m *CheckEntryReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckEntryReq.DiscardUnknown(m)
}

var xxx_messageInfo_CheckEntryReq proto.InternalMessageInfo

type CheckEntryResp struct {
	CommonIsOpen         bool     `protobuf:"varint,1,opt,name=common_is_open,json=commonIsOpen,proto3" json:"common_is_open"`
	GrandIsOpen          bool     `protobuf:"varint,2,opt,name=grand_is_open,json=grandIsOpen,proto3" json:"grand_is_open"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckEntryResp) Reset()         { *m = CheckEntryResp{} }
func (m *CheckEntryResp) String() string { return proto.CompactTextString(m) }
func (*CheckEntryResp) ProtoMessage()    {}
func (*CheckEntryResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_revenue_http_logic_907ee343f90f615b, []int{103}
}
func (m *CheckEntryResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckEntryResp.Unmarshal(m, b)
}
func (m *CheckEntryResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckEntryResp.Marshal(b, m, deterministic)
}
func (dst *CheckEntryResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckEntryResp.Merge(dst, src)
}
func (m *CheckEntryResp) XXX_Size() int {
	return xxx_messageInfo_CheckEntryResp.Size(m)
}
func (m *CheckEntryResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckEntryResp.DiscardUnknown(m)
}

var xxx_messageInfo_CheckEntryResp proto.InternalMessageInfo

func (m *CheckEntryResp) GetCommonIsOpen() bool {
	if m != nil {
		return m.CommonIsOpen
	}
	return false
}

func (m *CheckEntryResp) GetGrandIsOpen() bool {
	if m != nil {
		return m.GrandIsOpen
	}
	return false
}

type GetPreInfoReq struct {
	PondType             uint32   `protobuf:"varint,1,opt,name=pond_type,json=pondType,proto3" json:"pond_type"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPreInfoReq) Reset()         { *m = GetPreInfoReq{} }
func (m *GetPreInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetPreInfoReq) ProtoMessage()    {}
func (*GetPreInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_revenue_http_logic_907ee343f90f615b, []int{104}
}
func (m *GetPreInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPreInfoReq.Unmarshal(m, b)
}
func (m *GetPreInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPreInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetPreInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPreInfoReq.Merge(dst, src)
}
func (m *GetPreInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetPreInfoReq.Size(m)
}
func (m *GetPreInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPreInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPreInfoReq proto.InternalMessageInfo

func (m *GetPreInfoReq) GetPondType() uint32 {
	if m != nil {
		return m.PondType
	}
	return 0
}

type GetPreInfoResp struct {
	SingleLotteryCost    uint32   `protobuf:"varint,1,opt,name=single_lottery_cost,json=singleLotteryCost,proto3" json:"single_lottery_cost"`
	RemainLotteryTimes   uint32   `protobuf:"varint,2,opt,name=remain_lottery_times,json=remainLotteryTimes,proto3" json:"remain_lottery_times"`
	IncrLotteryTbean     uint32   `protobuf:"varint,3,opt,name=incr_lottery_tbean,json=incrLotteryTbean,proto3" json:"incr_lottery_tbean"`
	IncrLotteryTimes     uint32   `protobuf:"varint,4,opt,name=incr_lottery_times,json=incrLotteryTimes,proto3" json:"incr_lottery_times"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPreInfoResp) Reset()         { *m = GetPreInfoResp{} }
func (m *GetPreInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetPreInfoResp) ProtoMessage()    {}
func (*GetPreInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_revenue_http_logic_907ee343f90f615b, []int{105}
}
func (m *GetPreInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPreInfoResp.Unmarshal(m, b)
}
func (m *GetPreInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPreInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetPreInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPreInfoResp.Merge(dst, src)
}
func (m *GetPreInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetPreInfoResp.Size(m)
}
func (m *GetPreInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPreInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPreInfoResp proto.InternalMessageInfo

func (m *GetPreInfoResp) GetSingleLotteryCost() uint32 {
	if m != nil {
		return m.SingleLotteryCost
	}
	return 0
}

func (m *GetPreInfoResp) GetRemainLotteryTimes() uint32 {
	if m != nil {
		return m.RemainLotteryTimes
	}
	return 0
}

func (m *GetPreInfoResp) GetIncrLotteryTbean() uint32 {
	if m != nil {
		return m.IncrLotteryTbean
	}
	return 0
}

func (m *GetPreInfoResp) GetIncrLotteryTimes() uint32 {
	if m != nil {
		return m.IncrLotteryTimes
	}
	return 0
}

// 获取奖池预览
type GetAwardPreviewReq struct {
	PondType             uint32   `protobuf:"varint,1,opt,name=pond_type,json=pondType,proto3" json:"pond_type"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAwardPreviewReq) Reset()         { *m = GetAwardPreviewReq{} }
func (m *GetAwardPreviewReq) String() string { return proto.CompactTextString(m) }
func (*GetAwardPreviewReq) ProtoMessage()    {}
func (*GetAwardPreviewReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_revenue_http_logic_907ee343f90f615b, []int{106}
}
func (m *GetAwardPreviewReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAwardPreviewReq.Unmarshal(m, b)
}
func (m *GetAwardPreviewReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAwardPreviewReq.Marshal(b, m, deterministic)
}
func (dst *GetAwardPreviewReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAwardPreviewReq.Merge(dst, src)
}
func (m *GetAwardPreviewReq) XXX_Size() int {
	return xxx_messageInfo_GetAwardPreviewReq.Size(m)
}
func (m *GetAwardPreviewReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAwardPreviewReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAwardPreviewReq proto.InternalMessageInfo

func (m *GetAwardPreviewReq) GetPondType() uint32 {
	if m != nil {
		return m.PondType
	}
	return 0
}

type GetAwardPreviewResp struct {
	AwardPreviewList     []*AwardPreviewInfo `protobuf:"bytes,1,rep,name=award_preview_list,json=awardPreviewList,proto3" json:"award_preview_list"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *GetAwardPreviewResp) Reset()         { *m = GetAwardPreviewResp{} }
func (m *GetAwardPreviewResp) String() string { return proto.CompactTextString(m) }
func (*GetAwardPreviewResp) ProtoMessage()    {}
func (*GetAwardPreviewResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_revenue_http_logic_907ee343f90f615b, []int{107}
}
func (m *GetAwardPreviewResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAwardPreviewResp.Unmarshal(m, b)
}
func (m *GetAwardPreviewResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAwardPreviewResp.Marshal(b, m, deterministic)
}
func (dst *GetAwardPreviewResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAwardPreviewResp.Merge(dst, src)
}
func (m *GetAwardPreviewResp) XXX_Size() int {
	return xxx_messageInfo_GetAwardPreviewResp.Size(m)
}
func (m *GetAwardPreviewResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAwardPreviewResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAwardPreviewResp proto.InternalMessageInfo

func (m *GetAwardPreviewResp) GetAwardPreviewList() []*AwardPreviewInfo {
	if m != nil {
		return m.AwardPreviewList
	}
	return nil
}

type AwardPreviewInfo struct {
	AwardInfo            *GloryMagicAwardInfo `protobuf:"bytes,1,opt,name=award_info,json=awardInfo,proto3" json:"award_info"`
	Chance               uint32               `protobuf:"varint,2,opt,name=chance,proto3" json:"chance"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *AwardPreviewInfo) Reset()         { *m = AwardPreviewInfo{} }
func (m *AwardPreviewInfo) String() string { return proto.CompactTextString(m) }
func (*AwardPreviewInfo) ProtoMessage()    {}
func (*AwardPreviewInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_revenue_http_logic_907ee343f90f615b, []int{108}
}
func (m *AwardPreviewInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AwardPreviewInfo.Unmarshal(m, b)
}
func (m *AwardPreviewInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AwardPreviewInfo.Marshal(b, m, deterministic)
}
func (dst *AwardPreviewInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AwardPreviewInfo.Merge(dst, src)
}
func (m *AwardPreviewInfo) XXX_Size() int {
	return xxx_messageInfo_AwardPreviewInfo.Size(m)
}
func (m *AwardPreviewInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_AwardPreviewInfo.DiscardUnknown(m)
}

var xxx_messageInfo_AwardPreviewInfo proto.InternalMessageInfo

func (m *AwardPreviewInfo) GetAwardInfo() *GloryMagicAwardInfo {
	if m != nil {
		return m.AwardInfo
	}
	return nil
}

func (m *AwardPreviewInfo) GetChance() uint32 {
	if m != nil {
		return m.Chance
	}
	return 0
}

type GloryMagicAwardInfo struct {
	AwardName            string   `protobuf:"bytes,1,opt,name=award_name,json=awardName,proto3" json:"award_name"`
	AwardIcon            string   `protobuf:"bytes,2,opt,name=award_icon,json=awardIcon,proto3" json:"award_icon"`
	AwardWorth           uint32   `protobuf:"varint,3,opt,name=award_worth,json=awardWorth,proto3" json:"award_worth"`
	AwardNum             uint32   `protobuf:"varint,4,opt,name=award_num,json=awardNum,proto3" json:"award_num"`
	AwardDays            uint32   `protobuf:"varint,5,opt,name=award_days,json=awardDays,proto3" json:"award_days"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GloryMagicAwardInfo) Reset()         { *m = GloryMagicAwardInfo{} }
func (m *GloryMagicAwardInfo) String() string { return proto.CompactTextString(m) }
func (*GloryMagicAwardInfo) ProtoMessage()    {}
func (*GloryMagicAwardInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_revenue_http_logic_907ee343f90f615b, []int{109}
}
func (m *GloryMagicAwardInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GloryMagicAwardInfo.Unmarshal(m, b)
}
func (m *GloryMagicAwardInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GloryMagicAwardInfo.Marshal(b, m, deterministic)
}
func (dst *GloryMagicAwardInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GloryMagicAwardInfo.Merge(dst, src)
}
func (m *GloryMagicAwardInfo) XXX_Size() int {
	return xxx_messageInfo_GloryMagicAwardInfo.Size(m)
}
func (m *GloryMagicAwardInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_GloryMagicAwardInfo.DiscardUnknown(m)
}

var xxx_messageInfo_GloryMagicAwardInfo proto.InternalMessageInfo

func (m *GloryMagicAwardInfo) GetAwardName() string {
	if m != nil {
		return m.AwardName
	}
	return ""
}

func (m *GloryMagicAwardInfo) GetAwardIcon() string {
	if m != nil {
		return m.AwardIcon
	}
	return ""
}

func (m *GloryMagicAwardInfo) GetAwardWorth() uint32 {
	if m != nil {
		return m.AwardWorth
	}
	return 0
}

func (m *GloryMagicAwardInfo) GetAwardNum() uint32 {
	if m != nil {
		return m.AwardNum
	}
	return 0
}

func (m *GloryMagicAwardInfo) GetAwardDays() uint32 {
	if m != nil {
		return m.AwardDays
	}
	return 0
}

// 获取中奖轮播
type GetWinningCarouselReq struct {
	PondType             uint32   `protobuf:"varint,1,opt,name=pond_type,json=pondType,proto3" json:"pond_type"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetWinningCarouselReq) Reset()         { *m = GetWinningCarouselReq{} }
func (m *GetWinningCarouselReq) String() string { return proto.CompactTextString(m) }
func (*GetWinningCarouselReq) ProtoMessage()    {}
func (*GetWinningCarouselReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_revenue_http_logic_907ee343f90f615b, []int{110}
}
func (m *GetWinningCarouselReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetWinningCarouselReq.Unmarshal(m, b)
}
func (m *GetWinningCarouselReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetWinningCarouselReq.Marshal(b, m, deterministic)
}
func (dst *GetWinningCarouselReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetWinningCarouselReq.Merge(dst, src)
}
func (m *GetWinningCarouselReq) XXX_Size() int {
	return xxx_messageInfo_GetWinningCarouselReq.Size(m)
}
func (m *GetWinningCarouselReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetWinningCarouselReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetWinningCarouselReq proto.InternalMessageInfo

func (m *GetWinningCarouselReq) GetPondType() uint32 {
	if m != nil {
		return m.PondType
	}
	return 0
}

type WinningInfo struct {
	Nickname             string               `protobuf:"bytes,1,opt,name=nickname,proto3" json:"nickname"`
	AwardInfo            *GloryMagicAwardInfo `protobuf:"bytes,2,opt,name=award_info,json=awardInfo,proto3" json:"award_info"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *WinningInfo) Reset()         { *m = WinningInfo{} }
func (m *WinningInfo) String() string { return proto.CompactTextString(m) }
func (*WinningInfo) ProtoMessage()    {}
func (*WinningInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_revenue_http_logic_907ee343f90f615b, []int{111}
}
func (m *WinningInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WinningInfo.Unmarshal(m, b)
}
func (m *WinningInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WinningInfo.Marshal(b, m, deterministic)
}
func (dst *WinningInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WinningInfo.Merge(dst, src)
}
func (m *WinningInfo) XXX_Size() int {
	return xxx_messageInfo_WinningInfo.Size(m)
}
func (m *WinningInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_WinningInfo.DiscardUnknown(m)
}

var xxx_messageInfo_WinningInfo proto.InternalMessageInfo

func (m *WinningInfo) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *WinningInfo) GetAwardInfo() *GloryMagicAwardInfo {
	if m != nil {
		return m.AwardInfo
	}
	return nil
}

type GetWinningCarouselResp struct {
	WinningInfoList      []*WinningInfo `protobuf:"bytes,1,rep,name=winning_info_list,json=winningInfoList,proto3" json:"winning_info_list"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *GetWinningCarouselResp) Reset()         { *m = GetWinningCarouselResp{} }
func (m *GetWinningCarouselResp) String() string { return proto.CompactTextString(m) }
func (*GetWinningCarouselResp) ProtoMessage()    {}
func (*GetWinningCarouselResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_revenue_http_logic_907ee343f90f615b, []int{112}
}
func (m *GetWinningCarouselResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetWinningCarouselResp.Unmarshal(m, b)
}
func (m *GetWinningCarouselResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetWinningCarouselResp.Marshal(b, m, deterministic)
}
func (dst *GetWinningCarouselResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetWinningCarouselResp.Merge(dst, src)
}
func (m *GetWinningCarouselResp) XXX_Size() int {
	return xxx_messageInfo_GetWinningCarouselResp.Size(m)
}
func (m *GetWinningCarouselResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetWinningCarouselResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetWinningCarouselResp proto.InternalMessageInfo

func (m *GetWinningCarouselResp) GetWinningInfoList() []*WinningInfo {
	if m != nil {
		return m.WinningInfoList
	}
	return nil
}

// 抽奖接口
type LotteryReq struct {
	PondType             uint32   `protobuf:"varint,1,opt,name=pond_type,json=pondType,proto3" json:"pond_type"`
	Times                uint32   `protobuf:"varint,2,opt,name=times,proto3" json:"times"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *LotteryReq) Reset()         { *m = LotteryReq{} }
func (m *LotteryReq) String() string { return proto.CompactTextString(m) }
func (*LotteryReq) ProtoMessage()    {}
func (*LotteryReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_revenue_http_logic_907ee343f90f615b, []int{113}
}
func (m *LotteryReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LotteryReq.Unmarshal(m, b)
}
func (m *LotteryReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LotteryReq.Marshal(b, m, deterministic)
}
func (dst *LotteryReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LotteryReq.Merge(dst, src)
}
func (m *LotteryReq) XXX_Size() int {
	return xxx_messageInfo_LotteryReq.Size(m)
}
func (m *LotteryReq) XXX_DiscardUnknown() {
	xxx_messageInfo_LotteryReq.DiscardUnknown(m)
}

var xxx_messageInfo_LotteryReq proto.InternalMessageInfo

func (m *LotteryReq) GetPondType() uint32 {
	if m != nil {
		return m.PondType
	}
	return 0
}

func (m *LotteryReq) GetTimes() uint32 {
	if m != nil {
		return m.Times
	}
	return 0
}

type LotteryResp struct {
	WinningType          uint32                 `protobuf:"varint,1,opt,name=winning_type,json=winningType,proto3" json:"winning_type"`
	AwardInfo            []*GloryMagicAwardInfo `protobuf:"bytes,2,rep,name=award_info,json=awardInfo,proto3" json:"award_info"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *LotteryResp) Reset()         { *m = LotteryResp{} }
func (m *LotteryResp) String() string { return proto.CompactTextString(m) }
func (*LotteryResp) ProtoMessage()    {}
func (*LotteryResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_revenue_http_logic_907ee343f90f615b, []int{114}
}
func (m *LotteryResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LotteryResp.Unmarshal(m, b)
}
func (m *LotteryResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LotteryResp.Marshal(b, m, deterministic)
}
func (dst *LotteryResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LotteryResp.Merge(dst, src)
}
func (m *LotteryResp) XXX_Size() int {
	return xxx_messageInfo_LotteryResp.Size(m)
}
func (m *LotteryResp) XXX_DiscardUnknown() {
	xxx_messageInfo_LotteryResp.DiscardUnknown(m)
}

var xxx_messageInfo_LotteryResp proto.InternalMessageInfo

func (m *LotteryResp) GetWinningType() uint32 {
	if m != nil {
		return m.WinningType
	}
	return 0
}

func (m *LotteryResp) GetAwardInfo() []*GloryMagicAwardInfo {
	if m != nil {
		return m.AwardInfo
	}
	return nil
}

// 获取抽奖记录
type GetLotteryRecordReq struct {
	PondType             uint32   `protobuf:"varint,1,opt,name=pond_type,json=pondType,proto3" json:"pond_type"`
	Offset               string   `protobuf:"bytes,2,opt,name=offset,proto3" json:"offset"`
	Limit                uint32   `protobuf:"varint,3,opt,name=limit,proto3" json:"limit"`
	ExchangeType         uint32   `protobuf:"varint,4,opt,name=exchange_type,json=exchangeType,proto3" json:"exchange_type"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetLotteryRecordReq) Reset()         { *m = GetLotteryRecordReq{} }
func (m *GetLotteryRecordReq) String() string { return proto.CompactTextString(m) }
func (*GetLotteryRecordReq) ProtoMessage()    {}
func (*GetLotteryRecordReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_revenue_http_logic_907ee343f90f615b, []int{115}
}
func (m *GetLotteryRecordReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetLotteryRecordReq.Unmarshal(m, b)
}
func (m *GetLotteryRecordReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetLotteryRecordReq.Marshal(b, m, deterministic)
}
func (dst *GetLotteryRecordReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetLotteryRecordReq.Merge(dst, src)
}
func (m *GetLotteryRecordReq) XXX_Size() int {
	return xxx_messageInfo_GetLotteryRecordReq.Size(m)
}
func (m *GetLotteryRecordReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetLotteryRecordReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetLotteryRecordReq proto.InternalMessageInfo

func (m *GetLotteryRecordReq) GetPondType() uint32 {
	if m != nil {
		return m.PondType
	}
	return 0
}

func (m *GetLotteryRecordReq) GetOffset() string {
	if m != nil {
		return m.Offset
	}
	return ""
}

func (m *GetLotteryRecordReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *GetLotteryRecordReq) GetExchangeType() uint32 {
	if m != nil {
		return m.ExchangeType
	}
	return 0
}

type LotteryRecord struct {
	AwardInfo            *GloryMagicAwardInfo `protobuf:"bytes,1,opt,name=award_info,json=awardInfo,proto3" json:"award_info"`
	Cost                 uint32               `protobuf:"varint,2,opt,name=cost,proto3" json:"cost"`
	GotTime              uint32               `protobuf:"varint,3,opt,name=got_time,json=gotTime,proto3" json:"got_time"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *LotteryRecord) Reset()         { *m = LotteryRecord{} }
func (m *LotteryRecord) String() string { return proto.CompactTextString(m) }
func (*LotteryRecord) ProtoMessage()    {}
func (*LotteryRecord) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_revenue_http_logic_907ee343f90f615b, []int{116}
}
func (m *LotteryRecord) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LotteryRecord.Unmarshal(m, b)
}
func (m *LotteryRecord) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LotteryRecord.Marshal(b, m, deterministic)
}
func (dst *LotteryRecord) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LotteryRecord.Merge(dst, src)
}
func (m *LotteryRecord) XXX_Size() int {
	return xxx_messageInfo_LotteryRecord.Size(m)
}
func (m *LotteryRecord) XXX_DiscardUnknown() {
	xxx_messageInfo_LotteryRecord.DiscardUnknown(m)
}

var xxx_messageInfo_LotteryRecord proto.InternalMessageInfo

func (m *LotteryRecord) GetAwardInfo() *GloryMagicAwardInfo {
	if m != nil {
		return m.AwardInfo
	}
	return nil
}

func (m *LotteryRecord) GetCost() uint32 {
	if m != nil {
		return m.Cost
	}
	return 0
}

func (m *LotteryRecord) GetGotTime() uint32 {
	if m != nil {
		return m.GotTime
	}
	return 0
}

type GetMyLotteryRecordResp struct {
	LotteryRecordList    []*LotteryRecord `protobuf:"bytes,1,rep,name=lottery_record_list,json=lotteryRecordList,proto3" json:"lottery_record_list"`
	Offset               string           `protobuf:"bytes,2,opt,name=offset,proto3" json:"offset"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GetMyLotteryRecordResp) Reset()         { *m = GetMyLotteryRecordResp{} }
func (m *GetMyLotteryRecordResp) String() string { return proto.CompactTextString(m) }
func (*GetMyLotteryRecordResp) ProtoMessage()    {}
func (*GetMyLotteryRecordResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_revenue_http_logic_907ee343f90f615b, []int{117}
}
func (m *GetMyLotteryRecordResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMyLotteryRecordResp.Unmarshal(m, b)
}
func (m *GetMyLotteryRecordResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMyLotteryRecordResp.Marshal(b, m, deterministic)
}
func (dst *GetMyLotteryRecordResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMyLotteryRecordResp.Merge(dst, src)
}
func (m *GetMyLotteryRecordResp) XXX_Size() int {
	return xxx_messageInfo_GetMyLotteryRecordResp.Size(m)
}
func (m *GetMyLotteryRecordResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMyLotteryRecordResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetMyLotteryRecordResp proto.InternalMessageInfo

func (m *GetMyLotteryRecordResp) GetLotteryRecordList() []*LotteryRecord {
	if m != nil {
		return m.LotteryRecordList
	}
	return nil
}

func (m *GetMyLotteryRecordResp) GetOffset() string {
	if m != nil {
		return m.Offset
	}
	return ""
}

// 查询用户周卡入口权限
// url：/tt-revenue-http-logic/present-week-card/entry
type GetPresentWeekCardEntryReq struct {
	MarketId             uint32   `protobuf:"varint,1,opt,name=market_id,json=marketId,proto3" json:"market_id"`
	CliVersion           uint32   `protobuf:"varint,2,opt,name=cli_version,json=cliVersion,proto3" json:"cli_version"`
	OsType               uint32   `protobuf:"varint,3,opt,name=os_type,json=osType,proto3" json:"os_type"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPresentWeekCardEntryReq) Reset()         { *m = GetPresentWeekCardEntryReq{} }
func (m *GetPresentWeekCardEntryReq) String() string { return proto.CompactTextString(m) }
func (*GetPresentWeekCardEntryReq) ProtoMessage()    {}
func (*GetPresentWeekCardEntryReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_revenue_http_logic_907ee343f90f615b, []int{118}
}
func (m *GetPresentWeekCardEntryReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPresentWeekCardEntryReq.Unmarshal(m, b)
}
func (m *GetPresentWeekCardEntryReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPresentWeekCardEntryReq.Marshal(b, m, deterministic)
}
func (dst *GetPresentWeekCardEntryReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPresentWeekCardEntryReq.Merge(dst, src)
}
func (m *GetPresentWeekCardEntryReq) XXX_Size() int {
	return xxx_messageInfo_GetPresentWeekCardEntryReq.Size(m)
}
func (m *GetPresentWeekCardEntryReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPresentWeekCardEntryReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPresentWeekCardEntryReq proto.InternalMessageInfo

func (m *GetPresentWeekCardEntryReq) GetMarketId() uint32 {
	if m != nil {
		return m.MarketId
	}
	return 0
}

func (m *GetPresentWeekCardEntryReq) GetCliVersion() uint32 {
	if m != nil {
		return m.CliVersion
	}
	return 0
}

func (m *GetPresentWeekCardEntryReq) GetOsType() uint32 {
	if m != nil {
		return m.OsType
	}
	return 0
}

type GetPresentWeekCardEntryResp struct {
	HaveAccess           bool     `protobuf:"varint,1,opt,name=have_access,json=haveAccess,proto3" json:"have_access"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPresentWeekCardEntryResp) Reset()         { *m = GetPresentWeekCardEntryResp{} }
func (m *GetPresentWeekCardEntryResp) String() string { return proto.CompactTextString(m) }
func (*GetPresentWeekCardEntryResp) ProtoMessage()    {}
func (*GetPresentWeekCardEntryResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_revenue_http_logic_907ee343f90f615b, []int{119}
}
func (m *GetPresentWeekCardEntryResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPresentWeekCardEntryResp.Unmarshal(m, b)
}
func (m *GetPresentWeekCardEntryResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPresentWeekCardEntryResp.Marshal(b, m, deterministic)
}
func (dst *GetPresentWeekCardEntryResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPresentWeekCardEntryResp.Merge(dst, src)
}
func (m *GetPresentWeekCardEntryResp) XXX_Size() int {
	return xxx_messageInfo_GetPresentWeekCardEntryResp.Size(m)
}
func (m *GetPresentWeekCardEntryResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPresentWeekCardEntryResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPresentWeekCardEntryResp proto.InternalMessageInfo

func (m *GetPresentWeekCardEntryResp) GetHaveAccess() bool {
	if m != nil {
		return m.HaveAccess
	}
	return false
}

type GetMyLotteryRecordTypeReq struct {
	PondType             uint32   `protobuf:"varint,1,opt,name=pond_type,json=pondType,proto3" json:"pond_type"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetMyLotteryRecordTypeReq) Reset()         { *m = GetMyLotteryRecordTypeReq{} }
func (m *GetMyLotteryRecordTypeReq) String() string { return proto.CompactTextString(m) }
func (*GetMyLotteryRecordTypeReq) ProtoMessage()    {}
func (*GetMyLotteryRecordTypeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_revenue_http_logic_907ee343f90f615b, []int{120}
}
func (m *GetMyLotteryRecordTypeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMyLotteryRecordTypeReq.Unmarshal(m, b)
}
func (m *GetMyLotteryRecordTypeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMyLotteryRecordTypeReq.Marshal(b, m, deterministic)
}
func (dst *GetMyLotteryRecordTypeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMyLotteryRecordTypeReq.Merge(dst, src)
}
func (m *GetMyLotteryRecordTypeReq) XXX_Size() int {
	return xxx_messageInfo_GetMyLotteryRecordTypeReq.Size(m)
}
func (m *GetMyLotteryRecordTypeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMyLotteryRecordTypeReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetMyLotteryRecordTypeReq proto.InternalMessageInfo

func (m *GetMyLotteryRecordTypeReq) GetPondType() uint32 {
	if m != nil {
		return m.PondType
	}
	return 0
}

type GetMyLotteryRecordTypeResp struct {
	TypeList             []uint32 `protobuf:"varint,1,rep,packed,name=type_list,json=typeList,proto3" json:"type_list"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetMyLotteryRecordTypeResp) Reset()         { *m = GetMyLotteryRecordTypeResp{} }
func (m *GetMyLotteryRecordTypeResp) String() string { return proto.CompactTextString(m) }
func (*GetMyLotteryRecordTypeResp) ProtoMessage()    {}
func (*GetMyLotteryRecordTypeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_revenue_http_logic_907ee343f90f615b, []int{121}
}
func (m *GetMyLotteryRecordTypeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMyLotteryRecordTypeResp.Unmarshal(m, b)
}
func (m *GetMyLotteryRecordTypeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMyLotteryRecordTypeResp.Marshal(b, m, deterministic)
}
func (dst *GetMyLotteryRecordTypeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMyLotteryRecordTypeResp.Merge(dst, src)
}
func (m *GetMyLotteryRecordTypeResp) XXX_Size() int {
	return xxx_messageInfo_GetMyLotteryRecordTypeResp.Size(m)
}
func (m *GetMyLotteryRecordTypeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMyLotteryRecordTypeResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetMyLotteryRecordTypeResp proto.InternalMessageInfo

func (m *GetMyLotteryRecordTypeResp) GetTypeList() []uint32 {
	if m != nil {
		return m.TypeList
	}
	return nil
}

// 获取陪伴排行榜
type GetAccompanyRankListReq struct {
	Index                uint32   `protobuf:"varint,1,opt,name=index,proto3" json:"index"`
	Count                uint32   `protobuf:"varint,2,opt,name=count,proto3" json:"count"`
	RankType             uint32   `protobuf:"varint,3,opt,name=rank_type,json=rankType,proto3" json:"rank_type"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAccompanyRankListReq) Reset()         { *m = GetAccompanyRankListReq{} }
func (m *GetAccompanyRankListReq) String() string { return proto.CompactTextString(m) }
func (*GetAccompanyRankListReq) ProtoMessage()    {}
func (*GetAccompanyRankListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_revenue_http_logic_907ee343f90f615b, []int{122}
}
func (m *GetAccompanyRankListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAccompanyRankListReq.Unmarshal(m, b)
}
func (m *GetAccompanyRankListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAccompanyRankListReq.Marshal(b, m, deterministic)
}
func (dst *GetAccompanyRankListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAccompanyRankListReq.Merge(dst, src)
}
func (m *GetAccompanyRankListReq) XXX_Size() int {
	return xxx_messageInfo_GetAccompanyRankListReq.Size(m)
}
func (m *GetAccompanyRankListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAccompanyRankListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAccompanyRankListReq proto.InternalMessageInfo

func (m *GetAccompanyRankListReq) GetIndex() uint32 {
	if m != nil {
		return m.Index
	}
	return 0
}

func (m *GetAccompanyRankListReq) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *GetAccompanyRankListReq) GetRankType() uint32 {
	if m != nil {
		return m.RankType
	}
	return 0
}

// 用户信息
type AccompanyRankUserInfo struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid"`
	Nickname             string   `protobuf:"bytes,2,opt,name=nickname,proto3" json:"nickname"`
	ChannelId            uint32   `protobuf:"varint,3,opt,name=channel_id,json=channelId,proto3" json:"channel_id"`
	RoomStatus           uint32   `protobuf:"varint,4,opt,name=room_status,json=roomStatus,proto3" json:"room_status"`
	HeadFrame            string   `protobuf:"bytes,5,opt,name=head_frame,json=headFrame,proto3" json:"head_frame"`
	ExtendJson           string   `protobuf:"bytes,6,opt,name=extend_json,json=extendJson,proto3" json:"extend_json"`
	HeadwearKey          string   `protobuf:"bytes,7,opt,name=headwear_key,json=headwearKey,proto3" json:"headwear_key"`
	Account              string   `protobuf:"bytes,8,opt,name=account,proto3" json:"account"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AccompanyRankUserInfo) Reset()         { *m = AccompanyRankUserInfo{} }
func (m *AccompanyRankUserInfo) String() string { return proto.CompactTextString(m) }
func (*AccompanyRankUserInfo) ProtoMessage()    {}
func (*AccompanyRankUserInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_revenue_http_logic_907ee343f90f615b, []int{123}
}
func (m *AccompanyRankUserInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AccompanyRankUserInfo.Unmarshal(m, b)
}
func (m *AccompanyRankUserInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AccompanyRankUserInfo.Marshal(b, m, deterministic)
}
func (dst *AccompanyRankUserInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AccompanyRankUserInfo.Merge(dst, src)
}
func (m *AccompanyRankUserInfo) XXX_Size() int {
	return xxx_messageInfo_AccompanyRankUserInfo.Size(m)
}
func (m *AccompanyRankUserInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_AccompanyRankUserInfo.DiscardUnknown(m)
}

var xxx_messageInfo_AccompanyRankUserInfo proto.InternalMessageInfo

func (m *AccompanyRankUserInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *AccompanyRankUserInfo) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *AccompanyRankUserInfo) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *AccompanyRankUserInfo) GetRoomStatus() uint32 {
	if m != nil {
		return m.RoomStatus
	}
	return 0
}

func (m *AccompanyRankUserInfo) GetHeadFrame() string {
	if m != nil {
		return m.HeadFrame
	}
	return ""
}

func (m *AccompanyRankUserInfo) GetExtendJson() string {
	if m != nil {
		return m.ExtendJson
	}
	return ""
}

func (m *AccompanyRankUserInfo) GetHeadwearKey() string {
	if m != nil {
		return m.HeadwearKey
	}
	return ""
}

func (m *AccompanyRankUserInfo) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

// 陪伴小屋信息
type AccompanyHouse struct {
	ResourceUrl          string   `protobuf:"bytes,1,opt,name=resource_url,json=resourceUrl,proto3" json:"resource_url"`
	Md5                  string   `protobuf:"bytes,2,opt,name=md5,proto3" json:"md5"`
	IsIntoRoom           bool     `protobuf:"varint,3,opt,name=is_into_room,json=isIntoRoom,proto3" json:"is_into_room"`
	HouseName            string   `protobuf:"bytes,4,opt,name=house_name,json=houseName,proto3" json:"house_name"`
	Radio                float32  `protobuf:"fixed32,5,opt,name=radio,proto3" json:"radio"`
	StaticIconUrl        string   `protobuf:"bytes,6,opt,name=static_icon_url,json=staticIconUrl,proto3" json:"static_icon_url"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AccompanyHouse) Reset()         { *m = AccompanyHouse{} }
func (m *AccompanyHouse) String() string { return proto.CompactTextString(m) }
func (*AccompanyHouse) ProtoMessage()    {}
func (*AccompanyHouse) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_revenue_http_logic_907ee343f90f615b, []int{124}
}
func (m *AccompanyHouse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AccompanyHouse.Unmarshal(m, b)
}
func (m *AccompanyHouse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AccompanyHouse.Marshal(b, m, deterministic)
}
func (dst *AccompanyHouse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AccompanyHouse.Merge(dst, src)
}
func (m *AccompanyHouse) XXX_Size() int {
	return xxx_messageInfo_AccompanyHouse.Size(m)
}
func (m *AccompanyHouse) XXX_DiscardUnknown() {
	xxx_messageInfo_AccompanyHouse.DiscardUnknown(m)
}

var xxx_messageInfo_AccompanyHouse proto.InternalMessageInfo

func (m *AccompanyHouse) GetResourceUrl() string {
	if m != nil {
		return m.ResourceUrl
	}
	return ""
}

func (m *AccompanyHouse) GetMd5() string {
	if m != nil {
		return m.Md5
	}
	return ""
}

func (m *AccompanyHouse) GetIsIntoRoom() bool {
	if m != nil {
		return m.IsIntoRoom
	}
	return false
}

func (m *AccompanyHouse) GetHouseName() string {
	if m != nil {
		return m.HouseName
	}
	return ""
}

func (m *AccompanyHouse) GetRadio() float32 {
	if m != nil {
		return m.Radio
	}
	return 0
}

func (m *AccompanyHouse) GetStaticIconUrl() string {
	if m != nil {
		return m.StaticIconUrl
	}
	return ""
}

// 排行榜信息
type AccompanyRankItem struct {
	UidA                 *AccompanyRankUserInfo `protobuf:"bytes,1,opt,name=uidA,proto3" json:"uidA"`
	UidB                 *AccompanyRankUserInfo `protobuf:"bytes,2,opt,name=uidB,proto3" json:"uidB"`
	FellowLevel          uint32                 `protobuf:"varint,3,opt,name=fellow_level,json=fellowLevel,proto3" json:"fellow_level"`
	FellowName           string                 `protobuf:"bytes,4,opt,name=fellow_name,json=fellowName,proto3" json:"fellow_name"`
	House                *AccompanyHouse        `protobuf:"bytes,5,opt,name=house,proto3" json:"house"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *AccompanyRankItem) Reset()         { *m = AccompanyRankItem{} }
func (m *AccompanyRankItem) String() string { return proto.CompactTextString(m) }
func (*AccompanyRankItem) ProtoMessage()    {}
func (*AccompanyRankItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_revenue_http_logic_907ee343f90f615b, []int{125}
}
func (m *AccompanyRankItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AccompanyRankItem.Unmarshal(m, b)
}
func (m *AccompanyRankItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AccompanyRankItem.Marshal(b, m, deterministic)
}
func (dst *AccompanyRankItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AccompanyRankItem.Merge(dst, src)
}
func (m *AccompanyRankItem) XXX_Size() int {
	return xxx_messageInfo_AccompanyRankItem.Size(m)
}
func (m *AccompanyRankItem) XXX_DiscardUnknown() {
	xxx_messageInfo_AccompanyRankItem.DiscardUnknown(m)
}

var xxx_messageInfo_AccompanyRankItem proto.InternalMessageInfo

func (m *AccompanyRankItem) GetUidA() *AccompanyRankUserInfo {
	if m != nil {
		return m.UidA
	}
	return nil
}

func (m *AccompanyRankItem) GetUidB() *AccompanyRankUserInfo {
	if m != nil {
		return m.UidB
	}
	return nil
}

func (m *AccompanyRankItem) GetFellowLevel() uint32 {
	if m != nil {
		return m.FellowLevel
	}
	return 0
}

func (m *AccompanyRankItem) GetFellowName() string {
	if m != nil {
		return m.FellowName
	}
	return ""
}

func (m *AccompanyRankItem) GetHouse() *AccompanyHouse {
	if m != nil {
		return m.House
	}
	return nil
}

// 获取陪伴排行榜
type GetAccompanyRankListResp struct {
	RankList             []*AccompanyRankItem `protobuf:"bytes,1,rep,name=rank_list,json=rankList,proto3" json:"rank_list"`
	NextPageNum          uint32               `protobuf:"varint,2,opt,name=next_page_num,json=nextPageNum,proto3" json:"next_page_num"`
	ServerTime           int64                `protobuf:"varint,3,opt,name=server_time,json=serverTime,proto3" json:"server_time"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *GetAccompanyRankListResp) Reset()         { *m = GetAccompanyRankListResp{} }
func (m *GetAccompanyRankListResp) String() string { return proto.CompactTextString(m) }
func (*GetAccompanyRankListResp) ProtoMessage()    {}
func (*GetAccompanyRankListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_revenue_http_logic_907ee343f90f615b, []int{126}
}
func (m *GetAccompanyRankListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAccompanyRankListResp.Unmarshal(m, b)
}
func (m *GetAccompanyRankListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAccompanyRankListResp.Marshal(b, m, deterministic)
}
func (dst *GetAccompanyRankListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAccompanyRankListResp.Merge(dst, src)
}
func (m *GetAccompanyRankListResp) XXX_Size() int {
	return xxx_messageInfo_GetAccompanyRankListResp.Size(m)
}
func (m *GetAccompanyRankListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAccompanyRankListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAccompanyRankListResp proto.InternalMessageInfo

func (m *GetAccompanyRankListResp) GetRankList() []*AccompanyRankItem {
	if m != nil {
		return m.RankList
	}
	return nil
}

func (m *GetAccompanyRankListResp) GetNextPageNum() uint32 {
	if m != nil {
		return m.NextPageNum
	}
	return 0
}

func (m *GetAccompanyRankListResp) GetServerTime() int64 {
	if m != nil {
		return m.ServerTime
	}
	return 0
}

// 获取我的陪伴排行榜
type ExpandMyAccompanyRankListReq struct {
	RankType             uint32   `protobuf:"varint,1,opt,name=rank_type,json=rankType,proto3" json:"rank_type"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ExpandMyAccompanyRankListReq) Reset()         { *m = ExpandMyAccompanyRankListReq{} }
func (m *ExpandMyAccompanyRankListReq) String() string { return proto.CompactTextString(m) }
func (*ExpandMyAccompanyRankListReq) ProtoMessage()    {}
func (*ExpandMyAccompanyRankListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_revenue_http_logic_907ee343f90f615b, []int{127}
}
func (m *ExpandMyAccompanyRankListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ExpandMyAccompanyRankListReq.Unmarshal(m, b)
}
func (m *ExpandMyAccompanyRankListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ExpandMyAccompanyRankListReq.Marshal(b, m, deterministic)
}
func (dst *ExpandMyAccompanyRankListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ExpandMyAccompanyRankListReq.Merge(dst, src)
}
func (m *ExpandMyAccompanyRankListReq) XXX_Size() int {
	return xxx_messageInfo_ExpandMyAccompanyRankListReq.Size(m)
}
func (m *ExpandMyAccompanyRankListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ExpandMyAccompanyRankListReq.DiscardUnknown(m)
}

var xxx_messageInfo_ExpandMyAccompanyRankListReq proto.InternalMessageInfo

func (m *ExpandMyAccompanyRankListReq) GetRankType() uint32 {
	if m != nil {
		return m.RankType
	}
	return 0
}

// 获取我的陪伴排行榜
type ExpandMyAccompanyRankListResp struct {
	UidA                 *AccompanyRankUserInfo                                     `protobuf:"bytes,1,opt,name=uidA,proto3" json:"uidA"`
	RankList             []*ExpandMyAccompanyRankListResp_ExpandMyAccompanyRankItem `protobuf:"bytes,2,rep,name=rank_list,json=rankList,proto3" json:"rank_list"`
	XXX_NoUnkeyedLiteral struct{}                                                   `json:"-"`
	XXX_unrecognized     []byte                                                     `json:"-"`
	XXX_sizecache        int32                                                      `json:"-"`
}

func (m *ExpandMyAccompanyRankListResp) Reset()         { *m = ExpandMyAccompanyRankListResp{} }
func (m *ExpandMyAccompanyRankListResp) String() string { return proto.CompactTextString(m) }
func (*ExpandMyAccompanyRankListResp) ProtoMessage()    {}
func (*ExpandMyAccompanyRankListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_revenue_http_logic_907ee343f90f615b, []int{128}
}
func (m *ExpandMyAccompanyRankListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ExpandMyAccompanyRankListResp.Unmarshal(m, b)
}
func (m *ExpandMyAccompanyRankListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ExpandMyAccompanyRankListResp.Marshal(b, m, deterministic)
}
func (dst *ExpandMyAccompanyRankListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ExpandMyAccompanyRankListResp.Merge(dst, src)
}
func (m *ExpandMyAccompanyRankListResp) XXX_Size() int {
	return xxx_messageInfo_ExpandMyAccompanyRankListResp.Size(m)
}
func (m *ExpandMyAccompanyRankListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ExpandMyAccompanyRankListResp.DiscardUnknown(m)
}

var xxx_messageInfo_ExpandMyAccompanyRankListResp proto.InternalMessageInfo

func (m *ExpandMyAccompanyRankListResp) GetUidA() *AccompanyRankUserInfo {
	if m != nil {
		return m.UidA
	}
	return nil
}

func (m *ExpandMyAccompanyRankListResp) GetRankList() []*ExpandMyAccompanyRankListResp_ExpandMyAccompanyRankItem {
	if m != nil {
		return m.RankList
	}
	return nil
}

type ExpandMyAccompanyRankListResp_ExpandMyAccompanyRankItem struct {
	UidB                 *AccompanyRankUserInfo `protobuf:"bytes,1,opt,name=uidB,proto3" json:"uidB"`
	FellowLevel          uint32                 `protobuf:"varint,2,opt,name=fellow_level,json=fellowLevel,proto3" json:"fellow_level"`
	FellowName           string                 `protobuf:"bytes,3,opt,name=fellow_name,json=fellowName,proto3" json:"fellow_name"`
	House                *AccompanyHouse        `protobuf:"bytes,4,opt,name=house,proto3" json:"house"`
	RankNow              uint32                 `protobuf:"varint,5,opt,name=rank_now,json=rankNow,proto3" json:"rank_now"`
	RankLast             uint32                 `protobuf:"varint,6,opt,name=rank_last,json=rankLast,proto3" json:"rank_last"`
	GapToRiseRank        uint32                 `protobuf:"varint,7,opt,name=gap_to_rise_rank,json=gapToRiseRank,proto3" json:"gap_to_rise_rank"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *ExpandMyAccompanyRankListResp_ExpandMyAccompanyRankItem) Reset() {
	*m = ExpandMyAccompanyRankListResp_ExpandMyAccompanyRankItem{}
}
func (m *ExpandMyAccompanyRankListResp_ExpandMyAccompanyRankItem) String() string {
	return proto.CompactTextString(m)
}
func (*ExpandMyAccompanyRankListResp_ExpandMyAccompanyRankItem) ProtoMessage() {}
func (*ExpandMyAccompanyRankListResp_ExpandMyAccompanyRankItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_revenue_http_logic_907ee343f90f615b, []int{128, 0}
}
func (m *ExpandMyAccompanyRankListResp_ExpandMyAccompanyRankItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ExpandMyAccompanyRankListResp_ExpandMyAccompanyRankItem.Unmarshal(m, b)
}
func (m *ExpandMyAccompanyRankListResp_ExpandMyAccompanyRankItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ExpandMyAccompanyRankListResp_ExpandMyAccompanyRankItem.Marshal(b, m, deterministic)
}
func (dst *ExpandMyAccompanyRankListResp_ExpandMyAccompanyRankItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ExpandMyAccompanyRankListResp_ExpandMyAccompanyRankItem.Merge(dst, src)
}
func (m *ExpandMyAccompanyRankListResp_ExpandMyAccompanyRankItem) XXX_Size() int {
	return xxx_messageInfo_ExpandMyAccompanyRankListResp_ExpandMyAccompanyRankItem.Size(m)
}
func (m *ExpandMyAccompanyRankListResp_ExpandMyAccompanyRankItem) XXX_DiscardUnknown() {
	xxx_messageInfo_ExpandMyAccompanyRankListResp_ExpandMyAccompanyRankItem.DiscardUnknown(m)
}

var xxx_messageInfo_ExpandMyAccompanyRankListResp_ExpandMyAccompanyRankItem proto.InternalMessageInfo

func (m *ExpandMyAccompanyRankListResp_ExpandMyAccompanyRankItem) GetUidB() *AccompanyRankUserInfo {
	if m != nil {
		return m.UidB
	}
	return nil
}

func (m *ExpandMyAccompanyRankListResp_ExpandMyAccompanyRankItem) GetFellowLevel() uint32 {
	if m != nil {
		return m.FellowLevel
	}
	return 0
}

func (m *ExpandMyAccompanyRankListResp_ExpandMyAccompanyRankItem) GetFellowName() string {
	if m != nil {
		return m.FellowName
	}
	return ""
}

func (m *ExpandMyAccompanyRankListResp_ExpandMyAccompanyRankItem) GetHouse() *AccompanyHouse {
	if m != nil {
		return m.House
	}
	return nil
}

func (m *ExpandMyAccompanyRankListResp_ExpandMyAccompanyRankItem) GetRankNow() uint32 {
	if m != nil {
		return m.RankNow
	}
	return 0
}

func (m *ExpandMyAccompanyRankListResp_ExpandMyAccompanyRankItem) GetRankLast() uint32 {
	if m != nil {
		return m.RankLast
	}
	return 0
}

func (m *ExpandMyAccompanyRankListResp_ExpandMyAccompanyRankItem) GetGapToRiseRank() uint32 {
	if m != nil {
		return m.GapToRiseRank
	}
	return 0
}

func init() {
	proto.RegisterType((*GetDramaDetailReq)(nil), "tt_revenue_http_logic.GetDramaDetailReq")
	proto.RegisterType((*UserProfile)(nil), "tt_revenue_http_logic.UserProfile")
	proto.RegisterType((*GetDramaDetailResp)(nil), "tt_revenue_http_logic.GetDramaDetailResp")
	proto.RegisterType((*PiaRole)(nil), "tt_revenue_http_logic.PiaRole")
	proto.RegisterType((*GetDramaExpansionReq)(nil), "tt_revenue_http_logic.GetDramaExpansionReq")
	proto.RegisterType((*GetDramaExpansionResp)(nil), "tt_revenue_http_logic.GetDramaExpansionResp")
	proto.RegisterType((*PiaAddDramaFeedBackReq)(nil), "tt_revenue_http_logic.PiaAddDramaFeedBackReq")
	proto.RegisterType((*CpGameGodRankMem)(nil), "tt_revenue_http_logic.CpGameGodRankMem")
	proto.RegisterType((*CpGameGodRankInfo)(nil), "tt_revenue_http_logic.CpGameGodRankInfo")
	proto.RegisterType((*GetChannelCpGameGodRankReq)(nil), "tt_revenue_http_logic.GetChannelCpGameGodRankReq")
	proto.RegisterType((*GetChannelCpGameGodRankResp)(nil), "tt_revenue_http_logic.GetChannelCpGameGodRankResp")
	proto.RegisterType((*GetH5CPInfoReq)(nil), "tt_revenue_http_logic.GetH5CPInfoReq")
	proto.RegisterType((*GetH5CPInfoResp)(nil), "tt_revenue_http_logic.GetH5CPInfoResp")
	proto.RegisterType((*CostInfoMaterial)(nil), "tt_revenue_http_logic.CostInfoMaterial")
	proto.RegisterType((*ComposeGiftReq)(nil), "tt_revenue_http_logic.ComposeGiftReq")
	proto.RegisterType((*ComposeGiftResp)(nil), "tt_revenue_http_logic.ComposeGiftResp")
	proto.RegisterType((*ComposeGiftConf)(nil), "tt_revenue_http_logic.ComposeGiftConf")
	proto.RegisterType((*GetComposeGiftConfListReq)(nil), "tt_revenue_http_logic.GetComposeGiftConfListReq")
	proto.RegisterType((*GetComposeGiftConfListResp)(nil), "tt_revenue_http_logic.GetComposeGiftConfListResp")
	proto.RegisterType((*ComposeLog)(nil), "tt_revenue_http_logic.ComposeLog")
	proto.RegisterType((*GetUserComposeLogsReq)(nil), "tt_revenue_http_logic.GetUserComposeLogsReq")
	proto.RegisterType((*GetUserComposeLogsResp)(nil), "tt_revenue_http_logic.GetUserComposeLogsResp")
	proto.RegisterType((*MaterialInfo)(nil), "tt_revenue_http_logic.materialInfo")
	proto.RegisterType((*GetUserMaterialListReq)(nil), "tt_revenue_http_logic.GetUserMaterialListReq")
	proto.RegisterType((*GetUserMaterialListResp)(nil), "tt_revenue_http_logic.GetUserMaterialListResp")
	proto.RegisterType((*GetMagicFragmentRequest)(nil), "tt_revenue_http_logic.GetMagicFragmentRequest")
	proto.RegisterType((*GetMagicFragmentResponse)(nil), "tt_revenue_http_logic.GetMagicFragmentResponse")
	proto.RegisterType((*CheckGiftComposeEntryReq)(nil), "tt_revenue_http_logic.CheckGiftComposeEntryReq")
	proto.RegisterType((*CheckGiftComposeEntryResp)(nil), "tt_revenue_http_logic.CheckGiftComposeEntryResp")
	proto.RegisterType((*CostMaterialInfo)(nil), "tt_revenue_http_logic.CostMaterialInfo")
	proto.RegisterType((*DarkComposeGiftReq)(nil), "tt_revenue_http_logic.DarkComposeGiftReq")
	proto.RegisterType((*DarkComposeGiftResp)(nil), "tt_revenue_http_logic.DarkComposeGiftResp")
	proto.RegisterType((*DarkComposeGiftConf)(nil), "tt_revenue_http_logic.DarkComposeGiftConf")
	proto.RegisterType((*GetGiftComposeHomePageInfoReq)(nil), "tt_revenue_http_logic.GetGiftComposeHomePageInfoReq")
	proto.RegisterType((*GetGiftComposeHomePageInfoResp)(nil), "tt_revenue_http_logic.GetGiftComposeHomePageInfoResp")
	proto.RegisterType((*DarkComposeLog)(nil), "tt_revenue_http_logic.DarkComposeLog")
	proto.RegisterType((*GetUserDarkComposeLogsReq)(nil), "tt_revenue_http_logic.GetUserDarkComposeLogsReq")
	proto.RegisterType((*GetUserDarkComposeLogsResp)(nil), "tt_revenue_http_logic.GetUserDarkComposeLogsResp")
	proto.RegisterType((*DarkMaterialInfo)(nil), "tt_revenue_http_logic.DarkMaterialInfo")
	proto.RegisterType((*GetUserGift2MaterialsListReq)(nil), "tt_revenue_http_logic.GetUserGift2MaterialsListReq")
	proto.RegisterType((*GetUserGift2MaterialsListResp)(nil), "tt_revenue_http_logic.GetUserGift2MaterialsListResp")
	proto.RegisterType((*GetAllUserMaterialListReq)(nil), "tt_revenue_http_logic.GetAllUserMaterialListReq")
	proto.RegisterType((*GetAllUserMaterialListResp)(nil), "tt_revenue_http_logic.GetAllUserMaterialListResp")
	proto.RegisterType((*DarkGiftBonusLog)(nil), "tt_revenue_http_logic.DarkGiftBonusLog")
	proto.RegisterType((*GetDarkGiftBonusLogsReq)(nil), "tt_revenue_http_logic.GetDarkGiftBonusLogsReq")
	proto.RegisterType((*GetDarkGiftBonusLogsResp)(nil), "tt_revenue_http_logic.GetDarkGiftBonusLogsResp")
	proto.RegisterType((*GetFellowInfoReq)(nil), "tt_revenue_http_logic.GetFellowInfoReq")
	proto.RegisterType((*FellowTask)(nil), "tt_revenue_http_logic.FellowTask")
	proto.RegisterType((*FellowLevelConfig)(nil), "tt_revenue_http_logic.FellowLevelConfig")
	proto.RegisterType((*OnAccompanyRankInfo)(nil), "tt_revenue_http_logic.OnAccompanyRankInfo")
	proto.RegisterType((*GradingInfo)(nil), "tt_revenue_http_logic.GradingInfo")
	proto.RegisterType((*GetFellowInfoResp)(nil), "tt_revenue_http_logic.GetFellowInfoResp")
	proto.RegisterType((*DatingGameRecord)(nil), "tt_revenue_http_logic.DatingGameRecord")
	proto.RegisterType((*PiecesSummaryData)(nil), "tt_revenue_http_logic.PiecesSummaryData")
	proto.RegisterType((*WeddingCertificate)(nil), "tt_revenue_http_logic.WeddingCertificate")
	proto.RegisterType((*PiecesData)(nil), "tt_revenue_http_logic.PiecesData")
	proto.RegisterType((*GetPiecesInfoReq)(nil), "tt_revenue_http_logic.GetPiecesInfoReq")
	proto.RegisterType((*GetPiecesInfoResp)(nil), "tt_revenue_http_logic.GetPiecesInfoResp")
	proto.RegisterType((*GetCPStrengthHistoryReq)(nil), "tt_revenue_http_logic.GetCPStrengthHistoryReq")
	proto.RegisterType((*StrengthHistory)(nil), "tt_revenue_http_logic.StrengthHistory")
	proto.RegisterType((*GetCPStrengthHistoryResp)(nil), "tt_revenue_http_logic.GetCPStrengthHistoryResp")
	proto.RegisterType((*UnboundFellowReq)(nil), "tt_revenue_http_logic.UnboundFellowReq")
	proto.RegisterType((*UnboundFellowResp)(nil), "tt_revenue_http_logic.UnboundFellowResp")
	proto.RegisterType((*CancelUnboundFellowReq)(nil), "tt_revenue_http_logic.CancelUnboundFellowReq")
	proto.RegisterType((*CancelUnboundFellowResp)(nil), "tt_revenue_http_logic.CancelUnboundFellowResp")
	proto.RegisterType((*DirectUnboundFellowReq)(nil), "tt_revenue_http_logic.DirectUnboundFellowReq")
	proto.RegisterType((*DirectUnboundFellowResp)(nil), "tt_revenue_http_logic.DirectUnboundFellowResp")
	proto.RegisterType((*SetNameplateInUseReq)(nil), "tt_revenue_http_logic.SetNameplateInUseReq")
	proto.RegisterType((*SetNameplateInUseResp)(nil), "tt_revenue_http_logic.SetNameplateInUseResp")
	proto.RegisterType((*ChangeFellowBindTypeReq)(nil), "tt_revenue_http_logic.ChangeFellowBindTypeReq")
	proto.RegisterType((*ChangeFellowBindTypeResp)(nil), "tt_revenue_http_logic.ChangeFellowBindTypeResp")
	proto.RegisterType((*GetHuntMonsterPropsInfoReq)(nil), "tt_revenue_http_logic.GetHuntMonsterPropsInfoReq")
	proto.RegisterType((*GetHuntMonsterPropsInfoResp)(nil), "tt_revenue_http_logic.GetHuntMonsterPropsInfoResp")
	proto.RegisterType((*MaskedPkScore)(nil), "tt_revenue_http_logic.MaskedPkScore")
	proto.RegisterType((*MaskedPkScoreLog)(nil), "tt_revenue_http_logic.MaskedPkScoreLog")
	proto.RegisterType((*GetMaskedPkScoreLogReq)(nil), "tt_revenue_http_logic.GetMaskedPkScoreLogReq")
	proto.RegisterType((*GetMaskedPkScoreLogResp)(nil), "tt_revenue_http_logic.GetMaskedPkScoreLogResp")
	proto.RegisterType((*GetMyLotteryRecordReq)(nil), "tt_revenue_http_logic.GetMyLotteryRecordReq")
	proto.RegisterType((*PeripheralProduct)(nil), "tt_revenue_http_logic.PeripheralProduct")
	proto.RegisterType((*PeripheralBriefInfo)(nil), "tt_revenue_http_logic.PeripheralBriefInfo")
	proto.RegisterType((*GetPeripheralBriefInfoReq)(nil), "tt_revenue_http_logic.GetPeripheralBriefInfoReq")
	proto.RegisterType((*GetPeripheralBriefInfoResp)(nil), "tt_revenue_http_logic.GetPeripheralBriefInfoResp")
	proto.RegisterType((*GetPeripheralProductDetailReq)(nil), "tt_revenue_http_logic.GetPeripheralProductDetailReq")
	proto.RegisterType((*GetPeripheralProductDetailResp)(nil), "tt_revenue_http_logic.GetPeripheralProductDetailResp")
	proto.RegisterType((*SinglePeripheralProductOrder)(nil), "tt_revenue_http_logic.SinglePeripheralProductOrder")
	proto.RegisterType((*PeripheralPayReq)(nil), "tt_revenue_http_logic.PeripheralPayReq")
	proto.RegisterType((*PeripheralPayResp)(nil), "tt_revenue_http_logic.PeripheralPayResp")
	proto.RegisterType((*PeripheralPayFaceCheckReq)(nil), "tt_revenue_http_logic.PeripheralPayFaceCheckReq")
	proto.RegisterType((*PeripheralPayFaceCheckResp)(nil), "tt_revenue_http_logic.PeripheralPayFaceCheckResp")
	proto.RegisterType((*OrderPeripheralProductReq)(nil), "tt_revenue_http_logic.OrderPeripheralProductReq")
	proto.RegisterType((*OrderPeripheralProductResp)(nil), "tt_revenue_http_logic.OrderPeripheralProductResp")
	proto.RegisterType((*PeripheralProductOrderInfo)(nil), "tt_revenue_http_logic.PeripheralProductOrderInfo")
	proto.RegisterType((*GetPeripheralProductOrdersReq)(nil), "tt_revenue_http_logic.GetPeripheralProductOrdersReq")
	proto.RegisterType((*GetPeripheralProductOrdersResp)(nil), "tt_revenue_http_logic.GetPeripheralProductOrdersResp")
	proto.RegisterType((*PeripheralOrderAddr)(nil), "tt_revenue_http_logic.PeripheralOrderAddr")
	proto.RegisterType((*GetUserPeripheralOrderAddrReq)(nil), "tt_revenue_http_logic.GetUserPeripheralOrderAddrReq")
	proto.RegisterType((*GetUserPeripheralOrderAddrResp)(nil), "tt_revenue_http_logic.GetUserPeripheralOrderAddrResp")
	proto.RegisterType((*SetUserPeripheralOrderAddrReq)(nil), "tt_revenue_http_logic.SetUserPeripheralOrderAddrReq")
	proto.RegisterType((*SetUserPeripheralOrderAddrResp)(nil), "tt_revenue_http_logic.SetUserPeripheralOrderAddrResp")
	proto.RegisterType((*AwardInfo)(nil), "tt_revenue_http_logic.AwardInfo")
	proto.RegisterType((*ReturnConsumeAwardInfo)(nil), "tt_revenue_http_logic.ReturnConsumeAwardInfo")
	proto.RegisterType((*GetReturnAwardInfoResp)(nil), "tt_revenue_http_logic.GetReturnAwardInfoResp")
	proto.RegisterType((*CheckEntryReq)(nil), "tt_revenue_http_logic.CheckEntryReq")
	proto.RegisterType((*CheckEntryResp)(nil), "tt_revenue_http_logic.CheckEntryResp")
	proto.RegisterType((*GetPreInfoReq)(nil), "tt_revenue_http_logic.GetPreInfoReq")
	proto.RegisterType((*GetPreInfoResp)(nil), "tt_revenue_http_logic.GetPreInfoResp")
	proto.RegisterType((*GetAwardPreviewReq)(nil), "tt_revenue_http_logic.GetAwardPreviewReq")
	proto.RegisterType((*GetAwardPreviewResp)(nil), "tt_revenue_http_logic.GetAwardPreviewResp")
	proto.RegisterType((*AwardPreviewInfo)(nil), "tt_revenue_http_logic.AwardPreviewInfo")
	proto.RegisterType((*GloryMagicAwardInfo)(nil), "tt_revenue_http_logic.GloryMagicAwardInfo")
	proto.RegisterType((*GetWinningCarouselReq)(nil), "tt_revenue_http_logic.GetWinningCarouselReq")
	proto.RegisterType((*WinningInfo)(nil), "tt_revenue_http_logic.WinningInfo")
	proto.RegisterType((*GetWinningCarouselResp)(nil), "tt_revenue_http_logic.GetWinningCarouselResp")
	proto.RegisterType((*LotteryReq)(nil), "tt_revenue_http_logic.LotteryReq")
	proto.RegisterType((*LotteryResp)(nil), "tt_revenue_http_logic.LotteryResp")
	proto.RegisterType((*GetLotteryRecordReq)(nil), "tt_revenue_http_logic.GetLotteryRecordReq")
	proto.RegisterType((*LotteryRecord)(nil), "tt_revenue_http_logic.LotteryRecord")
	proto.RegisterType((*GetMyLotteryRecordResp)(nil), "tt_revenue_http_logic.GetMyLotteryRecordResp")
	proto.RegisterType((*GetPresentWeekCardEntryReq)(nil), "tt_revenue_http_logic.GetPresentWeekCardEntryReq")
	proto.RegisterType((*GetPresentWeekCardEntryResp)(nil), "tt_revenue_http_logic.GetPresentWeekCardEntryResp")
	proto.RegisterType((*GetMyLotteryRecordTypeReq)(nil), "tt_revenue_http_logic.GetMyLotteryRecordTypeReq")
	proto.RegisterType((*GetMyLotteryRecordTypeResp)(nil), "tt_revenue_http_logic.GetMyLotteryRecordTypeResp")
	proto.RegisterType((*GetAccompanyRankListReq)(nil), "tt_revenue_http_logic.GetAccompanyRankListReq")
	proto.RegisterType((*AccompanyRankUserInfo)(nil), "tt_revenue_http_logic.AccompanyRankUserInfo")
	proto.RegisterType((*AccompanyHouse)(nil), "tt_revenue_http_logic.AccompanyHouse")
	proto.RegisterType((*AccompanyRankItem)(nil), "tt_revenue_http_logic.AccompanyRankItem")
	proto.RegisterType((*GetAccompanyRankListResp)(nil), "tt_revenue_http_logic.GetAccompanyRankListResp")
	proto.RegisterType((*ExpandMyAccompanyRankListReq)(nil), "tt_revenue_http_logic.ExpandMyAccompanyRankListReq")
	proto.RegisterType((*ExpandMyAccompanyRankListResp)(nil), "tt_revenue_http_logic.ExpandMyAccompanyRankListResp")
	proto.RegisterType((*ExpandMyAccompanyRankListResp_ExpandMyAccompanyRankItem)(nil), "tt_revenue_http_logic.ExpandMyAccompanyRankListResp.ExpandMyAccompanyRankItem")
	proto.RegisterEnum("tt_revenue_http_logic.FeedbackType", FeedbackType_name, FeedbackType_value)
	proto.RegisterEnum("tt_revenue_http_logic.MaterialType", MaterialType_name, MaterialType_value)
	proto.RegisterEnum("tt_revenue_http_logic.FellowType", FellowType_name, FellowType_value)
	proto.RegisterEnum("tt_revenue_http_logic.FellowBindType", FellowBindType_name, FellowBindType_value)
	proto.RegisterEnum("tt_revenue_http_logic.PondType", PondType_name, PondType_value)
	proto.RegisterEnum("tt_revenue_http_logic.UserRoomStatus", UserRoomStatus_name, UserRoomStatus_value)
	proto.RegisterEnum("tt_revenue_http_logic.LotteryResp_WINNING_TYPE", LotteryResp_WINNING_TYPE_name, LotteryResp_WINNING_TYPE_value)
}

func init() {
	proto.RegisterFile("tt-revenue-http-logic.proto", fileDescriptor_tt_revenue_http_logic_907ee343f90f615b)
}

var fileDescriptor_tt_revenue_http_logic_907ee343f90f615b = []byte{
	// 6816 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xd4, 0x7c, 0x4b, 0x6c, 0x24, 0xc9,
	0x71, 0xe8, 0x56, 0xf3, 0xd7, 0x1d, 0xcd, 0x26, 0x9b, 0x45, 0xce, 0x0c, 0x39, 0x5f, 0x4e, 0xed,
	0x6f, 0x34, 0xda, 0xd9, 0xa7, 0xdd, 0xd5, 0x3e, 0x48, 0x58, 0x61, 0xb5, 0x64, 0xf3, 0x33, 0xad,
	0x25, 0x9b, 0x54, 0x91, 0xdc, 0xc1, 0xae, 0x80, 0x57, 0x2f, 0xa7, 0x2a, 0xd9, 0x2c, 0xb1, 0xeb,
	0xb3, 0x95, 0xd5, 0xfc, 0x08, 0xd0, 0xe1, 0x7d, 0x24, 0xe0, 0x01, 0x7a, 0xb2, 0x64, 0xc3, 0x80,
	0xaf, 0xbe, 0x18, 0x30, 0x60, 0x43, 0x36, 0x74, 0xb0, 0xa1, 0x8b, 0x7c, 0xd0, 0xc1, 0x27, 0x0b,
	0x30, 0x60, 0xc0, 0x17, 0x1b, 0x30, 0x60, 0xc0, 0x80, 0x6f, 0xba, 0xf9, 0x68, 0x44, 0x64, 0x56,
	0x55, 0x56, 0x7f, 0x38, 0x33, 0xbb, 0x2b, 0x1b, 0xbe, 0xcc, 0x74, 0x46, 0x46, 0x46, 0x66, 0xc6,
	0x2f, 0x23, 0x23, 0xa3, 0x08, 0xb7, 0xd2, 0xf4, 0x51, 0xc2, 0xcf, 0x78, 0xd8, 0xe7, 0x8f, 0x4e,
	0xd2, 0x34, 0x7e, 0xd4, 0x8b, 0xba, 0xbe, 0xfb, 0x66, 0x9c, 0x44, 0x69, 0x64, 0x5e, 0x4b, 0x53,
	0x47, 0x75, 0x3a, 0xd8, 0xe9, 0x50, 0xa7, 0xf5, 0x26, 0x2c, 0x6c, 0xf3, 0x74, 0x23, 0x61, 0x01,
	0xdb, 0xe0, 0x29, 0xf3, 0x7b, 0x36, 0xff, 0xd4, 0x5c, 0x81, 0xaa, 0x87, 0x10, 0xc7, 0xf7, 0x96,
	0x8d, 0x55, 0xe3, 0x41, 0xc3, 0x9e, 0xa1, 0x76, 0xdb, 0xb3, 0xfe, 0xce, 0x80, 0xfa, 0x91, 0xe0,
	0xc9, 0x7e, 0x12, 0x1d, 0xfb, 0x3d, 0x6e, 0x36, 0x61, 0xa2, 0x9f, 0x63, 0xe1, 0x4f, 0x73, 0x19,
	0x66, 0x98, 0xeb, 0x46, 0xfd, 0x30, 0x5d, 0xae, 0xac, 0x1a, 0x0f, 0x6a, 0x76, 0xd6, 0x34, 0x6f,
	0x42, 0x35, 0xf4, 0xdd, 0xd3, 0x90, 0x05, 0x7c, 0x79, 0x82, 0xba, 0xf2, 0xb6, 0xf9, 0x32, 0x34,
	0x14, 0x9a, 0xc3, 0x7a, 0x3e, 0x13, 0xcb, 0x93, 0x84, 0x30, 0xab, 0x80, 0x6b, 0x08, 0xc3, 0xc9,
	0x04, 0xbf, 0x58, 0x9e, 0x92, 0x93, 0x09, 0x7e, 0x61, 0xae, 0xc2, 0xec, 0x09, 0x67, 0x9e, 0xe3,
	0x07, 0x5d, 0x27, 0xf0, 0xde, 0x5d, 0x9e, 0xa6, 0x51, 0x80, 0xb0, 0x76, 0xd0, 0xdd, 0xf5, 0xde,
	0x35, 0x5f, 0x85, 0x79, 0xc2, 0xf0, 0x2e, 0x73, 0xa4, 0x19, 0x49, 0x1a, 0xc1, 0x1b, 0x97, 0x12,
	0xcd, 0xfa, 0x37, 0x03, 0xcc, 0x41, 0x46, 0x88, 0xd8, 0x5c, 0x82, 0xa9, 0xd4, 0x4f, 0x7b, 0x9c,
	0x36, 0x58, 0xb3, 0x65, 0xc3, 0xbc, 0x05, 0x35, 0x37, 0x3a, 0xe3, 0x89, 0xd3, 0x4f, 0x7a, 0x6a,
	0x93, 0x55, 0x02, 0x1c, 0x25, 0x3d, 0xf3, 0x3a, 0x4c, 0xb3, 0x7e, 0x7a, 0x12, 0x25, 0x6a, 0x8f,
	0xaa, 0x85, 0x4c, 0x0d, 0x58, 0x8f, 0x3b, 0x6e, 0x98, 0xd2, 0xe6, 0x1a, 0xf6, 0x0c, 0xb6, 0x5b,
	0x61, 0x6a, 0xde, 0x01, 0x38, 0xe6, 0x79, 0xa7, 0xdc, 0x5e, 0x4d, 0x42, 0xb0, 0x7b, 0x05, 0xaa,
	0xe7, 0x51, 0xe2, 0x51, 0xe7, 0xb4, 0x1c, 0x89, 0x6d, 0xec, 0x32, 0x61, 0x32, 0x65, 0x5d, 0xb1,
	0x3c, 0xb3, 0x3a, 0xf1, 0xa0, 0x66, 0xd3, 0x6f, 0x5c, 0xb3, 0xc7, 0x85, 0x9b, 0x2c, 0x57, 0xe5,
	0x9a, 0xa9, 0x81, 0x62, 0x71, 0xa3, 0x30, 0xe5, 0x61, 0xba, 0x5c, 0x93, 0x62, 0x51, 0x4d, 0xeb,
	0x2f, 0x0d, 0x98, 0xd9, 0xf7, 0x99, 0x1d, 0xf5, 0xb8, 0x39, 0x07, 0x15, 0x25, 0xcd, 0x9a, 0x5d,
	0xf1, 0x3d, 0xa4, 0x4f, 0xe2, 0x92, 0x9b, 0xa4, 0xdf, 0x99, 0x14, 0x26, 0x0a, 0x29, 0xe0, 0x96,
	0xcf, 0x58, 0xca, 0x12, 0x25, 0x35, 0xd5, 0x32, 0x2d, 0x98, 0xf5, 0xc3, 0x34, 0x89, 0xbc, 0xbe,
	0x9b, 0xfa, 0x51, 0x48, 0x3b, 0xab, 0xd9, 0x25, 0x18, 0xae, 0xd6, 0x8d, 0x7a, 0x51, 0xa2, 0x44,
	0x27, 0x1b, 0xe6, 0xab, 0x30, 0xe7, 0xf9, 0xac, 0x17, 0x75, 0xfb, 0xdc, 0x49, 0x58, 0xea, 0x47,
	0x24, 0x34, 0xc3, 0x6e, 0x64, 0x50, 0x1b, 0x81, 0xd6, 0x5b, 0xb0, 0x94, 0x09, 0x6d, 0xf3, 0x22,
	0x66, 0xa1, 0xf0, 0xa3, 0xf0, 0x19, 0x0a, 0xfc, 0x53, 0x03, 0xae, 0x8d, 0x18, 0x23, 0xe2, 0x9c,
	0x97, 0xc6, 0x28, 0x5e, 0x56, 0xc6, 0xf0, 0x72, 0xa2, 0xc4, 0x4b, 0xf3, 0xab, 0x30, 0x95, 0x44,
	0x3d, 0x8e, 0xea, 0x3b, 0xf1, 0xa0, 0xfe, 0xf6, 0xdd, 0x37, 0x47, 0x5a, 0xdd, 0x9b, 0x8a, 0xdd,
	0xb6, 0x44, 0xb6, 0x7e, 0x61, 0xc0, 0xf5, 0x7d, 0x9f, 0xad, 0x79, 0x1e, 0x2d, 0x6b, 0x8b, 0x73,
	0x6f, 0x9d, 0xb9, 0xa7, 0xb8, 0x93, 0xc7, 0xd0, 0x38, 0xe6, 0xdc, 0x7b, 0xca, 0xdc, 0x53, 0x27,
	0xbd, 0x8c, 0xa5, 0x22, 0xce, 0xbd, 0xfd, 0xf2, 0x18, 0xc2, 0x5b, 0x0a, 0xf7, 0xf0, 0x32, 0xe6,
	0xf6, 0xec, 0xb1, 0xd6, 0xd2, 0x17, 0x5d, 0x29, 0x2f, 0xfa, 0x3e, 0xcc, 0xc6, 0xbe, 0x9b, 0xf6,
	0x13, 0xee, 0xf4, 0x7c, 0x81, 0x7b, 0x42, 0x06, 0xd4, 0x15, 0x6c, 0xc7, 0x17, 0x69, 0x89, 0xa1,
	0x93, 0x65, 0x86, 0xfe, 0xc8, 0x80, 0x66, 0x2b, 0xde, 0x66, 0x01, 0xdf, 0x8e, 0x3c, 0x9b, 0x85,
	0xa7, 0xbb, 0x3c, 0xf8, 0xc2, 0xdc, 0xc2, 0x0a, 0x54, 0x05, 0x0f, 0x3d, 0xe7, 0x8c, 0xf5, 0xb2,
	0x79, 0xb1, 0xfd, 0x11, 0xeb, 0x99, 0xd7, 0x60, 0xda, 0x17, 0x4e, 0x70, 0x16, 0x93, 0x5a, 0x55,
	0xed, 0x29, 0x5f, 0xec, 0x9e, 0xc5, 0xd6, 0x9f, 0x18, 0xb0, 0x50, 0x5a, 0x4e, 0x3b, 0x3c, 0x8e,
	0xcc, 0x75, 0xa8, 0x06, 0x3c, 0x90, 0xdb, 0x33, 0x48, 0x34, 0xaf, 0x8f, 0xe1, 0xe0, 0xe0, 0x56,
	0xec, 0x99, 0x80, 0x07, 0xc4, 0x83, 0x25, 0x98, 0x3a, 0x63, 0xbd, 0xbe, 0x34, 0x86, 0x86, 0x2d,
	0x1b, 0xa8, 0x35, 0x09, 0x0b, 0x4f, 0x95, 0x39, 0xd0, 0x6f, 0xd2, 0x24, 0x3f, 0xe0, 0x6a, 0xc5,
	0xf4, 0x1b, 0x97, 0xfb, 0xb4, 0x4b, 0x0e, 0x43, 0x5a, 0xc1, 0xd4, 0xd3, 0xee, 0x51, 0xd2, 0xb3,
	0xde, 0x83, 0x9b, 0xdb, 0x3c, 0x6d, 0x9d, 0xb0, 0x30, 0xe4, 0xbd, 0xd2, 0xdc, 0x28, 0xfd, 0x3b,
	0x00, 0xae, 0xec, 0x2a, 0x34, 0xb9, 0xa6, 0x20, 0x6d, 0xcf, 0xfa, 0x0e, 0xdc, 0x1a, 0x3b, 0x58,
	0xc4, 0xe6, 0x37, 0x60, 0x52, 0xdb, 0xf0, 0x83, 0xe7, 0xd9, 0x30, 0x32, 0xcb, 0xa6, 0x51, 0xd6,
	0x16, 0xcc, 0x6d, 0xf3, 0xf4, 0xf1, 0xbb, 0xad, 0x7d, 0x02, 0xf2, 0x4f, 0x71, 0x0b, 0xc1, 0xa5,
	0x53, 0xc8, 0x75, 0x2a, 0xb8, 0x3c, 0xf2, 0x3d, 0xe9, 0xbd, 0x7a, 0xbd, 0xe8, 0x9c, 0xba, 0x2a,
	0x99, 0xf7, 0x42, 0xc8, 0x91, 0xef, 0x59, 0xbf, 0xa9, 0xc0, 0x7c, 0x89, 0x90, 0x88, 0xc7, 0x51,
	0x5a, 0x85, 0xd9, 0xe0, 0xd2, 0x41, 0xe1, 0x3b, 0x9a, 0xd7, 0x81, 0xe0, 0xb2, 0xe3, 0xbb, 0xa7,
	0x1d, 0xd4, 0x87, 0x1b, 0x30, 0x13, 0x5c, 0x3a, 0xbe, 0x1b, 0x85, 0x99, 0x77, 0x0d, 0x2e, 0xdb,
	0x6e, 0x14, 0x0e, 0x2c, 0x62, 0x72, 0x60, 0x11, 0xe6, 0x03, 0x68, 0xaa, 0xee, 0x82, 0xba, 0x94,
	0xc3, 0x9c, 0x84, 0xe7, 0x33, 0xdc, 0x83, 0xba, 0xc2, 0xa4, 0x59, 0xd4, 0x81, 0x22, 0x41, 0xd9,
	0x4c, 0x6e, 0x3f, 0x49, 0x78, 0x98, 0x3a, 0xbd, 0x33, 0x72, 0x4b, 0x28, 0x13, 0x09, 0xd9, 0x39,
	0x33, 0xbf, 0x04, 0xcd, 0xac, 0x5b, 0xa4, 0x09, 0x0f, 0xbb, 0xe9, 0x09, 0x39, 0xe2, 0x86, 0x3d,
	0xaf, 0xe0, 0x07, 0x0a, 0x6c, 0xbe, 0x09, 0x8b, 0x05, 0xa5, 0x02, 0xbb, 0x46, 0xd8, 0x0b, 0x39,
	0xc9, 0x1c, 0xff, 0x01, 0x34, 0x43, 0x7e, 0x51, 0x46, 0x06, 0x42, 0x9e, 0x43, 0x78, 0x81, 0x69,
	0x79, 0xd0, 0x6c, 0x45, 0x22, 0x45, 0x7e, 0xef, 0xb2, 0x94, 0x27, 0x3e, 0xa3, 0x73, 0xa9, 0xeb,
	0x1f, 0xa7, 0xed, 0x8c, 0xe7, 0xaa, 0x85, 0x86, 0x89, 0xbf, 0x3a, 0xfd, 0x40, 0xc9, 0x2e, 0x6b,
	0x9a, 0x77, 0x01, 0xfa, 0x82, 0x27, 0xed, 0x94, 0x07, 0x6d, 0x4f, 0x29, 0xb8, 0x06, 0xb1, 0x7e,
	0x5a, 0x81, 0xb9, 0x56, 0x14, 0xc4, 0x91, 0xe0, 0xdb, 0xfe, 0x71, 0x8a, 0x2a, 0xa2, 0xd9, 0x7d,
	0x4d, 0xda, 0xfd, 0x2a, 0xd4, 0xc5, 0x65, 0x98, 0x9e, 0xf0, 0xd4, 0x77, 0xdb, 0x99, 0x7a, 0xe8,
	0x20, 0x1c, 0x13, 0xf6, 0x83, 0xec, 0x3c, 0x09, 0xfb, 0x81, 0xb9, 0x06, 0x33, 0x81, 0x7f, 0x81,
	0x46, 0xa7, 0xfc, 0xe8, 0x58, 0x63, 0x1d, 0xd8, 0xa4, 0x9d, 0x8d, 0x43, 0x13, 0x74, 0x23, 0x91,
	0x1d, 0xa6, 0xf4, 0x1b, 0x95, 0x27, 0x12, 0xd2, 0x8b, 0x4a, 0xb1, 0x4e, 0x47, 0x82, 0x5c, 0x63,
	0x13, 0x26, 0x58, 0x1c, 0xab, 0xb8, 0x00, 0x7f, 0xe2, 0x09, 0x1f, 0xb0, 0xe4, 0x94, 0xa7, 0x68,
	0x77, 0xf2, 0x1c, 0xad, 0x4a, 0x40, 0xdb, 0x43, 0x87, 0x15, 0xf7, 0x58, 0x7a, 0x1c, 0x25, 0x81,
	0x3a, 0x4b, 0xf3, 0xb6, 0xb5, 0x00, 0xf3, 0x25, 0x96, 0x88, 0xd8, 0xfa, 0x59, 0xa5, 0x04, 0x6b,
	0x45, 0xe1, 0xf1, 0x20, 0x57, 0x8c, 0x61, 0xae, 0xbc, 0x02, 0x8d, 0xbc, 0x89, 0x8b, 0x54, 0x9c,
	0x2b, 0x03, 0x4b, 0x58, 0x9d, 0xc2, 0x81, 0x96, 0x81, 0xe6, 0x6b, 0x30, 0x97, 0x03, 0xf6, 0x13,
	0xdf, 0xcd, 0x3c, 0xd3, 0x00, 0x14, 0xcf, 0xeb, 0x1c, 0x72, 0x94, 0x7b, 0xaa, 0x12, 0x0c, 0xbd,
	0xa0, 0x1f, 0x7a, 0xfc, 0x42, 0x45, 0x22, 0xb2, 0x81, 0xca, 0xd5, 0x8f, 0x0f, 0xd1, 0xe7, 0x49,
	0x83, 0x50, 0x2d, 0x64, 0x95, 0x17, 0x9d, 0x87, 0xd4, 0x23, 0xad, 0x20, 0x6f, 0xa3, 0xe2, 0x1d,
	0xfb, 0xb2, 0x4b, 0xaa, 0x7c, 0xd6, 0xb4, 0x1e, 0xc1, 0x0a, 0xfa, 0xb5, 0x32, 0xcf, 0x50, 0xac,
	0x23, 0x55, 0xcc, 0xfa, 0x1f, 0xd2, 0x87, 0x8e, 0x42, 0x17, 0xb1, 0xf9, 0x01, 0x9d, 0x7b, 0xc7,
	0xbe, 0x3a, 0xd9, 0xeb, 0x6f, 0xbf, 0x36, 0x56, 0x99, 0x4a, 0x04, 0xec, 0x6c, 0x98, 0xf5, 0xbb,
	0x15, 0x00, 0xd5, 0xb9, 0x13, 0x75, 0x71, 0xdd, 0x51, 0xe2, 0xf1, 0xa4, 0x9d, 0x2d, 0x22, 0x6b,
	0x96, 0x65, 0xe6, 0x07, 0x23, 0x64, 0x86, 0xfb, 0xfe, 0xed, 0xc9, 0x0c, 0x6d, 0x58, 0x2a, 0x7c,
	0x09, 0x36, 0x24, 0xd7, 0xe9, 0x11, 0x72, 0xbd, 0x09, 0x55, 0x34, 0x92, 0x0d, 0x2e, 0x5c, 0x65,
	0x08, 0x79, 0x3b, 0x37, 0xa6, 0x6a, 0x61, 0x4c, 0xd6, 0x77, 0x28, 0x8c, 0xc2, 0xab, 0x40, 0xc1,
	0x1a, 0x31, 0xda, 0x05, 0x5c, 0x87, 0xe9, 0xe8, 0xf8, 0x58, 0xf0, 0x54, 0xf1, 0x43, 0xb5, 0x90,
	0x91, 0x31, 0xeb, 0xf2, 0x4e, 0x6e, 0xfc, 0x59, 0xd3, 0xfa, 0x14, 0xae, 0x8f, 0x22, 0x2e, 0x62,
	0xf3, 0x5d, 0x98, 0xec, 0x45, 0xb9, 0x28, 0xef, 0x5f, 0x2d, 0xca, 0x9d, 0xa8, 0x6b, 0x13, 0x3a,
	0xda, 0xdb, 0x09, 0x13, 0x1d, 0x7e, 0x91, 0xee, 0xb3, 0xae, 0x94, 0x4b, 0xd5, 0xd6, 0x41, 0xd6,
	0x3f, 0x1b, 0x30, 0x1b, 0x28, 0x37, 0x42, 0x21, 0xc3, 0x8b, 0xfb, 0xcb, 0x9b, 0x50, 0xa5, 0x9f,
	0x5a, 0x20, 0x93, 0xb5, 0xcd, 0xdb, 0x50, 0xc3, 0xdf, 0xba, 0x24, 0x0b, 0x40, 0x46, 0xb3, 0xb0,
	0xb9, 0xac, 0x39, 0xc6, 0xdc, 0xca, 0x9e, 0x79, 0x66, 0xd0, 0x33, 0xeb, 0xa6, 0x55, 0x2d, 0x9b,
	0xd6, 0xc3, 0x9c, 0xb3, 0x99, 0xcf, 0x1c, 0x6f, 0x57, 0x87, 0x70, 0x63, 0x24, 0xae, 0x88, 0xcd,
	0xaf, 0xc3, 0x14, 0xae, 0x30, 0x93, 0xc3, 0xb8, 0x70, 0x54, 0x67, 0xa8, 0x2d, 0x47, 0x58, 0x2b,
	0x44, 0x75, 0x97, 0x75, 0x7d, 0x77, 0x2b, 0x61, 0xdd, 0x80, 0x87, 0x38, 0x7d, 0x9f, 0x8b, 0xd4,
	0xfa, 0x79, 0x05, 0x96, 0x87, 0xfb, 0x44, 0x1c, 0x85, 0x42, 0x1e, 0xcc, 0x0a, 0x56, 0x04, 0x43,
	0x90, 0x81, 0xda, 0x1e, 0x5e, 0x21, 0x73, 0x04, 0x2d, 0x7c, 0x98, 0xcd, 0x80, 0x24, 0x87, 0xfb,
	0x90, 0xb7, 0x9d, 0xd8, 0x77, 0x95, 0x9c, 0x72, 0xca, 0xfb, 0xbe, 0x8b, 0x77, 0x8f, 0x02, 0x45,
	0x93, 0x57, 0x4e, 0x5d, 0xca, 0xec, 0x75, 0x98, 0x77, 0x49, 0xcd, 0xfc, 0x94, 0x3b, 0x49, 0x1f,
	0x83, 0x7e, 0x15, 0x51, 0xe4, 0x60, 0x1b, 0xa1, 0xe6, 0x57, 0x60, 0xc9, 0x63, 0xc9, 0xa9, 0x33,
	0x88, 0x2d, 0xad, 0xd0, 0xc4, 0xbe, 0x56, 0x79, 0xc4, 0x1b, 0x40, 0x50, 0x87, 0x87, 0x3c, 0xe9,
	0x5e, 0x3a, 0x22, 0x8d, 0x42, 0x2e, 0x94, 0x55, 0x36, 0xb1, 0x67, 0x93, 0x3a, 0x0e, 0x08, 0x6e,
	0xdd, 0x84, 0xe5, 0xd6, 0x09, 0x77, 0x4f, 0xa5, 0xe3, 0x22, 0xc5, 0xdf, 0x0c, 0xd3, 0xe4, 0xd2,
	0xe6, 0x9f, 0x62, 0x70, 0xbe, 0x32, 0xa6, 0x53, 0xc4, 0x78, 0x20, 0xfa, 0xc2, 0x89, 0x62, 0x1e,
	0x12, 0x3b, 0xab, 0xf6, 0xb4, 0x2f, 0xf6, 0x62, 0x1e, 0x62, 0x64, 0x92, 0x89, 0x4e, 0x38, 0x69,
	0xd4, 0x53, 0x7c, 0x90, 0xfa, 0xbe, 0x90, 0x77, 0x1d, 0x46, 0x3d, 0xc9, 0x8b, 0xd7, 0x60, 0x3e,
	0x8e, 0xa2, 0x9e, 0x73, 0xdc, 0xef, 0xf5, 0x1c, 0x19, 0x24, 0x4b, 0x8b, 0x6e, 0x20, 0x78, 0xab,
	0xdf, 0xeb, 0x7d, 0x84, 0x40, 0xeb, 0x07, 0x86, 0x0c, 0x4c, 0x76, 0x75, 0x43, 0xbb, 0x21, 0x95,
	0xbf, 0x10, 0x6a, 0x66, 0x69, 0x2b, 0xd2, 0x9e, 0x9c, 0x70, 0xd8, 0xd4, 0x56, 0x61, 0x16, 0xd5,
	0xdd, 0xf1, 0x53, 0x1e, 0xe0, 0xc0, 0xa1, 0xe0, 0x04, 0x4f, 0x70, 0x1a, 0x4c, 0xc7, 0xbd, 0x14,
	0x20, 0x51, 0xc3, 0x63, 0xd3, 0xfa, 0xbd, 0x0a, 0x98, 0x1b, 0x39, 0xdf, 0xaf, 0x88, 0x5e, 0x30,
	0xd8, 0x93, 0x38, 0x4e, 0x11, 0xdb, 0x2a, 0xc8, 0xc8, 0xd0, 0x05, 0x2f, 0x1a, 0xfe, 0x85, 0xbc,
	0x68, 0x3c, 0x3b, 0x76, 0xd1, 0xf9, 0xf0, 0x9f, 0x1f, 0xbb, 0x5c, 0x83, 0xc5, 0x21, 0xa6, 0x88,
	0xd8, 0xfa, 0x45, 0x65, 0x08, 0x4e, 0x31, 0x4c, 0x99, 0x37, 0xc6, 0x20, 0x6f, 0x4a, 0x02, 0xa8,
	0x94, 0x05, 0x90, 0x77, 0x86, 0xa3, 0x7c, 0xe5, 0x1d, 0x00, 0xea, 0x8c, 0x47, 0x3b, 0xcb, 0x4c,
	0x2d, 0xfa, 0xc3, 0xde, 0xf2, 0x06, 0xcc, 0x88, 0x28, 0xa1, 0x8d, 0x4b, 0x7f, 0x39, 0x8d, 0xcd,
	0xb6, 0x87, 0x1d, 0xfd, 0xd8, 0x49, 0x87, 0x03, 0x94, 0x5b, 0x50, 0xc3, 0x80, 0x44, 0x76, 0x0d,
	0x46, 0x28, 0x2b, 0x50, 0x3d, 0xf6, 0x55, 0x5f, 0x39, 0x44, 0x41, 0x8d, 0xf7, 0x85, 0x43, 0x56,
	0xda, 0x8d, 0x3c, 0x8f, 0x0b, 0x41, 0xa1, 0x78, 0xd5, 0x6e, 0xf8, 0x02, 0x99, 0xb5, 0x2d, 0x81,
	0xd6, 0x3d, 0xb8, 0xb3, 0xcd, 0x53, 0xcd, 0xfa, 0x1e, 0x47, 0x01, 0xc7, 0x03, 0x47, 0x5d, 0xaa,
	0xac, 0x1f, 0x4f, 0xc0, 0xdd, 0xab, 0x30, 0x44, 0x8c, 0x71, 0xbf, 0xee, 0x09, 0xc8, 0x1e, 0x24,
	0xbb, 0xe7, 0x78, 0xe1, 0x08, 0xd0, 0x2c, 0x7e, 0x4b, 0x76, 0x9b, 0x3b, 0x24, 0xb5, 0xd5, 0x92,
	0x64, 0xc8, 0x21, 0xa9, 0xed, 0x4a, 0xaa, 0x0e, 0x5c, 0xcf, 0x14, 0x83, 0x04, 0x85, 0x71, 0x94,
	0xb4, 0x88, 0x29, 0xb2, 0x88, 0x87, 0x63, 0x2c, 0x62, 0x84, 0x92, 0xd9, 0x8b, 0xee, 0x70, 0x58,
	0x67, 0x3e, 0x82, 0x45, 0x97, 0x85, 0x4e, 0xa1, 0x7d, 0x92, 0xfa, 0xf4, 0xea, 0x04, 0xae, 0xc7,
	0x65, 0x61, 0x2b, 0xd3, 0x42, 0x42, 0x7f, 0x03, 0xcc, 0x6c, 0xe1, 0x9a, 0xc2, 0x4a, 0x3d, 0x68,
	0xaa, 0x9e, 0x7c, 0x84, 0xf5, 0x1b, 0x03, 0xe6, 0xb4, 0x95, 0x60, 0xc4, 0xb7, 0x02, 0x55, 0x0a,
	0xf1, 0x1c, 0x7f, 0x28, 0xe4, 0xbb, 0x0f, 0xb3, 0x19, 0xcd, 0xb4, 0x88, 0xf8, 0xea, 0x0a, 0x96,
	0xa9, 0xd8, 0xe7, 0xd6, 0xf5, 0x30, 0x8f, 0xec, 0x72, 0x17, 0xa8, 0x9b, 0xc1, 0x74, 0xd9, 0x0c,
	0x28, 0x3f, 0x29, 0x52, 0xc7, 0x7b, 0xde, 0x60, 0xce, 0xa1, 0x80, 0x1b, 0x4f, 0xfa, 0xf2, 0xce,
	0xbf, 0xb0, 0x80, 0xee, 0x92, 0x42, 0xf4, 0x91, 0x13, 0x50, 0x34, 0xa1, 0x07, 0x75, 0xaf, 0x3e,
	0x5b, 0x3d, 0x5e, 0x24, 0xb0, 0xfb, 0x49, 0x05, 0x9a, 0x38, 0xf4, 0x73, 0x9f, 0x39, 0xff, 0xd1,
	0x3e, 0x6b, 0xf0, 0x8c, 0x1b, 0x0e, 0xf3, 0x74, 0xff, 0x54, 0x8e, 0xf3, 0xca, 0xde, 0xb7, 0x36,
	0x70, 0xfc, 0xed, 0xc1, 0x6d, 0x25, 0x0d, 0x34, 0xab, 0xb7, 0x33, 0xce, 0x88, 0xb1, 0xa1, 0xe0,
	0x33, 0xce, 0x41, 0xeb, 0xd7, 0x06, 0xb9, 0xb9, 0x71, 0x14, 0x45, 0x6c, 0xee, 0x40, 0x23, 0x73,
	0x3f, 0xcf, 0x93, 0x85, 0x1b, 0x14, 0x98, 0x9d, 0xc7, 0xe6, 0x64, 0xd1, 0xef, 0xc0, 0xf5, 0x80,
	0x27, 0x5d, 0xee, 0x68, 0x11, 0xa1, 0x24, 0x5b, 0x21, 0x1f, 0xb0, 0x48, 0xbd, 0x5b, 0x79, 0x6c,
	0x48, 0x83, 0xbe, 0x0c, 0xa6, 0x1c, 0xa4, 0x24, 0x5f, 0x24, 0x3b, 0x1b, 0xf6, 0x3c, 0xf5, 0x6c,
	0x93, 0x0e, 0x20, 0xb2, 0xba, 0x82, 0xae, 0xf5, 0x7a, 0xcf, 0x17, 0x2a, 0xff, 0x71, 0x85, 0x14,
	0x7c, 0x24, 0xfe, 0x7f, 0xbd, 0xdd, 0x9b, 0xdf, 0x81, 0x6b, 0x71, 0xc2, 0x05, 0x92, 0x2e, 0xaf,
	0x7b, 0xf2, 0xc5, 0xd6, 0xbd, 0xa8, 0xa8, 0xe8, 0x0c, 0xb1, 0xfe, 0xd6, 0x90, 0x06, 0x89, 0xf3,
	0xad, 0x47, 0x61, 0x5f, 0x3c, 0xc3, 0xc5, 0x6a, 0xb6, 0x5a, 0x19, 0x69, 0xab, 0xae, 0xca, 0xc3,
	0x2b, 0x5b, 0x6d, 0x85, 0x69, 0xd9, 0x56, 0x27, 0x07, 0x6c, 0x35, 0x1b, 0x87, 0xf1, 0xbf, 0x66,
	0x8c, 0x18, 0xfb, 0x5f, 0x87, 0x69, 0x11, 0xf5, 0x13, 0x37, 0x8f, 0xb2, 0x64, 0x0b, 0x2f, 0x1f,
	0x6e, 0xc2, 0x59, 0xca, 0xf5, 0x18, 0x02, 0x24, 0x88, 0xee, 0x55, 0x1f, 0xd3, 0xad, 0x66, 0x70,
	0x5b, 0x2f, 0xe8, 0x3f, 0x97, 0x60, 0xaa, 0xe7, 0x07, 0x7e, 0xb6, 0x1b, 0xd9, 0xb0, 0x9e, 0xd0,
	0xa5, 0x68, 0x04, 0x69, 0x11, 0x9b, 0xef, 0x95, 0x52, 0xbc, 0x57, 0xc9, 0x45, 0x1f, 0xab, 0x32,
	0xbc, 0x8f, 0xa1, 0xb9, 0xcd, 0xd3, 0x2d, 0x99, 0xda, 0xfc, 0x5c, 0x39, 0xde, 0x1f, 0x55, 0x00,
	0x24, 0x9d, 0x43, 0x26, 0x4e, 0xb5, 0x57, 0xa4, 0x06, 0xbd, 0x22, 0xdd, 0x82, 0x1a, 0xf3, 0x3c,
	0x27, 0x8e, 0x7c, 0x95, 0xfd, 0xaf, 0xd8, 0x55, 0xe6, 0x79, 0xfb, 0xd8, 0x96, 0xe1, 0xea, 0x85,
	0xea, 0x94, 0x1b, 0xaf, 0x06, 0xec, 0x42, 0x76, 0xde, 0x80, 0x19, 0xec, 0x2c, 0xde, 0xcc, 0xa6,
	0x03, 0x76, 0xd1, 0x92, 0x6f, 0x16, 0xe8, 0x7e, 0x7a, 0x3c, 0xd5, 0x1f, 0xcd, 0xea, 0x19, 0x0c,
	0x51, 0xf2, 0xb7, 0xbb, 0x69, 0xfd, 0xed, 0x6e, 0x05, 0xaa, 0xdf, 0xed, 0x07, 0x31, 0x79, 0x62,
	0x79, 0x34, 0xce, 0x60, 0x1b, 0x3d, 0xb1, 0x09, 0x93, 0x94, 0xf3, 0x95, 0x31, 0x33, 0xfd, 0x46,
	0x18, 0x9d, 0xa2, 0x32, 0x56, 0xa6, 0xdf, 0xa8, 0x0c, 0x4f, 0xfb, 0x69, 0x1a, 0x85, 0x4e, 0xca,
	0x2f, 0x52, 0x8a, 0xfb, 0x6a, 0x36, 0x48, 0xd0, 0x21, 0xbf, 0x48, 0xad, 0xef, 0xc3, 0x82, 0xe4,
	0xc6, 0x0e, 0x3f, 0xe3, 0xbd, 0x16, 0xa5, 0x91, 0x48, 0xb8, 0xd8, 0xcc, 0x18, 0x4b, 0x0d, 0x84,
	0x16, 0x6c, 0x69, 0xd8, 0xb2, 0x91, 0xcf, 0x3a, 0xa1, 0xcd, 0xba, 0x04, 0x53, 0xec, 0x9c, 0x25,
	0x9e, 0x52, 0x67, 0xd9, 0xc8, 0xd7, 0x3c, 0x55, 0xac, 0xd9, 0xda, 0x82, 0xc5, 0xbd, 0x70, 0xcd,
	0x45, 0x5e, 0xb0, 0xf0, 0x32, 0x7f, 0x03, 0xb9, 0x05, 0xb5, 0x84, 0x85, 0xda, 0x33, 0x52, 0xc3,
	0xae, 0x22, 0x80, 0x02, 0xf2, 0xec, 0x19, 0xa3, 0x52, 0x3c, 0x63, 0x58, 0x3f, 0x36, 0xa0, 0xbe,
	0x9d, 0x30, 0xcf, 0x0f, 0xbb, 0x44, 0x60, 0xf4, 0x0e, 0xee, 0xc3, 0x6c, 0x57, 0x22, 0xe9, 0xb7,
	0xee, 0xba, 0x82, 0x65, 0x97, 0xee, 0x0c, 0x45, 0x4b, 0xdd, 0x67, 0x28, 0x94, 0x55, 0x7f, 0x19,
	0x1a, 0x19, 0x8a, 0x7c, 0x0e, 0x54, 0xef, 0xbf, 0x0a, 0xd8, 0x42, 0x98, 0xf5, 0xeb, 0x29, 0x7a,
	0xad, 0xd6, 0x35, 0x56, 0xc4, 0x23, 0xde, 0x9a, 0xae, 0xd6, 0x56, 0xca, 0x01, 0xc8, 0x6e, 0x5d,
	0xe9, 0x54, 0xda, 0x3f, 0x57, 0xca, 0xa7, 0x7e, 0xe8, 0x95, 0x6e, 0x8f, 0x08, 0x20, 0x5e, 0x15,
	0x4f, 0x04, 0xd4, 0x3d, 0xa5, 0x32, 0x11, 0x52, 0xff, 0x11, 0xe1, 0x5d, 0x98, 0x4c, 0x99, 0x38,
	0xa5, 0x80, 0x74, 0x7c, 0x92, 0xaa, 0x30, 0x18, 0x9b, 0xd0, 0xcd, 0x0f, 0x61, 0x96, 0x58, 0xea,
	0xc8, 0xc4, 0x23, 0x3d, 0xea, 0x8e, 0x7f, 0xb7, 0x19, 0xd2, 0x30, 0xbb, 0xde, 0xd3, 0xd4, 0x6d,
	0x05, 0x68, 0xc1, 0x8e, 0xc7, 0x2e, 0xb3, 0xd8, 0x00, 0xdb, 0x1b, 0xec, 0x12, 0xd7, 0x9f, 0x79,
	0x77, 0xb4, 0x02, 0xa9, 0xda, 0xa0, 0x40, 0x68, 0x08, 0xf7, 0x61, 0xb6, 0x1f, 0x3e, 0x8d, 0xfa,
	0xc8, 0x00, 0x74, 0x77, 0xf2, 0x91, 0xa1, 0xae, 0x60, 0x14, 0x5f, 0xdc, 0x87, 0xd9, 0x8c, 0x06,
	0x49, 0xbd, 0x2e, 0x45, 0xaa, 0x60, 0x24, 0xf5, 0x37, 0x61, 0xd1, 0xed, 0x61, 0xc4, 0xe0, 0x9e,
	0xb0, 0x10, 0x4f, 0x2b, 0x5a, 0xf0, 0xf2, 0xac, 0x7a, 0xde, 0xc0, 0xae, 0x16, 0xf5, 0xc8, 0x9d,
	0x98, 0xaf, 0xc3, 0x3c, 0xcb, 0x94, 0x56, 0x5d, 0x46, 0x1a, 0xab, 0xc6, 0x83, 0x09, 0x7b, 0x2e,
	0x07, 0xcb, 0xdb, 0xc8, 0x57, 0x60, 0x69, 0x00, 0x51, 0x46, 0xba, 0x73, 0x32, 0xa1, 0x52, 0xc6,
	0xa6, 0x98, 0x77, 0x1f, 0xe6, 0x48, 0xf5, 0xfd, 0xf0, 0x38, 0x92, 0x07, 0xd9, 0xfc, 0x95, 0x37,
	0x91, 0x11, 0xe6, 0x63, 0xcf, 0x26, 0xea, 0x17, 0x9d, 0x90, 0x9b, 0x9a, 0x4a, 0x87, 0xc7, 0xd1,
	0x72, 0x73, 0xd5, 0x78, 0x50, 0x7f, 0xdb, 0x1a, 0x43, 0x4f, 0xb3, 0xa2, 0x42, 0xed, 0xc3, 0xe3,
	0xc8, 0xfa, 0x7b, 0x3a, 0x0b, 0x53, 0x3f, 0xec, 0x6e, 0xb3, 0x80, 0xdb, 0xdc, 0x8d, 0x12, 0x3a,
	0xd7, 0x84, 0xcb, 0x43, 0xed, 0x5a, 0x3d, 0x43, 0xed, 0x36, 0x69, 0xb6, 0xec, 0xd2, 0x4c, 0xad,
	0x46, 0x90, 0x2c, 0xcb, 0x88, 0x1d, 0x78, 0xa3, 0xcf, 0x42, 0xd4, 0x02, 0x60, 0xde, 0x05, 0x78,
	0xca, 0xdc, 0xd3, 0x6e, 0x82, 0x52, 0x54, 0x06, 0xa6, 0x41, 0x50, 0x2f, 0x24, 0x71, 0xf9, 0x4c,
	0xab, 0xf4, 0x9a, 0x40, 0x2d, 0x7a, 0xa9, 0x95, 0xf9, 0xa2, 0x73, 0xce, 0xe4, 0x6b, 0x3d, 0xe5,
	0x8b, 0x9e, 0x70, 0x96, 0xa0, 0xf7, 0xc0, 0xa0, 0x55, 0x9d, 0x8b, 0xf4, 0xdb, 0xfa, 0xd9, 0x24,
	0x2c, 0xec, 0xfb, 0xdc, 0xe5, 0xe2, 0xa0, 0x1f, 0x04, 0x2c, 0xb9, 0xdc, 0x60, 0x29, 0xc3, 0x30,
	0xe4, 0x84, 0x85, 0x9e, 0xe3, 0x87, 0x0e, 0xfd, 0x2f, 0xa7, 0x92, 0xbb, 0x9c, 0x47, 0x48, 0x3b,
	0x7c, 0xcc, 0x42, 0x4f, 0xce, 0xf7, 0x08, 0x16, 0x4b, 0xc8, 0xb4, 0x14, 0xa1, 0xb6, 0xdd, 0x2c,
	0xb0, 0x0f, 0x08, 0x8e, 0x7c, 0x73, 0x63, 0x45, 0x51, 0xc5, 0x03, 0x6e, 0x2c, 0x29, 0xe1, 0xf9,
	0x1d, 0x17, 0xaf, 0x66, 0x93, 0xea, 0xfc, 0x8e, 0xf3, 0xb7, 0xb5, 0x55, 0x98, 0x75, 0x63, 0x87,
	0xf8, 0xa4, 0x05, 0xe9, 0xe0, 0xc6, 0xfb, 0x08, 0x42, 0xa3, 0x78, 0x19, 0x1a, 0x6e, 0xec, 0x68,
	0x0c, 0x54, 0x59, 0x74, 0x37, 0x5e, 0x2f, 0x58, 0xf8, 0x3a, 0xcc, 0x7b, 0x48, 0x42, 0x43, 0x93,
	0x87, 0xcc, 0x1c, 0x82, 0x35, 0xc4, 0xff, 0x0e, 0x37, 0x12, 0x96, 0x70, 0x27, 0xe1, 0x3d, 0x96,
	0xfa, 0x51, 0x28, 0x4e, 0xfc, 0x98, 0x84, 0x2a, 0x96, 0xab, 0xf4, 0xfc, 0x7e, 0x0d, 0xbb, 0x6d,
	0xad, 0x17, 0x05, 0x4c, 0xc9, 0xc4, 0xe1, 0x71, 0x4f, 0xbb, 0xca, 0x88, 0xcd, 0xc1, 0x41, 0xeb,
	0x5d, 0x8c, 0x3d, 0x3d, 0xd2, 0x30, 0x27, 0x21, 0xf5, 0x5a, 0x86, 0x67, 0xc4, 0x0a, 0x65, 0x6d,
	0xb4, 0x67, 0xe5, 0x68, 0xa5, 0x9b, 0x9f, 0xc0, 0xe2, 0x39, 0xf7, 0xa4, 0x9f, 0xe6, 0x49, 0xea,
	0x1f, 0xfb, 0x2e, 0xea, 0x5a, 0x9d, 0xd4, 0xff, 0x4b, 0x63, 0x68, 0x3e, 0x91, 0x23, 0x5a, 0xc5,
	0x00, 0xdb, 0x3c, 0x1f, 0x82, 0x59, 0xbf, 0x32, 0xc0, 0x1c, 0x46, 0x35, 0xbf, 0x06, 0x53, 0xdd,
	0x24, 0x8a, 0x64, 0xce, 0x63, 0xbc, 0x8d, 0x69, 0x55, 0x49, 0xb6, 0x1c, 0x80, 0x23, 0x9f, 0x26,
	0xbe, 0x27, 0x0d, 0xe5, 0x39, 0x47, 0xd2, 0x00, 0x74, 0x6f, 0xd9, 0x36, 0xc9, 0x03, 0x4e, 0x90,
	0x23, 0xaa, 0x2b, 0x18, 0x79, 0xc0, 0x1b, 0x30, 0x13, 0xfb, 0x2e, 0x29, 0x8b, 0xaa, 0x7a, 0x89,
	0xe9, 0x25, 0xc5, 0x3a, 0x04, 0x90, 0x7a, 0x4f, 0x0a, 0x8f, 0x47, 0x39, 0x72, 0xc8, 0x50, 0x47,
	0x39, 0xee, 0x28, 0x3b, 0xde, 0x2b, 0xda, 0xf1, 0x5e, 0x36, 0xce, 0x89, 0x41, 0xe3, 0x54, 0xc1,
	0x9a, 0x24, 0xfc, 0xf9, 0x82, 0xb5, 0x7f, 0x9a, 0xa0, 0x53, 0x54, 0x27, 0x35, 0xfe, 0x49, 0xfe,
	0x19, 0x47, 0xe9, 0x3a, 0xcc, 0x08, 0x69, 0xdd, 0xb4, 0xe4, 0xf1, 0xa7, 0xd5, 0x90, 0x27, 0xb0,
	0xb3, 0x81, 0xe6, 0xd7, 0x61, 0x3a, 0xa6, 0x5e, 0x75, 0xbb, 0xb8, 0x7f, 0x25, 0x09, 0x1a, 0xab,
	0x06, 0x98, 0x5f, 0x85, 0xeb, 0xc3, 0xd6, 0x40, 0x45, 0x3c, 0x53, 0x64, 0x44, 0x4b, 0x83, 0xf6,
	0x70, 0xc8, 0xba, 0x02, 0xf7, 0xe4, 0x0b, 0x47, 0x69, 0x9a, 0xf2, 0x64, 0x35, 0xf4, 0x64, 0x04,
	0x40, 0x1b, 0xce, 0x64, 0xcf, 0x7b, 0x1c, 0xaf, 0x50, 0x99, 0x0d, 0x2b, 0xf0, 0xa6, 0x84, 0x9a,
	0x8f, 0x20, 0xd3, 0x62, 0xdd, 0xde, 0x65, 0xf4, 0xb8, 0xa0, 0x7a, 0x34, 0x93, 0x7f, 0x19, 0x1a,
	0xb9, 0xe9, 0x50, 0x88, 0x23, 0x6d, 0x36, 0x53, 0x34, 0x0a, 0x71, 0xcc, 0x4d, 0xb8, 0xa7, 0xf8,
	0x7d, 0x12, 0xf5, 0x05, 0x77, 0x46, 0x4c, 0x20, 0xe3, 0xcd, 0xdb, 0x12, 0xed, 0x31, 0x62, 0x3d,
	0x19, 0x9c, 0xcb, 0xfa, 0x3e, 0x5d, 0x47, 0x5a, 0xfb, 0x99, 0x7f, 0x7b, 0xec, 0x8b, 0x34, 0xa2,
	0x27, 0x81, 0xcf, 0x28, 0xe8, 0xe2, 0xca, 0x32, 0x31, 0xfa, 0xca, 0x32, 0xa9, 0x5f, 0x59, 0x3e,
	0x86, 0xf9, 0x81, 0x99, 0x47, 0xda, 0xc1, 0x4d, 0xa8, 0xe6, 0x2e, 0x19, 0x67, 0x9c, 0xb2, 0xf3,
	0x36, 0x4e, 0x98, 0x70, 0x26, 0x8a, 0x42, 0x0f, 0xd9, 0xb2, 0xfe, 0xca, 0xa0, 0xeb, 0xd0, 0x88,
	0xad, 0x7d, 0x66, 0x25, 0x7e, 0xa1, 0xbd, 0x99, 0x1f, 0xc0, 0xcc, 0x89, 0x9c, 0x52, 0xa5, 0x33,
	0xc7, 0xbd, 0x27, 0x0f, 0x2e, 0x30, 0x1b, 0x86, 0xa6, 0x7c, 0x24, 0x43, 0x29, 0x19, 0xf9, 0x28,
	0xa9, 0x44, 0xb1, 0xbe, 0xf2, 0x28, 0x56, 0x2b, 0x4f, 0x59, 0xd2, 0xe5, 0xa9, 0xbe, 0x72, 0x09,
	0x41, 0x53, 0x5e, 0x84, 0x85, 0x01, 0x4a, 0x22, 0xb6, 0x3a, 0x70, 0xbd, 0xc5, 0x42, 0x97, 0xf7,
	0xbe, 0xa0, 0x49, 0x56, 0xe0, 0xc6, 0x48, 0x7a, 0x72, 0xaa, 0x0d, 0x3f, 0xe1, 0x6e, 0xfa, 0xc5,
	0x4d, 0x35, 0x92, 0x9e, 0x88, 0xad, 0x1f, 0x1a, 0xb0, 0x74, 0xc0, 0x29, 0xb2, 0xa4, 0x53, 0xba,
	0x1d, 0x1e, 0x09, 0xfe, 0x99, 0x67, 0x2a, 0xc5, 0x58, 0x13, 0x57, 0xc5, 0x58, 0x93, 0x03, 0x31,
	0x96, 0x75, 0x03, 0xae, 0x8d, 0x58, 0x87, 0x88, 0xad, 0x7f, 0x31, 0xe0, 0x86, 0x1e, 0xd0, 0xae,
	0xab, 0xfb, 0xc2, 0x67, 0x5f, 0xe4, 0x2b, 0x30, 0x77, 0x9c, 0x44, 0x81, 0x53, 0x5c, 0x45, 0xe4,
	0x52, 0x67, 0x11, 0x9a, 0x91, 0xa7, 0xda, 0x26, 0xc4, 0xd2, 0xef, 0x24, 0xaa, 0x56, 0x00, 0xe1,
	0x5b, 0xc5, 0xbd, 0x64, 0x15, 0x66, 0xd3, 0x48, 0xa3, 0xa6, 0x22, 0xbc, 0x34, 0xca, 0x69, 0xbd,
	0x02, 0x73, 0x69, 0x54, 0xa2, 0x24, 0x73, 0x92, 0xb3, 0x69, 0x54, 0xd0, 0x91, 0x2f, 0x8e, 0xa3,
	0x36, 0x2a, 0x62, 0x6b, 0x97, 0x12, 0x61, 0x8f, 0xfb, 0x61, 0xba, 0x1b, 0x85, 0x22, 0xa5, 0x93,
	0x35, 0xce, 0x4f, 0xac, 0xd1, 0x99, 0xc5, 0xa2, 0xc4, 0xad, 0x32, 0x58, 0xe2, 0xf6, 0xfb, 0x93,
	0x54, 0xe3, 0x36, 0x9a, 0x9e, 0x88, 0xd1, 0x85, 0xf4, 0x05, 0x4f, 0x48, 0x54, 0x92, 0x6a, 0xde,
	0x2e, 0x15, 0x16, 0x56, 0x06, 0x0a, 0x0b, 0x6f, 0x41, 0x2d, 0x46, 0x42, 0x5a, 0xf2, 0xa8, 0x4a,
	0x80, 0x56, 0x98, 0x9a, 0xff, 0x0d, 0x96, 0x44, 0xca, 0x2e, 0x9d, 0x6c, 0x61, 0x51, 0xa8, 0x97,
	0xed, 0x2e, 0x60, 0x9f, 0x2a, 0xba, 0xdb, 0x0b, 0x29, 0xd5, 0xf0, 0x16, 0x5c, 0x2b, 0x0d, 0x38,
	0xf6, 0xcf, 0xf4, 0xb4, 0x84, 0xa9, 0x8d, 0xd8, 0xf2, 0xcf, 0x68, 0xc8, 0x37, 0xe0, 0x16, 0x0f,
	0x53, 0x9e, 0xe4, 0x63, 0x4e, 0xa2, 0x7e, 0xe2, 0xd0, 0x2d, 0xa5, 0xa8, 0xf3, 0xbd, 0x41, 0x28,
	0x6a, 0xe4, 0xe3, 0xa8, 0x9f, 0xe0, 0x6d, 0x04, 0x47, 0x3f, 0x80, 0x26, 0xd5, 0x45, 0x66, 0x77,
	0x30, 0x37, 0xcc, 0x82, 0xef, 0x39, 0x84, 0xef, 0x4b, 0x30, 0x62, 0x7e, 0x00, 0x77, 0x08, 0x13,
	0xf7, 0x90, 0xe2, 0x99, 0xc2, 0x06, 0x86, 0xc9, 0xcb, 0xe1, 0x0a, 0x22, 0xed, 0x85, 0xfc, 0x50,
	0xa1, 0x68, 0x14, 0xde, 0x87, 0xdb, 0xc7, 0x7e, 0x22, 0x52, 0x87, 0xe8, 0xa4, 0x4f, 0x39, 0x0b,
	0x4b, 0x04, 0x64, 0x76, 0x79, 0x99, 0x70, 0x0e, 0x78, 0xe8, 0x1d, 0x22, 0x86, 0x36, 0xfe, 0xcb,
	0x60, 0x32, 0xcf, 0x73, 0x8e, 0x59, 0x28, 0x1c, 0x3c, 0x9e, 0x62, 0x1a, 0x25, 0xef, 0x94, 0xf3,
	0xcc, 0xf3, 0xb6, 0x58, 0x28, 0xb6, 0x11, 0x8e, 0xc8, 0xf2, 0x5d, 0x0d, 0x23, 0xd5, 0x20, 0xc0,
	0xe9, 0xdc, 0x13, 0x8a, 0x2d, 0xe9, 0x5d, 0xcd, 0xce, 0xa0, 0xad, 0x13, 0xeb, 0x13, 0x68, 0xec,
	0x32, 0x71, 0xca, 0xbd, 0xfd, 0xd3, 0x03, 0x37, 0x4a, 0x46, 0x15, 0xa2, 0x2f, 0xc1, 0x94, 0xc0,
	0xae, 0x2c, 0xb5, 0x42, 0x0d, 0x8c, 0xec, 0x4e, 0x98, 0x70, 0xce, 0xfd, 0xf4, 0xc4, 0x4b, 0xd8,
	0x39, 0xc9, 0x5e, 0xbe, 0x18, 0x3c, 0x51, 0x20, 0xeb, 0x0c, 0x9a, 0x25, 0xda, 0xcf, 0xc8, 0x4f,
	0x5e, 0x87, 0x69, 0x16, 0xe4, 0x85, 0xad, 0x53, 0xb6, 0x6a, 0xd1, 0x2b, 0x8c, 0xcc, 0x19, 0xb2,
	0x5c, 0xc5, 0x24, 0x60, 0xad, 0xc8, 0xf0, 0x4c, 0x16, 0x21, 0xa0, 0xd5, 0xa6, 0xda, 0x8c, 0xc1,
	0xa9, 0xd1, 0x6c, 0x4c, 0x98, 0x8c, 0x59, 0x37, 0xcb, 0xda, 0xd0, 0x6f, 0xd2, 0x60, 0xd6, 0xe5,
	0x8e, 0xf0, 0xbf, 0x97, 0xbf, 0xaf, 0x22, 0xe0, 0xc0, 0xff, 0x1e, 0xb7, 0x3e, 0x52, 0x45, 0x16,
	0x83, 0xa4, 0x9e, 0x3b, 0x65, 0x38, 0x34, 0x54, 0xa6, 0x0c, 0xcf, 0xa8, 0xea, 0x67, 0xf7, 0x72,
	0x27, 0x4a, 0x53, 0x8e, 0x87, 0x2e, 0x5d, 0x11, 0x64, 0xa5, 0x2a, 0x25, 0xa4, 0xf4, 0xec, 0x52,
	0x8d, 0x20, 0xe4, 0x57, 0x5e, 0x28, 0xe3, 0x99, 0xc9, 0x72, 0xb2, 0xc8, 0xaf, 0xff, 0xb2, 0x02,
	0x0b, 0xfb, 0x3c, 0xf1, 0xe3, 0x13, 0x9e, 0xb0, 0xde, 0xbe, 0x2c, 0x1f, 0xc7, 0x49, 0x63, 0xf9,
	0x53, 0x7b, 0x81, 0x56, 0x90, 0xb6, 0x57, 0x24, 0x00, 0x2b, 0x7a, 0x02, 0x10, 0x15, 0x20, 0x0a,
	0x38, 0xf1, 0x0e, 0x83, 0x77, 0x95, 0x8c, 0xca, 0x60, 0x78, 0xd5, 0xbb, 0x07, 0x75, 0x8f, 0xbe,
	0x01, 0x70, 0x34, 0x19, 0x81, 0x04, 0x6d, 0xa8, 0x5c, 0x9c, 0x7c, 0xe8, 0x99, 0x52, 0x59, 0x3b,
	0x7a, 0xe4, 0x41, 0x85, 0x4b, 0x23, 0xf7, 0x34, 0xab, 0xd5, 0xa1, 0x06, 0x6a, 0x34, 0xed, 0xca,
	0x89, 0x79, 0xe2, 0x90, 0xce, 0x28, 0x4b, 0x6d, 0x10, 0x78, 0x9f, 0x27, 0x7b, 0x08, 0x44, 0x0d,
	0x53, 0xf7, 0x89, 0xec, 0x0a, 0x38, 0x23, 0x2f, 0x14, 0x02, 0x97, 0x1c, 0x25, 0x7e, 0xd7, 0x0f,
	0xd5, 0xf3, 0x92, 0xb4, 0xb8, 0xba, 0x84, 0xc9, 0x07, 0xa6, 0x7b, 0x50, 0x27, 0xda, 0xdc, 0xd3,
	0xac, 0x0b, 0x14, 0xa8, 0x15, 0xa6, 0xd6, 0x5f, 0x1b, 0xb0, 0x58, 0x70, 0x70, 0x3d, 0xf1, 0xf9,
	0x31, 0x25, 0xf5, 0x7e, 0x5b, 0x3c, 0xcc, 0x59, 0x34, 0xa9, 0xb3, 0x68, 0x15, 0x66, 0x7d, 0xe1,
	0x10, 0x63, 0x9c, 0xa8, 0x9f, 0xaa, 0xd2, 0x6d, 0xf0, 0xc5, 0x01, 0x82, 0xf6, 0xfa, 0xe9, 0xd0,
	0x5e, 0xa7, 0x87, 0xf6, 0x6a, 0xdd, 0xa2, 0xb7, 0x99, 0x11, 0x9b, 0xb1, 0xf9, 0xa7, 0x16, 0xa7,
	0xf3, 0x67, 0x64, 0xa7, 0x88, 0xcd, 0x6d, 0xa8, 0xd1, 0x73, 0x9a, 0x66, 0x01, 0xe3, 0x72, 0x40,
	0xa3, 0x48, 0x54, 0x71, 0x30, 0x3d, 0x62, 0xbc, 0x4f, 0x0f, 0x5e, 0x43, 0x2a, 0x59, 0x7c, 0x43,
	0x73, 0x35, 0x5f, 0xad, 0x2e, 0xbd, 0xfa, 0x8f, 0x1d, 0x2f, 0x62, 0x73, 0x53, 0x2d, 0x95, 0xd2,
	0x4b, 0xc6, 0xd5, 0x97, 0xab, 0x41, 0x32, 0x72, 0xa1, 0x94, 0x61, 0x3a, 0x82, 0xdb, 0x07, 0x7e,
	0xd8, 0xed, 0xf1, 0x21, 0x24, 0xa9, 0x76, 0xcf, 0x90, 0x7f, 0xd9, 0xb9, 0x35, 0x32, 0xe7, 0x66,
	0xfd, 0xa9, 0x01, 0x4d, 0x8d, 0x22, 0xa3, 0xab, 0xc5, 0x3d, 0xa8, 0xc7, 0xc5, 0x29, 0xa8, 0xfc,
	0x24, 0xc4, 0xf9, 0xd9, 0x47, 0x69, 0xd5, 0x7e, 0xe8, 0xf5, 0xf2, 0x57, 0xc4, 0x9a, 0x5d, 0x95,
	0x00, 0x19, 0x77, 0x69, 0x2b, 0x51, 0xd9, 0xab, 0x62, 0x25, 0xb7, 0xa0, 0xe6, 0xf1, 0x33, 0xdf,
	0xe5, 0x4e, 0xee, 0x1a, 0xaa, 0x12, 0xd0, 0xa6, 0xd4, 0x95, 0xdb, 0xf3, 0xf1, 0x44, 0xd2, 0x03,
	0x1b, 0x09, 0xa2, 0x90, 0xe5, 0x5f, 0x8d, 0x92, 0x03, 0x61, 0x97, 0xf9, 0xe7, 0x3d, 0xd1, 0xa9,
	0x2a, 0x7f, 0x42, 0xed, 0xc6, 0x06, 0x2a, 0x29, 0x6e, 0x23, 0xf7, 0xf7, 0x95, 0x7c, 0x1f, 0x7b,
	0xca, 0xe5, 0xbf, 0x06, 0xf3, 0x6e, 0xcf, 0x57, 0x18, 0xd2, 0x3e, 0x54, 0x11, 0xa7, 0xdb, 0xf3,
	0x09, 0xe9, 0x90, 0xec, 0xa4, 0x09, 0x13, 0xa9, 0x38, 0xcd, 0x1c, 0x59, 0x2a, 0x4e, 0x69, 0xa1,
	0xea, 0xc0, 0x0f, 0x58, 0x9c, 0xa7, 0x99, 0x24, 0x68, 0x97, 0xc5, 0xc8, 0x85, 0x8c, 0x6c, 0x90,
	0x69, 0x7f, 0x2d, 0x92, 0x24, 0xe9, 0x3b, 0xa9, 0xb9, 0xd4, 0x29, 0x56, 0x17, 0x46, 0xea, 0x6e,
	0x5a, 0x4f, 0xf7, 0xd5, 0xf2, 0x3a, 0x91, 0xf5, 0x93, 0x0a, 0xac, 0x94, 0x36, 0xbb, 0xc5, 0x5c,
	0x4e, 0x65, 0x60, 0x28, 0xa5, 0x57, 0xa0, 0x81, 0xed, 0xb5, 0x7e, 0x7a, 0x72, 0xa8, 0x6d, 0xbe,
	0x0c, 0x34, 0xdf, 0x85, 0x1b, 0xc7, 0xcc, 0xe5, 0x0e, 0xeb, 0xa7, 0x27, 0x4e, 0x9c, 0x44, 0x67,
	0x3e, 0xce, 0xe7, 0x46, 0x5e, 0xe6, 0x0a, 0x96, 0x8e, 0x15, 0xfe, 0xbe, 0xea, 0x6c, 0x45, 0x1e,
	0x37, 0xd7, 0xe1, 0xee, 0x88, 0x61, 0x09, 0x17, 0xfd, 0x5e, 0xea, 0x78, 0x2c, 0x65, 0x8a, 0x51,
	0x37, 0x07, 0x47, 0xdb, 0x84, 0x42, 0x29, 0x93, 0x77, 0xe0, 0x7a, 0x41, 0x43, 0x0d, 0x95, 0x62,
	0x92, 0x8c, 0x5c, 0xcc, 0xc6, 0xca, 0x31, 0x72, 0xbd, 0x77, 0x00, 0x12, 0x59, 0x46, 0x88, 0x22,
	0x93, 0x7c, 0xad, 0x29, 0x48, 0xdb, 0xb3, 0x7e, 0xc7, 0x80, 0x9b, 0xe3, 0x58, 0x22, 0xe2, 0x81,
	0xd1, 0xc6, 0xc0, 0x68, 0x3a, 0xdd, 0x70, 0x31, 0x74, 0x0b, 0xc8, 0x82, 0x54, 0x84, 0x50, 0xe6,
	0xb1, 0xbc, 0x60, 0xfa, 0xaa, 0xe6, 0x22, 0x75, 0xbe, 0x5b, 0xdc, 0x5d, 0xf3, 0x05, 0xb7, 0x64,
	0xdf, 0xb7, 0xf0, 0x22, 0xfb, 0x17, 0x15, 0x58, 0x21, 0x81, 0x0d, 0x5b, 0x2f, 0xff, 0xd4, 0xdc,
	0x2e, 0x9d, 0xd2, 0xef, 0x8c, 0xbb, 0x62, 0x5e, 0x61, 0xd9, 0xf2, 0xc4, 0x36, 0xdf, 0x87, 0x49,
	0xe6, 0x79, 0x89, 0x4a, 0x81, 0x3d, 0xdb, 0xd9, 0xd1, 0xd8, 0x35, 0xcf, 0x4b, 0x6c, 0x1a, 0x67,
	0xae, 0x43, 0x15, 0xd5, 0x8d, 0xbc, 0x90, 0x4c, 0xf1, 0xbc, 0xfe, 0x6c, 0x2f, 0x44, 0xee, 0xc0,
	0x9e, 0x89, 0xd9, 0x25, 0x9d, 0x31, 0xbb, 0x50, 0x23, 0xfe, 0x10, 0x91, 0x49, 0x22, 0xf2, 0x95,
	0xe7, 0x21, 0xa2, 0xab, 0xad, 0x5d, 0x45, 0x12, 0xe4, 0xd2, 0xfe, 0xdc, 0x80, 0x9b, 0xe3, 0x38,
	0x27, 0x62, 0xb3, 0xa5, 0xad, 0xf8, 0xb9, 0xfd, 0xa6, 0x74, 0x08, 0xc5, 0x92, 0x3b, 0xfa, 0x92,
	0x25, 0xef, 0xde, 0x7a, 0xc1, 0x25, 0x8b, 0x58, 0x5b, 0xf3, 0xff, 0xa9, 0x94, 0xf4, 0x4f, 0x93,
	0x13, 0x4d, 0x77, 0x45, 0x78, 0xf9, 0x21, 0xcc, 0x66, 0x6e, 0x31, 0x7f, 0xe3, 0x7f, 0x91, 0xa3,
	0xa0, 0xae, 0x46, 0xef, 0xe8, 0xda, 0x30, 0xf1, 0x19, 0xb5, 0xe1, 0x1e, 0xd4, 0xd3, 0x28, 0x65,
	0xbd, 0x52, 0x9d, 0x0b, 0x10, 0x48, 0xc6, 0x21, 0xb7, 0xa1, 0x86, 0x8e, 0x4b, 0xa4, 0x2c, 0x90,
	0xde, 0x6d, 0xc2, 0x2e, 0x00, 0xd6, 0xee, 0xe8, 0x53, 0x93, 0x66, 0xa1, 0xb7, 0xf2, 0x22, 0x4e,
	0x34, 0x46, 0xc7, 0x89, 0x15, 0x3d, 0xcd, 0x34, 0xe6, 0x10, 0xcd, 0xc8, 0xd1, 0x21, 0xaa, 0x9b,
	0xd1, 0x5b, 0xcf, 0xcb, 0xb4, 0x5c, 0x30, 0x2a, 0xec, 0xfd, 0x58, 0x8f, 0x9d, 0x72, 0x9e, 0xe4,
	0x5f, 0x47, 0x1a, 0xda, 0xd7, 0x91, 0x18, 0xf7, 0x9c, 0x44, 0x61, 0x1e, 0x30, 0x51, 0x83, 0xbe,
	0x7e, 0xf3, 0xbc, 0x84, 0x0b, 0x91, 0x7d, 0x31, 0xa8, 0x9a, 0xaa, 0x40, 0x90, 0x52, 0xd0, 0x23,
	0xb8, 0xce, 0x3f, 0xb5, 0xfe, 0x27, 0x6d, 0x72, 0x2c, 0x82, 0x88, 0x73, 0xa1, 0x1a, 0x9f, 0x4d,
	0xa8, 0x96, 0x03, 0x77, 0x0e, 0xae, 0x5a, 0xc2, 0xe7, 0x9e, 0x60, 0x15, 0xee, 0x1e, 0x5c, 0xb9,
	0x05, 0xeb, 0xe7, 0x06, 0xd4, 0xd6, 0xf0, 0xb6, 0x70, 0x75, 0x75, 0x56, 0xa9, 0xac, 0xa3, 0x32,
	0xa6, 0xac, 0xe3, 0x8a, 0x72, 0x10, 0x7a, 0x7d, 0xd6, 0xca, 0x41, 0xe8, 0xe9, 0x39, 0x1b, 0x77,
	0xc6, 0x7a, 0x7a, 0x8d, 0xdd, 0x47, 0xac, 0x87, 0x7e, 0x9f, 0x5f, 0xc4, 0x7e, 0xc2, 0xe9, 0x19,
	0x55, 0x1d, 0xc6, 0x12, 0xb2, 0xc1, 0x2e, 0xad, 0x3f, 0x34, 0xe0, 0xba, 0xcd, 0xd3, 0x7e, 0x12,
	0xb6, 0xa2, 0x50, 0xf4, 0x03, 0x5e, 0x6c, 0xe1, 0x9b, 0xd9, 0x7d, 0x48, 0x73, 0x43, 0xab, 0x63,
	0x18, 0x97, 0x8f, 0x52, 0x37, 0x26, 0x22, 0x70, 0x0f, 0xea, 0x09, 0x77, 0xb9, 0x7f, 0xc6, 0x69,
	0x61, 0x52, 0xef, 0x41, 0x81, 0x70, 0x6d, 0xaf, 0xc2, 0x5c, 0x86, 0x20, 0x52, 0x96, 0xf6, 0x45,
	0x56, 0xba, 0xa9, 0xa0, 0x07, 0x04, 0xb4, 0xfe, 0x71, 0x82, 0x6e, 0x95, 0x72, 0x99, 0xc5, 0x4c,
	0xa8, 0x37, 0x0f, 0x61, 0x21, 0x65, 0xe2, 0xd4, 0x21, 0x63, 0xe2, 0xea, 0xad, 0x57, 0x3d, 0xc5,
	0x61, 0xc7, 0x8e, 0x84, 0x53, 0xdc, 0x61, 0x41, 0xc3, 0x8d, 0x42, 0x5a, 0x70, 0x48, 0xcc, 0xc8,
	0x0b, 0x1d, 0xc3, 0x1d, 0x84, 0x6d, 0xb0, 0x4b, 0xc4, 0x09, 0xd8, 0x85, 0x86, 0xa3, 0x1e, 0xd6,
	0x03, 0x76, 0x91, 0xe3, 0xac, 0x41, 0x5d, 0xf6, 0x67, 0xb5, 0x0c, 0x13, 0xcf, 0xc5, 0x18, 0xa0,
	0x41, 0xd4, 0x36, 0xdf, 0x83, 0x9b, 0x1a, 0x09, 0x67, 0x80, 0x09, 0x52, 0x82, 0x37, 0x0a, 0x7c,
	0x5b, 0x67, 0x07, 0x45, 0x6e, 0x51, 0x88, 0x87, 0x34, 0xca, 0x8b, 0x58, 0x2b, 0xc5, 0x8a, 0xdb,
	0x53, 0x52, 0x44, 0xee, 0xbe, 0x06, 0xf3, 0x54, 0x78, 0xa2, 0xe1, 0xa9, 0x5b, 0x5b, 0xc0, 0x2e,
	0x34, 0x3c, 0x9b, 0xf8, 0x42, 0x38, 0x72, 0x47, 0x55, 0xda, 0xd1, 0xa3, 0x31, 0x3b, 0x1a, 0xad,
	0x2d, 0xf6, 0xac, 0xab, 0x41, 0xcc, 0x37, 0xc0, 0x94, 0x5b, 0x2b, 0x09, 0x46, 0x5e, 0xfa, 0x9a,
	0xd4, 0xa3, 0x49, 0xc6, 0x9a, 0x87, 0x06, 0x1d, 0x38, 0x79, 0xcd, 0xff, 0x27, 0x30, 0xa7, 0x03,
	0x44, 0x6c, 0xbe, 0x02, 0x73, 0x6e, 0x14, 0x04, 0x51, 0xe8, 0x94, 0xcb, 0xfd, 0x67, 0x25, 0xb4,
	0x2d, 0x8b, 0xfe, 0x2d, 0x2a, 0xc1, 0x08, 0xbd, 0x1c, 0x49, 0x15, 0x53, 0x12, 0x50, 0xe2, 0x58,
	0x6f, 0x40, 0x03, 0x3d, 0x6e, 0x92, 0x95, 0x2f, 0x53, 0x16, 0x22, 0x0a, 0x4b, 0xd7, 0xfe, 0x2a,
	0x02, 0x28, 0xe8, 0xfe, 0x1b, 0x83, 0xbe, 0x21, 0xcd, 0xd1, 0x45, 0x6c, 0xbe, 0x09, 0x8b, 0x82,
	0x82, 0x16, 0xa7, 0x27, 0x53, 0x08, 0x0e, 0x15, 0xa3, 0x1a, 0x2a, 0xb3, 0x46, 0x5d, 0x2a, 0xb9,
	0xd0, 0x8a, 0x44, 0x4a, 0xef, 0x9d, 0x3c, 0x60, 0x7e, 0x98, 0xe3, 0xd3, 0x69, 0xa2, 0xd4, 0xcf,
	0x94, 0x7d, 0x6a, 0x00, 0xb2, 0x83, 0x3e, 0x9e, 0xf0, 0x43, 0x37, 0x29, 0xf0, 0x9f, 0x72, 0x16,
	0x2a, 0x55, 0x6c, 0x62, 0x4f, 0x86, 0x8d, 0xf0, 0x61, 0x6c, 0xa2, 0x3e, 0x39, 0x8c, 0x8d, 0x70,
	0xeb, 0x2d, 0xfa, 0x23, 0x01, 0x24, 0xa5, 0xfd, 0x84, 0x9f, 0xf9, 0xfc, 0xfc, 0x99, 0x3c, 0xe8,
	0xc1, 0xe2, 0xd0, 0x10, 0x11, 0x9b, 0x47, 0x99, 0x8c, 0x63, 0x09, 0x7c, 0x9e, 0xb2, 0x40, 0x9d,
	0x08, 0x31, 0x54, 0x2a, 0x83, 0x82, 0xd0, 0xb5, 0xb4, 0x0f, 0xcd, 0x41, 0x2c, 0xb3, 0x3d, 0xc2,
	0x15, 0x8d, 0xf3, 0xe1, 0xdb, 0xbd, 0x28, 0xb9, 0xa4, 0xef, 0x6f, 0x46, 0x3a, 0xa5, 0xeb, 0x30,
	0x8d, 0x57, 0x95, 0xbc, 0xa4, 0x5c, 0xb5, 0xac, 0x3f, 0x33, 0x60, 0x71, 0xc4, 0xd0, 0x22, 0x2b,
	0xa4, 0x1d, 0x93, 0x92, 0x5c, 0x56, 0x34, 0xab, 0x56, 0x86, 0x7e, 0xb9, 0xa2, 0x75, 0x93, 0x63,
	0xbe, 0x07, 0x75, 0xd9, 0x7d, 0x1e, 0x25, 0xe9, 0x49, 0xf6, 0x8d, 0x07, 0x81, 0x9e, 0x20, 0x84,
	0xea, 0xca, 0x24, 0xf9, 0x7e, 0x90, 0x55, 0xe9, 0x48, 0xea, 0xfd, 0xa0, 0x20, 0xee, 0xb1, 0xcb,
	0xcc, 0x2d, 0x48, 0xf4, 0x0d, 0x76, 0x29, 0xac, 0xaf, 0x52, 0x26, 0xeb, 0x89, 0x1f, 0x86, 0x7e,
	0xd8, 0x6d, 0xb1, 0x24, 0xea, 0x0b, 0xde, 0x7b, 0xa6, 0x34, 0x53, 0xa8, 0xab, 0x21, 0xb4, 0x3f,
	0x3d, 0xc3, 0x6c, 0x0c, 0x64, 0x98, 0xcb, 0x6c, 0xaf, 0x7c, 0x0e, 0xb6, 0x5b, 0x27, 0xe4, 0xc2,
	0x87, 0xd6, 0x2a, 0x62, 0xb3, 0x03, 0x0b, 0xe7, 0x12, 0xac, 0xd5, 0xb6, 0x48, 0x2d, 0x1a, 0xf7,
	0xda, 0xad, 0xad, 0xdf, 0x9e, 0x3f, 0x2f, 0x1a, 0xa4, 0x3f, 0xdf, 0x04, 0xc8, 0x53, 0x7b, 0x57,
	0xb3, 0x42, 0x66, 0x86, 0x0a, 0x53, 0x94, 0x0d, 0xeb, 0xff, 0x56, 0xa0, 0x9e, 0x53, 0x10, 0x31,
	0x3d, 0xa4, 0xab, 0x05, 0x6a, 0x54, 0xea, 0x0a, 0x46, 0x84, 0x06, 0x19, 0x35, 0xf1, 0xd9, 0x19,
	0xf5, 0x43, 0x03, 0x66, 0x9f, 0xb4, 0x3b, 0x9d, 0x76, 0x67, 0xdb, 0x39, 0xfc, 0x78, 0x7f, 0xd3,
	0x5c, 0x86, 0x25, 0xbd, 0xed, 0x1c, 0x75, 0x3e, 0xec, 0xec, 0x3d, 0xe9, 0x34, 0x5f, 0x32, 0x97,
	0xa0, 0x59, 0xea, 0x59, 0x6f, 0x6f, 0x37, 0x0d, 0xf3, 0x3a, 0x98, 0x25, 0xe8, 0xd6, 0xce, 0xda,
	0xc1, 0xe3, 0x66, 0xc5, 0xbc, 0x01, 0x8b, 0x25, 0x78, 0x67, 0xcf, 0xde, 0x5d, 0xdb, 0x69, 0x4e,
	0x98, 0xd7, 0x60, 0x61, 0xa0, 0xa3, 0xb3, 0xd9, 0x9c, 0xb4, 0x7e, 0x60, 0x90, 0xd9, 0x0f, 0xa5,
	0x49, 0xaf, 0xe4, 0x68, 0x39, 0x49, 0x5a, 0x7b, 0x46, 0x92, 0xf4, 0x65, 0x68, 0xf0, 0x0b, 0x55,
	0x5a, 0xa5, 0xbd, 0xf9, 0xcc, 0x66, 0x40, 0xd2, 0xd7, 0xff, 0x67, 0x40, 0xa3, 0xb4, 0x88, 0x2f,
	0xd2, 0x1b, 0x64, 0x5f, 0x12, 0x54, 0xb4, 0xef, 0x94, 0x30, 0x98, 0x8a, 0xd2, 0xa2, 0x68, 0x02,
	0x83, 0xa9, 0x28, 0xa5, 0x83, 0xea, 0x87, 0x86, 0xcc, 0x6f, 0x0f, 0x26, 0x8f, 0x45, 0x6c, 0x1e,
	0xc2, 0x62, 0xe6, 0x80, 0x65, 0x91, 0x8a, 0xae, 0xc8, 0xaf, 0x8c, 0x59, 0x5d, 0x99, 0xcc, 0x42,
	0x4f, 0x6f, 0xd2, 0x65, 0x67, 0x0c, 0x3f, 0x2d, 0x21, 0x53, 0x84, 0xf2, 0x85, 0xe2, 0x09, 0xe7,
	0xa7, 0x2d, 0x96, 0x78, 0xd9, 0xf1, 0x59, 0xfe, 0x7c, 0xca, 0xc8, 0xea, 0x51, 0xd5, 0xe7, 0x53,
	0x32, 0xcf, 0xe4, 0x9c, 0xf1, 0x44, 0xf8, 0xca, 0x65, 0xc9, 0x3c, 0xd3, 0x47, 0x12, 0xa2, 0x7f,
	0xa7, 0x95, 0x3d, 0x26, 0xd3, 0x77, 0x5a, 0xd6, 0xfb, 0xf4, 0x8e, 0x35, 0x7a, 0x52, 0x11, 0x23,
	0xe1, 0x13, 0x76, 0xc6, 0x1d, 0xe6, 0xba, 0x78, 0x49, 0x90, 0xe7, 0x33, 0x20, 0x68, 0x8d, 0x20,
	0xd6, 0xd7, 0x28, 0xe9, 0x39, 0xc0, 0xbc, 0xec, 0x79, 0xf1, 0x4a, 0x9f, 0xf5, 0x75, 0xda, 0xee,
	0xc8, 0x91, 0x82, 0xbe, 0x16, 0xc3, 0x51, 0x05, 0xc3, 0x1b, 0x76, 0x15, 0x01, 0xe4, 0x0e, 0x9e,
	0xd2, 0x33, 0x42, 0xa9, 0x16, 0x2e, 0xab, 0x81, 0xcf, 0x3f, 0x4c, 0x35, 0xf4, 0x0f, 0x53, 0xe9,
	0xaf, 0xb9, 0x14, 0xd9, 0x42, 0xd9, 0x28, 0x57, 0x9e, 0x4e, 0x94, 0x2b, 0x4f, 0xad, 0xff, 0x5d,
	0x81, 0x6b, 0xa5, 0x19, 0xf0, 0x9e, 0x40, 0xfa, 0x35, 0xfc, 0xa4, 0x73, 0xd5, 0x8b, 0x5e, 0xf9,
	0x21, 0x71, 0x62, 0xe0, 0x21, 0x91, 0xe2, 0xe9, 0x28, 0x0a, 0xb2, 0x30, 0x51, 0xdd, 0x5c, 0x11,
	0xa4, 0x22, 0xc3, 0x3b, 0x40, 0x7f, 0x36, 0xc8, 0x39, 0x4e, 0x8a, 0x3f, 0x0e, 0x51, 0x43, 0xc8,
	0x56, 0xa2, 0xfe, 0x2e, 0x04, 0xbf, 0x48, 0x79, 0xe8, 0xc9, 0xc4, 0x8e, 0xfa, 0xbb, 0x10, 0x12,
	0xf4, 0x2d, 0x11, 0x85, 0x94, 0x13, 0xe7, 0xcc, 0x3b, 0xe7, 0x2c, 0x71, 0x4e, 0xf9, 0x65, 0x96,
	0x97, 0xcb, 0x60, 0x1f, 0xf2, 0x4b, 0xfd, 0x6f, 0xa0, 0x54, 0x4b, 0x7f, 0x03, 0xc5, 0xfa, 0x95,
	0x01, 0x73, 0x39, 0x13, 0xa8, 0xa6, 0x03, 0xe9, 0x25, 0x5c, 0x96, 0x9f, 0x53, 0x8e, 0x5d, 0x9e,
	0x2f, 0xf5, 0x0c, 0x76, 0x94, 0xf4, 0x90, 0x41, 0x81, 0xf7, 0xae, 0xe2, 0x04, 0xfe, 0x54, 0xf9,
	0x75, 0x3f, 0x4c, 0x23, 0x87, 0x4a, 0xa6, 0x26, 0xb2, 0xfc, 0x7a, 0x3b, 0x4c, 0x23, 0x3b, 0x8a,
	0xe8, 0x58, 0x94, 0x95, 0x25, 0xfa, 0xeb, 0x36, 0x41, 0x3a, 0xea, 0xfa, 0x9a, 0x30, 0xcf, 0x8f,
	0x88, 0x01, 0x15, 0x5b, 0x36, 0x30, 0x1a, 0x46, 0xbe, 0xf9, 0x2e, 0x9d, 0xd4, 0xda, 0x27, 0x47,
	0x0d, 0x09, 0xc6, 0xe3, 0xfa, 0x28, 0xe9, 0x59, 0x7f, 0x50, 0x81, 0x85, 0x72, 0xe5, 0x64, 0xca,
	0x03, 0xf3, 0x03, 0x98, 0xec, 0xfb, 0xde, 0x9a, 0x72, 0x36, 0x6f, 0x8c, 0x8b, 0x6e, 0x46, 0xe9,
	0x80, 0x4d, 0x23, 0x15, 0x85, 0x75, 0x75, 0x8a, 0xbe, 0x38, 0x85, 0x75, 0xad, 0xe6, 0x57, 0x96,
	0x30, 0x97, 0x6a, 0x7e, 0xa9, 0x88, 0x56, 0x2b, 0xeb, 0xd5, 0x58, 0xa3, 0x0a, 0x47, 0x88, 0x37,
	0xef, 0xc1, 0x14, 0x31, 0x8a, 0x78, 0x33, 0xfe, 0x3b, 0xa5, 0xb2, 0x1c, 0x6d, 0x39, 0xc6, 0xfa,
	0x23, 0x59, 0xb7, 0x32, 0xc2, 0x96, 0x64, 0xae, 0x9f, 0x0c, 0xe4, 0x39, 0xfe, 0x5c, 0xcb, 0x10,
	0x7b, 0xa5, 0x29, 0x91, 0xc3, 0xb3, 0xa0, 0x41, 0x7f, 0x20, 0x84, 0xde, 0x65, 0x8a, 0x2f, 0x98,
	0xea, 0xa1, 0xfa, 0x16, 0x0a, 0xc3, 0xa2, 0x7b, 0x50, 0x17, 0x3c, 0x39, 0xcb, 0x12, 0xcc, 0xb2,
	0xb0, 0x0d, 0x24, 0x88, 0xdc, 0xf4, 0x7b, 0x70, 0x9b, 0xfe, 0x2e, 0x92, 0xb7, 0x7b, 0x39, 0xd2,
	0xf0, 0xaf, 0x2a, 0x23, 0xb7, 0x7e, 0x32, 0x09, 0x77, 0xae, 0x18, 0x4d, 0x7f, 0x8e, 0xe1, 0xf3,
	0x2a, 0xc3, 0xa9, 0xce, 0x2c, 0x19, 0x2e, 0x74, 0xc6, 0x90, 0xb9, 0x72, 0x29, 0xa3, 0x7b, 0xcb,
	0x2c, 0xbd, 0xf9, 0xcb, 0x0a, 0xac, 0x8c, 0xc5, 0xcb, 0xf5, 0xd2, 0xf8, 0xc2, 0xf4, 0xb2, 0xf2,
	0x4c, 0xbd, 0x9c, 0x18, 0xaf, 0x97, 0x93, 0x2f, 0xae, 0x97, 0x78, 0x60, 0x13, 0x37, 0xc3, 0xe8,
	0x3c, 0xcb, 0x7e, 0x60, 0xbb, 0x13, 0x9d, 0xe7, 0x92, 0xee, 0x31, 0x91, 0x15, 0x24, 0x48, 0xc6,
	0x30, 0x91, 0x9a, 0xaf, 0x43, 0xb3, 0xcb, 0x62, 0x07, 0x1d, 0x8d, 0x2f, 0x38, 0x15, 0x2e, 0x64,
	0x37, 0xe4, 0x2e, 0x8b, 0x0f, 0x23, 0xdb, 0x17, 0x1c, 0xf7, 0xfb, 0xf0, 0x1f, 0x2a, 0x30, 0xab,
	0xff, 0x59, 0x2a, 0x73, 0x05, 0xae, 0x6d, 0x6d, 0x6e, 0x6e, 0xac, 0xaf, 0xb5, 0x3e, 0x94, 0x31,
	0x53, 0xbb, 0xf3, 0xd1, 0xda, 0x4e, 0x7b, 0xa3, 0xf9, 0x92, 0x79, 0x07, 0x56, 0xca, 0x5d, 0x3b,
	0xf8, 0x6b, 0x6f, 0xcb, 0x59, 0xdf, 0xde, 0x6d, 0x1a, 0xe6, 0xab, 0x70, 0xbf, 0xdc, 0x8d, 0xff,
	0x1c, 0x6c, 0x1e, 0x1e, 0x62, 0xf8, 0x75, 0xd4, 0x69, 0xed, 0x6c, 0xae, 0xd9, 0xcd, 0x8a, 0x79,
	0x13, 0xae, 0x97, 0xd1, 0x1e, 0xef, 0xed, 0x6f, 0x3a, 0x7b, 0x07, 0x87, 0xcd, 0x89, 0xe1, 0x19,
	0xa8, 0xef, 0x60, 0xf3, 0xdb, 0x47, 0x9b, 0x3b, 0xcd, 0xc9, 0xe1, 0x19, 0x76, 0xda, 0x9d, 0xcd,
	0x03, 0xa7, 0xb5, 0xb7, 0xb3, 0x67, 0x3b, 0xdb, 0x3b, 0x6b, 0x76, 0xbb, 0xb3, 0xdd, 0x9c, 0x32,
	0x2d, 0xb8, 0x5b, 0x46, 0x7b, 0x62, 0xef, 0x75, 0xb6, 0x9d, 0x83, 0xbd, 0xa3, 0xce, 0x86, 0xb3,
	0xbb, 0x66, 0x7f, 0xd8, 0x9c, 0x36, 0x5f, 0x86, 0x7b, 0x65, 0x9c, 0xf5, 0xed, 0x5d, 0xe7, 0x60,
	0xcf, 0xa6, 0xc5, 0x6e, 0xda, 0xf6, 0x9e, 0xdd, 0x9c, 0x31, 0x1f, 0xc0, 0x2b, 0xa3, 0x08, 0x49,
	0x54, 0x24, 0xb6, 0xb9, 0xb5, 0xb5, 0xd9, 0x3a, 0x6c, 0x56, 0x31, 0x02, 0x2d, 0x63, 0xee, 0x1d,
	0x3e, 0xde, 0xb4, 0x9b, 0xb5, 0x87, 0x5b, 0x30, 0x9b, 0x7d, 0x5e, 0xa5, 0xfe, 0xce, 0xd7, 0x92,
	0xde, 0xde, 0x56, 0x1f, 0xfd, 0x35, 0x5f, 0x32, 0x6f, 0xc3, 0xb2, 0xde, 0x93, 0x7d, 0x20, 0x46,
	0xbd, 0xc6, 0xc3, 0xff, 0x65, 0xe4, 0xdf, 0xf0, 0x20, 0x99, 0xdb, 0xb0, 0xbc, 0xd9, 0x39, 0xda,
	0x75, 0xb6, 0x36, 0x77, 0x76, 0xf6, 0x9e, 0x0c, 0x46, 0xcf, 0xcb, 0xb0, 0x34, 0xd4, 0xbb, 0x6e,
	0xef, 0x35, 0x8d, 0x91, 0xe3, 0x76, 0xd6, 0x36, 0x3e, 0xc6, 0xde, 0x0a, 0xb2, 0x7f, 0xa8, 0xb7,
	0xdd, 0x39, 0x6c, 0xef, 0xae, 0x1d, 0x6e, 0x36, 0x27, 0x1e, 0x9e, 0xc1, 0x5c, 0xb9, 0xa4, 0xc8,
	0xbc, 0x0f, 0x77, 0xf4, 0x01, 0xeb, 0xed, 0xce, 0xc6, 0xe0, 0x5a, 0x56, 0xe1, 0xf6, 0x38, 0x94,
	0xf6, 0xb7, 0x8f, 0x36, 0x9b, 0x86, 0x79, 0x0f, 0x6e, 0x8d, 0xc6, 0xd8, 0x3d, 0xda, 0x39, 0x6c,
	0x37, 0x2b, 0x0f, 0xdb, 0x50, 0xdd, 0xcf, 0xa2, 0x70, 0x13, 0xe6, 0xf6, 0xf7, 0x32, 0x04, 0x0a,
	0xe7, 0xe9, 0xb2, 0xa0, 0xc3, 0x28, 0xf6, 0x37, 0xcc, 0x45, 0x98, 0x2f, 0xa0, 0xdb, 0xf6, 0x5a,
	0x67, 0xa3, 0x59, 0x79, 0xf8, 0xff, 0x0d, 0x98, 0x43, 0x1b, 0xb7, 0x8b, 0xc8, 0xe2, 0x36, 0x2c,
	0x1f, 0x1d, 0x6c, 0xda, 0x8e, 0xbd, 0xb7, 0xb7, 0xeb, 0x1c, 0x1c, 0xae, 0x1d, 0x1e, 0x1d, 0x38,
	0xed, 0x0e, 0x35, 0x9b, 0x2f, 0x99, 0x77, 0xe1, 0xe6, 0xa8, 0xde, 0x9d, 0xf6, 0x47, 0xa8, 0x6b,
	0x06, 0x6a, 0xf3, 0xa8, 0xfe, 0xfd, 0x0f, 0x9b, 0x15, 0xe4, 0xce, 0x50, 0xdf, 0x93, 0xb5, 0xc3,
	0xd6, 0xe3, 0x6c, 0xf8, 0xc4, 0xfa, 0xd4, 0x27, 0x13, 0x2c, 0xf6, 0x9f, 0x4e, 0xd3, 0x5f, 0x81,
	0x7c, 0xe7, 0xdf, 0x03, 0x00, 0x00, 0xff, 0xff, 0x72, 0x4a, 0xef, 0x94, 0x24, 0x52, 0x00, 0x00,
}
