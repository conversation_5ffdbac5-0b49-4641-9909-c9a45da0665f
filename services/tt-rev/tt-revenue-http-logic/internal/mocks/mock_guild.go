// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/clients/guild (interfaces: IClient)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	client "golang.52tt.com/pkg/client"
	protocol "golang.52tt.com/pkg/protocol"
	Guild "golang.52tt.com/protocol/services/guildsvr"
)

// MockGuildIClient is a mock of IClient interface.
type MockGuildIClient struct {
	ctrl     *gomock.Controller
	recorder *MockGuildIClientMockRecorder
}

// MockGuildIClientMockRecorder is the mock recorder for MockGuildIClient.
type MockGuildIClientMockRecorder struct {
	mock *MockGuildIClient
}

// NewMockGuildIClient creates a new mock instance.
func NewMockGuildIClient(ctrl *gomock.Controller) *MockGuildIClient {
	mock := &MockGuildIClient{ctrl: ctrl}
	mock.recorder = &MockGuildIClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockGuildIClient) EXPECT() *MockGuildIClientMockRecorder {
	return m.recorder
}

// AddGroupMember mocks base method.
func (m *MockGuildIClient) AddGroupMember(arg0 context.Context, arg1 uint32, arg2 *Guild.AddGroupMemberReq) protocol.ServerError {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddGroupMember", arg0, arg1, arg2)
	ret0, _ := ret[0].(protocol.ServerError)
	return ret0
}

// AddGroupMember indicates an expected call of AddGroupMember.
func (mr *MockGuildIClientMockRecorder) AddGroupMember(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddGroupMember", reflect.TypeOf((*MockGuildIClient)(nil).AddGroupMember), arg0, arg1, arg2)
}

// AddMulticastRelations mocks base method.
func (m *MockGuildIClient) AddMulticastRelations(arg0 context.Context, arg1 []uint32, arg2 map[uint32]string) protocol.ServerError {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddMulticastRelations", arg0, arg1, arg2)
	ret0, _ := ret[0].(protocol.ServerError)
	return ret0
}

// AddMulticastRelations indicates an expected call of AddMulticastRelations.
func (mr *MockGuildIClientMockRecorder) AddMulticastRelations(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddMulticastRelations", reflect.TypeOf((*MockGuildIClient)(nil).AddMulticastRelations), arg0, arg1, arg2)
}

// AllocGroupDisplayId mocks base method.
func (m *MockGuildIClient) AllocGroupDisplayId(arg0 context.Context) (uint32, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AllocGroupDisplayId", arg0)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// AllocGroupDisplayId indicates an expected call of AllocGroupDisplayId.
func (mr *MockGuildIClientMockRecorder) AllocGroupDisplayId(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AllocGroupDisplayId", reflect.TypeOf((*MockGuildIClient)(nil).AllocGroupDisplayId), arg0)
}

// BatGetGuildOfficialByUids mocks base method.
func (m *MockGuildIClient) BatGetGuildOfficialByUids(arg0 context.Context, arg1, arg2 uint32, arg3 []uint32) (*Guild.BatGetGuildOfficialByUidsResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatGetGuildOfficialByUids", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*Guild.BatGetGuildOfficialByUidsResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// BatGetGuildOfficialByUids indicates an expected call of BatGetGuildOfficialByUids.
func (mr *MockGuildIClientMockRecorder) BatGetGuildOfficialByUids(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatGetGuildOfficialByUids", reflect.TypeOf((*MockGuildIClient)(nil).BatGetGuildOfficialByUids), arg0, arg1, arg2, arg3)
}

// BatchDeleteGuildTrans mocks base method.
func (m *MockGuildIClient) BatchDeleteGuildTrans(arg0 context.Context, arg1 []uint32, arg2, arg3, arg4 uint32) protocol.ServerError {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchDeleteGuildTrans", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(protocol.ServerError)
	return ret0
}

// BatchDeleteGuildTrans indicates an expected call of BatchDeleteGuildTrans.
func (mr *MockGuildIClientMockRecorder) BatchDeleteGuildTrans(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchDeleteGuildTrans", reflect.TypeOf((*MockGuildIClient)(nil).BatchDeleteGuildTrans), arg0, arg1, arg2, arg3, arg4)
}

// BatchGetGroup mocks base method.
func (m *MockGuildIClient) BatchGetGroup(arg0 context.Context, arg1 uint32, arg2 *Guild.BatchGetGroupReq) ([]*Guild.MyGroupInfo, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetGroup", arg0, arg1, arg2)
	ret0, _ := ret[0].([]*Guild.MyGroupInfo)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// BatchGetGroup indicates an expected call of BatchGetGroup.
func (mr *MockGuildIClientMockRecorder) BatchGetGroup(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetGroup", reflect.TypeOf((*MockGuildIClient)(nil).BatchGetGroup), arg0, arg1, arg2)
}

// BatchGetGuildTrans mocks base method.
func (m *MockGuildIClient) BatchGetGuildTrans(arg0 context.Context, arg1 []uint32) ([]*Guild.GuildTrans, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetGuildTrans", arg0, arg1)
	ret0, _ := ret[0].([]*Guild.GuildTrans)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// BatchGetGuildTrans indicates an expected call of BatchGetGuildTrans.
func (mr *MockGuildIClientMockRecorder) BatchGetGuildTrans(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetGuildTrans", reflect.TypeOf((*MockGuildIClient)(nil).BatchGetGuildTrans), arg0, arg1)
}

// CC mocks base method.
func (m *MockGuildIClient) CC() client.Conn {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CC")
	ret0, _ := ret[0].(client.Conn)
	return ret0
}

// CC indicates an expected call of CC.
func (mr *MockGuildIClientMockRecorder) CC() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CC", reflect.TypeOf((*MockGuildIClient)(nil).CC))
}

// CheckGuildChairman mocks base method.
func (m *MockGuildIClient) CheckGuildChairman(arg0 context.Context, arg1, arg2 uint32) (bool, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckGuildChairman", arg0, arg1, arg2)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// CheckGuildChairman indicates an expected call of CheckGuildChairman.
func (mr *MockGuildIClientMockRecorder) CheckGuildChairman(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckGuildChairman", reflect.TypeOf((*MockGuildIClient)(nil).CheckGuildChairman), arg0, arg1, arg2)
}

// CheckUserGroupAdminInfoInGuild mocks base method.
func (m *MockGuildIClient) CheckUserGroupAdminInfoInGuild(arg0 context.Context, arg1 uint32, arg2 []uint32, arg3, arg4 uint32) (*Guild.CheckUserGroupAdminInfoInAllGroupResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckUserGroupAdminInfoInGuild", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(*Guild.CheckUserGroupAdminInfoInAllGroupResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// CheckUserGroupAdminInfoInGuild indicates an expected call of CheckUserGroupAdminInfoInGuild.
func (mr *MockGuildIClientMockRecorder) CheckUserGroupAdminInfoInGuild(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckUserGroupAdminInfoInGuild", reflect.TypeOf((*MockGuildIClient)(nil).CheckUserGroupAdminInfoInGuild), arg0, arg1, arg2, arg3, arg4)
}

// CheckUserGuildDuty mocks base method.
func (m *MockGuildIClient) CheckUserGuildDuty(arg0 context.Context, arg1, arg2 uint32) (*Guild.CheckUserGuildDutyResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckUserGuildDuty", arg0, arg1, arg2)
	ret0, _ := ret[0].(*Guild.CheckUserGuildDutyResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// CheckUserGuildDuty indicates an expected call of CheckUserGuildDuty.
func (mr *MockGuildIClientMockRecorder) CheckUserGuildDuty(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckUserGuildDuty", reflect.TypeOf((*MockGuildIClient)(nil).CheckUserGuildDuty), arg0, arg1, arg2)
}

// Close mocks base method.
func (m *MockGuildIClient) Close() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Close")
}

// Close indicates an expected call of Close.
func (mr *MockGuildIClientMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockGuildIClient)(nil).Close))
}

// CreateGroup mocks base method.
func (m *MockGuildIClient) CreateGroup(arg0 context.Context, arg1 uint32, arg2 *Guild.CreateGroupReq) (*Guild.CreateGroupResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateGroup", arg0, arg1, arg2)
	ret0, _ := ret[0].(*Guild.CreateGroupResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// CreateGroup indicates an expected call of CreateGroup.
func (mr *MockGuildIClientMockRecorder) CreateGroup(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateGroup", reflect.TypeOf((*MockGuildIClient)(nil).CreateGroup), arg0, arg1, arg2)
}

// CreateGuild mocks base method.
func (m *MockGuildIClient) CreateGuild(arg0 context.Context, arg1 uint32, arg2 string) (uint32, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateGuild", arg0, arg1, arg2)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// CreateGuild indicates an expected call of CreateGuild.
func (mr *MockGuildIClientMockRecorder) CreateGuild(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateGuild", reflect.TypeOf((*MockGuildIClient)(nil).CreateGuild), arg0, arg1, arg2)
}

// DelMulticastRelations mocks base method.
func (m *MockGuildIClient) DelMulticastRelations(arg0 context.Context, arg1 []uint32, arg2 map[uint32]string) protocol.ServerError {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelMulticastRelations", arg0, arg1, arg2)
	ret0, _ := ret[0].(protocol.ServerError)
	return ret0
}

// DelMulticastRelations indicates an expected call of DelMulticastRelations.
func (mr *MockGuildIClientMockRecorder) DelMulticastRelations(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelMulticastRelations", reflect.TypeOf((*MockGuildIClient)(nil).DelMulticastRelations), arg0, arg1, arg2)
}

// DeleteGuildTrans mocks base method.
func (m *MockGuildIClient) DeleteGuildTrans(arg0 context.Context, arg1, arg2, arg3, arg4 uint32) protocol.ServerError {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteGuildTrans", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(protocol.ServerError)
	return ret0
}

// DeleteGuildTrans indicates an expected call of DeleteGuildTrans.
func (mr *MockGuildIClientMockRecorder) DeleteGuildTrans(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteGuildTrans", reflect.TypeOf((*MockGuildIClient)(nil).DeleteGuildTrans), arg0, arg1, arg2, arg3, arg4)
}

// DismissGroup mocks base method.
func (m *MockGuildIClient) DismissGroup(arg0 context.Context, arg1 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DismissGroup", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// DismissGroup indicates an expected call of DismissGroup.
func (mr *MockGuildIClientMockRecorder) DismissGroup(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DismissGroup", reflect.TypeOf((*MockGuildIClient)(nil).DismissGroup), arg0, arg1)
}

// GenTGroupHeadImage mocks base method.
func (m *MockGuildIClient) GenTGroupHeadImage(arg0 context.Context, arg1 uint32, arg2 string) protocol.ServerError {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GenTGroupHeadImage", arg0, arg1, arg2)
	ret0, _ := ret[0].(protocol.ServerError)
	return ret0
}

// GenTGroupHeadImage indicates an expected call of GenTGroupHeadImage.
func (mr *MockGuildIClientMockRecorder) GenTGroupHeadImage(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GenTGroupHeadImage", reflect.TypeOf((*MockGuildIClient)(nil).GenTGroupHeadImage), arg0, arg1, arg2)
}

// GetGroup mocks base method.
func (m *MockGuildIClient) GetGroup(arg0 context.Context, arg1 uint32) (*Guild.GroupResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGroup", arg0, arg1)
	ret0, _ := ret[0].(*Guild.GroupResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetGroup indicates an expected call of GetGroup.
func (mr *MockGuildIClientMockRecorder) GetGroup(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGroup", reflect.TypeOf((*MockGuildIClient)(nil).GetGroup), arg0, arg1)
}

// GetGroupMember mocks base method.
func (m *MockGuildIClient) GetGroupMember(arg0 context.Context, arg1, arg2 uint32) (*Guild.GroupMemberResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGroupMember", arg0, arg1, arg2)
	ret0, _ := ret[0].(*Guild.GroupMemberResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetGroupMember indicates an expected call of GetGroupMember.
func (mr *MockGuildIClientMockRecorder) GetGroupMember(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGroupMember", reflect.TypeOf((*MockGuildIClient)(nil).GetGroupMember), arg0, arg1, arg2)
}

// GetGroupMemberList mocks base method.
func (m *MockGuildIClient) GetGroupMemberList(arg0 context.Context, arg1, arg2, arg3, arg4 uint32) (*Guild.GroupMemberListResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGroupMemberList", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(*Guild.GroupMemberListResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetGroupMemberList indicates an expected call of GetGroupMemberList.
func (mr *MockGuildIClientMockRecorder) GetGroupMemberList(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGroupMemberList", reflect.TypeOf((*MockGuildIClient)(nil).GetGroupMemberList), arg0, arg1, arg2, arg3, arg4)
}

// GetGroupMemberListAdmin mocks base method.
func (m *MockGuildIClient) GetGroupMemberListAdmin(arg0 context.Context, arg1, arg2, arg3, arg4 uint32) (*Guild.GroupMemberListResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGroupMemberListAdmin", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(*Guild.GroupMemberListResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetGroupMemberListAdmin indicates an expected call of GetGroupMemberListAdmin.
func (mr *MockGuildIClientMockRecorder) GetGroupMemberListAdmin(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGroupMemberListAdmin", reflect.TypeOf((*MockGuildIClient)(nil).GetGroupMemberListAdmin), arg0, arg1, arg2, arg3, arg4)
}

// GetGuild mocks base method.
func (m *MockGuildIClient) GetGuild(arg0 context.Context, arg1 uint32) (*Guild.GuildResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGuild", arg0, arg1)
	ret0, _ := ret[0].(*Guild.GuildResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetGuild indicates an expected call of GetGuild.
func (mr *MockGuildIClientMockRecorder) GetGuild(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGuild", reflect.TypeOf((*MockGuildIClient)(nil).GetGuild), arg0, arg1)
}

// GetGuildBat mocks base method.
func (m *MockGuildIClient) GetGuildBat(arg0 context.Context, arg1 uint32, arg2 *Guild.GetGuildBatReq) (*Guild.GetGuildBatResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGuildBat", arg0, arg1, arg2)
	ret0, _ := ret[0].(*Guild.GetGuildBatResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetGuildBat indicates an expected call of GetGuildBat.
func (mr *MockGuildIClientMockRecorder) GetGuildBat(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGuildBat", reflect.TypeOf((*MockGuildIClient)(nil).GetGuildBat), arg0, arg1, arg2)
}

// GetGuildByShortId mocks base method.
func (m *MockGuildIClient) GetGuildByShortId(arg0 context.Context, arg1 uint32) (*Guild.GuildResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGuildByShortId", arg0, arg1)
	ret0, _ := ret[0].(*Guild.GuildResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetGuildByShortId indicates an expected call of GetGuildByShortId.
func (mr *MockGuildIClientMockRecorder) GetGuildByShortId(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGuildByShortId", reflect.TypeOf((*MockGuildIClient)(nil).GetGuildByShortId), arg0, arg1)
}

// GetGuildCheckinCount mocks base method.
func (m *MockGuildIClient) GetGuildCheckinCount(arg0 context.Context, arg1 uint32, arg2 *Guild.GetGuildCheckinCountReq) (*Guild.GetGuildCheckinCountResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGuildCheckinCount", arg0, arg1, arg2)
	ret0, _ := ret[0].(*Guild.GetGuildCheckinCountResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGuildCheckinCount indicates an expected call of GetGuildCheckinCount.
func (mr *MockGuildIClientMockRecorder) GetGuildCheckinCount(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGuildCheckinCount", reflect.TypeOf((*MockGuildIClient)(nil).GetGuildCheckinCount), arg0, arg1, arg2)
}

// GetGuildCheckinList mocks base method.
func (m *MockGuildIClient) GetGuildCheckinList(arg0 context.Context, arg1 uint32, arg2 *Guild.GetGuildCheckinListReq) (*Guild.GetGuildCheckinListResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGuildCheckinList", arg0, arg1, arg2)
	ret0, _ := ret[0].(*Guild.GetGuildCheckinListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGuildCheckinList indicates an expected call of GetGuildCheckinList.
func (mr *MockGuildIClientMockRecorder) GetGuildCheckinList(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGuildCheckinList", reflect.TypeOf((*MockGuildIClient)(nil).GetGuildCheckinList), arg0, arg1, arg2)
}

// GetGuildDonateCount mocks base method.
func (m *MockGuildIClient) GetGuildDonateCount(arg0 context.Context, arg1 uint32, arg2 *Guild.GetGuildDonateCountReq) (*Guild.GetGuildDonateCountResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGuildDonateCount", arg0, arg1, arg2)
	ret0, _ := ret[0].(*Guild.GetGuildDonateCountResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGuildDonateCount indicates an expected call of GetGuildDonateCount.
func (mr *MockGuildIClientMockRecorder) GetGuildDonateCount(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGuildDonateCount", reflect.TypeOf((*MockGuildIClient)(nil).GetGuildDonateCount), arg0, arg1, arg2)
}

// GetGuildDonateList mocks base method.
func (m *MockGuildIClient) GetGuildDonateList(arg0 context.Context, arg1 uint32, arg2 *Guild.GetGuildDonateListReq) (*Guild.GetGuildDonateListResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGuildDonateList", arg0, arg1, arg2)
	ret0, _ := ret[0].(*Guild.GetGuildDonateListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGuildDonateList indicates an expected call of GetGuildDonateList.
func (mr *MockGuildIClientMockRecorder) GetGuildDonateList(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGuildDonateList", reflect.TypeOf((*MockGuildIClient)(nil).GetGuildDonateList), arg0, arg1, arg2)
}

// GetGuildExtraGameCount mocks base method.
func (m *MockGuildIClient) GetGuildExtraGameCount(arg0 context.Context, arg1 uint32, arg2 *Guild.GetGuildExtraGameCountReq) (*Guild.GetGuildExtraGameCountResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGuildExtraGameCount", arg0, arg1, arg2)
	ret0, _ := ret[0].(*Guild.GetGuildExtraGameCountResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGuildExtraGameCount indicates an expected call of GetGuildExtraGameCount.
func (mr *MockGuildIClientMockRecorder) GetGuildExtraGameCount(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGuildExtraGameCount", reflect.TypeOf((*MockGuildIClient)(nil).GetGuildExtraGameCount), arg0, arg1, arg2)
}

// GetGuildGameCountConfig mocks base method.
func (m *MockGuildIClient) GetGuildGameCountConfig(arg0 context.Context, arg1 uint32, arg2 *Guild.GetGuildGameCountConfigReq) (*Guild.GetGuildGameCountConfigResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGuildGameCountConfig", arg0, arg1, arg2)
	ret0, _ := ret[0].(*Guild.GetGuildGameCountConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGuildGameCountConfig indicates an expected call of GetGuildGameCountConfig.
func (mr *MockGuildIClientMockRecorder) GetGuildGameCountConfig(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGuildGameCountConfig", reflect.TypeOf((*MockGuildIClient)(nil).GetGuildGameCountConfig), arg0, arg1, arg2)
}

// GetGuildGameList mocks base method.
func (m *MockGuildIClient) GetGuildGameList(arg0 context.Context, arg1, arg2 uint32) (*Guild.GetGuildGameListResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGuildGameList", arg0, arg1, arg2)
	ret0, _ := ret[0].(*Guild.GetGuildGameListResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetGuildGameList indicates an expected call of GetGuildGameList.
func (mr *MockGuildIClientMockRecorder) GetGuildGameList(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGuildGameList", reflect.TypeOf((*MockGuildIClient)(nil).GetGuildGameList), arg0, arg1, arg2)
}

// GetGuildGroupList mocks base method.
func (m *MockGuildIClient) GetGuildGroupList(arg0 context.Context, arg1, arg2 uint32) (*Guild.GroupIdListResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGuildGroupList", arg0, arg1, arg2)
	ret0, _ := ret[0].(*Guild.GroupIdListResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetGuildGroupList indicates an expected call of GetGuildGroupList.
func (mr *MockGuildIClientMockRecorder) GetGuildGroupList(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGuildGroupList", reflect.TypeOf((*MockGuildIClient)(nil).GetGuildGroupList), arg0, arg1, arg2)
}

// GetGuildMember mocks base method.
func (m *MockGuildIClient) GetGuildMember(arg0 context.Context, arg1, arg2 uint32) (*Guild.GuildMemberResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGuildMember", arg0, arg1, arg2)
	ret0, _ := ret[0].(*Guild.GuildMemberResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetGuildMember indicates an expected call of GetGuildMember.
func (mr *MockGuildIClientMockRecorder) GetGuildMember(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGuildMember", reflect.TypeOf((*MockGuildIClient)(nil).GetGuildMember), arg0, arg1, arg2)
}

// GetGuildMemberList mocks base method.
func (m *MockGuildIClient) GetGuildMemberList(arg0 context.Context, arg1, arg2 uint32, arg3, arg4 bool) (*Guild.GuildMemberListResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGuildMemberList", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(*Guild.GuildMemberListResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetGuildMemberList indicates an expected call of GetGuildMemberList.
func (mr *MockGuildIClientMockRecorder) GetGuildMemberList(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGuildMemberList", reflect.TypeOf((*MockGuildIClient)(nil).GetGuildMemberList), arg0, arg1, arg2, arg3, arg4)
}

// GetGuildOfficialByUid mocks base method.
func (m *MockGuildIClient) GetGuildOfficialByUid(arg0 context.Context, arg1, arg2, arg3 uint32) (*Guild.GetGuildOfficialByUidResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGuildOfficialByUid", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*Guild.GetGuildOfficialByUidResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetGuildOfficialByUid indicates an expected call of GetGuildOfficialByUid.
func (mr *MockGuildIClientMockRecorder) GetGuildOfficialByUid(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGuildOfficialByUid", reflect.TypeOf((*MockGuildIClient)(nil).GetGuildOfficialByUid), arg0, arg1, arg2, arg3)
}

// GetGuildTrans mocks base method.
func (m *MockGuildIClient) GetGuildTrans(arg0 context.Context, arg1 uint32) (*Guild.GuildTrans, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGuildTrans", arg0, arg1)
	ret0, _ := ret[0].(*Guild.GuildTrans)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetGuildTrans indicates an expected call of GetGuildTrans.
func (mr *MockGuildIClientMockRecorder) GetGuildTrans(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGuildTrans", reflect.TypeOf((*MockGuildIClient)(nil).GetGuildTrans), arg0, arg1)
}

// GetTGroupByDisplayId mocks base method.
func (m *MockGuildIClient) GetTGroupByDisplayId(arg0 context.Context, arg1 uint32) (*Guild.GroupResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTGroupByDisplayId", arg0, arg1)
	ret0, _ := ret[0].(*Guild.GroupResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetTGroupByDisplayId indicates an expected call of GetTGroupByDisplayId.
func (mr *MockGuildIClientMockRecorder) GetTGroupByDisplayId(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTGroupByDisplayId", reflect.TypeOf((*MockGuildIClient)(nil).GetTGroupByDisplayId), arg0, arg1)
}

// GetUserGroupIDListByGuildID mocks base method.
func (m *MockGuildIClient) GetUserGroupIDListByGuildID(arg0 context.Context, arg1, arg2 uint32) ([]uint32, []uint32, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserGroupIDListByGuildID", arg0, arg1, arg2)
	ret0, _ := ret[0].([]uint32)
	ret1, _ := ret[1].([]uint32)
	ret2, _ := ret[2].(protocol.ServerError)
	return ret0, ret1, ret2
}

// GetUserGroupIDListByGuildID indicates an expected call of GetUserGroupIDListByGuildID.
func (mr *MockGuildIClientMockRecorder) GetUserGroupIDListByGuildID(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserGroupIDListByGuildID", reflect.TypeOf((*MockGuildIClient)(nil).GetUserGroupIDListByGuildID), arg0, arg1, arg2)
}

// GetUserGroupList mocks base method.
func (m *MockGuildIClient) GetUserGroupList(arg0 context.Context, arg1, arg2 uint32) ([]*Guild.GroupResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserGroupList", arg0, arg1, arg2)
	ret0, _ := ret[0].([]*Guild.GroupResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetUserGroupList indicates an expected call of GetUserGroupList.
func (mr *MockGuildIClientMockRecorder) GetUserGroupList(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserGroupList", reflect.TypeOf((*MockGuildIClient)(nil).GetUserGroupList), arg0, arg1, arg2)
}

// GroupSetUidNeedVerify mocks base method.
func (m *MockGuildIClient) GroupSetUidNeedVerify(arg0 context.Context, arg1, arg2, arg3 uint32) protocol.ServerError {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GroupSetUidNeedVerify", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(protocol.ServerError)
	return ret0
}

// GroupSetUidNeedVerify indicates an expected call of GroupSetUidNeedVerify.
func (mr *MockGuildIClientMockRecorder) GroupSetUidNeedVerify(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GroupSetUidNeedVerify", reflect.TypeOf((*MockGuildIClient)(nil).GroupSetUidNeedVerify), arg0, arg1, arg2, arg3)
}

// GuildDeleteGames mocks base method.
func (m *MockGuildIClient) GuildDeleteGames(arg0 context.Context, arg1, arg2 uint32, arg3 []uint32) protocol.ServerError {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GuildDeleteGames", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(protocol.ServerError)
	return ret0
}

// GuildDeleteGames indicates an expected call of GuildDeleteGames.
func (mr *MockGuildIClientMockRecorder) GuildDeleteGames(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GuildDeleteGames", reflect.TypeOf((*MockGuildIClient)(nil).GuildDeleteGames), arg0, arg1, arg2, arg3)
}

// ModifyGuildName mocks base method.
func (m *MockGuildIClient) ModifyGuildName(arg0 context.Context, arg1 uint32, arg2 *Guild.ModifyGuildNameReq) (*Guild.ModifyGuildNameResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ModifyGuildName", arg0, arg1, arg2)
	ret0, _ := ret[0].(*Guild.ModifyGuildNameResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// ModifyGuildName indicates an expected call of ModifyGuildName.
func (mr *MockGuildIClientMockRecorder) ModifyGuildName(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ModifyGuildName", reflect.TypeOf((*MockGuildIClient)(nil).ModifyGuildName), arg0, arg1, arg2)
}

// ModifyTGroupDesc mocks base method.
func (m *MockGuildIClient) ModifyTGroupDesc(arg0 context.Context, arg1 uint32, arg2 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ModifyTGroupDesc", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// ModifyTGroupDesc indicates an expected call of ModifyTGroupDesc.
func (mr *MockGuildIClientMockRecorder) ModifyTGroupDesc(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ModifyTGroupDesc", reflect.TypeOf((*MockGuildIClient)(nil).ModifyTGroupDesc), arg0, arg1, arg2)
}

// RemoveGuildOfficialMember mocks base method.
func (m *MockGuildIClient) RemoveGuildOfficialMember(arg0 context.Context, arg1, arg2 uint32) protocol.ServerError {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RemoveGuildOfficialMember", arg0, arg1, arg2)
	ret0, _ := ret[0].(protocol.ServerError)
	return ret0
}

// RemoveGuildOfficialMember indicates an expected call of RemoveGuildOfficialMember.
func (mr *MockGuildIClientMockRecorder) RemoveGuildOfficialMember(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RemoveGuildOfficialMember", reflect.TypeOf((*MockGuildIClient)(nil).RemoveGuildOfficialMember), arg0, arg1, arg2)
}

// SetShortId mocks base method.
func (m *MockGuildIClient) SetShortId(arg0 context.Context, arg1, arg2, arg3 uint32) protocol.ServerError {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetShortId", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(protocol.ServerError)
	return ret0
}

// SetShortId indicates an expected call of SetShortId.
func (mr *MockGuildIClientMockRecorder) SetShortId(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetShortId", reflect.TypeOf((*MockGuildIClient)(nil).SetShortId), arg0, arg1, arg2, arg3)
}

// Stub mocks base method.
func (m *MockGuildIClient) Stub() interface{} {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Stub")
	ret0, _ := ret[0].(interface{})
	return ret0
}

// Stub indicates an expected call of Stub.
func (mr *MockGuildIClientMockRecorder) Stub() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Stub", reflect.TypeOf((*MockGuildIClient)(nil).Stub))
}

// TGroupGetUserJoinGroupList mocks base method.
func (m *MockGuildIClient) TGroupGetUserJoinGroupList(arg0 context.Context, arg1 uint32) ([]uint32, []uint32, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TGroupGetUserJoinGroupList", arg0, arg1)
	ret0, _ := ret[0].([]uint32)
	ret1, _ := ret[1].([]uint32)
	ret2, _ := ret[2].(protocol.ServerError)
	return ret0, ret1, ret2
}

// TGroupGetUserJoinGroupList indicates an expected call of TGroupGetUserJoinGroupList.
func (mr *MockGuildIClientMockRecorder) TGroupGetUserJoinGroupList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TGroupGetUserJoinGroupList", reflect.TypeOf((*MockGuildIClient)(nil).TGroupGetUserJoinGroupList), arg0, arg1)
}

// UnSetShortId mocks base method.
func (m *MockGuildIClient) UnSetShortId(arg0 context.Context, arg1, arg2, arg3 uint32) protocol.ServerError {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UnSetShortId", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(protocol.ServerError)
	return ret0
}

// UnSetShortId indicates an expected call of UnSetShortId.
func (mr *MockGuildIClientMockRecorder) UnSetShortId(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UnSetShortId", reflect.TypeOf((*MockGuildIClient)(nil).UnSetShortId), arg0, arg1, arg2, arg3)
}

// UpdateGroupAllMute mocks base method.
func (m *MockGuildIClient) UpdateGroupAllMute(arg0 context.Context, arg1 uint32, arg2 bool) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateGroupAllMute", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateGroupAllMute indicates an expected call of UpdateGroupAllMute.
func (mr *MockGuildIClientMockRecorder) UpdateGroupAllMute(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateGroupAllMute", reflect.TypeOf((*MockGuildIClient)(nil).UpdateGroupAllMute), arg0, arg1, arg2)
}

// UpdateGroupName mocks base method.
func (m *MockGuildIClient) UpdateGroupName(arg0 context.Context, arg1 uint32, arg2 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateGroupName", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateGroupName indicates an expected call of UpdateGroupName.
func (mr *MockGuildIClientMockRecorder) UpdateGroupName(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateGroupName", reflect.TypeOf((*MockGuildIClient)(nil).UpdateGroupName), arg0, arg1, arg2)
}

// UpdateGuild mocks base method.
func (m *MockGuildIClient) UpdateGuild(arg0 context.Context, arg1 *Guild.UpdateGuildReq) protocol.ServerError {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateGuild", arg0, arg1)
	ret0, _ := ret[0].(protocol.ServerError)
	return ret0
}

// UpdateGuild indicates an expected call of UpdateGuild.
func (mr *MockGuildIClientMockRecorder) UpdateGuild(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateGuild", reflect.TypeOf((*MockGuildIClient)(nil).UpdateGuild), arg0, arg1)
}

// UpdateGuildGameLimit mocks base method.
func (m *MockGuildIClient) UpdateGuildGameLimit(arg0 context.Context, arg1 uint32, arg2 *Guild.UpdateGuildGameLimitReq) (*Guild.UpdateGuildGameLimitResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateGuildGameLimit", arg0, arg1, arg2)
	ret0, _ := ret[0].(*Guild.UpdateGuildGameLimitResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// UpdateGuildGameLimit indicates an expected call of UpdateGuildGameLimit.
func (mr *MockGuildIClientMockRecorder) UpdateGuildGameLimit(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateGuildGameLimit", reflect.TypeOf((*MockGuildIClient)(nil).UpdateGuildGameLimit), arg0, arg1, arg2)
}
