package main

import (
	"context"
	"crypto/tls"
	"fmt"
	beego "github.com/astaxie/beego/config"
	"github.com/globalsign/mgo"
	"github.com/globalsign/mgo/bson"
	"github.com/go-gomail/gomail"
	_ "github.com/go-sql-driver/mysql" // MySQL驱动。
	"github.com/tealeg/xlsx"
	"golang.52tt.com/pkg/config"
	"golang.52tt.com/pkg/log"
	"google.golang.org/grpc"
	"os"
	"strconv"

	channellivestats "golang.52tt.com/clients/channel-live-stats"
	"time"
)

const (
	CActorMission = "channel_live_actor_mission"
)

func InitMongo(mgoName string) *MongoDao {
	cfg, _ := beego.NewConfig("json", "/home/<USER>/lja/channel-live-mission/channel-live-mission.json")

	mgoConfig := config.NewMongoConfigWithSection(cfg, mgoName)
	fmt.Println("url:", mgoConfig.URI())
	sess, err := mgo.DialWithTimeout(mgoConfig.URI(), 60*time.Minute)
	if err != nil {
		log.Errorf("InitMongo err:%v", err)
		panic(err)
	}

	sess.SetPoolLimit(mgoConfig.MaxPoolSize)
	sess.SetMode(mgo.Secondary, true)
	sess.SetSafe(&mgo.Safe{})

	return &MongoDao{
		infoSession: sess,
	}
}

type MongoDao struct {
	infoSession *mgo.Session
	dbName      string
}

func (m *MongoDao) connect(collection string) (*mgo.Session, *mgo.Collection) {
	s := m.infoSession.Copy()
	c := s.DB("").C(collection)
	return s, c
}

type ActorLiveRecord struct {
	Id       string `bson:"_id"`
	Uid      uint32 `bson:"uid,omitempty"`
	Income   uint64 `bson:"income,omitempty"`
	TimeCnt  uint32 `bson:"time_cnt,omitempty"`
	Key      string `bson:"key,omitempty"`
	UpdateTs int64  `bson:"update_ts,omitempty"`
}

func GetWeekAndDayNum(now time.Time) (dayNum, weekNum uint32) {
	nowTs := now.Unix()
	var oneMondayTs int64 = 1590940800 // 2020-06-01 00:00:00 随便一个星期一

	dayNum = uint32((nowTs + 8*3600) / (24 * 3600))
	weekNum = uint32((nowTs-oneMondayTs)/(7*24*3600)) + 1

	return
}

/*
func GetDayTimeByKey(dayKey string) (time.Time, error) {
	keyList := strings.Split(dayKey, "-")
	if len(keyList) != 2 {
		return time.Time{}, errors.New("key err")
	}

	dayNum := strconv.Atoi(keyList[1])
}
*/

func (m *MongoDao) GetActorDayRecordList(beginTm, endTm time.Time, offset, limit int, isDay bool) ([]*ActorLiveRecord, error) {
	s, c := m.connect(CActorMission)
	defer s.Close()

	list := make([]*ActorLiveRecord, 0)
	beginDayKey, beginWeekKey := GetWeekAndDayNum(beginTm)
	endDayKey, endWeekKey := GetWeekAndDayNum(endTm)

	var err error
	if isDay {
		c.Find(bson.M{"key": bson.M{"$gte": fmt.Sprintf("day-%d", beginDayKey), "$lte": fmt.Sprintf("day-%d", endDayKey)}}).Limit(limit).Skip(offset).All(&list)
	} else {
		if beginTm.Weekday() != time.Monday {
			beginWeekKey = beginWeekKey + 1
		}
		c.Find(bson.M{"key": bson.M{"$gte": fmt.Sprintf("week-%d", beginWeekKey), "$lte": fmt.Sprintf("week-%d", endWeekKey)}}).Limit(limit).Skip(offset).All(&list)
	}
	if err != nil {
		log.Errorf("GetActorDayRecordList fail. err:%v", err)
		return nil, err
	}

	return list, nil
}

func sendMail(filePathList []string, header string) error {
	m := gomail.NewMessage()
	m.SetHeader("From", "<EMAIL>")
	m.SetHeader("To", "<EMAIL>", "<EMAIL>", "<EMAIL>")
	//m.SetHeader("To", "<EMAIL>")
	m.SetHeader("Subject", header)

	m.SetBody("text/html", "数据表在附件")
	for _, filePath := range filePathList {
		m.Attach(filePath) //附件
	}

	d := gomail.NewDialer("mail.52tt.com", 465, "<EMAIL>", "V7.D.sTy@%bDB50t#n.r$A4h+-FnXA")

	d.TLSConfig = &tls.Config{InsecureSkipVerify: true}
	if err := d.DialAndSend(m); err != nil {
		fmt.Println(err)
		return err
	}
	return nil
}

func main() {
	nowTm := time.Now()
	//nowTm := time.Unix(1683507600, 0)
	firstDayMonth := time.Date(nowTm.Year(), nowTm.Month(), 1, 0, 0, 0, 0, time.Local)
	lastMonthEnd := time.Unix(firstDayMonth.Unix()-1, 0)
	lastMonthBegin := time.Date(lastMonthEnd.Year(), lastMonthEnd.Month(), 1, 0, 0, 0, 0, time.Local)

	var pathDay string
	var pathWeek string

	fileDay := xlsx.NewFile()
	fileWeek := xlsx.NewFile()

	liveStatsCli := channellivestats.NewIClient(grpc.WithBlock())
	ctx := context.Background()

	// 主播每日数据
	{
		sheet, err := fileDay.AddSheet("主播日维度任务数据")
		if err != nil {
			log.Errorf("file.AddSheet failed err:%v", err)
			return
		}

		row := sheet.AddRow()
		slice := []string{"主播uid", "时长", "流水", "day"}
		row.WriteSlice(&slice, -1)

		beginTm := time.Unix(lastMonthBegin.Unix()-1, 0)
		endTm := lastMonthEnd

		err = ExportAnchorRecordData(ctx, fileDay, beginTm, endTm, sheet, true, liveStatsCli)
		if err != nil {
			log.Errorf("ExportAnchorRecordData failed err:%v", err)
			return
		}

		beginDayKey, _ := GetWeekAndDayNum(beginTm)
		endDayKey, _ := GetWeekAndDayNum(endTm)

		pathDay = fmt.Sprintf("主播日维度任务数据%04d年%02d月(day-%d,day-%d).xlsx", lastMonthBegin.Year(), lastMonthBegin.Month(), beginDayKey, endDayKey)
		fileDay.Save(pathDay)
	}

	// 主播每周数据
	{
		sheet, err := fileWeek.AddSheet("主播周维度任务数据")
		if err != nil {
			log.Errorf("file.AddSheet failed err:%v", err)
			return
		}

		row := sheet.AddRow()
		slice := []string{"主播uid", "时长", "流水", "week"}
		row.WriteSlice(&slice, -1)

		beginTm := lastMonthBegin
		endTm := lastMonthEnd

		err = ExportAnchorRecordData(ctx, fileWeek, beginTm, endTm, sheet, false, liveStatsCli)
		if err != nil {
			log.Errorf("ExportAnchorRecordData failed err:%v", err)
			return
		}

		_, beginWeekKey := GetWeekAndDayNum(beginTm)
		_, endWeekKey := GetWeekAndDayNum(endTm)

		log.Infof("GetWeekAndDayNum beginTm:%d", beginTm.Unix())

		if beginTm.Weekday() != time.Monday {
			beginWeekKey = beginWeekKey + 1
		}
		pathWeek = fmt.Sprintf("主播周维度任务数据%04d年%02d月(week-%d,week-%d).xlsx", lastMonthBegin.Year(), lastMonthBegin.Month(), beginWeekKey, endWeekKey)
		fileWeek.Save(pathWeek)
	}

	err := sendMail([]string{pathDay, pathWeek}, "主播任务数据")
	if err != nil {
		log.Errorf("sendMail failed err:%v", err)
	}

	os.Remove(pathDay)
	os.Remove(pathWeek)

}

func ExportAnchorRecordData(ctx context.Context, file *xlsx.File, beginTm, endTm time.Time, sheet *xlsx.Sheet, isDay bool, liveStatsCli channellivestats.IClient) error {
	liveMissionMongo := InitMongo("liveMissionMongo")

	log.Infof("ExportAnchorRecordData begin b:%v e:%v isDay:%v", beginTm, endTm, isDay)

	list := make([]*ActorLiveRecord, 0)
	offset := 0
	limit := 1000
	for {

		log.Infof("ExportAnchorRecordData offset:%d limit:%d", offset, limit)

		tmplist, err := liveMissionMongo.GetActorDayRecordList(beginTm, endTm, offset, limit, isDay)
		if err != nil {
			log.Errorf("ExportAnchorRecordData GetActorDayRecordList failed b:%v e:%v isDay:%v err:%v", beginTm, endTm, isDay, err)
			return err
		}

		list = append(list, tmplist...)

		if len(tmplist) < limit {
			break
		}

		offset = offset + limit
	}

	mapUid2Is := make(map[uint32]bool, 0)
	uidList := make([]uint32, 0)
	for _, info := range list {
		if !mapUid2Is[info.Uid] {
			uidList = append(uidList, info.Uid)
			mapUid2Is[info.Uid] = true
		}
	}

	log.InfoWithCtx(ctx, "ExportAnchorRecordData uidList:%d", len(uidList))

	if isDay {
		for tm := beginTm; tm.Unix() <= endTm.Unix(); tm = tm.AddDate(0, 0, 1) {
			for _, uid := range uidList {
				liveStatsResp, sErr := liveStatsCli.GetAnchorDailyRecordWithDateList(ctx, uid, uid, uint32(tm.Unix()), uint32(tm.Unix()))
				if sErr != nil {
					log.ErrorWithCtx(ctx, "ExportAnchorRecordData GetAnchorDailyRecordWithDateList failed uid:%d err:%v", uid, sErr)
					return sErr
				}

				if len(liveStatsResp.GetList()) != 0 {
					tmKey, _ := GetWeekAndDayNum(tm)

					row := sheet.AddRow()
					cell := row.AddCell()
					cell.Value = strconv.Itoa(int(uid))
					cell = row.AddCell()
					cell.Value = strconv.Itoa(int(liveStatsResp.GetList()[0].GetLiveMinutes() * 60))
					cell = row.AddCell()
					cell.Value = strconv.Itoa(int(liveStatsResp.GetList()[0].GetAnchorIncome() + liveStatsResp.GetList()[0].GetKnightIncome()))
					cell = row.AddCell()
					cell.Value = fmt.Sprintf("day-%d", tmKey)

					log.Infof("ExportAnchorRecordData day uid:%d tm:%v liveStatsResp:%v", uid, tm, liveStatsResp)
				}
			}
		}
	} else {
		firstWeek := beginTm.AddDate(0, 0, -(int(beginTm.Weekday())-1+7)%7)

		for tm := firstWeek; tm.Unix() <= endTm.Unix(); tm = tm.AddDate(0, 0, 7) {
			for _, uid := range uidList {
				liveStatsResp, sErr := liveStatsCli.BatchGetAnchorWeeklyRecord(ctx, 0, 0, []uint32{uid}, uint32(tm.Unix()), uint32(tm.AddDate(0, 0, 6).Unix()))
				if sErr != nil {
					log.ErrorWithCtx(ctx, "ExportAnchorRecordData BatchGetAnchorWeeklyRecord failed uid:%d err:%v", uid, sErr)
					return sErr
				}

				if len(liveStatsResp.GetList()) != 0 {
					_, tmKey := GetWeekAndDayNum(tm)

					row := sheet.AddRow()
					cell := row.AddCell()
					cell.Value = strconv.Itoa(int(uid))
					cell = row.AddCell()
					cell.Value = strconv.Itoa(int(liveStatsResp.GetList()[0].GetValidDaysCnt()))
					cell = row.AddCell()
					cell.Value = strconv.Itoa(int(liveStatsResp.GetList()[0].GetAnchorIncome() + liveStatsResp.GetList()[0].GetAnchorKnightIncome()))
					cell = row.AddCell()
					cell.Value = fmt.Sprintf("week-%d", tmKey)

					log.Infof("ExportAnchorRecordData week uid:%d tm:%v liveStatsResp:%v", uid, tm, liveStatsResp)
				}
			}
		}
	}

	log.Infof("ExportAnchorRecordData end b:%v e:%v isDay:%v size:%d", beginTm, endTm, isDay, len(list))
	return nil
}
