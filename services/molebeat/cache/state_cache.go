package cache

import (
    "context"
    "encoding/json"
    "errors"
    "fmt"
    "golang.52tt.com/services/molebeat/conf"
    "golang.52tt.com/services/molebeat/mongo"
    "sort"
    "strconv"
    "strings"
    "time"

    "github.com/go-redis/redis"
    "gitlab.ttyuyin.com/avengers/tyr/compatible/proto"
    pb "golang.52tt.com/protocol/services/molebeat"

    "golang.52tt.com/pkg/hash/hashdb"
    "golang.52tt.com/pkg/log"
)

const timeFormat = "2006-01-02T15:04"
const dateFormat = "2006-01-02"

type StatCache struct {
    redisStatClient *hashdb.KHashDB
}

func NewStatCache(r *hashdb.KHashDB) *StatCache {
    return &StatCache{redisStatClient: r}
}

type UserLotteryResultInfo struct {
    Item conf.LotteryResultItem `json:"item"`
    Uid  uint32                 `json:"uid"`
    Ts   uint32                 `json:"ts"`
    Chid uint32                 `json:"chid"`
}

type ScoreLotteryInfo struct {
    score int64
    info  *UserLotteryResultInfo
}

type ScoreLotteryInfos []*ScoreLotteryInfo

const (
    OffsetPriceSort         = 10000
    StatRedisName1          = "stat_redis1"
    StatRedisName2          = "stat_redis2"
    StatRedisName3          = "stat_redis3"
    StatRedisName4          = "stat_redis4"
    StatRedisName5          = "stat_redis5"
    UserLotterySortCacheKey = "user_lottery_sort"
)

// 存于某个redis
func (r *StatCache) SaveLimitLotteryHistory(ctx context.Context, gameid, realPriceSort uint32, lotteryInfo *UserLotteryResultInfo) error {
    log.DebugWithCtx(ctx, "SaveLimitLotteryHistory gameid: %d,price: %d,ts: %d", gameid, realPriceSort, lotteryInfo.Ts)
    score := Sum2Score(gameid*OffsetPriceSort+realPriceSort, lotteryInfo.Ts)
    strInfo, err := json.Marshal(lotteryInfo)
    if err != nil {
        log.ErrorWithCtx(ctx, "SaveLimitLotteryHistory func Marshal err : %s", err.Error())
        return err
    }
    //strUid := strconv.Itoa(int(lotteryInfo.Uid))
    client, err := r.redisStatClient.GetRdbByNode(StatRedisName1)
    if err != nil {
        log.ErrorWithCtx(ctx, "[SaveLimitLotteryHistory]GetRdb err UserLimitLotteryCacheKey: %s err: %s", StatRedisName1, err.Error())
        return err
    }

    err = client.Client().ZAdd(UserLotterySortCacheKey, redis.Z{
        Score:  score,
        Member: string(strInfo),
    }).Err()
    if err != nil {
        log.ErrorWithCtx(ctx, "ZIncrBy err : %s", err.Error())
        //return err
    }
    return nil
}

func (r *StatCache) LoadLimitLotteryHistory(ctx context.Context, num uint32) ([]*UserLotteryResultInfo, error) {
    client, err := r.redisStatClient.GetRdbByNode(StatRedisName1)
    if err != nil {
        log.ErrorWithCtx(ctx, "[LoadLimitLotteryHistory]GetRdb err UserLimitLotteryCacheKey: %s err: %s", StatRedisName1, err.Error())
        return nil, err
    }
    cnt := num
    if cnt > 50 || cnt == 0 {
        cnt = 50
    }
    var infos = make(ScoreLotteryInfos, 0, cnt)
    z, err := client.Client().ZRevRangeWithScores(UserLotterySortCacheKey, 0, int64(cnt)).Result()
    if err != nil {
        log.ErrorWithCtx(ctx, "LoadLimitLotteryHistory ZRevRangeByScoreWithScores err : %s", err.Error())
        return nil, err
    }
    for _, m := range z {
        info := &UserLotteryResultInfo{}
        userInfoMember, ok := m.Member.(string)
        if !ok {
            continue
        }
        err = json.Unmarshal([]byte(userInfoMember), info)
        if err != nil {
            log.ErrorWithCtx(ctx, "UserLotteryResultInfo Unmarshal err : %s", err.Error())
            continue
        }
        infos = append(infos, &ScoreLotteryInfo{
            score: int64(m.Score),
            info:  info,
        })
    }

    infos.Sort()
    size := len(infos)
    //if size > 50 {
    //remNum := size - 50
    //r.copysToRem = make([]*UserLotteryResultInfo, 0)
    //for i := 0; i < remNum; i++ {
    //	r.copysToRem = append(r.copysToRem, infos[50+i].info)
    //}
    //size = 50
    //}
    result := make([]*UserLotteryResultInfo, 0, size)
    for _, v := range infos {
        result = append(result, v.info)
    }
    return result, nil
}

func (r *StatCache) RemoveLimitLotteryHistory(ctx context.Context) error {
    //log.DebugWithCtx(ctx, " to RemoveLimitLotteryHistory")
    client, err := r.redisStatClient.GetRdbByNode(StatRedisName1)
    if err != nil {
        log.ErrorWithCtx(ctx, "[RemoveLimitLotteryHistory]GetRdb err UserLimitLotteryCacheKey: %s err: %s", StatRedisName1, err.Error())
        return err
    }

    err = client.Client().ZRemRangeByRank(UserLotterySortCacheKey, 0, -51).Err()
    if err != nil {
        log.ErrorWithCtx(ctx, "RemoveLimitLotteryHistory, ZRemRangeByRank key: %s err: %s", UserLotterySortCacheKey, err.Error())
        return err
    }

    //if len(r.copysToRem) != 0 {
    //	remList := make([]string, 0)
    //	for _, v := range r.copysToRem {
    //		str, err := json.Marshal(v)
    //		if err != nil {
    //			log.ErrorWithCtx(ctx,"Marshal err : %s", err.Error())
    //			return err
    //		}
    //		remList = append(remList, string(str))
    //	}
    //	clients := r.redisHashClient.GetRdbs()
    //	for _, v := range clients {
    //		err := v.Client().ZRem(UserLimitLotteryCacheKey, remList).Err()
    //		if err != nil {
    //			log.ErrorWithCtx(ctx,"ZRem err : %s", err.Error())
    //			return err
    //		}
    //	}
    //	r.copysToRem = make([]*UserLotteryResultInfo, 0, 10)
    //}
    return nil
}

func (r *StatCache) getUserRealPrice(gametime string) string {
    return fmt.Sprintf("mole_realprice_%s", gametime)
}

func (r *StatCache) SaveUserRealPrice(ctx context.Context, gametime int64, uid, chid, sortPrice uint32) (err error) {
    client, err := r.redisStatClient.GetRdbByNode(StatRedisName1)
    if err != nil {
        log.ErrorWithCtx(ctx, "[SaveUserRealPrice]GetRdbByNode err:%s", err.Error())
        return
    }
    gameTime := time.Unix(gametime, 0).Format(timeFormat)
    key := r.getUserRealPrice(gameTime)
    member := fmt.Sprintf("%d_%d", uid, chid)
    err = client.Client().ZIncrBy(key, float64(sortPrice), member).Err()
    if err != nil {
        log.ErrorWithCtx(ctx, "SaveUserRealPrice ZIncrBy err:%s", err.Error())
    }
    return
}

func (r *StatCache) GetUserRealPrice(ctx context.Context, gametime int64) (uid, chid uint32, err error) {
    client, err := r.redisStatClient.GetRdbByNode(StatRedisName1)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetRdb err:%s", err.Error())
        return
    }
    gameTime := time.Unix(gametime, 0).Format(timeFormat)
    key := r.getUserRealPrice(gameTime)
    members, err := client.Client().ZRevRangeWithScores(key, 0, 1).Result()
    if err != nil {
        if err.Error() == "redis: nil" {
            return 0, 0, nil
        }
        log.ErrorWithCtx(ctx, "GetUserRealPrice ZRevRangeByScoreWithScores err:%s", err.Error())
        return 0, 0, err
    }
    if len(members) != 0 {
        m, ok := members[0].Member.(string)
        if !ok {
            log.ErrorWithCtx(ctx, "ch string err, %s", members[0].Member)
            return 0, 0, errors.New("ch string err")
        }
        memberSlice := strings.Split(m, "_")
        if len(memberSlice) != 2 {
            log.ErrorWithCtx(ctx, "len err, %s", m)
            return 0, 0, errors.New("len err")
        }
        nUid, err := strconv.Atoi(memberSlice[0])
        if err != nil {
            return 0, 0, errors.New("Atoi err")
        }
        nChid, err := strconv.Atoi(memberSlice[1])
        if err != nil {
            return 0, 0, errors.New("Atoi err")
        }
        return uint32(nUid), uint32(nChid), nil
    }
    return 0, 0, nil
}

func (r *StatCache) getUserTbeanPrice(gameTime string) string {
    return fmt.Sprintf("user_tbean_price_%s", gameTime)
}

func (r *StatCache) getUserTbeanPriceTotal(dayGameTime string) string {
    return fmt.Sprintf("user_tbean_price_total_%s", dayGameTime)
}

func (r *StatCache) SaveUserTbeanPrice(ctx context.Context, gameTime, tbeanPrice uint32) (err error) {
    configGameTime := time.Unix(int64(gameTime), 0).Format(timeFormat)
    client, err := r.redisStatClient.GetRdbByNode(StatRedisName1)
    if err != nil {
        log.ErrorWithCtx(ctx, "[SaveUserTbeanPrice]GetRdbByNode err:%s", err.Error())
        return
    }
    key := r.getUserTbeanPrice(configGameTime)
    err = client.Client().HIncrBy(key, "user_sum", 1).Err()
    if err != nil {
        log.ErrorWithCtx(ctx, "SaveUserTbeanPrice ZIncrBy user_sum err:%s, key:%s", err.Error(), key)
    }
    err = client.Client().HIncrBy(key, "price_sum", int64(tbeanPrice)).Err()
    if err != nil {
        log.ErrorWithCtx(ctx, "SaveUserTbeanPrice ZIncrBy price_sum err:%s, key:%s", err.Error(), key)
    }

    dayGameTime := time.Unix(int64(gameTime), 0).Format(dateFormat)
    key2 := r.getUserTbeanPriceTotal(dayGameTime)
    err = client.Client().IncrBy(key2, int64(tbeanPrice)).Err()
    if err != nil {
        log.ErrorWithCtx(ctx, "SaveUserTbeanPrice IncrBy err:%s, key2:%s", err.Error(), key2)
    }
    return err
}

func (r *StatCache) GetUserGameTbeanPrice(ctx context.Context, gameTime int64) (beanSum string, err error) {
    client, err := r.redisStatClient.GetRdbByNode(StatRedisName1)
    if err != nil {
        log.ErrorWithCtx(ctx, "[GetUserGameTbeanPrice]GetRdbByNode err:%s", err.Error())
        return
    }
    configGameTime := time.Unix(gameTime, 0).Format(timeFormat)
    key := r.getUserTbeanPrice(configGameTime)
    sum, err := client.Client().HGet(key, "price_sum").Result()
    return sum, err
}

func (r *StatCache) GetUserTbeanPrice(ctx context.Context, gameTime int64) (userSum, TbeanSum, totalSum uint32, err error) {
    client, err := r.redisStatClient.GetRdbByNode(StatRedisName1)
    if err != nil {
        log.ErrorWithCtx(ctx, "[GetUserTbeanPrice]GetRdbByNode err:%s", err.Error())
        return
    }
    configGameTime := time.Unix(gameTime, 0).Format(timeFormat)
    key := r.getUserTbeanPrice(configGameTime)
    sums, _ := client.Client().HMGet(key, []string{"user_sum", "price_sum"}...).Result()
    if len(sums) != 2 {
        return 0, 0, 0, errors.New("统计数据个数不为2")
    }

    dayGameTime := time.Unix(gameTime, 0).Format(dateFormat)
    key2 := r.getUserTbeanPriceTotal(dayGameTime)
    ret, err := client.Client().Get(key2).Result()
    if err != nil {
        if err.Error() != "redis: nil" {
            return 0, 0, 0, err
        }
    }
    if sums[0] != nil {
        if u, ok := sums[0].(string); !ok {
            return 0, 0, 0, errors.New("Trans to string failed!")
        } else {
            cnt, err := strconv.Atoi(u)
            if err != nil {
                return 0, 0, 0, err
            }
            userSum = uint32(cnt)
        }
    }

    if sums[1] != nil {
        if t, ok := sums[1].(string); !ok {
            return 0, 0, 0, errors.New("Trans to string failed!")
        } else {
            cnt, err := strconv.Atoi(t)
            if err != nil {
                return 0, 0, 0, err
            }
            TbeanSum = uint32(cnt)
        }
    }
    if ret != "" {
        total, err := strconv.Atoi(ret)
        if err != nil {
            return 0, 0, 0, errors.New("strconv Trans to int failed!")
        }
        totalSum = uint32(total)
    }
    return
}

func (s ScoreLotteryInfos) Len() int           { return len(s) }
func (s ScoreLotteryInfos) Less(i, j int) bool { return s[i].score > s[j].score }
func (s ScoreLotteryInfos) Swap(i, j int)      { s[i], s[j] = s[j], s[i] }
func (s ScoreLotteryInfos) Sort()              { sort.Sort(s) }

type LotteryStat struct {
    //uid    uint32
    //giftId string
    //giftName string
}

func (r *StatCache) SaveStatLottery(ctx context.Context, gameConfigBeginTime int64, uid uint32, sourceId uint32, sourceKey string, giftName string) error {
    gameTime := time.Unix(gameConfigBeginTime, 0).Format(timeFormat)
    key := fmt.Sprintf("stat_lottery_%s", gameTime)
    client, err := r.redisStatClient.GetRdbByNode(StatRedisName2)
    if err != nil {
        log.ErrorWithCtx(ctx, "[SaveStatLottery]GetRdbByNode err:%s, %s", err.Error())
        return err
    }
    strUid := strconv.Itoa(int(uid))

    lotteryStat := &pb.StatLottery{
        Uid:      uid,
        Giftname: giftName,
    }
    if sourceId != 0 {
        lotteryStat.Giftid = strconv.Itoa(int(sourceId))
    } else {
        lotteryStat.Giftid = sourceKey
    }
    byteStat, err := proto.Marshal(lotteryStat)
    if err != nil {
        return err
    }
    err = client.Client().HSet(key, strUid, string(byteStat)).Err()
    if err != nil {
        log.ErrorWithCtx(ctx, "SaveStatLottery HSet err:%s", err.Error())
        return err
    }
    return nil
}

func (r *StatCache) GetStatLottery(ctx context.Context, gameTime string) (map[string]string, error) {
    key := fmt.Sprintf("stat_lottery_%s", gameTime)
    client, err := r.redisStatClient.GetRdbByNode(StatRedisName2)
    if err != nil {
        log.ErrorWithCtx(ctx, "[GetStatLottery]GetRdbByNode err:%s, %s", err.Error())
        return nil, err
    }
    res, err := client.Client().HGetAll(key).Result()
    if err != nil {
        log.ErrorWithCtx(ctx, "GetStatLottery HGetAll err:%s", err.Error())
        return nil, err
    }
    return res, err
}

func (r *StatCache) SaveLotteryUser(ctx context.Context, uid uint32) error {
    key := "stat_uid"
    client, err := r.redisStatClient.GetRdbByNode(StatRedisName2)
    if err != nil {
        log.ErrorWithCtx(ctx, "[SaveLotteryUser]GetRdbByNode err:%s, %s", err.Error())
        return err
    }
    strUid := strconv.Itoa(int(uid))
    err = client.Client().HSet(key, strUid, 1).Err()
    if err != nil {
        log.ErrorWithCtx(ctx, "SaveLotteryUser:%s", err.Error())
        return err
    }
    return nil
}

func (r *StatCache) GetLotteryUser(ctx context.Context) (map[string]string, error) {
    key := "stat_uid"
    client, err := r.redisStatClient.GetRdbByNode(StatRedisName2)
    if err != nil {
        log.ErrorWithCtx(ctx, "[GetLotteryUser]GetRdbByNode err:%s, %s", err.Error())
        return nil, err
    }
    res, err := client.Client().HGetAll(key).Result()
    if err != nil {
        log.ErrorWithCtx(ctx, "GetLotteryUser err:%s", err.Error())
        return nil, err
    }
    return res, err
}

func (r *StatCache) SaveLotteryGive(ctx context.Context, awardName string) error {
    nowTime := time.Now().Format(dateFormat)
    key := fmt.Sprintf("stat_give_%s", nowTime)
    client, err := r.redisStatClient.GetRdbByNode(StatRedisName2)
    if err != nil {
        log.ErrorWithCtx(ctx, "[SaveLotteryGive]GetRdbByNode err:%s, %s", err.Error())
        return err
    }
    err = client.Client().HIncrBy(key, awardName, 1).Err()
    if err != nil {
        log.ErrorWithCtx(ctx, "SaveLotteryGive:%s", err.Error())
        return err
    }
    return nil
}

func (r *StatCache) GetLotteryGive(ctx context.Context, gameTime string) (map[string]string, error) {
    key := fmt.Sprintf("stat_give_%s", gameTime)
    client, err := r.redisStatClient.GetRdbByNode(StatRedisName2)
    if err != nil {
        log.ErrorWithCtx(ctx, "[GetLotteryGive]GetRdbByNode err:%s, %s", err.Error())
        return nil, err
    }
    res, err := client.Client().HGetAll(key).Result()
    if err != nil {
        log.ErrorWithCtx(ctx, "GetLotteryGive err:%s", err.Error())
        return nil, err
    }
    return res, err
}

const (
    HIT        = 1
    GET        = 2
    LOW_CNT    = 3
    MIDDLE_CNT = 4
    HIGH_CNT   = 5
)

//统计：
func (r *StatCache) SaveHitCnt(ctx context.Context, gameConfigBeginTime int64, hitType uint32) error {
    gameTime := time.Unix(gameConfigBeginTime, 0).Format(timeFormat)
    key := r.getStatGameDetailKey(gameTime)
    client, err := r.redisStatClient.GetRdbByNode(StatRedisName3)
    if err != nil {
        log.ErrorWithCtx(ctx, "[SaveHitCnt]GetRdbByNode err:%s, %s", err.Error())
        return err
    }
    log.InfoWithCtx(ctx, "SaveHitCnt type:%d,key:%s", hitType, key)
    if hitType == HIT {
        err = client.Client().HIncrBy(key, "hit", 1).Err()
    } else if hitType == GET {
        err = client.Client().HIncrBy(key, "get", 1).Err()
    } else if hitType == LOW_CNT {
        err = client.Client().HIncrBy(key, "low_cnt", 1).Err()
    } else if hitType == MIDDLE_CNT {
        err = client.Client().HIncrBy(key, "middle_cnt", 1).Err()
    } else if hitType == HIGH_CNT {
        err = client.Client().HIncrBy(key, "high_cnt", 1).Err()
    } else {
        log.ErrorWithCtx(ctx, "unknow type:%d", hitType)
    }
    if err != nil {
        log.ErrorWithCtx(ctx, "SaveHitCnt err:%s", err.Error())
    }
    return nil
}

func (r *StatCache) SaveHitCnt2(ctx context.Context, gameConfigBeginTime int64, hitType uint32) error {
    gameTime := time.Unix(gameConfigBeginTime, 0).Format(timeFormat)
    key := r.getStatGameDetailKey(gameTime)
    client, err := r.redisStatClient.GetRdbByNode(StatRedisName2)
    if err != nil {
        log.ErrorWithCtx(ctx, "[SaveHitCnt2]GetRdbByNode err:%s, %s", err.Error())
        return err
    }
    log.InfoWithCtx(ctx, "SaveHitCnt type:%d,key:%s", hitType, key)
    if hitType == HIT {
        err = client.Client().HIncrBy(key, "hit", 1).Err()
    } else if hitType == GET {
        err = client.Client().HIncrBy(key, "get", 1).Err()
    } else if hitType == LOW_CNT {
        err = client.Client().HIncrBy(key, "low_cnt", 1).Err()
    } else if hitType == MIDDLE_CNT {
        err = client.Client().HIncrBy(key, "middle_cnt", 1).Err()
    } else if hitType == HIGH_CNT {
        err = client.Client().HIncrBy(key, "high_cnt", 1).Err()
    } else {
        log.ErrorWithCtx(ctx, "unknow type:%d", hitType)
    }
    if err != nil {
        log.ErrorWithCtx(ctx, "SaveHitCnt err:%s", err.Error())
    }
    return nil
}

func (r *StatCache) Test(ctx context.Context) error {
    client, err := r.redisStatClient.GetRdbByNode(StatRedisName3)
    if err != nil {
        log.ErrorWithCtx(ctx, "[Test]GetRdbByNode err:%s, %s", err.Error())
        return err
    }
    _, err = client.Client().Set("test", "test", 100*time.Hour).Result()
    if err != nil {
        log.ErrorWithCtx(ctx, "[Test]GetHitCnt HGetAll err:%s", err.Error())
        return err
    }

    res, err := client.Client().Get("test").Result()
    if err != nil {
        log.ErrorWithCtx(ctx, "HGetAll err:%s", err.Error())
        return err
    }

    fmt.Println("GetHitCnt:", res)
    return nil
}

func (r *StatCache) GetHitCnt(ctx context.Context, gameTime string) (map[string]string, error) {
    key := r.getStatGameDetailKey(gameTime)
    client, err := r.redisStatClient.GetRdbByNode(StatRedisName3)
    if err != nil {
        log.ErrorWithCtx(ctx, "[GetHitCnt]GetRdbByNode err:%s, %s", err.Error())
        return nil, err
    }
    log.InfoWithCtx(ctx, "GetHitCnt key:%s", key)
    res, err := client.Client().HGetAll(key).Result()
    if err != nil {
        log.ErrorWithCtx(ctx, "[GetHitCnt]GetHitCnt HGetAll err:%s", err.Error())
        return nil, err
    }

    //log.InfoWithCtx(ctx, "GetHitCnt:%+v", res)
    if len(res) == 0 {
        //log.ErrorWithCtx(ctx,"GetHitCnt cnt==0, gametime:%s, res:%+v", gameTime, res)
        return nil, fmt.Errorf("cnt err:%d", len(res))
    }
    return res, nil
}

func (r *StatCache) GetHitCnt2(ctx context.Context, gameTime string) (map[string]string, error) {
    key := r.getStatGameDetailKey(gameTime)
    client, err := r.redisStatClient.GetRdbByNode(StatRedisName3)
    if err != nil {
        log.ErrorWithCtx(ctx, "[GetHitCnt2]GetRdbByNode err:%s, %s", err.Error())
        return nil, err
    }
    log.InfoWithCtx(ctx, "GetHitCnt key:%s", key)
    res, err := client.Client().HGetAll(key).Result()
    if err != nil {
        log.ErrorWithCtx(ctx, "[GetHitCnt2]GetHitCnt HGetAll err:%s", err.Error())
        return nil, err
    }

    //log.InfoWithCtx(ctx, "GetHitCnt:%+v", res)
    if len(res) == 0 {
        //log.ErrorWithCtx(ctx,"GetHitCnt cnt==0, gametime:%s, res:%+v", gameTime, res)
        return nil, fmt.Errorf("cnt err:%d", len(res))
    }
    return res, nil
}

type HitInfoT struct {
    LotteryCnt uint32 `json:"lotterycnt"`
    LowCnt     uint32 `json:"lowcnt"`
    MiddleCnt  uint32 `json:"middlecnt"`
    HighCnt    uint32 `json:"highcnt"`
    BackUpCnt  uint32 `json:"backupcnt"`
}

type UserHitInfoT struct {
    HitInfo *HitInfoT
    HitCnt  uint32
}

//击打
func (r *StatCache) SaveGameRoundHitCnt(ctx context.Context, gameConfigBeginTime int64, hitType uint32, uid uint32) error {
    gameTime := time.Unix(gameConfigBeginTime, 0).Format(timeFormat)
    key := r.getStatGameRound(gameTime, hitType)
    client, err := r.redisStatClient.GetRdbByNode(StatRedisName5)
    if err != nil {
        log.ErrorWithCtx(ctx, "[SaveGameRoundHitCnt]GetRdbByNode err:%s, %s", err.Error())
        return err
    }
    if hitType == HIT {
    } else if hitType == GET {
    } else {
        log.ErrorWithCtx(ctx, "unknow type:%d", hitType)
        return errors.New("type err")
    }

    strUid := strconv.Itoa(int(uid))
    err = client.Client().HSet(key, strUid, 1).Err()
    if err != nil {
        log.ErrorWithCtx(ctx, "SaveDayHitCnt:%s", err.Error())
        return err
    }
    return nil
}

//中奖
func (r *StatCache) SaveGameRoundLotteryInfo(ctx context.Context, gameConfigBeginTime int64, hitType uint32, uid uint32, cntType uint32) error {
    gameTime := time.Unix(gameConfigBeginTime, 0).Format(timeFormat)
    key := r.getStatGameRound(gameTime, hitType)
    client, err := r.redisStatClient.GetRdbByNode(StatRedisName4)
    if err != nil {
        log.ErrorWithCtx(ctx, "[SaveGameRoundLotteryInfo]GetRdbByNode err:%s, %s", err.Error())
        return err
    }
    if hitType == HIT {
    } else if hitType == GET {
    } else {
        log.ErrorWithCtx(ctx, "unknow type:%d", hitType)
        return errors.New("type err")
    }

    strUid := strconv.Itoa(int(uid))

    hitInfo := &HitInfoT{
        LotteryCnt: 1,
        LowCnt:     0,
        MiddleCnt:  0,
        HighCnt:    0,
        BackUpCnt:  0,
    }
    switch cntType {
    default:
        // do nothing
    case mongo.RatioFirstType:
        hitInfo.LowCnt = 1
    case mongo.RatioSecondType:
        hitInfo.MiddleCnt = 1
    case mongo.RatioThirdType:
        hitInfo.HighCnt = 1
    case mongo.RatioLowType:
        hitInfo.BackUpCnt = 1
    }
    hitInfoJson, err := json.Marshal(hitInfo)
    if err != nil {
        log.ErrorWithCtx(ctx, "json.Marshal err:%v, hitinfo:%+v", err, hitInfo)
        return err
    }
    err = client.Client().HSet(key, strUid, hitInfoJson).Err()
    if err != nil {
        log.ErrorWithCtx(ctx, "SaveGameRoundLotteryInfo:%s", err.Error())
        return err
    }
    return nil
}

/*func (r *StatCache) SaveDayHitCnt(gameConfigBeginTime int64, hitType uint32, uid uint32) error {
	gameTime := time.Unix(gameConfigBeginTime, 0).Format(dateFormat)
	key := fmt.Sprintf("stat_gameday_%s_%d", gameTime, hitType)
	client, err := r.redisStatClient.GetRdbByNode(StatRedisName4)
	if err != nil {
		log.ErrorWithCtx(ctx,"GetRdbByNode err:%s, %s", err.Error())
		return err
	}
	if hitType == HIT {
	} else if hitType == GET {
	} else {
		log.ErrorWithCtx(ctx,"unknow type:%d", hitType)
		return errors.New("type err")
	}

	strUid := strconv.Itoa(int(uid))
	err = client.Client().HSet(key, strUid, 1).Err()
	if err != nil {
		log.ErrorWithCtx(ctx,"SaveDayHitCnt:%s", err.Error())
		return err
	}
	return nil
}*/

func (r *StatCache) GetGameDayHitInfo(ctx context.Context, gameTime string) (map[string]uint32, map[string]*HitInfoT, error) {
    client, err := r.redisStatClient.GetRdbByNode(StatRedisName5)
    if err != nil {
        log.ErrorWithCtx(ctx, "[GetGameDayHitInfo]GetRdbByNode err:%s", err.Error())
        return nil, nil, err
    }
    hitMap := make(map[string]uint32)
    for hour := 0; hour <= 23; hour++ {
        //for min := 0; min <= 55; min = min + 10 {
        //	gameTime1 := gameTime + fmt.Sprintf("T%02d:%02d", hour, min) //min gameTime1 := gameTime + fmt.Sprintf("T%02d:%02d", hour, min)
        gameTime1 := gameTime + fmt.Sprintf("T%02d:%02d", hour, 0)
        //gameTime1 := gameTime + fmt.Sprintf("T%02d:00", hour)
        key1 := r.getStatGameRound(gameTime1, HIT)
        hitInfos, err := client.Client().HGetAll(key1).Result()
        if err != nil {
            log.ErrorWithCtx(ctx, "GetGameDayHitCnt HGetAll err:%s", err.Error())
            return nil, nil, err
        }
        for uid := range hitInfos {
            if cnt, ok := hitMap[uid]; ok {
                hitMap[uid] = cnt + 1
            } else {
                hitMap[uid] = 1
            }
        }
        //}
    }

    client, err = r.redisStatClient.GetRdbByNode(StatRedisName4)
    if err != nil {
        log.ErrorWithCtx(ctx, "[GetGameDayHitInfo]GetRdbByNode StatRedisName4 err:%s", err.Error())
        return nil, nil, err
    }
    getMap := make(map[string]*HitInfoT)
    for hour := 0; hour <= 23; hour++ {
        for min := 0; min <= 55; min = min + 10 {
            gameTime2 := gameTime + fmt.Sprintf("T%02d:%02d", hour, min) //min
            //gameTime2 := gameTime + fmt.Sprintf("T%02d:00", hour)
            key1 := r.getStatGameRound(gameTime2, GET)
            hitInfos, err := client.Client().HGetAll(key1).Result()
            if err != nil {
                log.ErrorWithCtx(ctx, "GetGameDayHitCnt HGetAll err:%s", err.Error())
                return nil, nil, err
            }

            for uid, jsonNewCntInfo := range hitInfos {
                hitInfo := &HitInfoT{}
                err = json.Unmarshal([]byte(jsonNewCntInfo), hitInfo)
                if err != nil {
                    log.ErrorWithCtx(ctx, "json.Unmarshal err:%v, %+v", err, hitInfo)
                    continue
                }

                if cntInfo, ok := getMap[uid]; ok {
                    tmpCntInfo := &HitInfoT{}
                    tmpCntInfo.HighCnt = cntInfo.HighCnt + hitInfo.HighCnt
                    tmpCntInfo.MiddleCnt = cntInfo.MiddleCnt + hitInfo.MiddleCnt
                    tmpCntInfo.LowCnt = cntInfo.LowCnt + hitInfo.LowCnt
                    tmpCntInfo.LotteryCnt = cntInfo.LotteryCnt + hitInfo.LotteryCnt
                    tmpCntInfo.BackUpCnt = cntInfo.BackUpCnt + hitInfo.BackUpCnt
                    getMap[uid] = tmpCntInfo
                } else {
                    tmpCntInfo := &HitInfoT{}
                    tmpCntInfo.HighCnt = hitInfo.HighCnt
                    tmpCntInfo.MiddleCnt = hitInfo.MiddleCnt
                    tmpCntInfo.LowCnt = hitInfo.LowCnt
                    tmpCntInfo.LotteryCnt = hitInfo.LotteryCnt
                    tmpCntInfo.BackUpCnt = hitInfo.BackUpCnt
                    getMap[uid] = tmpCntInfo
                }
            }
        }
    }

    return hitMap, getMap, nil
}

/*func (r *StatCache) GetDayHitCnt(gameTime string) (int64, int64, error) {
	client, err := r.redisStatClient.GetRdbByNode(StatRedisName4)
	if err != nil {
		log.ErrorWithCtx(ctx,"GetRdbByNode err:%s", err.Error())
		return 0, 0, err
	}
	key1 := fmt.Sprintf("stat_gameday_%s_%d", gameTime, HIT)
	hitLen, err := client.Client().HLen(key1).Result()
	if err != nil {
		log.ErrorWithCtx(ctx,"GetDayHitCnt HGetAll err:%s", err.Error())
		return 0, 0, err
	}

	key2 := fmt.Sprintf("stat_gameday_%s_%d", gameTime, GET)
	getLen, err := client.Client().HLen(key2).Result()
	if err != nil {
		log.ErrorWithCtx(ctx,"GetDayHitCnt HGetAll err:%s", err.Error())
		return 0, 0, err
	}
	return hitLen, getLen, nil
}*/

func (r *StatCache) GetOldLotteryGive(ctx context.Context) (map[string]string, error) {
    key := "stat_give"
    client, err := r.redisStatClient.GetRdbByNode(StatRedisName2)
    if err != nil {
        log.ErrorWithCtx(ctx, "[GetOldLotteryGive]GetRdbByNode err:%s, %s", err.Error())
        return nil, err
    }
    res, err := client.Client().HGetAll(key).Result()
    if err != nil {

        log.ErrorWithCtx(ctx, "GetLotteryGive err:%s", err.Error())
        return nil, err
    }
    return res, err
}

//用户获奖后的数据
func (r *StatCache) SaveDayUserBeanPrice(ctx context.Context, gameConfigBeginTime int64, uid uint32, beanPrice int64) error {
    gameTime := time.Unix(gameConfigBeginTime, 0).Format(dateFormat)
    key := fmt.Sprintf("user_beanprice_%s", gameTime)
    client, err := r.redisStatClient.GetRdbByNode(StatRedisName4)
    if err != nil {
        log.ErrorWithCtx(ctx, "[SaveDayUserBeanPrice]GetRdbByNode err:%s, %s", err.Error())
        return err
    }

    strUid := strconv.Itoa(int(uid))
    err = client.Client().HIncrBy(key, strUid, beanPrice).Err()
    if err != nil {
        log.ErrorWithCtx(ctx, "SaveUserBeanPrice:%s", err.Error())
        return err
    }
    return nil
}

func (r *StatCache) GetDayUserBeanPrice(ctx context.Context, gameConfigBeginTime string) (map[string]string, error) {
    //gameTime := time.Unix(gameConfigBeginTime, 0).Format(timeFormat)
    key := fmt.Sprintf("user_beanprice_%s", gameConfigBeginTime)
    client, err := r.redisStatClient.GetRdbByNode(StatRedisName4)
    if err != nil {
        log.ErrorWithCtx(ctx, "[GetDayUserBeanPrice]GetRdbByNode err:%s, %s", err.Error())
        return nil, err
    }

    resultPrice, err := client.Client().HGetAll(key).Result()
    if err != nil {
        log.ErrorWithCtx(ctx, "GetUserBeanPrice:%s", err.Error())
        return nil, err
    }
    return resultPrice, nil
}

func (r *StatCache) getStatGameDetailKey(gameTime string) string {
    return fmt.Sprintf("stat_gamedetail_%s", gameTime)
}

func (r *StatCache) getStatGameRound(gameTime string, hitType uint32) string {
    return fmt.Sprintf("stat_gameround_%s_%d", gameTime, hitType)
}
