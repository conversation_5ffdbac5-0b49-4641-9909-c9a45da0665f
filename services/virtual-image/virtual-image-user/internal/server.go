package internal

import (
    "context"
    "gitlab.ttyuyin.com/avengers/tyr/core/log"
    mysqlConnect "gitlab.ttyuyin.com/avengers/tyr/core/middleware/mysql/connect"
    redisConnect "gitlab.ttyuyin.com/avengers/tyr/core/middleware/redis/connect"
    "golang.52tt.com/pkg/bylink"
    "golang.52tt.com/pkg/protocol"
    "golang.52tt.com/protocol/app/virtual_image_logic"
    "golang.52tt.com/protocol/common/status"
    "golang.52tt.com/protocol/services/demo/echo"
    virtual_image_user "golang.52tt.com/protocol/services/virtual-image-user"
    "golang.52tt.com/services/virtual-image/virtual-image-user/internal/comm"
    "golang.52tt.com/services/virtual-image/virtual-image-user/internal/conf"
    "golang.52tt.com/services/virtual-image/virtual-image-user/internal/event"
    "golang.52tt.com/services/virtual-image/virtual-image-user/internal/model/anti-corruption-layer"
    display_switch "golang.52tt.com/services/virtual-image/virtual-image-user/internal/model/display-switch"
    "golang.52tt.com/services/virtual-image/virtual-image-user/internal/model/reminder"
    user_event "golang.52tt.com/services/virtual-image/virtual-image-user/internal/model/user-event"
    user_relation "golang.52tt.com/services/virtual-image/virtual-image-user/internal/model/user-relation"
    user_virtual_avatar "golang.52tt.com/services/virtual-image/virtual-image-user/internal/model/user-virtual-avatar"
    "golang.52tt.com/services/virtual-image/virtual-image-user/internal/model/user-virtual-avatar/cache"
    "golang.52tt.com/services/virtual-image/virtual-image-user/internal/producer"
    context0 "golang.org/x/net/context"
    "google.golang.org/grpc/codes"
)

type Server struct {
    bc              *conf.BusinessConfManager
    userVA          user_virtual_avatar.IMgr
    acLayer         anti_corruption_layer.IMgr
    userRelationMgr user_relation.IMgr
    displaySwitch   display_switch.IMgr
    reminder        reminder.IMgr
    userEvent       user_event.IMgr
    kfkEvent        *event.KafkaEvent
}

func (s *Server) GetVirtualImageBeginnerGuide(ctx context.Context, request *virtual_image_user.GetVirtualImageBeginnerGuideRequest) (*virtual_image_user.GetVirtualImageBeginnerGuideResponse, error) {
    resp := &virtual_image_user.GetVirtualImageBeginnerGuideResponse{}

    done, err := s.userEvent.IsUserBeginnerGuideDone(ctx, request.GetUid())
    if err != nil {
        log.ErrorWithCtx(ctx, "GetVirtualImageBeginnerGuide failed to user_event.IsUserBeginnerGuideDone. req:%v, err:%v", request, err)
        return resp, err
    }
    resp.HasGuide = !done
    return resp, nil
}

func (s *Server) MarkVirtualImageBeginnerGuideDone(ctx context.Context, request *virtual_image_user.MarkVirtualImageBeginnerGuideDoneRequest) (*virtual_image_user.MarkVirtualImageBeginnerGuideDoneResponse, error) {
    resp := &virtual_image_user.MarkVirtualImageBeginnerGuideDoneResponse{}
    if err := s.userEvent.MarkUserBeginnerGuideDone(ctx, request.GetUid()); err != nil {
        log.ErrorWithCtx(ctx, "MarkVirtualImageBeginnerGuideDone failed to user_event.MarkUserBeginnerGuideDone. req:%v, err:%v", request, err)
        return resp, err
    }
    return resp, nil
}

func (s *Server) SetVirtualImagePoseType(ctx context.Context, request *virtual_image_user.SetVirtualImagePoseTypeRequest) (*virtual_image_user.SetVirtualImagePoseTypeResponse, error) {
    log.InfoWithCtx(ctx, "SetVirtualImagePoseType req:%+v", request)
    resp := &virtual_image_user.SetVirtualImagePoseTypeResponse{}

    err := s.displaySwitch.UpdateUserPoseType(ctx, request.GetUid(), request.GetScene(), request.GetPoseType() )
    if err != nil {
        log.ErrorWithCtx(ctx, "SetVirtualImagePoseType failed to display_switch.UpdateUserPoseType. req:%v, err:%v", request, err)
        return resp, err
    }

    return resp, nil
}

func NewServer(ctx context.Context, cfg *conf.StartConfig) (*Server, error) {
    log.Infof("server startup with cfg: %+v", *cfg)

    redisClient, err := redisConnect.NewClient(ctx, cfg.RedisConfig)
    if err != nil {
        log.ErrorWithCtx(ctx, "NewServer fail to redisConnect.NewClient, %+v, err:%v", cfg.RedisConfig, err)
        return nil, err
    }

    mysqlDBCli, err := mysqlConnect.NewClient(ctx, cfg.MysqlConfig)
    if err != nil {
        log.ErrorWithCtx(ctx, "NewServer fail to mysqlConnect.NewClient, %+v, err:%v", cfg.MysqlConfig, err)
        return nil, err
    }

    mysqlROCli, err := mysqlConnect.NewClient(ctx, cfg.MysqlReadOnlyConfig)
    if err != nil {
        return nil, err
    }

    bc, err := conf.NewBusinessConfManager()
    if err != nil {
        return nil, err
    }

    kfkProduer, err := producer.NewKafkaProducer(cfg)
    if err != nil {
        log.ErrorWithCtx(ctx, "NewServer fail to producer.NewKafkaProducer, %+v, err:%v", cfg, err)
        return nil, err
    }

    // 百灵数据统计 初始化
    bylinkCollect, err := bylink.NewKfkCollector()
    if err != nil {
        log.Errorf("bylink.NewKfkCollector() failed err:%v", err)
        return nil, err
    }
    bylink.InitGlobalCollector(bylinkCollect)

    acl, err := anti_corruption_layer.NewMgr()
    if err != nil {
        log.ErrorWithCtx(ctx, "NewServer fail to anti_corruption_layer.NewMgr, err:%v", err)
        return nil, err
    }

    userVA, err := user_virtual_avatar.NewMgr(mysqlDBCli, mysqlROCli, redisClient, acl, kfkProduer)
    if err != nil {
        log.ErrorWithCtx(ctx, "NewServer fail to user_virtual_avatar.NewMgr, err:%v", err)
        return nil, err
    }

    userRelationMgr, err := user_relation.NewMgr(mysqlDBCli, redisClient, bc, acl)
    if err != nil {
        log.ErrorWithCtx(ctx, "NewServer fail to user_relation.NewMgr, err:%v", err)
        return nil, err
    }

    displaySwitch, err := display_switch.NewMgr(mysqlDBCli, redisClient)
    if err != nil {
        log.ErrorWithCtx(ctx, "NewServer fail to display_switch.NewMgr, err:%v", err)
        return nil, err
    }

    reminderMgr, err := reminder.NewMgr(bc, redisClient, userVA, acl, displaySwitch)
    if err != nil {
        log.ErrorWithCtx(ctx, "NewServer fail to reminder.NewMgr, err:%v", err)
        return nil, err
    }

    kfkEv, err := event.NewKafkaEvent(cfg, userVA, userRelationMgr, acl, displaySwitch, reminderMgr)
    if err != nil {
        log.ErrorWithCtx(ctx, "NewServer fail to event.NewKafkaEvent, err:%v", err)
        return nil, err
    }

    userEventMgr, err := user_event.NewMgr(mysqlDBCli)
    if err != nil {
        log.ErrorWithCtx(ctx, "NewServer fail to user_event.NewMgr, err:%v", err)
        return nil, err
    }

    s := &Server{
        bc:              bc,
        userVA:          userVA,
        acLayer:         acl,
        userRelationMgr: userRelationMgr,
        displaySwitch:   displaySwitch,
        reminder:        reminderMgr,
        kfkEvent:        kfkEv,
        userEvent:       userEventMgr,
    }

    return s, nil
}

func (s *Server) ShutDown() {
    s.kfkEvent.Close()
    s.reminder.Stop()
    s.userVA.Stop()
    s.acLayer.Stop()
    s.userRelationMgr.Stop()
    s.displaySwitch.Stop()
    s.userEvent.Stop()
}

func (s *Server) Echo(ctx context.Context, req *echo.StringMessage) (*echo.StringMessage, error) {
    return req, nil
}

func (s *Server) GetUserVirtualImageList(ctx context.Context, req *virtual_image_user.GetUserVirtualImageListReq) (*virtual_image_user.GetUserVirtualImageListResp, error) {
    resp := &virtual_image_user.GetUserVirtualImageListResp{
        Items: make([]*virtual_image_user.UserItemInfo, 0),
        Suits: make([]*virtual_image_user.UserSuitInfo, 0),
    }

    list, err := s.userVA.GetUserVirtualImageList(ctx, req.GetUid())
    if err != nil {
        log.ErrorWithCtx(ctx, "GetUserVirtualImageList failed to GetUserVirtualImageList. req:%v, err:%v", req, err)
        return resp, err
    }

    inUseMap, err := s.userVA.GetUserInUseMap(ctx, req.GetUid())
    if err != nil {
        log.ErrorWithCtx(ctx, "GetUserVirtualImageList failed to GetUserInUseMap. req:%v, err:%v", req, err)
        return resp, err
    }

    //now := time.Now()
    inuseList := make([]*cache.UserVAInUse, 0)
    for _, info := range inUseMap {
        inuseList = append(inuseList, info)
    }
    resp.InuseItems = comm.FillInuseItemInfoList(inuseList)

    if len(list) == 0 {
        return resp, nil
    }

    userItemMap := make(map[uint32]*virtual_image_user.UserItemInfo)
    for _, info := range list {
        if _, ok := inUseMap[info.CfgId]; ok {
            info.InUse = true
        }

        resp.Items = append(resp.Items, info)
        userItemMap[info.CfgId] = info
    }

    suitList, err := s.userVA.GetUserOwnSuitList(ctx, req.GetUid())
    if err != nil {
        log.ErrorWithCtx(ctx, "GetUserVirtualImageList failed to GetUserOwnSuitList. req:%v, err:%v", req, err)
        return resp, err
    }

    for _, suit := range suitList {
        suitPbInfo := &virtual_image_user.UserSuitInfo{
            SuitId:              suit.SuitId,
            Name:                suit.SuitName,
            SuitIcon:            suit.SuitIcon,
            LevelIcon:           suit.LevelIcon,
            PromotionResourceId: suit.PromotionId,
            Items:               make([]*virtual_image_user.UserItemInfo, 0),
            ExpireTime:          suit.ExpireTime,
            UpdateTime:          suit.UpdateTime,
        }
        for _, cfgId := range suit.SuitItems {
            itemInfo, ok := userItemMap[cfgId]
            if !ok {
                break
            }
            suitPbInfo.Items = append(suitPbInfo.Items, itemInfo)
        }

        if len(suitPbInfo.Items) != len(suit.SuitItems) {
            continue
        }

        resp.Suits = append(resp.Suits, suitPbInfo)
    }

    // 获取朝向
    orientationMap, err := s.userVA.BatchGetUserOrientation(ctx, []uint32{req.GetUid()})
    if err != nil {
        log.WarnWithCtx(ctx, "GetUserVirtualImageList failed to BatchGetUserOrientation. req:%v, err:%v", req, err)
    }

    resp.Orientation = orientationMap[req.GetUid()]
    return resp, nil
}

func (s *Server) BatchGetUserInuseItemInfo(ctx context.Context, req *virtual_image_user.BatchGetUserInuseItemInfoReq) (*virtual_image_user.BatchGetUserInuseItemInfoResp, error) {
    resp := &virtual_image_user.BatchGetUserInuseItemInfoResp{
        UserInuseItemInfo: make([]*virtual_image_user.UserInuseItemInfo, 0),
    }

    uid2List, err := s.userVA.BatchGetUserInUseList(ctx, req.GetUidList())
    if err != nil {
        log.ErrorWithCtx(ctx, "BatchGetUserInuseItemInfo failed to BatchGetUserInUseList. req:%v, err:%v", req, err)
        return nil, err
    }

    // 获取朝向
    orientationMap, err := s.userVA.BatchGetUserOrientation(ctx, req.GetUidList())
    if err != nil {
        log.WarnWithCtx(ctx, "BatchGetUserInuseItemInfo failed to BatchGetUserOrientation. req:%v, err:%v", req, err)
    }

    //now := time.Now()
    for uid, list := range uid2List {
        resp.UserInuseItemInfo = append(resp.UserInuseItemInfo, &virtual_image_user.UserInuseItemInfo{
            Uid:         uid,
            Items:       comm.FillInuseItemInfoList(list),
            Orientation: orientationMap[uid],
        })
    }

    return resp, nil
}

func (s *Server) GetUserDisplayData(ctx context.Context, req *virtual_image_user.GetUserDisplayDataReq) (*virtual_image_user.GetUserDisplayDataResp, error) {
    resp := &virtual_image_user.GetUserDisplayDataResp{
        UserDisplaySwitch: make([]*virtual_image_user.DisplaySwitch, 0),
    }

    switchMap, err := s.displaySwitch.GetUserDisplaySwitchMap(ctx, req.GetUid())
    if err != nil {
        log.ErrorWithCtx(ctx, "GetUserDisplayData failed to GetUserDisplaySwitchMap. req:%v, err:%v", req, err)
        return resp, err
    }

    if len(switchMap) == 0 {
        return resp, nil
    }

    relationUid, err := s.userRelationMgr.GetUserRelationInUse(ctx, req.GetUid())
    if err != nil {
        log.WarnWithCtx(ctx, "GetUserDisplayData failed to GetUserRelationInUse. req:%v, err:%v", req, err)
    }

    uidList := []uint32{req.GetUid()}
    if relationUid != 0 {
        uidList = append(uidList, relationUid)
    }

    infoMap, err := s.userVA.BatchGetUserInUseList(ctx, uidList)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetUserDisplayData failed to BatchGetUserVirtualImageList. req:%v, err:%v", req, err)
        return resp, err
    }

    //now := time.Now()
    if _, ok := infoMap[req.GetUid()]; !ok {
        return resp, nil
    }

    // 获取朝向
    orientationMap, err := s.userVA.BatchGetUserOrientation(ctx, uidList)
    if err != nil {
        log.WarnWithCtx(ctx, "GetUserDisplayData failed to BatchGetUserOrientation. req:%v, err:%v", req, err)
    }

    uid2PbInfo := make(map[uint32]*virtual_image_user.UserInuseItemInfo)
    for uid, list := range infoMap {
        uid2PbInfo[uid] = &virtual_image_user.UserInuseItemInfo{
            Uid:         uid,
            Items:       comm.FillInuseItemInfoList(list),
            Orientation: orientationMap[uid],
        }
    }

    resp.UserInuseItemInfo = uid2PbInfo[req.GetUid()]
    resp.RelationUserInuseItemInfo = uid2PbInfo[relationUid]

    for _, info := range switchMap {
        resp.UserDisplaySwitch = append(resp.UserDisplaySwitch, &virtual_image_user.DisplaySwitch{
            Type:     info.Type,
            SwitchOn: info.Switch,
        })
    }

    // 获取用户姿势设置
    poseMap, err := s.displaySwitch.GetUserPoseType(ctx, req.GetUid(), []uint32{
        uint32(virtual_image_logic.VirtualImageDisplaySwitch_VIRTUAL_IMAGE_DISPLAY_SWITCH_PERSONAL_PAGE),
            uint32(virtual_image_logic.VirtualImageDisplaySwitch_VIRTUAL_IMAGE_DISPLAY_SWITCH_PROFILE_CARD)})
    if err != nil {
        log.WarnWithCtx(ctx, "GetUserDisplayData failed to GetUserPoseType. req:%v, err:%v", req, err)
    }
    for k, v := range poseMap {
        resp.PoseInfo = append(resp.PoseInfo, &virtual_image_user.PoseInfo{
            Scene:    k,
            PoseType: v,
        })
    }
    return resp, nil
}

//func fillInuseItemInfoList(list []*cache.UserVAInUse, now time.Time) []*virtual_image_user.InuseItemInfo {
//    items := make([]*virtual_image_user.InuseItemInfo, 0)
//    for _, info := range list {
//        items = append(items, &virtual_image_user.InuseItemInfo{
//            CfgId:         info.VAId,
//            SubCategory:   info.CategoryID,
//            ExpireTime:    info.ExpireTs,
//            UseRightsType: info.Rights,
//        })
//    }
//    return items
//}

func (s *Server) GiveVirtualImageToUser(ctx context.Context, req *virtual_image_user.GiveVirtualImageToUserReq) (*virtual_image_user.GiveVirtualImageToUserResp, error) {
    resp := &virtual_image_user.GiveVirtualImageToUserResp{}
    firstGain, err := s.userVA.GiveVirtualImageToUser(ctx, req)
    if err != nil {
        log.ErrorWithCtx(ctx, "GiveVirtualImageToUser failed. req:%v, err:%v", req, err)
        return resp, err
    }

    // 首次获得
    if firstGain {
        // 打开用户外显开关
        _, err := s.displaySwitch.SetUserDisplaySwitch(ctx, req.GetUid(),
            uint32(virtual_image_logic.VirtualImageDisplaySwitch_VIRTUAL_IMAGE_DISPLAY_SWITCH_MAIN), true, true)
        if err != nil {
            log.WarnWithCtx(ctx, "GiveVirtualImageToUser failed to SetUserDisplaySwitch. req:%v, err:%v", req, err)
        }
    }

    return resp, nil
}

func (s *Server) SaveUserSuit(ctx context.Context, req *virtual_image_user.SaveUserSuitReq) (*virtual_image_user.SaveUserSuitResp, error) {
    resp := &virtual_image_user.SaveUserSuitResp{}
    //err := s.userVA.SaveUserCustomSuit(ctx, req)
    //if err != nil {
    //    log.ErrorWithCtx(ctx, "SaveUserSuit failed. req:%v, err:%v", req, err)
    //    return resp, err
    //}
    return resp, nil
}

func (s *Server) GetUserSaveSuitList(ctx context.Context, req *virtual_image_user.GetUserSaveSuitListReq) (*virtual_image_user.GetUserSaveSuitListResp, error) {
    resp := &virtual_image_user.GetUserSaveSuitListResp{}
    //list, err := s.userVA.GetUserCustomSuitList(ctx, req.GetUid())
    //if err != nil {
    //    log.ErrorWithCtx(ctx, "GetUserSaveSuitList failed to GetUserCustomSuitList. req:%v, err:%v", req, err)
    //    return resp, err
    //}
    //
    //if len(list) == 0 {
    //    return resp, nil
    //}
    //
    //userVaList, err := s.userVA.GetUserVirtualImageList(ctx, req.GetUid())
    //if err != nil {
    //    log.ErrorWithCtx(ctx, "GetUserSaveSuitList failed to GetUserVirtualImageList. req:%v, err:%v", req, err)
    //    return resp, err
    //}
    //
    //ID2UserVa := make(map[uint32]*virtual_image_user.UserItemInfo)
    //for _, item := range userVaList {
    //    ID2UserVa[item.CfgId] = item
    //}
    //
    //resp.Suits = make([]*virtual_image_user.UserSuitInfo, 0)
    //for _, info := range list {
    //    suit := &virtual_image_user.UserSuitInfo{
    //        SuitId: info.SuitId,
    //        Items:  make([]*virtual_image_user.UserItemInfo, 0),
    //    }
    //
    //    for _, cfgId := range info.SuitItems {
    //        if va, ok := ID2UserVa[cfgId]; ok {
    //            suit.Items = append(suit.Items, va)
    //        } else {
    //            suit.Items = append(suit.Items, &virtual_image_user.UserItemInfo{
    //                CfgId: cfgId,
    //            })
    //        }
    //    }
    //    resp.Suits = append(resp.Suits, suit)
    //}

    return resp, nil
}

func (s *Server) SetUserVirtualImageInUse(ctx context.Context, req *virtual_image_user.SetUserVirtualImageInUseReq) (*virtual_image_user.SetUserVirtualImageInUseResp, error) {
    resp := &virtual_image_user.SetUserVirtualImageInUseResp{}
    err := s.userVA.SetUserVirtualImageInUse(ctx, req)
    if err != nil {
        log.ErrorWithCtx(ctx, "SetUserVirtualImageInUse failed. req:%v, err:%v", req, err)
        return resp, err
    }

    // 通知房间
    err = s.UserInuseVIChangeChannelNotify(ctx, req.GetUid())
    if err != nil {
        log.WarnWithCtx(ctx, "SetUserVirtualImageInUse failed to UserInuseVIChangeChannelNotify. req:%v, err:%v", req, err)
    }

    isClear := true
    for _, item := range req.GetItems() {
        if item.GetCfgId() != 0 {
            isClear = false
            break
        }
    }

    if isClear {
        // 取消佩戴虚拟形象，提醒不可外显状态
        err = s.reminder.CannotDisplayDelayReminder(ctx, req.GetUid())
        if err != nil {
            log.WarnWithCtx(ctx, "SetUserVirtualImageInUse failed to CannotDisplayDelayReminder. req:%v, err:%v", req, err)
        }
    } else {
        // 用户佩戴虚拟形象，移除不可外显状态提醒
        s.reminder.RemoveUserDisplayReminder(ctx, req.GetUid())
    }

    err = s.reminder.FirstUseReminder(ctx, req.GetUid())
    if err != nil {
        log.WarnWithCtx(ctx, "SetUserVirtualImageInUse failed to FirstUseReminder. req:%v, err:%v", req, err)
    }

    return resp, nil
}

func (s *Server) GetUserDisplaySwitch(ctx context.Context, req *virtual_image_user.GetUserDisplaySwitchRequest) (*virtual_image_user.GetUserDisplaySwitchResponse, error) {
    resp := &virtual_image_user.GetUserDisplaySwitchResponse{
        DisplaySwitch: make([]*virtual_image_user.DisplaySwitch, 0),
    }

    switchMap, err := s.displaySwitch.GetUserDisplaySwitchMap(ctx, req.GetUid())
    if err != nil {
        log.ErrorWithCtx(ctx, "GetUserDisplaySwitch failed to GetUserDisplaySwitchList. req:%v, err:%v", req, err)
        return resp, err
    }

    info, ok := switchMap[uint32(virtual_image_logic.VirtualImageDisplaySwitch_VIRTUAL_IMAGE_DISPLAY_SWITCH_ROOM_ENTER_EFFECT)]
    if ok && info.Switch == true {
        targetUid, err := s.userRelationMgr.GetUserRelationInUse(ctx, req.GetUid())
        if err != nil {
            log.ErrorWithCtx(ctx, "GetUserDisplaySwitch failed to GetUserRelationInUse. req:%v, err:%v", req, err)
            return resp, err
        }

        // 未设置双人关系，不展示房间特效开关
        if targetUid == 0 {
            info.Switch = false
        }
    }

    for _, info := range switchMap {
        resp.DisplaySwitch = append(resp.DisplaySwitch, &virtual_image_user.DisplaySwitch{
            Type:     info.Type,
            SwitchOn: info.Switch,
        })
    }

    return resp, nil
}

func (s *Server) BatchGetUserDisplaySwitch(ctx context.Context, req *virtual_image_user.BatchGetUserDisplaySwitchRequest) (*virtual_image_user.BatchGetUserDisplaySwitchResponse, error) {
    resp := &virtual_image_user.BatchGetUserDisplaySwitchResponse{
        UserDisplaySwitchList: make([]*virtual_image_user.UserDisplaySwitch, 0),
    }

    if len(req.GetUidList()) == 0 {
        return resp, nil
    }

    switchMap, err := s.displaySwitch.BatchGetUserDisplaySwitch(ctx, req.GetUidList())
    if err != nil {
        log.ErrorWithCtx(ctx, "BatchGetUserDisplaySwitch failed to BatchGetUserDisplaySwitch. req:%v, err:%v", req, err)
        return resp, err
    }

    for uid, ty2SwitchInfo := range switchMap {
        userSwitch := &virtual_image_user.UserDisplaySwitch{
            Uid:           uid,
            DisplaySwitch: make([]*virtual_image_user.DisplaySwitch, 0),
        }
        for _, info := range ty2SwitchInfo {
            userSwitch.DisplaySwitch = append(userSwitch.DisplaySwitch, &virtual_image_user.DisplaySwitch{
                Type:     info.Type,
                SwitchOn: info.Switch,
            })
        }
        resp.UserDisplaySwitchList = append(resp.UserDisplaySwitchList, userSwitch)
    }

    return resp, nil
}

func (s *Server) SetUserDisplaySwitch(ctx context.Context, req *virtual_image_user.SetUserDisplaySwitchRequest) (*virtual_image_user.SetUserDisplaySwitchResponse, error) {
    resp := &virtual_image_user.SetUserDisplaySwitchResponse{}
    switchInfo := req.GetDisplaySwitch()

    if switchInfo.GetType() == uint32(virtual_image_logic.VirtualImageDisplaySwitch_VIRTUAL_IMAGE_DISPLAY_SWITCH_ROOM_ENTER_EFFECT) &&
        switchInfo.SwitchOn == true {

        targetUid, err := s.userRelationMgr.GetUserRelationInUse(ctx, req.GetUid())
        if err != nil {
            log.ErrorWithCtx(ctx, "GetUserDisplaySwitch failed to GetUserRelationInUse. req:%v, err:%v", req, err)
            return resp, err
        }

        // 未设置双人关系， 无法开启房间特效开关
        if targetUid == 0 {
            return resp, protocol.NewExactServerError(codes.OK, status.ErrVirtualImageSetDisplaySwitchFail, "选择双人模式后生效")
        }
    }

    ok, err := s.displaySwitch.SetUserDisplaySwitch(ctx, req.GetUid(), switchInfo.GetType(), switchInfo.GetSwitchOn(), false)
    if err != nil {
        log.ErrorWithCtx(ctx, "SetUserDisplaySwitch failed to SetUserDisplaySwitch. req:%v, err:%v", req, err)
        return resp, err
    }

    // 设置失败
    if !ok {
        log.WarnWithCtx(ctx, "SetUserDisplaySwitch failed. req:%+v", req)
        return resp, nil
    }

    if switchInfo.GetType() == uint32(virtual_image_logic.VirtualImageDisplaySwitch_VIRTUAL_IMAGE_DISPLAY_SWITCH_MAIN) ||
        switchInfo.GetType() == uint32(virtual_image_logic.VirtualImageDisplaySwitch_VIRTUAL_IMAGE_DISPLAY_SWITCH_MIC) {
        // 影响房间麦位外显数据，通知房间
        err = s.UserInuseVIChangeChannelNotify(ctx, req.GetUid())
        if err != nil {
            log.WarnWithCtx(ctx, "SetUserDisplaySwitch failed to UserInuseVIChangeChannelNotify. req:%v, err:%v", req, err)
        }
    }

    if switchInfo.GetType() == uint32(virtual_image_logic.VirtualImageDisplaySwitch_VIRTUAL_IMAGE_DISPLAY_SWITCH_MAIN) {
        if !switchInfo.GetSwitchOn() {
            // 用户关闭外显总开关，移除不可外显状态提醒
            s.reminder.RemoveUserDisplayReminder(ctx, req.GetUid())
        }
    }

    return resp, nil
}

func (s *Server) SetUserVirtualImageOrientation(ctx context.Context, req *virtual_image_user.SetUserVirtualImageOrientationReq) (*virtual_image_user.SetUserVirtualImageOrientationResp, error) {
    resp := &virtual_image_user.SetUserVirtualImageOrientationResp{}

    err := s.userVA.SetUserOrientation(ctx, req.GetUid(), req.GetOrientation())
    if err != nil {
        log.ErrorWithCtx(ctx, "SetUserVirtualImageOrientation failed. req:%v, err:%v", req, err)
        return resp, err
    }

    // 通知房间
    err = s.UserInuseVIChangeChannelNotify(ctx, req.GetUid())
    if err != nil {
        log.WarnWithCtx(ctx, "SetUserVirtualImageOrientation failed to UserInuseVIChangeChannelNotify. req:%v, err:%v", req, err)
    }

    return resp, nil
}

// UserInuseVIChangeChannelNotify 用户佩戴的虚拟形象变更通知房间
func (s *Server) UserInuseVIChangeChannelNotify(ctx context.Context, uid uint32) error {
    switchMap, err := s.displaySwitch.GetUserDisplaySwitchMap(ctx, uid)
    if err != nil {
        log.ErrorWithCtx(ctx, "UserInuseVIChangeChannelNotify failed to GetUserDisplaySwitchMap. uid:%v, err:%v", uid, err)
        return err
    }

    inuseInfo := &virtual_image_user.UserInuseItemInfo{
        Uid: uid,
    }

    // 开启了在房间麦位外显
    if info, ok := switchMap[uint32(virtual_image_logic.VirtualImageDisplaySwitch_VIRTUAL_IMAGE_DISPLAY_SWITCH_MIC)]; ok && info.Switch {
        // 获取佩戴信息
        userVIResp, err := s.BatchGetUserInuseItemInfo(ctx, &virtual_image_user.BatchGetUserInuseItemInfoReq{
            UidList: []uint32{uid},
        })
        if err != nil {
            log.ErrorWithCtx(ctx, "UserInuseVIChangeChannelNotify failed to BatchGetUserInuseItemInfo. uid:%v, err:%v", uid, err)
            return err
        }

        if len(userVIResp.UserInuseItemInfo) > 0 {
            inuseInfo = userVIResp.GetUserInuseItemInfo()[0]
        }
    }

    // 推送通知
    err = s.acLayer.PushChannelUserVIChange(ctx, 0, inuseInfo, true)
    if err != nil {
        log.ErrorWithCtx(ctx, "UserInuseVIChangeChannelNotify failed to PushChannelUserVIChange. uid:%v, err:%v", uid, err)
        return err
    }

    log.DebugWithCtx(ctx, "UserInuseVIChangeChannelNotify success. uid:%v", uid)
    return nil
}

func (s *Server) GetUserItemObtainRecord(c context0.Context, req *virtual_image_user.GetUserItemObtainRecordReq) (*virtual_image_user.GetUserItemObtainRecordResp, error) {
    out := &virtual_image_user.GetUserItemObtainRecordResp{}
    // 参数校验
    if req.GetUid() == 0 {
        log.ErrorWithCtx(c, "GetUserItemObtainRecord fail. req:%+v", req)
        return out, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid)
    }
    if req.GetLimit() == 0 {
        return out, nil
    }

    return s.userVA.GetUserVirtualImageAwardLogs(c, req)
}

func (s *Server) GetFollowEnterChannelInfo(ctx context.Context, req *virtual_image_user.GetFollowEnterChannelInfoRequest) (*virtual_image_user.GetFollowEnterChannelInfoResponse, error) {
    out := &virtual_image_user.GetFollowEnterChannelInfoResponse{}
    uid, followedUid := req.GetUid(), req.GetFollowedUid()

    switchMap, err := s.displaySwitch.GetUserDisplaySwitchMap(ctx, req.GetUid())
    if err != nil {
        log.ErrorWithCtx(ctx, "GetFollowEnterChannelInfo failed to GetUserDisplaySwitchList. req:%v, err:%v", req, err)
        return out, err
    }

    // 未开启双人进房特效开关
    if info, ok := switchMap[uint32(virtual_image_logic.VirtualImageDisplaySwitch_VIRTUAL_IMAGE_DISPLAY_SWITCH_ROOM_ENTER_EFFECT)]; !ok || info.Switch == false {
        return out, nil
    }

    cpUid, err := s.userRelationMgr.GetUserRelationInUse(ctx, uid)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetFollowEnterChannelInfo failed to GetUserRelationInUse. req:%v, err:%v", req, err)
        return out, err
    }
    if followedUid != cpUid {
        return out, nil
    }

    //cpUid, err = s.userRelationMgr.GetUserRelationInUse(ctx, followedUid)
    //if err != nil {
    //    log.ErrorWithCtx(ctx, "GetFollowEnterChannelInfo failed to GetUserRelationInUse. req:%v, err:%v", req, err)
    //    return out, err
    //}
    //if uid != cpUid {
    //    return out, nil
    //}

    inuseMap, err := s.userVA.BatchGetUserInUseList(ctx, []uint32{uid, followedUid})
    if err != nil {
        log.ErrorWithCtx(ctx, "GetFollowEnterChannelInfo failed to BatchGetUserInUseList. req:%v, err:%v", req, err)
        return out, err
    }

    //now := time.Now()
    userItemInfo := &virtual_image_user.UserInuseItemInfo{
        Uid:   uid,
        Items: comm.FillInuseItemInfoList(inuseMap[uid]),
    }
    followedUserItemInfo := &virtual_image_user.UserInuseItemInfo{
        Uid:   followedUid,
        Items: comm.FillInuseItemInfoList(inuseMap[followedUid]),
    }

    if len(userItemInfo.GetItems()) == 0 || len(followedUserItemInfo.GetItems()) == 0 {
        return out, nil
    }

    out.UserItemInfo = userItemInfo
    out.FollowedUserItemInfo = followedUserItemInfo

    return out, nil
}
