package cache

import (
    "context"
    "encoding/json"
    "gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/redis"
    "gitlab.ttyuyin.com/tt-infra/tyr/log"
    "strconv"
    "time"
)

type UserOwnSuit struct {
    SuitId      uint32   `json:"suit_id"`
    SuitName    string   `json:"suit_name"`
    SuitItems   []uint32 `json:"suit_items"`
    ExpireTime  int64    `json:"expire_time"`
    SuitIcon    string   `json:"suit_icon"`
    LevelIcon   string   `json:"level_icon"`
    PromotionId uint32   `json:"promotion_id"`
    UpdateTime  int64    `json:"update_time"`
}

func genUserOwnSuitListKey(uid uint32) string {
    return "virtual_image_user_own_suit_list:" + strconv.Itoa(int(uid))
}

// GetUserOwnSuitList 获取用户拥有的套装列表
func (c *Cache) GetUserOwnSuitList(ctx context.Context, uid uint32) ([]*UserOwnSuit, bool, error) {
    infoList := make([]*UserOwnSuit, 0)
    key := genUserOwnSuitListKey(uid)

    val, err := c.cmder.Get(ctx, key).Result()
    if err != nil {
        if redis.IsNil(err) {
            return infoList, false, nil
        }
        log.ErrorWithCtx(ctx, "GetUserOwnSuitList Get error: %v", err)
        return infoList, false, err
    }

    err = json.Unmarshal([]byte(val), &infoList)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetUserOwnSuitList unmarshal json error: %v, val:%v", err, val)
        return infoList, false, err
    }

    return infoList, true, nil
}

// SetUserOwnSuitList 设置用户拥有的套装列表
func (c *Cache) SetUserOwnSuitList(ctx context.Context, uid uint32, infoList []*UserOwnSuit) error {
    val, err := json.Marshal(infoList)
    if err != nil {
        log.ErrorWithCtx(ctx, "SetUserRelationList marshal json error: %v, item:%v", err, infoList)
        return err
    }

    key := genUserOwnSuitListKey(uid)
    err = c.cmder.Set(ctx, key, val, 48*time.Hour).Err()
    if err != nil {
        log.ErrorWithCtx(ctx, "SetUserRelationList Set error: %v", err)
        return err
    }

    return nil
}

// ClearUserOwnSuitList 删除用户拥有的套装列表
func (c *Cache) ClearUserOwnSuitList(ctx context.Context, uid uint32) error {
    key := genUserOwnSuitListKey(uid)
    err := c.cmder.Del(ctx, key).Err()
    if err != nil {
        log.ErrorWithCtx(ctx, "ClearUserOwnSuitList Del error: %v", err)
        return err
    }

    return nil
}
