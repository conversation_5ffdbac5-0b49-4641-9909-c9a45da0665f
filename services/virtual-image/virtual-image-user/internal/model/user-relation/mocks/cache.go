// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/services/virtual-image/virtual-image-user/internal/model/user-relation/cache (interfaces: ICache)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"
	time "time"

	redis "github.com/go-redis/redis/v8"
	gomock "github.com/golang/mock/gomock"
	cache "golang.52tt.com/services/virtual-image/virtual-image-user/internal/model/user-relation/cache"
)

// MockICache is a mock of ICache interface.
type MockICache struct {
	ctrl     *gomock.Controller
	recorder *MockICacheMockRecorder
}

// MockICacheMockRecorder is the mock recorder for MockICache.
type MockICacheMockRecorder struct {
	mock *MockICache
}

// NewMockICache creates a new mock instance.
func NewMockICache(ctrl *gomock.Controller) *MockICache {
	mock := &MockICache{ctrl: ctrl}
	mock.recorder = &MockICacheMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockICache) EXPECT() *MockICacheMockRecorder {
	return m.recorder
}

// AddUserInviteSet mocks base method.
func (m *MockICache) AddUserInviteSet(arg0 context.Context, arg1 uint32, arg2 []uint32, arg3 time.Duration) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddUserInviteSet", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddUserInviteSet indicates an expected call of AddUserInviteSet.
func (mr *MockICacheMockRecorder) AddUserInviteSet(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddUserInviteSet", reflect.TypeOf((*MockICache)(nil).AddUserInviteSet), arg0, arg1, arg2, arg3)
}

// AddUserReceiveInviteHash mocks base method.
func (m *MockICache) AddUserReceiveInviteHash(arg0 context.Context, arg1 uint32, arg2 []*cache.InviteInfo, arg3 time.Duration) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddUserReceiveInviteHash", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddUserReceiveInviteHash indicates an expected call of AddUserReceiveInviteHash.
func (mr *MockICacheMockRecorder) AddUserReceiveInviteHash(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddUserReceiveInviteHash", reflect.TypeOf((*MockICache)(nil).AddUserReceiveInviteHash), arg0, arg1, arg2, arg3)
}

// CheckInviteSet mocks base method.
func (m *MockICache) CheckInviteSet(arg0 context.Context, arg1, arg2 uint32) (*cache.InviteCheckInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckInviteSet", arg0, arg1, arg2)
	ret0, _ := ret[0].(*cache.InviteCheckInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckInviteSet indicates an expected call of CheckInviteSet.
func (mr *MockICacheMockRecorder) CheckInviteSet(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckInviteSet", reflect.TypeOf((*MockICache)(nil).CheckInviteSet), arg0, arg1, arg2)
}

// Close mocks base method.
func (m *MockICache) Close() error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Close")
	ret0, _ := ret[0].(error)
	return ret0
}

// Close indicates an expected call of Close.
func (mr *MockICacheMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockICache)(nil).Close))
}

// DelSendInviteSet mocks base method.
func (m *MockICache) DelSendInviteSet(arg0 context.Context, arg1, arg2 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelSendInviteSet", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// DelSendInviteSet indicates an expected call of DelSendInviteSet.
func (mr *MockICacheMockRecorder) DelSendInviteSet(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelSendInviteSet", reflect.TypeOf((*MockICache)(nil).DelSendInviteSet), arg0, arg1, arg2)
}

// DelUserBindCnt mocks base method.
func (m *MockICache) DelUserBindCnt(arg0 context.Context, arg1 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelUserBindCnt", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// DelUserBindCnt indicates an expected call of DelUserBindCnt.
func (mr *MockICacheMockRecorder) DelUserBindCnt(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelUserBindCnt", reflect.TypeOf((*MockICache)(nil).DelUserBindCnt), arg0, arg1)
}

// DelUserReceiveInviteHash mocks base method.
func (m *MockICache) DelUserReceiveInviteHash(arg0 context.Context, arg1, arg2 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelUserReceiveInviteHash", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// DelUserReceiveInviteHash indicates an expected call of DelUserReceiveInviteHash.
func (mr *MockICacheMockRecorder) DelUserReceiveInviteHash(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelUserReceiveInviteHash", reflect.TypeOf((*MockICache)(nil).DelUserReceiveInviteHash), arg0, arg1, arg2)
}

// DelUserRelationInUse mocks base method.
func (m *MockICache) DelUserRelationInUse(arg0 context.Context, arg1 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelUserRelationInUse", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// DelUserRelationInUse indicates an expected call of DelUserRelationInUse.
func (mr *MockICacheMockRecorder) DelUserRelationInUse(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelUserRelationInUse", reflect.TypeOf((*MockICache)(nil).DelUserRelationInUse), arg0, arg1)
}

// GetRedisClient mocks base method.
func (m *MockICache) GetRedisClient() redis.Cmdable {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRedisClient")
	ret0, _ := ret[0].(redis.Cmdable)
	return ret0
}

// GetRedisClient indicates an expected call of GetRedisClient.
func (mr *MockICacheMockRecorder) GetRedisClient() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRedisClient", reflect.TypeOf((*MockICache)(nil).GetRedisClient))
}

// GetUserBindCnt mocks base method.
func (m *MockICache) GetUserBindCnt(arg0 context.Context, arg1 uint32) (bool, uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserBindCnt", arg0, arg1)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(uint32)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetUserBindCnt indicates an expected call of GetUserBindCnt.
func (mr *MockICacheMockRecorder) GetUserBindCnt(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserBindCnt", reflect.TypeOf((*MockICache)(nil).GetUserBindCnt), arg0, arg1)
}

// GetUserReceiveInviteHash mocks base method.
func (m *MockICache) GetUserReceiveInviteHash(arg0 context.Context, arg1 uint32) (bool, map[uint32]*cache.InviteInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserReceiveInviteHash", arg0, arg1)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(map[uint32]*cache.InviteInfo)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetUserReceiveInviteHash indicates an expected call of GetUserReceiveInviteHash.
func (mr *MockICacheMockRecorder) GetUserReceiveInviteHash(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserReceiveInviteHash", reflect.TypeOf((*MockICache)(nil).GetUserReceiveInviteHash), arg0, arg1)
}

// GetUserRelationInUse mocks base method.
func (m *MockICache) GetUserRelationInUse(arg0 context.Context, arg1 uint32) (bool, uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserRelationInUse", arg0, arg1)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(uint32)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetUserRelationInUse indicates an expected call of GetUserRelationInUse.
func (mr *MockICacheMockRecorder) GetUserRelationInUse(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserRelationInUse", reflect.TypeOf((*MockICache)(nil).GetUserRelationInUse), arg0, arg1)
}

// LPushExpireInfo mocks base method.
func (m *MockICache) LPushExpireInfo(arg0 context.Context, arg1 []*cache.InviteInfo) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "LPushExpireInfo", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// LPushExpireInfo indicates an expected call of LPushExpireInfo.
func (mr *MockICacheMockRecorder) LPushExpireInfo(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "LPushExpireInfo", reflect.TypeOf((*MockICache)(nil).LPushExpireInfo), arg0, arg1)
}

// RPopExpireInfo mocks base method.
func (m *MockICache) RPopExpireInfo(arg0 context.Context) (*cache.InviteInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RPopExpireInfo", arg0)
	ret0, _ := ret[0].(*cache.InviteInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RPopExpireInfo indicates an expected call of RPopExpireInfo.
func (mr *MockICacheMockRecorder) RPopExpireInfo(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RPopExpireInfo", reflect.TypeOf((*MockICache)(nil).RPopExpireInfo), arg0)
}

// SetUserBindCnt mocks base method.
func (m *MockICache) SetUserBindCnt(arg0 context.Context, arg1, arg2 uint32, arg3 time.Duration) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetUserBindCnt", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetUserBindCnt indicates an expected call of SetUserBindCnt.
func (mr *MockICacheMockRecorder) SetUserBindCnt(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetUserBindCnt", reflect.TypeOf((*MockICache)(nil).SetUserBindCnt), arg0, arg1, arg2, arg3)
}

// SetUserRelationInUse mocks base method.
func (m *MockICache) SetUserRelationInUse(arg0 context.Context, arg1, arg2 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetUserRelationInUse", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetUserRelationInUse indicates an expected call of SetUserRelationInUse.
func (mr *MockICacheMockRecorder) SetUserRelationInUse(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetUserRelationInUse", reflect.TypeOf((*MockICache)(nil).SetUserRelationInUse), arg0, arg1, arg2)
}
