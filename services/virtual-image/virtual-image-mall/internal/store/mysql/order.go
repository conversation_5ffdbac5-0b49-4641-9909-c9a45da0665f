package mysqlStore

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"gitlab.ttyuyin.com/avengers/tyr/core/log"
	"gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/mysql"
	"golang.52tt.com/pkg/foundation/utils"
	pb "golang.52tt.com/protocol/services/virtual-image-mall"

	"github.com/jmoiron/sqlx"
)

type CommodityDataOrders struct {
	Uid                uint32          `db:"uid" json:"uid"`                           //用户id
	OrderNo            string          `db:"order_no" json:"order_no"`                 //订单号
	PayStatus          uint32          `db:"pay_status" json:"pay_status"`             //支付状态 1:待支付 2:已支付
	CreateTime         time.Time       `db:"create_time" json:"create_time"`           //创建时间
	UpdateTime         time.Time       `db:"update_time" json:"update_time"`           //更新时间
	TotalPrice         uint32          `db:"total_price" json:"total_price"`           //总价
	AvgPrice           uint32          `db:"avg_price" json:"avg_price"`               //平均价
	CommodityId        uint32          `db:"commodity_id" json:"commodity_id"`         //商品id
	PackageId          uint32          `db:"package_id" json:"package_id"`             //套餐id
	CommodityType      uint32          `db:"commodity_type" json:"commodity_type"`     //商品类型 1:单品 2:套装
	Category           uint32          `db:"category" json:"category"`                 //商品品类大类 see CommodityCategory_*
	SubCategory        uint32          `db:"sub_category" json:"sub_category"`         //商品品类子类
	EffectiveDay       uint32          `db:"effective_day" json:"effective_day"`       //有效天数
	Count              uint32          `db:"c_count" json:"c_count"`                   //数量
	ResourceIdList     json.RawMessage `db:"resource_id_list" json:"resource_id_list"` //资源id列表
	CommodityName      string          `db:"commodity_name" json:"commodity_name"`     //商品名称
	BigTradeNo         string          `db:"big_trade_no" json:"big_trade_no"`         //大订单号
	PromotionalVideoId uint32          `db:"pro_video_id" json:"pro_video_id"`         //宣传片id
}

func (t *CommodityDataOrders) String() string {
	return utils.ToJson(t)
}
func (t *CommodityDataOrders) SqlString() string {
	return utils.ToSqlStr(t)
}
func (t *CommodityDataOrders) TableName() string {
	return fmt.Sprintf("%s_%04d%02d", tblCommodityDataOrders, t.CreateTime.Year(), t.CreateTime.Month())
}

func getTableNameByTime(t time.Time) string {
	return fmt.Sprintf("%s_%04d%02d", tblCommodityDataOrders, t.Year(), t.Month())
}

func getTableNameByOrderID(ctx context.Context, orderID string) string {
	strList := strings.Split(orderID, "_")
	timeStr := strList[len(strList)-1]
	t, err := time.Parse("20060102150405", timeStr)
	if err != nil {
		log.ErrorWithCtx(ctx, "getTableNameByOrderID time.Parse fail err:%v", err)
		return ""
	}
	return fmt.Sprintf("%s_%04d%02d", tblCommodityDataOrders, t.Year(), t.Month())
}

func (s *Mysql) BuyCommodityData(ctx context.Context, in *pb.BuyCommodityDataRequest) error {
	err := s.buyCommodityData(ctx, in)
	if err != nil {
		if strings.Contains(err.Error(), "Error 1146") {
			s.createOrderTable()
			err = s.buyCommodityData(ctx, in)
			if err != nil {
				log.ErrorWithCtx(ctx, "BuyCommodityData fail int:%+v, err:%v", in, err)
				return err
			}
		}
		return err
	}
	return nil
}

func (s *Mysql) buyCommodityData(ctx context.Context, in *pb.BuyCommodityDataRequest) error {
	log.DebugWithCtx(ctx, "BuyCommodityData in:%v", in)
	if len(in.GetOrders()) == 0 {
		log.ErrorWithCtx(ctx, "BuyCommodityData orders is empty")
		return nil
	}

	info := CommodityDataOrders{
		CreateTime: time.Unix(int64(in.GetOrders()[0].GetCreateTime()), 0),
	}
	tableName := info.TableName()

	args := make([]interface{}, 0)
	sql := fmt.Sprintf("insert into %+v (`uid`,  `order_no`, `pay_status`, `total_price`, `avg_price`, `commodity_id`, `package_id`, "+
		"`c_count`, `resource_id_list`, `effective_day`, `commodity_type`, `category`, `sub_category`, `commodity_name`, `create_time`, `big_trade_no`, `pro_video_id`) values ", tableName)
	for _, v := range in.GetOrders() {
		dataTime := time.Unix(int64(v.GetCreateTime()), 0).Format("2006-01-02 15:04:05")
		resourceIdList, err := json.Marshal(v.GetResourceIdList())
		if err != nil {
			log.ErrorWithCtx(ctx, "BuyCommodityData json.Marshal fail err:%v data:%v", err)
			continue
		}
		sql += "(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?),"
		args = append(args, v.GetUid(), v.GetDataOrderId(), v.GetPayStatus(), v.GetTotalPrice(), v.GetAvgPrice(), v.GetCommodityId(), v.GetPackageId(),
			v.GetCount(), resourceIdList, v.GetEffectiveDay(), v.GetCommodityType(), v.GetCategory(), v.GetSubCategory(), v.GetCommodityName(), dataTime, v.GetBigTradeNo(), v.GetPromotionalVideoId())
	}

	sql = sql[:len(sql)-1]
	_, err := s.db.ExecContext(ctx, sql, args...)
	if err != nil {
		log.ErrorWithCtx(ctx, "AddShoppingCar error: %v sql:%v, args:%v", err, sql, args)
	}
	return err
}

func (s *Mysql) UpdateCommodityDataOrdersStatus(ctx context.Context, orderNo, bigOrderNo string, payStatus uint32, updateTime time.Time) error {
	log.DebugWithCtx(ctx, "UpdateCommodityDataOrdersStatus orderNo:%s payStatus:%d, updateTime:%v", orderNo, payStatus, updateTime)
	tableName := getTableNameByOrderID(ctx, bigOrderNo)
	sql := fmt.Sprintf("update %+v set `pay_status` = %d, `update_time` = '%v' where `big_trade_no` = '%s'", tableName, payStatus, updateTime, bigOrderNo)
	if len(orderNo) > 0 {
		tableName = getTableNameByOrderID(ctx, orderNo)
		sql = fmt.Sprintf("update %+v set `pay_status` = %d, `update_time` = '%v'  where `order_no` = '%s'", tableName, payStatus, updateTime, orderNo)
	}
	_, err := s.db.ExecContext(ctx, sql)
	if err != nil {
		log.ErrorWithCtx(ctx, "UpdateCommodityDataOrdersStatus tableName:%s, sql:%s, error: %v", tableName, sql, err)
		return err
	}
	log.InfoWithCtx(ctx, "UpdateCommodityDataOrdersStatus success tableName:%s, sql:%s", tableName, sql)
	return nil
}

// 获取冻结的订单
func (s *Mysql) GetCommodityDataOrdersPaying(ctx context.Context) ([]*CommodityDataOrders, error) {
	log.DebugWithCtx(ctx, "GetCommodityDataOrdersPaying ")
	orderList := make([]*CommodityDataOrders, 0)
	queryTime := time.Unix(time.Now().Unix()-5, 0)
	info := CommodityDataOrders{
		CreateTime: queryTime,
	}
	tableName := info.TableName()

	sql := fmt.Sprintf("select %s from %+v where `pay_status` = 2 and create_time < '%s'", info.SqlString(), tableName, queryTime.Format("2006-01-02 15:04:05"))
	log.DebugWithCtx(ctx, "GetCommodityDataOrdersPaying no data sql: %v", sql)
	var err error
	var inQuery string
	args := make([]interface{}, 0)
	inQuery, args, err = sqlx.In(sql)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetCommodityDataOrdersPaying In fail, err :%v", err)
		return orderList, nil
	}
	err = s.db.SelectContext(ctx, &orderList, inQuery, args...)
	if err != nil {
		if mysql.IsNoRowsError(err) {
			log.DebugWithCtx(ctx, "GetCommodityDataOrdersPaying no data pidList: %v", orderList)
			return orderList, nil
		}
		log.ErrorWithCtx(ctx, "GetCommodityDataOrdersPaying sql:%v error: %v", sql, err)
		return nil, err
	}
	log.DebugWithCtx(ctx, "GetCommodityDataOrdersPaying orderList:%v", orderList)

	return orderList, nil
}

func (s *Mysql) BatchGetCommodityDataOrders(ctx context.Context, orderNoList []string, isBigOrder bool) ([]*CommodityDataOrders, error) {
	log.DebugWithCtx(ctx, "BatchGetCommodityDataOrders orderNoList:%v", orderNoList)
	orderList := make([]*CommodityDataOrders, 0)
	if len(orderNoList) == 0 {
		log.ErrorWithCtx(ctx, "BatchGetCommodityDataOrders orderNoList is empty")
		return orderList, nil
	}

	info := CommodityDataOrders{}
	tableName := getTableNameByOrderID(ctx, orderNoList[0])
	sql := fmt.Sprintf("select %s from %+v where `order_no` in (?)", info.SqlString(), tableName)
	if isBigOrder {
		sql = fmt.Sprintf("select %s from %+v where `big_trade_no` in (?)", info.SqlString(), tableName)
	}

	var err error
	var inQuery string
	args := make([]interface{}, 0)
	inQuery, args, err = sqlx.In(sql, orderNoList)
	if err != nil {
		log.ErrorWithCtx(ctx, "BatchGetCommodityData In fail, err :%v", err)
		return orderList, nil
	}
	err = s.db.SelectContext(ctx, &orderList, inQuery, args...)
	if err != nil {
		if mysql.IsNoRowsError(err) {
			log.DebugWithCtx(ctx, "BatchGetCommodityData no data pidList: %v", orderList)
			return orderList, nil
		}
		log.ErrorWithCtx(ctx, "BatchGetCommodityData error: %v sql:%v", err, sql)
		return nil, err
	}

	return orderList, nil
}

func (s *Mysql) GetConsumeTotalCountInfo(ctx context.Context, beginTime, endTime time.Time) (*StCount, error) {
	out := &StCount{}
	temp := "SELECT COUNT(distinct(big_trade_no)) as cnt, IFNULL(SUM(total_price), 0) as worth FROM %s WHERE update_time >= ? AND update_time < ? AND pay_status = ? AND total_price>0"

	tmpCnt := &StCount{}
	query := fmt.Sprintf(temp, getTableNameByTime(beginTime))
	err := s.db.GetContext(ctx, tmpCnt, query, beginTime, endTime, pb.CommodityPayStatus_COMMODITY_PAY_STATUS_SHIPPED)
	if err != nil {
		if !mysql.IsMySQLError(err, 1146) {
			log.ErrorWithCtx(ctx, "GetConsumeTotalCountInfo fail. queryMonthTime:%v, err:%v", beginTime, err)
			return out, err
		}
	}

	out.Cnt += tmpCnt.Cnt
	out.Worth += tmpCnt.Worth

	// 查上月
	tmpCnt2 := &StCount{}
	lastMonthTime := time.Date(beginTime.Year(), beginTime.Month(), 1, 0, 0, 0, 0, time.Local).AddDate(0, -1, 0)
	query2 := fmt.Sprintf(temp, getTableNameByTime(lastMonthTime))
	err = s.db.GetContext(ctx, tmpCnt2, query2, beginTime, endTime, pb.CommodityPayStatus_COMMODITY_PAY_STATUS_SHIPPED)
	if err != nil {
		if !mysql.IsMySQLError(err, 1146) {
			log.ErrorWithCtx(ctx, "GetConsumeTotalCountInfo fail. queryMonthTime:%v, err:%v", beginTime, err)
			return out, err
		}
	}
	out.Cnt += tmpCnt2.Cnt
	out.Worth += tmpCnt2.Worth

	return out, nil
}

func (s *Mysql) GetConsumeOrderIds(ctx context.Context, beginTime, endTime time.Time) ([]string, error) {
	list := make([]string, 0)
	temp := "SELECT distinct(big_trade_no) FROM %s WHERE update_time >= ? AND update_time < ? AND pay_status = ? AND total_price>0"

	query := fmt.Sprintf(temp, getTableNameByTime(beginTime))
	err := s.db.SelectContext(ctx, &list, query, beginTime, endTime, pb.CommodityPayStatus_COMMODITY_PAY_STATUS_SHIPPED)
	if err != nil {
		if !mysql.IsMySQLError(err, 1146) {
			log.ErrorWithCtx(ctx, "GetConsumeOrderIds fail. queryMonthTime:%v, err:%v", beginTime, err)
			return list, err
		}
	}

	// 查上月
	lastMonthTime := time.Date(beginTime.Year(), beginTime.Month(), 1, 0, 0, 0, 0, time.Local).AddDate(0, -1, 0)
	query2 := fmt.Sprintf(temp, getTableNameByTime(lastMonthTime))
	list2 := make([]string, 0)
	err = s.db.SelectContext(ctx, &list2, query2, beginTime, endTime, pb.CommodityPayStatus_COMMODITY_PAY_STATUS_SHIPPED)
	if err != nil {
		if !mysql.IsMySQLError(err, 1146) {
			log.ErrorWithCtx(ctx, "GetConsumeOrderIds fail. queryMonthTime:%v, err:%v", beginTime, err)
			return list, err
		}
	}
	list = append(list, list2...)
	return list, nil
}
