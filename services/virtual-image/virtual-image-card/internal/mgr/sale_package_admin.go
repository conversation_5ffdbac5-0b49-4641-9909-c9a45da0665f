package mgr

import (
    "context"
    "encoding/json"
    "fmt"
    "golang.52tt.com/pkg/log"
    "golang.52tt.com/pkg/protocol"
    "golang.52tt.com/protocol/common/status"
    pb "golang.52tt.com/protocol/services/virtual-image-card"
    "golang.52tt.com/services/virtual-image/virtual-image-card/internal/store"
    "google.golang.org/grpc/codes"
    "time"
)

func (m *Mgr) SalePackageSort(ctx context.Context, saleIdList []uint32) error {
    if len(saleIdList) == 0 {
        log.ErrorWithCtx(ctx, "SalePackageSort saleIdList is empty")
        return protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "列表为空")
    }
    if len(saleIdList) > 100 {
        log.ErrorWithCtx(ctx, "SalePackageSort saleIdList is too long")
        return protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "列表长度超过100")
    }

    num := uint32(len(saleIdList))
    for _, id := range saleIdList {
        err := m.store.SetSalePackageWeight(ctx, id, num)
        if err != nil {
            log.ErrorWithCtx(ctx, "SalePackageSort fail err:%v", err)
            return err
        }
        num--
    }
    log.InfoWithCtx(ctx, "SalePackageSort success saleIdList:%v", saleIdList)
    return nil
}

func (m *Mgr) AddSalePackage(ctx context.Context, reqSp *pb.SalePackage, isCheck bool) (*pb.AddSalePackageResp, error) {
    out := &pb.AddSalePackageResp{}
    if reqSp.GetPackageInfo().GetId() == 0 {
        log.ErrorWithCtx(ctx, "AddSalePackage fail reqSp:%+v", reqSp)
        return out, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "套餐id不能为空")
    }

    // 获取最大权重
    maxWeight, err := m.store.GetSalePackageMaxWeight(ctx)
    if err != nil {
        log.ErrorWithCtx(ctx, "AddSalePackage fail err:%v", err)
        return out, err
    }
    reqSp.Weight = maxWeight + 1
    spPack, err := pbSpPack2dbSpPack(ctx, reqSp, 0)
    if err != nil {
        log.ErrorWithCtx(ctx, "AddSalePackage fail err:%v", err)
        return out, err
    }

    if reqSp.GetPackageInfo().GetId() != 0 && reqSp.GetEndTs() > uint64(time.Now().Unix()) {
        packageInfo := m.localCache.GetPackageConf(reqSp.GetPackageInfo().GetId())
        if packageInfo == nil {
            log.ErrorWithCtx(ctx, "AddSalePackage fail err:%v", err)
            return out, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "套餐不存在")
        }
        if !packageInfo.IsEnable {
            log.ErrorWithCtx(ctx, "AddSalePackage fail err:%v", err)
            return out, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, fmt.Sprintf("存在已停用的套餐ID:%d,不能上架", reqSp.GetPackageInfo().GetId()))
        }
    }

    if !isCheck {
        err = m.store.AddOrUpdateSalePackage(ctx, spPack)
        if err != nil {
            log.ErrorWithCtx(ctx, "AddSalePackage fail err:%v", err)
            return out, err
        }
    } else {
        saleInfo, err := m.store.GetSaleInfoByPackageId(ctx, reqSp.GetPackageInfo().GetId())
        if err != nil {
            log.ErrorWithCtx(ctx, "AddSalePackage fail err:%v", err)
            return out, err
        }
        now := time.Now().Unix()
        if saleInfo != nil && saleInfo.SaleId != reqSp.GetSaleId() && saleInfo.EndTime.Unix() > now {
            log.ErrorWithCtx(ctx, "AddSalePackage fail err:%v, saleId:%+v", err, saleInfo.SaleId)
            out.AlreadyOnShelfList = append(out.AlreadyOnShelfList, saleInfo.SaleId)
            return out, nil
        }
    }

    log.InfoWithCtx(ctx, "AddSalePackage success reqSp:%+v", reqSp)
    return out, nil
}

func pbSpPack2dbSpPack(ctx context.Context, pbSpPack *pb.SalePackage, pkgId uint32) (*store.SalePackage, error) {
    byteValue, err := json.Marshal(pbSpPack.GetCondition())
    if err != nil {
        log.ErrorWithCtx(ctx, "pbSpPack2dbSpPack json.Marshal failed condition:%+v err:%v", pbSpPack.GetCondition(), err)
        return nil, err
    }
    beginTime := time.Unix(int64(pbSpPack.GetBeginTs()), 0)
    endTime := time.Unix(int64(pbSpPack.GetEndTs()), 0)

    id := pkgId
    if pkgId == 0 {
        id = pbSpPack.GetPackageInfo().GetId()
    }
    spInfo := &store.SalePackage{
        SaleId:        pbSpPack.GetSaleId(),
        PackageId:     id,
        BeginTime:     beginTime,
        EndTime:       endTime,
        ShowCondition: string(byteValue),
        Weight:        pbSpPack.GetWeight(),
        Label:         pbSpPack.GetLabel(),
        Operator:      pbSpPack.GetOperator(),
        Remarks:       pbSpPack.GetRemarks(),
        MarketId:      pbSpPack.GetMarketId(),
    }
    return spInfo, nil
}

func (m *Mgr) AddSalePackageByGroup(ctx context.Context, reqSp *pb.SalePackage, isCheck bool) error {
    pkgListMap, err := m.store.GetPackageIdsByGroupIds(ctx, []uint32{reqSp.GetGroupId()})
    if err != nil {
        log.ErrorWithCtx(ctx, "AddSalePackageByGroup fail err:%v", err)
        return err
    }
    pkgList := pkgListMap[reqSp.GetGroupId()]
    if len(pkgList) == 0 {
        log.ErrorWithCtx(ctx, "AddSalePackageByGroup fail err:%v", err)
        return protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "没有查询到套餐组套餐")
    }

    maxWeight, err := m.store.GetSalePackageMaxWeight(ctx)
    if err != nil {
        log.ErrorWithCtx(ctx, "AddSalePackageByGroup fail err:%v", err)
        return err
    }
    salePackages := make([]*store.SalePackage, 0)
    for i, info := range pkgList {
        reqSp.Weight = maxWeight + uint32(i) + 1
        spPack, err := pbSpPack2dbSpPack(ctx, reqSp, info.PackageId)
        if err != nil {
            log.ErrorWithCtx(ctx, "AddSalePackageByGroup fail err:%v", err)
            return err
        }
        spPack.MarketId = info.MarketId
        salePackages = append(salePackages, spPack)
    }
    if !isCheck {
        err = m.store.BatchAddOrUpdateSalePackage(ctx, salePackages)
        if err != nil {
            log.ErrorWithCtx(ctx, "AddSalePackageByGroup fail err:%v", err)
            return err
        }
    }

    return nil
}

func (m *Mgr) UpdateSalePackage(ctx context.Context, reqSp *pb.SalePackage) error {
    if reqSp.GetPackageInfo().GetId() == 0 {
        log.ErrorWithCtx(ctx, "UpdateSalePackage fail reqSp:%+v", reqSp)
        return protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "套餐id不能为空")
    }

    if reqSp.GetEndTs() > uint64(time.Now().Unix()) {
        packageInfo := m.localCache.GetPackageConf(reqSp.GetPackageInfo().GetId())
        if packageInfo == nil {
            log.ErrorWithCtx(ctx, "UpdateSalePackage fail, packageInfo:%+v", packageInfo)
            return protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "套餐不存在")
        }
        if !packageInfo.IsEnable {
            log.ErrorWithCtx(ctx, "UpdateSalePackage fail, packageInfo:%+v", packageInfo)
            return protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "套餐已停用")
        }
    }

    spPack, err := pbSpPack2dbSpPack(ctx, reqSp, 0)
    if err != nil {
        log.ErrorWithCtx(ctx, "UpdateSalePackage fail err:%v", err)
        return err
    }

    err = m.store.AddOrUpdateSalePackage(ctx, spPack)
    if err != nil {
        log.ErrorWithCtx(ctx, "UpdateSalePackage fail err:%v", err)
        return err
    }

    log.InfoWithCtx(ctx, "UpdateSalePackage success reqSp:%+v", reqSp)
    return nil
}

func (m *Mgr) GetSalePackageListByStatus(ctx context.Context, reqStatus uint32) ([]*pb.SalePackage, error) {

    result := make([]*pb.SalePackage, 0)
    salePackageList, err := m.store.GetSalePackageListByStatus(ctx, reqStatus)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetSalePackageListByStatus fail err:%v", err)
        return result, err
    }

    var pidList = make([]uint32, 0)
    for _, sp := range salePackageList {
        pidList = append(pidList, sp.PackageId)
    }

    packageList, err := m.store.GetPackageListByPIds(ctx, pidList)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetPackageListByPIds fail err:%v", err)
        return result, err
    }

    mapId2Package := make(map[uint32]*store.Package)
    for _, p := range packageList {
        mapId2Package[p.Id] = p
    }

    for _, sPkg := range salePackageList {
        pbSalePackage := m.fillSalePackage(ctx, mapId2Package, sPkg, reqStatus)
        result = append(result, pbSalePackage)
    }

    return result, nil
}

func (m *Mgr) fillSalePackage(ctx context.Context, mapId2Package map[uint32]*store.Package, salePackage *store.SalePackage,
    saleStatus uint32) *pb.SalePackage {
    nowTs := uint64(time.Now().Unix())
    log.DebugWithCtx(ctx, "fillSalePackage salePackage:%+v saleStatus:%d nowTs:%d", salePackage, saleStatus, nowTs)
    result := &pb.SalePackage{}

    if dbPackage, ok := mapId2Package[salePackage.PackageId]; ok {
        tmpPackage := dbPack2pbPack(dbPackage)

        if saleStatus == uint32(pb.SalePackageStatus_ENUM_SALE_PACKAGE_STATUS_SHELF) && uint64(salePackage.BeginTime.Unix()) > nowTs {
            saleStatus = uint32(pb.SalePackageStatus_ENUM_SALE_PACKAGE_STATUS_WAIT_SHELF)
        }

        tmpSalePackage, err := m.sdbPack2spbPack(ctx, salePackage, saleStatus, nowTs, tmpPackage)
        if nil != err {
            log.ErrorWithCtx(ctx, "fillSalePackage sdbPack2spbPack err:%v", err)
            return result
        }

        result = tmpSalePackage
    }
    return result
}

func (m *Mgr) sdbPack2spbPack(ctx context.Context, sdbPack *store.SalePackage, saleStatus uint32, nowTs uint64, tmpPackage *pb.Package) (*pb.SalePackage, error) {

    if saleStatus == uint32(pb.SalePackageStatus_ENUM_SALE_PACKAGE_STATUS_SHELF) && uint64(sdbPack.BeginTime.Unix()) > nowTs {
        saleStatus = uint32(pb.SalePackageStatus_ENUM_SALE_PACKAGE_STATUS_WAIT_SHELF)
    }

    condition := &pb.DisplayCondition{}
    err := json.Unmarshal([]byte(sdbPack.ShowCondition), condition)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetSalePackageListByStatus json.Unmarshal failed condition:%s err:%v", sdbPack.ShowCondition, err)
        return nil, protocol.NewExactServerError(nil, status.ErrSuperPlayerSysErr)
    }
    tmpSalePackage := &pb.SalePackage{
        SaleId:      sdbPack.SaleId,
        PackageInfo: tmpPackage,
        BeginTs:     uint64(sdbPack.BeginTime.Unix()),
        EndTs:       uint64(sdbPack.EndTime.Unix()),
        Condition:   condition,
        Weight:      sdbPack.Weight,
        SaleStatus:  pb.SalePackageStatus(saleStatus),
        Label:       sdbPack.Label,
        Operator:    sdbPack.Operator,
        UpdateTs:    uint64(sdbPack.UpdateTs.Unix()),
        Remarks:     sdbPack.Remarks,
        MarketId:    sdbPack.MarketId,
        GroupId:     tmpPackage.GetGroupId(),
    }
    return tmpSalePackage, nil
}
