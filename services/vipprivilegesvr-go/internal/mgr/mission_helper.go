package mgr

import (
	"context"
	"encoding/binary"
	numericgo "golang.52tt.com/clients/numeric-go"
	"sync/atomic"
	"time"

	missionTL "golang.52tt.com/clients/missiontimeline"
	pushNotificationv2 "golang.52tt.com/clients/push-notification/v2"
	"golang.52tt.com/clients/seqgen/v2"
	"golang.52tt.com/clients/userscore"
	"golang.52tt.com/pkg/log"
	ttgrpc "golang.52tt.com/pkg/protocol/grpc"
	"golang.52tt.com/protocol/app/sync"
	tlsvr "golang.52tt.com/protocol/services/missiontimelinesvr"
	pushPB "golang.52tt.com/protocol/services/push-notification/v2"
)

type MissionHelper struct {
	seqgenCli  *seqgen.Client
	tlCli      *missionTL.Client
	numericCli numericgo.IClient
	scoreCli   *userscore.Client
	pushCli    *pushNotificationv2.Client
}

func NewMissionHelper(seqgenCli *seqgen.Client, pushClient *pushNotificationv2.Client, numericClient numericgo.IClient) *MissionHelper {
	return &MissionHelper{
		seqgenCli:  seqgenCli,
		tlCli:      missionTL.NewClient(),
		numericCli: numericClient,
		scoreCli:   userscore.NewClient(),
		pushCli:    pushClient,
	}
}

// 通知财富值/魅力值变更
func (m *MissionHelper) UpdateUserScoreAndCharmRichSync(ctx context.Context, uid uint32) error {
	// 获取魅力财富
	numeric, numErr := m.numericCli.GetPersonalNumericV2(ctx, uid)
	if numErr != nil {
		log.ErrorWithCtx(ctx, "Mission GetPersonalNumeric err: %s", numErr.Error())
		return numErr
	}

	score, scoreErr := m.scoreCli.GetUserScore(ctx, uid, 0)
	if scoreErr != nil {
		log.ErrorWithCtx(ctx, "Mission GetUserScore err: %s", scoreErr.Error())
		return scoreErr
	}

	//生成seq id
	seq, err := m.seqgenCli.GenerateSequence(ctx, uid, seqgen.NamespaceUser, seqgen.KeyGrowth, 1)
	if err != nil {
		log.ErrorWithCtx(ctx, "Mission GenerateSequence err: %s", err.Error())
		return err
	}

	numMsg := &tlsvr.NumericInfoMessage{
		Charm:   uint32(numeric.GetCharm()),
		Rich:    uint32(numeric.GetRich()),
		Charm64: numeric.GetCharm(),
		Rich64:  numeric.GetRich(),
	}

	err = m.tlCli.NumericChanged(ctx, uid, uint32(seq), numMsg)
	if err != nil {
		log.ErrorWithCtx(ctx, "Mission NumericChanged err: %s", err.Error())
		return err
	}

	msg := &tlsvr.UserScoreMessage{
		Score: score,
	}

	err = m.tlCli.ScoreChanged(ctx, uid, uint32(seq), msg)
	if err != nil {
		log.ErrorWithCtx(ctx, "Mission ScoreChanged err: %s", err.Error())
		return err
	}

	log.InfoWithCtx(ctx, "UpdateUserScoreAndCharmRichSync ok : uid:%d, rich:%d, charm:%d, score:%d, seq:%d", uid, numeric.GetRich(), numeric.GetCharm(), score, seq)
	return nil
}

func (s *MissionHelper) notifyClient(ctx context.Context, uidList []uint32, syncType uint32) (err error) {
	var b [4]byte
	binary.BigEndian.PutUint32(b[:], uint32(syncType)) // Network Order
	sequence := uint32(time.Now().UnixNano())
	seq := atomic.AddUint32(&sequence, 1)
	err = s.pushCli.PushToUsers(ctx, uidList, &pushPB.CompositiveNotification{
		Sequence:           seq,
		TerminalTypePolicy: pushNotificationv2.DefaultPolicy,
		AppId:              0,
		ProxyNotification: &pushPB.ProxyNotification{
			Type:    uint32(pushPB.ProxyNotification_NOTIFY),
			Payload: b[:],
		},
	})

	if err != nil {
		log.ErrorWithCtx(ctx, "NotifySyncX - users=%v(%d) type=%v seq=%d err=%s", uidList, len(uidList), syncType, seq, err.Error())
	} else {
		log.DebugWithCtx(ctx, "NotifySyncX - users=%v(%d) type=%v seq=%d", uidList, len(uidList), syncType, seq)
	}

	return err

}

func (m *MissionHelper) DoMission(rootCtx context.Context, uid uint32) {
	tryNum := 0
	ctx, cancel := ttgrpc.NewContextWithInfoTimeout(rootCtx, 5*time.Second)
	defer cancel()
	for {
		tryNum++
		err := m.UpdateUserScoreAndCharmRichSync(ctx, uid)
		if err == nil {
			break
		}
		log.ErrorWithCtx(ctx, "Mission UpdateUserScoreAndCharmRichSync uid:%d, retry_num:%d, err: %v", uid, tryNum, err)
		if tryNum == 3 {
			break
		}
	}

	_ = m.notifyClient(ctx, []uint32{uid}, uint32(sync.SyncReq_GROW))
}
