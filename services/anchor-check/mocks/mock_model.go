// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/services/anchor-check/model (interfaces: IStore)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"
	time "time"

	gomock "github.com/golang/mock/gomock"
	gorm "github.com/jinzhu/gorm"
	AnchorCheck "golang.52tt.com/protocol/services/anchor-check"
	model "golang.52tt.com/services/anchor-check/model"
)

// MockIStore is a mock of IStore interface.
type MockIStore struct {
	ctrl     *gomock.Controller
	recorder *MockIStoreMockRecorder
}

// MockIStoreMockRecorder is the mock recorder for MockIStore.
type MockIStoreMockRecorder struct {
	mock *MockIStore
}

// NewMockIStore creates a new mock instance.
func NewMockIStore(ctrl *gomock.Controller) *MockIStore {
	mock := &MockIStore{ctrl: ctrl}
	mock.recorder = &MockIStoreMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIStore) EXPECT() *MockIStoreMockRecorder {
	return m.recorder
}

// AutoMigrateAnchorCheck mocks base method.
func (m *MockIStore) AutoMigrateAnchorCheck() error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AutoMigrateAnchorCheck")
	ret0, _ := ret[0].(error)
	return ret0
}

// AutoMigrateAnchorCheck indicates an expected call of AutoMigrateAnchorCheck.
func (mr *MockIStoreMockRecorder) AutoMigrateAnchorCheck() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AutoMigrateAnchorCheck", reflect.TypeOf((*MockIStore)(nil).AutoMigrateAnchorCheck))
}

// AutoMigrateAnchorCheckWhite mocks base method.
func (m *MockIStore) AutoMigrateAnchorCheckWhite() error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AutoMigrateAnchorCheckWhite")
	ret0, _ := ret[0].(error)
	return ret0
}

// AutoMigrateAnchorCheckWhite indicates an expected call of AutoMigrateAnchorCheckWhite.
func (mr *MockIStoreMockRecorder) AutoMigrateAnchorCheckWhite() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AutoMigrateAnchorCheckWhite", reflect.TypeOf((*MockIStore)(nil).AutoMigrateAnchorCheckWhite))
}

// Close mocks base method.
func (m *MockIStore) Close() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Close")
}

// Close indicates an expected call of Close.
func (mr *MockIStoreMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockIStore)(nil).Close))
}

// GetAllNotAudioCheckList mocks base method.
func (m *MockIStore) GetAllNotAudioCheckList(arg0 uint32) ([]*model.AnchorCheck, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllNotAudioCheckList", arg0)
	ret0, _ := ret[0].([]*model.AnchorCheck)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllNotAudioCheckList indicates an expected call of GetAllNotAudioCheckList.
func (mr *MockIStoreMockRecorder) GetAllNotAudioCheckList(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllNotAudioCheckList", reflect.TypeOf((*MockIStore)(nil).GetAllNotAudioCheckList), arg0)
}

// GetAllNotRegisterList mocks base method.
func (m *MockIStore) GetAllNotRegisterList(arg0 uint32) ([]*model.AnchorCheck, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllNotRegisterList", arg0)
	ret0, _ := ret[0].([]*model.AnchorCheck)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllNotRegisterList indicates an expected call of GetAllNotRegisterList.
func (mr *MockIStoreMockRecorder) GetAllNotRegisterList(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllNotRegisterList", reflect.TypeOf((*MockIStore)(nil).GetAllNotRegisterList), arg0)
}

// GetAllSubmitNoFileUrlList mocks base method.
func (m *MockIStore) GetAllSubmitNoFileUrlList(arg0 int64) []*model.AnchorCheck {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllSubmitNoFileUrlList", arg0)
	ret0, _ := ret[0].([]*model.AnchorCheck)
	return ret0
}

// GetAllSubmitNoFileUrlList indicates an expected call of GetAllSubmitNoFileUrlList.
func (mr *MockIStoreMockRecorder) GetAllSubmitNoFileUrlList(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllSubmitNoFileUrlList", reflect.TypeOf((*MockIStore)(nil).GetAllSubmitNoFileUrlList), arg0)
}

// GetAllWhiteCountWithTimeRange mocks base method.
func (m *MockIStore) GetAllWhiteCountWithTimeRange(arg0, arg1, arg2 uint32, arg3 int32) uint32 {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllWhiteCountWithTimeRange", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(uint32)
	return ret0
}

// GetAllWhiteCountWithTimeRange indicates an expected call of GetAllWhiteCountWithTimeRange.
func (mr *MockIStoreMockRecorder) GetAllWhiteCountWithTimeRange(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllWhiteCountWithTimeRange", reflect.TypeOf((*MockIStore)(nil).GetAllWhiteCountWithTimeRange), arg0, arg1, arg2, arg3)
}

// GetAllWhiteWithTimeRange mocks base method.
func (m *MockIStore) GetAllWhiteWithTimeRange(arg0, arg1, arg2, arg3, arg4 uint32, arg5 int32) []model.AnchorCheckWhite {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllWhiteWithTimeRange", arg0, arg1, arg2, arg3, arg4, arg5)
	ret0, _ := ret[0].([]model.AnchorCheckWhite)
	return ret0
}

// GetAllWhiteWithTimeRange indicates an expected call of GetAllWhiteWithTimeRange.
func (mr *MockIStoreMockRecorder) GetAllWhiteWithTimeRange(arg0, arg1, arg2, arg3, arg4, arg5 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllWhiteWithTimeRange", reflect.TypeOf((*MockIStore)(nil).GetAllWhiteWithTimeRange), arg0, arg1, arg2, arg3, arg4, arg5)
}

// GetAnchorCheckByCreateTime mocks base method.
func (m *MockIStore) GetAnchorCheckByCreateTime(arg0 uint32) ([]*model.AnchorCheck, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAnchorCheckByCreateTime", arg0)
	ret0, _ := ret[0].([]*model.AnchorCheck)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAnchorCheckByCreateTime indicates an expected call of GetAnchorCheckByCreateTime.
func (mr *MockIStoreMockRecorder) GetAnchorCheckByCreateTime(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAnchorCheckByCreateTime", reflect.TypeOf((*MockIStore)(nil).GetAnchorCheckByCreateTime), arg0)
}

// GetAnchorCheckCount mocks base method.
func (m *MockIStore) GetAnchorCheckCount(arg0 *AnchorCheck.AnchorCheckGetCheckListReq) uint32 {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAnchorCheckCount", arg0)
	ret0, _ := ret[0].(uint32)
	return ret0
}

// GetAnchorCheckCount indicates an expected call of GetAnchorCheckCount.
func (mr *MockIStoreMockRecorder) GetAnchorCheckCount(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAnchorCheckCount", reflect.TypeOf((*MockIStore)(nil).GetAnchorCheckCount), arg0)
}

// GetAnchorCheckData mocks base method.
func (m *MockIStore) GetAnchorCheckData(arg0 string) *model.AnchorCheck {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAnchorCheckData", arg0)
	ret0, _ := ret[0].(*model.AnchorCheck)
	return ret0
}

// GetAnchorCheckData indicates an expected call of GetAnchorCheckData.
func (mr *MockIStoreMockRecorder) GetAnchorCheckData(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAnchorCheckData", reflect.TypeOf((*MockIStore)(nil).GetAnchorCheckData), arg0)
}

// GetAnchorCheckHistory mocks base method.
func (m *MockIStore) GetAnchorCheckHistory(arg0 uint32) ([]*model.AnchorCheck, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAnchorCheckHistory", arg0)
	ret0, _ := ret[0].([]*model.AnchorCheck)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAnchorCheckHistory indicates an expected call of GetAnchorCheckHistory.
func (mr *MockIStoreMockRecorder) GetAnchorCheckHistory(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAnchorCheckHistory", reflect.TypeOf((*MockIStore)(nil).GetAnchorCheckHistory), arg0)
}

// GetAnchorCheckList mocks base method.
func (m *MockIStore) GetAnchorCheckList(arg0 *AnchorCheck.AnchorCheckGetCheckListReq) []*model.AnchorCheck {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAnchorCheckList", arg0)
	ret0, _ := ret[0].([]*model.AnchorCheck)
	return ret0
}

// GetAnchorCheckList indicates an expected call of GetAnchorCheckList.
func (mr *MockIStoreMockRecorder) GetAnchorCheckList(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAnchorCheckList", reflect.TypeOf((*MockIStore)(nil).GetAnchorCheckList), arg0)
}

// GetAnchorCheckRecord mocks base method.
func (m *MockIStore) GetAnchorCheckRecord(arg0 []string) ([]*model.AnchorCheckRecord, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAnchorCheckRecord", arg0)
	ret0, _ := ret[0].([]*model.AnchorCheckRecord)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAnchorCheckRecord indicates an expected call of GetAnchorCheckRecord.
func (mr *MockIStoreMockRecorder) GetAnchorCheckRecord(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAnchorCheckRecord", reflect.TypeOf((*MockIStore)(nil).GetAnchorCheckRecord), arg0)
}

// GetAnchorCheckTargetRecord mocks base method.
func (m *MockIStore) GetAnchorCheckTargetRecord(arg0 []string) ([]*model.AnchorCheckRecord, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAnchorCheckTargetRecord", arg0)
	ret0, _ := ret[0].([]*model.AnchorCheckRecord)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAnchorCheckTargetRecord indicates an expected call of GetAnchorCheckTargetRecord.
func (mr *MockIStoreMockRecorder) GetAnchorCheckTargetRecord(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAnchorCheckTargetRecord", reflect.TypeOf((*MockIStore)(nil).GetAnchorCheckTargetRecord), arg0)
}

 

 
// GetExpireListWithTimeRange mocks base method.
func (m *MockIStore) GetExpireListWithTimeRange(arg0, arg1 uint32) []*model.AnchorCheckWhite {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetExpireListWithTimeRange", arg0, arg1)
	ret0, _ := ret[0].([]*model.AnchorCheckWhite)
	return ret0
}

// GetExpireListWithTimeRange indicates an expected call of GetExpireListWithTimeRange.
func (mr *MockIStoreMockRecorder) GetExpireListWithTimeRange(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetExpireListWithTimeRange", reflect.TypeOf((*MockIStore)(nil).GetExpireListWithTimeRange), arg0, arg1)
}

// GetFirstAnchorCheckWhiteData mocks base method.
func (m *MockIStore) GetFirstAnchorCheckWhiteData(arg0 uint32) (*model.AnchorCheckWhite, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetFirstAnchorCheckWhiteData", arg0)
	ret0, _ := ret[0].(*model.AnchorCheckWhite)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetFirstAnchorCheckWhiteData indicates an expected call of GetFirstAnchorCheckWhiteData.
func (mr *MockIStoreMockRecorder) GetFirstAnchorCheckWhiteData(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFirstAnchorCheckWhiteData", reflect.TypeOf((*MockIStore)(nil).GetFirstAnchorCheckWhiteData), arg0)
}

// GetFirstAnchorCheckWhiteDataNoTime mocks base method.
func (m *MockIStore) GetFirstAnchorCheckWhiteDataNoTime(arg0 uint32) *model.AnchorCheckWhite {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetFirstAnchorCheckWhiteDataNoTime", arg0)
	ret0, _ := ret[0].(*model.AnchorCheckWhite)
	return ret0
}

// GetFirstAnchorCheckWhiteDataNoTime indicates an expected call of GetFirstAnchorCheckWhiteDataNoTime.
func (mr *MockIStoreMockRecorder) GetFirstAnchorCheckWhiteDataNoTime(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFirstAnchorCheckWhiteDataNoTime", reflect.TypeOf((*MockIStore)(nil).GetFirstAnchorCheckWhiteDataNoTime), arg0)
}

// GetLastAnchorCheckUpgrade mocks base method.
func (m *MockIStore) GetLastAnchorCheckUpgrade(arg0 *gorm.DB, arg1 uint32) (uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLastAnchorCheckUpgrade", arg0, arg1)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLastAnchorCheckUpgrade indicates an expected call of GetLastAnchorCheckUpgrade.
func (mr *MockIStoreMockRecorder) GetLastAnchorCheckUpgrade(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLastAnchorCheckUpgrade", reflect.TypeOf((*MockIStore)(nil).GetLastAnchorCheckUpgrade), arg0, arg1)
}

// GetLastCreateScore mocks base method.
func (m *MockIStore) GetLastCreateScore(arg0 uint32) *model.AnchorCheck {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLastCreateScore", arg0)
	ret0, _ := ret[0].(*model.AnchorCheck)
	return ret0
}

// GetLastCreateScore indicates an expected call of GetLastCreateScore.
func (mr *MockIStoreMockRecorder) GetLastCreateScore(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLastCreateScore", reflect.TypeOf((*MockIStore)(nil).GetLastCreateScore), arg0)
}

// Init mocks base method.
func (m *MockIStore) Init() error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Init")
	ret0, _ := ret[0].(error)
	return ret0
}

// Init indicates an expected call of Init.
func (mr *MockIStoreMockRecorder) Init() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Init", reflect.TypeOf((*MockIStore)(nil).Init))
}

// InsertAnchorCheck mocks base method.
func (m *MockIStore) InsertAnchorCheck(arg0, arg1, arg2 string, arg3, arg4 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InsertAnchorCheck", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(error)
	return ret0
}

// InsertAnchorCheck indicates an expected call of InsertAnchorCheck.
func (mr *MockIStoreMockRecorder) InsertAnchorCheck(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InsertAnchorCheck", reflect.TypeOf((*MockIStore)(nil).InsertAnchorCheck), arg0, arg1, arg2, arg3, arg4)
}

// InsertAnchorCheckRecord mocks base method.
func (m *MockIStore) InsertAnchorCheckRecord(arg0 []*model.AnchorCheckRecord) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InsertAnchorCheckRecord", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// InsertAnchorCheckRecord indicates an expected call of InsertAnchorCheckRecord.
func (mr *MockIStoreMockRecorder) InsertAnchorCheckRecord(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InsertAnchorCheckRecord", reflect.TypeOf((*MockIStore)(nil).InsertAnchorCheckRecord), arg0)
}

// InsertAnchorCheckUpgrade mocks base method.
func (m *MockIStore) InsertAnchorCheckUpgrade(arg0 context.Context, arg1 uint32, arg2 time.Time) (uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InsertAnchorCheckUpgrade", arg0, arg1, arg2)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// InsertAnchorCheckUpgrade indicates an expected call of InsertAnchorCheckUpgrade.
func (mr *MockIStoreMockRecorder) InsertAnchorCheckUpgrade(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InsertAnchorCheckUpgrade", reflect.TypeOf((*MockIStore)(nil).InsertAnchorCheckUpgrade), arg0, arg1, arg2)
}

// InsertAnchorCheckWhite mocks base method.
func (m *MockIStore) InsertAnchorCheckWhite(arg0 uint32, arg1 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InsertAnchorCheckWhite", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// InsertAnchorCheckWhite indicates an expected call of InsertAnchorCheckWhite.
func (mr *MockIStoreMockRecorder) InsertAnchorCheckWhite(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InsertAnchorCheckWhite", reflect.TypeOf((*MockIStore)(nil).InsertAnchorCheckWhite), arg0, arg1)
}

// UpdateAllAnchorCheckWhiteStatus mocks base method.
func (m *MockIStore) UpdateAllAnchorCheckWhiteStatus(arg0 uint32, arg1 byte) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateAllAnchorCheckWhiteStatus", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateAllAnchorCheckWhiteStatus indicates an expected call of UpdateAllAnchorCheckWhiteStatus.
func (mr *MockIStoreMockRecorder) UpdateAllAnchorCheckWhiteStatus(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateAllAnchorCheckWhiteStatus", reflect.TypeOf((*MockIStore)(nil).UpdateAllAnchorCheckWhiteStatus), arg0, arg1)
}

// UpdateAnchorCheckWhiteStatus mocks base method.
func (m *MockIStore) UpdateAnchorCheckWhiteStatus(arg0 uint32, arg1 byte) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateAnchorCheckWhiteStatus", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateAnchorCheckWhiteStatus indicates an expected call of UpdateAnchorCheckWhiteStatus.
func (mr *MockIStoreMockRecorder) UpdateAnchorCheckWhiteStatus(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateAnchorCheckWhiteStatus", reflect.TypeOf((*MockIStore)(nil).UpdateAnchorCheckWhiteStatus), arg0, arg1)
}

// UpdateAudioCheckStatus mocks base method.
func (m *MockIStore) UpdateAudioCheckStatus(arg0 string, arg1, arg2 float64, arg3, arg4 uint32, arg5 string) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateAudioCheckStatus", arg0, arg1, arg2, arg3, arg4, arg5)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateAudioCheckStatus indicates an expected call of UpdateAudioCheckStatus.
func (mr *MockIStoreMockRecorder) UpdateAudioCheckStatus(arg0, arg1, arg2, arg3, arg4, arg5 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateAudioCheckStatus", reflect.TypeOf((*MockIStore)(nil).UpdateAudioCheckStatus), arg0, arg1, arg2, arg3, arg4, arg5)
}

// UpdateAudioLiveCheckStatus mocks base method.
func (m *MockIStore) UpdateAudioLiveCheckStatus(arg0 string, arg1 float64, arg2 string) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateAudioLiveCheckStatus", arg0, arg1, arg2)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateAudioLiveCheckStatus indicates an expected call of UpdateAudioLiveCheckStatus.
func (mr *MockIStoreMockRecorder) UpdateAudioLiveCheckStatus(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateAudioLiveCheckStatus", reflect.TypeOf((*MockIStore)(nil).UpdateAudioLiveCheckStatus), arg0, arg1, arg2)
}

// UpdateFileUrl mocks base method.
func (m *MockIStore) UpdateFileUrl(arg0, arg1 string, arg2, arg3 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateFileUrl", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateFileUrl indicates an expected call of UpdateFileUrl.
func (mr *MockIStoreMockRecorder) UpdateFileUrl(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateFileUrl", reflect.TypeOf((*MockIStore)(nil).UpdateFileUrl), arg0, arg1, arg2, arg3)
}

// UpdateRegisterStatus mocks base method.
func (m *MockIStore) UpdateRegisterStatus(arg0 string) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateRegisterStatus", arg0)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateRegisterStatus indicates an expected call of UpdateRegisterStatus.
func (mr *MockIStoreMockRecorder) UpdateRegisterStatus(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateRegisterStatus", reflect.TypeOf((*MockIStore)(nil).UpdateRegisterStatus), arg0)
}

// UpdateScores mocks base method.
func (m *MockIStore) UpdateScores(arg0 string, arg1 *model.AnchorCheck) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateScores", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateScores indicates an expected call of UpdateScores.
func (mr *MockIStoreMockRecorder) UpdateScores(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateScores", reflect.TypeOf((*MockIStore)(nil).UpdateScores), arg0, arg1)
}

// UpdateStatus mocks base method.
func (m *MockIStore) UpdateStatus(arg0 string, arg1 byte) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateStatus", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateStatus indicates an expected call of UpdateStatus.
func (mr *MockIStoreMockRecorder) UpdateStatus(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateStatus", reflect.TypeOf((*MockIStore)(nil).UpdateStatus), arg0, arg1)
}
