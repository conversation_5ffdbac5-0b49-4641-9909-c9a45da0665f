package manager

import (
	"context"
	"fmt"
	"reflect"
	"testing"
	"time"

	"github.com/go-redis/redis"
	_ "github.com/go-sql-driver/mysql"
	"github.com/golang/mock/gomock"
	anchor_level "golang.52tt.com/clients/anchor-level"
	apicenter "golang.52tt.com/clients/apicenter/apiserver"
	anchor_levelmocks "golang.52tt.com/clients/mocks/anchor-level"
	apicentermocks "golang.52tt.com/clients/mocks/apicenter/apiserver"
	"golang.52tt.com/pkg/config"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	pb "golang.52tt.com/protocol/services/anchor-check"
	apiPB "golang.52tt.com/protocol/services/apicenter/apiserver"
	"golang.52tt.com/services/anchor-check/cache"
	"golang.52tt.com/services/anchor-check/conf"
	"golang.52tt.com/services/anchor-check/mocks"
	"golang.52tt.com/services/anchor-check/model"
	"golang.52tt.com/services/anchor-check/utils"
	"golang.52tt.com/services/anchor-check/zego"
)

func init() {
	log.SetLevel(log.DebugLevel)
}

func TestAnchorCheckMgr_GetCheckList(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockCache := mocks.NewMockIRedisCache(ctl)
	mockStore := mocks.NewMockIStore(ctl)
	mockZego := mocks.NewMockIZegoClient(ctl)
	//mockApicenter := apicentermocks.NewMockIClient(ctl)
	offset := uint32(0)
	limit := uint32(1)
	uid := uint32(123)
	taskId := "abc"
	roomId := "xyz"
	streamId := "s123"
	fileUrl := "https://..."
	cyScore := uint32(1)
	sxScore := uint32(2)
	yzScore := uint32(3)
	kcScore := uint32(4)
	status := int8(0)
	beginTime := uint32(1662432320)
	endTime := uint32(1662450950)
	remark := "test"
	createTime := uint32(1662432320)
	updateTime := uint32(1662450950)
	count := uint32(1)
	list := []*model.AnchorCheck{{
		TaskId:     taskId,
		RoomId:     roomId,
		StreamId:   streamId,
		Uid:        uid,
		FileUrl:    fileUrl,
		CyScore:    cyScore,
		SxScore:    sxScore,
		YzScore:    yzScore,
		KcScore:    kcScore,
		Status:     uint8(status),
		BeginTime:  beginTime,
		EndTime:    endTime,
		Remark:     remark,
		CreateTime: createTime,
		UpdateTime: updateTime,
	}}

	/*
		var offset, limit, uid, beginTime, endTime uint32 = req.GetOffset(), req.GetLimit(), req.GetUid(), req.GetBeginTime(), req.GetEndTime()
		var status int8 = int8(req.GetStatus())

	*/
	req := &pb.AnchorCheckGetCheckListReq{
		Offset:    offset,
		Limit:     limit,
		Uid:       uid,
		Status:    int32(status),
		BeginTime: beginTime,
		EndTime:   endTime,
	}

	gomock.InOrder(
		mockStore.EXPECT().GetAnchorCheckList(gomock.Any()).Return(list),
		mockStore.EXPECT().GetAnchorCheckCount(gomock.Any()).Return(count),
	)

	type fields struct {
		cache      cache.IRedisCache
		store      model.IStore
		zegoClient zego.IZegoClient
		apiClient  apicenter.IClient
	}
	type args struct {
		offset    uint32
		limit     uint32
		status    int32
		uid       uint32
		beginTime uint32
		endTime   uint32
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   []*model.AnchorCheck
		want1  uint32
	}{
		// Add test cases.
		{
			name: "GetCheckList",
			fields: fields{
				cache:      mockCache,
				store:      mockStore,
				zegoClient: mockZego,
				//apiClient:  mockApicenter,
			},
			args: args{
				offset:    offset,
				limit:     limit,
				status:    int32(status),
				uid:       uid,
				beginTime: beginTime,
				endTime:   endTime,
			},
			want:  list,
			want1: 1,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mgr := &AnchorCheckMgr{
				cache:      tt.fields.cache,
				store:      tt.fields.store,
				zegoClient: tt.fields.zegoClient,
				apiClient:  tt.fields.apiClient,
			}
			got, got1 := mgr.GetCheckList(req)
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetCheckList() got = %v, want %v", got, tt.want)
			}
			if got1 != tt.want1 {
				t.Errorf("GetCheckList() got1 = %v, want %v", got1, tt.want1)
			}
		})
	}
}

func TestAnchorCheckMgr_GetExpireListWithTimeRange(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	beginTime := uint32(1662432320)
	endTime := uint32(1662450950)
	beginTime2 := uint32(utils.GetCreateTimeAtExpireTime(int64(beginTime - 1))) //要把时间减1，可以覆盖到3天前1天
	endTime2 := uint32(utils.GetCreateTimeAtExpireTime(int64(endTime)))
	uid := uint32(123)

	mockCache := mocks.NewMockIRedisCache(ctl)
	mockStore := mocks.NewMockIStore(ctl)
	mockZego := mocks.NewMockIZegoClient(ctl)
	//mockApicenter := apicentermocks.NewMockIClient(ctl)

	gomock.InOrder(
		mockStore.EXPECT().GetExpireListWithTimeRange(beginTime2, endTime2).Return([]*model.AnchorCheckWhite{
			{
				Uid: uid,
			},
		}),
	)

	type fields struct {
		cache      cache.IRedisCache
		store      model.IStore
		zegoClient zego.IZegoClient
		apiClient  apicenter.IClient
	}
	type args struct {
		beginTime uint32
		endTime   uint32
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   []uint32
	}{
		// Add test cases.
		{
			name: "TestAnchorCheckMgr_GetExpireListWithTimeRange",
			fields: fields{
				cache:      mockCache,
				store:      mockStore,
				zegoClient: mockZego,
				//apiClient:  mockApicenter,
			},
			args: args{
				beginTime: beginTime,
				endTime:   endTime,
			},
			want: []uint32{uid},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mgr := &AnchorCheckMgr{
				cache:      tt.fields.cache,
				store:      tt.fields.store,
				zegoClient: tt.fields.zegoClient,
				apiClient:  tt.fields.apiClient,
			}
			if got := mgr.GetExpireListWithTimeRange(tt.args.beginTime, tt.args.endTime); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetExpireListWithTimeRange() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestAnchorCheckMgr_GetLastCreateScore(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	uid := uint32(123)

	mockCache := mocks.NewMockIRedisCache(ctl)
	mockStore := mocks.NewMockIStore(ctl)
	mockZego := mocks.NewMockIZegoClient(ctl)
	//mockApicenter := apicentermocks.NewMockIClient(ctl)

	data := &model.AnchorCheck{}

	gomock.InOrder(
		mockStore.EXPECT().GetLastCreateScore(uid).Return(data),
	)

	type fields struct {
		cache      cache.IRedisCache
		store      model.IStore
		zegoClient zego.IZegoClient
		//apiClient  apicenter.IClient
	}
	type args struct {
		uid uint32
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   *model.AnchorCheck
	}{
		// Add test cases.
		{
			name: "TestAnchorCheckMgr_GetLastCreateScore",
			fields: fields{
				cache:      mockCache,
				store:      mockStore,
				zegoClient: mockZego,
				//apiClient:  mockApicenter,
			},
			args: args{
				uid: uid,
			},
			want: data,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mgr := &AnchorCheckMgr{
				cache:      tt.fields.cache,
				store:      tt.fields.store,
				zegoClient: tt.fields.zegoClient,
				//apiClient:  tt.fields.apiClient,
			}
			if got := mgr.GetLastCreateScore(tt.args.uid); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetLastCreateScore() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestAnchorCheckMgr_GetMaxRecordTime(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	maxRecordTime := 123

	mockCache := mocks.NewMockIRedisCache(ctl)
	mockStore := mocks.NewMockIStore(ctl)
	mockZego := mocks.NewMockIZegoClient(ctl)
	//mockApicenter := apicentermocks.NewMockIClient(ctl)

	gomock.InOrder(
		mockZego.EXPECT().GetMaxRecordTime().Return(maxRecordTime),
	)

	type fields struct {
		cache      cache.IRedisCache
		store      model.IStore
		zegoClient zego.IZegoClient
		apiClient  apicenter.IClient
	}
	tests := []struct {
		name   string
		fields fields
		want   int
	}{
		// Add test cases.
		{
			name: "TestAnchorCheckMgr_GetMaxRecordTime",
			fields: fields{
				cache:      mockCache,
				store:      mockStore,
				zegoClient: mockZego,
				//apiClient:  mockApicenter,
			},
			want: maxRecordTime,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mgr := &AnchorCheckMgr{
				cache:      tt.fields.cache,
				store:      tt.fields.store,
				zegoClient: tt.fields.zegoClient,
				apiClient:  tt.fields.apiClient,
			}
			if got := mgr.GetMaxRecordTime(); got != tt.want {
				t.Errorf("GetMaxRecordTime() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestAnchorCheckMgr_Init(t *testing.T) {

	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockCache := mocks.NewMockIRedisCache(ctl)
	mockStore := mocks.NewMockIStore(ctl)
	mockZego := mocks.NewMockIZegoClient(ctl)
	//mockApicenter := apicentermocks.NewMockIClient(ctl)

	gomock.InOrder(
		mockStore.EXPECT().Init().Return(nil),
		//mockStore.EXPECT().AutoMigrateAnchorCheck().Return(nil),
		//mockStore.EXPECT().AutoMigrateAnchorCheckWhite().Return(nil),
	)

	type fields struct {
		cache      cache.IRedisCache
		store      model.IStore
		zegoClient zego.IZegoClient
		apiClient  apicenter.IClient
	}
	tests := []struct {
		name    string
		fields  fields
		wantErr bool
	}{
		// Add test cases.
		{
			name: "TestAnchorCheckMgr_Init",
			fields: fields{
				cache:      mockCache,
				store:      mockStore,
				zegoClient: mockZego,
				//apiClient:  mockApicenter,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mgr := &AnchorCheckMgr{
				cache:      tt.fields.cache,
				store:      tt.fields.store,
				zegoClient: tt.fields.zegoClient,
				apiClient:  tt.fields.apiClient,
			}
			if err := mgr.Init(); (err != nil) != tt.wantErr {
				t.Errorf("Init() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}

}

func TestAnchorCheckMgr_SendTTMsg(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockCache := mocks.NewMockIRedisCache(ctl)
	mockStore := mocks.NewMockIStore(ctl)
	mockZego := mocks.NewMockIZegoClient(ctl)
	mockApicenter := apicentermocks.NewMockIClient(ctl)

	uid := uint32(123)
	cyScore := uint32(5)
	sum := uint32(6)
	msg := new(apiPB.ImMsg)
	msg.ImType = &apiPB.ImType{
		SenderType:   uint32(apiPB.IM_SENDER_TYPE_IM_SENDER_NORMAL),
		ReceiverType: uint32(apiPB.IM_RECEIVER_TYPE_IM_RECEIVER_USER),
	}

	msg.FromUid = 10000 // TT语音助手

	msg.ToIdList = []uint32{uid}
	msg.ImContent = &apiPB.ImContent{}
	content := fmt.Sprintf("很遗憾由于您的考核评级为D级（才艺分：%d分，总分：%d分），未能通过考核，现按规定将对直播间进行封禁操作，敬请谅解。\n\n"+
		"若您认为本次考核未能发挥最佳水平，可申请复核，申请地址：https://jinshuju.net/f/K1xMOZ",
		cyScore, sum)
	hightlight := "https://jinshuju.net/f/K1xMOZ"
	url := "https://jinshuju.net/f/K1xMOZ"
	msg.ImContent.TextHlUrl = &apiPB.ImTextWithHighlightUrl{
		Content:    content,
		Hightlight: hightlight,
		Url:        url,
	}
	msg.ImType.ContentType = uint32(apiPB.IM_CONTENT_TYPE_IM_CONTENT_TEXT_WITH_HL_URL)
	msg.Platform = apiPB.Platform_UNSPECIFIED
	msg.AppPlatform = "all"

	gomock.InOrder(
		mockApicenter.EXPECT().SendImMsg(context.Background(), uid, protocol.TT, []*apiPB.ImMsg{msg}, true).Return(nil),
	)

	type fields struct {
		cache      cache.IRedisCache
		store      model.IStore
		zegoClient zego.IZegoClient
		apiClient  apicenter.IClient
	}
	type args struct {
		ctx     context.Context
		uid     uint32
		content string
		hlight  string
		url     string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		// Add test cases.
		{
			name: "TestAnchorCheckMgr_SendTTMsg",
			fields: fields{
				cache:      mockCache,
				store:      mockStore,
				zegoClient: mockZego,
				apiClient:  mockApicenter,
			},
			args: args{
				ctx:     context.Background(),
				uid:     uid,
				content: content,
				hlight:  hightlight,
				url:     url,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mgr := &AnchorCheckMgr{
				cache:      tt.fields.cache,
				store:      tt.fields.store,
				zegoClient: tt.fields.zegoClient,
				apiClient:  tt.fields.apiClient,
			}
			if err := mgr.SendTTMsg(tt.args.ctx, tt.args.uid, tt.args.content, tt.args.hlight, tt.args.url); (err != nil) != tt.wantErr {
				t.Errorf("SendTTMsg() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

/*
// go test -timeout 30s -run ^TestAnchorCheckMgr_SendWhiteExpireMsg$ golang.52tt.com/services/anchor-check/manager -v -count=1
func TestAnchorCheckMgr_SendWhiteExpireMsg(t *testing.T) {
	//return
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockdyconf := mocks.NewMockISDyConfigHandler(ctl)
	imsg := conf.AnchorCheckImMsg{}
	mockCache := mocks.NewMockIRedisCache(ctl)
	mockStore := mocks.NewMockIStore(ctl)
	mockZego := mocks.NewMockIZegoClient(ctl)
	mockApicenter := apicentermocks.NewMockIClient(ctl)

	uid := uint32(123)
	msg := new(apiPB.ImMsg)
	msg.ImType = &apiPB.ImType{
		SenderType:   uint32(apiPB.IM_SENDER_TYPE_IM_SENDER_NORMAL),
		ReceiverType: uint32(apiPB.IM_RECEIVER_TYPE_IM_RECEIVER_USER),
	}

	msg.FromUid = 10000 // TT语音助手

	msg.ToIdList = []uint32{uid}
	msg.ImContent = &apiPB.ImContent{}
	content := "由于未在3天内上传考核音频，很遗憾您未能通过考核，现按规定将对直播间进行封禁操作，敬请谅解。"
	hightlight := ""
	url := ""
	msg.ImContent.TextHlUrl = &apiPB.ImTextWithHighlightUrl{
		Content:    content,
		Hightlight: hightlight,
		Url:        url,
	}
	msg.ImType.ContentType = uint32(apiPB.IM_CONTENT_TYPE_IM_CONTENT_TEXT_WITH_HL_URL)
	msg.Platform = apiPB.Platform_UNSPECIFIED
	msg.AppPlatform = "all"

	tNow := time.Unix(1662566400, 0)
	expireTime := utils.GetCreateTimeAtExpireTime(tNow.Unix())

	gomock.InOrder(
		mockCache.EXPECT().SetSendWhiteExpireMsgDate("20220908").Return(true, nil),
		mockStore.EXPECT().GetExpireListWithTimeRange(uint32(expireTime-86400), uint32(expireTime)).Return([]*model.AnchorCheckWhite{{
			Uid: uid,
		}}),
		mockdyconf.EXPECT().GetAnchorCheckImMsg().Return(imsg),
		mockApicenter.EXPECT().SendImMsg(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil),
		mockCache.EXPECT().DelAnchorCheckWhiteV2(uid).Return(nil),
	)

	type fields struct {
		cache      cache.IRedisCache
		store      model.IStore
		zegoClient zego.IZegoClient
		apiClient  apicenter.IClient
		conf.ISDyConfigHandler
	}
	type args struct {
		ctx  context.Context
		tNow time.Time
	}
	tests := []struct {
		name   string
		fields fields
		args   args
	}{
		// Add test cases.
		{
			name: "TestAnchorCheckMgr_SendWhiteExpireMsg",
			fields: fields{
				cache:             mockCache,
				store:             mockStore,
				zegoClient:        mockZego,
				apiClient:         mockApicenter,
				ISDyConfigHandler: mockdyconf,
			},
			args: args{
				ctx:  context.Background(),
				tNow: tNow,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mgr := &AnchorCheckMgr{
				cache:             tt.fields.cache,
				store:             tt.fields.store,
				zegoClient:        tt.fields.zegoClient,
				apiClient:         tt.fields.apiClient,
				ISDyConfigHandler: tt.fields.ISDyConfigHandler,
			}
			mgr.SendWhiteExpireMsg(tt.args.ctx, tt.args.tNow)
		})
	}
}
*/

/*
// go test -timeout 30s -run ^TestAnchorCheckMgr_SendWillExpireMsg$ golang.52tt.com/services/anchor-check/manager -v -count=1
func TestAnchorCheckMgr_SendWillExpireMsg(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockCache := mocks.NewMockIRedisCache(ctl)
	mockStore := mocks.NewMockIStore(ctl)
	mockZego := mocks.NewMockIZegoClient(ctl)
	mockApicenter := apicentermocks.NewMockIClient(ctl)

	uid := uint32(123)
	msg := new(apiPB.ImMsg)
	msg.ImType = &apiPB.ImType{
		SenderType:   uint32(apiPB.IM_SENDER_TYPE_IM_SENDER_NORMAL),
		ReceiverType: uint32(apiPB.IM_RECEIVER_TYPE_IM_RECEIVER_USER),
	}

	msg.FromUid = 10000 // TT语音助手

	msg.ToIdList = []uint32{uid}
	msg.ImContent = &apiPB.ImContent{}
	content := "请在今天内开播并提交音频，未提交将封禁你的直播间 立即开播>"
	hightlight := "立即开播>"
	url := "tt://m.52tt.com/home?main_pos=0&vice_pos=1"
	msg.ImContent.TextHlUrl = &apiPB.ImTextWithHighlightUrl{
		Content:    content,
		Hightlight: hightlight,
		Url:        url,
	}
	msg.ImType.ContentType = uint32(apiPB.IM_CONTENT_TYPE_IM_CONTENT_TEXT_WITH_HL_URL)
	msg.Platform = apiPB.Platform_UNSPECIFIED
	msg.AppPlatform = "all"

	tNow := time.Unix(1662595200, 0)
	expireTime := utils.GetCreateTimeAtExpireTime(tNow.Unix() + 16*3600)
	mockdyconf := mocks.NewMockISDyConfigHandler(ctl)
	imsg := conf.AnchorCheckImMsg{}

	gomock.InOrder(
		mockCache.EXPECT().SetSendWhiteWillExpireMsgDate("20220908").Return(true, nil),
		mockStore.EXPECT().GetExpireListWithTimeRange(uint32(expireTime-86400), uint32(expireTime)).Return([]*model.AnchorCheckWhite{{
			Uid: uid,
		}}),
		mockdyconf.EXPECT().GetAnchorCheckImMsg().Return(imsg),
		mockApicenter.EXPECT().SendImMsg(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil),
	)

	type fields struct {
		cache             cache.IRedisCache
		store             model.IStore
		zegoClient        zego.IZegoClient
		apiClient         apicenter.IClient
		ISDyConfigHandler conf.ISDyConfigHandler
	}
	type args struct {
		ctx  context.Context
		tNow time.Time
	}
	tests := []struct {
		name   string
		fields fields
		args   args
	}{
		// Add test cases.
		{
			name: "TestAnchorCheckMgr_SendWillExpireMsg",
			fields: fields{
				cache:             mockCache,
				store:             mockStore,
				zegoClient:        mockZego,
				apiClient:         mockApicenter,
				ISDyConfigHandler: mockdyconf,
			},
			args: args{
				ctx:  context.Background(),
				tNow: tNow,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mgr := &AnchorCheckMgr{
				cache:             tt.fields.cache,
				store:             tt.fields.store,
				zegoClient:        tt.fields.zegoClient,
				apiClient:         tt.fields.apiClient,
				ISDyConfigHandler: tt.fields.ISDyConfigHandler,
			}
			mgr.SendWillExpireMsg(tt.args.ctx, tt.args.tNow)
		})
	}
}

*/

// go test -timeout 30s -run ^TestAnchorCheckMgr_SetCheckData2$ golang.52tt.com/services/anchor-check/manager -v -count=1
func TestAnchorCheckMgr_SetCheckData2(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockStore := mocks.NewMockIStore(ctl)
	anchorLevelClient := anchor_levelmocks.NewMockIClient(ctl)
	mockApicenter := apicentermocks.NewMockIClient(ctl)
	mockdyconf := mocks.NewMockISDyConfigHandler(ctl)
	msg := conf.AnchorCheckImMsg{}

	uid := uint32(123)
	taskId := "1"
	cyScore := uint32(9)
	sxScore := uint32(9)
	yzScore := uint32(9)
	kcScore := uint32(9)
	status := model.AnchorCheckStatusPass
	remark := "test"
	//anchorData := &model.AnchorCheck{Uid: uid}
	operator := "a"
	orgData := &model.AnchorCheck{
		Uid: uid,
	}

	info := &model.AnchorCheck{
		Uid:      uid,
		TaskId:   taskId,
		CyScore:  cyScore,
		SxScore:  sxScore,
		YzScore:  yzScore,
		KcScore:  kcScore,
		Remark:   remark,
		Status:   uint8(status),
		Operator: operator,
	}

	gomock.InOrder(
		mockStore.EXPECT().GetAnchorCheckData(taskId).Return(orgData),
		anchorLevelClient.EXPECT().SetAnchorCheckPass(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil),
		mockStore.EXPECT().UpdateScores(gomock.Any(), gomock.Any()).Return(nil),
		mockdyconf.EXPECT().GetAnchorCheckImMsg().Return(msg),
		mockApicenter.EXPECT().SendImMsg(context.Background(), uid, protocol.TT, gomock.Any(), true).Return(nil),
	)

	type fields struct {
		cache             cache.IRedisCache
		store             model.IStore
		zegoClient        zego.IZegoClient
		apiClient         apicenter.IClient
		anchorLevelClient anchor_level.IClient
		ISDyConfigHandler conf.ISDyConfigHandler
	}
	type args struct {
		ctx      context.Context
		taskId   string
		cyScore  uint32
		sxScore  uint32
		yzScore  uint32
		kcScore  uint32
		remark   string
		operator string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		// Add test cases.
		{
			name: "TestAnchorCheckMgr_SetCheckData",
			fields: fields{
				//	cache:             mockCache,
				store: mockStore,
				//zegoClient:        mockZego,
				apiClient:         mockApicenter,
				anchorLevelClient: anchorLevelClient,
				ISDyConfigHandler: mockdyconf,
			},
			args: args{
				ctx:      context.Background(),
				taskId:   taskId,
				cyScore:  cyScore,
				sxScore:  sxScore,
				yzScore:  yzScore,
				kcScore:  kcScore,
				remark:   remark,
				operator: operator,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mgr := &AnchorCheckMgr{
				cache:             tt.fields.cache,
				store:             tt.fields.store,
				zegoClient:        tt.fields.zegoClient,
				apiClient:         tt.fields.apiClient,
				anchorLevelClient: tt.fields.anchorLevelClient,
				ISDyConfigHandler: tt.fields.ISDyConfigHandler,
			}
			if err := mgr.SetCheckData(tt.args.ctx, info); (err != nil) != tt.wantErr {
				t.Errorf("SetCheckData() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}

}

// go test -timeout 30s -run ^TestAnchorCheckMgr_SetCheckData$ golang.52tt.com/services/anchor-check/manager -v -count=1
func TestAnchorCheckMgr_SetCheckData(t *testing.T) {

	return
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockCache := mocks.NewMockIRedisCache(ctl)
	mockStore := mocks.NewMockIStore(ctl)
	mockZego := mocks.NewMockIZegoClient(ctl)
	mockApicenter := apicentermocks.NewMockIClient(ctl)
	anchorLevelClient := anchor_levelmocks.NewMockIClient(ctl)

	uid := uint32(123)
	taskId := "1"
	cyScore := uint32(9)
	sxScore := uint32(9)
	yzScore := uint32(9)
	kcScore := uint32(9)
	status := model.AnchorCheckStatusPass
	remark := "test"
	//anchorData := &model.AnchorCheck{Uid: uid}
	operator := "a"

	orgData := &model.AnchorCheck{
		Uid: uid,
	}

	info := &model.AnchorCheck{
		CyScore:  cyScore,
		SxScore:  sxScore,
		YzScore:  yzScore,
		KcScore:  kcScore,
		Remark:   remark,
		Status:   uint8(status),
		Operator: operator,
	}
	gomock.InOrder(
		mockStore.EXPECT().GetAnchorCheckData(taskId).Return(orgData),
		anchorLevelClient.EXPECT().SetAnchorCheckPass(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil),
		mockStore.EXPECT().UpdateScores(gomock.Any(), gomock.Any()).Return(nil),
		//mockStore.EXPECT().GetAnchorCheckData(taskId).Return(anchorData),
		mockApicenter.EXPECT().SendImMsg(context.Background(), uid, protocol.TT, gomock.Any(), true).Return(nil),
	)

	type fields struct {
		cache             cache.IRedisCache
		store             model.IStore
		zegoClient        zego.IZegoClient
		apiClient         apicenter.IClient
		anchorLevelClient anchor_level.IClient
	}
	type args struct {
		ctx      context.Context
		taskId   string
		cyScore  uint32
		sxScore  uint32
		yzScore  uint32
		kcScore  uint32
		remark   string
		operator string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		// Add test cases.
		{
			name: "TestAnchorCheckMgr_SetCheckData",
			fields: fields{
				cache:             mockCache,
				store:             mockStore,
				zegoClient:        mockZego,
				apiClient:         mockApicenter,
				anchorLevelClient: anchorLevelClient,
			},
			args: args{
				ctx:      context.Background(),
				taskId:   taskId,
				cyScore:  cyScore,
				sxScore:  sxScore,
				yzScore:  yzScore,
				kcScore:  kcScore,
				remark:   remark,
				operator: operator,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mgr := &AnchorCheckMgr{
				cache:             tt.fields.cache,
				store:             tt.fields.store,
				zegoClient:        tt.fields.zegoClient,
				apiClient:         tt.fields.apiClient,
				anchorLevelClient: tt.fields.anchorLevelClient,
			}
			if err := mgr.SetCheckData(tt.args.ctx, info); (err != nil) != tt.wantErr {
				t.Errorf("SetCheckData() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}

}

func TestAnchorCheckMgr_SetWhite(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockCache := mocks.NewMockIRedisCache(ctl)
	mockStore := mocks.NewMockIStore(ctl)
	mockZego := mocks.NewMockIZegoClient(ctl)
	mockApicenter := apicentermocks.NewMockIClient(ctl)
	mockdyconf := mocks.NewMockISDyConfigHandler(ctl)
	imsg := conf.AnchorCheckImMsg{}

	uid := uint32(123)
	operator := "a"

	msg := new(apiPB.ImMsg)
	msg.ImType = &apiPB.ImType{
		SenderType:   uint32(apiPB.IM_SENDER_TYPE_IM_SENDER_NORMAL),
		ReceiverType: uint32(apiPB.IM_RECEIVER_TYPE_IM_RECEIVER_USER),
	}

	msg.FromUid = 10000 // TT语音助手

	msg.ToIdList = []uint32{uid}
	msg.ImContent = &apiPB.ImContent{}
	content := "目前官方需要考核您的直播等级，需要在即日起三天内开播，并上传直播音频以完成考核 立即开播>\n\n注：直播音频仅能上传1次，请谨慎点击确认按钮"
	hightlight := "立即开播>"
	url := "tt://m.52tt.com/home?main_pos=0&vice_pos=1"
	msg.ImContent.TextHlUrl = &apiPB.ImTextWithHighlightUrl{
		Content:    content,
		Hightlight: hightlight,
		Url:        url,
	}
	msg.ImType.ContentType = uint32(apiPB.IM_CONTENT_TYPE_IM_CONTENT_TEXT_WITH_HL_URL)
	msg.Platform = apiPB.Platform_UNSPECIFIED
	msg.AppPlatform = "all"

	gomock.InOrder(
		mockCache.EXPECT().DelAnchorCheckWhiteV2(uid).Return(nil),
		mockStore.EXPECT().InsertAnchorCheckWhite(uid, operator).Return(nil),
		mockCache.EXPECT().DelAnchorCheckWhiteV2(uid).Return(nil),
		mockdyconf.EXPECT().GetAnchorCheckImMsg().Return(imsg),
		mockApicenter.EXPECT().SendImMsg(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil),
	)

	type fields struct {
		cache      cache.IRedisCache
		store      model.IStore
		zegoClient zego.IZegoClient
		apiClient  apicenter.IClient
		conf.ISDyConfigHandler
	}
	type args struct {
		ctx      context.Context
		uid      uint32
		operator string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		// Add test cases.
		{
			name: "TestAnchorCheckMgr_SetWhite",
			fields: fields{
				cache:             mockCache,
				store:             mockStore,
				zegoClient:        mockZego,
				apiClient:         mockApicenter,
				ISDyConfigHandler: mockdyconf,
			},
			args: args{
				ctx:      context.Background(),
				uid:      uid,
				operator: operator,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mgr := &AnchorCheckMgr{
				cache:             tt.fields.cache,
				store:             tt.fields.store,
				zegoClient:        tt.fields.zegoClient,
				apiClient:         tt.fields.apiClient,
				ISDyConfigHandler: tt.fields.ISDyConfigHandler,
			}
			if err := mgr.SetWhite(tt.args.ctx, tt.args.uid, tt.args.operator); (err != nil) != tt.wantErr {
				t.Errorf("SetWhite() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

/*
func TestAnchorCheckMgr_ShutDown(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockCache := mocks.NewMockIRedisCache(ctl)
	mockStore := mocks.NewMockIStore(ctl)
	mockZego := mocks.NewMockIZegoClient(ctl)
	mockApicenter := apicentermocks.NewMockIClient(ctl)

	gomock.InOrder(
		mockStore.EXPECT().Close(),
		mockCache.EXPECT().Close(),
	)

	type fields struct {
		cache      cache.IRedisCache
		store      model.IStore
		zegoClient zego.IZegoClient
		apiClient  apicenter.IClient
	}
	tests := []struct {
		name   string
		fields fields
	}{
		// Add test cases.
		{
			name: "TestAnchorCheckMgr_ShutDown",
			fields: fields{
				cache:      mockCache,
				store:      mockStore,
				zegoClient: mockZego,
				apiClient:  mockApicenter,
			},
		},
	}
	for _, tt := range tests {
		ctx := context.Background()

		t.Run(tt.name, func(t *testing.T) {

			mgr := &AnchorCheckMgr{
				cache:      tt.fields.cache,
				store:      tt.fields.store,
				zegoClient: tt.fields.zegoClient,
				apiClient:  tt.fields.apiClient,
			}
			mgr.StartTimer(ctx)
			mgr.ShutDown()

		})
	}
}

*/

func TestAnchorCheckMgr_StartRecord(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockCache := mocks.NewMockIRedisCache(ctl)
	mockStore := mocks.NewMockIStore(ctl)
	mockZego := mocks.NewMockIZegoClient(ctl)
	mockApicenter := apicentermocks.NewMockIClient(ctl)

	ctx := context.Background()
	uid := uint32(123)
	roomId := "123"
	streamId := "abc"
	taskId := "a123"

	gomock.InOrder(
		mockZego.EXPECT().StartRecord(gomock.Any(), gomock.Any(), gomock.Any()).Return(taskId, nil),
		mockStore.EXPECT().InsertAnchorCheck(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil),
	)

	type fields struct {
		cache      cache.IRedisCache
		store      model.IStore
		zegoClient zego.IZegoClient
		apiClient  apicenter.IClient
	}
	type args struct {
		ctx      context.Context
		uid      uint32
		roomId   string
		streamId string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    string
		wantErr bool
	}{
		// Add test cases.
		{
			name: "TestAnchorCheckMgr_StartRecord",
			fields: fields{
				cache:      mockCache,
				store:      mockStore,
				zegoClient: mockZego,
				apiClient:  mockApicenter,
			},
			args: args{
				ctx:      ctx,
				uid:      uid,
				roomId:   roomId,
				streamId: streamId,
			},
			want:    taskId,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mgr := &AnchorCheckMgr{
				cache:      tt.fields.cache,
				store:      tt.fields.store,
				zegoClient: tt.fields.zegoClient,
				apiClient:  tt.fields.apiClient,
			}
			got, err := mgr.StartRecord(tt.args.ctx, tt.args.uid, tt.args.roomId, tt.args.streamId, 0)
			if (err != nil) != tt.wantErr {
				t.Errorf("StartRecord() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("StartRecord() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestAnchorCheckMgr_StopRecord(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockCache := mocks.NewMockIRedisCache(ctl)
	mockStore := mocks.NewMockIStore(ctl)
	mockZego := mocks.NewMockIZegoClient(ctl)
	mockApicenter := apicentermocks.NewMockIClient(ctl)

	ctx := context.Background()
	uid := uint32(123)
	taskId := "a123"
	anchorData := &model.AnchorCheck{
		Uid:    uid,
		Status: model.AnchorCheckStatusInit,
	}
	acStatus := model.AnchorCheckStatusSubmit

	gomock.InOrder(
		mockStore.EXPECT().GetAnchorCheckData(taskId).Return(anchorData),
		mockZego.EXPECT().StopRecord(ctx, taskId).Return(nil),
		mockStore.EXPECT().UpdateStatus(taskId, uint8(acStatus)).Return(nil),
	)

	type fields struct {
		cache      cache.IRedisCache
		store      model.IStore
		zegoClient zego.IZegoClient
		apiClient  apicenter.IClient
	}
	type args struct {
		ctx    context.Context
		uid    uint32
		taskId string
		submit bool
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		// Add test cases.
		{
			name: "TestAnchorCheckMgr_StopRecord",
			fields: fields{
				cache:      mockCache,
				store:      mockStore,
				zegoClient: mockZego,
				apiClient:  mockApicenter,
			},
			args: args{
				ctx:    ctx,
				uid:    uid,
				taskId: taskId,
				submit: true,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mgr := &AnchorCheckMgr{
				cache:      tt.fields.cache,
				store:      tt.fields.store,
				zegoClient: tt.fields.zegoClient,
				apiClient:  tt.fields.apiClient,
			}
			if err := mgr.StopRecord(tt.args.ctx, tt.args.uid, tt.args.taskId, tt.args.submit); (err != nil) != tt.wantErr {
				t.Errorf("StopRecord() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestAnchorCheckMgr_SubmitWhite(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockCache := mocks.NewMockIRedisCache(ctl)
	mockStore := mocks.NewMockIStore(ctl)
	mockZego := mocks.NewMockIZegoClient(ctl)
	mockApicenter := apicentermocks.NewMockIClient(ctl)

	uid := uint32(123)

	gomock.InOrder(
		mockStore.EXPECT().UpdateAllAnchorCheckWhiteStatus(uid, uint8(model.AnchorCheckWhiteStatusSubmit)).Return(nil),
		mockCache.EXPECT().DelAnchorCheckWhiteV2(uid).Return(nil),
	)

	type fields struct {
		cache      cache.IRedisCache
		store      model.IStore
		zegoClient zego.IZegoClient
		apiClient  apicenter.IClient
	}
	type args struct {
		ctx context.Context
		uid uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		// Add test cases.
		{
			name: "TestAnchorCheckMgr_SubmitWhite",
			fields: fields{
				cache:      mockCache,
				store:      mockStore,
				zegoClient: mockZego,
				apiClient:  mockApicenter,
			},
			args: args{
				ctx: context.Background(),
				uid: uid,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mgr := &AnchorCheckMgr{
				cache:      tt.fields.cache,
				store:      tt.fields.store,
				zegoClient: tt.fields.zegoClient,
				apiClient:  tt.fields.apiClient,
			}
			if err := mgr.SubmitWhite(tt.args.ctx, tt.args.uid); (err != nil) != tt.wantErr {
				t.Errorf("SubmitWhite() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestAnchorCheckMgr_SyncZegoMp3(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockCache := mocks.NewMockIRedisCache(ctl)
	mockStore := mocks.NewMockIStore(ctl)
	mockZego := mocks.NewMockIZegoClient(ctl)
	mockApicenter := apicentermocks.NewMockIClient(ctl)

	taskId := "123"
	list := []*model.AnchorCheck{
		{
			TaskId: taskId,
		},
	}

	beginTime := uint32(1662432320)
	endTime := uint32(1662450950)
	fileUrl := "http://..."

	desc := &zego.DescribeRecordStatus{}
	desc.Data.Status = zego.ZegoRecordOver
	desc.Data.RecordBeginTimestamp = int64(beginTime) * 1000
	desc.Data.RecordEndTimestamp = int64(endTime) * 1000
	desc.Data.RecordFiles = []zego.RecordFileData{
		{
			Status:  zego.ZegoUploadSuccess,
			FileUrl: fileUrl,
		},
	}

	gomock.InOrder(
		mockStore.EXPECT().GetAllSubmitNoFileUrlList(time.Now().Unix()).Return(list),
		mockZego.EXPECT().DescribeRecordStatus(taskId).Return(desc, nil),
		mockStore.EXPECT().UpdateFileUrl(taskId, fileUrl, beginTime, endTime).Return(nil),
	)

	type fields struct {
		cache      cache.IRedisCache
		store      model.IStore
		zegoClient zego.IZegoClient
		apiClient  apicenter.IClient
	}
	tests := []struct {
		name   string
		fields fields
	}{
		// Add test cases.
		{
			name: "TestAnchorCheckMgr_SyncZegoMp3",
			fields: fields{
				cache:      mockCache,
				store:      mockStore,
				zegoClient: mockZego,
				apiClient:  mockApicenter,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mgr := &AnchorCheckMgr{
				cache:      tt.fields.cache,
				store:      tt.fields.store,
				zegoClient: tt.fields.zegoClient,
				apiClient:  tt.fields.apiClient,
			}
			mgr.SyncZegoMp3()
		})
	}
}

func TestAnchorCheckMgr_UpdateAllRecordFiles(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockCache := mocks.NewMockIRedisCache(ctl)
	mockStore := mocks.NewMockIStore(ctl)
	mockZego := mocks.NewMockIZegoClient(ctl)
	mockApicenter := apicentermocks.NewMockIClient(ctl)

	taskId := "123"

	beginTime := uint32(1662432320)
	endTime := uint32(1662450950)
	fileUrl := "http://..."

	desc := &zego.DescribeRecordStatus{}
	desc.Data.Status = zego.ZegoRecordOver
	desc.Data.RecordBeginTimestamp = int64(beginTime) * 1000
	desc.Data.RecordEndTimestamp = int64(endTime) * 1000
	desc.Data.RecordFiles = []zego.RecordFileData{
		{
			Status:  zego.ZegoUploadSuccess,
			FileUrl: fileUrl,
		},
	}

	gomock.InOrder(
		mockStore.EXPECT().UpdateFileUrl(taskId, fileUrl, beginTime, endTime).Return(nil),
	)

	type fields struct {
		cache      cache.IRedisCache
		store      model.IStore
		zegoClient zego.IZegoClient
		apiClient  apicenter.IClient
	}
	type args struct {
		list      *[]zego.RecordFileData
		taskId    string
		beginTime uint32
		endTime   uint32
	}
	tests := []struct {
		name   string
		fields fields
		args   args
	}{
		// Add test cases.
		{
			name: "TestAnchorCheckMgr_UpdateAllRecordFiles",
			fields: fields{
				cache:      mockCache,
				store:      mockStore,
				zegoClient: mockZego,
				apiClient:  mockApicenter,
			},
			args: args{
				list:      &desc.Data.RecordFiles,
				taskId:    taskId,
				beginTime: beginTime,
				endTime:   endTime,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mgr := &AnchorCheckMgr{
				cache:      tt.fields.cache,
				store:      tt.fields.store,
				zegoClient: tt.fields.zegoClient,
				apiClient:  tt.fields.apiClient,
			}
			mgr.UpdateAllRecordFiles(tt.args.list, tt.args.taskId, tt.args.beginTime, tt.args.endTime)
		})
	}
}

func TestNewAnchorCheckMgr(t *testing.T) {
	return

	cfg, err := config.NewConfig("json", "../anchor-check.json")
	if err != nil {
		log.Errorln("Failed to init ServerConfig from file:")
		return
	}
	redisConfig := new(config.RedisConfig)
	redisConfig.Read(cfg, "redis")
	redisClient := redis.NewClient(&redis.Options{
		Addr:     redisConfig.Addr(),
		PoolSize: redisConfig.PoolSize,
	})
	NewAnchorCheckMgr(cfg, redisClient)
}

func TestNewAnchorCheckMgrx(t *testing.T) {
	type args struct {
		cfg config.Configer
	}
	tests := []struct {
		name    string
		args    args
		want    *AnchorCheckMgr
		wantErr bool
	}{
		// TODO: Add test cases.
	}

	for _, tt := range tests {
		redisConfig := new(config.RedisConfig)
		redisConfig.Read(tt.args.cfg, "redis")
		redisClient := redis.NewClient(&redis.Options{
			Addr:     redisConfig.Addr(),
			PoolSize: redisConfig.PoolSize,
		})
		t.Run(tt.name, func(t *testing.T) {
			got, err := NewAnchorCheckMgr(tt.args.cfg, redisClient)
			if (err != nil) != tt.wantErr {
				t.Errorf("NewAnchorCheckMgr() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("NewAnchorCheckMgr() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestAnchorCheckMgr_GetStore(t *testing.T) {
	type fields struct {
		cache             cache.IRedisCache
		store             model.IStore
		zegoClient        zego.IZegoClient
		apiClient         apicenter.IClient
		anchorLevelClient anchor_level.IClient
		ISDyConfigHandler conf.ISDyConfigHandler
	}
	tests := []struct {
		name   string
		fields fields
		want   model.IStore
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mgr := &AnchorCheckMgr{
				cache:             tt.fields.cache,
				store:             tt.fields.store,
				zegoClient:        tt.fields.zegoClient,
				apiClient:         tt.fields.apiClient,
				anchorLevelClient: tt.fields.anchorLevelClient,
				ISDyConfigHandler: tt.fields.ISDyConfigHandler,
			}
			if got := mgr.GetStore(); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("AnchorCheckMgr.GetStore() = %v, want %v", got, tt.want)
			}
		})
	}
}

// go test -timeout 30s -run ^TestAnchorCheckMgr_CheckInWhite$ golang.52tt.com/services/anchor-check/manager
func TestAnchorCheckMgr_CheckInWhite(t *testing.T) {

	ctl := gomock.NewController(t)
	defer ctl.Finish()
	mockstore := mocks.NewMockIStore(ctl)
	mockcache := mocks.NewMockIRedisCache(ctl)
	//createTime := uint32(0)
	info := &model.AnchorCheckWhite{}
	info2 := &pb.WhiteDetailInfo{}
	gomock.InOrder(
		mockcache.EXPECT().GetAnchorCheckWhiteV2(gomock.Any()).Return(info2, false, nil),
		mockstore.EXPECT().GetFirstAnchorCheckWhiteData(gomock.Any()).Return(info, nil),
		mockcache.EXPECT().SetAnchorCheckWhiteV2(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil),
		//mockstore.EXPECT().InsertAnchorCheckUpgrade(gomock.Any(), gomock.Any(), gomock.Any()).Return(createTime, nil),
	)

	type fields struct {
		cache             cache.IRedisCache
		store             model.IStore
		zegoClient        zego.IZegoClient
		apiClient         apicenter.IClient
		anchorLevelClient anchor_level.IClient
		ISDyConfigHandler conf.ISDyConfigHandler
	}
	type args struct {
		ctx context.Context
		uid uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.WhiteData
		wantErr bool
	}{
		{
			fields: fields{
				store: mockstore,
				cache: mockcache,
			},
			args: args{
				ctx: context.Background(),
				uid: 0,
			},
			want: &pb.WhiteData{},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mgr := &AnchorCheckMgr{
				cache:             tt.fields.cache,
				store:             tt.fields.store,
				zegoClient:        tt.fields.zegoClient,
				apiClient:         tt.fields.apiClient,
				anchorLevelClient: tt.fields.anchorLevelClient,
				ISDyConfigHandler: tt.fields.ISDyConfigHandler,
			}
			got, err := mgr.CheckInWhite(tt.args.ctx, tt.args.uid)
			if (err != nil) != tt.wantErr {
				t.Errorf("AnchorCheckMgr.CheckInWhite() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("AnchorCheckMgr.CheckInWhite() = %v, want %v", got, tt.want)
			}
		})
	}
}

// todo1
func TestAnchorCheckMgr_handleLevelE(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()
	mockApicenter := apicentermocks.NewMockIClient(ctl)
	mockdyconf := mocks.NewMockISDyConfigHandler(ctl)
	msg := conf.AnchorCheckImMsg{}
	gomock.InOrder(
		mockdyconf.EXPECT().GetAnchorCheckImMsg().Return(msg),
		mockApicenter.EXPECT().SendImMsg(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil),
		mockdyconf.EXPECT().GetAnchorCheckImMsg().Return(msg),
		mockApicenter.EXPECT().SendImMsg(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil),
		mockdyconf.EXPECT().GetAnchorCheckImMsg().Return(msg),
		mockApicenter.EXPECT().SendImMsg(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil),
		mockdyconf.EXPECT().GetAnchorCheckImMsg().Return(msg),
		mockApicenter.EXPECT().SendImMsg(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil),
		mockdyconf.EXPECT().GetAnchorCheckImMsg().Return(msg),
		mockApicenter.EXPECT().SendImMsg(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil),
		mockdyconf.EXPECT().GetAnchorCheckImMsg().Return(msg),
		mockApicenter.EXPECT().SendImMsg(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil),
	)

	orgData1 := &model.AnchorCheck{
		Status: 1,
	}
	orgData12 := &model.AnchorCheck{
		Status: 3,
	}
	info1 := &model.AnchorCheck{
		CyScore: 10,
		SxScore: 10,
		YzScore: 10,
		KcScore: 10,
	}

	info2 := &model.AnchorCheck{}
	info3 := &model.AnchorCheck{CyScore: 1}

	//orgData2 := &model.AnchorCheck{}
	//info2 := &model.AnchorCheck{}

	type fields struct {
		cache             cache.IRedisCache
		store             model.IStore
		zegoClient        zego.IZegoClient
		apiClient         apicenter.IClient
		anchorLevelClient anchor_level.IClient
		ISDyConfigHandler conf.ISDyConfigHandler
	}
	type args struct {
		orgData *model.AnchorCheck
		info    *model.AnchorCheck
	}
	tests := []struct {
		name   string
		fields fields
		args   args
	}{
		{
			fields: fields{apiClient: mockApicenter, ISDyConfigHandler: mockdyconf},
			args: args{
				orgData: orgData1,
				info:    info1,
			},
		},
		{
			fields: fields{apiClient: mockApicenter, ISDyConfigHandler: mockdyconf},
			args: args{
				orgData: orgData12,
				info:    info1,
			},
		},
		{
			fields: fields{apiClient: mockApicenter, ISDyConfigHandler: mockdyconf},
			args: args{
				orgData: orgData1,
				info:    info2,
			},
		},
		{
			fields: fields{apiClient: mockApicenter, ISDyConfigHandler: mockdyconf},
			args: args{
				orgData: orgData12,
				info:    info2,
			},
		},
		{
			fields: fields{apiClient: mockApicenter, ISDyConfigHandler: mockdyconf},
			args: args{
				orgData: orgData1,
				info:    info3,
			},
		},
		{
			fields: fields{apiClient: mockApicenter, ISDyConfigHandler: mockdyconf},
			args: args{
				orgData: orgData12,
				info:    info3,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mgr := &AnchorCheckMgr{
				cache:             tt.fields.cache,
				store:             tt.fields.store,
				zegoClient:        tt.fields.zegoClient,
				apiClient:         tt.fields.apiClient,
				anchorLevelClient: tt.fields.anchorLevelClient,
				ISDyConfigHandler: tt.fields.ISDyConfigHandler,
			}
			mgr.handleLevelE(tt.args.orgData, tt.args.info)
		})
	}
}

func TestAnchorCheckMgr_GetWhiteList(t *testing.T) {
	type fields struct {
		cache             cache.IRedisCache
		store             model.IStore
		zegoClient        zego.IZegoClient
		apiClient         apicenter.IClient
		anchorLevelClient anchor_level.IClient
		ISDyConfigHandler conf.ISDyConfigHandler
	}
	type args struct {
		ctx       context.Context
		offset    uint32
		limit     uint32
		uid       uint32
		beginTime uint32
		endTime   uint32
		status    int32
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   []model.AnchorCheckWhite
		want1  uint32
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mgr := &AnchorCheckMgr{
				cache:             tt.fields.cache,
				store:             tt.fields.store,
				zegoClient:        tt.fields.zegoClient,
				apiClient:         tt.fields.apiClient,
				anchorLevelClient: tt.fields.anchorLevelClient,
				ISDyConfigHandler: tt.fields.ISDyConfigHandler,
			}
			got, got1 := mgr.GetWhiteList(tt.args.ctx, tt.args.offset, tt.args.limit, tt.args.uid, tt.args.beginTime, tt.args.endTime, tt.args.status)
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("AnchorCheckMgr.GetWhiteList() got = %v, want %v", got, tt.want)
			}
			if got1 != tt.want1 {
				t.Errorf("AnchorCheckMgr.GetWhiteList() got1 = %v, want %v", got1, tt.want1)
			}
		})
	}
}

// go test -timeout 30s -run ^TestAnchorCheckMgr_GetAnchorCheckHistory$ golang.52tt.com/services/anchor-check/manager -v -count=1
func TestAnchorCheckMgr_GetAnchorCheckHistory(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()
	mockstore := mocks.NewMockIStore(ctl)
	list := []*model.AnchorCheck{
		{
			Uid: 1,
		},
	}
	resp := &pb.GetAnchorCheckHistoryResp{
		List: []*pb.AnchorCheckData{
			{
				Uid:   1,
				Level: "E",
			},
		},
	}
	gomock.InOrder(
		mockstore.EXPECT().GetAnchorCheckHistory(gomock.Any()).Return(list, nil),
	)

	type fields struct {
		cache             cache.IRedisCache
		store             model.IStore
		zegoClient        zego.IZegoClient
		apiClient         apicenter.IClient
		anchorLevelClient anchor_level.IClient
		ISDyConfigHandler conf.ISDyConfigHandler
	}
	type args struct {
		ctx context.Context
		in  *pb.GetAnchorCheckHistoryReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.GetAnchorCheckHistoryResp
		wantErr bool
	}{
		{
			fields: fields{
				store: mockstore,
			},
			args: args{ctx: context.Background(), in: &pb.GetAnchorCheckHistoryReq{}},
			want: resp,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &AnchorCheckMgr{
				cache:             tt.fields.cache,
				store:             tt.fields.store,
				zegoClient:        tt.fields.zegoClient,
				apiClient:         tt.fields.apiClient,
				anchorLevelClient: tt.fields.anchorLevelClient,
				ISDyConfigHandler: tt.fields.ISDyConfigHandler,
			}
			got, err := m.GetAnchorCheckHistory(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("AnchorCheckMgr.GetAnchorCheckHistory() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("AnchorCheckMgr.GetAnchorCheckHistory() = %v, want %v", got, tt.want)
			}
		})
	}
}

// go test -timeout 30s -run ^TestAnchorCheckMgr_GetLastAnchorCheckUpgrade$ golang.52tt.com/services/anchor-check/manager -v -count=1
func TestAnchorCheckMgr_GetLastAnchorCheckUpgrade(t *testing.T) {

	ctl := gomock.NewController(t)
	defer ctl.Finish()
	mockstore := mocks.NewMockIStore(ctl)
	createTime := uint32(0)
	gomock.InOrder(
		mockstore.EXPECT().GetLastAnchorCheckUpgrade(gomock.Any(), gomock.Any()).Return(createTime, nil),
	)

	type fields struct {
		cache             cache.IRedisCache
		store             model.IStore
		zegoClient        zego.IZegoClient
		apiClient         apicenter.IClient
		anchorLevelClient anchor_level.IClient
		ISDyConfigHandler conf.ISDyConfigHandler
	}
	type args struct {
		ctx context.Context
		in  *pb.UidReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.GetLastAnchorCheckUpgradeResp
		wantErr bool
	}{
		{
			fields: fields{store: mockstore},
			args:   args{ctx: context.Background(), in: &pb.UidReq{}},
			want:   &pb.GetLastAnchorCheckUpgradeResp{},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &AnchorCheckMgr{
				cache:             tt.fields.cache,
				store:             tt.fields.store,
				zegoClient:        tt.fields.zegoClient,
				apiClient:         tt.fields.apiClient,
				anchorLevelClient: tt.fields.anchorLevelClient,
				ISDyConfigHandler: tt.fields.ISDyConfigHandler,
			}
			got, err := m.GetLastAnchorCheckUpgrade(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("AnchorCheckMgr.GetLastAnchorCheckUpgrade() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("AnchorCheckMgr.GetLastAnchorCheckUpgrade() = %v, want %v", got, tt.want)
			}
		})
	}
}

// go test -timeout 30s -run ^TestAnchorCheckMgr_ApplyAnchorCheckUpgrade$ golang.52tt.com/services/anchor-check/manager -v -count=1
func TestAnchorCheckMgr_ApplyAnchorCheckUpgrade(t *testing.T) {

	ctl := gomock.NewController(t)
	defer ctl.Finish()
	mockstore := mocks.NewMockIStore(ctl)
	mockcache := mocks.NewMockIRedisCache(ctl)
	createTime := uint32(1)
	gomock.InOrder(
		mockstore.EXPECT().InsertAnchorCheckUpgrade(gomock.Any(), gomock.Any(), gomock.Any()).Return(createTime, nil),
		mockcache.EXPECT().DelAnchorCheckWhiteV2(gomock.Any()).Return(nil),
	)

	type fields struct {
		cache             cache.IRedisCache
		store             model.IStore
		zegoClient        zego.IZegoClient
		apiClient         apicenter.IClient
		anchorLevelClient anchor_level.IClient
		ISDyConfigHandler conf.ISDyConfigHandler
	}
	type args struct {
		ctx context.Context
		in  *pb.UidReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.ApplyAnchorCheckUpgradeReqResp
		wantErr bool
	}{
		{
			fields: fields{store: mockstore, cache: mockcache},
			args:   args{ctx: context.Background(), in: &pb.UidReq{}},
			want:   &pb.ApplyAnchorCheckUpgradeReqResp{RemainDay: 1},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &AnchorCheckMgr{
				cache:             tt.fields.cache,
				store:             tt.fields.store,
				zegoClient:        tt.fields.zegoClient,
				apiClient:         tt.fields.apiClient,
				anchorLevelClient: tt.fields.anchorLevelClient,
				ISDyConfigHandler: tt.fields.ISDyConfigHandler,
			}
			got, err := m.ApplyAnchorCheckUpgrade(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("AnchorCheckMgr.ApplyAnchorCheckUpgrade() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("AnchorCheckMgr.ApplyAnchorCheckUpgrade() = %v, want %v", got, tt.want)
			}
		})
	}
}
