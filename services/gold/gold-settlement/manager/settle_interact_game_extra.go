package manager

import (
	"context"
	"fmt"
	"time"

	"github.com/360EntSecGroup-Skylar/excelize"
	"golang.52tt.com/pkg/commission"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/settlement"
	"golang.52tt.com/pkg/settlement/export"
	pb "golang.52tt.com/protocol/services/gold-commission"
	Guild "golang.52tt.com/protocol/services/guildsvr"
	settlementBill "golang.52tt.com/protocol/services/settlement-bill"
	settlement_bill "golang.52tt.com/protocol/services/settlement-bill"
	"golang.52tt.com/services/gold/common"
	"golang.52tt.com/services/gold/common/model"
	"golang.52tt.com/services/gold/gold-settlement/mysql"
)

type SettleInteractGameExtra struct {
	ctx      context.Context
	billType settlementBill.SettlementBillType
	goldType pb.GoldType
	mgr      IManager
	store    mysql.IGoldCommissionMysql
	abort    bool
	guildIds []uint32 // 测试环境指定guildid结算
}

func (s *SettleInteractGameExtra) GetBillType() settlementBill.SettlementBillType { return s.billType }
func (s *SettleInteractGameExtra) GetGoldType() pb.GoldType                       { return s.goldType }
func (s *SettleInteractGameExtra) GetContext() context.Context                    { return s.ctx }
func (s *SettleInteractGameExtra) WasAbort() bool                                 { return s.abort }
func (s *SettleInteractGameExtra) Abort()                                         { s.abort = true }

func NewSettleInteractGameExtra(ctx context.Context, mgr IManager, guildIds []uint32) *SettleInteractGameExtra {
	billType := settlementBill.SettlementBillType_InteractGameExtraCommission
	goldType := pb.GoldType_INTERACT_GAME_GOLD
	store, err := mgr.GetStore(goldType)
	if err != nil {
		log.ErrorWithCtx(ctx, "NewSettleInteractGameExtra %s GetStore err:%s", billType, err)
	}
	log.Infof("NewSettleInteractGameExtra billType=%d goldType=%d specific guildIds=%v", billType, goldType, guildIds)
	return &SettleInteractGameExtra{
		ctx:      ctx,
		billType: billType,
		goldType: goldType,
		mgr:      mgr,
		store:    store,
		guildIds: guildIds,
	}
}

func (s *SettleInteractGameExtra) GetStore() mysql.IGoldCommissionMysql {
	return s.store
}

func (s *SettleInteractGameExtra) Aggregate() error {
	ctx := s.ctx
	billType := s.GetBillType()
	settleStatus := pb.ReqSettleStatus_ReqSettleStatusWait
	nowTime := time.Now()

	// 结算时间区间
	start, end := s.mgr.GetSettleMonthRange(nowTime)

	// 获取公会列表
	guildIds, err := s.store.GetDistinctGuildIds(ctx, start, end, settleStatus)
	if err != nil {
		log.ErrorWithCtx(ctx, "Aggregate %s GetDistinctGuildIds err:%s", billType, err)
		return err
	}

	log.Infof("Aggregate %s settlement guild ids: %+v start %s, end %s", billType, guildIds, start, end)

	if len(guildIds) == 0 {
		s.Abort()
		return nil
	}

	if len(s.guildIds) > 0 {
		guildIds = s.guildIds
		log.Infof("Aggregate %s settlement specific guild ids: %+v start %s, end %s", billType, guildIds, start, end)
	}

	desc := fmt.Sprintf("汇总于:%s 前的互动游戏会长额外奖励", end.Format("2006-01-02 00:00:00"))

	for _, guildId := range guildIds {

		if s.mgr.IsSkipGuild(guildId) {
			log.Infof("Settle %s skip guild id: %+v", billType, guildId)
			continue
		}

		settleInfo := &model.InteractGameExtraSettleInfo{
			CommonSettleInfo: model.CommonSettleInfo{
				BillType: billType,
				Now:      nowTime,
				Start:    start,
				End:      end,
				GuildId:  guildId,
			},
		}

		handle := func() (err error) {
			// 填充公会信息
			{
				var guildInfo *Guild.GuildResp
				var guildDismiss bool
				guildInfo, guildDismiss, err = s.mgr.GetGuildInfo(ctx, guildId)
				if err != nil {
					log.ErrorWithCtx(ctx, "Aggregate %s GetGuildInfo err:%s", billType, billType, err)
					return
				}
				// 公会不存在，不重试
				if guildDismiss {
					log.WarnWithCtx(ctx, "Aggregate %s guildDismiss, guild_id:%d", billType, guildId)
					settleInfo.NotSettle = true
					settleInfo.NotSettleReason = "公会已解散"

					return
				}
				settleInfo.GuildUid = guildInfo.GetOwner()
				settleInfo.GuildName = guildInfo.GetName()
				settleInfo.GuildDismiss = guildDismiss
			}

			_, income, err := s.GetExtraIncome(ctx, settleInfo)
			if err != nil {
				log.ErrorWithCtx(ctx, "Aggregate %s GetFeeIncome err:%s", billType, err)
				return
			}

			if income == 0 {
				log.ErrorWithCtx(ctx, "Aggregate %s income = 0, guild_id = %d", billType, guildId)
				return
			}

			// 处理对公
			isCorporate, settleUid, err := s.mgr.HandleCorporate(ctx, &settleInfo.CommonSettleInfo)
			if err != nil {
				log.ErrorWithCtx(ctx, "Aggregate %s HandleCorporate err:%s", billType, err)
				return
			}

			dateKey := s.mgr.GenDayKey(nowTime, guildId)
			settleInfo.IsCorporate = isCorporate
			settleInfo.SettleUid = settleUid
			settleInfo.DateKey = dateKey
			settleInfo.Remark = desc

			// 结算结果入库
			if err = s.store.AggregationInteractGameExtraIncome(ctx, settleInfo); err != nil {
				log.ErrorWithCtx(ctx, "Aggregate %s Settlement err:%s, settleUid:%d", billType, err, settleUid)
				// 如果汇总记录已存在，不重试
				if model.IsDuplicate(err) {
					return nil
				}
				return err
			}
			return
		}

		err = common.Retry(10, time.Second*1, handle)
		if err != nil {
			log.ErrorWithCtx(ctx, "Aggregate %s retry err:%s", billType, err)
			settleInfo.Err = err.Error()
		}
	}
	return nil
}

func (s *SettleInteractGameExtra) Settle() error {
	ctx := s.ctx
	billType := settlement_bill.SettlementBillType_InteractGameExtraCommission
	settleStatus := pb.ReqSettleStatus_ReqSettleStatusWait
	nowTime := time.Now()
	// 结算时间区间
	start, end := s.mgr.GetSettleMonthRange(nowTime)

	ResetSettledUidList()
	defer ResetSettledUidList()

	// 取汇总结果
	guildSettleInfos, err := s.store.GetInteractGameAggExtraIncome(ctx, start, settleStatus)
	if err != nil {
		log.ErrorWithCtx(ctx, "Settle %s GetInteractGameAggExtraIncome err:%s", s.GetBillType(), err)
		return err
	}

	defer s.mgr.GetAlert().SendInfo(fmt.Sprintf("结算完成 互动游戏额外奖励 共%d条", len(guildSettleInfos)))

	if len(guildSettleInfos) == 0 {
		s.Abort()
		return nil
	}

	for _, gi := range guildSettleInfos {
		if gi.NotSettle {
			continue
		}

		if len(s.guildIds) > 0 {
			var exist = false
			for _, gid := range s.guildIds {
				if gid == gi.GuildID {
					exist = true
					break
				}
			}
			if !exist {
				log.Debugf("Settle not settle guildId=%d", gi.GuildID)
				continue
			}
		}

		settleInfo := &model.InteractGameExtraSettleInfo{
			CommonSettleInfo: model.CommonSettleInfo{
				BillType:  billType,
				Now:       nowTime,
				Start:     start,
				End:       end,
				GuildId:   gi.GuildID,
				SettleUid: gi.GuildOwner,
				DateKey:   gi.DateKey,
			},
		}

		// important!
		settleInfo.Fee = gi.InteractGameFee
		settleInfo.Income = gi.InteractGameExtraIncome

		err = common.Retry(10, time.Second*1, func() error {
			return s.SettleHandle(settleInfo)
		})
		if err != nil {
			log.ErrorWithCtx(ctx, "Settle %s retry err:%s", s.GetBillType(), err)
		}
	}

	return nil
}

func (s *SettleInteractGameExtra) SettleHandle(gi *model.InteractGameExtraSettleInfo) error {
	var err error
	ctx := s.ctx
	billType := s.GetBillType()
	// 创建结算单
	gi.BillId, err = s.mgr.CreateBill(ctx, billType, gi.SettleUid, gi.Start, gi.End)
	if err != nil {
		log.ErrorWithCtx(ctx, "Settle %s CreateBill err:%s, settleUid:%d", billType, err, gi.SettleUid)
		return err
	}

	// 结算事务
	var comResp *commission.SettlementDataResponse
	if err = s.store.InteractGameExtraIncomeSettlement(ctx, gi, func() error {
		cli := s.mgr.GetCommissionClient(billType)
		comResp, err = cli.Settlement(ctx, gi.SettleUid, gi.Income, gi.DateKey, gi.Remark)
		if err != nil {
			log.ErrorWithCtx(ctx, "Settle %s Settlement err:%s, settleUid:%d, income:%d", billType, err, gi.SettleUid, gi.Income)
			if !IsSettleFatal(err) {
				return nil
			}
			time.Sleep(100 * time.Millisecond)
			return err
		}
		return nil
	}); err != nil {
		log.ErrorWithCtx(ctx, "Settle %s Settlement err:%s, settleUid:%d", billType, err, gi.SettleUid)
		return err
	}

	// 记录货币服务器时间与订单ID
	_ = s.store.SetExtraSettleReconcileInfo(ctx, gi.GuildId, gi.Start, comResp)

	return nil
}

func (s *SettleInteractGameExtra) Report() error {
	if true {
		return s.ReportV2()
	}

	ctx := s.ctx
	billType := s.GetBillType()
	settleStatus := pb.ReqSettleStatus_ReqSettleStatusFinished
	nowTime := time.Now()
	start, _ := s.mgr.GetSettleMonthRange(nowTime)
	tableRows := make([][]interface{}, 0)

	// 取汇总结果
	guildSettleInfos, err := s.store.GetInteractGameAggExtraIncome(ctx, start, settleStatus)
	if err != nil {
		log.ErrorWithCtx(ctx, "Report %s GetInteractGameAggIncome err:%s", billType, err)
		return err
	}

	for _, gi := range guildSettleInfos {
		if gi.Income == 0 {
			continue
		}

		var errInfo string
		if gi.NotSettle {
			errInfo = gi.NotSettleReason
		}

		guildInfo, _, err := s.mgr.GetGuildInfo(ctx, gi.GuildID)
		if err != nil {
			log.ErrorWithCtx(ctx, "Report %s GetGuildInfo err:%s, guildId:%d", billType, err, gi.GuildID)
		}

		// 公会ID，会长UID，工会名称，互动游戏流水，总流水，佣金收益元
		printLine := []interface{}{
			gi.GuildID,
			gi.GuildOwner,
			gi.InteractGameFee,
			gi.Fee,
			settlement.CentToMoney(gi.InteractGameExtraIncome),
			guildInfo.GetName(),
			errInfo,
		}

		fmt.Printf("Report %s settlement info: %+v \n", billType, printLine)

		tableRows = append(tableRows, printLine)
	}

	if len(tableRows) == 0 {
		return nil
	}

	tpName := settlement.BillTypeLabel.Text(billType)
	title := tpName + "-" + nowTime.Format("2006-01-02")
	e := export.CreateReportTableHeader(title, []string{"公会ID", "会长UID", "互动游戏流水（T豆）", "总流水（T豆）", "额外奖励（元）", "公会名称"})
	e.PushRowsV2(tableRows)
	f := fmt.Sprintf("%s.xlsx", title)
	e.Save(f)
	s.mgr.SendEmail(ctx, s.mgr.CreateEmailTitle(tpName, nowTime), "见附件", []string{f})
	return nil
}

func (s *SettleInteractGameExtra) ReportV2() error {
	ctx := s.ctx
	billType := s.GetBillType()
	settleStatus := pb.ReqSettleStatus_ReqSettleStatusFinished
	nowTime := time.Now()
	start, end := s.mgr.GetSettleMonthRange(nowTime)
	lastStart, lastEnd := s.mgr.GetSettleMonthLastRange(start)

	// 取汇总结果
	guildSettleInfos, err := s.store.GetInteractGameAggExtraIncome(ctx, start, settleStatus)
	if err != nil {
		log.ErrorWithCtx(ctx, "Report %s GetInteractGameAggIncome err:%s", billType, err)
		return err
	}

	// Create new Excel file
	f := excelize.NewFile()
	sheetName := "互动游戏额外奖励"
	f.SetSheetName("Sheet1", sheetName)

	// 本周期
	showEnd := end
	if end.Day() == 1 { // 如果结算结束日期是1号，往前一天
		showEnd = end.AddDate(0, 0, -1)
	}
	thisRangeText := fmt.Sprintf("%s-%d", start.Format("2006_01_02"), showEnd.Day())

	// 上周期
	showLastEnd := lastEnd
	if lastEnd.Day() == 1 { // 如果结算结束日期是1号，往前一天
		showLastEnd = lastEnd.AddDate(0, 0, -1)
	}
	lastRangeText := fmt.Sprintf("%s-%d", lastStart.Format("2006_01_02"), showLastEnd.Day())

	// Set title in first row and merge cells
	tpName := settlement.BillTypeLabel.Text(billType)
	title := fmt.Sprintf("%s-%s", tpName, nowTime.Format("2006-01-02"))
	lastCol := fmt.Sprintf("%c", 'A'+17) // 18 columns total (0-17)
	f.MergeCell(sheetName, "A1", lastCol+"1")
	f.SetCellValue(sheetName, "A1", title)

	// Set column headers
	headers := []string{
		"总会长UID", "总公会ID", thisRangeText + "总流水（元）", lastRangeText + "总流水（元）", "总流水增长率",
		"公会ID", "公会靓号ID", "公会名称", "听听达人UID",
		"房间号", "房间ID", "房间名", thisRangeText + "房间流水（元）",
		lastRangeText + "房间流水（元）", "房间流水增长率", "结算比例", "房间结算金额（元）", "总结算金额（元）",
	}

	for i, header := range headers {
		cell := fmt.Sprintf("%c2", 'A'+i)
		f.SetCellValue(sheetName, cell, header)
	}

	// Set column widths
	for i := 0; i < len(headers); i++ {
		col := fmt.Sprintf("%c", 'A'+i)
		f.SetColWidth(sheetName, col, col, 14)
	}

	currentRow := 3
	for _, gi := range guildSettleInfos {
		if gi.Income == 0 {
			continue
		}

		guildInfo, _, err := s.mgr.GetGuildInfo(ctx, gi.GuildID)
		if err != nil {
			log.ErrorWithCtx(ctx, "Report %s GetGuildInfo err:%s, guildId:%d", billType, err, gi.GuildID)
			continue
		}

		channelIncomes, err := s.store.GetInteractGameExtraChannelIncome(ctx, start, gi.GuildID)
		if err != nil {
			log.ErrorWithCtx(ctx, "Report %s GetInteractGameExtraChannelIncome err:%s, guildId:%d", billType, err, gi.GuildID)
			continue
		}

		cidList := make([]uint32, 0)
		for _, ci := range channelIncomes {
			cidList = append(cidList, ci.ChannelId)
		}
		channelMap, err := s.mgr.BatGetChannelInfo(ctx, cidList)
		if err != nil {
			log.ErrorWithCtx(ctx, "Report %s BatGetChannelInfo err:%s, guildId:%d", billType, err, gi.GuildID)
			continue
		}

		// Calculate total guild info
		totalFee := uint64(0)
		lastTotalFee := uint64(0)
		for _, ci := range channelIncomes {
			totalFee += ci.ChannelFee
			lastTotalFee += ci.LastChannelFee
		}
		totalGrowthRate := float64(0)
		if lastTotalFee > 0 {
			totalGrowthRate = float64(totalFee-lastTotalFee) / float64(lastTotalFee)
		}

		// 短号
		guildShowId := guildInfo.GetGuildId()
		if guildInfo.GetShortId() != 0 {
			guildShowId = guildInfo.GetShortId()
		}

		// Write channel rows
		startRow := currentRow
		for _, ci := range channelIncomes {
			// Write channel specific data
			rowData := []interface{}{
				gi.GuildOwner,                                      // 总会长UID
				gi.GuildID,                                         // 总公会ID
				settlement.CentToMoney(totalFee),                   // 本周期总流水（元）
				settlement.CentToMoney(lastTotalFee),               // 上周期总流水（元）
				fmt.Sprintf("%.2f%%", totalGrowthRate*100),         // 总流水增长率
				gi.GuildID,                                         // 公会ID
				guildShowId,                                        // 公会靓号ID
				guildInfo.GetName(),                                // 公会名称
				ci.AnchorId,                                        // 听听达人uid
				channelMap[ci.ChannelId].GetChannelViewId(),        // 房间号
				ci.ChannelId,                                       // 房间ID
				channelMap[ci.ChannelId].GetName(),                 // 房间名
				settlement.CentToMoney(ci.ChannelFee),              // 本周期房间流水（元）
				settlement.CentToMoney(ci.LastChannelFee),          // 上周期房间流水（元）
				fmt.Sprintf("%.2f%%", ci.GrowthRate*100),           // 房间流水增长率
				fmt.Sprintf("%.2f%%", ci.Rate*100),                 // 结算比例
				settlement.CentToMoney(ci.ExtraIncome),             // 房间结算金额（元）
				settlement.CentToMoney(gi.InteractGameExtraIncome), // 总结算金额（元）
			}

			for i, value := range rowData {
				cell := fmt.Sprintf("%c%d", 'A'+i, currentRow)
				f.SetCellValue(sheetName, cell, value)
			}
			currentRow++
		}

		// Merge cells for guild info
		if currentRow > startRow+1 {
			f.MergeCell(sheetName, fmt.Sprintf("A%d", startRow), fmt.Sprintf("A%d", currentRow-1)) // 总会长UID
			f.MergeCell(sheetName, fmt.Sprintf("B%d", startRow), fmt.Sprintf("B%d", currentRow-1)) // 总公会ID
			f.MergeCell(sheetName, fmt.Sprintf("C%d", startRow), fmt.Sprintf("C%d", currentRow-1)) // 本周期总流水（元）
			f.MergeCell(sheetName, fmt.Sprintf("D%d", startRow), fmt.Sprintf("D%d", currentRow-1)) // 上周期总流水（元）
			f.MergeCell(sheetName, fmt.Sprintf("E%d", startRow), fmt.Sprintf("E%d", currentRow-1)) // 总流水增长率
			f.MergeCell(sheetName, fmt.Sprintf("F%d", startRow), fmt.Sprintf("F%d", currentRow-1)) // 公会ID
			f.MergeCell(sheetName, fmt.Sprintf("G%d", startRow), fmt.Sprintf("G%d", currentRow-1)) // 公会靓号ID
			f.MergeCell(sheetName, fmt.Sprintf("H%d", startRow), fmt.Sprintf("H%d", currentRow-1)) // 公会名称
			f.MergeCell(sheetName, fmt.Sprintf("R%d", startRow), fmt.Sprintf("R%d", currentRow-1)) // 总结算金额（元）

			// 文字置顶
			style, _ := f.NewStyle(`{"alignment":{"vertical":"top"}}`)
			f.SetCellStyle(sheetName, fmt.Sprintf("A%d", startRow), fmt.Sprintf("H%d", currentRow-1), style)
			f.SetCellStyle(sheetName, fmt.Sprintf("R%d", startRow), fmt.Sprintf("R%d", currentRow-1), style)
		}
	}

	// Save the file
	filename := fmt.Sprintf("%s.xlsx", title)
	if err := f.SaveAs(filename); err != nil {
		log.ErrorWithCtx(ctx, "Report %s SaveAs err:%s", billType, err)
		return err
	}

	// Send email
	s.mgr.SendEmail(ctx, s.mgr.CreateEmailTitle(tpName, nowTime), "见附件", []string{filename})

	return nil
}

func (s *SettleInteractGameExtra) GetExtraIncome(ctx context.Context, settleInfo *model.InteractGameExtraSettleInfo) (
	fee, income uint64, err error) {
	guildId, start, end := settleInfo.GuildId, settleInfo.Start, settleInfo.End
	cidFeeList, err := s.store.GetInteractGameChannelFee(ctx, guildId, start, end, pb.SourceType_SourceTypeInteractGame)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetExtraIncome %s GetGuildAnchorFee err:%s", s.GetBillType(), err)
		return
	}

	lastStart, lastEnd := s.mgr.GetSettleMonthLastRange(start)
	lastCidFeeList, err := s.store.GetInteractGameChannelFee(ctx, guildId, lastStart, lastEnd, pb.SourceType_SourceTypeInteractGame)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetExtraIncome %s GetGuildAnchorFee err:%s", s.GetBillType(), err)
		return
	}

	lastFee := uint64(0)
	lastCidFeeMap := make(map[uint32]uint64)
	for _, item := range lastCidFeeList {
		lastCidFeeMap[item.ChannelId] = item.ChannelFee
		lastFee += item.ChannelFee
	}

	log.InfoWithCtx(ctx, "GetExtraIncome %s guildId=%d lastStart=%s lastEnd=%s lastFee=%d", s.GetBillType(), guildId, lastStart, lastEnd, lastFee)

	for _, item := range cidFeeList {
		extraIncome, rate := getInteractGameExtraIncome(item.ChannelFee)
		fee += item.ChannelFee
		income += extraIncome

		lastChannelFee := lastCidFeeMap[item.ChannelId] // 上个周期房间流水
		var growthRate float64
		if lastChannelFee > 0 {
			growthRate = float64(item.ChannelFee-lastChannelFee) / float64(lastChannelFee) // 房间流水增长率
		}

		log.InfoWithCtx(ctx, "GetExtraIncome %s guildId=%d cid=%d uid=%d channelFee=%d extraIncome=%d rate=%f lastChannelFee=%d growthRate=%f", s.GetBillType(), guildId, item.ChannelId, item.AnchorId, item.ChannelFee, extraIncome, rate, lastChannelFee, growthRate)

		// 记录房间的汇总结果
		err = s.store.SetInteractGameExtraChannelIncome(ctx, start, guildId, item.ChannelId, item.AnchorId, item.ChannelFee, lastChannelFee, extraIncome, rate, growthRate)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetExtraIncome %s SetInteractGameExtraChannelIncome err:%s", s.GetBillType(), err)
			return
		}
	}

	settleInfo.LastFee = lastFee
	if lastFee > 0 {
		settleInfo.GrowthRate = float64(fee-lastFee) / float64(lastFee)
		log.DebugWithCtx(ctx, "GetExtraIncome %s guildId=%d lastStart=%s lastEnd=%s lastFee=%d fee=%d growthRate=%f", s.GetBillType(), guildId, lastStart, lastEnd, lastFee, fee, settleInfo.GrowthRate)
	}

	log.InfoWithCtx(ctx, "GetExtraIncome %s guildId=%d lastStart=%s lastEnd=%s lastFee=%d fee=%d income=%d", s.GetBillType(), guildId, lastStart, lastEnd, lastFee, fee, income)

	settleInfo.Fee = fee
	settleInfo.Income = income
	return fee, income, nil
}

// 额外奖励
func getInteractGameExtraIncome(fee uint64) (income uint64, rate float64) {
	w := uint64(10000 * 100) // w rmb
	if fee < 15*w {
		return
	}
	if fee >= 15*w && fee < 30*w {
		income = uint64(float64(fee) * 0.02)
		rate = 0.02
	} else if fee >= 30*w && fee < 50*w {
		income = uint64(float64(fee) * 0.03)
		rate = 0.03
	} else if fee >= 50*w && fee < 80*w {
		income = uint64(float64(fee) * 0.04)
		rate = 0.04
	} else {
		income = uint64(float64(fee) * 0.05)
		rate = 0.05
	}
	return income, rate
}
