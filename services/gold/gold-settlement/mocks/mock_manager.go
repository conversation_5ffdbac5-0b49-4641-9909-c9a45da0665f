// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/services/gold/gold-settlement/manager (interfaces: IManager)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"
	time "time"

	gomock "github.com/golang/mock/gomock"
	commission "golang.52tt.com/pkg/commission"
	protocol "golang.52tt.com/pkg/protocol"
	alert "golang.52tt.com/pkg/settlement/alert"
	Channel "golang.52tt.com/protocol/services/channelsvr"
	gold_commission "golang.52tt.com/protocol/services/gold-commission"
	Guild "golang.52tt.com/protocol/services/guildsvr"
	reconcile_v2 "golang.52tt.com/protocol/services/reconcile-v2"
	settlement_bill "golang.52tt.com/protocol/services/settlement-bill"
	model "golang.52tt.com/services/gold/common/model"
	cache "golang.52tt.com/services/gold/gold-settlement/cache"
	mysql "golang.52tt.com/services/gold/gold-settlement/mysql"
)

// MockIManager is a mock of IManager interface.
type MockIManager struct {
	ctrl     *gomock.Controller
	recorder *MockIManagerMockRecorder
}

// MockIManagerMockRecorder is the mock recorder for MockIManager.
type MockIManagerMockRecorder struct {
	mock *MockIManager
}

// NewMockIManager creates a new mock instance.
func NewMockIManager(ctrl *gomock.Controller) *MockIManager {
	mock := &MockIManager{ctrl: ctrl}
	mock.recorder = &MockIManagerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIManager) EXPECT() *MockIManagerMockRecorder {
	return m.recorder
}

// BatGetChannelInfo mocks base method.
func (m *MockIManager) BatGetChannelInfo(arg0 context.Context, arg1 []uint32) (map[uint32]*Channel.ChannelSimpleInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatGetChannelInfo", arg0, arg1)
	ret0, _ := ret[0].(map[uint32]*Channel.ChannelSimpleInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatGetChannelInfo indicates an expected call of BatGetChannelInfo.
func (mr *MockIManagerMockRecorder) BatGetChannelInfo(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatGetChannelInfo", reflect.TypeOf((*MockIManager)(nil).BatGetChannelInfo), arg0, arg1)
}

// CreateBill mocks base method.
func (m *MockIManager) CreateBill(arg0 context.Context, arg1 settlement_bill.SettlementBillType, arg2 uint32, arg3, arg4 time.Time) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateBill", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateBill indicates an expected call of CreateBill.
func (mr *MockIManagerMockRecorder) CreateBill(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateBill", reflect.TypeOf((*MockIManager)(nil).CreateBill), arg0, arg1, arg2, arg3, arg4)
}

// CreateEmailTitle mocks base method.
func (m *MockIManager) CreateEmailTitle(arg0 string, arg1 time.Time) string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateEmailTitle", arg0, arg1)
	ret0, _ := ret[0].(string)
	return ret0
}

// CreateEmailTitle indicates an expected call of CreateEmailTitle.
func (mr *MockIManagerMockRecorder) CreateEmailTitle(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateEmailTitle", reflect.TypeOf((*MockIManager)(nil).CreateEmailTitle), arg0, arg1)
}

// GenDayKey mocks base method.
func (m *MockIManager) GenDayKey(arg0 time.Time, arg1 uint32) string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GenDayKey", arg0, arg1)
	ret0, _ := ret[0].(string)
	return ret0
}

// GenDayKey indicates an expected call of GenDayKey.
func (mr *MockIManagerMockRecorder) GenDayKey(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GenDayKey", reflect.TypeOf((*MockIManager)(nil).GenDayKey), arg0, arg1)
}

// GetAlert mocks base method.
func (m *MockIManager) GetAlert() alert.IFeishu {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAlert")
	ret0, _ := ret[0].(alert.IFeishu)
	return ret0
}

// GetAlert indicates an expected call of GetAlert.
func (mr *MockIManagerMockRecorder) GetAlert() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAlert", reflect.TypeOf((*MockIManager)(nil).GetAlert))
}

// GetAmuseExtraSettleOrderCount mocks base method.
func (m *MockIManager) GetAmuseExtraSettleOrderCount(arg0 context.Context, arg1 *reconcile_v2.TimeRangeReq) (*reconcile_v2.CountResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAmuseExtraSettleOrderCount", arg0, arg1)
	ret0, _ := ret[0].(*reconcile_v2.CountResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAmuseExtraSettleOrderCount indicates an expected call of GetAmuseExtraSettleOrderCount.
func (mr *MockIManagerMockRecorder) GetAmuseExtraSettleOrderCount(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAmuseExtraSettleOrderCount", reflect.TypeOf((*MockIManager)(nil).GetAmuseExtraSettleOrderCount), arg0, arg1)
}

// GetAmuseExtraSettleOrderList mocks base method.
func (m *MockIManager) GetAmuseExtraSettleOrderList(arg0 context.Context, arg1 *reconcile_v2.TimeRangeReq) (*reconcile_v2.OrderIdsResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAmuseExtraSettleOrderList", arg0, arg1)
	ret0, _ := ret[0].(*reconcile_v2.OrderIdsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAmuseExtraSettleOrderList indicates an expected call of GetAmuseExtraSettleOrderList.
func (mr *MockIManagerMockRecorder) GetAmuseExtraSettleOrderList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAmuseExtraSettleOrderList", reflect.TypeOf((*MockIManager)(nil).GetAmuseExtraSettleOrderList), arg0, arg1)
}

// GetAmuseGoldSettleOrderCount mocks base method.
func (m *MockIManager) GetAmuseGoldSettleOrderCount(arg0 context.Context, arg1 *reconcile_v2.TimeRangeReq) (*reconcile_v2.CountResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAmuseGoldSettleOrderCount", arg0, arg1)
	ret0, _ := ret[0].(*reconcile_v2.CountResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAmuseGoldSettleOrderCount indicates an expected call of GetAmuseGoldSettleOrderCount.
func (mr *MockIManagerMockRecorder) GetAmuseGoldSettleOrderCount(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAmuseGoldSettleOrderCount", reflect.TypeOf((*MockIManager)(nil).GetAmuseGoldSettleOrderCount), arg0, arg1)
}

// GetAmuseGoldSettleOrderList mocks base method.
func (m *MockIManager) GetAmuseGoldSettleOrderList(arg0 context.Context, arg1 *reconcile_v2.TimeRangeReq) (*reconcile_v2.OrderIdsResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAmuseGoldSettleOrderList", arg0, arg1)
	ret0, _ := ret[0].(*reconcile_v2.OrderIdsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAmuseGoldSettleOrderList indicates an expected call of GetAmuseGoldSettleOrderList.
func (mr *MockIManagerMockRecorder) GetAmuseGoldSettleOrderList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAmuseGoldSettleOrderList", reflect.TypeOf((*MockIManager)(nil).GetAmuseGoldSettleOrderList), arg0, arg1)
}

// GetCache mocks base method.
func (m *MockIManager) GetCache() *cache.GoldSettlementCache {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCache")
	ret0, _ := ret[0].(*cache.GoldSettlementCache)
	return ret0
}

// GetCache indicates an expected call of GetCache.
func (mr *MockIManagerMockRecorder) GetCache() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCache", reflect.TypeOf((*MockIManager)(nil).GetCache))
}

// GetCommissionClient mocks base method.
func (m *MockIManager) GetCommissionClient(arg0 settlement_bill.SettlementBillType) commission.Client {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCommissionClient", arg0)
	ret0, _ := ret[0].(commission.Client)
	return ret0
}

// GetCommissionClient indicates an expected call of GetCommissionClient.
func (mr *MockIManagerMockRecorder) GetCommissionClient(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCommissionClient", reflect.TypeOf((*MockIManager)(nil).GetCommissionClient), arg0)
}

// GetGoldSettleOrderCount mocks base method.
func (m *MockIManager) GetGoldSettleOrderCount(arg0 context.Context, arg1 *reconcile_v2.TimeRangeReq, arg2 gold_commission.GoldType) (*reconcile_v2.CountResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGoldSettleOrderCount", arg0, arg1, arg2)
	ret0, _ := ret[0].(*reconcile_v2.CountResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGoldSettleOrderCount indicates an expected call of GetGoldSettleOrderCount.
func (mr *MockIManagerMockRecorder) GetGoldSettleOrderCount(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGoldSettleOrderCount", reflect.TypeOf((*MockIManager)(nil).GetGoldSettleOrderCount), arg0, arg1, arg2)
}

// GetGoldSettleOrderList mocks base method.
func (m *MockIManager) GetGoldSettleOrderList(arg0 context.Context, arg1 *reconcile_v2.TimeRangeReq, arg2 gold_commission.GoldType) (*reconcile_v2.OrderIdsResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGoldSettleOrderList", arg0, arg1, arg2)
	ret0, _ := ret[0].(*reconcile_v2.OrderIdsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGoldSettleOrderList indicates an expected call of GetGoldSettleOrderList.
func (mr *MockIManagerMockRecorder) GetGoldSettleOrderList(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGoldSettleOrderList", reflect.TypeOf((*MockIManager)(nil).GetGoldSettleOrderList), arg0, arg1, arg2)
}

// GetGuildInfo mocks base method.
func (m *MockIManager) GetGuildInfo(arg0 context.Context, arg1 uint32) (*Guild.GuildResp, bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGuildInfo", arg0, arg1)
	ret0, _ := ret[0].(*Guild.GuildResp)
	ret1, _ := ret[1].(bool)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetGuildInfo indicates an expected call of GetGuildInfo.
func (mr *MockIManagerMockRecorder) GetGuildInfo(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGuildInfo", reflect.TypeOf((*MockIManager)(nil).GetGuildInfo), arg0, arg1)
}

// GetGuildInfoFromCache mocks base method.
func (m *MockIManager) GetGuildInfoFromCache(arg0 context.Context, arg1 uint32) (*Guild.GuildResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGuildInfoFromCache", arg0, arg1)
	ret0, _ := ret[0].(*Guild.GuildResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetGuildInfoFromCache indicates an expected call of GetGuildInfoFromCache.
func (mr *MockIManagerMockRecorder) GetGuildInfoFromCache(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGuildInfoFromCache", reflect.TypeOf((*MockIManager)(nil).GetGuildInfoFromCache), arg0, arg1)
}

// GetMasterUid mocks base method.
func (m *MockIManager) GetMasterUid(arg0 context.Context, arg1 uint32) (uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMasterUid", arg0, arg1)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMasterUid indicates an expected call of GetMasterUid.
func (mr *MockIManagerMockRecorder) GetMasterUid(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMasterUid", reflect.TypeOf((*MockIManager)(nil).GetMasterUid), arg0, arg1)
}

// GetSettleMonthLastRange mocks base method.
func (m *MockIManager) GetSettleMonthLastRange(arg0 time.Time) (time.Time, time.Time) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSettleMonthLastRange", arg0)
	ret0, _ := ret[0].(time.Time)
	ret1, _ := ret[1].(time.Time)
	return ret0, ret1
}

// GetSettleMonthLastRange indicates an expected call of GetSettleMonthLastRange.
func (mr *MockIManagerMockRecorder) GetSettleMonthLastRange(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSettleMonthLastRange", reflect.TypeOf((*MockIManager)(nil).GetSettleMonthLastRange), arg0)
}

// GetSettleMonthRange mocks base method.
func (m *MockIManager) GetSettleMonthRange(arg0 time.Time) (time.Time, time.Time) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSettleMonthRange", arg0)
	ret0, _ := ret[0].(time.Time)
	ret1, _ := ret[1].(time.Time)
	return ret0, ret1
}

// GetSettleMonthRange indicates an expected call of GetSettleMonthRange.
func (mr *MockIManagerMockRecorder) GetSettleMonthRange(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSettleMonthRange", reflect.TypeOf((*MockIManager)(nil).GetSettleMonthRange), arg0)
}

// GetSpecialSettleDayRange mocks base method.
func (m *MockIManager) GetSpecialSettleDayRange(arg0 time.Time) (time.Time, time.Time) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSpecialSettleDayRange", arg0)
	ret0, _ := ret[0].(time.Time)
	ret1, _ := ret[1].(time.Time)
	return ret0, ret1
}

// GetSpecialSettleDayRange indicates an expected call of GetSpecialSettleDayRange.
func (mr *MockIManagerMockRecorder) GetSpecialSettleDayRange(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSpecialSettleDayRange", reflect.TypeOf((*MockIManager)(nil).GetSpecialSettleDayRange), arg0)
}

// GetStore mocks base method.
func (m *MockIManager) GetStore(arg0 gold_commission.GoldType) (mysql.IGoldCommissionMysql, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetStore", arg0)
	ret0, _ := ret[0].(mysql.IGoldCommissionMysql)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetStore indicates an expected call of GetStore.
func (mr *MockIManagerMockRecorder) GetStore(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetStore", reflect.TypeOf((*MockIManager)(nil).GetStore), arg0)
}

// GetYuyinExtraSettleOrderCount mocks base method.
func (m *MockIManager) GetYuyinExtraSettleOrderCount(arg0 context.Context, arg1 *reconcile_v2.TimeRangeReq) (*reconcile_v2.CountResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetYuyinExtraSettleOrderCount", arg0, arg1)
	ret0, _ := ret[0].(*reconcile_v2.CountResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetYuyinExtraSettleOrderCount indicates an expected call of GetYuyinExtraSettleOrderCount.
func (mr *MockIManagerMockRecorder) GetYuyinExtraSettleOrderCount(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetYuyinExtraSettleOrderCount", reflect.TypeOf((*MockIManager)(nil).GetYuyinExtraSettleOrderCount), arg0, arg1)
}

// GetYuyinExtraSettleOrderList mocks base method.
func (m *MockIManager) GetYuyinExtraSettleOrderList(arg0 context.Context, arg1 *reconcile_v2.TimeRangeReq) (*reconcile_v2.OrderIdsResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetYuyinExtraSettleOrderList", arg0, arg1)
	ret0, _ := ret[0].(*reconcile_v2.OrderIdsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetYuyinExtraSettleOrderList indicates an expected call of GetYuyinExtraSettleOrderList.
func (mr *MockIManagerMockRecorder) GetYuyinExtraSettleOrderList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetYuyinExtraSettleOrderList", reflect.TypeOf((*MockIManager)(nil).GetYuyinExtraSettleOrderList), arg0, arg1)
}

// GetYuyinGoldSettleOrderCount mocks base method.
func (m *MockIManager) GetYuyinGoldSettleOrderCount(arg0 context.Context, arg1 *reconcile_v2.TimeRangeReq) (*reconcile_v2.CountResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetYuyinGoldSettleOrderCount", arg0, arg1)
	ret0, _ := ret[0].(*reconcile_v2.CountResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetYuyinGoldSettleOrderCount indicates an expected call of GetYuyinGoldSettleOrderCount.
func (mr *MockIManagerMockRecorder) GetYuyinGoldSettleOrderCount(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetYuyinGoldSettleOrderCount", reflect.TypeOf((*MockIManager)(nil).GetYuyinGoldSettleOrderCount), arg0, arg1)
}

// GetYuyinGoldSettleOrderList mocks base method.
func (m *MockIManager) GetYuyinGoldSettleOrderList(arg0 context.Context, arg1 *reconcile_v2.TimeRangeReq) (*reconcile_v2.OrderIdsResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetYuyinGoldSettleOrderList", arg0, arg1)
	ret0, _ := ret[0].(*reconcile_v2.OrderIdsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetYuyinGoldSettleOrderList indicates an expected call of GetYuyinGoldSettleOrderList.
func (mr *MockIManagerMockRecorder) GetYuyinGoldSettleOrderList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetYuyinGoldSettleOrderList", reflect.TypeOf((*MockIManager)(nil).GetYuyinGoldSettleOrderList), arg0, arg1)
}

// HandleCorporate mocks base method.
func (m *MockIManager) HandleCorporate(arg0 context.Context, arg1 *model.CommonSettleInfo) (bool, uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "HandleCorporate", arg0, arg1)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(uint32)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// HandleCorporate indicates an expected call of HandleCorporate.
func (mr *MockIManagerMockRecorder) HandleCorporate(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HandleCorporate", reflect.TypeOf((*MockIManager)(nil).HandleCorporate), arg0, arg1)
}

// IsSkipGuild mocks base method.
func (m *MockIManager) IsSkipGuild(arg0 uint32) bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IsSkipGuild", arg0)
	ret0, _ := ret[0].(bool)
	return ret0
}

// IsSkipGuild indicates an expected call of IsSkipGuild.
func (mr *MockIManagerMockRecorder) IsSkipGuild(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsSkipGuild", reflect.TypeOf((*MockIManager)(nil).IsSkipGuild), arg0)
}

// NewFlow mocks base method.
func (m *MockIManager) NewFlow(arg0 context.Context, arg1 *gold_commission.NewFlowReq) (*gold_commission.NewFlowResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "NewFlow", arg0, arg1)
	ret0, _ := ret[0].(*gold_commission.NewFlowResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// NewFlow indicates an expected call of NewFlow.
func (mr *MockIManagerMockRecorder) NewFlow(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "NewFlow", reflect.TypeOf((*MockIManager)(nil).NewFlow), arg0, arg1)
}

// SendEmail mocks base method.
func (m *MockIManager) SendEmail(arg0 context.Context, arg1, arg2 string, arg3 []string) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "SendEmail", arg0, arg1, arg2, arg3)
}

// SendEmail indicates an expected call of SendEmail.
func (mr *MockIManagerMockRecorder) SendEmail(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendEmail", reflect.TypeOf((*MockIManager)(nil).SendEmail), arg0, arg1, arg2, arg3)
}

// SettleAmuseExtra mocks base method.
func (m *MockIManager) SettleAmuseExtra(arg0 context.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SettleAmuseExtra", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// SettleAmuseExtra indicates an expected call of SettleAmuseExtra.
func (mr *MockIManagerMockRecorder) SettleAmuseExtra(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SettleAmuseExtra", reflect.TypeOf((*MockIManager)(nil).SettleAmuseExtra), arg0)
}

// SettleGold mocks base method.
func (m *MockIManager) SettleGold(arg0 context.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SettleGold", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// SettleGold indicates an expected call of SettleGold.
func (mr *MockIManagerMockRecorder) SettleGold(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SettleGold", reflect.TypeOf((*MockIManager)(nil).SettleGold), arg0)
}

// SettleYuyinExtra mocks base method.
func (m *MockIManager) SettleYuyinExtra(arg0 context.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SettleYuyinExtra", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// SettleYuyinExtra indicates an expected call of SettleYuyinExtra.
func (mr *MockIManagerMockRecorder) SettleYuyinExtra(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SettleYuyinExtra", reflect.TypeOf((*MockIManager)(nil).SettleYuyinExtra), arg0)
}
