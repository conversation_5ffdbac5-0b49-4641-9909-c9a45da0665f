package runtime

import (
	"bytes"
	"encoding/hex"
	"fmt"
	"sync"

	//pb "github.com/golang/protobuf/proto"
	pb "gitlab.ttyuyin.com/avengers/tyr/compatible/proto"

	"golang.52tt.com/clients/session"
	"golang.52tt.com/pkg/alarm"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/metrics"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/services/logic-grpc-gateway/gwcontext"
	"golang.52tt.com/services/logic-grpc-gateway/logicproto"
	"golang.org/x/net/context"

	"golang.52tt.com/protocol/common/status"
	"google.golang.org/grpc/codes"
	gRPCstatus "google.golang.org/grpc/status"
)

const (
	errPBMarshalling   = -100003
	kGetSession        = "GetSession"
	kInvalidMagic      = "InvalidMagic"
	kGetSessionFail    = "GetSessionErr"
	kPktLenInvalid     = "PktLenInvalid"
	kLenLessHeaderSize = "LenLessHeaderSize"
	kDecryptErr        = "DecryptErr"
	kEncryptErr        = "EncryptErr"
	kInvokeErr         = "InvokeErr"
	kDecompressErr     = "DecompressErr"
	kFailBySystem      = -2
)

var (
	kBadPkgErr = protocol.NewServerError(status.ErrBadRequest)
)

type LogicHandler func(ctx context.Context, w ResponseWriter, req *logicproto.ServicePacket) error

type LogicServerMux struct {
	mu       sync.RWMutex
	handlers map[int]handler

	sessionClient *session.Client
}

func NewLogicServerMux() *LogicServerMux {
	return &LogicServerMux{
		handlers:      make(map[int]handler),
		sessionClient: session.NewClient(),
	}
}

func (m *LogicServerMux) Handle(cmdID int, cmdName string, h LogicHandler) {
	m.mu.Lock()
	defer m.mu.Unlock()

	m.handlers[cmdID] = handler{cmdID, cmdName, h}
}

func (m *LogicServerMux) DeleteHandler(cmdID int) {
	m.mu.Lock()
	defer m.mu.Unlock()

	delete(m.handlers, cmdID)
}

func (m *LogicServerMux) ServeRequest(w ResponseWriter, request *Request) {
	w.SetCloseAfterReply(true)

	var (
		err       error
		hdr       *logicproto.ServicePacketHeader
		extraTail []byte
	)

	defer func() {
		if err != nil {

			var requestId uint64
			var requestIdStr string
			if extraTail != nil {
				var (
					err2      error
					extra     = &logicproto.ExtraTailReq{}
					extraResp = &logicproto.ExtraTailResp{}
				)

				if err2 = pb.Unmarshal(extraTail, extra); err2 != nil {
					log.Errorf("pb ExtraTailReq unmarshal fail, err:%v", err2)
				} else {
					requestId = extra.GetRequestId()
					requestIdStr = extra.GetRequestIdStr()
					extraResp.RequestId = &requestId
					extraTail, err2 = pb.Marshal(extraResp)
					if err2 != nil || len(extraTail) == 0 {
						log.Errorf("marshal extrailResp fail err:%v", err2)
					}
				}
			}

			// grpc err 2 5 14 = -2, app页面是否显示 -2 系统错误?
			if sc := protocol.FromSvrkitError(err); sc != nil {
				log.Debugf("<uid:%d,cmd:%d> ServeRequest FromSvrkitError:%d(%s)", hdr.Uid, hdr.CommandID, sc.Code(), sc.Message())
				hdr.Return = int32(sc.Code())
			} else { // tcp write err
				hdr.Return = -6
			}

			// grpc err 4=-5, 13=-21
			st, _ := gRPCstatus.FromError(err)
			switch st.Code() {
			case codes.ResourceExhausted:
				hdr.Return = status.ErrTrafficAdmin // -8
			case codes.DeadlineExceeded:
				hdr.Return = status.ErrRequestTimeout // -5
			case codes.Internal:
				hdr.Return = status.ErrPbParseError // -21
			}

			// reset pkg bodylen
			hdr.BodyLength = 0
			hdr.TailLen = 0
			hdr.ExtraTailLen = uint16(len(extraTail))
			logicResponse := &logicproto.ServicePacket{
				Header:    hdr,
				ExtraTail: extraTail,
			}

			log.Errorf("gateway write bad pkg response ->[%s] <uid:%d,cmd:%d,requestId:%s,ret:%d,err:(%v),rsp_plen:(%d)=HeaderLength(%d)+BodyLength(%d)+TailLen(%d)+ExtraTailLen(%d)",
				request.RemoteAddr,
				hdr.Uid,
				hdr.CommandID,
				requestIdStr,
				hdr.Return,
				err,
				uint32(logicResponse.Header.HeaderLength)+uint32(logicResponse.Header.BodyLength)+uint32(logicResponse.Header.TailLen)+uint32(logicResponse.Header.ExtraTailLen),
				uint32(logicResponse.Header.HeaderLength),
				uint32(logicResponse.Header.BodyLength),
				uint32(logicResponse.Header.TailLen),
				uint32(logicResponse.Header.ExtraTailLen),
			)

			// ignore tcp write err
			_ = logicResponse.WriteTo(w)
		}
	}()

	// Validate request
	reqLen := uint32(len(request.Body))
	if reqLen < logicproto.ServicePacketHeaderLength {
		err = kBadPkgErr
		metrics.DISCOVERY_FUNC_TRACK(protocol.NewServerError(kFailBySystem, kLenLessHeaderSize)).End()
		return
	}

	r := bytes.NewReader(request.Body)
	hdr = new(logicproto.ServicePacketHeader)

	hdr.ReadFrom(r)

	if hdr.Magic != logicproto.ServicePacketMagic {
		err = kBadPkgErr
		log.Errorf("<cmd:%d,uid:%d,>get reqLen(%d) > invalid magic(%d) return ", hdr.CommandID, hdr.Uid, reqLen, hdr.Magic)
		metrics.DISCOVERY_FUNC_TRACK(protocol.NewServerError(kFailBySystem, kInvalidMagic)).End()
		return
	}

	if hdr.BodyLength+uint32(hdr.HeaderLength)+uint32(hdr.TailLen)+uint32(hdr.ExtraTailLen) > reqLen {
		err = kBadPkgErr
		log.Errorf("<cmd:%d,uid:%d,>get reqLen(%d) > BodyLength(%d)+HeaderLength(%d) + TailLen(%d) + ExtraTailLen(%d) return ", hdr.CommandID, hdr.Uid, reqLen, hdr.BodyLength, hdr.HeaderLength, hdr.TailLen, hdr.ExtraTailLen)
		metrics.DISCOVERY_FUNC_TRACK(protocol.NewServerError(kFailBySystem, kPktLenInvalid)).End()
		return
	}
	log.Debugf("ServeRequest RemoteAddr:%s, uid:%v, cmd:%v", request.RemoteAddr, hdr.Uid, hdr.CommandID)

	exceptTailLen := uint32(hdr.HeaderLength) + hdr.BodyLength
	body := request.Body[hdr.HeaderLength:exceptTailLen]
	//尾部取出来暂不用

	tail := request.Body[exceptTailLen : exceptTailLen+uint32(hdr.TailLen)]
	extraTail = request.Body[exceptTailLen+uint32(hdr.TailLen):]

	m.mu.RLock()
	h, ok := m.handlers[int(hdr.CommandID)]
	m.mu.RUnlock()

	if ok {
		var sessionKey []byte
		if hdr.Uid > 0 && hdr.SessionKey[0] == 0 {
			_, _, appID := protocol.UnPackTerminalType(hdr.TerminalType)
			// no session key from proxy, get from session server (for fast login)
			var sk string
			sk, err = m.sessionClient.GetSession(request.Context(), hdr.Uid, uint32(appID))
			if err != nil {
				log.Errorf("Failed to get session key from session server: %d %s %v", hdr.Uid, appID, err)
				metrics.DISCOVERY_FUNC_TRACK(protocol.NewServerError(kFailBySystem, kGetSessionFail)).End()
				return
			}
			metrics.DISCOVERY_FUNC_TRACK(protocol.NewServerError(0, kGetSession)).End()
			log.Warnf("Got session key for %d %d", hdr.Uid, len(sk))
			sessionKey = []byte(sk)
		} else {
			sessionKey = hdr.SessionKey[:]
		}

		// decrypt
		body, err = logicproto.Decrypt(body, sessionKey, hdr.CryptAlgorithm)
		if err != nil {
			err = kBadPkgErr
			log.Errorf("decrypt failed: uid:%d cmd:%d %d %s key:%s alg:%d bodyLen:%d, err:%v",
				hdr.Uid, hdr.CommandID, hdr.IP, hex.EncodeToString(hdr.DeviceID[:]),
				hex.EncodeToString(hdr.SessionKey[:]), hdr.CryptAlgorithm, hdr.BodyLength, err)
			metrics.DISCOVERY_FUNC_TRACK(protocol.NewServerError(kFailBySystem, kDecryptErr)).End()
			return
		}

		// decrypt OK, session key matches
		if hdr.Uid > 0 && hdr.SessionKey[0] == 0 {
			if len(sessionKey) >= 32 {
				sessionKey = sessionKey[:32]
			}
			copy(hdr.SessionKey[:], sessionKey)
		}

		body, err = logicproto.Decompress(body, hdr.CompressVersion, hdr.CompressAlgorithm)
		if err != nil {
			err = kBadPkgErr
			log.Errorf("decompress failed: %d %d %d %s ver:%d alg:%d",
				hdr.Uid, hdr.CommandID, hdr.IP, hex.EncodeToString(hdr.DeviceID[:]),
				hdr.CompressVersion, hdr.CompressAlgorithm)
			metrics.DISCOVERY_FUNC_TRACK(protocol.NewServerError(kFailBySystem, kDecompressErr)).End()
			return
		}

		if len(body) != int(hdr.CompressLength) {
			log.Warnf("uid: %d cmd: %d inconsistent error: hdr.CompressLength %d, decompressed len %d",
				hdr.Uid, hdr.CommandID, hdr.CompressLength, len(body))
		}

		if hdr.TerminalType == 0 {
			hdr.TerminalType, _ = protocol.TerminalTypeFromClientType(hdr.ClientType)
		}

		log.Debugf("LogicMux: serve req %+v %d", hdr, len(body))

		ctx, _ := context.WithCancel(request.Context())
		err = h.h(ctx, w, &logicproto.ServicePacket{Header: hdr, Body: body, Tail: tail, ExtraTail: extraTail, RemoteAddr: request.RemoteAddr})
	} else {
		//use cli register cmd handler
		//example: logic-cmd-configer update --service retcode-metrics --id 777666 --method /retcode_metrics.Retcode/Echosvr
		log.Warnf("uid:%d not find cmd:%d router", hdr.Uid, hdr.CommandID)
		if hdr.Uid > 0 {
			alarm.SendAlarm("logic_grpc_gw_router_conf", fmt.Sprintf("uid:%d not find cmd:%d etcd grpc router", hdr.Uid, hdr.CommandID))
		}
		metrics.DISCOVERY_FUNC_TRACK(protocol.NewServerError(kFailBySystem, "cmd_etcd_router_not_find")).End()
	}
}

// ForwardResponse -
func ForwardResponse(ctx context.Context, w ResponseWriter, req *logicproto.ServicePacket, body, extraTail []byte) error {
	metadata, ok := GRPCServerMetadataFromContext(ctx)
	if !ok {
		log.Errorf("Failed to extract GRPCServeMetadata from context")
		//return
	}
	if info := gwcontext.ParseServiceInfo(metadata.HeaderMD); info != nil {
		if req.Header.Uid == 0 {
			req.Header.Uid = info.UserID
		}
		if req.Header.SessionKey[0] == 0 {
			copy(req.Header.SessionKey[:], []byte(info.SessionKey))
		}
		if req.Header.ClientType == 0 {
			req.Header.ClientType = info.ClientType
		}
	}

	hdr := req.Header
	logicResponse := &logicproto.ServicePacket{
		Header:    hdr,
		ExtraTail: extraTail,
	}
	log.Debugf("LogicMux: cmd:%d, uid:%d, metadata:%+v", hdr.CommandID, hdr.Uid, metadata.HeaderMD)

	var (
		Ret            int32
		ServiceRetCode int32 = int32(gwcontext.ResponseServiceRetCode(metadata.HeaderMD))
	)
	//cpplogic have two ret code
	if sret := gwcontext.ResponseServiceSRetCode(metadata.HeaderMD); sret != 0 {
		Ret = ServiceRetCode
		ServiceRetCode = int32(sret)
	}

	// OK
	logicResponse.Body = body

	logicResponse.Header.Return = Ret
	logicResponse.Header.BodyLength = uint32(len(body))
	logicResponse.Header.CompressLength = uint32(len(body))
	logicResponse.Header.ServiceRetCode = ServiceRetCode

	// compress
	if cb, err := logicproto.Compress(body, hdr.CompressVersion, hdr.CompressAlgorithm); err == nil && len(cb) < len(body) {
		log.Debugf("LogicMux: cmd:%d, uid:%d, compress %d -> %d", hdr.CommandID, hdr.Uid, len(logicResponse.Body), len(cb))
		logicResponse.Body = cb
		logicResponse.Header.BodyLength = uint32(len(cb))
	} else {
		if err != nil {
			log.Errorf("logic_mux WARNING compress failed: %v", err)
		}
		logicResponse.Header.CompressAlgorithm = logicproto.CompressAlgorithmNone
	}

	respCryptAlg, respCryptKey := gwcontext.ResponseCryptAlgorithmAndKey(metadata.HeaderMD)
	alg := logicproto.CryptAlgorithmAES
	key := hdr.SessionKey[:]
	if respCryptAlg != nil {
		alg = *respCryptAlg
		key = respCryptKey
	}

	// encrypt
	b, err := logicproto.Encrypt(logicResponse.Body, key, alg)
	if err != nil {
		log.Errorf("LogicMux: cmd:%d, uid:%d, Encrypt failed %+v", hdr.CommandID, hdr.Uid, err)
		metrics.DISCOVERY_FUNC_TRACK(protocol.NewServerError(kFailBySystem, kEncryptErr)).End()
		return kBadPkgErr
	}
	logicResponse.Body = b
	logicResponse.Header.BodyLength = uint32(len(b))
	logicResponse.Header.CryptAlgorithm = alg
	logicResponse.Header.TailLen = 0
	logicResponse.Header.ExtraTailLen = uint16(len(extraTail))
	logicResponse.Header.HeaderLength = uint8(logicproto.ServicePacketHeaderLengthV2)

	plen := uint32(logicResponse.Header.HeaderLength) + logicResponse.Header.BodyLength + uint32(logicResponse.Header.ExtraTailLen)
	err = logicResponse.WriteTo(w)
	if err != nil {
		return err
	}

	log.Debugf("LogicMux: serve resp %+v %d %d %d", logicResponse.Header, len(logicResponse.Body), plen, len(extraTail))
	return nil
}

type handler struct {
	cmdID   int
	cmdName string
	h       LogicHandler
}
