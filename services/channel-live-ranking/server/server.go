package server

import (
	"context"
	"time"

	"github.com/go-redis/redis"
	"github.com/jmoiron/sqlx"
	"gitlab.ttyuyin.com/avengers/tyr/pkg/cluster/timer"
	"golang.52tt.com/pkg/config"
	"golang.52tt.com/pkg/log"
	tracing "golang.52tt.com/pkg/tracing/jaeger"
	pb "golang.52tt.com/protocol/services/channel-live-ranking"
	reconcile_v2 "golang.52tt.com/protocol/services/reconcile-v2"
	"golang.52tt.com/services/channel-live-ranking/cache"
	"golang.52tt.com/services/channel-live-ranking/conf"
	"golang.52tt.com/services/channel-live-ranking/event"
	"golang.52tt.com/services/channel-live-ranking/manager"
	"golang.52tt.com/services/channel-live-ranking/mysql"
	"gopkg.in/errgo.v2/errors"
)

type ChannelLiveRankingServer struct {
	sc                   *conf.ServiceConfigT
	mgr                  *manager.ChannelLiveRankingManager
	cacheClient          *cache.ChannelLiveRankingCache
	mysqlStore           *mysql.Store
	channelKafkaSub      *event.SimpleChannelKafkaSub
	chLiveFansKafkaSub   *event.ChLiveFansKafkaSub
	chLiveStatusKafkaSub *event.ChLiveStatusKafkaSub
	presentEventSub      *event.PresentEventSub
	anchorFansEventSub   *event.AnchorFansEventSub
	knightEventSub       *event.KnightEventSub
	yKWEventSub          *event.YKWEventSub
	timerD               *timer.Timer
}

func NewChannelLiveRankingServer(ctx context.Context, cfg config.Configer) (*ChannelLiveRankingServer, error) {

	sc := &conf.ServiceConfigT{}

	cfgPath := ctx.Value("configfile").(string)
	if cfgPath == "" {
		return nil, errors.New("configfile not exist")
	}
	err := sc.Parse(cfgPath)
	if err != nil {
		log.ErrorWithCtx(ctx, "config Parse fail err:%v", err)
		return nil, err
	}

	if sc.RedisConfig.PoolSize == 0 {
		sc.RedisConfig.PoolSize = 300
	}
	redisClient := redis.NewClient(&redis.Options{
		Network:            sc.GetRedisConfig().Protocol,
		Addr:               sc.GetRedisConfig().Addr(),
		PoolSize:           sc.GetRedisConfig().PoolSize,
		IdleCheckFrequency: sc.GetRedisConfig().IdleCheckFrequency(),
		DB:                 sc.GetRedisConfig().DB,
	})
	log.DebugWithCtx(ctx, "Initialized redis connection pool to %s://%s/%d", sc.GetRedisConfig().Protocol, sc.GetRedisConfig().Addr(), sc.GetRedisConfig().DB)
	redisTracer := tracing.Init("channel-live-ranking_redis")
	cacheClient := cache.NewChannelLiveRankingCache(redisClient, redisTracer)

	mysqlDb, err := sqlx.Connect("mysql", sc.GetMysqlConnectionString())
	if err != nil {
		log.ErrorWithCtx(ctx, "Failed to create mysql %v", err)
		return nil, err
	}
	mysqlDb.SetMaxOpenConns(100)
	mysqlDb.SetMaxIdleConns(100)
	mysqlDb.SetConnMaxLifetime(time.Minute * 5)
	mysqlStore := mysql.NewMysql(mysqlDb)
	mysqlStore.CreateMysqlTable()

	// 大小进程的定时器，同一个任务只会在一个节点上执行
	timerD_, err := timer.NewTimerD(ctx, "channel-live-ranking", timer.WithV6RedisCmdable(redisClient))
	if err != nil {
		log.ErrorWithCtx(ctx, "Failed to NewTimerD err:%v", err)
		return nil, err
	}

	mgr, err := manager.NewChannelLiveRankingManager(sc, cacheClient, mysqlStore, timerD_)
	if err != nil {
		log.ErrorWithCtx(ctx, "Failed to NewChannelLiveRankingManager err:%v", err)
		return nil, err
	}

	channelEvent, err := event.NewChannelKafkaSubscriber(sc.GetChannelKafkaConfig(), mgr)
	if err != nil {
		log.ErrorWithCtx(ctx, "Failed to NewChannelKafkaSubscriber err %s", err.Error())
		return nil, err
	}

	/*
		err = channelEvent.Start()
		if err != nil {
			log.ErrorWithCtx(ctx, "Failed to Start subGoldAwardEvent.Start() err %s", err.Error())
			return nil, err
		}
	*/

	chLiveFansEvent, err := event.NewChLiveFansKafkaSubscriber(sc.GetFansLoveKafkaConfig(), mgr)
	if err != nil {
		log.ErrorWithCtx(ctx, "Failed to NewChannelKafkaSubscriber err %s", err.Error())
		return nil, err
	}

	/*
		err = chLiveFansEvent.Start()
		if err != nil {
			log.ErrorWithCtx(ctx, "Failed to Start subGoldAwardEvent.Start() err %s", err.Error())
			return nil, err
		}
	*/

	chLiveStatusEvent, err := event.NewChLiveStatusKafkaSubscriber(sc.GetLiveStatusKafkaConfig(), mgr)
	if err != nil {
		log.ErrorWithCtx(ctx, "Failed to NewChLiveStatusKafkaSubscriber err %s", err.Error())
		return nil, err
	}

	/*
		err = chLiveStatusEvent.Start()
		if err != nil {
			log.ErrorWithCtx(ctx, "Failed to Start subGoldAwardEvent.Start() err %s", err.Error())
			return nil, err
		}
	*/

	presentEventcfg := sc.PresentKafkaConfig
	presentEventSub, err := event.NewPresentEventSubscriber(presentEventcfg, mgr)
	if err != nil {
		log.ErrorWithCtx(ctx, "Failed to NewPresentEventSubscriber err %s", err.Error())
		return nil, err
	}
	/*
		err = presentEventSub.Start()
		if err != nil {
			log.ErrorWithCtx(ctx, "Failed to Start NewPresentEventSubscriber.Start() err %s", err.Error())
			return nil, err
		}
	*/

	anchorFansKafkaConfig := sc.AnchorFansKafkaConfig
	anchorFansEventSub, err := event.NewAnchorFansEventSub(anchorFansKafkaConfig, mgr)
	if err != nil {
		log.ErrorWithCtx(ctx, "Failed to NewAnchorFansEventSub err %s", err.Error())
		return nil, err
	}

	/*
		err = anchorFansEventSub.Start()
		if err != nil {
			log.ErrorWithCtx(ctx, "Failed to Start NewAnchorFansEventSub.Start() err %s", err.Error())
			return nil, err
		}
	*/

	knightKafkaConfig := sc.KnightKafkaConfig
	knightEventSub, err := event.NewKnightEventSub(knightKafkaConfig, mgr)
	if err != nil {
		log.ErrorWithCtx(ctx, "Failed to NewKnightEventSub err %s", err.Error())
		return nil, err
	}
	/*
		err = knightEventSub.Start()
		if err != nil {
			log.ErrorWithCtx(ctx, "Failed to Start NewKnightEventSub.Start() err %s", err.Error())
			return nil, err
		}
	*/

	ykwKafkaConfig := sc.YKWKafkaConfig
	yKWEventSub, err := event.NewYKWEventSub(ykwKafkaConfig, mgr)
	if err != nil {
		log.ErrorWithCtx(ctx, "Failed to NewYKWEventSub err %s", err.Error())
		return nil, err
	}
	/*
		err = yKWEventSub.Start()
		if err != nil {
			log.ErrorWithCtx(ctx, "Failed to Start NewYKWEventSub.Start() err %s", err.Error())
			return nil, err
		}
	*/

	timerD_.Start()

	return &ChannelLiveRankingServer{
		sc:                   sc,
		mgr:                  mgr,
		cacheClient:          cacheClient,
		mysqlStore:           mysqlStore,
		channelKafkaSub:      channelEvent,
		chLiveFansKafkaSub:   chLiveFansEvent,
		chLiveStatusKafkaSub: chLiveStatusEvent,
		presentEventSub:      presentEventSub,
		anchorFansEventSub:   anchorFansEventSub,
		knightEventSub:       knightEventSub,
		yKWEventSub:          yKWEventSub,
		timerD:               timerD_,
	}, nil
}

func (s *ChannelLiveRankingServer) GetRankingList(ctx context.Context, in *pb.GetRankingListReq) (out *pb.GetRankingListResp, err error) {
	return s.mgr.GetRankingListWithMyRank(ctx, in.GetActorUid(), in.GetRankingType(), in.GetQueryTimeType(), s.mgr.BusinessConfMgr.GetRankingListMemSize())
}

func (s *ChannelLiveRankingServer) GetAnchorHonorNameplate(ctx context.Context, in *pb.GetAnchorHonorNameplateReq) (out *pb.GetAnchorHonorNameplateResp, err error) {
	out = &pb.GetAnchorHonorNameplateResp{}

	out.HonorNameplate, err = s.mgr.GetAnchorHonorNameplate(ctx, in.GetActorUid())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAnchorHonorNameplate fail to GetAnchorHonorNameplate. req:%+v err:%v", in, err)
		return out, err
	}

	log.DebugfWithCtx(ctx, "GetAnchorHonorNameplate req:%+v, rsp:%+v", in, out)
	return out, nil
}

func (s *ChannelLiveRankingServer) BatchGetAnchorHonorNameplate(ctx context.Context, in *pb.BatchGetAnchorHonorNameplateReq) (out *pb.BatchGetAnchorHonorNameplateResp, err error) {
	out = &pb.BatchGetAnchorHonorNameplateResp{}

	out.MapHonorNameplate, err = s.mgr.BatchGetAnchorHonorNameplate(ctx, in.GetActorUidList())
	if err != nil {
		log.ErrorWithCtx(ctx, "BatchGetAnchorHonorNameplate fail to BatchGetAnchorHonorNameplate. req:%+v err:%v", in, err)
		return out, err
	}

	log.DebugfWithCtx(ctx, "BatchGetAnchorHonorNameplate req:%+v, rsp:%+v", in, out)
	return out, nil
}

func (s *ChannelLiveRankingServer) GiveAnchorHonorNameplate(ctx context.Context, in *pb.GiveAnchorHonorNameplateReq) (out *pb.GiveAnchorHonorNameplateResp, err error) {
	out = &pb.GiveAnchorHonorNameplateResp{}

	err = s.mgr.GiveAnchorHonorNameplate(ctx, in.GetActorUid(), in.GetHonorId(), in.GetExpiredTime())
	if err != nil {
		log.ErrorWithCtx(ctx, "GiveAnchorHonorNameplate fail to GiveAnchorHonorNameplate. req:%+v err:%v", in, err)
		return out, err
	}

	log.DebugfWithCtx(ctx, "GiveAnchorHonorNameplate req:%+v, rsp:%+v", in, out)
	return out, nil
}

func (s *ChannelLiveRankingServer) GetLiveFansWeekRank(ctx context.Context, in *pb.GetLiveFansWeekRankReq) (*pb.GetLiveFansWeekRankResp, error) {
	return s.mgr.GetLiveFansWeekRank(ctx, in)
}

func (s *ChannelLiveRankingServer) SettleLiveFansWeekRank(ctx context.Context, in *pb.SettleLiveFansWeekRankReq) (*pb.SettleLiveFansWeekRankResp, error) {
	return s.mgr.SettleLiveFansWeekRank(ctx, in)
}

func (s *ChannelLiveRankingServer) SettleLiveFansWeekRankYKW(ctx context.Context, in *pb.SettleLiveFansWeekRankYKWReq) (*pb.SettleLiveFansWeekRankYKWResp, error) {
	return s.mgr.SettleLiveFansWeekRankYKW(ctx, in)
}
func (s *ChannelLiveRankingServer) TestSettleLiveFansWeekRank(ctx context.Context, in *pb.TestSettleLiveFansWeekRankReq) (*pb.TestSettleLiveFansWeekRankResp, error) {
	return s.mgr.TestSettleLiveFansWeekRank(ctx, in)
}
func (s *ChannelLiveRankingServer) TestSettleLiveFansWeekRankYKW(ctx context.Context, in *pb.TestSettleLiveFansWeekRankYKWReq) (*pb.TestSettleLiveFansWeekRankYKWResp, error) {
	return s.mgr.TestSettleLiveFansWeekRankYKW(ctx, in)
}
func (s *ChannelLiveRankingServer) HandleFansWeekRank(ctx context.Context, in *pb.HandleFansWeekRankReq) (*pb.HandleFansWeekRankResp, error) {
	return s.mgr.HandleFansWeekRank(ctx, in)
}
func (s *ChannelLiveRankingServer) FixFansWeekRankOrder(ctx context.Context, in *reconcile_v2.ReplaceOrderReq) (*reconcile_v2.EmptyResp, error) {
	return s.mgr.FixFansWeekRankOrder(ctx, in)
}
func (s *ChannelLiveRankingServer) GetFansWeekRankOrderCount(ctx context.Context, in *reconcile_v2.TimeRangeReq) (*reconcile_v2.CountResp, error) {
	return s.mgr.GetFansWeekRankOrderCount(ctx, in)
}
func (s *ChannelLiveRankingServer) GetFansWeekRankOrderList(ctx context.Context, in *reconcile_v2.TimeRangeReq) (*reconcile_v2.OrderIdsResp, error) {
	return s.mgr.GetFansWeekRankOrderList(ctx, in)
}

func (s *ChannelLiveRankingServer) ShutDown() {
	s.mgr.ShutDown()
	s.channelKafkaSub.Close()
	s.chLiveFansKafkaSub.Close()
	s.chLiveStatusKafkaSub.Close()
	s.presentEventSub.Close()
	s.anchorFansEventSub.Close()
	s.knightEventSub.Close()
	s.yKWEventSub.Close()
	s.timerD.Stop()
	log.Infof("ChannelLiveRankingServer ShutDown")
}
