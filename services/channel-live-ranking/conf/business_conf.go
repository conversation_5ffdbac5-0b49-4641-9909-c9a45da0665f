package conf

import (
	"crypto/md5"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"path/filepath"
	"sync"
	"time"

	"github.com/fsnotify/fsnotify"
	"github.com/spf13/viper"
	"golang.52tt.com/pkg/log"
)

const BusinessConfFile = "/data/oss/conf-center/tt/channel_live_ranking.json"

var LastConfMd5Sum [md5.Size]byte

const (
	AwardTypeUnknown               = 0
	AwardTypeHonorNameplate        = 1
	AwardTypeChannelBackground     = 2
	AwardTypeHeadwear              = 3
	AwardTypePackage               = 4
	AwardUserDecoration            = 5
	AwardChannelPersonalDecoration = 6
)

type HonorNameplateInfo struct {
	Id              uint32 `json:"id"`
	Name            string `json:"name"`
	Url             string `json:"url"`
	RankingEnterUrl string `json:"ranking_enter_url"`
}

type AwardItem struct {
	AwardType     uint32 `json:"award_type"`
	GiftId        string `json:"gift_id"`
	Num           uint32 `json:"num"`
	IncrExpiredTs uint32 `json:"incr_expired_ts"`
	AwardDesc     string `json:"award_desc"`
	Version       string `json:"version"`
}
type AwardConfInfo struct {
	RankingType   uint32       `json:"ranking_type"`
	RankRange     [2]uint32    `json:"rank_range"`
	AwardItemList []*AwardItem `json:"award_item_list"`
}

type BusinessConf struct {
	HonorNameplateConfList    []*HonorNameplateInfo `json:"honor_nameplate_conf_list"`
	RankingBlackList          []uint32              `json:"ranking_black_list"`
	AnchorRankPushIntervalSec uint32                `json:"anchor_rank_push_interval_sec"`
	AwardConfInfoList         []*AwardConfInfo      `json:"award_conf_info_list"`
	MaxAwardRank              uint32                `json:"max_award_rank"`
	RankingListMemSize        uint32                `json:"ranking_list_mem_size"`
	HighCertIdList            []uint32              `json:"high_cert_id_list"` // 优质的主播认证标识id

	FansWeekRankConf struct {
		FansWeekRankTop1AwardMinVal uint32

		AddFansLoveValuelList []struct {
			Rank       uint32
			AddLoveVal uint32
		}
		Rank2LoveValue map[uint32]uint32

		SettleCron string
		BlackUids  []uint32

		BanSettle         bool
		BanInSettleStatus bool
	}

	WarnConf struct {
		FeiShuUrl string
		Push      bool
	}
}

func (c *BusinessConf) Parse(configFile string) (isChange bool, err error) {
	defer func() {
		if e := recover(); e != nil {
			err = fmt.Errorf("Failed to parse config: %v \n", e)
		}
	}()

	data, err := ioutil.ReadFile(configFile)
	if err != nil {
		return false, err
	}

	md5Sum := md5.Sum(data)
	if md5Sum == LastConfMd5Sum {
		isChange = false
		return
	}

	err = json.Unmarshal(data, &c)
	if err != nil {
		return false, err
	}

	c.FansWeekRankConf.Rank2LoveValue = map[uint32]uint32{}
	for _, info := range c.FansWeekRankConf.AddFansLoveValuelList {
		c.FansWeekRankConf.Rank2LoveValue[info.Rank] = info.AddLoveVal
	}

	LastConfMd5Sum = md5Sum

	log.Infof("BusinessConf : %+v", c)
	log.Infof("FansWeekRankConf=%+v", c.FansWeekRankConf)
	return true, nil
}

type BusinessConfManager struct {
	Done   chan interface{}
	Change chan bool
	mutex  sync.RWMutex
	conf   *BusinessConf
}

func NewBusinessConfManager() *BusinessConfManager {
	businessConf := &BusinessConf{}

	_, err := businessConf.Parse(BusinessConfFile)
	if err != nil {
		log.Errorf("NewBusinessConfManager fail to Parse BusinessConf err:%v", err)
	}

	confMgr := &BusinessConfManager{
		conf:   businessConf,
		Done:   make(chan interface{}),
		Change: make(chan bool, 1),
	}

	//go confMgr.Watch(BusinessConfFile)
	//confMgr.WatchV2(BusinessConfFile)
	go confMgr.WatchV3(BusinessConfFile)

	return confMgr
}

func (bm *BusinessConfManager) Reload(file string) error {
	businessConf := &BusinessConf{}

	isChange, err := businessConf.Parse(file)
	if err != nil {
		log.Errorf("NewBusinessConfManager fail to Parse BusinessConf err:%v", err)
		return err
	}

	if isChange {
		bm.mutex.Lock()
		bm.conf = businessConf
		bm.mutex.Unlock()

		bm.Change <- true
		log.Infof("Reload %+v", businessConf)
	}

	return nil
}

func (bm *BusinessConfManager) WatchV3(file string) {
	log.Infof("Watch start. file:%s", file)

	for {
		select {
		case _, ok := <-bm.Done:
			if !ok {
				log.Infof("Watch done")
				return
			}

		case <-time.After(1 * time.Second):
			log.Debugf("Watch check change")

			err := bm.Reload(file)
			if err != nil {
				log.Errorf("Watch Reload fail. file:%s, err:%v", file, err)
			}
		}
	}
}

func (bm *BusinessConfManager) WatchV2(file string) {
	v := viper.New()
	v.SetConfigFile(file)
	v.SetConfigType("json")

	v.OnConfigChange(func(event fsnotify.Event) {
		log.Debugf("Watch watcher.Events:%+v", event)

		if event.Op&fsnotify.Create == fsnotify.Create || event.Op&fsnotify.Write == fsnotify.Write {
			_ = bm.Reload(file)
			bm.Change <- true
			log.Debugf("Watch file change")
		}
	})

	v.WatchConfig()
}

func (bm *BusinessConfManager) Watch(file string) {
	filePath, fileName := filepath.Split(file)
	log.Debugf("Watch path:%s, fileName:%s", filePath, fileName)

	watcher, err := fsnotify.NewWatcher()
	if err != nil {
		log.Errorf("Watch >> new watcher fail: %v", err)
		return
	}

	err = watcher.Add(filePath)
	if err != nil {
		log.Errorf("Watch >> watcher add fail: %v", err)
		return
	}

	log.Infof("Watch start")

	for {
		select {
		case <-bm.Done:
			_ = watcher.Close()
			log.Infof("Watch done")
			return

		case event := <-watcher.Events:
			log.Debugf("Watch watcher.Events:%+v", event)

			if event.Name == fileName && (event.Op&fsnotify.Create == fsnotify.Create ||
				event.Op&fsnotify.Write == fsnotify.Write || event.Op&fsnotify.Chmod == fsnotify.Chmod) {

				_ = bm.Reload(file)
				bm.Change <- true
				log.Debugf("Watch file change")
			}

		case err := <-watcher.Errors:
			log.Errorf("Watch err:%v", err)
			return
		}
	}
}

func (bm *BusinessConfManager) GetHonorNameplateById(id uint32) HonorNameplateInfo {
	out := HonorNameplateInfo{}

	bm.mutex.RLock()
	defer bm.mutex.RUnlock()

	for _, info := range bm.conf.HonorNameplateConfList {
		if info.Id == id {
			out = *info
		}
	}

	return out
}

func (bm *BusinessConfManager) CheckIfInRankingBlackList(uid uint32) bool {
	bm.mutex.RLock()
	defer bm.mutex.RUnlock()

	for _, id := range bm.conf.RankingBlackList {
		if id == uid {
			return true
		}
	}

	return false
}

func (bm *BusinessConfManager) GetRankingBlackList() []uint32 {
	bm.mutex.RLock()
	defer bm.mutex.RUnlock()

	return bm.conf.RankingBlackList
}

func (bm *BusinessConfManager) GetRankPushIntervalSec() uint32 {
	bm.mutex.RLock()
	defer bm.mutex.RUnlock()

	return bm.conf.AnchorRankPushIntervalSec
}

func (bm *BusinessConfManager) GetAwardConfList() []*AwardConfInfo {
	bm.mutex.RLock()
	defer bm.mutex.RUnlock()

	return bm.conf.AwardConfInfoList
}

func (bm *BusinessConfManager) GetMaxAwardRank() uint32 {
	bm.mutex.RLock()
	defer bm.mutex.RUnlock()

	return bm.conf.MaxAwardRank
}

func (bm *BusinessConfManager) GetRankingListMemSize() uint32 {
	bm.mutex.RLock()
	defer bm.mutex.RUnlock()

	memSize := bm.conf.RankingListMemSize
	if memSize == 0 || memSize > 150 {
		// 默认20
		memSize = 20
	}

	return memSize
}

func (bm *BusinessConfManager) GetHighCertIdList() []uint32 {
	bm.mutex.RLock()
	defer bm.mutex.RUnlock()

	return bm.conf.HighCertIdList
}

func (bm *BusinessConfManager) GetFansWeekRankTop1AwardMinVal() uint32 {
	bm.mutex.RLock()
	defer bm.mutex.RUnlock()
	return bm.conf.FansWeekRankConf.FansWeekRankTop1AwardMinVal
}
func (bm *BusinessConfManager) GetRankAddLoveValue(rank uint32) uint32 {
	bm.mutex.RLock()
	defer bm.mutex.RUnlock()
	return bm.conf.FansWeekRankConf.Rank2LoveValue[rank]
}

func (bm *BusinessConfManager) GetWarnConf() (string, bool) {
	bm.mutex.RLock()
	defer bm.mutex.RUnlock()
	return bm.conf.WarnConf.FeiShuUrl, bm.conf.WarnConf.Push
}

func (bm *BusinessConfManager) GetSettleCron() string {
	bm.mutex.RLock()
	defer bm.mutex.RUnlock()
	if bm.conf.FansWeekRankConf.SettleCron == "" {
		return "0 0 * * *"
	}
	return bm.conf.FansWeekRankConf.SettleCron
}

func (bm *BusinessConfManager) CheckIsBlockUser(uid uint32) bool {
	bm.mutex.RLock()
	defer bm.mutex.RUnlock()
	for _, blockUid := range bm.conf.FansWeekRankConf.BlackUids {
		if uid == blockUid {
			return true
		}
	}
	return false
}
func (bm *BusinessConfManager) BanSettle() bool {
	bm.mutex.RLock()
	defer bm.mutex.RUnlock()
	return bm.conf.FansWeekRankConf.BanSettle
}

func (bm *BusinessConfManager) BanInSettleStatus() bool {
	bm.mutex.RLock()
	defer bm.mutex.RUnlock()
	return bm.conf.FansWeekRankConf.BanInSettleStatus
}
