package server

import (
	channelRecSvrPb "golang.52tt.com/protocol/services/channel-recommend-svr"
)

const (
	ENUM_SCENE_HOT                 = 1 //热门房间推荐场景
	ENUM_SCENE_FIRST               = 2 // 娱乐首页推荐列表场景
	ENUM_SCENE_SECOND_PERSONAL_TAG = 3 // 娱乐二级个性标签推荐列表场景
	ENUM_SCENE_SECOND_COMMON_TAG   = 4 // 娱乐二级通用标签推荐列表场景
)

const (
	ENUM_CHANNEL_SWITCH_FLAG_BITMAP_INIT                     = 0x1
	ENUM_CHANNEL_SWITCH_FLAG_BITMAP_RECOMMEND                = 0x2  // 推荐开关
	ENUM_CHANNEL_SWITCH_FLAG_BITMAP_LOCKSCREEN               = 0x4  // 锁屏开关
	ENUM_CHANNEL_SWITCH_FLAG_BITMAP_AUTO_DISABLE_MIC         = 0x8  // 自动锁麦开关
	ENUM_CHANNEL_SWITCH_FLAG_BITMAP_DISABLE_ATTACHMENT_MSG   = 0x10 // 禁止公屏发图片开关
	ENUM_CHANNEL_SWITCH_FLAG_BITMAP_DISABLE_LEVEL_LMT        = 0x20 // 禁止等级限制的用户在公屏发言开关
	ENUM_CHANNEL_SWITCH_FLAG_BITMAP_OPEN_LIVE_CONNECT_MIC    = 0x40 // 开启直播房连麦功能
	ENUM_CHANNEL_SWITCH_FLAG_BITMAP_OPEN_NORMAL_QUEUE_UP_MIC = 0x80 // 开启普通房排麦功能

	ENUM_CHANNEL_SWITCH_FLAG_BITMAP_MAX = 0x80000000
	ENUM_CHANNEL_SWITCH_FLAG_BITMAP_ALL = 0xFFFFFFFF
)

const (
	NewUserSec = 7 * 24 * 3600 // 新注册用户时间

	StartCnt = 100

	UserEnterChannelMaxCnt = 1
	SmallActChannelMaxCnt  = 2
)

type FillRecChannelInfoParam struct {
	Uid            uint32
	RecInfoList    []*channelRecSvrPb.ChannelRecommendSimpleInfo
	Count          uint32
	IsOfficialVer  bool   // 是否是官频版本
	IsNeedCheckAll bool   // 是否需要检查列表的所有房间
	HomeType       uint32 // see channel_.proto GetRecommonChannelListReq HomeType 首页类型
	SceneType      uint32 // see data.go 场景类型
}
