package manager

import (
	"context"
	"github.com/golang/mock/gomock"
	apicenter "golang.52tt.com/clients/mocks/apicenter/apiserver"
	"golang.52tt.com/clients/mocks/guild"
	"golang.52tt.com/clients/mocks/public"
	userol "golang.52tt.com/clients/mocks/user-online"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/services/channel-recommend/channel-recommend-svr/model"
	"reflect"
	"testing"
	"time"

	"golang.52tt.com/clients/mocks/anchorcontract-go"
	"golang.52tt.com/clients/mocks/channel-live-mgr"
	anchorContractPb "golang.52tt.com/protocol/services/anchorcontract-go"
	pb "golang.52tt.com/protocol/services/channel-recommend-svr"
	liveMgrPb "golang.52tt.com/protocol/services/channellivemgr"
	publicPB "golang.52tt.com/protocol/services/publicsvr"
	olPb "golang.52tt.com/protocol/services/user-online"
	"golang.52tt.com/services/channel-recommend/channel-recommend-svr/mocks"
)

var nowTm = time.Now()
var endTm = nowTm.AddDate(0, 0, 3)

var anchorFlowCard = model.AnchorFlowCard{
	GrantId:      1,
	AnchorUid:    2212797,
	RecLevel:     1,
	ExpireTime:   endTm,
	GuildGrantId: 0,
	TotalCnt:     19,
	UsedCnt:      10,
	BanBeginTs:   0,
	BanEndTs:     0,
	Remark:       "dd",
	UpdateTime:   nowTm,
	CreateTime:   nowTm,
}

var guildFlowCard = model.GuildFlowCard{
	GrantId:    1,
	GuildId:    105467,
	RecLevel:   1,
	ExpireTime: endTm,
	TotalCnt:   10,
	UsedCnt:    1,
	BanBeginTs: 0,
	BanEndTs:   0,
	Remark:     "dd",
	UpdateTime: nowTm,
	CreateTime: nowTm,
}

func Test_transLimitConfListToString(t *testing.T) {
	type args struct {
		confList []*pb.LimitConf
	}
	tests := []struct {
		name    string
		args    args
		want    string
		wantErr bool
	}{
		{
			name: "transLimitConfListToString",
			args: args{confList: []*pb.LimitConf{
				&pb.LimitConf{
					HourCnt: 1,
					ConfList: []*pb.LevelConf{
						&pb.LevelConf{
							Level: 1,
							Cnt:   1,
						},
					},
				},
			}},
			want:    "15,20,40:1,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40",
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := transLimitConfListToString(tt.args.confList)
			if (err != nil) != tt.wantErr {
				t.Errorf("transLimitConfListToString() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("transLimitConfListToString() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_transStringToLimitConfList(t *testing.T) {
	type args struct {
		limitConf string
	}

	res, _ := transStringToLimitConfList("15,20,40:1,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40")

	tests := []struct {
		name    string
		args    args
		want    []*pb.LimitConf
		wantErr bool
	}{
		{
			name:    "transStringToLimitConfList",
			args:    args{limitConf: "15,20,40:1,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40"},
			want:    res,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := transStringToLimitConfList(tt.args.limitConf)
			if (err != nil) != tt.wantErr {
				t.Errorf("transStringToLimitConfList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("transStringToLimitConfList() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestChannelRecommendManager_delFlowCardLimitCache(t *testing.T) {
	type fields struct {
		clientPool *model.SClientPool
	}
	type args struct {
		beginTs uint32
		endTs   uint32
	}

	ctl := gomock.NewController(t)
	defer ctl.Finish()
	mockCache := mocks.NewMockICache(ctl)

	gomock.InOrder(
		mockCache.EXPECT().DelFlowCardLimitConf(gomock.Any()),
	)

	tests := []struct {
		name   string
		fields fields
		args   args
	}{
		{
			name:   "delFlowCardLimitCache",
			fields: fields{clientPool: &model.SClientPool{Cache: mockCache}},
			args: args{
				beginTs: uint32(nowTm.Unix()),
				endTs:   uint32(nowTm.Unix() + 3600),
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &ChannelRecommendManager{
				clientPool: tt.fields.clientPool,
			}
			m.delFlowCardLimitCache(tt.args.beginTs, tt.args.endTs)
		})
	}
}

func TestChannelRecommendManager_AddFlowCardLimitConf(t *testing.T) {
	type fields struct {
		clientPool *model.SClientPool
	}
	type args struct {
		ctx context.Context
		req *pb.AddFlowCardLimitConfReq
	}

	ctl := gomock.NewController(t)
	defer ctl.Finish()
	mockCache := mocks.NewMockICache(ctl)
	mockStore := mocks.NewMockIStore(ctl)
	ctx := context.Background()

	gomock.InOrder(
		mockStore.EXPECT().AddFlowCardLimit(gomock.Any()),
		mockCache.EXPECT().DelFlowCardLimitConf(gomock.Any()),
	)

	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.AddFlowCardLimitConfResp
		wantErr bool
	}{
		{
			name: "AddFlowCardLimitConf",
			fields: fields{clientPool: &model.SClientPool{
				Store: mockStore,
				Cache: mockCache,
			}},
			args: args{
				ctx: ctx,
				req: &pb.AddFlowCardLimitConfReq{
					Conf: &pb.FlowCardLimitConf{
						BeginTs: uint32(nowTm.Unix()),
						EndTs:   uint32(endTm.Unix()),
						ConfList: []*pb.LimitConf{
							&pb.LimitConf{
								HourCnt: 1,
								ConfList: []*pb.LevelConf{
									&pb.LevelConf{
										Level: 1,
										Cnt:   1,
									},
								},
							},
						},
					},
				},
			},
			want:    &pb.AddFlowCardLimitConfResp{},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &ChannelRecommendManager{
				clientPool: tt.fields.clientPool,
			}
			got, err := m.AddFlowCardLimitConf(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("AddFlowCardLimitConf() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("AddFlowCardLimitConf() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestChannelRecommendManager_UpdateFlowCardLimitConf(t *testing.T) {
	type fields struct {
		clientPool *model.SClientPool
	}
	type args struct {
		ctx context.Context
		req *pb.UpdateFlowCardLimitConfReq
	}

	ctl := gomock.NewController(t)
	defer ctl.Finish()
	mockCache := mocks.NewMockICache(ctl)
	mockStore := mocks.NewMockIStore(ctl)
	ctx := context.Background()

	gomock.InOrder(
		mockStore.EXPECT().UpdateFlowCardLimitCnt(gomock.Any(), gomock.Any()),
		mockCache.EXPECT().DelFlowCardLimitConf(gomock.Any()),
	)

	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.UpdateFlowCardLimitConfResp
		wantErr bool
	}{
		{
			name: "UpdateFlowCardLimitConf",
			fields: fields{clientPool: &model.SClientPool{
				Store: mockStore,
				Cache: mockCache,
			}},
			args: args{
				ctx: ctx,
				req: &pb.UpdateFlowCardLimitConfReq{
					Conf: &pb.FlowCardLimitConf{
						Id:      1,
						BeginTs: uint32(nowTm.Unix()),
						EndTs:   uint32(endTm.Unix()),
						ConfList: []*pb.LimitConf{
							&pb.LimitConf{
								HourCnt: 1,
								ConfList: []*pb.LevelConf{
									&pb.LevelConf{
										Level: 1,
										Cnt:   1,
									},
								},
							},
						},
					},
				},
			},
			want:    &pb.UpdateFlowCardLimitConfResp{},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &ChannelRecommendManager{
				clientPool: tt.fields.clientPool,
			}
			got, err := m.UpdateFlowCardLimitConf(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("UpdateFlowCardLimitConf() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("UpdateFlowCardLimitConf() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestChannelRecommendManager_GetFlowCardLimitConfList(t *testing.T) {
	type fields struct {
		clientPool *model.SClientPool
	}
	type args struct {
		ctx context.Context
		req *pb.GetFlowCardLimitConfListReq
	}

	ctl := gomock.NewController(t)
	defer ctl.Finish()
	mockStore := mocks.NewMockIStore(ctl)
	ctx := context.Background()

	gomock.InOrder(
		mockStore.EXPECT().GetFlowCardLimitList(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return([]model.FlowCardLimit{
			model.FlowCardLimit{
				Id:         1,
				LimitConf:  "15,20,40:1,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40",
				BeginTime:  nowTm,
				EndTime:    endTm,
				UpdateTime: nowTm,
				CreateTime: nowTm,
			},
		}, nil),
		mockStore.EXPECT().GetFlowCardLimitTotalCnt(gomock.Any(), gomock.Any()).Return(uint32(1), nil),
	)

	res, _ := transStringToLimitConfList("15,20,40:1,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40")

	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.GetFlowCardLimitConfListResp
		wantErr bool
	}{
		{
			name: "GetFlowCardLimitConfList",
			fields: fields{clientPool: &model.SClientPool{
				Store: mockStore,
			}},
			args: args{
				ctx: ctx,
				req: &pb.GetFlowCardLimitConfListReq{
					Page:     1,
					PageSize: 2,
				},
			},
			want: &pb.GetFlowCardLimitConfListResp{
				ConfList: []*pb.FlowCardLimitConf{
					&pb.FlowCardLimitConf{
						Id:       1,
						BeginTs:  uint32(nowTm.Unix()),
						EndTs:    uint32(endTm.Unix()),
						ConfList: res,
					},
				},
				NextPage:             0,
				TotalCnt:             1,
				XXX_NoUnkeyedLiteral: struct{}{},
				XXX_unrecognized:     nil,
				XXX_sizecache:        0,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &ChannelRecommendManager{
				clientPool: tt.fields.clientPool,
			}
			got, err := m.GetFlowCardLimitConfList(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetFlowCardLimitConfList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetFlowCardLimitConfList() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestChannelRecommendManager_DelFlowCardLimitConf(t *testing.T) {
	type fields struct {
		clientPool *model.SClientPool
	}
	type args struct {
		ctx context.Context
		req *pb.DelFlowCardLimitConfReq
	}

	ctl := gomock.NewController(t)
	defer ctl.Finish()
	mockCache := mocks.NewMockICache(ctl)
	mockStore := mocks.NewMockIStore(ctl)
	ctx := context.Background()

	gomock.InOrder(
		mockStore.EXPECT().GetFlowCardLimitById(gomock.Any()).Return(&model.FlowCardLimit{
			Id:         1,
			LimitConf:  "15,20,40:1,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40",
			BeginTime:  nowTm,
			EndTime:    endTm,
			UpdateTime: nowTm,
			CreateTime: nowTm,
		}, nil),
		mockStore.EXPECT().DelFlowCardLimit(gomock.Any()),
		mockCache.EXPECT().DelFlowCardLimitConf(gomock.Any()),
	)

	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.DelFlowCardLimitConfResp
		wantErr bool
	}{
		{
			name: "DelFlowCardLimitConf",
			fields: fields{clientPool: &model.SClientPool{
				Store: mockStore,
				Cache: mockCache,
			}},
			args: args{
				ctx: ctx,
				req: &pb.DelFlowCardLimitConfReq{
					Id: 1,
				},
			},
			want:    &pb.DelFlowCardLimitConfResp{},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &ChannelRecommendManager{
				clientPool: tt.fields.clientPool,
			}
			got, err := m.DelFlowCardLimitConf(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("DelFlowCardLimitConf() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("DelFlowCardLimitConf() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestChannelRecommendManager_GetGrantFlowCardList(t *testing.T) {
	type fields struct {
		clientPool *model.SClientPool
	}
	type args struct {
		ctx context.Context
		req *pb.GetGrantFlowCardListReq
	}

	ctl := gomock.NewController(t)
	defer ctl.Finish()
	mockStore := mocks.NewMockIStore(ctl)
	ctx := context.Background()

	gomock.InOrder(
		mockStore.EXPECT().GetAnchorFlowCardList(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return([]model.AnchorFlowCard{
			anchorFlowCard,
		}, nil).AnyTimes(),
		mockStore.EXPECT().GetAnchorFlowCardTotalCnt(gomock.Any(), gomock.Any(), gomock.Any()).Return(uint32(1), nil).AnyTimes(),
		mockStore.EXPECT().GetGuildFlowCardList(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return([]model.GuildFlowCard{
			guildFlowCard,
		}, nil).AnyTimes(),
		mockStore.EXPECT().GetGuildFlowCardTotalCnt(gomock.Any(), gomock.Any()).Return(uint32(1), nil).AnyTimes(),
	)

	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.GetGrantFlowCardListResp
		wantErr bool
	}{
		{
			name: "GetGrantFlowCardList",
			fields: fields{clientPool: &model.SClientPool{
				Store: mockStore,
			}},
			args: args{
				ctx: ctx,
				req: &pb.GetGrantFlowCardListReq{
					GrantType: 1,
					Page:      1,
					PageSize:  2,
				},
			},
			want: &pb.GetGrantFlowCardListResp{
				InfoList: []*pb.FlowCardGrantInfo{
					&pb.FlowCardGrantInfo{
						GrantId:      anchorFlowCard.GrantId,
						Id:           anchorFlowCard.AnchorUid,
						Level:        anchorFlowCard.RecLevel,
						ExpirtTs:     uint32(anchorFlowCard.ExpireTime.Unix()),
						Cnt:          anchorFlowCard.TotalCnt,
						Remark:       anchorFlowCard.Remark,
						BanBeginTs:   anchorFlowCard.BanBeginTs,
						BanEndTs:     anchorFlowCard.BanEndTs,
						UsedCnt:      anchorFlowCard.UsedCnt,
						UpdateTs:     uint32(anchorFlowCard.UpdateTime.Unix()),
						CreateTs:     uint32(anchorFlowCard.CreateTime.Unix()),
						GuildGrantId: 0,
					},
				},
				NextPage: 0,
				TotalCnt: 1,
			},
			wantErr: false,
		},
		{
			name: "GetGrantFlowCardList",
			fields: fields{clientPool: &model.SClientPool{
				Store: mockStore,
			}},
			args: args{
				ctx: ctx,
				req: &pb.GetGrantFlowCardListReq{
					GrantType: 0,
					Page:      1,
					PageSize:  2,
				},
			},
			want: &pb.GetGrantFlowCardListResp{
				InfoList: []*pb.FlowCardGrantInfo{
					&pb.FlowCardGrantInfo{
						GrantId:      guildFlowCard.GrantId,
						Id:           guildFlowCard.GuildId,
						Level:        guildFlowCard.RecLevel,
						ExpirtTs:     uint32(guildFlowCard.ExpireTime.Unix()),
						Cnt:          guildFlowCard.TotalCnt,
						Remark:       guildFlowCard.Remark,
						BanBeginTs:   guildFlowCard.BanBeginTs,
						BanEndTs:     guildFlowCard.BanEndTs,
						UsedCnt:      guildFlowCard.UsedCnt,
						UpdateTs:     uint32(guildFlowCard.UpdateTime.Unix()),
						CreateTs:     uint32(guildFlowCard.CreateTime.Unix()),
						GuildGrantId: 0,
					},
				},
				NextPage: 0,
				TotalCnt: 1,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &ChannelRecommendManager{
				clientPool: tt.fields.clientPool,
			}
			got, err := m.GetGrantFlowCardList(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetGrantFlowCardList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetGrantFlowCardList() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestChannelRecommendManager_GrantFlowCard(t *testing.T) {
	type fields struct {
		clientPool *model.SClientPool
	}
	type args struct {
		ctx context.Context
		req *pb.GrantFlowCardReq
	}

	ctl := gomock.NewController(t)
	defer ctl.Finish()
	mockGuild := guild.NewMockIClient(ctl)
	mockPublic := public.NewMockIClient(ctl)
	mockApi := apicenter.NewMockIClient(ctl)
	mockStore := mocks.NewMockIStore(ctl)
	mockOl := userol.NewMockIClient(ctl)
	mockBusinessConf := mocks.NewMockIBusinessConfManager(ctl)
	ctx := context.Background()

	gomock.InOrder(
		mockStore.EXPECT().Transaction(gomock.Any(), gomock.Any()).AnyTimes(),
		mockStore.EXPECT().GrantGuildFlowCard(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes(),
		mockStore.EXPECT().GrantAnchorFlowCard(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes(),
		mockOl.EXPECT().GetMobileOnlineInfo(gomock.Any(), gomock.Any()).Return(&olPb.OnlineInfo{
			Uid:          2212,
			TerminalType: 1114112,
			MarketId:     0,
		}, nil).AnyTimes().AnyTimes(),
		mockBusinessConf.EXPECT().GetImPushConf(gomock.Any(), gomock.Any()).Return(model.ImPushConf{
			WebUrl:      "dd",
			AppName:     "dd",
			AppPlatform: "dd",
		}).AnyTimes(),
		mockGuild.EXPECT().GetGuild(gomock.Any(), gomock.Any()).AnyTimes(),
		mockPublic.EXPECT().GetPublicAccountsByBindedIdList(gomock.Any(), gomock.Any(), gomock.Any()).Return(&publicPB.GetPublicAccountsByBindedIdListResp{
			[]*publicPB.StPublicAccount{
				&publicPB.StPublicAccount{
					PublicId: 1,
					Name:     model.FuWuHaoGuild,
				},
				{
					PublicId: 2,
					Name:     model.FuWuHaoAnchor,
				},
			},
		}, nil).AnyTimes(),
		mockApi.EXPECT().SendImMsg(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes(),
	)

	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.GrantFlowCardResp
		wantErr bool
	}{
		{
			name: "GrantFlowCard",
			fields: fields{clientPool: &model.SClientPool{
				Store:     mockStore,
				PublicCli: mockPublic,
				ApiCli:    mockApi,
				GuildCli:  mockGuild,
			}},
			args: args{
				ctx: ctx,
				req: &pb.GrantFlowCardReq{
					GrantType: 0,
					Info: &pb.FlowCardGrantInfo{
						Id:       105467,
						Level:    1,
						ExpirtTs: uint32(endTm.Unix()),
						Cnt:      10,
					},
				},
			},
			want:    &pb.GrantFlowCardResp{},
			wantErr: false,
		},
		{
			name: "GrantFlowCard2",
			fields: fields{clientPool: &model.SClientPool{
				Store:           mockStore,
				PublicCli:       mockPublic,
				ApiCli:          mockApi,
				GuildCli:        mockGuild,
				UserOlCli:       mockOl,
				BusinessConfMgr: mockBusinessConf,
			}},
			args: args{
				ctx: ctx,
				req: &pb.GrantFlowCardReq{
					GrantType: 1,
					Info: &pb.FlowCardGrantInfo{
						Id:       2212797,
						Level:    1,
						ExpirtTs: uint32(endTm.Unix()),
						Cnt:      10,
					},
				},
			},
			want:    &pb.GrantFlowCardResp{},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &ChannelRecommendManager{
				clientPool: tt.fields.clientPool,
			}
			got, err := m.GrantFlowCard(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GrantFlowCard() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GrantFlowCard() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestChannelRecommendManager_BatGrantFlowCard(t *testing.T) {
	type fields struct {
		clientPool *model.SClientPool
	}
	type args struct {
		ctx context.Context
		req *pb.BatGrantFlowCardReq
	}

	ctl := gomock.NewController(t)
	defer ctl.Finish()
	mockGuild := guild.NewMockIClient(ctl)
	mockPublic := public.NewMockIClient(ctl)
	mockApi := apicenter.NewMockIClient(ctl)
	mockStore := mocks.NewMockIStore(ctl)
	ctx := context.Background()

	gomock.InOrder(
		mockStore.EXPECT().Transaction(gomock.Any(), gomock.Any()),
		mockGuild.EXPECT().GetGuild(gomock.Any(), gomock.Any()),
		mockPublic.EXPECT().GetPublicAccountsByBindedIdList(gomock.Any(), gomock.Any(), gomock.Any()).Return(&publicPB.GetPublicAccountsByBindedIdListResp{
			[]*publicPB.StPublicAccount{
				&publicPB.StPublicAccount{
					PublicId: 1,
					Name:     model.FuWuHaoGuild,
				},
			},
		}, nil),
		mockApi.EXPECT().SendImMsg(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()),
	)

	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.BatGrantFlowCardResp
		wantErr bool
	}{
		{
			name: "BatGrantFlowCard",
			fields: fields{clientPool: &model.SClientPool{
				Store:     mockStore,
				PublicCli: mockPublic,
				ApiCli:    mockApi,
				GuildCli:  mockGuild,
			}},
			args: args{
				ctx: ctx,
				req: &pb.BatGrantFlowCardReq{
					GrantType: 0,
					InfoList: []*pb.FlowCardGrantInfo{
						&pb.FlowCardGrantInfo{
							Id:       123,
							Level:    1,
							ExpirtTs: uint32(endTm.Unix()),
							Cnt:      10,
						},
					},
				},
			},
			want:    &pb.BatGrantFlowCardResp{},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &ChannelRecommendManager{
				clientPool: tt.fields.clientPool,
			}
			got, err := m.BatGrantFlowCard(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("BatGrantFlowCard() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("BatGrantFlowCard() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestChannelRecommendManager_ReclaimGrantedFlowCard(t *testing.T) {
	type fields struct {
		clientPool *model.SClientPool
	}
	type args struct {
		ctx context.Context
		req *pb.ReclaimGrantedFlowCardReq
	}

	ctl := gomock.NewController(t)
	defer ctl.Finish()
	mockStore := mocks.NewMockIStore(ctl)
	ctx := context.Background()

	gomock.InOrder(
		mockStore.EXPECT().ReclaimGuildFlowCardCnt(gomock.Any(), gomock.Any()),
		mockStore.EXPECT().ReclaimAnchorFlowCardCnt(gomock.Any(), gomock.Any()),
	)

	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.ReclaimGrantedFlowCardResp
		wantErr bool
	}{
		{
			name: "ReclaimGrantedFlowCard",
			fields: fields{clientPool: &model.SClientPool{
				Store: mockStore,
			}},
			args: args{
				ctx: ctx,
				req: &pb.ReclaimGrantedFlowCardReq{
					GrantType:  0,
					GrantId:    1,
					ReclaimCnt: 1,
				},
			},
			want:    &pb.ReclaimGrantedFlowCardResp{},
			wantErr: false,
		},
		{
			name: "ReclaimGrantedFlowCard",
			fields: fields{clientPool: &model.SClientPool{
				Store: mockStore,
			}},
			args: args{
				ctx: ctx,
				req: &pb.ReclaimGrantedFlowCardReq{
					GrantType:  1,
					GrantId:    1,
					ReclaimCnt: 1,
				},
			},
			want:    &pb.ReclaimGrantedFlowCardResp{},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &ChannelRecommendManager{
				clientPool: tt.fields.clientPool,
			}
			got, err := m.ReclaimGrantedFlowCard(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("ReclaimGrantedFlowCard() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("ReclaimGrantedFlowCard() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestChannelRecommendManager_BanGrantedFlowCard(t *testing.T) {
	type fields struct {
		clientPool *model.SClientPool
	}
	type args struct {
		ctx context.Context
		req *pb.BanGrantedFlowCardReq
	}

	ctl := gomock.NewController(t)
	defer ctl.Finish()
	mockStore := mocks.NewMockIStore(ctl)
	ctx := context.Background()

	gomock.InOrder(
		mockStore.EXPECT().BanGuildFlowCard(gomock.Any(), gomock.Any(), gomock.Any()),
		mockStore.EXPECT().BanAnchorFlowCard(gomock.Any(), gomock.Any(), gomock.Any()),
	)

	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.BanGrantedFlowCardResp
		wantErr bool
	}{
		{
			name:   "BanGrantedFlowCard",
			fields: fields{clientPool: &model.SClientPool{Store: mockStore}},
			args: args{
				ctx: ctx,
				req: &pb.BanGrantedFlowCardReq{
					GrantType: 0,
					GrantId:   1,
					BeginTs:   uint32(nowTm.Unix()),
					EndTs:     uint32(endTm.Unix()),
				},
			},
			want:    &pb.BanGrantedFlowCardResp{},
			wantErr: false,
		},
		{
			name:   "BanGrantedFlowCard",
			fields: fields{clientPool: &model.SClientPool{Store: mockStore}},
			args: args{
				ctx: ctx,
				req: &pb.BanGrantedFlowCardReq{
					GrantType: 1,
					GrantId:   2,
					BeginTs:   uint32(nowTm.Unix()),
					EndTs:     uint32(endTm.Unix()),
				},
			},
			want:    &pb.BanGrantedFlowCardResp{},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &ChannelRecommendManager{
				clientPool: tt.fields.clientPool,
			}
			got, err := m.BanGrantedFlowCard(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("BanGrantedFlowCard() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("BanGrantedFlowCard() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestChannelRecommendManager_GrantAnchorFlowCardByGuild(t *testing.T) {
	type fields struct {
		clientPool *model.SClientPool
	}
	type args struct {
		ctx context.Context
		req *pb.GrantAnchorFlowCardByGuildReq
	}

	ctl := gomock.NewController(t)
	defer ctl.Finish()
	mockStore := mocks.NewMockIStore(ctl)
	mockOl := userol.NewMockIClient(ctl)
	ctx := context.Background()

	gomock.InOrder(
		mockStore.EXPECT().GetGuildFlowCardById(gomock.Any()).Return(&guildFlowCard, nil),
		mockStore.EXPECT().Transaction(gomock.Any(), gomock.Any()),
		mockOl.EXPECT().GetMobileOnlineInfo(gomock.Any(), gomock.Any()).Return(&olPb.OnlineInfo{
			Uid:          2212,
			TerminalType: 1114112,
			MarketId:     0,
		}, nil).AnyTimes().AnyTimes(),
	)

	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.GrantAnchorFlowCardByGuildResp
		wantErr bool
	}{
		{
			name:   "GrantAnchorFlowCardByGuild",
			fields: fields{clientPool: &model.SClientPool{Store: mockStore, UserOlCli: mockOl}},
			args: args{
				ctx: ctx,
				req: &pb.GrantAnchorFlowCardByGuildReq{
					GuildId: guildFlowCard.GuildId,
					GrantId: guildFlowCard.GrantId,
					InfoList: []*pb.FlowCardGrantInfo{
						&pb.FlowCardGrantInfo{
							Id:       2212797,
							ExpirtTs: uint32(endTm.Unix()),
						},
					},
				},
			},
			want:    &pb.GrantAnchorFlowCardByGuildResp{},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &ChannelRecommendManager{
				clientPool: tt.fields.clientPool,
			}
			got, err := m.GrantAnchorFlowCardByGuild(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GrantAnchorFlowCardByGuild() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GrantAnchorFlowCardByGuild() got = %v, want %v", got, tt.want)
			}
		})
	}

}

/*
func TestChannelRecommendManager_getHourLimitCnt(t *testing.T) {
	type fields struct {
		clientPool *model.SClientPool
	}
	type args struct {
		level      uint32
		dayHourCnt uint32
		limitConf  *pb.LimitConf
	}

	tests := []struct {
		name   string
		fields fields
		args   args
		want   uint32
	}{
		{
			name:   "getHourLimitCnt",
			fields: fields{},
			args: args{
				level:      1,
				dayHourCnt: 1,
				limitConf: &pb.LimitConf{
					HourCnt: 1,
					ConfList: []*pb.LevelConf{
						&pb.LevelConf{
							Level: 1,
							Cnt:   1,
						},
					},
				},
			},
			want: 1,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &ChannelRecommendManager{
				clientPool: tt.fields.clientPool,
			}
			if got := m.getHourLimitCnt(tt.args.level, tt.args.dayHourCnt, tt.args.limitConf); got != tt.want {
				t.Errorf("getHourLimitCnt() = %v, want %v", got, tt.want)
			}
		})
	}
}
*/

func TestChannelRecommendManager_getFlowCardHourLimitCnt(t *testing.T) {
	type fields struct {
		clientPool *model.SClientPool
	}
	type args struct {
		level uint32
		ts    uint32
	}

	ctl := gomock.NewController(t)
	defer ctl.Finish()
	mockStore := mocks.NewMockIStore(ctl)
	mockCache := mocks.NewMockICache(ctl)

	gomock.InOrder(
		mockCache.EXPECT().GetFlowCardLimitConf(gomock.Any()).Return("", nil),
		mockStore.EXPECT().GetFlowCardLimitListByTs(gomock.Any()).Return([]model.FlowCardLimit{
			model.FlowCardLimit{
				Id:         1,
				LimitConf:  "15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40",
				BeginTime:  nowTm,
				EndTime:    endTm,
				UpdateTime: nowTm,
				CreateTime: nowTm,
			},
		}, nil),
		mockCache.EXPECT().SetFlowCardLimitConf(gomock.Any(), gomock.Any()),
	)

	tests := []struct {
		name    string
		fields  fields
		args    args
		want    uint32
		wantErr bool
	}{
		{
			name: "getFlowCardHourLimitCnt",
			fields: fields{clientPool: &model.SClientPool{
				Store: mockStore,
				Cache: mockCache,
			}},
			args: args{
				level: 1,
				ts:    uint32(nowTm.Unix()),
			},
			want:    15,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &ChannelRecommendManager{
				clientPool: tt.fields.clientPool,
			}
			got, err := m.getFlowCardHourLimitCnt(tt.args.level, tt.args.ts)
			if (err != nil) != tt.wantErr {
				t.Errorf("getFlowCardHourLimitCnt() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("getFlowCardHourLimitCnt() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestChannelRecommendManager_GetFlowCardHourRemainCnt(t *testing.T) {
	type fields struct {
		clientPool *model.SClientPool
	}
	type args struct {
		ctx context.Context
		req *pb.GetFlowCardHourRemainCntReq
	}

	ctl := gomock.NewController(t)
	defer ctl.Finish()
	mockStore := mocks.NewMockIStore(ctl)
	mockCache := mocks.NewMockICache(ctl)
	ctx := context.Background()

	gomock.InOrder(
		mockCache.EXPECT().GetFlowCardHourUseCnt(gomock.Any(), gomock.Any()).Return(uint32(1), nil),
		mockCache.EXPECT().GetFlowCardLimitConf(gomock.Any()).Return("", nil),
		mockStore.EXPECT().GetFlowCardLimitListByTs(gomock.Any()).Return([]model.FlowCardLimit{
			model.FlowCardLimit{
				Id:         1,
				LimitConf:  "15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40",
				BeginTime:  nowTm,
				EndTime:    endTm,
				UpdateTime: nowTm,
				CreateTime: nowTm,
			},
		}, nil),
		mockCache.EXPECT().SetFlowCardLimitConf(gomock.Any(), gomock.Any()),
	)

	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.GetFlowCardHourRemainCntResp
		wantErr bool
	}{
		{
			name: "GetFlowCardHourRemainCnt",
			fields: fields{clientPool: &model.SClientPool{
				Store: mockStore,
				Cache: mockCache,
			}},
			args: args{
				ctx: ctx,
				req: &pb.GetFlowCardHourRemainCntReq{
					Ts:    uint32(nowTm.Unix()),
					Level: 1,
				},
			},
			want:    &pb.GetFlowCardHourRemainCntResp{RemainCnt: 14},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &ChannelRecommendManager{
				clientPool: tt.fields.clientPool,
			}
			got, err := m.GetFlowCardHourRemainCnt(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetFlowCardHourRemainCnt() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetFlowCardHourRemainCnt() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestChannelRecommendManager_checkFlowCardTimeLimit(t *testing.T) {
	type fields struct {
		clientPool *model.SClientPool
	}
	type args struct {
		anchorUid  uint32
		beginTs    uint32
		expireTs   uint32
		banBeginTs uint32
		banEndTs   uint32
		nowTm      time.Time
	}

	ctl := gomock.NewController(t)
	defer ctl.Finish()
	mockCache := mocks.NewMockICache(ctl)

	gomock.InOrder(
		mockCache.EXPECT().IncrUseFlowCardToastFlag(gomock.Any(), gomock.Any(), gomock.Any()).Return(uint32(2), nil),
	)

	var ts uint32 = 1663843663
	tests := []struct {
		name   string
		fields fields
		args   args
		want   protocol.ServerError
	}{
		{
			name:   "checkFlowCardTimeLimit",
			fields: fields{clientPool: &model.SClientPool{Cache: mockCache}},
			args: args{
				anchorUid:  2212797,
				beginTs:    ts,
				expireTs:   ts + 1000,
				banBeginTs: 0,
				banEndTs:   0,
				nowTm:      time.Unix(int64(ts), 0),
			},
			want: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &ChannelRecommendManager{
				clientPool: tt.fields.clientPool,
			}
			if got := m.checkFlowCardTimeLimit(tt.args.anchorUid, tt.args.beginTs, tt.args.expireTs, tt.args.banBeginTs, tt.args.banEndTs, tt.args.nowTm); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("checkFlowCardTimeLimit() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestChannelRecommendManager_reclaimUseCnt(t *testing.T) {
	type fields struct {
		clientPool *model.SClientPool
	}
	type args struct {
		anchorUid   uint32
		guildId     uint32
		recLevel    uint32
		beginTs     uint32
		reclaimType int
	}

	ctl := gomock.NewController(t)
	defer ctl.Finish()
	mockCache := mocks.NewMockICache(ctl)

	gomock.InOrder(
		mockCache.EXPECT().IncrFlowCardHourUseCnt(gomock.Any(), gomock.Any(), gomock.Any()),
		mockCache.EXPECT().IncrAnchorFlowCardHourUseCnt(gomock.Any(), gomock.Any(), gomock.Any()),
		mockCache.EXPECT().IncrAnchorFlowCardDayUseCnt(gomock.Any(), gomock.Any(), gomock.Any()),
		mockCache.EXPECT().IncrGuildFlowCardDayUseCnt(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()),
	)

	tests := []struct {
		name   string
		fields fields
		args   args
	}{
		{
			name:   "reclaimUseCnt",
			fields: fields{clientPool: &model.SClientPool{Cache: mockCache}},
			args: args{
				anchorUid:   2212797,
				guildId:     22,
				recLevel:    1,
				beginTs:     uint32(nowTm.Unix()),
				reclaimType: ReclaimUseCntTypeCommon + ReclaimUseHourCntTypeAnchor + ReclaimUseDayCntTypeAnchor + ReclaimUseCntTypeGuild,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &ChannelRecommendManager{
				clientPool: tt.fields.clientPool,
			}
			m.reclaimUseCnt(tt.args.anchorUid, tt.args.guildId, tt.args.recLevel, tt.args.beginTs, tt.args.reclaimType)
		})
	}
}

func TestChannelRecommendManager_GetAllUseFlowCardAnchor(t *testing.T) {
	type fields struct {
		clientPool *model.SClientPool
	}
	type args struct {
		ctx context.Context
		req *pb.GetAllUseFlowCardAnchorReq
	}

	ctl := gomock.NewController(t)
	defer ctl.Finish()
	mockCache := mocks.NewMockICache(ctl)
	ctx := context.Background()

	gomock.InOrder(
		mockCache.EXPECT().GetAllUseFlowCardChByTs(gomock.Any()).Return(map[uint32]uint32{
			1: 1,
		}, nil),
	)

	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.GetAllUseFlowCardAnchorResp
		wantErr bool
	}{
		{
			name:   "GetAllUseFlowCardAnchor",
			fields: fields{clientPool: &model.SClientPool{Cache: mockCache}},
			args: args{
				ctx: ctx,
				req: &pb.GetAllUseFlowCardAnchorReq{
					Ts: uint32(nowTm.Unix()),
				},
			},
			want:    &pb.GetAllUseFlowCardAnchorResp{MapCidLv: map[uint32]uint32{1: 1}},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &ChannelRecommendManager{
				clientPool: tt.fields.clientPool,
			}
			got, err := m.GetAllUseFlowCardAnchor(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetAllUseFlowCardAnchor() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetAllUseFlowCardAnchor() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestChannelRecommendManager_checkFlowCardUseLimit(t *testing.T) {
	type fields struct {
		clientPool *model.SClientPool
	}
	type args struct {
		ctx        context.Context
		checkParam CheckParam
	}

	ctl := gomock.NewController(t)
	defer ctl.Finish()
	mockCache := mocks.NewMockICache(ctl)
	mockAnchor := anchorcontract_go.NewMockIClient(ctl)
	ctx := context.Background()

	limitConf := "15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40"
	gomock.InOrder(
		mockCache.EXPECT().IncrUseFlowCardToastFlag(gomock.Any(), gomock.Any(), gomock.Any()).Return(uint32(2), nil).AnyTimes(),
		mockCache.EXPECT().IncrFlowCardHourUseCnt(gomock.Any(), gomock.Any(), gomock.Any()).Return(uint32(1), nil),
		mockCache.EXPECT().GetFlowCardLimitConf(gomock.Any()).Return(limitConf, nil),
		mockCache.EXPECT().IncrAnchorFlowCardHourUseCnt(gomock.Any(), gomock.Any(), gomock.Any()).Return(uint32(1), nil),
		mockCache.EXPECT().IncrAnchorFlowCardDayUseCnt(gomock.Any(), gomock.Any(), gomock.Any()).Return(uint32(1), nil),
		mockAnchor.EXPECT().GetUserContractCacheInfo(gomock.Any(), gomock.Any(), gomock.Any()).Return(&anchorContractPb.ContractCacheInfo{
			Contract: &anchorContractPb.ContractInfo{
				GuildId:    105467,
				ExpireTime: uint32(endTm.Unix()),
			},
			AnchorIdentityList: []uint32{0, 1},
		}, nil),
		mockCache.EXPECT().IncrGuildFlowCardDayUseCnt(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(uint32(1), nil),
		mockCache.EXPECT().GetGuildFlowCardUseLimit(gomock.Any(), gomock.Any()).Return(uint32(15), nil),
	)

	tests := []struct {
		name   string
		fields fields
		args   args
		want   uint32
		want1  protocol.ServerError
	}{
		{
			name: "checkFlowCardUseLimit",
			fields: fields{clientPool: &model.SClientPool{
				Cache:             mockCache,
				AnchorContractCli: mockAnchor,
			}},
			args: args{
				ctx: ctx,
				checkParam: CheckParam{
					anchorUid:  2212797,
					guildId:    0,
					recLevel:   1,
					beginTs:    uint32(nowTm.Unix()),
					expireTs:   uint32(endTm.Unix()),
					banBeginTs: 0,
					banEndTs:   0,
				},
			},
			want:  105467,
			want1: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &ChannelRecommendManager{
				clientPool: tt.fields.clientPool,
			}
			got, got1 := m.checkFlowCardUseLimit(tt.args.ctx, tt.args.checkParam)
			if got != tt.want {
				t.Errorf("checkFlowCardUseLimit() got = %v, want %v", got, tt.want)
			}
			if !reflect.DeepEqual(got1, tt.want1) {
				t.Errorf("checkFlowCardUseLimit() got1 = %v, want %v", got1, tt.want1)
			}
		})
	}
}

func TestChannelRecommendManager_effectAnchorFlowCardLv(t *testing.T) {
	type fields struct {
		clientPool *model.SClientPool
	}
	type args struct {
		ctx       context.Context
		anchorUid uint32
		recLevel  uint32
		ts        uint32
	}

	ctl := gomock.NewController(t)
	defer ctl.Finish()
	mockCache := mocks.NewMockICache(ctl)
	mockLiveMgr := channellivemgr.NewMockIClient(ctl)
	ctx := context.Background()

	gomock.InOrder(
		mockLiveMgr.EXPECT().GetChannelLiveInfo(gomock.Any(), gomock.Any(), gomock.Any()).Return(&liveMgrPb.GetChannelLiveInfoResp{
			ChannelLiveInfo: &liveMgrPb.ChannelLiveInfo{ChannelId: 123},
		}, nil),
		mockCache.EXPECT().SetChannelFlowCardRecommendLv(gomock.Any(), gomock.Any(), gomock.Any()),
	)

	tests := []struct {
		name   string
		fields fields
		args   args
		want   protocol.ServerError
	}{
		{
			name: "effectAnchorFlowCardLv",
			fields: fields{clientPool: &model.SClientPool{
				Cache:      mockCache,
				LiveMgrCli: mockLiveMgr,
			}},
			args: args{
				ctx:       ctx,
				anchorUid: 2212797,
				recLevel:  1,
				ts:        uint32(nowTm.Unix()),
			},
			want: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &ChannelRecommendManager{
				clientPool: tt.fields.clientPool,
			}
			if got := m.effectAnchorFlowCardLv(tt.args.ctx, tt.args.anchorUid, tt.args.recLevel, tt.args.ts); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("effectAnchorFlowCardLv() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestChannelRecommendManager_guildUseFlowCard(t *testing.T) {
	type fields struct {
		clientPool *model.SClientPool
	}
	type args struct {
		ctx context.Context
		req *pb.UseFlowCardReq
	}

	ctl := gomock.NewController(t)
	defer ctl.Finish()
	mockCache := mocks.NewMockICache(ctl)
	mockStore := mocks.NewMockIStore(ctl)
	mockAnchor := anchorcontract_go.NewMockIClient(ctl)
	ctx := context.Background()

	limitConf := "15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40"
	gomock.InOrder(
		mockStore.EXPECT().GetGuildFlowCardById(gomock.Any()).Return(&model.GuildFlowCard{
			GrantId:    1,
			GuildId:    123,
			RecLevel:   1,
			ExpireTime: endTm,
			TotalCnt:   10,
			UsedCnt:    1,
		}, nil),
		mockCache.EXPECT().IncrUseFlowCardToastFlag(gomock.Any(), gomock.Any(), gomock.Any()).Return(uint32(2), nil).AnyTimes(),
		mockCache.EXPECT().IncrFlowCardHourUseCnt(gomock.Any(), gomock.Any(), gomock.Any()).Return(uint32(1), nil),
		mockCache.EXPECT().GetFlowCardLimitConf(gomock.Any()).Return(limitConf, nil),
		mockCache.EXPECT().IncrAnchorFlowCardHourUseCnt(gomock.Any(), gomock.Any(), gomock.Any()).Return(uint32(1), nil),
		mockCache.EXPECT().IncrAnchorFlowCardDayUseCnt(gomock.Any(), gomock.Any(), gomock.Any()).Return(uint32(1), nil),
		mockCache.EXPECT().IncrGuildFlowCardDayUseCnt(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(uint32(1), nil),
		mockCache.EXPECT().GetGuildFlowCardUseLimit(gomock.Any(), gomock.Any()).Return(uint32(15), nil),
		mockStore.EXPECT().Transaction(gomock.Any(), gomock.Any()),
	)

	tests := []struct {
		name    string
		fields  fields
		args    args
		want    uint32
		wantErr bool
	}{
		{
			name: "guildUseFlowCard",
			fields: fields{clientPool: &model.SClientPool{
				Store:             mockStore,
				Cache:             mockCache,
				AnchorContractCli: mockAnchor,
			}},
			args: args{
				ctx: ctx,
				req: &pb.UseFlowCardReq{
					GrantId:   1,
					AnchorUid: 2212797,
					BeginTs:   uint32(nowTm.Unix()),
					GuildId:   123,
				},
			},
			want:    1,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &ChannelRecommendManager{
				clientPool: tt.fields.clientPool,
			}
			got, err := m.guildUseFlowCard(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("guildUseFlowCard() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("guildUseFlowCard() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestChannelRecommendManager_anchorUseFlowCard(t *testing.T) {
	type fields struct {
		clientPool *model.SClientPool
	}
	type args struct {
		ctx context.Context
		req *pb.UseFlowCardReq
	}

	ctl := gomock.NewController(t)
	defer ctl.Finish()
	mockCache := mocks.NewMockICache(ctl)
	mockStore := mocks.NewMockIStore(ctl)
	mockAnchor := anchorcontract_go.NewMockIClient(ctl)
	ctx := context.Background()

	limitConf := "15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40"
	gomock.InOrder(
		mockStore.EXPECT().GetAnchorFlowCardById(gomock.Any()).Return(&model.AnchorFlowCard{
			GrantId:      1,
			AnchorUid:    2212797,
			RecLevel:     1,
			ExpireTime:   endTm,
			GuildGrantId: 0,
			TotalCnt:     10,
			UsedCnt:      1,
		}, nil),
		mockCache.EXPECT().IncrUseFlowCardToastFlag(gomock.Any(), gomock.Any(), gomock.Any()).Return(uint32(2), nil).AnyTimes(),
		mockCache.EXPECT().IncrFlowCardHourUseCnt(gomock.Any(), gomock.Any(), gomock.Any()).Return(uint32(1), nil),
		mockCache.EXPECT().GetFlowCardLimitConf(gomock.Any()).Return(limitConf, nil),
		mockCache.EXPECT().IncrAnchorFlowCardHourUseCnt(gomock.Any(), gomock.Any(), gomock.Any()).Return(uint32(1), nil),
		mockCache.EXPECT().IncrAnchorFlowCardDayUseCnt(gomock.Any(), gomock.Any(), gomock.Any()).Return(uint32(1), nil),
		mockAnchor.EXPECT().GetUserContractCacheInfo(gomock.Any(), gomock.Any(), gomock.Any()).Return(&anchorContractPb.ContractCacheInfo{
			Contract: &anchorContractPb.ContractInfo{
				GuildId:    105467,
				ExpireTime: uint32(endTm.Unix()),
			},
			AnchorIdentityList: []uint32{0, 1},
		}, nil),
		mockCache.EXPECT().IncrGuildFlowCardDayUseCnt(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(uint32(1), nil),
		mockCache.EXPECT().GetGuildFlowCardUseLimit(gomock.Any(), gomock.Any()).Return(uint32(15), nil),
		mockStore.EXPECT().Transaction(gomock.Any(), gomock.Any()),
	)

	tests := []struct {
		name    string
		fields  fields
		args    args
		want    uint32
		wantErr bool
	}{
		{
			name: "anchorUseFlowCard",
			fields: fields{clientPool: &model.SClientPool{
				Store:             mockStore,
				Cache:             mockCache,
				AnchorContractCli: mockAnchor,
			}},
			args: args{
				ctx: ctx,
				req: &pb.UseFlowCardReq{
					GrantId:   1,
					AnchorUid: 2212797,
					BeginTs:   uint32(nowTm.Unix()),
					GuildId:   0,
				},
			},
			want:    1,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &ChannelRecommendManager{
				clientPool: tt.fields.clientPool,
			}
			got, err := m.anchorUseFlowCard(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("anchorUseFlowCard() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("anchorUseFlowCard() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestChannelRecommendManager_UseFlowCard(t *testing.T) {
	type fields struct {
		clientPool *model.SClientPool
	}
	type args struct {
		ctx context.Context
		req *pb.UseFlowCardReq
	}

	ctl := gomock.NewController(t)
	defer ctl.Finish()
	mockCache := mocks.NewMockICache(ctl)
	mockStore := mocks.NewMockIStore(ctl)
	mockAnchor := anchorcontract_go.NewMockIClient(ctl)
	mockPublic := public.NewMockIClient(ctl)
	mockApi := apicenter.NewMockIClient(ctl)
	ctx := context.Background()

	limitConf := "15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40:15,20,40"
	gomock.InOrder(
		mockStore.EXPECT().GetAnchorFlowCardById(gomock.Any()).Return(&model.AnchorFlowCard{
			GrantId:      1,
			AnchorUid:    2212797,
			RecLevel:     1,
			ExpireTime:   endTm,
			GuildGrantId: 0,
			TotalCnt:     10,
			UsedCnt:      1,
		}, nil),
		mockCache.EXPECT().IncrUseFlowCardToastFlag(gomock.Any(), gomock.Any(), gomock.Any()).Return(uint32(2), nil).AnyTimes(),
		mockCache.EXPECT().IncrFlowCardHourUseCnt(gomock.Any(), gomock.Any(), gomock.Any()).Return(uint32(1), nil),
		mockCache.EXPECT().GetFlowCardLimitConf(gomock.Any()).Return(limitConf, nil),
		mockCache.EXPECT().IncrAnchorFlowCardHourUseCnt(gomock.Any(), gomock.Any(), gomock.Any()).Return(uint32(1), nil),
		mockCache.EXPECT().IncrAnchorFlowCardDayUseCnt(gomock.Any(), gomock.Any(), gomock.Any()).Return(uint32(1), nil),
		mockAnchor.EXPECT().GetUserContractCacheInfo(gomock.Any(), gomock.Any(), gomock.Any()).Return(&anchorContractPb.ContractCacheInfo{
			Contract: &anchorContractPb.ContractInfo{
				GuildId:    105467,
				ExpireTime: uint32(endTm.Unix()),
			},
			AnchorIdentityList: []uint32{0, 1},
		}, nil),
		mockCache.EXPECT().IncrGuildFlowCardDayUseCnt(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(uint32(1), nil),
		mockCache.EXPECT().GetGuildFlowCardUseLimit(gomock.Any(), gomock.Any()).Return(uint32(15), nil),
		mockStore.EXPECT().Transaction(gomock.Any(), gomock.Any()),
		mockPublic.EXPECT().GetPublicAccountsByBindedIdList(gomock.Any(), gomock.Any(), gomock.Any()).Return(&publicPB.GetPublicAccountsByBindedIdListResp{
			[]*publicPB.StPublicAccount{
				&publicPB.StPublicAccount{
					PublicId: 1,
					Name:     model.FuWuHaoAnchor,
				},
			},
		}, nil),
		mockApi.EXPECT().SendImMsg(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()),
	)

	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.UseFlowCardResp
		wantErr bool
	}{
		{
			name: "UseFlowCard",
			fields: fields{clientPool: &model.SClientPool{
				Store:             mockStore,
				Cache:             mockCache,
				AnchorContractCli: mockAnchor,
				PublicCli:         mockPublic,
				ApiCli:            mockApi,
			}},
			args: args{
				ctx: ctx,
				req: &pb.UseFlowCardReq{
					GrantId:   1,
					AnchorUid: 2212797,
					BeginTs:   uint32(nowTm.Unix()),
				},
			},
			want:    &pb.UseFlowCardResp{},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &ChannelRecommendManager{
				clientPool: tt.fields.clientPool,
			}
			got, err := m.UseFlowCard(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("UseFlowCard() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("UseFlowCard() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestChannelRecommendManager_GetFlowCardListByType(t *testing.T) {
	type fields struct {
		clientPool *model.SClientPool
	}
	type args struct {
		ctx context.Context
		req *pb.GetFlowCardListByTypeReq
	}

	ctl := gomock.NewController(t)
	defer ctl.Finish()
	mockStore := mocks.NewMockIStore(ctl)
	ctx := context.Background()

	gomock.InOrder(
		mockStore.EXPECT().GetAnchorFlowCardListByType(gomock.Any(), gomock.Any(), gomock.Any()).Return([]model.AnchorFlowCard{
			anchorFlowCard,
		}, nil),
	)

	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.GetFlowCardListByTypeResp
		wantErr bool
	}{
		{
			name: "GetFlowCardListByType",
			fields: fields{clientPool: &model.SClientPool{
				Store: mockStore,
			}},
			args: args{
				ctx: ctx,
				req: &pb.GetFlowCardListByTypeReq{
					GrantType: 1,
					Type:      0,
					Level:     0,
					Id:        2212797,
				},
			},
			want: &pb.GetFlowCardListByTypeResp{
				InfoList: []*pb.FlowCardGrantInfo{
					&pb.FlowCardGrantInfo{
						GrantId:      anchorFlowCard.GrantId,
						Id:           anchorFlowCard.AnchorUid,
						Level:        anchorFlowCard.RecLevel,
						ExpirtTs:     uint32(anchorFlowCard.ExpireTime.Unix()),
						Cnt:          anchorFlowCard.TotalCnt,
						Remark:       anchorFlowCard.Remark,
						BanBeginTs:   anchorFlowCard.BanBeginTs,
						BanEndTs:     anchorFlowCard.BanEndTs,
						UsedCnt:      anchorFlowCard.UsedCnt,
						UpdateTs:     uint32(anchorFlowCard.UpdateTime.Unix()),
						CreateTs:     uint32(anchorFlowCard.CreateTime.Unix()),
						GuildGrantId: 0,
					},
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &ChannelRecommendManager{
				clientPool: tt.fields.clientPool,
			}
			got, err := m.GetFlowCardListByType(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetFlowCardListByType() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetFlowCardListByType() got = %v, want %v", got, tt.want)
			}
		})
	}
}
