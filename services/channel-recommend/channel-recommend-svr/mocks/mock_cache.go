// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/services/channel-recommend/channel-recommend-svr/model (interfaces: ICache)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"
	time "time"

	gomock "github.com/golang/mock/gomock"
	entertainmentRecommendBack "golang.52tt.com/protocol/services/entertainmentRecommendBackSvr"
	model "golang.52tt.com/services/channel-recommend/channel-recommend-svr/model"
)

// MockICache is a mock of ICache interface.
type MockICache struct {
	ctrl     *gomock.Controller
	recorder *MockICacheMockRecorder
}

func (m *MockICache) GetUserFeedBackList(ctx context.Context, uid uint32) (map[uint32]uint32, error) {
	//TODO implement me
	panic("implement me")
}

func (m *MockICache) AddUserFeedBack(ctx context.Context, uid, cid, ts, expireTs uint32) error {
	//TODO implement me
	panic("implement me")
}

func (m *MockICache) GetTagLevelChannelList(userCategory, tagId, level, start, stop uint32) ([]uint32, error) {
	//TODO implement me
	panic("implement me")
}

// MockICacheMockRecorder is the mock recorder for MockICache.
type MockICacheMockRecorder struct {
	mock *MockICache
}

// NewMockICache creates a new mock instance.
func NewMockICache(ctrl *gomock.Controller) *MockICache {
	mock := &MockICache{ctrl: ctrl}
	mock.recorder = &MockICacheMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockICache) EXPECT() *MockICacheMockRecorder {
	return m.recorder
}

// AddChLotteryInfo mocks base method.
func (m *MockICache) AddChLotteryInfo(arg0 *model.ChLotteryInfo) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddChLotteryInfo", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddChLotteryInfo indicates an expected call of AddChLotteryInfo.
func (mr *MockICacheMockRecorder) AddChLotteryInfo(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddChLotteryInfo", reflect.TypeOf((*MockICache)(nil).AddChLotteryInfo), arg0)
}

// AddLotteryRecCh mocks base method.
func (m *MockICache) AddLotteryRecCh(arg0 []*model.RecLotteryChInfo) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddLotteryRecCh", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddLotteryRecCh indicates an expected call of AddLotteryRecCh.
func (mr *MockICacheMockRecorder) AddLotteryRecCh(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddLotteryRecCh", reflect.TypeOf((*MockICache)(nil).AddLotteryRecCh), arg0)
}

// AddOldRecommendChannelInfo mocks base method.
func (m *MockICache) AddOldRecommendChannelInfo(arg0 *model.RecommendChannelInfo) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddOldRecommendChannelInfo", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddOldRecommendChannelInfo indicates an expected call of AddOldRecommendChannelInfo.
func (mr *MockICacheMockRecorder) AddOldRecommendChannelInfo(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddOldRecommendChannelInfo", reflect.TypeOf((*MockICache)(nil).AddOldRecommendChannelInfo), arg0)
}

// AddRecommendChannelInfo mocks base method.
func (m *MockICache) AddRecommendChannelInfo(arg0 *model.RecommendChannelInfo) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddRecommendChannelInfo", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddRecommendChannelInfo indicates an expected call of AddRecommendChannelInfo.
func (mr *MockICacheMockRecorder) AddRecommendChannelInfo(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddRecommendChannelInfo", reflect.TypeOf((*MockICache)(nil).AddRecommendChannelInfo), arg0)
}

// AddRoiChannelInfo mocks base method.
func (m *MockICache) AddRoiChannelInfo(arg0 *model.RoiChannelInfo) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddRoiChannelInfo", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddRoiChannelInfo indicates an expected call of AddRoiChannelInfo.
func (mr *MockICacheMockRecorder) AddRoiChannelInfo(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddRoiChannelInfo", reflect.TypeOf((*MockICache)(nil).AddRoiChannelInfo), arg0)
}

// BatChLotteryCnt mocks base method.
func (m *MockICache) BatChLotteryCnt(arg0 []uint32, arg1 uint32) (map[uint32]uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatChLotteryCnt", arg0, arg1)
	ret0, _ := ret[0].(map[uint32]uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatChLotteryCnt indicates an expected call of BatChLotteryCnt.
func (mr *MockICacheMockRecorder) BatChLotteryCnt(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatChLotteryCnt", reflect.TypeOf((*MockICache)(nil).BatChLotteryCnt), arg0, arg1)
}

// BatGetChannelCommon mocks base method.
func (m *MockICache) BatGetChannelCommon(arg0 []uint32) (map[uint32]*entertainmentRecommendBack.ChannelCommonInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatGetChannelCommon", arg0)
	ret0, _ := ret[0].(map[uint32]*entertainmentRecommendBack.ChannelCommonInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatGetChannelCommon indicates an expected call of BatGetChannelCommon.
func (mr *MockICacheMockRecorder) BatGetChannelCommon(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatGetChannelCommon", reflect.TypeOf((*MockICache)(nil).BatGetChannelCommon), arg0)
}

// BatGetEnterChCnt mocks base method.
func (m *MockICache) BatGetEnterChCnt(arg0 []uint32) (map[uint32]uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatGetEnterChCnt", arg0)
	ret0, _ := ret[0].(map[uint32]uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatGetEnterChCnt indicates an expected call of BatGetEnterChCnt.
func (mr *MockICacheMockRecorder) BatGetEnterChCnt(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatGetEnterChCnt", reflect.TypeOf((*MockICache)(nil).BatGetEnterChCnt), arg0)
}

// BatGetUserRoiChannelType mocks base method.
func (m *MockICache) BatGetUserRoiChannelType(arg0 uint32, arg1 []uint32) (map[uint32]uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatGetUserRoiChannelType", arg0, arg1)
	ret0, _ := ret[0].(map[uint32]uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatGetUserRoiChannelType indicates an expected call of BatGetUserRoiChannelType.
func (mr *MockICacheMockRecorder) BatGetUserRoiChannelType(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatGetUserRoiChannelType", reflect.TypeOf((*MockICache)(nil).BatGetUserRoiChannelType), arg0, arg1)
}

// BatSetUserRoiChannelType mocks base method.
func (m *MockICache) BatSetUserRoiChannelType(arg0 uint32, arg1 map[uint32]uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatSetUserRoiChannelType", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// BatSetUserRoiChannelType indicates an expected call of BatSetUserRoiChannelType.
func (mr *MockICacheMockRecorder) BatSetUserRoiChannelType(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatSetUserRoiChannelType", reflect.TypeOf((*MockICache)(nil).BatSetUserRoiChannelType), arg0, arg1)
}

// BatchAddUserAbtestChannel mocks base method.
func (m *MockICache) BatchAddUserAbtestChannel(arg0 uint32, arg1 map[uint32]uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchAddUserAbtestChannel", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// BatchAddUserAbtestChannel indicates an expected call of BatchAddUserAbtestChannel.
func (mr *MockICacheMockRecorder) BatchAddUserAbtestChannel(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchAddUserAbtestChannel", reflect.TypeOf((*MockICache)(nil).BatchAddUserAbtestChannel), arg0, arg1)
}

// BatchAddUserAbtestQuickEnterChannel mocks base method.
func (m *MockICache) BatchAddUserAbtestQuickEnterChannel(arg0, arg1 uint32, arg2 map[uint32]uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchAddUserAbtestQuickEnterChannel", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// BatchAddUserAbtestQuickEnterChannel indicates an expected call of BatchAddUserAbtestQuickEnterChannel.
func (mr *MockICacheMockRecorder) BatchAddUserAbtestQuickEnterChannel(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchAddUserAbtestQuickEnterChannel", reflect.TypeOf((*MockICache)(nil).BatchAddUserAbtestQuickEnterChannel), arg0, arg1, arg2)
}

// BatchAddUserRoiChannel mocks base method.
func (m *MockICache) BatchAddUserRoiChannel(arg0 uint32, arg1 map[uint32]uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchAddUserRoiChannel", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// BatchAddUserRoiChannel indicates an expected call of BatchAddUserRoiChannel.
func (mr *MockICacheMockRecorder) BatchAddUserRoiChannel(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchAddUserRoiChannel", reflect.TypeOf((*MockICache)(nil).BatchAddUserRoiChannel), arg0, arg1)
}

// BatchGetChLotteryInfo mocks base method.
func (m *MockICache) BatchGetChLotteryInfo(arg0 []uint32) (map[uint32]*model.ChLotteryInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetChLotteryInfo", arg0)
	ret0, _ := ret[0].(map[uint32]*model.ChLotteryInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetChLotteryInfo indicates an expected call of BatchGetChLotteryInfo.
func (mr *MockICacheMockRecorder) BatchGetChLotteryInfo(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetChLotteryInfo", reflect.TypeOf((*MockICache)(nil).BatchGetChLotteryInfo), arg0)
}

// BatchGetChannelGiftInfo mocks base method.
func (m *MockICache) BatchGetChannelGiftInfo(arg0 []uint32, arg1 uint32) (map[uint32][]uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetChannelGiftInfo", arg0, arg1)
	ret0, _ := ret[0].(map[uint32][]uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetChannelGiftInfo indicates an expected call of BatchGetChannelGiftInfo.
func (mr *MockICacheMockRecorder) BatchGetChannelGiftInfo(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetChannelGiftInfo", reflect.TypeOf((*MockICache)(nil).BatchGetChannelGiftInfo), arg0, arg1)
}

// SetChannelGiftInfo mocks base method.
func (m *MockICache) SetRevenueSwitchHub(arg0, arg1 uint32, arg2 bool) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetRevenueSwitchHub", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetChannelGiftInfo indicates an expected call of SetChannelGiftInfo.
func (mr *MockICacheMockRecorder) SetRevenueSwitchHub(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetRevenueSwitchHub", reflect.TypeOf((*MockICache)(nil).SetRevenueSwitchHub), arg0, arg1, arg2)
}

// GetRevenueSwitchHub mocks base method.
func (m *MockICache) GetRevenueSwitchHub(arg0 uint32) ([]uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRevenueSwitchHub", arg0)
	ret0, _ := ret[0].([]uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRevenueSwitchHub indicates an expected call of BatchGetChannelGiftInfo.
func (mr *MockICacheMockRecorder) GetRevenueSwitchHub(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRevenueSwitchHub", reflect.TypeOf((*MockICache)(nil).GetRevenueSwitchHub), arg0)
}

// BatchGetRevenueSwitchHub mocks base method.
func (m *MockICache) BatchGetRevenueSwitchHub(arg0 []uint32, arg1 uint32) ([]uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetRevenueSwitchHub", arg0, arg1)
	ret0, _ := ret[0].([]uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetRevenueSwitchHub indicates an expected call of BatchGetChannelGiftInfo.
func (mr *MockICacheMockRecorder) BatchGetRevenueSwitchHub(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetRevenueSwitchHub", reflect.TypeOf((*MockICache)(nil).BatchGetRevenueSwitchHub), arg0, arg1)
}

// CheckDeviceIdIsRoi mocks base method.
func (m *MockICache) CheckDeviceIdIsRoi(arg0 string) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckDeviceIdIsRoi", arg0)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckDeviceIdIsRoi indicates an expected call of CheckDeviceIdIsRoi.
func (mr *MockICacheMockRecorder) CheckDeviceIdIsRoi(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckDeviceIdIsRoi", reflect.TypeOf((*MockICache)(nil).CheckDeviceIdIsRoi), arg0)
}

// CheckLotteryChIsRec mocks base method.
func (m *MockICache) CheckLotteryChIsRec(arg0 uint32) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckLotteryChIsRec", arg0)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckLotteryChIsRec indicates an expected call of CheckLotteryChIsRec.
func (mr *MockICacheMockRecorder) CheckLotteryChIsRec(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckLotteryChIsRec", reflect.TypeOf((*MockICache)(nil).CheckLotteryChIsRec), arg0)
}

// CheckRoiHighIsPush mocks base method.
func (m *MockICache) CheckRoiHighIsPush(arg0 uint32) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckRoiHighIsPush", arg0)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckRoiHighIsPush indicates an expected call of CheckRoiHighIsPush.
func (mr *MockICacheMockRecorder) CheckRoiHighIsPush(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckRoiHighIsPush", reflect.TypeOf((*MockICache)(nil).CheckRoiHighIsPush), arg0)
}

// CheckUserIsRoi mocks base method.
func (m *MockICache) CheckUserIsRoi(arg0 uint32) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckUserIsRoi", arg0)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckUserIsRoi indicates an expected call of CheckUserIsRoi.
func (mr *MockICacheMockRecorder) CheckUserIsRoi(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckUserIsRoi", reflect.TypeOf((*MockICache)(nil).CheckUserIsRoi), arg0)
}

// CheckUserIsRoiHigh mocks base method.
func (m *MockICache) CheckUserIsRoiHigh(arg0 uint32) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckUserIsRoiHigh", arg0)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckUserIsRoiHigh indicates an expected call of CheckUserIsRoiHigh.
func (mr *MockICacheMockRecorder) CheckUserIsRoiHigh(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckUserIsRoiHigh", reflect.TypeOf((*MockICache)(nil).CheckUserIsRoiHigh), arg0)
}

// ClearAllUserAbtestChannel mocks base method.
func (m *MockICache) ClearAllUserAbtestChannel(arg0 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ClearAllUserAbtestChannel", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// ClearAllUserAbtestChannel indicates an expected call of ClearAllUserAbtestChannel.
func (mr *MockICacheMockRecorder) ClearAllUserAbtestChannel(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ClearAllUserAbtestChannel", reflect.TypeOf((*MockICache)(nil).ClearAllUserAbtestChannel), arg0)
}

// ClearAllUserRoiChannel mocks base method.
func (m *MockICache) ClearAllUserRoiChannel(arg0 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ClearAllUserRoiChannel", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// ClearAllUserRoiChannel indicates an expected call of ClearAllUserRoiChannel.
func (mr *MockICacheMockRecorder) ClearAllUserRoiChannel(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ClearAllUserRoiChannel", reflect.TypeOf((*MockICache)(nil).ClearAllUserRoiChannel), arg0)
}

// ClearChannelCoolDown mocks base method.
func (m *MockICache) ClearChannelCoolDown(arg0 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ClearChannelCoolDown", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// ClearChannelCoolDown indicates an expected call of ClearChannelCoolDown.
func (mr *MockICacheMockRecorder) ClearChannelCoolDown(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ClearChannelCoolDown", reflect.TypeOf((*MockICache)(nil).ClearChannelCoolDown), arg0)
}

// ClearChannelEnterLog mocks base method.
func (m *MockICache) ClearChannelEnterLog(arg0, arg1 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ClearChannelEnterLog", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// ClearChannelEnterLog indicates an expected call of ClearChannelEnterLog.
func (mr *MockICacheMockRecorder) ClearChannelEnterLog(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ClearChannelEnterLog", reflect.TypeOf((*MockICache)(nil).ClearChannelEnterLog), arg0, arg1)
}

// ClearUserEnterChannel mocks base method.
func (m *MockICache) ClearUserEnterChannel(arg0, arg1 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ClearUserEnterChannel", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// ClearUserEnterChannel indicates an expected call of ClearUserEnterChannel.
func (mr *MockICacheMockRecorder) ClearUserEnterChannel(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ClearUserEnterChannel", reflect.TypeOf((*MockICache)(nil).ClearUserEnterChannel), arg0, arg1)
}

// ClearUserRoiChannelType mocks base method.
func (m *MockICache) ClearUserRoiChannelType(arg0 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ClearUserRoiChannelType", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// ClearUserRoiChannelType indicates an expected call of ClearUserRoiChannelType.
func (mr *MockICacheMockRecorder) ClearUserRoiChannelType(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ClearUserRoiChannelType", reflect.TypeOf((*MockICache)(nil).ClearUserRoiChannelType), arg0)
}

// DelChLotteryInfo mocks base method.
func (m *MockICache) DelChLotteryInfo(arg0 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelChLotteryInfo", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// DelChLotteryInfo indicates an expected call of DelChLotteryInfo.
func (mr *MockICacheMockRecorder) DelChLotteryInfo(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelChLotteryInfo", reflect.TypeOf((*MockICache)(nil).DelChLotteryInfo), arg0)
}

// DelChannelBigGiftInfo mocks base method.
func (m *MockICache) DelChannelBigGiftInfo(arg0 uint32) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "DelChannelBigGiftInfo", arg0)
}

// DelChannelBigGiftInfo indicates an expected call of DelChannelBigGiftInfo.
func (mr *MockICacheMockRecorder) DelChannelBigGiftInfo(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelChannelBigGiftInfo", reflect.TypeOf((*MockICache)(nil).DelChannelBigGiftInfo), arg0)
}

// DelDeviceQueryCnt mocks base method.
func (m *MockICache) DelDeviceQueryCnt(arg0 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelDeviceQueryCnt", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// DelDeviceQueryCnt indicates an expected call of DelDeviceQueryCnt.
func (mr *MockICacheMockRecorder) DelDeviceQueryCnt(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelDeviceQueryCnt", reflect.TypeOf((*MockICache)(nil).DelDeviceQueryCnt), arg0)
}

// DelFlowCardLimitConf mocks base method.
func (m *MockICache) DelFlowCardLimitConf(arg0 []uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelFlowCardLimitConf", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// DelFlowCardLimitConf indicates an expected call of DelFlowCardLimitConf.
func (mr *MockICacheMockRecorder) DelFlowCardLimitConf(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelFlowCardLimitConf", reflect.TypeOf((*MockICache)(nil).DelFlowCardLimitConf), arg0)
}

// DelLotteryRecCh mocks base method.
func (m *MockICache) DelLotteryRecCh(arg0 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelLotteryRecCh", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// DelLotteryRecCh indicates an expected call of DelLotteryRecCh.
func (mr *MockICacheMockRecorder) DelLotteryRecCh(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelLotteryRecCh", reflect.TypeOf((*MockICache)(nil).DelLotteryRecCh), arg0)
}

// DelOldRecommendChannelInfo mocks base method.
func (m *MockICache) DelOldRecommendChannelInfo(arg0 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelOldRecommendChannelInfo", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// DelOldRecommendChannelInfo indicates an expected call of DelOldRecommendChannelInfo.
func (mr *MockICacheMockRecorder) DelOldRecommendChannelInfo(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelOldRecommendChannelInfo", reflect.TypeOf((*MockICache)(nil).DelOldRecommendChannelInfo), arg0)
}

// DelRecommendChannelInfo mocks base method.
func (m *MockICache) DelRecommendChannelInfo(arg0 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelRecommendChannelInfo", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// DelRecommendChannelInfo indicates an expected call of DelRecommendChannelInfo.
func (mr *MockICacheMockRecorder) DelRecommendChannelInfo(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelRecommendChannelInfo", reflect.TypeOf((*MockICache)(nil).DelRecommendChannelInfo), arg0)
}

// DelRoiChannelInfo mocks base method.
func (m *MockICache) DelRoiChannelInfo(arg0 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelRoiChannelInfo", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// DelRoiChannelInfo indicates an expected call of DelRoiChannelInfo.
func (mr *MockICacheMockRecorder) DelRoiChannelInfo(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelRoiChannelInfo", reflect.TypeOf((*MockICache)(nil).DelRoiChannelInfo), arg0)
}

// DelRoiHighPushFlag mocks base method.
func (m *MockICache) DelRoiHighPushFlag(arg0 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelRoiHighPushFlag", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// DelRoiHighPushFlag indicates an expected call of DelRoiHighPushFlag.
func (mr *MockICacheMockRecorder) DelRoiHighPushFlag(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelRoiHighPushFlag", reflect.TypeOf((*MockICache)(nil).DelRoiHighPushFlag), arg0)
}

// GetAllCoolDownChannel mocks base method.
func (m *MockICache) GetAllCoolDownChannel(arg0 uint32, arg1 time.Duration) (map[uint32]bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllCoolDownChannel", arg0, arg1)
	ret0, _ := ret[0].(map[uint32]bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllCoolDownChannel indicates an expected call of GetAllCoolDownChannel.
func (mr *MockICacheMockRecorder) GetAllCoolDownChannel(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllCoolDownChannel", reflect.TypeOf((*MockICache)(nil).GetAllCoolDownChannel), arg0, arg1)
}

// GetAllLotteryCh mocks base method.
func (m *MockICache) GetAllLotteryCh() ([]*model.ChLotteryInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllLotteryCh")
	ret0, _ := ret[0].([]*model.ChLotteryInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllLotteryCh indicates an expected call of GetAllLotteryCh.
func (mr *MockICacheMockRecorder) GetAllLotteryCh() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllLotteryCh", reflect.TypeOf((*MockICache)(nil).GetAllLotteryCh))
}

// GetAllOldRecommendChannel mocks base method.
func (m *MockICache) GetAllOldRecommendChannel() ([]*model.RecommendChannelInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllOldRecommendChannel")
	ret0, _ := ret[0].([]*model.RecommendChannelInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllOldRecommendChannel indicates an expected call of GetAllOldRecommendChannel.
func (mr *MockICacheMockRecorder) GetAllOldRecommendChannel() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllOldRecommendChannel", reflect.TypeOf((*MockICache)(nil).GetAllOldRecommendChannel))
}

// GetAllRecommendChannel mocks base method.
func (m *MockICache) GetAllRecommendChannel() ([]*model.RecommendChannelInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllRecommendChannel")
	ret0, _ := ret[0].([]*model.RecommendChannelInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllRecommendChannel indicates an expected call of GetAllRecommendChannel.
func (mr *MockICacheMockRecorder) GetAllRecommendChannel() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllRecommendChannel", reflect.TypeOf((*MockICache)(nil).GetAllRecommendChannel))
}

// GetAllRoiChannel mocks base method.
func (m *MockICache) GetAllRoiChannel() ([]*model.RoiChannelInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllRoiChannel")
	ret0, _ := ret[0].([]*model.RoiChannelInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllRoiChannel indicates an expected call of GetAllRoiChannel.
func (mr *MockICacheMockRecorder) GetAllRoiChannel() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllRoiChannel", reflect.TypeOf((*MockICache)(nil).GetAllRoiChannel))
}

// GetAllUseFlowCardChByTs mocks base method.
func (m *MockICache) GetAllUseFlowCardChByTs(arg0 uint32) (map[uint32]uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllUseFlowCardChByTs", arg0)
	ret0, _ := ret[0].(map[uint32]uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllUseFlowCardChByTs indicates an expected call of GetAllUseFlowCardChByTs.
func (mr *MockICacheMockRecorder) GetAllUseFlowCardChByTs(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllUseFlowCardChByTs", reflect.TypeOf((*MockICache)(nil).GetAllUseFlowCardChByTs), arg0)
}

// GetAllUserAbtestChannel mocks base method.
func (m *MockICache) GetAllUserAbtestChannel(arg0, arg1, arg2 uint32) ([]uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllUserAbtestChannel", arg0, arg1, arg2)
	ret0, _ := ret[0].([]uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllUserAbtestChannel indicates an expected call of GetAllUserAbtestChannel.
func (mr *MockICacheMockRecorder) GetAllUserAbtestChannel(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllUserAbtestChannel", reflect.TypeOf((*MockICache)(nil).GetAllUserAbtestChannel), arg0, arg1, arg2)
}

// GetAllUserAbtestQuickEnterChannel mocks base method.
func (m *MockICache) GetAllUserAbtestQuickEnterChannel(arg0, arg1 uint32) ([]uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllUserAbtestQuickEnterChannel", arg0, arg1)
	ret0, _ := ret[0].([]uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllUserAbtestQuickEnterChannel indicates an expected call of GetAllUserAbtestQuickEnterChannel.
func (mr *MockICacheMockRecorder) GetAllUserAbtestQuickEnterChannel(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllUserAbtestQuickEnterChannel", reflect.TypeOf((*MockICache)(nil).GetAllUserAbtestQuickEnterChannel), arg0, arg1)
}

// GetAllUserEnterChannel mocks base method.
func (m *MockICache) GetAllUserEnterChannel(arg0, arg1 uint32, arg2 time.Duration) (map[uint32]bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllUserEnterChannel", arg0, arg1, arg2)
	ret0, _ := ret[0].(map[uint32]bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllUserEnterChannel indicates an expected call of GetAllUserEnterChannel.
func (mr *MockICacheMockRecorder) GetAllUserEnterChannel(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllUserEnterChannel", reflect.TypeOf((*MockICache)(nil).GetAllUserEnterChannel), arg0, arg1, arg2)
}

// GetAllUserRoiChannel mocks base method.
func (m *MockICache) GetAllUserRoiChannel(arg0, arg1, arg2 uint32) ([]uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllUserRoiChannel", arg0, arg1, arg2)
	ret0, _ := ret[0].([]uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllUserRoiChannel indicates an expected call of GetAllUserRoiChannel.
func (mr *MockICacheMockRecorder) GetAllUserRoiChannel(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllUserRoiChannel", reflect.TypeOf((*MockICache)(nil).GetAllUserRoiChannel), arg0, arg1, arg2)
}

// GetChannelEnterLogCnt mocks base method.
func (m *MockICache) GetChannelEnterLogCnt(arg0, arg1 uint32, arg2 time.Duration) (uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChannelEnterLogCnt", arg0, arg1, arg2)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChannelEnterLogCnt indicates an expected call of GetChannelEnterLogCnt.
func (mr *MockICacheMockRecorder) GetChannelEnterLogCnt(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChannelEnterLogCnt", reflect.TypeOf((*MockICache)(nil).GetChannelEnterLogCnt), arg0, arg1, arg2)
}

// GetChannelGiftValueList mocks base method.
func (m *MockICache) GetChannelGiftValueList(arg0 int64, arg1 time.Time) ([]uint32, map[uint32]float64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChannelGiftValueList", arg0, arg1)
	ret0, _ := ret[0].([]uint32)
	ret1, _ := ret[1].(map[uint32]float64)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetChannelGiftValueList indicates an expected call of GetChannelGiftValueList.
func (mr *MockICacheMockRecorder) GetChannelGiftValueList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChannelGiftValueList", reflect.TypeOf((*MockICache)(nil).GetChannelGiftValueList), arg0, arg1)
}

// GetFilterSABCCids mocks base method.
func (m *MockICache) GetFilterSABCCids(arg0 context.Context, arg1, arg2, arg3 uint32) (map[uint32]struct{}, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetFilterSABCCids", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(map[uint32]struct{})
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetFilterSABCCids indicates an expected call of GetFilterSABCCids.
func (mr *MockICacheMockRecorder) GetFilterSABCCids(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFilterSABCCids", reflect.TypeOf((*MockICache)(nil).GetFilterSABCCids), arg0, arg1, arg2, arg3)
}

// GetFlowCardHourUseCnt mocks base method.
func (m *MockICache) GetFlowCardHourUseCnt(arg0, arg1 uint32) (uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetFlowCardHourUseCnt", arg0, arg1)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetFlowCardHourUseCnt indicates an expected call of GetFlowCardHourUseCnt.
func (mr *MockICacheMockRecorder) GetFlowCardHourUseCnt(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFlowCardHourUseCnt", reflect.TypeOf((*MockICache)(nil).GetFlowCardHourUseCnt), arg0, arg1)
}

// GetFlowCardLimitConf mocks base method.
func (m *MockICache) GetFlowCardLimitConf(arg0 uint32) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetFlowCardLimitConf", arg0)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetFlowCardLimitConf indicates an expected call of GetFlowCardLimitConf.
func (mr *MockICacheMockRecorder) GetFlowCardLimitConf(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFlowCardLimitConf", reflect.TypeOf((*MockICache)(nil).GetFlowCardLimitConf), arg0)
}

// GetGuildFlowCardUseLimit mocks base method.
func (m *MockICache) GetGuildFlowCardUseLimit(arg0, arg1 uint32) (uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGuildFlowCardUseLimit", arg0, arg1)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGuildFlowCardUseLimit indicates an expected call of GetGuildFlowCardUseLimit.
func (mr *MockICacheMockRecorder) GetGuildFlowCardUseLimit(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGuildFlowCardUseLimit", reflect.TypeOf((*MockICache)(nil).GetGuildFlowCardUseLimit), arg0, arg1)
}

// GetLevelChannel mocks base method.
func (m *MockICache) GetLevelChannel(arg0, arg1 uint32) ([]uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLevelChannel", arg0, arg1)
	ret0, _ := ret[0].([]uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLevelChannel indicates an expected call of GetLevelChannel.
func (mr *MockICacheMockRecorder) GetLevelChannel(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLevelChannel", reflect.TypeOf((*MockICache)(nil).GetLevelChannel), arg0, arg1)
}

// GetLotteryRecChList mocks base method.
func (m *MockICache) GetLotteryRecChList(arg0, arg1 uint32) ([]uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLotteryRecChList", arg0, arg1)
	ret0, _ := ret[0].([]uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLotteryRecChList indicates an expected call of GetLotteryRecChList.
func (mr *MockICacheMockRecorder) GetLotteryRecChList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLotteryRecChList", reflect.TypeOf((*MockICache)(nil).GetLotteryRecChList), arg0, arg1)
}

// GetPersonalTagLevelChannel mocks base method.
func (m *MockICache) GetPersonalTagLevelChannel(arg0, arg1 uint32, arg2 int32) ([]uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPersonalTagLevelChannel", arg0, arg1, arg2)
	ret0, _ := ret[0].([]uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPersonalTagLevelChannel indicates an expected call of GetPersonalTagLevelChannel.
func (mr *MockICacheMockRecorder) GetPersonalTagLevelChannel(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPersonalTagLevelChannel", reflect.TypeOf((*MockICache)(nil).GetPersonalTagLevelChannel), arg0, arg1, arg2)
}

// GetTagLevelChannel mocks base method.
func (m *MockICache) GetTagLevelChannel(arg0, arg1, arg2 uint32) ([]uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTagLevelChannel", arg0, arg1, arg2)
	ret0, _ := ret[0].([]uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTagLevelChannel indicates an expected call of GetTagLevelChannel.
func (mr *MockICacheMockRecorder) GetTagLevelChannel(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTagLevelChannel", reflect.TypeOf((*MockICache)(nil).GetTagLevelChannel), arg0, arg1, arg2)
}

// GetUseEnterChannel mocks base method.
func (m *MockICache) GetUseEnterChannel(arg0, arg1 uint32) ([]uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUseEnterChannel", arg0, arg1)
	ret0, _ := ret[0].([]uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUseEnterChannel indicates an expected call of GetUseEnterChannel.
func (mr *MockICacheMockRecorder) GetUseEnterChannel(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUseEnterChannel", reflect.TypeOf((*MockICache)(nil).GetUseEnterChannel), arg0, arg1)
}

// GetUserAbtestChannelNum mocks base method.
func (m *MockICache) GetUserAbtestChannelNum(arg0 uint32) (uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserAbtestChannelNum", arg0)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserAbtestChannelNum indicates an expected call of GetUserAbtestChannelNum.
func (mr *MockICacheMockRecorder) GetUserAbtestChannelNum(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserAbtestChannelNum", reflect.TypeOf((*MockICache)(nil).GetUserAbtestChannelNum), arg0)
}

// GetUserLabel mocks base method.
func (m *MockICache) GetUserLabel(arg0 uint32) (map[string]string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserLabel", arg0)
	ret0, _ := ret[0].(map[string]string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserLabel indicates an expected call of GetUserLabel.
func (mr *MockICacheMockRecorder) GetUserLabel(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserLabel", reflect.TypeOf((*MockICache)(nil).GetUserLabel), arg0)
}

// GetUserPreferChannel mocks base method.
func (m *MockICache) GetUserPreferChannel(arg0 uint32) (uint32, bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserPreferChannel", arg0)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(bool)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetUserPreferChannel indicates an expected call of GetUserPreferChannel.
func (mr *MockICacheMockRecorder) GetUserPreferChannel(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserPreferChannel", reflect.TypeOf((*MockICache)(nil).GetUserPreferChannel), arg0)
}

// GetUserRandIndex mocks base method.
func (m *MockICache) GetUserRandIndex(arg0, arg1, arg2 uint32, arg3 bool) (uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserRandIndex", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserRandIndex indicates an expected call of GetUserRandIndex.
func (mr *MockICacheMockRecorder) GetUserRandIndex(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserRandIndex", reflect.TypeOf((*MockICache)(nil).GetUserRandIndex), arg0, arg1, arg2, arg3)
}

// GetUserRoiType mocks base method.
func (m *MockICache) GetUserRoiType(arg0 uint32) (uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserRoiType", arg0)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserRoiType indicates an expected call of GetUserRoiType.
func (mr *MockICacheMockRecorder) GetUserRoiType(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserRoiType", reflect.TypeOf((*MockICache)(nil).GetUserRoiType), arg0)
}

// IncrAnchorFlowCardDayUseCnt mocks base method.
func (m *MockICache) IncrAnchorFlowCardDayUseCnt(arg0, arg1 uint32, arg2 int64) (uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IncrAnchorFlowCardDayUseCnt", arg0, arg1, arg2)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IncrAnchorFlowCardDayUseCnt indicates an expected call of IncrAnchorFlowCardDayUseCnt.
func (mr *MockICacheMockRecorder) IncrAnchorFlowCardDayUseCnt(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IncrAnchorFlowCardDayUseCnt", reflect.TypeOf((*MockICache)(nil).IncrAnchorFlowCardDayUseCnt), arg0, arg1, arg2)
}

// IncrAnchorFlowCardHourUseCnt mocks base method.
func (m *MockICache) IncrAnchorFlowCardHourUseCnt(arg0, arg1 uint32, arg2 int64) (uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IncrAnchorFlowCardHourUseCnt", arg0, arg1, arg2)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IncrAnchorFlowCardHourUseCnt indicates an expected call of IncrAnchorFlowCardHourUseCnt.
func (mr *MockICacheMockRecorder) IncrAnchorFlowCardHourUseCnt(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IncrAnchorFlowCardHourUseCnt", reflect.TypeOf((*MockICache)(nil).IncrAnchorFlowCardHourUseCnt), arg0, arg1, arg2)
}

// IncrChLotteryCnt mocks base method.
func (m *MockICache) IncrChLotteryCnt(arg0, arg1 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IncrChLotteryCnt", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// IncrChLotteryCnt indicates an expected call of IncrChLotteryCnt.
func (mr *MockICacheMockRecorder) IncrChLotteryCnt(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IncrChLotteryCnt", reflect.TypeOf((*MockICache)(nil).IncrChLotteryCnt), arg0, arg1)
}

// IncrChannelGiftValue mocks base method.
func (m *MockICache) IncrChannelGiftValue(arg0 uint32, arg1 float64, arg2 time.Time) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IncrChannelGiftValue", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// IncrChannelGiftValue indicates an expected call of IncrChannelGiftValue.
func (mr *MockICacheMockRecorder) IncrChannelGiftValue(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IncrChannelGiftValue", reflect.TypeOf((*MockICache)(nil).IncrChannelGiftValue), arg0, arg1, arg2)
}

// IncrDeviceQueryCnt mocks base method.
func (m *MockICache) IncrDeviceQueryCnt(arg0 string) (uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IncrDeviceQueryCnt", arg0)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IncrDeviceQueryCnt indicates an expected call of IncrDeviceQueryCnt.
func (mr *MockICacheMockRecorder) IncrDeviceQueryCnt(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IncrDeviceQueryCnt", reflect.TypeOf((*MockICache)(nil).IncrDeviceQueryCnt), arg0)
}

// IncrEnterChCnt mocks base method.
func (m *MockICache) IncrEnterChCnt(arg0 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IncrEnterChCnt", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// IncrEnterChCnt indicates an expected call of IncrEnterChCnt.
func (mr *MockICacheMockRecorder) IncrEnterChCnt(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IncrEnterChCnt", reflect.TypeOf((*MockICache)(nil).IncrEnterChCnt), arg0)
}

// IncrFlowCardHourUseCnt mocks base method.
func (m *MockICache) IncrFlowCardHourUseCnt(arg0, arg1 uint32, arg2 int64) (uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IncrFlowCardHourUseCnt", arg0, arg1, arg2)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IncrFlowCardHourUseCnt indicates an expected call of IncrFlowCardHourUseCnt.
func (mr *MockICacheMockRecorder) IncrFlowCardHourUseCnt(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IncrFlowCardHourUseCnt", reflect.TypeOf((*MockICache)(nil).IncrFlowCardHourUseCnt), arg0, arg1, arg2)
}

// IncrGuildFlowCardDayUseCnt mocks base method.
func (m *MockICache) IncrGuildFlowCardDayUseCnt(arg0, arg1, arg2 uint32, arg3 int64) (uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IncrGuildFlowCardDayUseCnt", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IncrGuildFlowCardDayUseCnt indicates an expected call of IncrGuildFlowCardDayUseCnt.
func (mr *MockICacheMockRecorder) IncrGuildFlowCardDayUseCnt(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IncrGuildFlowCardDayUseCnt", reflect.TypeOf((*MockICache)(nil).IncrGuildFlowCardDayUseCnt), arg0, arg1, arg2, arg3)
}

// IncrSoonExpireFlowCardPushFlag mocks base method.
func (m *MockICache) IncrSoonExpireFlowCardPushFlag(arg0 uint32, arg1 string, arg2 int64) (uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IncrSoonExpireFlowCardPushFlag", arg0, arg1, arg2)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IncrSoonExpireFlowCardPushFlag indicates an expected call of IncrSoonExpireFlowCardPushFlag.
func (mr *MockICacheMockRecorder) IncrSoonExpireFlowCardPushFlag(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IncrSoonExpireFlowCardPushFlag", reflect.TypeOf((*MockICache)(nil).IncrSoonExpireFlowCardPushFlag), arg0, arg1, arg2)
}

// IncrUseFlowCardToastFlag mocks base method.
func (m *MockICache) IncrUseFlowCardToastFlag(arg0, arg1 uint32, arg2 int64) (uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IncrUseFlowCardToastFlag", arg0, arg1, arg2)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IncrUseFlowCardToastFlag indicates an expected call of IncrUseFlowCardToastFlag.
func (mr *MockICacheMockRecorder) IncrUseFlowCardToastFlag(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IncrUseFlowCardToastFlag", reflect.TypeOf((*MockICache)(nil).IncrUseFlowCardToastFlag), arg0, arg1, arg2)
}

// RecordChannelEnterLog mocks base method.
func (m *MockICache) RecordChannelEnterLog(arg0, arg1, arg2 uint32, arg3 time.Duration) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RecordChannelEnterLog", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// RecordChannelEnterLog indicates an expected call of RecordChannelEnterLog.
func (mr *MockICacheMockRecorder) RecordChannelEnterLog(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RecordChannelEnterLog", reflect.TypeOf((*MockICache)(nil).RecordChannelEnterLog), arg0, arg1, arg2, arg3)
}

// SetChannelCoolDown mocks base method.
func (m *MockICache) SetChannelCoolDown(arg0, arg1 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetChannelCoolDown", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetChannelCoolDown indicates an expected call of SetChannelCoolDown.
func (mr *MockICacheMockRecorder) SetChannelCoolDown(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetChannelCoolDown", reflect.TypeOf((*MockICache)(nil).SetChannelCoolDown), arg0, arg1)
}

// SetChannelFlowCardRecommendLv mocks base method.
func (m *MockICache) SetChannelFlowCardRecommendLv(arg0, arg1, arg2 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetChannelFlowCardRecommendLv", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetChannelFlowCardRecommendLv indicates an expected call of SetChannelFlowCardRecommendLv.
func (mr *MockICacheMockRecorder) SetChannelFlowCardRecommendLv(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetChannelFlowCardRecommendLv", reflect.TypeOf((*MockICache)(nil).SetChannelFlowCardRecommendLv), arg0, arg1, arg2)
}

// SetChannelGiftInfo mocks base method.
func (m *MockICache) SetChannelGiftInfo(arg0, arg1, arg2 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetChannelGiftInfo", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetChannelGiftInfo indicates an expected call of SetChannelGiftInfo.
func (mr *MockICacheMockRecorder) SetChannelGiftInfo(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetChannelGiftInfo", reflect.TypeOf((*MockICache)(nil).SetChannelGiftInfo), arg0, arg1, arg2)
}

// SetDeviceIdRoiFlag mocks base method.
func (m *MockICache) SetDeviceIdRoiFlag(arg0 string, arg1 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetDeviceIdRoiFlag", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetDeviceIdRoiFlag indicates an expected call of SetDeviceIdRoiFlag.
func (mr *MockICacheMockRecorder) SetDeviceIdRoiFlag(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetDeviceIdRoiFlag", reflect.TypeOf((*MockICache)(nil).SetDeviceIdRoiFlag), arg0, arg1)
}

// SetFilterSABCCids mocks base method.
func (m *MockICache) SetFilterSABCCids(arg0 context.Context, arg1, arg2, arg3 uint32, arg4 []uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetFilterSABCCids", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetFilterSABCCids indicates an expected call of SetFilterSABCCids.
func (mr *MockICacheMockRecorder) SetFilterSABCCids(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetFilterSABCCids", reflect.TypeOf((*MockICache)(nil).SetFilterSABCCids), arg0, arg1, arg2, arg3, arg4)
}

// SetFlowCardLimitConf mocks base method.
func (m *MockICache) SetFlowCardLimitConf(arg0 uint32, arg1 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetFlowCardLimitConf", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetFlowCardLimitConf indicates an expected call of SetFlowCardLimitConf.
func (mr *MockICacheMockRecorder) SetFlowCardLimitConf(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetFlowCardLimitConf", reflect.TypeOf((*MockICache)(nil).SetFlowCardLimitConf), arg0, arg1)
}

// SetRoiHighPushFlag mocks base method.
func (m *MockICache) SetRoiHighPushFlag(arg0, arg1 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetRoiHighPushFlag", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetRoiHighPushFlag indicates an expected call of SetRoiHighPushFlag.
func (mr *MockICacheMockRecorder) SetRoiHighPushFlag(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetRoiHighPushFlag", reflect.TypeOf((*MockICache)(nil).SetRoiHighPushFlag), arg0, arg1)
}

// SetUserEnterChannel mocks base method.
func (m *MockICache) SetUserEnterChannel(arg0, arg1, arg2 uint32, arg3 time.Duration) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetUserEnterChannel", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetUserEnterChannel indicates an expected call of SetUserEnterChannel.
func (mr *MockICacheMockRecorder) SetUserEnterChannel(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetUserEnterChannel", reflect.TypeOf((*MockICache)(nil).SetUserEnterChannel), arg0, arg1, arg2, arg3)
}

// SetUserLabel mocks base method.
func (m *MockICache) SetUserLabel(arg0 uint32, arg1 map[string]string, arg2 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetUserLabel", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetUserLabel indicates an expected call of SetUserLabel.
func (mr *MockICacheMockRecorder) SetUserLabel(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetUserLabel", reflect.TypeOf((*MockICache)(nil).SetUserLabel), arg0, arg1, arg2)
}

// SetUserPreferChannel mocks base method.
func (m *MockICache) SetUserPreferChannel(arg0, arg1 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetUserPreferChannel", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetUserPreferChannel indicates an expected call of SetUserPreferChannel.
func (mr *MockICacheMockRecorder) SetUserPreferChannel(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetUserPreferChannel", reflect.TypeOf((*MockICache)(nil).SetUserPreferChannel), arg0, arg1)
}

// SetUserRoiFlag mocks base method.
func (m *MockICache) SetUserRoiFlag(arg0, arg1 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetUserRoiFlag", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetUserRoiFlag indicates an expected call of SetUserRoiFlag.
func (mr *MockICacheMockRecorder) SetUserRoiFlag(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetUserRoiFlag", reflect.TypeOf((*MockICache)(nil).SetUserRoiFlag), arg0, arg1)
}

// SetUserRoiHighFlag mocks base method.
func (m *MockICache) SetUserRoiHighFlag(arg0, arg1 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetUserRoiHighFlag", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetUserRoiHighFlag indicates an expected call of SetUserRoiHighFlag.
func (mr *MockICacheMockRecorder) SetUserRoiHighFlag(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetUserRoiHighFlag", reflect.TypeOf((*MockICache)(nil).SetUserRoiHighFlag), arg0, arg1)
}

// SetUserRoiType mocks base method.
func (m *MockICache) SetUserRoiType(arg0, arg1, arg2 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetUserRoiType", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetUserRoiType indicates an expected call of SetUserRoiType.
func (mr *MockICacheMockRecorder) SetUserRoiType(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetUserRoiType", reflect.TypeOf((*MockICache)(nil).SetUserRoiType), arg0, arg1, arg2)
}
