package cache

import (
	"context"
	"fmt"
	"gitlab.ttyuyin.com/avengers/tyr/core/log"
	redisConnect "gitlab.ttyuyin.com/avengers/tyr/core/middleware/redis/connect"
	"gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/redis"
	pb "golang.52tt.com/protocol/services/channel-recommend-svr"
	"golang.52tt.com/services/channel-recommend/channel-recommend-relationship/internal/conf"
	"strconv"
	"strings"
	"time"
)

const (
	WeekSec = 7 * 24 * 3600
)

type Cache struct {
	dyconf conf.IDynamicConf
	cmder  redis.Cmdable
	recR   redis.Cmdable
	recM   redis.Cmdable
}

func NewCache(ctx context.Context, dyConf conf.IDynamicConf, myRedisConf, recRRedisConf, recMRedisConf *redisConnect.RedisConfig) (ICache, error) {
	client, err := redisConnect.NewClient(ctx, myRedisConf)
	if err != nil {
		log.ErrorWithCtx(ctx, "NewCache failed,cfg:%+v err: %+v", myRedisConf, err)
		return nil, err
	}
	recr, err := redisConnect.NewClient(ctx, recRRedisConf)
	if err != nil {
		log.ErrorWithCtx(ctx, "NewCache failed,r cfg:%+v err: %+v", recRRedisConf, err)
		return nil, err
	}
	recm, err := redisConnect.NewClient(ctx, recMRedisConf)
	if err != nil {
		log.ErrorWithCtx(ctx, "NewCache failed,m cfg:%+v err: %+v", recMRedisConf, err)
		return nil, err
	}
	c := &Cache{
		dyconf: dyConf,
		cmder:  client,
		recR:   recr,
		recM:   recm,
	}
	log.InfoWithCtx(ctx, "NewCache OK")
	return c, nil
}

func (c *Cache) Close() error {
	if err := c.cmder.(redis.Client).Close(); err != nil {
		return err
	}
	if err := c.recR.(redis.Client).Close(); err != nil {
		return err
	}
	if err := c.recM.(redis.Client).Close(); err != nil {
		return err
	}
	return nil
}

// KeyLevelChannel 等级库Key
func KeyLevelChannel(level, userCategory uint32) string {
	return fmt.Sprintf("C_L_T_%d_%d_%d", userCategory, level, 2)
}

// KeyRelationshipChannel 用户关系链Key
func KeyRelationshipChannel(uid, relationshipType uint32) string {
	return fmt.Sprintf("RRSC_cids_%d_%d", uid, relationshipType)
}

func KeyLastCalcRelationshipTime(uid uint32) string {
	return fmt.Sprintf("RRSC_last_calc_%d", uid)
}

func KeyFilterChannels(uid uint32) string {
	return fmt.Sprintf("RRSC_filters_%d", uid)
}

func KeyTagChannelTitle(dateTag string) string {
	return fmt.Sprintf("channel_title_%s", dateTag)
}

func KeyMyGuildChannels(uid uint32) string {
	return fmt.Sprintf("RRSC_guild_cids_%d", uid)
}

// GetLevelChannel 获取当前等级库房间
func (c *Cache) GetLevelChannel(ctx context.Context, level, userCategory uint32) ([]uint32, error) {
	key := KeyLevelChannel(level, userCategory)
	cidList := make([]uint32, 0)

	memList, err := c.recR.ZRevRange(ctx, key, 0, 1500).Result()
	if err != nil && err != redis.Nil {
		return nil, err
	}

	for _, mem := range memList {
		channelId, err := strconv.ParseUint(mem, 10, 32)
		if err != nil {
			log.Errorf("GetLevelChannel ParseInt failed level:%d userCategory:%d err:%v", level, userCategory, err)
			continue
		}

		cidList = append(cidList, uint32(channelId))
	}
	return cidList, nil
}

// AddLastCalcRelationshipTime 更新最近计算用户关系链时间
func (c *Cache) AddLastCalcRelationshipTime(ctx context.Context, uid uint32) error {
	key := KeyLastCalcRelationshipTime(uid)
	err := c.cmder.Set(ctx, key, time.Now().Unix(), time.Hour).Err()
	if err != nil {
		log.ErrorWithCtx(ctx, "AddLastCalcUid Set failed uid:%d err:%v", uid, err)
		return err
	}
	return nil
}

// GetLastCalcRelationshipTime 获取最近计算用户关系链时间
func (c *Cache) GetLastCalcRelationshipTime(ctx context.Context, uid uint32) (int64, error) {
	key := KeyLastCalcRelationshipTime(uid)
	score, err := c.cmder.Get(ctx, key).Int64()
	if err != nil {
		if err == redis.Nil {
			return 0, nil
		}
		log.ErrorWithCtx(ctx, "GetLastCalcRelationshipTime failed uid:%d err:%v", uid, err)
		return 0, err
	}
	return score, nil
}

// AddRelationshipChannel 添加某种关系链类型的推荐房间列表
func (c *Cache) AddRelationshipChannel(ctx context.Context, uid, relationshipType uint32, cids []uint32) error {
	key := KeyRelationshipChannel(uid, relationshipType)
	if len(cids) == 0 { //删除
		err := c.cmder.Del(ctx, key).Err()
		if err != nil {
			log.ErrorWithCtx(ctx, "AddRelationshipChannel Del failed uid:%d relationshipType:%d err:%v", uid, relationshipType, err)
			return nil
		}
		return nil
	}
	cidsVals := make([]string, len(cids))
	for i, cid := range cids {
		cidsVals[i] = fmt.Sprintf("%d", cid)
	}
	val := strings.Join(cidsVals, ",")
	err := c.cmder.Set(ctx, key, val, time.Hour*24).Err()
	if err != nil {
		log.ErrorWithCtx(ctx, "AddRelationshipChannel key:%s, val:%v err:%v", key, val, err)
		return err
	}
	return nil
}

func (c *Cache) GetRelationshipChannel(ctx context.Context, uid uint32) (map[uint32][]uint32, error) {
	keys := make([]string, 0)
	allRts := make([]uint32, 0)
	hasUsedEnter := false
	for _, rt := range c.dyconf.GetRelationshipConf().RelationshipTypes {
		if rt.Type != uint32(pb.RecommendRelationshipType_RECOMMMENT_RELATIONSHIP_TYPE_USED_ENTER) {
			keys = append(keys, KeyRelationshipChannel(uid, rt.Type))
			allRts = append(allRts, rt.Type)
		} else {
			hasUsedEnter = true
		}
	}
	relationshipMap := make(map[uint32][]uint32)
	// 曾经进入的房间，取原来的值
	if hasUsedEnter {
		cids, err := c.getUseEnterChannel(ctx, uid)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetRelationshipChannel getUseEnterChannel failed uid:%d err:%v", uid, err)
		} else {
			relationshipMap[uint32(pb.RecommendRelationshipType_RECOMMMENT_RELATIONSHIP_TYPE_USED_ENTER)] = cids
		}
	}

	if len(keys) == 0 {
		return relationshipMap, nil
	}
	//其它类型
	values, err := c.cmder.MGet(ctx, keys...).Result()
	if err != nil {
		log.ErrorWithCtx(ctx, "GetRelationshipChannel MGet failed uid:%d err:%v", uid, err)
		return nil, err
	}

	for i, val := range values {
		if val == nil {
			continue
		}
		cids := strings.Split(val.(string), ",")
		relationshipType := allRts[i]
		relationshipMap[relationshipType] = make([]uint32, 0)
		for _, cid := range cids {
			cidInt, err := strconv.ParseUint(cid, 10, 32)
			if err != nil {
				log.ErrorWithCtx(ctx, "GetRelationshipChannel ParseInt failed uid:%d err:%v", uid, err)
				continue
			}
			relationshipMap[relationshipType] = append(relationshipMap[relationshipType], uint32(cidInt))
		}
	}
	return relationshipMap, nil
}

func (c *Cache) GetRelationshipChannelUidsByType(ctx context.Context, uid, rt uint32) ([]uint32, error) {
	if !c.dyconf.GetRelationshipConf().IsRelationshipType(rt) {
		return nil, nil
	}
	if rt == uint32(pb.RecommendRelationshipType_RECOMMMENT_RELATIONSHIP_TYPE_USED_ENTER) {
		return c.getUseEnterChannel(ctx, uid)
	}
	key := KeyRelationshipChannel(uid, rt)
	val, err := c.cmder.Get(ctx, key).Result()
	if err != nil {
		if err == redis.Nil {
			return nil, nil
		}
		log.ErrorWithCtx(ctx, "GetRelationshipChannelUidsByType Get failed uid:%d rt:%d err:%v", uid, rt, err)
		return nil, err
	}
	cids := strings.Split(val, ",")
	cidList := make([]uint32, 0)
	for _, cid := range cids {
		cidInt, err := strconv.ParseUint(cid, 10, 32)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetRelationshipChannelUidsByType ParseInt failed uid:%d err:%v", uid, err)
			continue
		}
		cidList = append(cidList, uint32(cidInt))
	}
	log.DebugWithCtx(ctx, "GetRelationshipChannelUidsByType OK uid:%d rt:%d cids:%v", uid, rt, cidList)
	return cidList, nil
}

func (c *Cache) Lock(ctx context.Context, key string, expire time.Duration) bool {
	ok, err := c.cmder.SetNX(ctx, key, "1", expire).Result()
	if err != nil {
		log.ErrorWithCtx(ctx, "Lock failed key:%s err:%v", key, err)
		return false
	}
	return ok
}

func (c *Cache) Unlock(ctx context.Context, key string) {
	_, err := c.cmder.Del(ctx, key).Result()
	if err != nil {
		log.ErrorWithCtx(ctx, "Unlock failed key:%s err:%v", key, err)
	}
}

func (c *Cache) AddFilterChannels(ctx context.Context, uid uint32, cids []uint32) error {
	if len(cids) == 0 {
		return nil
	}
	key := KeyFilterChannels(uid)
	members := make([]*redis.Z, len(cids))
	ts := time.Now().Unix()
	for i, cid := range cids {
		members[i] = &redis.Z{
			Score:  float64(ts),
			Member: cid,
		}
	}
	err := c.cmder.ZAdd(ctx, key, members...).Err()
	if err != nil {
		log.ErrorWithCtx(ctx, "AddFilterChannels SAdd failed uid:%d, cids:%v err:%v", uid, cids, err)
		return err
	}
	err = c.cmder.Expire(ctx, key, time.Minute*30).Err()
	if err != nil {
		log.ErrorWithCtx(ctx, "AddFilterChannels Expire failed uid:%d err:%v", uid, err)
		return err
	}
	return nil
}

func (c *Cache) GetFilterChannels(ctx context.Context, uid uint32) (map[uint32]struct{}, error) {
	key := KeyFilterChannels(uid)
	minTs := time.Now().Add(-time.Second * time.Duration(c.dyconf.GetRelationshipConf().CooldownTime)).Unix()
	members, err := c.cmder.ZRangeByScore(ctx, key, &redis.ZRangeBy{
		Min:    strconv.FormatInt(minTs, 10),
		Max:    "+inf",
		Offset: 0,
		Count:  1000,
	}).Result()

	if err != nil {
		log.ErrorWithCtx(ctx, "GetFilterChannels SMembers failed uid:%d err:%v", uid, err)
		return nil, err
	}
	cidList := make(map[uint32]struct{})
	for _, cid := range members {
		cidInt, err := strconv.ParseUint(cid, 10, 32)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetFilterChannels ParseInt failed uid:%d err:%v", uid, err)
			continue
		}
		cidList[uint32(cidInt)] = struct{}{}
	}
	go func() {
		subCtx, cancel := context.WithTimeout(context.Background(), time.Second)
		defer cancel()
		_, err = c.cmder.ZRemRangeByScore(subCtx, key, "-inf", strconv.FormatInt(minTs, 10)).Result()
		if err != nil {
			log.ErrorWithCtx(subCtx, "GetFilterChannels ZRemRangeByScore failed uid:%d err:%v", uid, err)
		}
	}()
	return cidList, nil
}

func keyUserEnter(uid uint32) string {
	return fmt.Sprintf("C_U_E_%d", uid)
}

// GetUseEnterChannel 曾经进入过的房间
func (c *Cache) getUseEnterChannel(ctx context.Context, uid uint32) ([]uint32, error) {
	key := keyUserEnter(uid)
	cidList := make([]uint32, 0)
	nowTs := uint32(time.Now().Unix())
	memList, err := c.recM.ZRevRangeByScore(ctx, key, &redis.ZRangeBy{
		Min:    fmt.Sprintf("%d", nowTs-WeekSec),
		Max:    fmt.Sprintf("%d", nowTs),
		Offset: 0,
		Count:  256,
	}).Result()
	if err != nil {
		return nil, err
	}

	for _, mem := range memList {
		channelId, err := strconv.ParseUint(mem, 10, 32)
		if nil != err {
			log.ErrorWithCtx(ctx, "GetUseEnterChannel ParseUint failed uid:%d mem:%v err:%v", uid, mem, err)
			continue
		}

		cidList = append(cidList, uint32(channelId))
	}

	return cidList, nil
}

func (c *Cache) AddMyGuildChannels(ctx context.Context, uid uint32, cids map[uint32]uint32) error {
	key := KeyMyGuildChannels(uid)
	if len(cids) == 0 {
		err := c.cmder.Del(ctx, key).Err()
		if err != nil {
			log.ErrorWithCtx(ctx, "AddMyGuildChannels Del failed uid:%d err:%v", uid, err)
			return nil
		}
		return nil

	}
	zMem := make([]*redis.Z, 0)
	for cid, day := range cids {
		zMem = append(zMem, &redis.Z{
			Score:  float64(day),
			Member: cid,
		})
	}

	keyTmp := key + "_tmp"
	_, e := c.cmder.Pipelined(ctx, func(pipe redis.Pipeliner) error {
		err := pipe.ZAdd(ctx, keyTmp, zMem...).Err()
		if err != nil {
			return err
		}
		err = pipe.Rename(ctx, keyTmp, key).Err()
		if err != nil {
			return err
		}
		err = pipe.Expire(ctx, key, time.Hour*24).Err()
		if err != nil {
			return err
		}
		return nil
	})
	if e != nil {
		log.ErrorWithCtx(ctx, "AddMyGuildChannels failed uid:%d, cids:%v err:%v", uid, cids, e)
		return e
	}
	return nil
}

func (c *Cache) GetMyGuildChannels(ctx context.Context, uid uint32) ([]uint32, error) {
	key := KeyMyGuildChannels(uid)
	cids := make([]uint32, 0)
	memList, err := c.recM.ZRevRange(ctx, key, 0, -1).Result()
	if err != nil {
		return nil, err
	}

	for _, mem := range memList {
		channelId, err := strconv.ParseUint(mem, 10, 32)
		if nil != err {
			log.Errorf("GetMyGuildChannels ParseUint failed uid:%d mem:%v err:%v", uid, mem, err)
			continue
		}

		cids = append(cids, uint32(channelId))
	}

	return cids, nil
}

func (c *Cache) BatAddChannelTitle(ctx context.Context, dateTag string, mapCid2Title map[uint32]string) error {
	if len(mapCid2Title) == 0 {
		return nil
	}

	field2Value := make(map[string]interface{})
	for cid, val := range mapCid2Title {
		field2Value[fmt.Sprintf("%d", cid)] = val
	}

	key := KeyTagChannelTitle(dateTag)
	err := c.cmder.HMSet(ctx, key, field2Value).Err()
	if err != nil {
		return err
	}

	c.cmder.Expire(ctx, key, time.Hour*48)
	return err
}

func (c *Cache) GetAllChannelTitle(ctx context.Context, dateTag string) (map[uint32]string, error) {
	mapId2Title := make(map[uint32]string)

	vals, err := c.cmder.HGetAll(ctx, KeyTagChannelTitle(dateTag)).Result()
	if err != nil {
		return mapId2Title, err
	}

	if len(vals) == 0 {
		return mapId2Title, nil
	}

	for k, v := range vals {
		cid, tmpErr := strconv.ParseUint(k, 10, 32)
		if tmpErr != nil {
			log.ErrorWithCtx(ctx, "GetAllChannelTitle strconv.ParseUint failed dateTag:%s k:%v err:%v", dateTag, k, tmpErr)
			continue
		}

		mapId2Title[uint32(cid)] = v
	}

	return mapId2Title, nil
}
