// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/services/channel-live-fans/event (interfaces: IAddFansSubscriber)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
)

// MockIAddFansSubscriber is a mock of IAddFansSubscriber interface.
type MockIAddFansSubscriber struct {
	ctrl     *gomock.Controller
	recorder *MockIAddFansSubscriberMockRecorder
}

// MockIAddFansSubscriberMockRecorder is the mock recorder for MockIAddFansSubscriber.
type MockIAddFansSubscriberMockRecorder struct {
	mock *MockIAddFansSubscriber
}

// NewMockIAddFansSubscriber creates a new mock instance.
func NewMockIAddFansSubscriber(ctrl *gomock.Controller) *MockIAddFansSubscriber {
	mock := &MockIAddFansSubscriber{ctrl: ctrl}
	mock.recorder = &MockIAddFansSubscriberMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIAddFansSubscriber) EXPECT() *MockIAddFansSubscriberMockRecorder {
	return m.recorder
}

// CheckAnchorSetNamePermit mocks base method.
func (m *MockIAddFansSubscriber) CheckAnchorSetNamePermit(arg0 context.Context, arg1 uint32) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "CheckAnchorSetNamePermit", arg0, arg1)
}

// CheckAnchorSetNamePermit indicates an expected call of CheckAnchorSetNamePermit.
func (mr *MockIAddFansSubscriberMockRecorder) CheckAnchorSetNamePermit(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckAnchorSetNamePermit", reflect.TypeOf((*MockIAddFansSubscriber)(nil).CheckAnchorSetNamePermit), arg0, arg1)
}

// Close mocks base method.
func (m *MockIAddFansSubscriber) Close() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Close")
}

// Close indicates an expected call of Close.
func (mr *MockIAddFansSubscriberMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockIAddFansSubscriber)(nil).Close))
}

// SendSetGroupNameTTMsg mocks base method.
func (m *MockIAddFansSubscriber) SendSetGroupNameTTMsg(arg0 context.Context, arg1 uint32, arg2 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendSetGroupNameTTMsg", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// SendSetGroupNameTTMsg indicates an expected call of SendSetGroupNameTTMsg.
func (mr *MockIAddFansSubscriberMockRecorder) SendSetGroupNameTTMsg(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendSetGroupNameTTMsg", reflect.TypeOf((*MockIAddFansSubscriber)(nil).SendSetGroupNameTTMsg), arg0, arg1, arg2)
}
