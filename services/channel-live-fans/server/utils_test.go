package server

import (
	"golang.52tt.com/protocol/app"
	"golang.52tt.com/protocol/services/channellivefans"
	"golang.52tt.com/services/channel-live-fans/mongo"
	"reflect"
	"testing"
)

func Test_fillAppPlateConfigMsg(t *testing.T) {
	type args struct {
		fansPlateInfo channellivefans.FansPlateInfo
	}
	tests := []struct {
		name string
		args args
		want *app.FansPlateInfo
	}{
		{
			name: "fillAppPlateConfigMsg",
			args: args{fansPlateInfo: getCommonPlateConfig(uint32(1), false)},
			want: &app.FansPlateInfo{
				Type: uint32(app.FansPlateInfo_E_PLATE_COMMON),
				CommonPlateInfo: &app.CommonPlateInfo{
					CommonImgUrl:     CommonUnActivePlateImgUrl1,
					LevelFontColor:   CommonUnActivePlateLevelFontColor,
					LevelShadowColor: "",
				},
				NamePlateInfo: nil,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := fillAppPlateConfigMsg(tt.args.fansPlateInfo); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("fillAppPlateConfigMsg() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_transPlateConfig(t *testing.T) {
	type args struct {
		plateConfig mongo.PlateConfigInfos
	}
	tests := []struct {
		name string
		args args
		want NamePlateConfigInfos
	}{
		{
			name: "transPlateConfig",
			args: args{plateConfig: MongoNamePlateInfo},
			want: transPlateConfig(MongoNamePlateInfo),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := transPlateConfig(tt.args.plateConfig); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("transPlateConfig() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_getCommonPlateConfig(t *testing.T) {
	type args struct {
		fansLevel    uint32
		isActiveFans bool
	}
	tests := []struct {
		name string
		args args
		want channellivefans.FansPlateInfo
	}{
		{
			name: "getCommonPlateConfig",
			args: args{fansLevel: 1, isActiveFans: false},
			want: channellivefans.FansPlateInfo{
				Type: uint32(channellivefans.FansPlateInfo_E_PLATE_COMMON),
				CommonPlateInfo: &channellivefans.CommonPlateInfo{
					CommonImgUrl:     CommonUnActivePlateImgUrl1,
					LevelFontColor:   CommonUnActivePlateLevelFontColor,
					LevelShadowColor: "",
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := getCommonPlateConfig(tt.args.fansLevel, tt.args.isActiveFans); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("getCommonPlateConfig() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_getPlateConfigByAntive(t *testing.T) {
	type args struct {
		isActiveFans      bool
		fansLevel         uint32
		anchorPlateConfig mongo.PlateConfigInfos
	}
	tests := []struct {
		name  string
		args  args
		want  NamePlateConfigInfos
		want1 CommonPlateConfig
	}{
		{
			name: "getPlateConfigByAntive",
			args: args{
				isActiveFans:      false,
				fansLevel:         1,
				anchorPlateConfig: MongoNamePlateInfo,
			},
			want:  DefaultNameGrayPlateInfo,
			want1: mapLevel2CommonUnActivePlateConfig[1],
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, got1 := getPlateConfigByAntive(tt.args.isActiveFans, tt.args.fansLevel, tt.args.anchorPlateConfig)
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("getPlateConfigByAntive() got = %v, want %v", got, tt.want)
			}
			if !reflect.DeepEqual(got1, tt.want1) {
				t.Errorf("getPlateConfigByAntive() got1 = %v, want %v", got1, tt.want1)
			}
		})
	}
}

func Test_getPlateParam(t *testing.T) {
	type args struct {
		isActiveFans  bool
		fansLevel     uint32
		fontCnt       uint32
		namePlateConf NamePlateConfigInfos
	}
	tests := []struct {
		name  string
		args  args
		want  string
		want1 string
		want2 string
		want3 string
		want4 uint32
		want5 uint32
	}{
		{
			name: "Test_getPlateParam",
			args: args{
				isActiveFans:  false,
				fansLevel:     1,
				fontCnt:       1,
				namePlateConf: DefaultNameGrayPlateInfo,
			},
			want:  CommonUnActivePlateLevelFontColor,
			want1: "https://ga-album-cdnqn.52tt.com/fansgroup/oldFansPlate20231219/ic_voice_live_fans_brand_grey_lv1_1font.png?FansPlateLevelW=24",
			want2: mapLevelShadowColor[1],
			want3: "",
			want4: 38,
			want5: 20,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, got1, got2, got3, got4, got5 := getPlateParam(tt.args.isActiveFans, tt.args.fansLevel, tt.args.fontCnt, 1, tt.args.namePlateConf)
			if got != tt.want {
				t.Errorf("getPlateParam() got = %v, want %v", got, tt.want)
			}
			if got1 != tt.want1 {
				t.Errorf("getPlateParam() got1 = %v, want %v", got1, tt.want1)
			}
			if got2 != tt.want2 {
				t.Errorf("getPlateParam() got2 = %v, want %v", got2, tt.want2)
			}
			if got3 != tt.want3 {
				t.Errorf("getPlateParam() got3 = %v, want %v", got3, tt.want3)
			}
			if got4 != tt.want4 {
				t.Errorf("getPlateParam() got4 = %v, want %v", got4, tt.want4)
			}
			if got5 != tt.want5 {
				t.Errorf("getPlateParam() got5 = %v, want %v", got5, tt.want5)
			}
		})
	}
}

func Test_getNamePlateConfig(t *testing.T) {
	type args struct {
		fanInfo           FansInfo
		groupName         string
		anchorPlateConfig mongo.PlateConfigInfos
	}

	fanInfo := FansInfo{
		AnchorUid:       anchorUid,
		FansUid:         fansUid,
		FansLevel:       1,
		KnightType:      0,
		IsActiveFans:    false,
		IsFinishMission: false,
	}

	tests := []struct {
		name string
		args args
		want channellivefans.FansPlateInfo
	}{
		{
			name: "getNamePlateConfig",
			args: args{
				fanInfo:           fanInfo,
				groupName:         "团",
				anchorPlateConfig: MongoNamePlateInfo,
			},
			want: getNamePlateConfig(fanInfo, "团", MongoNamePlateInfo, 1),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := getNamePlateConfig(tt.args.fanInfo, tt.args.groupName, tt.args.anchorPlateConfig, 1); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("getNamePlateConfig() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_getAnchorNamePlateConfig(t *testing.T) {
	type args struct {
		anchorUid         uint32
		groupName         string
		anchorPlateConfig mongo.PlateConfigInfos
	}
	tests := []struct {
		name string
		args args
		want channellivefans.FansPlateInfo
	}{
		{
			name: "getAnchorNamePlateConfig",
			args: args{
				anchorUid:         anchorUid,
				groupName:         "团",
				anchorPlateConfig: MongoNamePlateInfo,
			},
			want: getAnchorNamePlateConfig(anchorUid, "团", MongoNamePlateInfo),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := getAnchorNamePlateConfig(tt.args.anchorUid, tt.args.groupName, tt.args.anchorPlateConfig); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("getAnchorNamePlateConfig() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_getAnchorNamePlate(t *testing.T) {
	type args struct {
		groupName string
		plateInfo mongo.PlateConfigInfos
	}
	tests := []struct {
		name string
		args args
		want channellivefans.GroupNamePlateInfo
	}{
		{
			name: "getAnchorNamePlate",
			args: args{
				groupName: "团",
				plateInfo: MongoNamePlateInfo,
			},
			want: getAnchorNamePlate("团", MongoNamePlateInfo),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := getAnchorNamePlate(tt.args.groupName, tt.args.plateInfo); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("getAnchorNamePlate() = %v, want %v", got, tt.want)
			}
		})
	}
}

/*
func Test_getFansLevelInfo(t *testing.T) {
	type args struct {
		loveValue uint32
	}
	tests := []struct {
		name  string
		args  args
		want  uint32
		want1 uint32
		want2 uint32
		want3 float32
	}{
		{
			name:  "getFansLevelInfo",
			args:  args{loveValue: 100},
			want:  1,
			want1: 2,
			want2: 1100,
			want3: float32(100) / float32(1200),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, got1, got2, got3 := getFansLevelInfo(tt.args.loveValue)
			if got != tt.want {
				t.Errorf("getFansLevelInfo() got = %v, want %v", got, tt.want)
			}
			if got1 != tt.want1 {
				t.Errorf("getFansLevelInfo() got1 = %v, want %v", got1, tt.want1)
			}
			if got2 != tt.want2 {
				t.Errorf("getFansLevelInfo() got2 = %v, want %v", got2, tt.want2)
			}
			if got3 != tt.want3 {
				t.Errorf("getFansLevelInfo() got3 = %v, want %v", got3, tt.want3)
			}
		})
	}
}
*/

func Test_checkHasChineseChar(t *testing.T) {
	type args struct {
		str string
	}
	tests := []struct {
		name string
		args args
		want bool
	}{
		{
			name: "checkHasChineseChar",
			args: args{str: "团"},
			want: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := checkHasChineseChar(tt.args.str); got != tt.want {
				t.Errorf("checkHasChineseChar() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_getGroupNameLen(t *testing.T) {
	type args struct {
		groupName string
	}
	tests := []struct {
		name string
		args args
		want uint32
	}{
		{
			name: "getGroupNameLen",
			args: args{groupName: "团"},
			want: 3,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := getGroupNameLen(tt.args.groupName); got != tt.want {
				t.Errorf("getGroupNameLen() = %v, want %v", got, tt.want)
			}
		})
	}
}
