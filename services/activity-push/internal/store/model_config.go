package store

import "time"

// ActivityPushWindowConfig 活动弹窗配置表
type ActivityPushWindowConfig struct {
	Id            uint32    `gorm:"column:id" db:"id" json:"id" form:"id"`
	Name          string    `gorm:"column:name" db:"name" json:"name" form:"name"`                                         //活动推送名称
	Type          uint32    `gorm:"column:type" db:"type" json:"type" form:"type"`                                         //资源类型：1、静态；2、动态
	SType         uint32    `gorm:"column:s_type" db:"s_type" json:"s_type" form:"s_type"`                                 //规格类型：0、标准；1、大赛事
	CType         uint32    `gorm:"column:c_type" db:"c_type" json:"c_type" form:"c_type"`                                 //内容类型：1、无用户弹窗；2、单用户弹窗；3、双用户弹窗
	NickCol       string    `gorm:"column:nick_col" db:"nick_col" json:"nick_col" form:"nick_col"`                         //昵称颜色配置
	Url           string    `gorm:"column:url" db:"url" json:"url" form:"url"`                                             //资源URL
	Md5           string    `gorm:"column:md5" db:"md5" json:"md5" form:"md5"`                                             //资源MD5值
	Pic           string    `gorm:"column:pic" db:"pic" json:"pic" form:"pic"`                                             //资源浏览图
	HasBanner     uint32    `gorm:"column:has_banner" db:"has_banner" json:"has_banner" form:"has_banner"`                 //是否有标题资源：0、没有；1、有
	BannerUrl     string    `gorm:"column:banner_url" db:"banner_url" json:"banner_url" form:"banner_url"`                 //标题资源地址
	HasField      uint32    `gorm:"column:has_field" db:"has_field" json:"has_field" form:"has_field"`                     //是否包含文案：0、不包含；1、包含
	FieldCol      string    `gorm:"column:field_col" db:"field_col" json:"field_col" form:"field_col"`                     //文案颜色
	ButtonUrl     string    `gorm:"column:button_url" db:"button_url" json:"button_url" form:"button_url"`                 //按钮背景图片地址
	ButtonMd5     string    `gorm:"column:button_md5" db:"button_md5" json:"button_md5" form:"button_md5"`                 //按钮背景图片md5值
	NeedJump      uint32    `gorm:"column:need_jump" db:"need_jump" json:"need_jump" form:"need_jump"`                     //是否需要跳转：0、不需要；1、需要
	JumpNotice    string    `gorm:"column:jump_notice" db:"jump_notice" json:"jump_notice" form:"jump_notice"`             //跳转文案
	JumpCol       string    `gorm:"column:jump_col" db:"jump_col" json:"jump_col" form:"jump_col"`                         //跳转文案文字颜色
	JumpUrl       string    `gorm:"column:jump_url" db:"jump_url" json:"jump_url" form:"jump_url"`                         //跳转链接地址
	JumpUrlNew    string    `gorm:"column:jump_url_new" db:"jump_url_new" json:"jump_url_new" form:"jump_url_new"`         //跳转链接地址
	JumpImgUrl    string    `gorm:"column:jump_img_url" db:"jump_img_url" json:"jump_img_url" form:"jump_img_url"`         //跳转按钮背景图片地址
	JumpImgMd5    string    `gorm:"column:jump_img_md5" db:"jump_img_md5" json:"jump_img_md5" form:"jump_img_md5"`         //跳转按钮背景图片md5值
	DisplayTime   uint32    `gorm:"column:display_time" db:"display_time" json:"display_time" form:"display_time"`         //弹窗持续时间
	PushType      uint32    `gorm:"column:push_type" db:"push_type" json:"push_type" form:"push_type"`                     //推送类型：1、按照工会房间进行推送；2、按照房间进行推送；3、按照用户进行推送
	Remark        string    `gorm:"column:remark" db:"remark" json:"remark" form:"remark"`                                 //备注
	ConfigUrl     string    `gorm:"column:config_url" db:"config_url" json:"config_url" form:"config_url"`                 //调用地址链接(废弃，移到新表)
	DelFlag       uint32    `gorm:"column:del_flag" db:"del_flag" json:"del_flag" form:"del_flag"`                         //删除标记：0、未删除；1、已删除
	Creator       string    `gorm:"column:creator" db:"creator" json:"creator" form:"creator"`                             //创建人
	CreateTime    time.Time `gorm:"column:create_time" db:"create_time" json:"create_time" form:"create_time"`             //记录创建时间
	Updater       string    `gorm:"column:updater" db:"updater" json:"updater" form:"updater"`                             //更新人
	UpdateTime    time.Time `gorm:"column:update_time" db:"update_time" json:"update_time" form:"update_time"`             //记录更新时间
	DisplayDevice string    `gorm:"column:display_device" db:"display_device" json:"display_device" form:"display_device"` //弹窗展示设备
	DisplayAll    bool      `gorm:"column:display_all" db:"display_all" json:"display_all" form:"display_all"`             //是否所有客户端显示
}

func (a *ActivityPushWindowConfig) TableName() string {
	return "activity_push_window_config"
}

// CreateActivityPushWindowConfigTable 铭牌配置表建表语句
var CreateActivityPushWindowConfigTable = `
CREATE TABLE activity_push_window_config (
   id BIGINT ( 20 ) UNSIGNED NOT NULL AUTO_INCREMENT,
   name VARCHAR ( 256 ) NOT NULL COMMENT '活动推送名称',
   type TINYINT ( 8 ) NOT NULL COMMENT '资源类型：1、静态；2、动态',
   s_type TINYINT ( 8 ) NOT NULL COMMENT '规格类型：0、标准；1、大赛事',
   c_type TINYINT ( 8 ) NOT NULL COMMENT '内容类型：1、无用户弹窗；2、单用户弹窗；3、双用户弹窗',
   nick_col VARCHAR ( 512 ) COMMENT '昵称颜色配置',
   url VARCHAR ( 512 ) NOT NULL COMMENT '资源URL',
   md5 VARCHAR ( 512 ) NOT NULL COMMENT '资源MD5值',
   pic VARCHAR ( 512 ) NOT NULL COMMENT '资源浏览图',
   has_banner TINYINT ( 8 ) NOT NULL COMMENT '是否有标题资源：0、没有；1、有',
   banner_url VARCHAR ( 512 ) COMMENT '标题资源地址',
   has_field TINYINT ( 8 ) NOT NULL COMMENT '是否包含文案：0、不包含；1、包含',
   field_col VARCHAR ( 512 ) COMMENT '文案颜色',
   button_url VARCHAR ( 512 ) NOT NULL COMMENT '按钮背景图片地址',
   button_md5 VARCHAR ( 512 ) NOT NULL COMMENT '按钮背景图片md5值',
   need_jump TINYINT ( 8 ) NOT NULL COMMENT '是否需要跳转：0、不需要；1、需要',
   jump_notice VARCHAR ( 512 ) COMMENT '跳转文案',
   jump_col VARCHAR ( 512 ) COMMENT '跳转文案文字颜色',
   jump_url VARCHAR ( 512 ) COMMENT '跳转链接地址',
   jump_img_url VARCHAR ( 512 ) COMMENT '跳转按钮背景图片地址',
   jump_img_md5 VARCHAR ( 512 ) COMMENT '跳转按钮背景图片md5值',
   display_time INT ( 10 ) UNSIGNED NOT NULL COMMENT '弹窗持续时间',
   push_type TINYINT ( 8 ) NOT NULL COMMENT '推送类型：1、按照工会房间进行推送；2、按照房间进行推送；3、按照用户进行推送',
   remark text COMMENT '备注',
   config_url VARCHAR ( 512 ) COMMENT '调用地址链接(废弃，移到新表)',
   del_flag TINYINT ( 8 ) NOT NULL COMMENT '删除标记：0、未删除；1、已删除',
   creator VARCHAR ( 512 ) NOT NULL COMMENT '创建人',
   create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
   updater VARCHAR ( 512 ) NOT NULL COMMENT '更新人',
   update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',
   jump_url_new VARCHAR ( 1024 ) COMMENT '(新)跳转链接地址',
   display_device VARCHAR ( 512 ) COMMENT '弹窗展示设备',
   display_all TINYINT ( 8 ) NOT NULL DEFAULT 0 COMMENT '是否所有客户端显示',
   PRIMARY KEY ( id ),
   KEY idx_name ( id, name, del_flag ),
   KEY idx_not_del ( id, del_flag )
) ENGINE = INNODB DEFAULT CHARSET = utf8 COMMENT = '活动弹窗配置表';
`

// ActivityPushWindowExtConfig 活动推送弹窗扩展配置表
type ActivityPushWindowExtConfig struct {
	Id         uint32    `gorm:"column:id" db:"id" json:"id" form:"id"`
	PushId     uint32    `gorm:"column:push_id" db:"push_id" json:"push_id" form:"push_id"`                 //活动推送ID
	MarketId   uint32    `gorm:"column:market_id" db:"market_id" json:"market_id" form:"market_id"`         //市场ID
	DeviceId   uint32    `gorm:"column:device_id" db:"device_id" json:"device_id" form:"device_id"`         //设备ID
	ConfigUrl  string    `gorm:"column:config_url" db:"config_url" json:"config_url" form:"config_url"`     //调用地址链接
	UpdateTime time.Time `gorm:"column:update_time" db:"update_time" json:"update_time" form:"update_time"` //记录更新时间
}

func (a *ActivityPushWindowExtConfig) TableName() string {
	return "activity_push_window_ext_config"
}

// CreateActivityPushWindowExtConfigTable 活动推送弹窗扩展配置表建表语句
var CreateActivityPushWindowExtConfigTable = `
CREATE TABLE activity_push_window_ext_config (
	id BIGINT ( 20 ) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',
	push_id BIGINT ( 20 ) UNSIGNED NOT NULL COMMENT '活动推送ID',
	market_id BIGINT ( 20 ) UNSIGNED NOT NULL COMMENT '市场ID',
	device_id BIGINT ( 20 ) UNSIGNED NOT NULL COMMENT '设备ID',
	config_url VARCHAR ( 512 ) NOT NULL COMMENT '调用地址链接',
	update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',
	PRIMARY KEY ( id ),
	UNIQUE KEY uniq_push_id_market_id_device_id ( push_id, market_id, device_id )
) ENGINE = INNODB DEFAULT CHARSET = utf8 COMMENT = '活动推送弹窗扩展配置表';
`
