package server

import (
	"context"
	"golang.52tt.com/pkg/log"
	channelPb "golang.52tt.com/protocol/app/channel"
	reconcile_v2 "golang.52tt.com/protocol/services/reconcile-v2"
	"golang.52tt.com/protocol/services/userpresent"
	"golang.52tt.com/protocol/services/yuyingold"
	"golang.52tt.com/services/yuyin-gold/mysql"
	"time"
)

func (s *YuyinGoldServer) GetGoldOrderCount(ctx context.Context, in *reconcile_v2.TimeRangeReq) (*reconcile_v2.CountResp, error) {
	log.InfoWithCtx(ctx, "GetGoldOrderCount req: %+v", in)
	out := &reconcile_v2.CountResp{}
	totalCnt, err := s.MysqlStore.GetVerifyYuyinCnt(ctx, in.GetBeginTime(), in.GetEndTime())
	if err != nil {
		return out, err
	}
	sumFee, err := s.MysqlStore.GetVerifyYuyinSum(ctx, in.GetBeginTime(), in.GetEndTime())
	if err != nil {
		return out, err
	}
	out.Count = totalCnt
	out.Value = sumFee
	log.InfoWithCtx(ctx, "return GetGoldOrderCount req: %+v, rsp:%+v", in, out)
	return out, nil
}

func (s *YuyinGoldServer) GetGoldOrderList(ctx context.Context, in *reconcile_v2.TimeRangeReq) (*reconcile_v2.OrderIdsResp, error) {
	log.InfoWithCtx(ctx, "GetGoldOrderList req: %+v", in)
	out := &reconcile_v2.OrderIdsResp{}
	items, err := s.MysqlStore.GetVerifyYuyin(ctx, in.GetBeginTime(), in.GetEndTime())
	if err != nil {
		return out, err
	}
	for _, item := range items {
		out.OrderIds = append(out.OrderIds, item.OrderId)
	}
	log.InfoWithCtx(ctx, "return GetGoldOrderList req: %+v, rsp:%+v", in, out)
	return out, nil
}

func (s *YuyinGoldServer) ReplaceOrder(ctx context.Context, in *reconcile_v2.ReplaceOrderReq) (*reconcile_v2.EmptyResp, error) {
	log.InfoWithCtx(ctx, "ReplaceOrder req: %+v", in)
	out := &reconcile_v2.EmptyResp{}

	presentOrderResp, sErr := s.reconcilePresentClient.GetOrderInfoById(ctx, in.GetOrderId())
	if sErr != nil {
		log.Warnf("ReplaceOrder GetOrderInfoById err:%v, in:%s", sErr, in.GetOrderId())
		return out, sErr
	}
	presentOrder := presentOrderResp.OrderInfo

	if presentOrder.GetChannelType() != uint32(channelPb.ChannelType_RADIO_LIVE_CHANNEL_TYPE) {
		log.ErrorWithCtx(ctx, "ReplaceOrder GetChannelType != uint32(channelPb.ChannelType_RADIO_LIVE_CHANNEL_TYPE) in:%+v", presentOrder.GetChannelType())
		return out, nil
	}

	guildId := presentOrder.GetGuildId()
	if guildId == 0 {
		channelResp, sErr := s.channelClient.GetChannelDetailInfo(ctx, 0, presentOrder.ChanelId)
		if sErr != nil {
			log.Warnf("ReplaceOrder GetChannelDetailInfo err:%v, in:%s", sErr, presentOrder.ChanelId)
			return out, sErr
		}
		contractResp, sErr := s.anchorContractGoCli.GetUserContractCacheInfo(ctx, 0, *channelResp.CreaterUid)
		if sErr != nil {
			log.Warnf("ReplaceOrder GetUserContractCacheInfo err:%v, in:%s", sErr, presentOrder.ChanelId)
			return out, sErr
		}
		guildId = contractResp.GetContract().GetGuildId()
	}

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()
	totalPrice := int64(presentOrder.GetTotalPrice())
	var price uint32
	if presentOrder.ItemCount > 0 {
		price = uint32(totalPrice) / presentOrder.ItemCount
	}
	orderInfo := &yuyingold.OrderInfoT{
		Uid:         presentOrder.FromUid,
		TargetUid:   presentOrder.ToUid,
		OrderId:     presentOrder.OrderId,
		ChannelId:   presentOrder.ChanelId,
		ChannelType: presentOrder.ChanelId,
		GuildId:     guildId,
		SendTime:    uint32(presentOrder.CreateTime),
		ItemId:      presentOrder.ItemId,
		ItemCount:   presentOrder.ItemCount,
		Price:       price,
		PriceType:   uint32(userpresent.PresentPriceType_PRESENT_PRICE_TBEAN),
	}
	log.InfoWithCtx(ctx, "ReplaceOrder reconcile order info: %+v", orderInfo)

	goldDiamondValue := totalPrice / 20
	err := s.MysqlStore.AddVerifyYuyin(ctx, presentOrder.GetFromUid(), presentOrder.GetChanelId(), presentOrder.GetOrderId(), goldDiamondValue, int64(presentOrder.GetCreateTime()), totalPrice, mysql.VerifyStatusInit, "addorder")
	if err != nil {
		log.Warnf("ReplaceOrder, AddVerifyYuyin err:%v, in:%s", err, orderInfo.String())
	}
	err, _ = s.kafkaSub.HandlerYuyinGoldEvent(ctx, orderInfo, 0, 0, "addorder")
	if err != nil {
		_ = s.MysqlStore.UpdateVerifyYuyinStatus(ctx, presentOrder.GetOrderId(), int64(presentOrder.GetCreateTime()), mysql.VerifyStatusFailed)
	} else {
		_ = s.MysqlStore.UpdateVerifyYuyinStatus(ctx, presentOrder.GetOrderId(), int64(presentOrder.GetCreateTime()), mysql.VerifyStatusSuccess)
	}
	log.InfoWithCtx(ctx, "return ReplaceOrder req: %+v, rsp:%+v", in, out)
	return out, nil
}
