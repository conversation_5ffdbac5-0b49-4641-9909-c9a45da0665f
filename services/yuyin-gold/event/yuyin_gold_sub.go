package event

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"github.com/Shopify/sarama"
	"gitlab.ttyuyin.com/avengers/tyr/compatible/proto"
	"gitlab.ttyuyin.com/golang/gudetama/log"
	"golang.52tt.com/clients/anchorcontract"
	"golang.52tt.com/clients/channel"
	"golang.52tt.com/pkg/monitor/web"
	channelPb "golang.52tt.com/protocol/app/channel"
	"golang.52tt.com/protocol/services/minToolkit/kafka/pb/kafkapresent"
	"golang.52tt.com/protocol/services/userpresent"
	pb "golang.52tt.com/protocol/services/yuyingold"
	"golang.52tt.com/services/ugc/common/event"
	"golang.52tt.com/services/yuyin-gold/cache"
	"golang.52tt.com/services/yuyin-gold/mongo"
	"golang.52tt.com/services/yuyin-gold/mysql"
	send "golang.52tt.com/services/yuyin-gold/tools/monkey-send-chat"
)

const (
	topicTypeGoldAward = "present_event_v2"
)

type LocalCache struct {
	NoBonusGuildMap map[uint32]bool
}

func (l *LocalCache) GetNoBonusGuildMap() map[uint32]bool {
	if l == nil {
		return nil
	}
	return l.NoBonusGuildMap
}

type YuyinGoldSubscriber struct {
	*event.KafkaSub
	mysqlStore     *mysql.Store
	mDao           *mongo.MongoDao
	channelClient  *channel.Client
	contractClient *anchorcontract.Client
	CacheClient    *cache.YuyinGoldCache
	localCache     *LocalCache
}

func NewYuyinGoldSubscriber(clientId, groupId string, topics, brokers []string, mysqlStore *mysql.Store, mDao *mongo.MongoDao, contractClient *anchorcontract.Client, CacheClient *cache.YuyinGoldCache, channelClient *channel.Client, localCache *LocalCache) (*YuyinGoldSubscriber, error) {

	conf := sarama.NewConfig()
	conf.ClientID = clientId
	conf.Consumer.Offsets.Initial = sarama.OffsetOldest
	conf.Consumer.Return.Errors = true

	kafkaSub, err := event.NewKafkaSub(topicTypeGoldAward, brokers, groupId, topics, conf)
	if err != nil {
		log.Errorf("Failed to create kafka-subscriber: %s", err.Error())
		return nil, err
	}

	sub := &YuyinGoldSubscriber{
		KafkaSub:       kafkaSub,
		mysqlStore:     mysqlStore,
		mDao:           mDao,
		contractClient: contractClient,
		CacheClient:    CacheClient,
		localCache:     localCache,
	}

	sub.SetMessageProcessor(sub.handlerEvent)

	sub.channelClient = channelClient
	return sub, nil
}

func (s *YuyinGoldSubscriber) Close() {
	s.KafkaSub.Stop()
}

func (s *YuyinGoldSubscriber) handlerEvent(msg *sarama.ConsumerMessage) (error, bool) {
	switch msg.Topic {
	case topicTypeGoldAward:
		return s.handlerYuyinGoldEventProxy(msg)
	default:
	}
	return nil, false
}

func (s *YuyinGoldSubscriber) handlerYuyinGoldEventProxy(msg *sarama.ConsumerMessage) (error, bool) {
	web.IncrKafka(1)
	awardEvent := &kafkapresent.PresentEvent{}
	err := proto.Unmarshal(msg.Value, awardEvent)
	if err != nil {
		log.Errorf("handlerYuyinGoldEventProxy Failed to proto.Unmarshal err: %s", err.Error())
		return err, false
	}

	if awardEvent.GetPriceType() != uint32(userpresent.PresentPriceType_PRESENT_PRICE_TBEAN) {
		log.Warnf("handlerYuyinGoldEvent, NotHandle, PriceType:%d", awardEvent.GetPriceType())
		return nil, false
	}
	orderInfo := &pb.OrderInfoT{
		Uid:         awardEvent.GetUid(),
		TargetUid:   awardEvent.GetTargetUid(),
		OrderId:     awardEvent.GetOrderId(),
		ChannelId:   awardEvent.GetChannelId(),
		ChannelType: awardEvent.GetChannelType(),
		GuildId:     awardEvent.GetGuildId(),
		SendTime:    awardEvent.GetSendTime(),
		ItemId:      awardEvent.GetItemId(),
		ItemCount:   awardEvent.GetItemCount(),
		Price:       awardEvent.GetPrice(),
		PriceType:   awardEvent.GetPriceType(),
	}
	log.Debugf("handlerYuyinGoldEventProxy %s, pid:%d, offset:%d", awardEvent.String(), msg.Partition, msg.Offset)
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()
	totalPrice := int64(awardEvent.GetPrice()) * int64(awardEvent.GetItemCount())
	goldDiamondValue := totalPrice / 20
	if awardEvent.GetChannelType() == uint32(channelPb.ChannelType_RADIO_LIVE_CHANNEL_TYPE) {
		err = s.mysqlStore.AddVerifyYuyin(ctx, orderInfo.GetUid(), orderInfo.GetChannelId(), orderInfo.GetOrderId(), goldDiamondValue, int64(orderInfo.GetSendTime()), totalPrice, mysql.VerifyStatusInit, "event")
		if err != nil {
			log.Warnf("handlerYuyinGoldEventProxy, AddVerifyYuyin err:%v, awardEvent:%s", err, awardEvent.String())
		}
		err, ret := s.HandlerYuyinGoldEvent(ctx, orderInfo, msg.Partition, msg.Offset, "event")
		if err != nil {
			s.mysqlStore.UpdateVerifyYuyinStatus(ctx, orderInfo.GetOrderId(), int64(orderInfo.GetSendTime()), mysql.VerifyStatusFailed)
		} else {
			s.mysqlStore.UpdateVerifyYuyinStatus(ctx, orderInfo.GetOrderId(), int64(orderInfo.GetSendTime()), mysql.VerifyStatusSuccess)
		}
		return err, ret
	}
	return nil, false
}

func (s *YuyinGoldSubscriber) HandlerYuyinGoldEvent(ctx context.Context, awardEvent *pb.OrderInfoT, partition int32, offset int64, strType string) (error, bool) {

	log.Debugf("handlerYuyinGoldEvent come %s, pid:%d, offset:%d, type:%s, orderid:%s", awardEvent.String(), partition, offset, strType, awardEvent.GetOrderId())

	// if strType == "event" {
	//	return errors.New("errors"), false
	// }

	if awardEvent.GetChannelType() != uint32(channelPb.ChannelType_RADIO_LIVE_CHANNEL_TYPE) {
		log.Warnf("handlerAwardYuyinGoldEvent, NotHandle,  ChannelType:%d, orderid:%s", awardEvent.GetChannelType(), awardEvent.GetOrderId())
		return nil, false
	}

	/*if awardEvent.GetGuildId() == 1664253 || awardEvent.GetGuildId() == 1696486 || awardEvent.GetGuildId() == 2166673 ||
		awardEvent.GetGuildId() == 2281636 || awardEvent.GetGuildId() == 1384231 || awardEvent.GetGuildId() == 2248919 ||
		awardEvent.GetGuildId() == 1911638 || awardEvent.GetGuildId() == 2162220 || awardEvent.GetGuildId() == 1359997 ||
		awardEvent.GetGuildId() == 2203992 || awardEvent.GetGuildId() == 2203988 {
		log.Warnf("handlerAwardYuyinGoldEvent, NotHandle, unuse Guild:%d, PriceType:%d", awardEvent.GetGuildId(), awardEvent.GetPriceType())
		return nil, false
	}*/

	totalPrice := int64(awardEvent.GetPrice()) * int64(awardEvent.GetItemCount())
	goldDiamondValue := totalPrice / 20
	boughtTime := int64(int64(awardEvent.GetSendTime()) * 1000)

	fIncome := float64(totalPrice / 20)

	extandDetail := mysql.ExtandDetailT{
		Fee:        totalPrice,
		Uid:        awardEvent.GetUid(),
		ReceiveUid: awardEvent.GetTargetUid(),
		GiftId:     awardEvent.GetItemId(),
		ChannelId:  awardEvent.GetChannelId(),
		Order:      awardEvent.GetOrderId(),
	}

	nSendTime := awardEvent.GetSendTime()
	sendTime := time.Unix(int64(nSendTime), 0)

	updateTime := time.Now()
	if updateTime.Hour() >= 1 && sendTime.Day() != updateTime.Day() {
		log.Errorf("handlerYuyinGoldEvent Get Err Time, nowTime:%s, boughtTime:%s", updateTime.Format("20060102"), sendTime.Format("20060102"))
	}

	extandInfo := &mysql.ExtandInfoT{
		ExtandInfo: extandDetail,
		Diamond:    fIncome,
	}
	jsonExtand, err := json.Marshal(extandInfo)
	if err != nil {
		log.Errorf("json Marshal err:%s, %+v", err.Error(), extandInfo)
	}

	channelInfo, err := s.channelClient.GetChannelDetailInfo(ctx, awardEvent.GetUid(), awardEvent.GetChannelId())
	if err != nil {
		log.Errorf("handlerYuyinGoldEvent, channelClient.GetChannelDetailInfo err:%s, channelid:%d, orderid:%s", err.Error(), awardEvent.GetChannelId(), awardEvent.GetOrderId())
		errInfo := fmt.Sprintf("GetChannelDetailInfo err:%v", err)
		send.GetMonkey().SendToChat("语音流水", errInfo+"==order_id:"+awardEvent.GetOrderId())
		return err, true
	}

	var contractGuildId uint32
	var contractActorId = channelInfo.GetCreaterUid()
	if strType == "addorder" {
		logRsp, err := s.contractClient.GetSignContractChangeLog(ctx, channelInfo.GetCreaterUid())
		if err != nil {
			log.Errorf("GetSignContractChangeLog err:%s, uid:%d, orderid:%s", err.Error(), channelInfo.GetCreaterUid(), awardEvent.GetOrderId())
			errInfo := fmt.Sprintf("GetSignContractChangeLog err:%v", err)
			send.GetMonkey().SendToChat("语音流水", errInfo+"==order_id:"+awardEvent.GetOrderId())
			return err, false // 对帐系统补单
		}
		bFound := false
		nLogCnt := len(logRsp.GetLogList())
		for i := nLogCnt - 1; i >= 0; i-- {
			logInfo := logRsp.GetLogList()[i]
			if logInfo.GetCancelContractTime() == 0 {
				if awardEvent.GetSendTime() >= logInfo.GetSignTime() {
					contractGuildId = logInfo.GuildId
					contractActorId = logInfo.Uid
					bFound = true
					break
				}
			} else {
				if awardEvent.GetSendTime() >= logInfo.GetSignTime() && awardEvent.GetSendTime() <= logInfo.GetCancelContractTime() {
					contractGuildId = logInfo.GuildId
					contractActorId = logInfo.Uid
					bFound = true
					break
				}
			}
		}
		if !bFound {
			log.Errorf("handlerYuyinGoldEvent GetSignContractChangeLog err, anchorid:%d, loglist:%v, orderid:%s", channelInfo.GetCreaterUid(), logRsp.GetLogList(), awardEvent.GetOrderId())
			return nil, false
		}
		log.Warnf("handlerYuyinGoldEvent GetSignContractChangeLog, anchorid:%d, loglist:%v, orderid:%s", channelInfo.GetCreaterUid(), logRsp.GetLogList(), awardEvent.GetOrderId())

	} else {
		rsp, err := s.contractClient.GetUserContractInfo(ctx, awardEvent.GetTargetUid(), channelInfo.GetCreaterUid())
		if err != nil {
			errInfo := fmt.Sprintf("GetUserContractInfo err:%v", err)
			send.GetMonkey().SendToChat("语音流水", errInfo+"==order_id:"+awardEvent.GetOrderId())
			log.Errorf("handlerYuyinGoldEvent contractClient.GetUserContractInfo err:%s, targetid:%d, anchorid:%d, orderid:%s", err.Error(), awardEvent.GetTargetUid(), channelInfo.GetCreaterUid(), awardEvent.GetOrderId())
			// err = s.mysqlStore.AwardFail(ctx, rsp.GetContract().GetGuildId(), awardEvent.GetUid(), awardEvent.GetChannelId(), rsp.GetContract().GetActorUid(), awardEvent.GetOrderId(), goldDiamondValue, boughtTime, totalPrice, string(jsonExtand))
			/*err = s.mysqlStore.AwardFail(ctx, awardEvent.GetGuildId(), awardEvent.GetUid(), awardEvent.GetChannelId(), awardEvent.GetTargetUid(), awardEvent.GetOrderId(), goldDiamondValue, boughtTime, totalPrice, string(jsonExtand))
			if err != nil {
				if strings.Contains(err.Error(), "Duplicate entry") {
					log.Warnf("err boughttime:%d, nowtime:%d, handlerYuyinGoldEvent AwardFail duplicate %+v, orderid:%s\n", boughtTime, updateTime.Unix(), awardEvent, awardEvent.GetOrderId())
					return nil, false
				}
				log.Errorf("handlerYuyinGoldEvent AwardFail err:%s, offset:%d, orderid:%s", err.Error(), offset, awardEvent.GetOrderId())
				log.Errorln("handlerYuyinGoldEvent AwardFail info err:", rsp.GetContract().GetGuildId(), awardEvent.GetUid(), awardEvent.GetChannelId(), awardEvent.GetOrderId(), goldDiamondValue, boughtTime, totalPrice, string(jsonExtand))
				return err, true
			} else {
				log.Infof("boughttime:%d, nowtime:%d, handlerYuyinGoldEvent AwardFail insert %+v, orderid:%s\n", boughtTime, updateTime.Unix(), awardEvent, awardEvent.GetOrderId())
			}
			return nil, false*/
			return err, true
		}
		if rsp.GetContract().GetGuildId() == 0 {
			log.Errorf("handlerYuyinGoldEvent guildid err, Guild:%d, anchorid:%d, channelid:%d, orderid:%s", rsp.GetContract().GetGuildId(), channelInfo.GetCreaterUid(), awardEvent.GetChannelId(), awardEvent.GetOrderId())
			return nil, false
		}
		// add check time
		if time.Unix(int64(rsp.GetContract().GetExpireTime()), 0).Before(time.Unix(int64(awardEvent.GetSendTime()), 0)) {
			log.Errorf("handlerYuyinGoldEvent guildid err,  Guild:%d, anchorid:%d, channelid:%d, gitftime:%d, expiretime:%d, orderid:%s", rsp.GetContract().GetGuildId(), channelInfo.GetCreaterUid(), awardEvent.GetChannelId(), awardEvent.GetSendTime(), rsp.GetContract().GetExpireTime(), awardEvent.GetOrderId())
			return nil, false
		}

		contractGuildId = rsp.GetContract().GetGuildId()
		contractActorId = rsp.GetContract().GetActorUid()
	}
	if contractGuildId == 0 || contractActorId == 0 {
		log.Warnf("handlerYuyinGoldEvent, value 0 err, contractGuildId:0, contractActorId:0, awardEvent:%s, orderid:%s", awardEvent.String(), awardEvent.GetOrderId())
		return nil, false
	}

	if s.localCache.GetNoBonusGuildMap() != nil {
		if _, ok := s.localCache.GetNoBonusGuildMap()[contractGuildId]; ok {
			log.Warnf("handlerAwardYuyinGoldEvent, NotHandle, unuse Guild:%d, PriceType:%d", contractGuildId, awardEvent.GetPriceType())
			return nil, false
		}
	}

	_, bExist, err := s.mDao.GetAwardGuildRoomType(contractGuildId, mongo.YuyinType)
	if err != nil {
		errInfo := fmt.Sprintf("GetAwardGuildRoomType err:%v", err)
		send.GetMonkey().SendToChat("语音流水", errInfo+"==order_id:"+awardEvent.GetOrderId())
		log.Warnf("handlerYuyinGoldEvent, GetAwardGuildRoomType err:%s, Guild:%d, anchorid:%d, orderid:%s", err.Error(), contractGuildId, channelInfo.GetCreaterUid(), awardEvent.GetOrderId())
		return err, true
	}
	if !bExist {
		log.Debugf("handlerYuyinGoldEvent, GetAwardGuildRoomType Not Exist, Guild:%d, anchorid:%d, orderid:%s", contractGuildId, channelInfo.GetCreaterUid(), awardEvent.GetOrderId())
		return nil, false
	}

	err = s.mysqlStore.AwardDiamond(ctx, contractGuildId, awardEvent.GetUid(), awardEvent.GetChannelId(), contractActorId, awardEvent.GetOrderId(), goldDiamondValue, boughtTime, totalPrice, string(jsonExtand), false, 0)
	if err != nil {
		if strings.Contains(err.Error(), "Duplicate entry") {
			log.Warnf("err boughttime:%d, nowtime:%d, handlerYuyinGoldEvent AwardDiamond duplicate %+v, tableid:%d, %+v, orderid:%s\n", boughtTime, updateTime.Unix(), awardEvent, s.mysqlStore.GetTableIndex(contractGuildId), awardEvent, awardEvent.GetOrderId())
			return nil, false
		}
		errInfo := fmt.Sprintf("AwardDiamond err:%v", err)
		send.GetMonkey().SendToChat("语音流水", errInfo+"==order_id:"+awardEvent.GetOrderId())
		log.Errorf("handlerYuyinGoldEvent AwardDiamond err:%s, offset:%d, tableid:%d", err.Error(), offset, s.mysqlStore.GetTableIndex(contractGuildId))
		log.Errorln("handlerYuyinGoldEvent AwardDiamond info err:", contractGuildId, awardEvent.GetUid(), awardEvent.GetChannelId(), awardEvent.GetOrderId(), goldDiamondValue, boughtTime, totalPrice, string(jsonExtand))
		return err, true
	} else {
		log.Infof("boughttime:%d, nowtime:%d, handlerYuyinGoldEvent AwardDiamond insert %+v, guildid:%d, orderid:%s", boughtTime, updateTime.Unix(), awardEvent, contractGuildId, awardEvent.GetOrderId())
		s.CacheClient.IncrMonthRank(contractGuildId, awardEvent.GetUid(), totalPrice, updateTime)
	}

	return nil, true
}
