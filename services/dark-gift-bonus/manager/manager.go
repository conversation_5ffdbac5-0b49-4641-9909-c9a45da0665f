package manager

import (
    "context"
    "errors"
    "fmt"
    "gitlab.ttyuyin.com/avengers/tyr/compatible/proto"
    "gitlab.ttyuyin.com/avengers/tyr/pkg/cluster/timer"
    "golang.52tt.com/clients/backpack"
    backpacksender "golang.52tt.com/clients/backpack-sender"
    PushNotification "golang.52tt.com/clients/push-notification/v2"
    userPresent "golang.52tt.com/clients/userpresent"
    "golang.52tt.com/pkg/log"
    "golang.52tt.com/pkg/protocol"
    dark_gift_bonus_logic "golang.52tt.com/protocol/app/dark-gift-bonus-logic"
    gaPush "golang.52tt.com/protocol/app/push"
    bpb "golang.52tt.com/protocol/services/backpacksender"
    backpackPb "golang.52tt.com/protocol/services/backpacksvr"
    pb "golang.52tt.com/protocol/services/dark-gift-bonus"
    pushPB "golang.52tt.com/protocol/services/push-notification/v2"
    "golang.52tt.com/services/dark-gift-bonus/cache"
    "golang.52tt.com/services/dark-gift-bonus/conf"
    "golang.52tt.com/services/dark-gift-bonus/mysql"
    butils "golang.52tt.com/services/risk-control/backpack-sender/utils"
    "sync"
    "time"
)

type Mgr struct {
	shutDown chan interface{}
	mysql    mysql.IStore
	cache    cache.ICache
	sc       *conf.ServiceConfigT
	bc       conf.IBusinessConfManager
	wg       sync.WaitGroup
    timeD    *timer.Timer

	backpackCli    backpack.IClient
	bpSenderCli    backpacksender.IClient
	userPresentCli userPresent.IClient
	pushCli        PushNotification.IClient
}

func NewMgr(db mysql.IStore, redisCache cache.ICache, sc *conf.ServiceConfigT) (*Mgr, error) {
	var err error
	mgr := &Mgr{
		shutDown: make(chan interface{}),
		mysql:    db,
		cache:    redisCache,
		sc:       sc,
		bc:       conf.NewBusinessConfManager(),
	}

	mgr.backpackCli = backpack.NewClient()
	mgr.bpSenderCli, err = backpacksender.NewClient()
	if err != nil {
		log.Errorf("NewMgr fail to backpacksender.NewClient(). err:%v", err)
		return mgr, err
	}

	mgr.userPresentCli = userPresent.NewClient()
	mgr.pushCli, err = PushNotification.NewClient()
	if err != nil {
		log.Errorf("NewMgr fail to PushNotification.NewClient(). err:%v", err)
		return mgr, err
	}

	mgr.StartTimer()
	return mgr, nil
}

func (m *Mgr) ShutDown() {
    m.timeD.Stop()
	close(m.shutDown)
	m.bc.Close()
	m.wg.Wait()
}

func (m *Mgr) getBgItemInfo(ctx context.Context, uid, bgId uint32) (itemId, itemType, itemCount uint32, err error) {
	itemCfgResp, err := m.backpackCli.GetPackageItemCfg(ctx, uid, &backpackPb.GetPackageItemCfgReq{BgId: bgId})
	if err != nil {
		log.ErrorWithCtx(ctx, "getBgItemInfo fail to GetPackageItemCfg. uid:%d, bgId:%d, err:%v", uid, bgId, err)
		return 0, 0, 0, err
	}

	// 包裹中不是只有一个物品，异常
	if len(itemCfgResp.GetItemCfgList()) != 1 {
		return 0, 0, 0, errors.New("包裹配置错误")
	}

	cfg := itemCfgResp.GetItemCfgList()[0]

	return cfg.GetSourceId(), cfg.GetItemType(), cfg.GetItemCount(), nil
}

// 清除用户buff相关缓存
func (m *Mgr) clearUserBuffCache(ctx context.Context, uid, source uint32) error {

	buffTypeList := []uint32{uint32(pb.BuffType_DressUpDebris), uint32(pb.BuffType_GiftDebris), uint32(pb.BuffType_AnyDebris)}
	err := m.cache.ClearUserWeekContinueCnt(source, uid, buffTypeList)
	if err != nil {
		log.ErrorWithCtx(ctx, "clearUserBuffCache fail to ClearUserWeekContinueCnt. uid:%d, source:%d, err:%v", uid, source, err)
	}

	err = m.cache.DelUserLastBonusBuff(source, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "clearUserBuffCache fail to DelUserLastBonusBuff. uid:%d, source:%d, err:%v", uid, source, err)
	}

	return nil
}

func (m *Mgr) HandleGoldSmashEggBuff(ctx context.Context, uid, cid, bgId uint32, winningOrderId string) error {
	whiteSwitch, mapWhiteUid := m.bc.GetWhiteUidMap()
	if whiteSwitch {
		if !mapWhiteUid[uid] {
			// 开启了白名单但是不在白名单
			return nil
		}
	}

	handleCnt, err := m.cache.IncrHandleOrderCnt(winningOrderId)
	if err != nil {
		log.ErrorWithCtx(ctx, "HandleGoldSmashEggBuff fail to IncrHandleOrderCnt. winningOrderId:%s, uid:%d, bgId:%d, err:%v", winningOrderId, uid, bgId, err)
		return err
	}

	if handleCnt > 1 {
		// 已经处理过了，不再处理
		return nil
	}

	price := uint32(20) // 金色转转魔力球20元一个
	source := uint32(pb.DarkGiftBonusSource_GoldSmashEgg)

	// 增加本周消费金额
	currConsumePrice, err := m.cache.IncrUserWeekConsumePrice(source, uid, price)
	if err != nil {
		log.ErrorWithCtx(ctx, "HandleGoldSmashEggBuff fail to IncrUserWeekConsumePrice. uid:%d, bgId:%d, err:%v", uid, bgId, err)
		return err
	}

	itemId, itemType, _, err := m.getBgItemInfo(ctx, uid, bgId)
	if err != nil {
		log.ErrorWithCtx(ctx, "HandleGoldSmashEggBuff fail to getBgItemInfo. uid:%d, bgId:%d, err:%v", uid, bgId, err)
		return err
	}

	// 不是碎片类型
	if itemType != uint32(backpackPb.PackageItemType_BACKPACK_LOTTERY_FRAGMENT) {
		// 清除用户buff相关缓存
		return m.clearUserBuffCache(ctx, uid, source)
	}

	switch itemId {
	case m.bc.GetDressFragmentId(): // 装扮碎片
		return m.HandleFragmentBuff(ctx, uid, cid, source, currConsumePrice, uint32(pb.BuffType_DressUpDebris), uint32(pb.BuffType_GiftDebris))

	case m.bc.GetGiftFragmentId(): // 礼物碎片
		return m.HandleFragmentBuff(ctx, uid, cid, source, currConsumePrice, uint32(pb.BuffType_GiftDebris), uint32(pb.BuffType_DressUpDebris))

	default:
		// 清除用户buff相关缓存
		return m.clearUserBuffCache(ctx, uid, source)
	}
}

func (m *Mgr) HandleFragmentBuff(ctx context.Context, uid, cid, source, currConsumePrice, bingoBuffType, clearBuffType uint32) error {
	// 获取上次奖励记录
	lastBonus, exist, err := m.cache.GetUserLastBonusBuff(source, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "HandleFragmentBuff fail to GetUserLastBonusBuff. uid:%d, err:%v", uid, err)
		return err
	}

	if exist &&
		lastBonus.BuffType != uint32(pb.BuffType_AnyDebris) &&
		lastBonus.BuffType != bingoBuffType {
		// 不连续了(任意碎片buff除外)，清除上一次奖励记录
		_ = m.cache.DelUserLastBonusBuff(source, uid)
	}

	// 清互斥的buff类型
	err = m.cache.ClearUserWeekContinueCnt(source, uid, []uint32{clearBuffType})
	if err != nil {
		log.ErrorWithCtx(ctx, "HandleFragmentBuff fail to ClearUserWeekContinueCnt. uid:%d, err:%v", uid, err)
		return err
	}

	buffListOpt, err := m.GetBuffConfList(ctx, source)
	if err != nil {
		log.ErrorWithCtx(ctx, "HandleFragmentBuff fail to GetBuffConfList. uid:%d, err:%v", uid, err)
		return err
	}

	currContinueCnt, err := m.cache.IncrUserWeekContinueCnt(source, bingoBuffType, uid, 1)
	if err != nil {
		log.ErrorWithCtx(ctx, "HandleFragmentBuff fail to IncrUserWeekContinueCnt. uid:%d, err:%v", uid, err)
		return err
	}

	bingoBuff := getBingoBuff(bingoBuffType, currConsumePrice, currContinueCnt, buffListOpt)
	// 未命中规则
	if bingoBuff == nil {
		return m.handleAnyFragmentBuff(ctx, uid, cid, source, currConsumePrice, buffListOpt)
	}

	// 本轮已经触发奖励过了
	if exist && bingoBuff.GetBuffType() == lastBonus.BuffType &&
		bingoBuff.GetConsumeRmb() == lastBonus.GoalPrice &&
		bingoBuff.GetContinueCnt() == lastBonus.GoalCnt {

		return m.handleAnyFragmentBuff(ctx, uid, cid, source, currConsumePrice, buffListOpt)
	}

	// award
	err = m.AwardDarkGift(ctx, uid, cid, bingoBuff)
	if err != nil {
		log.ErrorWithCtx(ctx, "HandleFragmentBuff fail to AwardDarkGift. uid:%d, err:%v", uid, err)
		return err
	}

	// 记录上一次获奖buff
	_ = m.cache.SetUserLastBonusBuff(source, uid, &cache.LastUserBonusBuff{
		BuffType:  bingoBuff.GetBuffType(),
		GoalPrice: bingoBuff.GetConsumeRmb(),
		GoalCnt:   bingoBuff.GetContinueCnt(),
	})

	// 已经是同类型同人群下 最高级的buff了
	if checkBingoBuffIfTopLevel(bingoBuff, buffListOpt) {
		// 需清除用户相关buff缓存
		err = m.clearUserBuffCache(ctx, uid, source)
		if err != nil {
			log.ErrorWithCtx(ctx, "HandleFragmentBuff fail to clearUserBuffCache. uid:%d, err:%v", uid, err)
		}
	} else {
		// 装扮碎片/礼物碎片发奖后，需清除任意碎片类型连续次数
		err = m.cache.ClearUserWeekContinueCnt(source, uid, []uint32{uint32(pb.BuffType_AnyDebris)})
		if err != nil {
			log.ErrorWithCtx(ctx, "HandleFragmentBuff fail to ClearUserWeekContinueCnt. uid:%d, err:%v", uid, err)
		}
	}

	return nil
}

func (m *Mgr) handleAnyFragmentBuff(ctx context.Context, uid, cid, source, currConsumePrice uint32, buffListOpt *pb.BuffConfListOpt) error {
	buffType := uint32(pb.BuffType_AnyDebris)
	currContinueCnt, err := m.cache.IncrUserWeekContinueCnt(source, buffType, uid, 1)
	if err != nil {
		log.ErrorWithCtx(ctx, "handleAnyFragmentBuff fail to IncrUserWeekContinueCnt. uid:%d, err:%v", uid, err)
		return err
	}

	bingoBuff := getBingoBuff(buffType, currConsumePrice, currContinueCnt, buffListOpt)
	// 未命中规则
	if bingoBuff == nil {
		return nil
	}

	lastBonus, exist, err := m.cache.GetUserLastBonusBuff(source, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "handleAnyFragmentBuff fail to GetUserLastBonusBuff. uid:%d, err:%v", uid, err)
		return err
	}

	// 本轮已经触发奖励过了
	if exist && bingoBuff.GetBuffType() == lastBonus.BuffType &&
		bingoBuff.GetConsumeRmb() == lastBonus.GoalPrice &&
		bingoBuff.GetContinueCnt() == lastBonus.GoalCnt {

		return nil
	}

	// award
	err = m.AwardDarkGift(ctx, uid, cid, bingoBuff)
	if err != nil {
		log.ErrorWithCtx(ctx, "handleAnyFragmentBuff fail to AwardDarkGift. uid:%d, err:%v", uid, err)
		return err
	}

	// 记录上一次获奖buff
	_ = m.cache.SetUserLastBonusBuff(source, uid, &cache.LastUserBonusBuff{
		BuffType:  bingoBuff.GetBuffType(),
		GoalPrice: bingoBuff.GetConsumeRmb(),
		GoalCnt:   bingoBuff.GetContinueCnt(),
	})

	// 已经是同类型同人群下 最高级的buff了
	if checkBingoBuffIfTopLevel(bingoBuff, buffListOpt) {
		// 需清除用户相关buff缓存
		err = m.clearUserBuffCache(ctx, uid, source)
		if err != nil {
			log.ErrorWithCtx(ctx, "handleAnyFragmentBuff fail to clearUserBuffCache. uid:%d, err:%v", uid, err)
		}
	} else {
		// 发奖后，需清除 装扮碎片/礼物碎片 连续次数
		err = m.cache.ClearUserWeekContinueCnt(source, uid, []uint32{uint32(pb.BuffType_DressUpDebris), uint32(pb.BuffType_GiftDebris)})
		if err != nil {
			log.ErrorWithCtx(ctx, "handleAnyFragmentBuff fail to ClearUserWeekContinueCnt. uid:%d, err:%v", uid, err)
		}
	}

	return nil
}

func (m *Mgr) getItemInfo(ctx context.Context, uid, itemId, itemType uint32) (itemPrice uint32, name, pic string, err error) {
	if itemType == uint32(backpackPb.PackageItemType_BACKPACK_LOTTERY_FRAGMENT) {
		itemCfgResp, err := m.backpackCli.GetItemCfg(ctx, uid, &backpackPb.GetItemCfgReq{
			ItemType:         itemType,
			ItemSourceIdList: []uint32{itemId},
		})
		if err != nil || len(itemCfgResp.GetItemCfgList()) == 0 {
			log.ErrorWithCtx(ctx, "getItemInfo fail to GetItemCfg. uid:%d, itemId:%d, err:%v", uid, itemId, err)
			return 0, "", "", err
		}

		cfg := &backpackPb.LotteryFragmentCfg{}
		_ = proto.Unmarshal(itemCfgResp.GetItemCfgList()[0], cfg)
		itemPrice, name, pic = cfg.GetFragmentPrice(), cfg.GetFragmentName(), cfg.GetFragmentUrl()

	} else if itemType == uint32(backpackPb.PackageItemType_BACKPACK_PRESENT) {
		presentResp, err := m.userPresentCli.GetPresentConfigById(ctx, itemId)
		if err != nil {
			log.ErrorWithCtx(ctx, "getItemInfo fail to GetPresentConfigById. uid:%d, itemId:%d, err:%v", uid, itemId, err)
			return 0, "", "", err
		}

		cfg := presentResp.GetItemConfig()
		itemPrice, name, pic = cfg.GetPrice(), cfg.GetName(), cfg.GetIconUrl()

	} else {
		log.ErrorWithCtx(ctx, "getItemInfo fail. uid:%d, itemId:%d, err:itemType err", uid, itemId)
		return 0, "", "", errors.New("itemType err")
	}

	return
}

func (m *Mgr) AwardDarkGift(ctx context.Context, uid, cid uint32, bingoBuff *pb.BuffConf) error {
	if bingoBuff == nil {
		return nil
	}
	bgId := bingoBuff.GetBgId()

	itemId, itemType, itemCount, err := m.getBgItemInfo(ctx, uid, bgId)
	if err != nil {
		log.ErrorWithCtx(ctx, "AwardDarkGift fail to getBgItemInfo. uid:%d, bgId:%d, err:%v", uid, bgId, err)
		return err
	}

	itemPrice, name, pic, err := m.getItemInfo(ctx, uid, itemId, itemType)
	if err != nil {
		log.ErrorWithCtx(ctx, "AwardDarkGift fail to getItemInfo. uid:%d, itemId:%d, err:%v", uid, itemId, err)
		return err
	}

	totalPrice := itemPrice * itemCount
	now := time.Now()
	awardBusinessId, _ := m.bc.GetRPAwardBusinessInfo()
	orderId := fmt.Sprintf("%d_%d_%d_%d_%d", awardBusinessId, uid, bingoBuff.GetConfId(), bingoBuff.GetBgId(), now.Unix())

	err = m.SendPackage(ctx, uid, bgId, 1, orderId, now)
	if err != nil {
		log.ErrorWithCtx(ctx, "AwardDarkGift fail to SendPackage. uid:%d, bgId:%d, err:%v", uid, bgId, err)
		return err
	}

	err = m.mysql.RecordDarkGiftBonusLog(&mysql.DarkGiftBonusMonthLog{
		OrderId:        orderId,
		Uid:            uid,
		BuffId:         bingoBuff.GetConfId(),
		BusinessSource: bingoBuff.GetSource(),
		ChannelId:      cid,
		GiftId:         itemId,
		GiftAmount:     itemCount,
		GiftType:       genGiftType(itemType),
		GiftName:       name,
		GiftPic:        pic,
		GiftTotalPrice: totalPrice,
		BgId:           bgId,
		CreateTime:     now,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "AwardDarkGift fail to RecordDarkGiftBonusLog. uid:%d, bgId:%d, orderId:%s, err:%v", uid, bgId, orderId, err)
		return err
	}

	opt := &dark_gift_bonus_logic.DarkGiftBonusNotifyOpt{
		Uid:      uid,
		EffectLv: bingoBuff.GetBonusEffect(),
		Record: &dark_gift_bonus_logic.DarkGiftBonusRecord{
			OrderId:     orderId,
			BonusSource: bingoBuff.GetSource(),
			GiftId:      itemId,
			GiftAmount:  itemCount,
			GiftType:    genGiftType(itemType),
			GiftName:    name,
			GiftPic:     pic,
			TotalPrice:  totalPrice,
			CreateTime:  uint32(now.Unix()),
		},
	}

	darkGiftEffectMap := m.bc.GetDarkGiftEffectMap()
	if effectInfo, ok := darkGiftEffectMap[fmt.Sprint(itemId)]; ok {
		opt.EffectUrl = effectInfo.EffectUrl
		opt.EffectMd5 = effectInfo.Md5
	}

	err = m.PushUserAwardInfo(ctx, opt)
	if err != nil {
		log.ErrorWithCtx(ctx, "AwardDarkGift fail to PushUserAwardInfo. uid:%d, bgId:%d, err:%v", uid, bgId, err)
		return err
	}

	log.Infof("AwardDarkGift uid:%d, bgId:%d", uid, bgId)
	return nil
}

func genGiftType(itemType uint32) uint32 {
	if itemType == uint32(backpackPb.PackageItemType_BACKPACK_LOTTERY_FRAGMENT) {
		return uint32(pb.DarkGiftBonusType_EnergyStone)

	} else if itemType == uint32(backpackPb.PackageItemType_BACKPACK_PRESENT) {
		return uint32(pb.DarkGiftBonusType_DarkGift)
	}

	return 0
}

func (m *Mgr) SendPackage(ctx context.Context, uid, bgId, num uint32, orderId string, t time.Time) error {
	awardBusinessId, awardSecretKey := m.bc.GetRPAwardBusinessInfo()
	cipherText := butils.AESEncrypt([]byte(orderId), []byte(awardSecretKey))

	// 接入风控发奖
	_, sErr := m.bpSenderCli.SendBackpackWithRiskControl(ctx, &bpb.SendBackpackWithRiskControlReq{
		BusinessId:  awardBusinessId,
		BackpackId:  bgId,
		BackpackCnt: num,
		ReceiveUid:  uid,
		ServerTime:  t.Unix(),
		OrderId:     orderId,
		Ciphertext:  cipherText,
	})
	if sErr != nil {
		log.ErrorWithCtx(ctx, "SendPackage SendBackpackWithRiskControl fail. uid:%d bgId:%d err:%v", uid, bgId, sErr)
		// 订单已存在
		//if sErr.Code() == status.ErrRiskControlBackpackDuplicateOrderid {
		//	return nil
		//}

		return sErr
	}

	log.Infof("SendPackage SendBackpackWithRiskControl success. uid:%d bgId:%d num:%d orderId:%s", uid, bgId, num, orderId)
	return nil
}

// 是否是同人群下同类型buff下 最高等级的规则
func checkBingoBuffIfTopLevel(bingoBuff *pb.BuffConf, opt *pb.BuffConfListOpt) bool {
	for _, buff := range opt.GetConfList() {
		if buff.GetBuffType() == bingoBuff.GetBuffType() &&
			buff.GetConsumeRmb() == bingoBuff.GetConsumeRmb() &&
			buff.GetContinueCnt() > bingoBuff.GetContinueCnt() {
			return false
		}
	}

	return true
}

func getBingoBuff(buffType, currConsumePrice, currContinueCnt uint32, opt *pb.BuffConfListOpt) *pb.BuffConf {
	for _, buff := range opt.GetConfList() {
		if buff.GetBuffType() == buffType &&
			buff.GetConsumeRmb() <= currConsumePrice &&
			buff.GetContinueCnt() <= currContinueCnt {
			// 命中规则
			return buff
		}
	}

	return nil
}

func (m *Mgr) GetDarkGiftBonusRecords(ctx context.Context, in *pb.GetDarkGiftBonusRecordReq) ([]*pb.DarkGiftBonusRecord, error) {
	list := make([]*pb.DarkGiftBonusRecord, 0)
	uid, source := in.GetUid(), in.GetBonusSource()
	offset, limit := in.GetOffset(), in.GetLimit()

	if limit == 0 {
		return list, nil
	}

	now := time.Now()
	logList, err := m.mysql.GetUserDarkGiftBonusLogs(uid, source, offset, limit, now)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetDarkGiftBonusRecords fail to GetUserDarkGiftBonusLogs. in:%+v, err:%v", in, err)
		return list, nil
	}

	logCnt := uint32(len(logList))
	// 不够数，拿上个月的记录来凑, 最多只查近两个月数据
	if logCnt < limit {
		// 获取这个月的记录数量
		thisMonthLogCnt, err := m.mysql.GetUserDarkGiftBonusLogCnt(uid, source, now)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetDarkGiftBonusRecords fail to GetUserDarkGiftBonusLogCnt. in:%+v, err:%v", in, err)
			return list, nil
		}

		thisMonthBegin := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, time.Local)
		lastMonthBegin := thisMonthBegin.AddDate(0, -1, 0)

		newLimit := limit - logCnt
		newOffset := uint32(0)
		if offset > thisMonthLogCnt {
			newOffset = offset - thisMonthLogCnt
		}

		lastMonthLogList, err := m.mysql.GetUserDarkGiftBonusLogs(uid, source, newOffset, newLimit, lastMonthBegin)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetDarkGiftBonusRecords fail to GetUserDarkGiftBonusLogs. in:%+v, err:%v", in, err)
			return list, nil
		}

		logList = append(logList, lastMonthLogList...)
	}

	for _, info := range logList {
		list = append(list, fillDarkGiftBonusRecordPb(info))
	}

	return list, nil
}

func fillDarkGiftBonusRecordPb(info *mysql.DarkGiftBonusMonthLog) *pb.DarkGiftBonusRecord {
	return &pb.DarkGiftBonusRecord{
		OrderId:     info.OrderId,
		GiftType:    info.GiftType,
		GiftId:      info.GiftId,
		GiftAmount:  info.GiftAmount,
		GiftName:    info.GiftName,
		GiftPic:     info.GiftPic,
		TotalPrice:  info.GiftTotalPrice,
		BonusSource: info.BusinessSource,
		CreateTime:  uint32(info.CreateTime.Unix()),
	}
}

func (m *Mgr) PushUserAwardInfo(ctx context.Context, opt *dark_gift_bonus_logic.DarkGiftBonusNotifyOpt) error {
	uid := opt.GetUid()
	if uid == 0 {
		return nil
	}

	b, _ := proto.Marshal(opt)
	err := m.PushMsgToUser(ctx, uid, uint32(gaPush.PushMessage_DARK_GIFT_BONUS_MSG), b)
	if err != nil {
		log.ErrorWithCtx(ctx, "PushUserAwardInfo fail to PushMsgToUser. uid:%v, info:%+v, err:%v", uid, opt, err)
		return err
	}

	log.Debugf("PushUserAwardInfo uid:%v, opt:%+v", uid, opt)
	return nil
}

func (m *Mgr) PushMsgToUser(ctx context.Context, uid, cmd uint32, msg []byte) error {
	pushMessage := &gaPush.PushMessage{
		Cmd:     cmd,
		Content: msg,
	}
	pushMessageBytes, _ := pushMessage.Marshal()

	err := m.pushCli.PushToUsers(ctx, []uint32{uid}, &pushPB.CompositiveNotification{
		Sequence:           uid,
		TerminalTypeList:   []uint32{protocol.MobileAndroidTT, protocol.MobileIPhoneTT},
		TerminalTypePolicy: PushNotification.DefaultPolicy,
		AppId:              0,
		ProxyNotification: &pushPB.ProxyNotification{
			Type:    uint32(pushPB.ProxyNotification_PUSH),
			Payload: pushMessageBytes,
		},
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "PushMsgToUser PushToUsers err: %s", err.Error())
		return err
	}

	return nil
}

func (m *Mgr) GetDarkBonusSummaryWorth(ctx context.Context, in *pb.GetDarkGiftBonusSummaryReq) (*pb.GetDarkGiftBonusSummaryResp, error) {
	out := &pb.GetDarkGiftBonusSummaryResp{}
	var err error
	beginTime := time.Unix(int64(in.GetBeginTs()), 0)
	endTime := time.Unix(int64(in.GetEndTs()), 0)

	if beginTime.After(endTime) {
		return out, nil
	}

	out.TotalWorth, err = m.mysql.GetDarkGiftBonusSummaryWorth(in.GetBonusSource(), beginTime, endTime)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetDarkBonusSummaryWorth GetDarkGiftBonusSummaryWorth in:%+v, err: %s", in, err.Error())
		return out, err
	}

	return out, nil
}
