package store

import (
	"context"
	"database/sql"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/services/smash-egg-notify/internal/entity"
	"time"
	"fmt"
)

var createSmashEggNotifyTable = `
CREATE TABLE IF NOT EXISTS smash_egg_notify_v2 (
	id int(10) unsigned NOT NULL AUTO_INCREMENT,
    notify_type tinyint NOT NULL COMMENT '1：奖池下架提醒、2：奖池及兑换更新提醒、3：兑换更新提醒', 
	notify_begin timestamp,
	notify_end timestamp,
	has_floating tinyint,
	floating_text varchar(64),
	floating_duration int,
	has_zhuanzhuan tinyint,
	zhuanzhuan_text varchar(64),
	zhuanzhuan_duration int,
	has_exchange int,
	exchange_text varchar(64),
	exchange_duration int,
	has_public tinyint,
	public_text varchar(255),
	has_red tinyint,
	text_color varchar(64),
	modify_time timestamp NOT NULL DEFAULT current_timestamp,
    smash_activity_type tinyint NOT NULL,
	UNIQUE KEY idx_notify_act_type(notify_type,smash_activity_type),
	PRIMARY KEY (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;`

var createGiftNotifyTable = `
CREATE TABLE IF NOT EXISTS gift_notify (
	id int(10) unsigned NOT NULL AUTO_INCREMENT,
    notify_type tinyint NOT NULL DEFAULT 4 COMMENT '4：礼物过期', 
	notify_begin timestamp,
	notify_end timestamp,
	gift_text varchar(64),
	gift_duration int,
	modify_time timestamp NOT NULL DEFAULT current_timestamp,
    operator_name varchar(64),
	PRIMARY KEY (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;`

func (s *Store) CreateSmashEggNotifyTable() error {
	_, err := s.db.Exec(createSmashEggNotifyTable)
	if nil != err {
		log.Errorf("Store CreateSmashEggNotifyTable err: %v", err)
		return err
	}
	return nil
}

func (s *Store) CreateGiftNotifyTable() error {
	_, err := s.db.Exec(createGiftNotifyTable)
	if nil != err {
		log.Errorf("Store CreateGiftNotifyTable err: %v", err)
		return err
	}
	return nil
}

func (s *Store) SetSmashEggNotify(ctx context.Context, notify entity.SmashEggNotify) error {
	query := `REPLACE INTO smash_egg_notify_v2 (notify_type,notify_begin,notify_end,has_floating,floating_text,floating_duration,
has_zhuanzhuan,zhuanzhuan_text,zhuanzhuan_duration,has_public,public_text,has_red,modify_time,text_color,has_exchange,exchange_text,exchange_duration,smash_activity_type) VALUES (?,FROM_UNIXTIME(?),FROM_UNIXTIME(?),?,?,?,?,?,?,?,?,?,FROM_UNIXTIME(?),?,?,?,?,?)`
	params := make([]interface{}, 0, 18)
	params = append(params, notify.NotifyType)
	params = append(params, notify.NotifyBegin)
	params = append(params, notify.NotifyEnd)
	params = append(params, notify.HasFloating)
	params = append(params, notify.FloatingText)
	params = append(params, notify.FloatingDuration)
	params = append(params, notify.HasZhuanzhuan)
	params = append(params, notify.ZhuanzhuanText)
	params = append(params, notify.ZhuanzhuanDuration)
	params = append(params, notify.HasPublic)
	params = append(params, notify.PublicText)
	params = append(params, notify.HasRed)
	params = append(params, time.Now().Unix())
	params = append(params, notify.TextColor)
	params = append(params, notify.HasExchange)
	params = append(params, notify.ExchangeText)
	params = append(params, notify.ExchangeDuration)
	params = append(params, notify.SmashActivityType)

	_, err := s.db.ExecContext(ctx, query, params...)
	if err != nil {
		log.Errorf("Store SetNotify err: %v", err)
		return err
	}
	return err
}

func (s *Store) GetSmashEggNotify(ctx context.Context, notifyType, activityType int) (notify []entity.SmashEggNotify, err error) {
	var query string
	if notifyType == 0 {
		query = `SELECT notify_type,UNIX_TIMESTAMP(notify_begin) as notify_begin,UNIX_TIMESTAMP(notify_end) as notify_end,has_floating,floating_text,floating_duration,has_zhuanzhuan,zhuanzhuan_text,
zhuanzhuan_duration,has_public,public_text,has_red,UNIX_TIMESTAMP(modify_time) as modify_time,text_color,has_exchange,exchange_text,exchange_duration,smash_activity_type FROM smash_egg_notify_v2`
	} else {
		query = fmt.Sprintf("SELECT notify_type,UNIX_TIMESTAMP(notify_begin) as notify_begin,UNIX_TIMESTAMP(notify_end) as notify_end,"+
			"has_floating,floating_text,floating_duration,has_zhuanzhuan,zhuanzhuan_text,zhuanzhuan_duration,has_public,public_text,has_red,"+
			"UNIX_TIMESTAMP(modify_time) as modify_time,text_color,has_exchange,exchange_text,exchange_duration,smash_activity_type FROM smash_egg_notify_v2 "+
			"WHERE notify_type = %d and smash_activity_type = %d", notifyType, activityType)
	}

	err = s.db.SelectContext(ctx, &notify, query)
	if err != nil {
		log.Errorf("Store GetNotify err: %v", err)
		return
	}
	return notify, err
}

func (s *Store) SetGiftNotify(ctx context.Context, notify entity.GiftNotify) error {
	var query string
	var params []interface{}
	if notify.Id != 0 {
		//update
		params = make([]interface{}, 0, 6)
		params = append(params, notify.Id)
		query = `REPLACE INTO gift_notify (id,notify_begin,notify_end,gift_text,gift_duration,operator_name) VALUES (?,FROM_UNIXTIME(?),FROM_UNIXTIME(?),?,?,?)`
		//query = `REPLACE INTO gift_notify (id,notify_begin,notify_end,gift_text,gift_duration,operator_name) VALUES (:id,FROM_UNIXTIME(:notify_begin),FROM_UNIXTIME(:notify_end),:gift_text,FROM_UNIXTIME(:gift_duration),:operator_name)`

	} else {
		//insert
		params = make([]interface{}, 0, 5)
		query = `INSERT INTO gift_notify (notify_begin,notify_end,gift_text,gift_duration,operator_name) VALUES (FROM_UNIXTIME(?),FROM_UNIXTIME(?),?,?,?)`
	}

	params = append(params, notify.NotifyBegin)
	params = append(params, notify.NotifyEnd)
	params = append(params, notify.GiftText)
	params = append(params, notify.GiftDuration)
	params = append(params, notify.OperatorName)

	_, err := s.db.ExecContext(ctx, query, params...)
	if err != nil {
		log.Errorf("SetGiftNotify error : %v", err)
	}
	return err
}

func (s *Store) GetAllGiftNotify(ctx context.Context) (notify []entity.GiftNotify, err error) {
	query := `SELECT id,notify_type,UNIX_TIMESTAMP(notify_begin) as notify_begin,UNIX_TIMESTAMP(notify_end) as notify_end,gift_text,gift_duration,operator_name,UNIX_TIMESTAMP(modify_time) as modify_time FROM gift_notify order by notify_end desc`
	err = s.db.SelectContext(ctx, &notify, query)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil
		}
		log.Errorf("GetAllGiftNotify Query error:%v", err)
		return nil, err
	}

	/*	err = rows.Scan(&notify)
		if err != nil {
			log.Errorf("GetAllGiftNotify Scan error:%v", err)
			return
		}*/

	return
}

func (s *Store) GetCurrentGiftNotify(ctx context.Context) (notify *entity.GiftNotify, err error) {
	now := time.Now().Unix()
	var dest []entity.GiftNotify
	query := `SELECT id,notify_type,UNIX_TIMESTAMP(notify_begin) as notify_begin,UNIX_TIMESTAMP(notify_end) as notify_end,gift_text,gift_duration,operator_name,UNIX_TIMESTAMP(modify_time) as modify_time FROM gift_notify WHERE notify_begin<FROM_UNIXTIME(?) AND notify_end>FROM_UNIXTIME(?)`
	err = s.db.SelectContext(ctx, &dest, query, now, now)
	if err != nil {
		log.Errorf("GetAllGiftNotify Query error:%v", err)
		return
	}

	if len(dest) > 0 {
		notify = &dest[0]
	} else {
		notify = nil
	}
	/*
		err = result.Scan(notify)
		if err != nil {
			log.Errorf("GetAllGiftNotify Scan error:%v", err)
			return
		}*/

	return

}

func (s *Store) DeleteGiftNotify(ctx context.Context, id uint32) error {
	query := "DELETE FROM gift_notify WHERE id = ?"

	_, err := s.db.ExecContext(ctx, query, id)
	if err != nil {
		log.Errorf("SetGiftNotify error : %v", err)
	}

	return nil

}

/*func (s *Store) GetSimpleNotify(ctx context.Context, notifyPlace int) (notify []entity.SmashEggNotify, err error) {
	var query string
	query = `SELECT notify_type,notify_begin,notify_end,has_floating,floating_text,floating_duration,has_zhuanzhuan,zhuanzhuan_text,
zhuanzhuan_duration,has_public,public_text,public_duration,has_red,modify_time FROM smash_egg_notify_v2 WHERE notify_type = `

	err = s.db.SelectContext(ctx, notify, query)
	return notify, err
}
*/
