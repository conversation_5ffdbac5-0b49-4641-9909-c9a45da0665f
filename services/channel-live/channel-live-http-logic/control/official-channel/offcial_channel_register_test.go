package official_channel

import (
	"encoding/json"
	"github.com/golang/mock/gomock"
	"golang.52tt.com/clients/mocks/channel"
	"golang.52tt.com/clients/mocks/channel-live-management"
	"golang.52tt.com/clients/mocks/channel-live-mgr"
	"golang.52tt.com/clients/mocks/official-live-channel"
	"golang.52tt.com/pkg/web"
	pb "golang.52tt.com/protocol/services/channel-live-http-logic"
	liveManagementPb "golang.52tt.com/protocol/services/channel-live-management"
	channelPb "golang.52tt.com/protocol/services/channelsvr"
	officialPb "golang.52tt.com/protocol/services/official-live-channel"
	"golang.52tt.com/services/channel-live/channel-live-http-logic/client"
	"io/ioutil"
	"net/http"
	"strings"
	"testing"
	"time"
)

var nowTm = time.Now()
var registerInfo = &liveManagementPb.OfficialChRegisterInfo{
	ChannelId:     123,
	ActTime:       "ddd",
	RegisterTs:    nowTm.Unix(),
	RegisterCond:  "ddd",
	SortWeight:    1,
	ActionExample: "ddd",
	AudioExample:  "ddd",
	IntroExemple:  "ddd",
}

type indexHandler struct {
}

func (ih *indexHandler) Header() http.Header {
	return http.Header{}
}

func (ih *indexHandler) Write([]byte) (int, error) {
	return 0, nil
}

func (ih *indexHandler) WriteHeader(statusCode int) {
	return
}

func TestCheckUserRegisterEntry(t *testing.T) {
	type args struct {
		authInfo *web.AuthInfo
		w        http.ResponseWriter
		r        *http.Request
	}

	ctl := gomock.NewController(t)
	defer ctl.Finish()
	liveManage := channellivemanagement.NewMockIClient(ctl)
	client.LiveManagementCli = liveManage

	gomock.InOrder(
		liveManage.EXPECT().CheckUserRegisterEntry(gomock.Any(), gomock.Any()).Return(&liveManagementPb.CheckUserRegisterEntryResp{
			IsHasEntry: false,
		}, nil).AnyTimes(),
	)

	tests := []struct {
		name string
		args args
	}{
		{
			name: "TestCheckUserRegisterEntry",
			args: args{
				authInfo: &web.AuthInfo{UserID: 123},
				w:        &indexHandler{},
				r:        &http.Request{},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			client.LiveManagementCli = liveManage
			CheckUserRegisterEntry(tt.args.authInfo, tt.args.w, tt.args.r)
		})
	}
}

func TestGetRegisterOfficialChList(t *testing.T) {
	type args struct {
		authInfo *web.AuthInfo
		w        http.ResponseWriter
		r        *http.Request
	}

	ctl := gomock.NewController(t)
	defer ctl.Finish()
	liveManage := channellivemanagement.NewMockIClient(ctl)
	officialCli := official_live_channel.NewMockIClient(ctl)
	channelCli := channel.NewMockIClient(ctl)

	req := &pb.GetRegisterOfficialChListReq{}

	byteReq, _ := json.Marshal(req)

	strReq := strings.NewReader(string(byteReq))
	rc := ioutil.NopCloser(strReq)

	var cid uint32 = 123

	gomock.InOrder(
		liveManage.EXPECT().GetRegisterOfficialChList(gomock.Any(), gomock.Any()).Return(&liveManagementPb.GetRegisterOfficialChListResp{
			InfoList: []*liveManagementPb.OfficialChannelInfo{
				{
					Info:           registerInfo,
					RegisterStatus: 2,
					RegisterMatchList: []*liveManagementPb.MatchInfo{
						{
							MatchTs:   nowTm.Unix(),
							MatchName: "ddd",
							ScheduleList: []*liveManagementPb.MatchSchedule{
								{
									BeginTs:    nowTm.AddDate(0, 0, 1).Unix(),
									EndTs:      nowTm.AddDate(0, 0, 2).Unix(),
									Uid:        12345,
									ScheduleId: 1,
								},
							},
							MatchId: 1,
						},
					},
				},
			},
			MyRegisterList: nil,
		}, nil).AnyTimes(),
		officialCli.EXPECT().GetRelayScheduleList(gomock.Any(), gomock.Any()).Return(&officialPb.GetRelayScheduleListResp{
			Schedule: []*officialPb.RelaySchedule{
				&officialPb.RelaySchedule{
					ScheduleId: 0,
					Sections: []*officialPb.RelaySection{
						{
							SectionId:             1,
							ChannelId:             123,
							AnchorId:              0,
							AnchorAccount:         "",
							AnchorNickname:        "",
							AnchorSex:             0,
							Banner:                "",
							IntroductionText:      "",
							IntroductionImg:       "",
							StartTime:             nowTm.Unix(),
							EndTime:               nowTm.AddDate(0, 0, 1).Unix(),
							IntroductionImgWidth:  0,
							IntroductionImgHeight: 0,
							AllowAnchorTagIds:     nil,
						},
					},
					NextScheduleId: 2,
					UpdateTime:     0,
					ScheduleDate:   0,
					ScheduleName:   "ddd",
				},
			},
		}, nil),
		channelCli.EXPECT().BatchGetChannelSimpleInfo(gomock.Any(), gomock.Any(), gomock.Any()).Return(map[uint32]*channelPb.ChannelSimpleInfo{
			123: &channelPb.ChannelSimpleInfo{
				ChannelId:            &cid,
				DisplayId:            nil,
				AppId:                nil,
				HasPwd:               nil,
				ChannelType:          nil,
				BindId:               nil,
				SwitchFlag:           nil,
				CreaterUid:           nil,
				CreateTs:             nil,
				IconMd5:              nil,
				TopicTitle:           nil,
				Passwd:               nil,
				Name:                 nil,
				IsDel:                nil,
				EnterControlType:     nil,
				ChannelViewId:        nil,
				XXX_NoUnkeyedLiteral: struct{}{},
				XXX_unrecognized:     nil,
				XXX_sizecache:        0,
			},
		}, nil),
	)

	tests := []struct {
		name string
		args args
	}{
		{
			name: "TestGetRegisterOfficialChList",
			args: args{
				authInfo: &web.AuthInfo{
					UserID:     0,
					OpenId:     "",
					GameId:     0,
					RoomId:     0,
					Username:   "",
					Session:    "",
					AppID:      0,
					OS:         0,
					Platform:   0,
					MarketID:   0,
					CliVersion: 0,
					Body:       byteReq,
				},
				w: &indexHandler{},
				r: &http.Request{
					Method:           "",
					URL:              nil,
					Proto:            "",
					ProtoMajor:       0,
					ProtoMinor:       0,
					Header:           nil,
					Body:             rc,
					GetBody:          nil,
					ContentLength:    0,
					TransferEncoding: nil,
					Close:            false,
					Host:             "",
					Form:             nil,
					PostForm:         nil,
					MultipartForm:    nil,
					Trailer:          nil,
					RemoteAddr:       "",
					RequestURI:       "",
					TLS:              nil,
					Cancel:           nil,
					Response:         nil,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			client.LiveManagementCli = liveManage
			client.OfficialLiveChannelCli = officialCli
			client.ChannelCli = channelCli
			GetRegisterOfficialChList(tt.args.authInfo, tt.args.w, tt.args.r)
		})
	}
}

func TestGetOfficialChMatchInfo(t *testing.T) {
	type args struct {
		authInfo *web.AuthInfo
		w        http.ResponseWriter
		r        *http.Request
	}

	ctl := gomock.NewController(t)
	defer ctl.Finish()
	liveManage := channellivemanagement.NewMockIClient(ctl)
	liveMgr := channellivemgr.NewMockIClient(ctl)

	req := &pb.GetOfficialChMatchInfoReq{}
	byteReq, _ := json.Marshal(req)

	strReq := strings.NewReader(string(byteReq))
	rc := ioutil.NopCloser(strReq)

	officialCli := official_live_channel.NewMockIClient(ctl)

	gomock.InOrder(
		liveManage.EXPECT().GetRegisterOfficialChList(gomock.Any(), gomock.Any()).Return(&liveManagementPb.GetRegisterOfficialChListResp{
			InfoList: []*liveManagementPb.OfficialChannelInfo{
				{
					Info:           registerInfo,
					RegisterStatus: 2,
					RegisterMatchList: []*liveManagementPb.MatchInfo{
						{
							MatchTs:   nowTm.Unix(),
							MatchName: "ddd",
							ScheduleList: []*liveManagementPb.MatchSchedule{
								{
									BeginTs:    nowTm.AddDate(0, 0, 1).Unix(),
									EndTs:      nowTm.AddDate(0, 0, 2).Unix(),
									Uid:        12345,
									ScheduleId: 1,
								},
							},
							MatchId: 1,
						},
					},
				},
			},
			MyRegisterList: nil,
		}, nil).AnyTimes(),
		officialCli.EXPECT().GetRelayScheduleList(gomock.Any(), gomock.Any()).Return(&officialPb.GetRelayScheduleListResp{
			Schedule: []*officialPb.RelaySchedule{
				&officialPb.RelaySchedule{
					ScheduleId: 0,
					Sections: []*officialPb.RelaySection{
						{
							SectionId:             1,
							ChannelId:             123,
							AnchorId:              0,
							AnchorAccount:         "",
							AnchorNickname:        "",
							AnchorSex:             0,
							Banner:                "",
							IntroductionText:      "",
							IntroductionImg:       "",
							StartTime:             nowTm.AddDate(0, 0, 2).Unix(),
							EndTime:               nowTm.AddDate(0, 0, 3).Unix(),
							IntroductionImgWidth:  0,
							IntroductionImgHeight: 0,
							AllowAnchorTagIds:     nil,
						},
					},
					NextScheduleId: 2,
					UpdateTime:     0,
					ScheduleDate:   0,
					ScheduleName:   "ddd",
				},
			},
		}, nil),
		liveMgr.EXPECT().GetChannelLiveInfo(gomock.Any(), gomock.Any(), gomock.Any()),
	)

	tests := []struct {
		name string
		args args
	}{
		{
			name: "TestGetOfficialChMatchInfo",
			args: args{
				authInfo: &web.AuthInfo{
					UserID:     0,
					OpenId:     "",
					GameId:     0,
					RoomId:     0,
					Username:   "",
					Session:    "",
					AppID:      0,
					OS:         0,
					Platform:   0,
					MarketID:   0,
					CliVersion: 0,
					Body:       byteReq,
				},
				w: &indexHandler{},
				r: &http.Request{
					Method:           "",
					URL:              nil,
					Proto:            "",
					ProtoMajor:       0,
					ProtoMinor:       0,
					Header:           nil,
					Body:             rc,
					GetBody:          nil,
					ContentLength:    0,
					TransferEncoding: nil,
					Close:            false,
					Host:             "",
					Form:             nil,
					PostForm:         nil,
					MultipartForm:    nil,
					Trailer:          nil,
					RemoteAddr:       "",
					RequestURI:       "",
					TLS:              nil,
					Cancel:           nil,
					Response:         nil,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			client.LiveManagementCli = liveManage
			client.OfficialLiveChannelCli = officialCli
			client.LiveMgrCli = liveMgr
			GetOfficialChMatchInfo(tt.args.authInfo, tt.args.w, tt.args.r)
		})
	}
}
