package manager

import (
	"context"
	"sort"
	"time"

	"gitlab.ttyuyin.com/avengers/tyr/compatible/proto"
	"golang.52tt.com/pkg/foundation/utils"
	"golang.52tt.com/pkg/log"
	ga "golang.52tt.com/protocol/app"
	channelPB "golang.52tt.com/protocol/app/channel"
	channelLiveLogicPB "golang.52tt.com/protocol/app/channel-live-logic"
	pb "golang.52tt.com/protocol/services/channel-live-pk"
	"golang.52tt.com/protocol/services/knightgroupmembers"
	"golang.52tt.com/services/channel-live/channel-live-pk/internal/model"
)

// 在房骑士信息

func (m *Manager) GetOnlineKnightList(ctx context.Context, req *pb.GetOnlineKnightListReq) (*pb.GetOnlineKnightListResp, error) {
	anchorUid := req.AnchorUid
	out := &pb.GetOnlineKnightListResp{}

	liveResp, serr := model.ClientPool.ChannelLiveMgrCli.GetChannelLiveInfo(ctx, anchorUid, true)
	if serr != nil {
		log.ErrorWithCtx(ctx, "GetOnlineKnightList GetChannelLiveInfo fail %v, uid=%d", serr, anchorUid)
		return out, serr
	}
	log.DebugWithCtx(ctx, "GetOnlineKnightList uid=%d GetChannelLiveInfo=%+v", anchorUid, liveResp.GetChannelLiveInfo())
	channelId := liveResp.GetChannelLiveInfo().GetChannelId()
	if channelId == 0 {
		return out, nil
	}

	cidPkMap, err := model.ClientPool.Cache.BatGetChannelMultiPk([]uint32{channelId})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetOnlineKnightList BatGetChannelMultiPk fail %v, cid=%d", err, channelId)
		return out, err
	}
	pkId := cidPkMap[channelId]
	log.InfoWithCtx(ctx, "GetOnlineKnightList anchorUid=%d channelId=%d pkId=%d", anchorUid, channelId, pkId)
	if pkId == 0 {
		return out, nil
	}

	knightList, err := model.ClientPool.Cache.GetKnightList(channelId)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetOnlineKnightList GetKnightList fail %v, uid=%d", err, anchorUid)
		return out, err
	}
	log.DebugWithCtx(ctx, "GetOnlineKnightList uid=%d cid=%d Cache.GetKnightList=%v", anchorUid, channelId, knightList)
	if len(knightList) == 0 {
		return out, nil
	}

	uid2KnightLevel, _, err := GetAnchorKnightInfo(ctx, anchorUid, channelId)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetOnlineKnightList GetAnchorKnightInfo fail %v, cid=%d", serr, channelId)
		return out, serr
	}
	uid2UKWPersonInfo, err := model.ClientPool.YkwCli.BatchGetUKWPersonInfoOnly(ctx, knightList)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetOnlineKnightList BatchGetUKWPersonInfoOnly fail %v, cid=%d", err, channelId)
		return out, err
	}
	log.DebugWithCtx(ctx, "GetOnlineKnightList uid=%d cid=%d knightList=%v BatchGetUKWPersonInfoOnly=%+v", anchorUid, channelId, knightList, uid2UKWPersonInfo)

	uid2PkScore, err := model.ClientPool.Cache.BatchGetUserPkValue(pkId, channelId, knightList)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetOnlineKnightList BatchGetUserPkValue fail %v, cid=%d", err, channelId)
		return out, err
	}
	log.DebugWithCtx(ctx, "GetOnlineKnightList uid=%d cid=%d pkId=%d knightList=%v BatchGetUserPkValue=%+v", anchorUid, channelId, pkId, knightList, uid2PkScore)

	for _, uid := range knightList {
		if uid2KnightLevel[uid] == 0 {
			log.InfoWithCtx(ctx, "GetOnlineKnightList knight expire. anchor_uid=%d knight_uid=%d", anchorUid, uid)
			continue
		}
		// 神秘人过滤
		if uid2UKWPersonInfo[uid].GetLevel() > 0 {
			log.InfoWithCtx(ctx, "GetOnlineKnightList ukw ignore. anchor_uid=%d uid=%d", anchorUid, uid)
			continue
		}

		userInfo := &pb.MultiPkUserInfo{
			Uid:         uid,
			KnightLevel: uint32(uid2KnightLevel[uid]),
			PkSocre:     uint64(uid2PkScore[uid]),
			FirstKill:   false,
		}
		out.KnightList = append(out.KnightList, userInfo)
	}

	// 按送礼值倒序排，如果送礼值相同按骑士等级排
	sort.Slice(out.KnightList, func(i, j int) bool {
		if out.KnightList[i].PkSocre == out.KnightList[j].PkSocre {
			return out.KnightList[i].KnightLevel > out.KnightList[j].KnightLevel
		}
		return out.KnightList[i].PkSocre > out.KnightList[j].PkSocre
	})

	log.DebugWithCtx(ctx, "GetOnlineKnightList anchorUid=%d channelId=%d out=%+v", anchorUid, channelId, out.KnightList)
	return out, nil
}

func PushKnightList(ctx context.Context, pkId, anchorUid, channelId uint32, multiPkChannelIds []uint32, desc string, pushOnlyExist bool) error {
	log.Infof("PushKnightList begin anchorUid=%d channelId=%d desc=%s multiPkChannelIds=%v", anchorUid, channelId, desc, multiPkChannelIds)

	knightInfo := &channelLiveLogicPB.MultiPkKnightInfo{
		AnchorUid: anchorUid,
		ChannelId: channelId,
	}

	knightList, err := model.ClientPool.Cache.GetKnightList(channelId)
	if err != nil {
		log.ErrorWithCtx(ctx, "PushKnightList GetKnightList fail %v, cid=%d", err, channelId)
		return err
	}
	log.Infof("PushKnightList pkId=%d cid=%d desc=%s knightList=%v", pkId, channelId, desc, knightList)

	if len(knightList) > 0 {
		userMap, serr := model.ClientPool.AccountCli.GetUsersMap(ctx, knightList)
		if serr != nil {
			log.ErrorWithCtx(ctx, "PushKnightList GetUsersMap fail %v, cid=%d", serr, channelId)
			return serr
		}
		uid2KnightLevel, _, err := GetAnchorKnightInfo(ctx, anchorUid, channelId)
		if err != nil {
			log.ErrorWithCtx(ctx, "PushKnightList GetAnchorKnightInfo fail %v, cid=%d", err, channelId)
			return err
		}
		log.InfoWithCtx(ctx, "PushKnightList uid=%d cid=%d knightList=%v uid2KnightLevel=%+v", anchorUid, channelId, knightList, uid2KnightLevel)

		uid2UKWPersonInfo, err := model.ClientPool.YkwCli.BatchGetUKWPersonInfoOnly(ctx, knightList)
		if err != nil {
			log.ErrorWithCtx(ctx, "PushKnightList BatchGetUKWPersonInfoOnly fail %v, cid=%d", err, channelId)
			return err
		}
		log.InfoWithCtx(ctx, "PushKnightList uid=%d cid=%d knightList=%v BatchGetUKWPersonInfoOnly=%+v", anchorUid, channelId, knightList, uid2UKWPersonInfo)

		uid2PkScore, err := model.ClientPool.Cache.BatchGetUserPkValue(pkId, channelId, knightList)
		if err != nil {
			log.ErrorWithCtx(ctx, "PushKnightList BatchGetUserPkValue fail %v, cid=%d", err, channelId)
			return err
		}
		log.InfoWithCtx(ctx, "PushKnightList uid=%d cid=%d knightList=%v BatchGetUserPkValue=%+v", anchorUid, channelId, knightList, uid2PkScore)

		for _, uid := range knightList {
			// 不是骑士
			if uid2KnightLevel[uid] == 0 {
				log.InfoWithCtx(ctx, "PushKnightList knight expire. anchor_uid=%d knight_uid=%d", anchorUid, uid)
				continue
			}

			// 神秘人过滤
			if uid2UKWPersonInfo[uid].GetLevel() > 0 {
				log.InfoWithCtx(ctx, "PushKnightList ukw ignore. anchor_uid=%d uid=%d", anchorUid, uid)
				continue
			}

			userInfo := userMap[uid]
			knightInfo.KnightList = append(knightInfo.KnightList, &channelLiveLogicPB.MultiPkUserInfo{
				UserProfile: &ga.UserProfile{
					Uid:          uid,
					Account:      userInfo.GetUsername(),
					Nickname:     userInfo.GetNickname(),
					AccountAlias: userInfo.GetAlias(),
					Sex:          uint32(userInfo.GetSex()),
				},
				PkScore:     uint64(uid2PkScore[uid]),
				KnightLevel: uint32(uid2KnightLevel[uid]),
			})
		}

		// sort
		sortMultiPkKnightList(knightInfo.KnightList)
	}

	if pushOnlyExist && len(knightInfo.KnightList) == 0 {
		log.InfoWithCtx(ctx, "PushKnightList ignore. pkId=%d, anchorUid=%d, channelId=%d desc=%s", pkId, anchorUid, channelId, desc)
		return nil
	}

	bin, _ := proto.Marshal(knightInfo)
	msg := &channelPB.ChannelBroadcastMsg{
		Time:         uint64(time.Now().Unix()),
		ToChannelId:  0, //channelId,
		Type:         uint32(channelPB.ChannelMsgType_CHANNEL_LIVE_MULTI_PK_KNIGHT_MSG),
		PbOptContent: bin,
	}
	channelMsgBin, _ := msg.Marshal()

	log.InfoWithCtx(ctx, "PushKnightList anchorUid=%d cid=%d desc=%s multiPkChannelIds=%v cache.knightList=%v push.knightInfo=%s",
		anchorUid, channelId, desc, multiPkChannelIds, knightList, utils.ToJson(knightInfo))

	err = PushChannelBroMsgToChannels(ctx, multiPkChannelIds, channelMsgBin)
	if err != nil {
		log.ErrorWithCtx(ctx, "PushKnightList PushChannelBroMsgToChannels fail %v, anchorUid=%d cid=%d", err, anchorUid, channelId)
		return err
	}
	return nil
}

// 如果没有首席，按收礼值降序排序
// 如果有首席则排首位
func sortMultiPkKnightList(knightList []*channelLiveLogicPB.MultiPkUserInfo) {
	if len(knightList) <= 1 {
		return
	}

	sort.Slice(knightList, func(i, j int) bool {
		if knightList[i].KnightLevel == knightList[j].KnightLevel {
			return knightList[i].PkScore > knightList[j].PkScore
		}
		return knightList[i].KnightLevel > knightList[j].KnightLevel
	})
}

// 获取当前在房的骑士, 并排序
func getOnlineKnight(ctx context.Context, anchorUid, channelId uint32) ([]*channelLiveLogicPB.MultiPkUserInfo, error) {
	list := []*channelLiveLogicPB.MultiPkUserInfo{}

	cidPkMap, err := model.ClientPool.Cache.BatGetChannelMultiPk([]uint32{channelId})
	if err != nil {
		log.ErrorWithCtx(ctx, "getOnlineKnight BatGetChannelMultiPk fail %v, cid=%d", err, channelId)
		return list, err
	}
	pkId := cidPkMap[channelId]
	log.InfoWithCtx(ctx, "getOnlineKnight anchorUid=%d channelId=%d pkId=%d", anchorUid, channelId, pkId)
	if pkId == 0 {
		return list, nil
	}

	knightList, err := model.ClientPool.Cache.GetKnightList(channelId)
	if err != nil {
		log.ErrorWithCtx(ctx, "getOnlineKnight GetKnightList fail %v, uid=%d", err, anchorUid)
		return list, err
	}
	log.InfoWithCtx(ctx, "getOnlineKnight uid=%d cid=%d Cache.GetKnightList=%v", anchorUid, channelId, knightList)
	if len(knightList) == 0 {
		return list, nil
	}

	uid2PkScore, err := model.ClientPool.Cache.BatchGetUserPkValue(pkId, channelId, knightList)
	if err != nil {
		log.ErrorWithCtx(ctx, "getOnlineKnight BatchGetUserPkValue fail %v, cid=%d", err, channelId)
		return list, err
	}
	log.InfoWithCtx(ctx, "getOnlineKnight uid=%d cid=%d pkId=%d BatchGetUserPkValue=%+v", anchorUid, channelId, pkId, uid2PkScore)

	uid2KnightLevel, _, err := GetAnchorKnightInfo(ctx, anchorUid, channelId)
	if err != nil {
		log.ErrorWithCtx(ctx, "getOnlineKnight GetAnchorKnightInfo fail %v, cid=%d", err, channelId)
		return list, err
	}
	log.InfoWithCtx(ctx, "getOnlineKnight uid=%d cid=%d uid2KnightLevel=%+v", anchorUid, channelId, uid2KnightLevel)

	uid2UKWPersonInfo, err := model.ClientPool.YkwCli.BatchGetUKWPersonInfoOnly(ctx, knightList)
	if err != nil {
		log.ErrorWithCtx(ctx, "getOnlineKnight BatchGetUKWPersonInfoOnly fail %v, cid=%d", err, channelId)
		return list, err
	}
	log.InfoWithCtx(ctx, "getOnlineKnight uid=%d cid=%d BatchGetUKWPersonInfoOnly=%+v", anchorUid, channelId, uid2UKWPersonInfo)

	for _, uid := range knightList {
		if uid2KnightLevel[uid] == 0 {
			log.InfoWithCtx(ctx, "getOnlineKnight knight expire. anchor_uid=%d knight_uid=%d", anchorUid, uid)
			continue
		}
		// 神秘人过滤
		if uid2UKWPersonInfo[uid].GetLevel() > 0 {
			log.InfoWithCtx(ctx, "getOnlineKnight ukw ignore. anchor_uid=%d uid=%d", anchorUid, uid)
			continue
		}

		list = append(list, &channelLiveLogicPB.MultiPkUserInfo{
			UserProfile: &ga.UserProfile{Uid: uid},
			KnightLevel: uint32(uid2KnightLevel[uid]),
			PkScore:     uint64(uid2PkScore[uid]),
		})
	}

	sortMultiPkKnightList(list)
	return list, nil
}

// 获取主播的所有骑士
func GetAnchorKnightInfo(ctx context.Context, anchorUid, channelId uint32) (
	map[uint32]ga.EChannelMemberKnightLevelID, uint32, error) {

	uid2level := map[uint32]ga.EChannelMemberKnightLevelID{}

	knightMemberResp, err := model.ClientPool.KnightgroupmemberCli.GetKnightGroupMember(ctx, &knightgroupmembers.GetKnightGroupMemberReq{
		ChannelId: channelId,
		AnchorUid: anchorUid,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetOnlineKnightList GetKnightGroupMember fail %v, uid=%d", err, anchorUid)
		return uid2level, 0, err
	}
	log.DebugWithCtx(ctx, "GetOnlineKnightList uid=%d cid=%d GetKnightGroupMember=%+v", anchorUid, channelId, knightMemberResp)
	chiefKnightUid := knightMemberResp.GetChiefUid()

	for _, info := range knightMemberResp.GetKnightMemberList() {
		if info.BeginTime <= uint32(time.Now().Unix()) && info.EndTime > uint32(time.Now().Unix()) {
			uid2level[info.Uid] = ga.EChannelMemberKnightLevelID_ENUM_CHANNEL_MEMBER_KNIGHT_COMMON
			if chiefKnightUid == info.Uid {
				uid2level[info.Uid] = ga.EChannelMemberKnightLevelID_ENUM_CHANNEL_MEMBER_KNIGHT_CHIEF
			}
		}
	}
	return uid2level, chiefKnightUid, nil
}
