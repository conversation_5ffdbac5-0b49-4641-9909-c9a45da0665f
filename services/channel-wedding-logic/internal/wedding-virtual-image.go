package internal

import (
	"context"
	"gitlab.ttyuyin.com/tt-infra/tyr/log"
	"golang.52tt.com/pkg/protocol"
	protogrpc "golang.52tt.com/pkg/protocol/grpc"
	"golang.52tt.com/protocol/app/channel_wedding_logic"
	"golang.52tt.com/protocol/common/status"
	channel_wedding "golang.52tt.com/protocol/services/channel-wedding"
	virtual_image_user "golang.52tt.com/protocol/services/virtual-image-user"
	"google.golang.org/grpc/codes"
)

// BatchGetUserWeddingClothes 批量获取用户服装信息请求
func (s *Server) BatchGetUserWeddingClothes(ctx context.Context, in *channel_wedding_logic.BatchGetUserWeddingClothesRequest) (*channel_wedding_logic.BatchGetUserWeddingClothesResponse, error) {
	out := &channel_wedding_logic.BatchGetUserWeddingClothesResponse{}
	svrInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
	if !ok {
		return out, ErrParamInValid
	}
	opUid := svrInfo.UserID

	list := make([]*channel_wedding_logic.WeddingClothesInfo, 0)
	if len(in.GetUidList()) == 0 {
		return out, nil
	}
	if len(in.GetUidList()) > 100 {
		log.ErrorWithCtx(ctx, "BatchGetUserWeddingClothes uidList too long. len:%v", len(in.GetUidList()))
		return out, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "uidList too long")
	}

	resp, err := s.virtualImageCli.BatchGetUserInuseItemInfo(ctx, &virtual_image_user.BatchGetUserInuseItemInfoReq{
		UidList: in.GetUidList(),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "BatchGetUserWeddingClothes fail to BatchGetUserInuseItemInfo in:%+v, err:%v", in, err)
		return out, err
	}

	poseResp, err := s.weddingCli.BatchGetUserWeddingPose(ctx, &channel_wedding.BatchGetUserWeddingPoseReq{
		Uid:     opUid,
		Cid:     in.GetCid(),
		UidList: in.GetUidList(),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "BatchGetUserWeddingClothes fail to BatchGetUserWeddingPose in:%+v, err:%v", in, err)
		return out, err
	}

	poseMap := make(map[uint32]*channel_wedding.UserWeddingPose)
	for _, v := range poseResp.GetUserPoseList() {
		poseMap[v.GetUid()] = v
	}

	for _, v := range resp.GetUserInuseItemInfo() {
		itemList := make([]uint32, 0, len(v.GetItems()))
		for _, item := range v.GetItems() {
			itemList = append(itemList, item.GetCfgId())
		}

		pose := poseMap[v.GetUid()]
		if pose.GetPose() > 0 {
			itemList = append(itemList, pose.GetPose())
			if pose.GetPoseBoneId() > 0 {
				itemList = append(itemList, pose.GetPoseBoneId())
			}

			if pose.GetBasePoseBoneId() > 0 {
				itemList = append(itemList, pose.GetBasePoseBoneId())
			}
		}

		list = append(list, &channel_wedding_logic.WeddingClothesInfo{
			Uid:           v.GetUid(),
			ClothesIdList: itemList,
			Orientation:   pose.GetOrientation(),
		})
	}

	out.ClothesInfoList = list
	return out, nil
}

// GetUserWeddingPose 获取用户的婚礼姿势组件
func (s *Server) GetUserWeddingPose(ctx context.Context, in *channel_wedding_logic.GetUserWeddingPoseRequest) (*channel_wedding_logic.GetUserWeddingPoseResponse, error) {
	out := &channel_wedding_logic.GetUserWeddingPoseResponse{}
	svrInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
	if !ok {
		return out, ErrParamInValid
	}
	opUid := svrInfo.UserID

	user, sErr := s.userProfile.GetUserProfileV2(ctx, opUid, false)
	if sErr != nil {
		log.ErrorWithCtx(ctx, "GetUserWeddingPose fail to GetUserProfileV2, %+v, err:%v", in, sErr)
		return out, sErr
	}

	resp, err := s.weddingCli.GetUserWeddingPoseList(ctx, &channel_wedding.GetUserWeddingPoseListReq{
		Uid: opUid,
		Cid: in.GetCid(),
		Sex: user.GetSex(),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetUserWeddingPose fail to GetUserWeddingPoseList, %+v, err:%v", in, err)
		return out, err
	}

	out.PoseIdList = resp.GetPoseList()
	out.CurrPoseId = resp.GetCurrPose()
	out.Orientation = resp.GetOrientation()

	return out, nil
}

// SetUserInuseWeddingPose 设置用户使用的婚礼姿势组件
func (s *Server) SetUserInuseWeddingPose(ctx context.Context, in *channel_wedding_logic.SetUserInuseWeddingPoseRequest) (*channel_wedding_logic.SetUserInuseWeddingPoseResponse, error) {
	out := &channel_wedding_logic.SetUserInuseWeddingPoseResponse{}
	svrInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
	if !ok {
		return out, ErrParamInValid
	}
	opUid := svrInfo.UserID

	_, err := s.weddingCli.SetUserWeddingPose(ctx, &channel_wedding.SetUserWeddingPoseReq{
		Uid:  opUid,
		Cid:  in.GetCid(),
		Pose: in.GetPoseId(),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "SetUserInuseWeddingPose fail to SetUserWeddingPose, %+v, err:%v", in, err)
		return out, err
	}

	log.InfoWithCtx(ctx, "SetUserInuseWeddingPose success, %+v", in)
	return out, nil
}

// SetUserWeddingOrientation 设置用户婚礼形象朝向
func (s *Server) SetUserWeddingOrientation(ctx context.Context, in *channel_wedding_logic.SetUserWeddingOrientationRequest) (*channel_wedding_logic.SetUserWeddingOrientationResponse, error) {
	out := &channel_wedding_logic.SetUserWeddingOrientationResponse{}
	svrInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
	if !ok {
		return out, ErrParamInValid
	}
	opUid := svrInfo.UserID

	_, err := s.weddingCli.SetUserWeddingOrientation(ctx, &channel_wedding.SetUserWeddingOrientationReq{
		Uid:         opUid,
		Cid:         in.GetCid(),
		Orientation: in.GetOrientation(),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "SetUserWeddingOrientation fail to SetUserWeddingOrientation, %+v, err:%v", in, err)
		return out, err
	}

	log.InfoWithCtx(ctx, "SetUserWeddingOrientation success, %+v", in)
	return out, nil
}

// BatchGetUserInuseWeddingPose 批量获取用户使用中的婚礼姿势组件
func (s *Server) BatchGetUserInuseWeddingPose(ctx context.Context, in *channel_wedding_logic.BatchGetUserInuseWeddingPoseRequest) (*channel_wedding_logic.BatchGetUserInuseWeddingPoseResponse, error) {
	out := &channel_wedding_logic.BatchGetUserInuseWeddingPoseResponse{}
	svrInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
	if !ok {
		return out, ErrParamInValid
	}
	opUid := svrInfo.UserID

	poseList := make([]*channel_wedding_logic.UserWeddingPose, 0)
	if len(in.GetUidList()) == 0 {
		return out, nil
	}
	if len(in.GetUidList()) > 100 {
		log.ErrorWithCtx(ctx, "BatchGetUserInuseWeddingPose uidList too long. len:%v", len(in.GetUidList()))
		return out, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "uidList too long")
	}

	resp, err := s.weddingCli.BatchGetUserWeddingPose(ctx, &channel_wedding.BatchGetUserWeddingPoseReq{
		Uid:     opUid,
		UidList: in.GetUidList(),
		Cid:     in.GetCid(),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "BatchGetUserInuseWeddingPose fail to BatchGetUserWeddingPose, %+v, err:%v", in, err)
		return out, err
	}

	for _, v := range resp.GetUserPoseList() {
		poseList = append(poseList, &channel_wedding_logic.UserWeddingPose{
			Uid:    v.GetUid(),
			PoseId: v.GetPose(),
		})
	}

	out.UserPose = poseList
	return out, nil
}
