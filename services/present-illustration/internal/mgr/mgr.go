package mgr

import (
	"context"
	"errors"
	"gitlab.ttyuyin.com/tt-infra/tyr/log"
	"golang.52tt.com/pkg/coroutine"
	"golang.52tt.com/pkg/protocol"
	protogrpc "golang.52tt.com/pkg/protocol/grpc"
	pblogic "golang.52tt.com/protocol/app/present_illustration_logic"
	"golang.52tt.com/protocol/common/status"
	"golang.52tt.com/protocol/services/minToolkit/kafka/pb/kafkapresent"
	pb "golang.52tt.com/protocol/services/present-illustration"
	userpresent "golang.52tt.com/protocol/services/userpresent"
	"golang.52tt.com/services/present-illustration/common/dycfg"
	"golang.52tt.com/services/present-illustration/common/localcache"
	"golang.52tt.com/services/present-illustration/internal/cache"
	"golang.52tt.com/services/present-illustration/internal/conf"
	"golang.52tt.com/services/present-illustration/internal/rpc"
	"golang.52tt.com/services/present-illustration/internal/store"
	"google.golang.org/grpc/codes"
	"time"
)

var (
	ErrSysErr = protocol.NewExactServerError(codes.Internal, status.ErrIllustrationDbErr, "系统错误")
)

type Manager struct {
	cache        cache.ICache
	store        store.IStore
	dyConfig     dycfg.IConfDynamic
	illCfgCache  *localcache.IllustrationCfgLocalCache
	presentCache *localcache.PresentLocalCache
	clients      rpc.IRpcClients

	PiDyConf *dycfg.PresentIllustrationDyConf
}

func NewManager(ctx context.Context, cfg *conf.StartConfig, dyConf dycfg.IConfDynamic) (IManager, error) {
	cache_, err := cache.NewCache(ctx, cfg.RedisConfig)
	if nil != err {
		log.ErrorWithCtx(ctx, "init redis fail, err: %v", err)
		return nil, err
	}

	store_, err := store.NewStore(ctx, cfg.MysqlConfig, cfg.MysqlReadOnlyConfig, dyConf)
	if nil != err {
		log.ErrorWithCtx(ctx, "init store fail, err: %v", err)
		return nil, err
	}

	bgCtx := protogrpc.NewContextWithInfo(ctx)

	illustrationConfigCache, err := localcache.NewIllustrationCfgLocalCache(bgCtx, func(ctx context.Context) ([]*pb.IllustrationConfig, error) {
		list, err := store_.GetAllIllustrationConfig(bgCtx)
		if err != nil {
			log.ErrorWithCtx(bgCtx, "NewIllustrationCfgLocalCache GetAllIllustrationConfig store err: %v", err)
			return nil, err
		}
		pbList := make([]*pb.IllustrationConfig, 0, len(list))
		for _, v := range list {
			pbList = append(pbList, v.ToPb())
		}
		return pbList, nil
	}, 11*time.Second)
	if err != nil {
		log.ErrorWithCtx(ctx, "init NewIllustrationCfgLocalCache err: %v", err)
		return nil, err
	}
	presentCache, err := localcache.NewPresentCache()
	if err != nil {
		log.ErrorWithCtx(ctx, "init NewPresentCache err: %v", err)
		return nil, err
	}
	cli := rpc.NewRpcClients(dyConf)
	log.InfoWithCtx(ctx, "NewManager ok")
	manager := &Manager{
		cache:        cache_,
		store:        store_,
		dyConfig:     dyConf,
		clients:      cli,
		illCfgCache:  illustrationConfigCache,
		presentCache: presentCache,
		PiDyConf:     dyConf.Get(),
	}
	//物品过期检查
	coroutine.FixIntervalExec(manager.CheckWhiteSwitch, time.Minute*5)
	return manager, nil
}

func (m *Manager) Close() {
	_ = m.cache.Close()
	_ = m.store.Close()
}

// 统计收礼事件
func (m *Manager) HandlerPresentEvent(ctx context.Context, present *kafkapresent.PresentEvent) error {
	allCfg := m.illCfgCache.GetIllustrationCfgListCache()
	sendTime := uint64(present.SendTime)
	for _, cfg := range allCfg {
		if sendTime < cfg.StartTime || cfg.EndTime > 0 && cfg.EndTime < sendTime {
			continue
		}
		for _, giftInfo := range cfg.PresentList {
			if giftInfo.GiftId == present.ItemId {
				oldStatus, result, oldLv, newLv, err := m.store.CalcReceivedGiftStat(ctx, cfg, present)
				if errors.Is(err, store.ErrOrderExist) {
					break
				}
				if err != nil { //如果失败，消费框架重试
					log.ErrorWithCtx(ctx, "HandlerPresentEvent id:%d, event:%s, err: %v", cfg.Id, present.String(), err)
					return err
				}
				_ = m.cache.ClearIllustration(ctx, present.TargetUid)
				m.PushReachUpgradeMsg(ctx, present.TargetUid, present.ChannelId, cfg, oldStatus, result, oldLv, newLv)
				break
			}
		}
	}

	if present.PriceType == uint32(userpresent.PresentPriceType_PRESENT_PRICE_TBEAN) {
		//记录所有订单表，用于对账。只对账T豆礼物
		err := m.store.AddAllUserGiftStat(ctx, &store.AllUserGiftStat{
			OrderId:     present.OrderId,
			Uid:         present.Uid,
			ToUid:       present.TargetUid,
			GiftId:      present.ItemId,
			GiftNum:     present.ItemCount,
			TotalPrice:  present.Price * present.ItemCount,
			OutsideTime: time.Unix(int64(present.SendTime), 0),
		})
		if err != nil { //失败，消费框架重试
			log.ErrorWithCtx(ctx, "HandlerPresentEvent all gift order event:%s, err: %v", present.String(), err)
			return err
		}
	}
	return nil
}

func (m *Manager) GetLastOpenTime(ctx context.Context, uid uint32) (uint32, error) {
	ts, err := m.cache.GetLastOpenTime(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetLastOpenTime cache uid:%d, err: %v", uid, err)
		return 0, err
	}
	if ts > 0 {
		return ts, nil
	}
	ts, err = m.store.GetLastOpenTime(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetLastOpenTime store uid:%d, err: %v", uid, err)
		return 0, err
	}
	if ts == 0 {
		ts = m.dyConfig.Get().FuncStartTs
	}
	_ = m.cache.SetLastOpenTime(ctx, uid, ts)
	return ts, nil
}

func (m *Manager) IsCheckNew(cfg *pb.IllustrationConfig, nowTs, lastOpenTs uint64) bool {
	if cfg.EndTime > 0 && cfg.EndTime <= nowTs || cfg.StartTime > nowTs { //先过滤过期和未开始的
		return false
	}
	newStartTs := m.dyConfig.Get().NewStartTs
	if newStartTs > 0 && cfg.StartTime < newStartTs { //开始计算上新的时间戳
		return false
	}
	if uint64(lastOpenTs) <= cfg.StartTime && cfg.StartTime <= nowTs { //返场也算上新
		return true
	}
	if uint64(lastOpenTs) <= cfg.CreateTime && cfg.CreateTime <= nowTs { //新建图鉴开始时间为过去时间的也算上新
		return true
	}
	return false
}

// 获取用户所有图鉴
func (m *Manager) GetIllustrationListAll(ctx context.Context, uid uint32, isSelf bool, isClearRed bool) ([]*pb.IllustrationInfo, error) {
	list, err := m.GetIllustrationList(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetIllustrationListAll uid:%d, err: %v", uid, err)
		return nil, ErrSysErr
	}
	pblist := []*pb.IllustrationInfo{}
	haveIllustrations := map[uint32]*pb.IllustrationInfo{}
	for _, info := range list {
		cfg := m.illCfgCache.GetIllustrationCfgCache(info.Id)
		if cfg == nil {
			continue
		}
		haveIllustrations[info.Id] = info
		pblist = append(pblist, info)
	}

	lastOpenTs := uint32(0)
	nowTs := uint64(time.Now().Unix())
	if isSelf {
		lastOpenTs, err = m.GetLastOpenTime(ctx, uid)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetIllustrationListAll GetLastOpenTime uid:%d, err: %v", uid, err)
			return nil, ErrSysErr
		}
	}
	allCfg := m.illCfgCache.GetIllustrationCfgListCache()
	for _, cfg := range allCfg {
		info, ok := haveIllustrations[cfg.Id]
		if !ok {
			if nowTs < cfg.StartTime || cfg.EndTime > 0 && cfg.EndTime < nowTs { //未生效的不加入
				continue
			}
			pblist = append(pblist, &pb.IllustrationInfo{
				Id:          cfg.Id,
				TotalGiftId: uint32(len(cfg.PresentList)),
			})
		} else {
			info.TotalGiftId = uint32(len(cfg.PresentList))
			if info.Status == uint32(pblogic.IllustrationCollectStatus_ILLUSTRATION_COLLECT_STATUS_COLLECTED) {
				info.Level = m.dyConfig.Get().GetLevelByPrice(info.Value)
			}
		}
	}

	if isSelf {
		for _, info := range pblist {
			cfg := m.illCfgCache.GetIllustrationCfgCache(info.Id)
			if cfg == nil {
				continue
			}
			if m.IsCheckNew(cfg, nowTs, uint64(lastOpenTs)) { //上新
				info.HasNew = true
			}
		}
		if isClearRed { //清红点
			_ = m.store.SetLastOpenTime(ctx, uid, uint32(nowTs))
			_ = m.cache.CleanLastOpenTime(ctx, uid)
			affectedRows, _ := m.store.CleanRedpoint(ctx, uid, lastOpenTs)
			if affectedRows > 0 {
				m.cache.ClearIllustration(ctx, uid)
			}
			log.InfoWithCtx(ctx, "Clean redpoint uid:%d, affected:%d", uid, affectedRows)
		}
	}
	return pblist, nil
}

// 获取用户所有有记录的图鉴
func (m *Manager) GetIllustrationList(ctx context.Context, uid uint32) ([]*pb.IllustrationInfo, error) {
	list, err := m.cache.GetIllustrationListAll(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetIllustrationList cache uid:%d, err: %v", uid, err)
		return nil, err
	}

	if list != nil {
		return list, nil
	}
	list, _, err = m.loadIllustrationListAll(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetIllustrationList,loadIllustrationListAll uid:%d, err: %v", uid, err)
		return nil, err
	}
	return list, nil
}

func (m *Manager) loadIllustrationListAll(ctx context.Context, uid uint32) ([]*pb.IllustrationInfo, *pb.GetIllustrationSummaryRsp, error) {
	modelList, err := m.store.GetIllustrationListAll(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "loadIllustrationListAll store uid:%d, err: %v", uid, err)
		return nil, nil, err
	}
	infoList := []*pb.IllustrationInfo{}
	summaryRsp := &pb.GetIllustrationSummaryRsp{}
	lvCounts := map[uint32]uint32{}
	for _, mod := range modelList {
		cfg := m.illCfgCache.GetIllustrationCfgCache(mod.IllustrationId)
		if cfg == nil {
			continue
		}
		info := &pb.IllustrationInfo{
			Id:       mod.IllustrationId,
			Status:   mod.Status,
			Progress: mod.GiftIdNum,
			Value:    mod.TotalPrice,
			Level:    m.dyConfig.Get().GetLevelByPrice(mod.TotalPrice),
			RedDot:   pb.IllustrationInfoRedType(mod.RedPoint),
		}
		infoList = append(infoList, info)
		//生成摘要信息
		if info.Status == uint32(pblogic.IllustrationCollectStatus_ILLUSTRATION_COLLECT_STATUS_COLLECTED) {
			summaryRsp.CollectedTotal++
		} else {
			info.Level = 0
		}
		if info.RedDot > 0 {
			summaryRsp.RedDot = true
		}
		if info.Level > 0 {
			lvCounts[info.Level]++
		}
	}

	lvConfs := m.dyConfig.Get().LevelConfList
	for i := len(lvConfs) - 1; i >= 0; i-- {
		if cnt, ok := lvCounts[lvConfs[i].Lv]; ok {
			summaryRsp.TopList = append(summaryRsp.TopList, &pb.SummaryTopInfo{
				Level: lvConfs[i].Lv,
				Count: cnt,
			})
			if len(summaryRsp.TopList) == 2 {
				break
			}
		}
	}

	_ = m.cache.SetIllustration(ctx, uid, infoList, summaryRsp)

	return infoList, summaryRsp, nil
}

func (m *Manager) GetIllustrationSummary(ctx context.Context, uid uint32) (*pb.GetIllustrationSummaryRsp, error) {
	rsp, err := m.cache.GetIllustrationSummary(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetIllustrationSummary cache uid:%d, err: %v", uid, err)
		return nil, ErrSysErr
	}

	hasCollected := map[uint32]struct{}{}
	if rsp == nil {
		var infoList []*pb.IllustrationInfo
		infoList, rsp, err = m.loadIllustrationListAll(ctx, uid)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetIllustrationSummary,loadIllustrationListAll uid:%d, err: %v", uid, err)
			return nil, ErrSysErr
		}
		for _, info := range infoList {
			if info.Status == uint32(pblogic.IllustrationCollectStatus_ILLUSTRATION_COLLECT_STATUS_COLLECTED) {
				hasCollected[info.Id] = struct{}{}
			}
		}
	} else {
		hasCollected, err = m.cache.GetUserHasCollected(ctx, uid)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetIllustrationSummary cache uid:%d, err: %v", uid, err)
			return nil, ErrSysErr
		}
	}

	//未收集：当前生效且未收集成功的数量。需要每次计算
	nowTs := uint64(time.Now().Unix())
	allCfg := m.illCfgCache.GetIllustrationCfgListCache()
	notCollectNum := uint32(0)
	for _, cfg := range allCfg {
		if cfg.StartTime <= nowTs && (cfg.EndTime == 0 || nowTs <= cfg.EndTime) {
			if _, ok := hasCollected[cfg.Id]; !ok {
				notCollectNum++
			}
		}
	}
	rsp.NotCollectTotal = notCollectNum

	//获取上新标签
	lastOpenTs, err := m.GetLastOpenTime(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetIllustrationSummary GetLastOpenTime uid:%d, err: %v", uid, err)
		return rsp, err
	}
	for _, cfg := range allCfg {
		if m.IsCheckNew(cfg, nowTs, uint64(lastOpenTs)) { //上新
			rsp.HasNew = true
			break
		}
	}
	return rsp, nil
}

func (m *Manager) GetIllustrationDetail(ctx context.Context, uid, illustrationId uint32) (*pb.GetIllustrationDetailRsp, error) {
	rsp := &pb.GetIllustrationDetailRsp{
		BaseInfo: &pb.IllustrationInfo{
			Id: illustrationId,
		},
	}
	info, err := m.store.GetIllustrationById(ctx, uid, illustrationId)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetIllustrationDetail store uid:%d,%d, err: %v", uid, illustrationId, err)
		return rsp, ErrSysErr
	}
	if info == nil {
		return rsp, nil
	}
	modelList, err := m.store.GetIllustrationDetail(ctx, uid, illustrationId)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetIllustrationDetail store uid:%d,%d, err: %v", uid, illustrationId, err)
		return rsp, ErrSysErr
	}
	rsp.BaseInfo.Status = info.Status
	rsp.BaseInfo.Progress = info.GiftIdNum
	rsp.BaseInfo.Value = info.TotalPrice
	if info.Status == uint32(pblogic.IllustrationCollectStatus_ILLUSTRATION_COLLECT_STATUS_COLLECTED) {
		rsp.BaseInfo.Level = m.dyConfig.Get().GetLevelByPrice(info.TotalPrice)
	}
	cfg := m.illCfgCache.GetIllustrationCfgCache(illustrationId)
	if cfg != nil {
		rsp.BaseInfo.TotalGiftId = uint32(len(cfg.PresentList))
	}
	for _, mod := range modelList {
		totalPrice := mod.TotalPrice
		if mod.PriceType != uint32(userpresent.PresentPriceType_PRESENT_PRICE_TBEAN) {
			totalPrice = 0
		}
		rsp.GiftList = append(rsp.GiftList, &pb.IllustrationGiftInfo{
			GiftId:        mod.GiftId,
			ReceivedNum:   mod.GiftNum,
			ReceivedPrice: totalPrice,
			PriceType:     mod.PriceType,
		})
	}
	return rsp, nil
}

func (m *Manager) CheckWhiteSwitch() {
	cf := m.dyConfig.Get()
	if m.PiDyConf == cf {
		return
	}
	changeUids := []uint32{}
	for _, uid := range cf.SwitchWhiteUids {
		if _, ok := m.PiDyConf.SwitchWiteUidsMap[uid]; !ok {
			changeUids = append(changeUids, uid)
		}
	}

	for _, uid := range m.PiDyConf.SwitchWhiteUids {
		if _, ok := cf.SwitchWiteUidsMap[uid]; !ok {
			changeUids = append(changeUids, uid)
		}
	}
	ctx, cancel := context.WithTimeout(context.Background(), time.Minute*4)
	defer cancel()
	log.InfoWithCtx(ctx, "CheckWhiteSwitch changeUids:%v", changeUids)
	m.PiDyConf = cf
	for _, uid := range changeUids {
		err := m.clients.PushSwitchChangeMsg(ctx, uid, cf.IsUserSwitch(uid))
		if err != nil {
			log.ErrorWithCtx(ctx, "CheckWhiteSwitch push err:%v, %v", uid, cf.IsUserSwitch(uid))
		}
	}
}

func (m *Manager) GetUserSampleRedpoint(ctx context.Context, uid uint32) (*pb.GetUserSampleRedpointRsp, error) {
	rsp := &pb.GetUserSampleRedpointRsp{}
	//先检查上新
	lastOpenTs, err := m.GetLastOpenTime(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetUserSampleRedpoint GetLastOpenTime uid:%d, err: %v", uid, err)
		return rsp, ErrSysErr
	}
	allCfg := m.illCfgCache.GetIllustrationCfgListCache()
	nowTs := uint64(time.Now().Unix())
	for _, cfg := range allCfg {
		if m.IsCheckNew(cfg, nowTs, uint64(lastOpenTs)) { //上新
			rsp.Redpoint = true
			return rsp, nil
		}
	}

	summaryRsp, err := m.cache.GetIllustrationSummary(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetUserSampleRedpoint GetIllustrationSummary cache uid:%d, err: %v", uid, err)
		return rsp, ErrSysErr
	}

	if summaryRsp != nil {
		rsp.Redpoint = summaryRsp.RedDot
		return rsp, nil
	}

	_, summaryRsp, err = m.loadIllustrationListAll(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetUserSampleRedpoint,loadIllustrationListAll uid:%d, err: %v", uid, err)
		return rsp, ErrSysErr
	}

	rsp.Redpoint = summaryRsp.RedDot
	return rsp, nil
}

func (m *Manager) CleanUserIllustration(ctx context.Context, uid uint32) error {
	if !m.dyConfig.Get().IsDebug {
		return nil
	}
	log.InfoWithCtx(ctx, "CleanUserIllustration uid:%d", uid)
	err := m.store.CleanUserIllustration(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "CleanUserIllustration uid:%d, err: %v", uid, err)
		return ErrSysErr
	}
	m.cache.ClearIllustration(ctx, uid)
	return nil
}
