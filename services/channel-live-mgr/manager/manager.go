package manager

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/go-redis/redis"
	"gitlab.ttyuyin.com/avengers/tyr/compatible/proto"
	"gitlab.ttyuyin.com/golang/gudetama/oss/datacenter"
	"golang.52tt.com/clients/account"
	"golang.52tt.com/clients/channel"
	time_present2 "golang.52tt.com/protocol/app/time_present"
	time_present "golang.52tt.com/protocol/services/time-present"

	//"golang.52tt.com/clients/channelim"
	"golang.52tt.com/clients/channelmic"
	PushNotification "golang.52tt.com/clients/push-notification/v2"
	"golang.52tt.com/clients/user-profile-api"
	ukw "golang.52tt.com/clients/you-know-who"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	cpb "golang.52tt.com/protocol/app/channel"
	pbLogic "golang.52tt.com/protocol/app/channel-live-logic"
	gaPush "golang.52tt.com/protocol/app/push"
	upb "golang.52tt.com/protocol/app/userpresent"
	"golang.52tt.com/protocol/common/status"
	pb "golang.52tt.com/protocol/services/channellivemgr"
	mpb "golang.52tt.com/protocol/services/channelmicsvr"
	chpb "golang.52tt.com/protocol/services/channelsvr"
	"golang.52tt.com/protocol/services/minToolkit/kafka/pb/kafkasimplemic"
	pushPb "golang.52tt.com/protocol/services/push-notification/v2"
	//"golang.52tt.com/protocol/services/youknowwho"
	"golang.52tt.com/protocol/app"
	"golang.52tt.com/services/channel-live-mgr/cache"
	"golang.52tt.com/services/channel-live-mgr/conf"
	"golang.52tt.com/services/channel-live-mgr/items"
	"golang.52tt.com/services/channel-live-mgr/kafka_prod"
	"golang.52tt.com/services/channel-live-mgr/mysql"
	"golang.52tt.com/services/channel-live-mgr/pk"
	"strconv"
	"strings"
	"time"
)

const (
	lockKey                         = "channel_live_task_lock"
	OPEN_NORMAL_QUEUE_UP_MIC uint32 = 0x80 //channelinfodef.h enum E_CHANNEL_SWITCH_FLAG_BITMAP_TYPE
	timeLayout                      = "2006-01-02 15:04:05"

	DefaultUkwLevel uint32 = 1
)

type Manager struct {
	//chanenlIMCli    *channelim.Client
	channelCli        *channel.Client
	channelMicCli     *channelmic.Client
	accountCli        *account.Client
	cacheClient       *cache.ChannelLiveMgrCache
	mysqlStore        *mysql.Store
	pushClient        *PushNotification.Client
	sc                *conf.ServiceConfigT
	ctx               context.Context
	channelLiveProd   *kafka_prod.KafkaProduce
	redisMicQue       *redis.Client
	userProfileCli    *user_profile_api.Client
	ukwCli            *ukw.Client
	timePresentClient time_present.TimePresentClient
}

func NewManager(cacheClient *cache.ChannelLiveMgrCache, mysqlStore *mysql.Store, rd *redis.Client, pushClient *PushNotification.Client, accountCli *account.Client,
	sc *conf.ServiceConfigT, channelLiveProd *kafka_prod.KafkaProduce, ctx context.Context) *Manager {

	channelMicCli := channelmic.NewClient()
	channelCli := channel.NewClient()
	//cli := channelmember.NewClient(grpc.WithBlock(), grpc.WithInsecure(), grpc.WithAuthority("channelmemberviprank.52tt.local"))

	userProfileCli, err := user_profile_api.NewClient()
	if err != nil {
		log.Errorf("user_profile_api.NewClient() failed err:%v", err)
	}

	ukwCli, err := ukw.NewClient()
	if err != nil {
		log.Errorf("ukw.NewClient() failed err:%v", err)
	}
	timePresentClient, err := time_present.NewClient(ctx)
	if err != nil {
		log.Errorf("time_present.NewClient() failed err:%v", err)
	}

	return &Manager{
		redisMicQue:       rd,
		channelCli:        channelCli,
		accountCli:        accountCli,
		cacheClient:       cacheClient,
		mysqlStore:        mysqlStore,
		pushClient:        pushClient,
		channelMicCli:     channelMicCli,
		channelLiveProd:   channelLiveProd,
		ctx:               ctx,
		sc:                sc,
		userProfileCli:    userProfileCli,
		ukwCli:            ukwCli,
		timePresentClient: timePresentClient,
	}
}

// 数据上报
func (m *Manager) DataCenterReport(bizID string, channelId uint32, args map[string]interface{}) {
	reportBeginTime := m.cacheClient.GetReportBeginTime(channelId)
	startTime := time.Unix(int64(reportBeginTime), 0).Format("2006-01-02 15:04:05")
	endTime := time.Now().Format("2006-01-02 15:04:05")
	args["startTime"] = startTime
	args["endTime"] = endTime
	args["createTime"] = time.Now().Format("2006-01-02 15:04:05")
	datacenter.StdReportKV(bizID, args) //"************"
}

func (s *Manager) FillMicList(channelIdA, channelIdB uint32) ([]*pbLogic.PkMicSpace, []*pbLogic.PkMicSpace) {
	ctx, cancel := context.WithTimeout(context.Background(), time.Second*5)
	defer cancel()

	pkMicListA := make([]*pbLogic.PkMicSpace, 0)
	pkMicListB := make([]*pbLogic.PkMicSpace, 0)

	micListA, err := s.channelMicCli.GetMicrList(ctx, channelIdA, channelIdA)
	if err != nil {
		log.Errorf("FillMicList GetMicrList err:%v cid:%v", err, channelIdA)
		return pkMicListA, pkMicListB
	}

	micListB, err := s.channelMicCli.GetMicrList(ctx, channelIdB, channelIdB)
	if err != nil {
		log.Errorf("FillMicList GetMicrList err:%v cid:%v", err, channelIdB)
		return pkMicListA, pkMicListB
	}

	uidList := make([]uint32, 0)
	if micListA != nil {
		for _, m := range micListA.AllMicList {
			if m.MicUid == 0 {
				continue
			}
			uidList = append(uidList, m.MicUid)
		}
	}

	if micListB != nil {
		for _, m := range micListB.AllMicList {
			if m.MicUid == 0 {
				continue
			}
			uidList = append(uidList, m.MicUid)
		}
	}

	if len(uidList) == 0 {
		log.InfoWithCtx(ctx, "FillMicList micList is empty %d %d", channelIdA, channelIdB)
		return pkMicListA, pkMicListB
	}

	mapUid2Profile, err := s.userProfileCli.BatchGetUserProfileV2(ctx, uidList, true)
	if err != nil {
		log.ErrorWithCtx(ctx, "FillMicList BatchGetUserProfileV2 failed uidList:%v err:%v", uidList, err)
		return pkMicListA, pkMicListB
	}

	pkstateA := pk.PkState{
		ChannelID_A: channelIdA,
	}
	micA, _ := pkstateA.GetMicList()

	pkstateB := pk.PkState{
		ChannelID_A: channelIdB,
	}
	micB, _ := pkstateB.GetMicList()

	if micListA != nil {
		for _, m := range micListA.AllMicList {
			if m.MicUid == 0 {
				continue
			}
			clientId := ""
			videoId := ""
			if mic, ok := micA[m.MicUid]; ok {
				clientId = mic.VoiceID
				videoId = mic.VideoID
			}

			pkMicListA = append(pkMicListA, &pbLogic.PkMicSpace{
				MicId:                m.MicId,
				Uid:                  mapUid2Profile[m.MicUid].GetUid(),
				Account:              mapUid2Profile[m.MicUid].GetAccount(),
				Nick:                 mapUid2Profile[m.MicUid].GetNickname(),
				ChannelClientId:      clientId,
				ChannelVideoClientId: videoId,
			})
		}
	}

	if micListB != nil {
		for _, m := range micListB.AllMicList {
			if m.MicUid == 0 {
				continue
			}
			clientId := ""
			videoId := ""
			if mic, ok := micB[m.MicUid]; ok {
				clientId = mic.VoiceID
				videoId = mic.VideoID
			}

			pkMicListB = append(pkMicListB, &pbLogic.PkMicSpace{
				MicId:                m.MicId,
				Uid:                  mapUid2Profile[m.MicUid].GetUid(),
				Account:              mapUid2Profile[m.MicUid].GetAccount(),
				Nick:                 mapUid2Profile[m.MicUid].GetNickname(),
				ChannelClientId:      clientId,
				UserProfile:          mapUid2Profile[m.MicUid],
				ChannelVideoClientId: videoId,
			})
		}
	}

	return pkMicListA, pkMicListB
}

func (m *Manager) PushReliableChannelMsg(ctx context.Context, fromUser, toUser *account.User, cid uint32,
	nomal_opt_content []byte, pb_opt_content []byte, content []byte, cmd uint32) error {

	bMsg := cpb.ChannelBroadcastMsg{
		ToChannelId: cid,
		Type:        cmd,
	}

	if content != nil {
		bMsg.Content = content
	}

	if pb_opt_content != nil {
		bMsg.PbOptContent = pb_opt_content
	}

	if nomal_opt_content != nil {
		bMsg.AttContent = nomal_opt_content
	}

	if fromUser != nil {
		bMsg.FromUid = fromUser.Uid
		bMsg.FromAccount = fromUser.Username
		bMsg.FromNick = fromUser.Nickname
		bMsg.FromSex = fromUser.Sex
	}

	if toUser != nil {
		bMsg.TargetUid = toUser.Uid
		bMsg.TargetSex = toUser.Sex
		bMsg.TargetNick = toUser.Nickname
		bMsg.TargetAccount = toUser.Username
	}

	bMsgBin, err := bMsg.Marshal()

	if nil != err {
		log.Errorf("PushReliableChannelMsg Marshal cid:%v err:%v", cid, err)
		return err
	}

	pmsg := &gaPush.PushMessage{
		Cmd:     uint32(gaPush.PushMessage_CHANNEL_MSG_BRO),
		Content: bMsgBin,
		SeqId:   uint32(time.Now().Unix()) + cid,
	}

	pmsgbin, _ := pmsg.Marshal()

	err = m.pushClient.PushMulticast(ctx, uint64(cid), fmt.Sprintf("%d@channel", cid), []uint32{}, &pushPb.CompositiveNotification{
		Sequence: uint32(time.Now().Unix()) + cid,
		TerminalTypeList: []uint32{
			protocol.MobileAndroidTT,
			protocol.MobileIPhoneTT,
		},
		TerminalTypePolicy: PushNotification.DefaultPolicy,
		AppId:              uint32(protocol.TT),
		ProxyNotification: &pushPb.ProxyNotification{
			Type:       uint32(pushPb.ProxyNotification_PUSH),
			Payload:    pmsgbin,
			Policy:     pushPb.ProxyNotification_DEFAULT,
			ExpireTime: 600,
		},
	})

	if err != nil {
		log.Errorf("PushReliableChannelMsg fail err:%v", err)
	} else {
		log.Debugf("PushReliableChannelMsg seqid:%v ", pmsg.SeqId)
	}

	return nil
}

func (s *Manager) PushChannelMsg(ctx context.Context, pbmsg []byte, cmd uint32, channelid_list []uint32) error {

	log.Debugf("PushChannelMsg cmd:%v channel_list%v", cmd, channelid_list)

	for _, cid := range channelid_list {
		bMsg := cpb.ChannelBroadcastMsg{
			ToChannelId:  cid,
			Type:         cmd,
			PbOptContent: pbmsg,
		}

		bMsgBin, _ := bMsg.Marshal()

		pmsg := &gaPush.PushMessage{
			Cmd:     uint32(gaPush.PushMessage_CHANNEL_MSG_BRO),
			Content: bMsgBin,
			SeqId:   uint32(time.Now().Unix()) + cid,
		}

		pmsgbin, _ := pmsg.Marshal()

		err := s.pushClient.PushMulticast(ctx, uint64(cid), fmt.Sprintf("%d@channel", cid), []uint32{}, &pushPb.CompositiveNotification{
			Sequence: uint32(time.Now().Unix()) + cid,
			TerminalTypeList: []uint32{
				protocol.MobileAndroidTT,
				protocol.MobileIPhoneTT,
			},
			TerminalTypePolicy: PushNotification.DefaultPolicy,
			AppId:              uint32(protocol.TT),
			ProxyNotification: &pushPb.ProxyNotification{
				Type:       uint32(pushPb.ProxyNotification_PUSH),
				Payload:    pmsgbin,
				Policy:     pushPb.ProxyNotification_DEFAULT,
				ExpireTime: 600,
			},
		})
		if err != nil {
			log.Errorf("PushChannelMsg PushChannelMsg fail cid:%d err:%v", cid, err)
		}
	}
	return nil
}

// 生成主播积分月剩余表
func (m *Manager) GenMonthAnchorScoreProcess() {
	now := time.Now()
	if now.Day() == 1 && now.Hour() == 0 {

		lockKey := "gen_month_score_lock"
		getLock, _ := m.cacheClient.GetLockWithTtl(lockKey, time.Hour)
		if !getLock {
			return
		}

		thisMonth := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, time.Local)
		lastMonth := thisMonth.AddDate(0, -1, 0)

		// 生成主播积分月度剩余表
		tblExist, err := m.mysqlStore.GenMonthAnchorScore(lastMonth)
		if err != nil {
			// 解锁，继续尝试执行
			m.cacheClient.UnLock(lockKey)
			log.Errorf("Failed to GenMonthAnchorScore err(%s)", err.Error())

		} else if !tblExist {
			// 修正
			err = m.mysqlStore.AdjustMonthAnchorScore(lastMonth, time.Now())
			if err != nil {
				log.Errorf("Failed to AdjustMonthAnchorScore err(%s)", err.Error())
			}
		}
	}
}

// 更新PK进度 TODO
func (m *Manager) HandlerPkProcess() {
	if !conf.GetGConfig().IsProd() {
		return
	}

	bLock, _ := m.cacheClient.GetLock(lockKey)
	if !bLock {
		return
	}

	tasks, _ := m.cacheClient.GetTriggerTask()

	m.cacheClient.UnLock(lockKey)

	for _, task := range tasks {
		m.HandlerTask(task)
	}
}

func (m *Manager) FillUserDate(ctx context.Context, pushMsg *pbLogic.ChannelLivePkStatusPushMsg, channelId uint32) error {
	pkStateA := pk.PkState{
		ChannelID_A: channelId,
	}
	pkInfoA, err := pkStateA.GetPkInfo()
	if err != nil {
		log.Errorf("FillUserDate err:%v channelID:%v", err, channelId)
		return err
	}

	if pkInfoA.TChannelID == 0 || pkInfoA.TUID == 0 {
		log.Errorf("FillUserDate empty channelID:%v", channelId)
		return errors.New("empty")
	}

	pkStateB := pk.PkState{
		ChannelID_A: pkInfoA.TChannelID,
		ChannelID_B: channelId,
	}
	pkInfoB, err := pkStateB.GetPkInfo()
	if err != nil {
		log.Errorf("FillUserDate err:%v channelID:%v", err, pkInfoA.TChannelID)
		return err
	}

	if pkInfoB.TUID == 0 || pkInfoB.TChannelID == 0 {
		log.Errorf("FillUserDate empty2 channelID:%v", pkInfoA.TChannelID)
		return errors.New("empty")
	}

	firstStrUidInfo, cid := pkStateB.GetFirstShootUid()
	firstUidInfo := m.ParseStrUidInfo(cid, firstStrUidInfo)

	log.Debugf("FillUserDate firstUid:%v uidInfo:%v cid:%v", firstStrUidInfo, firstUidInfo, cid)

	uidList := []uint32{pkInfoA.TUID, pkInfoB.TUID}
	if firstUidInfo.GetUid() != 0 {
		uidList = append(uidList, firstUidInfo.GetUid())
	}

	users, err := m.accountCli.GetUsersMap(ctx, uidList)
	if err != nil {
		log.Errorf("FillUserDate GetUsersMap err:%v", err)
		return err
	}

	userB, ok := users[pkInfoA.TUID]
	if !ok {
		log.Errorf("FillUserDate GetUser1 err:%v uid:%v", err, pkInfoA.TUID)
		return err
	}

	userA, ok := users[pkInfoB.TUID]
	if !ok {
		log.Errorf("FillUserDate GetUser2 err:%v uid:%v", err, pkInfoB.TUID)
		return err
	}

	pushMsg.ChallengeUser = &pbLogic.PkUserInfo{
		Uid:       pkInfoA.TUID,
		Account:   userB.Username,
		Nickname:  userB.Nickname,
		Sex:       userB.Sex,
		Alias:     userB.Alias,
		ChannelId: pkInfoA.TChannelID,
		Status:    pbLogic.EnumChannelLiveStatus_OPEN,
	}

	pushMsg.BechallengeUser = &pbLogic.PkUserInfo{
		Uid:       pkInfoB.TUID,
		Account:   userA.Username,
		Nickname:  userA.Nickname,
		Sex:       userA.Sex,
		Alias:     userA.Alias,
		ChannelId: pkInfoB.TChannelID,
		Status:    pbLogic.EnumChannelLiveStatus_OPEN,
	}

	if firstUidInfo.GetUid() != 0 {
		userFirst, ok := users[firstUidInfo.GetUid()]
		if !ok {
			log.ErrorWithCtx(ctx, "FillUserDate no firstUid info firstUid:%d cid:%d", firstUidInfo.GetUid(), cid)
			return err
		}

		resUid, firstAccount, firstNickname, userProfile, _ := getUserAccountInfoFromUidInfo(userFirst, firstUidInfo)

		if pushMsg.CommonInfo == nil {
			pushMsg.CommonInfo = &pbLogic.PkCommonInfo{}
		}
		pushMsg.CommonInfo.FirstKillUser = &pbLogic.SendGiftUserInfo{
			Uid:         resUid,
			Account:     firstAccount,
			Nickname:    firstNickname,
			Sex:         uint32(userFirst.GetSex()),
			FirstKill:   true,
			ChannelId:   cid,
			UserProfile: userProfile,
		}
	}

	// 最后填下亲密礼物的信息
	intimatePresent := m.GetLiveIntimatePresentInfo(ctx, channelId, []uint32{pkInfoA.TChannelID})
	pushMsg.TimePresentInfo = intimatePresent

	return nil
}

func (m *Manager) FillRankData(ctx context.Context, pushMsg *pbLogic.ChannelLivePkStatusPushMsg, pkstate *pk.PkState, task *cache.TickTask) (*pk.PkScore, *pk.PkScore) {
	pkstate.ChannelID_A = task.AChannelId
	aPkInfo, _ := pkstate.GetPkInfo()
	aRank, _ := pkstate.GetPkRank(int64(aPkInfo.FBeginTime), 0, 3)
	aUseItemTopUidStr := pkstate.GetTopUseItem(aPkInfo.FBeginTime)
	aUseItemTopUidInfo := m.ParseStrUidInfo(task.AChannelId, aUseItemTopUidStr)
	aRankCnt := pkstate.GetPkRankLen(aPkInfo.FBeginTime)
	aRedScore, aRedCnt := pkstate.GetRedScoreTotal()

	pkstate.ChannelID_A = task.BChannelId
	bPkInfo, _ := pkstate.GetPkInfo()
	bRank, _ := pkstate.GetPkRank(int64(bPkInfo.FBeginTime), 0, 3)
	bUseItemTopUidStr := pkstate.GetTopUseItem(bPkInfo.FBeginTime)
	bUseItemTopUidInfo := m.ParseStrUidInfo(task.BChannelId, bUseItemTopUidStr)
	bRankCnt := pkstate.GetPkRankLen(aPkInfo.FBeginTime)
	bRedScore, bRedCnt := pkstate.GetRedScoreTotal()

	log.Debugf("FillRankData aUseItemTopUid:%v bUseItemTopUid:%v", aUseItemTopUidStr, bUseItemTopUidStr)

	uidList := make([]uint32, 0)
	for _, rank := range aRank {
		uidInfo := m.ParseStrUidInfo(task.AChannelId, rank.StrUidInfo)
		if uidInfo.GetUid() != 0 {
			uidList = append(uidList, uidInfo.Uid)
		}
	}
	for _, rank := range bRank {
		uidInfo := m.ParseStrUidInfo(task.BChannelId, rank.StrUidInfo)
		if uidInfo.GetUid() != 0 {
			uidList = append(uidList, uidInfo.Uid)
		}
	}

	if aUseItemTopUidInfo.GetUid() > 0 {
		uidList = append(uidList, aUseItemTopUidInfo.GetUid())
	}

	if bUseItemTopUidInfo.GetUid() > 0 {
		uidList = append(uidList, bUseItemTopUidInfo.GetUid())
	}

	usermap, err := m.accountCli.GetUsersMap(ctx, uidList)
	if err != nil {
		log.Errorf("FillRankData GetUsersMap failed task:%v uidList:%v", task, uidList)
		return &pk.PkScore{}, &pk.PkScore{}
	}

	aranks := make([]*pbLogic.SendGiftUserInfo, 0)
	for _, item := range aRank {
		userInfo := m.ParseStrUidInfo(task.AChannelId, item.StrUidInfo)
		if userInfo.GetUid() != 0 {
			resUid, account, nickname, userProfile, _ := getUserAccountInfoFromUidInfo(usermap[userInfo.GetUid()], userInfo)
			aranks = append(aranks, &pbLogic.SendGiftUserInfo{
				Uid:         resUid,
				Account:     account,
				Score:       item.Score,
				Nickname:    nickname,
				Sex:         uint32(usermap[userInfo.GetUid()].GetSex()),
				UserProfile: userProfile,
			})
		}
	}

	micLista, micListb := m.FillMicList(task.AChannelId, task.BChannelId)

	pushMsg.ChallengeScore = &pbLogic.ChannelPKSingleScore{
		ChannelId:     task.AChannelId,
		ScoreValue:    aPkInfo.PKScore,
		TopScoreUser:  aranks,
		EffectItemCnt: aPkInfo.EffectCnt,
		MicList:       micLista,
	}

	//使用道具之王
	if aUseItemTopUidInfo.GetUid() > 0 {
		resUid, account, nickname, userProfile, _ := getUserAccountInfoFromUidInfo(usermap[aUseItemTopUidInfo.GetUid()], aUseItemTopUidInfo)
		pushMsg.ChallengeScore.UseItemTopUser = &pbLogic.SendGiftUserInfo{
			Uid:         resUid,
			Account:     account,
			Nickname:    nickname,
			Sex:         uint32(usermap[aUseItemTopUidInfo.GetUid()].GetSex()),
			ChannelId:   task.AChannelId,
			UserProfile: userProfile,
		}
	}

	//B房间数据
	branks := make([]*pbLogic.SendGiftUserInfo, 0)
	for _, item := range bRank {
		userInfo := m.ParseStrUidInfo(task.BChannelId, item.StrUidInfo)
		if userInfo.GetUid() != 0 {
			resUid, account, nickname, userProfile, _ := getUserAccountInfoFromUidInfo(usermap[userInfo.GetUid()], userInfo)
			branks = append(branks, &pbLogic.SendGiftUserInfo{
				Uid:         resUid,
				Account:     account,
				Score:       item.Score,
				Nickname:    nickname,
				Sex:         uint32(usermap[userInfo.GetUid()].GetSex()),
				UserProfile: userProfile,
			})
		}
	}
	pushMsg.BechallengeScore = &pbLogic.ChannelPKSingleScore{
		ChannelId:     task.BChannelId,
		ScoreValue:    bPkInfo.PKScore,
		EffectItemCnt: bPkInfo.EffectCnt,
		TopScoreUser:  branks,
		MicList:       micListb,
	}

	//使用道具之王
	if bUseItemTopUidInfo.GetUid() > 0 {
		resUid, account, nickname, userProfile, _ := getUserAccountInfoFromUidInfo(usermap[bUseItemTopUidInfo.GetUid()], bUseItemTopUidInfo)
		pushMsg.BechallengeScore.UseItemTopUser = &pbLogic.SendGiftUserInfo{
			Uid:         resUid,
			Account:     account,
			Nickname:    nickname,
			Sex:         uint32(usermap[bUseItemTopUidInfo.GetUid()].GetSex()),
			ChannelId:   task.BChannelId,
			UserProfile: userProfile,
		}
	}

	pkScoreA := &pk.PkScore{
		PkRankCnt: aRankCnt,
		RedScore:  aRedScore,
		RedCnt:    aRedCnt,
		ExtScore:  int64(aPkInfo.ExtScore),
	}

	pkScoreB := &pk.PkScore{
		PkRankCnt: bRankCnt,
		RedScore:  bRedScore,
		RedCnt:    bRedCnt,
		ExtScore:  int64(bPkInfo.ExtScore),
	}

	return pkScoreA, pkScoreB
}

func (m *Manager) HandlerTask(task *cache.TickTask) {
	log.Debugf("HandlerTask old task:%+v", *task)
	ctx, cancel := context.WithTimeout(context.Background(), time.Second*3)
	defer cancel()

	status := pbLogic.EnumChannelLivePKStatus(task.Status)
	nextStatus := status + 1
	var nextTriggeTime = conf.Sec2State[uint32(nextStatus)]

	//检查PK状态，如果已经结束就不做任何事情了
	pkstate := pk.PkState{
		ChannelID_A: task.AChannelId,
		ChannelID_B: task.BChannelId,
	}
	pkinfo, serr := pkstate.GetPkInfo()
	if serr != nil || pkinfo.TChannelID == 0 {
		log.Errorf("HandlerTask TChannelID == 0 task:%+v  serr:%v", *task, serr)
		return
	}

	//已经不是同一场PK，不处理
	if task.BeginTs != pkinfo.BeginTime {
		log.Errorf("HandlerTask beginTs not equal task:%+v beginTime:%v\n", *task, pkinfo.BeginTime)
		return
	}

	if nextStatus > pbLogic.EnumChannelLivePKStatus_FINISH {
		// 本次pk已经结束，清除pk信息。避免客户端调用pk结束接口异常。没有清除pk信息
		pkstate.FinishPK()
		log.Debugf("HandlerTask pk finish task:%+v info:%v", *task, pkinfo)
		return
	}

	extConf := conf.GetExtraTimeConf()
	pushMsg := pbLogic.ChannelLivePkStatusPushMsg{
		CommonInfo: &pbLogic.PkCommonInfo{
			BeginTime:       pkinfo.BeginTime,
			Status:          nextStatus,
			IsExtraTime:     pkinfo.IsExtra,
			PkExtraTimeRule: extConf.Rule,
			ExtraLeftTime:   uint32(extConf.LeftTs),
			IsOpenExtraTime: pkinfo.IsOpenExtra,
		},
	}

	pkScoreA, pkScoreB := &pk.PkScore{}, &pk.PkScore{}
	m.FillUserDate(ctx, &pushMsg, task.BChannelId)
	if status == pbLogic.EnumChannelLivePKStatus_LAST {
		pkScoreA, pkScoreB = m.FillRankData(ctx, &pushMsg, &pkstate, task) ////结束阶段分别推，填数据
	}

	pushMsgBin, _ := proto.Marshal(&pushMsg)
	perr := m.PushChannelMsg(ctx, pushMsgBin, uint32(cpb.ChannelMsgType_CHANNEL_LIVE_PK_STATUS_MGS), []uint32{task.AChannelId, task.BChannelId})
	if perr != nil {
		log.Errorf("HandlerTask PushChannelMsg perr:%v", perr)
	}

	log.Debugf("HandlerTask  pushMsg:%+v status:%v nextTriggeTime:%v", pushMsg, status, nextTriggeTime)

	//重新添加到ticker
	task.Status = uint32(nextStatus)
	m.cacheClient.AddTaskToTick(task, uint32(time.Now().Unix())+nextTriggeTime)

	log.Debugf("HandlerTask  newTast:%+v", *task)

	if status == pbLogic.EnumChannelLivePKStatus_LAST {

		//增加PK次数，用于限制PK次数
		m.AddPkCnt([]uint32{task.AUid, task.BUid})

		var pkResult int32
		if pkinfo.ApplyCID == pushMsg.ChallengeUser.ChannelId {
			if pushMsg.ChallengeScore.ScoreValue > pushMsg.BechallengeScore.ScoreValue {
				pkResult = 1
			} else {
				pkResult = 3
			}
		} else {
			if pushMsg.ChallengeScore.ScoreValue > pushMsg.BechallengeScore.ScoreValue {
				pkResult = 3
			} else {
				pkResult = 1
			}
		}
		//pkstate.FinishPK()
		isDelayed := pkinfo.ExtraNum
		reportMap := map[string]interface{}{
			"launchId":          pushMsg.ChallengeUser.Uid,
			"receiveId":         pushMsg.BechallengeUser.Uid,
			"launchCount":       pkScoreA.PkRankCnt,
			"receiveCount":      pkScoreB.PkRankCnt,
			"launchValue":       pushMsg.ChallengeScore.ScoreValue,
			"receiveValue":      pushMsg.BechallengeScore.ScoreValue,
			"launchNum":         pushMsg.ChallengeScore.ScoreValue,
			"receiveNum":        pushMsg.BechallengeScore.ScoreValue,
			"launchAuraValue":   pkScoreA.RedScore,
			"launchAuraNum":     pkScoreA.RedCnt,
			"receiveAuraValue":  pkScoreB.RedScore,
			"receiveAuraNum":    pkScoreB.RedCnt,
			"result":            pkResult,
			"isDelayed":         isDelayed,
			"launchExtravalue":  pkScoreA.ExtScore, //发起方延时阶段受到的T豆价值
			"receiveExtravalue": pkScoreB.ExtScore, //接受方延时阶段受到的T豆价值
			"startTime":         time.Unix(int64(pkinfo.BeginTime), 0).Format(timeLayout),
			"endTime":           time.Now().Format("2006-01-02 15:04:05"),
			"createTime":        time.Now().Format("2006-01-02 15:04:05"),
		}

		datacenter.StdReportKV("************", reportMap)

		log.Infof("FinishPK report data %+v", reportMap)

		var winUid, winChannelId, lostChannelID uint32 = 0, 0, 0
		if pushMsg.ChallengeScore.ScoreValue > pushMsg.BechallengeScore.ScoreValue {
			winUid, winChannelId = pushMsg.ChallengeUser.Uid, pushMsg.ChallengeUser.ChannelId
			lostChannelID = pushMsg.BechallengeUser.ChannelId
		} else if pushMsg.ChallengeScore.ScoreValue < pushMsg.BechallengeScore.ScoreValue {
			winUid, winChannelId = pushMsg.BechallengeUser.Uid, pushMsg.BechallengeUser.ChannelId
			lostChannelID = pushMsg.ChallengeUser.ChannelId
		}

		userScoreList := make([]*pbLogic.SendGiftUserInfo, 0)
		lostUserScoreList := make([]*pbLogic.SendGiftUserInfo, 0)
		if winChannelId > 0 {
			pkstate.ChannelID_A = winChannelId
			ranks, _ := pkstate.GetPkRank(int64(pkinfo.FBeginTime), 0, 500)
			for _, r := range ranks {
				if r.Score == 0 {
					continue
				}
				userInfo := m.ParseStrUidInfo(winChannelId, r.StrUidInfo)
				if userInfo.GetUid() != 0 {
					userScoreList = append(userScoreList, &pbLogic.SendGiftUserInfo{
						Uid:   userInfo.GetUid(),
						Score: r.Score,
					})
				}
			}
		}
		if lostChannelID > 0 {
			lostPkstate := pk.PkState{
				ChannelID_A: lostChannelID,
			}
			ranks, _ := lostPkstate.GetPkRank(int64(pkinfo.FBeginTime), 0, 500)
			for _, r := range ranks {
				if r.Score == 0 {
					continue
				}
				userInfo := m.ParseStrUidInfo(lostChannelID, r.StrUidInfo)
				if userInfo.GetUid() != 0 {
					lostUserScoreList = append(lostUserScoreList, &pbLogic.SendGiftUserInfo{
						Uid:   userInfo.GetUid(),
						Score: r.Score,
					})
				}
			}
		}

		//matchTy := conf.GetGConfig().GetMatchType( pkinfo.BeginTime )
		m.channelLiveProd.ProduceChannelLiveEvent(&pbLogic.ChannelLiveKafkaEvent{
			AnchorUid:        pushMsg.ChallengeUser.Uid,
			ChannelId:        pushMsg.ChallengeUser.ChannelId,
			OppAnchorUid:     pushMsg.BechallengeUser.Uid,
			OppChannelId:     pushMsg.BechallengeUser.ChannelId,
			ChannelPkStatus:  pbLogic.EnumChannelLivePKStatus_PUNISH,
			WinUid:           winUid,
			WinChannelId:     winChannelId,
			Ty:               pbLogic.ChannelLiveKafkaEventType_ChannelLivePkType,
			UserScoreList:    userScoreList,
			MatchModel:       pbLogic.ChannelLivePKMatchType(task.Source),
			AnchorPkValue:    pushMsg.ChallengeScore.ScoreValue,
			OppAnchorPkValue: pushMsg.BechallengeScore.ScoreValue,
			OppUserScoreList: lostUserScoreList,
			ApplyId:          pkinfo.ApplyID,
			MatchType:        int64(pkinfo.MatchSource),
			CreateTime:       time.Now().Unix(),
		})

		//记录PK值到Redis
		m.cacheClient.RecordPKValue(pushMsg.ChallengeUser.Uid, pushMsg.BechallengeScore.ScoreValue)
		m.cacheClient.RecordPKValue(pushMsg.BechallengeUser.Uid, pushMsg.ChallengeScore.ScoreValue)
	}

}

/*
func getUserAccountInfo(uid uint32, userInfo *account.User, ukwInfo *youknowwho.UKWPersonInfo) (string, string, *app.UserProfile, *app.UserUKWInfo) {
	log.Debugf("getUserAccountInfo begin userInfo:%v ukwInfo:%v", userInfo, ukwInfo)

	account, nickName := userInfo.GetUsername(), userInfo.GetNickname()
	if ukwInfo.GetAccount() != "" {
		account = ukwInfo.GetAccount()
		nickName = ukwInfo.GetNickname()
	}

	userProfile := &app.UserProfile{
		Uid:          uid,
		Account:      userInfo.GetUsername(),
		Nickname:     userInfo.GetNickname(),
		AccountAlias: userInfo.GetAlias(),
		Sex:          uint32(userInfo.GetSex()),
		Privilege: &app.UserPrivilege{
			Account:  ukwInfo.GetAccount(),
			Nickname: ukwInfo.GetNickname(),
		},
	}

	userUkwInfo := &app.UserUKWInfo{
		Level:     ukwInfo.GetLevel(),
		Medal:     ukwInfo.GetMedal(),
		HeadFrame: ukwInfo.GetHeadFrame(),
	}

	byteUkwInfo, err := proto.Marshal(userUkwInfo)
	if err != nil {
		log.Errorf("getUserAccountInfo json.Marshal failed info:%v err:%v", userUkwInfo, err)
		return account, nickName, userProfile, userUkwInfo
	}

	userProfile.Privilege.Type = uint32(app.EUserPrivilegeType_ENUM_USER_PRIVILEGE_UKW)
	userProfile.Privilege.Options = byteUkwInfo

	return account, nickName, userProfile, userUkwInfo
}
*/

func getUserAccountInfoFromUidInfo(user *account.User, userInfo *pb.UkwInfo) (uint32, string, string, *app.UserProfile, *app.UserUKWInfo) {
	log.Debugf("getUserAccountInfoFromUidInfo begin user:%v ukwInfo:%v", user, userInfo)

	uid, account, nickName := user.GetUid(), user.GetUsername(), user.GetNickname()
	if userInfo.GetLevel() > 0 {
		account = userInfo.GetAccount()
		nickName = userInfo.GetNickname()
		uid = userInfo.GetFakeUid()
	}

	userProfile := &app.UserProfile{
		Uid:          uid,
		Account:      account,
		Nickname:     nickName,
		AccountAlias: user.GetAlias(),
		Sex:          uint32(user.GetSex()),
		Privilege: &app.UserPrivilege{
			Account:  userInfo.GetAccount(),
			Nickname: userInfo.GetNickname(),
		},
	}

	userUkwInfo := &app.UserUKWInfo{
		Level:     userInfo.GetLevel(),
		Medal:     userInfo.GetMedal(),
		HeadFrame: userInfo.GetHeadFrame(),
	}

	byteUkwInfo, err := proto.Marshal(userUkwInfo)
	if err != nil {
		log.Errorf("getUserAccountInfoFromUidInfo json.Marshal failed info:%v err:%v", userUkwInfo, err)
		return uid, account, nickName, userProfile, userUkwInfo
	}

	userProfile.Privilege.Type = uint32(app.EUserPrivilegeType_ENUM_USER_PRIVILEGE_UKW)
	userProfile.Privilege.Options = byteUkwInfo

	log.Debugf("getUserAccountInfoFromUidInfo end user:%v ukwInfo:%v userProfile:%v", user, userInfo, userProfile)
	return uid, account, nickName, userProfile, userUkwInfo
}

func (m *Manager) FillScoreChange(ctx context.Context, channelId uint32, pkScoreChange *pbLogic.ChannelPKSingleScore) bool {
	pkState := pk.PkState{
		ChannelID_A: channelId,
	}

	pkInfo, err := pkState.GetPkInfo()
	if err != nil {
		return false
	}
	pkState.ChannelID_B = pkInfo.TChannelID

	pkScoreChange.ChannelId = channelId
	pkScoreChange.ScoreValue = pkInfo.PKScore
	pkScoreChange.TopScoreUser = make([]*pbLogic.SendGiftUserInfo, 0)
	pkScoreChange.EffectItemCnt = pkInfo.EffectCnt

	// 获取使用道具排行
	topUseItemUidStr := pkState.GetTopUseItem(pkInfo.FBeginTime)
	topUseItemUidInfo := m.ParseStrUidInfo(channelId, topUseItemUidStr)

	// 获取首杀
	firstStrUidInfo, firstShootCid := pkState.GetFirstShootUid()
	firstUidInfo := m.ParseStrUidInfo(firstShootCid, firstStrUidInfo)
	if firstShootCid != channelId {
		// 不是当前房间的首杀，不推了
		firstUidInfo.Uid = 0
	}

	ranks, _ := pkState.GetPkRank(int64(pkInfo.FBeginTime), 0, 3)
	uidList := make([]uint32, 0)
	for _, rank := range ranks {
		userInfo := m.ParseStrUidInfo(channelId, rank.StrUidInfo)
		if userInfo.GetUid() != 0 {
			uidList = append(uidList, userInfo.GetUid())
		}
	}

	tmpUidList := uidList
	if topUseItemUidInfo.GetUid() > 0 {
		tmpUidList = append(tmpUidList, topUseItemUidInfo.GetUid())
	}

	if firstUidInfo.GetUid() > 0 {
		tmpUidList = append(tmpUidList, firstUidInfo.GetUid())
	}

	userMap, err := m.accountCli.GetUsersMap(ctx, tmpUidList)
	if err != nil {
		log.ErrorWithCtx(ctx, "FillScoreChange GetUsersMap failed cid:%d tmpList:%v err:%v", channelId, tmpUidList, err)
		return false
	}

	if topUseItemUidInfo.GetUid() > 0 {
		resUid, account, nickName, userProfile, _ := getUserAccountInfoFromUidInfo(userMap[topUseItemUidInfo.GetUid()], topUseItemUidInfo)
		pkScoreChange.UseItemTopUser = &pbLogic.SendGiftUserInfo{
			Uid:         resUid,
			Account:     account,
			Nickname:    nickName,
			Sex:         uint32(userMap[topUseItemUidInfo.GetUid()].GetSex()),
			FirstKill:   false,
			ChannelId:   channelId,
			UserProfile: userProfile,
		}
	}

	for _, rank := range ranks {
		userInfo := m.ParseStrUidInfo(channelId, rank.StrUidInfo)
		if userInfo.GetUid() != 0 {
			resUid, account, nickName, userProfile, _ := getUserAccountInfoFromUidInfo(userMap[userInfo.GetUid()], userInfo)
			pkScoreChange.TopScoreUser = append(pkScoreChange.TopScoreUser, &pbLogic.SendGiftUserInfo{
				Uid:         resUid,
				Account:     account,
				Score:       rank.Score,
				Nickname:    nickName,
				Sex:         uint32(userMap[userInfo.GetUid()].GetSex()),
				UserProfile: userProfile,
			})
		}
	}

	if firstUidInfo.GetUid() > 0 {
		resUid, account, nickName, userProfile, _ := getUserAccountInfoFromUidInfo(userMap[firstUidInfo.GetUid()], firstUidInfo)
		pkScoreChange.FirstKillUser = &pbLogic.SendGiftUserInfo{
			Uid:         resUid,
			Account:     account,
			Nickname:    nickName,
			Sex:         uint32(userMap[firstUidInfo.GetUid()].GetSex()),
			FirstKill:   true,
			ChannelId:   firstShootCid,
			UserProfile: userProfile,
		}
	}

	log.Debugf("FillScoreChange end cid:%d pkScoreChange:%v pkInfo:%v", channelId, pkScoreChange, pkInfo)
	return true
}

// 处理神秘人现身逻辑
func (m *Manager) HandleYkwOffEvent(ctx context.Context, uid, cid uint32, account string) {
	log.InfoWithCtx(ctx, "HandleYkwOffEvent begin uid:%d cid:%d account:%s", uid, cid, account)

	strUidInfo := m.getStrUidInfo(&pb.UkwInfo{
		Uid:     uid,
		Account: account,
		Level:   1, // 填个默认值
	})

	// 删除神秘人缓存信息
	err := m.cacheClient.DelUkwInfo(cid, strUidInfo)
	if err != nil {
		log.ErrorWithCtx(ctx, "HandleYkwOffEvent DelUkwInfo failed uid:%d cid:%d account:%s err:%v", uid, cid, account, err)
		return
	}

	pkState := pk.PkState{
		ChannelID_A: cid,
	}

	pkInfo, err := pkState.GetPkInfo()
	if err != nil {
		log.ErrorWithCtx(ctx, "HandleYkwOffEvent GetPkInfo failed uid:%d cid:%d account:%s err:%v", uid, cid, account, err)
		return
	}

	if pkInfo.TChannelID == 0 {
		log.InfoWithCtx(ctx, "HandleYkwOffEvent no pk uid:%d cid:%d account:%s pkInfo:%v", uid, cid, account, pkInfo)
		return
	}

	// 先获取作为神秘人的送礼排行榜值
	score, err := pkState.GetUserPkRankScore(cid, pkInfo.BeginTime, strUidInfo)
	if err != nil {
		log.ErrorWithCtx(ctx, "HandleYkwOffEvent GetUserPkRankScore failed uid:%d cid:%d account:%s err:%v", uid, cid, account, err)
		return
	}

	// 删除
	err = pkState.DelUserPkRank(cid, pkInfo.BeginTime, strUidInfo)
	if err != nil {
		log.ErrorWithCtx(ctx, "HandleYkwOffEvent DelUserPkRank failed uid:%d cid:%d account:%s err:%v", uid, cid, account, err)
		return
	}

	// 加到真实身份的排行榜
	err = pkState.IncrPkRankScore(cid, pkInfo.BeginTime, m.getStrUidInfo(&pb.UkwInfo{Uid: uid}), score)
	if err != nil {
		log.ErrorWithCtx(ctx, "HandleYkwOffEvent IncrPkRankScore failed uid:%d cid:%d account:%s err:%v", uid, cid, account, err)
		return
	}

	// 神秘人现身重新推一次首杀，贡献榜，pk值信息
	pkScoreChange := &pbLogic.ChannelPKSingleScore{}
	ok := m.FillScoreChange(ctx, cid, pkScoreChange)
	if ok {
		msgBin, _ := proto.Marshal(pkScoreChange)
		m.PushChannelMsg(ctx, msgBin, uint32(cpb.ChannelMsgType_CHANNEL_LIVE_PK_SCORE_MSG), []uint32{pkInfo.TChannelID, cid})
	}

	log.InfoWithCtx(ctx, "HandleYkwOffEvent end uid:%d cid:%d account:%s score:%d pkinfo:%v", uid, cid, account, score, pkInfo)
}

// 处理消费事件
func (m *Manager) HandlerConsumeEvent(ctx context.Context, sendUid, targetUid, channelId, value uint32, orderID string,
	itemSource, tagType int32, fromUkwInfo *pb.UkwInfo) {
	if value == 0 {
		log.InfoWithCtx(ctx, "HandlerConsumeEvent value is 0 sendUid:%d targetUid:%d channelId:%d orderID:%s",
			sendUid, targetUid, channelId, orderID)
		return
	}

	//去重
	cnt := m.cacheClient.IncrKey(orderID, time.Hour*2)
	if cnt > 1 {
		log.Infof("HandlerConsumeEvent order is already proc orderId:%s", orderID)
		return
	}

	// 是否是主播
	chLiveInfo, err := m.cacheClient.GetChannelLiveInfo(channelId, pb.EnumIDType_CHANNEL_ID)
	if err != nil || chLiveInfo.Uid == 0 {
		log.Errorf("HandlerConsumeEvent channelId:%d orderId:%s err:%v ", channelId, orderID, err)
		return
	}

	if fromUkwInfo.GetLevel() > 0 {
		// 需要记录神秘人实时信息
		err = m.cacheClient.SetUkwInfo(channelId, m.getStrUidInfo(fromUkwInfo), fromUkwInfo)
		if err != nil {
			log.Errorf("HandlerConsumeEvent SetUkwInfo failed sendUid:%d targetUid:%d channelId:%d ukwInfo:%v err:%v", sendUid, targetUid, channelId, fromUkwInfo, err)
		}
	}

	//送礼事件直播处理
	m.HandlerLivePresentEvent(ctx, chLiveInfo, sendUid, targetUid, channelId, value, itemSource, tagType, fromUkwInfo)

	//送礼事件PK处理逻辑, 送给主播的才需要处理
	if chLiveInfo.Uid == targetUid {
		m.HandlerPkPresentEvent(ctx, sendUid, targetUid, channelId, value, itemSource, fromUkwInfo)
	}

	log.Infof("HandlerConsumeEvent channel:%d uid:%d targetUid:%d value:%d orderId:%s", channelId, sendUid, targetUid, value, orderID)
}

func (m *Manager) HandlerLivePresentEvent(ctx context.Context, chLiveInfo *pb.ChannelLiveInfo, sendUid, targetUid, channelId, value uint32,
	itemSource, tagType int32, fromUkwInfo *pb.UkwInfo) {
	//是否正在直播
	ChannelLiveStatus, err := m.cacheClient.GetChannelLiveStatus(chLiveInfo.Uid)
	if err != nil || ChannelLiveStatus.Uid == 0 {
		log.Errorf("HandlerLivePresentEvent GetChannelLiveStatus channelId:%d %d %d err:%v", channelId, sendUid, targetUid, err)
		return
	}

	// 房间流水
	totalValue, err := m.cacheClient.UpdateChannelLiveData(channelId, int64(value), cache.G_GiftValue)
	if err != nil {
		log.Errorf("HandlerLivePresentEvent UpdateChannelLiveData failed %d %d %d %d %d err:%v", sendUid, targetUid, channelId, value, itemSource, err)
	}

	var totalAnchorGiftValue uint64
	//送给主播的才处理
	if chLiveInfo.Uid == targetUid {
		//直播数据统计/
		// 送礼人数
		_, err = m.cacheClient.UpdateChannelLiveData(channelId, int64(sendUid), cache.G_SendGiftAudienceCnt)
		if err != nil {
			log.Errorf("HandlerLivePresentEvent UpdateChannelLiveData failed %d %d %d %d %d err:%v", sendUid, targetUid, channelId, value, itemSource, err)
		}

		// 每日送礼人数
		_, err = m.cacheClient.AddDayStatPF(chLiveInfo.Uid, sendUid, cache.Pay_type)
		if err != nil {
			log.Errorf("HandlerLivePresentEvent AddDayStatPF failed %d %d %d %d %d err:%v", sendUid, targetUid, channelId, value, itemSource, err)
		}

		switch tagType {
		case -1:
			//骑士流水
			anchorKnightValue, err := m.cacheClient.UpdateChannelLiveData(channelId, int64(value), cache.G_AnchorKnightValue)
			if err != nil {
				log.Errorf("HandlerLivePresentEvent UpdateChannelLiveData failed %d %d %d %d %d err:%v", sendUid, targetUid, channelId, value, itemSource, err)
			}

			anchorGiftValue, err := m.cacheClient.GetChannelLiveDataByType(channelId, cache.G_AnchorGiftValue)
			if err != nil {
				log.Errorf("HandlerLivePresentEvent GetChannelLiveDataByType failed %d %d %d %d %d err:%v", sendUid, targetUid, channelId, value, itemSource, err)
			}

			gameGiftValue, err := m.cacheClient.GetChannelLiveDataByType(channelId, cache.G_GameGiftValue)
			if err != nil {
				log.Errorf("HandlerLivePresentEvent GetChannelLiveDataByType failed %d %d %d %d %d err:%v", sendUid, targetUid, channelId, value, itemSource, err)
			}

			totalAnchorGiftValue = uint64(anchorKnightValue) + anchorGiftValue + gameGiftValue
		case int32(app.PresentTagType_PRESENT_TAG_CHANNEL_GAME):
			// 互动游戏流水
			gameGiftValue, err := m.cacheClient.UpdateChannelLiveData(channelId, int64(value), cache.G_GameGiftValue)
			if err != nil {
				log.Errorf("HandlerLivePresentEvent UpdateChannelLiveData failed %d %d %d %d %d err:%v", sendUid, targetUid, channelId, value, itemSource, err)
			}

			anchorGiftValue, err := m.cacheClient.GetChannelLiveDataByType(channelId, cache.G_AnchorGiftValue)
			if err != nil {
				log.Errorf("HandlerLivePresentEvent GetChannelLiveDataByType failed %d %d %d %d %d err:%v", sendUid, targetUid, channelId, value, itemSource, err)
			}

			anchorKnightValue, err := m.cacheClient.GetChannelLiveDataByType(channelId, cache.G_AnchorKnightValue)
			if err != nil {
				log.Errorf("HandlerLivePresentEvent GetChannelLiveDataByType failed %d %d %d %d %d err:%v", sendUid, targetUid, channelId, value, itemSource, err)
			}

			totalAnchorGiftValue = anchorKnightValue + anchorGiftValue + uint64(gameGiftValue)
		default:
			anchorGiftValue, err := m.cacheClient.UpdateChannelLiveData(channelId, int64(value), cache.G_AnchorGiftValue)
			if err != nil {
				log.Errorf("HandlerLivePresentEvent UpdateChannelLiveData failed %d %d %d %d %d err:%v", sendUid, targetUid, channelId, value, itemSource, err)
			}

			anchorKnightValue, err := m.cacheClient.GetChannelLiveDataByType(channelId, cache.G_AnchorKnightValue)
			if err != nil {
				log.Errorf("HandlerLivePresentEvent GetChannelLiveDataByType failed %d %d %d %d %d err:%v", sendUid, targetUid, channelId, value, itemSource, err)
			}

			gameGiftValue, err := m.cacheClient.GetChannelLiveDataByType(channelId, cache.G_GameGiftValue)
			if err != nil {
				log.Errorf("HandlerLivePresentEvent GetChannelLiveDataByType failed %d %d %d %d %d err:%v", sendUid, targetUid, channelId, value, itemSource, err)
			}

			totalAnchorGiftValue = anchorKnightValue + uint64(anchorGiftValue) + gameGiftValue
		}

		// 神秘人不加到送礼排行榜
		if fromUkwInfo.GetLevel() == 0 {
			// 增加送礼榜积分
			err = m.cacheClient.UpdateSendGiftRank(channelId, sendUid, value)
			if err != nil {
				log.Errorf("HandlerLivePresentEvent UpdateSendGiftRank failed  %d %d %d %d %d err:%v", sendUid, targetUid, channelId, value, itemSource, err)
			}
		}

		// 增加对应主播的粉团的今日礼物值
		err = m.cacheClient.UpdateGiftValue(targetUid, sendUid, value)
		if err != nil {
			log.Errorf("HandlerLivePresentEvent UpdateGiftValue err(%s) targetUid(%d) sendUid(%d) value(%d) err:%v", err.Error(), targetUid, sendUid, value, err)
		}
	}

	//直播值变化推送
	channelLiveDataPush := &pbLogic.ChannelLiveDataPushMsg{
		LiveData: &pbLogic.ChannelLiveData{
			GiftValue:       uint32(totalValue),
			AnchorGiftValue: uint32(totalAnchorGiftValue),
		},
	}
	pushBin, _ := proto.Marshal(channelLiveDataPush)
	m.PushChannelMsg(ctx, pushBin, uint32(cpb.ChannelMsgType_CHANNEL_LIVE_DATA_MSG), []uint32{channelId})

	log.DebugWithCtx(ctx, "HandlerLivePresentEvent cid:%d value:%d totalValue:%d totalAnchorGiftValue:%d",
		channelId, value, totalValue, totalAnchorGiftValue)
}

func (m *Manager) getStrUidInfo(ukwInfo *pb.UkwInfo) string {
	log.Debugf("getStrUidInfo begin ukwInfo:%v", ukwInfo)

	if ukwInfo.GetLevel() == 0 {
		return fmt.Sprintf("%d", ukwInfo.GetUid())
	} else {
		return fmt.Sprintf("%d-%s", ukwInfo.GetUid(), ukwInfo.GetAccount())
	}
}

func (m *Manager) ParseStrUidInfo(cid uint32, strUidInfo string) *pb.UkwInfo {
	log.Debugf("parseStrUidInfo begin cid:%d strUidInfo:%s", cid, strUidInfo)

	ukwInfo := &pb.UkwInfo{}
	resList := strings.Split(strUidInfo, "-")

	if strUidInfo == "" || len(resList) <= 0 {
		return ukwInfo
	}

	uid, err := strconv.Atoi(resList[0])
	if err != nil {
		log.Errorf("ParseStrUidInfo failed cid:%d info:%v res:%s err:%v", cid, strUidInfo, resList[0], err)
		return ukwInfo
	}

	if len(resList) > 1 {
		// 神秘人
		ukwInfo, err = m.cacheClient.GetUkwInfo(cid, strUidInfo)
		if err != nil {
			log.Errorf("ParseStrUidInfo GetUkwInfo failed cid:%d info:%v err:%v", cid, strUidInfo, err)
			return ukwInfo
		}

		if ukwInfo.GetUid() == 0 {
			// 兼容旧数据，也读取旧key， 一段时间后可以删除
			ukwInfo, err = m.cacheClient.GetUkwInfo(0, strUidInfo)
			if err != nil {
				log.Errorf("ParseStrUidInfo GetUkwInfo failed cid:%d info:%v err:%v", cid, strUidInfo, err)
				return ukwInfo
			}
		}
	}

	ukwInfo.Uid = uint32(uid)

	log.Debugf("ParseStrUidInfo end cid:%d strUidInfo:%s uwkInfo:%v", cid, strUidInfo, ukwInfo)
	return ukwInfo
}

func (m *Manager) HandlerPkPresentEvent(ctx context.Context, sendUid, targetUid, channelId, value uint32, itemSource int32, fromUkwInfo *pb.UkwInfo) {

	tbeanVal := value
	pkState := pk.PkState{ChannelID_A: channelId}

	//PK状态
	isScoreing := pkState.IsScoreing()
	if !isScoreing {
		return //不在计分阶段
	}

	pkInfo, _ := pkState.GetPkInfo()
	pkState.ChannelID_B = pkInfo.TChannelID
	if pkInfo.MUID != targetUid { //不是送给主播的礼物不统计
		return
	}

	bs := pk.PkState{ChannelID_A: pkInfo.TChannelID}
	bInfo, err := bs.GetPkInfo()
	if nil != err {
		log.Errorf("channel_live_HandlerPresentEvent GetPkInfo err:%v", err)
		return
	}

	//特殊时间段内屏蔽主播列表
	if upb.PresentSourceType(itemSource) == upb.PresentSourceType_PRESENT_SOURCE_PACKAGE && conf.IsPkActivityAnchor(targetUid) {
		log.Errorf("channel_live_HandlerPresentEvent IsPkActivityAnchor targetUid:%v channelId:%v", targetUid, channelId)
		return
	}
	var cBeforeScore, tBeforeScore = int64(bInfo.PKScore), int64(pkInfo.PKScore)

	//根据PK的不同阶段进行对应的处理
	var isScoreChange, isFirstKill bool

	stage, _ := pkState.GetPkStage()
	pks := pb.EnumChannelLivePKStatus(stage)
	switch pks {
	case pb.EnumChannelLivePKStatus_BEGIN:
		//首杀阶段暴击
		isFirstKill = pkState.SetFirstShootUid(m.getStrUidInfo(fromUkwInfo), int64(channelId))
		if isFirstKill {
			m.HandlerFirstKill(ctx, channelId, sendUid, value)
			value = uint32(float64(value) * 1.2)
		}
	case pb.EnumChannelLivePKStatus_TOOL:
		//1、道具阶段使用道具
		isScoreChange = m.UserItem(ctx, channelId, sendUid, &value, fromUkwInfo)
		m.GainItem(ctx, channelId, sendUid, value/100)
	}

	//增加PK排行榜
	var extractScore int32 = 0
	if pkInfo.ExtraNum > 0 {
		extractScore = int32(value)
	}

	log.Debugf("channel_live_HandlerPresentEvent pkInfo.ExtraNum:%v", pkInfo.ExtraNum)

	pkState.IncrPkRankScore(channelId, pkInfo.FBeginTime, m.getStrUidInfo(fromUkwInfo), value)

	pkInfo, _ = pkState.GetPkInfo()

	pkState.IncrScore(channelId, int32(value), 0)

	//加时逻辑
	isExtract, cerr := m.CheckAndExtraTime(ctx, cBeforeScore, tBeforeScore, pkInfo.TChannelID, bInfo.TChannelID, sendUid, tbeanVal, int32(value))
	if nil != cerr {
		log.Errorf("CheckAndExtraTime tcid:%v bcid:%v cerr:%v", pkInfo.TChannelID, bInfo.TChannelID, cerr)
	}

	if isExtract {
		extractScore = int32(value)
	}
	if extractScore > 0 {
		pkState.IncrScore(channelId, 0, extractScore)
	}

	//PK积分变化提送
	pkScoreChange := &pbLogic.ChannelPKSingleScore{}
	ok := m.FillScoreChange(ctx, channelId, pkScoreChange)
	if ok {
		msgBin, _ := proto.Marshal(pkScoreChange)
		go func() {
			subCtx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
			defer cancel()
			m.PushChannelMsg(subCtx, msgBin, uint32(cpb.ChannelMsgType_CHANNEL_LIVE_PK_SCORE_MSG), []uint32{pkInfo.TChannelID, channelId})
		}()
	}

	//对面房间也变了积分，因为本房间的鸡蛋道具扣减对面积分
	if isScoreChange {
		pkScoreChange := &pbLogic.ChannelPKSingleScore{}
		ok := m.FillScoreChange(ctx, pkInfo.TChannelID, pkScoreChange)
		if ok {
			msgBin, _ := proto.Marshal(pkScoreChange)
			go func() {
				subCtx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
				defer cancel()
				m.PushChannelMsg(subCtx, msgBin, uint32(cpb.ChannelMsgType_CHANNEL_LIVE_PK_SCORE_MSG), []uint32{pkInfo.TChannelID, channelId})
			}()
		}
	}
}

func (m *Manager) FillStartPkInfo(uida, uidb, channelIda, channelIdb, beginTs uint32) *pbLogic.ChannelLivePkStatusPushMsg {

	ctx := context.Background()

	micListb, micLista := m.FillMicList(channelIdb, channelIda)

	uidList := []uint32{uida, uidb}

	users, _ := m.accountCli.GetUsersMap(ctx, uidList)
	userb, ok := users[uidb]
	if !ok {
		return nil
	}
	usera, ok := users[uida]
	if !ok {
		return nil
	}

	bt := pk.PkState{
		ChannelID_A: channelIda,
		ChannelID_B: channelIdb,
	}
	bi, _ := bt.GetPkInfo()

	extConf := conf.GetExtraTimeConf()
	pkStatusPush := &pbLogic.ChannelLivePkStatusPushMsg{
		ChallengeUser: &pbLogic.PkUserInfo{
			Uid:       uidb,
			Account:   userb.Username,
			Nickname:  userb.Nickname,
			ChannelId: channelIdb,
			Alias:     userb.Alias,
			Status:    pbLogic.EnumChannelLiveStatus_OPEN,
			Sex:       userb.GetSex(),
		},
		ChallengeScore: &pbLogic.ChannelPKSingleScore{
			ChannelId:     channelIdb,
			ScoreValue:    0,
			MicList:       micListb,
			EffectItemCnt: 0,
		},
		BechallengeUser: &pbLogic.PkUserInfo{
			Uid:       uida,
			Account:   usera.Username,
			Nickname:  usera.Nickname,
			ChannelId: channelIda,
			Alias:     usera.Alias,
			Status:    pbLogic.EnumChannelLiveStatus_OPEN,
			Sex:       usera.GetSex(),
		},
		BechallengeScore: &pbLogic.ChannelPKSingleScore{
			ChannelId:     channelIda,
			ScoreValue:    0,
			MicList:       micLista,
			EffectItemCnt: 0,
		},
		CommonInfo: &pbLogic.PkCommonInfo{
			BeginTime:       beginTs,
			Status:          pbLogic.EnumChannelLivePKStatus_BEGIN,
			IsExtraTime:     false,
			PkExtraTimeRule: extConf.Rule,
			ExtraLeftTime:   uint32(extConf.LeftTs),
			IsOpenExtraTime: bi.IsOpenExtra,
		},
		TimePresentInfo: m.GetLiveIntimatePresentInfo(ctx, channelIda, []uint32{channelIdb}),
	}

	if user, ok := users[uida]; ok {
		pkStatusPush.BechallengeUser.Sex = user.Sex
		pkStatusPush.BechallengeUser.Alias = user.Alias
	}

	if user, ok := users[uidb]; ok {
		pkStatusPush.ChallengeUser.Sex = user.Sex
		pkStatusPush.ChallengeUser.Alias = user.Alias
	}

	log.Debugf("FillStartPkInfo %+v", pkStatusPush)

	return pkStatusPush
}

func (m *Manager) IsAble2Extra(pkInfoA, pkInfoB *pk.PKInfo, extConf *conf.ExtraPkTimeStu, pkScore int32) (bool, error) {
	ts := pk.PkState{
		ChannelID_A: pkInfoB.TChannelID,
		ChannelID_B: pkInfoA.TChannelID,
	}

	var totalScore = int64(pkInfoA.PKScore + pkInfoB.PKScore)
	nowTs := time.Now().Unix()
	leftTs := conf.GetPkTotalSec() - (nowTs - int64(pkInfoA.FBeginTime))
	if leftTs >= 1 { //是否在leftts 之前两个人的PK值满足加时要求 conf.GetExtraTimeConf().LeftTs
		if totalScore >= conf.GetExtraTimeConf().TotalScore {
			ts.SetExtra()
			extraMsg := &pbLogic.PkExtraTimePushMsg{
				IsExtra:         true,
				PkExtraTimeRule: extConf.Rule,
				ExtraLeftTime:   uint32(extConf.LeftTs),
			}
			subMsg, err := proto.Marshal(extraMsg)
			if nil != err {
				return false, err
			}
			pkExtraPush := &pbLogic.ChannelLivePkCommonPushMsg{
				MsgType: pbLogic.ChannelLivePkCommonPushMsg_ENUM_EXTRA_TIME,
				MsgBin:  subMsg,
			}
			pkStatusPushBin, err := proto.Marshal(pkExtraPush)
			if nil != err {
				return false, err
			}
			m.PushChannelMsg(context.Background(), pkStatusPushBin, uint32(150), []uint32{pkInfoB.TChannelID, pkInfoA.TChannelID})

			return true, fmt.Errorf("满足条件 %v", totalScore)
		}
	}
	return false, fmt.Errorf("条件尚未满足 %v %v %v %v", leftTs, conf.GetExtraTimeConf().LeftTs, totalScore, conf.GetExtraTimeConf().TotalScore)
}

// 是否触发加时
func (m *Manager) CheckAndExtraTime(ctx context.Context, cBeforeScore, tBeforeScore int64, cCid, tCid, sendUid, tbeamVal uint32, pkScore int32) (bool, error) {
	//isExtract := false

	cs := pk.PkState{
		ChannelID_A: cCid,
		ChannelID_B: tCid,
	}
	ci, _ := cs.GetPkInfo()

	if !ci.IsOpenExtra {
		return false, fmt.Errorf("加时玩法未开启 %v %v", cCid, tCid)
	}

	ts := pk.PkState{
		ChannelID_A: tCid,
		ChannelID_B: cCid,
	}
	ti, _ := ts.GetPkInfo()

	extraConf := conf.GetExtraTimeConf()

	//剩余10秒之前，两个人的积分之和是否满足条件
	if !ci.IsExtra {
		_, err := m.IsAble2Extra(ci, ti, extraConf, pkScore)
		if nil != err {
			return false, err
		}
		//isExtract = ise
	}

	if tbeamVal < extraConf.SingleVal {
		return false, errors.New("礼价值不满足")
	}

	cAfterScore, tAfterScore := ci.PKScore, ti.PKScore

	sumScore := cBeforeScore + tBeforeScore
	if sumScore < extraConf.TotalScore {
		return false, fmt.Errorf("PK总积分不满足 s:%v n:%v", sumScore, extraConf.TotalScore)
	}

	if cBeforeScore == tBeforeScore || (cBeforeScore > tBeforeScore) == (cAfterScore > tAfterScore) {
		return false, fmt.Errorf("没有发生超越 cb:%v tb:%v ca:%v ta:%v", cBeforeScore, tBeforeScore, cAfterScore, tAfterScore)
	}

	nowTs := time.Now().Unix()
	leftTs := conf.GetPkTotalSec() - (nowTs - int64(ti.BeginTime))
	if leftTs < conf.GetExtraTimeConf().FixTs || leftTs > conf.GetExtraTimeConf().LeftTs {
		return false, fmt.Errorf("不在触发时间范围 leftTs:%v need:%v", leftTs, conf.GetExtraTimeConf().LeftTs)
	}

	//更新Redis begintime，定时器那边会判断begintime是否相同决定要不要执行
	newBeginTs, err := ts.DelayPkFinishTime()
	if nil != err {
		return false, fmt.Errorf("DelayPkFinishTime fail %v", err)
	}

	log.Debugf("CheckAndExtraTime trigger11 acid:%v bcid:%v o:%v n:%v\n", cCid, tCid, ci.BeginTime, newBeginTs)

	//重新加入定时事件
	task := &cache.TickTask{
		AUid:       ci.MUID,
		BUid:       ci.TUID,
		AChannelId: cCid,
		BChannelId: tCid,
		Status:     uint32(pb.EnumChannelLivePKStatus_LAST),
		BeginTs:    uint32(newBeginTs),
	}

	pkStatusPush := &pbLogic.ChannelLivePkStatusPushMsg{}
	m.FillUserDate(ctx, pkStatusPush, task.BChannelId)
	m.FillRankData(ctx, pkStatusPush, &cs, task)

	if pkStatusPush.CommonInfo == nil {
		pkStatusPush.CommonInfo = &pbLogic.PkCommonInfo{}
	}
	pkStatusPush.CommonInfo.BeginTime = uint32(newBeginTs)
	pkStatusPush.CommonInfo.Status = pbLogic.EnumChannelLivePKStatus_BEGIN
	pkStatusPush.CommonInfo.IsExtraTime = true
	pkStatusPush.CommonInfo.ExtraLeftTime = uint32(extraConf.LeftTs)
	pkStatusPush.CommonInfo.PkExtraTimeRule = extraConf.Rule
	pkStatusPush.CommonInfo.IsOpenExtraTime = ci.IsOpenExtra

	userProfile, err := m.userProfileCli.GetUserProfileV2(ctx, sendUid, true)
	if err != nil {
		log.ErrorWithCtx(ctx, "CheckAndExtraTime GetUserProfileV2 failed sendUid:%d err:%v", sendUid, err)
		return false, err
	}

	pkStatusPush.SendGiftUser = &pbLogic.SendGiftUserInfo{
		Uid:         userProfile.GetUid(),
		Account:     userProfile.GetAccount(),
		Nickname:    userProfile.GetNickname(),
		Sex:         userProfile.GetSex(),
		UserProfile: userProfile,
	}

	//PK开始广播到两个房间
	pkStatusPushBin, _ := proto.Marshal(pkStatusPush)
	m.PushChannelMsg(ctx, pkStatusPushBin, uint32(cpb.ChannelMsgType_CHANNEL_LIVE_PK_STATUS_MGS), []uint32{cCid, tCid})

	log.Debugf("CheckAndExtraTime msg  %+v\n", pkStatusPush)

	log.Debugf("CheckAndExtraTime trigger acid:%v bcid:%v o:%v n:%v\n", cCid, tCid, ci.BeginTime, newBeginTs)

	triggerTime := uint32(newBeginTs + uint64(conf.GetPkTotalSec()))
	m.cacheClient.AddTaskToTick(task, triggerTime)

	ts.IncrExtraNum()

	return true, nil
}

// 获得道具
func (m *Manager) GainItem(ctx context.Context, channelId, sendUid, price uint32) {

	log.DebugWithCtx(ctx, "GainItem cid:%d senduid:%d", channelId, sendUid)

	itemID := conf.GetGConfig().GetHitItemID(price)
	if itemID == "" {
		return
	}

	itemConf := conf.GetGConfig().GetItemConf(itemID)
	if itemConf.ItemId == "" {
		return
	}

	pkstate := pk.PkState{
		ChannelID_A: channelId,
		ChannelID_B: 0,
	}
	pkInfo, _ := pkstate.GetPkInfo()

	m.cacheClient.GainItemToUser(channelId, pkInfo.FBeginTime, sendUid, itemID)

	log.DebugWithCtx(m.ctx, "GainItem GainItemToUser cid:%d senduid:%d itemid:%d", channelId, sendUid, itemID)

	userProfileInfo, serr := m.userProfileCli.GetUserProfileV2(ctx, sendUid, true)
	if serr != nil {
		log.ErrorWithCtx(ctx, "GainItem GetUserProfile failed cid:%d senduid:%d err:%v", channelId, sendUid, serr)
	}

	getGiftPushMsg := &pbLogic.GetGiftPushMsg{
		Uid:         userProfileInfo.GetUid(),
		Account:     userProfileInfo.GetAccount(),
		ItemId:      itemID,
		Nickname:    userProfileInfo.GetNickname(),
		ChannelId:   channelId,
		UserProfile: userProfileInfo,
	}

	pushBin, _ := proto.Marshal(getGiftPushMsg)

	perr := m.PushChannelMsg(ctx, pushBin, uint32(cpb.ChannelMsgType_CHANNEL_LIVE_PK_GET_TOOL_MSG), []uint32{channelId})

	if perr != nil {
		log.Errorf("GainItem HandlerPresentEvent PushToUsers channelId:%v err:%v", channelId, perr)
	}

	flag := 2
	if pkInfo.ApplyCID == channelId {
		flag = 1
	}
	m.DataCenterReport("************", channelId, map[string]interface{}{
		"uid":      sendUid,
		"propId":   itemID,
		"propNum":  1,
		"propName": itemConf.Name,
		"propType": itemConf.Type,
		"type":     1,
		"flag":     flag,
	})
}

func (m *Manager) DecScore(ctx context.Context, sendUid, cid, targetCid, newCnt uint32, userProfile *app.UserProfile) {
	pkState := pk.PkState{
		ChannelID_A: cid,
		ChannelID_B: 0,
	}
	decScore := pkState.ReductScore(newCnt)

	log.Infof("SendGiftEffectPushMsg DecScore sendUid:%d cid:%d newCnt:%d decScore:%d userProfile:%v", sendUid, cid, newCnt, decScore, userProfile)

	pkState.ReductScoreTotal(decScore)

	if decScore <= 0 {
		return
	}

	itemConf := conf.GetGConfig().GetItemConfByType(pb.ItemType_Deduct_Type)
	if itemConf == nil {
		log.Errorf("SendGiftEffectPushMsg not item conf")
		return
	}

	targetMsg := fmt.Sprintf(itemConf.SvrTargetMsg, decScore)
	msg := fmt.Sprintf(itemConf.SvrUseMsg, newCnt, decScore)

	dedPushMsg := &pbLogic.SendGiftEffectPushMsg{
		SendUser: &pbLogic.SendGiftUserInfo{
			Uid:         userProfile.GetUid(),
			Account:     userProfile.GetAccount(),
			Nickname:    userProfile.GetNickname(),
			ChannelId:   cid,
			UserProfile: userProfile,
		},
		ItemId:        itemConf.ItemId,
		Ty:            pbLogic.ItemType_Deduct_Type,
		TextMsg:       msg,
		TargetTextMsg: targetMsg,
		Url:           itemConf.TargetEffectUrl,
		Value:         int32(-decScore),
	}

	log.Infof("SendGiftEffectPushMsg senduid:%d channelid:%d target_channel_id:%d itemid:%d value:%d userProfile:%v ",
		sendUid, cid, targetCid, itemConf.ItemId, decScore, userProfile)

	msgBin, err := proto.Marshal(dedPushMsg)
	if err != nil {
		log.Errorf("SendGiftEffectPushMsg DecScore Marshal senduid:%v cid:%v err:%v", sendUid, cid, err)
		return
	}
	m.PushChannelMsg(ctx, msgBin, uint32(cpb.ChannelMsgType_CHANNEL_LIVE_PK_TOOL_EFFECT_MSG), []uint32{cid, targetCid})
}

func (m *Manager) GenChannelLiveID(ctx context.Context) (uint64, error) {
	channelLiveId, _ := m.cacheClient.GenNewChannelLiveId(1, false)
	if channelLiveId <= 1024 { //Redis被清了
		tmpID, err := m.mysqlStore.GetMaxChannelLiveID(ctx)
		log.Debugf("GenChannelLiveID max id:%v", tmpID)
		if err != nil {
			log.Errorf("GenChannelLiveID GetMaxChannelLiveID err:%v", err)
			return channelLiveId, err
		}
		channelLiveId, _ = m.cacheClient.GenNewChannelLiveId(int64(tmpID+1024), true)
	}
	return channelLiveId, nil
}

// 送礼自动使用道具
func (m *Manager) UserItem(ctx context.Context, channelId, sendUid uint32, value *uint32, fromUkwInfo *pb.UkwInfo) bool {

	var bChange bool

	pkState := pk.PkState{
		ChannelID_A: channelId,
		ChannelID_B: 0,
	}
	pkInfo, _ := pkState.GetPkInfo()
	pkState.ChannelID_B = pkInfo.TChannelID

	itemList, _ := m.cacheClient.GetUserItemList(channelId, pkInfo.FBeginTime, sendUid)

	var unUseItemPtr *pb.ToolItem = nil
	for _, item := range itemList {
		if item.BeUsed {
			continue
		}
		unUseItemPtr = item
	}
	if unUseItemPtr == nil {
		return false
	}
	//标志礼物为使用状态
	m.cacheClient.UseItem(channelId, pkInfo.FBeginTime, sendUid, unUseItemPtr.ItemId, unUseItemPtr.ItemNs) //TODO 原子操作

	userProfileInfo, serr := m.userProfileCli.GetUserProfileV2(ctx, sendUid, true)
	if serr != nil {
		log.ErrorWithCtx(ctx, "UserItem GetUserProfile failed cid:%d senduid:%d err:%v", channelId, sendUid, serr)
		return false
	}

	//使用道具推送
	itemConf := conf.GetGConfig().GetItemConf(unUseItemPtr.ItemId)
	if itemConf.ItemId == "" {
		return false
	}

	beforeValue := *value
	items.UserItem(unUseItemPtr.ItemId, channelId, sendUid, value)

	m.DataCenterReport("************", channelId, map[string]interface{}{
		"uid":      sendUid,
		"propId":   unUseItemPtr.ItemId,
		"propNum":  1,
		"propName": itemConf.Name,
		"propType": itemConf.Type,
		"type":     2,
	})

	if pb.ItemType(itemConf.Type) == pb.ItemType_Effect_Type {
		_, newCnt := pkState.IncrUseItem(channelId, pkInfo.FBeginTime, m.getStrUidInfo(fromUkwInfo))
		//氛围道具到一定数量，扣除对方的积分
		m.DecScore(ctx, sendUid, channelId, pkInfo.TChannelID, uint32(newCnt), userProfileInfo)
		bChange = true
	}

	useItemPushMsg := &pbLogic.SendGiftEffectPushMsg{
		SendUser: &pbLogic.SendGiftUserInfo{
			Uid:         userProfileInfo.GetUid(),
			Account:     userProfileInfo.GetAccount(),
			Nickname:    userProfileInfo.GetNickname(),
			ChannelId:   channelId,
			UserProfile: userProfileInfo,
		},
		TextMsg:       itemConf.UseMsg,
		TargetTextMsg: itemConf.TargetMsg,
		Url:           itemConf.EffectUrl,
		ItemId:        unUseItemPtr.ItemId,
		Ty:            pbLogic.ItemType(itemConf.Type),
		Value:         int32((*value) - beforeValue),
	}

	log.Debugf("UserItem useItemPushMsg:%v itemConf:%v channelId:%v", useItemPushMsg, itemConf, channelId)

	msgBin, err := proto.Marshal(useItemPushMsg)

	if err != nil {
		log.Errorf("UserItem Marshal err:%v useItemPushMsg:%v", err, useItemPushMsg)
		return bChange
	}

	cids := []uint32{channelId}
	if pb.ItemType(itemConf.Type) == pb.ItemType_Effect_Type {
		cids = append(cids, pkInfo.TChannelID)
	}
	perr := m.PushChannelMsg(ctx, msgBin, uint32(cpb.ChannelMsgType_CHANNEL_LIVE_PK_TOOL_EFFECT_MSG), cids)

	log.Debugf("UserItem PushChannelMsg itemid:%v cids:%v perr:%v", itemConf.ItemId, cids, perr)

	return bChange
}

func (m *Manager) HandlerFirstKill(ctx context.Context, cid, sendUid, value uint32) {
	userProfileInfo, serr := m.userProfileCli.GetUserProfileV2(ctx, sendUid, true)
	if serr != nil {
		log.ErrorWithCtx(ctx, "HandlerFirstKill GetUserProfile failed senduid:%d err:%v", sendUid, serr)
		return
	}

	pkstate := pk.PkState{ChannelID_A: cid}
	pkinfo, err := pkstate.GetPkInfo()
	if pkinfo.TChannelID == 0 {
		log.Errorf("HandlerFirstKill GetPkInfo sendUid:%v err:%v", sendUid, err)
		return
	}

	//首杀推送
	itemConf := conf.GetGConfig().GetItemConfByType(pb.ItemType_First_Kill_Type)
	addScore := uint32(float32(value) * (float32(itemConf.Value) / 100))

	textMsg := fmt.Sprintf(itemConf.SvrUseMsg, userProfileInfo.GetNickname(), addScore)
	targetMsg := fmt.Sprintf(itemConf.SvrTargetMsg, userProfileInfo.GetNickname(), addScore)

	pushMsg := &pbLogic.SendGiftEffectPushMsg{
		SendUser: &pbLogic.SendGiftUserInfo{
			Uid:         userProfileInfo.GetUid(),
			Account:     userProfileInfo.GetAccount(),
			Nickname:    userProfileInfo.GetNickname(),
			ChannelId:   cid,
			FirstKill:   true,
			UserProfile: userProfileInfo,
		},
		Value:         int32(addScore),
		TextMsg:       textMsg,
		TargetTextMsg: targetMsg,
		Url:           itemConf.EffectUrl,
		ItemId:        itemConf.ItemId,
		Ty:            pbLogic.ItemType_First_Kill_Type,
	}
	msgBin, err := proto.Marshal(pushMsg)
	if err != nil {
		log.Errorf("channel_live_HandlerPresentEvent HandlerFirstKill marshal err:%v pushMsg:%v", err, pushMsg)
		return
	}

	m.PushChannelMsg(ctx, msgBin, uint32(cpb.ChannelMsgType_CHANNEL_LIVE_PK_TOOL_EFFECT_MSG), []uint32{pkinfo.TChannelID, cid})

	log.Debugf("channel_live_HandlerPresentEvent HandlerFirstKill first shoot uid:%v pushMsg:%v", sendUid, pushMsg)

	flag := 2
	if cid == pkinfo.ApplyCID {
		flag = 1
	}
	m.DataCenterReport("************", cid, map[string]interface{}{
		"uid":        sendUid,
		"flag":       flag,
		"createTime": time.Now().Unix(),
		"anchorId":   pkinfo.MUID,
		"num":        value,
	})
}

// 进出房事件处理
func (m *Manager) HandlerChannelEvent(enterUid, channelId, eventType uint32, isUkw bool) {

	if !conf.GetGConfig().IsProd() {
		return
	}

	//uid, targetUid
	log.Debugf("HandlerChannelEvent enterUid:%d, channelId:%d, eventType:%d", enterUid, channelId, eventType)

	chLiveInfo, err := m.cacheClient.GetChannelLiveInfo(channelId, pb.EnumIDType_CHANNEL_ID)
	if err != nil {
		log.Errorf("HandlerChannelEvent GetChannelLiveInfo %d %d %d err:%v", enterUid, channelId, eventType, err)
		return
	}

	chLiveStatus, err := m.cacheClient.GetChannelLiveStatus(chLiveInfo.Uid)
	if err != nil {
		log.Errorf("HandlerChannelEvent GetChannelLiveStatus %d %d %d err:%v", enterUid, channelId, eventType, err)
		return
	}

	if chLiveStatus.ChannelId == 0 {
		log.Errorf("HandlerChannelEvent GetChannelLiveInfo %d %d %d ChannelId==0", enterUid, channelId, eventType)
		return
	}

	//进房观众统计相关
	if chLiveInfo.Uid != enterUid {
		//添加每场观众数
		m.cacheClient.UpdateChannelLiveData(channelId, int64(enterUid), cache.G_AudienceCnt)
		//添加每日观众数
		m.cacheClient.AddDayStatPF(chLiveInfo.Uid, enterUid, cache.Audience_type)

		//观看时长统计,神秘人不统计
		if !isUkw {
			m.cacheClient.SetUserWatchStatus(channelId, enterUid, eventType)
		}
	} else {
		//主播退房相关处理
		pkstate := pk.PkState{ChannelID_A: channelId}
		pkinfo, _ := pkstate.GetPkInfo()
		if enterUid == pkinfo.MUID && eventType == 0 {
			if pkinfo.TChannelID != 0 {
				pkstate.ChannelID_B = pkinfo.TChannelID
				m.HandlerMicEvent(&kafkasimplemic.SimpleMicEvent{
					ChId:      channelId,
					MicUserId: enterUid,
				})
				//pkstate.FinishPK()

				log.Debugf("HandlerChannelEvent finish pk channel_id:%v target_channel_id:%v", channelId, pkinfo.TChannelID)
			}
		}
	}
}

// PK期间处理麦位事件
func (m *Manager) HandlerMicEvent(micEvent *kafkasimplemic.SimpleMicEvent) {

	if !conf.GetGConfig().IsProd() {
		return
	}

	if cpb.ChannelType(micEvent.ChannelType) != cpb.ChannelType_RADIO_LIVE_CHANNEL_TYPE {
		return
	}

	log.Debugf("HandlerMicEvent micEvent:%v", micEvent)

	pkState := pk.PkState{ChannelID_A: micEvent.ChId}
	pkInfo, _ := pkState.GetPkInfo()
	if pkInfo.TChannelID > 0 {
		mapMic, _ := pkState.GetMicList()
		m.PushMicInfo(micEvent.ChId, pkInfo.TChannelID, mapMic)
	}
}

func (m *Manager) PushMicInfo(sChannelID, TChannelId uint32, mapMic map[uint32]*pk.MicInfo) {
	ctx, cancel := context.WithTimeout(context.Background(), time.Second*5)
	defer cancel()

	micResp, err := m.channelMicCli.GetMicrList(ctx, sChannelID, 1024)
	if err != nil {
		log.Errorf("PushMicInfo GetMicrList id:%d err:%v", sChannelID, err)
		return
	}

	pushMsg := &pbLogic.PkMicInfoPushMsg{
		ChannelId: sChannelID,
		MicList:   make([]*pbLogic.PkMicSpace, 0),
	}

	pkstate := pk.PkState{ChannelID_A: sChannelID}
	mics, perr := pkstate.GetMicList()
	if perr != nil {
		log.Errorf("PushMicInfo id:%d err:%v", sChannelID, perr)
	}

	uids := make([]uint32, 0)
	for _, item := range micResp.AllMicList {
		if item.MicUid == 0 {
			continue
		}
		uids = append(uids, item.MicUid)
	}

	if len(uids) == 0 {
		log.InfoWithCtx(ctx, "PushMicInfo micList is empty in:%d", sChannelID)
		return
	}

	mapUid2Profile, err := m.userProfileCli.BatchGetUserProfileV2(ctx, uids, true)
	if err != nil {
		log.ErrorWithCtx(ctx, "PushMicInfo BatchGetUserProfileV2 failed id:%d uidList:%v err:%v", sChannelID, uids, err)
		return
	}

	for _, item := range micResp.AllMicList {
		if item.MicUid == 0 {
			continue
		}

		profile, ok := mapUid2Profile[item.MicUid]
		if !ok {
			continue
		}

		clientId := ""
		micInfo, ok := mics[item.MicUid]
		if ok {
			clientId = micInfo.VoiceID
		}

		mic := pbLogic.PkMicSpace{
			MicId:           item.MicId,
			Uid:             profile.GetUid(),
			Account:         profile.GetAccount(),
			Nick:            profile.GetNickname(),
			ChannelClientId: clientId,
			Alias:           profile.GetAccountAlias(),
			Sex:             int32(profile.GetSex()),
			UserProfile:     profile,
		}
		if m, ok := mapMic[item.MicUid]; ok {
			mic.ChannelClientId = m.VoiceID
			mic.ChannelVideoClientId = m.VideoID
		}
		pushMsg.MicList = append(pushMsg.MicList, &mic)
	}

	pushMsgBin, _ := proto.Marshal(pushMsg)

	log.Infof("PushMicInfo pushMsg:%v", pushMsg)

	m.PushChannelMsg(ctx, pushMsgBin, uint32(cpb.ChannelMsgType_CHANNEL_LIVE_MIC_LIST_MSG), []uint32{TChannelId})
}

func (m *Manager) GetChannelLiveAvg(uid uint32) (float32, float32, float32, float32, float32, float32) {
	var liveScore7, liveDayCnt7, liveScore30, liveDayCnt30 float32 = 0, 0, 0, 0
	timeStr := time.Now().Format("2006-01-02")
	t, _ := time.Parse("2006-01-02", timeStr)
	endTs := uint32(t.Unix())
	beginTs := endTs - 3600*24*30
	currDayNo := cache.GetDayNo(uint32(time.Now().Unix()))
	day2score := make(map[uint32]uint32)

	results, _ := m.mysqlStore.GetChannelLiveRecordsByTime(m.ctx, []uint32{uid}, beginTs, endTs)
	if res, ok := results[uid]; ok {
		for _, rc := range res {
			currDay := cache.GetDayNo(rc.BeginTime)
			day2score[currDay] = day2score[currDay] + rc.Score
		}
	}
	for dayNo, value := range day2score {
		if currDayNo-dayNo <= 7 {
			liveDayCnt7 = liveDayCnt7 + 1
			liveScore7 = liveScore7 + float32(value)
		}
		if currDayNo-dayNo <= 30 {
			liveDayCnt30 = liveDayCnt30 + 1
			liveScore30 = liveScore30 + float32(value)
		}
	}

	var avgScore7 float32 = 0
	var avgScore30 float32 = 0
	if liveDayCnt7 > 0 {
		avgScore7 = liveScore7 / liveDayCnt7
	}
	if liveScore30 > 0 {
		avgScore30 = liveScore30 / liveDayCnt30
	}

	log.Debugf("GetPKMatchValue uid:%v beginTs:%v endTs:%v day2score:%v", uid, beginTs, endTs, day2score)

	return avgScore7, avgScore30, liveDayCnt7, liveScore7, liveDayCnt30, liveScore30
}

func (m *Manager) GetPKMatchValue(uid uint32, matchTy pb.ChannelLivePKMatchType) (uint32, string) {
	var matchScore uint32 = 0

	if matchTy == pb.ChannelLivePKMatchType_CPK_Match_Rank {
		mapUid2Score := m.GetPkRankScoreFromWeb([]uint32{uid})
		matchScore = mapUid2Score[uid]

		return matchScore, conf.GetLevelName(matchScore)
	}

	if matchTy == pb.ChannelLivePKMatchType_CPK_Match_Nornal {
		npk, w1, w2, w3 := conf.GetMatchAttr()
		tmpScore, err := m.cacheClient.GetMatchScore(uid, matchTy)
		pkscore := m.cacheClient.GetPKAvg(uid, npk)
		liveScore := float32(tmpScore)
		var liveScore7, liveDayCnt7, avg7 float32
		var liveScore30, liveDayCnt30, avg30 float32
		if err != nil {
			avg7, avg30, liveDayCnt7, liveScore7, liveDayCnt30, liveScore30 = m.GetChannelLiveAvg(uid)
			liveScore = avg7*w2 + avg30*w3
			m.cacheClient.SetMatchScore(uid, matchTy, uint32(liveScore))
		}
		matchScore = uint32(liveScore + float32(pkscore)*w1)
		log.Debugf("GetPKMatchValue uid:%v pkscore:%v liveScore7:%v liveDayCnt7:%v liveScore30:%v liveDayCnt30:%v liveScore:%v matchScore:%v",
			uid, pkscore, liveScore7, liveDayCnt7, liveScore30, liveDayCnt30, liveScore, matchScore)
	}

	return matchScore, ""
}

// 关闭排麦
func (m *Manager) SwitchMicQueue(channelID, UID uint32, flagBit uint32) error {
	ctx := context.Background()

	_, err := m.channelCli.ModifyChannel(ctx, UID, &chpb.ModifyChannelReq{
		ChannelId:        &channelID,
		OpUid:            &UID,
		SwitchFlagBitmap: &flagBit,
	})
	if nil != err {
		log.Errorf("SwitchMicQueue ModifyChannel channelID:%v err:%v", channelID, err)
	}

	simpleInfo, err := m.channelCli.GetChannelSimpleInfo(ctx, channelID, channelID)
	if nil != err {
		log.Errorf("SwitchMicQueue ModifyChannel channelID:%v err:%v", channelID, err)
		return err
	}

	opUser, err := m.accountCli.GetUser(ctx, UID)
	if nil != err {
		log.Errorf("SwitchMicQueue GetUser channelID:%v err:%v", channelID, err)
		return err
	}

	resp, err := m.channelMicCli.GetMicrList(ctx, channelID, UID)
	if nil != err {
		log.Errorf("SwitchMicQueue channelID:%v uid:%v err:%v", channelID, UID, err)
		return err
	}

	targetUidList := make([]uint32, 0)

	for _, mInfo := range resp.AllMicList {
		if mInfo.MicId == 1 || mInfo.MicUid == 0 { //micID == 1 主播位
			continue
		}
		targetUidList = append(targetUidList, mInfo.MicUid)
	}

	if len(targetUidList) > 0 {
		kresp, kerr := m.channelMicCli.KickoutChannelMic(ctx, UID, &mpb.KickoutChannelMicReq{
			OpUid:         UID,
			ChannelId:     channelID,
			TargetUidList: targetUidList,
			BanSecond:     0,
			SwitchFlag:    *simpleInfo.SwitchFlag,
		})

		if nil != kerr {
			log.Errorf("SwitchMicQueue KickoutChannelMic uid:%v channelID:%v err:%v ", UID, channelID, kerr)
			return kerr
		}

		opt := cpb.ChannelMicOpt{
			AllMicList: make([]*cpb.SimpleMicrSpace, 0),
		}

		for _, r := range kresp.AllMicList {
			opt.AllMicList = append(opt.AllMicList, &cpb.SimpleMicrSpace{
				MicId:    r.MicId,
				MicState: r.MicState,
				Uid:      r.MicUid,
			})
		}

		targetUidList = append(targetUidList, UID)
		aResp, err := m.accountCli.GetUsersMap(ctx, targetUidList)

		if nil != err {
			log.Errorf("SwitchMicQueue GetUsersMap channelID:%v err:%v", channelID, err)
			return err
		}

		for _, r := range kresp.KickoutMicList {
			u, ok := aResp[r.MicUid]
			if !ok {
				continue
			}

			opt.OpMicid = r.MicId
			opt.OpMicUid = r.MicUid
			opt.OpMicSex = u.Sex
			opt.OpTimeMs = kresp.ServerTimeMs
			opt.CurrMicmode = resp.MicrMode

			msg, err := proto.Marshal(&opt)

			if nil != err {
				log.Errorf("SwitchMicQueue Marshal channelID:%v err:%v", channelID, err)
				continue
			}

			context := []byte("被设置为旁听")
			perr := m.PushReliableChannelMsg(ctx, opUser, u, channelID, nil, msg, context, uint32(cpb.ChannelMsgType_CHANNEL_KICKED_MIC))
			if nil != perr {
				log.Errorf("SwitchMicQueue PushReliableChannelMsg channelID:%v err:%v", channelID, perr)
				return err
			}
		}
	}

	//回收排麦权限，推送关闭排麦消息
	mapData := make(map[string]interface{})
	mapData["sub_type"] = cpb.ChannelMsgSubType_CHANNEL_MSG_SUB_CHANGE_OPEN_QUEUE_UP_MIC_SWITCH
	mapData["is_open"] = false
	jsonStr, _ := json.Marshal(mapData)

	perr := m.PushReliableChannelMsg(ctx, opUser, nil, channelID, nil, nil, jsonStr,
		uint32(cpb.ChannelMsgType_CHANNEL_CONFIG_MODIFY_MSG))
	if nil != perr {
		log.Errorf("SwitchMicQueue PushReliableChannelMsg channelID:%v err:%v", channelID, perr)
		return err
	}

	//清掉原来的排名列表
	m.redisMicQue.Del(fmt.Sprintf("ZSET_QUEUE_MIC_APPLY_%v", channelID))

	return nil
}

func (m *Manager) SetAuthFlag(req *pb.SetAuthFlagReq) ([]uint32, error) {
	ctx := context.Background()
	ts := uint32(time.Now().Unix())

	failUidList := make([]uint32, 0)

	for _, uid := range req.UidList {
		clInfo := pb.ChannelLiveInfo{}
		err := m.mysqlStore.GetChannelLiveInfo(ctx, &clInfo, uid)
		if nil != err {

			failUidList = append(failUidList, uid)
			log.Errorf("SetAuthFlag GetChannelLiveInfo err:%v uid:%v", err, uid)
			continue
		}

		f := uint32(1) << uint32(req.AuthFlag)

		simpleInfo, err := m.channelCli.GetChannelSimpleInfo(ctx, clInfo.Uid, clInfo.ChannelId)
		if nil != err {
			failUidList = append(failUidList, uid)
			log.Errorf("SetAuthFlag SwitchMicQueue GetChannelSimpleInfo uid:%v channelID:%v err:%v", clInfo.Uid, clInfo.ChannelId, err)
			continue
		}

		flag := *simpleInfo.SwitchFlag
		if req.AuthFlag == pb.ChannelLiveAuthFlag_MQ_Auth {
			if req.Flag {
				flag = flag | OPEN_NORMAL_QUEUE_UP_MIC
			} else {
				flag = flag & (^OPEN_NORMAL_QUEUE_UP_MIC)
			}
		}

		if req.Flag {
			clInfo.Authority = clInfo.Authority | f
		} else {
			if clInfo.Authority&f > 0 {
				clInfo.Authority = clInfo.Authority ^ f
			}
			if req.AuthFlag == pb.ChannelLiveAuthFlag_MQ_Auth {
				m.SwitchMicQueue(clInfo.ChannelId, clInfo.Uid, flag)
			}
		}

		log.Infof("SetAuthFlag uid:%v flag:%v clInfo:%v\n", uid, flag, clInfo)

		pbInfo := &pb.ChannelLiveInfo{
			Uid:        uid,
			ChannelId:  clInfo.ChannelId,
			BeginTime:  clInfo.BeginTime,
			EndTime:    clInfo.EndTime,
			CreateTime: clInfo.CreateTime,
			OperName:   clInfo.OperName,
			TagId:      clInfo.TagId,
			UpdateTime: ts,
			Authority:  clInfo.Authority,
		}

		err = m.mysqlStore.SetChannelLiveInfo(ctx, pbInfo)
		if nil != err {
			failUidList = append(failUidList, uid)
		}
		err = m.cacheClient.SetChannelLiveInfo(clInfo.Uid, pbInfo)
		if nil != err {
			failUidList = append(failUidList, uid)
		}
		log.Debugf("SetAuthFlag finish uid:%v", uid)
	}
	return failUidList, nil
}

func (m *Manager) PushUserChannelMsg(ctx context.Context, msg []byte, uidList []uint32) error {

	if len(uidList) == 0 {
		log.Errorf("PushUserChannelMsg uidList empty ")
		return nil
	}

	log.Debugf("PushUserChannelMsg begin %d", uidList[0])

	pushMessage := &gaPush.PushMessage{
		Cmd:     uint32(gaPush.PushMessage_CHANNEL_LIVE_APPOINT_PK_PUSH_EVENT),
		Content: msg,
	}
	pushMessageBytes, _ := proto.Marshal(pushMessage)

	notification := &pushPb.CompositiveNotification{
		Sequence:           uint32(time.Now().Unix()),
		TerminalTypeList:   []uint32{protocol.MobileAndroidTT, protocol.MobileIPhoneTT},
		TerminalTypePolicy: PushNotification.DefaultPolicy,
		AppId:              0,
		ProxyNotification: &pushPb.ProxyNotification{
			Type:    uint32(pushPb.ProxyNotification_PUSH),
			Payload: pushMessageBytes,
		},
	}
	log.Infof("PushUserChannelMsg end %d", uidList[0])

	return m.pushClient.PushToUsers(ctx, uidList, notification)
}

func (m *Manager) CheckIsPkCntLimit(uid uint32, challenger bool) protocol.ServerError {
	pkCntLimit := conf.GetPkCntLimitItem(true)
	if nil == pkCntLimit {
		return nil
	}

	cntKey := fmt.Sprintf("day_timerange_pk_cnt_%v_%v", uid, pkCntLimit.BeginTime)
	cnt, _ := m.cacheClient.RedisClient.Get(cntKey).Int()
	if cnt >= pkCntLimit.Cnt {
		beginDuration := time.Duration(pkCntLimit.BeginTime) * time.Second
		endDuration := time.Duration(pkCntLimit.EndTime) * time.Second

		errMsg := fmt.Sprintf("每日%02v:%02v～%02v:%02v期间只允许连麦PK%v次哦～", int(beginDuration.Minutes())/60, int(beginDuration.Minutes())%60,
			int(endDuration.Minutes())/60, int(endDuration.Minutes())%60, pkCntLimit.Cnt)

		if !challenger {
			errMsg = fmt.Sprintf("该达人已到达%02v:%02v～%02v:%02v期间的连麦限额了哦，不支持再连麦PK了", int(beginDuration.Minutes())/60, int(beginDuration.Minutes())%60,
				int(endDuration.Minutes())/60, int(endDuration.Minutes())%60)
		}

		return protocol.NewExactServerError(nil, status.ErrChannelLivePkCntLimit, errMsg)
	}

	return nil
}

func (m *Manager) GetPkLimitInfo() *pb.PkLimitInfo {
	pkCntLimit := conf.GetPkCntLimitItem(false)

	pkLimitInfo := &pb.PkLimitInfo{
		TimeRange: "",
		LimitCnt:  "",
	}

	if nil == pkCntLimit {
		return pkLimitInfo
	}

	beginDuration := time.Duration(pkCntLimit.BeginTime) * time.Second
	endDuration := time.Duration(pkCntLimit.EndTime) * time.Second

	pkLimitInfo.TimeRange = fmt.Sprintf("%02v:%02v～%02v:%02v", int(beginDuration.Minutes())/60, int(beginDuration.Minutes())%60,
		int(endDuration.Minutes())/60, int(endDuration.Minutes())%60)

	pkLimitInfo.LimitCnt = fmt.Sprintf("%v次", pkCntLimit.Cnt)

	return pkLimitInfo
}

func (m *Manager) AddPkCnt(uids []uint32) error {
	pkCntLimit := conf.GetPkCntLimitItem(true)
	if nil == pkCntLimit {
		return nil
	}

	pipe := m.cacheClient.RedisClient.Pipeline()
	for _, uid := range uids {
		cntKey := fmt.Sprintf("day_timerange_pk_cnt_%v_%v", uid, pkCntLimit.BeginTime)
		pipe.IncrBy(cntKey, 1).Err()
		pipe.Expire(cntKey, time.Second*time.Duration(pkCntLimit.EndTime-pkCntLimit.BeginTime))
	}
	_, err := pipe.Exec()

	return err
}

func (m *Manager) GetLiveIntimatePresentInfo(ctx context.Context, channelId uint32, pkChannelList []uint32) *time_present2.LiveIntimatePresentInfo {
	logicResp := &time_present2.LiveIntimatePresentInfo{}

	resp, err := m.timePresentClient.GetChannelLiveIntimatePresentList(ctx, &time_present.GetChannelLiveIntimatePresentListReq{
		ChannelId:       channelId,
		PkChannelIdList: pkChannelList,
	})

	if err != nil {
		log.ErrorWithCtx(ctx, "GetChannelLiveIntimatePresentList failed channelId:%v err:%v", channelId, err)
		return logicResp
	}

	log.DebugWithCtx(ctx, "GetChannelLiveIntimatePresentList resp:%v", resp)

	logicResp.LastUpdateTime = resp.GetLiveIntimatePresentInfo().GetLastUpdateTime()
	logicResp.TriggerChannelId = resp.GetLiveIntimatePresentInfo().GetTriggerChannelId()

	for _, item := range resp.GetLiveIntimatePresentInfo().GetChannelInfo() {
		fromUserProfile := &app.UserProfile{}
		err := proto.Unmarshal(item.FromUser, fromUserProfile)
		if err != nil {
			log.ErrorWithCtx(ctx, "SendLiveIntimatePresentListUpdatePush err , content %+v , err %+v", item, err)
		}

		toUserProfile := &app.UserProfile{}
		err = proto.Unmarshal(item.ToUser, toUserProfile)
		if err != nil {
			log.ErrorWithCtx(ctx, "SendLiveIntimatePresentListUpdatePush err , content %+v , err %+v", item, err)
		}

		logicItem := &time_present2.LiveIntimatePresentChannelInfo{
			ChannelId:      item.GetChannelId(),
			PresentId:      item.GetPresentId(),
			FromUser:       fromUserProfile,
			ToUser:         toUserProfile,
			LastChangeTime: item.GetLastChangeTime(),
		}

		logicResp.ChannelInfo = append(logicResp.ChannelInfo, logicItem)
	}

	return logicResp
}
