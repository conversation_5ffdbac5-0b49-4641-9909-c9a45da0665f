package pk

import (
	"fmt"
	"github.com/go-redis/redis"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	pb "golang.52tt.com/protocol/app/channel-live-logic"
	"golang.52tt.com/protocol/common/status"
	"golang.52tt.com/services/channel-live-mgr/cache"
	"golang.52tt.com/services/channel-live-mgr/conf"
	"strconv"
	"time"
)

const (
	MUID        = "my_uid"
	TUID        = "target_uid"        //PK对手UID
	TCID        = "target_channel_id" //PK对手channelID
	PKBEGIN     = "pk_begin_time"
	FPKBEGIN    = "f_pk_begin_time" //可能会因为防偷塔机制改变
	PKSCORE     = "pk_score"        //PK获得总分
	EXTRASCORE  = "extra_score"     //加时期间获得的PK分
	APPLY_CID   = "apply"           //发起PK人的房间ID
	APPLYID     = "apply_id"        //发起PK申请的唯一ID
	MIC_FLAG    = "mic_flag"
	IsExtra     = "is_extra"      //是否可以触发加时
	IsOpenExtra = "is_open_extra" //玩法是否开启
	ExtraNum    = "extra_num"     //加时次数
	MatchSource = "match_source"  //PK的匹配方式

	EFFECT_ITEM_CNT = "effect_item_cnt" //氛围道具数量

	REDUCT_SCORE = "REDUCT_SCORE" //因为氛围道具减了多少积分
	REDUCT_CNT   = "REDUCT_CNT"   //氛围道具触发次数

	FirstShootUid = "FirstShoot" //首杀UID
	FirstShootCid = "FirstShootCid"
	FirstShootCnt = "FirstShootCnt"
)

type PKInfo struct {
	MUID       uint32
	TUID       uint32
	TChannelID uint32

	PKScore    uint32
	ExtScore   uint32 //加时阶段增加的PK值
	BeginTime  uint32 //跟跟随加时改变
	FBeginTime uint32 //固定不变
	EffectCnt  uint32

	RedScore uint32
	RedCnt   uint32

	MicFlag  uint32
	ApplyID  int64
	ApplyCID uint32

	IsExtra     bool  //是否满足加时条件,10秒前PK值满足条件
	IsOpenExtra bool  //开启本场PK的时候，PK加时玩法是否配置开启
	ExtraNum    int32 //加时的次数
	MatchSource int32 //PK匹配模式
}

type PkState struct {
	ChannelID_A uint32
	UID_A       uint32

	ChannelID_B uint32
	UID_B       uint32
	MatchSource int32 //发起PK的方式
}

type PkScore struct {
	PkRankCnt int64
	RedScore  int64
	RedCnt    int64
	ExtScore  int64
}

type MicInfo struct {
	Uid       uint32
	ChannelID uint32
	VoiceID   string // 语音流ID
	VideoID   string // 视频流ID
}

//排行榜数据
func pkRankKey(channelId, beginTs uint32) string {
	return fmt.Sprintf("pk_rank_%v_%v", channelId, beginTs)
}

//两个房间公用数据
func pkpublickey(channelIdA, channelIdB uint32) string {
	if channelIdA < channelIdB {
		return fmt.Sprintf("pk_public_key_%v_%v", channelIdA, channelIdB)
	}
	return fmt.Sprintf("pk_public_key_%v_%v", channelIdB, channelIdA)
}

//个人数据
func pkey(channelID uint32) string {
	return fmt.Sprintf("channel_pk_state_%v", channelID)
}

//麦位信息排行榜
func pmickey(channelID uint32) string {
	return fmt.Sprintf("channel_pk_mic_%v", channelID)
}

// 房间麦位的视频流信息
func pmicVedioKey(channelID uint32) string {
	return fmt.Sprintf("channel_pk_mic_vedio_%v", channelID)
}

//氛围道具排行榜
func pkuseritem(channelID, beginTs uint32) string {
	return fmt.Sprintf("pk_use_item_cnt_%v_%v", channelID, beginTs)
}

func matchDayCntKey(channelIDA, channelIDB uint32) string {
	nowTs := time.Now()
	matchDayLimitCntKey := fmt.Sprintf("match_day_limit_%v_%v_%v", nowTs.Format("2006-01-02"), channelIDA, channelIDB)
	if channelIDA > channelIDB {
		matchDayLimitCntKey = fmt.Sprintf("match_day_limit_%v_%v_%v", nowTs.Format("2006-01-02"), channelIDB, channelIDA)
	}
	return matchDayLimitCntKey
}

func matchListKey(cid, matchType uint32) string {
	return fmt.Sprintf("match_list_%d_%d", matchType, cid)
}

func GetMatchDayCnt(channelIDA, channelIDB uint32) int {
	rd := cache.GetGlobalRedis()

	matchDayLimitCntKey := matchDayCntKey(channelIDA, channelIDB)

	cnt, _ := rd.Get(matchDayLimitCntKey).Int()
	return cnt
}

func IncrMatchDayCnt(channelIDA, channelIDB uint32) {
	rd := cache.GetGlobalRedis()

	matchDayLimitCntKey := matchDayCntKey(channelIDA, channelIDB)

	rd.Incr(matchDayLimitCntKey)
	rd.Expire(matchDayLimitCntKey, time.Second*3600*24)
}

// 房间pk匹配成功列表
func AddChMatchList(cidA, cidB, matchType, ts uint32) error {
	rd := cache.GetGlobalRedis()

	keyA := matchListKey(cidA, matchType)
	err := rd.ZAdd(keyA, redis.Z{
		Score:  float64(ts),
		Member: cidB,
	}).Err()

	rd.Expire(keyA, 365*24*3600*time.Second)

	return err
}

// 获取房间匹配成功时间
func GetChMatchTs(cidA, cidB, matchType uint32) (uint32, error) {
	pipe := cache.GetGlobalRedis()

	keyA := matchListKey(cidA, matchType)
	fScoreA, err := pipe.ZScore(keyA, fmt.Sprintf("%d", cidB)).Result()
	if err != nil && err != redis.Nil {
		return 0, err
	}
	uScoreA := uint32(fScoreA)

	return uScoreA, nil
}

// 获取某个时间之后pk场次
func GetChMatchCntByTs(cid, matchType, ts uint32) (uint32, error) {
	rd := cache.GetGlobalRedis()

	key := matchListKey(cid, matchType)
	cnt, err := rd.ZCount(key, fmt.Sprintf("%d", ts), "+inf").Result()
	if err != nil && err != redis.Nil {
		return 0, err
	}

	return uint32(cnt), err
}

func RemoveMatchListByTs(cid, matchType, ts uint32) error {
	rd := cache.GetGlobalRedis()

	key := matchListKey(cid, matchType)
	err := rd.ZRemRangeByScore(key, "0", fmt.Sprintf("%d", ts)).Err()
	return err
}

func (p *PkState) StartPK() (int64, error) {
	rd := cache.GetGlobalRedis()

	extraConf := conf.GetExtraTimeConf()
	isOpenExtra := 0
	if extraConf.Switch {
		isOpenExtra = 1
	}

	nowTs := time.Now().Unix()
	startFunc := func() error {
		txf := func(tx *redis.Tx) error {
			//检查是否在PK中
			channelID_B, err := tx.HGet(pkey(p.ChannelID_A), TCID).Uint64()
			if err == nil && channelID_B > 0 {
				return protocol.NewExactServerError(nil, status.ErrChannelLivePkIng, "正在PK")
			}
			ChannelID_A, err := tx.HGet(pkey(p.ChannelID_B), TCID).Uint64()
			if err == nil && ChannelID_A > 0 {
				return protocol.NewExactServerError(nil, status.ErrChannelLivePkIng, "正在PK")
			}
			//设置PK状态
			_, err = tx.TxPipelined(func(pipe redis.Pipeliner) error {
				pkData := make(map[string]interface{})
				//公共
				pkData[APPLY_CID] = p.ChannelID_B
				pkData[APPLYID] = p.UID_B
				pkData[IsOpenExtra] = isOpenExtra
				pkData[PKBEGIN] = nowTs
				pkData[FPKBEGIN] = nowTs
				pkData[IsExtra] = 0
				pkData[ExtraNum] = 0
				pkData[MIC_FLAG] = 0
				pkData[PKSCORE] = 0
				pkData[EXTRASCORE] = 0
				pkData[MatchSource] = p.MatchSource

				//uida 数据
				pkData[TCID] = p.ChannelID_B
				pkData[TUID] = p.UID_B
				pkData[MUID] = p.UID_A
				pipe.HMSet(pkey(p.ChannelID_A), pkData)

				//UIDb 数据
				pkData[MUID] = p.UID_B
				pkData[TCID] = p.ChannelID_A
				pkData[TUID] = p.UID_A
				pipe.HMSet(pkey(p.ChannelID_B), pkData)

				pipe.Del(pkpublickey(p.ChannelID_A, p.ChannelID_B))
				pipe.Set(cache.PkOpponentsChannelID(p.ChannelID_A), p.ChannelID_B, 0)
				pipe.Set(cache.PkOpponentsChannelID(p.ChannelID_B), p.ChannelID_A, 0)

				//过期兜底
				pipe.Expire(pkey(p.ChannelID_A), time.Hour)
				pipe.Expire(pkey(p.ChannelID_B), time.Hour)

				return nil
			})
			if err != nil {
				log.Errorf("StartPK TxPipelined err:%v", err)
			}

			return nil
		}

		var err error = nil
		for i := 0; i < 16; i++ {
			err = rd.Watch(txf, pkey(p.ChannelID_A), pkey(p.ChannelID_B))
			if err == nil {
				return nil
			}
			log.Errorf("StartPK Watch txf err:%v try:%v", err, i)
		}
		return err
	}

	err := startFunc()
	if err != nil {
		log.Errorf("StartPK startfn err:%v", err)
		return nowTs, err
	}
	return nowTs, nil
}

func (p *PkState) GetFirstShootUid() (string, uint32) {
	key := pkpublickey(p.ChannelID_A, p.ChannelID_B)
	rd := cache.GetGlobalRedis()

	strFirstUidInfo, err := rd.HGet(key, FirstShootUid).Result()
	if err != nil && err != redis.Nil {
		log.Errorf("GetFirstShootUid failed %d %d err:%v", p.ChannelID_A, p.ChannelID_B, err)
	}

	cid, _ := rd.HGet(key, FirstShootCid).Int64()
	return strFirstUidInfo, uint32(cid)
}

func (p *PkState) SetFirstShootUid(strUidInfo string, channelId int64) bool {
	pkey := pkpublickey(p.ChannelID_A, p.ChannelID_B)
	rd := cache.GetGlobalRedis()
	cnt := rd.HIncrBy(pkey, FirstShootCnt, 1).Val()
	if cnt == 1 {
		rd.HSet(pkey, FirstShootUid, strUidInfo).Result()
		rd.HSet(pkey, FirstShootCid, channelId).Result()
		return true
	}
	return false
}

func (p *PkState) FinishPK() error {
	rd := cache.GetGlobalRedis()
	pipe := rd.Pipeline()
	defer pipe.Close()

	//清理数据
	pipe.Del(pkey(p.ChannelID_A))
	pipe.Del(pkey(p.ChannelID_B))

	pipe.Del(pkpublickey(p.ChannelID_A, p.ChannelID_B))
	pipe.Del(cache.PkOpponentsChannelID(p.ChannelID_A))
	pipe.Del(cache.PkOpponentsChannelID(p.ChannelID_B))

	pipe.Exec()

	return nil
}

func (p *PkState) IncrPkRankScore(cid, beginTs uint32, sendUidInfo string, score uint32) error {
	rd := cache.GetGlobalRedis()

	log.Debugf("IncrPkRankScore cid:%d send:%v score:%d", cid, sendUidInfo, score)

	key := pkRankKey(cid, beginTs) //fmt.Sprintf("pk_rank_%v", cid)

	_, err := rd.ZIncr(key, redis.Z{
		Member: sendUidInfo,
		Score:  float64(score),
	}).Result()
	if err != nil {
		log.Errorf("IncrPkRankScore rd.ZIncr failed %d %d %s %d", cid, beginTs, sendUidInfo, score)
		return err
	}

	err = rd.Expire(key, (30*24*3600)*time.Second).Err()
	if err != nil {
		log.Errorf("IncrPkRankScore rd.Expire failed %d %d %s %d", cid, beginTs, sendUidInfo, score)
	}

	return nil
}

func (p *PkState) GetPkRank(beginTs, off, cnt int64) ([]*cache.RankUser, error) {
	ranks := make([]*cache.RankUser, 0)

	rd := cache.GetGlobalRedis()
	key := pkRankKey(p.ChannelID_A, uint32(beginTs)) //fmt.Sprintf("pk_rank_%v", p.ChannelID_A)
	results, err := rd.ZRevRangeWithScores(key, off, cnt).Result()
	if err != nil {
		log.Errorf("GetPkRank ZRangeWithScores fail err:%v", err)
	}

	log.Debugf("GetPkRank result:%v", results)

	for _, user := range results {
		ranks = append(ranks, &cache.RankUser{
			Score:      uint32(user.Score),
			StrUidInfo: user.Member.(string),
		})
	}
	return ranks, nil
}

func (p *PkState) GetPkRankLen(beginTs uint32) int64 {
	key := pkRankKey(p.ChannelID_A, beginTs)
	rd := cache.GetGlobalRedis()
	cnt, _ := rd.ZCount(key, "0", "900000000").Result()
	return cnt
}

func (p *PkState) GetUserPkRankScore(cid, beginTs uint32, sendUidInfo string) (uint32, error) {
	key := pkRankKey(cid, beginTs)
	rd := cache.GetGlobalRedis()

	score, err := rd.ZScore(key, sendUidInfo).Result()
	return uint32(score), err
}

func (p *PkState) DelUserPkRank(cid, beginTs uint32, sendUidInfo string) error {
	key := pkRankKey(cid, beginTs)
	rd := cache.GetGlobalRedis()
	return rd.ZRem(key, sendUidInfo).Err()
}

//统计使用氛围道具次数
func (p *PkState) IncrUseItem(cid, beginTs uint32, strUidInfo string) (float64, int64) {
	rd := cache.GetGlobalRedis()
	key := pkuseritem(cid, beginTs)
	newVal := rd.ZIncr(key, redis.Z{
		Score:  1,
		Member: strUidInfo,
	}).Val()

	newEffectCnt, _ := rd.HIncrBy(pkey(cid), EFFECT_ITEM_CNT, 1).Result()

	err := rd.Expire(key, (30*24*3600)*time.Second).Err()
	if err != nil {
		log.Errorf("IncrUseItem rd.Expire failed %d %d %s err:%v", cid, beginTs, strUidInfo, err)
	}

	return newVal, newEffectCnt
}

func (p *PkState) GetTopUseItem(bt uint32) string {
	rd := cache.GetGlobalRedis()
	key := pkuseritem(p.ChannelID_A, bt)
	results, err := rd.ZRevRangeWithScores(key, 0, 1).Result()
	if err == nil && len(results) >= 1 {
		return results[0].Member.(string)
	}
	return ""
}

func (p *PkState) GetRedScoreTotal() (int64, int64) {
	rd := cache.GetGlobalRedis()
	score, _ := rd.HGet(pkey(p.ChannelID_A), REDUCT_SCORE).Int64()
	cnt, _ := rd.HGet(pkey(p.ChannelID_A), REDUCT_CNT).Int64()
	return score, cnt
}

//氛围道具效果统计
func (p *PkState) ReductScoreTotal(decScore uint32) {
	rd := cache.GetGlobalRedis()
	rd.HIncrBy(pkey(p.ChannelID_A), REDUCT_SCORE, int64(decScore)).Result()
	rd.HIncrBy(pkey(p.ChannelID_A), REDUCT_CNT, 1).Result()
}

//氛围道具到一定数量时，扣除对手的积分
func (p *PkState) ReductScore(cnt uint32) uint32 {

	decScore := conf.GetGConfig().GetDecScore(cnt)

	if decScore == 0 {
		return 0
	}

	pkInfo, err := p.GetPkInfo()
	if err != nil {
		log.Errorf("SendGiftEffectPushMsg ReductScore GetPkInfo1 err:%v ChannelID:%v", err, p.ChannelID_A)
		return 0
	}

	targetPkState := PkState{
		ChannelID_A: pkInfo.TChannelID,
		ChannelID_B: 0,
	}

	targetPKInfo, err := targetPkState.GetPkInfo()
	if err != nil {
		log.Errorf("SendGiftEffectPushMsg ReductScore GetPkInfo2 err:%v ChannelID:%v", err, targetPkState.ChannelID_A)
		return 0
	}

	//watch redis
	if decScore > targetPKInfo.PKScore {
		decScore = targetPKInfo.PKScore
	}
	p.IncrScore(pkInfo.TChannelID, int32(-decScore), 0)

	log.Debugf("SendGiftEffectPushMsg DeductScore_IncrScore %v %v %v", p.ChannelID_A, targetPkState.ChannelID_A, decScore)

	return uint32(decScore)
}

func (p *PkState) IncrScore(cid uint32, score, extraScore int32) (int64, error) {
	rd := cache.GetGlobalRedis()
	newScore, _ := rd.HIncrBy(pkey(cid), PKSCORE, int64(score)).Result()
	if extraScore > 0 {
		newExtScore, err := rd.HIncrBy(pkey(cid), EXTRASCORE, int64(extraScore)).Result()
		log.Debugf("IncrScore %v %v", newExtScore, err)
	}
	return newScore, nil
}

func (p *PkState) OnVoiceIDChange(m *MicInfo) (map[uint32]*MicInfo, error) {
	rd := cache.GetGlobalRedis()

	key := fmt.Sprintf("%v", m.Uid)
	mkey := pmickey(m.ChannelID)
	if m.VoiceID != "" {
		rd.HSet(mkey, key, m.VoiceID)
	} else {
		rd.HDel(mkey, key)
	}
	vedioKey := pmicVedioKey(m.ChannelID)
	if m.VideoID != "" {
		rd.HSet(vedioKey, key, m.VideoID)
	} else {
		rd.HDel(vedioKey, key)
	}

	mapMic := make(map[uint32]*MicInfo, 0)
	arr, err := rd.HGetAll(mkey).Result()
	if err == nil {
		for key, vid := range arr {
			uid, err := strconv.ParseUint(key, 10, 32)
			if err != nil {
				continue
			}
			mapMic[uint32(uid)] = &MicInfo{
				Uid:       uint32(uid),
				ChannelID: m.ChannelID,
				VoiceID:   vid,
			}
		}
	}

	vedios, err := rd.HGetAll(vedioKey).Result()
	if err == nil {
		for key, vid := range vedios {
			uid, err := strconv.ParseUint(key, 10, 32)
			if err != nil {
				continue
			}

			info := mapMic[uint32(uid)]
			if info != nil {
				info.VideoID = vid
			} else {
				mapMic[uint32(uid)] = &MicInfo{
					Uid:       uint32(uid),
					ChannelID: m.ChannelID,
					VideoID:   vid,
				}
			}
		}
	}
	return mapMic, nil
}

//PK当前阶段
func (p *PkState) GetPkStage() (pb.EnumChannelLivePKStatus, error) {
	rd := cache.GetGlobalRedis()
	btime, _ := rd.HGet(pkey(p.ChannelID_A), PKBEGIN).Uint64()
	passTime := time.Now().Unix() - int64(btime)
	state := conf.GetGConfig().GetState(int(passTime))
	return pb.EnumChannelLivePKStatus(state), nil
}

func (p *PkState) IsPKing() bool {
	stage, _ := p.GetPkStage()
	return stage != pb.EnumChannelLivePKStatus_IDLE
}

func (p *PkState) IsScoreing() bool {
	stage, _ := p.GetPkStage()
	if pb.EnumChannelLivePKStatus_BEGIN <= stage && stage <= pb.EnumChannelLivePKStatus_LAST {
		return true
	}
	return false
}

//pk信息
func (p *PkState) GetPkInfo() (*PKInfo, error) {
	rd := cache.GetGlobalRedis()
	pkInfo := &PKInfo{}

	arr, err := rd.HGetAll(pkey(p.ChannelID_A)).Result()
	if err != nil {
		return pkInfo, err
	}

	if err == nil {
		for k, v := range arr {
			val, _ := strconv.ParseUint(v, 10, 32)
			if k == TCID {
				pkInfo.TChannelID = uint32(val)
			} else if k == PKBEGIN {
				pkInfo.BeginTime = uint32(val)
			} else if k == PKSCORE {
				pkInfo.PKScore = uint32(val)
			} else if k == TUID {
				pkInfo.TUID = uint32(val)
			} else if k == APPLY_CID {
				pkInfo.ApplyCID = uint32(val)
			} else if k == EFFECT_ITEM_CNT {
				pkInfo.EffectCnt = uint32(val)
			} else if k == MUID {
				pkInfo.MUID = uint32(val)
			} else if k == MIC_FLAG {
				pkInfo.MicFlag = uint32(val)
			} else if k == APPLYID {
				pkInfo.ApplyID = int64(val)
			} else if k == FPKBEGIN {
				pkInfo.FBeginTime = uint32(val)
			} else if k == IsExtra {
				pkInfo.IsExtra = int32(val) > 0
			} else if k == IsOpenExtra {
				pkInfo.IsOpenExtra = int32(val) > 0
			} else if k == EXTRASCORE {
				pkInfo.ExtScore = uint32(val)
			} else if k == MatchSource {
				pkInfo.MatchSource = int32(val)
			} else if k == ExtraNum {
				pkInfo.ExtraNum = int32(val)
			}
		}
	}

	//兼容旧版
	if pkInfo.FBeginTime == 0 {
		pkInfo.FBeginTime = pkInfo.BeginTime
	}
	return pkInfo, nil
}

func (p *PkState) SetMicFlag(flat uint32) error {
	rd := cache.GetGlobalRedis()
	_, err := rd.HSet(pkey(p.ChannelID_A), MIC_FLAG, flat).Result()
	return err
}

//设置是否已经满足加时条件
func (p *PkState) SetExtra() {
	rd := cache.GetGlobalRedis()
	rd.HSet(pkey(p.ChannelID_A), IsExtra, 1)
	rd.HSet(pkey(p.ChannelID_B), IsExtra, 1)
}

//记录加时次数
func (p *PkState) IncrExtraNum() {
	rd := cache.GetGlobalRedis()
	rd.HIncrBy(pkey(p.ChannelID_A), ExtraNum, 1)
	rd.HIncrBy(pkey(p.ChannelID_B), ExtraNum, 1)
}

//PK房间麦位信息
func (p *PkState) GetMicList() (map[uint32]*MicInfo, error) {
	rd := cache.GetGlobalRedis()
	mkey := pmickey(p.ChannelID_A)
	mapMic := make(map[uint32]*MicInfo, 0)
	arr, err := rd.HGetAll(mkey).Result()
	if err == nil {
		for key, vid := range arr {
			uid, err := strconv.ParseUint(key, 10, 32)
			if err != nil {
				continue
			}
			mapMic[uint32(uid)] = &MicInfo{
				Uid:       uint32(uid),
				ChannelID: p.ChannelID_A,
				VoiceID:   vid,
			}
		}
	}
	vedioKey := pmicVedioKey(p.ChannelID_A)
	vedios, err := rd.HGetAll(vedioKey).Result()
	if err == nil {
		for key, vid := range vedios {
			uid, err := strconv.ParseUint(key, 10, 32)
			if err != nil {
				continue
			}

			info := mapMic[uint32(uid)]
			if info != nil {
				info.VideoID = vid
			} else {
				mapMic[uint32(uid)] = &MicInfo{
					Uid:       uint32(uid),
					ChannelID: p.ChannelID_A,
					VideoID:   vid,
				}
			}
		}
	}
	return mapMic, nil
}

/*** PK防偷塔 begin ***/

//TODO 使用begin做KEY的地方修改
func (p *PkState) DelayPkFinishTime() (uint64, error) {
	rd := cache.GetGlobalRedis()
	extraConf := conf.GetExtraTimeConf()
	newBeginTs := time.Now().Unix() - conf.GetPkTotalSec() + extraConf.LeftTs

	pipe := rd.Pipeline()
	defer pipe.Close()

	pipe.HSet(pkey(p.ChannelID_A), PKBEGIN, newBeginTs)
	pipe.HSet(pkey(p.ChannelID_B), PKBEGIN, newBeginTs)
	pipe.Exec()

	return uint64(newBeginTs), nil
}

/*** PK防偷塔  end ***/
