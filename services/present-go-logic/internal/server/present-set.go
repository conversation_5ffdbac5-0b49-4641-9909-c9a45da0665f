package server

import (
	"fmt"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/protocol/grpc"
	pb "golang.52tt.com/protocol/app/present-go-logic"
	"golang.52tt.com/protocol/common/status"
	present_set "golang.52tt.com/protocol/services/present-set"
	revenue_api_go "golang.52tt.com/protocol/services/revenue-api-go"
	"golang.52tt.com/services/present-go-logic/internal/conf"
	client "golang.52tt.com/services/present-go-logic/internal/rpc"
	"golang.org/x/net/context"
	"sort"
	"strconv"
	"time"
)

func (s *PresentGoLogic_) GetPresentSetInfo(ctx context.Context, req *pb.GetPresentSetInfoReq) (*pb.GetPresentSetInfoResp, error) {
	resp := &pb.GetPresentSetInfoResp{
		SetList: make([]*pb.PresentSetInfo, 0),
	}
	serviceInfo, ok := grpc.ServiceInfoFromContext(ctx)
	if !ok {
		log.ErrorWithCtx(ctx, "GetPresentSetInfo grpc.ServiceInfoFromContext err , uid %d", serviceInfo.UserID)
		return resp, protocol.NewExactServerError(nil, status.ErrRevenueSvrErr)
	}
	uid := serviceInfo.UserID

	// 获取配置信息，这个后续做到本地缓存

	_, setInfo := s.presentSetCache.GetConfigList()

	// 过滤一下
	setInfoList := make([]*present_set.PresentSetConfig, 0)
	originPresentMap := make(map[uint32]uint32)
	for _, item := range setInfo {
		if item.GetPeriod() != present_set.PresentSetPeriod_PRESENT_SET_PERIOD_LONG {
			if item.StartTime > uint32(time.Now().Unix()) || item.EndTime < uint32(time.Now().Unix()) {
				continue
			}
		}

		setInfoList = append(setInfoList, item)
		originPresentMap[item.GetSetId()] = item.GetOriginPresent().GetPresentId()
	}

	// 获取用户信息
	userInfo, err := client.PresentSetCli.GetUserSetsInfo(ctx, &present_set.GetUserSetsInfoReq{Uid: uid})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetUserSetsInfo err:%s", err)
		return resp, err
	}

	userInfoMap := make(map[uint32]*present_set.UserSetInfo)
	for _, item := range userInfo.GetUserSetInfoList() {
		userInfoMap[item.SetId] = item
	}

	// 组装信息
	for _, item := range setInfoList {
		if len(item.GetPresentList()) == 0 {
			continue
		}
		userPresentMap := make(map[uint32]*present_set.UserSetInfo_UserPresentInfo)
		for _, userPresent := range userInfoMap[item.GetSetId()].GetPresentList() {
			userPresentMap[userPresent.GetPresentId()] = userPresent
		}

		lastPresent := userInfoMap[item.GetSetId()].GetLastPresentId()

		SetItemList := make([]*pb.PresentSetItem, 0)
		setRankMap := make(map[uint32]float64)
		for _, setItem := range item.GetPresentList() {
			unlocked := userPresentMap[setItem.GetPresentId()].GetIsCollected()
			getAll := userInfoMap[item.SetId].GetCollectTotal() == userInfoMap[item.SetId].GetCollectProgress()

			tmp := &pb.PresentSetItem{
				ItemId:        setItem.GetPresentId(),
				RareLevel:     tranSvrRare(setItem.GetPresentLevel()),
				IsUnlocked:    userPresentMap[setItem.GetPresentId()].GetIsCollected(),
				IsNew:         userPresentMap[setItem.GetPresentId()].GetIsNew(),
				BackgroundUrl: s.presentSetConf.GetPresentLevelBackground(setItem.GetPresentLevel()), // 动态配置
			}

			sta := getTextStatus(unlocked, getAll)
			if sta == 1 {
				tmp.ExtendText = fmt.Sprintf(s.presentSetConf.GetPresentExtendText(sta), formatToTenThousandth(setItem.GetProbability()))
			} else {
				tmp.ExtendText = s.presentSetConf.GetPresentExtendText(sta)
			}

			setRankMap[setItem.GetPresentId()] = setItem.GetRank()

			SetItemList = append(SetItemList, tmp)
		}

		sort.Slice(SetItemList, func(i, j int) bool {
			// 解锁的在前面
			if SetItemList[i].GetIsUnlocked() != SetItemList[j].GetIsUnlocked() {
				return SetItemList[i].GetIsUnlocked()
			}

			// rank小的在前面
			if setRankMap[SetItemList[i].GetItemId()] != setRankMap[SetItemList[j].GetItemId()] {
				return setRankMap[SetItemList[i].GetItemId()] < setRankMap[SetItemList[j].GetItemId()]
			}

			return false
		})

		tmp := &pb.PresentSetInfo{
			SetId:         item.SetId,
			SetItemList:   SetItemList,
			DefaultItemId: lastPresent,
			BeginTime:     item.GetStartTime(),
			EndTime:       item.GetEndTime(),
			IsPermanent:   item.GetPeriod() == present_set.PresentSetPeriod_PRESENT_SET_PERIOD_LONG,
			Rank:          float32(item.GetRank()),
			IsBatch:       !s.presentConfigMemCache.GetConfigById(lastPresent).GetExtend().GetUnshowBatchOption(),
			IsNew:         userPresentMap[lastPresent].GetIsNew(),
		}

		resp.SetList = append(resp.SetList, tmp)

	}

	sort.Slice(resp.SetList, func(i, j int) bool {
		// 按rank，rank小的在前面，如果rank一致，再看礼物价值

		if resp.SetList[i].GetRank() != resp.SetList[j].GetRank() {
			return resp.SetList[i].GetRank() < resp.SetList[j].GetRank()
		}

		// 礼物价值大的在前面
		return s.presentConfigMemCache.GetConfigById(originPresentMap[resp.SetList[i].GetSetId()]).GetPrice() >
			s.presentConfigMemCache.GetConfigById(originPresentMap[resp.SetList[j].GetSetId()]).GetPrice()
	})

	for _, item := range setInfo {
		if item.GetChangeTime() >= resp.GetLastUpdateTime() {
			resp.LastUpdateTime = item.GetChangeTime()
		}
	}

	if userInfo.GetLastUpdateTime() >= resp.GetLastUpdateTime() {
		resp.LastUpdateTime = userInfo.GetLastUpdateTime()
	}

	return resp, nil
}

func (s *PresentGoLogic_) GetPresentSetDetail(ctx context.Context, req *pb.GetPresentSetDetailReq) (*pb.GetPresentSetDetailResp, error) {
	resp := &pb.GetPresentSetDetailResp{}

	serviceInfo, ok := grpc.ServiceInfoFromContext(ctx)
	if !ok {
		log.ErrorWithCtx(ctx, "GetPresentSetInfo grpc.ServiceInfoFromContext err , uid %d", serviceInfo.UserID)
		return resp, protocol.NewExactServerError(nil, status.ErrRevenueSvrErr)
	}
	uid := serviceInfo.UserID

	respInfo, err := client.PresentSetCli.GetPresentSetInfo(ctx, &present_set.GetPresentSetInfoReq{SetId: req.GetSetId()})
	if err != nil || respInfo == nil || (respInfo.GetPresentSet().GetPeriod() == present_set.PresentSetPeriod_PRESENT_SET_PERIOD_LIMITED &&
		(respInfo.GetPresentSet().GetEndTime() < uint32(time.Now().Unix()) || respInfo.GetPresentSet().GetStartTime() > uint32(time.Now().Unix()))) {
		log.ErrorWithCtx(ctx, "GetPresentSetInfo GetConfigById err , uid %d", serviceInfo.UserID)
		return resp, protocol.NewExactServerError(nil, status.ErrRevenueSvrErr, "活动已过期")
	}

	setInfo := respInfo.GetPresentSet()

	// 获取用户信息
	userInfo, err := client.PresentSetCli.GetUserSetsInfo(ctx, &present_set.GetUserSetsInfoReq{Uid: uid, SetId: req.GetSetId()})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetUserSetsInfo err:%s", err)
		return resp, err
	}

	setUserInfo := &present_set.UserSetInfo{}
	for _, item := range userInfo.GetUserSetInfoList() {
		if item.GetSetId() == req.GetSetId() {
			setUserInfo = item
		}
	}

	// 获取中奖用户账户信息
	uidList := make([]uint32, 0)
	for _, item := range userInfo.GetUserBroadcastList() {
		uidList = append(uidList, item.GetUid())
	}

	userMap, err := client.AccountCli.GetUsersMap(ctx, uidList)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetUsersMap err:%s", err)
		return resp, err
	}

	broadcastList := make([]*pb.PresentSetUserBroadcast, 0)
	accountList := make([]string, 0)
	for _, item := range userInfo.GetUserBroadcastList() {
		accountList = append(accountList, userMap[item.GetUid()].GetUsername())
	}

	headImageMap, err := client.HeadImageCli.BatchGetHeadImageMd5(ctx, uid, accountList)
	if err != nil {
		log.ErrorWithCtx(ctx, "BatchGetHeadImageMd5 err:%s", err)
		return resp, err
	}

	for _, item := range userInfo.GetUserBroadcastList() {
		broadcastList = append(broadcastList, &pb.PresentSetUserBroadcast{
			Uid:      item.GetUid(),
			Account:  userMap[item.GetUid()].GetUsername(),
			Nickname: userMap[item.GetUid()].GetNickname(),
			HeadMd5:  headImageMap[userMap[item.GetUid()].GetUsername()],
			ItemId:   item.GetPresentId(),
		})
	}

	collectionAward := make([]*pb.PresentSetCollectionAwardItem, 0)
	for _, item := range setInfo.GetAwardItemList() {
		collectionAward = append(collectionAward, &pb.PresentSetCollectionAwardItem{
			IconUrl:   item.GetAwardPic(),
			AwardType: pb.PresentSetCollectionAwardType(item.GetAwardType()),
			AwardName: item.GetAwardName(),
			AwardDesc: getAwardDesc(item.GetAwardType(), item.GetAwardCountInfo()),
			MarkText:  getAwardMark(item.GetAwardType(), item.GetAwardCount()),
		})
	}

	setItemList := make([]*pb.PresentSetItem, 0)
	setUserPresentInfo := make(map[uint32]*present_set.UserSetInfo_UserPresentInfo)
	for _, item := range setUserInfo.GetPresentList() {
		setUserPresentInfo[item.GetPresentId()] = item
	}

	emperorCfg := s.emperorSetCache.GetConfigById(setInfo.GetEmperorSetId())

	setRankMap := make(map[uint32]float64)
	for _, item := range setInfo.GetPresentList() {
		unlocked := setUserPresentInfo[item.GetPresentId()].GetIsCollected()
		getAll := setUserInfo.GetCollectTotal() == setUserInfo.GetCollectProgress()
		tmp := &pb.PresentSetItem{
			ItemId:        item.GetPresentId(),
			RareLevel:     tranSvrRare(item.GetPresentLevel()),
			IsUnlocked:    setUserPresentInfo[item.GetPresentId()].GetIsCollected(),
			IsNew:         setUserPresentInfo[item.GetPresentId()].GetIsNew(),
			BackgroundUrl: s.presentSetConf.GetPresentLevelBackground(item.GetPresentLevel()), // 动态配置
		}
		sta := getTextStatus(unlocked, getAll)
		if sta == 1 {
			tmp.ExtendText = fmt.Sprintf(s.presentSetConf.GetPresentExtendText(sta), formatToTenThousandth(item.GetProbability()))
		} else {
			tmp.ExtendText = s.presentSetConf.GetPresentExtendText(sta)
		}

		setItemList = append(setItemList, tmp)
		setRankMap[item.GetPresentId()] = item.GetRank()
	}

	sort.Slice(setItemList, func(i, j int) bool {

		// rank小的在前面
		if setRankMap[setItemList[i].GetItemId()] != setRankMap[setItemList[j].GetItemId()] {
			return setRankMap[setItemList[i].GetItemId()] < setRankMap[setItemList[j].GetItemId()]
		}

		return false
	})

	setDetail := pb.PresentSetDetail{
		SetId:         setInfo.GetSetId(),
		SetName:       setInfo.GetSetName(),
		BeginTime:     setInfo.GetStartTime(),
		EndTime:       setInfo.GetEndTime(),
		IsPermanent:   setInfo.GetPeriod() == present_set.PresentSetPeriod_PRESENT_SET_PERIOD_LONG,
		UserBroadcast: broadcastList,
		CollectionAward: &pb.PresentSetCollectionAward{
			AwardText:   setInfo.GetAwardText(),
			AwardList:   collectionAward,
			PreviewType: pb.PresentPreviewType(setInfo.GetPreviewType()),
			PreviewUrl:  setInfo.GetPreviewResUrl(),
			PreviewMd5:  setInfo.GetPreviewResMd5(),
		},
		CollectCount:           setUserInfo.GetCollectProgress(),
		TotalCount:             setUserInfo.GetCollectTotal(),
		SetItemList:            setItemList,
		ActivityUrl:            setInfo.GetActivityJumpUrl(),
		ActivityIcon:           setInfo.GetActivityEntranceResUrl(),
		CmsUrl:                 s.presentSetConf.GetCmsUrl(setInfo.GetSetId()),
		DefaultItemId:          setUserInfo.GetRarestPresentId(),
		Source:                 s.presentSetConf.PresentSetDetailResource, // 动态配置
		ActivityUrlType:        tranActivityUrlType(setInfo.GetActivityLinkType()),
		EmperorSetId:           setInfo.GetEmperorSetId(),
		SendAllEmperorSetText:  emperorCfg.GetSendEmperorSetText(),
		SendAllEmperorSetPrice: emperorCfg.GetPresentsTotalPrice(),
	}

	if setDetail.Source != nil {
		setDetail.Source.SendEmperorSetFrameUrl = emperorCfg.GetSendEmperorSetFrameUrl()
	}

	return &pb.GetPresentSetDetailResp{SetDetail: &setDetail}, nil
}

func getAwardDesc(awardType revenue_api_go.RevenueAwardType, awardCountInfo string) string {
	switch awardType {
	case revenue_api_go.RevenueAwardType_RevenueAwardTypePackage:
		return awardCountInfo
	case revenue_api_go.RevenueAwardType_RevenueAwardTypeOfficialCert:
		return "大v认证"
	case revenue_api_go.RevenueAwardType_RevenueAwardTypeNameplate:
		return "个人铭牌"
	case revenue_api_go.RevenueAwardType_RevenueAwardTypeHeadWear:
		return "麦位框"
	case revenue_api_go.RevenueAwardType_RevenueAwardTypeHorse:
		return "坐骑"
	case revenue_api_go.RevenueAwardType_RevenueAwardTypeFloat:
		return "主页飘"
	case revenue_api_go.RevenueAwardType_RevenueAwardTypeChannelInfoCard:
		return "房间资料卡"
	default:
		return ""
	}
}

func getAwardMark(awardType revenue_api_go.RevenueAwardType, awardCount uint32) string {
	switch awardType {
	case revenue_api_go.RevenueAwardType_RevenueAwardTypePackage:
		return "x" + strconv.Itoa(int(awardCount))
	default:
		return strconv.Itoa(int(awardCount)) + "天"
	}
}

func tranSvrRare(level present_set.PresentLevel) pb.PresentSetItemRareLevel {
	switch level {
	case present_set.PresentLevel_PRESENT_LEVEL_R:
		return pb.PresentSetItemRareLevel_PresentSetItemRareLevelRare
	case present_set.PresentLevel_PRESENT_LEVEL_SR:
		return pb.PresentSetItemRareLevel_PresentSetItemRareLevelSuperRare
	case present_set.PresentLevel_PRESENT_LEVEL_SSR:
		return pb.PresentSetItemRareLevel_PresentSetItemRareLevelSuperiorSuperRare
	default:
		return 0
	}
}

func getTextStatus(isUnlocked, isGetAll bool) uint32 {
	if isGetAll {
		return conf.StatusGetAll
	}
	if isUnlocked {
		return conf.StatusUnlocked
	}
	return conf.StatusNotUnlocked
}

func tranActivityUrlType(activityLinkType present_set.ActivityLinkType) pb.PresentSetActivityUrlType {
	switch activityLinkType {
	case present_set.ActivityLinkType_ACTIVITY_LINK_TYPE_HALF_SCREEN:
		return pb.PresentSetActivityUrlType_PresentSetActivityTypeHalfScreen
	case present_set.ActivityLinkType_ACTIVITY_LINK_TYPE_FULL_SCREEN:
		return pb.PresentSetActivityUrlType_PresentSetActivityTypeFullScreen
	default:
		return 0
	}
}

func formatToTenThousandth(val float64) string {
	tenThousandth := val * 100
	formattedStr := fmt.Sprintf("%.3f", tenThousandth)

	// 移除末尾的零
	for formattedStr[len(formattedStr)-1] == '0' {
		formattedStr = formattedStr[:len(formattedStr)-1]
	}

	// 移除末尾的点
	if formattedStr[len(formattedStr)-1] == '.' {
		formattedStr = formattedStr[:len(formattedStr)-1]
	}

	return formattedStr
}
