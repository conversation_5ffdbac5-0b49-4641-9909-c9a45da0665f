package emperor

import (
	"context"
	"golang.52tt.com/pkg/log"
	usual_device_svr "golang.52tt.com/protocol/services/usual-device-svr"
	"golang.52tt.com/services/present-go-logic/internal/model/info"
	"golang.52tt.com/services/present-go-logic/internal/model/interceptor"
	"golang.52tt.com/services/present-go-logic/internal/rpc"
	"time"
)

type usualDeviceInterceptor struct {
}

var UsualDeviceInterceptor *usualDeviceInterceptor

func WithUsualDeviceInterceptor() interceptor.Interceptor {
	return UsualDeviceInterceptor
}

func (s *usualDeviceInterceptor) Handle(info *info.RequestBaseInfo) error {
	// 500ms短超时，拉不到就直接返回
	ctx, cancel := context.WithTimeout(info.GetCtx(), 500*time.Millisecond)
	defer cancel()

	serviceInfo := info.GetServiceInfo()

	result, err := client.UsualDeviceCli.CheckUsualDevice(ctx, string(serviceInfo.DeviceID), serviceInfo.UserID,
		uint32(usual_device_svr.CheckType_ENUM_CHECK_TYPE_CONSUME), uint32(serviceInfo.ClientType))
	if err != nil {
		log.ErrorWithCtx(ctx, "usualDeviceInterceptor CheckUsualDevice fail, uid:%+v, err:%+v", serviceInfo.UserID, err)
		return err
	}

	if !result.GetResult() {
		err := client.UsualDeviceCli.GetDeviceAuthError(ctx, uint64(serviceInfo.UserID), serviceInfo.ClientType, serviceInfo.ClientVersion)
		if err != nil {
			return err
		}
	}

	return nil
}
