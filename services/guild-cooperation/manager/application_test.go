package manager

import (
	"bou.ke/monkey"
	"context"
	"github.com/golang/mock/gomock"
	"golang.52tt.com/clients/account"
	apicenter "golang.52tt.com/clients/apicenter/apiserver"
	censoring "golang.52tt.com/clients/censoring-proxy"
	"golang.52tt.com/clients/golddiamonn"
	"golang.52tt.com/clients/guild"
	"golang.52tt.com/clients/guildapi"
	mocksAccount "golang.52tt.com/clients/mocks/account"
	mockGolddiamonn "golang.52tt.com/clients/mocks/golddiamonn"
	mocksGuild "golang.52tt.com/clients/mocks/guild"
	mocksGuildApi "golang.52tt.com/clients/mocks/guildapi"
	mocksObsGateway "golang.52tt.com/clients/mocks/obsgateway"
	mocksTtcProxy "golang.52tt.com/clients/mocks/ttc-proxy"
	"golang.52tt.com/clients/obsgateway"
	ttc_proxy "golang.52tt.com/clients/ttc-proxy"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/protocol/app/auth"
	gaSync "golang.52tt.com/protocol/app/sync"
	accountPB "golang.52tt.com/protocol/services/accountsvr"
	pb "golang.52tt.com/protocol/services/guild-cooperation"
	guildapi2 "golang.52tt.com/protocol/services/guildapi"
	"golang.52tt.com/services/guild-cooperation/cache"
	"golang.52tt.com/services/guild-cooperation/common"
	"golang.52tt.com/services/guild-cooperation/conf"
	"golang.52tt.com/services/guild-cooperation/mocks"
	"golang.52tt.com/services/guild-cooperation/mongo"
	"golang.52tt.com/services/guild-cooperation/mysql"
	"golang.52tt.com/services/notify"
	"google.golang.org/grpc"
	"reflect"
	"strings"
	"testing"
	"time"
)

func TestManager_ApplyCooperation(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	ctx := context.Background()

	uid := uint32(123)
	guildId := uint32(456)
	applicationId := "appid"
	app := &mysql.CooperationApplication{
		ApplicationID:         applicationId,
		UID:                   uid,
		BusinessLicensePhotos: "1,2",
		BillHistoryPhotos:     "1,2",
		Step:                  pb.ApplicationStep_StepCommunicate,
	}

	req := &pb.ApplyCooperationReq{
		ApplicationId: applicationId,
		Uid:           uid,
		GuildId:       guildId,
	}

	mockMysql := mocks.NewMockIStore(ctl)
	mockAccountCli := mocksAccount.NewMockIClient(ctl)

	gomock.InOrder(
		mockMysql.EXPECT().GetApplication(ctx, applicationId).Return(app, nil),
		mockAccountCli.EXPECT().GetUser(ctx, uid).Return(&accountPB.UserResp{
			Uid:            uid,
			CurrentGuildId: guildId,
		}, nil),
		mockMysql.EXPECT().ApplicationCooperation(ctx, req).Return(nil),
	)

	resp := &pb.ApplyCooperationResp{}

	type fields struct {
		cacheStore        *cache.GuildCooperationCache
		mysqlStore        mysql.IStore
		mgo               *mongo.MongoDao
		sc                conf.IServiceConfigT
		guildClient       guild.IClient
		accountClient     account.IClient
		goldDiamondClient golddiamonn.IClient
		obsGatewayClient  obsgateway.IClient
	}
	type args struct {
		ctx context.Context
		req *pb.ApplyCooperationReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.ApplyCooperationResp
		wantErr bool
	}{
		{
			name: "ApplyCooperation",
			fields: fields{
				cacheStore:        nil,
				mysqlStore:        mockMysql,
				mgo:               nil,
				sc:                nil,
				guildClient:       nil,
				accountClient:     mockAccountCli,
				goldDiamondClient: nil,
				obsGatewayClient:  nil,
			},
			args: args{
				ctx: ctx,
				req: req,
			},
			want:    resp,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &Manager{
				mysqlStore:       tt.fields.mysqlStore,
				sc:               tt.fields.sc,
				guildClient:      tt.fields.guildClient,
				accountClient:    tt.fields.accountClient,
				obsGatewayClient: tt.fields.obsGatewayClient,
			}
			got, err := m.ApplyCooperation(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("ApplyCooperation() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("ApplyCooperation() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestManager_ApproveApplication(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	ctx := context.Background()

	uid := uint32(123)
	applicationId := "appid"
	step := pb.ApplicationStep_StepCooperation

	app := &mysql.CooperationApplication{
		ApplicationID:         applicationId,
		UID:                   uid,
		BusinessLicensePhotos: "1,2",
		BillHistoryPhotos:     "1,2",
		Step:                  pb.ApplicationStep_StepCooperation,
	}
	mockAccountCli := mocksAccount.NewMockIClient(ctl)
	mockObsCli := mocksObsGateway.NewMockIClient(ctl)
	mockConfig := mocks.NewMockIServiceConfigT(ctl)
	mockMysql := mocks.NewMockIStore(ctl)

	monkey.Patch(apicenter.NewClient, func(dopts ...grpc.DialOption) *apicenter.Client {
		return &apicenter.Client{}
	})

	monkey.PatchInstanceMethod(reflect.TypeOf(&apicenter.Client{}), "SendImMsg", func(c *apicenter.Client,
		ctx context.Context, uid uint32, appID protocol.AppID, msgList []*apicenter.ImMsg, notify bool) protocol.ServerError {
		return nil
	})

	gomock.InOrder(
		mockMysql.EXPECT().GetApplication(ctx, applicationId).Return(app, nil),
		mockMysql.EXPECT().ApproveApplication(ctx, app, pb.ApplicationStep_StepCooperation, pb.ApplicationOperation_OperationApproval, gomock.Any()).Return(nil),
		mockConfig.EXPECT().GetSmsUsage().Return(conf.SMSUsage{}),
		//mockAccountCli.EXPECT().GetUser(ctx, app.UID).Return(nil, nil),
		//mockConfig.EXPECT().GetObsConfig().Return("", "", ""),
	)

	resp := &pb.ApproveApplicationResp{}

	type fields struct {
		cacheStore        *cache.GuildCooperationCache
		mysqlStore        mysql.IStore
		mgo               *mongo.MongoDao
		sc                conf.IServiceConfigT
		guildClient       guild.IClient
		accountClient     account.IClient
		goldDiamondClient golddiamonn.IClient
		obsGatewayClient  obsgateway.IClient
		apiCenterClient   apicenter.IClient
	}
	type args struct {
		ctx context.Context
		req *pb.ApproveApplicationReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.ApproveApplicationResp
		wantErr bool
	}{
		{
			name: "ApproveApplication",
			fields: fields{
				mysqlStore:        mockMysql,
				sc:                mockConfig,
				guildClient:       nil,
				accountClient:     mockAccountCli,
				goldDiamondClient: nil,
				obsGatewayClient:  mockObsCli,
				apiCenterClient:   &apicenter.Client{},
			},
			args: args{
				ctx: ctx,
				req: &pb.ApproveApplicationReq{
					ApplicationId: applicationId,
					Step:          step,
				},
			},
			want:    resp,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &Manager{
				mysqlStore:       tt.fields.mysqlStore,
				sc:               tt.fields.sc,
				guildClient:      tt.fields.guildClient,
				accountClient:    tt.fields.accountClient,
				obsGatewayClient: tt.fields.obsGatewayClient,
				apiCenterClient:  tt.fields.apiCenterClient,
			}
			got, err := m.ApproveApplication(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("ApproveApplication() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("ApproveApplication() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestManager_CreateApplyLimit(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	ctx := context.Background()

	now := time.Now()
	uid := uint32(123)
	tid := "123"
	monkey.Patch(time.Now, func() time.Time {
		return now
	})

	req := &pb.CreateApplyLimitReq{
		Ttid:      tid,
		StartTime: 0,
		EndTime:   0,
		Operator:  "",
	}

	mockAccountCli := mocksAccount.NewMockIClient(ctl)
	mockObsCli := mocksObsGateway.NewMockIClient(ctl)
	mockConfig := mocks.NewMockIServiceConfigT(ctl)
	mockMysql := mocks.NewMockIStore(ctl)

	gomock.InOrder(
		mockAccountCli.EXPECT().GetUidByAlias(ctx, tid).Return(uid, "123", nil),
		mockMysql.EXPECT().GetLastApplication(ctx, uid, pb.CooperationType_CTypeInvalid).Return(nil, nil),
		mockMysql.EXPECT().GetLastApplyLimit(ctx, uid, now).Return(nil, nil),
		mockMysql.EXPECT().CreateApplyLimit(ctx, req, uid).Return(nil),
	)

	resp := &pb.CreateApplyLimitResp{}

	type fields struct {
		cacheStore        *cache.GuildCooperationCache
		mysqlStore        mysql.IStore
		mgo               *mongo.MongoDao
		sc                conf.IServiceConfigT
		guildClient       guild.IClient
		accountClient     account.IClient
		goldDiamondClient golddiamonn.IClient
		obsGatewayClient  obsgateway.IClient
	}
	type args struct {
		ctx context.Context
		req *pb.CreateApplyLimitReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.CreateApplyLimitResp
		wantErr bool
	}{
		{
			name: "CreateApplyLimit",
			fields: fields{
				mysqlStore:        mockMysql,
				sc:                mockConfig,
				guildClient:       nil,
				accountClient:     mockAccountCli,
				goldDiamondClient: nil,
				obsGatewayClient:  mockObsCli,
			},
			args: args{
				ctx: ctx,
				req: req,
			},
			want:    resp,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &Manager{
				mysqlStore:       tt.fields.mysqlStore,
				sc:               tt.fields.sc,
				guildClient:      tt.fields.guildClient,
				accountClient:    tt.fields.accountClient,
				obsGatewayClient: tt.fields.obsGatewayClient,
			}
			got, err := m.CreateApplyLimit(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("CreateApplyLimit() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("CreateApplyLimit() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestManager_CreateGuild(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	ctx := context.Background()

	uid := uint32(123)
	guildId := uint32(123)

	req := &pb.CreateGuildReq{
		Uid:       uid,
		GuildName: "",
	}
	resp := &pb.CreateGuildResp{
		GuildId: guildId,
	}

	mockAccountCli := mocksAccount.NewMockIClient(ctl)
	mockGuildCli := mocksGuild.NewMockIClient(ctl)
	mockGuildApiCli := mocksGuildApi.NewMockIClient(ctl)
	mockTTcProxyCli := mocksTtcProxy.NewMockIClient(ctl)
	mockObsCli := mocksObsGateway.NewMockIClient(ctl)
	mockConfig := mocks.NewMockIServiceConfigT(ctl)
	mockMysql := mocks.NewMockIStore(ctl)

	gomock.InOrder(
		mockAccountCli.EXPECT().GetUser(ctx, uid).Return(&accountPB.UserResp{
			Uid: uid,
			//CurrentGuildId: guildId,
		}, nil),
		mockTTcProxyCli.EXPECT().GetUserRealNameAuthInfo(ctx, uint64(uid)).Return(&ttc_proxy.GetAuthInfoResp{
			Status: int32(auth.E_REALNAME_AUTH_STATUS_ENUM_REALNAME_AUTH_PASS),
		}, nil),
		mockGuildApiCli.EXPECT().GuildCreate(ctx, &guildapi2.GuildCreateReq{
			BaseReq: &guildapi2.ApiBaseReq{
				Uid: req.GetUid(),
			},
			OwnerUid: uid,
			Name:     "",
		}).Return(&guildapi2.GuildCreateResp{GuildId: guildId}, nil),
	)

	type fields struct {
		cacheStore        *cache.GuildCooperationCache
		mysqlStore        mysql.IStore
		mgo               *mongo.MongoDao
		sc                conf.IServiceConfigT
		guildClient       guild.IClient
		guildApiClient    guildapi.IClient
		accountClient     account.IClient
		goldDiamondClient golddiamonn.IClient
		obsGatewayClient  obsgateway.IClient

		censoringProxyCli censoring.IClient
		ttProxyClient     ttc_proxy.IClient
	}
	type args struct {
		ctx context.Context
		req *pb.CreateGuildReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.CreateGuildResp
		wantErr bool
	}{
		{
			name: "CreateGuild",
			fields: fields{
				mysqlStore:        mockMysql,
				sc:                mockConfig,
				guildClient:       mockGuildCli,
				guildApiClient:    mockGuildApiCli,
				accountClient:     mockAccountCli,
				goldDiamondClient: nil,
				obsGatewayClient:  mockObsCli,
				censoringProxyCli: nil,
				ttProxyClient:     mockTTcProxyCli,
			},
			args: args{
				ctx: ctx,
				req: req,
			},
			want:    resp,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &Manager{
				mysqlStore:           tt.fields.mysqlStore,
				sc:                   tt.fields.sc,
				guildClient:          tt.fields.guildClient,
				guildApiClient:       tt.fields.guildApiClient,
				accountClient:        tt.fields.accountClient,
				obsGatewayClient:     tt.fields.obsGatewayClient,
				censoringProxyClient: tt.fields.censoringProxyCli,
				ttProxyClient:        tt.fields.ttProxyClient,
			}
			got, err := m.CreateGuild(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("CreateGuild() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("CreateGuild() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestManager_DeleteApplyLimit(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	ctx := context.Background()

	uid := uint32(123)

	req := &pb.DeleteApplyLimitReq{
		Uid: uid,
	}
	resp := &pb.DeleteApplyLimitResp{}

	mockAccountCli := mocksAccount.NewMockIClient(ctl)
	mockGuildCli := mocksGuild.NewMockIClient(ctl)
	mockObsCli := mocksObsGateway.NewMockIClient(ctl)
	mockConfig := mocks.NewMockIServiceConfigT(ctl)
	mockMysql := mocks.NewMockIStore(ctl)

	gomock.InOrder(
		mockMysql.EXPECT().DeleteApplyLimit(ctx, uid).Return(nil),
	)

	type fields struct {
		cacheStore        *cache.GuildCooperationCache
		mysqlStore        mysql.IStore
		mgo               *mongo.MongoDao
		sc                conf.IServiceConfigT
		guildClient       guild.IClient
		accountClient     account.IClient
		goldDiamondClient golddiamonn.IClient
		obsGatewayClient  obsgateway.IClient
	}
	type args struct {
		ctx context.Context
		req *pb.DeleteApplyLimitReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.DeleteApplyLimitResp
		wantErr bool
	}{
		{
			name: "DeleteApplyLimit",
			fields: fields{
				mysqlStore:        mockMysql,
				sc:                mockConfig,
				guildClient:       mockGuildCli,
				accountClient:     mockAccountCli,
				goldDiamondClient: nil,
				obsGatewayClient:  mockObsCli,
			},
			args: args{
				ctx: ctx,
				req: req,
			},
			want:    resp,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &Manager{
				mysqlStore:       tt.fields.mysqlStore,
				sc:               tt.fields.sc,
				guildClient:      tt.fields.guildClient,
				accountClient:    tt.fields.accountClient,
				obsGatewayClient: tt.fields.obsGatewayClient,
			}
			got, err := m.DeleteApplyLimit(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("DeleteApplyLimit() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("DeleteApplyLimit() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestManager_DeleteNotification(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	ctx := context.Background()

	nid := uint32(123)

	req := &pb.DeleteNotificationReq{
		NotificationId: nid,
	}
	resp := &pb.DeleteNotificationResp{}

	mockAccountCli := mocksAccount.NewMockIClient(ctl)
	mockGuildCli := mocksGuild.NewMockIClient(ctl)
	mockObsCli := mocksObsGateway.NewMockIClient(ctl)
	mockConfig := mocks.NewMockIServiceConfigT(ctl)
	mockMysql := mocks.NewMockIStore(ctl)

	gomock.InOrder(
		mockMysql.EXPECT().DeleteNotification(ctx, nid).Return(nil),
	)

	type fields struct {
		cacheStore        *cache.GuildCooperationCache
		mysqlStore        mysql.IStore
		mgo               *mongo.MongoDao
		sc                conf.IServiceConfigT
		guildClient       guild.IClient
		accountClient     account.IClient
		goldDiamondClient golddiamonn.IClient
		obsGatewayClient  obsgateway.IClient
	}
	type args struct {
		ctx context.Context
		req *pb.DeleteNotificationReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.DeleteNotificationResp
		wantErr bool
	}{
		{
			name: "DeleteNotification",
			fields: fields{
				mysqlStore:        mockMysql,
				sc:                mockConfig,
				guildClient:       mockGuildCli,
				accountClient:     mockAccountCli,
				goldDiamondClient: nil,
				obsGatewayClient:  mockObsCli,
			},
			args: args{
				ctx: ctx,
				req: req,
			},
			want:    resp,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &Manager{
				mysqlStore:       tt.fields.mysqlStore,
				sc:               tt.fields.sc,
				guildClient:      tt.fields.guildClient,
				accountClient:    tt.fields.accountClient,
				obsGatewayClient: tt.fields.obsGatewayClient,
			}
			got, err := m.DeleteNotification(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("DeleteNotification() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("DeleteNotification() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestManager_EditNotification(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	ctx := context.Background()

	nid := uint32(123)

	req := &pb.EditNotificationReq{
		NotificationId: nid,
	}
	resp := &pb.EditNotificationResp{}

	mockAccountCli := mocksAccount.NewMockIClient(ctl)
	mockGuildCli := mocksGuild.NewMockIClient(ctl)
	mockObsCli := mocksObsGateway.NewMockIClient(ctl)
	mockConfig := mocks.NewMockIServiceConfigT(ctl)
	mockMysql := mocks.NewMockIStore(ctl)

	gomock.InOrder(
		mockMysql.EXPECT().CreateOrEditNotification(ctx, req).Return(nil),
	)

	type fields struct {
		cacheStore        *cache.GuildCooperationCache
		mysqlStore        mysql.IStore
		mgo               *mongo.MongoDao
		sc                conf.IServiceConfigT
		guildClient       guild.IClient
		accountClient     account.IClient
		goldDiamondClient golddiamonn.IClient
		obsGatewayClient  obsgateway.IClient
	}
	type args struct {
		ctx context.Context
		req *pb.EditNotificationReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.EditNotificationResp
		wantErr bool
	}{
		{
			name: "EditNotification",
			fields: fields{
				mysqlStore:        mockMysql,
				sc:                mockConfig,
				guildClient:       mockGuildCli,
				accountClient:     mockAccountCli,
				goldDiamondClient: nil,
				obsGatewayClient:  mockObsCli,
			},
			args: args{
				ctx: ctx,
				req: req,
			},
			want:    resp,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &Manager{
				mysqlStore:       tt.fields.mysqlStore,
				sc:               tt.fields.sc,
				guildClient:      tt.fields.guildClient,
				accountClient:    tt.fields.accountClient,
				obsGatewayClient: tt.fields.obsGatewayClient,
			}
			got, err := m.EditNotification(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("EditNotification() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("EditNotification() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestManager_GetApplicationInfo(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	ctx := context.Background()

	uid := uint32(123)
	applicationId := "appid"

	app := &mysql.CooperationApplication{
		ApplicationID:         applicationId,
		UID:                   uid,
		BusinessLicensePhotos: "1",
		Step:                  pb.ApplicationStep_StepCooperation,
	}

	appPb := app.ConvToPb()
	appPb.BusinessLicensePhotos = []string{"//1?token=123"}

	req := &pb.GetApplicationInfoReq{
		Uid:           uid,
		ApplicationId: applicationId,
	}

	mockAccountCli := mocksAccount.NewMockIClient(ctl)
	mockObsCli := mocksObsGateway.NewMockIClient(ctl)
	mockConfig := mocks.NewMockIServiceConfigT(ctl)
	mockMysql := mocks.NewMockIStore(ctl)

	gomock.InOrder(
		mockMysql.EXPECT().GetApplication(ctx, applicationId).Return(app, nil),
		mockAccountCli.EXPECT().GetUser(ctx, uid).Return(&accountPB.UserResp{
			Uid: uid,
			//CurrentGuildId: guildId,
		}, nil),
		mockConfig.EXPECT().GetObsConfig().Return("", "", ""),
		mockObsCli.EXPECT().ClaimToken(ctx, gomock.Any()).Return([]string{"123"}, nil),
	)

	resp := &pb.GetApplicationInfoResp{
		Application: appPb,
	}

	type fields struct {
		cacheStore        *cache.GuildCooperationCache
		mysqlStore        mysql.IStore
		mgo               *mongo.MongoDao
		sc                conf.IServiceConfigT
		guildClient       guild.IClient
		accountClient     account.IClient
		goldDiamondClient golddiamonn.IClient
		obsGatewayClient  obsgateway.IClient
	}
	type args struct {
		ctx context.Context
		req *pb.GetApplicationInfoReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.GetApplicationInfoResp
		wantErr bool
	}{
		{
			name: "GetApplicationInfo",
			fields: fields{
				mysqlStore:        mockMysql,
				sc:                mockConfig,
				guildClient:       nil,
				accountClient:     mockAccountCli,
				goldDiamondClient: nil,
				obsGatewayClient:  mockObsCli,
			},
			args: args{
				ctx: ctx,
				req: req,
			},
			want:    resp,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &Manager{
				mysqlStore:       tt.fields.mysqlStore,
				sc:               tt.fields.sc,
				guildClient:      tt.fields.guildClient,
				accountClient:    tt.fields.accountClient,
				obsGatewayClient: tt.fields.obsGatewayClient,
			}
			got, err := m.GetApplicationInfo(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetApplicationInfo() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetApplicationInfo() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestManager_GetApplications(t *testing.T) {
	type fields struct {
		cacheStore        *cache.GuildCooperationCache
		mysqlStore        mysql.IStore
		mgo               *mongo.MongoDao
		sc                conf.IServiceConfigT
		guildClient       guild.IClient
		accountClient     account.IClient
		goldDiamondClient golddiamonn.IClient
		obsGatewayClient  obsgateway.IClient
	}
	type args struct {
		ctx context.Context
		req *pb.GetApplicationsReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.GetApplicationsResp
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &Manager{
				mysqlStore:       tt.fields.mysqlStore,
				sc:               tt.fields.sc,
				guildClient:      tt.fields.guildClient,
				accountClient:    tt.fields.accountClient,
				obsGatewayClient: tt.fields.obsGatewayClient,
			}
			got, err := m.GetApplications(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetApplications() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetApplications() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestManager_GetApplyLimitList(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	ctx := context.Background()

	now := time.Now()
	uid := uint32(123)
	tid := "123"

	appLimit := &mysql.ApplyLimit{
		UID:        uid,
		CreateTime: now,
		UpdateTime: now,
	}
	appLimitList := []*mysql.ApplyLimit{appLimit}

	appLimitPb := appLimit.ConvToPb()
	appLimitPb.Ttid = tid

	req := &pb.GetApplyLimitListReq{
		Ttid: tid,
	}

	mockAccountCli := mocksAccount.NewMockIClient(ctl)
	mockObsCli := mocksObsGateway.NewMockIClient(ctl)
	mockConfig := mocks.NewMockIServiceConfigT(ctl)
	mockMysql := mocks.NewMockIStore(ctl)

	gomock.InOrder(
		mockAccountCli.EXPECT().GetUidByAlias(ctx, tid).Return(uid, "123", nil),
		mockMysql.EXPECT().GetApplyLimitList(ctx, req, uid).Return(appLimitList, int64(1), nil),
		mockAccountCli.EXPECT().BatGetUserByUid(ctx, uid).Return(map[uint32]*accountPB.UserResp{
			uid: {
				Alias: tid,
			},
		}, nil),
	)

	resp := &pb.GetApplyLimitListResp{
		ApplyLimits: []*pb.ApplyLimit{
			appLimitPb,
		},
		Total: 1,
	}

	type fields struct {
		cacheStore        *cache.GuildCooperationCache
		mysqlStore        mysql.IStore
		mgo               *mongo.MongoDao
		sc                conf.IServiceConfigT
		guildClient       guild.IClient
		accountClient     account.IClient
		goldDiamondClient golddiamonn.IClient
		obsGatewayClient  obsgateway.IClient
	}
	type args struct {
		ctx context.Context
		req *pb.GetApplyLimitListReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.GetApplyLimitListResp
		wantErr bool
	}{
		{
			name: "GetApplyLimitList",
			fields: fields{
				mysqlStore:        mockMysql,
				sc:                mockConfig,
				guildClient:       nil,
				accountClient:     mockAccountCli,
				goldDiamondClient: nil,
				obsGatewayClient:  mockObsCli,
			},
			args: args{
				ctx: ctx,
				req: req,
			},
			want:    resp,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &Manager{
				mysqlStore:       tt.fields.mysqlStore,
				sc:               tt.fields.sc,
				guildClient:      tt.fields.guildClient,
				accountClient:    tt.fields.accountClient,
				obsGatewayClient: tt.fields.obsGatewayClient,
			}
			got, err := m.GetApplyLimitList(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetApplyLimitList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetApplyLimitList() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestManager_GetCooperationInfo(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	ctx := context.Background()
	uid := uint32(123)
	guildId := uint32(123)
	tid := "123"

	req := &pb.GetCooperationInfoReq{
		Uid: uid,
	}

	mockAccountCli := mocksAccount.NewMockIClient(ctl)
	mockObsCli := mocksObsGateway.NewMockIClient(ctl)
	mockGoldDiamonnCli := mockGolddiamonn.NewMockIClient(ctl)
	mockConfig := mocks.NewMockIServiceConfigT(ctl)
	mockDyConfig := mocks.NewMockIConfDynamic(ctl)
	mockMysql := mocks.NewMockIStore(ctl)

	gomock.InOrder(
		mockDyConfig.EXPECT().Get().Return(&conf.GuildCooperationDyConf{}),
		mockAccountCli.EXPECT().GetUser(ctx, uid).Return(&accountPB.UserResp{
			Uid:            uid,
			Alias:          tid,
			CurrentGuildId: guildId,
		}, nil),
		// m.GetGuildCooperationInfos
		mockMysql.EXPECT().GetCooperationWithGuild(ctx, guildId).Return([]*mysql.GuildCooperationInfo{
			{
				GuildId:         guildId,
				CooperationType: uint32(pb.CooperationType_CTypeAmuse),
			},
			{
				GuildId:         guildId,
				CooperationType: uint32(pb.CooperationType_CTypeYuyin),
			},
			{
				GuildId:         guildId,
				CooperationType: uint32(pb.CooperationType_CTypeESports),
			},
		}, nil),
		//mockMysql.EXPECT().GetLastApplication(ctx, uid, pb.CooperationType_CTypeESports).Return(&mysql.CooperationApplication{}, nil),
	)

	resp := &pb.GetCooperationInfoResp{
		CooperationInfo: []*pb.CooperationInfo{
			{
				CooperationType: pb.CooperationType_CTypeAmuse,
				IsCooperation:   true,
			},
			{
				CooperationType: pb.CooperationType_CTypeYuyin,
				IsCooperation:   true,
			},
			{
				CooperationType: pb.CooperationType_CTypeESports,
				IsCooperation:   true,
			},
		},
	}

	type fields struct {
		cacheStore        *cache.GuildCooperationCache
		mysqlStore        mysql.IStore
		mgo               *mongo.MongoDao
		sc                conf.IServiceConfigT
		guildClient       guild.IClient
		accountClient     account.IClient
		goldDiamondClient golddiamonn.IClient
		obsGatewayClient  obsgateway.IClient
		dyCfg             conf.IConfDynamic
	}
	type args struct {
		ctx context.Context
		req *pb.GetCooperationInfoReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.GetCooperationInfoResp
		wantErr bool
	}{
		{
			name: "GetCooperationInfo",
			fields: fields{
				mysqlStore:        mockMysql,
				sc:                mockConfig,
				guildClient:       nil,
				accountClient:     mockAccountCli,
				goldDiamondClient: mockGoldDiamonnCli,
				obsGatewayClient:  mockObsCli,
				dyCfg:             mockDyConfig,
			},
			args: args{
				ctx: ctx,
				req: req,
			},
			want:    resp,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &Manager{
				mysqlStore:       tt.fields.mysqlStore,
				sc:               tt.fields.sc,
				guildClient:      tt.fields.guildClient,
				accountClient:    tt.fields.accountClient,
				obsGatewayClient: tt.fields.obsGatewayClient,
				dyCfg:            tt.fields.dyCfg,
			}
			got, err := m.GetCooperationInfo(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetCooperationInfo() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetCooperationInfo() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestManager_GetGuildInfo(t *testing.T) {
	type fields struct {
		cacheStore        *cache.GuildCooperationCache
		mysqlStore        mysql.IStore
		mgo               *mongo.MongoDao
		sc                conf.IServiceConfigT
		guildClient       guild.IClient
		accountClient     account.IClient
		goldDiamondClient golddiamonn.IClient
		obsGatewayClient  obsgateway.IClient
	}
	type args struct {
		ctx context.Context
		req *pb.GetGuildInfoReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.GetGuildInfoResp
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &Manager{
				mysqlStore:    tt.fields.mysqlStore,
				sc:            tt.fields.sc,
				guildClient:   tt.fields.guildClient,
				accountClient: tt.fields.accountClient,

				obsGatewayClient: tt.fields.obsGatewayClient,
			}
			got, err := m.GetGuildInfo(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetGuildInfo() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetGuildInfo() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestManager_GetNotificationList(t *testing.T) {
	type fields struct {
		cacheStore        *cache.GuildCooperationCache
		mysqlStore        mysql.IStore
		mgo               *mongo.MongoDao
		sc                conf.IServiceConfigT
		guildClient       guild.IClient
		accountClient     account.IClient
		goldDiamondClient golddiamonn.IClient
		obsGatewayClient  obsgateway.IClient
	}
	type args struct {
		ctx context.Context
		req *pb.GetNotificationListReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.GetNotificationListResp
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &Manager{
				mysqlStore:    tt.fields.mysqlStore,
				sc:            tt.fields.sc,
				guildClient:   tt.fields.guildClient,
				accountClient: tt.fields.accountClient,

				obsGatewayClient: tt.fields.obsGatewayClient,
			}
			got, err := m.GetNotificationList(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetNotificationList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetNotificationList() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestManager_GetOperationHistory(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	ctx := context.Background()
	now := time.Now()
	uid := uint32(123)
	tid := "123"
	applicationId := "applicationId"
	monkey.Patch(common.CreateApplicationID, func() string {
		return applicationId
	})

	app := &mysql.CooperationApplicationHistory{
		ApplicationID:   applicationId,
		UID:             uid,
		CooperationType: pb.CooperationType_CTypeYuyin,
		Step:            pb.ApplicationStep_StepSubmit,
		CreateTime:      now,
	}
	apps := []*mysql.CooperationApplicationHistory{app}

	req := &pb.GetOperationHistoryReq{}

	mockAccountCli := mocksAccount.NewMockIClient(ctl)
	mockObsCli := mocksObsGateway.NewMockIClient(ctl)
	mockConfig := mocks.NewMockIServiceConfigT(ctl)
	mockMysql := mocks.NewMockIStore(ctl)

	gomock.InOrder(
		mockMysql.EXPECT().GetOperationHistoryList(ctx, req, uint32(0)).Return(apps, int64(1), nil),
		mockAccountCli.EXPECT().BatGetUserByUid(ctx, uid).Return(map[uint32]*accountPB.UserResp{
			uid: {
				Alias: tid,
			},
		}, nil),
	)

	resp := &pb.GetOperationHistoryResp{
		Total: uint32(1),
		OperateHistories: []*pb.OperateHistory{
			{
				ApplicationId:   applicationId,
				Uid:             uid,
				Ttid:            tid,
				CooperationType: pb.CooperationType_CTypeYuyin,
				Step:            pb.ApplicationStep_StepSubmit,
				CreateTime:      uint32(now.Unix()),
			},
		},
	}

	type fields struct {
		cacheStore        *cache.GuildCooperationCache
		mysqlStore        mysql.IStore
		mgo               *mongo.MongoDao
		sc                conf.IServiceConfigT
		guildClient       guild.IClient
		accountClient     account.IClient
		goldDiamondClient golddiamonn.IClient
		obsGatewayClient  obsgateway.IClient
	}
	type args struct {
		ctx context.Context
		req *pb.GetOperationHistoryReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.GetOperationHistoryResp
		wantErr bool
	}{
		{
			name: "GetOperationHistory",
			fields: fields{
				mysqlStore:        mockMysql,
				sc:                mockConfig,
				guildClient:       nil,
				accountClient:     mockAccountCli,
				goldDiamondClient: nil,
				obsGatewayClient:  mockObsCli,
			},
			args: args{
				ctx: ctx,
				req: req,
			},
			want:    resp,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &Manager{
				mysqlStore:    tt.fields.mysqlStore,
				sc:            tt.fields.sc,
				guildClient:   tt.fields.guildClient,
				accountClient: tt.fields.accountClient,

				obsGatewayClient: tt.fields.obsGatewayClient,
			}
			got, err := m.GetOperationHistory(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetOperationHistory() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetOperationHistory() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestManager_GetSimpleApplicationById(t *testing.T) {
	type fields struct {
		cacheStore        *cache.GuildCooperationCache
		mysqlStore        mysql.IStore
		mgo               *mongo.MongoDao
		sc                conf.IServiceConfigT
		guildClient       guild.IClient
		accountClient     account.IClient
		goldDiamondClient golddiamonn.IClient
		obsGatewayClient  obsgateway.IClient
	}
	type args struct {
		ctx   context.Context
		appId string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *mysql.CooperationApplication
		want1   *pb.CooperationApplication
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &Manager{
				mysqlStore:    tt.fields.mysqlStore,
				sc:            tt.fields.sc,
				guildClient:   tt.fields.guildClient,
				accountClient: tt.fields.accountClient,

				obsGatewayClient: tt.fields.obsGatewayClient,
			}
			got, got1, err := m.GetSimpleApplicationById(tt.args.ctx, tt.args.appId)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetSimpleApplicationById() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetSimpleApplicationById() got = %v, want %v", got, tt.want)
			}
			if !reflect.DeepEqual(got1, tt.want1) {
				t.Errorf("GetSimpleApplicationById() got1 = %v, want %v", got1, tt.want1)
			}
		})
	}
}

func TestManager_RejectApplication(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	ctx := context.Background()

	uid := uint32(123)
	applicationId := "appid"
	step := pb.ApplicationStep_StepCooperation

	app := &mysql.CooperationApplication{
		ApplicationID:         applicationId,
		UID:                   uid,
		BusinessLicensePhotos: "1,2",
		BillHistoryPhotos:     "1,2",
		Step:                  pb.ApplicationStep_StepCooperation,
	}
	mockAccountCli := mocksAccount.NewMockIClient(ctl)
	mockObsCli := mocksObsGateway.NewMockIClient(ctl)
	mockConfig := mocks.NewMockIServiceConfigT(ctl)
	mockMysql := mocks.NewMockIStore(ctl)

	monkey.Patch(apicenter.NewClient, func(dopts ...grpc.DialOption) *apicenter.Client {
		return &apicenter.Client{}
	})

	monkey.PatchInstanceMethod(reflect.TypeOf(&apicenter.Client{}), "SendImMsg", func(c *apicenter.Client,
		ctx context.Context, uid uint32, appID protocol.AppID, msgList []*apicenter.ImMsg, notify bool) protocol.ServerError {
		return nil
	})

	gomock.InOrder(
		mockMysql.EXPECT().GetApplication(ctx, applicationId).Return(app, nil),
		mockMysql.EXPECT().ApproveApplication(ctx, app, pb.ApplicationStep_StepCooperation, pb.ApplicationOperation_OperationReject, nil).Return(nil),
		mockConfig.EXPECT().GetSmsUsage().Return(conf.SMSUsage{}),
		//mockAccountCli.EXPECT().GetUser(ctx, app.UID).Return(nil, nil),
		//mockConfig.EXPECT().GetObsConfig().Return("", "", ""),
	)

	resp := &pb.RejectApplicationResp{}

	type fields struct {
		cacheStore        *cache.GuildCooperationCache
		mysqlStore        mysql.IStore
		mgo               *mongo.MongoDao
		sc                conf.IServiceConfigT
		guildClient       guild.IClient
		accountClient     account.IClient
		goldDiamondClient golddiamonn.IClient
		obsGatewayClient  obsgateway.IClient
		apiCenterClient   apicenter.IClient
	}
	type args struct {
		ctx context.Context
		req *pb.RejectApplicationReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.RejectApplicationResp
		wantErr bool
	}{
		{
			name: "RejectApplication",
			fields: fields{
				mysqlStore:        mockMysql,
				sc:                mockConfig,
				guildClient:       nil,
				accountClient:     mockAccountCli,
				goldDiamondClient: nil,
				obsGatewayClient:  mockObsCli,
				apiCenterClient:   &apicenter.Client{},
			},
			args: args{
				ctx: ctx,
				req: &pb.RejectApplicationReq{
					ApplicationId: applicationId,
					Step:          step,
				},
			},
			want:    resp,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &Manager{
				mysqlStore:       tt.fields.mysqlStore,
				sc:               tt.fields.sc,
				guildClient:      tt.fields.guildClient,
				accountClient:    tt.fields.accountClient,
				obsGatewayClient: tt.fields.obsGatewayClient,
				apiCenterClient:  tt.fields.apiCenterClient,
			}
			got, err := m.RejectApplication(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("RejectApplication() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("RejectApplication() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestManager_SubmitApplication(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	ctx := context.Background()
	uid := uint32(123)
	guildId := uint32(123)
	applicationId := "applicationId"
	now := time.Now()
	monkey.Patch(common.CreateApplicationID, func() string {
		return applicationId
	})
	monkey.Patch(time.Now, func() time.Time {
		return now
	})

	monkey.Patch(notify.NotifySync, func(uid uint32, syncType gaSync.SyncReq_SyncType) (totalPres, ok int) {
		return 0, 0
	})

	monkey.Patch(apicenter.NewClient, func(dopts ...grpc.DialOption) *apicenter.Client {
		return &apicenter.Client{}
	})

	monkey.PatchInstanceMethod(reflect.TypeOf(&apicenter.Client{}), "SendImMsg", func(c *apicenter.Client,
		ctx context.Context, uid uint32, appID protocol.AppID, msgList []*apicenter.ImMsg, notify bool) protocol.ServerError {
		return nil
	})

	req := &pb.SubmitApplicationReq{
		Uid:                   uid,
		ExternalPlatform:      "e",
		Wechat:                "w",
		Corporation:           "c",
		LegalPerson:           "l",
		CorporationCode:       "cc",
		BankAccount:           "b",
		BusinessLicensePhotos: []string{"1"},
		BillHistoryPhotos:     []string{"1"},
		//CooperationType:       pb.CooperationType_CTypeYuyin,
		CooperationTypes: []pb.CooperationType{pb.CooperationType_CTypeYuyin},
		SourcePlatform:   pb.SourcePlatform_PLATFORM_APP,
	}

	app := &mysql.CooperationApplication{
		ApplicationID:         applicationId,
		UID:                   uid,
		CooperationType:       pb.CooperationType_CTypeYuyin,
		WechatID:              req.GetWechat(),
		LegalPerson:           req.GetLegalPerson(),
		CorporationName:       req.GetCorporation(),
		CorporationCode:       req.GetCorporationCode(),
		BankAccount:           req.GetBankAccount(),
		ExternalPlatform:      req.GetExternalPlatform(),
		BusinessLicensePhotos: strings.Join(req.GetBusinessLicensePhotos(), ","),
		BillHistoryPhotos:     strings.Join(req.GetBillHistoryPhotos(), ","),
		Step:                  pb.ApplicationStep_StepSubmit, // 第一步 提交资料 未审核
		SourcePlatform:        req.GetSourcePlatform(),
		Phone:                 req.GetPhone(),
	}
	apps := []*mysql.CooperationApplication{app}

	mockAccountCli := mocksAccount.NewMockIClient(ctl)
	mockObsCli := mocksObsGateway.NewMockIClient(ctl)
	mockConfig := mocks.NewMockIServiceConfigT(ctl)
	mockMysql := mocks.NewMockIStore(ctl)
	mockGoldDiamonnCli := mockGolddiamonn.NewMockIClient(ctl)

	gomock.InOrder(
		mockAccountCli.EXPECT().GetUser(ctx, uid).Return(&accountPB.UserResp{
			Uid:            uid,
			CurrentGuildId: guildId,
		}, nil),
		// m.GetGuildCooperationInfos
		mockMysql.EXPECT().GetCooperationWithGuild(ctx, guildId).Return([]*mysql.GuildCooperationInfo{
			{
				GuildId:         guildId,
				CooperationType: uint32(pb.CooperationType_CTypeAmuse),
			},
		}, nil),
		mockMysql.EXPECT().GetLastApplication(ctx, uid, pb.CooperationType_CTypeYuyin).Return(nil, nil),
		//mockMysql.EXPECT().GetLastApplicationByPhone(ctx, phone, pb.CooperationType_CTypeInvalid).Return(nil, nil),

		mockMysql.EXPECT().GetLastApplyLimit(ctx, uid, now).Return(nil, nil),
		mockMysql.EXPECT().CreateApplications(ctx, apps).Return(nil),
		mockConfig.EXPECT().GetSmsUsage().Return(conf.SMSUsage{}),
	)

	resp := &pb.SubmitApplicationResp{
		//ApplicationId: applicationId,
		ApplicationIds: []string{applicationId},
	}

	type fields struct {
		cacheStore        *cache.GuildCooperationCache
		mysqlStore        mysql.IStore
		mgo               *mongo.MongoDao
		sc                conf.IServiceConfigT
		guildClient       guild.IClient
		accountClient     account.IClient
		goldDiamondClient golddiamonn.IClient
		obsGatewayClient  obsgateway.IClient
		apiCenterClient   apicenter.IClient
	}
	type args struct {
		ctx context.Context
		req *pb.SubmitApplicationReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.SubmitApplicationResp
		wantErr bool
	}{
		{
			name: "SubmitApplication",
			fields: fields{
				mysqlStore:        mockMysql,
				sc:                mockConfig,
				guildClient:       nil,
				accountClient:     mockAccountCli,
				goldDiamondClient: mockGoldDiamonnCli,
				obsGatewayClient:  mockObsCli,
				apiCenterClient:   &apicenter.Client{},
			},
			args: args{
				ctx: ctx,
				req: req,
			},
			want:    resp,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &Manager{
				mysqlStore:       tt.fields.mysqlStore,
				sc:               tt.fields.sc,
				guildClient:      tt.fields.guildClient,
				accountClient:    tt.fields.accountClient,
				obsGatewayClient: tt.fields.obsGatewayClient,
				apiCenterClient:  tt.fields.apiCenterClient,
			}
			got, err := m.SubmitApplication(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("SubmitApplication() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("SubmitApplication() got = %v, want %v", got, tt.want)
			}
		})
	}
	time.Sleep(time.Millisecond * 100)
}
