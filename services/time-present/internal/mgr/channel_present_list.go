package mgr

import (
	"context"
	"fmt"
	"github.com/golang/protobuf/proto"
	micLayout "gitlab.ttyuyin.com/gengo/bizplatform-ttinfra/tt/bizplatform/ttinfra/channel_mic_layout"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	protogrpc "golang.52tt.com/pkg/protocol/grpc"
	ga "golang.52tt.com/protocol/app"
	channelPB_ "golang.52tt.com/protocol/app/channel"
	"golang.52tt.com/protocol/app/time_present"
	"golang.52tt.com/protocol/common/status"
	pb "golang.52tt.com/protocol/services/time-present"
	"golang.52tt.com/services/time-present/internal/cache"
	"time"
)

// AddChannelTimePresent 添加时间礼物到礼物队列
func (m *manager) AddChannelTimePresent(c context.Context, req *pb.AddChannelTimePresentReq) (*pb.AddChannelTimePresentResp, error) {
	resp := &pb.AddChannelTimePresentResp{}

	log.DebugWithCtx(c, "AddChannelTimePresent req: %+v", req)

	// 幂等
	ok, err := m.cache.CheckOrderId(c, req.GetOrderId())
	if err != nil {
		log.ErrorWithCtx(c, "AddChannelTimePresent CheckOrderId error: %+v", err)
		return resp, err
	}

	if !ok {
		log.ErrorWithCtx(c, "AddChannelTimePresent CheckOrderId orderId: %s already exist", req.GetOrderId())
		return resp, nil
	}

	// 分布式阻塞锁
	err = m.cache.LockChannelTimePresent(c, req.GetChannelId())
	if err != nil {
		log.ErrorWithCtx(c, "AddChannelTimePresent LockChannelTimePresent error: %+v", err)
		return resp, err
	}

	defer func() {
		err := m.cache.UnlockChannelTimePresent(c, req.GetChannelId())
		if err != nil {
			log.ErrorWithCtx(c, "AddChannelTimePresent UnlockChannelTimePresent error: %+v", err)
		}
	}()

	nowTs := uint64(time.Now().Unix()) + uint64(m.dyConfig.GetTimePresentFirstPresentDelayTime(req.GetItemId()))

	// 先拿时间礼物配置
	timePresent, err := m.GetTimePresentById(c, req.GetItemId())
	if err != nil {
		log.ErrorWithCtx(c, "AddChannelTimePresent GetTimePresentById error: %+v", err)
		return resp, err
	}

	// 再拿该房间目前的时间礼物队列
	timePresentList, err := m.cache.GetChannelTimePresentFromQueue(c, req.GetChannelId(), int64(nowTs), true)
	if err != nil {
		log.ErrorWithCtx(c, "AddChannelTimePresent GetChannelTimePresentFromQueue error: %+v", err)
		return resp, err
	}

	// 找到最晚的时间
	for _, item := range timePresentList {
		if item.GetEndTime() > nowTs {
			nowTs = item.GetEndTime()
		}
	}

	for _, item := range req.GetTimePresent() {
		timePresentPb := pb.TimePresentItem{
			Id:         m.genTimePresentUuid(),
			ItemId:     req.GetItemId(),
			FromUser:   item.GetFromUser(),
			ToUser:     item.GetToUser(),
			BeginTime:  nowTs,
			EndTime:    nowTs + uint64(timePresent.TimeSecond) + uint64(m.dyConfig.GetTimePresentTransferTime(req.GetItemId())),
			ChannelId:  req.GetChannelId(),
			DuringTime: timePresent.TimeSecond,
			FromUid:    item.GetFromUid(),
			ToUid:      item.GetToUid(),
		}

		// 添加到礼物队列
		err = m.cache.AddChannelTimePresentToQueue(c, &cache.TimePresentItem{
			TimePresentItem: timePresentPb,
			MicReflect:      item.GetMicIdList(),
			TemplateId:      timePresent.MicLayoutTemplateID,
			OrderId:         item.GetOrderId(),
		})
		if err != nil {
			log.ErrorWithCtx(c, "AddChannelTimePresent AddChannelTimePresentToQueue error: %+v", err)
			return resp, err
		}

		nowTs = nowTs + uint64(timePresent.TimeSecond) + uint64(m.dyConfig.GetTimePresentTransferTime(req.GetItemId()))
	}

	// 发时间礼物列表更新推送
	err = m.SendTimePresentListUpdatePush(c, req.GetChannelId(), true) // 发时间礼物列表更新推送

	return resp, nil
}

// GetChannelTimePresentList 获取时间礼物队列
func (m *manager) GetChannelTimePresentList(c context.Context, req *pb.GetChannelTimePresentListReq) (*pb.GetChannelTimePresentListResp, error) {
	resp := &pb.GetChannelTimePresentListResp{}

	nowTs := time.Now().Unix()
	// 获取礼物队列
	timePresentList, err := m.cache.GetChannelTimePresentFromQueue(c, req.GetChannelId(), nowTs, true)
	if err != nil {
		log.ErrorWithCtx(c, "GetChannelTimePresentList GetChannelTimePresentList error: %+v", err)
		return resp, err
	}

	timePresentListResp := make([]*pb.TimePresentItem, 0, len(timePresentList))
	for _, item := range timePresentList {
		timePresentListResp = append(timePresentListResp, &item.TimePresentItem)
	}

	resp.TimePresent = timePresentListResp
	return resp, nil
}

// HandleAllBeginChannelTimePresent 扫描并处理所有开始了的时间礼物
func (m *manager) HandleAllBeginChannelTimePresent(ctx context.Context) {
	timestamp := time.Now().Unix()
	timePresentList, err := m.cache.GetAllBeginChannelTimePresentFromQueue(ctx, timestamp)
	if err != nil {
		log.ErrorWithCtx(ctx, "HandleAllBeginChannelTimePresent GetAllBeginChannelTimePresentFromQueue error: %+v", err)
		return
	}

	for _, timePresent := range timePresentList {
		// 处理时间礼物的开始
		timePresent := timePresent
		go func() {
			log.DebugWithCtx(ctx, "HandleAllBeginChannelTimePresent timePresent: %+v", timePresent)
			if err := m.HandleChannelTimePresentBegin(ctx, timePresent); err != nil {
				log.ErrorWithCtx(ctx, "HandleAllBeginChannelTimePresent HandleChannelTimePresent error: %+v", err)
				return
			}
		}()

		// 稍微等一下，防止并发太多
		time.Sleep(time.Millisecond * 5)
	}

	return
}

// HandleChannelTimePresentBegin 处理时间礼物开始
func (m *manager) HandleChannelTimePresentBegin(ctx context.Context, timePresent *cache.TimePresentItem) error {
	// 移除已经处理的时间礼物
	if err := m.cache.RemoveChannelBeginTimePresentFromQueue(ctx, timePresent); err != nil {
		log.ErrorWithCtx(ctx, "HandleAllBeginChannelTimePresent RemoveChannelTimePresentFromQueue error: %+v", err)
		return err
	}

	fromUserProfile := &ga.UserProfile{}
	err := proto.Unmarshal(timePresent.FromUser, fromUserProfile)
	if err != nil {
		log.ErrorWithCtx(ctx, "SendTimePresentListUpdatePush err , content %+v , err %+v", timePresent, err)
		return err
	}

	toUserProfile := &ga.UserProfile{}
	err = proto.Unmarshal(timePresent.ToUser, toUserProfile)
	if err != nil {
		log.ErrorWithCtx(ctx, "SendTimePresentListUpdatePush err , content %+v , err %+v", timePresent, err)
		return err
	}

	// 额外查一下userProfile，避免已经现身了推的还是假的uid
	realtimeFromUid, realtimeToUid := fromUserProfile.GetUid(), toUserProfile.GetUid()
	realUid := m.youKnowWhoCli.BatchGetMapTrueUidByFake(ctx, []uint32{fromUserProfile.GetUid(), toUserProfile.GetUid()})
	log.DebugWithCtx(ctx, "HandleChannelTimePresentBegin realUid %v", realUid)
	userProfileMap, _ := m.userProfileApiCli.BatchGetUserProfile(ctx, []uint32{realUid[fromUserProfile.GetUid()], realUid[toUserProfile.GetUid()]})
	log.DebugWithCtx(ctx, "HandleChannelTimePresentBegin userProfileMap %v", userProfileMap)
	// 如果现在的跟之前的不同，就用现在的
	if userProfileMap[realUid[fromUserProfile.GetUid()]] != nil {
		realtimeFromUid = userProfileMap[realUid[fromUserProfile.GetUid()]].GetUid()
	}
	if userProfileMap[realUid[toUserProfile.GetUid()]] != nil {
		realtimeToUid = userProfileMap[realUid[toUserProfile.GetUid()]].GetUid()
	}

	mappedMicList := make([]*micLayout.MappedMic, 0)

	nowId := uint32(3)
	for _, item := range timePresent.MicReflect {

		tmpId := nowId
		if item.GetUid() == timePresent.GetFromUid() {
			tmpId = 2
		} else if item.GetUid() == timePresent.GetToUid() {
			tmpId = 1
		} else {
			nowId++
		}

		mappedMicList = append(mappedMicList, &micLayout.MappedMic{
			MicId:    item.GetMicId(),
			MicPosId: tmpId,
		})
	}

	err = m.retryUntilSuccess(ctx, func() protocol.ServerError {
		_, err = m.micLayoutCli.SwitchMicLayoutTemplate(ctx, &micLayout.SwitchMicLayoutTemplateRequest{
			OpUid:               0,
			BizType:             2, // to_do 协议暂时没有
			Cid:                 timePresent.GetChannelId(),
			MicLayoutTemplateId: timePresent.TemplateId,
			MappedMicList:       mappedMicList,
			RelatedUser: &micLayout.RelatedUserInfo{
				Uid:     fromUserProfile.GetUid(),
				Account: fromUserProfile.GetAccount(),
			},
			RelatedToUserList: []*micLayout.RelatedUserInfo{{
				Uid:     toUserProfile.GetUid(),
				Account: toUserProfile.GetAccount(),
			}},
			EventId:             timePresent.OrderId,
			ValidDurationSecond: timePresent.GetDuringTime(),
		})
		if err != nil {
			log.ErrorWithCtx(ctx, "HandleChannelTimePresentEnd SwitchMicLayoutTemplate error: %+v", err)
			return protocol.ToServerError(err)
		}
		return nil
	})

	if err != nil {
		log.ErrorWithCtx(ctx, "HandleChannelTimePresentEnd SwitchMicLayoutTemplate timePresent %v error: %+v", timePresent, err)
		return err
	}

	// 发时间礼物列表更新推送
	err = m.SendTimePresentListUpdatePush(ctx, timePresent.GetChannelId(), true) // 发时间礼物列表更新推送
	if err != nil {
		log.ErrorWithCtx(ctx, "HandleChannelTimePresent SendTimePresentListUpdatePush error: %+v", err)
	}

	// 发公屏推送
	err = m.SendTimePresentListUpdateImPush(ctx, timePresent, true, realtimeFromUid, realtimeToUid)
	if err != nil {
		log.ErrorWithCtx(ctx, "HandleChannelTimePresent SendTimePresentListUpdateImPush error: %+v", err)
	}

	return err
}

// HandleChannelTimePresentEnd 处理时间礼物结束
func (m *manager) HandleChannelTimePresentEnd(ctx context.Context, timePresent *cache.TimePresentItem) error {
	// 先查一下队列里还有没有其他没结束的礼物，没有才要清除布局
	presentList, err := m.cache.GetChannelTimePresentFromQueue(ctx, timePresent.GetChannelId(), time.Now().Unix(), true)
	if err != nil {
		log.ErrorWithCtx(ctx, "HandleChannelTimePresentEnd GetChannelTimePresentFromQueue error: %+v", err)
	}

	fromUserProfile := &ga.UserProfile{}
	err = proto.Unmarshal(timePresent.FromUser, fromUserProfile)
	if err != nil {
		log.ErrorWithCtx(ctx, "SendTimePresentListUpdatePush err , content %+v , err %+v", timePresent, err)
		return err
	}

	toUserProfile := &ga.UserProfile{}
	err = proto.Unmarshal(timePresent.ToUser, toUserProfile)
	if err != nil {
		log.ErrorWithCtx(ctx, "SendTimePresentListUpdatePush err , content %+v , err %+v", timePresent, err)
		return err
	}

	// 额外查一下userProfile，避免已经现身了推的还是假的uid
	realtimeFromUid, realtimeToUid := fromUserProfile.GetUid(), toUserProfile.GetUid()
	realUid := m.youKnowWhoCli.BatchGetMapTrueUidByFake(ctx, []uint32{fromUserProfile.GetUid(), toUserProfile.GetUid()})
	log.DebugWithCtx(ctx, "HandleChannelTimePresentEnd realUid %v", realUid)
	userProfileMap, _ := m.userProfileApiCli.BatchGetUserProfile(ctx, []uint32{realUid[fromUserProfile.GetUid()], realUid[toUserProfile.GetUid()]})
	log.DebugWithCtx(ctx, "HandleChannelTimePresentEnd userProfileMap %v", userProfileMap)
	// 如果现在的跟之前的不同，就用现在的
	if userProfileMap[realUid[fromUserProfile.GetUid()]] != nil {
		realtimeFromUid = userProfileMap[realUid[fromUserProfile.GetUid()]].GetUid()
	}
	if userProfileMap[realUid[toUserProfile.GetUid()]] != nil {
		realtimeToUid = userProfileMap[realUid[toUserProfile.GetUid()]].GetUid()
	}

	if len(presentList) == 0 {
		err := m.retryUntilSuccess(ctx, func() protocol.ServerError {
			_, err := m.micLayoutCli.CloseBizMicLayoutTemplate(ctx, &micLayout.CloseBizMicLayoutTemplateRequest{
				OpUid:   0,
				BizType: 2, // to_do 协议暂时没有
				Cid:     timePresent.GetChannelId(),
			})
			return protocol.ToServerError(err)
		})
		if err != nil {
			log.ErrorWithCtx(ctx, "HandleChannelTimePresentEnd CloseBizMicLayoutTemplate timePresent %v error: %+v", timePresent, err)
			return err
		}
	}

	// 结束这里要先确认成功再移除，防止一直卡在某个地方
	// 移除已经处理的时间礼物
	if err := m.cache.RemoveChannelEndTimePresentFromQueue(ctx, timePresent); err != nil {
		log.ErrorWithCtx(ctx, "HandleAllBeginChannelTimePresent RemoveChannelTimePresentFromQueue error: %+v", err)
		return err
	}

	if len(presentList) == 0 {
		// 发时间礼物列表更新推送
		err = m.SendTimePresentListUpdatePush(ctx, timePresent.GetChannelId(), false) // 发时间礼物列表更新推送
		if err != nil {
			log.ErrorWithCtx(ctx, "HandleChannelTimePresent SendTimePresentListUpdatePush error: %+v", err)
		}
	}

	// 发公屏推送
	err = m.SendTimePresentListUpdateImPush(ctx, timePresent, false, realtimeFromUid, realtimeToUid)
	if err != nil {
		log.ErrorWithCtx(ctx, "HandleChannelTimePresent SendTimePresentListUpdateImPush error: %+v", err)
	}

	return err
}

// SendTimePresentListUpdateImPush 发送时间礼物列表更新推送
func (m *manager) SendTimePresentListUpdateImPush(ctx context.Context, timePresent *cache.TimePresentItem, isBegin bool, realtimeFromUid, realtimeToUid uint32) error {
	fromUserProfile := &ga.UserProfile{}
	err := proto.Unmarshal(timePresent.FromUser, fromUserProfile)
	if err != nil {
		log.ErrorWithCtx(ctx, "SendTimePresentListUpdatePush err , content %+v , err %+v", timePresent, err)
		return err
	}

	toUserProfile := &ga.UserProfile{}
	err = proto.Unmarshal(timePresent.ToUser, toUserProfile)
	if err != nil {
		log.ErrorWithCtx(ctx, "SendTimePresentListUpdatePush err , content %+v , err %+v", timePresent, err)
		return err
	}

	msg := &time_present.TimePresentChangeChannelImMsg{}
	if isBegin {
		msg = &time_present.TimePresentChangeChannelImMsg{
			ChangeMsgSender: &time_present.TimePresentChangeChannelImMsgContent{
				Uid:                realtimeFromUid,
				ChangeMsgText:      fmt.Sprintf(m.dyConfig.GetChannelMsgContent().SenderMsg, truncateString(toUserProfile.GetNickname(), 6)),
				ChangeMsgHighlight: m.dyConfig.GetChannelMsgContent().SenderHighLightMsg,
			},
			ChangeMsgReceiver: &time_present.TimePresentChangeChannelImMsgContent{
				Uid:                realtimeToUid,
				ChangeMsgText:      fmt.Sprintf(m.dyConfig.GetChannelMsgContent().ReceiverMsg, truncateString(fromUserProfile.GetNickname(), 6)),
				ChangeMsgHighlight: m.dyConfig.GetChannelMsgContent().ReceiverHighLightMsg,
			},
			ChangeMsgOthers: &time_present.TimePresentChangeChannelImMsgContent{
				Uid:                0,
				ChangeMsgText:      fmt.Sprintf(m.dyConfig.GetChannelMsgContent().OtherMsg, truncateString(fromUserProfile.GetNickname(), 6), truncateString(toUserProfile.GetNickname(), 6)),
				ChangeMsgHighlight: m.dyConfig.GetChannelMsgContent().OtherHighLightMsg,
			},
			ItemId: timePresent.GetItemId(),
		}
	} else {
		msg = &time_present.TimePresentChangeChannelImMsg{
			ChangeMsgSender: &time_present.TimePresentChangeChannelImMsgContent{
				Uid:                realtimeFromUid,
				ChangeMsgText:      fmt.Sprintf(m.dyConfig.GetChannelMsgEndContent().SenderMsg, truncateString(toUserProfile.GetNickname(), 6)),
				ChangeMsgHighlight: m.dyConfig.GetChannelMsgEndContent().SenderHighLightMsg,
			},
			ChangeMsgReceiver: &time_present.TimePresentChangeChannelImMsgContent{
				Uid:                realtimeToUid,
				ChangeMsgText:      fmt.Sprintf(m.dyConfig.GetChannelMsgEndContent().ReceiverMsg, truncateString(fromUserProfile.GetNickname(), 6)),
				ChangeMsgHighlight: m.dyConfig.GetChannelMsgEndContent().ReceiverHighLightMsg,
			},
			ChangeMsgOthers: &time_present.TimePresentChangeChannelImMsgContent{
				Uid:                0,
				ChangeMsgText:      fmt.Sprintf(m.dyConfig.GetChannelMsgEndContent().OtherMsg, truncateString(fromUserProfile.GetNickname(), 6), truncateString(toUserProfile.GetNickname(), 6)),
				ChangeMsgHighlight: m.dyConfig.GetChannelMsgEndContent().OtherHighLightMsg,
			},
			ItemId: timePresent.GetItemId(),
		}
	}

	MsgContent, _ := proto.Marshal(msg)

	channelMsg := &channelPB_.ChannelBroadcastMsg{
		FromUid:       fromUserProfile.GetUid(),
		FromNick:      fromUserProfile.GetNickname(),
		FromAccount:   fromUserProfile.GetAccount(),
		ToChannelId:   timePresent.GetChannelId(),
		Time:          timePresent.GetBeginTime(),
		Type:          uint32(channelPB_.ChannelMsgType_TIME_PRESENT_CHANGE_CHANNEL_IM_MSG),
		Content:       MsgContent,
		PbOptContent:  MsgContent,
		TargetUid:     toUserProfile.GetUid(),
		TargetNick:    toUserProfile.GetNickname(),
		TargetAccount: toUserProfile.GetAccount(),
	}

	err = m.channelMsgExpressCli.SendChannelBroadcastMsg(ctx, channelMsg)
	if err != nil {
		log.ErrorWithCtx(ctx, "SendTimePresentListUpdatePush SendChannelBroadcastMsg error: %+v", err)
		return err
	}

	log.InfoWithCtx(ctx, "SendTimePresentListUpdatePush success msg: %+v", msg)
	return nil
}

// SendTimePresentListUpdatePush 发送时间礼物列表更新推送
func (m *manager) SendTimePresentListUpdatePush(ctx context.Context, channelId uint32, isBegin bool) error {
	msg := &time_present.TimePresentList{
		Items: make([]*time_present.TimePresent, 0),
	}

	subCtx, cancel := protogrpc.InheritContextWithInfoTimeout(ctx, time.Millisecond*1000)
	defer cancel()

	// 查询该房间的时间礼物列表
	timePresent, err := m.cache.GetChannelTimePresentFromQueue(subCtx, channelId, time.Now().Unix(), true)
	if err != nil {
		log.ErrorWithCtx(subCtx, "SendTimePresentListUpdatePush GetChannelTimePresentFromQueue error: %+v", err)
		return err
	}

	for _, item := range timePresent {
		fromUserProfile := &ga.UserProfile{}
		err := proto.Unmarshal(item.FromUser, fromUserProfile)
		if err != nil {
			log.ErrorWithCtx(subCtx, "SendTimePresentListUpdatePush err , content %+v , err %+v", timePresent, err)
			return err
		}

		toUserProfile := &ga.UserProfile{}
		err = proto.Unmarshal(item.ToUser, toUserProfile)
		if err != nil {
			log.ErrorWithCtx(subCtx, "SendTimePresentListUpdatePush err , content %+v , err %+v", timePresent, err)
			return err
		}

		tmpMsg := &time_present.TimePresent{
			Id:         item.GetId(),
			ItemId:     item.GetItemId(),
			FromUser:   fromUserProfile,
			ToUser:     toUserProfile,
			BeginTime:  item.GetBeginTime(),
			EndTime:    item.GetEndTime(),
			DuringTime: item.GetDuringTime(),
		}

		msg.Items = append(msg.Items, tmpMsg)
	}

	MsgContent, _ := proto.Marshal(msg)

	channelMsg := &channelPB_.ChannelBroadcastMsg{
		ToChannelId:  channelId,
		Time:         uint64(time.Now().Unix()),
		Type:         uint32(channelPB_.ChannelMsgType_TIME_PRESENT_LIST_CHANGE_MSG),
		Content:      MsgContent,
		PbOptContent: MsgContent,
	}

	err = m.channelMsgExpressCli.SendChannelBroadcastMsg(subCtx, channelMsg)
	if err != nil {
		log.ErrorWithCtx(subCtx, "SendTimePresentListUpdatePush SendChannelBroadcastMsg error: %+v", err)
		return err
	}

	log.InfoWithCtx(subCtx, "SendTimePresentListUpdatePush success msg: %+v", msg)
	return nil
}

// HandleAllEndChannelTimePresent 扫描并处理所有结束了的时间礼物
func (m *manager) HandleAllEndChannelTimePresent(ctx context.Context) {
	timestamp := time.Now().Unix()
	timePresentList, err := m.cache.GetAllEndChannelTimePresentFromQueue(ctx, timestamp)
	if err != nil {
		log.ErrorWithCtx(ctx, "HandleAllEndChannelTimePresent GetAllEndChannelTimePresentFromQueue error: %+v", err)
		return
	}

	for _, timePresent := range timePresentList {
		// 处理时间礼物的结束
		timePresent := timePresent
		go func() {
			log.DebugWithCtx(ctx, "HandleAllEndChannelTimePresent timePresent: %+v", timePresent)
			if err := m.HandleChannelTimePresentEnd(ctx, timePresent); err != nil {
				log.ErrorWithCtx(ctx, "HandleAllEndChannelTimePresent HandleChannelTimePresent error: %+v", err)
				return
			}
		}()

		// 稍微等一下，防止并发太多
		time.Sleep(time.Millisecond * 5)
	}

	return
}

// ClearChannelTimePresentQueue 清除时间礼物队列
func (m *manager) ClearChannelTimePresentQueue(ctx context.Context, channelId uint32) error {
	log.InfoWithCtx(ctx, "ClearChannelTimePresentQueue req %d", channelId)
	// 先查一下队列里没有结束的礼物，没有才要清除布局
	presentList, err := m.cache.GetChannelTimePresentFromQueue(ctx, channelId, time.Now().Unix(), true)
	if err != nil {
		log.ErrorWithCtx(ctx, "HandleChannelTimePresentEnd GetChannelTimePresentFromQueue error: %+v", err)
	}

	for _, item := range presentList {
		log.DebugWithCtx(ctx, "presentList item %+v", item)
	}

	err = m.cache.ClearChannelTimePresentQueue(ctx, channelId, presentList)
	if err != nil {
		log.ErrorWithCtx(ctx, "ClearChannelTimePresentQueue ClearChannelTimePresentQueue error: %+v", err)
		return err
	}

	err = m.retryUntilSuccess(ctx, func() protocol.ServerError {
		_, err := m.micLayoutCli.CloseBizMicLayoutTemplate(ctx, &micLayout.CloseBizMicLayoutTemplateRequest{
			OpUid:   0,
			BizType: 2, // to_do 协议暂时没有
			Cid:     channelId,
		})
		return protocol.ToServerError(err)
	})

	if err != nil {
		log.ErrorWithCtx(ctx, "ClearChannelTimePresentList CloseBizMicLayoutTemplate error: %+v", err)
		return err
	}

	return nil
}

// genTimePresentUuid 生成时间礼物uuid，只是简单做个标记，即使重复了也没有太大问题
func (m *manager) genTimePresentUuid() uint32 {
	return uint32(m.randSource.Int63() & 0x7fffffff)
}

// 重试输入的函数直到成功
func (m *manager) retryUntilSuccess(ctx context.Context, f func() protocol.ServerError) error {
	var err protocol.ServerError
	for i := 0; i < 10; i++ {
		err = f()
		if err == nil {
			log.DebugWithCtx(ctx, "retryUntilSuccess success , ctx %d func %v", ctx, f)
			return nil
		}
		if err.Code() == status.ErrChannelMicLayoutUnsupportSchemeDetailType || err.Code() == status.ErrChannelMicLayoutSwitchTemplateInvalidErr ||
			err.Code() == status.ErrChannelMicLayoutParamInvalid || err.Code() == status.ErrChannelMicLayoutUnsupportSchemeDetailType {
			log.ErrorWithCtx(ctx, "retryUntilSuccess error: %+v , no retry ", err)
			return err
		}
		log.ErrorWithCtx(ctx, "retryUntilSuccess error: %+v", err)
	}

	_ = m.reporter.SendError(fmt.Sprintf("时间礼物切换房间模版重试后仍然失败，错误信息：%v", err))
	return err
}

func truncateString(s string, maxLen int) string {
	runes := []rune(s) // 将字符串转换为 rune 切片
	if len(runes) > maxLen {
		return string(runes[:(maxLen-1)]) + "..."
	}
	return s
}
