package server

import (
	"context"
	"reflect"
	"testing"
	"time"

	"bou.ke/monkey"
	"gitlab.ttyuyin.com/avengers/tyr/compatible/proto"
	"github.com/opentracing/opentracing-go"
	"golang.52tt.com/clients/account"
	channelapigo "golang.52tt.com/clients/channelapi-go"
	"golang.52tt.com/clients/channelol"
	"golang.52tt.com/clients/cooldown"
	"golang.52tt.com/clients/nobility"
	perfect_match "golang.52tt.com/clients/perfect-match"
	youknowwho "golang.52tt.com/clients/you-know-who"
	youknowwhowrite "golang.52tt.com/clients/you-know-who-write"
	"golang.52tt.com/pkg/bylink"
	"golang.52tt.com/pkg/config"
	"golang.52tt.com/pkg/protocol"
	protogrpc "golang.52tt.com/pkg/protocol/grpc"
	"golang.52tt.com/pkg/store/redis"
	tracing "golang.52tt.com/pkg/tracing/jaeger"
	"golang.52tt.com/protocol/app"
	"golang.52tt.com/protocol/app/youknowwhologic"
	channelolpb "golang.52tt.com/protocol/services/channelol"
	nobilitypd "golang.52tt.com/protocol/services/nobilitysvr"
	perfect_matchPb "golang.52tt.com/protocol/services/perfect-match"
	youknowwhopb "golang.52tt.com/protocol/services/youknowwho"
	youknowwhowritepb "golang.52tt.com/protocol/services/youknowwho/youknowwhowrite"
	"golang.52tt.com/services/you-know-who-logic/cache"
	"golang.52tt.com/services/you-know-who-logic/clients"
	"golang.52tt.com/services/you-know-who-logic/conf"
	"google.golang.org/grpc"
)

func TestYouKnowWhologic__ChangeUKWSwitch(t *testing.T) {
	ctxNew := context.Background()
	ctx := protogrpc.WithServiceInfo(ctxNew, &protogrpc.ServiceInfo{UserID: 12345})

	monkey.PatchInstanceMethod(reflect.TypeOf(&conf.SvrDynamic{}), "GetSvrConf", func(*conf.SvrDynamic) *conf.ServiceConfig {
		return &conf.ServiceConfig{
			NewVersionSwitch: false,
			VersionLimit:     false,
		}
	})

	monkey.PatchInstanceMethod(reflect.TypeOf(&conf.UKWWhitelistDynamic{}), "GetUKWWhitelistMap", func(s *conf.UKWWhitelistDynamic) map[uint32]*conf.WhiteConfig {
		confMap := make(map[uint32]*conf.WhiteConfig)
		return confMap
	})

	monkey.PatchInstanceMethod(reflect.TypeOf(&channelol.Client{}), "GetUsersChannelId", func(c *channelol.Client,
		ctx context.Context, uin, targetUid uint32) (*channelolpb.UserChannelId, protocol.ServerError) {
		return &channelolpb.UserChannelId{
			ChannelId: 0,
		}, nil
	})

	monkey.PatchInstanceMethod(reflect.TypeOf(&youknowwho.Client{}), "GetUKWInfo", func(c *youknowwho.Client,
		ctx context.Context, uid uint32) (*youknowwhopb.UKWInfo, protocol.ServerError) {
		return &youknowwhopb.UKWInfo{
			Uid: 12345,
			UkwPermissionInfo: &youknowwhopb.UKWPermissionInfo{
				FakeUid:    66666,
				Status:     1,
				Switch:     0,
				ExpireTime: **********,
			},
			UkwPersonInfo: &youknowwhopb.UKWPersonInfo{
				FakeUid:  66666,
				Nickname: "神秘人66666",
				Account:  "ukw66666",
			},
		}, nil
	})

	monkey.Patch(time.Now, func() time.Time {
		t := time.Unix(**********, 0)
		return t
	})

	monkey.PatchInstanceMethod(reflect.TypeOf(&nobility.Client{}), "GetNobilityInfo", func(c *nobility.Client,
		ctx context.Context, uid uint32, queryRecords bool) (*nobilitypd.NobilityInfo, protocol.ServerError) {
		return &nobilitypd.NobilityInfo{
			Invisible: false,
		}, nil
	})

	monkey.PatchInstanceMethod(reflect.TypeOf(&cooldown.Client{}), "CheckNx", func(c *cooldown.Client,
		ctx context.Context, uid uint32, key string, ttl int32) (bool, error) {
		return false, nil
	})

	monkey.PatchInstanceMethod(reflect.TypeOf(&youknowwhowrite.Client{}), "UserChangeUKWStatus", func(c *youknowwhowrite.Client,
		ctx context.Context, uid uint32, switchTy youknowwhowritepb.SwitchType) (*youknowwhowritepb.ChangeStatusResp, protocol.ServerError) {
		return &youknowwhowritepb.ChangeStatusResp{}, nil
	})
	monkey.PatchInstanceMethod(reflect.TypeOf(&youknowwho.Client{}), "UserChangeUKWStatus", func(c *youknowwho.Client,
		ctx context.Context, uid uint32, switchTy youknowwhopb.UKWSwitchType) (*youknowwhopb.UserChangeUKWStatusResp, protocol.ServerError) {
		return &youknowwhopb.UserChangeUKWStatusResp{}, nil
	})

	monkey.PatchInstanceMethod(reflect.TypeOf(&nobility.Client{}), "SetInvisibleStatus", func(c *nobility.Client,
		ctx context.Context, uid uint32, status int32) (*nobilitypd.SetInvisibleStatusResp, protocol.ServerError) {
		return &nobilitypd.SetInvisibleStatusResp{}, nil
	})

	monkey.PatchInstanceMethod(reflect.TypeOf(&perfect_match.Client{}), "GetPerfectMatchEntry", func(c *perfect_match.Client,
		ctx context.Context, req *perfect_matchPb.GetPerfectMatchEntryReq, opts ...grpc.CallOption) (*perfect_matchPb.GetPerfectMatchEntryResp, error) {
		return &perfect_matchPb.GetPerfectMatchEntryResp{
			Phase: uint32(perfect_matchPb.GetPerfectMatchEntryResp_PHASE_UNSPECIFIED),
		}, nil
	})

	type fields struct {
	}
	type args struct {
		ctx context.Context
		in  *youknowwhologic.ChangeUKWSwitchReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name:   "测试旧版本切换开关",
			fields: fields{},
			args: args{
				ctx: ctx,
				in: &youknowwhologic.ChangeUKWSwitchReq{
					Status:    1,
					ChannelId: 13579,
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &YouKnowWhologic_{}
			_, err := s.ChangeUKWSwitch(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("ChangeUKWSwitch() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}

func TestYouKnowWhologic__ChangeUKWSwitchV2(t *testing.T) {
	ctx := context.Background()

	monkey.PatchInstanceMethod(reflect.TypeOf(&youknowwho.Client{}), "GetUKWInfo", func(c *youknowwho.Client,
		ctx context.Context, uid uint32) (*youknowwhopb.UKWInfo, protocol.ServerError) {
		return &youknowwhopb.UKWInfo{
			Uid: 12345,
			UkwPermissionInfo: &youknowwhopb.UKWPermissionInfo{
				FakeUid:    66666,
				Status:     1,
				Switch:     0,
				ExpireTime: **********,
			},
			UkwPersonInfo: &youknowwhopb.UKWPersonInfo{
				FakeUid:  66666,
				Nickname: "神秘人66666",
				Account:  "ukw66666",
			},
		}, nil
	})

	monkey.Patch(time.Now, func() time.Time {
		t := time.Unix(**********, 0)
		return t
	})

	monkey.PatchInstanceMethod(reflect.TypeOf(&channelol.Client{}), "GetUsersChannelId", func(c *channelol.Client,
		ctx context.Context, uin, targetUid uint32) (*channelolpb.UserChannelId, protocol.ServerError) {
		return &channelolpb.UserChannelId{
			ChannelId: 0,
		}, nil
	})

	monkey.PatchInstanceMethod(reflect.TypeOf(&conf.SvrDynamic{}), "GetSvrConf", func(*conf.SvrDynamic) *conf.ServiceConfig {
		return &conf.ServiceConfig{
			NewVersionSwitch: false,
			VersionLimit:     false,
		}
	})

	monkey.PatchInstanceMethod(reflect.TypeOf(&youknowwhowrite.Client{}), "UserChangeUKWStatus", func(c *youknowwhowrite.Client,
		ctx context.Context, uid uint32, switchTy youknowwhowritepb.SwitchType) (*youknowwhowritepb.ChangeStatusResp, protocol.ServerError) {
		return &youknowwhowritepb.ChangeStatusResp{}, nil
	})
	monkey.PatchInstanceMethod(reflect.TypeOf(&youknowwho.Client{}), "UserChangeUKWStatus", func(c *youknowwho.Client,
		ctx context.Context, uid uint32, switchTy youknowwhopb.UKWSwitchType) (*youknowwhopb.UserChangeUKWStatusResp, protocol.ServerError) {
		return &youknowwhopb.UserChangeUKWStatusResp{}, nil
	})

	monkey.PatchInstanceMethod(reflect.TypeOf(&perfect_match.Client{}), "GetPerfectMatchEntry", func(c *perfect_match.Client,
		ctx context.Context, req *perfect_matchPb.GetPerfectMatchEntryReq, opts ...grpc.CallOption) (*perfect_matchPb.GetPerfectMatchEntryResp, error) {
		return &perfect_matchPb.GetPerfectMatchEntryResp{
			Phase: uint32(perfect_matchPb.GetPerfectMatchEntryResp_PHASE_UNSPECIFIED),
		}, nil
	})

	type fields struct {
	}
	type args struct {
		ctx           context.Context
		uid           uint32
		in            *youknowwhologic.ChangeUKWSwitchReq
		clientType    uint16
		clientVersion uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name:   "新版本切换神秘人开关",
			fields: fields{},
			args: args{
				ctx: ctx,
				uid: 123456,
				in: &youknowwhologic.ChangeUKWSwitchReq{
					Status:    1,
					ChannelId: 0,
				},
				clientType:    1,
				clientVersion: 0,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &YouKnowWhologic_{}
			_, err := s.ChangeUKWSwitchV2(tt.args.ctx, tt.args.uid, tt.args.in, tt.args.clientType, tt.args.clientVersion)
			if (err != nil) != tt.wantErr {
				t.Errorf("ChangeUKWSwitchV2() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}

func TestYouKnowWhologic__UserChangeUKWEnterNotice(t *testing.T) {
	ctxNew := context.Background()
	ctx := protogrpc.WithServiceInfo(ctxNew, &protogrpc.ServiceInfo{UserID: 12345})

	monkey.PatchInstanceMethod(reflect.TypeOf(&channelol.Client{}), "GetUsersChannelId", func(c *channelol.Client,
		ctx context.Context, uin, targetUid uint32) (*channelolpb.UserChannelId, protocol.ServerError) {
		return &channelolpb.UserChannelId{
			ChannelId: 0,
		}, nil
	})

	monkey.PatchInstanceMethod(reflect.TypeOf(&youknowwho.Client{}), "GetUKWInfo", func(c *youknowwho.Client,
		ctx context.Context, uid uint32) (*youknowwhopb.UKWInfo, protocol.ServerError) {
		return &youknowwhopb.UKWInfo{
			Uid: 12345,
			UkwPermissionInfo: &youknowwhopb.UKWPermissionInfo{
				FakeUid:    66666,
				Status:     1,
				Switch:     0,
				ExpireTime: **********,
			},
			UkwPersonInfo: &youknowwhopb.UKWPersonInfo{
				FakeUid:  66666,
				Nickname: "神秘人66666",
				Account:  "ukw66666",
			},
		}, nil
	})

	monkey.Patch(time.Now, func() time.Time {
		t := time.Unix(**********, 0)
		return t
	})

	monkey.PatchInstanceMethod(reflect.TypeOf(&nobility.Client{}), "GetNobilityInfo", func(c *nobility.Client,
		ctx context.Context, uid uint32, queryRecords bool) (*nobilitypd.NobilityInfo, protocol.ServerError) {
		return &nobilitypd.NobilityInfo{
			Invisible: false,
		}, nil
	})

	monkey.PatchInstanceMethod(reflect.TypeOf(&cooldown.Client{}), "CheckNx", func(c *cooldown.Client,
		ctx context.Context, uid uint32, key string, ttl int32) (bool, error) {
		return false, nil
	})

	monkey.PatchInstanceMethod(reflect.TypeOf(&conf.SvrDynamic{}), "GetSvrConf", func(*conf.SvrDynamic) *conf.ServiceConfig {
		return &conf.ServiceConfig{}
	})
	monkey.PatchInstanceMethod(reflect.TypeOf(&youknowwhowrite.Client{}), "UserChangeUKWEnterNotice", func(c *youknowwhowrite.Client,
		ctx context.Context, uid uint32, switchTy youknowwhowritepb.SwitchType) (*youknowwhowritepb.ChangeEnterNoticeResp, protocol.ServerError) {
		return &youknowwhowritepb.ChangeEnterNoticeResp{
			EnterNoticeSwitch: youknowwhowritepb.SwitchType_SWITCH_ON,
		}, nil
	})
	monkey.PatchInstanceMethod(reflect.TypeOf(&youknowwho.Client{}), "UserChangeUKWEnterNotice", func(c *youknowwho.Client,
		ctx context.Context, uid uint32, switchTy youknowwhopb.UKWSwitchType) (*youknowwhopb.UserChangeUKWEnterNoticeResp, protocol.ServerError) {
		return &youknowwhopb.UserChangeUKWEnterNoticeResp{
			EnterNoticeSwitch: youknowwhopb.UKWSwitchType_UKW_SWITCH_ON,
		}, nil
	})

	monkey.PatchInstanceMethod(reflect.TypeOf(&nobility.Client{}), "SetInvisibleStatus", func(c *nobility.Client,
		ctx context.Context, uid uint32, status int32) (*nobilitypd.SetInvisibleStatusResp, protocol.ServerError) {
		return &nobilitypd.SetInvisibleStatusResp{}, nil
	})

	type fields struct {
	}
	type args struct {
		ctx context.Context
		in  *youknowwhologic.UserChangeUKWEnterNoticeReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    youknowwhologic.UKWSwitchType
		wantErr bool
	}{
		{
			name:   "测试切换进房提醒开关",
			fields: fields{},
			args: args{
				ctx: ctx,
				in: &youknowwhologic.UserChangeUKWEnterNoticeReq{
					EnterNoticeSwitch: 1,
				},
			},
			want:    youknowwhologic.UKWSwitchType_UKW_SWITCH_ON,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &YouKnowWhologic_{}
			got, err := s.UserChangeUKWEnterNotice(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("UserChangeUKWEnterNotice() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got.EnterNoticeSwitch, tt.want) {
				t.Errorf("UserChangeUKWEnterNotice() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestYouKnowWhologic__GetUKWInfo(t *testing.T) {
	ctxNew := context.Background()
	ctx := protogrpc.WithServiceInfo(ctxNew, &protogrpc.ServiceInfo{UserID: 12345})

	monkey.PatchInstanceMethod(reflect.TypeOf(&youknowwho.Client{}), "GetUKWInfo", func(c *youknowwho.Client,
		ctx context.Context, uid uint32) (*youknowwhopb.UKWInfo, protocol.ServerError) {
		return &youknowwhopb.UKWInfo{
			Uid: 12345,
			UkwPermissionInfo: &youknowwhopb.UKWPermissionInfo{
				FakeUid:     66666,
				Nickname:    "神秘人66666",
				Status:      1,
				Switch:      1,
				ExpireTime:  **********,
				ServerTime:  **********,
				Level:       1,
				RankSwitch:  0,
				EnterNotice: 1,
			},
			UkwPersonInfo: &youknowwhopb.UKWPersonInfo{
				FakeUid:            66666,
				Nickname:           "神秘人66666",
				Account:            "ukw66666",
				Medal:              "medal",
				Level:              1,
				HeadFrame:          "headFrame",
				EnterEffect:        "enterEffect",
				EnterEffectVersion: "enterEffectVersion",
				HeadWear:           "headWear",
				FollowImg:          "followImg",
			},
		}, nil
	})

	monkey.PatchInstanceMethod(reflect.TypeOf(&youknowwho.Client{}), "GetUKWConfInfo", func(c *youknowwho.Client,
		ctx context.Context, marketId uint32, os uint32) (*youknowwhopb.GetUKWConfInfoResp, protocol.ServerError) {
		return &youknowwhopb.GetUKWConfInfoResp{
			Medal:              "medal",
			HeadFrame:          "headFrame",
			EnterEffect:        "enterEffect",
			EnterEffectVersion: "enterEffectVersion",
			HeadWear:           "headWear",
			FollowImg:          "followImg",
			UserMedal:          "userMedal",
			UserMedalGray:      "userMedalGray",
			BuyPageUrl:         "buyPageUrl",
			NobilityUrl:        "nobilityUrl",
		}, nil
	})

	monkey.PatchInstanceMethod(reflect.TypeOf(&conf.SvrDynamic{}), "GetMedalConf", func(*conf.SvrDynamic) *conf.MedalConf {
		return &conf.MedalConf{
			ExpiredNotice: "神秘人套餐已过期啦，前往续费",
			RenewNotice:   "神秘人套餐有效期仅剩%+v天，前往续费",
		}
	})

	result := &youknowwhologic.UKWInfo{
		Uid: 12345,
		UkwPermissionInfo: &youknowwhologic.UKWPermissionInfo{
			FakeUid:           66666,
			Nickname:          "神秘人66666",
			Status:            1,
			Switch:            1,
			ExpireTime:        **********,
			ServerTime:        **********,
			Level:             1,
			RankSwitch:        0,
			IsOpen:            youknowwhologic.UKWOpenStatus_UKW_ON,
			EnterNoticeSwitch: youknowwhologic.UKWSwitchType_UKW_SWITCH_ON,
			FakeAccount:       "ukw66666",
		},
		UkwPersonInfo: &youknowwhologic.UKWPersonInfo{
			Nickname:  "神秘人66666",
			Account:   "ukw66666",
			Medal:     "medal",
			Level:     1,
			HeadFrame: "headFrame",
		},
		UkwMedal: &youknowwhologic.UKWMedal{
			Type:       youknowwhologic.MedalType_MEDAL_TYPE_WEAR,
			MedalUrl:   "userMedal",
			JumpUrl:    "buyPageUrl",
			NeedNotice: false,
			Notice:     "",
			IsExpired:  false,
		},
	}

	type fields struct {
	}
	type args struct {
		ctx context.Context
		in  *youknowwhologic.GetUKWInfoReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *youknowwhologic.UKWInfo
		wantErr bool
	}{
		{
			name:   "获取神秘人信息",
			fields: fields{},
			args: args{
				ctx: ctx,
				in:  &youknowwhologic.GetUKWInfoReq{},
			},
			want:    result,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &YouKnowWhologic_{}
			got, err := s.GetUKWInfo(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetUKWInfo() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got.UkwInfo, tt.want) {
				t.Errorf("GetUKWInfo() got = %v, want %v", got.UkwInfo, tt.want)
			}
		})
	}
}

func TestYouKnowWhologic__GetUKWUserProfile(t *testing.T) {
	ctxNew := context.Background()
	ctx := protogrpc.WithServiceInfo(ctxNew, &protogrpc.ServiceInfo{UserID: 12345,
		ClientVersion: protocol.FormatClientVersion(6, 16, 0), ClientType: protocol.ClientTypeANDROID})

	monkey.PatchInstanceMethod(reflect.TypeOf(&youknowwho.Client{}), "GetTrueUidByFake", func(c *youknowwho.Client,
		ctx context.Context, uid uint32) (uint32, protocol.ServerError) {
		return 12345, nil
	})

	monkey.PatchInstanceMethod(reflect.TypeOf(&account.Client{}), "GetUser", func(c *account.Client,
		ctx context.Context, uid uint32) (*account.User, protocol.ServerError) {
		return &account.User{
			Username: "TT1234556",
			Nickname: "测试昵称",
			Alias:    "Alias",
			Sex:      1,
		}, nil
	})

	monkey.PatchInstanceMethod(reflect.TypeOf(&youknowwho.Client{}), "GetUKWInfo", func(c *youknowwho.Client,
		ctx context.Context, uid uint32) (*youknowwhopb.UKWInfo, protocol.ServerError) {
		return &youknowwhopb.UKWInfo{
			Uid: 12345,
			UkwPermissionInfo: &youknowwhopb.UKWPermissionInfo{
				FakeUid:     66666,
				Nickname:    "神秘人66666",
				Status:      1,
				Switch:      1,
				ExpireTime:  **********,
				ServerTime:  **********,
				Level:       1,
				RankSwitch:  0,
				EnterNotice: 1,
			},
			UkwPersonInfo: &youknowwhopb.UKWPersonInfo{
				FakeUid:            66666,
				Nickname:           "神秘人66666",
				Account:            "ukw66666",
				Medal:              "medal",
				Level:              1,
				HeadFrame:          "headFrame",
				EnterEffect:        "enterEffect",
				EnterEffectVersion: "enterEffectVersion",
				HeadWear:           "headWear",
				FollowImg:          "followImg",
			},
		}, nil
	})

	monkey.PatchInstanceMethod(reflect.TypeOf(&conf.SvrDynamic{}), "GetMedalConf", func(*conf.SvrDynamic) *conf.MedalConf {
		return &conf.MedalConf{
			ExpiredNotice: "神秘人套餐已过期啦，前往续费",
			RenewNotice:   "神秘人套餐有效期仅剩%+v天，前往续费",
		}
	})

	monkey.PatchInstanceMethod(reflect.TypeOf(&youknowwho.Client{}), "GetUKWConfInfo", func(c *youknowwho.Client,
		ctx context.Context, marketId uint32, os uint32) (*youknowwhopb.GetUKWConfInfoResp, protocol.ServerError) {
		return &youknowwhopb.GetUKWConfInfoResp{
			Medal:              "medal",
			HeadFrame:          "headFrame",
			EnterEffect:        "enterEffect",
			EnterEffectVersion: "enterEffectVersion",
			HeadWear:           "headWear",
			FollowImg:          "followImg",
			UserMedal:          "userMedal",
			UserMedalGray:      "userMedalGray",
			BuyPageUrl:         "buyPageUrl",
			NobilityUrl:        "nobilityUrl",
		}, nil
	})

	monkey.PatchInstanceMethod(reflect.TypeOf(&channelol.Client{}), "GetUsersChannelId", func(c *channelol.Client,
		ctx context.Context, uin, targetUid uint32) (*channelolpb.UserChannelId, protocol.ServerError) {
		return &channelolpb.UserChannelId{
			ChannelId: 10086,
		}, nil
	})

	// 组装信息结构体
	msg := &app.UserUKWInfo{
		Level:     1,
		Medal:     "medal",
		HeadFrame: "headFrame",
	}
	content, errMarshal := proto.Marshal(msg)
	if errMarshal != nil {
		t.Error("proto.Marshal err=", errMarshal)
	}

	result1 := &app.UserProfile{
		Uid:          66666,
		Account:      "ukw66666",
		Nickname:     "神秘人66666",
		AccountAlias: "Alias",
		Sex:          1,
		Privilege: &app.UserPrivilege{
			Nickname: "神秘人66666",
			Account:  "ukw66666",
			Type:     1,
			Options:  content,
		},
	}
	result2 := &app.UserUKWInfo{
		Level:     1,
		Medal:     "medal",
		HeadFrame: "headFrame",
	}
	result3 := &youknowwhologic.UKWMedal{
		Type:       youknowwhologic.MedalType_MEDAL_TYPE_WEAR,
		MedalUrl:   "userMedal",
		JumpUrl:    "buyPageUrl",
		NeedNotice: true,
		Notice:     "神秘人套餐有效期仅剩3天，前往续费",
		IsExpired:  false,
	}

	type fields struct {
	}
	type args struct {
		ctx context.Context
		in  *youknowwhologic.GetUKWUserProfileReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want1   *app.UserProfile
		want2   *app.UserUKWInfo
		want3   *youknowwhologic.UKWMedal
		wantErr bool
	}{
		{
			name:   "测试获取神秘人客态资料卡信息",
			fields: fields{},
			args: args{
				ctx: ctx,
				in: &youknowwhologic.GetUKWUserProfileReq{
					Uid: 66666,
				},
			},
			want1:   result1,
			want2:   result2,
			want3:   result3,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &YouKnowWhologic_{}
			got, err := s.GetUKWUserProfile(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetUKWUserProfile() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got.GetUserProfile(), tt.want1) {
				t.Errorf("GetUKWUserProfile() got = %v, want %v", got.UserProfile, tt.want1)
			}
			if !reflect.DeepEqual(got.GetUserUkwInfo(), tt.want2) {
				t.Errorf("GetUKWUserProfile() got = %v, want %v", got.GetUserUkwInfo(), tt.want2)
			}
			if !reflect.DeepEqual(got.GetUkwMedal(), tt.want3) {
				t.Errorf("GetUKWUserProfile() got = %v, want %v", got.GetUkwMedal(), tt.want3)
			}
		})
	}
}

func TestYouKnowWhologic__ChangeRankSwitch(t *testing.T) {
	ctxNew := context.Background()
	ctx := protogrpc.WithServiceInfo(ctxNew, &protogrpc.ServiceInfo{UserID: 12345})

	monkey.PatchInstanceMethod(reflect.TypeOf(&channelol.Client{}), "GetUsersChannelId", func(c *channelol.Client,
		ctx context.Context, uin, targetUid uint32) (*channelolpb.UserChannelId, protocol.ServerError) {
		return &channelolpb.UserChannelId{
			ChannelId: 0,
		}, nil
	})

	monkey.PatchInstanceMethod(reflect.TypeOf(&youknowwho.Client{}), "GetUKWInfo", func(c *youknowwho.Client,
		ctx context.Context, uid uint32) (*youknowwhopb.UKWInfo, protocol.ServerError) {
		return &youknowwhopb.UKWInfo{
			Uid: 12345,
			UkwPermissionInfo: &youknowwhopb.UKWPermissionInfo{
				FakeUid:     66666,
				Nickname:    "神秘人66666",
				Status:      1,
				Switch:      1,
				ExpireTime:  **********,
				ServerTime:  **********,
				Level:       1,
				RankSwitch:  0,
				EnterNotice: 1,
			},
			UkwPersonInfo: &youknowwhopb.UKWPersonInfo{
				FakeUid:            66666,
				Nickname:           "神秘人66666",
				Account:            "ukw66666",
				Medal:              "medal",
				Level:              1,
				HeadFrame:          "headFrame",
				EnterEffect:        "enterEffect",
				EnterEffectVersion: "enterEffectVersion",
				HeadWear:           "headWear",
				FollowImg:          "followImg",
			},
		}, nil
	})

	monkey.Patch(time.Now, func() time.Time {
		t := time.Unix(**********, 0)
		return t
	})

	monkey.PatchInstanceMethod(reflect.TypeOf(&conf.SvrDynamic{}), "GetSvrConf", func(*conf.SvrDynamic) *conf.ServiceConfig {
		return &conf.ServiceConfig{}
	})
	monkey.PatchInstanceMethod(reflect.TypeOf(&youknowwhowrite.Client{}), "UserChangeRankSwitch", func(c *youknowwhowrite.Client,
		ctx context.Context, uid uint32, switchTy youknowwhowritepb.RankType) (*youknowwhowritepb.ChangeRankSwitchResp, protocol.ServerError) {
		return &youknowwhowritepb.ChangeRankSwitchResp{
			RankSwitch: youknowwhowritepb.RankType_RANK_ON,
		}, nil
	})
	monkey.PatchInstanceMethod(reflect.TypeOf(&youknowwho.Client{}), "UserChangeRankSwitch", func(c *youknowwho.Client,
		ctx context.Context, uid uint32, switchTy youknowwhopb.RankSwitchType) (*youknowwhopb.UserChangeRankSwitchResp, protocol.ServerError) {
		return &youknowwhopb.UserChangeRankSwitchResp{
			RankSwitch: youknowwhopb.RankSwitchType_UKW_RANK_SWITCH_ON,
		}, nil
	})

	type fields struct {
	}
	type args struct {
		ctx context.Context
		in  *youknowwhologic.ChangeRankSwitchReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantOut youknowwhologic.RankSwitchType
		wantErr bool
	}{
		{
			name:   "切换神秘人排行榜开关",
			fields: fields{},
			args: args{
				ctx: ctx,
				in: &youknowwhologic.ChangeRankSwitchReq{
					RankSwitch: youknowwhologic.RankSwitchType_UKW_RANK_SWITCH_ON,
				},
			},
			wantOut: youknowwhologic.RankSwitchType_UKW_RANK_SWITCH_ON,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &YouKnowWhologic_{}
			gotOut, err := s.ChangeRankSwitch(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("ChangeRankSwitch() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(gotOut.RankSwitch, tt.wantOut) {
				t.Errorf("ChangeRankSwitch() gotOut = %v, want %v", gotOut.RankSwitch, tt.wantOut)
			}
		})
	}
}

func TestYouKnowWhologic__ExposureUKW(t *testing.T) {
	ctxNew := context.Background()
	ctx := protogrpc.WithServiceInfo(ctxNew, &protogrpc.ServiceInfo{UserID: 12345})

	monkey.PatchInstanceMethod(reflect.TypeOf(&cache.YouKnowWhoLogicCache{}), "GetExposureLock", func(c *cache.YouKnowWhoLogicCache,
		ctx context.Context, uid uint32) (int64, error) {
		return 0, nil
	})

	monkey.PatchInstanceMethod(reflect.TypeOf(&youknowwho.Client{}), "GetUKWInfo", func(c *youknowwho.Client,
		ctx context.Context, uid uint32) (*youknowwhopb.UKWInfo, protocol.ServerError) {
		return &youknowwhopb.UKWInfo{
			Uid: 12345,
			UkwPermissionInfo: &youknowwhopb.UKWPermissionInfo{
				FakeUid:     66666,
				Nickname:    "神秘人66666",
				Status:      1,
				Switch:      1,
				ExpireTime:  **********,
				ServerTime:  **********,
				Level:       1,
				RankSwitch:  0,
				EnterNotice: 1,
			},
			UkwPersonInfo: &youknowwhopb.UKWPersonInfo{
				FakeUid:            66666,
				Nickname:           "神秘人66666",
				Account:            "ukw66666",
				Medal:              "medal",
				Level:              1,
				HeadFrame:          "headFrame",
				EnterEffect:        "enterEffect",
				EnterEffectVersion: "enterEffectVersion",
				HeadWear:           "headWear",
				FollowImg:          "followImg",
			},
		}, nil
	})

	monkey.PatchInstanceMethod(reflect.TypeOf(&conf.SvrDynamic{}), "GetSvrConf", func(*conf.SvrDynamic) *conf.ServiceConfig {
		return &conf.ServiceConfig{}
	})
	monkey.PatchInstanceMethod(reflect.TypeOf(&youknowwhowrite.Client{}), "ExposureUKW", func(c *youknowwhowrite.Client,
		ctx context.Context, uid uint32) protocol.ServerError {
		return nil
	})
	monkey.PatchInstanceMethod(reflect.TypeOf(&youknowwho.Client{}), "ExposureUKW", func(c *youknowwho.Client,
		ctx context.Context, uid uint32) protocol.ServerError {
		return nil
	})

	monkey.PatchInstanceMethod(reflect.TypeOf(&cache.YouKnowWhoLogicCache{}), "SetExposureLock", func(c *cache.YouKnowWhoLogicCache,
		ctx context.Context, uid uint32, expireTime int64) error {
		return nil
	})

	monkey.PatchInstanceMethod(reflect.TypeOf(&channelol.Client{}), "GetUsersChannelId", func(c *channelol.Client,
		ctx context.Context, uin, targetUid uint32) (*channelolpb.UserChannelId, protocol.ServerError) {
		return &channelolpb.UserChannelId{
			ChannelId: 13579,
		}, nil
	})

	monkey.PatchInstanceMethod(reflect.TypeOf(&account.Client{}), "GetUser", func(c *account.Client,
		ctx context.Context, uid uint32) (*account.User, protocol.ServerError) {
		return &account.User{
			Username: "TT1234556",
			Nickname: "测试昵称",
			Alias:    "Alias",
			Sex:      1,
		}, nil
	})

	monkey.Patch(clients.PushReliableChannelMsg, func(ctx context.Context, msgBin []byte, content string, cmd uint32, channelId uint32) error {
		return nil
	})

	monkey.PatchInstanceMethod(reflect.TypeOf(&channelapigo.Client{}), "HoldMicPush", func(c *channelapigo.Client,
		ctx context.Context, channelId, holdMicUid, opSource uint32) protocol.ServerError {
		return nil
	})

	type fields struct {
	}
	type args struct {
		ctx context.Context
		in  *youknowwhologic.ExposureUKWReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name:   "神秘人现身",
			fields: fields{},
			args: args{
				ctx: ctx,
				in:  &youknowwhologic.ExposureUKWReq{},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &YouKnowWhologic_{}
			_, err := s.ExposureUKW(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("ExposureUKW() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}

func TestNewYouKnowWhologic_(t *testing.T) {

	ctx := context.Background()
	ctx = context.WithValue(ctx, "configfile", "test")

	monkey.PatchInstanceMethod(reflect.TypeOf(&conf.ServiceConfigT{}), "Parse", func(sc *conf.ServiceConfigT,
		configFile string) (err error) {
		return nil
	})

	monkey.Patch(redis.NewClient, func(cfg *config.RedisConfig, opts ...redis.Option) *redis.Client {
		return &redis.Client{}
	})

	monkey.Patch(tracing.Init, func(serviceName string) opentracing.Tracer {
		return nil
	})

	monkey.PatchInstanceMethod(reflect.TypeOf(&conf.ServiceConfigT{}), "GetRedisConfig", func(sc *conf.ServiceConfigT,
	) *config.RedisConfig {
		return &config.RedisConfig{}
	})

	monkey.Patch(cache.NewYouKnowWhoCache, func(r *redis.Client, tracer opentracing.Tracer) (*cache.YouKnowWhoLogicCache, error) {
		return &cache.YouKnowWhoLogicCache{}, nil
	})

	monkey.Patch(conf.Setup, func() error {
		return nil
	})

	monkey.Patch(bylink.NewKfkCollector, func() (bylink.Bylink, error) {
		return nil, nil
	})

	monkey.Patch(bylink.InitGlobalCollector, func(b bylink.Bylink) {})

	type args struct {
		ctx    context.Context
		config config.Configer
		tracer opentracing.Tracer
	}
	tests := []struct {
		name    string
		args    args
		want    *YouKnowWhologic_
		wantErr bool
	}{
		{
			name: "NewYouKnowWhologic_",
			args: args{
				ctx:    ctx,
				config: nil,
				tracer: nil,
			},
			want: &YouKnowWhologic_{
				sc:         &conf.ServiceConfigT{},
				cacheStore: &cache.YouKnowWhoLogicCache{},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := NewYouKnowWhologic_(tt.args.ctx, tt.args.config, tt.args.tracer)
			if (err != nil) != tt.wantErr {
				t.Errorf("NewYouKnowWhologic_() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("NewYouKnowWhologic_() got = %v, want %v", got, tt.want)
			}
		})
	}
}
