package model

import (
	"context"
	"errors"
	"fmt"
	"sync"
	"time"

	"github.com/scylladb/go-set/u32set"
	pres_v2 "golang.52tt.com/clients/presence/v2"
	push "golang.52tt.com/clients/push-notification/v2"
	"golang.52tt.com/pkg/config"
	"golang.52tt.com/pkg/log"
	ttproto "golang.52tt.com/pkg/protocol"
	"golang.52tt.com/protocol/app/auth"
	ttpush "golang.52tt.com/protocol/app/push"
	pushpb "golang.52tt.com/protocol/services/push-notification/v2"
	"google.golang.org/grpc"
	"gopkg.in/redis.v5"
)

var (
	_defaultLogManager LogManager

	errNotInitialized = errors.New("not initialized")

	presV2Client     *pres_v2.Client
	oncePresV2Client sync.Once
)

type UploadLogCallbackFunc func(ctx context.Context, in *auth.UploadLogReq)

func SetDefaultLogManager(m LogManager) {
	_defaultLogManager = m
}

type LogConfig struct {
	DeferUploadRedis    *config.RedisConfig `yaml:"deferUploadRedis"`
	AcceptTerminalTypes []uint32            `yaml:"acceptTerminalTypes"`
}

func getPresClient() *pres_v2.Client {
	oncePresV2Client.Do(func() {
		presV2Client, _ = pres_v2.NewClient(grpc.WithBlock())
	})
	return presV2Client
}

type LogManager interface {
	PushUploadLogClaimToUser(ctx context.Context, uid uint32, start, end string, force bool, pullCtx []byte, originalCmd string) (int, error)

	RegisterCallbackFunc(fn UploadLogCallbackFunc)
	UploadLogCallback(ctx context.Context, in *auth.UploadLogReq)
}

type logManager struct {
	conf *LogConfig

	deferUploadRedisClient *redis.Client
	pushClient             *push.Client

	callbacks []UploadLogCallbackFunc
}

func NewLogManager(c *LogConfig) LogManager {
	pc, _ := push.NewClient()

	m := &logManager{
		conf:       c,
		pushClient: pc,
		deferUploadRedisClient: redis.NewClient(&redis.Options{
			Addr: c.DeferUploadRedis.Addr(),
		}),
	}

	log.InfoWithCtx(context.TODO(), "Ping defer upload redis %s: %v", c.DeferUploadRedis.Addr(), m.deferUploadRedisClient.Ping().Err())

	return m
}

func (m *logManager) PushUploadLogClaimToUser(ctx context.Context, uid uint32, start, end string, force bool, pullCtx []byte, originalCmd string) (int, error) {
	claim := &ttpush.PullLogMessage{
		OriginalCommand: originalCmd,
		PullLogContext:  pullCtx,
		Force:           force,
	}
	if len(start) > 0 {
		claim.StartTime = start
	}
	if len(end) > 0 {
		claim.EndTime = end
	}

	err := m.saveDeferUploadLogClaim(ctx, uid, claim)
	log.InfoWithCtx(ctx, "saveDeferUploadLogClaim %d %v fail: %v", uid, claim, err)
	if err != nil {
		return 0, err
	}

	acceptTerminalTypes := u32set.New(m.conf.AcceptTerminalTypes...)

	/*
			presList, err := rpc.PresClient.GetPresence(ctx, uid) //nolint:golint,staticcheck
		if err != nil {
					return 0, err
		}
		pushPresList := make([]*pushpb.Presence, 0)
		for _, pres := range presList {
			if !acceptTerminalTypes.Has(pres.TerminalType) {
				log.InfoWithCtx(ctx, "skip presence %v", pres)
				continue
			}
			pushPresList = append(pushPresList, &pushpb.Presence{
				ProxyIp:      pres.ProxyIp,
				ProxyPort:    pres.ProxyPort,
				Uid:          pres.Uid,
				ClientId:     pres.ClientId,
				TerminalType: pres.TerminalType,
			})
		}
	*/
	presV2List, errV2 := getPresClient().GetUserPres(ctx, uid)
	if errV2 != nil {
		return 0, errV2
	}
	pushPresV2List := make([]*pushpb.Presence, 0)
	for _, presV2 := range presV2List.InfoList {
		if !acceptTerminalTypes.Has(presV2.Val.Terminal) {
			log.InfoWithCtx(ctx, "skip presenceV2 %v", presV2)
			continue
		}
		pushPresV2List = append(pushPresV2List, &pushpb.Presence{
			ProxyIp:      presV2.Key.ProxyIp,
			ProxyPort:    presV2.Key.ProxyPort,
			Uid:          presV2.Key.UserId,
			ClientId:     presV2.Key.ClientId,
			TerminalType: presV2.Val.Terminal,
		})
	}

	ts := uint32(time.Now().Unix())
	pushMessage := &ttpush.PushMessage{
		Cmd:   uint32(ttpush.PushMessage_PULL_LOG),
		SeqId: ts,
	}
	pushMessage.Content, _ = claim.Marshal()

	proxyNoti := &pushpb.ProxyNotification{
		Type:       uint32(pushpb.ProxyNotification_PUSH),
		ExpireTime: 3600,
		PushLabel:  push.LabelSakuraBot,
	}
	proxyNoti.Payload, _ = pushMessage.Marshal()

	noti := &pushpb.CompositiveNotification{
		AppId:    uint32(ttproto.TT),
		Sequence: ts,
		TerminalTypeList: []uint32{
			ttproto.MobileAndroidTT, ttproto.MobileIPhoneTT, ttproto.WindowsTT,
		},
		ProxyNotification: proxyNoti,
		ClientTimestamp:   ts,
	}

	resp, err := m.pushClient.PushToPresenceList(ctx, pushPresV2List, noti)
	log.InfoWithCtx(ctx, "push to user %d: %v %v", uid, resp, err)
	if err != nil {
		return 0, err
	}
	return len(pushPresV2List), nil
}

func (m *logManager) UploadLogCallback(ctx context.Context, in *auth.UploadLogReq) {
	defer func() {
		log.InfoWithCtx(ctx, "deleteAllDeferUploadClaimForUid %d", in.Uid)
		m.deleteAllDeferUploadClaimForUid(ctx, in.Uid)
	}()

	for _, cb := range m.callbacks {
		cb(ctx, in)
	}
}

func (m *logManager) RegisterCallbackFunc(fn UploadLogCallbackFunc) {
	m.callbacks = append(m.callbacks, fn)
}

const (
	uidZSetKey         = "Wait_PullLogUser_ZSet"
	claimHMapKeyPrefix = "Wait_PullLogUser_HMap_"
	ttlKeyPrefix       = "Wait_PullLogUser_TTL_KV_"
)

func (m *logManager) saveDeferUploadLogClaim(ctx context.Context, uid uint32, claim *ttpush.PullLogMessage) error {
	now := time.Now().Unix()
	_, err := m.deferUploadRedisClient.TxPipelined(func(pl *redis.Pipeline) error {
		pl.ZAdd(uidZSetKey, redis.Z{Member: uid, Score: float64(now)})
		var (
			key      = fmt.Sprintf("%s%d", claimHMapKeyPrefix, uid)
			field    = fmt.Sprintf("%d_%d", now, uid)
			value, _ = claim.Marshal()
		)

		pl.HSet(key, field, value)
		pl.Set(fmt.Sprintf("%s%d", ttlKeyPrefix, uid), "5", 0)
		return nil
	})
	return err
}

func (m *logManager) deleteAllDeferUploadClaimForUid(ctx context.Context, uid uint32) error {
	_, err := m.deferUploadRedisClient.TxPipelined(func(pl *redis.Pipeline) error {
		pl.ZRem(uidZSetKey, uid)
		pl.Del(fmt.Sprintf("%s%d", claimHMapKeyPrefix, uid))
		pl.Del(fmt.Sprintf("%s%d", ttlKeyPrefix, uid))
		return nil
	})
	return err
}

func PushUploadLogClaimToUser(ctx context.Context, uid uint32, start, end string, force bool, pullCtx []byte, originalCmd string) (int, error) {
	if _defaultLogManager == nil {
		return 0, errNotInitialized
	}
	return _defaultLogManager.PushUploadLogClaimToUser(ctx, uid, start, end, force, pullCtx, originalCmd)
}

func UploadLogCallback(ctx context.Context, in *auth.UploadLogReq) {
	_defaultLogManager.UploadLogCallback(ctx, in)
}

func RegisterCallbackFunc(fn UploadLogCallbackFunc) {
	_defaultLogManager.RegisterCallbackFunc(fn)
}
