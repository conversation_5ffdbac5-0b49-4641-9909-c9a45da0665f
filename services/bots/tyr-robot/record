2022-12-06 17:36:46 start:
serviceName: music-topic-channel-subscriber  issueKey: AYRbEX4KQgS5eBhcSx13  errInfo: SA4006: this value of `relationItem` is never used
serviceName: client-conf-mgr  issueKey: AYM02SPb3YHuYDVT3jDV  errInfo: Refactor this piece of code to not have any dead code after this "return".
serviceName: client-conf-mgr  issueKey: AYM02SPT3YHuYDVT3jDQ  errInfo: S1028: should use fmt.Errorf(...) instead of errors.New(fmt.Sprintf(...))
serviceName: client-conf-mgr  issueKey: AYTDQBRlQgS5eBhcHPbA  errInfo: S1028: should use fmt.Errorf(...) instead of errors.New(fmt.Sprintf(...))
serviceName: client-conf-mgr  issueKey: AYTDQBRlQgS5eBhcHPbB  errInfo: S1028: should use fmt.Errorf(...) instead of errors.New(fmt.Sprintf(...))
serviceName: client-conf-mgr  issueKey: AYM02SPT3YHuYDVT3jDR  errInfo: S1028: should use fmt.Errorf(...) instead of errors.New(fmt.Sprintf(...))
serviceName: client-conf-mgr  issueKey: AYQUe5oAQgS5eBhcP8zE  errInfo: S1028: should use fmt.Errorf(...) instead of errors.New(fmt.Sprintf(...))
serviceName: client-conf-mgr  issueKey: AYM02SPb3YHuYDVT3jDW  errInfo: unreachable: unreachable code
serviceName: super-player-svr  issueKey: AYRQIYZUQgS5eBhcEQZX  errInfo: lostcancel: the cancel function returned by context.WithTimeout should be called, not discarded, to avoid a context leak
serviceName: super-player-svr  issueKey: AYSkjDI6QgS5eBhck6jJ  errInfo: SA1029: should not use built-in type string as key for value; define your own type to avoid collisions
serviceName: super-player-svr  issueKey: AYNjLYfR3YHuYDVTeuBd  errInfo: SA1029: should not use built-in type string as key for value; define your own type to avoid collisions
serviceName: super-player-svr  issueKey: AYNjLYfR3YHuYDVTeuBe  errInfo: SA1029: should not use built-in type string as key for value; define your own type to avoid collisions
serviceName: super-player-svr  issueKey: AYNjLYfR3YHuYDVTeuBf  errInfo: SA1029: should not use built-in type string as key for value; define your own type to avoid collisions
serviceName: super-player-svr  issueKey: AYSkjDI6QgS5eBhck6jK  errInfo: SA1029: should not use built-in type string as key for value; define your own type to avoid collisions
serviceName: super-player-svr  issueKey: AYSkjDI6QgS5eBhck6jL  errInfo: SA1029: should not use built-in type string as key for value; define your own type to avoid collisions
serviceName: super-player-svr  issueKey: AYSkjDI6QgS5eBhck6jM  errInfo: SA1029: should not use built-in type string as key for value; define your own type to avoid collisions
serviceName: super-player-svr  issueKey: AYSkjDI6QgS5eBhck6jN  errInfo: SA1029: should not use built-in type string as key for value; define your own type to avoid collisions
serviceName: super-player-svr  issueKey: AYSkjDI6QgS5eBhck6jO  errInfo: SA1029: should not use built-in type string as key for value; define your own type to avoid collisions
serviceName: super-player-svr  issueKey: AYNjLYfR3YHuYDVTeuBc  errInfo: SA4009(related information): assignment to err
serviceName: super-player-svr  issueKey: AYSkjDI6QgS5eBhck6jP  errInfo: SA1029: should not use built-in type string as key for value; define your own type to avoid collisions
serviceName: super-player-svr  issueKey: AYSkjDI6QgS5eBhck6jQ  errInfo: SA1029: should not use built-in type string as key for value; define your own type to avoid collisions
serviceName: super-player-svr  issueKey: AYSkjDI6QgS5eBhck6jR  errInfo: SA1029: should not use built-in type string as key for value; define your own type to avoid collisions
serviceName: super-player-svr  issueKey: AYSkjDI6QgS5eBhck6jS  errInfo: SA1029: should not use built-in type string as key for value; define your own type to avoid collisions
serviceName: super-player-svr  issueKey: AYSkjDI6QgS5eBhck6jT  errInfo: SA1029: should not use built-in type string as key for value; define your own type to avoid collisions
serviceName: super-player-svr  issueKey: AYSkjDI6QgS5eBhck6jU  errInfo: SA1029: should not use built-in type string as key for value; define your own type to avoid collisions
serviceName: super-player-svr  issueKey: AYSkjDI6QgS5eBhck6jW  errInfo: SA1029: should not use built-in type string as key for value; define your own type to avoid collisions
serviceName: super-player-svr  issueKey: AYSkjDI6QgS5eBhck6jX  errInfo: SA1029: should not use built-in type string as key for value; define your own type to avoid collisions
serviceName: super-player-svr  issueKey: AYSkjDI6QgS5eBhck6jV  errInfo: SA1029: should not use built-in type string as key for value; define your own type to avoid collisions
serviceName: super-player-svr  issueKey: AYNjLYfR3YHuYDVTeuBX  errInfo: lostcancel: the cancel function returned by context.WithTimeout should be called, not discarded, to avoid a context leak
serviceName: super-player-svr  issueKey: AYSkjDI6QgS5eBhck6i5  errInfo: lostcancel: the cancel function returned by context.WithTimeout should be called, not discarded, to avoid a context leak
serviceName: super-player-svr  issueKey: AYSkjDI6QgS5eBhck6i6  errInfo: lostcancel: the cancel function returned by context.WithTimeout should be called, not discarded, to avoid a context leak
serviceName: super-player-svr  issueKey: AYSkjDI6QgS5eBhck6i7  errInfo: lostcancel: the cancel function returned by context.WithTimeout should be called, not discarded, to avoid a context leak
serviceName: super-player-svr  issueKey: AYSkjDI6QgS5eBhck6i8  errInfo: lostcancel: the cancel function returned by context.WithTimeout should be called, not discarded, to avoid a context leak
serviceName: super-player-svr  issueKey: AYSkjDI6QgS5eBhck6i9  errInfo: lostcancel: the cancel function returned by context.WithTimeout should be called, not discarded, to avoid a context leak
serviceName: super-player-svr  issueKey: AYSkjDI6QgS5eBhck6i-  errInfo: lostcancel: the cancel function returned by context.WithTimeout should be called, not discarded, to avoid a context leak
serviceName: super-player-svr  issueKey: AYSkjDI6QgS5eBhck6i_  errInfo: lostcancel: the cancel function returned by context.WithTimeout should be called, not discarded, to avoid a context leak
serviceName: super-player-svr  issueKey: AYSkjDI6QgS5eBhck6jA  errInfo: lostcancel: the cancel function returned by context.WithTimeout should be called, not discarded, to avoid a context leak
serviceName: super-player-svr  issueKey: AYSkjDI6QgS5eBhck6jB  errInfo: lostcancel: the cancel function returned by context.WithTimeout should be called, not discarded, to avoid a context leak
serviceName: super-player-svr  issueKey: AYSkjDI6QgS5eBhck6jC  errInfo: lostcancel: the cancel function returned by context.WithTimeout should be called, not discarded, to avoid a context leak
serviceName: super-player-svr  issueKey: AYSkjDI6QgS5eBhck6jD  errInfo: lostcancel: the cancel function returned by context.WithTimeout should be called, not discarded, to avoid a context leak
serviceName: super-player-svr  issueKey: AYSkjDI6QgS5eBhck6jE  errInfo: lostcancel: the cancel function returned by context.WithTimeout should be called, not discarded, to avoid a context leak
serviceName: super-player-svr  issueKey: AYNjLYfR3YHuYDVTeuBb  errInfo: SA4009: argument err is overwritten before first use
serviceName: super-player-svr  issueKey: AYRQIYZUQgS5eBhcEQZW  errInfo: lostcancel: the cancel function returned by context.WithTimeout should be called, not discarded, to avoid a context leak
serviceName: avatar-http-logic-hy  issueKey: AYQUckeSQgS5eBhcPpZ3  errInfo: Decode not declared by package tools
serviceName: avatar-http-logic-hy  issueKey: AYQUckeSQgS5eBhcPpZ1  errInfo: PCM not declared by package tools
serviceName: avatar-http-logic-hy  issueKey: AYQUckeSQgS5eBhcPpZ2  errInfo: MP3 not declared by package tools
serviceName: tel-call-logic  issueKey: AYQUm0mqQgS5eBhcREwT  errInfo: unreachable: unreachable code
serviceName: channel-open-game-record  issueKey: AYQUeIWzQgS5eBhcP3pc  errInfo: S1011: should replace loop with `values = append(values, strList...)`
serviceName: dark-checker  issueKey: AYQUfhp_QgS5eBhcQBrE  errInfo: S1036: unnecessary guard around map access
serviceName: auto-award  issueKey: AYSEJxdeQgS5eBhc3eAk  errInfo: SA4006: this value of `err` is never used
serviceName: auto-award  issueKey: AYSEJxdeQgS5eBhc3eAl  errInfo: SA4006: this value of `err` is never used
serviceName: apicenter-go  issueKey: AYPAxstLwyc6c63XOqpe  errInfo: lostcancel: the cancel function returned by context.WithTimeout should be called, not discarded, to avoid a context leak
serviceName: apicenter-go  issueKey: AYPQIECXwyc6c63XZgkz  errInfo: SA4010: this result of append is never used, except maybe in other appends
serviceName: apicenter-go  issueKey: AYM1dKoX3YHuYDVT3sSs  errInfo: S1005: unnecessary assignment to the blank identifier
serviceName: apicenter-go  issueKey: AYM1dKoT3YHuYDVT3sSr  errInfo: SA4006: this value of `err` is never used
serviceName: apicenter-go  issueKey: AYM1dKnn3YHuYDVT3sSh  errInfo: SA4010: this result of append is never used, except maybe in other appends
serviceName: apicenter-go  issueKey: AYM1dKnG3YHuYDVT3sSR  errInfo: S1011: should replace loop with `chIdList = append(chIdList, whiteResp.GetChannelIdList()...)`
serviceName: apicenter-go  issueKey: AYM1dKnG3YHuYDVT3sSW  errInfo: SA4006: this value of `serverErr` is never used
serviceName: apicenter-go  issueKey: AYM1dKnG3YHuYDVT3sSX  errInfo: SA4006: this value of `serverErr` is never used
serviceName: apicenter-go  issueKey: AYM1dKnG3YHuYDVT3sSS  errInfo: unreachable: unreachable code
serviceName: apicenter-go  issueKey: AYP5cvbcQgS5eBhc-80C  errInfo: SA4006: this value of `err` is never used
serviceName: apicenter-go  issueKey: AYP5cvbcQgS5eBhc-80B  errInfo: SA4006: this value of `err` is never used
serviceName: apicenter-go  issueKey: AYQH2t-JQgS5eBhcBTAs  errInfo: S1011: should replace loop with `uidList = append(uidList, in.GetUidList()...)`
serviceName: apicenter-go  issueKey: AYM1dKnG3YHuYDVT3sSQ  errInfo: S1011: should replace loop with `chIdList = append(chIdList, in.GetChannelIdList()...)`
serviceName: channel-live-ranking  issueKey: AYMdBZmx3YHuYDVTz8RU  errInfo: S1031: unnecessary nil check around range
serviceName: avatar-http-logic  issueKey: AYNp0rkZwyc6c63XsWa8  errInfo: Decode not declared by package tools
serviceName: avatar-http-logic  issueKey: AYNp0rkZwyc6c63XsWa6  errInfo: PCM not declared by package tools
serviceName: avatar-http-logic  issueKey: AYNp0rkZwyc6c63XsWa7  errInfo: MP3 not declared by package tools
serviceName: channel-msg-express  issueKey: AYM8DcTs3YHuYDVT-3rI  errInfo: S1011: should replace loop with `r.ToChannelIdList = append(r.ToChannelIdList, rule.ToChannelIdList...)`
serviceName: unclaimed  issueKey: AYQUoYa_QgS5eBhcRRso  errInfo: SA4006: this value of `err` is never used
serviceName: unclaimed  issueKey: AYQUoYbFQgS5eBhcRRsq  errInfo: response body must be closed
serviceName: channelguild-go  issueKey: AYN4fdp9wyc6c63XvlZQ  errInfo: lostcancel: the cancel function returned by context.WithTimeout should be called, not discarded, to avoid a context leak
serviceName: channelguild-go  issueKey: AYN4fdp9wyc6c63XvlZR  errInfo: lostcancel: the cancel function returned by context.WithTimeout should be called, not discarded, to avoid a context leak
serviceName: channelguild-go  issueKey: AYN4fdp9wyc6c63XvlZS  errInfo: lostcancel: the cancel function returned by context.WithTimeout should be called, not discarded, to avoid a context leak
serviceName: ugc-friendship  issueKey: AYRb6_jxQgS5eBhcUNpQ  errInfo: Consider preallocating `needSupervisionUidList`
serviceName: channel-room  issueKey: AYQUel2zQgS5eBhcP6J9  errInfo: SA1015: using time.Tick leaks the underlying ticker, consider using it only in endless functions, tests and the main package, and use time.NewTicker here
serviceName: channel-room  issueKey: AYQUel2zQgS5eBhcP6J3  errInfo: S1012: should use `time.Since` instead of `time.Now().Sub`
serviceName: concert  issueKey: AYSjrrTmQgS5eBhcjMCC  errInfo: SA4021: x = append(y) is equivalent to x = y
serviceName: concert  issueKey: AYQOQgEPQgS5eBhcIuIB  errInfo: SA5008: duplicate struct tag "json"
serviceName: channel-live-fans  issueKey: AYSEJzQrQgS5eBhc3eZN  errInfo: "golang.52tt.com/services/knight-group/knight-group-logic/client" imported but not used as clientX
serviceName: channel-live-fans  issueKey: AYQOFY8sQgS5eBhcII4y  errInfo: Consider preallocating `tmpSortAddedGroupInfo`
serviceName: super-channel-logic  issueKey: AYOC0z-9wyc6c63X5Jjo  errInfo: S1028: should use fmt.Errorf(...) instead of errors.New(fmt.Sprintf(...))
serviceName: super-channel-logic  issueKey: AYOC0z-Owyc6c63X5Jjg  errInfo: SA4024: builtin function len does not return negative values
serviceName: super-channel-logic  issueKey: AYOC0z98wyc6c63X5Jja  errInfo: SA9003: empty branch
serviceName: anchorcontract-go  issueKey: AYS-CWBSQgS5eBhc8aN2  errInfo: m.channelCli undefined (type *AnchorContractMgr has no field or method channelCli)
serviceName: anchorcontract-go  issueKey: AYS-CWBEQgS5eBhc8aNx  errInfo: m.apiCenterCli undefined (type *AnchorContractMgr has no field or method apiCenterCli)
serviceName: anchorcontract-go  issueKey: AYS-CWBEQgS5eBhc8aNy  errInfo: FollowPublicReq_SYSTEM not declared by package apicenter
serviceName: anchorcontract-go  issueKey: AYS-CWBEQgS5eBhc8aNz  errInfo: m.apiCenterCli undefined (type *AnchorContractMgr has no field or method apiCenterCli)
serviceName: anchorcontract-go  issueKey: AYS-CWBEQgS5eBhc8aN0  errInfo: FollowPublicReq_SYSTEM not declared by package apicenter
serviceName: anchorcontract-go  issueKey: AYS-CWARQgS5eBhc8aNv  errInfo: m.apiCenterCli undefined (type *AnchorContractMgr has no field or method apiCenterCli)
serviceName: anchorcontract-go  issueKey: AYS-CWARQgS5eBhc8aNw  errInfo: FollowPublicReq_SYSTEM not declared by package apicenter
serviceName: anchorcontract-go  issueKey: AYQ3yMkYQgS5eBhc1hsS  errInfo: "github.com/go-sql-driver/mysql" imported but not used as mysql2
serviceName: anchorcontract-go  issueKey: AYQ3yMjTQgS5eBhc1hsJ  errInfo: ParentGuildInfo not declared by package anchorcontract_go
serviceName: anchorcontract-go  issueKey: AYRVLAlBQgS5eBhcLsu-  errInfo: ParentGuildInfo not declared by package anchorcontract_go
serviceName: anchorcontract-go  issueKey: AYQ3yMjTQgS5eBhc1hsL  errInfo: ParentGuildInfo not declared by package anchorcontract_go
serviceName: hobby-channel  issueKey: AYMbVUSp3YHuYDVTxZJ2  errInfo: S1005: unnecessary assignment to the blank identifier
serviceName: hobby-channel  issueKey: AYMbVUSK3YHuYDVTxZJw  errInfo: SA4006: this value of `cert` is never used
serviceName: channel-level  issueKey: AYOIhPF9wyc6c63X_UWt  errInfo: SA4003: no value of type uint32 is less than 0
serviceName: channel-level  issueKey: AYOIhPFcwyc6c63X_UWl  errInfo: SA9003: empty branch
serviceName: avatar  issueKey: AYQNQtp6QgS5eBhcGCqx  errInfo: composites: `go.mongodb.org/mongo-driver/bson/primitive.E` composite literal uses unkeyed fields
serviceName: avatar  issueKey: AYTDNv9-QgS5eBhcGzZ7  errInfo: composites: `go.mongodb.org/mongo-driver/bson/primitive.E` composite literal uses unkeyed fields
serviceName: avatar  issueKey: AYQNQtp6QgS5eBhcGCqv  errInfo: composites: `go.mongodb.org/mongo-driver/bson/primitive.E` composite literal uses unkeyed fields
serviceName: avatar  issueKey: AYQNQtp6QgS5eBhcGCqw  errInfo: composites: `go.mongodb.org/mongo-driver/bson/primitive.E` composite literal uses unkeyed fields
serviceName: official-live-channel  issueKey: AYMdBZxG3YHuYDVTz8ez  errInfo: SA4006: this value of `err` is never used
serviceName: chat-card-logic  issueKey: AYQdP2mvQgS5eBhcbJ3x  errInfo: S1012: should use `time.Since` instead of `time.Now().Sub`
serviceName: sakura-bot  issueKey: AYQN__MmQgS5eBhcHdt9  errInfo: "golang.52tt.com/protocol/services/logicsvr-go/bots" imported but not used as logic_bots
serviceName: sing-a-round  issueKey: AYM1dM713YHuYDVT3s1N  errInfo: S1001: should use copy() instead of a loop
serviceName: sing-a-round  issueKey: AYTDWYWIQgS5eBhcIX3A  errInfo: composites: `go.mongodb.org/mongo-driver/bson/primitive.E` composite literal uses unkeyed fields
serviceName: sing-a-round  issueKey: AYTDWYWIQgS5eBhcIX3B  errInfo: composites: `go.mongodb.org/mongo-driver/bson/primitive.E` composite literal uses unkeyed fields
serviceName: sing-a-round  issueKey: AYTDWYWOQgS5eBhcIX3C  errInfo: composites: `go.mongodb.org/mongo-driver/bson/primitive.E` composite literal uses unkeyed fields
serviceName: sing-a-round  issueKey: AYTDWYWOQgS5eBhcIX3F  errInfo: composites: `go.mongodb.org/mongo-driver/bson/primitive.E` composite literal uses unkeyed fields
serviceName: sing-a-round  issueKey: AYTDWYWOQgS5eBhcIX3G  errInfo: composites: `go.mongodb.org/mongo-driver/bson/primitive.E` composite literal uses unkeyed fields
serviceName: sing-a-round  issueKey: AYTDWYWOQgS5eBhcIX3D  errInfo: composites: `go.mongodb.org/mongo-driver/bson/primitive.E` composite literal uses unkeyed fields
serviceName: sing-a-round  issueKey: AYTDWYWOQgS5eBhcIX3E  errInfo: composites: `go.mongodb.org/mongo-driver/bson/primitive.E` composite literal uses unkeyed fields
serviceName: sing-a-round  issueKey: AYTDWYWOQgS5eBhcIX3H  errInfo: composites: `go.mongodb.org/mongo-driver/bson/primitive.E` composite literal uses unkeyed fields
serviceName: sing-a-round  issueKey: AYTDWYWOQgS5eBhcIX3I  errInfo: composites: `go.mongodb.org/mongo-driver/bson/primitive.E` composite literal uses unkeyed fields
serviceName: sing-a-round  issueKey: AYTDWYWIQgS5eBhcIX2-  errInfo: composites: `go.mongodb.org/mongo-driver/bson/primitive.E` composite literal uses unkeyed fields
serviceName: sing-a-round  issueKey: AYTDWYWIQgS5eBhcIX2_  errInfo: composites: `go.mongodb.org/mongo-driver/bson/primitive.E` composite literal uses unkeyed fields
serviceName: sing-a-round  issueKey: AYM1dM8C3YHuYDVT3s1P  errInfo: composites: `go.mongodb.org/mongo-driver/bson/primitive.E` composite literal uses unkeyed fields
serviceName: sing-a-round  issueKey: AYM1dM8C3YHuYDVT3s1Q  errInfo: composites: `go.mongodb.org/mongo-driver/bson/primitive.E` composite literal uses unkeyed fields
serviceName: sing-a-round  issueKey: AYM1dM8C3YHuYDVT3s1O  errInfo: S1011: should replace loop with `idToSong = append(idToSong, songs...)`
serviceName: sing-a-round  issueKey: AYM1dM7V3YHuYDVT3s1H  errInfo: SA4010: this result of append is never used, except maybe in other appends
serviceName: sing-a-round  issueKey: AYM1dM5Z3YHuYDVT3s04  errInfo: SA4010: this result of append is never used, except maybe in other appends
serviceName: sing-a-round  issueKey: AYM1dM5Z3YHuYDVT3s05  errInfo: SA4010: this result of append is never used, except maybe in other appends
serviceName: sing-a-round  issueKey: AYTDWYWaQgS5eBhcIX3L  errInfo: composites: `go.mongodb.org/mongo-driver/bson/primitive.E` composite literal uses unkeyed fields
serviceName: sing-a-round  issueKey: AYM1dM8C3YHuYDVT3s1R  errInfo: composites: `go.mongodb.org/mongo-driver/bson/primitive.E` composite literal uses unkeyed fields
serviceName: sing-a-round  issueKey: AYTDWYWIQgS5eBhcIX29  errInfo: composites: `go.mongodb.org/mongo-driver/bson/primitive.E` composite literal uses unkeyed fields
serviceName: sing-a-round  issueKey: AYM1dM7i3YHuYDVT3s1L  errInfo: SA9003: empty branch
serviceName: sing-a-round  issueKey: AYTDWYWIQgS5eBhcIX28  errInfo: composites: `go.mongodb.org/mongo-driver/bson/primitive.E` composite literal uses unkeyed fields
serviceName: sing-a-round  issueKey: AYTDWYWaQgS5eBhcIX3J  errInfo: composites: `go.mongodb.org/mongo-driver/bson/primitive.E` composite literal uses unkeyed fields
serviceName: sing-a-round  issueKey: AYM1dM8K3YHuYDVT3s1T  errInfo: S1025: the argument's underlying type is a slice of bytes, should use a simple conversion instead of fmt.Sprintf
serviceName: sing-a-round  issueKey: AYTDWYWaQgS5eBhcIX3K  errInfo: composites: `go.mongodb.org/mongo-driver/bson/primitive.E` composite literal uses unkeyed fields
serviceName: sing-a-round  issueKey: AYM1dM6_3YHuYDVT3s1D  errInfo: SA4006: this value of `err` is never used
serviceName: sing-a-round  issueKey: AYTDWYWIQgS5eBhcIX26  errInfo: composites: `go.mongodb.org/mongo-driver/bson/primitive.E` composite literal uses unkeyed fields
serviceName: sing-a-round  issueKey: AYTDWYWIQgS5eBhcIX27  errInfo: composites: `go.mongodb.org/mongo-driver/bson/primitive.E` composite literal uses unkeyed fields
serviceName: channel-live-mgr  issueKey: AYSPK6tJQgS5eBhcJkcU  errInfo: lostcancel: the cancel function returned by context.WithTimeout should be called, not discarded, to avoid a context leak
serviceName: channel-live-mgr  issueKey: AYM7345b3YHuYDVT-cXp  errInfo: SA4006: this value of `appointPkInfo` is never used
serviceName: channel-live-mgr  issueKey: AYSPK6tJQgS5eBhcJkcc  errInfo: SA4006: this value of `err` is never used
serviceName: channel-live-mgr  issueKey: AYSPK6tJQgS5eBhcJkca  errInfo: SA4006: this value of `err` is never used
serviceName: channel-live-mgr  issueKey: AYSPK6tJQgS5eBhcJkcJ  errInfo: S1028: should use fmt.Errorf(...) instead of errors.New(fmt.Sprintf(...))
serviceName: channel-live-mgr  issueKey: AYSPK6tJQgS5eBhcJkcK  errInfo: S1028: should use fmt.Errorf(...) instead of errors.New(fmt.Sprintf(...))
serviceName: channel-live-mgr  issueKey: AYSPK6tJQgS5eBhcJkcb  errInfo: SA4006: this value of `err` is never used
serviceName: channel-live-mgr  issueKey: AYSPK6tJQgS5eBhcJkcd  errInfo: SA4006: this value of `err` is never used
serviceName: channel-live-mgr  issueKey: AYSPK6tJQgS5eBhcJkcL  errInfo: S1011: should replace loop with `out.Items = append(out.Items, items...)`
serviceName: masked-pk-svr  issueKey: AYQyuqW0QgS5eBhcv1nX  errInfo: S1011: should replace loop with `uidList = append(uidList, anchorList...)`
serviceName: masked-pk-svr  issueKey: AYPQu3uVwyc6c63Xbc0t  errInfo: SA4010: this result of append is never used, except maybe in other appends
serviceName: masked-pk-svr  issueKey: AYMc_9U83YHuYDVTz7jP  errInfo: S1009: should omit nil check; len() for nil maps is defined as zero
serviceName: masked-pk-svr  issueKey: AYMc_9U83YHuYDVTz7jQ  errInfo: S1009: should omit nil check; len() for nil maps is defined as zero
serviceName: masked-pk-svr  issueKey: AYMc_9U83YHuYDVTz7jS  errInfo: SA4010: this result of append is never used, except maybe in other appends
serviceName: masked-pk-svr  issueKey: AYMc_9VG3YHuYDVTz7jW  errInfo: SA4006: this value of `config` is never used
serviceName: masked-pk-svr  issueKey: AYMc_9UE3YHuYDVTz7ij  errInfo: SA4009(related information): assignment to ctx
serviceName: masked-pk-svr  issueKey: AYQTyT5HQgS5eBhcOTaT  errInfo: Consider preallocating `pushChannelList`
serviceName: masked-pk-svr  issueKey: AYMc_9UE3YHuYDVTz7ii  errInfo: SA4009: argument ctx is overwritten before first use
serviceName: masked-pk-svr  issueKey: AYMc_9Ub3YHuYDVTz7ix  errInfo: SA4006: this value of `config` is never used
serviceName: masked-pk-svr  issueKey: AYMc_9Ub3YHuYDVTz7iz  errInfo: SA4006: this value of `exist` is never used
serviceName: masked-pk-svr  issueKey: AYSFDTPPQgS5eBhc6Ob9  errInfo: SA4006: this value of `config` is never used
serviceName: masked-pk-svr  issueKey: AYMc_9Ub3YHuYDVTz7iy  errInfo: SA4006: this value of `config` is never used
serviceName: masked-pk-svr  issueKey: AYMc_9Ub3YHuYDVTz7i0  errInfo: SA4006: this value of `failList` is never used
serviceName: masked-pk-svr  issueKey: AYMc_9Ub3YHuYDVTz7i3  errInfo: SA4006: this value of `err` is never used
serviceName: masked-pk-svr  issueKey: AYMc_9Ub3YHuYDVTz7i4  errInfo: SA4006: this value of `fail` is never used
serviceName: masked-pk-svr  issueKey: AYMc_9Ub3YHuYDVTz7i5  errInfo: SA4006: this value of `channelList` is never used
serviceName: masked-pk-svr  issueKey: AYSFDTPPQgS5eBhc6Ob-  errInfo: SA4006: this value of `config` is never used
serviceName: masked-pk-svr  issueKey: AYMc_9Ub3YHuYDVTz7i1  errInfo: SA4006: this value of `uidList` is never used
serviceName: masked-pk-svr  issueKey: AYMc_9Ub3YHuYDVTz7i2  errInfo: SA4006: this value of `channelList` is never used
serviceName: tmp-channel-open-game  issueKey: AYQUm_U8QgS5eBhcRGEi  errInfo: SA4006: this value of `err` is never used
serviceName: tmp-channel-open-game  issueKey: AYQUm_U8QgS5eBhcRGEh  errInfo: SA4006: this value of `err` is never used
serviceName: tmp-channel-open-game  issueKey: AYQUm_VPQgS5eBhcRGEo  errInfo: SA1015: using time.Tick leaks the underlying ticker, consider using it only in endless functions, tests and the main package, and use time.NewTicker here
serviceName: tmp-channel-open-game  issueKey: AYQUm_VAQgS5eBhcRGEj  errInfo: S1028: should use fmt.Errorf(...) instead of errors.New(fmt.Sprintf(...))
serviceName: gold-commission  issueKey: AYSnoEADQgS5eBhcmT--  errInfo: S1003: should use strings.ContainsAny(tag, channelTagGuild) instead
serviceName: gold-commission  issueKey: AYSnoEADQgS5eBhcmT-_  errInfo: S1003: should use strings.ContainsAny(tag, channelTagBlind) instead
serviceName: gold-commission  issueKey: AYSnoEADQgS5eBhcmT_A  errInfo: S1003: should use strings.ContainsAny(tag, channelTagCP) instead
serviceName: gold-commission  issueKey: AYS-Cg7lQgS5eBhc8bs5  errInfo: unknown field `StatPeriodCny` in struct literal
serviceName: gold-commission  issueKey: AYS-Cg7lQgS5eBhc8bs6  errInfo: unknown field `ComparePeriodCny` in struct literal
serviceName: gold-commission  issueKey: AYS-Cg7lQgS5eBhc8bs9  errInfo: unknown field `StatPeriodCny` in struct literal
serviceName: gold-commission  issueKey: AYS-Cg7lQgS5eBhc8bs-  errInfo: unknown field `ComparePeriodCny` in struct literal
serviceName: gold-commission  issueKey: AYR0zlszQgS5eBhcgMhF  errInfo: composites: `go.mongodb.org/mongo-driver/bson/primitive.E` composite literal uses unkeyed fields
serviceName: gold-commission  issueKey: AYQNGeAjQgS5eBhcFt1G  errInfo: composites: `go.mongodb.org/mongo-driver/bson/primitive.E` composite literal uses unkeyed fields
serviceName: gold-commission  issueKey: AYQNGeAjQgS5eBhcFt1H  errInfo: composites: `go.mongodb.org/mongo-driver/bson/primitive.E` composite literal uses unkeyed fields
serviceName: gold-commission  issueKey: AYQNGeAjQgS5eBhcFt1I  errInfo: composites: `go.mongodb.org/mongo-driver/bson/primitive.E` composite literal uses unkeyed fields
serviceName: gold-commission  issueKey: AYR0zlszQgS5eBhcgMhC  errInfo: composites: `go.mongodb.org/mongo-driver/bson/primitive.E` composite literal uses unkeyed fields
serviceName: gold-commission  issueKey: AYR0zlszQgS5eBhcgMhD  errInfo: composites: `go.mongodb.org/mongo-driver/bson/primitive.E` composite literal uses unkeyed fields
serviceName: gold-commission  issueKey: AYR0zlszQgS5eBhcgMhE  errInfo: composites: `go.mongodb.org/mongo-driver/bson/primitive.E` composite literal uses unkeyed fields
serviceName: gold-commission  issueKey: AYR0zlszQgS5eBhcgMhG  errInfo: composites: `go.mongodb.org/mongo-driver/bson/primitive.E` composite literal uses unkeyed fields
serviceName: music-nest  issueKey: AYTCKF1cQgS5eBhcCNOd  errInfo: SA4006: this value of `err` is never used
serviceName: music-nest  issueKey: AYMcU_LT3YHuYDVTyfbw  errInfo: S1011: should replace loop with `guests = append(guests, item.GetGuestInfos()...)`
serviceName: music-nest  issueKey: AYMcU_Jt3YHuYDVTyfba  errInfo: S1011: should replace loop with `activitiesRet = append(activitiesRet, activities...)`
serviceName: music-nest  issueKey: AYMcU_Jt3YHuYDVTyfbb  errInfo: S1011: should replace loop with `activitiesRet = append(activitiesRet, activities...)`
serviceName: music-nest  issueKey: AYMcU_Kr3YHuYDVTyfbm  errInfo: S1005: unnecessary assignment to the blank identifier
serviceName: recall-minigame-user  issueKey: AYQUk9L7QgS5eBhcQ1hO  errInfo: printf: fmt.Sprintf format %d reads arg #2, but call has 1 arg
serviceName: recall-minigame-user  issueKey: AYQUk9L7QgS5eBhcQ1hP  errInfo: printf: fmt.Sprintf format %d reads arg #2, but call has 1 arg
serviceName: censoring-proxy  issueKey: AYQUc_WmQgS5eBhcPtJ8  errInfo: SA4009: argument taskID is overwritten before first use
serviceName: ugc-friendship-logic  issueKey: AYQUnrPOQgS5eBhcRMIn  errInfo: loopclosure: loop variable followUid captured by func literal
serviceName: comm-push-op-schedule  issueKey: AYQUfH5BQgS5eBhcP_Me  errInfo: Consider preallocating `needPushDndUids`
serviceName: rhythm-logic  issueKey: AYTmsf_FQgS5eBhc4atI  errInfo: SA4010: this result of append is never used, except maybe in other appends
serviceName: interaction-intimacy-recommend  issueKey: AYNptzvAwyc6c63XsDkA  errInfo: SA2000: should call s.sw.Add(1) before starting the goroutine to avoid a race
serviceName: interaction-intimacy-recommend  issueKey: AYNptzvAwyc6c63XsDj_  errInfo: SA2000: should call s.sw.Add(1) before starting the goroutine to avoid a race
serviceName: interaction-intimacy-recommend  issueKey: AYNptzvMwyc6c63XsDkO  errInfo: SA9003: empty branch
serviceName: interaction-intimacy-recommend  issueKey: AYNptzvAwyc6c63XsDj-  errInfo: SA2000: should call s.sw.Add(1) before starting the goroutine to avoid a race
serviceName: channel-ktv  issueKey: AYS-DLYvQgS5eBhc8lPB  errInfo: info.HandClapUser undefined (type *"golang.52tt.com/services/channel-ktv/model/play/entity".Play has no field or method HandClapUser)
serviceName: channel-ktv  issueKey: AYS-DLYmQgS5eBhc8lO4  errInfo: info.HandClapUser undefined (type *"golang.52tt.com/services/channel-ktv/model/play/entity".Play has no field or method HandClapUser)
serviceName: channel-ktv  issueKey: AYS-DLYmQgS5eBhc8lO5  errInfo: info.HandClapUser undefined (type *"golang.52tt.com/services/channel-ktv/model/play/entity".Play has no field or method HandClapUser)
serviceName: channel-ktv  issueKey: AYS-DLYmQgS5eBhc8lO6  errInfo: msg.HandClapUser undefined (type *"golang.52tt.com/protocol/app/channel-ktv".ChannelKTVUpdate has no field or method HandClapUser)
serviceName: channel-ktv  issueKey: AYTRmsCsQgS5eBhclph3  errInfo: info.HandClapList undefined (type *"golang.52tt.com/services/channel-ktv/model/play/entity".Play has no field or method HandClapList)
serviceName: channel-ktv  issueKey: AYS-DLYmQgS5eBhc8lO8  errInfo: HandClapUserMicStatus_HandClapUserMicStatus_ON_MIC not declared by package channel_ktv
serviceName: channel-ktv  issueKey: AYS-DLYvQgS5eBhc8lO-  errInfo: info.HandClapList undefined (type *"golang.52tt.com/services/channel-ktv/model/play/entity".Play has no field or method HandClapList)
serviceName: channel-ktv  issueKey: AYS-DLYvQgS5eBhc8lO_  errInfo: info.HandClapList undefined (type *"golang.52tt.com/services/channel-ktv/model/play/entity".Play has no field or method HandClapList)
serviceName: channel-ktv  issueKey: AYS-DLYvQgS5eBhc8lPA  errInfo: info.HandClapList undefined (type *"golang.52tt.com/services/channel-ktv/model/play/entity".Play has no field or method HandClapList)
serviceName: channel-ktv  issueKey: AYS-DLYvQgS5eBhc8lPC  errInfo: info.HandClapUser undefined (type *"golang.52tt.com/services/channel-ktv/model/play/entity".Play has no field or method HandClapUser)
serviceName: channel-ktv  issueKey: AYS-DLYvQgS5eBhc8lO9  errInfo: HandClapUserMicStatus not declared by package channel_ktv
serviceName: channel-ktv  issueKey: AYS-DLYmQgS5eBhc8lO2  errInfo: HandClapUserMicStatus not declared by package channel_ktv
serviceName: channel-ktv  issueKey: AYS-DLYmQgS5eBhc8lO3  errInfo: unknown field `HandClapList` in struct literal
serviceName: channel-ktv  issueKey: AYTBkBkhQgS5eBhcAmqV  errInfo: unknown field `In` in struct literal
serviceName: channel-ktv  issueKey: AYTBkBkhQgS5eBhcAmqW  errInfo: unknown field `Out` in struct literal
serviceName: channel-ktv  issueKey: AYTBkBkhQgS5eBhcAmqX  errInfo: unknown field `In` in struct literal
serviceName: channel-ktv  issueKey: AYTBkBkhQgS5eBhcAmqY  errInfo: unknown field `Out` in struct literal
serviceName: channel-ktv  issueKey: AYTBkBkhQgS5eBhcAmqZ  errInfo: unknown field `In` in struct literal
serviceName: channel-ktv  issueKey: AYTBkBkhQgS5eBhcAmqa  errInfo: unknown field `Out` in struct literal

2022-12-06 18:44:18 start:
serviceName: sakura-bot  issueKey: AYQN__MmQgS5eBhcHdt9  errInfo: "golang.52tt.com/protocol/services/logicsvr-go/bots" imported but not used as logic_bots
serviceName: super-player-svr  issueKey: AYRQIYZUQgS5eBhcEQZX  errInfo: lostcancel: the cancel function returned by context.WithTimeout should be called, not discarded, to avoid a context leak
serviceName: super-player-svr  issueKey: AYSkjDI6QgS5eBhck6jJ  errInfo: SA1029: should not use built-in type string as key for value; define your own type to avoid collisions
serviceName: super-player-svr  issueKey: AYNjLYfR3YHuYDVTeuBd  errInfo: SA1029: should not use built-in type string as key for value; define your own type to avoid collisions
serviceName: super-player-svr  issueKey: AYNjLYfR3YHuYDVTeuBe  errInfo: SA1029: should not use built-in type string as key for value; define your own type to avoid collisions
serviceName: super-player-svr  issueKey: AYNjLYfR3YHuYDVTeuBf  errInfo: SA1029: should not use built-in type string as key for value; define your own type to avoid collisions
serviceName: super-player-svr  issueKey: AYSkjDI6QgS5eBhck6jK  errInfo: SA1029: should not use built-in type string as key for value; define your own type to avoid collisions
serviceName: super-player-svr  issueKey: AYSkjDI6QgS5eBhck6jL  errInfo: SA1029: should not use built-in type string as key for value; define your own type to avoid collisions
serviceName: super-player-svr  issueKey: AYSkjDI6QgS5eBhck6jM  errInfo: SA1029: should not use built-in type string as key for value; define your own type to avoid collisions
serviceName: super-player-svr  issueKey: AYSkjDI6QgS5eBhck6jN  errInfo: SA1029: should not use built-in type string as key for value; define your own type to avoid collisions
serviceName: super-player-svr  issueKey: AYSkjDI6QgS5eBhck6jO  errInfo: SA1029: should not use built-in type string as key for value; define your own type to avoid collisions
serviceName: super-player-svr  issueKey: AYNjLYfR3YHuYDVTeuBc  errInfo: SA4009(related information): assignment to err
serviceName: super-player-svr  issueKey: AYSkjDI6QgS5eBhck6jP  errInfo: SA1029: should not use built-in type string as key for value; define your own type to avoid collisions
serviceName: super-player-svr  issueKey: AYSkjDI6QgS5eBhck6jQ  errInfo: SA1029: should not use built-in type string as key for value; define your own type to avoid collisions
serviceName: super-player-svr  issueKey: AYSkjDI6QgS5eBhck6jR  errInfo: SA1029: should not use built-in type string as key for value; define your own type to avoid collisions
serviceName: super-player-svr  issueKey: AYSkjDI6QgS5eBhck6jS  errInfo: SA1029: should not use built-in type string as key for value; define your own type to avoid collisions
serviceName: super-player-svr  issueKey: AYSkjDI6QgS5eBhck6jT  errInfo: SA1029: should not use built-in type string as key for value; define your own type to avoid collisions
serviceName: super-player-svr  issueKey: AYSkjDI6QgS5eBhck6jU  errInfo: SA1029: should not use built-in type string as key for value; define your own type to avoid collisions
serviceName: super-player-svr  issueKey: AYSkjDI6QgS5eBhck6jW  errInfo: SA1029: should not use built-in type string as key for value; define your own type to avoid collisions
serviceName: super-player-svr  issueKey: AYSkjDI6QgS5eBhck6jX  errInfo: SA1029: should not use built-in type string as key for value; define your own type to avoid collisions
serviceName: super-player-svr  issueKey: AYSkjDI6QgS5eBhck6jV  errInfo: SA1029: should not use built-in type string as key for value; define your own type to avoid collisions
serviceName: super-player-svr  issueKey: AYNjLYfR3YHuYDVTeuBX  errInfo: lostcancel: the cancel function returned by context.WithTimeout should be called, not discarded, to avoid a context leak
serviceName: super-player-svr  issueKey: AYSkjDI6QgS5eBhck6i5  errInfo: lostcancel: the cancel function returned by context.WithTimeout should be called, not discarded, to avoid a context leak
serviceName: super-player-svr  issueKey: AYSkjDI6QgS5eBhck6i6  errInfo: lostcancel: the cancel function returned by context.WithTimeout should be called, not discarded, to avoid a context leak
serviceName: super-player-svr  issueKey: AYSkjDI6QgS5eBhck6i7  errInfo: lostcancel: the cancel function returned by context.WithTimeout should be called, not discarded, to avoid a context leak
serviceName: super-player-svr  issueKey: AYSkjDI6QgS5eBhck6i8  errInfo: lostcancel: the cancel function returned by context.WithTimeout should be called, not discarded, to avoid a context leak
serviceName: super-player-svr  issueKey: AYSkjDI6QgS5eBhck6i9  errInfo: lostcancel: the cancel function returned by context.WithTimeout should be called, not discarded, to avoid a context leak
serviceName: super-player-svr  issueKey: AYSkjDI6QgS5eBhck6i-  errInfo: lostcancel: the cancel function returned by context.WithTimeout should be called, not discarded, to avoid a context leak
serviceName: super-player-svr  issueKey: AYSkjDI6QgS5eBhck6i_  errInfo: lostcancel: the cancel function returned by context.WithTimeout should be called, not discarded, to avoid a context leak
serviceName: super-player-svr  issueKey: AYSkjDI6QgS5eBhck6jA  errInfo: lostcancel: the cancel function returned by context.WithTimeout should be called, not discarded, to avoid a context leak
serviceName: super-player-svr  issueKey: AYSkjDI6QgS5eBhck6jB  errInfo: lostcancel: the cancel function returned by context.WithTimeout should be called, not discarded, to avoid a context leak
serviceName: super-player-svr  issueKey: AYSkjDI6QgS5eBhck6jC  errInfo: lostcancel: the cancel function returned by context.WithTimeout should be called, not discarded, to avoid a context leak
serviceName: super-player-svr  issueKey: AYSkjDI6QgS5eBhck6jD  errInfo: lostcancel: the cancel function returned by context.WithTimeout should be called, not discarded, to avoid a context leak
serviceName: super-player-svr  issueKey: AYSkjDI6QgS5eBhck6jE  errInfo: lostcancel: the cancel function returned by context.WithTimeout should be called, not discarded, to avoid a context leak
serviceName: super-player-svr  issueKey: AYNjLYfR3YHuYDVTeuBb  errInfo: SA4009: argument err is overwritten before first use
serviceName: super-player-svr  issueKey: AYRQIYZUQgS5eBhcEQZW  errInfo: lostcancel: the cancel function returned by context.WithTimeout should be called, not discarded, to avoid a context leak
serviceName: chat-card-logic  issueKey: AYQdP2mvQgS5eBhcbJ3x  errInfo: S1012: should use `time.Since` instead of `time.Now().Sub`
serviceName: official-live-channel  issueKey: AYMdBZxG3YHuYDVTz8ez  errInfo: SA4006: this value of `err` is never used
serviceName: channel-room  issueKey: AYQUel2zQgS5eBhcP6J9  errInfo: SA1015: using time.Tick leaks the underlying ticker, consider using it only in endless functions, tests and the main package, and use time.NewTicker here
serviceName: channel-room  issueKey: AYQUel2zQgS5eBhcP6J3  errInfo: S1012: should use `time.Since` instead of `time.Now().Sub`
serviceName: anchorcontract-go  issueKey: AYS-CWBSQgS5eBhc8aN2  errInfo: m.channelCli undefined (type *AnchorContractMgr has no field or method channelCli)
serviceName: anchorcontract-go  issueKey: AYS-CWBEQgS5eBhc8aNx  errInfo: m.apiCenterCli undefined (type *AnchorContractMgr has no field or method apiCenterCli)
serviceName: anchorcontract-go  issueKey: AYS-CWBEQgS5eBhc8aNy  errInfo: FollowPublicReq_SYSTEM not declared by package apicenter
serviceName: anchorcontract-go  issueKey: AYS-CWBEQgS5eBhc8aNz  errInfo: m.apiCenterCli undefined (type *AnchorContractMgr has no field or method apiCenterCli)
serviceName: anchorcontract-go  issueKey: AYS-CWBEQgS5eBhc8aN0  errInfo: FollowPublicReq_SYSTEM not declared by package apicenter
serviceName: anchorcontract-go  issueKey: AYS-CWARQgS5eBhc8aNv  errInfo: m.apiCenterCli undefined (type *AnchorContractMgr has no field or method apiCenterCli)
serviceName: anchorcontract-go  issueKey: AYS-CWARQgS5eBhc8aNw  errInfo: FollowPublicReq_SYSTEM not declared by package apicenter
serviceName: anchorcontract-go  issueKey: AYQ3yMkYQgS5eBhc1hsS  errInfo: "github.com/go-sql-driver/mysql" imported but not used as mysql2
serviceName: anchorcontract-go  issueKey: AYQ3yMjTQgS5eBhc1hsJ  errInfo: ParentGuildInfo not declared by package anchorcontract_go
serviceName: anchorcontract-go  issueKey: AYRVLAlBQgS5eBhcLsu-  errInfo: ParentGuildInfo not declared by package anchorcontract_go
serviceName: anchorcontract-go  issueKey: AYQ3yMjTQgS5eBhc1hsL  errInfo: ParentGuildInfo not declared by package anchorcontract_go
serviceName: avatar-http-logic  issueKey: AYNp0rkZwyc6c63XsWa8  errInfo: Decode not declared by package tools
serviceName: avatar-http-logic  issueKey: AYNp0rkZwyc6c63XsWa6  errInfo: PCM not declared by package tools
serviceName: avatar-http-logic  issueKey: AYNp0rkZwyc6c63XsWa7  errInfo: MP3 not declared by package tools
serviceName: tel-call-logic  issueKey: AYQUm0mqQgS5eBhcREwT  errInfo: unreachable: unreachable code
serviceName: ugc-friendship-logic  issueKey: AYQUnrPOQgS5eBhcRMIn  errInfo: loopclosure: loop variable followUid captured by func literal
serviceName: interaction-intimacy-recommend  issueKey: AYNptzvAwyc6c63XsDkA  errInfo: SA2000: should call s.sw.Add(1) before starting the goroutine to avoid a race
serviceName: interaction-intimacy-recommend  issueKey: AYNptzvAwyc6c63XsDj_  errInfo: SA2000: should call s.sw.Add(1) before starting the goroutine to avoid a race
serviceName: interaction-intimacy-recommend  issueKey: AYNptzvMwyc6c63XsDkO  errInfo: SA9003: empty branch
serviceName: interaction-intimacy-recommend  issueKey: AYNptzvAwyc6c63XsDj-  errInfo: SA2000: should call s.sw.Add(1) before starting the goroutine to avoid a race
serviceName: apicenter-go  issueKey: AYPAxstLwyc6c63XOqpe  errInfo: lostcancel: the cancel function returned by context.WithTimeout should be called, not discarded, to avoid a context leak
serviceName: apicenter-go  issueKey: AYPQIECXwyc6c63XZgkz  errInfo: SA4010: this result of append is never used, except maybe in other appends
serviceName: apicenter-go  issueKey: AYM1dKoX3YHuYDVT3sSs  errInfo: S1005: unnecessary assignment to the blank identifier
serviceName: apicenter-go  issueKey: AYM1dKoT3YHuYDVT3sSr  errInfo: SA4006: this value of `err` is never used
serviceName: apicenter-go  issueKey: AYM1dKnn3YHuYDVT3sSh  errInfo: SA4010: this result of append is never used, except maybe in other appends
serviceName: apicenter-go  issueKey: AYM1dKnG3YHuYDVT3sSR  errInfo: S1011: should replace loop with `chIdList = append(chIdList, whiteResp.GetChannelIdList()...)`
serviceName: apicenter-go  issueKey: AYM1dKnG3YHuYDVT3sSW  errInfo: SA4006: this value of `serverErr` is never used
serviceName: apicenter-go  issueKey: AYM1dKnG3YHuYDVT3sSX  errInfo: SA4006: this value of `serverErr` is never used
serviceName: apicenter-go  issueKey: AYM1dKnG3YHuYDVT3sSS  errInfo: unreachable: unreachable code
serviceName: apicenter-go  issueKey: AYP5cvbcQgS5eBhc-80C  errInfo: SA4006: this value of `err` is never used
serviceName: apicenter-go  issueKey: AYP5cvbcQgS5eBhc-80B  errInfo: SA4006: this value of `err` is never used
serviceName: apicenter-go  issueKey: AYQH2t-JQgS5eBhcBTAs  errInfo: S1011: should replace loop with `uidList = append(uidList, in.GetUidList()...)`
serviceName: apicenter-go  issueKey: AYM1dKnG3YHuYDVT3sSQ  errInfo: S1011: should replace loop with `chIdList = append(chIdList, in.GetChannelIdList()...)`
serviceName: music-nest  issueKey: AYTm22z-QgS5eBhc5TGp  errInfo: other declaration of topicTypePkChange
serviceName: music-nest  issueKey: AYTm22z-QgS5eBhc5TGq  errInfo: other declaration of PkChangeSub
serviceName: music-nest  issueKey: AYTm22z-QgS5eBhc5TGs  errInfo: other declaration of Close
serviceName: music-nest  issueKey: AYTm22z-QgS5eBhc5TGr  errInfo: other declaration of NewPkChangeSub
serviceName: music-nest  issueKey: AYTm22z-QgS5eBhc5TGt  errInfo: other declaration of handlerEvent
serviceName: music-nest  issueKey: AYTm22z-QgS5eBhc5TGu  errInfo: other declaration of handlerChannelEvent
serviceName: music-nest  issueKey: AYTm220FQgS5eBhc5TGw  errInfo: `topicTypePkChange` redeclared in this block
serviceName: music-nest  issueKey: AYTm220FQgS5eBhc5TGx  errInfo: `PkChangeSub` redeclared in this block
serviceName: music-nest  issueKey: AYTm220FQgS5eBhc5TGz  errInfo: method Close already declared for type PkChangeSub struct{*"golang.52tt.com/services/ugc/common/event".KafkaSub; musicNestMgr *manager.Manager}
serviceName: music-nest  issueKey: AYTm220FQgS5eBhc5TGy  errInfo: `NewPkChangeSub` redeclared in this block
serviceName: music-nest  issueKey: AYTm220FQgS5eBhc5TG0  errInfo: method handlerEvent already declared for type PkChangeSub struct{*"golang.52tt.com/services/ugc/common/event".KafkaSub; musicNestMgr *manager.Manager}
serviceName: music-nest  issueKey: AYTm220FQgS5eBhc5TG1  errInfo: method handlerChannelEvent already declared for type PkChangeSub struct{*"golang.52tt.com/services/ugc/common/event".KafkaSub; musicNestMgr *manager.Manager}
serviceName: music-nest  issueKey: AYTCKF1cQgS5eBhcCNOd  errInfo: SA4006: this value of `err` is never used
serviceName: music-nest  issueKey: AYMcU_Jt3YHuYDVTyfba  errInfo: S1011: should replace loop with `activitiesRet = append(activitiesRet, activities...)`
serviceName: music-nest  issueKey: AYMcU_Jt3YHuYDVTyfbb  errInfo: S1011: should replace loop with `activitiesRet = append(activitiesRet, activities...)`
serviceName: music-nest  issueKey: AYMcU_Kr3YHuYDVTyfbm  errInfo: S1005: unnecessary assignment to the blank identifier
serviceName: censoring-proxy  issueKey: AYQUc_WmQgS5eBhcPtJ8  errInfo: SA4009: argument taskID is overwritten before first use
serviceName: concert  issueKey: AYSjrrTmQgS5eBhcjMCC  errInfo: SA4021: x = append(y) is equivalent to x = y
serviceName: concert  issueKey: AYQOQgEPQgS5eBhcIuIB  errInfo: SA5008: duplicate struct tag "json"
serviceName: auto-award  issueKey: AYSEJxdeQgS5eBhc3eAk  errInfo: SA4006: this value of `err` is never used
serviceName: auto-award  issueKey: AYSEJxdeQgS5eBhc3eAl  errInfo: SA4006: this value of `err` is never used
serviceName: channel-live-mgr  issueKey: AYSPK6tJQgS5eBhcJkcU  errInfo: lostcancel: the cancel function returned by context.WithTimeout should be called, not discarded, to avoid a context leak
serviceName: channel-live-mgr  issueKey: AYM7345b3YHuYDVT-cXp  errInfo: SA4006: this value of `appointPkInfo` is never used
serviceName: channel-live-mgr  issueKey: AYSPK6tJQgS5eBhcJkcc  errInfo: SA4006: this value of `err` is never used
serviceName: channel-live-mgr  issueKey: AYSPK6tJQgS5eBhcJkca  errInfo: SA4006: this value of `err` is never used
serviceName: channel-live-mgr  issueKey: AYSPK6tJQgS5eBhcJkcJ  errInfo: S1028: should use fmt.Errorf(...) instead of errors.New(fmt.Sprintf(...))
serviceName: channel-live-mgr  issueKey: AYSPK6tJQgS5eBhcJkcK  errInfo: S1028: should use fmt.Errorf(...) instead of errors.New(fmt.Sprintf(...))
serviceName: channel-live-mgr  issueKey: AYSPK6tJQgS5eBhcJkcb  errInfo: SA4006: this value of `err` is never used
serviceName: channel-live-mgr  issueKey: AYSPK6tJQgS5eBhcJkcd  errInfo: SA4006: this value of `err` is never used
serviceName: channel-live-mgr  issueKey: AYSPK6tJQgS5eBhcJkcL  errInfo: S1011: should replace loop with `out.Items = append(out.Items, items...)`
serviceName: channel-live-ranking  issueKey: AYMdBZmx3YHuYDVTz8RU  errInfo: S1031: unnecessary nil check around range
serviceName: recall-minigame-user  issueKey: AYQUk9L7QgS5eBhcQ1hO  errInfo: printf: fmt.Sprintf format %d reads arg #2, but call has 1 arg
serviceName: recall-minigame-user  issueKey: AYQUk9L7QgS5eBhcQ1hP  errInfo: printf: fmt.Sprintf format %d reads arg #2, but call has 1 arg
serviceName: dark-checker  issueKey: AYQUfhp_QgS5eBhcQBrE  errInfo: S1036: unnecessary guard around map access
serviceName: tmp-channel-open-game  issueKey: AYQUm_U8QgS5eBhcRGEi  errInfo: SA4006: this value of `err` is never used
serviceName: tmp-channel-open-game  issueKey: AYQUm_U8QgS5eBhcRGEh  errInfo: SA4006: this value of `err` is never used
serviceName: tmp-channel-open-game  issueKey: AYQUm_VPQgS5eBhcRGEo  errInfo: SA1015: using time.Tick leaks the underlying ticker, consider using it only in endless functions, tests and the main package, and use time.NewTicker here
serviceName: tmp-channel-open-game  issueKey: AYQUm_VAQgS5eBhcRGEj  errInfo: S1028: should use fmt.Errorf(...) instead of errors.New(fmt.Sprintf(...))
serviceName: client-conf-mgr  issueKey: AYM02SPb3YHuYDVT3jDV  errInfo: Refactor this piece of code to not have any dead code after this "return".
serviceName: client-conf-mgr  issueKey: AYM02SPT3YHuYDVT3jDQ  errInfo: S1028: should use fmt.Errorf(...) instead of errors.New(fmt.Sprintf(...))
serviceName: client-conf-mgr  issueKey: AYTDQBRlQgS5eBhcHPbA  errInfo: S1028: should use fmt.Errorf(...) instead of errors.New(fmt.Sprintf(...))
serviceName: client-conf-mgr  issueKey: AYTDQBRlQgS5eBhcHPbB  errInfo: S1028: should use fmt.Errorf(...) instead of errors.New(fmt.Sprintf(...))
serviceName: client-conf-mgr  issueKey: AYM02SPT3YHuYDVT3jDR  errInfo: S1028: should use fmt.Errorf(...) instead of errors.New(fmt.Sprintf(...))
serviceName: client-conf-mgr  issueKey: AYQUe5oAQgS5eBhcP8zE  errInfo: S1028: should use fmt.Errorf(...) instead of errors.New(fmt.Sprintf(...))
serviceName: client-conf-mgr  issueKey: AYM02SPb3YHuYDVT3jDW  errInfo: unreachable: unreachable code
serviceName: gold-commission  issueKey: AYSnoEADQgS5eBhcmT--  errInfo: S1003: should use strings.ContainsAny(tag, channelTagGuild) instead
serviceName: gold-commission  issueKey: AYSnoEADQgS5eBhcmT-_  errInfo: S1003: should use strings.ContainsAny(tag, channelTagBlind) instead
serviceName: gold-commission  issueKey: AYSnoEADQgS5eBhcmT_A  errInfo: S1003: should use strings.ContainsAny(tag, channelTagCP) instead
serviceName: gold-commission  issueKey: AYS-Cg7lQgS5eBhc8bs5  errInfo: unknown field `StatPeriodCny` in struct literal
serviceName: gold-commission  issueKey: AYS-Cg7lQgS5eBhc8bs6  errInfo: unknown field `ComparePeriodCny` in struct literal
serviceName: gold-commission  issueKey: AYS-Cg7lQgS5eBhc8bs9  errInfo: unknown field `StatPeriodCny` in struct literal
serviceName: gold-commission  issueKey: AYS-Cg7lQgS5eBhc8bs-  errInfo: unknown field `ComparePeriodCny` in struct literal
serviceName: gold-commission  issueKey: AYR0zlszQgS5eBhcgMhF  errInfo: composites: `go.mongodb.org/mongo-driver/bson/primitive.E` composite literal uses unkeyed fields
serviceName: gold-commission  issueKey: AYQNGeAjQgS5eBhcFt1G  errInfo: composites: `go.mongodb.org/mongo-driver/bson/primitive.E` composite literal uses unkeyed fields
serviceName: gold-commission  issueKey: AYQNGeAjQgS5eBhcFt1H  errInfo: composites: `go.mongodb.org/mongo-driver/bson/primitive.E` composite literal uses unkeyed fields
serviceName: gold-commission  issueKey: AYQNGeAjQgS5eBhcFt1I  errInfo: composites: `go.mongodb.org/mongo-driver/bson/primitive.E` composite literal uses unkeyed fields
serviceName: gold-commission  issueKey: AYR0zlszQgS5eBhcgMhC  errInfo: composites: `go.mongodb.org/mongo-driver/bson/primitive.E` composite literal uses unkeyed fields
serviceName: gold-commission  issueKey: AYR0zlszQgS5eBhcgMhD  errInfo: composites: `go.mongodb.org/mongo-driver/bson/primitive.E` composite literal uses unkeyed fields
serviceName: gold-commission  issueKey: AYR0zlszQgS5eBhcgMhE  errInfo: composites: `go.mongodb.org/mongo-driver/bson/primitive.E` composite literal uses unkeyed fields
serviceName: gold-commission  issueKey: AYR0zlszQgS5eBhcgMhG  errInfo: composites: `go.mongodb.org/mongo-driver/bson/primitive.E` composite literal uses unkeyed fields
serviceName: avatar-http-logic-hy  issueKey: AYQUckeSQgS5eBhcPpZ3  errInfo: Decode not declared by package tools
serviceName: avatar-http-logic-hy  issueKey: AYQUckeSQgS5eBhcPpZ1  errInfo: PCM not declared by package tools
serviceName: avatar-http-logic-hy  issueKey: AYQUckeSQgS5eBhcPpZ2  errInfo: MP3 not declared by package tools
serviceName: super-channel-logic  issueKey: AYOC0z-9wyc6c63X5Jjo  errInfo: S1028: should use fmt.Errorf(...) instead of errors.New(fmt.Sprintf(...))
serviceName: super-channel-logic  issueKey: AYOC0z-Owyc6c63X5Jjg  errInfo: SA4024: builtin function len does not return negative values
serviceName: super-channel-logic  issueKey: AYOC0z98wyc6c63X5Jja  errInfo: SA9003: empty branch
serviceName: channel-level  issueKey: AYOIhPF9wyc6c63X_UWt  errInfo: SA4003: no value of type uint32 is less than 0
serviceName: channel-level  issueKey: AYOIhPFcwyc6c63X_UWl  errInfo: SA9003: empty branch
serviceName: masked-pk-svr  issueKey: AYQyuqW0QgS5eBhcv1nX  errInfo: S1011: should replace loop with `uidList = append(uidList, anchorList...)`
serviceName: masked-pk-svr  issueKey: AYPQu3uVwyc6c63Xbc0t  errInfo: SA4010: this result of append is never used, except maybe in other appends
serviceName: masked-pk-svr  issueKey: AYMc_9U83YHuYDVTz7jP  errInfo: S1009: should omit nil check; len() for nil maps is defined as zero
serviceName: masked-pk-svr  issueKey: AYMc_9U83YHuYDVTz7jQ  errInfo: S1009: should omit nil check; len() for nil maps is defined as zero
serviceName: masked-pk-svr  issueKey: AYMc_9U83YHuYDVTz7jS  errInfo: SA4010: this result of append is never used, except maybe in other appends
serviceName: masked-pk-svr  issueKey: AYMc_9VG3YHuYDVTz7jW  errInfo: SA4006: this value of `config` is never used
serviceName: masked-pk-svr  issueKey: AYMc_9UE3YHuYDVTz7ij  errInfo: SA4009(related information): assignment to ctx
serviceName: masked-pk-svr  issueKey: AYMc_9UE3YHuYDVTz7ii  errInfo: SA4009: argument ctx is overwritten before first use
serviceName: masked-pk-svr  issueKey: AYMc_9Ub3YHuYDVTz7ix  errInfo: SA4006: this value of `config` is never used
serviceName: masked-pk-svr  issueKey: AYMc_9Ub3YHuYDVTz7iz  errInfo: SA4006: this value of `exist` is never used
serviceName: masked-pk-svr  issueKey: AYSFDTPPQgS5eBhc6Ob9  errInfo: SA4006: this value of `config` is never used
serviceName: masked-pk-svr  issueKey: AYMc_9Ub3YHuYDVTz7iy  errInfo: SA4006: this value of `config` is never used
serviceName: masked-pk-svr  issueKey: AYMc_9Ub3YHuYDVTz7i0  errInfo: SA4006: this value of `failList` is never used
serviceName: masked-pk-svr  issueKey: AYMc_9Ub3YHuYDVTz7i3  errInfo: SA4006: this value of `err` is never used
serviceName: masked-pk-svr  issueKey: AYMc_9Ub3YHuYDVTz7i4  errInfo: SA4006: this value of `fail` is never used
serviceName: masked-pk-svr  issueKey: AYMc_9Ub3YHuYDVTz7i5  errInfo: SA4006: this value of `channelList` is never used
serviceName: masked-pk-svr  issueKey: AYSFDTPPQgS5eBhc6Ob-  errInfo: SA4006: this value of `config` is never used
serviceName: masked-pk-svr  issueKey: AYMc_9Ub3YHuYDVTz7i1  errInfo: SA4006: this value of `uidList` is never used
serviceName: masked-pk-svr  issueKey: AYMc_9Ub3YHuYDVTz7i2  errInfo: SA4006: this value of `channelList` is never used
serviceName: unclaimed  issueKey: AYQUoYa_QgS5eBhcRRso  errInfo: SA4006: this value of `err` is never used
serviceName: unclaimed  issueKey: AYQUoYbFQgS5eBhcRRsq  errInfo: response body must be closed
serviceName: sing-a-round  issueKey: AYM1dM713YHuYDVT3s1N  errInfo: S1001: should use copy() instead of a loop
serviceName: sing-a-round  issueKey: AYTDWYWIQgS5eBhcIX3A  errInfo: composites: `go.mongodb.org/mongo-driver/bson/primitive.E` composite literal uses unkeyed fields
serviceName: sing-a-round  issueKey: AYTDWYWIQgS5eBhcIX3B  errInfo: composites: `go.mongodb.org/mongo-driver/bson/primitive.E` composite literal uses unkeyed fields
serviceName: sing-a-round  issueKey: AYTDWYWOQgS5eBhcIX3C  errInfo: composites: `go.mongodb.org/mongo-driver/bson/primitive.E` composite literal uses unkeyed fields
serviceName: sing-a-round  issueKey: AYTDWYWOQgS5eBhcIX3F  errInfo: composites: `go.mongodb.org/mongo-driver/bson/primitive.E` composite literal uses unkeyed fields
serviceName: sing-a-round  issueKey: AYTDWYWOQgS5eBhcIX3G  errInfo: composites: `go.mongodb.org/mongo-driver/bson/primitive.E` composite literal uses unkeyed fields
serviceName: sing-a-round  issueKey: AYTDWYWOQgS5eBhcIX3D  errInfo: composites: `go.mongodb.org/mongo-driver/bson/primitive.E` composite literal uses unkeyed fields
serviceName: sing-a-round  issueKey: AYTDWYWOQgS5eBhcIX3E  errInfo: composites: `go.mongodb.org/mongo-driver/bson/primitive.E` composite literal uses unkeyed fields
serviceName: sing-a-round  issueKey: AYTDWYWOQgS5eBhcIX3H  errInfo: composites: `go.mongodb.org/mongo-driver/bson/primitive.E` composite literal uses unkeyed fields
serviceName: sing-a-round  issueKey: AYTDWYWOQgS5eBhcIX3I  errInfo: composites: `go.mongodb.org/mongo-driver/bson/primitive.E` composite literal uses unkeyed fields
serviceName: sing-a-round  issueKey: AYTDWYWIQgS5eBhcIX2-  errInfo: composites: `go.mongodb.org/mongo-driver/bson/primitive.E` composite literal uses unkeyed fields
serviceName: sing-a-round  issueKey: AYTDWYWIQgS5eBhcIX2_  errInfo: composites: `go.mongodb.org/mongo-driver/bson/primitive.E` composite literal uses unkeyed fields
serviceName: sing-a-round  issueKey: AYM1dM8C3YHuYDVT3s1P  errInfo: composites: `go.mongodb.org/mongo-driver/bson/primitive.E` composite literal uses unkeyed fields
serviceName: sing-a-round  issueKey: AYM1dM8C3YHuYDVT3s1Q  errInfo: composites: `go.mongodb.org/mongo-driver/bson/primitive.E` composite literal uses unkeyed fields
serviceName: sing-a-round  issueKey: AYM1dM8C3YHuYDVT3s1O  errInfo: S1011: should replace loop with `idToSong = append(idToSong, songs...)`
serviceName: sing-a-round  issueKey: AYM1dM7V3YHuYDVT3s1H  errInfo: SA4010: this result of append is never used, except maybe in other appends
serviceName: sing-a-round  issueKey: AYM1dM5Z3YHuYDVT3s04  errInfo: SA4010: this result of append is never used, except maybe in other appends
serviceName: sing-a-round  issueKey: AYM1dM5Z3YHuYDVT3s05  errInfo: SA4010: this result of append is never used, except maybe in other appends
serviceName: sing-a-round  issueKey: AYTDWYWaQgS5eBhcIX3L  errInfo: composites: `go.mongodb.org/mongo-driver/bson/primitive.E` composite literal uses unkeyed fields
serviceName: sing-a-round  issueKey: AYM1dM8C3YHuYDVT3s1R  errInfo: composites: `go.mongodb.org/mongo-driver/bson/primitive.E` composite literal uses unkeyed fields
serviceName: sing-a-round  issueKey: AYTDWYWIQgS5eBhcIX29  errInfo: composites: `go.mongodb.org/mongo-driver/bson/primitive.E` composite literal uses unkeyed fields
serviceName: sing-a-round  issueKey: AYM1dM7i3YHuYDVT3s1L  errInfo: SA9003: empty branch
serviceName: sing-a-round  issueKey: AYTDWYWIQgS5eBhcIX28  errInfo: composites: `go.mongodb.org/mongo-driver/bson/primitive.E` composite literal uses unkeyed fields
serviceName: sing-a-round  issueKey: AYTDWYWaQgS5eBhcIX3J  errInfo: composites: `go.mongodb.org/mongo-driver/bson/primitive.E` composite literal uses unkeyed fields
serviceName: sing-a-round  issueKey: AYM1dM8K3YHuYDVT3s1T  errInfo: S1025: the argument's underlying type is a slice of bytes, should use a simple conversion instead of fmt.Sprintf
serviceName: sing-a-round  issueKey: AYTDWYWaQgS5eBhcIX3K  errInfo: composites: `go.mongodb.org/mongo-driver/bson/primitive.E` composite literal uses unkeyed fields
serviceName: sing-a-round  issueKey: AYM1dM6_3YHuYDVT3s1D  errInfo: SA4006: this value of `err` is never used
serviceName: sing-a-round  issueKey: AYTDWYWIQgS5eBhcIX26  errInfo: composites: `go.mongodb.org/mongo-driver/bson/primitive.E` composite literal uses unkeyed fields
serviceName: sing-a-round  issueKey: AYTDWYWIQgS5eBhcIX27  errInfo: composites: `go.mongodb.org/mongo-driver/bson/primitive.E` composite literal uses unkeyed fields
serviceName: channel-ktv  issueKey: AYS-DLYvQgS5eBhc8lPB  errInfo: info.HandClapUser undefined (type *"golang.52tt.com/services/channel-ktv/model/play/entity".Play has no field or method HandClapUser)
serviceName: channel-ktv  issueKey: AYS-DLYmQgS5eBhc8lO4  errInfo: info.HandClapUser undefined (type *"golang.52tt.com/services/channel-ktv/model/play/entity".Play has no field or method HandClapUser)
serviceName: channel-ktv  issueKey: AYS-DLYmQgS5eBhc8lO5  errInfo: info.HandClapUser undefined (type *"golang.52tt.com/services/channel-ktv/model/play/entity".Play has no field or method HandClapUser)
serviceName: channel-ktv  issueKey: AYS-DLYmQgS5eBhc8lO6  errInfo: msg.HandClapUser undefined (type *"golang.52tt.com/protocol/app/channel-ktv".ChannelKTVUpdate has no field or method HandClapUser)
serviceName: channel-ktv  issueKey: AYTRmsCsQgS5eBhclph3  errInfo: info.HandClapList undefined (type *"golang.52tt.com/services/channel-ktv/model/play/entity".Play has no field or method HandClapList)
serviceName: channel-ktv  issueKey: AYS-DLYmQgS5eBhc8lO8  errInfo: HandClapUserMicStatus_HandClapUserMicStatus_ON_MIC not declared by package channel_ktv
serviceName: channel-ktv  issueKey: AYS-DLYvQgS5eBhc8lO-  errInfo: info.HandClapList undefined (type *"golang.52tt.com/services/channel-ktv/model/play/entity".Play has no field or method HandClapList)
serviceName: channel-ktv  issueKey: AYS-DLYvQgS5eBhc8lO_  errInfo: info.HandClapList undefined (type *"golang.52tt.com/services/channel-ktv/model/play/entity".Play has no field or method HandClapList)
serviceName: channel-ktv  issueKey: AYS-DLYvQgS5eBhc8lPA  errInfo: info.HandClapList undefined (type *"golang.52tt.com/services/channel-ktv/model/play/entity".Play has no field or method HandClapList)
serviceName: channel-ktv  issueKey: AYS-DLYvQgS5eBhc8lPC  errInfo: info.HandClapUser undefined (type *"golang.52tt.com/services/channel-ktv/model/play/entity".Play has no field or method HandClapUser)
serviceName: channel-ktv  issueKey: AYS-DLYvQgS5eBhc8lO9  errInfo: HandClapUserMicStatus not declared by package channel_ktv
serviceName: channel-ktv  issueKey: AYS-DLYmQgS5eBhc8lO2  errInfo: HandClapUserMicStatus not declared by package channel_ktv
serviceName: channel-ktv  issueKey: AYS-DLYmQgS5eBhc8lO3  errInfo: unknown field `HandClapList` in struct literal
serviceName: channel-ktv  issueKey: AYTBkBkhQgS5eBhcAmqV  errInfo: unknown field `In` in struct literal
serviceName: channel-ktv  issueKey: AYTBkBkhQgS5eBhcAmqW  errInfo: unknown field `Out` in struct literal
serviceName: channel-ktv  issueKey: AYTBkBkhQgS5eBhcAmqX  errInfo: unknown field `In` in struct literal
serviceName: channel-ktv  issueKey: AYTBkBkhQgS5eBhcAmqY  errInfo: unknown field `Out` in struct literal
serviceName: channel-ktv  issueKey: AYTBkBkhQgS5eBhcAmqZ  errInfo: unknown field `In` in struct literal
serviceName: channel-ktv  issueKey: AYTBkBkhQgS5eBhcAmqa  errInfo: unknown field `Out` in struct literal
serviceName: channel-msg-express  issueKey: AYM8DcTs3YHuYDVT-3rI  errInfo: S1011: should replace loop with `r.ToChannelIdList = append(r.ToChannelIdList, rule.ToChannelIdList...)`
serviceName: channelguild-go  issueKey: AYN4fdp9wyc6c63XvlZQ  errInfo: lostcancel: the cancel function returned by context.WithTimeout should be called, not discarded, to avoid a context leak
serviceName: channelguild-go  issueKey: AYN4fdp9wyc6c63XvlZR  errInfo: lostcancel: the cancel function returned by context.WithTimeout should be called, not discarded, to avoid a context leak
serviceName: channelguild-go  issueKey: AYN4fdp9wyc6c63XvlZS  errInfo: lostcancel: the cancel function returned by context.WithTimeout should be called, not discarded, to avoid a context leak
serviceName: music-topic-channel-subscriber  issueKey: AYRbEX4KQgS5eBhcSx13  errInfo: SA4006: this value of `relationItem` is never used
serviceName: hobby-channel  issueKey: AYMbVUSp3YHuYDVTxZJ2  errInfo: S1005: unnecessary assignment to the blank identifier
serviceName: hobby-channel  issueKey: AYMbVUSK3YHuYDVTxZJw  errInfo: SA4006: this value of `cert` is never used
serviceName: avatar  issueKey: AYQNQtp6QgS5eBhcGCqx  errInfo: composites: `go.mongodb.org/mongo-driver/bson/primitive.E` composite literal uses unkeyed fields
serviceName: avatar  issueKey: AYTDNv9-QgS5eBhcGzZ7  errInfo: composites: `go.mongodb.org/mongo-driver/bson/primitive.E` composite literal uses unkeyed fields
serviceName: avatar  issueKey: AYQNQtp6QgS5eBhcGCqv  errInfo: composites: `go.mongodb.org/mongo-driver/bson/primitive.E` composite literal uses unkeyed fields
serviceName: avatar  issueKey: AYQNQtp6QgS5eBhcGCqw  errInfo: composites: `go.mongodb.org/mongo-driver/bson/primitive.E` composite literal uses unkeyed fields
serviceName: channel-live-fans  issueKey: AYSEJzQrQgS5eBhc3eZN  errInfo: "golang.52tt.com/services/knight-group/knight-group-logic/client" imported but not used as clientX
serviceName: channel-open-game-record  issueKey: AYQUeIWzQgS5eBhcP3pc  errInfo: S1011: should replace loop with `values = append(values, strList...)`

2022-12-07 10:48:30 start:

2022-12-07 11:03:04 start:
serviceName: sing-a-round  issueKey: AYM1dM8K3YHuYDVT3s1T  errInfo: S1025: the argument's underlying type is a slice of bytes, should use a simple conversion instead of fmt.Sprintf
serviceName: super-player-svr  issueKey: AYSkjDI6QgS5eBhck6jJ  errInfo: SA1029: should not use built-in type string as key for value; define your own type to avoid collisions
serviceName: super-player-svr  issueKey: AYNjLYfR3YHuYDVTeuBd  errInfo: SA1029: should not use built-in type string as key for value; define your own type to avoid collisions
serviceName: super-player-svr  issueKey: AYNjLYfR3YHuYDVTeuBe  errInfo: SA1029: should not use built-in type string as key for value; define your own type to avoid collisions
serviceName: super-player-svr  issueKey: AYNjLYfR3YHuYDVTeuBf  errInfo: SA1029: should not use built-in type string as key for value; define your own type to avoid collisions
serviceName: super-player-svr  issueKey: AYSkjDI6QgS5eBhck6jK  errInfo: SA1029: should not use built-in type string as key for value; define your own type to avoid collisions
serviceName: super-player-svr  issueKey: AYSkjDI6QgS5eBhck6jL  errInfo: SA1029: should not use built-in type string as key for value; define your own type to avoid collisions
serviceName: super-player-svr  issueKey: AYSkjDI6QgS5eBhck6jM  errInfo: SA1029: should not use built-in type string as key for value; define your own type to avoid collisions
serviceName: super-player-svr  issueKey: AYSkjDI6QgS5eBhck6jN  errInfo: SA1029: should not use built-in type string as key for value; define your own type to avoid collisions
serviceName: super-player-svr  issueKey: AYSkjDI6QgS5eBhck6jO  errInfo: SA1029: should not use built-in type string as key for value; define your own type to avoid collisions
serviceName: super-player-svr  issueKey: AYSkjDI6QgS5eBhck6jP  errInfo: SA1029: should not use built-in type string as key for value; define your own type to avoid collisions
serviceName: super-player-svr  issueKey: AYSkjDI6QgS5eBhck6jQ  errInfo: SA1029: should not use built-in type string as key for value; define your own type to avoid collisions
serviceName: super-player-svr  issueKey: AYSkjDI6QgS5eBhck6jR  errInfo: SA1029: should not use built-in type string as key for value; define your own type to avoid collisions
serviceName: super-player-svr  issueKey: AYSkjDI6QgS5eBhck6jS  errInfo: SA1029: should not use built-in type string as key for value; define your own type to avoid collisions
serviceName: super-player-svr  issueKey: AYSkjDI6QgS5eBhck6jT  errInfo: SA1029: should not use built-in type string as key for value; define your own type to avoid collisions
serviceName: super-player-svr  issueKey: AYSkjDI6QgS5eBhck6jU  errInfo: SA1029: should not use built-in type string as key for value; define your own type to avoid collisions
serviceName: super-player-svr  issueKey: AYSkjDI6QgS5eBhck6jW  errInfo: SA1029: should not use built-in type string as key for value; define your own type to avoid collisions
serviceName: super-player-svr  issueKey: AYSkjDI6QgS5eBhck6jX  errInfo: SA1029: should not use built-in type string as key for value; define your own type to avoid collisions
serviceName: super-player-svr  issueKey: AYSkjDI6QgS5eBhck6jV  errInfo: SA1029: should not use built-in type string as key for value; define your own type to avoid collisions
serviceName: gold-commission  issueKey: AYMbIdDv3YHuYDVTxFpt  errInfo: SA1029: should not use built-in type string as key for value; define your own type to avoid collisions

2022-12-15 18:36:20 start:

2022-12-15 18:37:17 start:

2022-12-15 18:40:20 start:

2022-12-15 18:41:13 start:
