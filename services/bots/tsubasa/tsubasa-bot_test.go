package main

import (
	"context"
	"fmt"
	"github.com/larksuite/botframework-go/SDK/appconfig"
	"github.com/larksuite/botframework-go/SDK/auth"
	"github.com/larksuite/botframework-go/SDK/chat"
	"github.com/larksuite/botframework-go/SDK/common"
	"github.com/larksuite/botframework-go/SDK/message"
	"github.com/larksuite/botframework-go/SDK/protocol"
	"golang.52tt.com/pkg/log"
	"io"

	/*	"golang.52tt.com/services/bots/push/handler_event"*/
	"golang.52tt.com/services/bots/tsubasa/app"
	"os"
	"testing"
)

const (
	yuantingopenid = "ou_3126459cb6cd89a83c3dce1f41510187"
	haiboopenid    = "ou_07eefc2770d93af078727ab5b11fb7f6"
	haibomsgid     = "om_7209c1587c0393e100b1d13d7dc4971a"
)

type pushCardContext struct {
	//Request   *handler_event.PushRequest `json:"request"`
	TaskID    string `json:"task_id"`
	PushAt    uint64 `json:"push_at"`
	Total     uint64 `json:"total"`
	Confirmed bool   `json:"confirmed"`
}

var gconf appconfig.AppConfig = appconfig.AppConfig{
	AppID:       "cli_9f7e92672032500c",
	AppType:     protocol.InternalApp, // Independent Software Vendor App / Internal App
	AppSecret:   "eN64Os34Ny4dPjEaSUINmfB8r7mqHEx6",
	VerifyToken: "FgNOtzHJfLP5WwN0Qdd49b3fmZOmf7YA",
	EncryptKey:  "EuiSYErzlMnTA2sTPt5O9ekFUDgxCKtE",
}

func TestFs(t *testing.T) {
	conf := appconfig.AppConfig{
		AppID:       "cli_9f7e92672032500c",
		AppType:     protocol.InternalApp, // Independent Software Vendor App / Internal App
		AppSecret:   "eN64Os34Ny4dPjEaSUINmfB8r7mqHEx6",
		VerifyToken: "FgNOtzHJfLP5WwN0Qdd49b3fmZOmf7YA",
		EncryptKey:  "EuiSYErzlMnTA2sTPt5O9ekFUDgxCKtE",
	}

	appconfig.Init(conf)
	ctx := context.Background()
	token, err := auth.GetAppAccessToken(ctx, "cli_9f7e92672032500c")
	if err != nil {
		log.Errorf("GetAppAccessToken err: %s", err.Error())
		return
	}
	const (
		batget   = "/open-apis/contact/v1/user/batch_get"
		scopeget = "/open-apis/contact/v1/scope/get"
	)

	//rspBytes, _, err := common.DoHttpGetOApi(scopeget, common.NewHeaderToken(token), nil)
	rspBytes, _, err := common.DoHttpGetOApi(batget, common.NewHeaderToken(token), map[string]string{"open_ids": yuantingopenid})
	log.Infof("rspBytes:%+s", string(rspBytes))

	/*card */
	//card, _ := handler_event.buildCardMessage(&pushCardContext{})
	//target := &protocol.UserInfo{ID: "ou_07eefc2770d93af078727ab5b11fb7f6", Type: protocol.UserTypeOpenID}
	//message.SendCardMessage(
	//	ctx,
	//	"2d51aa38a2cf175e",
	//	"cli_9f7e92672032500c",
	//	target,
	//	"om_7209c1587c0393e100b1d13d7dc4971a",
	//	*card, true,
	//)

	/*var m = url.Values{}
	m.Add("open_ids", "ou_c53ffca480b6993af0c762e0bb2dc5c2")
	request, err := http.NewRequest("GET", "https://open.feishu.cn/open-apis/contact/v1/user/batch_get?"+m.Encode(), nil)
	if err != nil {
		log.Errorln(err)
	}
	ctx := context.Background()
	token, err := auth.GetAppAccessToken(ctx, "cli_9f7e92672032500c")
	if err != nil {
		log.Errorf("GetAppAccessToken err: %s", err.Error())
		return
	}
	log.Infof("token: %s", token)
	request.Header.Set("Authorization", "Bearer "+token)

	resp, err := http.DefaultClient.Do(request)
	if err != nil {
		log.Errorf("Failed to get vivo statis %+v", err)
		return
	}

	defer resp.Body.Close()
	out, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		log.Errorf("Failed to saveMsg %+v", err)
		return
	}
	log.Infof("get vivo statis resp %s", string(out))*/

}

/*
export appid="cli_9f7e92672032500c"
export tenantkey="2d51aa38a2cf175e"
export chatid="oc_2162839560be840f571cce636c7c9af4"
export openid="ou_07eefc2770d93af078727ab5b11fb7f6"
export appsecret="eN64Os34Ny4dPjEaSUINmfB8r7mqHEx6"
export verifytoken="FgNOtzHJfLP5WwN0Qdd49b3fmZOmf7YA"
export encryptkey="EuiSYErzlMnTA2sTPt5O9ekFUDgxCKtE"

*/
func TestCard(t *testing.T) {
	conf := appconfig.AppConfig{
		AppID:       "cli_9f7e92672032500c",
		AppType:     protocol.InternalApp, // Independent Software Vendor App / Internal App
		AppSecret:   "eN64Os34Ny4dPjEaSUINmfB8r7mqHEx6",
		VerifyToken: "FgNOtzHJfLP5WwN0Qdd49b3fmZOmf7YA",
		EncryptKey:  "EuiSYErzlMnTA2sTPt5O9ekFUDgxCKtE",
	}

	appconfig.Init(conf)
	ctx := context.Background()
	token, err := auth.GetAppAccessToken(ctx, "cli_9f7e92672032500c")
	if err != nil {
		log.Errorf("GetAppAccessToken err: %s", err.Error())
		return
	}
	log.Debugf("token: %+v,appid:%s", token, os.Getenv("appid"))

	const (
		batget   = "/open-apis/contact/v1/user/batch_get"
		scopeget = "/open-apis/contact/v1/scope/get"
	)

	/*card */
	/*builder := &message.CardBuilder{}
	//add header
	content := "selectStaticMenu demo"
	line := 1
	title := protocol.TextForm{
		Tag:     protocol.PLAIN_TEXT_E,
		Content: &content,
		Lines:   &line,
	}
	builder.AddHeader(title, "")
	builder.AddHRBlock()
	//add config
	config := protocol.ConfigForm{
		MinVersion:     protocol.VersionForm{},
		WideScreenMode: true,
	}
	builder.SetConfig(config)
	//add block
	params := map[string]string{}
	content1 := "option1"
	content2 := "option2"
	content3 := "option3"
	option1 := message.NewOption(protocol.TextForm{Tag: "plain_text", Content: &content1}, "option1")
	option2 := message.NewOption(protocol.TextForm{Tag: "plain_text", Content: &content2}, "option2")
	option3 := message.NewOption(protocol.TextForm{Tag: "plain_text", Content: &content3}, "option3")
	options := []protocol.OptionForm{option1, option2, option3}
	initOption := "test"
	menu := message.NewSelectStaticMenu(message.NewMDText("default", nil, nil, nil),
		params, options, &initOption, nil, "selectStaticMenu")
	builder.AddDIVBlock(message.NewMDText("", nil, nil, nil), nil, menu)
	builder.AddHRBlock()
	card, err := builder.BuildForm()

	user := &protocol.UserInfo{
		ID:   "ou_07eefc2770d93af078727ab5b11fb7f6",
		Type: protocol.UserTypeOpenID,
	}

	resp, err := message.SendCardMessage(ctx, "2d51aa38a2cf175e", "cli_9f7e92672032500c", user, "", *card, false)
	if err != nil {
		t.Errorf("TestSelectStaticMenu failed:%v\n", err)
	} else {
		t.Logf("code:%d ,msg: %s ,openMessageID: %s\n", resp.Code, resp.Msg, resp.Data.MessageID)
	}*/

	/*builder := &message.CardBuilder{}
	//add header
	content := "selectPersonMenu demo"
	line := 1
	title := protocol.TextForm{
		Tag:     protocol.PLAIN_TEXT_E,
		Content: &content,
		Lines:   &line,
	}
	builder.AddHeader(title, "")
	builder.AddHRBlock()
	//add config
	config := protocol.ConfigForm{
		MinVersion:     protocol.VersionForm{},
		WideScreenMode: true,
	}
	builder.SetConfig(config)
	//add block
	params := map[string]string{}
	options := []protocol.OptionForm{{}}
	menu := message.NewSelectPersonMenu(message.NewMDText("default", nil, nil, nil),
		params, options, nil, nil, "selectPersonMenu")
	builder.AddDIVBlock(message.NewMDText("", nil, nil, nil), nil, menu)
	builder.AddHRBlock()
	card, err := builder.BuildForm()

	user := &protocol.UserInfo{
		ID:   "ou_07eefc2770d93af078727ab5b11fb7f6",
		Type: protocol.UserTypeOpenID,
	}

	resp, err := message.SendCardMessage(ctx, "2d51aa38a2cf175e", "cli_9f7e92672032500c", user, "", *card, false)
	if err != nil {
		t.Errorf("TestSelectPersonMenu failed:%v\n", err)
	} else {
		t.Logf("code:%d ,msg: %s ,openMessageID: %s\n", resp.Code, resp.Msg, resp.Data.MessageID)
	}*/
	/*builder := &message.CardBuilder{}
	//add header
	content := "PickerDate demo"
	line := 1
	title := protocol.TextForm{
		Tag:     protocol.PLAIN_TEXT_E,
		Content: &content,
		Lines:   &line,
	}
	builder.AddHeader(title, "")
	builder.AddHRBlock()
	//add config
	config := protocol.ConfigForm{
		MinVersion:     protocol.VersionForm{},
		WideScreenMode: true,
	}
	builder.SetConfig(config)
	//add block
	params := map[string]string{}
	initialDate := "2019-9-1"
	menu := NewPickerDatetime(message.NewMDText("default", nil, nil, nil),
		params, nil, &initialDate, "PickerDate")
	builder.AddDIVBlock(message.NewMDText("", nil, nil, nil), nil, menu)
	builder.AddHRBlock()
	card, err := builder.BuildForm()

	user := &protocol.UserInfo{
		ID:   "ou_07eefc2770d93af078727ab5b11fb7f6",
		Type: protocol.UserTypeOpenID,
	}

	resp, err := message.SendCardMessage(ctx, "2d51aa38a2cf175e", "cli_9f7e92672032500c", user, "", *card, false)
	if err != nil {
		t.Errorf("TestPickerDate failed:%v\n", err)
	} else {
		t.Logf("code:%d ,msg: %s ,openMessageID: %s\n", resp.Code, resp.Msg, resp.Data.MessageID)
	}*/
	builder := &message.CardBuilder{}
	//add header
	content := "Please choose color"
	line := 1
	title := protocol.TextForm{
		Tag:     protocol.PLAIN_TEXT_E,
		Content: &content,
		Lines:   &line,
	}
	builder.AddHeader(title, "")
	builder.AddHRBlock()
	//add config
	config := protocol.ConfigForm{
		MinVersion:     protocol.VersionForm{},
		WideScreenMode: true,
	}
	builder.SetConfig(config)
	//add block
	builder.AddDIVBlock(nil, []protocol.FieldForm{
		*message.NewField(false, message.NewMDText("**Async**", nil, nil, nil)),
	}, nil)
	payload1 := make(map[string]string, 0)
	payload1["color"] = "red"
	payload2 := make(map[string]string, 0)
	payload2["color"] = "black"
	builder.AddActionBlock([]protocol.ActionElement{
		message.NewButton(message.NewMDText("red11", nil, nil, nil),
			nil, nil, payload1, protocol.PRIMARY, nil, "asyncButton"),
		message.NewButton(message.NewMDText("black", nil, nil, nil),
			nil, nil, payload2, protocol.PRIMARY, nil, "asyncButton"),
	})
	card, err := builder.BuildForm()

	user := &protocol.UserInfo{
		ID:   "ou_07eefc2770d93af078727ab5b11fb7f6",
		Type: protocol.UserTypeOpenID,
	}

	resp, err := message.SendCardMessage(ctx, "2d51aa38a2cf175e", "cli_9f7e92672032500c", user, "", *card, false)
	if err != nil {
		t.Errorf("TestAsyncButton failed:%v\n", err)
	} else {
		t.Logf("code:%d ,msg: %s ,openMessageID: %s\n", resp.Code, resp.Msg, resp.Data.MessageID)
	}
}
func NewPickerDatetime(placeHolder *protocol.TextForm, params map[string]string,
	confirm *protocol.ConfirmForm, initialDatetime *string, method string) *protocol.PickerDatetimeForm {
	form := &protocol.PickerDatetimeForm{}
	form.Tag = protocol.PICKERDATETIME_E
	form.Placeholder = placeHolder
	form.Params = params
	form.InitialDatetime = initialDatetime
	form.Confirm = confirm
	form.SetAction(method, *protocol.NewMeta())
	return form
}

func TestGetChatList(t *testing.T) {
	ctx := context.Background()
	//token, err := auth.GetAppAccessToken(ctx, "cli_9f7e92672032500c")
	//if err != nil {
	//	log.Errorf("GetAppAccessToken err: %s", err.Error())
	//	return
	//}
	conf := appconfig.AppConfig{
		AppID:       "cli_9f7e92672032500c",
		AppType:     protocol.InternalApp, // Independent Software Vendor App / Internal App
		AppSecret:   "eN64Os34Ny4dPjEaSUINmfB8r7mqHEx6",
		VerifyToken: "FgNOtzHJfLP5WwN0Qdd49b3fmZOmf7YA",
		EncryptKey:  "EuiSYErzlMnTA2sTPt5O9ekFUDgxCKtE",
	}

	appconfig.Init(conf)
	resp, err := chat.GetChatList(ctx, "2d51aa38a2cf175e", app.APPID, 20, "")
	if err != nil {
		t.Errorf("GetChatList failed: %v", err)
	} else {
		t.Logf("GetChatList hasMore[%v]pageToken[%s]groupNum[%d]", resp.Data.HasMore, resp.Data.PageToken, len(resp.Data.Groups))
		for k, v := range resp.Data.Groups {
			t.Logf("GetChatList chatList %d:%+v\n", k+1, *v)
		}
	}
}
func TestUploadFile(t *testing.T) {
	conf := appconfig.AppConfig{
		AppID:       "cli_9f7e92672032500c",
		AppType:     protocol.InternalApp, // Independent Software Vendor App / Internal App
		AppSecret:   "eN64Os34Ny4dPjEaSUINmfB8r7mqHEx6",
		VerifyToken: "FgNOtzHJfLP5WwN0Qdd49b3fmZOmf7YA",
		EncryptKey:  "EuiSYErzlMnTA2sTPt5O9ekFUDgxCKtE",
	}

	appconfig.Init(conf)
	ctx := context.Background()
	token, err := auth.GetAppAccessToken(ctx, "cli_9f7e92672032500c")
	if err != nil {
		log.Errorf("GetAppAccessToken err: %s", err.Error())
		return
	}
	fmt.Println("token ", token)

	/* 上传文件 */
	fp, err := os.Open("2022-01-01至2022-02-01 TTCLUB报名统计表.xls")
	if err != nil {
		return
	}
	defer fp.Close()
	buff := make([]byte, 0, 10240)

	for {
		len, err := fp.Read(buff)
		if err == io.EOF || len <= 0 {
			break
		}
	}
	log.Infof("to upload")
	departmentdetail, _, err := common.DoHttpPostOApi("/open-apis/im/v1/files", common.NewHeaderToken(token),
		map[string]interface{}{"file_type": "xls", "file_name": "2022-01-01至2022-02-01 TTCLUB报名统计表.xls", "file": buff})
	log.Infof("departmentdetail:%+s,err:%+v", string(departmentdetail), err)

}

//curl --location --request POST 'https://open.feishu.cn/open-apis/im/v1/files' \
//--header 'Authorization: Bearer t-1803e867f0721be12fbe4f524585b149eae35542' \
//--form 'file_type="xls"' \
//--form 'file=@"2022-01-01至2022-02-01 TTCLUB报名统计表.xls"'

/*查看群成员、将成员写入成员表*/
func TestGetChatUserList(t *testing.T) {
	ctx := context.Background()
	appconfig.Init(gconf)
	resp, err := chat.GetChatInfo(ctx, "2d51aa38a2cf175e", app.APPID, "oc_e3d58f2cc354ac686db5904c1930323c")
	if err != nil {
		t.Errorf("GetChatList failed: %v", err)
	} else {
		log.Infof("resp:%+v", resp)
	}
	token, err := auth.GetAppAccessToken(ctx, "cli_9f7e92672032500c")
	if err != nil {
		log.Errorf("GetAppAccessToken err: %s", err.Error())
		return
	}

	//rspBytes, _, err := common.DoHttpGetOApi("/open-apis/contact/v1/user/batch_get", common.NewHeaderToken(token),
	//	map[string]string{"open_ids": "ou_07eefc2770d93af078727ab5b11fb7f6"})
	//log.Infof("rspBytes:%+s", string(rspBytes))
	//
	//getdepartment, _, err := common.DoHttpGetOApi("/open-apis/contact/v3/departments", common.NewHeaderToken(token),
	//	nil)
	//log.Infof("getdepartment:%+s", string(getdepartment))

	/*查所有部门的所有人员信息*/
	departmentdetail, _, err := common.DoHttpGetOApi("/open-apis/contact/v1/department/user/detail/list", common.NewHeaderToken(token),
		map[string]string{"open_department_id": "od-7e96e33e91070782dc2d924b1bb1b904", "page_size": "80", "fetch_child": "true"})
	log.Infof("departmentdetail:%+s", string(departmentdetail))

	//UserID:cb2d423d
	//departmentdetail, _, err := common.DoHttpGetOApi("/open-apis/contact/v3/users/:ou_07eefc2770d93af078727ab5b11fb7f6", common.NewHeaderToken(token),
	//	map[string]string{"user_id_type": "user_id"})
	//log.Infof("departmentdetail:%+s", string(departmentdetail))

	/*0:第一级*/
	//departmentdetail, _, err := common.DoHttpGetOApi("/open-apis/contact/v1/department/simple/list", common.NewHeaderToken(token),
	//	map[string]string{"open_department_id": "0", "page_size": "10"})
	//log.Infof("departmentdetail:%+s", string(departmentdetail))

}
