package mgr

import (
	"errors"
	"golang.52tt.com/services/rank/channel-online-rank/internal/rank-v2/cache"
	mocks2 "golang.52tt.com/services/rank/channel-online-rank/internal/rank-v2/mocks"
	"golang.52tt.com/services/rank/channel-online-rank/internal/rank-v2/model"
	"golang.52tt.com/services/rank/channel-online-rank/internal/rank-v2/report"
	"golang.52tt.com/services/rank/channel-online-rank/internal/rpc"
	"testing"

	"github.com/golang/mock/gomock"
	mockchannel "golang.52tt.com/clients/mocks/channel"
	mockchannelmemberviprank "golang.52tt.com/clients/mocks/channelmemberVipRank"
	mockchannelol "golang.52tt.com/clients/mocks/channelol"
	mocknob "golang.52tt.com/clients/mocks/nobility"
	ga "golang.52tt.com/protocol/app/channel"
	channellivelogicpb "golang.52tt.com/protocol/app/channel-live-logic"
	channelmemberviprank "golang.52tt.com/protocol/services/channelmemberVipRank"
	channelsvrpb "golang.52tt.com/protocol/services/channelsvr"
	"golang.52tt.com/protocol/services/knightgroupmembers"
	kafkapresentPB "golang.52tt.com/protocol/services/minToolkit/kafka/pb/kafkapresent"
	kafkatbeanPB "golang.52tt.com/protocol/services/minToolkit/kafka/pb/kafkatbean"
	"golang.52tt.com/protocol/services/youknowwho"
)

// go test -timeout 30s -run ^TestRankV2Manager_HandlePresentEvent2$ golang.52tt.com/services/rank/channel-online-rank/rank-v2/mgr -v -count=1
func TestRankV2Manager_HandlePresentEvent2(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()
	mockModel := mocks2.NewMockIRankV2Store(ctl)
	mockcache := mocks2.NewMockIRedisCache(ctl)

	gomock.InOrder(
		//GetOrderCount
		mockModel.EXPECT().RecordChannelRankPresentLog(gomock.Any()).Return(nil),
		mockcache.EXPECT().IncrMemberScore(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(false, nil),
		mockcache.EXPECT().IncrRankDayRecord(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(errors.New("")),
	)

	type fields struct {
		cache cache.IRedisCache
		model model.IRankV2Store
	}
	type args struct {
		event   *kafkapresentPB.PresentEvent
		rankUid uint32
		isDelay bool
	}
	tests := []struct {
		name   string
		fields fields
		args   args
	}{
		{
			fields: fields{
				cache: mockcache,
				model: mockModel,
			},
			args: args{
				event: &kafkapresentPB.PresentEvent{
					Uid:         1,
					ChannelId:   1,
					ChannelType: uint32(ga.ChannelType_RADIO_LIVE_CHANNEL_TYPE),
				},
				rankUid: 2,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &RankV2Manager{
				cache:  tt.fields.cache,
				model:  tt.fields.model,
				report: report.NewFeiShuReporterV2("", false),
			}
			m.HandlePresentEvent(tt.args.event, tt.args.rankUid, tt.args.isDelay)
		})
	}
}

// go test -timeout 30s -run ^TestRankV2Manager_HandlePresentEvent$ golang.52tt.com/services/rank/channel-online-rank/rank-v2/mgr -v -count=1
func TestRankV2Manager_HandlePresentEvent(t *testing.T) {
	return

	ctl := gomock.NewController(t)
	defer ctl.Finish()
	mockModel := mocks2.NewMockIRankV2Store(ctl)
	mockcache := mocks2.NewMockIRedisCache(ctl)

	mockchannelcli := mockchannelmemberviprank.NewMockIClient(ctl)

	mocknobcli := mocknob.NewMockIClient(ctl)
	rpc.NobCli = mocknobcli

	mockchannelolcli := mockchannelol.NewMockIClient(ctl)
	rpc.ChannelOlCli = mockchannelolcli

	info := uint32(0)
	totalconsu := &channelmemberviprank.MemberConsumeInfo{}
	gomock.InOrder(
		//GetOrderCount
		mockModel.EXPECT().RecordChannelRankPresentLog(gomock.Any()).Return(nil),
		mockcache.EXPECT().IncrMemberScore(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(false, nil),
		mockcache.EXPECT().IncrRankDayRecord(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil),

		mockcache.EXPECT().AddWeekConsume(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil),
		mockcache.EXPECT().InitActivityScore(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil),

		mockcache.EXPECT().GetRankDayRecord(gomock.Any(), gomock.Any(), gomock.Any()).Return(true, info, nil),
		mocknobcli.EXPECT().GetNobilityInfo(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil),
		mockchannelcli.EXPECT().GetUserConsumeInfo(gomock.Any(), gomock.Any(), gomock.Any()).Return(totalconsu, nil),
		mockchannelolcli.EXPECT().GetUserChannelId(gomock.Any(), gomock.Any(), gomock.Any()).Return(uint32(1), nil),

		mockcache.EXPECT().AddMember(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil),
		//mockcache.EXPECT().SaveRankDayRecord(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(errors.New("1")),
	)

	type fields struct {
		cache cache.IRedisCache
		model model.IRankV2Store
	}
	type args struct {
		event   *kafkapresentPB.PresentEvent
		rankUid uint32
		isDelay bool
	}
	tests := []struct {
		name   string
		fields fields
		args   args
	}{
		{
			fields: fields{
				cache: mockcache,
				model: mockModel,
			},
			args: args{
				event: &kafkapresentPB.PresentEvent{
					Uid:         1,
					ChannelId:   1,
					ChannelType: uint32(ga.ChannelType_RADIO_LIVE_CHANNEL_TYPE),
				},
				rankUid: 1,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &RankV2Manager{
				cache: tt.fields.cache,
				model: tt.fields.model,
			}
			m.HandlePresentEvent(tt.args.event, tt.args.rankUid, tt.args.isDelay)
		})
	}
}

// go test -timeout 30s -run ^TestRankV2Manager_HandleUkwEvent$ golang.52tt.com/services/rank/channel-online-rank/rank-v2/mgr -v -count=1
func TestRankV2Manager_HandleUkwEvent(t *testing.T) {

	ctl := gomock.NewController(t)

	defer ctl.Finish()

	mockcache := mocks2.NewMockIRedisCache(ctl)

	mockchannelcli := mockchannel.NewMockIClient(ctl)
	rpc.ChannelCli = mockchannelcli

	mocknobcli := mocknob.NewMockIClient(ctl)
	rpc.NobCli = mocknobcli

	ctype := uint32(ga.ChannelType_RADIO_LIVE_CHANNEL_TYPE)
	channelinfoo := &channelsvrpb.ChannelSimpleInfo{
		ChannelType: &ctype,
	}
	ukwtodayConsume, ukwtotalConsume := uint32(10), uint32(100)

	info := uint32(0)

	ev := &youknowwho.UKWChangeStatusMsg{
		MsgType: youknowwho.UKWMsgType_ENUM_USER_EXPOSURE_UKW_TYPE,
	}

	gomock.InOrder(
		mockchannelcli.EXPECT().GetChannelSimpleInfo(gomock.Any(), gomock.Any(), gomock.Any()).Return(channelinfoo, nil),
		mockcache.EXPECT().GetRankScore(gomock.Any(), gomock.Any(), gomock.Any()).Return(ukwtodayConsume, ukwtotalConsume, nil),
		mockcache.EXPECT().GetRankDayRecord(gomock.Any(), gomock.Any(), gomock.Any()).Return(true, info, nil),
		mocknobcli.EXPECT().GetNobilityInfo(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil),

		mockcache.EXPECT().DelMember(gomock.Any(), gomock.Any(), gomock.Any()).Return(true, nil),
		mockcache.EXPECT().AddMember(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil),
		mockcache.EXPECT().SaveRankDayRecord(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(errors.New("")),
	)

	type fields struct {
		cache cache.IRedisCache
		model model.IRankV2Store
	}
	type args struct {
		event *youknowwho.UKWChangeStatusMsg
	}
	tests := []struct {
		name   string
		fields fields
		args   args
	}{
		{
			fields: fields{
				cache: mockcache,
			},
			args: args{
				event: ev,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &RankV2Manager{
				cache: tt.fields.cache,
				model: tt.fields.model,
			}
			m.HandleUkwEvent(tt.args.event)
		})
	}
}

// go test -timeout 30s -run ^TestRankV2Manager_HandleChannelLiveEvent$ golang.52tt.com/services/rank/channel-online-rank/rank-v2/mgr -v -count=1
func TestRankV2Manager_HandleChannelLiveEvent(t *testing.T) {

	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockcache := mocks2.NewMockIRedisCache(ctl)

	ev := &channellivelogicpb.ChannelLiveKafkaEvent{
		ChannelLiveStatus: channellivelogicpb.EnumChannelLiveStatus_CLOSE,
	}
	gomock.InOrder(
		mockcache.EXPECT().DelRank(gomock.Any(), gomock.Any()).Return(true, nil),
		mockcache.EXPECT().DelRankDayRecord(gomock.Any(), gomock.Any()).Return(true, nil),
	)

	type fields struct {
		cache cache.IRedisCache
		model model.IRankV2Store
	}
	type args struct {
		event *channellivelogicpb.ChannelLiveKafkaEvent
	}
	tests := []struct {
		name   string
		fields fields
		args   args
	}{
		{
			fields: fields{
				cache: mockcache,
			},
			args: args{
				event: ev,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &RankV2Manager{
				cache: tt.fields.cache,
				model: tt.fields.model,
			}
			m.HandleChannelLiveEvent(tt.args.event)
		})
	}
}

// go test -timeout 30s -run ^TestRankV2Manager_HandleKnightEvent$ golang.52tt.com/services/rank/channel-online-rank/rank-v2/mgr -v -count=1
func TestRankV2Manager_HandleKnightEvent(t *testing.T) {

	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockModel := mocks2.NewMockIRankV2Store(ctl)
	mockcache := mocks2.NewMockIRedisCache(ctl)

	ev := &knightgroupmembers.JoinKnightGroupEvent{
		ChannelId: 1,
		Price:     22,
	}

	gomock.InOrder(
		mockModel.EXPECT().RecordChannelRankPresentLog(gomock.Any()).Return(nil),
		mockcache.EXPECT().IncrMemberScore(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(true, errors.New("")),
	)

	type fields struct {
		cache cache.IRedisCache
		model model.IRankV2Store
	}
	type args struct {
		event *knightgroupmembers.JoinKnightGroupEvent
	}
	tests := []struct {
		name   string
		fields fields
		args   args
	}{
		{
			fields: fields{
				cache: mockcache,
				model: mockModel,
			},
			args: args{
				event: ev,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &RankV2Manager{
				cache: tt.fields.cache,
				model: tt.fields.model,
			}
			m.HandleKnightEvent(tt.args.event)
		})
	}
}

// go test -timeout 30s -run ^TestRankV2Manager_HandleUkwEvent$ golang.52tt.com/services/rank/channel-online-rank/rank-v2/mgr -v -count=1
func TestRankV2Manager_HandleTbeanEvent(t *testing.T) {

	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockModel := mocks2.NewMockIRankV2Store(ctl)
	mockcache := mocks2.NewMockIRedisCache(ctl)

	gomock.InOrder(
		mockModel.EXPECT().RecordChannelRankPresentLog(gomock.Any()).Return(nil),
		mockcache.EXPECT().IncrMemberScore(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(true, errors.New("")),
	)

	type fields struct {
		cache    cache.IRedisCache
		model    model.IRankV2Store
		shutDown chan interface{}
		report   report.IFeishuReporterV2
	}
	type args struct {
		event       *kafkatbeanPB.TBeanConsumeEvent
		rankUid     uint32
		channelType uint32
		isDelay     bool
	}
	tests := []struct {
		name   string
		fields fields
		args   args
	}{
		{
			fields: fields{
				cache: mockcache,
				model: mockModel,
			},
			args: args{
				event: &kafkatbeanPB.TBeanConsumeEvent{
					Uid: 1,
				},
				rankUid:     2,
				channelType: 4,
				isDelay:     false,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &RankV2Manager{
				cache:    tt.fields.cache,
				model:    tt.fields.model,
				shutDown: tt.fields.shutDown,
			}
			m.HandleTbeanEvent(tt.args.event, tt.args.rankUid, tt.args.channelType, tt.args.isDelay)
		})
	}
}
