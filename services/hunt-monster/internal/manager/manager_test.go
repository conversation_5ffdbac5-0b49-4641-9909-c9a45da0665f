package manager

import (
	"context"

	"reflect"
	"testing"
	"time"

	"github.com/golang/mock/gomock"

	accountClient "golang.52tt.com/clients/account"
	"golang.52tt.com/clients/mocks/account"
	apicenter "golang.52tt.com/clients/mocks/apicenter/apiserver"
	"golang.52tt.com/clients/mocks/backpack"
	backpacksender "golang.52tt.com/clients/mocks/backpack-sender"
	userPresentPb "golang.52tt.com/protocol/services/userpresent"

	mock_channel "golang.52tt.com/clients/mocks/channel"
	channellivemgr "golang.52tt.com/clients/mocks/channel-live-mgr"
	channel_personalization "golang.52tt.com/clients/mocks/channel-personalization"
	"golang.52tt.com/clients/mocks/guild"
	headwear_go "golang.52tt.com/clients/mocks/headwearer"
	PushNotification "golang.52tt.com/clients/mocks/push-notification/v2"
	userPresent "golang.52tt.com/clients/mocks/userpresent"
	accountPb "golang.52tt.com/protocol/services/accountsvr"
	bpb "golang.52tt.com/protocol/services/backpacksender"
	backpackPb "golang.52tt.com/protocol/services/backpacksvr"
	clpb "golang.52tt.com/protocol/services/channellivemgr"
	channelPb "golang.52tt.com/protocol/services/channelsvr"
	channelpb "golang.52tt.com/protocol/services/channelsvr"
	guildPb "golang.52tt.com/protocol/services/guildsvr"
	pb "golang.52tt.com/protocol/services/huntmonster"
	"golang.52tt.com/services/hunt-monster/internal/conf"
)

func TestManager_ActivateMonster(t *testing.T) {
	controller := gomock.NewController(t)
	defer controller.Finish()

	type args struct {
		ctx     context.Context
		cid     uint32
		uid     uint32
		maxLife int64
	}
	tests := []struct {
		name     string
		args     args
		initFunc func(t *managerTestHelper)
	}{
		{
			name: "有密码",
			args: args{
				ctx:     context.Background(),
				cid:     2254093,
				uid:     2465920,
				maxLife: 44444,
			},
			initFunc: func(t *managerTestHelper) {
				hasPwd := true
				t.getChannelCli().EXPECT().GetChannelDetailInfo(gomock.Any(), uint32(2254093), uint32(2254093)).Return(&channelPb.GetChannelDetailInfoResp{
					ChannelBaseinfo: &channelPb.ChannelBaseInfo{
						HasPwd: &hasPwd,
					},
				}, nil)
			},
		},
		{
			name: "没密码 ",
			args: args{
				ctx:     context.Background(),
				cid:     2254093,
				uid:     2465920,
				maxLife: 44444,
			},
			initFunc: func(t *managerTestHelper) {
				hasPwd := false
				t.getChannelCli().EXPECT().GetChannelDetailInfo(gomock.Any(), uint32(2254093), uint32(2254093)).Return(&channelPb.GetChannelDetailInfoResp{
					ChannelBaseinfo: &channelPb.ChannelBaseInfo{
						HasPwd: &hasPwd,
					},
				}, nil)
				t.getCache().EXPECT().GenMonsterID(gomock.Any()).Return(int64(1025))
				t.getAccount().EXPECT().GetUser(gomock.Any(), uint32(2465920)).Return(&accountClient.User{
					Uid: 2465920,
				}, nil)
				t.getBusinessCfg().EXPECT().GetCountDown().Return(int64(60)).AnyTimes()
				t.getBusinessCfg().EXPECT().GetMonsterLiveSec().Return(int64(180)).AnyTimes()
				m := &pb.Monster{
					ChannelId: 2254093,
					TriggerUser: &pb.User{
						Uid: 2465920,
					},
					MonsterId:     1025,
					CurrLifePoint: 44444,
					MaxLifePoint:  44444,
					CreateTime:    time.Now().Unix(),
					IconUrl:       "",
					Status:        pb.MonsterStatus_IN_QUEUE,
					CountDown:     60,
					LiveTime:      240,
				}
				t.getDb().EXPECT().InsertGlobalMonsterInfo(gomock.Any(), m)
				t.getCache().EXPECT().PushMToQ(gomock.Any(), m, uint32(2254093)).Return(nil)
				t.getCache().EXPECT().AddChannelActivateCnt(gomock.Any(), uint32(2254093)).Return(int64(10), nil)
				t.getPushCli().EXPECT().PushMulticast(gomock.Any(), uint64(2254093), "2254093@channel", []uint32{}, gomock.Any()).Return(nil)
				t.getCache().EXPECT().GetCurrM(gomock.Any(), uint32(2254093)).Return(m, nil)
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			h := newManagerTestHelper(t)
			if tt.initFunc != nil {
				tt.initFunc(h)
			}
			h.ActivateMonster(tt.args.ctx, tt.args.cid, tt.args.uid, tt.args.maxLife)
		})
	}
}

func TestManager_GetUserInfos(t *testing.T) {
	controller := gomock.NewController(t)
	defer controller.Finish()
	accountCli := account.NewMockIClient(controller)
	accountCli.EXPECT().GetUsersMap(gomock.Any(), []uint32{2465920, 2465922}).Return(
		map[uint32]*accountPb.UserResp{
			2465920: {
				Uid: 2465920,
			},
			2465922: {
				Uid: 2465922,
			},
		}, nil,
	)
	mgr := NewManager(nil, nil, nil, accountCli, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil)
	type args struct {
		ctx     context.Context
		uidList []uint32
	}
	tests := []struct {
		name string
		m    *Manager
		args args
		want map[uint32]*accountClient.User
	}{
		{
			name: "GetUserInfos",
			m:    mgr,
			args: args{
				ctx:     context.Background(),
				uidList: []uint32{2465920, 2465922},
			},
			want: map[uint32]*accountPb.UserResp{
				2465920: {
					Uid: 2465920,
				},
				2465922: {
					Uid: 2465922,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := tt.m.GetUserInfos(tt.args.ctx, tt.args.uidList); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("Manager.GetUserInfos() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestManager_GetChannelDetailInfos(t *testing.T) {
	controller := gomock.NewController(t)
	defer controller.Finish()

	cacheMock := NewMockIHuntMonsterCache(controller)
	mockStore := NewMockIStore(controller)
	accountCli := account.NewMockIClient(controller)

	pushCli := PushNotification.NewMockIClient(controller)
	backpackCli := backpack.NewMockIClient(controller)
	personalCli := channel_personalization.NewMockIClient(controller)
	//tbeanCli := NewTbeanCli(controller)
	apiCli := apicenter.NewMockIClient(controller)
	headwearCli := headwear_go.NewMockIClient(controller)
	presentCli := userPresent.NewMockIClient(controller)
	channelCli := mock_channel.NewMockIClient(controller)
	hasPwd := false
	channelId1 := uint32(2254093)
	channelId2 := uint32(2254094)
	displayId := uint32(123)
	channelType := uint32(4)
	channelName := "小王的房间"
	anchorUid := uint32(2465920)
	channelCli.EXPECT().GetChannelDetailInfo(gomock.Any(), uint32(2254093), uint32(2254093)).Return(&channelPb.GetChannelDetailInfoResp{
		ChannelBaseinfo: &channelPb.ChannelBaseInfo{
			HasPwd:      &hasPwd,
			ChannelId:   &channelId1,
			DisplayId:   &displayId,
			ChannelName: &channelName,
		},
		ChannelBindType: &channelType,
		CreaterUid:      &anchorUid,
	}, nil).AnyTimes()
	channelCli.EXPECT().GetChannelDetailInfo(gomock.Any(), uint32(2254094), uint32(2254094)).Return(&channelPb.GetChannelDetailInfoResp{
		ChannelBaseinfo: &channelPb.ChannelBaseInfo{
			HasPwd:      &hasPwd,
			ChannelId:   &channelId2,
			DisplayId:   &displayId,
			ChannelName: &channelName,
		},
		ChannelBindType: &channelType,
		CreaterUid:      &anchorUid,
	}, nil).AnyTimes()
	bindId := uint32(153360)
	channelCli.EXPECT().GetChannelSimpleInfo(gomock.Any(), uint32(2254093), uint32(2254093)).Return(&channelPb.ChannelSimpleInfo{
		BindId: &bindId,
	}, nil)
	channelCli.EXPECT().GetChannelSimpleInfo(gomock.Any(), uint32(2254094), uint32(2254094)).Return(&channelPb.ChannelSimpleInfo{
		BindId: &bindId,
	}, nil)
	channelLiveCli := channellivemgr.NewMockIClient(controller)
	guildCli := guild.NewMockIClient(controller)
	guildCli.EXPECT().GetGuild(gomock.Any(), uint32(153360)).Return(&guildPb.GuildResp{ShortId: 7777}, nil).Times(2)
	bakcPackSenderCli := backpacksender.NewMockIClient(controller)
	bakcPackSenderCli.EXPECT().SendBackpackWithRiskControl(gomock.Any(), gomock.Any()).Return(&bpb.SendBackpackWithRiskControlResp{}, nil).AnyTimes()
	confMgr := conf.NewMockIBusinessConfManager(controller)
	confMgr.EXPECT().GetCountDown().Return(int64(60)).AnyTimes()
	confMgr.EXPECT().GetMonsterLiveSec().Return(int64(180)).AnyTimes()
	mgr := &Manager{
		cache:                cacheMock,
		db:                   mockStore,
		accountCli:           accountCli,
		pushCli:              pushCli,
		backPackCli:          backpackCli,
		personalizationCli:   personalCli,
		apiClient:            apiCli,
		headwearCli:          headwearCli,
		presentCli:           presentCli,
		ChannelCli:           channelCli,
		channelLiveCli:       channelLiveCli,
		guildCli:             guildCli,
		backpackSenderClient: bakcPackSenderCli,
	}
	type args struct {
		ctx         context.Context
		channelList []uint32
	}
	tests := []struct {
		name string
		m    *Manager
		args args
		want []*pb.ChannelInfo
	}{
		{
			name: "GetChannelDetailInfos",
			m:    mgr,
			args: args{
				ctx:         context.Background(),
				channelList: []uint32{2254093, 2254094},
			},
			want: []*pb.ChannelInfo{
				{
					ChannelId:   2254093,
					DisplayId:   7777,
					ChannelType: 4,
					ChannelName: "小王的房间",
					AnchorUid:   2465920,
				},
				{
					ChannelId:   2254094,
					DisplayId:   7777,
					ChannelType: 4,
					ChannelName: "小王的房间",
					AnchorUid:   2465920,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := tt.m.GetChannelDetailInfos(tt.args.ctx, tt.args.channelList); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("Manager.GetChannelDetailInfos() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestManager_AttackM(t *testing.T) {
	controller := gomock.NewController(t)
	defer controller.Finish()

	ts := time.Now().Unix() - 70
	type args struct {
		ctx       context.Context
		uid       uint32
		cid       uint32
		cnt       int64
		monsterID int64
	}
	tests := []struct {
		name string

		args     args
		want     *pb.Monster
		want1    *pb.AttackResult
		wantErr  bool
		initFunc func(m *managerTestHelper)
	}{
		{
			name: "正常",
			args: args{
				ctx:       context.Background(),
				uid:       2465920,
				cid:       2254093,
				cnt:       1,
				monsterID: 1025,
			},
			want: &pb.Monster{
				MonsterId: 1000,
				ChannelId: 2254093,
				TriggerUser: &pb.User{
					Uid: 2465920,
				},
				MaxLifePoint: 1000,
				CreateTime:   ts,
				CountDown:    60,
				Status:       pb.MonsterStatus_KILLED,
			},
			want1: &pb.AttackResult{
				AttackValue: 10,
				TotalValue:  100,
				LeftItemCnt: 100,
				AttackRank:  1,
			},
			wantErr: false,
			initFunc: func(m *managerTestHelper) {
				monster := &pb.Monster{
					ChannelId: 2254093,
					TriggerUser: &pb.User{
						Uid: 2465920,
					},
					MonsterId:     1000,
					CurrLifePoint: 10,
					MaxLifePoint:  1000,
					CreateTime:    ts,
					CountDown:     60,
				}

				// 优化
				m.getCache().EXPECT().GetCurrM(gomock.Any(), uint32(2254093)).Return(monster, nil).AnyTimes()
				m.getBusinessCfg().EXPECT().GetCountDown().Return(int64(60)).AnyTimes()
				m.getBusinessCfg().EXPECT().GetMonsterLiveSec().Return(int64(180)).AnyTimes()
				m.getBusinessCfg().EXPECT().GetAttackNum().Return(float64(10)).Times(2)
				m.getBusinessCfg().EXPECT().GetDefeatAward().Return(&conf.MonsterAward{
					Id:  "1221",
					Ty:  1,
					Cnt: 1,
				}).AnyTimes()
				m.getBackpackCli().EXPECT().GetPackageItemCfg(gomock.Any(), uint32(1221), &backpackPb.GetPackageItemCfgReq{BgId: 1221}).Return(
					&backpackPb.GetPackageItemCfgResp{
						ItemCfgList: []*backpackPb.PackageItemCfg{
							{
								BgItemId:  1638,
								BgId:      1221,
								ItemType:  1,
								SourceId:  440,
								ItemCount: 1,
								IsDel:     false,
							},
						},
					}, nil,
				)
				m.getPresentCli().EXPECT().GetPresentConfigById(gomock.Any(), uint32(440)).Return(
					&userPresentPb.GetPresentConfigByIdResp{
						ItemConfig: &userPresentPb.StPresentItemConfig{
							ItemId:    440,
							Name:      "小拳拳",
							Price:     10,
							PriceType: 2,
						},
					}, nil,
				)

				m.getBusinessCfg().EXPECT().GetReportURl().Return("xx").AnyTimes()

				currM := &pb.Monster{
					MonsterId: 1000,
					ChannelId: 2254093,
					TriggerUser: &pb.User{
						Uid: 2465920,
					},
					CurrLifePoint: 10,
					MaxLifePoint:  1000,
					CreateTime:    ts,
					CountDown:     60,
				}
				m.getCache().EXPECT().AttackM(gomock.Any(), uint32(2254093), uint32(2465920), int64(1), currM).DoAndReturn(func(ctx context.Context, cid, uid uint32, cnt int64, currM *pb.Monster) (*pb.AttackResult, error) {
					currM.CurrLifePoint = currM.CurrLifePoint - cnt*10
					return &pb.AttackResult{
						AttackValue: 10,
						AttackRank:  1,
						LeftItemCnt: 100,
					}, nil
				})

				m.getCache().EXPECT().AddToAttackRank(gomock.Any(), uint32(2254093), uint32(2465920), uint32(10), int64(1000)).Return(float64(100), nil)
				m.getCache().EXPECT().GetAttackRank(gomock.Any(), uint32(2254093), uint32(2465920), int64(1000)).Return(int64(1), nil)
				m.getPushCli().EXPECT().PushMulticast(gomock.Any(), uint64(2254093), "2254093@channel", []uint32{}, gomock.Any()).Return(nil).AnyTimes()

				m.getCache().EXPECT().IncrKeyForDoOnce(gomock.Any(), "monster_award_1000").Return(int64(1), nil)
				m.getCache().EXPECT().MonsterLockKey(uint32(2254093)).Return("monster-2254093")
				m.getCache().EXPECT().Lock(gomock.Any(), "monster-2254093", time.Second*8).Return(true)
				m.getCache().EXPECT().UnLock(gomock.Any(), "monster-2254093").Return(nil)
				m.getCache().EXPECT().GetAttackRankByPage(gomock.Any(), uint32(2254093), int64(1000), int64(0), int64(Step-1)).Return(
					map[uint32]int64{
						2254093: 50,
						2254094: 60,
					}, nil,
				)
				m.getAccount().EXPECT().GetUsersMap(gomock.Any(), gomock.Any()).Return(
					map[uint32]*accountPb.UserResp{
						2254093: {Uid: 2254093},
						2254094: {Uid: 2254094},
					}, nil,
				)

				tmpM := *currM
				tmpM.CurrLifePoint = 0
				tmpM.Status = pb.MonsterStatus_KILLED

				m.getDb().EXPECT().BatchRecordAward(gomock.Any(), gomock.Any(), &tmpM, true).Return(nil)
				//m.getDb().EXPECT().RecordAward(gomock.Any(), uint32(2254093), &tmpM, int64(5), int64(50), true).Return(true, nil)
				//m.getDb().EXPECT().RecordAward(gomock.Any(), uint32(2254094), &tmpM, int64(6), int64(60), true).Return(true, nil)

				m.getCache().EXPECT().GetAttackRankByPage(gomock.Any(), uint32(2254093), int64(1000), int64(0), int64(2)).Return(
					map[uint32]int64{}, nil,
				)
				m.getCache().EXPECT().DelCurrM(gomock.Any(), uint32(2254093)).Return(nil)
				m.getCache().EXPECT().PopMFromQ(gomock.Any(), uint32(2254093)).Return(nil)
				m.getDb().EXPECT().UpdateMonsterInfo(gomock.Any(), int(2), monster)

				m.getChannelCli().EXPECT().GetChannelDetailInfo(gomock.Any(), uint32(2254093), uint32(2254093)).Return(
					&channelpb.GetChannelDetailInfoResp{
						ChannelBaseinfo: &channelpb.ChannelBaseInfo{
							DisplayId: new(uint32),
						},
					}, nil,
				)

				m.getDb().EXPECT().AddItemLog(gomock.Any(), uint32(2465920), uint32(2254093), uint32(100), int32(-1)).Return(nil)
				m.getCache().EXPECT().RemoveFinishMonster(gomock.Any(), uint32(2254093)).Return(nil)
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := newManagerTestHelper(t)
			if tt.initFunc != nil {
				tt.initFunc(m)
			}
			got, got1, err := m.AttackM(tt.args.ctx, tt.args.uid, tt.args.cid, tt.args.cnt, tt.args.monsterID)
			if (err != nil) != tt.wantErr {
				t.Errorf("Manager.AttackM() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("Manager.AttackM() got = %v, want %v", got, tt.want)
			}
			if !reflect.DeepEqual(got1, tt.want1) {
				t.Errorf("Manager.AttackM() got1 = %v, want %v", got1, tt.want1)
			}
			time.Sleep(time.Millisecond * 200)
		})
	}
}

func TestManager_GetChannelLiveStatus(t *testing.T) {
	controller := gomock.NewController(t)
	defer controller.Finish()
	channelLiveCli := channellivemgr.NewMockIClient(controller)
	channelLiveCli.EXPECT().BatchGetChannelLiveStatus(gomock.Any(), clpb.BatchGetChannelLiveStatusReq{
		GetAll:      false,
		ChannelList: []uint32{2254093, 2254094},
	}).Return(
		&clpb.BatchGetChannelLiveStatusResp{
			ChannelLiveInfoList: []*clpb.ChannelLiveStatusInfo{
				{
					ChannelLiveStatus: &clpb.ChannelLiveStatus{
						Uid: 2465920,
					},
				},
				{
					ChannelLiveStatus: &clpb.ChannelLiveStatus{
						Uid: 2465922,
					},
				},
			},
		}, nil,
	)
	mgr := &Manager{
		cache:                nil,
		db:                   nil,
		BusinessCfg:          nil,
		accountCli:           nil,
		pushCli:              nil,
		backPackCli:          nil,
		personalizationCli:   nil,
		tbeanClient:          nil,
		apiClient:            nil,
		headwearCli:          nil,
		presentCli:           nil,
		ChannelCli:           nil,
		channelLiveCli:       channelLiveCli,
		guildCli:             nil,
		backpackSenderClient: nil,
	}
	type args struct {
		ctx         context.Context
		channelList []uint32
	}
	tests := []struct {
		name    string
		m       *Manager
		args    args
		want    []*clpb.ChannelLiveStatusInfo
		wantErr bool
	}{
		{
			name: "GetChannelLiveStatus",
			m:    mgr,
			args: args{
				ctx:         context.Background(),
				channelList: []uint32{2254093, 2254094},
			},
			want: []*clpb.ChannelLiveStatusInfo{
				{
					ChannelLiveStatus: &clpb.ChannelLiveStatus{
						Uid: 2465920,
					},
				},
				{

					ChannelLiveStatus: &clpb.ChannelLiveStatus{
						Uid: 2465922,
					},
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := tt.m.GetChannelLiveStatus(tt.args.ctx, tt.args.channelList)
			if (err != nil) != tt.wantErr {
				t.Errorf("Manager.GetChannelLiveStatus() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("Manager.GetChannelLiveStatus() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestManager_RefreshTopThree(t *testing.T) {

	type args struct {
		c     context.Context
		cid   uint32
		currM *pb.Monster
	}
	tests := []struct {
		name     string
		args     args
		initFunc func(m *managerTestHelper)
	}{
		{
			name: "topThree为空",
			args: args{
				c:   context.Background(),
				cid: 2254093,
				currM: &pb.Monster{
					MonsterId: 1000,
				},
			},
			initFunc: func(m *managerTestHelper) {
				m.getCache().EXPECT().GetAttackRankByPage(gomock.Any(), uint32(2254093), int64(1000), int64(0), int64(2)).Return(
					map[uint32]int64{}, nil,
				)
			},
		},
		{
			name: "topThree为空",
			args: args{
				c:   context.Background(),
				cid: 2254093,
				currM: &pb.Monster{
					ChannelId: 2254093,
					MonsterId: 1000,
				},
			},
			initFunc: func(m *managerTestHelper) {
				m.getCache().EXPECT().GetAttackRankByPage(gomock.Any(), uint32(2254093), int64(1000), int64(0), int64(2)).Return(
					map[uint32]int64{
						2465920: 100,
					}, nil,
				)
				m.getAccount().EXPECT().GetUsersMap(gomock.Any(), []uint32{2465920}).Return(
					map[uint32]*accountClient.User{
						2465920: {Uid: 2465920},
					}, nil,
				)
				m.getPushCli().EXPECT().PushMulticast(gomock.Any(), uint64(2254093), "2254093@channel", []uint32{}, gomock.Any()).Return(nil)
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := newManagerTestHelper(t)
			if tt.initFunc != nil {
				tt.initFunc(m)
			}
			m.RefreshTopThree(tt.args.c, tt.args.cid, tt.args.currM)
		})
	}
}
