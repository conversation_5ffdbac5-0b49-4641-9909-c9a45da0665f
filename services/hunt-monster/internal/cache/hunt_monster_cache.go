package cache

import (
	"context"
	"errors"
	"fmt"
	"strconv"
	"time"

	"gitlab.ttyuyin.com/avengers/tyr/compatible/proto"
	redis "gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/redis"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	lpb "golang.52tt.com/protocol/app/huntmonsterlogic"
	pb "golang.52tt.com/protocol/services/huntmonster"
	"golang.52tt.com/services/hunt-monster/internal/manager"
)

const (
	monsterIdkey = "hunt-monster-id"
	GTQ          = "hunt-monster-global-tick-queue" //定时器用于检查怪物过期
	GHM          = "hunt-monster-global-channel-v2" //全服当前有boss房间列表

	CID        = "cid"
	UID        = "uid"
	LC         = "curr_life"  //当前剩余生命值
	MC         = "max_life"   //最大生命值
	MID        = "monster_id" //全服唯一怪物ID
	CT         = "create_time"
	URL        = "icon_url"
	CD         = "count_down"
	CACHE_TIME = 200 * 24 * 3600 // 缓存过期时间 200天
)

func (c *Cache) userAwardRecordKey(uid uint32) string {
	activityID := c.businessCfg.GetActivityID()
	return fmt.Sprintf("user-award-recored-%v-%v", activityID, uid)
}

func (c *Cache) channelActRecordKey() string {
	activityID := c.businessCfg.GetActivityID()
	return fmt.Sprintf("channel-activate-rank-%v", activityID)
}

func (c *Cache) userHuntRecordKey(uid uint32) string {
	activityID := c.businessCfg.GetActivityID()
	return fmt.Sprintf("user-hunt-record-%v-%v", activityID, uid%100)
}

func (c *Cache) userItemKey(uid uint32) string {
	activityID := c.businessCfg.GetActivityID()
	return fmt.Sprintf("user-hunt-monster-item-v2-%v-%v", activityID, uid)
}

func (c *Cache) MonsterLockKey(cid uint32) string {
	return fmt.Sprintf("curr-hunt-monster-lock-%v", cid)
}

func (c *Cache) channelMonsterQueKey(cid uint32) string {
	return fmt.Sprintf("hunt-monster-channel-queue-%v", cid)
}

func (c *Cache) currMonsterKey(cid uint32) string {
	return fmt.Sprintf("hunt-monster-channel-current-%v", cid)
}

func (c *Cache) attackRankKey(cid uint32, monsterID int64) string {
	return fmt.Sprintf("monster-%v-%v", cid, monsterID)
}

func (c *Cache) Lock(ctx context.Context, key string, dura time.Duration) bool {
	result, err := c.cmder.SetNX(ctx, key, 1, dura).Result()
	if err != nil {
		log.ErrorWithCtx(ctx, "Lock failed to SetNX, key:%s, err:%v", key, err)
		return false
	}
	return result
}

func (c *Cache) UnLock(ctx context.Context, key string) error {
	err := c.cmder.Del(ctx, key).Err()
	if err != nil {
		log.ErrorWithCtx(ctx, "UnLock failed to Del,key:%s, err:%v", key, err)
		return err
	}
	return nil
}

func (c *Cache) GetMonsterChannelList(ctx context.Context) []uint32 {

	channels := make([]uint32, 0, 1024)
	results, err := c.cmder.ZRevRange(ctx, GHM, 0, 1024).Result()
	if err != nil {
		log.ErrorWithCtx(ctx, "GetMonsterChannelList failed to ZRevRange, err:%v", err)
		return channels
	}
	for _, r := range results {
		cid, err := strconv.ParseUint(r, 10, 32)
		if nil == err {
			channels = append(channels, uint32(cid))
		} else {
			log.ErrorWithCtx(ctx, "GetMonsterChannelList failed to ParseUint, r:%v, err:%v", r, err)
			continue
		}
	}

	return channels
}

func (c *Cache) GetCurrM(ctx context.Context, cid uint32) (*pb.Monster, error) {
	m := &pb.Monster{}
	results, err := c.cmder.HGetAll(ctx, c.currMonsterKey(cid)).Result()
	if nil != err {
		log.ErrorWithCtx(ctx, "GetCurrM failed to HGetAll, cid:%d, err:%v", cid, err)
		return m, err
	}

	for k, v := range results {
		if k == URL {
			m.IconUrl = v
			continue
		}

		val, err := strconv.ParseInt(v, 10, 64)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetCurrM failed to ParseInt, v:%v,err:%v", v, err)
			continue
		}
		if val < 0 {
			val = 0
		}
		switch k {
		case CID:
			m.ChannelId = uint32(val)
		case UID:
			m.TriggerUser = &pb.User{
				Uid: uint32(val),
			}
		case MID:
			m.MonsterId = val
		case MC:
			m.MaxLifePoint = val
		case LC:
			m.CurrLifePoint = val
		case CT:
			m.CreateTime = val
		case CD:
			m.CountDown = val
		default:
			log.DebugWithCtx(ctx, "GetCurrM get invalid k:%s", k)
		}
		m.LiveTime = m.CountDown + c.businessCfg.GetMonsterLiveSec()
	}
	return m, nil
}

func (c *Cache) DelCurrM(ctx context.Context, cid uint32) error {
	err := c.cmder.Del(ctx, c.currMonsterKey(cid)).Err()
	if err != nil {
		log.ErrorWithCtx(ctx, "DelCurrM failed to Del, cid:%d, err:%v", cid, err)
		return err
	}

	member := fmt.Sprintf("%v", cid)
	err = c.cmder.ZRem(ctx, GHM, member).Err()
	if err != nil {
		log.ErrorWithCtx(ctx, "DelCurrM failed to ZRem, cid:%d, err:%v", cid, err)
		return err
	}
	log.InfoWithCtx(ctx, "DelCurrM cid:%v", cid)
	return nil
}

func (c *Cache) SetCurrM(ctx context.Context, cid uint32, monster *pb.Monster, hasPwd bool) (string, error) {

	files := make(map[string]interface{})
	files[CID] = monster.ChannelId
	files[UID] = monster.TriggerUser.GetUid()
	files[MID] = monster.MonsterId
	files[MC] = monster.MaxLifePoint
	files[LC] = monster.CurrLifePoint
	files[CT] = monster.CreateTime
	files[URL] = monster.IconUrl
	files[CD] = monster.CountDown

	//加入全服当前有龙房间队列
	if !hasPwd {
		member := fmt.Sprintf("%v", cid)
		err := c.cmder.ZAdd(ctx, GHM, &redis.Z{
			Score:  float64(time.Now().Unix()),
			Member: member,
		}).Err()
		if err != nil {
			log.ErrorWithCtx(ctx, "SetCurrM failed to ZAdd, cid:%d, monster:%v, err:%v", cid, monster, err)
			return "", err
		}
	}

	_, err := c.cmder.HMSet(ctx, c.currMonsterKey(cid), files).Result()
	if err != nil {
		log.ErrorWithCtx(ctx, "SetCurrM failed to HMSet, cid:%d, monster:%v, err:%v", cid, monster, err)
		return "", err
	}
	log.InfoWithCtx(ctx, "SetCurrM cid:%v monster:%v", cid, *monster)
	return "", err
}

func (c *Cache) AttackM(ctx context.Context, cid, uid uint32, cnt int64, currM *pb.Monster) (*pb.AttackResult, error) {
	attackp := &pb.AttackResult{}

	monsterKey := c.currMonsterKey(cid) //当前房间monster hash
	pipe := c.cmder.Pipeline()
	defer func() {
		_, err := pipe.Exec(ctx)
		if err != nil {
			log.ErrorWithCtx(ctx, "AttackM failed to pipe.Exec, err:%v", err)
		}
		err = pipe.Close()
		if err != nil {
			log.ErrorWithCtx(ctx, "AttackM failed to pipe.Close, err:%v", err)
		}
	}()

	realAttackVal := cnt * int64(c.businessCfg.GetAttackNum())
	leftItemCnt, err := c.cmder.IncrBy(ctx, c.userItemKey(uid), -1*cnt).Result()
	if err != nil {
		log.ErrorWithCtx(ctx, "AttackM failed to IncrBy, err:%v", err)
	}
	_, err = c.cmder.IncrBy(ctx, c.userItemKey(0), -1*cnt).Result()
	if err != nil {
		log.ErrorWithCtx(ctx, "AttackM failed to IncrBy, err:%v", err)
	}

	// 道具数量不足，用多少补多少
	if leftItemCnt < 0 {
		pipe.IncrBy(ctx, c.userItemKey(uid), cnt)
		pipe.IncrBy(ctx, c.userItemKey(0), cnt)
		err = protocol.NewExactServerError(nil, -5796, "没有剩余道具")
		log.ErrorWithCtx(ctx, "AttackM HIncrBy leftItemCnt<0, uid:%d, monsterId:%d, err:%v",
			uid, currM.MonsterId, err)
		return attackp, err
	}

	newLifeCnt, err := c.cmder.HIncrBy(ctx, monsterKey, LC, -realAttackVal).Result()
	if nil != err {
		log.ErrorWithCtx(ctx, "AttackM HIncrBy err:%v", err)
		return attackp, err
	}

	if newLifeCnt < 0 {

		// 攻击前龙已经死了
		if newLifeCnt <= (-realAttackVal) {
			pipe.IncrBy(ctx, c.userItemKey(uid), cnt)
			pipe.IncrBy(ctx, c.userItemKey(0), cnt)
			err = protocol.NewExactServerError(nil, -5795, "来晚了，龙已被击败")
			log.ErrorWithCtx(ctx, "AttackM has be defeat uid:%v, monsterId:%d, cid:%v, err:%v",
				uid, currM.MonsterId, cid, err)
			return attackp, err

		} else { // 当龙的血量小于攻击血量时，龙被击杀，实际伤害值为龙的血量值； （龙当前血量： 30 攻击血量50， 攻击后-20， 实际攻击数值 30）
			realAttackVal = newLifeCnt + realAttackVal
			newLifeCnt = 0
		}
	}

	attackp.AttackValue = realAttackVal
	attackp.LeftItemCnt = leftItemCnt
	currM.CurrLifePoint = newLifeCnt
	log.InfoWithCtx(ctx, "AttackM success, monsterId:%d, uid:%d", currM.MonsterId, uid)
	return attackp, nil
}

// 房间激活怪物次数统计
func (c *Cache) AddChannelActivateCnt(ctx context.Context, cid uint32) (int64, error) {
	key := c.channelActRecordKey()
	newVal, err := c.cmder.ZIncr(ctx, key, &redis.Z{
		Score:  1,
		Member: cid,
	}).Result()
	if err != nil {
		log.ErrorWithCtx(ctx, "AddChannelActivateCnt failed to ZIncr, cid:%d, err:%v", cid, err)
		return 0, err
	}
	//自动过期
	err = c.cmder.Expire(ctx, key, CACHE_TIME*time.Second).Err()
	if err != nil {
		log.ErrorWithCtx(ctx, "AddChannelActivateCnt failed to Expire, cid:%d, err:%v", cid, err)
		return 0, err
	}

	return int64(newVal), nil
}

func (c *Cache) GetChannelActivateRank(ctx context.Context) ([]*pb.ChannelInfo, error) {
	key := c.channelActRecordKey()
	ranks := make([]*pb.ChannelInfo, 0)

	res, err := c.cmder.ZRevRangeWithScores(ctx, key, 0, 99).Result()
	if nil != err {
		log.ErrorWithCtx(ctx, "GetChannelActivateRank failed to ZRevRangeWithScores, err:%v", err)
		return ranks, err
	}
	for _, v := range res {
		cid, err := strconv.ParseUint(v.Member.(string), 10, 32)
		if nil != err {
			log.ErrorWithCtx(ctx, "GetChannelActivateRank failed to ParseUint, v:%v, err:%v", v, err)
			continue
		}

		ranks = append(ranks, &pb.ChannelInfo{
			ChannelId:     uint32(cid),
			ActivateCount: uint32(v.Score),
		})
	}
	return ranks, nil
}

func (c *Cache) AddHuntMonsterItem(ctx context.Context, uid uint32, incrCnt int64) (int64, error) {
	key := c.userItemKey(uid)

	pipeline := c.cmder.Pipeline()
	defer pipeline.Close()

	pipeline.IncrBy(ctx, key, incrCnt)
	pipeline.IncrBy(ctx, c.userItemKey(0), incrCnt)
	pipeline.Expire(ctx, key, CACHE_TIME*time.Second)

	results, err := pipeline.Exec(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "AddHuntMonsterItem failed to pipeline.Exec, err:%v", err)
		return 0, err
	}

	finalCnt := results[0].(*redis.IntCmd).Val()
	return finalCnt, nil
}

func (c *Cache) GetHuntMonsterItem(ctx context.Context, uid uint32) int64 {
	key := c.userItemKey(uid)
	itemCnt, err := c.cmder.Get(ctx, key).Int64()
	if nil != err {
		if !errors.Is(err, redis.Nil) {
			log.ErrorWithCtx(ctx, "GetHuntMonsterItem uid:%v, key:%s, err:%v", uid, key, err)
			return 0
		}
		log.DebugWithCtx(ctx, "GetHuntMonsterItem nil, uid:%v, key:%s", uid, key)
		return 0
	}
	return itemCnt
}

// 用户打龙数据统计
func (c *Cache) RecordUserHuntMonster(ctx context.Context, uid uint32, itemList []*lpb.AwardItem, value int64) {
	key := c.userHuntRecordKey(uid)
	pipe := c.cmder.Pipeline()
	defer pipe.Close()
	pipe.HIncrBy(ctx, key, fmt.Sprintf("cnt_%v", uid), 1)
	pipe.HIncrBy(ctx, key, fmt.Sprintf("val_%v", uid), value)

	awardKey := c.userAwardRecordKey(uid)
	for _, item := range itemList {
		msg, err := proto.Marshal(item)
		if nil != err {
			continue
		}
		pipe.RPush(ctx, awardKey, msg)
	}
	// 自动过期
	pipe.Expire(ctx, key, CACHE_TIME*time.Second)
	pipe.Expire(ctx, awardKey, CACHE_TIME*time.Second)
	_, err := pipe.Exec(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "RecordUserHuntMonster failed to pipe.Exec, err:%v", err)
	}
}

func (c *Cache) GetUserHuntMonsterRecord(ctx context.Context, uid uint32, needAwardList bool) (*manager.UserHuntRecord, []*lpb.AwardItem) {
	record := &manager.UserHuntRecord{
		Cnt: 0,
		Val: 0,
	}
	itemList := make([]*lpb.AwardItem, 0)

	key := c.userHuntRecordKey(uid)
	res, err := c.cmder.HMGet(ctx, key, fmt.Sprintf("cnt_%v", uid), fmt.Sprintf("val_%v", uid)).Result()
	if nil != err || len(res) != 2 {
		log.ErrorWithCtx(ctx, "GetUserHuntMonsterRecord failed to HMGet, uid:%d, err:%v", uid, err)
		return record, itemList
	}

	if res[0] != nil {
		record.Cnt, _ = strconv.ParseUint(res[0].(string), 10, 32)
	}
	if res[1] != nil {
		record.Val, _ = strconv.ParseUint(res[1].(string), 10, 32)
	}

	if needAwardList {
		tmpItemList, err := c.getAwardList(ctx, uid)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetUserHuntMonsterRecord failed to getAwardList, uid:%d, err:%v", uid, err)
			return record, itemList
		}
		itemList = tmpItemList
	}

	return record, itemList
}

func (c *Cache) getAwardList(ctx context.Context, uid uint32) ([]*lpb.AwardItem, error) {
	itemList := make([]*lpb.AwardItem, 0)
	awardKey := c.userAwardRecordKey(uid)
	msgList, err := c.cmder.LRange(ctx, awardKey, 0, 99).Result()
	if nil != err {
		log.ErrorWithCtx(ctx, "GetUserHuntMonsterRecord err:%v", err)
		return itemList, err
	}

	//反序
	l, r := 0, len(msgList)-1
	for l < r {
		msgList[l], msgList[r] = msgList[r], msgList[l]
		l++
		r--
	}

	for _, b := range msgList {
		awardItem := &lpb.AwardItem{}
		err := proto.Unmarshal([]byte(b), awardItem)
		if nil != err {
			log.ErrorWithCtx(ctx, "getAwardList failed to Unmarshal, b:%v, err:%v", b, err)
			continue
		}
		itemList = append(itemList, awardItem)
	}
	return itemList, nil

}

func (m *Cache) PushMToQ(ctx context.Context, monster *pb.Monster, cid uint32) error {
	qKey := m.channelMonsterQueKey(cid)
	msg, err := proto.Marshal(monster)
	if nil != err {
		log.ErrorWithCtx(ctx, "manager PushMTOGQ monster:%v err:%v", monster, err)
		return err
	}
	_, err = m.cmder.RPush(ctx, qKey, msg).Result()
	if err != nil {
		log.ErrorWithCtx(ctx, "PushMToQ failed to RPush, cid:%d, monster:%+v, err:%v", cid, monster, err)
		return err
	}
	log.InfoWithCtx(ctx, "PushMToQ success, monster:%+v, cid:%d", monster, cid)
	return nil
}

func (m *Cache) PopMFromQ(ctx context.Context, cid uint32) *pb.Monster {
	qKey := m.channelMonsterQueKey(cid)
	monster := &pb.Monster{}
	bmst, err := m.cmder.LPop(ctx, qKey).Result()
	if nil != err {
		if !errors.Is(err, redis.Nil) {
			log.ErrorWithCtx(ctx, "manager PopMFromGQ err:%v", err)
			return nil
		}
		log.DebugWithCtx(ctx, "manager PopMFromGQ empty, key:%s", qKey)
		return nil
	}

	err = proto.Unmarshal([]byte(bmst), monster)
	if nil != err {
		log.ErrorWithCtx(ctx, "manager PopFronGQ Unmarshal err:%v", err)
		return nil
	}
	log.InfoWithCtx(ctx, "PopMFromQ success, cid:%d, monster:%+v", cid, monster)
	return monster
}

func (m *Cache) GetAttackResult(ctx context.Context, cid, uid uint32, monsterID int64) int64 {
	key := m.attackRankKey(cid, monsterID)
	member := fmt.Sprintf("%v", uid)

	value, err := m.cmder.ZScore(ctx, key, member).Result()
	if err != nil {
		if !errors.Is(err, redis.Nil) {
			log.ErrorWithCtx(ctx, "GetAttackResult uid:%v, key:%s, err:%v", uid, key, err)
			return 0
		}
		log.DebugWithCtx(ctx, "GetAttackResult redis nil, key:%s", key)
		return 0
	}
	return int64(value)
}

func (m *Cache) GenMonsterID(ctx context.Context) int64 {
	monsterId, err := m.cmder.Incr(ctx, monsterIdkey).Result()
	if nil != err {
		log.ErrorWithCtx(ctx, "GenMonsterID Incr err:%v", err)
		return 0
	}
	log.InfoWithCtx(ctx, "GenMonsterID success:%d", monsterId)
	return monsterId
}

func (m *Cache) MonsterIdIncrBy(ctx context.Context, cnt int64) error {
	_, err := m.cmder.IncrBy(ctx, monsterIdkey, cnt).Result()
	if err != nil {
		log.ErrorWithCtx(ctx, "MonsterIdIncrBy failed to IncrBy, cnt:%d, err:%v", cnt, err)
		return err
	}
	return nil
}

func (m *Cache) GetQRange(ctx context.Context, cid uint32) ([]*pb.Monster, error) {
	qkey := m.channelMonsterQueKey(cid)
	mlist := make([]*pb.Monster, 0)
	results, err := m.cmder.LRange(ctx, qkey, 0, 1024).Result()
	if err != nil {
		log.ErrorWithCtx(ctx, "GetQRange LRange failed, qkey:%s, err:%v", qkey, err)
		return mlist, err
	}

	for _, r := range results {
		m := &pb.Monster{}
		err := proto.Unmarshal([]byte(r), m)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetQRange Unmarshal failed, qkey:%s, err:%v", qkey, err)
			continue
		}
		mlist = append(mlist, m)
	}

	return mlist, nil
}

func (m *Cache) AddToCheckTimer(ctx context.Context, triggerTime float64, cid uint32) error {
	//加入定时器，用于定时检查龙是否过期
	_, err := m.cmder.ZAdd(ctx, GTQ, &redis.Z{
		Score:  triggerTime,
		Member: fmt.Sprintf("%v", cid),
	}).Result()
	if err != nil {
		log.ErrorWithCtx(ctx, "AddToCheckTimer failed to Zadd, cid:%d, triggerTime:%f, err:%v", cid, triggerTime, err)
		return err
	}
	return nil

}

func (m *Cache) IncrKeyForDoOnce(ctx context.Context, key string) (int64, error) {

	res, err := m.cmder.Incr(ctx, key).Result()
	if err != nil {
		log.ErrorWithCtx(ctx, "IncrKeyForDoOnce failed, key:%s, err:%v", key, err)
		return 0, err
	}

	_, err = m.cmder.Expire(ctx, key, time.Hour*24*8).Result()
	if err != nil {
		log.ErrorWithCtx(ctx, "IncrKeyForDoOnce failed to Expire, key:%s, err:%v", key, err)
	}
	return res, nil
}

func (m *Cache) AddToAttackRank(ctx context.Context, cid, uid, cnt uint32, monsterId int64) (newCnt float64, err error) {
	key, member := m.attackRankKey(cid, monsterId), fmt.Sprintf("%v", uid)
	newCnt, err = m.cmder.ZIncrBy(ctx, key, float64(cnt), member).Result()
	if nil != err {
		log.ErrorWithCtx(ctx, "manager AttackRank ZIncrBy fail uid:%v err:%v", uid, err)
		return newCnt, err
	}
	expireTime := 3600 * 24 * 8 * time.Second
	if newCnt == float64(cnt) {
		if err := m.cmder.Expire(ctx, key, expireTime).Err(); err != nil {
			log.ErrorWithCtx(ctx, "AddToAttackRank failed to Expire, cid:%d, uid:%d, monsterId:%d", cid, uid, monsterId)
		}
	}
	log.DebugWithCtx(ctx, "AddToAttackRank success, cid:%d, uid:%d, cnt:%d, monsterId:%d", cid, uid, cnt, monsterId)
	return newCnt, nil
}

func (m *Cache) GetAttackRank(ctx context.Context, cid, uid uint32, monsterId int64) (rank int64, err error) {
	key, member := m.attackRankKey(cid, monsterId), fmt.Sprintf("%v", uid)
	rank, err = m.cmder.ZRevRank(ctx, key, member).Result()
	if err != nil {
		log.ErrorWithCtx(ctx, "AddToAttackRank failed to ZRevRank, key:%v, member:%v", key, member)
		return 0, err
	}
	return rank, nil
}

func (m *Cache) GetAttackRankByPage(ctx context.Context, cid uint32, monsterId, offset, cnt int64) (map[uint32]int64, error) {
	key := m.attackRankKey(cid, monsterId)
	uid2Score := make(map[uint32]int64)
	results, err := m.cmder.ZRevRangeWithScores(ctx, key, offset, cnt).Result()
	if nil != err {
		log.ErrorWithCtx(ctx, "manager GetAttackRank ZRevRangeWithScores err:%v", err)
		return uid2Score, err
	}
	for _, v := range results {
		uid, err := strconv.ParseUint(v.Member.(string), 10, 32)
		if nil != err {
			log.ErrorWithCtx(ctx, "GetAttackRank failed to ParseUint, err:%v", err)
			continue
		}
		uid2Score[uint32(uid)] = int64(v.Score)
	}
	log.DebugWithCtx(ctx, "GetAttackRankByPage cid:%d, monsterId:%d, offerset:%d, cnt:%d, len:%d",
		cid, monsterId, offset, cnt, len(uid2Score))
	return uid2Score, nil
}

func (m *Cache) GetMonsterOfDurationByPage(ctx context.Context, minTs, maxTs string, offset, cnt int64) ([]uint32, error) {
	res, err := m.cmder.ZRangeByScore(ctx, GTQ, &redis.ZRangeBy{
		Min:    minTs,
		Max:    maxTs,
		Offset: offset,
		Count:  cnt,
	}).Result()
	if err != nil {
		log.ErrorWithCtx(ctx, "GetTimeoutMonsterByPage failed, maxTs:%s, key:%s, err:%v", maxTs, GTQ, err)
		return []uint32{}, err
	}
	uidList := make([]uint32, 0, len(res))
	for _, r := range res {
		cid, err := strconv.ParseUint(r, 10, 32)
		if err != nil {
			continue
		}
		uidList = append(uidList, uint32(cid))
	}
	return uidList, nil
}

func (m *Cache) RemoveTimeoutMonster(ctx context.Context, maxTs string) error {
	if cnt, err := m.cmder.ZRemRangeByScore(ctx, GTQ, "0", maxTs).Result(); err != nil {
		log.ErrorWithCtx(ctx, "RemoveTimeoutMonster failed to ZRemRangeByScore, maxTs:%s, err:%v",
			maxTs, err)
		return err
	} else {
		log.InfoWithCtx(ctx, "RemoveTimeoutMonster success, maxTs:%s, cnt:%d", maxTs, cnt)
		return nil
	}
}

func (m *Cache) RemoveFinishMonster(ctx context.Context, cidList ...uint32) error {
	if len(cidList) == 0 {
		return nil
	}
	memList := make([]interface{}, 0, len(cidList))
	for _, cid := range cidList {
		memList = append(memList, cid)
	}
	if err := m.cmder.ZRem(ctx, GTQ, memList...).Err(); err != nil {
		log.ErrorWithCtx(ctx, "RemoveFinishMonster failed to ZRem, cidList:%v, err:%v", cidList, err)
		return err
	}
	log.InfoWithCtx(ctx, "RemoveFinishMonster success, cidList:%v", cidList)
	return nil
}
