// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/services/one-piece/one-piece/internal/mysql (interfaces: IStore)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"
	time "time"

	gomock "github.com/golang/mock/gomock"
	mysql "gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/mysql"
	mysql0 "golang.52tt.com/services/one-piece/one-piece/internal/mysql"
)

// MockIStore is a mock of IStore interface.
type MockIStore struct {
	ctrl     *gomock.Controller
	recorder *MockIStoreMockRecorder
}

// MockIStoreMockRecorder is the mock recorder for MockIStore.
type MockIStoreMockRecorder struct {
	mock *MockIStore
}

// NewMockIStore creates a new mock instance.
func NewMockIStore(ctrl *gomock.Controller) *MockIStore {
	mock := &MockIStore{ctrl: ctrl}
	mock.recorder = &MockIStoreMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIStore) EXPECT() *MockIStoreMockRecorder {
	return m.recorder
}

// AddBoxCfg mocks base method.
func (m *MockIStore) AddBoxCfg(arg0 context.Context, arg1 *mysql0.Box) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddBoxCfg", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddBoxCfg indicates an expected call of AddBoxCfg.
func (mr *MockIStoreMockRecorder) AddBoxCfg(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddBoxCfg", reflect.TypeOf((*MockIStore)(nil).AddBoxCfg), arg0, arg1)
}

// BatchBindIslandBox mocks base method.
func (m *MockIStore) BatchBindIslandBox(arg0 context.Context, arg1, arg2 uint32, arg3 map[uint32]uint32, arg4, arg5 time.Time) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchBindIslandBox", arg0, arg1, arg2, arg3, arg4, arg5)
	ret0, _ := ret[0].(error)
	return ret0
}

// BatchBindIslandBox indicates an expected call of BatchBindIslandBox.
func (mr *MockIStoreMockRecorder) BatchBindIslandBox(arg0, arg1, arg2, arg3, arg4, arg5 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchBindIslandBox", reflect.TypeOf((*MockIStore)(nil).BatchBindIslandBox), arg0, arg1, arg2, arg3, arg4, arg5)
}

// ChangeAwardRecordStatus mocks base method.
func (m *MockIStore) ChangeAwardRecordStatus(arg0 context.Context, arg1 time.Time, arg2 uint32, arg3 string, arg4 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ChangeAwardRecordStatus", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(error)
	return ret0
}

// ChangeAwardRecordStatus indicates an expected call of ChangeAwardRecordStatus.
func (mr *MockIStoreMockRecorder) ChangeAwardRecordStatus(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ChangeAwardRecordStatus", reflect.TypeOf((*MockIStore)(nil).ChangeAwardRecordStatus), arg0, arg1, arg2, arg3, arg4)
}

// ChangeConsumeRecordPayInfo mocks base method.
func (m *MockIStore) ChangeConsumeRecordPayInfo(arg0 context.Context, arg1 time.Time, arg2 uint32, arg3 []uint32, arg4 uint32, arg5, arg6 string) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ChangeConsumeRecordPayInfo", arg0, arg1, arg2, arg3, arg4, arg5, arg6)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ChangeConsumeRecordPayInfo indicates an expected call of ChangeConsumeRecordPayInfo.
func (mr *MockIStoreMockRecorder) ChangeConsumeRecordPayInfo(arg0, arg1, arg2, arg3, arg4, arg5, arg6 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ChangeConsumeRecordPayInfo", reflect.TypeOf((*MockIStore)(nil).ChangeConsumeRecordPayInfo), arg0, arg1, arg2, arg3, arg4, arg5, arg6)
}

// ChangeConsumeRecordStatus mocks base method.
func (m *MockIStore) ChangeConsumeRecordStatus(arg0 context.Context, arg1 mysql.Txx, arg2 time.Time, arg3 uint32, arg4 []uint32, arg5 uint32, arg6 string) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ChangeConsumeRecordStatus", arg0, arg1, arg2, arg3, arg4, arg5, arg6)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ChangeConsumeRecordStatus indicates an expected call of ChangeConsumeRecordStatus.
func (mr *MockIStoreMockRecorder) ChangeConsumeRecordStatus(arg0, arg1, arg2, arg3, arg4, arg5, arg6 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ChangeConsumeRecordStatus", reflect.TypeOf((*MockIStore)(nil).ChangeConsumeRecordStatus), arg0, arg1, arg2, arg3, arg4, arg5, arg6)
}

// Close mocks base method.
func (m *MockIStore) Close() error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Close")
	ret0, _ := ret[0].(error)
	return ret0
}

// Close indicates an expected call of Close.
func (mr *MockIStoreMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockIStore)(nil).Close))
}

// CreateAwardLogTbl mocks base method.
func (m *MockIStore) CreateAwardLogTbl(arg0 context.Context, arg1 mysql.Txx, arg2 time.Time, arg3 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateAwardLogTbl", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateAwardLogTbl indicates an expected call of CreateAwardLogTbl.
func (mr *MockIStoreMockRecorder) CreateAwardLogTbl(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateAwardLogTbl", reflect.TypeOf((*MockIStore)(nil).CreateAwardLogTbl), arg0, arg1, arg2, arg3)
}

// CreateBoxTbl mocks base method.
func (m *MockIStore) CreateBoxTbl(arg0 context.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateBoxTbl", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateBoxTbl indicates an expected call of CreateBoxTbl.
func (mr *MockIStoreMockRecorder) CreateBoxTbl(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateBoxTbl", reflect.TypeOf((*MockIStore)(nil).CreateBoxTbl), arg0)
}

// CreateConsumeLogTbl mocks base method.
func (m *MockIStore) CreateConsumeLogTbl(arg0 context.Context, arg1 time.Time) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateConsumeLogTbl", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateConsumeLogTbl indicates an expected call of CreateConsumeLogTbl.
func (mr *MockIStoreMockRecorder) CreateConsumeLogTbl(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateConsumeLogTbl", reflect.TypeOf((*MockIStore)(nil).CreateConsumeLogTbl), arg0, arg1)
}

// CreateDailyAwardLogTbl mocks base method.
func (m *MockIStore) CreateDailyAwardLogTbl(arg0 context.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateDailyAwardLogTbl", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateDailyAwardLogTbl indicates an expected call of CreateDailyAwardLogTbl.
func (mr *MockIStoreMockRecorder) CreateDailyAwardLogTbl(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateDailyAwardLogTbl", reflect.TypeOf((*MockIStore)(nil).CreateDailyAwardLogTbl), arg0)
}

// CreateDailyFinancialLogTbl mocks base method.
func (m *MockIStore) CreateDailyFinancialLogTbl(arg0 context.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateDailyFinancialLogTbl", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateDailyFinancialLogTbl indicates an expected call of CreateDailyFinancialLogTbl.
func (mr *MockIStoreMockRecorder) CreateDailyFinancialLogTbl(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateDailyFinancialLogTbl", reflect.TypeOf((*MockIStore)(nil).CreateDailyFinancialLogTbl), arg0)
}

// CreateIslandBoxTbl mocks base method.
func (m *MockIStore) CreateIslandBoxTbl(arg0 context.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateIslandBoxTbl", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateIslandBoxTbl indicates an expected call of CreateIslandBoxTbl.
func (mr *MockIStoreMockRecorder) CreateIslandBoxTbl(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateIslandBoxTbl", reflect.TypeOf((*MockIStore)(nil).CreateIslandBoxTbl), arg0)
}

// CreateLimitConfTbl mocks base method.
func (m *MockIStore) CreateLimitConfTbl(arg0 context.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateLimitConfTbl", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateLimitConfTbl indicates an expected call of CreateLimitConfTbl.
func (mr *MockIStoreMockRecorder) CreateLimitConfTbl(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateLimitConfTbl", reflect.TypeOf((*MockIStore)(nil).CreateLimitConfTbl), arg0)
}

// CreatePrizeTbl mocks base method.
func (m *MockIStore) CreatePrizeTbl(arg0 context.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreatePrizeTbl", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreatePrizeTbl indicates an expected call of CreatePrizeTbl.
func (mr *MockIStoreMockRecorder) CreatePrizeTbl(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreatePrizeTbl", reflect.TypeOf((*MockIStore)(nil).CreatePrizeTbl), arg0)
}

// CreateRemainChanceTbl mocks base method.
func (m *MockIStore) CreateRemainChanceTbl(arg0 context.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateRemainChanceTbl", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateRemainChanceTbl indicates an expected call of CreateRemainChanceTbl.
func (mr *MockIStoreMockRecorder) CreateRemainChanceTbl(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateRemainChanceTbl", reflect.TypeOf((*MockIStore)(nil).CreateRemainChanceTbl), arg0)
}

// CreateTable mocks base method.
func (m *MockIStore) CreateTable() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "CreateTable")
}

// CreateTable indicates an expected call of CreateTable.
func (mr *MockIStoreMockRecorder) CreateTable() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateTable", reflect.TypeOf((*MockIStore)(nil).CreateTable))
}

// DecrUserRemainChance mocks base method.
func (m *MockIStore) DecrUserRemainChance(arg0 context.Context, arg1 mysql.Txx, arg2, arg3, arg4, arg5 uint32) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DecrUserRemainChance", arg0, arg1, arg2, arg3, arg4, arg5)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DecrUserRemainChance indicates an expected call of DecrUserRemainChance.
func (mr *MockIStoreMockRecorder) DecrUserRemainChance(arg0, arg1, arg2, arg3, arg4, arg5 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DecrUserRemainChance", reflect.TypeOf((*MockIStore)(nil).DecrUserRemainChance), arg0, arg1, arg2, arg3, arg4, arg5)
}

// DelBoxCfg mocks base method.
func (m *MockIStore) DelBoxCfg(arg0 context.Context, arg1 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelBoxCfg", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// DelBoxCfg indicates an expected call of DelBoxCfg.
func (mr *MockIStoreMockRecorder) DelBoxCfg(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelBoxCfg", reflect.TypeOf((*MockIStore)(nil).DelBoxCfg), arg0, arg1)
}

// GetAllIslandBox mocks base method.
func (m *MockIStore) GetAllIslandBox(arg0 context.Context) ([]*mysql0.IslandBox, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllIslandBox", arg0)
	ret0, _ := ret[0].([]*mysql0.IslandBox)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllIslandBox indicates an expected call of GetAllIslandBox.
func (mr *MockIStoreMockRecorder) GetAllIslandBox(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllIslandBox", reflect.TypeOf((*MockIStore)(nil).GetAllIslandBox), arg0)
}

// GetAllPrizeList mocks base method.
func (m *MockIStore) GetAllPrizeList(arg0 context.Context) ([]*mysql0.Prize, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllPrizeList", arg0)
	ret0, _ := ret[0].([]*mysql0.Prize)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllPrizeList indicates an expected call of GetAllPrizeList.
func (mr *MockIStoreMockRecorder) GetAllPrizeList(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllPrizeList", reflect.TypeOf((*MockIStore)(nil).GetAllPrizeList), arg0)
}

// GetAwardLog mocks base method.
func (m *MockIStore) GetAwardLog(arg0 context.Context, arg1, arg2 time.Time) ([]*mysql0.AwardLog, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAwardLog", arg0, arg1, arg2)
	ret0, _ := ret[0].([]*mysql0.AwardLog)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAwardLog indicates an expected call of GetAwardLog.
func (mr *MockIStoreMockRecorder) GetAwardLog(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAwardLog", reflect.TypeOf((*MockIStore)(nil).GetAwardLog), arg0, arg1, arg2)
}

// GetAwardOrderIds mocks base method.
func (m *MockIStore) GetAwardOrderIds(arg0 context.Context, arg1, arg2 time.Time) ([]string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAwardOrderIds", arg0, arg1, arg2)
	ret0, _ := ret[0].([]string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAwardOrderIds indicates an expected call of GetAwardOrderIds.
func (mr *MockIStoreMockRecorder) GetAwardOrderIds(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAwardOrderIds", reflect.TypeOf((*MockIStore)(nil).GetAwardOrderIds), arg0, arg1, arg2)
}

// GetAwardRecordByStatus mocks base method.
func (m *MockIStore) GetAwardRecordByStatus(arg0 context.Context, arg1, arg2, arg3 uint32, arg4, arg5 time.Time) ([]*mysql0.AwardRecord, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAwardRecordByStatus", arg0, arg1, arg2, arg3, arg4, arg5)
	ret0, _ := ret[0].([]*mysql0.AwardRecord)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAwardRecordByStatus indicates an expected call of GetAwardRecordByStatus.
func (mr *MockIStoreMockRecorder) GetAwardRecordByStatus(arg0, arg1, arg2, arg3, arg4, arg5 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAwardRecordByStatus", reflect.TypeOf((*MockIStore)(nil).GetAwardRecordByStatus), arg0, arg1, arg2, arg3, arg4, arg5)
}

// GetAwardStatistics mocks base method.
func (m *MockIStore) GetAwardStatistics(arg0 context.Context, arg1, arg2 time.Time) (map[uint32]*mysql0.Statistics, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAwardStatistics", arg0, arg1, arg2)
	ret0, _ := ret[0].(map[uint32]*mysql0.Statistics)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAwardStatistics indicates an expected call of GetAwardStatistics.
func (mr *MockIStoreMockRecorder) GetAwardStatistics(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAwardStatistics", reflect.TypeOf((*MockIStore)(nil).GetAwardStatistics), arg0, arg1, arg2)
}

// GetAwardTotalCountInfo mocks base method.
func (m *MockIStore) GetAwardTotalCountInfo(arg0 context.Context, arg1, arg2 time.Time) (*mysql0.StCount, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAwardTotalCountInfo", arg0, arg1, arg2)
	ret0, _ := ret[0].(*mysql0.StCount)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAwardTotalCountInfo indicates an expected call of GetAwardTotalCountInfo.
func (mr *MockIStoreMockRecorder) GetAwardTotalCountInfo(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAwardTotalCountInfo", reflect.TypeOf((*MockIStore)(nil).GetAwardTotalCountInfo), arg0, arg1, arg2)
}

// GetBoxCfgUpdateVersion mocks base method.
func (m *MockIStore) GetBoxCfgUpdateVersion(arg0 context.Context) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBoxCfgUpdateVersion", arg0)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBoxCfgUpdateVersion indicates an expected call of GetBoxCfgUpdateVersion.
func (mr *MockIStoreMockRecorder) GetBoxCfgUpdateVersion(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBoxCfgUpdateVersion", reflect.TypeOf((*MockIStore)(nil).GetBoxCfgUpdateVersion), arg0)
}

// GetChanceBuyStatsUseTBeanTime mocks base method.
func (m *MockIStore) GetChanceBuyStatsUseTBeanTime(arg0 context.Context, arg1, arg2 time.Time) (*mysql0.Stats, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChanceBuyStatsUseTBeanTime", arg0, arg1, arg2)
	ret0, _ := ret[0].(*mysql0.Stats)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChanceBuyStatsUseTBeanTime indicates an expected call of GetChanceBuyStatsUseTBeanTime.
func (mr *MockIStoreMockRecorder) GetChanceBuyStatsUseTBeanTime(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChanceBuyStatsUseTBeanTime", reflect.TypeOf((*MockIStore)(nil).GetChanceBuyStatsUseTBeanTime), arg0, arg1, arg2)
}

// GetConsumeOrderIds mocks base method.
func (m *MockIStore) GetConsumeOrderIds(arg0 context.Context, arg1, arg2 time.Time) ([]string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetConsumeOrderIds", arg0, arg1, arg2)
	ret0, _ := ret[0].([]string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetConsumeOrderIds indicates an expected call of GetConsumeOrderIds.
func (mr *MockIStoreMockRecorder) GetConsumeOrderIds(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetConsumeOrderIds", reflect.TypeOf((*MockIStore)(nil).GetConsumeOrderIds), arg0, arg1, arg2)
}

// GetConsumeRecordByPayId mocks base method.
func (m *MockIStore) GetConsumeRecordByPayId(arg0 context.Context, arg1 uint32, arg2 time.Time, arg3 string) (*mysql0.ConsumeRecord, bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetConsumeRecordByPayId", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*mysql0.ConsumeRecord)
	ret1, _ := ret[1].(bool)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetConsumeRecordByPayId indicates an expected call of GetConsumeRecordByPayId.
func (mr *MockIStoreMockRecorder) GetConsumeRecordByPayId(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetConsumeRecordByPayId", reflect.TypeOf((*MockIStore)(nil).GetConsumeRecordByPayId), arg0, arg1, arg2, arg3)
}

// GetConsumeTotalCountInfo mocks base method.
func (m *MockIStore) GetConsumeTotalCountInfo(arg0 context.Context, arg1, arg2 time.Time) (*mysql0.StCount, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetConsumeTotalCountInfo", arg0, arg1, arg2)
	ret0, _ := ret[0].(*mysql0.StCount)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetConsumeTotalCountInfo indicates an expected call of GetConsumeTotalCountInfo.
func (mr *MockIStoreMockRecorder) GetConsumeTotalCountInfo(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetConsumeTotalCountInfo", reflect.TypeOf((*MockIStore)(nil).GetConsumeTotalCountInfo), arg0, arg1, arg2)
}

// GetDateRemainTotal mocks base method.
func (m *MockIStore) GetDateRemainTotal(arg0 context.Context, arg1 time.Time) (*mysql0.Stats, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetDateRemainTotal", arg0, arg1)
	ret0, _ := ret[0].(*mysql0.Stats)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetDateRemainTotal indicates an expected call of GetDateRemainTotal.
func (mr *MockIStoreMockRecorder) GetDateRemainTotal(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDateRemainTotal", reflect.TypeOf((*MockIStore)(nil).GetDateRemainTotal), arg0, arg1)
}

// GetEffectBoxCfgList mocks base method.
func (m *MockIStore) GetEffectBoxCfgList(arg0 context.Context) ([]*mysql0.Box, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetEffectBoxCfgList", arg0)
	ret0, _ := ret[0].([]*mysql0.Box)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetEffectBoxCfgList indicates an expected call of GetEffectBoxCfgList.
func (mr *MockIStoreMockRecorder) GetEffectBoxCfgList(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetEffectBoxCfgList", reflect.TypeOf((*MockIStore)(nil).GetEffectBoxCfgList), arg0)
}

// GetEffectPrizeList mocks base method.
func (m *MockIStore) GetEffectPrizeList(arg0 context.Context, arg1, arg2 uint32, arg3 time.Time) ([]*mysql0.Prize, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetEffectPrizeList", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].([]*mysql0.Prize)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetEffectPrizeList indicates an expected call of GetEffectPrizeList.
func (mr *MockIStoreMockRecorder) GetEffectPrizeList(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetEffectPrizeList", reflect.TypeOf((*MockIStore)(nil).GetEffectPrizeList), arg0, arg1, arg2, arg3)
}

// GetIslandBox mocks base method.
func (m *MockIStore) GetIslandBox(arg0 context.Context, arg1, arg2 uint32, arg3 time.Time) ([]*mysql0.IslandBox, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetIslandBox", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].([]*mysql0.IslandBox)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetIslandBox indicates an expected call of GetIslandBox.
func (mr *MockIStoreMockRecorder) GetIslandBox(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetIslandBox", reflect.TypeOf((*MockIStore)(nil).GetIslandBox), arg0, arg1, arg2, arg3)
}

// GetIslandBoxUpdateVersion mocks base method.
func (m *MockIStore) GetIslandBoxUpdateVersion(arg0 context.Context, arg1 time.Time) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetIslandBoxUpdateVersion", arg0, arg1)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetIslandBoxUpdateVersion indicates an expected call of GetIslandBoxUpdateVersion.
func (mr *MockIStoreMockRecorder) GetIslandBoxUpdateVersion(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetIslandBoxUpdateVersion", reflect.TypeOf((*MockIStore)(nil).GetIslandBoxUpdateVersion), arg0, arg1)
}

// GetLimitConfList mocks base method.
func (m *MockIStore) GetLimitConfList(arg0 context.Context) ([]*mysql0.UserLimitCfg, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLimitConfList", arg0)
	ret0, _ := ret[0].([]*mysql0.UserLimitCfg)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLimitConfList indicates an expected call of GetLimitConfList.
func (mr *MockIStoreMockRecorder) GetLimitConfList(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLimitConfList", reflect.TypeOf((*MockIStore)(nil).GetLimitConfList), arg0)
}

// GetLotteryPeopleCnt mocks base method.
func (m *MockIStore) GetLotteryPeopleCnt(arg0 context.Context, arg1, arg2 time.Time) (uint64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLotteryPeopleCnt", arg0, arg1, arg2)
	ret0, _ := ret[0].(uint64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLotteryPeopleCnt indicates an expected call of GetLotteryPeopleCnt.
func (mr *MockIStoreMockRecorder) GetLotteryPeopleCnt(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLotteryPeopleCnt", reflect.TypeOf((*MockIStore)(nil).GetLotteryPeopleCnt), arg0, arg1, arg2)
}

// GetLotteryTotalPeopleCnt mocks base method.
func (m *MockIStore) GetLotteryTotalPeopleCnt(arg0 context.Context, arg1, arg2 time.Time) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLotteryTotalPeopleCnt", arg0, arg1, arg2)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLotteryTotalPeopleCnt indicates an expected call of GetLotteryTotalPeopleCnt.
func (mr *MockIStoreMockRecorder) GetLotteryTotalPeopleCnt(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLotteryTotalPeopleCnt", reflect.TypeOf((*MockIStore)(nil).GetLotteryTotalPeopleCnt), arg0, arg1, arg2)
}

// GetPrizeUpdateVersion mocks base method.
func (m *MockIStore) GetPrizeUpdateVersion(arg0 context.Context, arg1, arg2 uint32, arg3 time.Time) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPrizeUpdateVersion", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPrizeUpdateVersion indicates an expected call of GetPrizeUpdateVersion.
func (mr *MockIStoreMockRecorder) GetPrizeUpdateVersion(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPrizeUpdateVersion", reflect.TypeOf((*MockIStore)(nil).GetPrizeUpdateVersion), arg0, arg1, arg2, arg3)
}

// GetTotalRemainChance mocks base method.
func (m *MockIStore) GetTotalRemainChance(arg0 context.Context) (uint64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTotalRemainChance", arg0)
	ret0, _ := ret[0].(uint64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTotalRemainChance indicates an expected call of GetTotalRemainChance.
func (mr *MockIStoreMockRecorder) GetTotalRemainChance(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTotalRemainChance", reflect.TypeOf((*MockIStore)(nil).GetTotalRemainChance), arg0)
}

// GetUserAwardRecords mocks base method.
func (m *MockIStore) GetUserAwardRecords(arg0 context.Context, arg1, arg2, arg3, arg4 uint32, arg5 string, arg6 bool) ([]*mysql0.AwardRecord, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserAwardRecords", arg0, arg1, arg2, arg3, arg4, arg5, arg6)
	ret0, _ := ret[0].([]*mysql0.AwardRecord)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserAwardRecords indicates an expected call of GetUserAwardRecords.
func (mr *MockIStoreMockRecorder) GetUserAwardRecords(arg0, arg1, arg2, arg3, arg4, arg5, arg6 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserAwardRecords", reflect.TypeOf((*MockIStore)(nil).GetUserAwardRecords), arg0, arg1, arg2, arg3, arg4, arg5, arg6)
}

// GetUserRemainChance mocks base method.
func (m *MockIStore) GetUserRemainChance(arg0 context.Context, arg1 uint32) (*mysql0.RemainChance, bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserRemainChance", arg0, arg1)
	ret0, _ := ret[0].(*mysql0.RemainChance)
	ret1, _ := ret[1].(bool)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetUserRemainChance indicates an expected call of GetUserRemainChance.
func (mr *MockIStoreMockRecorder) GetUserRemainChance(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserRemainChance", reflect.TypeOf((*MockIStore)(nil).GetUserRemainChance), arg0, arg1)
}

// GetUserRemainChanceForUpdate mocks base method.
func (m *MockIStore) GetUserRemainChanceForUpdate(arg0 context.Context, arg1 mysql.Txx, arg2 uint32) (*mysql0.RemainChance, bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserRemainChanceForUpdate", arg0, arg1, arg2)
	ret0, _ := ret[0].(*mysql0.RemainChance)
	ret1, _ := ret[1].(bool)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetUserRemainChanceForUpdate indicates an expected call of GetUserRemainChanceForUpdate.
func (mr *MockIStoreMockRecorder) GetUserRemainChanceForUpdate(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserRemainChanceForUpdate", reflect.TypeOf((*MockIStore)(nil).GetUserRemainChanceForUpdate), arg0, arg1, arg2)
}

// IncrUserRemainChance mocks base method.
func (m *MockIStore) IncrUserRemainChance(arg0 context.Context, arg1 mysql.Txx, arg2, arg3 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IncrUserRemainChance", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// IncrUserRemainChance indicates an expected call of IncrUserRemainChance.
func (mr *MockIStoreMockRecorder) IncrUserRemainChance(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IncrUserRemainChance", reflect.TypeOf((*MockIStore)(nil).IncrUserRemainChance), arg0, arg1, arg2, arg3)
}

// InsertAwardRecords mocks base method.
func (m *MockIStore) InsertAwardRecords(arg0 context.Context, arg1 mysql.Txx, arg2 time.Time, arg3, arg4 uint32, arg5 []*mysql0.AwardRecord) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InsertAwardRecords", arg0, arg1, arg2, arg3, arg4, arg5)
	ret0, _ := ret[0].(error)
	return ret0
}

// InsertAwardRecords indicates an expected call of InsertAwardRecords.
func (mr *MockIStoreMockRecorder) InsertAwardRecords(arg0, arg1, arg2, arg3, arg4, arg5 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InsertAwardRecords", reflect.TypeOf((*MockIStore)(nil).InsertAwardRecords), arg0, arg1, arg2, arg3, arg4, arg5)
}

// InsertConsumeRecord mocks base method.
func (m *MockIStore) InsertConsumeRecord(arg0 context.Context, arg1 *mysql0.ConsumeRecord) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InsertConsumeRecord", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// InsertConsumeRecord indicates an expected call of InsertConsumeRecord.
func (mr *MockIStoreMockRecorder) InsertConsumeRecord(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InsertConsumeRecord", reflect.TypeOf((*MockIStore)(nil).InsertConsumeRecord), arg0, arg1)
}

// InsertDailyAwardLog mocks base method.
func (m *MockIStore) InsertDailyAwardLog(arg0 context.Context, arg1 *mysql0.DailyAwardLog) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InsertDailyAwardLog", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// InsertDailyAwardLog indicates an expected call of InsertDailyAwardLog.
func (mr *MockIStoreMockRecorder) InsertDailyAwardLog(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InsertDailyAwardLog", reflect.TypeOf((*MockIStore)(nil).InsertDailyAwardLog), arg0, arg1)
}

// InsertDailyConsumeLog mocks base method.
func (m *MockIStore) InsertDailyConsumeLog(arg0 context.Context, arg1 *mysql0.DailyConsumeLog) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InsertDailyConsumeLog", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// InsertDailyConsumeLog indicates an expected call of InsertDailyConsumeLog.
func (mr *MockIStoreMockRecorder) InsertDailyConsumeLog(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InsertDailyConsumeLog", reflect.TypeOf((*MockIStore)(nil).InsertDailyConsumeLog), arg0, arg1)
}

// InsertUserRemainChance mocks base method.
func (m *MockIStore) InsertUserRemainChance(arg0 context.Context, arg1 *mysql0.RemainChance) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InsertUserRemainChance", arg0, arg1)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// InsertUserRemainChance indicates an expected call of InsertUserRemainChance.
func (mr *MockIStoreMockRecorder) InsertUserRemainChance(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InsertUserRemainChance", reflect.TypeOf((*MockIStore)(nil).InsertUserRemainChance), arg0, arg1)
}

// SetLimitConfList mocks base method.
func (m *MockIStore) SetLimitConfList(arg0 context.Context, arg1 []*mysql0.UserLimitCfg) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetLimitConfList", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetLimitConfList indicates an expected call of SetLimitConfList.
func (mr *MockIStoreMockRecorder) SetLimitConfList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetLimitConfList", reflect.TypeOf((*MockIStore)(nil).SetLimitConfList), arg0, arg1)
}

// SetPrizeList mocks base method.
func (m *MockIStore) SetPrizeList(arg0 context.Context, arg1, arg2 uint32, arg3 []*mysql0.Prize, arg4, arg5 time.Time) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetPrizeList", arg0, arg1, arg2, arg3, arg4, arg5)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetPrizeList indicates an expected call of SetPrizeList.
func (mr *MockIStoreMockRecorder) SetPrizeList(arg0, arg1, arg2, arg3, arg4, arg5 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetPrizeList", reflect.TypeOf((*MockIStore)(nil).SetPrizeList), arg0, arg1, arg2, arg3, arg4, arg5)
}

// Transaction mocks base method.
func (m *MockIStore) Transaction(arg0 context.Context, arg1 func(mysql.Txx) error) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Transaction", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// Transaction indicates an expected call of Transaction.
func (mr *MockIStoreMockRecorder) Transaction(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Transaction", reflect.TypeOf((*MockIStore)(nil).Transaction), arg0, arg1)
}

// UpdateBoxCfg mocks base method.
func (m *MockIStore) UpdateBoxCfg(arg0 context.Context, arg1 *mysql0.Box) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateBoxCfg", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateBoxCfg indicates an expected call of UpdateBoxCfg.
func (mr *MockIStoreMockRecorder) UpdateBoxCfg(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateBoxCfg", reflect.TypeOf((*MockIStore)(nil).UpdateBoxCfg), arg0, arg1)
}
