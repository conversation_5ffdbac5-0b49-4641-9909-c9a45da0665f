package manager

import (
	"context"
	"github.com/golang/mock/gomock"
	accountCli "golang.52tt.com/clients/account"
	accountMock "golang.52tt.com/clients/mocks/account"
	apicenter "golang.52tt.com/clients/mocks/apicenter/apiserver"
	"golang.52tt.com/clients/mocks/backpack"
	backpacksender "golang.52tt.com/clients/mocks/backpack-sender"
	probgamecenter "golang.52tt.com/clients/mocks/prob-game-center"
	PushNotification "golang.52tt.com/clients/mocks/push-notification/v2"
	unifiedPay "golang.52tt.com/clients/mocks/unified_pay"
	userPresent "golang.52tt.com/clients/mocks/userpresent"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/protocol/common/status"
	backpackPb "golang.52tt.com/protocol/services/backpacksvr"
	reconcile_v2 "golang.52tt.com/protocol/services/reconcile-v2"
	UnifiedPayCallback "golang.52tt.com/protocol/services/unified_pay/cb"
	userpresentPB "golang.52tt.com/protocol/services/userpresent"
	"golang.52tt.com/services/one-piece/one-piece/internal/cache"
	"golang.52tt.com/services/one-piece/one-piece/internal/conf"
	"golang.52tt.com/services/one-piece/one-piece/internal/manager/define"
	"golang.52tt.com/services/one-piece/one-piece/internal/mocks"
	"golang.52tt.com/services/one-piece/one-piece/internal/mysql"
	_ "golang.52tt.com/services/recommend-dialog/tools/grpc_proxy/enable"
	"math/rand"
	"testing"
	"time"

	publicnotice "golang.52tt.com/clients/mocks/public-notice"
	pb "golang.52tt.com/protocol/services/one-piece"
)

func TestMgr_initRandN(t *testing.T) {
	rand.Seed(time.Now().UnixNano())

	for j := 0; j < 5; j++ {
		times := 1000000
		total := int64(0)

		for i := 0; i < times; i++ {
			total += int64(initRandN(23932))
		}

		t.Log(float32(total) / float32(times))
	}

}

func TestMgr_BuyChance(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	ctx := context.Background()
	uid := uint32(1)

	mockStore := mocks.NewMockIStore(ctl)
	mockCache := mocks.NewMockICache(ctl)
	mockCfg := mocks.NewMockIBusinessConfManager(ctl)
	mockUnifiedPayCli := unifiedPay.NewMockIClient(ctl)
	mockAccountCli := accountMock.NewMockIClient(ctl)
	mockProbGameCli := probgamecenter.NewMockIClient(ctl)
	mockBackSenderCli := backpacksender.NewMockIClient(ctl)

	m := Mgr{
		store:             mockStore,
		redisCache:        mockCache,
		bc:                mockCfg,
		unifiedPayCli:     mockUnifiedPayCli,
		accountCli:        mockAccountCli,
		probGameCli:       mockProbGameCli,
		backpackSenderCli: mockBackSenderCli,
	}

	cases := []struct {
		Name    string
		Amount  uint32
		Fee     uint32
		wantErr bool
	}{
		{"BuyChance common", 10, 10 * 1000, false},
		{"BuyChance limit", 20, 20 * 1000, true},
		{"BuyChance limit", 30, 30 * 1000, true},
		{"BuyChance not eq", 10, 5 * 1000, true},
	}

	for _, testCase := range cases {
		gomock.InOrder(
			mockCache.EXPECT().GetPriceLimitCfg(gomock.Any()).Return(map[uint32]uint32{mysql.DailyBuyLimitType: 200, mysql.SingleBuyLimitType: 100}, nil).AnyTimes(),
			mockCache.EXPECT().GetUserDailyPrice(gomock.Any(), uint32(time.Now().Day()), uid, mysql.DailyBuyLimitType).Return(uint32(0), nil).AnyTimes(),
			mockCfg.EXPECT().GetChancePackId().Return(uint32(123)).AnyTimes(),
			mockCfg.EXPECT().GetBackSenderInfo().Return(uint32(10), "ouhafzzqulsyttdw").AnyTimes(),
			mockStore.EXPECT().InsertConsumeRecord(ctx, gomock.Any()).Return(nil).AnyTimes(),
			mockCfg.EXPECT().GetPayAppId().Return("TT_HHXB").AnyTimes(),
			mockUnifiedPayCli.EXPECT().PresetFreeze(ctx, uid, gomock.Any(), "TT_HHXB", gomock.Any(),
				time.Now().Format("2006-01-02 15:04:05"), gomock.Any()).Return(uint32(0), nil).AnyTimes(),
			mockStore.EXPECT().ChangeConsumeRecordPayInfo(gomock.Any(), gomock.Any(), uid, gomock.Any(),
				gomock.Any(), gomock.Any(), gomock.Any()).Return(true, nil).AnyTimes(),
			mockStore.EXPECT().InsertUserRemainChance(ctx, &mysql.RemainChance{Uid: uid}).Return(true, nil).AnyTimes(),
			mockStore.EXPECT().Transaction(ctx, gomock.Any()).Return(nil).AnyTimes(),

			mockCache.EXPECT().DelUserRemainChance(gomock.Any(), uid).Return(nil).AnyTimes(),
			mockCfg.EXPECT().GetTestPayUidList().Return([]uint32{0}).AnyTimes(),

			mockAccountCli.EXPECT().GetUser(ctx, uid).Return(&accountCli.User{Uid: uid}, nil).AnyTimes(),
			mockCfg.EXPECT().GetPayAppId().Return("TT_HHXB").AnyTimes(),
			mockUnifiedPayCli.EXPECT().UnfreezeAndConsume(gomock.Any(), gomock.Any()).Return("", "", nil).AnyTimes(),
			mockStore.EXPECT().ChangeConsumeRecordPayInfo(gomock.Any(), gomock.Any(), uid, gomock.Any(),
				gomock.Any(), gomock.Any(), gomock.Any()).Return(true, nil).AnyTimes(),

			mockCache.EXPECT().IncrUserDailyPrice(gomock.Any(), gomock.Any(), uid, gomock.Any(), mysql.DailyBuyLimitType).Return(nil).AnyTimes(),

			mockProbGameCli.EXPECT().CheckFuse(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes(),
			mockCfg.EXPECT().GetChancePackId().Return(uint32(123)).AnyTimes(),
			mockCfg.EXPECT().GetBackSenderInfo().Return(uint32(10), "ouhafzzqulsyttdw").AnyTimes(),
			mockBackSenderCli.EXPECT().SendBackpackWithRiskControl(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes(),
			mockStore.EXPECT().ChangeAwardRecordStatus(gomock.Any(), gomock.Any(), uid, gomock.Any(), gomock.Any()).Return(nil).AnyTimes(),
		)

		t.Run(testCase.Name, func(t *testing.T) {
			_, _, err := m.BuyChance(ctx, &pb.BuyChanceReq{
				Uid:         uid,
				Amount:      testCase.Amount,
				Fee:         testCase.Fee,
				OutsideTime: uint32(time.Now().Unix()),
			})
			if (err != nil) != testCase.wantErr {
				t.Errorf("BuyChance error = %v, wantErr = %v", err, testCase.wantErr)
				return
			}

			if err == nil {
				time.Sleep(300 * time.Millisecond) // 等待协程执行完
			}
		})
	}
}

func TestMgr_Callback(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	ctx := context.Background()
	uid := uint32(1)

	mockStore := mocks.NewMockIStore(ctl)
	mockCache := mocks.NewMockICache(ctl)
	mockCfg := mocks.NewMockIBusinessConfManager(ctl)
	mockUnifiedPayCli := unifiedPay.NewMockIClient(ctl)
	mockAccountCli := accountMock.NewMockIClient(ctl)

	m := Mgr{
		store:         mockStore,
		redisCache:    mockCache,
		bc:            mockCfg,
		unifiedPayCli: mockUnifiedPayCli,
		accountCli:    mockAccountCli,
	}

	cases := []struct {
		Name    string
		OrderId string
		wantErr bool
		wantOp  UnifiedPayCallback.Op
	}{
		{"Callback commit", "commit", false, UnifiedPayCallback.Op_COMMIT},
		{"Callback rollback", "rollback", false, UnifiedPayCallback.Op_ROLLBACK},
		{"Callback not exist", "not exist", true, 0},
	}

	for _, testCase := range cases {
		gomock.InOrder(
			mockStore.EXPECT().GetConsumeRecordByPayId(ctx, uid, gomock.Any(), gomock.Any()).AnyTimes().DoAndReturn(
				func(ctx context.Context, uid uint32, t time.Time, payOrderId string) (*mysql.ConsumeRecord, bool, error) {
					r := &mysql.ConsumeRecord{
						Uid:          uid,
						Amount:       1,
						Fee:          1000,
						PayOrderId:   payOrderId,
						AwardOrderId: payOrderId,
						CreateTime:   time.Now().AddDate(0, 0, -1),
					}

					if payOrderId == "commit" {
						r.Status = mysql.ConsumeStatusDone
					} else if payOrderId == "rollback" {
						r.Status = mysql.ConsumeStatusInit
					} else if payOrderId == "not exist" {
						return r, false, nil
					}

					return r, true, nil
				}),

			mockAccountCli.EXPECT().GetUser(ctx, uid).AnyTimes().Return(&accountCli.User{Uid: uid}, nil),
			mockCfg.EXPECT().GetPayAppId().AnyTimes().Return("TT_HHXB"),
			mockUnifiedPayCli.EXPECT().UnfreezeAndConsume(gomock.Any(), gomock.Any()).AnyTimes().Return("", "", nil),
			mockStore.EXPECT().ChangeConsumeRecordPayInfo(gomock.Any(), gomock.Any(), uid, gomock.Any(),
				gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes().Return(true, nil),

			mockStore.EXPECT().ChangeConsumeRecordPayInfo(gomock.Any(), gomock.Any(), uid, gomock.Any(),
				gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes().Return(true, nil),
			mockCfg.EXPECT().GetPayAppId().AnyTimes().Return("TT_HHXB"),
			mockUnifiedPayCli.EXPECT().UnFreezeAndRefund(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes().Return(nil),
		)

		t.Run(testCase.Name, func(t *testing.T) {
			op, err := m.Callback(ctx, uid, testCase.OrderId)
			if (err != nil) != testCase.wantErr {
				t.Errorf("Callback error = %v, wantErr = %v", err, testCase.wantErr)
				return
			}
			if op != testCase.wantOp {
				t.Errorf("Callback op = %v, wantOp = %v", err, testCase.wantOp)
				return
			}
		})
	}
}

func TestMgr_CheckBoxLocalCacheUpdate(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockStore := mocks.NewMockIStore(ctl)
	mockCache := mocks.NewMockICache(ctl)
	mockCfg := mocks.NewMockIBusinessConfManager(ctl)

	m := Mgr{
		store:      mockStore,
		redisCache: mockCache,
		bc:         mockCfg,
	}

	gomock.InOrder(
		mockStore.EXPECT().GetBoxCfgUpdateVersion(gomock.Any()).Return(int64(1), nil),
		mockStore.EXPECT().GetIslandBoxUpdateVersion(gomock.Any(), gomock.Any()).AnyTimes().Return(int64(1), nil),
		mockStore.EXPECT().GetEffectBoxCfgList(gomock.Any()).Return([]*mysql.Box{
			{1, "test", "test", "pic", false, time.Now(), time.Now()},
		}, nil),
		mockStore.EXPECT().GetIslandBox(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes().Return([]*mysql.IslandBox{
			{
				Id:       1,
				Mode:     1,
				Wind:     1,
				IslandId: 1,
				BoxId:    1,
			},
		}, nil),
	)

	t.Run("CheckBoxLocalCacheUpdate", func(t *testing.T) {
		err := m.CheckBoxLocalCacheUpdate()
		if err != nil {
			t.Errorf("CheckBoxLocalCacheUpdate error = %v", err)
			return
		}
	})
}

func TestMgr_checkWindPoolUpdate(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockStore := mocks.NewMockIStore(ctl)
	mockCache := mocks.NewMockICache(ctl)
	mockCfg := mocks.NewMockIBusinessConfManager(ctl)

	m := Mgr{
		store:      mockStore,
		redisCache: mockCache,
		bc:         mockCfg,
	}

	gomock.InOrder(
		mockStore.EXPECT().GetPrizeUpdateVersion(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes().Return(int64(1), nil),
		mockStore.EXPECT().GetEffectPrizeList(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes().Return([]*mysql.Prize{
			{
				IslandId: 1,
				Weight:   10,
				PackId:   100,
			},
		}, nil),
	)

	t.Run("checkWindPoolUpdate", func(t *testing.T) {
		ok, err := m.checkWindPoolUpdate(context.Background(), 0, 0, time.Now())
		if err != nil || ok == false {
			t.Errorf("checkWindPoolUpdate error = %v ok = %v", err, ok)
			return
		}
	})
}

func TestMgr_CheckWindChange(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockStore := mocks.NewMockIStore(ctl)
	mockCache := mocks.NewMockICache(ctl)
	mockCfg := mocks.NewMockIBusinessConfManager(ctl)
	mockPushCli := PushNotification.NewMockIClient(ctl)

	m := Mgr{
		store:      mockStore,
		redisCache: mockCache,
		bc:         mockCfg,
		pushCli:    mockPushCli,
	}

	newPool := initPrizePool(1, []*mysql.Prize{
		{
			Mode:     0,
			Wind:     0,
			IslandId: 2,
			Weight:   1,
		},
		{
			Mode:     0,
			Wind:     0,
			IslandId: 1,
			Weight:   1,
		},
	})
	_ = m.lottery.setPrizePool(0, 0, newPool)
	_ = m.lottery.setPrizePool(0, 1, newPool)
	_ = m.lottery.setPrizePool(0, 2, newPool)

	_ = m.lottery.setPrizePool(1, 0, newPool)
	_ = m.lottery.setPrizePool(1, 1, newPool)
	_ = m.lottery.setPrizePool(1, 2, newPool)
	TestMode = true

	gomock.InOrder(
		mockCache.EXPECT().GetWindInfo(gomock.Any()).Return(&cache.WindInfo{CurrWind: 0, NextWind: 2}, nil),
		mockCache.EXPECT().GetAllMinuteWindVal(gomock.Any()).Return(map[string]uint32{"test": 1}, nil),
		mockCfg.EXPECT().GetWindCfg().AnyTimes().Return(&conf.WindCfg{WindContinueMinutes: []uint32{5}, VirWindSpeedBase: 1, MaxWindSpeed: 1}),
		mockCache.EXPECT().SetWindInfo(gomock.Any(), gomock.Any()).AnyTimes().Return(nil),
		mockCache.EXPECT().ClearMinuteWindVal(gomock.Any()).AnyTimes().Return(nil),
		mockCache.EXPECT().SetWindInfo(gomock.Any(), gomock.Any()).AnyTimes().Return(nil),

		mockPushCli.EXPECT().PushMulticast(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes().Return(nil),
	)

	t.Run("CheckWindChange", func(t *testing.T) {
		err := m.CheckWindChange()
		if err != nil {
			t.Errorf("CheckWindChange error = %v", err)
			return
		}
	})
}

func TestMgr_CheckPackConfUpdatePresent(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockStore := mocks.NewMockIStore(ctl)
	mockCache := mocks.NewMockICache(ctl)
	mockCfg := mocks.NewMockIBusinessConfManager(ctl)
	mockBackpack := backpack.NewMockIClient(ctl)
	mockUserpresent := userPresent.NewMockIClient(ctl)

	m := Mgr{
		store:          mockStore,
		redisCache:     mockCache,
		bc:             mockCfg,
		backpackCli:    mockBackpack,
		userPresentCli: mockUserpresent,
	}

	gomock.InOrder(
		mockCfg.EXPECT().GetChancePackId().AnyTimes().Return(uint32(1)),
		mockBackpack.EXPECT().GetPackageItemCfg(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&backpackPb.GetPackageItemCfgResp{
			ItemCfgList: []*backpackPb.PackageItemCfg{{
				BgItemId:  1,
				BgId:      1,
				ItemType:  uint32(backpackPb.PackageItemType_BACKPACK_PRESENT),
				SourceId:  2,
				ItemCount: 1,
				Months:    6,
			}},
		}, nil),
		mockUserpresent.EXPECT().GetPresentConfigById(gomock.Any(), gomock.Any()).Return(&userpresentPB.GetPresentConfigByIdResp{
			ItemConfig: &userpresentPB.StPresentItemConfig{
				ItemId:    1,
				Name:      "test",
				IconUrl:   "test_url",
				Price:     100,
				PriceType: uint32(userpresentPB.PresentPriceType_PRESENT_PRICE_TBEAN),
			}}, nil),
		mockCfg.EXPECT().GetBingoId().Return(uint32(1)),
	)

	t.Run("TestMgr_CheckPackConfUpdatePresent", func(t *testing.T) {
		err := m.CheckPackConfUpdate()
		if err != nil {
			t.Errorf("CheckPackConfUpdate error = %v", err)
			return
		}
	})
}

func TestMgr_CheckPackConfUpdateFragment(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockStore := mocks.NewMockIStore(ctl)
	mockCache := mocks.NewMockICache(ctl)
	mockCfg := mocks.NewMockIBusinessConfManager(ctl)
	mockBackpack := backpack.NewMockIClient(ctl)
	mockUserpresent := userPresent.NewMockIClient(ctl)

	m := Mgr{
		store:          mockStore,
		redisCache:     mockCache,
		bc:             mockCfg,
		backpackCli:    mockBackpack,
		userPresentCli: mockUserpresent,
	}

	itemCfgList := make([][]byte, 0)
	cfg := &backpackPb.LotteryFragmentCfg{
		FragmentId:   1,
		FragmentName: "test",
	}
	b, _ := cfg.Marshal()
	itemCfgList = append(itemCfgList, b)

	gomock.InOrder(
		mockCfg.EXPECT().GetChancePackId().AnyTimes().Return(uint32(1)),
		mockBackpack.EXPECT().GetPackageItemCfg(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&backpackPb.GetPackageItemCfgResp{
			ItemCfgList: []*backpackPb.PackageItemCfg{{
				BgItemId:  1,
				BgId:      1,
				ItemType:  uint32(backpackPb.PackageItemType_BACKPACK_LOTTERY_FRAGMENT),
				SourceId:  2,
				ItemCount: 1,
				Months:    6,
			}},
		}, nil),
		mockBackpack.EXPECT().GetItemCfg(gomock.Any(), gomock.Any(), gomock.Any()).Return(&backpackPb.GetItemCfgResp{
			ItemCfgList: itemCfgList,
		}, nil),
		mockCfg.EXPECT().GetBingoId().Return(uint32(1)),
	)

	t.Run("TestMgr_CheckPackConfUpdateFragment", func(t *testing.T) {
		err := m.CheckPackConfUpdate()
		if err != nil {
			t.Errorf("CheckPackConfUpdate error = %v", err)
			return
		}
	})
}

func TestMgr_CheckProfitFusing(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockStore := mocks.NewMockIStore(ctl)
	mockCache := mocks.NewMockICache(ctl)
	mockCfg := mocks.NewMockIBusinessConfManager(ctl)

	m := Mgr{
		store:      mockStore,
		redisCache: mockCache,
		bc:         mockCfg,
	}

	gomock.InOrder(
		mockCache.EXPECT().CheckIfFusing(gomock.Any()).Return(false, nil),
		mockCfg.EXPECT().GetHistoryProfitFusingMin().Return(int64(1000)),
		mockCfg.EXPECT().GetHourProfitFusingMin().Return(int64(100)),
		mockCache.EXPECT().SetFusing(gomock.Any()).Return(nil),
		mockCfg.EXPECT().GetHourProfitFusingMin().Return(int64(100)),
		mockCfg.EXPECT().GetFeiShuRobotUrl().Return(""),
	)

	t.Run("TestMgr_CheckProfitFusing", func(t *testing.T) {
		err := m.CheckProfitFusing(-100, -200)
		if err != nil && protocol.ToServerError(err).Code() != status.ErrOnePieceNotAvailable {
			t.Errorf("CheckProfitFusing error = %v", err)
			return
		}
	})
}

func TestMgr_CheckProfitWarningHour(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockStore := mocks.NewMockIStore(ctl)
	mockCache := mocks.NewMockICache(ctl)
	mockCfg := mocks.NewMockIBusinessConfManager(ctl)

	m := Mgr{
		store:      mockStore,
		redisCache: mockCache,
		bc:         mockCfg,
	}

	var tmp time.Time
	gomock.InOrder(
		mockCache.EXPECT().CheckIfFusing(gomock.Any()).Return(false, nil),
		mockCache.EXPECT().CheckIfWarning(gomock.Any(), gomock.Any()).Return(false, nil),
		mockCache.EXPECT().GetHourProfit(gomock.Any(), gomock.Any()).Return(int64(-1000), nil),
		mockCfg.EXPECT().GetHourProfitWarnMin().Return(int64(100)),
		mockCache.EXPECT().SetWarning(gomock.Any(), gomock.Any()).Return(nil),
		mockCfg.EXPECT().GetFeiShuRobotUrl().Return(""),
	)

	t.Run("TestMgr_CheckProfitWarningHour", func(t *testing.T) {
		err := m.CheckProfitWarning(cache.HourWarnType, &tmp)
		if err != nil {
			t.Errorf("CheckProfitWarning error = %v", err)
			return
		}
	})
}

func TestMgr_CheckProfitWarningHistory(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockStore := mocks.NewMockIStore(ctl)
	mockCache := mocks.NewMockICache(ctl)
	mockCfg := mocks.NewMockIBusinessConfManager(ctl)

	m := Mgr{
		store:      mockStore,
		redisCache: mockCache,
		bc:         mockCfg,
	}

	var tmp time.Time
	gomock.InOrder(
		mockCache.EXPECT().CheckIfFusing(gomock.Any()).Return(false, nil),
		mockCache.EXPECT().CheckIfWarning(gomock.Any(), gomock.Any()).Return(false, nil),
		mockCache.EXPECT().GetHistoryProfit(gomock.Any()).Return(int64(-1000), nil),
		mockCfg.EXPECT().GetHistoryProfitUpperLimit().Return(int64(2000)),
		mockCfg.EXPECT().GetHistoryProfitWarnMin().Return(int64(100)),
		mockCache.EXPECT().SetWarning(gomock.Any(), gomock.Any()).Return(nil),
		mockCfg.EXPECT().GetFeiShuRobotUrl().Return(""),
	)

	t.Run("TestMgr_CheckProfitWarningHistory", func(t *testing.T) {
		err := m.CheckProfitWarning(cache.HistoryWarnType, &tmp)
		if err != nil {
			t.Errorf("CheckProfitWarning error = %v", err)
			return
		}
	})
}

func TestReportWindChange(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	t.Run("ReportWindChange", func(t *testing.T) {
		ReportWindChange(context.Background(), 1, time.Now().Unix(), time.Now().Unix()+60)
		ReportWindChange(context.Background(), 2, time.Now().Unix(), time.Now().Unix()+60)
		ReportWindChange(context.Background(), 3, time.Now().Unix(), time.Now().Unix()+60)
	})
}

func TestMgr_GetAwardOrderIds(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockStore := mocks.NewMockIStore(ctl)
	mockCache := mocks.NewMockICache(ctl)
	mockCfg := mocks.NewMockIBusinessConfManager(ctl)

	m := Mgr{
		store:      mockStore,
		redisCache: mockCache,
		bc:         mockCfg,
	}

	gomock.InOrder(
		mockStore.EXPECT().GetAwardOrderIds(gomock.Any(), gomock.Any(), gomock.Any()).Return([]string{}, nil),
	)

	t.Run("GetAwardOrderIds", func(t *testing.T) {
		_, err := m.GetAwardOrderIds(context.Background(), &reconcile_v2.TimeRangeReq{
			BeginTime: time.Now().Unix(),
			EndTime:   time.Now().Unix() + 60,
		})
		if err != nil {
			t.Errorf("GetAwardOrderIds fail err:%v", err)
		}
	})
}

func TestMgr_GetAwardTotalCount(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockStore := mocks.NewMockIStore(ctl)
	mockCache := mocks.NewMockICache(ctl)
	mockCfg := mocks.NewMockIBusinessConfManager(ctl)

	m := Mgr{
		store:      mockStore,
		redisCache: mockCache,
		bc:         mockCfg,
	}

	gomock.InOrder(
		mockStore.EXPECT().GetAwardTotalCountInfo(gomock.Any(), gomock.Any(), gomock.Any()).Return(&mysql.StCount{}, nil),
	)

	t.Run("GetAwardTotalCount", func(t *testing.T) {
		_, err := m.GetAwardTotalCount(context.Background(), &reconcile_v2.TimeRangeReq{
			BeginTime: time.Now().Unix(),
			EndTime:   time.Now().Unix() + 60,
		})
		if err != nil {
			t.Errorf("GetAwardTotalCount fail err:%v", err)
		}
	})
}

func TestMgr_GetConsumeOrderIds(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockStore := mocks.NewMockIStore(ctl)
	mockCache := mocks.NewMockICache(ctl)
	mockCfg := mocks.NewMockIBusinessConfManager(ctl)

	m := Mgr{
		store:      mockStore,
		redisCache: mockCache,
		bc:         mockCfg,
	}

	gomock.InOrder(
		mockStore.EXPECT().GetConsumeOrderIds(gomock.Any(), gomock.Any(), gomock.Any()).Return([]string{}, nil),
	)

	t.Run("GetConsumeOrderIds", func(t *testing.T) {
		_, err := m.GetConsumeOrderIds(context.Background(), &reconcile_v2.TimeRangeReq{
			BeginTime: time.Now().Unix(),
			EndTime:   time.Now().Unix() + 60,
		})
		if err != nil {
			t.Errorf("GetConsumeOrderIds fail err:%v", err)
		}
	})
}

func TestMgr_GetConsumeTotalCount(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockStore := mocks.NewMockIStore(ctl)
	mockCache := mocks.NewMockICache(ctl)
	mockCfg := mocks.NewMockIBusinessConfManager(ctl)

	m := Mgr{
		store:      mockStore,
		redisCache: mockCache,
		bc:         mockCfg,
	}

	gomock.InOrder(
		mockStore.EXPECT().GetConsumeTotalCountInfo(gomock.Any(), gomock.Any(), gomock.Any()).Return(&mysql.StCount{}, nil),
	)

	t.Run("GetConsumeTotalCount", func(t *testing.T) {
		_, err := m.GetConsumeTotalCount(context.Background(), &reconcile_v2.TimeRangeReq{
			BeginTime: time.Now().Unix(),
			EndTime:   time.Now().Unix() + 60,
		})
		if err != nil {
			t.Errorf("GetConsumeTotalCount fail err:%v", err)
		}
	})
}

func TestMgr_GetFragmentCfg(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockStore := mocks.NewMockIStore(ctl)
	mockCache := mocks.NewMockICache(ctl)
	mockCfg := mocks.NewMockIBusinessConfManager(ctl)
	mockBackpack := backpack.NewMockIClient(ctl)
	mockUserpresent := userPresent.NewMockIClient(ctl)

	itemCfgList := make([][]byte, 0)
	cfg := &backpackPb.LotteryFragmentCfg{
		FragmentId:   1,
		FragmentName: "test",
	}
	b, _ := cfg.Marshal()
	itemCfgList = append(itemCfgList, b)

	m := Mgr{
		store:          mockStore,
		redisCache:     mockCache,
		bc:             mockCfg,
		backpackCli:    mockBackpack,
		userPresentCli: mockUserpresent,
	}

	gomock.InOrder(
		mockBackpack.EXPECT().GetItemCfg(gomock.Any(), gomock.Any(), gomock.Any()).Return(&backpackPb.GetItemCfgResp{
			ItemCfgList: itemCfgList,
		}, nil),
	)

	t.Run("GetFragmentCfg", func(t *testing.T) {
		_, err := m.GetFragmentCfg(context.Background(), 1)
		if err != nil {
			t.Errorf("GetFragmentCfg fail err:%v", err)
		}
	})
}

func TestMgr_GetPresentCfg(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockStore := mocks.NewMockIStore(ctl)
	mockCache := mocks.NewMockICache(ctl)
	mockCfg := mocks.NewMockIBusinessConfManager(ctl)
	mockUserpresent := userPresent.NewMockIClient(ctl)

	m := Mgr{
		store:          mockStore,
		redisCache:     mockCache,
		bc:             mockCfg,
		userPresentCli: mockUserpresent,
	}

	gomock.InOrder(
		mockUserpresent.EXPECT().GetPresentConfigById(gomock.Any(), gomock.Any()).Return(&userpresentPB.GetPresentConfigByIdResp{
			ItemConfig: &userpresentPB.StPresentItemConfig{
				ItemId:    1,
				Name:      "test",
				IconUrl:   "test_url",
				Price:     100,
				PriceType: uint32(userpresentPB.PresentPriceType_PRESENT_PRICE_TBEAN),
			}}, nil),
	)

	t.Run("GetPresentCfg", func(t *testing.T) {
		_, err := m.GetPresentCfg(context.Background(), 1)
		if err != nil {
			t.Errorf("GetPresentCfg fail err:%v", err)
		}
	})
}

func TestMgr_GetOnePieceInfo(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockStore := mocks.NewMockIStore(ctl)
	mockCache := mocks.NewMockICache(ctl)
	mockCfg := mocks.NewMockIBusinessConfManager(ctl)

	m := Mgr{
		store:      mockStore,
		redisCache: mockCache,
		bc:         mockCfg,
	}

	newPool := initPrizePool(1, []*mysql.Prize{
		{
			Mode:     0,
			Wind:     0,
			IslandId: 2,
			Weight:   1,
		},
		{
			Mode:     0,
			Wind:     0,
			IslandId: 1,
			Weight:   1,
		},
	})
	_ = m.lottery.setPrizePool(0, 0, newPool)
	_ = m.lottery.setPrizePool(0, 1, newPool)
	_ = m.lottery.setPrizePool(0, 2, newPool)

	_ = m.lottery.setPrizePool(1, 0, newPool)
	_ = m.lottery.setPrizePool(1, 1, newPool)
	_ = m.lottery.setPrizePool(1, 2, newPool)

	gomock.InOrder(
		mockCache.EXPECT().GetWindInfo(gomock.Any()).Return(&cache.WindInfo{CurrWind: 0, NextWind: 2}, nil),
		mockCache.EXPECT().GetAllMinuteWindVal(gomock.Any()).Return(map[string]uint32{"test": 1}, nil),
		mockCfg.EXPECT().GetWindCfg().AnyTimes().Return(&conf.WindCfg{WindContinueMinutes: []uint32{5}, VirWindSpeedBase: 1, MaxWindSpeed: 1}),

		mockCache.EXPECT().GetUserRemainChance(gomock.Any(), gomock.Any()).Return(uint32(20), true, nil),
		mockCache.EXPECT().GetUserMileage(gomock.Any(), gomock.Any()).Return(uint32(20), false, nil),
		mockStore.EXPECT().GetUserRemainChance(gomock.Any(), gomock.Any()).Return(&mysql.RemainChance{
			Uid: 1, Amount: 20, Mileage: 20,
		}, true, nil),

		mockCache.EXPECT().SetUserMileage(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil),
		mockCache.EXPECT().SetUserRemainChance(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil),
		mockCfg.EXPECT().GetGoalMileage().Return(uint32(200)),
	)

	t.Run("GetOnePieceInfo", func(t *testing.T) {
		_, err := m.GetOnePieceInfo(context.Background(), uint32(1), pb.Mode_Senior)
		if err != nil {
			t.Errorf("GetOnePieceInfo fail err:%v", err)
		}
	})
}

func TestMgr_GetRecentWinningRecords(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockStore := mocks.NewMockIStore(ctl)
	mockCache := mocks.NewMockICache(ctl)
	mockCfg := mocks.NewMockIBusinessConfManager(ctl)

	m := Mgr{
		store:      mockStore,
		redisCache: mockCache,
		bc:         mockCfg,
	}

	m.lottery.setAllPrizeInfo(map[uint32]mysql.Prize{
		1: {PackId: 1}})

	gomock.InOrder(
		mockCache.EXPECT().GetWinningRecord(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return([]*mysql.AwardRecord{
			{PackId: 1},
		}, nil),
	)

	t.Run("GetRecentWinningRecords", func(t *testing.T) {
		_, err := m.GetRecentWinningRecords(context.Background(), uint32(1), 10, pb.Mode_Senior)
		if err != nil {
			t.Errorf("GetRecentWinningRecords fail err:%v", err)
		}
	})
}

func TestMgr_GetWinningRecords(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockStore := mocks.NewMockIStore(ctl)
	mockCache := mocks.NewMockICache(ctl)
	mockCfg := mocks.NewMockIBusinessConfManager(ctl)

	m := Mgr{
		store:      mockStore,
		redisCache: mockCache,
		bc:         mockCfg,
	}

	m.lottery.setAllPrizeInfo(map[uint32]mysql.Prize{
		1: {PackId: 1}})

	gomock.InOrder(
		mockStore.EXPECT().GetUserAwardRecords(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return([]*mysql.AwardRecord{
			{PackId: 1},
		}, nil),
	)

	t.Run("GetWinningRecords", func(t *testing.T) {
		_, err := m.GetWinningRecords(context.Background(), &pb.GetWinningRecordsReq{
			Uid:   1,
			Limit: 10,
		})
		if err != nil {
			t.Errorf("GetWinningRecords fail err:%v", err)
		}
	})
}

func TestMgr_CheckProfitWarning(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockStore := mocks.NewMockIStore(ctl)
	mockCache := mocks.NewMockICache(ctl)
	mockCfg := mocks.NewMockIBusinessConfManager(ctl)

	m := Mgr{
		store:      mockStore,
		redisCache: mockCache,
		bc:         mockCfg,
	}

	m.lottery.setAllPrizeInfo(map[uint32]mysql.Prize{
		1: {PackId: 1}})

	gomock.InOrder(
		mockCache.EXPECT().CheckIfFusing(gomock.Any()).Return(false, nil),
		mockCache.EXPECT().CheckIfWarning(gomock.Any(), gomock.Any()).Return(false, nil),
		mockCache.EXPECT().GetHourProfit(gomock.Any(), gomock.Any()).Return(int64(-100), nil),
		mockCfg.EXPECT().GetHourProfitWarnMin().Return(int64(100)),
		mockCache.EXPECT().SetWarning(gomock.Any(), gomock.Any()).Return(nil),
		mockCfg.EXPECT().GetFeiShuRobotUrl().AnyTimes().Return(""),
	)

	now := time.Now().Add(-time.Hour)
	t.Run("CheckProfitWarning", func(t *testing.T) {
		err := m.CheckProfitWarning(cache.HourWarnType, &now)
		if err != nil {
			t.Errorf("CheckProfitWarning fail err:%v", err)
		}
	})

	gomock.InOrder(
		mockCache.EXPECT().CheckIfFusing(gomock.Any()).Return(false, nil),
		mockCache.EXPECT().CheckIfWarning(gomock.Any(), gomock.Any()).Return(false, nil),
		mockCache.EXPECT().GetHistoryProfit(gomock.Any()).Return(int64(100), nil),
		mockCfg.EXPECT().GetHistoryProfitUpperLimit().Return(int64(100)),
		mockCfg.EXPECT().GetHistoryProfitExtract().Return(int64(100)),
		mockCache.EXPECT().IncrHistoryProfit(gomock.Any(), gomock.Any()).Return(int64(0), nil),
		mockCfg.EXPECT().GetHistoryProfitWarnMin().Return(int64(-100)),
		mockCache.EXPECT().SetWarning(gomock.Any(), gomock.Any()).Return(nil),
		mockCfg.EXPECT().GetFeiShuRobotUrl().AnyTimes().Return(""),
	)

	t.Run("CheckProfitWarning", func(t *testing.T) {
		err := m.CheckProfitWarning(cache.HistoryWarnType, &now)
		if err != nil {
			t.Errorf("CheckProfitWarning fail err:%v", err)
		}
	})
}

func TestMgr_GetBoxCfgList(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockStore := mocks.NewMockIStore(ctl)
	mockCache := mocks.NewMockICache(ctl)
	mockCfg := mocks.NewMockIBusinessConfManager(ctl)

	m := Mgr{
		store:      mockStore,
		redisCache: mockCache,
		bc:         mockCfg,
	}

	gomock.InOrder(
		mockStore.EXPECT().GetEffectBoxCfgList(gomock.Any()).Return([]*mysql.Box{{Id: 1}}, nil),
	)

	t.Run("GetBoxCfgList", func(t *testing.T) {
		_, err := m.GetBoxCfgList(context.Background())
		if err != nil {
			t.Errorf("GetBoxCfgList fail err:%v", err)
		}
	})
}

func TestMgr_GetOnePieceLimitConf(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockStore := mocks.NewMockIStore(ctl)
	mockCache := mocks.NewMockICache(ctl)
	mockCfg := mocks.NewMockIBusinessConfManager(ctl)

	m := Mgr{
		store:      mockStore,
		redisCache: mockCache,
		bc:         mockCfg,
	}

	gomock.InOrder(
		mockCache.EXPECT().GetPriceLimitCfg(gomock.Any()).Return(nil, nil),
		mockStore.EXPECT().GetLimitConfList(gomock.Any()).Return([]*mysql.UserLimitCfg{
			{LimitType: mysql.DailyBuyLimitType},
			{LimitType: mysql.DailyUseLimitType},
			{LimitType: mysql.SingleBuyLimitType},
		}, nil),
		mockCache.EXPECT().SetPriceLimitCfg(gomock.Any(), gomock.Any()).Return(nil),
	)

	t.Run("GetOnePieceLimitConf", func(t *testing.T) {
		_, err := m.GetOnePieceLimitConf(context.Background())
		if err != nil {
			t.Errorf("GetOnePieceLimitConf fail err:%v", err)
		}
	})
}

func TestMgr_GetOnePiecePrizePool(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockStore := mocks.NewMockIStore(ctl)
	mockCache := mocks.NewMockICache(ctl)
	mockCfg := mocks.NewMockIBusinessConfManager(ctl)

	m := Mgr{
		store:      mockStore,
		redisCache: mockCache,
		bc:         mockCfg,
	}

	gomock.InOrder(
		mockStore.EXPECT().GetEffectPrizeList(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return([]*mysql.Prize{{IslandId: 1}}, nil),
		mockStore.EXPECT().GetIslandBox(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return([]*mysql.IslandBox{{IslandId: 1}}, nil),
	)

	t.Run("GetOnePiecePrizePool", func(t *testing.T) {
		_, err := m.GetOnePiecePrizePool(context.Background(), &pb.GetOnePiecePrizePoolReq{})
		if err != nil {
			t.Errorf("GetOnePiecePrizePool fail err:%v", err)
		}
	})
}

func TestMgr_ReleaseFusing(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockStore := mocks.NewMockIStore(ctl)
	mockCache := mocks.NewMockICache(ctl)
	mockCfg := mocks.NewMockIBusinessConfManager(ctl)

	m := Mgr{
		store:      mockStore,
		redisCache: mockCache,
		bc:         mockCfg,
	}

	gomock.InOrder(
		mockCache.EXPECT().CheckIfFusing(gomock.Any()).Return(true, nil),
		mockCache.EXPECT().GetHourProfit(gomock.Any(), gomock.Any()).Return(int64(0), nil),
		mockCache.EXPECT().GetHistoryProfit(gomock.Any()).Return(int64(0), nil),

		mockCfg.EXPECT().GetHistoryProfitFusingMin().Return(int64(0)),
		mockCache.EXPECT().IncrHistoryProfit(gomock.Any(), gomock.Any()).Return(int64(0), nil),

		mockCfg.EXPECT().GetHourProfitFusingMin().Return(int64(0)),
		mockCache.EXPECT().DelHourProfit(gomock.Any(), gomock.Any()).Return(nil),
		mockCache.EXPECT().DelFusing(gomock.Any()).Return(nil),

		mockCfg.EXPECT().GetFeiShuRobotUrl().AnyTimes().Return(""),
	)

	t.Run("ReleaseFusing", func(t *testing.T) {
		err := m.ReleaseFusing(5000)
		if err != nil {
			t.Errorf("ReleaseFusing fail err:%v", err)
		}
	})
}

func TestMgr_LotteryDraw(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockStore := mocks.NewMockIStore(ctl)
	mockCache := mocks.NewMockICache(ctl)
	mockCfg := mocks.NewMockIBusinessConfManager(ctl)

	m := Mgr{
		store:      mockStore,
		redisCache: mockCache,
		bc:         mockCfg,
	}

	gomock.InOrder(
		mockCache.EXPECT().GetPriceLimitCfg(gomock.Any()).Return(map[uint32]uint32{mysql.DailyUseLimitType: 100}, nil),
		mockCache.EXPECT().GetUserDailyPrice(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(uint32(10), nil),
		mockCache.EXPECT().GetWindInfo(gomock.Any()).Return(&cache.WindInfo{}, nil),

		mockStore.EXPECT().Transaction(gomock.Any(), gomock.Any()).Return(nil),
		mockCache.EXPECT().RecordWinning(gomock.Any(), gomock.Any()).Return(nil),

		mockCache.EXPECT().IncrUserDailyPrice(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil),
		mockCfg.EXPECT().GetUserInvestFlagExpireDay().Return(uint32(30)),
		mockCache.EXPECT().SetUserInvestFlagWithExpire(gomock.Any(), uint32(1), time.Duration(30*24*3600)*time.Second),
		mockCache.EXPECT().DelUserRemainChance(gomock.Any(), gomock.Any()).Return(nil),
		mockCache.EXPECT().DelUserMileage(gomock.Any(), gomock.Any()).Return(nil),
		mockCache.EXPECT().AddMinuteWindVal(gomock.Any(), gomock.Any()).Return(nil),

		mockCache.EXPECT().GetWindInfo(gomock.Any()).Return(&cache.WindInfo{CurrWind: 0, NextWind: 2}, nil),
		mockCache.EXPECT().GetAllMinuteWindVal(gomock.Any()).Return(map[string]uint32{"test": 1}, nil),
		mockCfg.EXPECT().GetWindCfg().AnyTimes().Return(&conf.WindCfg{WindContinueMinutes: []uint32{5}, VirWindSpeedBase: 1, MaxWindSpeed: 1}),
	)

	t.Run("LotteryDraw", func(t *testing.T) {
		_, err := m.LotteryDraw(context.Background(), &pb.LotteryDrawReq{
			Uid:          1,
			ChannelId:    1,
			ChanceAmount: 2,
		})
		if err != nil {
			t.Errorf("LotteryDraw fail err:%v", err)
		}
	})
}

func TestMgr_handleLotteryWinningList(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockStore := mocks.NewMockIStore(ctl)
	mockCache := mocks.NewMockICache(ctl)
	mockCfg := mocks.NewMockIBusinessConfManager(ctl)

	m := Mgr{
		store:      mockStore,
		redisCache: mockCache,
		bc:         mockCfg,
	}

	m.lottery.setAllPrizeInfo(map[uint32]mysql.Prize{
		1: {PackId: 1}})

	t.Run("handleLotteryWinningList", func(t *testing.T) {
		m.handleLotteryWinningList([]*mysql.AwardRecord{
			{PackId: 1}, {PackId: 2, BingoType: 2},
		})
	})
}

func TestMgr_lotteryDrawTransaction(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockStore := mocks.NewMockIStore(ctl)
	mockCache := mocks.NewMockICache(ctl)
	mockCfg := mocks.NewMockIBusinessConfManager(ctl)
	mockProbCli := probgamecenter.NewMockIClient(ctl)

	m := Mgr{
		store:       mockStore,
		redisCache:  mockCache,
		bc:          mockCfg,
		probGameCli: mockProbCli,
	}

	newPool := initPrizePool(1, []*mysql.Prize{
		{
			Mode:     0,
			Wind:     0,
			IslandId: 2,
			Weight:   1,
			PackId:   1,
		},
		{
			Mode:     0,
			Wind:     0,
			IslandId: 1,
			Weight:   1,
			PackId:   2,
		},
	})
	_ = m.lottery.setPrizePool(0, 0, newPool)
	_ = m.lottery.setPrizePool(0, 1, newPool)
	_ = m.lottery.setPrizePool(0, 2, newPool)

	_ = m.lottery.setPrizePool(1, 0, newPool)
	_ = m.lottery.setPrizePool(1, 1, newPool)
	_ = m.lottery.setPrizePool(1, 2, newPool)

	m.lottery.setAllPrizeInfo(map[uint32]mysql.Prize{
		1: {PackId: 1}})

	gomock.InOrder(
		mockStore.EXPECT().GetUserRemainChanceForUpdate(gomock.Any(), gomock.Any(), gomock.Any()).Return(&mysql.RemainChance{Amount: 10}, true, nil),
		mockCfg.EXPECT().GetMaxRandN().Return(uint32(100)),
		mockCfg.EXPECT().GetGoalMileage().Return(uint32(1000)),
		mockCfg.EXPECT().GetBingoId().AnyTimes().Return(uint32(1)),

		mockCfg.EXPECT().GetBackSenderInfo().AnyTimes().Return(uint32(1), "test"),
		mockCache.EXPECT().IncrHourProfit(gomock.Any(), gomock.Any(), gomock.Any()).Return(int64(0), nil),
		mockCache.EXPECT().IncrHistoryProfit(gomock.Any(), gomock.Any()).Return(int64(0), nil),
		mockCache.EXPECT().CheckIfFusing(gomock.Any()).Return(false, nil),
		mockCfg.EXPECT().GetHistoryProfitFusingMin().Return(int64(100)),
		mockCfg.EXPECT().GetHourProfitFusingMin().Return(int64(100)),

		mockProbCli.EXPECT().CheckFuse(gomock.Any(), gomock.Any()).Return(nil, nil),
		mockStore.EXPECT().InsertAwardRecords(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil),
		mockStore.EXPECT().DecrUserRemainChance(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(true, nil),
	)

	t.Run("lotteryDrawTransaction", func(t *testing.T) {
		_, _, _, _, err := m.lotteryDrawTransaction(context.Background(), nil, 1, 1, define.PrizeDrawReq{Cnt: 1}, time.Now())
		if err != nil {
			t.Errorf("lotteryDrawTransaction fail err:%v", err)
		}
	})
}

func TestMgr_handleTimeoutBingoNotify(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockStore := mocks.NewMockIStore(ctl)
	mockCache := mocks.NewMockICache(ctl)
	mockCfg := mocks.NewMockIBusinessConfManager(ctl)
	mockProbCli := probgamecenter.NewMockIClient(ctl)
	mockPushCli := publicnotice.NewMockIClient(ctl)
	mockApiCenterCli := apicenter.NewMockIClient(ctl)

	m := Mgr{
		store:           mockStore,
		redisCache:      mockCache,
		bc:              mockCfg,
		probGameCli:     mockProbCli,
		publicNoticeCli: mockPushCli,
		apiCenterCli:    mockApiCenterCli,
	}

	gomock.InOrder(
		mockCache.EXPECT().PopExpireBingoNotify(gomock.Any()).Return(&pb.WinningRecord{
			Id: "", AwardInfo: &pb.AwardPackInfo{PackId: 1, PackAmount: 1},
		}, true, nil),
		mockCfg.EXPECT().NeedBingoBreakingNews().Return(true),
		mockPushCli.EXPECT().PushBreakingNews(gomock.Any(), gomock.Any()).Return(nil, nil),
		mockCfg.EXPECT().GetBingoTtMsg().Return("test"),
		mockApiCenterCli.EXPECT().SendImMsg(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil),
	)

	t.Run("handleTimeoutBingoNotify", func(t *testing.T) {
		err := m.handleTimeoutBingoNotify()
		if err != nil {
			t.Errorf("handleTimeoutBingoNotify fail err:%v", err)
		}
	})
}

func TestMgr_SetBoxCfg(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockStore := mocks.NewMockIStore(ctl)
	mockCache := mocks.NewMockICache(ctl)
	mockCfg := mocks.NewMockIBusinessConfManager(ctl)

	m := Mgr{
		store:      mockStore,
		redisCache: mockCache,
		bc:         mockCfg,
	}

	gomock.InOrder(
		mockStore.EXPECT().AddBoxCfg(gomock.Any(), gomock.Any()).Return(nil),
	)

	t.Run("SetBoxCfg", func(t *testing.T) {
		err := m.SetBoxCfg(context.Background(), &pb.BoxCfg{
			Name: "test",
		})
		if err != nil {
			t.Errorf("SetBoxCfg fail err:%v", err)
		}
	})
}

func TestMgr_DelBoxCfg(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockStore := mocks.NewMockIStore(ctl)
	mockCache := mocks.NewMockICache(ctl)
	mockCfg := mocks.NewMockIBusinessConfManager(ctl)

	m := Mgr{
		store:      mockStore,
		redisCache: mockCache,
		bc:         mockCfg,
	}

	gomock.InOrder(
		mockStore.EXPECT().DelBoxCfg(gomock.Any(), gomock.Any()).Return(nil),
	)

	t.Run("DelBoxCfg", func(t *testing.T) {
		err := m.DelBoxCfg(context.Background(), 1)
		if err != nil {
			t.Errorf("DelBoxCfg fail err:%v", err)
		}
	})
}

func TestMgr_UpdateBoxCfg(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockStore := mocks.NewMockIStore(ctl)
	mockCache := mocks.NewMockICache(ctl)
	mockCfg := mocks.NewMockIBusinessConfManager(ctl)

	m := Mgr{
		store:      mockStore,
		redisCache: mockCache,
		bc:         mockCfg,
	}

	gomock.InOrder(
		mockStore.EXPECT().UpdateBoxCfg(gomock.Any(), gomock.Any()).Return(nil),
	)

	t.Run("UpdateBoxCfg", func(t *testing.T) {
		err := m.UpdateBoxCfg(context.Background(), &pb.BoxCfg{
			Name: "test",
			Id:   1,
		})
		if err != nil {
			t.Errorf("UpdateBoxCfg fail err:%v", err)
		}
	})
}

func TestMgr_SetOnePieceLimitConf(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockStore := mocks.NewMockIStore(ctl)
	mockCache := mocks.NewMockICache(ctl)
	mockCfg := mocks.NewMockIBusinessConfManager(ctl)

	m := Mgr{
		store:      mockStore,
		redisCache: mockCache,
		bc:         mockCfg,
	}

	gomock.InOrder(
		mockStore.EXPECT().SetLimitConfList(gomock.Any(), gomock.Any()).Return(nil),
		mockCache.EXPECT().SetPriceLimitCfg(gomock.Any(), gomock.Any()).Return(nil),
	)

	t.Run("SetOnePieceLimitConf", func(t *testing.T) {
		_, err := m.SetOnePieceLimitConf(context.Background(), &pb.SetOnePieceLimitConfReq{})
		if err != nil {
			t.Errorf("SetOnePieceLimitConf fail err:%v", err)
		}
	})
}

func TestMgr_SetOnePiecePrizePool(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockStore := mocks.NewMockIStore(ctl)
	mockCache := mocks.NewMockICache(ctl)
	mockCfg := mocks.NewMockIBusinessConfManager(ctl)

	m := Mgr{
		store:      mockStore,
		redisCache: mockCache,
		bc:         mockCfg,
	}

	gomock.InOrder(
		mockCfg.EXPECT().GetMinAvgProfit().AnyTimes().Return(0),
		mockStore.EXPECT().SetPrizeList(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil),
		mockStore.EXPECT().BatchBindIslandBox(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil),
	)

	t.Run("SetOnePiecePrizePool", func(t *testing.T) {
		_, err := m.SetOnePiecePrizePool(context.Background(), &pb.SetOnePiecePrizePoolReq{
			PrizeList: []*pb.Prize{
				{Weight: 10, PackId: 1, PackAmount: 1, IslandId: 1, PackWorth: 0},
			},
		})
		if err != nil {
			t.Errorf("SetOnePiecePrizePool fail err:%v", err)
		}
	})
}

func TestMgr_getAwardStatistics(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockStore := mocks.NewMockIStore(ctl)
	mockCache := mocks.NewMockICache(ctl)
	mockCfg := mocks.NewMockIBusinessConfManager(ctl)

	m := Mgr{
		store:      mockStore,
		redisCache: mockCache,
		bc:         mockCfg,
	}

	gomock.InOrder(
		mockStore.EXPECT().GetAwardStatistics(gomock.Any(), gomock.Any(), gomock.Any()).Return(map[uint32]*mysql.Statistics{}, nil),
		mockStore.EXPECT().GetLotteryTotalPeopleCnt(gomock.Any(), gomock.Any(), gomock.Any()).Return(int64(10), nil),
	)

	t.Run("getAwardStatistics", func(t *testing.T) {
		m.getAwardStatistics(context.Background(), time.Now(), time.Now().Add(time.Hour))
	})
}

func Test_genReportStrWithCmp(t *testing.T) {
	t.Run("genReportStrWithCmp", func(t *testing.T) {
		str := genReportStrWithCmp("test", &Stats{}, &Stats{}, &Stats{}, false)
		if len(str) == 0 {
			t.Errorf("genReportStrWithCmp fail str:%v", str)
		}
	})
}

func TestMgr_sendPackage(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockStore := mocks.NewMockIStore(ctl)
	mockCache := mocks.NewMockICache(ctl)
	mockCfg := mocks.NewMockIBusinessConfManager(ctl)
	mockBackpackSender := backpacksender.NewMockIClient(ctl)

	m := Mgr{
		store:             mockStore,
		redisCache:        mockCache,
		bc:                mockCfg,
		backpackSenderCli: mockBackpackSender,
	}

	gomock.InOrder(
		mockCfg.EXPECT().GetBackSenderInfo().Return(uint32(1), "hqfycvvqjzkvrusg"),
		mockBackpackSender.EXPECT().SendBackpackWithRiskControl(gomock.Any(), gomock.Any()).Return(nil, nil),
		mockStore.EXPECT().ChangeAwardRecordStatus(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil),
	)

	t.Run("sendPackage", func(t *testing.T) {
		m.sendPackage([]*mysql.AwardRecord{
			{OrderId: "test"},
		}, map[string]string{"test": "test"}, 0)
	})
}

func TestMgr_GetOnePieceExemptVal(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockCache := mocks.NewMockICache(ctl)
	mockStore := mocks.NewMockIStore(ctl)

	m := Mgr{
		redisCache: mockCache,
		store:      mockStore,
	}

	uid := uint32(1)

	type args struct {
		ctx context.Context
		uid uint32
	}
	tests := []struct {
		name           string
		initFunc       func()
		args           args
		wantInvestFlag bool
		wantMileage    uint32
		wantErr        bool
	}{
		{
			name: "TestMgr_GetOnePieceExemptVal",
			initFunc: func() {
				mockCache.EXPECT().GetUserInvestFlag(gomock.Any(), uid).Return(true, nil)
				mockCache.EXPECT().GetUserMileage(gomock.Any(), uid).Return(uint32(0), false, nil)
				mockStore.EXPECT().GetUserRemainChance(gomock.Any(), gomock.Any()).Return(&mysql.RemainChance{
					Uid: 1, Amount: 20, Mileage: 20,
				}, true, nil)

				mockCache.EXPECT().SetUserMileage(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
			},
			args: args{
				ctx: context.Background(),
				uid: uid,
			},
			wantInvestFlag: true,
			wantMileage:    uint32(20),
			wantErr:        false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := m
			if tt.initFunc != nil {
				tt.initFunc()
			}
			gotInvestFlag, gotMileage, err := m.GetOnePieceExemptVal(tt.args.ctx, tt.args.uid)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetOnePieceExemptVal() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if gotInvestFlag != tt.wantInvestFlag {
				t.Errorf("GetOnePieceExemptVal() gotInvestFlag = %v, want %v", gotInvestFlag, tt.wantInvestFlag)
			}
			if gotMileage != tt.wantMileage {
				t.Errorf("GetOnePieceExemptVal() gotMileage = %v, want %v", gotMileage, tt.wantMileage)
			}
		})
	}
}
