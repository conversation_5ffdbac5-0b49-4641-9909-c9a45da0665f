package server

import (
	"context"
	"errors"
	"fmt"
	"github.com/opentracing/opentracing-go"
	account "golang.52tt.com/clients/account-go"
	treasure_house "golang.52tt.com/clients/treasure-house"
	usualDevice "golang.52tt.com/clients/usual-device"
	"golang.52tt.com/pkg/decoration"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	protogrpc "golang.52tt.com/pkg/protocol/grpc"
	"golang.52tt.com/protocol/app/treasure_house_logic"
	"golang.52tt.com/protocol/common/status"
	pb "golang.52tt.com/protocol/services/treasure-house"
	"golang.52tt.com/services/treasure-house-logic/cache"
	"sort"
	"time"

	"golang.52tt.com/pkg/config"
	"golang.52tt.com/services/treasure-house-logic/conf"
)

// 兜底问号文案
var interrogationText = "商品属性：礼物使用权\n使用价格：%d豆"

const titleGet = "https://ga-album-cdnqn.52tt.com/guildmedal/title_zbg_pop_acquire.png"
const titleBuy = "https://ga-album-cdnqn.52tt.com/guildmedal/title_zbg_pop_buy.png"

type TreasureHouseLogic_ struct {
	sc             conf.IServiceConfigT
	treasureCli    treasure_house.IClient
	accountCli     account.IClient
	usualDeviceCli usualDevice.IClient
	presentCache   cache.IPresentLocalCache
	remindCfgCache cache.ITreasureRemindCfgLocalCache
	whiteListCache cache.IWhiteListLocalCache
}

func (s *TreasureHouseLogic_) GetTreasureActivityList(ctx context.Context, req *treasure_house_logic.GetTreasureActivityListReq) (*treasure_house_logic.GetTreasureActivityListResp, error) {
	resp := new(treasure_house_logic.GetTreasureActivityListResp)
	resp.TreasureActivityList = make([]*treasure_house_logic.TreasureActivity, 0)

	serviceInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
	if !ok {
		log.ErrorWithCtx(ctx, "GetTreasureActivityList ServiceInfoFromContext fail. req:%+v", req)
		return resp, protocol.NewExactServerError(nil, status.ErrBadRequest)
	}
	uid := serviceInfo.UserID
	marketId := serviceInfo.MarketID
	os := protocol.NewTerminalType(serviceInfo.TerminalType).OS()

	log.DebugWithCtx(ctx, "GetTreasureActivityList uid:%d, marketId:%d, os:%d", uid, marketId, os)

	// 入口开关
	if !s.sc.GetEntranceSwitch() {
		// 非白名单
		if !s.whiteListCache.IsWhiteList(uid) {
			resp.EntranceSwitch = treasure_house_logic.TreasureEntranceSwitch_TREASURE_ENTRANCE_SWITCH_OFF
			return resp, nil
		}
	}

	resp.EntranceSwitch = treasure_house_logic.TreasureEntranceSwitch_TREASURE_ENTRANCE_SWITCH_ON

	treasureResp, err := s.treasureCli.GetActivityConfigList(ctx, &pb.GetActivityConfigListReq{
		Uid:   uid,
		Limit: 100,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetTreasureActivityList GetActivityConfigList err: %v", err)
		return resp, err
	}

	for _, ac := range treasureResp.GetActivityConfigList() {
		info := s.ConvActivityConfigPbToLogic(ac, marketId, os)
		resp.TreasureActivityList = append(resp.TreasureActivityList, info)
	}

	return resp, nil
}

func (s *TreasureHouseLogic_) GetTreasureActivity(ctx context.Context, req *treasure_house_logic.GetTreasureActivityReq) (*treasure_house_logic.GetTreasureActivityResp, error) {
	resp := new(treasure_house_logic.GetTreasureActivityResp)

	serviceInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
	if !ok {
		log.ErrorWithCtx(ctx, "GetTreasureActivity ServiceInfoFromContext fail. req:%+v", req)
		return resp, protocol.NewExactServerError(nil, status.ErrBadRequest)
	}
	uid := serviceInfo.UserID
	marketId := serviceInfo.MarketID
	os := protocol.NewTerminalType(serviceInfo.TerminalType).OS()

	treasureResp, err := s.treasureCli.GetActivityConfig(ctx, req.GetActivityId(), uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetTreasureActivity GetActivityConfig err: %v", err)
		return resp, err
	}

	resp.TreasureActivity = s.ConvActivityConfigPbToLogic(treasureResp.GetActivityConfig(), marketId, os)
	return resp, nil
}

func (s *TreasureHouseLogic_) ConvActivityConfigPbToLogic(ac *pb.ActivityConfig, markId uint32, os protocol.OS) *treasure_house_logic.TreasureActivity {
	if ac == nil {
		return nil
	}
	phaseList := ac.GetActivityPhaseList()
	userPermission := ac.GetUserPermissionInfo()
	now := time.Now()

	// 获取当前阶段
	var curPhase *pb.ActivityPhase
	sort.Slice(phaseList, func(i, j int) bool {
		return phaseList[i].StartTime > phaseList[j].StartTime
	})
	for _, p := range phaseList {
		if int64(p.GetStartTime()) <= now.Unix() {
			if p.GetForever() || int64(p.GetEndTime()) >= now.Unix() {
				curPhase = p
			}
		}
	}

	// 阶段永久时结束时间置0
	if curPhase.GetForever() {
		curPhase.EndTime = 0
	}

	// 活动结束阶段
	if curPhase == nil {
		curPhase = &pb.ActivityPhase{
			PhaseType: pb.PhaseType_PHASE_TYPE_ENDING,
		}
	}

	present := s.presentCache.GetPresentCache(ac.GetGiftId())
	presentPermission := treasure_house_logic.TreasurePermissionStatus(userPermission.GetPresentPermission())

	// 跳转按钮转换
	var buttonAction treasure_house_logic.TreasureButtonAction
	var buttonLink string
	switch curPhase.GetButtonAction() {
	case pb.ButtonAction_BUTTON_ACTION_ACTIVITY_JUMP:
		buttonAction = treasure_house_logic.TreasureButtonAction_TREASURE_BUTTON_ACTION_JUMP
		buttonLink = getPackageJump(curPhase.GetPackageJumpConfig(), markId, os)
		// 跳转链接为空视为不可点击
		if buttonLink == "" {
			buttonAction = treasure_house_logic.TreasureButtonAction_TREASURE_BUTTON_ACTION_DISABLE
			log.WarnWithCtx(context.Background(), "ConvActivityConfigPbToLogic buttonLink is empty. activityId:%d, markId:%d, os:%d",
				ac.GetActivityId(), markId, os)
		}
	case pb.ButtonAction_BUTTON_ACTION_JUMP:
		buttonAction = treasure_house_logic.TreasureButtonAction_TREASURE_BUTTON_ACTION_JUMP
		buttonLink = curPhase.GetButtonLink()
	default:
		buttonAction = treasure_house_logic.TreasureButtonAction(curPhase.GetButtonAction())
	}

	// 问号文案兜底
	inText := curPhase.GetInterrogationText()
	if inText == "" {
		switch curPhase.GetPhaseType() {
		case pb.PhaseType_PHASE_TYPE_WARMUP,
			pb.PhaseType_PHASE_TYPE_CONDITION:
			inText = fmt.Sprintf(interrogationText, present.GetPrice())
		case pb.PhaseType_PHASE_TYPE_ACTIVITY,
			pb.PhaseType_PHASE_TYPE_TOP_UP,
			pb.PhaseType_PHASE_TYPE_PURCHASE:
			inText = fmt.Sprintf(interrogationText, present.GetPrice())
			if curPhase.GetValidTimeSeconds() > 0 {
				validDur := convValidTime(curPhase.GetValidTimeSeconds())
				inText += fmt.Sprintf("\n有效期：%s", validDur)
			} else {
				// 截止时间
				if curPhase.GetValidTimeDeadline() > 0 {
					expireText := time.Unix(int64(curPhase.GetValidTimeDeadline()), 0).Format("2006.01.02 15:04")
					inText += fmt.Sprintf("\n截至%s", expireText)
				}
			}
		default:
		}
	}

	// 客户端要求权限过期后把按钮禁用
	if presentPermission == treasure_house_logic.TreasurePermissionStatus_TREASURE_PERMISSION_STATUS_EXPIRE {
		buttonAction = treasure_house_logic.TreasureButtonAction_TREASURE_BUTTON_ACTION_DISABLE
	}

	return &treasure_house_logic.TreasureActivity{
		ActivityId:        ac.GetActivityId(),
		ActivityStartTime: ac.GetStartTime(),
		ActivityEndTime:   ac.GetEndTime(),
		ActivityForever:   ac.GetForever(),
		PhaseType:         treasure_house_logic.TreasurePhaseType(curPhase.GetPhaseType()),
		ButtonAction:      buttonAction,
		ButtonLink:        buttonLink,
		ButtonTopText:     curPhase.GetButtonTopText(),
		InterrogationText: inText,
		MsgBoxTitle:       curPhase.GetMsgBoxTitle(),
		MsgBoxContent:     curPhase.GetMsgBoxContent(),
		PurchasedCount:    curPhase.GetPurchasedCount(),
		ValidTime:         curPhase.GetValidTimeSeconds(),
		PresentPermission: presentPermission,
		PhaseStartTime:    curPhase.GetStartTime(),
		PhaseEndTime:      curPhase.GetEndTime(),
		TopUpAmount:       curPhase.GetTopUpAmount(),
		TopUpProgress:     userPermission.GetTopUpProgress(),
		ExpireTime:        userPermission.GetExpireTime(),
		PresentPrivilege:  treasure_house_logic.TreasurePresentPrivilege(userPermission.GetPresentPrivilege()),
		PresentInfo: &treasure_house_logic.TreasurePresentInfo{
			GiftId:        ac.GetGiftId(),
			Name:          present.GetName(),
			IconUrl:       present.GetIconUrl(),
			Price:         uint64(present.GetPrice()),
			DiscountPrice: curPhase.GetDiscountPrice(),
			BackgroundUrl: ac.GetBackgroundUrl(),
			VideoUrl:      ac.VideoUrl,
			VideoMd5:      ac.GetVideoMd5(),
		},
	}
}

func (s *TreasureHouseLogic_) ClaimPresentPermission(ctx context.Context, req *treasure_house_logic.ClaimPresentPermissionReq) (
	*treasure_house_logic.ClaimPresentPermissionResp, error) {
	resp := new(treasure_house_logic.ClaimPresentPermissionResp)

	serviceInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
	if !ok {
		log.ErrorWithCtx(ctx, "ClaimPresentPermission ServiceInfoFromContext fail. req:%+v", req)
		return resp, protocol.NewExactServerError(nil, status.ErrBadRequest)
	}
	uid := serviceInfo.UserID
	marketId := serviceInfo.MarketID
	os := protocol.NewTerminalType(serviceInfo.TerminalType).OS()

	treasureResp, err := s.treasureCli.ClaimPresentPermission(ctx, &pb.ClaimPresentPermissionReq{
		ActivityId: req.GetActivityId(),
		PhaseType:  pb.PhaseType(req.GetPhaseType()),
		Uid:        uid,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "ClaimPresentPermission ClaimPresentPermission err: %v", err)
		return resp, err
	}

	resp.TreasureActivity = s.ConvActivityConfigPbToLogic(treasureResp.GetActivityConfig(), marketId, os)

	if resp.TreasureActivity.GetPhaseType() == treasure_house_logic.TreasurePhaseType_TREASURE_PHASE_TYPE_CONDITION {
		expireTime := time.Unix(int64(resp.TreasureActivity.GetExpireTime()), 0).Local()
		user, _ := s.accountCli.GetUserByUid(ctx, uid)
		resp.GetTreasureActivity().ExtendJson = decoration.GetTreasureInviteJson(resp.TreasureActivity.GetPresentInfo().GetName(), user.GetNickname(), expireTime)
	} else {
		// 如果是按天数/小时发
		timeStr := ""
		if resp.TreasureActivity.GetValidTime() != 0 && resp.TreasureActivity.GetValidTime() >= 86400 {
			timeStr = fmt.Sprintf("%d天", resp.TreasureActivity.GetValidTime()/86400)
		} else if resp.TreasureActivity.GetValidTime() != 0 && resp.TreasureActivity.GetValidTime() < 86400 {
			timeStr = fmt.Sprintf("%d小时", resp.TreasureActivity.GetValidTime()/3600)
		} else {
			expireTime := time.Unix(int64(resp.GetTreasureActivity().GetExpireTime()), 0).Local()
			timeStr = fmt.Sprintf("%s后到期", expireTime.Format("2006.01.02 15:04"))
		}
		resp.GetTreasureActivity().ExtendJson = decoration.GetTreasureReceiveJson(resp.GetTreasureActivity().GetPresentInfo().GetName(),
			resp.GetTreasureActivity().GetPresentInfo().GetIconUrl(), timeStr, titleGet,
		)
	}

	log.InfoWithCtx(ctx, "ClaimPresentPermission req:%+v resp:%+v", req, resp)

	return resp, nil
}

func (s *TreasureHouseLogic_) BuyPresentPermission(ctx context.Context, req *treasure_house_logic.BuyPresentPermissionReq) (
	*treasure_house_logic.BuyPresentPermissionResp, error) {
	resp := new(treasure_house_logic.BuyPresentPermissionResp)

	serviceInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
	if !ok {
		log.ErrorWithCtx(ctx, "BuyPresentPermission ServiceInfoFromContext fail. req:%+v", req)
		return resp, protocol.NewExactServerError(nil, status.ErrBadRequest)
	}
	uid := serviceInfo.UserID
	marketId := serviceInfo.MarketID
	os := protocol.NewTerminalType(serviceInfo.TerminalType).OS()

	sErr := s.checkUsualDevice(ctx, serviceInfo)
	if sErr != nil {
		log.Errorf("BuyChance checkUsualDevice fail. uid:%v, err:%v", uid, sErr)
		return resp, sErr
	}

	treasureResp, err := s.treasureCli.BuyPresentPermission(ctx, &pb.BuyPresentPermissionReq{
		ActivityId: req.GetActivityId(),
		PhaseType:  pb.PhaseType(req.GetPhaseType()),
		Uid:        uid,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "BuyPresentPermission BuyPresentPermission err: %v", err)
		return resp, err
	}

	resp.TreasureActivity = s.ConvActivityConfigPbToLogic(treasureResp.GetActivityConfig(), marketId, os)
	// 如果是按天数/小时发
	timeStr := ""
	if resp.TreasureActivity.GetValidTime() != 0 && resp.TreasureActivity.GetValidTime() >= 86400 {
		timeStr = fmt.Sprintf("%d天", resp.TreasureActivity.GetValidTime()/86400)
	} else if resp.TreasureActivity.GetValidTime() != 0 && resp.TreasureActivity.GetValidTime() < 86400 {
		timeStr = fmt.Sprintf("%d小时", resp.TreasureActivity.GetValidTime()/3600)
	} else {
		expireTime := time.Unix(int64(resp.GetTreasureActivity().GetExpireTime()), 0).Local()
		timeStr = fmt.Sprintf("%s后到期", expireTime.Format("2006.01.02 15:04"))
	}

	resp.GetTreasureActivity().ExtendJson = decoration.GetTreasureReceiveJson(resp.GetTreasureActivity().GetPresentInfo().GetName(),
		resp.GetTreasureActivity().GetPresentInfo().GetIconUrl(), timeStr, titleBuy,
	)

	log.InfoWithCtx(ctx, "BuyPresentPermission req:%+v resp:%+v", req, resp)

	return resp, nil
}

func (s *TreasureHouseLogic_) GetTreasureActivityUpdateInfo(ctx context.Context, req *treasure_house_logic.GetTreasureActivityUpdateInfoReq) (*treasure_house_logic.GetTreasureActivityUpdateInfoResp, error) {
	_ = req
	resp := new(treasure_house_logic.GetTreasureActivityUpdateInfoResp)

	// 入口开关
	if !s.sc.GetEntranceSwitch() {
		resp.EntranceSwitch = treasure_house_logic.TreasureEntranceSwitch_TREASURE_ENTRANCE_SWITCH_OFF
		return resp, nil
	}

	resp = s.remindCfgCache.GetRemindCfgCache()
	if resp == nil {
		log.ErrorWithCtx(ctx, "GetTreasureActivityUpdateInfo GetRemindCfgCache err resp == nil")
		resp = new(treasure_house_logic.GetTreasureActivityUpdateInfoResp)
	}
	resp.EntranceSwitch = treasure_house_logic.TreasureEntranceSwitch_TREASURE_ENTRANCE_SWITCH_ON

	return resp, nil
}

func (s *TreasureHouseLogic_) checkUsualDevice(ctx context.Context, serviceInfo *protogrpc.ServiceInfo) error {
	uid := serviceInfo.UserID
	res, sErr := s.usualDeviceCli.CheckUsualDevice(ctx, string(serviceInfo.DeviceID), uid, 1, uint32(serviceInfo.ClientType))
	if sErr != nil {
		log.Errorf("checkUsualDevice Fail to CheckUsualDevice err(%v)", sErr)
		return sErr
	}
	if !res.Result {
		err := s.usualDeviceCli.GetDeviceAuthError(ctx, uint64(uid), serviceInfo.ClientType, serviceInfo.ClientVersion)
		if err != nil {
			return err
		}
	}

	return nil
}

func convValidTime(sec uint32) string {
	var validDur string
	if sec > 86400 {
		validDur = fmt.Sprintf("%d天", sec/86400)
	} else {
		validDur = fmt.Sprintf("%d小时", sec/3600)
	}
	return validDur
}

func getPackageJump(list []*pb.PackageJumpConfig, markId uint32, os protocol.OS) string {
	for _, j := range list {
		if protocol.OS(j.GetPlatform()) == os && j.GetApp() == markId {
			return j.GetLink()
		}
		// 全部平台
		if 255 == j.GetPlatform() && j.GetApp() == markId {
			return j.GetLink()
		}
	}
	return ""
}

func NewTreasureHouseLogic_(ctx context.Context, config config.Configer, tracer opentracing.Tracer) (*TreasureHouseLogic_, error) {

	sc := &conf.ServiceConfigT{}

	cfgPath := ctx.Value("configfile").(string)
	if cfgPath == "" {
		return nil, errors.New("configfile not exist")
	}
	err := sc.Parse(cfgPath)
	if err != nil {
		return nil, err
	}

	presentCache, err := cache.NewPresentCache()
	if err != nil {
		log.ErrorWithCtx(ctx, "NewTreasureHouseLogic_ NewPresentCache err:%s", err)
		return nil, err
	}
	remindCfgCache, err := cache.NewTreasureRemindCfgLocalCache()
	if err != nil {
		log.ErrorWithCtx(ctx, "NewTreasureHouseLogic_ NewTreasureRemindCfgLocalCache err:%s", err)
		return nil, err
	}

	whiteListCache, err := cache.NewWhiteListLocalCache()
	if err != nil {
		log.ErrorWithCtx(ctx, "NewTreasureHouseLogic_ NewWhiteListLocalCache err:%s", err)
	}

	return &TreasureHouseLogic_{
		sc:             sc,
		treasureCli:    treasure_house.NewTracedClient(tracer),
		accountCli:     account.NewIClient(),
		presentCache:   presentCache,
		remindCfgCache: remindCfgCache,
		whiteListCache: whiteListCache,
		usualDeviceCli: usualDevice.NewIClient(),
	}, nil
}

func (s *TreasureHouseLogic_) ShutDown() {
}
