package mysql

import (
	"context"
	"github.com/DATA-DOG/go-sqlmock"
	"github.com/jinzhu/gorm"
	pb "golang.52tt.com/protocol/services/headwear-go"
	"regexp"
	"testing"
	"time"
)

var mysqlStore *Store
var ctx context.Context

func getMockDb(t *testing.T) (*Store, sqlmock.Sqlmock, error) {
	// 创建 sqlmock 对象
	db, mock, err := sqlmock.New()
	if err != nil {
		t.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
	}

	// 使用sqlmock对象作为gorm的数据库连接
	gormDB, err := gorm.Open("mysql", db)
	if err != nil {
		t.Fatalf("an error '%s' was not expected when opening a gorm database", err)
	}

	mysqlStore = &Store{db: gormDB}

	return &Store{db: gormDB}, mock, err
}

func TestStore_GetUserHeadwearList(t *testing.T) {
	db, mock, _ := getMockDb(t)
	rows := &sqlmock.Rows{}
	mock.ExpectQuery(regexp.QuoteMeta("SELECT uid,headwear_id,suite_id,award_time,expire_time,extra_time,use_type,custom_text FROM `user_headwear_11` WHERE (uid=? and UNIX_TIMESTAMP(expire_time)>0)")).WithArgs(111).WillReturnRows(rows)

	list, err := db.GetUserHeadwearList(ctx, 111)
	t.Log(list, err)
}

func TestStore_GetUserHeadwearById(t *testing.T) {
	db, mock, _ := getMockDb(t)
	rows := &sqlmock.Rows{}
	mock.ExpectQuery(regexp.QuoteMeta("SELECT uid,headwear_id,suite_id,award_time,expire_time,extra_time,use_type,custom_text FROM `user_headwear_27` WHERE (uid=? and headwear_id=? and custom_text = ? and UNIX_TIMESTAMP(expire_time)>0)")).WithArgs(2210427, 1, "").WillReturnRows(rows)

	t.Log(db.GetUserHeadwearById(ctx, 2210427, 1, ""))
}

func TestStore_GetUserHeadwearBySuite(t *testing.T) {
	db, mock, _ := getMockDb(t)
	rows := &sqlmock.Rows{}
	mock.ExpectQuery(regexp.QuoteMeta("SELECT uid,headwear_id,suite_id,award_time,expire_time,extra_time,use_type,custom_text FROM `user_headwear_27` WHERE (uid=? and suite_id=? and custom_text= ? and UNIX_TIMESTAMP(expire_time)>0)")).WithArgs(2210427, 106, "").WillReturnRows(rows)

	t.Log(db.GetUserHeadwearBySuite(ctx, 2210427, 106, ""))
}

func TestStore_BatchGetHeadwearConfig(t *testing.T) {
	db, mock, _ := getMockDb(t)
	rows := &sqlmock.Rows{}
	mock.ExpectQuery(regexp.QuoteMeta("SELECT id,suite_id,name,img,level,holding_time,static_img,gif_img,type,cp_img,custom_type,notes,text_color,source_type,cp_notes,cp_name,cp_id,headwear_extend_type,headwear_source_url,headwear_source_md5 FROM `headwear_config`")).WithArgs(355, 356, 357).WillReturnRows(rows)

	list, _ := db.BatchGetHeadwearConfig(ctx, []uint32{355, 356, 357})
	for _, info := range list {
		t.Log(info)
	}
}

func TestStore_GetHeadwearConfigAll(t *testing.T) {
	db, mock, _ := getMockDb(t)
	rows := &sqlmock.Rows{}
	mock.ExpectQuery(regexp.QuoteMeta("SELECT id,suite_id,name,img,level,holding_time,static_img,gif_img,type,cp_img,custom_type,notes,text_color,source_type,cp_notes,cp_name,cp_id,headwear_extend_type,headwear_source_url,headwear_source_md5 FROM `headwear_config`")).WillReturnRows(rows)

	list, _ := db.GetHeadwearConfigAll(ctx)
	for _, info := range list {
		t.Log(info)
	}
}

func TestStore_GetHeadwearConfigBySuiteLv(t *testing.T) {
	db, mock, _ := getMockDb(t)
	rows := &sqlmock.Rows{}
	mock.ExpectQuery(regexp.QuoteMeta("SELECT id,suite_id,name,img,level,holding_time,static_img,gif_img,type,cp_img,custom_type,notes,text_color,source_type,cp_notes,cp_name,cp_id,headwear_extend_type,headwear_source_url,headwear_source_md5 FROM `headwear_config`")).WithArgs(1000, 1).WillReturnRows(rows)

	info, err := db.GetHeadwearConfigBySuiteLv(ctx, 1000, 1)
	t.Logf("%+v,%v", info, err)
}

func TestStore_AddHeadwearConfig(t *testing.T) {
	db, mock, _ := getMockDb(t)
	mock.ExpectBegin()
	mock.ExpectExec(regexp.QuoteMeta("INSERT INTO `headwear_config` (`suite_id`,`type`,`level`,`valid`,`holding_time`,`name`,`img`,`static_img`,`gif_img`,`cp_img`,`custom_type`,`notes`,`text_color`,`source_type`,`cp_notes`,`cp_name`,`cp_id`,`headwear_extend_type`,`headwear_source_url`,`headwear_source_md5`) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)")).WithArgs(
		5, 0, 1, 0, 0, "测试一下", "", "", "", "", 0, "", "", 0, "", "", 0, 0, "", "").WillReturnResult(sqlmock.NewResult(0, 0))
	mock.ExpectCommit()

	t.Log(db.AddHeadwearConfig(&pb.HeadwearConfig{
		SuiteId: 5,
		Level:   1,
		Name:    "测试一下",
	}))
}

func TestStore_DelHeadwearConfig(t *testing.T) {
	db, mock, _ := getMockDb(t)
	mock.ExpectBegin()
	mock.ExpectExec(regexp.QuoteMeta("DELETE FROM `headwear_config` WHERE `headwear_config`.`id` = ? AND ((id=?))")).WithArgs(
		999211, 999211).WillReturnResult(sqlmock.NewResult(0, 0))
	mock.ExpectCommit()

	t.Log(db.DelHeadwearConfig(999211))
}

/*func TestStore_UpdateHeadwearConfig(t *testing.T) {
	t.Log(mysqlStore.UpdateHeadwearConfig(&HeadwearConfig{
		Id:      627,
		SuiteId: 1000,
		Level:   1,
		Name:    "测试一下3",
	}))
}*/

func TestStore_SetUserHeadwearInUse(t *testing.T) {
	db, mock, _ := getMockDb(t)
	//mock.ExpectBegin()
	mock.ExpectExec(regexp.QuoteMeta("insert into user_headwear_inuse_11(uid, headwear_id, cp_uid,custom_text) values(?, ?, ?,?) ON DUPLICATE KEY UPDATE headwear_id=? ,cp_uid=? , custom_text = ?")).WithArgs(
		11, 1, 0, "", 1, 0, "").WillReturnResult(sqlmock.NewResult(0, 0))
	//mock.ExpectCommit()

	t.Log(db.SetUserHeadwearInUse(nil, 11, 1, 0, ""))
}

func TestStore_GetUserHeadwearInUse(t *testing.T) {
	db, mock, _ := getMockDb(t)
	rows := &sqlmock.Rows{}
	mock.ExpectQuery(regexp.QuoteMeta("SELECT uid, headwear_id, cp_uid, custom_text FROM `user_headwear_inuse_27` WHERE (uid=?)")).WithArgs(2210427).WillReturnRows(rows)

	t.Log(db.GetUserHeadwearInUse(ctx, 2210427))
}

func TestStore_RemoveUserHeadwearInUse(t *testing.T) {
	db, mock, _ := getMockDb(t)
	mock.ExpectBegin()
	mock.ExpectExec(regexp.QuoteMeta("DELETE FROM `user_headwear_inuse_27` WHERE (uid=?)")).WithArgs(
		27).WillReturnResult(sqlmock.NewResult(0, 0))
	mock.ExpectCommit()
	t.Log(db.RemoveUserHeadwearInUse(27))
}

func TestStore_BatchGetUserHeadwearInUse(t *testing.T) {
	db, mock, _ := getMockDb(t)
	rows := &sqlmock.Rows{}
	mock.ExpectQuery(regexp.QuoteMeta("SELECT uid, headwear_id, cp_uid, custom_text FROM `user_headwear_inuse_01` WHERE (uid in (?))")).WithArgs(1).WillReturnRows(rows)

	out, err := db.BatchGetUserHeadwearInUse(ctx, []uint32{1})
	t.Log(err)
	for _, info := range out {
		t.Log(info)
	}
}

func TestStore_AddHeadwearAwardOrder(t *testing.T) {
	db, mock, _ := getMockDb(t)
	nowTs := time.Now()

	rows := &sqlmock.Rows{}

	mock.ExpectQuery(regexp.QuoteMeta("SELECT order_id FROM `headwear_order_info_11` WHERE (order_id=?)")).WithArgs(sqlmock.AnyArg()).WillReturnRows(rows)
	mock.ExpectQuery(regexp.QuoteMeta("SELECT order_id FROM `headwear_order_info_10` WHERE (order_id=?)")).WithArgs(sqlmock.AnyArg()).WillReturnRows(rows)
	mock.ExpectBegin()
	mock.ExpectExec(regexp.QuoteMeta("INSERT INTO `headwear_order_info_11` (`order_id`,`create_time`,`outside_time`,`source_type`) VALUES (?,?,?,?)")).WithArgs(
		sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg()).WillReturnResult(sqlmock.NewResult(0, 0))
	mock.ExpectCommit()

	t.Log(db.AddHeadwearAwardOrder(ctx, nil, nowTs, "test_order"))
}

func TestStore_CheckIfOrderIdExist(t *testing.T) {
	db, mock, _ := getMockDb(t)
	rows := &sqlmock.Rows{}
	mock.ExpectQuery(regexp.QuoteMeta("SELECT order_id FROM `headwear_order_info_14` WHERE (order_id=?)")).WithArgs("test_order").WillReturnRows(rows)

	t.Log(db.CheckIfOrderIdExist(ctx, 14, "test_order"))
}

func TestStore_GetLastSuiteId(t *testing.T) {
	db, mock, _ := getMockDb(t)
	rows := &sqlmock.Rows{}
	mock.ExpectQuery(regexp.QuoteMeta("SELECT suite_id FROM `headwear_config` WHERE (suite_id >= ? AND suite_id < ?) ORDER BY suite_id")).WithArgs(5000, 10000).WillReturnRows(rows)

	t.Log(db.GetLastSuiteId(ctx, 5000, 10000))
}

func TestStore_SaveHeadwearCustomText(t *testing.T) {
	db, mock, _ := getMockDb(t)
	//mock.ExpectBegin()
	mock.ExpectExec(regexp.QuoteMeta("insert into user_headwear_custom_text(uid,headwear_id,cp_uid,custom_text,suite_id,expire_time,status,show_custom_text,use_change_count , custom_text_under_review) values(?, ?, ?, ?, ? , ? , ? , ? , ? , ?)  ON DUPLICATE KEY UPDATE headwear_id = ?,expire_time = ?,status = ?,show_custom_text = ?,use_change_count= ? , custom_text_under_review = ?")).WithArgs(
		sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg()).WillReturnResult(sqlmock.NewResult(0, 0))
	rows := &sqlmock.Rows{}
	mock.ExpectQuery(regexp.QuoteMeta("SELECT id ,uid,headwear_id,cp_uid,custom_text,suite_id,award_time,expire_time,status,show_custom_text,use_change_count,custom_text_under_review FROM `user_headwear_custom_text`  WHERE (uid=? and headwear_id=? and cp_uid=? and custom_text=?) ORDER BY `user_headwear_custom_text`.`id` ASC LIMIT 1")).WithArgs(
		sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg()).WillReturnRows(rows)

	//mock.ExpectCommit()

	t.Log(db.SaveHeadwearCustomText(nil, &UserHeadwearCustomText{
		Uid:                   12345,
		HeadwearId:            111,
		SuiteId:               111,
		CpUid:                 0,
		CustomText:            "哈哈",
		ExpireTime:            time.Now(),
		ShowCustomText:        "showCustomText",
		AwardTime:             time.Now(),
		Status:                1,
		UseChangeCount:        0,
		CustomTextUnderReview: "test",
	}))
}

func TestStore_UpdateUserHeadwearCustomTextResultById(t *testing.T) {
	db, mock, _ := getMockDb(t)
	mock.ExpectBegin()
	mock.ExpectExec(regexp.QuoteMeta("UPDATE `user_headwear_custom_text` SET `show_custom_text` = custom_text_under_review, `status` = ?, `use_change_count` = use_change_count + 1 WHERE (id = ?)")).WithArgs(
		sqlmock.AnyArg(), sqlmock.AnyArg()).WillReturnResult(sqlmock.NewResult(0, 0))
	mock.ExpectCommit()
	t.Log(db.UpdateUserHeadwearCustomTextResultById(ctx, 2, true))
}
