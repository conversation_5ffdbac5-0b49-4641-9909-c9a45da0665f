package mgr

import (
	"context"
	"fmt"
	"reflect"
	"testing"
	"time"

	"bou.ke/monkey"

	"github.com/golang/mock/gomock"

	glory_reward "golang.52tt.com/protocol/services/glory-reward"
	"golang.52tt.com/services/glory-reward/cache"
	"golang.52tt.com/services/glory-reward/conf"
	"golang.52tt.com/services/glory-reward/rpc"
)

type settlementRewardMgrForTest struct {
	*settlementRewardMgr
}

func newSettlementRewardMgrForTest(t *testing.T) *settlementRewardMgrForTest {
	controller := gomock.NewController(t)
	return &settlementRewardMgrForTest{
		&settlementRewardMgr{
			transaction:     NewMockTransaction(controller),
			feishu:          NewMockFeishu(controller),
			client:          rpc.NewMockGloryClient(controller),
			dyConf:          conf.NewMockDynamicConf(controller),
			cache:           cache.NewMockGloryCache(controller),
			funcCache:       cache.NewMockFuncCache(controller),
			presentMemCache: cache.NewMockPresentMemCache(controller),
			taskMgr:         NewMockTaskMgr(controller),
			fragRewardMgr:   NewMockFragmentRewardMgr(controller),
			giftFlowStore:   NewMockGiftFlowStore(controller),
			whiteList:       NewMockWhiteListMgr(controller),
		},
	}
}

func (receiver *settlementRewardMgrForTest) getTransaction() *MockTransaction {
	return receiver.transaction.(*MockTransaction)
}

func (receiver *settlementRewardMgrForTest) getFeishu() *MockFeishu {
	return receiver.feishu.(*MockFeishu)
}

func (receiver *settlementRewardMgrForTest) getClient() *rpc.MockGloryClient {
	return receiver.client.(*rpc.MockGloryClient)
}

func (receiver *settlementRewardMgrForTest) getDyConf() *conf.MockDynamicConf {
	return receiver.dyConf.(*conf.MockDynamicConf)
}

func (receiver *settlementRewardMgrForTest) getCache() *cache.MockGloryCache {
	return receiver.cache.(*cache.MockGloryCache)
}

func (receiver *settlementRewardMgrForTest) getFuncCache() *cache.MockFuncCache {
	return receiver.funcCache.(*cache.MockFuncCache)
}

func (receiver *settlementRewardMgrForTest) getPresentMemCache() *cache.MockPresentMemCache {
	return receiver.presentMemCache.(*cache.MockPresentMemCache)
}

func (receiver *settlementRewardMgrForTest) getTaskMgr() *MockTaskMgr {
	return receiver.taskMgr.(*MockTaskMgr)
}

func (receiver *settlementRewardMgrForTest) getFragRewardMgr() *MockFragmentRewardMgr {
	return receiver.fragRewardMgr.(*MockFragmentRewardMgr)
}

func (receiver *settlementRewardMgrForTest) getGiftFlowStore() *MockGiftFlowStore {
	return receiver.giftFlowStore.(*MockGiftFlowStore)
}

func (receiver *settlementRewardMgrForTest) getWhiteList() *MockWhiteListMgr {
	return receiver.whiteList.(*MockWhiteListMgr)
}

func Test_settlementRewardMgr_PushTaskPopWindow(t *testing.T) {
	type args struct {
		ctx       context.Context
		uid       uint32
		channelId uint32
		fragType  []glory_reward.RewardType
	}
	tests := []struct {
		name     string
		args     args
		wantErr  bool
		initFunc func(s *settlementRewardMgrForTest)
	}{
		{
			name: "测试手动推送弹窗",
			args: args{
				ctx:       context.Background(),
				uid:       2521335,
				channelId: 6666666,
				fragType: []glory_reward.RewardType{
					4, 5, 6, 7, 8,
				},
			},
			wantErr: false,
			initFunc: func(s *settlementRewardMgrForTest) {
				s.getDyConf().EXPECT().IsTestMode().Return(true)
				s.getDyConf().EXPECT().GetPushConf().Return(
					map[uint32]*conf.PushWindowConf{
						1: {
							FlagTypeField:         1,
							UrlField:              "https://ga-album-cdnqn.52tt.com/glory-world/reward/icon_glory_fragment_big.png",
							Md5Field:              "d4054d730cb274c11b064efa7d9a593e",
							DisplayTimeField:      5,
							SpecialEffectUrlField: "https://ga-album-cdnqn.52tt.com/testing-yunying/5d64-18949503583.zip",
							SpecialEffectMd5Field: "dcd3bac0e78de62196298bac3d1d2ed8",
							ShineUrlField:         "https://ga-album-cdnqn.52tt.com/glory-world/reward/icon_glory_fragment_light.png",
							CircleLightUrlField:   "https://ga-album-cdnqn.52tt.com/glory-world/reward/img_aperture.png",
						},
						2: {
							FlagTypeField:         2,
							UrlField:              "https://ga-album-cdnqn.52tt.com/glory-world/reward/icon_glory_fragment_big.png",
							Md5Field:              "d4054d730cb274c11b064efa7d9a593e",
							DisplayTimeField:      5,
							SpecialEffectUrlField: "https://ga-album-cdnqn.52tt.com/testing-yunying/552d-1894951c0d9.zip",
							SpecialEffectMd5Field: "0131b857fc773851c864e9f062021b9c",
							ShineUrlField:         "https://ga-album-cdnqn.52tt.com/glory-world/reward/icon_glory_fragment_light.png",
							CircleLightUrlField:   "https://ga-album-cdnqn.52tt.com/glory-world/reward/img_aperture.png",
						},
					})
				s.getClient().EXPECT().GetUserLoginInfo(gomock.Any(), uint32(2521335)).Return(uint32(0), uint32(0), nil)
				s.getDyConf().EXPECT().GetEnterInfo(uint32(2521335), uint32(0), uint32(0), false).Return(true, "https://app.52tt.com/testing/frontend-web-assist-glory-world/index.html")
				s.getClient().EXPECT().PushTaskRewardMsg(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
				s.getClient().EXPECT().PushTaskRewardMsg(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
				s.getClient().EXPECT().PushTaskRewardMsg(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
				s.getClient().EXPECT().PushTaskRewardMsg(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
				s.getClient().EXPECT().PushTaskRewardMsg(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := newSettlementRewardMgrForTest(t)
			if tt.initFunc != nil {
				tt.initFunc(s)
			}
			if err := s.PushTaskPopWindow(tt.args.ctx, tt.args.uid, tt.args.channelId, tt.args.fragType); (err != nil) != tt.wantErr {
				t.Errorf("PushTaskPopWindow() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func Test_settlementRewardMgr_AddFragment(t *testing.T) {
	now := time.Now()
	type args struct {
		ctx      context.Context
		uid      uint32
		fragType glory_reward.GloryFragmentType
		num      uint32
	}
	tests := []struct {
		name     string
		args     args
		wantErr  bool
		initFunc func(s *settlementRewardMgrForTest)
	}{
		{
			name: "测试手动增加星钻接口",
			args: args{
				ctx:      context.Background(),
				uid:      2521335,
				fragType: glory_reward.GloryFragmentType_GloryFragmentTypeFame,
				num:      1000,
			},
			wantErr: false,
			initFunc: func(s *settlementRewardMgrForTest) {
				monkey.Patch(time.Now, func() time.Time { return now })
				s.getDyConf().EXPECT().IsTestMode().Return(true)
				s.getDyConf().EXPECT().GetPkgId().Return(map[uint32]uint32{1: 1936, 2: 1937})
				s.getDyConf().EXPECT().GetPkgBusinessInfo().Return(uint32(325), "xeeiaphmlrtnobsn")
				s.getFragRewardMgr().EXPECT().BatchAddFragReward(gomock.Any(), gomock.Any(), []*GloryWorldFragmentReward{
					{
						OrderId:            fmt.Sprintf("%+v_%+v_TEST_%+v_%+v", 325, 1936, 2521335, now.Unix()),
						Uid:                2521335,
						FragmentType:       uint8(glory_reward.GloryFragmentType_GloryFragmentTypeFame),
						FragmentNum:        1000,
						FragmentRewardType: uint16(glory_reward.FragmentRewardType_FragmentRewardTypeTest),
						RewardType:         uint16(glory_reward.RewardType_RewardTypeTest),
						RewardRemark:       "手动添加",
						IsSend:             false,
						CreateTime:         now,
						UpdateTime:         now,
					},
				}).Return(nil)
				s.getClient().EXPECT().SendFragment(gomock.Any(), uint32(2521335), uint32(1936), uint32(1000), now,
					fmt.Sprintf("%+v_%+v_TEST_%+v_%+v", 325, 1936, 2521335, now.Unix()), uint32(325), "xeeiaphmlrtnobsn").Return(nil)
				s.getFragRewardMgr().EXPECT().BatchSendFragReward(gomock.Any(), []*GloryWorldFragmentReward{
					{
						OrderId:            fmt.Sprintf("%+v_%+v_TEST_%+v_%+v", 325, 1936, 2521335, now.Unix()),
						Uid:                2521335,
						FragmentType:       uint8(glory_reward.GloryFragmentType_GloryFragmentTypeFame),
						FragmentNum:        1000,
						FragmentRewardType: uint16(glory_reward.FragmentRewardType_FragmentRewardTypeTest),
						RewardType:         uint16(glory_reward.RewardType_RewardTypeTest),
						RewardRemark:       "手动添加",
						IsSend:             false,
						CreateTime:         now,
						UpdateTime:         now,
					},
				})
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := newSettlementRewardMgrForTest(t)
			if tt.initFunc != nil {
				tt.initFunc(s)
			}
			if err := s.AddFragment(tt.args.ctx, tt.args.uid, tt.args.fragType, tt.args.num); (err != nil) != tt.wantErr {
				t.Errorf("AddFragment() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func Test_settlementRewardMgr_AutoResendPkg(t *testing.T) {
	//now := time.Now()
	//fiveMinuteAgoTime := time.Date(now.Year(), now.Month(), now.Day(), now.Hour(), now.Minute()-5, now.Second(), 0, time.Local)
	type args struct {
		ctx context.Context
	}
	tests := []struct {
		name     string
		args     args
		initFunc func(s *settlementRewardMgrForTest)
	}{}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := newSettlementRewardMgrForTest(t)
			if tt.initFunc != nil {
				tt.initFunc(s)
			}
			s.AutoResendPkg(tt.args.ctx)
		})
	}
}

func Test_settlementRewardMgr_CountPresentOrderNumAndValueByTime(t *testing.T) {
	start := time.Unix(1701941820, 0)
	end := time.Unix(1701941880, 0)
	type args struct {
		ctx       context.Context
		startTime int64
		endTime   int64
	}
	tests := []struct {
		name      string
		args      args
		wantCount uint32
		wantValue uint32
		wantErr   bool
		initFunc  func(s *settlementRewardMgrForTest)
	}{
		{
			name: "测试统计礼物订单数量和价值",
			args: args{
				ctx:       context.Background(),
				startTime: start.Unix(),
				endTime:   end.Unix(),
			},
			wantCount: 1,
			wantValue: 100,
			wantErr:   false,
			initFunc: func(s *settlementRewardMgrForTest) {
				s.getGiftFlowStore().EXPECT().GetFlowInfoWithPeriod(gomock.Any(), start.Unix(), end.Unix()).Return(uint32(1), uint32(100), nil)
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := newSettlementRewardMgrForTest(t)
			if tt.initFunc != nil {
				tt.initFunc(s)
			}
			gotCount, gotValue, err := s.CountPresentOrderNumAndValueByTime(tt.args.ctx, tt.args.startTime, tt.args.endTime)
			if (err != nil) != tt.wantErr {
				t.Errorf("CountPresentOrderNumAndValueByTime() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if gotCount != tt.wantCount {
				t.Errorf("CountPresentOrderNumAndValueByTime() gotCount = %v, want %v", gotCount, tt.wantCount)
			}
			if gotValue != tt.wantValue {
				t.Errorf("CountPresentOrderNumAndValueByTime() gotValue = %v, want %v", gotValue, tt.wantValue)
			}
		})
	}
}

func Test_settlementRewardMgr_GetPresentOrderIdsByTime(t *testing.T) {
	start := time.Unix(1701941820, 0)
	end := time.Unix(1701941880, 0)
	type args struct {
		ctx       context.Context
		startTime int64
		endTime   int64
	}
	tests := []struct {
		name         string
		args         args
		wantOrderIds []string
		wantErr      bool
		initFunc     func(s *settlementRewardMgrForTest)
	}{
		{
			name: "测试获取礼物订单id",
			args: args{
				ctx:       context.Background(),
				startTime: start.Unix(),
				endTime:   end.Unix(),
			},
			wantOrderIds: []string{"123456"},
			wantErr:      false,
			initFunc: func(s *settlementRewardMgrForTest) {
				s.getGiftFlowStore().EXPECT().GetOrderListWithPeriod(gomock.Any(), start.Unix(), end.Unix()).Return([]string{"123456"}, nil)
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := newSettlementRewardMgrForTest(t)
			if tt.initFunc != nil {
				tt.initFunc(s)
			}
			gotOrderIds, err := s.GetPresentOrderIdsByTime(tt.args.ctx, tt.args.startTime, tt.args.endTime)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetPresentOrderIdsByTime() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(gotOrderIds, tt.wantOrderIds) {
				t.Errorf("GetPresentOrderIdsByTime() gotOrderIds = %v, want %v", gotOrderIds, tt.wantOrderIds)
			}
		})
	}
}

func Test_settlementRewardMgr_IsValidToAddFragment(t *testing.T) {
	type args struct {
		ctx        context.Context
		itemId     uint32
		priceType  uint32
		itemSource uint32
		tagType    uint32
		price      uint32
	}
	tests := []struct {
		name     string
		args     args
		want     bool
		initFunc func(s *settlementRewardMgrForTest)
	}{
		{
			name: "测试礼物是否合法",
			args: args{
				ctx:        context.Background(),
				itemId:     1,
				priceType:  2,
				itemSource: 0,
				tagType:    1,
				price:      100,
			},
			want: true,
			initFunc: func(s *settlementRewardMgrForTest) {
				s.getWhiteList().EXPECT().GetPresentWhiteListMap().Return(map[uint32]bool{2: true})
				s.getDyConf().EXPECT().GiftLessPrice().Return(uint32(100))
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := newSettlementRewardMgrForTest(t)
			if tt.initFunc != nil {
				tt.initFunc(s)
			}
			if got := s.IsValidToAddFragment(tt.args.ctx, tt.args.itemId, tt.args.priceType, tt.args.itemSource, tt.args.tagType, tt.args.price); got != tt.want {
				t.Errorf("IsValidToAddFragment() = %v, want %v", got, tt.want)
			}
		})
	}
}
