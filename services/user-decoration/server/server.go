package server

import (
	"context"
	"database/sql"
	"fmt"
	"github.com/go-redis/redis"
	"github.com/golang/protobuf/proto"
	"github.com/jmoiron/sqlx"
	apicenter "golang.52tt.com/clients/apicenter/apiserver"
	imapi "golang.52tt.com/clients/im-api"
	push "golang.52tt.com/clients/push-notification/v2"
	"golang.52tt.com/clients/seqgen/v2"
	userol "golang.52tt.com/clients/user-online"
	"golang.52tt.com/pkg/bylink"
	"golang.52tt.com/pkg/coroutine"
	decoration_report "golang.52tt.com/pkg/decoration/bylink"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/protocol/grpc"
	"golang.52tt.com/protocol/app/profile"
	push2 "golang.52tt.com/protocol/app/push"
	"golang.52tt.com/protocol/common/status"
	im_api "golang.52tt.com/protocol/services/im-api"
	push_notification "golang.52tt.com/protocol/services/push-notification/v2"
	user_online "golang.52tt.com/protocol/services/user-online"
	"golang.52tt.com/services/user-decoration/event"
	"golang.52tt.com/services/user-decoration/manager"
	"strconv"
	"sync"
	"time"

	"golang.52tt.com/pkg/log"

	"golang.52tt.com/pkg/config"
	pb "golang.52tt.com/protocol/services/user-decoration"
)

// Server -
type Server struct {
	sto                  *store
	floatConfigCache     map[string]*pb.DecorationInfo
	channelInfoCardCache map[string]*pb.DecorationInfo
	cacheLock            sync.RWMutex

	imapiClient  imapi.IClient
	pushClient   push.IClient
	seqClint     seqgen.IClient
	userOlClient userol.IClient

	awardMgr *manager.AwardMgr

	kafkaSub *event.KafkaEvent
}

func (s *Server) PushAwardNotifyForTest(ctx context.Context, req *pb.PushAwardNotifyForTestReq) (*pb.PushAwardNotifyForTestResp, error) {
	out := &pb.PushAwardNotifyForTestResp{}
	uid := req.GetUid()
	seq, sErr := s.seqClint.GenerateSequence(ctx, uid, seqgen.NamespaceUser, seqgen.KeyReliablePush, 1)
	if sErr != nil {
		log.ErrorWithCtx(ctx, "Failed to GenerateSequence %d: %v", uid, sErr)
		return out, sErr
	}

	msgInfo := &profile.LiveAwardInfoCardNotify{
		NameStr: fmt.Sprintf("%s 资料卡", req.GetName()),
		TimeStr: fmt.Sprintf("使用时间为 %d 天", req.GetDay()),
		AwardInfo: &profile.LiveAwardInfoCardExtend{
			NotifyPreviewImg: req.GetPreviewImg(),
		},
		Type:    profile.Type_CHANNEL_INFO_CARD,
		Version: "v1",
		Id:      req.GetId(),
	}

	msgContent, _ := proto.Marshal(msgInfo)
	pushMsg := &push2.PushMessage{
		Cmd:     uint32(push2.PushMessage_LIVE_AWARD_CHANNEL_INFO_CARD_GAIN_PUSH),
		Content: msgContent,
		SeqId:   uint32(seq),
	}

	payload, _ := pushMsg.Marshal()

	// 推送
	err := s.pushClient.PushToUsers(ctx, []uint32{uid}, &push_notification.CompositiveNotification{
		Sequence:           uint32(seq),
		TerminalTypePolicy: push.DefaultPolicy,
		AppId:              0,
		ProxyNotification: &push_notification.ProxyNotification{
			Type:       uint32(push_notification.ProxyNotification_PUSH),
			Policy:     push_notification.ProxyNotification_RELIABLE,
			ExpireTime: 3600,
			Payload:    payload,
		},
	})

	if err != nil {
		log.ErrorWithCtx(ctx, "Failed to PushToUsers %d: %v", uid, err)
		return out, err
	}

	return out, nil
}

// New -
func New(ctx context.Context, configer config.Configer) (pb.UserDecorationServer,
	error) {

	mysqlConf := config.NewMysqlConfigWithSection(configer, "mysql").ConnectionString()
	redisConf := config.NewRedisConfigWithSection(configer, "redis")
	srv, err := newServer(ctx, mysqlConf, redisConf)
	if err != nil {
		log.ErrorWithCtx(ctx, "NewServer err: %v", err)
		return nil, err
	}

	// 百灵数据统计 初始化
	bylinkCollect, err := bylink.NewKfkCollector()
	if err != nil {
		log.ErrorWithCtx(ctx, "bylink.NewKfkCollector() failed err:%v", err)
		return nil, err
	}
	bylink.InitGlobalCollector(bylinkCollect)

	srv.imapiClient = imapi.NewIClient()
	srv.pushClient = push.NewIClient()
	srv.seqClint = seqgen.NewIClient()
	srv.userOlClient = userol.NewIClient()

	srv.awardMgr = manager.NewAwardMgr(ctx, srv.sto.mysql, srv.sto.redis, srv.pushClient, srv.seqClint)

	authKafkaConfig := config.NewKafkaConfigWithSection(configer, "kafka_auth_event")
	anchorKafkaConfig := config.NewKafkaConfigWithSection(configer, "kafka_anchor_event")
	kafkaSub, err := event.NewKafkaEvent(authKafkaConfig, anchorKafkaConfig, srv.awardMgr)
	if err != nil {
		log.ErrorWithCtx(ctx, "NewKafkaEvent err: %v", err)
		return nil, err
	}

	srv.kafkaSub = kafkaSub

	return srv, nil
}

func newServer(ctx context.Context, mysqlConf string, redisConf *config.RedisConfig) (srv *Server, err error) {
	srv = &Server{
		sto: &store{
			apiCenterClient: apicenter.NewClient(),
		},
	}

	srv.sto.mysql, err = sqlx.Connect("mysql", mysqlConf)
	if err != nil {
		log.ErrorWithCtx(ctx, "newServer Connect err: %v", err)
		return
	}

	option := redis.Options{
		Addr: redisConf.Addr(),
	}
	srv.sto.redis = redis.NewClient(&option)
	res := srv.sto.redis.Ping()
	if err = res.Err(); err != nil {
		log.ErrorWithCtx(ctx, "newServer Ping err: %v", res.Err())
		return
	}
	srv.sto.alterTable()

	srv.reloadFloatCache()
	coroutine.FixIntervalExec(srv.reloadFloatCache, time.Second*5)

	return
}

func (s *Server) reloadFloatCache() {
	floatConfigCache := make(map[string]*pb.DecorationInfo)
	ctx, cancel := context.WithTimeout(context.Background(), time.Second*4)
	defer cancel()

	floatInfo, err := s.sto.decorations(ctx, &pb.DecorationsReq{Typ: pb.Type_FLOAT}, false)
	if err != nil {
		log.ErrorWithCtx(ctx, "reloadFloatCache decorations err: %v", err)
	} else {
		for _, item := range floatInfo.GetDecorationInfos() {
			floatConfigCache[item.GetDecoration().GetId()] = item
			log.DebugWithCtx(ctx, "reloadFloatCache value %v", item)
		}
	}

	channelInfoCardCache := make(map[string]*pb.DecorationInfo)
	//缓存已经删除状态的信息，提供给单查接口用于显示兑换记录等
	channelInfoCard, err := s.sto.decorations(ctx, &pb.DecorationsReq{Typ: pb.Type_CHANNEL_INFO_CARD}, true)
	if err != nil {
		log.ErrorWithCtx(ctx, "reloadFloatCache decorations err:%v", err)
	} else {
		for _, item := range channelInfoCard.GetDecorationInfos() {
			channelInfoCardCache[item.GetDecoration().GetId()] = item
			log.DebugWithCtx(ctx, "reloadFloatCache value %v", item)
		}
	}

	s.cacheLock.Lock()
	s.floatConfigCache = floatConfigCache
	s.channelInfoCardCache = channelInfoCardCache
	s.cacheLock.Unlock()

	log.InfoWithCtx(ctx, "reloadFloatCache float len %d channelInfo len:%d", len(floatConfigCache), len(channelInfoCardCache))
}

/*func (s *Server) maintain() {
	for range time.Tick(5 * time.Second) {
		s.reloadFloatCache()
	}
}*/

// Upsert 用于为用户添加或更新一个装饰品。
func (s *Server) Upsert(ctx context.Context, in *pb.UpsertReq) (*pb.UpsertResp, error) {

	log.DebugWithCtx(ctx, "Upsert, req: %+v", in)

	out := &pb.UpsertResp{}
	serviceInfo, ok := grpc.ServiceInfoFromContext(ctx)
	log.DebugWithCtx(ctx, "service_info %v %v", serviceInfo, ok)

	customType := 0
	s.cacheLock.RLock()
	if cfg, ok := s.floatConfigCache[in.GetDec().GetId()]; ok {
		customType = int(cfg.GetDecDetail().GetType())
	}
	s.cacheLock.RUnlock()
	if customType == int(pb.DecorationCustomType_DecFusion) && (in.GetFusionTtid() == "" || in.GetDec().FusionTtid == "") {
		log.ErrorWithCtx(ctx, "Upsert err no fusion_ttid , req:%v", in)
		return out, protocol.NewServerError(status.ErrBadRequest, "融合ttid不能为空")
	} else if customType == int(pb.DecorationCustomType_DecNone) {
		in.FusionTtid = ""
		in.GetDec().FusionTtid = ""
	}

	req := &upsertReq{
		uid:   in.GetUid(),
		typ:   int(in.GetTyp()),
		begin: time.Now().Unix(),
		dur:   in.GetDur(),
		decoration: decoration{
			Typ:        int(in.GetDec().GetTyp()),
			Id:         in.GetDec().GetId(),
			Version:    in.GetDec().GetVersion(),
			FusionTtid: in.GetDec().GetFusionTtid(),
		},
		orderId:    in.GetOrderId(),
		fusionTtid: in.GetDec().GetFusionTtid(),
		cardExtend: in.GetCardExtend(),
	}
	name, end, _, err := s.sto.upsert(ctx, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "Upsert upsert req:%+v err: %v", req, err)
		return out, err
	}

	go decoration_report.ReportDressSendData(ctx, serviceInfo, &decoration_report.DressSendData{
		Uid:       in.GetUid(),
		EndTime:   time.Unix(end, 0).Local().Format("2006-01-02 15:04:05"),
		DressId:   in.GetDec().GetId(),
		DressName: name,
		DressType: decoration_report.GetReportDressType(in.GetDec().GetTyp()),
		Ttid:      in.GetFusionTtid(),
	})

	// 如果是直播主播资料卡奖励，发推送并自动佩戴
	if s.channelInfoCardCache[in.GetDec().GetId()].GetDecDetail().GetChannelInfoCardType() == pb.ChannelInfoCardType_CHANNEL_INFO_CARD_TYPE_LIVE_AWARD {
		log.DebugWithCtx(ctx, "Upsert channel info card, uid: %v, cfg: %v, dur: %v", in.GetUid(), s.floatConfigCache[in.GetDec().GetId()], in.GetDur())
		go s.SendChannelInfoCardMsg(ctx, req.uid, s.channelInfoCardCache[in.GetDec().GetId()], uint32(req.dur))
		_, err = s.Adorn(ctx, &pb.AdornReq{
			Uid: in.Uid,
			Dec: in.GetDec(),
		})

		if err != nil {
			log.ErrorWithCtx(ctx, "Upsert adorn err: %v", err)
		}
	}

	return out, nil
}

// SendChannelInfoCardMsg 发送主播资料卡奖励消息
func (s *Server) SendChannelInfoCardMsg(ctx context.Context, uid uint32, cfg *pb.DecorationInfo, dur uint32) {
	tmpCtx, cancel := context.WithTimeout(context.Background(), time.Second*5)
	defer cancel()

	log.DebugWithCtx(ctx, "SendChannelInfoCardMsg, uid: %v, cfg: %v, dur: %v", uid, cfg, dur)
	// im
	content := fmt.Sprintf("恭喜你获得%s%d天，已自动帮你佩戴成功，若需取消佩戴请前往个性装扮查看>", cfg.GetDecDetail().GetName(), dur/86400)
	highlight := "前往个性装扮查看>"
	_, err := s.imapiClient.SendTTAssistantText(tmpCtx, &im_api.SendTTAssistantTextReq{
		ToUid: uid,
		Text: &im_api.Text{
			Content:   content,
			Highlight: highlight,
			Url:       "tt://m.52tt.com/userPersonalityDress?select_tab=6",
		},
		Opt:       nil,
		Namespace: "",
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "SendIMMsgWithJumpUrl fail. uid:%d err: %s", uid, err.Error())
		return
	}

	// 先查用户在线
	ol, err := s.userOlClient.GetLatestOnlineInfo(tmpCtx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "Failed to GetLatestOnlineInfo %d: %v", uid, err)
		return
	}

	isPush := ol.GetOnlineType() == user_online.OnlineType_ONLINE_TYPE_ONLINE

	// 用户不在线, 记录到发奖的表里
	err = s.awardMgr.RecordAwardUid(tmpCtx, uid, dur/86400, cfg.GetDecDetail().GetName(), cfg.GetDecoration().GetId(), cfg.GetDecDetail().GetLiveAwardInfoCardConfig().GetNotifyPreviewImg(), isPush)
	if err != nil {
		log.ErrorWithCtx(ctx, "Failed to RecordAwardUid %d: %v", uid, err)
	}

	if !isPush {
		log.InfoWithCtx(ctx, "user %d is offline, no need to push", uid)
		return
	}

	seq, sErr := s.seqClint.GenerateSequence(tmpCtx, uid, seqgen.NamespaceUser, seqgen.KeyReliablePush, 1)
	if sErr != nil {
		log.ErrorWithCtx(ctx, "Failed to GenerateSequence %d: %v", uid, sErr)
		return
	}

	msgInfo := &profile.LiveAwardInfoCardNotify{
		NameStr: fmt.Sprintf("%s 资料卡", cfg.GetDecDetail().GetName()),
		TimeStr: fmt.Sprintf("使用时间为 %d 天", dur/86400),
		AwardInfo: &profile.LiveAwardInfoCardExtend{
			NotifyPreviewImg: cfg.GetDecDetail().GetLiveAwardInfoCardConfig().GetNotifyPreviewImg(),
		},
		Type:    profile.Type(cfg.GetDecoration().GetTyp()),
		Version: cfg.GetDecoration().GetVersion(),
		Id:      cfg.GetDecoration().GetId(),
	}

	msgContent, _ := proto.Marshal(msgInfo)
	pushMsg := &push2.PushMessage{
		Cmd:     uint32(push2.PushMessage_LIVE_AWARD_CHANNEL_INFO_CARD_GAIN_PUSH),
		Content: msgContent,
		SeqId:   uint32(seq),
	}

	payload, _ := pushMsg.Marshal()

	// 推送
	err = s.pushClient.PushToUsers(tmpCtx, []uint32{uid}, &push_notification.CompositiveNotification{
		Sequence:           uint32(seq),
		TerminalTypePolicy: push.DefaultPolicy,
		AppId:              0,
		ProxyNotification: &push_notification.ProxyNotification{
			Type:       uint32(push_notification.ProxyNotification_PUSH),
			Policy:     push_notification.ProxyNotification_RELIABLE,
			ExpireTime: 3600,
			Payload:    payload,
		},
	})

	if err != nil {
		log.ErrorWithCtx(ctx, "Failed to PushToUsers %d: %v", uid, err)
		return
	}

	log.InfoWithCtx(ctx, "SendChannelInfoCardMsg success, uid: %v, cfg: %v, dur: %v", uid, cfg, dur)
	return
}

// UserDecorations 用于返回用户所拥有的某类装饰品。
func (s *Server) UserDecorations(ctx context.Context, in *pb.UserDecorationsReq) (*pb.UserDecorationsResp, error) {

	log.DebugWithCtx(ctx, "UserDecorations, uid: %v, type: %v", in.GetUid(), in.GetTyp())

	out := &pb.UserDecorationsResp{}

	req := &userDecorationsReq{
		uid: in.GetUid(),
		typ: int(in.GetTyp()),
	}
	resp, err := s.sto.userDecorations(ctx, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "UserDecorations userDecorations err: %v", err)
		return out, err
	}

	out.DecInfos = append(out.DecInfos, resp.decs...)

	return out, nil
}

// Current 用于获取用户当前佩戴的装饰品。
func (s *Server) Current(ctx context.Context, in *pb.CurrentReq) (*pb.CurrentResp, error) {

	log.DebugWithCtx(ctx, "Current, uid: %v, type: %v", in.GetUid(), in.GetTyp())

	out := &pb.CurrentResp{
		Dec:  &pb.Decoration{},
		Time: &pb.Tim{},
	}

	req := &currentReq{
		uid: in.GetUid(),
		typ: int(in.GetTyp()),
	}

	resp, err := s.sto.current(ctx, req)
	if err != nil {
		if err == redis.Nil {
			return out, nil
		}
		log.ErrorWithCtx(ctx, "Current current err: %v", err)
		return out, err
	}

	out.Dec.Typ = resp.Dec.GetTyp()
	out.Dec.Id = resp.Dec.GetId()
	out.Dec.Version = resp.Dec.GetVersion()
	out.Dec.FusionTtid = resp.Dec.GetFusionTtid()
	out.Time.End = resp.Time.End
	out.Dec.LiveAwardInfoCardExtend = resp.Dec.GetLiveAwardInfoCardExtend()
	return out, nil
}

// Adorn 用于用户佩戴装饰品。
func (s *Server) Adorn(ctx context.Context, in *pb.AdornReq) (*pb.AdornResp, error) {

	log.DebugWithCtx(ctx, "Adorn, uid: %v", in.GetUid())

	out := &pb.AdornResp{}
	serviceInfo, ok := grpc.ServiceInfoFromContext(ctx)
	log.DebugWithCtx(ctx, "service_info %v %v", serviceInfo, ok)

	req := &adornReq{
		uid: in.GetUid(),
		decoration: decoration{
			Typ:        int(in.GetDec().GetTyp()),
			Id:         in.GetDec().GetId(),
			Version:    in.GetDec().GetVersion(),
			FusionTtid: in.GetDec().GetFusionTtid(),
		},
	}
	decName, end, _, err := s.sto.adorn(ctx, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "Adorn adorn err: %v", err)
		return out, err
	}

	go decoration_report.ReportDressUseData(ctx, serviceInfo, &decoration_report.DressUseData{
		Uid:       in.GetUid(),
		EndTime:   time.Unix(end, 0).Local().Format("2006-01-02 15:04:05"),
		DressId:   in.GetDec().GetId(),
		DressName: decName,
		DressType: decoration_report.GetReportDressType(in.GetDec().GetTyp()),
		Ttid:      in.GetDec().GetFusionTtid(),
		Status:    decoration_report.StatusUse,
	})

	return out, nil
}

// Remove 用于用户卸下装饰品
func (s *Server) Remove(ctx context.Context, in *pb.RemoveReq) (*pb.RemoveResp, error) {

	log.DebugWithCtx(ctx, "Remove, uid: %v, type: %v", in.GetUid(), in.GetTyp())

	out := &pb.RemoveResp{}
	serviceInfo, _ := grpc.ServiceInfoFromContext(ctx)

	req := &removeReq{
		uid: in.GetUid(),
		typ: int(in.GetTyp()),
	}
	_, err := s.sto.remove(ctx, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "Remove remove err: %v", err)
		return out, err
	}

	go decoration_report.ReportDressUseData(ctx, serviceInfo, &decoration_report.DressUseData{
		Uid:       in.GetUid(),
		DressId:   strconv.Itoa(0),
		DressName: "",
		DressType: decoration_report.GetReportDressType(in.GetTyp()),
		Ttid:      "",
		Status:    decoration_report.StatusClear,
	})
	//
	return out, nil
}

// Decorations 返回某类装饰品
func (s *Server) Decorations(ctx context.Context, in *pb.DecorationsReq) (*pb.DecorationsResp, error) {
	if len(in.GetId()) > 0 && in.GetTyp() == pb.Type_CHANNEL_INFO_CARD { //给商场点个查询用
		return s.getChannelInfoCardFromCache(in.GetId())
	}
	return s.sto.decorations(ctx, in, in.GetAll())
}

// GetDecorationById 用于返回某个装饰。
func (s *Server) GetDecorationById(ctx context.Context, in *pb.GetDecorationByIdReq) (*pb.GetDecorationByIdResp, error) {

	log.DebugWithCtx(ctx, "GetDecorationById, id: %v, type: %v", in.GetId(), in.GetTyp())

	out := &pb.GetDecorationByIdResp{}

	resp, err := s.sto.getDecorationById(ctx, in.GetId(), uint32(in.GetTyp()), in.GetVersion())
	if err == sql.ErrNoRows {
		log.ErrorWithCtx(ctx, "GetDecorationById getDecorationById err: %v", err)
		return out, protocol.NewServerError(status.ErrUserDecorationNotExist, "该装扮不存在")
	} else if err != nil {
		log.ErrorWithCtx(ctx, "GetDecorationById getDecorationById err: %v", err)
		return out, err
	}

	out.DecInfo = resp

	return out, nil
}

func (s *Server) getChannelInfoCardFromCache(id string) (*pb.DecorationsResp, error) {
	s.cacheLock.RLock()
	defer s.cacheLock.RUnlock()
	resp := &pb.DecorationsResp{DecorationInfos: []*pb.DecorationInfo{}}
	if info, ok := s.channelInfoCardCache[id]; ok {
		resp.DecorationInfos = append(resp.DecorationInfos, info)
		return resp, nil
	}
	return resp, protocol.NewServerError(status.ErrChannelPslConfigNotExist, "装饰配置不存在")
}

// InsertDecoration 插入装饰品。
func (s *Server) InsertDecoration(ctx context.Context, in *pb.InsertDecorationReq) (*pb.InsertDecorationResp, error) {
	return s.sto.insertDecoration(ctx, in)
}

// DelDecoration 删除装饰品。
func (s *Server) DelDecoration(ctx context.Context, in *pb.DelDecorationReq) (*pb.DelDecorationResp, error) {
	log.DebugWithCtx(ctx, "DelDecoration in : %+v", in)
	return s.sto.DelDecoration(ctx, in)
}

func (s *Server) BatchUpsertDecoration(ctx context.Context, in *pb.BatchUpsertDecorationReq) (*pb.BatchUpsertDecorationResp, error) {
	out := &pb.BatchUpsertDecorationResp{}
	serviceInfo, _ := grpc.ServiceInfoFromContext(ctx)

	for _, item := range in.GetUser() {
		customType := 0
		s.cacheLock.RLock()
		if cfg, ok := s.floatConfigCache[in.GetDec().GetId()]; ok {
			customType = int(cfg.GetDecDetail().GetType())
		}
		s.cacheLock.RUnlock()
		if customType == int(pb.DecorationCustomType_DecFusion) && item.GetFusionTtid() == "" {
			log.ErrorWithCtx(ctx, "Upsert err no fusion_ttid , req:%v", in)
			return out, protocol.NewServerError(status.ErrBadRequest, "融合ttid不能为空")
		} else if customType == int(pb.DecorationCustomType_DecNone) {
			item.FusionTtid = ""
		}

		tmpReq := &upsertReq{
			uid:   item.GetUid(),
			typ:   int(in.GetTyp()),
			begin: time.Now().Unix(),
			dur:   in.GetDur(),
			decoration: decoration{
				Typ:        int(in.GetDec().GetTyp()),
				Id:         in.GetDec().GetId(),
				Version:    in.GetDec().GetVersion(),
				FusionTtid: item.GetFusionTtid(),
			},
			orderId:    genOrderId(item.GetUid(), in.GetDec().GetId()),
			fusionTtid: item.GetFusionTtid(),
		}
		name, end, _, err := s.sto.upsert(ctx, tmpReq)
		if err != nil {
			log.ErrorWithCtx(ctx, "Upsert upsert err: %v", err)
			return out, err
		}

		go decoration_report.ReportDressSendData(ctx, serviceInfo, &decoration_report.DressSendData{
			Uid:       item.GetUid(),
			EndTime:   time.Unix(end, 0).Local().Format("2006-01-02 15:04:05"),
			DressId:   in.GetDec().GetId(),
			DressName: name,
			DressType: decoration_report.GetReportDressType(in.GetDec().GetTyp()),
			Ttid:      item.GetFusionTtid(),
		})

	}

	return out, nil

}

func genOrderId(uid uint32, id string) string {
	return fmt.Sprintf("headwear_%d_%s_%d", uid, id, time.Now().UnixNano()/1000000)
}
