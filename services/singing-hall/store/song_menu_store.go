package store

import (
	"fmt"
	"math"
	"strconv"
	"strings"
	"time"

	"github.com/jmoiron/sqlx"
	"golang.52tt.com/pkg/log"
)

type SongMenu struct {
	ID         uint32 `db:"id"`
	Name       string `db:"name"`
	Index      uint32 `db:"index"`
	Uid        uint32 `db:"uid"`
	SongNum    uint32
	CreateTime time.Time `db:"create_time"`
	UpdateTime time.Time `db:"update_time"`
}

const defaultPageSize = 10
const GetSongMenuSQL = "SELECT * FROM song_menu WHERE `uid` = ? AND `index` < ? ORDER BY `index` DESC LIMIT ?"
const CountSongMenuSQL = "SELECT COUNT(1) FROM `song_menu` WHERE `uid` = ?"

func (s *Store) GetSongMenu(uid, offset, pageSize uint32) ([]*SongMenu, uint32, error) {
	songMenu := make([]*SongMenu, 0, 8)
	if offset == 0 {
		offset = math.MaxInt32
	}
	if pageSize == 0 {
		pageSize = defaultPageSize
	}
	err := s.mysqlCli.Select(&songMenu, GetSongMenuSQL, uid, offset, pageSize)
	if err != nil {
		return songMenu, 0, err
	}

	menuIds := make([]uint32, len(songMenu))
	for i, item := range songMenu {
		menuIds[i] = item.ID
	}
	songNumMap := s.CountSongNumBatch(menuIds)
	for _, item := range songMenu {
		item.SongNum = songNumMap[item.ID]
	}

	total := uint32(0)
	row, err := s.mysqlCli.Query(CountSongMenuSQL, uid)
	if err != nil {
		return songMenu, total, err
	}
	defer row.Close()
	if row.Err() != nil {
		return songMenu, total, row.Err()
	}
	if row.Next() {
		row.Scan(&total)
	}

	return songMenu, total, nil
}

const InsertSongMenuSQL = "INSERT INTO song_menu(`name`, `index`, `uid`, `create_time`) VALUES(?, ?, ?, ?)"

func (s *Store) CreateSongMenu(songMenu *SongMenu) (uint32, error) {
	now := time.Now()
	rs, err := s.mysqlCli.Exec(InsertSongMenuSQL, songMenu.Name, now.Unix(), songMenu.Uid, now)
	lId, _ := rs.LastInsertId()
	return uint32(lId), err
}

const UpdateSongMenuSQL = "UPDATE song_menu SET `name` = ?, `update_time` = ? WHERE `id` = ?"

func (s *Store) UpdateSongMenu(songMenu *SongMenu) error {
	_, err := s.mysqlCli.Exec(UpdateSongMenuSQL, songMenu.Name, time.Now(), songMenu.ID)
	return err
}

const BatchDelSongMenuSQL = "DELETE FROM song_menu WHERE `uid` = %d AND `id` IN (%s)"

func (s *Store) BatchDelSongMenu(uid uint32, ids []uint32) error {
	idsStrArr := make([]string, len(ids))
	for i, id := range ids {
		idsStrArr[i] = strconv.FormatInt(int64(id), 10)
	}
	idsStr := strings.Join(idsStrArr, ",")
	sql := fmt.Sprintf(BatchDelSongMenuSQL, uid, idsStr)
	_, err := s.mysqlCli.Exec(sql)
	return err
}

const SortSongMenuSQL = "INSERT INTO song_menu(`id`, `name`, `index`, `uid`, `create_time`, `update_time`) values%s"

func (s *Store) EditSongMenu(uid uint32, ids []uint32) error {
	tx, err := s.mysqlCli.Beginx()
	if err != nil {
		return err
	}
	songMenuMap := s.getSongMenuMapByUid(tx, uid)

	if err := s.delSongMenuByUid(tx, uid); err != nil {
		return err
	}

	idLen := len(ids)
	val := make([]string, 0, idLen)
	params := make([]interface{}, 0, 8)
	for i, id := range ids {
		if songMenu := songMenuMap[id]; songMenu != nil {
			val = append(val, "(?, ?, ?, ?, ?, ?)")
			params = append(params, songMenu.ID, songMenu.Name, idLen-i, songMenu.Uid, songMenu.CreateTime, time.Now())
		}
	}

	if len(val) == 0 {
		tx.Commit()
		return nil // 删除全部
	}

	sql := fmt.Sprintf(SortSongMenuSQL, strings.Join(val, ","))
	_, err = tx.Exec(sql, params...)
	if err != nil {
		tx.Rollback()
		return err
	}

	tx.Commit()

	return err
}

const GetSongMenuOwnerUidSQL = "SELECT FROM song_menu `uid` WHERE `id` = ?"

func (s *Store) GetSongMenuOwnerUid(menuId uint32) uint32 {
	rows, err := s.mysqlCli.Query(GetSongMenuOwnerUidSQL, menuId)
	if err != nil {
		log.Errorf("GetSongMenuOwnerUid, error: %v, menuId: %d", err, menuId)
		return 0
	}
	defer rows.Close()
	if rows.Err() != nil {
		log.Errorf("GetSongMenuOwnerUid, error: %v, menuId: %d", rows.Err(), menuId)
		return 0
	}
	songMenu := &SongMenu{}
	if rows.Next() {
		rows.Scan(songMenu)
	}

	return songMenu.Uid
}

const GetSongMenuByUidSQL = "SELECT * FROM `song_menu` WHERE `uid` = ?"

func (s *Store) getSongMenuMapByUid(tx *sqlx.Tx, uid uint32) map[uint32]*SongMenu {

	songMenuMap := make(map[uint32]*SongMenu)
	songMenuList := make([]*SongMenu, 0, 8)
	err := tx.Select(&songMenuList, GetSongMenuByUidSQL, uid)
	if err != nil {
		log.Errorf("getSongMenuMapByUid, error: %v, uid: %d", err, uid)
	}

	for _, item := range songMenuList {
		songMenuMap[item.ID] = item
	}
	return songMenuMap
}

func (s *Store) delSongMenuByUid(tx *sqlx.Tx, uid uint32) error {
	_, err := tx.Exec("DELETE FROM `song_menu` WHERE `uid` = ?", uid)
	return err
}
