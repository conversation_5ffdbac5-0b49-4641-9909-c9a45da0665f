package report

import (
	"encoding/json"
	"errors"
	"fmt"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/services/reconcile-v2/internal/config"
	"io/ioutil"
	"net/http"
	"strings"
	"time"
)

// FeishuReporterV2 .
type FeishuReporterV2 struct {
	url string
	env string
	push bool
	alarmConfig *config.AlarmPlatform
}

// FeiShuMsgV2 see https://getfeishu.cn/hc/zh-cn/articles/360024984973-%E5%9C%A8%E7%BE%A4%E8%81%8A%E4%B8%AD%E4%BD%BF%E7%94%A8%E6%9C%BA%E5%99%A8%E4%BA%BA
type FeiShuMsgV2 struct {
	MsgType string      `json:"msg_type"`
	Content interface{} `json:"content"`
}

// ContextText 文本消息
type ContextText struct {
	Text string `json:"text"`
}

// NewFeiShuReporterV2 .
func NewFeiShuReporterV2(url string, env string, push bool, alarmConfig *config.AlarmPlatform) *FeishuReporterV2 {
	return &FeishuReporterV2{
		url: url,
		env: env,
		push: push,
		alarmConfig: alarmConfig,
	}
}

func (s *FeishuReporterV2) SendInfo(text string) error {
	return s.send(fmt.Sprintf("<%s> [Info] %s", s.env, text))
}

func (s *FeishuReporterV2) SendWarning(text string) error {
	return s.send(fmt.Sprintf("<%s> [Warning] %s", s.env, text))
}

func (s *FeishuReporterV2) SendError(text string) error {
	return s.send(fmt.Sprintf("<%s><at user_id=\"all\"></at> [Error] %s", s.env, text))
}

// Send .
func (s *FeishuReporterV2) send(text string) (err error) {
	if !s.push {
		return nil
	}

	log.Infof("send feishu: %s", text)

	msg := &FeiShuMsgV2{
		MsgType: "text",
		Content: ContextText{Text: text},
	}
	info := make(map[string]interface{})
	data, _ := json.Marshal(msg)
	err = post(s.url, string(data), &info)
	if err != nil {
		log.Errorf("send feishu err=%v", err)
		return
	}

	log.Infof("send feishu ret=%v", info)

	_, exist := info["StatusCode"]
	if exist {
		return
	}

	errMsg := info["msg"]
	if nil != errMsg {
		err = errors.New(errMsg.(string))
	}
	return
}

func post(url string, data string, result interface{}) (err error) {
	for i := 0; i < 3; i++ {
		var resp *http.Response
		resp, err = http.Post(url, "application/json; charset=UTF-8", strings.NewReader(data))
		if err != nil {
			log.Errorf("Failed to send Feishu Report err(%+v)", err)
			time.Sleep(time.Second)
			continue
		}

		var body []byte
		body, err = ioutil.ReadAll(resp.Body)
		if err != nil {
			_ = resp.Body.Close()
			return
		}
		_ = resp.Body.Close()

		if 0 == len(body) {
			err = errors.New("response not correct")
			return
		}
		err = json.Unmarshal(body, result)
		return
	}
	return
}