package manager

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"os"
	"sort"
	"strconv"
	"strings"
	"time"

	"github.com/shopspring/decimal"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/settlement"
	"golang.52tt.com/pkg/settlement/export"
	"golang.52tt.com/pkg/settlement/timeconv"
	pb "golang.52tt.com/protocol/services/gold-commission"
	guildCooperation "golang.52tt.com/protocol/services/guild-cooperation"
	Guild "golang.52tt.com/protocol/services/guildsvr"
	"golang.52tt.com/services/gold-commission/client"
	"golang.52tt.com/services/gold-commission/conf"
	"golang.52tt.com/services/gold-commission/mysql"
	"golang.52tt.com/services/gold-commission/utils"
	"golang.52tt.com/services/gold/common"
	"golang.52tt.com/services/gold/common/model"
)

// GetGuildsYuyinAnchorStat 会长后台-收益管理-语音佣金
func (m *Manager) GetGuildsYuyinAnchorStat(ctx context.Context, req *pb.GetGuildsYuyinAnchorStatReq) (*pb.GetGuildsYuyinAnchorStatResp, error) {
	log.InfoWithCtx(ctx, "GetGuildsYuyinAnchorStat in:%+v", req)
	resp := &pb.GetGuildsYuyinAnchorStatResp{}
	nowTime := time.Now()
	var beginTime, endTime time.Time
	if req.BeginTime > 0 && req.EndTime > 0 {
		beginTime, endTime = time.Unix(int64(req.BeginTime), 0), time.Unix(int64(req.EndTime), 0)
	} else if req.Unit == pb.TimeFilterUnit_BY_DAY {
		beginTime = time.Date(nowTime.Year(), nowTime.Month(), nowTime.Day(), 0, 0, 0, 0, time.Local)
		endTime = nowTime
	} else if req.Unit == pb.TimeFilterUnit_BY_MONTH {
		beginTime = time.Date(nowTime.Year(), nowTime.Month(), 1, 0, 0, 0, 0, time.Local)
		endTime = nowTime
	} else {
		err := errors.New("type err")
		log.ErrorWithCtx(ctx, "GetGuildsYuyinAnchorStat args.type error. in %v err %v", req, err)
		return resp, err
	}

	records, err := m.mysqlStore.GetYuyinDB().GetGuildsGoldStat(ctx, req.Unit, req.GetGuildIds(), beginTime, endTime, req.Offset, req.Limit)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetGuildsYuyinAnchorStat GetGuildsAnchorStat err:%v", err)
		return resp, err
	}

	// 收集主播信息
	_, anchorList, err := m.changeGoldStatRecordToAnchorList(ctx, records, pb.GoldType_YUYIN_GOLD)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetGuildsYuyinAnchorStat changeGoldStatRecordToAnchorList err:%v", err)
		return resp, err
	}

	// todo 临时分页
	start := req.Offset
	end := start + req.Limit
	cnt := uint32(len(anchorList))
	if start > cnt {
		anchorList = make([]*pb.BaseAnchorInfo, 0)
	} else if end > cnt {
		anchorList = anchorList[start:cnt]
	} else {
		anchorList = anchorList[start:end]
	}

	resp.AnchorList = anchorList
	log.DebugWithCtx(ctx, "GetGuildsYuyinAnchorStat resp in:%+v,out:%+v", req, resp)
	return resp, nil
}

// GetGuildsAmuseChannelStat 会长后台-收益管理-娱乐房佣金
func (m *Manager) GetGuildsAmuseChannelStat(ctx context.Context, req *pb.GetGuildsAmuseChannelStatReq) (*pb.GetGuildsAmuseChannelStatResp, error) {
	log.InfoWithCtx(ctx, "GetGuildsAmuseChannelStat in:%+v", req)
	resp := &pb.GetGuildsAmuseChannelStatResp{}
	nowTime := time.Now()
	var beginTime, endTime time.Time
	if req.BeginTime > 0 && req.EndTime > 0 {
		beginTime, endTime = time.Unix(int64(req.BeginTime), 0), time.Unix(int64(req.EndTime), 0)
	} else if req.Unit == pb.TimeFilterUnit_BY_DAY {
		beginTime = time.Date(nowTime.Year(), nowTime.Month(), nowTime.Day(), 0, 0, 0, 0, time.Local)
		endTime = nowTime
	} else if req.Unit == pb.TimeFilterUnit_BY_MONTH {
		beginTime = time.Date(nowTime.Year(), nowTime.Month(), 1, 0, 0, 0, 0, time.Local)
		endTime = nowTime
	} else {
		err := errors.New("type err")
		log.ErrorWithCtx(ctx, "GetGuildsAmuseChannelStat args.type error. in %v err %v", req, err)
		return resp, err
	}

	records, err := m.mysqlStore.GetAmuseDB().GetGuildsGoldStat(ctx, req.Unit, req.GetGuildIds(), beginTime, endTime, req.Offset, req.Limit)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetGuildsAmuseChannelStat GetGuildsAnchorStat err:%v", err)
		return resp, err
	}

	// 收集主播信息
	_, channelList, err := m.changeGoldStatRecordToChannelList(ctx, records)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetGuildsAmuseChannelStat changeGoldStatRecordToChannelList err:%v", err)
		return resp, err
	}

	// todo 临时分页
	start := req.Offset
	end := start + req.Limit
	cnt := uint32(len(channelList))
	if start > cnt {
		channelList = make([]*pb.BaseChannelInfo, 0)
	} else if end > cnt {
		channelList = channelList[start:cnt]
	} else {
		channelList = channelList[start:end]
	}

	resp.ChannelList = channelList
	log.DebugWithCtx(ctx, "GetGuildsAmuseChannelStat resp in:%+v,out:%+v", req, resp)
	return resp, nil
}

// GetGuildsInteractGameAnchorStat 会长后台-收益管理-互动游戏
func (m *Manager) GetGuildsInteractGameAnchorStat(ctx context.Context, req *pb.GetGuildsInteractGameAnchorStatReq) (
	*pb.GetGuildsInteractGameAnchorStatResp, error) {
	log.InfoWithCtx(ctx, "GetGuildsInteractGameAnchorStat in:%+v", req)
	resp := &pb.GetGuildsInteractGameAnchorStatResp{}
	nowTime := time.Now()
	var beginTime, endTime time.Time
	if req.BeginTime > 0 && req.EndTime > 0 {
		beginTime, endTime = time.Unix(int64(req.BeginTime), 0), time.Unix(int64(req.EndTime), 0)
	} else if req.Unit == pb.TimeFilterUnit_BY_DAY {
		beginTime = time.Date(nowTime.Year(), nowTime.Month(), nowTime.Day(), 0, 0, 0, 0, time.Local)
		endTime = nowTime
	} else if req.Unit == pb.TimeFilterUnit_BY_MONTH {
		beginTime = time.Date(nowTime.Year(), nowTime.Month(), 1, 0, 0, 0, 0, time.Local)
		endTime = nowTime
	} else {
		err := errors.New("type err")
		log.ErrorWithCtx(ctx, "GetGuildsInteractGameAnchorStat args.type error. in %v err %v", req, err)
		return resp, err
	}

	records, err := m.mysqlStore.GetGameDB().GetGuildsGoldStat(ctx, req.Unit, req.GetGuildIds(), beginTime, endTime, req.Offset, req.Limit)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetGuildsInteractGameAnchorStat GetGuildsAnchorStat err:%v", err)
		return resp, err
	}

	// 收集主播信息
	_, anchorList, err := m.changeGoldStatRecordToAnchorList(ctx, records, pb.GoldType_INTERACT_GAME_GOLD)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetGuildsInteractGameAnchorStat changeGoldStatRecordToAnchorList err:%v", err)
		return resp, err
	}

	// todo 临时分页
	start := req.Offset
	end := start + req.Limit
	cnt := uint32(len(anchorList))
	if start > cnt {
		anchorList = make([]*pb.BaseAnchorInfo, 0)
	} else if end > cnt {
		anchorList = anchorList[start:cnt]
	} else {
		anchorList = anchorList[start:end]
	}

	resp.AnchorList = anchorList
	log.DebugWithCtx(ctx, "GetGuildsInteractGameAnchorStat resp in:%+v,out:%+v", req, resp)
	return resp, nil
}

// GetGuildsESportCoachStat 会长后台-收益管理-电竞佣金
func (m *Manager) GetGuildsESportCoachStat(ctx context.Context, req *pb.GetGuildsESportCoachStatReq) (*pb.GetGuildsESportCoachStatResp, error) {
	log.InfoWithCtx(ctx, "GetGuildsESportCoachStat in:%+v", req)
	resp := &pb.GetGuildsESportCoachStatResp{}
	nowTime := time.Now()
	var beginTime, endTime time.Time
	if req.BeginTime > 0 && req.EndTime > 0 {
		beginTime, endTime = time.Unix(int64(req.BeginTime), 0), time.Unix(int64(req.EndTime), 0)
	} else if req.Unit == pb.TimeFilterUnit_BY_DAY {
		beginTime = time.Date(nowTime.Year(), nowTime.Month(), nowTime.Day(), 0, 0, 0, 0, time.Local)
		endTime = nowTime
	} else if req.Unit == pb.TimeFilterUnit_BY_MONTH {
		beginTime = time.Date(nowTime.Year(), nowTime.Month(), 1, 0, 0, 0, 0, time.Local)
		endTime = nowTime
	} else {
		err := errors.New("type err")
		log.ErrorWithCtx(ctx, "GetGuildsESportCoachStat args.type error. in %v err %v", req, err)
		return resp, err
	}

	records, err := m.mysqlStore.GetESportDB().GetGuildsGoldStat(ctx, req.Unit, req.GetGuildIds(), beginTime, endTime, req.Offset, req.Limit)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetGuildsESportCoachStat GetGuildsAnchorStat err:%v", err)
		return resp, err
	}

	// 收集主播信息
	_, anchorList, err := m.changeGoldStatRecordToAnchorList(ctx, records, pb.GoldType_ESPORT_GOLD)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetGuildsESportCoachStat changeGoldStatRecordToAnchorList err:%v", err)
		return resp, err
	}

	// todo 临时分页
	start := req.Offset
	end := start + req.Limit
	cnt := uint32(len(anchorList))
	if start > cnt {
		anchorList = make([]*pb.BaseAnchorInfo, 0)
	} else if end > cnt {
		anchorList = anchorList[start:cnt]
	} else {
		anchorList = anchorList[start:end]
	}

	resp.CoachList = anchorList
	log.DebugWithCtx(ctx, "GetGuildsESportCoachStat resp in:%+v,out:%+v", req, resp)
	return resp, nil
}

func (m *Manager) changeGoldStatRecordToAnchorList(ctx context.Context, records []*mysql.GoldStat, goldType pb.GoldType) (
	anchorIdList []uint32, anchorList []*pb.BaseAnchorInfo, err error) {
	// 收集主播信息
	anchorMap := make(map[string]*pb.BaseAnchorInfo, 0)
	for _, r := range records {
		ut := uint32(r.Date.Unix())
		key := fmt.Sprintf("%d_%d_%d", r.GuildId, ut, r.AnchorId)
		if anchorMap[key] == nil {
			anchorMap[key] = &pb.BaseAnchorInfo{}
		}
		anchorMap[key].AnchorId = r.AnchorId
		anchorMap[key].GuildId = r.GuildId
		anchorMap[key].Fee = anchorMap[key].Fee + r.Fee
		anchorMap[key].Income = common.ConvIncome(anchorMap[key].Fee, goldType)
		anchorMap[key].Date = uint32(r.Date.Unix())
		switch r.Source {
		case uint32(pb.SourceType_SourceTypePresent):
			anchorMap[key].PresentFee = r.Fee
		case uint32(pb.SourceType_SourceTypeKnight):
			anchorMap[key].KnightFee = r.Fee
		case uint32(pb.SourceType_SourceTypeInteractGame):
			anchorMap[key].InteractGameFee = r.Fee
		case uint32(pb.SourceType_SourceTypeESport):
			anchorMap[key].EsportFee = r.Fee
		default:
			log.ErrorWithCtx(ctx, "changeGoldStatRecordToAnchorList has wrong source record, records:%+v", r)
			return nil, nil, fmt.Errorf("GetGuildsYuyinAnchorStat has wrong source record, records:%+v", r)
		}
	}

	// 组装返回体
	anchorIdList = make([]uint32, 0)
	anchorList = make([]*pb.BaseAnchorInfo, 0)
	for _, r := range anchorMap {
		record := r
		anchorIdList = append(anchorIdList, record.AnchorId)
		anchorList = append(anchorList, record)
	}

	sort.Slice(anchorList, func(i, j int) bool {
		if anchorList[i].Date != anchorList[j].Date {
			return anchorList[i].Date > anchorList[j].Date
		}
		if anchorList[i].Fee != anchorList[j].Fee {
			return anchorList[i].Fee > anchorList[j].Fee
		}
		return anchorList[i].AnchorId > anchorList[j].AnchorId
	})

	return anchorIdList, anchorList, nil
}
func (m *Manager) changeGoldStatRecordToChannelList(ctx context.Context, records []*mysql.GoldStat) (
	channelIdList []uint32, channelList []*pb.BaseChannelInfo, err error) {
	channelMap := make(map[string]*pb.BaseChannelInfo, 0)
	for _, r := range records {
		ut := uint32(r.Date.Unix())
		key := fmt.Sprintf("%d_%d_%d", r.GuildId, ut, r.ChannelId)
		if channelMap[key] == nil {
			channelMap[key] = &pb.BaseChannelInfo{}
		}
		channelMap[key].ChannelId = r.ChannelId
		channelMap[key].GuildId = r.GuildId
		channelMap[key].Fee = channelMap[key].Fee + r.Fee
		channelMap[key].Income = common.AmuseConvIncome(channelMap[key].Fee)
		channelMap[key].Date = uint32(r.Date.Unix())
		switch r.Source {
		case uint32(pb.SourceType_SourceTypePresent):
			channelMap[key].PresentFee = r.Fee
		case uint32(pb.SourceType_SourceTypeKnight):
			channelMap[key].KnightFee = r.Fee
		case uint32(pb.SourceType_SourceTypeWerewolf):
			channelMap[key].WerewolfFee = r.Fee
		case uint32(pb.SourceType_SourceTypeInteractGame):
			channelMap[key].InteractGameFee = r.Fee
		default:
			log.ErrorWithCtx(ctx, "changeGoldStatRecordToChannelList has wrong source record, records:%+v", r)
			return nil, nil, fmt.Errorf("changeGoldStatRecordToChannelList has wrong source record, records:%+v", r)
		}
	}

	// 组装返回体
	channelIdList = make([]uint32, 0)
	channelList = make([]*pb.BaseChannelInfo, 0)
	for _, r := range channelMap {
		record := r
		channelIdList = append(channelIdList, record.ChannelId)
		channelList = append(channelList, record)
	}

	sort.Slice(channelList, func(i, j int) bool {
		if channelList[i].Date != channelList[j].Date {
			return channelList[i].Date > channelList[j].Date
		}
		if channelList[i].Fee != channelList[j].Fee {
			return channelList[i].Fee > channelList[j].Fee
		}
		return channelList[i].ChannelId > channelList[j].ChannelId
	})

	return channelIdList, channelList, nil
}

func (m *Manager) GetGuildsUnSettlementSummary(ctx context.Context, req *pb.GetGuildsUnSettlementSummaryReq) (*pb.GetGuildsUnSettlementSummaryRsp, error) {
	var err error
	resp := &pb.GetGuildsUnSettlementSummaryRsp{}
	if len(req.GetGuildIds()) == 0 {
		return resp, nil
	}

	now := time.Now()
	var dayFeeSum, monthFeeSum uint64
	guildIds := req.GetGuildIds()
	goldType := req.GetGoldType()

	store := m.mysqlStore.GetStoreV2(goldType)
	if store == nil {
		log.ErrorWithCtx(ctx, "GetGuildsUnSettlementSummary GetStoreV2 failed, req:[%+v]", req)
		return resp, fmt.Errorf("get store failed")
	}

	cache := m.cacheStore.GetCacheV2(goldType)
	if cache == nil {
		log.ErrorWithCtx(ctx, "GetGuildsUnSettlementSummary GetCacheV2 failed, req:[%+v]", req)
		return resp, fmt.Errorf("get cache failed")
	}

	dayFeeSum, err = store.GetGuildsDaySummary(ctx, guildIds, now)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetUnSettlementSummary GetGuildsDaySummary in: %v err %v", req, err)
		return resp, err
	}

	monthFeeSum, err = store.GetGuildsMonthSummary(ctx, guildIds, now)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetUnSettlementSummary GetGuildsMonthSummary in: %v err %v", req, err)
		return resp, err
	}

	resp.TodayFee = dayFeeSum
	resp.MonthFee = monthFeeSum

	// 查询日或月的总消费人数，会长后台的子母公会暂不支持
	if req.IncludeTodayPaidCnt && len(guildIds) > 0 {
		paidCnt, err := cache.GetGuildDailyPaidCnt(ctx, guildIds[0], now)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetUnSettlementSummary GetGuildDailyPaidCnt err:%s", err)
			return resp, err
		}
		resp.TodayPaidCnt = uint64(paidCnt)
		log.DebugWithCtx(ctx, "GetUnSettlementSummary GetGuildDailyPaidCnt resp:[%+v]", resp)
	}
	if req.IncludeMonthPaidCnt && len(guildIds) > 0 {
		paidCnt, err := cache.GetGuildMonthlyPaidCnt(ctx, guildIds[0], now)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetUnSettlementSummary GetGuildMonthlyPaidCnt err:%s", err)
			return resp, err
		}
		resp.MonthPaidCnt = uint64(paidCnt)
		log.DebugWithCtx(ctx, "GetUnSettlementSummary GetGuildMonthlyPaidCnt resp:[%+v]", resp)
	}

	switch goldType {
	case pb.GoldType_AMUSE_GOLD:
		resp.TodayIncome = common.AmuseConvIncome(dayFeeSum)
		resp.MonthIncome = common.AmuseConvIncome(monthFeeSum)

		var passGuildIds []uint32
		_, passGuildIds, err = m.mysqlStore.GetAmuseDB().CheckAmuseGuildsSettleList(ctx, req.GetGuildIds())
		if err != nil {
			log.ErrorWithCtx(ctx, "GetUnSettlementSummary CheckAmuseGuildsSettleList err:%s", err)
			return resp, err
		}
		if len(passGuildIds) == 0 {
			resp.NotSettleAmuseExtra = true
			log.DebugWithCtx(ctx, "GetUnSettlementSummary NotSettleAmuseExtra resp:[%+v]", resp)
			return resp, nil
		}

		if now.Day() == 1 {
			lastMonth := timeconv.AddDate(now, 0, -1, 0)
			extraList, err := m.mysqlStore.GetGuildsAmuseExtraIncome(ctx, lastMonth, passGuildIds)
			if err != nil {
				log.ErrorWithCtx(ctx, "GetUnSettlementSummary GetGuildsAmuseExtraIncome err:%s", err)
				return resp, nil
			}
			for _, ge := range extraList {
				resp.LastMonthExtraIncome += ge.SettlementMoney
			}
		} else {
			extraChannelMap, _, err := m.mysqlStore.GetGuildsAmuseExtraIncomeSnapshotMap(ctx, utils.DayTime(now), passGuildIds)
			if err != nil {
				log.ErrorWithCtx(ctx, "GetUnSettlementSummary GetGuildsAmuseExtraIncomeSnapshotMap err:%s", err)
				return resp, nil
			}
			for _, v := range extraChannelMap {
				resp.ThisMonthExtraIncome += v.SettlementMoney
			}
		}
	case pb.GoldType_YUYIN_GOLD:
		resp.TodayIncome = common.YuyinConvIncome(dayFeeSum)
		resp.MonthIncome = common.YuyinConvIncome(monthFeeSum)
	case pb.GoldType_INTERACT_GAME_GOLD:
		resp.TodayIncome = common.InteractGameConvIncome(dayFeeSum)
		resp.MonthIncome = common.InteractGameConvIncome(monthFeeSum)
	case pb.GoldType_ESPORT_GOLD:
		resp.TodayIncome = common.ESportConvIncome(dayFeeSum)
		resp.MonthIncome = common.ESportConvIncome(monthFeeSum)
	default:
	}

	return resp, nil
}

const KeyGetUidGuildsExtraIncome = "GetUidGuildsExtraIncome"

// GetGuildYuyinExtraIncome 获取公会语音直播按月份额外收益
func (m *Manager) GetGuildYuyinExtraIncome(ctx context.Context, req *pb.GetGuildYuyinExtraIncomeReq) (*pb.GetGuildYuyinExtraIncomeRsp, error) {
	rsp := &pb.GetGuildYuyinExtraIncomeRsp{}
	// 数据库直接查询方法
	fetch := func() (interface{}, error) {

		// 参数校验
		var err error
		if err = m.PrepareGetGuildYuyinExtraIncomeReq(req); err != nil {
			log.ErrorWithCtx(ctx, "get PrepareGetGuildYuyinExtraIncomeReq error, req:[%+v], err:[%+v]", req, err.Error())
			return rsp, err
		}

		beginTime := time.Unix(req.GetBeginTime(), 0)
		endTime := time.Unix(req.GetEndTime(), 0)

		// 数据库查询对应信息，该处写入为每月月初，不包含本月数据。
		infos, err := m.mysqlStore.GetYuyinDB().GetGuildsExtraIncome(ctx, req.GetGuildIds(), 0, beginTime, endTime)
		if err != nil {
			log.ErrorWithCtx(ctx, "get GetGuildExtraIncome error, req:[%+v], err:[%+v]", req, err.Error())
			return rsp, err
		}
		rsp.List = make([]*pb.ExtraIncome, 0)
		for i := 0; i < len(infos); i++ {
			item := &pb.ExtraIncome{
				GuildId:           infos[i].GuildId,
				ExtraIncomeRatio:  infos[i].ExtraIncomeRatio,
				MonthRecordIncome: uint64(int64(infos[i].MonthRecordIncome) + int64(infos[i].ValidAnchorIncome) + int64(infos[i].ValidAnchorAddIncome) + int64(infos[i].PotentialAnchorIncome)),
				Key:               time.Unix(int64(infos[i].StatTime), 0).Format("200601"),
			}
			// 浮点型精准数据，与最后结算使用的比例相同
			if infos[i].ExtraIncomeRatio != 0 {
				item.ExtraIncomeRatioV2 = decimal.NewFromInt(int64(infos[i].ExtraIncomeRatio)).StringFixed(1)
			} else {
				item.ExtraIncomeRatioV2 = decimal.NewFromFloat(infos[i].ExtraIncomeNewRatio).StringFixed(1)
			}
			rsp.List = append(rsp.List, item)

		}
		return rsp, nil
	}

	// 缓存判断，如果缓存存在直接从缓存获取，目前缓存时间是两分钟，详见utils/redis-mutex/mutex包。
	err := m.cacheStore.GetYuyinCache().Take(ctx, rsp, KeyGetUidGuildsExtraIncome+strconv.Itoa(int(req.GetUid())), fetch)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetGuildYuyinExtraIncome with cache failed, err:[%+v]", err)
	}

	log.DebugWithCtx(ctx, "get GetGuildExtraIncome finished, req:[%+v], rsp:[%+v]", req, rsp)
	return rsp, err
}

// PrepareGetGuildYuyinExtraIncomeReq 校验请求参数
func (m *Manager) PrepareGetGuildYuyinExtraIncomeReq(req *pb.GetGuildYuyinExtraIncomeReq) error {
	if req.GetUid() == 0 || len(req.GetGuildIds()) == 0 {
		return fmt.Errorf("check PrepareGetGuildYuyinExtraIncomeReq param error %+v", req)
	}
	// 如果没有设置相关的参数则自动获取6个月数据
	if req.GetBeginTime() <= 0 {
		req.BeginTime = time.Date(time.Now().Year(), time.Now().Month()-6, 1, 0, 0, 0, 0, time.Local).Unix()
	}
	if req.GetEndTime() == 0 {
		req.EndTime = time.Now().Unix()
	}
	return nil
}

const GetGuildYuyinTaskList = "GetGuildYuyinTaskList"

// GetGuildYuyinTaskList 获取语音直播工会任务列表
func (m *Manager) GetGuildYuyinTaskList(ctx context.Context, req *pb.GetGuildYuyinTaskListReq) (*pb.GetGuildYuyinTaskListRsp, error) {
	rsp := &pb.GetGuildYuyinTaskListRsp{}
	log.InfoWithCtx(ctx, "GetGuildYuyinTaskList req:[%+v]", req)

	// 用于直接查询的方法
	rpcCall := func() (interface{}, error) {

		// 处理时间范围
		nowTime := GetCurrentSnapShot(ctx)
		thisMonthZero := time.Date(nowTime.Year(), nowTime.Month(), 1, 0, 0, 0, 0, time.Local)
		thisMonthEnd := thisMonthZero.AddDate(0, 1, 0)
		rsp.RemainTime = thisMonthEnd.Unix() - nowTime.Unix()

		// 请求channel-live-stats服务，获取主播相关指标：有效主播数、新增有效主播数、潜力主播数
		var err error
		resp, err := client.ChannelLiveStatsCli.GetGuildMonthlyStats(ctx, req.GetUid(), req.GetGuildId(), uint32(time.Now().Unix()))
		if err != nil {
			log.ErrorWithCtx(ctx, "GetGuildMonthlyStats failed, err:[%+v], uid:[%+v], guildId:[%+v]", err, req.GetUid(), req.GetGuildId())
			return rsp, err
		}
		log.DebugWithCtx(ctx, "GetGuildMonthlyStats info [%+v]", resp)

		// 根据佣金系统获取对应的流水信息
		fees, err := m.mysqlStore.GetYuyinDB().GetGuildsMonthSummary(ctx, []uint32{req.GetGuildId()}, nowTime)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetFeesByTime error in%v,err:%v", req, err)
			return rsp, err
		}

		// 判断是否为新公会
		isNewGuild, newGuildPeriod, err := m.validGuildNewYuyinApplication(ctx, req.GetGuildId(), nowTime)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetGuildYuyinTaskList validGuildNewYuyinApplication failed, guildId:[%+v] err:[%+v]", req.GetGuildId(), err)
			return rsp, err
		}

		// 组装返回信息
		taskList := make([]*pb.GuildTaskInfo, 4)
		stats := resp.GetStats()
		if stats == nil {
			log.ErrorWithCtx(ctx, "GetGuildMonthlyStats stats is nil, req:[%+v]", req)
			return rsp, nil
		}
		// 月直播流水
		income := uint64(fees) / 100
		var ratio1, ratio2, ratio3, ratio4 float64
		taskList[0], ratio1 = GenYuyinTask(income, getConditionList(1, isNewGuild))
		// 月有效主播数
		taskList[1], ratio2 = GenYuyinTask(uint64(stats.GetValidAnchorCnt()), getConditionList(2, isNewGuild))
		// 月新增有效主播数
		taskList[2], ratio3 = GenYuyinTask(uint64(stats.GetNewValidAnchorCnt()), getConditionList(3, isNewGuild))
		// 月潜力主播数
		taskList[3], ratio4 = GenYuyinTask(uint64(stats.GetPotentialAnchorCnt()), getConditionList(4, isNewGuild))
		// 组装其它信息
		total := ratio1 + ratio2 + ratio3 + ratio4
		rsp.List = taskList
		rsp.BuffTotal = strconv.FormatFloat(total, 'f', 1, 32)
		rsp.IsNewGuild = isNewGuild
		rsp.TaskDetail = getTaskDetail(isNewGuild)
		rsp.NewGuildPeriod = newGuildPeriod
		return rsp, nil
	}

	// 按照公会维度设置缓存，这里主要目的是为了防止频繁查数据库导致数据库崩溃，可能造成获取的值存在差异。目前缓存时间是两分钟，详见utils/redis-mutex/mutex包。
	err := m.cacheStore.GetYuyinCache().Take(ctx, rsp, GetGuildYuyinTaskList+strconv.Itoa(int(req.GetGuildId())), rpcCall)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetGuildYuyinTaskList with cache failed, err:[%+v]", err)
		return rsp, err
	}

	log.DebugWithCtx(ctx, "GetGuildTaskList  req:[%+v] rsp:[%+v]", req, rsp)
	return rsp, nil
}

// validGuildNewApplication 判断公会是否是新加入语音合作公会
// 需求：https://q9jvw0u5f5.feishu.cn/docs/doccnYYhx3ARPWVBCoQ7SpBgRQc#
func (m *Manager) validGuildNewYuyinApplication(ctx context.Context, guildId uint32, now time.Time) (isNewGuild uint32, newGuildPeriod string, err error) {

	// 获取公会加入语音合作库时间
	histories, err := client.GuildCooperationCli.GetGuildCooperationHistory(ctx, guildId, guildCooperation.CooperationType_CTypeYuyin)
	if err != nil {
		log.ErrorWithCtx(ctx, "validGuildNewYuyinApplication GetGuildYuyinApplicationCreateTime failed, err:[%+v]", err)
		return 0, "", err
	}
	// 判断是否为空
	if len(histories) == 0 {
		log.WarnWithCtx(ctx, "validGuildNewYuyinApplication GetGuildCooperationHistory, guild not yuyin application guild.")
		return 0, "", nil
	}
	// 获取最早加入的时间
	var createTime time.Time
	for _, history := range histories {
		if history.GetOptType() == guildCooperation.CoopOptType_Add {
			if createTime.IsZero() {
				createTime = time.Unix(history.GetOptTime(), 0)
			}
			if !createTime.IsZero() && createTime.After(time.Unix(history.GetOptTime(), 0)) {
				createTime = time.Unix(history.GetOptTime(), 0)
			}
		}
	}
	// 判断是否取到时间
	if createTime.IsZero() {
		log.WarnWithCtx(ctx, "validGuildNewYuyinApplication GetGuildYuyinApplicationCreateTime, guild history not has add guild.")
		return 0, "", nil
	}

	// 根据加入时间判断是否为新加入公会
	startTime := time.Date(createTime.Year(), createTime.Month(), 1, 0, 0, 0, 0, time.Local)
	if createTime.Day() >= 20 { // 当月20号以后加入的，从下个自然月开始算首月
		startTime = time.Date(createTime.Year(), createTime.Month()+1, 1, 0, 0, 0, 0, time.Local)
	}
	validMonth := time.Date(now.Year(), now.Month()-2, 1, 0, 0, 0, 0, time.Local)
	if validMonth.After(startTime) { // 首次加入语音直播合作库3个自然月内的公会为新公会
		return 0, "", nil
	} else {
		isNewGuild = 1
	}
	// 处理外显新公会时间范围
	endTime := time.Date(startTime.Year(), startTime.Month()+2, 1, 0, 0, 0, 0, time.Local)
	newGuildPeriod = fmt.Sprintf("%+v月-%+v月", int(startTime.Month()), int(endTime.Month()))
	return isNewGuild, newGuildPeriod, nil
}

func getTaskDetail(isNewGuild uint32) []*pb.GuildTaskDetailInfo {
	config := conf.YuyinTaskConf.Get()
	if isNewGuild == 1 {
		return genRespTaskDetail(config.NewGuildTaskList)
	}
	return genRespTaskDetail(config.GuildTaskList)
}

func genRespTaskDetail(configs []*conf.GuildTaskDetail) []*pb.GuildTaskDetailInfo {
	result := make([]*pb.GuildTaskDetailInfo, 4)
	for _, c := range configs {
		config := c
		if config.Level == 1 {
			result[0] = &pb.GuildTaskDetailInfo{
				Name:      config.Name,
				RatioList: genRespTaskRatio(config.RatioList),
			}
		}
		if config.Level == 2 {
			result[1] = &pb.GuildTaskDetailInfo{
				Name:      config.Name,
				RatioList: genRespTaskRatio(config.RatioList),
			}
		}
		if config.Level == 3 {
			result[2] = &pb.GuildTaskDetailInfo{
				Name:      config.Name,
				RatioList: genRespTaskRatio(config.RatioList),
			}
		}
		if config.Level == 4 {
			result[3] = &pb.GuildTaskDetailInfo{
				Name:      config.Name,
				RatioList: genRespTaskRatio(config.RatioList),
			}
		}
	}
	return result
}

func genRespTaskRatio(configs []*conf.GuildTaskRatio) []*pb.GuildTaskRatioInfo {
	result := make([]*pb.GuildTaskRatioInfo, 0)
	for _, config := range configs {
		result = append(result, &pb.GuildTaskRatioInfo{
			Value:      config.BeginCondition,
			Ratio:      strconv.FormatFloat(config.Ratio, 'f', 1, 32),
			ExtraRatio: config.ExtraRatio,
			Level:      config.Level,
		})
	}
	sort.Slice(result, func(i, j int) bool {
		return result[i].Level < result[j].Level
	})
	return result
}

func getConditionList(level uint32, isNewGuild uint32) *conf.GuildTaskDetail {
	config := conf.YuyinTaskConf.Get()
	if isNewGuild == 1 {
		return getTask(level, config.NewGuildTaskList)
	}
	return getTask(level, config.GuildTaskList)
}

func getTask(level uint32, config []*conf.GuildTaskDetail) *conf.GuildTaskDetail {
	for _, taskDetail := range config {
		result := taskDetail
		if result.Level == level {
			return result
		}
	}
	return &conf.GuildTaskDetail{}
}

// GenYuyinTask 生成语音直播任务列表
func GenYuyinTask(income uint64, taskConditionList *conf.GuildTaskDetail) (*pb.GuildTaskInfo, float64) {
	var ratio string
	ratioNum, level := GenYuyinTaskRatio(income, taskConditionList)
	ratio = strconv.FormatFloat(ratioNum, 'f', 1, 32)

	return &pb.GuildTaskInfo{
		Ratio: ratio,  // 当前的比例
		Value: income, // 当前的情况
		Level: level,  // 等级
	}, ratioNum
}

// GenYuyinTaskRatio 获取语音公会任务对应奖励比例
func GenYuyinTaskRatio(income uint64, taskConditionList *conf.GuildTaskDetail) (ratio float64, level uint32) {
	// 保证顺序，做一轮排序
	sort.Slice(taskConditionList.RatioList, func(i, j int) bool {
		return taskConditionList.RatioList[i].Level < taskConditionList.RatioList[j].Level
	})

	// 判断当前的比例，由于是顺序判断梯度，所以ratio值会取到最大比例
	for _, v := range taskConditionList.RatioList {
		if v.BeginCondition > income { // 减少循环次数
			break
		}
		if v.BeginCondition <= income && income < v.EndCondition {
			ratio = v.Ratio
			level = v.Level
		}
	}

	return ratio, level
}

// GenerateYuyinExtraIncome 生成语音直播额外收益详情
// 目前该方法保证了原子性操作，即便是多次操作，也能保证其数据准确性
func (m *Manager) GenerateYuyinExtraIncome(ctx context.Context, nowTime time.Time, isDaily bool) bool {
	log.InfoWithCtx(ctx, "GenerateYuyinExtraIncome start")
	log.InfoWithCtx(ctx, "GenerateYuyinExtraIncome time:[%+v]", nowTime)

	// 生成对应的处理时间
	endTime := time.Date(nowTime.Year(), nowTime.Month(), 1, 0, 0, 0, 0, time.Local)
	statTime := time.Date(nowTime.Year(), nowTime.Month()-1, 1, 0, 0, 0, 0, time.Local)

	if isDaily {
		statTime = time.Date(nowTime.Year(), nowTime.Month(), 1, 0, 0, 0, 0, time.Local)
		endTime = nowTime
	}

	// 查询当前需要处理的所有公会
	guildIds, err := m.mysqlStore.GetYuyinDB().GetGuildIdsFromMonthStat(ctx, statTime, endTime)
	if err != nil {
		errMsg := fmt.Sprintf("GeneralExtraIncome mysqlStore.GetGuildIds err:%s", err)
		log.ErrorWithCtx(ctx, errMsg)
		m.feishu.SendError(fmt.Sprintf("语音额外统计错误：%+v", errMsg))
		return false
	}
	log.DebugWithCtx(ctx, "GetGuildIdsFromMonthStat succ, guildIds:[%+v]", guildIds)

	// 循环处理所有公会信息
	extraInfos := make([]*model.YuyinGoldExtraIncome, 0)
	guildLen := len(guildIds)
	for j := 0; j < guildLen; {
		guildId := guildIds[j]

		// 生成额外收益详情
		extraInfo, err := m.genYuyinGoldExtraIncomeDetail(ctx, guildId, statTime, endTime)
		if err != nil {
			log.ErrorWithCtx(ctx, "GenerateYuyinExtraIncome genYuyinGoldExtraIncomeDetail failed, "+
				"guildId:[%+v], statTime:[%+v], err:[%+v]", guildId, statTime, err)
			continue
		}
		// 若返回的结构为空的时候直接跳过
		if extraInfo == nil {
			log.WarnWithCtx(ctx, "GenerateYuyinExtraIncome genYuyinGoldExtraIncomeDetail get empty, guildid:[%+v]", guildId)
			j++
			continue
		}

		// 新增或更新额外收入表。
		if !isDaily || m.sc.IsTest() { // 仅当统计上月收益时才进行存储
			err = m.mysqlStore.GetYuyinDB().AddYuyinGuildExtraIncome(ctx, extraInfo)
			if err != nil {
				time.Sleep(100 * time.Millisecond)
				log.ErrorWithCtx(ctx, "AddGuildExtraIncome err:", err)
				m.feishu.SendError(fmt.Sprintf("AddGuildExtraIncome err:" + err.Error()))
				continue
			}
		}
		extraInfos = append(extraInfos, extraInfo)
		j++

		time.Sleep(100 * time.Millisecond) // 减小数据库结算时的压力
	}

	// 生成对应表格并发送文件
	m.genYuyinExtraExcelAndSendEmail(ctx, statTime, extraInfos)

	m.feishu.SendInfo(fmt.Sprintf("语音直播额外奖励 汇总完成 成功数：%d", len(extraInfos)))

	log.InfoWithCtx(ctx, "GenerateYuyinExtraIncome succ")
	return true
}

// genYuyinGoldExtraIncomeDetail 生成公会语音额外收益详情数据
func (m *Manager) genYuyinGoldExtraIncomeDetail(ctx context.Context, guildId uint32, statTime, endTime time.Time) (*model.YuyinGoldExtraIncome, error) {

	// 是否对公
	masterResp, err := client.ExchangeGuildCli.GetMasterUid(ctx, guildId)
	if err != nil {
		log.ErrorWithCtx(ctx, "genYuyinGoldExtraIncomeDetail fail exchangeCli.GetMasterUid  err %+v, guild %d", err, guildId)
		return nil, err
	}

	var guildOwner uint32
	var isCorp bool
	// 非对公
	if masterResp.GetUid() == 0 {
		guildResp, _, sErr := m.GetGuildInfo(ctx, guildId)
		if sErr != nil {
			log.ErrorWithCtx(ctx, "genYuyinGoldExtraIncomeDetail GetGuildInfo err:%s", sErr)
			return nil, sErr
		}
		guildOwner = guildResp.GetOwner()
	} else {
		guildOwner = masterResp.GetUid()
		isCorp = true
	}

	// 获取公会时间对应的公会任务详情
	totalIncome, totalFee, monthValidAnchorCnt, monthNewAddValidAnchorCnt, potentialAnchorCnt, taskErr := m.GetGuildIncomeTaskInfo(ctx, guildId, statTime, endTime)
	if taskErr != nil {
		errMsg := fmt.Sprintf("GetGuildTaskInfo err:%s, guildid:%d", taskErr, guildId)
		log.ErrorWithCtx(ctx, errMsg)
		m.feishu.SendError(fmt.Sprintf("语音额外统计错误：%+v", errMsg))
		time.Sleep(100 * time.Millisecond)
		return nil, taskErr
	}

	// 若总收益额为空的时候，直接跳过
	if totalIncome == 0 {
		errMsg := fmt.Sprintf("GetIncomesByTime err, guildid:%d, totalIncome:0", guildId)
		log.WarnWithCtx(ctx, errMsg)
		return nil, nil
	}

	// 判断是否为新公会
	isNewGuild, _, err := m.validGuildNewYuyinApplication(ctx, guildId, statTime)
	if err != nil {
		log.ErrorWithCtx(ctx, "GenerateYuyinExtraIncome validGuildNewYuyinApplication failed, guildId:[%+v] err:[%+v]", guildId, err)
		return nil, err
	}

	var ratio float64                       // 额外收益比例
	var monthValidAnchorIncome uint64       // 月有效主播收益
	var monthNewAddValidAnchorIncome uint64 // 月新增有效主播收益

	// 月直播流水收益
	var recordIncome uint64
	// totalIncome 非流水 而是金钻 流水的
	var recordIncomeRatio float64
	recordIncomeRatio, _ = GenYuyinTaskRatio(uint64(totalFee/100), getConditionList(1, isNewGuild)) // 这里计算比例的时候按元为单位进行计算。
	ratio += recordIncomeRatio
	recordIncome = uint64(float64(totalFee) * recordIncomeRatio / 100)
	log.DebugWithCtx(ctx, "GenYuyinTaskRatio task1, recordIncomeRatio:[%+v], recordIncome:[%+v]",
		recordIncomeRatio, recordIncome)

	// 月有效主播数收益
	var monthValidAnchorRatio float64
	monthValidAnchorRatio, _ = GenYuyinTaskRatio(uint64(monthValidAnchorCnt), getConditionList(2, isNewGuild))
	ratio += monthValidAnchorRatio
	monthValidAnchorIncome = uint64(float64(totalFee) * monthValidAnchorRatio / 100)
	log.DebugWithCtx(ctx, "GenYuyinTaskRatio task2, monthValidAnchorRatio:[%+v], monthValidAnchorIncome:[%+v]",
		monthValidAnchorRatio, monthValidAnchorIncome)

	// 月新增有效主播数收益
	var monthNewAddValidAnchorRatio float64
	monthNewAddValidAnchorRatio, _ = GenYuyinTaskRatio(uint64(monthNewAddValidAnchorCnt), getConditionList(3, isNewGuild))
	ratio += monthNewAddValidAnchorRatio
	monthNewAddValidAnchorIncome = uint64(float64(totalFee) * monthNewAddValidAnchorRatio / 100)
	log.DebugWithCtx(ctx, "GenYuyinTaskRatio task3, monthNewAddValidAnchorRatio:[%+v], monthNewAddValidAnchorIncome:[%+v]",
		monthNewAddValidAnchorRatio, monthNewAddValidAnchorIncome)

	// 月潜力主播数收益
	var potentialAnchorRatio float64
	var potentialAnchorIncome uint64
	potentialAnchorRatio, _ = GenYuyinTaskRatio(uint64(potentialAnchorCnt), getConditionList(4, isNewGuild))
	ratio += potentialAnchorRatio
	potentialAnchorIncome = uint64(float64(totalFee) * potentialAnchorRatio / 100)
	log.DebugWithCtx(ctx, "GenYuyinTaskRatio task4, potentialAnchorRatio:[%+v], potentialAnchorIncome:[%+v]",
		potentialAnchorRatio, potentialAnchorIncome)

	// 这里改为使用数据库实体传入对象信息
	extraInfo := &model.YuyinGoldExtraIncome{
		GuildId:               guildId,
		IsCorp:                isCorp,
		GuildOwner:            guildOwner,
		SettlementDate:        utils.GetMonthInt(statTime),
		StatTime:              uint64(statTime.Unix()),
		TotalFee:              uint64(totalFee),
		MonthRecordIncome:     recordIncome,
		ValidAnchorIncome:     monthValidAnchorIncome,
		ValidAnchorAddIncome:  monthNewAddValidAnchorIncome,
		PotentialAnchorIncome: potentialAnchorIncome,
		ExtraIncomeNewRatio:   ratio,
		MonthValidAnchor:      monthValidAnchorCnt,
		MonthAddAnchor:        monthNewAddValidAnchorCnt,
		PotentialAnchor:       potentialAnchorCnt,
		IsNewGuild:            isNewGuild,
	}

	return extraInfo, nil
}

// genExcelAndSendEmail 生成语音额外收益邮件并发送对应的邮件
func (m *Manager) genYuyinExtraExcelAndSendEmail(ctx context.Context, timeStart time.Time, extraInfos []*model.YuyinGoldExtraIncome) {

	// 生成Excel
	fileName, err := m.genExcel(ctx, timeStart, extraInfos)
	if err != nil {
		log.ErrorWithCtx(ctx, "GenUKWReport genExcel failed, err:[%+v]", err)
		return
	}
	// 本地文件清除
	defer func() {
		_ = os.Remove(fileName)
	}()

	// 发送上月统计邮件
	m.sendEmail(ctx, m.createEmailTitle("会长服务号佣金-语音直播数据", timeStart), "见附件", []string{fileName})
}

func (m *Manager) genExcel(ctx context.Context, startTime time.Time, extraInfos []*model.YuyinGoldExtraIncome) (string, error) {

	// 生成神秘人特权开通数据总汇
	if len(extraInfos) == 0 {
		err := fmt.Errorf("genExcel failed, extraInfos is null! ")
		log.ErrorWithCtx(ctx, "%+v", err)
		return "", err
	}
	extraInfoWithdraws := make([][]interface{}, 0)
	for _, e := range extraInfos {
		row := []interface{}{
			e.GuildId,
			e.TotalFee,
			e.MonthRecordIncome,
			e.ValidAnchorIncome,
			e.ValidAnchorAddIncome,
			e.PotentialAnchorIncome,
			strconv.FormatFloat(e.ExtraIncomeNewRatio, 'f', 1, 32),
			e.MonthValidAnchor,
			e.MonthAddAnchor,
			e.PotentialAnchor,
		}
		extraInfoWithdraws = append(extraInfoWithdraws, row)
	}
	e := export.CreateReportTableHeader("会长服务号佣金-语音直播数据", []string{"公会id", "月流水", "月流水任务收益", "月有效主播任务收益",
		"月新增有效主播任务收益", "潜力主播任务收益", "收益比率", "月有效主播数", "月新增有效主播数", "潜力主播数"})
	e.PushRowsV2(extraInfoWithdraws)

	// 生成本地表格
	fileName := fmt.Sprintf("extra-%s.xlsx", startTime.Format(PeriodFormatStr))
	e.Save(fileName)
	return fileName, nil
}

const (
	PeriodFormatStr = "200601"
)

func (m *Manager) createEmailTitle(title string, t time.Time) string {
	title = fmt.Sprintf("%s - %s", title, t.Format(PeriodFormatStr))
	if m.sc.IsTest() {
		title += " [测试环境]"
	}
	return title
}

const SpecialEmailTo = "special_email_to"

// SendEmail 发送报表邮件
func (m *Manager) sendEmail(ctx context.Context, title, html string, attachPath []string) {
	var to []string

	// 配置文件
	to = conf.YuyinTaskConf.GetReportEmailTo()

	// 指定收件人
	if val, ok := ctx.Value(SpecialEmailTo).(string); ok {
		to = strings.SplitAfter(val, ",")
	}

	settlement.SendEmail(ctx, to, title, html, attachPath)
}

// GetGuildIncomeTaskInfo 获取公会营收任务进度详情
func (m *Manager) GetGuildIncomeTaskInfo(ctx context.Context, guildId uint32, beginTime, endTime time.Time) (
	int64, int64, uint32, uint32, uint32, error) {

	// 获取公会语音直播无状态汇总数据（包含礼物流水和骑士流水）
	totalFee, err := m.mysqlStore.GetYuyinDB().GetFeesByTimeNoStatus(ctx, guildId, beginTime, endTime)
	if err != nil {
		errMsg := fmt.Sprintf("GetGuildIncomeTaskInfo GetFeesByTimeNoStatus err:[%+v], guildid:[%+v]", err, guildId)
		log.ErrorWithCtx(ctx, errMsg)
		return 0, 0, 0, 0, 0, err
	}
	// 实际金钻收益
	totalIncome := int64(common.YuyinConvIncome(totalFee.FeeSum))

	// 处理请求参数，确保获取时间是上月的一号
	getTime := utils.MonthTime(beginTime).Unix()
	log.InfoWithCtx(ctx, "GetGuildIncomeTaskInfo getTime:[%+v]", getTime)

	// 获取公会任务主播相关信息
	statRsp, err := client.ChannelLiveStatsCli.GetGuildMonthlyStats(ctx, guildId, guildId, uint32(getTime))
	if err != nil {
		log.ErrorWithCtx(ctx, "GetGuildMonthlyStats err:[%+v], in:[%+v]", err, guildId)
		return 0, 0, 0, 0, 0, err
	}
	monthValidAnchorCnt := statRsp.GetStats().GetValidAnchorCnt()
	monthNewAddValidAnchorCnt := statRsp.GetStats().GetNewValidAnchorCnt()
	potentialAnchorCnt := statRsp.GetStats().GetPotentialAnchorCnt()

	return totalIncome, int64(totalFee.FeeSum), monthValidAnchorCnt, monthNewAddValidAnchorCnt, potentialAnchorCnt, nil
}

const extraIncome string = "extra_income"

// GenerateYuyinExtraIncomeWithLock 使用带锁方式结算额外收益
func (m *Manager) GenerateYuyinExtraIncomeWithLock(ctx context.Context, nowTime time.Time, isDaily bool) {
	getNowTimeKey := nowTime.Format("20060102")

	// 使用分布式锁进行加锁，非阻塞流程，保证当月操作的唯一性
	lock := m.cacheStore.LockYuyinGenDayFlag(nowTime, getNowTimeKey)
	if !lock.Locked {
		log.WarnWithCtx(ctx, "GeneralYuyinExtraIncomeWithLock not get lock, skip over generate")
		return
	}

	// 开始进行结算
	log.InfoWithCtx(ctx, "GenerateYuyinExtraIncomeWithLock GenDayInfoAtTime:", nowTime.Unix(), extraIncome)
	if !m.GenerateYuyinExtraIncome(ctx, nowTime, isDaily) {
		// 当任务执行失败时，进行解锁，以保证其它时候可以对其进行修正
		if err := m.cacheStore.UnLockYuyinGenDayFlag(lock); err != nil {
			jsonInfo, _ := json.Marshal(lock)
			log.ErrorWithCtx(ctx, "GenerateYuyinExtraIncomeWithLock UnLockYuyinGenDayFlag failed, err:[%+v], lock:[%+v]",
				err, string(jsonInfo))
		}
	}
}

// AmuseGuildChannelIncomeList 会长后台-多人互动-房间经营数据
func (m *Manager) AmuseGuildChannelIncomeList(ctx context.Context, req *pb.AmuseGuildChannelIncomeListReq) (*pb.AmuseGuildChannelIncomeListResp, error) {
	resp := &pb.AmuseGuildChannelIncomeListResp{}
	nowTime := time.Now()
	var startTime, endTime time.Time
	var compareBeginTime, compareEndTime time.Time
	if nowTime.Day() == 1 {
		startTime = time.Date(nowTime.Year(), nowTime.Month()-1, 1, 0, 0, 0, 0, time.Local)
		endTime = time.Date(nowTime.Year(), nowTime.Month(), 1, 0, 0, 0, 0, time.Local)
		compareBeginTime = time.Date(nowTime.Year(), nowTime.Month()-2, 1, 0, 0, 0, 0, time.Local)
		compareEndTime = time.Date(nowTime.Year(), nowTime.Month()-1, 1, 0, 0, 0, 0, time.Local)
	} else {
		startTime = time.Date(nowTime.Year(), nowTime.Month(), 1, 0, 0, 0, 0, time.Local)
		endTime = time.Date(nowTime.Year(), nowTime.Month(), nowTime.Day(), 0, 0, 0, 0, time.Local)
		compareBeginTime = time.Date(nowTime.Year(), nowTime.Month()-1, 1, 0, 0, 0, 0, time.Local)
		compareEndTime = time.Date(nowTime.Year(), nowTime.Month()-1, nowTime.Day(), 0, 0, 0, 0, time.Local)
	}

	store := m.mysqlStore.GetAmuseDB()

	infos, total, err := store.GetGuildDayChannelSummary(ctx, req.GetGuildId(), startTime, endTime, req.GetChannelId(), req.GetOffset(), req.GetLimit())
	if err != nil {
		log.ErrorWithCtx(ctx, "AmuseGuildChannelIncomeList GetGuildDayChannelSummary in%v err %v", req, err)
		return resp, err
	}

	var roomQoq float32
	for _, info := range infos {
		lastMonthFee, _, err := store.GetGuildRoomMonthSum(ctx, req.GetGuildId(), info.ChannelId, compareBeginTime)
		if err != nil {
			log.ErrorWithCtx(ctx, "AmuseGuildChannelIncomeList lastMonthFee in%v err %v", req, err)
			return resp, err
		}
		samePeriodFee, _, err := store.GetGuildRoomDayRangeSum(ctx, req.GetGuildId(), info.ChannelId, compareBeginTime, compareEndTime)
		if err != nil {
			log.ErrorWithCtx(ctx, "AmuseGuildChannelIncomeList GetGuildRoomDayRangeSum in%v err %v", req, err)
			return resp, err
		}
		if info.Fee == 0 || samePeriodFee == 0 {
			roomQoq = 0
		} else {
			this := float64(info.Fee)
			last := float64(samePeriodFee)
			roomQoq = float32((this - last) / last)
		}
		resp.ChannelList = append(resp.ChannelList, &pb.AmuseGuildChannelIncomeListRespLItem{
			ChannelId:     info.ChannelId,
			Fee:           info.Fee,
			SamePeriodFee: samePeriodFee,
			SamePeriodQoq: roomQoq,
			LastMonthFee:  lastMonthFee,
		})
	}
	resp.Total = total
	return resp, nil
}

func (m *Manager) GetGuildInfo(ctx context.Context, guildId uint32) (
	guildInfo *Guild.GuildResp, dismiss bool, err error) {
	var guildErr protocol.ServerError
	guildInfo, guildErr = client.GuildCli.GetGuild(ctx, guildId)
	if guildErr != nil && guildErr.Code() != -501 {
		log.ErrorWithCtx(ctx, "GetGuildInfo GetGuild err:%s", guildErr)
		err = guildErr
		return
	} else if guildErr != nil && guildErr.Code() == -501 {
		// 公会不存在，但有流水，需记录
		log.WarnWithCtx(ctx, "GetGuildInfo guild not found, guild_id:%d", guildId)
		dismiss = true
		return
	} else {
		return
	}
}
