syntax = "proto3";

import "reconcile-v2/reconcile-v2.proto";

option go_package = "golang.52tt.com/protocol/services/gold-commission";
package gold_commission;


service GoldCommission {
  // 主动结算语音会长佣金
  rpc SettlementYuyinGold (SettlementYuyinGoldReq) returns (SettlementYuyinGoldResp) {}
  rpc GenYuyinExtraReport (GenYuyinExtraReportReq) returns (GenYuyinExtraReportResp) {}

  // 语音礼物佣金补单
  rpc GetYuyinGoldPresentOrderCount (ReconcileV2.TimeRangeReq) returns (ReconcileV2.CountResp) {}
  rpc GetYuyinGoldPresentOrderList (ReconcileV2.TimeRangeReq) returns (ReconcileV2.OrderIdsResp) {}
  rpc ReplaceYuyinGoldPresentOrder (ReconcileV2.ReplaceOrderReq) returns (ReconcileV2.EmptyResp) {}

  // 语音骑士佣金补单
  rpc GetYuyinGoldKnightOrderCount (ReconcileV2.TimeRangeReq) returns (ReconcileV2.CountResp) {}
  rpc GetYuyinGoldKnightOrderList (ReconcileV2.TimeRangeReq) returns (ReconcileV2.OrderIdsResp) {}
  rpc ReplaceYuyinGoldKnightOrder (ReconcileV2.ReplaceOrderReq) returns (ReconcileV2.EmptyResp) {}

  // 娱乐房礼物佣金补单
  rpc GetAmuseGoldPresentOrderCount (ReconcileV2.TimeRangeReq) returns (ReconcileV2.CountResp) {}
  rpc GetAmuseGoldPresentOrderList (ReconcileV2.TimeRangeReq) returns (ReconcileV2.OrderIdsResp) {}
  rpc ReplaceAmuseGoldPresentOrder (ReconcileV2.ReplaceOrderReq) returns (ReconcileV2.EmptyResp) {}

  // 娱乐房狼人杀佣金补单
  rpc GetAmuseGoldWerewolfOrderCount (ReconcileV2.TimeRangeReq) returns (ReconcileV2.CountResp) {}
  rpc GetAmuseGoldWerewolfOrderList (ReconcileV2.TimeRangeReq) returns (ReconcileV2.OrderIdsResp) {}
  rpc ReplaceAmuseGoldWerewolfOrder (ReconcileV2.ReplaceOrderReq) returns (ReconcileV2.EmptyResp) {}

  // 互动游戏佣金补单
  rpc GetInteractGameGoldPresentOrderCount (ReconcileV2.TimeRangeReq) returns (ReconcileV2.CountResp) {}
  rpc GetInteractGameGoldPresentOrderList (ReconcileV2.TimeRangeReq) returns (ReconcileV2.OrderIdsResp) {}
  rpc ReplaceInteractGameGoldPresentOrder (ReconcileV2.ReplaceOrderReq) returns (ReconcileV2.EmptyResp) {}

  /* 会长后台接口 */

  // 获取对公会长旗下多个公会下的语音主播/房间流水统计
  rpc GetGuildsYuyinAnchorStat (GetGuildsYuyinAnchorStatReq) returns (GetGuildsYuyinAnchorStatResp) {}
  rpc GetGuildsAmuseChannelStat (GetGuildsAmuseChannelStatReq) returns (GetGuildsAmuseChannelStatResp) {}
  rpc GetGuildsInteractGameAnchorStat (GetGuildsInteractGameAnchorStatReq) returns (GetGuildsInteractGameAnchorStatResp) {}
  // 获取公会下未结算总流水
  rpc GetGuildsUnSettlementSummary (GetGuildsUnSettlementSummaryReq) returns (GetGuildsUnSettlementSummaryRsp) {}
  // 娱乐房收入情况查询
  rpc AmuseGuildChannelIncomeList (AmuseGuildChannelIncomeListReq) returns (AmuseGuildChannelIncomeListResp) {}

  /* 会长服务号接口 */

  // 房间类型合并接口
  // 获取消费排行榜
  rpc GetConsumeRank (GetConsumeRankReq) returns (GetConsumeRankRsp) {}
  // 根据截止时间获取月收益日趋势列表
  rpc GetIncomeTrendList (GetIncomeTrendListReq) returns (GetIncomeTrendListRsp) {}
  // 获取日收益对比
  rpc GetGuildDayQoq (GetGuildDayQoqReq) returns (GetGuildDayQoqResp) {}
  // 获取公会月详情列表（消费成员或主播维度）
  rpc GetGuildMonthMemberList (GetGuildMonthMemberListReq) returns (GetGuildMonthMemberListResp) {}
  // 获取该日收益情况
  rpc GetDayIncome (GetDayIncomeReq) returns (GetDayIncomeRsp) {}
  // 获取该月收益情况
  rpc GetMonthIncome (GetMonthIncomeReq) returns (GetMonthIncomeRsp) {}

  // 娱乐房佣金接口
  // 获取娱乐房房间详情
  rpc GetAmuseChannelDetail (GetAmuseChannelDetailReq) returns (GetAmuseChannelDetailRsp) {}
  // 获取娱乐房收入情况
  rpc GetAmuseIncomeDetail (GetAmuseIncomeDetailReq) returns (GetAmuseIncomeDetailRsp) {}
  // 获取工会娱乐房房间日流水列表（房间纬度）
  rpc GetAmuseGuildRoomIncomeList (GetAmuseGuildRoomIncomeListReq) returns (GetAmuseGuildRoomIncomeListRsp) {}
  // 获取语音直播公会日收益详情列表（主播维度）
  rpc GetAmuseGuildDayIncomeList (GetGuildDayIncomeListReq) returns (GetAmuseGuildDayIncomeListResp) {}
  // 获取语音直播公会月详情列表（日维度）
  rpc GetAmuseGuildMonthIncomeList (GetGuildMonthIncomeListReq) returns (GetGuildMonthIncomeListResp) {}
  // 查询娱乐房收益明细
  rpc SearchAmuseGuildDetail (SearchAmuseGuildDetailReq) returns (SearchAmuseGuildDetailResp) {}
  // 查询娱乐房房间日环比数据
  rpc GetAmuseRoomDayQoqInfo (GetAmuseRoomDayQoqInfoReq) returns (GetAmuseRoomDayQoqInfoRsp) {}
  // 获取多人互动额外收益详情（按月）
  rpc GetAmuseExtraDetail (GetAmuseExtraDetailReq) returns (GetAmuseExtraDetailResp) {}

  // 语音房佣金接口
  // 获取会长服务号语音直播收益数据详情（会长服务号首页加载接口）
  rpc GetYuyinIncomeDetail (GetYuyinIncomeDetailReq) returns (GetYuyinIncomeDetailRsp) {}
  // 获取语音直播该周收益情况
  rpc GetYuyinWeekIncome (GetYuyinWeekIncomeReq) returns (GetYuyinWeekIncomeRsp) {}
  // 获取语音直播按月份额外收益
  rpc GetGuildYuyinExtraIncome (GetGuildYuyinExtraIncomeReq) returns (GetGuildYuyinExtraIncomeRsp) {}
  // 获取语音直播工会任务列表
  rpc GetGuildYuyinTaskList (GetGuildYuyinTaskListReq) returns (GetGuildYuyinTaskListRsp) {}
  // 获取语音直播公会日收益详情列表（主播维度）
  rpc GetYuyinGuildDayIncomeList (GetGuildDayIncomeListReq) returns (GetYuyinGuildDayIncomeListResp) {}
  // 获取语音直播公会月详情列表（日维度）
  rpc GetYuyinGuildMonthIncomeList (GetGuildMonthIncomeListReq) returns (GetGuildMonthIncomeListResp) {}
  // 查询语音直播收益明细
  rpc SearchYuyinGuildDetail (SearchYuyinGuildDetailReq) returns (SearchYuyinGuildDetailResp) {}

  // 互动游戏佣金接口
  rpc GetInteractGameIncomeDetail (GetInteractGameIncomeDetailReq) returns (GetInteractGameIncomeDetailRsp) {}
  // 获取互动游戏日收益详情列表（主播维度）
  rpc GetInteractGameDayIncomeList (GetGuildDayIncomeListReq) returns (GetInteractGameDayIncomeListResp) {}
  // 获取互动游戏月详情列表（日维度）
  rpc GetInteractGameMonthIncomeList (GetGuildMonthIncomeListReq) returns (GetInteractGameMonthIncomeListResp) {}
  // 获取语音直播该周收益
  rpc GetInteractGameWeekIncome (GetInteractGameWeekIncomeReq) returns (GetInteractGameWeekIncomeRsp) {}

  /* 运营后台接口 */
  // 获取指定时间段内产生流水的娱乐房公会IDs
  rpc GetAmuseGuildIdsByRange (GetAmuseGuildIdsByRangeReq) returns (GetAmuseGuildIdsByRangeResp) {}
  // 多人互动额外奖励结算报表（旧蓝色后台迁移）
  rpc GetAmuseExtraIncomeSettleList (GetAmuseExtraIncomeSettleListReq) returns (GetAmuseExtraIncomeSettleListResp) {}
  // 获取结算公会名单（仅多人互动额外奖励）
  rpc GetAmuseSettleGuildList(GetAmuseSettleGuildListReq) returns (GetAmuseSettleGuildListResp) {}
  // 获取结算房间名单
  rpc GetAmuseSettleChannelList(GetAmuseSettleChannelListReq) returns (GetAmuseSettleChannelListResp) {}
  // 设置公会结算状态
  rpc SetAmuseSettleGuild(SetAmuseSettleGuildReq) returns (SetAmuseSettleGuildResp) {}
  // 设置房间结算状态
  rpc SetAmuseSettleChannel(SetAmuseSettleChannelReq) returns (SetAmuseSettleChannelResp) {}


  // 获取公会互动游戏历史权限
  rpc GetGuildInteractGamePer(GetGuildInteractGamePerReq) returns (GetGuildInteractGamePerResp){}
  // 获取互动游戏额外奖励
  rpc GetInteractGameExtraIncome(GetInteractGameExtraIncomeReq) returns (GetInteractGameExtraIncomeResp){} 

}

// 佣金类型
enum GoldType {
  UNKNOWN_GOLD = 0;
  AMUSE_GOLD = 4; // 多人互动会长佣金（与房间类型无关，仅为了兼容旧版）
  YUYIN_GOLD = 7; // 语音直播会长佣金
  INTERACT_GAME_GOLD = 101; // 互动游戏会长佣金
  INTERACT_GAME_EXTRA_GOLD = 102; // 互动游戏会长额外奖励
}

// 流水来源
enum SourceType {
  UnknownType = 0;
  SourceTypePresent = 1; // 收礼
  // 2\3 ignore
  SourceTypeKnight = 4; // 加入骑士团
  SourceTypeWerewolf = 5; // 狼人杀
  SourceTypeInteractGame = 6; // 互动游戏
}

enum ChannelType {
  // from ga:ChannelType
  CTypeInvalid = 0;
  CTypeAmuse = 4;
  CTypeYuyin = 7;
}

enum TimeFilterUnit {
  BY_DAY = 0;
  BY_WEEK = 1;
  BY_MONTH = 2;
}

// 请求结算状态 (实际转换为 0 未结算 1 已结算)
enum ReqSettleStatus {
  ReqSettleStatusALL = 0; // 所有状态
  ReqSettleStatusWait = 1; // 待结算
  ReqSettleStatusFinished = 2; // 已结算
}

// 查询娱乐房房间日环比数据
message GetAmuseRoomDayQoqInfoReq {
  uint32 guild_id = 1; // 工会ID
  uint32 room_id = 2;  // 房间ID
  int64 day = 3;  // 日期
}
message GetAmuseRoomDayQoqInfoRsp {
  int64 stat_time = 1; // 日期
  int64 income = 2;  // 当日收益
  int64 compare_stat_time = 3; // 对比日期
  int64 compare_income = 4; // 对比日收益
  float qoq = 5; // 环比
  uint32 room_id = 6; // 房间ID
}

// 获取娱乐房房间收益详情
message GetAmuseChannelDetailReq {
  uint32 guild_id = 1; // 工会ID
}
message GetAmuseChannelDetailRsp {
  repeated ChannelFee channel_fees = 1; // 房间收益
}

// 房间收益
message ChannelFee {
  uint32 room_id = 1; // 房间ID
  int64 fee = 2; // 收益
  float month_last_and_this_qoq = 3; // 本月/上月环比
}

//
message GetAmuseIncomeDetailReq {
  uint32 guild_id = 1; // 工会ID
}
message GetAmuseIncomeDetailRsp {
  int64 today_income = 1;
  int64 yesterday_income = 2;
  int64 this_month_income = 3;
  int64 last_month_income = 4;
  float day_qoq = 5;
  float month_qoq = 6;
  float yesterday_qoq = 7; // 昨日/上月昨日环比
  float month_last_and_this_qoq = 8; // 本月/上月环比
  int64 last_month_same_period = 9; // 上月同期值
  int64 month_six_income = 10; //6个月收益
  int64 this_month_extra_income = 11; // 本月额外收益
  int64 last_month_extra_income = 12; // 上月额外收益
  bool not_settle_amuse_extra = 13; // 不结算多人互动额外收益
}

// 增长详情
message StatIncomeInfo {
  int64 stat_time = 1;
  int64 members = 2;
  int64 incomes = 3;
  int64 fees = 4;
}

// 获取娱乐房今日收益详情
message GetAmuseGuildRoomIncomeListReq {
  uint32 guild_id = 1;
  uint32 offset = 2;
  uint32 limit = 3;
  RangeType range_type = 4;
  int64 begin_time = 5;
  int64 end_time = 6;
  uint32 room_id = 7;
}
message GetAmuseGuildRoomIncomeListRsp {
  int64 total_fees = 1;
  int64 total_incomes = 2;
  bool next_page = 3;
  repeated StatIncomeInfo stat_income_info = 4;
}

message SettlementYuyinGoldReq {
  bool certain = 1;
}

message SettlementYuyinGoldResp {}

// 主动推送语音额外收益统计报表
message GenYuyinExtraReportReq {
  bool is_daily = 1;
}
message GenYuyinExtraReportResp {}

message AwardGoldOrder {
  uint32 guild_id = 1;
  string order_id = 2;
  uint64 fee = 3;
  uint64 income = 4;
  uint32 channel_id = 5;
  uint32 target_uid = 6;
  uint32 paid_uid = 7;
  uint32 bought_time = 8;
  string extend = 9;
  SourceType source_type = 10;
  bool is_reconcile_order = 11;
  string deal_token = 12;
  bool is_ukw_paid = 13;
  bool is_ukw_target = 14;
  ChannelType channelType = 15;
  string ukw_paid_account = 16;
  string ukw_target_account = 17;
  GoldType gold_type = 18;
}

message BaseAnchorInfo {
  uint32 guild_id = 1;
  uint32 anchor_id = 2;
  uint32 date = 3;
  uint32 paid_uid_cnt = 4; // 消费人数
  uint32 paid_uid = 5;
  string paid_ukw_account = 6; // 神秘人ID

  uint64 fee = 10;
  uint64 income = 11;
  uint64 present_fee = 12;
  uint64 knight_fee = 14;
  uint64 interact_game_fee = 15; // 互动游戏流水
  uint64 interact_game_dur = 16; // 互动游戏时长
}

message BaseChannelInfo {
  uint32 guild_id = 1;
  uint32 channel_id = 2;
  uint32 date = 3;
  uint32 paid_uid_cnt = 4; // 消费人数
  uint32 paid_uid = 5;
  string paid_ukw_account = 7; // 神秘人ID

  uint64 fee = 10;
  uint64 income = 11;
  uint64 present_fee = 12;
  uint64 knight_fee = 14;
  uint64 werewolf_fee = 15;
  uint64 interact_game_fee = 16;
}

message BaseGuildInfo {
  uint32 guild_id = 1;
  uint32 date = 3;
  uint32 paid_uid_cnt = 4; // 消费人数

  uint64 fee = 10;
  uint64 income = 11;
  uint64 present_fee = 12;
  uint64 knight_fee = 14;
  uint64 werewolf_fee = 15;
  uint64 interact_game_fee = 16;
}

message GetGuildsYuyinAnchorStatReq {
  repeated uint32 guild_ids = 1;
  uint32 begin_time = 2;
  uint32 end_time = 3;
  ReqSettleStatus settle_status = 4; // 是否已结算
  TimeFilterUnit unit = 5;
  uint32 offset = 6;
  uint32 limit = 7;
}
message GetGuildsYuyinAnchorStatResp {
  repeated BaseAnchorInfo anchorList = 1;
}

message GetGuildsAmuseChannelStatReq {
  repeated uint32 guild_ids = 1;
  uint32 begin_time = 2;
  uint32 end_time = 3;
  ReqSettleStatus settle_status = 4; // 是否已结算
  TimeFilterUnit unit = 5;
  uint32 offset = 6;
  uint32 limit = 7;
}
message GetGuildsAmuseChannelStatResp {
  repeated BaseChannelInfo channelList = 1;
}

message GetGuildsInteractGameAnchorStatReq {
  repeated uint32 guild_ids = 1;
  uint32 begin_time = 2;
  uint32 end_time = 3;
  ReqSettleStatus settle_status = 4; // 是否已结算
  TimeFilterUnit unit = 5;
  uint32 offset = 6;
  uint32 limit = 7;
}
message GetGuildsInteractGameAnchorStatResp {
  repeated BaseAnchorInfo anchorList = 1;
}

message GetGuildsUnSettlementSummaryReq {
  repeated uint32 guild_ids = 1;
  bool include_today_paid_cnt = 2;
  bool include_month_paid_cnt = 3;
  ChannelType channel_type = 4; // 废弃 2023.07
  GoldType gold_type = 5; // 佣金类型
}
message GetGuildsUnSettlementSummaryRsp {
  uint64 today_fee = 1; // 总流水包含送礼与骑士
  uint64 today_income = 2;
  uint64 month_fee = 3;
  uint64 month_income = 4;
  uint64 today_paid_cnt = 5;
  uint64 month_paid_cnt = 6;
  uint64 this_month_extra_income = 7;
  uint64 last_month_extra_income = 8;
  bool not_settle_amuse_extra = 9; // 不结算多人互动额外收益
}

// 获取会长服务号语音直播数据详情
message GetYuyinIncomeDetailReq {
  uint32 uid = 1; // 用户Uid
  uint32 guild_id = 2; // 公会ID
}
message GetYuyinIncomeDetailRsp {
  DailyIncome daily_income = 2; // 收益日增长信息
  WeeklyIncome weekly_income = 3; // 收益周增长信息
  MonthlyIncome monthly_income = 4; // 收益月增长信息
}

// DailyIncome 收益日增长信息
message DailyIncome {
  uint64 today_income = 1; // 今日增长
  uint64 yesterday_income = 2; // 昨日增长
  float daily_qoq = 3; // 上月同日环比增长率
}

// WeeklyIncome 收益周增长信息
message WeeklyIncome {
  uint64 this_week_income = 1; // 本周增长
  uint64 last_week_income = 2; // 上一周增长
  int64 current_time = 3; // 当前时间
  int64 week_start_time = 4; // 周开始时间
  int64 week_end_time = 5; // 周结束时间
}

// MonthlyIncome 收益月增长信息
message MonthlyIncome {
  uint64 this_month_income = 1; // 该月收入
  uint64 last_month_income = 2; // 上一个月收入
  float monthly_qoq = 3; // 上月环比增长率
  uint64 same_last_month_income = 4; // 上月同期收入
  uint64 last_month_valid_income = 5; // 上月总收益
  uint64 last_six_month_income = 6; // 最近六个月的总收益
}

// 根据截止时间获取月收益日趋势列表
message GetIncomeTrendListReq {
  uint32 uid = 1; // 用户ID
  uint32 guild_id = 2; // 公会ID
  int64 end_time = 3; // 截止计算时间
  ChannelType channel_type = 4; // 房间类型（废弃 2023.07）
  GoldType gold_type = 5; // 佣金类型
}
message GetIncomeTrendListRsp {
  repeated TrendInfo list = 1; // 趋势值信息
}

// RangeType 获取类型枚举值
enum RangeType {
  DAY_RANGE_TYPE = 0;
  MONTH_RANGE_TYPE = 1;
  DAY_RANGE_TYPE_FOR_TEST = 10001; // 仅用于测试，显示日榜当天值
}

// TrendInfo 趋势值信息
message TrendInfo {
  string key = 1;  // 自然日
  uint64 value = 2; // 增长量
}

// 获取消费排行榜
message GetConsumeRankReq {
  RangeType consume_type = 1; // 排行榜类型
  uint32 guild_id = 2; // 公会ID
  ChannelType channel_type = 3; // 获取排行榜房间类型（废弃 2023.07）
  GoldType gold_type = 4; // 佣金类型
}
message GetConsumeRankRsp {
  repeated ConsumeRankItem consume_rank_list = 1; // 消费排行列表
}

// ConsumeRankItem 用户消费排行榜信息
message ConsumeRankItem {
  string alias = 1; // 用户TTId
  string nick_name = 2; // 用户昵称
  string account = 3; // 用户账号
  int64 consume = 4; // 消费金额
}

// 获取该日收益情况
message GetDayIncomeReq {
  uint32 uid = 1; // 用户uid
  uint32 guild_id = 2; // 公会ID
  ChannelType channel_type = 3; // 房间类型（废弃 2023.07）
  GoldType gold_type = 4; // 佣金类型
}
message GetDayIncomeRsp {
  DailyIncome daily_income = 1; // 收益日增长信息
}

// 获取语音直播该周收益情况
message GetYuyinWeekIncomeReq {
  uint32 uid = 1; // 用户uid
  uint32 guild_id = 2; // 公会ID
}
message GetYuyinWeekIncomeRsp {
  WeeklyIncome weekly_income = 1; // 收益周增长信息
}

// 获取互动游戏该周收益情况
message GetInteractGameWeekIncomeReq {
  uint32 uid = 1; // 用户uid
  uint32 guild_id = 2; // 公会ID
}
message GetInteractGameWeekIncomeRsp {
  WeeklyIncome weekly_income = 1; // 收益周增长信息
}

// 获取该月收益情况
message GetMonthIncomeReq {
  uint32 uid = 1; // 用户uid
  uint32 guild_id = 2; // 公会ID
  ChannelType channel_type = 3; // 房间类型（废弃 2023.07）
  GoldType gold_type = 4; // 佣金类型
}
message GetMonthIncomeRsp {
  MonthlyIncome monthly_income = 1; // 收益月增长信息
}

// 获取语音直播按月份额外收益
message GetGuildYuyinExtraIncomeReq {
  uint32 uid = 1; // 用户ID
  repeated uint32 guild_ids = 2; // 公会列表
  int64  begin_time = 3; // 计算开始时间
  int64  end_time = 4; // 计算结束时间
  ReqSettleStatus settle_status = 5; // 是否已结算
}
message GetGuildYuyinExtraIncomeRsp {
  repeated ExtraIncome list = 1; // 额外收益列表
}

// ExtraIncome 额外收益记录
// 额外收益+当月公会任务奖励分成比例 的记录
message ExtraIncome {
  uint64 month_record_income = 1;
  uint32 extra_income_ratio = 2;
  string key = 3;
  string extra_income_ratio_v2 = 4;
  uint32 guild_id = 5;
}

// 获取语音直播工会任务列表
message GetGuildYuyinTaskListReq {
  uint32 uid = 1; // 用户uid
  uint32 guild_id = 2; // 公会ID
}
message GetGuildYuyinTaskListRsp {
  repeated GuildTaskInfo list = 1; // 公会任务列表
  int64 remain_time = 2; // 剩余时间
  string buff_total = 3; // 比例数目
  repeated GuildTaskDetailInfo task_detail = 4;  // 任务详情列表
  uint32 is_new_guild = 5;  // 是否是新工会：0：否 1:是
  string new_guild_period = 6; // 新工会生效月份
}

// GuildTaskInfo 公会任务信息
message GuildTaskInfo {
  uint64 value = 1; // 当前情况
  string ratio = 2; // 当前比例
  uint32 level = 3; // 满足等级
}

// 公会任务比例信息
message GuildTaskRatioInfo {
  uint64 value = 1;       // 任务值
  string ratio = 2;       // 任务比例
  string extra_ratio = 3; // 额外增加比例
  uint32 level = 4;       // 等级
}

// 公会任务详情
message GuildTaskDetailInfo {
  string name = 1;    // 任务名称
  repeated GuildTaskRatioInfo ratio_list = 2; // 任务比例列表
}

message GetGuildDayIncomeListReq {
  uint32 guild_id = 1;
  uint64 datetime = 2;
  uint32 offset = 3;
  uint32 limit = 4;
}
message GetYuyinGuildDayIncomeListResp {
  repeated BaseAnchorInfo anchor_list = 1;
}
message GetAmuseGuildDayIncomeListResp {
  repeated BaseChannelInfo channel_list = 1;
}
message GetInteractGameDayIncomeListResp {
  repeated BaseAnchorInfo anchor_list = 1;
}

message GetGuildMonthIncomeListReq {
  uint32 guild_id = 1;
  uint64 datetime = 2;
  uint32 offset = 3;
  uint32 limit = 4;
}
message GetGuildMonthIncomeListResp {
  repeated BaseGuildInfo guild_list = 1;
}

message GetInteractGameMonthIncomeListResp {
  repeated BaseAnchorInfo anchor_list = 1;
}

message GetGuildMonthMemberListReq {
  ChannelType channel_type = 1; // （废弃 2023.07）
  enum QueryType {
    UNKNOWN = 0;
    ANCHOR_ID = 1;
    PAID_UID = 2;
  }
  uint32 guild_id = 2;
  uint64 datetime = 3;
  uint32 offset = 4;
  uint32 limit = 5;
  QueryType type = 6;
  GoldType gold_type = 7; // 佣金类型
}
message GetGuildMonthMemberListResp {
  // 根据房间类型返回其中之一
  repeated BaseAnchorInfo anchor_list = 1;
  repeated BaseChannelInfo channel_list = 2;
}

message SearchYuyinGuildDetailReq {
  uint32 guild_id = 1;
  uint32 offset = 3;
  uint32 limit = 4;
  uint64 start = 5;
  uint64 end = 6;
  uint32 anchor_id = 7;
  uint32 paid_uid = 8;
}
message SearchYuyinGuildDetailResp {
  repeated BaseAnchorInfo detail_list = 1;
}

message SearchAmuseGuildDetailReq {
  uint32 guild_id = 1;
  uint32 offset = 3;
  uint32 limit = 4;
  uint64 start = 5;
  uint64 end = 6;
  uint32 channel_id = 7;
  uint32 paid_uid = 8;
}
message SearchAmuseGuildDetailResp {
  repeated BaseChannelInfo detail_list = 1;
}

message GetGuildDayQoqReq {
  uint32 guild_id = 1;
  uint64 day_time = 2;
  ChannelType channel_type = 3; // （废弃 2023.07）
  GoldType gold_type = 4; // 佣金类型
}
message GetGuildDayQoqResp {
  uint64 day_time = 1;
  uint64 fee = 2;
  uint64 income = 3;
  uint64 compare_time = 4;
  uint64 compare_fee = 5;
  uint64 compare_income = 6;
  double qoq = 7;
}

message AmuseGuildChannelIncomeListReq {
  uint32 guild_id = 1;
  repeated uint32 channel_id = 2; // 为空返回所有房间
  uint32 offset = 3;
  uint32 limit = 4;
}

message AmuseGuildChannelIncomeListResp {
  message lItem {
    uint32 channel_id = 1;
    uint64 fee = 2; // 当月流水
    uint64 same_period_fee = 3; // 上月同期流水
    float same_period_qoq = 4; // 同期环比
    uint64 last_month_fee = 5; // 上月流水
  }
  repeated lItem channel_list = 1;
  uint32 total = 2;
}

message GetAmuseGuildIdsByRangeReq {
  uint32 start_time = 2;
  uint32 end_time = 3;
}
message GetAmuseGuildIdsByRangeResp {
  repeated uint32 guild_ids = 1;
}

message AmuseExtraIncomeSettleItem {
  uint32 master_uid = 1; // 对公会长UID
  uint32 master_guild_id = 2; // 对公会长公会ID
  repeated Guild guild_list = 3;
  uint64 stat_period_fee = 4; // 统计时段公会流水（T豆）
  uint64 compare_period_fee = 5; // 对比时段公会流水（T豆）
  string grow_rate = 6; // 增长率
  string stat_period_cny = 7; // 统计时段公会流水（元）
  string compare_period_cny = 8; // 对比时段公会流水（元）

  message Guild {
    uint32 guild_id = 1; // 公会ID
    uint32 guild_display_id = 2; // 公会ID
    string guild_name = 3;
    repeated Channel channel_list = 7;
    uint64 settle_money = 9; // 公会结算金额（各房间之和）
    string settle_money_cny = 10; // 公会结算金额（元）
  }

  message Channel {
    uint32 channel_id = 1;
    uint32 channel_display_id = 2;
    string channel_name = 3;
    string channel_tag = 4; // 房间标签
    uint64 stat_period_fee = 5; // 统计时段房间流水（T豆）
    uint64 compare_period_fee = 6; // 对比时段房间流水（T豆）
    string grow_rate = 7; // 增长率
    string settle_rate = 8; // 房间结算比例
    uint64 settle_money = 9; // 房间结算金额（分）
    string settle_money_cny = 10; // 房间结算金额（元）
    string stat_period_cny = 11; // 统计时段房间流水（元）
    string compare_period_cny = 12; // 对比时段房间流水（元）
    string channel_view_id = 13; // 房间长号
  }
}

message GetAmuseExtraIncomeSettleListReq {
  uint32 guild_id = 1;
  uint32 start_time = 2;
  uint32 end_time = 3;
  uint32 compare_start_time = 4;
  uint32 compare_end_time = 5;
  uint32 master_uid = 7; // 对公会长UID
}

message GetAmuseExtraIncomeSettleListResp {
  repeated AmuseExtraIncomeSettleItem list = 1;
  uint64 total_fee = 2; // 合计（T豆）
  string total_fee_cny = 3; // 合计（元）
}

message GetAmuseSettleGuildListReq {
  uint32 keyword = 1; // 公会ID/公会短号/对公会长UID，默认返回已结算公会
  uint32 offset = 2;
  uint32 limit = 3;
}
message GetAmuseSettleGuildListResp {
  repeated Guild guild_list = 1;
  uint32 total = 2;
  message Guild {
    uint32 master_uid = 1;
    uint32 guild_id = 2;
    uint32 guild_display_id = 3;
    string guild_name = 4;
    uint32 channel_num = 5;
    uint32 settle_status = 6; // 1 结算 2 不结算
  }
}

message GetAmuseSettleChannelListReq {
  uint32 guild_id = 1;
  uint32 channel_id = 2; // 房间ID、靓号
}
message GetAmuseSettleChannelListResp {
  repeated Channel channel_list = 1;
  message Channel {
    uint32 channel_id = 2;
    uint32 channel_display_id = 3;
    string channel_name = 4;
    uint32 settle_status = 5; // 1 结算 2 不结算
    string channel_view_id = 6; // 房间长号
  }
}

message SetAmuseSettleGuildReq {
  uint32 guild_id = 1;
  uint32 settle_status = 5; // 1 结算 2 不结算
}
message SetAmuseSettleGuildResp {
}

message SetAmuseSettleChannelReq {
  uint32 channel_id = 1;
  uint32 settle_status = 5; // 1 结算 2 不结算
}
message SetAmuseSettleChannelResp {
}

message GetAmuseExtraDetailReq {
  repeated uint32 guild_ids = 1;
  uint32 yearmonth = 2;
}
message GetAmuseExtraDetailResp {
  uint32 year_month = 1; // 202201
  uint64 this_month_fee = 2; // T豆流水
  uint64 last_month_fee = 3;
  uint64 this_month_income = 4; // 本月收益（金钻）
  string this_month_income_cny = 5; // 本月收益（元）
  uint64 prepaid_money = 6; // 预付金额（分）
  string prepaid_money_cny = 7; // 预付金额（元）
  string remark = 8; // 备注
  string grow_rate = 9; // 增长率
  repeated ChannelItem channel_list = 10;

  message ChannelItem {
    uint32 channel_id = 1;
    string channel_tag = 4; // 房间标签
    uint64 this_month_fee = 5; // T豆流水
    uint64 last_month_fee = 6;
    string settlement_rate = 7;
    uint64 this_month_income = 8; // 本月收益（金钻）
    uint32 guild_id = 9;
    string grow_rate = 10;
  }
}

// 获取会长服务号互动游戏收益
message GetInteractGameIncomeDetailReq {
  uint32 uid = 1; // 用户Uid
  uint32 guild_id = 2; // 公会ID
}
message GetInteractGameIncomeDetailRsp {
  DailyIncome daily_income = 2; // 当日收益
  WeeklyIncome weekly_income = 3; // 本周收益
  MonthlyIncome monthly_income = 4; // 本月收益
  MonthlyIncome monthly_extra_income = 5; // 额外奖励收益
}


message GetGuildInteractGamePerReq {
  uint32 guild_id = 1; // 公会ID
}
message GetGuildInteractGamePerResp {
  bool is_show=1;
}


message GetInteractGameExtraIncomeReq {
  repeated uint32 guild_ids = 1;
  uint32 month_time=2;
}
message GetInteractGameExtraIncomeResp {
  InteractGameExtraDetail info=1;
}
message InteractGameExtraDetail {
  uint32 month_time=1;
  uint64 game_month_total_fee =2; // 月互动游戏总流水 T豆
  uint64 game_month_extra_income =3; // 月互动游戏额外奖励 金钻
  repeated InteractGameExtraAnchorInfo anchor_info_list=4;
}
message InteractGameExtraAnchorInfo {
  uint32 anchor_uid = 1;
  uint64 game_month_fee=2; // 月互动游戏流水 T豆
  float game_income_rate=3; // 收益比率
  uint64 game_income=4; // 收益 金钻
}
