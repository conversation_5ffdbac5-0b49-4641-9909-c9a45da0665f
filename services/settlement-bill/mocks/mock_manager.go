// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/services/settlement-bill/manager (interfaces: IManager)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	oa "golang.52tt.com/pkg/oa"
	settlement "golang.52tt.com/pkg/settlement"
	settlement_bill "golang.52tt.com/protocol/services/settlement-bill"
	model "golang.52tt.com/services/settlement-bill/model"
	mysql "golang.52tt.com/services/settlement-bill/mysql"
	gorm "gorm.io/gorm"
)

// MockIManager is a mock of IManager interface.
type MockIManager struct {
	ctrl     *gomock.Controller
	recorder *MockIManagerMockRecorder
}

// MockIManagerMockRecorder is the mock recorder for MockIManager.
type MockIManagerMockRecorder struct {
	mock *MockIManager
}

// NewMockIManager creates a new mock instance.
func NewMockIManager(ctrl *gomock.Controller) *MockIManager {
	mock := &MockIManager{ctrl: ctrl}
	mock.recorder = &MockIManagerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIManager) EXPECT() *MockIManagerMockRecorder {
	return m.recorder
}

// AssociateReceipt mocks base method.
func (m *MockIManager) AssociateReceipt(arg0 context.Context, arg1 *settlement_bill.AssociateReceiptReq) (*settlement_bill.AssociateReceiptResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AssociateReceipt", arg0, arg1)
	ret0, _ := ret[0].(*settlement_bill.AssociateReceiptResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AssociateReceipt indicates an expected call of AssociateReceipt.
func (mr *MockIManagerMockRecorder) AssociateReceipt(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AssociateReceipt", reflect.TypeOf((*MockIManager)(nil).AssociateReceipt), arg0, arg1)
}

// BatchGetTaxRate mocks base method.
func (m *MockIManager) BatchGetTaxRate(arg0 context.Context, arg1 *settlement_bill.BatchGetTaxRateReq) (*settlement_bill.BatchGetTaxRateResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetTaxRate", arg0, arg1)
	ret0, _ := ret[0].(*settlement_bill.BatchGetTaxRateResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetTaxRate indicates an expected call of BatchGetTaxRate.
func (mr *MockIManagerMockRecorder) BatchGetTaxRate(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetTaxRate", reflect.TypeOf((*MockIManager)(nil).BatchGetTaxRate), arg0, arg1)
}

// CheckOAInstIdStatus mocks base method.
func (m *MockIManager) CheckOAInstIdStatus(arg0 context.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckOAInstIdStatus", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// CheckOAInstIdStatus indicates an expected call of CheckOAInstIdStatus.
func (mr *MockIManagerMockRecorder) CheckOAInstIdStatus(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckOAInstIdStatus", reflect.TypeOf((*MockIManager)(nil).CheckOAInstIdStatus), arg0)
}

// ConfirmWithdraw mocks base method.
func (m *MockIManager) ConfirmWithdraw(arg0 context.Context, arg1 *settlement_bill.ConfirmWithdrawReq) (*settlement_bill.ConfirmWithdrawResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ConfirmWithdraw", arg0, arg1)
	ret0, _ := ret[0].(*settlement_bill.ConfirmWithdrawResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ConfirmWithdraw indicates an expected call of ConfirmWithdraw.
func (mr *MockIManagerMockRecorder) ConfirmWithdraw(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ConfirmWithdraw", reflect.TypeOf((*MockIManager)(nil).ConfirmWithdraw), arg0, arg1)
}

// ConfirmWithdrawBefore mocks base method.
func (m *MockIManager) ConfirmWithdrawBefore(arg0 context.Context, arg1 *settlement_bill.ConfirmWithdrawReq) (*mysql.SettlementBill, *oa.FileInfo, *model.SettlementBillPrintInfo, uint64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ConfirmWithdrawBefore", arg0, arg1)
	ret0, _ := ret[0].(*mysql.SettlementBill)
	ret1, _ := ret[1].(*oa.FileInfo)
	ret2, _ := ret[2].(*model.SettlementBillPrintInfo)
	ret3, _ := ret[3].(uint64)
	ret4, _ := ret[4].(error)
	return ret0, ret1, ret2, ret3, ret4
}

// ConfirmWithdrawBefore indicates an expected call of ConfirmWithdrawBefore.
func (mr *MockIManagerMockRecorder) ConfirmWithdrawBefore(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ConfirmWithdrawBefore", reflect.TypeOf((*MockIManager)(nil).ConfirmWithdrawBefore), arg0, arg1)
}

// CreatePrivateWithdrawBill mocks base method.
func (m *MockIManager) CreatePrivateWithdrawBill(arg0 context.Context, arg1 *mysql.SettlePrivateWithdrawBill, arg2 func() (int, error)) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreatePrivateWithdrawBill", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreatePrivateWithdrawBill indicates an expected call of CreatePrivateWithdrawBill.
func (mr *MockIManagerMockRecorder) CreatePrivateWithdrawBill(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreatePrivateWithdrawBill", reflect.TypeOf((*MockIManager)(nil).CreatePrivateWithdrawBill), arg0, arg1, arg2)
}

// CreateSettlementBill mocks base method.
func (m *MockIManager) CreateSettlementBill(arg0 context.Context, arg1 *settlement_bill.CreateSettlementBillReq) (*settlement_bill.CreateSettlementBillResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateSettlementBill", arg0, arg1)
	ret0, _ := ret[0].(*settlement_bill.CreateSettlementBillResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateSettlementBill indicates an expected call of CreateSettlementBill.
func (mr *MockIManagerMockRecorder) CreateSettlementBill(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateSettlementBill", reflect.TypeOf((*MockIManager)(nil).CreateSettlementBill), arg0, arg1)
}

// CreateSettlementBillBefore mocks base method.
func (m *MockIManager) CreateSettlementBillBefore(arg0 context.Context, arg1 *settlement_bill.CreateSettlementBillReq) (*model.SettlementMeta, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateSettlementBillBefore", arg0, arg1)
	ret0, _ := ret[0].(*model.SettlementMeta)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateSettlementBillBefore indicates an expected call of CreateSettlementBillBefore.
func (mr *MockIManagerMockRecorder) CreateSettlementBillBefore(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateSettlementBillBefore", reflect.TypeOf((*MockIManager)(nil).CreateSettlementBillBefore), arg0, arg1)
}

// CreateSettlementBillPrintInfo mocks base method.
func (m *MockIManager) CreateSettlementBillPrintInfo(arg0 context.Context, arg1 *mysql.SettlementBill, arg2 *model.CorporationInfo) (*model.SettlementBillPrintInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateSettlementBillPrintInfo", arg0, arg1, arg2)
	ret0, _ := ret[0].(*model.SettlementBillPrintInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateSettlementBillPrintInfo indicates an expected call of CreateSettlementBillPrintInfo.
func (mr *MockIManagerMockRecorder) CreateSettlementBillPrintInfo(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateSettlementBillPrintInfo", reflect.TypeOf((*MockIManager)(nil).CreateSettlementBillPrintInfo), arg0, arg1, arg2)
}

// GenPDF mocks base method.
func (m *MockIManager) GenPDF(arg0 context.Context, arg1 *mysql.SettlementBill) (string, *model.SettlementBillPrintInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GenPDF", arg0, arg1)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(*model.SettlementBillPrintInfo)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GenPDF indicates an expected call of GenPDF.
func (mr *MockIManagerMockRecorder) GenPDF(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GenPDF", reflect.TypeOf((*MockIManager)(nil).GenPDF), arg0, arg1)
}

// GenSettleBillPdf mocks base method.
func (m *MockIManager) GenSettleBillPdf(arg0 context.Context, arg1 *settlement_bill.GenSettleBillPdfReq) (*settlement_bill.GenSettleBillPdfResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GenSettleBillPdf", arg0, arg1)
	ret0, _ := ret[0].(*settlement_bill.GenSettleBillPdfResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GenSettleBillPdf indicates an expected call of GenSettleBillPdf.
func (mr *MockIManagerMockRecorder) GenSettleBillPdf(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GenSettleBillPdf", reflect.TypeOf((*MockIManager)(nil).GenSettleBillPdf), arg0, arg1)
}

// GetAmuseExtraIncomePrepaid mocks base method.
func (m *MockIManager) GetAmuseExtraIncomePrepaid(arg0 context.Context, arg1 *settlement_bill.GetAmuseExtraIncomePrepaidReq) (*settlement_bill.GetAmuseExtraIncomePrepaidResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAmuseExtraIncomePrepaid", arg0, arg1)
	ret0, _ := ret[0].(*settlement_bill.GetAmuseExtraIncomePrepaidResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAmuseExtraIncomePrepaid indicates an expected call of GetAmuseExtraIncomePrepaid.
func (mr *MockIManagerMockRecorder) GetAmuseExtraIncomePrepaid(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAmuseExtraIncomePrepaid", reflect.TypeOf((*MockIManager)(nil).GetAmuseExtraIncomePrepaid), arg0, arg1)
}

// GetAnchorScoreWithdrawRecords mocks base method.
func (m *MockIManager) GetAnchorScoreWithdrawRecords(arg0 context.Context, arg1 *settlement_bill.GetAnchorScoreWithdrawRecordsReq) (*settlement_bill.GetAnchorScoreWithdrawRecordsResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAnchorScoreWithdrawRecords", arg0, arg1)
	ret0, _ := ret[0].(*settlement_bill.GetAnchorScoreWithdrawRecordsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAnchorScoreWithdrawRecords indicates an expected call of GetAnchorScoreWithdrawRecords.
func (mr *MockIManagerMockRecorder) GetAnchorScoreWithdrawRecords(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAnchorScoreWithdrawRecords", reflect.TypeOf((*MockIManager)(nil).GetAnchorScoreWithdrawRecords), arg0, arg1)
}

// GetAssociatedBillItems mocks base method.
func (m *MockIManager) GetAssociatedBillItems(arg0 context.Context, arg1 *settlement_bill.GetAssociatedBillItemsReq) (*settlement_bill.GetAssociatedBillItemsResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAssociatedBillItems", arg0, arg1)
	ret0, _ := ret[0].(*settlement_bill.GetAssociatedBillItemsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAssociatedBillItems indicates an expected call of GetAssociatedBillItems.
func (mr *MockIManagerMockRecorder) GetAssociatedBillItems(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAssociatedBillItems", reflect.TypeOf((*MockIManager)(nil).GetAssociatedBillItems), arg0, arg1)
}

// GetBillLastBalance mocks base method.
func (m *MockIManager) GetBillLastBalance(arg0 context.Context, arg1 *settlement_bill.CreateSettlementBillReq, arg2 *mysql.SettlementBill) (uint64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBillLastBalance", arg0, arg1, arg2)
	ret0, _ := ret[0].(uint64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBillLastBalance indicates an expected call of GetBillLastBalance.
func (mr *MockIManagerMockRecorder) GetBillLastBalance(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBillLastBalance", reflect.TypeOf((*MockIManager)(nil).GetBillLastBalance), arg0, arg1, arg2)
}

// GetConfigCenter mocks base method.
func (m *MockIManager) GetConfigCenter() settlement.IConfigCenter {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetConfigCenter")
	ret0, _ := ret[0].(settlement.IConfigCenter)
	return ret0
}

// GetConfigCenter indicates an expected call of GetConfigCenter.
func (mr *MockIManagerMockRecorder) GetConfigCenter() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetConfigCenter", reflect.TypeOf((*MockIManager)(nil).GetConfigCenter))
}

// GetCorporationInfo mocks base method.
func (m *MockIManager) GetCorporationInfo(arg0 context.Context, arg1 uint32) (*model.CorporationInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCorporationInfo", arg0, arg1)
	ret0, _ := ret[0].(*model.CorporationInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCorporationInfo indicates an expected call of GetCorporationInfo.
func (mr *MockIManagerMockRecorder) GetCorporationInfo(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCorporationInfo", reflect.TypeOf((*MockIManager)(nil).GetCorporationInfo), arg0, arg1)
}

// GetDeductMoneyDetailByBillId mocks base method.
func (m *MockIManager) GetDeductMoneyDetailByBillId(arg0 context.Context, arg1 *settlement_bill.GetDeductMoneyListByBillIdReq) (*settlement_bill.GetDeductMoneyListByBillIdResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetDeductMoneyDetailByBillId", arg0, arg1)
	ret0, _ := ret[0].(*settlement_bill.GetDeductMoneyListByBillIdResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetDeductMoneyDetailByBillId indicates an expected call of GetDeductMoneyDetailByBillId.
func (mr *MockIManagerMockRecorder) GetDeductMoneyDetailByBillId(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDeductMoneyDetailByBillId", reflect.TypeOf((*MockIManager)(nil).GetDeductMoneyDetailByBillId), arg0, arg1)
}

// GetExtraIncomeDetailChannelSubsidy mocks base method.
func (m *MockIManager) GetExtraIncomeDetailChannelSubsidy(arg0 context.Context, arg1 *settlement_bill.GetExtraIncomeDetailReq) (*settlement_bill.GetExtraIncomeDetailChannelSubsidyResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetExtraIncomeDetailChannelSubsidy", arg0, arg1)
	ret0, _ := ret[0].(*settlement_bill.GetExtraIncomeDetailChannelSubsidyResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetExtraIncomeDetailChannelSubsidy indicates an expected call of GetExtraIncomeDetailChannelSubsidy.
func (mr *MockIManagerMockRecorder) GetExtraIncomeDetailChannelSubsidy(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetExtraIncomeDetailChannelSubsidy", reflect.TypeOf((*MockIManager)(nil).GetExtraIncomeDetailChannelSubsidy), arg0, arg1)
}

// GetExtraIncomeDetailDeduct mocks base method.
func (m *MockIManager) GetExtraIncomeDetailDeduct(arg0 context.Context, arg1 *settlement_bill.GetExtraIncomeDetailReq) (*settlement_bill.GetExtraIncomeDetailDeductResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetExtraIncomeDetailDeduct", arg0, arg1)
	ret0, _ := ret[0].(*settlement_bill.GetExtraIncomeDetailDeductResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetExtraIncomeDetailDeduct indicates an expected call of GetExtraIncomeDetailDeduct.
func (mr *MockIManagerMockRecorder) GetExtraIncomeDetailDeduct(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetExtraIncomeDetailDeduct", reflect.TypeOf((*MockIManager)(nil).GetExtraIncomeDetailDeduct), arg0, arg1)
}

// GetExtraIncomeDetailDeepCoop mocks base method.
func (m *MockIManager) GetExtraIncomeDetailDeepCoop(arg0 context.Context, arg1 *settlement_bill.GetExtraIncomeDetailReq) (*settlement_bill.GetExtraIncomeDetailDeepCoopResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetExtraIncomeDetailDeepCoop", arg0, arg1)
	ret0, _ := ret[0].(*settlement_bill.GetExtraIncomeDetailDeepCoopResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetExtraIncomeDetailDeepCoop indicates an expected call of GetExtraIncomeDetailDeepCoop.
func (mr *MockIManagerMockRecorder) GetExtraIncomeDetailDeepCoop(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetExtraIncomeDetailDeepCoop", reflect.TypeOf((*MockIManager)(nil).GetExtraIncomeDetailDeepCoop), arg0, arg1)
}

// GetExtraIncomeDetailNewGuildSubsidy mocks base method.
func (m *MockIManager) GetExtraIncomeDetailNewGuildSubsidy(arg0 context.Context, arg1 *settlement_bill.GetExtraIncomeDetailReq) (*settlement_bill.GetExtraIncomeDetailNewGuildSubsidyResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetExtraIncomeDetailNewGuildSubsidy", arg0, arg1)
	ret0, _ := ret[0].(*settlement_bill.GetExtraIncomeDetailNewGuildSubsidyResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetExtraIncomeDetailNewGuildSubsidy indicates an expected call of GetExtraIncomeDetailNewGuildSubsidy.
func (mr *MockIManagerMockRecorder) GetExtraIncomeDetailNewGuildSubsidy(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetExtraIncomeDetailNewGuildSubsidy", reflect.TypeOf((*MockIManager)(nil).GetExtraIncomeDetailNewGuildSubsidy), arg0, arg1)
}

// GetExtraIncomeRecordList mocks base method.
func (m *MockIManager) GetExtraIncomeRecordList(arg0 context.Context, arg1 *settlement_bill.GetExtraIncomeRecordListReq) (*settlement_bill.GetExtraIncomeRecordListResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetExtraIncomeRecordList", arg0, arg1)
	ret0, _ := ret[0].(*settlement_bill.GetExtraIncomeRecordListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetExtraIncomeRecordList indicates an expected call of GetExtraIncomeRecordList.
func (mr *MockIManagerMockRecorder) GetExtraIncomeRecordList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetExtraIncomeRecordList", reflect.TypeOf((*MockIManager)(nil).GetExtraIncomeRecordList), arg0, arg1)
}

// GetGuildTaxRate mocks base method.
func (m *MockIManager) GetGuildTaxRate(arg0 context.Context, arg1 *settlement_bill.GetGuildTaxRateReq) (*settlement_bill.GetGuildTaxRateResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGuildTaxRate", arg0, arg1)
	ret0, _ := ret[0].(*settlement_bill.GetGuildTaxRateResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGuildTaxRate indicates an expected call of GetGuildTaxRate.
func (mr *MockIManagerMockRecorder) GetGuildTaxRate(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGuildTaxRate", reflect.TypeOf((*MockIManager)(nil).GetGuildTaxRate), arg0, arg1)
}

// GetMonthsBySettleBillId mocks base method.
func (m *MockIManager) GetMonthsBySettleBillId(arg0 context.Context, arg1 *settlement_bill.GetMonthsBySettleBillIdReq) (*settlement_bill.GetMonthsBySettleBillIdResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMonthsBySettleBillId", arg0, arg1)
	ret0, _ := ret[0].(*settlement_bill.GetMonthsBySettleBillIdResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMonthsBySettleBillId indicates an expected call of GetMonthsBySettleBillId.
func (mr *MockIManagerMockRecorder) GetMonthsBySettleBillId(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMonthsBySettleBillId", reflect.TypeOf((*MockIManager)(nil).GetMonthsBySettleBillId), arg0, arg1)
}

// GetReceiptBillList mocks base method.
func (m *MockIManager) GetReceiptBillList(arg0 context.Context, arg1 *settlement_bill.GetReceiptBillListReq) (*settlement_bill.GetReceiptBillListResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetReceiptBillList", arg0, arg1)
	ret0, _ := ret[0].(*settlement_bill.GetReceiptBillListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetReceiptBillList indicates an expected call of GetReceiptBillList.
func (mr *MockIManagerMockRecorder) GetReceiptBillList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetReceiptBillList", reflect.TypeOf((*MockIManager)(nil).GetReceiptBillList), arg0, arg1)
}

// GetReceiptList mocks base method.
func (m *MockIManager) GetReceiptList(arg0 context.Context, arg1 *settlement_bill.GetReceiptListReq) (*settlement_bill.GetReceiptListResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetReceiptList", arg0, arg1)
	ret0, _ := ret[0].(*settlement_bill.GetReceiptListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetReceiptList indicates an expected call of GetReceiptList.
func (mr *MockIManagerMockRecorder) GetReceiptList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetReceiptList", reflect.TypeOf((*MockIManager)(nil).GetReceiptList), arg0, arg1)
}

// GetReceiptTotalAmount mocks base method.
func (m *MockIManager) GetReceiptTotalAmount(arg0 context.Context, arg1 *settlement_bill.GetReceiptTotalAmountReq) (*settlement_bill.GetReceiptTotalAmountResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetReceiptTotalAmount", arg0, arg1)
	ret0, _ := ret[0].(*settlement_bill.GetReceiptTotalAmountResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetReceiptTotalAmount indicates an expected call of GetReceiptTotalAmount.
func (mr *MockIManagerMockRecorder) GetReceiptTotalAmount(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetReceiptTotalAmount", reflect.TypeOf((*MockIManager)(nil).GetReceiptTotalAmount), arg0, arg1)
}

// GetSettlementBill mocks base method.
func (m *MockIManager) GetSettlementBill(arg0 context.Context, arg1 *settlement_bill.GetSettlementBillReq) (*settlement_bill.GetSettlementBillResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSettlementBill", arg0, arg1)
	ret0, _ := ret[0].(*settlement_bill.GetSettlementBillResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSettlementBill indicates an expected call of GetSettlementBill.
func (mr *MockIManagerMockRecorder) GetSettlementBill(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSettlementBill", reflect.TypeOf((*MockIManager)(nil).GetSettlementBill), arg0, arg1)
}

// GetSettlementBillDetailByInstId mocks base method.
func (m *MockIManager) GetSettlementBillDetailByInstId(arg0 context.Context, arg1 *settlement_bill.GetSettlementBillDetailByInstIdReq) (*settlement_bill.GetSettlementBillDetailByInstIdResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSettlementBillDetailByInstId", arg0, arg1)
	ret0, _ := ret[0].(*settlement_bill.GetSettlementBillDetailByInstIdResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSettlementBillDetailByInstId indicates an expected call of GetSettlementBillDetailByInstId.
func (mr *MockIManagerMockRecorder) GetSettlementBillDetailByInstId(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSettlementBillDetailByInstId", reflect.TypeOf((*MockIManager)(nil).GetSettlementBillDetailByInstId), arg0, arg1)
}

// GetSettlementBillWaitReceipt mocks base method.
func (m *MockIManager) GetSettlementBillWaitReceipt(arg0 context.Context, arg1 *settlement_bill.GetSettlementBillWaitReceiptReq) (*settlement_bill.GetSettlementBillWaitReceiptResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSettlementBillWaitReceipt", arg0, arg1)
	ret0, _ := ret[0].(*settlement_bill.GetSettlementBillWaitReceiptResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSettlementBillWaitReceipt indicates an expected call of GetSettlementBillWaitReceipt.
func (mr *MockIManagerMockRecorder) GetSettlementBillWaitReceipt(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSettlementBillWaitReceipt", reflect.TypeOf((*MockIManager)(nil).GetSettlementBillWaitReceipt), arg0, arg1)
}

// GetSettlementBillWaitWithdraw mocks base method.
func (m *MockIManager) GetSettlementBillWaitWithdraw(arg0 context.Context, arg1 *settlement_bill.GetSettlementBillWaitWithdrawReq) (*settlement_bill.GetSettlementBillWaitWithdrawResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSettlementBillWaitWithdraw", arg0, arg1)
	ret0, _ := ret[0].(*settlement_bill.GetSettlementBillWaitWithdrawResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSettlementBillWaitWithdraw indicates an expected call of GetSettlementBillWaitWithdraw.
func (mr *MockIManagerMockRecorder) GetSettlementBillWaitWithdraw(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSettlementBillWaitWithdraw", reflect.TypeOf((*MockIManager)(nil).GetSettlementBillWaitWithdraw), arg0, arg1)
}

// GetTaxRateList mocks base method.
func (m *MockIManager) GetTaxRateList(arg0 context.Context, arg1 *settlement_bill.GetTaxRateListReq) (*settlement_bill.GetTaxRateListResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTaxRateList", arg0, arg1)
	ret0, _ := ret[0].(*settlement_bill.GetTaxRateListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTaxRateList indicates an expected call of GetTaxRateList.
func (mr *MockIManagerMockRecorder) GetTaxRateList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTaxRateList", reflect.TypeOf((*MockIManager)(nil).GetTaxRateList), arg0, arg1)
}

// GetWaitWithdrawDeepCoop mocks base method.
func (m *MockIManager) GetWaitWithdrawDeepCoop(arg0 context.Context, arg1 *settlement_bill.GetWaitWithdrawDeepCoopReq) (*settlement_bill.GetWaitWithdrawDeepCoopResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetWaitWithdrawDeepCoop", arg0, arg1)
	ret0, _ := ret[0].(*settlement_bill.GetWaitWithdrawDeepCoopResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetWaitWithdrawDeepCoop indicates an expected call of GetWaitWithdrawDeepCoop.
func (mr *MockIManagerMockRecorder) GetWaitWithdrawDeepCoop(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWaitWithdrawDeepCoop", reflect.TypeOf((*MockIManager)(nil).GetWaitWithdrawDeepCoop), arg0, arg1)
}

// GetWaitWithdrawMonths mocks base method.
func (m *MockIManager) GetWaitWithdrawMonths(arg0 context.Context, arg1 *settlement_bill.GetWaitWithdrawMonthsReq) (*settlement_bill.GetWaitWithdrawMonthsResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetWaitWithdrawMonths", arg0, arg1)
	ret0, _ := ret[0].(*settlement_bill.GetWaitWithdrawMonthsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetWaitWithdrawMonths indicates an expected call of GetWaitWithdrawMonths.
func (mr *MockIManagerMockRecorder) GetWaitWithdrawMonths(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWaitWithdrawMonths", reflect.TypeOf((*MockIManager)(nil).GetWaitWithdrawMonths), arg0, arg1)
}

// HandleMergeBill mocks base method.
func (m *MockIManager) HandleMergeBill(arg0 context.Context, arg1 *model.SettlementMeta, arg2 *mysql.SettlementBill) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "HandleMergeBill", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// HandleMergeBill indicates an expected call of HandleMergeBill.
func (mr *MockIManagerMockRecorder) HandleMergeBill(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HandleMergeBill", reflect.TypeOf((*MockIManager)(nil).HandleMergeBill), arg0, arg1, arg2)
}

// IsAllowWithdraw mocks base method.
func (m *MockIManager) IsAllowWithdraw(arg0 context.Context, arg1 *settlement_bill.IsAllowWithdrawReq) (*settlement_bill.IsAllowWithdrawResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IsAllowWithdraw", arg0, arg1)
	ret0, _ := ret[0].(*settlement_bill.IsAllowWithdrawResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IsAllowWithdraw indicates an expected call of IsAllowWithdraw.
func (mr *MockIManagerMockRecorder) IsAllowWithdraw(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsAllowWithdraw", reflect.TypeOf((*MockIManager)(nil).IsAllowWithdraw), arg0, arg1)
}

// PrivateWithdraw mocks base method.
func (m *MockIManager) PrivateWithdraw(arg0 context.Context, arg1 *settlement_bill.PrivateWithdrawReq) (*settlement_bill.PrivateWithdrawResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PrivateWithdraw", arg0, arg1)
	ret0, _ := ret[0].(*settlement_bill.PrivateWithdrawResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PrivateWithdraw indicates an expected call of PrivateWithdraw.
func (mr *MockIManagerMockRecorder) PrivateWithdraw(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PrivateWithdraw", reflect.TypeOf((*MockIManager)(nil).PrivateWithdraw), arg0, arg1)
}

// PrivateWithdrawManually mocks base method.
func (m *MockIManager) PrivateWithdrawManually(arg0 context.Context, arg1 *settlement_bill.PrivateWithdrawReq) (*settlement_bill.PrivateWithdrawResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PrivateWithdrawManually", arg0, arg1)
	ret0, _ := ret[0].(*settlement_bill.PrivateWithdrawResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PrivateWithdrawManually indicates an expected call of PrivateWithdrawManually.
func (mr *MockIManagerMockRecorder) PrivateWithdrawManually(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PrivateWithdrawManually", reflect.TypeOf((*MockIManager)(nil).PrivateWithdrawManually), arg0, arg1)
}

// RecordAmuseExtraPrepaid mocks base method.
func (m *MockIManager) RecordAmuseExtraPrepaid(arg0 *gorm.DB, arg1 uint32, arg2 []*settlement_bill.AmuseExtraPrepaidIncome, arg3 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RecordAmuseExtraPrepaid", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// RecordAmuseExtraPrepaid indicates an expected call of RecordAmuseExtraPrepaid.
func (mr *MockIManagerMockRecorder) RecordAmuseExtraPrepaid(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RecordAmuseExtraPrepaid", reflect.TypeOf((*MockIManager)(nil).RecordAmuseExtraPrepaid), arg0, arg1, arg2, arg3)
}

// RecordExtraIncome mocks base method.
func (m *MockIManager) RecordExtraIncome(arg0 context.Context, arg1 *settlement_bill.RecordExtraIncomeReq) (*settlement_bill.RecordExtraIncomeResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RecordExtraIncome", arg0, arg1)
	ret0, _ := ret[0].(*settlement_bill.RecordExtraIncomeResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RecordExtraIncome indicates an expected call of RecordExtraIncome.
func (mr *MockIManagerMockRecorder) RecordExtraIncome(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RecordExtraIncome", reflect.TypeOf((*MockIManager)(nil).RecordExtraIncome), arg0, arg1)
}

// RecordExtraIncomeDeduction mocks base method.
func (m *MockIManager) RecordExtraIncomeDeduction(arg0 *gorm.DB, arg1 uint32, arg2 []*settlement_bill.DeductMoney, arg3 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RecordExtraIncomeDeduction", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// RecordExtraIncomeDeduction indicates an expected call of RecordExtraIncomeDeduction.
func (mr *MockIManagerMockRecorder) RecordExtraIncomeDeduction(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RecordExtraIncomeDeduction", reflect.TypeOf((*MockIManager)(nil).RecordExtraIncomeDeduction), arg0, arg1, arg2, arg3)
}

// RecordExtraIncomeDeepCooperation mocks base method.
func (m *MockIManager) RecordExtraIncomeDeepCooperation(arg0 *gorm.DB, arg1 uint32, arg2 []*settlement_bill.DeepCooperationIncome, arg3 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RecordExtraIncomeDeepCooperation", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// RecordExtraIncomeDeepCooperation indicates an expected call of RecordExtraIncomeDeepCooperation.
func (mr *MockIManagerMockRecorder) RecordExtraIncomeDeepCooperation(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RecordExtraIncomeDeepCooperation", reflect.TypeOf((*MockIManager)(nil).RecordExtraIncomeDeepCooperation), arg0, arg1, arg2, arg3)
}

// RecordExtraIncomeYuyinChannelSubsidy mocks base method.
func (m *MockIManager) RecordExtraIncomeYuyinChannelSubsidy(arg0 *gorm.DB, arg1 uint32, arg2 []*settlement_bill.YuyinAnchorSubsidyIncome, arg3 []*settlement_bill.YuyinNewGuildSubsidyIncome, arg4 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RecordExtraIncomeYuyinChannelSubsidy", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(error)
	return ret0
}

// RecordExtraIncomeYuyinChannelSubsidy indicates an expected call of RecordExtraIncomeYuyinChannelSubsidy.
func (mr *MockIManagerMockRecorder) RecordExtraIncomeYuyinChannelSubsidy(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RecordExtraIncomeYuyinChannelSubsidy", reflect.TypeOf((*MockIManager)(nil).RecordExtraIncomeYuyinChannelSubsidy), arg0, arg1, arg2, arg3, arg4)
}

// RecordReceiptFile mocks base method.
func (m *MockIManager) RecordReceiptFile(arg0 context.Context, arg1 *settlement_bill.RecordReceiptFileReq) (*settlement_bill.RecordReceiptFileResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RecordReceiptFile", arg0, arg1)
	ret0, _ := ret[0].(*settlement_bill.RecordReceiptFileResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RecordReceiptFile indicates an expected call of RecordReceiptFile.
func (mr *MockIManagerMockRecorder) RecordReceiptFile(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RecordReceiptFile", reflect.TypeOf((*MockIManager)(nil).RecordReceiptFile), arg0, arg1)
}

// RecordTaxRate mocks base method.
func (m *MockIManager) RecordTaxRate(arg0 context.Context, arg1 *settlement_bill.RecordTaxRateReq) (*settlement_bill.RecordTaxRateResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RecordTaxRate", arg0, arg1)
	ret0, _ := ret[0].(*settlement_bill.RecordTaxRateResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RecordTaxRate indicates an expected call of RecordTaxRate.
func (mr *MockIManagerMockRecorder) RecordTaxRate(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RecordTaxRate", reflect.TypeOf((*MockIManager)(nil).RecordTaxRate), arg0, arg1)
}

// ReportConfirmDeductMoney mocks base method.
func (m *MockIManager) ReportConfirmDeductMoney(arg0 context.Context, arg1 *settlement_bill.ReportConfirmReq) (*settlement_bill.ReportConfirmResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ReportConfirmDeductMoney", arg0, arg1)
	ret0, _ := ret[0].(*settlement_bill.ReportConfirmResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ReportConfirmDeductMoney indicates an expected call of ReportConfirmDeductMoney.
func (mr *MockIManagerMockRecorder) ReportConfirmDeductMoney(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReportConfirmDeductMoney", reflect.TypeOf((*MockIManager)(nil).ReportConfirmDeductMoney), arg0, arg1)
}

// ReportConfirmDeepCoop mocks base method.
func (m *MockIManager) ReportConfirmDeepCoop(arg0 context.Context, arg1 *settlement_bill.ReportConfirmReq) (*settlement_bill.ReportConfirmResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ReportConfirmDeepCoop", arg0, arg1)
	ret0, _ := ret[0].(*settlement_bill.ReportConfirmResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ReportConfirmDeepCoop indicates an expected call of ReportConfirmDeepCoop.
func (mr *MockIManagerMockRecorder) ReportConfirmDeepCoop(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReportConfirmDeepCoop", reflect.TypeOf((*MockIManager)(nil).ReportConfirmDeepCoop), arg0, arg1)
}

// ReportConfirmWith mocks base method.
func (m *MockIManager) ReportConfirmWith(arg0 context.Context, arg1 *settlement_bill.ReportConfirmWithReq) (*settlement_bill.ReportConfirmWithResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ReportConfirmWith", arg0, arg1)
	ret0, _ := ret[0].(*settlement_bill.ReportConfirmWithResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ReportConfirmWith indicates an expected call of ReportConfirmWith.
func (mr *MockIManagerMockRecorder) ReportConfirmWith(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReportConfirmWith", reflect.TypeOf((*MockIManager)(nil).ReportConfirmWith), arg0, arg1)
}

// ReportConfirmWithByTypes mocks base method.
func (m *MockIManager) ReportConfirmWithByTypes(arg0 context.Context, arg1 []settlement_bill.SettlementBillType) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ReportConfirmWithByTypes", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// ReportConfirmWithByTypes indicates an expected call of ReportConfirmWithByTypes.
func (mr *MockIManagerMockRecorder) ReportConfirmWithByTypes(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReportConfirmWithByTypes", reflect.TypeOf((*MockIManager)(nil).ReportConfirmWithByTypes), arg0, arg1)
}

// ReportConfirmYuyinSubsidy mocks base method.
func (m *MockIManager) ReportConfirmYuyinSubsidy(arg0 context.Context, arg1 *settlement_bill.ReportConfirmReq) (*settlement_bill.ReportConfirmResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ReportConfirmYuyinSubsidy", arg0, arg1)
	ret0, _ := ret[0].(*settlement_bill.ReportConfirmResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ReportConfirmYuyinSubsidy indicates an expected call of ReportConfirmYuyinSubsidy.
func (mr *MockIManagerMockRecorder) ReportConfirmYuyinSubsidy(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReportConfirmYuyinSubsidy", reflect.TypeOf((*MockIManager)(nil).ReportConfirmYuyinSubsidy), arg0, arg1)
}

// RestartProcess mocks base method.
func (m *MockIManager) RestartProcess(arg0 context.Context, arg1, arg2 *mysql.SettleReceiptsBill, arg3 *oa.ProcessBill) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RestartProcess", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// RestartProcess indicates an expected call of RestartProcess.
func (mr *MockIManagerMockRecorder) RestartProcess(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RestartProcess", reflect.TypeOf((*MockIManager)(nil).RestartProcess), arg0, arg1, arg2, arg3)
}

// SaveAnchorScoreRecords mocks base method.
func (m *MockIManager) SaveAnchorScoreRecords(arg0 context.Context, arg1 *mysql.SettlementBill) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "SaveAnchorScoreRecords", arg0, arg1)
}

// SaveAnchorScoreRecords indicates an expected call of SaveAnchorScoreRecords.
func (mr *MockIManagerMockRecorder) SaveAnchorScoreRecords(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SaveAnchorScoreRecords", reflect.TypeOf((*MockIManager)(nil).SaveAnchorScoreRecords), arg0, arg1)
}

// SendEmail mocks base method.
func (m *MockIManager) SendEmail(arg0 context.Context, arg1, arg2 string, arg3 []string) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "SendEmail", arg0, arg1, arg2, arg3)
}

// SendEmail indicates an expected call of SendEmail.
func (mr *MockIManagerMockRecorder) SendEmail(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendEmail", reflect.TypeOf((*MockIManager)(nil).SendEmail), arg0, arg1, arg2, arg3)
}

// SetTaxRate mocks base method.
func (m *MockIManager) SetTaxRate(arg0 context.Context, arg1 *settlement_bill.SetTaxRateReq) (*settlement_bill.SetTaxRateResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetTaxRate", arg0, arg1)
	ret0, _ := ret[0].(*settlement_bill.SetTaxRateResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetTaxRate indicates an expected call of SetTaxRate.
func (mr *MockIManagerMockRecorder) SetTaxRate(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetTaxRate", reflect.TypeOf((*MockIManager)(nil).SetTaxRate), arg0, arg1)
}

// SettlementYuyinSubsidy mocks base method.
func (m *MockIManager) SettlementYuyinSubsidy(arg0 context.Context) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "SettlementYuyinSubsidy", arg0)
}

// SettlementYuyinSubsidy indicates an expected call of SettlementYuyinSubsidy.
func (mr *MockIManagerMockRecorder) SettlementYuyinSubsidy(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SettlementYuyinSubsidy", reflect.TypeOf((*MockIManager)(nil).SettlementYuyinSubsidy), arg0)
}

// StartProcess mocks base method.
func (m *MockIManager) StartProcess(arg0 context.Context, arg1 *model.AssociateReceiptInfo) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "StartProcess", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// StartProcess indicates an expected call of StartProcess.
func (mr *MockIManagerMockRecorder) StartProcess(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "StartProcess", reflect.TypeOf((*MockIManager)(nil).StartProcess), arg0, arg1)
}

// UpdateBillLastBalance mocks base method.
func (m *MockIManager) UpdateBillLastBalance(arg0 context.Context, arg1 []settlement_bill.SettlementBillType) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "UpdateBillLastBalance", arg0, arg1)
}

// UpdateBillLastBalance indicates an expected call of UpdateBillLastBalance.
func (mr *MockIManagerMockRecorder) UpdateBillLastBalance(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateBillLastBalance", reflect.TypeOf((*MockIManager)(nil).UpdateBillLastBalance), arg0, arg1)
}
