package manager

import (
	"context"
	"fmt"
	"github.com/golang/mock/gomock"
	"golang.52tt.com/clients/account"
	mocksExchange "golang.52tt.com/clients/exchange/mocks"
	commissionMocks "golang.52tt.com/pkg/commission/mocks"
	mocksOA "golang.52tt.com/pkg/oa/mocks"
	mocks3 "golang.52tt.com/pkg/settlement/mocks"
	mocksCfgCenter "golang.52tt.com/pkg/settlement/mocks"
	"golang.52tt.com/pkg/settlement/pdf"
	mocks2 "golang.52tt.com/pkg/settlement/pdf/mocks"
	exchange "golang.52tt.com/protocol/services/exchange"
	"golang.52tt.com/services/settlement-bill/model"
	"os"

	Exchange "golang.52tt.com/clients/exchange"
	"golang.52tt.com/clients/guild"
	"golang.52tt.com/clients/realnameauth"
	"golang.52tt.com/pkg/commission"
	"golang.52tt.com/pkg/oa"
	"golang.52tt.com/pkg/settlement"
	pb "golang.52tt.com/protocol/services/settlement-bill"
	"golang.52tt.com/services/settlement-bill/cache"
	"golang.52tt.com/services/settlement-bill/conf"
	"golang.52tt.com/services/settlement-bill/mocks"
	"golang.52tt.com/services/settlement-bill/mysql"
	"golang.52tt.com/services/settlement-bill/utils"
	"gorm.io/gorm"
	"reflect"
	"strings"
	"testing"
	"time"
)

func TestCommissionCli_getClient(t *testing.T) {
	type fields struct {
		CommissionClient           commission.Client
		GiftScoreClient            commission.Client
		AwardScoreClient           commission.Client
		MaskScoreClient            commission.Client
		KnightScoreClient          commission.Client
		AmuseCommissionClient      commission.Client
		YuyinBaseCommissionClient  commission.Client
		YuyinAwardCommissionClient commission.Client
	}
	type args struct {
		billType pb.SettlementBillType
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   commission.Client
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &CommissionCli{
				CommissionClient:           tt.fields.CommissionClient,
				GiftScoreClient:            tt.fields.GiftScoreClient,
				AwardScoreClient:           tt.fields.AwardScoreClient,
				MaskScoreClient:            tt.fields.MaskScoreClient,
				KnightScoreClient:          tt.fields.KnightScoreClient,
				AmuseCommissionClient:      tt.fields.AmuseCommissionClient,
				YuyinBaseCommissionClient:  tt.fields.YuyinBaseCommissionClient,
				YuyinAwardCommissionClient: tt.fields.YuyinAwardCommissionClient,
			}
			if got := c.getClient(tt.args.billType); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("getClient() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestManager_CreateSettlementBill(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	uid := uint32(123456)
	billTypeGiftScore := pb.SettlementBillType_GiftScore
	now := utils.NowTime()
	billId := createBillId(uid, billTypeGiftScore, now)
	tax := uint32(3)
	var validSettleMonths []uint32
	settleEnd := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, time.Local)

	mockStore := mocks.NewMockIStore(ctl)
	mockCache := mocks.NewMockISettlementBillCache(ctl)
	// mockConfig := mocks.NewMockIServiceConfigT(ctl)

	resp := &pb.CreateSettlementBillResp{
		BillId: billId,
	}

	incomeSum := uint64(0)
	actualIncomePay, taxCompensationRate, compensationPoint :=
		settlement.SettleMoneyCal(tax, float64(incomeSum), 0, 0)

	bill := &mysql.SettlementBill{
		BillId:              billId,
		GuildOwner:          uid,
		TaxRate:             tax,
		BillType:            uint8(billTypeGiftScore),
		IncomePay:           uint64(actualIncomePay),
		IncomeSum:           incomeSum,
		TaxCompensationRate: taxCompensationRate,
		CompensationPoint:   compensationPoint,
		SettleDate:          utils.DayTime(now),
		SettleStart:         utils.MonthTime(now),
		SettleEnd:           now,
	}

	ctx := context.Background()
	gomock.InOrder(
		mockStore.EXPECT().GetUidLastBill(ctx, uid, billTypeGiftScore).Return(&mysql.SettlementBill{
			BillId:     billId,
			GuildOwner: uid,
			SettleEnd:  settleEnd,
		}, nil),
		mockCache.EXPECT().GetCreateSettleBillLock(ctx, uid, billTypeGiftScore).Return(true, nil),
		mockStore.EXPECT().GetAllowMergeSettlementBillByUidType(ctx, uid, billTypeGiftScore).Return(nil, gorm.ErrRecordNotFound),
		mockStore.EXPECT().GetGuildTaxRate(ctx, uid).Return(&mysql.TaxRate{
			GuildOwner: uid,
			TaxRate:    tax,
		}, nil),
		mockStore.EXPECT().CreateOrMergeSettlementBill(ctx, bill, true, validSettleMonths).Return(nil),
		mockCache.EXPECT().ReleaseCreateSettleBillLock(ctx, uid, billTypeGiftScore).Return(nil),
		mockStore.EXPECT().FinishCreateSettlementBill(ctx, billId, &oa.FileInfo{}).Return(nil),
	)

	type fields struct {
		cfgCenter        settlement.IConfigCenter
		cacheClient      cache.ISettlementBillCache
		mysqlStore       mysql.IStore
		sc               conf.IServiceConfigT
		oaCli            oa.IClient
		accountCli       account.IClient
		exchangeGuildCli Exchange.IGuildClient
		guildCli         guild.IClient
		realNameAuthCli  realnameauth.IClient
		commissionCli    *CommissionCli
	}
	type args struct {
		ctx context.Context
		req *pb.CreateSettlementBillReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.CreateSettlementBillResp
		wantErr bool
	}{
		{name: "CreateSettlementBill-gift-score",
			fields: fields{mysqlStore: mockStore, cacheClient: mockCache},
			args: args{ctx: context.Background(), req: &pb.CreateSettlementBillReq{
				Uid:       uid,
				BillType:  billTypeGiftScore,
				SettleEnd: uint32(time.Now().Unix()),
			}},
			want:    resp,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &Manager{
				cfgCenter:        tt.fields.cfgCenter,
				cacheClient:      tt.fields.cacheClient,
				mysqlStore:       tt.fields.mysqlStore,
				sc:               tt.fields.sc,
				oaCli:            tt.fields.oaCli,
				accountCli:       tt.fields.accountCli,
				exchangeGuildCli: tt.fields.exchangeGuildCli,
				guildCli:         tt.fields.guildCli,
				realNameAuthCli:  tt.fields.realNameAuthCli,
				commissionCli:    tt.fields.commissionCli,
			}
			got, err := m.CreateSettlementBill(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("CreateSettlementBill() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			fmt.Printf("got  -> %+v\n", got)
			fmt.Printf("want -> %+v\n", tt.want)
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("CreateSettlementBill() got = %v, want %v", got, tt.want)
			}
		})
	}
	time.Sleep(1 * time.Second)
}

func TestManager_GenSettleBillPdf(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	uid := uint32(123456)
	billTypeGiftScore := pb.SettlementBillType_GiftScore
	now := utils.NowTime()
	billId := createBillId(uid, billTypeGiftScore, now)
	fileId := "123"
	company := "123"
	settleEnd := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, time.Local)

	// path := "/tmp/1.pdf"
	_, _ = os.Create("/tmp/settle_pdf_tpl.html")

	mockStore := mocks.NewMockIStore(ctl)
	mockPDF := mocks2.NewMockIGenClient(ctl)
	mockConfig := mocks.NewMockIServiceConfigT(ctl)
	mockConfigCenter := mocks3.NewMockIConfigCenter(ctl)
	mockExchangeGuildCli := mocksExchange.NewMockIGuildClient(ctl)
	mockOA := mocksOA.NewMockIClient(ctl)

	resp := &pb.GenSettleBillPdfResp{
		FileId: fileId,
	}

	ctx := context.Background()
	gomock.InOrder(
		mockStore.EXPECT().GetSettlementBill(ctx, billId).Return(&mysql.SettlementBill{
			BillId:     billId,
			GuildOwner: uid,
			SettleEnd:  settleEnd,
			BillType:   uint8(billTypeGiftScore),
			Status:     uint8(pb.SettlementBillStatus_WaitWithdraw),
		}, nil),
		mockExchangeGuildCli.EXPECT().GetMainData(ctx, uid).Return(&exchange.GetMain{
			MasterUid: uid,
			Company:   company,
		}, nil),
		mockConfig.EXPECT().IsTest().Return(true),
		mockStore.EXPECT().GetSettleDeductMoneyByBillId(ctx, uid, billId).Return([]*mysql.SettleDeductMoney{}, nil),
		mockPDF.EXPECT().GenPdf(gomock.Any(), gomock.Any()).Return(true, nil),
		mockOA.EXPECT().UploadFileByPath(ctx, gomock.Any()).Return(&oa.FileInfo{
			FileId: fileId,
		}, nil),
		mockStore.EXPECT().SaveBillPDF(ctx, billId, uid, &oa.FileInfo{FileId: fileId}).Return(nil),
	)

	type fields struct {
		cfgCenter        settlement.IConfigCenter
		cacheClient      cache.ISettlementBillCache
		mysqlStore       mysql.IStore
		sc               conf.IServiceConfigT
		oaCli            oa.IClient
		accountCli       account.IClient
		exchangeGuildCli Exchange.IGuildClient
		guildCli         guild.IClient
		realNameAuthCli  realnameauth.IClient
		commissionCli    *CommissionCli
		PdfGenCli        pdf.IGenClient
	}
	type args struct {
		ctx context.Context
		req *pb.GenSettleBillPdfReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.GenSettleBillPdfResp
		wantErr bool
	}{
		{name: "GenSettleBillPdf",
			fields: fields{
				mysqlStore:       mockStore,
				sc:               mockConfig,
				cfgCenter:        mockConfigCenter,
				exchangeGuildCli: mockExchangeGuildCli,
				oaCli:            mockOA,
				PdfGenCli:        mockPDF},
			args: args{ctx: context.Background(), req: &pb.GenSettleBillPdfReq{
				BillId: billId,
			}},
			want:    resp,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &Manager{
				cfgCenter:        tt.fields.cfgCenter,
				cacheClient:      tt.fields.cacheClient,
				mysqlStore:       tt.fields.mysqlStore,
				sc:               tt.fields.sc,
				oaCli:            tt.fields.oaCli,
				accountCli:       tt.fields.accountCli,
				exchangeGuildCli: tt.fields.exchangeGuildCli,
				guildCli:         tt.fields.guildCli,
				realNameAuthCli:  tt.fields.realNameAuthCli,
				commissionCli:    tt.fields.commissionCli,
				PdfGenCli:        tt.fields.PdfGenCli,
			}
			got, err := m.GenSettleBillPdf(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GenSettleBillPdf() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GenSettleBillPdf() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestManager_GetAssociatedBillItems(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	uid := uint32(123456)
	billTypeGiftScore := pb.SettlementBillType_GiftScore
	now := utils.NowTime()
	billId := createBillId(uid, billTypeGiftScore, now)
	settleIds := []string{"123", "456"}

	mockStore := mocks.NewMockIStore(ctl)
	mockConfig := mocks.NewMockIServiceConfigT(ctl)

	resp := &pb.GetAssociatedBillItemsResp{
		List: []*pb.SettlementBillDetail{
			{
				BillId:          billId,
				Type:            billTypeGiftScore,
				Uid:             uid,
				AllowWithdraw:   settlement.AllowWithdraw(uid, billTypeGiftScore, now, true),
				GeneralBillData: &pb.GeneralBill{},
			},
		},
	}

	ctx := context.Background()
	gomock.InOrder(
		mockStore.EXPECT().GetReceiptBill(ctx, billId).Return(&mysql.SettleReceiptsBill{
			BillId:              billId,
			GuildOwner:          uid,
			AssociatedSettleIds: strings.Join(settleIds, ","),
		}, nil),
		mockStore.EXPECT().GetSettlementBills(ctx, settleIds).Return([]*mysql.SettlementBill{
			{
				BillId:     billId,
				GuildOwner: uid,
				BillType:   uint8(billTypeGiftScore),
			},
		}, nil),
		mockConfig.EXPECT().IsTest().Return(true),
	)

	type fields struct {
		cfgCenter        settlement.IConfigCenter
		cacheClient      cache.ISettlementBillCache
		mysqlStore       mysql.IStore
		sc               conf.IServiceConfigT
		oaCli            oa.IClient
		accountCli       account.IClient
		exchangeGuildCli Exchange.IGuildClient
		guildCli         guild.IClient
		realNameAuthCli  realnameauth.IClient
		commissionCli    *CommissionCli
	}
	type args struct {
		ctx context.Context
		req *pb.GetAssociatedBillItemsReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.GetAssociatedBillItemsResp
		wantErr bool
	}{
		{name: "GetAssociatedBillItems",
			fields: fields{
				mysqlStore: mockStore,
				sc:         mockConfig,
			},
			args: args{ctx: context.Background(), req: &pb.GetAssociatedBillItemsReq{
				Uid:           uid,
				ReceiptBillId: billId,
			}},
			want:    resp,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &Manager{
				cfgCenter:        tt.fields.cfgCenter,
				cacheClient:      tt.fields.cacheClient,
				mysqlStore:       tt.fields.mysqlStore,
				sc:               tt.fields.sc,
				oaCli:            tt.fields.oaCli,
				accountCli:       tt.fields.accountCli,
				exchangeGuildCli: tt.fields.exchangeGuildCli,
				guildCli:         tt.fields.guildCli,
				realNameAuthCli:  tt.fields.realNameAuthCli,
				commissionCli:    tt.fields.commissionCli,
			}
			got, err := m.GetAssociatedBillItems(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetAssociatedBillItems() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetAssociatedBillItems() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestManager_GetBillLastBalance(t *testing.T) {
	type fields struct {
		cfgCenter        settlement.IConfigCenter
		cacheClient      cache.ISettlementBillCache
		mysqlStore       mysql.IStore
		sc               conf.IServiceConfigT
		oaCli            oa.IClient
		accountCli       account.IClient
		exchangeGuildCli Exchange.IGuildClient
		guildCli         guild.IClient
		realNameAuthCli  realnameauth.IClient
		commissionCli    *CommissionCli
	}
	type args struct {
		ctx        context.Context
		req        *pb.CreateSettlementBillReq
		originBill *mysql.SettlementBill
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    uint64
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &Manager{
				cfgCenter:        tt.fields.cfgCenter,
				cacheClient:      tt.fields.cacheClient,
				mysqlStore:       tt.fields.mysqlStore,
				sc:               tt.fields.sc,
				oaCli:            tt.fields.oaCli,
				accountCli:       tt.fields.accountCli,
				exchangeGuildCli: tt.fields.exchangeGuildCli,
				guildCli:         tt.fields.guildCli,
				realNameAuthCli:  tt.fields.realNameAuthCli,
				commissionCli:    tt.fields.commissionCli,
			}
			got, err := m.GetBillLastBalance(tt.args.ctx, tt.args.req, tt.args.originBill)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetBillLastBalance() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("GetBillLastBalance() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestManager_GetDeductMoneyDetailByBillId(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	now := utils.NowTime()
	uid := uint32(123456)
	settleDate := utils.GetMonthInt(now)
	billTypeGiftScore := pb.SettlementBillType_GiftScore
	billId := createBillId(uid, billTypeGiftScore, now)
	deduct := uint64(123)
	remark := "123"

	mockStore := mocks.NewMockIStore(ctl)

	resp := &pb.GetDeductMoneyListByBillIdResp{
		List: []*pb.DeductMoneyDetail{
			{
				SettlementDate: settleDate,
				DeductMoney:    deduct,
				Remark:         remark,
			},
		},
	}

	ctx := context.Background()
	gomock.InOrder(
		mockStore.EXPECT().GetSettlementBill(ctx, billId).Return(&mysql.SettlementBill{
			BillId:     billId,
			GuildOwner: uid,
		}, nil),
		mockStore.EXPECT().GetSettleDeductMoneyByBillId(ctx, uid, billId).Return([]*mysql.SettleDeductMoney{
			{
				GuildOwner:     uid,
				SettlementDate: settleDate,
				BillType:       uint8(billTypeGiftScore),
				Money:          deduct,
				Remark:         remark,
			},
		}, nil),
	)

	type fields struct {
		cfgCenter        settlement.IConfigCenter
		cacheClient      cache.ISettlementBillCache
		mysqlStore       mysql.IStore
		sc               conf.IServiceConfigT
		oaCli            oa.IClient
		accountCli       account.IClient
		exchangeGuildCli Exchange.IGuildClient
		guildCli         guild.IClient
		realNameAuthCli  realnameauth.IClient
		commissionCli    *CommissionCli
	}
	type args struct {
		ctx context.Context
		req *pb.GetDeductMoneyListByBillIdReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.GetDeductMoneyListByBillIdResp
		wantErr bool
	}{
		{name: "GetDeductMoneyDetailByBillId",
			fields: fields{
				mysqlStore: mockStore,
			},
			args: args{ctx: context.Background(), req: &pb.GetDeductMoneyListByBillIdReq{
				BillId: billId,
			}},
			want:    resp,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &Manager{
				cfgCenter:        tt.fields.cfgCenter,
				cacheClient:      tt.fields.cacheClient,
				mysqlStore:       tt.fields.mysqlStore,
				sc:               tt.fields.sc,
				oaCli:            tt.fields.oaCli,
				accountCli:       tt.fields.accountCli,
				exchangeGuildCli: tt.fields.exchangeGuildCli,
				guildCli:         tt.fields.guildCli,
				realNameAuthCli:  tt.fields.realNameAuthCli,
				commissionCli:    tt.fields.commissionCli,
			}
			got, err := m.GetDeductMoneyDetailByBillId(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetDeductMoneyDetailByBillId() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetDeductMoneyDetailByBillId() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestManager_GetMonthsBySettleBillId(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	now := utils.NowTime()
	uid := uint32(123456)
	billTypeGiftScore := pb.SettlementBillType_GiftScore
	billId := createBillId(uid, billTypeGiftScore, now)

	mockStore := mocks.NewMockIStore(ctl)

	resp := &pb.GetMonthsBySettleBillIdResp{
		Months: []uint32{},
	}

	ctx := context.Background()
	gomock.InOrder(
		mockStore.EXPECT().GetSettlementBill(ctx, billId).Return(&mysql.SettlementBill{
			BillId:     billId,
			GuildOwner: uid,
			BillType:   uint8(billTypeGiftScore),
		}, nil),
	)

	type fields struct {
		cfgCenter        settlement.IConfigCenter
		cacheClient      cache.ISettlementBillCache
		mysqlStore       mysql.IStore
		sc               conf.IServiceConfigT
		oaCli            oa.IClient
		accountCli       account.IClient
		exchangeGuildCli Exchange.IGuildClient
		guildCli         guild.IClient
		realNameAuthCli  realnameauth.IClient
		commissionCli    *CommissionCli
	}
	type args struct {
		ctx context.Context
		req *pb.GetMonthsBySettleBillIdReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.GetMonthsBySettleBillIdResp
		wantErr bool
	}{
		{name: "GetMonthsBySettleBillId",
			fields: fields{
				mysqlStore: mockStore,
			},
			args: args{ctx: context.Background(), req: &pb.GetMonthsBySettleBillIdReq{
				BillId: billId,
				Uid:    uid,
			}},
			want:    resp,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &Manager{
				cfgCenter:        tt.fields.cfgCenter,
				cacheClient:      tt.fields.cacheClient,
				mysqlStore:       tt.fields.mysqlStore,
				sc:               tt.fields.sc,
				oaCli:            tt.fields.oaCli,
				accountCli:       tt.fields.accountCli,
				exchangeGuildCli: tt.fields.exchangeGuildCli,
				guildCli:         tt.fields.guildCli,
				realNameAuthCli:  tt.fields.realNameAuthCli,
				commissionCli:    tt.fields.commissionCli,
			}
			got, err := m.GetMonthsBySettleBillId(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetMonthsBySettleBillId() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetMonthsBySettleBillId() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestManager_GetSettlementBill(t *testing.T) {
	type fields struct {
		cfgCenter        settlement.IConfigCenter
		cacheClient      cache.ISettlementBillCache
		mysqlStore       mysql.IStore
		sc               conf.IServiceConfigT
		oaCli            oa.IClient
		accountCli       account.IClient
		exchangeGuildCli Exchange.IGuildClient
		guildCli         guild.IClient
		realNameAuthCli  realnameauth.IClient
		commissionCli    *CommissionCli
	}
	type args struct {
		ctx context.Context
		req *pb.GetSettlementBillReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.GetSettlementBillResp
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &Manager{
				cfgCenter:        tt.fields.cfgCenter,
				cacheClient:      tt.fields.cacheClient,
				mysqlStore:       tt.fields.mysqlStore,
				sc:               tt.fields.sc,
				oaCli:            tt.fields.oaCli,
				accountCli:       tt.fields.accountCli,
				exchangeGuildCli: tt.fields.exchangeGuildCli,
				guildCli:         tt.fields.guildCli,
				realNameAuthCli:  tt.fields.realNameAuthCli,
				commissionCli:    tt.fields.commissionCli,
			}
			got, err := m.GetSettlementBill(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetSettlementBill() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetSettlementBill() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestManager_GetSettlementBillWaitReceipt(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	uid := uint32(123456)
	billId := "123"
	url := "https://google.com"

	mockStore := mocks.NewMockIStore(ctl)
	mockConfig := mocks.NewMockIServiceConfigT(ctl)
	mockOA := mocksOA.NewMockIClient(ctl)
	mockConfigCenter := mocksCfgCenter.NewMockIConfigCenter(ctl)

	ctx := context.Background()
	gomock.InOrder(
		mockConfigCenter.EXPECT().IsDisableReceiptVerify(uid).Return(false),
		mockStore.EXPECT().GetSettlementBillWaitReceipt(ctx, uid).
			Return([]*mysql.SettlementBill{
				{
					BillId: billId,
					PdfId:  url,
					Status: uint8(pb.SettlementBillStatus_WaitAdjustReceipt),
				},
			}, nil),
		mockConfig.EXPECT().IsTest().Return(true),
	)

	resp := &pb.GetSettlementBillWaitReceiptResp{
		Bills: []*pb.SettlementBillDetail{
			{
				BillId:          billId,
				FileId:          url,
				GeneralBillData: &pb.GeneralBill{},
				Status:          pb.SettlementBillStatus_WaitAdjustReceipt,
			},
		},
	}

	type fields struct {
		cfgCenter        settlement.IConfigCenter
		cacheClient      cache.ISettlementBillCache
		mysqlStore       mysql.IStore
		sc               conf.IServiceConfigT
		oaCli            oa.IClient
		accountCli       account.IClient
		exchangeGuildCli Exchange.IGuildClient
		guildCli         guild.IClient
		realNameAuthCli  realnameauth.IClient
		commissionCli    *CommissionCli
	}
	type args struct {
		ctx context.Context
		req *pb.GetSettlementBillWaitReceiptReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.GetSettlementBillWaitReceiptResp
		wantErr bool
	}{
		{name: "GetSettlementBillWaitReceipt",
			fields: fields{mysqlStore: mockStore,
				sc:        mockConfig,
				oaCli:     mockOA,
				cfgCenter: mockConfigCenter,
			},
			args: args{ctx: context.Background(), req: &pb.GetSettlementBillWaitReceiptReq{
				Uid:           uid,
				ReceiptBillId: "123",
			}},
			want:    resp,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &Manager{
				cfgCenter:        tt.fields.cfgCenter,
				cacheClient:      tt.fields.cacheClient,
				mysqlStore:       tt.fields.mysqlStore,
				sc:               tt.fields.sc,
				oaCli:            tt.fields.oaCli,
				accountCli:       tt.fields.accountCli,
				exchangeGuildCli: tt.fields.exchangeGuildCli,
				guildCli:         tt.fields.guildCli,
				realNameAuthCli:  tt.fields.realNameAuthCli,
				commissionCli:    tt.fields.commissionCli,
			}
			got, err := m.GetSettlementBillWaitReceipt(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetSettlementBillWaitReceipt() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetSettlementBillWaitReceipt() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestManager_GetSettlementBillWaitWithdraw(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	uid := uint32(123456)
	billTypeGiftScore := pb.SettlementBillType_GiftScore
	now := utils.NowTime()
	guildId := uint32(123)
	billId := createReceiptBillId(now, guildId)
	tax := uint32(3)
	balance := uint64(10000)

	mockStore := mocks.NewMockIStore(ctl)
	mockConfig := mocks.NewMockIServiceConfigT(ctl)
	// mockConfigCenter := mocks3.NewMockIConfigCenter(ctl)
	mockCommission := commissionMocks.NewMockClient(ctl)

	commissionCli := &CommissionCli{
		GiftScoreClient: mockCommission,
	}

	actualIncomePay, taxCompensationRate, compensationPoint := settlement.SettleMoneyCal(tax, float64(balance), float64(0), 0)

	resp := &pb.GetSettlementBillWaitWithdrawResp{
		List: []*pb.SettlementBillDetail{
			{
				BillId:          billId,
				Type:            billTypeGiftScore,
				Status:          pb.SettlementBillStatus_WaitWithdraw,
				TaxRate:         tax,
				IncomeSum:       balance,
				ActualIncomePay: uint64(actualIncomePay),
				GeneralBillData: &pb.GeneralBill{Money: balance},
				AllowWithdraw:   settlement.AllowWithdraw(uid, billTypeGiftScore, now, true),
			},
		},
	}

	resp.TaxRate = tax
	resp.TaxCompensationRate = uint32(taxCompensationRate * 10000)
	resp.CompensationPoint = uint32(compensationPoint * 10000)
	resp.TaxCompensationRateText = fmt.Sprintf("%.2f", taxCompensationRate*100)
	resp.CompensationPointText = fmt.Sprintf("%.2f", compensationPoint*100)

	ctx := context.Background()
	gomock.InOrder(
		mockStore.EXPECT().GetGuildTaxRate(ctx, uid).Return(&mysql.TaxRate{
			TaxRate: tax,
		}, nil),
		mockStore.EXPECT().GetWaitWithdrawSettlementBill(ctx, uid).Return([]*mysql.SettlementBill{
			{
				BillId:   billId,
				BillType: uint8(billTypeGiftScore),
				Status:   uint8(pb.SettlementBillStatus_WaitWithdraw),
			},
		}, nil),
		mockConfig.EXPECT().IsTest().Return(true).AnyTimes(),
		mockCommission.EXPECT().GetBalance(ctx, uid).Return(balance, uint64(0), nil),
	)

	type fields struct {
		cfgCenter        settlement.IConfigCenter
		cacheClient      cache.ISettlementBillCache
		mysqlStore       mysql.IStore
		sc               conf.IServiceConfigT
		oaCli            oa.IClient
		accountCli       account.IClient
		exchangeGuildCli Exchange.IGuildClient
		guildCli         guild.IClient
		realNameAuthCli  realnameauth.IClient
		commissionCli    *CommissionCli
	}
	type args struct {
		ctx context.Context
		req *pb.GetSettlementBillWaitWithdrawReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.GetSettlementBillWaitWithdrawResp
		wantErr bool
	}{
		{name: "GetSettlementBillWaitWithdraw",
			fields: fields{
				mysqlStore:    mockStore,
				commissionCli: commissionCli,
				sc:            mockConfig,
			},
			args: args{ctx: context.Background(), req: &pb.GetSettlementBillWaitWithdrawReq{
				Uid: uid,
			}},
			want:    resp,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &Manager{
				cfgCenter:        tt.fields.cfgCenter,
				cacheClient:      tt.fields.cacheClient,
				mysqlStore:       tt.fields.mysqlStore,
				sc:               tt.fields.sc,
				oaCli:            tt.fields.oaCli,
				accountCli:       tt.fields.accountCli,
				exchangeGuildCli: tt.fields.exchangeGuildCli,
				guildCli:         tt.fields.guildCli,
				realNameAuthCli:  tt.fields.realNameAuthCli,
				commissionCli:    tt.fields.commissionCli,
			}
			got, err := m.GetSettlementBillWaitWithdraw(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetSettlementBillWaitWithdraw() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetSettlementBillWaitWithdraw() got = %+v, want %+v", got, tt.want)
			}
		})
	}
}

func TestManager_GetWaitWithdrawDeepCoop(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	uid := uint32(123456)
	billType := pb.SettlementBillType_DeepCoop
	settleStartMonth := uint32(202208)
	settleEndMonth := utils.GetMonthInt(time.Now())

	billId := "123"
	settleDate := time.Date(2022, 8, 1, 0, 0, 0, 0, time.Local)
	tax := uint32(3)
	incomeSum := uint64(10000)
	actualIncomePay, _, _ := settlement.SettleMoneyCal(tax, float64(incomeSum), 0, 0)

	mockStore := mocks.NewMockIStore(ctl)
	mockConfig := mocks.NewMockIServiceConfigT(ctl)
	mockOA := mocksOA.NewMockIClient(ctl)

	ctx := context.Background()
	gomock.InOrder(
		mockStore.EXPECT().GetUidLastBill(ctx, uid, billType).
			Return(&mysql.SettlementBill{
				BillId: billId, SettleDate: settleDate,
			}, nil),
		mockStore.EXPECT().GetWaitWithdrawDeepCoopByRange(ctx, uid, settleStartMonth, settleEndMonth).
			Return([]*mysql.SettleDeepCooperation{
				{SettlementDate: utils.GetMonthInt(settleDate), SettlementMoney: incomeSum},
			}, nil),
		mockStore.EXPECT().GetGuildTaxRate(ctx, uid).Return(&mysql.TaxRate{
			TaxRate: tax,
		}, nil),
	)

	resp := &pb.GetWaitWithdrawDeepCoopResp{
		TaxRate:         tax,
		IncomeSum:       incomeSum,
		ActualIncomePay: uint64(actualIncomePay),
		DeepCoopBill: &pb.GeneralBill{
			Money: incomeSum,
		},
	}

	type fields struct {
		cfgCenter        settlement.IConfigCenter
		cacheClient      cache.ISettlementBillCache
		mysqlStore       mysql.IStore
		sc               conf.IServiceConfigT
		oaCli            oa.IClient
		accountCli       account.IClient
		exchangeGuildCli Exchange.IGuildClient
		guildCli         guild.IClient
		realNameAuthCli  realnameauth.IClient
		commissionCli    *CommissionCli
	}
	type args struct {
		ctx context.Context
		req *pb.GetWaitWithdrawDeepCoopReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.GetWaitWithdrawDeepCoopResp
		wantErr bool
	}{
		{name: "GetWaitWithdrawDeepCoop",
			fields: fields{mysqlStore: mockStore,
				sc:    mockConfig,
				oaCli: mockOA,
			},
			args: args{ctx: context.Background(), req: &pb.GetWaitWithdrawDeepCoopReq{
				Uid: uid,
			}},
			want:    resp,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &Manager{
				cfgCenter:        tt.fields.cfgCenter,
				cacheClient:      tt.fields.cacheClient,
				mysqlStore:       tt.fields.mysqlStore,
				sc:               tt.fields.sc,
				oaCli:            tt.fields.oaCli,
				accountCli:       tt.fields.accountCli,
				exchangeGuildCli: tt.fields.exchangeGuildCli,
				guildCli:         tt.fields.guildCli,
				realNameAuthCli:  tt.fields.realNameAuthCli,
				commissionCli:    tt.fields.commissionCli,
			}
			got, err := m.GetWaitWithdrawDeepCoop(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetWaitWithdrawDeepCoop() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetWaitWithdrawDeepCoop() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestManager_GetWaitWithdrawMonths(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	uid := uint32(123456)
	billType := pb.SettlementBillType_DeepCoop
	settleStartMonth := uint32(202208)
	settleEndMonth := utils.GetMonthInt(time.Now())
	months := []uint32{202208}
	billId := "123"
	settleDate := time.Date(2022, 8, 1, 0, 0, 0, 0, time.Local)

	mockStore := mocks.NewMockIStore(ctl)
	mockConfig := mocks.NewMockIServiceConfigT(ctl)
	mockOA := mocksOA.NewMockIClient(ctl)

	ctx := context.Background()
	gomock.InOrder(
		mockStore.EXPECT().GetUidLastBill(ctx, uid, billType).
			Return(&mysql.SettlementBill{
				BillId: billId, SettleDate: settleDate,
			}, nil),
		mockStore.EXPECT().GetWaitWithdrawDeepCoopByRange(ctx, uid, settleStartMonth, settleEndMonth).
			Return([]*mysql.SettleDeepCooperation{
				{SettlementDate: utils.GetMonthInt(settleDate)},
			}, nil),
	)

	resp := &pb.GetWaitWithdrawMonthsResp{
		Months: months,
	}

	type fields struct {
		cfgCenter        settlement.IConfigCenter
		cacheClient      cache.ISettlementBillCache
		mysqlStore       mysql.IStore
		sc               conf.IServiceConfigT
		oaCli            oa.IClient
		accountCli       account.IClient
		exchangeGuildCli Exchange.IGuildClient
		guildCli         guild.IClient
		realNameAuthCli  realnameauth.IClient
		commissionCli    *CommissionCli
	}
	type args struct {
		ctx context.Context
		req *pb.GetWaitWithdrawMonthsReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.GetWaitWithdrawMonthsResp
		wantErr bool
	}{
		{name: "GetWaitWithdrawMonths-deep",
			fields: fields{mysqlStore: mockStore,
				sc:    mockConfig,
				oaCli: mockOA,
			},
			args: args{ctx: context.Background(), req: &pb.GetWaitWithdrawMonthsReq{
				Uid:      uid,
				BillType: billType,
			}},
			want:    resp,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &Manager{
				cfgCenter:        tt.fields.cfgCenter,
				cacheClient:      tt.fields.cacheClient,
				mysqlStore:       tt.fields.mysqlStore,
				sc:               tt.fields.sc,
				oaCli:            tt.fields.oaCli,
				accountCli:       tt.fields.accountCli,
				exchangeGuildCli: tt.fields.exchangeGuildCli,
				guildCli:         tt.fields.guildCli,
				realNameAuthCli:  tt.fields.realNameAuthCli,
				commissionCli:    tt.fields.commissionCli,
			}
			got, err := m.GetWaitWithdrawMonths(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetWaitWithdrawMonths() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetWaitWithdrawMonths() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestManager_GetWaitWithdrawMonths_Subsidy(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	uid := uint32(123456)
	billTypeSubsidy := pb.SettlementBillType_YuyinSubsidy
	settleDate := utils.MonthTime(time.Now())
	settleDate1 := utils.MonthTime(time.Now())

	settleStartMonth := utils.GetMonthInt(settleDate)
	settleEndMonth := utils.GetMonthInt(settleDate1)
	months := []uint32{settleStartMonth}
	billId := "123"

	mockStore := mocks.NewMockIStore(ctl)
	mockConfig := mocks.NewMockIServiceConfigT(ctl)
	mockOA := mocksOA.NewMockIClient(ctl)

	ctx := context.Background()
	gomock.InOrder(
		mockStore.EXPECT().GetUidLastBill(ctx, uid, billTypeSubsidy).
			Return(&mysql.SettlementBill{
				BillId: billId, SettleDate: settleDate,
			}, nil),
		mockStore.EXPECT().GetWaitWithdrawAnchorSubsidyByRange(ctx, uid, settleStartMonth, settleEndMonth).
			Return([]*mysql.SettleAnchorSubsidy{
				{SettlementDate: utils.GetMonthInt(settleDate)},
			}, nil),
		mockStore.EXPECT().GetWaitWithdrawNewGuildSubsidyByRange(ctx, uid, settleStartMonth, settleEndMonth).
			Return(nil, nil),
	)

	resp := &pb.GetWaitWithdrawMonthsResp{
		Months: months,
	}

	type fields struct {
		cfgCenter        settlement.IConfigCenter
		cacheClient      cache.ISettlementBillCache
		mysqlStore       mysql.IStore
		sc               conf.IServiceConfigT
		oaCli            oa.IClient
		accountCli       account.IClient
		exchangeGuildCli Exchange.IGuildClient
		guildCli         guild.IClient
		realNameAuthCli  realnameauth.IClient
		commissionCli    *CommissionCli
	}
	type args struct {
		ctx context.Context
		req *pb.GetWaitWithdrawMonthsReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.GetWaitWithdrawMonthsResp
		wantErr bool
	}{
		{name: "GetWaitWithdrawMonths-subsidy",
			fields: fields{mysqlStore: mockStore,
				sc:    mockConfig,
				oaCli: mockOA,
			},
			args: args{ctx: context.Background(), req: &pb.GetWaitWithdrawMonthsReq{
				Uid:      uid,
				BillType: billTypeSubsidy,
			}},
			want:    resp,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &Manager{
				cfgCenter:        tt.fields.cfgCenter,
				cacheClient:      tt.fields.cacheClient,
				mysqlStore:       tt.fields.mysqlStore,
				sc:               tt.fields.sc,
				oaCli:            tt.fields.oaCli,
				accountCli:       tt.fields.accountCli,
				exchangeGuildCli: tt.fields.exchangeGuildCli,
				guildCli:         tt.fields.guildCli,
				realNameAuthCli:  tt.fields.realNameAuthCli,
				commissionCli:    tt.fields.commissionCli,
			}
			got, err := m.GetWaitWithdrawMonths(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetWaitWithdrawMonths() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetWaitWithdrawMonths() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestManager_finishCreateSettlementBill(t *testing.T) {
	type fields struct {
		cfgCenter        settlement.IConfigCenter
		cacheClient      cache.ISettlementBillCache
		mysqlStore       mysql.IStore
		sc               conf.IServiceConfigT
		oaCli            oa.IClient
		accountCli       account.IClient
		exchangeGuildCli Exchange.IGuildClient
		guildCli         guild.IClient
		realNameAuthCli  realnameauth.IClient
		commissionCli    *CommissionCli
	}
	type args struct {
		ctx  context.Context
		bill *mysql.SettlementBill
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &Manager{
				cfgCenter:        tt.fields.cfgCenter,
				cacheClient:      tt.fields.cacheClient,
				mysqlStore:       tt.fields.mysqlStore,
				sc:               tt.fields.sc,
				oaCli:            tt.fields.oaCli,
				accountCli:       tt.fields.accountCli,
				exchangeGuildCli: tt.fields.exchangeGuildCli,
				guildCli:         tt.fields.guildCli,
				realNameAuthCli:  tt.fields.realNameAuthCli,
				commissionCli:    tt.fields.commissionCli,
			}
			if err := m.finishCreateSettlementBill(tt.args.ctx, tt.args.bill); (err != nil) != tt.wantErr {
				t.Errorf("finishCreateSettlementBill() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestManager_getUnSettleDeductMoney(t *testing.T) {
	type fields struct {
		cfgCenter        settlement.IConfigCenter
		cacheClient      cache.ISettlementBillCache
		mysqlStore       mysql.IStore
		sc               conf.IServiceConfigT
		oaCli            oa.IClient
		accountCli       account.IClient
		exchangeGuildCli Exchange.IGuildClient
		guildCli         guild.IClient
		realNameAuthCli  realnameauth.IClient
		commissionCli    *CommissionCli
	}
	type args struct {
		ctx context.Context
		uid uint32
		t   pb.SettlementBillType
	}
	tests := []struct {
		name      string
		fields    fields
		args      args
		wantSum   uint64
		wantDates []uint32
		wantErr   bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &Manager{
				cfgCenter:        tt.fields.cfgCenter,
				cacheClient:      tt.fields.cacheClient,
				mysqlStore:       tt.fields.mysqlStore,
				sc:               tt.fields.sc,
				oaCli:            tt.fields.oaCli,
				accountCli:       tt.fields.accountCli,
				exchangeGuildCli: tt.fields.exchangeGuildCli,
				guildCli:         tt.fields.guildCli,
				realNameAuthCli:  tt.fields.realNameAuthCli,
				commissionCli:    tt.fields.commissionCli,
			}
			gotSum, gotDates, err := m.getUnSettleDeductMoney(tt.args.ctx, tt.args.uid, tt.args.t)
			if (err != nil) != tt.wantErr {
				t.Errorf("getUnSettleDeductMoney() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if gotSum != tt.wantSum {
				t.Errorf("getUnSettleDeductMoney() gotSum = %v, want %v", gotSum, tt.wantSum)
			}
			if !reflect.DeepEqual(gotDates, tt.wantDates) {
				t.Errorf("getUnSettleDeductMoney() gotDates = %v, want %v", gotDates, tt.wantDates)
			}
		})
	}
}

func TestManager_onlyGenPdf(t *testing.T) {
	type fields struct {
		cfgCenter        settlement.IConfigCenter
		cacheClient      cache.ISettlementBillCache
		mysqlStore       mysql.IStore
		sc               conf.IServiceConfigT
		oaCli            oa.IClient
		accountCli       account.IClient
		exchangeGuildCli Exchange.IGuildClient
		guildCli         guild.IClient
		realNameAuthCli  realnameauth.IClient
		commissionCli    *CommissionCli
	}
	type args struct {
		ctx  context.Context
		bill *mysql.SettlementBill
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    string
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &Manager{
				cfgCenter:        tt.fields.cfgCenter,
				cacheClient:      tt.fields.cacheClient,
				mysqlStore:       tt.fields.mysqlStore,
				sc:               tt.fields.sc,
				oaCli:            tt.fields.oaCli,
				accountCli:       tt.fields.accountCli,
				exchangeGuildCli: tt.fields.exchangeGuildCli,
				guildCli:         tt.fields.guildCli,
				realNameAuthCli:  tt.fields.realNameAuthCli,
				commissionCli:    tt.fields.commissionCli,
			}
			got, err := m.onlyGenPdf(tt.args.ctx, tt.args.bill)
			if (err != nil) != tt.wantErr {
				t.Errorf("onlyGenPdf() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("onlyGenPdf() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestManager_CreateSettlementBillBefore(t *testing.T) {
	type fields struct {
		cfgCenter        settlement.IConfigCenter
		cacheClient      cache.ISettlementBillCache
		mysqlStore       mysql.IStore
		sc               conf.IServiceConfigT
		oaCli            oa.IClient
		accountCli       account.IClient
		exchangeGuildCli Exchange.IGuildClient
		guildCli         guild.IClient
		realNameAuthCli  realnameauth.IClient
		commissionCli    *CommissionCli
		PdfGenCli        pdf.IGenClient
	}
	type args struct {
		ctx context.Context
		req *pb.CreateSettlementBillReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *model.SettlementMeta
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &Manager{
				cfgCenter:        tt.fields.cfgCenter,
				cacheClient:      tt.fields.cacheClient,
				mysqlStore:       tt.fields.mysqlStore,
				sc:               tt.fields.sc,
				oaCli:            tt.fields.oaCli,
				accountCli:       tt.fields.accountCli,
				exchangeGuildCli: tt.fields.exchangeGuildCli,
				guildCli:         tt.fields.guildCli,
				realNameAuthCli:  tt.fields.realNameAuthCli,
				commissionCli:    tt.fields.commissionCli,
				PdfGenCli:        tt.fields.PdfGenCli,
			}
			got, err := m.CreateSettlementBillBefore(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("CreateSettlementBillBefore() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("CreateSettlementBillBefore() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestManager_HandleMergeBill(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockStore := mocks.NewMockIStore(ctl)
	// mockConfig := mocks.NewMockIServiceConfigT(ctl)
	// mockOA := mocksOA.NewMockIClient(ctl)

	billType := pb.SettlementBillType_YuyinSubsidy
	billId := "123456"
	meta := &model.SettlementMeta{
		BillType: billType,
	}
	originBill := &mysql.SettlementBill{
		BillId:       billId,
		FinishedTime: utils.NowTime(),
	}

	// ctx := context.Background()

	type fields struct {
		cfgCenter        settlement.IConfigCenter
		cacheClient      cache.ISettlementBillCache
		mysqlStore       mysql.IStore
		sc               conf.IServiceConfigT
		oaCli            oa.IClient
		accountCli       account.IClient
		exchangeGuildCli Exchange.IGuildClient
		guildCli         guild.IClient
		realNameAuthCli  realnameauth.IClient
		commissionCli    *CommissionCli
		PdfGenCli        pdf.IGenClient
	}
	type args struct {
		ctx        context.Context
		meta       *model.SettlementMeta
		originBill *mysql.SettlementBill
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{name: "HandleMergeBill",
			fields: fields{
				mysqlStore: mockStore,
			},
			args:    args{ctx: context.Background(), meta: meta, originBill: originBill},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &Manager{
				cfgCenter:        tt.fields.cfgCenter,
				cacheClient:      tt.fields.cacheClient,
				mysqlStore:       tt.fields.mysqlStore,
				sc:               tt.fields.sc,
				oaCli:            tt.fields.oaCli,
				accountCli:       tt.fields.accountCli,
				exchangeGuildCli: tt.fields.exchangeGuildCli,
				guildCli:         tt.fields.guildCli,
				realNameAuthCli:  tt.fields.realNameAuthCli,
				commissionCli:    tt.fields.commissionCli,
				PdfGenCli:        tt.fields.PdfGenCli,
			}
			if err := m.HandleMergeBill(tt.args.ctx, tt.args.meta, tt.args.originBill); (err != nil) != tt.wantErr {
				t.Errorf("HandleMergeBill() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestManager_getWaitSettleSubsidyMonth(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	uid := uint32(123456)
	settleStartMonth := uint32(202208)
	settleEndMonth := uint32(202208)
	months := []uint32{202208}

	mockStore := mocks.NewMockIStore(ctl)
	mockConfig := mocks.NewMockIServiceConfigT(ctl)
	mockOA := mocksOA.NewMockIClient(ctl)

	ctx := context.Background()
	gomock.InOrder(
		mockStore.EXPECT().GetWaitSettleAnchorSubsidyByRange(ctx, uid, settleStartMonth, settleEndMonth).
			Return([]*mysql.SettleAnchorSubsidy{
				{SettlementDate: 202208},
			}, nil),
	)

	type fields struct {
		cfgCenter        settlement.IConfigCenter
		cacheClient      cache.ISettlementBillCache
		mysqlStore       mysql.IStore
		sc               conf.IServiceConfigT
		oaCli            oa.IClient
		accountCli       account.IClient
		exchangeGuildCli Exchange.IGuildClient
		guildCli         guild.IClient
		realNameAuthCli  realnameauth.IClient
		commissionCli    *CommissionCli
		PdfGenCli        pdf.IGenClient
	}
	type args struct {
		ctx              context.Context
		uid              uint32
		settleStartMonth uint32
		settleEndMonth   uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    []uint32
		wantErr bool
	}{
		{name: "getWaitSettleSubsidyMonth",
			fields: fields{mysqlStore: mockStore,
				sc:    mockConfig,
				oaCli: mockOA,
			},
			args:    args{ctx: context.Background(), uid: uid, settleStartMonth: settleStartMonth, settleEndMonth: settleEndMonth},
			want:    months,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &Manager{
				cfgCenter:        tt.fields.cfgCenter,
				cacheClient:      tt.fields.cacheClient,
				mysqlStore:       tt.fields.mysqlStore,
				sc:               tt.fields.sc,
				oaCli:            tt.fields.oaCli,
				accountCli:       tt.fields.accountCli,
				exchangeGuildCli: tt.fields.exchangeGuildCli,
				guildCli:         tt.fields.guildCli,
				realNameAuthCli:  tt.fields.realNameAuthCli,
				commissionCli:    tt.fields.commissionCli,
				PdfGenCli:        tt.fields.PdfGenCli,
			}
			got, err := m.getWaitSettleSubsidyMonth(tt.args.ctx, tt.args.uid, tt.args.settleStartMonth, tt.args.settleEndMonth)
			if (err != nil) != tt.wantErr {
				t.Errorf("getWaitSettleSubsidyMonth() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("getWaitSettleSubsidyMonth() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestManager_commissionAmuseExtraChecker(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	uid := uint32(123456)
	settleStartMonth := uint32(202208)
	meta := &model.SettlementMeta{
		Uid:              uid,
		SettleStartMonth: settleStartMonth,
	}

	mockStore := mocks.NewMockIStore(ctl)

	ctx := context.Background()
	gomock.InOrder(
		mockStore.EXPECT().GetAmuseExtraPrepaid(ctx, settleStartMonth, uid, uint8(mysql.IncomeSettleStatusWaiting)).
			Return(&mysql.SettleAmuseExtraPrepaid{}, nil),
	)

	type fields struct {
		cfgCenter        settlement.IConfigCenter
		cacheClient      cache.ISettlementBillCache
		mysqlStore       mysql.IStore
		sc               conf.IServiceConfigT
		oaCli            oa.IClient
		accountCli       account.IClient
		exchangeGuildCli Exchange.IGuildClient
		exchangeCli      Exchange.IClient
		guildCli         guild.IClient
		realNameAuthCli  realnameauth.IClient
		commissionCli    *CommissionCli
		PdfGenCli        pdf.IGenClient
	}
	type args struct {
		ctx  context.Context
		meta *model.SettlementMeta
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{name: "commissionAmuseExtraChecker",
			fields:  fields{mysqlStore: mockStore},
			args:    args{ctx: context.Background(), meta: meta},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &Manager{
				cfgCenter:        tt.fields.cfgCenter,
				cacheClient:      tt.fields.cacheClient,
				mysqlStore:       tt.fields.mysqlStore,
				sc:               tt.fields.sc,
				oaCli:            tt.fields.oaCli,
				accountCli:       tt.fields.accountCli,
				exchangeGuildCli: tt.fields.exchangeGuildCli,
				exchangeCli:      tt.fields.exchangeCli,
				guildCli:         tt.fields.guildCli,
				realNameAuthCli:  tt.fields.realNameAuthCli,
				commissionCli:    tt.fields.commissionCli,
				PdfGenCli:        tt.fields.PdfGenCli,
			}
			if err := m.commissionAmuseExtraChecker(tt.args.ctx, tt.args.meta); (err != nil) != tt.wantErr {
				t.Errorf("commissionAmuseExtraChecker() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestManager_commissionChecker(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	uid := uint32(123456)
	now := time.Now()
	settleStartMonth := utils.GetMonthInt(now.AddDate(-1, 0, 0))
	settleEndMonth := utils.GetMonthInt(now)
	billType := pb.SettlementBillType_AmuseCommission
	meta := &model.SettlementMeta{
		Uid:              uid,
		BillType:         billType,
		SettleStartMonth: settleStartMonth,
		SettleEndMonth:   settleEndMonth,
	}

	mockStore := mocks.NewMockIStore(ctl)

	ctx := context.Background()
	gomock.InOrder(
		mockStore.EXPECT().GetWaitSettleDeductByRange(ctx, uid, settleStartMonth, settleEndMonth, billType).
			Return([]*mysql.SettleDeductMoney{
				{},
			}, nil),
	)

	type fields struct {
		cfgCenter        settlement.IConfigCenter
		cacheClient      cache.ISettlementBillCache
		mysqlStore       mysql.IStore
		sc               conf.IServiceConfigT
		oaCli            oa.IClient
		accountCli       account.IClient
		exchangeGuildCli Exchange.IGuildClient
		exchangeCli      Exchange.IClient
		guildCli         guild.IClient
		realNameAuthCli  realnameauth.IClient
		commissionCli    *CommissionCli
		PdfGenCli        pdf.IGenClient
	}
	type args struct {
		ctx  context.Context
		meta *model.SettlementMeta
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{name: "commissionChecker",
			fields:  fields{mysqlStore: mockStore},
			args:    args{ctx: context.Background(), meta: meta},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &Manager{
				cfgCenter:        tt.fields.cfgCenter,
				cacheClient:      tt.fields.cacheClient,
				mysqlStore:       tt.fields.mysqlStore,
				sc:               tt.fields.sc,
				oaCli:            tt.fields.oaCli,
				accountCli:       tt.fields.accountCli,
				exchangeGuildCli: tt.fields.exchangeGuildCli,
				exchangeCli:      tt.fields.exchangeCli,
				guildCli:         tt.fields.guildCli,
				realNameAuthCli:  tt.fields.realNameAuthCli,
				commissionCli:    tt.fields.commissionCli,
				PdfGenCli:        tt.fields.PdfGenCli,
			}
			if err := m.commissionChecker(tt.args.ctx, tt.args.meta); (err != nil) != tt.wantErr {
				t.Errorf("commissionChecker() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestManager_subsidyChecker(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	uid := uint32(123456)
	now := time.Now()
	settleStartMonth := utils.GetMonthInt(now.AddDate(-1, 0, 0))
	settleEndMonth := utils.GetMonthInt(now)
	billType := pb.SettlementBillType_YuyinSubsidy
	meta := &model.SettlementMeta{
		Uid:              uid,
		BillType:         billType,
		SettleStartMonth: settleStartMonth,
		SettleEndMonth:   settleEndMonth,
	}

	mockStore := mocks.NewMockIStore(ctl)

	ctx := context.Background()
	gomock.InOrder(
		mockStore.EXPECT().GetWaitSettleAnchorSubsidyByRange(ctx, uid, settleStartMonth, settleEndMonth).
			Return([]*mysql.SettleAnchorSubsidy{
				{},
			}, nil),
	)

	type fields struct {
		cfgCenter        settlement.IConfigCenter
		cacheClient      cache.ISettlementBillCache
		mysqlStore       mysql.IStore
		sc               conf.IServiceConfigT
		oaCli            oa.IClient
		accountCli       account.IClient
		exchangeGuildCli Exchange.IGuildClient
		exchangeCli      Exchange.IClient
		guildCli         guild.IClient
		realNameAuthCli  realnameauth.IClient
		commissionCli    *CommissionCli
		PdfGenCli        pdf.IGenClient
	}
	type args struct {
		ctx  context.Context
		meta *model.SettlementMeta
		req  *pb.CreateSettlementBillReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{name: "subsidyChecker",
			fields: fields{mysqlStore: mockStore},
			args: args{ctx: context.Background(), meta: meta, req: &pb.CreateSettlementBillReq{
				YuyinSubsidy: &pb.YuyinSubsidyBill{
					YuyinAnchorSubsidy: 100,
				},
			}},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &Manager{
				cfgCenter:        tt.fields.cfgCenter,
				cacheClient:      tt.fields.cacheClient,
				mysqlStore:       tt.fields.mysqlStore,
				sc:               tt.fields.sc,
				oaCli:            tt.fields.oaCli,
				accountCli:       tt.fields.accountCli,
				exchangeGuildCli: tt.fields.exchangeGuildCli,
				exchangeCli:      tt.fields.exchangeCli,
				guildCli:         tt.fields.guildCli,
				realNameAuthCli:  tt.fields.realNameAuthCli,
				commissionCli:    tt.fields.commissionCli,
				PdfGenCli:        tt.fields.PdfGenCli,
			}
			if err := m.subsidyChecker(tt.args.ctx, tt.args.meta, tt.args.req); (err != nil) != tt.wantErr {
				t.Errorf("subsidyChecker() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
