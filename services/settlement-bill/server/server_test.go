package server

import (
	"context"
	"github.com/golang/mock/gomock"
	"golang.52tt.com/pkg/config"
	pb "golang.52tt.com/protocol/services/settlement-bill"
	"golang.52tt.com/services/settlement-bill/cache"
	"golang.52tt.com/services/settlement-bill/conf"
	"golang.52tt.com/services/settlement-bill/manager"
	"golang.52tt.com/services/settlement-bill/mocks"
	"golang.52tt.com/services/settlement-bill/mysql"
	"reflect"
	"time"

	// "golang.52tt.com/services/settlement-bill/utils/export"
	"testing"
)

func TestNewSettlementBillServer(t *testing.T) {
	type args struct {
		ctx context.Context
		cfg config.Configer
	}
	tests := []struct {
		name    string
		args    args
		want    *SettlementBillServer
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := NewSettlementBillServer(tt.args.ctx, tt.args.cfg)
			if (err != nil) != tt.wantErr {
				t.Errorf("NewSettlementBillServer() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("NewSettlementBillServer() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestSettlementBillServer_AssociateReceipt(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockStore := mocks.NewMockIStore(ctl)
	mockCache := mocks.NewMockISettlementBillCache(ctl)
	mockConfig := mocks.NewMockIServiceConfigT(ctl)
	// mockOA := oa.NewMockIClient(ctl)
	mockManager := mocks.NewMockIManager(ctl)

	gomock.InOrder(
		mockManager.EXPECT().AssociateReceipt(context.Background(), &pb.AssociateReceiptReq{
			Uid: 123456,
		}).Return(&pb.AssociateReceiptResp{}, nil),
	)

	type fields struct {
		sc          conf.IServiceConfigT
		cacheClient cache.ISettlementBillCache
		mysqlStore  mysql.IStore
		mgr         manager.IManager
	}
	type args struct {
		c   context.Context
		req *pb.AssociateReceiptReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.AssociateReceiptResp
		wantErr bool
	}{
		{name: "AssociateReceiptResp",
			fields: fields{mysqlStore: mockStore, cacheClient: mockCache, sc: mockConfig, mgr: mockManager},
			args: args{c: context.Background(), req: &pb.AssociateReceiptReq{
				Uid: 123456,
			}},
			want: &pb.AssociateReceiptResp{},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &SettlementBillServer{
				sc:          tt.fields.sc,
				cacheClient: tt.fields.cacheClient,
				mysqlStore:  tt.fields.mysqlStore,
				mgr:         tt.fields.mgr,
			}
			got, err := s.AssociateReceipt(tt.args.c, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("AssociateReceipt() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("AssociateReceipt() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestSettlementBillServer_ConfirmWithdraw(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockStore := mocks.NewMockIStore(ctl)
	mockCache := mocks.NewMockISettlementBillCache(ctl)
	mockConfig := mocks.NewMockIServiceConfigT(ctl)
	// mockOA := oa.NewMockIClient(ctl)
	mockManager := mocks.NewMockIManager(ctl)

	gomock.InOrder(
		mockManager.EXPECT().ConfirmWithdraw(context.Background(), &pb.ConfirmWithdrawReq{
			BillId: "123456",
		}).Return(&pb.ConfirmWithdrawResp{}, nil),
	)

	type fields struct {
		sc          conf.IServiceConfigT
		cacheClient cache.ISettlementBillCache
		mysqlStore  mysql.IStore
		mgr         manager.IManager
	}
	type args struct {
		c   context.Context
		req *pb.ConfirmWithdrawReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.ConfirmWithdrawResp
		wantErr bool
	}{
		{name: "ConfirmWithdraw",
			fields: fields{mysqlStore: mockStore, cacheClient: mockCache, sc: mockConfig, mgr: mockManager},
			args: args{c: context.Background(), req: &pb.ConfirmWithdrawReq{
				BillId: "123456",
			}},
			want: &pb.ConfirmWithdrawResp{},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &SettlementBillServer{
				sc:          tt.fields.sc,
				cacheClient: tt.fields.cacheClient,
				mysqlStore:  tt.fields.mysqlStore,
				mgr:         tt.fields.mgr,
			}
			got, err := s.ConfirmWithdraw(tt.args.c, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("ConfirmWithdraw() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("ConfirmWithdraw() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestSettlementBillServer_CreateSettlementBill(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockStore := mocks.NewMockIStore(ctl)
	mockCache := mocks.NewMockISettlementBillCache(ctl)
	mockConfig := mocks.NewMockIServiceConfigT(ctl)
	// mockOA := oa.NewMockIClient(ctl)
	mockManager := mocks.NewMockIManager(ctl)

	gomock.InOrder(
		mockManager.EXPECT().CreateSettlementBill(context.Background(), &pb.CreateSettlementBillReq{
			BillType:    pb.SettlementBillType_GiftScore,
			Uid:         123456,
			SettleStart: 0,
			SettleEnd:   uint32(time.Now().Unix()),
			LastBalance: 0,
		}).Return(&pb.CreateSettlementBillResp{}, nil),
	)

	type fields struct {
		sc          conf.IServiceConfigT
		cacheClient cache.ISettlementBillCache
		mysqlStore  mysql.IStore
		mgr         manager.IManager
	}
	type args struct {
		c   context.Context
		req *pb.CreateSettlementBillReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.CreateSettlementBillResp
		wantErr bool
	}{
		{name: "CreateSettlementBill",
			fields: fields{mysqlStore: mockStore, cacheClient: mockCache, sc: mockConfig, mgr: mockManager},
			args: args{c: context.Background(), req: &pb.CreateSettlementBillReq{
				BillType:    pb.SettlementBillType_GiftScore,
				Uid:         123456,
				SettleStart: 0,
				SettleEnd:   uint32(time.Now().Unix()),
				LastBalance: 0,
			}},
			want: &pb.CreateSettlementBillResp{},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &SettlementBillServer{
				sc:          tt.fields.sc,
				cacheClient: tt.fields.cacheClient,
				mysqlStore:  tt.fields.mysqlStore,
				mgr:         tt.fields.mgr,
			}
			got, err := s.CreateSettlementBill(tt.args.c, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("CreateSettlementBill() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("CreateSettlementBill() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestSettlementBillServer_GenSettleBillPdf(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockStore := mocks.NewMockIStore(ctl)
	mockCache := mocks.NewMockISettlementBillCache(ctl)
	mockConfig := mocks.NewMockIServiceConfigT(ctl)
	// mockOA := oa.NewMockIClient(ctl)
	mockManager := mocks.NewMockIManager(ctl)

	gomock.InOrder(
		mockManager.EXPECT().GenSettleBillPdf(context.Background(), &pb.GenSettleBillPdfReq{
			BillId: "123456",
		}).Return(&pb.GenSettleBillPdfResp{}, nil),
	)

	type fields struct {
		sc          conf.IServiceConfigT
		cacheClient cache.ISettlementBillCache
		mysqlStore  mysql.IStore
		mgr         manager.IManager
	}
	type args struct {
		c   context.Context
		req *pb.GenSettleBillPdfReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.GenSettleBillPdfResp
		wantErr bool
	}{
		{name: "GenSettleBillPdf",
			fields: fields{mysqlStore: mockStore, cacheClient: mockCache, sc: mockConfig, mgr: mockManager},
			args: args{c: context.Background(), req: &pb.GenSettleBillPdfReq{
				BillId: "123456",
			}},
			want: &pb.GenSettleBillPdfResp{},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &SettlementBillServer{
				sc:          tt.fields.sc,
				cacheClient: tt.fields.cacheClient,
				mysqlStore:  tt.fields.mysqlStore,
				mgr:         tt.fields.mgr,
			}
			got, err := s.GenSettleBillPdf(tt.args.c, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GenSettleBillPdf() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GenSettleBillPdf() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestSettlementBillServer_GetAssociatedBillItems(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockStore := mocks.NewMockIStore(ctl)
	mockCache := mocks.NewMockISettlementBillCache(ctl)
	mockConfig := mocks.NewMockIServiceConfigT(ctl)
	// mockOA := oa.NewMockIClient(ctl)
	mockManager := mocks.NewMockIManager(ctl)

	gomock.InOrder(
		mockManager.EXPECT().GetAssociatedBillItems(context.Background(), &pb.GetAssociatedBillItemsReq{
			Uid:           123456,
			ReceiptBillId: "123456",
		}).Return(&pb.GetAssociatedBillItemsResp{}, nil),
	)

	type fields struct {
		sc          conf.IServiceConfigT
		cacheClient cache.ISettlementBillCache
		mysqlStore  mysql.IStore
		mgr         manager.IManager
	}
	type args struct {
		c   context.Context
		req *pb.GetAssociatedBillItemsReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.GetAssociatedBillItemsResp
		wantErr bool
	}{
		{name: "GetAssociatedBillItems",
			fields: fields{mysqlStore: mockStore, cacheClient: mockCache, sc: mockConfig, mgr: mockManager},
			args: args{c: context.Background(), req: &pb.GetAssociatedBillItemsReq{
				Uid:           123456,
				ReceiptBillId: "123456",
			}},
			want: &pb.GetAssociatedBillItemsResp{},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &SettlementBillServer{
				sc:          tt.fields.sc,
				cacheClient: tt.fields.cacheClient,
				mysqlStore:  tt.fields.mysqlStore,
				mgr:         tt.fields.mgr,
			}
			got, err := s.GetAssociatedBillItems(tt.args.c, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetAssociatedBillItems() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetAssociatedBillItems() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestSettlementBillServer_GetDeductMoney(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	type fields struct {
		sc          conf.IServiceConfigT
		cacheClient cache.ISettlementBillCache
		mysqlStore  mysql.IStore
		mgr         manager.IManager
	}
	type args struct {
		c   context.Context
		req *pb.GetDeductMoneyReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.GetDeductMoneyResp
		wantErr bool
	}{}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &SettlementBillServer{
				sc:          tt.fields.sc,
				cacheClient: tt.fields.cacheClient,
				mysqlStore:  tt.fields.mysqlStore,
				mgr:         tt.fields.mgr,
			}
			got, err := s.GetDeductMoney(tt.args.c, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetDeductMoney() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetDeductMoney() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestSettlementBillServer_GetDeductMoneyDetailByBillId(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockStore := mocks.NewMockIStore(ctl)
	mockCache := mocks.NewMockISettlementBillCache(ctl)
	mockConfig := mocks.NewMockIServiceConfigT(ctl)
	// mockOA := oa.NewMockIClient(ctl)
	mockManager := mocks.NewMockIManager(ctl)

	gomock.InOrder(
		mockManager.EXPECT().GetDeductMoneyDetailByBillId(context.Background(), &pb.GetDeductMoneyListByBillIdReq{
			BillId: "123456",
		}).Return(&pb.GetDeductMoneyListByBillIdResp{}, nil),
	)

	type fields struct {
		sc          conf.IServiceConfigT
		cacheClient cache.ISettlementBillCache
		mysqlStore  mysql.IStore
		mgr         manager.IManager
	}
	type args struct {
		c   context.Context
		req *pb.GetDeductMoneyListByBillIdReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.GetDeductMoneyListByBillIdResp
		wantErr bool
	}{
		{name: "GetDeductMoneyDetailByBillId",
			fields: fields{mysqlStore: mockStore, cacheClient: mockCache, sc: mockConfig, mgr: mockManager},
			args: args{c: context.Background(), req: &pb.GetDeductMoneyListByBillIdReq{
				BillId: "123456",
			}},
			want: &pb.GetDeductMoneyListByBillIdResp{},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &SettlementBillServer{
				sc:          tt.fields.sc,
				cacheClient: tt.fields.cacheClient,
				mysqlStore:  tt.fields.mysqlStore,
				mgr:         tt.fields.mgr,
			}
			got, err := s.GetDeductMoneyDetailByBillId(tt.args.c, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetDeductMoneyDetailByBillId() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetDeductMoneyDetailByBillId() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestSettlementBillServer_GetExtraIncomeDetailChannelSubsidy(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockStore := mocks.NewMockIStore(ctl)
	mockCache := mocks.NewMockISettlementBillCache(ctl)
	mockConfig := mocks.NewMockIServiceConfigT(ctl)
	// mockOA := oa.NewMockIClient(ctl)
	mockManager := mocks.NewMockIManager(ctl)

	gomock.InOrder(
		mockManager.EXPECT().GetExtraIncomeDetailChannelSubsidy(context.Background(), &pb.GetExtraIncomeDetailReq{
			Uid:    123456,
			Offset: 0,
			Limit:  10,
		}).Return(&pb.GetExtraIncomeDetailChannelSubsidyResp{}, nil),
	)

	type fields struct {
		sc          conf.IServiceConfigT
		cacheClient cache.ISettlementBillCache
		mysqlStore  mysql.IStore
		mgr         manager.IManager
	}
	type args struct {
		c   context.Context
		req *pb.GetExtraIncomeDetailReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.GetExtraIncomeDetailChannelSubsidyResp
		wantErr bool
	}{
		{name: "GetExtraIncomeDetailChannelSubsidy",
			fields: fields{mysqlStore: mockStore, cacheClient: mockCache, sc: mockConfig, mgr: mockManager},
			args: args{c: context.Background(), req: &pb.GetExtraIncomeDetailReq{
				Uid:    123456,
				Offset: 0,
				Limit:  10,
			}},
			want: &pb.GetExtraIncomeDetailChannelSubsidyResp{},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &SettlementBillServer{
				sc:          tt.fields.sc,
				cacheClient: tt.fields.cacheClient,
				mysqlStore:  tt.fields.mysqlStore,
				mgr:         tt.fields.mgr,
			}
			got, err := s.GetExtraIncomeDetailChannelSubsidy(tt.args.c, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetExtraIncomeDetailChannelSubsidy() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetExtraIncomeDetailChannelSubsidy() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestSettlementBillServer_GetExtraIncomeDetailDeduct(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockStore := mocks.NewMockIStore(ctl)
	mockCache := mocks.NewMockISettlementBillCache(ctl)
	mockConfig := mocks.NewMockIServiceConfigT(ctl)
	// mockOA := oa.NewMockIClient(ctl)
	mockManager := mocks.NewMockIManager(ctl)

	gomock.InOrder(
		mockManager.EXPECT().GetExtraIncomeDetailDeduct(context.Background(), &pb.GetExtraIncomeDetailReq{
			Uid:    123456,
			Offset: 0,
			Limit:  10,
		}).Return(&pb.GetExtraIncomeDetailDeductResp{}, nil),
	)

	type fields struct {
		sc          conf.IServiceConfigT
		cacheClient cache.ISettlementBillCache
		mysqlStore  mysql.IStore
		mgr         manager.IManager
	}
	type args struct {
		c   context.Context
		req *pb.GetExtraIncomeDetailReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.GetExtraIncomeDetailDeductResp
		wantErr bool
	}{
		{name: "GetExtraIncomeDetailDeduct",
			fields: fields{mysqlStore: mockStore, cacheClient: mockCache, sc: mockConfig, mgr: mockManager},
			args: args{c: context.Background(), req: &pb.GetExtraIncomeDetailReq{
				Uid:    123456,
				Offset: 0,
				Limit:  10,
			}},
			want: &pb.GetExtraIncomeDetailDeductResp{},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &SettlementBillServer{
				sc:          tt.fields.sc,
				cacheClient: tt.fields.cacheClient,
				mysqlStore:  tt.fields.mysqlStore,
				mgr:         tt.fields.mgr,
			}
			got, err := s.GetExtraIncomeDetailDeduct(tt.args.c, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetExtraIncomeDetailDeduct() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetExtraIncomeDetailDeduct() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestSettlementBillServer_GetExtraIncomeDetailDeepCoop(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockStore := mocks.NewMockIStore(ctl)
	mockCache := mocks.NewMockISettlementBillCache(ctl)
	mockConfig := mocks.NewMockIServiceConfigT(ctl)
	// mockOA := oa.NewMockIClient(ctl)
	mockManager := mocks.NewMockIManager(ctl)

	gomock.InOrder(
		mockManager.EXPECT().GetExtraIncomeDetailDeepCoop(context.Background(), &pb.GetExtraIncomeDetailReq{
			Uid:            123456,
			Offset:         0,
			Limit:          10,
			SettlementDate: 0,
		}).Return(&pb.GetExtraIncomeDetailDeepCoopResp{}, nil),
	)

	type fields struct {
		sc          conf.IServiceConfigT
		cacheClient cache.ISettlementBillCache
		mysqlStore  mysql.IStore
		mgr         manager.IManager
	}
	type args struct {
		c   context.Context
		req *pb.GetExtraIncomeDetailReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.GetExtraIncomeDetailDeepCoopResp
		wantErr bool
	}{
		{name: "GetExtraIncomeDetailDeepCoop",
			fields: fields{mysqlStore: mockStore, cacheClient: mockCache, sc: mockConfig, mgr: mockManager},
			args: args{c: context.Background(), req: &pb.GetExtraIncomeDetailReq{
				Uid:    123456,
				Offset: 0,
				Limit:  10,
			}},
			want: &pb.GetExtraIncomeDetailDeepCoopResp{},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &SettlementBillServer{
				sc:          tt.fields.sc,
				cacheClient: tt.fields.cacheClient,
				mysqlStore:  tt.fields.mysqlStore,
				mgr:         tt.fields.mgr,
			}
			got, err := s.GetExtraIncomeDetailDeepCoop(tt.args.c, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetExtraIncomeDetailDeepCoop() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetExtraIncomeDetailDeepCoop() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestSettlementBillServer_GetExtraIncomeDetailNewGuildSubsidy(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockStore := mocks.NewMockIStore(ctl)
	mockCache := mocks.NewMockISettlementBillCache(ctl)
	mockConfig := mocks.NewMockIServiceConfigT(ctl)
	// mockOA := oa.NewMockIClient(ctl)
	mockManager := mocks.NewMockIManager(ctl)

	gomock.InOrder(
		mockManager.EXPECT().GetExtraIncomeDetailNewGuildSubsidy(context.Background(), &pb.GetExtraIncomeDetailReq{
			Uid:            123456,
			Offset:         0,
			Limit:          10,
			SettlementDate: 0,
		}).Return(&pb.GetExtraIncomeDetailNewGuildSubsidyResp{}, nil),
	)

	type fields struct {
		sc          conf.IServiceConfigT
		cacheClient cache.ISettlementBillCache
		mysqlStore  mysql.IStore
		mgr         manager.IManager
	}
	type args struct {
		c   context.Context
		req *pb.GetExtraIncomeDetailReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.GetExtraIncomeDetailNewGuildSubsidyResp
		wantErr bool
	}{
		{name: "GetExtraIncomeDetailNewGuildSubsidy",
			fields: fields{mysqlStore: mockStore, cacheClient: mockCache, sc: mockConfig, mgr: mockManager},
			args: args{c: context.Background(), req: &pb.GetExtraIncomeDetailReq{
				Uid:            123456,
				Offset:         0,
				Limit:          10,
				SettlementDate: 0,
			}},
			want: &pb.GetExtraIncomeDetailNewGuildSubsidyResp{},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &SettlementBillServer{
				sc:          tt.fields.sc,
				cacheClient: tt.fields.cacheClient,
				mysqlStore:  tt.fields.mysqlStore,
				mgr:         tt.fields.mgr,
			}
			got, err := s.GetExtraIncomeDetailNewGuildSubsidy(tt.args.c, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetExtraIncomeDetailNewGuildSubsidy() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetExtraIncomeDetailNewGuildSubsidy() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestSettlementBillServer_GetExtraIncomeRecordList(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockStore := mocks.NewMockIStore(ctl)
	mockCache := mocks.NewMockISettlementBillCache(ctl)
	mockConfig := mocks.NewMockIServiceConfigT(ctl)
	// mockOA := oa.NewMockIClient(ctl)
	mockManager := mocks.NewMockIManager(ctl)

	gomock.InOrder(
		mockManager.EXPECT().GetExtraIncomeRecordList(context.Background(), &pb.GetExtraIncomeRecordListReq{
			Uid:    123456,
			Offset: 0,
			Limit:  10,
		}).Return(&pb.GetExtraIncomeRecordListResp{}, nil),
	)

	type fields struct {
		sc          conf.IServiceConfigT
		cacheClient cache.ISettlementBillCache
		mysqlStore  mysql.IStore
		mgr         manager.IManager
	}
	type args struct {
		c   context.Context
		req *pb.GetExtraIncomeRecordListReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.GetExtraIncomeRecordListResp
		wantErr bool
	}{
		{name: "GetExtraIncomeRecordList",
			fields: fields{mysqlStore: mockStore, cacheClient: mockCache, sc: mockConfig, mgr: mockManager},
			args: args{c: context.Background(), req: &pb.GetExtraIncomeRecordListReq{
				Uid:    123456,
				Offset: 0,
				Limit:  10,
			}},
			want: &pb.GetExtraIncomeRecordListResp{},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &SettlementBillServer{
				sc:          tt.fields.sc,
				cacheClient: tt.fields.cacheClient,
				mysqlStore:  tt.fields.mysqlStore,
				mgr:         tt.fields.mgr,
			}
			got, err := s.GetExtraIncomeRecordList(tt.args.c, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetExtraIncomeRecordList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetExtraIncomeRecordList() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestSettlementBillServer_GetGuildTaxRate(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockStore := mocks.NewMockIStore(ctl)
	mockCache := mocks.NewMockISettlementBillCache(ctl)
	mockConfig := mocks.NewMockIServiceConfigT(ctl)
	// mockOA := oa.NewMockIClient(ctl)
	mockManager := mocks.NewMockIManager(ctl)

	gomock.InOrder(
		mockManager.EXPECT().GetGuildTaxRate(context.Background(), &pb.GetGuildTaxRateReq{
			Uid: 123456,
		}).Return(&pb.GetGuildTaxRateResp{}, nil),
	)

	type fields struct {
		sc          conf.IServiceConfigT
		cacheClient cache.ISettlementBillCache
		mysqlStore  mysql.IStore
		mgr         manager.IManager
	}
	type args struct {
		c   context.Context
		req *pb.GetGuildTaxRateReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.GetGuildTaxRateResp
		wantErr bool
	}{
		{name: "GetGuildTaxRate",
			fields: fields{mysqlStore: mockStore, cacheClient: mockCache, sc: mockConfig, mgr: mockManager},
			args: args{c: context.Background(), req: &pb.GetGuildTaxRateReq{
				Uid: 123456,
			}},
			want: &pb.GetGuildTaxRateResp{},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &SettlementBillServer{
				sc:          tt.fields.sc,
				cacheClient: tt.fields.cacheClient,
				mysqlStore:  tt.fields.mysqlStore,
				mgr:         tt.fields.mgr,
			}
			got, err := s.GetGuildTaxRate(tt.args.c, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetGuildTaxRate() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetGuildTaxRate() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestSettlementBillServer_GetMonthsBySettleBillId(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockStore := mocks.NewMockIStore(ctl)
	mockCache := mocks.NewMockISettlementBillCache(ctl)
	mockConfig := mocks.NewMockIServiceConfigT(ctl)
	// mockOA := oa.NewMockIClient(ctl)
	mockManager := mocks.NewMockIManager(ctl)

	gomock.InOrder(
		mockManager.EXPECT().GetMonthsBySettleBillId(context.Background(), &pb.GetMonthsBySettleBillIdReq{
			Uid:    123456,
			BillId: "123456",
		}).Return(&pb.GetMonthsBySettleBillIdResp{}, nil),
	)

	type fields struct {
		sc          conf.IServiceConfigT
		cacheClient cache.ISettlementBillCache
		mysqlStore  mysql.IStore
		mgr         manager.IManager
	}
	type args struct {
		c   context.Context
		req *pb.GetMonthsBySettleBillIdReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.GetMonthsBySettleBillIdResp
		wantErr bool
	}{
		{name: "GetMonthsBySettleBillId",
			fields: fields{mysqlStore: mockStore, cacheClient: mockCache, sc: mockConfig, mgr: mockManager},
			args: args{c: context.Background(), req: &pb.GetMonthsBySettleBillIdReq{
				Uid:    123456,
				BillId: "123456"}},
			want: &pb.GetMonthsBySettleBillIdResp{},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &SettlementBillServer{
				sc:          tt.fields.sc,
				cacheClient: tt.fields.cacheClient,
				mysqlStore:  tt.fields.mysqlStore,
				mgr:         tt.fields.mgr,
			}
			got, err := s.GetMonthsBySettleBillId(tt.args.c, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetMonthsBySettleBillId() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetMonthsBySettleBillId() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestSettlementBillServer_GetPrepaidMoney(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	type fields struct {
		sc          conf.IServiceConfigT
		cacheClient cache.ISettlementBillCache
		mysqlStore  mysql.IStore
		mgr         manager.IManager
	}
	type args struct {
		c   context.Context
		req *pb.GetPrepaidMoneyReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.GetPrepaidMoneyResp
		wantErr bool
	}{}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &SettlementBillServer{
				sc:          tt.fields.sc,
				cacheClient: tt.fields.cacheClient,
				mysqlStore:  tt.fields.mysqlStore,
				mgr:         tt.fields.mgr,
			}
			got, err := s.GetPrepaidMoney(tt.args.c, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetPrepaidMoney() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetPrepaidMoney() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestSettlementBillServer_GetReceiptBillList(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockStore := mocks.NewMockIStore(ctl)
	mockCache := mocks.NewMockISettlementBillCache(ctl)
	mockConfig := mocks.NewMockIServiceConfigT(ctl)
	// mockOA := oa.NewMockIClient(ctl)
	mockManager := mocks.NewMockIManager(ctl)

	gomock.InOrder(
		mockManager.EXPECT().GetReceiptBillList(context.Background(), &pb.GetReceiptBillListReq{
			Uid:    123456,
			Offset: 0,
			Limit:  10,
		}).Return(&pb.GetReceiptBillListResp{}, nil),
	)

	type fields struct {
		sc          conf.IServiceConfigT
		cacheClient cache.ISettlementBillCache
		mysqlStore  mysql.IStore
		mgr         manager.IManager
	}
	type args struct {
		c   context.Context
		req *pb.GetReceiptBillListReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.GetReceiptBillListResp
		wantErr bool
	}{
		{name: "GetReceiptBillList",
			fields: fields{mysqlStore: mockStore, cacheClient: mockCache, sc: mockConfig, mgr: mockManager},
			args: args{c: context.Background(), req: &pb.GetReceiptBillListReq{
				Uid:    123456,
				Offset: 0,
				Limit:  10,
			}},
			want: &pb.GetReceiptBillListResp{},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &SettlementBillServer{
				sc:          tt.fields.sc,
				cacheClient: tt.fields.cacheClient,
				mysqlStore:  tt.fields.mysqlStore,
				mgr:         tt.fields.mgr,
			}
			got, err := s.GetReceiptBillList(tt.args.c, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetReceiptBillList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetReceiptBillList() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestSettlementBillServer_GetReceiptList(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockStore := mocks.NewMockIStore(ctl)
	mockCache := mocks.NewMockISettlementBillCache(ctl)
	mockConfig := mocks.NewMockIServiceConfigT(ctl)
	// mockOA := oa.NewMockIClient(ctl)
	mockManager := mocks.NewMockIManager(ctl)

	gomock.InOrder(
		mockManager.EXPECT().GetReceiptList(context.Background(), &pb.GetReceiptListReq{
			ReceiptBillId: "123456",
		}).Return(&pb.GetReceiptListResp{}, nil),
	)

	type fields struct {
		sc          conf.IServiceConfigT
		cacheClient cache.ISettlementBillCache
		mysqlStore  mysql.IStore
		mgr         manager.IManager
	}
	type args struct {
		c   context.Context
		req *pb.GetReceiptListReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.GetReceiptListResp
		wantErr bool
	}{
		{name: "GetReceiptList",
			fields: fields{mysqlStore: mockStore, cacheClient: mockCache, sc: mockConfig, mgr: mockManager},
			args: args{c: context.Background(), req: &pb.GetReceiptListReq{
				ReceiptBillId: "123456",
			}},
			want: &pb.GetReceiptListResp{},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &SettlementBillServer{
				sc:          tt.fields.sc,
				cacheClient: tt.fields.cacheClient,
				mysqlStore:  tt.fields.mysqlStore,
				mgr:         tt.fields.mgr,
			}
			got, err := s.GetReceiptList(tt.args.c, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetReceiptList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetReceiptList() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestSettlementBillServer_GetSettlementBill(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockStore := mocks.NewMockIStore(ctl)
	mockCache := mocks.NewMockISettlementBillCache(ctl)
	mockConfig := mocks.NewMockIServiceConfigT(ctl)
	// mockOA := oa.NewMockIClient(ctl)
	mockManager := mocks.NewMockIManager(ctl)

	gomock.InOrder(
		mockManager.EXPECT().GetSettlementBill(context.Background(), &pb.GetSettlementBillReq{
			Uid:    123456,
			BillId: "123456",
		}).Return(&pb.GetSettlementBillResp{}, nil),
	)

	type fields struct {
		sc          conf.IServiceConfigT
		cacheClient cache.ISettlementBillCache
		mysqlStore  mysql.IStore
		mgr         manager.IManager
	}
	type args struct {
		c   context.Context
		req *pb.GetSettlementBillReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.GetSettlementBillResp
		wantErr bool
	}{
		{name: "GetSettlementBill",
			fields: fields{mysqlStore: mockStore, cacheClient: mockCache, sc: mockConfig, mgr: mockManager},
			args: args{c: context.Background(), req: &pb.GetSettlementBillReq{
				Uid:    123456,
				BillId: "123456",
			}},
			want: &pb.GetSettlementBillResp{},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &SettlementBillServer{
				sc:          tt.fields.sc,
				cacheClient: tt.fields.cacheClient,
				mysqlStore:  tt.fields.mysqlStore,
				mgr:         tt.fields.mgr,
			}
			got, err := s.GetSettlementBill(tt.args.c, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetSettlementBill() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetSettlementBill() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestSettlementBillServer_GetSettlementBillWaitReceipt(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockStore := mocks.NewMockIStore(ctl)
	mockCache := mocks.NewMockISettlementBillCache(ctl)
	mockConfig := mocks.NewMockIServiceConfigT(ctl)
	// mockOA := oa.NewMockIClient(ctl)
	mockManager := mocks.NewMockIManager(ctl)

	gomock.InOrder(
		mockManager.EXPECT().GetSettlementBillWaitReceipt(context.Background(), &pb.GetSettlementBillWaitReceiptReq{
			Uid: 123456,
		}).Return(&pb.GetSettlementBillWaitReceiptResp{}, nil),
	)

	type fields struct {
		sc          conf.IServiceConfigT
		cacheClient cache.ISettlementBillCache
		mysqlStore  mysql.IStore
		mgr         manager.IManager
	}
	type args struct {
		c   context.Context
		req *pb.GetSettlementBillWaitReceiptReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.GetSettlementBillWaitReceiptResp
		wantErr bool
	}{
		{name: "GetSettlementBillWaitReceipt",
			fields: fields{mysqlStore: mockStore, cacheClient: mockCache, sc: mockConfig, mgr: mockManager},
			args: args{c: context.Background(), req: &pb.GetSettlementBillWaitReceiptReq{
				Uid: 123456,
			}},
			want: &pb.GetSettlementBillWaitReceiptResp{},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &SettlementBillServer{
				sc:          tt.fields.sc,
				cacheClient: tt.fields.cacheClient,
				mysqlStore:  tt.fields.mysqlStore,
				mgr:         tt.fields.mgr,
			}
			got, err := s.GetSettlementBillWaitReceipt(tt.args.c, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetSettlementBillWaitReceipt() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetSettlementBillWaitReceipt() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestSettlementBillServer_GetSettlementBillWaitWithdraw(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockStore := mocks.NewMockIStore(ctl)
	mockCache := mocks.NewMockISettlementBillCache(ctl)
	mockConfig := mocks.NewMockIServiceConfigT(ctl)
	// mockOA := oa.NewMockIClient(ctl)
	mockManager := mocks.NewMockIManager(ctl)

	gomock.InOrder(
		mockManager.EXPECT().GetSettlementBillWaitWithdraw(context.Background(), &pb.GetSettlementBillWaitWithdrawReq{
			Uid: 123456,
		}).Return(&pb.GetSettlementBillWaitWithdrawResp{}, nil),
	)

	type fields struct {
		sc          conf.IServiceConfigT
		cacheClient cache.ISettlementBillCache
		mysqlStore  mysql.IStore
		mgr         manager.IManager
	}
	type args struct {
		c   context.Context
		req *pb.GetSettlementBillWaitWithdrawReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.GetSettlementBillWaitWithdrawResp
		wantErr bool
	}{
		{name: "GetSettlementBillWaitWithdraw",
			fields: fields{mysqlStore: mockStore, cacheClient: mockCache, sc: mockConfig, mgr: mockManager},
			args: args{c: context.Background(), req: &pb.GetSettlementBillWaitWithdrawReq{
				Uid: 123456,
			}},
			want: &pb.GetSettlementBillWaitWithdrawResp{},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &SettlementBillServer{
				sc:          tt.fields.sc,
				cacheClient: tt.fields.cacheClient,
				mysqlStore:  tt.fields.mysqlStore,
				mgr:         tt.fields.mgr,
			}
			got, err := s.GetSettlementBillWaitWithdraw(tt.args.c, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetSettlementBillWaitWithdraw() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetSettlementBillWaitWithdraw() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestSettlementBillServer_GetTaxRateList(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockStore := mocks.NewMockIStore(ctl)
	mockCache := mocks.NewMockISettlementBillCache(ctl)
	mockConfig := mocks.NewMockIServiceConfigT(ctl)
	// mockOA := oa.NewMockIClient(ctl)
	mockManager := mocks.NewMockIManager(ctl)

	gomock.InOrder(
		mockManager.EXPECT().GetTaxRateList(context.Background(), &pb.GetTaxRateListReq{
			Uid:    123456,
			Offset: 0,
			Limit:  10,
		}).Return(&pb.GetTaxRateListResp{}, nil),
	)

	type fields struct {
		sc          conf.IServiceConfigT
		cacheClient cache.ISettlementBillCache
		mysqlStore  mysql.IStore
		mgr         manager.IManager
	}
	type args struct {
		c   context.Context
		req *pb.GetTaxRateListReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.GetTaxRateListResp
		wantErr bool
	}{
		{name: "GetTaxRateList",
			fields: fields{mysqlStore: mockStore, cacheClient: mockCache, sc: mockConfig, mgr: mockManager},
			args: args{c: context.Background(), req: &pb.GetTaxRateListReq{
				Uid:    123456,
				Offset: 0,
				Limit:  10,
			}},
			want: &pb.GetTaxRateListResp{},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &SettlementBillServer{
				sc:          tt.fields.sc,
				cacheClient: tt.fields.cacheClient,
				mysqlStore:  tt.fields.mysqlStore,
				mgr:         tt.fields.mgr,
			}
			got, err := s.GetTaxRateList(tt.args.c, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetTaxRateList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetTaxRateList() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestSettlementBillServer_GetWaitWithdrawDeepCoop(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockStore := mocks.NewMockIStore(ctl)
	mockCache := mocks.NewMockISettlementBillCache(ctl)
	mockConfig := mocks.NewMockIServiceConfigT(ctl)
	// mockOA := oa.NewMockIClient(ctl)
	mockManager := mocks.NewMockIManager(ctl)

	gomock.InOrder(
		mockManager.EXPECT().GetWaitWithdrawDeepCoop(context.Background(), &pb.GetWaitWithdrawDeepCoopReq{
			Uid: 123456,
		}).Return(&pb.GetWaitWithdrawDeepCoopResp{}, nil),
	)

	type fields struct {
		sc          conf.IServiceConfigT
		cacheClient cache.ISettlementBillCache
		mysqlStore  mysql.IStore
		mgr         manager.IManager
	}
	type args struct {
		c   context.Context
		req *pb.GetWaitWithdrawDeepCoopReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.GetWaitWithdrawDeepCoopResp
		wantErr bool
	}{
		{name: "GetWaitWithdrawDeepCoop",
			fields: fields{mysqlStore: mockStore, cacheClient: mockCache, sc: mockConfig, mgr: mockManager},
			args: args{c: context.Background(), req: &pb.GetWaitWithdrawDeepCoopReq{
				Uid: 123456,
			}},
			want: &pb.GetWaitWithdrawDeepCoopResp{},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &SettlementBillServer{
				sc:          tt.fields.sc,
				cacheClient: tt.fields.cacheClient,
				mysqlStore:  tt.fields.mysqlStore,
				mgr:         tt.fields.mgr,
			}
			got, err := s.GetWaitWithdrawDeepCoop(tt.args.c, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetWaitWithdrawDeepCoop() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetWaitWithdrawDeepCoop() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestSettlementBillServer_GetWaitWithdrawMonths(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockStore := mocks.NewMockIStore(ctl)
	mockCache := mocks.NewMockISettlementBillCache(ctl)
	mockConfig := mocks.NewMockIServiceConfigT(ctl)
	// mockOA := oa.NewMockIClient(ctl)
	mockManager := mocks.NewMockIManager(ctl)

	gomock.InOrder(
		mockManager.EXPECT().GetWaitWithdrawMonths(context.Background(), &pb.GetWaitWithdrawMonthsReq{
			Uid:      123456,
			BillType: pb.SettlementBillType_GiftScore,
		}).Return(&pb.GetWaitWithdrawMonthsResp{}, nil),
	)

	type fields struct {
		sc          conf.IServiceConfigT
		cacheClient cache.ISettlementBillCache
		mysqlStore  mysql.IStore
		mgr         manager.IManager
	}
	type args struct {
		c   context.Context
		req *pb.GetWaitWithdrawMonthsReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.GetWaitWithdrawMonthsResp
		wantErr bool
	}{
		{name: "GetWaitWithdrawMonths",
			fields: fields{mysqlStore: mockStore, cacheClient: mockCache, sc: mockConfig, mgr: mockManager},
			args: args{c: context.Background(), req: &pb.GetWaitWithdrawMonthsReq{
				Uid:      123456,
				BillType: pb.SettlementBillType_GiftScore,
			}},
			want: &pb.GetWaitWithdrawMonthsResp{},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &SettlementBillServer{
				sc:          tt.fields.sc,
				cacheClient: tt.fields.cacheClient,
				mysqlStore:  tt.fields.mysqlStore,
				mgr:         tt.fields.mgr,
			}
			got, err := s.GetWaitWithdrawMonths(tt.args.c, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetWaitWithdrawMonths() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetWaitWithdrawMonths() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestSettlementBillServer_RecordExtraIncome(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockStore := mocks.NewMockIStore(ctl)
	mockCache := mocks.NewMockISettlementBillCache(ctl)
	mockConfig := mocks.NewMockIServiceConfigT(ctl)
	// mockOA := oa.NewMockIClient(ctl)
	mockManager := mocks.NewMockIManager(ctl)

	gomock.InOrder(
		mockManager.EXPECT().RecordExtraIncome(context.Background(), &pb.RecordExtraIncomeReq{
			SettlementDate:           202208,
			IncomeType:               0,
			Operator:                 "",
			FileName:                 "",
			FileMd5:                  "",
			FileUrl:                  "",
			DeepCooperationList:      nil,
			YuyinAnchorSubsidyList:   nil,
			YuyinNewGuildSubsidyList: nil,
			DeductList:               nil,
		}).Return(&pb.RecordExtraIncomeResp{}, nil),
	)

	type fields struct {
		sc          conf.IServiceConfigT
		cacheClient cache.ISettlementBillCache
		mysqlStore  mysql.IStore
		mgr         manager.IManager
	}
	type args struct {
		c   context.Context
		req *pb.RecordExtraIncomeReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.RecordExtraIncomeResp
		wantErr bool
	}{
		{name: "RecordExtraIncome",
			fields: fields{mysqlStore: mockStore, cacheClient: mockCache, sc: mockConfig, mgr: mockManager},
			args: args{c: context.Background(), req: &pb.RecordExtraIncomeReq{
				SettlementDate:           202208,
				IncomeType:               0,
				Operator:                 "",
				FileName:                 "",
				FileMd5:                  "",
				FileUrl:                  "",
				DeepCooperationList:      nil,
				YuyinAnchorSubsidyList:   nil,
				YuyinNewGuildSubsidyList: nil,
				DeductList:               nil,
			}},
			want: &pb.RecordExtraIncomeResp{},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &SettlementBillServer{
				sc:          tt.fields.sc,
				cacheClient: tt.fields.cacheClient,
				mysqlStore:  tt.fields.mysqlStore,
				mgr:         tt.fields.mgr,
			}
			got, err := s.RecordExtraIncome(tt.args.c, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("RecordExtraIncome() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("RecordExtraIncome() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestSettlementBillServer_RecordReceiptFile(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockStore := mocks.NewMockIStore(ctl)
	mockCache := mocks.NewMockISettlementBillCache(ctl)
	mockConfig := mocks.NewMockIServiceConfigT(ctl)
	// mockOA := oa.NewMockIClient(ctl)
	mockManager := mocks.NewMockIManager(ctl)

	gomock.InOrder(
		mockManager.EXPECT().RecordReceiptFile(context.Background(), &pb.RecordReceiptFileReq{
			ReceiptId: "123456",
			FileName:  "",
			Size:      1024,
			ShowSize:  "1k",
			FileClass: "",
			Uid:       123456,
		}).Return(&pb.RecordReceiptFileResp{}, nil),
	)

	type fields struct {
		sc          conf.IServiceConfigT
		cacheClient cache.ISettlementBillCache
		mysqlStore  mysql.IStore
		mgr         manager.IManager
	}
	type args struct {
		c   context.Context
		req *pb.RecordReceiptFileReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.RecordReceiptFileResp
		wantErr bool
	}{
		{name: "RecordReceiptFile",
			fields: fields{mysqlStore: mockStore, cacheClient: mockCache, sc: mockConfig, mgr: mockManager},
			args: args{c: context.Background(), req: &pb.RecordReceiptFileReq{
				ReceiptId: "123456",
				FileName:  "",
				Size:      1024,
				ShowSize:  "1k",
				FileClass: "",
				Uid:       123456,
			}},
			want: &pb.RecordReceiptFileResp{},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &SettlementBillServer{
				sc:          tt.fields.sc,
				cacheClient: tt.fields.cacheClient,
				mysqlStore:  tt.fields.mysqlStore,
				mgr:         tt.fields.mgr,
			}
			got, err := s.RecordReceiptFile(tt.args.c, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("RecordReceiptFile() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("RecordReceiptFile() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestSettlementBillServer_RecordTaxRate(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockStore := mocks.NewMockIStore(ctl)
	mockCache := mocks.NewMockISettlementBillCache(ctl)
	mockConfig := mocks.NewMockIServiceConfigT(ctl)
	// mockOA := oa.NewMockIClient(ctl)
	mockManager := mocks.NewMockIManager(ctl)

	gomock.InOrder(
		mockManager.EXPECT().RecordTaxRate(context.Background(), &pb.RecordTaxRateReq{
			Operator:    "123456",
			TaxRateList: []*pb.TaxRate{},
		}).Return(&pb.RecordTaxRateResp{}, nil),
	)

	type fields struct {
		sc          conf.IServiceConfigT
		cacheClient cache.ISettlementBillCache
		mysqlStore  mysql.IStore
		mgr         manager.IManager
	}
	type args struct {
		c   context.Context
		req *pb.RecordTaxRateReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.RecordTaxRateResp
		wantErr bool
	}{
		{name: "RecordTaxRate",
			fields: fields{mysqlStore: mockStore, cacheClient: mockCache, sc: mockConfig, mgr: mockManager},
			args: args{c: context.Background(), req: &pb.RecordTaxRateReq{
				Operator:    "123456",
				TaxRateList: []*pb.TaxRate{},
			}},
			want: &pb.RecordTaxRateResp{},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &SettlementBillServer{
				sc:          tt.fields.sc,
				cacheClient: tt.fields.cacheClient,
				mysqlStore:  tt.fields.mysqlStore,
				mgr:         tt.fields.mgr,
			}
			got, err := s.RecordTaxRate(tt.args.c, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("RecordTaxRate() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("RecordTaxRate() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestSettlementBillServer_ReportConfirmDeductMoney(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockStore := mocks.NewMockIStore(ctl)
	mockCache := mocks.NewMockISettlementBillCache(ctl)
	mockConfig := mocks.NewMockIServiceConfigT(ctl)
	// mockOA := oa.NewMockIClient(ctl)
	mockManager := mocks.NewMockIManager(ctl)

	gomock.InOrder(
		mockManager.EXPECT().ReportConfirmDeductMoney(context.Background(), &pb.ReportConfirmReq{
			StartTime:        123456,
			EndTime:          123456,
			ConfirmSendEmail: true,
		}).Return(&pb.ReportConfirmResp{}, nil),
	)

	type fields struct {
		sc          conf.IServiceConfigT
		cacheClient cache.ISettlementBillCache
		mysqlStore  mysql.IStore
		mgr         manager.IManager
	}
	type args struct {
		c   context.Context
		req *pb.ReportConfirmReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.ReportConfirmResp
		wantErr bool
	}{
		{name: "ReportConfirmDeductMoney",
			fields: fields{mysqlStore: mockStore, cacheClient: mockCache, sc: mockConfig, mgr: mockManager},
			args: args{c: context.Background(), req: &pb.ReportConfirmReq{
				StartTime:        123456,
				EndTime:          123456,
				ConfirmSendEmail: true,
			}},
			want: &pb.ReportConfirmResp{},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &SettlementBillServer{
				sc:          tt.fields.sc,
				cacheClient: tt.fields.cacheClient,
				mysqlStore:  tt.fields.mysqlStore,
				mgr:         tt.fields.mgr,
			}
			got, err := s.ReportConfirmDeductMoney(tt.args.c, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("ReportConfirmDeductMoney() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("ReportConfirmDeductMoney() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestSettlementBillServer_ReportConfirmDeepCoop(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockStore := mocks.NewMockIStore(ctl)
	mockCache := mocks.NewMockISettlementBillCache(ctl)
	mockConfig := mocks.NewMockIServiceConfigT(ctl)
	// mockOA := oa.NewMockIClient(ctl)
	mockManager := mocks.NewMockIManager(ctl)

	gomock.InOrder(
		mockManager.EXPECT().ReportConfirmDeepCoop(context.Background(), &pb.ReportConfirmReq{
			StartTime:        123456,
			EndTime:          123456,
			ConfirmSendEmail: true,
		}).Return(&pb.ReportConfirmResp{}, nil),
	)

	type fields struct {
		sc          conf.IServiceConfigT
		cacheClient cache.ISettlementBillCache
		mysqlStore  mysql.IStore
		mgr         manager.IManager
	}
	type args struct {
		c   context.Context
		req *pb.ReportConfirmReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.ReportConfirmResp
		wantErr bool
	}{
		{name: "ReportConfirmDeepCoop",
			fields: fields{mysqlStore: mockStore, cacheClient: mockCache, sc: mockConfig, mgr: mockManager},
			args: args{c: context.Background(), req: &pb.ReportConfirmReq{
				StartTime:        123456,
				EndTime:          123456,
				ConfirmSendEmail: true,
			}},
			want: &pb.ReportConfirmResp{},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &SettlementBillServer{
				sc:          tt.fields.sc,
				cacheClient: tt.fields.cacheClient,
				mysqlStore:  tt.fields.mysqlStore,
				mgr:         tt.fields.mgr,
			}
			got, err := s.ReportConfirmDeepCoop(tt.args.c, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("ReportConfirmDeepCoop() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("ReportConfirmDeepCoop() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestSettlementBillServer_ReportConfirmWith(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockStore := mocks.NewMockIStore(ctl)
	mockCache := mocks.NewMockISettlementBillCache(ctl)
	mockConfig := mocks.NewMockIServiceConfigT(ctl)
	// mockOA := oa.NewMockIClient(ctl)
	mockManager := mocks.NewMockIManager(ctl)

	gomock.InOrder(
		mockManager.EXPECT().ReportConfirmWith(context.Background(), &pb.ReportConfirmWithReq{
			BillType: []pb.SettlementBillType{pb.SettlementBillType_GiftScore},
		}).Return(&pb.ReportConfirmWithResp{}, nil),
	)

	type fields struct {
		sc          conf.IServiceConfigT
		cacheClient cache.ISettlementBillCache
		mysqlStore  mysql.IStore
		mgr         manager.IManager
	}
	type args struct {
		c   context.Context
		req *pb.ReportConfirmWithReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.ReportConfirmWithResp
		wantErr bool
	}{
		{name: "ReportConfirmWith",
			fields: fields{mysqlStore: mockStore, cacheClient: mockCache, sc: mockConfig, mgr: mockManager},
			args: args{c: context.Background(), req: &pb.ReportConfirmWithReq{
				BillType: []pb.SettlementBillType{pb.SettlementBillType_GiftScore},
			}},
			want: &pb.ReportConfirmWithResp{},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &SettlementBillServer{
				sc:          tt.fields.sc,
				cacheClient: tt.fields.cacheClient,
				mysqlStore:  tt.fields.mysqlStore,
				mgr:         tt.fields.mgr,
			}
			got, err := s.ReportConfirmWith(tt.args.c, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("ReportConfirmWith() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("ReportConfirmWith() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestSettlementBillServer_ReportConfirmYuyinSubsidy(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockStore := mocks.NewMockIStore(ctl)
	mockCache := mocks.NewMockISettlementBillCache(ctl)
	mockConfig := mocks.NewMockIServiceConfigT(ctl)
	// mockOA := oa.NewMockIClient(ctl)
	mockManager := mocks.NewMockIManager(ctl)

	gomock.InOrder(
		mockManager.EXPECT().ReportConfirmYuyinSubsidy(context.Background(), &pb.ReportConfirmReq{
			StartTime:        123456,
			EndTime:          123456,
			ConfirmSendEmail: true,
		}).Return(&pb.ReportConfirmResp{}, nil),
	)

	type fields struct {
		sc          conf.IServiceConfigT
		cacheClient cache.ISettlementBillCache
		mysqlStore  mysql.IStore
		mgr         manager.IManager
	}
	type args struct {
		c   context.Context
		req *pb.ReportConfirmReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.ReportConfirmResp
		wantErr bool
	}{
		{name: "ReportConfirmYuyinSubsidy",
			fields: fields{mysqlStore: mockStore, cacheClient: mockCache, sc: mockConfig, mgr: mockManager},
			args: args{c: context.Background(), req: &pb.ReportConfirmReq{
				StartTime:        123456,
				EndTime:          123456,
				ConfirmSendEmail: true,
			}},
			want: &pb.ReportConfirmResp{},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &SettlementBillServer{
				sc:          tt.fields.sc,
				cacheClient: tt.fields.cacheClient,
				mysqlStore:  tt.fields.mysqlStore,
				mgr:         tt.fields.mgr,
			}
			got, err := s.ReportConfirmYuyinSubsidy(tt.args.c, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("ReportConfirmYuyinSubsidy() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("ReportConfirmYuyinSubsidy() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestSettlementBillServer_ShutDown(t *testing.T) {
	type fields struct {
		sc          conf.IServiceConfigT
		cacheClient cache.ISettlementBillCache
		mysqlStore  mysql.IStore
		mgr         manager.IManager
	}
	tests := []struct {
		name   string
		fields fields
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &SettlementBillServer{
				sc:          tt.fields.sc,
				cacheClient: tt.fields.cacheClient,
				mysqlStore:  tt.fields.mysqlStore,
				mgr:         tt.fields.mgr,
			}
			s.ShutDown()
		})
	}
}
