package hanlders

import (
	"context"
	"encoding/json"
	"fmt"
	channelpb_ "golang.52tt.com/protocol/app/channel"
	enterPb "golang.52tt.com/protocol/services/entertainmentRecommendBackSvr"
	"math"
	"net/http"
	"strconv"
	"strings"
	"sync"
	"time"

	"golang.52tt.com/pkg/datahouse"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/web"
	"golang.52tt.com/services/guild-management/guild-management-http-logic/conf"
	"golang.52tt.com/services/guild-management/guild-management-http-logic/models"
	api "golang.52tt.com/services/guild-management/guild-management-http-logic/models/gen-go"
)

// GetGuildChannelBusinessData 获取公会房间经营信息
func GetGuildChannelBusinessData(authInfo *web.AuthInfo, w http.ResponseWriter, r *http.Request) {
	ctx, cancel := context.WithTimeout(r.Context(), time.Second*10)
	defer cancel()

	req := &api.GetGuildChannelBusinessDataReq{}
	err := json.Unmarshal(authInfo.Body, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetGuildChannelBusinessData Failed to parse request body [%s], err %+v", string(authInfo.Body), err)
		web.ServeBadReq(w)
		return
	}
	guildId := req.GetGuildId()
	uid := authInfo.UserID

	log.DebugWithCtx(ctx, "GetGuildChannelBusinessData begin uid:%d, %+v", uid, req)

	_, respCode, respMsg := CheckUserPermission(CheckPerInReq{
		ctx:         ctx,
		uid:         uid,
		guildId:     guildId,
		menuPerType: api.GuildMultiPlayDataType_MultiPlayBusinessDiag.String(),
		funcPerType: api.MultiBusinessDiagFuncType_MultiBusinessDiagFuncStability.String(),
	})
	if respCode != 0 {
		_ = web.ServeAPICodeJson(w, respCode, respMsg, nil)
		return
	}

	channelResp, sErr := models.GetModelServer().ChannelCli.GetChannelSimpleInfoByViewId(ctx, uid, req.GetChannelViewId())
	if sErr != nil {
		log.ErrorWithCtx(ctx, "GetGuildChannelBusinessData Failed to GetChannelSimpleInfoByViewId err uid:%d req:%v err:%+v", uid, req, sErr)
		web.ServeGameErrReq(w, -500, sErr.Message())
		return
	}

	tm := time.Unix(int64(req.GetTs()), 0)
	endTm := tm.AddDate(0, 0, -GetWeekDayOffset(tm)+1)
	startTm := endTm.AddDate(0, 0, -6*7)
	timeDim := "week"

	if req.GetDimType() == uint32(api.GetGuildChannelBusinessDataReq_TimeDimType_Month) {
		timeDim = "month"
		endTm = time.Date(tm.Year(), tm.Month(), 1, 0, 0, 0, 0, time.Local)
		startTm = endTm.AddDate(0, -6, 0)
	}

	OpdataMultiRoomStatsResp, err := datahouse.QueryOpdataMultiRoomStats(ctx, &datahouse.OpdataMultiRoomStatsReq{
		StartDate:   startTm,
		EndDate:     endTm,
		TimeDim:     timeDim,
		GuildIdList: []uint32{channelResp.GetBindId()},
		RoomIdList:  []uint32{channelResp.GetChannelId()},
		Page:        1,
		PageSize:    10,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetGuildChannelBusinessData Failed to QueryOpdataMultiRoomStats err uid:%d req:%v err:%+v", uid, req, err)
		web.ServeGameErrReq(w, -500, "系统错误")
		return
	}

	mapTm2Data := make(map[string]*datahouse.OpdataMultiRoomStats)
	for _, data := range OpdataMultiRoomStatsResp.Data {
		mapTm2Data[data.DataDate] = data
	}

	resp := &api.GetGuildChannelBusinessDataResp{
		ChannelList: make([]*api.GuildChannelBusinessData, 0),
	}

	switch api.GetGuildChannelBusinessDataReq_TimeDimType(req.GetDimType()) {
	case api.GetGuildChannelBusinessDataReq_TimeDimType_Week:
		for t := startTm; t.Unix() <= endTm.Unix(); t = t.AddDate(0, 0, 7) {
			tmpData := &api.GuildChannelBusinessData{
				Fee: "0",
				Ts:  uint32(t.Unix()),
			}

			if _, ok := mapTm2Data[t.Format(time.DateOnly)]; ok {
				tmpData.Fee = mapTm2Data[t.Format(time.DateOnly)].RoomRevenue
			}

			resp.ChannelList = append(resp.ChannelList, tmpData)
		}

	case api.GetGuildChannelBusinessDataReq_TimeDimType_Month:
		for t := startTm; t.Unix() <= endTm.Unix(); t = t.AddDate(0, 1, 0) {
			tmpData := &api.GuildChannelBusinessData{
				Fee: "0",
				Ts:  uint32(t.Unix()),
			}

			if _, ok := mapTm2Data[t.Format(time.DateOnly)]; ok {
				tmpData.Fee = mapTm2Data[t.Format(time.DateOnly)].RoomRevenue
			}

			resp.ChannelList = append(resp.ChannelList, tmpData)
		}

	}

	log.DebugWithCtx(ctx, "GetGuildChannelBusinessData end uid:%d, req:%v resp:%+v", uid, req, resp)
	_ = web.ServeAPIJson(w, resp)
}

// 计算增长型分值
func CulScoreByIncr(ctx context.Context, guildId uint32, monthVal, lastNMonthAve float64, abilityScoreConfs []conf.AbilityScoreConfig) int32 {
	scoreConfigList := make([]conf.ScoreConfig, 0)
	var baseVal float64
	for _, conf := range abilityScoreConfs {
		if (lastNMonthAve >= float64(conf.MinVal) && lastNMonthAve < float64(conf.MaxVal)) || conf.MaxVal == 0 {
			scoreConfigList = conf.ScoreConfigList
			baseVal = float64(conf.BaseVal)
			break
		}
	}

	if len(scoreConfigList) == 0 {
		log.ErrorWithCtx(ctx, "CulScoreByIncr Failed to get score config list, guildId:%d, monthVal:%f, lastNMonthAve:%f", guildId, monthVal, lastNMonthAve)
		return 0
	}

	var score int32
	incr := monthVal - baseVal

	if incr > 0 {
		for _, conf := range scoreConfigList {
			if incr >= float64(conf.MinIncr) && (incr < float64(conf.MaxIncr) || conf.MaxIncr == 0) {
				score = conf.Score
				break
			}
		}
	}

	log.DebugWithCtx(ctx, "CulScoreByIncr guildId:%d, monthVal:%f, lastNMonthAve:%f, scoreConfigList:%+v, score:%d", guildId, monthVal, lastNMonthAve, scoreConfigList, score)
	return score
}

// 计算非增长型分值
func CulScore(ctx context.Context, guildId uint32, monthVal float64, abilityScoreConfs []conf.AbilityScoreConfig, abilityType api.AbilityItemType) int32 {
	var score int32

	if monthVal == 0 && abilityType == api.AbilityItemType_AbilityItemType_Risk_Resistance {
		log.DebugWithCtx(ctx, "CulScore guildId:%d, monthVal:%f, abilityType:%s, scoreConfigList:%+v, score:%d", guildId, monthVal, abilityType.String(), abilityScoreConfs, 10)
		return 10
	}

	for _, conf := range abilityScoreConfs {
		if (monthVal >= conf.FMinVal && monthVal < conf.FMaxVal) || conf.FMaxVal == 0 {
			score = conf.Score
			break
		}
	}

	log.DebugWithCtx(ctx, "CulScore guildId:%d, monthVal:%f, abilityType:%s, scoreConfigList:%+v, score:%d", guildId, monthVal, abilityType.String(), abilityScoreConfs, score)
	return score
}

func ParseFloatByStr(ctx context.Context, str string) (float64, float64, string, string) {
	str = strings.ReplaceAll(str, "[", "")
	str = strings.ReplaceAll(str, "]", "")
	str = strings.ReplaceAll(str, "\"", "")
	str = strings.ReplaceAll(str, " ", "")

	list := strings.Split(str, "-")
	if len(list) != 2 {
		log.ErrorWithCtx(ctx, "ParseFloatByStr Failed to parse str:%s", str)
		return 0, 0, "0", "0"
	}

	val, err := strconv.ParseFloat(list[0], 64)
	if err != nil {
		log.ErrorWithCtx(ctx, "ParseFloatByStr Failed to parse str:%s, err:%+v", str, err)
		return 0, 0, "0", "0"
	}

	lastVal, err := strconv.ParseFloat(list[1], 64)
	if err != nil {
		log.ErrorWithCtx(ctx, "ParseFloatByStr Failed to parse str:%s, err:%+v", str, err)
		return 0, 0, "0", "0"
	}

	log.DebugWithCtx(ctx, "ParseFloatByStr str:%s, val:%f, lastVal:%f", str, val, lastVal)
	return val, lastVal, list[0], list[1]
}

// 营收分值
func CulRevenueScore(ctx context.Context, guildId uint32, data *datahouse.OpdataMultiOperate, tStr, endTmStr string) int32 {
	var revenue, lastNMonthAve float64 = 0, 0
	if data.TotalFlow != "" {
		revenue, _, _, _ = ParseFloatByStr(ctx, data.TotalFlow)
	}
	if tStr == endTmStr && data.MonthlyEstimatedFlow != "" {
		revenue, _ = strconv.ParseFloat(data.MonthlyEstimatedFlow, 64)
	}
	if data.Last3mAvgFlow != "" {
		lastNMonthAve, _ = strconv.ParseFloat(data.Last3mAvgFlow, 64)
	}

	return CulScoreByIncr(ctx, guildId, revenue, lastNMonthAve, conf.RevenueScoreConfig)
}

// 招新分值
func CulRecruitScore(ctx context.Context, guildId uint32, data *datahouse.OpdataMultiOperate) int32 {
	var newPractitioners, lastNMonthNAve float64 = 0, 0
	if data.NewPractitioners != "" {
		newPractitioners, _, _, _ = ParseFloatByStr(ctx, data.NewPractitioners)
	}

	if data.Last3mAvgNewPractitioners != "" {
		lastNMonthNAve, _ = strconv.ParseFloat(data.Last3mAvgNewPractitioners, 64)
	}

	return CulScoreByIncr(ctx, guildId, newPractitioners, lastNMonthNAve, conf.RecruitScoreConfig)
}

// 孵化分值
func CulHatchScore(ctx context.Context, guildId uint32, data *datahouse.OpdataMultiOperate) int32 {
	var proPractitioners, lastNMonthNAve float64 = 0, 0
	if data.ProPractitioners != "" {
		proPractitioners, _, _, _ = ParseFloatByStr(ctx, data.ProPractitioners)
	}

	if data.Last3mAvgProPractitioners != "" {
		lastNMonthNAve, _ = strconv.ParseFloat(data.Last3mAvgProPractitioners, 64)
	}

	return CulScoreByIncr(ctx, guildId, proPractitioners, lastNMonthNAve, conf.HatchScoreConfig)
}

// 安全项分值
func CulSafetyScore(ctx context.Context, guildId uint32, data *datahouse.OpdataMultiOperate) int32 {
	var ecoScore float64 = 0
	if data.EcoScore != "" {
		ecoScore, _ = strconv.ParseFloat(data.EcoScore, 64)
	}

	return CulScore(ctx, guildId, ecoScore, conf.SafetyScoreConfig, api.AbilityItemType_AbilityItemType_Safety)
}

// 稳定项分值
func CulStabilityScore(ctx context.Context, guildId uint32, data *datahouse.OpdataMultiOperate) int32 {
	var val float64 = 0
	if data.AvgNotspecialFlow != "" {
		val, _ = strconv.ParseFloat(data.AvgNotspecialFlow, 64)
	}

	return CulScore(ctx, guildId, val, conf.StabilityScoreConfig, api.AbilityItemType_AbilityItemType_Stability)
}

// 抗风险项分值
func CulRiskResistanceScore(ctx context.Context, guildId uint32, data *datahouse.OpdataMultiOperate) int32 {
	var val float64 = 0
	if data.LuckyGiftRatio != "" {
		val, _ = strconv.ParseFloat(data.LuckyGiftRatio, 64)
	}

	return CulScore(ctx, guildId, val, conf.RiskResistanceScoreConfig, api.AbilityItemType_AbilityItemType_Risk_Resistance)
}

func getDataStr(val uint64) string {
	if val >= 10000 {
		return fmt.Sprintf("%d万元", val/10000)
	}

	return fmt.Sprintf("%d元", val)
}

// 总体分析
func GetTotalBusinessDiag(ctx context.Context, guildId uint32, tm time.Time, mapTm2Data map[string]*datahouse.OpdataMultiOperate) string {
	guildLevel := "暂无层级"
	rangeStr := ""
	if data, ok := mapTm2Data[tm.Format(time.DateOnly)]; ok {
		if data.TotalFlowLevel != "" {
			strList := strings.Split(data.TotalFlowLevel, ":")
			if len(strList) == 2 {
				rangeList := strings.Split(strList[1], "-")
				if len(rangeList) == 2 {
					minVal, err := strconv.ParseUint(rangeList[0], 10, 64)
					if err != nil {
						log.ErrorWithCtx(ctx, "GetTotalBusinessDiag Failed to parse minVal guildId:%d data:%v str:%s err:%+v", guildId, data, rangeList[0], err)
						return ""
					}

					maxVal, err := strconv.ParseUint(rangeList[1], 10, 64)
					if err != nil {
						log.ErrorWithCtx(ctx, "GetTotalBusinessDiag Failed to parse minVal guildId:%d data:%v str:%s err:%+v", guildId, data, rangeList[1], err)
						return ""
					}

					guildLevel = strList[0]

					if minVal != 0 && maxVal != 0 {
						rangeStr = fmt.Sprintf("月流水在%s（含）--%s之间", getDataStr(minVal), getDataStr(maxVal))
					} else if minVal == 0 && maxVal != 0 {
						rangeStr = fmt.Sprintf("月流水在%s（不含）--%s之间", getDataStr(minVal), getDataStr(maxVal))
					} else if maxVal == 0 && minVal != 0 {
						rangeStr = fmt.Sprintf("月流水大于%s（含）", getDataStr(minVal))
					}
				}
			}
		}
	}

	htmlStr := ""
	htmlStr = `<div class="diagnosis_content">公会当前所处层级：<span class="bold" style="color: #388DFF;">%s</span> %s</div>`
	htmlStr = fmt.Sprintf(htmlStr, guildLevel, rangeStr)

	log.DebugWithCtx(ctx, "GetTotalBusinessDiag guildId:%d, tm:%s, mapTm2Data:%+v htmlStr:%s", guildId, tm.Format(time.DateOnly), mapTm2Data, htmlStr)
	return htmlStr
}

// 本月经营分析
func GetMonthBusinessDiag(ctx context.Context, guildId uint32, monthTm time.Time, mapTmType2Score map[string]int32) string {
	monthStr := monthTm.Format(time.DateOnly)
	lastMonthStr := monthTm.AddDate(0, -1, 0).Format(time.DateOnly)
	var incrList, sameList, decList, strongList, weakList []api.AbilityItemType
	var incrStr, sameStr, decStr, strongStr, weakStr string
	var changeHtml, abilityHtml string
	var abilityHtmlList []string
	for _, abilityType := range []api.AbilityItemType{
		api.AbilityItemType_AbilityItemType_Revenue,
		api.AbilityItemType_AbilityItemType_Recruit,
		api.AbilityItemType_AbilityItemType_Hatch,
		api.AbilityItemType_AbilityItemType_Safety,
		api.AbilityItemType_AbilityItemType_Stability,
		api.AbilityItemType_AbilityItemType_Risk_Resistance} {

		val := mapTmType2Score[fmt.Sprintf("%s-%d", monthStr, uint32(abilityType))]
		lastVal := mapTmType2Score[fmt.Sprintf("%s-%d", lastMonthStr, uint32(abilityType))]

		if val > lastVal {
			incrList = append(incrList, abilityType)
		} else if val == lastVal {
			sameList = append(sameList, abilityType)
		} else {
			decList = append(decList, abilityType)
		}

		if val >= 7 {
			strongList = append(strongList, abilityType)
		} else if val <= 2 {
			weakList = append(weakList, abilityType)
		}
	}

	for index, incr := range incrList {
		if index == len(incrList)-1 {
			incrStr += fmt.Sprintf("%s", conf.MapType2Name[incr])
		} else {
			incrStr += fmt.Sprintf("%s、", conf.MapType2Name[incr])
		}
	}

	if incrStr != "" {
		changeHtml += fmt.Sprintf("%s <span class=\"bold\" style=\"color: #FF5674;\">提升</span>，", incrStr)
	}

	for index, same := range sameList {
		if index == len(sameList)-1 {
			sameStr += fmt.Sprintf("%s", conf.MapType2Name[same])
		} else {
			sameStr += fmt.Sprintf("%s、", conf.MapType2Name[same])
		}
	}

	if sameStr != "" {
		changeHtml += fmt.Sprintf("%s <span class=\"bold\" style=\"color: #388DFF;\">持平</span>，", sameStr)
	}

	for index, desc := range decList {
		if index == len(decList)-1 {
			decStr += fmt.Sprintf("%s", conf.MapType2Name[desc])
		} else {
			decStr += fmt.Sprintf("%s、", conf.MapType2Name[desc])
		}
	}

	if decStr != "" {
		changeHtml += fmt.Sprintf("%s <span class=\"bold\" style=\"color: #17CBC2;\">下滑</span>，", decStr)
	}

	for index, strong := range strongList {
		if index == len(strongList)-1 {
			strongStr += fmt.Sprintf("%s", conf.MapType2Name[strong])
		} else {
			strongStr += fmt.Sprintf("%s、", conf.MapType2Name[strong])
		}
	}

	if strongStr != "" {
		abilityHtmlList = append(abilityHtmlList, fmt.Sprintf("%s 是公会的 <span class=\"bold\" style=\"color: #FF5674;\">强项</span>", strongStr))
	}

	for index, weak := range weakList {
		if index == len(weakList)-1 {
			weakStr += fmt.Sprintf("%s", conf.MapType2Name[weak])
		} else {
			weakStr += fmt.Sprintf("%s、", conf.MapType2Name[weak])
		}
	}

	if weakStr != "" {
		abilityHtmlList = append(abilityHtmlList, fmt.Sprintf("%s 是公会的 <span class=\"bold\" style=\"color: #17CBC2;\">薄弱项</span>", weakStr))
	}

	if len(abilityHtmlList) > 0 {
		abilityHtml = strings.Join(abilityHtmlList, "，")
	} else {
		abilityHtml = "公会发展 <span class=\"bold\" style=\"color: ##388DFF;\">较均衡</span>"
	}

	htmlStr := `<div class="block"><div class="block_title">本月数据分析</div><div class="block_content">本月 %s%s</div></div>`
	htmlStr = fmt.Sprintf(htmlStr, changeHtml, abilityHtml)

	log.DebugWithCtx(ctx, "GetMonthBusinessDiag guildId:%d, monthTm:%s, mapTmType2Score:%+v htmlStr:%s", guildId, monthTm.Format(time.DateOnly), mapTmType2Score, htmlStr)
	return htmlStr
}

func GetRecentBusinessDiag(ctx context.Context, guildId uint32, monthTm time.Time, mapTmType2Score map[string]int32, recentHasDataCnt int) string {
	if recentHasDataCnt <= 1 {
		log.DebugWithCtx(ctx, "GetRecentBusinessDiag no need diag guildId:%d monthTm:%v recentHasDataCnt:%d", guildId, monthTm, recentHasDataCnt)
		return `<div class="block"><div class="block_title">半年趋势分析</div><div class="block_content">公会稳定运营满2个月，即可获取专属的半年趋势分析报告</div></div>`
	}

	// 获取过去完整6个月的月份字符串
	months := make([]string, 6)
	for i := 0; i < 6; i++ {
		// 从当前月份往前推i个月
		tmpMonth := monthTm.AddDate(0, -i, 0)
		months[5-i] = tmpMonth.Format(time.DateOnly) // 倒序，使months[0]为最早的月份，months[5]为当前月份
	}

	// 按类型分别获取各个月份的分值
	abilityTypes := []api.AbilityItemType{
		api.AbilityItemType_AbilityItemType_Revenue,
		api.AbilityItemType_AbilityItemType_Recruit,
		api.AbilityItemType_AbilityItemType_Hatch,
		api.AbilityItemType_AbilityItemType_Safety,
		api.AbilityItemType_AbilityItemType_Stability,
		api.AbilityItemType_AbilityItemType_Risk_Resistance,
	}

	mapDiag2List := make(map[string][]api.AbilityItemType)

	for _, aType := range abilityTypes {
		// 获取该类型6个月的分值数据
		scores := make([]int32, 6)
		for i, month := range months {
			key := fmt.Sprintf("%s-%d", month, aType)
			scores[i] = mapTmType2Score[key]
		}

		// 计算第1月和第6月的分值差异
		diff := scores[5] - scores[0]

		// 检查是否有下滑的月份
		hasDecrease := false
		decreaseCount := 0
		// 检查是否有增长的月份
		hasIncrease := false
		increaseCount := 0
		// 检查连续增长或下滑情况
		continuousIncrease := 0
		maxContinuousIncrease := 0
		continuousDecrease := 0
		maxContinuousDecrease := 0

		for i := 1; i < 6; i++ {
			monthDiff := scores[i] - scores[i-1]
			if monthDiff < 0 {
				hasDecrease = true
				decreaseCount++
				continuousDecrease++
				continuousIncrease = 0
				if continuousDecrease > maxContinuousDecrease {
					maxContinuousDecrease = continuousDecrease
				}
			} else if monthDiff > 0 {
				hasIncrease = true
				increaseCount++
				continuousIncrease++
				continuousDecrease = 0
				if continuousIncrease > maxContinuousIncrease {
					maxContinuousIncrease = continuousIncrease
				}
			}
		}

		// 判断表现类型
		result := conf.Unstable

		// 1. 平稳增长
		if !hasDecrease && diff >= 2 && diff <= 5 {
			result = conf.SteadyGrowth

		}

		// 2. 持续增长
		if !hasDecrease && diff >= 6 && diff <= 10 {
			result = conf.ContinuedGrowth

		}
		// 3. 波动增长
		if decreaseCount >= 1 && decreaseCount <= 2 && (scores[5] > scores[0]) && diff >= 2 && diff <= 10 {
			// 检查单月下滑是否都在[0,2]范围内
			validDownslide := true
			for i := 1; i < 6; i++ {
				if scores[i] < scores[i-1] && scores[i-1]-scores[i] > 2 {
					validDownslide = false
					break
				}
			}
			if validDownslide {
				result = conf.FluctuatingGrowth
			}
		}

		// 4. 稳定
		if func() bool {
			// 6个月数据无变化
			allSame := true
			for i := 1; i < 6; i++ {
				if scores[i] != scores[i-1] {
					allSame = false
					break
				}
			}
			if allSame {
				return true
			}

			// 每月环比上月增长/下滑[0,2]分，且没有连续两月以上增长或下滑
			validChange := true
			for i := 1; i < 6; i++ {
				change := scores[i] - scores[i-1]
				if change < -2 || change > 2 {
					validChange = false
					break
				}
			}
			return validChange && maxContinuousIncrease < 2 && maxContinuousDecrease < 2
		}() {
			result = conf.Stable
		}

		// 5. 快速下滑
		if !hasIncrease && diff <= -2 && diff >= -5 {
			result = conf.RapidDecline

		}

		// 6. 持续下滑
		if !hasIncrease && diff <= -6 && diff >= -10 {
			result = conf.ContinuedDecline

		}

		// 7. 震荡下滑
		if increaseCount >= 1 && increaseCount <= 2 && (scores[5] < scores[0]) && diff >= -10 && diff <= -2 {
			// 检查月增长是否都在[0,2]范围内
			validUpslide := true
			for i := 1; i < 6; i++ {
				if scores[i] > scores[i-1] && scores[i]-scores[i-1] > 2 {
					validUpslide = false
					break
				}
			}
			if validUpslide {
				result = conf.FluctuatingDecline
			}
		}

		mapDiag2List[result] = append(mapDiag2List[result], aType)
	}

	changeHtmlList := make([]string, 0)
	itemList := []string{conf.SteadyGrowth, conf.ContinuedGrowth, conf.FluctuatingGrowth, conf.Stable, conf.RapidDecline, conf.ContinuedDecline, conf.FluctuatingDecline, conf.Unstable}
	for _, res := range itemList {
		if len(mapDiag2List[res]) > 0 {
			var aTypeStr string
			for index, aType := range mapDiag2List[res] {
				if index == len(mapDiag2List[res])-1 {
					aTypeStr += fmt.Sprintf("%s", conf.MapType2Name[aType])
				} else {
					aTypeStr += fmt.Sprintf("%s、", conf.MapType2Name[aType])
				}
			}

			changeHtmlList = append(changeHtmlList, fmt.Sprintf("%s <span class=\"bold\" style=\"color: %s;\">%s</span>", aTypeStr, conf.MapChangeStr2Color[res], res))
		}
	}

	htmlStr := `<div class="block"><div class="block_title">半年趋势分析</div><div class="block_content">近半年公会 %s</div></div>`
	htmlStr = fmt.Sprintf(htmlStr, strings.Join(changeHtmlList, "，"))

	log.DebugWithCtx(ctx, "GetRecentBusinessDiag guildId:%d, monthTm:%s, mapTmType2Score:%+v, mapDiag2List:%+v htmlStr:%s mapDiag2List:%v",
		guildId, monthTm.Format(time.DateOnly), mapTmType2Score, mapDiag2List, htmlStr, mapDiag2List)
	return htmlStr
}

// GetGuildRadarChart 获取公会的能力星图
func GetGuildRadarChart(authInfo *web.AuthInfo, w http.ResponseWriter, r *http.Request) {
	ctx, cancel := context.WithTimeout(r.Context(), time.Second*10)
	defer cancel()

	req := &api.GetGuildRadarChartReq{}
	err := json.Unmarshal(authInfo.Body, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetGuildRadarChart Failed to parse request body [%s], err %+v", string(authInfo.Body), err)
		web.ServeBadReq(w)
		return
	}
	guildId := req.GetGuildId()
	uid := authInfo.UserID

	log.DebugWithCtx(ctx, "GetGuildRadarChart begin uid:%d, %+v", uid, req)

	_, respCode, respMsg := CheckUserPermission(CheckPerInReq{
		ctx:         ctx,
		uid:         uid,
		guildId:     guildId,
		menuPerType: api.GuildMultiPlayDataType_MultiPlayBusinessDiag.String(),
		funcPerType: api.MultiBusinessDiagFuncType_MultiBusinessDiagFuncRadarChart.String(),
	})
	if respCode != 0 {
		_ = web.ServeAPICodeJson(w, respCode, respMsg, nil)
		return
	}

	nowTm := time.Now()
	endTm := time.Date(nowTm.Year(), nowTm.Month(), 1, 0, 0, 0, 0, time.Local)
	startTm := endTm.AddDate(0, -6, 0)

	OpdataMultiOperateResp, err := datahouse.QueryOpdataMultiOperate(ctx, &datahouse.OpdataMultiOperateReq{
		StartDate:   startTm,
		EndDate:     endTm,
		TimeDim:     "month",
		GuildIdList: []uint32{req.GetGuildId()},
		Page:        1,
		PageSize:    10,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetGuildRadarChart Failed to QueryOpdataMultiOperate err uid:%d req:%v err:%+v", uid, req, err)
		web.ServeGameErrReq(w, -500, "系统错误")
		return
	}

	mapTm2Data := make(map[string]*datahouse.OpdataMultiOperate)
	for _, data := range OpdataMultiOperateResp.Data {
		mapTm2Data[data.DataDate] = data
	}

	mapTmType2Score := make(map[string]int32)
	recentHasDataCnt := 0

	resp := &api.GetGuildRadarChartResp{
		AbilityList: make([]*api.MonthAbilityInfo, 0),
	}

	for t := startTm; t.Unix() <= endTm.Unix(); t = t.AddDate(0, 1, 0) {
		tStr := t.Format(time.DateOnly)
		if _, ok := mapTm2Data[tStr]; ok {
			data := mapTm2Data[tStr]
			if tStr != endTm.Format(time.DateOnly) {
				recentHasDataCnt++
			}

			//营收项
			mapTmType2Score[fmt.Sprintf("%s-%d", tStr, uint32(api.AbilityItemType_AbilityItemType_Revenue))] =
				CulRevenueScore(ctx, guildId, data, tStr, endTm.Format(time.DateOnly))
			//招新项
			mapTmType2Score[fmt.Sprintf("%s-%d", tStr, uint32(api.AbilityItemType_AbilityItemType_Recruit))] =
				CulRecruitScore(ctx, guildId, data)
			//孵化项
			mapTmType2Score[fmt.Sprintf("%s-%d", tStr, uint32(api.AbilityItemType_AbilityItemType_Hatch))] =
				CulHatchScore(ctx, guildId, data)
			//安全项
			mapTmType2Score[fmt.Sprintf("%s-%d", tStr, uint32(api.AbilityItemType_AbilityItemType_Safety))] =
				CulSafetyScore(ctx, guildId, data)
			//稳定项
			mapTmType2Score[fmt.Sprintf("%s-%d", tStr, uint32(api.AbilityItemType_AbilityItemType_Stability))] =
				CulStabilityScore(ctx, guildId, data)
			//抗风险项
			mapTmType2Score[fmt.Sprintf("%s-%d", tStr, uint32(api.AbilityItemType_AbilityItemType_Risk_Resistance))] =
				CulRiskResistanceScore(ctx, guildId, data)
		}

		tmpAbilityInfo := &api.MonthAbilityInfo{
			Ts: uint32(t.Unix()),
		}

		tmpAbilityInfo.InfoList = append(tmpAbilityInfo.InfoList, &api.AbilityInfo{
			Type:  uint32(api.AbilityItemType_AbilityItemType_Revenue),
			Score: uint32(mapTmType2Score[fmt.Sprintf("%s-%d", tStr, uint32(api.AbilityItemType_AbilityItemType_Revenue))]),
		})

		tmpAbilityInfo.InfoList = append(tmpAbilityInfo.InfoList, &api.AbilityInfo{
			Type:  uint32(api.AbilityItemType_AbilityItemType_Recruit),
			Score: uint32(mapTmType2Score[fmt.Sprintf("%s-%d", tStr, uint32(api.AbilityItemType_AbilityItemType_Recruit))]),
		})

		tmpAbilityInfo.InfoList = append(tmpAbilityInfo.InfoList, &api.AbilityInfo{
			Type:  uint32(api.AbilityItemType_AbilityItemType_Hatch),
			Score: uint32(mapTmType2Score[fmt.Sprintf("%s-%d", tStr, uint32(api.AbilityItemType_AbilityItemType_Hatch))]),
		})

		tmpAbilityInfo.InfoList = append(tmpAbilityInfo.InfoList, &api.AbilityInfo{
			Type:  uint32(api.AbilityItemType_AbilityItemType_Safety),
			Score: uint32(mapTmType2Score[fmt.Sprintf("%s-%d", tStr, uint32(api.AbilityItemType_AbilityItemType_Safety))]),
		})

		tmpAbilityInfo.InfoList = append(tmpAbilityInfo.InfoList, &api.AbilityInfo{
			Type:  uint32(api.AbilityItemType_AbilityItemType_Stability),
			Score: uint32(mapTmType2Score[fmt.Sprintf("%s-%d", tStr, uint32(api.AbilityItemType_AbilityItemType_Stability))]),
		})

		tmpAbilityInfo.InfoList = append(tmpAbilityInfo.InfoList, &api.AbilityInfo{
			Type:  uint32(api.AbilityItemType_AbilityItemType_Risk_Resistance),
			Score: uint32(mapTmType2Score[fmt.Sprintf("%s-%d", tStr, uint32(api.AbilityItemType_AbilityItemType_Risk_Resistance))]),
		})

		resp.AbilityList = append(resp.AbilityList, tmpAbilityInfo)
	}

	totalBusinessDiag := GetTotalBusinessDiag(ctx, guildId, endTm, mapTm2Data)
	monthBusinessDiag := GetMonthBusinessDiag(ctx, guildId, endTm, mapTmType2Score)
	recentBusinessDiag := GetRecentBusinessDiag(ctx, guildId, endTm.AddDate(0, -1, 0), mapTmType2Score, recentHasDataCnt)

	resp.BusinessDiag = totalBusinessDiag + monthBusinessDiag + recentBusinessDiag

	log.DebugWithCtx(ctx, "GetGuildRadarChart end uid:%d, req:%v resp:%+v", uid, req, resp)
	_ = web.ServeAPIJson(w, resp)
}

func CulStringDataRatio(ctx context.Context, guildId uint32, cntA, cntB string) (int32, string) {
	if cntA == cntB {
		log.DebugWithCtx(ctx, "CulRevenueFloatRatio equal guildId:%d cntA [%s] cntB [%s]", guildId, cntA, cntB)
		return 0, ""
	}

	floatCntA, err := strconv.ParseFloat(cntA, 64)
	if err != nil {
		log.ErrorWithCtx(ctx, "CurLastRatio Failed to parse guildId:%d cntA [%s], err %+v", guildId, cntA, err)
		return 0, ""
	}

	floatCntB, err := strconv.ParseFloat(cntB, 64)
	if err != nil {
		log.ErrorWithCtx(ctx, "CurLastRatio Failed to parse guildId:%d cntB [%s], err %+v", guildId, cntB, err)
		return 0, ""
	}

	if floatCntA == floatCntB {
		log.DebugWithCtx(ctx, "CulRevenueFloatRatio equal guildId:%d cntA [%s] cntB [%s]", guildId, cntA, cntB)
		return 0, ""
	}

	if floatCntA != 0 && floatCntB != 0 {
		floatRatio := CulFloatRatio(floatCntA, floatCntB)
		if floatRatio < 0 {
			return -1, fmt.Sprintf("%0.2f%%", -floatRatio*100)
		} else {
			return 1, fmt.Sprintf("%0.2f%%", floatRatio*100)
		}
	}

	if floatCntA == 0 {
		return -1, "100%"
	}

	if floatCntB == 0 {
		return 1, "100%"
	}

	return 0, ""
}

// 获取变化文案
func getChangeStr(incr int32, incrStr, stableStr string) string {
	if incr == 0 {
		return stableStr
	}

	if incr > 0 {
		return fmt.Sprintf("增长<span class=\"bold\" style=\"color: #FF5674;\"> %s</span>", incrStr)
	}

	return fmt.Sprintf("减少<span class=\"bold\" style=\"color: #17CBC2;\"> %s</span>", incrStr)
}

// 获取流水文案
func getFeeStr(val float64) string {
	/*
		if val >= 10000 {
			return fmt.Sprintf("%0.2f万", val/10000)
		}
	*/
	if val != math.Floor(val) {
		return fmt.Sprintf("%0.2f元", val)
	}

	return fmt.Sprintf("%0.f元", val)
}

// 获取百分比
func getPercentStr(val float64) string {
	val = val * 100
	if val != math.Floor(val) {
		return fmt.Sprintf("%0.2f%%", val)
	}

	return fmt.Sprintf("%0.f%%", val)
}

// 获取html头部
func getHtmlHead(title string) string {
	return fmt.Sprintf("<div class=\"block_title\"><span class=\"bold\" style=\"color: #000000;\">%s</span></div>", title)
}

// 获取总流水html
func getTotalFeeHtml(ctx context.Context, guildId, dimType uint32, revenue *api.Revenue, totalFee float64, lastTotalFeeStr,
	totalFlowRank, monthTop3Room string) string {
	var html string
	html += getHtmlHead(fmt.Sprintf("公会总流水 %s", getFeeStr(totalFee)))
	incr, ratioStr := CulStringDataRatio(ctx, guildId, revenue.TotalFee, lastTotalFeeStr)
	changeStr := getChangeStr(incr, ratioStr, "与上周持平")
	if dimType == uint32(api.GetGuildAbilityBusinessDiagReq_TimeDimType_Month) {
		changeStr = getChangeStr(incr, ratioStr, "与上月持平")
	}

	if totalFlowRank == "" {
		html += fmt.Sprintf("<div class=\"block_content\">%s</div>", changeStr)
	} else {
		fTotalFlowRank, err := strconv.ParseFloat(totalFlowRank, 64)
		if err != nil {
			log.ErrorWithCtx(ctx, "getTotalFeeHtml Failed to parse guildId:%d totalFlowRank [%s], err %+v", guildId, totalFlowRank, err)
			fTotalFlowRank = 1
		}

		html += fmt.Sprintf("<div class=\"block_content\">%s， 超过了 <span class=\"bold\" style=\"color: #000000;\">%s</span> 的同层级公会</div>",
			changeStr, fmt.Sprintf("%0.2f%%", (1-fTotalFlowRank)*100))
	}

	roomList := strings.Split(monthTop3Room, ",")
	cidList := make([]uint32, 0)
	for _, room := range roomList {
		cidInt, err := strconv.Atoi(room)
		if err != nil {
			log.ErrorWithCtx(ctx, "getTotalFeeHtml strconv.Atoi(room) failed guildId:%d room:%s err:%v", guildId, room, err)
		} else {
			cidList = append(cidList, uint32(cidInt))
		}
	}

	viewIdList := make([]string, 0)
	if len(cidList) != 0 {
		tmpMapCid2RoomInfo, err := batGetChannel(ctx, cidList)
		if err != nil {
			log.ErrorWithCtx(ctx, "getStabilityTopChannelList Failed to batGetChannel err guildId:%d err:%+v", guildId, err)
		} else {
			for _, cid := range cidList {
				if v, ok := tmpMapCid2RoomInfo[cid]; ok {
					viewIdList = append(viewIdList, fmt.Sprintf("【ID%s】", v.GetChannelViewId()))
				}
			}
		}
	}

	if len(viewIdList) != 0 {
		topStr := fmt.Sprintf("<div class=\"block_content\">本周流水TOP3房间分别是<span class=\"bold\" style=\"color: #000000;\">%s</span></div>", strings.Join(viewIdList, "、"))
		if dimType == uint32(api.GetGuildAbilityBusinessDiagReq_TimeDimType_Month) {
			topStr = fmt.Sprintf("<div class=\"block_content\">本月流水TOP3房间分别是<span class=\"bold\" style=\"color: #000000;\">%s</span></div>", strings.Join(viewIdList, "、"))
		}
		html += topStr
	}

	return fmt.Sprintf("<div class=\"block\">%s</div>", html)
}

// 获取其他种类流水html
func getOtherFeeHtml(ctx context.Context, guildId, dimType uint32, feeType string, contentFee float64, contentFeeStr, lastContentFeeStr string,
	mapIncr2Str map[int32]string) string {
	var html string
	html += getHtmlHead(fmt.Sprintf("%s %s", feeType, getFeeStr(contentFee)))
	incr, ratioStr := CulStringDataRatio(ctx, guildId, contentFeeStr, lastContentFeeStr)
	changeStr := getChangeStr(incr, ratioStr, "无变化")
	if dimType == uint32(api.GetGuildAbilityBusinessDiagReq_TimeDimType_Month) {
		changeStr = getChangeStr(incr, ratioStr, "与上月持平")
	}

	diagStr := fmt.Sprintf("<div class=\"block_content\">%s</div>", changeStr)
	if dimType == uint32(api.GetGuildAbilityBusinessDiagReq_TimeDimType_Month) {
		if mapIncr2Str[incr] != "" {
			diagStr = fmt.Sprintf("<div class=\"block_content\">%s, %s</div>", changeStr, mapIncr2Str[incr])
		}
	}

	html += diagStr

	return fmt.Sprintf("<div class=\"block\">%s</div>", html)
}

// 营收项分析
func getRevenueAbilityBusinessDiag(ctx context.Context, guildId uint32, req *api.GetGuildAbilityBusinessDiagReq, mapTm2Data map[string]*datahouse.OpdataMultiOperate,
	tmList []time.Time, mapTm2Str map[time.Time]string) *api.GetGuildAbilityBusinessDiagResp {
	resp := &api.GetGuildAbilityBusinessDiagResp{
		Revenue: &api.BusinessRevenue{},
	}

	html := ""
	for index, tm := range tmList {
		tStr := mapTm2Str[tm]
		revenue := &api.Revenue{
			Ts:              uint32(tm.Unix()),
			TotalFee:        "0",
			ContentFee:      "0",
			InteractFee:     "0",
			ParticularFee:   "0",
			PractitionerFee: "0",
		}

		var totalFee, contentFee, interactFee, particularFee, practitionerFee float64
		lastTotalFeeStr, lastContentFeeStr, lastInteractFeeStr, lastParticularFeeStr, lastPractitionerFeeStr := "0", "0", "0", "0", "0"
		var totalFlowRank, monthTop3Room string
		if data, ok := mapTm2Data[tStr]; ok {
			totalFee, _, revenue.TotalFee, lastTotalFeeStr = ParseFloatByStr(ctx, data.TotalFlow)
			contentFee, _, revenue.ContentFee, lastContentFeeStr = ParseFloatByStr(ctx, data.ContentFlow)
			interactFee, _, revenue.InteractFee, lastInteractFeeStr = ParseFloatByStr(ctx, data.InteractiveFlow)
			particularFee, _, revenue.ParticularFee, lastParticularFeeStr = ParseFloatByStr(ctx, data.SpecialFlow)
			practitionerFee, _, revenue.PractitionerFee, lastPractitionerFeeStr = ParseFloatByStr(ctx, data.PractitionerFlow)
			totalFlowRank = data.TotalFlowRank
			monthTop3Room = data.MonthTop3Rooms
		}

		if index == len(tmList)-1 {
			//公会总流水
			html += getTotalFeeHtml(ctx, guildId, req.GetDimType(), revenue, totalFee, lastTotalFeeStr, totalFlowRank, monthTop3Room)
			//内容品类流水
			html += getOtherFeeHtml(ctx, guildId, req.GetDimType(), "内容品类流水", contentFee, revenue.ContentFee, lastContentFeeStr, map[int32]string{
				1:  "高价值内容持续转化",
				0:  "需强化内容创新迭代",
				-1: "建议优化公会内容结构",
			})
			//互动品类流水
			html += getOtherFeeHtml(ctx, guildId, req.GetDimType(), "互动品类流水", interactFee, revenue.InteractFee, lastInteractFeeStr, map[int32]string{
				1:  "互动玩法带动效果显著",
				0:  "可尝试突破现有互动模式",
				-1: "需结合新玩法激活用户参与",
			})
			//特殊品类流水
			html += getOtherFeeHtml(ctx, guildId, req.GetDimType(), "特殊品类流水", particularFee, revenue.ParticularFee, lastParticularFeeStr, map[int32]string{})
			// 从业者流水
			html += getOtherFeeHtml(ctx, guildId, req.GetDimType(), "从业者流水", practitionerFee, revenue.PractitionerFee, lastPractitionerFeeStr, map[int32]string{})
		}

		resp.Revenue.RevList = append(resp.Revenue.RevList, revenue)
	}

	resp.Revenue.DiagList = append(resp.Revenue.DiagList, html)

	log.DebugWithCtx(ctx, "getRevenueAbilityBusinessDiag guildId:%d, req:%v mapTm2Data:%v resp:%v", guildId, req, mapTm2Data, resp)
	return resp
}

func CurValueLastRatio(guildId uint32, cntA, cntB string, ratio *api.LastRatio, pointCnt int) {
	floatCntA, err := strconv.ParseFloat(cntA, 64)
	if err != nil {
		log.Errorf("CurValueLastRatio Failed to parse guildId:%d cntA [%s], err %+v", guildId, cntA, err)
		floatCntA = 0
	}

	floatCntB, err := strconv.ParseFloat(cntB, 64)
	if err != nil {
		log.Errorf("CurValueLastRatio Failed to parse guildId:%d cntB [%s], err %+v", guildId, cntB, err)
		floatCntB = 0
	}
	format := "%0.f"
	if pointCnt > 0 {
		format = "%." + strconv.Itoa(pointCnt) + "f"
	}

	floatRatio := floatCntA - floatCntB
	if floatRatio < 0 {
		ratio.Ratio = -1
		ratio.RatioValue = fmt.Sprintf(format, -floatRatio)
	} else if floatRatio == 0 {
		ratio.Ratio = 0
		ratio.RatioValue = "-"
	} else {
		ratio.Ratio = 1
		ratio.RatioValue = fmt.Sprintf(format, floatRatio)
	}

	log.Debugf("CurValueLastRatio guildId:%d cntA [%s] cntB [%s] ratio [%+v]", guildId, cntA, cntB, ratio)
	return
}

func CurStringDataValue(guildId uint32, cntA, cntB string) (int32, float64) {
	floatCntA, err := strconv.ParseFloat(cntA, 64)
	if err != nil {
		log.Errorf("CurStringDataValue Failed to parse guildId:%d cntA [%s], err %+v", guildId, cntA, err)
		return 0, 0
	}

	floatCntB, err := strconv.ParseFloat(cntB, 64)
	if err != nil {
		log.Errorf("CurStringDataValue Failed to parse guildId:%d cntB [%s], err %+v", guildId, cntB, err)
		return 0, 0
	}

	var incr int32 = 0
	floatVal := floatCntA - floatCntB
	if floatVal > 0 {
		incr = 1
	} else if floatVal < 0 {
		incr = -1
		floatVal = -floatVal
	}

	log.Debugf("CurStringDataValue guildId:%d cntA [%s] cntB [%s] floatVal [%+v]", guildId, cntA, cntB, floatVal)
	return incr, floatVal
}

// 获取公会等级水平
func getGuildLevelStr(rank string, suffix string) string {
	if rank == "" {
		return suffix
	}

	fRank, _ := strconv.ParseFloat(rank, 64)

	for _, v := range conf.GuildLevelConfigList {
		if fRank > v.MinVal && fRank <= v.MaxVal || v.MaxVal == 0 {
			return fmt.Sprintf("，%s%s", v.LevelStr, suffix)
		}
	}
	return suffix
}

// 招新项分析
func getRecruitAbilityBusinessDiag(ctx context.Context, guildId uint32, req *api.GetGuildAbilityBusinessDiagReq, mapTm2Data map[string]*datahouse.OpdataMultiOperate,
	tmList []time.Time, mapTm2Str map[time.Time]string) *api.GetGuildAbilityBusinessDiagResp {
	resp := &api.GetGuildAbilityBusinessDiagResp{
		Recruit: &api.BusinessRecruit{
			NewPractitionerRatio:            &api.LastRatio{Ratio: 0, RatioValue: "-"},
			NewPractitionerIncomeRatio:      &api.LastRatio{Ratio: 0, RatioValue: "-"},
			NewValidPractitionerRatio:       &api.LastRatio{Ratio: 0, RatioValue: "-"},
			NewValidPractitionerIncomeRatio: &api.LastRatio{Ratio: 0, RatioValue: "-"},
			NewProPractitionerRatio:         &api.LastRatio{Ratio: 0, RatioValue: "-"},
			NewProPractitionerIncomeRatio:   &api.LastRatio{Ratio: 0, RatioValue: "-"},
		},
	}

	log.DebugWithCtx(ctx, "getRecruitAbilityBusinessDiag guildId:%d, req:%v mapTm2Data:%v", guildId, req, mapTm2Data)
	for index, tm := range tmList {
		tStr := mapTm2Str[tm]
		recruit := &api.Recruit{
			Ts:                         uint32(tm.Unix()),
			NewPractitioner:            "0",
			NewPractitionerIncome:      "0",
			NewValidPractitioner:       "0",
			NewValidPractitionerIncome: "0",
			NewProPractitioner:         "0",
			NewProPractitionerIncome:   "0",
		}

		LastRecruit := &api.Recruit{
			NewPractitioner:            "0",
			NewPractitionerIncome:      "0",
			NewValidPractitioner:       "0",
			NewValidPractitionerIncome: "0",
			NewProPractitioner:         "0",
			NewProPractitionerIncome:   "0",
		}

		var newPractitioner, newPractitionerIncome, newValidPractitioner, newValidPractitionerIncome, newProPractitioner, newProPractitionerIncome float64
		var newPractitionersRank, newValidPractitionersRank, newProPractitionersRank, newPractitionerIncomeRank, newValidPractitionerIncomeRank, newProPractitionerIncomeRank string
		if data, ok := mapTm2Data[tStr]; ok {
			newPractitioner, _, recruit.NewPractitioner, LastRecruit.NewPractitioner = ParseFloatByStr(ctx, data.NewPractitioners)
			newPractitionerIncome, _, recruit.NewPractitionerIncome, LastRecruit.NewPractitionerIncome = ParseFloatByStr(ctx, data.NewPractitionerIncome)
			newValidPractitioner, _, recruit.NewValidPractitioner, LastRecruit.NewValidPractitioner = ParseFloatByStr(ctx, data.NewValidPractitioners)
			newValidPractitionerIncome, _, recruit.NewValidPractitionerIncome, LastRecruit.NewValidPractitionerIncome = ParseFloatByStr(ctx, data.NewValidPractitionerIncome)
			newProPractitioner, _, recruit.NewProPractitioner, LastRecruit.NewProPractitioner = ParseFloatByStr(ctx, data.NewProPractitioners)
			newProPractitionerIncome, _, recruit.NewProPractitionerIncome, LastRecruit.NewProPractitionerIncome = ParseFloatByStr(ctx, data.NewProPractitionerIncome)

			newPractitionersRank = data.NewPractitionersRank
			newValidPractitionersRank = data.NewValidPractitionersRank
			newProPractitionersRank = data.NewProPractitionersRank
			newPractitionerIncomeRank = data.NewPractitionerIncomeRank
			newValidPractitionerIncomeRank = data.NewValidPractitionerIncomeRank
			newProPractitionerIncomeRank = data.NewProPractitionerIncomeRank
		}

		if index == len(tmList)-1 {
			CurValueLastRatio(guildId, recruit.NewPractitioner, LastRecruit.NewPractitioner, resp.Recruit.NewPractitionerRatio, 0)
			CurValueLastRatio(guildId, recruit.NewPractitionerIncome, LastRecruit.NewPractitionerIncome, resp.Recruit.NewPractitionerIncomeRatio, 2)
			CurValueLastRatio(guildId, recruit.NewValidPractitioner, LastRecruit.NewValidPractitioner, resp.Recruit.NewValidPractitionerRatio, 0)
			CurValueLastRatio(guildId, recruit.NewValidPractitionerIncome, LastRecruit.NewValidPractitionerIncome, resp.Recruit.NewValidPractitionerIncomeRatio, 2)
			CurValueLastRatio(guildId, recruit.NewProPractitioner, LastRecruit.NewProPractitioner, resp.Recruit.NewProPractitionerRatio, 0)
			CurValueLastRatio(guildId, recruit.NewProPractitionerIncome, LastRecruit.NewProPractitionerIncome, resp.Recruit.NewProPractitionerIncomeRatio, 2)

			if newPractitioner != 0 {
				resp.Recruit.NewPractitionerIncomeAve = fmt.Sprintf("%.2f", newPractitionerIncome/newPractitioner)
			} else {
				resp.Recruit.NewPractitionerIncomeAve = "0"
			}

			if newValidPractitioner != 0 {
				resp.Recruit.NewValidPractitionerIncomeAve = fmt.Sprintf("%.2f", newValidPractitionerIncome/newValidPractitioner)
			} else {
				resp.Recruit.NewValidPractitionerIncomeAve = "0"
			}

			if newProPractitioner != 0 {
				resp.Recruit.NewProPractitionerIncomeAve = fmt.Sprintf("%.2f", newProPractitionerIncome/newProPractitioner)
			} else {
				resp.Recruit.NewProPractitionerIncomeAve = "0"
			}

			var htmlCnt, htmlIncome string

			cntItemHtml := getHtmlHead(fmt.Sprintf("新增从业者 %s人", recruit.NewPractitioner))
			incr, gapVal := CurStringDataValue(guildId, recruit.NewPractitioner, LastRecruit.NewPractitioner)
			cntItemHtml += fmt.Sprintf("<div class=\"block_content\">%s%s</div>", getChangeStr(incr, fmt.Sprintf("%.0f人", gapVal), "无增长"), getGuildLevelStr(newPractitionersRank, ""))
			htmlCnt += fmt.Sprintf("<div class=\"block\">%s</div>", cntItemHtml)

			cntItemHtml = getHtmlHead(fmt.Sprintf("新增有效从业者 %s人", recruit.NewValidPractitioner))
			incr, gapVal = CurStringDataValue(guildId, recruit.NewValidPractitioner, LastRecruit.NewValidPractitioner)
			cntItemHtml += fmt.Sprintf("<div class=\"block_content\">%s%s</div>", getChangeStr(incr, fmt.Sprintf("%.0f人", gapVal), "无增长"), getGuildLevelStr(newValidPractitionersRank, ""))
			htmlCnt += fmt.Sprintf("<div class=\"block\">%s</div>", cntItemHtml)

			cntItemHtml = getHtmlHead(fmt.Sprintf("新增专业从业者 %s人", recruit.NewProPractitioner))
			incr, gapVal = CurStringDataValue(guildId, recruit.NewProPractitioner, LastRecruit.NewProPractitioner)
			cntItemHtml += fmt.Sprintf("<div class=\"block_content\">%s%s</div>", getChangeStr(incr, fmt.Sprintf("%.0f人", gapVal), "无增长"), getGuildLevelStr(newProPractitionersRank, ""))
			htmlCnt += fmt.Sprintf("<div class=\"block\">%s</div>", cntItemHtml)
			resp.Recruit.DiagList = append(resp.Recruit.DiagList, htmlCnt)

			cntItemHtml = getHtmlHead(fmt.Sprintf("新增从业者收礼 %s", getFeeStr(newPractitionerIncome)))
			incr, gapVal = CurStringDataValue(guildId, recruit.NewPractitionerIncome, LastRecruit.NewPractitionerIncome)
			cntItemHtml += fmt.Sprintf("<div class=\"block_content\">%s%s</div>", getChangeStr(incr, getFeeStr(gapVal), "无增长"), getGuildLevelStr(newPractitionerIncomeRank, ""))
			htmlIncome += fmt.Sprintf("<div class=\"block\">%s</div>", cntItemHtml)

			cntItemHtml = getHtmlHead(fmt.Sprintf("新增有效从业者收礼 %s", getFeeStr(newValidPractitionerIncome)))
			incr, gapVal = CurStringDataValue(guildId, recruit.NewValidPractitionerIncome, LastRecruit.NewValidPractitionerIncome)
			cntItemHtml += fmt.Sprintf("<div class=\"block_content\">%s%s</div>", getChangeStr(incr, getFeeStr(gapVal), "无增长"), getGuildLevelStr(newValidPractitionerIncomeRank, ""))
			htmlIncome += fmt.Sprintf("<div class=\"block\">%s</div>", cntItemHtml)

			cntItemHtml = getHtmlHead(fmt.Sprintf("新增专业从业者收礼 %s", getFeeStr(newProPractitionerIncome)))
			incr, gapVal = CurStringDataValue(guildId, recruit.NewProPractitionerIncome, LastRecruit.NewProPractitionerIncome)
			cntItemHtml += fmt.Sprintf("<div class=\"block_content\">%s%s</div>", getChangeStr(incr, getFeeStr(gapVal), "无增长"), getGuildLevelStr(newProPractitionerIncomeRank, ""))
			htmlIncome += fmt.Sprintf("<div class=\"block\">%s</div>", cntItemHtml)

			resp.Recruit.DiagList = append(resp.Recruit.DiagList, htmlIncome)

		}

		resp.Recruit.RecruitList = append(resp.Recruit.RecruitList, recruit)
	}

	log.DebugWithCtx(ctx, "getRecruitAbilityBusinessDiag guildId:%d, req:%v mapTm2Data:%v resp:%v", guildId, req, mapTm2Data, resp)
	return resp
}

// 孵化项分析
func getHatchAbilityBusinessDiag(ctx context.Context, guildId uint32, req *api.GetGuildAbilityBusinessDiagReq, mapTm2Data map[string]*datahouse.OpdataMultiOperate,
	tmList []time.Time, mapTm2Str map[time.Time]string) *api.GetGuildAbilityBusinessDiagResp {
	resp := &api.GetGuildAbilityBusinessDiagResp{
		Hatch: &api.BusinessHatch{
			SignPractitionerRatio:               &api.LastRatio{Ratio: 0, RatioValue: "-"},
			SignPractitionerIncomeRatio:         &api.LastRatio{Ratio: 0, RatioValue: "-"},
			NewPractitionersRetentionRatio:      &api.LastRatio{Ratio: 0, RatioValue: "-"},
			NewPractitionerRetentionIncomeRatio: &api.LastRatio{Ratio: 0, RatioValue: "-"},
			ProPractitionerRatio:                &api.LastRatio{Ratio: 0, RatioValue: "-"},
			ProPractitionerIncomeRatio:          &api.LastRatio{Ratio: 0, RatioValue: "-"},
		},
	}

	for index, tm := range tmList {
		tStr := mapTm2Str[tm]
		hatch := &api.Hatch{
			Ts:                             uint32(tm.Unix()),
			SignPractitioner:               "0",
			SignPractitionerIncome:         "0",
			SignPractitionerRemain:         "0",
			NewPractitionersRetention:      "0",
			NewPractitionerRetentionIncome: "0",
			ProPractitioner:                "0",
			ProPractitionerIncome:          "0",
			ProPractitionerRemain:          "0",
		}

		LastHatch := &api.Hatch{
			SignPractitioner:               "0",
			SignPractitionerIncome:         "0",
			SignPractitionerRemain:         "0",
			NewPractitionersRetention:      "0",
			NewPractitionerRetentionIncome: "0",
			ProPractitioner:                "0",
			ProPractitionerIncome:          "0",
			ProPractitionerRemain:          "0",
		}

		var signPractitioner, signPractitionerIncome, newPractitionersRetention, newPractitionerRetentionIncome, proPractitioner, proPractitionerIncome float64
		var signedPractitionersRank, newPractitionersRetentionRank, proPractitionersRank, signedIncomeRank, newPractitionerRetentionIncomeRank, proPractitionerIncomeRank string
		if data, ok := mapTm2Data[tStr]; ok {
			signPractitioner, _, hatch.SignPractitioner, LastHatch.SignPractitioner = ParseFloatByStr(ctx, data.SignedPractitioners)
			signPractitionerIncome, _, hatch.SignPractitionerIncome, LastHatch.SignPractitionerIncome = ParseFloatByStr(ctx, data.SignedIncome)
			newPractitionersRetention, _, hatch.NewPractitionersRetention, LastHatch.NewPractitionersRetention = ParseFloatByStr(ctx, data.NewPractitionersRetention)
			newPractitionerRetentionIncome, _, hatch.NewPractitionerRetentionIncome, LastHatch.NewPractitionerRetentionIncome = ParseFloatByStr(ctx, data.NewPractitionerRetentionIncome)
			proPractitioner, _, hatch.ProPractitioner, LastHatch.ProPractitioner = ParseFloatByStr(ctx, data.ProPractitioners)
			proPractitionerIncome, _, hatch.ProPractitionerIncome, LastHatch.ProPractitionerIncome = ParseFloatByStr(ctx, data.ProPractitionerIncome)

			signedPractitionersRank = data.SignedPractitionersRank
			newPractitionersRetentionRank = data.NewPractitionersRetentionRank
			proPractitionersRank = data.ProPractitionersRank
			signedIncomeRank = data.SignedIncomeRank
			newPractitionerRetentionIncomeRank = data.NewPractitionerRetentionIncomeRank
			proPractitionerIncomeRank = data.ProPractitionerIncomeRank
		}

		if index == len(tmList)-1 {
			CurValueLastRatio(guildId, hatch.SignPractitioner, LastHatch.SignPractitioner, resp.Hatch.SignPractitionerRatio, 0)
			CurValueLastRatio(guildId, hatch.SignPractitionerIncome, LastHatch.SignPractitionerIncome, resp.Hatch.SignPractitionerIncomeRatio, 2)
			CurValueLastRatio(guildId, hatch.NewPractitionersRetention, LastHatch.NewPractitionersRetention, resp.Hatch.NewPractitionersRetentionRatio, 0)
			CurValueLastRatio(guildId, hatch.NewPractitionerRetentionIncome, LastHatch.NewPractitionerRetentionIncome, resp.Hatch.NewPractitionerRetentionIncomeRatio, 2)
			CurValueLastRatio(guildId, hatch.ProPractitioner, LastHatch.ProPractitioner, resp.Hatch.ProPractitionerRatio, 0)
			CurValueLastRatio(guildId, hatch.ProPractitionerIncome, LastHatch.ProPractitionerIncome, resp.Hatch.ProPractitionerIncomeRatio, 2)

			if signPractitioner != 0 {
				resp.Hatch.SignPractitionerIncomeAve = fmt.Sprintf("%.2f", signPractitionerIncome/signPractitioner)
			} else {
				resp.Hatch.SignPractitionerIncomeAve = "0"
			}

			if newPractitionersRetention != 0 {
				resp.Hatch.NewPractitionersRetentionAve = fmt.Sprintf("%.2f", newPractitionerRetentionIncome/newPractitionersRetention)
			} else {
				resp.Hatch.NewPractitionersRetentionAve = "0"
			}

			if proPractitioner != 0 {
				resp.Hatch.ProPractitionerIncomeAve = fmt.Sprintf("%.2f", proPractitionerIncome/proPractitioner)
			} else {
				resp.Hatch.ProPractitionerIncomeAve = "0"
			}

			var htmlCnt, htmlIncome string

			cntItemHtml := getHtmlHead(fmt.Sprintf("签约从业者 %s人", hatch.SignPractitioner))
			incr, gapVal := CurStringDataValue(guildId, hatch.SignPractitioner, LastHatch.SignPractitioner)
			cntItemHtml += fmt.Sprintf("<div class=\"block_content\">%s%s</div>", getChangeStr(incr, fmt.Sprintf("%.0f人", gapVal), "无变化"), getGuildLevelStr(signedPractitionersRank, ""))
			htmlCnt += fmt.Sprintf("<div class=\"block\">%s</div>", cntItemHtml)

			cntItemHtml = getHtmlHead(fmt.Sprintf("上月新增从业者本月留存 %s人", hatch.NewPractitionersRetention))
			incr, gapVal = CurStringDataValue(guildId, hatch.NewPractitionersRetention, LastHatch.NewPractitionersRetention)
			cntItemHtml += fmt.Sprintf("<div class=\"block_content\">%s%s</div>", getChangeStr(incr, fmt.Sprintf("%.0f人", gapVal), "无变化"), getGuildLevelStr(newPractitionersRetentionRank, ""))
			htmlCnt += fmt.Sprintf("<div class=\"block\">%s</div>", cntItemHtml)

			cntItemHtml = getHtmlHead(fmt.Sprintf("专业从业者 %s人", hatch.ProPractitioner))
			incr, gapVal = CurStringDataValue(guildId, hatch.ProPractitioner, LastHatch.ProPractitioner)
			cntItemHtml += fmt.Sprintf("<div class=\"block_content\">%s%s</div>", getChangeStr(incr, fmt.Sprintf("%.0f人", gapVal), "无变化"), getGuildLevelStr(proPractitionersRank, ""))
			htmlCnt += fmt.Sprintf("<div class=\"block\">%s</div>", cntItemHtml)

			resp.Hatch.DiagList = append(resp.Hatch.DiagList, htmlCnt)

			cntItemHtml = getHtmlHead(fmt.Sprintf("签约从业者收礼值 %s", getFeeStr(signPractitionerIncome)))
			incr, gapVal = CurStringDataValue(guildId, hatch.SignPractitionerIncome, LastHatch.SignPractitionerIncome)
			cntItemHtml += fmt.Sprintf("<div class=\"block_content\">%s%s</div>", getChangeStr(incr, getFeeStr(gapVal), "无变化"), getGuildLevelStr(signedIncomeRank, ""))
			htmlIncome += fmt.Sprintf("<div class=\"block\">%s</div>", cntItemHtml)

			cntItemHtml = getHtmlHead(fmt.Sprintf("上月新增从业者本月留存者收礼 %s", getFeeStr(newPractitionerRetentionIncome)))
			incr, gapVal = CurStringDataValue(guildId, hatch.NewPractitionerRetentionIncome, LastHatch.NewPractitionerRetentionIncome)
			cntItemHtml += fmt.Sprintf("<div class=\"block_content\">%s%s</div>", getChangeStr(incr, getFeeStr(gapVal), "无变化"), getGuildLevelStr(newPractitionerRetentionIncomeRank, ""))
			htmlIncome += fmt.Sprintf("<div class=\"block\">%s</div>", cntItemHtml)

			cntItemHtml = getHtmlHead(fmt.Sprintf("新增专业从业者收礼 %s", getFeeStr(proPractitionerIncome)))
			incr, gapVal = CurStringDataValue(guildId, hatch.ProPractitionerIncome, LastHatch.ProPractitionerIncome)
			cntItemHtml += fmt.Sprintf("<div class=\"block_content\">%s%s</div>", getChangeStr(incr, getFeeStr(gapVal), "无变化"), getGuildLevelStr(proPractitionerIncomeRank, ""))
			htmlIncome += fmt.Sprintf("<div class=\"block\">%s</div>", cntItemHtml)

			resp.Hatch.DiagList = append(resp.Hatch.DiagList, htmlIncome)
		}

		resp.Hatch.HatchList = append(resp.Hatch.HatchList, hatch)
	}

	log.DebugWithCtx(ctx, "getHatchAbilityBusinessDiag guildId:%d, req:%v mapTm2Data:%v resp:%v", guildId, req, mapTm2Data, resp)
	return resp
}

// 安全项分析
func getSafetyAbilityBusinessDiag(ctx context.Context, guildId uint32, req *api.GetGuildAbilityBusinessDiagReq, mapTm2Data map[string]*datahouse.OpdataMultiOperate,
	tmList []time.Time, mapTm2Str map[time.Time]string) *api.GetGuildAbilityBusinessDiagResp {
	resp := &api.GetGuildAbilityBusinessDiagResp{
		Safety: &api.BusinessSafety{
			VioRatioLastRatio:    &api.LastRatio{Ratio: 0, RatioValue: "-"},
			VioPractitionerRatio: &api.LastRatio{Ratio: 0, RatioValue: "-"},
			FacePassRatio:        &api.LastRatio{Ratio: 0, RatioValue: "-"},
			VioDetailList:        make(map[string]float32),
		},
	}

	for index, tm := range tmList {
		tStr := mapTm2Str[tm]
		safety := &api.Safety{
			Ts:               uint32(tm.Unix()),
			VioRatio:         "0",
			VioPractitioner:  "0",
			FacePassRatio:    "0",
			FacePassRatioOld: "0",
			FacePassRatioNew: "0",
		}

		Lastsafety := &api.Safety{
			VioRatio:         "0",
			VioPractitioner:  "0",
			FacePassRatio:    "0",
			FacePassRatioOld: "0",
			FacePassRatioNew: "0",
		}

		maxVioName := ""
		var vioRatio, facePassRatio float64
		if data, ok := mapTm2Data[tStr]; ok {
			vioRatio, _, safety.VioRatio, Lastsafety.VioRatio = ParseFloatByStr(ctx, data.DailyViolationRate)
			_, _, safety.VioPractitioner, Lastsafety.VioPractitioner = ParseFloatByStr(ctx, data.ViolationPractitioners)
			facePassRatio, _, safety.FacePassRatio, Lastsafety.FacePassRatio = ParseFloatByStr(ctx, data.FaceVerificationRate)
			_, _, safety.FacePassRatioNew, safety.FacePassRatioOld = ParseFloatByStr(ctx, data.FaceVerificationRateNewOld)

			if index == len(tmList)-1 {
				str := data.ViolationDetail
				str = strings.ReplaceAll(str, "[", "")
				str = strings.ReplaceAll(str, "]", "")
				str = strings.ReplaceAll(str, "\"", "")
				str = strings.ReplaceAll(str, " ", "")

				strList := strings.Split(str, ",")
				var maxVio float32
				for _, v := range strList {
					list := strings.Split(v, "-")
					if len(list) == 2 {
						val, err := strconv.ParseFloat(list[1], 64)
						if err != nil {
							log.ErrorWithCtx(ctx, "getSafetyAbilityBusinessDiag Failed to parse guildId:%d ViolationDetail [%s], err %+v", guildId, list[1], err)
						} else {
							resp.Safety.VioDetailList[list[0]] = float32(val)
							if float32(val) > maxVio || maxVio == 0 {
								maxVio = float32(val)
								maxVioName = list[0]
							}
						}
					}
				}
			}
		}

		if index == len(tmList)-1 {
			CurValueLastRatio(guildId, safety.VioRatio, Lastsafety.VioRatio, resp.Safety.VioRatioLastRatio, 4)
			CurValueLastRatio(guildId, safety.VioPractitioner, Lastsafety.VioPractitioner, resp.Safety.VioPractitionerRatio, 0)
			CurValueLastRatio(guildId, safety.FacePassRatio, Lastsafety.FacePassRatio, resp.Safety.FacePassRatio, 4)

			var html string
			html = getHtmlHead("本周数据分析")
			if req.GetDimType() == uint32(api.GetGuildAbilityBusinessDiagReq_TimeDimType_Month) {
				html = getHtmlHead("本月数据分析")
			}

			incr, gapVal := CurStringDataValue(guildId, safety.VioRatio, Lastsafety.VioRatio)
			cntItemHtml := fmt.Sprintf("对比上周，日均违规率 <span class=\"bold\" style=\"color: #1E1F21;\">%s</span>，%s，", fmt.Sprintf("%0.2f%%", vioRatio*100), getChangeStr(incr, fmt.Sprintf("%0.2f%%", gapVal*100), "无变化"))
			if req.GetDimType() == uint32(api.GetGuildAbilityBusinessDiagReq_TimeDimType_Month) {
				cntItemHtml = fmt.Sprintf("对比上月，日均违规率 <span class=\"bold\" style=\"color: #1E1F21;\">%s</span>，%s，", fmt.Sprintf("%0.2f%%", vioRatio*100), getChangeStr(incr, fmt.Sprintf("%0.2f%%", gapVal*100), "无变化"))
			}
			incr, fGapVal := CurStringDataValue(guildId, safety.VioPractitioner, Lastsafety.VioPractitioner)
			cntItemHtml += fmt.Sprintf("违规从业者 <span class=\"bold\" style=\"color: #1E1F21;\">%s人</span>，%s，", safety.VioPractitioner, getChangeStr(incr, fmt.Sprintf("%.0f人", fGapVal), "无变化"))
			incr, gapVal = CurStringDataValue(guildId, safety.FacePassRatio, Lastsafety.FacePassRatio)
			cntItemHtml += fmt.Sprintf("人脸验证通过率 <span class=\"bold\" style=\"color: #1E1F21;\">%s</span>，%s", fmt.Sprintf("%0.2f%%", facePassRatio*100), getChangeStr(incr, fmt.Sprintf("%0.2f%%", gapVal*100), "无变化"))
			html += fmt.Sprintf("<div class=\"block_content\">%s</div>", cntItemHtml)

			if maxVioName != "" && req.GetDimType() == uint32(api.GetGuildAbilityBusinessDiagReq_TimeDimType_Month) {
				html += fmt.Sprintf("<div class=\"block_content\">本月违规分布主要在 <span class=\"bold\" style=\"color: #1E1F21;\">【%s】</span>，请公会做好相关内容培训</div>", maxVioName)
			}
			resp.Safety.DiagList = append(resp.Safety.DiagList, fmt.Sprintf("<div class=\"block\">%s</div>", html))
		}

		resp.Safety.SafetyList = append(resp.Safety.SafetyList, safety)
	}

	log.DebugWithCtx(ctx, "getSafetyAbilityBusinessDiag guildId:%d, req:%v mapTm2Data:%v resp:%v", guildId, req, mapTm2Data, resp)
	return resp
}

func fillGuildChannelBusinessData(data *api.GuildChannelBusinessData, roomData *datahouse.OpdataMultiRoomStats) {
	data.Fee = "0"
	data.SignIncome = "0"
	data.SignIncomeRatio = "0"
	data.ValidLiveCnt = "0"
	data.BaseLiveCnt = "0"
	data.NewPractitioner = "0"
	data.NewPractitionersRetention = "0"
	data.ProPractitioner = "0"

	if roomData != nil {
		if roomData.RoomRevenue != "" {
			data.Fee = roomData.RoomRevenue
		}
		if roomData.SignedMemberGiftIncome != "" {
			data.SignIncome = roomData.SignedMemberGiftIncome
		}
		if roomData.SignedMemberGiftRatio != "" {
			data.SignIncomeRatio = roomData.SignedMemberGiftRatio
		}
		if roomData.EffectiveOpeningDays != "" {
			data.ValidLiveCnt = roomData.EffectiveOpeningDays
		}
		if roomData.BaseOpeningDays != "" {
			data.BaseLiveCnt = roomData.BaseOpeningDays
		}
		if roomData.NewPractitionersCount != "" {
			data.NewPractitioner = roomData.NewPractitionersCount
		}
		if roomData.NewPractitionersRemain != "" {
			data.NewPractitionersRetention = roomData.NewPractitionersRemain
		}
		if roomData.ProfessionalPractitionersCount != "" {
			data.ProPractitioner = roomData.ProfessionalPractitionersCount
		}
	}
}

func getStabilityTopChannelList(ctx context.Context, guildId uint32, monthTop3Room string, tm time.Time, timeDim string) []*api.GuildChannelBusinessData {
	dataList := make([]*api.GuildChannelBusinessData, 0)
	cidList := make([]uint32, 0)
	roomList := strings.Split(monthTop3Room, ",")
	for _, room := range roomList {
		cidInt, err := strconv.Atoi(room)
		if err != nil {
			log.ErrorWithCtx(ctx, "getStabilityTopChannelList Failed to parse guildId:%d MonthTop3Rooms [%s], err %+v", guildId, room, err)
		} else {
			cidList = append(cidList, uint32(cidInt))
		}
	}

	if len(cidList) == 0 {
		return dataList
	}

	startTm := tm.AddDate(0, 0, -7)
	if timeDim == "month" {
		startTm = tm.AddDate(0, -1, 0)
	}

	mapTm2RoomData := make(map[string]*datahouse.OpdataMultiRoomStats)
	mapCid2TagName := make(map[uint32]string)
	mapId2CidList := make(map[uint32][]uint32)

	mapCid2RoomInfo, err := batGetChannel(ctx, cidList)
	if err != nil {
		log.ErrorWithCtx(ctx, "getStabilityTopChannelList Failed to batGetChannel err guildId:%d err:%+v", guildId, err)
		return dataList
	}

	for _, cid := range cidList {
		if mapCid2RoomInfo[cid].GetBindId() != 0 {
			mapId2CidList[mapCid2RoomInfo[cid].GetBindId()] = append(mapId2CidList[mapCid2RoomInfo[cid].GetBindId()], cid)
		}
	}

	wg := sync.WaitGroup{}
	wg.Add(2)

	go func() {
		defer wg.Done()
		for id, list := range mapId2CidList {
			OpdataMultiRoomStatsResp, err := datahouse.QueryOpdataMultiRoomStats(ctx, &datahouse.OpdataMultiRoomStatsReq{
				StartDate:   startTm,
				EndDate:     tm,
				TimeDim:     timeDim,
				GuildIdList: []uint32{id},
				RoomIdList:  list,
				Page:        1,
				PageSize:    10,
			})
			if err != nil {
				log.ErrorWithCtx(ctx, "getStabilityTopChannelList Failed to QueryOpdataMultiRoomStats err guildId:%d err:%+v", guildId, err)
			} else {
				for _, data := range OpdataMultiRoomStatsResp.Data {
					mapTm2RoomData[fmt.Sprintf("%s-%d", data.DataDate, data.RoomId)] = data
				}
			}
		}
	}()

	go func() {
		defer wg.Done()
		tagResp, err := models.GetModelServer().EntertainmentCli.BatchGetChannelTag(ctx, 0, &enterPb.BatchGetChannelTagReq{
			ChannelIdList: cidList,
		})
		if err != nil || len(cidList) != len(tagResp.GetChannelTagList()) {
			log.ErrorWithCtx(ctx, "getStabilityTopChannelList Failed to BatchGetChannelTag err guildId:%d err:%+v", guildId, err)
		} else {
			for i, info := range tagResp.GetChannelTagList() {
				mapCid2TagName[cidList[i]] = info.GetName()
			}
		}
	}()

	wg.Wait()

	for _, cid := range cidList {
		tmpData := &api.GuildChannelBusinessData{
			Cid:                            cid,
			ViewId:                         mapCid2RoomInfo[cid].GetChannelViewId(),
			Name:                           mapCid2RoomInfo[cid].GetName(),
			Tag:                            mapCid2TagName[cid],
			FeeRatio:                       &api.LastRatio{Ratio: 0, RatioValue: "-"},
			SignIncomeLastRatio:            &api.LastRatio{Ratio: 0, RatioValue: "-"},
			SignIncomeRatioLastRatio:       &api.LastRatio{Ratio: 0, RatioValue: "-"},
			ValidLiveCntRatio:              &api.LastRatio{Ratio: 0, RatioValue: "-"},
			BaseLiveCntRatio:               &api.LastRatio{Ratio: 0, RatioValue: "-"},
			NewPractitionerRatio:           &api.LastRatio{Ratio: 0, RatioValue: "-"},
			NewPractitionersRetentionRatio: &api.LastRatio{Ratio: 0, RatioValue: "-"},
			ProPractitionerRatio:           &api.LastRatio{Ratio: 0, RatioValue: "-"},
			Ts:                             uint32(tm.Unix()),
		}
		tmpLastData := &api.GuildChannelBusinessData{}

		fillGuildChannelBusinessData(tmpData, mapTm2RoomData[fmt.Sprintf("%s-%d", tm.Format(time.DateOnly), cid)])
		fillGuildChannelBusinessData(tmpLastData, mapTm2RoomData[fmt.Sprintf("%s-%d", startTm.Format(time.DateOnly), cid)])

		CurLastRatio(guildId, tmpData.Fee, tmpLastData.Fee, tmpData.FeeRatio)
		CurLastRatio(guildId, tmpData.SignIncome, tmpLastData.SignIncome, tmpData.SignIncomeLastRatio)
		CurLastRatio(guildId, tmpData.SignIncomeRatio, tmpLastData.SignIncomeRatio, tmpData.SignIncomeRatioLastRatio)
		CurValueLastRatio(guildId, tmpData.ValidLiveCnt, tmpLastData.ValidLiveCnt, tmpData.ValidLiveCntRatio, 0)
		CurValueLastRatio(guildId, tmpData.BaseLiveCnt, tmpLastData.BaseLiveCnt, tmpData.BaseLiveCntRatio, 0)
		CurValueLastRatio(guildId, tmpData.NewPractitioner, tmpLastData.NewPractitioner, tmpData.NewPractitionerRatio, 0)
		CurValueLastRatio(guildId, tmpData.NewPractitionersRetention, tmpLastData.NewPractitionersRetention, tmpData.NewPractitionersRetentionRatio, 0)
		CurValueLastRatio(guildId, tmpData.ProPractitioner, tmpLastData.ProPractitioner, tmpData.ProPractitionerRatio, 0)

		dataList = append(dataList, tmpData)
	}

	log.DebugWithCtx(ctx, "getStabilityTopChannelList guildId:%d mapTm2RoomData:%v mapCid2RoomInfo:%v mapCid2TagName:%v", guildId, mapTm2RoomData, mapCid2RoomInfo, mapCid2TagName)
	return dataList
}

func getGuildMultiChannelList(ctx context.Context, guildId uint32) []string {
	//母公会能看子母的所有房间，子公会只能看自己公会的房间
	guildIdList := models.GetModelServer().GetSignDyConfig().GetChildGuilds(guildId)

	guildChannelMap, sErr := models.GetModelServer().ChannelGuildGoCli.BatGetChannelGuildMap(ctx, guildIdList, uint32(channelpb_.ChannelType_GUILD_PUBLIC_FUN_CHANNEL_TYPE))
	if sErr != nil {
		log.ErrorWithCtx(ctx, "getGuildMultiChannelList Failed to BatGetChannelGuildMap err guildId:%d sErr:%+v", guildId, sErr)
		return []string{}
	}

	channelIdList := make([]uint32, 0)
	for _, item := range guildChannelMap {
		channelIdList = append(channelIdList, item.ChannelIds...)
	}

	mapCid2RoomInfo, err := batGetChannel(ctx, channelIdList)
	if err != nil {
		log.ErrorWithCtx(ctx, "getGuildMultiChannelList Failed to batGetChannel err guildId:%d err:%+v", guildId, err)
		return []string{}
	}

	channelViewIdList := make([]string, 0)
	for _, id := range channelIdList {
		if roomInfo, ok := mapCid2RoomInfo[id]; ok {
			channelViewIdList = append(channelViewIdList, roomInfo.GetChannelViewId())
		}
	}

	log.DebugWithCtx(ctx, "getGuildMultiChannelList begin guildIdList:%v channelViewIdList:%v", guildIdList, channelViewIdList)
	return channelViewIdList
}

// 稳定项分析
func getStabilityAbilityBusinessDiag(ctx context.Context, guildId uint32, req *api.GetGuildAbilityBusinessDiagReq, mapTm2Data map[string]*datahouse.OpdataMultiOperate,
	tmList []time.Time, mapTm2Str map[time.Time]string, timeDim string) *api.GetGuildAbilityBusinessDiagResp {
	resp := &api.GetGuildAbilityBusinessDiagResp{
		Stability: &api.BusinessStability{
			TopChannelList: make([]*api.GuildChannelBusinessData, 0),
		},
	}

	for index, tm := range tmList {
		tStr := mapTm2Str[tm]
		stability := &api.Stability{
			Ts:                   uint32(tm.Unix()),
			ChannelCnt:           "0",
			AveFee:               "0",
			ContentChannelCnt:    "0",
			ContentAveFee:        "0",
			InteractChannelCnt:   "0",
			InteractAveFee:       "0",
			ParticularChannelCnt: "0",
			ParticularAveFee:     "0",
		}

		data, ok := mapTm2Data[tStr]
		var aveFee, contentAveFee, interactAveFee, particularAveFee float64
		lastAveFee, lastContentAveFee, lastInteractAveFee, lastParticularAveFee := "0", "0", "0", "0"
		if ok {
			stability.ChannelCnt = data.OperatingRooms
			aveFee, _, stability.AveFee, lastAveFee = ParseFloatByStr(ctx, data.AvgOperatingFlow)
			stability.ContentChannelCnt = data.ContentRooms
			contentAveFee, _, stability.ContentAveFee, lastContentAveFee = ParseFloatByStr(ctx, data.AvgContentFlow)
			stability.InteractChannelCnt = data.InteractiveRooms
			interactAveFee, _, stability.InteractAveFee, lastInteractAveFee = ParseFloatByStr(ctx, data.AvgInteractiveFlow)
			stability.ParticularChannelCnt = data.SpecialRooms
			particularAveFee, _, stability.ParticularAveFee, lastParticularAveFee = ParseFloatByStr(ctx, data.AvgSpecialFlow)

		}

		log.DebugWithCtx(ctx, "getStabilityAbilityBusinessDiag guildId:%d, req:%v tm:%v data:%v", guildId, req, tm, data)
		if index == len(tmList)-1 {
			if ok && data.MonthTop3Rooms != "" {
				resp.Stability.TopChannelList = getStabilityTopChannelList(ctx, guildId, data.MonthTop3Rooms, tm, timeDim)
			}

			topChannelIdList := make([]string, 0)
			for _, topChannel := range resp.Stability.TopChannelList {
				topChannelIdList = append(topChannelIdList, fmt.Sprintf("【ID%s】", topChannel.GetViewId()))
			}

			var html string

			cntItemHtml := getHtmlHead(fmt.Sprintf("经营房间数量 %s个", stability.ChannelCnt))
			incr, gapVal := CulStringDataRatio(ctx, guildId, stability.AveFee, lastAveFee)
			cntItemHtml += fmt.Sprintf("<div class=\"block_content\"> 单厅流水 <span class=\"bold\" style=\"color: #000000;\">%s</span>，%s </div>", getFeeStr(aveFee), getChangeStr(incr, gapVal, "无变化"))
			if len(topChannelIdList) != 0 {
				str := "本周流水TOP3房间分别是"
				if req.GetDimType() == uint32(api.GetGuildAbilityBusinessDiagReq_TimeDimType_Month) {
					str = "本月流水TOP3房间分别是"
				}
				cntItemHtml += fmt.Sprintf("<div class=\"block_content\"> %s <span class=\"bold\" style=\"color: #000000;\">%s</span></div>", str, strings.Join(topChannelIdList, "、"))
			}
			html += fmt.Sprintf("<div class=\"block\">%s</div>", cntItemHtml)

			cntItemHtml = getHtmlHead(fmt.Sprintf("内容房间数量 %s个", stability.ContentChannelCnt))
			incr, gapVal = CulStringDataRatio(ctx, guildId, stability.ContentAveFee, lastContentAveFee)
			cntItemHtml += fmt.Sprintf("<div class=\"block_content\"> 单厅流水 <span class=\"bold\" style=\"color: #000000;\">%s</span>，%s </div>", getFeeStr(contentAveFee), getChangeStr(incr, gapVal, "无变化"))
			html += fmt.Sprintf("<div class=\"block\">%s</div>", cntItemHtml)

			cntItemHtml = getHtmlHead(fmt.Sprintf("互动房间数量 %s个", stability.InteractChannelCnt))
			incr, gapVal = CulStringDataRatio(ctx, guildId, stability.InteractAveFee, lastInteractAveFee)
			cntItemHtml += fmt.Sprintf("<div class=\"block_content\"> 单厅流水 <span class=\"bold\" style=\"color: #000000;\">%s</span>，%s </div>", getFeeStr(interactAveFee), getChangeStr(incr, gapVal, "无变化"))
			html += fmt.Sprintf("<div class=\"block\">%s</div>", cntItemHtml)

			cntItemHtml = getHtmlHead(fmt.Sprintf("特殊品类房间数量 %s个", stability.ParticularChannelCnt))
			incr, gapVal = CulStringDataRatio(ctx, guildId, stability.ParticularAveFee, lastParticularAveFee)
			cntItemHtml += fmt.Sprintf("<div class=\"block_content\"> 单厅流水 <span class=\"bold\" style=\"color: #000000;\">%s</span>，%s </div>", getFeeStr(particularAveFee), getChangeStr(incr, gapVal, "无变化"))
			html += fmt.Sprintf("<div class=\"block\">%s</div>", cntItemHtml)

			resp.Stability.DiagList = append(resp.Stability.DiagList, html)
		}

		resp.Stability.StabilityList = append(resp.Stability.StabilityList, stability)
	}

	resp.Stability.GuildChannelIdList = getGuildMultiChannelList(ctx, guildId)

	log.DebugWithCtx(ctx, "getStabilityAbilityBusinessDiag guildId:%d, req:%v mapTm2Data:%v resp:%v", guildId, req, mapTm2Data, resp)
	return resp
}

// 抗风险项分析
func getRiskResistanceAbilityBusinessDiag(ctx context.Context, guildId uint32, req *api.GetGuildAbilityBusinessDiagReq, mapTm2Data map[string]*datahouse.OpdataMultiOperate,
	tmList []time.Time, mapTm2Str map[time.Time]string) *api.GetGuildAbilityBusinessDiagResp {
	resp := &api.GetGuildAbilityBusinessDiagResp{
		RiskResistance: &api.BusinessRiskResistance{
			LuckyGiftFeeRatio: &api.LastRatio{Ratio: 0, RatioValue: "-"},
			PkgGiftFeeRatio:   &api.LastRatio{Ratio: 0, RatioValue: "-"},
		},
	}

	for index, tm := range tmList {
		tStr := mapTm2Str[tm]
		riskResistance := &api.RiskResistance{
			Ts:             uint32(tm.Unix()),
			LuckyGiftFee:   "0",
			LuckyGiftRatio: "0",
			PkgGiftFee:     "0",
			PkgGiftRatio:   "0",
			OtherRatio:     "1",
			TbeanGiftRatio: "0",
		}

		lastRiskResistance := &api.RiskResistance{
			LuckyGiftFee: "0",
			PkgGiftFee:   "0",
		}

		data, ok := mapTm2Data[tStr]
		var luckyGiftFee, pkgGiftFee, luckyGiftRatio, pkgGiftRatio float64
		if ok {
			luckyGiftFee, _, riskResistance.LuckyGiftFee, lastRiskResistance.LuckyGiftFee = ParseFloatByStr(ctx, data.LuckyGiftFlow)
			riskResistance.LuckyGiftRatio = data.LuckyGiftRatio
			pkgGiftFee, _, riskResistance.PkgGiftFee, lastRiskResistance.PkgGiftFee = ParseFloatByStr(ctx, data.BackpackGiftFlow)
			riskResistance.PkgGiftRatio = data.BackpackGiftRatio
			riskResistance.TbeanGiftRatio = data.TbeanGiftRatio
			riskResistance.OtherRatio = data.OtherGiftRatio

			luckyGiftRatio, _ = strconv.ParseFloat(riskResistance.LuckyGiftRatio, 64)
			pkgGiftRatio, _ = strconv.ParseFloat(riskResistance.PkgGiftRatio, 64)
		}

		if index == len(tmList)-1 {
			CurLastRatio(guildId, riskResistance.LuckyGiftFee, lastRiskResistance.LuckyGiftFee, resp.RiskResistance.LuckyGiftFeeRatio)
			CurLastRatio(guildId, riskResistance.PkgGiftFee, lastRiskResistance.PkgGiftFee, resp.RiskResistance.PkgGiftFeeRatio)

			giftDiag := "较不健康"
			if pkgGiftRatio < 0.5 && luckyGiftRatio < 0.5 {
				giftDiag = "较健康"
			}
			html := fmt.Sprintf("<div class=\"diagnosis_content\">公会流水构成 <span class=\"bold\" style=\"color: #17CBC2;\">%s</span></div>", giftDiag)

			cntItemHtml := getHtmlHead(fmt.Sprintf("幸运礼物流水 %s", getFeeStr(luckyGiftFee)))
			incr, gapVal := CulStringDataRatio(ctx, guildId, riskResistance.LuckyGiftFee, lastRiskResistance.LuckyGiftFee)
			cntItemHtml += fmt.Sprintf("<div class=\"block_content\"> 占比 <span class=\"bold\" style=\"color: #000000;\">%s</span>，%s </div>", fmt.Sprintf("%0.2f%%", luckyGiftRatio*100), getChangeStr(incr, gapVal, "无变化"))
			html += fmt.Sprintf("<div class=\"block\">%s</div>", cntItemHtml)

			cntItemHtml = getHtmlHead(fmt.Sprintf("背包礼物流水 %s", getFeeStr(pkgGiftFee)))
			incr, gapVal = CulStringDataRatio(ctx, guildId, riskResistance.PkgGiftFee, lastRiskResistance.PkgGiftFee)
			cntItemHtml += fmt.Sprintf("<div class=\"block_content\"> 占比 <span class=\"bold\" style=\"color: #000000;\">%s</span>，%s </div>", fmt.Sprintf("%0.2f%%", pkgGiftRatio*100), getChangeStr(incr, gapVal, "无变化"))
			html += fmt.Sprintf("<div class=\"block\">%s</div>", cntItemHtml)

			resp.RiskResistance.DiagList = append(resp.RiskResistance.DiagList, html)
		}

		resp.RiskResistance.RiskList = append(resp.RiskResistance.RiskList, riskResistance)
	}

	log.DebugWithCtx(ctx, "getRiskResistanceAbilityBusinessDiag guildId:%d, req:%v mapTm2Data:%v resp:%v", guildId, req, mapTm2Data, resp)
	return resp
}

// GetGuildAbilityBusinessDiag 获取公会能力项经营诊断
func GetGuildAbilityBusinessDiag(authInfo *web.AuthInfo, w http.ResponseWriter, r *http.Request) {
	ctx, cancel := context.WithTimeout(r.Context(), time.Second*10)
	defer cancel()

	req := &api.GetGuildAbilityBusinessDiagReq{}
	err := json.Unmarshal(authInfo.Body, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetGuildAbilityBusinessDiag Failed to parse request body [%s], err %+v", string(authInfo.Body), err)
		web.ServeBadReq(w)
		return
	}
	guildId := req.GetGuildId()
	uid := authInfo.UserID

	log.DebugWithCtx(ctx, "GetGuildAbilityBusinessDiag begin uid:%d, %+v", uid, req)

	mapAbility2FuncPer := map[uint32]string{
		1: api.MultiBusinessDiagFuncType_MultiBusinessDiagFuncRevenue.String(),
		2: api.MultiBusinessDiagFuncType_MultiBusinessDiagFuncRecruit.String(),
		3: api.MultiBusinessDiagFuncType_MultiBusinessDiagFuncHatch.String(),
		4: api.MultiBusinessDiagFuncType_MultiBusinessDiagFuncSafety.String(),
		5: api.MultiBusinessDiagFuncType_MultiBusinessDiagFuncStability.String(),
		6: api.MultiBusinessDiagFuncType_MultiBusinessDiagFuncRiskResistance.String(),
	}

	_, respCode, respMsg := CheckUserPermission(CheckPerInReq{
		ctx:         ctx,
		uid:         uid,
		guildId:     guildId,
		menuPerType: api.GuildMultiPlayDataType_MultiPlayBusinessDiag.String(),
		funcPerType: mapAbility2FuncPer[req.GetAbilityItemType()],
	})
	if respCode != 0 {
		_ = web.ServeAPICodeJson(w, respCode, respMsg, nil)
		return
	}

	tm := time.Unix(int64(req.GetBeginTs()), 0)
	endTm := tm.AddDate(0, 0, -GetWeekDayOffset(tm)+1)
	startTm := endTm.AddDate(0, 0, -6*7)
	timeDim := "week"

	if req.GetDimType() == uint32(api.GetGuildAbilityBusinessDiagReq_TimeDimType_Month) {
		timeDim = "month"
		endTm = time.Date(tm.Year(), tm.Month(), 1, 0, 0, 0, 0, time.Local)
		startTm = endTm.AddDate(0, -6, 0)
	}

	OpdataMultiOperateResp, err := datahouse.QueryOpdataMultiOperate(ctx, &datahouse.OpdataMultiOperateReq{
		StartDate:   startTm,
		EndDate:     endTm,
		TimeDim:     timeDim,
		GuildIdList: []uint32{req.GetGuildId()},
		Page:        1,
		PageSize:    10,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetGuildAbilityBusinessDiag Failed to QueryOpdataMultiOperate err uid:%d req:%v err:%+v", uid, req, err)
		web.ServeGameErrReq(w, -500, "系统错误")
		return
	}

	mapTm2Data := make(map[string]*datahouse.OpdataMultiOperate)
	for _, data := range OpdataMultiOperateResp.Data {
		mapTm2Data[data.DataDate] = data
	}

	tmList := make([]time.Time, 0)
	mapTm2Str := make(map[time.Time]string)
	switch api.GetGuildAbilityBusinessDiagReq_TimeDimType(req.GetDimType()) {
	case api.GetGuildAbilityBusinessDiagReq_TimeDimType_Week:
		for t := startTm; t.Unix() <= endTm.Unix(); t = t.AddDate(0, 0, 7) {
			tmList = append(tmList, t)
			mapTm2Str[t] = t.Format(time.DateOnly)
		}

	case api.GetGuildAbilityBusinessDiagReq_TimeDimType_Month:
		for t := startTm; t.Unix() <= endTm.Unix(); t = t.AddDate(0, 1, 0) {
			tmList = append(tmList, t)
			mapTm2Str[t] = t.Format(time.DateOnly)
		}
	}

	log.DebugWithCtx(ctx, "GetGuildAbilityBusinessDiag uid:%d, req:%+v startTm:%v endTm:%v tmList:%v", uid, req, startTm, endTm, tmList)

	resp := &api.GetGuildAbilityBusinessDiagResp{}

	switch api.AbilityItemType(req.GetAbilityItemType()) {
	case api.AbilityItemType_AbilityItemType_Revenue:
		resp = getRevenueAbilityBusinessDiag(ctx, guildId, req, mapTm2Data, tmList, mapTm2Str)
	case api.AbilityItemType_AbilityItemType_Recruit:
		resp = getRecruitAbilityBusinessDiag(ctx, guildId, req, mapTm2Data, tmList, mapTm2Str)
	case api.AbilityItemType_AbilityItemType_Hatch:
		resp = getHatchAbilityBusinessDiag(ctx, guildId, req, mapTm2Data, tmList, mapTm2Str)
	case api.AbilityItemType_AbilityItemType_Safety:
		resp = getSafetyAbilityBusinessDiag(ctx, guildId, req, mapTm2Data, tmList, mapTm2Str)
	case api.AbilityItemType_AbilityItemType_Stability:
		resp = getStabilityAbilityBusinessDiag(ctx, guildId, req, mapTm2Data, tmList, mapTm2Str, timeDim)
	case api.AbilityItemType_AbilityItemType_Risk_Resistance:
		resp = getRiskResistanceAbilityBusinessDiag(ctx, guildId, req, mapTm2Data, tmList, mapTm2Str)

	default:
		log.ErrorWithCtx(ctx, "GetGuildAbilityBusinessDiag invalid AbilityItemType uid:%d req:%v err:%+v", uid, req, err)
		web.ServeGameErrReq(w, -500, "参数错误")
		return
	}

	log.DebugWithCtx(ctx, "GetGuildAbilityBusinessDiag end uid:%d, req:%v resp:%+v", uid, req, resp)
	_ = web.ServeAPIJson(w, resp)
}
