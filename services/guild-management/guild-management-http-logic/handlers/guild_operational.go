package hanlders

import (
	"context"
	"encoding/json"
	"fmt"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/web"
	channellivestats "golang.52tt.com/protocol/services/channel-live-stats"
	guildCooperation "golang.52tt.com/protocol/services/guild-cooperation"
	signAnchorStatsPb "golang.52tt.com/protocol/services/sign-anchor-stats"
	"golang.52tt.com/services/guild-management/guild-management-http-logic/models"
	api "golang.52tt.com/services/guild-management/guild-management-http-logic/models/gen-go"
	"math"
	"net/http"
	"sort"
	"time"
)

type ScoreData struct {
	TotalScore float64
	Rank       int
	Type       uint32
	Name       string
	Cnt        uint32
}

const (
	MatureAnchorScore    = uint32(1)
	ProAnchorScore       = uint32(2)
	PotActiveAnchorScore = uint32(3)
	NewActiveAnchorScore = uint32(4)
	HighLevel            = 30
	MaxRevenueLevel      = 9
	MinRevenueLevel      = 1
)

func getAllLiveCoopGuildList(ctx context.Context) ([]uint32, error) {
	subCtx, cancel := context.WithTimeout(ctx, 5*time.Second)
	defer cancel()

	guildList := make([]uint32, 0)
	mapGuild2Is := make(map[uint32]bool)

	coopTypeList := []guildCooperation.CooperationType{
		guildCooperation.CooperationType_CTypeYuyin,
	}

	for _, coopType := range coopTypeList {
		offset := uint32(0)
		limit := uint32(100)
		var totalCnt int

		for {
			resp, err := models.GetModelServer().GuildCoopCli.GetCooperationGuildIdsV2(subCtx,
				coopType, []uint32{}, offset, limit)
			if err != nil {
				log.ErrorWithCtx(ctx, "getAllLiveCoopGuildList GetCooperationGuildIdsV2 err:%s", err)
				return nil, err
			}

			for _, guildId := range resp.GetGuildList() {
				if !mapGuild2Is[guildId] {
					guildList = append(guildList, guildId)
					mapGuild2Is[guildId] = true
				}
			}

			totalCnt += len(resp.GetGuildList())

			if len(resp.GetGuildList()) < int(limit) {
				break
			}

			offset += limit
		}

		log.InfoWithCtx(ctx, "getAllLiveCoopGuildList coopType:%d totalCnt:%d", coopType, totalCnt)
	}

	log.InfoWithCtx(ctx, "getAllLiveCoopGuildList end len:%d guildList:%v", len(guildList), guildList)
	return guildList, nil
}

func GetGuildOperationalCapabilities(authInfo *web.AuthInfo, w http.ResponseWriter, r *http.Request) {
	ctx, cancel := context.WithTimeout(r.Context(), time.Second*10)
	defer cancel()

	req := &api.GetGuildOperationalCapabilitiesRequest{}
	resp := &api.GetGuildOperationalCapabilitiesResponse{}
	err := json.Unmarshal(authInfo.Body, req)
	if err != nil {
		log.ErrorWithCtx(ctx, ""+
			" Failed to parse request body [%s], err %+v", string(authInfo.Body), err)
		web.ServeBadReq(w)
		return
	}

	log.DebugWithCtx(ctx, "GetGuildOperationalCapabilities begin %s %+v", string(authInfo.Body), req)

	uid := authInfo.UserID
	guildId := req.GetGuildId()

	_, respCode, respMsg := CheckUserPermission(CheckPerInReq{
		ctx:         ctx,
		uid:         uid,
		guildId:     guildId,
		menuPerType: api.GuildLiveDataMenuType_GuildBusinessDiagnosis.String(),
		funcPerType: api.BusinessDiagnosisFuncType_BusinessDiagnosisAbility.String(),
	})
	if respCode != 0 {
		_ = web.ServeAPICodeJson(w, respCode, respMsg, nil)
		return
	}

	/*
		guildCoopList, err := getAllLiveCoopGuildList(ctx)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetGuildOperationalCapabilities getAllLiveCoopGuildList req:%v err:%s", req, err)
			web.ServeBadReq(w)
			return
		}

		mapId2IsCoop := make(map[uint32]bool)
		for _, id := range guildCoopList {
			mapId2IsCoop[id] = true
		}

		_, mapId2BindId, _, err := getBindGuildList(ctx)
		if err != nil {
			log.ErrorWithCtx(ctx, "PullGuildOperationalCapabilities getBindGuildList req:%v err:%s", req, err)
			return
		}
		if bindId, ok := mapId2BindId[guildId]; ok && mapId2IsCoop[bindId] && mapId2IsCoop[guildId] {
			guildId = bindId
		}
	*/

	log.DebugWithCtx(ctx, "GetGuildOperationalCapabilities statResp:%v", guildId)

	statResp, err := models.GetModelServer().ChannelLiveStatsClient.GetGuildOperationalCapabilities(ctx, &channellivestats.GetGuildOperationalCapabilitiesReq{
		GuildId: guildId,
		EndDate: req.GetToTime(),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetGuildOperationalCapabilities GetGuildOperationalCapabilities failed, err:%v", err)
		web.ServeBadReq(w)
		return
	}
	log.DebugWithCtx(ctx, "GetGuildOperationalCapabilities statResp:%v", statResp)

	if len(statResp.GetDataList()) == 0 {
		log.ErrorWithCtx(ctx, "GetGuildOperationalCapabilities GetGuildOperationalCapabilities failed, err:%v", err)
		_ = web.ServeAPIJson(w, resp)
		return
	}
	guildData := statResp.GetDataList()[0]
	statLevelResp, err := models.GetModelServer().ChannelLiveStatsClient.GetGuildOperationalCapabilities(ctx, &channellivestats.GetGuildOperationalCapabilitiesReq{
		RevenueLevel: guildData.GetRevenueLevel(),
		EndDate:      req.GetToTime(),
	})
	if len(statLevelResp.GetDataList()) == 0 {
		log.ErrorWithCtx(ctx, "GetGuildOperationalCapabilities GetGuildOperationalCapabilities failed, err:%v", err)
		_ = web.ServeAPIJson(w, resp)
		return
	}
	log.DebugWithCtx(ctx, "GetGuildOperationalCapabilities statResp:%v", statLevelResp)
	resp = InitGuildOperationalCapabilities(guildData, statLevelResp.GetDataList())
	HighResp := GetHighLevelData(ctx, guildData, req.GetToTime())

	dataLen := uint32(len(statLevelResp.GetDataList()))
	ComputeCapabilitiesData(dataLen, resp.NewSignAnchor, HighResp.NewSignAnchor)
	ComputeCapabilitiesData(dataLen, resp.EnabledLiveUser, HighResp.EnabledLiveUser)
	ComputeCapabilitiesData(dataLen, resp.EnabledLiveNewUser, HighResp.EnabledLiveNewUser)
	ComputeCapabilitiesData(dataLen, resp.ProAnchor, HighResp.ProAnchor)
	ComputeCapabilitiesData(dataLen, resp.MatureAnchor, HighResp.MatureAnchor)
	ComputeCapabilitiesData(dataLen, resp.PotActiveAnchor, HighResp.PotActiveAnchor)
	ComputeCapabilitiesData(dataLen, resp.NewSignProAnchor, HighResp.NewSignProAnchor)
	ComputeCapabilitiesData(dataLen, resp.NewActiveAnchor, HighResp.NewActiveAnchor)
	switch guildData.GetRevenueLevel() {
	case 1:
		resp.BelongsLevelDesc = "您的公会当前位于 [0, 1千万豆) 流水层级"
	case 2:
		resp.BelongsLevelDesc = "您的公会当前位于 [1千万豆, 3千万豆) 流水层级"
	case 3:
		resp.BelongsLevelDesc = "您的公会当前位于 [3千万豆, 5千万豆) 流水层级"
	case 4:
		resp.BelongsLevelDesc = "您的公会当前位于 [5千万豆, 1亿豆) 流水层级"
	case 5:
		resp.BelongsLevelDesc = "您的公会当前位于 [1亿豆, 2亿豆) 流水层级"
	case 6:
		resp.BelongsLevelDesc = "您的公会当前位于 [2亿豆, 3亿豆) 流水层级"
	case 7:
		resp.BelongsLevelDesc = "您的公会当前位于 [3亿豆, 5亿豆) 流水层级"
	case 8:
		resp.BelongsLevelDesc = "您的公会当前位于 [5亿豆, 10亿豆) 流水层级"
	case 9:
		resp.BelongsLevelDesc = "您的公会当前位于 [10亿豆, ∞) 流水层级"

	}

	log.DebugWithCtx(ctx, "GetGuildOperationalCapabilities end uid:%v guildId:%v resp:%v", uid, guildId, resp)
	_ = web.ServeAPIJson(w, resp)
}

func getBindGuildList(ctx context.Context) (map[uint32][]uint32, map[uint32]uint32, map[uint32]bool, error) {
	subCtx, cancel := context.WithTimeout(ctx, 10*time.Second)
	defer cancel()

	signAnchorResp, err := models.GetModelServer().SignAnchorStatsCli.GetBindGuildInfoList(subCtx, &signAnchorStatsPb.GetBindGuildInfoListReq{})
	if err != nil {
		log.ErrorWithCtx(ctx, "getBindGuildList GetBindGuildInfoList failed err:%v", err)
		return nil, nil, nil, err
	}

	mapId2List := make(map[uint32][]uint32)
	mapId2BindId := make(map[uint32]uint32)
	mapGuild2IsBind := make(map[uint32]bool)
	for _, bindGuild := range signAnchorResp.GetInfoList() {
		if bindGuild.GetGuildId() == bindGuild.GetBindGuildId() {
			continue
		}

		if _, ok := mapId2List[bindGuild.GetBindGuildId()]; !ok {
			mapId2List[bindGuild.GetBindGuildId()] = make([]uint32, 0)
		}

		mapId2List[bindGuild.GetBindGuildId()] = append(mapId2List[bindGuild.GetBindGuildId()], bindGuild.GetGuildId())
		mapId2BindId[bindGuild.GetGuildId()] = bindGuild.GetBindGuildId()
		mapGuild2IsBind[bindGuild.GetGuildId()] = true
		mapGuild2IsBind[bindGuild.GetBindGuildId()] = true
	}

	log.InfoWithCtx(ctx, "getBindGuildList end len:%d mapId2List:%v mapId2BindId:%v mapGuild2IsBind:%v", len(mapId2List), mapId2List, mapId2BindId, mapGuild2IsBind)
	return mapId2List, mapId2BindId, mapGuild2IsBind, nil
}

func GetHighLevelData(ctx context.Context, guildData *channellivestats.GuildOperationalData, endDate int64) *api.GetGuildOperationalCapabilitiesResponse {
	HighResp := &api.GetGuildOperationalCapabilitiesResponse{}
	log.DebugWithCtx(ctx, "GetHighLevelData guildId:%v cLevel:%v", guildData.GetGuildId(), guildData.GetRevenueLevel())
	if guildData.GetRevenueLevel() < MinRevenueLevel || guildData.GetRevenueLevel() > MaxRevenueLevel {
		return HighResp
	}

	for level := guildData.GetRevenueLevel(); level < MaxRevenueLevel; level++ {
		levelResp, err := models.GetModelServer().ChannelLiveStatsClient.GetGuildOperationalCapabilities(ctx, &channellivestats.GetGuildOperationalCapabilitiesReq{
			RevenueLevel: level + 1,
			EndDate:      endDate,
		})
		if err != nil {
			log.ErrorWithCtx(ctx, "GetGuildOperationalCapabilities GetGuildOperationalCapabilities failed, err:%v", err)
			return HighResp
		}
		if len(levelResp.GetDataList()) > 0 {
			HighResp = InitGuildOperationalCapabilities(guildData, levelResp.GetDataList())
			log.DebugWithCtx(ctx, "GetHighLevelData guildId:%v cLevel:%v level:%v levelResp:%v", guildData.GetGuildId(), guildData.GetRevenueLevel(), level, HighResp)
			return HighResp
		}
	}
	log.DebugWithCtx(ctx, "GetHighLevelData guildId:%v HighResp:%v", guildData.GetGuildId(), HighResp)
	return HighResp
}

func ComputeCapabilitiesData(dataLen uint32, data, HighData *api.GuildBenchmarkingData) {
	log.DebugWithCtx(context.Background(), "ComputeCapabilitiesData dataLen:%d data:%v HighData:%v", dataLen, data, HighData)
	if HighData != nil {
		data.HasHighLevel = true
		data.ExceedCnt = int32(data.GetSelfData()) - int32(HighData.GetAvgData())
	}
	log.DebugWithCtx(context.Background(), "ComputeCapabilitiesData dataLen:%d data:%v", dataLen, data)

	if dataLen <= 3 {
		if data.GetRank() == 1 {
			data.Level = uint32(api.RankLevel_RankLevel_High)
		}
		if data.GetRank() == 2 {
			data.Level = uint32(api.RankLevel_RankLevel_Middle)
		}
		if data.GetRank() == 3 {
			data.Level = uint32(api.RankLevel_RankLevel_Low)
		}
	} else if dataLen > 3 && dataLen <= 5 {
		if data.GetRank() <= 2 {
			data.Level = uint32(api.RankLevel_RankLevel_High)
		} else if data.GetRank() == 3 {
			data.Level = uint32(api.RankLevel_RankLevel_Middle)
		} else {
			data.Level = uint32(api.RankLevel_RankLevel_Low)
		}
	} else if dataLen > 5 && dataLen <= 10 {
		if data.GetRank() <= 2 {
			data.Level = uint32(api.RankLevel_RankLevel_High)
		} else if data.GetRank() <= 5 {
			data.Level = uint32(api.RankLevel_RankLevel_Middle)
		} else {
			data.Level = uint32(api.RankLevel_RankLevel_Low)
		}
	} else if dataLen > 11 && dataLen <= 15 {
		if data.GetRank() <= 3 {
			data.Level = uint32(api.RankLevel_RankLevel_High)
		} else if data.GetRank() <= 7 {
			data.Level = uint32(api.RankLevel_RankLevel_Middle)
		} else {
			data.Level = uint32(api.RankLevel_RankLevel_Low)
		}
	} else if dataLen > 15 && dataLen <= 20 {
		if data.GetRank() <= 4 {
			data.Level = uint32(api.RankLevel_RankLevel_High)
		} else if data.GetRank() <= 8 {
			data.Level = uint32(api.RankLevel_RankLevel_Middle)
		} else {
			data.Level = uint32(api.RankLevel_RankLevel_Low)
		}
	} else {
		if data.GetRank() < 3 {
			data.Level = uint32(api.RankLevel_RankLevel_High)
		} else if data.GetRank() <= 6 {
			data.Level = uint32(api.RankLevel_RankLevel_Middle)
		} else {
			data.Level = uint32(api.RankLevel_RankLevel_Low)
		}
	}

}

func InitGuildOperationalCapabilities(guildData *channellivestats.GuildOperationalData, dataList []*channellivestats.GuildOperationalData) *api.GetGuildOperationalCapabilitiesResponse {
	resp := &api.GetGuildOperationalCapabilitiesResponse{
		NewSignAnchor: &api.GuildBenchmarkingData{
			TopData:  guildData.GetNewSignAnchorCnt(),
			SelfData: guildData.GetNewSignAnchorCnt(),
			//AvgData:  guildData.GetNewSignAnchorCnt(),
			Rank: 1,
		},
		EnabledLiveNewUser: &api.GuildBenchmarkingData{
			TopData:  guildData.GetEnabledLiveNewUserCount(),
			SelfData: guildData.GetEnabledLiveNewUserCount(),
			//AvgData:  guildData.GetEnabledLiveNewUserCount(),
			Rank: 1,
		},
		EnabledLiveUser: &api.GuildBenchmarkingData{
			TopData:  guildData.GetEnabledLiveUserCount(),
			SelfData: guildData.GetEnabledLiveUserCount(),
			//AvgData:  guildData.GetEnabledLiveUserCount(),
			Rank: 1,
		},
		NewActiveAnchor: &api.GuildBenchmarkingData{
			TopData:  guildData.GetNewActiveAnchorCnt(),
			SelfData: guildData.GetNewActiveAnchorCnt(),
			//AvgData:  guildData.GetNewActiveAnchorCnt(),
			Rank: 1,
		},
		ProAnchor: &api.GuildBenchmarkingData{
			TopData:  guildData.GetProAnchorCnt(),
			SelfData: guildData.GetProAnchorCnt(),
			//AvgData:  guildData.GetProAnchorCnt(),
			Rank: 1,
		},
		MatureAnchor: &api.GuildBenchmarkingData{
			TopData:  guildData.GetMatureAnchorCnt(),
			SelfData: guildData.GetMatureAnchorCnt(),
			//AvgData:  guildData.GetMatureAnchorCnt(),
			Rank: 1,
		},
		PotActiveAnchor: &api.GuildBenchmarkingData{
			TopData:  guildData.GetPotActiveAnchorCnt(),
			SelfData: guildData.GetPotActiveAnchorCnt(),
			//AvgData:  guildData.GetPotActiveAnchorCnt(),
			Rank: 1,
		},
		NewSignProAnchor: &api.GuildBenchmarkingData{
			TopData:  guildData.GetNewSignProAnchor(),
			SelfData: guildData.GetNewSignProAnchor(),
			//AvgData:  guildData.GetNewSignProAnchor(),
			Rank: 1,
		},
	}
	var (
		newSignAnchorLen, enabledLiveUserLen, enabledLiveNewUserLen, proAnchorLen    uint32
		matureAnchorLen, potActiveAnchorLen, newSignProAnchorLen, newActiveAnchorLen uint32
	)

	for _, data := range dataList {

		newSignAnchorLen = UpdateGuildBenchmarkingData(data.GetNewSignAnchorCnt(), guildData.GetNewSignAnchorCnt(), resp.NewSignAnchor, data.GetRevenue(), guildData.GetRevenue(), data.GetRevenueTime(), guildData.GetRevenueTime(), newSignAnchorLen)
		enabledLiveUserLen = UpdateGuildBenchmarkingData(data.GetEnabledLiveUserCount(), guildData.GetEnabledLiveUserCount(), resp.EnabledLiveUser, data.GetRevenue(), guildData.GetRevenue(), data.GetRevenueTime(), data.GetRevenueTime(), enabledLiveUserLen)
		enabledLiveNewUserLen = UpdateGuildBenchmarkingData(data.GetEnabledLiveNewUserCount(), guildData.GetEnabledLiveNewUserCount(), resp.EnabledLiveNewUser, data.GetRevenue(), guildData.GetRevenue(), data.GetRevenueTime(), guildData.GetRevenueTime(), enabledLiveNewUserLen)
		proAnchorLen = UpdateGuildBenchmarkingData(data.GetProAnchorCnt(), guildData.GetProAnchorCnt(), resp.ProAnchor, data.GetRevenue(), guildData.GetRevenue(), data.GetRevenueTime(), guildData.GetRevenueTime(), proAnchorLen)
		matureAnchorLen = UpdateGuildBenchmarkingData(data.GetMatureAnchorCnt(), guildData.GetMatureAnchorCnt(), resp.MatureAnchor, data.GetRevenue(), guildData.GetRevenue(), data.GetRevenueTime(), guildData.GetRevenueTime(), matureAnchorLen)
		potActiveAnchorLen = UpdateGuildBenchmarkingData(data.GetPotActiveAnchorCnt(), guildData.GetPotActiveAnchorCnt(), resp.PotActiveAnchor, data.GetRevenue(), guildData.GetRevenue(), data.GetRevenueTime(), guildData.GetRevenueTime(), potActiveAnchorLen)
		newSignProAnchorLen = UpdateGuildBenchmarkingData(data.GetNewSignProAnchor(), guildData.GetNewSignProAnchor(), resp.NewSignProAnchor, data.GetRevenue(), guildData.GetRevenue(), data.GetRevenueTime(), guildData.GetRevenueTime(), newSignProAnchorLen)
		newActiveAnchorLen = UpdateGuildBenchmarkingData(data.GetNewActiveAnchorCnt(), guildData.GetNewActiveAnchorCnt(), resp.NewActiveAnchor, data.GetRevenue(), guildData.GetRevenue(), data.GetRevenueTime(), guildData.GetRevenueTime(), newActiveAnchorLen)
	}
	log.DebugWithCtx(context.Background(), "InitGuildOperationalCapabilities newSignProAnchorLen:%d xx:%v ", newSignProAnchorLen, resp.NewSignProAnchor.GetAvgData())
	statLen := uint32(len(dataList))
	log.DebugWithCtx(context.Background(), "InitGuildOperationalCapabilities statLen:%d", statLen)
	resp.NewSignAnchor.AvgData = uint32(math.Round(float64(resp.NewSignAnchor.GetAvgData()) / float64(newSignAnchorLen)))
	resp.EnabledLiveUser.AvgData = uint32(math.Round(float64(resp.EnabledLiveUser.GetAvgData()) / float64(enabledLiveUserLen)))
	resp.EnabledLiveNewUser.AvgData = uint32(math.Round(float64(resp.EnabledLiveNewUser.GetAvgData()) / float64(enabledLiveNewUserLen)))
	resp.ProAnchor.AvgData = uint32(math.Round(float64(resp.ProAnchor.GetAvgData()) / float64(proAnchorLen)))
	resp.MatureAnchor.AvgData = uint32(math.Round(float64(resp.MatureAnchor.GetAvgData()) / float64(matureAnchorLen)))
	resp.PotActiveAnchor.AvgData = uint32(math.Round(float64(resp.PotActiveAnchor.GetAvgData()) / float64(potActiveAnchorLen)))
	resp.NewSignProAnchor.AvgData = uint32(math.Round(float64(resp.NewSignProAnchor.GetAvgData()) / float64(newSignProAnchorLen)))
	resp.NewActiveAnchor.AvgData = uint32(math.Round(float64(resp.NewActiveAnchor.GetAvgData()) / float64(newActiveAnchorLen)))
	return resp
}

func UpdateGuildBenchmarkingData(cnt, selfCnt uint32, data *api.GuildBenchmarkingData, revenue, selfRevenue uint64, revenueTime, selfRevenueTime, dataLen uint32) uint32 {

	if cnt > selfCnt {
		data.Rank += 1
	} else if cnt == selfCnt {
		if revenue > selfRevenue {
			data.Rank += 1
		} else if revenue == selfRevenue {
			if revenueTime < selfRevenueTime {
				data.Rank += 1
			}
		}
	}
	if cnt > 0 {
		dataLen += 1
	}

	if cnt > data.TopData {
		data.TopData = cnt
	}
	data.AvgData += cnt
	return dataLen

}
func GetCurriculumMessageList(authInfo *web.AuthInfo, w http.ResponseWriter, r *http.Request) {
	ctx, cancel := context.WithTimeout(r.Context(), time.Second*10)
	defer cancel()

	req := &api.GetCurriculumMessageListRequest{}
	resp := &api.GetCurriculumMessageListResponse{}
	err := json.Unmarshal(authInfo.Body, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetCurriculumMessageList Failed to parse request body [%s], err %+v", string(authInfo.Body), err)
		web.ServeBadReq(w)
		return
	}

	uid := authInfo.UserID
	guildId := req.GetGuildId()
	_, respCode, respMsg := CheckUserPermission(CheckPerInReq{
		ctx:         ctx,
		uid:         uid,
		guildId:     guildId,
		menuPerType: api.GuildLiveDataMenuType_GuildBusinessDiagnosis.String(),
		funcPerType: api.BusinessDiagnosisFuncType_BusinessDiagnosisAbility.String(),
	})
	if respCode != 0 {
		_ = web.ServeAPICodeJson(w, respCode, respMsg, nil)
		return
	}

	cResp, err := models.GetModelServer().ChannelLiveStatsClient.GetCurriculumMessageList(ctx, &channellivestats.GetCurriculumMessageListReq{
		Type: req.GetType(),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetCurriculumMessageList GetCurriculumMessageList failed, err:%v", err)
		web.ServeBadReq(w)
		return
	}
	for _, data := range cResp.GetCurriculumList() {
		resp.CurriculumList = append(resp.CurriculumList, &api.CurriculumMessage{
			CurriculumTitle: data.GetCurriculumTitle(),
			CurriculumDesc:  data.GetCurriculumDesc(),
			FileType:        data.GetFileType(),
			FileUrl:         data.GetFileUrl(),
		})
	}

	log.DebugWithCtx(ctx, "GetCurriculumMessageList end uid:%d req:%v resp:%v", uid, req, resp)
	_ = web.ServeAPIJson(w, resp)
}

func GetGuildOperationalSummary(authInfo *web.AuthInfo, w http.ResponseWriter, r *http.Request) {
	ctx, cancel := context.WithTimeout(r.Context(), time.Second*10)
	defer cancel()
	uid := authInfo.UserID

	req := &api.GetGuildOperationalSummaryRequest{}
	err := json.Unmarshal(authInfo.Body, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetGuildOperationalSummary Failed to parse request body [%s], err %+v", string(authInfo.Body), err)
		web.ServeBadReq(w)
		return
	}

	guildId := req.GetGuildId()

	_, respCode, respMsg := CheckUserPermission(CheckPerInReq{
		ctx:         ctx,
		uid:         uid,
		guildId:     guildId,
		menuPerType: api.GuildLiveDataMenuType_GuildBusinessDiagnosis.String(),
		funcPerType: api.BusinessDiagnosisFuncType_BusinessDiagnosisAbility.String(),
	})
	if respCode != 0 {
		_ = web.ServeAPICodeJson(w, respCode, respMsg, nil)
		return
	}

	/*
		guildCoopList, err := getAllLiveCoopGuildList(ctx)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetGuildOperationalSummary getAllLiveCoopGuildList req:%v err:%s", req, err)
			web.ServeBadReq(w)
			return
		}

		mapId2IsCoop := make(map[uint32]bool)
		for _, id := range guildCoopList {
			mapId2IsCoop[id] = true
		}

		_, mapId2BindId, _, err := getBindGuildList(ctx)
		if err != nil {
			log.ErrorWithCtx(ctx, "PullGuildOperationalCapabilities getBindGuildList err:%s", err)
			return
		}
		if bindId, ok := mapId2BindId[guildId]; ok && mapId2IsCoop[bindId] && mapId2IsCoop[guildId] {
			guildId = bindId
		}
	*/

	guildResp, err := models.GetModelServer().ChannelLiveStatsClient.GetGuildOperationalCapabilities(ctx, &channellivestats.GetGuildOperationalCapabilitiesReq{
		EndDate: req.GetToTime(),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetGuildOperationalCapabilities GetGuildOperationalCapabilities failed, err:%v", err)
		web.ServeBadReq(w)
		return
	}

	dataList := make([]*channellivestats.GuildOperationalData, 0)
	for _, data := range guildResp.GetDataList() {
		dataList = append(dataList, data)
	}

	var (
		totalScore, matureAnchorScore, proAnchorScore, potActiveAnchorScore, newActiveAnchorScore, revenueScore float64
		totalRank, matureAnchorRank, proAnchorRank, potActiveAnchorRank, newActiveAnchorRank                    int
	)
	log.DebugWithCtx(ctx, "GetGuildOperationalSummary dataList %+v", dataList)
	sort.SliceStable(dataList, func(i, j int) bool {
		return dataList[i].GetNewActiveAnchorCnt() > dataList[j].GetNewActiveAnchorCnt()
	})
	log.DebugWithCtx(ctx, "GetGuildOperationalSummary dataList %+v", dataList)
	highLevelDataList := make([]*ScoreData, 0)
	levelDataList := make([]*ScoreData, 0)
	for i, data := range dataList {
		if data.GetGuildId() == guildId {
			revenueScore = math.Round(float64(data.GetRevenueScore())*0.2/10) / 100
			newActiveAnchorRank = i + 1
			newActiveAnchorScore = math.Round(float64(data.GetNewActiveAnchorScore())*0.15/10) / 100
			log.DebugWithCtx(ctx, "GetGuildOperationalSummary newActiveAnchorScore %+v", newActiveAnchorScore)
			if newActiveAnchorRank <= HighLevel {
				highLevelDataList = append(highLevelDataList, &ScoreData{
					TotalScore: newActiveAnchorScore,
					Cnt:        data.GetNewActiveAnchorCnt(),
					Rank:       newActiveAnchorRank,
					Type:       NewActiveAnchorScore,
					Name:       "新增活跃达人",
				})
			}
			levelDataList = append(levelDataList, &ScoreData{
				TotalScore: newActiveAnchorScore,
				Cnt:        data.GetNewActiveAnchorCnt(),
				Rank:       newActiveAnchorRank,
				Type:       NewActiveAnchorScore,
				Name:       "新增活跃达人",
			})
			log.DebugWithCtx(ctx, "GetGuildOperationalSummary newActiveAnchorRank %+v i:%v", newActiveAnchorRank, i)

			break
		}
	}

	sort.SliceStable(dataList, func(i, j int) bool {
		return dataList[i].GetMatureAnchorCnt() > dataList[j].GetMatureAnchorCnt()
	})
	for i, data := range dataList {
		if data.GetGuildId() == guildId {
			matureAnchorRank = i + 1
			matureAnchorScore = math.Round(float64(data.GetMatureAnchorScore())*0.2/10) / 100
			if matureAnchorRank <= HighLevel {
				highLevelDataList = append(highLevelDataList, &ScoreData{
					TotalScore: matureAnchorScore,
					Cnt:        data.GetMatureAnchorCnt(),
					Rank:       matureAnchorRank,
					Type:       MatureAnchorScore,
					Name:       "成熟达人",
				})
			}
			levelDataList = append(levelDataList, &ScoreData{
				TotalScore: matureAnchorScore,
				Cnt:        data.GetMatureAnchorCnt(),
				Rank:       matureAnchorRank,
				Type:       MatureAnchorScore,
				Name:       "成熟达人",
			})

			break
		}
	}

	sort.SliceStable(dataList, func(i, j int) bool {
		return dataList[i].GetProAnchorCnt() > dataList[j].GetProAnchorCnt()
	})
	for i, data := range dataList {
		if data.GetGuildId() == guildId {
			proAnchorRank = i + 1
			proAnchorScore = math.Round(float64(data.GetProAnchorScore())*0.25/10) / 100
			if proAnchorRank <= HighLevel {
				highLevelDataList = append(highLevelDataList, &ScoreData{
					Cnt:        data.GetProAnchorCnt(),
					TotalScore: proAnchorScore,
					Rank:       proAnchorRank,
					Type:       ProAnchorScore,
					Name:       "专业从业者",
				})
			}
			levelDataList = append(levelDataList, &ScoreData{
				Cnt:        data.GetProAnchorCnt(),
				TotalScore: proAnchorScore,
				Rank:       proAnchorRank,
				Type:       ProAnchorScore,
				Name:       "专业从业者",
			})

			break
		}
	}

	sort.SliceStable(dataList, func(i, j int) bool {
		return dataList[i].GetPotActiveAnchorCnt() > dataList[j].GetPotActiveAnchorCnt()
	})
	for i, data := range dataList {
		if data.GetGuildId() == guildId {
			potActiveAnchorRank = i + 1
			potActiveAnchorScore = math.Round(float64(data.GetPotActiveAnchorScore())*0.2/10) / 100
			if potActiveAnchorRank <= HighLevel {
				highLevelDataList = append(highLevelDataList, &ScoreData{
					Cnt:        data.GetPotActiveAnchorCnt(),
					TotalScore: potActiveAnchorScore,
					Rank:       potActiveAnchorRank,
					Type:       PotActiveAnchorScore,
					Name:       "潜力活跃达人",
				})
			}
			levelDataList = append(levelDataList, &ScoreData{
				Cnt:        data.GetPotActiveAnchorCnt(),
				TotalScore: potActiveAnchorScore,
				Rank:       potActiveAnchorRank,
				Type:       PotActiveAnchorScore,
				Name:       "潜力活跃达人",
			})

			break
		}
	}

	sort.SliceStable(dataList, func(i, j int) bool {
		if dataList[i].GetTotalScore() > dataList[j].GetTotalScore() {
			return true
		}
		if dataList[i].GetTotalScore() == dataList[j].GetTotalScore() {
			return dataList[i].GetRevenue() > dataList[j].GetRevenue()
		}
		return false
	})
	for i, data := range dataList {
		if data.GetGuildId() == guildId {
			totalRank = i + 1
			if data.GetTotalScore() == 0 {
				totalRank = len(dataList)
			}
			log.DebugWithCtx(ctx, "GetGuildOperationalSummary totalRank %+v i:%v, totalScore:%v", totalRank, i, totalScore)
			totalScore = float64(data.GetTotalScore())
			break
		}
	}
	totalScore = math.Round((totalScore / 1000)) / 100

	resp := &api.GetGuildOperationalSummaryResponse{}
	if totalScore >= 85 {
		resp.Level = uint32(api.GuildOperationalSummaryLevel_GuildOperationalSummaryLevel_Excellent)
	} else if totalScore >= 70 {
		resp.Level = uint32(api.GuildOperationalSummaryLevel_GuildOperationalSummaryLevel_Good)
	} else if totalScore >= 60 {
		resp.Level = uint32(api.GuildOperationalSummaryLevel_GuildOperationalSummaryLevel_Median)
	} else if totalScore >= 40 {
		resp.Level = uint32(api.GuildOperationalSummaryLevel_GuildOperationalSummaryLevel_Ordinary)
	} else {
		resp.Level = uint32(api.GuildOperationalSummaryLevel_GuildOperationalSummaryLevel_Poor)
	}
	//resp.OperationalLevelDesc = fmt.Sprintf("您的公会当前经营水平分：%d分，排名：%d名", totalScore, totalRank)
	resp.OperationalLevelDesc = fmt.Sprintf("<p style=\"font-size: 18px; font-weight: 400; color: #000000;\">公会当前经营水平分 <span style=\"height: 30px;font-size: 30px;font-weight: 600;color: #388dff;line-height: 30px;etter-spacing: 0px;\"> %0.2f <span style=\"font-size: 22px;\">分</span></span>，\n            排名第<span style=\"    height: 30px;font-size: 30px;font-weight: 600;color: #388dff;line-height: 30px;etter-spacing: 0px;\"> %d <span style=\"font-size: 22px;\">名</span></span></p>", totalScore, totalRank)
	if len(highLevelDataList) > 0 {
		sort.SliceStable(highLevelDataList, func(i, j int) bool {
			if highLevelDataList[i].Rank < highLevelDataList[j].Rank {
				return true
			}
			if highLevelDataList[i].Rank == highLevelDataList[j].Rank {
				return highLevelDataList[i].TotalScore > highLevelDataList[j].TotalScore
			}
			return false
		})
		tmpData := highLevelDataList[0]
		//resp.OperationalStrengthsDesc = fmt.Sprintf("经营维度中%s指标排名最高，排名: %d名，指标得分: %d分", tmpData.Name, tmpData.Rank, tmpData.TotalScore)
		resp.OperationalStrengthsDesc = fmt.Sprintf("<p style=\"font-size: 16px; font-weight: 400; color: #000000;\">经营维度中<span style=\" height: 28px; font-size: 18px; font-weight: 600;color: #388dff;line-height: 28px; letter-spacing: 0px;\">「%s」</span>指标排名最高，排名<span style=\" height: 28px; font-size: 18px; font-weight: 600;color: #388dff;line-height: 28px; letter-spacing: 0px;\">%d名</span>，指标得分<span style=\" height: 28px; font-size: 18px; font-weight: 600;color: #388dff;line-height: 28px; letter-spacing: 0px;\">%0.2f分</span></p>", tmpData.Name, tmpData.Rank, tmpData.TotalScore)
	} else {
		resp.OperationalStrengthsDesc = fmt.Sprintf("<p style=\"font-size: 16px; font-weight: 400; color: #000000;\">当前公会整体经营表现欠佳，各指标排名落后</p>")
	}
	sort.SliceStable(levelDataList, func(i, j int) bool {
		if levelDataList[i].Rank > levelDataList[j].Rank {
			return true
		}
		if levelDataList[i].Rank == levelDataList[j].Rank {
			return levelDataList[i].TotalScore < levelDataList[j].TotalScore
		}
		return false
	})

	resp.OperationalLevelDescDetailTitle = fmt.Sprintf("<p>\n   <span style=\" font-size: 24px; font-weight: 600; color: #1E1F21;\">总分：</span>\n                    <span style=\" height: 30px;font-size: 30px; font-weight: 600;color: #388dff; line-height: 30px;\">%0.2f<span style=\"font-size:22px \">分</span></span>\n     </p>", totalScore)
	resp.OperationalLevelDescDetailContent = fmt.Sprintf("<p>\n   公会营收流水\n   <span style=\"height: 20px;font-size: 20px;font-weight: 600;color: #388DFF;line-height: 20px;letter-spacing: 0px;\">%0.2f分</span>\n         +\n        新增活跃达人\n     <span style=\"height: 20px;font-size: 20px;font-weight: 600;color: #388DFF;line-height: 20px;letter-spacing: 0px;\">%0.2f分</span>\n    +\n   专业从业者\n  <span style=\"height: 20px;font-size: 20px;font-weight: 600;color: #388DFF;line-height: 20px;letter-spacing: 0px;\">%0.2f分</span>\n   +\n  成熟达人\n  <span style=\"height: 20px;font-size: 20px;font-weight: 600;color: #388DFF;line-height: 20px;letter-spacing: 0px;\">%0.2f分</span>\n  +\n        潜力活跃达人\n     <span style=\"height: 20px;font-size: 20px;font-weight: 600;color: #388DFF;line-height: 20px;letter-spacing: 0px;\">%0.2f分</span>\n   </p>\n     </p>", revenueScore, newActiveAnchorScore, proAnchorScore, matureAnchorScore, potActiveAnchorScore)
	resp.OperationalLevelDescDetailCalculate = "【（公会营收流水/最高公会营收流水）*20% + （公会新增活跃达人数/最高公会新增活跃达人数）*15% + （公会专业从业者数/最高公会专业从业者数） *25% + （成熟达人数/最高公会成熟达人数）*20% + （公会潜力活跃达人数/最高公会潜力活跃达人数）*20% 】 *100"
	if len(levelDataList) > 1 {
		tmpData_1 := levelDataList[0]
		tmpData_2 := levelDataList[1]
		resp.OperationalWeaknessesDesc = fmt.Sprintf("<p style=\"margin-top:-23px;font-size: 14px; font-weight: 400; color: #000000;\">经营维度中<span style=\" height: 28px; font-size: 18px; font-weight: 600;color: #388dff;line-height: 28px; letter-spacing: 0px;\">「%s」</span>指标和<span style=\" height: 28px; font-size: 18px; font-weight: 600;color: #388dff;line-height: 28px; letter-spacing: 0px;\">「%s」</span>指标得分最低，分别为<span style=\" height: 28px; font-size: 18px; font-weight: 600;color: #388dff;line-height: 28px; letter-spacing: 0px;\">%0.2f分</span>\n                                    <span style=\" height: 28px; font-size: 18px; font-weight: 600;color: #388dff;line-height: 28px; letter-spacing: 0px;\">（%d名）</span>和<span style=\" height: 28px; font-size: 18px; font-weight: 600;color: #388dff;line-height: 28px; letter-spacing: 0px;\">%0.2f分</span><span style=\" height: 28px; font-size: 18px; font-weight: 600;color: #388dff;line-height: 28px; letter-spacing: 0px;\">（%d名）</span></p>",
			tmpData_1.Name, tmpData_2.Name, tmpData_1.TotalScore, tmpData_1.Rank, tmpData_2.TotalScore, tmpData_2.Rank)
	}

	log.DebugWithCtx(ctx, "GetGuildOperationalSummary end uid:%v guildId:%v resp:%v", uid, guildId, resp)
	_ = web.ServeAPIJson(w, resp)
}
