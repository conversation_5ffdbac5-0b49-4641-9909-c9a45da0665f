package hanlders

import (
	"context"
	"encoding/json"
	"net/http"
	"time"

	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/oa"
	"golang.52tt.com/pkg/settlement"
	"golang.52tt.com/pkg/web"
	pb "golang.52tt.com/protocol/services/settlement-bill"
	"golang.52tt.com/services/guild-management/guild-management-http-logic/models"
	api "golang.52tt.com/services/guild-management/guild-management-http-logic/models/gen-go"
)

const (
	invoiceVerifySuccess = 0
	invoiceVerifyFiled   = -1
)

func GetSettlementBillWaitWithdraw(authInfo *web.AuthInfo, w http.ResponseWriter, r *http.Request) {
	ctx, cancel := context.WithTimeout(r.Context(), time.Second*5)
	defer cancel()

	now := time.Now()
	req := &api.GetSettlementBillWaitWithdrawReq{}
	err := json.Unmarshal(authInfo.Body, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetSettlementBillWaitWithdraw Failed to parse request body [%s], err %+v", string(authInfo.Body), err)
		web.ServeBadReq(w)
		return
	}

	log.DebugWithCtx(ctx, "GetSettlementBillWaitWithdraw begin %s %+v", string(authInfo.Body), req)

	uid := authInfo.UserID
	guildId := req.GetGuildId()
	isChairman, serr := CheckIsLibraryGuildChairman(ctx, uid, guildId)
	if serr != nil {
		log.ErrorWithCtx(ctx, "GetSettlementBillWaitWithdraw failed to CheckIsLibraryGuildChairman [%+v], err %+v", req, serr)
		_ = web.ServeAPICodeJson(w, int32(serr.Code()), serr.Message(), nil)
		return
	}

	if !isChairman {
		log.ErrorWithCtx(ctx, "GetSettlementBillWaitWithdraw failed to CheckGuildChairman [%+v]. User is not guild chairman.", req)
		_ = web.ServeAPICodeJson(w, -505, "账号没有权限", nil)
		return
	}

	settlementBillCli := models.GetModelServer().SettlementBillCli
	billsResp, serr := settlementBillCli.GetSettlementBillWaitWithdraw(ctx, uid)
	if serr != nil {
		log.ErrorWithCtx(ctx, "GetSettlementBillWaitWithdraw settlementBillCli err:%v", serr)
		_ = web.ServeAPICodeJson(w, int32(serr.Code()), serr.Message(), nil)
		return
	}
	billItems := make([]*api.SettlementBillItem, 0)
	for _, bill := range billsResp.GetList() {
		billItems = append(billItems, settleBillPb2Item(bill))
	}

	out := &api.GetSettlementBillWaitWithdrawResp{
		Bills:                   billItems,
		TaxCompensationRate:     billsResp.TaxCompensationRate,
		CompensationPoint:       billsResp.CompensationPoint,
		TaxCompensationRateText: billsResp.TaxCompensationRateText,
		CompensationPointText:   billsResp.CompensationPointText,
		TaxRate:                 billsResp.TaxRate,
	}
	log.DebugWithCtx(ctx, "GetSettlementBillWaitWithdraw uid(%d) out(%+v) cost:%v", uid, out, time.Since(now))
	_ = web.ServeAPIJson(w, out)
}

func GetSettlementBillWaitReceipt(authInfo *web.AuthInfo, w http.ResponseWriter, r *http.Request) {
	ctx, cancel := context.WithTimeout(r.Context(), time.Second*5)
	defer cancel()

	now := time.Now()
	req := &api.GetSettlementBillWaitReceiptReq{}
	err := json.Unmarshal(authInfo.Body, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetSettlementBillWaitReceipt Failed to parse request body [%s], err %+v", string(authInfo.Body), err)
		web.ServeBadReq(w)
		return
	}
	log.DebugWithCtx(ctx, "GetSettlementBillWaitReceipt begin %s %+v", string(authInfo.Body), req)

	uid := authInfo.UserID
	guildId := req.GetGuildId()
	isChairman, serr := CheckIsLibraryGuildChairman(ctx, uid, guildId)
	if serr != nil {
		log.ErrorWithCtx(ctx, "GetSettlementBillWaitReceipt failed to CheckIsLibraryGuildChairman [%+v], err %+v", req, serr)
		_ = web.ServeAPICodeJson(w, int32(serr.Code()), serr.Message(), nil)
		return
	}

	if !isChairman {
		log.ErrorWithCtx(ctx, "GetSettlementBillWaitReceipt failed to CheckGuildChairman [%+v]. User is not guild chairman.", req)
		_ = web.ServeAPICodeJson(w, -505, "账号没有权限", nil)
		return
	}

	settlementBillCli := models.GetModelServer().SettlementBillCli
	bills, disableVerify, serr := settlementBillCli.GetSettlementBillWaitReceiptV2(ctx, uid, req.GetReceiptBillId())
	if serr != nil {
		log.ErrorWithCtx(ctx, "GetSettlementBillWaitReceipt settlementBillCli err:%v", err)
		_ = web.ServeAPICodeJson(w, int32(serr.Code()), serr.Message(), nil)
		return
	}
	billItems := make([]*api.SettlementBillItem, 0)
	for _, bill := range bills {
		billItems = append(billItems, settleBillPb2Item(bill))
	}

	out := &api.GetSettlementBillWaitReceiptResp{
		Bills:         billItems,
		DisableVerify: disableVerify,
	}
	log.DebugWithCtx(ctx, "GetSettlementBillWaitReceipt uid(%d) out(%+v) cost:%v", uid, out, time.Since(now))
	_ = web.ServeAPIJson(w, out)
}

func settleBillPb2Item(bill *pb.SettlementBillDetail) *api.SettlementBillItem {
	item := &api.SettlementBillItem{
		BillId:        bill.BillId,
		BillType:      api.SettlementBillType(bill.Type),
		IncomeSum:     bill.IncomeSum,
		IncomePay:     bill.ActualIncomePay,
		FileId:        bill.FileId,
		SettleStart:   bill.SettleStart,
		SettleEnd:     bill.SettleEnd,
		SettleDate:    time.Unix(int64(bill.SettleDate), 0).Format("2006-01-02"),
		AllowWithdraw: bill.AllowWithdraw,
	}

	switch bill.Type {
	case pb.SettlementBillType_GiftScore:
		item.PubGiftScore = bill.GetGeneralBillData().GetMoney()
	case pb.SettlementBillType_AwardScore:
		item.PubAwardScore = bill.GetGeneralBillData().GetMoney()
	case pb.SettlementBillType_MaskPKScore:
		item.PubPkScore = bill.GetGeneralBillData().GetMoney()
	case pb.SettlementBillType_KnightScore:
		item.KnightScore = bill.GetGeneralBillData().GetMoney()
	case pb.SettlementBillType_AmuseCommission:
		item.AmuseCommission = bill.GetGeneralBillData().GetMoney()
		item.AmuseDeductMoney = bill.GetGeneralBillData().GetDeductMoney()
	case pb.SettlementBillType_AmuseExtra:
		item.AmuseExtra = bill.GetGeneralBillData().GetMoney()
		item.PrepaidMoney = bill.GetGeneralBillData().GetPrepaidMoney()
	case pb.SettlementBillType_YuyinBaseCommission:
		item.YuyinCommissionBase = bill.GetGeneralBillData().GetMoney()
		item.YuyinDeductMoney = bill.GetGeneralBillData().GetDeductMoney()
	case pb.SettlementBillType_MonthMiddle:
		item.YuyinCommissionAward = bill.GetGeneralBillData().GetMoney()
	case pb.SettlementBillType_DeepCoop:
		item.DeepCoopSettle = bill.GetDeepCoopBill().GetMoney()
		item.PrepaidMoney = bill.GetDeepCoopBill().GetPrepaidMoney()
	case pb.SettlementBillType_YuyinSubsidy:
		item.YuyinAnchorSubsidy = bill.GetYuyinSubsidy().GetYuyinAnchorSubsidy()
		item.YuyinNewGuildSubsidy = bill.GetYuyinSubsidy().GetYuyinNewGuildSubsidy()
	case pb.SettlementBillType_InteractGameCommission:
		item.InteractGameCommission = bill.GetGeneralBillData().GetMoney()
	case pb.SettlementBillType_InteractGameExtraCommission:
		item.InteractGameCommissionExtra = bill.GetGeneralBillData().GetMoney()
	case pb.SettlementBillType_ESportScore:
		item.EsportScore = bill.GetGeneralBillData().GetMoney()
	case pb.SettlementBillType_ESportCommission:
		item.EsportCommission = bill.GetGeneralBillData().GetMoney()
	}
	return item
}

func AssociateReceipts(authInfo *web.AuthInfo, w http.ResponseWriter, r *http.Request) {
	ctx, cancel := context.WithTimeout(r.Context(), time.Second*5)
	defer cancel()

	now := time.Now()
	req := &api.AssociateReceiptsReq{}
	err := json.Unmarshal(authInfo.Body, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "AssociateReceipts Failed to parse request body [%s], err %+v", string(authInfo.Body), err)
		web.ServeBadReq(w)
		return
	}

	log.DebugWithCtx(ctx, "AssociateReceipts begin %s %+v", string(authInfo.Body), req)

	uid := authInfo.UserID
	guildId := req.GetGuildId()
	isChairman, serr := CheckIsLibraryGuildChairman(ctx, uid, guildId)
	if serr != nil {
		log.ErrorWithCtx(ctx, "AssociateReceipts failed to CheckIsLibraryGuildChairman [%+v], err %+v", req, serr)
		_ = web.ServeAPICodeJson(w, int32(serr.Code()), serr.Message(), nil)
		return
	}

	if !isChairman {
		log.ErrorWithCtx(ctx, "AssociateReceipts failed to CheckGuildChairman [%+v]. User is not guild chairman.", req)
		_ = web.ServeAPICodeJson(w, -505, "账号没有权限", nil)
		return
	}

	settlementBillCli := models.GetModelServer().SettlementBillCli
	receiptBillId, serr := settlementBillCli.AssociateReceipt(ctx, &pb.AssociateReceiptReq{
		Uid:               uid,
		ReceiptIds:        req.ReceiptIDs,
		SettlementBillIds: req.SettlementOrderIDs,
		ExpressOrderIds:   req.ExpressOrderIDs,
		ReceiptBillId:     req.ReceiptBillId,
	})
	if serr != nil {
		log.ErrorWithCtx(ctx, "AssociateReceipts settlementBillCli err:%v", err)
		_ = web.ServeAPICodeJson(w, int32(serr.Code()), serr.Message(), nil)
		return
	}

	out := &api.AssociateReceiptsResp{
		ReceiptBillId: receiptBillId,
	}
	log.DebugWithCtx(ctx, "AssociateReceipts uid(%d) out(%+v) cost:%v", uid, out, time.Since(now))
	_ = web.ServeAPIJson(w, out)
}

func GetReceiptList(authInfo *web.AuthInfo, w http.ResponseWriter, r *http.Request) {
	ctx, cancel := context.WithTimeout(r.Context(), time.Second*5)
	defer cancel()

	now := time.Now()
	req := &api.GetReceiptListReq{}
	err := json.Unmarshal(authInfo.Body, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetReceiptList Failed to parse request body [%s], err %+v", string(authInfo.Body), err)
		web.ServeBadReq(w)
		return
	}

	log.DebugWithCtx(ctx, "GetReceiptList begin %s %+v", string(authInfo.Body), req)

	uid := authInfo.UserID
	guildId := req.GetGuildId()
	isChairman, serr := CheckIsLibraryGuildChairman(ctx, uid, guildId)
	if serr != nil {
		log.ErrorWithCtx(ctx, "GetReceiptList failed to CheckIsLibraryGuildChairman [%+v], err %+v", req, serr)
		_ = web.ServeAPICodeJson(w, int32(serr.Code()), serr.Message(), nil)
		return
	}

	if !isChairman {
		log.ErrorWithCtx(ctx, "GetReceiptList failed to CheckGuildChairman [%+v]. User is not guild chairman.", req)
		_ = web.ServeAPICodeJson(w, -505, "账号没有权限", nil)
		return
	}

	settlementBillCli := models.GetModelServer().SettlementBillCli
	resp, serr := settlementBillCli.GetReceiptList(ctx, req.ReceiptBillId)
	if serr != nil {
		log.ErrorWithCtx(ctx, "GetReceiptList settlementBillCli err:%v", err)
		_ = web.ServeAPICodeJson(w, int32(serr.Code()), serr.Message(), nil)
		return
	}

	receiptItems := make([]*api.GetReceiptListResp_ReceiptItem, 0)
	for _, receipt := range resp.Receipts {
		receiptItems = append(receiptItems, &api.GetReceiptListResp_ReceiptItem{
			ReceiptId: receipt.ReceiptId,
			FileName:  receipt.FileName,
			FileUrl:   receipt.FileUrl,
		})
	}

	expressOrderIds := make([]string, 0)
	if len(resp.ExpressOrderIds) > 0 {
		expressOrderIds = resp.ExpressOrderIds
	}

	out := &api.GetReceiptListResp{
		ExpressOrderIDs: expressOrderIds,
	}
	out.Receipts = receiptItems
	log.DebugWithCtx(ctx, "GetReceiptList uid(%d) out(%+v) cost:%v", uid, out, time.Since(now))
	_ = web.ServeAPIJson(w, out)
}

func GetReceiptUrl(authInfo *web.AuthInfo, w http.ResponseWriter, r *http.Request) {
	ctx, cancel := context.WithTimeout(r.Context(), time.Second*5)
	defer cancel()

	now := time.Now()
	req := &api.GetReceiptUrlReq{}
	err := json.Unmarshal(authInfo.Body, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetReceiptUrl Failed to parse request body [%s], err %+v", string(authInfo.Body), err)
		web.ServeBadReq(w)
		return
	}

	log.DebugWithCtx(ctx, "GetReceiptUrl begin %s %+v", string(authInfo.Body), req)

	// todo 权限检查
	uid := authInfo.UserID
	oaCli := models.GetModelServer().OAClient
	url, err := oaCli.GetFileUrl(ctx, req.ReceiptId)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetReceiptUrl Upload err:%v", err)
		_ = web.ServeAPICodeJson(w, -500, err.Error(), nil)
		return
	}

	out := &api.GetReceiptUrlResp{
		FileUrl: url,
	}
	log.DebugWithCtx(ctx, "GetReceiptUrl uid(%d) out(%+v) cost:%v", uid, out, time.Since(now))
	_ = web.ServeAPIJson(w, out)
}

func GetAssociatedBillItems(authInfo *web.AuthInfo, w http.ResponseWriter, r *http.Request) {
	ctx, cancel := context.WithTimeout(r.Context(), time.Second*5)
	defer cancel()

	now := time.Now()
	req := &api.GetAssociatedBillItemsReq{}
	err := json.Unmarshal(authInfo.Body, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAssociatedBillItems Failed to parse request body [%s], err %+v", string(authInfo.Body), err)
		web.ServeBadReq(w)
		return
	}

	log.DebugWithCtx(ctx, "GetAssociatedBillItems begin %s %+v", string(authInfo.Body), req)

	uid := authInfo.UserID
	guildId := req.GetGuildId()
	isChairman, serr := CheckIsLibraryGuildChairman(ctx, uid, guildId)
	if serr != nil {
		log.ErrorWithCtx(ctx, "GetAssociatedBillItems failed to CheckIsLibraryGuildChairman [%+v], err %+v", req, serr)
		_ = web.ServeAPICodeJson(w, int32(serr.Code()), serr.Message(), nil)
		return
	}

	if !isChairman {
		log.ErrorWithCtx(ctx, "GetAssociatedBillItems failed to CheckGuildChairman [%+v]. User is not guild chairman.", req)
		_ = web.ServeAPICodeJson(w, -505, "账号没有权限", nil)
		return
	}

	settlementBillCli := models.GetModelServer().SettlementBillCli
	resp, serr := settlementBillCli.GetAssociatedBillItems(ctx, &pb.GetAssociatedBillItemsReq{
		Uid:           uid,
		ReceiptBillId: req.ReceiptBillId,
	})
	if serr != nil {
		log.ErrorWithCtx(ctx, "GetAssociatedBillItems settlementBillCli err:%v", err)
		_ = web.ServeAPICodeJson(w, int32(serr.Code()), serr.Message(), nil)
		return
	}
	billItems := make([]*api.SettlementBillItem, 0)
	for _, bill := range resp.List {
		billItems = append(billItems, settleBillPb2Item(bill))
	}
	out := &api.GetSettlementBillWaitReceiptResp{
		Bills: billItems,
	}
	log.DebugWithCtx(ctx, "GetAssociatedBillItems uid(%d) out(%+v) cost:%v", uid, out, time.Since(now))
	_ = web.ServeAPIJson(w, out)
}

func GetReceiptBillList(authInfo *web.AuthInfo, w http.ResponseWriter, r *http.Request) {
	ctx, cancel := context.WithTimeout(r.Context(), time.Second*5)
	defer cancel()

	now := time.Now()
	req := &api.GetReceiptBillListReq{}
	err := json.Unmarshal(authInfo.Body, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetReceiptBillList Failed to parse request body [%s], err %+v", string(authInfo.Body), err)
		web.ServeBadReq(w)
		return
	}

	log.DebugWithCtx(ctx, "GetReceiptBillList begin %s %+v", string(authInfo.Body), req)

	uid := authInfo.UserID
	guildId := req.GetGuildId()
	isChairman, serr := CheckIsLibraryGuildChairman(ctx, uid, guildId)
	if serr != nil {
		log.ErrorWithCtx(ctx, "GetReceiptBillList failed to CheckIsLibraryGuildChairman [%+v], err %+v", req, serr)
		_ = web.ServeAPICodeJson(w, int32(serr.Code()), serr.Message(), nil)
		return
	}

	if !isChairman {
		log.ErrorWithCtx(ctx, "GetReceiptBillList failed to CheckGuildChairman [%+v]. User is not guild chairman.", req)
		_ = web.ServeAPICodeJson(w, -505, "账号没有权限", nil)
		return
	}

	statusList := make([]pb.OAAuditBillStatus, 0)
	for _, status := range req.Status {
		statusList = append(statusList, pb.OAAuditBillStatus(status))
	}

	resp, serr := models.GetModelServer().SettlementBillCli.GetReceiptBillList(ctx, &pb.GetReceiptBillListReq{
		Uid:    uid,
		Offset: req.Offset,
		Limit:  req.Limit,
		Status: statusList,
	})
	if serr != nil {
		log.ErrorWithCtx(ctx, "GetReceiptBillList settlementBillCli err:%v", err)
		_ = web.ServeAPICodeJson(w, int32(serr.Code()), serr.Message(), nil)
		return
	}

	receiptBillList := make([]*api.ReceiptBill, 0)
	for _, receiptBill := range resp.ReceiptBillList {
		receiptBillList = append(receiptBillList, &api.ReceiptBill{
			ReceiptBillId:    receiptBill.ReceiptBillId,
			Status:           receiptBill.Status,
			SettlementBillId: receiptBill.SettlementBillId,
			Remark:           receiptBill.Remark,
		})
	}

	out := &api.GetReceiptBillListResp{
		ReceiptBillList: receiptBillList,
		Total:           resp.Total,
	}
	log.DebugWithCtx(ctx, "GetReceiptBillList uid(%d) out(%+v) cost:%v", uid, out, time.Since(now))
	_ = web.ServeAPIJson(w, out)
}

func GetWaitWithdrawMonths(authInfo *web.AuthInfo, w http.ResponseWriter, r *http.Request) {
	ctx, cancel := context.WithTimeout(r.Context(), time.Second*5)
	defer cancel()

	now := time.Now()
	req := &api.GetWaitWithdrawMonthsReq{}
	err := json.Unmarshal(authInfo.Body, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetWaitWithdrawMonths Failed to parse request body [%s], err %+v", string(authInfo.Body), err)
		web.ServeBadReq(w)
		return
	}

	log.DebugWithCtx(ctx, "GetWaitWithdrawMonths begin %s %+v", string(authInfo.Body), req)

	uid := authInfo.UserID
	guildId := req.GetGuildId()
	isChairman, serr := CheckIsLibraryGuildChairman(ctx, uid, guildId)
	if serr != nil {
		log.ErrorWithCtx(ctx, "GetWaitWithdrawMonths failed to CheckIsLibraryGuildChairman [%+v], err %+v", req, serr)
		_ = web.ServeAPICodeJson(w, int32(serr.Code()), serr.Message(), nil)
		return
	}

	if !isChairman {
		log.ErrorWithCtx(ctx, "GetWaitWithdrawMonths failed to CheckGuildChairman [%+v]. User is not guild chairman.", req)
		_ = web.ServeAPICodeJson(w, -505, "账号没有权限", nil)
		return
	}

	settlementBillCli := models.GetModelServer().SettlementBillCli
	months, serr := settlementBillCli.GetWaitWithdrawMonths(ctx, req.Uid, pb.SettlementBillType(req.BillType))
	if serr != nil {
		log.ErrorWithCtx(ctx, "GetWaitWithdrawMonths settlementBillCli err:%v", err)
		_ = web.ServeAPICodeJson(w, int32(serr.Code()), serr.Message(), nil)
		return
	}

	out := &api.GetWaitWithdrawMonthsResp{
		Months: months,
	}
	log.DebugWithCtx(ctx, "GetWaitWithdrawMonths uid(%d) out(%+v) cost:%v", uid, out, time.Since(now))
	_ = web.ServeAPIJson(w, out)
}

func GetMonthsBySettleBillId(authInfo *web.AuthInfo, w http.ResponseWriter, r *http.Request) {
	ctx, cancel := context.WithTimeout(r.Context(), time.Second*5)
	defer cancel()

	now := time.Now()
	req := &api.GetMonthsBySettleBillIdReq{}
	err := json.Unmarshal(authInfo.Body, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetMonthsBySettleBillId Failed to parse request body [%s], err %+v", string(authInfo.Body), err)
		web.ServeBadReq(w)
		return
	}

	log.DebugWithCtx(ctx, "GetMonthsBySettleBillId begin %s %+v", string(authInfo.Body), req)

	uid := authInfo.UserID
	guildId := req.GetGuildId()
	isChairman, serr := CheckIsLibraryGuildChairman(ctx, uid, guildId)
	if serr != nil {
		log.ErrorWithCtx(ctx, "GetMonthsBySettleBillId failed to CheckIsLibraryGuildChairman [%+v], err %+v", req, serr)
		_ = web.ServeAPICodeJson(w, int32(serr.Code()), serr.Message(), nil)
		return
	}

	if !isChairman {
		log.ErrorWithCtx(ctx, "GetMonthsBySettleBillId failed to CheckGuildChairman [%+v]. User is not guild chairman.", req)
		_ = web.ServeAPICodeJson(w, -505, "账号没有权限", nil)
		return
	}

	settlementBillCli := models.GetModelServer().SettlementBillCli
	months, serr := settlementBillCli.GetMonthsBySettleBillId(ctx, &pb.GetMonthsBySettleBillIdReq{
		Uid:    req.Uid,
		BillId: req.BillId,
	})
	if serr != nil {
		log.ErrorWithCtx(ctx, "GetMonthsBySettleBillId settlementBillCli err:%v", err)
		_ = web.ServeAPICodeJson(w, int32(serr.Code()), serr.Message(), nil)
		return
	}

	out := &api.GetWaitWithdrawMonthsResp{
		Months: months,
	}
	log.DebugWithCtx(ctx, "GetMonthsBySettleBillId uid(%d) out(%+v) cost:%v", uid, out, time.Since(now))
	_ = web.ServeAPIJson(w, out)
}

func UploadReceipt(authInfo *web.AuthInfo, w http.ResponseWriter, r *http.Request) {
	ctx, cancel := context.WithTimeout(r.Context(), time.Second*30)
	defer cancel()

	now := time.Now()

	if err := r.ParseMultipartForm(10 << 20); err != nil {
		log.ErrorWithCtx(ctx, "UploadReceipt ParseMultipartForm err:%v", err)
		_ = web.ServeAPICodeJson(w, -500, "文件大小超出限制", nil)
		return
	}

	file, handler, err := r.FormFile("file")
	if err != nil {
		log.ErrorWithCtx(ctx, "UploadReceipt FormFile err:%v", err)
		_ = web.ServeAPICodeJson(w, -500, "系统错误", nil)
		return
	}
	defer file.Close()

	errorList := make([]*api.ReceiptErrorInfo, 0)

	oaCli := models.GetModelServer().OAClient
	fileInfo, invoice, err := oaCli.UploadInvoiceVerify(ctx, file, handler.Filename)
	if err != nil {
		// 灵犀异常
		if oaErr, ok := err.(*oa.Error); ok {
			log.WarnWithCtx(ctx, "UploadReceipt verifyRet FAILED code:%d, reason:%s", oaErr.Code, oaErr.Message)

			// 灵犀校验发票错误
			out := &api.UploadReceiptResp{
				VerificationResult: invoiceVerifyFiled,
			}

			switch oaErr.Code {
			case oa.ReceiptTodayTimesErr:
				errorList = append(errorList, &api.ReceiptErrorInfo{
					Title:  "发票查验次数过多",
					Result: "",
					Reason: "查验失败原因：同一张票一天最多查询5次",
				})
			case oa.ReceiptNotFoundErr:
				errorList = append(errorList, &api.ReceiptErrorInfo{
					Title: "查无此票",
				})
			case oa.ReceiptConsistencyErr:
				errorList = append(errorList, &api.ReceiptErrorInfo{
					Title: "发票信息不一致",
				})
			case oa.ReceiptTimeoutErr:
				errorList = append(errorList, &api.ReceiptErrorInfo{
					Title: "识别超时，请重新上传",
				})
			default:
				errorList = append(errorList, &api.ReceiptErrorInfo{
					Title: "系统未知异常，联系管理员",
				})
			}

			log.InfoWithCtx(ctx, "UploadReceipt errList: %+v", errorList)
			out.ErrorList = errorList
			_ = web.ServeAPIJson(w, out)
			return
		}

		log.ErrorWithCtx(ctx, "UploadReceipt UploadInvoiceVerify err:%v", err)
		_ = web.ServeAPICodeJson(w, -500, "系统错误", nil)
		return
	}

	if len(invoice.ItemInfos) == 0 {
		log.ErrorWithCtx(ctx, "UploadReceipt invoice.ItemInfos is empty")
		_ = web.ServeAPICodeJson(w, -500, "系统错误", nil)
		return
	}

	// 首个项目信息
	invoiceFirstItem := invoice.ItemInfos[0]

	log.InfoWithCtx(ctx, "UploadReceipt name:%s, size:%d", handler.Filename, handler.Size)

	uid := authInfo.UserID
	amount := settlement.MoneyToCent(invoice.SumAmount)
	resp, serr := models.GetModelServer().SettlementBillCli.RecordReceiptFile(ctx, &pb.RecordReceiptFileReq{
		Uid:            uid,
		ReceiptId:      fileInfo.FileId,
		FileName:       fileInfo.FileName,
		Size:           uint64(fileInfo.Size),
		ShowSize:       fileInfo.ShowSize,
		FileClass:      fileInfo.FileClass,
		ReceiptDate:    invoice.InvoiceDate,
		ReceiptCode:    invoice.InvoiceCode,
		ReceiptNo:      invoice.InvoiceNo,
		TaxRate:        invoiceFirstItem.ItemTaxRate,
		TaxAmount:      settlement.MoneyToCent(invoice.TaxAmount),
		Amount:         amount,
		ExTaxAmount:    settlement.MoneyToCent(invoice.ExTaxAmount),
		VCode:          invoice.CheckCode,
		Content:        invoiceFirstItem.ItemName,
		SellerName:     invoice.PayeeName,
		SellerTaxNo:    invoice.PayeeTaxNo,
		PurchaserName:  invoice.PayerName,
		PurchaserTaxNo: invoice.PayerTaxNo,
	})
	if serr != nil {
		log.ErrorWithCtx(ctx, "UploadReceipt RecordReceiptFile err:%v", serr)
		_ = web.ServeAPICodeJson(w, -500, "系统错误", nil)
		return
	}

	if invoice.InvalidFlag != oa.ReceiptStatusNormal {
		errorList = append(errorList, &api.ReceiptErrorInfo{
			Title:  "发票状态",
			Result: "发票识别结果：作废|红冲|失联|失控|异常状态",
			Reason: "查验失败原因：不符合平台要求",
		})
	}

	for _, e := range resp.GetErrorList() {
		errorList = append(errorList, &api.ReceiptErrorInfo{
			Title:  e.GetTitle(),
			Result: e.GetResult(),
			Reason: e.GetReason(),
		})
	}

	var verifyResult int32
	if len(errorList) == 0 {
		verifyResult = invoiceVerifySuccess
	} else {
		verifyResult = invoiceVerifyFiled
	}

	out := &api.UploadReceiptResp{
		ReceiptId:          fileInfo.FileId,
		ReceiptSign:        resp.GetReceiptSign(),
		Amount:             amount,
		VerificationResult: verifyResult,
		ErrorList:          errorList,
	}
	log.InfoWithCtx(ctx, "UploadReceipt errList: %+v", errorList)
	log.DebugWithCtx(ctx, "UploadReceipt uid(%d) out(%+v) cost:%v", uid, out, time.Since(now))
	_ = web.ServeAPIJson(w, out)
}

func GetReceiptTotalAmountReq(authInfo *web.AuthInfo, w http.ResponseWriter, r *http.Request) {
	ctx, cancel := context.WithTimeout(r.Context(), time.Second*5)
	defer cancel()

	now := time.Now()
	req := &api.GetMonthsBySettleBillIdReq{}
	err := json.Unmarshal(authInfo.Body, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetReceiptTotalAmountReq Failed to parse request body [%s], err %+v", string(authInfo.Body), err)
		web.ServeBadReq(w)
		return
	}

	log.DebugWithCtx(ctx, "GetReceiptTotalAmountReq begin %s %+v", string(authInfo.Body), req)

	uid := authInfo.UserID
	guildId := req.GetGuildId()
	isChairman, serr := CheckIsLibraryGuildChairman(ctx, uid, guildId)
	if serr != nil {
		log.ErrorWithCtx(ctx, "GetReceiptTotalAmountReq failed to CheckIsLibraryGuildChairman [%+v], err %+v", req, serr)
		_ = web.ServeAPICodeJson(w, int32(serr.Code()), serr.Message(), nil)
		return
	}

	if !isChairman {
		log.ErrorWithCtx(ctx, "GetReceiptTotalAmountReq failed to CheckGuildChairman [%+v]. User is not guild chairman.", req)
		_ = web.ServeAPICodeJson(w, -505, "账号没有权限", nil)
		return
	}

	settlementBillCli := models.GetModelServer().SettlementBillCli
	months, serr := settlementBillCli.GetMonthsBySettleBillId(ctx, &pb.GetMonthsBySettleBillIdReq{
		Uid:    req.Uid,
		BillId: req.BillId,
	})
	if serr != nil {
		log.ErrorWithCtx(ctx, "GetReceiptTotalAmountReq settlementBillCli err:%v", err)
		_ = web.ServeAPICodeJson(w, int32(serr.Code()), serr.Message(), nil)
		return
	}

	out := &api.GetWaitWithdrawMonthsResp{
		Months: months,
	}
	log.DebugWithCtx(ctx, "GetReceiptTotalAmountReq uid(%d) out(%+v) cost:%v", uid, out, time.Since(now))
	_ = web.ServeAPIJson(w, out)

}
