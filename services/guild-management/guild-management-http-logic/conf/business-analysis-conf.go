package conf

import (
	api "golang.52tt.com/services/guild-management/guild-management-http-logic/models/gen-go"
)

const (
	SteadyGrowth       = "平稳增长"
	ContinuedGrowth    = "持续增长"
	FluctuatingGrowth  = "波动增长"
	Stable             = "稳定"
	RapidDecline       = "快速下滑"
	ContinuedDecline   = "持续下滑"
	FluctuatingDecline = "震荡下滑"
	Unstable           = "不稳定"
)

var MapType2Name = map[api.AbilityItemType]string{
	api.AbilityItemType_AbilityItemType_Revenue:         "营收项",
	api.AbilityItemType_AbilityItemType_Recruit:         "招新项",
	api.AbilityItemType_AbilityItemType_Hatch:           "孵化项",
	api.AbilityItemType_AbilityItemType_Safety:          "安全项",
	api.AbilityItemType_AbilityItemType_Stability:       "稳定性项",
	api.AbilityItemType_AbilityItemType_Risk_Resistance: "抗风险项",
}

var MapChangeStr2Color = map[string]string{
	SteadyGrowth:       "#FF5674",
	ContinuedGrowth:    "#FF5674",
	FluctuatingGrowth:  "#FF5674",
	Stable:             "#388DFF",
	RapidDecline:       "#17CBC2",
	ContinuedDecline:   "#17CBC2",
	FluctuatingDecline: "#17CBC2",
	Unstable:           "#17CBC2",
}

var MapFeeType2Name = map[api.KpiType]string{
	api.KpiType_Kpi_Type_0_2w_Fee_Anchor:      "[0,2w豆)",
	api.KpiType_Kpi_Type_2w_10w_Fee_Anchor:    "[2w,10w豆)",
	api.KpiType_Kpi_Type_10w_20w_Fee_Anchor:   "[10w,20w豆)",
	api.KpiType_Kpi_Type_20w_50w_Fee_Anchor:   "[20w,50w豆)",
	api.KpiType_Kpi_Type_50w_100w_Fee_Anchor:  "[50w,100w豆)",
	api.KpiType_Kpi_Type_100w_Up_Fee_Anchor:   "[100w,+∞豆)",
	api.KpiType_Kpi_Type_0_20w_Fee_Anchor:     "[0,20w豆)",
	api.KpiType_Kpi_Type_100w_300w_Fee_Anchor: "[100w,300w豆)",
	api.KpiType_Kpi_Type_300w_500w_Fee_Anchor: "[300w,500w豆)",
	api.KpiType_Kpi_Type_500w_Up_Fee_Anchor:   "[500w,+∞豆)",
}

// 分值配置
type ScoreConfig struct {
	Score   int32
	MinIncr uint64
	MaxIncr uint64
}

// 能力项计分配置
type AbilityScoreConfig struct {
	MinVal          uint64
	MaxVal          uint64
	BaseVal         uint64
	Score           int32
	FMinVal         float64       // 浮点型区间
	FMaxVal         float64       // 浮点型区间
	ScoreConfigList []ScoreConfig //增长值分数配置
}

// 公会水平配置
type GuildLevelConfig struct {
	MinVal   float64
	MaxVal   float64
	LevelStr string
}

// 公会水平配置
var GuildLevelConfigList = []GuildLevelConfig{
	{MinVal: 0, MaxVal: 0.1, LevelStr: "在同层级公会中处于领先水平"},
	{MinVal: 0.1, MaxVal: 0.3, LevelStr: "在同层级公会中处于较高水平"},
	{MinVal: 0.3, MaxVal: 0.6, LevelStr: "与同层级公会平均水平持平"},
	{MinVal: 0.6, MaxVal: 0, LevelStr: "在同层级公会中处于较低水平"},
}

// 营收项计分配置
var RevenueScoreConfig = []AbilityScoreConfig{
	{
		MinVal:  0,
		MaxVal:  1000000,
		BaseVal: 100000,
		ScoreConfigList: []ScoreConfig{{1, 0, 100000}, {2, 100000, 200000}, {3, 200000, 300000},
			{4, 300000, 400000}, {5, 400000, 500000}, {6, 500000, 600000}, {7, 600000, 700000},
			{8, 700000, 800000}, {9, 800000, 900000}, {10, 900000, 0}},
	},
	{
		MinVal:  1000000,
		MaxVal:  3000000,
		BaseVal: 1000000,
		ScoreConfigList: []ScoreConfig{{1, 0, 100000}, {2, 100000, 200000}, {3, 200000, 400000},
			{4, 400000, 600000}, {5, 600000, 800000}, {6, 800000, 1000000}, {7, 1000000, 1300000},
			{8, 1300000, 1600000}, {9, 1600000, 2000000}, {10, 2000000, 0}},
	},
	{
		MinVal:  3000000,
		MaxVal:  5000000,
		BaseVal: 3000000,
		ScoreConfigList: []ScoreConfig{{1, 0, 100000}, {2, 100000, 200000}, {3, 200000, 400000},
			{4, 400000, 600000}, {5, 600000, 800000}, {6, 800000, 1000000}, {7, 1000000, 1300000},
			{8, 1300000, 1600000}, {9, 1600000, 2000000}, {10, 2000000, 0}},
	},
	{
		MinVal:  5000000,
		MaxVal:  7000000,
		BaseVal: 5000000,
		ScoreConfigList: []ScoreConfig{{1, 0, 100000}, {2, 100000, 200000}, {3, 200000, 400000},
			{4, 400000, 600000}, {5, 600000, 800000}, {6, 800000, 1000000}, {7, 1000000, 1300000},
			{8, 1300000, 1600000}, {9, 1600000, 2000000}, {10, 2000000, 0}},
	},
	{
		MinVal:  7000000,
		MaxVal:  10000000,
		BaseVal: 7000000,
		ScoreConfigList: []ScoreConfig{{1, 0, 100000}, {2, 100000, 300000}, {3, 300000, 600000},
			{4, 600000, 900000}, {5, 900000, 1200000}, {6, 1200000, 1500000}, {7, 1500000, 2000000},
			{8, 2000000, 2500000}, {9, 2500000, 3000000}, {10, 3000000, 0}},
	},
	{
		MinVal:  10000000,
		MaxVal:  15000000,
		BaseVal: 10000000,
		ScoreConfigList: []ScoreConfig{{1, 0, 200000}, {2, 200000, 500000}, {3, 500000, 900000},
			{4, 900000, 1400000}, {5, 1400000, 1900000}, {6, 1900000, 2500000}, {7, 2500000, 3250000},
			{8, 3250000, 4000000}, {9, 4000000, 5000000}, {10, 5000000, 0}},
	},
	{
		MinVal:  15000000,
		MaxVal:  20000000,
		BaseVal: 15000000,
		ScoreConfigList: []ScoreConfig{{1, 0, 200000}, {2, 200000, 500000}, {3, 500000, 900000},
			{4, 900000, 1400000}, {5, 1400000, 1900000}, {6, 1900000, 2500000}, {7, 2500000, 3250000},
			{8, 3250000, 4000000}, {9, 4000000, 5000000}, {10, 5000000, 0}},
	},
	{
		MinVal:  20000000,
		MaxVal:  25000000,
		BaseVal: 20000000,
		ScoreConfigList: []ScoreConfig{{1, 0, 200000}, {2, 200000, 500000}, {3, 500000, 900000},
			{4, 900000, 1400000}, {5, 1400000, 1900000}, {6, 1900000, 2500000}, {7, 2500000, 3250000},
			{8, 3250000, 4000000}, {9, 4000000, 5000000}, {10, 5000000, 0}},
	},
	{
		MinVal:  25000000,
		MaxVal:  30000000,
		BaseVal: 25000000,
		ScoreConfigList: []ScoreConfig{{1, 0, 200000}, {2, 200000, 500000}, {3, 500000, 900000},
			{4, 900000, 1400000}, {5, 1400000, 1900000}, {6, 1900000, 2500000}, {7, 2500000, 3250000},
			{8, 3250000, 4000000}, {9, 4000000, 5000000}, {10, 5000000, 0}},
	},
	{
		MinVal:  30000000,
		MaxVal:  0,
		BaseVal: 30000000,
		ScoreConfigList: []ScoreConfig{{1, 0, 200000}, {2, 200000, 500000}, {3, 500000, 900000},
			{4, 900000, 1400000}, {5, 1400000, 1900000}, {6, 1900000, 2500000}, {7, 2500000, 3250000},
			{8, 3250000, 4000000}, {9, 4000000, 5000000}, {10, 5000000, 0}},
	},
}

// 招新项计分配置
var RecruitScoreConfig = []AbilityScoreConfig{
	{
		MinVal:  0,
		MaxVal:  50,
		BaseVal: 10,
		ScoreConfigList: []ScoreConfig{{1, 0, 5}, {2, 5, 10}, {3, 10, 15},
			{4, 15, 20}, {5, 20, 25}, {6, 25, 30}, {7, 30, 35},
			{8, 35, 40}, {9, 40, 50}, {10, 50, 0}},
	},
	{
		MinVal:  50,
		MaxVal:  100,
		BaseVal: 50,
		ScoreConfigList: []ScoreConfig{{1, 0, 5}, {2, 5, 10}, {3, 10, 15},
			{4, 15, 20}, {5, 20, 25}, {6, 25, 30}, {7, 30, 35},
			{8, 35, 40}, {9, 40, 50}, {10, 50, 0}},
	},
	{
		MinVal:  100,
		MaxVal:  200,
		BaseVal: 100,
		ScoreConfigList: []ScoreConfig{{1, 0, 10}, {2, 10, 20}, {3, 20, 30},
			{4, 30, 40}, {5, 40, 50}, {6, 50, 60}, {7, 60, 70},
			{8, 70, 80}, {9, 80, 100}, {10, 100, 0}},
	},
	{
		MinVal:  200,
		MaxVal:  300,
		BaseVal: 200,
		ScoreConfigList: []ScoreConfig{{1, 0, 10}, {2, 10, 20}, {3, 20, 30},
			{4, 30, 40}, {5, 40, 50}, {6, 50, 60}, {7, 60, 70},
			{8, 70, 80}, {9, 80, 100}, {10, 100, 0}},
	},
	{
		MinVal:  300,
		MaxVal:  500,
		BaseVal: 300,
		ScoreConfigList: []ScoreConfig{{1, 0, 10}, {2, 10, 25}, {3, 25, 40},
			{4, 40, 55}, {5, 55, 80}, {6, 80, 110}, {7, 110, 140},
			{8, 140, 170}, {9, 170, 200}, {10, 200, 0}},
	},
	{
		MinVal:  500,
		MaxVal:  1000,
		BaseVal: 500,
		ScoreConfigList: []ScoreConfig{{1, 0, 10}, {2, 10, 30}, {3, 30, 50},
			{4, 50, 90}, {5, 90, 130}, {6, 130, 160}, {7, 160, 200},
			{8, 200, 250}, {9, 250, 300}, {10, 300, 0}},
	},
	{
		MinVal:  1000,
		MaxVal:  0,
		BaseVal: 1000,
		ScoreConfigList: []ScoreConfig{{1, 0, 10}, {2, 10, 30}, {3, 30, 50},
			{4, 50, 90}, {5, 90, 130}, {6, 130, 160}, {7, 160, 200},
			{8, 200, 250}, {9, 250, 300}, {10, 300, 0}},
	},
}

// 孵化项计分配置
var HatchScoreConfig = []AbilityScoreConfig{
	{
		MinVal:  0,
		MaxVal:  50,
		BaseVal: 10,
		ScoreConfigList: []ScoreConfig{{1, 0, 5}, {2, 5, 10}, {3, 10, 15},
			{4, 15, 20}, {5, 20, 25}, {6, 25, 30}, {7, 30, 35},
			{8, 35, 40}, {9, 40, 50}, {10, 50, 0}},
	},
	{
		MinVal:  50,
		MaxVal:  100,
		BaseVal: 50,
		ScoreConfigList: []ScoreConfig{{1, 0, 5}, {2, 5, 10}, {3, 10, 15},
			{4, 15, 20}, {5, 20, 25}, {6, 25, 30}, {7, 30, 35},
			{8, 35, 40}, {9, 40, 50}, {10, 50, 0}},
	},
	{
		MinVal:  100,
		MaxVal:  200,
		BaseVal: 100,
		ScoreConfigList: []ScoreConfig{{1, 0, 10}, {2, 10, 20}, {3, 20, 30},
			{4, 30, 40}, {5, 40, 50}, {6, 50, 60}, {7, 60, 70},
			{8, 70, 80}, {9, 80, 100}, {10, 100, 0}},
	},
	{
		MinVal:  200,
		MaxVal:  300,
		BaseVal: 200,
		ScoreConfigList: []ScoreConfig{{1, 0, 10}, {2, 10, 20}, {3, 20, 30},
			{4, 30, 40}, {5, 40, 50}, {6, 50, 60}, {7, 60, 70},
			{8, 70, 80}, {9, 80, 100}, {10, 100, 0}},
	},
	{
		MinVal:  300,
		MaxVal:  500,
		BaseVal: 300,
		ScoreConfigList: []ScoreConfig{{1, 0, 10}, {2, 10, 25}, {3, 25, 40},
			{4, 40, 55}, {5, 55, 80}, {6, 80, 110}, {7, 110, 140},
			{8, 140, 170}, {9, 170, 200}, {10, 200, 0}},
	},
	{
		MinVal:  500,
		MaxVal:  1000,
		BaseVal: 500,
		ScoreConfigList: []ScoreConfig{{1, 0, 10}, {2, 10, 30}, {3, 30, 50},
			{4, 50, 90}, {5, 90, 130}, {6, 130, 160}, {7, 160, 200},
			{8, 200, 250}, {9, 250, 300}, {10, 300, 0}},
	},
	{
		MinVal:  1000,
		MaxVal:  0,
		BaseVal: 1000,
		ScoreConfigList: []ScoreConfig{{1, 0, 10}, {2, 10, 30}, {3, 30, 50},
			{4, 50, 90}, {5, 90, 130}, {6, 130, 160}, {7, 160, 200},
			{8, 200, 250}, {9, 250, 300}, {10, 300, 0}},
	},
}

// 安全项计分配置
var SafetyScoreConfig = []AbilityScoreConfig{
	{FMinVal: 0, FMaxVal: 10, Score: 0},
	{FMinVal: 10, FMaxVal: 20, Score: 1},
	{FMinVal: 20, FMaxVal: 30, Score: 2},
	{FMinVal: 30, FMaxVal: 40, Score: 3},
	{FMinVal: 40, FMaxVal: 50, Score: 4},
	{FMinVal: 50, FMaxVal: 60, Score: 5},
	{FMinVal: 60, FMaxVal: 70, Score: 6},
	{FMinVal: 70, FMaxVal: 80, Score: 7},
	{FMinVal: 80, FMaxVal: 90, Score: 8},
	{FMinVal: 90, FMaxVal: 100, Score: 9},
	{FMinVal: 100, FMaxVal: 0, Score: 10},
}

// 稳定项计分配置
var StabilityScoreConfig = []AbilityScoreConfig{
	{FMinVal: 0, FMaxVal: 10000, Score: 0},
	{FMinVal: 10000, FMaxVal: 50000, Score: 1},
	{FMinVal: 50000, FMaxVal: 100000, Score: 2},
	{FMinVal: 100000, FMaxVal: 150000, Score: 3},
	{FMinVal: 150000, FMaxVal: 200000, Score: 4},
	{FMinVal: 200000, FMaxVal: 250000, Score: 5},
	{FMinVal: 250000, FMaxVal: 350000, Score: 6},
	{FMinVal: 350000, FMaxVal: 500000, Score: 7},
	{FMinVal: 500000, FMaxVal: 1000000, Score: 8},
	{FMinVal: 1000000, FMaxVal: 2000000, Score: 9},
	{FMinVal: 2000000, FMaxVal: 0, Score: 10},
}

// 抗风险项计分配置
var RiskResistanceScoreConfig = []AbilityScoreConfig{
	{FMinVal: 0, FMaxVal: 0.1, Score: 9},
	{FMinVal: 0.1, FMaxVal: 0.2, Score: 8},
	{FMinVal: 0.2, FMaxVal: 0.3, Score: 7},
	{FMinVal: 0.3, FMaxVal: 0.4, Score: 6},
	{FMinVal: 0.4, FMaxVal: 0.5, Score: 5},
	{FMinVal: 0.5, FMaxVal: 0.6, Score: 4},
	{FMinVal: 0.6, FMaxVal: 0.7, Score: 3},
	{FMinVal: 0.7, FMaxVal: 0.8, Score: 2},
	{FMinVal: 0.8, FMaxVal: 0.9, Score: 1},
	{FMinVal: 0.9, FMaxVal: 1, Score: 0},
}
