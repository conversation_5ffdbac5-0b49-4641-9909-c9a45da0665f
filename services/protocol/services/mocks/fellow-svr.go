// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/protocol/services/fellow-svr (interfaces: FellowSvrClient)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	fellow_svr "golang.52tt.com/protocol/services/fellow-svr"
	reconcile_v2 "golang.52tt.com/protocol/services/reconcile-v2"
	cb "golang.52tt.com/protocol/services/unified_pay/cb"
	grpc "google.golang.org/grpc"
)

// MockFellowSvrClient is a mock of FellowSvrClient interface.
type MockFellowSvrClient struct {
	ctrl     *gomock.Controller
	recorder *MockFellowSvrClientMockRecorder
}

// MockFellowSvrClientMockRecorder is the mock recorder for MockFellowSvrClient.
type MockFellowSvrClientMockRecorder struct {
	mock *MockFellowSvrClient
}

// NewMockFellowSvrClient creates a new mock instance.
func NewMockFellowSvrClient(ctrl *gomock.Controller) *MockFellowSvrClient {
	mock := &MockFellowSvrClient{ctrl: ctrl}
	mock.recorder = &MockFellowSvrClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockFellowSvrClient) EXPECT() *MockFellowSvrClientMockRecorder {
	return m.recorder
}

// AddChannelRelationshipBinding mocks base method.
func (m *MockFellowSvrClient) AddChannelRelationshipBinding(arg0 context.Context, arg1 *fellow_svr.ChannelRelationshipBindingAddReq, arg2 ...grpc.CallOption) (*fellow_svr.ChannelRelationshipBindingAddResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AddChannelRelationshipBinding", varargs...)
	ret0, _ := ret[0].(*fellow_svr.ChannelRelationshipBindingAddResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddChannelRelationshipBinding indicates an expected call of AddChannelRelationshipBinding.
func (mr *MockFellowSvrClientMockRecorder) AddChannelRelationshipBinding(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddChannelRelationshipBinding", reflect.TypeOf((*MockFellowSvrClient)(nil).AddChannelRelationshipBinding), varargs...)
}

// AddFellowPointDelay mocks base method.
func (m *MockFellowSvrClient) AddFellowPointDelay(arg0 context.Context, arg1 *fellow_svr.AddFellowPointDelayReq, arg2 ...grpc.CallOption) (*fellow_svr.AddFellowPointDelayResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AddFellowPointDelay", varargs...)
	ret0, _ := ret[0].(*fellow_svr.AddFellowPointDelayResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddFellowPointDelay indicates an expected call of AddFellowPointDelay.
func (mr *MockFellowSvrClientMockRecorder) AddFellowPointDelay(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddFellowPointDelay", reflect.TypeOf((*MockFellowSvrClient)(nil).AddFellowPointDelay), varargs...)
}

// AddFellowPresentConfig mocks base method.
func (m *MockFellowSvrClient) AddFellowPresentConfig(arg0 context.Context, arg1 *fellow_svr.AddFellowPresentConfigReq, arg2 ...grpc.CallOption) (*fellow_svr.AddFellowPresentConfigResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AddFellowPresentConfig", varargs...)
	ret0, _ := ret[0].(*fellow_svr.AddFellowPresentConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddFellowPresentConfig indicates an expected call of AddFellowPresentConfig.
func (mr *MockFellowSvrClientMockRecorder) AddFellowPresentConfig(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddFellowPresentConfig", reflect.TypeOf((*MockFellowSvrClient)(nil).AddFellowPresentConfig), varargs...)
}

// AddRare mocks base method.
func (m *MockFellowSvrClient) AddRare(arg0 context.Context, arg1 *fellow_svr.AddRareReq, arg2 ...grpc.CallOption) (*fellow_svr.AddRareResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AddRare", varargs...)
	ret0, _ := ret[0].(*fellow_svr.AddRareResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddRare indicates an expected call of AddRare.
func (mr *MockFellowSvrClientMockRecorder) AddRare(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddRare", reflect.TypeOf((*MockFellowSvrClient)(nil).AddRare), varargs...)
}

// AddRelationship mocks base method.
func (m *MockFellowSvrClient) AddRelationship(arg0 context.Context, arg1 *fellow_svr.RelationshipAddReq, arg2 ...grpc.CallOption) (*fellow_svr.RelationshipAddResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AddRelationship", varargs...)
	ret0, _ := ret[0].(*fellow_svr.RelationshipAddResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddRelationship indicates an expected call of AddRelationship.
func (mr *MockFellowSvrClientMockRecorder) AddRelationship(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddRelationship", reflect.TypeOf((*MockFellowSvrClient)(nil).AddRelationship), varargs...)
}

// BatchGetFellowSimpleInfoByPairList mocks base method.
func (m *MockFellowSvrClient) BatchGetFellowSimpleInfoByPairList(arg0 context.Context, arg1 *fellow_svr.BatchGetFellowSimpleInfoByPairListReq, arg2 ...grpc.CallOption) (*fellow_svr.BatchGetFellowSimpleInfoByPairListResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchGetFellowSimpleInfoByPairList", varargs...)
	ret0, _ := ret[0].(*fellow_svr.BatchGetFellowSimpleInfoByPairListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetFellowSimpleInfoByPairList indicates an expected call of BatchGetFellowSimpleInfoByPairList.
func (mr *MockFellowSvrClientMockRecorder) BatchGetFellowSimpleInfoByPairList(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetFellowSimpleInfoByPairList", reflect.TypeOf((*MockFellowSvrClient)(nil).BatchGetFellowSimpleInfoByPairList), varargs...)
}

// CancelFellowInvite mocks base method.
func (m *MockFellowSvrClient) CancelFellowInvite(arg0 context.Context, arg1 *fellow_svr.CancelFellowInviteReq, arg2 ...grpc.CallOption) (*fellow_svr.CancelFellowInviteResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CancelFellowInvite", varargs...)
	ret0, _ := ret[0].(*fellow_svr.CancelFellowInviteResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CancelFellowInvite indicates an expected call of CancelFellowInvite.
func (mr *MockFellowSvrClientMockRecorder) CancelFellowInvite(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CancelFellowInvite", reflect.TypeOf((*MockFellowSvrClient)(nil).CancelFellowInvite), varargs...)
}

// CancelUnboundFellow mocks base method.
func (m *MockFellowSvrClient) CancelUnboundFellow(arg0 context.Context, arg1 *fellow_svr.CancelUnboundFellowReq, arg2 ...grpc.CallOption) (*fellow_svr.CancelUnboundFellowResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CancelUnboundFellow", varargs...)
	ret0, _ := ret[0].(*fellow_svr.CancelUnboundFellowResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CancelUnboundFellow indicates an expected call of CancelUnboundFellow.
func (mr *MockFellowSvrClientMockRecorder) CancelUnboundFellow(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CancelUnboundFellow", reflect.TypeOf((*MockFellowSvrClient)(nil).CancelUnboundFellow), varargs...)
}

// ChangeFellowBindType mocks base method.
func (m *MockFellowSvrClient) ChangeFellowBindType(arg0 context.Context, arg1 *fellow_svr.ChangeFellowBindTypeReq, arg2 ...grpc.CallOption) (*fellow_svr.ChangeFellowBindTypeResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ChangeFellowBindType", varargs...)
	ret0, _ := ret[0].(*fellow_svr.ChangeFellowBindTypeResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ChangeFellowBindType indicates an expected call of ChangeFellowBindType.
func (mr *MockFellowSvrClientMockRecorder) ChangeFellowBindType(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ChangeFellowBindType", reflect.TypeOf((*MockFellowSvrClient)(nil).ChangeFellowBindType), varargs...)
}

// ChannelSendFellowPresent mocks base method.
func (m *MockFellowSvrClient) ChannelSendFellowPresent(arg0 context.Context, arg1 *fellow_svr.ChannelSendFellowPresentReq, arg2 ...grpc.CallOption) (*fellow_svr.ChannelSendFellowPresentResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ChannelSendFellowPresent", varargs...)
	ret0, _ := ret[0].(*fellow_svr.ChannelSendFellowPresentResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ChannelSendFellowPresent indicates an expected call of ChannelSendFellowPresent.
func (mr *MockFellowSvrClientMockRecorder) ChannelSendFellowPresent(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ChannelSendFellowPresent", reflect.TypeOf((*MockFellowSvrClient)(nil).ChannelSendFellowPresent), varargs...)
}

// CheckFellowInvite mocks base method.
func (m *MockFellowSvrClient) CheckFellowInvite(arg0 context.Context, arg1 *fellow_svr.CheckFellowInviteReq, arg2 ...grpc.CallOption) (*fellow_svr.CheckFellowInviteResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CheckFellowInvite", varargs...)
	ret0, _ := ret[0].(*fellow_svr.CheckFellowInviteResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckFellowInvite indicates an expected call of CheckFellowInvite.
func (mr *MockFellowSvrClientMockRecorder) CheckFellowInvite(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckFellowInvite", reflect.TypeOf((*MockFellowSvrClient)(nil).CheckFellowInvite), varargs...)
}

// CntPresentEvLogTotalCount mocks base method.
func (m *MockFellowSvrClient) CntPresentEvLogTotalCount(arg0 context.Context, arg1 *reconcile_v2.TimeRangeReq, arg2 ...grpc.CallOption) (*reconcile_v2.CountResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CntPresentEvLogTotalCount", varargs...)
	ret0, _ := ret[0].(*reconcile_v2.CountResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CntPresentEvLogTotalCount indicates an expected call of CntPresentEvLogTotalCount.
func (mr *MockFellowSvrClientMockRecorder) CntPresentEvLogTotalCount(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CntPresentEvLogTotalCount", reflect.TypeOf((*MockFellowSvrClient)(nil).CntPresentEvLogTotalCount), varargs...)
}

// DelChannelRelationshipBinding mocks base method.
func (m *MockFellowSvrClient) DelChannelRelationshipBinding(arg0 context.Context, arg1 *fellow_svr.ChannelRelationshipBindingDeleteReq, arg2 ...grpc.CallOption) (*fellow_svr.ChannelRelationshipBindingDeleteResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DelChannelRelationshipBinding", varargs...)
	ret0, _ := ret[0].(*fellow_svr.ChannelRelationshipBindingDeleteResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DelChannelRelationshipBinding indicates an expected call of DelChannelRelationshipBinding.
func (mr *MockFellowSvrClientMockRecorder) DelChannelRelationshipBinding(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelChannelRelationshipBinding", reflect.TypeOf((*MockFellowSvrClient)(nil).DelChannelRelationshipBinding), varargs...)
}

// DelFellowPresentConfig mocks base method.
func (m *MockFellowSvrClient) DelFellowPresentConfig(arg0 context.Context, arg1 *fellow_svr.DelFellowPresentConfigReq, arg2 ...grpc.CallOption) (*fellow_svr.DelFellowPresentConfigResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DelFellowPresentConfig", varargs...)
	ret0, _ := ret[0].(*fellow_svr.DelFellowPresentConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DelFellowPresentConfig indicates an expected call of DelFellowPresentConfig.
func (mr *MockFellowSvrClientMockRecorder) DelFellowPresentConfig(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelFellowPresentConfig", reflect.TypeOf((*MockFellowSvrClient)(nil).DelFellowPresentConfig), varargs...)
}

// DelRare mocks base method.
func (m *MockFellowSvrClient) DelRare(arg0 context.Context, arg1 *fellow_svr.DelRareReq, arg2 ...grpc.CallOption) (*fellow_svr.DelRareResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DelRare", varargs...)
	ret0, _ := ret[0].(*fellow_svr.DelRareResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DelRare indicates an expected call of DelRare.
func (mr *MockFellowSvrClientMockRecorder) DelRare(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelRare", reflect.TypeOf((*MockFellowSvrClient)(nil).DelRare), varargs...)
}

// DelRelationship mocks base method.
func (m *MockFellowSvrClient) DelRelationship(arg0 context.Context, arg1 *fellow_svr.RelationshipDeleteReq, arg2 ...grpc.CallOption) (*fellow_svr.RelationshipDeleteResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DelRelationship", varargs...)
	ret0, _ := ret[0].(*fellow_svr.RelationshipDeleteResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DelRelationship indicates an expected call of DelRelationship.
func (mr *MockFellowSvrClientMockRecorder) DelRelationship(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelRelationship", reflect.TypeOf((*MockFellowSvrClient)(nil).DelRelationship), varargs...)
}

// DirectUnboundFellow mocks base method.
func (m *MockFellowSvrClient) DirectUnboundFellow(arg0 context.Context, arg1 *fellow_svr.DirectUnboundFellowReq, arg2 ...grpc.CallOption) (*fellow_svr.DirectUnboundFellowResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DirectUnboundFellow", varargs...)
	ret0, _ := ret[0].(*fellow_svr.DirectUnboundFellowResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DirectUnboundFellow indicates an expected call of DirectUnboundFellow.
func (mr *MockFellowSvrClientMockRecorder) DirectUnboundFellow(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DirectUnboundFellow", reflect.TypeOf((*MockFellowSvrClient)(nil).DirectUnboundFellow), varargs...)
}

// FixFellowOrder mocks base method.
func (m *MockFellowSvrClient) FixFellowOrder(arg0 context.Context, arg1 *reconcile_v2.ReplaceOrderReq, arg2 ...grpc.CallOption) (*reconcile_v2.EmptyResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "FixFellowOrder", varargs...)
	ret0, _ := ret[0].(*reconcile_v2.EmptyResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FixFellowOrder indicates an expected call of FixFellowOrder.
func (mr *MockFellowSvrClientMockRecorder) FixFellowOrder(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FixFellowOrder", reflect.TypeOf((*MockFellowSvrClient)(nil).FixFellowOrder), varargs...)
}

// FixPresentEvLogOrder mocks base method.
func (m *MockFellowSvrClient) FixPresentEvLogOrder(arg0 context.Context, arg1 *reconcile_v2.ReplaceOrderReq, arg2 ...grpc.CallOption) (*reconcile_v2.EmptyResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "FixPresentEvLogOrder", varargs...)
	ret0, _ := ret[0].(*reconcile_v2.EmptyResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FixPresentEvLogOrder indicates an expected call of FixPresentEvLogOrder.
func (mr *MockFellowSvrClientMockRecorder) FixPresentEvLogOrder(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FixPresentEvLogOrder", reflect.TypeOf((*MockFellowSvrClient)(nil).FixPresentEvLogOrder), varargs...)
}

// GetAllChannelFellowInvite mocks base method.
func (m *MockFellowSvrClient) GetAllChannelFellowInvite(arg0 context.Context, arg1 *fellow_svr.GetAllChannelFellowInviteReq, arg2 ...grpc.CallOption) (*fellow_svr.GetAllChannelFellowInviteResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetAllChannelFellowInvite", varargs...)
	ret0, _ := ret[0].(*fellow_svr.GetAllChannelFellowInviteResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllChannelFellowInvite indicates an expected call of GetAllChannelFellowInvite.
func (mr *MockFellowSvrClientMockRecorder) GetAllChannelFellowInvite(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllChannelFellowInvite", reflect.TypeOf((*MockFellowSvrClient)(nil).GetAllChannelFellowInvite), varargs...)
}

// GetAllFellowPresentConfig mocks base method.
func (m *MockFellowSvrClient) GetAllFellowPresentConfig(arg0 context.Context, arg1 *fellow_svr.GetAllFellowPresentConfigReq, arg2 ...grpc.CallOption) (*fellow_svr.GetAllFellowPresentConfigResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetAllFellowPresentConfig", varargs...)
	ret0, _ := ret[0].(*fellow_svr.GetAllFellowPresentConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllFellowPresentConfig indicates an expected call of GetAllFellowPresentConfig.
func (mr *MockFellowSvrClientMockRecorder) GetAllFellowPresentConfig(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllFellowPresentConfig", reflect.TypeOf((*MockFellowSvrClient)(nil).GetAllFellowPresentConfig), varargs...)
}

// GetChannelFellowCandidateInfo mocks base method.
func (m *MockFellowSvrClient) GetChannelFellowCandidateInfo(arg0 context.Context, arg1 *fellow_svr.GetChannelFellowCandidateInfoReq, arg2 ...grpc.CallOption) (*fellow_svr.GetChannelFellowCandidateInfoResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetChannelFellowCandidateInfo", varargs...)
	ret0, _ := ret[0].(*fellow_svr.GetChannelFellowCandidateInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChannelFellowCandidateInfo indicates an expected call of GetChannelFellowCandidateInfo.
func (mr *MockFellowSvrClientMockRecorder) GetChannelFellowCandidateInfo(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChannelFellowCandidateInfo", reflect.TypeOf((*MockFellowSvrClient)(nil).GetChannelFellowCandidateInfo), varargs...)
}

// GetChannelRareConfig mocks base method.
func (m *MockFellowSvrClient) GetChannelRareConfig(arg0 context.Context, arg1 *fellow_svr.GetChannelRareConfigReq, arg2 ...grpc.CallOption) (*fellow_svr.GetChannelRareConfigResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetChannelRareConfig", varargs...)
	ret0, _ := ret[0].(*fellow_svr.GetChannelRareConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChannelRareConfig indicates an expected call of GetChannelRareConfig.
func (mr *MockFellowSvrClientMockRecorder) GetChannelRareConfig(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChannelRareConfig", reflect.TypeOf((*MockFellowSvrClient)(nil).GetChannelRareConfig), varargs...)
}

// GetChannelRelationshipBindingList mocks base method.
func (m *MockFellowSvrClient) GetChannelRelationshipBindingList(arg0 context.Context, arg1 *fellow_svr.ChannelRelationshipBindingListReq, arg2 ...grpc.CallOption) (*fellow_svr.ChannelRelationshipBindingListResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetChannelRelationshipBindingList", varargs...)
	ret0, _ := ret[0].(*fellow_svr.ChannelRelationshipBindingListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChannelRelationshipBindingList indicates an expected call of GetChannelRelationshipBindingList.
func (mr *MockFellowSvrClientMockRecorder) GetChannelRelationshipBindingList(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChannelRelationshipBindingList", reflect.TypeOf((*MockFellowSvrClient)(nil).GetChannelRelationshipBindingList), varargs...)
}

// GetFellowCandidateInfo mocks base method.
func (m *MockFellowSvrClient) GetFellowCandidateInfo(arg0 context.Context, arg1 *fellow_svr.GetFellowCandidateInfoReq, arg2 ...grpc.CallOption) (*fellow_svr.GetFellowCandidateInfoResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetFellowCandidateInfo", varargs...)
	ret0, _ := ret[0].(*fellow_svr.GetFellowCandidateInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetFellowCandidateInfo indicates an expected call of GetFellowCandidateInfo.
func (mr *MockFellowSvrClientMockRecorder) GetFellowCandidateInfo(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFellowCandidateInfo", reflect.TypeOf((*MockFellowSvrClient)(nil).GetFellowCandidateInfo), varargs...)
}

// GetFellowCandidateList mocks base method.
func (m *MockFellowSvrClient) GetFellowCandidateList(arg0 context.Context, arg1 *fellow_svr.GetFellowCandidateListReq, arg2 ...grpc.CallOption) (*fellow_svr.GetFellowCandidateListResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetFellowCandidateList", varargs...)
	ret0, _ := ret[0].(*fellow_svr.GetFellowCandidateListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetFellowCandidateList indicates an expected call of GetFellowCandidateList.
func (mr *MockFellowSvrClientMockRecorder) GetFellowCandidateList(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFellowCandidateList", reflect.TypeOf((*MockFellowSvrClient)(nil).GetFellowCandidateList), varargs...)
}

// GetFellowInfoByUid mocks base method.
func (m *MockFellowSvrClient) GetFellowInfoByUid(arg0 context.Context, arg1 *fellow_svr.GetFellowInfoByUidReq, arg2 ...grpc.CallOption) (*fellow_svr.GetFellowInfoByUidResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetFellowInfoByUid", varargs...)
	ret0, _ := ret[0].(*fellow_svr.GetFellowInfoByUidResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetFellowInfoByUid indicates an expected call of GetFellowInfoByUid.
func (mr *MockFellowSvrClientMockRecorder) GetFellowInfoByUid(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFellowInfoByUid", reflect.TypeOf((*MockFellowSvrClient)(nil).GetFellowInfoByUid), varargs...)
}

// GetFellowInviteInfoById mocks base method.
func (m *MockFellowSvrClient) GetFellowInviteInfoById(arg0 context.Context, arg1 *fellow_svr.GetFellowInviteInfoByIdReq, arg2 ...grpc.CallOption) (*fellow_svr.GetFellowInviteInfoByIdResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetFellowInviteInfoById", varargs...)
	ret0, _ := ret[0].(*fellow_svr.GetFellowInviteInfoByIdResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetFellowInviteInfoById indicates an expected call of GetFellowInviteInfoById.
func (mr *MockFellowSvrClientMockRecorder) GetFellowInviteInfoById(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFellowInviteInfoById", reflect.TypeOf((*MockFellowSvrClient)(nil).GetFellowInviteInfoById), varargs...)
}

// GetFellowInviteList mocks base method.
func (m *MockFellowSvrClient) GetFellowInviteList(arg0 context.Context, arg1 *fellow_svr.GetFellowInviteListReq, arg2 ...grpc.CallOption) (*fellow_svr.GetFellowInviteListResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetFellowInviteList", varargs...)
	ret0, _ := ret[0].(*fellow_svr.GetFellowInviteListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetFellowInviteList indicates an expected call of GetFellowInviteList.
func (mr *MockFellowSvrClientMockRecorder) GetFellowInviteList(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFellowInviteList", reflect.TypeOf((*MockFellowSvrClient)(nil).GetFellowInviteList), varargs...)
}

// GetFellowList mocks base method.
func (m *MockFellowSvrClient) GetFellowList(arg0 context.Context, arg1 *fellow_svr.GetFellowListReq, arg2 ...grpc.CallOption) (*fellow_svr.GetFellowListResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetFellowList", varargs...)
	ret0, _ := ret[0].(*fellow_svr.GetFellowListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetFellowList indicates an expected call of GetFellowList.
func (mr *MockFellowSvrClientMockRecorder) GetFellowList(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFellowList", reflect.TypeOf((*MockFellowSvrClient)(nil).GetFellowList), varargs...)
}

// GetFellowPoint mocks base method.
func (m *MockFellowSvrClient) GetFellowPoint(arg0 context.Context, arg1 *fellow_svr.GetFellowPointReq, arg2 ...grpc.CallOption) (*fellow_svr.GetFellowPointResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetFellowPoint", varargs...)
	ret0, _ := ret[0].(*fellow_svr.GetFellowPointResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetFellowPoint indicates an expected call of GetFellowPoint.
func (mr *MockFellowSvrClientMockRecorder) GetFellowPoint(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFellowPoint", reflect.TypeOf((*MockFellowSvrClient)(nil).GetFellowPoint), varargs...)
}

// GetFellowPresentConfigById mocks base method.
func (m *MockFellowSvrClient) GetFellowPresentConfigById(arg0 context.Context, arg1 *fellow_svr.GetFellowPresentConfigByIdReq, arg2 ...grpc.CallOption) (*fellow_svr.GetFellowPresentConfigByIdResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetFellowPresentConfigById", varargs...)
	ret0, _ := ret[0].(*fellow_svr.GetFellowPresentConfigByIdResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetFellowPresentConfigById indicates an expected call of GetFellowPresentConfigById.
func (mr *MockFellowSvrClientMockRecorder) GetFellowPresentConfigById(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFellowPresentConfigById", reflect.TypeOf((*MockFellowSvrClient)(nil).GetFellowPresentConfigById), varargs...)
}

// GetFellowPresentDetail mocks base method.
func (m *MockFellowSvrClient) GetFellowPresentDetail(arg0 context.Context, arg1 *fellow_svr.GetFellowPresentDetailReq, arg2 ...grpc.CallOption) (*fellow_svr.GetFellowPresentDetailResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetFellowPresentDetail", varargs...)
	ret0, _ := ret[0].(*fellow_svr.GetFellowPresentDetailResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetFellowPresentDetail indicates an expected call of GetFellowPresentDetail.
func (mr *MockFellowSvrClientMockRecorder) GetFellowPresentDetail(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFellowPresentDetail", reflect.TypeOf((*MockFellowSvrClient)(nil).GetFellowPresentDetail), varargs...)
}

// GetFellowSimpleInfo mocks base method.
func (m *MockFellowSvrClient) GetFellowSimpleInfo(arg0 context.Context, arg1 *fellow_svr.GetFellowSimpleInfoReq, arg2 ...grpc.CallOption) (*fellow_svr.GetFellowSimpleInfoResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetFellowSimpleInfo", varargs...)
	ret0, _ := ret[0].(*fellow_svr.GetFellowSimpleInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetFellowSimpleInfo indicates an expected call of GetFellowSimpleInfo.
func (mr *MockFellowSvrClientMockRecorder) GetFellowSimpleInfo(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFellowSimpleInfo", reflect.TypeOf((*MockFellowSvrClient)(nil).GetFellowSimpleInfo), varargs...)
}

// GetFellowType mocks base method.
func (m *MockFellowSvrClient) GetFellowType(arg0 context.Context, arg1 *fellow_svr.GetFellowTypeReq, arg2 ...grpc.CallOption) (*fellow_svr.GetFellowTypeResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetFellowType", varargs...)
	ret0, _ := ret[0].(*fellow_svr.GetFellowTypeResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetFellowType indicates an expected call of GetFellowType.
func (mr *MockFellowSvrClientMockRecorder) GetFellowType(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFellowType", reflect.TypeOf((*MockFellowSvrClient)(nil).GetFellowType), varargs...)
}

// GetFromAllRelationshipByIds mocks base method.
func (m *MockFellowSvrClient) GetFromAllRelationshipByIds(arg0 context.Context, arg1 *fellow_svr.GetFromAllRelationshipByIdsReq, arg2 ...grpc.CallOption) (*fellow_svr.GetFromAllRelationshipByIdsResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetFromAllRelationshipByIds", varargs...)
	ret0, _ := ret[0].(*fellow_svr.GetFromAllRelationshipByIdsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetFromAllRelationshipByIds indicates an expected call of GetFromAllRelationshipByIds.
func (mr *MockFellowSvrClientMockRecorder) GetFromAllRelationshipByIds(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFromAllRelationshipByIds", reflect.TypeOf((*MockFellowSvrClient)(nil).GetFromAllRelationshipByIds), varargs...)
}

// GetHistoryFellowType mocks base method.
func (m *MockFellowSvrClient) GetHistoryFellowType(arg0 context.Context, arg1 *fellow_svr.GetHistoryFellowTypeReq, arg2 ...grpc.CallOption) (*fellow_svr.GetHistoryFellowTypeResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetHistoryFellowType", varargs...)
	ret0, _ := ret[0].(*fellow_svr.GetHistoryFellowTypeResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetHistoryFellowType indicates an expected call of GetHistoryFellowType.
func (mr *MockFellowSvrClientMockRecorder) GetHistoryFellowType(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetHistoryFellowType", reflect.TypeOf((*MockFellowSvrClient)(nil).GetHistoryFellowType), varargs...)
}

// GetOnMicFellowList mocks base method.
func (m *MockFellowSvrClient) GetOnMicFellowList(arg0 context.Context, arg1 *fellow_svr.GetOnMicFellowListReq, arg2 ...grpc.CallOption) (*fellow_svr.GetOnMicFellowListResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetOnMicFellowList", varargs...)
	ret0, _ := ret[0].(*fellow_svr.GetOnMicFellowListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOnMicFellowList indicates an expected call of GetOnMicFellowList.
func (mr *MockFellowSvrClientMockRecorder) GetOnMicFellowList(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOnMicFellowList", reflect.TypeOf((*MockFellowSvrClient)(nil).GetOnMicFellowList), varargs...)
}

// GetPresentEvLogOrderIds mocks base method.
func (m *MockFellowSvrClient) GetPresentEvLogOrderIds(arg0 context.Context, arg1 *reconcile_v2.TimeRangeReq, arg2 ...grpc.CallOption) (*reconcile_v2.OrderIdsResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetPresentEvLogOrderIds", varargs...)
	ret0, _ := ret[0].(*reconcile_v2.OrderIdsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPresentEvLogOrderIds indicates an expected call of GetPresentEvLogOrderIds.
func (mr *MockFellowSvrClientMockRecorder) GetPresentEvLogOrderIds(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPresentEvLogOrderIds", reflect.TypeOf((*MockFellowSvrClient)(nil).GetPresentEvLogOrderIds), varargs...)
}

// GetRareConfig mocks base method.
func (m *MockFellowSvrClient) GetRareConfig(arg0 context.Context, arg1 *fellow_svr.GetRareConfigReq, arg2 ...grpc.CallOption) (*fellow_svr.GetRareConfigResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetRareConfig", varargs...)
	ret0, _ := ret[0].(*fellow_svr.GetRareConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRareConfig indicates an expected call of GetRareConfig.
func (mr *MockFellowSvrClientMockRecorder) GetRareConfig(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRareConfig", reflect.TypeOf((*MockFellowSvrClient)(nil).GetRareConfig), varargs...)
}

// GetRareList mocks base method.
func (m *MockFellowSvrClient) GetRareList(arg0 context.Context, arg1 *fellow_svr.GetRareListReq, arg2 ...grpc.CallOption) (*fellow_svr.GetRareListResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetRareList", varargs...)
	ret0, _ := ret[0].(*fellow_svr.GetRareListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRareList indicates an expected call of GetRareList.
func (mr *MockFellowSvrClientMockRecorder) GetRareList(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRareList", reflect.TypeOf((*MockFellowSvrClient)(nil).GetRareList), varargs...)
}

// GetRareTags mocks base method.
func (m *MockFellowSvrClient) GetRareTags(arg0 context.Context, arg1 *fellow_svr.GetRareTagsReq, arg2 ...grpc.CallOption) (*fellow_svr.GetRareTagsResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetRareTags", varargs...)
	ret0, _ := ret[0].(*fellow_svr.GetRareTagsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRareTags indicates an expected call of GetRareTags.
func (mr *MockFellowSvrClientMockRecorder) GetRareTags(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRareTags", reflect.TypeOf((*MockFellowSvrClient)(nil).GetRareTags), varargs...)
}

// GetRelationship mocks base method.
func (m *MockFellowSvrClient) GetRelationship(arg0 context.Context, arg1 *fellow_svr.RelationshipGetReq, arg2 ...grpc.CallOption) (*fellow_svr.RelationshipGetResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetRelationship", varargs...)
	ret0, _ := ret[0].(*fellow_svr.RelationshipGetResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRelationship indicates an expected call of GetRelationship.
func (mr *MockFellowSvrClientMockRecorder) GetRelationship(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRelationship", reflect.TypeOf((*MockFellowSvrClient)(nil).GetRelationship), varargs...)
}

// GetRelationshipList mocks base method.
func (m *MockFellowSvrClient) GetRelationshipList(arg0 context.Context, arg1 *fellow_svr.RelationshipListReq, arg2 ...grpc.CallOption) (*fellow_svr.RelationshipListResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetRelationshipList", varargs...)
	ret0, _ := ret[0].(*fellow_svr.RelationshipListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRelationshipList indicates an expected call of GetRelationshipList.
func (mr *MockFellowSvrClientMockRecorder) GetRelationshipList(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRelationshipList", reflect.TypeOf((*MockFellowSvrClient)(nil).GetRelationshipList), varargs...)
}

// GetRoomFellowList mocks base method.
func (m *MockFellowSvrClient) GetRoomFellowList(arg0 context.Context, arg1 *fellow_svr.GetRoomFellowListReq, arg2 ...grpc.CallOption) (*fellow_svr.GetRoomFellowListResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetRoomFellowList", varargs...)
	ret0, _ := ret[0].(*fellow_svr.GetRoomFellowListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRoomFellowList indicates an expected call of GetRoomFellowList.
func (mr *MockFellowSvrClientMockRecorder) GetRoomFellowList(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRoomFellowList", reflect.TypeOf((*MockFellowSvrClient)(nil).GetRoomFellowList), varargs...)
}

// GetSendInviteList mocks base method.
func (m *MockFellowSvrClient) GetSendInviteList(arg0 context.Context, arg1 *fellow_svr.GetSendInviteListReq, arg2 ...grpc.CallOption) (*fellow_svr.GetSendInviteListResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetSendInviteList", varargs...)
	ret0, _ := ret[0].(*fellow_svr.GetSendInviteListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSendInviteList indicates an expected call of GetSendInviteList.
func (mr *MockFellowSvrClientMockRecorder) GetSendInviteList(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSendInviteList", reflect.TypeOf((*MockFellowSvrClient)(nil).GetSendInviteList), varargs...)
}

// GetWebFellowInfo mocks base method.
func (m *MockFellowSvrClient) GetWebFellowInfo(arg0 context.Context, arg1 *fellow_svr.GetWebFellowListReq, arg2 ...grpc.CallOption) (*fellow_svr.GetWebFellowListResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetWebFellowInfo", varargs...)
	ret0, _ := ret[0].(*fellow_svr.GetWebFellowListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetWebFellowInfo indicates an expected call of GetWebFellowInfo.
func (mr *MockFellowSvrClientMockRecorder) GetWebFellowInfo(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWebFellowInfo", reflect.TypeOf((*MockFellowSvrClient)(nil).GetWebFellowInfo), varargs...)
}

// HandleChannelFellowInvite mocks base method.
func (m *MockFellowSvrClient) HandleChannelFellowInvite(arg0 context.Context, arg1 *fellow_svr.HandleChannelFellowInviteReq, arg2 ...grpc.CallOption) (*fellow_svr.HandleChannelFellowInviteResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "HandleChannelFellowInvite", varargs...)
	ret0, _ := ret[0].(*fellow_svr.HandleChannelFellowInviteResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// HandleChannelFellowInvite indicates an expected call of HandleChannelFellowInvite.
func (mr *MockFellowSvrClientMockRecorder) HandleChannelFellowInvite(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HandleChannelFellowInvite", reflect.TypeOf((*MockFellowSvrClient)(nil).HandleChannelFellowInvite), varargs...)
}

// HandleFellowInvite mocks base method.
func (m *MockFellowSvrClient) HandleFellowInvite(arg0 context.Context, arg1 *fellow_svr.HandleFellowInviteReq, arg2 ...grpc.CallOption) (*fellow_svr.HandleFellowInviteResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "HandleFellowInvite", varargs...)
	ret0, _ := ret[0].(*fellow_svr.HandleFellowInviteResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// HandleFellowInvite indicates an expected call of HandleFellowInvite.
func (mr *MockFellowSvrClientMockRecorder) HandleFellowInvite(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HandleFellowInvite", reflect.TypeOf((*MockFellowSvrClient)(nil).HandleFellowInvite), varargs...)
}

// Notify mocks base method.
func (m *MockFellowSvrClient) Notify(arg0 context.Context, arg1 *cb.PayNotify, arg2 ...grpc.CallOption) (*cb.PayNotifyResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "Notify", varargs...)
	ret0, _ := ret[0].(*cb.PayNotifyResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Notify indicates an expected call of Notify.
func (mr *MockFellowSvrClientMockRecorder) Notify(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Notify", reflect.TypeOf((*MockFellowSvrClient)(nil).Notify), varargs...)
}

// SendChannelFellowInvite mocks base method.
func (m *MockFellowSvrClient) SendChannelFellowInvite(arg0 context.Context, arg1 *fellow_svr.SendChannelFellowInviteReq, arg2 ...grpc.CallOption) (*fellow_svr.SendChannelFellowInviteResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SendChannelFellowInvite", varargs...)
	ret0, _ := ret[0].(*fellow_svr.SendChannelFellowInviteResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SendChannelFellowInvite indicates an expected call of SendChannelFellowInvite.
func (mr *MockFellowSvrClientMockRecorder) SendChannelFellowInvite(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendChannelFellowInvite", reflect.TypeOf((*MockFellowSvrClient)(nil).SendChannelFellowInvite), varargs...)
}

// SendFellowInvite mocks base method.
func (m *MockFellowSvrClient) SendFellowInvite(arg0 context.Context, arg1 *fellow_svr.SendFellowInviteReq, arg2 ...grpc.CallOption) (*fellow_svr.SendFellowInviteResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SendFellowInvite", varargs...)
	ret0, _ := ret[0].(*fellow_svr.SendFellowInviteResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SendFellowInvite indicates an expected call of SendFellowInvite.
func (mr *MockFellowSvrClientMockRecorder) SendFellowInvite(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendFellowInvite", reflect.TypeOf((*MockFellowSvrClient)(nil).SendFellowInvite), varargs...)
}

// SendFellowPresent mocks base method.
func (m *MockFellowSvrClient) SendFellowPresent(arg0 context.Context, arg1 *fellow_svr.SendFellowPresentReq, arg2 ...grpc.CallOption) (*fellow_svr.SendFellowPresentResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SendFellowPresent", varargs...)
	ret0, _ := ret[0].(*fellow_svr.SendFellowPresentResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SendFellowPresent indicates an expected call of SendFellowPresent.
func (mr *MockFellowSvrClientMockRecorder) SendFellowPresent(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendFellowPresent", reflect.TypeOf((*MockFellowSvrClient)(nil).SendFellowPresent), varargs...)
}

// SetBindRelation mocks base method.
func (m *MockFellowSvrClient) SetBindRelation(arg0 context.Context, arg1 *fellow_svr.SetBindRelationReq, arg2 ...grpc.CallOption) (*fellow_svr.SetBindRelationResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SetBindRelation", varargs...)
	ret0, _ := ret[0].(*fellow_svr.SetBindRelationResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetBindRelation indicates an expected call of SetBindRelation.
func (mr *MockFellowSvrClientMockRecorder) SetBindRelation(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetBindRelation", reflect.TypeOf((*MockFellowSvrClient)(nil).SetBindRelation), varargs...)
}

// TestSetFellowLevel mocks base method.
func (m *MockFellowSvrClient) TestSetFellowLevel(arg0 context.Context, arg1 *fellow_svr.TestSetFellowLevelReq, arg2 ...grpc.CallOption) (*fellow_svr.TestSetFellowLevelResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "TestSetFellowLevel", varargs...)
	ret0, _ := ret[0].(*fellow_svr.TestSetFellowLevelResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// TestSetFellowLevel indicates an expected call of TestSetFellowLevel.
func (mr *MockFellowSvrClientMockRecorder) TestSetFellowLevel(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TestSetFellowLevel", reflect.TypeOf((*MockFellowSvrClient)(nil).TestSetFellowLevel), varargs...)
}

// TestUpgradeImMsg mocks base method.
func (m *MockFellowSvrClient) TestUpgradeImMsg(arg0 context.Context, arg1 *fellow_svr.TestUpgradeImMsgReq, arg2 ...grpc.CallOption) (*fellow_svr.TestUpgradeImMsgResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "TestUpgradeImMsg", varargs...)
	ret0, _ := ret[0].(*fellow_svr.TestUpgradeImMsgResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// TestUpgradeImMsg indicates an expected call of TestUpgradeImMsg.
func (mr *MockFellowSvrClientMockRecorder) TestUpgradeImMsg(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TestUpgradeImMsg", reflect.TypeOf((*MockFellowSvrClient)(nil).TestUpgradeImMsg), varargs...)
}

// UnboundFellow mocks base method.
func (m *MockFellowSvrClient) UnboundFellow(arg0 context.Context, arg1 *fellow_svr.UnboundFellowReq, arg2 ...grpc.CallOption) (*fellow_svr.UnboundFellowResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UnboundFellow", varargs...)
	ret0, _ := ret[0].(*fellow_svr.UnboundFellowResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UnboundFellow indicates an expected call of UnboundFellow.
func (mr *MockFellowSvrClientMockRecorder) UnboundFellow(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UnboundFellow", reflect.TypeOf((*MockFellowSvrClient)(nil).UnboundFellow), varargs...)
}

// UnlockFellowSite mocks base method.
func (m *MockFellowSvrClient) UnlockFellowSite(arg0 context.Context, arg1 *fellow_svr.UnlockFellowSiteReq, arg2 ...grpc.CallOption) (*fellow_svr.UnlockFellowSiteResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UnlockFellowSite", varargs...)
	ret0, _ := ret[0].(*fellow_svr.UnlockFellowSiteResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UnlockFellowSite indicates an expected call of UnlockFellowSite.
func (mr *MockFellowSvrClientMockRecorder) UnlockFellowSite(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UnlockFellowSite", reflect.TypeOf((*MockFellowSvrClient)(nil).UnlockFellowSite), varargs...)
}

// UpdateChannelRelationshipBinding mocks base method.
func (m *MockFellowSvrClient) UpdateChannelRelationshipBinding(arg0 context.Context, arg1 *fellow_svr.ChannelRelationshipBindingUpdateReq, arg2 ...grpc.CallOption) (*fellow_svr.ChannelRelationshipBindingUpdateResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateChannelRelationshipBinding", varargs...)
	ret0, _ := ret[0].(*fellow_svr.ChannelRelationshipBindingUpdateResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateChannelRelationshipBinding indicates an expected call of UpdateChannelRelationshipBinding.
func (mr *MockFellowSvrClientMockRecorder) UpdateChannelRelationshipBinding(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateChannelRelationshipBinding", reflect.TypeOf((*MockFellowSvrClient)(nil).UpdateChannelRelationshipBinding), varargs...)
}

// UpdateFellowPresentConfig mocks base method.
func (m *MockFellowSvrClient) UpdateFellowPresentConfig(arg0 context.Context, arg1 *fellow_svr.UpdateFellowPresentConfigReq, arg2 ...grpc.CallOption) (*fellow_svr.UpdateFellowPresentConfigResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateFellowPresentConfig", varargs...)
	ret0, _ := ret[0].(*fellow_svr.UpdateFellowPresentConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateFellowPresentConfig indicates an expected call of UpdateFellowPresentConfig.
func (mr *MockFellowSvrClientMockRecorder) UpdateFellowPresentConfig(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateFellowPresentConfig", reflect.TypeOf((*MockFellowSvrClient)(nil).UpdateFellowPresentConfig), varargs...)
}

// UpdateRelationship mocks base method.
func (m *MockFellowSvrClient) UpdateRelationship(arg0 context.Context, arg1 *fellow_svr.RelationshipUpdateReq, arg2 ...grpc.CallOption) (*fellow_svr.RelationshipUpdateResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateRelationship", varargs...)
	ret0, _ := ret[0].(*fellow_svr.RelationshipUpdateResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateRelationship indicates an expected call of UpdateRelationship.
func (mr *MockFellowSvrClientMockRecorder) UpdateRelationship(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateRelationship", reflect.TypeOf((*MockFellowSvrClient)(nil).UpdateRelationship), varargs...)
}
