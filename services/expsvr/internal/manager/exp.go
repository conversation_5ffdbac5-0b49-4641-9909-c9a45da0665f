package manager

import (
	"context"
	"encoding/binary"
	"errors"
	"github.com/jinzhu/gorm"
	"gitlab.ttyuyin.com/bizFund/bizFund/pkg/seqgen"
	pushclient "golang.52tt.com/clients/push-notification/v2"
	"golang.52tt.com/pkg/datacenter"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/protocol/grpc"
	"golang.52tt.com/protocol/app/sync"
	"golang.52tt.com/protocol/common/status"
	pb "golang.52tt.com/protocol/services/expsvr"
	kafkaUserInfo "golang.52tt.com/protocol/services/minToolkit/kafka/pb/kafkauserinfo"
	tlsvr "golang.52tt.com/protocol/services/missiontimelinesvr"
	pushPB "golang.52tt.com/protocol/services/push-notification/v2"
	"golang.52tt.com/services/expsvr/internal/cache"
	"golang.52tt.com/services/expsvr/internal/conf"
	"golang.52tt.com/services/expsvr/internal/model"
	"golang.52tt.com/services/expsvr/internal/utils"
	"google.golang.org/grpc/codes"
	"sync/atomic"
	"time"
)

// GetUserExp 获取用户经验
func (m *Manager) GetUserExp(ctx context.Context, req *pb.GetUserExpReq) (*pb.GetUserExpResp, error) {
	if req.GetUid() == 0 {
		return &pb.GetUserExpResp{}, nil
	}
	exp, err := m.cacheClient.GetUserExp(req.GetUid())
	if err != nil {
		if err.Error() != cache.RedisNotFoundErr {
			log.ErrorWithCtx(ctx, "GetUserExp Get exp cache err:%+v, req:%+v", err, req)
			return nil, err
		}
		userExp, err := m.mysqlStore.GetUserExp(ctx, req.Uid)
		if err != nil {
			if !errors.Is(err, gorm.ErrRecordNotFound) {
				log.ErrorWithCtx(ctx, "GetUserExp Get exp db err:%+v, req:%+v", err, req)
				return nil, err
			}
			return &pb.GetUserExpResp{}, nil
		}
		exp = userExp.Exp
		go func() {
			ctx = grpc.NewContextWithInfo(ctx)
			err = m.cacheClient.SetUserExp(req.Uid, userExp.Exp)
			if err != nil {
				log.ErrorWithCtx(ctx, "GetUserExp set exp cache err:%+v, req:%+v", err, req)
				return
			}
		}()
	}
	return &pb.GetUserExpResp{Exp: exp, Level: m.sc.GetLevelExp().GetLevelByExp(exp)}, nil
}

// AddUserExp 增加经验
func (m *Manager) AddUserExp(ctx context.Context, req *pb.AddUserExpReq) (*pb.AddUserExpResp, error) {
	if req.GetUid() == 0 || req.Exp == 0 {
		return &pb.AddUserExpResp{}, nil
	}
	if req.MissionKey == "" {
		req.MissionKey = utils.GetOrderIdSimple(req.Uid)
	}
	exp := req.Exp // 增减经验值
	var originExp, newExp int32
	var originLevel, newLevel uint32
	userExp, err := m.mysqlStore.GetUserExp(ctx, req.Uid)
	if err != nil {
		if !errors.Is(err, gorm.ErrRecordNotFound) {
			log.ErrorWithCtx(ctx, "AddUserExp GetUserExp err:%s, req:%+v", err, req)
			return nil, err
		}
	}
	if userExp == nil { // 0级新用户
		originExp = 0
	} else {
		originExp = userExp.Exp
	}
	originLevel = m.sc.GetLevelExp().GetLevelByExp(originExp)
	newExp = originExp + exp
	if exp > 0 && newExp < 0 { // 增加经验时，防止溢出
		newExp = conf.MaxExp
	}
	if newExp < 0 { // 减少经验时，操作后不能为负
		newExp = 0
	}
	newLevel = m.sc.GetLevelExp().GetLevelByExp(newExp)
	if err = m.mysqlStore.AddUserExp(ctx, req, originExp, newExp, newLevel); err != nil {
		log.ErrorWithCtx(ctx, "AddUserExp db err:%s", err)
		return nil, protocol.NewExactServerError(codes.OK, status.ErrGrowExpAdded)
	}
	if err = m.cacheClient.SetUserExp(req.Uid, newExp); err != nil {
		log.ErrorWithCtx(ctx, "AddUserExp set exp cache err:%+v, req:%+v", err, req)
	}
	now := time.Now()
	if originLevel != newLevel {
		log.InfoWithCtx(ctx, "AddUserExp user level changed, uid:%d, originLevel:%d, newLevel:%d, ",
			req.Uid, originLevel, newLevel)

		go func() {
			ctx, cancel := grpc.NewContextWithInfoTimeout(ctx, 5*time.Second)
			defer cancel()
			defer func() {
				if err := recover(); err != nil {
					log.ErrorWithCtx(ctx, "AddUserExp defer panic err:%s, req:%+v", err, req)
				}
			}()

			// 升级发送kafka消息
			eventOpt := new(kafkaUserInfo.UserLevelEventOpt)
			eventOpt.Level = newLevel
			eventOpt.OriginLevel = originLevel
			optSerialize, err := eventOpt.Marshal()
			if err != nil {
				log.ErrorWithCtx(ctx, "AddUserExp Marshal err:%s, req:%+v", err, req)
			} else {
				e := &kafkaUserInfo.UserEvent{
					Uid:       req.Uid,
					Type:      uint32(kafkaUserInfo.EVENT_TYPE_EVENT_LEVEL_CHG),
					UpdateTs:  uint32(now.Unix()),
					OptPbInfo: optSerialize,
				}
				m.kafkaProduce.ProducerUserEvent(ctx, e)
				log.InfoWithCtx(ctx, "AddUserExp send kafka UserEvent EVENT_TYPE_EVENT_LEVEL_CHG, uid:%d, level:%d, originLevel:%d, event:%+v",
					req.Uid, newLevel, originLevel, e)
			}

			// oss 上报用户等级变化
			datacenter.StdReportKV(ctx, "************", map[string]interface{}{
				"totalDate":    now.Format(utils.TimeLayout),
				"uid":          req.Uid,
				"createTime":   now.Unix(),
				"level":        newLevel,
				"glamourLevel": "",
				"wealthLevel":  "",
			})
		}()
	}
	return &pb.AddUserExpResp{}, nil
}

// GetLevelExpScope 获取等级经验范围
func (m *Manager) GetLevelExpScope(ctx context.Context, req *pb.GetLevelExpScopeReq) (*pb.GetLevelExpScopeResp, error) {
	_ = ctx
	start, end := m.sc.GetLevelExp().GetExpScopeByLevel(req.GetLevel())
	return &pb.GetLevelExpScopeResp{
		StartExp: uint32(start),
		EndExp:   uint32(end),
	}, nil
}

// HasAddedUserExp 是否存在经验变更记录
func (m *Manager) HasAddedUserExp(ctx context.Context, req *pb.HasAddedUserExpReq) (*pb.HasAddedUserExpResp, error) {
	added, changedExp, err := m.mysqlStore.HasAddedUserExp(ctx, req.Uid, req.MissionKey)
	if err != nil {
		log.ErrorWithCtx(ctx, "HasAddedUserExp get exp log err:%+v, req:%+v", err, req)
		return nil, err
	}
	return &pb.HasAddedUserExpResp{
		Added:      added,
		ChangedExp: changedExp,
	}, nil
}

// BatGetUserExp 批量获取用户经验
func (m *Manager) BatGetUserExp(ctx context.Context, req *pb.BatGetUserExpReq) (*pb.BatGetUserExpResp, error) {
	uidCount := len(req.UidList)
	if uidCount == 0 {
		return &pb.BatGetUserExpResp{}, nil
	}
	if uidCount > 1000 {
		return nil, errors.New("too much uid")
	}
	userExp, missUidList, err := m.cacheClient.GetBatchUserExp(req.UidList)
	if err != nil {
		log.ErrorWithCtx(ctx, "BatGetUserExp batch get exp err:%+v, req:%+v", err, req)
		return nil, err
	}
	if len(missUidList) > 0 {
		userExpDB, err := m.mysqlStore.GetBatchUserExp(ctx, missUidList)
		if err != nil {
			return nil, err
		}
		userExp = append(userExp, userExpDB...)
		go func() {
			newCtx := grpc.NewContextWithInfo(ctx)
			err = m.cacheClient.SetBatchUserExp(userExpDB)
			if err != nil {
				log.ErrorWithCtx(newCtx, "BatGetUserExp SetBatchUserExp err:%+v", err)
			}
		}()
	}
	for _, ue := range userExp {
		ue.Level = m.sc.GetLevelExp().GetLevelByExp(ue.Exp)
	}
	return &pb.BatGetUserExpResp{
		UserExpList: userExp,
	}, nil
}

// ReportUserAliveness 上报用户活跃
func (m *Manager) ReportUserAliveness(ctx context.Context, req *pb.ReportUserAlivenessReq) (*pb.ReportUserAlivenessResp, error) {
	now := time.Now()
	if now.Hour() < 1 {
		shortTime := now.Year()*10000 + int(now.Month())*100 + now.Day()
		if (int(req.Uid)^shortTime)%60 > now.Minute() {
			return &pb.ReportUserAlivenessResp{}, nil
		}
	}
	if err := m.AddAsyncTask(ctx, &model.ReportUserAliveness{Uid: req.Uid, ReportTime: time.Now()}); err != nil {
		log.ErrorWithCtx(ctx, "ReportUserAliveness AddAsyncTask err:%+v, req:%+v", err, req)
		return nil, errors.New("report failed")
	}
	return &pb.ReportUserAlivenessResp{
		ChangedExp: 10, // 仅用于表示上报成功
	}, nil
}

// ReportUserAlivenessSync 上报用户活跃，在线时间增加经验
func (m *Manager) ReportUserAlivenessSync(ctx context.Context, uid uint32, now time.Time) error {
	var addExp uint32 // 增加经验
	// 获取用户在线记录
	data, err := m.cacheClient.GetUserOnlineFields(uid, now)
	if err != nil {
		log.ErrorWithCtx(ctx, "ReportUserAliveness GetUserOnlineFields err:%+v, uid:%d", err, uid)
	}
	lastReportTime := data.LastReportTime
	var addUpOnlineTotalTime int64 // 累计在线时长
	if data.FinFlag < 2 {
		var differTime int64 // 时间差
		nowTimestamp := now.Unix()
		if nowTimestamp > lastReportTime {
			differTime = nowTimestamp - lastReportTime
		}
		// 基于sync的间隔是4分半到5分半钟一次，加上网络延迟（放宽时间）
		if lastReportTime > 0 && differTime > 250 && differTime < 350 {
			addUpOnlineTotalTime, err = m.cacheClient.UserOnlineTimeIncr(data, 300) // 在线时间增加5分钟
			if err != nil {
				log.ErrorWithCtx(ctx, "ReportUserAliveness HIncrBy err: %v, uid:%d", err, uid)
				return err
			}
			/*
			   用户在线时长为0-3分钟 不增加经验
			   3-9分钟 10经验
			   10-20分钟 增加到20经验 即比上一时段 增加10经验
			   30-59分钟 增加到50经验 即比上一时段 增加30经验
			   60分钟 增加到150经验 即比上一时段  增加100经验
			   也就是说用户 在线60分钟 最多总共增加150经验
			*/
			switch addUpOnlineTotalTime {
			case 300:
				addExp = 10
			case 600:
				addExp = 10
			case 1800:
				addExp = 30
			case 3600:
				addExp = 100
			default:
				if addUpOnlineTotalTime >= 3600 {
					data.FinFlag = 1
				}
			}
		}
		// 更新记录
		if err = m.cacheClient.SaveUserOnlineData(data); err != nil {
			log.ErrorWithCtx(ctx, "ReportUserAliveness SaveUserOnlineData err: %v, uid:%d", err, uid)
			return err
		}
	}
	// 创建KEY设置超时 2d
	if lastReportTime == 0 {
		_ = m.cacheClient.UserOnlineDataExpire(data, time.Hour*48)
	}

	if addExp == 0 {
		return nil
	}

	log.InfoWithCtx(ctx, "ReportUserAliveness uid:%d, exp:%d, online:%d", uid, addExp, addUpOnlineTotalTime)

	resp, err := m.spClient.GetSuperPlayerInfo(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "ReportUserAliveness spClient.GetSuperPlayerInfo err:%+v, uid:%d", err, uid)
		return err
	}
	superPlayerLevel := resp.GetSuperPlayerInfo().GetSuperPlayerLevel()
	ExpireTimestamp := resp.GetSuperPlayerInfo().GetExpireTimestamp()
	if superPlayerLevel != 0 && ExpireTimestamp > now.Unix() {
		// 超级会员经验加速状态
		speedUpStatus, err := m.cacheClient.GetUserSpeedUpStatus(uid)
		if err != nil {
			log.ErrorWithCtx(ctx, "ReportUserAlivenessSync GetUserSpeedUpStatus failed uid:%d err:%+v", uid, err)
		}

		log.DebugWithCtx(ctx, "ReportUserAlivenessSync user speed up status uid:%d status:%d", uid, speedUpStatus)
		if speedUpStatus == int32(pb.ExpSpeedUpStatus_SpeadUpOpen) {
			addExp *= 2
		}
	}

	if _, err = m.AddUserExp(ctx, &pb.AddUserExpReq{
		Uid:         uid,
		Exp:         int32(addExp),
		MissionDesc: "在线奖励",
		OpUid:       0,
	}); err != nil {
		log.ErrorWithCtx(ctx, "ReportUserAliveness AddUserExp err:%+v, uid:%d", err, uid)
	}

	if addExp >= 30 {
		// 通知客户端经验变更，完成后面两次才更新，否则太多了
		go func() {
			ctx = grpc.NewContextWithInfo(ctx)
			if err = m.UpdateUserGrowTimeline(ctx, uid); err != nil {
				log.ErrorWithCtx(ctx, "ReportUserAliveness UpdateUserGrowTimeline err:%+v, uid:%d", err, uid)
			}
		}()
	}
	return nil
}

// GetUserOnlineTime 获取用户在线时长
func (m *Manager) GetUserOnlineTime(ctx context.Context, req *pb.GetUserOnlineTimeReq) (*pb.GetUserOnlineTimeResp, error) {
	resp, sErr := m.spClient.GetSuperPlayerInfo(ctx, req.Uid)
	if sErr != nil {
		log.ErrorWithCtx(ctx, "GetUserOnlineTime spClient.GetSuperPlayerInfo err:%+v", sErr)
		return nil, sErr
	}
	onlineTime, err := m.cacheClient.GetUserOnlineTime(req.Uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetUserOnlineTime cache get online time err:%+v", err)
		return nil, err
	}
	var level uint32
	if resp.GetSuperPlayerInfo().GetExpireTimestamp() < time.Now().Unix() {
		level = uint32(resp.GetSuperPlayerInfo().GetSuperPlayerLevel())
	}
	return &pb.GetUserOnlineTimeResp{
		OnlineTime:     onlineTime,
		SuperPlayerLev: level,
	}, nil
}

// GetUserExpLog 获取用户经验变更记录
func (m *Manager) GetUserExpLog(ctx context.Context, req *pb.GetUserExpLogReq) (*pb.GetUserExpLogResp, error) {
	expLogs, err := m.mysqlStore.GetUserExpLog(ctx, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetUserExpLog get exp logs err:%+v", err)
		return nil, err
	}
	infos := make([]*pb.UserExpLogInfo, len(expLogs))
	for i, expLog := range expLogs {
		infos[i] = &pb.UserExpLogInfo{
			EValue: uint32(expLog.ChangedExp),
			OpTime: expLog.ChangedTime.Format(utils.TimeLayout),
			Desc:   expLog.OrderDesc,
		}
	}
	return &pb.GetUserExpLogResp{LogInfos: infos}, nil
}

// GetSuperPlayerExpSpeedUpStatus 获取会员经验加速状态
func (m *Manager) GetSuperPlayerExpSpeedUpStatus(ctx context.Context, in *pb.GetSuperPlayerExpSpeedUpStatusReq) (*pb.GetSuperPlayerExpSpeedUpStatusResp, error) {
	out := &pb.GetSuperPlayerExpSpeedUpStatusResp{}

	log.DebugWithCtx(ctx, "GetSuperPlayerExpSpeedUpStatus begin in:%v", in)
	speedUpStatus, err := m.cacheClient.GetUserSpeedUpStatus(in.GetUid())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetSuperPlayerExpSpeedUpStatus GetUserSpeedUpStatus failed req:%v err:%v", in, err)
		return out, err
	}

	out.SpeedUpStatus = uint32(speedUpStatus)

	log.DebugWithCtx(ctx, "GetSuperPlayerExpSpeedUpStatus end in:%v resp:%v", in, out)
	return out, nil
}

// SetSuperPlayerExpSpeedUpStatus 设置会员经验加速状态
func (m *Manager) SetSuperPlayerExpSpeedUpStatus(ctx context.Context, in *pb.SetSuperPlayerExpSpeedUpStatusReq) (*pb.SetSuperPlayerExpSpeedUpStatusResp, error) {
	out := &pb.SetSuperPlayerExpSpeedUpStatusResp{}

	log.DebugWithCtx(ctx, "SetSuperPlayerExpSpeedUpStatus begin in:%v", in)
	// 打开状态直接删除，key不存在表示打开
	if in.GetSpeedUpStatus() == uint32(pb.ExpSpeedUpStatus_SpeadUpOpen) {
		err := m.cacheClient.DelUserSpeedUpStatus(in.GetUid())
		if err != nil {
			log.ErrorWithCtx(ctx, "SetSuperPlayerExpSpeedUpStatus DelUserSpeedUpStatus failed in:%v err:%v", in, err)
			return out, err
		}
	} else {
		err := m.cacheClient.SetUserSpeedUpStatus(in.GetUid(), int32(in.GetSpeedUpStatus()))
		if err != nil {
			log.ErrorWithCtx(ctx, "SetSuperPlayerExpSpeedUpStatus SetUserSpeedUpStatus failed in:%v err:%v", in, err)
			return out, err
		}
	}

	log.DebugWithCtx(ctx, "SetSuperPlayerExpSpeedUpStatus end in:%v out:%v", in, out)

	return out, nil
}

func (m *Manager) UpdateUserGrowTimeline(ctx context.Context, uid uint32) error {
	userExp, err := m.GetUserExp(ctx, &pb.GetUserExpReq{Uid: uid})
	if err != nil {
		log.ErrorWithCtx(ctx, "UpdateUserGrowTimeline GetUserExp err:%s, uid:%d", err, uid)
		return err
	}
	if userExp == nil || userExp.GetExp() == 0 {
		return nil
	}

	exp := userExp.GetExp()
	newLevel := m.sc.GetLevelExp().GetLevelByExp(exp)
	levelStartExp, levelEndExp := m.sc.GetLevelExp().GetExpScopeByLevel(newLevel)

	curr, _ := m.currCli.GetUserCurrency(ctx, uid)
	seq, err := m.seqgenCli.GenerateSequence(ctx, uid, seqgen.NamespaceUser, seqgen.KeyGrowth, 1)
	if err != nil {
		log.ErrorWithCtx(ctx, "UpdateUserGrowTimeline GenerateSequence err: %s, uid:%d", err, uid)
		return err
	}
	msg := &tlsvr.GrowInfoMessage{
		Exp:                exp,
		Level:              newLevel,
		CurrentLevelExpMin: uint32(levelStartExp),
		CurrentLevelExpMax: uint32(levelEndExp),
		Currency:           curr,
	}
	err = m.tlCli.ExpCurrencyChanged(ctx, uid, uint32(seq), msg)
	if err != nil {
		log.ErrorWithCtx(ctx, "UpdateUserGrowTimeline ExpCurrencyChanged err: %s, uid:%d", err, uid)
		return err
	}

	m.notifyClient(ctx, []uint32{uid})

	log.DebugWithCtx(ctx, "UpdateUserGrowTimeline uid:%d, exp:%d, level:%d, minExp:%d, maxExp:%d, currency:%+v", uid, exp, newLevel, levelStartExp, levelEndExp, curr)
	return nil
}

func (m *Manager) notifyClient(ctx context.Context, uidList []uint32) {
	var b [4]byte
	binary.BigEndian.PutUint32(b[:], uint32(sync.SyncReq_GROW)) // Network Order
	sequence := uint32(time.Now().UnixNano())
	seq := atomic.AddUint32(&sequence, 1)
	err := m.pushCli.PushToUsers(ctx, uidList, &pushPB.CompositiveNotification{
		Sequence:           seq,
		TerminalTypePolicy: pushclient.DefaultPolicy,
		AppId:              0,
		ProxyNotification: &pushPB.ProxyNotification{
			Type:    uint32(pushPB.ProxyNotification_NOTIFY),
			Payload: b[:],
		},
	})
	if err != nil {
		log.DebugWithCtx(ctx, "NotifySyncX - users=%v(%d) type=%v seq=%d err=%s", uidList, len(uidList), sync.SyncReq_GROW, seq, err.Error())
	} else {
		log.DebugWithCtx(ctx, "NotifySyncX - users=%v(%d) type=%v seq=%d", uidList, len(uidList), sync.SyncReq_GROW, seq)
	}
	return
}
