package manager

// import (
// 	"context"
// 	"errors"
// 	"fmt"
// 	"net/http"
// 	"reflect"
// 	"testing"
// 	"time"

// 	"bou.ke/monkey"
// 	"github.com/golang/mock/gomock"
// 	"github.com/jarcoal/httpmock"
// 	"github.com/jmoiron/sqlx"
// 	"golang.52tt.com/clients/account"
// 	anchorcheck "golang.52tt.com/clients/anchor-check"
// 	anchorcontract_go "golang.52tt.com/clients/anchorcontract-go"
// 	apicenter "golang.52tt.com/clients/apicenter/apiserver"
// 	"golang.52tt.com/clients/channel"
// 	channellivefans "golang.52tt.com/clients/channel-live-fans"
// 	channellivemgr "golang.52tt.com/clients/channel-live-mgr"
// 	channellivestats "golang.52tt.com/clients/channel-live-stats"
// 	channellottery "golang.52tt.com/clients/channel-lottery"
// 	channel_recommend_svr "golang.52tt.com/clients/channel-recommend-svr"
// 	channelvotepkgo "golang.52tt.com/clients/channel-vote-pk-go"
// 	"golang.52tt.com/clients/channelbackground"
// 	"golang.52tt.com/clients/entertainmentrecommendback"
// 	accountmock "golang.52tt.com/clients/mocks/account"
// 	anchorchecktmock "golang.52tt.com/clients/mocks/anchor-check"
// 	anchorcontractmock "golang.52tt.com/clients/mocks/anchorcontract-go"
// 	mockapi "golang.52tt.com/clients/mocks/apicenter/apiserver"
// 	channelmock "golang.52tt.com/clients/mocks/channel"
// 	mockchannellivemgr "golang.52tt.com/clients/mocks/channel-live-mgr"
// 	mockChannelLiveStats "golang.52tt.com/clients/mocks/channel-live-stats"
// 	channelLotterymock "golang.52tt.com/clients/mocks/channel-lottery"
// 	recommendmock "golang.52tt.com/clients/mocks/channel-recommend-svr"
// 	publicmock "golang.52tt.com/clients/mocks/public"
// 	userOlmock "golang.52tt.com/clients/mocks/user-online"
// 	"golang.52tt.com/clients/public"
// 	userol "golang.52tt.com/clients/user-online"
// 	"golang.52tt.com/pkg/config"
// 	"golang.52tt.com/pkg/datahouse"
// 	"golang.52tt.com/pkg/log"
// 	"golang.52tt.com/pkg/protocol"
// 	"golang.52tt.com/pkg/urrc"
// 	accountPB "golang.52tt.com/protocol/services/accountsvr"
// 	AnchorCheck "golang.52tt.com/protocol/services/anchor-check"
// 	pb "golang.52tt.com/protocol/services/anchor-level"
// 	channel_live_stats "golang.52tt.com/protocol/services/channel-live-stats"
// 	channel_lottery "golang.52tt.com/protocol/services/channel-lottery"
// 	channellotteryPB "golang.52tt.com/protocol/services/channel-lottery"
// 	channellivemgrPB "golang.52tt.com/protocol/services/channellivemgr"
// 	channelsvr "golang.52tt.com/protocol/services/channelsvr"
// 	publicPB "golang.52tt.com/protocol/services/publicsvr"
// 	userolpb "golang.52tt.com/protocol/services/user-online"
// 	"golang.52tt.com/services/anchor-level/conf"
// 	"golang.52tt.com/services/anchor-level/mocks"
// 	"golang.52tt.com/services/anchor-level/report"
// 	"golang.52tt.com/services/anchor-level/store"
// )

// func init() {
// 	log.SetLevel(log.DebugLevel)
// }

// func getdb() *store.Store {
// 	mysqlConfig := &config.MysqlConfig{
// 		Host:     "**************",
// 		Port:     3306,
// 		Database: "appsvr",
// 		UserName: "godman",
// 		Password: "thegodofman",
// 		Charset:  "utf8mb4",
// 	}

// 	log.Infof("mysql_config connect %s", mysqlConfig.ConnectionString())
// 	db, err := sqlx.Connect("mysql", mysqlConfig.ConnectionString())
// 	if err != nil {
// 		fmt.Println(err)
// 		return nil
// 	}

// 	store := store.NewStore(db)
// 	return store
// }

// func getmgr() *AnchorLevelMgr {
// 	cfg, err := config.NewConfig("json", "../anchor-level.json")
// 	if err != nil {
// 		log.Errorln("Failed to init ServerConfig from file:")
// 		return nil
// 	}

// 	mgr, err := NewAnchorLevelMgr(cfg)
// 	if err != nil {
// 		log.Fatalln(err)
// 		return nil
// 	}
// 	time.Sleep(time.Second * 3)
// 	return mgr
// }

// // go test -timeout 30s -run ^TestHandleSetLotteryOpen$ golang.52tt.com/services/anchor-level/manager -v -count=1
// func TestHandleSetLotteryOpen(t *testing.T) {

// 	settleMonthTm, _ := time.Parse("2006-01", "2023-08")
// 	end := time.Date(settleMonthTm.Year(), settleMonthTm.Month(), 1, 0, 0, 0, 0, time.Local).AddDate(0, 2+1, 0).Add(-time.Second)

// 	t.Log(settleMonthTm)
// 	t.Log(end)
// 	return

// 	info := &store.LiveAnchorMonthTaskAwardLog{
// 		AnchorUid: 2404178,
// 		ChannelId: 2255867,
// 	}
// 	channelLotteryDays := uint32(30)
// 	log.Infof("HandleSetLotteryOpen begin uid %d info %+v channelLotteryDays %d", info.AnchorUid, info, channelLotteryDays)

// 	now := time.Now()
// 	day := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, time.Local)
// 	uid := info.AnchorUid
// 	ctx := context.Background()

// 	channelCli := channel.NewClient()
// 	channelLotteryCli, _ := channellottery.NewClient()
// 	time.Sleep(time.Second * 3)

// 	channelInfo, serr := channelCli.GetChannelSimpleInfo(ctx, 0, info.ChannelId)
// 	if serr != nil {
// 		log.Errorf("LiveAnchorLevelMonthAward awardUser GetChannelSimpleInfo fail %+v, uid %d", serr, info.AnchorUid)
// 		return
// 	}

// 	displayId := channelInfo.GetDisplayId()
// 	lotteryOpenResp, serr := channelLotteryCli.SetLotteryOpenList(ctx, &channel_lottery.SetLotteryOpenListReq{
// 		Data: []*channel_lottery.LotteryOpen{
// 			{
// 				ChannelId: info.ChannelId,
// 				DisplayId: displayId,
// 				BeginTime: now.Unix(),
// 				EndTime:   day.AddDate(0, 0, int(channelLotteryDays)).Unix(),
// 			},
// 		},
// 	})
// 	if serr != nil {
// 		log.Errorf("LiveAnchorLevelMonthAward awardUser uid %d SetLotteryOpenList fail %+v", uid, serr)
// 		return
// 	}
// 	log.Infof("HandleSetLotteryOpen SetLotteryOpenList uid %d info %+v channelLotteryDays %d, resp=%q",
// 		info.AnchorUid, info, channelLotteryDays, lotteryOpenResp)

// 	// 时间冲突了
// 	if len(lotteryOpenResp.GetTimeConflictId()) > 0 {

// 		log.Errorf("LiveAnchorLevelMonthAward awardUser.SetLotteryOpenList timeConflict %v, uid %d cid %d displayId %d",
// 			lotteryOpenResp.GetTimeConflictId(), uid, info.ChannelId, displayId)

// 		openList, err := channelLotteryCli.SearchLotteryOpens(ctx, info.ChannelId, 0, 0, 1) // 传channelid
// 		if err != nil {
// 			log.Errorf("SearchLotteryOpens fail %v, uid %d cid %d displayId %d", err, uid, info.ChannelId, displayId)
// 			return
// 		}
// 		log.Infof("LiveAnchorLevelMonthAward SearchLotteryOpens uid %d openList total=%d, list=%+v", uid, len(openList.GetDataList()), openList)

// 		if len(openList.GetDataList()) > 0 {
// 			data := openList.GetDataList()[0]
// 			log.Infof("SearchLotteryOpens uid %d data=%q", uid, data)

// 			// 抽奖入口开放时间叠加
// 			beginTs := data.EndTime + 1
// 			beginTm := time.Unix(beginTs, 0)
// 			beginDayTime := time.Date(beginTm.Year(), beginTm.Month(), beginTm.Day(), 0, 0, 0, 0, time.Local)
// 			endTs := beginDayTime.AddDate(0, 0, int(channelLotteryDays)).Unix()
// 			lotteryOpenResp, serr := channelLotteryCli.SetLotteryOpenList(ctx, &channel_lottery.SetLotteryOpenListReq{
// 				Data: []*channel_lottery.LotteryOpen{
// 					{
// 						ChannelId: info.ChannelId,
// 						DisplayId: displayId,
// 						BeginTime: beginTs,
// 						EndTime:   endTs, // 先这样
// 					},
// 				},
// 			})
// 			if serr != nil {
// 				log.Errorf("awardUser uid %d SetLotteryOpenList fail %+v", uid, serr)
// 				return
// 			}

// 			// 还有冲突，那再手动处理
// 			if len(lotteryOpenResp.GetTimeConflictId()) > 0 {

// 				log.Errorf("fix SetLotteryOpenList fail. uid %d cid %d displayId %d [%d-%d] lotteryOpenResp=%+v",
// 					uid, info.ChannelId, displayId, beginTs, endTs, lotteryOpenResp)
// 			} else {
// 				log.Infof("fix SetLotteryOpenList ok uid %d cid %d displayId %d day %d [%d-%d], lotteryOpenResp=%+v",
// 					uid, info.ChannelId, displayId, channelLotteryDays, beginTs, endTs, lotteryOpenResp)
// 			}
// 		} else {

// 			log.Errorf("fix SetLotteryOpenList fail. uid %d cid %d displayId %d got openList=0", uid, info.ChannelId, displayId)
// 		}

// 	} else {
// 		// 正常发放
// 		log.Infof("LiveAnchorLevelMonthAward awardUser.SetLotteryOpenList ok uid %d cid %d displayId %d ChannelLotteryDays %d",
// 			uid, info.ChannelId, displayId, channelLotteryDays)
// 	}
// }

// // go test -timeout 30s -run ^TestCnt$ golang.52tt.com/services/anchor-level/manager -v -count=1
// func TestCnt(t *testing.T) {

// 	settleMonthTm := time.Now()
// 	monthBegin := time.Date(settleMonthTm.Year(), settleMonthTm.Month(), 1, 0, 0, 0, 0, time.Local)
// 	monthEnd := time.Date(settleMonthTm.Year(), settleMonthTm.Month()+1, 1, 0, 0, 0, 0, time.Local).Add(-time.Second)

// 	t.Logf("%s\n", monthBegin)
// 	t.Logf("%s\n", monthEnd)
// 	return

// 	m := getmgr()

// 	var page, pageSize uint32 = 1, 50
// 	total := 0
// 	for {

// 		time.Sleep(time.Millisecond * 50)

// 		// 全量主播uid channel-live-mgr.GetAnchorList tbl_channel_live
// 		// select %s from tbl_channel_live where end_time >= now() limit ?,?
// 		ctx, cancel := context.WithTimeout(context.Background(), time.Second*20)
// 		defer cancel()

// 		anchorList, err := m.channelLiveMgr.GetAnchorList(ctx, page, pageSize)
// 		if err != nil {
// 			log.Errorf("LiveAnchorLevelMonthSettle GetAnchorList fail %v", err)
// 			return
// 		}
// 		if len(anchorList) == 0 {
// 			break
// 		}
// 		total += len(anchorList)

// 		anchorUids := []uint32{}
// 		anchorUid2ChannelId := map[uint32]uint32{}
// 		for _, info := range anchorList {
// 			anchorUids = append(anchorUids, info.Uid)
// 			anchorUid2ChannelId[info.Uid] = info.ChannelId
// 		}
// 		log.Infof("LiveAnchorLevelMonthSettle GetAnchorList page %d size %d uids %v", page, len(anchorList), anchorUids)

// 		page++
// 	}

// 	t.Logf("total %d\n", total)

// }

// /*

//  */
// // go test -timeout 30s -run ^TestLiveAnchorLevelMonthSettleByUid$ golang.52tt.com/services/anchor-level/manager -v -count=1
// func TestLiveAnchorLevelMonthSettleByUid(t *testing.T) {
// 	return

// 	mgr := getmgr()
// 	ctx := context.Background()
// 	monthTm := time.Now()
// 	uids := []uint32{2404178}

// 	liveInfo, serr := mgr.GetChannelLiveMgr().GetAnchorByUidList(ctx, &channellivemgrPB.GetAnchorByUidListReq{UidList: uids})
// 	if serr != nil {
// 		log.Errorln(serr)
// 		return
// 	}
// 	anchorUid2ChannelId := map[uint32]uint32{}
// 	for _, info := range liveInfo.GetAnchorList() {
// 		anchorUid2ChannelId[info.Uid] = info.ChannelId
// 	}

// 	taskList, anchorlevel2Name, err := mgr.GetStore().GetLiveAnchorMonthTask()
// 	if err != nil {
// 		log.Errorf("LiveAnchorLevelMonthSettle GetLiveAnchorMonthTask fail %v", err)
// 		return
// 	}
// 	if len(taskList) == 0 {
// 		log.Errorf("LiveAnchorLevelMonthSettle GetLiveAnchorMonthTask got 0")
// 		return
// 	}
// 	log.Infof("LiveAnchorLevelMonthSettle GetLiveAnchorMonthTask %s", taskList)
// 	taskLimit := taskList[0] // 最低任务要求

// 	mgr.LiveAnchorLevelMonthSettleByUid(ctx, monthTm, uids, anchorUid2ChannelId, taskLimit, taskList, anchorlevel2Name)
// }

// // go test -timeout 30s -run ^TestCalcAnchorLevel$ golang.52tt.com/services/anchor-level/manager -v -count=1
// func TestCalcAnchorLevel(t *testing.T) {

// 	w := uint32(10000)
// 	passUids := []uint32{1, 2}
// 	settleMonthTm := time.Now().AddDate(0, -1, 0)

// 	uid2monthStats := map[uint32]*channel_live_stats.BatchGetAnchorMonthlyStatsByUidRespMonthStats{
// 		1: {
// 			AnchorIncome:  521200,
// 			NewFansCnt:    2,
// 			ConsumerCnt:   2,
// 			LiveActiveCnt: 10,
// 		},
// 		2: {
// 			AnchorIncome:  521200,
// 			NewFansCnt:    2,
// 			ConsumerCnt:   2,
// 			LiveActiveCnt: 20,
// 		},
// 	}
// 	taskList := []*store.LiveAnchorMonthTask{
// 		{
// 			AnchorLevel:     1,
// 			MonthConsumeCnt: 50,
// 			MonthNewFansCnt: 20,
// 			MonthIncome:     50 * w,
// 		},
// 		{
// 			AnchorLevel:     2,
// 			MonthConsumeCnt: 70,
// 			MonthNewFansCnt: 30,
// 			MonthIncome:     80 * w,
// 		},
// 		{
// 			AnchorLevel:     3,
// 			MonthConsumeCnt: 100,
// 			MonthNewFansCnt: 50,
// 			MonthIncome:     100 * w,
// 		},
// 		{
// 			AnchorLevel:     4,
// 			MonthConsumeCnt: 150,
// 			MonthNewFansCnt: 70,
// 			MonthIncome:     500 * w,
// 		},
// 		{
// 			AnchorLevel:     5,
// 			MonthConsumeCnt: 200,
// 			MonthNewFansCnt: 100,
// 			MonthIncome:     1000 * w,
// 		},
// 	}

// 	if false {
// 		store := getdb()
// 		var err error
// 		taskList, _, err = store.GetLiveAnchorMonthTask()
// 		if err != nil {
// 			t.Log(err)
// 			return
// 		}
// 		for _, info := range taskList {
// 			t.Logf("%q\n", info)
// 		}
// 	}

// 	uid2level := CalcAnchorLevel(passUids, settleMonthTm, uid2monthStats, taskList)
// 	for uid, level := range uid2level {
// 		t.Logf("uid %d level %d", uid, level)
// 	}

// }

// // go test -timeout 30s -run ^TestL$ golang.52tt.com/services/anchor-level/manager -v -count=1
// func TestL(t *testing.T) {

// 	return

// 	dyconfig := conf.NewConfigHandler(conf.DyconfigPath)
// 	if err := dyconfig.Start(); err != nil {
// 		return
// 	}

// 	userOlCli, _ := userol.NewClient()

// 	mgr := &AnchorLevelMgr{
// 		publicCli: public.NewClient(),
// 		userOlCli: userOlCli,
// 		dyconfig:  dyconfig,
// 	}
// 	time.Sleep(time.Second * 3)

// 	content := "【见习主播荣誉达成】\n亲爱的主播：\n\t\t\t\t\t恭喜您在上月完成“见习主播”等级任务，本月获得“见习主播”荣誉标识，继续完成更高等级任务可获得更多流量卡奖励、精美房间背景、粉丝铭牌哦，加油吧~"
// 	//mgr.SendIMMsgWithJumpUrl(context.TODO(), 2404178, content, "", "")

// 	mgr.SendVoiceLiveMsg(context.Background(), 2404178, content, "", true)

// 	return
// 	settleMonthTm := time.Now().AddDate(0, -1, 0)
// 	monthBegin := time.Date(settleMonthTm.Year(), settleMonthTm.Month(), 1, 0, 0, 0, 0, time.Local)
// 	monthEnd := monthBegin.AddDate(0, 1, 0).Add(-time.Second)

// 	t.Logf("%s\n", monthBegin)
// 	t.Logf("%s\n", monthEnd)
// }

// // go test -timeout 30s -run ^TestSettleMonthAdvancedTask$ golang.52tt.com/services/anchor-level/manager -v -count=1
// func TestSettleMonthAdvancedTask(t *testing.T) {

// 	/*
// 		func SettleMonthAdvancedTask(uids []uint32,
// 		settleMonthStats, lastsettleMonthStats map[uint32]*channel_live_stats.BatchGetAnchorMonthlyStatsByUidRespMonthStats) (
// 		uid2AdvancedTaskPass map[uint32]bool) */

// 	uid2MonthTaskResult := map[uint32]*store.ExtraInfo{
// 		1: {}, 2: {}, 3: {},
// 	}
// 	w := uint32(10000)
// 	uids := []uint32{1, 2, 3}
// 	settleMonthStats := map[uint32]*channel_live_stats.BatchGetAnchorMonthlyStatsByUidRespMonthStats{
// 		1: {
// 			AnchorIncome: 50 * w,
// 		},
// 		2: {
// 			AnchorIncome: 60 * w,
// 		},
// 		3: {
// 			AnchorIncome: 400 * w,
// 		},
// 	}
// 	lastsettleMonthStats := map[uint32]*channel_live_stats.BatchGetAnchorMonthlyStatsByUidRespMonthStats{
// 		2: {
// 			AnchorIncome: 50 * w,
// 		},
// 		3: {
// 			AnchorIncome: 500 * w,
// 		},
// 	}

// 	uid2AdvancedTaskPass := SettleMonthAdvancedTask(uids, settleMonthStats, lastsettleMonthStats, uid2MonthTaskResult)
// 	t.Log(uid2AdvancedTaskPass)
// }

// // go test -timeout 30s -run ^TestGetAdvancedTaskMonthIncome$ golang.52tt.com/services/anchor-level/manager -v -count=1
// func TestGetAdvancedTaskMonthIncome(t *testing.T) {
// 	w := uint32(10000)
// 	lastMonthIncome := uint32(0)
// 	t.Log(lastMonthIncome, GetAdvancedTaskMonthIncome(lastMonthIncome))

// 	lastMonthIncome = 50 * w
// 	t.Log(lastMonthIncome, GetAdvancedTaskMonthIncome(lastMonthIncome))

// 	lastMonthIncome = 50*w + 1*w
// 	t.Log(lastMonthIncome, GetAdvancedTaskMonthIncome(lastMonthIncome))

// 	lastMonthIncome = 80 * w
// 	t.Log(lastMonthIncome, GetAdvancedTaskMonthIncome(lastMonthIncome))

// 	lastMonthIncome = 100 * w
// 	t.Log(lastMonthIncome, GetAdvancedTaskMonthIncome(lastMonthIncome))
// 	lastMonthIncome = 200 * w
// 	t.Log(lastMonthIncome, GetAdvancedTaskMonthIncome(lastMonthIncome))

// 	lastMonthIncome = 500 * w
// 	t.Log(lastMonthIncome, GetAdvancedTaskMonthIncome(lastMonthIncome))
// 	lastMonthIncome = 700 * w
// 	t.Log(lastMonthIncome, GetAdvancedTaskMonthIncome(lastMonthIncome))

// 	lastMonthIncome = 1000 * w
// 	t.Log(lastMonthIncome, GetAdvancedTaskMonthIncome(lastMonthIncome))
// 	lastMonthIncome = 1500 * w
// 	t.Log(lastMonthIncome, GetAdvancedTaskMonthIncome(lastMonthIncome))

// 	lastMonthIncome = 2000 * w
// 	t.Log(lastMonthIncome, GetAdvancedTaskMonthIncome(lastMonthIncome))
// 	lastMonthIncome = 3000 * w
// 	t.Log(lastMonthIncome, GetAdvancedTaskMonthIncome(lastMonthIncome))

// 	lastMonthIncome = 5000 * w
// 	t.Log(lastMonthIncome, GetAdvancedTaskMonthIncome(lastMonthIncome))
// 	lastMonthIncome = 6000 * w
// 	t.Log(lastMonthIncome, GetAdvancedTaskMonthIncome(lastMonthIncome))
// 	lastMonthIncome = 8000 * w
// 	t.Log(lastMonthIncome, GetAdvancedTaskMonthIncome(lastMonthIncome))
// }

// // go test -timeout 30s -run ^TestAnchorLevelMgr_GetLiveAnchorTaskEntry$ golang.52tt.com/services/anchor-level/manager -v -count=1

// func TestAnchorLevelMgr_GetLiveAnchorTaskEntry(t *testing.T) {

// 	ctl := gomock.NewController(t)
// 	defer ctl.Finish()

// 	mockStore := mocks.NewMockIStore(ctl)
// 	mockchannelCli := channelmock.NewMockIClient(ctl)
// 	mockconf := mocks.NewMockISDyConfigHandler(ctl)
// 	mockanchorcontractCli := anchorcontractmock.NewMockIClient(ctl)
// 	mockanchorcheckCli := anchorchecktmock.NewMockIClient(ctl)

// 	var (
// 		channelInfo = &channelsvr.ChannelSimpleInfo{}
// 		serr        = protocol.NewServerError(-2)

// 		ctype        = uint32(7)
// 		channelInfo2 = &channelsvr.ChannelSimpleInfo{
// 			ChannelType: &ctype,
// 		}
// 		levelInfo = &store.LiveAnchorMonthTaskAwardLog{}

// 		ttl  = uint32(0)
// 		ttl2 = uint32(10)

// 		w          = &AnchorCheck.WhiteData{}
// 		w2         = &AnchorCheck.WhiteData{ExpireTime: 1}
// 		checkResp  = &AnchorCheck.AnchorCheckGetCheckListResp{}
// 		checkResp2 = &AnchorCheck.AnchorCheckGetCheckListResp{List: []*AnchorCheck.AnchorCheckData{{Status: 4}}}
// 	)

// 	gomock.InOrder(
// 		mockchannelCli.EXPECT().GetChannelSimpleInfo(gomock.Any(), gomock.Any(), gomock.Any()).Return(channelInfo, serr),

// 		mockchannelCli.EXPECT().GetChannelSimpleInfo(gomock.Any(), gomock.Any(), gomock.Any()).Return(channelInfo, nil),

// 		mockchannelCli.EXPECT().GetChannelSimpleInfo(gomock.Any(), gomock.Any(), gomock.Any()).Return(channelInfo2, nil),
// 		mockanchorcontractCli.EXPECT().GetRadioLiveAnchorExamine(gomock.Any(), gomock.Any()).Return(ttl, nil),
// 		mockanchorcheckCli.EXPECT().CheckInWhite(gomock.Any(), gomock.Any()).Return(w, nil),
// 		mockanchorcheckCli.EXPECT().AnchorCheckGetCheckList(gomock.Any(), gomock.Any()).Return(checkResp, nil),
// 		mockStore.EXPECT().GetLiveAnchorMonthTaskAwardByUid(gomock.Any(), gomock.Any()).Return(levelInfo, nil),
// 		mockconf.EXPECT().GetLiveAnchorTaskUrl(gomock.Any(), gomock.Any()).Return("1?pageName=live-task&immersion=1&anchor_uid=1"),
// 		mockconf.EXPECT().GetAnchorLevel2BaseImgurl(gomock.Any()).Return("1"),

// 		mockchannelCli.EXPECT().GetChannelSimpleInfo(gomock.Any(), gomock.Any(), gomock.Any()).Return(channelInfo2, nil),
// 		mockanchorcontractCli.EXPECT().GetRadioLiveAnchorExamine(gomock.Any(), gomock.Any()).Return(ttl, serr),

// 		mockchannelCli.EXPECT().GetChannelSimpleInfo(gomock.Any(), gomock.Any(), gomock.Any()).Return(channelInfo2, nil),
// 		mockanchorcontractCli.EXPECT().GetRadioLiveAnchorExamine(gomock.Any(), gomock.Any()).Return(ttl2, nil),

// 		mockchannelCli.EXPECT().GetChannelSimpleInfo(gomock.Any(), gomock.Any(), gomock.Any()).Return(channelInfo2, nil),
// 		mockanchorcontractCli.EXPECT().GetRadioLiveAnchorExamine(gomock.Any(), gomock.Any()).Return(ttl, nil),
// 		mockanchorcheckCli.EXPECT().CheckInWhite(gomock.Any(), gomock.Any()).Return(w2, serr),

// 		mockchannelCli.EXPECT().GetChannelSimpleInfo(gomock.Any(), gomock.Any(), gomock.Any()).Return(channelInfo2, nil),
// 		mockanchorcontractCli.EXPECT().GetRadioLiveAnchorExamine(gomock.Any(), gomock.Any()).Return(ttl, nil),
// 		mockanchorcheckCli.EXPECT().CheckInWhite(gomock.Any(), gomock.Any()).Return(w2, nil),

// 		mockchannelCli.EXPECT().GetChannelSimpleInfo(gomock.Any(), gomock.Any(), gomock.Any()).Return(channelInfo2, nil),
// 		mockanchorcontractCli.EXPECT().GetRadioLiveAnchorExamine(gomock.Any(), gomock.Any()).Return(ttl, nil),
// 		mockanchorcheckCli.EXPECT().CheckInWhite(gomock.Any(), gomock.Any()).Return(w, nil),
// 		mockanchorcheckCli.EXPECT().AnchorCheckGetCheckList(gomock.Any(), gomock.Any()).Return(checkResp, serr),

// 		mockchannelCli.EXPECT().GetChannelSimpleInfo(gomock.Any(), gomock.Any(), gomock.Any()).Return(channelInfo2, nil),
// 		mockanchorcontractCli.EXPECT().GetRadioLiveAnchorExamine(gomock.Any(), gomock.Any()).Return(ttl, nil),
// 		mockanchorcheckCli.EXPECT().CheckInWhite(gomock.Any(), gomock.Any()).Return(w, nil),
// 		mockanchorcheckCli.EXPECT().AnchorCheckGetCheckList(gomock.Any(), gomock.Any()).Return(checkResp2, nil),
// 	)

// 	type fields struct {
// 		store                      store.IStore
// 		dyconfig                   conf.ISDyConfigHandler
// 		LocalCache                 *LocalCache
// 		channelLiveMgr             channellivemgr.IClient
// 		channelLiveStats           channellivestats.IClient
// 		channelRecommendSvr        channel_recommend_svr.IClient
// 		entertainmentRecommendBack entertainmentrecommendback.IClient
// 		anchorcontractGoClient     anchorcontract_go.IClient
// 		reporter                   report.IFeishuReporterV2
// 		accountCli                 account.IClient
// 		channelbackgroundCli       channelbackground.IClient
// 		channelLotteryCli          channellottery.IClient
// 		channelLiveFansCli         channellivefans.IClient
// 		channelVotePkGoCli         channelvotepkgo.IClient
// 		publicCli                  public.IClient
// 		apiCli                     apicenter.IClient
// 		userOlCli                  userol.IClient
// 		channelCli                 channel.IClient
// 		anchorCheckCli             anchorcheck.IClient
// 	}
// 	type args struct {
// 		ctx context.Context
// 		in  *pb.GetLiveAnchorTaskEntryReq
// 	}
// 	tests := []struct {
// 		name    string
// 		fields  fields
// 		args    args
// 		want    *pb.GetLiveAnchorTaskEntryResp
// 		wantErr bool
// 	}{
// 		{
// 			fields: fields{
// 				channelCli: mockchannelCli,
// 			},
// 			args: args{
// 				ctx: context.Background(),
// 				in: &pb.GetLiveAnchorTaskEntryReq{
// 					Uid:       1,
// 					ChannelId: 1,
// 				},
// 			},
// 			want:    &pb.GetLiveAnchorTaskEntryResp{},
// 			wantErr: true,
// 		},
// 		{
// 			fields: fields{
// 				channelCli: mockchannelCli,
// 			},
// 			args: args{
// 				ctx: context.Background(),
// 				in: &pb.GetLiveAnchorTaskEntryReq{
// 					Uid:       1,
// 					ChannelId: 1,
// 				},
// 			},
// 			want:    &pb.GetLiveAnchorTaskEntryResp{},
// 			wantErr: false,
// 		},

// 		{
// 			fields: fields{
// 				channelCli:             mockchannelCli,
// 				store:                  mockStore,
// 				dyconfig:               mockconf,
// 				anchorcontractGoClient: mockanchorcontractCli,
// 				anchorCheckCli:         mockanchorcheckCli,
// 			},
// 			args: args{
// 				ctx: context.Background(),
// 				in: &pb.GetLiveAnchorTaskEntryReq{
// 					Uid:       1,
// 					ChannelId: 1,
// 				},
// 			},
// 			want: &pb.GetLiveAnchorTaskEntryResp{
// 				Title:      "星级任务",
// 				JumpUrl:    "1?pageName=live-task&immersion=1&anchor_uid=1",
// 				BaseImgurl: "1",
// 			},
// 			wantErr: false,
// 		},

// 		{
// 			fields: fields{
// 				channelCli: mockchannelCli,
// 			},
// 			args: args{
// 				ctx: context.Background(),
// 				in: &pb.GetLiveAnchorTaskEntryReq{
// 					Uid:       0,
// 					ChannelId: 1,
// 				},
// 			},
// 			want:    &pb.GetLiveAnchorTaskEntryResp{},
// 			wantErr: false,
// 		},

// 		// 通过签约
// 		{
// 			fields: fields{
// 				channelCli:             mockchannelCli,
// 				store:                  mockStore,
// 				dyconfig:               mockconf,
// 				anchorcontractGoClient: mockanchorcontractCli,
// 				anchorCheckCli:         mockanchorcheckCli,
// 			},
// 			args: args{
// 				ctx: context.Background(),
// 				in: &pb.GetLiveAnchorTaskEntryReq{
// 					Uid:       1,
// 					ChannelId: 1,
// 				},
// 			},
// 			want:    &pb.GetLiveAnchorTaskEntryResp{},
// 			wantErr: true,
// 		},
// 		{
// 			fields: fields{
// 				channelCli:             mockchannelCli,
// 				store:                  mockStore,
// 				dyconfig:               mockconf,
// 				anchorcontractGoClient: mockanchorcontractCli,
// 				anchorCheckCli:         mockanchorcheckCli,
// 			},
// 			args: args{
// 				ctx: context.Background(),
// 				in: &pb.GetLiveAnchorTaskEntryReq{
// 					Uid:       1,
// 					ChannelId: 1,
// 				},
// 			},
// 			want:    &pb.GetLiveAnchorTaskEntryResp{},
// 			wantErr: false,
// 		},

// 		// 白名单
// 		{
// 			fields: fields{
// 				channelCli:             mockchannelCli,
// 				store:                  mockStore,
// 				dyconfig:               mockconf,
// 				anchorcontractGoClient: mockanchorcontractCli,
// 				anchorCheckCli:         mockanchorcheckCli,
// 			},
// 			args: args{
// 				ctx: context.Background(),
// 				in: &pb.GetLiveAnchorTaskEntryReq{
// 					Uid:       1,
// 					ChannelId: 1,
// 				},
// 			},
// 			want:    &pb.GetLiveAnchorTaskEntryResp{},
// 			wantErr: true,
// 		},
// 		{
// 			fields: fields{
// 				channelCli:             mockchannelCli,
// 				store:                  mockStore,
// 				dyconfig:               mockconf,
// 				anchorcontractGoClient: mockanchorcontractCli,
// 				anchorCheckCli:         mockanchorcheckCli,
// 			},
// 			args: args{
// 				ctx: context.Background(),
// 				in: &pb.GetLiveAnchorTaskEntryReq{
// 					Uid:       1,
// 					ChannelId: 1,
// 				},
// 			},
// 			want:    &pb.GetLiveAnchorTaskEntryResp{},
// 			wantErr: false,
// 		},

// 		// 考核未通过
// 		{
// 			fields: fields{
// 				channelCli:             mockchannelCli,
// 				store:                  mockStore,
// 				dyconfig:               mockconf,
// 				anchorcontractGoClient: mockanchorcontractCli,
// 				anchorCheckCli:         mockanchorcheckCli,
// 			},
// 			args: args{
// 				ctx: context.Background(),
// 				in: &pb.GetLiveAnchorTaskEntryReq{
// 					Uid:       1,
// 					ChannelId: 1,
// 				},
// 			},
// 			want:    &pb.GetLiveAnchorTaskEntryResp{},
// 			wantErr: true,
// 		},
// 		{
// 			fields: fields{
// 				channelCli:             mockchannelCli,
// 				store:                  mockStore,
// 				dyconfig:               mockconf,
// 				anchorcontractGoClient: mockanchorcontractCli,
// 				anchorCheckCli:         mockanchorcheckCli,
// 			},
// 			args: args{
// 				ctx: context.Background(),
// 				in: &pb.GetLiveAnchorTaskEntryReq{
// 					Uid:       1,
// 					ChannelId: 1,
// 				},
// 			},
// 			want:    &pb.GetLiveAnchorTaskEntryResp{},
// 			wantErr: false,
// 		},
// 	}
// 	for _, tt := range tests {
// 		t.Run(tt.name, func(t *testing.T) {
// 			m := &AnchorLevelMgr{
// 				store:                      tt.fields.store,
// 				dyconfig:                   tt.fields.dyconfig,
// 				LocalCache:                 tt.fields.LocalCache,
// 				channelLiveMgr:             tt.fields.channelLiveMgr,
// 				channelLiveStats:           tt.fields.channelLiveStats,
// 				channelRecommendSvr:        tt.fields.channelRecommendSvr,
// 				entertainmentRecommendBack: tt.fields.entertainmentRecommendBack,
// 				anchorcontractGoClient:     tt.fields.anchorcontractGoClient,
// 				reporter:                   tt.fields.reporter,
// 				accountCli:                 tt.fields.accountCli,
// 				channelbackgroundCli:       tt.fields.channelbackgroundCli,
// 				channelLotteryCli:          tt.fields.channelLotteryCli,
// 				channelLiveFansCli:         tt.fields.channelLiveFansCli,
// 				channelVotePkGoCli:         tt.fields.channelVotePkGoCli,
// 				publicCli:                  tt.fields.publicCli,
// 				apiCli:                     tt.fields.apiCli,
// 				userOlCli:                  tt.fields.userOlCli,
// 				channelCli:                 mockchannelCli,
// 				anchorCheckCli:             mockanchorcheckCli,
// 			}
// 			got, err := m.GetLiveAnchorTaskEntry(tt.args.ctx, tt.args.in)
// 			if (err != nil) != tt.wantErr {
// 				t.Errorf("AnchorLevelMgr.GetLiveAnchorTaskEntry() error = %v, wantErr %v", err, tt.wantErr)
// 				return
// 			}
// 			if !reflect.DeepEqual(got, tt.want) {
// 				//t.Errorf("AnchorLevelMgr.GetLiveAnchorTaskEntry() = %v, want %v", got, tt.want)
// 			}
// 		})
// 	}
// }

// // go test -timeout 30s -run ^TestAnchorLevelMgr_GetLiveAnchorLevel$ golang.52tt.com/services/anchor-level/manager -v -count=1
// func TestAnchorLevelMgr_GetLiveAnchorLevel(t *testing.T) {

// 	ctl := gomock.NewController(t)
// 	defer ctl.Finish()

// 	type fields struct {
// 		store                      store.IStore
// 		dyconfig                   *conf.SDyConfigHandler
// 		LocalCache                 *LocalCache
// 		channelLiveMgr             channellivemgr.IClient
// 		channelLiveStats           channellivestats.IClient
// 		channelRecommendSvr        channel_recommend_svr.IClient
// 		entertainmentRecommendBack entertainmentrecommendback.IClient
// 		anchorcontractGoClient     anchorcontract_go.IClient
// 		reporter                   report.IFeishuReporterV2
// 		accountCli                 account.IClient
// 		channelbackgroundCli       channelbackground.IClient
// 		channelLotteryCli          channellottery.IClient
// 		channelLiveFansCli         channellivefans.IClient
// 		channelVotePkGoCli         channelvotepkgo.IClient
// 		publicCli                  public.IClient
// 		apiCli                     apicenter.IClient
// 		userOlCli                  userol.IClient
// 	}
// 	type args struct {
// 		ctx context.Context
// 		in  *pb.GetLiveAnchorLevelReq
// 	}
// 	tests := []struct {
// 		name    string
// 		fields  fields
// 		args    args
// 		wantOut *pb.GetLiveAnchorLevelResp
// 		wantErr bool
// 	}{
// 		{
// 			fields: fields{
// 				LocalCache: &LocalCache{
// 					uid2LevelCerts: [2]map[uint32]*pb.LiveAnchorLevelInfo{
// 						{
// 							1: &pb.LiveAnchorLevelInfo{},
// 							2: &pb.LiveAnchorLevelInfo{},
// 						},
// 					},
// 				},
// 			},
// 			args: args{
// 				ctx: context.TODO(),
// 				in:  &pb.GetLiveAnchorLevelReq{},
// 			},
// 			wantOut: &pb.GetLiveAnchorLevelResp{
// 				List: []*pb.LiveAnchorLevelInfo{
// 					{}, {},
// 				},
// 			},
// 		},
// 		{
// 			fields: fields{
// 				LocalCache: &LocalCache{
// 					uid2LevelCerts: [2]map[uint32]*pb.LiveAnchorLevelInfo{
// 						{
// 							1: &pb.LiveAnchorLevelInfo{Level: pb.ANCHOR_LEVEL_TYPE_ANCHOR_LEVEL_TYPE_A},
// 							2: &pb.LiveAnchorLevelInfo{},
// 						},
// 					},
// 				},
// 			},
// 			args: args{
// 				ctx: context.TODO(),
// 				in: &pb.GetLiveAnchorLevelReq{
// 					Level: pb.ANCHOR_LEVEL_TYPE_ANCHOR_LEVEL_TYPE_A,
// 				},
// 			},
// 			wantOut: &pb.GetLiveAnchorLevelResp{
// 				List: []*pb.LiveAnchorLevelInfo{
// 					{Level: pb.ANCHOR_LEVEL_TYPE_ANCHOR_LEVEL_TYPE_A},
// 				},
// 			},
// 		},
// 	}
// 	for _, tt := range tests {
// 		t.Run(tt.name, func(t *testing.T) {
// 			m := &AnchorLevelMgr{
// 				store:                      tt.fields.store,
// 				dyconfig:                   tt.fields.dyconfig,
// 				LocalCache:                 tt.fields.LocalCache,
// 				channelLiveMgr:             tt.fields.channelLiveMgr,
// 				channelLiveStats:           tt.fields.channelLiveStats,
// 				channelRecommendSvr:        tt.fields.channelRecommendSvr,
// 				entertainmentRecommendBack: tt.fields.entertainmentRecommendBack,
// 				anchorcontractGoClient:     tt.fields.anchorcontractGoClient,
// 				reporter:                   tt.fields.reporter,
// 				accountCli:                 tt.fields.accountCli,
// 				channelbackgroundCli:       tt.fields.channelbackgroundCli,
// 				channelLotteryCli:          tt.fields.channelLotteryCli,
// 				channelLiveFansCli:         tt.fields.channelLiveFansCli,
// 				channelVotePkGoCli:         tt.fields.channelVotePkGoCli,
// 				publicCli:                  tt.fields.publicCli,
// 				apiCli:                     tt.fields.apiCli,
// 				userOlCli:                  tt.fields.userOlCli,
// 			}
// 			gotOut, err := m.GetLiveAnchorLevel(tt.args.ctx, tt.args.in)
// 			if (err != nil) != tt.wantErr {
// 				t.Errorf("AnchorLevelMgr.GetLiveAnchorLevel() error = %v, wantErr %v", err, tt.wantErr)
// 				return
// 			}
// 			if !reflect.DeepEqual(gotOut, tt.wantOut) {
// 				t.Errorf("AnchorLevelMgr.GetLiveAnchorLevel() = %v, want %v", gotOut, tt.wantOut)
// 			}
// 		})
// 	}
// }

// // go test -timeout 30s -run ^TestAnchorLevelMgr_GetAnchorLevelMonthTask$ golang.52tt.com/services/anchor-level/manager -v -count=1
// func TestAnchorLevelMgr_GetAnchorLevelMonthTask(t *testing.T) {

// 	ctl := gomock.NewController(t)
// 	defer ctl.Finish()

// 	/*
// 		mockStore := mocks.NewMockIStore(ctl)
// 		mockchannelCli := channelmock.NewMockIClient(ctl)
// 		mockconf := mocks.NewMockISDyConfigHandler(ctl)
// 		mockanchorcontractCli := anchorcontractmock.NewMockIClient(ctl)
// 		mockanchorcheckCli := anchorchecktmock.NewMockIClient(ctl)
// 		mockChannelLiveStatsCli := mockChannelLiveStats.NewMockIClient(ctl)
// 	*/
// 	type fields struct {
// 		store                      store.IStore
// 		dyconfig                   conf.ISDyConfigHandler
// 		LocalCache                 *LocalCache
// 		channelLiveMgr             channellivemgr.IClient
// 		channelLiveStats           channellivestats.IClient
// 		channelRecommendSvr        channel_recommend_svr.IClient
// 		entertainmentRecommendBack entertainmentrecommendback.IClient
// 		anchorcontractGoClient     anchorcontract_go.IClient
// 		reporter                   report.IFeishuReporterV2
// 		accountCli                 account.IClient
// 		channelbackgroundCli       channelbackground.IClient
// 		channelLotteryCli          channellottery.IClient
// 		channelLiveFansCli         channellivefans.IClient
// 		channelVotePkGoCli         channelvotepkgo.IClient
// 		publicCli                  public.IClient
// 		apiCli                     apicenter.IClient
// 		userOlCli                  userol.IClient
// 		channelCli                 channel.IClient
// 		anchorCheckCli             anchorcheck.IClient
// 	}
// 	type args struct {
// 		ctx context.Context
// 		in  *pb.UidReq
// 	}
// 	tests := []struct {
// 		name    string
// 		fields  fields
// 		args    args
// 		wantOut *pb.GetAnchorLevelMonthTaskResp
// 		wantErr bool
// 	}{
// 		{
// 			fields:  fields{},
// 			args:    args{ctx: context.Background(), in: &pb.UidReq{Uid: 0}},
// 			wantOut: &pb.GetAnchorLevelMonthTaskResp{},
// 		},

// 		/*
// 			{
// 				fields: fields{
// 					store:                  mockStore,
// 					channelCli:             mockchannelCli,
// 					dyconfig:               mockconf,
// 					anchorcontractGoClient: mockanchorcontractCli,
// 					anchorCheckCli:         mockanchorcheckCli,
// 					channelLiveStats:       mockChannelLiveStatsCli,
// 				},
// 				args:    args{ctx: context.Background(), in: &pb.UidReq{Uid: 1}},
// 				wantOut: &pb.GetAnchorLevelMonthTaskResp{},
// 			},
// 		*/
// 	}
// 	for _, tt := range tests {
// 		t.Run(tt.name, func(t *testing.T) {
// 			m := &AnchorLevelMgr{
// 				store:                      tt.fields.store,
// 				dyconfig:                   tt.fields.dyconfig,
// 				LocalCache:                 tt.fields.LocalCache,
// 				channelLiveMgr:             tt.fields.channelLiveMgr,
// 				channelLiveStats:           tt.fields.channelLiveStats,
// 				channelRecommendSvr:        tt.fields.channelRecommendSvr,
// 				entertainmentRecommendBack: tt.fields.entertainmentRecommendBack,
// 				anchorcontractGoClient:     tt.fields.anchorcontractGoClient,
// 				reporter:                   tt.fields.reporter,
// 				accountCli:                 tt.fields.accountCli,
// 				channelbackgroundCli:       tt.fields.channelbackgroundCli,
// 				channelLotteryCli:          tt.fields.channelLotteryCli,
// 				channelLiveFansCli:         tt.fields.channelLiveFansCli,
// 				channelVotePkGoCli:         tt.fields.channelVotePkGoCli,
// 				publicCli:                  tt.fields.publicCli,
// 				apiCli:                     tt.fields.apiCli,
// 				userOlCli:                  tt.fields.userOlCli,
// 			}
// 			gotOut, err := m.GetAnchorLevelMonthTask(tt.args.ctx, tt.args.in)
// 			if (err != nil) != tt.wantErr {
// 				t.Errorf("AnchorLevelMgr.GetAnchorLevelMonthTask() error = %v, wantErr %v", err, tt.wantErr)
// 				return
// 			}
// 			if !reflect.DeepEqual(gotOut, tt.wantOut) {
// 				t.Errorf("AnchorLevelMgr.GetAnchorLevelMonthTask() = %v, want %v", gotOut, tt.wantOut)
// 			}
// 		})
// 	}
// }

// // go test -timeout 30s -run ^TestAnchorLevelMgr_GetLiveAnchorLevelByUid$ golang.52tt.com/services/anchor-level/manager -v -count=1
// func TestAnchorLevelMgr_GetLiveAnchorLevelByUid(t *testing.T) {

// 	ctl := gomock.NewController(t)
// 	defer ctl.Finish()

// 	type fields struct {
// 		store                      store.IStore
// 		dyconfig                   *conf.SDyConfigHandler
// 		LocalCache                 *LocalCache
// 		channelLiveMgr             channellivemgr.IClient
// 		channelLiveStats           channellivestats.IClient
// 		channelRecommendSvr        channel_recommend_svr.IClient
// 		entertainmentRecommendBack entertainmentrecommendback.IClient
// 		anchorcontractGoClient     anchorcontract_go.IClient
// 		reporter                   report.IFeishuReporterV2
// 		accountCli                 account.IClient
// 		channelbackgroundCli       channelbackground.IClient
// 		channelLotteryCli          channellottery.IClient
// 		channelLiveFansCli         channellivefans.IClient
// 		channelVotePkGoCli         channelvotepkgo.IClient
// 		publicCli                  public.IClient
// 		apiCli                     apicenter.IClient
// 		userOlCli                  userol.IClient
// 	}
// 	type args struct {
// 		ctx context.Context
// 		in  *pb.GetLiveAnchorLevelByUidReq
// 	}
// 	tests := []struct {
// 		name    string
// 		fields  fields
// 		args    args
// 		wantOut *pb.GetLiveAnchorLevelByUidResp
// 		wantErr bool
// 	}{
// 		{

// 			fields: fields{
// 				LocalCache: &LocalCache{
// 					uid2LevelCerts: [2]map[uint32]*pb.LiveAnchorLevelInfo{
// 						{
// 							1: &pb.LiveAnchorLevelInfo{},
// 						},
// 					},
// 				},
// 			},
// 			args: args{
// 				ctx: context.Background(),
// 				in:  &pb.GetLiveAnchorLevelByUidReq{Uids: []uint32{1}},
// 			},
// 			wantOut: &pb.GetLiveAnchorLevelByUidResp{
// 				Uid2AnchorLevel: map[uint32]*pb.LiveAnchorLevelInfo{
// 					1: &pb.LiveAnchorLevelInfo{},
// 				},
// 			},
// 			wantErr: false,
// 		},
// 	}
// 	for _, tt := range tests {
// 		t.Run(tt.name, func(t *testing.T) {
// 			m := &AnchorLevelMgr{
// 				store:                      tt.fields.store,
// 				dyconfig:                   tt.fields.dyconfig,
// 				LocalCache:                 tt.fields.LocalCache,
// 				channelLiveMgr:             tt.fields.channelLiveMgr,
// 				channelLiveStats:           tt.fields.channelLiveStats,
// 				channelRecommendSvr:        tt.fields.channelRecommendSvr,
// 				entertainmentRecommendBack: tt.fields.entertainmentRecommendBack,
// 				anchorcontractGoClient:     tt.fields.anchorcontractGoClient,
// 				reporter:                   tt.fields.reporter,
// 				accountCli:                 tt.fields.accountCli,
// 				channelbackgroundCli:       tt.fields.channelbackgroundCli,
// 				channelLotteryCli:          tt.fields.channelLotteryCli,
// 				channelLiveFansCli:         tt.fields.channelLiveFansCli,
// 				channelVotePkGoCli:         tt.fields.channelVotePkGoCli,
// 				publicCli:                  tt.fields.publicCli,
// 				apiCli:                     tt.fields.apiCli,
// 				userOlCli:                  tt.fields.userOlCli,
// 			}
// 			gotOut, err := m.GetLiveAnchorLevelByUid(tt.args.ctx, tt.args.in)
// 			if (err != nil) != tt.wantErr {
// 				t.Errorf("AnchorLevelMgr.GetLiveAnchorLevelByUid() error = %v, wantErr %v", err, tt.wantErr)
// 				return
// 			}
// 			if !reflect.DeepEqual(gotOut, tt.wantOut) {
// 				//	t.Errorf("AnchorLevelMgr.GetLiveAnchorLevelByUid() = %v, want %v", gotOut, tt.wantOut)
// 			}
// 		})
// 	}
// }

// func TestAnchorLevelMgr_LiveAnchorLevelMonthSettleTimer(t *testing.T) {
// 	type fields struct {
// 		store                      store.IStore
// 		dyconfig                   *conf.SDyConfigHandler
// 		LocalCache                 *LocalCache
// 		channelLiveMgr             channellivemgr.IClient
// 		channelLiveStats           channellivestats.IClient
// 		channelRecommendSvr        channel_recommend_svr.IClient
// 		entertainmentRecommendBack entertainmentrecommendback.IClient
// 		anchorcontractGoClient     anchorcontract_go.IClient
// 		reporter                   report.IFeishuReporterV2
// 		accountCli                 account.IClient
// 		channelbackgroundCli       channelbackground.IClient
// 		channelLotteryCli          channellottery.IClient
// 		channelLiveFansCli         channellivefans.IClient
// 		channelVotePkGoCli         channelvotepkgo.IClient
// 		publicCli                  public.IClient
// 		apiCli                     apicenter.IClient
// 		userOlCli                  userol.IClient
// 	}
// 	tests := []struct {
// 		name   string
// 		fields fields
// 	}{
// 		// TODO: Add test cases.
// 	}
// 	for _, tt := range tests {
// 		t.Run(tt.name, func(t *testing.T) {
// 			m := &AnchorLevelMgr{
// 				store:                      tt.fields.store,
// 				dyconfig:                   tt.fields.dyconfig,
// 				LocalCache:                 tt.fields.LocalCache,
// 				channelLiveMgr:             tt.fields.channelLiveMgr,
// 				channelLiveStats:           tt.fields.channelLiveStats,
// 				channelRecommendSvr:        tt.fields.channelRecommendSvr,
// 				entertainmentRecommendBack: tt.fields.entertainmentRecommendBack,
// 				anchorcontractGoClient:     tt.fields.anchorcontractGoClient,
// 				reporter:                   tt.fields.reporter,
// 				accountCli:                 tt.fields.accountCli,
// 				channelbackgroundCli:       tt.fields.channelbackgroundCli,
// 				channelLotteryCli:          tt.fields.channelLotteryCli,
// 				channelLiveFansCli:         tt.fields.channelLiveFansCli,
// 				channelVotePkGoCli:         tt.fields.channelVotePkGoCli,
// 				publicCli:                  tt.fields.publicCli,
// 				apiCli:                     tt.fields.apiCli,
// 				userOlCli:                  tt.fields.userOlCli,
// 			}
// 			m.LiveAnchorLevelMonthSettleTimer()
// 		})
// 	}
// }

// // go test -timeout 30s -run ^TestAnchorLevelMgr_LiveAnchorLevelMonthSettle$ golang.52tt.com/services/anchor-level/manager -v -count=1
// func TestAnchorLevelMgr_LiveAnchorLevelMonthSettle(t *testing.T) {
// 	ctl := gomock.NewController(t)
// 	defer ctl.Finish()

// 	mockStore := mocks.NewMockIStore(ctl)
// 	mockReport := mocks.NewMockIFeishuReporterV2(ctl)
// 	mockchannelliveMgr := mockchannellivemgr.NewMockIClient(ctl)
// 	mockChannelLiveStatsCli := mockChannelLiveStats.NewMockIClient(ctl)

// 	settleMonthTm := time.Date(2022, 12, 1, 0, 0, 0, 0, time.Local)
// 	taskList := []*store.LiveAnchorMonthTask{}
// 	taskList1 := []*store.LiveAnchorMonthTask{{AnchorLevel: 1}}
// 	anchorlevel2Name := map[uint32]*store.LiveAnchorMonthTask{}
// 	anchorList := []*channellivemgrPB.AnchorInfo{{Uid: 1}}
// 	serr := protocol.NewServerError(-2)

// 	gomock.InOrder(
// 		mockReport.EXPECT().SendInfo(gomock.Any()).Return(nil),
// 		mockStore.EXPECT().GetLiveAnchorMonthTask().Return(nil, nil, errors.New("")),
// 		mockReport.EXPECT().SendError(gomock.Any()).Return(nil),

// 		mockReport.EXPECT().SendInfo(gomock.Any()).Return(nil),
// 		mockStore.EXPECT().GetLiveAnchorMonthTask().Return(taskList, anchorlevel2Name, nil),
// 		mockReport.EXPECT().SendInfo(gomock.Any()).Return(nil),

// 		mockReport.EXPECT().SendInfo(gomock.Any()).Return(nil),
// 		mockStore.EXPECT().GetLiveAnchorMonthTask().Return(taskList1, anchorlevel2Name, nil),
// 		mockchannelliveMgr.EXPECT().GetAnchorList(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil),
// 		mockReport.EXPECT().SendInfo(gomock.Any()).Return(nil),

// 		mockReport.EXPECT().SendInfo(gomock.Any()).Return(nil),
// 		mockStore.EXPECT().GetLiveAnchorMonthTask().Return(taskList1, anchorlevel2Name, nil),
// 		mockchannelliveMgr.EXPECT().GetAnchorList(gomock.Any(), gomock.Any(), gomock.Any()).Return(anchorList, nil),
// 		mockChannelLiveStatsCli.EXPECT().BatchGetAnchorMonthlyStatsByUid(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, serr),
// 		mockReport.EXPECT().SendError(gomock.Any()).Return(nil),
// 	)

// 	type fields struct {
// 		store                      store.IStore
// 		dyconfig                   *conf.SDyConfigHandler
// 		LocalCache                 *LocalCache
// 		channelLiveMgr             channellivemgr.IClient
// 		channelLiveStats           channellivestats.IClient
// 		channelRecommendSvr        channel_recommend_svr.IClient
// 		entertainmentRecommendBack entertainmentrecommendback.IClient
// 		anchorcontractGoClient     anchorcontract_go.IClient
// 		reporter                   report.IFeishuReporterV2
// 		accountCli                 account.IClient
// 		channelbackgroundCli       channelbackground.IClient
// 		channelLotteryCli          channellottery.IClient
// 		channelLiveFansCli         channellivefans.IClient
// 		channelVotePkGoCli         channelvotepkgo.IClient
// 		publicCli                  public.IClient
// 		apiCli                     apicenter.IClient
// 		userOlCli                  userol.IClient
// 	}
// 	type args struct {
// 		settleMonthTm time.Time
// 	}
// 	tests := []struct {
// 		name    string
// 		fields  fields
// 		args    args
// 		wantErr bool
// 	}{
// 		{
// 			fields: fields{
// 				reporter: mockReport,
// 				store:    mockStore,
// 			},
// 			args: args{
// 				settleMonthTm: settleMonthTm,
// 			},
// 			wantErr: true,
// 		},
// 		{
// 			fields: fields{
// 				reporter:       mockReport,
// 				store:          mockStore,
// 				channelLiveMgr: mockchannelliveMgr,
// 			},
// 			args: args{
// 				settleMonthTm: settleMonthTm,
// 			},
// 			wantErr: false,
// 		},

// 		{
// 			fields: fields{
// 				reporter:       mockReport,
// 				store:          mockStore,
// 				channelLiveMgr: mockchannelliveMgr,
// 			},
// 			args: args{
// 				settleMonthTm: settleMonthTm,
// 			},
// 			wantErr: false,
// 		},

// 		{
// 			fields: fields{
// 				reporter:         mockReport,
// 				store:            mockStore,
// 				channelLiveMgr:   mockchannelliveMgr,
// 				channelLiveStats: mockChannelLiveStatsCli,
// 			},
// 			args: args{
// 				settleMonthTm: settleMonthTm,
// 			},
// 			wantErr: true,
// 		},
// 	}
// 	for _, tt := range tests {
// 		t.Run(tt.name, func(t *testing.T) {
// 			m := &AnchorLevelMgr{
// 				store:                      tt.fields.store,
// 				dyconfig:                   tt.fields.dyconfig,
// 				LocalCache:                 tt.fields.LocalCache,
// 				channelLiveMgr:             tt.fields.channelLiveMgr,
// 				channelLiveStats:           tt.fields.channelLiveStats,
// 				channelRecommendSvr:        tt.fields.channelRecommendSvr,
// 				entertainmentRecommendBack: tt.fields.entertainmentRecommendBack,
// 				anchorcontractGoClient:     tt.fields.anchorcontractGoClient,
// 				reporter:                   tt.fields.reporter,
// 				accountCli:                 tt.fields.accountCli,
// 				channelbackgroundCli:       tt.fields.channelbackgroundCli,
// 				channelLotteryCli:          tt.fields.channelLotteryCli,
// 				channelLiveFansCli:         tt.fields.channelLiveFansCli,
// 				channelVotePkGoCli:         tt.fields.channelVotePkGoCli,
// 				publicCli:                  tt.fields.publicCli,
// 				apiCli:                     tt.fields.apiCli,
// 				userOlCli:                  tt.fields.userOlCli,
// 			}
// 			if err := m.LiveAnchorLevelMonthSettle(tt.args.settleMonthTm); (err != nil) != tt.wantErr {
// 				t.Errorf("AnchorLevelMgr.LiveAnchorLevelMonthSettle() error = %v, wantErr %v", err, tt.wantErr)
// 			}
// 		})
// 	}
// }

// // go test -timeout 30s -run ^TestAnchorLevelMgr_LiveAnchorLevelMonthAward$ golang.52tt.com/services/anchor-level/manager -v -count=1
// func TestAnchorLevelMgr_LiveAnchorLevelMonthAward(t *testing.T) {
// 	ctl := gomock.NewController(t)
// 	defer ctl.Finish()

// 	mockStore := mocks.NewMockIStore(ctl)
// 	mockReport := mocks.NewMockIFeishuReporterV2(ctl)

// 	settleMonthTm := time.Date(2022, 12, 1, 0, 0, 0, 0, time.Local)
// 	level2AwardConfig := map[uint32]*store.LiveAnchorMonthAwardConfig{
// 		1: {
// 			ChannelLotteryDays: 1,
// 		},
// 		4: {
// 			ChannelLotteryDays: 1,
// 		},
// 		5: {
// 			ChannelLotteryDays: 1,
// 			ChannelVoteDays:    1,
// 		},
// 	}
// 	awardList0 := []*store.LiveAnchorMonthTaskAwardLog{}
// 	awardList := []*store.LiveAnchorMonthTaskAwardLog{
// 		{AnchorUid: 101, IsDone: true},
// 	}

// 	gomock.InOrder(
// 		mockReport.EXPECT().SendInfo(gomock.Any()).Return(nil),
// 		mockStore.EXPECT().GetLiveAnchorMonthAwardConfig().Return(nil, level2AwardConfig, errors.New("failx")),
// 		mockReport.EXPECT().SendError(gomock.Any()).Return(nil),

// 		mockReport.EXPECT().SendInfo(gomock.Any()).Return(nil),
// 		mockStore.EXPECT().GetLiveAnchorMonthAwardConfig().Return(nil, level2AwardConfig, nil),
// 		mockStore.EXPECT().GetLiveAnchorMonthTaskAwardLog(gomock.Any(), gomock.Any(), gomock.Any()).Return(awardList, errors.New("failx")),
// 		mockReport.EXPECT().SendError(gomock.Any()).Return(nil),

// 		mockReport.EXPECT().SendInfo(gomock.Any()).Return(nil),
// 		mockStore.EXPECT().GetLiveAnchorMonthAwardConfig().Return(nil, level2AwardConfig, nil),
// 		mockStore.EXPECT().GetLiveAnchorMonthTaskAwardLog(gomock.Any(), gomock.Any(), gomock.Any()).Return(awardList0, nil),
// 		mockReport.EXPECT().SendInfo(gomock.Any()).Return(nil),

// 		mockReport.EXPECT().SendInfo(gomock.Any()).Return(nil),
// 		mockStore.EXPECT().GetLiveAnchorMonthAwardConfig().Return(nil, level2AwardConfig, nil),
// 		mockStore.EXPECT().GetLiveAnchorMonthTaskAwardLog(gomock.Any(), gomock.Any(), gomock.Any()).Return(awardList, nil),
// 		mockReport.EXPECT().SendInfo(gomock.Any()).Return(nil),
// 	)

// 	type fields struct {
// 		store                      store.IStore
// 		dyconfig                   *conf.SDyConfigHandler
// 		LocalCache                 *LocalCache
// 		channelLiveMgr             channellivemgr.IClient
// 		channelLiveStats           channellivestats.IClient
// 		channelRecommendSvr        channel_recommend_svr.IClient
// 		entertainmentRecommendBack entertainmentrecommendback.IClient
// 		anchorcontractGoClient     anchorcontract_go.IClient
// 		reporter                   report.IFeishuReporterV2
// 		accountCli                 account.IClient
// 		channelbackgroundCli       channelbackground.IClient
// 		channelLotteryCli          channellottery.IClient
// 		channelLiveFansCli         channellivefans.IClient
// 		channelVotePkGoCli         channelvotepkgo.IClient
// 		publicCli                  public.IClient
// 		apiCli                     apicenter.IClient
// 		userOlCli                  userol.IClient
// 	}
// 	type args struct {
// 		settleMonthTm time.Time
// 	}
// 	tests := []struct {
// 		name   string
// 		fields fields
// 		args   args
// 	}{
// 		{
// 			fields: fields{store: mockStore, reporter: mockReport},
// 			args:   args{settleMonthTm: settleMonthTm},
// 		},
// 		{
// 			fields: fields{store: mockStore, reporter: mockReport},
// 			args:   args{settleMonthTm: settleMonthTm},
// 		},
// 		{
// 			fields: fields{store: mockStore, reporter: mockReport},
// 			args:   args{settleMonthTm: settleMonthTm},
// 		},
// 		{
// 			fields: fields{store: mockStore, reporter: mockReport},
// 			args:   args{settleMonthTm: settleMonthTm},
// 		},
// 	}
// 	for _, tt := range tests {
// 		t.Run(tt.name, func(t *testing.T) {
// 			m := &AnchorLevelMgr{
// 				store:                      tt.fields.store,
// 				dyconfig:                   tt.fields.dyconfig,
// 				LocalCache:                 tt.fields.LocalCache,
// 				channelLiveMgr:             tt.fields.channelLiveMgr,
// 				channelLiveStats:           tt.fields.channelLiveStats,
// 				channelRecommendSvr:        tt.fields.channelRecommendSvr,
// 				entertainmentRecommendBack: tt.fields.entertainmentRecommendBack,
// 				anchorcontractGoClient:     tt.fields.anchorcontractGoClient,
// 				reporter:                   tt.fields.reporter,
// 				accountCli:                 tt.fields.accountCli,
// 				channelbackgroundCli:       tt.fields.channelbackgroundCli,
// 				channelLotteryCli:          tt.fields.channelLotteryCli,
// 				channelLiveFansCli:         tt.fields.channelLiveFansCli,
// 				channelVotePkGoCli:         tt.fields.channelVotePkGoCli,
// 				publicCli:                  tt.fields.publicCli,
// 				apiCli:                     tt.fields.apiCli,
// 				userOlCli:                  tt.fields.userOlCli,
// 			}
// 			m.LiveAnchorLevelMonthAward(tt.args.settleMonthTm)
// 		})
// 	}
// }

// /*
// // go test -timeout 30s -run ^TestAnchorLevelMgr_awardUser$ golang.52tt.com/services/anchor-level/manager -v -count=1
// func TestAnchorLevelMgr_awardUser(t *testing.T) {

// 	ctl := gomock.NewController(t)
// 	defer ctl.Finish()

// 	mockchannelLotteryCli := channelLotterymock.NewMockIClient(ctl)
// 	mockchannelvotepkgo := channelvotepkgomock.NewMockIClient(ctl)
// 	mockchannelLiveFansCli := channelLiveFansmock.NewMockIClient(ctl)
// 	mockchannelbackgroundCli := channelbackgroundmock.NewMockIClient(ctl)
// 	mockchannelRecommendSvr := recommendmock.NewMockIClient(ctl)
// 	mockchannelCli := channelmock.NewMockIClient(ctl)
// 	//mockStore := mocks.NewMockIStore(ctl)

// 	var (
// 		info = &store.LiveAnchorMonthTaskAwardLog{
// 			AnchorLevel: 4,
// 		}
// 		level2AwardConfig = map[uint32]*store.LiveAnchorMonthAwardConfig{
// 			1: {
// 				ChannelLotteryDays: 1,
// 			},
// 			4: {
// 				ChannelLotteryDays: 1,
// 			},
// 			5: {
// 				ChannelLotteryDays: 1,
// 				ChannelVoteDays:    1,
// 			},
// 		}
// 		err = protocol.NewServerError(-2, "")

// 		info2 = &store.LiveAnchorMonthTaskAwardLog{
// 			AnchorLevel: 5,
// 		}

// 		info3 = &store.LiveAnchorMonthTaskAwardLog{
// 			AnchorLevel:      5,
// 			AdvancedTaskDone: true,
// 		}
// 		info4 = &store.LiveAnchorMonthTaskAwardLog{
// 			AnchorLevel:      5,
// 			AdvancedTaskDone: true,
// 		}

// 		info5 = &store.LiveAnchorMonthTaskAwardLog{
// 			AnchorLevel:      1,
// 			AdvancedTaskDone: true,
// 		}

// 		info6 = &store.LiveAnchorMonthTaskAwardLog{
// 			AnchorLevel:      10,
// 			AdvancedTaskDone: true,
// 		}

// 		displayId   = uint32(1)
// 		channelInfo = &channelsvr.ChannelSimpleInfo{
// 			DisplayId: &displayId,
// 		}
// 	)

// 	gomock.InOrder(
// 		mockchannelCli.EXPECT().GetChannelSimpleInfo(gomock.Any(), gomock.Any(), gomock.Any()).Return(channelInfo, nil),
// 		mockchannelLotteryCli.EXPECT().SetLotteryOpenList(gomock.Any(), gomock.Any()).Return(nil, err),

// 		mockchannelCli.EXPECT().GetChannelSimpleInfo(gomock.Any(), gomock.Any(), gomock.Any()).Return(channelInfo, nil),
// 		mockchannelLotteryCli.EXPECT().SetLotteryOpenList(gomock.Any(), gomock.Any()).Return(nil, nil),
// 		mockchannelvotepkgo.EXPECT().BatchSetChannelVote(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, err),

// 		mockchannelCli.EXPECT().GetChannelSimpleInfo(gomock.Any(), gomock.Any(), gomock.Any()).Return(channelInfo, nil),
// 		mockchannelLotteryCli.EXPECT().SetLotteryOpenList(gomock.Any(), gomock.Any()).Return(nil, nil),
// 		mockchannelvotepkgo.EXPECT().BatchSetChannelVote(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil),
// 		mockchannelLiveFansCli.EXPECT().GrantAnchorPlate(gomock.Any(), gomock.Any()).Return(nil, err),

// 		mockchannelCli.EXPECT().GetChannelSimpleInfo(gomock.Any(), gomock.Any(), gomock.Any()).Return(channelInfo, nil),
// 		mockchannelLotteryCli.EXPECT().SetLotteryOpenList(gomock.Any(), gomock.Any()).Return(nil, nil),
// 		mockchannelvotepkgo.EXPECT().BatchSetChannelVote(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil),
// 		mockchannelLiveFansCli.EXPECT().GrantAnchorPlate(gomock.Any(), gomock.Any()).Return(nil, nil),
// 		mockchannelbackgroundCli.EXPECT().GiveChannelLiveBg(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(err),

// 		mockchannelCli.EXPECT().GetChannelSimpleInfo(gomock.Any(), gomock.Any(), gomock.Any()).Return(channelInfo, nil),
// 		mockchannelLotteryCli.EXPECT().SetLotteryOpenList(gomock.Any(), gomock.Any()).Return(nil, nil),
// 		mockchannelvotepkgo.EXPECT().BatchSetChannelVote(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil),
// 		mockchannelLiveFansCli.EXPECT().GrantAnchorPlate(gomock.Any(), gomock.Any()).Return(nil, nil),
// 		mockchannelbackgroundCli.EXPECT().GiveChannelLiveBg(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil),
// 		mockchannelRecommendSvr.EXPECT().BatGrantFlowCard(gomock.Any(), gomock.Any()).Return(nil, nil),
// 		//mockchannelRecommendSvr.EXPECT().GrantFlowCard(gomock.Any(), gomock.Any()).Return(nil, nil),
// 		//mockchannelRecommendSvr.EXPECT().GrantFlowCard(gomock.Any(), gomock.Any()).Return(nil, err),

// 		//mockchannelLiveFansCli.EXPECT().GrantAnchorPlate(gomock.Any(), gomock.Any()).Return(nil, nil),
// 		//mockchannelbackgroundCli.EXPECT().GiveChannelLiveBg(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil),
// 		mockchannelRecommendSvr.EXPECT().BatGrantFlowCard(gomock.Any(), gomock.Any()).Return(nil, nil),
// 		//mockchannelRecommendSvr.EXPECT().GrantFlowCard(gomock.Any(), gomock.Any()).Return(nil, err),
// 	)

// 	type fields struct {
// 		store                      store.IStore
// 		dyconfig                   *conf.SDyConfigHandler
// 		LocalCache                 *LocalCache
// 		channelLiveMgr             channellivemgr.IClient
// 		channelLiveStats           channellivestats.IClient
// 		channelRecommendSvr        channel_recommend_svr.IClient
// 		entertainmentRecommendBack entertainmentrecommendback.IClient
// 		anchorcontractGoClient     anchorcontract_go.IClient
// 		reporter                   report.IFeishuReporterV2
// 		accountCli                 account.IClient
// 		channelbackgroundCli       channelbackground.IClient
// 		channelLotteryCli          channellottery.IClient
// 		channelLiveFansCli         channellivefans.IClient
// 		channelVotePkGoCli         channelvotepkgo.IClient
// 		publicCli                  public.IClient
// 		apiCli                     apicenter.IClient
// 		userOlCli                  userol.IClient
// 		channelCli                 channel.IClient
// 	}
// 	type args struct {
// 		info              *store.LiveAnchorMonthTaskAwardLog
// 		level2AwardConfig map[uint32]*store.LiveAnchorMonthAwardConfig
// 	}
// 	tests := []struct {
// 		name    string
// 		fields  fields
// 		args    args
// 		wantErr bool
// 	}{
// 		{ //1
// 			fields: fields{
// 				channelLotteryCli: mockchannelLotteryCli,
// 				channelCli:        mockchannelCli,
// 			},
// 			args: args{
// 				info:              info,
// 				level2AwardConfig: level2AwardConfig,
// 			},
// 			wantErr: true,
// 		},
// 		{ // 2
// 			fields: fields{
// 				channelLotteryCli:  mockchannelLotteryCli,
// 				channelVotePkGoCli: mockchannelvotepkgo,
// 				channelCli:         mockchannelCli,
// 			},
// 			args: args{
// 				info:              info2,
// 				level2AwardConfig: level2AwardConfig,
// 			},
// 			wantErr: true,
// 		},
// 		{ //3
// 			fields: fields{
// 				channelLotteryCli:  mockchannelLotteryCli,
// 				channelVotePkGoCli: mockchannelvotepkgo,
// 				channelLiveFansCli: mockchannelLiveFansCli,
// 				channelCli:         mockchannelCli,
// 			},
// 			args: args{
// 				info:              info3,
// 				level2AwardConfig: level2AwardConfig,
// 			},
// 			wantErr: true,
// 		},
// 		{ //4
// 			fields: fields{
// 				channelLotteryCli:    mockchannelLotteryCli,
// 				channelVotePkGoCli:   mockchannelvotepkgo,
// 				channelLiveFansCli:   mockchannelLiveFansCli,
// 				channelbackgroundCli: mockchannelbackgroundCli,
// 				channelCli:           mockchannelCli,
// 			},
// 			args: args{
// 				info:              info4,
// 				level2AwardConfig: level2AwardConfig,
// 			},
// 			wantErr: true,
// 		},

// 		{ // 5
// 			fields: fields{
// 				channelLotteryCli:    mockchannelLotteryCli,
// 				channelVotePkGoCli:   mockchannelvotepkgo,
// 				channelLiveFansCli:   mockchannelLiveFansCli,
// 				channelbackgroundCli: mockchannelbackgroundCli,
// 				channelRecommendSvr:  mockchannelRecommendSvr,
// 				channelCli:           mockchannelCli,
// 				//store:                mockStore,
// 			},
// 			args: args{
// 				info:              info4,
// 				level2AwardConfig: level2AwardConfig,
// 			},
// 			wantErr: true,
// 		},

// 		{ //6
// 			fields: fields{
// 				channelLotteryCli:    mockchannelLotteryCli,
// 				channelVotePkGoCli:   mockchannelvotepkgo,
// 				channelLiveFansCli:   mockchannelLiveFansCli,
// 				channelbackgroundCli: mockchannelbackgroundCli,
// 				channelRecommendSvr:  mockchannelRecommendSvr,
// 				channelCli:           mockchannelCli,
// 				//store:                mockStore,
// 			},
// 			args: args{
// 				info:              info5,
// 				level2AwardConfig: level2AwardConfig,
// 			},
// 			wantErr: true,
// 		},

// 		{ //7
// 			fields: fields{
// 				channelLotteryCli:    mockchannelLotteryCli,
// 				channelVotePkGoCli:   mockchannelvotepkgo,
// 				channelLiveFansCli:   mockchannelLiveFansCli,
// 				channelbackgroundCli: mockchannelbackgroundCli,
// 				channelRecommendSvr:  mockchannelRecommendSvr,
// 				channelCli:           mockchannelCli,
// 				//store:                mockStore,
// 			},
// 			args: args{
// 				info:              info6,
// 				level2AwardConfig: level2AwardConfig,
// 			},
// 			wantErr: true,
// 		},
// 	}
// 	for _, tt := range tests {
// 		t.Run(tt.name, func(t *testing.T) {
// 			m := &AnchorLevelMgr{
// 				store:                      tt.fields.store,
// 				dyconfig:                   tt.fields.dyconfig,
// 				LocalCache:                 tt.fields.LocalCache,
// 				channelLiveMgr:             tt.fields.channelLiveMgr,
// 				channelLiveStats:           tt.fields.channelLiveStats,
// 				channelRecommendSvr:        tt.fields.channelRecommendSvr,
// 				entertainmentRecommendBack: tt.fields.entertainmentRecommendBack,
// 				anchorcontractGoClient:     tt.fields.anchorcontractGoClient,
// 				reporter:                   tt.fields.reporter,
// 				accountCli:                 tt.fields.accountCli,
// 				channelbackgroundCli:       tt.fields.channelbackgroundCli,
// 				channelLotteryCli:          tt.fields.channelLotteryCli,
// 				channelLiveFansCli:         tt.fields.channelLiveFansCli,
// 				channelVotePkGoCli:         tt.fields.channelVotePkGoCli,
// 				publicCli:                  tt.fields.publicCli,
// 				apiCli:                     tt.fields.apiCli,
// 				userOlCli:                  tt.fields.userOlCli,
// 				channelCli:                 tt.fields.channelCli,
// 			}
// 			if err := m.awardUser(tt.args.info, tt.args.level2AwardConfig); (err != nil) != tt.wantErr {
// 				t.Errorf("AnchorLevelMgr.awardUser() error = %v, wantErr %v", err, tt.wantErr)
// 			}
// 		})
// 	}
// }
// */

// // go test -timeout 30s -run ^TestAnchorLevelMgr_filterViolation$ golang.52tt.com/services/anchor-level/manager -v -count=1
// func TestAnchorLevelMgr_filterViolation(t *testing.T) {

// 	httpmock.ActivateNonDefault(urrc.GetdefaultHTTPClient())
// 	url := "http://dun-internal.52tt.com/api/urrc-sanction/biz/sanction/record/batch/list"
// 	body := `{"code":200, "data":[] }`
// 	response := httpmock.NewStringResponder(http.StatusOK, body)
// 	httpmock.RegisterResponder("POST", url, response)

// 	ctl := gomock.NewController(t)
// 	defer ctl.Finish()

// 	mockaccountCli := accountmock.NewMockIClient(ctl)

// 	var (
// 		usermp = map[uint32]*accountPB.UserResp{
// 			2404178: {Alias: "999"},
// 		}
// 		err = protocol.NewServerError(-2)
// 	)
// 	uid2MonthTaskResult := map[uint32]*store.ExtraInfo{
// 		2404178: {},
// 	}
// 	gomock.InOrder(
// 		mockaccountCli.EXPECT().GetUsersMap(gomock.Any(), gomock.Any()).Return(usermp, err),

// 		mockaccountCli.EXPECT().GetUsersMap(gomock.Any(), gomock.Any()).Return(usermp, nil),

// 		//mockaccountCli.EXPECT().GetUsersMap(gomock.Any(), gomock.Any()).Return(usermp, nil),
// 	)

// 	type fields struct {
// 		store                      store.IStore
// 		dyconfig                   *conf.SDyConfigHandler
// 		LocalCache                 *LocalCache
// 		channelLiveMgr             channellivemgr.IClient
// 		channelLiveStats           channellivestats.IClient
// 		channelRecommendSvr        channel_recommend_svr.IClient
// 		entertainmentRecommendBack entertainmentrecommendback.IClient
// 		anchorcontractGoClient     anchorcontract_go.IClient
// 		reporter                   report.IFeishuReporterV2
// 		accountCli                 account.IClient
// 		channelbackgroundCli       channelbackground.IClient
// 		channelLotteryCli          channellottery.IClient
// 		channelLiveFansCli         channellivefans.IClient
// 		channelVotePkGoCli         channelvotepkgo.IClient
// 		publicCli                  public.IClient
// 		apiCli                     apicenter.IClient
// 		userOlCli                  userol.IClient
// 	}
// 	type args struct {
// 		ctx                 context.Context
// 		uids                []uint32
// 		settleMonthTm       time.Time
// 		taskLimit           *store.LiveAnchorMonthTask
// 		uid2MonthTaskResult map[uint32]*store.ExtraInfo
// 	}
// 	tests := []struct {
// 		name    string
// 		fields  fields
// 		args    args
// 		want    []uint32
// 		wantErr bool
// 	}{
// 		{
// 			fields: fields{accountCli: mockaccountCli},
// 			args: args{
// 				ctx:                 context.Background(),
// 				uids:                []uint32{2404178},
// 				settleMonthTm:       time.Now(),
// 				taskLimit:           &store.LiveAnchorMonthTask{MonthViolation_CCnt: 2},
// 				uid2MonthTaskResult: uid2MonthTaskResult,
// 			},
// 			want:    []uint32{},
// 			wantErr: true,
// 		},
// 		{
// 			fields: fields{accountCli: mockaccountCli},
// 			args: args{
// 				ctx:                 context.Background(),
// 				uids:                []uint32{2404178},
// 				settleMonthTm:       time.Now(),
// 				taskLimit:           &store.LiveAnchorMonthTask{MonthViolation_CCnt: 2},
// 				uid2MonthTaskResult: uid2MonthTaskResult,
// 			},
// 			want:    []uint32{2404178},
// 			wantErr: false,
// 		},
// 	}
// 	for _, tt := range tests {
// 		t.Run(tt.name, func(t *testing.T) {
// 			m := &AnchorLevelMgr{
// 				store:                      tt.fields.store,
// 				dyconfig:                   tt.fields.dyconfig,
// 				LocalCache:                 tt.fields.LocalCache,
// 				channelLiveMgr:             tt.fields.channelLiveMgr,
// 				channelLiveStats:           tt.fields.channelLiveStats,
// 				channelRecommendSvr:        tt.fields.channelRecommendSvr,
// 				entertainmentRecommendBack: tt.fields.entertainmentRecommendBack,
// 				anchorcontractGoClient:     tt.fields.anchorcontractGoClient,
// 				reporter:                   tt.fields.reporter,
// 				accountCli:                 tt.fields.accountCli,
// 				channelbackgroundCli:       tt.fields.channelbackgroundCli,
// 				channelLotteryCli:          tt.fields.channelLotteryCli,
// 				channelLiveFansCli:         tt.fields.channelLiveFansCli,
// 				channelVotePkGoCli:         tt.fields.channelVotePkGoCli,
// 				publicCli:                  tt.fields.publicCli,
// 				apiCli:                     tt.fields.apiCli,
// 				userOlCli:                  tt.fields.userOlCli,
// 			}
// 			got, err := m.filterViolation(tt.args.ctx, tt.args.uids, tt.args.settleMonthTm,
// 				tt.args.taskLimit, tt.args.uid2MonthTaskResult)
// 			if (err != nil) != tt.wantErr {
// 				t.Errorf("AnchorLevelMgr.filterViolation() error = %v, wantErr %v", err, tt.wantErr)
// 				return
// 			}
// 			if !reflect.DeepEqual(got, tt.want) {
// 				t.Errorf("AnchorLevelMgr.filterViolation() = %v, want %v", got, tt.want)
// 			}
// 		})
// 	}
// }

// /*
// // go test -timeout 30s -run ^TestAnchorLevelMgr_filterDatahouse$ golang.52tt.com/services/anchor-level/manager -v -count=1
// func TestAnchorLevelMgr_filterDatahouse(t *testing.T) {
// 	// http://dap-moa.ttyuyin.com:8101/tt-activity/tt/queryMemberMonthIndex?apiToken=27bd4a08e51a58cd9abd881f6b072d67&start_date=202212&end_date=202212&user_id=2404179&guild_id=0&pageSize=1000

// 	httpmock.ActivateNonDefault(datahouse.GetdefaultHTTPClient())
// 	url := "http://dap-moa.ttyuyin.com:8101/tt-activity/tt/queryMemberMonthIndex?apiToken=27bd4a08e51a58cd9abd881f6b072d67&start_date=202212&end_date=202212&user_id=2404178&guild_id=0&pageSize=1000"
// 	body := `{"code":200, "data":{"data":[{"user_id":2404178,"login_days":20}]} }`
// 	response := httpmock.NewStringResponder(http.StatusOK, body)
// 	httpmock.RegisterResponder("GET", url, response)

// 	url2 := "http://dap-moa.ttyuyin.com:8101/tt-activity/tt/queryMemberMonthIndex?apiToken=27bd4a08e51a58cd9abd881f6b072d67&start_date=202212&end_date=202212&user_id=2404179&guild_id=0&pageSize=1000"
// 	response = httpmock.NewStringResponder(http.StatusBadRequest, "")
// 	httpmock.RegisterResponder("GET", url2, response)

// 	month := time.Date(2022, 12, 1, 0, 0, 0, 0, time.Local)

// 	type fields struct {
// 		store                      store.IStore
// 		dyconfig                   *conf.SDyConfigHandler
// 		LocalCache                 *LocalCache
// 		channelLiveMgr             channellivemgr.IClient
// 		channelLiveStats           channellivestats.IClient
// 		channelRecommendSvr        channel_recommend_svr.IClient
// 		entertainmentRecommendBack entertainmentrecommendback.IClient
// 		anchorcontractGoClient     anchorcontract_go.IClient
// 		reporter                   report.IFeishuReporterV2
// 		accountCli                 account.IClient
// 		channelbackgroundCli       channelbackground.IClient
// 		channelLotteryCli          channellottery.IClient
// 		channelLiveFansCli         channellivefans.IClient
// 		channelVotePkGoCli         channelvotepkgo.IClient
// 		publicCli                  public.IClient
// 		apiCli                     apicenter.IClient
// 		userOlCli                  userol.IClient
// 	}
// 	type args struct {
// 		ctx           context.Context
// 		uids          []uint32
// 		settleMonthTm time.Time
// 		taskLimit     *store.LiveAnchorMonthTask
// 	}
// 	tests := []struct {
// 		name    string
// 		fields  fields
// 		args    args
// 		want    []uint32
// 		wantErr bool
// 	}{
// 		{
// 			fields:  fields{},
// 			args:    args{ctx: context.TODO(), uids: []uint32{2404178}, settleMonthTm: month, taskLimit: &store.LiveAnchorMonthTask{MonthPlatformDays: 20}},
// 			want:    []uint32{2404178},
// 			wantErr: false,
// 		},
// 		{
// 			fields:  fields{},
// 			args:    args{ctx: context.TODO(), uids: []uint32{2404179}, settleMonthTm: month, taskLimit: &store.LiveAnchorMonthTask{MonthPlatformDays: 20}},
// 			want:    []uint32{},
// 			wantErr: true,
// 		},
// 	}
// 	for _, tt := range tests {
// 		t.Run(tt.name, func(t *testing.T) {
// 			m := &AnchorLevelMgr{
// 				store:                      tt.fields.store,
// 				dyconfig:                   tt.fields.dyconfig,
// 				LocalCache:                 tt.fields.LocalCache,
// 				channelLiveMgr:             tt.fields.channelLiveMgr,
// 				channelLiveStats:           tt.fields.channelLiveStats,
// 				channelRecommendSvr:        tt.fields.channelRecommendSvr,
// 				entertainmentRecommendBack: tt.fields.entertainmentRecommendBack,
// 				anchorcontractGoClient:     tt.fields.anchorcontractGoClient,
// 				reporter:                   tt.fields.reporter,
// 				accountCli:                 tt.fields.accountCli,
// 				channelbackgroundCli:       tt.fields.channelbackgroundCli,
// 				channelLotteryCli:          tt.fields.channelLotteryCli,
// 				channelLiveFansCli:         tt.fields.channelLiveFansCli,
// 				channelVotePkGoCli:         tt.fields.channelVotePkGoCli,
// 				publicCli:                  tt.fields.publicCli,
// 				apiCli:                     tt.fields.apiCli,
// 				userOlCli:                  tt.fields.userOlCli,
// 			}
// 			got, err := m.filterDatahouse(tt.args.ctx, tt.args.uids, tt.args.settleMonthTm, tt.args.taskLimit)
// 			if (err != nil) != tt.wantErr {
// 				t.Errorf("AnchorLevelMgr.filterDatahouse() error = %v, wantErr %v", err, tt.wantErr)
// 				return
// 			}
// 			if !reflect.DeepEqual(got, tt.want) {
// 				t.Errorf("AnchorLevelMgr.filterDatahouse() = %v, want %v", got, tt.want)
// 			}
// 		})
// 	}
// }
// */

// // go test -timeout 30s -run ^TestAnchorLevelMgr_ReplenishAnchorLevel$ golang.52tt.com/services/anchor-level/manager -v -count=1
// func TestAnchorLevelMgr_ReplenishAnchorLevel(t *testing.T) {
// 	ctl := gomock.NewController(t)
// 	defer ctl.Finish()

// 	mockStore := mocks.NewMockIStore(ctl)
// 	mockchannelliveMgr := mockchannellivemgr.NewMockIClient(ctl)

// 	anchorUid := uint32(1)
// 	settleMonth := time.Date(2023, 3, 1, 0, 0, 0, 0, time.Local)
// 	in := &pb.ReplenishAnchorLevelReq{
// 		SettleMonth: uint32(settleMonth.Unix()),
// 		InfoList: []*pb.ReplenishAnchorLevelReq_ReplenishAnchorLevelInfo{
// 			{
// 				AnchorUid:        anchorUid,
// 				AnchorLevel:      uint32(pb.ANCHOR_LEVEL_TYPE_ANCHOR_LEVEL_TYPE_A),
// 				AdvancedTaskDone: 1,
// 			},
// 		},
// 	}
// 	liveInfo := &channellivemgrPB.GetAnchorByUidListResp{
// 		AnchorList: []*channellivemgrPB.AnchorInfo{
// 			{
// 				Uid:       anchorUid,
// 				ChannelId: 1,
// 			},
// 		},
// 	}
// 	taskList1 := []*store.LiveAnchorMonthTask{{AnchorLevel: 1}}
// 	anchorlevel2Name := map[uint32]*store.LiveAnchorMonthTask{
// 		uint32(pb.ANCHOR_LEVEL_TYPE_ANCHOR_LEVEL_TYPE_A): {
// 			AnchorLevelName: "xx",
// 		},
// 	}
// 	gomock.InOrder(
// 		mockchannelliveMgr.EXPECT().GetAnchorByUidList(gomock.Any(), gomock.Any()).Return(liveInfo, nil),
// 		mockStore.EXPECT().GetLiveAnchorMonthTask().Return(taskList1, anchorlevel2Name, nil),
// 		mockStore.EXPECT().RecordLiveAnchorMonthTaskAward(gomock.Any(), gomock.Any()).Return(errors.New("1")),
// 	)

// 	type fields struct {
// 		store                      store.IStore
// 		dyconfig                   conf.ISDyConfigHandler
// 		LocalCache                 *LocalCache
// 		channelLiveMgr             channellivemgr.IClient
// 		channelLiveStats           channellivestats.IClient
// 		channelRecommendSvr        channel_recommend_svr.IClient
// 		entertainmentRecommendBack entertainmentrecommendback.IClient
// 		anchorcontractGoClient     anchorcontract_go.IClient
// 		reporter                   report.IFeishuReporterV2
// 		accountCli                 account.IClient
// 		channelbackgroundCli       channelbackground.IClient
// 		channelLotteryCli          channellottery.IClient
// 		channelLiveFansCli         channellivefans.IClient
// 		channelVotePkGoCli         channelvotepkgo.IClient
// 		publicCli                  public.IClient
// 		apiCli                     apicenter.IClient
// 		userOlCli                  userol.IClient
// 		channelCli                 channel.IClient
// 		anchorCheckCli             anchorcheck.IClient
// 	}
// 	type args struct {
// 		ctx context.Context
// 		in  *pb.ReplenishAnchorLevelReq
// 	}
// 	tests := []struct {
// 		name    string
// 		fields  fields
// 		args    args
// 		want    *pb.ReplenishAnchorLevelResp
// 		wantErr bool
// 	}{
// 		{
// 			fields: fields{
// 				store:          mockStore,
// 				channelLiveMgr: mockchannelliveMgr,
// 			},
// 			args: args{
// 				ctx: context.Background(),
// 				in:  in,
// 			},
// 			want:    &pb.ReplenishAnchorLevelResp{},
// 			wantErr: true,
// 		},
// 	}
// 	for _, tt := range tests {
// 		t.Run(tt.name, func(t *testing.T) {
// 			m := &AnchorLevelMgr{
// 				store:                      tt.fields.store,
// 				dyconfig:                   tt.fields.dyconfig,
// 				LocalCache:                 tt.fields.LocalCache,
// 				channelLiveMgr:             tt.fields.channelLiveMgr,
// 				channelLiveStats:           tt.fields.channelLiveStats,
// 				channelRecommendSvr:        tt.fields.channelRecommendSvr,
// 				entertainmentRecommendBack: tt.fields.entertainmentRecommendBack,
// 				anchorcontractGoClient:     tt.fields.anchorcontractGoClient,
// 				reporter:                   tt.fields.reporter,
// 				accountCli:                 tt.fields.accountCli,
// 				channelbackgroundCli:       tt.fields.channelbackgroundCli,
// 				channelLotteryCli:          tt.fields.channelLotteryCli,
// 				channelLiveFansCli:         tt.fields.channelLiveFansCli,
// 				channelVotePkGoCli:         tt.fields.channelVotePkGoCli,
// 				publicCli:                  tt.fields.publicCli,
// 				apiCli:                     tt.fields.apiCli,
// 				userOlCli:                  tt.fields.userOlCli,
// 				channelCli:                 tt.fields.channelCli,
// 				anchorCheckCli:             tt.fields.anchorCheckCli,
// 			}
// 			got, err := m.ReplenishAnchorLevel(tt.args.ctx, tt.args.in)
// 			if (err != nil) != tt.wantErr {
// 				t.Errorf("AnchorLevelMgr.ReplenishAnchorLevel() error = %v, wantErr %v", err, tt.wantErr)
// 				return
// 			}
// 			if !reflect.DeepEqual(got, tt.want) {
// 				t.Errorf("AnchorLevelMgr.ReplenishAnchorLevel() = %v, want %v", got, tt.want)
// 			}
// 		})
// 	}
// }

// // go test -timeout 30s -run ^TestAnchorLevelMgr_GetAnchorMonthStats$ golang.52tt.com/services/anchor-level/manager -v -count=1
// func TestAnchorLevelMgr_GetAnchorMonthStats(t *testing.T) {
// 	ctl := gomock.NewController(t)
// 	defer ctl.Finish()

// 	mockChannelLiveStatsCli := mockChannelLiveStats.NewMockIClient(ctl)

// 	var (
// 		monthStatsResp = &channel_live_stats.BatchGetAnchorMonthlyStatsByUidResp{
// 			List: []*channel_live_stats.BatchGetAnchorMonthlyStatsByUidRespMonthStats{
// 				{
// 					AnchorUid: 1,
// 				},
// 			},
// 		}
// 		uid2monthStats = map[uint32]*channel_live_stats.BatchGetAnchorMonthlyStatsByUidRespMonthStats{
// 			1: {
// 				AnchorUid: 1,
// 			},
// 		}
// 	)

// 	gomock.InOrder(
// 		mockChannelLiveStatsCli.EXPECT().BatchGetAnchorMonthlyStatsByUid(gomock.Any(), gomock.Any(), gomock.Any()).Return(monthStatsResp, nil),
// 	)

// 	type fields struct {
// 		store                      store.IStore
// 		dyconfig                   *conf.SDyConfigHandler
// 		LocalCache                 *LocalCache
// 		channelLiveMgr             channellivemgr.IClient
// 		channelLiveStats           channellivestats.IClient
// 		channelRecommendSvr        channel_recommend_svr.IClient
// 		entertainmentRecommendBack entertainmentrecommendback.IClient
// 		anchorcontractGoClient     anchorcontract_go.IClient
// 		reporter                   report.IFeishuReporterV2
// 		accountCli                 account.IClient
// 		channelbackgroundCli       channelbackground.IClient
// 		channelLotteryCli          channellottery.IClient
// 		channelLiveFansCli         channellivefans.IClient
// 		channelVotePkGoCli         channelvotepkgo.IClient
// 		publicCli                  public.IClient
// 		apiCli                     apicenter.IClient
// 		userOlCli                  userol.IClient
// 	}
// 	type args struct {
// 		ctx        context.Context
// 		monthTm    time.Time
// 		anchorUids []uint32
// 	}
// 	tests := []struct {
// 		name   string
// 		fields fields
// 		args   args
// 		want   map[uint32]*channel_live_stats.BatchGetAnchorMonthlyStatsByUidRespMonthStats
// 		want1  protocol.ServerError
// 	}{
// 		{
// 			fields: fields{
// 				channelLiveStats: mockChannelLiveStatsCli,
// 			},
// 			args: args{
// 				ctx:        context.Background(),
// 				monthTm:    time.Now(),
// 				anchorUids: []uint32{1},
// 			},
// 			want:  uid2monthStats,
// 			want1: nil,
// 		},
// 	}
// 	for _, tt := range tests {
// 		t.Run(tt.name, func(t *testing.T) {
// 			m := &AnchorLevelMgr{
// 				store:                      tt.fields.store,
// 				dyconfig:                   tt.fields.dyconfig,
// 				LocalCache:                 tt.fields.LocalCache,
// 				channelLiveMgr:             tt.fields.channelLiveMgr,
// 				channelLiveStats:           tt.fields.channelLiveStats,
// 				channelRecommendSvr:        tt.fields.channelRecommendSvr,
// 				entertainmentRecommendBack: tt.fields.entertainmentRecommendBack,
// 				anchorcontractGoClient:     tt.fields.anchorcontractGoClient,
// 				reporter:                   tt.fields.reporter,
// 				accountCli:                 tt.fields.accountCli,
// 				channelbackgroundCli:       tt.fields.channelbackgroundCli,
// 				channelLotteryCli:          tt.fields.channelLotteryCli,
// 				channelLiveFansCli:         tt.fields.channelLiveFansCli,
// 				channelVotePkGoCli:         tt.fields.channelVotePkGoCli,
// 				publicCli:                  tt.fields.publicCli,
// 				apiCli:                     tt.fields.apiCli,
// 				userOlCli:                  tt.fields.userOlCli,
// 			}
// 			got, got1 := m.GetAnchorMonthStats(tt.args.ctx, tt.args.monthTm, tt.args.anchorUids)
// 			if !reflect.DeepEqual(got, tt.want) {
// 				t.Errorf("AnchorLevelMgr.GetAnchorMonthStats() got = %v, want %v", got, tt.want)
// 			}
// 			if !reflect.DeepEqual(got1, tt.want1) {
// 				t.Errorf("AnchorLevelMgr.GetAnchorMonthStats() got1 = %v, want %v", got1, tt.want1)
// 			}
// 		})
// 	}
// }

// // go test -timeout 30s -run ^TestAnchorLevelMgr_loadFromDB$ golang.52tt.com/services/anchor-level/manager -v -count=1
// func TestAnchorLevelMgr_loadFromDB(t *testing.T) {

// 	return

// 	ctl := gomock.NewController(t)
// 	defer ctl.Finish()

// 	mockStore := mocks.NewMockIStore(ctl)
// 	mockconf := mocks.NewMockISDyConfigHandler(ctl)

// 	var (
// 		list = []*store.LiveAnchorMonthTaskAwardLog{
// 			{
// 				AnchorUid: 1,
// 				IsDone:    true,
// 			},
// 		}
// 	)
// 	level2Config := map[uint32]string{}

// 	gomock.InOrder(

// 		mockStore.EXPECT().GetLiveAnchorMonthTaskAwardLog(gomock.Any(), gomock.Any(), gomock.Any()).Return(list, nil),
// 		// GetAnchorCertConfig
// 		mockStore.EXPECT().GetAnchorCertConfig(gomock.Any()).Return(level2Config, nil),
// 		//mockconf.EXPECT().GetAnchorLevel2CertBaseImgurl(gomock.Any()).Return("1"),
// 		mockconf.EXPECT().GetShadowColor().Return("1"),
// 	)

// 	type fields struct {
// 		store                      store.IStore
// 		dyconfig                   conf.ISDyConfigHandler
// 		LocalCache                 *LocalCache
// 		channelLiveMgr             channellivemgr.IClient
// 		channelLiveStats           channellivestats.IClient
// 		channelRecommendSvr        channel_recommend_svr.IClient
// 		entertainmentRecommendBack entertainmentrecommendback.IClient
// 		anchorcontractGoClient     anchorcontract_go.IClient
// 		reporter                   report.IFeishuReporterV2
// 		accountCli                 account.IClient
// 		channelbackgroundCli       channelbackground.IClient
// 		channelLotteryCli          channellottery.IClient
// 		channelLiveFansCli         channellivefans.IClient
// 		channelVotePkGoCli         channelvotepkgo.IClient
// 		publicCli                  public.IClient
// 		apiCli                     apicenter.IClient
// 		userOlCli                  userol.IClient
// 	}
// 	tests := []struct {
// 		name   string
// 		fields fields
// 	}{
// 		{
// 			fields: fields{
// 				LocalCache: &LocalCache{
// 					uid2LevelCerts: [2]map[uint32]*pb.LiveAnchorLevelInfo{{}, {}},
// 				},
// 				store:    mockStore,
// 				dyconfig: mockconf,
// 			},
// 		},
// 	}
// 	for _, tt := range tests {
// 		t.Run(tt.name, func(t *testing.T) {
// 			m := &AnchorLevelMgr{
// 				store:                      tt.fields.store,
// 				dyconfig:                   tt.fields.dyconfig,
// 				LocalCache:                 tt.fields.LocalCache,
// 				channelLiveMgr:             tt.fields.channelLiveMgr,
// 				channelLiveStats:           tt.fields.channelLiveStats,
// 				channelRecommendSvr:        tt.fields.channelRecommendSvr,
// 				entertainmentRecommendBack: tt.fields.entertainmentRecommendBack,
// 				anchorcontractGoClient:     tt.fields.anchorcontractGoClient,
// 				reporter:                   tt.fields.reporter,
// 				accountCli:                 tt.fields.accountCli,
// 				channelbackgroundCli:       tt.fields.channelbackgroundCli,
// 				channelLotteryCli:          tt.fields.channelLotteryCli,
// 				channelLiveFansCli:         tt.fields.channelLiveFansCli,
// 				channelVotePkGoCli:         tt.fields.channelVotePkGoCli,
// 				publicCli:                  tt.fields.publicCli,
// 				apiCli:                     tt.fields.apiCli,
// 				userOlCli:                  tt.fields.userOlCli,
// 			}
// 			m.loadFromDB()
// 		})
// 	}
// }

// // go test -timeout 30s -run ^TestAnchorLevelMgr_LiveAnchorLevelMonthSettleByUid5$ golang.52tt.com/services/anchor-level/manager -v -count=1
// func TestAnchorLevelMgr_LiveAnchorLevelMonthSettleByUid5(t *testing.T) {
// 	ctl := gomock.NewController(t)
// 	defer ctl.Finish()

// 	mockChannelLiveStatsCli := mockChannelLiveStats.NewMockIClient(ctl)
// 	mockpublicCli := publicmock.NewMockIClient(ctl)
// 	mockuserOlCli := userOlmock.NewMockIClient(ctl)
// 	mockconf := mocks.NewMockISDyConfigHandler(ctl)
// 	mockapiCli := mockapi.NewMockIClient(ctl)
// 	mockStore := mocks.NewMockIStore(ctl)
// 	mockaccountCli := accountmock.NewMockIClient(ctl)

// 	httpmock.ActivateNonDefault(urrc.GetdefaultHTTPClient())
// 	url := "http://dun-internal.52tt.com/api/urrc-sanction/biz/sanction/record/batch/list"
// 	//body := `{"code":200,"success":true,"data":[{"objId":"999","currentState":-1,"unblockType":"","unblockTime":-1,"records":[{"levelKey":"C","total":1,"details":[{"tenantId":"TT","id":"794635657747439616","appId":"1","appName":"TT语音","objTypeId":"1","objTypeName":"用户","objId":"999888","objName":"李j","objImg":"https://api.52tt.com/face?uid=2404178","objStatus":1,"objStatusDesc":"正常","objRemark":"[{\"attrDesc\":\"设备\",\"attrValue\":\"Xlg2xKLwQ+aPQK/t87BK0Q== : 正常\"},{\"attrDesc\":\"内部ID\",\"attrValue\":\"2404178\"},{\"attrDesc\":\"手机号码\",\"attrValue\":\"****\"},{\"attrDesc\":\"主体加密串\",\"attrValue\":\"dXJyY185OTk4ODg=\"}]","bizId":"1551860852149075969","bizName":"用户即时音频实时处罚业务","violationLevelKey":"","violationCount":0,"violationReasonId":-1,"violationReason":"低级趣味","pubViolationReason":"","violationEvidence":"","sanctionActionDesc":"暂无处罚","optUserId":"1260405679697551362","optUserName":"bpo3","optTime":1669622369893,"sourceType":"人工处罚","sourceRecordId":"","remark":"","createTime":1669622369893,"synStatus":2,"synStatusDesc":"同步成功","labelId":38,"labelName":"低级趣味","labelLevelKey":"C","levelType":2}]}]}],"msg":"操作成功"}`
// 	body := `{"code":200,"success":true,"data":[{"objId":"999","currentState":-1,"unblockType":"","unblockTime":-1,"records":[{"levelKey":"C","total":2,"details":[{"tenantId":"TT","id":"794635657747439616","appId":"1","appName":"TT语音","objTypeId":"1","objTypeName":"用户","objId":"999888","objName":"李j","objImg":"https://api.52tt.com/face?uid=2404178","objStatus":1,"objStatusDesc":"正常","objRemark":"[{\"attrDesc\":\"设备\",\"attrValue\":\"Xlg2xKLwQ+aPQK/t87BK0Q== : 正常\"},{\"attrDesc\":\"内部ID\",\"attrValue\":\"2404178\"},{\"attrDesc\":\"手机号码\",\"attrValue\":\"****\"},{\"attrDesc\":\"主体加密串\",\"attrValue\":\"dXJyY185OTk4ODg=\"}]","bizId":"1551860852149075969","bizName":"用户即时音频实时处罚业务","violationLevelKey":"","violationCount":0,"violationReasonId":-1,"violationReason":"低级趣味","pubViolationReason":"","violationEvidence":"","sanctionActionDesc":"暂无处罚","optUserId":"1260405679697551362","optUserName":"bpo3","optTime":1669622369893,"sourceType":"人工处罚","sourceRecordId":"","remark":"","createTime":1669622369893,"synStatus":2,"synStatusDesc":"同步成功","labelId":38,"labelName":"低级趣味","labelLevelKey":"C","levelType":2},{"tenantId":"TT","id":"794635657747439616","appId":"1","appName":"TT语音","objTypeId":"1","objTypeName":"用户","objId":"999888","objName":"李j","objImg":"https://api.52tt.com/face?uid=2404178","objStatus":1,"objStatusDesc":"正常","objRemark":"[{\"attrDesc\":\"设备\",\"attrValue\":\"Xlg2xKLwQ+aPQK/t87BK0Q== : 正常\"},{\"attrDesc\":\"内部ID\",\"attrValue\":\"2404178\"},{\"attrDesc\":\"手机号码\",\"attrValue\":\"****\"},{\"attrDesc\":\"主体加密串\",\"attrValue\":\"dXJyY185OTk4ODg=\"}]","bizId":"1551860852149075969","bizName":"用户即时音频实时处罚业务","violationLevelKey":"","violationCount":0,"violationReasonId":-1,"violationReason":"低级趣味","pubViolationReason":"","violationEvidence":"","sanctionActionDesc":"暂无处罚","optUserId":"1260405679697551362","optUserName":"bpo3","optTime":1669622369893,"sourceType":"人工处罚","sourceRecordId":"","remark":"","createTime":1669622369893,"synStatus":2,"synStatusDesc":"同步成功","labelId":38,"labelName":"低级趣味","labelLevelKey":"C","levelType":2}]}]}],"msg":"操作成功"}`

// 	response := httpmock.NewStringResponder(http.StatusOK, body)
// 	httpmock.RegisterResponder("POST", url, response)

// 	httpmock.ActivateNonDefault(datahouse.GetdefaultHTTPClient())
// 	url2 := "http://dap-moa.ttyuyin.com:8101/tt-activity/tt/queryMemberMonthIndex?apiToken=27bd4a08e51a58cd9abd881f6b072d67&start_date=202212&end_date=202212&user_id=4&guild_id=0&pageSize=1000"
// 	body2 := `{"code":200, "data":{"data":[{"user_id":4,"login_days":20}]} }`
// 	response2 := httpmock.NewStringResponder(http.StatusOK, body2)
// 	httpmock.RegisterResponder("GET", url2, response2)

// 	var (
// 		monthStatsResp = &channel_live_stats.BatchGetAnchorMonthlyStatsByUidResp{
// 			List: []*channel_live_stats.BatchGetAnchorMonthlyStatsByUidRespMonthStats{
// 				{
// 					AnchorUid:     4,
// 					AnchorIncome:  50 * 10000,
// 					NewFansCnt:    20,
// 					LiveActiveCnt: 20,
// 					ConsumerCnt:   1,
// 				},
// 			},
// 		}
// 		lastmonthStatsResp = &channel_live_stats.BatchGetAnchorMonthlyStatsByUidResp{
// 			List: []*channel_live_stats.BatchGetAnchorMonthlyStatsByUidRespMonthStats{
// 				{
// 					AnchorUid:     4,
// 					AnchorIncome:  0,
// 					NewFansCnt:    20,
// 					LiveActiveCnt: 20,
// 				},
// 			},
// 		}

// 		taskList = []*store.LiveAnchorMonthTask{
// 			{
// 				AnchorLevel:         1,
// 				AnchorLevelName:     "见习主播",
// 				MonthActiveDays:     20,
// 				MonthPlatformDays:   20,
// 				MonthNewFansCnt:     20,
// 				MonthConsumeCnt:     1,
// 				MonthIncome:         50 * 10000,
// 				MonthViolation_CCnt: 2,
// 			},
// 			{
// 				AnchorLevel:         2,
// 				AnchorLevelName:     "新锐主播",
// 				MonthActiveDays:     20,
// 				MonthPlatformDays:   20,
// 				MonthNewFansCnt:     20,
// 				MonthConsumeCnt:     2,
// 				MonthIncome:         80 * 10000,
// 				MonthViolation_CCnt: 2,
// 			},
// 			{
// 				AnchorLevel:         3,
// 				AnchorLevelName:     "精英主播",
// 				MonthActiveDays:     20,
// 				MonthPlatformDays:   20,
// 				MonthNewFansCnt:     20,
// 				MonthConsumeCnt:     3,
// 				MonthIncome:         100 * 10000,
// 				MonthViolation_CCnt: 2,
// 			},
// 			{
// 				AnchorLevel:         4,
// 				AnchorLevelName:     "大师主播",
// 				MonthActiveDays:     20,
// 				MonthPlatformDays:   20,
// 				MonthNewFansCnt:     20,
// 				MonthConsumeCnt:     4,
// 				MonthIncome:         500 * 10000,
// 				MonthViolation_CCnt: 2,
// 			},
// 			{
// 				AnchorLevel:         5,
// 				AnchorLevelName:     "传奇主播",
// 				MonthActiveDays:     20,
// 				MonthPlatformDays:   20,
// 				MonthNewFansCnt:     20,
// 				MonthConsumeCnt:     5,
// 				MonthIncome:         1000 * 10000,
// 				MonthViolation_CCnt: 2,
// 			},
// 		}
// 		taskLimit = taskList[0]

// 		anchorlevel2Name = map[uint32]*store.LiveAnchorMonthTask{
// 			1: {AnchorLevelName: "见习主播"},
// 			2: {AnchorLevelName: "新锐主播"},
// 			3: {AnchorLevelName: "精英主播"},
// 			4: {AnchorLevelName: "大师主播"},
// 			5: {AnchorLevelName: "传奇主播"},
// 		}
// 		anchorUid2ChannelId2 = map[uint32]uint32{
// 			4: 1,
// 		}

// 		settleMonthTm = time.Date(2022, 12, 1, 0, 0, 0, 0, time.Local)

// 		usermp = map[uint32]*accountPB.UserResp{
// 			4: {Alias: "999"},
// 		}
// 	)

// 	gomock.InOrder(

// 		// case6全部通过，判断具体等级，进阶任务
// 		mockChannelLiveStatsCli.EXPECT().BatchGetAnchorMonthlyStatsByUid(gomock.Any(), gomock.Any(), gomock.Any()).Return(monthStatsResp, nil),
// 		mockaccountCli.EXPECT().GetUsersMap(gomock.Any(), gomock.Any()).Return(usermp, nil),
// 		mockChannelLiveStatsCli.EXPECT().BatchGetAnchorMonthlyStatsByUid(gomock.Any(), gomock.Any(), gomock.Any()).Return(lastmonthStatsResp, nil),
// 		mockStore.EXPECT().RecordLiveAnchorMonthTaskAward(gomock.Any(), gomock.Any()).Return(nil),
// 	)

// 	type fields struct {
// 		store                      store.IStore
// 		dyconfig                   conf.ISDyConfigHandler
// 		LocalCache                 *LocalCache
// 		channelLiveMgr             channellivemgr.IClient
// 		channelLiveStats           channellivestats.IClient
// 		channelRecommendSvr        channel_recommend_svr.IClient
// 		entertainmentRecommendBack entertainmentrecommendback.IClient
// 		anchorcontractGoClient     anchorcontract_go.IClient
// 		reporter                   report.IFeishuReporterV2
// 		accountCli                 account.IClient
// 		channelbackgroundCli       channelbackground.IClient
// 		channelLotteryCli          channellottery.IClient
// 		channelLiveFansCli         channellivefans.IClient
// 		channelVotePkGoCli         channelvotepkgo.IClient
// 		publicCli                  public.IClient
// 		apiCli                     apicenter.IClient
// 		userOlCli                  userol.IClient
// 		channelCli                 channel.IClient
// 		anchorCheckCli             anchorcheck.IClient
// 	}
// 	type args struct {
// 		ctx                 context.Context
// 		settleMonthTm       time.Time
// 		anchorUids          []uint32
// 		anchorUid2ChannelId map[uint32]uint32
// 		taskLimit           *store.LiveAnchorMonthTask
// 		taskList            []*store.LiveAnchorMonthTask
// 		anchorlevel2Name    map[uint32]*store.LiveAnchorMonthTask
// 	}
// 	tests := []struct {
// 		name    string
// 		fields  fields
// 		args    args
// 		wantErr bool
// 	}{
// 		{
// 			fields: fields{
// 				channelLiveStats: mockChannelLiveStatsCli,
// 				publicCli:        mockpublicCli,
// 				userOlCli:        mockuserOlCli,
// 				dyconfig:         mockconf,
// 				apiCli:           mockapiCli,
// 				store:            mockStore,
// 				accountCli:       mockaccountCli,
// 			},
// 			args: args{
// 				ctx:                 context.Background(),
// 				settleMonthTm:       settleMonthTm,
// 				anchorUids:          []uint32{4},
// 				taskLimit:           taskLimit,
// 				taskList:            taskList,
// 				anchorlevel2Name:    anchorlevel2Name,
// 				anchorUid2ChannelId: anchorUid2ChannelId2,
// 			},
// 			wantErr: false,
// 		},
// 	}
// 	for _, tt := range tests {
// 		t.Run(tt.name, func(t *testing.T) {
// 			m := &AnchorLevelMgr{
// 				store:                      tt.fields.store,
// 				dyconfig:                   tt.fields.dyconfig,
// 				LocalCache:                 tt.fields.LocalCache,
// 				channelLiveMgr:             tt.fields.channelLiveMgr,
// 				channelLiveStats:           tt.fields.channelLiveStats,
// 				channelRecommendSvr:        tt.fields.channelRecommendSvr,
// 				entertainmentRecommendBack: tt.fields.entertainmentRecommendBack,
// 				anchorcontractGoClient:     tt.fields.anchorcontractGoClient,
// 				reporter:                   tt.fields.reporter,
// 				accountCli:                 tt.fields.accountCli,
// 				channelbackgroundCli:       tt.fields.channelbackgroundCli,
// 				channelLotteryCli:          tt.fields.channelLotteryCli,
// 				channelLiveFansCli:         tt.fields.channelLiveFansCli,
// 				channelVotePkGoCli:         tt.fields.channelVotePkGoCli,
// 				publicCli:                  tt.fields.publicCli,
// 				apiCli:                     tt.fields.apiCli,
// 				userOlCli:                  tt.fields.userOlCli,
// 				channelCli:                 tt.fields.channelCli,
// 				anchorCheckCli:             tt.fields.anchorCheckCli,
// 			}
// 			if err := m.LiveAnchorLevelMonthSettleByUid(tt.args.ctx, tt.args.settleMonthTm, tt.args.anchorUids, tt.args.anchorUid2ChannelId, tt.args.taskLimit, tt.args.taskList, tt.args.anchorlevel2Name); (err != nil) != tt.wantErr {
// 				t.Errorf("AnchorLevelMgr.LiveAnchorLevelMonthSettleByUid() error = %v, wantErr %v", err, tt.wantErr)
// 			}
// 		})
// 	}
// }

// /*
// // go test -timeout 30s -run ^TestAnchorLevelMgr_LiveAnchorLevelMonthSettleByUid3$ golang.52tt.com/services/anchor-level/manager -v -count=1
// func TestAnchorLevelMgr_LiveAnchorLevelMonthSettleByUid3(t *testing.T) {
// 	ctl := gomock.NewController(t)
// 	defer ctl.Finish()

// 	mockChannelLiveStatsCli := mockChannelLiveStats.NewMockIClient(ctl)
// 	mockpublicCli := publicmock.NewMockIClient(ctl)
// 	mockuserOlCli := userOlmock.NewMockIClient(ctl)
// 	mockconf := mocks.NewMockISDyConfigHandler(ctl)
// 	mockapiCli := mockapi.NewMockIClient(ctl)
// 	mockStore := mocks.NewMockIStore(ctl)
// 	mockaccountCli := accountmock.NewMockIClient(ctl)

// 	httpmock.ActivateNonDefault(urrc.GetdefaultHTTPClient())
// 	url := "http://dun-internal.52tt.com/api/urrc-sanction/biz/sanction/record/batch/list"
// 	body := `{"code":200, "data":[] }`
// 	response := httpmock.NewStringResponder(http.StatusOK, body)
// 	httpmock.RegisterResponder("POST", url, response)

// 	var (
// 		monthStatsResp = &channel_live_stats.BatchGetAnchorMonthlyStatsByUidResp{
// 			List: []*channel_live_stats.BatchGetAnchorMonthlyStatsByUidRespMonthStats{
// 				{
// 					AnchorUid:     2,
// 					AnchorIncome:  50 * 10000,
// 					NewFansCnt:    20,
// 					LiveActiveCnt: 20,
// 					ConsumerCnt:   5 - 1,
// 				},
// 				{
// 					AnchorUid:     4,
// 					AnchorIncome:  50 * 10000,
// 					NewFansCnt:    20,
// 					LiveActiveCnt: 20,
// 					ConsumerCnt:   5,
// 				},
// 			},
// 		}

// 		taskLimit = &store.LiveAnchorMonthTask{
// 			MonthIncome:       50 * 10000,
// 			MonthActiveDays:   20,
// 			MonthPlatformDays: 20,
// 			MonthNewFansCnt:   20,
// 			MonthConsumeCnt:   5,
// 		}
// 		taskList             = []*store.LiveAnchorMonthTask{}
// 		anchorlevel2Name     = map[uint32]*store.LiveAnchorMonthTask{}
// 		anchorUid2ChannelId2 = map[uint32]uint32{
// 			4: 1,
// 		}

// 		publicRsp2 = &publicPB.GetPublicAccountsByBindedIdListResp{
// 			PublicAccountList: []*publicPB.StPublicAccount{
// 				{
// 					Name:     "语音主播服务号",
// 					PublicId: 1,
// 				},
// 			},
// 		}
// 		ol4 = &userolpb.OnlineInfo{
// 			Uid: 4,
// 		}

// 		settleMonthTm = time.Date(2022, 12, 1, 0, 0, 0, 0, time.Local)

// 		usermp = map[uint32]*accountPB.UserResp{
// 			4: {Alias: "999"},
// 		}
// 	)

// 	gomock.InOrder(

// 		// case5 月平台活跃天不通过
// 		mockChannelLiveStatsCli.EXPECT().BatchGetAnchorMonthlyStatsByUid(gomock.Any(), gomock.Any(), gomock.Any()).Return(monthStatsResp, nil),
// 		mockaccountCli.EXPECT().GetUsersMap(gomock.Any(), gomock.Any()).Return(usermp, nil),
// 		mockconf.EXPECT().GetAnchorLevelUnfinishMsg().Return(""),
// 		mockpublicCli.EXPECT().GetPublicAccountsByBindedIdList(gomock.Any(), gomock.Any(), gomock.Any()).Return(publicRsp2, nil),
// 		mockuserOlCli.EXPECT().GetLastMobileOnlineInfo(gomock.Any(), gomock.Any()).Return(ol4, nil),
// 		mockconf.EXPECT().GetAppName(gomock.Any()).Return("1"),
// 		mockconf.EXPECT().GetLiveAnchorTaskUrl(gomock.Any(), gomock.Any()).Return("1"),
// 		mockapiCli.EXPECT().SendImMsg(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil),
// 	)

// 	type fields struct {
// 		store                      store.IStore
// 		dyconfig                   conf.ISDyConfigHandler
// 		LocalCache                 *LocalCache
// 		channelLiveMgr             channellivemgr.IClient
// 		channelLiveStats           channellivestats.IClient
// 		channelRecommendSvr        channel_recommend_svr.IClient
// 		entertainmentRecommendBack entertainmentrecommendback.IClient
// 		anchorcontractGoClient     anchorcontract_go.IClient
// 		reporter                   report.IFeishuReporterV2
// 		accountCli                 account.IClient
// 		channelbackgroundCli       channelbackground.IClient
// 		channelLotteryCli          channellottery.IClient
// 		channelLiveFansCli         channellivefans.IClient
// 		channelVotePkGoCli         channelvotepkgo.IClient
// 		publicCli                  public.IClient
// 		apiCli                     apicenter.IClient
// 		userOlCli                  userol.IClient
// 		channelCli                 channel.IClient
// 		anchorCheckCli             anchorcheck.IClient
// 	}
// 	type args struct {
// 		ctx                 context.Context
// 		settleMonthTm       time.Time
// 		anchorUids          []uint32
// 		anchorUid2ChannelId map[uint32]uint32
// 		taskLimit           *store.LiveAnchorMonthTask
// 		taskList            []*store.LiveAnchorMonthTask
// 		anchorlevel2Name    map[uint32]*store.LiveAnchorMonthTask
// 	}
// 	tests := []struct {
// 		name    string
// 		fields  fields
// 		args    args
// 		wantErr bool
// 	}{
// 		{
// 			fields: fields{
// 				channelLiveStats: mockChannelLiveStatsCli,
// 				publicCli:        mockpublicCli,
// 				userOlCli:        mockuserOlCli,
// 				dyconfig:         mockconf,
// 				apiCli:           mockapiCli,
// 				store:            mockStore,
// 				accountCli:       mockaccountCli,
// 			},
// 			args: args{
// 				ctx:                 context.Background(),
// 				settleMonthTm:       settleMonthTm,
// 				anchorUids:          []uint32{4},
// 				taskLimit:           taskLimit,
// 				taskList:            taskList,
// 				anchorlevel2Name:    anchorlevel2Name,
// 				anchorUid2ChannelId: anchorUid2ChannelId2,
// 			},
// 			wantErr: false,
// 		},
// 	}
// 	for _, tt := range tests {
// 		t.Run(tt.name, func(t *testing.T) {
// 			m := &AnchorLevelMgr{
// 				store:                      tt.fields.store,
// 				dyconfig:                   tt.fields.dyconfig,
// 				LocalCache:                 tt.fields.LocalCache,
// 				channelLiveMgr:             tt.fields.channelLiveMgr,
// 				channelLiveStats:           tt.fields.channelLiveStats,
// 				channelRecommendSvr:        tt.fields.channelRecommendSvr,
// 				entertainmentRecommendBack: tt.fields.entertainmentRecommendBack,
// 				anchorcontractGoClient:     tt.fields.anchorcontractGoClient,
// 				reporter:                   tt.fields.reporter,
// 				accountCli:                 tt.fields.accountCli,
// 				channelbackgroundCli:       tt.fields.channelbackgroundCli,
// 				channelLotteryCli:          tt.fields.channelLotteryCli,
// 				channelLiveFansCli:         tt.fields.channelLiveFansCli,
// 				channelVotePkGoCli:         tt.fields.channelVotePkGoCli,
// 				publicCli:                  tt.fields.publicCli,
// 				apiCli:                     tt.fields.apiCli,
// 				userOlCli:                  tt.fields.userOlCli,
// 				channelCli:                 tt.fields.channelCli,
// 				anchorCheckCli:             tt.fields.anchorCheckCli,
// 			}
// 			if err := m.LiveAnchorLevelMonthSettleByUid(tt.args.ctx, tt.args.settleMonthTm, tt.args.anchorUids, tt.args.anchorUid2ChannelId, tt.args.taskLimit, tt.args.taskList, tt.args.anchorlevel2Name); (err != nil) != tt.wantErr {
// 				t.Errorf("AnchorLevelMgr.LiveAnchorLevelMonthSettleByUid() error = %v, wantErr %v", err, tt.wantErr)
// 			}
// 		})
// 	}
// }
// */

// // go test -timeout 30s -run ^TestAnchorLevelMgr_LiveAnchorLevelMonthSettleByUid4$ golang.52tt.com/services/anchor-level/manager -v -count=1
// func TestAnchorLevelMgr_LiveAnchorLevelMonthSettleByUid4(t *testing.T) {
// 	ctl := gomock.NewController(t)
// 	defer ctl.Finish()

// 	mockChannelLiveStatsCli := mockChannelLiveStats.NewMockIClient(ctl)
// 	mockpublicCli := publicmock.NewMockIClient(ctl)
// 	mockuserOlCli := userOlmock.NewMockIClient(ctl)
// 	mockconf := mocks.NewMockISDyConfigHandler(ctl)
// 	mockapiCli := mockapi.NewMockIClient(ctl)
// 	mockStore := mocks.NewMockIStore(ctl)
// 	mockaccountCli := accountmock.NewMockIClient(ctl)

// 	var (
// 		monthStatsResp = &channel_live_stats.BatchGetAnchorMonthlyStatsByUidResp{
// 			List: []*channel_live_stats.BatchGetAnchorMonthlyStatsByUidRespMonthStats{
// 				{
// 					AnchorUid:     4,
// 					AnchorIncome:  50*10000 - 1,
// 					NewFansCnt:    20,
// 					LiveActiveCnt: 20,
// 				},
// 			},
// 		}

// 		taskLimit = &store.LiveAnchorMonthTask{
// 			MonthIncome:       50 * 10000,
// 			MonthActiveDays:   20,
// 			MonthPlatformDays: 20,
// 			MonthNewFansCnt:   20,
// 			MonthConsumeCnt:   5,
// 		}
// 		taskList             = []*store.LiveAnchorMonthTask{}
// 		anchorlevel2Name     = map[uint32]*store.LiveAnchorMonthTask{}
// 		anchorUid2ChannelId2 = map[uint32]uint32{
// 			4: 1,
// 		}

// 		publicRsp2 = &publicPB.GetPublicAccountsByBindedIdListResp{
// 			PublicAccountList: []*publicPB.StPublicAccount{
// 				{
// 					Name:     "语音主播服务号",
// 					PublicId: 1,
// 				},
// 			},
// 		}
// 		ol4 = &userolpb.OnlineInfo{
// 			Uid: 4,
// 		}

// 		settleMonthTm = time.Date(2022, 12, 1, 0, 0, 0, 0, time.Local)
// 	)

// 	gomock.InOrder(

// 		// case1  收礼值，直播活跃天，新增粉丝团数不通过
// 		mockChannelLiveStatsCli.EXPECT().BatchGetAnchorMonthlyStatsByUid(gomock.Any(), gomock.Any(), gomock.Any()).Return(monthStatsResp, nil),

// 		mockconf.EXPECT().GetAnchorLevelUnfinishMsg().Return(""),
// 		mockpublicCli.EXPECT().GetPublicAccountsByBindedIdList(gomock.Any(), gomock.Any(), gomock.Any()).Return(publicRsp2, nil),
// 		mockuserOlCli.EXPECT().GetLastMobileOnlineInfo(gomock.Any(), gomock.Any()).Return(ol4, nil),
// 		mockconf.EXPECT().GetAppName(gomock.Any()).Return("1"),
// 		mockconf.EXPECT().GetLiveAnchorTaskUrl(gomock.Any(), gomock.Any()).Return("1"),
// 		mockapiCli.EXPECT().SendImMsg(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil),
// 	)

// 	type fields struct {
// 		store                      store.IStore
// 		dyconfig                   conf.ISDyConfigHandler
// 		LocalCache                 *LocalCache
// 		channelLiveMgr             channellivemgr.IClient
// 		channelLiveStats           channellivestats.IClient
// 		channelRecommendSvr        channel_recommend_svr.IClient
// 		entertainmentRecommendBack entertainmentrecommendback.IClient
// 		anchorcontractGoClient     anchorcontract_go.IClient
// 		reporter                   report.IFeishuReporterV2
// 		accountCli                 account.IClient
// 		channelbackgroundCli       channelbackground.IClient
// 		channelLotteryCli          channellottery.IClient
// 		channelLiveFansCli         channellivefans.IClient
// 		channelVotePkGoCli         channelvotepkgo.IClient
// 		publicCli                  public.IClient
// 		apiCli                     apicenter.IClient
// 		userOlCli                  userol.IClient
// 		channelCli                 channel.IClient
// 		anchorCheckCli             anchorcheck.IClient
// 	}
// 	type args struct {
// 		ctx                 context.Context
// 		settleMonthTm       time.Time
// 		anchorUids          []uint32
// 		anchorUid2ChannelId map[uint32]uint32
// 		taskLimit           *store.LiveAnchorMonthTask
// 		taskList            []*store.LiveAnchorMonthTask
// 		anchorlevel2Name    map[uint32]*store.LiveAnchorMonthTask
// 	}
// 	tests := []struct {
// 		name    string
// 		fields  fields
// 		args    args
// 		wantErr bool
// 	}{
// 		{
// 			fields: fields{
// 				channelLiveStats: mockChannelLiveStatsCli,
// 				publicCli:        mockpublicCli,
// 				userOlCli:        mockuserOlCli,
// 				dyconfig:         mockconf,
// 				apiCli:           mockapiCli,
// 				store:            mockStore,
// 				accountCli:       mockaccountCli,
// 			},
// 			args: args{
// 				ctx:                 context.Background(),
// 				settleMonthTm:       settleMonthTm,
// 				anchorUids:          []uint32{4},
// 				taskLimit:           taskLimit,
// 				taskList:            taskList,
// 				anchorlevel2Name:    anchorlevel2Name,
// 				anchorUid2ChannelId: anchorUid2ChannelId2,
// 			},
// 			wantErr: false,
// 		},
// 	}
// 	for _, tt := range tests {
// 		t.Run(tt.name, func(t *testing.T) {
// 			m := &AnchorLevelMgr{
// 				store:                      tt.fields.store,
// 				dyconfig:                   tt.fields.dyconfig,
// 				LocalCache:                 tt.fields.LocalCache,
// 				channelLiveMgr:             tt.fields.channelLiveMgr,
// 				channelLiveStats:           tt.fields.channelLiveStats,
// 				channelRecommendSvr:        tt.fields.channelRecommendSvr,
// 				entertainmentRecommendBack: tt.fields.entertainmentRecommendBack,
// 				anchorcontractGoClient:     tt.fields.anchorcontractGoClient,
// 				reporter:                   tt.fields.reporter,
// 				accountCli:                 tt.fields.accountCli,
// 				channelbackgroundCli:       tt.fields.channelbackgroundCli,
// 				channelLotteryCli:          tt.fields.channelLotteryCli,
// 				channelLiveFansCli:         tt.fields.channelLiveFansCli,
// 				channelVotePkGoCli:         tt.fields.channelVotePkGoCli,
// 				publicCli:                  tt.fields.publicCli,
// 				apiCli:                     tt.fields.apiCli,
// 				userOlCli:                  tt.fields.userOlCli,
// 				channelCli:                 tt.fields.channelCli,
// 				anchorCheckCli:             tt.fields.anchorCheckCli,
// 			}
// 			if err := m.LiveAnchorLevelMonthSettleByUid(tt.args.ctx, tt.args.settleMonthTm, tt.args.anchorUids, tt.args.anchorUid2ChannelId, tt.args.taskLimit, tt.args.taskList, tt.args.anchorlevel2Name); (err != nil) != tt.wantErr {
// 				t.Errorf("AnchorLevelMgr.LiveAnchorLevelMonthSettleByUid() error = %v, wantErr %v", err, tt.wantErr)
// 			}
// 		})
// 	}
// }

// // go test -timeout 30s -run ^TestDiff$ golang.52tt.com/services/anchor-level/manager -v -count=1
// func TestDiff(t *testing.T) {

// 	passUids := []uint32{1, 2, 3}
// 	anchorUids := []uint32{1, 5, 2, 3, 4}

// 	nopass := GetNoPassUids(anchorUids, passUids)

// 	t.Log(nopass)
// }

// // go test -timeout 30s -run ^TestAnchorLevelMgr_LiveAnchorLevelMonthSettleByUid5x$ golang.52tt.com/services/anchor-level/manager -v -count=1
// func TestAnchorLevelMgr_LiveAnchorLevelMonthSettleByUid5x(t *testing.T) {
// 	ctl := gomock.NewController(t)
// 	defer ctl.Finish()

// 	mockChannelLiveStatsCli := mockChannelLiveStats.NewMockIClient(ctl)
// 	mockpublicCli := publicmock.NewMockIClient(ctl)
// 	mockuserOlCli := userOlmock.NewMockIClient(ctl)
// 	mockconf := mocks.NewMockISDyConfigHandler(ctl)
// 	mockapiCli := mockapi.NewMockIClient(ctl)
// 	mockStore := mocks.NewMockIStore(ctl)
// 	mockaccountCli := accountmock.NewMockIClient(ctl)

// 	httpmock.ActivateNonDefault(urrc.GetdefaultHTTPClient())
// 	url := "http://dun-internal.52tt.com/api/urrc-sanction/biz/sanction/record/batch/list"
// 	//body := `{"code":200,"success":true,"data":[{"objId":"999","currentState":-1,"unblockType":"","unblockTime":-1,"records":[{"levelKey":"C","total":1,"details":[{"tenantId":"TT","id":"794635657747439616","appId":"1","appName":"TT语音","objTypeId":"1","objTypeName":"用户","objId":"999888","objName":"李j","objImg":"https://api.52tt.com/face?uid=2404178","objStatus":1,"objStatusDesc":"正常","objRemark":"[{\"attrDesc\":\"设备\",\"attrValue\":\"Xlg2xKLwQ+aPQK/t87BK0Q== : 正常\"},{\"attrDesc\":\"内部ID\",\"attrValue\":\"2404178\"},{\"attrDesc\":\"手机号码\",\"attrValue\":\"****\"},{\"attrDesc\":\"主体加密串\",\"attrValue\":\"dXJyY185OTk4ODg=\"}]","bizId":"1551860852149075969","bizName":"用户即时音频实时处罚业务","violationLevelKey":"","violationCount":0,"violationReasonId":-1,"violationReason":"低级趣味","pubViolationReason":"","violationEvidence":"","sanctionActionDesc":"暂无处罚","optUserId":"1260405679697551362","optUserName":"bpo3","optTime":1669622369893,"sourceType":"人工处罚","sourceRecordId":"","remark":"","createTime":1669622369893,"synStatus":2,"synStatusDesc":"同步成功","labelId":38,"labelName":"低级趣味","labelLevelKey":"C","levelType":2}]}]}],"msg":"操作成功"}`
// 	body := `{"code":200,"success":true,"data":[{"objId":"999","currentState":-1,"unblockType":"","unblockTime":-1,"records":[{"levelKey":"C","total":2,"details":[{"tenantId":"TT","id":"794635657747439616","appId":"1","appName":"TT语音","objTypeId":"1","objTypeName":"用户","objId":"999888","objName":"李j","objImg":"https://api.52tt.com/face?uid=2404178","objStatus":1,"objStatusDesc":"正常","objRemark":"[{\"attrDesc\":\"设备\",\"attrValue\":\"Xlg2xKLwQ+aPQK/t87BK0Q== : 正常\"},{\"attrDesc\":\"内部ID\",\"attrValue\":\"2404178\"},{\"attrDesc\":\"手机号码\",\"attrValue\":\"****\"},{\"attrDesc\":\"主体加密串\",\"attrValue\":\"dXJyY185OTk4ODg=\"}]","bizId":"1551860852149075969","bizName":"用户即时音频实时处罚业务","violationLevelKey":"","violationCount":0,"violationReasonId":-1,"violationReason":"低级趣味","pubViolationReason":"","violationEvidence":"","sanctionActionDesc":"暂无处罚","optUserId":"1260405679697551362","optUserName":"bpo3","optTime":1669622369893,"sourceType":"人工处罚","sourceRecordId":"","remark":"","createTime":1669622369893,"synStatus":2,"synStatusDesc":"同步成功","labelId":38,"labelName":"低级趣味","labelLevelKey":"C","levelType":2},{"tenantId":"TT","id":"794635657747439616","appId":"1","appName":"TT语音","objTypeId":"1","objTypeName":"用户","objId":"999888","objName":"李j","objImg":"https://api.52tt.com/face?uid=2404178","objStatus":1,"objStatusDesc":"正常","objRemark":"[{\"attrDesc\":\"设备\",\"attrValue\":\"Xlg2xKLwQ+aPQK/t87BK0Q== : 正常\"},{\"attrDesc\":\"内部ID\",\"attrValue\":\"2404178\"},{\"attrDesc\":\"手机号码\",\"attrValue\":\"****\"},{\"attrDesc\":\"主体加密串\",\"attrValue\":\"dXJyY185OTk4ODg=\"}]","bizId":"1551860852149075969","bizName":"用户即时音频实时处罚业务","violationLevelKey":"","violationCount":0,"violationReasonId":-1,"violationReason":"低级趣味","pubViolationReason":"","violationEvidence":"","sanctionActionDesc":"暂无处罚","optUserId":"1260405679697551362","optUserName":"bpo3","optTime":1669622369893,"sourceType":"人工处罚","sourceRecordId":"","remark":"","createTime":1669622369893,"synStatus":2,"synStatusDesc":"同步成功","labelId":38,"labelName":"低级趣味","labelLevelKey":"C","levelType":2}]}]}],"msg":"操作成功"}`

// 	response := httpmock.NewStringResponder(http.StatusOK, body)
// 	httpmock.RegisterResponder("POST", url, response)

// 	httpmock.ActivateNonDefault(datahouse.GetdefaultHTTPClient())

// 	// http://dap-moa.ttyuyin.com:8101/tt-activity/tt/queryMemberMonthIndex?apiToken=27bd4a08e51a58cd9abd881f6b072d67&start_date=202301&end_date=202301&user_id=4&guild_id=0&pageSize=1000
// 	url2 := "http://dap-moa.ttyuyin.com:8101/tt-activity/tt/queryMemberMonthIndex?apiToken=27bd4a08e51a58cd9abd881f6b072d67&start_date=202301&end_date=202301&user_id=4&guild_id=0&pageSize=1000"
// 	body2 := `{"code":200, "data":{"data":[{"user_id":4,"login_days":20}]} }`
// 	response2 := httpmock.NewStringResponder(http.StatusOK, body2)
// 	httpmock.RegisterResponder("GET", url2, response2)

// 	var (
// 		monthStatsResp = &channel_live_stats.BatchGetAnchorMonthlyStatsByUidResp{
// 			List: []*channel_live_stats.BatchGetAnchorMonthlyStatsByUidRespMonthStats{
// 				{
// 					AnchorUid:     4,
// 					AnchorIncome:  50 * 10000,
// 					NewFansCnt:    20,
// 					LiveActiveCnt: 20,
// 					ConsumerCnt:   2,
// 				},
// 				{
// 					AnchorUid:     5,
// 					AnchorIncome:  50 * 10000,
// 					NewFansCnt:    20,
// 					LiveActiveCnt: 20,
// 					ConsumerCnt:   0,
// 				},
// 			},
// 		}
// 		lastmonthStatsResp = &channel_live_stats.BatchGetAnchorMonthlyStatsByUidResp{
// 			List: []*channel_live_stats.BatchGetAnchorMonthlyStatsByUidRespMonthStats{
// 				{
// 					AnchorUid:     4,
// 					AnchorIncome:  0,
// 					NewFansCnt:    20,
// 					LiveActiveCnt: 20,
// 				},
// 			},
// 		}

// 		taskList = []*store.LiveAnchorMonthTask{
// 			{
// 				AnchorLevel:         1,
// 				AnchorLevelName:     "见习主播",
// 				MonthActiveDays:     20,
// 				MonthPlatformDays:   20,
// 				MonthNewFansCnt:     20,
// 				MonthConsumeCnt:     1,
// 				MonthIncome:         50 * 10000,
// 				MonthViolation_CCnt: 2,
// 			},
// 			{
// 				AnchorLevel:         2,
// 				AnchorLevelName:     "新锐主播",
// 				MonthActiveDays:     20,
// 				MonthPlatformDays:   20,
// 				MonthNewFansCnt:     20,
// 				MonthConsumeCnt:     2,
// 				MonthIncome:         80 * 10000,
// 				MonthViolation_CCnt: 2,
// 			},
// 			{
// 				AnchorLevel:         3,
// 				AnchorLevelName:     "精英主播",
// 				MonthActiveDays:     20,
// 				MonthPlatformDays:   20,
// 				MonthNewFansCnt:     20,
// 				MonthConsumeCnt:     3,
// 				MonthIncome:         100 * 10000,
// 				MonthViolation_CCnt: 2,
// 			},
// 			{
// 				AnchorLevel:         4,
// 				AnchorLevelName:     "大师主播",
// 				MonthActiveDays:     20,
// 				MonthPlatformDays:   20,
// 				MonthNewFansCnt:     20,
// 				MonthConsumeCnt:     4,
// 				MonthIncome:         500 * 10000,
// 				MonthViolation_CCnt: 2,
// 			},
// 			{
// 				AnchorLevel:         5,
// 				AnchorLevelName:     "传奇主播",
// 				MonthActiveDays:     20,
// 				MonthPlatformDays:   20,
// 				MonthNewFansCnt:     20,
// 				MonthConsumeCnt:     5,
// 				MonthIncome:         1000 * 10000,
// 				MonthViolation_CCnt: 2,
// 			},
// 		}
// 		taskLimit = taskList[0]

// 		anchorlevel2Name = map[uint32]*store.LiveAnchorMonthTask{
// 			1: {AnchorLevelName: "见习主播"},
// 			2: {AnchorLevelName: "新锐主播"},
// 			3: {AnchorLevelName: "精英主播"},
// 			4: {AnchorLevelName: "大师主播"},
// 			5: {AnchorLevelName: "传奇主播"},
// 		}
// 		anchorUid2ChannelId2 = map[uint32]uint32{
// 			4: 1,
// 			5: 2,
// 		}

// 		settleMonthTm = time.Date(2023, 1, 1, 0, 0, 0, 0, time.Local)

// 		usermp = map[uint32]*accountPB.UserResp{
// 			4: {Alias: "999"},
// 			5: {Alias: "888"},
// 		}

// 		publicRsp2 = &publicPB.GetPublicAccountsByBindedIdListResp{
// 			PublicAccountList: []*publicPB.StPublicAccount{
// 				{
// 					Name:     "语音主播服务号",
// 					PublicId: 1,
// 				},
// 			},
// 		}
// 		ol4 = &userolpb.OnlineInfo{
// 			Uid: 5,
// 		}
// 	)

// 	gomock.InOrder(

// 		// case6全部通过，判断具体等级，进阶任务
// 		mockChannelLiveStatsCli.EXPECT().BatchGetAnchorMonthlyStatsByUid(gomock.Any(), gomock.Any(), gomock.Any()).Return(monthStatsResp, nil),
// 		//mockStore.EXPECT().GetMonthConsumerCntFromGiftDB(gomock.Any(), gomock.Any(), gomock.Any()).Return([]uint32{1}, nil),
// 		//mockStore.EXPECT().GetMonthConsumerCntFromKnightDB(gomock.Any(), gomock.Any()).Return([]uint32{1, 2, 3, 4, 5}, nil),
// 		mockaccountCli.EXPECT().GetUsersMap(gomock.Any(), gomock.Any()).Return(usermp, nil),
// 		mockChannelLiveStatsCli.EXPECT().BatchGetAnchorMonthlyStatsByUid(gomock.Any(), gomock.Any(), gomock.Any()).Return(lastmonthStatsResp, nil),

// 		mockconf.EXPECT().GetAnchorLevelUnfinishMsg().Return(""),
// 		mockpublicCli.EXPECT().GetPublicAccountsByBindedIdList(gomock.Any(), gomock.Any(), gomock.Any()).Return(publicRsp2, nil),
// 		mockuserOlCli.EXPECT().GetLastMobileOnlineInfo(gomock.Any(), gomock.Any()).Return(ol4, nil),
// 		mockconf.EXPECT().GetAppName(gomock.Any()).Return("1"),
// 		mockconf.EXPECT().GetLiveAnchorTaskUrl(gomock.Any(), gomock.Any()).Return("1"),
// 		mockapiCli.EXPECT().SendImMsg(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil),

// 		mockStore.EXPECT().RecordLiveAnchorMonthTaskAward(gomock.Any(), gomock.Any()).Return(nil),
// 	)

// 	type fields struct {
// 		store                      store.IStore
// 		dyconfig                   conf.ISDyConfigHandler
// 		LocalCache                 *LocalCache
// 		channelLiveMgr             channellivemgr.IClient
// 		channelLiveStats           channellivestats.IClient
// 		channelRecommendSvr        channel_recommend_svr.IClient
// 		entertainmentRecommendBack entertainmentrecommendback.IClient
// 		anchorcontractGoClient     anchorcontract_go.IClient
// 		reporter                   report.IFeishuReporterV2
// 		accountCli                 account.IClient
// 		channelbackgroundCli       channelbackground.IClient
// 		channelLotteryCli          channellottery.IClient
// 		channelLiveFansCli         channellivefans.IClient
// 		channelVotePkGoCli         channelvotepkgo.IClient
// 		publicCli                  public.IClient
// 		apiCli                     apicenter.IClient
// 		userOlCli                  userol.IClient
// 		channelCli                 channel.IClient
// 		anchorCheckCli             anchorcheck.IClient
// 	}
// 	type args struct {
// 		ctx                 context.Context
// 		settleMonthTm       time.Time
// 		anchorUids          []uint32
// 		anchorUid2ChannelId map[uint32]uint32
// 		taskLimit           *store.LiveAnchorMonthTask
// 		taskList            []*store.LiveAnchorMonthTask
// 		anchorlevel2Name    map[uint32]*store.LiveAnchorMonthTask
// 	}
// 	tests := []struct {
// 		name    string
// 		fields  fields
// 		args    args
// 		wantErr bool
// 	}{
// 		{
// 			fields: fields{
// 				channelLiveStats: mockChannelLiveStatsCli,
// 				publicCli:        mockpublicCli,
// 				userOlCli:        mockuserOlCli,
// 				dyconfig:         mockconf,
// 				apiCli:           mockapiCli,
// 				store:            mockStore,
// 				accountCli:       mockaccountCli,
// 			},
// 			args: args{
// 				ctx:                 context.Background(),
// 				settleMonthTm:       settleMonthTm,
// 				anchorUids:          []uint32{4, 5},
// 				taskLimit:           taskLimit,
// 				taskList:            taskList,
// 				anchorlevel2Name:    anchorlevel2Name,
// 				anchorUid2ChannelId: anchorUid2ChannelId2,
// 			},
// 			wantErr: false,
// 		},
// 	}
// 	for _, tt := range tests {
// 		t.Run(tt.name, func(t *testing.T) {
// 			m := &AnchorLevelMgr{
// 				store:                      tt.fields.store,
// 				dyconfig:                   tt.fields.dyconfig,
// 				LocalCache:                 tt.fields.LocalCache,
// 				channelLiveMgr:             tt.fields.channelLiveMgr,
// 				channelLiveStats:           tt.fields.channelLiveStats,
// 				channelRecommendSvr:        tt.fields.channelRecommendSvr,
// 				entertainmentRecommendBack: tt.fields.entertainmentRecommendBack,
// 				anchorcontractGoClient:     tt.fields.anchorcontractGoClient,
// 				reporter:                   tt.fields.reporter,
// 				accountCli:                 tt.fields.accountCli,
// 				channelbackgroundCli:       tt.fields.channelbackgroundCli,
// 				channelLotteryCli:          tt.fields.channelLotteryCli,
// 				channelLiveFansCli:         tt.fields.channelLiveFansCli,
// 				channelVotePkGoCli:         tt.fields.channelVotePkGoCli,
// 				publicCli:                  tt.fields.publicCli,
// 				apiCli:                     tt.fields.apiCli,
// 				userOlCli:                  tt.fields.userOlCli,
// 				channelCli:                 tt.fields.channelCli,
// 				anchorCheckCli:             tt.fields.anchorCheckCli,
// 			}
// 			if err := m.LiveAnchorLevelMonthSettleByUid(tt.args.ctx, tt.args.settleMonthTm, tt.args.anchorUids, tt.args.anchorUid2ChannelId, tt.args.taskLimit, tt.args.taskList, tt.args.anchorlevel2Name); (err != nil) != tt.wantErr {
// 				t.Errorf("AnchorLevelMgr.LiveAnchorLevelMonthSettleByUid() error = %v, wantErr %v", err, tt.wantErr)
// 			}
// 		})
// 	}
// }

// // case 付费人数不通过
// // go test -timeout 30s -run ^TestAnchorLevelMgr_LiveAnchorLevelMonthSettleByUid_ConsumerCnt$ golang.52tt.com/services/anchor-level/manager -v -count=1
// func TestAnchorLevelMgr_LiveAnchorLevelMonthSettleByUid_ConsumerCnt(t *testing.T) {
// 	ctl := gomock.NewController(t)
// 	defer ctl.Finish()

// 	mockChannelLiveStatsCli := mockChannelLiveStats.NewMockIClient(ctl)
// 	mockpublicCli := publicmock.NewMockIClient(ctl)
// 	mockuserOlCli := userOlmock.NewMockIClient(ctl)
// 	mockconf := mocks.NewMockISDyConfigHandler(ctl)
// 	mockapiCli := mockapi.NewMockIClient(ctl)
// 	mockStore := mocks.NewMockIStore(ctl)

// 	var (
// 		anchorUids     = []uint32{2}
// 		monthStatsResp = &channel_live_stats.BatchGetAnchorMonthlyStatsByUidResp{
// 			List: []*channel_live_stats.BatchGetAnchorMonthlyStatsByUidRespMonthStats{
// 				{
// 					AnchorUid:     2,
// 					AnchorIncome:  50 * 10000,
// 					NewFansCnt:    20,
// 					LiveActiveCnt: 20,
// 				},
// 			},
// 		}

// 		taskLimit = &store.LiveAnchorMonthTask{
// 			MonthIncome:     50 * 10000,
// 			MonthActiveDays: 20,
// 			MonthNewFansCnt: 20,
// 			MonthConsumeCnt: 5,
// 		}
// 		taskList            = []*store.LiveAnchorMonthTask{}
// 		anchorlevel2Name    = map[uint32]*store.LiveAnchorMonthTask{}
// 		anchorUid2ChannelId = map[uint32]uint32{
// 			2: 1,
// 		}

// 		publicRsp2 = &publicPB.GetPublicAccountsByBindedIdListResp{
// 			PublicAccountList: []*publicPB.StPublicAccount{
// 				{
// 					Name:     "语音主播服务号",
// 					PublicId: 1,
// 				},
// 			},
// 		}
// 		ol2 = &userolpb.OnlineInfo{
// 			Uid: 2,
// 		}

// 		settleMonthTm = time.Date(2022, 12, 1, 0, 0, 0, 0, time.Local)
// 	)

// 	gomock.InOrder(

// 		//case3 付费人数不通过
// 		mockChannelLiveStatsCli.EXPECT().BatchGetAnchorMonthlyStatsByUid(gomock.Any(), gomock.Any(), gomock.Any()).Return(monthStatsResp, nil),
// 		mockconf.EXPECT().GetAnchorLevelUnfinishMsg().Return(""),
// 		mockpublicCli.EXPECT().GetPublicAccountsByBindedIdList(gomock.Any(), gomock.Any(), gomock.Any()).Return(publicRsp2, nil),
// 		mockuserOlCli.EXPECT().GetLastMobileOnlineInfo(gomock.Any(), gomock.Any()).Return(ol2, nil),
// 		mockconf.EXPECT().GetAppName(gomock.Any()).Return("1"),
// 		mockconf.EXPECT().GetLiveAnchorTaskUrl(gomock.Any(), gomock.Any()).Return("1"),
// 		mockapiCli.EXPECT().SendImMsg(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil),
// 	)

// 	type fields struct {
// 		store                      store.IStore
// 		dyconfig                   conf.ISDyConfigHandler
// 		LocalCache                 *LocalCache
// 		channelLiveMgr             channellivemgr.IClient
// 		channelLiveStats           channellivestats.IClient
// 		channelRecommendSvr        channel_recommend_svr.IClient
// 		entertainmentRecommendBack entertainmentrecommendback.IClient
// 		anchorcontractGoClient     anchorcontract_go.IClient
// 		reporter                   report.IFeishuReporterV2
// 		accountCli                 account.IClient
// 		channelbackgroundCli       channelbackground.IClient
// 		channelLotteryCli          channellottery.IClient
// 		channelLiveFansCli         channellivefans.IClient
// 		channelVotePkGoCli         channelvotepkgo.IClient
// 		publicCli                  public.IClient
// 		apiCli                     apicenter.IClient
// 		userOlCli                  userol.IClient
// 		channelCli                 channel.IClient
// 		anchorCheckCli             anchorcheck.IClient
// 	}
// 	type args struct {
// 		ctx                 context.Context
// 		settleMonthTm       time.Time
// 		anchorUids          []uint32
// 		anchorUid2ChannelId map[uint32]uint32
// 		taskLimit           *store.LiveAnchorMonthTask
// 		taskList            []*store.LiveAnchorMonthTask
// 		anchorlevel2Name    map[uint32]*store.LiveAnchorMonthTask
// 	}
// 	tests := []struct {
// 		name    string
// 		fields  fields
// 		args    args
// 		wantErr bool
// 	}{
// 		{
// 			fields: fields{
// 				channelLiveStats: mockChannelLiveStatsCli,
// 				publicCli:        mockpublicCli,
// 				userOlCli:        mockuserOlCli,
// 				dyconfig:         mockconf,
// 				apiCli:           mockapiCli,
// 				store:            mockStore,
// 			},
// 			args: args{
// 				ctx:                 context.Background(),
// 				settleMonthTm:       settleMonthTm,
// 				anchorUids:          anchorUids,
// 				taskLimit:           taskLimit,
// 				taskList:            taskList,
// 				anchorlevel2Name:    anchorlevel2Name,
// 				anchorUid2ChannelId: anchorUid2ChannelId,
// 			},
// 			wantErr: false,
// 		},
// 	}
// 	for _, tt := range tests {
// 		t.Run(tt.name, func(t *testing.T) {
// 			m := &AnchorLevelMgr{
// 				store:                      tt.fields.store,
// 				dyconfig:                   tt.fields.dyconfig,
// 				LocalCache:                 tt.fields.LocalCache,
// 				channelLiveMgr:             tt.fields.channelLiveMgr,
// 				channelLiveStats:           tt.fields.channelLiveStats,
// 				channelRecommendSvr:        tt.fields.channelRecommendSvr,
// 				entertainmentRecommendBack: tt.fields.entertainmentRecommendBack,
// 				anchorcontractGoClient:     tt.fields.anchorcontractGoClient,
// 				reporter:                   tt.fields.reporter,
// 				accountCli:                 tt.fields.accountCli,
// 				channelbackgroundCli:       tt.fields.channelbackgroundCli,
// 				channelLotteryCli:          tt.fields.channelLotteryCli,
// 				channelLiveFansCli:         tt.fields.channelLiveFansCli,
// 				channelVotePkGoCli:         tt.fields.channelVotePkGoCli,
// 				publicCli:                  tt.fields.publicCli,
// 				apiCli:                     tt.fields.apiCli,
// 				userOlCli:                  tt.fields.userOlCli,
// 				channelCli:                 tt.fields.channelCli,
// 				anchorCheckCli:             tt.fields.anchorCheckCli,
// 			}
// 			if err := m.LiveAnchorLevelMonthSettleByUid(tt.args.ctx, tt.args.settleMonthTm, tt.args.anchorUids, tt.args.anchorUid2ChannelId, tt.args.taskLimit, tt.args.taskList, tt.args.anchorlevel2Name); (err != nil) != tt.wantErr {
// 				t.Errorf("AnchorLevelMgr.LiveAnchorLevelMonthSettleByUid() error = %v, wantErr %v", err, tt.wantErr)
// 			}
// 		})
// 	}
// }

// // case 违规不通过
// // go test -timeout 30s -run ^TestAnchorLevelMgr_LiveAnchorLevelMonthSettleByUid_Valid$ golang.52tt.com/services/anchor-level/manager -v -count=1
// func TestAnchorLevelMgr_LiveAnchorLevelMonthSettleByUid_Valid(t *testing.T) {
// 	ctl := gomock.NewController(t)
// 	defer ctl.Finish()

// 	mockChannelLiveStatsCli := mockChannelLiveStats.NewMockIClient(ctl)
// 	mockpublicCli := publicmock.NewMockIClient(ctl)
// 	mockuserOlCli := userOlmock.NewMockIClient(ctl)
// 	mockconf := mocks.NewMockISDyConfigHandler(ctl)
// 	mockapiCli := mockapi.NewMockIClient(ctl)
// 	mockStore := mocks.NewMockIStore(ctl)
// 	mockaccountCli := accountmock.NewMockIClient(ctl)

// 	httpmock.ActivateNonDefault(urrc.GetdefaultHTTPClient())
// 	url := "http://dun-internal.52tt.com/api/urrc-sanction/biz/sanction/record/batch/list"
// 	body := `{"code":200,"success":true,"data":[{"objId":"999","currentState":-1,"unblockType":"","unblockTime":-1,"records":[{"levelKey":"A","total":1,"details":[{"tenantId":"TT","id":"794635657747439616","appId":"1","appName":"TT语音","objTypeId":"1","objTypeName":"用户","objId":"999888","objName":"李j","objImg":"https://api.52tt.com/face?uid=2404178","objStatus":1,"objStatusDesc":"正常","objRemark":"[{\"attrDesc\":\"设备\",\"attrValue\":\"Xlg2xKLwQ+aPQK/t87BK0Q== : 正常\"},{\"attrDesc\":\"内部ID\",\"attrValue\":\"2404178\"},{\"attrDesc\":\"手机号码\",\"attrValue\":\"****\"},{\"attrDesc\":\"主体加密串\",\"attrValue\":\"dXJyY185OTk4ODg=\"}]","bizId":"1551860852149075969","bizName":"用户即时音频实时处罚业务","violationLevelKey":"","violationCount":0,"violationReasonId":-1,"violationReason":"低级趣味","pubViolationReason":"","violationEvidence":"","sanctionActionDesc":"暂无处罚","optUserId":"1260405679697551362","optUserName":"bpo3","optTime":1669622369893,"sourceType":"人工处罚","sourceRecordId":"","remark":"","createTime":1669622369893,"synStatus":2,"synStatusDesc":"同步成功","labelId":38,"labelName":"低级趣味","labelLevelKey":"C","levelType":2}]}]}],"msg":"操作成功"}`
// 	response := httpmock.NewStringResponder(http.StatusOK, body)
// 	httpmock.RegisterResponder("POST", url, response)

// 	var (
// 		anchorUids     = []uint32{2}
// 		monthStatsResp = &channel_live_stats.BatchGetAnchorMonthlyStatsByUidResp{
// 			List: []*channel_live_stats.BatchGetAnchorMonthlyStatsByUidRespMonthStats{
// 				{
// 					AnchorUid:     2,
// 					AnchorIncome:  50 * 10000,
// 					NewFansCnt:    20,
// 					LiveActiveCnt: 20,
// 					ConsumerCnt:   5,
// 				},
// 			},
// 		}

// 		taskLimit = &store.LiveAnchorMonthTask{
// 			MonthIncome:     50 * 10000,
// 			MonthActiveDays: 20,
// 			MonthNewFansCnt: 20,
// 			MonthConsumeCnt: 5,
// 		}
// 		taskList            = []*store.LiveAnchorMonthTask{}
// 		anchorlevel2Name    = map[uint32]*store.LiveAnchorMonthTask{}
// 		anchorUid2ChannelId = map[uint32]uint32{
// 			2: 1,
// 		}

// 		publicRsp2 = &publicPB.GetPublicAccountsByBindedIdListResp{
// 			PublicAccountList: []*publicPB.StPublicAccount{
// 				{
// 					Name:     "语音主播服务号",
// 					PublicId: 1,
// 				},
// 			},
// 		}
// 		ol2 = &userolpb.OnlineInfo{
// 			Uid: 2,
// 		}

// 		settleMonthTm = time.Date(2022, 12, 1, 0, 0, 0, 0, time.Local)

// 		usermp = map[uint32]*accountPB.UserResp{
// 			2: {Alias: "999"},
// 		}
// 	)

// 	gomock.InOrder(
// 		mockChannelLiveStatsCli.EXPECT().BatchGetAnchorMonthlyStatsByUid(gomock.Any(), gomock.Any(), gomock.Any()).Return(monthStatsResp, nil),
// 		mockaccountCli.EXPECT().GetUsersMap(gomock.Any(), gomock.Any()).Return(usermp, nil),
// 		mockconf.EXPECT().GetAnchorLevelUnfinishMsg().Return(""),
// 		mockpublicCli.EXPECT().GetPublicAccountsByBindedIdList(gomock.Any(), gomock.Any(), gomock.Any()).Return(publicRsp2, nil),
// 		mockuserOlCli.EXPECT().GetLastMobileOnlineInfo(gomock.Any(), gomock.Any()).Return(ol2, nil),
// 		mockconf.EXPECT().GetAppName(gomock.Any()).Return("1"),
// 		mockconf.EXPECT().GetLiveAnchorTaskUrl(gomock.Any(), gomock.Any()).Return("1"),
// 		mockapiCli.EXPECT().SendImMsg(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil),
// 	)

// 	type fields struct {
// 		store                      store.IStore
// 		dyconfig                   conf.ISDyConfigHandler
// 		LocalCache                 *LocalCache
// 		channelLiveMgr             channellivemgr.IClient
// 		channelLiveStats           channellivestats.IClient
// 		channelRecommendSvr        channel_recommend_svr.IClient
// 		entertainmentRecommendBack entertainmentrecommendback.IClient
// 		anchorcontractGoClient     anchorcontract_go.IClient
// 		reporter                   report.IFeishuReporterV2
// 		accountCli                 account.IClient
// 		channelbackgroundCli       channelbackground.IClient
// 		channelLotteryCli          channellottery.IClient
// 		channelLiveFansCli         channellivefans.IClient
// 		channelVotePkGoCli         channelvotepkgo.IClient
// 		publicCli                  public.IClient
// 		apiCli                     apicenter.IClient
// 		userOlCli                  userol.IClient
// 		channelCli                 channel.IClient
// 		anchorCheckCli             anchorcheck.IClient
// 	}
// 	type args struct {
// 		ctx                 context.Context
// 		settleMonthTm       time.Time
// 		anchorUids          []uint32
// 		anchorUid2ChannelId map[uint32]uint32
// 		taskLimit           *store.LiveAnchorMonthTask
// 		taskList            []*store.LiveAnchorMonthTask
// 		anchorlevel2Name    map[uint32]*store.LiveAnchorMonthTask
// 	}
// 	tests := []struct {
// 		name    string
// 		fields  fields
// 		args    args
// 		wantErr bool
// 	}{
// 		{
// 			fields: fields{
// 				channelLiveStats: mockChannelLiveStatsCli,
// 				publicCli:        mockpublicCli,
// 				userOlCli:        mockuserOlCli,
// 				dyconfig:         mockconf,
// 				apiCli:           mockapiCli,
// 				store:            mockStore,
// 				accountCli:       mockaccountCli,
// 			},
// 			args: args{
// 				ctx:                 context.Background(),
// 				settleMonthTm:       settleMonthTm,
// 				anchorUids:          anchorUids,
// 				taskLimit:           taskLimit,
// 				taskList:            taskList,
// 				anchorlevel2Name:    anchorlevel2Name,
// 				anchorUid2ChannelId: anchorUid2ChannelId,
// 			},
// 			wantErr: false,
// 		},
// 	}
// 	for _, tt := range tests {
// 		t.Run(tt.name, func(t *testing.T) {
// 			m := &AnchorLevelMgr{
// 				store:                      tt.fields.store,
// 				dyconfig:                   tt.fields.dyconfig,
// 				LocalCache:                 tt.fields.LocalCache,
// 				channelLiveMgr:             tt.fields.channelLiveMgr,
// 				channelLiveStats:           tt.fields.channelLiveStats,
// 				channelRecommendSvr:        tt.fields.channelRecommendSvr,
// 				entertainmentRecommendBack: tt.fields.entertainmentRecommendBack,
// 				anchorcontractGoClient:     tt.fields.anchorcontractGoClient,
// 				reporter:                   tt.fields.reporter,
// 				accountCli:                 tt.fields.accountCli,
// 				channelbackgroundCli:       tt.fields.channelbackgroundCli,
// 				channelLotteryCli:          tt.fields.channelLotteryCli,
// 				channelLiveFansCli:         tt.fields.channelLiveFansCli,
// 				channelVotePkGoCli:         tt.fields.channelVotePkGoCli,
// 				publicCli:                  tt.fields.publicCli,
// 				apiCli:                     tt.fields.apiCli,
// 				userOlCli:                  tt.fields.userOlCli,
// 				channelCli:                 tt.fields.channelCli,
// 				anchorCheckCli:             tt.fields.anchorCheckCli,
// 			}
// 			if err := m.LiveAnchorLevelMonthSettleByUid(tt.args.ctx, tt.args.settleMonthTm, tt.args.anchorUids, tt.args.anchorUid2ChannelId, tt.args.taskLimit, tt.args.taskList, tt.args.anchorlevel2Name); (err != nil) != tt.wantErr {
// 				t.Errorf("AnchorLevelMgr.LiveAnchorLevelMonthSettleByUid() error = %v, wantErr %v", err, tt.wantErr)
// 			}
// 		})
// 	}
// }

// // go test -timeout 30s -run ^TestAnchorLevelMgr_LiveAnchorLevelMonthSettleByUid2$ golang.52tt.com/services/anchor-level/manager -v -count=1
// func TestAnchorLevelMgr_LiveAnchorLevelMonthSettleByUid2(t *testing.T) {

// 	return

// 	ctl := gomock.NewController(t)
// 	defer ctl.Finish()

// 	mockChannelLiveStatsCli := mockChannelLiveStats.NewMockIClient(ctl)
// 	mockpublicCli := publicmock.NewMockIClient(ctl)
// 	mockuserOlCli := userOlmock.NewMockIClient(ctl)
// 	mockconf := mocks.NewMockISDyConfigHandler(ctl)
// 	mockapiCli := mockapi.NewMockIClient(ctl)
// 	mockStore := mocks.NewMockIStore(ctl)
// 	mockaccountCli := accountmock.NewMockIClient(ctl)

// 	httpmock.ActivateNonDefault(urrc.GetdefaultHTTPClient())
// 	url := "http://dun-internal.52tt.com/api/urrc-sanction/biz/sanction/record/batch/list"
// 	body := `{"code":200,"success":true,"data":[{"objId":"999","currentState":-1,"unblockType":"","unblockTime":-1,"records":[{"levelKey":"A","total":1,"details":[{"tenantId":"TT","id":"794635657747439616","appId":"1","appName":"TT语音","objTypeId":"1","objTypeName":"用户","objId":"999888","objName":"李j","objImg":"https://api.52tt.com/face?uid=2404178","objStatus":1,"objStatusDesc":"正常","objRemark":"[{\"attrDesc\":\"设备\",\"attrValue\":\"Xlg2xKLwQ+aPQK/t87BK0Q== : 正常\"},{\"attrDesc\":\"内部ID\",\"attrValue\":\"2404178\"},{\"attrDesc\":\"手机号码\",\"attrValue\":\"****\"},{\"attrDesc\":\"主体加密串\",\"attrValue\":\"dXJyY185OTk4ODg=\"}]","bizId":"1551860852149075969","bizName":"用户即时音频实时处罚业务","violationLevelKey":"","violationCount":0,"violationReasonId":-1,"violationReason":"低级趣味","pubViolationReason":"","violationEvidence":"","sanctionActionDesc":"暂无处罚","optUserId":"1260405679697551362","optUserName":"bpo3","optTime":1669622369893,"sourceType":"人工处罚","sourceRecordId":"","remark":"","createTime":1669622369893,"synStatus":2,"synStatusDesc":"同步成功","labelId":38,"labelName":"低级趣味","labelLevelKey":"C","levelType":2}]}]}],"msg":"操作成功"}`
// 	response := httpmock.NewStringResponder(http.StatusOK, body)
// 	httpmock.RegisterResponder("POST", url, response)

// 	var (
// 		monthStatsResp = &channel_live_stats.BatchGetAnchorMonthlyStatsByUidResp{
// 			List: []*channel_live_stats.BatchGetAnchorMonthlyStatsByUidRespMonthStats{
// 				{
// 					AnchorUid:     2,
// 					AnchorIncome:  50 * 10000,
// 					NewFansCnt:    20,
// 					LiveActiveCnt: 20,
// 				},
// 				{
// 					AnchorUid:     4,
// 					AnchorIncome:  50 * 10000,
// 					NewFansCnt:    20,
// 					LiveActiveCnt: 20,
// 				},
// 			},
// 		}

// 		taskLimit = &store.LiveAnchorMonthTask{
// 			MonthIncome:     50 * 10000,
// 			MonthActiveDays: 20,
// 			MonthNewFansCnt: 20,
// 			MonthConsumeCnt: 5,
// 		}
// 		taskList            = []*store.LiveAnchorMonthTask{}
// 		anchorlevel2Name    = map[uint32]*store.LiveAnchorMonthTask{}
// 		anchorUid2ChannelId = map[uint32]uint32{
// 			2: 1,
// 		}
// 		anchorUid2ChannelId2 = map[uint32]uint32{
// 			4: 1,
// 		}

// 		publicRsp2 = &publicPB.GetPublicAccountsByBindedIdListResp{
// 			PublicAccountList: []*publicPB.StPublicAccount{
// 				{
// 					Name:     "语音主播服务号",
// 					PublicId: 1,
// 				},
// 			},
// 		}
// 		ol2 = &userolpb.OnlineInfo{
// 			Uid: 2,
// 		}
// 		ol4 = &userolpb.OnlineInfo{
// 			Uid: 4,
// 		}

// 		settleMonthTm = time.Date(2022, 12, 1, 0, 0, 0, 0, time.Local)

// 		usermp = map[uint32]*accountPB.UserResp{
// 			4: {Alias: "999"},
// 		}
// 	)

// 	gomock.InOrder(

// 		//case3 付费人数不通过
// 		mockChannelLiveStatsCli.EXPECT().BatchGetAnchorMonthlyStatsByUid(gomock.Any(), gomock.Any(), gomock.Any()).Return(monthStatsResp, nil),
// 		mockconf.EXPECT().GetAnchorLevelUnfinishMsg().Return(""),
// 		mockpublicCli.EXPECT().GetPublicAccountsByBindedIdList(gomock.Any(), gomock.Any(), gomock.Any()).Return(publicRsp2, nil),
// 		mockuserOlCli.EXPECT().GetLastMobileOnlineInfo(gomock.Any(), gomock.Any()).Return(ol2, nil),
// 		mockconf.EXPECT().GetAppName(gomock.Any()).Return("1"),
// 		mockconf.EXPECT().GetLiveAnchorTaskUrl(gomock.Any(), gomock.Any()).Return("1"),
// 		mockapiCli.EXPECT().SendImMsg(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil),

// 		// case4 违规不通过
// 		mockChannelLiveStatsCli.EXPECT().BatchGetAnchorMonthlyStatsByUid(gomock.Any(), gomock.Any(), gomock.Any()).Return(monthStatsResp, nil),
// 		mockaccountCli.EXPECT().GetUsersMap(gomock.Any(), gomock.Any()).Return(usermp, nil),
// 		mockconf.EXPECT().GetAnchorLevelUnfinishMsg().Return(""),
// 		mockpublicCli.EXPECT().GetPublicAccountsByBindedIdList(gomock.Any(), gomock.Any(), gomock.Any()).Return(publicRsp2, nil),
// 		mockuserOlCli.EXPECT().GetLastMobileOnlineInfo(gomock.Any(), gomock.Any()).Return(ol4, nil),
// 		mockconf.EXPECT().GetAppName(gomock.Any()).Return("1"),
// 		mockconf.EXPECT().GetLiveAnchorTaskUrl(gomock.Any(), gomock.Any()).Return("1"),
// 		mockapiCli.EXPECT().SendImMsg(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil),
// 	)

// 	type fields struct {
// 		store                      store.IStore
// 		dyconfig                   conf.ISDyConfigHandler
// 		LocalCache                 *LocalCache
// 		channelLiveMgr             channellivemgr.IClient
// 		channelLiveStats           channellivestats.IClient
// 		channelRecommendSvr        channel_recommend_svr.IClient
// 		entertainmentRecommendBack entertainmentrecommendback.IClient
// 		anchorcontractGoClient     anchorcontract_go.IClient
// 		reporter                   report.IFeishuReporterV2
// 		accountCli                 account.IClient
// 		channelbackgroundCli       channelbackground.IClient
// 		channelLotteryCli          channellottery.IClient
// 		channelLiveFansCli         channellivefans.IClient
// 		channelVotePkGoCli         channelvotepkgo.IClient
// 		publicCli                  public.IClient
// 		apiCli                     apicenter.IClient
// 		userOlCli                  userol.IClient
// 		channelCli                 channel.IClient
// 		anchorCheckCli             anchorcheck.IClient
// 	}
// 	type args struct {
// 		ctx                 context.Context
// 		settleMonthTm       time.Time
// 		anchorUids          []uint32
// 		anchorUid2ChannelId map[uint32]uint32
// 		taskLimit           *store.LiveAnchorMonthTask
// 		taskList            []*store.LiveAnchorMonthTask
// 		anchorlevel2Name    map[uint32]*store.LiveAnchorMonthTask
// 	}
// 	tests := []struct {
// 		name    string
// 		fields  fields
// 		args    args
// 		wantErr bool
// 	}{
// 		{
// 			fields: fields{
// 				channelLiveStats: mockChannelLiveStatsCli,
// 				publicCli:        mockpublicCli,
// 				userOlCli:        mockuserOlCli,
// 				dyconfig:         mockconf,
// 				apiCli:           mockapiCli,
// 				store:            mockStore,
// 			},
// 			args: args{
// 				ctx:                 context.Background(),
// 				settleMonthTm:       settleMonthTm,
// 				anchorUids:          []uint32{2, 4},
// 				taskLimit:           taskLimit,
// 				taskList:            taskList,
// 				anchorlevel2Name:    anchorlevel2Name,
// 				anchorUid2ChannelId: anchorUid2ChannelId,
// 			},
// 			wantErr: false,
// 		},
// 		{
// 			fields: fields{
// 				channelLiveStats: mockChannelLiveStatsCli,
// 				publicCli:        mockpublicCli,
// 				userOlCli:        mockuserOlCli,
// 				dyconfig:         mockconf,
// 				apiCli:           mockapiCli,
// 				store:            mockStore,
// 				accountCli:       mockaccountCli,
// 			},
// 			args: args{
// 				ctx:                 context.Background(),
// 				settleMonthTm:       settleMonthTm,
// 				anchorUids:          []uint32{2, 4},
// 				taskLimit:           taskLimit,
// 				taskList:            taskList,
// 				anchorlevel2Name:    anchorlevel2Name,
// 				anchorUid2ChannelId: anchorUid2ChannelId2,
// 			},
// 			wantErr: false,
// 		},
// 	}
// 	for _, tt := range tests {
// 		t.Run(tt.name, func(t *testing.T) {
// 			m := &AnchorLevelMgr{
// 				store:                      tt.fields.store,
// 				dyconfig:                   tt.fields.dyconfig,
// 				LocalCache:                 tt.fields.LocalCache,
// 				channelLiveMgr:             tt.fields.channelLiveMgr,
// 				channelLiveStats:           tt.fields.channelLiveStats,
// 				channelRecommendSvr:        tt.fields.channelRecommendSvr,
// 				entertainmentRecommendBack: tt.fields.entertainmentRecommendBack,
// 				anchorcontractGoClient:     tt.fields.anchorcontractGoClient,
// 				reporter:                   tt.fields.reporter,
// 				accountCli:                 tt.fields.accountCli,
// 				channelbackgroundCli:       tt.fields.channelbackgroundCli,
// 				channelLotteryCli:          tt.fields.channelLotteryCli,
// 				channelLiveFansCli:         tt.fields.channelLiveFansCli,
// 				channelVotePkGoCli:         tt.fields.channelVotePkGoCli,
// 				publicCli:                  tt.fields.publicCli,
// 				apiCli:                     tt.fields.apiCli,
// 				userOlCli:                  tt.fields.userOlCli,
// 				channelCli:                 tt.fields.channelCli,
// 				anchorCheckCli:             tt.fields.anchorCheckCli,
// 			}
// 			if err := m.LiveAnchorLevelMonthSettleByUid(tt.args.ctx, tt.args.settleMonthTm, tt.args.anchorUids, tt.args.anchorUid2ChannelId, tt.args.taskLimit, tt.args.taskList, tt.args.anchorlevel2Name); (err != nil) != tt.wantErr {
// 				t.Errorf("AnchorLevelMgr.LiveAnchorLevelMonthSettleByUid() error = %v, wantErr %v", err, tt.wantErr)
// 			}
// 		})
// 	}

// }

// // go test -timeout 30s -run ^TestAnchorLevelMgr_LiveAnchorLevelMonthSettleByUid$ golang.52tt.com/services/anchor-level/manager -v -count=1
// func TestAnchorLevelMgr_LiveAnchorLevelMonthSettleByUid(t *testing.T) {

// 	ctl := gomock.NewController(t)
// 	defer ctl.Finish()

// 	mockChannelLiveStatsCli := mockChannelLiveStats.NewMockIClient(ctl)
// 	mockpublicCli := publicmock.NewMockIClient(ctl)
// 	mockuserOlCli := userOlmock.NewMockIClient(ctl)
// 	mockconf := mocks.NewMockISDyConfigHandler(ctl)
// 	mockapiCli := mockapi.NewMockIClient(ctl)
// 	//mockStore := mocks.NewMockIStore(ctl)

// 	var (
// 		serr           = protocol.NewServerError(-2)
// 		monthStatsResp = &channel_live_stats.BatchGetAnchorMonthlyStatsByUidResp{
// 			List: []*channel_live_stats.BatchGetAnchorMonthlyStatsByUidRespMonthStats{
// 				{
// 					AnchorUid: 1,
// 				},
// 				{
// 					AnchorUid:     2,
// 					AnchorIncome:  50 * 10000,
// 					NewFansCnt:    20,
// 					LiveActiveCnt: 20,
// 				},
// 			},
// 		}

// 		taskLimit = &store.LiveAnchorMonthTask{
// 			MonthIncome:     50 * 10000,
// 			MonthActiveDays: 20,
// 			MonthNewFansCnt: 20,
// 			MonthConsumeCnt: 20,
// 		}
// 		taskList         = []*store.LiveAnchorMonthTask{}
// 		anchorlevel2Name = map[uint32]*store.LiveAnchorMonthTask{}

// 		publicRsp2 = &publicPB.GetPublicAccountsByBindedIdListResp{
// 			PublicAccountList: []*publicPB.StPublicAccount{
// 				{
// 					Name:     "语音主播服务号",
// 					PublicId: 1,
// 				},
// 			},
// 		}
// 		ol1 = &userolpb.OnlineInfo{
// 			Uid: 1,
// 		}
// 		//ol2 = &userolpb.OnlineInfo{
// 		//	Uid: 1,
// 		//}
// 	)

// 	gomock.InOrder(
// 		//case1
// 		mockChannelLiveStatsCli.EXPECT().BatchGetAnchorMonthlyStatsByUid(gomock.Any(), gomock.Any(), gomock.Any()).Return(monthStatsResp, serr),

// 		//case2
// 		mockChannelLiveStatsCli.EXPECT().BatchGetAnchorMonthlyStatsByUid(gomock.Any(), gomock.Any(), gomock.Any()).Return(monthStatsResp, nil),
// 		mockconf.EXPECT().GetAnchorLevelUnfinishMsg().Return(""),
// 		mockpublicCli.EXPECT().GetPublicAccountsByBindedIdList(gomock.Any(), gomock.Any(), gomock.Any()).Return(publicRsp2, nil),
// 		mockuserOlCli.EXPECT().GetLastMobileOnlineInfo(gomock.Any(), gomock.Any()).Return(ol1, nil),
// 		mockconf.EXPECT().GetAppName(gomock.Any()).Return("1"),
// 		mockconf.EXPECT().GetLiveAnchorTaskUrl(gomock.Any(), gomock.Any()).Return("1"),
// 		mockapiCli.EXPECT().SendImMsg(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil),

// 		//case3
// 		//mockChannelLiveStatsCli.EXPECT().BatchGetAnchorMonthlyStatsByUid(gomock.Any(), gomock.Any(), gomock.Any()).Return(monthStatsResp, nil),
// 		//mockStore.EXPECT().GetMonthConsumerCntFromGiftDB(gomock.Any(), gomock.Any(), gomock.Any()).Return([]uint32{1}, nil),
// 		//mockStore.EXPECT().GetMonthConsumerCntFromKnightDB(gomock.Any(), gomock.Any()).Return([]uint32{1, 2}, nil),
// 		//mockconf.EXPECT().GetAnchorLevelUnfinishMsg().Return(""),
// 		//mockpublicCli.EXPECT().GetPublicAccountsByBindedIdList(gomock.Any(), gomock.Any(), gomock.Any()).Return(publicRsp2, nil),
// 		//mockuserOlCli.EXPECT().GetLastMobileOnlineInfo(gomock.Any(), gomock.Any()).Return(ol2, nil),
// 		//mockconf.EXPECT().GetAppName(gomock.Any()).Return("1"),
// 		//mockconf.EXPECT().GetLiveAnchorTaskUrl(gomock.Any(), gomock.Any()).Return("1"),
// 		//mockapiCli.EXPECT().SendImMsg(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil),
// 	)

// 	type fields struct {
// 		store                      store.IStore
// 		dyconfig                   conf.ISDyConfigHandler
// 		LocalCache                 *LocalCache
// 		channelLiveMgr             channellivemgr.IClient
// 		channelLiveStats           channellivestats.IClient
// 		channelRecommendSvr        channel_recommend_svr.IClient
// 		entertainmentRecommendBack entertainmentrecommendback.IClient
// 		anchorcontractGoClient     anchorcontract_go.IClient
// 		reporter                   report.IFeishuReporterV2
// 		accountCli                 account.IClient
// 		channelbackgroundCli       channelbackground.IClient
// 		channelLotteryCli          channellottery.IClient
// 		channelLiveFansCli         channellivefans.IClient
// 		channelVotePkGoCli         channelvotepkgo.IClient
// 		publicCli                  public.IClient
// 		apiCli                     apicenter.IClient
// 		userOlCli                  userol.IClient
// 		channelCli                 channel.IClient
// 		anchorCheckCli             anchorcheck.IClient
// 	}
// 	type args struct {
// 		ctx                 context.Context
// 		settleMonthTm       time.Time
// 		anchorUids          []uint32
// 		anchorUid2ChannelId map[uint32]uint32
// 		taskLimit           *store.LiveAnchorMonthTask
// 		taskList            []*store.LiveAnchorMonthTask
// 		anchorlevel2Name    map[uint32]*store.LiveAnchorMonthTask
// 	}
// 	tests := []struct {
// 		name    string
// 		fields  fields
// 		args    args
// 		wantErr bool
// 	}{
// 		{
// 			fields: fields{
// 				channelLiveStats: mockChannelLiveStatsCli,
// 				publicCli:        mockpublicCli,
// 				userOlCli:        mockuserOlCli,
// 				dyconfig:         mockconf,
// 				apiCli:           mockapiCli,
// 			},
// 			args: args{ctx: context.Background(), settleMonthTm: time.Now(), anchorUids: []uint32{1},
// 				taskLimit:        taskLimit,
// 				taskList:         taskList,
// 				anchorlevel2Name: anchorlevel2Name,
// 			},
// 			wantErr: true,
// 		},

// 		{
// 			fields: fields{
// 				channelLiveStats: mockChannelLiveStatsCli,
// 				publicCli:        mockpublicCli,
// 				userOlCli:        mockuserOlCli,
// 				dyconfig:         mockconf,
// 				apiCli:           mockapiCli,
// 			},
// 			args: args{ctx: context.Background(), settleMonthTm: time.Now(), anchorUids: []uint32{1},
// 				taskLimit:        taskLimit,
// 				taskList:         taskList,
// 				anchorlevel2Name: anchorlevel2Name,
// 			},
// 			wantErr: false,
// 		},

// 		/*
// 			{
// 				fields: fields{
// 					channelLiveStats: mockChannelLiveStatsCli,
// 					publicCli:        mockpublicCli,
// 					userOlCli:        mockuserOlCli,
// 					dyconfig:         mockconf,
// 					apiCli:           mockapiCli,
// 					store:            mockStore,
// 				},
// 				args: args{ctx: context.Background(),
// 					settleMonthTm: time.Now(), anchorUids: []uint32{2},
// 					taskLimit:        taskLimit,
// 					taskList:         taskList,
// 					anchorlevel2Name: anchorlevel2Name,
// 				},
// 				wantErr: false,
// 			},
// 		*/
// 	}
// 	for _, tt := range tests {
// 		t.Run(tt.name, func(t *testing.T) {
// 			m := &AnchorLevelMgr{
// 				store:                      tt.fields.store,
// 				dyconfig:                   tt.fields.dyconfig,
// 				LocalCache:                 tt.fields.LocalCache,
// 				channelLiveMgr:             tt.fields.channelLiveMgr,
// 				channelLiveStats:           tt.fields.channelLiveStats,
// 				channelRecommendSvr:        tt.fields.channelRecommendSvr,
// 				entertainmentRecommendBack: tt.fields.entertainmentRecommendBack,
// 				anchorcontractGoClient:     tt.fields.anchorcontractGoClient,
// 				reporter:                   tt.fields.reporter,
// 				accountCli:                 tt.fields.accountCli,
// 				channelbackgroundCli:       tt.fields.channelbackgroundCli,
// 				channelLotteryCli:          tt.fields.channelLotteryCli,
// 				channelLiveFansCli:         tt.fields.channelLiveFansCli,
// 				channelVotePkGoCli:         tt.fields.channelVotePkGoCli,
// 				publicCli:                  tt.fields.publicCli,
// 				apiCli:                     tt.fields.apiCli,
// 				userOlCli:                  tt.fields.userOlCli,
// 				channelCli:                 tt.fields.channelCli,
// 				anchorCheckCli:             tt.fields.anchorCheckCli,
// 			}
// 			if err := m.LiveAnchorLevelMonthSettleByUid(tt.args.ctx, tt.args.settleMonthTm, tt.args.anchorUids, tt.args.anchorUid2ChannelId, tt.args.taskLimit, tt.args.taskList, tt.args.anchorlevel2Name); (err != nil) != tt.wantErr {
// 				t.Errorf("AnchorLevelMgr.LiveAnchorLevelMonthSettleByUid() error = %v, wantErr %v", err, tt.wantErr)
// 			}
// 		})
// 	}
// }

// func TestAnchorLevelMgr_AwardUser(t *testing.T) {
// 	type fields struct {
// 		store                      store.IStore
// 		dyconfig                   conf.ISDyConfigHandler
// 		LocalCache                 *LocalCache
// 		channelLiveMgr             channellivemgr.IClient
// 		channelLiveStats           channellivestats.IClient
// 		channelRecommendSvr        channel_recommend_svr.IClient
// 		entertainmentRecommendBack entertainmentrecommendback.IClient
// 		anchorcontractGoClient     anchorcontract_go.IClient
// 		reporter                   report.IFeishuReporterV2
// 		accountCli                 account.IClient
// 		channelbackgroundCli       channelbackground.IClient
// 		channelLotteryCli          channellottery.IClient
// 		channelLiveFansCli         channellivefans.IClient
// 		channelVotePkGoCli         channelvotepkgo.IClient
// 		publicCli                  public.IClient
// 		apiCli                     apicenter.IClient
// 		userOlCli                  userol.IClient
// 		channelCli                 channel.IClient
// 		anchorCheckCli             anchorcheck.IClient
// 	}
// 	type args struct {
// 		info              *store.LiveAnchorMonthTaskAwardLog
// 		level2AwardConfig map[uint32]*store.LiveAnchorMonthAwardConfig
// 	}
// 	tests := []struct {
// 		name    string
// 		fields  fields
// 		args    args
// 		wantErr bool
// 	}{
// 		// TODO: Add test cases.
// 	}
// 	for _, tt := range tests {
// 		t.Run(tt.name, func(t *testing.T) {
// 			m := &AnchorLevelMgr{
// 				store:                      tt.fields.store,
// 				dyconfig:                   tt.fields.dyconfig,
// 				LocalCache:                 tt.fields.LocalCache,
// 				channelLiveMgr:             tt.fields.channelLiveMgr,
// 				channelLiveStats:           tt.fields.channelLiveStats,
// 				channelRecommendSvr:        tt.fields.channelRecommendSvr,
// 				entertainmentRecommendBack: tt.fields.entertainmentRecommendBack,
// 				anchorcontractGoClient:     tt.fields.anchorcontractGoClient,
// 				reporter:                   tt.fields.reporter,
// 				accountCli:                 tt.fields.accountCli,
// 				channelbackgroundCli:       tt.fields.channelbackgroundCli,
// 				channelLotteryCli:          tt.fields.channelLotteryCli,
// 				channelLiveFansCli:         tt.fields.channelLiveFansCli,
// 				channelVotePkGoCli:         tt.fields.channelVotePkGoCli,
// 				publicCli:                  tt.fields.publicCli,
// 				apiCli:                     tt.fields.apiCli,
// 				userOlCli:                  tt.fields.userOlCli,
// 				channelCli:                 tt.fields.channelCli,
// 				anchorCheckCli:             tt.fields.anchorCheckCli,
// 			}
// 			if err := m.AwardUser(tt.args.info, tt.args.level2AwardConfig); (err != nil) != tt.wantErr {
// 				t.Errorf("AnchorLevelMgr.AwardUser() error = %v, wantErr %v", err, tt.wantErr)
// 			}
// 		})
// 	}
// }

// // go test -timeout 30s -run ^TestAnchorLevelMgr_HandleSetLotteryOpen$ golang.52tt.com/services/anchor-level/manager -v -count=1
// func TestAnchorLevelMgr_HandleSetLotteryOpen(t *testing.T) {

// 	ctl := gomock.NewController(t)
// 	defer ctl.Finish()

// 	fakeNowTs := int64(1675180800)
// 	monkey.Patch(time.Now, func() time.Time {
// 		return time.Unix(fakeNowTs, 0)
// 	})

// 	mockchannelLotteryCli := channelLotterymock.NewMockIClient(ctl)
// 	mockchannelCli := channelmock.NewMockIClient(ctl)
// 	mockReport := mocks.NewMockIFeishuReporterV2(ctl)

// 	info := &store.LiveAnchorMonthTaskAwardLog{
// 		AnchorUid: 2404178,
// 		ChannelId: 2651478,
// 	}
// 	channelLotteryDays := uint32(30)
// 	DisplayId := uint32(666)
// 	lotteryOpenResp := &channellotteryPB.SetLotteryOpenListResp{TimeConflictId: []uint32{}}
// 	lotteryOpenResp2 := &channellotteryPB.SetLotteryOpenListResp{TimeConflictId: []uint32{DisplayId}}

// 	openList2 := &channellotteryPB.SearchLotteryOpensResp{
// 		DataList: []*channellotteryPB.LotteryOpen{
// 			{
// 				DisplayId: DisplayId,
// 				BeginTime: fakeNowTs - 60,
// 				EndTime:   fakeNowTs + 120,
// 			},
// 		},
// 	}

// 	gomock.InOrder(
// 		// 正常发放
// 		mockchannelCli.EXPECT().GetChannelSimpleInfo(gomock.Any(), gomock.Any(), gomock.Any()).Return(&channelsvr.ChannelSimpleInfo{DisplayId: &DisplayId}, nil),
// 		mockchannelLotteryCli.EXPECT().SetLotteryOpenList(gomock.Any(), gomock.Any()).Return(lotteryOpenResp, nil),

// 		// 冲突 2.叠加成功
// 		mockchannelCli.EXPECT().GetChannelSimpleInfo(gomock.Any(), gomock.Any(), gomock.Any()).Return(&channelsvr.ChannelSimpleInfo{DisplayId: &DisplayId}, nil),
// 		mockchannelLotteryCli.EXPECT().SetLotteryOpenList(gomock.Any(), gomock.Any()).Return(lotteryOpenResp2, nil),
// 		mockchannelLotteryCli.EXPECT().SearchLotteryOpens(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(openList2, nil),
// 		mockchannelLotteryCli.EXPECT().SetLotteryOpenList(gomock.Any(), gomock.Any()).Return(lotteryOpenResp, nil),
// 	)

// 	type fields struct {
// 		store                      store.IStore
// 		dyconfig                   conf.ISDyConfigHandler
// 		LocalCache                 *LocalCache
// 		channelLiveMgr             channellivemgr.IClient
// 		channelLiveStats           channellivestats.IClient
// 		channelRecommendSvr        channel_recommend_svr.IClient
// 		entertainmentRecommendBack entertainmentrecommendback.IClient
// 		anchorcontractGoClient     anchorcontract_go.IClient
// 		reporter                   report.IFeishuReporterV2
// 		accountCli                 account.IClient
// 		channelbackgroundCli       channelbackground.IClient
// 		channelLotteryCli          channellottery.IClient
// 		channelLiveFansCli         channellivefans.IClient
// 		channelVotePkGoCli         channelvotepkgo.IClient
// 		publicCli                  public.IClient
// 		apiCli                     apicenter.IClient
// 		userOlCli                  userol.IClient
// 		channelCli                 channel.IClient
// 		anchorCheckCli             anchorcheck.IClient
// 	}
// 	type args struct {
// 		ctx                context.Context
// 		info               *store.LiveAnchorMonthTaskAwardLog
// 		channelLotteryDays uint32
// 	}
// 	tests := []struct {
// 		name    string
// 		fields  fields
// 		args    args
// 		wantErr bool
// 	}{
// 		{
// 			fields: fields{
// 				channelCli:        mockchannelCli,
// 				channelLotteryCli: mockchannelLotteryCli,
// 			},
// 			args: args{
// 				ctx:                context.Background(),
// 				info:               info,
// 				channelLotteryDays: channelLotteryDays,
// 			},
// 		},

// 		{
// 			fields: fields{
// 				channelCli:        mockchannelCli,
// 				channelLotteryCli: mockchannelLotteryCli,
// 				reporter:          mockReport,
// 			},
// 			args: args{
// 				ctx:                context.Background(),
// 				info:               info,
// 				channelLotteryDays: channelLotteryDays,
// 			},
// 		},
// 	}
// 	for _, tt := range tests {
// 		t.Run(tt.name, func(t *testing.T) {
// 			m := &AnchorLevelMgr{
// 				store:                      tt.fields.store,
// 				dyconfig:                   tt.fields.dyconfig,
// 				LocalCache:                 tt.fields.LocalCache,
// 				channelLiveMgr:             tt.fields.channelLiveMgr,
// 				channelLiveStats:           tt.fields.channelLiveStats,
// 				channelRecommendSvr:        tt.fields.channelRecommendSvr,
// 				entertainmentRecommendBack: tt.fields.entertainmentRecommendBack,
// 				anchorcontractGoClient:     tt.fields.anchorcontractGoClient,
// 				reporter:                   tt.fields.reporter,
// 				accountCli:                 tt.fields.accountCli,
// 				channelbackgroundCli:       tt.fields.channelbackgroundCli,
// 				channelLotteryCli:          tt.fields.channelLotteryCli,
// 				channelLiveFansCli:         tt.fields.channelLiveFansCli,
// 				channelVotePkGoCli:         tt.fields.channelVotePkGoCli,
// 				publicCli:                  tt.fields.publicCli,
// 				apiCli:                     tt.fields.apiCli,
// 				userOlCli:                  tt.fields.userOlCli,
// 				channelCli:                 tt.fields.channelCli,
// 				anchorCheckCli:             tt.fields.anchorCheckCli,
// 			}
// 			if err := m.HandleSetLotteryOpen(tt.args.ctx, tt.args.info, tt.args.channelLotteryDays); (err != nil) != tt.wantErr {
// 				t.Errorf("AnchorLevelMgr.HandleSetLotteryOpen() error = %v, wantErr %v", err, tt.wantErr)
// 			}
// 		})
// 	}
// }

// // go test -timeout 30s -run ^TestAnchorLevelMgr_HandleGrantFlowCard$ golang.52tt.com/services/anchor-level/manager -v -count=1
// func TestAnchorLevelMgr_HandleGrantFlowCard(t *testing.T) {
// 	ctl := gomock.NewController(t)
// 	defer ctl.Finish()

// 	mockchannelRecommendSvr := recommendmock.NewMockIClient(ctl)

// 	serr := protocol.NewServerError(-2)

// 	gomock.InOrder(
// 		mockchannelRecommendSvr.EXPECT().BatGrantFlowCard(gomock.Any(), gomock.Any()).Return(nil, serr),

// 		//mockchannelRecommendSvr.EXPECT().GrantFlowCard(gomock.Any(), gomock.Any()).Return(nil, serr),

// 		//mockchannelRecommendSvr.EXPECT().GrantFlowCard(gomock.Any(), gomock.Any()).Return(nil, nil),
// 		//mockchannelRecommendSvr.EXPECT().GrantFlowCard(gomock.Any(), gomock.Any()).Return(nil, serr),
// 	)

// 	type fields struct {
// 		store                      store.IStore
// 		dyconfig                   conf.ISDyConfigHandler
// 		LocalCache                 *LocalCache
// 		channelLiveMgr             channellivemgr.IClient
// 		channelLiveStats           channellivestats.IClient
// 		channelRecommendSvr        channel_recommend_svr.IClient
// 		entertainmentRecommendBack entertainmentrecommendback.IClient
// 		anchorcontractGoClient     anchorcontract_go.IClient
// 		reporter                   report.IFeishuReporterV2
// 		accountCli                 account.IClient
// 		channelbackgroundCli       channelbackground.IClient
// 		channelLotteryCli          channellottery.IClient
// 		channelLiveFansCli         channellivefans.IClient
// 		channelVotePkGoCli         channelvotepkgo.IClient
// 		publicCli                  public.IClient
// 		apiCli                     apicenter.IClient
// 		userOlCli                  userol.IClient
// 		channelCli                 channel.IClient
// 		anchorCheckCli             anchorcheck.IClient
// 	}
// 	type args struct {
// 		ctx       context.Context
// 		info      *store.LiveAnchorMonthTaskAwardLog
// 		awardInfo *store.LiveAnchorMonthAwardConfig
// 	}
// 	tests := []struct {
// 		name    string
// 		fields  fields
// 		args    args
// 		wantErr bool
// 	}{
// 		{
// 			fields: fields{channelRecommendSvr: mockchannelRecommendSvr},
// 			args: args{ctx: context.Background(),
// 				info:      &store.LiveAnchorMonthTaskAwardLog{},
// 				awardInfo: &store.LiveAnchorMonthAwardConfig{},
// 			},
// 			wantErr: true,
// 		},
// 	}
// 	for _, tt := range tests {
// 		t.Run(tt.name, func(t *testing.T) {
// 			m := &AnchorLevelMgr{
// 				store:                      tt.fields.store,
// 				dyconfig:                   tt.fields.dyconfig,
// 				LocalCache:                 tt.fields.LocalCache,
// 				channelLiveMgr:             tt.fields.channelLiveMgr,
// 				channelLiveStats:           tt.fields.channelLiveStats,
// 				channelRecommendSvr:        tt.fields.channelRecommendSvr,
// 				entertainmentRecommendBack: tt.fields.entertainmentRecommendBack,
// 				anchorcontractGoClient:     tt.fields.anchorcontractGoClient,
// 				reporter:                   tt.fields.reporter,
// 				accountCli:                 tt.fields.accountCli,
// 				channelbackgroundCli:       tt.fields.channelbackgroundCli,
// 				channelLotteryCli:          tt.fields.channelLotteryCli,
// 				channelLiveFansCli:         tt.fields.channelLiveFansCli,
// 				channelVotePkGoCli:         tt.fields.channelVotePkGoCli,
// 				publicCli:                  tt.fields.publicCli,
// 				apiCli:                     tt.fields.apiCli,
// 				userOlCli:                  tt.fields.userOlCli,
// 				channelCli:                 tt.fields.channelCli,
// 				anchorCheckCli:             tt.fields.anchorCheckCli,
// 			}
// 			if err := m.HandleGrantFlowCard(tt.args.ctx, tt.args.info, tt.args.awardInfo); (err != nil) != tt.wantErr {
// 				t.Errorf("AnchorLevelMgr.HandleGrantFlowCard() error = %v, wantErr %v", err, tt.wantErr)
// 			}
// 		})
// 	}
// }

// func TestGetNoPassUids(t *testing.T) {
// 	type args struct {
// 		all  []uint32
// 		pass []uint32
// 	}
// 	tests := []struct {
// 		name string
// 		args args
// 		want []uint32
// 	}{
// 		// TODO: Add test cases.
// 	}
// 	for _, tt := range tests {
// 		t.Run(tt.name, func(t *testing.T) {
// 			if got := GetNoPassUids(tt.args.all, tt.args.pass); !reflect.DeepEqual(got, tt.want) {
// 				t.Errorf("GetNoPassUids() = %v, want %v", got, tt.want)
// 			}
// 		})
// 	}
// }

// /*
// // go test -timeout 30s -run ^Test_remain$ golang.52tt.com/services/anchor-level/manager -v -count=1
// func Test_remain(t *testing.T) {
// 	t.Log(remain(1, 2))
// 	t.Log(remain(2, 1))
// }
// */
