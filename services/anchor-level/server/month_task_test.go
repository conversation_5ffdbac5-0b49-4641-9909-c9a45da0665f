package server

import (
	"context"
	"reflect"
	"testing"

	"github.com/golang/mock/gomock"
	"golang.52tt.com/pkg/log"
	pb "golang.52tt.com/protocol/services/anchor-level"
	"golang.52tt.com/services/anchor-level/manager"
	"golang.52tt.com/services/anchor-level/mocks"
)

func init() {
	log.SetLevel(log.DebugLevel)
}

func initMgr(t *testing.T) *mocks.MockIAnchorLevelMgr {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockMgr := mocks.NewMockIAnchorLevelMgr(ctl)
	return mockMgr
}

// go test -timeout 30s -run ^TestAnchorLevelServer_GetLiveAnchorTaskEntry$ golang.52tt.com/services/anchor-level/server -v -count=1
func TestAnchorLevelServer_GetLiveAnchorTaskEntry(t *testing.T) {

	mgr := initMgr(t)

	out := &pb.GetLiveAnchorTaskEntryResp{}
	gomock.InOrder(
		mgr.EXPECT().GetLiveAnchorTaskEntry(gomock.Any(), gomock.Any()).Return(out, nil),
	)

	type fields struct {
		mgr manager.IAnchorLevelMgr
	}
	type args struct {
		ctx context.Context
		in  *pb.GetLiveAnchorTaskEntryReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantOut *pb.GetLiveAnchorTaskEntryResp
		wantErr bool
	}{
		{
			fields:  fields{mgr: mgr},
			args:    args{ctx: context.Background(), in: &pb.GetLiveAnchorTaskEntryReq{}},
			wantOut: out,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &AnchorLevelServer{
				mgr: tt.fields.mgr,
			}
			gotOut, err := s.GetLiveAnchorTaskEntry(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("AnchorLevelServer.GetLiveAnchorTaskEntry() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(gotOut, tt.wantOut) {
				t.Errorf("AnchorLevelServer.GetLiveAnchorTaskEntry() = %v, want %v", gotOut, tt.wantOut)
			}
		})
	}
}

// go test -timeout 30s -run ^TestAnchorLevelServer_GetLiveAnchorTaskEntry$ golang.52tt.com/services/anchor-level/server -v -count=1
func TestAnchorLevelServer_GetLiveAnchorLevel(t *testing.T) {

	mgr := initMgr(t)

	out := &pb.GetLiveAnchorLevelResp{}
	gomock.InOrder(
		mgr.EXPECT().GetLiveAnchorLevel(gomock.Any(), gomock.Any()).Return(out, nil),
	)

	type fields struct {
		mgr manager.IAnchorLevelMgr
	}
	type args struct {
		ctx context.Context
		in  *pb.GetLiveAnchorLevelReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantOut *pb.GetLiveAnchorLevelResp
		wantErr bool
	}{
		{
			fields:  fields{mgr: mgr},
			args:    args{ctx: context.Background(), in: &pb.GetLiveAnchorLevelReq{}},
			wantOut: out,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &AnchorLevelServer{
				mgr: tt.fields.mgr,
			}
			gotOut, err := s.GetLiveAnchorLevel(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("AnchorLevelServer.GetLiveAnchorLevel() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(gotOut, tt.wantOut) {
				t.Errorf("AnchorLevelServer.GetLiveAnchorLevel() = %v, want %v", gotOut, tt.wantOut)
			}
		})
	}
}

// go test -timeout 30s -run ^TestAnchorLevelServer_GetAnchorLevelMonthTask$ golang.52tt.com/services/anchor-level/server -v -count=1
func TestAnchorLevelServer_GetAnchorLevelMonthTask(t *testing.T) {

	mgr := initMgr(t)

	out := &pb.GetAnchorLevelMonthTaskResp{}
	gomock.InOrder(
		mgr.EXPECT().GetAnchorLevelMonthTask(gomock.Any(), gomock.Any()).Return(out, nil),
	)

	type fields struct {
		mgr manager.IAnchorLevelMgr
	}
	type args struct {
		ctx context.Context
		in  *pb.UidReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantOut *pb.GetAnchorLevelMonthTaskResp
		wantErr bool
	}{
		{
			fields:  fields{mgr: mgr},
			args:    args{ctx: context.Background(), in: &pb.UidReq{}},
			wantOut: out,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &AnchorLevelServer{
				mgr: tt.fields.mgr,
			}
			gotOut, err := s.GetAnchorLevelMonthTask(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("AnchorLevelServer.GetAnchorLevelMonthTask() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(gotOut, tt.wantOut) {
				t.Errorf("AnchorLevelServer.GetAnchorLevelMonthTask() = %v, want %v", gotOut, tt.wantOut)
			}
		})
	}
}

// go test -timeout 30s -run ^TestAnchorLevelServer_GetLiveAnchorLevelByUid$ golang.52tt.com/services/anchor-level/server -v -count=1
func TestAnchorLevelServer_GetLiveAnchorLevelByUid(t *testing.T) {

	mgr := initMgr(t)

	out := &pb.GetLiveAnchorLevelByUidResp{}
	gomock.InOrder(
		mgr.EXPECT().GetLiveAnchorLevelByUid(gomock.Any(), gomock.Any()).Return(out, nil),
	)

	type fields struct {
		mgr manager.IAnchorLevelMgr
	}
	type args struct {
		ctx context.Context
		in  *pb.GetLiveAnchorLevelByUidReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantOut *pb.GetLiveAnchorLevelByUidResp
		wantErr bool
	}{
		{
			fields:  fields{mgr: mgr},
			args:    args{ctx: context.Background(), in: &pb.GetLiveAnchorLevelByUidReq{}},
			wantOut: out,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &AnchorLevelServer{
				mgr: tt.fields.mgr,
			}
			gotOut, err := s.GetLiveAnchorLevelByUid(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("AnchorLevelServer.GetLiveAnchorLevelByUid() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(gotOut, tt.wantOut) {
				t.Errorf("AnchorLevelServer.GetLiveAnchorLevelByUid() = %v, want %v", gotOut, tt.wantOut)
			}
		})
	}
}
