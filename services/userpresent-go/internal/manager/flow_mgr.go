package manager

import (
	"context"
	"golang.52tt.com/pkg/log"
	userpresent "golang.52tt.com/protocol/services/userpresent-go"
	"golang.52tt.com/services/userpresent-go/internal/cache"
)

func (m *UserPresentGoMgr) AddPresentFlowConfig(ctx context.Context, req *userpresent.AddPresentFlowConfigReq) (configResp *userpresent.AddPresentFlowConfigResp, err error) {
	configResp = &userpresent.AddPresentFlowConfigResp{}

	_, err = m.store.AddPresentFlowConfig(ctx, &userpresent.StPresentFlowConfig{
		Url:  req.GetFlowUrl(),
		Md5:  req.GetFlowMd5(),
		Desc: req.GetFlowDesc(),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "AddPresentFlowConfig err ,req %v , err %v", req, err)
		return configResp, err
	}

	_ = m.cache.NotifyPresentConfigUpdate(ctx, cache.FlowConfigUpdate)

	log.DebugWithCtx(ctx, "AddPresentFlowConfig success ,req %v , resp %v", req, configResp)
	return configResp, err
}

func (m *UserPresentGoMgr) DelPresentFlowConfig(ctx context.Context, req *userpresent.DelPresentFlowConfigReq) (configResp *userpresent.DelPresentFlowConfigResp, err error) {
	configResp = &userpresent.DelPresentFlowConfigResp{}

	err = m.store.DeletePresentFlowConfig(ctx, req.GetFlowId())
	if err != nil {
		log.ErrorWithCtx(ctx, "DelPresentFlowConfig err ,req %v , err %v", req, err)
		return configResp, err
	}

	_ = m.cache.NotifyPresentConfigUpdate(ctx, cache.FlowConfigUpdate)

	log.DebugWithCtx(ctx, "DelPresentFlowConfig success ,req %v , resp %v", req, configResp)
	return configResp, err
}

func (m *UserPresentGoMgr) UpdatePresentFlowConfig(ctx context.Context, req *userpresent.UpdatePresentFlowConfigReq) (configResp *userpresent.UpdatePresentFlowConfigResp, err error) {
	configResp = &userpresent.UpdatePresentFlowConfigResp{}

	_, err = m.store.UpdatePresentFlowConfig(ctx, &userpresent.StPresentFlowConfig{
		FlowId: req.GetFlowId(),
		Url:    req.GetFlowUrl(),
		Md5:    req.GetFlowMd5(),
		Desc:   req.GetFlowDesc(),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "DelPresentFlowConfig err ,req %v , err %v", req, err)
		return configResp, err
	}

	_ = m.cache.NotifyPresentConfigUpdate(ctx, cache.FlowConfigUpdate)

	log.DebugWithCtx(ctx, "DelPresentFlowConfig success ,req %v , resp %v", req, configResp)
	return configResp, err
}

func (m *UserPresentGoMgr) GetPresentFlowConfigById(ctx context.Context, req *userpresent.GetPresentFlowConfigByIdReq) (configResp *userpresent.GetPresentFlowConfigByIdResp, err error) {
	configResp = &userpresent.GetPresentFlowConfigByIdResp{}
	configResp.FlowConfig = m.presentCache.GetFlowConfigById(req.GetFlowId())

	log.DebugWithCtx(ctx, "GetPresentFlowConfigById success ,req %v , resp %v", req, configResp)
	return configResp, err
}

func (m *UserPresentGoMgr) GetPresentFlowConfigList(ctx context.Context, req *userpresent.GetPresentFlowConfigListReq) (configResp *userpresent.GetPresentFlowConfigListResp, err error) {
	configResp = &userpresent.GetPresentFlowConfigListResp{}
	configResp.FlowList = m.presentCache.GetFlowConfigList()

	log.DebugWithCtx(ctx, "GetPresentFlowConfigList success ,req %v , resp %v", req, configResp)
	return configResp, err
}

func (m *UserPresentGoMgr) GetPresentFlowConfigUpdateTime(ctx context.Context, req *userpresent.GetPresentFlowConfigUpdateTimeReq) (configResp *userpresent.GetPresentFlowConfigUpdateTimeResp, err error) {
	configResp = &userpresent.GetPresentFlowConfigUpdateTimeResp{}
	configResp.UpdateTime = m.presentCache.GetFlowConfigUpdateTime()

	log.DebugWithCtx(ctx, "GetPresentFlowConfigUpdateTime success ,req %v , resp %v", req, configResp)
	return configResp, err
}
