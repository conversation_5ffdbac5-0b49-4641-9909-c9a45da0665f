package store

import (
	"context"
	"database/sql"
	"errors"
	errors2 "gitlab.ttyuyin.com/avengers/tyr/core/service/errors"
	"gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/mysql"
	"gitlab.ttyuyin.com/tt-infra/tyr/log"
	"golang.52tt.com/protocol/common/status"
	userpresent "golang.52tt.com/protocol/services/userpresent-go"
)

func (s *Store) AddPresentFlowConfig(ctx context.Context, config *userpresent.StPresentFlowConfig) (configResp *userpresent.StPresentFlowConfig, err error) {
	configResp = &userpresent.StPresentFlowConfig{}

	addSql := "INSERT INTO user_present_flow_config(flow_url, flow_md5, flow_desc, update_time) VALUES(?, ?, ?, CURRENT_TIMESTAMP())"
	cb, err := s.db.ExecContext(ctx, addSql, config.GetUrl(), config.GetMd5(), config.GetDesc())
	if err != nil {
		log.ErrorWithCtx(ctx, "AddPresentFlowConfig err ,req %v , err %v", config, err)
		return configResp, errors2.NewExactServerError(nil, status.ErrRevenueSvrErr)
	}

	lastId, _ := cb.LastInsertId()
	configResp, err = s.GetPresentFlowConfigById(ctx, uint32(lastId))
	if err != nil {
		log.ErrorWithCtx(ctx, "AddPresentFlowConfig GetPresentFlowConfigById err ,id %d, err %v", lastId, err)
		return
	}

	return
}

func (s *Store) GetPresentFlowConfigById(ctx context.Context, id uint32) (config *userpresent.StPresentFlowConfig, err error) {
	config = &userpresent.StPresentFlowConfig{}
	tmp := &UserPresentFlowConfig{}

	getSql := "SELECT flow_id , flow_url , flow_md5 , flow_desc , UNIX_TIMESTAMP(update_time) as update_time,UNIX_TIMESTAMP(create_time) as create_time FROM user_present_flow_config WHERE flow_id = ?"
	err = s.db.GetContext(ctx, tmp, getSql, id)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			log.ErrorWithCtx(ctx, "GetPresentFlowConfigById no rows ,id %d", id)
			return config, errors2.NewExactServerError(nil, status.ErrUserPresentFlowConfigNotExist)
		}

		log.ErrorWithCtx(ctx, "GetPresentFlowConfigById err , id %d, err %v", id, err)
		return config, err
	}

	config = tranPresentFlowConfigToPb(tmp)

	return config, nil
}

func (s *Store) GetPresentFlowConfigList(ctx context.Context) (list []*userpresent.StPresentFlowConfig, err error) {
	list = make([]*userpresent.StPresentFlowConfig, 0)
	tmp := make([]*UserPresentFlowConfig, 0)

	getSql := "SELECT flow_id , flow_url, flow_md5  , flow_desc , UNIX_TIMESTAMP(update_time) as update_time,UNIX_TIMESTAMP(create_time) as create_time FROM user_present_flow_config"
	err = s.db.SelectContext(ctx, &tmp, getSql)
	if mysql.IsNoRowsError(err) {
		return list, nil
	}
	if err != nil {
		log.ErrorWithCtx(ctx, "GetPresentFlowConfigList err , err %v", err)
		return list, err
	}

	for _, v := range tmp {
		list = append(list, tranPresentFlowConfigToPb(v))
	}

	return list, nil
}

func (s *Store) UpdatePresentFlowConfig(ctx context.Context, config *userpresent.StPresentFlowConfig) (configResp *userpresent.StPresentFlowConfig, err error) {
	configResp = &userpresent.StPresentFlowConfig{}

	updateSql := "UPDATE user_present_flow_config SET flow_url = ?, flow_md5 = ?, flow_desc = ?, update_time = CURRENT_TIMESTAMP() WHERE flow_id = ?"
	_, err = s.db.ExecContext(ctx, updateSql, config.GetUrl(), config.GetMd5(), config.GetDesc(), config.GetFlowId())
	if err != nil {
		log.ErrorWithCtx(ctx, "UpdatePresentFlowConfig err ,req %v , err %v", config, err)
		return configResp, errors2.NewExactServerError(nil, status.ErrRevenueSvrErr)
	}

	configResp, err = s.GetPresentFlowConfigById(ctx, config.FlowId)
	if err != nil {
		log.ErrorWithCtx(ctx, "UpdatePresentFlowConfig GetPresentFlowConfigById err ,id %d, err %v", config.FlowId, err)
		return
	}
	return
}

func (s *Store) DeletePresentFlowConfig(ctx context.Context, id uint32) (err error) {
	deleteSql := "DELETE FROM user_present_flow_config WHERE flow_id = ?"
	_, err = s.db.ExecContext(ctx, deleteSql, id)
	if err != nil {
		log.ErrorWithCtx(ctx, "DeletePresentFlowConfig err ,id %d , err %v", id, err)
		return errors2.NewExactServerError(nil, status.ErrRevenueSvrErr)
	}

	return nil
}

func (s *Store) GetPresentFlowConfigUpdateTime(ctx context.Context) (time uint32, err error) {
	getSql := "SELECT UNIX_TIMESTAMP(update_time) as update_time FROM user_present_flow_config ORDER BY update_time DESC LIMIT 1"
	err = s.db.GetContext(ctx, &time, getSql)
	if mysql.IsNoRowsError(err) {
		return 0, nil
	}
	if err != nil {
		log.ErrorWithCtx(ctx, "GetPresentFlowConfigUpdateTime err ,err %v", err)
		return time, err
	}

	return time, nil
}

func tranPresentFlowConfigToPb(config *UserPresentFlowConfig) *userpresent.StPresentFlowConfig {
	return &userpresent.StPresentFlowConfig{
		FlowId:     config.FlowId,
		Url:        config.FlowUrl,
		Md5:        config.FlowMd5,
		Desc:       config.FlowDesc,
		UpdateTime: config.UpdateTime,
		CreateTime: config.CreateTime,
	}
}
