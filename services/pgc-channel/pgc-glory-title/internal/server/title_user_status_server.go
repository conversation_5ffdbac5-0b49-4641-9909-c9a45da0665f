package server

import (
	"context"
	"fmt"
	"golang.52tt.com/pkg/log"
	pb "golang.52tt.com/protocol/services/pgc-glory-title"
	"golang.52tt.com/services/pgc-channel/pgc-glory-title/internal/data"
	"time"
)

func getProcessMsg(processVal, weekPrice, monthPrice, titleType uint32, nowTs, expireTs int64) string {
	switch pb.ETitleType(titleType) {
	case pb.ETitleType_E_TITLE_TYPE_UNSPECIFIED:
		if processVal == 0 {
			return fmt.Sprintf(data.ProcessMsgWeek_1, fmt.Sprintf("%d", weekPrice))
		} else if weekPrice >= processVal {
			return fmt.Sprintf(data.ProcessMsgWeek_2, fmt.Sprintf("%d", weekPrice-processVal))
		} else {
			return fmt.Sprintf(data.ProcessMsgWeek_2, "0")
		}
	case pb.ETitleType_E_TITLE_TYPE_WEEK:
		var dayCnt uint32 = 1
		if expireTs >= nowTs {
			dayCnt = uint32(expireTs-nowTs)/(data.DaySeconds) + 1
		}

		if monthPrice >= processVal {
			return fmt.Sprintf(data.ProcessMsgMonth_1, dayCnt, fmt.Sprintf("%d", monthPrice-processVal))
		} else {
			return fmt.Sprintf(data.ProcessMsgMonth_1, dayCnt, "0")
		}
	case pb.ETitleType_E_TITLE_TYPE_MONTH:
		return ""
	}
	return ""
}

func getNewProcessMsg(processVal, dayPrice, weekPrice, monthPrice, titleType uint32, nowTs, expireTs int64) string {
	switch pb.ETitleType(titleType) {
	case pb.ETitleType_E_TITLE_TYPE_UNSPECIFIED:
		if processVal == 0 {
			return fmt.Sprintf(data.ProcessMsgDay_1, fmt.Sprintf("%d", dayPrice))
		} else if dayPrice >= processVal {
			return fmt.Sprintf(data.ProcessMsgDay_2, fmt.Sprintf("%d", dayPrice-processVal))
		} else {
			return fmt.Sprintf(data.ProcessMsgDay_2, "0")
		}
	case pb.ETitleType_E_TITLE_TYPE_DAY:
		var hourCnt uint32 = 1
		if expireTs >= nowTs {
			hourCnt = uint32(expireTs-nowTs)/(data.HourSeconds) + 1
		}

		if weekPrice >= processVal {
			return fmt.Sprintf(data.ProcessMsgWeek_3, hourCnt, fmt.Sprintf("%d", weekPrice-processVal))
		} else {
			return fmt.Sprintf(data.ProcessMsgWeek_3, hourCnt, "0")
		}
	case pb.ETitleType_E_TITLE_TYPE_WEEK:
		var dayCnt uint32 = 1
		if expireTs >= nowTs {
			dayCnt = uint32(expireTs-nowTs)/(data.DaySeconds) + 1
		}

		if monthPrice >= processVal {
			return fmt.Sprintf(data.ProcessMsgMonth_1, dayCnt, fmt.Sprintf("%d", monthPrice-processVal))
		} else {
			return fmt.Sprintf(data.ProcessMsgMonth_1, dayCnt, "0")
		}
	case pb.ETitleType_E_TITLE_TYPE_MONTH:
		return ""
	}
	return ""
}

func (s *Server) GetPgcGloryTitleData(ctx context.Context, req *pb.GetPgcGloryTitleDataReq) (*pb.GetPgcGloryTitleDataResp, error) {
	resp := &pb.GetPgcGloryTitleDataResp{}

	_, weekPrice, monthPrice, dayPrice, isDayTitleBegin := s.mgr.CheckUserHasTitledEntry(ctx, req.GetToUid(), req.GetChannelId())

	titleUserStatus, err := s.mgr.GetTitleUserStatus(ctx, req.GetFromUid(), req.GetToUid(), req.GetChannelId())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetPgcGloryTitleData GetTitleUserStatus req:%v err=%v", req, err)
		return resp, err
	}

	nowTs := time.Now().Unix()
	isNeedProcessVal := true
	if titleUserStatus.ExpireTime != 0 && titleUserStatus.ExpireTime < nowTs {
		// 冠名身份过期，不需要查询进度
		isNeedProcessVal = false
		titleUserStatus.TitleType = uint32(pb.ETitleType_E_TITLE_TYPE_UNSPECIFIED)
	}

	if titleUserStatus.TitleType == uint32(pb.ETitleType_E_TITLE_TYPE_DAY) && (!req.GetIsDayTitleVer() || !isDayTitleBegin) {
		titleUserStatus.TitleType = uint32(pb.ETitleType_E_TITLE_TYPE_UNSPECIFIED)
	}

	var processVal uint64
	if isNeedProcessVal {
		processVal, err = s.mgr.GetTitleUserProcess(ctx, titleUserStatus)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetPgcGloryTitleData GetTitleUserProcess req:%v err=%v", req, err)
			return resp, err
		}
	}

	_, titleCnt, _, err := s.mgr.GetAnchorInvertorBaseInfo(ctx, req.GetChannelId(), req.GetToUid())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetPgcGloryTitleData GetAnchorInvertorBaseInfo req:%v err=%v", req, err)
		return resp, err
	}

	mapUid2Switch, err := s.mgr.GetTitleUserPrivilegeSwitch(ctx, req.GetChannelId(), req.GetToUid(), []uint32{req.GetFromUid()})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetPgcGloryTitleData GetTitleUserPrivilegeSwitch req:%v err=%v", req, err)
		return resp, err
	}

	mapType2Switch, err := s.mgr.GetUserPrivilegeGlobalSwitch(ctx, req.GetFromUid(), []uint32{uint32(pb.PrivilegeType_PRIVILEGE_TYPE_HEADWEAR)})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetPgcGloryTitleData GetUserPrivilegeGlobalSwitch req:%v err=%v", req, err)
		return resp, err
	}

	privilegeSwitch := mapUid2Switch[req.GetFromUid()]
	if titleUserStatus.TitleType == uint32(pb.ETitleType_E_TITLE_TYPE_UNSPECIFIED) {
		privilegeSwitch = true
	}

	resp.TitledCnt = titleCnt
	resp.PrivilegeSwitch = privilegeSwitch
	resp.WeekPrice = weekPrice
	resp.MonthPrice = monthPrice
	resp.CurrentPrice = uint32(processVal)
	if req.GetIsDayTitleVer() && isDayTitleBegin {
		resp.ProcessMsg = getNewProcessMsg(uint32(processVal), dayPrice, weekPrice, monthPrice, titleUserStatus.TitleType, nowTs, titleUserStatus.ExpireTime)
		resp.DescMsg = s.dyConf.GetTitleNewDescMsg(titleUserStatus.TitleType)
		resp.DayPrice = dayPrice
	} else {
		resp.ProcessMsg = getProcessMsg(uint32(processVal), weekPrice, monthPrice, titleUserStatus.TitleType, nowTs, titleUserStatus.ExpireTime)
		resp.DescMsg = s.dyConf.GetTitleDescMsg(titleUserStatus.TitleType)
	}

	resp.TitleType = titleUserStatus.TitleType
	resp.ExpireTime = titleUserStatus.ExpireTime
	resp.MapTypePrivilege = s.mgr.GetTitlePrivilegeConfList(ctx, req.GetIsDayTitleVer(), isDayTitleBegin)
	resp.MapTypeSwitch = mapType2Switch
	log.DebugWithCtx(ctx, "GetPgcGloryTitleData req:%v resp:%v", req, resp)
	return resp, nil
}

func (s *Server) GetUserTitledInfo(ctx context.Context, req *pb.GetUserTitledInfoReq) (*pb.GetUserTitledInfoResp, error) {
	resp := &pb.GetUserTitledInfoResp{}

	hasEntry, _, _, _, _ := s.mgr.CheckUserHasTitledEntry(ctx, req.GetTargetUid(), req.GetChannelId())
	if !hasEntry {
		return resp, nil
	}

	topList, titleCnt, targetRank, err := s.mgr.GetAnchorInvertorBaseInfo(ctx, req.GetChannelId(), req.GetTargetUid())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetUserTitledInfo GetAnchorInvertorBaseInfo req:%v err=%v", req, err)
		return resp, err
	}

	resp.HasEntry = hasEntry
	if targetRank > 0 && targetRank <= 5 {
		resp.ChannelTitledRank = fmt.Sprintf(data.ChannelTitledRankMsg, targetRank)
	}

	resp.TitledCnt = titleCnt
	resp.TopTitledList = topList

	log.DebugWithCtx(ctx, "GetUserTitledInfo req:%v resp:%v", req, resp)
	return resp, nil
}

func (s *Server) GetChannelTitleInfo(ctx context.Context, req *pb.GetChannelTitleInfoReq) (*pb.GetChannelTitleInfoResp, error) {
	resp := &pb.GetChannelTitleInfoResp{}

	hasEntry, _, _ := s.mgr.CheckChannelHasTitledEntry(ctx, req.GetChannelId())

	resp.HasEntry = hasEntry
	log.DebugWithCtx(ctx, "GetChannelTitleInfo req:%v resp:%v", req, resp)
	return resp, nil
}

func (s *Server) BatGetUserChannelTitleInfo(ctx context.Context, req *pb.BatGetUserChannelTitleInfoReq) (*pb.BatGetUserChannelTitleInfoResp, error) {
	resp := &pb.BatGetUserChannelTitleInfoResp{
		MapUidInfo: make(map[uint32]*pb.UserChannelTitleInfo),
	}

	hasEntry, _, _ := s.mgr.CheckChannelHasTitledEntry(ctx, req.GetCid())

	for _, uid := range req.GetUidList() {
		resp.MapUidInfo[uid] = &pb.UserChannelTitleInfo{
			MapUidType: make(map[uint32]uint32),
		}

		if !hasEntry {
			continue
		}

		titleType, _, mapTitledUser2Type, err := s.mgr.GetUserChannelTitleTypeWithSwitch(ctx, uid, req.GetCid())
		if err != nil {
			log.ErrorWithCtx(ctx, "BatGetUserChannelTitleInfo GetTitleUserPrivilegeSwitch req:%v err=%v", req, err)
			continue
		}

		mapType2Switch := make(map[uint32]bool)
		if titleType != uint32(pb.ETitleType_E_TITLE_TYPE_UNSPECIFIED) {
			mapType2Switch, err = s.mgr.GetUserPrivilegeGlobalSwitch(ctx, uid, []uint32{uint32(pb.PrivilegeType_PRIVILEGE_TYPE_HEADWEAR)})
			if err != nil {
				log.ErrorWithCtx(ctx, "BatGetUserChannelTitleInfo GetUserPrivilegeGlobalSwitch req:%v err=%v", req, err)
				continue
			}
		}

		resp.MapUidInfo[uid].MapUidType = mapTitledUser2Type
		resp.MapUidInfo[uid].TitleType = titleType
		resp.MapUidInfo[uid].MapTypeSwitch = mapType2Switch
	}

	resp.MapTypeCert = s.dyConf.GetTitleCert()
	resp.HeadwearId = s.dyConf.GetTitleHeadWearId()
	effectUrl, effectMd5, followEffectUrl, followEffectMd5 := s.dyConf.GetEnterEffect()
	resp.EnterEffectUrl = effectUrl
	resp.EnterEffectMd5 = effectMd5
	resp.FollowEnterEffectUrl = followEffectUrl
	resp.FollowEnterEffectMd5 = followEffectMd5

	log.DebugWithCtx(ctx, "BatGetUserChannelTitleInfo req:%v resp:%v", req, resp)
	return resp, nil
}
