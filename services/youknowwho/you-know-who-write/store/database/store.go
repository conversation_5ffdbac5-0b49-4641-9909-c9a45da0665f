package database

import (
	"context"
	"errors"
	"fmt"
	"time"

	_ "github.com/go-sql-driver/mysql" // MySQL驱动。
	"golang.52tt.com/pkg/config"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/services/youknowwho/common/model"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
)

func NewMysql(cfg *config.MysqlConfig, testMode bool) (*Store, error) {
	var err error
	var mysqlDb *gorm.DB
	if mysqlDb, err = gorm.Open(mysql.New(mysql.Config{
		DSN:                       cfg.ConnectionString(),
		DefaultStringSize:         256,   // default size for string fields
		DisableDatetimePrecision:  true,  // disable datetime precision, which not supported before MySQL 5.6
		DontSupportRenameIndex:    true,  // drop & create when rename index, rename index not supported before MySQL 5.7, MariaDB
		DontSupportRenameColumn:   true,  // `change` when rename column, rename column not supported before MySQL 8, MariaDB
		SkipInitializeWithVersion: false, // auto configure based on currently MySQL version
	}), &gorm.Config{}); err != nil {
		return nil, err
	}

	if testMode {
		mysqlDb = mysqlDb.Debug()
	}

	return &Store{db: mysqlDb}, nil
}

type Store struct {
	db *gorm.DB
}

const (
	SetDelFlagParam = "del_flag = ?"
	SetUidFlagParam = "uid = ?"
)

// GetUKWPermissionInfo 获取指定神秘人的权限信息
// @uid 神秘人Uid
func (s *Store) GetUKWPermissionInfo(ctx context.Context, uid uint32) (*model.YouKnowWhoPermissionTable, error) {
	log.DebugWithCtx(ctx, "GetUKWPermissionInfo uid:[%+v]", uid)
	permission := &model.YouKnowWhoPermissionTable{}

	if err := s.db.WithContext(ctx).Model(&model.YouKnowWhoPermissionTable{}).
		Where(SetUidFlagParam, uid).
		Where(SetDelFlagParam, model.TABLE_DELETE_FLAG_NO_DELETE).
		First(permission).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return &model.YouKnowWhoPermissionTable{}, nil
		}
		log.ErrorWithCtx(ctx, "GetUKWPermissionInfo Get info failed, uid:[%+v], err:[%+v]", uid, err)
		return permission, err
	}

	log.DebugWithCtx(ctx, "GetUKWPermissionInfo permission:[%+v]", permission)
	return permission, nil
}

// UpdateUKWNicknameAndFakeUid 同时更新神秘人昵称和假uid映射关系，主要用于暴露身份之后的刷新操作
// @uid 用户的真实uid
// @newNickname 神秘人新昵称
// @newFakeUid 神秘人新的假uid映射关系
func (s *Store) UpdateUKWNicknameAndFakeUid(ctx context.Context, uid uint32, newNickname string, newFakeUid uint32) error {
	log.DebugWithCtx(ctx, "UpdateUKWNicknameAndFakeUid uid:[%+v], newNickname:[%+v], newFakeUid:[%+v]", uid, newNickname, newFakeUid)
	// 开启事务操作
	if err := s.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {

		// 更新神秘人昵称和假uid
		if err := tx.Model(&model.YouKnowWhoPermissionTable{}).
			Where(SetUidFlagParam, uid).
			Where(SetDelFlagParam, model.TABLE_DELETE_FLAG_NO_DELETE).
			Updates(map[string]interface{}{
				"fake_uid": newFakeUid,
				"nickname": newNickname,
			}).Error; err != nil {
			log.ErrorWithCtx(ctx, "UpdateUKWNicknameAndFakeUid update permission failed, err:[%+v]", err)
			return err
		}

		// 更新假uid映射表
		timeNow := time.Now()
		periodTime := time.Date(timeNow.Year(), timeNow.Month(), timeNow.Day(), timeNow.Hour()-3, 0, 0, 0, time.Local)
		if err := tx.Model(&model.UKWFakeUidMappingRelationsTable{}).Create(&model.UKWFakeUidMappingRelationsTable{
			Period:     time.Date(periodTime.Year(), periodTime.Month(), periodTime.Day(), 0, 0, 0, 0, time.Local),
			FakeUid:    newFakeUid,
			Uid:        uid,
			Nickname:   newNickname,
			UpdateTime: timeNow,
			DelFlag:    model.TABLE_DELETE_FLAG_NO_DELETE,
		}).Error; err != nil {
			log.ErrorWithCtx(ctx, "UpdateUKWNicknameAndFakeUid update FakeUidMappingRelations failed, err:[%+v]", err)
			return err
		}

		return nil
	}); err != nil {
		log.ErrorWithCtx(ctx, "UpdateUKWNicknameAndFakeUid Transaction err:[%+v]", err)
		return err
	}

	return nil
}

// GetAllUKWPermissionInfos 分页获取全部神秘人信息
// @pageSize 分页大小
// @pageNum 页数，页数从第一页开始
func (s *Store) GetAllUKWPermissionInfos(ctx context.Context, pageSize, pageNum uint32) ([]*model.YouKnowWhoPermissionTable, error) {
	log.DebugWithCtx(ctx, "GetAllUKWPermissionInfos pageSize:[%+v], pageNum:[%+v]", pageSize, pageNum)

	// 处理分页问题
	if pageNum < 1 {
		err := fmt.Errorf("GetAllUKWPermissionInfos pageNum is below than 1, pageNum:[%+v]", pageNum)
		log.ErrorWithCtx(ctx, "%+v", err)
		return nil, err
	}
	page := pageNum - 1
	offset := page * pageSize

	// 拉取数据
	permissionInfos := make([]*model.YouKnowWhoPermissionTable, 0)
	if err := s.db.WithContext(ctx).Model(&model.YouKnowWhoPermissionTable{}).
		Where(SetDelFlagParam, model.TABLE_DELETE_FLAG_NO_DELETE).
		Offset(int(offset)).Limit(int(pageSize)).Find(&permissionInfos).Error; err != nil {
		log.ErrorWithCtx(ctx, "GetAllUKWPermissionInfos Get info failed, pageSize:[%+v], pageNum:[%+v], err:[%+v]",
			pageSize, pageNum, err)
		return permissionInfos, err
	}

	log.DebugWithCtx(ctx, "GetAllUKWPermissionInfos permissionInfos:[%+v]", permissionInfos)
	return permissionInfos, nil
}

// GetMappingRelationsWithPeriod 查询指定时间段内的假uid映射关系
func (s *Store) GetMappingRelationsWithPeriod(ctx context.Context, startTime, endTime time.Time, limit, offset uint32) (
	[]*model.UKWFakeUidMappingRelationsTable, error) {

	log.DebugWithCtx(ctx, "GetMappingRelationsWithTime startTime:[%+v], endTime:[%+v], limit:[%+v], offset:[%+v]",
		startTime.String(), endTime.String(), limit, offset)

	// 拉取数据
	result := make([]*model.UKWFakeUidMappingRelationsTable, 0)
	if err := s.db.WithContext(ctx).Model(&model.UKWFakeUidMappingRelationsTable{}).
		Where("uid_period >= ?", startTime).
		Where("uid_period < ?", endTime).
		Where(SetDelFlagParam, model.TABLE_DELETE_FLAG_NO_DELETE).
		Offset(int(offset)).Limit(int(limit)).Find(&result).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			log.WarnWithCtx(ctx, "GetUKWUserFlow is null.")
			return result, nil
		}
		log.ErrorWithCtx(ctx, "GetUKWUserFlow Get info failed, startTime:[%+v], endTime:[%+v], , pageSize:[%+v], pageNum:[%+v], err:[%+v]",
			startTime.String(), endTime.String(), limit, offset, err)
		return result, err
	}

	return result, nil
}

// GetMappingRelationsWithTime 查询指定时间点的假uid映射关系
func (s *Store) GetMappingRelationsWithTime(ctx context.Context, t time.Time, limit, offset uint32) (
	[]*model.UKWFakeUidMappingRelationsTable, error) {

	log.DebugWithCtx(ctx, "GetMappingRelationsWithTime time:[%+v], limit:[%+v], offset:[%+v]",
		t.String(), limit, offset)

	// 拉取数据
	result := make([]*model.UKWFakeUidMappingRelationsTable, 0)
	if err := s.db.WithContext(ctx).Model(&model.UKWFakeUidMappingRelationsTable{}).
		Where("uid_period = ?", t).
		Where(SetDelFlagParam, model.TABLE_DELETE_FLAG_NO_DELETE).
		Offset(int(offset)).Limit(int(limit)).Find(&result).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			log.WarnWithCtx(ctx, "GetUKWUserFlow is null.")
			return result, nil
		}
		log.ErrorWithCtx(ctx, "GetUKWUserFlow Get info failed, t:[%+v], pageSize:[%+v], pageNum:[%+v], err:[%+v]",
			t.String(), limit, offset, err)
		return result, err
	}

	return result, nil
}

// UKWSwitchChange 神秘人开关状态变更
// @info 修改后的神秘人权限信息
func (s *Store) UKWSwitchChange(ctx context.Context, info *model.YouKnowWhoPermissionTable, oldRankSwitch bool) error {
	log.DebugWithCtx(ctx, "UKWSwitchChange info:[%+v]", info)

	if err := s.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 操作开关
		if err := tx.Model(&model.YouKnowWhoPermissionTable{}).
			Where(SetUidFlagParam, info.Uid).
			Where(SetDelFlagParam, model.TABLE_DELETE_FLAG_NO_DELETE).
			Updates(map[string]interface{}{
				"switch":      info.Switch,
				"rank_switch": info.RankSwitch,
			}).Error; err != nil {
			log.ErrorWithCtx(ctx, "UKWSwitchChange update failed, err:[%+v]", err)
			return err
		}

		// 组装流水
		flow := &model.YouKnowWhoFlowTable{}
		flow.Uid = info.Uid
		flow.OldStatus = info.Status
		flow.NewStatus = info.Status
		flow.OldRankSwitch = oldRankSwitch
		flow.NewRankSwitch = info.RankSwitch
		flow.TimeLeft = info.EffectiveTime
		flow.EffectiveTime = info.EffectiveTime
		flow.StartTime = info.OpenTime
		if info.Switch == model.UKW_SWITCH_ON {
			flow.ChangeReason = model.UKW_OPEN_SWITCH
		} else {
			flow.ChangeReason = model.UKW_CLOSE_SWITCH
		}
		flow.CreateTime = time.Now()
		flow.UpdateTime = time.Now()
		flow.DelFlag = model.TABLE_DELETE_FLAG_NO_DELETE

		// 插入流水
		flowTblName := model.GetUKWFlowTbl(flow.CreateTime)
		if err := tx.Table(flowTblName).Create(flow).Error; err != nil {
			log.ErrorWithCtx(ctx, "UKWSwitchChange create flow info failed, err:%s", err)
			return err
		}

		return nil
	}); err != nil {
		log.ErrorWithCtx(ctx, "UKWSwitchChange trans failed, err:[%+v]", err)
		return err
	}

	return nil
}

// UKWRankSwitchChange 神秘人排行榜开关状态变更
// @oldRankSwitch旧的开关状态
// @info 变更后的神秘人权限信息
func (s *Store) UKWRankSwitchChange(ctx context.Context, oldRankSwitch bool, info *model.YouKnowWhoPermissionTable) error {
	log.DebugWithCtx(ctx, "UKWRankSwitchChange info:[%+v]", info)

	if err := s.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 操作开关
		if err := tx.Model(&model.YouKnowWhoPermissionTable{}).
			Where(SetUidFlagParam, info.Uid).
			Where("del_flag != ?", model.TABLE_DELETE_FLAG_HAS_DELETE).
			Update("rank_switch", info.RankSwitch).Error; err != nil {
			log.ErrorWithCtx(ctx, "UKWRankSwitchChange update failed, err:[%+v]", err)
			return err
		}

		// 组装流水
		flow := &model.YouKnowWhoFlowTable{}
		flow.Uid = info.Uid
		flow.OldStatus = info.Status
		flow.NewStatus = info.Status
		flow.OldRankSwitch = oldRankSwitch
		flow.NewRankSwitch = info.RankSwitch
		flow.TimeLeft = info.EffectiveTime
		flow.EffectiveTime = info.EffectiveTime
		flow.StartTime = info.OpenTime
		if info.RankSwitch == model.UKW_RANK_SWITCH_ON {
			flow.ChangeReason = model.UKW_OPEN_RANK_SWITCH
		} else {
			flow.ChangeReason = model.UKW_CLOSE_RANK_SWITCH
		}
		flow.CreateTime = time.Now()
		flow.UpdateTime = time.Now()
		flow.DelFlag = model.TABLE_DELETE_FLAG_NO_DELETE

		// 插入流水
		flowTblName := model.GetUKWFlowTbl(flow.CreateTime)
		if err := tx.Table(flowTblName).Create(flow).Error; err != nil {
			log.ErrorWithCtx(ctx, "UKWRankSwitchChange create flow info failed, err:%s", err)
			return err
		}

		return nil
	}); err != nil {
		log.ErrorWithCtx(ctx, "UKWRankSwitchChange trans failed, err:[%+v]", err)
		return err
	}

	return nil
}

// UKWEnterNoticeChange 神秘人进房提示状态切换
// @info 修改后的神秘人权限信息
func (s *Store) UKWEnterNoticeChange(ctx context.Context, info *model.YouKnowWhoPermissionTable) error {
	log.DebugWithCtx(ctx, "UKWEnterNoticeChange info:[%+v]", info)

	if err := s.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		if err := tx.Model(&model.YouKnowWhoPermissionTable{}).
			Where(SetUidFlagParam, info.Uid).
			Where(SetDelFlagParam, model.TABLE_DELETE_FLAG_NO_DELETE).
			Updates(map[string]interface{}{
				"enter_notice": info.EnterNotice,
				"rank_switch":  model.UKW_RANK_SWITCH_OFF,
				"switch":       model.UKW_SWITCH_OFF,
			}).Error; err != nil {
			log.ErrorWithCtx(ctx, "UKWEnterNoticeChange update failed, err:[%+v]", err)
			return err
		}

		// 组装流水
		flow := &model.YouKnowWhoFlowTable{}
		flow.Uid = info.Uid
		flow.OldStatus = info.Status
		flow.NewStatus = info.Status
		flow.OldRankSwitch = info.RankSwitch
		flow.NewRankSwitch = model.UKW_RANK_SWITCH_OFF
		flow.TimeLeft = info.EffectiveTime
		flow.EffectiveTime = info.EffectiveTime
		flow.StartTime = info.OpenTime
		if info.EnterNotice == model.UKW_SWITCH_ON {
			flow.ChangeReason = model.UKW_NOTIC_OPEN_SWITCH
		} else {
			flow.ChangeReason = model.UKW_NOTIC_CLOSE_SWITCH
		}
		flow.CreateTime = time.Now()
		flow.UpdateTime = time.Now()
		flow.DelFlag = model.TABLE_DELETE_FLAG_NO_DELETE

		// 插入流水
		flowTblName := model.GetUKWFlowTbl(flow.CreateTime)
		if err := tx.Table(flowTblName).Create(flow).Error; err != nil {
			log.ErrorWithCtx(ctx, "UKWEnterNoticeChange create flow info failed, err:%s", err)
			return err
		}

		return nil
	}); err != nil {
		log.ErrorWithCtx(ctx, "UKWEnterNoticeChange trans failed, err:[%+v]", err)
		return err
	}

	return nil
}
