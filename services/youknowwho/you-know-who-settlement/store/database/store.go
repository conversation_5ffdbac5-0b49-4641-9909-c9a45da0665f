package database

import (
	"context"
	"errors"
	"fmt"
	"time"

	_ "github.com/go-sql-driver/mysql" // MySQL驱动。
	"golang.52tt.com/pkg/config"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/services/youknowwho/common/model"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

func NewMysql(cfg *config.MysqlConfig, testMode bool) (*Store, error) {
	var err error
	var mysqlDb *gorm.DB
	if mysqlDb, err = gorm.Open(mysql.New(mysql.Config{
		DSN:                       cfg.ConnectionString(),
		DefaultStringSize:         256,   // default size for string fields
		DisableDatetimePrecision:  true,  // disable datetime precision, which not supported before MySQL 5.6
		DontSupportRenameIndex:    true,  // drop & create when rename index, rename index not supported before MySQL 5.7, MariaDB
		DontSupportRenameColumn:   true,  // `change` when rename column, rename column not supported before MySQL 8, MariaDB
		SkipInitializeWithVersion: false, // auto configure based on currently MySQL version
	}), &gorm.Config{}); err != nil {
		return nil, err
	}

	if testMode {
		mysqlDb = mysqlDb.Debug()
	}

	return &Store{db: mysqlDb}, nil
}

type Store struct {
	db *gorm.DB
}

// CreateUKWOrderTable 用于定时创建神秘人订单表
func (s *Store) CreateUKWOrderTable() {
	nowTime := time.Now()
	// 本月初始时间
	thisMonthTime := time.Date(nowTime.Year(), nowTime.Month(), 1, 0, 0, 0, 0, time.Local)
	// 下月初始时间
	nextMonthTime := time.Date(nowTime.Year(), nowTime.Month()+1, 1, 0, 0, 0, 0, time.Local)
	// 创建本月表
	sqlOrderTableStr := fmt.Sprintf(model.CreateUKWOrderTable, model.GetUKWOrderTbl(thisMonthTime))
	err := s.db.Exec(sqlOrderTableStr).Error
	if err != nil {
		log.Warnf("CreateTable UKWOrderTable err: %s", err.Error())
	}
	// 创建下月表
	sqlOrderTableStr = fmt.Sprintf(model.CreateUKWOrderTable, model.GetUKWOrderTbl(nextMonthTime))
	err = s.db.Exec(sqlOrderTableStr).Error
	if err != nil {
		log.Warnf("CreateTable UKWOrderTable err: %s", err.Error())
	}
}

// CreateUKWFlowTable 用于定时创建神秘人流水表
func (s *Store) CreateUKWFlowTable() {
	nowTime := time.Now()
	// 本月初始时间
	thisMonthTime := time.Date(nowTime.Year(), nowTime.Month(), 1, 0, 0, 0, 0, time.Local)
	// 下月初始时间
	nextMonthTime := time.Date(nowTime.Year(), nowTime.Month()+1, 1, 0, 0, 0, 0, time.Local)
	// 创建本月表
	sqlFlowTableStr := fmt.Sprintf(model.CreateUKWFlowTable, model.GetUKWFlowTbl(thisMonthTime))
	err := s.db.Exec(sqlFlowTableStr).Error
	if err != nil {
		log.Warnf("CreateTable UKWStatusFlowTable err: %s", err.Error())
	}
	// 创建下月表
	sqlFlowTableStr = fmt.Sprintf(model.CreateUKWFlowTable, model.GetUKWFlowTbl(nextMonthTime))
	err = s.db.Exec(sqlFlowTableStr).Error
	if err != nil {
		log.Warnf("CreateTable UKWStatusFlowTable err: %s", err.Error())
	}
}

const (
	SetDelFlagParam = "del_flag = ?"
	SetUidFlagParam = "uid = ?"
)

// CreateUKWOrderReportTable 用于定时创建神秘人订单统计详情表
func (s *Store) CreateUKWOrderReportTable() {
	nowTime := time.Now()
	// 本月初始时间
	thisMonthTime := time.Date(nowTime.Year(), nowTime.Month(), 1, 0, 0, 0, 0, time.Local)
	// 下月初始时间
	nextMonthTime := time.Date(nowTime.Year(), nowTime.Month()+1, 1, 0, 0, 0, 0, time.Local)
	// 创建本月表
	sqlOrderTableStr := fmt.Sprintf(model.CreateUKWOrderReportTable, model.GetUKWOrderReportTbl(thisMonthTime))
	err := s.db.Exec(sqlOrderTableStr).Error
	if err != nil {
		log.Warnf("createUKWOrderReportTable UKWOrderReportTable err: %s", err.Error())
	}
	// 创建下月表
	sqlOrderTableStr = fmt.Sprintf(model.CreateUKWOrderReportTable, model.GetUKWOrderReportTbl(nextMonthTime))
	err = s.db.Exec(sqlOrderTableStr).Error
	if err != nil {
		log.Warnf("createUKWOrderReportTable UKWOrderReportTable err: %s", err.Error())
	}
}

// GetAllUKWPermissionInfos 分页获取全部神秘人信息
// @pageSize 分页大小
// @pageNum 页数，页数从第一页开始
func (s *Store) GetAllUKWPermissionInfos(ctx context.Context, pageSize, pageNum uint32) ([]*model.YouKnowWhoPermissionTable, error) {
	log.DebugWithCtx(ctx, "GetAllUKWPermissionInfos pageSize:[%+v], pageNum:[%+v]", pageSize, pageNum)

	// 处理分页问题
	if pageNum < 1 {
		err := fmt.Errorf("GetAllUKWPermissionInfos pageNum is below than 1, pageNum:[%+v]", pageNum)
		log.ErrorWithCtx(ctx, "%+v", err)
		return nil, err
	}
	page := pageNum - 1
	offset := page * pageSize

	// 拉取数据
	permissionInfos := make([]*model.YouKnowWhoPermissionTable, 0)
	if err := s.db.WithContext(ctx).Model(&model.YouKnowWhoPermissionTable{}).
		Where(SetDelFlagParam, model.TABLE_DELETE_FLAG_NO_DELETE).
		Offset(int(offset)).Limit(int(pageSize)).Find(&permissionInfos).Error; err != nil {
		log.ErrorWithCtx(ctx, "GetAllUKWPermissionInfos Get info failed, pageSize:[%+v], pageNum:[%+v], err:[%+v]",
			pageSize, pageNum, err)
		return permissionInfos, err
	}

	log.DebugWithCtx(ctx, "GetAllUKWPermissionInfos permissionInfos:[%+v]", permissionInfos)
	return permissionInfos, nil
}

// GetAllFreezeUKWPermissionInfos 分页获取全部被冻结的神秘人信息
// @pageSize 分页大小
// @pageNum 页数，页数从第一页开始
func (s *Store) GetAllFreezeUKWPermissionInfos(ctx context.Context, pageSize, pageNum uint32) ([]*model.YouKnowWhoPermissionTable, error) {
	log.DebugWithCtx(ctx, "GetAllFreezeUKWPermissionInfos pageSize:[%+v], pageNum:[%+v]", pageSize, pageNum)

	// 处理分页问题
	if pageNum < 1 {
		err := fmt.Errorf("GetAllFreezeUKWPermissionInfos pageNum is below than 1, pageNum:[%+v]", pageNum)
		log.ErrorWithCtx(ctx, "%+v", err)
		return nil, err
	}
	page := pageNum - 1
	offset := page * pageSize

	// 拉取数据
	permissionInfos := make([]*model.YouKnowWhoPermissionTable, 0)
	if err := s.db.WithContext(ctx).Model(&model.YouKnowWhoPermissionTable{}).
		Where(SetDelFlagParam, model.TABLE_DELETE_FLAG_NO_DELETE).
		Where("status = ?", model.UKW_FREEZE).
		Offset(int(offset)).Limit(int(pageSize)).Find(&permissionInfos).Error; err != nil {
		log.ErrorWithCtx(ctx, "GetAllFreezeUKWPermissionInfos Get info failed, pageSize:[%+v], pageNum:[%+v], err:[%+v]",
			pageSize, pageNum, err)
		return permissionInfos, err
	}

	log.DebugWithCtx(ctx, "GetAllFreezeUKWPermissionInfos permissionInfos:[%+v]", permissionInfos)
	return permissionInfos, nil
}

// GetAllOrdersWithUid 分页按月获取指定月份指定神秘人所有的财务报表统计订单消耗数据
// @t 月份时间
// @uid 神秘人uid
// @limit 分页大小
// @offset 偏移量
func (s *Store) GetAllOrdersWithUid(ctx context.Context, t time.Time, uid uint32, offset, limit int) (
	[]*model.YouKnowWhoOrderTable, error) {
	log.DebugWithCtx(ctx, "GetAllOrdersWithUid time:[%+v], uid:[%+v], offset:[%+v], limit:[%+v]",
		t.String(), uid, offset, limit)

	// 拉取数据
	result := make([]*model.YouKnowWhoOrderTable, 0)
	if err := s.db.WithContext(ctx).Table(model.GetUKWOrderTbl(t)).
		Where(SetUidFlagParam, uid).
		Where(SetDelFlagParam, model.TABLE_DELETE_FLAG_NO_DELETE).
		Offset(offset).Limit(limit).Find(&result).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			log.WarnWithCtx(ctx, "GetAllOrdersWithUid is null.")
			return result, nil
		}
		log.ErrorWithCtx(ctx, "GetAllOrdersWithUid Get info failed, t:[%+v], uid:[%+v], offset:[%+v], "+
			"limit:[%+v], err:[%+v]", t.String(), uid, offset, limit, err)
		return result, err
	}

	return result, nil
}

// GetAllOrderReportInfosWithUid 分页按月获取指定月份指定神秘人所有的财务报表统计订单消耗数据
// @t 月份时间
// @uid 神秘人uid
// @limit 分页大小
// @offset 偏移量
func (s *Store) GetAllOrderReportInfosWithUid(ctx context.Context, t time.Time, uid uint32, offset, limit int) (
	[]*model.YouKnowWhoOrderReportTable, error) {
	log.DebugWithCtx(ctx, "GetAllOrderReportInfosWithUid time:[%+v], uid:[%+v], offset:[%+v], limit:[%+v]",
		t.String(), uid, offset, limit)

	// 拉取数据
	result := make([]*model.YouKnowWhoOrderReportTable, 0)
	if err := s.db.WithContext(ctx).Table(model.GetUKWOrderReportTbl(t)).
		Where(SetUidFlagParam, uid).
		Where(SetDelFlagParam, model.TABLE_DELETE_FLAG_NO_DELETE).
		Offset(offset).Limit(limit).Find(&result).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			log.WarnWithCtx(ctx, "GetAllOrderReportInfosWithUid is null.")
			return result, nil
		}
		log.ErrorWithCtx(ctx, "GetAllOrderReportInfosWithUid Get info failed, t:[%+v], uid:[%+v], offset:[%+v], "+
			"limit:[%+v], err:[%+v]", t.String(), uid, offset, limit, err)
		return result, err
	}

	return result, nil
}

// GetUKWUserFlow 获取指定用户月流水
// @uid 神秘人Uid
// @t 确定月份的时间
// @limit 单页查询数量
// @offset 偏移量
func (s *Store) GetUKWUserFlow(ctx context.Context, uid uint32, t time.Time, offset, limit int) (
	[]*model.YouKnowWhoFlowTable, error) {
	log.DebugWithCtx(ctx, "GetUKWUserFlow time:[%+v], uid:[%+v], offset:[%+v], limit:[%+v]",
		t.String(), uid, offset, limit)

	// 拉取数据
	result := make([]*model.YouKnowWhoFlowTable, 0)
	if err := s.db.WithContext(ctx).Table(model.GetUKWFlowTbl(t)).
		Where(SetUidFlagParam, uid).
		Where(SetDelFlagParam, model.TABLE_DELETE_FLAG_NO_DELETE).
		Offset(offset).Limit(limit).Find(&result).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			log.WarnWithCtx(ctx, "GetUKWUserFlow is null.")
			return result, nil
		}
		log.ErrorWithCtx(ctx, "GetUKWUserFlow Get info failed, t:[%+v], uid:[%+v], offset:[%+v], limit:[%+v],"+
			" err:[%+v]", t.String(), uid, offset, limit, err)
		return result, err
	}

	return result, nil
}

// CountOrderWithPrice 累计价值相同的订单数量
// @t 决定表的时间
// @price 订单价格
// @giftPrice 附赠礼品价格
// @EffectiveTime 订单有效时长
func (s *Store) CountOrderWithPrice(ctx context.Context, t time.Time, price, giftPrice uint32, EffectiveTime uint64) (
	int64, error) {

	var count int64
	// 按照条件查询订单的数量
	if err := s.db.WithContext(ctx).Table(model.GetUKWOrderTbl(t)).
		Where("price = ?", price).
		Where("gift_price = ?", giftPrice).
		Where("effective_time = ?", EffectiveTime).
		Where("del_flag != ?", model.TABLE_DELETE_FLAG_HAS_DELETE).
		Count(&count).Error; err != nil {
		log.ErrorWithCtx(ctx, "CountOrderWithPrice failed, t:[%+v], price:[%+v], giftPrice:[%+v], EffectiveTime:[%+v], err:[%+v]",
			t, price, giftPrice, EffectiveTime, err)
		return 0, err
	}

	return count, nil
}

// GetPeriodReportWithInfo 按照统计信息获取账期内结算汇总信息
// @period 结算周期时间
// @price 套餐价格
// @giftPrice 附赠礼品价格
// @validTime 套餐有效时间
func (s *Store) GetPeriodReportWithInfo(ctx context.Context, period string, price uint32, giftPrice uint32, validTime uint32) (*model.YouKnowWhoReportTable, error) {

	result := &model.YouKnowWhoReportTable{}
	if err := s.db.WithContext(ctx).Model(&model.YouKnowWhoReportTable{}).
		Where("report_period = ?", period).
		Where("price = ?", price).
		Where("gift_price = ?", giftPrice).
		Where("valid_time = ?", validTime).
		Where(SetDelFlagParam, model.TABLE_DELETE_FLAG_NO_DELETE).
		First(&result).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return result, nil
		}
		log.ErrorWithCtx(ctx, "GetAllPeriodReportInfos failed, period:[%+v], price:[%+v], giftPrice:[%+v], validTime:[%+v], err:[%s]",
			period, price, giftPrice, validTime, err)
		return result, err
	}
	return result, nil
}

// UpdateUkwReportInfo 新建或更新神秘人财务统计汇总表数据
// @reportInfo 需要更新的统计数据
func (s *Store) UpdateUkwReportInfo(ctx context.Context, reportInfo *model.YouKnowWhoReportTable) error {

	if err := s.db.WithContext(ctx).Clauses(clause.OnConflict{ // 当已存在对应信息时，执行更新操作
		DoUpdates: clause.Assignments(map[string]interface{}{
			"start_remain_effective": reportInfo.StartRemainEffective,
			"cumulative_sales":       reportInfo.CumulativeSales,
			"cumulative_gift":        reportInfo.CumulativeGift,
			"cumulative_use":         reportInfo.CumulativeUse,
			"end_remain_effective":   reportInfo.EndRemainEffective,
			"table_check":            reportInfo.Check,
		}),
	}).Model(&model.YouKnowWhoReportTable{}).Create(reportInfo).Error; err != nil {
		log.ErrorWithCtx(ctx, "UpdateUkwReportInfo info failed, err:%s", err)
		return err
	}

	return nil
}

// UpdateUkwOrderReportInfo 更新神秘人财务报表统计订单消耗表
// @t 统计时间点
// @orderReportInfo 变更的财务报表统计订单消耗数据
func (s *Store) UpdateUkwOrderReportInfo(ctx context.Context, t time.Time, orderReportInfo *model.YouKnowWhoOrderReportTable) error {
	log.InfoWithCtx(ctx, "UpdateUkwOrderReportInfo t:[%+v], orderReportInfo:[%+v]", t.String(), orderReportInfo)

	if err := s.db.WithContext(ctx).Table(model.GetUKWOrderReportTbl(t)).Clauses(clause.OnConflict{ // 当已存在对应信息时，执行更新操作
		DoUpdates: clause.Assignments(map[string]interface{}{
			"effective_left": orderReportInfo.EffectiveLeft,
			"use_start_time": orderReportInfo.UseStartTime,
			"use_end_time":   orderReportInfo.UseEndTime,
		}),
	}).Create(orderReportInfo).Error; err != nil {
		log.ErrorWithCtx(ctx, "UpdateUkwOrderReportInfo failed, err:[%+v]", err)
		return err
	}

	return nil
}

// SettlementUKW 核销神秘人
// @permissionInfo 权限变更后数据
// @flowInfo 变更记录
func (s *Store) SettlementUKW(ctx context.Context, flowInfo *model.YouKnowWhoFlowTable, uid uint32, ts uint64) (*model.YouKnowWhoPermissionTable, error) {

	permissionInfo := &model.YouKnowWhoPermissionTable{}
	newPermissionInfo := &model.YouKnowWhoPermissionTable{}
	err := s.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {

		tx.Set("gorm:query_option", "FOR UPDATE")

		err := tx.Model(&model.YouKnowWhoPermissionTable{}).Where(SetUidFlagParam, uid).First(permissionInfo).Error
		if nil != err {
			log.ErrorWithCtx(ctx, "SettlementUKW First old permissionInfo fail err:%v", err)
			return err
		}

		updateVals := map[string]interface{}{}
		updateVals["effective_time"] = gorm.Expr("if(effective_time>=?,effective_time-?,0)", ts, ts)
		updateVals["status"] = gorm.Expr("if(effective_time>=?,status,0)", ts)
		updateVals["switch"] = gorm.Expr("if(effective_time>=?,switch,0)", ts)
		updateVals["rank_switch"] = gorm.Expr("if(effective_time>=?,rank_switch,0)", ts)
		updateVals["enter_notice"] = gorm.Expr("if(effective_time>=?,enter_notice,0)", ts)

		err = tx.Model(&model.YouKnowWhoPermissionTable{}).Where(SetUidFlagParam, uid).Updates(updateVals).Error

		if nil != err {
			log.ErrorWithCtx(ctx, "SettlementUKW update permissionInfo fail err:%v", err)
			return err
		}

		err = tx.Model(&model.YouKnowWhoPermissionTable{}).Where(SetUidFlagParam, uid).First(newPermissionInfo).Error
		if nil != err {
			log.ErrorWithCtx(ctx, "SettlementUKW First new permissionInfo fail err:%v", err)
			return err
		}

		flowInfo.UpdateTime = time.Now()
		flowInfo.StartTime = permissionInfo.OpenTime
		flowInfo.OldStatus = permissionInfo.Status
		flowInfo.NewStatus = newPermissionInfo.Status
		flowInfo.OldRankSwitch = permissionInfo.RankSwitch
		flowInfo.NewRankSwitch = newPermissionInfo.RankSwitch
		flowInfo.EffectiveTime = newPermissionInfo.EffectiveTime
		flowInfo.TimeLeft = permissionInfo.EffectiveTime

		flowTblName := model.GetUKWFlowTbl(flowInfo.CreateTime)
		if err := tx.Table(flowTblName).Create(flowInfo).Error; err != nil {
			log.ErrorWithCtx(ctx, "SettlementUKW create flow info failed, err:%s", err)
			return err
		}

		return nil
	})
	return newPermissionInfo, err

}

// GetUKWPermissionInfo 获取指定神秘人的权限信息
// @uid 神秘人Uid
func (s *Store) GetUKWPermissionInfo(ctx context.Context, uid uint32) (*model.YouKnowWhoPermissionTable, error) {
	log.DebugWithCtx(ctx, "GetUKWPermissionInfo uid:[%+v]", uid)
	permission := &model.YouKnowWhoPermissionTable{}

	if err := s.db.WithContext(ctx).Model(&model.YouKnowWhoPermissionTable{}).
		Where(SetUidFlagParam, uid).
		Where(SetDelFlagParam, model.TABLE_DELETE_FLAG_NO_DELETE).
		First(permission).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return &model.YouKnowWhoPermissionTable{}, nil
		}
		log.ErrorWithCtx(ctx, "GetUKWPermissionInfo Get info failed, uid:[%+v], err:[%+v]", uid, err)
		return permission, err
	}

	log.DebugWithCtx(ctx, "GetUKWPermissionInfo permission:[%+v]", permission)
	return permission, nil
}

// GetUkwStatusFlowLastedOpen 根据时间获取当天最近一次开通的记录
// @uid 神秘人的Uid
// @changeTime 变更的时间
func (s *Store) GetUkwStatusFlowLastedOpen(ctx context.Context, uid uint32, changeTime time.Time) (*model.YouKnowWhoFlowTable, error) {
	flow := &model.YouKnowWhoFlowTable{}

	// 处理时间
	beginTime := time.Date(changeTime.Year(), changeTime.Month(), changeTime.Day(), 0, 0, 0, 0, time.Local)
	endTime := time.Date(changeTime.Year(), changeTime.Month(), changeTime.Day()+1, 0, 0, 0, 0, time.Local)

	if err := s.db.WithContext(ctx).Table(model.GetUKWFlowTbl(changeTime)).
		Where(SetUidFlagParam, uid).
		Where(SetDelFlagParam, model.TABLE_DELETE_FLAG_NO_DELETE).
		Where("start_time >= ?", beginTime).
		Where("start_time <= ?", endTime).
		Where("old_status in (?)", []int{model.UKW_NO_OPEN, model.UKW_FREEZE}).
		Where("new_status = ?", model.UKW_OPEN).
		Order("update_time DESC").
		First(&flow).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return flow, nil
		}
		log.ErrorWithCtx(ctx, "GetUkwStatusChangeFlow failed, err:[%+v]", err)
		return nil, err
	}

	return flow, nil
}

// FreezeUKW 冻结神秘人，根据传入的需要变更的流水来进行处理神秘人权限数据
// NOTICE：当前函数会对当天神秘人时间做核减。
// @permissionInfo 权限变更后数据
// @flowInfo 变更记录
// @ts 需要核减的时间
func (s *Store) FreezeUKW(ctx context.Context, permissionInfo *model.YouKnowWhoPermissionTable, flowInfo *model.YouKnowWhoFlowTable, ts uint64) error {

	// 开启事务操作
	if err := s.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {

		updateVals := map[string]interface{}{}
		updateVals["effective_time"] = gorm.Expr("if(effective_time>=?,effective_time-?,0)", ts, ts) // 解冻同时需要核减时间
		updateVals["status"] = gorm.Expr("if(effective_time>=?,?,?)", ts, model.UKW_FREEZE, model.UKW_NO_OPEN)
		updateVals["switch"] = gorm.Expr("?", model.UKW_SWITCH_OFF)
		updateVals["rank_switch"] = gorm.Expr("?", model.UKW_RANK_SWITCH_OFF)
		updateVals["enter_notice"] = gorm.Expr("?", model.UKW_SWITCH_OFF)
		// 操作权限表
		if err := tx.Model(&model.YouKnowWhoPermissionTable{}).
			Where(SetUidFlagParam, permissionInfo.Uid).
			Where(SetDelFlagParam, model.TABLE_DELETE_FLAG_NO_DELETE).
			Where("status = ?", model.UKW_OPEN).
			Updates(updateVals).
			Error; err != nil {
			log.ErrorWithCtx(ctx, "FreezeUKW update Permission info failed, err:%s", err)
			return err
		}

		// 写入操作流水
		flowTblName := model.GetUKWFlowTbl(flowInfo.CreateTime)
		if err := tx.Table(flowTblName).Create(flowInfo).Error; err != nil {
			log.ErrorWithCtx(ctx, "FreezeUKW create flow info failed, err:%s", err)
			return err
		}

		return nil
	}); err != nil {
		log.ErrorWithCtx(ctx, "FreezeUKW Transaction err:[%+v]", err)
		return err
	}

	return nil
}

// UnFreezeUKW 解冻神秘人，根据传入的需要变更的流水来进行处理神秘人权限数据
// @permissionInfo 权限变更后数据
// @flowInfo 变更记录
func (s *Store) UnFreezeUKW(ctx context.Context, permissionInfo *model.YouKnowWhoPermissionTable, flowInfo *model.YouKnowWhoFlowTable) error {

	// 开启事务操作
	if err := s.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 操作权限表
		if err := tx.Model(&model.YouKnowWhoPermissionTable{}).
			Where(SetUidFlagParam, permissionInfo.Uid).
			Where(SetDelFlagParam, model.TABLE_DELETE_FLAG_NO_DELETE).
			Where("status = ?", model.UKW_FREEZE).
			Updates(map[string]interface{}{
				"status":       model.UKW_OPEN,
				"switch":       model.UKW_SWITCH_OFF,
				"rank_switch":  model.UKW_RANK_SWITCH_OFF,
				"open_time":    permissionInfo.OpenTime,
				"enter_notice": permissionInfo.EnterNotice,
			}).
			Error; err != nil {
			log.ErrorWithCtx(ctx, "UnFreezeUKW update Permission info failed, err:%s", err)
			return err
		}

		// 修改流水信息
		flowInfo.OldStatus = model.UKW_FREEZE
		flowInfo.NewStatus = model.UKW_OPEN
		flowInfo.OldRankSwitch = model.UKW_RANK_SWITCH_OFF
		flowInfo.NewRankSwitch = model.UKW_RANK_SWITCH_OFF
		// 写入操作流水
		flowTblName := model.GetUKWFlowTbl(flowInfo.CreateTime)
		if err := tx.Table(flowTblName).Create(flowInfo).Error; err != nil {
			log.ErrorWithCtx(ctx, "UnFreezeUKW create flow info failed, err:%s", err)
			return err
		}

		return nil
	}); err != nil {
		log.ErrorWithCtx(ctx, "UnFreezeUKW Transaction err:[%+v]", err)
		return err
	}

	return nil
}

// OpenUKW 开通神秘人
// @orderInfo 新增订单的详情
// @permissionInfo 修改后的神秘人身份数据
// @flowInfo 流水表，变更详情
// @mappingRelation 假uid映射关系表
func (s *Store) OpenUKW(ctx context.Context, orderInfo *model.YouKnowWhoOrderTable, permissionInfo *model.YouKnowWhoPermissionTable,
	flowInfo *model.YouKnowWhoFlowTable, mappingRelation *model.UKWFakeUidMappingRelationsTable) error {

	// 开启事务操作
	if err := s.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 更新订单表
		// 订单表更新冲突直接返回错误，保证不会重复累加神秘人剩余时间
		if err := tx.Table(model.GetUKWOrderTbl(flowInfo.CreateTime)).Create(orderInfo).Error; err != nil {
			log.ErrorWithCtx(ctx, "OpenUKW Create order failed, err:%s", err)
			return err
		}

		// 更新神秘人变更流水表
		if err := tx.Table(model.GetUKWFlowTbl(flowInfo.CreateTime)).
			Create(flowInfo).Error; err != nil {
			log.ErrorWithCtx(ctx, "OpenUKW Create status flow failed, err:%s", err)
			return err
		}

		// 更新神秘人权限表
		if err := tx.Clauses(clause.OnConflict{ // 当已存在对应的神秘人权限条目的时候，直接更新权限的时间信息
			DoUpdates: clause.Assignments(map[string]interface{}{
				"fake_uid":       permissionInfo.FakeUid,
				"status":         permissionInfo.Status,
				"switch":         permissionInfo.Switch,
				"rank_switch":    permissionInfo.RankSwitch,
				"enter_notice":   permissionInfo.EnterNotice,
				"effective_time": gorm.Expr("effective_time + ?", orderInfo.EffectiveTime), // 直接加数值，保证准确性，该字段不使用赋值更新
				"total_time":     gorm.Expr("total_time + ?", permissionInfo.TotalTime),
				"open_time":      permissionInfo.OpenTime,
			}),
		}).Model(&model.YouKnowWhoPermissionTable{}).Create(permissionInfo).Error; err != nil {
			log.ErrorWithCtx(ctx, "OpenUKW Create Permission info failed, err:%s", err)
			return err
		}

		// 写神秘人假uid映射表
		if mappingRelation != nil && mappingRelation.FakeUid != 0 {
			if err := tx.Model(&model.UKWFakeUidMappingRelationsTable{}).Create(mappingRelation).Error; err != nil {
				log.ErrorWithCtx(ctx, "OpenUKW update FakeUidMappingRelations failed, err:[%+v]", err)
				return err
			}
		}

		return nil
	}); err != nil {
		log.ErrorWithCtx(ctx, "OpenUKW Transaction err:[%+v]", err)
		return err
	}

	return nil
}

// UpdateOrderPkgSendType 更新订单的包裹发放状态
// @orderId 订单ID
// @openTime 开通时间
// @giftPrice 礼物价值
// @pkgSendType 需要切换的包裹发放状态
func (s *Store) UpdateOrderPkgSendType(ctx context.Context, orderId string, openTime time.Time, giftPrice uint32, pkgSendType bool) error {

	if err := s.db.WithContext(ctx).Table(model.GetUKWOrderTbl(openTime)).
		Where("order_id = ?", orderId).
		Where(SetDelFlagParam, model.TABLE_DELETE_FLAG_NO_DELETE).
		Updates(map[string]interface{}{
			"pkg_send":   pkgSendType,
			"gift_price": giftPrice,
		}).Error; err != nil {
		log.ErrorWithCtx(ctx, "UpdateOrderPkgSendType failed, orderId:[%+v], openTime:[%+v], pkgSend:[%+v], err:[%+v]",
			orderId, openTime.String(), pkgSendType, err)
		return err
	}

	return nil
}

// GetUKWOrder 获取神秘人订单
// @uid 神秘人ID
// @orderId 订单ID
// @openTime 订单时间
func (s *Store) GetUKWOrder(ctx context.Context, uid uint32, orderId string, openTime time.Time) (*model.YouKnowWhoOrderTable, error) {
	log.DebugWithCtx(ctx, "GetUKWOrder uid:[%+v], orderId:[%+v], openTime:[%+v]", uid, orderId, openTime.String())
	orderInfo := &model.YouKnowWhoOrderTable{}

	// 获取订单
	if err := s.db.WithContext(ctx).Table(model.GetUKWOrderTbl(openTime)).
		Where("order_id = ?", orderId).
		Where(SetUidFlagParam, uid).
		Where(SetDelFlagParam, model.TABLE_DELETE_FLAG_NO_DELETE).
		Find(orderInfo).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			log.WarnWithCtx(ctx, "GetUKWOrder not found ")
			return nil, nil
		}
		log.ErrorWithCtx(ctx, "GetUKWOrder find failed, err:[%+v]", err)
		return orderInfo, err
	}

	log.InfoWithCtx(ctx, "GetUKWOrder succ, orderInfo:[%+v]", orderInfo)
	return orderInfo, nil
}

// GetOrderInfoForTimeRange 获取对账所需订单数据
// @start 查询开始时间
// @end 查询结束时间
func (s *Store) GetOrderInfoForTimeRange(ctx context.Context, start time.Time, end time.Time) (uint32, uint32, error) {
	var count int64

	// 获取查询的条件
	db := s.db.WithContext(ctx).Table(model.GetUKWOrderTbl(start)).
		Where("open_time >= ?", start).
		Where("open_time < ?", end).
		Where(SetDelFlagParam, model.TABLE_DELETE_FLAG_NO_DELETE)

	// 查询数量
	if err := db.Select("order_id").Count(&count).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return 0, 0, nil
		}
		log.ErrorWithCtx(ctx, "GetOrderInfoForTimeRange count failed, start:[%+v], end:[%+v], err:[%+v]", start, end, err)
		return 0, 0, err
	}

	if count == 0 {
		return 0, 0, nil
	}

	// 计算总和
	orderSum := &model.YouKnowWhoOrderTable{}
	if err := db.Select("sum(price) as price").First(orderSum).Error; err != nil {
		log.ErrorWithCtx(ctx, "GetOrderInfoForTimeRange sum failed, start:[%+v], end:[%+v], err:[%+v]", start, end, err)
		return 0, 0, err
	}

	return uint32(count), orderSum.Price, nil
}

// GetAllPkgNotSendOrder 获取所有没有成功发放礼包的订单
// @t 当前时间，用于决定数据表
func (s *Store) GetAllPkgNotSendOrder(ctx context.Context, t time.Time) ([]*model.YouKnowWhoOrderTable, error) {

	result := make([]*model.YouKnowWhoOrderTable, 0)
	if err := s.db.WithContext(ctx).Table(model.GetUKWOrderTbl(t)).
		Where("pkg_send = ?", model.UKW_ORDER_PKG_NOT_SENT).
		Where(SetDelFlagParam, model.TABLE_DELETE_FLAG_NO_DELETE).
		Find(&result).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return result, nil
		}
		log.ErrorWithCtx(ctx, "GetAllPkgNotSendOrder failed, t:[%+v], err:[%+v]", t, err)
	}

	return result, nil
}
