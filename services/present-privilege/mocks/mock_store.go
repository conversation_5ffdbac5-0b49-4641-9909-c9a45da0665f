// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/services/present-privilege/mysql (interfaces: ITreasureStore,IStore)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"
	time "time"

	gomock "github.com/golang/mock/gomock"
	presentprivilege "golang.52tt.com/protocol/services/presentprivilege"
	mysql "golang.52tt.com/services/present-privilege/mysql"
)

// MockITreasureStore is a mock of ITreasureStore interface.
type MockITreasureStore struct {
	ctrl     *gomock.Controller
	recorder *MockITreasureStoreMockRecorder
}

// MockITreasureStoreMockRecorder is the mock recorder for MockITreasureStore.
type MockITreasureStoreMockRecorder struct {
	mock *MockITreasureStore
}

// NewMockITreasureStore creates a new mock instance.
func NewMockITreasureStore(ctrl *gomock.Controller) *MockITreasureStore {
	mock := &MockITreasureStore{ctrl: ctrl}
	mock.recorder = &MockITreasureStoreMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockITreasureStore) EXPECT() *MockITreasureStoreMockRecorder {
	return m.recorder
}

// AddTreasureCondition mocks base method.
func (m *MockITreasureStore) AddTreasureCondition(arg0 context.Context, arg1, arg2 uint32, arg3 []*mysql.TreasureConditions) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddTreasureCondition", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddTreasureCondition indicates an expected call of AddTreasureCondition.
func (mr *MockITreasureStoreMockRecorder) AddTreasureCondition(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddTreasureCondition", reflect.TypeOf((*MockITreasureStore)(nil).AddTreasureCondition), arg0, arg1, arg2, arg3)
}

// BeginTx mocks base method.
func (m *MockITreasureStore) BeginTx() mysql.ITreasureStore {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BeginTx")
	ret0, _ := ret[0].(mysql.ITreasureStore)
	return ret0
}

// BeginTx indicates an expected call of BeginTx.
func (mr *MockITreasureStoreMockRecorder) BeginTx() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BeginTx", reflect.TypeOf((*MockITreasureStore)(nil).BeginTx))
}

// CommitTx mocks base method.
func (m *MockITreasureStore) CommitTx() error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CommitTx")
	ret0, _ := ret[0].(error)
	return ret0
}

// CommitTx indicates an expected call of CommitTx.
func (mr *MockITreasureStoreMockRecorder) CommitTx() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CommitTx", reflect.TypeOf((*MockITreasureStore)(nil).CommitTx))
}

// CreateTable mocks base method.
func (m *MockITreasureStore) CreateTable(arg0 interface{}) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateTable", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateTable indicates an expected call of CreateTable.
func (mr *MockITreasureStoreMockRecorder) CreateTable(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateTable", reflect.TypeOf((*MockITreasureStore)(nil).CreateTable), arg0)
}

// CreateTreasurePrivilege mocks base method.
func (m *MockITreasureStore) CreateTreasurePrivilege(arg0 context.Context, arg1 *mysql.TreasurePrivilege) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateTreasurePrivilege", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateTreasurePrivilege indicates an expected call of CreateTreasurePrivilege.
func (mr *MockITreasureStoreMockRecorder) CreateTreasurePrivilege(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateTreasurePrivilege", reflect.TypeOf((*MockITreasureStore)(nil).CreateTreasurePrivilege), arg0, arg1)
}

// CreateTreasurePrivilegeMonthlyHistory mocks base method.
func (m *MockITreasureStore) CreateTreasurePrivilegeMonthlyHistory(arg0 context.Context, arg1 *mysql.TreasurePrivilegeMonthlyHistory) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateTreasurePrivilegeMonthlyHistory", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateTreasurePrivilegeMonthlyHistory indicates an expected call of CreateTreasurePrivilegeMonthlyHistory.
func (mr *MockITreasureStoreMockRecorder) CreateTreasurePrivilegeMonthlyHistory(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateTreasurePrivilegeMonthlyHistory", reflect.TypeOf((*MockITreasureStore)(nil).CreateTreasurePrivilegeMonthlyHistory), arg0, arg1)
}

// CreateUserTreasureCondition mocks base method.
func (m *MockITreasureStore) CreateUserTreasureCondition(arg0 context.Context, arg1 *mysql.UserTreasureCondition) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateUserTreasureCondition", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateUserTreasureCondition indicates an expected call of CreateUserTreasureCondition.
func (mr *MockITreasureStoreMockRecorder) CreateUserTreasureCondition(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateUserTreasureCondition", reflect.TypeOf((*MockITreasureStore)(nil).CreateUserTreasureCondition), arg0, arg1)
}

// DeleteTreasureCondition mocks base method.
func (m *MockITreasureStore) DeleteTreasureCondition(arg0 context.Context, arg1 []uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteTreasureCondition", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteTreasureCondition indicates an expected call of DeleteTreasureCondition.
func (mr *MockITreasureStoreMockRecorder) DeleteTreasureCondition(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteTreasureCondition", reflect.TypeOf((*MockITreasureStore)(nil).DeleteTreasureCondition), arg0, arg1)
}

// DeleteTreasurePrivilege mocks base method.
func (m *MockITreasureStore) DeleteTreasurePrivilege(arg0 context.Context, arg1, arg2 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteTreasurePrivilege", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteTreasurePrivilege indicates an expected call of DeleteTreasurePrivilege.
func (mr *MockITreasureStoreMockRecorder) DeleteTreasurePrivilege(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteTreasurePrivilege", reflect.TypeOf((*MockITreasureStore)(nil).DeleteTreasurePrivilege), arg0, arg1, arg2)
}

// DeleteTreasurePrivilegeMonthlyHistory mocks base method.
func (m *MockITreasureStore) DeleteTreasurePrivilegeMonthlyHistory(arg0 context.Context, arg1 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteTreasurePrivilegeMonthlyHistory", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteTreasurePrivilegeMonthlyHistory indicates an expected call of DeleteTreasurePrivilegeMonthlyHistory.
func (mr *MockITreasureStoreMockRecorder) DeleteTreasurePrivilegeMonthlyHistory(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteTreasurePrivilegeMonthlyHistory", reflect.TypeOf((*MockITreasureStore)(nil).DeleteTreasurePrivilegeMonthlyHistory), arg0, arg1)
}

// FindRecordsWithMaxEndTimeInRange mocks base method.
func (m *MockITreasureStore) FindRecordsWithMaxEndTimeInRange(arg0 context.Context, arg1 uint32, arg2, arg3 time.Time) ([]*mysql.TreasurePrivilege, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FindRecordsWithMaxEndTimeInRange", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].([]*mysql.TreasurePrivilege)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindRecordsWithMaxEndTimeInRange indicates an expected call of FindRecordsWithMaxEndTimeInRange.
func (mr *MockITreasureStoreMockRecorder) FindRecordsWithMaxEndTimeInRange(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindRecordsWithMaxEndTimeInRange", reflect.TypeOf((*MockITreasureStore)(nil).FindRecordsWithMaxEndTimeInRange), arg0, arg1, arg2, arg3)
}

// GetAllConditionPri mocks base method.
func (m *MockITreasureStore) GetAllConditionPri(arg0 context.Context, arg1, arg2 uint32) ([]*mysql.ConditionPrivilege, uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllConditionPri", arg0, arg1, arg2)
	ret0, _ := ret[0].([]*mysql.ConditionPrivilege)
	ret1, _ := ret[1].(uint32)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetAllConditionPri indicates an expected call of GetAllConditionPri.
func (mr *MockITreasureStoreMockRecorder) GetAllConditionPri(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllConditionPri", reflect.TypeOf((*MockITreasureStore)(nil).GetAllConditionPri), arg0, arg1, arg2)
}

// GetAllTreasureCondition mocks base method.
func (m *MockITreasureStore) GetAllTreasureCondition(arg0 context.Context) ([]*mysql.TreasureConditions, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllTreasureCondition", arg0)
	ret0, _ := ret[0].([]*mysql.TreasureConditions)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllTreasureCondition indicates an expected call of GetAllTreasureCondition.
func (mr *MockITreasureStoreMockRecorder) GetAllTreasureCondition(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllTreasureCondition", reflect.TypeOf((*MockITreasureStore)(nil).GetAllTreasureCondition), arg0)
}

// GetTreasureConditionByItemId mocks base method.
func (m *MockITreasureStore) GetTreasureConditionByItemId(arg0 context.Context, arg1 uint32) (*mysql.TreasureConditions, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTreasureConditionByItemId", arg0, arg1)
	ret0, _ := ret[0].(*mysql.TreasureConditions)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTreasureConditionByItemId indicates an expected call of GetTreasureConditionByItemId.
func (mr *MockITreasureStoreMockRecorder) GetTreasureConditionByItemId(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTreasureConditionByItemId", reflect.TypeOf((*MockITreasureStore)(nil).GetTreasureConditionByItemId), arg0, arg1)
}

// GetTreasureConditionTableName mocks base method.
func (m *MockITreasureStore) GetTreasureConditionTableName(arg0 uint32) string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTreasureConditionTableName", arg0)
	ret0, _ := ret[0].(string)
	return ret0
}

// GetTreasureConditionTableName indicates an expected call of GetTreasureConditionTableName.
func (mr *MockITreasureStoreMockRecorder) GetTreasureConditionTableName(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTreasureConditionTableName", reflect.TypeOf((*MockITreasureStore)(nil).GetTreasureConditionTableName), arg0)
}

// GetTreasurePrivilegeByUid mocks base method.
func (m *MockITreasureStore) GetTreasurePrivilegeByUid(arg0 context.Context, arg1 uint32) ([]*mysql.TreasurePrivilege, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTreasurePrivilegeByUid", arg0, arg1)
	ret0, _ := ret[0].([]*mysql.TreasurePrivilege)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTreasurePrivilegeByUid indicates an expected call of GetTreasurePrivilegeByUid.
func (mr *MockITreasureStoreMockRecorder) GetTreasurePrivilegeByUid(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTreasurePrivilegeByUid", reflect.TypeOf((*MockITreasureStore)(nil).GetTreasurePrivilegeByUid), arg0, arg1)
}

// GetTreasurePrivilegeByUidAndGiftId mocks base method.
func (m *MockITreasureStore) GetTreasurePrivilegeByUidAndGiftId(arg0 context.Context, arg1, arg2 uint32) (*mysql.TreasurePrivilege, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTreasurePrivilegeByUidAndGiftId", arg0, arg1, arg2)
	ret0, _ := ret[0].(*mysql.TreasurePrivilege)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTreasurePrivilegeByUidAndGiftId indicates an expected call of GetTreasurePrivilegeByUidAndGiftId.
func (mr *MockITreasureStoreMockRecorder) GetTreasurePrivilegeByUidAndGiftId(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTreasurePrivilegeByUidAndGiftId", reflect.TypeOf((*MockITreasureStore)(nil).GetTreasurePrivilegeByUidAndGiftId), arg0, arg1, arg2)
}

// GetTreasurePrivilegeMonthlyHistoryById mocks base method.
func (m *MockITreasureStore) GetTreasurePrivilegeMonthlyHistoryById(arg0 context.Context, arg1 string) (*mysql.TreasurePrivilegeMonthlyHistory, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTreasurePrivilegeMonthlyHistoryById", arg0, arg1)
	ret0, _ := ret[0].(*mysql.TreasurePrivilegeMonthlyHistory)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTreasurePrivilegeMonthlyHistoryById indicates an expected call of GetTreasurePrivilegeMonthlyHistoryById.
func (mr *MockITreasureStoreMockRecorder) GetTreasurePrivilegeMonthlyHistoryById(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTreasurePrivilegeMonthlyHistoryById", reflect.TypeOf((*MockITreasureStore)(nil).GetTreasurePrivilegeMonthlyHistoryById), arg0, arg1)
}

// GetTreasurePrivilegeMonthlyHistoryByUid mocks base method.
func (m *MockITreasureStore) GetTreasurePrivilegeMonthlyHistoryByUid(arg0 context.Context, arg1 uint32, arg2, arg3 time.Time) ([]*mysql.TreasurePrivilegeMonthlyHistory, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTreasurePrivilegeMonthlyHistoryByUid", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].([]*mysql.TreasurePrivilegeMonthlyHistory)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTreasurePrivilegeMonthlyHistoryByUid indicates an expected call of GetTreasurePrivilegeMonthlyHistoryByUid.
func (mr *MockITreasureStoreMockRecorder) GetTreasurePrivilegeMonthlyHistoryByUid(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTreasurePrivilegeMonthlyHistoryByUid", reflect.TypeOf((*MockITreasureStore)(nil).GetTreasurePrivilegeMonthlyHistoryByUid), arg0, arg1, arg2, arg3)
}

// GetTreasurePrivilegeTableName mocks base method.
func (m *MockITreasureStore) GetTreasurePrivilegeTableName(arg0 uint32) string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTreasurePrivilegeTableName", arg0)
	ret0, _ := ret[0].(string)
	return ret0
}

// GetTreasurePrivilegeTableName indicates an expected call of GetTreasurePrivilegeTableName.
func (mr *MockITreasureStoreMockRecorder) GetTreasurePrivilegeTableName(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTreasurePrivilegeTableName", reflect.TypeOf((*MockITreasureStore)(nil).GetTreasurePrivilegeTableName), arg0)
}

// GetUserAllTreasureCondition mocks base method.
func (m *MockITreasureStore) GetUserAllTreasureCondition(arg0 context.Context, arg1 uint32) ([]*mysql.UserTreasureCondition, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserAllTreasureCondition", arg0, arg1)
	ret0, _ := ret[0].([]*mysql.UserTreasureCondition)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserAllTreasureCondition indicates an expected call of GetUserAllTreasureCondition.
func (mr *MockITreasureStoreMockRecorder) GetUserAllTreasureCondition(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserAllTreasureCondition", reflect.TypeOf((*MockITreasureStore)(nil).GetUserAllTreasureCondition), arg0, arg1)
}

// GetUserTreasureCondition mocks base method.
func (m *MockITreasureStore) GetUserTreasureCondition(arg0 context.Context, arg1, arg2 uint32) (*mysql.UserTreasureCondition, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserTreasureCondition", arg0, arg1, arg2)
	ret0, _ := ret[0].(*mysql.UserTreasureCondition)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserTreasureCondition indicates an expected call of GetUserTreasureCondition.
func (mr *MockITreasureStoreMockRecorder) GetUserTreasureCondition(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserTreasureCondition", reflect.TypeOf((*MockITreasureStore)(nil).GetUserTreasureCondition), arg0, arg1, arg2)
}

// RollbackTx mocks base method.
func (m *MockITreasureStore) RollbackTx() error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RollbackTx")
	ret0, _ := ret[0].(error)
	return ret0
}

// RollbackTx indicates an expected call of RollbackTx.
func (mr *MockITreasureStoreMockRecorder) RollbackTx() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RollbackTx", reflect.TypeOf((*MockITreasureStore)(nil).RollbackTx))
}

// UpdateTreasureCondition mocks base method.
func (m *MockITreasureStore) UpdateTreasureCondition(arg0 context.Context, arg1 *mysql.TreasureConditions) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateTreasureCondition", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateTreasureCondition indicates an expected call of UpdateTreasureCondition.
func (mr *MockITreasureStoreMockRecorder) UpdateTreasureCondition(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateTreasureCondition", reflect.TypeOf((*MockITreasureStore)(nil).UpdateTreasureCondition), arg0, arg1)
}

// UpdateTreasurePrivilege mocks base method.
func (m *MockITreasureStore) UpdateTreasurePrivilege(arg0 context.Context, arg1 *mysql.TreasurePrivilege) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateTreasurePrivilege", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateTreasurePrivilege indicates an expected call of UpdateTreasurePrivilege.
func (mr *MockITreasureStoreMockRecorder) UpdateTreasurePrivilege(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateTreasurePrivilege", reflect.TypeOf((*MockITreasureStore)(nil).UpdateTreasurePrivilege), arg0, arg1)
}

// UpdateTreasurePrivilegeMonthlyHistory mocks base method.
func (m *MockITreasureStore) UpdateTreasurePrivilegeMonthlyHistory(arg0 context.Context, arg1 *mysql.TreasurePrivilegeMonthlyHistory) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateTreasurePrivilegeMonthlyHistory", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateTreasurePrivilegeMonthlyHistory indicates an expected call of UpdateTreasurePrivilegeMonthlyHistory.
func (mr *MockITreasureStoreMockRecorder) UpdateTreasurePrivilegeMonthlyHistory(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateTreasurePrivilegeMonthlyHistory", reflect.TypeOf((*MockITreasureStore)(nil).UpdateTreasurePrivilegeMonthlyHistory), arg0, arg1)
}

// MockIStore is a mock of IStore interface.
type MockIStore struct {
	ctrl     *gomock.Controller
	recorder *MockIStoreMockRecorder
}

// MockIStoreMockRecorder is the mock recorder for MockIStore.
type MockIStoreMockRecorder struct {
	mock *MockIStore
}

// NewMockIStore creates a new mock instance.
func NewMockIStore(ctrl *gomock.Controller) *MockIStore {
	mock := &MockIStore{ctrl: ctrl}
	mock.recorder = &MockIStoreMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIStore) EXPECT() *MockIStoreMockRecorder {
	return m.recorder
}

// AddCondition mocks base method.
func (m *MockIStore) AddCondition(arg0 context.Context, arg1 *presentprivilege.Privilege) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddCondition", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddCondition indicates an expected call of AddCondition.
func (mr *MockIStoreMockRecorder) AddCondition(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddCondition", reflect.TypeOf((*MockIStore)(nil).AddCondition), arg0, arg1)
}

// AddUserPrivilege mocks base method.
func (m *MockIStore) AddUserPrivilege(arg0 context.Context, arg1 []uint32, arg2 *presentprivilege.PresentPrivilege) (map[uint32]uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddUserPrivilege", arg0, arg1, arg2)
	ret0, _ := ret[0].(map[uint32]uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddUserPrivilege indicates an expected call of AddUserPrivilege.
func (mr *MockIStoreMockRecorder) AddUserPrivilege(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddUserPrivilege", reflect.TypeOf((*MockIStore)(nil).AddUserPrivilege), arg0, arg1, arg2)
}

// CreateTable mocks base method.
func (m *MockIStore) CreateTable(arg0 interface{}) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateTable", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateTable indicates an expected call of CreateTable.
func (mr *MockIStoreMockRecorder) CreateTable(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateTable", reflect.TypeOf((*MockIStore)(nil).CreateTable), arg0)
}

// DelConditions mocks base method.
func (m *MockIStore) DelConditions(arg0 context.Context, arg1 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelConditions", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// DelConditions indicates an expected call of DelConditions.
func (mr *MockIStoreMockRecorder) DelConditions(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelConditions", reflect.TypeOf((*MockIStore)(nil).DelConditions), arg0, arg1)
}

// GetConditionVal mocks base method.
func (m *MockIStore) GetConditionVal(arg0, arg1, arg2 uint32) (map[presentprivilege.ConditionType]int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetConditionVal", arg0, arg1, arg2)
	ret0, _ := ret[0].(map[presentprivilege.ConditionType]int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetConditionVal indicates an expected call of GetConditionVal.
func (mr *MockIStoreMockRecorder) GetConditionVal(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetConditionVal", reflect.TypeOf((*MockIStore)(nil).GetConditionVal), arg0, arg1, arg2)
}

// GetConditions mocks base method.
func (m *MockIStore) GetConditions(arg0 context.Context, arg1, arg2 uint32) ([]*presentprivilege.Privilege, uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetConditions", arg0, arg1, arg2)
	ret0, _ := ret[0].([]*presentprivilege.Privilege)
	ret1, _ := ret[1].(uint32)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetConditions indicates an expected call of GetConditions.
func (mr *MockIStoreMockRecorder) GetConditions(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetConditions", reflect.TypeOf((*MockIStore)(nil).GetConditions), arg0, arg1, arg2)
}

// GetPrivilegeList mocks base method.
func (m *MockIStore) GetPrivilegeList(arg0 context.Context, arg1 uint32) ([]*presentprivilege.PresentPrivilege, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPrivilegeList", arg0, arg1)
	ret0, _ := ret[0].([]*presentprivilege.PresentPrivilege)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPrivilegeList indicates an expected call of GetPrivilegeList.
func (mr *MockIStoreMockRecorder) GetPrivilegeList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPrivilegeList", reflect.TypeOf((*MockIStore)(nil).GetPrivilegeList), arg0, arg1)
}

// IncrConditionVal mocks base method.
func (m *MockIStore) IncrConditionVal(arg0 context.Context, arg1, arg2 uint32, arg3 presentprivilege.ConditionType, arg4 int64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IncrConditionVal", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(error)
	return ret0
}

// IncrConditionVal indicates an expected call of IncrConditionVal.
func (mr *MockIStoreMockRecorder) IncrConditionVal(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IncrConditionVal", reflect.TypeOf((*MockIStore)(nil).IncrConditionVal), arg0, arg1, arg2, arg3, arg4)
}
