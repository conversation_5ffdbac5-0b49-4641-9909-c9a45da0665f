package server

import (
	"context"
	"github.com/golang/mock/gomock"
	"github.com/opentracing/opentracing-go"
	channelClient "golang.52tt.com/clients/channel"
	"golang.52tt.com/clients/mocks/channel"
	superplayerdress "golang.52tt.com/clients/mocks/super-player-dress"
	superplayersvr "golang.52tt.com/clients/mocks/super-player-svr"
	spDressClient "golang.52tt.com/clients/super-player-dress"
	spClient "golang.52tt.com/clients/super-player-svr"
	"golang.52tt.com/pkg/config"
	protogrpc "golang.52tt.com/pkg/protocol/grpc"
	channelPb "golang.52tt.com/protocol/app/channel"
	pb "golang.52tt.com/protocol/app/super-player-dress-logic"
	channelsvr "golang.52tt.com/protocol/services/channelsvr"
	superdressPB "golang.52tt.com/protocol/services/superplayerdress"
	superplayerPB "golang.52tt.com/protocol/services/superplayersvr"
	"golang.52tt.com/services/super-player/super-player-dress-logic/conf"
	"testing"
	"time"
)

func TestNewSuperPlayerDressLogic(t *testing.T) {

	ctx := context.Background()
	ctx = context.WithValue(ctx, "configfile", "../super-player-dress-logic.json") // ctx.Value("configfile").(string)

	type args struct {
		ctx    context.Context
		config config.Configer
		tracer opentracing.Tracer
	}
	tests := []struct {
		name    string
		args    args
		want    *SuperPlayerDressLogic_
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "TestNewSuperPlayerDressLogic",
			args: args{
				ctx:    ctx,
				config: nil,
				tracer: nil,
			},
			want:    nil,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			NewSuperPlayerDressLogic(tt.args.ctx, tt.args.config, tt.args.tracer)
			/*			if (err != nil) != tt.wantErr {
							t.Errorf("NewSuperPlayerDressLogic() error = %v, wantErr %v", err, tt.wantErr)
							return
						}
						if !reflect.DeepEqual(got, tt.want) {
							t.Errorf("NewSuperPlayerDressLogic() got = %v, want %v", got, tt.want)
						}*/
		})
	}
}

func TestSuperPlayerDressLogic__GetChannelCurrDressId(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()
	ctx := context.Background()

	var uid, channelId uint32 = 1024, 1024
	channelCli := channel.NewMockIClient(ctl)
	spCli := superplayersvr.NewMockIClient(ctl)
	spDressCli := superplayerdress.NewMockIClient(ctl)
	ctx = protogrpc.WithServiceInfo(ctx, &protogrpc.ServiceInfo{UserID: uid})

	req := &pb.GetChannelCurrDressIdReq{
		BaseReq:   nil,
		ChannelId: channelId,
	}

	nowTs := time.Now()
	expireTs := nowTs.Unix()
	channelType := uint32(channelPb.ChannelType_USER_CHANNEL_TYPE)
	gomock.InOrder(
		channelCli.EXPECT().GetChannelSimpleInfo(ctx, uid, req.ChannelId).Return(&channelsvr.ChannelSimpleInfo{ChannelType: &channelType, CreaterUid: &uid}, nil),
		spCli.EXPECT().GetSuperPlayerInfo(ctx, uid).Return(&superplayerPB.GetSuperPlayerInfoResp{
			SuperPlayerInfo: &superplayerPB.SuperPlayerInfo{
				SuperPlayerUid:            uid,
				SuperPlayerValue:          0,
				SuperPlayerLevel:          0,
				BeginTimestamp:            0,
				ExpireTimestamp:           expireTs,
				YearMemberExpireTimestamp: 0,
			},
		}, nil),
		spDressCli.EXPECT().GetDressInUse(ctx, int64(uid), uint32(1), uint32(0)).Return(&superdressPB.GetDressInUseResp{}, nil),
	)

	type fields struct {
		sc         *conf.ServiceConfigT
		spCli      spClient.IClient
		spDressCli spDressClient.IClient
		channelCli channelClient.IClient
	}

	type args struct {
		ctx context.Context
		in  *pb.GetChannelCurrDressIdReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.GetChannelCurrDressIdResp
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "TestSuperPlayerDressLogic__GetChannelCurrDressId",
			fields: fields{
				sc:         nil,
				spCli:      spCli,
				spDressCli: spDressCli,
				channelCli: channelCli,
			},
			args: args{
				ctx: ctx,
				in:  req,
			},
			want:    nil,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &SuperPlayerDressLogic_{
				sc:         tt.fields.sc,
				spCli:      tt.fields.spCli,
				spDressCli: tt.fields.spDressCli,
				channelCli: tt.fields.channelCli,
			}
			s.GetChannelCurrDressId(tt.args.ctx, tt.args.in)
			/*			if (err != nil) != tt.wantErr {
							t.Errorf("GetChannelCurrDressId() error = %v, wantErr %v", err, tt.wantErr)
							return
						}
						if !reflect.DeepEqual(got, tt.want) {
							t.Errorf("GetChannelCurrDressId() got = %v, want %v", got, tt.want)
						}*/
		})
	}
}

func TestSuperPlayerDressLogic__GetDressConfigList(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()
	ctx := context.Background()

	var uid uint32 = 1024
	channelCli := channel.NewMockIClient(ctl)
	spCli := superplayersvr.NewMockIClient(ctl)
	spDressCli := superplayerdress.NewMockIClient(ctl)

	req := &pb.GetDressConfigListReq{
		BaseReq:   nil,
		DressType: 0,
		Offset:    0,
		Limit:     0,
	}
	resp := &superdressPB.GetDressConfigListResp{
		DressType: 0,
		DressList: []*superdressPB.Dress{{
			Id:    0,
			Level: 0,
		}},
		Offset: 0,
		Limit:  0,
		IsEnd:  false,
	}
	ctx = protogrpc.WithServiceInfo(ctx, &protogrpc.ServiceInfo{UserID: uid})
	gomock.InOrder(
		spDressCli.EXPECT().GetDressConfigList(ctx, req.GetDressType(), req.GetOffset(), req.GetLimit()).Return(resp, nil),
	)

	type fields struct {
		sc         *conf.ServiceConfigT
		spCli      spClient.IClient
		spDressCli spDressClient.IClient
		channelCli channelClient.IClient
	}
	type args struct {
		ctx context.Context
		in  *pb.GetDressConfigListReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.GetDressConfigListResp
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "TestSuperPlayerDressLogic__GetDressConfigList",
			fields: fields{
				sc:         nil,
				spCli:      spCli,
				spDressCli: spDressCli,
				channelCli: channelCli,
			},
			args: args{
				ctx: ctx,
				in:  req,
			},
			want:    nil,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &SuperPlayerDressLogic_{
				sc:         tt.fields.sc,
				spCli:      tt.fields.spCli,
				spDressCli: tt.fields.spDressCli,
				channelCli: tt.fields.channelCli,
			}
			s.GetDressConfigList(tt.args.ctx, tt.args.in)
			/*			if (err != nil) != tt.wantErr {
							t.Errorf("GetDressConfigList() error = %v, wantErr %v", err, tt.wantErr)
							return
						}
						if !reflect.DeepEqual(got, tt.want) {
							t.Errorf("GetDressConfigList() got = %v, want %v", got, tt.want)
						}*/
		})
	}
}

func TestSuperPlayerDressLogic__GetDressConfigMaxVersion(t *testing.T) {

	ctl := gomock.NewController(t)
	defer ctl.Finish()
	ctx := context.Background()

	var uid uint32 = 1024
	channelCli := channel.NewMockIClient(ctl)
	spCli := superplayersvr.NewMockIClient(ctl)
	spDressCli := superplayerdress.NewMockIClient(ctl)

	req := &pb.GetDressConfigMaxVersionReq{}
	ctx = protogrpc.WithServiceInfo(ctx, &protogrpc.ServiceInfo{UserID: uid})
	gomock.InOrder(
		spDressCli.EXPECT().GetDressConfigMaxVersion(ctx, req.GetMaxVersion(), req.GetDressType()).Return(&superdressPB.GetDressConfigMaxVersionResp{}, nil),
	)

	type fields struct {
		sc         *conf.ServiceConfigT
		spCli      spClient.IClient
		spDressCli spDressClient.IClient
		channelCli channelClient.IClient
	}
	type args struct {
		ctx context.Context
		in  *pb.GetDressConfigMaxVersionReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.GetDressConfigMaxVersionResp
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "TestSuperPlayerDressLogic__GetDressConfigMaxVersion",
			fields: fields{
				sc:         nil,
				spCli:      spCli,
				spDressCli: spDressCli,
				channelCli: channelCli,
			},
			args: args{
				ctx: ctx,
				in:  req,
			},
			want:    nil,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &SuperPlayerDressLogic_{
				sc:         tt.fields.sc,
				spCli:      tt.fields.spCli,
				spDressCli: tt.fields.spDressCli,
				channelCli: tt.fields.channelCli,
			}
			s.GetDressConfigMaxVersion(tt.args.ctx, tt.args.in)
			/*			if (err != nil) != tt.wantErr {
							t.Errorf("GetDressConfigMaxVersion() error = %v, wantErr %v", err, tt.wantErr)
							return
						}
						if !reflect.DeepEqual(got, tt.want) {
							t.Errorf("GetDressConfigMaxVersion() got = %v, want %v", got, tt.want)
						}*/
		})
	}
}

func TestSuperPlayerDressLogic__GetUserCurrChatBgDressIdList(t *testing.T) {

	ctl := gomock.NewController(t)
	defer ctl.Finish()
	ctx := context.Background()

	nowTs := time.Now()
	expireTs := nowTs.Unix()
	var uid uint32 = 1024
	channelCli := channel.NewMockIClient(ctl)
	spCli := superplayersvr.NewMockIClient(ctl)
	spDressCli := superplayerdress.NewMockIClient(ctl)

	req := &pb.GetUserCurrChatBgDressIdListReq{
		BaseReq: nil,
		Uid:     uid,
	}
	/*	resp := &superdressPB.GetDressConfigListResp{
		DressType:            0,
		DressList:            []*superdressPB.Dress{{
			Id:                   0,
			Level:                0,
		}},
		Offset:               0,
		Limit:                0,
		IsEnd:                false,
	}*/
	ctx = protogrpc.WithServiceInfo(ctx, &protogrpc.ServiceInfo{UserID: uid})
	gomock.InOrder(
		spCli.EXPECT().GetSuperPlayerInfo(ctx, uid).Return(&superplayerPB.GetSuperPlayerInfoResp{
			SuperPlayerInfo: &superplayerPB.SuperPlayerInfo{
				SuperPlayerUid:            uid,
				SuperPlayerValue:          0,
				SuperPlayerLevel:          0,
				BeginTimestamp:            0,
				ExpireTimestamp:           expireTs,
				YearMemberExpireTimestamp: 0,
			},
		}, nil),
		spDressCli.EXPECT().GetChatBgDressList(ctx, uid, uint32(0)).Return(&superdressPB.GetUserCurrChatBgDressIdListResp{
			CommonDressId: 0,
			DressList: []*superdressPB.ChatBgSpecial{{
				ToAccount: "x",
				DressId:   0,
			}},
		}, nil),
	)

	type fields struct {
		sc         *conf.ServiceConfigT
		spCli      spClient.IClient
		spDressCli spDressClient.IClient
		channelCli channelClient.IClient
	}
	type args struct {
		ctx context.Context
		in  *pb.GetUserCurrChatBgDressIdListReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.GetUserCurrChatBgDressIdListResp
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "TestSuperPlayerDressLogic__GetUserCurrChatBgDressIdList",
			fields: fields{
				sc:         nil,
				spCli:      spCli,
				spDressCli: spDressCli,
				channelCli: channelCli,
			},
			args: args{
				ctx: ctx,
				in:  req,
			},
			want:    nil,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &SuperPlayerDressLogic_{
				sc:         tt.fields.sc,
				spCli:      tt.fields.spCli,
				spDressCli: tt.fields.spDressCli,
				channelCli: tt.fields.channelCli,
			}
			s.GetUserCurrChatBgDressIdList(tt.args.ctx, tt.args.in)
			/*			if (err != nil) != tt.wantErr {
							t.Errorf("GetUserCurrChatBgDressIdList() error = %v, wantErr %v", err, tt.wantErr)
							return
						}
						if !reflect.DeepEqual(got, tt.want) {
							t.Errorf("GetUserCurrChatBgDressIdList() got = %v, want %v", got, tt.want)
						}*/
		})
	}
}

func TestSuperPlayerDressLogic__GetUserCurrChatBubbleDressId(t *testing.T) {

	ctl := gomock.NewController(t)
	defer ctl.Finish()
	ctx := context.Background()

	nowTs := time.Now()
	expireTs := nowTs.Unix()
	var uid uint32 = 1024
	channelCli := channel.NewMockIClient(ctl)
	spCli := superplayersvr.NewMockIClient(ctl)
	spDressCli := superplayerdress.NewMockIClient(ctl)

	req := &pb.GetUserCurrChatBubbleDressIdReq{
		BaseReq: nil,
		Uid:     uid,
	}
	ctx = protogrpc.WithServiceInfo(ctx, &protogrpc.ServiceInfo{UserID: uid})
	gomock.InOrder(
		spCli.EXPECT().GetSuperPlayerInfo(ctx, uid).Return(&superplayerPB.GetSuperPlayerInfoResp{
			SuperPlayerInfo: &superplayerPB.SuperPlayerInfo{
				SuperPlayerUid:            uid,
				SuperPlayerValue:          0,
				SuperPlayerLevel:          0,
				BeginTimestamp:            0,
				ExpireTimestamp:           expireTs,
				YearMemberExpireTimestamp: 0,
			},
		}, nil),
		spDressCli.EXPECT().GetDressInUse(ctx, int64(uid), uint32(4), uint32(0)).Return(&superdressPB.GetDressInUseResp{}, nil),
	)

	type fields struct {
		sc         *conf.ServiceConfigT
		spCli      spClient.IClient
		spDressCli spDressClient.IClient
		channelCli channelClient.IClient
	}
	type args struct {
		ctx context.Context
		in  *pb.GetUserCurrChatBubbleDressIdReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.GetUserCurrChatBubbleDressIdResp
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "TestSuperPlayerDressLogic__GetUserCurrChatBubbleDressId",
			fields: fields{
				sc:         nil,
				spCli:      spCli,
				spDressCli: spDressCli,
				channelCli: channelCli,
			},
			args: args{
				ctx: ctx,
				in:  req,
			},
			want:    nil,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &SuperPlayerDressLogic_{
				sc:         tt.fields.sc,
				spCli:      tt.fields.spCli,
				spDressCli: tt.fields.spDressCli,
				channelCli: tt.fields.channelCli,
			}
			s.GetUserCurrChatBubbleDressId(tt.args.ctx, tt.args.in)
			/*			if (err != nil) != tt.wantErr {
							t.Errorf("GetUserCurrChatBubbleDressId() error = %v, wantErr %v", err, tt.wantErr)
							return
						}
						if !reflect.DeepEqual(got, tt.want) {
							t.Errorf("GetUserCurrChatBubbleDressId() got = %v, want %v", got, tt.want)
						}*/
		})
	}
}

func TestSuperPlayerDressLogic__GetUserCurrSpecialConcernDressId(t *testing.T) {

	ctl := gomock.NewController(t)
	defer ctl.Finish()
	ctx := context.Background()

	nowTs := time.Now()
	expireTs := nowTs.Unix()
	var uid uint32 = 1024
	channelCli := channel.NewMockIClient(ctl)
	spCli := superplayersvr.NewMockIClient(ctl)
	spDressCli := superplayerdress.NewMockIClient(ctl)

	req := &pb.GetUserCurrSpecialConcernDressIdReq{
		BaseReq: nil,
		Uid:     uid,
	}
	ctx = protogrpc.WithServiceInfo(ctx, &protogrpc.ServiceInfo{UserID: uid})
	gomock.InOrder(
		spCli.EXPECT().GetSuperPlayerInfo(ctx, uid).Return(&superplayerPB.GetSuperPlayerInfoResp{
			SuperPlayerInfo: &superplayerPB.SuperPlayerInfo{
				SuperPlayerUid:            uid,
				SuperPlayerValue:          0,
				SuperPlayerLevel:          0,
				BeginTimestamp:            0,
				ExpireTimestamp:           expireTs,
				YearMemberExpireTimestamp: 0,
			},
		}, nil),
		spDressCli.EXPECT().GetDressInUse(ctx, int64(uid), uint32(2), uint32(0)).Return(&superdressPB.GetDressInUseResp{}, nil),
	)

	type fields struct {
		sc         *conf.ServiceConfigT
		spCli      spClient.IClient
		spDressCli spDressClient.IClient
		channelCli channelClient.IClient
	}
	type args struct {
		ctx context.Context
		in  *pb.GetUserCurrSpecialConcernDressIdReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.GetUserCurrSpecialConcernDressIdResp
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "TestSuperPlayerDressLogic__GetUserCurrSpecialConcernDressId",
			fields: fields{
				sc:         nil,
				spCli:      spCli,
				spDressCli: spDressCli,
				channelCli: channelCli,
			},
			args: args{
				ctx: ctx,
				in:  req,
			},
			want:    nil,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &SuperPlayerDressLogic_{
				sc:         tt.fields.sc,
				spCli:      tt.fields.spCli,
				spDressCli: tt.fields.spDressCli,
				channelCli: tt.fields.channelCli,
			}
			s.GetUserCurrSpecialConcernDressId(tt.args.ctx, tt.args.in)
			/*			if (err != nil) != tt.wantErr {
							t.Errorf("GetUserCurrSpecialConcernDressId() error = %v, wantErr %v", err, tt.wantErr)
							return
						}
						if !reflect.DeepEqual(got, tt.want) {
							t.Errorf("GetUserCurrSpecialConcernDressId() got = %v, want %v", got, tt.want)
						}*/
		})
	}
}

func TestSuperPlayerDressLogic__RemoveChannelCurrDressId(t *testing.T) {

	ctl := gomock.NewController(t)
	defer ctl.Finish()
	ctx := context.Background()

	nowTs := time.Now()
	expireTs := nowTs.Unix()
	var uid, role uint32 = 1024, 4
	channelCli := channel.NewMockIClient(ctl)
	spCli := superplayersvr.NewMockIClient(ctl)
	spDressCli := superplayerdress.NewMockIClient(ctl)

	req := &pb.RemoveChannelCurrDressIdReq{
		BaseReq:   nil,
		ChannelId: uid,
	}
	ctx = protogrpc.WithServiceInfo(ctx, &protogrpc.ServiceInfo{UserID: uid})
	channelType := uint32(channelPb.ChannelType_USER_CHANNEL_TYPE)

	adminList := []*channelsvr.ChannelAdmin{{
		Uid:       &uid,
		AdminRole: &role,
	}}
	gomock.InOrder(
		channelCli.EXPECT().GetChannelSimpleInfo(ctx, uid, req.ChannelId).Return(&channelsvr.ChannelSimpleInfo{ChannelType: &channelType, CreaterUid: &uid}, nil),
		channelCli.EXPECT().GetChannelAdmin(ctx, uid, req.ChannelId).Return(adminList, nil),
		spCli.EXPECT().GetSuperPlayerInfo(ctx, uid).Return(&superplayerPB.GetSuperPlayerInfoResp{
			SuperPlayerInfo: &superplayerPB.SuperPlayerInfo{
				SuperPlayerUid:            uid,
				SuperPlayerValue:          0,
				SuperPlayerLevel:          0,
				BeginTimestamp:            0,
				ExpireTimestamp:           expireTs,
				YearMemberExpireTimestamp: 0,
			},
		}, nil),
		spDressCli.EXPECT().SetDressInUse(ctx, int64(uid), uint32(0), uint32(1), uint32(0)).Return(nil, nil),
	)

	type fields struct {
		sc         *conf.ServiceConfigT
		spCli      spClient.IClient
		spDressCli spDressClient.IClient
		channelCli channelClient.IClient
	}
	type args struct {
		ctx context.Context
		in  *pb.RemoveChannelCurrDressIdReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.RemoveChannelCurrDressIdResp
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "TestSuperPlayerDressLogic__RemoveChannelCurrDressId",
			fields: fields{
				sc:         nil,
				spCli:      spCli,
				spDressCli: spDressCli,
				channelCli: channelCli,
			},
			args: args{
				ctx: ctx,
				in:  req,
			},
			want:    nil,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &SuperPlayerDressLogic_{
				sc:         tt.fields.sc,
				spCli:      tt.fields.spCli,
				spDressCli: tt.fields.spDressCli,
				channelCli: tt.fields.channelCli,
			}
			s.RemoveChannelCurrDressId(tt.args.ctx, tt.args.in)
			/*			if (err != nil) != tt.wantErr {
							t.Errorf("RemoveChannelCurrDressId() error = %v, wantErr %v", err, tt.wantErr)
							return
						}
						if !reflect.DeepEqual(got, tt.want) {
							t.Errorf("RemoveChannelCurrDressId() got = %v, want %v", got, tt.want)
						}*/
		})
	}
}

func TestSuperPlayerDressLogic__ShutDown(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()
	//ctx := context.Background()
	channelCli := channel.NewMockIClient(ctl)
	spCli := superplayersvr.NewMockIClient(ctl)
	spDressCli := superplayerdress.NewMockIClient(ctl)

	gomock.InOrder(
		spDressCli.EXPECT().Close().Return(),
		spCli.EXPECT().Close().Return(),
		channelCli.EXPECT().Close().Return(),
	)

	type fields struct {
		sc         *conf.ServiceConfigT
		spCli      spClient.IClient
		spDressCli spDressClient.IClient
		channelCli channelClient.IClient
	}
	tests := []struct {
		name   string
		fields fields
	}{
		// TODO: Add test cases.
		{
			name: "TestSuperPlayerDressLogic__ShutDown",
			fields: fields{
				sc:         nil,
				spCli:      spCli,
				spDressCli: spDressCli,
				channelCli: channelCli,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &SuperPlayerDressLogic_{
				sc:         tt.fields.sc,
				spCli:      tt.fields.spCli,
				spDressCli: tt.fields.spDressCli,
				channelCli: tt.fields.channelCli,
			}
			s.ShutDown()
		})
	}
}

func TestSuperPlayerDressLogic__checkIsUserChannelType(t *testing.T) {

	ctl := gomock.NewController(t)
	defer ctl.Finish()
	ctx := context.Background()

	//nowTs := time.Now()
	//expireTs := nowTs.Unix()
	var uid uint32 = 1024
	channelCli := channel.NewMockIClient(ctl)
	spCli := superplayersvr.NewMockIClient(ctl)
	spDressCli := superplayerdress.NewMockIClient(ctl)

	channelType := uint32(channelPb.ChannelType_USER_CHANNEL_TYPE)
	ctx = protogrpc.WithServiceInfo(ctx, &protogrpc.ServiceInfo{UserID: uid})
	gomock.InOrder(
		channelCli.EXPECT().GetChannelSimpleInfo(ctx, uid, uid).Return(&channelsvr.ChannelSimpleInfo{ChannelType: &channelType, CreaterUid: &uid}, nil),
	)

	type fields struct {
		sc         *conf.ServiceConfigT
		spCli      spClient.IClient
		spDressCli spDressClient.IClient
		channelCli channelClient.IClient
	}
	type args struct {
		uid       uint32
		channelId uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    uint32
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "TestSuperPlayerDressLogic__checkIsUserChannelType",
			fields: fields{
				sc:         nil,
				spCli:      spCli,
				spDressCli: spDressCli,
				channelCli: channelCli,
			},
			args: args{
				uid:       uid,
				channelId: uid,
			},
			want:    0,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &SuperPlayerDressLogic_{
				sc:         tt.fields.sc,
				spCli:      tt.fields.spCli,
				spDressCli: tt.fields.spDressCli,
				channelCli: tt.fields.channelCli,
			}
			s.checkIsUserChannelType(ctx, tt.args.uid, tt.args.channelId)
			/*			if (err != nil) != tt.wantErr {
							t.Errorf("checkIsUserChannelType() error = %v, wantErr %v", err, tt.wantErr)
							return
						}
						if got != tt.want {
							t.Errorf("checkIsUserChannelType() got = %v, want %v", got, tt.want)
						}*/
		})
	}
}
