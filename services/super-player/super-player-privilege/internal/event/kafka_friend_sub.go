package event

import (
	"context"
	"time"

	"gitlab.ttyuyin.com/avengers/tyr/compatible/proto"
	"gitlab.ttyuyin.com/tt-infra/middleware/kafka"
	"gitlab.ttyuyin.com/tt-infra/middleware/kafka/subscriber"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/protocol/services/minToolkit/kafka/pb/kafkaonline"
	"golang.52tt.com/services/super-player/super-player-privilege/internal/manager"
)

const (
	topicTypeFriend = "friendupdate"
)

type KafkaFriendSubscriber struct {
	kafkaSub subscriber.Subscriber
	mgr      *manager.SpecialConcernManager
}

func NewFriendKafkaSubscriber(ctx context.Context, clientId, groupId string, topics, brokers []string, mgr *manager.SpecialConcernManager) (*KafkaFriendSubscriber, error) {

	conf := kafka.DefaultConfig()
	conf.ClientID = clientId
	conf.Consumer.Offsets.Initial = kafka.OffsetNewest
	conf.Consumer.Return.Errors = true

	kafkaSub, err := kafka.NewSubscriber(brokers, conf, subscriber.WithMaxRetryTimes(3))
	if err != nil {
		log.ErrorWithCtx(ctx, "Failed to create kafka-subscriber %+v", err)
		return nil, err
	}

	sub := &KafkaFriendSubscriber{
		kafkaSub: kafkaSub,
		mgr:      mgr,
	}

	err = kafkaSub.SubscribeContext(groupId, topics, subscriber.ProcessorContextFunc(sub.handlerEvent))
	if err != nil {
		panic(err)
	}
	return sub, nil
}

func (s *KafkaFriendSubscriber) Close() error {
	return s.kafkaSub.Stop()
}

func (s *KafkaFriendSubscriber) handlerEvent(ctx context.Context, msg *subscriber.ConsumerMessage) (error, bool) {
	switch msg.Topic {
	case topicTypeFriend:
		return s.handlerFriendEvent(ctx, msg)
	}
	return nil, false
}

func (s *KafkaFriendSubscriber) handlerFriendEvent(ctx context.Context, msg *subscriber.ConsumerMessage) (error, bool) {
	friendEvent := kafkaonline.FrinedUpdate{}
	err := proto.Unmarshal(msg.Value, &friendEvent)
	if err != nil {
		log.ErrorWithCtx(ctx, "Failed to proto.Unmarshal %+v", err)
		return err, false
	}
	log.Infof("handlerEvent topic(%s) partition(%d) offset(%d),event:%s", msg.Topic, msg.Partition, msg.Offset, friendEvent.String())
	if !friendEvent.IsAdd {

		// 拿特别关心
		list, err := s.mgr.GetUserSpecialConcern(ctx, friendEvent.Uid, time.Now())
		if err != nil {
			log.ErrorWithCtx(ctx, "Failed to HandleFriendUpdate GetUserBeSpecialConcern fail uid: %v , err : %v", friendEvent.Uid, err)
			return err, true
		}

		for _, item := range list {
			if item == friendEvent.TargetUid {
				fErr := s.mgr.HandleFriendUpdate(ctx, friendEvent.Uid, friendEvent.TargetUid)
				if fErr != nil {
					log.ErrorWithCtx(ctx, "Failed to HandleFriendUpdate uid %d err %+v", friendEvent.Uid, err)
					return err, true
				}
			}
		}

		//res, err = s.mgr.SpCli.GetSuperPlayerInfo(ctx, friendEvent.TargetUid)
		//if err != nil {
		//	log.ErrorWithCtx(ctx,"Failed to GetSuperPlayerInfo %+v", err)
		//	return err, false
		//}
		//if res.GetSuperPlayerInfo().GetExpireTimestamp() > time.Now().Unix() {
		//	fErr := s.mgr.HandleFriendUpdate(ctx, friendEvent.TargetUid, friendEvent.Uid)
		//	if fErr != nil {
		//		log.ErrorWithCtx(ctx,"Failed to HandleFriendUpdate %+v", err)
		//		return err, false
		//	}
		//}

	}

	return nil, false
}
