package mgr

import (
	"context"
	"errors"
	"fmt"
	"time"

	"gitlab.ttyuyin.com/avengers/tyr/compatible/proto"
	"gitlab.ttyuyin.com/tt-infra/tyr/log"
	protogrpc "golang.52tt.com/pkg/protocol/grpc"
	"golang.52tt.com/protocol/app/superplayerlogic"
	pb "golang.52tt.com/protocol/services/superplayersvr"
	"golang.52tt.com/services/super-player/super-player-svr/internal/old-version/conf"
	"golang.52tt.com/services/super-player/super-player-svr/internal/pkg/utils"
)

// ValueChangeRecord 成长值和会员时间变化等流水表
// 这个表暂时不做迁移了，因为之前写得不太好，mission服务直接会写那张表，原来的冗余字段不做处理
type ValueChangeRecord struct {
	OrderID       string    `db:"order_id" json:"order_id"`
	ParentOrder   string    `db:"parent_order" json:"parent_order"`
	Reason        string    `db:"reason" json:"reason"`
	Uid           uint32    `db:"uid" json:"uid"`
	OldValue      int64     `db:"old_value" json:"old_value"`
	NewValue      int64     `db:"new_value" json:"new_value"`
	IncrValue     int64     `db:"incr_value" json:"incr_value"`
	ServerTime    time.Time `db:"server_time" json:"server_time"`
	CreateTime    time.Time `db:"create_time" json:"create_time"`
	OldBeginTime  time.Time `db:"old_begin_time" json:"old_begin_time"`   // 冗余字段，切换后废弃（根据类型决定）
	OldExpireTime time.Time `db:"old_expire_time" json:"old_expire_time"` // 冗余字段，切换后废弃（根据类型决定）
	NewBeginTime  time.Time `db:"new_begin_time" json:"new_begin_time"`   // 冗余字段，切换后废弃（根据类型决定）
	NewExpireTime time.Time `db:"new_expire_time" json:"new_expire_time"` // 冗余字段，切换后废弃（根据类型决定）
	IncrDays      int64     `db:"incr_months" json:"incr_months"`         // 冗余字段，切换后废弃（根据类型决定）
	PackageType   uint32    `db:"package_type" json:"package_type"`       // 套餐类型，冗余字段，切换后废弃（根据类型决定）
}

// LevelPopupInfo 权限升级弹窗信息
type LevelPopupInfo struct {
	IsNeedPush bool
	BeforeLv   uint32
	AfterLv    uint32
	PopUpMsg   string
}

// SettlementOrderScore 结算订单成长值结构体
type SettlementOrderScore struct {
	Uid          uint32 // 用户uid
	OrderId      string // 订单ID
	PrentOrderId string // 父订单ID
	IncrValue    int64  // 增加值
	IsExpire     bool   // 是否过期核减
}

// AwardValue 获取会员积分结构体
// 该结构用于其他模块调用使用
type AwardValue struct {
	OrderId     string
	ParentOrder string
	Reason      string
	Uid         uint32
	IncrValue   int64
	ServerTime  time.Time
}

// GetLvUpPopPuInfo 获取权限升级弹窗信息
func (m *Manager) GetLvUpPopPuInfo(ctx context.Context, uid uint32) (*LevelPopupInfo, error) {
	result := &LevelPopupInfo{}
	bLv, aLv, isExist, err := m.Cache.GetLvUpPopPuInfo(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetLvUpPopPuInfo failed uid:%v err:%v", uid, err)
		return result, err
	}
	// 需要弹窗
	if isExist {
		result.BeforeLv = bLv
		result.AfterLv = aLv
	}
	return result, nil
}

// DelLvUpPopPuInfo 删除权限升级弹窗信息
func (m *Manager) DelLvUpPopPuInfo(ctx context.Context, uid uint32) error {
	return m.Cache.DelLvUpPopPuInfo(ctx, uid)
}

// SetValueChangeTimestamp 设置用户值变化时间戳
func (m *Manager) SetValueChangeTimestamp(ctx context.Context, uid, timestamp uint32) error {
	return m.Cache.SetValueChangeTimestamp(ctx, uid, timestamp)
}

// GetValueRecord 获取用户值变化记录
func (m *Manager) GetValueRecord(ctx context.Context, uid uint32, off, count int64) (value int64, lastChangeTs int64, list []*pb.SuperPlayerValueRecord, err error) {
	//--story=1123253 --user=马富达 优化会员成长值记录占用redis空间问题 https://www.tapd.cn/32571206/s/3112063
	// 优化会员成长值记录占用redis空间问题，不再使用redis记录详细的成长值变化记录
	//value, list = m.Cache.GetValueDetail(ctx, uid, off, count)
	end := time.Now()
	start := time.Date(end.Year()-1, end.Month(), end.Day(), end.Hour(), end.Minute(), end.Second(), 0, time.Local)

	// 使用DB直接查询成长值详情
	records, err := m.Store.BatchGetUserValueChangeRecord(ctx, start, end, uid, uint32(off), uint32(count))
	if err != nil {
		log.ErrorWithCtx(ctx, "GetValueRecord BatchGetUserValueChangeRecord failed, uid:%+v, err:%+v", uid, err)
		return 0, 0, nil, err
	}
	// 组装返回结构
	list = make([]*pb.SuperPlayerValueRecord, 0)
	for _, record := range records {
		if record == nil || record.IncrValue == 0 {
			continue
		}
		list = append(list, &pb.SuperPlayerValueRecord{
			SuperPlayerUid: record.Uid,
			TimeStamp:      record.CreateTime.Unix(),
			Reason:         record.Reason,
			IncrValue:      record.IncrValue,
		})
	}

	// 获取当天成长值获取详情
	value, err = m.Store.GetTodayUserValueChangeSum(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetValueRecord GetTodayUserValueChangeSum failed, uid:%+v, err:%+v", uid, err)
		return 0, 0, nil, err
	}

	lastChangeTs, _ = m.Cache.GetValueChangeTimestamp(ctx, uid)
	return value, lastChangeTs, list, nil
}

// SettlementUserValue 结算用户会员值
func (m *Manager) SettlementUserValue(ctx context.Context, awardValueInfo *AwardValue, isExpire bool, updateMemberShipCache bool) (*ValueChangeRecord, *MembershipInfo, error) {
	log.DebugWithCtx(ctx, "SettlementUserValue start awardValueInfo:%+v", awardValueInfo)
	record, newInfo, err := m.Store.SettlementUserValue(ctx, awardValueInfo)
	if err != nil {
		log.ErrorWithCtx(ctx, "SettlementUserValue failed err:%v", err)
		return record, newInfo, err
	}
	if record == nil { // 如果为空，说明订单已经操作过了
		log.DebugWithCtx(ctx, "SettlementUserValue no change awardValueInfo:%+v", awardValueInfo)
		return nil, nil, nil
	}

	if updateMemberShipCache { // 如果是开通，外层更新
		if newInfo != nil { // 防空指针问题
			// 更新缓存
			err = m.Cache.SetMembershipInfo(ctx, m.convertAndFillMembership(&MembershipInfo{
				Uid:                      record.Uid,
				Value:                    record.NewValue,
				BeginTime:                newInfo.BeginTime,
				ExpireTime:               newInfo.ExpireTime,
				SvipBeginTime:            newInfo.SvipBeginTime,
				SvipExpireTime:           newInfo.SvipExpireTime,
				YearMemberExpireTime:     newInfo.YearMemberExpireTime,
				SvipYearMemberExpireTime: newInfo.SvipYearMemberExpireTime,
				IsSignedVip:              newInfo.IsSignedVip,
				IsSignedSvip:             newInfo.IsSignedSvip,
				CreateTime:               newInfo.CreateTime,
			}))
			if err != nil { // 缓存更新失败不返回错误，因为数据已经入库成功
				log.ErrorWithCtx(ctx, "SettlementUserValue SetMembershipInfo failed err:%v", err)
			}
		}
	}
	// --story=1123253 --user=马富达 优化会员成长值记录占用redis空间问题 https://www.tapd.cn/32571206/s/3112063
	// 优化会员成长值记录占用redis空间问题，不再使用redis记录详细的成长值变化记录
	//err = m.Cache.RecordValueDetail(ctx, &pb.SuperPlayerValueRecord{
	//	SuperPlayerUid: awardValueInfo.Uid,
	//	TimeStamp:      time.Now().Unix(),
	//	Reason:         awardValueInfo.Reason,
	//	IncrValue:      awardValueInfo.IncrValue,
	//})

	// 设置升级提醒标志
	newSuperLv := m.DyConfig.GetLevelByValue(record.NewValue)
	oldSuperLv := m.DyConfig.GetLevelByValue(record.OldValue)
	if oldSuperLv != 0 && newSuperLv > oldSuperLv {
		err = m.Cache.SetLvUpPopPuInfo(ctx, awardValueInfo.Uid, oldSuperLv, newSuperLv)
		if err != nil {
			log.ErrorWithCtx(ctx, "AddSuperPlayerValue SetLvUpPopPuInfo failed awardValueInfo:%+v err:%v", awardValueInfo, err)
		}
	}

	if !isExpire {
		newCtx := protogrpc.NewContextWithInfo(ctx)
		go m.sendMembershipChangeNotice(newCtx, newInfo)
	}

	log.DebugWithCtx(ctx, "SettlementUserValue success awardValueInfo:%+v", awardValueInfo)
	return record, newInfo, nil
}

// sendMembershipChangeNotice 发送会员变化通知
func (m *Manager) sendMembershipChangeNotice(ctx context.Context, info *MembershipInfo) {
	now := time.Now()
	// 这里获取的时候，不用考虑签约关系，对于客户端来说，签约状态和已开通一致
	_, _, signType, advStatus := utils.GetStatusWithPrivilege(ctx, m.convertAndFillMembership(info), []*pb.SuperPlayerContractInfo{}, now)
	expire := info.ExpireTime.Unix()
	if info.SvipExpireTime.After(info.ExpireTime) && info.SvipExpireTime.After(now) {
		expire = info.SvipExpireTime.Unix()
	}
	if expire < 0 {
		expire = 0
	}
	yearExpire := info.YearMemberExpireTime.Unix()
	if info.SvipYearMemberExpireTime.After(info.YearMemberExpireTime) && info.SvipYearMemberExpireTime.After(now) {
		yearExpire = info.SvipYearMemberExpireTime.Unix()
	}
	if yearExpire < 0 {
		yearExpire = 0
	}
	var lastExpiredType superplayerlogic.SuperPlayerExpiredType
	if info.ExpireTime.Before(now) && info.SvipExpireTime.Before(now) {
		if info.ExpireTime.After(info.SvipExpireTime) {
			lastExpiredType = superplayerlogic.SuperPlayerExpiredType_SUPER_PLAYER_EXPIRED_TYPE_SUPER_PLAYER
		} else {
			lastExpiredType = superplayerlogic.SuperPlayerExpiredType_SUPER_PLAYER_EXPIRED_TYPE_SVIP
		}
	}
	pushInfo := &superplayerlogic.SuperPlayerInfo{
		SuperPlayerUid:   info.Uid,
		SuperPlayerValue: info.Value,
		SuperPlayerLevel: m.DyConfig.GetLevelByValue(info.Value),
		BeginTimestamp:   info.BeginTime.Unix(),
		ExpireTimestamp:  expire,
		//SuperPlayerAccount:        "", 这个字段旧代码也没有做赋值，不知道干嘛的
		YearMemberExpireTimestamp: yearExpire,
		SuperPlayerType:           superplayerlogic.SuperPlayerVipType(signType),
		IsSvip:                    info.SvipExpireTime.After(time.Now()),
		EntryAdvStatus:            superplayerlogic.EntryAdvStatus(advStatus),
		ServerTime:                now.Unix(),
		LastExpiredType:           lastExpiredType,
	}
	subMsg, _ := proto.Marshal(pushInfo)
	err := m.Client.PushUserMsg(ctx, subMsg, superplayerlogic.SuperPlayerInfoPushMsg_ENUM_SUPER_PLAYER_INFO_CHANGE, info.Uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "sendMembershipChangeNotice notifyInfoChange fail info:%v err:%v", info, err)
	}
	log.DebugWithCtx(ctx, "sendMembershipChangeNotice pushInfo:%+v", pushInfo)
}

// SettlementOrderScore 结算订单成长值
func (m *Manager) SettlementOrderScore(ctx context.Context, order *SettlementOrderScore, updateCache bool) (*ValueChangeRecord, *MembershipInfo, error) {
	nowTs := time.Now()

	addValOrder := &AwardValue{
		OrderId:     order.OrderId,
		ParentOrder: order.PrentOrderId,
		Reason:      "套餐成长值",
		Uid:         order.Uid,
		IncrValue:   order.IncrValue,
		ServerTime:  nowTs,
	}

	// 判断完成锁
	isFinish, err := m.Cache.ValidHasSettlementOrderScoreLock(ctx, order.OrderId)
	if err != nil {
		log.ErrorWithCtx(ctx, "SettlementOrderScore ValidHasSettlementOrderScoreLock err:%v", err)
		return nil, nil, err
	}
	if isFinish { // 说明已经加过了
		log.DebugWithCtx(ctx, "SettlementOrderScore addValueOrderID:%v has been processed", order.OrderId)
		return nil, nil, nil
	}

	// 抢操作锁
	if isLock, err := m.Cache.SetDoSettlementOrderScoreLock(ctx, order.OrderId, nowTs); !isLock || err != nil {
		log.ErrorWithCtx(ctx, "SettlementOrderScore addValueOrderID:%v is locked or get lock failed, err:%v", order.OrderId, err)
		return nil, nil, nil
	}
	defer m.Cache.UnLockDoSettlementOrderScoreLock(ctx, order.OrderId, nowTs) // 释放锁

	// 抢到锁了，操作
	record, newInfo, err := m.SettlementUserValue(ctx, addValOrder, order.IsExpire, updateCache)
	if err != nil {
		log.ErrorWithCtx(ctx, "SettlementOrderScore AddSuperPlayerValue err:%v", err)
		return nil, nil, err
	}

	// 更新完成锁
	err = m.Cache.SetSettlementOrderScoreLock(ctx, order.OrderId, nowTs)
	if err != nil { // 如果失败了，就不管了，顶多是给用户加多一个月积分，订单有主键冲突，没有实际价值
		log.ErrorWithCtx(ctx, "SettlementOrderScore SetSettlementOrderScoreLock err:%v", err)
	}

	// 清除过期提醒缓存
	newCtx := protogrpc.NewContextWithInfo(ctx)
	go func() {
		err := m.Cache.DelUserValueReminder(newCtx, order.Uid)
		if err != nil {
			log.ErrorWithCtx(newCtx, "SettlementOrderScore DelUserValueReminder err:%v", err)
		}
	}()

	log.InfoWithCtx(ctx, "SettlementOrderScore addValOrder:%+v", addValOrder)
	return record, newInfo, nil
}

// LockAutoReduce 获取自动减少会员值锁
func (m *Manager) LockAutoReduce(ctx context.Context) (bool, error) {
	return m.Cache.LockAutoReduce(ctx, time.Minute*30)
}

func (m *Manager) UnlockAutoReduce(ctx context.Context) {
	m.Cache.UnlockAutoReduce(ctx)
}

// ValidAutoReduceValueFinish 判断自动减少会员值是否已经完成
func (m *Manager) ValidAutoReduceValueFinish(ctx context.Context, ts time.Time) (bool, error) {
	return m.Cache.ValidAutoReduceValueFinish(ctx, ts)
}

// LockAutoReduceValueFinish 锁定自动减少会员值完成
func (m *Manager) LockAutoReduceValueFinish(ctx context.Context, ts time.Time) (bool, error) {
	return m.Cache.LockAutoReduceValueFinish(ctx, ts)
}

const ThreeDays = time.Hour * 24 * 3

// DoAutoReduceValue 按用户执行自动减少成长值
func (m *Manager) DoAutoReduceValue(upCtx context.Context, info *pb.SuperPlayerInfo, ts time.Time, isSignUser bool) error {
	ctx, cancel := context.WithTimeout(upCtx, time.Second*5)
	defer cancel()
	log.DebugWithCtx(ctx, "DoAutoReduceValue info:%+v", info)

	expireTime := info.ExpireTimestamp
	if info.SvipExpireTimestamp > expireTime {
		expireTime = info.SvipExpireTimestamp
	}
	// 如果是签约用户统一延迟一定时间后再执行
	if isSignUser {
		expireTime = expireTime + m.DyConfig.GetAllSignUserDelayExpireHour()*3600
	}
	if ts.Unix() < expireTime || ts.Unix() < info.SvipExpireTimestamp {
		return errors.New("not expire")
	}
	if info.SuperPlayerValue == 0 {
		return errors.New("val==0")
	}

	//执行自动扣减
	orderId := genAutoReduceOrderID(info.SuperPlayerUid, ts, m.DyConfig.IsTestReduceValue())

	needReduceValue := conf.GetReduceValue(info.SuperPlayerValue)
	if needReduceValue > info.SuperPlayerValue {
		needReduceValue = info.SuperPlayerValue
	}
	reduceValue := needReduceValue * -1
	_, newMembership, err := m.SettlementUserValue(ctx, &AwardValue{
		OrderId:     orderId,
		Uid:         info.SuperPlayerUid,
		IncrValue:   reduceValue,
		Reason:      "超级玩家过期，成长值自动下降",
		ServerTime:  ts,
		ParentOrder: orderId,
	}, true, true)
	if err != nil {
		log.ErrorWithCtx(ctx, "doAutoReduceValue fail uid:%v err:%v", info.SuperPlayerUid, err)
		return err
	}
	if newMembership == nil {
		log.ErrorWithCtx(ctx, "doAutoReduceValue fail uid:%v newMembership is nil", info.SuperPlayerUid)
		return nil
	}

	//测试环境刷下，不然就等定时拉取
	if m.DyConfig.IsTestReduceValue() {
		subMsg, _ := proto.Marshal(&superplayerlogic.SuperPlayerInfo{
			SuperPlayerUid:            newMembership.Uid,
			SuperPlayerValue:          newMembership.Value,
			SuperPlayerLevel:          conf.GetLevelByValue(newMembership.Value),
			BeginTimestamp:            newMembership.BeginTime.Unix(),
			ExpireTimestamp:           newMembership.ExpireTime.Unix(),
			YearMemberExpireTimestamp: newMembership.YearMemberExpireTime.Unix(),
		})
		err = m.Client.PushUserMsg(ctx, subMsg, superplayerlogic.SuperPlayerInfoPushMsg_ENUM_SUPER_PLAYER_INFO_CHANGE, newMembership.Uid)
		if err != nil {
			return err
		}
	}

	// 记录需要做提醒的成长值
	newCtx := protogrpc.NewContextWithInfo(ctx)
	maxLeftDays := m.DyRenewalReminder.GetMaxAfterDays()
	expire := time.Unix(expireTime, 0)
	lastAddDay := time.Date(expire.Year(), expire.Month(), expire.Day()+int(maxLeftDays), 0, 0, 0, 0, time.Local)
	if lastAddDay.After(time.Now()) {
		go func() {
			err := m.Cache.SetUserValueReminder(newCtx, info.SuperPlayerUid, uint32(needReduceValue), ThreeDays)
			if err != nil {
				log.ErrorWithCtx(newCtx, "DoAutoReduceValue sendMembershipChangeNotice fail err:%v", err)
			}
		}()
	}

	return nil
}

// 每天自动减少会员值订单
func genAutoReduceOrderID(uid uint32, ts time.Time, testMode bool) string {
	if testMode {
		return fmt.Sprintf("auto_reduce_day_%v_%v", uid, utils.GetHourStr(ts))
	}
	return fmt.Sprintf("auto_reduce_day_%v_%v", uid, utils.GetDayStr(ts))
}

// GetAutoAddScoreTimerIndex 获取自动增加用户成长值索引
func (m *Manager) GetAutoAddScoreTimerIndex(ctx context.Context, val int64) (int64, error) {
	index, err := m.Cache.GetAutoAddScoreTimerIndex(ctx, val)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAutoAddScoreTimerIndex err:%v", err)
		return 0, err
	}
	return index, nil
}

// LockAutoAddScoreTimerIndex 锁定自动增加用户成长值索引
func (m *Manager) LockAutoAddScoreTimerIndex(ctx context.Context, index int64, expire time.Duration) (bool, error) {
	isLock, err := m.Cache.LockAutoAddScoreTimerIndex(ctx, index, expire)
	if err != nil {
		log.ErrorWithCtx(ctx, "LockAutoAddScoreTimerIndex err:%v", err)
		return false, err
	}
	return isLock, nil
}

// LockAutoAddScoreTimerIndexFinish 锁定自动增加用户成长值索引已经完成
func (m *Manager) LockAutoAddScoreTimerIndexFinish(ctx context.Context, index int64, finishTime time.Time) error {
	return m.Cache.LockAutoAddScoreTimerIndexFinish(ctx, index, finishTime)
}

// ValidAutoAddScoreTimerIndexFinish 判断自动增加用户成长值索引是否已经完成
func (m *Manager) ValidAutoAddScoreTimerIndexFinish(ctx context.Context, index int64, finishTime time.Time) (bool, error) {
	isFinish, err := m.Cache.ValidAutoAddScoreTimerIndexFinish(ctx, index, finishTime)
	if err != nil {
		log.ErrorWithCtx(ctx, "ValidAutoAddScoreTimerIndexFinish err:%v", err)
		return false, err
	}
	return isFinish, nil
}
