package internal

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"golang.52tt.com/pkg/bylink"
	"strings"
	"time"

	"gitlab.ttyuyin.com/avengers/tyr/core/log"
	"gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/mysql"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/protocol/grpc"
	"golang.52tt.com/protocol/common/status"
	pb "golang.52tt.com/protocol/services/superplayersvr"
	membershipMgrStruct "golang.52tt.com/services/super-player/super-player-svr/internal/membership/mgr"
	orderMgrStruct "golang.52tt.com/services/super-player/super-player-svr/internal/order-and-redemption/mgr"
	"golang.52tt.com/services/super-player/super-player-svr/internal/pkg/utils"
	"golang.52tt.com/services/super-player/super-player-svr/internal/sale-package/mgr"
)

// GetSuperPlayerPayRecordList 获取超级玩家支付记录
func (s *Server) GetSuperPlayerPayRecordList(ctx context.Context, req *pb.GetSuperPlayerPayRecordListReq) (*pb.GetSuperPlayerPayRecordListResp, error) {
	//return s.oldSvr.GetSuperPlayerPayRecordList(ctx, req)
	resp := &pb.GetSuperPlayerPayRecordListResp{}
	log.DebugWithCtx(ctx, "GetSuperPlayerPayRecordList req:%v", req)

	recordList, err := s.orderMgr.GetPayRecordList(ctx, req.GetSuperPlayerUid(), int(req.GetMarketId()), req.Off, req.Count)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetSuperPlayerPayRecordList failed, err: %v", err)
		return resp, protocol.NewExactServerError(nil, status.ErrRepositoryFailed, "数据异常，请联系相关人员解决！")
	}

	resp.PayRecordList = recordList
	log.DebugWithCtx(ctx, "GetSuperPlayerPayRecordList success, resp:%v", resp)
	return resp, nil
}

// GetDeviceBuyRecord 设备是否购买过
func (s *Server) GetDeviceBuyRecord(ctx context.Context, req *pb.GetDeviceBuyRecordReq) (*pb.GetDeviceBuyRecordResp, error) {
	//return s.oldSvr.GetDeviceBuyRecord(ctx, req)
	resp := &pb.GetDeviceBuyRecordResp{}
	log.DebugWithCtx(ctx, "GetDeviceBuyRecord req:%v", req)

	deviceRecord, err := s.orderMgr.GetDeviceRecord(ctx, req.DeviceId)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetDeviceBuyRecord failed, err: %v", err)
	}

	resp.LastBuyOrderId = deviceRecord
	log.DebugWithCtx(ctx, "GetDeviceBuyRecord success, resp:%v", resp)
	return resp, nil
}

func (s *Server) GetSuperPlayerContract(ctx context.Context, req *pb.GetSuperPlayerContractReq) (*pb.GetSuperPlayerContractResp, error) {
	//return s.oldSvr.GetSuperPlayerContract(ctx, req)
	resp := &pb.GetSuperPlayerContractResp{
		SuperPlayerContractList: make([]*pb.SuperPlayerContractInfo, 0),
	}
	log.DebugWithCtx(ctx, "GetSuperPlayerContract req:%v", req)

	if req.GetSuperPlayerUid() == 0 {
		return resp, nil
	}

	contractList, lastContractID, err := s.orderMgr.GetUserContract(ctx, req.GetSuperPlayerUid(), true)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetSuperPlayerContract failed, err: %v", err)
		return resp, protocol.NewExactServerError(nil, status.ErrRepositoryFailed, "数据异常，请联系相关人员解决！")
	}

	// 补充查询价格信息
	result := make([]*pb.SuperPlayerContractInfo, 0)
	for _, i := range contractList {
		info := i
		var packageInfo *mgr.PackageConf
		if info.PackageType == pb.PackageType_ENUM_PACKAGE_TYPE_SVIP {
			packageInfo = s.packageMgr.GetSvipPackageByID(info.PackageId)
		} else {
			packageInfo = s.packageMgr.GetPackageByID(info.PackageId)
		}
		if packageInfo == nil {
			log.ErrorWithCtx(ctx, "GetSuperPlayerContract failed, packageInfo is nil, info:%+v", info)
			continue
		}
		info.Price = packageInfo.Price
		result = append(result, info)
	}

	resp.LastIosContractId = lastContractID
	resp.SuperPlayerContractList = contractList
	isShowManagerContractBtn := false
	//取签约管理用的信息，包括已经解约的信息
	if len(contractList) > 0 {
		isShowManagerContractBtn = true
	} else {
		// 已经解约但是在套餐有效期中
		isShowManagerContractBtn, _ = s.orderMgr.IsContractTimeRange(ctx, req.GetSuperPlayerUid(), req.GetMarketId())
	}
	resp.IsShowManagerContractBtn = isShowManagerContractBtn
	log.DebugWithCtx(ctx, "GetSuperPlayerContract out:%+v", resp)

	return resp, nil
}

// CancelOrder 取消订单（目前未使用，支付不支持）
func (s *Server) CancelOrder(ctx context.Context, req *pb.CancelOrderReq) (*pb.CancelOrderResp, error) {
	//return s.oldSvr.CancelOrder(ctx, req)
	log.InfoWithCtx(ctx, "CancelOrder req:%v", req)
	resp := &pb.CancelOrderResp{}
	return resp, nil
}

func (s *Server) GetOrderStatus(ctx context.Context, req *pb.GetOrderStatusReq) (*pb.GetOrderStatusResp, error) {
	//return s.oldSvr.GetOrderStatus(ctx, req)
	resp := &pb.GetOrderStatusResp{}
	log.DebugWithCtx(ctx, "GetOrderStatus req:%v", req)

	if req.GetOrderId() == "" {
		return resp, protocol.NewExactServerError(nil, status.ErrSuperPlayerInvalidPara, "订单号不能为空")
	}

	order, err := s.orderMgr.GetOrder(ctx, req.GetOrderId(), time.Time{}, true)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetOrderStatus GetOrder err:%v", err)
		if mysql.IsNoRowsError(err) {
			resp.Status = pb.EnumOrderStatus_ORDER_INIT
			return resp, nil
		}
		return resp, protocol.NewExactServerError(nil, status.ErrSuperPlayerInvalidPara, fmt.Sprintf("获取订单状态失败:%v", err))
	}
	if order == nil {
		return resp, protocol.NewExactServerError(nil, status.ErrSuperPlayerInvalidPara, fmt.Sprintf("获取订单状态失败:%v", err))
	}

	resp.Status = pb.EnumOrderStatus(order.Status)
	return resp, nil
}

// SendSuperPlayerDressExperience 发放超级会员体验装扮
func (s *Server) SendSuperPlayerDressExperience(ctx context.Context, req *pb.SendSuperPlayerDressExperienceReq) (*pb.SendSuperPlayerDressExperienceResp, error) {
	//return s.oldSvr.SendSuperPlayerDressExperience(ctx, req)
	log.InfoWithCtx(ctx, "SendSuperPlayerDressExperience in:%+v", req)
	resp := &pb.SendSuperPlayerDressExperienceResp{}

	// 判断订单状态
	order, err := s.orderMgr.GetOrder(ctx, req.GetOrderId(), time.Time{}, true)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetOrderStatus GetOrder err:%v", err)
		if mysql.IsNoRowsError(err) {
			return resp, protocol.NewExactServerError(nil, status.ErrSuperPlayerInvalidPara, "该订单不存在")
		}
		return resp, protocol.NewExactServerError(nil, status.ErrSuperPlayerInvalidPara, fmt.Sprintf("获取订单状态失败:%v", err))
		//return resp, err
	}
	if order == nil {
		return resp, protocol.NewExactServerError(nil, status.ErrSuperPlayerInvalidPara, "该订单不存在")
	}

	if order.Status != int8(pb.EnumOrderStatus_ORDER_PAY_SUCCESS) {
		log.ErrorWithCtx(ctx, "GetOrderStatus invalid order status:%v", order.Status)
		return resp, protocol.NewExactServerError(nil, status.ErrSuperPlayerInvalidPara, "该订单还未成功支付")
	}

	// 调用下游服务发放
	if err = s.client.SendDressExperience(ctx, req.GetSuperPlayerUid(), req.GetIncrConfPackage(), req.GetOrderId(), req.GetReason(), req.GetServerTime()); err != nil {
		log.ErrorWithCtx(ctx, "SendDressExperience err:%v", err)
		return resp, err
	}

	return resp, nil
}

// GetCanUpgradeDays 获取用户可升级天数
func (s *Server) GetCanUpgradeDays(ctx context.Context, req *pb.GetCanUpgradeDaysReq) (*pb.GetCanUpgradeDaysResp, error) {
	//return s.oldSvr.GetCanUpgradeDays(ctx, req)
	resp := &pb.GetCanUpgradeDaysResp{}
	log.DebugWithCtx(ctx, "GetCanUpgradeDays req:%v", req)

	if req.GetSuperPlayerUid() == 0 {
		return resp, invalidErr
	}

	// 查用户到期时间
	userInfo, err := s.membershipMgr.GetMembershipInfo(ctx, req.GetSuperPlayerUid())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetCanUpgradeDays GetMembershipInfo err:%v", err)
		return resp, protocol.NewExactServerError(nil, status.ErrSuperPlayerSysErr, "数据异常，请联系相关人员解决！")
	}

	// 获取用户剩余天数
	sum, err := s.orderMgr.GetUserCanUpgradeDays(ctx, req.GetSuperPlayerUid())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetCanUpgradeDays failed, err: %v", err)
		return resp, protocol.NewExactServerError(nil, status.ErrSuperPlayerSysErr, "数据异常，请联系相关人员解决！")
	}
	log.DebugWithCtx(ctx, "GetCanUpgradeDays success, sum:%v", sum)

	// 获取用户剩余的天数
	var maxDays int64
	now := time.Now()
	todayZero := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())
	if userInfo.ExpireTimestamp > todayZero.Unix() {
		maxDays = userInfo.ExpireTimestamp - todayZero.Unix()/86400
	}
	if sum > maxDays { // 兜底告警
		if s.dyConf.GetSpecialNotice() {
			_ = s.feishu.SendInfo(fmt.Sprintf("[超级会员]GetCanUpgradeDays sum:%d > maxDays:%d, uid:%+v", sum, maxDays, req.GetSuperPlayerUid()))
		}
	}

	resp.CanUpgradeDays = uint32(sum)
	resp.SuperPlayerUid = req.GetSuperPlayerUid()
	log.DebugWithCtx(ctx, "GetCanUpgradeDays success, resp:%v", resp)
	return resp, nil
}

// CancelContract 解约
func (s *Server) CancelContract(ctx context.Context, req *pb.CancelContractReq) (*pb.CancelContractResp, error) {
	//return s.oldSvr.CancelContract(ctx, req)

	log.DebugWithCtx(ctx, "CancelContract req:%+v", req)
	resp := &pb.CancelContractResp{}

	err := s.orderMgr.CancelContract(ctx, req.GetUid(), req.GetContractId())

	defer func() {
		if err != nil {
			log.ErrorWithCtx(ctx, "CancelContract req:%+v err:%v", req, err)
		} else {
			log.InfoWithCtx(ctx, "CancelContract req:%+v", req)
		}
	}()

	return resp, err
}

// NotifyContract 更新签约状态
func (s *Server) NotifyContract(ctx context.Context, req *pb.NotifyContractReq) (*pb.NotifyContractResp, error) {
	resp := &pb.NotifyContractResp{}
	log.InfoWithCtx(ctx, "NotifyContract req:%v", req)

	// 先查签约信息
	contractInfo, err := s.orderMgr.GetContractByDbWithContractId(ctx, req.ContractId)
	if err != nil {
		log.ErrorWithCtx(ctx, "NotifyContract GetContractByDbWithContractId err:%v", err)
		return resp, protocol.NewExactServerError(nil, status.ErrSuperPlayerInvalidPara, fmt.Sprintf("获取签约信息失败:%v", err))
	}
	if contractInfo == nil { // 旧逻辑，签约不存在直接返回空
		log.ErrorWithCtx(ctx, "NotifyContract contractInfo is nil，req:%+v", req)
		return resp, nil
	}
	// 判断签约类型
	packageType := pb.PackageType(contractInfo.PackageType)
	log.DebugWithCtx(ctx, "NotifyContract contractInfo:%+v", contractInfo)

	// 查询套餐信息
	packageInfo := &mgr.PackageConf{}
	if packageType == pb.PackageType_ENUM_PACKAGE_TYPE_SVIP {
		packageInfo = s.packageMgr.GetSvipPackageByID(contractInfo.PackageId)
		if len(req.ProductId) > 0 {
			packageInfo = s.packageMgr.GetSvipPackageByProductID(req.ProductId)
			if nil == packageInfo {
				log.ErrorWithCtx(ctx, "NotifyContract GetPackageByProductID empty")
				return resp, protocol.NewExactServerError(nil, status.ErrSuperPlayerInvalidPara, "找不到套餐配置")
			}
		}
	} else {
		packageInfo = s.packageMgr.GetPackageByID(contractInfo.PackageId)
		if len(req.ProductId) > 0 {
			packageInfo = s.packageMgr.GetPackageByProductID(req.ProductId)
			if nil == packageInfo {
				log.ErrorWithCtx(ctx, "NotifyContract GetPackageByProductID empty")
				return resp, protocol.NewExactServerError(nil, status.ErrSuperPlayerInvalidPara, "找不到套餐配置")
			}
		}
	}
	if packageInfo == nil {
		log.ErrorWithCtx(ctx, "NotifyContract packageInfo is nil，req:%+v", req)
		return resp, protocol.NewExactServerError(nil, status.ErrSuperPlayerInvalidPara, "找不到套餐配置")
	}
	var nextPayTime time.Time
	now := time.Now()
	nextPayTime = time.Time{} // 赋值空
	if len(req.NextPayTime) > 0 {
		newNextPayTime, err := time.ParseInLocation("2006-01-02 15:04:05", req.NextPayTime, utils.GetUTS())
		if err == nil {
			nextPayTime = newNextPayTime
		} else {
			log.ErrorWithCtx(ctx, "NotifyContract time.ParseInLocation err:%+v", err)
			_ = s.feishu.SendInfo(fmt.Sprintf("[超级会员]NotifyContract time.ParseInLocation err:%+v, req:%+v", err, req))
			if utils.IsAppstore(req.PayChannel) { // IOS兜底（显示次月时间），防止下次支付时间异常
				nextPayTime = time.Unix(now.Unix(), 0) // 去除毫秒分表误差
			}
		}
		if utils.IsAppstore(req.PayChannel) && (nextPayTime.After(time.Date(now.Year(), now.Month()+2, 1, 0, 0, 0, 0, time.Local)) || nextPayTime.IsZero()) {
			_ = s.feishu.SendError(fmt.Sprintf("[超级会员]NotifyContract nextPayTime:%v > now:%v, req:%+v", nextPayTime, now, req))
			nextPayTime = time.Unix(now.Unix(), 0) // 去除毫秒分表误差
		}
	} else {
		if utils.IsAppstore(req.PayChannel) { // IOS兜底（显示次月时间），防止下次支付时间异常
			_ = s.feishu.SendError(fmt.Sprintf("[超级会员]NotifyContract nextPayTime is empty, req:%+v", req))
			nextPayTime = time.Unix(now.Unix(), 0) // 去除毫秒分表误差
		}
	}

	// 组装更新信息并进行更新
	var info = &orderMgrStruct.NotifyContractInfo{
		ContractId:  req.GetContractId(),
		Status:      req.GetStatus(),
		PayChannel:  req.GetPayChannel(),
		PackageId:   packageInfo.ID,
		PackageName: packageInfo.Name,
		BundleId:    packageInfo.ProductID,
		RealBuyerId: req.GetRealBuyerId(),
		ActiveUid:   req.GetSuperPlayerUid(),
		ActiveDays:  uint32(packageInfo.Days),
		NextPayTime: nextPayTime,
		Now:         now,
	}
	err = s.orderMgr.NotifyContract(ctx, info) //req.OrderId, req.ContractId, req.ProductId, req.Status
	if err != nil {
		log.ErrorWithCtx(ctx, "NotifyContract req:%+v err:%v", req, err)
		return resp, protocol.NewExactServerError(nil, status.ErrSuperPlayerInvalidPara, fmt.Sprintf("更新签约状态失败:%v", err))
	}
	log.DebugWithCtx(ctx, "NotifyContract req:%v resp:%v", req, resp)
	return resp, nil
}

// PlaceOrder 下单
func (s *Server) PlaceOrder(ctx context.Context, req *pb.PlaceOrderReq) (*pb.PlaceOrderResp, error) {
	resp := &pb.PlaceOrderResp{}
	log.InfoWithCtx(ctx, "PlaceOrder req:%+v", req)

	// 参数校验
	if req.GetOrder().GetUid() == 0 || req.GetOrder().GetPackageId() == "" {
		return resp, protocol.NewExactServerError(nil, status.ErrSuperPlayerInvalidPara, "请求参数错误")
	}

	packOrder := req.Order
	// 创建订单
	order := &orderMgrStruct.Order{
		Uid:         packOrder.Uid,
		PackageId:   packOrder.GetPackageId(),
		PayChannel:  packOrder.PayChannel,
		OsType:      packOrder.OsType,
		Version:     packOrder.DeviceId,
		BundleId:    "",
		MarketId:    packOrder.MarketId,
		PackageType: uint32(packOrder.PackageType),
	}

	{ // 判断是否已经被限购
		now := time.Now()
		// 获取限购套餐信息
		packageLimitConfig := s.packageMgr.GetPackageLimitByPackageInfo(ctx, req.GetOrder().GetPackageId(), req.GetOrder().GetPackageType(), now)
		if packageLimitConfig != nil && packageLimitConfig.LimitCount > 0 && packageLimitConfig.StartTime <= now.Unix() && packageLimitConfig.EndTime >= now.Unix() {
			// 判断是否被限购
			limit, err := s.orderMgr.ValidLimitPurchase(ctx, order.Uid, packageLimitConfig.Id, packageLimitConfig.LimitCount, s.activityConf.GetLimitFeqLockDuration())
			if err != nil {
				log.ErrorWithCtx(ctx, "PlaceOrder LimitSinglePurchase err:%v", err)
				return resp, err
			}
			if limit {
				log.ErrorWithCtx(ctx, "PlaceOrder LimitSinglePurchase uid:%v packageId:%v", order.Uid, order.PackageId)
				return resp, protocol.NewExactServerError(nil, status.ErrSuperPlayerInvalidPacket, "已达到限购次数")
			}
		}
	}

	// 查询可升级天数
	var canUpgradeDays int64
	var err error
	if packOrder.OrderType == utils.OrderTypeUpgrade {
		canUpgradeDays, err = s.orderMgr.GetUserCanUpgradeDays(ctx, packOrder.Uid)
		if err != nil {
			log.ErrorWithCtx(ctx, "PlaceOrder GetUserCanUpgradeDays err:%v", err)
			return resp, protocol.NewExactServerError(nil, status.ErrSuperPlayerSysErr, "数据异常，请联系相关人员解决！")
		}
	}

	order, packConf, upgradePackageConf, packageStatus, payChannelMap, discountPrice, err := s.ValidAndFilterOrderInfo(ctx, packOrder, order, canUpgradeDays)
	if err != nil {
		log.ErrorWithCtx(ctx, "PlaceOrder ValidAndFilterOrderInfo err:%v", err)
		return resp, err
	}

	if packConf == nil && packOrder.OrderType != utils.OrderTypeUpgrade {
		log.ErrorWithCtx(ctx, "PlaceOrder invalid paychannel packageOrder:%v", packOrder)
		return resp, protocol.NewExactServerError(nil, status.ErrSuperPlayerInvalidPara, "找不到套餐配置")
	}
	if upgradePackageConf == nil && packOrder.OrderType == utils.OrderTypeUpgrade {
		log.ErrorWithCtx(ctx, "PlaceOrder invalid paychannel packageOrder:%v", packOrder)
		return resp, protocol.NewExactServerError(nil, status.ErrSuperPlayerInvalidPara, "找不到升级套餐配置")
	}

	//订单已经下架
	if packageStatus == pb.PackageStatus_ENUM_PACKAGE_STATUS_STOPING {
		log.ErrorWithCtx(ctx, "PlaceOrder invalid PackageStatus packageOrder:%v", packOrder)
		return resp, protocol.NewExactServerError(nil, status.ErrSuperPlayerInvalidPara, "套餐已经下架")
	}
	if !payChannelMap[packOrder.PayChannel] && packOrder.PayChannel != "WCBRAND" {
		log.ErrorWithCtx(ctx, "PlaceOrder invalid paychannel packageOrder:%v", packOrder)
		return resp, protocol.NewExactServerError(nil, status.ErrSuperPlayerInvalidPara, "非法支付渠道")
	}

	//需要在下单时判断
	beforeUserInfo, _ := s.membershipMgr.GetMembershipInfo(ctx, packOrder.Uid)
	// 新逻辑，到期时间不超过2099年12月31日23:59:59
	limitTime := time.Date(2099, 12, 31, 23, 59, 59, 0, time.Local)
	if time.Unix(beforeUserInfo.ExpireTimestamp, 0).AddDate(0, 0, int(order.Days)).After(limitTime) {
		return resp, protocol.NewExactServerError(nil, status.ErrSuperPlayerInvalidPara, "会员最大时长限制")
	}
	var newUser bool
	var expireTime time.Time
	now := time.Now()
	// 这里不做签约时间偏移，里面去做
	if packOrder.PackageType == pb.PackageType_ENUM_PACKAGE_TYPE_NORMAL {
		newUser = !beforeUserInfo.IsSignedSuperPlayer
		if beforeUserInfo.ExpireTimestamp > now.Unix() {
			expireTime = time.Unix(beforeUserInfo.ExpireTimestamp, 0)
		} else {
			expireTime = now
		}
	} else {
		newUser = !beforeUserInfo.IsSignedSvip
		if beforeUserInfo.SvipExpireTimestamp > now.Unix() {
			expireTime = time.Unix(beforeUserInfo.SvipExpireTimestamp, 0)
		} else {
			expireTime = now
		}
	}

	// 下单
	placeOrderResp := &orderMgrStruct.PlaceOrderResp{}
	if packOrder.OrderType == utils.OrderTypeUpgrade {
		if upgradePackageConf == nil {
			log.ErrorWithCtx(ctx, "PlaceOrder invalid paychannel packageOrder:%v", packOrder)
			return resp, protocol.NewExactServerError(nil, status.ErrSuperPlayerInvalidPara, "找不到升级套餐配置")
		}
		placeOrderResp, err = s.orderMgr.PlaceUpgradeOrder(ctx, order, discountPrice, packOrder.Code, newUser, upgradePackageConf.UpgradePackageType == uint32(pb.UpgradePackageType_ENUM_UPGRADE_PACKAGE_TYPE_UPGRADE_ALL))
	} else {
		placeOrderResp, err = s.orderMgr.PlaceOrder(ctx, order, discountPrice, packOrder.Code, newUser, expireTime, req.GetOrder().GetOriginalTransactionIds())
	}
	if err != nil {
		log.ErrorWithCtx(ctx, "PlaceOrder fail order:%+v err:%v", packOrder, err)
		return resp, err
	}

	resp.OrderId = placeOrderResp.OrderId
	resp.Token = placeOrderResp.Token
	resp.ChannelMap = placeOrderResp.ChannelMap
	resp.CliOrderNo = placeOrderResp.CliOrderNo
	resp.Tsk = placeOrderResp.Tsk
	if packOrder.PackageType == pb.PackageType_ENUM_PACKAGE_TYPE_SVIP {
		resp.IsFirstOpen = !beforeUserInfo.IsSignedSvip
	} else {
		resp.IsFirstOpen = !beforeUserInfo.IsSignedSuperPlayer
	}
	log.DebugWithCtx(ctx, "PlaceOrder req:%+v resp:%+v", req, resp)
	return resp, nil
}

// ValidAndFilterOrderInfo 校验并过滤订单信息
func (s *Server) ValidAndFilterOrderInfo(ctx context.Context, packOrder *pb.PackageOrder, order *orderMgrStruct.Order, canUpgradeDays int64) (
	*orderMgrStruct.Order, *mgr.PackageConf, *mgr.UpgradePackageConf, pb.PackageStatus, map[string]bool, float32, error) {
	packConf := &mgr.PackageConf{}
	upgradeConf := &mgr.UpgradePackageConf{}
	var packageStatus pb.PackageStatus
	payChannelMap := make(map[string]bool)
	var discountPrice float32
	if packOrder.OrderType == utils.OrderTypeBuy || packOrder.OrderType == utils.OrderTypePeriod || packOrder.OrderType == "" {
		if packOrder.PackageType == pb.PackageType_ENUM_PACKAGE_TYPE_SVIP {
			packConf = s.packageMgr.GetSvipPackageByID(packOrder.PackageId)
		} else {
			packConf = s.packageMgr.GetPackageByID(packOrder.PackageId)
		}
		if packConf == nil {
			return nil, nil, nil, 0, nil, 0, protocol.NewExactServerError(nil, status.ErrSuperPlayerInvalidPara, "找不到套餐配置")
		}
		packageStatus = pb.PackageStatus(packConf.PackageStatus)
		payChannelMap = packConf.PayChannelMap
		order.Price = packConf.Price
		order.OriginalPrice = order.Price
		order.PackageName = packConf.Name
		pkgBytes, err := json.Marshal(packConf)
		if err != nil {
			log.ErrorWithCtx(ctx, "PlaceOrder json.Marshal err:%v", err)
		}
		order.PackageInfo = string(pkgBytes)
		order.PackageType = packConf.PackageType
		order.Days = uint32(packConf.Days)
		month := uint32(packConf.Days) / 30
		if month <= 0 {
			order.OrderValue = uint32(packConf.Value)
		} else {
			order.OrderValue = uint32(packConf.Value) * month
		}
		if packConf.Auto {
			order.OrderType = int8(pb.EnumOrderType_ORDER_TYPE_AUTO_PAY)
		} else {
			order.OrderType = int8(pb.EnumOrderType_ORDER_TYPE_INITIATIVE_PAY)
		}
		order.BundleId = packConf.ProductID
		discountPrice = packConf.DiscountPrice
	} else {
		upgradeConf = s.packageMgr.GetUpgradePackageByID(packOrder.PackageId)
		if upgradeConf == nil {
			return nil, nil, nil, 0, nil, 0, protocol.NewExactServerError(nil, status.ErrSuperPlayerInvalidPara, "找不到升级套餐配置")
		}
		packageStatus = pb.PackageStatus(upgradeConf.PackageStatus)
		payChannelMap = upgradeConf.PayChannelMap
		order.Price = upgradeConf.Price
		order.OriginalPrice = order.Price
		order.PackageName = upgradeConf.Name
		pkgBytes, err := json.Marshal(upgradeConf)
		if err != nil {
			log.ErrorWithCtx(ctx, "PlaceOrder json.Marshal err:%v", err)
		}
		order.PackageInfo = string(pkgBytes)
		order.PackageType = uint32(pb.PackageType_ENUM_PACKAGE_TYPE_SVIP)
		order.Days = uint32(upgradeConf.Days)
		order.OrderType = int8(pb.EnumOrderType_ORDER_TYPE_UPGRADE)
		order.BundleId = upgradeConf.ProductID
		month := uint32(upgradeConf.Days) / 30
		if month <= 0 {
			order.OrderValue = uint32(upgradeConf.Value)
		} else {
			order.OrderValue = uint32(upgradeConf.Value) * month
		}
		discountPrice = upgradeConf.DiscountPrice
		// 判断是否能够升级
		if upgradeConf.UpgradePackageType != uint32(pb.UpgradePackageType_ENUM_UPGRADE_PACKAGE_TYPE_UPGRADE_ALL) { // 如果升级全部，则查询当前可升级时间
			if canUpgradeDays < upgradeConf.Days {
				return nil, nil, nil, 0, nil, 0, protocol.NewExactServerError(nil, status.ErrSuperPlayerInvalidPara, "可升级天数不足，无法升级")
			}
		} else { // 如果是升级全部，看下可升级天数是否大于0
			if canUpgradeDays <= 0 {
				return nil, nil, nil, 0, nil, 0, protocol.NewExactServerError(nil, status.ErrSuperPlayerInvalidPara, "可升级天数不足，无法升级")
			}
			// 如果可以升级，就按不同的升级方式进行升级
			for _, upgradePrice := range upgradeConf.UpgradeAllConfig.GetUpgradePriceList() {
				if upgradePrice.GetStartDays() >= uint32(canUpgradeDays) || (upgradePrice.GetEndDays() < uint32(canUpgradeDays) && !upgradePrice.GetIsInfinite()) { // 左开右闭
					continue
				}
				dailyPrice := upgradePrice.GetPrice()
				order.Days = uint32(canUpgradeDays)
				order.Price = float32(uint32(dailyPrice*100)*uint32(canUpgradeDays)) / 100
				order.PackageName = fmt.Sprintf("升级%+v天", canUpgradeDays)
				order.OrderValue = uint32(upgradePrice.GetValue() * float32(canUpgradeDays))
			}
			if len(upgradeConf.UpgradeAllConfig.GetUpgradePriceList()) == 0 { // 如果梯度为空
				dailyPrice := upgradeConf.UpgradeAllConfig.GetUpgradeAveragePrice()
				order.Days = uint32(canUpgradeDays)
				order.Price = float32(uint32(dailyPrice*100)*uint32(canUpgradeDays)) / 100
				order.PackageName = fmt.Sprintf("升级%+v天", canUpgradeDays)
				order.OrderValue = uint32(upgradeConf.UpgradeAllConfig.GetUpgradeAverageValue() * float32(canUpgradeDays))
			}
			if order.OrderValue < 1 { // 防止仅升级一天的时候，造成成长值增加不足1
				order.OrderValue = 1
			}
			if order.Days == 0 {
				return nil, nil, nil, 0, nil, 0, protocol.NewExactServerError(nil, status.ErrSuperPlayerInvalidPara, "可升级天数不足，无法升级")
			}
		}
	}
	return order, packConf, upgradeConf, packageStatus, payChannelMap, discountPrice, nil
}

func (s *Server) NoticeOrderStatus(ctx context.Context, req *pb.NoticeOrderStatusReq) (*pb.NoticeOrderStatusResp, error) {
	resp := &pb.NoticeOrderStatusResp{}
	log.InfoWithCtx(ctx, "NoticeOrderStatus req:%+v", req)

	if !req.Result {
		//支付组那边失败的情况不会回调
		return resp, invalidErr
	}

	st := pb.EnumOrderStatus_ORDER_PAY_SUCCESS
	noticeTs := time.Now()
	if len(req.NoticeTime) > 0 {
		nTs, err := utils.ParseTimeFromString(req.NoticeTime)
		if err == nil {
			noticeTs = nTs
		}
	}

	// 查询订单
	order, err := s.orderMgr.GetOrder(ctx, req.GetOrderId(), time.Now(), true)
	if err != nil {
		log.ErrorWithCtx(ctx, "NoticeOrderStatus GetOrder err:%v", err)
		return resp, err
	}
	if order == nil {
		log.ErrorWithCtx(ctx, "NoticeOrderStatus GetOrder order is nil, req:%+v", req)
		return resp, protocol.NewExactServerError(nil, status.ErrSuperPlayerInvalidPara, "订单不存在")
	}
	if order.Status == int8(st) {
		log.WarnWithCtx(ctx, "NoticeOrderStatus order status is already success, req:%+v", req)
		return resp, nil
	}

	// 查询限购信息
	log.DebugWithCtx(ctx, "NoticeOrderStatus order.PackageId:%v order.PackageType:%v", order.PackageId, order.PackageType)
	packageLimitConfig := s.packageMgr.GetPackageLimitByPackageInfo(ctx, order.PackageId, pb.PackageType(order.PackageType), order.CreateTime)
	var pkgLimitId, limitCount uint32
	if packageLimitConfig != nil && !strings.HasPrefix(order.OrderId, "auto") {
		// 判断下单时间是否在限购时间内，如果在就限购
		if packageLimitConfig.StartTime <= order.CreateTime.Unix() && packageLimitConfig.EndTime >= order.CreateTime.Unix() {
			pkgLimitId = packageLimitConfig.Id
			limitCount = packageLimitConfig.LimitCount
		}
	}
	log.DebugWithCtx(ctx, "NoticeOrderStatus packageLimitConfig:%+v", packageLimitConfig)

	orderOpt := &orderMgrStruct.OrderOpt{
		ActiveUid:     req.GetSuperPlayerUid(),
		OrderId:       req.GetOrderId(),
		ServerTime:    noticeTs,
		PayChannel:    req.GetPayChannel(),
		TotalPrice:    req.GetTotalFee(),
		Status:        st,
		PkgLimitId:    pkgLimitId,
		PkgLimitCount: limitCount,
		OrderDays:     order.Days,
	}
	// 处理订单状态
	activeOrder, err := s.orderMgr.NoticeOrder(grpc.NewContextWithInfo(ctx), orderOpt) // 这里重置了取消超时时间，减少因为超时导致动作取消
	if err != nil {
		log.ErrorWithCtx(ctx, "NoticeOrderStatus err:%v", err)
		return resp, err
	}
	if activeOrder == nil { // 如果为空，说明被限购了，直接返回空
		return resp, err
	}

	_ = s.orderMgr.SetLatestPayRecord(ctx, activeOrder.ActiveUid) // 设置最新支付记录

	// 异步处理会员关系
	newCtx := grpc.NewContextWithInfo(ctx)
	go func() {
		s.ActiveOrder(newCtx, activeOrder)
	}()

	return resp, nil
}

// ActiveOrder 生效订单权益
func (s *Server) ActiveOrder(ctx context.Context, activeOrder *orderMgrStruct.Order) {
	awardPrivilegeTime := &membershipMgrStruct.AwardPrivilegeTime{
		OrderId:       activeOrder.OrderId,
		Reason:        fmt.Sprintf("订单%s生效", activeOrder.PackageName),
		PackageId:     activeOrder.PackageId,
		PackageName:   activeOrder.PackageName,
		Uid:           activeOrder.ActiveUid,
		IncrDays:      int64(activeOrder.Days),
		PayChannel:    activeOrder.PayChannel,
		PackageType:   activeOrder.PackageType,
		IsYearOrder:   s.dyConf.IsYearPack(activeOrder.Days),
		DelayReminder: false, // 这个参数目前好像没啥用了
		IsUpgrade:     activeOrder.OrderType == int8(pb.EnumOrderType_ORDER_TYPE_UPGRADE),
		ServerTime:    activeOrder.ServerTime,
		PlaceTime:     activeOrder.CreateTime,
	}
	beforeInfo, afterUserInfo, err := s.membershipMgr.SettlementUserPrivilegeTime(ctx, awardPrivilegeTime, false)
	if err != nil { // 这里仅报警，不影响正常流程
		log.ErrorWithCtx(ctx, "NoticeOrderStatus SettlementUserPrivilegeTime err:%v", err)
		return
	}

	// 判断是否需要增加成长值，有4种情况是增加成长值的：1、新开通会员 2、新开通SVIP 3、过期会员续费 4、过期SVIP续费
	nowTs := time.Now()
	log.DebugWithCtx(ctx, "beforeInfo:%+v afterUserInfo:%+v", beforeInfo, afterUserInfo)
	if (!beforeInfo.IsSignedVip && afterUserInfo.IsSignedVip && !beforeInfo.IsSignedSvip) ||
		(!beforeInfo.IsSignedSvip && afterUserInfo.IsSignedSvip) ||
		(beforeInfo.ExpireTime.Before(nowTs) && activeOrder.PackageType == uint32(pb.PackageType_ENUM_PACKAGE_TYPE_NORMAL) && beforeInfo.SvipExpireTime.Before(nowTs)) ||
		(beforeInfo.SvipExpireTime.Before(nowTs) && activeOrder.PackageType == uint32(pb.PackageType_ENUM_PACKAGE_TYPE_SVIP)) {
		// 先核销订单成长值
		addValueOrderId, icrValue, err := s.orderMgr.RedemptionUserOrderIndexValue(ctx, activeOrder.ActiveUid, activeOrder.OrderId)
		if err != nil {
			log.ErrorWithCtx(ctx, "SettlementUserPrivilegeTime RedemptionUserOrderIndexValue failed err:%v", err)
			return // 这里直接返回，后续有补单操作
		}

		awardValueInfo := &membershipMgrStruct.SettlementOrderScore{
			Uid:          activeOrder.Uid,
			OrderId:      addValueOrderId,
			PrentOrderId: activeOrder.OrderId,
			IncrValue:    int64(icrValue),
			IsExpire:     true, // 这里和原本逻辑保持一致，不发送通知
		}
		valRec, newValueInfo, err := s.membershipMgr.SettlementOrderScore(ctx, awardValueInfo, false)
		if err != nil {
			log.ErrorWithCtx(ctx, "SettlementUserPrivilegeTime SettlementOrderScore failed err:%v", err)
		} else {
			if valRec != nil { // 如果有记录，说明加上了
				afterUserInfo.Value = newValueInfo.Value // 更新信息里面的成长值
			}
		}
	}

	// 判断是否是限购订单
	packageLimitConfig := s.packageMgr.GetPackageLimitByPackageInfo(ctx, activeOrder.PackageId, pb.PackageType(activeOrder.PackageType), activeOrder.CreateTime)
	var pkgLimitId uint32
	if packageLimitConfig != nil {
		// 判断下单时间是否在限购时间内，如果在就限购
		if packageLimitConfig.StartTime <= activeOrder.CreateTime.Unix() && packageLimitConfig.EndTime >= activeOrder.CreateTime.Unix() {
			pkgLimitId = packageLimitConfig.Id
		}
	}

	// 处理推送
	err = s.membershipMgr.RefreshAndPushMembershipChange(ctx, awardPrivilegeTime, beforeInfo, afterUserInfo, pkgLimitId)
	if err != nil {
		log.ErrorWithCtx(ctx, "SettlementUserPrivilegeTime OnSuperPlayerInfoChange failed err:%v", err)
	}

	//记录签约套餐时间有效期
	if activeOrder.OrderType == int8(pb.EnumOrderType_ORDER_TYPE_AUTO_PAY) {
		if err = s.orderMgr.SetContractTimeRange(ctx, activeOrder.ActiveUid, activeOrder.MarketId, afterUserInfo.ExpireTime.Unix()); err != nil {
			log.ErrorWithCtx(ctx, "NoticeOrderStatus SetContractTimeRange err:%v", err)
		}
	}
	// 如果是自动下单，更新下次扣款时间
	if strings.Contains(activeOrder.OrderId, utils.OrderAutoVipPrefix) {
		// 如果是苹果和沙箱，就不更新，由回调更新
		if utils.IsAppstore(activeOrder.PayChannel) {
			return
		}
		var expiredTime time.Time
		if activeOrder.PackageType == uint32(pb.PackageType_ENUM_PACKAGE_TYPE_NORMAL) {
			expiredTime = afterUserInfo.ExpireTime
		} else {
			expiredTime = afterUserInfo.SvipExpireTime
		}
		s.orderMgr.UpdateContractInfoWithNoticeAutoOrder(ctx, activeOrder.OrderId, activeOrder.ContractId, activeOrder.ServerTime, expiredTime)
	}
}

// ReplacementOrder 补单
func (s *Server) ReplacementOrder(ctx context.Context, req *pb.ReplacementOrderReq) (*pb.ReplacementOrderResp, error) {
	log.DebugWithCtx(ctx, "ReplacementOrder req:%+v", req)
	resp := &pb.ReplacementOrderResp{}

	// 校验参数
	if req.GetOldOrderId() == "" || req.GetCreateTime() == 0 {
		log.ErrorWithCtx(ctx, "ReplacementOrder invalid req:%+v", req)
		return resp, protocol.NewExactServerError(nil, status.ErrSuperPlayerInvalidPara, "请求参数错误")
	}

	// 加锁
	if ok, _ := s.orderMgr.GetReplacementOrderLock(ctx, req.GetOldOrderId()); !ok {
		err := protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "补单频率限制")
		log.ErrorWithCtx(ctx, "ReplacementOrder req:%+v err:%v", req, err)
		return resp, err
	}

	// 获取订单
	createTime := time.Unix(int64(req.GetCreateTime()), 0)
	preOrder, err := s.orderMgr.GetOrder(ctx, req.GetOldOrderId(), createTime, false)
	if err != nil {
		log.ErrorWithCtx(ctx, "ReplacementOrder GetOrder fail req:%+v err:%v", req, err)
		return resp, err
	}

	if preOrder == nil {
		err = protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "原订单不存在")
		log.ErrorWithCtx(ctx, "ReplacementOrder order not exist req:%+v err:%v", req, err)
		return resp, err
	}

	if pb.EnumOrderStatus(preOrder.Status) != pb.EnumOrderStatus_ORDER_INIT {
		err = protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "不能补已经完结的单")
		log.ErrorWithCtx(ctx, "ReplacementOrder order finish  req:%+v err:%v", req, err)
		return resp, err
	}

	// 组转新订单信息
	payOrder := &pb.PackageOrder{
		Uid:        preOrder.Uid,
		PackageId:  preOrder.PackageId,
		PayChannel: preOrder.PayChannel,
		OrderType:  utils.GetOrderType(preOrder.OrderType == int8(pb.EnumOrderType_ORDER_TYPE_AUTO_PAY)),
		OsType:     preOrder.OsType,
		Version:    preOrder.Version,
		Remark:     fmt.Sprintf("补单%v", preOrder.PackageName),
		BundleId:   preOrder.BundleId,
		//Code:        "", 原来的补单接口也没有这个
		MarketId:    preOrder.MarketId,
		DeviceId:    preOrder.Version,
		PackageType: pb.PackageType(preOrder.PackageType),
	}

	placeOrderInfo, err := s.PlaceOrder(ctx, &pb.PlaceOrderReq{Order: payOrder})
	if err != nil {
		log.ErrorWithCtx(ctx, "ReplacementOrder fail req:%+v err:%v", req, err)
		return resp, protocol.NewExactServerError(nil, status.ErrSuperPlayerInvalidPara, fmt.Sprintf("补单失败:%v", err))
	}
	resp.OrderId = placeOrderInfo.OrderId

	log.DebugWithCtx(ctx, "ReplacementOrder req:%+v resp:%+v", req, resp)
	return resp, nil
}

// PlaceAutoPayOrder 自动下单补单接口
func (s *Server) PlaceAutoPayOrder(ctx context.Context, req *pb.PlaceAutoPayOrderReq) (*pb.PlaceAutoPayOrderResp, error) {
	out := &pb.PlaceAutoPayOrderResp{}

	log.InfoWithCtx(ctx, "PlaceAutoPayOrder req:%+v", req)

	// 查签约信息
	contractInfo, err := s.orderMgr.GetContractByDbWithContractId(ctx, req.ContractId)
	if err != nil {
		log.ErrorWithCtx(ctx, "PlaceAutoPayOrder GetContractByDbWithContractId err:%v", err)
		return out, protocol.NewExactServerError(nil, status.ErrSuperPlayerInvalidPara, fmt.Sprintf("获取签约信息失败:%v", err))
	}
	if contractInfo == nil {
		log.ErrorWithCtx(ctx, "PlaceAutoPayOrder contractInfo is nil，req:%+v", req)
		return out, protocol.NewExactServerError(nil, status.ErrSuperPlayerInvalidPara, "找不到签约信息")
	}

	// 根据签约信息查包裹信息
	packConf := &mgr.PackageConf{}
	if contractInfo.PackageType == uint32(pb.PackageType_ENUM_PACKAGE_TYPE_SVIP) {
		packConf = s.packageMgr.GetSvipPackageByProductID(req.GetProduceId())
	} else {
		packConf = s.packageMgr.GetPackageByProductID(req.GetProduceId())
	}
	if packConf == nil {
		log.ErrorWithCtx(ctx, "PlaceAutoPayOrder invalid packConf req:%+v", req)
		return out, protocol.NewExactServerError(nil, status.ErrSuperPlayerInvalidPara, "invalid produceID")
	}
	pkgInfoBytes, _ := json.Marshal(packConf)

	// 根据是否使用优惠来决定下单价格
	price := packConf.Price
	if req.GetHasDiscount() {
		if packConf.DiscountPrice != 0 {
			price = packConf.DiscountPrice
		}
	}

	err = s.orderMgr.PlaceAutoPayOrder(ctx, &orderMgrStruct.PlaceAutoPayOrderReq{
		ProduceId:   req.GetProduceId(),
		ContractId:  req.GetContractId(),
		PkgId:       packConf.ID,
		PkgName:     packConf.Name,
		PkgMarketId: packConf.MarketId,
		PkgStatus:   packConf.PackageStatus,
		OrderValue:  uint32(packConf.Value),
		Price:       price,
		Days:        uint32(packConf.Days),
		PkgInfo:     string(pkgInfoBytes),
		BundleId:    packConf.ProductID,
		PackageType: packConf.PackageType,
	})
	log.DebugWithCtx(ctx, "PlaceAutoPayOrder req:%+v err:%v", req, err)
	if err != nil {
		log.ErrorWithCtx(ctx, "PlaceAutoPayOrder fail req:%+v err:%v", req, err)
		return out, protocol.NewExactServerError(nil, status.ErrSuperPlayerSysErr, fmt.Sprintf("下单失败:%v", err))
	}

	return out, nil
}

// GetUserOrderLeftDays 获取用户订单剩余天数
func (s *Server) GetUserOrderLeftDays(ctx context.Context, req *pb.GetUserOrderLeftDaysReq) (*pb.GetUserOrderLeftDaysResp, error) {
	log.DebugWithCtx(ctx, "GetUserOrderLeftDays req:%+v", req)
	resp := &pb.GetUserOrderLeftDaysResp{}

	if req.GetSuperPlayerUid() == 0 {
		return resp, invalidErr
	}

	// 获取用户剩余天数
	vipLeftDays, svipLeftDays, err := s.orderMgr.GetUserOrderLeftDays(ctx, req.GetSuperPlayerUid())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetUserOrderLeftDays failed, err: %v", err)
		return resp, protocol.NewExactServerError(nil, status.ErrSuperPlayerSysErr, "数据异常，请联系相关人员解决！")
	}

	resp.SuperPlayerUid = req.GetSuperPlayerUid()
	resp.VipLeftDays = uint32(vipLeftDays)
	resp.SvipLeftDays = uint32(svipLeftDays)
	log.DebugWithCtx(ctx, "GetUserOrderLeftDays success, resp:%v", resp)
	return resp, nil
}

// autoPayTimer 扫描签约用户，发起自动扣款等操作
func (s *Server) autoPayTimer(ctx context.Context) {
	if isLock, _ := s.orderMgr.LockAutoPay(ctx); !isLock {
		log.DebugWithCtx(ctx, "autoPayTimer is locked")
		return
	}

	ctx, cancel := context.WithTimeout(context.Background(), time.Minute*8)
	defer func() {
		s.orderMgr.UnlockAutoPay(ctx)
		cancel()
	}()

	// 按照签约时间进行分段操作，防止超时
	var tmpTs, step int64 = s.dyConf.GetSPSystemBeginTs()
	beginTs, _ := s.orderMgr.GetAutoPayContractOffSetFinishKey(ctx)
	if beginTs <= tmpTs {
		beginTs = tmpTs
	}
	contracts, err := s.orderMgr.GetActiveContractListWithCreateTime(ctx, time.Unix(beginTs, 0), time.Unix(beginTs+step, 0))
	if err != nil {
		log.ErrorWithCtx(ctx, "autoPayTimer GetContractList off:%v err:%v", beginTs, err)
		return
	}

	log.InfoWithCtx(ctx, "autoPayTimer contracts off:%v sz:%v", beginTs, len(contracts))

	beginTs = beginTs + step
	if beginTs >= time.Now().Unix() {
		beginTs = tmpTs
	}
	go s.autoPayTimerHelper(grpc.NewContextWithInfo(ctx), contracts)
	if err = s.orderMgr.SetAutoPayContractOffSetFinishKey(ctx, beginTs); err != nil {
		log.ErrorWithCtx(ctx, "autoPayTimer SetAutoPayContractOffSetFinishKey err:%v", err)
	}
}

// autoPayTimerHelper 根据签约信息自动续期
func (s *Server) autoPayTimerHelper(ctx context.Context, contractList []*orderMgrStruct.ContractRecord) {
	ctx, cancel := context.WithTimeout(ctx, time.Minute*5)
	defer cancel()
	defer func() { // 防止定时任务异常退出
		if r := recover(); nil != r {
			log.ErrorWithCtx(ctx, "autoPayTimerHelper recover err:%v", r)
		}
	}()

	for _, tmpC := range contractList {
		contract := tmpC
		if contract.Status != utils.SIGN_CONTRACT { // 只处理签约状态
			continue
		}

		os := protocol.ANDROID
		if utils.IsAppstore(contract.PayChannel) { // 判断是否APPSTORE签约
			os = protocol.IOS
		}
		//提前5天提示扣款，如果不需要续约请取消订阅
		s.orderMgr.RenewalNotify(ctx, contract, os)

		//iOS由苹果管理下单，这里只是检查是否有扣款失败的签约然后发出助手消息
		if os == protocol.IOS {
			if time.Since(contract.NextTime).Seconds() >= s.dyConf.GetIosNoticeCancelContractTs() {
				s.orderMgr.SendCancelContractNotifyMsg(ctx, contract.Uid, os, contract)
			}
			continue
		}
		err := s.doAutoPay(contract)
		if err != nil {
			log.ErrorWithCtx(ctx, "autoPayTimer doAutoPay contract:%+v err:%v", contract, err)
		}
	}
}

// doAutoPay 主要是安卓签约主动扣费使用
func (s *Server) doAutoPay(contract *orderMgrStruct.ContractRecord) error {
	ctx, cancel := context.WithTimeout(context.Background(), time.Minute*5)
	defer cancel()
	log.InfoWithCtx(ctx, "doAutoPay contract :%+v", contract)

	// 查询套餐信息
	var packConf *mgr.PackageConf
	if contract.PackageType == uint32(pb.PackageType_ENUM_PACKAGE_TYPE_SVIP) {
		packConf = s.packageMgr.GetSvipPackageByID(contract.PackageId)
	} else {
		packConf = s.packageMgr.GetPackageByID(contract.PackageId)
	}
	if nil == packConf {
		log.ErrorWithCtx(ctx, "doAutoPay GetPackageByID empty contractID:%v", contract.ContractId)
		return nil
	}

	// 查询用户信息
	superPlayerInfo, err := s.membershipMgr.GetMembershipInfo(ctx, contract.Uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "doAutoPay GetSuperPlayerInfo ContractID:%v err:%v", contract.ContractId, err)
		return err
	}
	var expireTime int64
	if contract.PackageType == uint32(pb.PackageType_ENUM_PACKAGE_TYPE_SVIP) {
		expireTime = superPlayerInfo.SvipExpireTimestamp
	} else {
		expireTime = superPlayerInfo.ExpireTimestamp

	}
	pkgInfoBytes, err := json.Marshal(packConf)
	if err != nil {
		log.ErrorWithCtx(ctx, "doAutoPay json.Marshal err:%v", err)
	}

	// 下单
	if err = s.orderMgr.DoAutoPay(ctx, &orderMgrStruct.DoAutoPayParams{
		Contract:        contract,
		ExpireTimestamp: expireTime,
		Price:           packConf.Price,
		ProductId:       packConf.ProductID,
		Days:            uint32(packConf.Days),
		Value:           uint32(packConf.Value),
		PackageName:     packConf.Name,
		PackageInfo:     string(pkgInfoBytes),
		PackageType:     packConf.PackageType,
	}); err != nil {
		log.ErrorWithCtx(ctx, "doAutoPay DoAutoPay err:%v", err)
		return err
	}

	return nil
}

// autoCancelContractTimer 自动解约
func (s *Server) autoCancelContractTimer(ctx context.Context) {
	ctx, cancel := context.WithTimeout(context.Background(), time.Hour) // 1小时超时，防止死锁
	defer cancel()

	if isLock, _ := s.orderMgr.LockAutoCancelContract(ctx); !isLock {
		log.DebugWithCtx(ctx, "autoCancelContractTimer is locked")
		return
	}

	nowTs := time.Now()
	var beginTs, step int64 = s.dyConf.GetSPSystemBeginTs()
	for {
		contracts, err := s.orderMgr.GetContractListCanCancelWithCreateTime(ctx, time.Unix(beginTs, 0), time.Unix(beginTs+step, 0))
		if err != nil {
			log.ErrorWithCtx(ctx, "autoCancelContractTimer GetContractList err:%v", err)
			break
		}
		log.DebugWithCtx(ctx, "autoCancelContractTimer contracts:%v off:%v step:%v", len(contracts), beginTs, step)

		//过期掉扣款失败的订单
		for _, c := range contracts {
			tmpC := c
			//苹果不能自动解约 TODO后续优化
			if utils.IsAppstore(tmpC.PayChannel) {
				continue
			}
			if err := s.able2cancel(ctx, tmpC); err == nil {
				if err := s.orderMgr.DoCancelContract(ctx, tmpC); err != nil {
					log.ErrorWithCtx(ctx, "autoCancelContractTimer DoCancelContract err:%v", err)
				}
			} else {
				log.DebugWithCtx(ctx, "autoCancelContractTimer able2cancel contracts:%+v err:%v", tmpC, err)
			}
		}
		if beginTs >= nowTs.Unix() {
			break
		}
		beginTs = beginTs + step
	}
}

// able2cancel 判断是否可以解约
func (s *Server) able2cancel(ctx context.Context, contract *orderMgrStruct.ContractRecord) error {
	//这种状态的直接解约掉
	if contract.Status == utils.SPSYS_RESCIND_CONTRACT {
		return nil
	}
	if contract.Status == utils.INIT_CONTRACT { // 如果24小时之内不回调，也直接解约
		if contract.CreateTime.Before(time.Now().Add(-time.Hour * 24)) {
			return nil
		}
		return errors.New("init contract not the time")
	}
	if contract.Status != utils.SIGN_CONTRACT {
		return errors.New("cancel already ")
	}
	if isLock, _ := s.orderMgr.LockAutoCancelContractWithContract(ctx, contract.ContractId); !isLock {
		return errors.New("get lock fail")
	}

	superplayerInfo, err := s.membershipMgr.GetMembershipInfo(ctx, contract.Uid)
	if err != nil {
		return err
	}

	nowTs := time.Now().Unix()
	passSec := nowTs - superplayerInfo.ExpireTimestamp
	if passSec < s.dyConf.GetCancelContractHours()*3600 {
		return errors.New("not the time")
	}
	return nil
}

// RedemptionUserOrderTimer 核销用户订单
func (s *Server) RedemptionUserOrderTimer(ctx context.Context, now time.Time) {
	log.DebugWithCtx(ctx, "RedemptionUserOrderTimer start")
	ctx, cancelFunc := context.WithTimeout(ctx, time.Minute*20) // 进程锁25分钟，这里20分钟超时
	defer cancelFunc()

	// 1、按照uid结尾数字进行分批操作，防止执行时间过长
	idx, err := s.orderMgr.GetAutoRedemptionUserOrderIndex(ctx, 1)
	if err != nil {
		return
	}
	if idx > 2000 {
		go func() { // 防止溢出
			if _, err := s.orderMgr.GetAutoRedemptionUserOrderIndex(context.Background(), -100); err != nil {
				log.ErrorWithCtx(ctx, "RedemptionUserOrderTimer GetAutoRedemptionUserOrderIndex err:%v", err)
			}
		}()
	}
	idx = idx % 100

	// 2、加分段操作锁并判断是否已经完成分段操作
	if lock, _ := s.orderMgr.LockRedemptionUserOrderIndex(ctx, idx); !lock { // 这里不做释放锁操作，直接等待下次执行周期
		log.InfoWithCtx(ctx, "RedemptionUserOrderTimer is locked, idx:%v", idx)
		return
	}
	if finish, _ := s.orderMgr.ValidRedemptionUserOrderIndexFinish(ctx, idx, time.Now()); finish {
		log.InfoWithCtx(ctx, "RedemptionUserOrderTimer finish, idx:%v", idx)
		return
	}

	// 3、获取分段用户列表
	lastDay := time.Date(now.Year(), now.Month(), now.Day()-1, now.Hour(), 0, 0, 0, time.Local) // 获取昨天的时间，因为这里其实是结算上一天的
	uidList, err := s.membershipMgr.BatchGetUsersWithoutExpireByTime(ctx, uint32(idx), lastDay)
	if err != nil {
		log.ErrorWithCtx(ctx, "RedemptionUserOrderTimer BatchGetUsersWithoutExpireByTime err:%v", err)
		return
	}

	// 4、核销用户订单
	for _, uid := range uidList {
		// 查询会员信息
		info, err := s.membershipMgr.GetMembershipInfo(ctx, uid)
		if err != nil {
			log.ErrorWithCtx(ctx, "sendUseExperienceCardReport GetMembershipInfo fail, uid:%v, err:%v", uid, err)
			return
		}
		packageType := uint32(pb.PackageType_ENUM_PACKAGE_TYPE_NORMAL)
		if info.SvipExpireTimestamp >= time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, time.Local).Unix() { // 如果是SVIP用户
			packageType = uint32(pb.PackageType_ENUM_PACKAGE_TYPE_SVIP)
		}
		if err := s.orderMgr.RedemptionUserOrderDaily(ctx, now, uid, packageType); err != nil {
			log.ErrorWithCtx(ctx, "RedemptionUserOrderTimer RedemptionUserOrder err:%v", err)
			// 发送错误通知
			if err := s.feishu.SendError("核销用户订单失败" + fmt.Sprintf("uid:%v, err:%v", uid, err)); err != nil {
				log.ErrorWithCtx(ctx, "RedemptionUserOrderTimer SendError err:%v", err)
			}
			continue // 这里不做返回，继续执行
		}
	}

	// 5、加分段完成锁
	if err := s.orderMgr.LockRedemptionUserOrderIndexFinish(ctx, idx, time.Now()); err != nil {
		log.ErrorWithCtx(ctx, "RedemptionUserOrderTimer LockRedemptionUserOrderIndexFinish err:%v", err)
	}
}

// autoMakeUpOrderValueTimer 补单核销用户订单月成长值
func (s *Server) autoMakeUpOrderValueTimer(ctx context.Context, now time.Time) {
	log.DebugWithCtx(ctx, "autoMakeUpOrderValueTimer start")
	ctx, cancelFunc := context.WithTimeout(ctx, time.Minute*10)
	defer cancelFunc()

	// 查核销积分订单
	redemptionValueInfos, err := s.orderMgr.GetLastTenMinuteUserRedemptionValueInfo(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "autoMakeUpOrderValueTimer GetLastTenMinuteUserRedemptionValueInfo err:%v", err)
		return
	}

	// 循环操作检查订单生效
	for _, info := range redemptionValueInfos {
		// 变更成长值
		if _, _, err := s.membershipMgr.SettlementOrderScore(ctx, &membershipMgrStruct.SettlementOrderScore{
			Uid:          info.Uid,
			OrderId:      info.TargetOrder,
			PrentOrderId: info.OrderId,
			IncrValue:    int64(info.RedemptionValue),
			IsExpire:     false,
		}, true); err != nil {
			log.ErrorWithCtx(ctx, "autoAddScoreTimer SettlementUserValue err:%v", err)
			// 飞书告警
			if err := s.feishu.SendError("补单核销用户订单月成长值失败" + fmt.Sprintf("uid:%v, err:%v", info.Uid, err)); err != nil {
				log.ErrorWithCtx(ctx, "autoMakeUpOrderValueTimer SendError err:%v", err)
			}
		}
	}
}

// autoMakeUpOrderTimer 补单核销用户订单
func (s *Server) autoMakeUpOrderTimer(ctx context.Context) {
	log.DebugWithCtx(ctx, "autoMakeUpOrderTimer start")
	ctx, cancelFunc := context.WithTimeout(ctx, time.Minute*5)
	defer cancelFunc()

	// 查核销积分订单
	orders, err := s.orderMgr.GetLastFiveMinutesActiveOrderInfo(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "autoMakeUpOrderTimer GetLastFiveMinutesActiveOrderInfo err:%v", err)
		return
	}

	// 循环操作检查订单生效
	for _, order := range orders {
		// 过滤升级回滚订单
		if order.OrderType == int8(pb.EnumOrderType_ORDER_TYPE_ROLLBACK) {
			continue
		}
		// 激活订单权限
		s.ActiveOrder(ctx, order)
	}
}

// RevokeOrder 撤销订单
func (s *Server) RevokeOrder(ctx context.Context, req *pb.RevokeOrderReq) (*pb.RevokeOrderResp, error) {
	resp := &pb.RevokeOrderResp{}
	log.InfoWithCtx(ctx, "RevokeOrder req:%+v", req)

	nTs, err := utils.ParseTimeFromString(req.NotifyTime)
	if err != nil {
		log.ErrorWithCtx(ctx, "RevokeOrder ParseTimeFromString err:%v", err)
		return resp, protocol.NewExactServerError(nil, status.ErrSuperPlayerInvalidPara, "时间格式错误")
	}

	// 校验订单状态
	order, err := s.orderMgr.GetOrder(ctx, req.GetOrderId(), nTs, false)
	if err != nil {
		log.ErrorWithCtx(ctx, "RevokeOrder GetOrder err:%v", err)
		return resp, protocol.NewExactServerError(nil, status.ErrSuperPlayerSysErr, "订单获取失败")
	}
	if order == nil || order.OrderId != req.GetOrderId() || order.Uid != req.GetUid() {
		log.ErrorWithCtx(ctx, "RevokeOrder GetOrder order is nil, req:%+v", req)
		return resp, protocol.NewExactServerError(nil, status.ErrSuperPlayerInvalidPara, "订单不存在")
	}
	if order.Status != int8(pb.EnumOrderStatus_ORDER_PAY_SUCCESS) && order.Status != int8(pb.EnumOrderStatus_ORDER_REVOKE) {
		log.ErrorWithCtx(ctx, "RevokeOrder order status is not success, req:%+v", req)
		return resp, protocol.NewExactServerError(nil, status.ErrSuperPlayerInvalidPara, "订单状态不正确")
	}

	// 撤销订单
	if err := s.revokeOrder(ctx, req, pb.EnumOrderType(order.OrderType), pb.PackageType(order.PackageType), order.CreateTime); err != nil {
		log.ErrorWithCtx(ctx, "RevokeOrder revokeOrder err:%v", err)
		return resp, err
	}

	log.InfoWithCtx(ctx, "RevokeOrder success, req:%+v", req)
	return resp, nil
}

// revokeOrder 撤销订单
func (s *Server) revokeOrder(ctx context.Context, req *pb.RevokeOrderReq, orderType pb.EnumOrderType, pkgType pb.PackageType, createTime time.Time) error {
	// 操作订单相关逻辑
	revokeDays, revokeOrderId, revokeTime, err := s.orderMgr.RevokeOrder(ctx, req.GetUid(), req.GetOrderId(), createTime)
	if err != nil {
		log.ErrorWithCtx(ctx, "revokeOrder RevokeOrder err:%v", err)
		return protocol.NewExactServerError(nil, status.ErrSuperPlayerSysErr, "订单撤销失败")
	}
	if revokeDays == 0 {
		log.InfoWithCtx(ctx, "revokeOrder finish, no need to revoke user privilege, req:%+v", req)
		return nil
	}

	// 异步操作用户权限相关逻辑
	newCtx := grpc.NewContextWithInfo(ctx)
	go func() {
		if err := s.membershipMgr.RevokePrivilege(newCtx, req.GetUid(), revokeDays, revokeOrderId, revokeTime, orderType, pkgType); err != nil {
			log.ErrorWithCtx(ctx, "revokeOrder RevokePrivilege err:%v", err)
		}
	}()

	return nil
}

// revokeOrderMakeUpTimer 撤销订单补单
func (s *Server) revokeOrderMakeUpTimer(ctx context.Context, now time.Time) {
	// 查询流水
	flows, err := s.orderMgr.GetLastMinRevokeFlows(ctx, now)
	if err != nil {
		log.ErrorWithCtx(ctx, "revokeOrderMakeUpTimer GetLastMinRevokeFlows err:%v", err)
		return
	}

	// 循环操作检查订单生效
	for _, flow := range flows {
		// 查询订单
		order, err := s.orderMgr.GetOrder(ctx, flow.OrderId, flow.ServerTime, false)
		if err != nil {
			log.ErrorWithCtx(ctx, "RevokeOrder GetOrder err:%v", err)
			_ = s.feishu.SendError("撤销订单补单失败" + fmt.Sprintf("orderId:%v, err:%v", flow.OrderId, err))
			continue
		}

		if err := s.membershipMgr.RevokePrivilege(ctx, flow.Uid, flow.RevokeDays, flow.RevokeOrderId, flow.CreateTime, pb.EnumOrderType(order.OrderType), pb.PackageType(order.PackageType)); err != nil {
			log.ErrorWithCtx(ctx, "revokeOrder RevokePrivilege err:%v", err)
		}
	}
}

// IsAnnualSvipBeforeActivity 判断活动之前是否是年卡会员
func (s *Server) IsAnnualSvipBeforeActivity(ctx context.Context, req *pb.IsAnnualSvipBeforeActivityReq) (*pb.IsAnnualSvipBeforeActivityResp, error) {
	resp := &pb.IsAnnualSvipBeforeActivityResp{}

	resp.Uid = req.GetUid()
	// 直接查白名单
	if s.activityConf.IsWhiteList(req.GetUid()) {
		resp.IsAnnualSvip = true
		return resp, nil
	}

	// 如果不是白名单就查订单
	isAnnualSvip, err := s.orderMgr.ValidUserIsAnnualSvipBeforeTime(ctx, req.GetUid(), s.activityConf.GetActivityStartTime())
	if err != nil {
		log.ErrorWithCtx(ctx, "IsAnnualSvipBeforeActivity ValidUserIsAnnualSvipBeforeTime err:%v", err)
		return resp, protocol.NewExactServerError(nil, status.ErrSuperPlayerSysErr, "查询失败")
	}
	resp.IsAnnualSvip = isAnnualSvip
	return resp, nil
}

func (s *Server) OpenSuperPlayer(ctx context.Context, req *pb.OpenSuperPlayerReq) (*pb.OpenSuperPlayerResp, error) {
	log.DebugWithCtx(ctx, "OpenSuperPlayer req:%+v", req)
	resp := &pb.OpenSuperPlayerResp{}

	if req.Uid == 0 || req.GetDays() == 0 {
		return resp, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "参数错误！")
	}

	// 判断活动是否开启
	config := s.activityConf.GetExperienceConfigById(req.GetSourceId())
	if config == nil {
		return resp, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "活动不存在！")
	}
	now := time.Now()
	if now.Before(time.Unix(config.StartTime, 0)) || now.After(time.Unix(config.EndTime, 0)) {
		return resp, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "活动未开启！")
	}

	fmtType := "超级玩家"
	if req.GetPackageType() == pb.PackageType_ENUM_PACKAGE_TYPE_SVIP {
		fmtType = "SVIP"
	}
	awardPrivilegeTime := &membershipMgrStruct.AwardPrivilegeTime{
		OrderId:          "experience_card_" + req.GetOrderId(),
		Reason:           fmt.Sprintf("%+v体验卡*%+v天", fmtType, req.GetDays()),
		PackageId:        "",
		PackageName:      "",
		Uid:              req.GetUid(),
		IncrDays:         int64(req.GetDays()),
		PayChannel:       "ExperienceCard",
		PackageType:      uint32(req.GetPackageType()),
		IsYearOrder:      s.dyConf.IsYearPack(req.GetDays()),
		DelayReminder:    false, // 这个参数目前好像没啥用了
		IsUpgrade:        false,
		ServerTime:       time.Unix(req.GetServerTime(), 0),
		IsExperienceCard: true,
	}
	beforeInfo, afterUserInfo, err := s.membershipMgr.SettlementUserPrivilegeTime(ctx, awardPrivilegeTime, false)
	if err != nil { // 这里仅报警，不影响正常流程
		log.ErrorWithCtx(ctx, "NoticeOrderStatus SettlementUserPrivilegeTime err:%v", err)
		return resp, err
	}

	// 发送体验卡上报
	go sendExperienceCardReport(ctx, req.GetUid(), config.Notice, req.GetDays(), req.GetPackageType(), req.GetOrderId())

	// 处理推送
	err = s.membershipMgr.RefreshAndPushMembershipChange(ctx, awardPrivilegeTime, beforeInfo, afterUserInfo, 0)
	if err != nil {
		log.ErrorWithCtx(ctx, "SettlementUserPrivilegeTime OnSuperPlayerInfoChange failed err:%v", err)
	}
	log.DebugWithCtx(ctx, "OpenSuperPlayer success, req:%+v", req)

	return resp, nil
}

// sendExperienceCardReport 上报体验卡数据
func sendExperienceCardReport(ctx context.Context, uid uint32, activityName string, incrDays uint32, packageType pb.PackageType, order string) {

	// 组装上报数据
	data := map[string]interface{}{
		"uid":          uid,
		"source":       activityName,
		"order_id":     order,
		"day":          incrDays,
		"package_type": uint32(packageType),
	}

	// 上报数据
	byErr := bylink.Track(ctx, uint64(uid), "vip_experience_card_issue_log", data, false)
	if byErr != nil {
		log.ErrorWithCtx(ctx, "sendExperienceCardReport fail to bylink.Track error: %v", byErr)
		return
	}
	bylink.Flush()
	log.DebugWithCtx(ctx, "sendExperienceCardReport success, uid:%v, source:%s, day:%v, packageType:%v, order:%s", uid, activityName, incrDays, packageType, order)
}

// checkAllIOSContractStatus 检查所有IOS签约状态
func (s *Server) checkAllIOSContractStatus(ctx context.Context) {
	log.InfoWithCtx(ctx, "checkAllIOSContractStatus start")
	_ = s.coinFeishu.SendInfo("开始检查IOS签约状态")
	isAutoNextTime, isAutoStatus := s.dyConf.GetCheckIOSContractConfig()
	// 分批检查
	limit, offset := 1000, 0
	for {
		// 获取所有IOS签约用户
		contractList, err := s.orderMgr.GetContractsWithIOS(ctx, limit, offset)
		if err != nil {
			log.ErrorWithCtx(ctx, "checkAllIOSContractStatus GetIOSContractList err:%v", err)
			return
		}

		// 循环检查每一个签约信息
		for _, contract := range contractList {
			if contract.MarketId == 6 {
				continue
			}
			if contract.Uid == 0 {
				continue
			}
			// 查询货币签约状态
			data, err := s.coinCli.SignQuery(ctx, contract.ContractId, contract.Uid, contract.MarketId)
			if err != nil {
				log.ErrorWithCtx(ctx, "checkAllIOSContractStatus SignQuery err:%v", err)
				return
			}
			// 校验状态
			var hasContractInfo bool
			for _, coinContractInfo := range data {
				if coinContractInfo.CliContractId != contract.ContractId { // 合约ID不一致，跳过
					continue
				}
				hasContractInfo = true
				if coinContractInfo.Status != contract.Status { // 状态不一致，告警
					if coinContractInfo.Status == "SIGN_CONTRACT" && contract.Status == "SPSYS_SIGN_CONTRACT" {
						err = s.orderMgr.UpdateContractStatus(ctx, contract.Uid, contract.ContractId, coinContractInfo.Status)
						if err != nil {
							log.ErrorWithCtx(ctx, "checkAllIOSContractStatus UpdateContractStatus err:%v", err)
						}
						continue
					}
					if coinContractInfo.Status == "NONE" && contract.Status != "SIGN_CONTRACT" { // 初始状态跳过
						continue
					}
					err = s.coinFeishu.SendErrorNormal(fmt.Sprintf("IOS签约状态不一致，订单ID:%v, 会员系统状态:%v，货币状态:%v, 所属用户uid:%v", contract.ContractId, contract.Status, coinContractInfo.Status, contract.Uid))
					if err != nil {
						log.ErrorWithCtx(ctx, "checkAllIOSContractStatus SendError err:%v", err)
					}
					if isAutoStatus { // 自动修复
						err = s.orderMgr.UpdateContractStatus(ctx, contract.Uid, contract.ContractId, coinContractInfo.Status)
						if err != nil {
							log.ErrorWithCtx(ctx, "checkAllIOSContractStatus UpdateContractStatus err:%v", err)
							continue
						}
						_ = s.coinFeishu.SendInfo(fmt.Sprintf("IOS签约状态不一致，订单ID:%v, 货币状态:%v, 会员系统状态:%v，所属用户uid:%v，已自动修复", contract.ContractId, coinContractInfo.Status, contract.Status, contract.Uid))
					}
					continue
				}
				nextTime, err := utils.ParseTimeFromString(coinContractInfo.NextExecuteTime)
				if err != nil {
					log.ErrorWithCtx(ctx, "checkAllIOSContractStatus ParseTimeFromString err:%v", err)
					continue
				}
				if !nextTime.Equal(contract.NextTime) { // 下次扣款时间不一致，告警
					err = s.coinFeishu.SendWarning(fmt.Sprintf("IOS签约下次扣款时间不一致，订单ID:%v, 货币时间:%v, 会员系统时间:%v，所属用户uid:%v", contract.ContractId, coinContractInfo.NextExecuteTime, contract.NextTime, contract.Uid))
					if err != nil {
						log.ErrorWithCtx(ctx, "checkAllIOSContractStatus SendWarning err:%v", err)
					}
					if isAutoNextTime { // 自动修复
						err = s.orderMgr.UpdateContractNextTime(ctx, contract.Uid, contract.ContractId, nextTime)
						if err != nil {
							log.ErrorWithCtx(ctx, "checkAllIOSContractStatus UpdateContractNextTime err:%v", err)
							continue
						}
						_ = s.coinFeishu.SendInfo(fmt.Sprintf("IOS签约下次扣款时间不一致，订单ID:%v, 货币时间:%v, 会员系统时间:%v，所属用户uid:%v，已自动修复", contract.ContractId, coinContractInfo.NextExecuteTime, contract.NextTime, contract.Uid))
					}
				}
			}
			if !hasContractInfo { // 不存在签约信息，飞书告警
				// 检查用户是否已经注销签约
				signCancellation, err := s.coinCli.CheckSignCancellation(ctx, contract.Uid)
				if err != nil {
					log.ErrorWithCtx(ctx, "checkAllIOSContractStatus CheckSignCancellation err:%v", err)
					return
				}
				if signCancellation { // 如果已经注销了就不需要告警了
					if isAutoStatus {
						err = s.orderMgr.UpdateContractStatus(ctx, contract.Uid, contract.ContractId, "SPSYS_RESCIND_CONTRACT")
						if err != nil {
							log.ErrorWithCtx(ctx, "checkAllIOSContractStatus UpdateContractStatus err:%v", err)
						}
					}
					continue
				}
				err = s.coinFeishu.SendErrorNormal(fmt.Sprintf("货币接口IOS签约信息不存在，订单ID:%v， uid:%v", contract.ContractId, contract.Uid))
				if err != nil {
					log.ErrorWithCtx(ctx, "checkAllIOSContractStatus SendError err:%v", err)
				}
			}
		}
		offset = offset + limit
		if len(contractList) < limit {
			break
		}
	}
	log.InfoWithCtx(ctx, "checkAllIOSContractStatus finish")
	_ = s.coinFeishu.SendInfo("结束检查IOS签约")
}
