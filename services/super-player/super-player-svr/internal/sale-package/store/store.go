package store

import (
	"context"

	"gitlab.ttyuyin.com/avengers/tyr/core/log"
	mysqlConnect "gitlab.ttyuyin.com/avengers/tyr/core/middleware/mysql/connect"
	"gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/mysql"
)

const SuperPlayerPackageCfgTable = "tbl_superplayer_package"
const SuperPlayerSvipPackageCfgTable = "tbl_superplayer_svip_package"

type Store struct {
	db mysql.DBx
}

func NewStore(ctx context.Context, cfg *mysqlConnect.MysqlConfig) (*Store, error) {
	db, err := mysqlConnect.NewClient(ctx, cfg)
	if err != nil {
		return nil, err
	}

	if _, err := db.ExecContext(ctx, createPackageLimitConfigTable); err != nil {
		log.ErrorWithCtx(ctx, "createPackageLimitConfigTable failed, err: %v", err)
		return nil, err
	}

	if _, err := db.ExecContext(ctx, createOrderLimitTable); err != nil {
		log.ErrorWithCtx(ctx, "createOrderLimitTable failed, err: %v", err)
		return nil, err
	}

	if _, err := db.ExecContext(ctx, createGroupPackageSQL); err != nil {
		log.ErrorWithCtx(ctx, "createGroupPackageSQL failed, err: %v", err)
		return nil, err
	}

	s := &Store{
		db: db,
	}
	return s, nil
}

func (s *Store) Close() error {
	return s.db.Close()
}

func (s *Store) Transaction(ctx context.Context, f func(c context.Context, tx mysql.Txx) error) error {
	tx, err := s.db.Beginx()
	if err != nil {
		log.ErrorWithCtx(ctx, "Transaction BeginTx fail err %v", err)
		return err
	}

	err = f(ctx, tx)
	if err != nil {
		log.ErrorWithCtx(ctx, "Transaction   err %v", err)
		_ = tx.Rollback()
		return err
	}

	return tx.Commit()
}

func getSuperPlayerPackageCfgTableName(isSvip bool) string {
	if isSvip {
		return SuperPlayerSvipPackageCfgTable
	}
	return SuperPlayerPackageCfgTable
}

var createPackageLimitConfigTable = `CREATE TABLE IF NOT EXISTS tbl_superplayer_package_limit_config (
  id BIGINT (20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  limit_name VARCHAR(255) NOT NULL COMMENT '限购套餐名称',
  package_type tinyint(4) NOT NULL COMMENT '套餐类型',
  order_type tinyint(4) NOT NULL COMMENT '订单类型',
  package_id_info VARCHAR(1023) NOT NULL COMMENT '关联ID列表JSON',
  limit_start_time DATETIME NOT NULL COMMENT '限购开始时间',
  limit_end_time DATETIME NOT NULL COMMENT '限购结束时间',
  limit_count INT(10) UNSIGNED NOT NULL COMMENT '限购次数',
  remark VARCHAR(1023) DEFAULT NULL COMMENT '运营备注',
  create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  creator VARCHAR(255) NOT NULL COMMENT '创建人',
  update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',
  updater VARCHAR(255) NOT NULL COMMENT '更新人',
  del_flag tinyint(4) NOT NULL DEFAULT 0 COMMENT '删除标记，用于软删除',
  PRIMARY KEY (id)
) ENGINE = INNODB DEFAULT CHARSET = utf8 COMMENT = '会员套餐限购配置';`

var createOrderLimitTable = `CREATE TABLE IF NOT EXISTS tbl_superplayer_order_limit (
  id BIGINT (20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  uid BIGINT (20) UNSIGNED NOT NULL COMMENT 'uid',
  order_id VARCHAR (255) NOT NULL COMMENT '订单id',
  limit_id BIGINT (20) UNSIGNED NOT NULL COMMENT '限购配置id',
  create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',
  PRIMARY KEY (id),
  UNIQUE KEY uniq_idx_order ( order_id ),
  KEY idx_uid_limit ( uid, limit_id )
) ENGINE = INNODB DEFAULT CHARSET = utf8 COMMENT = '会员订单限购记录表';`
