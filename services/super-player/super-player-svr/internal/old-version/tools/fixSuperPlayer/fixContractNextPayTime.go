package main

import (
	"fmt"
	"golang.52tt.com/services/super-player/super-player-svr/internal/old-version/mysql"
	"strings"
	"time"
)

func getContractList(isTest bool) {
	nowTs := time.Now()
	ts := time.Date(nowTs.Year(), nowTs.Month(), nowTs.Day(), 0, 0, 0, 0, time.Local).AddDate(0, 0, 1)
	var count = 1024
	var contractList = make([]*mysql.ContractRecord, 0)
	readGormDB.Table("tbl_superplayer_contract_record_v2").Where("status = 'SIGN_CONTRACT' and package_id=? and next_time >= ?", 22, ts).
		Order("last_time asc").Limit(count).Find(&contractList)

	fmt.Printf("\n contractList sz:%v \n", len(contractList))

	for idx, _ := range contractList {
		ok := doFixContractNextPayTime(contractList[idx])
		if ok && isTest {
			break
		}
	}
}

func doFixContractNextPayTime(contract *mysql.ContractRecord) bool {
	tmpUserInfo := &mysql.SuperPlayerInfo{Uid: contract.Uid}
	result := readGormDB.First(tmpUserInfo)
	if nil != result.Error {
		fmt.Printf("\n SuperPlayerInfo first err:%v \n", result.Error)
		return false
	}

	var orderList = make([]*mysql.OrderRecord, 0)
	lastMt := time.Now().AddDate(0, -1, 0)
	orderInfo := &mysql.OrderRecord{ServerTime: lastMt}
	readGormDB.Table(orderInfo.TableName()).Where("package_id=? and status=? and uid=? and pay_channel='ALIPAY_PERIOD'", 22, 1, contract.Uid).
		Order("create_time DESC").Limit(1024).Find(&orderList)

	if len(orderList) != 1 {
		fmt.Printf("\n orderList sz != 1 uid=%v \n", contract.Uid)
		return false
	}

	if orderList[0].CreateTime.AddDate(0, 0, 3).Day() != contract.LastTime.Day() {
		fmt.Printf("\n CreateTime.Day() != LastTime.Day() uid=%v \n", contract.Uid)
		return false
	}

	cValues := make(map[string]interface{})
	cValues["next_time"] = contract.LastTime.AddDate(0, 0, 27)
	gormDB.Table("tbl_superplayer_contract_record_v2").Where("contract_id=? and uid=?", contract.ContractID, contract.Uid).Updates(cValues).Debug()

	fmt.Printf("\n fix contract:%+v \n", *contract)

	return true
}

func fixContractNextTime(isTest bool) {
	getContractList(isTest)
}

func fixNextPayTime(days uint32, isTest bool) {
	var contractList = make([]*mysql.ContractRecord, 0)
	readGormDB.Table("tbl_superplayer_contract_record_v2").Where("status='SIGN_CONTRACT' and package_id=? and datediff(next_time,last_time)>=?", 22, 1).
		Order("last_time asc").Find(&contractList)

	nowTs := time.Now()

	fixCnt := 0
	checkFixCnt := 0
	checkFixCnt2 := 0
	checkFixCnt3 := 0
	checkFixCnt4 := 0
	checkFixCnt5 := 0
	for _, contract := range contractList {
		tmpUserInfo := &mysql.SuperPlayerInfo{Uid: contract.Uid}
		result := readGormDB.First(tmpUserInfo)
		if nil != result.Error {
			fmt.Printf("\n SuperPlayerInfo first err:%v \n", result.Error)
			continue
		}
		if tmpUserInfo.ExpireTime.After(contract.NextTime) {
			continue
		} else {
			fixCnt++
		}

		var orderList = make([]*mysql.OrderRecord, 0)
		lastMt := time.Now().AddDate(0, -1, 0)
		orderInfo := &mysql.OrderRecord{ServerTime: lastMt}
		readGormDB.Table(orderInfo.TableName()).Where("package_id=? and status=? and uid=?", 22, 1, contract.Uid).
			Order("create_time DESC").Limit(1024).Find(&orderList)

		if len(orderList) != 1 {
			continue
		}

		if contract.CreateTime.After(orderList[0].CreateTime) {
			continue
		}

		fmt.Printf("\n Before1:%+v \n", contract)

		checkDelayTime := time.Date(nowTs.Year(), nowTs.Month(), nowTs.Day(), 0, 0, 0, 0, time.Local).AddDate(0, 0, int(days))
		//5月的单算出6月扣款时间
		orderNext := orderList[0].CreateTime.AddDate(0, 0, 30)
		if orderNext.Before(contract.NextTime) {
			checkFixCnt++

			fmt.Printf("\n Before:%+v \n", contract)

			if strings.Contains(orderList[0].OrderID, "auto") {
				checkFixCnt2++
				lastTs := orderNext.AddDate(0, 0, 3) //tmpUserInfo.ExpireTime
				nextTs := orderNext
				if nextTs.After(tmpUserInfo.ExpireTime) {
					lastTs = tmpUserInfo.ExpireTime
					nextTs = lastTs.AddDate(0, 0, -3)
					checkFixCnt4++
				}

				if checkDelayTime.After(nextTs) {
					checkFixCnt3++
					fmt.Printf("\n checkFix:%+v     lastTs:%v  nextTs:%v\n", contract, lastTs, nextTs)

					if !isTest {
						cValues := make(map[string]interface{})
						cValues["next_time"] = nextTs
						cValues["last_time"] = lastTs
						gormDB.Debug()
						gormDB.Table("tbl_superplayer_contract_record_v2").Where("contract_id=? and uid=?", contract.ContractID, contract.Uid).Updates(cValues)

						delKey := fmt.Sprintf("super_player_contract_v2_%v", contract.Uid)
						redisDB.Del(delKey)
					}
				}
			}
		}
	}
	fmt.Printf("\n fixNextPayTime fixCnt:%v checkFixCnt:%v checkFixCnt2:%v checkFixCnt3:%v checkFixCnt4:%v checkFixCnt5:%v contractList:%v \n",
		fixCnt, checkFixCnt, checkFixCnt2, checkFixCnt3, checkFixCnt4, checkFixCnt5, len(contractList))
}
