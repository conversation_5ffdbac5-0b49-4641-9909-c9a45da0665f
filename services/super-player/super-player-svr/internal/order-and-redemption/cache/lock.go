package cache

import (
	"context"
	"fmt"
	"time"

	"gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/redis"
	"gitlab.ttyuyin.com/tt-infra/tyr/log"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/protocol/common/status"
	"golang.52tt.com/services/super-player/super-player-svr/internal/pkg/utils"
)

const (
	autoPayLockKey                  = "super_player_auto_pay_lock"
	autoPayContractOffSetFinishKey  = "auto_pay_contract_list_off"
	autoCancelLockKey               = "super_player_auto_cancel_lock"
	autoRedemptionUserOrderIndex    = "super_player_auto_redemption_user_order_index"
	autoGenOrderSumReportOptLockKey = "super_player_auto_gen_order_sum_report_opt"
)

// lock 获取锁
func (c *Cache) lock(ctx context.Context, lockKey string, value string, expire time.Duration) (bool, error) {
	log.DebugWithCtx(ctx, "lock lockKey:%v", lockKey)
	isLock, err := c.cmder.SetNX(ctx, lockKey, value, expire).Result()
	if err != nil {
		return false, err
	}
	if !isLock {
		return false, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "get lock fail")
	}
	return true, nil
}

// unLock 释放锁
func (c *Cache) unLock(ctx context.Context, lockKey string) {
	log.DebugWithCtx(ctx, "unLock lockKey:%v", lockKey)
	c.cmder.Del(ctx, lockKey)
}

// IsLocked 判断是否被锁
func (c *Cache) isLocked(ctx context.Context, lockKey string) (bool, error) {
	log.DebugWithCtx(ctx, "isLocked lockKey:%v", lockKey)
	isLock, err := c.cmder.Exists(ctx, lockKey).Result()
	if err != nil {
		return false, err
	}

	return isLock == 1, nil
}

// getLimitPurchaseKey 获取限购锁的key
func getLimitPurchaseKey(uid uint32, keyword uint32) string {
	return fmt.Sprintf("sp_order_limit_single_purchase_%+v_%+v", uid, keyword)
}

// LockOrderLimitPurchase 订单限购加锁
func (c *Cache) LockOrderLimitPurchase(ctx context.Context, uid uint32, limitId uint32, orderId string, expire time.Duration) (bool, error) {
	isLock, err := c.cmder.SetNX(ctx, getLimitPurchaseKey(uid, limitId), orderId, expire).Result()
	if err != nil {
		log.ErrorWithCtx(ctx, "LockOrderLimitPurchase SetNX err:%v", err)
		return false, err
	}

	return isLock, nil
}

// UnlockOrderLimitPurchase 解锁订单限购
func (c *Cache) UnlockOrderLimitPurchase(ctx context.Context, uid uint32, limitId uint32, orderId string) error {

	// 先判断是否是当前进程锁住的
	lockOrderId, err := c.cmder.Get(ctx, getLimitPurchaseKey(uid, limitId)).Result()
	if err != nil {
		log.ErrorWithCtx(ctx, "UnlockOrderLimitPurchase Get err:%v", err)
		return err
	}
	if lockOrderId != orderId {
		return nil
	}

	// 如果是当前进程锁住的就解锁
	c.unLock(ctx, getLimitPurchaseKey(uid, limitId))
	return nil
}

func getOneMinLimitPurchaseKey(uid uint32, keyword uint32) string {
	return fmt.Sprintf("sp_order_limit_one_min_purchase_%+v_%+v", uid, keyword)
}

// LockOrderOneMinLimitPurchase 订单限购加锁
func (c *Cache) LockOrderOneMinLimitPurchase(ctx context.Context, uid uint32, limitId uint32, expire time.Duration) (bool, error) {
	isLock, err := c.cmder.SetNX(ctx, getOneMinLimitPurchaseKey(uid, limitId), uid, expire).Result()
	if err != nil {
		log.ErrorWithCtx(ctx, "LockOrderOneMinLimitPurchase SetNX err:%v", err)
		return false, err
	}

	return isLock, nil
}

// UnlockOrderOneMinLimitPurchase 解锁订单限购
func (c *Cache) UnlockOrderOneMinLimitPurchase(ctx context.Context, uid uint32, limitId uint32) {
	c.unLock(ctx, getOneMinLimitPurchaseKey(uid, limitId))
}

// getOrderOperateLockKey 获取订单操作锁的key
func getOrderOperateLockKey(uid uint32) string {
	return fmt.Sprintf("sp_order_oprate_user_%+v", uid)
}

// LockOrderOperate 订单操作加锁
func (c *Cache) LockOrderOperate(ctx context.Context, uid uint32, orderId string, expire time.Duration) (bool, error) {
	isLock, err := c.cmder.SetNX(ctx, getOrderOperateLockKey(uid), orderId, expire).Result()
	if err != nil {
		log.ErrorWithCtx(ctx, "LockOrderOperate SetNX err:%v", err)
		return false, err
	}

	return isLock, nil
}

// UnlockOrderOperate 解锁订单操作
func (c *Cache) UnlockOrderOperate(ctx context.Context, uid uint32, orderId string) error {

	// 先判断是否是当前进程锁住的
	lockOrderId, err := c.cmder.Get(ctx, getOrderOperateLockKey(uid)).Result()
	if err != nil {
		log.ErrorWithCtx(ctx, "UnlockOrderOperate Get err:%v", err)
		return err
	}
	if lockOrderId != orderId {
		return nil
	}

	// 如果是当前进程锁住的就解锁
	c.unLock(ctx, getOrderOperateLockKey(uid))
	return nil
}

func getPlaceOrderLockKey(uid uint32) string {
	return fmt.Sprintf("place_order_%v", uid)
}

// LockPlaceOrder 下单加锁
func (c *Cache) LockPlaceOrder(ctx context.Context, uid uint32, orderId string, expire time.Duration) (bool, error) {
	isLock, err := c.cmder.SetNX(ctx, getPlaceOrderLockKey(uid), orderId, expire).Result()
	if err != nil {
		log.ErrorWithCtx(ctx, "LockPlaceOrder SetNX err:%v", err)
		return false, err
	}
	return isLock, nil
}

// UnlockPlaceOrder 解锁下单
func (c *Cache) UnlockPlaceOrder(ctx context.Context, uid uint32, orderId string) error {
	// 判断锁是否是当前进程锁住的
	lockOrderId, err := c.cmder.Get(ctx, getPlaceOrderLockKey(uid)).Result()
	if err != nil {
		log.ErrorWithCtx(ctx, "UnlockPlaceOrder Get err:%v", err)
		return err
	}
	if lockOrderId != orderId {
		return nil
	}
	c.unLock(ctx, getPlaceOrderLockKey(uid))
	return nil
}

func genOrderLockKey(uid uint32, packageType uint32) string {
	if packageType == 0 {
		return fmt.Sprintf("super_player_auto_order_v2_%v", uid)
	}
	return fmt.Sprintf("super_player_auto_order_v2_%+v_%+v", uid, packageType)
}

// AutoOrderIntervalLock 会员自动下单间隔
func (c *Cache) AutoOrderIntervalLock(ctx context.Context, uid uint32, packageType uint32, orderId string, duration time.Duration) error {

	key := genOrderLockKey(uid, packageType)
	err := protocol.NewExactServerError(nil, status.ErrSuperPlayerInvalidPara, fmt.Sprintf("还有未完成的订单，%v秒后再来吧～", duration.Seconds()))
	ts := c.cmder.TTL(ctx, key).Val()
	if ts.Seconds() > 0 {
		err = protocol.NewExactServerError(nil, status.ErrSuperPlayerInvalidPara, fmt.Sprintf("还有未完成的订单，%v秒后再来吧～", ts.Seconds()))
		return err
	}

	pipe := c.cmder.Pipeline()
	pipe.ZAdd(ctx, key, &redis.Z{
		Score:  float64(time.Now().Unix()),
		Member: orderId,
	})
	pipe.Expire(ctx, key, duration)
	if _, err := pipe.Exec(ctx); err != nil {
		log.ErrorWithCtx(ctx, "PlayerAutoOrderInterval err:%v", err)
	}

	val := c.cmder.ZCard(ctx, key).Val()
	if val > 1 {
		return err
	}
	return nil
}

// UnLockAutoOrderInterval 解锁会员自动下单间隔
func (c *Cache) UnLockAutoOrderInterval(ctx context.Context, uid uint32, packageType uint32, orderId string) {
	if err := c.cmder.ZRem(ctx, genOrderLockKey(uid, packageType), orderId).Err(); err != nil {
		log.ErrorWithCtx(ctx, "UnLockAutoOrderInterval err:%v", err)
	}
}

func getReplacementOrderLockKey(order string) string {
	return fmt.Sprintf("sp_Order_ReplacementOrder_%v", order)
}

// LockReplacementOrder 锁定补单
func (c *Cache) LockReplacementOrder(ctx context.Context, order string, expire time.Duration) (bool, error) {
	return c.lock(ctx, getReplacementOrderLockKey(order), order, expire)
}

// LockOrderId 锁定订单ID
func (c *Cache) LockOrderId(ctx context.Context, orderId string, expire time.Duration) (bool, error) {
	return c.lock(ctx, orderId, orderId, expire)
}

// LockAutoPay 自动扣款操作锁
func (c *Cache) LockAutoPay(ctx context.Context, expire time.Duration) (bool, error) {
	return c.lock(ctx, autoPayLockKey, "1", expire)
}

// UnlockAutoPay 解锁自动扣款
func (c *Cache) UnlockAutoPay(ctx context.Context) {
	c.unLock(ctx, autoPayLockKey)
}

// SetAutoPayContractOffSetFinishKey 设置自动扣款合约列表时间偏移量完成锁
func (c *Cache) SetAutoPayContractOffSetFinishKey(ctx context.Context, val int64, expire time.Duration) error {
	_, err := c.cmder.Set(ctx, autoPayContractOffSetFinishKey, val, expire).Result()
	if err != nil {
		log.ErrorWithCtx(ctx, "SetAutoPayContractOffSetFinishKey err:%v", err)
		return err
	}
	return nil
}

// GetAutoPayContractOffSetFinishKey 获取自动扣款合约列表时间偏移量已完成的时间
func (c *Cache) GetAutoPayContractOffSetFinishKey(ctx context.Context) (int64, error) {
	timeTs, err := c.cmder.Get(ctx, autoPayContractOffSetFinishKey).Int64()
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAutoPayContractOffSetFinishKey err:%v", err)
		return 0, err
	}
	return timeTs, nil
}

// fiveDayRenewalNotifyLockKey 五天续费通知锁
func fiveDayRenewalNotifyLockKey(contractId string, nextPayTs int64) string {
	return fmt.Sprintf("five_day_renew_notify_lock_%v_%v", contractId, nextPayTs)
}

// LockFiveDayRenewalNotify 锁定五天续费通知
func (c *Cache) LockFiveDayRenewalNotify(ctx context.Context, contractId string, nextPayTs int64, expire time.Duration) (bool, error) {
	lock, err := c.lock(ctx, fiveDayRenewalNotifyLockKey(contractId, nextPayTs), "1", expire)
	if err != nil {
		log.ErrorWithCtx(ctx, "lockFiveDayRenewalNotify err:%v", err)
		return false, err
	}
	return lock, nil
}

// CancelContractNotifyLockKey 解约通知锁
func CancelContractNotifyLockKey(contractId string, nextPayTs int64) string {
	return fmt.Sprintf("cancel_contract_notice_lock_%v_%v", contractId, nextPayTs)
}

// LockCancelContractNotify 锁定解约通知
func (c *Cache) LockCancelContractNotify(ctx context.Context, contractId string, nextPayTs int64, expire time.Duration) (bool, error) {
	lock, err := c.lock(ctx, CancelContractNotifyLockKey(contractId, nextPayTs), "1", expire)
	if err != nil {
		log.ErrorWithCtx(ctx, "lockCancelContractNotify err:%v", err)
		return false, err
	}
	return lock, nil
}

func getAutoPayNoticeLockKey(uid uint32, nextTime time.Time) string {
	return fmt.Sprintf("super_player_auto_pay_notice_%v_%v", uid, utils.GetDayStr(nextTime))
}

// LockAutoPayNotice 锁定自动扣款通知
func (c *Cache) LockAutoPayNotice(ctx context.Context, uid uint32, nextTime time.Time, expire time.Duration) (bool, error) {
	return c.lock(ctx, getAutoPayNoticeLockKey(uid, nextTime), "1", expire)
}

func getLockAutoCancelContractWithContractLockKey(contractId string) string {
	return fmt.Sprintf("sp_auto_cancel_contract_%v", contractId)
}

// LockAutoCancelContractWithContract 锁定自动解约操作
func (c *Cache) LockAutoCancelContractWithContract(ctx context.Context, contractId string, expire time.Duration) (bool, error) {
	return c.lock(ctx, getLockAutoCancelContractWithContractLockKey(contractId), "1", expire)
}

// LockAutoCancelContract 锁定自动解约操作
func (c *Cache) LockAutoCancelContract(ctx context.Context, expire time.Duration) (bool, error) {
	return c.lock(ctx, autoCancelLockKey, "1", expire)
}

// GetAutoRedemptionUserOrderIndex 获取自动核销用户订单索引
func (c *Cache) GetAutoRedemptionUserOrderIndex(ctx context.Context, val int64) (int64, error) {
	idx, err := c.cmder.IncrBy(ctx, autoRedemptionUserOrderIndex, val).Result()
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAutoRedemptionUserOrderIndex IncrBy err:%v", err)
		return 0, err
	}
	return idx, nil
}

func getRedemptionUserOrderIndexLockKey(index int64) string {
	return fmt.Sprintf("redemption_user_order_index_%+v", index)
}

// LockRedemptionUserOrderIndex 锁定核销用户订单索引
func (c *Cache) LockRedemptionUserOrderIndex(ctx context.Context, index int64, expire time.Duration) (bool, error) {
	return c.lock(ctx, getRedemptionUserOrderIndexLockKey(index), "1", expire)
}

func getRedemptionUserOrderIndexFinishKey(index int64, finishTime time.Time) string {
	return fmt.Sprintf("redemption_user_order_index_finish_%+v_%+v", index, utils.GetDayStr(finishTime))
}

// LockRedemptionUserOrderIndexFinish 锁定核销用户订单索引完成
func (c *Cache) LockRedemptionUserOrderIndexFinish(ctx context.Context, index int64, finishTime time.Time) error {
	key := getRedemptionUserOrderIndexFinishKey(index, finishTime)
	err := c.cmder.SetEX(ctx, key, finishTime, time.Hour*48).Err()
	if err != nil {
		log.ErrorWithCtx(ctx, "LockRedemptionUserOrderIndexFinish err:%v", err)
	}
	return err
}

// ValidRedemptionUserOrderIndexFinish 核销用户订单索引是否已经完成
func (c *Cache) ValidRedemptionUserOrderIndexFinish(ctx context.Context, index int64, finishTime time.Time) (bool, error) {
	key := getRedemptionUserOrderIndexFinishKey(index, finishTime)
	result, err := c.cmder.Get(ctx, key).Result()
	if err != nil {
		if err == redis.Nil {
			return false, nil
		}
		log.ErrorWithCtx(ctx, "ValidRedemptionUserOrderIndexFinish err:%v", err)
		return false, err
	}
	return result != "", nil
}

func getRedemptionUserDayFinishLockKey(uid uint32, dayTime time.Time) string {
	return fmt.Sprintf("super_player_redemption_day_finish_%v_%v", uid, utils.GetDayStr(dayTime))
}

// LockRedemptionUserDayFinish 锁定核销用户订单索引完成
func (c *Cache) LockRedemptionUserDayFinish(ctx context.Context, uid uint32, dayTime time.Time) error {
	key := getRedemptionUserDayFinishLockKey(uid, dayTime)
	err := c.cmder.SetEX(ctx, key, dayTime.String(), time.Hour*48).Err()
	if err != nil {
		log.ErrorWithCtx(ctx, "LockRedemptionUserOrderIndexFinish err:%v", err)
	}
	return nil
}

// ValidRedemptionUserDayFinish 核销用户订单索引是否已经完成
func (c *Cache) ValidRedemptionUserDayFinish(ctx context.Context, uid uint32, dayTime time.Time) (bool, error) {
	key := getRedemptionUserDayFinishLockKey(uid, dayTime)
	result, err := c.cmder.Get(ctx, key).Result()
	if err != nil {
		if err == redis.Nil {
			return false, nil
		}
		log.ErrorWithCtx(ctx, "ValidRedemptionUserOrderIndexFinish err:%v", err)
		return false, err
	}
	return result != "", nil
}

func getRedemptionUserDayOptLockKey(uid uint32, dayTime time.Time) string {
	return fmt.Sprintf("super_player_redemption_day_finish_opt_%v_%v", uid, utils.GetDayStr(dayTime))
}

// LockRedemptionUserDayOpt 锁定核销用户订单索引
func (c *Cache) LockRedemptionUserDayOpt(ctx context.Context, uid uint32, dayTime time.Time) (bool, error) {
	return c.lock(ctx, getRedemptionUserDayOptLockKey(uid, dayTime), "1", time.Minute)
}

// UnlockRedemptionUserDayOpt 解锁核销用户订单索引
func (c *Cache) UnlockRedemptionUserDayOpt(ctx context.Context, uid uint32, dayTime time.Time) {
	c.unLock(ctx, getRedemptionUserDayOptLockKey(uid, dayTime))
}

func getRedemptionUserValueDayFinishLockKey(uid uint32, dayTime time.Time) string {
	return fmt.Sprintf("super_player_redemption_value_day_finish_%v_%v", uid, utils.GetDayStr(dayTime))
}

func getRedemptionUserValueDayOptLockKey(uid uint32, dayTime time.Time) string {
	return fmt.Sprintf("super_player_redemption_value_day_opt_%v_%v", uid, utils.GetDayStr(dayTime))
}

// LockRedemptionUserValueDayOpt 锁定核销用户成长值日操作
func (c *Cache) LockRedemptionUserValueDayOpt(ctx context.Context, uid uint32, dayTime time.Time) (bool, error) {
	return c.lock(ctx, getRedemptionUserValueDayOptLockKey(uid, dayTime), "1", time.Minute)
}

// UnlockRedemptionUserValueDayOpt 解锁核销用户成长值日操作
func (c *Cache) UnlockRedemptionUserValueDayOpt(ctx context.Context, uid uint32, dayTime time.Time) {
	c.unLock(ctx, getRedemptionUserValueDayOptLockKey(uid, dayTime))
}

// LockRedemptionUserValueDayFinish 锁定核销用户成长值日完成
func (c *Cache) LockRedemptionUserValueDayFinish(ctx context.Context, uid uint32, dayTime time.Time) error {
	key := getRedemptionUserValueDayFinishLockKey(uid, dayTime)
	err := c.cmder.SetEX(ctx, key, dayTime.String(), time.Hour*48).Err()
	if err != nil {
		log.ErrorWithCtx(ctx, "LockRedemptionUserValueDayFinish err:%v", err)
	}
	return nil
}

// ValidRedemptionUserValueDayFinish 核销用户成长值日是否已经完成
func (c *Cache) ValidRedemptionUserValueDayFinish(ctx context.Context, uid uint32, dayTime time.Time) (bool, error) {
	key := getRedemptionUserValueDayFinishLockKey(uid, dayTime)
	result, err := c.cmder.Get(ctx, key).Result()
	if err != nil {
		if err == redis.Nil {
			return false, nil
		}
		log.ErrorWithCtx(ctx, "ValidRedemptionUserValueDayFinish err:%v", err)
		return false, err
	}
	return result != "", nil
}

func getUserPlaceUpgradeOrderLockKey(uid uint32) string {
	return fmt.Sprintf("user_place_upgrade_order_lock_%v", uid)
}

// LockUserPlaceUpgradeOrder 锁定用户下单升级
func (c *Cache) LockUserPlaceUpgradeOrder(ctx context.Context, uid uint32, orderId string, expire time.Duration) (bool, error) {
	return c.lock(ctx, getUserPlaceUpgradeOrderLockKey(uid), orderId, expire)
}

// UnlockUserPlaceUpgradeOrder 解锁用户下单升级
func (c *Cache) UnlockUserPlaceUpgradeOrder(ctx context.Context, uid uint32, orderId string) {
	result, err := c.cmder.Get(ctx, getUserPlaceUpgradeOrderLockKey(uid)).Result()
	if err != nil {
		log.ErrorWithCtx(ctx, "UnlockUserPlaceUpgradeOrder err:%v", err)
		return
	}
	if result == orderId { // 只有指定订单锁住的才解锁
		c.unLock(ctx, getUserPlaceUpgradeOrderLockKey(uid))
		log.InfoWithCtx(ctx, "UnlockUserPlaceUpgradeOrder orderId:%v", orderId)
		return
	}
	log.InfoWithCtx(ctx, "UnlockUserPlaceUpgradeOrder orderId:%v not match", orderId)
}

// LockGenReportOpt 锁定生成报表操作
func (c *Cache) LockGenReportOpt(ctx context.Context) (bool, error) {
	return c.lock(ctx, autoGenOrderSumReportOptLockKey, "1", time.Hour)
}

// UnlockGenReportOpt 解锁生成报表操作
func (c *Cache) UnlockGenReportOpt(ctx context.Context) {
	c.unLock(ctx, autoGenOrderSumReportOptLockKey)
}

func getGenReportFinishLockKey(dayTime time.Time) string {
	return fmt.Sprintf("super_player_auto_gen_order_sum_report_finish_%+v", utils.GetMonStr(dayTime))
}

// LockGenReportFinish 锁定生成报表是否已经完成
func (c *Cache) LockGenReportFinish(ctx context.Context, dayTime time.Time) error {
	err := c.cmder.SetEX(ctx, getGenReportFinishLockKey(dayTime), dayTime.String(), time.Hour*24*30).Err()
	if err != nil {
		log.ErrorWithCtx(ctx, "LockGenReportFinish err:%v", err)
	}
	return nil
}

// ValidGenReportFinish 判断生成报表是否已经完成
func (c *Cache) ValidGenReportFinish(ctx context.Context, dayTime time.Time) (bool, error) {
	result, err := c.cmder.Get(ctx, getGenReportFinishLockKey(dayTime)).Result()
	if err != nil {
		if err == redis.Nil {
			return false, nil
		}
		log.ErrorWithCtx(ctx, "ValidGenReportFinish err:%v", err)
		return false, err
	}
	return result != "", nil
}
