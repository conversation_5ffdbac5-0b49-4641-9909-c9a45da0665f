package main

import (
	"context"
	"fmt"
	"time"

	"golang.52tt.com/pkg/config"

	pb "golang.52tt.com/protocol/services/superplayerdress"
	"golang.52tt.com/services/super-player/super-player-dress/conf"
	"golang.52tt.com/services/super-player/super-player-dress/store"
)

func main() {
	sc := &conf.ServiceConfig{
		Environment: "debug",
		MysqlConfig: &config.MysqlConfig{
			//Host: "**************", //云测
			//Host:     "*************", //内网
			Host:     "***********", //线上
			Port:     3306,
			Protocol: "tcp",
			//Database: "appsvr",
			Database: "spdress", //线上
			Charset:  "utf8mb4",
			UserName: "godman",
			Password: "thegodofman",
		},
		RedisConfig: &config.RedisConfig{
			//Host:         "*************",
			Host:         "************", //线上
			Port:         6379,
			Protocol:     "tcp",
			PingInterval: 300,
			PoolSize:     100,
			DB:           1,
		},
	}

	st, err := store.NewStore(sc)
	if err != nil {
		fmt.Println(err)
		return
	}

	st.Init()
	AddRoomSuit(st)
	AddSpecialConcern(st)
	AddChatBubble(st)
	AddChatBg(st)
}

func AddRoomSuit(st *store.Store) {
	//房间套装草地 1
	{
		var dressConfig store.DressConfig
		dressConfig.ID = 1
		dressConfig.Level = 1
		dressConfig.DressType = uint32(pb.DressType_DRESS_TYPE_ROOM_SUIT)
		dressConfig.ResourceUrl = `https://ga-album-cdnqn.52tt.com/tt-server/20210531154808_87450318.zip`
		dressConfig.ResourceMd5 = "9441d7fb0e1254c188ec26191add61a9"

		dressConfig.WebListUrl = `https://ga-album-cdnqn.52tt.com/tt-server/20210531154817_31374293.png`
		dressConfig.WebPreviewUrl = `https://ga-album-cdnqn.52tt.com/tt-server/20210531174730_15878455.png`
		dressConfig.WebTopUrl = `https://ga-album-cdnqn.52tt.com/tt-server/20210531154151_87910593.png`
		dressConfig.WebConfig = ``
		dressConfig.WebVideoUrl = "https://ga-album-cdnqn.52tt.com/tt-server/20210531154844_93499414.mp4"
		dressConfig.WebAudioUrl = ""
		dressConfig.WebStaticUrl = "https://ga-album-cdnqn.52tt.com/tt-server/20210531154835_68791823.png"
		dressConfig.Name = fmt.Sprintf("房间套装草地")
		dressConfig.IsDefault = 1
		dressConfig.CreateTime = time.Now()
		st.AddDressConfig(context.Background(), &dressConfig)
	}
	//房间套装梦幻星空
	{
		var dressConfig store.DressConfig
		dressConfig.ID = 2
		dressConfig.Level = 1
		dressConfig.DressType = uint32(pb.DressType_DRESS_TYPE_ROOM_SUIT)
		dressConfig.ResourceUrl = `https://ga-album-cdnqn.52tt.com/tt-server/20210628095243_98324925.zip`
		dressConfig.ResourceMd5 = "8b6e957f40c5fc5f2fd90594a4dc6b09"

		dressConfig.WebListUrl = `https://ga-album-cdnqn.52tt.com/tt-server/20210628095437_17676009.png`
		dressConfig.WebPreviewUrl = `https://ga-album-cdnqn.52tt.com/tt-server/20210628100050_22946682.png`
		dressConfig.WebTopUrl = ``
		dressConfig.WebConfig = ``
		dressConfig.WebVideoUrl = "https://ga-album-cdnqn.52tt.com/tt-server/20210628095513_36991151.mp4"
		dressConfig.WebAudioUrl = ""
		dressConfig.WebStaticUrl = "https://ga-album-cdnqn.52tt.com/tt-server/20210628100022_45129310.png"
		dressConfig.Name = fmt.Sprintf("房间套装梦幻星空")
		dressConfig.IsDefault = 0
		dressConfig.CreateTime = time.Now()
		st.AddDressConfig(context.Background(), &dressConfig)
	}

	//房间套装情侣
	{
		var dressConfig store.DressConfig
		dressConfig.ID = 3
		dressConfig.Level = 1
		dressConfig.DressType = uint32(pb.DressType_DRESS_TYPE_ROOM_SUIT)
		dressConfig.ResourceUrl = `https://ga-album-cdnqn.52tt.com/tt-server/20210630180225_49916021.zip`
		dressConfig.ResourceMd5 = "b9189d487d4e00c36cb77262afef8fea"

		dressConfig.WebListUrl = `https://ga-album-cdnqn.52tt.com/tt-server/20210630180430_80781509.jpg`
		dressConfig.WebPreviewUrl = `https://ga-album-cdnqn.52tt.com/tt-server/20210630180537_73621141.jpg`
		dressConfig.WebTopUrl = ``
		dressConfig.WebConfig = ``
		dressConfig.WebVideoUrl = "https://ga-album-cdnqn.52tt.com/tt-server/20210630180453_30529349.mp4"
		dressConfig.WebAudioUrl = ""
		dressConfig.WebStaticUrl = "https://ga-album-cdnqn.52tt.com/tt-server/20210630180514_88333075.png"
		dressConfig.Name = fmt.Sprintf("房间套装情侣")
		dressConfig.IsDefault = 0
		dressConfig.CreateTime = time.Now()
		st.AddDressConfig(context.Background(), &dressConfig)
	}
}

func AddSpecialConcern(st *store.Store) {
	{
		//特别关心 草原
		var dressConfig store.DressConfig
		dressConfig.ID = 1
		dressConfig.Level = 1
		dressConfig.DressType = uint32(pb.DressType_DRESS_TYPE_SPECIAL_CONCERN)
		dressConfig.ResourceUrl = `https://ga-album-cdnqn.52tt.com/tt-server/20210531174444_17888482.zip`
		dressConfig.ResourceMd5 = "74fcdb1b0dca644067ac3160f43502cf"

		dressConfig.WebListUrl = `https://ga-album-cdnqn.52tt.com/tt-server/20210531154201_92096091.png`
		dressConfig.WebPreviewUrl = `https://ga-album-cdnqn.52tt.com/tt-server/20210531154212_85998160.png`
		dressConfig.WebTopUrl = `https://ga-album-cdnqn.52tt.com/tt-server/20210531154151_87910593.png`
		dressConfig.WebConfig = ``
		dressConfig.WebVideoUrl = ""
		dressConfig.WebAudioUrl = "https://ga-album-cdnqn.52tt.com/tt-server/20210531154218_63962307.mp3"
		dressConfig.WebStaticUrl = ""
		dressConfig.Name = fmt.Sprintf("特别关心 草原")
		dressConfig.IsDefault = 1
		dressConfig.CreateTime = time.Now()
		st.AddDressConfig(context.Background(), &dressConfig)
	}

	//特别关心星星套装
	{
		var dressConfig store.DressConfig
		dressConfig.ID = 2
		dressConfig.Level = 1
		dressConfig.DressType = uint32(pb.DressType_DRESS_TYPE_SPECIAL_CONCERN)
		dressConfig.ResourceUrl = `https://ga-album-cdnqn.52tt.com/tt-server/20210531174521_48431174.zip`
		dressConfig.ResourceMd5 = "c677540c6dbd96b71fa1056bc4beaef0"

		dressConfig.WebListUrl = `https://ga-album-cdnqn.52tt.com/tt-server/20210531160326_4910502.png`
		dressConfig.WebPreviewUrl = `https://ga-album-cdnqn.52tt.com/tt-server/20210531160330_29888176.png`
		dressConfig.WebTopUrl = `https://ga-album-cdnqn.52tt.com/tt-server/20210531160335_93522166.png`
		dressConfig.WebConfig = ``
		dressConfig.WebAudioUrl = "https://ga-album-cdnqn.52tt.com/tt-server/20210531160340_46892454.mp3"
		dressConfig.WebVideoUrl = ""
		dressConfig.WebStaticUrl = ""
		dressConfig.Name = fmt.Sprintf("特别关心星星套装")
		dressConfig.IsDefault = 0
		dressConfig.CreateTime = time.Now()
		st.AddDressConfig(context.Background(), &dressConfig)
	}
}

func AddChatBubble(st *store.Store) {
	{
		//im气泡-粉爪爪
		var dressConfig store.DressConfig
		dressConfig.ID = 1
		dressConfig.Level = 1
		dressConfig.DressType = uint32(pb.DressType_DRESS_TYPE_CHAT_BUBBLE)
		dressConfig.ResourceUrl = `https://ga-album-cdnqn.52tt.com/tt-server/20210609194346_78196966.zip`
		dressConfig.ResourceMd5 = "0e7f8a1576fd16b4c247f6bc15f3b385"

		dressConfig.WebListUrl = `https://ga-album-cdnqn.52tt.com/tt-server/20210604175135_53316534.png`
		dressConfig.WebPreviewUrl = `https://ga-album-cdnqn.52tt.com/tt-server/20210604175141_63312314.png`
		dressConfig.WebTopUrl = ``
		dressConfig.WebConfig = ``
		dressConfig.WebVideoUrl = ""
		dressConfig.WebAudioUrl = ""
		dressConfig.WebStaticUrl = ""
		dressConfig.Name = fmt.Sprintf("im气泡-粉爪爪")
		dressConfig.IsDefault = 1
		dressConfig.CreateTime = time.Now()
		st.AddDressConfig(context.Background(), &dressConfig)
	}

	{
		//im气泡-科基汪
		var dressConfig store.DressConfig
		dressConfig.ID = 2
		dressConfig.Level = 2
		dressConfig.DressType = uint32(pb.DressType_DRESS_TYPE_CHAT_BUBBLE)
		dressConfig.ResourceUrl = `https://ga-album-cdnqn.52tt.com/tt-server/20210609194352_52678201.zip`
		dressConfig.ResourceMd5 = "38a45d062583547f3a6f03cba26acc50"

		dressConfig.WebListUrl = `https://ga-album-cdnqn.52tt.com/tt-server/20210608172241_96557993.png`
		dressConfig.WebPreviewUrl = `https://ga-album-cdnqn.52tt.com/tt-server/20210608172248_69709260.png`
		dressConfig.WebTopUrl = ``
		dressConfig.WebConfig = ``
		dressConfig.WebVideoUrl = ""
		dressConfig.WebAudioUrl = ""
		dressConfig.WebStaticUrl = ""
		dressConfig.Name = fmt.Sprintf("im气泡-粉爪爪")
		dressConfig.IsDefault = 0
		dressConfig.CreateTime = time.Now()
		st.AddDressConfig(context.Background(), &dressConfig)
	}
}

func AddChatBg(st *store.Store) {
	{
		//im背景-雪山
		var dressConfig store.DressConfig
		dressConfig.ID = 1
		dressConfig.Level = 1
		dressConfig.DressType = uint32(pb.DressType_DRESS_TYPE_CHAT_BACKGROUND)
		dressConfig.ResourceUrl = `https://ga-album-cdnqn.52tt.com/tt-server/20210610174356_77267868.png`
		dressConfig.ResourceMd5 = "d255f46d5826d0863ad56f9f49946dac"

		dressConfig.WebListUrl = `https://ga-album-cdnqn.52tt.com/tt-server/20210608172711_28686522.png`
		dressConfig.WebPreviewUrl = `https://ga-album-cdnqn.52tt.com/tt-server/20210608172716_75325308.png`
		dressConfig.WebTopUrl = ``
		dressConfig.WebConfig = ``
		dressConfig.WebVideoUrl = ""
		dressConfig.WebAudioUrl = ""
		dressConfig.WebStaticUrl = ""
		dressConfig.Name = fmt.Sprintf("im背景-雪山")
		dressConfig.IsDefault = 1
		dressConfig.CreateTime = time.Now()
		st.AddDressConfig(context.Background(), &dressConfig)
	}

	{
		//im背景-月球兔
		var dressConfig store.DressConfig
		dressConfig.ID = 2
		dressConfig.Level = 2
		dressConfig.DressType = uint32(pb.DressType_DRESS_TYPE_CHAT_BACKGROUND)
		dressConfig.ResourceUrl = `https://ga-album-cdnqn.52tt.com/tt-server/20210610174345_77232362.png`
		dressConfig.ResourceMd5 = "b93c7b0d62ada7a39b217fc9f3296aa3"

		dressConfig.WebListUrl = `https://ga-album-cdnqn.52tt.com/tt-server/20210608172757_56465221.png`
		dressConfig.WebPreviewUrl = `https://ga-album-cdnqn.52tt.com/tt-server/20210608172801_60727388.png`
		dressConfig.WebTopUrl = ``
		dressConfig.WebConfig = ``
		dressConfig.WebVideoUrl = ""
		dressConfig.WebAudioUrl = ""
		dressConfig.WebStaticUrl = ""
		dressConfig.Name = fmt.Sprintf("im背景-月球兔")
		dressConfig.IsDefault = 0
		dressConfig.CreateTime = time.Now()
		st.AddDressConfig(context.Background(), &dressConfig)
	}
}
