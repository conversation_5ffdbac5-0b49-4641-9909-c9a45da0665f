// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/services/numeric-go/manager (interfaces: IManager)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"
	time "time"

	gomock "github.com/golang/mock/gomock"
	kafkaanchorcontract "golang.52tt.com/protocol/services/minToolkit/kafka/pb/kafkaanchorcontract"
	kafkapresent "golang.52tt.com/protocol/services/minToolkit/kafka/pb/kafkapresent"
	numeric_go "golang.52tt.com/protocol/services/numeric-go"
)

// MockIManager is a mock of IManager interface.
type MockIManager struct {
	ctrl     *gomock.Controller
	recorder *MockIManagerMockRecorder
}

// MockIManagerMockRecorder is the mock recorder for MockIManager.
type MockIManagerMockRecorder struct {
	mock *MockIManager
}

// NewMockIManager creates a new mock instance.
func NewMockIManager(ctrl *gomock.Controller) *MockIManager {
	mock := &MockIManager{ctrl: ctrl}
	mock.recorder = &MockIManagerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIManager) EXPECT() *MockIManagerMockRecorder {
	return m.recorder
}

// AddUserNumeric mocks base method.
func (m *MockIManager) AddUserNumeric(arg0 context.Context, arg1 *numeric_go.AddUserNumericReq) (*numeric_go.AddUserNumericResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddUserNumeric", arg0, arg1)
	ret0, _ := ret[0].(*numeric_go.AddUserNumericResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddUserNumeric indicates an expected call of AddUserNumeric.
func (mr *MockIManagerMockRecorder) AddUserNumeric(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddUserNumeric", reflect.TypeOf((*MockIManager)(nil).AddUserNumeric), arg0, arg1)
}

// AsyncTaskHandler mocks base method.
func (m *MockIManager) AsyncTaskHandler(arg0 context.Context) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "AsyncTaskHandler", arg0)
}

// AsyncTaskHandler indicates an expected call of AsyncTaskHandler.
func (mr *MockIManagerMockRecorder) AsyncTaskHandler(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AsyncTaskHandler", reflect.TypeOf((*MockIManager)(nil).AsyncTaskHandler), arg0)
}

// BatchGetPersonalNumeric mocks base method.
func (m *MockIManager) BatchGetPersonalNumeric(arg0 context.Context, arg1 *numeric_go.BatchGetPersonalNumericReq) (*numeric_go.BatchGetPersonalNumericResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetPersonalNumeric", arg0, arg1)
	ret0, _ := ret[0].(*numeric_go.BatchGetPersonalNumericResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetPersonalNumeric indicates an expected call of BatchGetPersonalNumeric.
func (mr *MockIManagerMockRecorder) BatchGetPersonalNumeric(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetPersonalNumeric", reflect.TypeOf((*MockIManager)(nil).BatchGetPersonalNumeric), arg0, arg1)
}

// BatchRecordSendGiftEvent mocks base method.
func (m *MockIManager) BatchRecordSendGiftEvent(arg0 context.Context, arg1 *numeric_go.BatchRecordSendGiftEventReq) (*numeric_go.BatchRecordSendGiftEventResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchRecordSendGiftEvent", arg0, arg1)
	ret0, _ := ret[0].(*numeric_go.BatchRecordSendGiftEventResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchRecordSendGiftEvent indicates an expected call of BatchRecordSendGiftEvent.
func (mr *MockIManagerMockRecorder) BatchRecordSendGiftEvent(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchRecordSendGiftEvent", reflect.TypeOf((*MockIManager)(nil).BatchRecordSendGiftEvent), arg0, arg1)
}

// GetPersonalNumeric mocks base method.
func (m *MockIManager) GetPersonalNumeric(arg0 context.Context, arg1 *numeric_go.GetPersonalNumericReq) (*numeric_go.GetPersonalNumericResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPersonalNumeric", arg0, arg1)
	ret0, _ := ret[0].(*numeric_go.GetPersonalNumericResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPersonalNumeric indicates an expected call of GetPersonalNumeric.
func (mr *MockIManagerMockRecorder) GetPersonalNumeric(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPersonalNumeric", reflect.TypeOf((*MockIManager)(nil).GetPersonalNumeric), arg0, arg1)
}

// GetPersonalNumericV2 mocks base method.
func (m *MockIManager) GetPersonalNumericV2(arg0 context.Context, arg1 *numeric_go.GetPersonalNumericV2Req) (*numeric_go.GetPersonalNumericV2Resp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPersonalNumericV2", arg0, arg1)
	ret0, _ := ret[0].(*numeric_go.GetPersonalNumericV2Resp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPersonalNumericV2 indicates an expected call of GetPersonalNumericV2.
func (mr *MockIManagerMockRecorder) GetPersonalNumericV2(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPersonalNumericV2", reflect.TypeOf((*MockIManager)(nil).GetPersonalNumericV2), arg0, arg1)
}

// GetUserRichSwitch mocks base method.
func (m *MockIManager) GetUserRichSwitch(arg0 context.Context, arg1 *numeric_go.GetUserRichSwitchReq) (*numeric_go.GetUserRichSwitchResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserRichSwitch", arg0, arg1)
	ret0, _ := ret[0].(*numeric_go.GetUserRichSwitchResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserRichSwitch indicates an expected call of GetUserRichSwitch.
func (mr *MockIManagerMockRecorder) GetUserRichSwitch(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserRichSwitch", reflect.TypeOf((*MockIManager)(nil).GetUserRichSwitch), arg0, arg1)
}

// GetVipSince mocks base method.
func (m *MockIManager) GetVipSince(arg0 context.Context, arg1 *numeric_go.GetVipSinceReq) (*numeric_go.GetVipSinceResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetVipSince", arg0, arg1)
	ret0, _ := ret[0].(*numeric_go.GetVipSinceResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetVipSince indicates an expected call of GetVipSince.
func (mr *MockIManagerMockRecorder) GetVipSince(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetVipSince", reflect.TypeOf((*MockIManager)(nil).GetVipSince), arg0, arg1)
}

// HandleAnchorContractCancel mocks base method.
func (m *MockIManager) HandleAnchorContractCancel(arg0 context.Context, arg1 *kafkaanchorcontract.AnchorContractEvent) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "HandleAnchorContractCancel", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// HandleAnchorContractCancel indicates an expected call of HandleAnchorContractCancel.
func (mr *MockIManagerMockRecorder) HandleAnchorContractCancel(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HandleAnchorContractCancel", reflect.TypeOf((*MockIManager)(nil).HandleAnchorContractCancel), arg0, arg1)
}

// HandleAnchorContractSign mocks base method.
func (m *MockIManager) HandleAnchorContractSign(arg0 context.Context, arg1 *kafkaanchorcontract.AnchorContractEvent) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "HandleAnchorContractSign", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// HandleAnchorContractSign indicates an expected call of HandleAnchorContractSign.
func (mr *MockIManagerMockRecorder) HandleAnchorContractSign(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HandleAnchorContractSign", reflect.TypeOf((*MockIManager)(nil).HandleAnchorContractSign), arg0, arg1)
}

// IsRecordRich mocks base method.
func (m *MockIManager) IsRecordRich(arg0 context.Context, arg1 uint32) bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IsRecordRich", arg0, arg1)
	ret0, _ := ret[0].(bool)
	return ret0
}

// IsRecordRich indicates an expected call of IsRecordRich.
func (mr *MockIManagerMockRecorder) IsRecordRich(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsRecordRich", reflect.TypeOf((*MockIManager)(nil).IsRecordRich), arg0, arg1)
}

// PresentEventHandle mocks base method.
func (m *MockIManager) PresentEventHandle(arg0 context.Context, arg1 *kafkapresent.PresentEvent) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PresentEventHandle", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// PresentEventHandle indicates an expected call of PresentEventHandle.
func (mr *MockIManagerMockRecorder) PresentEventHandle(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PresentEventHandle", reflect.TypeOf((*MockIManager)(nil).PresentEventHandle), arg0, arg1)
}

// RecordSendGiftEvent mocks base method.
func (m *MockIManager) RecordSendGiftEvent(arg0 context.Context, arg1 *numeric_go.RecordSendGiftEventReq) (*numeric_go.RecordSendGiftEventResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RecordSendGiftEvent", arg0, arg1)
	ret0, _ := ret[0].(*numeric_go.RecordSendGiftEventResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RecordSendGiftEvent indicates an expected call of RecordSendGiftEvent.
func (mr *MockIManagerMockRecorder) RecordSendGiftEvent(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RecordSendGiftEvent", reflect.TypeOf((*MockIManager)(nil).RecordSendGiftEvent), arg0, arg1)
}

// ReportDataCenter mocks base method.
func (m *MockIManager) ReportDataCenter(arg0 context.Context, arg1, arg2 uint32, arg3 time.Time, arg4 bool, arg5, arg6 uint64) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "ReportDataCenter", arg0, arg1, arg2, arg3, arg4, arg5, arg6)
}

// ReportDataCenter indicates an expected call of ReportDataCenter.
func (mr *MockIManagerMockRecorder) ReportDataCenter(arg0, arg1, arg2, arg3, arg4, arg5, arg6 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReportDataCenter", reflect.TypeOf((*MockIManager)(nil).ReportDataCenter), arg0, arg1, arg2, arg3, arg4, arg5, arg6)
}

// SetUserRichSwitch mocks base method.
func (m *MockIManager) SetUserRichSwitch(arg0 context.Context, arg1 *numeric_go.SetUserRichSwitchReq) (*numeric_go.SetUserRichSwitchResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetUserRichSwitch", arg0, arg1)
	ret0, _ := ret[0].(*numeric_go.SetUserRichSwitchResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetUserRichSwitch indicates an expected call of SetUserRichSwitch.
func (mr *MockIManagerMockRecorder) SetUserRichSwitch(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetUserRichSwitch", reflect.TypeOf((*MockIManager)(nil).SetUserRichSwitch), arg0, arg1)
}

// UseRichCard mocks base method.
func (m *MockIManager) UseRichCard(arg0 context.Context, arg1 *numeric_go.UseRichCardReq) (*numeric_go.UseRichCardResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UseRichCard", arg0, arg1)
	ret0, _ := ret[0].(*numeric_go.UseRichCardResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UseRichCard indicates an expected call of UseRichCard.
func (mr *MockIManagerMockRecorder) UseRichCard(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UseRichCard", reflect.TypeOf((*MockIManager)(nil).UseRichCard), arg0, arg1)
}
