package main

import (
    "fmt"
    _ "github.com/go-sql-driver/mysql"
    "github.com/jinzhu/gorm"
    "github.com/spf13/cobra"
    "github.com/tealeg/xlsx"
    "golang.52tt.com/services/exchange/alg"
    "golang.52tt.com/services/exchange/conf"
    "golang.52tt.com/services/exchange/model"
    "golang.52tt.com/services/exchange/tools/init_id_distributor/config"
    "time"
)

//根据excel文档和配置，顺序初始化身份证对应的供应商
//excel格式，第一列：订单号，第二列：uid，第三列：身份证，第四列：提现金额

var sc config.ServiceConfigT

var (
    rootCommand = cobra.Command{
        Use:                        "init_id_distributor",
        Short:                      "init id number to distributor",
        Long: "init id number to distributor",
        Version:                    "1.0.0",
        Run: func(cmd *cobra.Command, args []string) {
            fmt.Println("in run")
            confPath, err := cmd.Flags().GetString("conf")
            if err != nil {
                fmt.Printf("cmd.Flags().GetString(\"conf\") err=%v\n", err)
            }
            fmt.Printf("confPath=%s\n", confPath)

            err = sc.Parse(confPath)
            if err != nil {
                fmt.Printf("config parse err=%v", err)
                return
            }

            mysqlDb, err := gorm.Open("mysql", sc.GetMysqlConfig().ConnectionString())
            if err != nil {
                fmt.Printf("Failed to connect mysql %v", err)
                return
            }

            xlsPath, err := cmd.Flags().GetString("xls")
            if err != nil {
                fmt.Printf("cmd.Flags().GetString(\"xls\") err=%v\n", err)
            }
            fmt.Printf("xlsPath=%s\n", xlsPath)

            xFile, err := xlsx.OpenFile(xlsPath)
            if err != nil {
                fmt.Println(err)
                return
            }

            conf.InitDistributorConfig()

            sheet := xFile.Sheets[0]

            if sheet.MaxRow < 2 {
                fmt.Println("xls no data")
                return
            }

            fmt.Printf("max row=%d\n", sheet.MaxRow)

            xDWL := model.ExchangeDistributorWithdrawLogDB{Db: mysqlDb}
            err = xDWL.AutoMigrateLastMonth() //还是把上个月的也存一下，方便测试rebalance
            if err != nil {
                fmt.Println(err)
                return
            }

            idWithdrawMap := make(map[string]uint32)
            totalWithdraw := uint64(0)
            for rowIndex := 1; rowIndex < sheet.MaxRow; rowIndex++ {
                row := sheet.Row(rowIndex)

                if len(row.Cells) < 4 {
                    fmt.Printf("len(row.Cells) < 4 read %d len\n", rowIndex)
                    break
                }

                orderId := row.Cells[0].Value

                if orderId == "" {
                    fmt.Printf("orderId empty read %d len\n", rowIndex)
                    break
                }

                uid, _ := row.Cells[1].Int64()
                idNumber := row.Cells[2].Value
                withdrawF, _ := row.Cells[3].Float()
                withdraw := uint32(withdrawF * 100)
                idWithdrawMap[idNumber] += withdraw
                totalWithdraw += uint64(withdraw)

                tXDWL := model.ExchangeDistributorWithdrawLogDB{Db: mysqlDb}
                tXDWL.OrderId = orderId
                tXDWL.Uid = uint32(uid)
                tXDWL.IdNumber = idNumber
                tXDWL.WithdrawAmount = uint64(withdraw)
                tXDWL.CreateTime = uint64(time.Now().Unix())
                err := tXDWL.SaveInitMonthData()
                if err != nil {
                    fmt.Println(err)
                    return
                }
            }

            distributorMaxMap := make(map[string]uint64)
            distributorCurrentMap := make(map[string]uint64)
            distributorList := make([]string, 0)
            for distributor, percent := range conf.DistributorConfig.DistributorPercentMap {
                distributorMaxMap[distributor] = totalWithdraw * uint64(percent) / 100
                distributorCurrentMap[distributor] = 0
                distributorList = append(distributorList, distributor)
                fmt.Printf("%s max=%d\n", distributor, distributorMaxMap[distributor])
            }

            distributorIndex := 0
            currentDistributor := distributorList[distributorIndex]
            fmt.Printf("start %s\n", currentDistributor)
            for idNumber, withdraw := range idWithdrawMap {
                if distributorCurrentMap[currentDistributor] >= distributorMaxMap[currentDistributor] && distributorIndex < len(distributorList)-1 {
                    fmt.Printf("%s put %d\n", currentDistributor, distributorCurrentMap[currentDistributor])
                    distributorIndex++
                    currentDistributor = distributorList[distributorIndex]
                }

                //判断60岁，因为是初始化，60岁以上肯定有一个，所以直接拿
                age, err := alg.GetAgeFromId(idNumber)
                if err != nil {
                    fmt.Printf("GetAgeFromId %s err=%v", idNumber, err)
                    return
                }

                xIdD := model.ExchangeIdDistributorDB{Db: mysqlDb}
                xIdD.IdNumber = idNumber
                xIdD.DistributorName = currentDistributor
                xIdD.CreateTime = uint64(time.Now().Unix())
                if age >= 60 {
                    xIdD.DistributorName = conf.DistributorConfig.SixTyAgeDistributor[0]
                }

                err = xIdD.SaveData()
                if err != nil {
                    fmt.Printf("xIdD.SaveData err=%v", err)
                    return
                }

                xDWL := model.ExchangeDistributorWithdrawLogDB{Db: mysqlDb}
                err = xDWL.UpdateInitMonthDistributor(xIdD.IdNumber, xIdD.DistributorName)
                if err != nil {
                    fmt.Println(err)
                    return
                }

                distributorCurrentMap[currentDistributor] += uint64(withdraw)
            }

            fmt.Printf("%s put %d\n", currentDistributor, distributorCurrentMap[currentDistributor])

            fmt.Println("done")
        },
    }
)

func main() {
    rootCommand.Flags().StringP("conf", "c", "", "config file path")
    rootCommand.Flags().StringP("xls", "x", "", "xls file path")
    err := rootCommand.Execute()
    if err != nil {
        panic(err)
    }
}
