package server

import (
    "context"
    "golang.52tt.com/pkg/log"
    exchangePB "golang.52tt.com/protocol/services/exchange"
    cache2 "golang.52tt.com/services/exchange/cache"
    "golang.52tt.com/services/exchange/comm"
    "golang.52tt.com/services/exchange/model"
    "strconv"
    "time"
)

const (
    cacheValueTaskStart = "1"
    cacheValueTaskOver = "2"
    cacheValueTaskErr = "3"
)

func (s *guildExchangeServer) SumAndSettlementTask(ctx context.Context, masterUid uint32, xType exchangePB.ExchangeType) {
    log.InfoWithCtx(ctx, "SumAndSettlementTask %d %d", masterUid, xType)
    cache := cache2.NewSumAndSettlementCache(s.rc)
    cacheValue := cache.Get(masterUid, uint32(xType))

    if cacheValue == cacheValueTaskStart || cacheValue == cacheValueTaskOver {
        log.WarnWithCtx(ctx, "SumAndSettlementCache value=%s", cacheValue)
        return
    }

    err := cache.Set(masterUid, uint32(xType), cacheValueTaskStart)
    if err != nil {
        log.WarnWithCtx(ctx, "SumAndSettlementCache Set uid=%d err=%v", masterUid, err)
        return
    }

    func() {
        defer func() {
            if err != nil {
                err = cache.Set(masterUid, uint32(xType), cacheValueTaskErr)
                if err != nil {
                    log.WarnWithCtx(ctx, "SumAndSettlementCache Set uid=%d err=%v", masterUid, err)
                    return
                }
            }
        }()

        xGDb := model.ExchangeGuildDB{Db: s.db}
        list := &[]model.ExchangeGuild{}
        err = xGDb.GetListWithMasterUid(masterUid, list)
        if err != nil {
            log.ErrorWithCtx(ctx, "GetListWithMasterUid err=%v", err)
            return
        }

        for _, elem := range *list {
            _, err = s.ManualSum(ctx, &exchangePB.ManualSumReq{
                Uid:      elem.UserId,
                GuildId:  elem.GuildId,
                AutoTime: uint32(time.Now().Unix()),
                Reason:   model.X_GUILD_X_REASON_AUTO_EXCHANGE,
            })
            if err != nil {
                log.ErrorWithCtx(ctx, "ManualSum uid=%d, guild_id=%d err=%+v", elem.UserId, elem.GuildId, err)
                continue //可能是因为黑名单或者冻结无法汇总，跳过
            }
        }

        log.InfoWithCtx(ctx, "SumAndSettlementTask FlushScoreSumForMaster %d", masterUid)
        comm.FlushScoreSumForMaster(s.db, s.rc, s.rb, masterUid)

        _, err = s.ManualSettlement(ctx, &exchangePB.ManualSettlementReq{
            MasterUid: masterUid,
            XType:     xType,
        })

        if err != nil {
            log.ErrorWithCtx(ctx, "ManualSettlement master_uid=%d, xType=%d err=%+v", masterUid, xType, err)
            return
        }
    }()

    err = cache.Set(masterUid, uint32(xType), cacheValueTaskOver)
    if err != nil {
        log.ErrorWithCtx(ctx, "SumAndSettlementCache Del uid=%d err=%v", masterUid, err)
    }
}

func (s *guildExchangeServer) SumAndSettlement(ctx context.Context, req *exchangePB.ManualSettlementReq) (*exchangePB.EmptyMsg, error) {
    resp := &exchangePB.EmptyMsg{}

    go s.SumAndSettlementTask(ctx, req.GetMasterUid(), req.GetXType())

    return resp, nil
}

func (s *guildExchangeServer) CheckSumAndSettlementStatus(ctx context.Context, req *exchangePB.ManualSettlementReq) (*exchangePB.CheckSumAndSettlementStatusResp, error) {
    resp := &exchangePB.CheckSumAndSettlementStatusResp{}
    cache := cache2.NewSumAndSettlementCache(s.rc)
    strStatus := cache.Get(req.GetMasterUid(), uint32(req.GetXType()))
    intStatus, _ := strconv.Atoi(strStatus)
    resp.Status = uint32(intStatus)
    return resp, nil
}
