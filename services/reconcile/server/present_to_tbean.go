// @Title  present_to_tbean.go
// @Description  1.礼物-T豆对账，检测礼物和T豆10分钟前 1分钟内所有订单总数是否一致，如果不一致，获取订单详情
// @Description  2.转转-T豆对账，检测转转和T豆10分钟前 1分钟内所有订单总数是否一致，如果不一致，获取订单详情
// <AUTHOR>

package server

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	channel_red_packet "golang.52tt.com/clients/channel-red-packet"
	magic_spirit "golang.52tt.com/clients/magic-spirit"
	present_middleware "golang.52tt.com/protocol/services/present-middleware"
	"io/ioutil"
	"math/rand"
	"net/http"
	"sort"
	"time"

	"google.golang.org/grpc"

	userPresent "golang.52tt.com/clients/userpresent"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/services/reconcile/conf"
	"golang.52tt.com/services/reconcile/report"
	"golang.52tt.com/services/reconcile/store"
	"golang.52tt.com/services/reconcile/util"
)

const (
	// PresentSendFromTBean T豆送礼
	PresentSendFromTBean = 0

	// CheckOrderTime 检测订单时间
	CheckOrderTime = 5 * 60

	// OnceLoopTime .
	OnceLoopTime = 60

	// ReqTypeOther .
	ReqTypeOther = "other"

	// ReqTypeMoLiQiu .
	ReqTypeMoLiQiu = "moliqiu"

	//T豆相关的都是靠走T豆回调的，T豆30分钟回调，查询我们的orderID，属于prepare了，但是没有commit的情况，commit前会写数据库，记录orderID
	//T豆有个消费时间，业务里会记录这个时间，然后按照这个时间去取orderId，这样就不会有时间段跨越的order了
	AppIDGift        = "TT_HZ"
	AppIDFellow      = "TT_ZY"
	AppIDRedPacket   = "TT_HB"
	AppIDMoLiQiu     = "moliqiu"
	AppIDMagicSpirit = "TT_XY"
)

// PresentToTbean 礼物-T豆对账
type PresentToTbean struct {
	ticker            *time.Ticker
	stopChan          chan struct{}
	sc                *conf.ServiceConfig
	st                *store.Store
	reporter          *report.FeishuReporterV2
	userPresentClient *userPresent.Client
	redPacketClient   *channel_red_packet.Client
	magicSpiritClient *magic_spirit.Client
}

// GetTbeanOrderInfoReq .
type GetTbeanOrderInfoReq struct {
	ID     uint32      `json:"id"`
	Client TbeanCaller `json:"client"`
	Data   TbeanData   `json:"data"`

	Sign    string `json:"sign"`
	Encrypt string `json:"encrypt"`
}

// TbeanData .
type TbeanData struct {
	Start  string `json:"startTime"`
	Type   string `json:"type"`
	Range  uint32 `json:"range"`
	Caller string `json:"caller"`
}

// TbeanCaller .
type TbeanCaller struct {
	Caller string `json:"caller"`
}

// TbeanState .
type TbeanState struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
}

// TBeanRespData .
type TBeanRespData struct {
	OrderCount uint32 `json:"countNum"`
	Amount     uint32 `json:"amount"`
}

// TbeanOrderCountResp .
type TbeanOrderCountResp struct {
	Data  TBeanRespData `json:"data"`
	State TbeanState    `json:"state"`
}

// TbeanOrderInfo .
type TbeanOrderInfo struct {
	ID         string `json:"id"`
	OrderType  string `json:"orderType"`
	OutTradeNo string `json:"outTradeNo"`
	Status     string `json:"status"`
	BuyerID    uint32 `json:"buyerId"`
	Price      uint32 `json:"price"`
	UnitPrice  uint32 `json:"unitPrice"`
}

// TbeanOrderListResp .
type TbeanOrderListResp struct {
	Data  []TbeanOrderInfo `json:"data"`
	State TbeanState       `json:"state"`
}

// NewPresentToTbeanReconcile .
func NewPresentToTbeanReconcile(cfg *conf.ServiceConfig, st *store.Store) (ps *PresentToTbean, err error) {
	p := &PresentToTbean{}
	p.userPresentClient = userPresent.NewClient(grpc.WithBlock())
	p.redPacketClient, _ = channel_red_packet.NewClient(grpc.WithBlock())
	p.magicSpiritClient, _ = magic_spirit.NewClient(grpc.WithBlock())
	p.reporter = report.NewFeiShuReporterV2(cfg.WarnFeiShuUrl, cfg.Env, cfg.Push, cfg.AlarmConfig)
	p.st = st
	p.sc = cfg
	go p.Start()
	return p, nil
}

// Start .
func (p *PresentToTbean) Start() {
	time.Sleep(time.Duration(rand.Intn(OnceLoopTime)+1) * time.Second)
	p.ticker = time.NewTicker(OnceLoopTime * time.Second)
	p.stopChan = make(chan struct{})

	for {
		select {
		case <-p.ticker.C:
			beginSecond := time.Now().Unix() - CheckOrderTime - OnceLoopTime
			beginSecond = util.GetCurrentMinuteTimeStamp(beginSecond) //取整分钟的时间
			//p.TTOrderToTBeanOrderCheck(beginSecond, AppIDGift, false)
			//p.TTOrderToTBeanOrderCheck(beginSecond, AppIDFellow, false)
			//p.TTOrderToTBeanOrderCheck(beginSecond, AppIDMoLiQiu, false)
			//p.TTOrderToTBeanOrderCheck(beginSecond, AppIDRedPacket, false)
			//p.TTOrderToTBeanOrderCheck(beginSecond, AppIDMagicSpirit, false)

			yesterdayBegin := beginSecond - 24*60*60
			p.TTOrderToTBeanOrderCheck(yesterdayBegin, AppIDGift, true)
			p.TTOrderToTBeanOrderCheck(yesterdayBegin, AppIDFellow, true)
			p.TTOrderToTBeanOrderCheck(yesterdayBegin, AppIDMoLiQiu, true)
			p.TTOrderToTBeanOrderCheck(yesterdayBegin, AppIDRedPacket, true)
			p.TTOrderToTBeanOrderCheck(yesterdayBegin, AppIDMagicSpirit, true)
		case <-p.stopChan:
			return
		}
	}
}

// Stop .
func (p *PresentToTbean) Stop() {
	p.stopChan <- struct{}{}
	p.ticker.Stop()
}

func (p *PresentToTbean) getOrderCountAndAmountByAppId(appId string, beginTime uint32) (uint32, uint32, error) {
	var orderCount, amount uint32
	endTime := beginTime + OnceLoopTime
	ctx, _ := context.WithTimeout(context.Background(), time.Second)
	month := util.GetYearAndMonth(time.Unix(int64(beginTime), 0))
	if appId == AppIDGift {
		presentOrderCount, _, _, presentPrice, err := p.st.GetPresentOrderSummary(ctx, month, beginTime, endTime,
			int32(present_middleware.PresentSourceType_PRESENT_SOURCE_BUY))
		if err != nil {
			log.Errorf("check present-backpack failed get present count failed:%v", err)
			return orderCount, amount, err
		}
		orderCount = presentOrderCount
		amount = presentPrice
	} else if appId == AppIDFellow {
		presentOrderCount, _, _, presentPrice, err := p.st.GetPresentOrderSummary(ctx, month, beginTime, endTime,
			int32(present_middleware.PresentSourceType_PRESENT_SOURCE_FELLOW))
		if err != nil {
			log.Errorf("check present-backpack failed get present count failed:%v", err)
			return orderCount, amount, err
		}
		orderCount = presentOrderCount
		amount = presentPrice
	} else if appId == AppIDRedPacket {
		redPacketOrder, err := p.redPacketClient.GetRedPacketOrderTotal(ctx, beginTime, endTime)
		if err != nil {
			log.Errorf("GetRedPacketOrderTotal err=%v", err)
			return orderCount, amount, err
		}
		orderCount = redPacketOrder.TotalCnt
		amount = redPacketOrder.TotalPrice
	} else if appId == AppIDMoLiQiu {
		startStr := util.GetTimeStr(int64(beginTime))
		endStr := util.GetTimeStr(int64(endTime))
		for i := 0; i < 10; i++ {
			month = util.GetMonth(time.Unix(int64(beginTime), 0))
			idx := fmt.Sprintf("%d", i)
			onceCount, fee, err := p.st.GetSmashEggOrderCount(context.Background(), month, idx, startStr, endStr)
			if err != nil {
				log.Errorf("check smashegg-tbean failed get smashegg count failed:%v", err)
				return orderCount, amount, err
			}
			orderCount = orderCount + onceCount
			amount = amount + fee
		}
	} else if appId == AppIDMagicSpirit {
		magicSpirit, err := p.magicSpiritClient.GetMagicSpiritOrderTotal(ctx, beginTime, endTime)
		if err != nil {
			log.Errorf("GetMagicSpiritOrderTotal err=%v", err)
			return orderCount, amount, err
		}
		orderCount = magicSpirit.OrderCnt
		amount = uint32(magicSpirit.TotalPrice)
	}
	return orderCount, amount, nil
}

// presentTBeanCheck 礼物-T豆对账
func (p *PresentToTbean) TTOrderToTBeanOrderCheck(beginTime int64, appId string, yesterday bool) {
	log.Debugf("TTOrderToTBeanOrderCheck begin:%d, appID:%s", beginTime, appId)
	endTime := beginTime + OnceLoopTime
	//获取总数
	//获取业务服务的订单总数和总价值
	orderCount, orderAmount, err := p.getOrderCountAndAmountByAppId(appId, uint32(beginTime))
	if err != nil {
		log.Errorf("getOrderCountAndAmountByAppId err=%v", err)
		return
	}

	//获取T豆这边从某个开始时间到1分钟后的订单总数和总价值
	var tbeanOrderCnt, tbeanAmount uint32
	giftData, terr := p.getTbeanOrderCount(beginTime, appId)
	if terr != nil {
		log.Errorf("check failed get tbean count failed:%v", terr)
		return
	}
	tbeanOrderCnt = giftData.OrderCount
	tbeanAmount = giftData.Amount

	log.Infof("present-tbean order appId:%s count:%d-%d, present-tbean price:%d-%d, begin:%d, end:%d",
		appId, orderCount, tbeanOrderCnt, orderAmount, tbeanAmount, beginTime, endTime)
	if orderCount == tbeanOrderCnt && orderAmount == tbeanAmount {
		return
	}

	//获取明细
	tbeanStart := time.Unix(beginTime, 0).Format("2006-01-02 15:04:05")
	orderList, terr := p.getTbeanOrderList(beginTime, appId)
	if terr != nil {
		log.Errorf("get tbean list failed:%v", terr)
		return
	}
	log.Infof("get tbean list appID:%s size:%d list:%v", appId, len(orderList.Data), orderList)
	for _, order := range orderList.Data {
		log.Errorf("TBEAN ORDER: %s  %s  %s", order.ID, order.OutTradeNo, tbeanStart)
	}

	appIdName := ""
	switch appId {
	case AppIDGift:
		appIdName = "礼物"
	case AppIDFellow:
		appIdName = "挚友"
	case AppIDRedPacket:
		appIdName = "红包"
	case AppIDMoLiQiu:
		appIdName = "转转"
	case AppIDMagicSpirit:
		appIdName = "幸运礼物"
	}

	msg := fmt.Sprintf("%s-T豆订单异常：业务查询范围（start：%d，end：%d）, tbean start：%s  [%s订单数量：%d，T豆订单数量：%d]，[%sT豆金额:%d，T豆订单金额:%d]",
		appIdName, beginTime, endTime, tbeanStart, appIdName, orderCount, tbeanOrderCnt, appIdName,
		orderAmount, giftData.Amount)

	var reportErr error
	if yesterday {
		msg += " 昨日补单失败，建议人工介入！"
		switch appId {
		case AppIDGift:
			msg += "[赵亮 T1548]"
		case AppIDFellow:
			msg += "[赵亮 T1548]"
		case AppIDRedPacket:
			msg += "[周韶勇 T1208]"
		case AppIDMoLiQiu:
			msg += "[周韶勇 T1208]"
		case AppIDMagicSpirit:
			msg += "[周韶勇 T1208]"
		}
		reportErr = p.reporter.SendError(msg)
		err := p.reporter.SendAlarm("T豆订单异常", msg, uint32(beginTime), []string{})
		if err != nil {
			log.Errorln(err)
		}
	} else {
		reportErr = p.reporter.SendWarning(msg)
	}
	if reportErr != nil {
		log.Errorf("reporter er:%v, ", reportErr)
	}

	log.Errorf("error present-tbean err:%s", msg)
	orderID := fmt.Sprintf("present_to_tbean_%d", beginTime)
	err = p.st.InsertErrorLog(orderID, msg, store.PresentToTBean)
	if terr != nil {
		log.Errorln(err)
	}
}

//getTbeanOrderCount 获取T豆订单数量
//http://s-doc2.ttyuyin.com/web/#/16/536
func (p *PresentToTbean) getTbeanOrderCount(begin int64, appId string) (data TBeanRespData, err error) {
	start := time.Unix(begin, 0).Format("2006-01-02 15:04:05")
	info, err := p.getTbeanOrderInfo(start, p.sc.TbeanOrderCountURL, appId, 1)
	if err != nil {
		return data, err
	}
	var resp TbeanOrderCountResp
	err = json.Unmarshal(info, &resp)
	if err != nil {
		return data, err
	}
	log.Infof("getTbeanOrderCount appId:%s, begin:%d, resp:%v", appId, begin, resp)
	return resp.Data, nil
}

// getTbeanOrderList 获取T豆订单列表
//http://s-doc2.ttyuyin.com/web/#/16?page_id=473
func (p *PresentToTbean) getTbeanOrderList(begin int64, appId string) (t TbeanOrderListResp, err error) {
	start := time.Unix(begin, 0).Format("2006-01-02 15:04:05")
	info, err := p.getTbeanOrderInfo(start, p.sc.TbeanOrderListURL, appId, 1)
	if err != nil {
		return t, err
	}

	var resp TbeanOrderListResp
	err = json.Unmarshal(info, &resp)
	if err != nil {
		return t, err
	}
	log.Infof("getTbeanOrderList appId:%s, begin:%d, resp:%v", appId, begin, resp)
	return resp, nil
}

// @接口文档 http://s-doc2.ttyuyin.com/web/#/16?page_id=473
//			http://s-doc2.ttyuyin.com/web/#/16/536
// @start : 格式 "2020-11-19 13:58:31"
// @postURL : url
// @offset :range start的偏移 分钟为单位 1-5
// @reqType : type 请求类型 moliqiu/other
func (p *PresentToTbean) getTbeanOrderInfo(start, postURL, appId string, offset uint32) (postRes []byte, err error) {
	reqType := ReqTypeOther
	if appId == AppIDMoLiQiu {
		reqType = ReqTypeMoLiQiu
		appId = AppIDGift
	}

	var req GetTbeanOrderInfoReq
	var caller TbeanCaller
	caller.Caller = appId
	req.Encrypt = "md5"
	req.Client = caller

	var data TbeanData
	data.Start = start
	data.Range = offset
	data.Type = reqType
	data.Caller = appId
	req.Data = data
	sign := fmt.Sprintf("%s", caller.Caller)

	var strSlice []string
	strSlice = append(strSlice, fmt.Sprintf("startTime=%s", data.Start))
	strSlice = append(strSlice, fmt.Sprintf("type=%s", data.Type))
	strSlice = append(strSlice, fmt.Sprintf("range=%d", data.Range))
	strSlice = append(strSlice, fmt.Sprintf("caller=%s", data.Caller))
	sort.Strings(strSlice)
	for _, v := range strSlice {
		sign = fmt.Sprintf("%s%s&", sign, v)
	}
	sign = sign[:len(sign)-1]
	if appId == AppIDGift {
		sign = fmt.Sprintf("%s%s", sign, p.sc.TbeanSecretKey)
	} else {
		sign = fmt.Sprintf("%s%s", sign, p.sc.TbeanFellowSecretKey)
	}
	req.Sign = util.GenMD5(sign)

	if bs, err := json.Marshal(req); err == nil {
		reqStr := bytes.NewBuffer([]byte(bs))
		bodyType := "application/json;charset=utf-8"
		resp, err := http.Post(postURL, bodyType, reqStr)
		if err != nil {
			log.Errorf("http post:%s err:%v", postURL, err)
			return nil, err
		}

		defer resp.Body.Close()
		body, err := ioutil.ReadAll(resp.Body)
		if err != nil {
			log.Errorf("http read body:%s err:%v", postURL, err)
			return nil, err
		}

		log.Infof("post appId:%s, url:%s, req:%s, resp:%s", appId, postURL, string(bs), string(body))

		return body, err
	}

	return nil, err
}
