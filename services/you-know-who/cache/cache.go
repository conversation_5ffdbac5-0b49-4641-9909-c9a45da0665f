package cache

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"sync"
	"time"

	"github.com/opentracing/opentracing-go"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/store/redis"
	mutex "golang.52tt.com/services/you-know-who/cache/redis-mutex"
)

type YouKnowWhoCache struct {
	redisClient                *redis.Client
	tracer                     opentracing.Tracer
	localCache                 []*sync.Map
	localCacheReadIndex        uint8
	localFakeUidCache          []*sync.Map
	localFakeUidCacheReadIndex uint8
	distributedLock            *mutex.DistributedLock
}

func NewYouKnowWhoCache(r *redis.Client, tracer opentracing.Tracer) (*YouKnowWhoCache, error) {
	c := &YouKnowWhoCache{redisClient: r, tracer: tracer}
	c.localCache = append(c.localCache, &sync.Map{}, &sync.Map{})
	c.localFakeUidCache = append(c.localFakeUidCache, &sync.Map{}, &sync.Map{})
	c.localCacheReadIndex = 0
	c.localFakeUidCacheReadIndex = 0
	lock, err := mutex.NewDistributedLock(r)
	if err != nil {
		log.Errorf("NewYouKnowWhoCache NewDistributedLock failed, err:[%+v]", err)
		return nil, err
	}
	c.distributedLock = lock
	return c, nil
}

const (
	UKW_REDIS_STATUS_INFO_KEY        = "You-Know-Who:status"
	UKW_REDIS_FAKE_UID_KEY           = "You-Know-Who:fake-uid"
	UKW_REDIS_UPDATE_INFO_KEY        = "You-Know-Who:update-info"
	UKW_REDIS_UPDATE_FAKE_UID_KEY    = "You-Know-Who:update-fake-uid"
	UKW_REDIS_REPORT_FINISHED_KEY    = "You-Know-Who:report-finished"
	UKW_REDIS_RANK_SWITCH_LOCK       = "You-Know-Who:rank-switch-lock"
	UKW_REDIS_REPORT_LOCK            = "You-Know-Who:report-lock"
	UKW_REDIS_FAKE_UID_POOL          = "You-Know-Who:fake-uid-pool"
	UKW_REDIS_FAKE_POOL_FINISHED_KEY = "You-Know-Who:fake-pool-finished"
)

// GetUKWStatusInfosKey 获取神秘人状态缓存key
func (y *YouKnowWhoCache) getUKWStatusInfosKey() string {
	return UKW_REDIS_STATUS_INFO_KEY
}

// ChangeUKWUidToString 将神秘人Uid转化为Field
func (y *YouKnowWhoCache) ChangeUKWUidToString(ukwUid uint32) string {
	return fmt.Sprintf("%+v", ukwUid)
}

// GetUKWStatusCacheInfo 获取指定神秘人缓存信息
func (y *YouKnowWhoCache) GetUKWStatusCacheInfo(ctx context.Context, ukwUid uint32) (*UKWCachePermissionInfo, error) {
	if ukwUid == 0 {
		log.ErrorWithCtx(ctx, "GetUKWStatusCacheInfo with a empty ukwUid! ukwUid:[%+v]", ukwUid)
		return nil, nil
	}

	// 获取field
	field := y.ChangeUKWUidToString(ukwUid)
	// 删除对应的状态缓存
	result, err := y.redisClient.HGet(y.getUKWStatusInfosKey(), field).Result()
	if err != nil {
		if err == redis.Nil {
			return nil, nil
		}
		log.ErrorWithCtx(ctx, "GetUKWStatusCacheInfo HGet failed, err:[%+v]", err)
		return nil, err
	}

	// 转化为结构化数据返回
	statusInfo := new(UKWCachePermissionInfo)
	err = json.Unmarshal([]byte(result), statusInfo)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetUKWStatusCacheInfo Unmarshal failed, err:[%+v]", err)
	}

	log.InfoWithCtx(ctx, "GetUKWStatusCacheInfo successful, info:[%+v]", statusInfo)
	return statusInfo, nil
}

// GetAllUKWStatusCacheInfos 查询当前所有神秘人状态缓存信息
func (y *YouKnowWhoCache) GetAllUKWStatusCacheInfos(ctx context.Context) ([]*UKWCachePermissionInfo, error) {

	// 使用HSCAN范围扫描获取
	statusInfos := make([]*UKWCachePermissionInfo, 0)
	var cursor uint64
	for {
		var keys []string
		var err error
		keys, cursor, err = y.redisClient.HScan(y.getUKWStatusInfosKey(), cursor, "*", 200).Result()
		if err != nil {
			log.ErrorWithCtx(ctx, "GetAllUKWStatusCacheInfos HScan failed, err:[%+v]", err)
			return statusInfos, err
		}

		for num, key := range keys {
			if num%2 == 0 { // 跳过key值
				continue
			}
			info := new(UKWCachePermissionInfo)
			err = json.Unmarshal([]byte(key), info)
			if err != nil {
				log.ErrorWithCtx(ctx, "GetAllUKWStatusCacheInfos Unmarshal failed, err:[%+v]", err)
				return statusInfos, err
			}
			statusInfos = append(statusInfos, info)
		}

		if cursor == 0 { // 当等于0的时候说明已经全部获取到了
			break
		}
	}

	return statusInfos, nil
}

// UpdateUKWStatusCacheInfo 更新神秘人状态缓存
func (y *YouKnowWhoCache) UpdateUKWStatusCacheInfo(ctx context.Context, info *UKWCachePermissionInfo) error {

	// 判断是否为空
	if info == nil {
		errInfo := fmt.Errorf("UpdateUKWStatusInfo with a empty info! info:[%+v]", info)
		log.ErrorWithCtx(ctx, errInfo.Error())
		return nil
	}

	// 获取field
	field := y.ChangeUKWUidToString(info.Uid)
	// 将结构体转化为string
	jsonStr, err := json.Marshal(info)
	if err != nil {
		log.WarnWithCtx(ctx, "UpdateUKWStatusInfo Marshal failed, err:[%+v]", err)
	}

	// 更新对应的值
	err = y.redisClient.HSet(y.getUKWStatusInfosKey(), field, string(jsonStr)).Err()
	if err != nil {
		log.ErrorWithCtx(ctx, "UpdateUKWStatusInfo HSet failed, info:[%+v], err:[%+v]", string(jsonStr), err)
		return err
	}

	return nil
}

// BatchUpdateUKWStatusCacheInfo 批量更新神秘人状态信息
func (y *YouKnowWhoCache) BatchUpdateUKWStatusCacheInfo(ctx context.Context, infos []*UKWCachePermissionInfo) error {
	// 判断是否为空
	if len(infos) == 0 {
		log.WarnWithCtx(ctx, "BatchUpdateUKWStatusCacheInfo with a empty infos! infos:[%+v]", infos)
		return nil
	}

	// 组装为更新结构体
	fieldMap := make(map[string]interface{})
	for _, info := range infos {
		// 获取field
		field := y.ChangeUKWUidToString(info.Uid)
		// 转化value
		valueByt, err := json.Marshal(info)
		if err != nil {
			log.WarnWithCtx(ctx, "BatchUpdateUKWStatusCacheInfo Marshal failed, info:[%+v], err:[%+v]", info, err)
			continue
		}
		fieldMap[field] = string(valueByt)
	}

	// 批量更新
	err := y.redisClient.HMSet(y.getUKWStatusInfosKey(), fieldMap).Err()
	if err != nil {
		log.ErrorWithCtx(ctx, "BatchUpdateUKWStatusCacheInfo HMSet failed, fieldMap:[%+v], err:[%+v]", fieldMap, err)
		return err
	}

	return nil
}

// DelUKWStatusCacheInfo 删除指定神秘人缓存信息
func (y *YouKnowWhoCache) DelUKWStatusCacheInfo(ctx context.Context, ukwUid uint32) error {
	if ukwUid == 0 {
		log.ErrorWithCtx(ctx, "DelUKWStatusInfo with a empty ukwUid! ukwUid:[%+v]", ukwUid)
		return nil
	}

	// 获取field
	field := y.ChangeUKWUidToString(ukwUid)
	// 删除对应的状态缓存
	err := y.redisClient.HDel(y.getUKWStatusInfosKey(), field).Err()
	if err != nil {
		log.ErrorWithCtx(ctx, "DelUKWStatusInfo HDel failed, err:[%+v]", err)
		return err
	}

	return nil
}

// GetUKWFakeUidInfosKey 获取神秘人假uid映射真uid的key
func (y *YouKnowWhoCache) GetUKWFakeUidInfosKey() string {
	return UKW_REDIS_FAKE_UID_KEY
}

// GetUKWFakeUidCacheInfo 根据假uid获取神秘人映射信息
func (y *YouKnowWhoCache) GetUKWFakeUidCacheInfo(ctx context.Context, fakeUid uint32) (*UKWCacheFakeUidInfo, error) {
	if fakeUid == 0 {
		log.ErrorWithCtx(ctx, "GetUKWFakeUidCacheInfo with a empty fakeUid! fakeUid:[%+v]", fakeUid)
		return nil, nil
	}

	// 获取field
	field := y.ChangeUKWUidToString(fakeUid)
	// 删除对应的状态缓存
	result, err := y.redisClient.HGet(y.GetUKWFakeUidInfosKey(), field).Result()
	if err != nil {
		if err == redis.Nil {
			return nil, nil
		}
		log.ErrorWithCtx(ctx, "GetUKWFakeUidCacheInfo HGet failed, err:[%+v]", err)
		return nil, err
	}

	// 转化为结构化数据返回
	fakeUidInfo := &UKWCacheFakeUidInfo{}
	err = json.Unmarshal([]byte(result), fakeUidInfo)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetUKWStatusCacheInfo Unmarshal failed, err:[%+v]", err)
	}

	log.InfoWithCtx(ctx, "GetUKWFakeUidCacheInfo successful, fakeUidInfo:[%+v]", fakeUidInfo)
	return fakeUidInfo, nil
}

// GetAllUKWFakeUidCacheInfos 查询当前神秘人假Uid到真Uid的映射关系
func (y *YouKnowWhoCache) GetAllUKWFakeUidCacheInfos(ctx context.Context) ([]*UKWCacheFakeUidInfo, error) {

	// 使用HSCAN范围扫描获取
	fakeUidInfos := make([]*UKWCacheFakeUidInfo, 0)
	var cursor uint64
	for {
		var keys []string
		var err error
		keys, cursor, err = y.redisClient.HScan(y.GetUKWFakeUidInfosKey(), cursor, "*", 200).Result()
		if err != nil {
			log.ErrorWithCtx(ctx, "GetAllUKWFakeUidCacheInfos HScan failed, err:[%+v]", err)
			return fakeUidInfos, err
		}

		for num, key := range keys {
			if num%2 == 0 { // 跳过key值
				continue
			}
			info := new(UKWCacheFakeUidInfo)
			err = json.Unmarshal([]byte(key), info)
			if err != nil {
				log.ErrorWithCtx(ctx, "GetAllUKWFakeUidCacheInfos Unmarshal failed, err:[%+v]", err)
				return fakeUidInfos, err
			}
			fakeUidInfos = append(fakeUidInfos, info)
		}

		if cursor == 0 { // 当等于0的时候说明已经全部获取到了
			break
		}
	}

	return fakeUidInfos, nil
}

// UpdateUKWFakeUidCacheInfo 更新神秘人假uid到真uid的映射关系
func (y *YouKnowWhoCache) UpdateUKWFakeUidCacheInfo(ctx context.Context, info *UKWCacheFakeUidInfo) error {

	// 判断是否为空
	if info == nil {
		errInfo := fmt.Errorf("UpdateUKWFakeUidCacheInfo with a empty info! info:[%+v]", info)
		log.ErrorWithCtx(ctx, errInfo.Error())
		return nil
	}

	// 获取field
	field := y.ChangeUKWUidToString(info.FakeUid)
	// 将结构体转化为string
	jsonStr, err := json.Marshal(info)
	if err != nil {
		log.WarnWithCtx(ctx, "UpdateUKWFakeUidCacheInfo Marshal failed, err:[%+v]", err)
	}

	// 更新对应的值
	err = y.redisClient.HSet(y.GetUKWFakeUidInfosKey(), field, string(jsonStr)).Err()
	if err != nil {
		log.ErrorWithCtx(ctx, "UpdateUKWFakeUidCacheInfo HSet failed, info:[%+v], err:[%+v]", string(jsonStr), err)
		return err
	}

	return nil
}

// BatchUpdateUKWFakeUidCacheInfo 批量更新神秘人假uid映射关系
func (y *YouKnowWhoCache) BatchUpdateUKWFakeUidCacheInfo(ctx context.Context, infos []*UKWCacheFakeUidInfo) error {
	// 判断是否为空
	if len(infos) == 0 {
		log.WarnWithCtx(ctx, "BatchUpdateUKWFakeUidCacheInfo with a empty infos! infos:[%+v]", infos)
		return nil
	}

	// 组装为更新结构体
	fieldMap := make(map[string]interface{})
	for _, info := range infos {
		// 获取field
		field := y.ChangeUKWUidToString(info.FakeUid)
		// 转化value
		valueByt, err := json.Marshal(info)
		if err != nil {
			log.WarnWithCtx(ctx, "BatchUpdateUKWFakeUidCacheInfo Marshal failed, info:[%+v], err:[%+v]", info, err)
			continue
		}
		fieldMap[field] = string(valueByt)
	}

	// 批量更新
	err := y.redisClient.HMSet(y.GetUKWFakeUidInfosKey(), fieldMap).Err()
	if err != nil {
		log.ErrorWithCtx(ctx, "BatchUpdateUKWFakeUidCacheInfo HMSet failed, fieldMap:[%+v], err:[%+v]", fieldMap, err)
		return err
	}

	return nil
}

// DelUKWFakeUidCacheInfo 删除指定神秘人假Uid映射关系
func (y *YouKnowWhoCache) DelUKWFakeUidCacheInfo(ctx context.Context, fakeUid uint32) error {
	if fakeUid == 0 {
		log.ErrorWithCtx(ctx, "DelUKWFakeUidCacheInfo with a empty fakeUid! fakeUid:[%+v]", fakeUid)
		return nil
	}

	// 获取field
	field := y.ChangeUKWUidToString(fakeUid)
	// 删除对应的状态缓存
	err := y.redisClient.HDel(y.GetUKWFakeUidInfosKey(), field).Err()
	if err != nil {
		log.ErrorWithCtx(ctx, "DelUKWFakeUidCacheInfo HDel failed, err:[%+v]", err)
		return err
	}

	return nil
}

// GetUKWNickNameUpdateKey 获取神秘人已更新昵称的用户列表
func (y *YouKnowWhoCache) getUKWInfoUpdateKey(t time.Time) string {
	return fmt.Sprintf(FMT_TWO_PARAM_CONST, UKW_REDIS_UPDATE_INFO_KEY, t.Format("20060102"))
}

// UpdateUKWInfoUpdate 更新当日已更新昵称和假uid的uid
func (y *YouKnowWhoCache) UpdateUKWInfoUpdate(ctx context.Context, uid uint32) error {

	// 判断是否为空
	if uid == 0 {
		errInfo := fmt.Errorf("UpdateUKWInfoUpdate with a empty uid! uid:[%+v]", uid)
		log.ErrorWithCtx(ctx, errInfo.Error())
		return nil
	}

	// 获取field
	field := y.ChangeUKWUidToString(uid)
	timeNow := time.Now()

	// 更新对应的值
	err := y.redisClient.HSet(y.getUKWInfoUpdateKey(timeNow), field, y.ChangeUKWUidToString(uid)).Err()
	if err != nil {
		log.ErrorWithCtx(ctx, "UpdateUKWInfoUpdate HSet failed, uid:[%+v], err:[%+v]", uid, err)
		return err
	}

	return nil
}

// GetAllUKWInfoUpdateInfo 查询当前所有神秘人已更新昵称和假uid的神秘人列表
func (y *YouKnowWhoCache) GetAllUKWInfoUpdateInfo(ctx context.Context) ([]uint32, error) {

	timeNow := time.Now()

	// 删除前一天刷新的缓存
	lastDay := time.Date(timeNow.Year(), timeNow.Month(), timeNow.Day()-1, 0, 0, 0, 0, time.Local)
	_ = y.redisClient.Del(y.getUKWInfoUpdateKey(lastDay)).Err()

	// 一次获取所有值
	result, err := y.redisClient.HGetAll(y.getUKWInfoUpdateKey(timeNow)).Result()
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAllUKWInfoUpdateInfo failed, err:[%+v]", err)
		return nil, err
	}

	// 解析结构
	uids := make([]uint32, 0)
	for _, info := range result {
		uid, err := strconv.Atoi(info)
		if err != nil {
			log.WarnWithCtx(ctx, "GetAllUKWInfoUpdateInfo Unmarshal statusInfo failed, info:[%+v], err:[%+v]",
				info, err)
		}
		log.DebugWithCtx(ctx, "GetAllUKWInfoUpdateInfo add info:[%+v]", info)
		uids = append(uids, uint32(uid))
	}

	return uids, nil
}

// GetUKWInfoUpdateInfo 判断指定uid的昵称和假uid是否更新过
func (y *YouKnowWhoCache) GetUKWInfoUpdateInfo(ctx context.Context, uid uint32) (bool, error) {
	if uid == 0 {
		log.ErrorWithCtx(ctx, "GetUKWInfoUpdateInfo with a empty fakeUid! fakeUid:[%+v]", uid)
		return false, nil
	}

	// 获取field
	field := y.ChangeUKWUidToString(uid)
	// 删除对应的状态缓存
	result, err := y.redisClient.HGet(y.getUKWInfoUpdateKey(time.Now()), field).Result()
	if err != nil {
		if err == redis.Nil {
			return false, nil
		}
		log.ErrorWithCtx(ctx, "GetUKWInfoUpdateInfo HGet failed, err:[%+v]", err)
		return false, err
	}

	log.InfoWithCtx(ctx, "GetUKWInfoUpdateInfo successful, result:[%+v]", result)
	// 判断是否已经更新过
	if result != "" {
		return true, nil
	}
	return false, nil
}

const FMT_TWO_PARAM_CONST = "%+v_%+v"

// GetUKWReportTaskFinishedKey 获取神秘人报表生成完成key
func (y *YouKnowWhoCache) getUKWReportTaskFinishedKey(t time.Time) string {
	return fmt.Sprintf(FMT_TWO_PARAM_CONST, UKW_REDIS_REPORT_FINISHED_KEY, t.Format("200601"))
}

// GetReportTaskFinishedTime 获取神秘人报表完成时间
func (y *YouKnowWhoCache) GetReportTaskFinishedTime(ctx context.Context, now time.Time) (string, error) {

	// 获取对应的月份的key
	key := y.getUKWReportTaskFinishedKey(now)

	// 获取完成时间
	result, err := y.redisClient.Get(key).Result()
	if err != nil {
		if err == redis.Nil {
			return "", nil
		}
		log.ErrorWithCtx(ctx, "GetReportTaskFinishedTime failed, time:[%+v], err:[%+v]", now, err)
		return "", err
	}

	return result, nil
}

// SetReportTaskFinishedTime 设置推送报表完成时间
func (y *YouKnowWhoCache) SetReportTaskFinishedTime(ctx context.Context, now time.Time) error {

	// 获取对应的月份的key
	key := y.getUKWReportTaskFinishedKey(now)

	// 将完成时间作为锁key
	// 这里锁的时间是2个小时
	err := y.redisClient.SetNX(key, now.String(), 2*time.Hour).Err()
	if err != nil {
		log.ErrorWithCtx(ctx, "SetReportTaskFinishedTime failed, time:[%+v], err:[%+v]", now, err)
		return err
	}

	return nil
}

// getUKWFakeUidUpdateFinishedKey 获取神秘人刷新假uid刷新完成的锁key
func (y *YouKnowWhoCache) getUKWFakeUidUpdateFinishedKey(t time.Time) string {
	return fmt.Sprintf(FMT_TWO_PARAM_CONST, UKW_REDIS_UPDATE_FAKE_UID_KEY, t.Format("20060102"))
}

// GetFakeUidUpdateFinishedTime 获取神秘人日假uid刷新完成时间
func (y *YouKnowWhoCache) GetFakeUidUpdateFinishedTime(ctx context.Context, now time.Time) (string, error) {

	// 获取对应的每日的key
	key := y.getUKWFakeUidUpdateFinishedKey(now)

	// 获取完成时间
	result, err := y.redisClient.Get(key).Result()
	if err != nil {
		if err == redis.Nil {
			return "", nil
		}
		log.ErrorWithCtx(ctx, "GetFakeUidUpdateFinishedTime failed, time:[%+v], err:[%+v]", now, err)
		return "", err
	}

	return result, nil
}

// SetFakeUidUpdateFinishedTime 设置完成每日假uid刷新的时间
func (y *YouKnowWhoCache) SetFakeUidUpdateFinishedTime(ctx context.Context, now time.Time) error {

	// 获取对应的日的key
	key := y.getUKWFakeUidUpdateFinishedKey(now)

	// 设置完成时间
	// 设置超时时间为一天
	err := y.redisClient.SetNX(key, now.String(), 24*time.Hour).Err()
	if err != nil {
		log.ErrorWithCtx(ctx, "SetFakeUidUpdateFinishedTime failed, time:[%+v], err:[%+v]", now, err)
		return err
	}

	return nil
}

// getUKWFakePoolUpdateFinishedKey 获取神秘人刷新假uid池子刷新完成的锁key
func (y *YouKnowWhoCache) getUKWFakePoolUpdateFinishedKey(t time.Time) string {
	return fmt.Sprintf(FMT_TWO_PARAM_CONST, UKW_REDIS_FAKE_POOL_FINISHED_KEY, t.Format("20060102"))
}

// GetFakePoolUpdateFinishedTime 获取神秘人日假uid池子刷新完成时间
func (y *YouKnowWhoCache) GetFakePoolUpdateFinishedTime(ctx context.Context, now time.Time) (string, error) {

	// 获取对应的每日的key
	key := y.getUKWFakePoolUpdateFinishedKey(now)

	// 获取完成时间
	result, err := y.redisClient.Get(key).Result()
	if err != nil {
		if err == redis.Nil {
			return "", nil
		}
		log.ErrorWithCtx(ctx, "GetFakePoolUpdateFinishedTime failed, time:[%+v], err:[%+v]", now, err)
		return "", err
	}

	return result, nil
}

// SetFakePoolUpdateFinishedTime 设置完成每日假uid池子刷新的时间
func (y *YouKnowWhoCache) SetFakePoolUpdateFinishedTime(ctx context.Context, now time.Time) error {

	// 获取对应的日的key
	key := y.getUKWFakePoolUpdateFinishedKey(now)

	// 设置完成时间
	// 设置超时时间为一天
	err := y.redisClient.SetNX(key, now.String(), 24*time.Hour).Err()
	if err != nil {
		log.ErrorWithCtx(ctx, "SetFakePoolUpdateFinishedTime failed, time:[%+v], err:[%+v]", now, err)
		return err
	}

	return nil
}

// GetUKWRankSwitchLockKey 获取神秘人排行榜推送开关的key
func (y *YouKnowWhoCache) getUKWRankSwitchLockKey(uid uint32) string {
	return fmt.Sprintf(FMT_TWO_PARAM_CONST, UKW_REDIS_RANK_SWITCH_LOCK, uid)
}

// GetRankSwitchLock 判断是否已有排行榜操作锁
func (y *YouKnowWhoCache) GetRankSwitchLock(ctx context.Context, uid uint32) (bool, error) {
	// 获取对应的月份的key
	key := y.getUKWRankSwitchLockKey(uid)

	// 获取完成时间
	result, err := y.redisClient.Get(key).Result()
	if err != nil {
		if err == redis.Nil {
			return true, nil
		}
		log.ErrorWithCtx(ctx, "GetRankSwitchLock failed, uid:[%+v], err:[%+v]", uid, err)
		return true, err
	}

	// 如果存在锁，返回false
	if result != "" {
		return false, err
	}

	return true, nil
}

// SetRankSwitchLock 设置排行榜开关切换锁
func (y *YouKnowWhoCache) SetRankSwitchLock(ctx context.Context, uid uint32, expireTime int64) error {

	// 获取对应的月份的key
	key := y.getUKWRankSwitchLockKey(uid)

	// 设置完成时间
	// 设置超时时间两个月
	err := y.redisClient.SetNX(key, uid, time.Duration(expireTime)*time.Second).Err()
	if err != nil {
		log.ErrorWithCtx(ctx, "SetRankSwitchLock failed, uid:[%+v], expireTime:[%+v], err:[%+v]", uid, expireTime, err)
		return err
	}

	return nil
}

func (y *YouKnowWhoCache) GetLockCache() *mutex.DistributedLock {
	if y.distributedLock != nil {
		return y.distributedLock
	}
	return &mutex.DistributedLock{}
}

func (y *YouKnowWhoCache) Lock(ctx context.Context, key string, duration time.Duration) bool {
	log.DebugWithCtx(ctx, "Lock with key:[%+v], duration:[%+v]", key, duration)
	return y.redisClient.SetNX(key, "1", duration).Val()
}

func (y *YouKnowWhoCache) UnLock(ctx context.Context, key string) error {
	log.DebugWithCtx(ctx, "UnLock with key:[%+v], duration:[%+v]", key)
	return y.redisClient.Del(key).Err()
}

func (y *YouKnowWhoCache) getFakeUidPoolKey() string {
	return UKW_REDIS_FAKE_UID_POOL
}

// SetFakeUidList 设置新的假uid号码池
func (y *YouKnowWhoCache) SetFakeUidList(ctx context.Context, fakeUidList []uint32) error {

	// 获取key
	key := y.getFakeUidPoolKey()

	// 循环设置对应的号码
	for _, fakeUid := range fakeUidList {
		err := y.redisClient.LPush(key, fakeUid).Err()
		if err != nil {
			log.ErrorWithCtx(ctx, "FakeUidPool SetFakeUidList LPush failed, key:[%+v], err:[%+v]", key, err)
			return err
		}

		// 刷完一个号码等待10ms，保证不会造成阻塞
		time.Sleep(10 * time.Millisecond)
	}

	return nil
}

// GetFakeUid 获取一个神秘人假uid
func (y *YouKnowWhoCache) GetFakeUid(ctx context.Context) (uint32, error) {

	// 获取key
	key := y.getFakeUidPoolKey()

	// 获取对应号码池的号码
	numStr, err := y.redisClient.RPop(key).Result()
	if err != nil {
		log.ErrorWithCtx(ctx, "FakeUidPool GetFakeUid RPop failed, key:[%+v], err:[%+v]", key, err)
		return 0, err
	}

	// 将字符串转换为数字
	var number uint32
	num, err := strconv.Atoi(numStr)
	if err != nil {
		log.ErrorWithCtx(ctx, "FakeUidPool GetFakeUid Atoi failed, key:[%+v], err:[%+v]", key, err)
		return 0, err
	}
	number = uint32(num)

	return number, nil
}

// GetFakeUidPoolLen 获取剩余的假uid数量
func (y *YouKnowWhoCache) GetFakeUidPoolLen(ctx context.Context) (uint32, error) {

	// 获取key
	key := y.getFakeUidPoolKey()

	// 查询剩余号码量
	poolLen, err := y.redisClient.LLen(key).Result()
	if err != nil {
		log.ErrorWithCtx(ctx, "FakeUidPool GetFakeUidPoolLen LLen failed, key:[%+v], err:[%+v]", key, err)
		return 0, err
	}

	return uint32(poolLen), nil
}

// GetAllFakeUidPoolUidList 获取所有假号码池子中的号码
func (y *YouKnowWhoCache) GetAllFakeUidPoolUidList(ctx context.Context) ([]string, error) {

	// 获取key
	key := y.getFakeUidPoolKey()

	// 查询剩余号码量
	result, err := y.redisClient.LRange(key, 0, -1).Result()
	if err != nil {
		log.ErrorWithCtx(ctx, "FakeUidPool GetFakeUidPoolLen LLen failed, key:[%+v], err:[%+v]", key, err)
		return result, err
	}

	return result, nil
}
