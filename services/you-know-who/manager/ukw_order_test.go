package manager

import (
	"bou.ke/monkey"
)

func init() {
	monkey.UnpatchAll()
}

//func TestManager_CheckOpenUKWOrder(t *testing.T) {
//	ctl := gomock.NewController(t)
//	defer ctl.Finish()
//	mockStore := mocks.NewMockIStore(ctl)
//	ctx := context.Background()
//	uid := uint32(12345)
//	orderId := "TT_UKW_12345"
//	openTime := time.Now()
//	gomock.InOrder(
//		mockStore.EXPECT().GetUKWOrder(ctx, uid, orderId, openTime).Return(&mysql.YouKnowWhoOrderTable{OrderId: orderId, Uid: 12345}, nil),
//	)
//	type fields struct {
//		mysqlStore     mysql.IStore
//		prodPermission events.IUKWPermissionProduce
//	}
//	type args struct {
//		ctx      context.Context
//		orderId  string
//		uid      uint32
//		openTime time.Time
//	}
//	tests := []struct {
//		name         string
//		fields       fields
//		args         args
//		wantHasOrder bool
//		wantErr      bool
//	}{
//		{
//			name: "测试校验订单接口",
//			fields: fields{
//				mysqlStore:     mockStore,
//				prodPermission: &events.UKWPermissionProduce{},
//			},
//			args: args{
//				ctx:      ctx,
//				orderId:  orderId,
//				uid:      uid,
//				openTime: openTime,
//			},
//			wantHasOrder: true,
//			wantErr:      false,
//		},
//	}
//	for _, tt := range tests {
//		t.Run(tt.name, func(t *testing.T) {
//			m := &Manager{
//				mysqlStore:     tt.fields.mysqlStore,
//				prodPermission: tt.fields.prodPermission,
//			}
//			gotHasOrder, err := m.CheckOpenUKWOrder(tt.args.ctx, tt.args.orderId, tt.args.uid, tt.args.openTime)
//			if (err != nil) != tt.wantErr {
//				t.Errorf("CheckOpenUKWOrder() error = %v, wantErr %v", err, tt.wantErr)
//				return
//			}
//			if gotHasOrder != tt.wantHasOrder {
//				t.Errorf("CheckOpenUKWOrder() gotHasOrder = %v, want %v", gotHasOrder, tt.wantHasOrder)
//			}
//		})
//	}
//}

//func TestManager_OrderTimeRangeInfo(t *testing.T) {
//	ctl := gomock.NewController(t)
//	defer ctl.Finish()
//	mockStore := mocks.NewMockIStore(ctl)
//	ctx := context.Background()
//	now := time.Now()
//	start := time.Date(now.Year(), now.Month(), now.Day(), now.Hour(), now.Minute(), 0, 0, time.Local)
//	end := time.Date(now.Year(), now.Month(), now.Day(), now.Hour(), now.Minute()+1, 0, 0, time.Local)
//	gomock.InOrder(
//		mockStore.EXPECT().GetOrderInfoForTimeRange(ctx, start, end).Return(uint32(1), uint32(1), nil),
//	)
//	type fields struct {
//		mysqlStore     mysql.IStore
//		prodPermission events.IUKWPermissionProduce
//	}
//	type args struct {
//		ctx context.Context
//		in  *pb.OrderTimeRangeInfoReq
//	}
//	tests := []struct {
//		name    string
//		fields  fields
//		args    args
//		wantOut *pb.OrderTimeRangeInfoResp
//		wantErr bool
//	}{
//		{
//			name: "对账接口查询",
//			fields: fields{
//				mysqlStore:     mockStore,
//				prodPermission: &events.UKWPermissionProduce{},
//			},
//			args: args{
//				ctx: ctx,
//				in: &pb.OrderTimeRangeInfoReq{
//					BeginTime: start.Unix(),
//					EndTime:   end.Unix(),
//				},
//			},
//			wantOut: &pb.OrderTimeRangeInfoResp{
//				Count: 1,
//				Value: 1,
//			},
//			wantErr: false,
//		},
//	}
//	for _, tt := range tests {
//		t.Run(tt.name, func(t *testing.T) {
//			m := &Manager{
//				mysqlStore:     tt.fields.mysqlStore,
//				prodPermission: tt.fields.prodPermission,
//			}
//			gotOut, err := m.OrderTimeRangeInfo(tt.args.ctx, tt.args.in)
//			if (err != nil) != tt.wantErr {
//				t.Errorf("OrderTimeRangeInfo() error = %v, wantErr %v", err, tt.wantErr)
//				return
//			}
//			if !reflect.DeepEqual(gotOut, tt.wantOut) {
//				t.Errorf("OrderTimeRangeInfo() gotOut = %v, want %v", gotOut, tt.wantOut)
//			}
//		})
//	}
//}

//func TestManager_OpenUKW(t *testing.T) {
//
//	orderId := "TEST-123"
//	price := int64(199900)
//	monkey.PatchInstanceMethod(reflect.TypeOf(&conf.UKWWhitelistDynamic{}), "GetUKWWhitelistMap", func(c *conf.UKWWhitelistDynamic,
//	) map[uint32]*conf.WhiteConfig {
//		return map[uint32]*conf.WhiteConfig{
//			12345: &conf.WhiteConfig{
//				Uid:           12345,
//				CanBuy:        true,
//				NeedTrueOrder: true,
//			},
//		}
//	})
//
//	monkey.Patch(deal_token.Decode, func(dealTokenStr string) (*deal_token.DealToken, error) {
//		return &deal_token.DealToken{
//			OrderID:    orderId,
//			BuyerID:    12345,
//			TotalPrice: price,
//		}, nil
//	})
//
//	monkey.Patch(deal_token.NewDealTokenData, func(tradeNo string, orderID string, serverName string, buyerID int64, totalPrice int64) *deal_token.DealToken {
//		return &deal_token.DealToken{
//			OrderID:    orderId,
//			BuyerID:    12345,
//			TotalPrice: price,
//		}
//	})
//
//	monkey.Patch(deal_token.AddDealToken, func(orgDealTokenStr string, newDealToken *deal_token.DealToken) (string, error) {
//		return "test", nil
//	})
//
//	now := time.Now()
//	monkey.PatchInstanceMethod(reflect.TypeOf(&mysql.Store{}), "GetUKWPermissionInfo", func(s *mysql.Store,
//		ctx context.Context, uid uint32) (*mysql.YouKnowWhoPermissionTable, error) {
//		return &mysql.YouKnowWhoPermissionTable{
//			Id:            1,
//			Uid:           12345,
//			FakeUid:       23456,
//			Nickname:      "神秘人66666",
//			Status:        0,
//			Switch:        0,
//			RankSwitch:    0,
//			EnterNotice:   0,
//			EffectiveTime: 0,
//			TotalTime:     864000,
//			OpenTime:      now,
//			CreateTime:    now,
//			UpdateTime:    now,
//			DelFlag:       0,
//		}, nil
//	})
//
//	monkey.PatchInstanceMethod(reflect.TypeOf(&conf.YouKnowWhoOrderDynamic{}), "GetOrderType", func(c *conf.YouKnowWhoOrderDynamic,
//		typeId uint32) *conf.OrderType {
//		return &conf.OrderType{
//			OrderTypeId:      1,
//			OrderTypeName:    "神秘人套餐测试",
//			Price:            199900,
//			EffectiveTime:    864000,
//			Discount:         "0.9",
//			OriginalPrice:    233333,
//			PkgId:            666,
//			PushLevel:        0,
//			PushRollingCount: 0,
//			PushRollingTime:  0,
//			PushOpenMsg:      false,
//		}
//	})
//
//	monkey.Patch(time.Now, func() time.Time {
//		return now
//	})
//
//	monkey.PatchInstanceMethod(reflect.TypeOf(&ttversion.Feature{}), "Atleast", func(f *ttversion.Feature,
//		clientType uint16, clientVersion uint32) bool {
//		return true
//	})
//
//	monkey.PatchInstanceMethod(reflect.TypeOf(&nobilityCli.Client{}), "GetNobilityInfo", func(c *nobilityCli.Client,
//		ctx context.Context, uid uint32, queryRecords bool) (*Nobility.NobilityInfo, protocol.ServerError) {
//		return &Nobility.NobilityInfo{
//			Invisible: false,
//		}, nil
//	})
//
//	monkey.PatchInstanceMethod(reflect.TypeOf(&nobilityCli.Client{}), "SetInvisibleStatus", func(c *nobilityCli.Client,
//		ctx context.Context, uid uint32, status int32) (*Nobility.SetInvisibleStatusResp, protocol.ServerError) {
//		return &Nobility.SetInvisibleStatusResp{}, nil
//	})
//
//	monkey.PatchInstanceMethod(reflect.TypeOf(&mysql.Store{}), "OpenUKW", func(c *mysql.Store,
//		ctx context.Context, orderInfo *mysql.YouKnowWhoOrderTable, permissionInfo *mysql.YouKnowWhoPermissionTable, flowInfo *mysql.YouKnowWhoFlowTable,
//		mappingRelation *mysql.UKWFakeUidMappingRelationsTable) error {
//		return nil
//	})
//
//	monkey.PatchInstanceMethod(reflect.TypeOf(&Manager{}), "RefreshSingleUKWInfo", func(m *Manager,
//		ctx context.Context, uid uint32) {
//	})
//
//	monkey.PatchInstanceMethod(reflect.TypeOf(&Manager{}), "RefreshSingleUKWFakeUid", func(m *Manager,
//		ctx context.Context, uid uint32, fakeUid uint32, nickName string) {
//	})
//
//	monkey.Patch(client.SendOneBackpackWithRiskControlForUKW, func(ctx context.Context, uid uint32, pkgId uint32, svrTime time.Time,
//		orderId string, isReissue bool) error {
//		return nil
//	})
//
//	monkey.Patch(client.GetPkgItemInfo, func(ctx context.Context, uid, pkgId uint32) (*client.PkgInfo, error) {
//		return &client.PkgInfo{
//			PkgId: 666,
//			Items: []*client.ItemInfo{
//				{
//					ItemId: 233,
//					Name:   "好东西",
//					Num:    1,
//					Price:  100000,
//				},
//			},
//		}, nil
//	})
//
//	monkey.PatchInstanceMethod(reflect.TypeOf(&mysql.Store{}), "UpdateOrderPkgSendType", func(m *mysql.Store,
//		ctx context.Context, orderId string, openTime time.Time, giftPrice uint32, pkgSendType uint32) error {
//		return nil
//	})
//
//	monkey.Patch(client.SendHelpMsg, func(ctx context.Context, uid uint32, msg, hlight, url string) error {
//		return nil
//	})
//
//	monkey.PatchInstanceMethod(reflect.TypeOf(&events.UKWTBeanKafkaProduce{}), "ProduceTBeanConsumeEvent", func(p *events.UKWTBeanKafkaProduce,
//		uid uint32, value uint32, timestamp uint32, order string) {
//	})
//
//	type fields struct {
//		cacheStore     cache.IYouKnowWhoCache
//		mysqlStore     mysql.IStore
//		sc             conf.IServiceConfigT
//		numberPool     numberpool.INumberPool
//		prodTBean      events.IUKWTBeanKafkaProduce
//		prodPermission events.IUKWPermissionProduce
//	}
//	type args struct {
//		ctx context.Context
//		in  *pb.OpenUKWReq
//	}
//	tests := []struct {
//		name    string
//		fields  fields
//		args    args
//		wantOut *pb.OpenUKWResp
//		wantErr bool
//	}{
//		{
//			name: "测试开通",
//			fields: fields{
//				mysqlStore:     &mysql.Store{},
//				cacheStore:     &cache.YouKnowWhoCache{},
//				prodPermission: &events.UKWPermissionProduce{},
//				prodTBean:      &events.UKWTBeanKafkaProduce{},
//			},
//			args: args{
//				ctx: context.Background(),
//				in: &pb.OpenUKWReq{
//					Uid:           12345,
//					OrderId:       orderId,
//					OpenTime:      864000,
//					Token:         "2333333333",
//					Price:         199900,
//					OrderType:     1,
//					ClientVersion: 1,
//					ClientType:    1,
//				},
//			},
//			wantErr: false,
//			wantOut: &pb.OpenUKWResp{
//				Uid:        12345,
//				ServerTime: uint32(now.Unix()),
//				ExpireTime: uint32(now.Unix() + 864000),
//			},
//		},
//	}
//	for _, tt := range tests {
//		t.Run(tt.name, func(t *testing.T) {
//			m := &Manager{
//				cacheStore:     tt.fields.cacheStore,
//				mysqlStore:     tt.fields.mysqlStore,
//				sc:             tt.fields.sc,
//				numberPool:     tt.fields.numberPool,
//				prodTBean:      tt.fields.prodTBean,
//				prodPermission: tt.fields.prodPermission,
//			}
//			gotOut, err := m.OpenUKW(tt.args.ctx, tt.args.in)
//			if (err != nil) != tt.wantErr {
//				t.Errorf("OpenUKW() error = %v, wantErr %v", err, tt.wantErr)
//				return
//			}
//			if !reflect.DeepEqual(gotOut, tt.wantOut) {
//				t.Errorf("OpenUKW() gotOut = %v, want %v", gotOut, tt.wantOut)
//			}
//		})
//	}
//}
