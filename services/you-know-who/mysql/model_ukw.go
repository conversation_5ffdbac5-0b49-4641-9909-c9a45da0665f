package mysql

import (
	"fmt"
	"time"
)

// YouKnowWhoPermissionTable 神秘人权限信息表
type YouKnowWhoPermissionTable struct {
	Id            uint64    `gorm:"column:id;PRIMARY" db:"id" json:"id" form:"id"`
	Uid           uint32    `gorm:"column:uid;PRIMARY" db:"uid" json:"uid" form:"uid"`                                     // 神秘人UID
	FakeUid       uint32    `gorm:"column:fake_uid" db:"fake_uid" json:"fake_uid" form:"fake_uid"`                         // 神秘人假uid
	Nickname      string    `gorm:"column:nickname" db:"nickname" json:"nickname" form:"nickname"`                         // 神秘人昵称
	Status        uint32    `gorm:"column:status" db:"status" json:"status" form:"status"`                                 // 当前生效状态：0：未生效或已过期；1:已开通；2：已冻结；3:已强制封禁
	Switch        uint32    `gorm:"column:switch" db:"switch" json:"switch" form:"switch"`                                 // 当前开关状态：0：关闭；1:开启
	RankSwitch    uint32    `gorm:"column:rank_switch" db:"rank_switch" json:"rank_switch" form:"rank_switch"`             // 当前周榜/爱意榜开关状态：0：关闭；1：开启
	EnterNotice   uint32    `gorm:"column:enter_notice" db:"enter_notice" json:"enter_notice" form:"enter_notice"`         // 进房开启神秘人提醒：0：关闭；1：开启
	EffectiveTime uint64    `gorm:"column:effective_time" db:"effective_time" json:"effective_time" form:"effective_time"` // 剩余生效时间，单位秒（s）
	TotalTime     uint64    `gorm:"column:total_time" db:"total_time" json:"total_time" form:"total_time"`                 // 已开通总天数
	OpenTime      time.Time `gorm:"column:open_time" db:"open_time" json:"open_time" form:"open_time"`                     // 首次开通时间
	CreateTime    time.Time `gorm:"column:create_time" db:"create_time" json:"create_time" form:"create_time"`             // 记录创建时间
	UpdateTime    time.Time `gorm:"column:update_time" db:"update_time" json:"update_time" form:"update_time"`             // 记录更新时间
	DelFlag       uint32    `gorm:"column:del_flag" db:"del_flag" json:"del_flag" form:"del_flag"`                         // 删除状态：0：未删除；1:已删除
}

func (y *YouKnowWhoPermissionTable) TableName() string {
	return "you_know_who_permission"
}

// CreateUKWPermissionTable 神秘人权限信息表建表语句
var CreateUKWPermissionTable = `
CREATE TABLE you_know_who_permission (
	id BIGINT ( 20 ) UNSIGNED NOT NULL AUTO_INCREMENT,
	uid INT ( 10 ) UNSIGNED NOT NULL COMMENT '神秘人UID',
	fake_uid INT ( 10 ) UNSIGNED NOT NULL COMMENT '神秘人假UID（每日更新）',
	nickname VARCHAR ( 256 ) NOT NULL COMMENT '神秘人昵称',
	status TINYINT ( 8 ) NOT NULL COMMENT '当前生效状态：0：未生效或已过期；1:已开通；2：已冻结；3:已强制封禁',
	switch TINYINT ( 8 ) NOT NULL COMMENT '当前开关状态：0：关闭；1:开启',
	rank_switch TINYINT ( 8 ) NOT NULL COMMENT '当前周榜/爱意榜开关状态：0：关闭；1:开启',
	enter_notice TINYINT ( 8 ) NOT NULL COMMENT '进房开启神秘人提醒：0：关闭；1:开启',
	effective_time INT ( 10 ) UNSIGNED NOT NULL COMMENT '剩余生效时间，单位秒（s）',
	total_time INT ( 10 ) UNSIGNED NOT NULL COMMENT '已开通总天数',
	open_time TIMESTAMP NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '首次开通时间',
	create_time TIMESTAMP NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '记录创建时间',
	update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',
	del_flag TINYINT ( 2 ) NOT NULL COMMENT '删除状态：0：未删除；1:已删除',
	PRIMARY KEY ( id, uid ),
	KEY del ( del_flag ),
	KEY ukw ( uid, del_flag ),
	KEY update_info ( update_time, del_flag ),
	KEY effective_info ( effective_time, del_flag ) 
) ENGINE = INNODB DEFAULT CHARSET = utf8 COMMENT = '神秘人权限信息表';
`

// YouKnowWhoOrderTable 神秘人订单详情表
type YouKnowWhoOrderTable struct {
	OrderId       string    `gorm:"column:order_id;PRIMARY" db:"order_id" json:"order_id" form:"order_id"`                     // 订单ID
	Uid           uint32    `gorm:"column:uid" db:"uid" json:"uid" form:"uid"`                                                 // 神秘人UID
	Price         uint32    `gorm:"column:price" db:"price" json:"price" form:"price"`                                         // 订单价值
	GiftPrice     uint32    `gorm:"column:gift_price" db:"gift_price" json:"gift_price" form:"gift_price"`                     // 赠送礼物价值
	EffectiveTime uint64    `gorm:"column:effective_time" db:"effective_time" json:"effective_time" form:"effective_time"`     // 订单生效时间，单位秒（s）
	OrderType     uint32    `gorm:"column:order_type" db:"order_type" json:"order_type" form:"order_type"`                     // 订单类型，详见配置文件
	OrderTypeName string    `gorm:"column:order_type_name" db:"order_type_name" json:"order_type_name" form:"order_type_name"` // 订单套餐名称
	PkgSend       uint32    `gorm:"column:pkg_send" db:"pkg_send" json:"pkg_send" form:"pkg_send"`                             // 是否已完成包裹发放
	OpenTime      time.Time `gorm:"column:open_time" db:"open_time" json:"open_time" form:"open_time"`                         // 订单购买时间
	UpdateTime    time.Time `gorm:"column:update_time" db:"update_time" json:"update_time" form:"update_time"`                 // 记录更新时间
	DelFlag       uint32    `gorm:"column:del_flag" db:"del_flag" json:"del_flag" form:"del_flag"`                             // 删除状态：0：未删除；1:已删除
}

const UKWOrderTbl = "you_know_who_order"

// getUKWOrderTbl 获取神秘人订单表名称，例：you_know_who_order_202207
func getUKWOrderTbl(t time.Time) string {
	return fmt.Sprintf("%s_%s", UKWOrderTbl, t.Format("200601"))
}

// CreateUKWOrderTable 神秘人订单详情表建表语句
var CreateUKWOrderTable = `
CREATE TABLE %+v (
	order_id VARCHAR ( 256 ) NOT NULL COMMENT '订单ID',
	uid INT ( 10 ) UNSIGNED NOT NULL COMMENT '神秘人UID',
	price INT ( 10 ) UNSIGNED NOT NULL COMMENT '订单价值',
    gift_price INT ( 10 ) UNSIGNED NOT NULL COMMENT '赠送礼物价值',
	effective_time INT ( 10 ) UNSIGNED NOT NULL COMMENT '订单生效时间，单位秒（s）',
	order_type INT ( 10 ) UNSIGNED NOT NULL COMMENT '订单类型，详见配置文件',
    order_type_name VARCHAR ( 256 ) NOT NULL COMMENT '订单套餐名称',
	pkg_send TINYINT ( 2 ) NOT NULL COMMENT '是否已完成包裹发放',
	open_time TIMESTAMP NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '订单购买时间',
	update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',
	del_flag TINYINT ( 2 ) NOT NULL COMMENT '删除状态：0：未删除；1:已删除',
	PRIMARY KEY ( order_id ),
	KEY fast_order ( order_id, del_flag ),
	KEY update_info ( update_time, del_flag ) 
) ENGINE = INNODB DEFAULT CHARSET = utf8 COMMENT = '神秘人订单详情表';
`

// YouKnowWhoFlowTable 神秘人权限变更流水表
type YouKnowWhoFlowTable struct {
	Id            uint64    `gorm:"column:id;PRIMARY" db:"id" json:"id" form:"id"`
	Uid           uint32    `gorm:"column:uid" db:"uid" json:"uid" form:"uid"`                                                 // 神秘人UID
	OldStatus     uint32    `gorm:"column:old_status" db:"old_status" json:"old_status" form:"old_status"`                     // 原生效状态：0：未生效或已过期；1:已开通；2：已冻结；3:已强制封禁
	NewStatus     uint32    `gorm:"column:new_status" db:"new_status" json:"new_status" form:"new_status"`                     // 当前生效状态：0：未生效或已过期；1:已开通；2：已冻结；3:已强制封禁
	OldRankSwitch uint32    `gorm:"column:old_rank_switch" db:"old_rank_switch" json:"old_rank_switch" form:"old_rank_switch"` // 原榜单开关状态：0：关闭；1:开启
	NewRankSwitch uint32    `gorm:"column:new_rank_switch" db:"new_rank_switch" json:"new_rank_switch" form:"new_rank_switch"` // 现榜单开关状态：0：关闭；1:开启
	TimeLeft      uint64    `gorm:"column:time_left" db:"time_left" json:"time_left" form:"time_left"`                         // 变更前剩余生效时间，单位秒（s）
	EffectiveTime uint64    `gorm:"column:effective_time" db:"effective_time" json:"effective_time" form:"effective_time"`     // 剩余生效时间，单位秒（s）
	StartTime     time.Time `gorm:"column:start_time" db:"start_time" json:"start_time" form:"start_time"`                     // 当前生效开始时间
	ChangeReason  string    `gorm:"column:change_reason" db:"change_reason" json:"change_reason" form:"change_reason"`         // 变更原因
	CreateTime    time.Time `gorm:"column:create_time" db:"create_time" json:"create_time" form:"create_time"`                 // 记录创建时间
	UpdateTime    time.Time `gorm:"column:update_time" db:"update_time" json:"update_time" form:"update_time"`                 // 记录更新时间
	DelFlag       uint32    `gorm:"column:del_flag" db:"del_flag" json:"del_flag" form:"del_flag"`                             // 删除状态：0：未删除；1:已删除
}

const UKWStatusFlowTbl = "you_know_who_flow"

// getUKWFlowTbl 获取神秘人流水表名称，例：you_know_who_flow_202207
func getUKWFlowTbl(t time.Time) string {
	return fmt.Sprintf("%s_%s", UKWStatusFlowTbl, t.Format("200601"))
}

// CreateUKWFlowTable 神秘人权限状态变更表建表语句
var CreateUKWFlowTable = `
CREATE TABLE %+v (
	id BIGINT ( 20 ) UNSIGNED NOT NULL AUTO_INCREMENT,
	uid INT ( 10 ) UNSIGNED NOT NULL COMMENT '神秘人UID',
	old_status TINYINT ( 8 ) NOT NULL COMMENT '原生效状态：0：未生效或已过期；1:已开通；2：已冻结；3:已强制封禁',
	new_status TINYINT ( 8 ) NOT NULL COMMENT '当前生效状态：0：未生效或已过期；1:已开通；2：已冻结；3:已强制封禁',
    old_rank_switch TINYINT ( 8 ) NOT NULL COMMENT '原榜单开关状态：0：关闭；1:开启',
	new_rank_switch TINYINT ( 8 ) NOT NULL COMMENT '当前榜单开关状态：0：关闭；1:开启',
	time_left INT ( 10 ) UNSIGNED NOT NULL COMMENT '变更前剩余生效时间，单位秒（s）',
	effective_time INT ( 10 ) UNSIGNED NOT NULL COMMENT '变更后剩余生效时间，单位秒（s）',
	start_time TIMESTAMP NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '当前生效开始时间',
	change_reason VARCHAR ( 256 ) NOT NULL COMMENT '变更原因',
	create_time TIMESTAMP NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '记录创建时间',
	update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',
	del_flag TINYINT ( 2 ) NOT NULL COMMENT '删除状态：0：未删除；1:已删除',
	PRIMARY KEY ( id ),
	KEY ukw ( uid, del_flag ),
	KEY update_info ( update_time, del_flag ),
	KEY effective_info ( effective_time, del_flag ) 
) ENGINE = INNODB DEFAULT CHARSET = utf8 COMMENT = '神秘人权限变更流水表';
`

// YouKnowWhoOrderReportTable 神秘人财务报表统计订单消耗表
type YouKnowWhoOrderReportTable struct {
	OrderId       string    `gorm:"column:order_id;PRIMARY" db:"order_id" json:"order_id" form:"order_id"`                     // 订单ID
	Uid           uint32    `gorm:"column:uid" db:"uid" json:"uid" form:"uid"`                                                 // 神秘人UID
	Price         uint32    `gorm:"column:price" db:"price" json:"price" form:"price"`                                         // 订单价值
	GiftPrice     uint32    `gorm:"column:gift_price" db:"gift_price" json:"gift_price" form:"gift_price"`                     // 赠送礼物价值
	OrderType     uint32    `gorm:"column:order_type" db:"order_type" json:"order_type" form:"order_type"`                     // 订单类型，详见配置文件
	OrderTypeName string    `gorm:"column:order_type_name" db:"order_type_name" json:"order_type_name" form:"order_type_name"` // 订单套餐名称
	EffectiveTime uint64    `gorm:"column:effective_time" db:"effective_time" json:"effective_time" form:"effective_time"`     // 订单生效时间，单位秒（s）
	EffectiveLeft uint64    `gorm:"column:effective_left" db:"effective_left" json:"effective_left" form:"effective_left"`     // 剩余生效时间，单位秒（s）
	OpenTime      time.Time `gorm:"column:open_time" db:"open_time" json:"open_time" form:"open_time"`                         // 订单购买时间
	UseStartTime  time.Time `gorm:"column:use_start_time" db:"use_start_time" json:"use_start_time" form:"use_start_time"`     // 最初使用时间
	UseEndTime    time.Time `gorm:"column:use_end_time" db:"use_end_time" json:"use_end_time" form:"use_end_time"`             // 最终使用时间
	UpdateTime    time.Time `gorm:"column:update_time" db:"update_time" json:"update_time" form:"update_time"`                 // 记录更新时间
	DelFlag       uint32    `gorm:"column:del_flag" db:"del_flag" json:"del_flag" form:"del_flag"`                             // 删除状态：0：未删除；1:已删除
}

const UKWOrderReportTbl = "you_know_who_order_report"

// getUKWOrderReportTbl 获取神秘人财务报表订单统计表名称，例：you_know_who_order_report_202207
func getUKWOrderReportTbl(t time.Time) string {
	return fmt.Sprintf("%s_%s", UKWOrderReportTbl, t.Format("200601"))
}

// CreateUKWOrderReportTable 神秘人财务报表统计订单消耗建表语句
var CreateUKWOrderReportTable = `
CREATE TABLE %+v (
	order_id VARCHAR ( 256 ) NOT NULL COMMENT '订单ID',
	uid INT ( 10 ) UNSIGNED NOT NULL COMMENT '神秘人UID',
	price INT ( 10 ) UNSIGNED NOT NULL COMMENT '订单价值',
    gift_price INT ( 10 ) UNSIGNED NOT NULL COMMENT '礼物价值',
    order_type INT ( 10 ) UNSIGNED NOT NULL COMMENT '订单类型，详见配置文件',
    order_type_name VARCHAR ( 256 ) NOT NULL COMMENT '订单套餐名称',
	effective_time INT ( 10 ) UNSIGNED NOT NULL COMMENT '订单生效时间，单位秒（s）',
    effective_left INT ( 10 ) UNSIGNED NOT NULL COMMENT '剩余生效时间，单位秒（s）',
	open_time TIMESTAMP NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '订单购买时间',
    use_start_time TIMESTAMP NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '最初使用时间',
    use_end_time TIMESTAMP NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '最终使用时间',
	update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',
	del_flag TINYINT ( 2 ) NOT NULL COMMENT '删除状态：0：未删除；1:已删除',
	PRIMARY KEY ( order_id ),
	KEY fast_order ( order_id, del_flag ),
	KEY update_info ( update_time, del_flag )
) ENGINE = INNODB DEFAULT CHARSET = utf8 COMMENT = '神秘人财务报表统计订单消耗表';
`

// YouKnowWhoReportTable 神秘人财务统计汇总表
type YouKnowWhoReportTable struct {
	Period               time.Time `gorm:"column:report_period;PRIMARY" db:"report_period" json:"report_period" form:"report_period"`                             // 账期
	ProductID            string    `gorm:"column:product_id" db:"product_id" json:"product_id" form:"product_id"`                                                 // 产品ID
	Name                 string    `gorm:"column:name" db:"name" json:"name" form:"name"`                                                                         // 产品名称
	Price                uint32    `gorm:"column:price;PRIMARY" db:"price" json:"price" form:"price"`                                                             // 产品单价
	GiftPrice            uint32    `gorm:"column:gift_price;PRIMARY" db:"gift_price" json:"gift_price" form:"gift_price"`                                         // 产品附赠礼物价值
	ValidTime            uint32    `gorm:"column:valid_time;PRIMARY" db:"valid_time" json:"valid_time" form:"valid_time"`                                         // 有效天数
	StartRemainEffective uint32    `gorm:"column:start_remain_effective" db:"start_remain_effective" json:"start_remain_effective" form:"start_remain_effective"` // 月初剩余有效价值（单位：秒）
	CumulativeSales      uint32    `gorm:"column:cumulative_sales" db:"cumulative_sales" json:"cumulative_sales" form:"cumulative_sales"`                         // 当月累计销售价值（单位：T豆）
	CumulativeGift       uint32    `gorm:"column:cumulative_gift" db:"cumulative_gift" json:"cumulative_gift" form:"cumulative_gift"`                             // 当月累计附赠包裹价值（单位：T豆）
	CumulativeUse        uint32    `gorm:"column:cumulative_use" db:"cumulative_use" json:"cumulative_use" form:"cumulative_use"`                                 // 当月累计使用价值（单位：秒）
	EndRemainEffective   uint32    `gorm:"column:end_remain_effective" db:"end_remain_effective" json:"end_remain_effective" form:"end_remain_effective"`         // 月末剩余有效价值（单位：秒）
	Check                uint32    `gorm:"column:table_check" db:"table_check" json:"table_check" form:"table_check"`                                             // 检查正常为1，失败为0。
	UpdateTime           time.Time `gorm:"column:update_time" db:"update_time" json:"update_time" form:"update_time"`                                             // 记录更新时间
	DelFlag              uint32    `gorm:"column:del_flag" db:"del_flag" json:"del_flag" form:"del_flag"`
}

func (y *YouKnowWhoReportTable) TableName() string {
	return "you_know_who_report"
}

// CreateUKWReportTable 神秘人财务统计建表语句
var CreateUKWReportTable = `
CREATE TABLE you_know_who_report (
	report_period datetime NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '账期',
	product_id VARCHAR ( 256 ) NOT NULL COMMENT '产品ID',
	name VARCHAR ( 256 ) NOT NULL COMMENT '产品名称',
	price INT ( 10 ) UNSIGNED NOT NULL COMMENT '订单价值',
	gift_price INT ( 10 ) UNSIGNED NOT NULL COMMENT '产品附赠礼物价值',
    valid_time INT ( 10 ) UNSIGNED NOT NULL COMMENT '有效时间',
	start_remain_effective INT ( 32 ) UNSIGNED NOT NULL COMMENT '月初剩余有效价值（单位：秒）',
    cumulative_sales INT ( 32 ) UNSIGNED NOT NULL COMMENT '当月累计销售价值（单位：T豆）',
    cumulative_gift INT ( 32 ) UNSIGNED NOT NULL COMMENT '当月累计销售价值（单位：T豆）',
    cumulative_use INT ( 32 ) UNSIGNED NOT NULL COMMENT '当月累计使用价值（单位：秒）',
	end_remain_effective INT ( 32 ) UNSIGNED NOT NULL COMMENT '月末剩余有效价值（单位：秒）',
	table_check TINYINT ( 2 ) NOT NULL COMMENT '检查正常为1，失败为0。检查方法详见代码统计结构体注释。',
	update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',
	del_flag TINYINT ( 2 ) NOT NULL COMMENT '删除状态：0：未删除；1:已删除',
	PRIMARY KEY ( report_period, price, gift_price, valid_time),
	KEY fast_info ( report_period, del_flag )
) ENGINE = INNODB DEFAULT CHARSET = utf8 COMMENT = '神秘人财务统计汇总表';
`

// UKWFakeUidMappingRelationsTable 神秘人假uid到真uid映射关系表
type UKWFakeUidMappingRelationsTable struct {
	Period     time.Time `gorm:"column:uid_period;PRIMARY" db:"uid_period" json:"uid_period" form:"uid_period"` // 时间段开始时间
	FakeUid    uint32    `gorm:"column:fake_uid;PRIMARY" db:"fake_uid" json:"fake_uid" form:"fake_uid"`         // 假uid
	Uid        uint32    `gorm:"column:uid" db:"uid" json:"uid" form:"uid"`                                     // 真uid
	Nickname   string    `gorm:"column:nickname" db:"nickname" json:"nickname" form:"nickname"`                 // 神秘人昵称
	UpdateTime time.Time `gorm:"column:update_time" db:"update_time" json:"update_time" form:"update_time"`     // 记录更新时间
	DelFlag    uint32    `gorm:"column:del_flag" db:"del_flag" json:"del_flag" form:"del_flag"`                 // 删除标记
}

func (y *UKWFakeUidMappingRelationsTable) TableName() string {
	return "you_know_who_fake_uid_mapping_relations"
}

var CreateUKWFakeUidMappingRelationsTbl = `
CREATE TABLE you_know_who_fake_uid_mapping_relations (
    uid_period datetime NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '时间段开始时间',
    fake_uid INT ( 10 ) UNSIGNED NOT NULL COMMENT '神秘人假UID',
    uid INT ( 10 ) UNSIGNED NOT NULL COMMENT '神秘人真UID',
    nickname VARCHAR ( 256 ) NOT NULL COMMENT '神秘人昵称',
    update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',
	del_flag TINYINT ( 2 ) NOT NULL COMMENT '删除状态：0：未删除；1:已删除',
	PRIMARY KEY ( uid_period, fake_uid )
) ENGINE = INNODB DEFAULT CHARSET = utf8 COMMENT = '神秘人假uid到真uid映射关系表';	
`
