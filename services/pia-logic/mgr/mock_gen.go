// Code generated by MockGen. DO NOT EDIT.
// Source: ./mgr_api.go

// Package mgr is a generated GoMock package.
package mgr

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	proto "gitlab.ttyuyin.com/avengers/tyr/compatible/proto"
	channel "golang.52tt.com/protocol/app/channel"
	pia "golang.52tt.com/protocol/app/pia"
	push "golang.52tt.com/protocol/app/push"
	channelsvr "golang.52tt.com/protocol/services/channelsvr"
	pia0 "golang.52tt.com/protocol/services/pia"
	context0 "golang.org/x/net/context"
)

// MockIOrderDrama is a mock of IOrderDrama interface.
type MockIOrderDrama struct {
	ctrl     *gomock.Controller
	recorder *MockIOrderDramaMockRecorder
}

// MockIOrderDramaMockRecorder is the mock recorder for MockIOrderDrama.
type MockIOrderDramaMockRecorder struct {
	mock *MockIOrderDrama
}

// NewMockIOrderDrama creates a new mock instance.
func NewMockIOrderDrama(ctrl *gomock.Controller) *MockIOrderDrama {
	mock := &MockIOrderDrama{ctrl: ctrl}
	mock.recorder = &MockIOrderDramaMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIOrderDrama) EXPECT() *MockIOrderDramaMockRecorder {
	return m.recorder
}

// DeleteOrderDrama mocks base method.
func (m *MockIOrderDrama) DeleteOrderDrama(c context.Context, req *pia.DeleteOrderDramaReq) (*pia.DeleteOrderDramaResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteOrderDrama", c, req)
	ret0, _ := ret[0].(*pia.DeleteOrderDramaResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteOrderDrama indicates an expected call of DeleteOrderDrama.
func (mr *MockIOrderDramaMockRecorder) DeleteOrderDrama(c, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteOrderDrama", reflect.TypeOf((*MockIOrderDrama)(nil).DeleteOrderDrama), c, req)
}

// DeleteOrderDramaForAllChannelSync mocks base method.
func (m *MockIOrderDrama) DeleteOrderDramaForAllChannelSync(c context.Context, dramaId uint32) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "DeleteOrderDramaForAllChannelSync", c, dramaId)
}

// DeleteOrderDramaForAllChannelSync indicates an expected call of DeleteOrderDramaForAllChannelSync.
func (mr *MockIOrderDramaMockRecorder) DeleteOrderDramaForAllChannelSync(c, dramaId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteOrderDramaForAllChannelSync", reflect.TypeOf((*MockIOrderDrama)(nil).DeleteOrderDramaForAllChannelSync), c, dramaId)
}

// GetOrderDramaList mocks base method.
func (m *MockIOrderDrama) GetOrderDramaList(c context.Context, req *pia.GetOrderDramaListReq) (*pia.GetOrderDramaListResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOrderDramaList", c, req)
	ret0, _ := ret[0].(*pia.GetOrderDramaListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOrderDramaList indicates an expected call of GetOrderDramaList.
func (mr *MockIOrderDramaMockRecorder) GetOrderDramaList(c, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOrderDramaList", reflect.TypeOf((*MockIOrderDrama)(nil).GetOrderDramaList), c, req)
}

// OrderDrama mocks base method.
func (m *MockIOrderDrama) OrderDrama(c context.Context, req *pia.OrderDramaReq) (*pia.OrderDramaResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "OrderDrama", c, req)
	ret0, _ := ret[0].(*pia.OrderDramaResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// OrderDrama indicates an expected call of OrderDrama.
func (mr *MockIOrderDramaMockRecorder) OrderDrama(c, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "OrderDrama", reflect.TypeOf((*MockIOrderDrama)(nil).OrderDrama), c, req)
}

// MockIConverter is a mock of IConverter interface.
type MockIConverter struct {
	ctrl     *gomock.Controller
	recorder *MockIConverterMockRecorder
}

// MockIConverterMockRecorder is the mock recorder for MockIConverter.
type MockIConverterMockRecorder struct {
	mock *MockIConverter
}

// NewMockIConverter creates a new mock instance.
func NewMockIConverter(ctrl *gomock.Controller) *MockIConverter {
	mock := &MockIConverter{ctrl: ctrl}
	mock.recorder = &MockIConverterMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIConverter) EXPECT() *MockIConverterMockRecorder {
	return m.recorder
}

// ChannelDramaInfoDetailPBToLogic mocks base method.
func (m *MockIConverter) ChannelDramaInfoDetailPBToLogic(info *pia0.ChannelDramaInfoDetail) *pia.ChannelDramaInfoDetail {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ChannelDramaInfoDetailPBToLogic", info)
	ret0, _ := ret[0].(*pia.ChannelDramaInfoDetail)
	return ret0
}

// ChannelDramaInfoDetailPBToLogic indicates an expected call of ChannelDramaInfoDetailPBToLogic.
func (mr *MockIConverterMockRecorder) ChannelDramaInfoDetailPBToLogic(info interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ChannelDramaInfoDetailPBToLogic", reflect.TypeOf((*MockIConverter)(nil).ChannelDramaInfoDetailPBToLogic), info)
}

// ChannelDramaPlayingTypeLogicToPB mocks base method.
func (m *MockIConverter) ChannelDramaPlayingTypeLogicToPB(info *pia.PiaChannelDramaPlayingType) *pia0.PiaChannelDramaPlayingType {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ChannelDramaPlayingTypeLogicToPB", info)
	ret0, _ := ret[0].(*pia0.PiaChannelDramaPlayingType)
	return ret0
}

// ChannelDramaPlayingTypeLogicToPB indicates an expected call of ChannelDramaPlayingTypeLogicToPB.
func (mr *MockIConverterMockRecorder) ChannelDramaPlayingTypeLogicToPB(info interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ChannelDramaPlayingTypeLogicToPB", reflect.TypeOf((*MockIConverter)(nil).ChannelDramaPlayingTypeLogicToPB), info)
}

// ChannelDramaPlayingTypePBToLogic mocks base method.
func (m *MockIConverter) ChannelDramaPlayingTypePBToLogic(info *pia0.PiaChannelDramaPlayingType) *pia.PiaChannelDramaPlayingType {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ChannelDramaPlayingTypePBToLogic", info)
	ret0, _ := ret[0].(*pia.PiaChannelDramaPlayingType)
	return ret0
}

// ChannelDramaPlayingTypePBToLogic indicates an expected call of ChannelDramaPlayingTypePBToLogic.
func (mr *MockIConverterMockRecorder) ChannelDramaPlayingTypePBToLogic(info interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ChannelDramaPlayingTypePBToLogic", reflect.TypeOf((*MockIConverter)(nil).ChannelDramaPlayingTypePBToLogic), info)
}

// DramaBgmStatusPBToLogic mocks base method.
func (m *MockIConverter) DramaBgmStatusPBToLogic(info *pia0.DramaBgmStatus) *pia.DramaBgmStatus {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DramaBgmStatusPBToLogic", info)
	ret0, _ := ret[0].(*pia.DramaBgmStatus)
	return ret0
}

// DramaBgmStatusPBToLogic indicates an expected call of DramaBgmStatusPBToLogic.
func (mr *MockIConverterMockRecorder) DramaBgmStatusPBToLogic(info interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DramaBgmStatusPBToLogic", reflect.TypeOf((*MockIConverter)(nil).DramaBgmStatusPBToLogic), info)
}

// DramaBgmVolStatusPBToLogic mocks base method.
func (m *MockIConverter) DramaBgmVolStatusPBToLogic(info *pia0.DramaBgmVolStatus) *pia.DramaBgmVolStatus {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DramaBgmVolStatusPBToLogic", info)
	ret0, _ := ret[0].(*pia.DramaBgmVolStatus)
	return ret0
}

// DramaBgmVolStatusPBToLogic indicates an expected call of DramaBgmVolStatusPBToLogic.
func (mr *MockIConverterMockRecorder) DramaBgmVolStatusPBToLogic(info interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DramaBgmVolStatusPBToLogic", reflect.TypeOf((*MockIConverter)(nil).DramaBgmVolStatusPBToLogic), info)
}

// DramaInfoToPBBgmList mocks base method.
func (m *MockIConverter) DramaInfoToPBBgmList(info []*pia0.PiaBGM) []*pia.PiaBGM {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DramaInfoToPBBgmList", info)
	ret0, _ := ret[0].([]*pia.PiaBGM)
	return ret0
}

// DramaInfoToPBBgmList indicates an expected call of DramaInfoToPBBgmList.
func (mr *MockIConverterMockRecorder) DramaInfoToPBBgmList(info interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DramaInfoToPBBgmList", reflect.TypeOf((*MockIConverter)(nil).DramaInfoToPBBgmList), info)
}

// DramaInfoToPBContentList mocks base method.
func (m *MockIConverter) DramaInfoToPBContentList(info []*pia0.PiaContent) []*pia.PiaContent {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DramaInfoToPBContentList", info)
	ret0, _ := ret[0].([]*pia.PiaContent)
	return ret0
}

// DramaInfoToPBContentList indicates an expected call of DramaInfoToPBContentList.
func (mr *MockIConverterMockRecorder) DramaInfoToPBContentList(info interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DramaInfoToPBContentList", reflect.TypeOf((*MockIConverter)(nil).DramaInfoToPBContentList), info)
}

// DramaInfoToPBPictureList mocks base method.
func (m *MockIConverter) DramaInfoToPBPictureList(info []*pia0.PiaPicture) []*pia.PiaPicture {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DramaInfoToPBPictureList", info)
	ret0, _ := ret[0].([]*pia.PiaPicture)
	return ret0
}

// DramaInfoToPBPictureList indicates an expected call of DramaInfoToPBPictureList.
func (mr *MockIConverterMockRecorder) DramaInfoToPBPictureList(info interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DramaInfoToPBPictureList", reflect.TypeOf((*MockIConverter)(nil).DramaInfoToPBPictureList), info)
}

// DramaInfoToPBRoleList mocks base method.
func (m *MockIConverter) DramaInfoToPBRoleList(info []*pia0.PiaRole) []*pia.PiaRole {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DramaInfoToPBRoleList", info)
	ret0, _ := ret[0].([]*pia.PiaRole)
	return ret0
}

// DramaInfoToPBRoleList indicates an expected call of DramaInfoToPBRoleList.
func (mr *MockIConverterMockRecorder) DramaInfoToPBRoleList(info interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DramaInfoToPBRoleList", reflect.TypeOf((*MockIConverter)(nil).DramaInfoToPBRoleList), info)
}

// DramaOrderListItemSvrToLogic mocks base method.
func (m *MockIConverter) DramaOrderListItemSvrToLogic(src *pia0.DramaOrderList_UserOrderInfo) *pia.DramaOrderList_UserOrderInfo {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DramaOrderListItemSvrToLogic", src)
	ret0, _ := ret[0].(*pia.DramaOrderList_UserOrderInfo)
	return ret0
}

// DramaOrderListItemSvrToLogic indicates an expected call of DramaOrderListItemSvrToLogic.
func (mr *MockIConverterMockRecorder) DramaOrderListItemSvrToLogic(src interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DramaOrderListItemSvrToLogic", reflect.TypeOf((*MockIConverter)(nil).DramaOrderListItemSvrToLogic), src)
}

// DramaOrderListSvrToLogic mocks base method.
func (m *MockIConverter) DramaOrderListSvrToLogic(src *pia0.DramaOrderList) *pia.DramaOrderList {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DramaOrderListSvrToLogic", src)
	ret0, _ := ret[0].(*pia.DramaOrderList)
	return ret0
}

// DramaOrderListSvrToLogic indicates an expected call of DramaOrderListSvrToLogic.
func (mr *MockIConverterMockRecorder) DramaOrderListSvrToLogic(src interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DramaOrderListSvrToLogic", reflect.TypeOf((*MockIConverter)(nil).DramaOrderListSvrToLogic), src)
}

// DramaStatusPBToLogic mocks base method.
func (m *MockIConverter) DramaStatusPBToLogic(resp *pia0.ChannelDramaStatus) *pia.ChannelDramaStatus {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DramaStatusPBToLogic", resp)
	ret0, _ := ret[0].(*pia.ChannelDramaStatus)
	return ret0
}

// DramaStatusPBToLogic indicates an expected call of DramaStatusPBToLogic.
func (mr *MockIConverterMockRecorder) DramaStatusPBToLogic(resp interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DramaStatusPBToLogic", reflect.TypeOf((*MockIConverter)(nil).DramaStatusPBToLogic), resp)
}

// DramaSubInfoPBToLogic mocks base method.
func (m *MockIConverter) DramaSubInfoPBToLogic(info *pia0.DramaSubInfo) *pia.DramaSubInfo {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DramaSubInfoPBToLogic", info)
	ret0, _ := ret[0].(*pia.DramaSubInfo)
	return ret0
}

// DramaSubInfoPBToLogic indicates an expected call of DramaSubInfoPBToLogic.
func (mr *MockIConverterMockRecorder) DramaSubInfoPBToLogic(info interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DramaSubInfoPBToLogic", reflect.TypeOf((*MockIConverter)(nil).DramaSubInfoPBToLogic), info)
}

// DramaV2PBToLogic mocks base method.
func (m *MockIConverter) DramaV2PBToLogic(info *pia0.DramaV2) *pia.DramaV2 {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DramaV2PBToLogic", info)
	ret0, _ := ret[0].(*pia.DramaV2)
	return ret0
}

// DramaV2PBToLogic indicates an expected call of DramaV2PBToLogic.
func (mr *MockIConverterMockRecorder) DramaV2PBToLogic(info interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DramaV2PBToLogic", reflect.TypeOf((*MockIConverter)(nil).DramaV2PBToLogic), info)
}

// GetDramaCollListRespSvr2Logic mocks base method.
func (m *MockIConverter) GetDramaCollListRespSvr2Logic(resp *pia0.GetDramaListByIdsResp) *pia.GetUserDramaCollectionResp {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetDramaCollListRespSvr2Logic", resp)
	ret0, _ := ret[0].(*pia.GetUserDramaCollectionResp)
	return ret0
}

// GetDramaCollListRespSvr2Logic indicates an expected call of GetDramaCollListRespSvr2Logic.
func (mr *MockIConverterMockRecorder) GetDramaCollListRespSvr2Logic(resp interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDramaCollListRespSvr2Logic", reflect.TypeOf((*MockIConverter)(nil).GetDramaCollListRespSvr2Logic), resp)
}

// GetDramaDetailByIdRespSvr2Logic mocks base method.
func (m *MockIConverter) GetDramaDetailByIdRespSvr2Logic(resp *pia0.GetDramaDetailByIdV2Resp) *pia.GetDramaDetailByIdResp {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetDramaDetailByIdRespSvr2Logic", resp)
	ret0, _ := ret[0].(*pia.GetDramaDetailByIdResp)
	return ret0
}

// GetDramaDetailByIdRespSvr2Logic indicates an expected call of GetDramaDetailByIdRespSvr2Logic.
func (mr *MockIConverterMockRecorder) GetDramaDetailByIdRespSvr2Logic(resp interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDramaDetailByIdRespSvr2Logic", reflect.TypeOf((*MockIConverter)(nil).GetDramaDetailByIdRespSvr2Logic), resp)
}

// GetDramaListRespSvr2Logic mocks base method.
func (m *MockIConverter) GetDramaListRespSvr2Logic(resp *pia0.GetDramaListResp) *pia.GetDramaListResp {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetDramaListRespSvr2Logic", resp)
	ret0, _ := ret[0].(*pia.GetDramaListResp)
	return ret0
}

// GetDramaListRespSvr2Logic indicates an expected call of GetDramaListRespSvr2Logic.
func (mr *MockIConverterMockRecorder) GetDramaListRespSvr2Logic(resp interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDramaListRespSvr2Logic", reflect.TypeOf((*MockIConverter)(nil).GetDramaListRespSvr2Logic), resp)
}

// GetRankingListRespSvr2Logic mocks base method.
func (m *MockIConverter) GetRankingListRespSvr2Logic(resp *pia0.PiaGetRankingListResp) *pia.PiaGetRankingListResp {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRankingListRespSvr2Logic", resp)
	ret0, _ := ret[0].(*pia.PiaGetRankingListResp)
	return ret0
}

// GetRankingListRespSvr2Logic indicates an expected call of GetRankingListRespSvr2Logic.
func (mr *MockIConverterMockRecorder) GetRankingListRespSvr2Logic(resp interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRankingListRespSvr2Logic", reflect.TypeOf((*MockIConverter)(nil).GetRankingListRespSvr2Logic), resp)
}

// GetSearchOptionGroupV2RespSvr2Logic mocks base method.
func (m *MockIConverter) GetSearchOptionGroupV2RespSvr2Logic(resp *pia0.GetSearchOptionGroupV2Resp) *pia.GetSearchOptionGroupV2Resp {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSearchOptionGroupV2RespSvr2Logic", resp)
	ret0, _ := ret[0].(*pia.GetSearchOptionGroupV2Resp)
	return ret0
}

// GetSearchOptionGroupV2RespSvr2Logic indicates an expected call of GetSearchOptionGroupV2RespSvr2Logic.
func (mr *MockIConverterMockRecorder) GetSearchOptionGroupV2RespSvr2Logic(resp interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSearchOptionGroupV2RespSvr2Logic", reflect.TypeOf((*MockIConverter)(nil).GetSearchOptionGroupV2RespSvr2Logic), resp)
}

// MicRoleBindingSvrToLogic mocks base method.
func (m *MockIConverter) MicRoleBindingSvrToLogic(resp *pia0.MicRoleMap) *pia.MicRoleMap {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "MicRoleBindingSvrToLogic", resp)
	ret0, _ := ret[0].(*pia.MicRoleMap)
	return ret0
}

// MicRoleBindingSvrToLogic indicates an expected call of MicRoleBindingSvrToLogic.
func (mr *MockIConverterMockRecorder) MicRoleBindingSvrToLogic(resp interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MicRoleBindingSvrToLogic", reflect.TypeOf((*MockIConverter)(nil).MicRoleBindingSvrToLogic), resp)
}

// PiaCopyDramaInfoWithStatusList2CopyDramaItemList mocks base method.
func (m *MockIConverter) PiaCopyDramaInfoWithStatusList2CopyDramaItemList(info []*pia0.PiaCopyDramaInfoWithStatus) []*pia.PiaCopyDramaItem {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PiaCopyDramaInfoWithStatusList2CopyDramaItemList", info)
	ret0, _ := ret[0].([]*pia.PiaCopyDramaItem)
	return ret0
}

// PiaCopyDramaInfoWithStatusList2CopyDramaItemList indicates an expected call of PiaCopyDramaInfoWithStatusList2CopyDramaItemList.
func (mr *MockIConverterMockRecorder) PiaCopyDramaInfoWithStatusList2CopyDramaItemList(info interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PiaCopyDramaInfoWithStatusList2CopyDramaItemList", reflect.TypeOf((*MockIConverter)(nil).PiaCopyDramaInfoWithStatusList2CopyDramaItemList), info)
}

// PiaCopyDramaInfoWithStatusListPBToLogic mocks base method.
func (m *MockIConverter) PiaCopyDramaInfoWithStatusListPBToLogic(info []*pia0.PiaCopyDramaInfoWithStatus) []*pia.PiaCopyDramaInfoWithStatus {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PiaCopyDramaInfoWithStatusListPBToLogic", info)
	ret0, _ := ret[0].([]*pia.PiaCopyDramaInfoWithStatus)
	return ret0
}

// PiaCopyDramaInfoWithStatusListPBToLogic indicates an expected call of PiaCopyDramaInfoWithStatusListPBToLogic.
func (mr *MockIConverterMockRecorder) PiaCopyDramaInfoWithStatusListPBToLogic(info interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PiaCopyDramaInfoWithStatusListPBToLogic", reflect.TypeOf((*MockIConverter)(nil).PiaCopyDramaInfoWithStatusListPBToLogic), info)
}

// PiaCopyDramaInfoWithStatusPBToLogic mocks base method.
func (m *MockIConverter) PiaCopyDramaInfoWithStatusPBToLogic(info *pia0.PiaCopyDramaInfoWithStatus) *pia.PiaCopyDramaInfoWithStatus {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PiaCopyDramaInfoWithStatusPBToLogic", info)
	ret0, _ := ret[0].(*pia.PiaCopyDramaInfoWithStatus)
	return ret0
}

// PiaCopyDramaInfoWithStatusPBToLogic indicates an expected call of PiaCopyDramaInfoWithStatusPBToLogic.
func (mr *MockIConverterMockRecorder) PiaCopyDramaInfoWithStatusPBToLogic(info interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PiaCopyDramaInfoWithStatusPBToLogic", reflect.TypeOf((*MockIConverter)(nil).PiaCopyDramaInfoWithStatusPBToLogic), info)
}

// PiaDramaPlayingRecordPBToLogic mocks base method.
func (m *MockIConverter) PiaDramaPlayingRecordPBToLogic(info *pia0.PiaDramaPlayingRecord) *pia.PiaDramaPlayingRecord {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PiaDramaPlayingRecordPBToLogic", info)
	ret0, _ := ret[0].(*pia.PiaDramaPlayingRecord)
	return ret0
}

// PiaDramaPlayingRecordPBToLogic indicates an expected call of PiaDramaPlayingRecordPBToLogic.
func (mr *MockIConverterMockRecorder) PiaDramaPlayingRecordPBToLogic(info interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PiaDramaPlayingRecordPBToLogic", reflect.TypeOf((*MockIConverter)(nil).PiaDramaPlayingRecordPBToLogic), info)
}

// SearchOptionsLogic2Svr mocks base method.
func (m *MockIConverter) SearchOptionsLogic2Svr(searchOptions []*pia.SearchOption) []*pia0.SearchOption {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SearchOptionsLogic2Svr", searchOptions)
	ret0, _ := ret[0].([]*pia0.SearchOption)
	return ret0
}

// SearchOptionsLogic2Svr indicates an expected call of SearchOptionsLogic2Svr.
func (mr *MockIConverterMockRecorder) SearchOptionsLogic2Svr(searchOptions interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SearchOptionsLogic2Svr", reflect.TypeOf((*MockIConverter)(nil).SearchOptionsLogic2Svr), searchOptions)
}

// MockIMainGame is a mock of IMainGame interface.
type MockIMainGame struct {
	ctrl     *gomock.Controller
	recorder *MockIMainGameMockRecorder
}

// MockIMainGameMockRecorder is the mock recorder for MockIMainGame.
type MockIMainGameMockRecorder struct {
	mock *MockIMainGame
}

// NewMockIMainGame creates a new mock instance.
func NewMockIMainGame(ctrl *gomock.Controller) *MockIMainGame {
	mock := &MockIMainGame{ctrl: ctrl}
	mock.recorder = &MockIMainGameMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIMainGame) EXPECT() *MockIMainGameMockRecorder {
	return m.recorder
}

// CancelSelectRole mocks base method.
func (m *MockIMainGame) CancelSelectRole(c context.Context, req *pia.PiaCancelSelectRoleReq) (*pia.PiaCancelSelectRoleResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CancelSelectRole", c, req)
	ret0, _ := ret[0].(*pia.PiaCancelSelectRoleResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CancelSelectRole indicates an expected call of CancelSelectRole.
func (mr *MockIMainGameMockRecorder) CancelSelectRole(c, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CancelSelectRole", reflect.TypeOf((*MockIMainGame)(nil).CancelSelectRole), c, req)
}

// ChangePlayingType mocks base method.
func (m *MockIMainGame) ChangePlayingType(c context.Context, req *pia.PiaChangePlayTypeReq) (*pia.PiaChangePlayTypeResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ChangePlayingType", c, req)
	ret0, _ := ret[0].(*pia.PiaChangePlayTypeResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ChangePlayingType indicates an expected call of ChangePlayingType.
func (mr *MockIMainGameMockRecorder) ChangePlayingType(c, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ChangePlayingType", reflect.TypeOf((*MockIMainGame)(nil).ChangePlayingType), c, req)
}

// FollowMic mocks base method.
func (m *MockIMainGame) FollowMic(c context.Context, request *pia.PiaFollowMicRequest) (*pia.PiaFollowMicResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FollowMic", c, request)
	ret0, _ := ret[0].(*pia.PiaFollowMicResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FollowMic indicates an expected call of FollowMic.
func (mr *MockIMainGameMockRecorder) FollowMic(c, request interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FollowMic", reflect.TypeOf((*MockIMainGame)(nil).FollowMic), c, request)
}

// GetDramaStatus mocks base method.
func (m *MockIMainGame) GetDramaStatus(c context.Context, req *pia.PiaGetDramaStatusReq) (*pia.PiaGetDramaStatusResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetDramaStatus", c, req)
	ret0, _ := ret[0].(*pia.PiaGetDramaStatusResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetDramaStatus indicates an expected call of GetDramaStatus.
func (mr *MockIMainGameMockRecorder) GetDramaStatus(c, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDramaStatus", reflect.TypeOf((*MockIMainGame)(nil).GetDramaStatus), c, req)
}

// GetMyFollowInfo mocks base method.
func (m *MockIMainGame) GetMyFollowInfo(c context.Context, req *pia.PiaGetMyFollowInfoRequest) (*pia.PiaGetMyFollowInfoResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMyFollowInfo", c, req)
	ret0, _ := ret[0].(*pia.PiaGetMyFollowInfoResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMyFollowInfo indicates an expected call of GetMyFollowInfo.
func (mr *MockIMainGameMockRecorder) GetMyFollowInfo(c, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMyFollowInfo", reflect.TypeOf((*MockIMainGame)(nil).GetMyFollowInfo), c, req)
}

// GetPreviousDialogueIndex mocks base method.
func (m *MockIMainGame) GetPreviousDialogueIndex(c context.Context, request *pia.PiaGetPreviousDialogueIndexRequest) (*pia.PiaGetPreviousDialogueIndexResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPreviousDialogueIndex", c, request)
	ret0, _ := ret[0].(*pia.PiaGetPreviousDialogueIndexResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPreviousDialogueIndex indicates an expected call of GetPreviousDialogueIndex.
func (mr *MockIMainGameMockRecorder) GetPreviousDialogueIndex(c, request interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPreviousDialogueIndex", reflect.TypeOf((*MockIMainGame)(nil).GetPreviousDialogueIndex), c, request)
}

// OperateBgm mocks base method.
func (m *MockIMainGame) OperateBgm(c context.Context, req *pia.PiaOperateBgmReq) (*pia.PiaOperateBgmResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "OperateBgm", c, req)
	ret0, _ := ret[0].(*pia.PiaOperateBgmResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// OperateBgm indicates an expected call of OperateBgm.
func (mr *MockIMainGameMockRecorder) OperateBgm(c, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "OperateBgm", reflect.TypeOf((*MockIMainGame)(nil).OperateBgm), c, req)
}

// OperateBgmVol mocks base method.
func (m *MockIMainGame) OperateBgmVol(c context.Context, req *pia.PiaOperateBgmVolReq) (*pia.PiaOperateBgmVolResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "OperateBgmVol", c, req)
	ret0, _ := ret[0].(*pia.PiaOperateBgmVolResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// OperateBgmVol indicates an expected call of OperateBgmVol.
func (mr *MockIMainGameMockRecorder) OperateBgmVol(c, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "OperateBgmVol", reflect.TypeOf((*MockIMainGame)(nil).OperateBgmVol), c, req)
}

// OperateDrama mocks base method.
func (m *MockIMainGame) OperateDrama(c context.Context, req *pia.PiaOperateDramaReq) (*pia.PiaOperateDramaResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "OperateDrama", c, req)
	ret0, _ := ret[0].(*pia.PiaOperateDramaResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// OperateDrama indicates an expected call of OperateDrama.
func (mr *MockIMainGameMockRecorder) OperateDrama(c, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "OperateDrama", reflect.TypeOf((*MockIMainGame)(nil).OperateDrama), c, req)
}

// PerformDrama mocks base method.
func (m *MockIMainGame) PerformDrama(c context.Context, request *pia.PiaPerformDramaRequest) (*pia.PiaPerformDramaResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PerformDrama", c, request)
	ret0, _ := ret[0].(*pia.PiaPerformDramaResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PerformDrama indicates an expected call of PerformDrama.
func (mr *MockIMainGameMockRecorder) PerformDrama(c, request interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PerformDrama", reflect.TypeOf((*MockIMainGame)(nil).PerformDrama), c, request)
}

// PiaCreateDramaCopy mocks base method.
func (m *MockIMainGame) PiaCreateDramaCopy(c context.Context, channelId, uid uint32, roundId int64) (uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PiaCreateDramaCopy", c, channelId, uid, roundId)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PiaCreateDramaCopy indicates an expected call of PiaCreateDramaCopy.
func (mr *MockIMainGameMockRecorder) PiaCreateDramaCopy(c, channelId, uid, roundId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PiaCreateDramaCopy", reflect.TypeOf((*MockIMainGame)(nil).PiaCreateDramaCopy), c, channelId, uid, roundId)
}

// PiaGetFollowedStatusOfMicList mocks base method.
func (m *MockIMainGame) PiaGetFollowedStatusOfMicList(c context.Context, req *pia.PiaGetFollowedStatusOfMicListRequest) (*pia.PiaGetFollowedStatusOfMicListResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PiaGetFollowedStatusOfMicList", c, req)
	ret0, _ := ret[0].(*pia.PiaGetFollowedStatusOfMicListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PiaGetFollowedStatusOfMicList indicates an expected call of PiaGetFollowedStatusOfMicList.
func (mr *MockIMainGameMockRecorder) PiaGetFollowedStatusOfMicList(c, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PiaGetFollowedStatusOfMicList", reflect.TypeOf((*MockIMainGame)(nil).PiaGetFollowedStatusOfMicList), c, req)
}

// ReportDialogueIndex mocks base method.
func (m *MockIMainGame) ReportDialogueIndex(c context.Context, request *pia.PiaReportDialogueIndexRequest) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ReportDialogueIndex", c, request)
	ret0, _ := ret[0].(error)
	return ret0
}

// ReportDialogueIndex indicates an expected call of ReportDialogueIndex.
func (mr *MockIMainGameMockRecorder) ReportDialogueIndex(c, request interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReportDialogueIndex", reflect.TypeOf((*MockIMainGame)(nil).ReportDialogueIndex), c, request)
}

// SelectDramaV2 mocks base method.
func (m *MockIMainGame) SelectDramaV2(c context.Context, req *pia.SelectDramaV2Req) (*pia.SelectDramaV2Resp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SelectDramaV2", c, req)
	ret0, _ := ret[0].(*pia.SelectDramaV2Resp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SelectDramaV2 indicates an expected call of SelectDramaV2.
func (mr *MockIMainGameMockRecorder) SelectDramaV2(c, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SelectDramaV2", reflect.TypeOf((*MockIMainGame)(nil).SelectDramaV2), c, req)
}

// SelectRole mocks base method.
func (m *MockIMainGame) SelectRole(c context.Context, req *pia.PiaSelectRoleReq) (*pia.PiaSelectRoleResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SelectRole", c, req)
	ret0, _ := ret[0].(*pia.PiaSelectRoleResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SelectRole indicates an expected call of SelectRole.
func (mr *MockIMainGameMockRecorder) SelectRole(c, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SelectRole", reflect.TypeOf((*MockIMainGame)(nil).SelectRole), c, req)
}

// SendDialogueIndex mocks base method.
func (m *MockIMainGame) SendDialogueIndex(ctx context.Context, request *pia.PiaSendDialogueIndexRequest) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendDialogueIndex", ctx, request)
	ret0, _ := ret[0].(error)
	return ret0
}

// SendDialogueIndex indicates an expected call of SendDialogueIndex.
func (mr *MockIMainGameMockRecorder) SendDialogueIndex(ctx, request interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendDialogueIndex", reflect.TypeOf((*MockIMainGame)(nil).SendDialogueIndex), ctx, request)
}

// UnFollowMic mocks base method.
func (m *MockIMainGame) UnFollowMic(c context.Context, request *pia.PiaUnFollowMicRequest) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UnFollowMic", c, request)
	ret0, _ := ret[0].(error)
	return ret0
}

// UnFollowMic indicates an expected call of UnFollowMic.
func (mr *MockIMainGameMockRecorder) UnFollowMic(c, request interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UnFollowMic", reflect.TypeOf((*MockIMainGame)(nil).UnFollowMic), c, request)
}

// MockIDramaInfo is a mock of IDramaInfo interface.
type MockIDramaInfo struct {
	ctrl     *gomock.Controller
	recorder *MockIDramaInfoMockRecorder
}

// MockIDramaInfoMockRecorder is the mock recorder for MockIDramaInfo.
type MockIDramaInfoMockRecorder struct {
	mock *MockIDramaInfo
}

// NewMockIDramaInfo creates a new mock instance.
func NewMockIDramaInfo(ctrl *gomock.Controller) *MockIDramaInfo {
	mock := &MockIDramaInfo{ctrl: ctrl}
	mock.recorder = &MockIDramaInfoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIDramaInfo) EXPECT() *MockIDramaInfoMockRecorder {
	return m.recorder
}

// GetDramaDetailById mocks base method.
func (m *MockIDramaInfo) GetDramaDetailById(c context0.Context, req *pia.GetDramaDetailByIdReq) (*pia.GetDramaDetailByIdResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetDramaDetailById", c, req)
	ret0, _ := ret[0].(*pia.GetDramaDetailByIdResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetDramaDetailById indicates an expected call of GetDramaDetailById.
func (mr *MockIDramaInfoMockRecorder) GetDramaDetailById(c, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDramaDetailById", reflect.TypeOf((*MockIDramaInfo)(nil).GetDramaDetailById), c, req)
}

// GetDramaDetailByIdV2 mocks base method.
func (m *MockIDramaInfo) GetDramaDetailByIdV2(ctx context.Context, dramaId uint32) (*pia0.DramaV2, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetDramaDetailByIdV2", ctx, dramaId)
	ret0, _ := ret[0].(*pia0.DramaV2)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetDramaDetailByIdV2 indicates an expected call of GetDramaDetailByIdV2.
func (mr *MockIDramaInfoMockRecorder) GetDramaDetailByIdV2(ctx, dramaId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDramaDetailByIdV2", reflect.TypeOf((*MockIDramaInfo)(nil).GetDramaDetailByIdV2), ctx, dramaId)
}

// GetDramaList mocks base method.
func (m *MockIDramaInfo) GetDramaList(c context0.Context, req *pia.GetDramaListReq) (*pia.GetDramaListResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetDramaList", c, req)
	ret0, _ := ret[0].(*pia.GetDramaListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetDramaList indicates an expected call of GetDramaList.
func (mr *MockIDramaInfoMockRecorder) GetDramaList(c, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDramaList", reflect.TypeOf((*MockIDramaInfo)(nil).GetDramaList), c, req)
}

// GetSearchOptionGroupV2 mocks base method.
func (m *MockIDramaInfo) GetSearchOptionGroupV2(c context0.Context, req *pia.GetSearchOptionGroupV2Req) (*pia.GetSearchOptionGroupV2Resp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSearchOptionGroupV2", c, req)
	ret0, _ := ret[0].(*pia.GetSearchOptionGroupV2Resp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSearchOptionGroupV2 indicates an expected call of GetSearchOptionGroupV2.
func (mr *MockIDramaInfoMockRecorder) GetSearchOptionGroupV2(c, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSearchOptionGroupV2", reflect.TypeOf((*MockIDramaInfo)(nil).GetSearchOptionGroupV2), c, req)
}

// PiaGetDramaCopyId mocks base method.
func (m *MockIDramaInfo) PiaGetDramaCopyId(c context0.Context, originDramaId, uid uint32) (uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PiaGetDramaCopyId", c, originDramaId, uid)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PiaGetDramaCopyId indicates an expected call of PiaGetDramaCopyId.
func (mr *MockIDramaInfoMockRecorder) PiaGetDramaCopyId(c, originDramaId, uid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PiaGetDramaCopyId", reflect.TypeOf((*MockIDramaInfo)(nil).PiaGetDramaCopyId), c, originDramaId, uid)
}

// PiaGetRankingList mocks base method.
func (m *MockIDramaInfo) PiaGetRankingList(c context.Context, req *pia.PiaGetRankingListReq) (*pia.PiaGetRankingListResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PiaGetRankingList", c, req)
	ret0, _ := ret[0].(*pia.PiaGetRankingListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PiaGetRankingList indicates an expected call of PiaGetRankingList.
func (mr *MockIDramaInfoMockRecorder) PiaGetRankingList(c, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PiaGetRankingList", reflect.TypeOf((*MockIDramaInfo)(nil).PiaGetRankingList), c, req)
}

// MockIDramaCollect is a mock of IDramaCollect interface.
type MockIDramaCollect struct {
	ctrl     *gomock.Controller
	recorder *MockIDramaCollectMockRecorder
}

// MockIDramaCollectMockRecorder is the mock recorder for MockIDramaCollect.
type MockIDramaCollectMockRecorder struct {
	mock *MockIDramaCollect
}

// NewMockIDramaCollect creates a new mock instance.
func NewMockIDramaCollect(ctrl *gomock.Controller) *MockIDramaCollect {
	mock := &MockIDramaCollect{ctrl: ctrl}
	mock.recorder = &MockIDramaCollectMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIDramaCollect) EXPECT() *MockIDramaCollectMockRecorder {
	return m.recorder
}

// DoDramaCollect mocks base method.
func (m *MockIDramaCollect) DoDramaCollect(ctx context.Context, req *pia.DoUserDramaCollectReq) (*pia.DoUserDramaCollectResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DoDramaCollect", ctx, req)
	ret0, _ := ret[0].(*pia.DoUserDramaCollectResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DoDramaCollect indicates an expected call of DoDramaCollect.
func (mr *MockIDramaCollectMockRecorder) DoDramaCollect(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DoDramaCollect", reflect.TypeOf((*MockIDramaCollect)(nil).DoDramaCollect), ctx, req)
}

// GetDramaCollectList mocks base method.
func (m *MockIDramaCollect) GetDramaCollectList(ctx context.Context, req *pia.GetUserDramaCollectionReq) (*pia.GetUserDramaCollectionResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetDramaCollectList", ctx, req)
	ret0, _ := ret[0].(*pia.GetUserDramaCollectionResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetDramaCollectList indicates an expected call of GetDramaCollectList.
func (mr *MockIDramaCollectMockRecorder) GetDramaCollectList(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDramaCollectList", reflect.TypeOf((*MockIDramaCollect)(nil).GetDramaCollectList), ctx, req)
}

// GetDramaCollectStatus mocks base method.
func (m *MockIDramaCollect) GetDramaCollectStatus(ctx context.Context, uid, dramaId uint32) (bool, uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetDramaCollectStatus", ctx, uid, dramaId)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(uint32)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetDramaCollectStatus indicates an expected call of GetDramaCollectStatus.
func (mr *MockIDramaCollectMockRecorder) GetDramaCollectStatus(ctx, uid, dramaId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDramaCollectStatus", reflect.TypeOf((*MockIDramaCollect)(nil).GetDramaCollectStatus), ctx, uid, dramaId)
}

// MockIGetDramaToken is a mock of IGetDramaToken interface.
type MockIGetDramaToken struct {
	ctrl     *gomock.Controller
	recorder *MockIGetDramaTokenMockRecorder
}

// MockIGetDramaTokenMockRecorder is the mock recorder for MockIGetDramaToken.
type MockIGetDramaTokenMockRecorder struct {
	mock *MockIGetDramaToken
}

// NewMockIGetDramaToken creates a new mock instance.
func NewMockIGetDramaToken(ctrl *gomock.Controller) *MockIGetDramaToken {
	mock := &MockIGetDramaToken{ctrl: ctrl}
	mock.recorder = &MockIGetDramaTokenMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIGetDramaToken) EXPECT() *MockIGetDramaTokenMockRecorder {
	return m.recorder
}

// SetSearchOpt mocks base method.
func (m *MockIGetDramaToken) SetSearchOpt(searchOptList []*pia.SearchOption) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "SetSearchOpt", searchOptList)
}

// SetSearchOpt indicates an expected call of SetSearchOpt.
func (mr *MockIGetDramaTokenMockRecorder) SetSearchOpt(searchOptList interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetSearchOpt", reflect.TypeOf((*MockIGetDramaToken)(nil).SetSearchOpt), searchOptList)
}

// String mocks base method.
func (m *MockIGetDramaToken) String(ctx context0.Context) string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "String", ctx)
	ret0, _ := ret[0].(string)
	return ret0
}

// String indicates an expected call of String.
func (mr *MockIGetDramaTokenMockRecorder) String(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "String", reflect.TypeOf((*MockIGetDramaToken)(nil).String), ctx)
}

// MockIAggregatePage is a mock of IAggregatePage interface.
type MockIAggregatePage struct {
	ctrl     *gomock.Controller
	recorder *MockIAggregatePageMockRecorder
}

// MockIAggregatePageMockRecorder is the mock recorder for MockIAggregatePage.
type MockIAggregatePageMockRecorder struct {
	mock *MockIAggregatePage
}

// NewMockIAggregatePage creates a new mock instance.
func NewMockIAggregatePage(ctrl *gomock.Controller) *MockIAggregatePage {
	mock := &MockIAggregatePage{ctrl: ctrl}
	mock.recorder = &MockIAggregatePageMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIAggregatePage) EXPECT() *MockIAggregatePageMockRecorder {
	return m.recorder
}

// GetPracticeDramaList mocks base method.
func (m *MockIAggregatePage) GetPracticeDramaList(c context0.Context, req *pia.GetPracticeDramaListReq) (*pia.GetPracticeDramaListResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPracticeDramaList", c, req)
	ret0, _ := ret[0].(*pia.GetPracticeDramaListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPracticeDramaList indicates an expected call of GetPracticeDramaList.
func (mr *MockIAggregatePageMockRecorder) GetPracticeDramaList(c, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPracticeDramaList", reflect.TypeOf((*MockIAggregatePage)(nil).GetPracticeDramaList), c, req)
}

// GetQualityDramaList mocks base method.
func (m *MockIAggregatePage) GetQualityDramaList(c context0.Context, req *pia.GetQualityDramaListReq) (*pia.GetQualityDramaListResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetQualityDramaList", c, req)
	ret0, _ := ret[0].(*pia.GetQualityDramaListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetQualityDramaList indicates an expected call of GetQualityDramaList.
func (mr *MockIAggregatePageMockRecorder) GetQualityDramaList(c, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetQualityDramaList", reflect.TypeOf((*MockIAggregatePage)(nil).GetQualityDramaList), c, req)
}

// MockIGetDramaCollectToken is a mock of IGetDramaCollectToken interface.
type MockIGetDramaCollectToken struct {
	ctrl     *gomock.Controller
	recorder *MockIGetDramaCollectTokenMockRecorder
}

// MockIGetDramaCollectTokenMockRecorder is the mock recorder for MockIGetDramaCollectToken.
type MockIGetDramaCollectTokenMockRecorder struct {
	mock *MockIGetDramaCollectToken
}

// NewMockIGetDramaCollectToken creates a new mock instance.
func NewMockIGetDramaCollectToken(ctrl *gomock.Controller) *MockIGetDramaCollectToken {
	mock := &MockIGetDramaCollectToken{ctrl: ctrl}
	mock.recorder = &MockIGetDramaCollectTokenMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIGetDramaCollectToken) EXPECT() *MockIGetDramaCollectTokenMockRecorder {
	return m.recorder
}

// SetSearchOpt mocks base method.
func (m *MockIGetDramaCollectToken) SetSearchOpt(searchOptList []*pia.SearchOption) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "SetSearchOpt", searchOptList)
}

// SetSearchOpt indicates an expected call of SetSearchOpt.
func (mr *MockIGetDramaCollectTokenMockRecorder) SetSearchOpt(searchOptList interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetSearchOpt", reflect.TypeOf((*MockIGetDramaCollectToken)(nil).SetSearchOpt), searchOptList)
}

// String mocks base method.
func (m *MockIGetDramaCollectToken) String(ctx context.Context) string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "String", ctx)
	ret0, _ := ret[0].(string)
	return ret0
}

// String indicates an expected call of String.
func (mr *MockIGetDramaCollectTokenMockRecorder) String(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "String", reflect.TypeOf((*MockIGetDramaCollectToken)(nil).String), ctx)
}

// MockIQualityDramaList is a mock of IQualityDramaList interface.
type MockIQualityDramaList struct {
	ctrl     *gomock.Controller
	recorder *MockIQualityDramaListMockRecorder
}

// MockIQualityDramaListMockRecorder is the mock recorder for MockIQualityDramaList.
type MockIQualityDramaListMockRecorder struct {
	mock *MockIQualityDramaList
}

// NewMockIQualityDramaList creates a new mock instance.
func NewMockIQualityDramaList(ctrl *gomock.Controller) *MockIQualityDramaList {
	mock := &MockIQualityDramaList{ctrl: ctrl}
	mock.recorder = &MockIQualityDramaListMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIQualityDramaList) EXPECT() *MockIQualityDramaListMockRecorder {
	return m.recorder
}

// GetQualityDramaList mocks base method.
func (m *MockIQualityDramaList) GetQualityDramaList(c context.Context, req *pia.GetQualityDramaListReq) (*pia.GetQualityDramaListResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetQualityDramaList", c, req)
	ret0, _ := ret[0].(*pia.GetQualityDramaListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetQualityDramaList indicates an expected call of GetQualityDramaList.
func (mr *MockIQualityDramaListMockRecorder) GetQualityDramaList(c, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetQualityDramaList", reflect.TypeOf((*MockIQualityDramaList)(nil).GetQualityDramaList), c, req)
}

// MockIGetDramaPlayingChannelToken is a mock of IGetDramaPlayingChannelToken interface.
type MockIGetDramaPlayingChannelToken struct {
	ctrl     *gomock.Controller
	recorder *MockIGetDramaPlayingChannelTokenMockRecorder
}

// MockIGetDramaPlayingChannelTokenMockRecorder is the mock recorder for MockIGetDramaPlayingChannelToken.
type MockIGetDramaPlayingChannelTokenMockRecorder struct {
	mock *MockIGetDramaPlayingChannelToken
}

// NewMockIGetDramaPlayingChannelToken creates a new mock instance.
func NewMockIGetDramaPlayingChannelToken(ctrl *gomock.Controller) *MockIGetDramaPlayingChannelToken {
	mock := &MockIGetDramaPlayingChannelToken{ctrl: ctrl}
	mock.recorder = &MockIGetDramaPlayingChannelTokenMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIGetDramaPlayingChannelToken) EXPECT() *MockIGetDramaPlayingChannelTokenMockRecorder {
	return m.recorder
}

// String mocks base method.
func (m *MockIGetDramaPlayingChannelToken) String(ctx context0.Context) string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "String", ctx)
	ret0, _ := ret[0].(string)
	return ret0
}

// String indicates an expected call of String.
func (mr *MockIGetDramaPlayingChannelTokenMockRecorder) String(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "String", reflect.TypeOf((*MockIGetDramaPlayingChannelToken)(nil).String), ctx)
}

// MockIRecommandChannel is a mock of IRecommandChannel interface.
type MockIRecommandChannel struct {
	ctrl     *gomock.Controller
	recorder *MockIRecommandChannelMockRecorder
}

// MockIRecommandChannelMockRecorder is the mock recorder for MockIRecommandChannel.
type MockIRecommandChannelMockRecorder struct {
	mock *MockIRecommandChannel
}

// NewMockIRecommandChannel creates a new mock instance.
func NewMockIRecommandChannel(ctrl *gomock.Controller) *MockIRecommandChannel {
	mock := &MockIRecommandChannel{ctrl: ctrl}
	mock.recorder = &MockIRecommandChannelMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIRecommandChannel) EXPECT() *MockIRecommandChannelMockRecorder {
	return m.recorder
}

// GetUgcRecommandChannel mocks base method.
func (m *MockIRecommandChannel) GetUgcRecommandChannel(ctx context.Context, pageToken *PageToken) (*RecommandSearchResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUgcRecommandChannel", ctx, pageToken)
	ret0, _ := ret[0].(*RecommandSearchResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUgcRecommandChannel indicates an expected call of GetUgcRecommandChannel.
func (mr *MockIRecommandChannelMockRecorder) GetUgcRecommandChannel(ctx, pageToken interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUgcRecommandChannel", reflect.TypeOf((*MockIRecommandChannel)(nil).GetUgcRecommandChannel), ctx, pageToken)
}

// MockIDramaPlayingChannel is a mock of IDramaPlayingChannel interface.
type MockIDramaPlayingChannel struct {
	ctrl     *gomock.Controller
	recorder *MockIDramaPlayingChannelMockRecorder
}

// MockIDramaPlayingChannelMockRecorder is the mock recorder for MockIDramaPlayingChannel.
type MockIDramaPlayingChannelMockRecorder struct {
	mock *MockIDramaPlayingChannel
}

// NewMockIDramaPlayingChannel creates a new mock instance.
func NewMockIDramaPlayingChannel(ctrl *gomock.Controller) *MockIDramaPlayingChannel {
	mock := &MockIDramaPlayingChannel{ctrl: ctrl}
	mock.recorder = &MockIDramaPlayingChannelMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIDramaPlayingChannel) EXPECT() *MockIDramaPlayingChannelMockRecorder {
	return m.recorder
}

// GetPlayingChannelV2 mocks base method.
func (m *MockIDramaPlayingChannel) GetPlayingChannelV2(c context0.Context, req *pia.GetPlayingChannelV2Req) (*pia.GetPlayingChannelV2Resp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPlayingChannelV2", c, req)
	ret0, _ := ret[0].(*pia.GetPlayingChannelV2Resp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPlayingChannelV2 indicates an expected call of GetPlayingChannelV2.
func (mr *MockIDramaPlayingChannelMockRecorder) GetPlayingChannelV2(c, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPlayingChannelV2", reflect.TypeOf((*MockIDramaPlayingChannel)(nil).GetPlayingChannelV2), c, req)
}

// MockIPageToken is a mock of IPageToken interface.
type MockIPageToken struct {
	ctrl     *gomock.Controller
	recorder *MockIPageTokenMockRecorder
}

// MockIPageTokenMockRecorder is the mock recorder for MockIPageToken.
type MockIPageTokenMockRecorder struct {
	mock *MockIPageToken
}

// NewMockIPageToken creates a new mock instance.
func NewMockIPageToken(ctrl *gomock.Controller) *MockIPageToken {
	mock := &MockIPageToken{ctrl: ctrl}
	mock.recorder = &MockIPageTokenMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIPageToken) EXPECT() *MockIPageTokenMockRecorder {
	return m.recorder
}

// String mocks base method.
func (m *MockIPageToken) String() string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "String")
	ret0, _ := ret[0].(string)
	return ret0
}

// String indicates an expected call of String.
func (mr *MockIPageTokenMockRecorder) String() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "String", reflect.TypeOf((*MockIPageToken)(nil).String))
}

// MockIChannelTopConfig is a mock of IChannelTopConfig interface.
type MockIChannelTopConfig struct {
	ctrl     *gomock.Controller
	recorder *MockIChannelTopConfigMockRecorder
}

// MockIChannelTopConfigMockRecorder is the mock recorder for MockIChannelTopConfig.
type MockIChannelTopConfigMockRecorder struct {
	mock *MockIChannelTopConfig
}

// NewMockIChannelTopConfig creates a new mock instance.
func NewMockIChannelTopConfig(ctrl *gomock.Controller) *MockIChannelTopConfig {
	mock := &MockIChannelTopConfig{ctrl: ctrl}
	mock.recorder = &MockIChannelTopConfigMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIChannelTopConfig) EXPECT() *MockIChannelTopConfigMockRecorder {
	return m.recorder
}

// GetUgcChannelTopConfig mocks base method.
func (m *MockIChannelTopConfig) GetUgcChannelTopConfig(ctx context.Context, token *PageToken) ([]*pia0.ChannelOrderConfig, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUgcChannelTopConfig", ctx, token)
	ret0, _ := ret[0].([]*pia0.ChannelOrderConfig)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUgcChannelTopConfig indicates an expected call of GetUgcChannelTopConfig.
func (mr *MockIChannelTopConfigMockRecorder) GetUgcChannelTopConfig(ctx, token interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUgcChannelTopConfig", reflect.TypeOf((*MockIChannelTopConfig)(nil).GetUgcChannelTopConfig), ctx, token)
}

// MockIPracticeDramaList is a mock of IPracticeDramaList interface.
type MockIPracticeDramaList struct {
	ctrl     *gomock.Controller
	recorder *MockIPracticeDramaListMockRecorder
}

// MockIPracticeDramaListMockRecorder is the mock recorder for MockIPracticeDramaList.
type MockIPracticeDramaListMockRecorder struct {
	mock *MockIPracticeDramaList
}

// NewMockIPracticeDramaList creates a new mock instance.
func NewMockIPracticeDramaList(ctrl *gomock.Controller) *MockIPracticeDramaList {
	mock := &MockIPracticeDramaList{ctrl: ctrl}
	mock.recorder = &MockIPracticeDramaListMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIPracticeDramaList) EXPECT() *MockIPracticeDramaListMockRecorder {
	return m.recorder
}

// GetPracticeDramaList mocks base method.
func (m *MockIPracticeDramaList) GetPracticeDramaList(c context.Context, req *pia.GetPracticeDramaListReq) (*pia.GetPracticeDramaListResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPracticeDramaList", c, req)
	ret0, _ := ret[0].(*pia.GetPracticeDramaListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPracticeDramaList indicates an expected call of GetPracticeDramaList.
func (mr *MockIPracticeDramaListMockRecorder) GetPracticeDramaList(c, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPracticeDramaList", reflect.TypeOf((*MockIPracticeDramaList)(nil).GetPracticeDramaList), c, req)
}

// MockIChannelInfoList is a mock of IChannelInfoList interface.
type MockIChannelInfoList struct {
	ctrl     *gomock.Controller
	recorder *MockIChannelInfoListMockRecorder
}

// MockIChannelInfoListMockRecorder is the mock recorder for MockIChannelInfoList.
type MockIChannelInfoListMockRecorder struct {
	mock *MockIChannelInfoList
}

// NewMockIChannelInfoList creates a new mock instance.
func NewMockIChannelInfoList(ctrl *gomock.Controller) *MockIChannelInfoList {
	mock := &MockIChannelInfoList{ctrl: ctrl}
	mock.recorder = &MockIChannelInfoListMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIChannelInfoList) EXPECT() *MockIChannelInfoListMockRecorder {
	return m.recorder
}

// GetAllIds mocks base method.
func (m *MockIChannelInfoList) GetAllIds() []uint32 {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllIds")
	ret0, _ := ret[0].([]uint32)
	return ret0
}

// GetAllIds indicates an expected call of GetAllIds.
func (mr *MockIChannelInfoListMockRecorder) GetAllIds() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllIds", reflect.TypeOf((*MockIChannelInfoList)(nil).GetAllIds))
}

// MockIChannelMgr is a mock of IChannelMgr interface.
type MockIChannelMgr struct {
	ctrl     *gomock.Controller
	recorder *MockIChannelMgrMockRecorder
}

// MockIChannelMgrMockRecorder is the mock recorder for MockIChannelMgr.
type MockIChannelMgrMockRecorder struct {
	mock *MockIChannelMgr
}

// NewMockIChannelMgr creates a new mock instance.
func NewMockIChannelMgr(ctrl *gomock.Controller) *MockIChannelMgr {
	mock := &MockIChannelMgr{ctrl: ctrl}
	mock.recorder = &MockIChannelMgrMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIChannelMgr) EXPECT() *MockIChannelMgrMockRecorder {
	return m.recorder
}

// GetChannelHotV2 mocks base method.
func (m *MockIChannelMgr) GetChannelHotV2(c context.Context, channelInfoMap map[uint32]*channelsvr.ChannelSimpleInfo) (map[uint32]uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChannelHotV2", c, channelInfoMap)
	ret0, _ := ret[0].(map[uint32]uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChannelHotV2 indicates an expected call of GetChannelHotV2.
func (mr *MockIChannelMgrMockRecorder) GetChannelHotV2(c, channelInfoMap interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChannelHotV2", reflect.TypeOf((*MockIChannelMgr)(nil).GetChannelHotV2), c, channelInfoMap)
}

// MockIMineMgr is a mock of IMineMgr interface.
type MockIMineMgr struct {
	ctrl     *gomock.Controller
	recorder *MockIMineMgrMockRecorder
}

// MockIMineMgrMockRecorder is the mock recorder for MockIMineMgr.
type MockIMineMgrMockRecorder struct {
	mock *MockIMineMgr
}

// NewMockIMineMgr creates a new mock instance.
func NewMockIMineMgr(ctrl *gomock.Controller) *MockIMineMgr {
	mock := &MockIMineMgr{ctrl: ctrl}
	mock.recorder = &MockIMineMgrMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIMineMgr) EXPECT() *MockIMineMgrMockRecorder {
	return m.recorder
}

// BatchDeleteMyPlayingRecord mocks base method.
func (m *MockIMineMgr) BatchDeleteMyPlayingRecord(c context.Context, req *pia.PiaBatchDeleteMyPlayingRecordReq) (*pia.PiaBatchDeleteMyPlayingRecordResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchDeleteMyPlayingRecord", c, req)
	ret0, _ := ret[0].(*pia.PiaBatchDeleteMyPlayingRecordResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchDeleteMyPlayingRecord indicates an expected call of BatchDeleteMyPlayingRecord.
func (mr *MockIMineMgrMockRecorder) BatchDeleteMyPlayingRecord(c, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchDeleteMyPlayingRecord", reflect.TypeOf((*MockIMineMgr)(nil).BatchDeleteMyPlayingRecord), c, req)
}

// GetMyDramaPlayingRecord mocks base method.
func (m *MockIMineMgr) GetMyDramaPlayingRecord(c context.Context, req *pia.GetMyDramaPlayingRecordReq) (*pia.GetMyDramaPlayingRecordResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMyDramaPlayingRecord", c, req)
	ret0, _ := ret[0].(*pia.GetMyDramaPlayingRecordResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMyDramaPlayingRecord indicates an expected call of GetMyDramaPlayingRecord.
func (mr *MockIMineMgrMockRecorder) GetMyDramaPlayingRecord(c, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMyDramaPlayingRecord", reflect.TypeOf((*MockIMineMgr)(nil).GetMyDramaPlayingRecord), c, req)
}

// GetMyPlayingRecordIdList mocks base method.
func (m *MockIMineMgr) GetMyPlayingRecordIdList(c context.Context, req *pia.PiaGetMyPlayingRecordIdListReq) (*pia.PiaGetMyPlayingRecordIdListResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMyPlayingRecordIdList", c, req)
	ret0, _ := ret[0].(*pia.PiaGetMyPlayingRecordIdListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMyPlayingRecordIdList indicates an expected call of GetMyPlayingRecordIdList.
func (mr *MockIMineMgrMockRecorder) GetMyPlayingRecordIdList(c, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMyPlayingRecordIdList", reflect.TypeOf((*MockIMineMgr)(nil).GetMyPlayingRecordIdList), c, req)
}

// MockCopiedDramaMgr is a mock of CopiedDramaMgr interface.
type MockCopiedDramaMgr struct {
	ctrl     *gomock.Controller
	recorder *MockCopiedDramaMgrMockRecorder
}

// MockCopiedDramaMgrMockRecorder is the mock recorder for MockCopiedDramaMgr.
type MockCopiedDramaMgrMockRecorder struct {
	mock *MockCopiedDramaMgr
}

// NewMockCopiedDramaMgr creates a new mock instance.
func NewMockCopiedDramaMgr(ctrl *gomock.Controller) *MockCopiedDramaMgr {
	mock := &MockCopiedDramaMgr{ctrl: ctrl}
	mock.recorder = &MockCopiedDramaMgrMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockCopiedDramaMgr) EXPECT() *MockCopiedDramaMgrMockRecorder {
	return m.recorder
}

// ConfirmCopiedDrama mocks base method.
func (m *MockCopiedDramaMgr) ConfirmCopiedDrama(ctx context.Context, req *pia.PiaConfirmCoverCopyDramaReq) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ConfirmCopiedDrama", ctx, req)
	ret0, _ := ret[0].(error)
	return ret0
}

// ConfirmCopiedDrama indicates an expected call of ConfirmCopiedDrama.
func (mr *MockCopiedDramaMgrMockRecorder) ConfirmCopiedDrama(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ConfirmCopiedDrama", reflect.TypeOf((*MockCopiedDramaMgr)(nil).ConfirmCopiedDrama), ctx, req)
}

// CopyDramaList mocks base method.
func (m *MockCopiedDramaMgr) CopyDramaList(c context.Context, req *pia.PiaCopyDramaListReq) (*pia.PiaCopyDramaListResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CopyDramaList", c, req)
	ret0, _ := ret[0].(*pia.PiaCopyDramaListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CopyDramaList indicates an expected call of CopyDramaList.
func (mr *MockCopiedDramaMgrMockRecorder) CopyDramaList(c, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CopyDramaList", reflect.TypeOf((*MockCopiedDramaMgr)(nil).CopyDramaList), c, req)
}

// CreateTempDramaCopyV2 mocks base method.
func (m *MockCopiedDramaMgr) CreateTempDramaCopyV2(ctx context.Context, req *pia.PiaCreateDramaCopyV2Req, uid uint32) (*pia.PiaCreateDramaCopyV2Resp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateTempDramaCopyV2", ctx, req, uid)
	ret0, _ := ret[0].(*pia.PiaCreateDramaCopyV2Resp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateTempDramaCopyV2 indicates an expected call of CreateTempDramaCopyV2.
func (mr *MockCopiedDramaMgrMockRecorder) CreateTempDramaCopyV2(ctx, req, uid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateTempDramaCopyV2", reflect.TypeOf((*MockCopiedDramaMgr)(nil).CreateTempDramaCopyV2), ctx, req, uid)
}

// DeleteDramaCopy mocks base method.
func (m *MockCopiedDramaMgr) DeleteDramaCopy(c context.Context, req *pia.DeleteDramaCopyReq) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteDramaCopy", c, req)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteDramaCopy indicates an expected call of DeleteDramaCopy.
func (mr *MockCopiedDramaMgrMockRecorder) DeleteDramaCopy(c, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteDramaCopy", reflect.TypeOf((*MockCopiedDramaMgr)(nil).DeleteDramaCopy), c, req)
}

// GetMyDramaCopyList mocks base method.
func (m *MockCopiedDramaMgr) GetMyDramaCopyList(c context.Context, req *pia.GetMyDramaCopyListReq) (*pia.GetMyDramaCopyListResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMyDramaCopyList", c, req)
	ret0, _ := ret[0].(*pia.GetMyDramaCopyListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMyDramaCopyList indicates an expected call of GetMyDramaCopyList.
func (mr *MockCopiedDramaMgrMockRecorder) GetMyDramaCopyList(c, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMyDramaCopyList", reflect.TypeOf((*MockCopiedDramaMgr)(nil).GetMyDramaCopyList), c, req)
}

// SetDramaCopyStatus mocks base method.
func (m *MockCopiedDramaMgr) SetDramaCopyStatus(c context.Context, req *pia.SetDramaCopyStatusReq) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetDramaCopyStatus", c, req)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetDramaCopyStatus indicates an expected call of SetDramaCopyStatus.
func (mr *MockCopiedDramaMgrMockRecorder) SetDramaCopyStatus(c, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetDramaCopyStatus", reflect.TypeOf((*MockCopiedDramaMgr)(nil).SetDramaCopyStatus), c, req)
}

// MockPushService is a mock of PushService interface.
type MockPushService struct {
	ctrl     *gomock.Controller
	recorder *MockPushServiceMockRecorder
}

// MockPushServiceMockRecorder is the mock recorder for MockPushService.
type MockPushServiceMockRecorder struct {
	mock *MockPushService
}

// NewMockPushService creates a new mock instance.
func NewMockPushService(ctrl *gomock.Controller) *MockPushService {
	mock := &MockPushService{ctrl: ctrl}
	mock.recorder = &MockPushServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockPushService) EXPECT() *MockPushServiceMockRecorder {
	return m.recorder
}

// PushMsgToChannel mocks base method.
func (m *MockPushService) PushMsgToChannel(ctx context.Context, channelId uint32, msgType channel.ChannelMsgType, content string, data proto.MessageV1) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PushMsgToChannel", ctx, channelId, msgType, content, data)
	ret0, _ := ret[0].(error)
	return ret0
}

// PushMsgToChannel indicates an expected call of PushMsgToChannel.
func (mr *MockPushServiceMockRecorder) PushMsgToChannel(ctx, channelId, msgType, content, data interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PushMsgToChannel", reflect.TypeOf((*MockPushService)(nil).PushMsgToChannel), ctx, channelId, msgType, content, data)
}

// PushMsgToUsers mocks base method.
func (m *MockPushService) PushMsgToUsers(ctx context.Context, msgType push.PushMessage_CMD_TYPE, data proto.MessageV1, uid ...uint32) error {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, msgType, data}
	for _, a := range uid {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PushMsgToUsers", varargs...)
	ret0, _ := ret[0].(error)
	return ret0
}

// PushMsgToUsers indicates an expected call of PushMsgToUsers.
func (mr *MockPushServiceMockRecorder) PushMsgToUsers(ctx, msgType, data interface{}, uid ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, msgType, data}, uid...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PushMsgToUsers", reflect.TypeOf((*MockPushService)(nil).PushMsgToUsers), varargs...)
}

// PushOrderDramaListMsg mocks base method.
func (m *MockPushService) PushOrderDramaListMsg(ctx context.Context, channelId uint32, data *pia.DramaOrderList) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PushOrderDramaListMsg", ctx, channelId, data)
	ret0, _ := ret[0].(error)
	return ret0
}

// PushOrderDramaListMsg indicates an expected call of PushOrderDramaListMsg.
func (mr *MockPushServiceMockRecorder) PushOrderDramaListMsg(ctx, channelId, data interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PushOrderDramaListMsg", reflect.TypeOf((*MockPushService)(nil).PushOrderDramaListMsg), ctx, channelId, data)
}
