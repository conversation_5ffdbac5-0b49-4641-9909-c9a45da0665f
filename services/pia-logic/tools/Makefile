#



.PHONY: mock
mock:
	cd ../mgr && mockgen -destination=./mock_gen.go -package=mgr -source=./mgr_api.go
	cd ../conf && mockgen -destination=./mock_gen.go -package=conf -source=./config_gen.go

.PHONY: test
test:
	go test -count=1 -cover ../...

.PHONY: setup-dev
setup-dev:
	docker-compose -f docker-compose.yaml up -d

.PHONY: remove-dev
remove-dev:
	docker-compose -f docker-compose.yaml down

.PHONY: drama-proto
drama-proto:
	protoc --go_out=:../store drama.proto