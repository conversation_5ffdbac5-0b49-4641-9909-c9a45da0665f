package dispatcher

import (
    "context"
    "golang.52tt.com/pkg/log"
    gabase "golang.52tt.com/protocol/app"
    channelPb "golang.52tt.com/protocol/app/channel"
    "golang.52tt.com/protocol/app/revenue"
    pb "golang.52tt.com/protocol/services/revenue-api-go"
    virtual_image_user "golang.52tt.com/protocol/services/virtual-image-user"
    UKWpb "golang.52tt.com/protocol/services/youknowwho"
    "golang.52tt.com/services/revenue-api-go/rpc"
)

// 虚拟形象二期
type virtualImageFillerMgr struct {
}

type virtualImageFiller struct {
    virtualImageMicInfoStrMap map[uint32][]byte
    ukwMap                    map[uint32]*UKWpb.UKWPersonInfo

    // 进房特效信息
    followEffectInfo *revenue.VirtualImageEnterFollowOpt
}

func NewVirtualImageFillerMgr() *virtualImageFillerMgr {
    return &virtualImageFillerMgr{}
}

func (m *virtualImageFillerMgr) GetMicFiller(ctx context.Context, req *CommonReq) (filler *MicFiller, err error) {
    return nil, nil
}

func (m *virtualImageFillerMgr) GetChannelImFiller(ctx context.Context, req *CommonReq) (filler *ChannelImFiller, err error) {
    return nil, nil
}

func (m *virtualImageFillerMgr) GetFillerIndex() RevenueInfoType {
    return RevenueInfoTypeVirtualImage
}

func (m *virtualImageFillerMgr) GetChannelFiller(ctx context.Context, req *CommonReq) (filler *ChannelFiller, err error) {
    vaFiller := &virtualImageFiller{}

    vaFiller.followEffectInfo, err = getVirtualImageEnterFollowOpt(ctx, req)

    return &ChannelFiller{ChannelFillerInterface: vaFiller}, err
}

func (h *virtualImageFiller) FillChannelInfo(opt *revenue.RevenueEnterChannelExtend) {
    opt.VirtualImageEnterFollowOpt = h.followEffectInfo
}

func getVirtualImageEnterFollowOpt(ctx context.Context, req *CommonReq) (*revenue.VirtualImageEnterFollowOpt, error) {
    out := &revenue.VirtualImageEnterFollowOpt{}

    if len(req.UidList) == 0 || req.Scene != uint32(pb.RevenueSceneType_RevenueSceneTypeEnterChannel) ||
        req.FollowedUid == 0 || req.ChannelId == 0 {
        // 无需处理
        return out, nil
    }

    // 仅pgc、官频和ugc房间需要处理
    if req.ChannelType != uint32(channelPb.ChannelType_GUILD_PUBLIC_FUN_CHANNEL_TYPE) &&
        req.ChannelType != uint32(channelPb.ChannelType_RADIO_LIVE_CHANNEL_TYPE) &&
        req.ChannelType != uint32(channelPb.ChannelType_USER_CHANNEL_TYPE) &&
        req.ChannelType != uint32(channelPb.ChannelType_OFFICIAL_LIVE_CHANNEL_TYPE) {
        return out, nil
    }

    // 判断相关用户是否神秘人
    userMap, err := getUserProfileMap(ctx, []uint32{req.UidList[0], req.FollowedUid}, true)
    if err != nil {
        return out, err
    }

    if userMap[req.UidList[0]].GetPrivilege().GetType() == uint32(gabase.EUserPrivilegeType_ENUM_USER_PRIVILEGE_UKW) ||
        userMap[req.FollowedUid].GetPrivilege().GetType() == uint32(gabase.EUserPrivilegeType_ENUM_USER_PRIVILEGE_UKW) {
        // 神秘人不需要处理
        return out, nil
    }

    uid := req.UidList[0]
    followUid := req.FollowedUid

    resp, err := rpc.Cli.VirtualImageUserCli.GetFollowEnterChannelInfo(ctx, &virtual_image_user.GetFollowEnterChannelInfoRequest{
        Uid: uid, FollowedUid: followUid,
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "getVirtualImageEnterFollowOpt fail to GetFollowEnterChannelInfo. uid:%d followUid:%d err:%v", uid, followUid, err)
        return out, err
    }

    out = &revenue.VirtualImageEnterFollowOpt{
        UserVirtualImageInfo:         fillUserVirtualImageInfo(resp.GetUserItemInfo(), userMap),
        FollowedUserVirtualImageInfo: fillUserVirtualImageInfo(resp.GetFollowedUserItemInfo(), userMap),
    }

    log.DebugWithCtx(ctx, "getVirtualImageEnterFollowOpt success uid:%d followUid:%d out:%v", req.UidList[0], req.FollowedUid, out)
    return out, nil
}

func fillUserVirtualImageInfo(info *virtual_image_user.UserInuseItemInfo, userMap map[uint32]*gabase.UserProfile) *revenue.UserVirtualImageInfo {
    if info == nil || info.GetUid() == 0 {
        return nil
    }

    list := make([]uint32, 0)
    for _, v := range info.GetItems() {
        list = append(list, v.GetCfgId())
    }

    return &revenue.UserVirtualImageInfo{
        UserProfile:    userMap[info.GetUid()],
        ResourceIdList: list,
    }
}
