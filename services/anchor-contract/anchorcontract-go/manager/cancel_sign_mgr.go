package manager

import (
	"context"
	"encoding/json"
	"fmt"
	"golang.52tt.com/pkg/marketid_helper"
	"golang.52tt.com/pkg/protocol/grpc"
	guildmanagementpb "golang.52tt.com/protocol/services/guild-management-svr"
	"sort"
	"strings"
	"time"

	"github.com/jinzhu/gorm"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/protocol/common/status"
	pb "golang.52tt.com/protocol/services/anchorcontract-go"
	apicenterPB "golang.52tt.com/protocol/services/apicenter/apiserver"
	"golang.52tt.com/protocol/services/channellivemgr"
	Exchange "golang.52tt.com/protocol/services/exchange"
	"golang.52tt.com/protocol/services/minToolkit/kafka/pb/kafkaanchorcontract"
	"golang.52tt.com/services/anchor-contract/anchorcontract-go/mysql"
	"golang.52tt.com/services/anchor-contract/anchorcontract-go/util"
)

func (m *AnchorContractMgr) GetCancelContractApplyList(ctx context.Context, in *pb.GetCancelContractApplyListReq) (*pb.GetCancelContractApplyListResp, error) {
	out := &pb.GetCancelContractApplyListResp{}
	begin := in.GetPage() * in.GetPageSize()

	list, err := m.store.GetGuildCancelContractApplyList(in.GetGuildId(), begin, in.GetPageSize())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetCancelContractApplyList fail to GetGuildCancelContractApplyList in:%+v, err:%v", in, err)
		return out, err
	}

	out.ApplyList = make([]*pb.CancelContractApply, 0, len(list))
	for _, info := range list {
		//
		anchorIdentityList, err := m.store.GetAnchorIdentity(nil, info.Uid)
		if err != nil {
			log.ErrorWithCtx(ctx, "ApplySignEsport fail to GetAnchorIdentity in:%+v, err:%v", in, err)
			return out, err
		}
		identityList := make([]uint32, 0, len(anchorIdentityList))
		for _, info := range anchorIdentityList {
			identityList = append(identityList, info.IdentityType)
		}

		//  电竞指导、语音主播、多人互动成员
		sort.Slice(identityList, func(i, j int) bool {
			return identityList[i] > identityList[j]
		})

		proofList, err := m.tranProofKeyListToProofUrlList(ctx, info.ImageProofList, info.VideoProofList)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetCancelContractApplyList fail to tranProofKeyListToProofUrlList in:%+v, err:%v", in, err)
		}

		out.ApplyList = append(out.ApplyList, &pb.CancelContractApply{
			Uid:                 info.Uid,
			GuildId:             info.GuildId,
			Status:              info.Status,
			ApplyTimestamp:      uint32(info.ApplyTime.Unix()),
			AnchorIdentityList:  identityList,
			CancelType:          info.CancelType,
			Reason:              info.Reason,
			CancelReasonText:    info.ReasonText,
			ProofList:           proofList,
			NegotiateReasonType: strings.Split(info.NegotiateReasonType, "_"),
		})
	}

	log.DebugWithCtx(ctx, "GetCancelContractApplyList in:%+v, out:%+v", in, out)
	return out, nil
}

func (m *AnchorContractMgr) ApplyCancelContract(ctx context.Context, in *pb.ApplyCancelContractReq) (*pb.ApplyCancelContractResp, error) {
	log.InfoWithCtx(ctx, "ApplyCancelContract uid %d, guildId %d, CancelReason %d",
		in.Uid, in.GuildId, in.CancelReason)

	out := &pb.ApplyCancelContractResp{}

	contract, exist, err := m.store.GetContractWithUid(in.GetUid())
	if err != nil {
		log.ErrorWithCtx(ctx, "ApplyCancelContract fail to GetContractWithUid in:%+v, err:%v", in, err)
		return out, err
	}

	if !exist || contract.GuildId != in.GetGuildId() {
		log.ErrorWithCtx(ctx, "ApplyCancelContract fail. in:%+v, err:contract is not found", in)
		return out, protocol.NewExactServerError(nil, status.ErrContractNonexist)
	}

	// 补充信息
	cancelApplyLog := &mysql.CancelContractApplyLog{
		Uid:                 in.GetUid(),
		GuildId:             in.GetGuildId(),
		SignTime:            contract.SignTime,
		ContractExpireTime:  contract.ContractExpireTime,
		Status:              0,
		Reason:              in.GetCancelReason(),
		ApplyTime:           time.Now(),
		LiveObtainTime:      time.Now(),
		MultiplayObtainTime: time.Now(),
		UpdateTime:          time.Now(),
	}

	identityList, err := m.store.GetAnchorIdentity(nil, in.GetUid())
	if err != nil {
		log.ErrorWithCtx(ctx, "ApplyCancelContract fail to GetAnchorIdentity in:%+v, err:%v", in, err)
		return out, err
	}

	identityInfo := &mysql.IdentityInfo{}
	for _, info := range identityList {
		if info.IdentityType == uint32(pb.SIGN_ANCHOR_IDENTITY_SIGN_ANCHOR_IDENTITY_RADIO_LIVE) {
			cancelApplyLog.LiveIdentityStatus = 1
			cancelApplyLog.LiveObtainTime = info.ObtainTime
			cancelApplyLog.AgentUid = info.AgentUid
		} else if info.IdentityType == uint32(pb.SIGN_ANCHOR_IDENTITY_SIGN_ANCHOR_IDENTITY_MULTIPLAYER) {
			cancelApplyLog.MultiplayIdentityStatus = 1
			cancelApplyLog.MultiplayObtainTime = info.ObtainTime
		} else {
			log.Errorf("ApplyCancelContract invalid type :%v info %+v", info.IdentityType, info)
		}

		identityInfo.AnchorIdentityInfoList = append(identityInfo.AnchorIdentityInfoList, &mysql.AnchorIdentityInfo{
			IdentityType: info.IdentityType,
			ObtainTime:   uint32(info.ObtainTime.Unix()),
			AgentUid:     info.AgentUid,
		})
	}
	identityInfoStr, _ := json.Marshal(identityInfo)
	cancelApplyLog.IdentityInfo = string(identityInfoStr)

	if cancelApplyLog.LiveIdentityStatus == 1 {
		liveAnchorInfo, err := m.liveMgrCli.GetAnchorByUidList(ctx, &channellivemgr.GetAnchorByUidListReq{UidList: []uint32{in.GetUid()}})
		if err != nil {
			log.ErrorWithCtx(ctx, "ApplyCancelContract fail to GetAnchorByUidList in:%+v, err:%v", in, err)
			return out, err
		}

		if len(liveAnchorInfo.AnchorList) > 0 {
			cancelApplyLog.TagId = liveAnchorInfo.AnchorList[0].TagId
		}
	}

	err = m.store.Transaction(ctx, func(tx *gorm.DB) error {

		err := m.store.AddCancelContractApply(tx, &mysql.CancelContractApply{
			Uid:        in.GetUid(),
			GuildId:    in.GetGuildId(),
			GuildName:  in.GetGuildName(),
			ApplyTime:  time.Now(),
			CancelType: uint32(pb.CancelContractType_CancelContractType_Older),
			Reason:     cancelApplyLog.Reason,
		})
		if err != nil {
			log.ErrorWithCtx(ctx, "ApplyCancelContract fail to AddCancelContractApply in:%+v, err:%v", in, err)
			return err
		}

		err = m.store.AddCancelContractApplyLog(tx, cancelApplyLog)
		if err != nil {
			log.ErrorWithCtx(ctx, "ApplyCancelContract fail to AddCancelContractApplyLog in:%+v, err:%v", in, err)
			return err
		}

		return nil
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "ApplyCancelContract fail to Transaction in:%+v, err:%v", in, err)
		return out, err
	}

	log.InfoWithCtx(ctx, "ApplyCancelContract in:%+v, cancelApplyLog %v", in, cancelApplyLog)
	return out, nil
}

func (m *AnchorContractMgr) HandlerCancelContractApply(rootCtx context.Context, in *pb.HandlerCancelContractApplyReq) (*pb.HandlerCancelContractApplyResp, error) {
	dealt, _ := rootCtx.Deadline()
	log.InfoWithCtx(rootCtx, "HandlerCancelContractApply uid %d, guildId %d, TargetUid %d HandleOpr %d, applyId:%d, deadline:%v",
		in.Uid, in.GuildId, in.TargetUid, in.HandleOpr, in.ApplyId, dealt)

	if in.GetHandleOpr() == uint32(pb.CANCEL_APPLY_OPR_CANCEL_APPLY_OPR_NORMAL) {
		// 兼容旧版会长经营后台的解约操作，签约优化需求上了之后就可以去掉
		in.HandleOpr = uint32(pb.CANCEL_APPLY_OPR_CANCEL_APPLY_OPR_ACCEPT)
	}

	log.InfoWithCtx(rootCtx, "HandlerCancelContractApply after uid %d, guildId %d, TargetUid %d HandleOpr %d, applyId:%d, deadline:%v",
		in.Uid, in.GuildId, in.TargetUid, in.HandleOpr, in.ApplyId, dealt)

	//上游传递的ctx，过期时间可能不足1分钟，这里重新生成一个ctx，方便审核耗时
	ctx, cancel := grpc.NewContextWithInfoTimeout(rootCtx, time.Minute)
	defer cancel()

	out := &pb.HandlerCancelContractApplyResp{}

	// 获取解约申请
	var apply *mysql.CancelContractApplyLog
	var err error
	if in.ApplyId == 0 {
		apply, err = m.store.GetGuildLeaderCanHandleCancelContractApplyLog(ctx, in.TargetUid, in.GuildId)
		if err != nil {
			log.ErrorWithCtx(ctx, "HandlerCancelContractApply fail to GetGuildLeaderCanHandleCancelContractApplyLog in:%+v, err:%v", in, err)
			return out, err
		}
	} else {
		apply, err = m.store.GetCancelContractApplyLogById(in.ApplyId)
		if err != nil {
			log.ErrorWithCtx(ctx, "HandlerCancelContractApply GetCancelContractApplyLogById fail req: %v, err: %v", in, err)
			return out, protocol.NewExactServerError(nil, status.ErrContractApplySignLimitOther, "获取解约申请失败")
		}
	}

	if apply == nil {
		log.ErrorWithCtx(ctx, "HandlerCancelContractApply fail apply not exist req: %v", in)
		return out, protocol.NewExactServerError(nil, status.ErrContractApplySignLimitOther, "解约申请已处理")
	}
	if apply.Status != uint32(pb.CancelContractStatus_CancelContractStatus_Apply) && apply.Status != uint32(pb.CancelContractStatus_CancelContractStatus_NegotiateOfficeAccepted) &&
		apply.Status != uint32(pb.CancelContractStatus_CancelContractStatus_PayOfficeAccepted) {
		log.ErrorWithCtx(ctx, "HandlerCancelContractApply fail apply not exist req: %v, apply:%v", in, apply)
		return out, protocol.NewExactServerError(nil, status.ErrContractApplySignLimitOther, "该条记录已处理")
	}
	//再检查一次条件, 不满足条件则直接终止
	if isAbort, err := m.checkApplyCancelAbortWhenAccept(ctx, apply.Uid, apply.GuildId); err != nil {
		if isAbort { //解约失败, 终止申请
			_ = m.AbortCancelContractApply(ctx, apply.Uid, apply.GuildId, apply.CancelType, apply.ApplyId, "解约失败")
		}
		return out, err
	}

	exist, identityInfo, err := m.store.GetCancelContractApply(in.GetTargetUid(), in.GetGuildId())
	if err != nil {
		log.ErrorWithCtx(ctx, "HandlerCancelContractApply fail to GetCancelContractApply in:%+v, err:%v", in, err)
		return out, err
	}
	if !exist {
		log.ErrorWithCtx(ctx, "HandlerCancelContractApply fail no exist. in:%+v", in)
		return out, protocol.NewExactServerError(nil, status.ErrContractNonexist, "解约申请已处理")
	}

	// 检查改解约申请是否可以被接受或者拒绝
	canAccept, canReject := isCanOperatorAcceptAndReject(identityInfo.CancelType, identityInfo.Status)
	if in.GetHandleOpr() == uint32(pb.CANCEL_APPLY_OPR_CANCEL_APPLY_OPR_REJECT) && !canReject {
		log.ErrorWithCtx(ctx, "HandlerCancelContractApply fail canReject. in:%+v", in)
		return out, protocol.NewExactServerError(nil, status.ErrContractApplySignLimitOther, "该解约申请不可拒绝")
	}
	if in.GetHandleOpr() == uint32(pb.CANCEL_APPLY_OPR_CANCEL_APPLY_OPR_ACCEPT) && !canAccept {
		log.ErrorWithCtx(ctx, "HandlerCancelContractApply fail canAccept. in:%+v", in)
		return out, protocol.NewExactServerError(nil, status.ErrContractApplySignLimitOther, "该解约申请不可接受")
	}

	if in.GetHandleOpr() == uint32(pb.CANCEL_APPLY_OPR_CANCEL_APPLY_OPR_REJECT) && identityInfo.CancelType == uint32(pb.CancelContractType_CancelContractType_Negotiate) {
		if (len(in.ProofUrls) == 0 && len(in.ProofList) == 0) || len(in.RejectTxt) == 0 {
			log.ErrorWithCtx(ctx, "HandlerCancelContractApply fail to RejectTxt or ProofUrls. in:%+v", in)
			return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "拒绝理由或凭证不能为空")
		}

		if len(in.ProofUrls) > 0 {
			// 图片审核

			allUrls := append(in.ProofUrls, in.ProofVideoUrls...)
			allTargetUrls, err := m.CheckAndTransforUrls(ctx, allUrls)
			if err != nil {
				log.ErrorWithCtx(ctx, "GetGuildCancelSignRecordList CheckAndTransforUrls fail req: %v, err: %v", in, err)
				return out, err
			}

			log.InfoWithCtx(ctx, "HandlerCancelContractApply in:%+v, allTargetUrls:%+v", in, allTargetUrls)

			proofUrls := make([]string, 0)
			proofVideoUrls := make([]string, 0)
			for _, item := range in.GetProofUrls() {
				proofUrls = append(proofUrls, allTargetUrls[item])
			}

			for _, item := range in.GetProofVideoUrls() {
				proofVideoUrls = append(proofVideoUrls, allTargetUrls[item])
			}

			err = m.SyncCensoringMix(ctx, in.GetUid(), in.GetGuildId(), in.RejectTxt, proofUrls, proofVideoUrls, nil, "", "")
			if err != nil {
				log.ErrorWithCtx(ctx, "HandlerCancelContractApply fail to SyncCensoringMix in:%+v, err:%v", in, err)
				if !m.newDyConf.GetContractDyConf().CensoringIgnoreResult {
					// 如果是违规，改一下错误码，避免前端直接返回到上一页
					if protocol.ToServerError(err).Code() == status.ErrContractApplySignLimitOther && strings.Contains(protocol.ToServerError(err).Message(), "违规") {
						log.ErrorWithCtx(ctx, "HandlerCancelContractApply fail to SyncCensoringMix in:%+v, err:%v", in, err)
						return out, protocol.NewExactServerError(nil, status.ErrRevenueSvrErr, protocol.ToServerError(err).Message())
					}
					return out, err
				}
			}
		}

		imgKeys := make([]string, 0)
		videoKeys := make([]string, 0)

		// 新版，带视频的证据
		if len(in.ProofList) > 0 {
			for _, item := range in.ProofList {
				if item.GetType() == pb.ProofContent_ProofType_Video {
					videoKeys = append(videoKeys, item.GetKey())
				}

				if item.GetType() == pb.ProofContent_ProofType_Image {
					imgKeys = append(imgKeys, item.GetKey())
				}
			}

			censorMap := make(map[string]string)
			if len(videoKeys) > 0 {
				censorMap, err = m.cache.BatchGetCensorKey(ctx, videoKeys)
				if err != nil {
					log.ErrorWithCtx(ctx, "ApplyCancelContractNew BatchGetCensorKey fail uid:%d, guild:%d, err:%v", in.GetUid(), in.GetGuildId(), err)
					return out, err
				}
			}

			for _, item := range in.ProofList {
				if item.GetType() == pb.ProofContent_ProofType_Video {
					if item.GetCensorKey() != censorMap[item.GetKey()] {
						log.ErrorWithCtx(ctx, "ApplyCancelContractNew video censor fail uid:%d, guild:%d, key:%s, censorKey:%s", in.GetUid(), in.GetGuildId(), item.GetKey(), item.GetCensorKey())
						return out, protocol.NewExactServerError(nil, status.ErrContractApplySignLimitOther, "视频审核未通过，请重新上传")
					}
				}
			}

			// 可能已经有转码结果了
			videoTranscode, _ := m.cache.BatchGetTranscodeResult(ctx, videoKeys)
			for i, item := range videoKeys {
				if res, ok := videoTranscode[item]; ok {
					videoKeys[i] = res
				}
			}
			log.DebugWithCtx(ctx, "HandlerCancelContractApply videoKeys:%+v, videoTranscode:%+v", videoKeys, videoTranscode)

			imgUrls := make([]string, 0)
			urlMap, err := m.GetObsTempUrl(ctx, imgKeys, 86400)
			if err != nil {
				log.ErrorWithCtx(ctx, "HandlerCancelContractApply fail to GetObsTempUrl in:%+v, err:%v", in, err)
				return out, err
			}

			for _, key := range imgKeys {
				imgUrls = append(imgUrls, urlMap[key])
			}

			// 图片审核
			err = m.SyncCensoringMix(ctx, in.GetUid(), in.GetGuildId(), in.RejectTxt, imgUrls, nil, nil, "", "")
			if err != nil {
				log.ErrorWithCtx(ctx, "HandlerCancelContractApply fail to SyncCensoringMix in:%+v, err:%v", in, err)
				if !m.newDyConf.GetContractDyConf().CensoringIgnoreResult {
					// 如果是违规，改一下错误码，避免前端直接返回到上一页
					if protocol.ToServerError(err).Code() == status.ErrContractApplySignLimitOther && strings.Contains(protocol.ToServerError(err).Message(), "违规") {
						log.ErrorWithCtx(ctx, "HandlerCancelContractApply fail to SyncCensoringMix in:%+v, err:%v", in, err)
						return out, protocol.NewExactServerError(nil, status.ErrRevenueSvrErr, protocol.ToServerError(err).Message())
					}
					return out, err
				}
			}
		}

		// 协商解约，公会长拒绝
		logInfo := &mysql.CancelContractApplyLog{
			Uid:        in.GetTargetUid(),
			GuildId:    in.GetGuildId(),
			CancelType: uint32(pb.CancelContractType_CancelContractType_Negotiate),
			Status:     uint32(pb.CancelContractStatus_CancelContractStatus_NegotiateReject),
			RejectTxt:  in.RejectTxt, // 拒绝理由
		}
		if len(in.ProofUrls) > 0 {
			logInfo.ProofUrls = strings.Join(in.ProofUrls, ",")
		}
		if len(in.ProofVideoUrls) > 0 {
			logInfo.ProofVideoUrls = strings.Join(in.ProofVideoUrls, ",")
		}
		if len(in.ProofList) > 0 {
			logInfo.ProofUrls = strings.Join(imgKeys, ",")
			logInfo.ProofVideoUrls = strings.Join(videoKeys, ",")
		}

		id := uint32(0)
		err = m.store.Transaction(ctx, func(tx *gorm.DB) error {
			e := m.store.UpdateCancelContractApply(tx, in.GetTargetUid(), in.GetGuildId(), logInfo.Status)
			if e != nil {
				log.ErrorWithCtx(ctx, "HandlerCancelContractApply fail to UpdateCancelContractApply in:%+v, err:%v", in, e)
				return e
			}
			e = m.store.UpdateCancelContractApplyLogInfo(tx, uint32(pb.CancelContractStatus_CancelContractStatus_Apply), logInfo)
			if e != nil {
				log.ErrorWithCtx(ctx, "HandlerCancelContractApply fail to UpdateCancelContractApplyLog in:%+v, err:%v", in, e)
				return e
			}

			content, _ := json.Marshal(logInfo)

			id, e = m.store.AddCancelReasonSnapshot(tx, &mysql.CancelReasonSnapshot{
				Id:      0,
				Uid:     in.GetTargetUid(),
				GuildId: in.GetGuildId(),
				Content: string(content),
			})

			if e != nil {
				log.ErrorWithCtx(ctx, "HandlerCancelContractApply fail to AddCancelReasonSnapshot in:%+v, err:%v", in, e)
				return e
			}

			return nil
		})
		if err != nil {
			log.ErrorWithCtx(ctx, "HandlerCancelContractApply fail to Transaction in:%+v, err:%v", in, err)
			return out, err
		}

		// 如果有视频，记录下转码前的对应关系
		if len(in.GetProofList()) > 0 {
			for _, v := range in.GetProofList() {
				if v.GetType() == pb.ProofContent_ProofType_Video {
					err = m.cache.RecordApplyToKey(ctx, v.Key, apply.ApplyId)
					if err != nil {
						log.ErrorWithCtx(ctx, "RecordApplyToKey fail key %s err %v", v.Key, err)
					}

					err = m.cache.RecordUidGuildIdToKey(ctx, v.Key, apply.Uid, apply.GuildId)
					if err != nil {
						log.ErrorWithCtx(ctx, "RecordUidGuildIdToKey fail key %s err %v", v.Key, err)
					}
				}
			}
		}

		if id != 0 {
			userOl, err := m.userOlCli.GetLastMobileOnlineInfo(ctx, in.GetTargetUid())
			if err != nil {
				log.ErrorWithCtx(ctx, "InviteMemberChangeWorkerType GetLastMobileOnlineInfo fail uid:%d, err: %v", in.GetTargetUid(), err)
			}

			jumpUrl := marketid_helper.Get("cancel_anchor_reject_reason", userOl.GetMarketId(), userOl.GetClientType()) + fmt.Sprintf("&unique_id=%d", id)
			guildInfo, err := m.guildCli.GetGuild(ctx, in.GetGuildId())
			if err != nil {
				log.ErrorWithCtx(ctx, "InviteMemberChangeWorkerType GetGuild fail guild:%d, err: %v", in.GetGuildId(), err)
			}
			msgContent := fmt.Sprintf("您申请的协商解约已被【%s】%d拒绝，点击查看拒绝理由>", guildInfo.GetName(), in.GetGuildId())

			_ = m.SendFuWuHaoMsg(ctx, in.GetTargetUid(), msgContent, "点击查看拒绝理由>", jumpUrl)
		}

		log.InfoWithCtx(ctx, "HandlerCancelContractApply in:%+v, reject ok", in)
		return out, nil
	}

	if in.GetHandleOpr() == uint32(pb.CANCEL_APPLY_OPR_CANCEL_APPLY_OPR_ACCEPT) {

		err = m.CancelContractByUid(ctx, in.GetUid(), in.GetTargetUid(), in.GetGuildId(), true)
		if err != nil {
			log.ErrorWithCtx(ctx, "HandlerCancelContractApply fail to CancelContractByUid in:%+v, err:%v", in, err)
			return out, err
		}
	}

	log.InfoWithCtx(ctx, "HandlerCancelContractApply in:%+v", in)
	return out, nil
}

func (m *AnchorContractMgr) CancelContractByUid(ctx context.Context, opUid, targetUid, guildId uint32, acceptCancelApply bool) error {
	log.InfoWithCtx(ctx, "CancelContractByUid  opUid %d, targetUid %d, guildId %d acceptCancelApply %v",
		opUid, targetUid, guildId, acceptCancelApply)

	now := time.Now()
	// 新提过来的需求是针对对公结算且积分处于冻结中的用户不允许发起解约。
	freezeStatusResp, err2 := m.ExchangeCli.BatchGetFreezeStatus(ctx, &Exchange.BatchGetFreezeStatusReq{UidList: []uint32{targetUid}})
	if err2 != nil {
		log.ErrorWithCtx(ctx, "CancelContractByUid BatchGetFreezeStatus fail %v, targetUid=%+v", err2, targetUid)
		return err2
	}
	guildExchangeDataResp, err := m.ExchangeGuildClient.GetUidGuildExchangeData(ctx, targetUid)
	if err != nil {
		log.ErrorWithCtx(ctx, "CancelContractByUid GetUidGuildExchangeData fail %v, targetUid=%+v", err, targetUid)
		return err
	}
	log.InfoWithCtx(ctx, "ApplyCancelContractV2 uid=%d BatchGetFreezeStatus=%+v GetUidGuildExchangeData=%+v", targetUid, freezeStatusResp, guildExchangeDataResp)
	if len(freezeStatusResp.GetFrozenUidList()) > 0 && guildExchangeDataResp.GetMasterUid() > 0 {
		log.ErrorWithCtx(ctx, "CancelContractByUid exchange limit. uid=%d", targetUid)
		return protocol.NewExactServerError(nil, status.ErrContractApplySignLimitExchange)
	}
	freezeResp, err := m.ExchangeCli.GetScorePartFreezeList(ctx, &Exchange.GetScorePartFreezeListReq{
		Uid:   targetUid,
		Limit: 10,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "CancelContractByUid GetScorePartFreezeList fail %v, targetUid=%+v", err, targetUid)
		return err
	}
	if freezeResp.GetSum().GetStatus() != 0 {
		log.ErrorWithCtx(ctx, "CancelContractByUid freeze limit. uid=%d", targetUid)
		return protocol.NewExactServerError(nil, status.ErrContractApplySignLimitExchange)
	}

	_, isMultiPlayer, err := m.store.CancelContractByUid(ctx, opUid, targetUid, guildId, uint32(pb.CONTRACT_CHANGE_TYPE_ENUM_CONTRACT_CHANGE_DEL), "Cancel Contract By Uid")
	if err != nil {
		log.ErrorWithCtx(ctx, "CancelContractByUid fail to CancelContractByUid opUid:%+v, targetUid:%+v, guildId:%+v, err:%v", opUid, targetUid, guildId, err)
		return err
	}

	gid := guildId
	msg := ""
	guildResp, serr := m.guildCli.GetGuild(ctx, guildId)
	if serr != nil {
		log.ErrorWithCtx(ctx, "CancelContractByUid fail to GetGuild opUid:%+v, targetUid:%+v, guildId:%+v, err:%+v", opUid, targetUid, guildId, serr)

	} else {
		if guildResp.GetShortId() > 0 {
			gid = guildResp.GetShortId()
		}
	}

	if opUid == 0 {
		msg = fmt.Sprintf("你所签约的公会【%s】(ID:%d) 于 %s 被平台解除合作关系，你与公会双方的签约关系即时失效，次日零点后可以签约新的公会。",
			guildResp.GetName(), gid, now.Format(util.DayTimeFormat))

	} else if acceptCancelApply {
		msg = fmt.Sprintf("你与【%s】(ID:%d) 的解约申请已被处理，双方于 %s 正式解约，次日零点后可以签约新的公会。",
			guildResp.GetName(), gid, now.Format(util.DayTimeFormat))

	} else {
		msg = fmt.Sprintf("你已被【%s】(ID:%d) 解约。双方于 %s 正式解约，次日零点后可以签约新的公会。",
			guildResp.GetName(), gid, now.Format(util.DayTimeFormat))
	}

	// tt助手消息
	_ = m.SendIMMsg(ctx, targetUid, msg)

	// 数据上报
	m.ContractDataCenterReport(ctx, targetUid, guildId, 0)

	if isMultiPlayer {
		go func(uid uint32) {
			ctx2, cancel := context.WithTimeout(context.Background(), time.Second*3)
			defer cancel()
			_, err := m.apiCenterCli.FollowPublic(ctx2, &apicenterPB.FollowPublicReq{
				Uid:        uid,
				PublicType: apicenterPB.FollowPublicReq_SYSTEM, // 系统公众号
				BindedId:   90005,                              // 公众号id
				IsFollow:   false,                              // 取关
			})
			log.Infof("CancelContractByUid FollowPublic uid %d, err %v", uid, err)
		}(targetUid)
	}

	// 删除缓存
	err = m.cache.DelUserContractInfoV2(targetUid)
	if err != nil {
		log.ErrorWithCtx(ctx, "CancelContractByUid fail to DelUserContractInfoV2 opUid:%+v, targetUid:%+v, guildId:%+v, err:%+v", opUid, targetUid, guildId, err)
	}

	_ = m.cache.DelGuildEsportScore(guildId, targetUid, uint32(time.Now().Unix()))

	err = m.cache.DelLockPayCancelAmount(ctx, guildId, targetUid)
	if err != nil {
		log.ErrorWithCtx(ctx, "CancelContractByUid fail to DelLockPayCancelAmount opUid:%+v, targetUid:%+v, guildId:%+v, err:%+v", opUid, guildId, targetUid, err)
	}

	// kafka事件
	m.KafkaProduceAnchorContractEvent(ctx, uint32(kafkaanchorcontract.EVENT_TYPE_EVENT_CANCEL_CONTRACT), targetUid, guildId, now)

	log.InfoWithCtx(ctx, "CancelContractByUid opUid:%+v, targetUid:%+v, guildId:%+v, acceptCancelApply %v msg %s",
		opUid, targetUid, guildId, acceptCancelApply, msg)
	return nil
}

func (m *AnchorContractMgr) ClearTimeOutContract(ctx context.Context) {

	defer func() {
		if err := recover(); err != nil {
			log.Errorf("ClearTimeOutContractHandle panic err:%v", err)
		}
	}()

	lockKey := "clear_timeout_contract_lock"
	if !m.cache.Lock(lockKey, 30*time.Second) {
		return
	}
	defer m.cache.UnLock(lockKey)

	now := time.Now()
	contractList, err := m.store.GetExpireContract(now)
	if err != nil {
		log.ErrorWithCtx(ctx, "ClearTimeOutContract fail to GetExpireContract err:%v", err)
		return
	}

	for _, contract := range contractList {
		targetUid := contract.Uid
		guildId := contract.GuildId

		_, isMultiPlayer, err := m.store.CancelContractByUid(ctx, 0, targetUid, guildId, uint32(pb.CONTRACT_CHANGE_TYPE_ENUM_CONTRACT_CHANGE_TIMEOUT), "expire,auto del")
		if err != nil {
			log.ErrorWithCtx(ctx, "ClearTimeOutContract fail to CancelContractByUid targetUid:%+v, guildId:%+v, err:%v", targetUid, guildId, err)
			continue
		}

		gid := guildId
		guildResp, serr := m.guildCli.GetGuild(ctx, guildId)
		if serr != nil {
			log.ErrorWithCtx(ctx, "ClearTimeOutContract fail to GetGuild targetUid:%+v, guildId:%+v, err:%+v", targetUid, guildId, serr)
		} else {
			if guildResp.GetShortId() > 0 {
				gid = guildResp.GetShortId()
			}
		}

		// 发给用户
		msg := fmt.Sprintf("你与【%s】(ID:%d) 的签约协议已到期，双方解除签约关系即时生效，次日零点后可以签约新的公会。",
			guildResp.GetName(), gid)
		_ = m.SendIMMsg(ctx, targetUid, msg)

		userResp, serr := m.accountCli.GetUser(ctx, targetUid)
		if serr != nil {
			log.ErrorWithCtx(ctx, "ClearTimeOutContract fail to GetGuild targetUid:%+v, guildId:%+v, err:%+v", targetUid, guildId, serr)
		} else {
			// 发给会长
			msg = fmt.Sprintf("%s(TTID:%s) 与公会签约关系到期，双方解除签约关系。", userResp.GetNickname(), userResp.GetAlias())
			_ = m.SendIMMsg(ctx, guildResp.GetOwner(), msg)
		}

		// 数据上报
		m.ContractDataCenterReport(ctx, targetUid, guildId, 0)

		if isMultiPlayer {
			ctx2, cancel := context.WithTimeout(context.Background(), time.Second*3)
			defer cancel()
			_, err := m.apiCenterCli.FollowPublic(ctx2, &apicenterPB.FollowPublicReq{
				Uid:        targetUid,
				PublicType: apicenterPB.FollowPublicReq_SYSTEM, // 系统公众号
				BindedId:   90005,                              // 公众号id
				IsFollow:   false,                              // 取关
			})
			log.Infof("ClearTimeOutContract FollowPublic uid %d, err %v", targetUid, err)
		}

		// 删除缓存
		err = m.cache.DelUserContractInfoV2(targetUid)
		if err != nil {
			log.ErrorWithCtx(ctx, "ClearTimeOutContract fail to DelUserContractInfoV2 targetUid:%+v, guildId:%+v, err:%+v", targetUid, guildId, err)
		}
		// kafka事件
		m.KafkaProduceAnchorContractEvent(ctx, uint32(kafkaanchorcontract.EVENT_TYPE_EVENT_CANCEL_CONTRACT), targetUid, guildId, now)
		log.InfoWithCtx(ctx, "ClearTimeOutContract CancelContractByUid targetUid:%+v, guildId:%+v isMultiPlayer:%v", targetUid, guildId, isMultiPlayer)
		time.Sleep(10 * time.Millisecond)
	}
}

func (m *AnchorContractMgr) CancelContractWhenApplyTimeOut(ctx context.Context, idx uint32, expireTime time.Time, isNegotiate bool, isPayAuto bool) {
	list, err := m.store.GetTimeOutCancelContractApplyList(idx, expireTime)
	if err != nil {
		log.ErrorWithCtx(ctx, "CancelContractWhenApplyTimeOut fail to GetTimeOutCancelContractApplyList idx:%+v, err:%+v", idx, err)
		return
	}

	for _, info := range list {
		if (info.CancelType == uint32(pb.CancelContractType_CancelContractType_Negotiate)) != isNegotiate {
			log.InfoWithCtx(ctx, "CancelContractWhenApplyTimeOut skip info:%+v", info)
			continue
		}

		if (info.CancelType == uint32(pb.CancelContractType_CancelContractType_Pay) && info.Status == uint32(pb.CancelContractStatus_CancelContractStatus_PayOfficeAccepted)) != isPayAuto {
			log.InfoWithCtx(ctx, "CancelContractWhenApplyTimeOut skip info:%+v", info)
			continue
		}

		err = m.CancelContractByUid(ctx, 1, info.Uid, info.GuildId, false)
		if err != nil {
			log.ErrorWithCtx(ctx, "CancelContractWhenApplyTimeOut fail to GetTimeOutCancelContractApplyList info:%+v, err:%+v", info, err)
			if sErr, ok := err.(protocol.ServerError); ok && sErr.Code() == status.ErrContractApplySignLimitExchange {
				_ = m.AbortCancelContractApply(ctx, info.Uid, info.GuildId, info.CancelType, 0, "积分冻结状态，请联系客服或80513按规定解除冻结后再尝试申请")
			}
			continue
		}

		log.InfoWithCtx(ctx, "CancelContractWhenApplyTimeOut info:%+v", info)
	}
}

func (m *AnchorContractMgr) ApplyCancelContractV2(ctx context.Context, in *pb.ApplyCancelContractV2Req) (*pb.ApplyCancelContractResp, error) {
	out := &pb.ApplyCancelContractResp{}
	uid := in.Uid
	guildId := in.GuildId
	log.InfoWithCtx(ctx, "ApplyCancelContractV2 begin %+v", in)
	if guildId == 0 || uid == 0 {
		return out, protocol.NewServerError(status.ErrRequestParamInvalid)
	}

	// 合约
	contract, exist, err := m.store.GetValidContractWithUid(uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "ApplyCancelContractV2 GetValidContractWithUid fail %v, in=%+v", err, in)
		return out, err
	}
	log.InfoWithCtx(ctx, "ApplyCancelContractV2 uid=%d GetValidContractWithUid=%+v", uid, contract)
	if !exist {
		log.ErrorWithCtx(ctx, "ApplyCancelContractV2 fail no contract. in=%+v", in)
		return out, protocol.NewExactServerError(nil, status.ErrContractNonexist)
	}
	if contract.GuildId != guildId {
		log.ErrorWithCtx(ctx, "ApplyCancelContractV2 fail contract guild_id invalid. in=%+v, contract=%+v", in, contract)
		return out, protocol.NewExactServerError(nil, status.ErrContractNonexist)
	}

	// 有对应身份=公会在对应合作库
	// 身份
	identityList, err := m.store.GetAnchorIdentity(nil, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "ApplyCancelContractV2 GetAnchorIdentity fail %v, in=%+v", err, in)
		return out, err
	}
	if len(identityList) == 0 {
		log.ErrorWithCtx(ctx, "ApplyCancelContractV2 len(identityList) == 0. in=%+v", in)
		return out, protocol.NewExactServerError(nil, status.ErrContractNonexist)
	}

	// 是否已发起申请
	exist, _, err = m.store.GetCancelContractApply(uid, guildId)
	if err != nil {
		log.ErrorWithCtx(ctx, "ApplyCancelContractV2 GetCancelContractApply fail %v, in=%+v", err, in)
		return out, err
	}
	if exist {
		log.ErrorWithCtx(ctx, "ApplyCancelContractV2 x exist. in=%+v", in)
		return out, protocol.NewExactServerError(nil, -1062)
	}

	//  如果账号是经纪人或者管理不可解约 不检查这个了
	//  如果账号是经纪人或者管理不可解约
	if m.newDyConf.GetContractDyConf().IsCheckAgent {
		agentResp, err := m.GuildManagementSvrCli.GetAgentGuild(ctx, uid)
		if err != nil {
			log.ErrorWithCtx(ctx, "ApplyCancelContractV2 GetAgentGuild fail %v, in=%+v", err, in)
			return out, err
		}
		log.InfoWithCtx(ctx, "ApplyCancelContractV2 uid=%d GetAgentGuild=%+v", uid, agentResp)
		agentType := agentResp.GetInfo().GetAgentType()
		if agentResp.GetInfo().GetGuild_Id() == guildId &&
			((agentType&uint32(guildmanagementpb.AgentType_AgentBroker) == uint32(guildmanagementpb.AgentType_AgentBroker)) ||
				(agentType&uint32(guildmanagementpb.AgentType_AgentAdmin) == uint32(guildmanagementpb.AgentType_AgentAdmin))) {
			log.ErrorWithCtx(ctx, "ApplyCancelContractV2 guild agent limit. uid=%d agentType=%d", uid, agentType)
			return out, protocol.NewExactServerError(nil, status.ErrContractApplySignLimitAgent)
		}
	}

	// 新提过来的需求是针对对公结算且积分处于冻结中的用户不允许发起解约。
	freezeStatusResp, err := m.ExchangeCli.BatchGetFreezeStatus(ctx, &Exchange.BatchGetFreezeStatusReq{UidList: []uint32{uid}})
	if err != nil {
		log.ErrorWithCtx(ctx, "ApplyCancelContractV2 BatchGetFreezeStatus fail %v, in=%+v", err, in)
		return out, err
	}
	guildExchangeDataResp, err := m.ExchangeGuildClient.GetUidGuildExchangeData(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "ApplyCancelContractV2 GetUidGuildExchangeData fail %v, in=%+v", err, in)
		return out, err
	}
	log.InfoWithCtx(ctx, "ApplyCancelContractV2 uid=%d BatchGetFreezeStatus=%+v GetUidGuildExchangeData=%+v", uid, freezeStatusResp, guildExchangeDataResp)
	if len(freezeStatusResp.GetFrozenUidList()) > 0 && guildExchangeDataResp.GetMasterUid() > 0 {
		log.ErrorWithCtx(ctx, "ApplyCancelContractV2 exchange limit. uid=%d", uid)
		return out, protocol.NewExactServerError(nil, status.ErrContractApplySignLimitExchange)
	}
	freezeResp, err := m.ExchangeCli.GetScorePartFreezeList(ctx, &Exchange.GetScorePartFreezeListReq{
		Uid:   uid,
		Limit: 10,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "ApplyCancelContractV2 GetScorePartFreezeList fail %v, in=%+v", err, in)
		return out, err
	}
	if freezeResp.GetSum().GetStatus() != 0 {
		log.ErrorWithCtx(ctx, "ApplyCancelContractV2 freeze limit. uid=%d", uid)
		return out, protocol.NewExactServerError(nil, status.ErrContractApplySignLimitExchange)
	}

	identity2Info := map[pb.SIGN_ANCHOR_IDENTITY]*mysql.AnchorIdentity{}
	identityInfo := &mysql.IdentityInfo{}
	// 补充信息
	cancelApplyLog := &mysql.CancelContractApplyLog{
		Uid:                in.GetUid(),
		GuildId:            in.GetGuildId(),
		SignTime:           contract.SignTime,
		ContractExpireTime: contract.ContractExpireTime,
		Status:             0,
		//Reason:              in.GetCancelReason(),
		ApplyTime:           time.Now(),
		LiveObtainTime:      time.Now(),
		MultiplayObtainTime: time.Now(),
		UpdateTime:          time.Now(),
	}
	for _, ident := range identityList {
		identityInfo.AnchorIdentityInfoList = append(identityInfo.AnchorIdentityInfoList, &mysql.AnchorIdentityInfo{
			IdentityType: ident.IdentityType,
			ObtainTime:   uint32(ident.ObtainTime.Unix()),
			AgentUid:     ident.AgentUid,
		})
		if ident.IdentityType == uint32(pb.SIGN_ANCHOR_IDENTITY_SIGN_ANCHOR_IDENTITY_RADIO_LIVE) {
			cancelApplyLog.LiveIdentityStatus = 1
			cancelApplyLog.LiveObtainTime = ident.ObtainTime
			cancelApplyLog.AgentUid = ident.AgentUid
		} else if ident.IdentityType == uint32(pb.SIGN_ANCHOR_IDENTITY_SIGN_ANCHOR_IDENTITY_MULTIPLAYER) {
			cancelApplyLog.MultiplayIdentityStatus = 1
			cancelApplyLog.MultiplayObtainTime = ident.ObtainTime
		} else {
			log.Debugf("")
			//log.Errorf("ApplyCancelContract invalid type :%v info %+v", info.IdentityType, info)
		}

		identity2Info[pb.SIGN_ANCHOR_IDENTITY(ident.IdentityType)] = ident
		log.InfoWithCtx(ctx, "ApplyCancelContractV2 uid=%d AnchorIdentity=%+v", uid, ident)
	}

	// 电竞不可主动解约
	if identity2Info[pb.SIGN_ANCHOR_IDENTITY_SIGN_ANCHOR_IDENTITY_E_SPORTS] != nil {
		log.ErrorWithCtx(ctx, "ApplyCancelContractV2 esport limit. uid=%d", uid)
		return out, protocol.NewExactServerError(nil, status.ErrContractApplySignLimitEsport)
	}

	reasonList := []uint32{}

	// 有直播权限自动获得主播身份、回收直播权限=回收主播身份
	// 主播
	if identity2Info[pb.SIGN_ANCHOR_IDENTITY_SIGN_ANCHOR_IDENTITY_RADIO_LIVE] != nil {
		reasons, err := m.checkAnchorCancelContract(ctx, uid, guildId,
			uint32(identity2Info[pb.SIGN_ANCHOR_IDENTITY_SIGN_ANCHOR_IDENTITY_RADIO_LIVE].ObtainTime.Unix()))
		if err != nil {
			log.ErrorWithCtx(ctx, "ApplyCancelContractV2 checkAnchorCancelContract fail %v, in=%+v", err, in)
			return out, err
		}
		log.InfoWithCtx(ctx, "ApplyCancelContractV2 uid=%d checkAnchorCancelContract=%v", uid, reasons)
		if len(reasons) == 0 {
			log.ErrorWithCtx(ctx, "ApplyCancelContractV2 anchor limit. uid=%d", uid)
			return out, protocol.NewExactServerError(nil, status.ErrContractApplySignLimitOther)
		}
		reasonList = append(reasonList, reasons...)
	}

	// 多人
	if identity2Info[pb.SIGN_ANCHOR_IDENTITY_SIGN_ANCHOR_IDENTITY_MULTIPLAYER] != nil {
		obtime := uint32(identity2Info[pb.SIGN_ANCHOR_IDENTITY_SIGN_ANCHOR_IDENTITY_MULTIPLAYER].ObtainTime.Unix())
		reasons, err := m.checkMultiPlayerCancelContract(ctx, uid, guildId, obtime)
		if err != nil {
			log.ErrorWithCtx(ctx, "ApplyCancelContractV2 checkMultiPlayerCancelContract fail %v, in=%+v", err, in)
			return out, err
		}
		log.InfoWithCtx(ctx, "ApplyCancelContractV2 uid=%d checkMultiPlayerCancelContract=%v", uid, reasons)
		if len(reasons) == 0 {
			log.ErrorWithCtx(ctx, "ApplyCancelContractV2 multiplayer limit. uid=%d", uid)
			return out, protocol.NewExactServerError(nil, status.ErrContractApplySignLimitOther)
		}
		reasonList = append(reasonList, reasons...)
	}

	if len(reasonList) == 0 {
		log.ErrorWithCtx(ctx, "ApplyCancelContractV2 final limit. uid=%d", uid)
		return out, protocol.NewExactServerError(nil, status.ErrContractApplySignLimitOther)
	}

	if in.IsCheck {
		log.InfoWithCtx(ctx, "ApplyCancelContractV2 is check. uid=%d", uid)
		return out, nil
	}

	if cancelApplyLog.LiveIdentityStatus == 1 {
		liveAnchorInfo, err := m.liveMgrCli.GetAnchorByUidList(ctx, &channellivemgr.GetAnchorByUidListReq{UidList: []uint32{in.GetUid()}})
		if err != nil {
			log.ErrorWithCtx(ctx, "ApplyCancelContractV2 fail to GetAnchorByUidList in:%+v, err:%v", in, err)
			return out, err
		}
		if len(liveAnchorInfo.AnchorList) > 0 {
			cancelApplyLog.TagId = liveAnchorInfo.AnchorList[0].TagId
		}
	}
	identityInfo.ReasonList = reasonList
	identityInfoStr, _ := json.Marshal(identityInfo)
	cancelApplyLog.IdentityInfo = string(identityInfoStr)
	err = m.store.Transaction(ctx, func(tx *gorm.DB) error {
		err := m.store.AddCancelContractApply(tx, &mysql.CancelContractApply{
			Uid:        in.GetUid(),
			GuildId:    in.GetGuildId(),
			GuildName:  "", //in.GetGuildName(),
			ApplyTime:  time.Now(),
			CancelType: uint32(pb.CancelContractType_CancelContractType_Older),
			Reason:     reasonList[0],
		})
		if err != nil {
			log.ErrorWithCtx(ctx, "ApplyCancelContractV2 fail to AddCancelContractApply in:%+v, err:%v", in, err)
			return err
		}
		err = m.store.AddCancelContractApplyLog(tx, cancelApplyLog)
		if err != nil {
			log.ErrorWithCtx(ctx, "ApplyCancelContractV2 fail to AddCancelContractApplyLog in:%+v, err:%v", in, err)
			return err
		}
		return nil
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "ApplyCancelContractV2 fail to Transaction in:%+v, err:%v", in, err)
		return out, err
	}

	log.InfoWithCtx(ctx, "ApplyCancelContractV2 end. uid=%d guildid=%d reasonList=%v", uid, guildId, reasonList)
	return out, nil
}

func (m *AnchorContractMgr) checkAnchorCancelContract(ctx context.Context, uid, guildId, obtainTime uint32) ([]uint32, error) {
	now := time.Now()
	condition := m.dyConfig.GetCancalContractCondition()
	obtainTm := time.Unix(int64(obtainTime), 0)
	monthDiff := util.MonthsDiff(now, obtainTm)
	if monthDiff < condition.LiveAnchorSignMonthLimit {
		log.Errorf("ApplyCancelContractV2 checkAnchorCancelContract live anchor no %d month vs (monthDiff=%d). uid=%d, obtainTm=%s", condition.LiveAnchorSignMonthLimit, monthDiff, uid, obtainTm)
		return nil, nil
	}

	liveAnchorIncomeRMB := condition.LiveAnchorIncomeRMB
	liveAnchorActiveDay := condition.LiveAnchorActiveDay
	log.Infof("checkAnchorCancelContract uid=%d liveAnchorIncomeRMB=%d liveAnchorActiveDay=%d", uid, liveAnchorIncomeRMB, liveAnchorActiveDay)

	/*
		- 签约账号在当前签约公会获得主播身份后，签约期间近六个月（不包含当月）连续单月在自己直播间收礼积分收益≤1000 元
		- 签约账号在当前签约公会获得主播身份后，签约期间近六个月（不包含当月）连续单月直播活跃天数≤15天
	*/
	nowMonth := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, time.Local)
	for i := 1; i <= int(condition.LiveAnchorSignMonthLimit); i++ {
		month := nowMonth.AddDate(0, -i, 0)
		stats, err := m.liveStatsCli.GetAnchorMonthlyStats(ctx, 0, uid, guildId, uint32(month.Unix()))
		if err != nil {
			log.ErrorWithCtx(ctx, "ApplyCancelContractV2 checkAnchorCancelContract GetAnchorMonthlyStats fail %v, uid=%d", err, uid)
			return nil, err
		}
		log.InfoWithCtx(ctx, "ApplyCancelContractV2 checkAnchorCancelContract uid=%d month=%s GetAnchorMonthlyStats=%+v", uid, month, stats)

		// GetAnchorIncome=T豆
		income := stats.GetStats().GetAnchorIncome() / 2
		activeDay := stats.GetStats().GetLiveActiveDays()

		// 1000RMB,15DAY
		if income > liveAnchorIncomeRMB*100 || activeDay > liveAnchorActiveDay {
			log.ErrorWithCtx(ctx, "ApplyCancelContractV2 checkAnchorCancelContract uid=%d limit. month=%s income=%d activeDay=%d", uid, month, income, activeDay)
			return nil, nil
		}
	}
	return []uint32{uint32(pb.CANCEL_REASON_TYPE_CANCEL_REASON_TYPE_LIVE_INCOME_ACTIVE)}, nil
}

func (m *AnchorContractMgr) checkMultiPlayerCancelContract(ctx context.Context, uid, guildId, obtainTime uint32) ([]uint32, error) {
	now := time.Now()
	if now.Unix()-int64(obtainTime) < 7*24*3600 {
		log.InfoWithCtx(ctx, "ApplyCancelContractV2 checkMultiPlayerCancelContract day7 uid=%d obtainTime=%d", uid, obtainTime)
		return []uint32{uint32(pb.CANCEL_REASON_TYPE_CANCEL_REASON_TYPE_DAY7_NOREASON)}, nil
	}

	obtainTm := time.Unix(int64(obtainTime), 0)
	monthDiff := util.MonthsDiff(now, obtainTm)
	if monthDiff < 2 {
		log.Errorf("ApplyCancelContractV2 checkMultiPlayerCancelContract multiplayers no 2 month vs (monthDiff=%d). uid=%d, obtainTm=%s", monthDiff, uid, obtainTm)
		return nil, protocol.NewExactServerError(nil, status.ErrContractApplySignLimitOther)
	}

	reasonList := []uint32{}
	nowMonth := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, time.Local)

	condition := m.dyConfig.GetCancalContractCondition()
	multiPlayerPersonIncomeRMB := condition.MultiPlayerPersonIncomeRMB
	multiPlayerGuildChannelFeeRMB := condition.MultiPlayerGuildChannelFeeRMB
	multiPlayerGuildChannelFeeCheckSwitch := condition.MultiPlayerGuildChannelFeeCheckSwitch
	log.Infof("checkMultiPlayerCancelContract uid=%d multiPlayerPersonIncomeRMB=%d multiPlayerGuildChannelFeeRMB=%d", uid, multiPlayerPersonIncomeRMB, multiPlayerGuildChannelFeeRMB)

	// 个人收益不达标=签约账号获得多人互动身份后，签约期间近两个月（不包含当月）单月收益≤1000 元（在签约关系公会及该公会关联的子母公会旗下任意经营房间产生的历史积分）
	var isPersonIncomeReason = true
	for i := 1; i <= 2; i++ {
		month := nowMonth.AddDate(0, -i, 0)

		// 获取子母公会组
		guildIdList := m.dyConfig.GetParentChildGroup(guildId)
		var income int64

		for _, id := range guildIdList {
			valmap, err := m.store.BatchGetGuildUserMonthScore(id, []uint32{uid}, month)
			if err != nil {
				log.ErrorWithCtx(ctx, "checkMultiPlayerCancelContract BatchGetGuildUserMonthScore fail %v, uid=%d", err, uid)
				return nil, err
			}
			income += valmap[uid]
		}

		log.InfoWithCtx(ctx, "ApplyCancelContractV2 checkMultiPlayerCancelContract uid=%d guildId=%d month=%s BatchGetGuildUserMonthScore=%d guildIdList:%v",
			uid, guildId, month, income, guildIdList)

		// 1000RMB
		if income > int64(multiPlayerPersonIncomeRMB*100) {
			isPersonIncomeReason = false
			log.ErrorWithCtx(ctx, "ApplyCancelContractV2 checkMultiPlayerCancelContract uid=%d preson_income limit. month=%s income=%d ", uid, month, income)
		}
	}
	if isPersonIncomeReason {
		reasonList = append(reasonList, uint32(pb.CANCEL_REASON_TYPE_CANCEL_REASON_TYPE_PERSON_INCOME))
	}

	// 公会房间流水不达标=签约账号获得多人互动身份后，签约期间公会旗下公开娱乐房近两个月（不包含当月）房间总流水≤3000元
	var isGuildFeeReason = true
	for i := 1; i <= 2; i++ {
		month := nowMonth.AddDate(0, -i, 0)

		// 公会房间流水
		yearmonth := month.Year()*100 + int(month.Month())
		exist, fee, err := m.store.GetGuildChannelFee(guildId, uint32(yearmonth))
		if err != nil {
			log.ErrorWithCtx(ctx, "checkMultiPlayerCancelContract GetGuildChannelFee fail %v, uid=%d", err, uid)
			return nil, err
		}

		// 不存在1月份数据，临时返回错误，等待数据汇总
		if yearmonth == 202401 && !exist && multiPlayerGuildChannelFeeCheckSwitch {
			log.ErrorWithCtx(ctx, "checkMultiPlayerCancelContract 202401 not exist. uid=%d guildid=%d", uid, guildId)
			return nil, protocol.NewExactServerError(nil, status.ErrRepositoryFailed)
		}

		log.InfoWithCtx(ctx, "ApplyCancelContractV2 checkMultiPlayerCancelContract uid=%d guildId=%d month=%s GetGuildChannelFee=%d",
			uid, guildId, month, fee)
		// 3000RMB
		if fee > int64(multiPlayerGuildChannelFeeRMB*100) {
			isGuildFeeReason = false
			log.ErrorWithCtx(ctx, "ApplyCancelContractV2 checkMultiPlayerCancelContract uid=%d guild_fee limit. month=%s guildId=%d fee=%d", uid, month, guildId, fee)
		}
	}
	if isGuildFeeReason {
		reasonList = append(reasonList, uint32(pb.CANCEL_REASON_TYPE_CANCEL_REASON_TYPE_GUILD_INCOME))
	}

	return reasonList, nil
}

func (m *AnchorContractMgr) CheckCanApplyCancelContract(ctx context.Context, in *pb.CheckCanApplyCancelContractReq) (*pb.CheckCanApplyCancelContractResp, error) {
	out := &pb.CheckCanApplyCancelContractResp{}
	uid := in.Uid
	guildId := in.GuildId
	log.InfoWithCtx(ctx, "CheckCanApplyCancelContract begin %+v", in)
	if guildId == 0 || uid == 0 {
		return out, protocol.NewServerError(status.ErrRequestParamInvalid)
	}

	// 合约
	contract, exist, err := m.store.GetValidContractWithUid(uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "CheckCanApplyCancelContract GetValidContractWithUid fail %v, in=%+v", err, in)
		return out, err
	}
	log.InfoWithCtx(ctx, "CheckCanApplyCancelContract uid=%d GetValidContractWithUid=%+v", uid, contract)
	if !exist {
		log.ErrorWithCtx(ctx, "CheckCanApplyCancelContract fail no contract. in=%+v", in)
		return out, protocol.NewExactServerError(nil, status.ErrContractNonexist)
	}
	if contract.GuildId != guildId {
		log.ErrorWithCtx(ctx, "CheckCanApplyCancelContract fail contract guild_id invalid. in=%+v, contract=%+v", in, contract)
		return out, protocol.NewExactServerError(nil, status.ErrContractNonexist)
	}

	// 有对应身份=公会在对应合作库
	// 身份
	identityList, err := m.store.GetAnchorIdentity(nil, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "CheckCanApplyCancelContract GetAnchorIdentity fail %v, in=%+v", err, in)
		return out, err
	}
	if len(identityList) == 0 {
		log.ErrorWithCtx(ctx, "CheckCanApplyCancelContract len(identityList) == 0. in=%+v", in)
		return out, protocol.NewExactServerError(nil, status.ErrContractNonexist)
	}

	identity2Info := map[pb.SIGN_ANCHOR_IDENTITY]*mysql.AnchorIdentity{}
	for _, ident := range identityList {
		identity2Info[pb.SIGN_ANCHOR_IDENTITY(ident.IdentityType)] = ident
		log.InfoWithCtx(ctx, "CheckCanApplyCancelContract uid=%d AnchorIdentity=%+v", uid, ident)
	}

	// 电竞不可主动解约
	if identity2Info[pb.SIGN_ANCHOR_IDENTITY_SIGN_ANCHOR_IDENTITY_E_SPORTS] != nil {
		log.ErrorWithCtx(ctx, "CheckCanApplyCancelContract esport limit. uid=%d", uid)
		return out, protocol.NewExactServerError(nil, status.ErrContractApplySignLimitEsport)
	}

	reasonList := []uint32{}

	// 有直播权限自动获得主播身份、回收直播权限=回收主播身份
	// 主播
	if identity2Info[pb.SIGN_ANCHOR_IDENTITY_SIGN_ANCHOR_IDENTITY_RADIO_LIVE] != nil {
		reasons, err := m.checkAnchorCancelContract(ctx, uid, guildId,
			uint32(identity2Info[pb.SIGN_ANCHOR_IDENTITY_SIGN_ANCHOR_IDENTITY_RADIO_LIVE].ObtainTime.Unix()))
		if err != nil {
			log.ErrorWithCtx(ctx, "CheckCanApplyCancelContract checkAnchorCancelContract fail %v, in=%+v", err, in)
			return out, err
		}
		log.InfoWithCtx(ctx, "CheckCanApplyCancelContract uid=%d checkAnchorCancelContract=%v", uid, reasons)
		if len(reasons) == 0 {
			log.ErrorWithCtx(ctx, "CheckCanApplyCancelContract anchor limit. uid=%d", uid)
			return out, protocol.NewExactServerError(nil, status.ErrContractApplySignLimitOther)
		}
		reasonList = append(reasonList, reasons...)
	}

	// 多人
	if identity2Info[pb.SIGN_ANCHOR_IDENTITY_SIGN_ANCHOR_IDENTITY_MULTIPLAYER] != nil {
		obtime := uint32(identity2Info[pb.SIGN_ANCHOR_IDENTITY_SIGN_ANCHOR_IDENTITY_MULTIPLAYER].ObtainTime.Unix())
		reasons, err := m.checkMultiPlayerCancelContract(ctx, uid, guildId, obtime)
		if err != nil {
			log.ErrorWithCtx(ctx, "CheckCanApplyCancelContract checkMultiPlayerCancelContract fail %v, in=%+v", err, in)
			return out, err
		}
		log.InfoWithCtx(ctx, "CheckCanApplyCancelContract uid=%d checkMultiPlayerCancelContract=%v", uid, reasons)
		if len(reasons) == 0 {
			log.ErrorWithCtx(ctx, "CheckCanApplyCancelContract multiplayer limit. uid=%d", uid)
			return out, protocol.NewExactServerError(nil, status.ErrContractApplySignLimitOther)
		}
		reasonList = append(reasonList, reasons...)
	}

	if len(reasonList) == 0 {
		log.ErrorWithCtx(ctx, "CheckCanApplyCancelContract final limit. uid=%d", uid)
		return out, protocol.NewExactServerError(nil, status.ErrContractApplySignLimitOther)
	}

	log.InfoWithCtx(ctx, "CheckCanApplyCancelContract uid=%d guildid=%d reasonList=%v", uid, guildId, reasonList)
	out.ReasonList = reasonList
	return out, nil
}
