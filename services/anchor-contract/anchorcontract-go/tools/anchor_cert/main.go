package main

import (
	"context"
	"crypto/tls"
	"encoding/json"
	"flag"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/go-gomail/gomail"
	"github.com/tealeg/xlsx"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/urrc"
	"golang.52tt.com/protocol/common/status"
	pb "golang.52tt.com/protocol/services/anchorcontract-go"
	"golang.52tt.com/services/anchor-contract/anchorcontract-go/conf"
	"golang.52tt.com/services/anchor-contract/anchorcontract-go/manager"
	"golang.52tt.com/services/anchor-contract/anchorcontract-go/mysql"
)

var (
	confpath                   = flag.String("conf", "", "conf")
	uidstr                     = flag.String("uids", "", "uids")
	settleMonth                = flag.Int("settleMonth", 0, "settleMonth")
	settleMusic                = flag.Bool("settleMusic", false, "settleMusic")
	settleEmotionStory         = flag.Bool("settleEmotionStory", false, "settleEmotionStory")
	settleTwoDimension         = flag.Bool("settleTwoDimension", false, "settleTwoDimension")
	downTwoDimension           = flag.Bool("downTwoDimension", false, "downTwoDimension")
	downMusic                  = flag.Bool("downMusic", false, "downMusic")
	settleAnchorCertMusicTimer = flag.Bool("settleAnchorCertMusicTimer", false, "settleAnchorCertMusicTimer")
	//

	ItemName = flag.String("item_name", "", "item_name")

	isMusic        = flag.Bool("music", false, "music")
	isEmotion      = flag.Bool("emotion", false, "emotion")
	isTwoDimension = flag.Bool("twodimension", false, "twodimension")
	toMails        = flag.String("toMail", "<EMAIL>", "toMail")

	isDryRun = flag.Bool("isDryRun", false, "isDryRun")

	toMailList = []string{}

	branch    string
	sha1ver   string // sha1 revision used to build the program
	buildTime string // when the executable was built
)

func init() {
	log.SetLevel(log.DebugLevel)
	fmt.Printf("branch=%q\n", branch)
	fmt.Printf("sha1ver=%q\n", sha1ver)
	fmt.Printf("buildTime=%q\n", buildTime)
}

func main() {

	flag.Parse()

	fmt.Printf("confpath=%s\n", *confpath)
	fmt.Printf("uidstr=%s\n", *uidstr)
	fmt.Printf("ItemName=%s\n", *ItemName)
	fmt.Printf("isMusic=%v\n", *isMusic)
	fmt.Printf("isEmotion=%v\n", *isEmotion)
	fmt.Printf("isTwoDimension=%v\n", *isEmotion)
	fmt.Printf("toMails=%q\n", *toMails)
	fmt.Printf("settleMonth=%d\n", *settleMonth)
	fmt.Printf("settleMusic=%v\n", *settleMusic)
	fmt.Printf("settleEmotionStory=%v\n", *settleEmotionStory)
	fmt.Printf("settleTwoDimension=%v\n", *settleTwoDimension)
	fmt.Printf("downTwoDimension=%v\n", *downTwoDimension)
	fmt.Printf("downMusic=%v\n", *downMusic)
	fmt.Printf("settleAnchorCertMusicTimer=%v\n", *settleAnchorCertMusicTimer)
	fmt.Printf("isDryRun=%v\n", *isDryRun)

	toMailList = strings.Split(*toMails, ",")

	sc := &conf.ServiceConfigT{}
	err := sc.Parse(*confpath)
	if err != nil {
		log.Errorf("config Parse fail err:%v", err)
		return
	}

	log.Infof("ServiceConfigT=%+v\n", sc)
	m, err := manager.NewAnchorContractMgrTest(sc)
	if err != nil {
		log.Errorln(err)
		return
	}
	time.Sleep(time.Second * 2)

	now := time.Now()
	settleMonthTm := time.Date(now.Year(), now.Month()-1, 1, 0, 0, 0, 0, time.Local)
	if *settleMonth > 0 {
		settleMonthTm = time.Unix(int64(*settleMonth), 0)
	}
	log.Infof("settleMonthTm=%s", settleMonthTm)

	if *isDryRun {
		DryRun(settleMonthTm, m)
		log.Infof("isDryRun return")
		return
	}

	if *isMusic {
		exportMusic(settleMonthTm, m)
		//return
	}
	if *isEmotion {
		exportEmotion(settleMonthTm, m)
		//return
	}
	if *isTwoDimension {
		exportTwoDimension(settleMonthTm, m)
		return
	}

	uidstrlist := strings.Split(*uidstr, ",")
	uids := []uint32{}
	for _, str := range uidstrlist {
		u, _ := strconv.Atoi(str)
		if u > 0 {
			uids = append(uids, uint32(u))
		}
	}
	log.Infof("uids=%v", uids)

	if *downTwoDimension {
		if len(uids) == 0 {
			return
		}
		settleTwoDimensionsProc(uids, settleMonthTm, m)
		m.DownGradeAnchorCertTwoDimension(settleMonthTm)
		log.Infof("downTwoDimension return")
		return
	}
	if *downMusic {
		//if len(uids) == 0 {
		//	return
		//}
		//settleMusicProc(uids, settleMonthTm, m)
		m.DownGradeAnchorCertMusic(settleMonthTm)
		log.Infof("downMusic return")
		return
	}

	if *settleMusic {
		if len(uids) == 0 {
			return
		}
		settleMusicProc(uids, settleMonthTm, m)
	}
	if *settleEmotionStory {
		if len(uids) == 0 {
			return
		}
		settleEmotionStoryProc(uids, settleMonthTm, m)
	}
	if *settleTwoDimension {
		if len(uids) == 0 {
			return
		}
		settleTwoDimensionsProc(uids, settleMonthTm, m)
	}

	if *settleAnchorCertMusicTimer {
		m.SettleAnchorCertMusicTimer(settleMonthTm)
	}

	//m.SettleAnchorCertTwoDimensionsTimer(time.Now())
	//m.SettleAnchorCertEmotionStoryTimer(time.Now())
	//
}

func settleMusicProc(uids []uint32, settleTm time.Time, m *manager.AnchorContractMgr) {
	_, _, dyconfig, _, liveMgrCli, _, _ := m.GetField()
	ctx := context.Background()

	for _, uid := range uids {
		liveResp, serr := liveMgrCli.GetChannelLiveInfo(ctx, uid, false)
		if serr != nil {
			if serr.Code() != status.ErrChannelLiveNotAuthority {
				log.ErrorWithCtx(ctx, "SetAnchorCertWhiteList GetChannelLiveInfo fail %v, uid=%d", serr, uid)
				return
			}
		}
		tagId := liveResp.GetChannelLiveInfo().GetTagId()
		if tagId != manager.TagIdTypeMusic {
			log.Errorf("settleMusicProc tagId != manager.TagIdTypeMusic, uid=%d,tagid=%d", uid, tagId)
			return
		}
	}

	settleMonth := time.Date(2023, 5, 1, 0, 0, 0, 0, time.Local)
	taskList := dyconfig.GetAnchorCertSettle().Music
	if len(taskList) == 0 {
		log.Errorln("no find taskList")
		return
	}
	log.Infof("GetAnchorCertSettle().Music=%s", taskList)

	err := m.SettleAnchorCertMusicProc(uids, uint32(settleMonth.Unix()), taskList)
	if err != nil {
		log.Errorf("SettleAnchorCertMusicTimer SettleAnchorCertMusicProc fail %v", err)
		return
	}

	// 发放标识
	err = m.AwardAnchorCertProc(settleMonth, uint32(pb.AnchorCertContentTagType_AnchorCertContentTagType_Music))
	if err != nil {
		log.Errorf("SettleAnchorCertMusicTimer AwardAnchorCertProc fail %v", err)
		return
	}
}

func settleEmotionStoryProc(uids []uint32, settleTm time.Time, m *manager.AnchorContractMgr) {
	_, _, dyconfig, _, liveMgrCli, _, _ := m.GetField()
	ctx := context.Background()

	for _, uid := range uids {
		liveResp, serr := liveMgrCli.GetChannelLiveInfo(ctx, uid, false)
		if serr != nil {
			if serr.Code() != status.ErrChannelLiveNotAuthority {
				log.ErrorWithCtx(ctx, "settleEmotionStoryProc GetChannelLiveInfo fail %v, uid=%d", serr, uid)
				return
			}
		}
		tagId := liveResp.GetChannelLiveInfo().GetTagId()
		if tagId != manager.TagIdTypeEmotion && tagId != manager.TagIdTypeStory {
			log.Errorf("settleEmotionStoryProc tagId != manager.TagIdTypeEmotion,TagIdTypeStory  uid=%d,tagid=%d", uid, tagId)
			return
		}
	}

	settleMonth := time.Date(2023, 5, 1, 0, 0, 0, 0, time.Local)
	taskList := dyconfig.GetAnchorCertSettle().EmotionStory
	if len(taskList) == 0 {
		log.Errorln("no find taskList")
		return
	}
	log.Infof("GetAnchorCertSettle().EmotionStory=%s", taskList)

	err := m.SettleAnchorCertEmotionStoryProc(uids, uint32(settleMonth.Unix()), taskList)
	if err != nil {
		log.Errorf("settleEmotionStoryProc SettleAnchorCertEmotionStoryProc fail %v", err)
		return
	}

	// 发放标识
	err = m.AwardAnchorCertProc(settleMonth, uint32(pb.AnchorCertContentTagType_AnchorCertContentTagType_EmotionStory))
	if err != nil {
		log.Errorf("settleEmotionStoryProc AwardAnchorCertProc fail %v", err)
		return
	}
}

func settleTwoDimensionsProc(uids []uint32, settleTm time.Time, m *manager.AnchorContractMgr) {
	_, _, dyconfig, _, liveMgrCli, _, _ := m.GetField()
	ctx := context.Background()

	for _, uid := range uids {
		liveResp, serr := liveMgrCli.GetChannelLiveInfo(ctx, uid, false)
		if serr != nil {
			if serr.Code() != status.ErrChannelLiveNotAuthority {
				log.ErrorWithCtx(ctx, "settleTwoDimensionsProc GetChannelLiveInfo fail %v, uid=%d", serr, uid)
				return
			}
		}
		tagId := liveResp.GetChannelLiveInfo().GetTagId()
		if tagId != manager.TagIdTypeTwoDimension {
			log.Errorf("settleTwoDimensionsProc tagId != manager.TagIdTypeTwoDimension, uid=%d,tagid=%d", uid, tagId)
			return
		}
	}

	settleMonth := time.Date(2023, 5, 1, 0, 0, 0, 0, time.Local)
	taskList := dyconfig.GetAnchorCertSettle().TwoDimensions
	if len(taskList) == 0 {
		log.Errorln("no find taskList")
		return
	}
	log.Infof("GetAnchorCertSettle().TwoDimensions=%s", taskList)

	err := m.SettleAnchorCertTwoDimensionsProc(uids, uint32(settleMonth.Unix()), taskList)
	if err != nil {
		log.Errorf("settleTwoDimensionsProc SettleAnchorCertTwoDimensionsProc fail %v", err)
		return
	}

	// 发放标识
	err = m.AwardAnchorCertProc(settleMonth, uint32(pb.AnchorCertContentTagType_AnchorCertContentTagType_TwoDimensions))
	if err != nil {
		log.Errorf("SettleAnchorCertMusicTimer AwardAnchorCertProc fail %v", err)
		return
	}
}

func exportMusic(settleTm time.Time, m *manager.AnchorContractMgr) {

	mysqlstore, _, _, accountCli, _, _, _ := m.GetField()

	list, err := mysqlstore.GetAnchorCertAwardList(uint32(settleTm.Unix()), int32(pb.AnchorCertContentTagType_AnchorCertContentTagType_Music))
	if err != nil {
		log.Errorf("GetAnchorCertAwardList fail %v", err)
		return
	}

	wbFile := xlsx.NewFile()
	sh, _ := wbFile.AddSheet("sheet1")
	title := sh.AddRow()

	// 结算月、主播ttid、主播uid、主播昵称、通过考核时间、A类违规、B类违规、C类违规、直播活跃天、新增粉团、付费人数、主播收礼、是否B级、
	//通过考核赛、通过双周赛、通过季度赛、通过燃乐考核期

	title.AddCell().SetString("结算月")
	title.AddCell().SetString("主播uid")
	title.AddCell().SetString("主播ttid")
	title.AddCell().SetString("主播昵称")
	title.AddCell().SetString("达成等级")
	title.AddCell().SetString("通过考核时间")
	title.AddCell().SetString("考核等级")
	title.AddCell().SetString("A类违规次数")
	title.AddCell().SetString("B类违规次数")
	title.AddCell().SetString("C类违规次数")
	title.AddCell().SetString("直播活跃天")
	title.AddCell().SetString("新增粉团")
	title.AddCell().SetString("付费人数")
	title.AddCell().SetString("主播收礼")
	title.AddCell().SetString("通过考核赛")
	title.AddCell().SetString("通过双周赛")
	title.AddCell().SetString("通过季度赛")
	title.AddCell().SetString("通过燃乐考核期")

	for _, info := range list {
		if info.Extra == "" {
			log.Errorf("exportMusic extra is null, info=%+v", info)
			continue
		}

		extra := &mysql.AnchorCertTaskStats{}
		err := json.Unmarshal([]byte(info.Extra), extra)
		if err != nil {
			log.Errorf("Unmarshal fail %v, info=%+v", err, info)
			continue
		}

		userInfo, err := accountCli.GetUserByUid(context.Background(), info.Uid)
		if err != nil {
			log.Errorf("exportMusic GetUserByUid fail %v uid=%d", err, info.Uid)
			continue
		}

		row := sh.AddRow()
		row.AddCell().SetString(settleTm.Format("200601"))
		row.AddCell().SetInt(int(info.Uid))
		row.AddCell().SetString(userInfo.GetAlias())
		row.AddCell().SetString(userInfo.GetNickname())
		row.AddCell().SetString(info.ItemName)
		checkTime := time.Unix(int64(extra.LastCheckTime), 0)
		row.AddCell().SetString(checkTime.Format("2006-01-02 15:04:05"))
		row.AddCell().SetString(extra.LastCheckLevel)
		row.AddCell().SetInt(int(extra.MonthViolationACnt))
		row.AddCell().SetInt(int(extra.MonthViolationBCnt))
		row.AddCell().SetInt(int(extra.MonthViolationCCnt))
		row.AddCell().SetInt(int(extra.MonthActiveDays))
		row.AddCell().SetInt(int(extra.MonthNewFansCnt))
		row.AddCell().SetInt(int(extra.MonthConsumeCnt))
		row.AddCell().SetInt(int(extra.MonthIncome))
		if extra.CheckCompetitionStatus == 1 {
			row.AddCell().SetString("是")
		} else {
			row.AddCell().SetString("")
		}
		if extra.BiweeklyCompetitionStatus == 1 {
			row.AddCell().SetString("是")
		} else {
			row.AddCell().SetString("")
		}
		if extra.QuarterCompetitionStatus == 1 {
			row.AddCell().SetString("是")
		} else {
			row.AddCell().SetString("")
		}
		if extra.BurnCompetitionStatus == 1 {
			row.AddCell().SetString("是")
		} else {
			row.AddCell().SetString("")
		}
	}
	fileName := fmt.Sprintf("主播认证等级_音乐_%s_数据.xlsx", settleTm.Format("200601"))
	err = wbFile.Save(fileName)
	if err != nil {
		fmt.Printf("exportMusic Failed to Save %v\n", err)
		return
	}

	SendMail(fileName, fileName, "", toMailList)
	log.Infof("exportMusic SendMail done")
}

func exportEmotion(settleTm time.Time, m *manager.AnchorContractMgr) {

	mysqlstore, _, _, accountCli, _, _, _ := m.GetField()

	list, err := mysqlstore.GetAnchorCertAwardList(uint32(settleTm.Unix()), int32(pb.AnchorCertContentTagType_AnchorCertContentTagType_EmotionStory))
	if err != nil {
		log.Errorf("GetAnchorCertAwardList fail %v", err)
		return
	}

	wbFile := xlsx.NewFile()
	sh, _ := wbFile.AddSheet("sheet1")
	title := sh.AddRow()

	/*
		结算月、主播ttid、主播uid、主播昵称、A类违规、B类违规、C类违规、直播活跃天、新增粉团、付费人数、主播收礼、声控动态条数、是否B级、月度考核赛
	*/

	title.AddCell().SetString("结算月")
	title.AddCell().SetString("主播uid")
	title.AddCell().SetString("主播ttid")
	title.AddCell().SetString("主播昵称")
	title.AddCell().SetString("达成等级")
	title.AddCell().SetString("通过考核时间")
	title.AddCell().SetString("考核等级")
	title.AddCell().SetString("A类违规次数")
	title.AddCell().SetString("B类违规次数")
	title.AddCell().SetString("C类违规次数")
	title.AddCell().SetString("直播活跃天")
	title.AddCell().SetString("新增粉团")
	title.AddCell().SetString("付费人数")
	title.AddCell().SetString("主播收礼")
	title.AddCell().SetString("声控动态条数")
	title.AddCell().SetString("通过月度考核赛")

	for _, info := range list {
		if info.Extra == "" {
			log.Errorf("exportEmotion extra is null, info=%+v", info)
			continue
		}

		extra := &mysql.AnchorCertTaskStats{}
		err := json.Unmarshal([]byte(info.Extra), extra)
		if err != nil {
			log.Errorf("Unmarshal fail %v, info=%+v", err, info)
			continue
		}

		userInfo, err := accountCli.GetUserByUid(context.Background(), info.Uid)
		if err != nil {
			log.Errorf("exportEmotion GetUserByUid fail %v uid=%d", err, info.Uid)
			continue
		}

		row := sh.AddRow()
		row.AddCell().SetString(settleTm.Format("200601"))
		row.AddCell().SetInt(int(info.Uid))
		row.AddCell().SetString(userInfo.GetAlias())
		row.AddCell().SetString(userInfo.GetNickname())
		row.AddCell().SetString(info.ItemName)
		checkTime := time.Unix(int64(extra.LastCheckTime), 0)
		row.AddCell().SetString(checkTime.Format("2006-01-02 15:04:05"))
		row.AddCell().SetString(extra.LastCheckLevel)
		row.AddCell().SetInt(int(extra.MonthViolationACnt))
		row.AddCell().SetInt(int(extra.MonthViolationBCnt))
		row.AddCell().SetInt(int(extra.MonthViolationCCnt))
		row.AddCell().SetInt(int(extra.MonthActiveDays))
		row.AddCell().SetInt(int(extra.MonthNewFansCnt))
		row.AddCell().SetInt(int(extra.MonthConsumeCnt))
		row.AddCell().SetInt(int(extra.MonthIncome))
		row.AddCell().SetInt(int(extra.VoiceRecordCnt))
		if extra.MonthCompetitionStatus == 1 {
			row.AddCell().SetString("是")
		} else {
			row.AddCell().SetString("")
		}
	}
	fileName := fmt.Sprintf("主播认证等级_情感故事_%s_数据.xlsx", settleTm.Format("200601"))
	err = wbFile.Save(fileName)
	if err != nil {
		fmt.Printf("exportEmotion Failed to Save %v\n", err)
		return
	}

	SendMail(fileName, fileName, "", toMailList)
	log.Infof("exportEmotion SendMail done")
}

func exportTwoDimension(settleTm time.Time, m *manager.AnchorContractMgr) {

	mysqlstore, _, _, accountCli, _, _, _ := m.GetField()

	list, err := mysqlstore.GetAnchorCertAwardList(uint32(settleTm.Unix()), int32(pb.AnchorCertContentTagType_AnchorCertContentTagType_TwoDimensions))
	if err != nil {
		log.Errorf("GetAnchorCertAwardList fail %v", err)
		return
	}

	wbFile := xlsx.NewFile()
	sh, _ := wbFile.AddSheet("sheet1")
	title := sh.AddRow()

	/*
		结算月、主播ttid、主播uid、主播昵称、A类违规、B类违规、C类违规、直播活跃天、新增粉团、付费人数、主播收礼、是否B级、月度考核赛、双周赛
	*/

	title.AddCell().SetString("结算月")
	title.AddCell().SetString("主播uid")
	title.AddCell().SetString("主播ttid")
	title.AddCell().SetString("主播昵称")
	title.AddCell().SetString("达成等级")
	title.AddCell().SetString("通过考核时间")
	title.AddCell().SetString("考核等级")
	title.AddCell().SetString("A类违规次数")
	title.AddCell().SetString("B类违规次数")
	title.AddCell().SetString("C类违规次数")
	title.AddCell().SetString("直播活跃天")
	title.AddCell().SetString("新增粉团")
	title.AddCell().SetString("付费人数")
	title.AddCell().SetString("主播收礼")
	title.AddCell().SetString("通过月度考核赛")
	title.AddCell().SetString("通过双月考核赛")

	for _, info := range list {
		if info.Extra == "" {
			log.Errorf("exportTwoDimension extra is null, info=%+v", info)
			continue
		}

		extra := &mysql.AnchorCertTaskStats{}
		err := json.Unmarshal([]byte(info.Extra), extra)
		if err != nil {
			log.Errorf("Unmarshal fail %v, info=%+v", err, info)
			continue
		}

		userInfo, err := accountCli.GetUserByUid(context.Background(), info.Uid)
		if err != nil {
			log.Errorf("exportTwoDimension GetUserByUid fail %v uid=%d", err, info.Uid)
			continue
		}

		row := sh.AddRow()
		row.AddCell().SetString(settleTm.Format("200601"))
		row.AddCell().SetInt(int(info.Uid))
		row.AddCell().SetString(userInfo.GetAlias())
		row.AddCell().SetString(userInfo.GetNickname())
		row.AddCell().SetString(info.ItemName)
		checkTime := time.Unix(int64(extra.LastCheckTime), 0)
		row.AddCell().SetString(checkTime.Format("2006-01-02 15:04:05"))
		row.AddCell().SetString(extra.LastCheckLevel)
		row.AddCell().SetInt(int(extra.MonthViolationACnt))
		row.AddCell().SetInt(int(extra.MonthViolationBCnt))
		row.AddCell().SetInt(int(extra.MonthViolationCCnt))
		row.AddCell().SetInt(int(extra.MonthActiveDays))
		row.AddCell().SetInt(int(extra.MonthNewFansCnt))
		row.AddCell().SetInt(int(extra.MonthConsumeCnt))
		row.AddCell().SetInt(int(extra.MonthIncome))

		if extra.MonthCompetitionStatus == 1 {
			row.AddCell().SetString("是")
		} else {
			row.AddCell().SetString("")
		}
		if extra.DoubleMonthCompetitionStatus == 1 {
			row.AddCell().SetString("是")
		} else {
			row.AddCell().SetString("")
		}
	}
	fileName := fmt.Sprintf("主播认证等级_二次元_%s_数据.xlsx", settleTm.Format("200601"))
	err = wbFile.Save(fileName)
	if err != nil {
		fmt.Printf("exportTwoDimension Failed to Save %v\n", err)
		return
	}

	SendMail(fileName, fileName, "", toMailList)
	log.Infof("exportTwoDimension SendMail done")
}

func SendMail(filePath, subject, text string, toMails []string) error {
	m := gomail.NewMessage()
	m.SetHeader("From", "<EMAIL>")

	m.SetHeader("To", toMails...)
	m.SetHeader("Subject", subject)

	m.SetBody("text/html", text)
	m.Attach(filePath) //附件

	d := gomail.NewDialer("mail.52tt.com", 465, "<EMAIL>", "V7.D.sTy@%bDB50t#n.r$A4h+-FnXA")

	d.TLSConfig = &tls.Config{InsecureSkipVerify: true}
	if err := d.DialAndSend(m); err != nil {
		log.Errorf("Failed to SendMail %v", err)
		return err
	}
	return nil
}

// ==============================================================
func DryRun(settleTm time.Time, m *manager.AnchorContractMgr) {

	DryRunMusic(settleTm, m)

	DryRunEmotionStory(settleTm, m)

	DryRunTwoDimensions(settleTm, m)
}

func DryRunTwoDimensions(settleMonthTm time.Time, m *manager.AnchorContractMgr) {
	_, _, dyConfig, _, _, _, _ := m.GetField()

	uids, err := m.GetAllAnchorUidByTagId([]uint32{manager.TagIdTypeTwoDimension})
	if err != nil {
		log.Errorf("SettleAnchorCertTwoDimensions GetAllAnchorUidByTagId fail err %v", err)
		return
	}
	log.Infof("SettleAnchorCertTwoDimensions twoDimensionTagId=%d total=%d", manager.TagIdTypeTwoDimension, len(uids))

	taskList := dyConfig.GetAnchorCertSettle().TwoDimensions
	if len(taskList) == 0 {
		err = fmt.Errorf("结算配置不存在")
		log.Errorf("SettleAnchorCertTwoDimensions GetAnchorCertSettle empty fail %v", err)
		return
	}

	log.Infof("SettleAnchorCertTwoDimensions begin settleMonthTm=%v, taskList=%s, uids size=%d",
		settleMonthTm, taskList, len(uids))

	limit := 50
	for i := 0; i < len(uids); i += limit {
		end := i + limit
		if end > len(uids) {
			end = len(uids)
		}

		uidsx := uids[i:end]
		if len(uidsx) == 0 {
			break
		}

		err = SettleAnchorCertTwoDimensionsProc2(m, uidsx, uint32(settleMonthTm.Unix()), taskList)
		if err != nil {
			return
		}
	}
}

func SettleAnchorCertTwoDimensionsProc2(m *manager.AnchorContractMgr, uids []uint32, settleMonth uint32, taskList []*conf.AnchorCertSettleConfig) error {
	uid2Stats := map[uint32]*mysql.AnchorCertTaskStats{}
	taskLimit := taskList[0]

	store, _, _, _, _, liveStatsCli, _ := m.GetField()

	ctx, cancel := context.WithTimeout(context.Background(), time.Second*10)
	defer cancel()
	monthStatsResp, serr := liveStatsCli.BatchGetAnchorMonthlyStatsByUid(ctx, uids, settleMonth)
	if serr != nil {
		log.Errorf("SettleAnchorCertTwoDimensionsProc BatchGetAnchorMonthlyStatsByUid fail %v", serr)
		return serr
	}
	log.DebugfWithCtx(ctx, "SettleAnchorCertTwoDimensionsProc BatchGetAnchorMonthlyStatsByUid uids=%v,settleMonth=%d, resp=%+v", uids, settleMonth, monthStatsResp)

	passUids := []uint32{}
	for _, info := range monthStatsResp.GetList() {
		var pass = false
		totalIncome := info.AnchorIncome + info.KnightIncome
		if info.LiveActiveCnt >= taskLimit.MonthActiveDays &&
			info.NewFansCnt >= taskLimit.MonthNewFansCnt &&
			info.ConsumerCnt >= taskLimit.MonthConsumeCnt &&
			totalIncome >= taskLimit.MonthIncome {

			pass = true
			passUids = append(passUids, info.AnchorUid)
			uid2Stats[info.AnchorUid] = &mysql.AnchorCertTaskStats{
				MonthActiveDays: info.LiveActiveCnt,
				MonthNewFansCnt: info.NewFansCnt,
				MonthConsumeCnt: info.ConsumerCnt,
				MonthIncome:     totalIncome,
			}
		}
		log.Infof("SettleAnchorCertTwoDimensionsProc step1 monthStats uid %d, pass=%v LiveActiveCnt:[%d-%d], NewFansCnt:[%d-%d], ConsumerCnt:[%d-%d] MonthIncome:[%d(%d+%d)-%d]",
			info.AnchorUid, pass, info.LiveActiveCnt, taskLimit.MonthActiveDays, info.NewFansCnt, taskLimit.MonthNewFansCnt,
			info.ConsumerCnt, taskLimit.MonthConsumeCnt, totalIncome, info.AnchorIncome, info.KnightIncome, taskLimit.MonthIncome)
	}
	if len(passUids) == 0 {
		return nil
	}

	for _, uid := range passUids {
		lastUpdateTime, lastCheckLevel, err := getAnchorCheckLastLevel(m, ctx, uid)
		if err != nil {
			log.Errorf("SettleAnchorCertTwoDimensionsProc getAnchorCheckLastLevel fail %v", err)
			return err
		}
		log.Infof("SettleAnchorCertTwoDimensionsProc step2 uid %d lastCheckLevel=%s", uid, lastCheckLevel)
		if !(lastCheckLevel == "S" || lastCheckLevel == "A" || lastCheckLevel == "B") {
			log.Infof("SettleAnchorCertTwoDimensionsProc step2 no pass. uid %d lastCheckLevel=%s", uid, lastCheckLevel)
			continue
		}

		monthViolationACnt, monthViolationBCnt, monthViolationCCnt, err := filterViolation(m, ctx, uid, settleMonth)
		if err != nil {
			log.Errorf("SettleAnchorCertTwoDimensionsProc filterViolation fail %v", err)
			return err
		}
		log.Infof("SettleAnchorCertTwoDimensionsProc step3 uid %d monthViolationACnt=%d, monthViolationBCnt=%d, monthViolationCCnt=%d",
			uid, monthViolationACnt, monthViolationBCnt, monthViolationCCnt)
		if !(monthViolationACnt <= taskLimit.MonthViolationACnt &&
			monthViolationBCnt <= taskLimit.MonthViolationBCnt &&
			monthViolationCCnt <= taskLimit.MonthViolationCCnt) {
			log.Infof("SettleAnchorCertTwoDimensionsProc step3 no pass. uid %d monthViolationACnt=[%d-%d], monthViolationBCnt=[%d-%d], monthViolationCCnt=[%d-%d]",
				uid, monthViolationACnt, taskLimit.MonthViolationACnt, monthViolationBCnt, taskLimit.MonthViolationBCnt,
				monthViolationCCnt, taskLimit.MonthViolationCCnt)
			continue
		}

		competitionRecord := &mysql.AnchorCertCompetitionRecord{}
		_, competitionRecordList, err := store.GetAnchorCertCompetitionRecordList(uid,
			uint32(pb.AnchorCertContentTagType_AnchorCertContentTagType_TwoDimensions), false, 0, 1)
		if err != nil {
			log.Errorf("SettleAnchorCertTwoDimensionsProc GetAnchorCertCompetitionRecordList fail %v", err)
			return err
		}
		if len(competitionRecordList) > 0 {
			competitionRecord = competitionRecordList[0]
		}
		log.Infof("SettleAnchorCertTwoDimensionsProc uid %d competitionRecord=%+v", uid, competitionRecord)

		uid2Stats[uid].LastCheckLevel = lastCheckLevel
		uid2Stats[uid].LastCheckTime = lastUpdateTime
		uid2Stats[uid].MonthViolationACnt = monthViolationACnt
		uid2Stats[uid].MonthViolationBCnt = monthViolationBCnt
		uid2Stats[uid].MonthViolationCCnt = monthViolationCCnt
		uid2Stats[uid].MonthCompetitionStatus = competitionRecord.MonthCompetitionStatus
		uid2Stats[uid].DoubleMonthCompetitionStatus = competitionRecord.DoubleMonthCompetitionStatus

		itemLevel, itemName := calcAnchorCertTwoDimensions(uid, settleMonth, uid2Stats[uid], taskList)
		log.Infof("SettleAnchorCertTwoDimensionsProc done uid=%d itemLevel=%d itemName=%s stats=%+v", uid, itemLevel, itemName, uid2Stats[uid])
		if itemName == "" {
			log.Infof("SettleAnchorCertTwoDimensionsProc final no pass uid=%d stats=%s", uid, uid2Stats[uid])
			continue
		}

		settleMonthTm := time.Unix(int64(settleMonth), 0)
		awardLog := &mysql.AnchorCertMonthTaskAwardLog{
			Uid:       uid,
			YearMonth: uint32(settleMonthTm.Year()*100 + int(settleMonthTm.Month())),
			TagType:   uint32(pb.AnchorCertContentTagType_AnchorCertContentTagType_TwoDimensions),
			ItemName:  itemName,
			ItemLevel: itemLevel,
			Extra:     uid2Stats[uid].String(),
		}
		log.Infof("SettleAnchorCertTwoDimensionsProc uid=%d awardLog=%+v", uid, awardLog)

	}
	return nil
}

func DryRunEmotionStory(settleMonthTm time.Time, m *manager.AnchorContractMgr) {
	_, _, dyConfig, _, _, _, _ := m.GetField()

	uids, err := m.GetAllAnchorUidByTagId([]uint32{manager.TagIdTypeEmotion, manager.TagIdTypeStory})
	if err != nil {
		log.Errorf("SettleAnchorCertEmotionStory GetAllAnchorUidByTagId fail err %v", err)
		return
	}
	log.Infof("SettleAnchorCertEmotionStory emotionTagId=%d total=%d", manager.TagIdTypeEmotion, len(uids))

	taskList := dyConfig.GetAnchorCertSettle().EmotionStory
	if len(taskList) == 0 {
		err = fmt.Errorf("结算配置不存在")
		log.Errorf("SettleAnchorCertEmotionStory GetAnchorCertSettle empty fail %v", err)
		return
	}

	log.Infof("SettleAnchorCertEmotionStory begin settleMonthTm=%v, taskList=%s, uids size=%d",
		settleMonthTm, taskList, len(uids))

	limit := 50
	for i := 0; i < len(uids); i += limit {
		end := i + limit
		if end > len(uids) {
			end = len(uids)
		}

		uidsx := uids[i:end]
		if len(uidsx) == 0 {
			break
		}

		err = SettleAnchorCertEmotionStoryProc2(m, uidsx, uint32(settleMonthTm.Unix()), taskList)
		if err != nil {
			return
		}
	}
}

func SettleAnchorCertEmotionStoryProc2(m *manager.AnchorContractMgr, uids []uint32, settleMonth uint32, taskList []*conf.AnchorCertSettleConfig) error {
	uid2Stats := map[uint32]*mysql.AnchorCertTaskStats{}
	taskLimit := taskList[0]
	settleMonthTm := time.Unix(int64(settleMonth), 0)
	monthBegin := time.Date(settleMonthTm.Year(), settleMonthTm.Month(), 1, 0, 0, 0, 0, time.Local)
	monthEnd := time.Date(settleMonthTm.Year(), settleMonthTm.Month()+1, 1, 0, 0, 0, 0, time.Local).Add(-time.Second)

	store, _, _, _, _, liveStatsCli, _ := m.GetField()

	ctx, cancel := context.WithTimeout(context.Background(), time.Second*10)
	defer cancel()
	monthStatsResp, serr := liveStatsCli.BatchGetAnchorMonthlyStatsByUid(ctx, uids, settleMonth)
	if serr != nil {
		log.Errorf("SettleAnchorCertEmotionStoryProc BatchGetAnchorMonthlyStatsByUid fail %v", serr)
		return serr
	}
	log.DebugfWithCtx(ctx, "SettleAnchorCertEmotionStoryProc BatchGetAnchorMonthlyStatsByUid uids=%v,settleMonth=%d, resp=%+v", uids, settleMonth, monthStatsResp)

	passUids := []uint32{}
	for _, info := range monthStatsResp.GetList() {
		var pass = false
		totalIncome := info.AnchorIncome + info.KnightIncome
		if info.LiveActiveCnt >= taskLimit.MonthActiveDays &&
			info.NewFansCnt >= taskLimit.MonthNewFansCnt &&
			info.ConsumerCnt >= taskLimit.MonthConsumeCnt &&
			totalIncome >= taskLimit.MonthIncome {

			pass = true
			passUids = append(passUids, info.AnchorUid)
			uid2Stats[info.AnchorUid] = &mysql.AnchorCertTaskStats{
				MonthActiveDays: info.LiveActiveCnt,
				MonthNewFansCnt: info.NewFansCnt,
				MonthConsumeCnt: info.ConsumerCnt,
				MonthIncome:     totalIncome,
			}
		}
		log.Infof("SettleAnchorCertEmotionStoryProc step1 monthStats uid %d, pass=%v LiveActiveCnt:[%d-%d], NewFansCnt:[%d-%d], ConsumerCnt:[%d-%d] MonthIncome:[%d(%d+%d)-%d]",
			info.AnchorUid, pass, info.LiveActiveCnt, taskLimit.MonthActiveDays, info.NewFansCnt, taskLimit.MonthNewFansCnt,
			info.ConsumerCnt, taskLimit.MonthConsumeCnt, totalIncome, info.AnchorIncome, info.KnightIncome, taskLimit.MonthIncome)
	}
	if len(passUids) == 0 {
		return nil
	}

	for _, uid := range passUids {
		lastUpdateTime, lastCheckLevel, err := getAnchorCheckLastLevel(m, ctx, uid)
		if err != nil {
			log.Errorf("SettleAnchorCertEmotionStoryProc getAnchorCheckLastLevel fail %v, uid=%d", err, uid)
			return err
		}
		log.Infof("SettleAnchorCertEmotionStoryProc step2 uid %d lastCheckLevel=%s", uid, lastCheckLevel)
		if !(lastCheckLevel == "S" || lastCheckLevel == "A" || lastCheckLevel == "B") {
			log.Infof("SettleAnchorCertEmotionStoryProc step2 no pass. uid %d lastCheckLevel=%s", uid, lastCheckLevel)
			continue
		}

		monthViolationACnt, monthViolationBCnt, monthViolationCCnt, err := filterViolation(m, ctx, uid, settleMonth)
		if err != nil {
			log.Errorf("SettleAnchorCertEmotionStoryProc filterViolation fail %v, uid=%d", err, uid)
			return err
		}
		log.Infof("SettleAnchorCertEmotionStoryProc step3 uid %d monthViolationACnt=%d, monthViolationBCnt=%d, monthViolationCCnt=%d",
			uid, monthViolationACnt, monthViolationBCnt, monthViolationCCnt)
		if !(monthViolationACnt <= taskLimit.MonthViolationACnt &&
			monthViolationBCnt <= taskLimit.MonthViolationBCnt &&
			monthViolationCCnt <= taskLimit.MonthViolationCCnt) {
			log.Infof("SettleAnchorCertEmotionStoryProc step3 no pass. uid %d monthViolationACnt=[%d-%d], monthViolationBCnt=[%d-%d], monthViolationCCnt=[%d-%d]",
				uid, monthViolationACnt, taskLimit.MonthViolationACnt, monthViolationBCnt, taskLimit.MonthViolationBCnt,
				monthViolationCCnt, taskLimit.MonthViolationCCnt)
			continue
		}

		competitionRecord := &mysql.AnchorCertCompetitionRecord{}
		_, competitionRecordList, err := store.GetAnchorCertCompetitionRecordList(uid,
			uint32(pb.AnchorCertContentTagType_AnchorCertContentTagType_EmotionStory), false, 0, 1)
		if err != nil {
			log.Errorf("SettleAnchorCertEmotionStoryProc GetAnchorCertCompetitionRecordList fail %v,uid=%d", err, uid)
			return err
		}
		if len(competitionRecordList) > 0 {
			competitionRecord = competitionRecordList[0]
		}
		log.Infof("SettleAnchorCertEmotionStoryProc uid=%d competitionRecord=%+v", uid, competitionRecord)

		vcPostCnt, err := m.GetVCPostList(ctx, uid, uint32(monthBegin.Unix()), uint32(monthEnd.Unix()))
		if err != nil {
			log.Errorf("SettleAnchorCertEmotionStoryProc GetVCPostList fail %v, uid=%d", err, uid)
			return err
		}
		log.Infof("SettleAnchorCertEmotionStoryProc GetVCPostList uid=%d vcPostCnt=%d", uid, vcPostCnt)

		uid2Stats[uid].LastCheckLevel = lastCheckLevel
		uid2Stats[uid].LastCheckTime = lastUpdateTime
		uid2Stats[uid].MonthViolationACnt = monthViolationACnt
		uid2Stats[uid].MonthViolationBCnt = monthViolationBCnt
		uid2Stats[uid].MonthViolationCCnt = monthViolationCCnt
		uid2Stats[uid].MonthCompetitionStatus = competitionRecord.MonthCompetitionStatus
		uid2Stats[uid].VoiceRecordCnt = vcPostCnt

		itemLevel, itemName := calcAnchorCertEmotionStory(uid, settleMonth, uid2Stats[uid], taskList)
		log.Infof("SettleAnchorCertEmotionStoryProc done uid=%d itemLevel=%d itemName=%s stats=%+v", uid, itemLevel, itemName, uid2Stats[uid])
		if itemName == "" {
			log.Infof("SettleAnchorCertEmotionStoryProc final no pass uid=%d stats=%s", uid, uid2Stats[uid])
			continue
		}

		settleMonthTm := time.Unix(int64(settleMonth), 0)
		awardLog := &mysql.AnchorCertMonthTaskAwardLog{
			Uid:       uid,
			YearMonth: uint32(settleMonthTm.Year()*100 + int(settleMonthTm.Month())),
			TagType:   uint32(pb.AnchorCertContentTagType_AnchorCertContentTagType_EmotionStory),
			ItemName:  itemName,
			ItemLevel: itemLevel,
			Extra:     uid2Stats[uid].String(),
		}
		log.Infof("SettleAnchorCertEmotionStoryProc uid=%d awardLog=%+v", uid, awardLog)
	}
	return nil
}

func DryRunMusic(settleMonthTm time.Time, m *manager.AnchorContractMgr) {
	uids, err := m.GetAllAnchorUidByTagId([]uint32{manager.TagIdTypeMusic})
	if err != nil {
		log.Errorf("SettleAnchorCertMusic GetAllAnchorUidByTagId fail err %v", err)
		return
	}
	log.Infof("SettleAnchorCertMusic musicTagId=%d total=%d", manager.TagIdTypeMusic, len(uids))

	_, _, dyConfig, _, _, _, _ := m.GetField()

	taskList := dyConfig.GetAnchorCertSettle().Music
	if len(taskList) == 0 {
		err = fmt.Errorf("结算配置不存在")
		log.Errorf("SettleAnchorCertMusic GetAnchorCertSettle empty fail %v", err)
		return
	}

	taskLimit := taskList[0]
	log.Infof("SettleAnchorCertMusic begin settleMonthTm=%v, taskLimit=%+v, uids size=%d", settleMonthTm, taskLimit, len(uids))

	limit := 50
	for i := 0; i < len(uids); i += limit {
		end := i + limit
		if end > len(uids) {
			end = len(uids)
		}

		uidsx := uids[i:end]
		log.Debugf("SettleAnchorCertMusic %d-%d uids=%v", i, end, uidsx)
		if len(uidsx) == 0 {
			break
		}

		err = SettleAnchorCertMusicProc2(m, uidsx, uint32(settleMonthTm.Unix()), taskList)
		if err != nil {
			return
		}
	}
}

func SettleAnchorCertMusicProc2(m *manager.AnchorContractMgr, uids []uint32, settleMonth uint32,
	taskList []*conf.AnchorCertSettleConfig) error {
	uid2Stats := map[uint32]*mysql.AnchorCertTaskStats{}
	taskLimit := taskList[0]

	store, _, _, _, _, liveStatsCli, _ := m.GetField()

	ctx, cancel := context.WithTimeout(context.Background(), time.Second*10)
	defer cancel()
	monthStatsResp, serr := liveStatsCli.BatchGetAnchorMonthlyStatsByUid(ctx, uids, settleMonth)
	if serr != nil {
		log.Errorf("SettleAnchorCertMusic BatchGetAnchorMonthlyStatsByUid fail %v", serr)
		return serr
	}
	log.DebugfWithCtx(ctx, "SettleAnchorCertMusicProc BatchGetAnchorMonthlyStatsByUid uids=%v,settleMonth=%d, resp=%+v", uids, settleMonth, monthStatsResp)

	passUids := []uint32{}
	for _, info := range monthStatsResp.GetList() {
		var pass = false
		totalIncome := info.AnchorIncome + info.KnightIncome
		if info.LiveActiveCnt >= taskLimit.MonthActiveDays &&
			info.NewFansCnt >= taskLimit.MonthNewFansCnt &&
			info.ConsumerCnt >= taskLimit.MonthConsumeCnt &&
			totalIncome >= taskLimit.MonthIncome {

			pass = true
			passUids = append(passUids, info.AnchorUid)
			uid2Stats[info.AnchorUid] = &mysql.AnchorCertTaskStats{
				MonthActiveDays: info.LiveActiveCnt,
				MonthNewFansCnt: info.NewFansCnt,
				MonthConsumeCnt: info.ConsumerCnt,
				MonthIncome:     totalIncome,
			}
		}
		log.Infof("SettleAnchorCertMusic step1 monthStats uid %d, pass=%v month=%q LiveActiveCnt:[%d-%d], NewFansCnt:[%d-%d], ConsumerCnt:[%d-%d] MonthIncome:[%d(%d+%d)-%d]",
			info.AnchorUid, pass, info.Yearmonth, info.LiveActiveCnt, taskLimit.MonthActiveDays, info.NewFansCnt, taskLimit.MonthNewFansCnt,
			info.ConsumerCnt, taskLimit.MonthConsumeCnt, totalIncome, info.AnchorIncome, info.KnightIncome, taskLimit.MonthIncome)
	}
	if len(passUids) == 0 {
		return nil
	}

	for _, uid := range passUids {
		lastUpdateTime, lastCheckLevel, err := getAnchorCheckLastLevel(m, ctx, uid)
		if err != nil {
			log.Errorf("SettleAnchorCertMusic getAnchorCheckLastLevel fail %v", err)
			return err
		}
		log.Infof("SettleAnchorCertMusic step2 uid %d lastCheckLevel=%s", uid, lastCheckLevel)
		if !(lastCheckLevel == "S" || lastCheckLevel == "A" || lastCheckLevel == "B") {
			log.Infof("SettleAnchorCertMusic step2 no pass. uid %d lastCheckLevel=%s", uid, lastCheckLevel)
			continue
		}

		monthViolationACnt, monthViolationBCnt, monthViolationCCnt, err := filterViolation(m, ctx, uid, settleMonth)
		if err != nil {
			log.Errorf("SettleAnchorCertMusic filterViolation fail %v, uid=%d", err, uid)
			return err
		}
		log.Infof("SettleAnchorCertMusic step3 uid %d monthViolationACnt=%d, monthViolationBCnt=%d, monthViolationCCnt=%d",
			uid, monthViolationACnt, monthViolationBCnt, monthViolationCCnt)
		if !(monthViolationACnt <= taskLimit.MonthViolationACnt &&
			monthViolationBCnt <= taskLimit.MonthViolationBCnt &&
			monthViolationCCnt <= taskLimit.MonthViolationCCnt) {
			log.Infof("SettleAnchorCertMusic step3 no pass. uid %d monthViolationACnt=[%d-%d], monthViolationBCnt=[%d-%d], monthViolationCCnt=[%d-%d]",
				uid, monthViolationACnt, taskLimit.MonthViolationACnt, monthViolationBCnt, taskLimit.MonthViolationBCnt,
				monthViolationCCnt, taskLimit.MonthViolationCCnt)
			continue
		}

		competitionRecord := &mysql.AnchorCertCompetitionRecord{}
		_, competitionRecordList, err := store.GetAnchorCertCompetitionRecordList(uid,
			uint32(pb.AnchorCertContentTagType_AnchorCertContentTagType_Music), false, 0, 1)
		if err != nil {
			log.Errorf("SettleAnchorCertMusic GetAnchorCertCompetitionRecordList fail %v", err)
			return err
		}
		if len(competitionRecordList) > 0 {
			competitionRecord = competitionRecordList[0]
		}
		log.Infof("SettleAnchorCertMusic uid %d competitionRecord=%+v", uid, competitionRecord)

		uid2Stats[uid].LastCheckLevel = lastCheckLevel
		uid2Stats[uid].LastCheckTime = lastUpdateTime
		uid2Stats[uid].MonthViolationACnt = monthViolationACnt
		uid2Stats[uid].MonthViolationBCnt = monthViolationBCnt
		uid2Stats[uid].MonthViolationCCnt = monthViolationCCnt
		uid2Stats[uid].CheckCompetitionStatus = competitionRecord.CheckCompetitionStatus
		uid2Stats[uid].QuarterCompetitionStatus = competitionRecord.QuarterCompetitionStatus
		uid2Stats[uid].BiweeklyCompetitionStatus = competitionRecord.BiweeklyCompetitionStatus
		uid2Stats[uid].BurnCompetitionStatus = competitionRecord.BurnCompetitionStatus
		uid2Stats[uid].MonthCompetitionStatus = competitionRecord.MonthCompetitionStatus
		uid2Stats[uid].DoubleMonthCompetitionStatus = competitionRecord.DoubleMonthCompetitionStatus

		itemLevel, itemName := calcAnchorCertMusicLevel(uid, settleMonth, uid2Stats[uid], taskList)
		log.Infof("SettleAnchorCertMusic done uid=%d itemLevel=%d itemName=%q stats=%+v", uid, itemLevel, itemName, uid2Stats[uid])
		if itemName == "" {
			log.Infof("SettleAnchorCertMusic final no pass uid=%d stats=%s", uid, uid2Stats[uid])
			continue
		}

		settleMonthTm := time.Unix(int64(settleMonth), 0)
		awardLog := &mysql.AnchorCertMonthTaskAwardLog{
			Uid:       uid,
			YearMonth: uint32(settleMonthTm.Year()*100 + int(settleMonthTm.Month())),
			TagType:   uint32(pb.AnchorCertContentTagType_AnchorCertContentTagType_Music),
			ItemName:  itemName,
			ItemLevel: itemLevel,
			Extra:     uid2Stats[uid].String(),
		}
		log.Infof("SettleAnchorCertMusic uid=%d awardLog=%+v", uid, awardLog)
	}
	return nil
}

func getAnchorCheckLastLevel(m *manager.AnchorContractMgr, ctx context.Context, uid uint32) (lastUpdateTime uint32, lastCheckLevel string, err error) {
	_, _, _, _, _, _, anchorCheckCli := m.GetField()
	checkResp, err := anchorCheckCli.GetAnchorCheckHistory(ctx, uid)
	if err != nil {
		log.Errorf("getAnchorCheckLastLevel fail %v", err)
		return
	}
	if len(checkResp.GetList()) > 0 {
		lastUpdateTime = checkResp.GetList()[0].GetUpdateTime()
		lastCheckLevel = checkResp.GetList()[0].GetLevel()
	}
	return
}

func filterViolation(m *manager.AnchorContractMgr, ctx context.Context, uid uint32, settleMonthTs uint32) (
	monthViolationACnt uint32, monthViolationBCnt uint32, monthViolationCCnt uint32, err error) {

	settleMonthTm := time.Unix(int64(settleMonthTs), 0)
	monthBegin := time.Date(settleMonthTm.Year(), settleMonthTm.Month(), 1, 0, 0, 0, 0, time.Local)
	monthEnd := time.Date(settleMonthTm.Year(), settleMonthTm.Month()+1, 1, 0, 0, 0, 0, time.Local).Add(-time.Second)

	_, _, _, accountCli, _, _, _ := m.GetField()

	userInfo, serr := accountCli.GetUserByUid(ctx, uid)
	if serr != nil {
		log.Errorf("filterViolation GetUsersMap fail %v, uids %v", serr, uid)
		err = serr
		return
	}
	alias := userInfo.GetAlias()
	// 违规次数
	tid2ViolationsInfo, err := urrc.GetViolationsInfoList(ctx, []string{alias}, monthBegin, monthEnd)
	if err != nil {
		log.Errorf("filterViolation urrc.GetViolationsInfoList fail %v", err)
		return
	}
	violationA, violationB, violationC := tid2ViolationsInfo[alias].GetViolation()
	monthViolationACnt, monthViolationBCnt, monthViolationCCnt = uint32(len(violationA)), uint32(len(violationB)), uint32(len(violationC))
	return
}

func calcAnchorCertMusicLevel(uid uint32, settleMonthTs uint32, stats *mysql.AnchorCertTaskStats, taskList []*conf.AnchorCertSettleConfig) (itemLevel uint32, itemName string) {
	for i := len(taskList) - 1; i >= 0; i-- {
		task := taskList[i]

		if stats.MonthActiveDays >= task.MonthActiveDays &&
			stats.MonthNewFansCnt >= task.MonthNewFansCnt &&
			stats.MonthConsumeCnt >= task.MonthConsumeCnt &&
			stats.MonthIncome >= task.MonthIncome &&
			stats.CheckCompetitionStatus == task.CheckCompetitionStatus &&
			stats.BiweeklyCompetitionStatus == task.BiweeklyCompetitionStatus &&
			stats.QuarterCompetitionStatus == task.QuarterCompetitionStatus &&
			stats.BurnCompetitionStatus == task.BurnCompetitionStatus {

			if task.ItemName != "星光歌手" {
				itemLevel = task.ItemLevel
				itemName = task.ItemName
				return
			}

			anchorType := getAnchorTypeByCheckTime(uid, settleMonthTs, int64(stats.LastCheckTime))
			needMonthActiveDays := task.Extra.AnchorType2MonthActiveDays[fmt.Sprintf("%d", anchorType)]
			log.Infof("calcAnchorCertMusicLevel 星光歌手 uid=%d settleMonthTs=%d LastCheckTime=%d anchorType=%d needMonthActiveDays=%d stats=%+v",
				uid, settleMonthTs, stats.LastCheckTime, anchorType, needMonthActiveDays, stats)
			if stats.MonthActiveDays >= needMonthActiveDays {
				itemLevel = task.ItemLevel
				itemName = task.ItemName
				return
			}
		}
	}
	return
}

func calcAnchorCertEmotionStory(uid uint32, settleMonthTs uint32, stats *mysql.AnchorCertTaskStats, taskList []*conf.AnchorCertSettleConfig) (itemLevel uint32, itemName string) {
	for i := len(taskList) - 1; i >= 0; i-- {
		task := taskList[i]
		if stats.MonthActiveDays >= task.MonthActiveDays &&
			stats.MonthNewFansCnt >= task.MonthNewFansCnt &&
			stats.MonthConsumeCnt >= task.MonthConsumeCnt &&
			stats.MonthIncome >= task.MonthIncome &&
			stats.VoiceRecordCnt >= task.VoiceRecordCnt &&
			stats.MonthCompetitionStatus == task.MonthCompetitionStatus &&
			stats.MonthViolationACnt <= task.MonthViolationACnt &&
			stats.MonthViolationBCnt <= task.MonthViolationBCnt &&
			stats.MonthViolationCCnt <= task.MonthViolationCCnt {

			itemLevel = task.ItemLevel
			itemName = task.ItemName
			return
		}
	}
	return
}

func calcAnchorCertTwoDimensions(uid uint32, settleMonthTs uint32, stats *mysql.AnchorCertTaskStats, taskList []*conf.AnchorCertSettleConfig) (itemLevel uint32, itemName string) {
	for i := len(taskList) - 1; i >= 0; i-- {
		task := taskList[i]
		if stats.MonthActiveDays >= task.MonthActiveDays &&
			stats.MonthNewFansCnt >= task.MonthNewFansCnt &&
			stats.MonthConsumeCnt >= task.MonthConsumeCnt &&
			stats.MonthIncome >= task.MonthIncome &&
			stats.MonthCompetitionStatus == task.MonthCompetitionStatus &&
			stats.DoubleMonthCompetitionStatus == task.DoubleMonthCompetitionStatus &&
			stats.MonthViolationACnt <= task.MonthViolationACnt &&
			stats.MonthViolationBCnt <= task.MonthViolationBCnt &&
			stats.MonthViolationCCnt <= task.MonthViolationCCnt {

			itemLevel = task.ItemLevel
			itemName = task.ItemName
			return
		}
	}
	return
}

const (
	AnchorTypeByCheckTime_Others              = 0
	AnchorTypeByCheckTime_LastMonthBefore11th = 1
	AnchorTypeByCheckTime_LastMonthAfter11th  = 2
)

/*
"month_platform_days_enum_desc": {
          "0": "非类型1,2的主播",
          "1": "上月通过考核且通过考核时间在10号前（包含10号）的主播",
          "2": "上月通过考核且通过考核时间在11号后（包含11号）的主播"
        }
*/
func getAnchorTypeByCheckTime(uid uint32, settleMonthTs uint32, lastCheckTime int64) uint32 {
	settleMonthTm := time.Unix(int64(settleMonthTs), 0)
	settleMonthBegin := time.Date(settleMonthTm.Year(), settleMonthTm.Month(), 1, 0, 0, 0, 0, time.Local).Unix()
	settleMonthEnd := time.Date(settleMonthTm.Year(), settleMonthTm.Month()+1, 1, 0, 0, 0, 0, time.Local).Add(-time.Second).Unix()
	checkTimeStep := time.Date(settleMonthTm.Year(), settleMonthTm.Month(), 11, 0, 0, 0, 0, time.Local).Unix()

	if lastCheckTime >= settleMonthBegin && lastCheckTime <= settleMonthEnd {
		if lastCheckTime < checkTimeStep {
			return AnchorTypeByCheckTime_LastMonthBefore11th
		} else {
			return AnchorTypeByCheckTime_LastMonthAfter11th
		}
	} else {
		return AnchorTypeByCheckTime_Others
	}
}
