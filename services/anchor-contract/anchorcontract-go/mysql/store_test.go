package mysql

/*
import (
	"context"
	"encoding/json"
	"fmt"
	"testing"
	"time"

	"github.com/jinzhu/gorm"
	"golang.52tt.com/pkg/config"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/protocol/common/status"
	pb "golang.52tt.com/protocol/services/anchorcontract-go"
	"golang.52tt.com/services/anchor-contract/anchorcontract-go/conf"
)

var mysqlStore *Store
var sc *conf.ServiceConfigT

func init() {
	log.SetLevel(log.DebugLevel)

	sc = &conf.ServiceConfigT{}
	err := sc.Parse("../anchorcontract-go.json")
	if err != nil {
		return
	}

	//mysqlStore.CreateTable()
}

// GetAllContractGuildId
// go test -timeout 30s -run ^TestGetAllContractGuildId$ golang.52tt.com/services/anchor-contract/anchorcontract-go/mysql -v -count=1
func TestGetAllContractGuildId(t *testing.T) {
	sc.MysqlConfig.Host = "************"
	//sc.MysqlConfig.Host = "*************"

	fmt.Printf("string:%s\n", sc.MysqlConfig.ConnectionString())
	mysqlDb, err := gorm.Open("mysql", sc.MysqlConfig.ConnectionString())
	if err != nil {
		fmt.Printf("Failed to Connect mysql %v", err)
		return
	}

	mysqlStore = NewMysql(mysqlDb.Debug(), mysqlDb.Debug())

	t.Log(mysqlStore.GetAllContractGuildId())
}

// GetGuildCancelSignRecordList
// go test -timeout 30s -run ^TestGetGuildCancelSignRecordList1$ golang.52tt.com/services/anchor-contract/anchorcontract-go/mysql -v -count=1
func TestGetGuildCancelSignRecordList1(t *testing.T) {
	return

	cfg := &config.MysqlConfig{
		Host:     "**************",
		Port:     3306,
		Database: "appsvr",
		UserName: "godman",
		Password: "thegodofman",
		Protocol: "tcp",
		Charset:  "utf8",
	}
	mysqlDb, err := gorm.Open("mysql", cfg.ConnectionString())
	if err != nil {
		fmt.Printf("Failed to Connect mysql %v", err)
		return
	}

	mysqlStore = NewMysql(mysqlDb.Debug(), mysqlDb.Debug())

	total, list, err := mysqlStore.GetGuildCancelSignRecordList(0, 100154, []uint32{}, 0, 10)
	if err != nil {
		t.Log(err)
		return
	}
	t.Log(total)
	for _, info := range list {
		t.Logf("%+v\n", info)
	}

}

// AddCancelContractApplyLog
// go test -timeout 30s -run ^TestAddCancelContractApplyLog2$ golang.52tt.com/services/anchor-contract/anchorcontract-go/mysql -v -count=1
func TestAddCancelContractApplyLog2(t *testing.T) {
	// AddCancelContractApplyLog
	cancelApplyLog := &CancelContractApplyLog{
		Uid:                 1,
		GuildId:             1,
		SignTime:            time.Now(),
		ContractExpireTime:  time.Now(),
		Status:              0,
		Reason:              1,
		ApplyTime:           time.Now(),
		LiveObtainTime:      time.Now(),
		MultiplayObtainTime: time.Now(),
		UpdateTime:          time.Now(),
	}
	identityInfo := &IdentityInfo{}
	identityInfo.AnchorIdentityInfoList = append(identityInfo.AnchorIdentityInfoList, &AnchorIdentityInfo{
		IdentityType: 0,
		ObtainTime:   uint32(time.Now().Unix()),
	})
	identityInfo.AnchorIdentityInfoList = append(identityInfo.AnchorIdentityInfoList, &AnchorIdentityInfo{
		IdentityType: 2,
		ObtainTime:   uint32(time.Now().Unix()),
	})
	identityInfoStr, _ := json.Marshal(identityInfo)
	cancelApplyLog.IdentityInfo = string(identityInfoStr)

	err := mysqlStore.AddCancelContractApplyLog(nil, cancelApplyLog)
	t.Log(err)
}

// AddContractApply
// go test -timeout 30s -run ^TestAddContractApply$ golang.52tt.com/services/anchor-contract/anchorcontract-go/mysql -v -count=1
func TestAddContractApply(t *testing.T) {

	info := &ContractApply{
		Uid:              1,
		IdentityType:     2,
		GuildId:          1,
		IdentityNum:      "1",
		ContractDuration: 36,
		ApplyTime:        time.Now(),
		UpdateTime:       time.Now(),
	}
	mysqlStore.AddContractApply(nil, info)
	t.Log(info.Id)

}

// go test -timeout 30s -run ^TestGetContractReadOnly$ golang.52tt.com/services/anchor-contract/anchorcontract-go/mysql -v -count=1
func TestGetContractReadOnly(t *testing.T) {
	total, list, err := mysqlStore.GetContractReadOnly(0, 10)
	t.Log(total, list, err)
}

func testTran(store *Store, applyId, opr uint32) error {

	ctx := context.Background()
	// 开启事务
	err := store.Transaction(ctx, func(tx *gorm.DB) error {

		applyInfo, err := store.GetContractApplyWithId(tx, applyId)
		if err != nil {
			log.ErrorWithCtx(ctx, "PresidentHandleApplySign fail to GetContractApplyWithId in:%+v, err:%v", applyId, err)
			return err
		}
		applySignGuildId := applyInfo.GuildId
		if opr == uint32(pb.HANDLE_SIGN_APPLY_OPR_HANDLE_SIGN_APPLY_OPR_ACCEPT) {
			if applyInfo.IdentityType == uint32(pb.SIGN_ANCHOR_IDENTITY_SIGN_ANCHOR_IDENTITY_RADIO_LIVE) {
				now := time.Now()
				monthBegin := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, time.Local)
				monthEnd := time.Date(now.Year(), now.Month()+1, 1, 0, 0, 0, 0, time.Local).Add(-time.Second)
				yearmonth := fmt.Sprintf("%d-%02d", now.Year(), now.Month())
				exist, info, err := store.GetGuildSignAnchorInfo(tx, applySignGuildId, yearmonth)
				if err != nil {
					log.Errorf("PresidentHandleApplySign GetGuildSignAnchorInfo fail %v,guildId=%d applyId=%d", err, applySignGuildId, applyId)
					return err
				}
				if exist {
					// for update
					monthUsedCnt, nowDayUsedCnt, err := store.GetGuildSignAnchorTimeRangeCnt(tx, applySignGuildId, monthBegin, monthEnd, now, true)
					if err != nil {
						log.Errorf("PresidentHandleApplySign GetGuildSignAnchorTimeRangeCnt fail %v,guildId=%d applyId=%d", err, applySignGuildId, applyId)
						return err
					}
					if now.Day() <= 25 {
						if nowDayUsedCnt >= info.MonthBefore25thCnt {
							err = protocol.NewExactServerError(nil, status.ErrContractAudioLilmitStatusDay)
							log.Errorf("PresidentHandleApplySign fail %v, guildId=%d applyId=%d", err, applySignGuildId, applyId)
							return err
						}
					}
					if monthUsedCnt >= info.MonthCnt {
						err = protocol.NewExactServerError(nil, status.ErrContractAudioLilmitStatusMonth)
						log.Errorf("PresidentHandleApplySign fail %v, guildId=%d applyId=%d", err, applySignGuildId, applyId)
						return err
					}
				}

				// 会长审批通过后，记录个数
				err = store.AddGuildSignAnchorLog(tx, applyInfo.GuildId, time.Now()) //
				if err != nil {
					log.ErrorWithCtx(ctx, "PresidentHandleApplySign fail to store.AddGuildSignAnchorLog  applyPbInfo:%+v err:%v",
						applyId, err)
					return err
				}
				log.InfoWithCtx(ctx, "PresidentHandleApplySign AddGuildSignAnchorLog guildId=%d applyId=%d", applyInfo.GuildId, applyId)
			}
		}
		return nil
	})
	return err
}

// go test -timeout 30s -run ^TestGetGuildSignAnchorInfX$ golang.52tt.com/services/anchor-contract/anchorcontract-go/mysql -v -count=1
func TestGetGuildSignAnchorInfX(t *testing.T) {
	/*
		insert tbl_contract_apply_v2(id,uid,guild_id,identity_type,contract_duration,identity_num,form_extra,extra)values(1,2404178,100154,1,36,'','','');
	*/
/*
	for i := 0; i < 10; i++ {
		go func(id int) {
			err := testTran(mysqlStore, 1, 1)
			log.Infof("id=%d err=%v", id, err)
		}(i)
	}

	select {}
}
*/

/*
// GetGuildSignAnchorInfo
// go test -timeout 30s -run ^TestGetGuildSignAnchorInfo$ golang.52tt.com/services/anchor-contract/anchorcontract-go/mysql -v -count=1
func TestGetGuildSignAnchorInfo(t *testing.T) {

	t.Log(mysqlStore.AddGuildSignAnchorLog(nil, 1, time.Now()))

	//list, err := mysqlStore.GetOfficialApplySignAnchorTimeRange(uint32(time.Now().Unix()),
	//	uint32(time.Now().Unix()-86400),
	//	0, 10)
	//t.Log(list, err)
	//return

	t.Log(mysqlStore.GetGuildSignAnchorInfo(nil, 103219, "2023-06"))
	//t.Log(mysqlStore.GetGuildSignAnchorInfo(nil, 11, "2023-05"))
}

// GetGuildSignAnchorTimeRangeCnt
// go test -timeout 30s -run ^TestGetGuildSignAnchorTimeRangeCnt$ golang.52tt.com/services/anchor-contract/anchorcontract-go/mysql -v -count=1
func TestGetGuildSignAnchorTimeRangeCnt(t *testing.T) {

	now := time.Now()
	monthBegin := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, time.Local)
	monthEnd := time.Date(now.Year(), now.Month()+1, 1, 0, 0, 0, 0, time.Local).Add(-time.Second)

	monthTotalCnt, nowDayCnt, err := mysqlStore.GetGuildSignAnchorTimeRangeCnt(nil, 1, monthBegin, monthEnd, time.Now(), false)
	t.Log(monthTotalCnt, nowDayCnt, err)

	return

	// 原来：已使用13个
	for i := 0; i < 10; i++ {
		go func(id int) {
			t.Log(id, "begin")
			monthTotalCnt, nowDayCnt, err := mysqlStore.GetGuildSignAnchorTimeRangeCnt(nil, 1, monthBegin, monthEnd, time.Now(), false)
			t.Log("end", id, monthTotalCnt, nowDayCnt, err)
			t.Log("incr", id, mysqlStore.AddGuildSignAnchorLog(nil, 1, time.Now()))
		}(i)
	}

	time.Sleep(time.Minute * 10)
}

// GetGuildSignAnchorInfoList
// go test -timeout 30s -run ^TestGetGuildSignAnchorInfoList$ golang.52tt.com/services/anchor-contract/anchorcontract-go/mysql -v -count=1
func TestGetGuildSignAnchorInfoList(t *testing.T) {

	total, list, err := mysqlStore.GetGuildSignAnchorInfoList(nil, "2023-05", nil, 0, 10)
	t.Log(total, list, err)
	total, list, err = mysqlStore.GetGuildSignAnchorInfoList(nil, "2023-05", []uint32{1}, 0, 10)
	t.Log(total, list, err)
}

// AddGuildSignAnchorInfo
// go test -timeout 30s -run ^TestAddGuildSignAnchorInfo$ golang.52tt.com/services/anchor-contract/anchorcontract-go/mysql -v -count=1
func TestAddGuildSignAnchorInfo(t *testing.T) {
	info := &GuildSignAnchorInfo{
		GuildId:            1,
		Yearmonth:          "2023-05",
		MonthBefore25thCnt: 11,
		MonthCnt:           101,
		Operator:           "x",
	}
	mysqlStore.AddGuildSignAnchorInfo(nil, info)
}

// go test -timeout 30s -run ^TestRecordAnchorCertChangeLog$ golang.52tt.com/services/anchor-contract/anchorcontract-go/mysql -v -count=1
func TestRecordAnchorCertChangeLog(t *testing.T) {
	ts := time.Date(2023, 4, 1, 0, 0, 0, 0, time.Local)
	info := &AnchorCertMonthTaskAwardChangeLog{
		Uid:       1,
		TagType:   1,
		ItemName:  "X",
		ItemLevel: 1,
		Operator:  "A",
	}
	t.Log(mysqlStore.RecordAnchorCertChangeLog(nil, uint32(ts.Unix()), info))
}

// go test -timeout 30s -run ^TestUpdateAnchorCertAwardLevel$ golang.52tt.com/services/anchor-contract/anchorcontract-go/mysql -v -count=1
func TestUpdateAnchorCertAwardLevel(t *testing.T) {
	//ts := time.Date(2023, 4, 1, 0, 0, 0, 0, time.Local)
	//t.Log(mysqlStore.UpdateAnchorCertAwardLevel(nil, uint32(ts.Unix()), 1, 1, "x1", 2))
}

// go test -timeout 30s -run ^TestGetAnchorCertUpgradeTask$ golang.52tt.com/services/anchor-contract/anchorcontract-go/mysql -v -count=1
func TestGetAnchorCertUpgradeTask(t *testing.T) {
	//t.Log(mysqlStore.GetAnchorCertUpgradeTask("1级", time.Now()))
}

// go test -timeout 30s -run ^TestGetAnchorCertAwardList$ golang.52tt.com/services/anchor-contract/anchorcontract-go/mysql -v -count=1
func TestGetAnchorCertAwardList(t *testing.T) {
	ts := time.Date(2023, 4, 1, 0, 0, 0, 0, time.Local)
	t.Log(mysqlStore.GetAnchorCertAwardList(uint32(ts.Unix()), -1))
	t.Log(mysqlStore.GetAnchorCertAwardList(uint32(ts.Unix()), 1))
}

// go test -timeout 30s -run ^TestGetAnchorCertAwardByUid$ golang.52tt.com/services/anchor-contract/anchorcontract-go/mysql -v -count=1
func TestGetAnchorCertAwardByUid(t *testing.T) {

	mysqlStore.InsertAnchorCertMonthTaskAwardLog(&AnchorCertMonthTaskAwardLog{
		Uid:       1,
		YearMonth: 202304,
		TagType:   1,
		ItemName:  "xx",
	})

	ts := time.Date(2023, 4, 1, 0, 0, 0, 0, time.Local)

	t.Log(mysqlStore.UpdateAnchorCertAwardStatus(nil, uint32(ts.Unix()), 1))
	t.Log(mysqlStore.GetAnchorCertAwardByUid(nil, uint32(ts.Unix()), 1))

}

// go test -timeout 30s -run ^TestGetAnchorCertCompetitionRecordList$ golang.52tt.com/services/anchor-contract/anchorcontract-go/mysql -v -count=1
func TestGetAnchorCertCompetitionRecordList(t *testing.T) {
	t.Log(mysqlStore.GetAnchorCertCompetitionRecordList(1, 0, false, 0, 10))
	t.Log(mysqlStore.GetAnchorCertCompetitionRecordList(1, 0, true, 0, 10))
}

// go test -timeout 30s -run ^TestUpdateAnchorCertCompetitionRecord$ golang.52tt.com/services/anchor-contract/anchorcontract-go/mysql -v -count=1
func TestUpdateAnchorCertCompetitionRecord(t *testing.T) {
	info := &AnchorCertCompetitionRecord{
		Uid:                          1,
		QuarterCompetitionStatus:     1,
		DoubleMonthCompetitionStatus: 1,
		Operator:                     "rr",
	}
	t.Log(mysqlStore.UpdateAnchorCertCompetitionRecord(nil, info))
}

// go test -timeout 30s -run ^TestAddAnchorCertCompetitionRecord$ golang.52tt.com/services/anchor-contract/anchorcontract-go/mysql -v -count=1
func TestAddAnchorCertCompetitionRecord(t *testing.T) {
	info := &AnchorCertCompetitionRecord{
		Uid:                          1,
		DoubleMonthCompetitionStatus: 1,
	}
	t.Log(mysqlStore.AddAnchorCertCompetitionRecord(nil, info))

	t.Log(mysqlStore.AddAnchorCertCompetitionRecordLog(nil, info))
}

// go test -timeout 30s -run ^TestDelAnchorCertUpgradeTask$ golang.52tt.com/services/anchor-contract/anchorcontract-go/mysql -v -count=1
func TestDelAnchorCertUpgradeTask(t *testing.T) {
	t.Log(mysqlStore.DelAnchorCertUpgradeTask(3))
}

// go test -timeout 30s -run ^TestGetAnchorCertUpgradeTaskList$ golang.52tt.com/services/anchor-contract/anchorcontract-go/mysql -v -count=1
func TestGetAnchorCertUpgradeTaskList(t *testing.T) {
	t.Log(mysqlStore.GetAnchorCertUpgradeTaskList(0, 0, "", 0, 1))
}

// go test -timeout 30s -run ^TestUpdateAnchorCertUpgradeTask$ golang.52tt.com/services/anchor-contract/anchorcontract-go/mysql -v -count=1
func TestUpdateAnchorCertUpgradeTask(t *testing.T) {
	t.Log(mysqlStore.UpdateAnchorCertUpgradeTask(&AnchorCertUpgradeTask{Id: 1}))
}

// go test -timeout 30s -run ^TestAddAnchorCertUpgradeTask$ golang.52tt.com/services/anchor-contract/anchorcontract-go/mysql -v -count=1
func TestAddAnchorCertUpgradeTask(t *testing.T) {

	info := &AnchorCertUpgradeTask{
		TaskType:         1,
		TaskName:         "name",
		TaskImgUrl:       "imgurl",
		ChannelId:        1,
		TaskInitialLevel: "1级",
		TaskTargetLevel:  "2级",
		TaskCheckMethod:  "xx",
		TaskJumpUrl:      "jumpurl",
		TaskCheckTime:    "周五",
		ShowBeginTime:    uint32(time.Now().Unix()),
		ShowEndTime:      uint32(time.Now().Unix()) + 86400*10,
		UpdateTime:       time.Now(),
	}
	err := mysqlStore.AddAnchorCertUpgradeTask(info)
	t.Log(err)

}

// GetContractByUids
// go test -timeout 30s -run ^TestGetContractByUids$ golang.52tt.com/services/anchor-contract/anchorcontract-go/mysql -v -count=1
func TestGetContractByUids(t *testing.T) {

	mysqlStore.GetContractByUids([]uint32{1, 2, 3}, 0, 10)
}

// go test -timeout 30s -run ^TestGetAnchorUidWithAgent$ golang.52tt.com/services/anchor-contract/anchorcontract-go/mysql -v -count=1
func TestGetAnchorUidWithAgent(t *testing.T) {
	m, err := mysqlStore.GetAnchorUidWithAgent(100154)
	t.Log(m, err)
}

// go test -timeout 30s -run ^TestGetGuildAllExtensionContract$ golang.52tt.com/services/anchor-contract/anchorcontract-go/mysql -v -count=1
func TestGetGuildAllExtensionContract(t *testing.T) {
	m, err := mysqlStore.GetGuildAllExtensionContract(1)
	t.Log(m, err)
}

// go test -timeout 30s -run ^TestAddExtensionContract$ golang.52tt.com/services/anchor-contract/anchorcontract-go/mysql -v -count=1
func TestAddExtensionContract(t *testing.T) {
	err := mysqlStore.AddExtensionContract(nil, &ExtensionContract{Uid: 1, GuildId: 1, ApplyTime: time.Now(), UpdateTime: time.Now()})
	t.Log(err)
}

// go test -timeout 30s -run ^TestGetGuildLiveAnchorTodayLive$ golang.52tt.com/services/anchor-contract/anchorcontract-go/mysql -v -count=1
func TestGetGuildLiveAnchorTodayLive(t *testing.T) {
	total, list, err := mysqlStore.GetGuildLiveAnchorTodayLive(999, []uint32{}, 0, 10)
	t.Log(total, list, err)
}

// go test -timeout 30s -run ^TestUpdateLiveAnchorDailyRecord$ golang.52tt.com/services/anchor-contract/anchorcontract-go/mysql -v -count=1
func TestUpdateLiveAnchorDailyRecord(t *testing.T) {
	err := mysqlStore.UpdateLiveAnchorDailyRecord(&LiveAnchorDailyRecord{
		Uid:     1,
		GuildId: 999,
		Date:    time.Now(),
	}, time.Now())
	t.Log(err)
}

// go test -timeout 30s -run ^TestFocusAnchor$ golang.52tt.com/services/anchor-contract/anchorcontract-go/mysql -v -count=1
func TestFocusAnchor(t *testing.T) {
	err := mysqlStore.FocusAnchor(&AnchorExtInfo{Uid: 1, GuildId: 999, IsFocus: 1, SignTime: time.Now(), ContractExpireTime: time.Now().AddDate(1, 0, 0), UpdateTime: time.Now()})
	t.Log(err)
}

// go test -timeout 30s -run ^TestAddAnchorExtInfo$ golang.52tt.com/services/anchor-contract/anchorcontract-go/mysql -v -count=1
func TestAddAnchorExtInfo(t *testing.T) {
	err := mysqlStore.AddAnchorExtInfo(&AnchorExtInfo{Uid: 1, GuildId: 999, SignTime: time.Now(), ContractExpireTime: time.Now().AddDate(1, 0, 0), UpdateTime: time.Now()})
	t.Log(err)
}

// go test -timeout 30s -run ^TestDeleteAnchorExtInfo$ golang.52tt.com/services/anchor-contract/anchorcontract-go/mysql -v -count=1
func TestDeleteAnchorExtInfo(t *testing.T) {
	err := mysqlStore.DeleteAnchorExtInfo(nil, 1)
	t.Log(err)
}

// go test -timeout 30s -run ^TestGetAnchorUidByAgent$ golang.52tt.com/services/anchor-contract/anchorcontract-go/mysql -v -count=1
func TestGetAnchorUidByAgent(t *testing.T) {
	uids, err := mysqlStore.GetAnchorUidByAgent(999, []uint32{11, 22})
	t.Log(uids, err)
}

// go test -timeout 30s -run ^TestGetGuildAnchorLastMonthScore$ golang.52tt.com/services/anchor-contract/anchorcontract-go/mysql -v -count=1
func TestGetGuildAnchorLastMonthScore(t *testing.T) {

	gid := uint32(100554)
	uids := []uint32{}
	offset := uint32(0)
	limit := uint32(20)

	total, uids, err := mysqlStore.GetGuildAnchorLastMonthScoreLessLimit(gid, uids, time.Now().AddDate(0, -1, 0), offset, limit)
	t.Log(total, uids, err)

	t.Log("\n\n")
	total, uids, err = mysqlStore.GetGuildAnchorLastMonthScoreLessLimit(gid, []uint32{2197152}, time.Now().AddDate(0, -1, 0), offset, limit)
	t.Log(total, uids, err)
}

// go test -timeout 30s -run ^TestBatchGetAnchorExtInfo$ golang.52tt.com/services/anchor-contract/anchorcontract-go/mysql -v -count=1
func TestBatchGetAnchorExtInfo(t *testing.T) {
	m, err := mysqlStore.BatchGetAnchorExtInfo([]uint32{1})
	t.Log(m, err)
}

// go test -timeout 30s -run ^TestGetGuildCancelSignRecordList$ golang.52tt.com/services/anchor-contract/anchorcontract-go/mysql -v -count=1
func TestGetGuildCancelSignRecordList(t *testing.T) {
	total, list, err := mysqlStore.GetGuildCancelSignRecordList(0, 2, []uint32{}, 0, 10)
	t.Log(total, list, err)
}

// go test -timeout 30s -run ^TestGetCancelContractApplyLog$ golang.52tt.com/services/anchor-contract/anchorcontract-go/mysql -v -count=1
func TestGetCancelContractApplyLog(t *testing.T) {
	info, err := mysqlStore.GetCancelContractApplyLog(1)
	t.Log(info, err)
}

// go test -timeout 30s -run ^TestAddCancelContractApplyLog$ golang.52tt.com/services/anchor-contract/anchorcontract-go/mysql -v -count=1
func TestAddCancelContractApplyLog(t *testing.T) {
	err := mysqlStore.AddCancelContractApplyLog(nil, &CancelContractApplyLog{
		Uid:                 1,
		GuildId:             2,
		SignTime:            time.Now(),
		ContractExpireTime:  time.Now(),
		Status:              0,
		Reason:              1,
		ApplyTime:           time.Now(),
		TagId:               1,
		LiveObtainTime:      time.Now(),
		MultiplayObtainTime: time.Now(),
		UpdateTime:          time.Now(),
	})
	t.Log(err)
}

// go test -timeout 30s -run ^TestDelAnchorExamineExpireRecord$ golang.52tt.com/services/anchor-contract/anchorcontract-go/mysql -v -count=1
func TestDelAnchorExamineExpireRecord(t *testing.T) {
	rowsAffected, err := mysqlStore.DelAnchorExamineExpireRecord(1, time.Now())
	t.Log(rowsAffected, err)
}

// go test -timeout 30s -run ^TestBatchGetUserExamineCertByItemIds$ golang.52tt.com/services/anchor-contract/anchorcontract-go/mysql -v -count=1
func TestBatchGetUserExamineCertByItemIds(t *testing.T) {
	m, _ := mysqlStore.BatchGetUserExamineCertByItemIds([]uint32{3, 2, 1})
	for id, info := range m {
		t.Logf("%d: %v\n", id, info)
	}
}

// go test -timeout 30s -run ^TestBatchGetUserExamineCertByRecordId$ golang.52tt.com/services/anchor-contract/anchorcontract-go/mysql -v -count=1
func TestBatchGetUserExamineCertByRecordId(t *testing.T) {
	m, _ := mysqlStore.BatchGetUserExamineCertByRecordId([]uint32{1, 2, 3})
	for id, info := range m {
		t.Logf("%d: %v\n", id, info)
	}
}

// go test -timeout 30s -run ^TestBatchGetExamineCertByItemId$ golang.52tt.com/services/anchor-contract/anchorcontract-go/mysql -v -count=1
func TestBatchGetExamineCertByItemId(t *testing.T) {
	m, _ := mysqlStore.BatchGetExamineCertByItemId([]uint32{1})
	for id, info := range m {
		t.Logf("%d: %v\n", id, info)
	}
}

// go test -timeout 30s -run ^TestBatchUpdatetUserExamineCertStatus$ golang.52tt.com/services/anchor-contract/anchorcontract-go/mysql -v -count=1
func TestBatchUpdatetUserExamineCertStatus(t *testing.T) {
	err := mysqlStore.BatchUpdatetUserExamineCertStatus([]uint32{1, 2, 3}, 1)
	t.Log(err)
}

// go test -timeout 30s -run ^TestGetGuildSignAnchorCnt$ golang.52tt.com/services/anchor-contract/anchorcontract-go/mysql -v -count=1
func TestGetGuildSignAnchorCnt(t *testing.T) {
	gid := uint32(123)
	cnt, err := mysqlStore.GetGuildSignAnchorCnt(gid, time.Now())
	t.Logf("%d %v\n", cnt, err)

	gid = uint32(111)
	cnt, err = mysqlStore.GetGuildSignAnchorCnt(gid, time.Now())
	t.Logf("%d %v\n", cnt, err)
}

// go test -timeout 30s -run ^TestExamineRecord$ golang.52tt.com/services/anchor-contract/anchorcontract-go/mysql -v -count=1
func TestExamineRecord(t *testing.T) {
	uid := uint32(111)
	err := mysqlStore.AddAnchorExamineRecord(&AnchorExamineRecord{
		Uid: uid,
		//GuildId:    123,
		//SignTime:   time.Now(),
		UpdateTime: time.Now(),
	})
	if err != nil {
		log.Errorf("fail to AddAnchorExamineRecord err:%+v", err)
	}
}

// go test -timeout 30s -run ^TestGetAnchorExamineNotUploadRecord$ golang.52tt.com/services/anchor-contract/anchorcontract-go/mysql -v -count=1
func TestGetAnchorExamineNotUploadRecord(t *testing.T) {
	list, err := mysqlStore.GetAnchorExamineNotUploadRecord(uint32(time.Now().Unix()-3600), uint32(time.Now().Unix()))
	t.Logf("%v %v\n", list, err)
}

// go test -timeout 30s -run ^TestGetAllLiveAnchorExamine$ golang.52tt.com/services/anchor-contract/anchorcontract-go/mysql -v -count=1
func TestGetAllLiveAnchorExamine(t *testing.T) {
	// GetAllLiveAnchorExamine(begin, limit, fromTime, toTime uint32, statusList []uint32)

	cnt, _ := mysqlStore.GetAllLiveAnchorExamineCnt([]uint32{0}, 1646064000, 1647791999)
	t.Logf("cnt %d\n", cnt)

	list, _ := mysqlStore.GetAllLiveAnchorExamine(0, 10, 1646064000, 1647791999, []uint32{0})
	for _, info := range list {
		t.Logf("%+v\n", info)
	}
}

// go test -timeout 30s -run ^TestCreate$ golang.52tt.com/services/anchor-contract/anchorcontract-go/mysql -v -count=1
func TestCreate(t *testing.T) {
	// AddContractApply

	err := mysqlStore.AddContractApply(nil, &ContractApply{
		Uid:              1,
		GuildId:          101,
		ContractDuration: 3,
		IdentityType:     1,
		IdentityNum:      "11111",
		ApplyStatus:      uint32(pb.APPLY_SIGN_STATUS_APPLY_SIGN_STATUS_PRESIDENT_HANDLING),
		ApplyTime:        time.Now(),
		UpdateTime:       time.Now(),
		FormExtra:        "",
		Age:              10,
		RechargeNum:      3000,
	})
	t.Logf("%v\n", err)
}

// go test -timeout 30s -run ^TestGetNotUploadExamineAnchorList$ golang.52tt.com/services/anchor-contract/anchorcontract-go/mysql
func TestGetNotUploadExamineAnchorList(t *testing.T) {
	beginTs := time.Date(2022, 4, 10, 0, 0, 0, 0, time.Local)
	endTs := time.Date(2022, 4, 15, 0, 0, 0, 0, time.Local)
	list, err := mysqlStore.GetAnchorExamineNotUploadRecord(uint32(beginTs.Unix()), uint32(endTs.Unix()))
	if err != nil {
		t.Logf("%s\n", err)
		return
	}

	t.Logf("count %d\n", len(list))
	for _, info := range list {
		t.Logf("%+v\n", info)
	}
}

// go test -timeout 30s -run ^TestMultiExamine$ golang.52tt.com/services/anchor-contract/anchorcontract-go/mysql -v -count=1
func TestMultiExamine(t *testing.T) {

	uid := uint32(102)
	guildid := uint32(1063307)

	info := &ContractApply{
		Uid:              uid,
		GuildId:          guildid,
		ContractDuration: 6,
		IdentityType:     1,
		IdentityNum:      "440204199610287037",
		ApplyStatus:      uint32(pb.APPLY_SIGN_STATUS_APPLY_SIGN_STATUS_OFFICIAL_HANDLING),
		ApplyTime:        time.Now(),
		UpdateTime:       time.Now(),
		//	HandlerTime:      time.Now(),
		Extra:         "{}",
		ConformStatus: 1,
	}

	for i := uint32(0); i < 25; i++ {
		info.Id = 0
		info.Uid = uid + i
		if i > 10 {
			info.ConformStatus = 0
		}
		//mysqlStore.AddContractApply(nil, info)
	}
}

// go test -timeout 30s -run ^TestCreateId$ golang.52tt.com/services/anchor-contract/anchorcontract-go/mysql -v -count=1
func TestCreateId(t *testing.T) {

	var start1 time.Time
	start1, _ = time.ParseInLocation("2006-01-02 15:04:05", "2022-03-31 09:00:00", time.Local)

	cert := &AnchorExamineCert{
		Uid:          11,
		Ttid:         "11",
		ItemId:       1,
		IdentityType: 1,
		StartTime:    start1,
		EndTime:      start1,
		UpdateTime:   start1,
	}

	t.Logf("cert: %+v\n", cert)
	mysqlStore.db.Create(cert)
	t.Logf("cert: %+v\n", cert)
}

// go test -timeout 30s -run ^TestGetUserExamineCertByQuery$ golang.52tt.com/services/anchor-contract/anchorcontract-go/mysql -v -count=1
func TestGetUserExamineCertByQuery(t *testing.T) {
	list, err := mysqlStore.GetUserExamineCertByQuery("identity_type=0", 0, 10)
	t.Logf("%+v, %v\n", list, err)
}

// go test -timeout 30s -run ^TestGetUserExamineCertListByItemId$ golang.52tt.com/services/anchor-contract/anchorcontract-go/mysql -v -count=1
func TestGetUserExamineCertListByItemId(t *testing.T) {
	list, err := mysqlStore.GetUserExamineCertListByItemId(1)
	t.Logf("%+v, %v\n", list, err)
}

// go test -timeout 30s -run ^TestGetExamineCertByQuery$ golang.52tt.com/services/anchor-contract/anchorcontract-go/mysql -v -count=1
func TestGetExamineCertByQuery(t *testing.T) {
	list, err := mysqlStore.GetExamineCertByQuery("")
	t.Logf("%+v, %v\n", list, err)
}

// go test -timeout 30s -run ^TestAddParentExamineCert$ golang.52tt.com/services/anchor-contract/anchorcontract-go/mysql -v -count=1
func TestAddParentExamineCert(t *testing.T) {
	t.Log(mysqlStore.AddParentExamineCert(0, "aa1", "bb", "", "", 1, uint32(time.Now().Unix()), uint32(time.Now().Unix())))
}

// go test -timeout 30s -run ^TestGetParentExamineCertList$ golang.52tt.com/services/anchor-contract/anchorcontract-go/mysql -v -count=1
func TestGetParentExamineCertList(t *testing.T) {
	list, err := mysqlStore.GetParentExamineCertList(0, 10, 0)
	t.Logf("%+v,%v\n", list, err)
}

/*
// go test -timeout 30s -run ^TestGetUserExamineCertListByUid$ golang.52tt.com/services/anchor-contract/anchorcontract-go/mysql -v -count=1
func TestGetUserExamineCertListByUid(t *testing.T) {
	list, err := mysqlStore.GetUserExamineCertListByUid(2286680, 0)
	t.Logf("%+v,%v\n", list, err)
}

// go test -timeout 30s -run ^TestGetExamineCert$ golang.52tt.com/services/anchor-contract/anchorcontract-go/mysql -v -count=1
func TestGetExamineCert(t *testing.T) {
	//info, err := mysqlStore.GetExamineCert(1, 2)
	//t.Logf("%+v,%v\n", info, err)
}

// go test -timeout 30s -run ^TestSetUserExamineCert$ golang.52tt.com/services/anchor-contract/anchorcontract-go/mysql -v -count=1
func TestSetUserExamineCert(t *testing.T) {

	now := uint32(time.Now().Unix())
	id, err := mysqlStore.SetUserExamineCert(1111, 1, 0, "ttid", "", now, now+3600)
	t.Logf("%v,%v\n", id, err)
}
*/

/*
func TestGetContractWithUid(t *testing.T) {
	mysqlStore.GetContractWithUid(2196173)
}

func TestStore_GetContractWithUid(t *testing.T) {
	t.Log(mysqlStore.GetValidContractWithUid(10000))
}

func TestStore_AddContract(t *testing.T) {
	t.Log(mysqlStore.AddContract(nil, &ContractInfo{
		Uid:                10001,
		GuildId:            102128,
		IdentityNum:        "440204199610287037",
		ContractDuration:   6,
		SignTime:           time.Now(),
		ContractExpireTime: time.Now().AddDate(0, 6, 0),
		UpdateTime:         time.Now(),
	}))
}

func TestStore_GetGuildContractWithUidList(t *testing.T) {
	list, err := mysqlStore.GetGuildContractWithUidList(102128, []uint32{10000, 10001, 2210427})
	t.Log(err)
	for _, info := range list {
		t.Logf("%+v", info)
	}
}

func TestStore_GetContractWithIdentityNum(t *testing.T) {
	t.Log(mysqlStore.GetContractWithIdentityNum("0"))
}

func TestStore_GetExpireContract(t *testing.T) {
	list, err := mysqlStore.GetExpireContract(time.Now().AddDate(0, 6, 0))
	t.Log(err)
	for _, info := range list {
		t.Logf("%+v", info)
	}
}

func TestStore_ContractExtension(t *testing.T) {
	t.Log(mysqlStore.ContractExtension(nil, 10000, 102128, 6, time.Now(), time.Now().AddDate(0, 12, 0)))
}

func TestStore_DelContract(t *testing.T) {
	t.Log(mysqlStore.DelContract(nil, 10000, 102128))
}

func TestStore_GetContractChangeLogWithUid(t *testing.T) {
	list, err := mysqlStore.GetContractChangeLogWithUid(2210427, 0, time.Now().AddDate(0, 0, -1), time.Now())
	t.Log(err)
	for _, info := range list {
		t.Logf("%+v", info)
	}
}

// go test -timeout 30s -run ^TestStore_GetContractChangeLogWithIdentityNum$ golang.52tt.com/services/anchor-contract/anchorcontract-go/mysql -v -count=1
func TestStore_GetContractChangeLogWithIdentityNum(t *testing.T) {
	now := time.Now()
	yearBegin := time.Date(now.Year(), 1, 1, 0, 0, 0, 0, time.Local)
	list, err := mysqlStore.GetContractChangeLogWithIdNum("610527199403044529", 0, yearBegin, time.Now())
	t.Log(err)
	for _, info := range list {
		t.Logf("%+v", info)
	}
}

func TestStore_RecordContractChangeLog(t *testing.T) {
	t.Log(mysqlStore.RecordContractChangeLog(nil, &ContractChangeLog{
		Uid:                2210427,
		GuildId:            102821,
		IdentityNum:        "440204199610287037",
		SignTime:           time.Now(),
		ChangeTime:         time.Now(),
		ChangeDesc:         "test2",
		ChangeType:         0,
		ContractDuration:   6,
		ContractExpireTime: time.Now().AddDate(0, 6, 0),
	}))
}

func TestStore_GetAnchorIdentity(t *testing.T) {
	list, err := mysqlStore.GetAnchorIdentity(nil, 2210427)
	t.Log(err)
	for _, info := range list {
		t.Logf("%+v", info)
	}
}

func TestStore_GetAnchorIdentityWithType(t *testing.T) {
	t.Log(mysqlStore.GetAnchorIdentityWithType(2210427, 1))
}

func TestStore_GetGuildUidListWithIdentity(t *testing.T) {
	t.Log(mysqlStore.GetGuildUidListWithIdentity(102821, 0, 0, 2))
}

func TestStore_AddUserAnchorIdentity(t *testing.T) {
	t.Log(mysqlStore.AddUserAnchorIdentity(context.Background(), nil, 100, 102821, 1, time.Now(), time.Now(), time.Now(), "test"))
}

func TestStore_DelUserAnchorIdentity(t *testing.T) {
	t.Log(mysqlStore.DelUserAnchorIdentity(context.Background(), nil, 100, 102821, 1, time.Now(), time.Now(), time.Now(), "test"))
}

func TestStore_GetAnchorIdentityChangeLog(t *testing.T) {
	list, err := mysqlStore.GetAnchorIdentityChangeLog(100, 1, 0)
	t.Log(err)
	for _, info := range list {
		t.Logf("%+v", info)
	}
}

func TestStore_GetGuildAnchorIdentityChangeLog(t *testing.T) {
	list, err := mysqlStore.GetGuildAnchorIdentityChangeLog(102821, 1, 1, 1, 0)
	t.Log(err)
	for _, info := range list {
		t.Logf("%+v", info)
	}
}

func TestStore_DelAnchorAllIdentity(t *testing.T) {
	t.Log(mysqlStore.DelAnchorAllIdentity(nil, 2210427))
}

func TestStore_AddContractApply(t *testing.T) {
	t.Log(mysqlStore.AddContractApply(nil, &ContractApply{
		Uid:              102,
		GuildId:          102821,
		ContractDuration: 6,
		IdentityType:     1,
		IdentityNum:      "440204199610287037",
		ApplyStatus:      uint32(pb.APPLY_SIGN_STATUS_APPLY_SIGN_STATUS_PRESIDENT_HANDLING),
		ApplyTime:        time.Now(),
		UpdateTime:       time.Now(),
		//HandlerTime:      time.Now(),
		Extra: "{}",
	}))
}

func TestStore_GetContractApplyWithId(t *testing.T) {
	t.Log(mysqlStore.GetContractApplyWithId(nil, 1))
}

func TestStore_GetUserContractApplyList(t *testing.T) {
	list, err := mysqlStore.GetUserContractApplyList(101, 0, 5, []uint32{5, 6}, true)
	t.Log(err)
	for _, info := range list {
		t.Logf("%+v", info)
	}
}

func TestStore_GetContractApplyWithUid(t *testing.T) {
	list, err := mysqlStore.GetContractApplyWithUid(100, []uint32{1})
	t.Log(err)
	for _, info := range list {
		t.Logf("%+v", info)
	}
}

func TestStore_GetContractApplyWithIdNum(t *testing.T) {
	list, err := mysqlStore.GetContractApplyWithIdNum("440204199610287037", []uint32{0, 1})
	t.Log(err)
	for _, info := range list {
		t.Logf("%+v", info)
	}
}

func TestStore_UpdateContractApplyStatusWithId(t *testing.T) {
	t.Log(mysqlStore.UpdateContractApplyStatusWithId(nil, 7, 0, 6, "test", "test"))
}

func TestStore_UpdateContractApplyStatus(t *testing.T) {
	t.Log(mysqlStore.UpdateContractApplyStatus(nil, 2210427, 1, []uint32{0, 2}, 3))
}

func TestStore_GetGuildContractApplyCnt(t *testing.T) {
	//t.Log(mysqlStore.GetGuildContractApplyCnt(102821, []uint32{0, 1}, []uint32{0, 3}))
}

func TestStore_GetGuildContractApplyList(t *testing.T) {
	/*
		list, err := mysqlStore.GetGuildContractApplyListWithType(102821, 1, 0, 2, []uint32{5, 6})
		t.Log(err)
		for _, info := range list {
			t.Logf("%+v", info)
		}
	*/
//}

/*
func TestStore_AddUserMonthScore(t *testing.T) {
	t.Log(mysqlStore.AddUserMonthScore(nil, 1, 1, 1, time.Now().AddDate(0, 1, 0)))
	t.Log(mysqlStore.AddUserMonthScore(nil, 2, 1, 1, time.Now().AddDate(0, 1, 0)))
	t.Log(mysqlStore.AddUserMonthScore(nil, 2, 1, 1, time.Now().AddDate(0, 1, 0)))
}

func TestStore_BatchGetGuildUserMonthScore(t *testing.T) {
	t.Log(mysqlStore.BatchGetGuildUserMonthScore(1, []uint32{1, 2, 3}, time.Now().AddDate(0, 1, 0)))
}

func TestStore_AddLiveAnchorExamine(t *testing.T) {
	t.Log(mysqlStore.AddLiveAnchorExamine(nil, &LiveAnchorExamine{
		Uid:          100,
		GuildId:      102821,
		ChannelTagId: 123,
		Status:       0,
		IsSetTime:    false,
		Giver:        "test",
	}))
}

func TestStore_GetUserLiveAnchorExamine(t *testing.T) {
	list, err := mysqlStore.GetUserLiveAnchorExamine(100, []uint32{1})
	t.Log(err)
	for _, info := range list {
		t.Logf("%+v", info)
	}
}

func TestStore_GetGuildLiveAnchorExamine(t *testing.T) {
	//list, err := mysqlStore.GetGuildLiveAnchorExamine(102821, 0, 2, []uint32{1})
	//t.Log(err)
	//for _, info := range list {
	//	t.Logf("%+v", info)
	//}
}

func TestStore_UpdateLiveAnchorExamineStatus(t *testing.T) {
	t.Log(mysqlStore.UpdateLiveAnchorExamineStatus(nil, 1, 0, "test", "test"))
}

func TestStore_UpdateLiveAnchorExamineTime(t *testing.T) {
	t.Log(mysqlStore.UpdateLiveAnchorExamineTime(nil, 1, time.Now()))
}

func TestStore_GetTimeoutLiveAnchorExamine(t *testing.T) {
	list, err := mysqlStore.GetTimeoutLiveAnchorExamine(time.Now(), 1)
	t.Log(err)
	for _, info := range list {
		t.Logf("%+v", info)
	}
}

func TestStore_AddUserToApplyBlacklist(t *testing.T) {
	t.Log(mysqlStore.AddUserToApplyBlacklist(nil, &ApplyBlacklist{
		Uid:          1,
		IdentityType: 0,
		BeginTime:    time.Now(),
		EndTime:      time.Now().Add(150 * time.Hour),
	}))
	t.Log(mysqlStore.AddUserToApplyBlacklist(nil, &ApplyBlacklist{
		Uid:          1,
		IdentityType: 1,
		BeginTime:    time.Now(),
		EndTime:      time.Now().Add(150 * time.Hour),
	}))
}
 */