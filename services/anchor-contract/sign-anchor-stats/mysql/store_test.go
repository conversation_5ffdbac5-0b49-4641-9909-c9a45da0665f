package mysql

/*
import (
	"fmt"
	"testing"
	"time"

	"github.com/jinzhu/gorm"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/services/anchor-contract/sign-anchor-stats/conf"
)

var mysqlStore *Store

func init() {
	log.SetLevel(log.DebugLevel)

	sc := &conf.ServiceConfigT{}
	err := sc.Parse("../sign-anchor-stats.json")
	if err != nil {
		return
	}

	fmt.Printf("string:%s\n", sc.MysqlConfig.ConnectionString())
	mysqlDb, err := gorm.Open("mysql", sc.MysqlConfig.ConnectionString())
	if err != nil {
		fmt.Printf("Failed to Connect mysql %v", err)
		return
	}

	userPresentSlaveDB, err := gorm.Open("mysql", sc.GetUserPresentSlaveDBConfig().ConnectionString())
	if err != nil {
		log.Errorf("Failed to create userPresentSlaveDB %v", err)
		return
	}

	mysqlStore = NewMysql(mysqlDb.Debug(), mysqlDb.Debug(), userPresentSlaveDB.Debug())
	mysqlStore.CreateTable()

	fmt.Printf("table err:%+v", err)
}

// go test -timeout 30s -run ^TestGetMultiAnchorChannelList$ golang.52tt.com/services/anchor-contract/sign-anchor-stats/mysql -v -count=1
func TestGetMultiAnchorChannelList(t *testing.T) {
	tm := time.Date(2023, 8, 21, 0, 0, 0, 0, time.Local)
	uids, _ := mysqlStore.GetMultiAnchorChannelList(1, tm, tm)
	t.Log(uids)
}

/*
// go test -timeout 30s -run ^TestGetMultiAnchorDailysStats$ golang.52tt.com/services/anchor-contract/sign-anchor-stats/mysql -v -count=1
func TestGetMultiAnchorDailysStats(t *testing.T) {
	now := time.Now()
	endT := time.Date(now.Year(), now.Month(), now.Day()-1, 0, 0, 0, 0, time.Local)
	beginT := time.Date(now.Year(), now.Month(), now.Day()-19, 0, 0, 0, 0, time.Local)
	date2IncomeInfo, _, _, err := mysqlStore.GetMultiAnchorDailysStats(1, beginT, endT)
	t.Log(date2IncomeInfo, err)
}

// go test -timeout 30s -run ^TestGetMultiAnchorDailyStatsNotGuild$ golang.52tt.com/services/anchor-contract/sign-anchor-stats/mysql -v -count=1
func TestGetMultiAnchorDailyStatsNotGuild(t *testing.T) {
	mysqlStore.GetMultiAnchorDailyStatsNotGuild(1, time.Now())
	mysqlStore.GetMultiAnchorMonthlyStatsNotGuild(1, time.Now())
}

// // select count(1) from user_present_monthly_history_202211 where to_uid=2459278 and from_uid=2531496;

// go test -timeout 30s -run ^TestCheckIfFirstSendGift$ golang.52tt.com/services/anchor-contract/sign-anchor-stats/mysql -v -count=1
func TestCheckIfFirstSendGift(t *testing.T) {
	cnt, err := mysqlStore.CheckIfFirstSendGift(2531496, 2459278, time.Now())
	t.Log(cnt, err)
}

// GetMultiAnchorMonthlyStatsByTime
// go test -timeout 30s -run ^TestGetMultiAnchorMonthlyStatsByTime$ golang.52tt.com/services/anchor-contract/sign-anchor-stats/mysql -v -count=1
func TestGetMultiAnchorMonthlyStatsByTime(t *testing.T) {

}

// go test -timeout 30s -run ^TestStore_GetMultiAnchorDailyStats$ golang.52tt.com/services/anchor-contract/sign-anchor-stats/mysql -v -count=1
func TestStore_GetMultiAnchorDailyStats(t *testing.T) {
	t.Log(mysqlStore.GetMultiAnchorDailyStats(context.Background(), nil, 1, 1, time.Now()))
}

// go test -timeout 30s -run ^TestGetMultiPlayerUkwPresentTempRecord$ golang.52tt.com/services/anchor-contract/sign-anchor-stats/mysql -v -count=1
func TestGetMultiPlayerUkwPresentTempRecord(t *testing.T) {

	now := time.Now()
	tm := time.Date(now.Year(), now.Month(), now.Day(), 3, 0, 0, 0, time.Local)

	list, err := mysqlStore.GetMultiPlayerUkwPresentTempRecord(tm)
	if err != nil {
		t.Log(err)
		return
	}

	for _, info := range list {
		t.Logf("info %+v\n", info)
	}
}

// go test -timeout 30s -run ^TestAddMultiPlayerUkwPresentRecord$ golang.52tt.com/services/anchor-contract/sign-anchor-stats/mysql -v -count=1
func TestAddMultiPlayerUkwPresentRecord(t *testing.T) {

	tm, _ := time.ParseInLocation("2006-01-02 15:04:05", "2022-11-20 08:54:22", time.Local)

	err := mysqlStore.AddMultiPlayerUkwPresentRecord(&MultiPlayerUkwPresentRecord{
		OrderId:    "a2",
		Uid:        1,
		ToUid:      1,
		TotalPrice: 1,
		SendTime:   tm,
		CreateTime: time.Now(),
	})
	t.Log(err)

}

// go test -timeout 30s -run ^TestGetMultiAnchorDailyStatsListByGuildId$ golang.52tt.com/services/anchor-contract/sign-anchor-stats/mysql -v -count=1
func TestGetMultiAnchorDailyStatsListByGuildId(t *testing.T) {
	list, err := mysqlStore.GetMultiAnchorDailyStatsListByGuildId(context.TODO(), 1, []uint32{123}, time.Now().AddDate(0, 0, -30), time.Now())
	t.Log(list, err)
}

// go test -timeout 30s -run ^TestCreate$ golang.52tt.com/services/anchor-contract/sign-anchor-stats/mysql -v -count=1
func TestCreate(t *testing.T) {
	mysqlStore.IncrUserPresentSendDailyStats(1, 100, 4)
	mysqlStore.IncrUserPresentSendDailyStats(1, 100, 7)
}

// go test -timeout 30s -run ^TestGetUserTbeanConsume$ golang.52tt.com/services/anchor-contract/sign-anchor-stats/mysql -v -count=1
func TestGetUserTbeanConsume(t *testing.T) {

	t2 := time.Now().AddDate(0, 0, 1)
	t1 := t2.AddDate(0, 0, -2)
	total, err := mysqlStore.GetUserTbeanConsume(1, uint32(t1.Unix()), uint32(t2.Unix()))
	t.Logf("%d %v\n", total, err)
}

func TestStore_GetMultiAnchorDailyStatsList(t *testing.T) {
	list, _, err := mysqlStore.GetMultiAnchorDailyStatsList(context.Background(), 0, []uint32{}, time.Now(), time.Now(), 0, 10)
	t.Log(err)
	for _, info := range list {
		t.Logf("%+v", info)
	}
}



func TestStore_SetMultiAnchorValidDay(t *testing.T) {
	t.Log(mysqlStore.SetMultiAnchorValidDay(context.Background(), nil, 1, 1, time.Now(), true))
}

func TestStore_GetMultiAnchorMonthlyStats(t *testing.T) {
	t.Log(mysqlStore.GetMultiAnchorMonthlyStats(context.Background(), 1, 1, time.Now()))
}

func TestStore_GetMultiAnchorMonthlyStatsList(t *testing.T) {
	list, err := mysqlStore.GetMultiAnchorMonthlyStatsList(context.Background(), 0, 5, time.Now())
	t.Log(err)
	for _, info := range list {
		t.Logf("%+v", info)
	}
}

func TestStore_IncrMultiAnchorMonthlyStats(t *testing.T) {
	t.Log(mysqlStore.IncrMultiAnchorMonthlyStats(context.Background(), nil, &MultiAnchorMonthlyStats{
		Uid:          1,
		SignGuildId:  1,
		AnchorIncome: 20,
		ValidSec:     60,
		Yearmonth:    GetYearMonth(time.Now()),
		ValidDayCnt:  1,
	}))
}

func TestStore_GetAnchorRechargeMonthlyStats(t *testing.T) {
	t.Log(mysqlStore.GetAnchorRechargeMonthlyStats(context.Background(), 1, time.Now()))
}

func TestStore_IncrAnchorRechargeMonthlyStats(t *testing.T) {
	t.Log(mysqlStore.IncrAnchorRechargeMonthlyStats(context.Background(), nil, &AnchorRechargeMonthlyStats{
		Uid:         1,
		RechargeNum: 20,
		Yearmonth:   GetYearMonth(time.Now()),
	}))
}

func TestGetYearMonth(t *testing.T) {
	t.Log(GetYearMonth(time.Now()))
}

func TestStore_GetBindGuildId(t *testing.T) {
	t.Log(mysqlStore.GetBindGuildId(context.Background(), 0))
}
*/
