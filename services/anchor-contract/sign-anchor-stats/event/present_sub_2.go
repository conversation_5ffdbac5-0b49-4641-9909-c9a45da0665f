package event

import (
	"context"
	"fmt"
	"gitlab.ttyuyin.com/avengers/tyr/compatible/proto"
	"gitlab.ttyuyin.com/tt-infra/middleware/kafka/subscriber"
	"golang.52tt.com/clients/channel"
	eventlink "golang.52tt.com/pkg/event-link-wrap"
	"golang.52tt.com/pkg/log"
	channelPb "golang.52tt.com/protocol/app/channel"
	"golang.52tt.com/protocol/services/minToolkit/kafka/pb/kafkapresent"
	presentPb "golang.52tt.com/protocol/services/userpresent"
	"golang.52tt.com/services/anchor-contract/sign-anchor-stats/manager"
)

const topicPresentEvent2 = "present_event_v2"

type PresentEventSub2 struct {
	kafkaSub   *eventlink.EventLinkAsyncSub
	channelCli *channel.Client
	mgr        *manager.SignAnchorStatsMgr
}

func NewPresentEventSub2(clientId, groupId string, topics, brokers []string, mgr *manager.SignAnchorStatsMgr) (*PresentEventSub2, error) {

	log.Infof("kafka_config Topics:%s, Brokers:%s", topics, brokers)
	channelCli := channel.NewClient()
	sub := &PresentEventSub2{
		channelCli: channelCli,
		mgr:        mgr,
	}

	option := []eventlink.Option{
		eventlink.WithMaxRetryTimes(5),
		eventlink.WithProcessWorkerNum(10),
	}
	kafkaSub, err := eventlink.NewEventLinkAsyncSub2(brokers, topics, groupId,
		clientId, sub.handlerEvent, option...)
	if err != nil {
		log.Errorf("Failed to create kafka-subscriber %+v", err)
		return nil, err
	}
	sub.kafkaSub = kafkaSub
	return sub, nil
}

func (s *PresentEventSub2) Close() {
	s.kafkaSub.Stop()
}

func (s *PresentEventSub2) handlerEvent(ctx context.Context, msg *subscriber.ConsumerMessage) (error, bool) {
	log.DebugWithCtx(ctx, "handlerEvent topic:%s", msg.Topic)

	switch msg.Topic {
	case topicPresentEvent:
		return s.handlerPresentEvent(ctx, msg)
	}
	return nil, false
}

func (s *PresentEventSub2) handlerPresentEvent(ctx context.Context, msg *subscriber.ConsumerMessage) (error, bool) {
	presentEvent := &kafkapresent.PresentEvent{}
	err := proto.Unmarshal(msg.Value, presentEvent)
	if err != nil {
		log.ErrorWithCtx(ctx, " handlerPresentEvent Failed to proto.Unmarshal err(%v)", err)
		return err, false
	}

	log.DebugWithCtx(ctx, "handlerPresentEvent %+v", presentEvent)

	if presentEvent.GetPriceType() != uint32(presentPb.PresentPriceType_PRESENT_PRICE_TBEAN) {
		log.DebugWithCtx(ctx, "handlerPresentEvent no need proc ev:%+v", presentEvent)
		return nil, false
	}

	if presentEvent.GetChannelType() != uint32(channelPb.ChannelType_GUILD_PUBLIC_FUN_CHANNEL_TYPE) &&
		presentEvent.GetChannelType() != uint32(channelPb.ChannelType_RADIO_LIVE_CHANNEL_TYPE) &&
		presentEvent.GetChannelType() != uint32(channelPb.ChannelType_OFFICIAL_LIVE_CHANNEL_TYPE) {
		log.DebugWithCtx(ctx, "handlerPresentEvent no need proc ev:%+v", presentEvent)
		return nil, false
	}

	key := fmt.Sprintf("present_sub_2-%s", presentEvent.GetOrderId())
	if ok, err := s.mgr.RedisCache.CheckOrderId(key); err != nil || !ok {
		log.Errorf("handlerPresentEvent ignore key:%v ok %v", key, err, ok)
		return nil, false
	}

	s.mgr.HandleUserInteractGift(ctx, presentEvent)

	return nil, false
}
