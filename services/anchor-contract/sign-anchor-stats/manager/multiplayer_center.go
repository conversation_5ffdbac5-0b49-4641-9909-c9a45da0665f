package manager

import (
	"context"
	"time"

	"gitlab.ttyuyin.com/avengers/tyr/compatible/proto"
	"golang.52tt.com/pkg/datahouse"
	utils2 "golang.52tt.com/pkg/foundation/utils"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/mapreduce"
	"golang.52tt.com/pkg/metrics"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/urrc"
	accountPB "golang.52tt.com/protocol/services/accountsvr"
	anchorgoPB "golang.52tt.com/protocol/services/anchorcontract-go"
	guildPB "golang.52tt.com/protocol/services/guildsvr"
	imApiPB "golang.52tt.com/protocol/services/im-api"
	"golang.52tt.com/protocol/services/minToolkit/kafka/pb/kafkapresent"
	pb "golang.52tt.com/protocol/services/sign-anchor-stats"
	ukwPB "golang.52tt.com/protocol/services/youknowwho"
	utilsX "golang.52tt.com/services/anchor-contract/anchorcontract-go/util"
	"golang.52tt.com/services/anchor-contract/sign-anchor-stats/cache"
	"golang.52tt.com/services/anchor-contract/sign-anchor-stats/filter"
	api "golang.52tt.com/services/anchor-contract/sign-anchor-stats/gen-go"
	"golang.52tt.com/services/anchor-contract/sign-anchor-stats/mysql"
	"golang.52tt.com/services/anchor-contract/sign-anchor-stats/utils"
)

type GuildMemberInfo struct {
	TodayLoginDuration uint32 // 今日活跃时长 秒
	TodayNewRelation   uint32 // 今日新增关系链

	WeekLoginDays        uint32 // 本周有效活跃天数
	WeekNewRelation      uint32 // 本周新增关系链
	WeekNewValidRelation uint32 // 本周新增有效关系链

	MonthLoginDays        uint32 // 本月有效活跃天数
	MonthFollowAccum      uint32 // 本月累计关注
	MonthNewFocusCnt      uint32 // 本月新增关注
	MonthLossFriendCnt    uint32 // 本月流失玩伴
	MonthLossFocusCnt     uint32 // 本月流失关注
	MonthNewRelation      uint32 // 本月新增关系链
	MonthNewValidRelation uint32 // 本月新增有效关系链
}

func (t *GuildMemberInfo) GetTodayLoginDuration() uint32 {
	if t == nil {
		return 0
	}
	return t.TodayLoginDuration
}
func (t *GuildMemberInfo) GetTodayNewRelation() uint32 {
	if t == nil {
		return 0
	}
	return t.TodayNewRelation
}

// 今日，本月 数仓接口聚合
func (m *SignAnchorStatsMgr) GetGuildMemberTodayInfo(ctx context.Context, tm time.Time, uid, guildId uint32) (
	resp *GuildMemberInfo, err error) {

	defer metrics.DISCOVERY_FUNC_TRACK(protocol.NewServerError(0, "GetGuildMemberTodayInfo")).End()

	resp = &GuildMemberInfo{}

	//todayStr := tm.Format("20060102")
	//monthBegin, monthEnd := utils.GetMonthTime(tm)

	// 今日的数据 （活跃时长）
	todayInfoList, err := datahouse.QueryMemberDailyInfo(ctx, uid, tm, tm)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetGuildMemberTodayInfo QueryMemberDailyInfo fail err %v, uid %d", err, uid)
		return
	}
	if len(todayInfoList) > 0 {
		resp.TodayLoginDuration = todayInfoList[0].LoginDuration
	}

	// 今日的数据 （有效关系链）
	todayChainInfoList, err := datahouse.QueryMemberChainDailyInfo(ctx, uid, tm, tm)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetGuildMemberTodayInfo QueryGuildMemberChainInfo fail err %v, uid %d", err, uid)
		return
	}
	if len(todayChainInfoList) > 0 {
		resp.TodayNewRelation = todayChainInfoList[0].ChainCnt
	}

	// 本月的数据 （关注数、玩伴数）
	monthInfoList, err := datahouse.QueryGuildMemberMonthInfo(ctx, []uint32{uid}, 0, tm, tm)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetGuildMemberTodayInfo QueryGuildMemberMonthInfo fail err %v, uid %d", err, uid)
		return
	}
	if len(monthInfoList) > 0 {
		resp.MonthLoginDays = monthInfoList[0].LoginDays
		resp.MonthNewFocusCnt = monthInfoList[0].AddFollowCnt
		resp.MonthLossFriendCnt = monthInfoList[0].LostFriendCnt
		resp.MonthLossFocusCnt = monthInfoList[0].LostFollowCnt
		resp.MonthFollowAccum = monthInfoList[0].FollowAccum
	}

	// 本月的数据 （有效关系链）
	monthChainInfoList, err := datahouse.QueryMemberChainMonthInfo(ctx, 0, []uint32{uid}, tm, tm)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetGuildMemberTodayInfo QueryMemberChainMonthInfo fail err %v, uid %d", err, uid)
		return
	}
	if len(monthChainInfoList) > 0 {
		resp.MonthNewRelation = monthChainInfoList[0].ChainCnt
		resp.MonthNewValidRelation += monthChainInfoList[0].VaildChainCnt
	}

	log.DebugWithCtx(ctx, "GetGuildMemberTodayInfo uid %d info %+v", uid, resp)
	return
}

// 本周 数仓接口聚合 （本周活跃天，本周新增关系链）
func (m *SignAnchorStatsMgr) GetGuildMemberWeekInfo(ctx context.Context, tm time.Time, uid, guildId uint32) (
	resp *GuildMemberInfo, err error) {

	defer metrics.DISCOVERY_FUNC_TRACK(protocol.NewServerError(0, "GetGuildMemberWeekInfo")).End()

	resp = &GuildMemberInfo{}
	weekBegin, weekEnd := utils.CalcWeekDurtion(tm)

	infoList, err := datahouse.QueryMemberDailyInfo(ctx, uid, weekBegin, weekEnd)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetGuildMemberWeekInfo QueryGuildMemberMonthInfo fail err %v, uid %d", err, uid)
		return
	}

	chainInfoList, err := datahouse.QueryMemberChainDailyInfo(ctx, uid, weekBegin, weekEnd)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetGuildMemberWeekInfo QueryGuildMemberChainInfo fail err %v, uid %d", err, uid)
		return
	}

	for _, info := range infoList {
		resp.WeekLoginDays += info.LoginDays
		log.DebugWithCtx(ctx, "GetGuildMemberWeekInfo uid %d memberinfo %+v", uid, info)
	}
	for _, info := range chainInfoList {
		resp.WeekNewRelation += info.ChainCnt
		resp.WeekNewValidRelation += info.VaildChainCnt
		log.DebugWithCtx(ctx, "GetGuildMemberWeekInfo uid %d chainInfo %+v", uid, info)
	}

	log.DebugWithCtx(ctx, "GetGuildMemberWeekInfo uid %d info %+v", uid, resp)
	return
}

func (m *SignAnchorStatsMgr) GetMultiPlayerHomepage(ctx context.Context, req *pb.GetMultiPlayerHomepageReq) (out *pb.GetMultiPlayerHomepageResp, err error) {

	defer metrics.DISCOVERY_FUNC_TRACK(protocol.NewServerError(0, "GetMultiPlayerHomepage")).End()

	out = &pb.GetMultiPlayerHomepageResp{
		TodayInfo:              &pb.MultiPlayerDailyInfo{},
		WeekInfo:               &pb.MultiPlayerWeekInfo{},
		MonthInfo:              &pb.MultiPlayerMonthInfo{},
		ConsumeTop3AccountList: []string{},
		ThisMonthCommunityInfo: &pb.MultiPlayerMonthCommunityInfo{},
	}

	defer func() {
		//err = protocol.NewServerError(-2, "服务器开小差了")
	}()

	uid := req.Uid
	now := time.Now()
	nowDay := utils.GetDayZeroTime(now)
	marketId, clientType := req.MarketId, req.ClientType

	log.DebugWithCtx(ctx, "GetMultiPlayerHomepage begin uid %d marketId %d, clientType %d", uid, marketId, clientType)

	contract, err := filter.GetUserContract(m.anchorContractCli, ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetMultiPlayerHomepage GetUserContract fail %v, uid %d", err, uid)
		return out, err
	}
	signguildId := contract.GetContract().GetGuildId()
	if signguildId == 0 {
		log.InfoWithCtx(ctx, "GetMultiPlayerHomepage not sign. uid %d", req.Uid)
		return out, nil
	}
	multiPlayerObtime := uint32(0)
	for _, info := range contract.GetInfoList() {
		if info.IdentityType == uint32(anchorgoPB.SIGN_ANCHOR_IDENTITY_SIGN_ANCHOR_IDENTITY_MULTIPLAYER) {
			multiPlayerObtime = info.ObtainTime
			break
		}
	}

	var (
		followerCount, friendCount                                                                          uint32
		userInfo                                                                                            *accountPB.UserResp
		usermp                                                                                              map[uint32]*accountPB.UserResp
		publicMsg                                                                                           string
		guildInfo                                                                                           *guildPB.GuildResp
		pbBin                                                                                               string
		pbExist                                                                                             bool
		sendpresent_uids_daily_cnt, sendpresent_uids_week_cnt, sendpresent_uids_month_cnt                   uint32
		first_sendpresent_uids_daily_cnt, first_sendpresent_uids_week_cnt, first_sendpresent_uids_month_cnt uint32
		consume_month_top3_uids                                                                             []uint32

		todayInfo, weekInfo *GuildMemberInfo
	)

	// 并发优化
	fillWiths := []func() error{}
	fillWiths = append(fillWiths,

		// 获取用户信息
		func() error {
			userInfo, err = m.accountCli.GetUser(ctx, uid)
			if err != nil {
				log.ErrorWithCtx(ctx, "GetMultiPlayerHomepage GetUser fail %v, uid %d", err, uid)
			}
			return err
		},
		// 获取粉丝数
		func() error {
			_, followerCount, err = m.ugcFriendshipCli.GetUserCounts(ctx, uid)
			if err != nil {
				log.ErrorWithCtx(ctx, "GetMultiPlayerHomepage GetUserCounts fail %v, uid %d", err, uid)
			}
			return err
		},
		// 获取玩伴数
		func() error {
			friendCount, err = m.ugcFriendshipCli.GetFriendCount(ctx, uid, 0)
			if err != nil {
				log.ErrorWithCtx(ctx, "GetMultiPlayerHomepage GetUserCounts fail %v, uid %d", err, uid)
			}
			return err
		},

		// redis缓存数据
		func() error {
			pbBin, pbExist, sendpresent_uids_daily_cnt, sendpresent_uids_week_cnt, sendpresent_uids_month_cnt,
				first_sendpresent_uids_daily_cnt, first_sendpresent_uids_week_cnt, first_sendpresent_uids_month_cnt, consume_month_top3_uids,
				err = m.RedisCache.GetMultiPlayerHomeInfo(now, uid, signguildId)
			if err != nil {
				log.ErrorWithCtx(ctx, "GetMultiPlayerHomepage redis.GetMultiPlayerHomeInfo fail %v, uid %d", err, uid)
				return err
			}
			// 月消费top3
			if len(consume_month_top3_uids) > 0 {
				usermp, err = utilsX.GetUsersMap(m.accountCli, ctx, consume_month_top3_uids)
				if err != nil {
					log.ErrorWithCtx(ctx, "GetMultiPlayerHomepage GetUsersMap fail %v, uid %d", err, uid)
				}
				return err
			}
			return err
		},

		// 公众号消息
		func() error {
			publicMsg, err = m.GetPublicLatestSimpleMessage(ctx, marketId, clientType)
			if err != nil {
				log.ErrorWithCtx(ctx, "GetMultiPlayerHomepage GetPublicLatestSimpleMessage fail %v, uid %d", err, uid)
			}
			return err
		},

		// 公会信息
		func() error {
			guildInfo, err = m.guildCli.GetGuild(ctx, signguildId)
			if err != nil {
				log.ErrorWithCtx(ctx, "GetMultiPlayerHomepage GetGuild fail %v, uid %d", err, uid)
			}
			return err
		},

		// 今日，本月数据
		func() error {
			todayInfo, err = m.GetGuildMemberTodayInfo(ctx, now, uid, signguildId)
			if err != nil {
				log.ErrorWithCtx(ctx, "GetMultiPlayerHomepage GetGuildMemberTodayInfo fail %v, uid %d", err, uid)
			}
			return err
		},
		// 本周数据
		func() error {
			weekInfo, err = m.GetGuildMemberWeekInfo(ctx, now, uid, signguildId)
			if err != nil {
				log.ErrorWithCtx(ctx, "GetMultiPlayerHomepage GetGuildMemberWeekInfo fail %v, uid %d", err, uid)
			}
			return err
		},
	)

	err = mapreduce.Finish(fillWiths...)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetMultiPlayerHomepage mapreduce fail err:%v, uid %d", err, uid)
		return out, err
	}

	// 到这里ctx不知还剩下多少超时时间
	// 月违规信息
	violationTotal, violation_ACnt, violation_BCnt, violation_CCnt, err := m.GetViolationsInfo(ctx, userInfo.GetAlias(), uid, now)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetMultiPlayerHomepage GetViolationsInfo fail  err:%v uid %d", err, uid)
		return out, err
	}
	log.DebugWithCtx(ctx, "GetMultiPlayerHomepage GetViolationsInfo uid %d alias %s total %d (%d %d %d)", uid, userInfo.GetAlias(), violationTotal, violation_ACnt, violation_BCnt, violation_CCnt)

	// 数仓接口聚合
	{
		out.TodayInfo.ActiveHour = utils.TimeStr(todayInfo.TodayLoginDuration)
		out.TodayInfo.NewRelation = todayInfo.TodayNewRelation
		out.WeekInfo.ActiveDaysCnt = weekInfo.WeekLoginDays
		out.WeekInfo.NewRelation = weekInfo.WeekNewRelation
		out.WeekInfo.NewValidRelation = weekInfo.WeekNewValidRelation
		out.MonthInfo.ActiveDaysCnt = todayInfo.MonthLoginDays
		out.MonthInfo.NewRelation = todayInfo.MonthNewRelation
		out.MonthInfo.NewValidRelation = todayInfo.MonthNewValidRelation

		// 月扩圈数据
		out.ThisMonthCommunityInfo.MonthNewRelation = todayInfo.MonthNewRelation
		out.ThisMonthCommunityInfo.MonthNewValidRelation = todayInfo.MonthNewValidRelation
		out.ThisMonthCommunityInfo.MonthFocusTotal = followerCount // todayInfo.MonthFollowAccum // 月累计关注
		out.ThisMonthCommunityInfo.MonthNewFocusCnt = todayInfo.MonthNewFocusCnt
		out.ThisMonthCommunityInfo.MonthLossFriendCnt = todayInfo.MonthLossFriendCnt
		out.ThisMonthCommunityInfo.MonthLossFocusCnt = todayInfo.MonthLossFocusCnt
	}

	// 填充成员收礼 接档时长 有效接档天
	{
		info := &pb.MultiPlayerCacheInfo{}
		if pbExist {
			if err = proto.Unmarshal([]byte(pbBin), info); err != nil {
				log.ErrorWithCtx(ctx, "GetMultiPlayerHomepage proto.Unmarshal failed %v, uid %d, pbBin %s", err, uid, pbBin)
			}
			log.Debugf("GetMultiPlayerHomepage uid %d hit cache %+v", uid, info)
		} else {
			log.Debugf("GetMultiPlayerHomepage uid %d reloadMultiPlayerCacheInfo", uid)
			info, err = m.reloadMultiPlayerCacheInfo(uid, signguildId, now)
			if err != nil {
				log.ErrorWithCtx(ctx, "GetMultiPlayerHomepage reloadMultiPlayerCacheInfo failed %v, uid %d, pbBin %s", err, uid, pbBin)
				return out, err
			}
			bin, _ := proto.Marshal(info)
			err = m.RedisCache.SetMultiPlayerCacheInfo(now, uid, signguildId, string(bin))
			if err != nil {
				log.ErrorWithCtx(ctx, "GetMultiPlayerHomepage SetMultiPlayerCacheInfo failed %v, uid %d", err, uid)
			}
		}

		out.TodayInfo.Fee = utils.IncomeStr(info.GetDailyInfo().GetIncome())
		out.TodayInfo.HoldHour = utils.TimeStr(info.GetDailyInfo().GetValidSec())
		out.WeekInfo.Fee = utils.IncomeStr(info.GetWeekInfo().GetIncome())
		out.WeekInfo.HoldDaysCnt = info.GetWeekInfo().GetValidDay()
		out.MonthInfo.Fee = utils.IncomeStr(info.GetMonthInfo().GetIncome())
		out.MonthInfo.HoldDaysCnt = info.GetMonthInfo().GetValidDay()
	}

	// 填充送礼人数
	{
		out.TodayInfo.Date = "今日"
		out.TodayInfo.SendpresentUidsCnt = sendpresent_uids_daily_cnt
		out.TodayInfo.FirstSendpresentUidsCnt = first_sendpresent_uids_daily_cnt

		out.WeekInfo.Date = "本周"
		out.WeekInfo.SendpresentUidsCnt = sendpresent_uids_week_cnt
		out.WeekInfo.FirstSendpresentUidsCnt = first_sendpresent_uids_week_cnt

		out.MonthInfo.Date = "本月"
		out.MonthInfo.SendpresentUidsCnt = sendpresent_uids_month_cnt
		out.MonthInfo.FirstSendpresentUidsCnt = first_sendpresent_uids_month_cnt
	}

	// 月消费top3
	{
		for _, uid := range consume_month_top3_uids {
			out.ConsumeTop3AccountList = append(out.ConsumeTop3AccountList, usermp[uid].GetUsername())
		}
	}

	// 填充公会信息
	{
		out.GuildId = signguildId
		out.GuildShortId = out.GuildId
		if guildInfo.GetShortId() > 0 {
			out.GuildShortId = guildInfo.GetShortId()
		}
		out.GuildName = guildInfo.GetName()
	}

	// 月违规信息
	{
		out.MonthInfo.Violation_ACnt = violation_ACnt
		out.MonthInfo.Violation_BCnt = violation_BCnt
		out.MonthInfo.Violation_CCnt = violation_CCnt
		out.MonthInfo.ViolationTotalCnt = violationTotal
	}

	// 公众号标题
	if publicMsg == "" {
		publicMsg = "暂时没有新消息哦"
	}

	signDayTime := utils.GetDayZeroTime(time.Unix(int64(multiPlayerObtime), 0))

	out.Uid = uid
	out.Account = userInfo.Username
	out.Nickname = userInfo.Nickname
	out.SignDays = uint32((nowDay.Unix()-signDayTime.Unix()))/86400 + 1
	out.FansCnt = followerCount
	out.FriendsCnt = friendCount
	out.PublicMsg = publicMsg

	log.InfoWithCtx(ctx, "GetMultiPlayerHomepage uid %d marketId %d, clientType %d out %s", uid, marketId, clientType, utils2.ToJson(out))
	return out, nil
}

// 数据详情
func (m *SignAnchorStatsMgr) GetMultiPlayerBaseInfo(ctx context.Context, req *pb.GetMultiPlayerBaseInfoReq) (*pb.GetMultiPlayerBaseInfoResp, error) {

	defer metrics.DISCOVERY_FUNC_TRACK(protocol.NewServerError(0, "GetMultiPlayerBaseInfo")).End()

	out := &pb.GetMultiPlayerBaseInfoResp{
		DailyInfoList: []*pb.MultiPlayerDailyInfo{},
		WeekInfoList:  []*pb.MultiPlayerDailyInfo{},
		MonthInfoList: []*pb.MultiPlayerMonthDetialInfo{},
	}

	now := time.Now()
	uid := req.Uid

	log.DebugWithCtx(ctx, "GetMultiPlayerBaseInfo begin uid %d", uid)

	contract, err := filter.GetUserContract(m.anchorContractCli, ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetMultiPlayerBaseInfo GetUserContract failed %v uid %d", err, req.Uid)
		return out, err
	}
	signguildId := contract.GetContract().GetGuildId()
	if signguildId == 0 {
		log.InfoWithCtx(ctx, "GetMultiPlayerBaseInfo not sign. uid %d", req.Uid)
		return out, nil
	}

	var (
		userInfo          *accountPB.UserResp
		dailyList         []cache.MultiPlayerSendPersonCnt // 近20天的收礼人数
		pbBin, pbExist    = "", false
		day2LoginDuration = map[string]uint32{}                          // ******** 近20天的活跃时长
		day2ChainInfo     = map[string]uint32{}                          // ******** 近20天的新增关系链
		date2IncomeInfo   = map[string]*mysql.MultiAnchorDailyStats{}    // ********, 日维度收礼，接档时长
		date2MonthStats   = map[string]*mysql.MultiAnchorMonthlyStats{}  // 202211 月维度收礼，有效接档天
		date2MonthSendCnt = map[string]*cache.MultiPlayerSendPersonCnt{} // 2006-01月维度送礼人数
		month2MemberInfo  = map[string]*datahouse.GuildMemberInfo{}      // 202211 月维度 有效活跃天
	)

	// 并发优化
	fillWiths := []func() error{}
	fillWiths = append(fillWiths,
		func() error {
			userInfo, err = m.accountCli.GetUser(ctx, uid)
			if err != nil {
				log.ErrorWithCtx(ctx, "GetMultiPlayerBaseInfo GetUser failed %v uid %d", err, uid)
			}
			return err
		},
		// 从缓存取 20天的 日记录
		func() error {
			dailyList, pbBin, pbExist, err = m.RedisCache.GetMultiPlayerTempInfo(now, uid, signguildId)
			if err != nil {
				log.ErrorWithCtx(ctx, "GetMultiPlayerBaseInfo cache.GetMultiPlayerTempInfo failed %v uid %d", err, uid)
			}
			log.DebugWithCtx(ctx, "GetMultiPlayerBaseInfo cache.GetMultiPlayerTempInfo uid %d pbExist %v dailyList %v", uid, pbExist, dailyList)
			return err
		},

		// 从db 查近20天的收礼金额，接档时长
		func() error {
			endT := time.Date(now.Year(), now.Month(), now.Day()-1, 0, 0, 0, 0, time.Local)
			beginT := time.Date(now.Year(), now.Month(), now.Day()-19, 0, 0, 0, 0, time.Local)
			date2IncomeInfo, _, _, err = m.Store.GetMultiAnchorDailysStats(uid, beginT, endT)
			if err != nil {
				log.ErrorWithCtx(ctx, "GetMultiPlayerBaseInfo store.GetMultiAnchorDailysStats failed %v, uid %d", err, uid)
			}
			log.DebugWithCtx(ctx, "GetMultiPlayerBaseInfo GetMultiAnchorDailysStats uid %d beginT %s endT %s date2info %v", uid, beginT, endT, date2IncomeInfo)
			return err
		},

		// 查20天的活跃时长
		func() error {
			dayInfoList, err := datahouse.QueryMemberDailyInfo(ctx, uid, now.AddDate(0, 0, -19), now)
			if err != nil {
				log.ErrorWithCtx(ctx, "GetMultiPlayerBaseInfo datahouse.QueryGuildMemberDailyInfo failed %v, uid %d", err, uid)
			}
			for _, info := range dayInfoList {
				day2LoginDuration[info.Date] = info.LoginDuration
				log.DebugWithCtx(ctx, "GetMultiPlayerBaseInfo QueryGuildMemberDailyInfo uid %d LoginDuration %d", uid, info.LoginDuration)
			}
			return err
		},

		// 查20天的关系链
		func() error {
			dayChainInfoList, err := datahouse.QueryMemberChainDailyInfo(ctx, uid, now.AddDate(0, 0, -19), now)
			if err != nil {
				log.ErrorWithCtx(ctx, "GetMultiPlayerBaseInfo datahouse.QueryGuildMemberChainInfo failed %v, uid %d", err, uid)
			}
			for _, info := range dayChainInfoList {
				day2ChainInfo[info.Date] = info.ChainCnt
				log.DebugWithCtx(ctx, "GetMultiPlayerBaseInfo QueryGuildMemberChainInfo uid %d ChainCnt %d", uid, info.ChainCnt)
			}
			return err
		},

		// 查3个月的有效活跃天数
		func() error {
			guildMemberInfoList, err := datahouse.QueryGuildMemberMonthInfo(ctx, []uint32{uid}, 0, now.Local().AddDate(0, -2, 0), now)
			if err != nil {
				log.ErrorWithCtx(ctx, "GetMultiPlayerBaseInfo datahouse.QueryGuildMemberMonthInfo fail %v, uid %d", err, uid)
			}
			for _, info := range guildMemberInfoList {
				month2MemberInfo[info.DataMonth] = info
				log.DebugWithCtx(ctx, "GetMultiPlayerBaseInfo QueryGuildMemberMonthInfo uid %d info %+v", uid, info)
			}
			return err
		},

		// 查上月，上上个月 的收礼和有效接档天
		func() error {
			date2MonthStats, err = m.Store.GetMultiAnchorMonthlyStatsByTime(uid, []time.Time{now.AddDate(0, -1, 0), now.AddDate(0, -2, 0)})
			if err != nil {
				log.ErrorWithCtx(ctx, "GetMultiPlayerBaseInfo store.GetMultiAnchorMonthlyStatsByTime failed %v, uid %d", err, uid)
			}
			log.DebugWithCtx(ctx, "GetMultiPlayerBaseInfo GetMultiAnchorMonthlyStatsByTime uid %d info %+v", uid, date2MonthStats)
			return err
		},

		// 查近3月 的送礼人数,首送礼人数
		func() error {
			date2MonthSendCnt, err = m.RedisCache.GetMultiPlayerTempSendGiftCntInfo(uid, signguildId, []time.Time{now, now.AddDate(0, -1, 0), now.AddDate(0, -2, 0)})
			if err != nil {
				log.ErrorWithCtx(ctx, "GetMultiPlayerBaseInfo cache.GetMultiPlayerTempSendGiftCntInfo failed %v, uid %d", err, uid)
			}
			log.DebugWithCtx(ctx, "GetMultiPlayerBaseInfo cache.GetMultiPlayerTempSendGiftCntInfo uid %d info %+v", uid, date2MonthSendCnt)
			return err
		},
	)

	err = mapreduce.Finish(fillWiths...)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetMultiPlayerBaseInfo mapreduce fail err:%v, uid %d", err, uid)
		return out, err
	}

	// 今日，当月的 成员收礼 接档时长 有效接档天
	cacheInfo := &pb.MultiPlayerCacheInfo{}
	{
		if pbExist {
			if err = proto.Unmarshal([]byte(pbBin), cacheInfo); err != nil {
				log.ErrorWithCtx(ctx, "GetMultiPlayerBaseInfo proto.Unmarshal failed %v, uid %d, pbBin %s", err, uid, pbBin)
			}
			log.DebugWithCtx(ctx, "GetMultiPlayerBaseInfo uid %d hit cache %+v", uid, cacheInfo)
		} else {
			cacheInfo, err = m.reloadMultiPlayerCacheInfo(uid, signguildId, now)
			if err != nil {
				log.ErrorWithCtx(ctx, "GetMultiPlayerBaseInfo reloadMultiPlayerCacheInfo failed %v, uid %d, pbBin %s", err, uid, pbBin)
				return out, err
			}
			bin, _ := proto.Marshal(cacheInfo)
			err = m.RedisCache.SetMultiPlayerCacheInfo(now, uid, signguildId, string(bin))
			if err != nil {
				log.ErrorWithCtx(ctx, "GetMultiPlayerBaseInfo SetMultiPlayerCacheInfo failed %v, uid %d", err, uid)
			}
			log.DebugWithCtx(ctx, "GetMultiPlayerBaseInfo reloadMultiPlayerCacheInfo uid %d info %+v", uid, cacheInfo)
		}
	}

	date2Violation, err := m.GetViolationsInfoByTemp(ctx, userInfo.GetAlias(), uid, []time.Time{now, now.AddDate(0, -1, 0), now.AddDate(0, -2, 0)})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetMultiPlayerBaseInfo GetViolationsInfoByTemp failed %v, uid %d", err, uid)
		return out, err
	}
	log.DebugWithCtx(ctx, "GetMultiPlayerBaseInfo uid %d GetViolationsInfoByTemp %+v", uid, date2Violation)

	// 填充日数据
	for i, info := range dailyList {
		DailyInfo := &pb.MultiPlayerDailyInfo{
			Date:                    info.Date,
			SendpresentUidsCnt:      info.SendPersonCnt,
			FirstSendpresentUidsCnt: info.FirstSendPersonCnt,
		}

		// 当天数据用缓存
		if i == 0 {
			DailyInfo.Date = "今日"
			DailyInfo.Fee = utils.IncomeStr(cacheInfo.GetDailyInfo().GetIncome())
			DailyInfo.HoldHour = utils.TimeStr(cacheInfo.GetDailyInfo().GetValidSec())
		} else {
			DailyInfo.Fee = utils.IncomeStr(date2IncomeInfo[info.Date].GetAnchorIncome())
			DailyInfo.HoldHour = utils.TimeStr(date2IncomeInfo[info.Date].GetValidSec())
		}

		tm := now.AddDate(0, 0, -i)
		tmStr := tm.Format("20060102")
		DailyInfo.NewRelation = day2ChainInfo[tmStr]
		DailyInfo.ActiveHour = utils.TimeStr(day2LoginDuration[tmStr])
		out.DailyInfoList = append(out.DailyInfoList, DailyInfo)
	}

	// 填充月数据
	// 2022-11
	thisMonthStr := mysql.GetYearMonth(now)
	lastMonthStr := mysql.GetYearMonth(now.AddDate(0, -1, 0))
	lastLastMonthStr := mysql.GetYearMonth(now.AddDate(0, -2, 0))

	// 202211
	thisMonthStr2 := mysql.GetYearMonthV2(now)
	lastMonthStr2 := mysql.GetYearMonthV2(now.AddDate(0, -1, 0))
	lastLastMonthStr2 := mysql.GetYearMonthV2(now.AddDate(0, -2, 0))

	thisMonthInfo := &pb.MultiPlayerMonthDetialInfo{
		Date:                    "本月",
		Fee:                     utils.IncomeStr(cacheInfo.GetMonthInfo().GetIncome()),
		SendpresentUidsCnt:      date2MonthSendCnt[thisMonthStr].GetSendPersonCnt(),
		FirstSendpresentUidsCnt: date2MonthSendCnt[thisMonthStr].GetFirstSendPersonCnt(),
		HoldDaysCnt:             cacheInfo.GetMonthInfo().GetValidDay(),
		ActiveDaysCnt:           month2MemberInfo[thisMonthStr2].GetLoginDays(),
		ViolationTotalCnt:       date2Violation[thisMonthStr].Total,
		Violation_ACnt:          date2Violation[thisMonthStr].Violation_ACnt,
		Violation_BCnt:          date2Violation[thisMonthStr].Violation_BCnt,
		Violation_CCnt:          date2Violation[thisMonthStr].Violation_CCnt,

		MonthFeeGrowrate:                     utils.GetGrowRate(cacheInfo.GetMonthInfo().GetIncome(), date2MonthStats[lastMonthStr].GetAnchorIncome(), -2),
		MonthHoldDaysCntGrowrate:             utils.GetGrowRate(cacheInfo.GetMonthInfo().GetValidDay(), date2MonthStats[lastMonthStr].GetValidDayCnt(), -2),
		MonthSendpresentUidsCntGrowrate:      utils.GetGrowRate(date2MonthSendCnt[thisMonthStr].GetSendPersonCnt(), date2MonthSendCnt[lastMonthStr].GetSendPersonCnt(), -2),
		MonthFirstSendpresentUidsCntGrowrate: utils.GetGrowRate(date2MonthSendCnt[thisMonthStr].GetFirstSendPersonCnt(), date2MonthSendCnt[lastMonthStr].GetFirstSendPersonCnt(), -2),
		MonthViolationTotalCntGrowrate:       utils.GetGrowRate(date2Violation[thisMonthStr].Total, date2Violation[lastMonthStr].Total, -2),
		MonthActiveDaysCntGrowrate:           utils.GetGrowRate(month2MemberInfo[thisMonthStr2].GetLoginDays(), month2MemberInfo[lastMonthStr2].GetLoginDays(), -2),
	}
	lastMonthInfo := &pb.MultiPlayerMonthDetialInfo{
		Date:                    "上月",
		Fee:                     utils.IncomeStr(date2MonthStats[lastMonthStr].GetAnchorIncome()),
		SendpresentUidsCnt:      date2MonthSendCnt[lastMonthStr].GetSendPersonCnt(),
		FirstSendpresentUidsCnt: date2MonthSendCnt[lastMonthStr].GetFirstSendPersonCnt(),
		HoldDaysCnt:             date2MonthStats[lastMonthStr].GetValidDayCnt(),
		ActiveDaysCnt:           month2MemberInfo[lastMonthStr2].GetLoginDays(),
		ViolationTotalCnt:       date2Violation[lastMonthStr].Total,
		Violation_ACnt:          date2Violation[lastMonthStr].Violation_ACnt,
		Violation_BCnt:          date2Violation[lastMonthStr].Violation_BCnt,
		Violation_CCnt:          date2Violation[lastMonthStr].Violation_CCnt,

		MonthFeeGrowrate:                     float32(utils.GetGrowRate(date2MonthStats[lastMonthStr].GetAnchorIncome(), date2MonthStats[lastLastMonthStr].GetAnchorIncome(), -2)),
		MonthSendpresentUidsCntGrowrate:      utils.GetGrowRate(date2MonthSendCnt[lastMonthStr].GetSendPersonCnt(), date2MonthSendCnt[lastLastMonthStr].GetSendPersonCnt(), -2),
		MonthFirstSendpresentUidsCntGrowrate: utils.GetGrowRate(date2MonthSendCnt[lastMonthStr].GetFirstSendPersonCnt(), date2MonthSendCnt[lastLastMonthStr].GetFirstSendPersonCnt(), -2),
		MonthHoldDaysCntGrowrate:             float32(utils.GetGrowRate(date2MonthStats[lastMonthStr].GetValidDayCnt(), date2MonthStats[lastLastMonthStr].GetValidDayCnt(), -2)),
		MonthViolationTotalCntGrowrate:       utils.GetGrowRate(date2Violation[lastMonthStr].Total, date2Violation[lastLastMonthStr].Total, -2),
		MonthActiveDaysCntGrowrate:           utils.GetGrowRate(month2MemberInfo[lastMonthStr2].GetLoginDays(), month2MemberInfo[lastLastMonthStr2].GetLoginDays(), -2),
	}

	out.MonthInfoList = append(out.MonthInfoList, thisMonthInfo, lastMonthInfo)

	log.DebugWithCtx(ctx, "GetMultiPlayerBaseInfo end uid %d, out %s", uid, utils2.ToJson(out))
	return out, nil
}

func (m *SignAnchorStatsMgr) GetMultiPlayerMonthConsumeTop10(ctx context.Context, req *pb.GetMultiPlayerMonthConsumeTop10Req) (*pb.GetMultiPlayerMonthConsumeTop10Resp, error) {
	out := &pb.GetMultiPlayerMonthConsumeTop10Resp{
		ThisMonthConsumeTop10: []*pb.GetMultiPlayerMonthConsumeTop10RespComsumer{},
		LastMonthConsumeTop10: []*pb.GetMultiPlayerMonthConsumeTop10RespComsumer{},
	}

	log.DebugWithCtx(ctx, "GetMultiPlayerMonthConsumeTop10 begin uid %d", req.Uid)

	uid := req.Uid

	contract, err := filter.GetUserContract(m.anchorContractCli, ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetMultiPlayerMonthConsumeTop10 GetUserContract fail %v, uid %d", err, uid)
		return out, err
	}
	signguildId := contract.GetContract().GetGuildId()
	if signguildId == 0 {
		log.InfoWithCtx(ctx, "GetMultiPlayerMonthConsumeTop10 not sign. uid %d", req.Uid)
		return out, nil
	}

	allUids, thisMonth, lastMonth, err := m.RedisCache.GetMultiPlayerMonthStatsTop10(time.Now(), uid, signguildId)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetMultiPlayerMonthConsumeTop10 failed %v uid %d", err, req.Uid)
		return out, err
	}

	if len(allUids) == 0 {
		return out, nil
	}

	uid2UserInfo, err := utilsX.GetUsersMap(m.accountCli, ctx, allUids)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetMultiPlayerMonthConsumeTop10 GetUsersMap failed %v uid %d", err, req.Uid)
		return out, err
	}

	for _, info := range thisMonth {
		out.ThisMonthConsumeTop10 = append(out.ThisMonthConsumeTop10, &pb.GetMultiPlayerMonthConsumeTop10RespComsumer{
			Uid:             info.Uid,
			Account:         uid2UserInfo[info.Uid].GetUsername(),
			Nickname:        uid2UserInfo[info.Uid].GetNickname(),
			MonthConsumeFee: info.Fee,
		})
	}
	for _, info := range lastMonth {
		out.LastMonthConsumeTop10 = append(out.LastMonthConsumeTop10, &pb.GetMultiPlayerMonthConsumeTop10RespComsumer{
			Uid:             info.Uid,
			Account:         uid2UserInfo[info.Uid].GetUsername(),
			Nickname:        uid2UserInfo[info.Uid].GetNickname(),
			MonthConsumeFee: info.Fee,
		})
	}

	log.DebugWithCtx(ctx, "GetMultiPlayerMonthConsumeTop10 uid %d, out %s", req.Uid, utils2.ToJson(out))
	return out, nil
}

func (m *SignAnchorStatsMgr) GetMultiPlayerMonthCommunityInfo(ctx context.Context, req *pb.GetMultiPlayerMonthCommunityInfoReq) (*pb.GetMultiPlayerMonthCommunityInfoResp, error) {

	defer metrics.DISCOVERY_FUNC_TRACK(protocol.NewServerError(0, "GetMultiPlayerMonthCommunityInfo")).End()

	out := &pb.GetMultiPlayerMonthCommunityInfoResp{
		ThisMonthCommunityInfo: &pb.MultiPlayerMonthCommunityInfo{
			Date: "本月",
		},
		LastMonthCommunityInfo: &pb.MultiPlayerMonthCommunityInfo{
			Date: "上月",
		},
	}
	uid := req.Uid
	now := time.Now()

	log.DebugWithCtx(ctx, "GetMultiPlayerMonthCommunityInfo begin uid %d", uid)

	var (
		followerCount uint32
		err           error
		contract      *anchorgoPB.ContractCacheInfo
	)

	// 并发优化
	fillWiths := []func() error{}
	fillWiths = append(fillWiths,
		// 获取粉丝数 累计关注
		func() error {
			_, followerCount, err = m.ugcFriendshipCli.GetUserCounts(ctx, uid)
			if err != nil {
				log.ErrorWithCtx(ctx, "GetMultiPlayerMonthCommunityInfo GetUserCounts fail %v, uid %d", err, uid)
			}
			return err
		},
		func() error {
			contract, err = filter.GetUserContract(m.anchorContractCli, ctx, uid)
			if err != nil {
				log.ErrorWithCtx(ctx, "GetMultiPlayerMonthCommunityInfo GetUserContract fail %v, uid %d", err, uid)
			}
			return err
		},
	)
	err = mapreduce.Finish(fillWiths...)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetMultiPlayerMonthCommunityInfo mapreduce fail err:%v, uid %d", err, uid)
		return out, err
	}

	signguildId := contract.GetContract().GetGuildId()
	if signguildId == 0 {
		log.InfoWithCtx(ctx, "GetMultiPlayerMonthCommunityInfo not sign. uid %d", req.Uid)
		return out, nil
	}

	month2MemberInfo := map[string]*datahouse.GuildMemberInfo{}
	month2ChainInfo := map[string]*datahouse.GuildMemberChainMonthInfo{}

	// 查3个月的 新增/流失关注数、玩半数
	guildMemberInfoList, err := datahouse.QueryGuildMemberMonthInfo(ctx, []uint32{uid}, 0, now.AddDate(0, -2, 0), now)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetMultiPlayerMonthCommunityInfo QueryGuildMemberMonthInfo fail %v, uid %d", err, uid)
		return out, err
	}
	for _, info := range guildMemberInfoList {
		month2MemberInfo[info.DataMonth] = info
		log.DebugWithCtx(ctx, "GetMultiPlayerMonthCommunityInfo QueryGuildMemberMonthInfo uid %d info %+v", uid, info)
	}

	// 查3个月的 关系链数据
	monthChainInfoList, err := datahouse.QueryMemberChainMonthInfo(ctx, 0, []uint32{uid}, now.AddDate(0, -2, 0), now)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetMultiPlayerMonthCommunityInfo QueryMemberChainMonthInfo fail %v, uid %d", err, uid)
		return out, err
	}
	for _, info := range monthChainInfoList {
		month2ChainInfo[info.Date] = info
		log.DebugWithCtx(ctx, "GetMultiPlayerMonthCommunityInfo QueryMemberChainMonthInfo uid %d tm %s totalInfo %+v", uid, info.Date, info)
	}

	thisMonthStr := mysql.GetYearMonthV2(now) // 200601
	lastMonthStr := mysql.GetYearMonthV2(now.AddDate(0, -1, 0))
	lastLastMonthStr := mysql.GetYearMonthV2(now.AddDate(0, -2, 0))

	out = &pb.GetMultiPlayerMonthCommunityInfoResp{
		ThisMonthCommunityInfo: &pb.MultiPlayerMonthCommunityInfo{
			Date:                          "本月",
			MonthFocusTotal:               followerCount,
			MonthNewFocusCnt:              month2MemberInfo[thisMonthStr].GetAddFollowCnt(),
			MonthLossFocusCnt:             month2MemberInfo[thisMonthStr].GetLostFollowCnt(),
			MonthLossFriendCnt:            month2MemberInfo[thisMonthStr].GetLostFriendCnt(),
			MonthNewRelation:              month2ChainInfo[thisMonthStr].GetChainCnt(),
			MonthNewValidRelation:         month2ChainInfo[thisMonthStr].GetVaildChainCnt(),
			MonthNewRelationGrowrate:      utils.GetGrowRate(month2ChainInfo[thisMonthStr].GetChainCnt(), month2ChainInfo[lastMonthStr].GetChainCnt(), -2),
			MonthNewValidRelationGrowrate: utils.GetGrowRate(month2ChainInfo[thisMonthStr].GetVaildChainCnt(), month2ChainInfo[lastMonthStr].GetVaildChainCnt(), -2),
			MonthFocusTotalGrowrate:       utils.GetGrowRate(followerCount, month2MemberInfo[lastMonthStr].GetFollowAccum(), -2),
			MonthNewFocusCntGrowrate:      utils.GetGrowRate(month2MemberInfo[thisMonthStr].GetAddFollowCnt(), month2MemberInfo[lastMonthStr].GetAddFollowCnt(), -2),
			MonthLossFriendCntGrowrate:    utils.GetGrowRate(month2MemberInfo[thisMonthStr].GetLostFriendCnt(), month2MemberInfo[lastMonthStr].GetLostFriendCnt(), -2),
			MonthLossFocusCntGrowrate:     utils.GetGrowRate(month2MemberInfo[thisMonthStr].GetLostFollowCnt(), month2MemberInfo[lastMonthStr].GetLostFollowCnt(), -2),
		},
		LastMonthCommunityInfo: &pb.MultiPlayerMonthCommunityInfo{
			Date:                          "上月",
			MonthFocusTotal:               month2MemberInfo[lastMonthStr].GetFollowAccum(),
			MonthNewFocusCnt:              month2MemberInfo[lastMonthStr].GetAddFollowCnt(),
			MonthLossFocusCnt:             month2MemberInfo[lastMonthStr].GetLostFollowCnt(),
			MonthLossFriendCnt:            month2MemberInfo[lastMonthStr].GetLostFriendCnt(),
			MonthNewRelation:              month2ChainInfo[lastMonthStr].GetChainCnt(),
			MonthNewValidRelation:         month2ChainInfo[lastMonthStr].GetVaildChainCnt(),
			MonthNewRelationGrowrate:      utils.GetGrowRate(month2ChainInfo[lastMonthStr].GetChainCnt(), month2ChainInfo[lastLastMonthStr].GetChainCnt(), -2),
			MonthNewValidRelationGrowrate: utils.GetGrowRate(month2ChainInfo[lastMonthStr].GetVaildChainCnt(), month2ChainInfo[lastLastMonthStr].GetVaildChainCnt(), -2),
			MonthFocusTotalGrowrate:       utils.GetGrowRate(month2MemberInfo[lastMonthStr].GetFollowAccum(), month2MemberInfo[lastLastMonthStr].GetFollowAccum(), -2),
			MonthNewFocusCntGrowrate:      utils.GetGrowRate(month2MemberInfo[lastMonthStr].GetAddFollowCnt(), month2MemberInfo[lastLastMonthStr].GetAddFollowCnt(), -2),
			MonthLossFriendCntGrowrate:    utils.GetGrowRate(month2MemberInfo[lastMonthStr].GetLostFriendCnt(), month2MemberInfo[lastLastMonthStr].GetLostFriendCnt(), -2),
			MonthLossFocusCntGrowrate:     utils.GetGrowRate(month2MemberInfo[lastMonthStr].GetLostFollowCnt(), month2MemberInfo[lastLastMonthStr].GetLostFollowCnt(), -2),
		},
	}

	log.DebugWithCtx(ctx, "GetMultiPlayerMonthCommunityInfo end uid %d out %s", uid, utils2.ToJson(out))
	return out, nil
}

// ====================================================================================================

func (m *SignAnchorStatsMgr) RecordMultiPlayerStatAll(event *kafkapresent.PresentEvent) {

	log.Debugf("RecordMultiPlayerStatAll begin %+v", event)

	if event.PriceType != 2 {
		log.Debugf("RecordMultiPlayerStatAll price type igonre %+v", event)
		return
	}

	var (
		uid, fromUid, totalPrice, sendTime uint32 = event.TargetUid,
			event.Uid, event.GetItemCount() * event.GetPrice(), event.SendTime
		delayedUpdateRank = false
	)

	ctx, cancel := context.WithTimeout(context.Background(), time.Second*3)
	defer cancel()
	contract, serr := m.anchorContractCli.GetUserContractCacheInfo(ctx, 0, uid)
	if serr != nil {
		log.Errorf("RecordMultiPlayerStatAll fail to GetUserContractCacheInfo uid:%+v err:%v", uid, serr)
		return
	}
	if contract.GetContract().GetGuildId() == 0 {
		log.Debugf("RecordMultiPlayerStatAll contract ignore uid %d", uid)
		return
	}

	signGuildId := contract.GetContract().GetGuildId()

	var isMultiPlayer = false
	for _, ident := range contract.GetAnchorIdentityList() {
		if ident == uint32(anchorgoPB.SIGN_ANCHOR_IDENTITY_SIGN_ANCHOR_IDENTITY_MULTIPLAYER) {
			isMultiPlayer = true
			break
		}
	}
	if !isMultiPlayer {
		log.Debugf("RecordMultiPlayerStatAll ignore isMultiPlayer %v uid %d", isMultiPlayer, uid)
		return
	}

	// 神秘人处理
	if event.GetFromUkwAccount() != "" {
		ctx, cancel := context.WithTimeout(context.Background(), time.Second*3)
		defer cancel()
		ukwInfo, err := m.ukwCli.GetUKWInfo(ctx, fromUid)
		if err != nil {
			log.Errorf("RecordMultiPlayerStatAll GetUKWInfo fail %v, uid %d", err, fromUid)
		} else {
			// 这里有没有可能用户送礼之后，关闭神秘人了，而服务消费送礼kafka事件阻塞，再去查已经不是神秘人了?

			if ukwInfo.GetUkwPermissionInfo().GetSwitch() == ukwPB.UKWSwitchType_UKW_SWITCH_ON {

				// 开启榜单开关，延时更新榜单
				if ukwInfo.GetUkwPermissionInfo().GetRankSwitch() == ukwPB.RankSwitchType_UKW_RANK_SWITCH_ON {
					delayedUpdateRank = true
					log.Infof("RecordMultiPlayerStatAll ukw sendUid %d, delayedUpdateRank, event %+v", fromUid, event)
				} else {
					//是神秘人又没有开启榜单开关，就不增加送礼记录了
					log.Infof("RecordMultiPlayerStatAll ukw sendUid %d, not open RankSwitch, ignore, event %+v", fromUid, event)
					return
				}
			} else {
				log.Warnf("RecordMultiPlayerStatAll got sendUid:%d, event.FromUkwAccount is %s, but is not ukw. event %+v",
					fromUid, event.GetFromUkwAccount(), event)
			}
		}
	}

	if !delayedUpdateRank {
		err := m.RedisCache.RecordMultiPlayerStatAll(signGuildId, sendTime, uid, fromUid, totalPrice)
		if err != nil {
			log.Errorf("RecordMultiPlayerStatAll to cache fail %v, event %+v", err, event)
		}

	} else {

		// 神秘人延迟增加榜单 凌晨3点更新
		info := &mysql.MultiPlayerUkwPresentRecord{
			OrderId:    event.OrderId,
			Uid:        event.Uid,
			ToUid:      event.TargetUid,
			GuildId:    signGuildId,
			TotalPrice: totalPrice,
			SendTime:   time.Unix(int64(event.SendTime), 0),
			CreateTime: time.Now(),
		}
		err := m.Store.AddMultiPlayerUkwPresentRecord(info)
		if err != nil {
			log.Errorf("RecordMultiPlayerStatAll store.AddMultiPlayerUkwPresentRecord fail %v event %+v", err, event)
		}
	}

	log.Infof("RecordMultiPlayerStatAll uid %d, fromUid %d, delayedUpdateRank %v, event %+v", uid, fromUid, delayedUpdateRank, event)
}

func (m *SignAnchorStatsMgr) RecordMultiPlayerCenterStats(
	signGuildId uint32, event *kafkapresent.PresentEvent) {

	defer metrics.DISCOVERY_FUNC_TRACK(protocol.NewServerError(0, "RecordMultiPlayerCenterStats")).End()

	// 这里只记录送礼人数、首送礼；不主动更新签约成员的收礼金额缓存，由查询mysql被动更新到缓存

	var (
		uid, fromUid, totalPrice, sendTime uint32 = event.TargetUid,
			event.Uid, event.GetItemCount() * event.GetPrice(), event.SendTime
	)

	// 查询前3个月的 全平台送礼记录
	recordTotal, err := m.Store.CheckIfFirstSendGift(fromUid, uid, time.Now())
	if err != nil {
		log.Errorf("RecordMultiPlayerCenterStats CheckIfFirstSendGift fail %v, uid %d from uid %d, event %+v", err, uid, fromUid, event)
		//return
		recordTotal = 9999
	}

	// 前3个月未送礼，本月在子母公会房首次送礼，算做首送礼
	isFirstSendGift, err := m.RedisCache.RecordMultiPlayerStats(signGuildId, sendTime, uid, fromUid, totalPrice, recordTotal)
	if err != nil {
		log.Errorf("RecordMultiPlayerCenterStats fail %v, uid %d from uid %d, event %+v", err, uid, fromUid, event)
		return
	}

	log.Infof("RecordMultiPlayerCenterStats done signGuildId %d, uid %d, fromUid %d, recordTotal(%d), isFirstSendGift(%v), totalPrice %d, event %+v",
		signGuildId, uid, fromUid, recordTotal, isFirstSendGift, totalPrice, event)
}

// 神秘人消费延迟处理
func (m *SignAnchorStatsMgr) UpdateMultiPlayerUkwPresentTempRecord() {

	now := time.Now()
	tm := time.Date(now.Year(), now.Month(), now.Day(), 3, 0, 0, 0, time.Local)

	list, err := m.Store.GetMultiPlayerUkwPresentTempRecord(tm)
	if err != nil {
		log.Errorf("UpdateMultiPlayerUkwPresentTempRecord GetMultiPlayerUkwPresentTempRecord fail %+v", err)
		return
	}

	log.Infof("UpdateMultiPlayerUkwPresentTempRecord got count %d", len(list))

	for _, info := range list {

		err := m.RedisCache.RecordMultiPlayerStatAll(info.GuildId, uint32(info.SendTime.Unix()), info.ToUid, info.Uid, info.TotalPrice)
		log.Infof("UpdateMultiPlayerUkwPresentTempRecord info %+v, err %v", info, err)

		time.Sleep(time.Millisecond * 50)
	}
}

func (m *SignAnchorStatsMgr) reloadMultiPlayerCacheInfo(uid, guildId uint32, tm time.Time) (*pb.MultiPlayerCacheInfo, error) {

	defer metrics.DISCOVERY_FUNC_TRACK(protocol.NewServerError(0, "reloadMultiPlayerCacheInfo")).End()

	var (
		dailyInfo                        = &mysql.MultiAnchorDailyStats{}
		weekTotalIncome, weekValidDayCnt uint32
		monthInfo                        = &mysql.MultiAnchorMonthlyStats{}
		err                              error
		info                             = &pb.MultiPlayerCacheInfo{}
		beginTm, endTm                   = utils.CalcWeekDurtion(tm)
	)

	// 并发优化
	fillWiths := []func() error{}
	fillWiths = append(fillWiths,
		func() error {
			dailyInfo, err = m.Store.GetMultiAnchorDailyStatsNotGuild(uid, tm)
			return err
		},
		func() error {
			_, weekTotalIncome, weekValidDayCnt, err = m.Store.GetMultiAnchorDailysStats(uid, beginTm, endTm)
			return err
		},
		func() error {
			monthInfo, err = m.Store.GetMultiAnchorMonthlyStatsNotGuild(uid, tm)
			return err
		},
	)

	err = mapreduce.Finish(fillWiths...)
	if err != nil {
		log.Errorf("reloadMultiPlayerCacheInfo mapreduce fail uid:%d, err:%v", uid, err)
		return info, err
	}

	info.DailyInfo = &pb.DailyInfo{
		Income:   dailyInfo.AnchorIncome,
		ValidSec: dailyInfo.ValidSec,
	}
	info.WeekInfo = &pb.DurInfo{
		Income:   weekTotalIncome,
		ValidDay: weekValidDayCnt,
	}
	info.MonthInfo = &pb.DurInfo{
		Income:   monthInfo.AnchorIncome,
		ValidDay: monthInfo.ValidDayCnt,
	}
	return info, nil
}

// 获取违规信息，按月 urrc.GetPractitionerViolationsInfoFromAudit
func (m *SignAnchorStatsMgr) GetViolationsInfo(ctx context.Context, ttid string, uid uint32, tm time.Time) (total, violation_ACnt, violation_BCnt, violation_CCnt uint32, err error) {

	monthBegin, monthEnd := utils.GetMonthTime(tm)
	endMonthTm := time.Date(monthEnd.Year(), monthEnd.Month(), monthEnd.Day(), 23, 59, 59, 0, time.Local)

	mapTid2Violations, err := urrc.GetViolationsInfoList(ctx, []string{ttid}, monthBegin, endMonthTm)
	if err != nil {
		return
	}

	if info, ok := mapTid2Violations[ttid]; ok {
		violation_ACnt, violation_BCnt, violation_CCnt = uint32(len(info.ViolationA)), uint32(len(info.ViolationB)), uint32(len(info.ViolationC))
		total = violation_ACnt + violation_BCnt + violation_CCnt
	}

	log.DebugWithCtx(ctx, "GetViolationsInfo ttid %s uid %d begin %s end %s, violationTotal %d [A:%d B:%d C:%d]",
		ttid, uid, monthBegin, endMonthTm, total, violation_ACnt, violation_BCnt, violation_CCnt)
	return
}

type ViolationsInfo struct {
	Total, Violation_ACnt, Violation_BCnt, Violation_CCnt uint32
}

func (t *ViolationsInfo) String() string {
	return utils2.ToJson(t)
}

func (m *SignAnchorStatsMgr) GetViolationsInfoByTemp(ctx context.Context, ttid string, uid uint32, tmList []time.Time) (map[string]*ViolationsInfo, error) {

	mp := map[string]*ViolationsInfo{}

	for _, tm := range tmList {
		total, violation_ACnt, violation_BCnt, violation_CCnt, err := m.GetViolationsInfo(ctx, ttid, uid, tm)
		if err != nil {
			return mp, err
		}

		mp[tm.Format("2006-01")] = &ViolationsInfo{
			Total:          total,
			Violation_ACnt: violation_ACnt,
			Violation_BCnt: violation_BCnt,
			Violation_CCnt: violation_CCnt,
		}
	}

	return mp, nil
}

func (m *SignAnchorStatsMgr) GetPublicLatestSimpleMessage(ctx context.Context, marketId, clientType uint32) (preview string, err error) {

	imApiResp, err := m.imApiCli.GetPublicLatestSimpleMessage(ctx, &imApiPB.GetPublicLatestSimpleMessageReq{
		PublicAccount: &imApiPB.PublicAccount{
			PublicType: imApiPB.PublicAccount_SYSTEM,
			BindedId:   90005,
		},
		Platform: &imApiPB.Platform{
			MarketId:   marketId,
			ClientType: clientType,
		},
	})
	if err != nil {
		return
	}

	if imApiResp.GetMsg().GetTitle() != "" {
		preview = imApiResp.GetMsg().GetTitle()
	} else {
		preview = imApiResp.GetMsg().GetContent()
	}
	return
}

func ToHttpGetMultiPlayerHomepageResp(msg *pb.GetMultiPlayerHomepageResp) *api.GetMultiPlayerHomepageResp {
	return &api.GetMultiPlayerHomepageResp{
		Uid:          msg.Uid,
		Account:      msg.Account,
		Nickname:     msg.Nickname,
		GuildId:      msg.GuildId,
		GuildShortId: msg.GuildShortId,
		GuildName:    msg.GuildName,
		SignDays:     msg.SignDays,
		FansCnt:      msg.FansCnt,
		FriendsCnt:   msg.FriendsCnt,
		TodayInfo: &api.MultiPlayerDailyInfo{
			Date:                    msg.GetTodayInfo().GetDate(),
			Fee:                     msg.GetTodayInfo().GetFee(),
			SendpresentUidsCnt:      msg.GetTodayInfo().GetSendpresentUidsCnt(),
			FirstSendpresentUidsCnt: msg.GetTodayInfo().GetFirstSendpresentUidsCnt(),
			HoldHour:                msg.GetTodayInfo().GetHoldHour(),
			ActiveHour:              msg.GetTodayInfo().GetActiveHour(),
			NewRelation:             msg.GetTodayInfo().GetNewRelation(),
		},
		WeekInfo: &api.MultiPlayerWeekInfo{
			Date:                    msg.GetWeekInfo().GetDate(),
			Fee:                     msg.GetWeekInfo().GetFee(),
			SendpresentUidsCnt:      msg.GetWeekInfo().GetSendpresentUidsCnt(),
			FirstSendpresentUidsCnt: msg.GetWeekInfo().GetFirstSendpresentUidsCnt(),
			HoldDaysCnt:             msg.GetWeekInfo().GetHoldDaysCnt(),
			ActiveDaysCnt:           msg.GetWeekInfo().GetActiveDaysCnt(),
			NewRelation:             msg.GetWeekInfo().GetNewRelation(),
			NewValidRelation:        msg.GetWeekInfo().GetNewValidRelation(),
		},
		MonthInfo: &api.MultiPlayerMonthInfo{
			Date:                    msg.GetMonthInfo().GetDate(),
			Fee:                     msg.GetMonthInfo().GetFee(),
			SendpresentUidsCnt:      msg.GetMonthInfo().GetSendpresentUidsCnt(),
			FirstSendpresentUidsCnt: msg.GetMonthInfo().GetFirstSendpresentUidsCnt(),
			HoldDaysCnt:             msg.GetMonthInfo().GetHoldDaysCnt(),
			ActiveDaysCnt:           msg.GetMonthInfo().GetActiveDaysCnt(),
			NewRelation:             msg.GetMonthInfo().GetNewRelation(),
			NewValidRelation:        msg.GetMonthInfo().GetNewValidRelation(),
			ViolationTotalCnt:       msg.GetMonthInfo().GetViolationTotalCnt(),
			Violation_ACnt:          msg.GetMonthInfo().GetViolation_ACnt(),
			Violation_BCnt:          msg.GetMonthInfo().GetViolation_BCnt(),
			Violation_CCnt:          msg.GetMonthInfo().GetViolation_CCnt(),
		},
		PublicMsg:              msg.PublicMsg,
		ConsumeTop3AccountList: msg.ConsumeTop3AccountList,
		ThisMonthCommunityInfo: &api.MultiPlayerMonthCommunityInfo{
			Date:                          msg.GetThisMonthCommunityInfo().GetDate(),
			MonthNewRelation:              msg.GetThisMonthCommunityInfo().GetMonthNewRelation(),
			MonthNewValidRelation:         msg.GetThisMonthCommunityInfo().GetMonthNewValidRelation(),
			MonthFocusTotal:               msg.GetThisMonthCommunityInfo().GetMonthFocusTotal(),
			MonthNewFocusCnt:              msg.GetThisMonthCommunityInfo().GetMonthNewFocusCnt(),
			MonthLossFriendCnt:            msg.GetThisMonthCommunityInfo().GetMonthLossFriendCnt(),
			MonthLossFocusCnt:             msg.GetThisMonthCommunityInfo().GetMonthLossFocusCnt(),
			MonthNewRelationGrowrate:      msg.GetThisMonthCommunityInfo().GetMonthNewRelationGrowrate(),
			MonthNewValidRelationGrowrate: msg.GetThisMonthCommunityInfo().GetMonthNewValidRelationGrowrate(),
			MonthFocusTotalGrowrate:       msg.GetThisMonthCommunityInfo().GetMonthFocusTotalGrowrate(),
			MonthNewFocusCntGrowrate:      msg.GetThisMonthCommunityInfo().GetMonthNewFocusCntGrowrate(),
			MonthLossFriendCntGrowrate:    msg.GetThisMonthCommunityInfo().GetMonthLossFriendCntGrowrate(),
			MonthLossFocusCntGrowrate:     msg.GetThisMonthCommunityInfo().GetMonthLossFocusCntGrowrate(),
		},
	}
}

func ToHttpGetMultiPlayerBaseInfoResp(msg *pb.GetMultiPlayerBaseInfoResp) *api.GetMultiPlayerBaseInfoResp {
	resp := &api.GetMultiPlayerBaseInfoResp{}

	for _, info := range msg.DailyInfoList {
		resp.DailyInfoList = append(resp.DailyInfoList, &api.MultiPlayerDailyInfo{
			Date:                    info.GetDate(),
			Fee:                     info.GetFee(),
			SendpresentUidsCnt:      info.GetSendpresentUidsCnt(),
			FirstSendpresentUidsCnt: info.GetFirstSendpresentUidsCnt(),
			HoldHour:                info.GetHoldHour(),
			ActiveHour:              info.GetActiveHour(),
			NewRelation:             info.GetNewRelation(),
		})
	}
	for _, info := range msg.WeekInfoList {
		resp.WeekInfoList = append(resp.WeekInfoList, &api.MultiPlayerDailyInfo{
			Date:                    info.GetDate(),
			Fee:                     info.GetFee(),
			SendpresentUidsCnt:      info.GetSendpresentUidsCnt(),
			FirstSendpresentUidsCnt: info.GetFirstSendpresentUidsCnt(),
			HoldHour:                info.GetHoldHour(),
			ActiveHour:              info.GetActiveHour(),
			NewRelation:             info.GetNewRelation(),
		})
	}
	for _, info := range msg.MonthInfoList {
		resp.MonthInfoList = append(resp.MonthInfoList, &api.MultiPlayerMonthDetialInfo{
			Date:                                 info.GetDate(),
			Fee:                                  info.GetFee(),
			SendpresentUidsCnt:                   info.GetSendpresentUidsCnt(),
			FirstSendpresentUidsCnt:              info.GetFirstSendpresentUidsCnt(),
			HoldDaysCnt:                          info.GetHoldDaysCnt(),
			ActiveDaysCnt:                        info.GetActiveDaysCnt(),
			ViolationTotalCnt:                    info.GetViolationTotalCnt(),
			Violation_ACnt:                       info.GetViolation_ACnt(),
			Violation_BCnt:                       info.GetViolation_BCnt(),
			Violation_CCnt:                       info.GetViolation_CCnt(),
			MonthFeeGrowrate:                     info.GetMonthFeeGrowrate(),
			MonthSendpresentUidsCntGrowrate:      info.GetMonthSendpresentUidsCntGrowrate(),
			MonthFirstSendpresentUidsCntGrowrate: info.GetMonthFirstSendpresentUidsCntGrowrate(),
			MonthHoldDaysCntGrowrate:             info.GetMonthHoldDaysCntGrowrate(),
			MonthActiveDaysCntGrowrate:           info.GetMonthActiveDaysCntGrowrate(),
			MonthViolationTotalCntGrowrate:       info.GetMonthViolationTotalCntGrowrate(),
		})
	}
	return resp
}

func ToHttpGetMultiPlayerMonthConsumeTop10Resp(msg *pb.GetMultiPlayerMonthConsumeTop10Resp) *api.GetMultiPlayerMonthConsumeTop10Resp {
	resp := &api.GetMultiPlayerMonthConsumeTop10Resp{}
	for _, info := range msg.ThisMonthConsumeTop10 {
		resp.ThisMonthConsumeTop10 = append(resp.ThisMonthConsumeTop10, &api.GetMultiPlayerMonthConsumeTop10RespComsumer{
			Uid:             info.Uid,
			Account:         info.Account,
			Nickname:        info.Nickname,
			MonthConsumeFee: info.MonthConsumeFee,
		})
	}

	for _, info := range msg.LastMonthConsumeTop10 {
		resp.LastMonthConsumeTop10 = append(resp.LastMonthConsumeTop10, &api.GetMultiPlayerMonthConsumeTop10RespComsumer{
			Uid:             info.Uid,
			Account:         info.Account,
			Nickname:        info.Nickname,
			MonthConsumeFee: info.MonthConsumeFee,
		})
	}
	return resp
}

func ToHttpGetMultiPlayerMonthCommunityInfoResp(msg *pb.GetMultiPlayerMonthCommunityInfoResp) *api.GetMultiPlayerMonthCommunityInfoResp {

	return &api.GetMultiPlayerMonthCommunityInfoResp{List: []*api.MultiPlayerMonthCommunityInfo{
		{
			Date:                          msg.GetThisMonthCommunityInfo().GetDate(),
			MonthNewRelation:              msg.GetThisMonthCommunityInfo().GetMonthNewRelation(),
			MonthNewValidRelation:         msg.GetThisMonthCommunityInfo().GetMonthNewValidRelation(),
			MonthFocusTotal:               msg.GetThisMonthCommunityInfo().GetMonthFocusTotal(),
			MonthNewFocusCnt:              msg.GetThisMonthCommunityInfo().GetMonthNewFocusCnt(),
			MonthLossFriendCnt:            msg.GetThisMonthCommunityInfo().GetMonthLossFriendCnt(),
			MonthLossFocusCnt:             msg.GetThisMonthCommunityInfo().GetMonthLossFocusCnt(),
			MonthNewRelationGrowrate:      msg.GetThisMonthCommunityInfo().GetMonthNewRelationGrowrate(),
			MonthNewValidRelationGrowrate: msg.GetThisMonthCommunityInfo().GetMonthNewValidRelationGrowrate(),
			MonthFocusTotalGrowrate:       msg.GetThisMonthCommunityInfo().GetMonthFocusTotalGrowrate(),
			MonthNewFocusCntGrowrate:      msg.GetThisMonthCommunityInfo().GetMonthNewFocusCntGrowrate(),
			MonthLossFriendCntGrowrate:    msg.GetThisMonthCommunityInfo().GetMonthLossFriendCntGrowrate(),
			MonthLossFocusCntGrowrate:     msg.GetThisMonthCommunityInfo().GetMonthLossFocusCntGrowrate(),
		},
		{
			Date:                          msg.GetLastMonthCommunityInfo().GetDate(),
			MonthNewRelation:              msg.GetLastMonthCommunityInfo().GetMonthNewRelation(),
			MonthNewValidRelation:         msg.GetLastMonthCommunityInfo().GetMonthNewValidRelation(),
			MonthFocusTotal:               msg.GetLastMonthCommunityInfo().GetMonthFocusTotal(),
			MonthNewFocusCnt:              msg.GetLastMonthCommunityInfo().GetMonthNewFocusCnt(),
			MonthLossFriendCnt:            msg.GetLastMonthCommunityInfo().GetMonthLossFriendCnt(),
			MonthLossFocusCnt:             msg.GetLastMonthCommunityInfo().GetMonthLossFocusCnt(),
			MonthNewRelationGrowrate:      msg.GetLastMonthCommunityInfo().GetMonthNewRelationGrowrate(),
			MonthNewValidRelationGrowrate: msg.GetLastMonthCommunityInfo().GetMonthNewValidRelationGrowrate(),
			MonthFocusTotalGrowrate:       msg.GetLastMonthCommunityInfo().GetMonthFocusTotalGrowrate(),
			MonthNewFocusCntGrowrate:      msg.GetLastMonthCommunityInfo().GetMonthNewFocusCntGrowrate(),
			MonthLossFriendCntGrowrate:    msg.GetLastMonthCommunityInfo().GetMonthLossFriendCntGrowrate(),
			MonthLossFocusCntGrowrate:     msg.GetLastMonthCommunityInfo().GetMonthLossFocusCntGrowrate(),
		},
	}}
}
