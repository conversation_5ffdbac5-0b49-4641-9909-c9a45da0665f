package manager

import (
	"context"
	"fmt"
	"github.com/jinzhu/gorm"
	"golang.52tt.com/pkg/datahouse"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/urrc"
	anchorcontractPb "golang.52tt.com/protocol/services/anchorcontract-go"
	pb "golang.52tt.com/protocol/services/sign-anchor-stats"
	"golang.52tt.com/services/anchor-contract/sign-anchor-stats/mysql"
	"golang.52tt.com/services/anchor-contract/sign-anchor-stats/utils"
	"time"
)

const (
	QualityMinMonthValidDayCnt  uint32 = 20
	QualityMinMonthIncome       uint32 = 500000
	QualityMinMonthActiveDayCnt uint32 = 20
	QualityMinMonthPayChains    uint32 = 10

	AnchorChMinMonthValidDayCnt uint32 = 15
	//AnchorChMinMonthIncome uint32 = 5000

	maxMonthAViolationsCnt = 0
	maxMonthBViolationsCnt = 0
	maxMonthCViolationsCnt = 2

	//专业从业者
	ProfessionPracValidDayCnt = 10     // 有效接档天数
	ProfessionPracIncome      = 200000 // 房间个人收礼

	//有效签约成员
	ValidSignMemValidDayCnt = 20     // 有效接档天数
	ValidSignMemIncome      = 200000 //房间个人收礼

	//新增有效签约成员
	NewValidSignMemValidDayCnt = 15     // 有效接档天数
	NewValidSignMemIncome      = 100000 //房间个人收礼
)

func (m *SignAnchorStatsMgr) CheckIsNeedUpdateThisMonth(scene string, nowTm time.Time) bool {
	log.Debugf("CheckIsNeedUpdateThisMonth scene:%s nowTm:%v", scene, nowTm)

	// 而且数仓每天定时统计数据需要时间，7点后再更新
	if nowTm.Hour() < 7 {
		return false
	}

	isProc, err := m.RedisCache.CheckDailyUpdateIsProc(scene, nowTm)
	if err != nil {
		log.Errorf("CheckIsNeedUpdateThisMonth CheckDailyUpdateIsProc failed scene:%s now:%v err:%v",
			scene, nowTm, err)
		return false
	}

	if isProc {
		log.Debugf("CheckIsNeedUpdateThisMonth is proc scene:%s now:%v", scene, nowTm)
		return false
	}

	log.Infof("CheckIsNeedUpdateThisMonth need update scene:%s nowTm:%v", scene, nowTm)
	return true
}

func (m *SignAnchorStatsMgr) CheckIsNeedUpdateLastMonth(scene string, nowTm time.Time) bool {
	log.Debugf("CheckIsNeedUpdateLastMonth scene:%s nowTm:%v", scene, nowTm)
	// 付费关系链数据是7天一个周期，而且数仓每天定时统计数据需要时间
	if nowTm.Day() > 8 || nowTm.Hour() < 7 {
		return false
	}

	isProc, err := m.RedisCache.CheckDailyUpdateIsProc(scene, nowTm)
	if err != nil {
		log.Errorf("CheckIsNeedUpdateLastMonth CheckDailyUpdateIsProc failed scene:%s now:%v err:%v",
			scene, nowTm, err)
		return false
	}

	if isProc {
		log.Debugf("CheckIsNeedUpdateLastMonth is proc scene:%s now:%v", scene, nowTm)
		return false
	}

	log.Infof("CheckIsNeedUpdateLastMonth need update scene:%s nowTm:%v", scene, nowTm)
	return true
}

// 讲多人互动房间日数据统计为月
func (m *SignAnchorStatsMgr) MultiChannelMonthStat(t time.Time) {
	ctx := context.Background()

	fmt.Println("day: ", t.Format("2006-01-02"))

	last := t.AddDate(0, -1, 0)

	// get distinct guild_id
	guildIds, err := m.Store.GetDayDistinctGuildId(ctx, t)
	if err != nil {
		log.Errorf("MultiChannelMonthStat GetDayDistinctGuildId err:%v", err)
		return
	}
	fmt.Println("MultiChannelMonthStat guildIds: ", guildIds)
	for _, guildId := range guildIds {
		// get distinct channel_id
		channelIds, err := m.Store.GetDayDistinctChannelId(ctx, t, guildId)
		if err != nil {
			log.Errorf("MultiChannelMonthStat GetDayDistinctChannelId err:%v", err)
			continue
		}
		fmt.Println("MultiChannelMonthStat channelIds: ", channelIds)
		for _, channelId := range channelIds {
			// get channel daily income
			dailyIncome, err := m.Store.GetChannelDailyIncome(ctx, t, guildId, channelId)
			if err != nil {
				log.Errorf("MultiChannelMonthStat GetChannelDailyIncome err:%v", err)
				continue
			}

			// get last monthly channel stat
			channelLastMonthlyStat, err := m.Store.GetChannelMonthlyStat(ctx, last, guildId, channelId)
			if err != nil {
				log.Errorf("MultiChannelMonthStat GetChannelMonthlyStat err:%v", err)
				continue
			}

			err = m.Store.Transaction(ctx, func(tx *gorm.DB) (err error) {
				getLock, err := m.Store.CreateChannelDailyLock(ctx, tx, &mysql.MultiChannelDailyLock{
					SignGuildId: guildId,
					ChannelId:   channelId,
					Date:        mysql.GetDateTime(t),
				})
				if err != nil {
					log.Errorf("HandlePresentEvent CreateChannelDailyLock err:%v", err)
					return
				}
				if !getLock {
					log.Infof("HandlePresentEvent repeated, guild:%d, channel:%d, date:%s", guildId, channelId, t)
					return
				}

				// incr monthly income
				err = m.Store.IncrMultiChannelMonthlyStats(ctx, tx, &mysql.MultiChannelMonthlyLog{
					SignGuildId:     guildId,
					ChannelId:       channelId,
					Date:            mysql.GetMonthTime(t),
					AnchorIncome:    dailyIncome,
					LastMonthIncome: channelLastMonthlyStat.AnchorIncome,
				})
				if err != nil {
					log.Errorf("HandlePresentEvent IncrMultiChannelMonthlyStats err:%v", err)
					return
				}

				// get monthly channel stat
				channelMonthlyStat, err := m.Store.GetChannelMonthlyStat(ctx, t, guildId, channelId)
				if err != nil {
					log.Errorf("MultiChannelMonthStat GetChannelMonthlyStat err:%v", err)
					return
				}

				// get last month same period income
				lastMonthDay := utils.LastMonthSameDay(t)
				lastMonthSameDayRecord, err := m.Store.GetLastMonthSamePeriod(ctx, lastMonthDay, guildId, channelId)
				if err != nil {
					log.Errorf("MultiChannelMonthStat GetLastMonthSamePeriod err:%v", err)
					return
				}
				var monthGrandIncome, lastMonthSameDayGrandIncome float32
				monthGrandIncome = float32(channelMonthlyStat.AnchorIncome)

				if lastMonthSameDayRecord != nil {
					lastMonthSameDayGrandIncome = float32(lastMonthSameDayRecord.MonthGrandIncome)
				}

				// get same period chain ratio
				var chainRatio float32
				if lastMonthSameDayGrandIncome > 0 {
					chainRatio = (monthGrandIncome - lastMonthSameDayGrandIncome) / lastMonthSameDayGrandIncome
				}

				// incr daily income
				err = m.Store.IncrMultiChannelDailyStats(ctx, tx, &mysql.MultiChannelDailyLog{
					SignGuildId:               guildId,
					ChannelId:                 channelId,
					Date:                      mysql.GetDateTime(t),
					Month:                     uint32(t.Year()*100) + uint32(t.Month()),
					AnchorIncome:              dailyIncome,
					MonthGrandIncome:          channelMonthlyStat.AnchorIncome,     // 月累计流水
					SamePeriodLastMonthIncome: uint32(lastMonthSameDayGrandIncome), // 上月同期流水
					SamePeriodChainRatio:      chainRatio,                          // 同期环比
				})
				if err != nil {
					log.Errorf("HandlePresentEvent IncrMultiChannelDailyStats err:%v", err)
					return
				}
				return nil
			})
			if err != nil {
				log.Errorf("MultiChannelMonthStat Transaction err:%v", err)
				continue
			}
		}
	}
}

func (m *SignAnchorStatsMgr) UpdateAnchorMonthlyAbilityInfo() {
	nowTm := time.Now()

	if !m.CheckIsNeedUpdateLastMonth("anchor_update", nowTm) {
		return
	}

	lastMonthTm := time.Date(nowTm.Year(), nowTm.Month(), 1, 0, 0, 0, 0, time.Local).AddDate(0, 0, -1)
	beginMonthTm := time.Date(lastMonthTm.Year(), lastMonthTm.Month(), 1, 0, 0, 0, 0, time.Local)
	endMonthTm := time.Date(lastMonthTm.Year(), lastMonthTm.Month(), lastMonthTm.Day(), 23, 59, 59, 0, time.Local)

	log.Infof("UpdateAnchorMonthlyAbilityInfo begin now:%v last:%v beginMonthTm:%v endMonthTm:%v", nowTm, lastMonthTm, beginMonthTm, endMonthTm)

	var offset uint32 = 0
	var limit uint32 = 50
	ctx := context.Background()
	for {
		monthStatList, _, err := m.Store.GetMultiAnchorMonthlyStatsByGuildId(ctx, beginMonthTm, endMonthTm, []uint32{}, QualityMinMonthValidDayCnt, QualityMinMonthIncome, 0, 0, offset, limit)
		if err != nil {
			log.Errorf("UpdateAnchorMonthlyAbilityInfo GetMultiAnchorMonthlyStatsByGuildId failed begin:%v %d %d err:%v", beginMonthTm, offset, limit, err)
			return
		}

		mapGuild2UidList := make(map[uint32][]uint32, 0)
		mapUid2Stat := make(map[uint32]*mysql.MultiAnchorMonthlyStats, 0)
		for _, stat := range monthStatList {
			mapGuild2UidList[stat.SignGuildId] = append(mapGuild2UidList[stat.SignGuildId], stat.Uid)
			mapUid2Stat[stat.Uid] = stat
		}

		for guildId, uidList := range mapGuild2UidList {
			tmpMapFive, tmpMapSeven, tmpMapConsume, err := datahouse.GetPractitionerGuildMonthlyDataFromDH(guildId, uidList, beginMonthTm, endMonthTm)
			if err != nil {
				log.Errorf("UpdateAnchorMonthlyAbilityInfo GetPractitionerGuildMonthlyDataFromDH failed guild:%d len:%d last:%v", guildId, len(uidList), beginMonthTm)
				return
			}

			userMap, err := m.accountCli.GetUsersMap(ctx, uidList)
			if err != nil {
				log.Errorf("UpdateAnchorMonthlyAbilityInfo GetUsersMap err:%v, uidList:%v", err, uidList)
				return
			}

			tidList := make([]string, 0)
			for _, v := range userMap {
				tidList = append(tidList, v.GetAlias())
			}

			mapTid2Violations, _, _, _, err := urrc.GetPractitionerViolationsInfoFromAudit(tidList, beginMonthTm, endMonthTm)
			if err != nil {
				log.Errorf("UpdateAnchorMonthlyAbilityInfo GetPractitionerViolationsInfoFromAudit failed guild:%d len:%d last:%v", guildId, len(tidList), beginMonthTm)
				return
			}

			for _, uid := range uidList {
				tid := userMap[uid].GetAlias()

				abilityType := uint32(pb.AbilityType_Ability_Invalid)
				if (tmpMapFive[uid]+tmpMapSeven[uid]) >= QualityMinMonthActiveDayCnt && tmpMapConsume[uid] >= QualityMinMonthPayChains && mapTid2Violations[tid][0] <= maxMonthAViolationsCnt &&
					mapTid2Violations[tid][1] <= maxMonthBViolationsCnt && mapTid2Violations[tid][2] <= maxMonthCViolationsCnt {
					// 满足优质条件，更新能力维度值
					abilityType = uint32(pb.AbilityType_Ability_Quality)
				}

				if stats, ok := mapUid2Stat[uid]; ok {
					stats.AbilityType = abilityType
					err = m.Store.UpdateMultiAnchorMonthlyAbility(ctx, nil, stats)
					if err != nil {
						log.Errorf("UpdateAnchorMonthlyAbilityInfo UpdateMultiAnchorMonthlyAbility failed stats:%v err:%v", stats, err)
						return
					}
				}
			}
		}

		if uint32(len(monthStatList)) < limit {
			break
		}
		offset = offset + limit
	}

	err := m.RedisCache.SetDailyUpdateProcFlag("anchor_update", nowTm)
	if err != nil {
		log.Errorf("UpdateAnchorMonthlyAbilityInfo SetDailyUpdateProcFlag failed last:%v %d %d err:%v", beginMonthTm, offset, limit, err)
		return
	}

	log.Infof("UpdateAnchorMonthlyAbilityInfo end now:%v last:%v cost:%v offset:%d", nowTm, beginMonthTm, time.Since(nowTm), offset)
}

// 更新公会和pgc房间的月统计信息
func (m *SignAnchorStatsMgr) UpdatePgcAndGuildStatsInfo() {
	nowTm := time.Now()

	beginMonthTm := time.Date(nowTm.Year(), nowTm.Month(), 1, 0, 0, 0, 0, time.Local)
	endMonthTm := nowTm

	lastMonthTm := beginMonthTm.AddDate(0, 0, -1)
	lastMonthBeginTm := time.Date(lastMonthTm.Year(), lastMonthTm.Month(), 1, 0, 0, 0, 0, time.Local)
	lastMonthEndTm := time.Date(lastMonthTm.Year(), lastMonthTm.Month(), lastMonthTm.Day(), 23, 59, 59, 0, time.Local)

	log.Infof("UpdatePgcAndGuildStatsInfo begin now:%v beginMonthTm:%v endMonthTm:%v lastMonthBeginTm:%v lastMonthEndTm:%v",
		nowTm, beginMonthTm, endMonthTm, lastMonthBeginTm, lastMonthEndTm)

	// 更新pgc房间本月统计数据
	if m.CheckIsNeedUpdateThisMonth("this_pgc_update", nowTm) {
		m.UpdatePgcMonthlyStatsInfo("this_pgc_update", nowTm, beginMonthTm, endMonthTm)
	}

	// 检查是否需要统计pgc房间上个月的统计数据
	if m.CheckIsNeedUpdateLastMonth("last_pgc_update", nowTm) {
		m.UpdatePgcMonthlyStatsInfo("last_pgc_update", nowTm, lastMonthBeginTm, lastMonthEndTm)
	}

	// 更新公会本月统计数据
	if m.CheckIsNeedUpdateThisMonth("this_guild_update", nowTm) {
		m.UpdateGuildMonthlyStatsInfo("this_guild_update", nowTm, beginMonthTm, endMonthTm)
	}

	// 检查是否需要统计公会上个月数据
	if m.CheckIsNeedUpdateLastMonth("last_guild_update", nowTm) {
		m.UpdateGuildMonthlyStatsInfo("last_guild_update", nowTm, lastMonthBeginTm, lastMonthEndTm)
	}
}

// 更新pgc房间月信息
func (m *SignAnchorStatsMgr) UpdatePgcMonthlyStatsInfo(scene string, nowTm, beginMonthTm, endMonthTm time.Time) {
	log.Infof("UpdatePgcMonthlyStatsInfo begin now:%v  begin:%v end:%v ", nowTm, beginMonthTm, endMonthTm)

	var offset uint32 = 0
	var limit uint32 = 100
	ctx := context.Background()
	for {
		monthStatList, _, err := m.Store.GetPgcMonthlyStats(ctx, 0, []uint32{}, offset, limit, beginMonthTm, endMonthTm, false)
		if err != nil {
			log.Errorf("UpdatePgcMonthlyStatsInfo GetPgcMonthlyStats failed nowTm:%v %d %d err:%v", nowTm, offset, limit, err)
			return
		}

		cidList := make([]uint32, 0)
		mapCid2Stat := make(map[uint32]*mysql.PgcMonthLyLog, 0)
		for _, stat := range monthStatList {
			cidList = append(cidList, stat.ChannelId)
			mapCid2Stat[stat.ChannelId] = stat
		}

		for _, cid := range cidList {
			var guildSignCnt uint32
			guildId := mapCid2Stat[cid].GuildId
			mapGuild2IsProc := make(map[uint32]bool, 0)

			// 获取绑定的子公会
			guildIdList, err := m.Store.GetBindedGuildList(guildId)
			if err != nil {
				log.Errorf("UpdatePgcMonthlyStatsInfo GetBindedGuildList failed nowTm:%v %d %d err:%v", nowTm, offset, limit, err)
				return
			}

			guildIdList = append(guildIdList, guildId)
			for _, id := range guildIdList {
				if !mapGuild2IsProc[id] {
					contractResp, err := m.anchorContractCli.GetGuildContractByIdentity(ctx, &anchorcontractPb.GetGuildContractByIdentityReq{
						GuildId:        id,
						AnchorIdentity: uint32(anchorcontractPb.SIGN_ANCHOR_IDENTITY_SIGN_ANCHOR_IDENTITY_MULTIPLAYER),
						AnchorFlag:     uint32(anchorcontractPb.ANCHOR_FLAG_AllAnchor),
						Offset:         0,
						Limit:          1,
					})
					if err != nil {
						log.Errorf("UpdatePgcMonthlyStatsInfo GetGuildContractByIdentity failed nowTm:%v %d %d %d err:%v", nowTm, offset, limit, id, err)
						return
					}

					mapGuild2IsProc[id] = true

					guildSignCnt += contractResp.GetTotalCnt()
				}
			}

			dailyTotalStats, err := m.Store.GetPgcDailyTotalStats(guildId, cid, beginMonthTm, endMonthTm)
			if err != nil {
				log.Errorf("UpdatePgcMonthlyStatsInfo GetPgcDailyTotalStats failed guildId:%d cid:%d tm:%v tm:%v err:%v", guildId, cid, beginMonthTm, endMonthTm, err)
				return
			}

			var anchorOffset, anchorLimit uint32 = 0, 50
			var validAnchorCnt, activeAnchorCnt, qualityAnchorCnt uint32 = 0, 0, 0
			var activeAnchorIncome, qualityAnchorIncome uint32 = 0, 0
			var professionPracCnt, validSignMemCnt uint32
			for {
				anchorMonthStatList, _, err := m.Store.GetMultiAnchorChannelMonthlyStat(ctx, cid, 0, 0, 0, 0, anchorOffset, anchorLimit, beginMonthTm, endMonthTm, false)
				if err != nil {
					log.Errorf("UpdatePgcMonthlyStatsInfo GetMultiAnchorChannelMonthlyStat failed nowTm:%v %d %d err:%v", nowTm, anchorOffset, anchorLimit, err)
					return
				}

				mapGuild2UidList := make(map[uint32][]uint32, 0)
				mapUid2Stat := make(map[uint32]*mysql.MultiAnchorChannelMonthlyLog, 0)
				for _, stat := range anchorMonthStatList {
					mapGuild2UidList[stat.SignGuildId] = append(mapGuild2UidList[stat.SignGuildId], stat.Uid)
					mapUid2Stat[stat.Uid] = stat
				}

				for guildId, uidList := range mapGuild2UidList {
					mapUidIsQuality := make(map[uint32]bool, 0)
					qualityAnchorList := make([]uint32, 0)

					for _, uid := range uidList {
						if mapUid2Stat[uid].ValidDayCnt >= QualityMinMonthValidDayCnt && mapUid2Stat[uid].AnchorIncome >= QualityMinMonthIncome {
							mapUidIsQuality[uid] = true
							qualityAnchorList = append(qualityAnchorList, uid)
						}
					}

					_, _, tmpMapConsume, err := datahouse.GetPractitionerGuildMonthlyDataFromDH(guildId, qualityAnchorList, beginMonthTm, endMonthTm)
					if err != nil {
						log.Errorf("UpdatePgcMonthlyStatsInfo GetPractitionerGuildMonthlyDataFromDH failed guild:%d len:%d nowTm:%v", guildId, len(qualityAnchorList), nowTm)
						return
					}

					tmpMapFive, tmpMapSeven, err := datahouse.GetChannelMonthlyDataFromDH(cid, qualityAnchorList, beginMonthTm, endMonthTm)
					if err != nil {
						log.Errorf("UpdatePgcMonthlyStatsInfo GetChannelMonthlyDataFromDH failed guild:%d len:%d nowTm:%v", guildId, len(qualityAnchorList), nowTm)
						return
					}

					userMap, err := m.accountCli.GetUsersMap(ctx, qualityAnchorList)
					if err != nil {
						log.Errorf("UpdatePgcMonthlyStatsInfo GetUsersMap err:%v, uidList:%v", err, qualityAnchorList)
						return
					}

					tidList := make([]string, 0)
					for _, v := range userMap {
						tidList = append(tidList, v.GetAlias())
					}

					mapTid2Violations, _, _, _, err := urrc.GetPractitionerViolationsInfoFromAudit(tidList, beginMonthTm, endMonthTm)
					if err != nil {
						log.Errorf("UpdatePgcMonthlyStatsInfo GetPractitionerViolationsInfoFromAudit failed guild:%d len:%d nowTm:%V", guildId, len(uidList), nowTm)
						return
					}

					for _, uid := range uidList {
						// 房间有效接档成员
						if mapUid2Stat[uid].ValidDayCnt >= AnchorChMinMonthValidDayCnt {
							validAnchorCnt++
						}

						// 房间活跃从业者数量
						if mapUid2Stat[uid].ValidSec > 0 && mapUid2Stat[uid].AnchorIncome > 0 {
							activeAnchorCnt++
							activeAnchorIncome += mapUid2Stat[uid].AnchorIncome
						}

						// 专业从业者数据
						if mapUid2Stat[uid].ValidDayCnt >= ProfessionPracValidDayCnt && mapUid2Stat[uid].AnchorIncome >= ProfessionPracIncome {
							professionPracCnt++
						}

						//有效签约成员数
						if mapUid2Stat[uid].ValidDayCnt >= ValidSignMemValidDayCnt && mapUid2Stat[uid].AnchorIncome >= ValidSignMemIncome {
							validSignMemCnt++
						}

						if mapUidIsQuality[uid] {
							tid := userMap[uid].GetAlias()
							// 房间优质从业者数量
							if (tmpMapFive[uid]+tmpMapSeven[uid]) >= QualityMinMonthActiveDayCnt && tmpMapConsume[uid] >= QualityMinMonthPayChains && mapTid2Violations[tid][0] <= maxMonthAViolationsCnt &&
								mapTid2Violations[tid][1] <= maxMonthBViolationsCnt && mapTid2Violations[tid][2] <= maxMonthCViolationsCnt {
								qualityAnchorCnt++
								qualityAnchorIncome += mapUid2Stat[uid].AnchorIncome
							}
						}
					}
				}

				if uint32(len(anchorMonthStatList)) < anchorLimit {
					break
				}
				anchorOffset = anchorOffset + anchorLimit
			}

			var cntRatio, incomeRatio, qualityTransRatio, channelPkgFeeRatio float32 = 0, 0, 0, 0
			if guildSignCnt != 0 {
				cntRatio = float32(qualityAnchorCnt) / float32(guildSignCnt)
			}
			if mapCid2Stat[cid].ChannelFee != 0 {
				incomeRatio = float32(qualityAnchorIncome) / float32(mapCid2Stat[cid].ChannelFee)
				channelPkgFeeRatio = float32(dailyTotalStats.TotalPkgFee) / float32(mapCid2Stat[cid].ChannelFee)
			}

			if activeAnchorCnt != 0 {
				qualityTransRatio = float32(qualityAnchorCnt) / float32(activeAnchorCnt)
			}

			m.Store.UpdatePgcMonthlyStats(&mysql.PgcMonthLyLog{
				GuildId:                  mapCid2Stat[cid].GuildId,
				Date:                     mapCid2Stat[cid].Date,
				ChannelId:                cid,
				ValidAnchorCnt:           validAnchorCnt,
				ActiveAnchorCnt:          activeAnchorCnt,
				QualityAnchorCnt:         qualityAnchorCnt,
				ActiveAnchorIncome:       activeAnchorIncome,
				QualityAnchorIncome:      qualityAnchorIncome,
				QualityAnchorCntRatio:    cntRatio,
				QualityAnchorIncomeRatio: incomeRatio,
				TotalChannelFee:          mapCid2Stat[cid].ChannelFee,
				UpdateTime:               time.Now(),
				QualityAnchorTransRatio:  qualityTransRatio,
				ChannelPkgFeeRatio:       channelPkgFeeRatio,
				ProfessionPracCnt:        professionPracCnt,
				ValidSignMemCnt:          validSignMemCnt,
			})
		}

		if uint32(len(monthStatList)) < limit {
			break
		}
		offset = offset + limit
	}

	err := m.RedisCache.SetDailyUpdateProcFlag(scene, nowTm)
	if err != nil {
		log.Errorf("UpdatePgcMonthlyStatsInfo SetDailyUpdateProcFlag failed nowTm:%v %d %d err:%v", nowTm, offset, limit, err)
		return
	}

	log.Infof("UpdatePgcMonthlyStatsInfo end now:%v  cost:%v offset:%d", nowTm, time.Since(nowTm), offset)
}

// 更新公会月信息
func (m *SignAnchorStatsMgr) UpdateGuildMonthlyStatsInfo(scene string, nowTm, beginMonthTm, endMonthTm time.Time) {
	log.Infof("UpdateGuildMonthlyStatsInfo begin now:%v begin:%v end:%v", nowTm, beginMonthTm, endMonthTm)

	var offset uint32 = 0
	var limit uint32 = 500
	ctx := context.Background()
	mapGuild2Fee := make(map[uint32]uint64, 0)
	for {
		monthStatList, _, err := m.Store.GetPgcMonthlyStats(ctx, 0, []uint32{}, offset, limit, beginMonthTm, endMonthTm, false)
		if err != nil {
			log.Errorf("UpdatePgcMonthlyStatsInfo GetPgcMonthlyStats failed nowTm:%v %d %d err:%v", nowTm, offset, limit, err)
			return
		}

		for _, stat := range monthStatList {
			mapGuild2Fee[stat.GuildId] += uint64(stat.ChannelFee)
		}

		if uint32(len(monthStatList)) < limit {
			break
		}

		offset = offset + limit
	}

	mapGuild2TotalStats := make(map[uint32]*mysql.GuildMonthLyLog, 0)

	for guildId, fee := range mapGuild2Fee {
		var guildSignCnt uint32

		contractResp, sErr := m.anchorContractCli.GetGuildContractByIdentity(ctx, &anchorcontractPb.GetGuildContractByIdentityReq{
			GuildId:        guildId,
			AnchorIdentity: uint32(anchorcontractPb.SIGN_ANCHOR_IDENTITY_SIGN_ANCHOR_IDENTITY_MULTIPLAYER),
			AnchorFlag:     uint32(anchorcontractPb.ANCHOR_FLAG_AllAnchor),
			Offset:         0,
			Limit:          1,
		})
		if sErr != nil {
			log.Errorf("UpdateGuildMonthlyStatsInfo GetGuildContractByIdentity failed nowTm:%v %d %d %d err:%v", nowTm, offset, limit, guildId, sErr)
			return
		}
		guildSignCnt = contractResp.GetTotalCnt()

		dailyTotalStats, err := m.Store.GetPgcDailyTotalStats(guildId, 0, beginMonthTm, endMonthTm)
		if err != nil {
			log.Errorf("UpdateGuildMonthlyStatsInfo GetPgcDailyTotalStats failed guildId:%d tm:%v tm:%v err:%v", guildId, beginMonthTm, endMonthTm, err)
			return
		}

		var anchorOffset, anchorLimit uint32 = 0, 50
		var activeAnchorCnt, qualityAnchorCnt uint32 = 0, 0
		var activeAnchorIncome, qualityAnchorIncome uint32 = 0, 0
		var professionPracCnt, newProfessionPracCnt uint32
		var validSignMemCnt, newValidSignMemCnt uint32
		for {
			anchorMonthStatList, _, err := m.Store.GetMultiAnchorMonthlyStatsByGuildId(ctx, beginMonthTm, endMonthTm, []uint32{}, 0, 0, 0, guildId, anchorOffset, anchorLimit)
			if err != nil {
				log.Errorf("UpdateGuildMonthlyStatsInfo GetMultiAnchorChannelMonthlyStat failed nowTm:%v %d %d err:%v", nowTm, anchorOffset, anchorLimit, err)
				return
			}

			mapGuild2UidList := make(map[uint32][]uint32, 0)
			mapUid2Stat := make(map[uint32]*mysql.MultiAnchorMonthlyStats, 0)
			for _, stat := range anchorMonthStatList {
				mapGuild2UidList[stat.SignGuildId] = append(mapGuild2UidList[stat.SignGuildId], stat.Uid)
				mapUid2Stat[stat.Uid] = stat
			}

			for guildId, uidList := range mapGuild2UidList {
				mapUidIsQuality := make(map[uint32]bool, 0)
				qualityAnchorList := make([]uint32, 0)

				for _, uid := range uidList {
					if mapUid2Stat[uid].ValidDayCnt >= QualityMinMonthValidDayCnt && mapUid2Stat[uid].AnchorIncome >= QualityMinMonthIncome {
						mapUidIsQuality[uid] = true
						qualityAnchorList = append(qualityAnchorList, uid)
					}
				}

				tmpMapFive, tmpMapSeven, tmpMapConsume, err := datahouse.GetPractitionerGuildMonthlyDataFromDH(guildId, qualityAnchorList, beginMonthTm, endMonthTm)
				if err != nil {
					log.Errorf("UpdateGuildMonthlyStatsInfo GetPractitionerGuildMonthlyDataFromDH failed guild:%d len:%d nowTm:%v", guildId, len(qualityAnchorList), nowTm)
					return
				}

				userMap, err := m.accountCli.GetUsersMap(ctx, uidList)
				if err != nil {
					log.Errorf("UpdateGuildMonthlyStatsInfo GetUsersMap err:%v, len:%d", err, len(uidList))
					return
				}

				tidList := make([]string, 0)
				for _, v := range userMap {
					// 满足优质最低标准在获取违规记录
					if mapUidIsQuality[v.GetUid()] {
						tidList = append(tidList, v.GetAlias())
					}
				}

				mapTid2Violations, _, _, _, err := urrc.GetPractitionerViolationsInfoFromAudit(tidList, beginMonthTm, endMonthTm)
				if err != nil {
					log.Errorf("UpdateGuildMonthlyStatsInfo GetPractitionerViolationsInfoFromAudit failed guild:%d len:%d nowTm:%v", guildId, len(qualityAnchorList), nowTm)
					return
				}

				log.Debugf("UpdateGuildMonthlyStatsInfo guildId:%d uidList:%v", guildId, uidList)

				for _, uid := range uidList {
					// 活跃从业者数量
					if mapUid2Stat[uid].ValidSec > 0 && mapUid2Stat[uid].AnchorIncome > 0 {
						activeAnchorCnt++
						activeAnchorIncome += mapUid2Stat[uid].AnchorIncome
					}

					registerTm := time.Unix(int64(userMap[uid].GetRegisteredAt()), 0)

					if mapUid2Stat[uid].ValidDayCnt >= ProfessionPracValidDayCnt && mapUid2Stat[uid].AnchorIncome >= ProfessionPracIncome {
						professionPracCnt++
						if registerTm.Month() == beginMonthTm.Month() {
							newProfessionPracCnt++
						}
					}

					if mapUid2Stat[uid].ValidDayCnt >= ValidSignMemValidDayCnt && mapUid2Stat[uid].AnchorIncome >= ValidSignMemIncome {
						validSignMemCnt++
					}

					if mapUid2Stat[uid].ValidDayCnt >= NewValidSignMemValidDayCnt && mapUid2Stat[uid].AnchorIncome >= NewValidSignMemIncome && registerTm.Month() == beginMonthTm.Month() {
						newValidSignMemCnt++
					}

					if mapUidIsQuality[uid] {
						tid := userMap[uid].GetAlias()
						// 优质从业者数量
						if (tmpMapFive[uid]+tmpMapSeven[uid]) >= QualityMinMonthActiveDayCnt && tmpMapConsume[uid] >= QualityMinMonthPayChains && mapTid2Violations[tid][0] <= maxMonthAViolationsCnt &&
							mapTid2Violations[tid][1] <= maxMonthBViolationsCnt && mapTid2Violations[tid][2] <= maxMonthCViolationsCnt {
							qualityAnchorCnt++
							qualityAnchorIncome += mapUid2Stat[uid].AnchorIncome
						}
					}
				}
			}

			if uint32(len(anchorMonthStatList)) < anchorLimit {
				break
			}
			anchorOffset = anchorOffset + anchorLimit
		}

		mapGuild2TotalStats[guildId] = &mysql.GuildMonthLyLog{
			GuildId:              guildId,
			Date:                 mysql.GetMonthTime(beginMonthTm),
			GuildFee:             fee,
			SignAnchorCnt:        guildSignCnt,
			ActiveAnchorCnt:      activeAnchorCnt,
			QualityAnchorCnt:     qualityAnchorCnt,
			ActiveAnchorIncome:   activeAnchorIncome,
			QualityAnchorIncome:  qualityAnchorIncome,
			GuildPkgFee:          dailyTotalStats.TotalPkgFee,
			ProfessionPracCnt:    professionPracCnt,
			NewProfessionPracCnt: newProfessionPracCnt,
			ValidSignMemCnt:      validSignMemCnt,
			NewValidSignMemCnt:   newValidSignMemCnt,
		}
	}

	for guildId := range mapGuild2Fee {
		// 获取子公会列表
		bindedIdList, err := m.Store.GetBindedGuildList(guildId)
		if err != nil {
			log.Errorf("UpdateGuildMonthlyStatsInfo GetBindGuildId failed nowTm:%v %d err:%v", nowTm, guildId, err)
			return
		}

		for _, id := range bindedIdList {
			if id == 0 || id == guildId {
				continue
			}

			if _, ok := mapGuild2TotalStats[id]; ok {
				// 需要算到母公会
				mapGuild2TotalStats[guildId].SignAnchorCnt += mapGuild2TotalStats[id].SignAnchorCnt
				mapGuild2TotalStats[guildId].ActiveAnchorCnt += mapGuild2TotalStats[id].ActiveAnchorCnt
				mapGuild2TotalStats[guildId].QualityAnchorCnt += mapGuild2TotalStats[id].QualityAnchorCnt
				mapGuild2TotalStats[guildId].ActiveAnchorIncome += mapGuild2TotalStats[id].ActiveAnchorIncome
				mapGuild2TotalStats[guildId].QualityAnchorIncome += mapGuild2TotalStats[id].QualityAnchorIncome
				mapGuild2TotalStats[guildId].GuildFee += mapGuild2TotalStats[id].GuildFee
				mapGuild2TotalStats[guildId].ProfessionPracCnt += mapGuild2TotalStats[id].ProfessionPracCnt
				mapGuild2TotalStats[guildId].NewProfessionPracCnt += mapGuild2TotalStats[id].NewProfessionPracCnt
				mapGuild2TotalStats[guildId].ValidSignMemCnt += mapGuild2TotalStats[id].ValidSignMemCnt
				mapGuild2TotalStats[guildId].NewValidSignMemCnt += mapGuild2TotalStats[id].NewValidSignMemCnt
			} else {
				// 子公会没有数据的话，也要查签约成员数量
				contractResp, err := m.anchorContractCli.GetGuildContractByIdentity(ctx, &anchorcontractPb.GetGuildContractByIdentityReq{
					GuildId:        id,
					AnchorIdentity: uint32(anchorcontractPb.SIGN_ANCHOR_IDENTITY_SIGN_ANCHOR_IDENTITY_MULTIPLAYER),
					AnchorFlag:     uint32(anchorcontractPb.ANCHOR_FLAG_AllAnchor),
					Offset:         0,
					Limit:          1,
				})
				if err != nil {
					log.Errorf("UpdateGuildMonthlyStatsInfo GetGuildContractByIdentity failed nowTm:%v %d %d %d err:%v", nowTm, offset, limit, guildId, err)
					return
				}

				mapGuild2TotalStats[guildId].SignAnchorCnt += contractResp.GetTotalCnt()
			}
		}
	}

	for _, stats := range mapGuild2TotalStats {
		var cntRatio, incomeRatio, guildPkgFeeRatio float32 = 0, 0, 0
		if stats.SignAnchorCnt != 0 {
			cntRatio = float32(stats.QualityAnchorCnt) / float32(stats.SignAnchorCnt)
		}
		if stats.GuildFee != 0 {
			incomeRatio = float32(stats.QualityAnchorIncome) / float32(stats.GuildFee)
			guildPkgFeeRatio = float32(stats.GuildPkgFee) / float32(stats.GuildFee)
		}

		err := m.Store.UpdateGuildMonthlyStats(&mysql.GuildMonthLyLog{
			GuildId:                  stats.GuildId,
			Date:                     stats.Date,
			GuildFee:                 stats.GuildFee,
			SignAnchorCnt:            stats.SignAnchorCnt,
			ActiveAnchorCnt:          stats.ActiveAnchorCnt,
			QualityAnchorCnt:         stats.QualityAnchorCnt,
			ActiveAnchorIncome:       stats.ActiveAnchorIncome,
			QualityAnchorIncome:      stats.QualityAnchorIncome,
			QualityAnchorCntRatio:    cntRatio,
			QualityAnchorIncomeRatio: incomeRatio,
			UpdateTime:               time.Now(),
			GuildPkgFee:              stats.GuildPkgFee,
			GuildPkgFeeRatio:         guildPkgFeeRatio,
			ProfessionPracCnt:        stats.ProfessionPracCnt,
			NewProfessionPracCnt:     stats.NewProfessionPracCnt,
			ValidSignMemCnt:          stats.ValidSignMemCnt,
			NewValidSignMemCnt:       stats.NewValidSignMemCnt,
		})
		if err != nil {
			log.Errorf("UpdateGuildMonthlyStatsInfo UpdateGuildMonthlyStats failed nowTm:%v stats:%v err:%v", nowTm, stats, err)
			return
		}
	}

	err := m.RedisCache.SetDailyUpdateProcFlag(scene, nowTm)
	if err != nil {
		log.Errorf("UpdateGuildMonthlyStatsInfo SetDailyUpdateProcFlag failed nowTm:%v %d %d err:%v", nowTm, offset, limit, err)
		return
	}

	log.Infof("UpdateGuildMonthlyStatsInfo end now:%v nowTm:%v cost:%v offset:%d", nowTm, nowTm, time.Since(nowTm), offset)

}
