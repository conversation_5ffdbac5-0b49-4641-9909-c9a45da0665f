package server

import (
	"context"
	"errors"
	"fmt"
	"math/rand"
	"time"

	"golang.52tt.com/pkg/config"
	"golang.52tt.com/pkg/foundation/utils"
	"golang.52tt.com/pkg/log"
	_ "golang.52tt.com/protocol/services/channel-red-packet"
	pb "golang.52tt.com/protocol/services/channel-red-packet"
	reconcile_v2 "golang.52tt.com/protocol/services/reconcile-v2"
	UnifiedPayCallback "golang.52tt.com/protocol/services/unified_pay/cb"
	"golang.52tt.com/services/channel-red-packet/channel-red-packet/breaker"
	"golang.52tt.com/services/channel-red-packet/channel-red-packet/cache"
	"golang.52tt.com/services/channel-red-packet/channel-red-packet/conf"
	"golang.52tt.com/services/channel-red-packet/channel-red-packet/event"
	"golang.52tt.com/services/channel-red-packet/channel-red-packet/manager"
	"golang.52tt.com/services/channel-red-packet/channel-red-packet/mysql"
)

type RedPacket struct {
	sc          conf.IServiceConfigT
	mgr         manager.IRedPacketMgr
	ukwEventSub *event.UkwEventSub
}

func NewRedPacket(ctx context.Context, cfg config.Configer) (*RedPacket, error) {

	sc := &conf.ServiceConfigT{}

	cfgPath := ctx.Value("configfile").(string)
	if cfgPath == "" {
		return nil, errors.New("configfile not exist")
	}
	err := sc.Parse(cfgPath)
	if err != nil {
		log.ErrorWithCtx(ctx, "config Parse fail err:%v", err)
		return nil, err
	}

	cacheClient := cache.NewRedPacketCache(sc.GetRedisConfig())

	mysqlStore, err := mysql.NewMysql(sc.GetMysqlConfig(), sc.GetReadonlyMysqlConfig())
	if err != nil {
		log.ErrorWithCtx(ctx, "Failed to NewMysql :%+v, %v", sc.GetMysqlConfig(), err)
		return nil, err
	}

	if err = breaker.Setup(ctx); err != nil {
		log.ErrorWithCtx(ctx, "breaker.Setup fail %v", err)
		return nil, err
	}
	mgr, err := manager.NewRedPacketMgr(ctx, sc, cacheClient, mysqlStore)
	if err != nil {
		log.ErrorWithCtx(ctx, "Failed to NewRedPacketMgr err:%v", err)
		return nil, err
	}

	ukwEventSub, err := event.NewUkwEventSub(sc.GetUKWKafkaConfig().ClientID, sc.GetUKWKafkaConfig().GroupID,
		sc.GetUKWKafkaConfig().TopicList(), sc.GetUKWKafkaConfig().BrokerList(), mgr)
	if err != nil {
		log.ErrorWithCtx(ctx, "Failed to NewUkwEventSub err:%v", err)
		return nil, err
	}

	return &RedPacket{
		sc:          sc,
		mgr:         mgr,
		ukwEventSub: ukwEventSub,
	}, nil
}

func (s *RedPacket) ShutDown() {
	s.ukwEventSub.Close()
	s.mgr.ShutDown()
}

func (s *RedPacket) AddRedPacketConf(ctx context.Context, req *pb.AddRedPacketConfReq) (*pb.AddRedPacketConfResp, error) {
	out := &pb.AddRedPacketConfResp{}

	return out, s.mgr.AddRedPacketConf(ctx, req)
}

func (s *RedPacket) DelRedPacketConf(ctx context.Context, req *pb.DelRedPacketConfReq) (*pb.DelRedPacketConfResp, error) {
	out := &pb.DelRedPacketConfResp{}
	return out, s.mgr.DelRedPacketConf(ctx, req.GetRedPacketId())
}

func (s *RedPacket) GetRedPacketConfById(ctx context.Context, req *pb.GetRedPacketConfByIdReq) (*pb.GetRedPacketConfByIdResp, error) {
	var err error
	out := &pb.GetRedPacketConfByIdResp{}

	out.Conf, _, err = s.mgr.GetRedPacketConfById(ctx, req.GetRedPacketId())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetRedPacketConfById fail to GetRedPacketConfById. in:%+v, err:%v", req, err)
		return out, err
	}

	return out, nil
}

func (s *RedPacket) GetRedPacketConf(ctx context.Context, req *pb.GetRedPacketConfReq) (*pb.GetRedPacketConfResp, error) {
	out := &pb.GetRedPacketConfResp{}

	list, err := s.mgr.GetRedPacketConf(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetRedPacketConfById fail to GetRedPacketConf. in:%+v, err:%v", req, err)
		return out, err
	}

	nowTs := uint32(time.Now().Unix())
	out.List = make([]*pb.RedPacketConf, 0, len(list))
	for _, rbConf := range list {
		if nowTs < rbConf.BeginTime || nowTs >= rbConf.EndTime {
			continue
		}

		out.List = append(out.List, rbConf)
	}

	out.DefaultPublicMsgList = s.mgr.GetBusinessConf().GetDefaultPublicMsgList()
	out.InvalidDesc = fmt.Sprintf("少于%d人抢红包不瓜分奖励，豆退回至金主", s.mgr.GetBusinessConf().GetRPInvalidAwardUserLimit())

	log.DebugWithCtx(ctx, "GetRedPacketConf in %+v, out %s", req, utils.ToJson(out))
	return out, nil
}

func (s *RedPacket) CheckIfCanSendRedPacket(ctx context.Context, req *pb.CheckIfCanSendRedPacketReq) (*pb.CheckIfCanSendRedPacketResp, error) {
	var err error
	out := &pb.CheckIfCanSendRedPacketResp{}
	out.Ok, err = s.mgr.CheckIfCanSendRedPacket(ctx, req.GetOpUid(), req.GetChannelId())
	if err != nil {
		log.ErrorWithCtx(ctx, "CheckIfCanSendRedPacket fail to CheckIfCanSendRedPacket. in:%+v, err:%v", req, err)
		return out, err
	}

	return out, nil
}

func (s *RedPacket) SendRedPacket(ctx context.Context, req *pb.SendRedPacketReq) (*pb.SendRedPacketResp, error) {
	out := &pb.SendRedPacketResp{}
	err := s.mgr.SendRedPacket(ctx, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "SendRedPacket fail to SendRedPacket. in:%+v, err:%v", req, err)
		return out, err
	}

	return out, nil
}

func (s *RedPacket) GetRedPacketList(ctx context.Context, req *pb.GetRedPacketListReq) (*pb.GetRedPacketListResp, error) {
	out := &pb.GetRedPacketListResp{}
	pbList, err := s.mgr.GetChannelRedPacketUseList(ctx, req.GetChannelId())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetRedPacketList fail to GetChannelRedPacketUseList. in:%+v, err:%v", req, err)
		return out, err
	}

	if len(pbList) > 0 {
		orderlist := []cache.UidOrderInfo{}
		for _, info := range pbList {
			orderlist = append(orderlist, cache.UidOrderInfo{Uid: info.SenderUid, OrderId: info.OrderId})
		}

		uid2UkwInfo, err := s.mgr.GetCache().BatchGetUKWCacheInfo(orderlist)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetRedPacketList fail to BatchGetUKWCacheInfo. in:%+v, err:%v", req, err)
		}
		if len(uid2UkwInfo) > 0 {
			for _, info := range pbList {
				info.UkwInfo = uid2UkwInfo[info.SenderUid]
			}
		}
	}

	out.SettleDurationSec = s.mgr.GetBusinessConf().GetSettleDurationSec()
	out.List = pbList
	out.RuleDesc = s.mgr.GenRuleDesc()

	log.DebugWithCtx(ctx, "GetRedPacketList in %+v, out %s", req, utils.ToJson(out))
	return out, nil
}

func (s *RedPacket) ReportRedPacketClickCnt(ctx context.Context, req *pb.ReportRedPacketClickCntReq) (*pb.ReportRedPacketClickCntResp, error) {
	out := &pb.ReportRedPacketClickCntResp{}

	if req.GetUid() == 0 || req.GetChannelId() == 0 || req.GetCnt() == 0 || req.GetOrderId() == "" {
		return out, nil
	}

	// 为防止新黑产收割走礼物，加入随机元素
	randCnt := uint32(rand.Intn(10000)) // #nosec
	cnt := req.GetCnt()%100 + randCnt*100

	err := s.mgr.GetCache().AddRedPacketClickCnt(req.GetOrderId(), req.GetUid(), cnt)
	if err != nil {
		log.ErrorWithCtx(ctx, "ReportRedPacketClickCnt fail to AddRedPacketClickCnt. in:%+v, err:%v", req, err)
		return out, err
	}

	log.DebugWithCtx(ctx, "ReportRedPacketClickCnt in %+v", req)
	return out, nil
}

func (s *RedPacket) Notify(ctx context.Context, req *UnifiedPayCallback.PayNotify) (*UnifiedPayCallback.PayNotifyResponse, error) {
	log.InfoWithCtx(ctx, "Notify req=%+v", req)
	out := &UnifiedPayCallback.PayNotifyResponse{}
	op, err := s.mgr.Callback(ctx, req.GetOutTradeNo())
	if err != nil {
		log.ErrorWithCtx(ctx, "Notify fail to Callback. in:%+v, err:%v", req, err)
		return out, err
	}

	out.Confirmed = true
	out.Op = op

	return out, nil
}

func (s *RedPacket) GetRedPacketOrderTotal(ctx context.Context, req *pb.GetRedPacketOrderTotalReq) (*pb.GetRedPacketOrderTotalResp, error) {
	out := &pb.GetRedPacketOrderTotalResp{}
	if req.GetBeginTime() == 0 || req.GetEndTime() == 0 {
		return out, nil
	}

	beginTime := time.Unix(int64(req.GetBeginTime()), 0)
	endTime := time.Unix(int64(req.GetEndTime()), 0)

	cnt, totalPrice, err := s.mgr.GetRedPacketOrderTotal(ctx, beginTime, endTime)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetRedPacketOrderTotal fail to GetRedPacketOrderTotal. in:%+v, err:%v", req, err)
		return out, err
	}

	out.TotalCnt = cnt
	out.TotalPrice = totalPrice

	return out, nil
}

func (s *RedPacket) GetRedPacketAwardTotal(ctx context.Context, req *pb.GetRedPacketAwardTotalReq) (*pb.GetRedPacketAwardTotalResp, error) {
	out := &pb.GetRedPacketAwardTotalResp{}
	if req.GetBeginTime() == 0 || req.GetEndTime() == 0 {
		return out, nil
	}

	beginTime := time.Unix(int64(req.GetBeginTime()), 0)
	endTime := time.Unix(int64(req.GetEndTime()), 0)

	cnt, totalPrice, err := s.mgr.GetRedPacketAwardTotal(ctx, beginTime, endTime)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetRedPacketAwardTotal fail to GetRedPacketAwardTotal. in:%+v, err:%v", req, err)
		return out, err
	}

	out.TotalCnt = cnt
	out.TotalPrice = totalPrice

	return out, nil
}

func (s *RedPacket) GetAwardTotalCount(ctx context.Context, req *reconcile_v2.TimeRangeReq) (*reconcile_v2.CountResp, error) {
	out := &reconcile_v2.CountResp{}
	if req.GetBeginTime() == 0 || req.GetEndTime() == 0 {
		return out, nil
	}

	beginTime := time.Unix(req.GetBeginTime(), 0)
	endTime := time.Unix(req.GetEndTime(), 0)

	cnt, totalPrice, err := s.mgr.GetRedPacketAwardTotal(ctx, beginTime, endTime)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAwardTotalCount fail to GetRedPacketAwardTotal. in:%+v, err:%v", req, err)
		return out, err
	}

	out.Count = cnt
	out.Value = totalPrice

	return out, nil
}

func (s *RedPacket) GetAwardOrderIds(ctx context.Context, req *reconcile_v2.TimeRangeReq) (*reconcile_v2.OrderIdsResp, error) {
	out := &reconcile_v2.OrderIdsResp{}
	if req.GetBeginTime() == 0 || req.GetEndTime() == 0 {
		return out, nil
	}

	var err error
	beginTime := time.Unix(req.GetBeginTime(), 0)
	endTime := time.Unix(req.GetEndTime(), 0)

	out.OrderIds, err = s.mgr.GetRedPacketAwardOrderIds(ctx, beginTime, endTime)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAwardOrderIds fail to GetRedPacketAwardOrderIds. in:%+v, err:%v", req, err)
		return out, err
	}

	return out, nil
}

func (s *RedPacket) GetConsumeTotalCount(ctx context.Context, req *reconcile_v2.TimeRangeReq) (*reconcile_v2.CountResp, error) {
	out := &reconcile_v2.CountResp{}
	if req.GetBeginTime() == 0 || req.GetEndTime() == 0 {
		return out, nil
	}

	beginTime := time.Unix(req.GetBeginTime(), 0)
	endTime := time.Unix(req.GetEndTime(), 0)

	cnt, totalPrice, err := s.mgr.GetRedPacketOrderTotal(ctx, beginTime, endTime)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetConsumeTotalCount fail to GetRedPacketOrderTotal. in:%+v, err:%v", req, err)
		return out, err
	}

	out.Count = cnt
	out.Value = totalPrice

	return out, nil
}

func (s *RedPacket) GetConsumeOrderIds(ctx context.Context, req *reconcile_v2.TimeRangeReq) (*reconcile_v2.OrderIdsResp, error) {
	out := &reconcile_v2.OrderIdsResp{}
	if req.GetBeginTime() == 0 || req.GetEndTime() == 0 {
		return out, nil
	}

	var err error
	beginTime := time.Unix(req.GetBeginTime(), 0)
	endTime := time.Unix(req.GetEndTime(), 0)

	out.OrderIds, err = s.mgr.GetRedPacketConsumeOrderIds(ctx, beginTime, endTime)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetConsumeOrderIds fail to GetRedPacketConsumeOrderIds. in:%+v, err:%v", req, err)
		return out, err
	}

	return out, nil
}

func (s *RedPacket) BatGetRedPacketChannel(ctx context.Context, req *pb.BatGetRedPacketChannelReq) (out *pb.BatGetRedPacketChannelResp, err error) {
	return s.mgr.BatGetRedPacketChannel(ctx, req.ChannelIds)
}
