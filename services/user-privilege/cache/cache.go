package cache

import (
	"fmt"
	"github.com/go-redis/redis"
	"gitlab.ttyuyin.com/avengers/tyr/compatible/proto"
	pb "golang.52tt.com/protocol/services/userprivilege"
	"strconv"
	"time"
)

type UserPrivilegeCache struct {
	redisClient *redis.Client
}

func NewUserPrivilegeCache(r *redis.Client) *UserPrivilegeCache {
	return &UserPrivilegeCache{redisClient: r}
}

func userPriKey(uid, ty uint32) string {
	return fmt.Sprintf("user_privilege_%v_%v", uid, ty)
}
func (c *UserPrivilegeCache) AddUserPrivilege(priList ...*pb.UserPrivilegeInfo) error {
	pipe := c.redisClient.Pipeline()
	for _, pri := range priList {
		bty, err := proto.Marshal(pri)
		if nil != err {
			continue
		}
		key := userPriKey(pri.GetUid(), uint32(pri.GetType()))
		file := strconv.Itoa(int(pri.GetPrivilegeId()))
		pipe.HSet(key, file, bty).Err()
	}
	_, err := pipe.Exec()

	return err
}

func (c *UserPrivilegeCache) GetUserPrivilege(uid uint32, pty pb.UserPrivilegeType) ([]*pb.UserPrivilegeInfo, error) {
	res := c.redisClient.HGetAll(userPriKey(uid, uint32(pty))).Val()
	expirePriList := make([]*pb.UserPrivilegeInfo, 0)
	currPriList := make([]*pb.UserPrivilegeInfo, 0)
	nowTs := time.Now()
	for _, v := range res {
		pri := &pb.UserPrivilegeInfo{}
		err := proto.Unmarshal([]byte(v), pri)
		if nil != err {
			continue
		}
		if pri.ExpireTime < nowTs.Unix() && pty != pb.UserPrivilegeType_INTERACTION_EMOJI {
			expirePriList = append(expirePriList, pri)
			continue
		}
		currPriList = append(currPriList, pri)
	}

	if len(expirePriList) > 0 {
		go func() {
			c.DelPrivilegeList(expirePriList...)
		}()
	}
	return currPriList, nil
}

func (c *UserPrivilegeCache) GetCacheDB() *redis.Client {
	return c.redisClient
}

func (c *UserPrivilegeCache) BatchGetUserPrivilege(uidList []uint32, pty pb.UserPrivilegeType) (map[uint32][]*pb.UserPrivilegeInfo, error) {
	uid2PriList := make(map[uint32][]*pb.UserPrivilegeInfo)
	pipe := c.redisClient.Pipeline()
	for _, uid := range uidList {
		pipe.HGetAll(userPriKey(uid, uint32(pty)))
	}
	cmds, err := pipe.Exec()
	if nil != err {
		return nil, err
	}

	expirePriList := make([]*pb.UserPrivilegeInfo, 0)

	nowTs := time.Now()
	for _, res := range cmds {
		arr, ok := res.(*redis.StringStringMapCmd)
		if ok {
			for _, v := range arr.Val() {
				pri := &pb.UserPrivilegeInfo{}
				err := proto.Unmarshal([]byte(v), pri)
				if nil != err {
					continue
				}
				if pri.GetExpireTime() < nowTs.Unix() && pri.GetType() != pb.UserPrivilegeType_INTERACTION_EMOJI {
					expirePriList = append(expirePriList, pri)
					continue
				}
				if _, ok := uid2PriList[pri.Uid]; !ok {
					uid2PriList[pri.Uid] = make([]*pb.UserPrivilegeInfo, 0)
				}
				uid2PriList[pri.Uid] = append(uid2PriList[pri.Uid], pri)
			}
		}
	}

	if len(expirePriList) > 0 {
		go func() {
			c.DelPrivilegeList(expirePriList...)
		}()
	}

	return uid2PriList, nil
}

func (c *UserPrivilegeCache) DelPrivilegeList(expirePriList ...*pb.UserPrivilegeInfo) {
	pipe := c.redisClient.Pipeline()
	for _, pri := range expirePriList {
		pipe.HDel(userPriKey(pri.GetUid(), uint32(pri.GetType())), strconv.Itoa(int(pri.GetPrivilegeId())))
	}
	pipe.Exec()
}
