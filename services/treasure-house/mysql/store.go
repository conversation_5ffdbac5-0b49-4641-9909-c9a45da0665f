package mysql

import (
    "context"
    "errors"
    _ "github.com/go-sql-driver/mysql" // MySQL驱动。
    "golang.52tt.com/pkg/log"
    "golang.52tt.com/services/treasure-house/conf"
    "gorm.io/driver/mysql"
    "gorm.io/gorm"
    "reflect"
    "regexp"
    "strings"
)

type Store struct {
    db *gorm.DB
}

func NewMysql(sc *conf.ServiceConfigT) (*Store, error) {
    var mysqlDb *gorm.DB
    var err error
    cfg := sc.GetMysqlConfig()
    if cfg == nil {
        return nil, errors.New("mysql config is nil")
    }
    if mysqlDb, err = gorm.Open(mysql.New(mysql.Config{
        DSN:                       cfg.ConnectionString(),
        DefaultStringSize:         256,   // default size for string fields
        DisableDatetimePrecision:  true,  // disable datetime precision, which not supported before MySQL 5.6
        DontSupportRenameIndex:    true,  // drop & create when rename index, rename index not supported before MySQL 5.7, MariaDB
        DontSupportRenameColumn:   true,  // `change` when rename column, rename column not supported before MySQL 8, MariaDB
        SkipInitializeWithVersion: false, // auto configure based on currently MySQL version
    }), &gorm.Config{}); err != nil {
        return nil, err
    }

    if sc.IsTest() {
        mysqlDb = mysqlDb.Debug()
    }

    return &Store{
        db: mysqlDb,
    }, nil
}

func (s *Store) Transaction(ctx context.Context, f func(tx *gorm.DB) error) error {
    tx := s.db.WithContext(ctx).Begin()
    defer func() {
        if r := recover(); r != nil {
            tx.Rollback()
        }
    }()
    err := f(tx)
    if err != nil {
        log.ErrorWithCtx(ctx, "Transaction fail err %v", err)
        _ = tx.Rollback()
        return err
    }
    return tx.Commit().Error
}

func GenerateUpdateMap(t interface{}) map[string]interface{} {
    updateMap := make(map[string]interface{})
    tType := reflect.TypeOf(t)
    tValue := reflect.ValueOf(t)
    for i := 0; i < tType.NumField(); i++ {
        field := tType.Field(i)
        fieldValue := tValue.Field(i)
        gormTag := field.Tag.Get("gorm")

        r, _ := regexp.Compile(`column:(\w*);?`)
        matches := r.FindStringSubmatch(gormTag)
        if len(matches) < 2 {
            log.Errorf("GenerateUpdateMap reg err, tag: %s", gormTag)
            continue
        }
        columnKey := strings.TrimSpace(matches[1])
        if columnKey == "" {
            continue
        }
        updateMap[columnKey] = fieldValue.Interface()
    }
    delete(updateMap, "create_time")
    delete(updateMap, "id")
    log.Debugf("GenerateUpdateMap updateMap: %+v", updateMap)
    return updateMap
}
