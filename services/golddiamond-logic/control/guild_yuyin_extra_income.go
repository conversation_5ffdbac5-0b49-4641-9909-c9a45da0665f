package control

import (
	"context"
	"encoding/json"
	pb "golang.52tt.com/protocol/services/gold-commission"
	"net/http"
	"time"

	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/web"
	"golang.52tt.com/services/golddiamond-logic/models"
	api "golang.52tt.com/services/golddiamond-logic/models/gen-go"
)

// GetGuildYuyinExtraIncomeHandler 获取语音直播按月份额外收益
func GetGuildYuyinExtraIncomeHandler(authInfo *web.AuthInfo, w http.ResponseWriter, r *http.Request) {
	ctx, cancel := context.WithTimeout(r.Context(), time.Second*10)
	defer cancel()

	// 解析请求参数
	var req api.GetGuildYuyinExtraIncomeReq
	err := json.Unmarshal(authInfo.Body, &req)
	if err != nil {
		log.ErrorWithCtx(ctx, "Failed to parse request body [%s], err [%+v]", string(authInfo.Body), err)
		web.ServeAPICodeJson(w, int32(api.ErrorCode_ERROR_CODE_UNMARSHAL), "unmarshal err",
			&api.GetGuildYuyinExtraIncomeRsp{})
		return
	}
	log.DebugfWithCtx(ctx, "GetGuildYuyinExtraIncomeHandler body:[%+v] req:[%+v]", string(authInfo.Body), req)

	// 校验参数合法性
	if req.GetGuildid() == 0 || req.GetUid() == "" {
		log.ErrorWithCtx(ctx, "GetGuildYuyinExtraIncomeHandler param err: guildId:[%+v]， uid:[%+v]"+
			"beginTime:[%+v], endTime:[%+v]", req.GetGuildid(), req.GetUid(), req.GetBeginTime(), req.GetEndTime())
		web.ServeAPICodeJson(w, int32(api.ErrorCode_ERROR_CODE_PARR), "param err",
			&api.GetGuildYuyinExtraIncomeRsp{})
		return
	}

	// RPC调用下游服务
	// 校验公会ID是否合法
	valid, guildUid := models.GetModelServer().CheckGuildId(ctx, req.GetGuildid(), authInfo.UserID)
	if !valid {
		log.ErrorWithCtx(ctx, "CheckGuildId param err:  guildId:[%+v], req_uid:[%+v], real_uid:[%+v]",
			req.GetGuildid(), authInfo.UserID, guildUid)
		web.ServeBadReq(w)
		return
	}
	// 构造下游请求参数
	extraReq := &pb.GetGuildYuyinExtraIncomeReq{
		Uid:       authInfo.UserID,
		GuildIds:  []uint32{req.GetGuildid()},
		BeginTime: req.GetBeginTime(),
		EndTime:   req.GetEndTime(),
	}
	// 请求下游接口
	extraRsp, err := models.GetModelServer().GoldCommissionClient.GetGuildYuyinExtraIncome(ctx, extraReq)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetGuildYuyinExtraIncome failed，err:[%+v]", err.Error())
		web.ServeAPIError(w)
		return
	}

	// 组装返回体
	rsp := &api.GetGuildYuyinExtraIncomeRsp{}
	rsp.List = make([]*api.ExtraIncome, 0)
	if extraRsp != nil {
		for _, info := range extraRsp.GetList() {
			info := &api.ExtraIncome{
				MonthRecordIncome:  info.GetMonthRecordIncome(),
				ExtraIncomeRatio:   info.GetExtraIncomeRatio(),
				ExtraIncomeRatioV2: info.GetExtraIncomeRatioV2(),
				Key:                info.GetKey(),
			}
			rsp.List = append(rsp.List, info)
		}
	}
	log.DebugfWithCtx(ctx, "GetGuildYuyinExtraIncomeHandler rsp [%+v]", rsp)
	web.ServeAPIJson(w, rsp)
}
