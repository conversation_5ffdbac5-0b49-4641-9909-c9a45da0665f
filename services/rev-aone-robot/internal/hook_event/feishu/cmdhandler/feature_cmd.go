package cmdhandler

import (
	"context"
	"fmt"
	"strings"

	"golang.52tt.com/pkg/log"
	"golang.52tt.com/services/rev-aone-robot/api/gitlab"
	"golang.52tt.com/services/rev-aone-robot/internal/common"
	dbscheme "golang.52tt.com/services/rev-aone-robot/internal/store/scheme"
	"golang.52tt.com/services/rev-aone-robot/util"
)

// FeatureCommand 特性需求分支相关的命令列表
func (m *LarkCmdHandlerMgr) FeatureCommand() []*util.ChatCommand {
	return []*util.ChatCommand{
		{
			Name:          "createFeature",
			Alias:         "cf",
			CmdDesc:       "我要在X.X.X迭代做一个功能，帮忙生成一个分支",
			ArgDesc:       " [迭代号 例如6.51.3] [关键描述语]",
			Handler:       m.CmdCreateFeature,
			ExtLoaderInfo: CommandVisibleBitmap_GROUP_WHITELIST | CommandVisibleBitmap_P2P_WHITELIST,
		},
		{
			Name:          "finishFeature",
			CmdDesc:       "做完需求了",
			ArgDesc:       " [分支名称]",
			Handler:       m.CmdFinishFeature,
			ExtLoaderInfo: CommandVisibleBitmap_GROUP_WHITELIST | CommandVisibleBitmap_P2P_WHITELIST,
		},
		{
			Name:          "listMyBranch",
			CmdDesc:       "列出我的分支",
			ArgDesc:       "",
			Handler:       m.CmdListMyBranch,
			ExtLoaderInfo: CommandVisibleBitmap_GROUP_WHITELIST | CommandVisibleBitmap_P2P_WHITELIST,
		},
	}
}

func (m *LarkCmdHandlerMgr) CmdCreateFeature(ctx context.Context, args []string) *util.CommandRet {
	larkCtxInfo := getLarkMsgInfoCtx(ctx)
	if nil == larkCtxInfo {
		return util.NewCommandRet("被玩坏了 没有context了", nil)
	}

	var retMsg string
	if len(args) < 2 {
		retMsg = "参数错误"
		return util.NewCommandRet(retMsg, fmt.Errorf(retMsg))
	}

	// [迭代号]
	iterativeRelease := args[0]
	// [关键描述语]
	keyDesc := args[1]

	// iterativeVer 迭代号 全转成小写
	iterativeRelease = strings.ToLower(iterativeRelease)

	// gitlab从master创建分支
	var gitlabApi *gitlab.GitlabRestApi
	gitlabApi = m.getUserGitlabApiClient(larkCtxInfo.Sender.UserId)
	if gitlabApi == nil {
		return util.NewCommandRet("没有找到你的gitlab账号，请联系管理员", fmt.Errorf("user gitlab token not found"))
	}

	// 查询数据库
	dao := m.storeMgr.GenDao()
	errDb, aoneUser := dao.GetUserByLarkUserID(ctx, larkCtxInfo.Sender.UserId)
	if errDb != nil {
		return util.NewCommandRet("数据库错误", errDb)
	}
	if aoneUser == nil {
		return util.NewCommandRet("没有找到你的用户信息", fmt.Errorf("user info not found"))
	}

	branchName, errName := genReleaseFeatureBranchName(aoneUser.Alias, iterativeRelease, keyDesc)
	if errName != nil {
		return util.NewCommandRet("好像是分支的名称生成出错了", errName)
	}

	_, errGit := gitlabApi.CreateBranch(branchName, "master")
	if errGit != nil {
		retMsg := fmt.Sprintf("调用gitlab创建分支[%s]失败 ：%s", branchName, errGit.Error())
		return util.NewCommandRet(retMsg, errGit)
	}

	// 记录分支信息
	errRec := m.recordNewFeatureBranch(ctx, dao, aoneUser, iterativeRelease, branchName)
	if errRec != nil {
		retMsg := fmt.Sprintf("记录分支[%s]信息到DB失败 ：%s", branchName, errRec.Error())
		return util.NewCommandRet(retMsg, errRec)
	}

	retMsg = fmt.Sprintf("创建分支[%s]成功", branchName)
	return util.NewCommandRet(retMsg, nil)

}

// genReleaseFeatureBranchName 生成特性分支名称
// release是迭代的意思：例如 6.49.x是版本号 一个版本下有多个迭代，6.49.3是迭代号
// 名称为格式为 feature/userAlias_v6_49_3_keyDesc
func genReleaseFeatureBranchName(userAlias, iterativeRelease, keyDesc string) (string, error) {
	// 检查迭代号格式是否正确
	parts := strings.Split(iterativeRelease, ".")
	if len(parts) != 3 {
		return "", fmt.Errorf("invalid release format, should be in the format 'num.num.num'")
	}

	// 迭代号中如果包含字母会报错
	for _, part := range parts {
		if strings.ContainsAny(part, "abcdefghijklmnopqrstuvwxyz") {
			return "", fmt.Errorf("invalid release format, should be in the format 'num.num.num'")
		}
	}

	// 将版本号从X.Y.Z转换为X_Y_Z
	convertedVersion := strings.Join(parts, "_")

	// 描述如果有空格转换为下划线
	convertedDesc := strings.Replace(keyDesc, " ", "_", -1)

	branchPrefix := fmt.Sprintf("feature/%s_v%s_%s", userAlias, convertedVersion, convertedDesc)
	return branchPrefix, nil

}

func (m *LarkCmdHandlerMgr) recordNewFeatureBranch(ctx context.Context,
	dao dbscheme.IAoneBaseDao, user *common.AoneUser, version string, branchName string) error {

	var opUserName string
	opUserName = user.Name

	newBranch := &common.AoneflowBranch{
		UserName:     opUserName,
		ProjectId:    m.gitlabConf.ProjectId,
		BranchName:   branchName,
		BranchType:   common.BranchType_FEATURE,
		BranchStatus: common.AoneFlowStatus_OPENED,
		SourceBranch: "master",
		BindVersion:  version,
		BranchDesc:   fmt.Sprintf("AoneFlow 特性分支"),
	}

	// 操作数据库
	errDB := dao.CreateAoneFlowBranch(ctx, newBranch)
	if errDB != nil {
		return fmt.Errorf("保存分支信息到DB失败 %s fail: %v", branchName, errDB)
	}

	return nil

}
func (m *LarkCmdHandlerMgr) CmdFinishFeature(ctx context.Context, args []string) *util.CommandRet {
	larkCtxInfo := getLarkMsgInfoCtx(ctx)
	if nil == larkCtxInfo {
		return util.NewCommandRet("被玩坏了 没有context了", nil)
	}

	return util.NewCommandRet("伦家这个组件还没有造好呢 。 不过嘛...\n 你现在可以尝试MR合并代码，并且删除本feature分支...", nil)

}
func (m *LarkCmdHandlerMgr) CmdListMyBranch(ctx context.Context, args []string) *util.CommandRet {
	larkCtxInfo := getLarkMsgInfoCtx(ctx)
	if nil == larkCtxInfo {
		return util.NewCommandRet("被玩坏了 没有context了", nil)
	}

	var aoneUser *common.AoneUser
	var branchList []common.AoneflowBranch

	var errUser error
	var errBranch error

	// 查询数据库
	dao := m.storeMgr.GenDao()
	errUser, aoneUser = dao.GetUserByLarkUserID(ctx, larkCtxInfo.Sender.UserId)
	if errUser != nil {
		log.Errorf("GetUserByLarkUserID fail %v", errUser)
		return util.NewCommandRet("数据库查询用户信息 出现错误", errUser)
	}
	if aoneUser == nil {
		return util.NewCommandRet("没有找到你的用户信息", fmt.Errorf("no user info found"))
	}
	errBranch, branchList = dao.GetUserActiveBranch(ctx, aoneUser.Name)
	if errBranch != nil {
		log.Errorf("GetUserActiveBranch fail %v", errBranch)
		return util.NewCommandRet("数据库查询分支信息 出现错误", errBranch)
	}
	if len(branchList) == 0 {
		return util.NewCommandRet("没有找到你的分支列表", fmt.Errorf("no branch found"))
	}

	var branchStr string
	for _, branch := range branchList {
		branchStr += fmt.Sprintf("分支: %s 类型: %s 关联版本: %s \n",
			branch.BranchName, branch.BranchType.Describe(), branch.BindVersion)
	}
	return util.NewCommandRet(branchStr, nil)

}
