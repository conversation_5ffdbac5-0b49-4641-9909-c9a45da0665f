package models

import (
	"context"
	"fmt"
	"gitlab.ttyuyin.com/tt-infra/tyr/log"
	"golang.52tt.com/clients/account"
	gaChannel "golang.52tt.com/protocol/app/channel"
	pushPb "golang.52tt.com/protocol/app/push"
	publicNoticePb "golang.52tt.com/protocol/services/public-notice"
	"golang.52tt.com/services/knight-group/knight-group-logic/client"
	"golang.52tt.com/services/you-know-who/client"
)

const ROLLING_COUNT = 1
const ROLLING_TIME = 10

func PushJoinKnightGroupBreakingNew(ctx context.Context, channelId uint32, anchorUser, knightUser *account.User) {
	channelInfo, serverError := clientX.ChannelClient.GetChannelSimpleInfo(ctx, 0, channelId)
	if serverError != nil {
		log.ErrorWithCtx(ctx, "PushJoinKnightGroupBreakingNew channelCli.GetChannelSimpleInfo err=%+v", serverError)
		return
	}

	BreakingNewsMessage := &publicNoticePb.CommonBreakingNewsV3{
		FromUserInfo: &publicNoticePb.UserInfo{
			Account: knightUser.GetUsername(),
			Nick:    knightUser.GetNickname(),
		},
		TargetUserInfo: &publicNoticePb.UserInfo{
			Account: anchorUser.GetUsername(),
			Nick:    anchorUser.GetNickname(),
		},
		NewsContent: fmt.Sprintf("恭喜“%v”成为主播“%v”的骑士，即日起开启守护之旅！", knightUser.GetNickname(), anchorUser.GetNickname()),
		FromUid:     knightUser.GetUid(),
		ChannelId:   channelId,
		ChannelInfo: &publicNoticePb.ChannelInfo{ChannelType: channelInfo.GetChannelType()},
		//OptData:     content,
		JumpUrl: fmt.Sprintf("tt://m.52tt.com/channel?channel_id=%d&channel_type=%d&channel_enter_source=%d", channelId,
			channelInfo.GetChannelType(), gaChannel.ChannelEnterReq_ENUM_CHANNEL_ENTER_NORMAL),
		BreakingNewsBaseOpt: &publicNoticePb.CommBreakingNewsBaseOpt{
			TriggerType:   uint32(48), // CommBreakingNewsBaseOpt_OPEN_YOU_KNOW_WHO
			RollingCount:  ROLLING_COUNT,
			RollingTime:   ROLLING_TIME,
			AnnounceScope: uint32(pushPb.CommBreakingNewsBaseOpt_INSIDE_CHANNEL),
			JumpType:      uint32(pushPb.CommBreakingNewsBaseOpt_JUMP_CLICK_INSIDE),
			JumpPosition:  uint32(pushPb.CommBreakingNewsBaseOpt_JUMP_TO_CHANNEL_CLICK),
		},
	}

	req := &publicNoticePb.PushBreakingNewsReq{
		CommonBreakingNews: BreakingNewsMessage,
	}
	_, err := client.PublicNoticeCli.PushBreakingNews(ctx, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "PushJoinKnightGroupBreakingNew PushBreakingNews failed (%d, %d) err=%+v", knightUser.GetUid(), channelId, err)
		return
	}
	log.InfoWithCtx(ctx, "PushJoinKnightGroupBreakingNew PushBreakingNews success, (%d, %d)", knightUser.GetUid(), channelId)
}
