package mgr

import (
	"context"
	"errors"
	"github.com/golang/mock/gomock"
	KChannelLiveStatsCli "golang.52tt.com/clients/mocks/channel-live-stats"
	KmCli "golang.52tt.com/clients/mocks/knight-group-members"
	KscoreCli "golang.52tt.com/clients/mocks/knight-group-score"
	channelLiveStatPb "golang.52tt.com/protocol/services/channel-live-stats"
	"golang.52tt.com/protocol/services/knightgroupmembers"
	"golang.52tt.com/services/knight-group/knight-group-mission/internal/conf"
	"golang.52tt.com/services/knight-group/knight-group-mission/internal/mocks"
	"golang.52tt.com/services/knight-group/knight-group-mission/internal/rpc"
	"golang.52tt.com/services/knight-group/knight-group-mission/internal/store"
	"testing"
	"time"
)

func TestKnightMissionMgr_GetKnightMission(t *testing.T) {
	ctx := context.Background()
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	sc := &conf.StartConfig{}
	err := sc.Parse("../../knight-group-mission.json")
	if err != nil {
		t.<PERSON><PERSON><PERSON>("config file err:%v", err)
	}
	mockCache := mocks.NewMockICache(ctl)
	mockStore := mocks.NewMockIStore(ctl)
	mockKmCli := KmCli.NewMockIClient(ctl)
	rpc.KnightMemberCli = mockKmCli
	testCases := []struct {
		name       string
		req_uid    uint32
		req_cid    uint32
		mockExpect func()
		exceptErr  bool
	}{
		{
			name: "Test_GetKnightMission error",
			mockExpect: func() {
				mockStore.EXPECT().GetAnchorUidKnightDurationTime(ctx, gomock.Any()).Return(nil, errors.New("mock error"))
			},
			exceptErr: true,
		},
		{
			name: "Test_GetKnightMission error-1",
			mockExpect: func() {
				mockStore.EXPECT().GetAnchorUidKnightDurationTime(ctx, gomock.Any()).Return(nil, nil)
				mockKmCli.EXPECT().GetKnightGroupMember(ctx, gomock.Any()).Return(&knightgroupmembers.GetKnightGroupMemberResp{}, nil)
				mockStore.EXPECT().GetKnightMissionsByToUid(ctx, gomock.Any(), gomock.Any()).Return(nil, nil)
				mockStore.EXPECT().GetKnightMissionHistoryByOrderId(ctx, gomock.Any()).Return(nil, errors.New("mock err 1"))
			},
			exceptErr: true,
		},
		{
			name: "Test_GetKnightMission ok",
			mockExpect: func() {
				mockStore.EXPECT().GetAnchorUidKnightDurationTime(ctx, gomock.Any()).Return(nil, nil)
				mockKmCli.EXPECT().GetKnightGroupMember(ctx, gomock.Any()).Return(&knightgroupmembers.GetKnightGroupMemberResp{}, nil)
				mockStore.EXPECT().GetKnightMissionsByToUid(ctx, gomock.Any(), gomock.Any()).Return([]*store.KnightMission{
					&store.KnightMission{
						OrderId: "UUUUUUXXXX",
					},
				}, nil)
				mockStore.EXPECT().GetKnightMissionHistoryByOrderId(ctx, gomock.Any()).Return(nil, nil)
			},
		},
	}

	m := &KnightMissionMgr{
		store: mockStore,
		cache: mockCache,
		sc:    sc,
	}
	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			tc.mockExpect()
			_, err := m.GetKnightMission(ctx, tc.req_uid, tc.req_cid)
			if (err != nil) != tc.exceptErr {
				t.Errorf("name:%s, exceptErr %v, but got err: %v", tc.name, tc.exceptErr, err)
				return
			}
		})
	}
}

func TestKnightMissionMgr_AddKnightMission(t *testing.T) {
	ctx := context.Background()
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockCache := mocks.NewMockICache(ctl)
	mockStore := mocks.NewMockIStore(ctl)
	NoMission = false
	event := &knightgroupmembers.JoinKnightGroupEvent{
		OrderId:     "testOrder_1655781979_3",
		AnchorUid:   2202538,
		KnightUid:   2402972,
		ChannelId:   2040103,
		ChannelType: 7,
		GuildId:     0,
		Price:       20000,
		CreateTime:  1655783179,
		BeginTime:   1655783179,
		ExpireTime:  1655882279,
	}
	now := time.Now()
	testCases := []struct {
		name       string
		event      *knightgroupmembers.JoinKnightGroupEvent
		mockExpect func()
		exceptErr  bool
	}{
		{
			name:  "TestKnightMissionMgr_AddKnightMission error",
			event: event,
			mockExpect: func() {
				mockStore.EXPECT().AddKnightMission(ctx, gomock.Any()).Return(errors.New("mock error"))
			},
			exceptErr: true,
		},

		{
			name:  "TestKnightMissionMgr_AddKnightMission error-1",
			event: event,
			mockExpect: func() {
				mockStore.EXPECT().AddKnightMission(ctx, gomock.Any()).Return(nil)
				mockStore.EXPECT().GetKnightDurationTime(ctx, gomock.Any(), gomock.Any()).Return(now, now, false, nil)
				mockStore.EXPECT().SetKnightDurationTime(ctx, gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(errors.New("mock error"))
			},
			exceptErr: true,
		},
		{
			name:  "TestKnightMissionMgr_AddKnightMission error-2",
			event: event,
			mockExpect: func() {
				mockStore.EXPECT().AddKnightMission(ctx, gomock.Any()).Return(nil)
				mockStore.EXPECT().GetKnightDurationTime(ctx, gomock.Any(), gomock.Any()).Return(now, now, true, nil)
				mockStore.EXPECT().UpdateKnightDurationTime(ctx, gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(errors.New("mock error"))
			},
			exceptErr: true,
		},
	}

	m := &KnightMissionMgr{
		store: mockStore,
		cache: mockCache,
	}
	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			tc.mockExpect()
			err := m.AddKnightMission(ctx, tc.event)
			if (err != nil) != tc.exceptErr {
				t.Errorf("name:%s, exceptErr %v, but got err: %v", tc.name, tc.exceptErr, err)
				return
			}
		})
	}
	//fmt.Printf("%+v", resp.TimeStringNow)
}

func Test_GetOrderForSettle(t *testing.T) {
	ctx := context.Background()
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	sc := &conf.StartConfig{}
	err := sc.Parse("../../knight-group-mission.json")
	if err != nil {
		t.Errorf("config file err:%v", err)
	}
	mockCache := mocks.NewMockICache(ctl)
	mockStore := mocks.NewMockIStore(ctl)
	testCases := []struct {
		name       string
		nowTs      time.Time
		week       uint32
		mockExpect func()
		exceptErr  bool
	}{
		{
			name: "Test_GetOrderForSettle error",
			mockExpect: func() {
				mockStore.EXPECT().GetKnightMissionsByCreateTime(ctx, gomock.Any(), gomock.Any()).Return(nil, errors.New("mock error"))
			},
			exceptErr: true,
			nowTs:     time.Now(),
			week:      1,
		},
		{
			name: "Test_GetOrderForSettle ok",
			mockExpect: func() {
				mockStore.EXPECT().GetKnightMissionsByCreateTime(ctx, gomock.Any(), gomock.Any()).Return(nil, nil)
			},
			nowTs: time.Now(),
			week:  1,
		},
	}

	m := &KnightMissionMgr{
		store: mockStore,
		cache: mockCache,
		sc:    sc,
	}
	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			tc.mockExpect()
			_, _, _, err := m.GetOrderForSettle(ctx, tc.nowTs, tc.week)
			if (err != nil) != tc.exceptErr {
				t.Errorf("name:%s, exceptErr %v, but got err: %v", tc.name, tc.exceptErr, err)
				return
			}
		})
	}
}

func Test_GetKnightMissionByTimeRange(t *testing.T) {
	ctx := context.Background()
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	sc := &conf.StartConfig{}
	err := sc.Parse("../../knight-group-mission.json")
	if err != nil {
		t.Errorf("config file err:%v", err)
	}
	mockCache := mocks.NewMockICache(ctl)
	mockStore := mocks.NewMockIStore(ctl)
	testCases := []struct {
		name       string
		begin      time.Time
		end        time.Time
		mockExpect func()
		exceptErr  bool
	}{
		{
			name: "Test_GetKnightMissionByTimeRange error",
			mockExpect: func() {
				mockStore.EXPECT().GetKnightMissionByOutsideTime(ctx, gomock.Any(), gomock.Any()).Return(nil, errors.New("mock error"))
			},
			exceptErr: true,
			begin:     time.Now(),
			end:       time.Now().AddDate(0, 1, 0),
		},
		{
			name: "Test_GetKnightMissionByTimeRange ok",
			mockExpect: func() {
				mockStore.EXPECT().GetKnightMissionByOutsideTime(ctx, gomock.Any(), gomock.Any()).Return(nil, nil)
			},
			begin: time.Now(),
			end:   time.Now().AddDate(0, 1, 0),
		},
	}

	m := &KnightMissionMgr{
		store: mockStore,
		cache: mockCache,
		sc:    sc,
	}
	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			tc.mockExpect()
			_, err := m.GetKnightMissionByTimeRange(ctx, tc.begin, tc.end)
			if (err != nil) != tc.exceptErr {
				t.Errorf("name:%s, exceptErr %v, but got err: %v", tc.name, tc.exceptErr, err)
				return
			}
		})
	}
}

func Test_SettleKnightMissionHandler(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	sc := &conf.StartConfig{
		TestConfig: &conf.TestConfig{
			NowTime: "2023-11-09 00:00:00",
		},
	}
	err := sc.Parse("../../knight-group-mission.json")
	if err != nil {
		t.Errorf("config file err:%v", err)
	}
	mockCache := mocks.NewMockICache(ctl)
	mockStore := mocks.NewMockIStore(ctl)
	testCases := []struct {
		name       string
		mockExpect func()
		exceptErr  bool
	}{
		{
			name: "Test_SettleKnightMissionHandler ok",
			mockExpect: func() {
				mockCache.EXPECT().CheckDaySettleCount(gomock.Any(), gomock.Any()).Return(false, nil)
			},
		},
	}

	m := &KnightMissionMgr{
		store: mockStore,
		cache: mockCache,
		sc:    sc,
	}
	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			tc.mockExpect()
			m.SettleKnightMissionHandler()
		})
	}
}

func Test_SettleKnightMission(t *testing.T) {
	ctx := context.Background()
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	sc := &conf.StartConfig{}
	err := sc.Parse("../../knight-group-mission.json")
	if err != nil {
		t.Errorf("config file err:%v", err)
	}
	mockCache := mocks.NewMockICache(ctl)
	mockStore := mocks.NewMockIStore(ctl)
	mockKmCli := KmCli.NewMockIClient(ctl)
	mockKscoreCli := KscoreCli.NewMockIClient(ctl)
	mockKChannelLiveStatsCli := KChannelLiveStatsCli.NewMockIClient(ctl)
	rpc.KnightMemberCli = mockKmCli
	rpc.KnightScoreCli = mockKscoreCli
	rpc.ChannelLiveCli = mockKChannelLiveStatsCli
	testCases := []struct {
		name       string
		req_uid    uint32
		req_cid    uint32
		mockExpect func()
		exceptErr  bool
	}{
		{
			name: "Test_SettleKnightMission error",
			mockExpect: func() {
				mockKChannelLiveStatsCli.EXPECT().GetAnchorDailyRecordWithDateList(ctx, gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&channelLiveStatPb.GetAnchorDailyRecordWithDateListResp{}, nil)
				mockStore.EXPECT().RecordKnightMissionHistory(ctx, gomock.Any()).Return(errors.New("mock error"))
			},
			exceptErr: true,
		},
		{
			name: "Test_SettleKnightMission ok",
			mockExpect: func() {
				mockKChannelLiveStatsCli.EXPECT().GetAnchorDailyRecordWithDateList(ctx, gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&channelLiveStatPb.GetAnchorDailyRecordWithDateListResp{
					List: []*channelLiveStatPb.AnchorDailyStats{
						&channelLiveStatPb.AnchorDailyStats{
							LiveValidMinutes: 200,
							IsValidDay:       true,
						},
						&channelLiveStatPb.AnchorDailyStats{
							LiveValidMinutes: 200,
							IsValidDay:       true,
						},
						&channelLiveStatPb.AnchorDailyStats{
							LiveValidMinutes: 200,
							IsValidDay:       true,
						},
					},
				}, nil)
				mockStore.EXPECT().RecordKnightMissionHistory(ctx, gomock.Any()).Return(nil)
				mockKscoreCli.EXPECT().AddKnightScore(ctx, gomock.Any()).Return(nil, nil)
			},
		},
	}

	m := &KnightMissionMgr{
		store: mockStore,
		cache: mockCache,
		sc:    sc,
	}
	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			tc.mockExpect()
			err := m.SettleKnightMission(ctx, &store.KnightMission{
				TotalPrice: 100000,
				OrderId:    "orderid_1",
			}, time.Now(), 1)
			if (err != nil) != tc.exceptErr {
				t.Errorf("name:%s, exceptErr %v, but got err: %v", tc.name, tc.exceptErr, err)
				return
			}
		})
	}
}
