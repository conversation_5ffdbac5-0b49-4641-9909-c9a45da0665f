package manager

import (
	"context"
	"fmt"
	"github.com/jinzhu/gorm"
	"golang.52tt.com/pkg/log"
	pb "golang.52tt.com/protocol/services/presentextraconf"
	"golang.52tt.com/protocol/services/userpresent"
	"golang.52tt.com/services/present-extra-conf/internal/conf"
	"golang.52tt.com/services/present-extra-conf/internal/rpc"
	"golang.52tt.com/services/present-extra-conf/internal/store"
	"sort"
	"strconv"
	"strings"
	"time"
)

const (
	JumpUrlTypeNormal   = 1
	JumpUrlTypeActivity = 2
)

type ExtraConfManager struct {
	store *store.Store
	stop  chan interface{}
}

type ExtraStats struct {
	LastUpdateTime uint32 `json:"last_update_time"`
}

func NewExtraConfManager(ctx context.Context, sc *conf.ServiceConfigT) (*ExtraConfManager, error) {

	var extraConfManager ExtraConfManager

	extraConfManager.stop = make(chan interface{})

	st, err := store.NewStore(ctx, sc)
	if err != nil {
		log.ErrorWithCtx(ctx, "NewExtraConfManager err , err: %v", err)
		return &extraConfManager, err
	}

	extraConfManager.store = st

	err = rpc.Setup()
	if err != nil {
		return nil, err
	}

	return &extraConfManager, nil
}

func NewMockManager(ctx context.Context, sc *conf.ServiceConfigT, db *gorm.DB) (*ExtraConfManager, error) {

	var extraConfManager ExtraConfManager

	extraConfManager.stop = make(chan interface{})

	st, err := store.NewMockStore(ctx, sc, db)
	if err != nil {
		log.ErrorWithCtx(ctx, "NewExtraConfManager err , err: %v", err)
		return &extraConfManager, err
	}

	extraConfManager.store = st

	//err = rpc.Setup()
	//if err != nil {
	//	return nil, err
	//}

	return &extraConfManager, nil
}

func (m *ExtraConfManager) SearchPresent(ctx context.Context, page, count, keyId uint32, keyWord string) ([]*pb.PresentBaseConfig, uint32, error) {
	out := make([]*pb.PresentBaseConfig, 0)

	resp, err := rpc.UserPresentClient.GetPresentConfigList(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "SearchPresent GetPresentConfigList err , err: %v", err)
		return out, 0, err
	}

	tmp := make([]*userpresent.StPresentItemConfig, 0)
	for _, item := range resp.GetItemList() {
		if keyWord != "" {
			if !strings.Contains(item.Name, keyWord) {
				continue
			}
		}
		if keyId != 0 {
			if !MatchNumber(item.ItemId, keyId) {
				continue
			}
		}
		tmp = append(tmp, item)
	}

	sort.Slice(tmp, func(i, j int) bool {
		return tmp[i].ItemId < tmp[j].ItemId
	})

	all := len(tmp)
	if count != 0 && page != 0 {
		tmp = tmp[min((page-1)*count, uint32(len(tmp)-1)):min(page*count, uint32(len(tmp)))]
	}
	for _, item := range tmp {
		out = append(out, &pb.PresentBaseConfig{
			GiftId:     item.GetItemId(),
			GiftName:   item.GetName(),
			PriceValue: item.GetPrice(),
			PriceType:  item.GetPriceType(),
			GiftImage:  item.GetIconUrl(),
		})
	}

	return out, uint32(all), err
}

func (m *ExtraConfManager) AddPresentFloat(ctx context.Context, req *pb.PresentFloatLayer) error {

	err := m.store.AddFloatConfig(ctx, &store.PresentFloatLayer{
		GiftId:              req.GetGiftId(),
		FrameImageUrl:       req.GetFloatImageUrl(),
		JumpUrl:             req.GetJumpUrl(),
		JumpUrlType:         urlType(req.GetIsActivityUrl()),
		EffectBegin:         time.Unix(int64(req.GetEffectBegin()), 0).Local(),
		EffectEnd:           time.Unix(int64(req.GetEffectEnd()), 0).Local(),
		Operator:            req.GetOperator(),
		ShowChannelTypeList: tranShowTypeToString(req.GetShowChannelType()),
		ShowAppTypeList:     tranAppTypeToString(req.GetShowAppType()),
		AppUrlList:          tranShowAppUrlToString(req.GetAppJumpUrl()),
		ActivityType:        uint32(req.GetActivityType()),
		SubActivityType:     uint32(req.GetSubActivityType()),
		ActivityName:        req.GetActivityName(),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "AddPresentFloat AddFloatConfig err , err: %v", err)
		return err
	}

	_ = m.store.SetLastUpdateTime(ctx, time.Now())
	return nil
}

func (m *ExtraConfManager) DelPresentFloat(ctx context.Context, id uint32) error {
	err := m.store.DelFloatConfig(ctx, id)
	if err != nil {
		log.ErrorWithCtx(ctx, "DelPresentFloat DelFloatConfig err , err: %v", err)
		return err
	}
	_ = m.store.SetLastUpdateTime(ctx, time.Now())
	return nil
}

func (m *ExtraConfManager) UpdatePresentFloat(ctx context.Context, req *pb.PresentFloatLayer) error {
	// 新增了AppUrlList之后，旧的JumpUrl字段只做兜底用，一旦填了新的就不用旧的了
	if len(req.GetAppJumpUrl()) > 0 {
		req.JumpUrl = ""
	}

	err := m.store.UpdateFloatConfig(ctx, &store.PresentFloatLayer{
		GiftId:              req.GetGiftId(),
		FrameImageUrl:       req.GetFloatImageUrl(),
		JumpUrl:             req.GetJumpUrl(),
		JumpUrlType:         urlType(req.GetIsActivityUrl()),
		EffectBegin:         time.Unix(int64(req.GetEffectBegin()), 0).Local(),
		EffectEnd:           time.Unix(int64(req.GetEffectEnd()), 0).Local(),
		Operator:            req.GetOperator(),
		UpdatedAt:           time.Now(),
		ShowChannelTypeList: tranShowTypeToString(req.GetShowChannelType()),
		ShowAppTypeList:     tranAppTypeToString(req.GetShowAppType()),
		AppUrlList:          tranShowAppUrlToString(req.GetAppJumpUrl()),
		ActivityType:        uint32(req.GetActivityType()),
		SubActivityType:     uint32(req.GetSubActivityType()),
		ActivityName:        req.GetActivityName(),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "UpdatePresentFloat UpdateFloatConfig err , err: %v", err)
		return err
	}
	_ = m.store.SetLastUpdateTime(ctx, time.Now())
	return nil
}

func (m *ExtraConfManager) GetPresentFloat(ctx context.Context, page, count, keyId, keyStatus uint32, keyWord string) ([]*pb.PresentFloatInfo, uint32, uint32, error) {
	out := make([]*pb.PresentFloatInfo, 0)

	resp, err := m.store.GetAllFloatConfig(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "SearchPresent GetPresentConfigList err , err: %v", err)
		return out, 0, 0, err
	}

	giftList := make([]uint32, 0)
	for _, item := range resp {
		giftList = append(giftList, item.GiftId)
	}

	presentConfig, err := rpc.UserPresentClient.GetPresentConfigByIdList(ctx, 0, giftList, 0)
	if err != nil {
		log.ErrorWithCtx(ctx, "SearchPresent GetPresentConfigByIdList err , err: %v", err)
		return out, 0, 0, err
	}

	presentConfigMap := make(map[uint32]*userpresent.StPresentItemConfig)
	for _, item := range presentConfig.GetItemList() {
		presentConfigMap[item.ItemId] = item
	}

	tmp := make([]*store.PresentFloatLayer, 0)
	for _, item := range resp {
		if !isSearchFloat(keyWord, keyId, keyStatus, presentConfigMap, item) {
			continue
		}

		tmp = append(tmp, item)
	}

	sort.Slice(tmp, func(i, j int) bool {
		return tmp[i].EffectBegin.Unix() > tmp[j].EffectBegin.Unix()
	})

	if len(resp) == 0 {
		return out, 0, 0, nil
	}

	all := len(tmp)

	if count != 0 && page != 0 {
		tmp = tmp[min((page-1)*count, uint32(len(tmp)-1)):min(page*count, uint32(len(tmp)))]
	}

	nowTs := time.Now().Unix()
	for _, item := range tmp {
		status := uint32(pb.EffectStatus_EffectStatus_Effective)
		if nowTs < item.EffectBegin.Unix() {
			status = uint32(pb.EffectStatus_EffectStatus_Waiting)
		}

		if nowTs > item.EffectEnd.Unix() {
			status = uint32(pb.EffectStatus_EffectStatus_Outdated)
		}

		out = append(out, &pb.PresentFloatInfo{
			LayerInfo: &pb.PresentFloatLayer{
				GiftId:          item.GiftId,
				FloatImageUrl:   item.FrameImageUrl,
				JumpUrl:         item.JumpUrl,
				IsActivityUrl:   isUrlActivity(item.JumpUrlType),
				EffectBegin:     uint32(item.EffectBegin.Unix()),
				EffectEnd:       uint32(item.EffectEnd.Unix()),
				Operator:        item.Operator,
				EffectStatus:    status,
				UpdateTime:      uint32(item.UpdatedAt.Unix()),
				ShowChannelType: tranStringToShowType(item.ShowChannelTypeList),
				ShowAppType:     tranStringToAppType(item.ShowAppTypeList),
				AppJumpUrl:      tranStringToShowAppUrl(item.JumpUrl, item.AppUrlList),
				ActivityType:    pb.PresentFloatLayer_ActivityType(item.ActivityType),
				SubActivityType: pb.PresentFloatLayer_SubActivityType(item.SubActivityType),
				ActivityName:    item.ActivityName,
			},
			PresentConfig: &pb.PresentBaseConfig{
				GiftId:     presentConfigMap[item.GiftId].GetItemId(),
				GiftName:   presentConfigMap[item.GiftId].GetName(),
				PriceValue: presentConfigMap[item.GiftId].GetPrice(),
				PriceType:  presentConfigMap[item.GiftId].GetPriceType(),
				GiftImage:  presentConfigMap[item.GiftId].GetIconUrl(),
			},
		})
	}
	lastUpdate, _ := m.store.GetLastUpdateTime(ctx)

	return out, uint32(all), uint32(lastUpdate.Unix()), err
}

func (m *ExtraConfManager) AddFlashConfig(ctx context.Context, req *pb.FlashEffectConfig) error {
	err := m.store.AddFlashConfig(ctx, &store.FlashEffectConfig{
		FlashId:   req.GetFlashId(),
		FlashName: req.GetFlashName(),
		FlashUrl:  req.GetFlashUrl(),
		FlashMd5:  req.GetFlashMd5(),
		Operator:  req.GetOperator(),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "AddFlashConfig AddFlashConfig err , err: %v", err)
		return err
	}
	_ = m.store.SetLastUpdateTime(ctx, time.Now())
	return nil
}

func (m *ExtraConfManager) DelFlashConfig(ctx context.Context, id uint32) error {
	err := m.store.DelFlashConfig(ctx, id)
	if err != nil {
		log.ErrorWithCtx(ctx, "DelFlashConfig DelFlashConfig err , err: %v", err)
		return err
	}
	_ = m.store.SetLastUpdateTime(ctx, time.Now())
	return nil
}

func (m *ExtraConfManager) UpdateFlashConfig(ctx context.Context, req *pb.FlashEffectConfig) error {
	err := m.store.UpdateFlashConfig(ctx, &store.FlashEffectConfig{
		FlashId:   req.GetFlashId(),
		FlashName: req.GetFlashName(),
		FlashUrl:  req.GetFlashUrl(),
		FlashMd5:  req.GetFlashMd5(),
		Operator:  req.GetOperator(),
		UpdatedAt: time.Now(),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "UpdateFlashConfig UpdateFlashConfig err , err: %v", err)
		return err
	}
	_ = m.store.SetLastUpdateTime(ctx, time.Now())
	return nil
}

func (m *ExtraConfManager) GetFlashConfig(ctx context.Context, page, count uint32, keyWord string) ([]*pb.FlashEffectConfig, uint32, error) {
	out := make([]*pb.FlashEffectConfig, 0)

	resp, err := m.store.GetAllFlashConfig(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetFlashConfig GetAllFlashConfig err , err: %v", err)
		return out, 0, err
	}

	tmp := make([]*store.FlashEffectConfig, 0)
	for _, item := range resp {
		if keyWord != "" {
			if !strings.Contains(item.FlashName, keyWord) {
				continue
			}
		}
		tmp = append(tmp, item)
	}

	sort.Slice(tmp, func(i, j int) bool {
		return tmp[i].FlashId < tmp[j].FlashId
	})

	all := uint32(len(tmp))

	if count != 0 && page != 0 {
		tmp = tmp[min((page-1)*count, uint32(len(tmp)-1)):min(page*count, uint32(len(tmp)))]
	}
	for _, item := range tmp {
		out = append(out, &pb.FlashEffectConfig{
			FlashId:    item.FlashId,
			FlashName:  item.FlashName,
			FlashUrl:   item.FlashUrl,
			FlashMd5:   item.FlashMd5,
			Operator:   item.Operator,
			CreateTime: uint32(item.CreatedAt.Unix()),
			UpdateTime: uint32(item.UpdatedAt.Unix()),
		})
	}

	return out, all, err
}

func (m *ExtraConfManager) UpdatePresentFlashInfo(ctx context.Context, giftId, flashId, begin, end uint32, operator string) error {
	err := m.store.UpdatePresentFlash(ctx, &store.PresentFlashInfo{
		GiftId:      giftId,
		FlashId:     flashId,
		EffectBegin: time.Unix(int64(begin), 0).Local(),
		EffectEnd:   time.Unix(int64(end), 0).Local(),
		Operator:    operator,
		UpdatedAt:   time.Now(),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "AddFlashConfig AddFlashConfig err , err: %v", err)
		return err
	}
	_ = m.store.SetLastUpdateTime(ctx, time.Now())
	return nil
}

func (m *ExtraConfManager) GetPresentFlashInfo(ctx context.Context, page, count, giftId uint32, giftName, flashName string) ([]*pb.PresentFlashInfo, uint32, uint32, error) {
	out := make([]*pb.PresentFlashInfo, 0)

	resp, err := m.store.GetAllPresentFlash(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetPresentFlashInfo GetAllPresentFlash err , err: %v", err)
		return out, 0, 0, err
	}

	giftList := make([]uint32, 0)
	flashList := make([]uint32, 0)
	for _, item := range resp {
		giftList = append(giftList, item.GiftId)
		flashList = append(flashList, item.FlashId)
	}

	presentConfig, err := rpc.UserPresentClient.GetPresentConfigByIdList(ctx, 0, giftList, 0)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetPresentFlashInfo GetPresentConfigByIdList err , err: %v", err)
		return out, 0, 0, err
	}
	presentConfigMap := make(map[uint32]*userpresent.StPresentItemConfig)
	for _, item := range presentConfig.GetItemList() {
		presentConfigMap[item.ItemId] = item
	}

	flashConfig, err := m.store.GetFlashConfigByIds(ctx, flashList)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetPresentFlashInfo GetFlashConfigByIds err , err: %v", err)
		return out, 0, 0, err
	}

	flashConfigMap := make(map[uint32]*store.FlashEffectConfig)
	for _, item := range flashConfig {
		flashConfigMap[item.FlashId] = item
	}

	tmp := make([]*store.PresentFlashInfo, 0)
	for _, item := range resp {
		if !isSearchFlash(giftName, flashName, giftId, flashConfigMap, presentConfigMap, item) {
			continue
		}
		tmp = append(tmp, item)
	}

	sort.Slice(tmp, func(i, j int) bool {
		return tmp[i].GiftId < tmp[j].GiftId
	})

	all := len(tmp)

	if count != 0 && page != 0 {
		tmp = tmp[min((page-1)*count, uint32(len(tmp)-1)):min(page*count, uint32(len(tmp)))]
	}

	nowTs := time.Now().Unix()
	for _, item := range tmp {
		status := uint32(pb.EffectStatus_EffectStatus_Effective)
		if nowTs < item.EffectBegin.Unix() {
			status = uint32(pb.EffectStatus_EffectStatus_Waiting)
		}

		if nowTs > item.EffectEnd.Unix() {
			status = uint32(pb.EffectStatus_EffectStatus_Outdated)
		}

		out = append(out, &pb.PresentFlashInfo{
			FlashInfo: &pb.FlashEffectConfig{
				FlashId:    flashConfigMap[item.FlashId].FlashId,
				FlashName:  flashConfigMap[item.FlashId].FlashName,
				FlashUrl:   flashConfigMap[item.FlashId].FlashUrl,
				FlashMd5:   flashConfigMap[item.FlashId].FlashMd5,
				Operator:   flashConfigMap[item.FlashId].Operator,
				CreateTime: uint32(flashConfigMap[item.FlashId].CreatedAt.Unix()),
				UpdateTime: uint32(flashConfigMap[item.FlashId].UpdatedAt.Unix()),
			},
			PresentConfig: &pb.PresentBaseConfig{
				GiftId:     presentConfigMap[item.GiftId].GetItemId(),
				GiftName:   presentConfigMap[item.GiftId].GetName(),
				PriceValue: presentConfigMap[item.GiftId].GetPrice(),
				PriceType:  presentConfigMap[item.GiftId].GetPriceType(),
				GiftImage:  presentConfigMap[item.GiftId].GetIconUrl(),
			},
			EffectBegin:  uint32(item.EffectBegin.Unix()),
			EffectEnd:    uint32(item.EffectEnd.Unix()),
			CreateTime:   uint32(item.CreatedAt.Unix()),
			Operator:     item.Operator,
			EffectStatus: status,
			UpdateTime:   uint32(item.UpdatedAt.Unix()),
		})
	}

	lastUpdate, _ := m.store.GetLastUpdateTime(ctx)
	return out, uint32(all), uint32(lastUpdate.Unix()), err
}

func MatchNumber(source, compare uint32) bool {
	// 都换成字符串，然后直接匹配
	sourceStr := strconv.Itoa(int(source))
	compareStr := strconv.Itoa(int(compare))
	return strings.Contains(sourceStr, compareStr)
}

func min(num1, num2 uint32) uint32 {
	if num1 > num2 {
		return num2
	} else {
		return num1
	}
}

func isUrlActivity(typ uint32) bool {
	return typ == JumpUrlTypeActivity
}

func urlType(isActivity bool) uint32 {
	if isActivity {
		return JumpUrlTypeActivity
	}
	return JumpUrlTypeNormal
}

func isSearchFloat(keyWord string, keyId, keyStatus uint32,
	presentConfigMap map[uint32]*userpresent.StPresentItemConfig, item *store.PresentFloatLayer) bool {
	if keyWord != "" {
		if !strings.Contains(presentConfigMap[item.GiftId].GetName(), keyWord) {
			return false
		}
	}
	if keyId != 0 {
		if !MatchNumber(item.GiftId, keyId) {
			return false
		}
	}

	if keyStatus != 0 {
		nowTs := time.Now().Unix()
		status := uint32(pb.EffectStatus_EffectStatus_Effective)
		if nowTs < item.EffectBegin.Unix() {
			status = uint32(pb.EffectStatus_EffectStatus_Waiting)
		}
		if nowTs > item.EffectEnd.Unix() {
			status = uint32(pb.EffectStatus_EffectStatus_Outdated)
		}
		if keyStatus != status {
			return false
		}
	}

	return true
}

func isSearchFlash(giftName, flashName string, giftId uint32, flashConfigMap map[uint32]*store.FlashEffectConfig,
	presentConfigMap map[uint32]*userpresent.StPresentItemConfig, item *store.PresentFlashInfo) bool {
	if _, ok := flashConfigMap[item.FlashId]; !ok {
		return false
	}

	if _, ok := presentConfigMap[item.GiftId]; !ok {
		return false
	}

	if giftName != "" {
		if !strings.Contains(presentConfigMap[item.GiftId].GetName(), giftName) {
			return false
		}
	}
	if giftId != 0 {
		if !MatchNumber(item.GiftId, giftId) {
			return false
		}
	}
	if flashName != "" {
		if !strings.Contains(flashConfigMap[item.FlashId].FlashName, flashName) {
			return false
		}
	}

	return true
}

func tranShowTypeToString(showType []pb.PresentFloatLayer_ChannelType) string {
	var showTypeStr string
	for _, v := range showType {
		showTypeStr += strconv.Itoa(int(v)) + ","
	}
	return showTypeStr
}

func tranStringToShowType(showTypeStr string) []pb.PresentFloatLayer_ChannelType {
	var showType []pb.PresentFloatLayer_ChannelType
	for _, v := range strings.Split(showTypeStr, ",") {
		vNum, _ := strconv.Atoi(v)
		if vNum != 0 {
			showType = append(showType, pb.PresentFloatLayer_ChannelType(vNum))
		}
	}
	return showType
}

func tranAppTypeToString(appType []pb.PresentFloatLayer_AppType) string {
	var appTypeStr string
	for _, v := range appType {
		appTypeStr += strconv.Itoa(int(v)) + ","
	}
	return appTypeStr
}

func tranStringToAppType(appTypeStr string) []pb.PresentFloatLayer_AppType {
	var appType []pb.PresentFloatLayer_AppType
	for _, v := range strings.Split(appTypeStr, ",") {
		vNum, _ := strconv.Atoi(v)
		if vNum != 0 {
			appType = append(appType, pb.PresentFloatLayer_AppType(vNum))
		}
	}
	return appType
}

func tranShowAppUrlToString(appUrl []*pb.AppUrl) string {
	var appUrlStr string
	for _, v := range appUrl {
		appUrlStr += fmt.Sprintf("%d_%s,", v.GetUrlType(), v.GetUrl())
	}
	return appUrlStr
}

func tranStringToShowAppUrl(sourceAppUrl, appUrlStr string) []*pb.AppUrl {
	appUrl := make([]*pb.AppUrl, 0)

	urlMap := make(map[pb.AppUrl_UrlType]string)
	for _, v := range strings.Split(appUrlStr, ",") {
		url := strings.Split(v, "_")
		if len(url) > 1 {
			typNum, _ := strconv.Atoi(url[0])
			urlMap[pb.AppUrl_UrlType(typNum)] = url[1]
		}
	}

	for _, typ := range []pb.AppUrl_UrlType{pb.AppUrl_URL_TYPE_TT, pb.AppUrl_URL_TYPE_HUANYOU_IOS, pb.AppUrl_URL_TYPE_HUANYOU_ANDROID, pb.AppUrl_URL_TYPE_MAIKE_IOS, pb.AppUrl_URL_TYPE_MAIKE_ANDROID} {
		url := ""
		if tmpUrl, ok := urlMap[typ]; ok {
			url = tmpUrl
		} else {
			url = sourceAppUrl
		}
		appUrl = append(appUrl, &pb.AppUrl{
			UrlType: typ,
			Url:     url,
		})
	}

	return appUrl
}
