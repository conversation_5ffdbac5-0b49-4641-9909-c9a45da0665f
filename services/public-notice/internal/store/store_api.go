package store

import (
	"context"
	public_notice "golang.52tt.com/protocol/services/public-notice"
)

type IStore interface {
	AddBreakingNewsConfig(newsCfg *BreakingNewsConfig) error
	AddBreakingNewsPriorityRecord(ctx context.Context, newId uint32, rank float32, beginTime, endTime int64) error
	BatchDelBreakingNewsConfig(newsIdsList []uint32) error
	BatchGetBreakingNewsConfig(newsId uint32, searchKeyword string) ([]*BreakingNewsConfig, error)
	DeleteBreakingNewsPriorityRecord(ctx context.Context, recordId uint32) error
	GetAllStickBreakingNews(ctx context.Context) (*public_notice.GetAllStickBreakingNewsResp, error)
	UpdateBreakingNewsConfig(newsCfg *BreakingNewsConfig) error
	UpdateBreakingNewsPriorityRecord(ctx context.Context, recordId uint32, rank float32, beginTime, endTime int64) error
	GetBreakingNewsConfig(ctx context.Context, newsId uint32) (*BreakingNewsConfig, error)
	GetStickRecordsByNewsId(ctx context.Context, newsId uint32) ([]*BreakingNewsSimpleItem, error)
	GetStickRecordByRecordId(ctx context.Context, recordId uint32) (*BreakingNewsSimpleItem, error)
	GetAvailableStickRecord(ctx context.Context, newsId uint32) (float32, error)
}
