package internal

import (
	"context"
	"fmt"
	anchorContractGo "golang.52tt.com/clients/anchorcontract-go"
	channel_follow "golang.52tt.com/clients/channel-follow"
	channelguildgo "golang.52tt.com/clients/channelguild-go"
	"golang.52tt.com/clients/channelmic"
	"golang.52tt.com/clients/entertainmentrecommendback"
	superplayer "golang.52tt.com/clients/super-player-svr"
	channel_member_rank "golang.52tt.com/protocol/services/channel-member-rank"
	channelMicPb "golang.52tt.com/protocol/services/channelmicsvr"
	entertainmentRecommendBack "golang.52tt.com/protocol/services/entertainmentRecommendBackSvr"
	"golang.52tt.com/services/channelrank-group/common"
	grpc "google.golang.org/grpc"
	"time"

	"golang.52tt.com/clients/account"
	"golang.52tt.com/clients/channel"
	channellivefans "golang.52tt.com/clients/channel-live-fans"
	channellivemgr "golang.52tt.com/clients/channel-live-mgr"
	channelliveranking "golang.52tt.com/clients/channel-live-ranking"
	channellottery "golang.52tt.com/clients/channel-lottery"
	channelmemberviprank "golang.52tt.com/clients/channelmemberVipRank"
	channelmemberviprank_go "golang.52tt.com/clients/channelmemberviprank-go"
	"golang.52tt.com/clients/guild"
	headimage "golang.52tt.com/clients/headimage"
	knightprivilege "golang.52tt.com/clients/knight-privilege"
	maskedpklive "golang.52tt.com/clients/masked-pk-live"
	maskedpksvr "golang.52tt.com/clients/masked-pk-svr"
	"golang.52tt.com/clients/nobility"
	numerigo "golang.52tt.com/clients/numeric-go"
	revenue_ext_game "golang.52tt.com/clients/revenue-ext-game"
	"golang.52tt.com/pkg/foundation/utils"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/mapreduce"
	"golang.52tt.com/pkg/protocol"
	protogrpc "golang.52tt.com/pkg/protocol/grpc"
	ga "golang.52tt.com/protocol/app/channel"
	"golang.52tt.com/protocol/common/status"
	accountPB "golang.52tt.com/protocol/services/accountsvr"
	channelfollowpb "golang.52tt.com/protocol/services/channel-follow"
	channellotterypb "golang.52tt.com/protocol/services/channel-lottery"
	channellivemgrpb "golang.52tt.com/protocol/services/channellivemgr"
	channelmemberviprankpb "golang.52tt.com/protocol/services/channelmemberviprank-go"
	channelsvrpb "golang.52tt.com/protocol/services/channelsvr"
	"golang.52tt.com/protocol/services/demo/echo"
	Guild "golang.52tt.com/protocol/services/guildsvr"
	maskedpksvrpb "golang.52tt.com/protocol/services/masked-pk-svr"
	revenuegamepb "golang.52tt.com/protocol/services/revenue-ext-game"
	conf2 "golang.52tt.com/services/channelrank-group/channel-rank-logic/internal/conf"
	"golang.52tt.com/services/channelrank-group/common/dyconf"
)

type StartConfig struct {
	// [optional] from startup arguments

	// from config file

}

type ChannelRankLogic struct {
	ctx                  context.Context
	dyconfig             dyconf.ISDyConfigHandler
	pkdyconfig           conf2.ISDyConfigHandler
	guildGroupCache      common.IConfDynamic
	newAnchorCache       *common.NewAnchorLocalCache
	channelTagLocalCache *common.ChannelTagLocalCache

	rankCli                       channelmemberviprank_go.IClient
	channelCli                    channel.IClient
	accountCli                    account.IClient
	anchorContractCli             anchorContractGo.IClient
	guildCli                      guild.IClient
	channellotteryCli             channellottery.IClient
	channellivemgrCli             channellivemgr.IClient
	channelLiveFans               channellivefans.IClient
	channelFollowCli              channel_follow.IClient
	channelMicCli                 channelmic.IClient
	channelGuildCli               channelguildgo.IClient
	entertainmentRecommendBackCli entertainmentrecommendback.IClient
	headimageCli                  headimage.IClient
	maskedpksvrCli                maskedpksvr.IClient
	maskedpkliveCli               maskedpklive.IClient
	revenueExtGameCli             revenue_ext_game.IClient
	fansCli                       channellivefans.IClient
	chLiveRankingCli              channelliveranking.IClient
	nobilityClient                nobility.IClient
	memberRankCli                 channelmemberviprank.IClient
	numericGoCli                  numerigo.IClient
	knightPriCli                  knightprivilege.IClient
	superPlayerCli                superplayer.IClient
	channelMemberRankCli          channel_member_rank.ChannelMemberRankClient
}

func NewChannelRankLogic(ctx context.Context, cfg *StartConfig) (*ChannelRankLogic, error) {
	var err error
	s := &ChannelRankLogic{
		ctx: ctx,
	}

	dyconfig := dyconf.NewConfigHandler(dyconf.DyconfigPath)
	if err := dyconfig.Start(); err != nil {
		log.Errorf("NewChannelRankLogic dyconfig.Start fail %v", err)
		return nil, err
	}
	s.dyconfig = dyconfig

	pkdyconfig := conf2.NewConfigHandler(conf2.GeneralCfgFile)
	if err := pkdyconfig.Start(); err != nil {
		log.Errorf("NewChannelRankLogic pkdyconfig.Start fail %v", err)
		return nil, err
	}
	s.pkdyconfig = pkdyconfig

	s.channelTagLocalCache, err = common.NewChannelTagCache()
	if err != nil {
		log.Errorf("NewChannelRankLogic NewChannelTagCache fail %v", err)
		return nil, err
	}

	guildGroupCache, err := common.NewSignAnchorDyConf()
	if err != nil {
		log.Errorf("NewChannelRankLogic NewSignAnchorDyConf fail %v", err)
		return nil, err
	}
	s.guildGroupCache = guildGroupCache

	s.newAnchorCache, err = common.NewNewAnchorCache()
	if err != nil {
		log.Errorf("NewChannelRankLogic NewNewAnchorCache fail %v", err)
		return nil, err
	}

	opts := []grpc.DialOption{grpc.WithBlock()}

	s.rankCli = channelmemberviprank_go.NewClient()
	s.channelCli = channel.NewClient()
	s.accountCli, _ = account.NewClient()
	s.guildCli = guild.NewClient()
	s.channellotteryCli, _ = channellottery.NewClient()
	s.channellivemgrCli = channellivemgr.NewIClient()
	s.channelLiveFans, _ = channellivefans.NewClient()
	s.channelFollowCli, _ = channel_follow.NewClient()
	s.channelMicCli = channelmic.NewClient()
	s.channelGuildCli, _ = channelguildgo.NewClient()
	s.entertainmentRecommendBackCli = entertainmentrecommendback.NewClient()
	s.headimageCli = headimage.NewClient()
	s.maskedpksvrCli, _ = maskedpksvr.NewClient()
	s.maskedpkliveCli, _ = maskedpklive.NewClient()
	s.revenueExtGameCli = revenue_ext_game.NewIClient()
	s.chLiveRankingCli = channelliveranking.NewIClient()
	s.fansCli = channellivefans.NewIClient()
	s.nobilityClient = nobility.NewIClient()
	s.memberRankCli = channelmemberviprank.NewIClient()
	s.numericGoCli = numerigo.NewIClient()
	s.knightPriCli = knightprivilege.NewIClient()
	s.anchorContractCli, _ = anchorContractGo.NewClient()
	s.superPlayerCli = superplayer.NewIClient()
	s.channelMemberRankCli = channel_member_rank.MustNewClient(ctx, opts...)

	//活动房排行榜数据加载定时器
	s.initSuperChannelMemberTimer()

	return s, nil
}

func (s *ChannelRankLogic) ShutDown() {
	log.Infof("ShutDown")
}
func (s *ChannelRankLogic) Echo(ctx context.Context, req *echo.StringMessage) (*echo.StringMessage, error) {
	log.DebugWithCtx(ctx, "Echo req:[%+v]", req)
	return req, nil
}

func (s *ChannelRankLogic) GetChannelHourRankTop1List(ctx context.Context, in *ga.GetChannelHourRankTop1ListRequest) (
	*ga.GetChannelHourRankTop1ListResponse, error) {

	si, _ := protogrpc.ServiceInfoFromContext(ctx)
	out := &ga.GetChannelHourRankTop1ListResponse{
		RankList: []*ga.ChannelHourRankTop1Info{},
	}
	hour := uint32(time.Now().Hour())

	resp, err := s.rankCli.GetChannelHourRankTop1(ctx, hour)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetChannelHourRankTop1List GetChannelHourRankTop1 fail %v", err)
		return out, err
	}

	channelIds := []uint32{}
	for _, info := range resp.GetRankList() {
		if info.GetChannelId() > 0 {
			channelIds = append(channelIds, info.GetChannelId())
		}
	}

	channelId2Info, err := s.channelCli.BatchGetChannelSimpleInfo(ctx, 0, channelIds)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetChannelHourRankTop1List BatchGetChannelSimpleInfo fail %v", err)
		return out, err
	}

	for _, info := range resp.GetRankList() {
		if info.ChannelId == 0 {
			continue
		}

		out.RankList = append(out.RankList, &ga.ChannelHourRankTop1Info{
			TagId:       info.TagId,
			TagName:     info.TagName,
			ChannelId:   info.ChannelId,
			ChannelName: channelId2Info[info.ChannelId].GetName(),
			ChannelIcon: channelId2Info[info.ChannelId].GetIconMd5(),
		})
	}

	log.DebugfWithCtx(ctx, "GetChannelHourRankTop1List hour=%d, %s, out=%s", hour, si.String(), utils.ToJson(out))
	return out, nil
}

func (s *ChannelRankLogic) ChannelHourRankTopN(ctx context.Context, in *ga.GetChannelHourRankTopNReq) (
	out *ga.GetChannelHourRankTopNResp, err error) {
	diemType := ga.MultiDimChannelRankType(in.GetMultiDimRankType())
	// 成员维度榜单，展示用户，注意直播间是按房间榜处理
	isMemberRank := common.IsMemberRank(diemType)
	isNewStarRank := diemType == ga.MultiDimChannelRankType_MULTI_DIM_CHANNEL_RANK_TYPE_NEW_STAR
	out = &ga.GetChannelHourRankTopNResp{
		RankList:            []*ga.ChannelHourRankInfo{},
		LastTopRankList:     []*ga.ChannelHourRankInfo{},
		MultiDimRankTabList: s.GetMultiDemiRankTab(in.RankType),
		IsMemberRank:        isMemberRank,
	}
	si, _ := protogrpc.ServiceInfoFromContext(ctx)
	uid := si.UserID
	if uid == 0 {
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}
	hour := uint32(time.Now().Hour())

	defer func() {
		log.InfoWithCtx(ctx, "ChannelHourRankTopN in=%s, %s, out=%s",
			utils.ToJson(in), si.String(), utils.ToJson(out))
	}()

	if !common.IsValidRank(in.GetRankType(), diemType) {
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}

	goalScore, rankViewCnt := s.dyconfig.GetGoalScoreAndViewCnt()

	// 蒙面pk期间检查
	minScore, maskedGameId, maskedLiveGameId, isInPk := s.checkMaskedPk(ctx, in.RankType)
	if isInPk {
		log.InfoWithCtx(ctx, "ChannelHourRankTopN checkMaskedPk isInPk. in=%+v, minScore=%d, maskedGameId=%d, maskedLiveGameId=%d",
			in, minScore, maskedGameId, maskedLiveGameId)
		rankViewCnt += 30 // 蒙面pk期间取多些数据，防止后续过滤后榜单数据过少
		if s.pkdyconfig.CheckMaskedPkHourRankAvoid() {
			err = protocol.NewExactServerError(nil, status.ErrChannelHourRankUnavaiable)
			log.ErrorWithCtx(ctx, "ChannelHourRankTopN checkMaskedPk fail %+v in=%+v", err, in)
			return
		}
	}

	// 检查签约成员
	var isContract, isNewStar bool
	contractResp, err := s.anchorContractCli.GetUserContractCacheInfo(ctx, 0, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "ChannelHourRankTopN GetUserContractCacheInfo fail %v", err)
		return
	}
	contractGuildId := contractResp.GetContract().GetGuildId()
	isContract = contractGuildId > 0

	// 查小时榜
	req := &channelmemberviprankpb.GetChannelHourRankListReq{
		Type:              in.RankType,
		Hour:              hour,
		Begin:             0,
		Limit:             rankViewCnt,
		Cid:               in.ChannelId,
		ViewCnt:           rankViewCnt,
		MultiDiemRankType: uint32(diemType),
	}
	if isContract {
		req.Uid = uid // 非签约成员不展示底部浮层
	}
	rankResp, err := s.rankCli.GetChannelHourRankList(ctx, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "ChannelHourRankTopN GetChannelHourRankList fail %v, in %+v", err, in)
		return
	}
	log.DebugWithCtx(ctx, "ChannelHourRankTopN GetChannelHourRankList req=%+v, resp=%s, uid:%d, contractGuildId:%d, isContract=%v",
		req, utils.ToJson(rankResp), uid, contractGuildId, isContract)

	out.GoalScore = goalScore
	out.RankViewCnt = rankResp.GetRealViewCnt()
	nowHour := time.Date(time.Now().Year(), time.Now().Month(), time.Now().Day(), time.Now().Hour(), 0, 0, 0, time.Local)
	out.NextSettleTs = uint32(nowHour.Unix()) + 3600

	rankList := rankResp.GetRankInfoList()
	rankLastTopNList := rankResp.GetLastTopRankList()
	myRank := rankResp.GetMyRankInfo()
	var hasRankTypeAccess bool
	var guildId2Info map[uint32]*Guild.GuildResp
	var uid2UserInfo map[uint32]*accountPB.UserResp
	var username2FaceMd5 map[string]string
	var channelId2Lottery map[uint32]*channellotterypb.LotterySimple
	var channelId2LiveInfo map[uint32]*channellivemgrpb.ChannelLiveStatusSimple
	var channelId2ExtGameType map[uint32]revenuegamepb.ExtGameType
	var uid2followMap map[uint32]*channelfollowpb.RoomInfo
	var channelId2InfoMap map[uint32]*channelsvrpb.ChannelSimpleInfo
	var uid2MicMap map[uint32]uint32

	if isMemberRank {
		if isContract && myRank == nil {
			// 签约用户成员榜始终展示自己
			myRank = &channelmemberviprankpb.ChannelHourRankInfo{Uid: uid}
		}
		uid2UserInfo, username2FaceMd5, uid2followMap, uid2MicMap, channelId2InfoMap, channelId2LiveInfo, hasRankTypeAccess, err = s.getMemberRankExtraInfo(ctx, rankList, rankLastTopNList, myRank, contractGuildId, in.RankType)
		if err != nil {
			log.ErrorWithCtx(ctx, "ChannelHourRankTopN getMemberRankExtraInfo fail %v", err)
			return
		}
		if in.RankType == 0 && isContract {
			// 签约用户均有权限上成员总榜
			hasRankTypeAccess = true
		}

		for _, info := range rankList {
			out.RankList = append(out.RankList, s.fillMemberRankInfo(info, uid2UserInfo, username2FaceMd5, uid2followMap, uid2MicMap, channelId2InfoMap, channelId2LiveInfo))
		}
		for _, info := range rankLastTopNList {
			out.LastTopRankList = append(out.LastTopRankList, s.fillMemberRankInfo(info, uid2UserInfo, username2FaceMd5, uid2followMap, uid2MicMap, channelId2InfoMap, channelId2LiveInfo))
		}

	} else {

		channelIds := []uint32{in.ChannelId}
		for _, info := range append(rankList, rankLastTopNList...) {
			channelIds = append(channelIds, info.GetCid())
		}
		channelId2InfoMap, err = s.channelCli.BatchGetChannelSimpleInfo(ctx, 0, channelIds)
		if err != nil {
			log.ErrorWithCtx(ctx, "ChannelHourRankTopN BatchGetChannelSimpleInfo fail %v", err)
			return
		}

		guildIds, anchorUids := parseRankResp(rankResp, channelId2InfoMap)
		guildId2Info, uid2UserInfo, username2FaceMd5, channelId2Lottery, channelId2LiveInfo, channelId2ExtGameType, err =
			s.getRankExtraInfo(ctx, guildIds, anchorUids, channelIds)
		if err != nil {
			log.ErrorWithCtx(ctx, "ChannelHourRankTopN getRankExtraInfo fail %v", err)
			return
		}

		for _, info := range append(rankList) {
			out.RankList = append(out.RankList, s.fillRankInfo(info, channelId2InfoMap, guildId2Info, uid2UserInfo, username2FaceMd5, channelId2Lottery, channelId2LiveInfo, channelId2ExtGameType))
		}
		for _, info := range rankLastTopNList {
			out.LastTopRankList = append(out.LastTopRankList, s.fillRankInfo(info, channelId2InfoMap, guildId2Info, uid2UserInfo, username2FaceMd5, channelId2Lottery, channelId2LiveInfo, channelId2ExtGameType))
		}

		// 检查新星主播
		if isNewStarRank {
			if thisChannelInfo, ok := channelId2InfoMap[in.ChannelId]; ok {
				if thisChannelInfo.GetChannelType() == uint32(ga.ChannelType_RADIO_LIVE_CHANNEL_TYPE) {
					isNewStar = s.IsNewStarAnchor(thisChannelInfo.GetBindId())
				}
			}
		}
	}

	showMyRank := myRank != nil
	if showMyRank && isNewStarRank {
		showMyRank = isNewStar
	}
	if showMyRank {
		out.MyRankInfo = fillMyRankInfo(myRank, channelId2InfoMap, uid2UserInfo, username2FaceMd5, guildId2Info)
	}
	out.IsHideMemberRankDValue = !hasRankTypeAccess // 没有房间标签权限，隐藏差值

	log.DebugWithCtx(ctx, "ChannelHourRankTopN maskedPkFilterHandle before in=%+v out=%+v", in, out)

	// 蒙面pk期间过滤逻辑
	if !s.dyconfig.IsDisableMaskPKFilter() && !isMemberRank {
		if maskedGameId > 0 || maskedLiveGameId > 0 {
			s.maskedPkFilterHandle(ctx, in.RankType, maskedGameId, maskedLiveGameId, minScore, out)
			log.InfoWithCtx(ctx, "ChannelHourRankTopN maskedPkFilterHandle RankList change to %+v", out.RankList)
		}
	}

	// 非签约成员视角隐藏分数，由于蒙面PK过滤逻辑需要用到分数，留到最后处理
	if !isContract {
		for i := range out.RankList {
			out.RankList[i].Score = 0
		}
		for i := range out.LastTopRankList {
			out.LastTopRankList[i].Score = 0
		}
	}

	log.DebugWithCtx(ctx, "ChannelHourRankTopN in:%+v out:%+v", in, out)

	return
}

func (s *ChannelRankLogic) ChannelHourRankById(ctx context.Context, in *ga.GetChannelHourRankByIdReq) (
	out *ga.GetChannelHourRankByIdResp, err error) {
	out = &ga.GetChannelHourRankByIdResp{
		RankInfo: &ga.ChannelHourRankInfo{},
	}
	si, _ := protogrpc.ServiceInfoFromContext(ctx)
	hour := uint32(time.Now().Hour())

	defer func() {
		log.InfoWithCtx(ctx, "ChannelHourRankById in=%s, %s, out=%s",
			utils.ToJson(in), si.String(), utils.ToJson(out))
	}()

	// 如果开关打开 要隐藏小时榜 才判断是否在pk
	if s.pkdyconfig.CheckMaskedPkHourRankAvoid() {
		_, _, _, isInPk := s.checkMaskedPk(ctx, in.RankType)
		if isInPk {
			err = protocol.NewExactServerError(nil, status.ErrChannelHourRankUnavaiable)
			log.ErrorWithCtx(ctx, "ChannelHourRankById checkMaskedPk fail %+v, in=%+v", err, in)
			return
		}
	}

	_, rankViewCnt := s.dyconfig.GetGoalScoreAndViewCnt()
	inInterval := s.dyconfig.GetInRankViewIntervalSec()
	if inInterval < 5 {
		inInterval = 5
	}

	rankResp, err := s.rankCli.GetHourRankById(ctx, in.ChannelId, in.RankType, hour, rankViewCnt)
	if err != nil {
		log.ErrorWithCtx(ctx, "ChannelHourRankById GetHourRankById fail %v", err)
		return
	}

	channelInfo, sErr := s.channelCli.GetChannelSimpleInfo(ctx, 0, in.ChannelId)
	if sErr != nil {
		log.ErrorWithCtx(ctx, "ChannelHourRankById GetChannelSimpleInfo fail %v", sErr)
		return
	}
	if channelInfo.GetIsDel() {
		err = protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
		log.ErrorWithCtx(ctx, "ChannelHourRankById channelid no find. in=%+v", in)
		return
	}

	out.NextReqIntervalSec = inInterval * 2
	out.RankViewCnt = rankResp.GetRealViewCnt()
	out.RankInfo.ChannelId = channelInfo.GetChannelId()
	out.RankInfo.ChannelType = channelInfo.GetChannelType()
	out.RankInfo.ChannelIcon = channelInfo.GetIconMd5()
	out.RankInfo.ChannelName = channelInfo.GetName()
	if in.RankType == 0 {
		out.RankInfo.TagId = rankResp.GetRankInfo().GetTagId()
	}
	out.RankInfo.Score = rankResp.GetRankInfo().GetScore()
	out.RankInfo.DValue = rankResp.GetRankInfo().GetDValue()
	out.RankInfo.Rank = rankResp.GetRankInfo().GetRank()

	return out, nil
}

func (s *ChannelRankLogic) maskedPkFilterHandle(ctx context.Context, rankType, maskedGameId, maskedLiveGameId, minScore uint32,
	out *ga.GetChannelHourRankTopNResp) {

	livecids, cids := parseGaRankResp(out)
	channelId2Status := map[uint32]uint32{}

	if maskedGameId > 0 {
		pkstatus, err := s.maskedpksvrCli.BatchGetChannelMaskedPKStatus(ctx, maskedGameId, cids)
		if err != nil {
			log.ErrorWithCtx(ctx, "maskedPkFilterHandle BatchGetChannelMaskedPKStatus fail %v", err)
			return
		}
		for cid, state := range pkstatus {
			channelId2Status[cid] = state
		}
		log.DebugfWithCtx(ctx, "maskedPkFilterHandle BatchGetChannelMaskedPKStatus, cid=%+v, maskedGameId=%d, pkstatus=%+v", cids, maskedGameId, pkstatus)
	}

	if maskedLiveGameId > 0 {
		livepkstatus, err := s.maskedpkliveCli.BatchGetLiveChannelMaskedPKStatus(ctx, maskedLiveGameId, livecids)
		if err != nil {
			log.ErrorWithCtx(ctx, "maskedPkFilterHandle BatchGetLiveChannelMaskedPKStatus fail %v", err)
			return
		}
		for cid, state := range livepkstatus {
			channelId2Status[cid] = state
		}
		log.DebugfWithCtx(ctx, "maskedPkFilterHandle BatchGetLiveChannelMaskedPKStatus, livecids=%+v, maskedLiveGameId=%d, pkstatus=%+v", livecids, maskedLiveGameId, livepkstatus)
	}

	log.InfoWithCtx(ctx, "maskedPkFilterHandle begin rankType=%d, maskedGameId=%d, maskedLiveGameId=%d, minScore=%d, channelId2Status=%+v",
		rankType, maskedGameId, maskedLiveGameId, minScore, channelId2Status)

	rank, myrank, maxRankCnt := uint32(0), uint32(0), uint32(10) // 只展示前十
	newRankList := make([]*ga.ChannelHourRankInfo, 0)
	for _, info := range out.GetRankList() {
		// 总榜上榜最低分数线
		if rankType == 0 && info.Score < minScore {
			continue
		}

		if channelId2Status[info.ChannelId] == uint32(maskedpksvrpb.ChannelMaskedPKStatus_NotParticipating) ||
			channelId2Status[info.ChannelId] == uint32(maskedpksvrpb.ChannelMaskedPKStatus_IsOut) {
			if rankType == 0 && maskedGameId == 0 && info.ChannelType == uint32(ga.ChannelType_GUILD_PUBLIC_FUN_CHANNEL_TYPE) {
				log.DebugWithCtx(ctx, "maskedPkFilterHandle Debug NoFilter rankType=%d, maskedGameId=%d, maskedLiveGameId=%d, channelId=%d, status=%d, channelType=%d, continue", rankType, maskedGameId, maskedLiveGameId, info.ChannelId, channelId2Status[info.ChannelId], info.ChannelType)
				// 请求总榜数据 且 未在娱乐厅蒙面pk期间 且 房间类型是娱乐厅
				// 不过滤
			} else {
				log.DebugWithCtx(ctx, "maskedPkFilterHandle Debug Filter rankType=%d, maskedGameId=%d, maskedLiveGameId=%d, channelId=%d, status=%d, channelType=%d, continue", rankType, maskedGameId, maskedLiveGameId, info.ChannelId, channelId2Status[info.ChannelId], info.ChannelType)
				// 否则过滤掉，不返回未参与和已淘汰的厅
				continue
			}
		}

		rank++
		if out.GetMyRankInfo().GetChannelId() == info.ChannelId {
			myrank = rank
		}

		newRank := copyChannelHourRankInfo(info)
		newRank.DValue = 0  // 不展示距上一名差距
		newRank.Rank = rank // 重置排名
		newRankList = append(newRankList, newRank)

		if rank >= maxRankCnt {
			break
		}
	}

	out.RankList = newRankList
	if out.GetMyRankInfo().GetChannelId() > 0 {
		out.MyRankInfo.DValue = 0
		out.MyRankInfo.IsHideDValue = true // 隐藏差值，展示礼物值分数
		out.MyRankInfo.Rank = myrank
	}
}

func (s *ChannelRankLogic) checkMaskedPk(ctx context.Context, rankType uint32) (
	minScore, maskedGameId, maskedLiveGameId uint32, isInPk bool) {

	// 不限制相亲厅(tag_id:2010) 和 CP战(tag_id:2014) 休闲玩 2017 拍拍 2019
	if rankType == 2010 || rankType == 2014 || rankType == 2017 || rankType == 2019 {
		return
	}

	// 听听榜不受蒙面PK影响 2024.06
	if common.IsLiveChannelTag(rankType) {
		return
	}

	score, liveScore := s.pkdyconfig.GetMaskedPkHourRankMinScore()

	// 先确认是娱乐厅榜单或者总榜的请求
	if score > 0 && (rankType == 0 || (rankType >= 2000 && rankType < 3000)) {
		pkresp, err := s.maskedpksvrCli.GetChannelMaskedPKCurrConf(ctx, 0)
		if err != nil {
			log.ErrorWithCtx(ctx, "checkMaskedPk GetChannelMaskedPKCurrConf fail %v", err)
			return
		}
		// 能获取到begin_ts(不为0)说明在pk
		if pkresp.GetConf().GetBeginTs() > 0 {
			maskedGameId = pkresp.GetConf().GetConfId()
			isInPk = true
			minScore = score
		}
	}

	// 看起来一样但这边是直播的判定
	// 如果在蒙面pk活动期间，不展示小时榜
	if liveScore > 0 && (rankType == 0 || rankType >= 3000) {
		livepkresp, err := s.maskedpkliveCli.GetLiveChannelMaskedPKCurrConf(ctx, 0)
		if err != nil {
			log.ErrorWithCtx(ctx, "checkMaskedPk GetLiveChannelMaskedPKCurrConf fail %v", err)
			return
		}
		// 能获取到begin_ts(不为0)说明在pk
		if livepkresp.GetConf().GetBeginTs() > 0 {
			maskedLiveGameId = livepkresp.GetConf().GetConfId()
			isInPk = true
			if liveScore > minScore {
				minScore = liveScore
			}
		}
	}

	return
}

func (s *ChannelRankLogic) getMemberRankExtraInfo(ctx context.Context,
	rankList, rankLastTopNList []*channelmemberviprankpb.ChannelHourRankInfo,
	myRank *channelmemberviprankpb.ChannelHourRankInfo,
	contractGuildId, rankType uint32,
) (
	uid2UserInfo map[uint32]*accountPB.UserResp,
	username2FaceMd5 map[string]string,
	followMap map[uint32]*channelfollowpb.RoomInfo,
	uid2MicMap map[uint32]uint32,
	channelId2InfoMap map[uint32]*channelsvrpb.ChannelSimpleInfo,
	channelId2LiveInfo map[uint32]*channellivemgrpb.ChannelLiveStatusSimple,
	hasRankTypeAccess bool,
	err error,
) {

	anchorList := make([]uint32, 0)
	for _, info := range append(rankList, rankLastTopNList...) {
		if info.GetUid() > 0 {
			anchorList = append(anchorList, info.GetUid())
		}
	}
	if myRank.GetUid() > 0 {
		anchorList = append(anchorList, myRank.GetUid())
	}

	log.DebugWithCtx(ctx, "getMemberRankExtraInfo anchorList:%+v, contractGuildId:%d, rankType:%d", anchorList, contractGuildId, rankType)

	channelId2LiveInfo = make(map[uint32]*channellivemgrpb.ChannelLiveStatusSimple)
	uid2UserInfo = map[uint32]*accountPB.UserResp{}
	username2FaceMd5 = map[string]string{}
	subCtx, cancel := context.WithTimeout(ctx, time.Millisecond*200)
	defer cancel()

	fillWiths := []func() error{
		func() error {
			// 查主播
			if len(anchorList) > 0 {
				uid2UserInfo, err = s.accountCli.GetUsersMap(ctx, anchorList)
				if err != nil {
					log.ErrorWithCtx(ctx, "getMemberRankExtraInfo GetUsersMap fail %v", err)
					return err
				}

				userNameList := make([]string, 0, len(uid2UserInfo))
				for _, info := range uid2UserInfo {
					userNameList = append(userNameList, info.GetUsername())
				}
				// 查头像md5
				username2FaceMd5, err = s.headimageCli.BatchGetHeadImageMd5(ctx, 0, userNameList)
				if err != nil {
					log.ErrorWithCtx(ctx, "getMemberRankExtraInfo BatchGetHeadImageMd5 fail %v", err)
					return err
				}
			}
			return nil
		},
		func() error {
			if len(anchorList) > 0 {
				followMap, err = s.channelFollowCli.BatchGetChannelFollowInfo(subCtx, 0, anchorList)
				if err != nil {
					log.ErrorWithCtx(ctx, "getMemberRankExtraInfo BatchGetChannelFollowInfo fail %v", err)
					return err
				}
				//  to_do 过滤房间类型
			}
			return nil
		},
		func() error {
			log.DebugWithCtx(ctx, "getMemberRankExtraInfo contractGuildId:%d, rankType:%d", contractGuildId, rankType)
			// 检查签约成员是否有上榜权限
			if contractGuildId == 0 {
				return nil
			}
			// 获取子母公会组
			guildIds, _ := s.guildGroupCache.Get().GetSubGuildIds(contractGuildId)
			if len(guildIds) == 0 {
				return nil
			}
			// 获取公会组旗下所有房间
			channelGuildRespList, sErr := s.channelGuildCli.BatGetChannelGuildList(ctx, guildIds, uint32(ga.ChannelType_GUILD_PUBLIC_FUN_CHANNEL_TYPE))
			if sErr != nil {
				log.ErrorWithCtx(ctx, "getMemberRankExtraInfo BatGetChannelGuildList fail %v", sErr)
				return nil
			}
			channelIds := make([]uint32, 0)
			for _, info := range channelGuildRespList {
				channelIds = append(channelIds, info.GetChannelIds()...)
			}
			if len(channelIds) == 0 {
				return nil
			}
			// 获取所有标签
			tagResp, err := s.entertainmentRecommendBackCli.BatchGetChannelTag(ctx, 0, &entertainmentRecommendBack.BatchGetChannelTagReq{
				ChannelIdList: channelIds,
			})
			if err != nil {
				log.ErrorWithCtx(ctx, "getMemberRankExtraInfo BatchGetChannelTag fail %v", err)
				return nil
			}

			for _, info := range tagResp.GetChannelTagList() {
				if info.GetTagId() == rankType {
					hasRankTypeAccess = true
					break
				}
			}

			log.DebugWithCtx(ctx, "getMemberRankExtraInfo contractGuildId:%d, guildIds:%+v, channelIds:%+v, tagResp:%+v, hasRankTypeAccess:%v",
				contractGuildId, guildIds, channelIds, tagResp, hasRankTypeAccess)
			return nil
		},
	}

	err = mapreduce.Finish(fillWiths...)
	if err != nil {
		log.ErrorWithCtx(ctx, "getMemberRankExtraInfo mapreduce fail err:%v", err)
		return
	}

	liveChannelIds := make([]uint32, 0)
	for _, v := range followMap {
		if v.GetRoomType() == uint32(ga.ChannelType_RADIO_LIVE_CHANNEL_TYPE) {
			liveChannelIds = append(liveChannelIds, v.GetRoomId())
		}
	}

	log.DebugWithCtx(ctx, "getMemberRankExtraInfo liveChannelIds:%+v", liveChannelIds)

	fillWiths = []func() error{
		func() error {
			// 直播状态
			liveResp, err := s.channellivemgrCli.BatchGetChannelLiveStatusSimple(ctx,
				channellivemgrpb.BatchGetChannelLiveStatusSimpleReq{ChannelList: liveChannelIds})
			if err != nil {
				log.ErrorWithCtx(ctx, "getMemberRankExtraInfo BatchGetChannelLiveStatusSimple fail %v", err)
			}
			for _, info := range liveResp.GetChannelLiveStatusList() {
				channelId2LiveInfo[info.ChannelId] = info
			}

			log.DebugWithCtx(ctx, "getMemberRankExtraInfo BatchGetChannelLiveStatusSimple channelId2LiveInfo:%+v", channelId2LiveInfo)
			return nil
		},
		func() error {
			var entChannelIds []uint32
			// 麦位
			uid2MicMap, entChannelIds = s.getMemberChannelMicInfo(ctx, rankList, followMap)
			if len(entChannelIds) > 0 {
				channelId2InfoMap, err = s.channelCli.BatchGetChannelSimpleInfo(ctx, 0, entChannelIds)
				if err != nil {
					log.ErrorWithCtx(ctx, "getMemberRankExtraInfo BatchGetChannelSimpleInfo fail %v", err)
				}
			}
			return nil
		},
	}

	err = mapreduce.Finish(fillWiths...)
	if err != nil {
		log.ErrorWithCtx(ctx, "getMemberRankExtraInfo mapreduce 2 fail err:%v", err)
		return
	}

	return
}

func (s *ChannelRankLogic) getRankExtraInfo(ctx context.Context,
	guildIds, anchorUids, channelIds []uint32) (
	guildId2Info map[uint32]*Guild.GuildResp,
	uid2UserInfo map[uint32]*accountPB.UserResp,
	username2FaceMd5 map[string]string,
	channelId2Lottery map[uint32]*channellotterypb.LotterySimple,
	channelId2LiveInfo map[uint32]*channellivemgrpb.ChannelLiveStatusSimple,
	channelId2ExtGameType map[uint32]revenuegamepb.ExtGameType,
	err error,
) {

	guildId2Info = map[uint32]*Guild.GuildResp{}
	uid2UserInfo = map[uint32]*accountPB.UserResp{}
	username2FaceMd5 = map[string]string{}
	channelId2Lottery = map[uint32]*channellotterypb.LotterySimple{}
	channelId2LiveInfo = map[uint32]*channellivemgrpb.ChannelLiveStatusSimple{}
	channelId2ExtGameType = map[uint32]revenuegamepb.ExtGameType{}

	subCtx, cancel := context.WithTimeout(ctx, time.Millisecond*200)
	defer cancel()

	fillWiths := []func() error{
		func() error {
			// 查公会
			if len(guildIds) == 0 {
				return nil
			}

			guildresp, err := s.guildCli.GetGuildBat(ctx, 0, &Guild.GetGuildBatReq{GuildIdList: guildIds})
			if err != nil {
				log.ErrorWithCtx(ctx, "ChannelHourRankTopN GetGuildBat fail %v", err)
				return err
			}
			for _, info := range guildresp.GetGuildList() {
				guildId2Info[info.GuildId] = info
			}
			return nil
		},
		func() error {
			// 查主播
			if len(anchorUids) > 0 {
				uid2UserInfo, err = s.accountCli.GetUsersMap(ctx, anchorUids)
				if err != nil {
					log.ErrorWithCtx(ctx, "ChannelHourRankTopN GetUsersMap fail %v", err)
					return err
				}

				userNameList := make([]string, 0, len(uid2UserInfo))
				for _, info := range uid2UserInfo {
					userNameList = append(userNameList, info.GetUsername())
				}
				// 查头像md5
				username2FaceMd5, err = s.headimageCli.BatchGetHeadImageMd5(ctx, 0, userNameList)
				if err != nil {
					log.ErrorWithCtx(ctx, "ChannelHourRankTopN BatchGetHeadImageMd5 fail %v", err)
					return err
				}
			}
			return nil
		},
		func() error {
			if len(anchorUids) == 0 {
				return nil
			}

			// 抽奖
			lotteryresp, err := s.channellotteryCli.BatchGetChannelLotteryId(subCtx, channelIds)
			if err != nil {
				log.ErrorWithCtx(ctx, "ChannelHourRankTopN BatchGetChannelLotteryId fail %v", err)
				//return err
			}
			if len(lotteryresp.GetLotterySimpleMap()) > 0 {
				channelId2Lottery = lotteryresp.GetLotterySimpleMap()
				log.DebugWithCtx(ctx, "getRankExtraInfo BatchGetChannelLotteryId %+v", channelId2Lottery)
			}
			return nil
		},
		func() error {
			// 直播状态
			liveresp, err := s.channellivemgrCli.BatchGetChannelLiveStatusSimple(ctx,
				channellivemgrpb.BatchGetChannelLiveStatusSimpleReq{ChannelList: channelIds})
			if err != nil {
				log.ErrorWithCtx(ctx, "ChannelHourRankTopN BatchGetChannelLiveStatusSimple fail %v", err)
				return err
			}
			for _, info := range liveresp.GetChannelLiveStatusList() {
				channelId2LiveInfo[info.ChannelId] = info
			}
			return nil
		},
		func() error {
			if len(anchorUids) == 0 {
				return nil
			}

			extgameResp, err := s.revenueExtGameCli.BatchGetMountExtGame(subCtx, &revenuegamepb.BatchGetMountExtGameReq{ChannelIdList: channelIds})
			if err != nil {
				log.ErrorWithCtx(ctx, "ChannelHourRankTopN BatchGetMountExtGame fail %v", err)
				//return err
			}
			for cid, gametype := range extgameResp.GetCidToGame() {
				channelId2ExtGameType[cid] = revenuegamepb.ExtGameType(gametype)
			}
			log.DebugWithCtx(ctx, "ChannelHourRankTopN BatchGetMountExtGame channelIds=%v extgameResp=%+v", channelIds, extgameResp)
			return nil
		},
	}

	err = mapreduce.Finish(fillWiths...)
	if err != nil {
		log.ErrorWithCtx(ctx, "ChannelHourRankTopN mapreduce fail err:%v", err)
		return
	}
	return
}

func parseGaRankResp(out *ga.GetChannelHourRankTopNResp) (liveCids, noLiveCids []uint32) {
	for _, info := range out.GetRankList() {
		fmt.Println("parseGaRankResp info:", info)
		if info.ChannelType == uint32(ga.ChannelType_RADIO_LIVE_CHANNEL_TYPE) {
			liveCids = append(liveCids, info.ChannelId)
		} else {
			noLiveCids = append(noLiveCids, info.ChannelId)
		}
	}
	return
}

func parseRankResp(rankResp *channelmemberviprankpb.GetChannelHourRankListResp,
	channelId2Info map[uint32]*channelsvrpb.ChannelSimpleInfo) (
	guildIds, anchorUids []uint32,
) {

	for _, info := range append(rankResp.GetRankInfoList(), rankResp.GetLastTopRankList()...) {
		channelInfo := channelId2Info[info.Cid]

		if channelInfo.GetChannelType() == uint32(ga.ChannelType_GUILD_PUBLIC_FUN_CHANNEL_TYPE) {
			guildIds = append(guildIds, channelInfo.GetBindId())
		}
		if channelInfo.GetChannelType() == uint32(ga.ChannelType_RADIO_LIVE_CHANNEL_TYPE) {
			anchorUids = append(anchorUids, channelInfo.GetBindId())
		}
	}

	if rankResp.GetMyRankInfo().GetCid() > 0 {
		myChannelInfo := channelId2Info[rankResp.GetMyRankInfo().GetCid()]
		if myChannelInfo.GetChannelType() == uint32(ga.ChannelType_GUILD_PUBLIC_FUN_CHANNEL_TYPE) {
			guildIds = append(guildIds, myChannelInfo.GetBindId())
		}
		if myChannelInfo.GetChannelType() == uint32(ga.ChannelType_RADIO_LIVE_CHANNEL_TYPE) {
			anchorUids = append(anchorUids, myChannelInfo.GetBindId())
		}
	}
	return
}

func fillMyRankInfo(myRankInfo *channelmemberviprankpb.ChannelHourRankInfo,
	channelId2Info map[uint32]*channelsvrpb.ChannelSimpleInfo,
	uid2UserInfo map[uint32]*accountPB.UserResp,
	username2FaceMd5 map[string]string,
	guildId2Info map[uint32]*Guild.GuildResp) *ga.ChannelHourRankInfo {

	uid := myRankInfo.GetUid()
	channelInfo := channelId2Info[myRankInfo.GetCid()]

	log.Debugf("fillMyRankInfo uid=%d, channelInfo=%+v, myRankInfo=%+v", uid, channelInfo, myRankInfo)

	gaMyRankInfo := &ga.ChannelHourRankInfo{
		ChannelId:   channelInfo.GetChannelId(),
		ChannelType: channelInfo.GetChannelType(),
		ChannelIcon: channelInfo.GetIconMd5(),
		ChannelName: channelInfo.GetName(),
		TagId:       myRankInfo.GetTagId(),
		Score:       myRankInfo.GetScore(),
		DValue:      myRankInfo.GetDValue(),
		Rank:        myRankInfo.GetRank(),
		AwardScore: &ga.ChannelHourRankAwardScore{
			Score: myRankInfo.GetAwardScore(),
		},
	}
	// 注 直播房房间榜、新星榜，及娱乐房的成员榜都需要填充用户信息
	if channelInfo.GetChannelType() == uint32(ga.ChannelType_GUILD_PUBLIC_FUN_CHANNEL_TYPE) || uid > 0 {
		uInfo := uid2UserInfo[uid]
		gaMyRankInfo.GuildId = channelInfo.GetBindId()
		gaMyRankInfo.GuildName = guildId2Info[channelInfo.GetBindId()].GetName()
		gaMyRankInfo.ActorUid = uid
		gaMyRankInfo.ActorAccount = uInfo.GetUsername()
		gaMyRankInfo.ActorNickName = uInfo.GetNickname()
		gaMyRankInfo.ActorFaceMd5 = username2FaceMd5[uInfo.GetUsername()]
	}
	if channelInfo.GetChannelType() == uint32(ga.ChannelType_RADIO_LIVE_CHANNEL_TYPE) {
		anchorUid := channelInfo.GetBindId()
		gaMyRankInfo.ActorUid = anchorUid
		gaMyRankInfo.ActorAccount = uid2UserInfo[anchorUid].GetUsername()
		gaMyRankInfo.ActorNickName = uid2UserInfo[anchorUid].GetNickname()
		gaMyRankInfo.ActorFaceMd5 = username2FaceMd5[uid2UserInfo[anchorUid].GetUsername()]
	}
	return gaMyRankInfo
}

func (s *ChannelRankLogic) fillRankInfo(info *channelmemberviprankpb.ChannelHourRankInfo,
	channelId2Info map[uint32]*channelsvrpb.ChannelSimpleInfo,
	guildId2Info map[uint32]*Guild.GuildResp,
	uid2UserInfo map[uint32]*accountPB.UserResp,
	username2FaceMd5 map[string]string,
	channelId2Lottery map[uint32]*channellotterypb.LotterySimple,
	channelId2LiveInfo map[uint32]*channellivemgrpb.ChannelLiveStatusSimple,
	channelId2ExtGameType map[uint32]revenuegamepb.ExtGameType,
) *ga.ChannelHourRankInfo {

	channelInfo := channelId2Info[info.Cid]
	rankInfo := &ga.ChannelHourRankInfo{
		ChannelId:   info.Cid,
		ChannelType: channelInfo.GetChannelType(),
		ChannelIcon: channelInfo.GetIconMd5(),
		ChannelName: channelInfo.GetName(),
		Rank:        info.Rank,
		TagId:       info.TagId,
		AwardScore: &ga.ChannelHourRankAwardScore{
			Score: info.GetAwardScore(),
		},
		Score: info.GetScore(),
	}

	if channelInfo.GetChannelType() == uint32(ga.ChannelType_GUILD_PUBLIC_FUN_CHANNEL_TYPE) {
		rankInfo.GuildId = channelInfo.GetBindId()
		rankInfo.GuildName = guildId2Info[channelInfo.GetBindId()].GetName()
	}
	if channelInfo.GetChannelType() == uint32(ga.ChannelType_RADIO_LIVE_CHANNEL_TYPE) {
		rankInfo.ActorUid = channelInfo.GetBindId()
		anchorUserInfo := uid2UserInfo[rankInfo.ActorUid]
		rankInfo.ActorAccount = anchorUserInfo.GetUsername()
		rankInfo.ActorNickName = anchorUserInfo.GetNickname()
		rankInfo.ActorFaceMd5 = username2FaceMd5[anchorUserInfo.GetUsername()]

		if liveInfo, ok := channelId2LiveInfo[info.Cid]; ok && liveInfo.GetStatus() != uint32(channellivemgrpb.EnumChannelLiveStatus_CLOSE) {
			rankInfo.IsLiving = true
			if liveInfo.GetPkChannelId() > 0 {
				rankInfo.LivingStatus = uint32(ga.ChannelHourRankInfo_InPk)
			}
		}
	}

	if lotteryInfo, ok := channelId2Lottery[info.Cid]; ok && lotteryInfo.GetLotteryId() > 0 && lotteryInfo.GetGiftName() != "" {
		rankInfo.LotteryCert = "天选之人抽好礼"
	}

	if extGameType := channelId2ExtGameType[info.Cid]; extGameType > 0 {
		certUrl := s.pkdyconfig.GetExtGameCertUrl(uint32(extGameType))
		if certUrl == "" {
			log.Errorf("no find extGameCertUrl. extGameType=%v, cid=%d", extGameType, info.Cid)
		} else {
			rankInfo.GameCertUrl = certUrl
		}
		log.Debugf("cid=%d extGameType=%v GetExtGameCertUrl=%s", info.Cid, extGameType, certUrl)
	}
	return rankInfo
}

// 填充成员榜单信息
func (s *ChannelRankLogic) fillMemberRankInfo(info *channelmemberviprankpb.ChannelHourRankInfo,
	uid2UserInfo map[uint32]*accountPB.UserResp,
	username2FaceMd5 map[string]string,
	followMap map[uint32]*channelfollowpb.RoomInfo,
	uid2MicMap map[uint32]uint32,
	channelId2InfoMap map[uint32]*channelsvrpb.ChannelSimpleInfo,
	channelId2LiveInfo map[uint32]*channellivemgrpb.ChannelLiveStatusSimple,
) *ga.ChannelHourRankInfo {
	uid := info.GetUid()
	anchorUserInfo := uid2UserInfo[uid]
	followInfo := followMap[uid]
	cid := followInfo.GetRoomId()

	rankInfo := &ga.ChannelHourRankInfo{
		ActorUid:      uid,
		Rank:          info.Rank,
		TagId:         info.TagId,
		ActorAccount:  anchorUserInfo.GetUsername(),
		ActorNickName: anchorUserInfo.GetNickname(),
		ActorFaceMd5:  username2FaceMd5[anchorUserInfo.GetUsername()],
		ChannelId:     cid,
		ChannelType:   followInfo.GetRoomType(),
		ChannelName:   channelId2InfoMap[cid].GetName(),
		MicId:         uid2MicMap[uid],
		AwardScore: &ga.ChannelHourRankAwardScore{
			Score: info.GetAwardScore(),
		},
		Score: info.GetScore(),
	}

	// 仅在自己的房间展示开播状态
	if liveInfo, ok := channelId2LiveInfo[cid]; ok &&
		liveInfo.GetAnchorUid() == uid &&
		liveInfo.GetStatus() != uint32(channellivemgrpb.EnumChannelLiveStatus_CLOSE) {
		rankInfo.IsLiving = true
		if liveInfo.GetPkChannelId() > 0 {
			rankInfo.LivingStatus = uint32(ga.ChannelHourRankInfo_InPk)
		}
	}

	return rankInfo
}

// deep copy
func copyChannelHourRankInfo(in *ga.ChannelHourRankInfo) *ga.ChannelHourRankInfo {
	return &ga.ChannelHourRankInfo{
		ChannelId:     in.ChannelId,
		ChannelType:   in.ChannelType,
		ChannelName:   in.ChannelName,
		Score:         in.Score,
		Rank:          in.Rank,
		ChannelIcon:   in.ChannelIcon,
		GuildId:       in.GuildId,
		GuildName:     in.GuildName,
		TagId:         in.TagId,
		DValue:        in.DValue,
		IsLiving:      in.IsLiving,
		ActorNickName: in.ActorNickName,
		ActorAccount:  in.ActorAccount,
		ActorUid:      in.ActorUid,
		ActorFaceMd5:  in.ActorFaceMd5,
		LivingStatus:  in.LivingStatus,
		IsHideDValue:  in.IsHideDValue,
		LotteryCert:   in.LotteryCert,
		AwardScore: &ga.ChannelHourRankAwardScore{
			Score: in.GetAwardScore().GetScore(),
		},
	}
}

// GetMultiDemiRankTab 获取多维度榜单列表
func (s *ChannelRankLogic) GetMultiDemiRankTab(rankType uint32) []*ga.MultiDimChannelRankTab {
	// 部分品类房间仅展示默认榜
	if !s.dyconfig.IsMemRankSupportChannelTag(rankType) {
		log.Debugf("GetMultiDemiRankTab ingore, IsMemRankSupportChannelTag rankType:%d", rankType)
		return make([]*ga.MultiDimChannelRankTab, 0)
	}
	memberRankName := "成员"
	memberRankType := ga.MultiDimChannelRankType_MULTI_DIM_CHANNEL_RANK_TYPE_MEMBER
	if common.IsLiveChannelTag(rankType) {
		// 禁用 语音房-新星榜
		if s.dyconfig.IsDisableNewStarRank() {
			log.Debugf("GetMultiDemiRankTab disable new star rankType:%d", rankType)
			return make([]*ga.MultiDimChannelRankTab, 0)
		}
		memberRankName = "新星"
		memberRankType = ga.MultiDimChannelRankType_MULTI_DIM_CHANNEL_RANK_TYPE_NEW_STAR
	}
	tabList := []*ga.MultiDimChannelRankTab{
		{
			// 默认
			Name: s.channelTagLocalCache.GetChannelHourRankDefaultDiemRankName(rankType),
		},
		{
			Type: uint32(memberRankType),
			Name: memberRankName,
		},
	}
	return tabList
}

func (s *ChannelRankLogic) getMemberChannelMicInfo(ctx context.Context,
	rankList []*channelmemberviprankpb.ChannelHourRankInfo,
	followMap map[uint32]*channelfollowpb.RoomInfo,
) (
	uid2MicMap map[uint32]uint32,
	entChannelIds []uint32,
) {
	uid2MicMap = make(map[uint32]uint32)
	entChannelIds = make([]uint32, 0)
	uid2EntChannelMap := make(map[uint32]uint32)
	for _, info := range rankList {
		fc := followMap[info.GetUid()]
		if fc.GetRoomId() > 0 && fc.GetRoomType() == uint32(ga.ChannelType_GUILD_PUBLIC_FUN_CHANNEL_TYPE) {
			uid2EntChannelMap[info.GetUid()] = fc.GetRoomId()
			entChannelIds = append(entChannelIds, fc.GetRoomId())
		}
	}

	log.DebugWithCtx(ctx, "getMemberChannelMicInfo uid2EntChannelMap:%+v, entChannelIds:%+v", uid2EntChannelMap, entChannelIds)

	if len(entChannelIds) == 0 {
		return
	}

	micResp, err := s.channelMicCli.BatGetMicrList(ctx, 0, &channelMicPb.BatGetMicrListReq{
		ChannelIdList: entChannelIds,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "getMemberChannelMicInfo BatGetMicrList fail %v", err)
		return
	}
	for _, channelMic := range micResp.GetMicDataList() {
		for _, mic := range channelMic.GetAllMicList() {
			// 仅成员当前在签约公会的娱乐房时展示麦序
			if _, ok := uid2EntChannelMap[mic.GetMicUid()]; ok {
				uid2MicMap[mic.GetMicUid()] = mic.GetMicId()
			}
		}
	}

	log.DebugWithCtx(ctx, "getMemberChannelMicInfo uid2MicMap:%+v", uid2MicMap)
	return
}

func (s *ChannelRankLogic) IsNewStarAnchor(uid uint32) bool {
	if s.dyconfig.IsNewStarWhiteList(uid) {
		return true
	}
	return s.newAnchorCache.IsNewAnchor(uid)
}
