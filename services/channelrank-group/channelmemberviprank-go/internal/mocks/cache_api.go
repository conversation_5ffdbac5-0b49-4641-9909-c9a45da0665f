// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/services/channelrank-group/channelmemberviprank-go/internal/cache (interfaces: ICache)

// Package mocks is a generated GoMock package.
package mocks

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	channelmemberviprank_go "golang.52tt.com/protocol/services/channelmemberviprank-go"
	cache "golang.52tt.com/services/channelrank-group/channelmemberviprank-go/internal/cache"
)

// MockICache is a mock of ICache interface.
type MockICache struct {
	ctrl     *gomock.Controller
	recorder *MockICacheMockRecorder
}

// MockICacheMockRecorder is the mock recorder for MockICache.
type MockICacheMockRecorder struct {
	mock *MockICache
}

// NewMockICache creates a new mock instance.
func NewMockICache(ctrl *gomock.Controller) *MockICache {
	mock := &MockICache{ctrl: ctrl}
	mock.recorder = &MockICacheMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockICache) EXPECT() *MockICacheMockRecorder {
	return m.recorder
}

// AddHourRankScore mocks base method.
func (m *MockICache) AddHourRankScore(arg0, arg1, arg2, arg3 uint32, arg4 uint64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddHourRankScore", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddHourRankScore indicates an expected call of AddHourRankScore.
func (mr *MockICacheMockRecorder) AddHourRankScore(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddHourRankScore", reflect.TypeOf((*MockICache)(nil).AddHourRankScore), arg0, arg1, arg2, arg3, arg4)
}

// BatchGetHourRankTop1 mocks base method.
func (m *MockICache) BatchGetHourRankTop1(arg0 uint32, arg1 []uint32) ([]*channelmemberviprank_go.GetChannelHourRankTop1Info, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetHourRankTop1", arg0, arg1)
	ret0, _ := ret[0].([]*channelmemberviprank_go.GetChannelHourRankTop1Info)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetHourRankTop1 indicates an expected call of BatchGetHourRankTop1.
func (mr *MockICacheMockRecorder) BatchGetHourRankTop1(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetHourRankTop1", reflect.TypeOf((*MockICache)(nil).BatchGetHourRankTop1), arg0, arg1)
}

// CheckHourRankPushFlag mocks base method.
func (m *MockICache) CheckHourRankPushFlag(arg0 uint32) bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckHourRankPushFlag", arg0)
	ret0, _ := ret[0].(bool)
	return ret0
}

// CheckHourRankPushFlag indicates an expected call of CheckHourRankPushFlag.
func (mr *MockICacheMockRecorder) CheckHourRankPushFlag(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckHourRankPushFlag", reflect.TypeOf((*MockICache)(nil).CheckHourRankPushFlag), arg0)
}

// CheckHourRankSettleFlag mocks base method.
func (m *MockICache) CheckHourRankSettleFlag(arg0 uint32) bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckHourRankSettleFlag", arg0)
	ret0, _ := ret[0].(bool)
	return ret0
}

// CheckHourRankSettleFlag indicates an expected call of CheckHourRankSettleFlag.
func (mr *MockICacheMockRecorder) CheckHourRankSettleFlag(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckHourRankSettleFlag", reflect.TypeOf((*MockICache)(nil).CheckHourRankSettleFlag), arg0)
}

// DelHourRankScore mocks base method.
func (m *MockICache) DelHourRankScore(arg0, arg1 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelHourRankScore", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// DelHourRankScore indicates an expected call of DelHourRankScore.
func (mr *MockICacheMockRecorder) DelHourRankScore(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelHourRankScore", reflect.TypeOf((*MockICache)(nil).DelHourRankScore), arg0, arg1)
}

// GetAllTagId mocks base method.
func (m *MockICache) GetAllTagId() []uint32 {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllTagId")
	ret0, _ := ret[0].([]uint32)
	return ret0
}

// GetAllTagId indicates an expected call of GetAllTagId.
func (mr *MockICacheMockRecorder) GetAllTagId() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllTagId", reflect.TypeOf((*MockICache)(nil).GetAllTagId))
}

// GetChannelHourMemberRankList mocks base method.
func (m *MockICache) GetChannelHourMemberRankList(arg0, arg1, arg2, arg3 uint32) ([]cache.IChannelHourInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChannelHourMemberRankList", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].([]cache.IChannelHourInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChannelHourMemberRankList indicates an expected call of GetChannelHourMemberRankList.
func (mr *MockICacheMockRecorder) GetChannelHourMemberRankList(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChannelHourMemberRankList", reflect.TypeOf((*MockICache)(nil).GetChannelHourMemberRankList), arg0, arg1, arg2, arg3)
}

// GetChannelHourMemberRankTopN mocks base method.
func (m *MockICache) GetChannelHourMemberRankTopN(arg0, arg1 uint32, arg2 int) ([]cache.IChannelHourInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChannelHourMemberRankTopN", arg0, arg1, arg2)
	ret0, _ := ret[0].([]cache.IChannelHourInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChannelHourMemberRankTopN indicates an expected call of GetChannelHourMemberRankTopN.
func (mr *MockICacheMockRecorder) GetChannelHourMemberRankTopN(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChannelHourMemberRankTopN", reflect.TypeOf((*MockICache)(nil).GetChannelHourMemberRankTopN), arg0, arg1, arg2)
}

// GetChannelHourRankById mocks base method.
func (m *MockICache) GetChannelHourRankById(arg0, arg1, arg2 uint32) (uint32, uint32, uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChannelHourRankById", arg0, arg1, arg2)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(uint32)
	ret2, _ := ret[2].(uint32)
	ret3, _ := ret[3].(error)
	return ret0, ret1, ret2, ret3
}

// GetChannelHourRankById indicates an expected call of GetChannelHourRankById.
func (mr *MockICacheMockRecorder) GetChannelHourRankById(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChannelHourRankById", reflect.TypeOf((*MockICache)(nil).GetChannelHourRankById), arg0, arg1, arg2)
}

// GetChannelHourRankList mocks base method.
func (m *MockICache) GetChannelHourRankList(arg0, arg1, arg2, arg3 uint32) ([]cache.IChannelHourInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChannelHourRankList", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].([]cache.IChannelHourInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChannelHourRankList indicates an expected call of GetChannelHourRankList.
func (mr *MockICacheMockRecorder) GetChannelHourRankList(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChannelHourRankList", reflect.TypeOf((*MockICache)(nil).GetChannelHourRankList), arg0, arg1, arg2, arg3)
}

// GetChannelHourRankTopN mocks base method.
func (m *MockICache) GetChannelHourRankTopN(arg0, arg1 uint32, arg2 int) ([]cache.IChannelHourInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChannelHourRankTopN", arg0, arg1, arg2)
	ret0, _ := ret[0].([]cache.IChannelHourInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChannelHourRankTopN indicates an expected call of GetChannelHourRankTopN.
func (mr *MockICacheMockRecorder) GetChannelHourRankTopN(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChannelHourRankTopN", reflect.TypeOf((*MockICache)(nil).GetChannelHourRankTopN), arg0, arg1, arg2)
}

// GetChannelHourScoreByRank mocks base method.
func (m *MockICache) GetChannelHourScoreByRank(arg0, arg1, arg2, arg3 uint32) (uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChannelHourScoreByRank", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChannelHourScoreByRank indicates an expected call of GetChannelHourScoreByRank.
func (mr *MockICacheMockRecorder) GetChannelHourScoreByRank(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChannelHourScoreByRank", reflect.TypeOf((*MockICache)(nil).GetChannelHourScoreByRank), arg0, arg1, arg2, arg3)
}

// GetChannelRankTop1 mocks base method.
func (m *MockICache) GetChannelRankTop1() *channelmemberviprank_go.GetChannelHourRankTop1Resp {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChannelRankTop1")
	ret0, _ := ret[0].(*channelmemberviprank_go.GetChannelHourRankTop1Resp)
	return ret0
}

// GetChannelRankTop1 indicates an expected call of GetChannelRankTop1.
func (mr *MockICacheMockRecorder) GetChannelRankTop1() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChannelRankTop1", reflect.TypeOf((*MockICache)(nil).GetChannelRankTop1))
}

// GetTagIdByCid mocks base method.
func (m *MockICache) GetTagIdByCid(arg0 uint32) uint32 {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTagIdByCid", arg0)
	ret0, _ := ret[0].(uint32)
	return ret0
}

// GetTagIdByCid indicates an expected call of GetTagIdByCid.
func (mr *MockICacheMockRecorder) GetTagIdByCid(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTagIdByCid", reflect.TypeOf((*MockICache)(nil).GetTagIdByCid), arg0)
}

// ResetHourRank mocks base method.
func (m *MockICache) ResetHourRank(arg0 uint32, arg1 map[uint32][]*cache.ChannelHourInfo) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ResetHourRank", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// ResetHourRank indicates an expected call of ResetHourRank.
func (mr *MockICacheMockRecorder) ResetHourRank(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ResetHourRank", reflect.TypeOf((*MockICache)(nil).ResetHourRank), arg0, arg1)
}

// ResetHourRankScore mocks base method.
func (m *MockICache) ResetHourRankScore(arg0, arg1, arg2 uint32, arg3 uint64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ResetHourRankScore", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// ResetHourRankScore indicates an expected call of ResetHourRankScore.
func (mr *MockICacheMockRecorder) ResetHourRankScore(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ResetHourRankScore", reflect.TypeOf((*MockICache)(nil).ResetHourRankScore), arg0, arg1, arg2, arg3)
}

// UpdateChannelRankTop1 mocks base method.
func (m *MockICache) UpdateChannelRankTop1(arg0 uint32, arg1 []*channelmemberviprank_go.GetChannelHourRankTop1Info) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "UpdateChannelRankTop1", arg0, arg1)
}

// UpdateChannelRankTop1 indicates an expected call of UpdateChannelRankTop1.
func (mr *MockICacheMockRecorder) UpdateChannelRankTop1(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateChannelRankTop1", reflect.TypeOf((*MockICache)(nil).UpdateChannelRankTop1), arg0, arg1)
}

// UpdateTagId mocks base method.
func (m *MockICache) UpdateTagId(arg0 map[uint32]uint32, arg1 map[uint32]struct{}) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "UpdateTagId", arg0, arg1)
}

// UpdateTagId indicates an expected call of UpdateTagId.
func (mr *MockICacheMockRecorder) UpdateTagId(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateTagId", reflect.TypeOf((*MockICache)(nil).UpdateTagId), arg0, arg1)
}
