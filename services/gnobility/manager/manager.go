package manager

import (
	"context"
	"encoding/binary"
	"errors"
	"fmt"
	"math"
	"strconv"
	"strings"
	"time"

	youknowwho "golang.52tt.com/clients/you-know-who"
	ga "golang.52tt.com/protocol/app"

	"github.com/Shopify/sarama"
	"github.com/go-redis/redis"
	"gitlab.ttyuyin.com/avengers/tyr/compatible/proto"
	"golang.52tt.com/clients/account"
	"golang.52tt.com/clients/channel"
	"golang.52tt.com/clients/channelol"
	HeadImage "golang.52tt.com/clients/headimage"
	"golang.52tt.com/clients/nobility"
	publicnotice "golang.52tt.com/clients/public-notice"
	pushNotification "golang.52tt.com/clients/push-notification/v2"
	pushclient "golang.52tt.com/clients/push-notification/v2"
	"golang.52tt.com/clients/seqgen/v2"
	user_decoration "golang.52tt.com/clients/user-decoration"
	presentclient "golang.52tt.com/clients/userpresent"
	"golang.52tt.com/pkg/coroutine"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	channelPB "golang.52tt.com/protocol/app/channel"
	pushPb "golang.52tt.com/protocol/app/push"
	"golang.52tt.com/protocol/app/sync"
	"golang.52tt.com/protocol/services/minToolkit/kafka/pb/kafkanobility"
	"golang.52tt.com/protocol/services/minToolkit/kafka/pb/kafkapresent"
	publicNoticePb "golang.52tt.com/protocol/services/public-notice"
	pushPB "golang.52tt.com/protocol/services/push-notification/v2"
	userdecpb "golang.52tt.com/protocol/services/user-decoration"
	"golang.52tt.com/services/gnobility/conf"
	"golang.52tt.com/services/gnobility/event"
	"golang.52tt.com/services/gnobility/mysql"

	apicenter "golang.52tt.com/clients/apicenter/apiserver"
	reconcilePresent "golang.52tt.com/clients/reconcile-v2-svr/reconcile-present"
	tracing "golang.52tt.com/pkg/tracing/jaeger"
	pb "golang.52tt.com/protocol/services/gnobility"
	"golang.52tt.com/services/gnobility/cache"
)

const (
	nobilityLevelFlag            = "nobility_level_flag"
	timeOutIdxKey, timeOutOffKey = "gnobility_timeout_idx", "gnobility_timeout_off"
	limit                        = 100
	extentValLockKey             = "nobility_handlerExtentVal_lock"
	extentValListKey             = "nobility_extent_val_list"

	godLevelTimeOutLockKey = "nobility_godleveltimeout_lock"
	godLevelTimeOutListKey = "nobility_godleveltimeout_list"
)

var mapPayChannel = map[string]bool{
	"WCBRAND":  true,
	"APPSTORE": true,
	"ALIPAY":   true,
	"WECHAT":   true,
	"ALITOP":   true,
}
var SwitchKeyList = []pb.NobilitySwitch{pb.NobilitySwitch_E_ALL,
	pb.NobilitySwitch_E_FANS,
	pb.NobilitySwitch_E_FOLLOW_7DAY,
	pb.NobilitySwitch_E_FOLLOW_30DAY,
	pb.NobilitySwitch_E_ONLY_FOLLOWING}

type Manager struct {
	batchCacheClient []*redis.Client       //会员批量接口 跟 c++共用
	cache            *cache.GNobilityCache //go服务单独接口
	//c_cache          *cache.GNobilityCache //go c++ 单独使用
	db         *mysql.Store
	rechargeDb *mysql.Store
	slaveDb    *mysql.Store

	consumerTbeanSub *event.KafkaConsumerTbeanSub //T豆消费
	presentSub       *event.KafkaPresentSubscriber
	nobelSubV1       *event.KafkaNobilityEventSubscriber //C++等级变化
	nobelProdV2      *event.KafkaProduce
	eSportTradeSub   *event.ESortTradeSub
	channelKfkSub    *event.ChannelKafkaSub

	apiClient    *apicenter.Client
	seqgenClient *seqgen.Client
	pushCli      *pushclient.Client

	channelCli      *channel.Client
	headImageCli    *HeadImage.Client
	accountCli      *account.Client
	presentCli      *presentclient.Client
	channelolCli    *channelol.Client
	userdeCli       *user_decoration.Client
	publicNoticeCli *publicnotice.Client
	ykwCli          *youknowwho.Client
	nobilityCli     nobility.IClient
	reconcilePreCli reconcilePresent.IClient
}

func NewManager(sc *conf.ServiceConfigT) (*Manager, error) {

	ctx := context.Background()

	redisClient := redis.NewClient(&redis.Options{
		Network:            sc.GetRedisConfig().Protocol,
		Addr:               sc.GetRedisConfig().Addr(),
		PoolSize:           sc.GetRedisConfig().PoolSize,
		IdleCheckFrequency: sc.GetRedisConfig().IdleCheckFrequency(),
		DB:                 sc.GetRedisConfig().DB,
	})

	var batchCacheClient = []*redis.Client{}
	redisTracer := tracing.Init("gnobilitysvr_redis")
	for _, r := range sc.GetBatchRedisConfig() {
		rClient := redis.NewClient(&redis.Options{
			Network:            r.Protocol,
			Addr:               r.Addr(),
			PoolSize:           r.PoolSize,
			IdleCheckFrequency: r.IdleCheckFrequency(),
			DB:                 r.DB,
		})
		batchCacheClient = append(batchCacheClient, rClient)
	}

	cacheClient := cache.NewGNobilityCache(redisClient, redisTracer)

	mysqlStore, err := mysql.NewMysql(sc.GetMysqlConnectionString())
	if err != nil {
		log.ErrorWithCtx(ctx, "NewMysql err:%v", err)
		return nil, err
	}

	sMysqlStore, err := mysql.NewMysql(sc.GetSlaveMysqlConnectionString())
	if nil != err {
		log.ErrorWithCtx(ctx, "NewMysql slave %v", err)
		return nil, err
	}

	rechargeMysqlStore, err := mysql.NewMysql(sc.GetRechargeMysqlConnectionString())
	if nil != err {
		log.ErrorWithCtx(ctx, "NewMysql err:%v", err)
		return nil, err
	}

	mgr := &Manager{
		batchCacheClient: batchCacheClient,
		cache:            cacheClient,
		db:               mysqlStore,
		slaveDb:          sMysqlStore,
		rechargeDb:       rechargeMysqlStore,
	}

	nobilitySubv1, err := event.NewKafkaNobilityEventSubscriber(ctx, sc.GetNobelInfoChangeConfigV1().ClientID, sc.GetNobelInfoChangeConfigV1().GroupID,
		sc.GetNobelInfoChangeConfigV1().TopicList(), sc.GetNobelInfoChangeConfigV1().BrokerList(), mgr)
	if nil != err {
		log.ErrorWithCtx(ctx, "NewKafkaNobilityEventSubscriber err:%v", err)
		return nil, err
	}

	err = nobilitySubv1.Start()
	if nil != err {
		log.ErrorWithCtx(ctx, "nobilitySub start err:%v", err)
		return nil, err
	}

	consumerSub, err := event.NewConsumeEventKafkaSubscriber(ctx, sc.GetConsumeKafkaConfig().ClientID, sc.GetConsumeKafkaConfig().GroupID,
		sc.GetConsumeKafkaConfig().TopicList(), sc.GetConsumeKafkaConfig().BrokerList(), mgr)

	if nil != err {
		log.ErrorWithCtx(ctx, "NewConsumeEventKafkaSubscriber err:%v", err)
		return nil, err
	}

	err = consumerSub.Start()
	if nil != err {
		log.ErrorWithCtx(ctx, "nobelSub start err:%v", err)
		return nil, err
	}

	presentSub, err := event.NewPresentKafkaSubscriber(ctx, sc.GetPresentKafkaConfig().ClientID, sc.GetPresentKafkaConfig().GroupID,
		sc.GetPresentKafkaConfig().TopicList(), sc.GetPresentKafkaConfig().BrokerList(), mgr)

	if nil != err {
		log.ErrorWithCtx(ctx, "NewPresentKafkaSubscriber err:%v", err)
		return nil, err
	}

	presentSub.Start()
	if nil != err {
		return nil, err
	}

	//go服务，会员开关变化kfk生产者
	nobelChangeProv2 := event.NewKafkaProduce(ctx, sc.GetNobelInfoChangeConfigV2().BrokerList(), sc.GetNobelInfoChangeConfigV2().ClientID,
		sc.GetNobelInfoChangeConfigV2().Topics)

	eSportTradeSub_, err := event.NewESortTradeSub(sc.GetESportTradeConfig().ClientID, sc.GetESportTradeConfig().GroupID, sc.GetESportTradeConfig().TopicList(),
		sc.GetESportTradeConfig().BrokerList())
	if nil != err {
		log.ErrorWithCtx(ctx, "NewESortTradeSub err:%v", err)
		return nil, err
	}

	channelKfkSub_, err := event.NewChannelKafkaSub(sc.GetChannelKfkConfig().ClientID, sc.GetChannelKfkConfig().GroupID, sc.GetChannelKfkConfig().TopicList(),
		sc.GetChannelKfkConfig().BrokerList(), mgr)
	if nil != err {
		log.ErrorWithCtx(ctx, "NewChannelKafkaSub err:%v", err)
		return nil, err
	}

	mgr.consumerTbeanSub, mgr.nobelSubV1, mgr.nobelProdV2 = consumerSub, nobilitySubv1, nobelChangeProv2
	mgr.presentSub = presentSub
	mgr.eSportTradeSub = eSportTradeSub_
	mgr.channelKfkSub = channelKfkSub_

	mgr.initRpc(ctx)

	dur := time.Minute * 60
	if conf.GetDaySec() <= 60 {
		dur = time.Millisecond * 500
	}
	coroutine.FixIntervalExec(mgr.godLevelAutoTimeoutNotice, dur)
	coroutine.FixIntervalExec(mgr.autoLoadFromDB, time.Second)
	coroutine.FixIntervalExec(mgr.handlerExtentVal, time.Second*2)
	coroutine.FixIntervalExec(mgr.handlerGodLevelTimeOut, time.Minute)

	return mgr, nil
}

func (mgr *Manager) initRpc(ctx context.Context) (*Manager, error) {
	var err error
	mgr.seqgenClient, err = seqgen.NewClient()
	if nil != err {
		log.ErrorWithCtx(ctx, "seqgen newClient err:%v", err)
		return nil, err
	}

	mgr.userdeCli, err = user_decoration.NewClient()
	if nil != err {
		log.ErrorWithCtx(ctx, "user_decoration NewClient err:%v", err)
		return nil, err
	}

	pushCli, err := pushclient.NewClient()
	if nil != err {
		log.ErrorWithCtx(ctx, "pushclient NewClient err:%v", err)
		return nil, err
	}
	publicnoticeCli, err := publicnotice.NewClient()
	if nil != err {
		log.ErrorWithCtx(ctx, "publicnotice NewClient err:%v", err)
		return nil, err
	}
	mgr.presentCli = presentclient.NewClient()
	mgr.channelolCli = channelol.NewClient()

	mgr.accountCli, err = account.NewClient()
	if nil != err {
		log.ErrorWithCtx(ctx, "account NewClient err:%v", err)
		return nil, err
	}
	mgr.channelCli = channel.NewClient()
	mgr.headImageCli = HeadImage.NewClient()
	mgr.pushCli = pushCli
	mgr.apiClient = apicenter.NewClient()
	mgr.publicNoticeCli = publicnoticeCli
	mgr.ykwCli, _ = youknowwho.NewClient()
	mgr.nobilityCli = nobility.NewIClient()
	mgr.reconcilePreCli = reconcilePresent.NewIClient()

	return mgr, nil
}

type RechargeEvent struct {
	Uid        uint32 `json:"uid"`
	Num        int64  `json:"num"`
	Type       string `json:"type"`
	OrderNo    string `json:"orderNo"`
	PayChannel string `json:"payChannel"`
	MTime      string `json:"mtime"`
}

func getNewPrefix(triggerType uint32) string {
	return "全服通知！！！恭喜"
}

func (m *Manager) clearNobilityCache(uid uint32) {
	key := fmt.Sprintf("nobility_v2_%v", uid)
	f := func(r *redis.Client) {
		r.Del(key)
	}
	m.batchOperCache(f)
}

type GiftInfo struct {
	GiftId    uint32
	GiftCount uint32
}

func (m *Manager) fillBreakingNew(uid, targetuid, channelId, nobilityLevel, extentCnt uint32, gift *GiftInfo, triggerType uint32) (*publicNoticePb.CommonBreakingNewsV3, error) {
	ctx, cancel := context.WithTimeout(context.Background(), time.Second*8)
	defer cancel()

	strNewsPrefix := getNewPrefix(triggerType)

	//user & channel
	breakingNewsMessage := &publicNoticePb.CommonBreakingNewsV3{
		FromUid:    uid,
		ChannelId:  channelId,
		NewsPrefix: strNewsPrefix,
	}

	//nobility
	breakingNewsMessage.NobilityExtentCnt = extentCnt
	breakingNewsMessage.NobilityLevel = nobilityLevel
	_, breakingNewsMessage.NobilityLevelName = conf.GetNobilityLevel(0, int64(nobilityLevel))

	//targetuser
	if targetuid > 0 {
		breakingNewsMessage.TargetUid = targetuid
	}

	//present
	if gift != nil && gift.GiftId > 0 {
		giftInfo, err := m.presentCli.GetPresentConfigById(ctx, gift.GiftId)
		if nil != err {
			log.ErrorWithCtx(ctx, "fillBreakingNew GetPresentConfigById giftId:%v err:%v", gift.GiftId, err)
		} else {
			breakingNewsMessage.PresentNewsBaseOpt = &publicNoticePb.PresentBreakingNewsBaseOpt{
				GiftName:    giftInfo.GetItemConfig().GetName(),
				GiftId:      gift.GiftId,
				GiftCount:   gift.GiftCount,
				GiftIconUrl: giftInfo.GetItemConfig().GetIconUrl(),
				GiftWorth:   gift.GiftCount * giftInfo.GetItemConfig().GetPrice(),
			}
		}
	}

	//breaking
	breakingNewsMessage.BreakingNewsBaseOpt = &publicNoticePb.CommBreakingNewsBaseOpt{
		TriggerType: triggerType, RollingCount: 2, RollingTime: 10,
		AnnounceScope:    uint32(pushPb.CommBreakingNewsBaseOpt_INSIDE_CHANNEL + pushPb.CommBreakingNewsBaseOpt_OUTSIDE_CHANNEL),
		JumpType:         uint32(pushPb.CommBreakingNewsBaseOpt_JUMP_ClICK_OUTSIDE),
		JumpPosition:     uint32(pushPb.CommBreakingNewsBaseOpt_JUMP_TO_CHANNEL_CLICK),
		AnnouncePosition: uint32(pushPb.CommBreakingNewsBaseOpt_UPPER),
	}

	return breakingNewsMessage, nil
}

func (m *Manager) PushBreakingNewsV3(uid, channelId uint32, breakingNewsMessage *publicNoticePb.CommonBreakingNewsV3) {
	ctx, cancel := context.WithTimeout(context.Background(), time.Second*5)
	defer cancel()
	req := &publicNoticePb.PushBreakingNewsReq{
		CommonBreakingNews: breakingNewsMessage,
	}
	_, err := m.publicNoticeCli.PushBreakingNews(ctx, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "PushBreakingNewsV3 fail. uid:%v, BreakingNewsMessage:%+v", uid,
			breakingNewsMessage)
	} else {
		log.InfoWithCtx(ctx, "PushBreakingNewsV3 finish. uid:%v, BreakingNewsMessage", uid,
			breakingNewsMessage)
	}
}

func (m *Manager) handlerGodLevelTimeOut() {
	ctx := context.Background()
	islock, err := m.cache.Lock(godLevelTimeOutLockKey, time.Second*18)
	if nil != err {
		log.ErrorWithCtx(ctx, "handlerGodLevelTimeOut Lock err:%v", err)
		return
	}
	if !islock {
		return
	}
	defer m.cache.UnLock(godLevelTimeOutLockKey)

	log.DebugWithCtx(ctx, "handlerGodLevelTimeOut begin")

	res, err := m.cache.RedisClient.ZRangeByScore(godLevelTimeOutListKey, redis.ZRangeBy{
		Min:    "0",
		Max:    fmt.Sprintf("%v", time.Now().Unix()),
		Offset: 0,
		Count:  limit,
	}).Result()
	if nil != err {
		log.ErrorWithCtx(ctx, "handlerGodLevelTimeOut ZRangeByScore err:%v", err)
		return
	}

	log.InfoWithCtx(ctx, "handlerGodLevelTimeOut len:%d res:%v", len(res), res)

	delKeys := make([]interface{}, 0)
	for _, r := range res {
		delKeys = append(delKeys, r)
		uid, err := strconv.ParseUint(r, 10, 32)
		if nil != err {
			log.ErrorWithCtx(ctx, "handlerGodLevelTimeOut ParseUint err:%v", err)
			continue
		}

		info, err := m.db.GetNobilityInfo(uint32(uid))
		if nil != err {
			log.ErrorWithCtx(ctx, "handlerGodLevelTimeOut GetNobilityInfo uid:%v err:%v", uid, err)
			continue
		}
		_, leftSec := getGodLevelLeftDay(info.CycleBeginTs)
		level, _ := conf.GetNobilityLevel(info.NobilityValue, info.NobilityLevel)
		if (uint32(level) < conf.GetConf().GodLevel) || leftSec <= 0 {
			m.onLevelDown(ctx, uint32(uid))
		}

		log.InfoWithCtx(ctx, "handlerGodLevelTimeOut uid:%v level:%v leftSec:%v info:%v", uid, level, leftSec, info)
	}

	m.cache.RedisClient.ZRem(godLevelTimeOutListKey, delKeys...)

	//return
}

func (m *Manager) handlerExtentVal() {
	ctx := context.Background()
	islock, err := m.cache.Lock(extentValLockKey, time.Second*18)
	if nil != err {
		log.ErrorWithCtx(ctx, "handlerExtentVal Lock err:%v", err)
		return
	}
	if !islock {
		return
	}
	defer m.cache.UnLock(extentValLockKey)

	res, err := m.cache.RedisClient.ZRevRangeWithScores(extentValListKey, 0, limit).Result()
	if nil != err {
		log.ErrorWithCtx(ctx, "handlerExtentVal ZRevRangeWithScores err:%v", err)
	}

	log.DebugfWithCtx(ctx, "handlerExtentVal res:%+v", res)

	delKeys := make([]interface{}, 0)
	for _, z := range res {
		key := z.Member.(string)
		val := z.Score
		delKeys = append(delKeys, z.Member)
		arr := strings.Split(key, "_")
		sz := len(arr)
		if sz < 2 {
			continue
		}
		uid, _ := strconv.ParseUint(arr[sz-1], 10, 32)
		if uid == 0 {
			continue
		}

		cnt, _ := m.cache.RedisClient.IncrBy(key, 1).Result()
		if cnt > 1 {
			log.ErrorWithCtx(ctx, "handlerExtentVal repeated uid:%v key:%v %v", uid, key, val)
			continue
		}
		m.cache.RedisClient.Expire(key, time.Hour*24)

		//预防大土豪
		for i := 0; i < 2; i++ {
			brepeat, err := m.HandlerExtent(ctx, uint32(uid), uint32(val), 0)
			if nil != err {
				log.ErrorWithCtx(ctx, "handlerExtentVal HandlerExtent uid%v val:%v err:%v", uid, val, err)
				break
			}
			if !brepeat {
				break
			}
			val = 0
		}
	}
	m.cache.RedisClient.ZRem(extentValListKey, delKeys...)

	//return
}

func (m *Manager) doTimeOutNotice(ctx context.Context, info *pb.NobilityInfo) {

	defer func() {
		if r := recover(); nil != r {
			log.ErrorWithCtx(ctx, "doTimeOutNotice recover uid:%v err:%v", info.Uid, r)
		}
	}()

	mapNoticeDay := map[int64]bool{15: true, 7: true, 1: true}

	leftDay, leftSec := getGodLevelLeftDay(int64(info.CycleTs))

	lockDay := leftDay
	if lockDay < 7 {
		lockDay = 0
	}

	//避免重复推送
	if lock, _ := m.cache.Lock(downNoticeKey(info.Uid, uint32(lockDay)), time.Second*time.Duration(conf.GetDaySec())); !lock {
		return
	}

	log.InfoWithCtx(ctx, "doTimeOutNotice info:%+v leftDay:%v lockDay:%d begin", info, leftDay, lockDay)

	if leftDay <= 3 {
		triggerTs := time.Now().Unix() + leftSec + 60
		err := m.cache.RedisClient.ZAdd(godLevelTimeOutListKey, redis.Z{
			Score:  float64(triggerTs),
			Member: fmt.Sprintf("%v", info.Uid),
		}).Err()
		if nil != err {
			log.ErrorWithCtx(ctx, "doTimeOutNotice ZAdd uid:%v err:%v", info.Uid, err)
		}

		log.InfoWithCtx(ctx, "doTimeOutNotice info:%+v triggerTs:%d", info, triggerTs)
	}

	log.DebugWithCtx(ctx, "doTimeOutNotice add godLevelTimeOutListKey uid:%v", info.Uid)

	if mapNoticeDay[leftDay] {
		currVal, err := m.cache.RedisClient.Get(extentValKey(info.Uid)).Int64()
		if nil != err && err != redis.Nil {
			log.ErrorWithCtx(ctx, "doTimeOutNotice Get uid:%v err:%v", info.Uid, err)
			return
		}
		var lackVal = float32(conf.GetConf().AddExpireDayVal - currVal)
		if lackVal <= 100 {
			lackVal = 0.1
		} else {
			lackVal = lackVal / 100
		}

		tempMsg := fmt.Sprintf("你的贵族身份【神王】即将过期，继续消费%v元可延期%v天，贵族详情点此查看>", lackVal, conf.GetConf().AddExpireDayNum)
		err = m.apiClient.SendTTHelperMsgWithUrl(info.Uid, tempMsg, "贵族详情点此查看>", conf.GetConf().NobilityWebUrl)
		if nil != err {
			log.ErrorWithCtx(ctx, "doTimeOutNotice SendTTHelperMsgWithUrl %v %v", info.Uid, tempMsg)
		}
	}

	log.DebugWithCtx(ctx, "doTimeOutNotice info:%+v leftDay:%v finish", info, leftDay)
}

// 过期定时提醒和定时过期
func (m *Manager) godLevelAutoTimeoutNotice() {
	ctx := context.Background()
	log.DebugWithCtx(ctx, "godLevelAutoTimeoutNotice begin")

	lkey := "gnobility_godlevel_auto_timeout_lock"
	lock, _ := m.cache.Lock(lkey, time.Second*120)
	if !lock {
		log.DebugWithCtx(ctx, "godLevelAutoTimeoutNotice lock fail")
		return
	}

	defer m.cache.UnLock(lkey)

	idx, off, err := m.getIdxAndOff(ctx, timeOutIdxKey, timeOutOffKey)
	if nil != err {
		log.DebugWithCtx(ctx, "godLevelAutoTimeoutNotice getIdxAndOff fail err:%v", err)
		return
	}

	nowTs := time.Now()
	cycleSec := nowTs.Unix() - 10*conf.GetDaySec() //10天前开始的
	level := conf.GetConf().GodLevel
	levelMinVal, err := conf.GetLevelMinVal(int64(level))
	if nil != err {
		log.ErrorWithCtx(ctx, "godLevelAutoTimeoutNotice GetLevelMinVal level:%v err:%v", level, err)
		return
	}
	query := fmt.Sprintf("(nobility_value >= %v or nobility_level=%v) and cycle_begin_ts<=%v", levelMinVal, level, cycleSec)
	infos, err := m.slaveDb.GetNobilityInfoList(idx, limit, off, query)
	if nil != err {
		log.ErrorWithCtx(ctx, "godLevelAutoTimeoutNotice GetNobilityInfoList err:%v", err)
		return
	}

	log.InfoWithCtx(ctx, "godLevelAutoTimeoutNotice idx:%v off:%v sz:%v", idx, off, len(infos))

	for _, f := range infos {
		log.InfoWithCtx(ctx, "godLevelAutoTimeoutNotice idx:%v off:%v nobility:%v", idx, off, f)

		m.doTimeOutNotice(ctx, &pb.NobilityInfo{
			Value:   uint64(f.NobilityValue),
			Level:   uint32(f.NobilityLevel),
			CycleTs: uint32(f.CycleBeginTs),
			Uid:     f.Uid,
		})
	}

	if limit > len(infos) {
		idx = (idx + 1) % 100
		off = 0
	} else {
		off = off + limit
	}

	updateInfos := make([]interface{}, 0)
	updateInfos = append(updateInfos, timeOutIdxKey, idx, timeOutOffKey, off)
	err = m.cache.RedisClient.MSet(updateInfos...).Err()
	if nil != err {
		log.ErrorWithCtx(ctx, "godLevelAutoTimeoutNotice MSet err:%v", err)
		return
	}

	log.DebugWithCtx(ctx, "godLevelAutoTimeoutNotice finish")
}

// 房间广播消息
func (m *Manager) pushChannelBroMsgToChannels(ctx context.Context, channelIds []uint32, skipUidList []uint32, channelMsgBin []byte) error {
	pushMessage := &pushPb.PushMessage{
		Cmd:     uint32(pushPb.PushMessage_CHANNEL_MSG_BRO),
		Content: channelMsgBin,
		SeqId:   uint32(time.Now().Unix()),
	}

	pushMessageBytes, e := pushMessage.Marshal()
	if e != nil {
		log.ErrorWithCtx(ctx, "pushChannelBroMsgToChannels Marshal channelIds:%v, err: %v", channelIds, e)
		return e
	}

	notification := &pushPB.CompositiveNotification{
		Sequence: uint32(time.Now().Unix()),
		TerminalTypeList: []uint32{
			protocol.MobileAndroidTT,
			protocol.MobileIPhoneTT,
		},
		TerminalTypePolicy: pushNotification.DefaultPolicy,
		AppId:              uint32(protocol.TT),
		ProxyNotification: &pushPB.ProxyNotification{
			Type:       uint32(pushPB.ProxyNotification_PUSH),
			Payload:    pushMessageBytes,
			Policy:     pushPB.ProxyNotification_DEFAULT,
			ExpireTime: 60,
		},
	}

	multicastMap := map[uint64]string{}
	for _, channelId := range channelIds {
		if channelId == 0 {
			continue
		}
		multicastMap[uint64(channelId)] = fmt.Sprintf("%d@channel", channelId)
	}

	if len(multicastMap) == 0 {
		return nil
	}

	err := m.pushCli.PushMulticasts(ctx, multicastMap, skipUidList, notification)
	if err != nil {
		log.ErrorWithCtx(ctx, "pushChannelBroMsgToChannels fail to PushMulticasts channelIds:%v, err: %s", channelIds, err.Error())
		return err
	}

	return nil
}

func (m *Manager) pushMsgToChannel(ctx context.Context, channelId, msgType uint32, content string, optContent []byte) error {

	msg := &channelPB.ChannelBroadcastMsg{ //pushChannelBroMsgToChannels
		FromUid:      10000,
		FromAccount:  "ttyuyinzhushou",
		FromNick:     "TT语音助手",
		Time:         uint64(time.Now().Unix()),
		ToChannelId:  channelId,
		Type:         msgType,
		Content:      []byte(content),
		PbOptContent: optContent,
	}

	channelMsgBin, gerr := msg.Marshal()
	if gerr != nil {
		log.ErrorWithCtx(ctx, "pushMsgToChannel marshal err: %s, %+v", gerr.Error(), msg)
		return gerr
	}

	gerr = m.pushChannelBroMsgToChannels(ctx, []uint32{channelId}, []uint32{}, channelMsgBin)
	if gerr != nil {
		log.ErrorWithCtx(ctx, "pushMsgToChannel fail to SendChannelBroadcastMsg. channelId:%d err:%v", channelId, gerr)
		return gerr
	}

	log.DebugfWithCtx(ctx, "pushMsgToChannel msgType:%v, channelId:%d, content:%s", msgType, channelId, content)
	return nil
}

func (m *Manager) getIdxAndOff(ctx context.Context, idxKey, offKey string) (int, int, error) {
	var idx, off uint64 = 0, 0
	result, err := m.cache.RedisClient.MGet(idxKey, offKey).Result()
	if nil != err && err != redis.Nil {
		log.ErrorWithCtx(ctx, "getIdxAndOff Get err:%v", err)
		return 0, 0, err
	}

	if 2 != len(result) {
		log.ErrorWithCtx(ctx, "getIdxAndOff invalid len")
		return 0, 0, errors.New("invalid result")
	}

	if nil != result[0] {
		idx, _ = strconv.ParseUint(result[0].(string), 10, 32)
	}
	if nil != result[1] {
		off, _ = strconv.ParseUint(result[1].(string), 10, 32)
	}
	return int(idx), int(off), nil
}

func (m *Manager) GetNobilityInfoList(idx, limit, off int, query string) ([]pb.NobilityInfo, error) {
	infos := make([]pb.NobilityInfo, 0)
	tmpInfos, err := m.slaveDb.GetNobilityInfoList(idx, limit, off, query)
	if nil != err {
		return infos, err
	}
	for _, info := range tmpInfos {
		tmpLevel, _ := conf.GetNobilityLevel(info.NobilityValue, info.NobilityLevel)
		infos = append(infos, pb.NobilityInfo{
			Value:         uint64(info.NobilityValue),
			Level:         uint32(tmpLevel),
			CycleTs:       uint32(info.CycleBeginTs),
			Uid:           info.Uid,
			WaitCostValue: uint32(info.WaitCostValue),
		})
	}
	return infos, nil
}

// 定时加载
func (m *Manager) autoLoadFromDB() {
	ctx := context.Background()
	idx := m.cache.RedisClient.IncrBy("gnobility_auto_load_idx", 1).Val()
	idx = idx % 100

	log.DebugWithCtx(ctx, "autoLoadFromDB idx:%v", idx)

	lkey := "gnobility_auto_load_lock"
	lock, _ := m.cache.Lock(lkey, time.Second*18)
	if !lock {
		return
	}

	defer m.cache.UnLock(lkey)

	level := conf.GetConf().GodLevel //godlevel 之前的等级
	levelMinVal, err := conf.GetLevelMinVal(int64(level))
	if nil != err {
		log.ErrorWithCtx(ctx, "autoLoadFromDB GetLevelMinVal level:%v err:%v", level, err)
		return
	}
	query := fmt.Sprintf("nobility_value >= %v or nobility_level=%v", levelMinVal, level)
	infos, err := m.GetNobilityInfoList(int(idx), limit, 0, query)
	if nil != err {
		log.ErrorWithCtx(ctx, "autoLoadFromDB GetNobilityInfoList err:%v", err)
		return
	}

	for _, info := range infos {
		log.DebugWithCtx(ctx, "autoLoadFromDB idx:%v off:%v info:%v", idx, 0, info)
	}

	if len(infos) > 0 {
		m.cache.SetNobilityInfoList(ctx, infos)
	}

	//return
}

func invalidEventType(ev RechargeEvent) bool {
	payChannel := ev.PayChannel
	if ev.Type == "transfor" {
		if payChannel != "I2C" && payChannel != "CH2C" {
			return false
		}
	} else if ev.Type == "charge" {
		if !mapPayChannel[payChannel] {
			return false
		}
	} else {
		return false
	}
	return true
}

func (m *Manager) sendUserDec(uid uint32, cycleBegin int64) error {
	ctx, cancel := context.WithTimeout(context.Background(), time.Second*3)
	defer cancel()
	orderId := fmt.Sprintf("nobility_send_decorations_%v_%v", uid, cycleBegin)

	nobilityConf := conf.GetConf()

	dec := nobilityConf.Decoreation
	if nil == dec {
		log.ErrorWithCtx(ctx, "sendUserDec Decoreation nil")
		return errors.New("sendUserDec Decoreation nil")
	}
	//加上周期
	cycleBegin = cycleBegin + nobilityConf.CycleSec

	var err error
	for i := 0; i < 3; i++ {
		err = m.userdeCli.Upsert(ctx, uid, userdecpb.Type_FLOAT, dec.Id, dec.Version, userdecpb.TimeOverlayType_TIMEALTER, cycleBegin, orderId)
		if nil != err {
			log.ErrorWithCtx(ctx, "sendUserDec uid:%v err:%v", uid, err)
			continue
		} else {
			break
		}
	}

	log.DebugfWithCtx(ctx, "sendUserDec success uid:%v cycleBegin:%v err:%v", uid, cycleBegin, err)

	return nil
}

// 定时加载，加时消费kfk更及时
func (m *Manager) HandlerNobilityEvent(msg *sarama.ConsumerMessage) error {
	ctx := context.Background()
	event := &kafkanobility.NobilityInfoChange{}
	err := proto.Unmarshal(msg.Value, event)
	if nil != err {
		log.ErrorWithCtx(ctx, "HandlerNobilityEvent UnmarshalMerge err:%v", err)
		return err
	}

	log.DebugfWithCtx(ctx, "HandlerNobilityEvent event:%+v", event)

	/*	m.cache.SetNobilityInfoList([]pb.NobilityInfo{{
		Value:     event.NobilityValue,
		KeepValue: event.KeepNobilityValue,
		Level:     event.Level,
		CycleTs:   event.CycleBeginTs,
		Uid:       event.Uid,
	}})*/
	//升级到神王发放主页飘
	if event.GetEventType() == 1 {
		if event.GetLevel() == conf.GetConf().GodLevel {
			m.sendUserDec(event.GetUid(), int64(event.GetCycleBeginTs()))
		}
	}
	//log.InfoWithCtx(ctx,"HandlerNobilityEvent event:%v", event)
	return nil
}

// for test
func (m *Manager) HandlerEventList(ctx context.Context, evList []RechargeEvent, istest bool) error {
	if 0 == len(evList) {
		return errors.New("invalid evList")
	}
	log.DebugfWithCtx(ctx, "HandlerEventList evList:%v", evList)

	var tmpInfo *pb.NobilityInfo = nil
	for _, ev := range evList {
		if nil == tmpInfo || tmpInfo.Uid != ev.Uid {
			info, err := m.cache.GetNobilityInfo(ctx, ev.Uid)
			if nil != err {
				log.ErrorWithCtx(ctx, "HandlerEventList GetNobilityInfo ev:%v err:%v", ev, err)
				continue
			}
			tmpInfo = info
		}

		if tmpInfo.Level < (conf.GetConf().GodLevel - 2) { //为避免连跳2级，从7级开始检查
			log.InfoWithCtx(ctx, "HandlerEventList invalid level ev:%v tmpInfo:%v", ev, tmpInfo)
			continue
		}

		ok := invalidEventType(ev)
		if !ok {
			log.DebugfWithCtx(ctx, "HandlerEventList invalidEventType ev:%v tmpInfo:%v", ev, tmpInfo)
			continue
		}

		key := fmt.Sprintf("charge_v2_%v", ev.OrderNo)
		cnt, _ := m.cache.RedisClient.IncrBy(key, 1).Result()
		if cnt > 1 {
			continue
		}

		if istest {
			m.db.RecordRecharge(&mysql.RechargeRecord{
				Uid:        ev.Uid,
				Num:        ev.Num,
				Level:      int64(tmpInfo.Level),
				OrderId:    ev.OrderNo,
				Type:       ev.Type,
				PayChannel: ev.PayChannel,
				ChargeTime: time.Now(),
				UpdateTime: time.Now(),
			})
		}

		//更新数据
		isLevelUp, newLevel, err := m.checkNobilityLevelUp(ctx, tmpInfo.Uid)
		if nil != err {
			log.ErrorWithCtx(ctx, "HandlerEventList checkNobilityLevelUp ev:%v err:%v", ev, err)
			continue
		}

		//更新推送
		if isLevelUp {
			m.onLevelUp(ctx, tmpInfo.Uid, uint32(newLevel))
		}
	}
	return nil
}

type ConsumeEvent struct {
	Uid       uint32
	ChannelId uint32
	Val       uint32

	ItemId    uint32
	ItemCount uint32
	TargetUid uint32
}

func (m *Manager) handlerSpecilGift(ctx context.Context, cEvent *ConsumeEvent) error {

	log.DebugfWithCtx(ctx, "OnConsumeEvent cEvent:%+v", cEvent)

	//贵族礼物全服推送
	if conf.IsSpecilGift(cEvent.ItemId) {
		info, gerr := m.cache.GetNobilityInfo(ctx, cEvent.Uid)
		if nil != gerr {
			log.ErrorWithCtx(ctx, "OnConsumeEvent GetNobilityInfo present:%+v err:%v", cEvent, gerr)
		}
		gift := &GiftInfo{
			GiftId:    cEvent.ItemId,
			GiftCount: cEvent.ItemCount,
		}
		breakMsg, err := m.fillBreakingNew(cEvent.Uid, cEvent.TargetUid, cEvent.ChannelId, info.Level, 0, gift,
			uint32(pushPb.CommBreakingNewsBaseOpt_CHANNEL_NOBILITY_SPECIAL_GIFT))
		if nil != err {
			log.ErrorWithCtx(ctx, "OnConsumeEvent fillBreakingNew err:%v", err)
		} else {
			m.PushBreakingNewsV3(cEvent.Uid, cEvent.ChannelId, breakMsg)
		}
	}
	return nil
}

func (m *Manager) HandlerExtent(ctx context.Context, uid, val, cid uint32) (bool, error) {
	dbInfo, err := m.db.GetNobilityInfo(uid)
	if nil != err {
		log.ErrorWithCtx(ctx, "OnConsumeEvent GetNobilityInfo uid, err:%v", uid, err)
		return false, err
	}

	currLevel, _ := conf.GetNobilityLevel(dbInfo.NobilityValue, dbInfo.NobilityLevel)

	//延长天数
	addDays, addCnt, isRepeat, err := m.checkExpirationDay(ctx, &dbInfo, int64(val))
	if nil != err {
		log.ErrorWithCtx(ctx, "OnConsumeEvent checkExpirationDay uid:%v err:%v", uid, err)
		return false, err
	}

	//延长全服推送
	if addDays > 0 {
		m.onExtentDay(ctx, uid, cid, uint32(currLevel), addDays, addCnt)
	}
	return isRepeat, nil
}

// 好几种消费渠道，加时转转消费
func (m *Manager) HandlerPresentEvent(msg *sarama.ConsumerMessage) error {
	ctx := context.Background()
	presentEvent := &kafkapresent.PresentEvent{}
	err := proto.Unmarshal(msg.Value, presentEvent)
	if err != nil {
		log.ErrorWithCtx(ctx, " handlerPresentEvent Failed to proto.Unmarshal err(%v)", err)
		return err
	}

	//背包的先不管
	if presentEvent.GetItemSource() == 1 {
		return nil
	}

	//不是T豆不处理
	if presentEvent.PriceType != 2 {
		return nil
	}

	log.DebugfWithCtx(ctx, "HandlerPresentEvent presentevent:%v", presentEvent)

	//贵族特殊礼物全服
	m.handlerSpecilGift(ctx, &ConsumeEvent{
		Uid:       presentEvent.Uid,
		ChannelId: presentEvent.ChannelId,
		Val:       presentEvent.Price * presentEvent.ItemCount,
		ItemId:    presentEvent.ItemId,
		ItemCount: presentEvent.ItemCount,
		TargetUid: presentEvent.TargetUid,
	})

	return nil
}

// for test
func (m *Manager) checkNobilityLevelUp(ctx context.Context, uid uint32) (bool, int64, error) {

	dbInfo, err := m.db.GetNobilityInfo(uid)
	if nil != err {
		log.ErrorWithCtx(ctx, "checkNobilityLevelUp GetNobilityInfo uid:%v err:%v", uid, err)
		return false, 0, err
	}

	oldLevel, _ := conf.GetNobilityLevel(dbInfo.NobilityValue, dbInfo.NobilityLevel)

	log.DebugfWithCtx(ctx, "checkNobilityLevelUp dbInfo:%+v oldLevel:%v godLevel:%v", dbInfo, oldLevel, conf.GetConf().GodLevel)

	if uint32(oldLevel) != conf.GetConf().GodLevel-1 {
		return false, 0, nil
	}

	nowTs := time.Now()
	beginTs := nowTs.AddDate(0, 0, -6)
	beginTs = time.Date(beginTs.Year(), beginTs.Month(), beginTs.Day(), 0, 0, 0, 0, time.Local)

	rechargeVal, err := m.rechargeDb.GetRecharSumValue(ctx, uid, beginTs, nowTs)
	if nil != err {
		log.ErrorWithCtx(ctx, "checkNobilityLevelUp uid:%v err:%v", uid, err)
		return false, 0, err
	}

	//newLevel := getNewLevel(rechargeVal)
	newLevel, _ := conf.GetNobilityLevel(rechargeVal, 0)

	isLevelUp := newLevel == int64(conf.GetConf().GodLevel)

	newLevelMinVal, err := conf.GetLevelMinVal(int64(conf.GetConf().GodLevel))
	if nil != err {
		log.ErrorWithCtx(ctx, "checkNobilityLevelUp GetLevelMinVal uid:%v err:%v", uid, err)
		return false, 0, err
	}
	oldLevelMinVal, _ := conf.GetLevelMinVal(int64(conf.GetConf().GodLevel - 1))

	//如果已经是神王了，修改待消费金额就OK
	var addWaitCost, newVal, cycleBeginTs int64 = 0, dbInfo.NobilityValue, 0
	if isLevelUp {
		ratio := conf.GetConf().WaitCostRatio
		if ratio == 0 {
			ratio = 0.6
		}
		levelGapValue := newLevelMinVal - oldLevelMinVal
		addWaitCost = int64(float32(levelGapValue) * ratio)
		newVal = newLevelMinVal
		cycleBeginTs = genCycleBeginTs().Unix() //升级重置
	}

	log.DebugfWithCtx(ctx, "checkNobilityLevelUp val:%v isLevelUp:%v newLevel:%v addWaitCost:%v cycleBeginTs:%v", rechargeVal, isLevelUp, newLevel, addWaitCost, cycleBeginTs)

	upInfo := &mysql.NobilityUpdateInfo{
		Uid:              uid,
		SetNobilityValue: newVal,
		SetCycleTs:       cycleBeginTs,
		AddWaitCostValue: addWaitCost,
		SetLevel:         newLevel,
		IsSetLevel:       true,
	}
	err = m.db.UpdateNobilityInfo(ctx, upInfo)
	if nil != err {
		log.ErrorWithCtx(ctx, "checkNobilityLevelUp UpdateNobilityInfo info:%v err:%v", upInfo, err)
		return false, 0, err
	}

	//清空cache , c++服务cache
	//m.cache.RedisClient.Del(nobilityInfoKey(info.Uid))

	m.cache.SetNobilityInfoList(ctx, []pb.NobilityInfo{{
		Uid:   uid,
		Value: uint64(newVal),
	}})

	return isLevelUp, newLevel, nil
}

// 检查有效期更新
func (m *Manager) checkExpirationDay(ctx context.Context, info *mysql.NobilityInfoStu, num int64) (int64, int64, bool, error) {

	val, err := m.cache.RedisClient.IncrBy(extentValKey(info.Uid), num).Result()
	if nil != err {
		log.ErrorWithCtx(ctx, "checkExpirationDay incrby uid:%v err:%v", info.Uid, err)
		return 0, 0, false, err
	}

	if val < conf.GetConf().AddExpireDayVal {
		log.DebugfWithCtx(ctx, "checkExpirationDay not enough uid:%v val:%v", info.Uid, val)
		return 0, 0, false, nil
	}

	addDayCnt := conf.GetConf().AddExpireDayNum
	newCnt := m.cache.RedisClient.IncrBy(continuousKey(info.Uid), 1).Val()
	if conf.GetConf().ContinuousNum != 0 {
		if newCnt%conf.GetConf().ContinuousNum == 0 { //每N次就加一次额外奖励
			addDayCnt = addDayCnt + conf.GetConf().RewardDayNum
		}
	}
	currDay, _ := getGodLevelLeftDay(info.CycleBeginTs)
	leftDays := conf.GetConf().ExpireDayMaxNum - currDay
	//不能超过ExpireDayMaxNum
	if leftDays < addDayCnt {
		log.ErrorWithCtx(ctx, "checkExpirationDay uid:%v leftCnt:%v addDay:%v", info.Uid, leftDays, addDayCnt)
		m.cache.RedisClient.IncrBy(continuousKey(info.Uid), -1)
		return 0, 0, false, errors.New("剩余天数不足")
	}

	//减去延长所需的值
	newVal, err := m.cache.RedisClient.IncrBy(extentValKey(info.Uid), -1*conf.GetConf().AddExpireDayVal).Result()
	if nil != err {
		log.ErrorWithCtx(ctx, "checkExpirationDay IncrBy fail uid:%v", info.Uid)
	}

	isRepeat := newVal >= conf.GetConf().AddExpireDayVal
	expireSec := time.Duration((currDay+addDayCnt+1)*conf.GetDaySec()) * time.Second //额外多一个DaySec
	m.cache.RedisClient.Expire(extentValKey(info.Uid), expireSec)
	m.cache.RedisClient.Expire(continuousKey(info.Uid), expireSec)
	log.DebugfWithCtx(ctx, "checkExpirationDay uid:%v val:%v newVal:%v currDay:%v newCnt:%v leftCnt:%v addDayNum:%v", info.Uid, val, newVal, currDay, newCnt, leftDays, addDayCnt)
	return addDayCnt, newCnt, isRepeat, nil
}
func genCycleBeginTs() time.Time {
	nowTs := time.Now()
	tomorrow := nowTs.AddDate(0, 0, 1)
	beginTs := time.Date(tomorrow.Year(), tomorrow.Month(), tomorrow.Day(), 0, 0, 0, 0, time.Local)
	return beginTs
}

func (m *Manager) notifyNobilityClient(uid uint32) error {
	ctx, cancel := context.WithTimeout(context.Background(), time.Second*2)
	defer cancel()
	seq, err := m.seqgenClient.GenerateSequence(ctx, uid, seqgen.NamespaceUser, seqgen.KeyNobility, 1)
	if err != nil {
		log.ErrorWithCtx(ctx, "notifyNobilityClient GenerateSequence apprentice uid:%v, err:%v", uid, err)
		return err
	}

	var byt [4]byte
	binary.BigEndian.PutUint32(byt[:], uint32(sync.SyncReq_NOBILITY)) // Network Order
	perr := m.pushCli.PushToUsers(ctx, []uint32{uid}, &pushPB.CompositiveNotification{
		Sequence:           uint32(seq),
		TerminalTypePolicy: pushclient.DefaultPolicy,
		AppId:              0,
		ProxyNotification: &pushPB.ProxyNotification{
			Type:    uint32(pushPB.ProxyNotification_NOTIFY),
			Payload: byt[:],
		},
	})
	if nil != perr {
		log.ErrorWithCtx(ctx, "notifyNobilityClient uid:%v err:%v", uid, perr)
		return perr
	}
	return nil
}
func (m *Manager) batchOperCache(f func(client *redis.Client)) {
	for _, r := range m.batchCacheClient {
		f(r)
	}
}

// 升级成功推送，等级更新 , 添加到批量数据
func (m *Manager) onLevelUp(ctx context.Context, uid, newLevel uint32) error {
	levelUpMsg := conf.GetConf().GodLevelUpMsg
	log.InfoWithCtx(ctx, "onLevelUp uid:%v", uid)
	err := m.apiClient.SendTTHelperMsgWithUrl(uid, levelUpMsg, "详情点此查看>", conf.GetConf().NobilityWebUrl)
	if nil != err {
		log.ErrorWithCtx(ctx, "onLevelUp SendTTHelperMsgWithUrl levelupmsg uid:%v err:%v", uid, err)
		return err
	}

	err = m.apiClient.SendTTHelperMsgWithUrl(uid, conf.GetConf().GodLevelPriMsg, "点此设置>", conf.GetConf().SafeWebUrl)
	if nil != err {
		log.ErrorWithCtx(ctx, "onLevelUp SendTTHelperMsgWithUrl primsg uid:%v err:%v", uid, err)
		return err
	}

	f := func(r *redis.Client) {
		r.HSet(nobilityLevelFlag, fmt.Sprintf("%v", uid), conf.GetConf().GodLevel)
	}

	m.batchOperCache(f)

	//全服推送
	subCtx, cancel := context.WithTimeout(context.Background(), time.Second*5)
	defer cancel()
	var channelId uint32 = 0
	olResp, err := m.channelolCli.GetUsersChannelId(subCtx, uid, uid)
	if nil != err {
		log.ErrorWithCtx(ctx, "onLevelUp GetUsersChannelId err:%v", err)
	} else {
		channelId = olResp.ChannelId
	}

	breakMsg, err := m.fillBreakingNew(uid, 0, channelId, newLevel, 0, nil, uint32(pushPb.CommBreakingNewsBaseOpt_CHANNEL_NOBILITY_GODLEVEL_LEVELUP))
	if nil != err {
		log.ErrorWithCtx(ctx, "onLevelUp fillBreakingNew uid:%v err:%v", uid, err)
		return err
	}
	m.PushBreakingNewsV3(uid, channelId, breakMsg)

	//公屏消息
	userInof, err := m.accountCli.GetUser(subCtx, uid)
	if nil != err {
		log.ErrorWithCtx(ctx, "onLevelUp GetUser uid:%v err:%v", uid, err)
		return err
	}
	_, levelName := conf.GetNobilityLevel(0, int64(newLevel))
	nobilityChannelMsg := &channelPB.NobilityChannelNotifyMsg{
		Uid:               uid,
		NickName:          userInof.Nickname,
		ChannelId:         channelId,
		EventType:         uint32(channelPB.NobilityChannelNotifyMsg_ENUM_NOBILITY_LEVEL_UP),
		NobilityLevel:     newLevel,
		NobilityLevelName: levelName,
		Account:           userInof.Username,
	}

	yresp, err := m.ykwCli.GetUKWInfo(ctx, uid)
	if nil != err {
		log.ErrorWithCtx(ctx, "onLevelUp GetUKWInfo uid:%v err:%v", uid, err)
		//return err
	} else {
		nobilityChannelMsg.UkwInfo = &ga.UserUKWInfo{
			Level:     yresp.GetUkwPersonInfo().GetLevel(),
			Medal:     yresp.GetUkwPersonInfo().GetMedal(),
			HeadFrame: yresp.GetUkwPersonInfo().GetHeadFrame(),
		}
		nobilityChannelMsg.UkwProfile = &ga.UserProfile{
			Uid:          uid,
			Account:      userInof.GetUsername(),
			Nickname:     userInof.GetNickname(),
			AccountAlias: userInof.GetAlias(),
			Sex:          uint32(userInof.GetSex()),
			Privilege: &ga.UserPrivilege{
				Account:  yresp.GetUkwPersonInfo().GetAccount(),
				Nickname: yresp.GetUkwPersonInfo().GetNickname(),
			},
		}
		if len(yresp.GetUkwPersonInfo().GetAccount()) > 0 {
			hresp, err := m.channelCli.GetChannelSimpleInfo(ctx, channelId, channelId)
			if nil != err {
				log.ErrorWithCtx(ctx, "onLevelUp GetChannelSimpleInfo channelId:%v err:%v", channelId, err)
			} else {
				channelType := channelPB.ChannelType(hresp.GetChannelType())
				if channelType == channelPB.ChannelType_RADIO_LIVE_CHANNEL_TYPE ||
					channelType == channelPB.ChannelType_OFFICIAL_LIVE_CHANNEL_TYPE ||
					channelType == channelPB.ChannelType_CPL_SUPER_CHANNEL_TYPE ||
					channelType == channelPB.ChannelType_GUILD_PUBLIC_FUN_CHANNEL_TYPE {
					nobilityChannelMsg.NickName = yresp.GetUkwPersonInfo().GetNickname()
					nobilityChannelMsg.Account = yresp.GetUkwPersonInfo().GetAccount()
				}
			}
		}
	}

	log.InfoWithCtx(ctx, "onLevelUp nobilityChannelMsg:%+v", nobilityChannelMsg)

	binMsg, _ := nobilityChannelMsg.Marshal()
	m.pushMsgToChannel(subCtx, channelId, uint32(channelPB.ChannelMsgType_NOBILITY_CHANNEL_MSG), "", binMsg)

	// clear old server redis cache
	// 推送c++等级变化kafka消息
	err = m.notifyNobilityClient(uid)
	if nil != err {
		log.ErrorWithCtx(ctx, "onLevelUp notifyNobilityClient uid:%v err:%v", uid, err)
		return err
	}

	return nil
}

// 降级到godlevel之下，清零连续延期次数 , 延长值 ， 从批量数据删除
func (m *Manager) onLevelDown(ctx context.Context, uid uint32) error {

	pipe := m.cache.RedisClient.Pipeline()
	pipe.Del(continuousKey(uid))
	pipe.Del(extentValKey(uid))
	pipe.Exec()

	//0等级之后，私聊重置, 低于9级关掉在线隐身权限
	flagMap := map[uint32]bool{}
	flagMap[uint32(pb.NobilitySwitch_E_ONLINE)] = false
	//flagMap[uint32(pb.NobilitySwitch_E_ALL)] = true

	flag, err := m.SetNobilitySwitchFlag(ctx, uid, flagMap)
	if nil != err {
		log.ErrorWithCtx(ctx, "onLevelDown SetNobilitySwitchFlag uid:%v flagMap:%+v err:%v", uid, flagMap, err)
		return err
	}

	// SetNobilitySwitchFlag接口已经发出kfk
	/*
		kfkEvent := &kafkanobility.NobilitySwitchFlagChange{
			Uid:        uid,
			SwtichFlag: 0,
		}
		bitMsg, err := proto.Marshal(kfkEvent)
		if nil != err {
			log.ErrorWithCtx(ctx, "onLevelDown Marshal err:%v", err)
			return err
		}
		if m.nobelProdV2 != nil {
			m.nobelProdV2.ProduceNobilityInfoChangeInfoV2(ctx, uid, bitMsg)
		}
	*/

	log.InfoWithCtx(ctx, "onLevelDown uid:%v flag:%v", uid, flag)

	return nil
}

// c_redis begin
func extentValKey(uid uint32) string {
	return fmt.Sprintf("nobility_god_extent_val_%v", uid)
}

func continuousKey(uid uint32) string {
	return fmt.Sprintf("nobility_continuous_%v", uid)
}

//c_redis end

func getGodLevelLeftDay(cycBeginTs int64) (int64, int64) {
	nowTs := time.Now()
	leftSec := cycBeginTs - nowTs.Unix() + conf.GetConf().CycleSec
	if leftSec <= 0 {
		return 0, 0
	}
	var extentDaysec = float64(conf.GetDaySec()) //每天多少秒
	leftDay := math.Floor(float64(leftSec) / extentDaysec)
	return int64(leftDay), leftSec
}

func (m *Manager) onExtentDay(ctx context.Context, uid, channelId, currLevel uint32, addDayNum, addCnt int64) error {

	addCycleTs := addDayNum * conf.GetDaySec()
	err := m.db.UpdateNobilityInfo(ctx, &mysql.NobilityUpdateInfo{
		Uid:        uid,
		AddCycleTs: addCycleTs,
	})
	if nil != err {
		log.ErrorWithCtx(ctx, "onExtentDay UpdateNobilityInfo uid:%v err:%v", uid, err)
		return err
	}

	info, err := m.db.GetNobilityInfo(uid)
	if nil != err {
		log.ErrorWithCtx(ctx, "onExtentDay GetNobilityInfo uid:%v err:%v", uid, err)
		return err
	}
	m.db.AddNobilityInfoChangeRecord(ctx, &info, addCycleTs, fmt.Sprintf("%v", addCnt))

	//修改神王主页飘时间
	m.sendUserDec(uid, info.CycleBeginTs)

	//发出延长kfk
	nowTs := time.Now()
	kfkEvent := &kafkanobility.NobilityExtentTimeEvent{
		Uid:      uid,
		Level:    uint32(info.NobilityLevel),
		Cnt:      uint32(addCnt),
		CreateAt: uint32(nowTs.Unix()),
	}
	bitMsg, perr := proto.Marshal(kfkEvent)
	if nil != perr {
		log.ErrorWithCtx(ctx, "onExtentDay proto Marshal uid:%v perr:%v", uid, perr)
		//return perr
	} else {
		err = m.nobelProdV2.ProduceNobilityInfoChangeInfoV2(ctx, uid, bitMsg)
		if nil != err {
			log.ErrorWithCtx(ctx, "SetNobilityOnlineSwitch ProduceNobilityInfoChangeInfoV2 uid:%v err:%v", uid, err)
		}
		log.InfoWithCtx(ctx, "ProduceNobilityInfoChangeInfoV2 uid:%v kfkEvent:%v", uid, kfkEvent)
	}

	currDayNum, _ := getGodLevelLeftDay(info.CycleBeginTs)
	tempMsg := fmt.Sprintf("恭喜你的【神王】贵族身份增加%v天有效期，当前共有%v天有效期，点此查看>", addDayNum, currDayNum)
	if addDayNum > conf.GetConf().AddExpireDayNum {
		rewardDay := conf.GetConf().RewardDayNum
		tempMsg = fmt.Sprintf("恭喜你的【神王】贵族身份增加%v天有效期，这是您第%v次延长有效期，获得额外%v天奖励，当前共有%v天有效期，点此查看>", addDayNum, addCnt, rewardDay, currDayNum)
	}
	err = m.apiClient.SendTTHelperMsgWithUrl(uid, tempMsg, "点此查看>", conf.GetConf().NobilityWebUrl)
	if nil != err {
		log.ErrorWithCtx(ctx, "onExtentDay SendTTHelperMsgWithUrl uid:%v err:%V", uid, err)
		return err
	}

	breakMsg, err := m.fillBreakingNew(uid, 0, channelId, currLevel, uint32(addCnt), nil, uint32(pushPb.CommBreakingNewsBaseOpt_CHANNEL_NOBILITY_EXTEND_TIME))
	if nil != err {
		log.ErrorWithCtx(ctx, "onExtentDay fillBreakingNew err:%v", err)
		return err
	}
	m.PushBreakingNewsV3(uid, channelId, breakMsg)

	m.clearNobilityCache(uid)

	err = m.notifyNobilityClient(uid)
	if nil != err {
		log.ErrorWithCtx(ctx, "onLevelDown notifyNobilityClient uid:%v err:%v", uid, err)
	}

	log.DebugfWithCtx(ctx, "onExtentDay uid:%v channelId:%v currLevel:%v addCnt:%v", uid, channelId, currLevel, addCnt)

	return nil
}

/*func (m *Manager) setVal(lkey, key string, val int64) error {
	isLock := m.cache.RedisClient.SetNX(lkey, 1, time.Second*3).Val()
	if false == isLock {
		return errors.New("稍后再试")
	}
	err := m.cache.RedisClient.Set(key, val, 0).Err()
	return err
}*/

func radiobutton(mapFlag map[uint32]bool) {
	isRadio := false
	for _, k := range SwitchKeyList {
		if mapFlag[uint32(k)] {
			isRadio = true
			break
		}
	}
	if !isRadio {
		return
	}
	for _, k := range SwitchKeyList {
		if !mapFlag[uint32(k)] {
			mapFlag[uint32(k)] = false
		}
	}
}

func (m *Manager) SetNobilitySwitchFlag(ctx context.Context, uid uint32, mapFlag map[uint32]bool) (uint32, error) {

	ok := mapFlag[uint32(pb.NobilitySwitch_E_ONLINE)]
	if ok {
		info, err := m.cache.GetNobilityInfo(ctx, uid)
		if nil != err {
			log.ErrorWithCtx(ctx, "SetNobilitySwitchFlag GetNobilityInfo uid:%v err:%v", uid, err)
			return 0, err
		}

		if info.Level < conf.GetConf().GodLevel {
			log.ErrorWithCtx(ctx, "SetNobilitySwitchFlag level check fail %v %v", uid, info.Level)
			return 0, errors.New("没有权限")
		}
	}

	//mysql
	oldFlag, err := m.cache.RedisClient.Get(switchFlagKey(uid)).Int64()
	if nil != err && err != redis.Nil {
		log.ErrorWithCtx(ctx, "SetNobilitySwitchFlag get fail uid:%v err:%v", uid, err)
		return 0, err
	}
	flag := uint32(oldFlag)

	//设置单选
	radiobutton(mapFlag)

	for k, v := range mapFlag {
		if v {
			flag = flag | k
		} else {
			flag = flag & (^k)
		}
	}

	err = m.cache.RedisClient.Set(switchFlagKey(uid), flag, 0).Err()
	if nil != err {
		log.ErrorWithCtx(ctx, "SetNobilityOnlineSwitch setVal err:%v", err)
		return flag, err
	}

	bitMsg, perr := proto.Marshal(&kafkanobility.NobilitySwitchFlagChange{
		Uid:        uid,
		SwtichFlag: flag,
	})
	if nil != perr {
		log.ErrorWithCtx(ctx, "onLevelDown Marshal err:%v", perr)
		return 0, perr
	}
	//发出kfk
	if m.nobelProdV2 != nil {
		err = m.nobelProdV2.ProduceNobilityInfoChangeInfoV2(ctx, uid, bitMsg)
		if nil != err {
			log.Errorf("SetNobilityOnlineSwitch ProduceNobilityInfoChangeInfoV2 uid:%v flag:%v err:%v", uid, flag, err)
		}
	}

	return flag, nil
}

func (m *Manager) NobilityChannelVisible(ctx context.Context, req *pb.NobilityChannelVisibleReq) error {
	flag, err := m.cache.RedisClient.Get(switchFlagKey(req.GetUid())).Int64()
	if nil != err && err != redis.Nil {
		log.ErrorWithCtx(ctx, "NobilityChannelVisible get fail uid:%v err:%v", req.GetUid(), err)
	}

	bitMsg, perr := proto.Marshal(&kafkanobility.NobilitySwitchFlagChange{
		Uid:        req.GetUid(),
		SwtichFlag: uint32(flag),
		Version:    1,
		Visible:    req.GetVisible(),
		NobLevel:   req.GetNobLevel(),
		ChannelId:  req.GetChannelId(),
	})
	if nil != perr {
		log.ErrorWithCtx(ctx, "NobilityChannelVisible Marshal req:%v err:%v", req, perr)
		return perr
	}

	if m.nobelProdV2 != nil {
		m.nobelProdV2.ProduceNobilityInfoChangeInfoV2(ctx, req.GetUid(), bitMsg)
	}

	err = m.cache.SetUserChannelInvisibleFlag(req.GetUid(), req.GetChannelId(), req.GetVisible())
	if nil != err {
		log.DebugWithCtx(ctx, "NobilityChannelVisible SetUserChannelInvisibleFlag err:%v", err)
	}

	log.InfoWithCtx(ctx, "NobilityChannelVisible req:%+v", req)
	return nil
}

func (m *Manager) GetChannelInvisibleFlag(ctx context.Context, req *pb.GetChannelInvisibleFlagReq) (*pb.GetChannelInvisibleFlagResp, error) {
	resp := &pb.GetChannelInvisibleFlagResp{}

	invisible, err := m.cache.GetUserChannelInvisibleFlag(req.GetUid(), req.GetCid())
	if nil != err {
		log.ErrorWithCtx(ctx, "GetChannelInvisibleFlag GetUserChannelInvisibleFlag err:%v", err)
		return resp, err
	}

	resp.Invisible = invisible

	log.DebugWithCtx(ctx, "GetChannelInvisibleFlag req:%+v", req)
	return resp, nil
}

func switchFlagKey(uid uint32) string {
	return fmt.Sprintf("nobility_switch_flag_%v", uid)
}

func (m *Manager) GetNobilitySwitchFlag(uidList []uint32) ([]uint32, error) {
	keys := []string{}
	for _, uid := range uidList {
		keys = append(keys, switchFlagKey(uid))
	}

	flagList := make([]uint32, 0, len(keys))
	flags, err := m.cache.RedisClient.MGet(keys...).Result()
	if nil != err {
		return flagList, err
	}
	for _, f := range flags {
		if f == nil {
			flagList = append(flagList, uint32(pb.NobilitySwitch_E_ALL)) //1 至少有个默认值
			continue
		}
		n := f.(string)
		num, _ := strconv.ParseUint(n, 10, 32)
		flagList = append(flagList, uint32(num))
	}

	return flagList, nil
}

func downNoticeKey(uid, day uint32) string {
	return fmt.Sprintf("godlevel_level_down_notice_%v_%v", uid, day)
}

func (m *Manager) ModifyNobilityValue(ctx context.Context, uid, val, waitCost, cycTs uint32) {
	level, _ := conf.GetNobilityLevel(int64(val), 0)
	if cycTs == 0 {
		cycTs = uint32(time.Now().Unix())
	}

	tbName := fmt.Sprintf("nobility_%02v", uid%100)
	k2v := make(map[string]interface{})
	k2v["nobility_value"] = val
	k2v["cycle_begin_ts"] = cycTs
	k2v["nobility_level"] = level
	k2v["wait_cost_value"] = waitCost
	k2v["total_wait_cost_value"] = waitCost
	k2v["keep_nobility_value"] = 0

	m.db.DB.Table(tbName).Create(&mysql.NobilityInfoStu{
		Uid:           uid,
		NobilityValue: int64(val),
		CycleBeginTs:  int64(cycTs),
	})
	m.db.DB.Table(tbName).Where("uid=?", uid).Updates(k2v)

	rechargeTable := fmt.Sprintf("user_consume_%02v", uid%100)
	consumerTable := fmt.Sprintf("user_charge_%02v", uid%100)
	query := fmt.Sprintf("delete from %v where uid=%v", rechargeTable, uid)
	m.db.DB.Exec(query)
	query = fmt.Sprintf("delete from %v where uid=%v", consumerTable, uid)
	m.db.DB.Exec(query)

	m.clearNobilityCache(uid)

	f := func(r *redis.Client) {
		r.HSet(nobilityLevelFlag, fmt.Sprintf("%v", uid), level)
		r.Del(fmt.Sprintf("needadd_%v", uid))
	}

	m.batchOperCache(f)

	nInfo := &pb.NobilityInfo{
		Value:   uint64(val),
		CycleTs: cycTs,
		Uid:     uid,
	}

	fkey := fmt.Sprintf("nobility_switch_flag_%v", uid)
	m.cache.RedisClient.Del(fkey)

	str, _ := proto.Marshal(nInfo)
	m.cache.RedisClient.Set(fmt.Sprintf("nobility_info_%v", uid), str, 0)

	keys := []string{}
	keys = append(keys, downNoticeKey(uid, 15))
	keys = append(keys, downNoticeKey(uid, 7))
	keys = append(keys, downNoticeKey(uid, 1))
	keys = append(keys, downNoticeKey(uid, 0))
	m.cache.RedisClient.Del(keys...)

	if level == 0 {
		kfkEvent := &kafkanobility.NobilitySwitchFlagChange{
			Uid:        uid,
			SwtichFlag: 0,
		}
		bitMsg, err := proto.Marshal(kfkEvent)
		if nil != err {
			log.Errorf("\n  ProduceNobilityInfoChangeInfoV2 proto.Marshal err:%v\n", err)
		} else {
			err = m.nobelProdV2.ProduceNobilityInfoChangeInfoV2(ctx, uid, bitMsg)
			if nil != err {
				log.Errorf("\n  ProduceNobilityInfoChangeInfoV2 err:%v\n", err)
			}
			log.InfoWithCtx(ctx, "ProduceNobilityInfoChangeInfoV2 uid:%v kfkEvent:%v", uid, kfkEvent)
		}

	}

	/*	err := m.nobelProdV1.ProduceNobilityInfoChangeInfoV1(&kafkanobility.NobilityInfoChange{
			Uid:           uid,
			Level:         uint32(level),
			NobilityValue: uint64(val),
			EventType:     0,
		})
		if nil != err {
			fmt.Printf("\n  ProduceNobilityInfoChangeInfoV1 err:%v\n", err)
		}*/

	if level < int64(conf.GetConf().GodLevel) {
		m.cache.RedisClient.Del(extentValKey(uid))
	}

	m.cache.SetNobilityInfoList(ctx, []pb.NobilityInfo{{
		Uid:   uid,
		Value: uint64(val),
	}})
}

func (m *Manager) Stop() {
	m.consumerTbeanSub.Stop()
	m.presentSub.Stop()
	m.nobelSubV1.Stop()
	m.eSportTradeSub.Stop()
	m.channelKfkSub.Stop()
}
