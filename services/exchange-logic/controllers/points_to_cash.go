package controllers

import (
	"context"
	"fmt"
	account2 "gitlab.ttyuyin.com/bizFund/bizFund/pkg/account"
	"golang.52tt.com/pkg/commission"
	"golang.52tt.com/pkg/tbean"
	"golang.52tt.com/pkg/web_token/beego"
	banuser "golang.52tt.com/protocol/services/banusersvr"
	"golang.52tt.com/protocol/services/esport_score"
	pb "golang.52tt.com/protocol/services/knightgroupscore"
	user_online "golang.52tt.com/protocol/services/user-online"
	userscore2 "golang.52tt.com/protocol/services/userscore"
	"golang.52tt.com/services/exchange-logic/conf"

	"time"

	"golang.52tt.com/clients/exchange"
	"golang.52tt.com/clients/session"
	"golang.52tt.com/clients/verifycode"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	authPB "golang.52tt.com/protocol/app/auth"
	"golang.52tt.com/protocol/common/status"
	exchangePB "golang.52tt.com/protocol/services/exchange"
	"golang.52tt.com/services/exchange-logic/models"
	apiPB "golang.52tt.com/services/exchange-logic/models/gen-go"
)

const (
	userScoreWithdrawOP     = uint32(authPB.E_SENSTIVE_VERIFYCODE_OP_TYPE_ENUM_SENSTIVE_OP_USER_SCORE_WITHDRAW)
	anchorScoreWithdrawOP   = uint32(authPB.E_SENSTIVE_VERIFYCODE_OP_TYPE_ENUM_SENSTIVE_OP_ANCHOR_SCORE_WITHDRAW)
	maskedPkScoreWithdrawOP = uint32(authPB.E_SENSTIVE_VERIFYCODE_OP_TYPE_ENUM_SENSTIVE_OP_MASKED_PK_SCORE_WITHDRAW)
	userScoreType           = uint32(0)
	anchorScoreType         = uint32(1)
	maskedPkScoreType       = uint32(2)
	knightScoreType         = uint32(3)
	eSportScoreType         = uint32(4)
	userScoreTbeanOnlyType  = uint32(5)
)

var (
	sessionClient = session.NewClient()
)

type PointsToCashController struct {
	APIController
}

func (c *PointsToCashController) Get() {
	authInfo := c.AuthInfo()

	ctx := c.Context()
	uid := authInfo.UserID
	pointType := c.GetUInt32("pointType", 0)
	log.InfoWithCtx(ctx, "PointsToCashController Get cash %+v, pointType=%d", authInfo, pointType)

	//检查登录限制
	bannedStatus, sErr := models.BanUserClient.GetBannedStatus(ctx, 0, uid, "", "", "")
	if sErr != nil {
		log.ErrorWithCtx(ctx, "models.BanUserClient.GetBannedStatus=%v", sErr)
	}
	if bannedStatus.GetStatus() == uint32(banuser.BAN_STATUS_BAN_ST_BANNED) {
		log.InfoWithCtx(ctx, "BanUserClient.GetBannedStatus %d is banned")
		c.ServeAPIJsonWithError(status.ErrExchangeCannotCash, "账号异常，请联系客服")
		return
	}
	log.InfoWithCtx(ctx, "BanUserClient.GetBannedStatus %d not banned", uid)

	//内部账号
	if account2.IsInternalUid(ctx, uid) {
		whiteExpire, err := models.ExchangeClient.CheckInnerWhite(ctx, uid)
		if err != nil {
			log.ErrorWithCtx(ctx, "ExchangeClient.CheckInnerWhite err=%v", err)
			return
		}
		if whiteExpire == 0 {
			c.ServeAPIJsonWithError(status.ErrExchangeCannotCash, "提现异常，请检查您的账号")
			return
		}
	}

	cash := c.GetFloat64("cash", 0.0)
	points := uint32(cash * 100)
	if points > 0 {
		//货币组黑名单V2
		blackUserV2, err := models.TBeanClient.CheckBlackUserV2(ctx, uid, []uint32{tbean.DataTypeWithdraw})
		if err != nil {
			sErr := protocol.ToServerError(err)
			c.ServeAPIJsonWithError(sErr.Code(), sErr.Message())
			return
		}

		for _, scene := range blackUserV2 {
			if scene == tbean.DataSceneMinorsRecharge {
				c.ServeAPIJsonWithError(status.ErrExchangeCannotCash, "暂不可提现积分，请前往个人主页右上角-我的客服-功能申诉申请解除")
				return
			}
		}

		if len(blackUserV2) > 0 {
			c.ServeAPIJsonWithError(status.ErrExchangeCannotCash, "暂不可提现积分，请联系客服")
			return
		}
	}

	if pointType == userScoreType || pointType == userScoreTbeanOnlyType {
		//历史1万
		can, err := models.ExchangeClient.GetScoreCanWithdraw(ctx, uid)
		if err != nil {
			serverError := protocol.ToServerError(err)
			log.ErrorWithCtx(ctx, "ExchangeClient.GetScoreCanWithdraw err=%v", serverError)
			c.ServeAPIJsonWithError(serverError.Code(), serverError.Message())
			return
		}

		if !can {
			log.InfoWithCtx(ctx, "ExchangeClient.GetScoreCanWithdraw can=%v uid=%d", can, uid)
			c.ServeAPIJsonWithError(status.ErrExchangeCannotCash, "积分不足，再去攒攒~")
			return
		}

		//身份判断
		uidList := []uint32{uid}

		typeMap, err := models.ScoreTypeMgr.GetScoreTypeMap(ctx, 0, 0, uidList)
		if err != nil {
			log.ErrorWithCtx(ctx, "ScoreTypeMgr.GetScoreTypeMap err=%v", err)
			sErr = protocol.ToServerError(err)
			c.ServeAPIJsonWithError(sErr.Code(), sErr.Error())
			return
		}

		//身份不对
		if typeMap[uid] > 0 {
			log.InfoWithCtx(ctx, "uid=%d score_type>0", uid)
			c.ServeAPIJsonWithError(status.ErrExchangeSignErr, "")
			return
		}

		if pointType == userScoreTbeanOnlyType {
			//超过了可以提现二类积分的时间，则检查白名单
			if conf.XGuildConfig.ScoreTypeTime < time.Now().Unix() {
				can = false
				//可以提现二类积分的白名单
				groupResp, serverError := models.IopClient.MatchGroup(ctx, []uint64{uint64(uid)}, []string{conf.XGuildConfig.TbeanOnlyWithdrawGroupId})
				if serverError != nil {
					log.ErrorWithCtx(ctx, "IopClient.MatchGroup err=%v", serverError)
					c.ServeAPIJsonWithError(serverError.Code(), serverError.Message())
					return
				}
				if len(groupResp) > 0 {
					for id, data := range groupResp[0].Result {
						if id == conf.XGuildConfig.TbeanOnlyWithdrawGroupId && data == "1" {
							can = true
						}
					}
				}

				if !can {
					log.InfoWithCtx(ctx, "conf.XGuildConfig.ScoreTypeTime(%d) > time.Now().Unix()(%d) and not in whitelist", conf.XGuildConfig.ScoreTypeTime, time.Now().Unix())
					c.ServeAPIJsonWithError(status.ErrExchangeCannotCash, "积分不足，再去攒攒~")
					return
				}
			}
		}
	}

	// 检查黑名单限制时间
	inBlacklist, err := models.CheckUserIfInBlacklistV2(ctx, uid, uint32(exchangePB.AddBlacklistReq_PointToCash))
	if err != nil || inBlacklist {
		log.ErrorWithCtx(ctx, "PointsToCashController fail. CheckUserIfInBlacklistV2 uid(%d) err:%v", uid, err)
		c.ServeAPIJsonWithError(status.ErrExchangeCannotCash, "暂不可提现积分，请联系客服")
		return
	}

	isEveryDay := conf.UidInEveryDay(uid)

	commissionCli := GetCommissionClient(pointType)
	if commissionCli == nil {
		log.ErrorWithCtx(ctx, "PointsExchange bad pointType uid(%d)", uid)
		c.ServeAPIJsonWithError(status.ErrExchangeParamErr)
		return
	}

	//检查银行卡是否异常
	err = commissionCli.CheckEncashBlock(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "CommissionClient.CheckEncashBlock uid(%d) %+v", uid, err)

		if e, ok := err.(commission.APIError); ok && e.Code() == commission.CodeErrorBindCard {
			c.ServeAPIJsonWithError(status.ErrCommissionUserBankInfoError)
			return
		}

		c.ServeAPIJsonWithError(status.ErrCommissionApiRespError, err.Error())
		return
	}

	IdInfo, err := commissionCli.GetIdentifyInfo(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "CommissionClient.GetIdentifyInfo uid(%d) %+v", uid, err)
		c.ServeAPIJsonWithError(status.ErrCommissionApiRespError, err.Error())
		return
	}

	if IdInfo.BankId == 0 {
		c.ServeAPIJsonWithError(status.ErrExchangeParamErr, "请先绑定银行卡")
		return
	}

	if isEveryDay {
		c.cashByDay(authInfo)
	} else if checkIfCanCashByWeek(pointType) {
		c.cashByWeek(authInfo, pointType)
	} else { //蒙面PK
		c.cashByMonth(authInfo)
	}

}

func (c *PointsToCashController) cashByWeek(authInfo *beegoExt.AuthInfo, pointType uint32) bool {
	ctx := c.Context()
	uid := authInfo.UserID

	log.DebugfWithCtx(ctx, "cashByWeek uid(%d)", uid)

	timeNow := time.Now()
	_, _, timeDay := timeNow.Date()

	//检查时间是否符合
	if !checkTodayCanCashByWeekend(ctx) {
		log.ErrorWithCtx(ctx, "PointsExchange failed today %d not allow exchange", timeDay)
		c.ServeAPIJsonWithError(status.ErrExchangeWeekEndCash)

		return false
	}

	year, week := timeNow.ISOWeek()
	orderId := fmt.Sprintf("%d_w_%d_%d_encash_%d", year, week, uid, pointType) //该orderid限制每周兑换一次
	billDate := fmt.Sprintf("%d-w-%d-%d", year, week, pointType)

	c.requestFinanceCash(ctx, orderId, authInfo, billDate)
	return true
}

func (c *PointsToCashController) cashByMonth(authInfo *beegoExt.AuthInfo) {
	ctx := c.Context()
	uid := authInfo.UserID
	//pointType := c.GetUInt32("pointType", 0)
	log.DebugfWithCtx(ctx, "cashByMonth uid(%d)", uid)

	timeNow := time.Now()
	_, _, timeDay := timeNow.Date()
	if !checkTodayCanCashByMonth() {
		log.ErrorWithCtx(ctx, "PointsExchange failed today %d not allow exchange", timeDay)
		c.ServeAPIJsonWithError(status.ErrExchange2327Cash)
		return
	}

	orderId := fmt.Sprintf("%d_%s_encash", uid, timeNow.Format("2006-01")) //该orderid限制每月兑换一次
	billDate := time.Now().Format("2006-01")

	c.requestFinanceCash(ctx, orderId, authInfo, billDate)

}

func (c *PointsToCashController) cashByDay(authInfo *beegoExt.AuthInfo) {
	ctx := c.Context()
	uid := authInfo.UserID
	//pointType := c.GetUInt32("pointType", 0)
	log.DebugfWithCtx(ctx, "cashByDay uid(%d)", uid)

	timeNow := time.Now()

	orderId := fmt.Sprintf("%d_%d_encash", uid, timeNow.Unix())
	billDate := time.Now().Format("2006-01-02 15:04:05")

	c.requestFinanceCash(ctx, orderId, authInfo, billDate)

}

func (c *PointsToCashController) requestFinanceCash(ctx context.Context, orderId string, authInfo *beegoExt.AuthInfo, billDate string) {

	realNameResp, err := models.RealNameClient.GetUserRealNameAuthInfo(c.Context(), authInfo.UserID)
	if err != nil {
		log.ErrorWithCtx(ctx, "PointsExchange GetUserRealNameAuthInfo failed %+v", err)
		c.ServeAPIJsonWithError(err.Code())
		return
	}

	if authPB.E_REALNAME_AUTH_STATUS(realNameResp.Status) == authPB.E_REALNAME_AUTH_STATUS_ENUM_REALNAME_AUTH_PASS ||
		authPB.E_REALNAME_AUTH_STATUS(realNameResp.Status) == authPB.E_REALNAME_AUTH_STATUS_ENUM_REALNAME_AUTH_UNVALID {
		log.DebugfWithCtx(ctx, "PointsExchange RealNameAuthInfo ok status(%d) ", realNameResp.Status)
	} else {
		log.ErrorWithCtx(ctx, "PointsExchange GetUserRealNameAuthInfo status(%d) %+v", realNameResp.Status, err)
		c.ServeAPIJsonWithError(status.ErrExchangeNotRealName)
		return
	}

	uid := authInfo.UserID

	if !checkIfUidInNonageList(uid) {
		//不在未成年 通过人工审核列表
		if realNameResp.RealnameInfo.Age < 18 {
			log.ErrorWithCtx(ctx, "PointsExchange GetUserRealNameAuthInfo target User age less than 16 %+v", realNameResp.RealnameInfo)
			c.ServeAPIJsonWithError(status.ErrExchangeNotAge18Cash)
			return
		}
	}

	//check points
	cash := c.GetFloat64("cash", 0.0)
	pointType := c.GetUInt32("pointType", 0)
	points := uint32(cash * 100)
	var pointsCurr uint32

	switch pointType {
	case userScoreType:
		//get user score
		pointsCurr, err = userScoreGoClient.GetUserScore(ctx, uid, uint32(userscore2.ScoreType_Origin))
		if err != nil {
			log.ErrorWithCtx(ctx, "PointsExchange getUserScore failed: %+v", err)
			c.ServeAPIJsonWithError(err.Code(), err.Message())
			return
		}

	case userScoreTbeanOnlyType:
		//get user score
		pointsCurr, err = userScoreGoClient.GetUserScore(ctx, uid, uint32(userscore2.ScoreType_TbeanOnly))
		if err != nil {
			log.ErrorWithCtx(ctx, "PointsExchange getUserScore failed: %+v", err)
			c.ServeAPIJsonWithError(err.Code(), err.Message())
			return
		}

	case anchorScoreType:
		// get anchor score
		resp, err := models.ChannelLiveMgrClient.GetChannelLiveAnchorScore(ctx, uid)
		if err != nil {
			log.ErrorWithCtx(ctx, "PointsExchange GetChannelLiveAnchorScore failed: %+v", err)
			c.ServeAPIJsonWithError(err.Code(), err.Message())
			return
		}
		pointsCurr = resp.Score
		// 区分开用户积分提现和直播奖励积分提现的orderID
		orderId = "anchor_" + orderId

	case maskedPkScoreType:
		//get masked-pk score
		pointsCurr, err = models.MaskedPkScoreClient.GetUserMaskPkScore(ctx, uid)
		if err != nil {
			log.ErrorWithCtx(ctx, "PointsExchange GetUserMaskPkScore failed: %+v", err)
			c.ServeAPIJsonWithError(err.Code(), err.Message())
			return
		}
		// 区分开用户积分提现和直播奖励积分提现的orderID
		orderId = "masked_pk_" + orderId

	case knightScoreType:
		req := &pb.GetKnightScoreReq{
			OwnerId: uid,
		}
		scoreResp, serverError := models.KnightClient.GetKnightScore(ctx, req)
		if serverError != nil {
			log.ErrorWithCtx(ctx, "PointsExchange GetKnightScore failed: %+v", serverError)
			c.ServeAPIJsonWithError(serverError.Code(), serverError.Message())
			return
		}
		pointsCurr = uint32(scoreResp.GetScore())
		orderId = "knight_" + orderId

	case eSportScoreType:
		req := &esport_score.GetEsportScoreReq{
			Uid: uid,
		}
		scoreResp, err := models.ESportClient.GetEsportScore(ctx, req)
		if err != nil {
			log.ErrorWithCtx(ctx, "PointsExchange GetEsportScore failed: %+v", err)
			c.ServeAPIJsonWithError(-2, err.Error())
			return
		}
		pointsCurr = uint32(scoreResp.GetScore())
		orderId = "esport_" + orderId

	default:
		log.ErrorWithCtx(ctx, "PointsExchange failed bad pointType")
		c.ServeAPIJsonWithError(status.ErrExchangeParamErr)
		return
	}

	if points > pointsCurr {
		log.ErrorWithCtx(ctx, "PointsExchange failed points is not enough points %d userpoints %d", points, pointsCurr)
		c.ServeAPIJsonWithError(status.ErrExchangePointNotEnough)
		return
	}

	//just for check
	if points == 0 {
		//log.Error(ctx, "PointsExchange points %d check process", points)
		c.ServeAPIJsonWithError(0, "OK")
		return
	}

	//exchange
	if points > 0 {
		if pointType != maskedPkScoreType && points < 10000 {
			//窗口期后才对礼物积分开启判断

			if pointType != userScoreType && pointType != userScoreTbeanOnlyType {
				log.ErrorWithCtx(ctx, "PointsExchange failed points %d less than 10000", points)
				c.ServeAPIJsonWithError(status.ErrExchangeMaskPk10000)
				return
			}

			if time.Now().Unix() > conf.XGuildConfig.ScoreTypeTime {

				isWhite := false
				//可以提现二类积分的白名单
				groupResp, serverError := models.IopClient.MatchGroup(ctx, []uint64{uint64(uid)}, []string{conf.XGuildConfig.TbeanOnlyWithdrawGroupId})
				if serverError != nil {
					log.ErrorWithCtx(ctx, "IopClient.MatchGroup err=%v", serverError)
					c.ServeAPIJsonWithError(serverError.Code(), serverError.Message())
					return
				}
				if len(groupResp) > 0 {
					for id, data := range groupResp[0].Result {
						if id == conf.XGuildConfig.TbeanOnlyWithdrawGroupId && data == "1" {
							isWhite = true
						}
					}
				}

				if !isWhite {
					log.ErrorWithCtx(ctx, "PointsExchange failed points %d less than 10000", points)
					c.ServeAPIJsonWithError(status.ErrExchangeMaskPk10000)
					return
				}
			}
		}

		//check verify code
		if genVerifyResult := c.checkVerifyCode(ctx, uid, 0, authInfo.Session); genVerifyResult.GetCode() != 0 {
			if genVerifyResult.GetCode() == -2 {
				log.ErrorWithCtx(ctx, "PointsExchange checkVerifyCode fail")
			}
			c.VerifyCodeErrResponse(genVerifyResult)
			return
		}

		//if pointType == anchorScoreType {
		//	// 区分开用户积分提现和直播奖励积分提现的orderID
		//	orderId = "anchor_" + orderId
		//}

		//submit
		if _, err := c.beginTx(uid, points, pointType, orderId, billDate); err != nil {
			log.ErrorWithCtx(ctx, "PointsExchange BeginTransaction %d %d %d %s failed: %+v", uid, points, pointType, orderId, err)
			c.ServeAPIJsonWithError(err.Code(), err.Message())
			return
		}

		log.InfoWithCtx(ctx, "point to cash uid=%d, points=%d, pointType=%d, orderId=%s, billDate=%s", uid, points, pointType, orderId, billDate)

		WithdrawStatus := (uint32)(1)
		pointsInfo := &apiPB.PointsToCashInfo{
			Income:         0,
			Points:         pointsCurr - points,
			WithdrawStatus: WithdrawStatus,
		}

		commissionCli := GetCommissionClient(pointType)
		if commissionCli == nil {
			log.ErrorWithCtx(ctx, "PointsExchange bad pointType uid(%d)", uid)
			c.ServeAPIJsonWithError(status.ErrExchangeParamErr)
			return
		}

		//get encashment log
		encashmentLog, logErr := commissionCli.GetEncashment(ctx, uid)
		if logErr != nil {
			log.ErrorWithCtx(ctx, "PointsExchange GetEncashment failed")
			c.ServeAPIJsonWithError(status.ErrCommissionApiRespError)
			return
		}

		drawingRecord := &apiPB.PointsToCashRecordResult{
			List:  make([]*apiPB.PointsToCashRecord, 0, len(encashmentLog.Rows)),
			Total: encashmentLog.Total,
		}

		for _, row := range encashmentLog.Rows {
			tm2, _ := time.Parse("2006-01-02 15:04:05", row.CreateTime)
			record := &apiPB.PointsToCashRecord{
				Id:       row.OrderId,
				BankName: row.BankName,
				CardNo:   row.BankCard,
				Income:   row.Amount,
				Status:   row.Status,
				Time:     (uint32)(tm2.Unix() - 3600*8),
				Remark:   row.Remark,
			}
			drawingRecord.List = append(drawingRecord.List, record)
			break
		}

		result := &apiPB.PointsToCashResp{
			PointsInfo:    pointsInfo,
			DrawingRecord: drawingRecord,
		}
		c.ServeAPIJson(result)
	}
}

func (c *PointsToCashController) Post() {
}

func (c *PointsToCashController) beginTx(uid, points, pointType uint32, orderID, currTime string) (*exchangePB.Transaction, protocol.ServerError) {
	cash := points
	sourceType := Exchange.Points
	orderDesc := "积分提现"
	pushContentFormat := "【提现通知】提现申请提交成功，已消耗积分%d。%s"

	switch pointType {
	case anchorScoreType:
		sourceType = Exchange.AnchorPoints
		orderDesc = "奖励积分提现"
		pushContentFormat = "【提现通知】提现申请提交成功，已消耗奖励积分%d。%s"
	case maskedPkScoreType:
		sourceType = Exchange.MaskedPkPoints
		orderDesc = "蒙面pk积分提现"
		pushContentFormat = "【提现通知】提现申请提交成功，已消耗蒙面pk积分%d。%s"
	case knightScoreType:
		sourceType = Exchange.KnightPoints
		orderDesc = "骑士积分提现"
		pushContentFormat = "【提现通知】提现申请提交成功，已消耗骑士积分%d。%s"
	case eSportScoreType:
		sourceType = exchangePB.CurrencyType_ESPORT_POINTS
		orderDesc = "电竞积分提现"
		pushContentFormat = "【提现通知】提现申请提交成功，已消耗电竞积分%d。%s"
	case userScoreTbeanOnlyType:
		sourceType = exchangePB.CurrencyType_POINTS_TBEAN_ONLY
	}

	//currTime := time.Now().Format("2006-01")
	tx, err := models.ExchangeClient.BeginTransactionEx(c.Context(), &exchangePB.Transaction{
		OrderId:      orderID,
		Uid:          uid,
		TargetType:   Exchange.Commission,
		TargetAmount: cash,
		BonusAmount:  0,
		SourceType:   sourceType,
		SourceAmount: points,
		UserData:     []byte(currTime),
		OrderDesc:    orderDesc,
		FinishMessage: &exchangePB.PushMessage{
			Content: fmt.Sprintf(pushContentFormat, points, time.Now().Format("01月02日 15:04:05")),
		},
	})

	return tx, err
}

func (c *PointsToCashController) VerifyCodeErrResponse(r *apiPB.VerifyCodeResp) {
	cb := c.Ctx.Input.Query("callback")
	if len(cb) > 0 {
		c.ServePB2JSONP(r)
	} else {
		c.ServePB2JSON(r)
	}

	if r.Code != 0 {
		c.StopRun()
	}
}

func (c *PointsToCashController) checkVerifyCode(ctx context.Context, uid, appId uint32, sessionKey string) *apiPB.VerifyCodeResp {
	errRet := &apiPB.VerifyCodeResp{
		Cooldown: 0,
		Code:     -2,
		Msg:      "系统繁忙",
	}

	successRet := &apiPB.VerifyCodeResp{
		Cooldown: 0,
		Code:     0,
		Msg:      "OK",
	}

	//获取是否开启验证码功能 全局开关，会返回true
	inUse, err := verifyCodeClient.CheckVerifyCodeInUse(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "checkVerifyCode CheckVerifyCodeInUse failed")
		return errRet
	}

	if !inUse {
		return successRet
	}

	if sessionKey == "" {
		onlineInfo, err := models.UserOnlineClient.GetLatestOnlineInfo(ctx, uid)
		if err != nil {
			log.ErrorWithCtx(ctx, "UserOnlineClient.GetLatestOnlineInfo %d err=%v", uid, err)
		}

		if onlineInfo.GetOnlineType() == user_online.OnlineType_ONLINE_TYPE_ONLINE {
			appId = uint32(protocol.NewTerminalType(onlineInfo.GetTerminalType()).AppID())
		}

		sessionKey, err = sessionClient.GetSession(ctx, uid, appId)
		if err != nil {
			log.ErrorWithCtx(ctx, "checkVerifyCode GetSessionEx %d %d failed: %v", uid, appId, err)
			return errRet
		}
	}

	pointType := c.GetUInt32("pointType", 0)

	opType := userScoreWithdrawOP
	if pointType == anchorScoreType {
		opType = anchorScoreWithdrawOP
	} else if pointType == maskedPkScoreType {
		opType = maskedPkScoreWithdrawOP
	} else {
		
	}

	//检查是否能需要进行验证操作
	checkverifyResp, err := verifyCodeClient.CheckVerifyCodeStatus(ctx, uid, opType, sessionKey)
	if err != nil {
		log.ErrorWithCtx(ctx, "checkVerifyCode CheckValid %d %s failed: %v", uid, sessionKey, err)
		return errRet
	}

	verifyResult := verifycode.CheckResult(checkverifyResp.GetResult())
	log.DebugfWithCtx(ctx, "verifycode client.CheckValid %d %s result: %v", uid, sessionKey, verifyResult)

	var Cooldown uint32
	switch verifyResult {
	case verifycode.OK, verifycode.Pass:
		return successRet

	case verifycode.SessionChanged, verifycode.NotExists, verifycode.NotVerified:
		accountResp, err := accountClient.GetUser(ctx, uid)
		if err != nil {
			log.ErrorWithCtx(ctx, "checkVerifyCode GetUser %d failed: %v", uid, err)
			return errRet
		}

		if accountResp.GetPhone() == "" {
			errRet := &apiPB.VerifyCodeResp{
				Cooldown: 0,
				Code:     status.ErrNoPhoneBind,
				Msg:      "提现操作需要绑定手机",
			}
			return errRet
		}

		if verifyResult == verifycode.NotVerified {
			Cooldown = checkverifyResp.GetCooldown()
		} /*else {
			Cooldown = 0
		}*/

		errRet := &apiPB.VerifyCodeResp{
			Cooldown: Cooldown,
			Code:     status.ErrVerifycodeForSensitiveOp,
			Msg:      "提现操作需要手机短信确认,请按提示操作。",
		}
		return errRet
	default:
		return errRet
	}

}

func checkTodayCanCashByWeekend(ctx context.Context) bool {

	today := time.Now()

	//周六
	if today.Weekday() == time.Saturday {
		return true
	}

	//周日
	if today.Weekday() == time.Sunday {

		if today.Day() == 1 {
			log.DebugfWithCtx(ctx, "sunday but 1 of month")
			return false
		}
		return true
	}

	return false
}

func checkIfCanCashByWeek(pointType uint32) bool {
	if pointType == userScoreType || pointType == anchorScoreType || pointType == knightScoreType || pointType == eSportScoreType || pointType == userScoreTbeanOnlyType {
		return true
	}

	return false
}

func checkTodayCanCashByMonth() bool {
	_, _, timeDay := time.Now().Date()
	return timeDay >= 1 && timeDay <= 3
}
