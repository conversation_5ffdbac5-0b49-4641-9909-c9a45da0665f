package setting

import (
	"github.com/go-redis/redis"
	"golang.52tt.com/services/smash-egg/kernel/utils/cache"
)

const systemConfigKey = "smash_egg_system_config"

type Cache struct {
	*cache.Cache

	cmder redis.Cmdable
}

func NewCache(client *redis.Client) *Cache {
	return &Cache{
		Cache: cache.NewCache(client),
		cmder: client,
	}
}

func (s *Cache) SetSystemConfig(field string, value interface{}) (bool, error) {
	return s.SetSubConfig(systemConfigKey, field, value)
}

func (s *Cache) GetSystemConfig(field string) (exist bool, data string, err error) {
	return s.GetSubConfig(systemConfigKey, field)
}

func (s *Cache) DelSystemConfig(field string) (bool, error) {
	return s.DelSubConfig(systemConfigKey, field)
}
