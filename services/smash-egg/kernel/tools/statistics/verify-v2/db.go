package main

import (
	"context"
	"errors"
	"fmt"
	"github.com/jmoiron/sqlx"
	"golang.52tt.com/pkg/config"
	"push/pkg/log"
	"sync"
	"time"

	_ "github.com/go-sql-driver/mysql" // MySQL驱动。
)

type Dao struct {
	db         *sqlx.DB
	darkGiftDb *sqlx.DB
}

func newDao(cfg, darkGiftCfg *config.MysqlConfig) (*Dao, error) {
	db, err := sqlx.Connect("mysql", cfg.ConnectionString())
	if err != nil {
		log.Errorf("failed to connect mysql %v", err)
		return nil, err
	}

	darkGiftDb, err := sqlx.Connect("mysql", darkGiftCfg.ConnectionString())
	if err != nil {
		log.Errorf("failed to connect mysql %v", err)
		return nil, err
	}

	s := &Dao{
		db:         db,
		darkGiftDb: darkGiftDb,
	}

	log.Infof("newDao success")
	return s, nil
}

const timeLayout = "2006-01-02 15:04:05"
const batchCountLimit = 600000

type rechargeData struct {
	Id  int64 `db:"id"`
	Uid int64 `db:"uid"`
	Num int64 `db:"num"`
}

func (s *Dao) GetRechargeData(ctx context.Context, begin, end time.Time) (recharges []rechargeData, err error) {
	//const tpl = `
	//	SELECT
	//	  uid, IFNULL (SUM(amount), 0)*200 AS num
	//	FROM smash_egg_month_consume_record_%02d_%d WHERE create_time BETWEEN '%s' AND '%s' group by uid`

	const tpl = `
		SELECT
		 IFNULL (max(id),0) as id,IFNULL (SUM(amount), 0) AS num
		FROM smash_egg_month_consume_record_%s WHERE id>%d and id<%d`

	bm := int(begin.Month())
	em := int(end.Month())

	for i := bm; i <= em; i++ {
		offset := int64(0)
		for j := 0; j < 20; j++ {
			tmp := make([]rechargeData, 0)

			query := fmt.Sprintf(tpl, genConsumeTableSuffixV2(begin.Year(), i), offset, offset+batchCountLimit)
			log.Infof("sql:%s\n", query)

			if err = s.db.SelectContext(ctx, &tmp, query); nil != err {
				if err.Error() != "sql: no rows in result set" {
					fmt.Printf("db SelectContext fail, err: %v\n", err)
					return
				}
			}
			recharges = append(recharges, tmp...)
			if len(tmp) == 0 || tmp[0].Id == 0 {
				log.Infof("done for this table")
				break
			}
			//获取当前最大的id
			maxId := offset
			for t := 0; t < len(tmp); t++ {
				if tmp[t].Id > maxId {
					maxId = tmp[t].Id
				}
			}

			offset = maxId
			log.Infof("new offset:%d", offset)
		}
	}
	return
}

func genConsumeTableSuffixV2(year, month int) string {
	return fmt.Sprintf("%04d%02d", year, month)
}

type winningData struct {
	Id   int64 `db:"id"`
	Pack int64 `db:"pack"`
	Mode int64 `db:"mode"`
	Num  int64 `db:"num"`
}

func (s *Dao) GetWinningData(ctx context.Context, begin, end time.Time) (winnings []winningData, err error) {
	log.Infof("GetWinningData inln")
	const tpl = `
				SELECT 
					IFNULL (max(id),0) as id, pack_id AS pack, mode, count(pack_id) AS num 
				FROM smash_egg_winning_record_%s WHERE id>%d and id<%d AND source<2 group by pack_id, mode;` // 每次统计60w数据，一般最多10次能查完

	bm := int(begin.Month())
	em := int(end.Month())

	for m := bm; m <= em; m++ {
		for i := 0; i < 10; i++ {
			offset := int64(0)

			for j := 0; j < 20; j++ {
				tmp := make([]winningData, 0)
				query := fmt.Sprintf(tpl, genWinningTableSuffixV2(i, begin.Year(), m), offset, offset+batchCountLimit)
				log.Infof("sql:%s\n", query)

				//winningOnce.Do(func() {
				//	log.Infof("sql:%s\n", query)
				//})

				if err = s.db.SelectContext(ctx, &tmp, query); nil != err {
					if err.Error() != "sql: no rows in result set" {
						log.Errorf("db SelectContext fail, err: %v\n", err)
						return
					}
				}
				winnings = append(winnings, tmp...)
				tmpLen := len(tmp)
				if tmpLen == 0 {
					log.Infof("done for this table")
					break
				}
				//获取当前最大的id
				maxId := offset
				for t := 0; t < tmpLen; t++ {
					if tmp[t].Id > maxId {
						maxId = tmp[t].Id
					}
				}

				offset = maxId
				log.Infof("new offset:%d", offset)
			}
		}
	}
	return winnings, nil
}

func genWinningTableSuffixV2(uid, year, month int) string {
	return fmt.Sprintf("%04d%02d_%02d", year, month, uid%10)
}

type DarkGiftBonusSummary struct {
	GiftId     uint32 `db:"gift_id"`
	GiftName   string `db:"gift_name"`
	TotalCnt   int64  `db:"total_cnt"`
	TotalPrice int64  `db:"total_price"`
}

func (s *Dao) GetDarkGiftBonusSummary(ctx context.Context, begin, end time.Time) (winnings []DarkGiftBonusSummary, err error) {

	const tpl = `
				SELECT 
					gift_id, gift_name, sum(gift_amount) as total_cnt, sum(gift_total_price) as total_price 
				FROM tbl_dark_gift_bonus_log_%04d%02d WHERE create_time BETWEEN '%s' AND '%s' AND business_source=1 group by gift_id, gift_name;`

	bm := int(begin.Month())
	em := int(end.Month())

	for m := bm; m <= em; m++ {
		tmp := make([]DarkGiftBonusSummary, 0)
		query := fmt.Sprintf(tpl, begin.Year(), m, begin.Format(timeLayout), end.Format(timeLayout))

		log.Infof("sql:%s", query)

		if err = s.darkGiftDb.SelectContext(ctx, &tmp, query); nil != err {
			if err.Error() != "sql: no rows in result set" {
				log.Errorf("db SelectContext fail, err: %v\n", err)
				return
			}
		}
		winnings = append(winnings, tmp...)
	}
	return winnings, nil
}

type chanceData struct {
	Uid    int64 `db:"uid"`
	Amount int64 `db:"amount"`

	ModifyTime time.Time `db:"modify_time"`
}

// 获取修改时间大于end或者数量大于0的用户剩余信息
func (s *Dao) getChance(ctx context.Context, start time.Time) (chances []chanceData, err error) {
	const tpl = `
				SELECT 
					uid, amount, modify_time 
				FROM smash_egg_remain_chance WHERE modify_time >= '%s' OR amount > 0`

	query := fmt.Sprintf(tpl, start.Format(timeLayout))

	log.Infof("sql:%s", query)

	tmp := make([]chanceData, 0)
	if err = s.db.SelectContext(ctx, &tmp, query); nil != err {
		if err.Error() != "sql: no rows in result set" {
			log.Errorf("db SelectContext fail, err: %v", err)
			return
		}
	}
	chances = tmp
	return chances, nil
}

type firstData struct {
	Uid        int64     `db:"uid"`
	Source     int       `db:"source"`
	OrderId    string    `db:"operate_id"`
	CreateTime time.Time `db:"create_time"`
	PackId     int64     `db:"pack_id"`
}

var firstOnce sync.Once

func (s *Dao) getFirstData(ctx context.Context, chance chanceData, start time.Time) (data firstData, ok bool, err error) {
	const tpl = `SELECT source,operate_id,create_time,pack_id FROM smash_egg_winning_record_%s WHERE uid=%d AND create_time >= '%s' order by id limit 1`

	query := fmt.Sprintf(tpl, genWinningTableSuffixV2(int(chance.Uid), start.Year(), int(start.Month())), chance.Uid, start.Format(timeLayout))

	firstOnce.Do(func() {
		log.Infof("sql:%s", query)
	})

	if err = s.db.GetContext(ctx, &data, query); nil != err {
		if err.Error() != "sql: no rows in result set" {
			log.Errorf("db GetContext fail, sql: %s, err : %v", query, err)
			return
		}
	} else {
		ok = true
	}
	data.Uid = chance.Uid
	return data, ok, nil
}

var firstPlayOnce sync.Once

func (s *Dao) getChanceByFirstPlay(ctx context.Context, first firstData) (data chanceData, ok bool, err error) {

	type playData struct {
		Amount int64 `db:"amount"`
		Mode   int64 `db:"mode"`
	}

	const tpl = `SELECT remain_chance as amount, mode FROM smash_egg_winning_record_%s WHERE uid=%d AND operate_id='%s'`

	query := fmt.Sprintf(tpl, genWinningTableSuffixV2(int(first.Uid), first.CreateTime.Year(), int(first.CreateTime.Month())), first.Uid, first.OrderId)

	firstPlayOnce.Do(func() {
		log.Infof("sql:%s", query)
	})

	t := playData{}
	if err = s.db.GetContext(ctx, &t, query); nil != err {
		if err.Error() != "sql: no rows in result set" {
			log.Errorf("db GetContext fail, sql: %s, err: %v\n", query, err)
			return
		}
	} else {
		ok = true
	}
	data.Uid = first.Uid

	if 0 == t.Mode {
		data.Amount = t.Amount + 1
	} else {
		data.Amount = t.Amount + 10
	}
	return data, ok, nil
}

var firstConsumeOnce sync.Once

func (s *Dao) getChanceByFirstConsume(ctx context.Context, first firstData) (data chanceData, ok bool, err error) {
	const tpl = `SELECT remain_chance-amount as amount FROM smash_egg_month_consume_record_%s WHERE uid=%d AND operate_id='%s'`

	query := fmt.Sprintf(tpl, genConsumeTableSuffixV2(first.CreateTime.Year(), int(first.CreateTime.Month())), first.Uid, first.OrderId)

	firstConsumeOnce.Do(func() {
		log.Infof("sql:%s", query)
	})

	if err = s.db.GetContext(ctx, &data, query); nil != err {
		if err.Error() != "sql: no rows in result set" {
			log.Errorf("db GetContext fail, sql: %s, err: %v\n", query, err)
			return
		}
	} else {
		ok = true
	}
	data.Uid = first.Uid
	return data, ok, nil
}

type remainData struct {
	Uid int64 `db:"uid" json:"uid"`
	Num int64 `db:"num" json:"num"`
}

func (s *Dao) GetRemainData(ctx context.Context, at time.Time) (remains []remainData, err error) {
	// 获取修改时间大于当前时间或者数量大于0的用户剩余信息
	chances, err := s.getChance(ctx, at)
	if err != nil {
		return nil, err
	}

	var sum int64
	for _, chance := range chances {
		sum += chance.Amount
	}

	log.Infof("current remain chance, now: %s, total: %d\n", at.Format(timeLayout), sum)

	tmp := make(map[int64]int64)

	process := 0

	for _, chance := range chances {
		if process++; process%1000 == 0 {
			log.Infof("count remain chance, process: %d, total: %d\n", process, sum)
		}

		// 修改时间小于统计开始时间, 则以当前值为余额
		if chance.ModifyTime.Format(timeLayout) < at.Format(timeLayout) {
			if 0 != chance.Amount {
				tmp[chance.Uid] = chance.Amount
			}
			continue
		}

		// 修改时间大于统计开始时间
		// 获取第一个修改记录
		first, ok, err := s.getFirstData(ctx, chance, at)
		if err != nil {
			return nil, err
		}
		if !ok {
			//取不到可能是因为时间误差，写到上个月的表里了，所以当前的就是剩余
			tmp[chance.Uid] = chance.Amount

			log.Infof("first data not found, uid: %d, modify at:%s, now: %s\n", chance.Uid, chance.ModifyTime.Format(timeLayout), at.Format(timeLayout))
			continue
		}

		// 通过修改记录获取余额(这里的余额是每个用户上个月剩余的余额，所以这里做了一下处理)
		if first.Source == 2 {
			// 第一条记录为充值记录
			chance, ok, err = s.getChanceByFirstConsume(ctx, first)
		} else {
			// 第一条记录为抽奖记录
			chance, ok, err = s.getChanceByFirstPlay(ctx, first)
		}
		if err != nil {
			return nil, err
		}
		if !ok {
			log.Infof("first data not found, uid: %d, modify at:%s, now: %s\n", chance.Uid, chance.ModifyTime.Format(timeLayout), at.Format(timeLayout))
			return nil, errors.New("first data not found")
		} else {
			tmp[chance.Uid] = chance.Amount
		}
	}

	remains = make([]remainData, 0, len(tmp))
	for k, v := range tmp {
		if 0 == v {
			continue
		}
		remains = append(remains, remainData{
			Uid: k,
			Num: v,
		})
	}
	return remains, nil
}
