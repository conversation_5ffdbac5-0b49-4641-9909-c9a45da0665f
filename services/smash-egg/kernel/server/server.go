package server

import (
	"context"
	"errors"
	"fmt"
	"gitlab.ttyuyin.com/tt-infra/tyr/log"
	backpackBaseClient "golang.52tt.com/clients/backpack-base"
	"golang.52tt.com/pkg/protocol/grpc"
	"golang.52tt.com/protocol/services/demo/echo"
	public_notice "golang.52tt.com/protocol/services/public-notice"
	ab_control "golang.52tt.com/services/smash-egg/ab-control"
	"golang.52tt.com/services/smash-egg/kernel/model/extra_award"
	"golang.52tt.com/services/smash-egg/kernel/model/prop"
	"runtime/debug"
	"strings"
	"sync"

	"github.com/go-redis/redis"
	"github.com/jmoiron/sqlx"
	accountService "golang.52tt.com/clients/account"
	apicenter "golang.52tt.com/clients/apicenter/apiserver"
	backpackSenderService "golang.52tt.com/clients/backpack-sender"
	channelService "golang.52tt.com/clients/channel"
	currencyService "golang.52tt.com/clients/currency"
	headImageService "golang.52tt.com/clients/headimage"
	probgamecenter "golang.52tt.com/clients/prob-game-center"
	"golang.52tt.com/pkg/deal_token"
	"golang.52tt.com/pkg/foundation/utils"
	appsync "golang.52tt.com/protocol/app/sync"
	apiPB "golang.52tt.com/protocol/services/apicenter/apiserver"
	backpackSenderPB "golang.52tt.com/protocol/services/backpacksender"
	kfk_smash_egg "golang.52tt.com/protocol/services/minToolkit/kafka/pb/kafka_smash_egg"
	probgamecenter2 "golang.52tt.com/protocol/services/probgamecenter"
	reconcile_v2 "golang.52tt.com/protocol/services/reconcile-v2"
	UnifiedPayCallback "golang.52tt.com/protocol/services/unified_pay/cb"
	"golang.52tt.com/services/notify"
	butils "golang.52tt.com/services/risk-control/backpack-sender/utils"
	businessCfg "golang.52tt.com/services/smash-egg/kernel/conf"
	"golang.52tt.com/services/smash-egg/kernel/model/event"
	"golang.52tt.com/services/smash-egg/kernel/model/magic"
	"golang.52tt.com/services/smash-egg/kernel/model/morph"
	"golang.52tt.com/services/smash-egg/kernel/model/report"
	"golang.52tt.com/services/smash-egg/kernel/model/setting"
	runCfg "golang.52tt.com/services/smash-egg/kernel/server/config"
	"golang.52tt.com/services/smash-egg/kernel/store"
	"golang.52tt.com/services/smash-egg/kernel/utils/common"
	unifiedSearchService "golang.52tt.com/services/unified-search/client"

	pushService "golang.52tt.com/clients/push-notification/v2"
	"golang.52tt.com/pkg/config"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/protocol/common/status"
	pb "golang.52tt.com/protocol/services/smash-egg"
	"golang.52tt.com/services/smash-egg/kernel/entity"

	cutil "golang.52tt.com/services/super-channel-logic/infra/util"

	"golang.52tt.com/services/smash-egg/kernel/model/activity_conf"
	"golang.52tt.com/services/smash-egg/kernel/model/light_effect"
	"golang.52tt.com/services/smash-egg/kernel/model/settle_refund"
	"time"
)

const (
	rollRecordCountLimit = 1000  // 滚动中奖记录限制数量
	rollRecordWorthLimit = 10000 // 滚动中奖记录限制价值

	// deficitUnknown = 0
	deficitNormal  = 1
	deficitWarning = 2
	deficitLimit   = 3

	//chargePrice       = 200
	chargeAmountLimit = 10000

	specialSourceRecharge = 2

	timeLayoutHourly = "2006-01-02/15"
	offsetUint64Test = 100000000000 // test 完成测试需要删除
)

type ruleOption struct {
	//CostAmount    uint32
	SimulateTimes uint32
}

var (
	notRecharge = []uint32{specialSourceRecharge}

	defaultErr   = protocol.ErrSystem
	invalidParam = protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "参数错误")

	ruleOptions = map[pb.Mode]ruleOption{
		pb.Mode_NORMAL_MODE: {
			//CostAmount:    1,
			SimulateTimes: 100000,
		},
		pb.Mode_GOLD_MODE: {
			//CostAmount:    10,
			SimulateTimes: 100000,
		},
	}
)

func getRuleOption(mode uint32) ruleOption {
	return ruleOptions[pb.Mode(mode)]
}

type SmashEggServer struct {
	cache *Cache

	store         *store.Store
	readonlyStore *store.Store

	pushClient           *pushService.Client
	backpackClient       *backpackBaseClient.Client
	backpackSenderClient *backpackSenderService.Client
	accountClient        *accountService.Client
	channelClient        *channelService.Client
	headImageClient      *headImageService.Client

	unifiedSearchClient *unifiedSearchService.UnifiedSearchClient

	apiCenterClient *apicenter.Client

	probGameCli        *probgamecenter.Client
	publicNoticeCli    public_notice.PublicNoticeClient
	magic              *magic.Magic
	setting            *setting.Setting
	reporter           *report.Reporter
	morph              *morph.Morph
	winningKfkProducer *event.KafkaProduce
	prop               *prop.Prop
	activityMgr        *activity_conf.SmashActivityMgr
	lightMgr           *light_effect.SmashLightMgr
	abControl          *ab_control.ABControl
	settleRefundMgr    *settle_refund.SettleRefundMgr
	extraAwardMgr      *extra_award.ExtraAwardMgr

	//bingo uint32

	stop chan interface{}
	wg   sync.WaitGroup
}

func NewSmashEggServer(ctx context.Context, sc *runCfg.Config) (*SmashEggServer, error) {
	//sc := &runCfg.Config{}
	//
	//err := sc.Parse(ctx, cfg)
	//if err != nil {
	//	log.ErrorWithCtx(ctx, "Parse failed: %v", err)
	//	return nil, err
	//}

	runCfg.Environment = sc.Environment
	runCfg.ThemeName = sc.ThemeName
	runCfg.TbeanContextPath = sc.TbeanContextPath

	bcFile := fmt.Sprintf("/data/oss/conf-center/tt/%s.json", runCfg.ThemeName)
	err := businessCfg.Load(ctx, bcFile)
	if err != nil {
		log.ErrorWithCtx(ctx, "Load business config failed: %v", err)
		return nil, err
	}

	log.Infof("run with config, service: %v, business: %v", utils.ToJson(sc), utils.ToJson(businessCfg.GetConfig()))

	s := &SmashEggServer{
		//bingo: businessCfg.GetConfig().Bingo,

		stop: make(chan interface{}),
	}

	redisClient := redis.NewClient(&redis.Options{
		Network:            sc.RedisConfig.Protocol,
		Addr:               sc.RedisConfig.Addr(),
		PoolSize:           sc.RedisConfig.PoolSize,
		IdleCheckFrequency: sc.RedisConfig.IdleCheckFrequency(),
		DB:                 sc.RedisConfig.DB,
	})

	dbClient, err := newMysqlClient(sc.MysqlConfig)
	if err != nil {
		log.ErrorWithCtx(ctx, "_initStore failed, err: %+v", err)
		return nil, err
	}

	readonlyDBClient, err := newMysqlClient(sc.MysqlSlaveConfig)
	if err != nil {
		log.ErrorWithCtx(ctx, "_initStore failed, err: %+v", err)
		return nil, err
	}

	s.cache = NewCache(redisClient)
	s.store = store.NewStore(dbClient)
	err = s.store.CreateRemainChanceTable()
	if err != nil {
		log.ErrorWithCtx(ctx, "Failed to CreateRemainChanceTable %v", err)
		return nil, err
	}
	err = s.store.CreateUserGuaranteedTbl(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "Failed to CreateRemainChanceTable %v", err)
		return nil, err
	}

	s.readonlyStore = store.NewStore(readonlyDBClient)

	s.pushClient, err = pushService.NewClient()
	if err != nil {
		log.ErrorWithCtx(ctx, "Failed to create notify %v", err)
		return nil, err
	}

	s.accountClient, err = accountService.NewClient()
	if err != nil {
		log.ErrorWithCtx(ctx, "Failed to create accountClient %v", err)
		return nil, err
	}

	s.probGameCli, err = probgamecenter.NewClient()
	if err != nil {
		log.ErrorWithCtx(ctx, "Failed to create probGameCli %v", err)
		return nil, err
	}

	s.publicNoticeCli, err = public_notice.NewClient(context.Background())
	if err != nil {
		log.ErrorWithCtx(ctx, "Failed to create publicNoticeCli, err:%v", err)
		return nil, err
	}
	s.backpackClient, _ = backpackBaseClient.NewClient()
	s.backpackSenderClient, _ = backpackSenderService.NewClient()

	s.channelClient = channelService.NewClient()
	s.headImageClient = headImageService.NewClient()
	s.apiCenterClient = apicenter.NewClient()
	s.unifiedSearchClient, _ = unifiedSearchService.NewClient()

	s.setting = setting.NewSetting(redisClient, dbClient)

	s.magic = magic.NewMagic(dbClient, redisClient)
	if err = s.magic.Start(0); err != nil {
		log.ErrorWithCtx(ctx, "start magic fail: %v", err)
		return nil, err
	}

	s.activityMgr, err = activity_conf.NewMgr(dbClient, redisClient)
	s.lightMgr, err = light_effect.NewMgr(dbClient)

	s.prop, err = prop.NewProp(dbClient, redisClient, s.activityMgr)
	if err != nil {
		log.ErrorWithCtx(ctx, "Failed to NewProp, err:%v", err)
		return nil, err
	}

	s.reporter = report.NewReporter(redisClient, readonlyDBClient, s.activityMgr)
	s.morph = morph.NewMorph(redisClient, dbClient, s.setting, s.pushClient, s.publicNoticeCli, s.activityMgr)

	s.settleRefundMgr, err = settle_refund.NewSettleRefundMgr(dbClient, redisClient, s.prop, s.activityMgr)
	if err != nil {
		log.ErrorWithCtx(ctx, "Failed to NewSettleRefundMgr, err:%v", err)
		return nil, err
	}

	s.extraAwardMgr, err = extra_award.NewMgr(dbClient, s.lightMgr, s.activityMgr, s.prop)
	if err != nil {
		log.ErrorWithCtx(ctx, "Failed to extra_award.NewMgr, err:%v", err)
		return nil, err
	}

	if !businessCfg.GetConfig().SyncRemoteDebug {
		if err = s.reporter.Start(); err != nil {
			log.ErrorWithCtx(ctx, "start magic fail: %v", err)
			return nil, err
		}
		/*if err = s.morph.Start(); err != nil {
			log.ErrorWithCtx(ctx, "start morph fail: %v", err)
			return nil, err
		}
		*/
	}

	s.abControl, err = ab_control.NewABControl()
	if err != nil {
		log.ErrorWithCtx(ctx, "Failed to NewABControl, err:%v", err)
		return nil, err
	}

	s.winningKfkProducer, err = event.NewKafkaProduce(sc.KafkaConfig)
	if err != nil {
		log.ErrorWithCtx(ctx, "Failed to NewKafkaProduce %v", err)
		return nil, err
	}

	//奖池定时更新
	s.magic.StartTimer()
	//启动维护线程
	s.maintain()
	return s, nil
}

func newMysqlClient(cfg *config.MysqlConfig) (*sqlx.DB, error) {
	dbClient, err := sqlx.Connect("mysql", cfg.ConnectionString())
	if err != nil {
		log.Errorf("_initStore failed, err: %+v", err)
		return nil, err
	}

	if cfg.MaxIdleConns > 0 {
		dbClient.SetMaxIdleConns(cfg.MaxIdleConns)
	}
	if cfg.MaxOpenConns > 0 {
		dbClient.SetMaxOpenConns(cfg.MaxOpenConns)
	}
	return dbClient, nil
}

func (s *SmashEggServer) ShutDown() {
	s.magic.Stop()
	s.reporter.Stop()
	s.morph.Stop()
	s.winningKfkProducer.Close()
	close(s.stop)
	s.wg.Wait()
}

func (s *SmashEggServer) Echo(_ context.Context, req *echo.StringMessage) (*echo.StringMessage, error) {
	return req, nil
}

func (s *SmashEggServer) deficitCheck(now time.Time) (deficit int64, status int, err error) {
	var cfg entity.SystemConfig
	err = s.setting.GetSystemConfig(&cfg)
	if nil != err {
		log.Errorf("get system config failed: %+v", err)
		return
	}

	if runCfg.StatusEnable != cfg.Status && runCfg.StatusTesting != cfg.Status {
		return
	}

	var profit int64
	if profit, err = s.getProfit(now); err != nil {
		log.Errorf("getProfit failed:%+v", err)
		return
	}

	log.Debugf("[stable]check magic profit is %d", profit)

	deficit = profit

	//触发停服
	if 0 != cfg.DeficitLimit && int64(cfg.DeficitLimit)+profit < 0 {
		cfg.Status = runCfg.StatusErr
		err = s.setting.SetSystemConfig(cfg)
		if nil != err {
			log.Errorf("_setConfig failed:%+v", err)
			return
		}
		status = deficitLimit
	} else if 0 != cfg.DeficitWarning && int64(cfg.DeficitWarning)+profit < 0 {
		status = deficitWarning
	} else {
		status = deficitNormal
	}
	return
}

func (s *SmashEggServer) awardConsume(uid, packId, amount uint32, operateId, dealToken string, at time.Time, actCfg *pb.SmashActivityConfig) (err error) {
	err = s.giveUserPackage(uid, packId, amount, operateId, dealToken, at, actCfg)
	if err != nil && !cutil.IsErrorOf(err, status.ErrRiskControlBackpackDuplicateOrderid) {
		log.Errorf("GiveUserPackage %d, %d failed: %v", uid, packId, err)
	} else {
		//记录奖品已经发放
		tctx, tctxCancel := context.WithTimeout(context.Background(), time.Second*3)
		defer tctxCancel()

		terr := s.store.ChangeConsumeRecordStatusV2(tctx, at, uid, 1, operateId)
		if nil != terr {
			log.Errorf("ChangeConsumeRecordStatusV2 %d, %s failed: %v", uid, operateId, terr)
		}

		terr = s.store.ChangeWinningRecordStatusV2(tctx, at, uid, 1, operateId)
		if nil != terr {
			log.Errorf("ChangeWinningRecordStatusV2 %d, %s failed: %v", uid, operateId, terr)
		}
	}
	return
}

func (s *SmashEggServer) awardWinning(uid, packId uint32, operateId, dealToken string, at time.Time, actCfg *pb.SmashActivityConfig) (err error) {
	err = s.giveUserPackage(uid, packId, 1, operateId, dealToken, at, actCfg)
	if err != nil && !cutil.IsErrorOf(err, status.ErrRiskControlBackpackDuplicateOrderid) {
		log.Errorf("GiveUserPackage %d, %d failed: %v", uid, packId, err)
	} else {
		//记录奖品已经发放
		tctx, tctxCancel := context.WithTimeout(context.Background(), time.Second*3)
		defer tctxCancel()

		terr := s.store.ChangeWinningRecordStatusV2(tctx, at, uid, 1, operateId)
		if nil != terr {
			log.Errorf("ChangeWinningRecordStatus %d, %s failed: %v", uid, operateId, terr)
		}
	}
	return
}

func (s *SmashEggServer) giveUserPackage(uid, packId, amount uint32, orderId, dealToken string, at time.Time, actCfg *pb.SmashActivityConfig) (err error) {
	//cfg := businessCfg.GetConfig()

	backpackBusinessId, backpackBusinessKey := actCfg.GetRiskId(), actCfg.GetRiskSecret()

	ctx, cancel := context.WithTimeout(context.Background(), time.Second*3)
	defer cancel()

	if packId == actCfg.GetPropGiftPackId() {
		_, err = s.backpackSenderClient.SendBackpackWithRiskControl(ctx, &backpackSenderPB.SendBackpackWithRiskControlReq{
			BusinessId:     backpackBusinessId,
			BackpackId:     packId,
			ReceiveUid:     uid,
			BackpackCnt:    amount,
			ServerTime:     at.Unix(),
			OrderId:        orderId,
			Ciphertext:     butils.AESEncrypt([]byte(orderId), []byte(backpackBusinessKey)),
			ExpireDuration: 1,
			SourceAppId:    "转转",
			DealToken:      dealToken,
		})
	} else {
		_, err = s.backpackSenderClient.SendOneBackpackWithRiskControl(ctx, &backpackSenderPB.SendOneBackpackWithRiskControlReq{
			BusinessId:  backpackBusinessId,
			BackpackId:  packId,
			ReceiveUid:  uid,
			ServerTime:  at.Unix(),
			OrderId:     orderId,
			Ciphertext:  butils.AESEncrypt([]byte(orderId), []byte(backpackBusinessKey)),
			SourceAppId: "转转",
			DealToken:   dealToken,
		})
	}

	log.Infof("GiveUserPackage app:%d, orderId:%s, uid:%d, packId:%d, amount:%d, err:%v", backpackBusinessId, orderId, uid, packId, amount, err)
	return err
}

func (s *SmashEggServer) reissueConsume(limit uint32, tableTime, begin, end time.Time, actCfg *pb.SmashActivityConfig) error {
	ctx, cancel := context.WithTimeout(context.Background(), time.Second*3)
	records, err := s.readonlyStore.GetConsumeRecordByStatusV2(ctx, tableTime, 0, uint32(begin.Unix()), uint32(end.Unix()), limit)
	cancel()

	if nil != err {
		log.Errorf("GetConsumeRecordByStatus %v fail %v", begin, err)
		return err
	}

	if 0 == len(records) {
		return nil
	}

	log.Debugf("reissueConsume >> find %d record to reissue at %v", len(records), begin)
	chargePrice := int64(actCfg.GetPropPrice())

	for i := 0; i < len(records); i++ {
		record := records[i]

		// 概率玩法风控中心检查
		dealToken, err := s.checkProbGameCenter(context.Background(), record.OperateId, record.Uid, chargePrice*int64(record.Amount), chargePrice*int64(record.Amount))
		if nil != err {
			log.Errorf("checkProbGameCenter failed, uid: %d, err: %v", record.Uid, err)
			continue
		}

		err = s.awardConsume(record.Uid, actCfg.GetPropGiftPackId(), record.Amount, record.OperateId, dealToken, record.CreateTime, actCfg)
		if nil != err {
			log.Errorf("reissueConsume %d, %d, %d, %s fail: %v", record.Uid, actCfg.GetPropGiftPackId(), record.Amount, record.OperateId, err)
		} else {
			log.Infof("reissueConsume %d, %d, %d, %s ok", record.Uid, actCfg.GetPropGiftPackId(), record.Amount, record.OperateId)
		}
	}
	return nil
}

func (s *SmashEggServer) reissueWinning(uidIndex, limit uint32, tableTime, begin, end time.Time, actCfg *pb.SmashActivityConfig) error {
	ctx, cancel := context.WithTimeout(context.Background(), time.Second*3)
	records, err := s.readonlyStore.GetWinningRecordByStatusV2(ctx, tableTime, uidIndex, 0, uint32(begin.Unix()), uint32(end.Unix()), limit)
	cancel()

	if nil != err {
		log.ErrorWithCtx(ctx, "GetWinningRecordByStatus %d fail %v", uidIndex, err)
		return err
	}

	if 0 == len(records) {
		return nil
	}

	log.Debugf("reissueWinning  >> find %d record to reissue at %d", len(records), uidIndex)

	chargePrice := int64(actCfg.GetPropPrice())
	for i := 0; i < len(records); i++ {
		record := records[i]

		//大奖不重发，防止出现问题
		//if record.PackId == s.bingo {
		//	continue
		//}

		//ruleOpt := getRuleOption(record.Mode)

		// 概率玩法风控中心检查
		dealToken, err := s.checkProbGameCenter(context.Background(), record.OperateId, record.Uid, chargePrice*int64(record.CostChanceNum), int64(record.PackWorth))
		if nil != err {
			log.Errorf("checkProbGameCenter failed, uid: %d, err: %v", record.Uid, err)
			continue
		}

		err = s.awardWinning(record.Uid, record.PackId, record.OperateId, dealToken, record.CreateTime, actCfg)
		if nil != err {
			log.ErrorWithCtx(ctx, "reissueWinning uid:%d, packId:%d, orderId:%s fail: %v", record.Uid, record.PackId, record.OperateId, err)
		} else {
			log.Infof("reissueWinning uid:%d, packId:%d, orderId:%s ok", record.Uid, record.PackId, record.OperateId)
		}
	}
	return nil
}

func (s *SmashEggServer) maintain() {
	s.wg.Add(4)

	// 清理砸蛋计数
	go func() {
		interval := time.Duration(60-time.Now().Minute()%60) * time.Minute
		for {
			select {
			case <-s.stop:
				s.wg.Done()
				return
			case <-time.After(interval):
				_, err := s.cache.DelUserHits()
				if nil != err {
					log.Errorf("DelUserHits failed: %+v", err)
				}
				interval = 60 * time.Minute
			}
		}
	}()

	//清理无效数据
	go func() {
		for {
			select {
			case <-s.stop:
				s.wg.Done()
				return
			case <-time.After(time.Minute):
				at := time.Now()

				_, err := s.cache.TrimWinningRecord(uint32(rollRecordCountLimit))
				if nil != err {
					log.Errorf("TrimWinningRecord failed: %+v", err)
				}

				//删除上一小时的收益数据
				if at.Minute() > 10 {
					_ = s.deleteProfit(common.TheHourAgo(at, 1))
				}
			}
		}
	}()

	//亏损统计上报
	go func() {
		for {
			select {
			case <-s.stop:
				s.wg.Done()
				return
			case <-time.After(time.Second * 5):
				now := time.Now()
				profit, tstatus, err := s.deficitCheck(now)
				if err != nil {
					log.Errorf("deficitCheck fail : %v", err)
					break
				}
				log.Debugf("[stable]deficitCheck got %d, %d", profit, tstatus)
			}
		}
	}()

	//处理漏发的包裹
	go func() {
		for {
			select {
			case <-s.stop:
				s.wg.Done()
				return
			case <-time.After(time.Minute):
				const reissueKey = "smash_egg_reissue_lock"

				cfg := businessCfg.GetConfig()
				if nil == cfg || 0 == cfg.ReissueGiftDuration {
					break
				}

				now := time.Now()
				ok, err := s.cache.Lock("", reissueKey, time.Minute*5)
				if err != nil {
					log.Errorf("Lock by reissue fail: %v", err)
					break
				}
				if !ok {
					break
				}

				/*dur := time.Duration(cfg.ReissueGiftDuration) * time.Second
				  begin, end := uint32(now.Add(-dur).Unix()), uint32(now.Unix()-60)

				  layout := "2006-01-02 15:04:05"
				  log.Debugf("reissue start from %s to %s", time.Unix(int64(begin), 0).Format(layout), time.Unix(int64(end), 0).Format(layout))

				  beginMonth := time.Unix(int64(begin), 0).Month()
				  endMonth := time.Unix(int64(end), 0).Month()

				  for m := beginMonth; m <= endMonth; m++ {
				  	for i := uint32(0); i < 100; i++ {
				  		//补发抽奖
				  		_ = s.reissueWinning(uint32(m), i, begin, end, cfg.ReissueGiftLimit)
				  	}
				  	for i := uint32(0); i < 10; i++ {
				  		//补发充值
				  		_ = s.reissueConsume(uint32(m), begin, end, cfg.ReissueGiftLimit)
				  	}
				  }*/

				actCfg, err := s.activityMgr.GetSmashActivityConfWithCache(context.Background(), false)
				if err != nil {
					log.Errorf("reissue fail to GetSmashActivityConfWithCache, err: %v", err)
					break
				}

				/*月表改造后 奖励补发调整*/
				dur := time.Duration(cfg.ReissueGiftDuration) * time.Second
				begin, end := now.Add(-dur), now.Add(-60*time.Second)
				layout := "2006-01-02 15:04:05"
				log.Debugf("reissue start from %s to %s", begin.Format(layout), end.Format(layout))

				for i := uint32(0); i < 10; i++ {
					//补发抽奖
					_ = s.reissueWinning(i, cfg.ReissueGiftLimit, begin, begin, end, actCfg)
				}
				//补发充值
				_ = s.reissueConsume(cfg.ReissueGiftLimit, begin, begin, end, actCfg)

				// 跨月补发
				if begin.Before(time.Date(end.Year(), end.Month(), 1, 0, 0, 0, 0, time.Local)) {
					for i := uint32(0); i < 10; i++ {
						//补发抽奖
						_ = s.reissueWinning(i, cfg.ReissueGiftLimit, end, begin, end, actCfg)
					}
					//补发充值
					_ = s.reissueConsume(cfg.ReissueGiftLimit, end, begin, end, actCfg)
				}

				if time.Since(now) < time.Minute*4 {
					_, _ = s.cache.Unlock(reissueKey)
				}
			}
		}
	}()
}

func (s *SmashEggServer) GetConsumeRecord(ctx context.Context, req *pb.GetConsumeRecordReq) (resp *pb.GetConsumeRecordResp, err error) {
	resp = &pb.GetConsumeRecordResp{}

	//if time.Unix(int64(req.GetBeginTime()), 0).Month() != time.Unix(int64(req.GetEndTime()), 0).Month() {
	//	return resp, protocol.NewExactServerError(nil, status.ErrSys, "查询不允许跨月")
	//}
	now := time.Now()
	beginTime := time.Unix(int64(req.GetBeginTime()), 0)
	endTime := time.Unix(int64(req.GetEndTime()), 0)
	if beginTime.Year() != endTime.Year() || beginTime.Month() != endTime.Month() {
		return resp, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "查询不允许跨月")
	}

	var records []entity.ConsumeRecord
	var tableTime time.Time
	tableTime = beginTime
	if req.BeginTime == 0 {
		tableTime = now
	}

	records, err = s.store.GetConsumeRecordV2(ctx, tableTime, req.GetOrderId(), req.GetOffset(), req.GetLimit(), req.GetUid(), req.GetBeginTime(), req.GetEndTime())
	if nil != err {
		log.ErrorWithCtx(ctx, "GetConsumeRecordV2 fail: %v", err)
		return resp, toServerError(err)
	}

	if len(records) == 0 {
		//本月无记录查上个月的
		tableTime = now.AddDate(0, -1, 0)
		records, err = s.store.GetConsumeRecordV2(ctx, tableTime, req.GetOrderId(), req.GetOffset(), req.GetLimit(), req.GetUid(), req.GetBeginTime(), req.GetEndTime())
		if nil != err {
			log.ErrorWithCtx(ctx, "GetConsumeRecord fail: %v", err)
			return resp, toServerError(err)
		}
	}

	if len(records) == 0 {
		//本月无记录查下个月的
		tableTime = now.AddDate(0, 1, 0)
		records, err = s.store.GetConsumeRecordV2(ctx, tableTime, req.GetOrderId(), req.GetOffset(), req.GetLimit(), req.GetUid(), req.GetBeginTime(), req.GetEndTime())
		if nil != err {
			log.ErrorWithCtx(ctx, "GetConsumeRecord fail: %v", err)
			return resp, toServerError(err)
		}
	}

	log.Debugf("GetConsumeRecord %d get %+v", req.GetUid(), records)

	resp.ConsumeRecordList = make([]*pb.ConsumeRecord, len(records))
	for i := 0; i < len(records); i++ {
		resp.ConsumeRecordList[i] = &pb.ConsumeRecord{
			Id:         records[i].Id,
			Uid:        records[i].Uid,
			Amount:     records[i].Amount,
			Fee:        records[i].Fee,
			OrderId:    records[i].OrderId,
			CreateTime: uint32(records[i].CreateTime.Unix()),
		}
	}
	return resp, nil
}

func (s *SmashEggServer) GetWinningRecord(ctx context.Context, req *pb.GetWinningRecordReq) (resp *pb.GetWinningRecordResp, err error) {
	resp = &pb.GetWinningRecordResp{}

	if time.Unix(int64(req.GetBeginTime()), 0).Month() != time.Unix(int64(req.GetEndTime()), 0).Month() {
		return resp, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "查询不允许跨月")
	}

	mode := uint32(req.GetMode())
	flag := uint32(req.GetMorphFlag())
	var records []entity.WinningRecord
	if 0 == req.Uid {
		if req.Limit == 0 {
			records, err = s.cache.GetWinningRecord(0, 100)
		} else {
			records, err = s.cache.GetWinningRecord(0, req.GetLimit())
		}
	} else {
		offset := req.GetOffset()
		if businessCfg.GetConfig().TestOffsetUint64 && req.GetOffset() != 0 {
			offset = offset - offsetUint64Test
		}

		if req.Rare {
			//records, err = s.store.GetWinningRecord(ctx, req.GetUid(), businessCfg.GetConfig().NotRares, req.GetOffset(), req.GetLimit(), req.GetBeginTime(), req.GetEndTime(), notRecharge)
			records, err = s.store.GetWinningRecordV2(ctx, req.GetUid(), mode, flag, businessCfg.GetConfig().NotRares, offset, req.GetLimit(), req.GetBeginTime(), req.GetEndTime(), notRecharge)
		} else {
			//records, err = s.store.GetWinningRecord(ctx, req.GetUid(), nil, req.GetOffset(), req.GetLimit(), req.GetBeginTime(), req.GetEndTime(), notRecharge)
			records, err = s.store.GetWinningRecordV2(ctx, req.GetUid(), mode, flag, nil, offset, req.GetLimit(), req.GetBeginTime(), req.GetEndTime(), notRecharge)
		}
	}
	if nil != err {
		log.ErrorWithCtx(ctx, "GetWinningRecord fail: %v", err)
		return resp, toServerError(err)
	}

	resp.WinningRecordList = make([]*pb.WinningRecord, len(records))
	for i := 0; i < len(records); i++ {
		record := records[i]
		prize := s.magic.GetPrize(record.PackId)

		resp.WinningRecordList[i] = &pb.WinningRecord{
			Id:         record.Id,
			Uid:        record.Uid,
			Source:     pb.Source(record.Source),
			Flag:       pb.Flag(record.Flag),
			PackId:     record.PackId,
			PackWorth:  record.PackWorth,
			PackName:   prize.PackName,
			PackPic:    prize.PackPic,
			PackAmount: prize.PackAmount,
			PackDesc:   prize.PackDesc,
			CreateTime: uint32(record.CreateTime.Unix()),
			ChannelId:  record.Channel,
			Mode:       pb.Mode(record.Mode),
		}
	}
	if len(resp.GetWinningRecordList()) > 0 {
		if businessCfg.GetConfig().TestOffsetUint64 {
			resp.GetWinningRecordList()[len(resp.WinningRecordList)-1].Id = resp.GetWinningRecordList()[len(resp.WinningRecordList)-1].Id + offsetUint64Test
		}
	}

	return resp, nil
}

func (s *SmashEggServer) GetSmashStatus(ctx context.Context, req *pb.GetSmashStatusReq) (resp *pb.GetSmashStatusResp, err error) {
	resp = &pb.GetSmashStatusResp{}

	/*ok, end, err := s.morph.Morphing()
	if err != nil {
		log.ErrorWithCtx(ctx, "get morph failed, err: %v", err)
		return resp, toServerError(err)
	}*/

	var cfg entity.SystemConfig
	err = s.setting.GetSystemConfig(&cfg)
	if nil != err {
		return resp, toServerError(err)
	}
	if err = s.checkSystemConfig(&cfg, req.Uid); err != nil {
		return resp, protocol.NewExactServerError(nil, status.ErrSmashEggSystemMaintain, "服务器网络异常")
	}

	resp.OverallSpeed = cfg.OverallSpeed
	/*
		//变身之后击打值为0, 变身态为1且未到截至时间则判断为变身态
		if ok && time.Now().Before(end) {
			resp.MorphFlag = pb.Flag_MORPH
			resp.MorphEndTime = uint32(end.Unix())
		} else {
			//resp.CurrentHits, err = s.getMorphHits(&cfg)
			//if err != nil {
			//	log.ErrorWithCtx(ctx, "get current hits failed, err: %+v", err)
			//	return
			//}
			if ok {
				resp.CurrentHits = 0
			} else {
				resp.CurrentHits = s.morph.GetMorphHits()
			}
		}
	*/
	if req.GetUid() == 0 {
		return resp, nil
	}

	//resp.MyTodayHits, err = s.cache.GetUserHits(req.GetUid())
	//if err != nil {
	//	log.ErrorWithCtx(ctx, "get my hits failed, err: %+v", err)
	//	return
	//}
	resp.MyRemainHits, resp.Speed, err = s.getRemainChance(ctx, req.GetUid())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetSmashStatus fail to getRemainChance, %+v, err: %v", req, err)
		return resp, err
	}

	// 保底值获取
	modeCfgList := businessCfg.GetConfig().ModeCfgList
	if len(modeCfgList) == 0 {
		return resp, nil
	}

	mode2Flag2Info, err := s.GetUserGuaranteedMap(ctx, req.GetUid())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetSmashStatus fail to GetUserGuaranteedMap, %+v, err: %v", req, err)
		return resp, err
	}

	resp.GuaranteedInfoList = make([]*pb.GuaranteedInfo, 0)
	for _, modeCfg := range modeCfgList {
		info := &pb.GuaranteedInfo{
			Mode:      pb.Mode(modeCfg.Mode),
			MorphFlag: pb.Flag(modeCfg.Flag),
		}

		if gInfo, ok := mode2Flag2Info[modeCfg.Mode][modeCfg.Flag]; ok {
			info.Speed = gInfo.CurrVal
			info.OverallSpeed = gInfo.GoalVal
		}

		if info.GetSpeed() == 0 || info.GetOverallSpeed() == 0 {
			// 重新获取最大保底值
			info.OverallSpeed = modeCfg.MaxGuaranteedVal
		}

		resp.GuaranteedInfoList = append(resp.GuaranteedInfoList, info)
	}

	return resp, nil
}

func (s *SmashEggServer) Recharge(ctx context.Context, req *pb.RechargeReq) (resp *pb.RechargeResp, err error) {
	resp = &pb.RechargeResp{}

	// 结算冻结中不可充值
	if !s.settleRefundMgr.CheckSettleStatus() {
		return resp, protocol.NewExactServerError(nil, status.ErrSmashEggSystemMaintain, "系统维护中")
	}

	//充值回调测试
	if businessCfg.GetConfig().TestRechargeCallBack {
		for _, v := range businessCfg.GetConfig().ReissueTestUidList {
			if req.GetRecord().GetUid() == v {
				log.Debugf("充值回调测试，uid:%d, OrderId:%v", req.GetRecord().GetUid(), req.GetRecord().GetOrderId())
				return resp, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "用户充值回调测试")
			}
		}
	}

	if nil == req.GetRecord() {
		return resp, invalidParam
	}

	// 判断order格式，防止测试跑到线上
	if (runCfg.Environment == runCfg.Production || runCfg.Environment == runCfg.Staging) &&
		!strings.HasPrefix(req.GetRecord().GetOrderId(), fmt.Sprintf("%d_%d_%d_%d", currencyService.CurrencyUser, req.GetRecord().GetUid(), currencyService.BizLottery, 0)) {
		log.ErrorWithCtx(ctx, "Recharge receive wrong order uid:%d, order id: %s", req.GetRecord().GetUid(), req.GetRecord().GetOrderId())
		return resp, nil
	}

	amount := req.GetRecord().GetAmount()
	fee := req.GetRecord().GetFee()
	uid := req.GetRecord().GetUid()
	orderId := req.GetRecord().GetOrderId()
	createTime := req.GetRecord().GetCreateTime()

	actCfg, err := s.activityMgr.GetSmashActivityConfWithCache(ctx, false)
	if err != nil {
		log.ErrorWithCtx(ctx, "Recharge fail to GetSmashActivityConfWithCache, req:%+v, err: %v", req, err)
		return resp, toServerError(err)
	}

	chargePrice := int64(actCfg.GetPropPrice())
	if amount > chargeAmountLimit || amount != fee/uint32(chargePrice) {
		return resp, invalidParam
	}

	log.DebugWithCtx(ctx, "%v recharge %v", uid, amount)

	var tx interface{}
	//txCtx, txCancel := context.WithTimeout(context.Background(), time.Minute)
	//defer txCancel()
	tx, err = s.store.TransBegin(ctx)
	if nil != err {
		log.ErrorWithCtx(ctx, "db trans begin failed, uid: %d, err: %v", uid, err)
		return resp, toServerError(err)
	}

	var consume entity.ConsumeRecord
	consume, err = s.doRecharge(ctx, tx, uid, amount, fee, createTime, orderId, actCfg)
	if nil != err {
		_ = s.store.TransEnd(tx, false)

		log.ErrorWithCtx(ctx, "doRecharge failed, uid: %d, err: %v", uid, err)
		return resp, toServerError(err)
	}

	err = s.store.TransEnd(tx, true)
	if nil != err {
		log.ErrorWithCtx(ctx, "trans end failed, uid: %d, err: %v", uid, err)
		return resp, toServerError(err)
	}

	//清理缓存
	//_, _ = s.cache.DelUserRemainChance(req.Record.Uid)
	_, _ = s.cache.DelUserSpeed(uid)
	_ = s.prop.ClearUserPropCache(ctx, uid)

	_, remainPropNum, err := s.GetUserPropList(ctx, uid, actCfg.GetSmashPropId())
	if err != nil {
		log.ErrorWithCtx(ctx, "Recharge fail to GetUserPropList, uid: %d, err: %v", uid, err)
	}

	// 概率玩法风控中心检查
	dealToken, err := s.checkProbGameCenter(ctx, consume.OperateId, uid, chargePrice*int64(amount), chargePrice*int64(amount))
	if nil != err {
		log.ErrorWithCtx(ctx, "checkProbGameCenter failed, uid: %d, err: %v", uid, err)
		//return resp, toServerError(err)
	}

	var testUser bool
	for _, v := range businessCfg.GetConfig().ReissueTestUidList {
		if uid == v {
			log.Debugf("补发测试用户，uid:%d, OperateId:%v", uid, consume.OperateId)
			testUser = true
		}
	}

	if !testUser {
		//给用户送充值礼物
		_ = s.awardConsume(uid, actCfg.GetPropGiftPackId(), amount, consume.OperateId, dealToken, time.Unix(int64(createTime), 0), actCfg)
	}

	resp.Amount = remainPropNum
	resp.OrderId = consume.OperateId
	return resp, nil
}

func (s *SmashEggServer) Smash(ctx context.Context, req *pb.SmashReq) (resp *pb.SmashResp, err error) {
	resp = &pb.SmashResp{}

	if req.Mode != pb.Mode_NORMAL_MODE && req.Mode != pb.Mode_GOLD_MODE {
		return resp, invalidParam
	}

	// 结算冻结中不可抽奖
	if !s.settleRefundMgr.CheckSettleStatus() {
		return resp, protocol.NewExactServerError(nil, status.ErrSmashEggSystemMaintain, "系统维护中")
	}

	uid := req.GetUid()
	source := uint32(req.GetSource())
	mode := uint32(req.GetMode())
	channelId := req.GetChannelId()
	flag := uint32(req.GetMorphFlag())

	log.DebugWithCtx(ctx, "smash %+v", req)

	//ruleOpt := getRuleOption(uint32(req.Mode))
	modeCfg := businessCfg.GetConfig().GetModeCfg(mode, flag)

	if modeCfg == nil || 0 == modeCfg.LotteryCostNum {
		return resp, invalidParam
	}

	costNum := modeCfg.LotteryCostNum
	//参数判断
	if uid == 0 && req.Amount != costNum {
		return resp, invalidParam
	}

	//系统检查
	var cfg entity.SystemConfig
	err = s.setting.GetSystemConfig(&cfg)
	if nil != err {
		return resp, toServerError(err)
	}
	if err = s.checkSystemConfig(&cfg, req.Uid); err != nil {
		return resp, protocol.NewExactServerError(nil, status.ErrSmashEggSystemMaintain, "服务器网络异常")
	}

	//用户状态检查
	var remainNum, currentDeficitLimit uint32
	{
		ctx2, ctx2Cancel := context.WithTimeout(context.Background(), time.Second*5)
		defer ctx2Cancel()
		_, remainNum, currentDeficitLimit, _, err = s.checkSmashStatus(ctx2, &cfg, uid)
		if nil != err {
			log.ErrorWithCtx(ctx, "checkSmashStatus failed, uid:%d, err: %v", uid, err)
			return resp, toServerError(err)
		}
		if remainNum < costNum {
			log.ErrorWithCtx(ctx, "smash chance not enough, uid:%d, remainNum:%d, costAmount:%d", uid, remainNum, costNum)
			return resp, protocol.NewExactServerError(nil, status.ErrSmashEggChanceNotEnough)
		}
	}

	actCfg, err := s.activityMgr.GetSmashActivityConfWithCache(ctx, false)
	if err != nil {
		log.ErrorWithCtx(ctx, "Smash fail to GetSmashActivityConfWithCache, req:%+v, err: %v", req, err)
		return resp, toServerError(err)
	}

	//开启事务
	var tx interface{}
	//txCtx, txCancel := context.WithTimeout(context.Background(), time.Minute)
	//defer txCancel()

	tx, err = s.store.TransBegin(ctx)
	if nil != err {
		log.ErrorWithCtx(ctx, "trans begin failed, uid: %d, err: %v", uid, err)
		return resp, toServerError(err)
	}

	//抽奖逻辑
	current := time.Unix(time.Now().Unix(), 0) // 保留到秒级

	// ！！！ doSmash 参数 maxSpeed 值与配置里面的 MaxSpeed 不同
	prize, winning, dealToken, err := s.doSmash(ctx, tx, current, currentDeficitLimit, uid, channelId, source, flag, /*cfg.MaxRandN, cfg.OverallSpeed,*/ mode, actCfg)
	if nil != err {
		_ = s.store.TransEnd(tx, false)

		log.ErrorWithCtx(ctx, "doSmash failed, uid: %d, err: %v", uid, err)
		return resp, toServerError(err)
	}

	//提交事务
	err = s.store.TransEnd(tx, true)
	if err != nil {
		log.ErrorWithCtx(ctx, "trans end failed, uid: %d, err: %v", uid, err)
		return resp, toServerError(err)
	}

	//清除缓存
	//_, _ = s.cache.DelUserRemainChance(req.Uid)
	//_, _ = s.cache.DelUserSpeed(uid)
	_ = s.cache.ClearUserGuaranteedInfo(uid)
	_ = s.prop.ClearUserPropCache(ctx, uid)

	_, remainPropNum, err := s.GetUserPropList(ctx, uid, actCfg.GetSmashPropId())
	if err != nil {
		log.ErrorWithCtx(ctx, "Smash fail to GetUserPropList, uid: %d, err: %v", uid, err)
	}

	//数据上报
	{
		//用户击打次数增加
		hits, err := s.cache.AddUserHits(winning.Uid, costNum)
		if nil != err {
			log.ErrorWithCtx(ctx, "add my hits failed, uid: %d, err: %v", uid, err)
		} else {
			log.DebugWithCtx(ctx, "%v hits %v", winning.Uid, hits)
		}

		_ = s.cache.SetUserSmashFlagWithExpire(uid, time.Duration(businessCfg.GetConfig().GetUserInvestFlagExpireDay()*24*3600)*time.Second)

		//全局击打次数增加, 变身期间或金色转转模式不增加
		if 0 == winning.Flag && mode == uint32(pb.Mode_NORMAL_MODE) {
			err = s.morph.AddMorphHits(current, costNum)
			if nil != err {
				log.ErrorWithCtx(ctx, "add current hits failed, uid: %d, err: %v", uid, err)
			} else {
				log.DebugWithCtx(ctx, "current hits %v", hits)
			}
		}

		//增加中奖缓存
		if winning.PackWorth >= rollRecordWorthLimit {
			terr := s.cache.RecordWinning(winning)
			if nil != terr {
				log.ErrorWithCtx(ctx, "add recent winning record failed, uid: %d, err: %v", uid, terr)
			}
		}
	}

	var testUser bool
	for _, v := range businessCfg.GetConfig().ReissueTestUidList {
		if uid == v {
			log.Debugf("补发测试用户，uid:%d, OperateId:%v", uid, winning.OperateId)
			testUser = true
		}
	}

	// 中奖kafka事件
	s.winningKfkProducer.ProduceSmashEggWinningEvent(ctx, &kfk_smash_egg.SmashEggWinningEvent{
		Uid:        uid,
		ChannelId:  channelId,
		Source:     kfk_smash_egg.Source(source),
		Flag:       kfk_smash_egg.Flag(flag),
		Mode:       kfk_smash_egg.Mode(mode),
		PackId:     prize.PackId,
		PackWorth:  prize.PackWorth,
		PackName:   prize.PackName,
		GiftPic:    prize.PackPic,
		GiftAmount: prize.PackAmount,
		PackDesc:   prize.PackDesc,
		CreateTime: uint32(current.Unix()),
		OrderId:    winning.OperateId,
	})

	if !testUser {
		//给用户发奖
		_ = s.awardWinning(uid, winning.PackId, winning.OperateId, dealToken, current, actCfg)
	}

	resp.RemainChance = remainPropNum
	resp.Speed = winning.Speed
	//resp.OverallSpeed = cfg.OverallSpeed
	resp.OverallSpeed = winning.OriginN
	resp.IsBingo = modeCfg.BingoPackId == prize.PackId

	// 获取奖励光效配置
	lightEffect := s.lightMgr.GetSmashLightEffectByPrize(ctx, &prize)
	log.DebugWithCtx(ctx, "Smash GetSmashLightEffectByPrize lightEffect%+v, uid:%d prize:%+v", lightEffect, uid, prize)
	resp.WinningRecordList = []*pb.WinningRecord{
		{
			//Id:              0,
			Uid:             uid,
			Source:          pb.Source(source),
			Flag:            pb.Flag(flag),
			PackId:          prize.PackId,
			PackWorth:       prize.PackWorth,
			PackName:        prize.PackName,
			PackPic:         prize.PackPic,
			PackAmount:      prize.PackAmount,
			PackDesc:        prize.PackDesc,
			CreateTime:      uint32(current.Unix()),
			ChannelId:       channelId,
			OrderId:         winning.OperateId,
			Mode:            pb.Mode(mode),
			LightEffectId:   lightEffect.GetConfId(),
			LightEffectText: lightEffect.GetText(),
			LightEffectUrl:  lightEffect.GetEffectRes(),
		},
	}
	if 0 == flag {
		resp.CurrentHits = s.morph.GetMorphHits()
	} else {
		resp.CurrentHits = 0
	}

	// 异步处理额外奖励
	go func() {
		newCtx, cancel := grpc.NewContextWithInfoTimeout(ctx, 10*time.Second)
		defer cancel()

		err = s.extraAwardMgr.ExtraAward(newCtx, uid, channelId, flag, mode, prize.PackId, uint32(current.Unix()), winning.OperateId)
		if err != nil {
			log.ErrorWithCtx(newCtx, "Smash fail to ExtraAward. in:%+v, err:%v", req, err)
		}
	}()
	return resp, nil
}

func (s *SmashEggServer) checkProbGameCenter(ctx context.Context, orderId string, uid uint32, consumeVal, prizeVal int64) (string, error) {
	quickCtx, cancel := context.WithTimeout(ctx, 500*time.Millisecond)
	defer cancel()

	probReq := &probgamecenter2.CheckFuseReq{
		BusinessId: "smash-egg",
		CheckItemList: []*probgamecenter2.CheckFuseItem{
			{
				ConsumeValue: consumeVal,
				PrizeValue:   prizeVal,
				OrderId:      orderId,
				Uid:          uid,
			},
		},
		DealToken: genDealToken(orderId, uid, prizeVal),
	}

	probResp, err := s.probGameCli.CheckFuse(quickCtx, probReq)
	if err != nil {
		if err.Code() == status.ErrRiskControlPlobGameFuse {
			// 熔断了
			return "", protocol.NewExactServerError(nil, status.ErrSmashEggSystemMaintain)
		}
		log.ErrorWithCtx(ctx, "checkProbGameCenter fail to CheckFuse. %+v, err:%v", probReq, err)
		return "", err
	}

	token := ""
	for _, ret := range probResp.GetCheckResultList() {
		if ret.IsFuse {
			// 熔断了
			return "", protocol.NewExactServerError(nil, status.ErrSmashEggSystemMaintain)
		}

		token = ret.GetDealToken()
	}

	return token, nil
}

func genDealToken(orderId string, uid uint32, totalPrice int64) string {
	t := deal_token.NewDealTokenData(orderId, orderId, "smash-egg", int64(uid), totalPrice)
	dealToken, err := deal_token.Encode(t)
	if err != nil {
		log.Errorf("genDealToken fail to deal_token.Encode. orderId:%s, uid:%d, err:%v", orderId, uid, err)
		return ""
	}

	return dealToken
}

func (s *SmashEggServer) GetSmashConfig(ctx context.Context, req *pb.GetSmashConfigReq) (resp *pb.GetSmashConfigResp, err error) {
	resp = &pb.GetSmashConfigResp{
		Config: &pb.Config{},
	}

	var cfg entity.SystemConfig
	err = s.setting.GetSystemConfig(&cfg)
	if nil != err {
		return resp, toServerError(err)
	}

	if req.GetUid() != 0 {
		err = s.checkSystemConfig(&cfg, req.GetUid())
		if err != nil {
			log.Errorf("checkSystemConfig of %d return %v", req.GetUid(), err)
		} else {
			resp.Accessible = true
		}
	}

	// 旧版转转正式下线后可删除
	actConf, err := s.activityMgr.GetSmashActivityConfWithCache(ctx, false)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetSmashActivityConfWithCache fail: %v", err)
		return resp, toServerError(err)
	}

	resp.Config = &pb.Config{
		MorphHits:      cfg.MorphHits,
		MorphDuration:  cfg.MorphDuration,
		DailyLimit:     cfg.DailyLimit,
		Status:         cfg.Status,
		DeficitLimit:   cfg.DeficitLimit,
		DeficitWarning: cfg.DeficitWarning,
		SpeedStep:      cfg.SpeedStep,
		SpeedUp:        cfg.SpeedUp,
		MaxSpeed:       cfg.MaxSpeed,
		MaxRandN:       cfg.MaxRandN,
		OverallSpeed:   cfg.OverallSpeed,
		RechargePackId: actConf.GetPropGiftPackId(),
	}
	return resp, nil
}

func (s *SmashEggServer) SetSmashConfig(ctx context.Context, req *pb.SetSmashConfigReq) (resp *pb.SetSmashConfigResp, err error) {
	resp = &pb.SetSmashConfigResp{}

	if nil == req.GetConfig() || req.GetConfig().GetStatus() > runCfg.StatusTesting {
		return resp, toServerError(err)
	}

	if runCfg.Production == runCfg.Environment || runCfg.Staging == runCfg.Environment {
		if req.GetConfig().GetOverallSpeed() < 20000 || req.GetConfig().GetMaxRandN() < 20000 || req.GetConfig().GetSpeedUp() < 200 {
			return resp, invalidParam
		}
	}

	err = s.setting.SetSystemConfig(*req.GetConfig())
	if nil != err {
		return resp, toServerError(err)
	}

	log.InfoWithCtx(ctx, "Smash config is now: %+v", *req.GetConfig())
	return resp, nil
}

func (s *SmashEggServer) GetPrizePool(ctx context.Context, req *pb.GetPrizePoolReq) (resp *pb.GetPrizePoolResp, err error) {
	resp = &pb.GetPrizePoolResp{}

	prizes, err := s.magic.GetCurrentPrizes(ctx, uint32(req.GetFlag()), uint32(req.GetMode()))
	if nil != err {
		log.ErrorWithCtx(ctx, "GetCurrentPrizes %v & %v fail: %v", req.GetFlag(), req.GetMode(), err)
		return resp, toServerError(err)
	}

	//只返回未禁用的奖励
	resp.PrizeList = make([]*pb.Prize, len(prizes))
	for i := 0; i < len(prizes); i++ {
		resp.PrizeList[i] = prizeToPB(&prizes[i])
	}
	log.DebugWithCtx(ctx, "GetPrizePool %v & %v -> %v", req.GetFlag(), req.GetMode(), utils.ToJson(resp.GetPrizeList()))
	return resp, nil
}

func (s *SmashEggServer) SetPrizePool(ctx context.Context, req *pb.SetPrizePoolReq) (resp *pb.SetPrizePoolResp, err error) {
	resp = &pb.SetPrizePoolResp{}

	if nil == req.GetPrizeList() {
		req.PrizeList = []*pb.Prize{}
	} else if !businessCfg.GetConfig().SkipPoolProfitSimulate {
		opt := getRuleOption(uint32(req.Mode))
		result, err := s.SimulateWithPrizePool(context.Background(), &pb.SimulateWithPrizePoolReq{
			//SmashProfit: opt.CostAmount * chargePrice,
			SmashTimes: opt.SimulateTimes,
			Flag:       req.Flag,
			Mode:       req.Mode,
			PrizeList:  req.PrizeList,
		})
		if err != nil {
			log.ErrorWithCtx(ctx, "SimulateWithPrizePool fail: %v", err)
			return resp, toServerError(err)
		}

		minPoolProfit := businessCfg.GetConfig().GetModeCfg(uint32(req.GetMode()), uint32(req.GetFlag())).MinPoolProfit
		if result.ExpectProfit < minPoolProfit || result.AverageProfit < minPoolProfit {
			log.ErrorWithCtx(ctx, "simulate get err, exp: %d, avg: %d", result.ExpectProfit, result.AverageProfit)
			return resp, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, fmt.Sprintf("模拟抽奖异常: 期望值：%d 元, 平均值：%d 元； 请重试！", result.ExpectProfit/100, result.AverageProfit/100))
		}
	} else {
		//do nothing
	}

	prizes := make([]entity.Prize, len(req.GetPrizeList()))
	for i := 0; i < len(req.GetPrizeList()); i++ {
		prizes[i] = prizeFromPB(req.PrizeList[i])
	}
	err = s.magic.SetCurrentPrizes(ctx, uint32(req.GetFlag()), uint32(req.GetMode()), req.GetEffectTime(), prizes)
	if nil != err {
		log.ErrorWithCtx(ctx, "SetCurrentPrizes fail: %v", err)
		return resp, toServerError(err)
	}

	log.InfoWithCtx(ctx, "SetPrizePool %v & %v -> %v", req.GetFlag(), req.GetMode(), utils.ToJson(req.GetPrizeList()))
	return resp, nil
}

func (s *SmashEggServer) SimulateWithPrizePool(ctx context.Context, req *pb.SimulateWithPrizePoolReq) (resp *pb.SimulateWithPrizePoolResp, err error) {
	resp = &pb.SimulateWithPrizePoolResp{}

	// 用于耗时统计
	begin := time.Now()

	if req.GetSmashTimes() == 0 || 0 == len(req.GetPrizeList()) {
		return resp, invalidParam
	}

	for i := 0; i < len(req.GetPrizeList()); i++ {
		prize := req.GetPrizeList()[i]
		if prize.Flag != req.GetFlag() || prize.Status != pb.Status_ENABLE {
			return resp, invalidParam
		}
	}

	actCfg, err := s.activityMgr.GetSmashActivityConfWithCache(ctx, false)
	if err != nil {
		log.ErrorWithCtx(ctx, "Simulate failed to GetSmashActivityConfWithCache: %v", err)
		return resp, err
	}

	modeCfg := businessCfg.GetConfig().GetModeCfg(uint32(req.GetMode()), uint32(req.GetFlag()))
	if modeCfg == nil || modeCfg.LotteryCostNum == 0 {
		log.ErrorWithCtx(ctx, "Simulate failed to GetSmashActivityConfWithCache: lost modeCfg")
		return resp, invalidParam
	}

	smashProfit := actCfg.GetPropPrice() * modeCfg.LotteryCostNum

	//计算期望值
	prizes := make([]entity.Prize, len(req.GetPrizeList()))
	for i := 0; i < len(req.GetPrizeList()); i++ {
		prizes[i] = prizeFromPB(req.GetPrizeList()[i])
	}

	var totalWeight uint32
	for i := 0; i < len(prizes); i++ {
		totalWeight += prizes[i].Weight
	}

	var expectProfit float64
	for i := 0; i < len(prizes); i++ {
		if smashProfit == prizes[i].PackWorth {
			continue
		}
		profit := (float64(prizes[i].Weight) / float64(totalWeight)) * (float64(smashProfit) - float64(prizes[i].PackWorth))
		expectProfit += profit
	}

	//模拟砸蛋，然后统计收益
	result, err := s.magic.Simulate(uint32(req.GetFlag()), uint32(req.GetMode()), req.GetSmashTimes(), prizes)
	if err != nil {
		log.ErrorWithCtx(ctx, "Simulate failed: %v", err)
		return resp, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, err.Error())
	}

	var totalWorth float64
	for i := 0; i < len(result); i++ {
		totalWorth += float64(result[i].PackWorth)
	}

	avgProfit := (float64(req.GetSmashTimes()*smashProfit) - totalWorth) / float64(req.GetSmashTimes())
	resp = &pb.SimulateWithPrizePoolResp{
		ExpectProfit:  int32(expectProfit),
		AverageProfit: int32(avgProfit),
	}

	log.InfoWithCtx(ctx, "simulate with %v takes %v, expect=%v, avg=%v", utils.ToJson(req), time.Since(begin), resp.ExpectProfit, resp.AverageProfit)
	return resp, nil
}

// Notify 充值回调
func (s *SmashEggServer) Notify(ctx context.Context, req *UnifiedPayCallback.PayNotify) (resp *UnifiedPayCallback.PayNotifyResponse, err error) {
	resp = &UnifiedPayCallback.PayNotifyResponse{}

	log.InfoWithCtx(ctx, "Receive pay notify: %s", utils.ToJson(req))

	svrResp, err := s.GetConsumeRecord(ctx, &pb.GetConsumeRecordReq{
		Uid:     req.Uid,
		OrderId: req.OutTradeNo,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "Notify GetConsumeRecord error : %v", err)
		return resp, err
	}

	if nil == svrResp || nil == svrResp.ConsumeRecordList || 0 == len(svrResp.ConsumeRecordList) {
		// 查不到记录，限制时间内不回滚
		at := uint32(time.Now().Unix())
		if req.CreateAt > at || at-req.CreateAt < businessCfg.GetConfig().RechargeRollbackAfter {
			log.ErrorWithCtx(ctx, "Notify record not ready, rollback after %ds", businessCfg.GetConfig().RechargeRollbackAfter)
			return resp, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, fmt.Sprintf("订单不存在，%ds后才允许回滚", businessCfg.GetConfig().RechargeRollbackAfter))
		}
		resp.Confirmed = true
		resp.Op = UnifiedPayCallback.Op_ROLLBACK
	} else {
		resp.Confirmed = true
		resp.Op = UnifiedPayCallback.Op_COMMIT
	}

	log.InfoWithCtx(ctx, "Receive pay notify return: %s", utils.ToJson(resp))
	return resp, nil
}

func (s *SmashEggServer) CheckWhitelist(ctx context.Context, req *pb.CheckWhitelistReq) (resp *pb.CheckWhitelistResp, err error) {
	resp = &pb.CheckWhitelistResp{}
	resp.Exist, err = s.cache.CheckWhitelist(req.Uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "CheckWhitelist %u fail", req.Uid, err)
		return resp, toServerError(err)
	}
	return resp, nil
}

func (s *SmashEggServer) CheckTester(ctx context.Context, req *pb.CheckTesterReq) (resp *pb.CheckTesterResp, err error) {
	resp = &pb.CheckTesterResp{}
	resp.Exist, err = s.cache.CheckTester(req.Uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "CheckTester %u fail", req.Uid, err)
		return resp, toServerError(err)
	}
	return resp, nil
}

func (s *SmashEggServer) checkSystemConfig(cfg *entity.SystemConfig, uid uint32) error {
	switch cfg.Status {
	case runCfg.StatusDisable:
		return errors.New("magic service is close")
	case runCfg.StatusErr:
		return errors.New("magic service is err")
	case runCfg.StatusTesting:
		var ok bool
		ok, err := s.cache.CheckTester(uid)
		if err != nil {
			log.Errorf("CheckTester fail: %s", err.Error())
			return err
		}
		if !ok {
			return errors.New("magic service is testing")
		}
	}

	if runCfg.Production == runCfg.Environment || runCfg.Staging == runCfg.Environment {
		if cfg.OverallSpeed < 20000 || cfg.MaxRandN < 20000 || cfg.SpeedUp < 200 {
			log.Errorf("system config is invalid")
			return errors.New("magic service is err")
		}
	}
	return nil
}

//func (s *SmashEggServer) timeoutContextDo(timeout time.Duration, f func(ctx context.Context) (interface{}, error)) (rs interface{}, err error) {
//	ctx, cancel := context.WithTimeout(context.Background(), timeout)
//	rs, err = f(ctx)
//	cancel()
//	return rs, err
//}

func (s *SmashEggServer) doRecharge(ctx context.Context, tx interface{}, uid, amount, fee, createTime uint32, orderId string, actCfg *pb.SmashActivityConfig) (consume entity.ConsumeRecord, err error) {
	//防止事务异常
	defer func() {
		if e := recover(); e != nil {
			log.ErrorWithCtx(ctx, "Failed to _onChannelEvent err:%v, stack: %v", e, string(debug.Stack()))
			err = errors.New("go panic")
		}
	}()

	rechargeAt := time.Unix(int64(createTime), 0)

	//插入消费记录
	opId := s.operateId(uid, "c", uint32(pb.Mode_NORMAL_MODE), actCfg.GetRiskId())
	consume = entity.ConsumeRecord{
		Uid:       uid,
		Amount:    amount,
		Fee:       fee,
		OrderId:   orderId,
		OperateId: opId,
		//RemainChance: restChance,
		CreateTime: rechargeAt,
	}

	err = s.store.InsertConsumeRecordV2(ctx, tx, s.store.GetConsumeTableSuffixV2(createTime, time.Now()), []entity.ConsumeRecord{
		consume,
	})
	if nil != err {
		log.ErrorWithCtx(ctx, "doRecharge fail to InsertConsumeRecord. uid:%d, orderId:%s, err:%v", uid, orderId, err)
		return consume, err
	}

	// 插入winning记录，方便数据对账，这样在winning表可以清晰的看到剩余次数的修改
	w := entity.WinningRecord{
		Uid:        uid,
		Source:     specialSourceRecharge,
		PackId:     actCfg.GetPropGiftPackId(),
		CreateTime: rechargeAt,
		OperateId:  opId,
		//RemainChance: restChance,
		Status: uint32(1),
	}
	err = s.store.InsertWinningRecordV2(ctx, tx, s.store.GetWinningTableSuffixV2(uid, createTime, time.Now()), []entity.WinningRecord{w})
	if nil != err {
		log.ErrorWithCtx(ctx, "doRecharge fail to InsertWinningRecordV2. uid:%d, orderId:%s, err:%v", uid, orderId, err)
		return consume, err
	}

	propDuration := actCfg.GetPropDuration()
	err = s.prop.AddUserProp(ctx, tx.(*sqlx.Tx), opId, uid, actCfg.GetSmashPropId(), amount, propDuration, rechargeAt)
	if nil != err {
		log.ErrorWithCtx(ctx, "doRecharge fail to AddUserProp. uid:%d, orderId:%s, err:%v", uid, orderId, err)
		return consume, err
	}

	return consume, nil
}

// doSmash 此处返回结果以及缓存奖品数据不采用指针，目的是为防止奖品不慎被外部修改
func (s *SmashEggServer) doSmash(ctx context.Context, tx interface{}, current time.Time, deficitLimit, uid, channelId, source, morph, /*maxN, maxSpeed,*/ mode uint32, actCfg *pb.SmashActivityConfig) (prize entity.Prize, winning entity.WinningRecord, dealToken string, err error) {
	//防止事务异常
	defer func() {
		if e := recover(); e != nil {
			log.ErrorWithCtx(ctx, "Failed to _onChannelEvent err:%v, stack: %v", e, string(debug.Stack()))
			err = errors.New("go panic")
		}
	}()

	//ruleOpt := getRuleOption(mode)
	modeCfg := businessCfg.GetConfig().GetModeCfg(mode, morph)
	if modeCfg == nil {
		return prize, winning, dealToken, invalidParam
	}

	costNum := modeCfg.LotteryCostNum
	opId := s.operateId(uid, "w", mode, actCfg.GetRiskId())

	//获取当前保底值
	randVal, exist, err := s.store.GetUserGuaranteedByMode(ctx, uid, mode, morph)
	if nil != err {
		log.ErrorWithCtx(ctx, "doSmash fail to GetRemainChanceForUpdate. uid:%d, current:%v, err:%v", uid, current, err)
		return prize, winning, dealToken, err
	}

	//超过30天的重置N值
	expireDuration := time.Duration(int64(businessCfg.GetConfig().GuaranteedExpireTs)) * time.Second
	if !exist || randVal.UpdateTime.Add(expireDuration).Before(time.Now()) {
		randVal = &store.UserGuaranteed{
			Uid:     uid,
			Mode:    mode,
			Flag:    morph,
			CurrVal: 0,
			GoalVal: modeCfg.MaxGuaranteedVal,
		}
	}

	//var newN, bingoScene, newSpeed uint32
	//prize, newN, newSpeed, bingoScene, err = s.magic.Turn(ctx, mode, morph, costNum, randVal.RandN, maxN, randVal.Speed, maxSpeed, actCfg.GetBingoPackId())

	var newGVal, bingoScene uint32
	prize, newGVal, bingoScene, err = s.magic.TurnV2(ctx, mode, morph, randVal.CurrVal, randVal.GoalVal, modeCfg.BingoPackId)
	if err != nil {
		log.ErrorWithCtx(ctx, "doSmash fail to Turn. uid:%d, current:%v, err:%v", uid, current, err)
		return prize, winning, dealToken, err
	}

	//风控逻辑
	var profit int64
	profit, err = s.addProfit(current, int64(actCfg.GetPropPrice()*costNum)-int64(prize.PackWorth))
	if err != nil {
		log.ErrorWithCtx(ctx, "doSmash fail to addProfit. uid:%d, current:%v, err:%v", uid, current, err)
		return prize, winning, dealToken, err
	}
	if 0 != deficitLimit && profit+int64(deficitLimit) < 0 {
		//err = errors.New("magic service is err")
		log.ErrorWithCtx(ctx, "doSmash >> deficit %d is bigger than limit %d. uid:%d", profit, deficitLimit, uid)
		return prize, winning, dealToken, protocol.NewExactServerError(nil, status.ErrSmashEggSystemMaintain)
	}

	//插入中奖记录
	winning = entity.WinningRecord{
		Uid:       uid,
		Channel:   channelId,
		Flag:      morph,
		Source:    source,
		Mode:      mode,
		PackId:    prize.PackId,
		PackWorth: prize.PackWorth,
		OperateId: opId,
		//RemainChance: restChance,
		//RandN:         newN,
		Speed:         newGVal,
		OriginN:       randVal.GoalVal,
		BingoType:     bingoScene,
		CreateTime:    current,
		CostChanceNum: costNum,
	}

	// 概率玩法风控中心检查
	dealToken, err = s.checkProbGameCenter(ctx, opId, uid, int64(actCfg.GetPropPrice()*costNum), int64(prize.PackWorth))
	if nil != err {
		log.ErrorWithCtx(ctx, "doSmash fail to checkProbGameCenter. uid:%d, current:%v, err:%v", uid, current, err)
		return prize, winning, dealToken, err
	}

	err = s.store.InsertWinningRecordV2(ctx, tx, s.store.GetWinningTableSuffixV2(uid, 0, current), []entity.WinningRecord{
		winning,
	})
	if nil != err {
		log.ErrorWithCtx(ctx, "doSmash fail to InsertWinningRecordV2. uid:%d, current:%v, err:%v", uid, current, err)
		return prize, winning, dealToken, err
	}

	// 消耗道具
	err = s.prop.UseUserProp(ctx, tx.(*sqlx.Tx), opId, uid, actCfg.GetSmashPropId(), costNum, current)
	if nil != err {
		log.ErrorWithCtx(ctx, "doSmash fail to UseUserProp. uid:%d, current:%v, err:%v", uid, current, err)
		return prize, winning, dealToken, err
	}

	randVal.CurrVal = newGVal
	if newGVal == 0 {
		randVal.GoalVal = modeCfg.MaxGuaranteedVal
	}
	// 更新保底值
	err = s.store.UpsertUserGuaranteed(ctx, tx.(*sqlx.Tx), randVal)
	if nil != err {
		log.ErrorWithCtx(ctx, "doSmash fail to UpdateRandNSpeed. uid:%d, current:%v, err:%v", uid, current, err)
		return prize, winning, dealToken, err
	}
	return prize, winning, dealToken, nil
}

func (s *SmashEggServer) getProfit(t time.Time) (profit int64, err error) {
	profit, err = s.cache.GetSubReport(t.Format(timeLayoutHourly), "profit")
	if err != nil {
		log.Errorf("get profit report failed, err: %v", err)
		return
	}
	return
}

func (s *SmashEggServer) addProfit(t time.Time, increment int64) (profit int64, err error) {
	profit, err = s.cache.AddSubReport(t.Format(timeLayoutHourly), "profit", increment)
	if err != nil {
		log.Errorf("add profit report failed, err: %v", err)
		return
	}
	return
}

func (s *SmashEggServer) deleteProfit(t time.Time) (err error) {
	_, err = s.cache.DelAllSubConfig(t.Format(timeLayoutHourly))
	if err != nil {
		log.Errorf("del profit report failed, err: %v", err)
		return
	}
	return
}

/*
func (s *SmashEggServer) getSpeed(ctx context.Context, uid uint32) (exist bool, chance uint32, err error) {
	exist, chance, err = s.cache.GetUserSpeed(uid)
	if nil != err {
		log.ErrorWithCtx(ctx, "get remain chance failed, err: %v", err)
		return
	}

	if exist {
		return
	}

	remain, err := s.store.GetRemainChanceAndSpeed(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "get remain chance failed, err: %v", err)
		return
	}

	if nil == remain {
		return
	}

	_, _ = s.cache.SetUserRemainChance(uid, remain.Amount)
	return true, remain.Amount, nil
}
*/

func (s *SmashEggServer) GetUserPropList(ctx context.Context, uid, propId uint32) ([]*pb.UserProp, uint32, error) {
	propList := make([]*pb.UserProp, 0)
	resp, err := s.prop.GetUserProp(ctx, uid)
	if nil != err {
		log.ErrorWithCtx(ctx, "GetUserPropList failed to GetUserProp, uid:%d, err:%v", uid, err)
		return propList, 0, err
	}

	now := time.Now()
	totalNum := uint32(0)

	for _, propInfo := range resp.GetUserPropList() {
		if propInfo.Num == 0 || propInfo.PropId != propId ||
			now.Unix() >= propInfo.ExpireTime {
			continue
		}

		propList = append(propList, propInfo)
		totalNum += propInfo.Num
	}

	return propList, totalNum, nil
}

func (s *SmashEggServer) getRemainChance(ctx context.Context, uid uint32) (chance, speed uint32, err error) {
	var speedExist bool
	//chanceExist, chance, err = s.cache.GetUserRemainChance(uid)
	//if nil != err {
	//	log.ErrorWithCtx(ctx, "get remain chance failed, err: %v", err)
	//	return
	//}

	actCfg, err := s.activityMgr.GetSmashActivityConfWithCache(ctx, false)
	if err != nil {
		log.ErrorWithCtx(ctx, "getRemainChance fail to GetSmashActivityConfWithCache, uid:%d, err: %v", uid, err)
		return 0, 0, err
	}

	_, chance, err = s.GetUserPropList(ctx, uid, actCfg.GetSmashPropId())
	if nil != err {
		log.ErrorWithCtx(ctx, "getRemainChance failed to GetUserPropList, uid:%d, err:%v", uid, err)
		return
	}

	speedExist, speed, err = s.cache.GetUserSpeed(uid)
	if nil != err {
		log.ErrorWithCtx(ctx, "get remain chance failed, err: %v", err)
		return
	}

	if speedExist {
		return
	}

	remain, err := s.store.GetRemainChanceAndSpeed(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "get remain chance failed, err: %v", err)
		return
	}

	if nil == remain {
		return
	}

	//_, _ = s.cache.SetUserRemainChance(uid, remain.Amount)
	_, _ = s.cache.SetUserSpeed(uid, remain.Speed)

	return chance, remain.Speed, nil
}

func (s *SmashEggServer) checkSmashStatus(ctx context.Context, cfg *entity.SystemConfig, uid uint32) (hits, chance, deficit, morph uint32, err error) {
	//获取砸蛋次数
	hits, err = s.cache.GetUserHits(uid)
	if nil != err {
		log.ErrorWithCtx(ctx, "get my hits failed, err: %v", err)
		return
	}

	if hits >= cfg.DailyLimit {
		err = protocol.NewExactServerError(nil, status.ErrSmashEggReachDailyLimit)
		return
	}

	chance, _, err = s.getRemainChance(ctx, uid)
	if nil != err {
		log.ErrorWithCtx(ctx, "get remain chance failed, err: %v", err)
		return
	}

	if chance == 0 {
		err = protocol.NewExactServerError(nil, status.ErrSmashEggChanceNotEnough)
		return
	}
	deficit = cfg.DeficitLimit

	//判断是否变身状态
	ok, end, err := s.morph.Morphing()
	if err != nil {
		log.ErrorWithCtx(ctx, "get morph failed, err: %v", err)
		return
	}

	//变身标志存在且时间未大于当前时间才认为是变身状态
	if ok && time.Now().Before(end) {
		morph = 1
	}
	return
}

func (s *SmashEggServer) operateId(uid uint32, ty string, mode uint32, backpackKey uint32) string {
	//cfg := businessCfg.GetConfig()

	//_, _, prefix := cfg.GetBackpackBusiness(mode)

	t := time.Now()
	ms := t.UnixNano() / 1e6
	sec := ms / 1e3
	return fmt.Sprintf("%d_mg_%s_%d_%d_%d", backpackKey, ty, uid, sec, ms-sec*1e3)
}

/*
func (s *SmashEggServer) bingoNotify(ctx context.Context, uid, channelId uint32, prize entity.Prize, actCfg *pb.SmashActivityConfig) (err error) {
	cfg, err := s.backpackClient.GetPackageItemCfg(ctx, prize.PackId)
	if nil != err {
		log.ErrorWithCtx(ctx, "bingoNotify failed to GetPackageItemCfg. uid:%d, err:%v", uid, err)
		return
	}

	if len(cfg.GetItemCfgList()) == 0 {
		log.Errorf("bingoNotify fail.  packId:%d, len(items) == 0 ", prize.PackId)
		return protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "大奖包裹配置有误")
	}

	pkgItemCfg := cfg.GetItemCfgList()[0]
	expireDays := pkgItemCfg.GetDynamicFinTime() / 24 / 3600
	ttMsg := businessCfg.GetConfig().TTNotifyMsg
	ttMsg = strings.ReplaceAll(ttMsg, "{giftName}", prize.PackName)
	ttMsg = strings.ReplaceAll(ttMsg, "{awardDays}", fmt.Sprint(expireDays))

	err = s.sendTTMsg(ctx, uid, ttMsg)
	if nil != err {
		log.ErrorWithCtx(ctx, "bingoNotify failed to sendTTMsg. uid:%d, err:%v", uid, err)
		return
	}

	// 全服公告
	if businessCfg.GetConfig().BreakingNewsId > 0 {
		err = s.broadcastGetHonourPresent(ctx, uid, channelId, prize.PackId, prize.PackAmount, actCfg)
		if nil != err {
			log.ErrorWithCtx(ctx, "bingoNotify failed to broadcastGetHonourPresent. uid:%d, err:%v", uid, err)
			return
		}

		if !businessCfg.GetConfig().BingoUserSearchEnable {
			// 用户禁止被搜索
			err = s.unifiedSearchClient.AddRisky(ctx, unifiedSearchPB.AddRiskyReq_EGG, &unifiedSearchPB.RiskyObject{
				Id:         uid,
				ObjectType: unifiedSearchPB.ObjectType_USER,
			})
			if err != nil {
				log.ErrorWithCtx(ctx, "bingoNotify fail to unified search AddRisky, uid: %d, err: %v", uid, err)
			}
		}
	}

	return
}

func (s *SmashEggServer) broadcastGetHonourPresent(ctx context.Context, uid, channelId, packId uint32, presentAmount uint32, actCfg *pb.SmashActivityConfig) (err error) {
	newsId := businessCfg.GetConfig().BreakingNewsId
	if newsId == 0 {
		log.ErrorWithCtx(ctx, "broadcastGetHonourPresent failed, BreakingNewId is 0. actCfg:%+v", actCfg)
		return protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "BreakingNewId is 0")
	}

	giftInfo, err := s.lightMgr.LoadGiftInfo(ctx, packId)
	if nil != err {
		log.ErrorWithCtx(ctx, "broadcastGetHonourPresent failed to LoadGiftInfo. packId:%d, err:%v", packId, err)
		return err
	}

	msg := &publicNoticePb.CommonBreakingNewsV3{
		FromUid:   uid,
		ChannelId: channelId,
		//NewsPrefix:  "难以置信！",
		NewsContent: actCfg.GetActivityName(),
		BreakingNewsBaseOpt: &publicNoticePb.CommBreakingNewsBaseOpt{
			RollingCount:     1,
			RollingTime:      10,
			AnnounceScope:    uint32(ga.CommBreakingNewsBaseOpt_INSIDE_CHANNEL),
			AnnouncePosition: uint32(ga.CommBreakingNewsBaseOpt_UPPER),
			JumpType:         uint32(ga.CommBreakingNewsBaseOpt_JUMP_CLICK_INSIDE),
			JumpPosition:     uint32(ga.CommBreakingNewsBaseOpt_JUMP_SMASH_EGGS_UI_CLICK),
			TriggerType:      uint32(ga.CommBreakingNewsBaseOpt_SMASH_EGG_BINGO_WITH_RICH_TEXT_NEWS),
		},
		JumpUrl: "tt://m.52tt.com/magic_turn",
		PresentNewsBaseOpt: &publicNoticePb.PresentBreakingNewsBaseOpt{
			GiftId:      giftInfo.Id,
			GiftName:    giftInfo.Name,
			GiftIconUrl: giftInfo.Icon,
			GiftWorth:   giftInfo.Worth,
			GiftCount:   presentAmount,
		},
	}

	_, err = s.publicNoticeCli.PushBreakingNews(ctx, &publicNoticePb.PushBreakingNewsReq{
		CommonBreakingNews: msg,
		RichTextNews: &publicNoticePb.RichTextNews{
			NewsId: newsId,
		},
	})
	if nil != err {
		log.ErrorWithCtx(ctx, "broadcastGetHonourPresent failed. actCfg:%+v, msg:%v err:%v", actCfg, msg, err)
		return err
	}

	log.DebugWithCtx(ctx, "broadcastGetHonourPresent. newsId:%d, msg:%v", newsId, msg)
	return nil
}

*/

// 发送TT助手消息
func (s *SmashEggServer) sendTTMsg(ctx context.Context, uid uint32, content string) error {
	msg := &apiPB.ImMsg{
		ImType: &apiPB.ImType{
			SenderType:   uint32(apiPB.IM_SENDER_TYPE_IM_SENDER_NORMAL),
			ReceiverType: uint32(apiPB.IM_RECEIVER_TYPE_IM_RECEIVER_USER),
			ContentType:  uint32(apiPB.IM_CONTENT_TYPE_IM_CONTENT_TEXT),
		},
		FromUid: uint32(10000),
		ToIdList: []uint32{
			uid,
		},
		ImContent: &apiPB.ImContent{
			TextNormal: &apiPB.ImTextNormal{
				Content: content,
			},
		},
		Platform:    apiPB.Platform_UNSPECIFIED,
		AppPlatform: "all",
	}

	//ctx, cancel := context.WithTimeout(context.Background(), time.Second*3)
	err := s.apiCenterClient.SendImMsg(ctx, uid, protocol.TT, []*apiPB.ImMsg{msg}, true)
	//cancel()
	if err != nil {
		log.ErrorWithCtx(ctx, "SendImMsg fail. uid:%d err: %s", uid, err.Error())
		return err
	}
	notify.NotifySync(uid, appsync.SyncReq_IM_MSG)
	return nil
}

func prizeFromPB(prize *pb.Prize) entity.Prize {
	return entity.Prize{
		Id:     prize.Id,
		Flag:   uint32(prize.Flag),
		Mode:   uint32(prize.Mode),
		Status: uint32(prize.Status),

		Weight:     prize.Weight,
		PackId:     prize.PackId,
		PackWorth:  prize.PackWorth,
		PackName:   prize.PackName,
		PackAmount: prize.PackAmount,
		PackPic:    prize.PackPic,
		PackDesc:   prize.PackDesc,
	}
}

func prizeToPB(prize *entity.Prize) *pb.Prize {
	return &pb.Prize{
		Id:         prize.Id,
		Flag:       pb.Flag(prize.Flag),
		Status:     pb.Status(prize.Status),
		Weight:     prize.Weight,
		PackId:     prize.PackId,
		PackWorth:  prize.PackWorth,
		PackName:   prize.PackName,
		PackAmount: prize.PackAmount,
		PackPic:    prize.PackPic,
		PackDesc:   prize.PackDesc,
	}
}

// 屏蔽系统错误，避免泄露敏感信息
func toServerError(err error) protocol.ServerError {
	if err == nil {
		return nil
	}

	if e, ok := err.(protocol.ServerError); ok {
		return e
	}
	return defaultErr
}

var ReconcileMaxDuration = 24 * time.Hour

func (s *SmashEggServer) GetAwardTotalCount(ctx context.Context, in *reconcile_v2.TimeRangeReq) (*reconcile_v2.CountResp, error) {
	out := &reconcile_v2.CountResp{}
	beginTime := time.Unix(in.GetBeginTime(), 0)
	endTime := time.Unix(in.GetEndTime(), 0)
	if beginTime.After(endTime) || beginTime.Add(ReconcileMaxDuration).Before(endTime) {
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "time range over")
	}

	info, err := s.readonlyStore.GetWinningTotalCountInfoV2(ctx, beginTime, endTime)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAwardTotalCount fail to GetWinningTotalCountInfo. b:%v, e:%v, err:%v", beginTime, endTime, err)
		return out, err
	}

	out.Count = uint32(info.Count)
	out.Value = uint32(info.Worth)

	return out, nil
}

func (s *SmashEggServer) GetAwardOrderIds(ctx context.Context, in *reconcile_v2.TimeRangeReq) (*reconcile_v2.OrderIdsResp, error) {
	out := &reconcile_v2.OrderIdsResp{}
	beginTime := time.Unix(in.GetBeginTime(), 0)
	endTime := time.Unix(in.GetEndTime(), 0)
	if beginTime.After(endTime) || beginTime.Add(ReconcileMaxDuration).Before(endTime) {
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "time range over")
	}

	var err error
	out.OrderIds, err = s.readonlyStore.GetWinningOrderIdListV2(ctx, beginTime, endTime)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAwardOrderIds fail to GetWinningOrderIdList. b:%v, e:%v, err:%v", beginTime, endTime, err)
		return out, err
	}

	return out, nil
}

func (s *SmashEggServer) GetConsumeTotalCount(ctx context.Context, in *reconcile_v2.TimeRangeReq) (*reconcile_v2.CountResp, error) {
	out := &reconcile_v2.CountResp{}
	beginTime := time.Unix(in.GetBeginTime(), 0)
	endTime := time.Unix(in.GetEndTime(), 0)
	if beginTime.After(endTime) || beginTime.Add(ReconcileMaxDuration).Before(endTime) {
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "time range over")
	}

	info, err := s.readonlyStore.GetConsumeTotalCountInfoV2(ctx, beginTime, endTime)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetConsumeTotalCount fail to GetConsumeTotalCountInfo. b:%v, e:%v, err:%v", beginTime, endTime, err)
		return out, err
	}

	out.Count = uint32(info.Count)
	out.Value = uint32(info.Worth)

	return out, nil
}

func (s *SmashEggServer) GetConsumeOrderIds(ctx context.Context, in *reconcile_v2.TimeRangeReq) (*reconcile_v2.OrderIdsResp, error) {
	out := &reconcile_v2.OrderIdsResp{}
	beginTime := time.Unix(in.GetBeginTime(), 0)
	endTime := time.Unix(in.GetEndTime(), 0)
	if beginTime.After(endTime) || beginTime.Add(ReconcileMaxDuration).Before(endTime) {
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "time range over")
	}

	var err error
	out.OrderIds, err = s.readonlyStore.GetConsumeOrderIdListV2(ctx, beginTime, endTime)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetConsumeOrderIds fail to GetConsumeOrderIdList. b:%v, e:%v, err:%v", beginTime, endTime, err)
		return out, err
	}

	return out, nil
}

func (s *SmashEggServer) GetPrizePoolTmp(ctx context.Context, req *pb.GetPrizePoolTmpReq) (*pb.GetPrizePoolTmpResp, error) {
	resp := &pb.GetPrizePoolTmpResp{}

	tmpList, err := s.magic.GetPrizesPoolTmp(ctx, uint32(req.GetFlag()), uint32(req.GetMode()))
	if err != nil {
		log.Errorf("GetPrizePoolTmp fail to GetPrizesPoolTmp, err:%v", err)
		return resp, err
	}
	for _, v := range tmpList {
		resp.PrizeList = append(resp.PrizeList, &pb.Prize{
			Id:         v.Id,
			Flag:       pb.Flag(v.Flag),
			Status:     pb.Status(v.Status),
			Weight:     v.Weight,
			PackId:     v.PackId,
			PackWorth:  v.PackWorth,
			PackName:   v.PackName,
			PackPic:    v.PackPic,
			PackAmount: v.PackAmount,
			PackDesc:   v.PackDesc,
			CreateTime: v.CreateTime,
			Mode:       pb.Mode(v.Mode),
		})
		resp.EffectTime = v.BeginTime
	}

	return resp, nil
}

func (s *SmashEggServer) DelTmpPrizePool(ctx context.Context, req *pb.DelTmpPrizePoolReq) (*pb.DelTmpPrizePoolResp, error) {
	resp := &pb.DelTmpPrizePoolResp{}

	t := time.Unix(int64(req.EffectTime), 0)
	err := s.magic.DelPrizesPoolTmp(ctx, uint32(req.GetFlag()), uint32(req.GetMode()), t)
	if err != nil {
		log.ErrorWithCtx(ctx, "DelTmpPrizePool fail,err:%v", err)
		return resp, err
	}

	return resp, nil
}

func (s *SmashEggServer) GetSmashEggExemptValue(ctx context.Context, req *pb.GetSmashEggExemptValueReq) (*pb.GetSmashEggExemptValueResp, error) {
	out := &pb.GetSmashEggExemptValueResp{}
	var err error

	uid := req.GetUid()

	out.SmashFlag, err = s.cache.GetUserSmashFlag(uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetSmashEggExemptValue GetUserSmashFlag uid:%d err:%v", uid, err)
		// return
	}

	out.RemainChance, out.LuckValue, err = s.getRemainChance(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetSmashEggExemptValue GetUserRemainChance uid:%d err:%v",
			uid, err)
		// return
	}

	return out, nil
}

// GetUserGuaranteedMap 获取用户的各模式的保底值
func (s *SmashEggServer) GetUserGuaranteedMap(ctx context.Context, uid uint32) (map[uint32]map[uint32]*store.UserGuaranteed, error) {
	mode2Flag2Info, exist, err := s.cache.GetUserGuaranteedInfo(uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetUserGuaranteedMap fail to redisCache.GetUserGuaranteedInfo. uid:%d, err:%v", uid, err)
		return mode2Flag2Info, err
	}

	if !exist {
		list, err := s.store.GetUserGuaranteed(ctx, uid)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetUserGuaranteedMap fail to store.GetUserGuaranteed. uid:%d, err:%v", uid, err)
			return mode2Flag2Info, err
		}

		err = s.cache.SetUserGuaranteedInfo(uid, list)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetUserGuaranteedMap fail to redisCache.SetUserGuaranteedInfo. uid:%d, err:%v", uid, err)
		}

		for _, info := range list {
			if _, ok := mode2Flag2Info[info.Mode]; !ok {
				mode2Flag2Info[info.Mode] = map[uint32]*store.UserGuaranteed{}
			}

			mode2Flag2Info[info.Mode][info.Flag] = info
		}
	}

	//超过过期时长的重置保底值
	expireDuration := time.Duration(int64(businessCfg.GetConfig().GuaranteedExpireTs)) * time.Second
	for _, flag2Info := range mode2Flag2Info {
		for _, info := range flag2Info {
			if info.UpdateTime.Add(expireDuration).Before(time.Now()) {
				info.CurrVal = 0

				modeCfg := businessCfg.GetConfig().GetModeCfg(info.Mode, info.Flag)
				if modeCfg != nil {
					info.GoalVal = modeCfg.MaxGuaranteedVal
				}
			}
		}
	}

	return mode2Flag2Info, nil
}
