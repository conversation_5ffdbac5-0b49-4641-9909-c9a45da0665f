package store

//合成物品相关
import (
	"context"
	"fmt"
	mysql_driver "github.com/go-sql-driver/mysql"
	"golang.52tt.com/pkg/log"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"time"
)

const CONVERSION_ITEM_TBL = `create table %s (
	log_id int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'id',
	order_id varchar(125) not null COMMENT '订单ID',
	uid int(10) unsigned not null COMMENT 'UID',
	user_item_id int(10) unsigned not null COMMENT '物品source_id',
	item_type tinyint unsigned not null COMMENT '类型',
	source tinyint unsigned not null COMMENT '来源',
	source_app_id varchar(64) NOT NULL COMMENT '物品详细来源',
	source_type int(10) unsigned not null COMMENT '物品source_id',
	total_price int unsigned NOT NULL default 0 COMMENT '物品总价值',
	use_count int(10) unsigned not null COMMENT '使用数量',
	outside_time timestamp not null COMMENT '订单时间',
	create_time timestamp not null default CURRENT_TIMESTAMP COMMENT '创建时间',
	bg_id int(10) not null DEFAULT 0 COMMENT '合成包裹ID',
	bg_num int(10) not null DEFAULT 0 COMMENT '合成包裹数量',
	primary key(log_id),
	index order_id_idx(order_id),
	index create_time_index(create_time),
	index outside_time_index(outside_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;`

type ConversionCostItemRecord struct {
	ID          uint32    `gorm:"column:log_id" db:"log_id" json:"log_id"`                      //  id
	OrderId     string    `gorm:"column:order_id" db:"order_id" json:"order_id"`                //  订单id
	Uid         uint32    `gorm:"column:uid" db:"uid" json:"uid"`                               //  uid
	SourceId    uint32    `gorm:"column:user_item_id" db:"user_item_id" json:"user_item_id"`    //  物品source_id
	ItemType    uint32    `gorm:"column:item_type" db:"item_type" json:"item_type"`             //  类型
	Source      uint32    `gorm:"column:source" db:"source" json:"source"`                      //  来源
	SourceAppId string    `gorm:"column:source_app_id" db:"source_app_id" json:"source_app_id"` //  物品详细来源
	SourceType  uint32    `gorm:"column:source_type" db:"source_type" json:"source_type"`       //  物品source_id
	TotalPrice  uint32    `gorm:"column:total_price" db:"total_price" json:"total_price"`       //  物品总价值
	UseCount    uint32    `gorm:"column:use_count" db:"use_count" json:"use_count"`             //  使用数量
	OutsideTime time.Time `gorm:"column:outside_time" db:"outside_time" json:"outside_time"`    //  订单时间
	CreateTime  time.Time `gorm:"column:create_time" db:"create_time" json:"create_time"`       //  创建时间
	BgId        uint32    `gorm:"column:bg_id" db:"bg_id" json:"bg_id"`                         //  合成包裹ID
	BgNum       uint32    `gorm:"column:bg_num" db:"bg_num" json:"bg_num"`                      //  合成包裹数量
}

func (m *ConversionCostItemRecord) TableName() string {
	return fmt.Sprintf("user_backpack_conversion_cost_item_month_%s", m.OutsideTime.Format("200601"))
}

func (m *ConversionCostItemRecord) CreateTable(db *gorm.DB) {
	tb := m.TableName()
	if db.Migrator().HasTable(tb) {
		log.Infof("CreateTable HasTable:%s", tb)
		return
	}
	sql := fmt.Sprintf(CONVERSION_ITEM_TBL, tb)
	err := db.Exec(sql).Error
	log.Infof("CONVERSION_ITEM_TBL:%s,  err:%v", tb, err)
}

func (s *Store) TXAddConversionCostItemRecord(ctx context.Context, tx *gorm.DB, records []*ConversionCostItemRecord) error {
	tb := records[0].TableName()
	return tx.Clauses(clause.Insert{
		Modifier: "IGNORE",
	}).Table(tb).
		Select("order_id", "uid", "user_item_id", "item_type", "source", "source_app_id", "source_type", "total_price", "use_count", "outside_time", "bg_id", "bg_num").
		CreateInBatches(records, len(records)).Error
}

//合成物品消耗
func (s *Store) ConversionItemCost(ctx context.Context, uid uint32, updateItems []*UseItemInfo,
	useItemRecords []*UseItemMonthRecord, costRecords []*ConversionCostItemRecord) error {

	updateItemSQL := s.SQLBatchDecreUseBackpack(ctx, uid, updateItems)

	e := s.Transaction(ctx, func(tx *gorm.DB) error {
		//1.添加合成物品消耗日志
		err := s.TXAddConversionCostItemRecord(ctx, tx, costRecords)
		if err != nil {
			return err
		}
		//2.添加物品消耗流水
		err = s.TXBatchUseRecords(ctx, tx, useItemRecords)
		if err != nil {
			return err
		}
		//3.扣减物品
		result := tx.Exec(updateItemSQL)
		err = result.Error
		if driverErr, ok := err.(*mysql_driver.MySQLError); ok && driverErr.Number == 1690 { //扣减数量不足
			return ErrNoEnougthItem
		}
		if err != nil {
			return err
		}
		if result.RowsAffected != int64(len(updateItems)) {
			return ErrNoEnougthItem
		}
		return nil
	})
	if e != nil {
		log.ErrorWithCtx(ctx, "ConversionItemCost uid:%d error:%v", uid, e)
		return e
	}
	return nil
}

func (s *Store) GetConversionCostItemRecord(ctx context.Context, lastId uint32, t time.Time, limit int) ([]*ConversionCostItemRecord, error) {
	m := &ConversionCostItemRecord{
		OutsideTime: t,
	}
	records := []*ConversionCostItemRecord{}
	err := s.GetOnlyReadDb().Table(m.TableName()).Where("log_id>?", lastId).Limit(limit).Order("log_id").Find(&records).Error
	if err != nil {
		log.ErrorWithCtx(ctx, "GetConversionCostItemRecord lastId:%d, t:%v error:%v", lastId, t, err)
		return nil, err
	}
	return records, nil
}
