package mgr

import (
	"context"
	"crypto/md5"
	"encoding/hex"
	"errors"
	"fmt"
	"golang.52tt.com/services/backpack-base/conf"
	"sort"
	"time"

	"github.com/google/wire"
	"golang.52tt.com/pkg/coroutine"
	"golang.52tt.com/pkg/deal_token"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/protocol/common/status"
	pb "golang.52tt.com/protocol/services/backpack-base"
	"golang.52tt.com/services/backpack-base/cache"
	"golang.52tt.com/services/backpack-base/store"
	"golang.52tt.com/services/backpack-base/utils"

	//"google.golang.org/grpc/codes"
	protogrpc "golang.52tt.com/pkg/protocol/grpc"
	"golang.52tt.com/protocol/services/userpresent"
	"golang.52tt.com/services/backpack-base/rpc"
)

const (
	ErrDBFailMsg = "获取背包数据失败"
)

var ProviderSetForMgr = wire.NewSet(
	NewFuncCardCfgMgr,
	NewPresentCfgMgr,
	NewPackageCfgMgr,
	NewFragmentMgr,
	NewAsyncTaskMgr,
	NewManager,
)

type Manager struct {
	store         store.IStore
	cache         cache.ICache
	packageCfgMgr *PackageCfgMgr
	asyncTaskMgr  *AsyncTaskMgr
	clients       *rpc.RpcClients
}

// GetRebuildingCacheKey 正在重建用户缓存时的locker
func GetRebuildingCacheKey(uid uint32) string {
	return fmt.Sprintf("RebuildUseBpLocker_%d", uid)
}

func NewManager(s store.IStore, c cache.ICache, pkgCfgMgr *PackageCfgMgr, asyncMgr *AsyncTaskMgr, cli *rpc.RpcClients) (IManager, func()) {
	m := &Manager{
		store:         s,
		cache:         c,
		packageCfgMgr: pkgCfgMgr,
		asyncTaskMgr:  asyncMgr,
		clients:       cli,
	}

	//定时建表
	coroutine.FixIntervalExec(m.GenTable, time.Minute*20)

	//合成物品订单检查
	coroutine.FixIntervalExec(m.CheckConversionItemGive, time.Minute)

	//物品过期检查
	coroutine.FixIntervalExec(m.CheckItemTimeout, time.Minute*10)

	//物品快过期提醒
	coroutine.FixIntervalExec(m.OnPushCheckTimeout, time.Minute)
	coroutine.FixIntervalExec(m.OnPushCheckTimeoutThree, time.Minute)

	return m, m.Close
}
func (m *Manager) Close() {
	coroutine.StopAll()
}

func (m *Manager) GenTable() {
	now := time.Now()
	if now.Hour() != 4 {
		return
	}
	ctx, cancel := context.WithTimeout(context.Background(), time.Hour)
	defer cancel()
	if !m.cache.Lock(ctx, "createbgtb", time.Hour, true) {
		return
	}
	defer m.cache.UnLock(ctx, "createbgtb", true)

	m.store.CreateTable()
}

// GetUserBackpack 获取背包数据
func (m *Manager) GetUserBackpack(ctx context.Context, uid uint32) ([]*pb.UserBackpackItem, uint32, error) {
	itemList, gainTs, err := m.cache.GetUserBackpack(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetUserBackpack cache uid:%d, err:%v", uid, err)
		return nil, 0, protocol.NewExactServerError(nil, status.ErrBackpackSysDbFail, ErrDBFailMsg)
	}
	if itemList != nil {
		return itemList, gainTs, nil
	}
	return m.loadUseBackpackStore(ctx, uid)
}

// 重数据库加库并写到cache
func (m *Manager) loadUseBackpackStore(ctx context.Context, uid uint32) ([]*pb.UserBackpackItem, uint32, error) {
	lockKey := GetRebuildingCacheKey(uid)
	_ = m.cache.Lock(ctx, lockKey, time.Second*5, false) //可以同时进入到这里，这里加锁防止过程中CleanUserBackpack的动作
	defer m.cache.UnLock(ctx, lockKey, false)
	itemList, err := m.store.GetUserBackpack(ctx, uid, false)
	if err != nil {
		log.ErrorWithCtx(ctx, "load GetUserBackpack store uid:%d, err:%v", uid, err)
		return nil, 0, protocol.NewExactServerError(nil, status.ErrBackpackSysDbFail, ErrDBFailMsg)
	}

	gainTs, err := m.store.GetUserLastObtainTime(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "load GetUserLastObtainTime store uid:%d, err:%v", uid, err)
		return nil, 0, protocol.NewExactServerError(nil, status.ErrBackpackSysDbFail, ErrDBFailMsg)
	}
	if len(itemList) == 0 { //插入个空item
		itemList = append(itemList, &pb.UserBackpackItem{})
	}
	if gainTs == 0 {
		gainTs = uint32(time.Now().Unix())
	}
	//设置cache
	_ = m.cache.SetUserBackpack(ctx, uid, itemList, gainTs)
	return itemList, gainTs, nil
}

// GetUserBackpackByItemId 获取背包某一种物品
func (m *Manager) GetUserBackpackByItemId(ctx context.Context, uid uint32, itemType, itemId uint32) ([]*pb.UserBackpackItem, error) {
	itemList, err := m.cache.GetUserBackpackByItemId(ctx, uid, itemType, itemId)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetUserBackpack cache uid:%d, err:%v", uid, err)
		return nil, protocol.NewExactServerError(nil, status.ErrBackpackSysDbFail, ErrDBFailMsg)
	}
	if itemList != nil {
		return itemList, nil
	}

	allItemList, _, err := m.loadUseBackpackStore(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "loadUseBackpackStore fail uid:%d, err:%v", uid, err)
		return nil, err
	}

	itemList = []*pb.UserBackpackItem{}
	for _, item := range allItemList {
		if item.ItemType == itemType && item.SourceId == itemId {
			itemList = append(itemList, item)
		}
	}
	return itemList, nil
}

// GetUserBackpackByItemType 获取背包某一大类物品
func (m *Manager) GetUserBackpackByItemType(ctx context.Context, uid uint32, itemType uint32) ([]*pb.UserBackpackItem, error) {
	itemList, err := m.cache.GetUserBackpackByItemType(ctx, uid, itemType)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetUserBackpackByItemType cache uid:%d, type:%d, err:%v", uid, itemType, err)
		return nil, protocol.NewExactServerError(nil, status.ErrBackpackSysDbFail, ErrDBFailMsg)
	}
	if itemList != nil {
		return itemList, nil
	}

	allItemList, _, err := m.loadUseBackpackStore(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "loadUseBackpackStore fail uid:%d, err:%v", uid, err)
		return nil, err
	}

	itemList = []*pb.UserBackpackItem{}
	for _, item := range allItemList {
		if item.ItemType == itemType {
			itemList = append(itemList, item)
		}
	}
	return itemList, nil
}

// GetUserLastObtainTs 获取最后获得物品时间
func (m *Manager) GetUserLastObtainTs(ctx context.Context, uid uint32) (uint32, error) {
	obtainTs, err := m.cache.GetUserLastObtainTs(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetUserLastObtainTs fail uid:%d, err:%v", uid, err)
		return 0, protocol.NewExactServerError(nil, status.ErrBackpackSysDbFail, ErrDBFailMsg)
	}
	if obtainTs == 0 {
		obtainTs = uint32(time.Now().Unix())
	}
	return obtainTs, nil
}

// CheckGiveSign 检查发放包裹签名
func (m *Manager) CheckGiveSign(req *pb.GiveUserPackageReq) bool {
	if !conf.IsCheckGiveSign() {
		return true
	}
	signStr := fmt.Sprintf("uid=%d&bg_id=%d&num=%d&source=%d&order_id=%s&expire_duration=%d&outside_time=d%d&total_price=%d",
		req.GetUid(), req.GetBgId(), req.GetNum(), req.GetSource(), req.GetOrderId(), req.GetExpireDuration(), req.GetOutsideTime(), req.GetTotalPrice())

	signBytes := md5.Sum([]byte(signStr + conf.COMMUNICATION_SIGN_KEY))
	sign := hex.EncodeToString(signBytes[:])
	if sign != req.GetSign() {
		return false
	}
	return true
}

// GiveUserPackage 发放包裹
func (m *Manager) GiveUserPackage(ctx context.Context, req *pb.GiveUserPackageReq) error {
	uid := req.Uid
	orderId := req.OrderId
	outsideTime := req.OutsideTime
	if outsideTime == 0 {
		outsideTime = uint32(time.Now().Unix())
	}
	bgCfg := m.packageCfgMgr.GetPackageConfig(ctx, req.BgId)
	if bgCfg == nil || bgCfg.IsDel {
		return protocol.NewExactServerError(nil, status.ErrBackpackIsDeleted)
	}
	// 检查签名
	if !m.CheckGiveSign(req) {
		log.ErrorWithCtx(ctx, "GiveUserPackage sign error req:%s", req.String())
		return protocol.NewExactServerError(nil, status.ErrBackpackInvalidParams, "sign error")
	}
	//获取包裹物品列表
	itemList := m.packageCfgMgr.GetItemListFromBg(ctx, uid, req.BgId, req.Num, req.ExpireDuration, req.Source)
	if len(itemList) == 0 {
		return protocol.NewExactServerError(nil, status.ErrBackpackPackageItemNotExist)
	}

	if len(orderId) == 0 {
		if req.Source != uint32(pb.PackageSourceType_PACKAGE_SOURCE_OFFICIAL) {
			return protocol.NewExactServerError(nil, status.ErrBackpackInvalidParams)
		}
		//运营后台发送的包裹，生成一个内部订单号
		orderId = utils.GenOfficialOrderID(uid)
	} else {
		//检查订单是否存在
		isExist, err := m.store.CheckGiveOrderIdExist(ctx, orderId, outsideTime)
		if err != nil {
			return protocol.NewExactServerError(nil, status.ErrBackpackSysDbFail)
		}
		if isExist { //已经添加
			log.InfoWithCtx(ctx, "GiveUserPackage orderId exist orderId:%s", orderId)
			return nil
		}
	}

	gainMonthV2Record := &store.UserPackageGainMonthV2{
		OutOrderId:  orderId,
		Uid:         uid,
		BgId:        req.BgId,
		Num:         req.Num,
		Source:      req.Source,
		SourceAppId: req.SourceAppId,
		TotalPrice:  req.TotalPrice,
		OutsideTime: time.Unix(int64(outsideTime), 0),
		CreateTime:  itemList[0].ObtainTime,
	}
	err := m.store.GiveUserPackage(ctx, uid, gainMonthV2Record, itemList)
	if err != nil {
		log.ErrorWithCtx(ctx, "GiveUserPackage store fail uid:%d, orderId:%s, err:%v", uid, orderId, err)
		if errors.Is(err, store.ErrOrderHasDone) { //已经添加
			return nil
		}
		return protocol.NewExactServerError(nil, status.ErrBackpackSysDbFail)
	}

	m.CleanUserBackpack(ctx, uid)

	//异步处理发放包裹后的事情
	m.asyncTaskMgr.AfterGiveUserPackage(ctx, uid, itemList)

	return nil
}

func (m *Manager) CheckCanUseItem(ctx context.Context, uid uint32, itemType, itemId uint32, useCount uint32) ([]*pb.UserBackpackItem, uint32, error) {
	itemList, err := m.GetUserBackpackByItemId(ctx, uid, itemType, itemId)
	if err != nil {
		log.ErrorWithCtx(ctx, "CheckCanUseItem GetUserBackpackByItemId fail uid:%d, item:%d,%d, err:%v", uid, itemType, itemId, err)
		return nil, 0, protocol.NewExactServerError(nil, status.ErrBackpackSysDbFail)
	}
	totalNum := uint32(0)
	now := uint32(time.Now().Unix())
	canUseItems := make([]*pb.UserBackpackItem, 0)
	for _, item := range itemList {
		if item.UserItemId == 0 {
			continue
		}
		if item.FinTime != 0 && item.FinTime <= now {
			continue
		}
		if item.ItemCount == 0 {
			continue
		}
		canUseItems = append(canUseItems, item)
		totalNum += item.ItemCount

	}
	if totalNum < useCount {
		return nil, totalNum, protocol.NewExactServerError(nil, status.ErrBackpackUserNotEnoughItem)
	}
	if len(canUseItems) > 1 {
		sort.Sort(store.FinTimeSortSlice(canUseItems))
	}
	resultItems := make([]*pb.UserBackpackItem, 0)
	num := uint32(0)
	for _, item := range canUseItems {
		if num < useCount {
			resultItems = append(resultItems, item)
		} else {
			break
		}
		num += item.ItemCount
	}

	return resultItems, totalNum, nil
}

// GenUseBackpackItemData 拼装物品扣减数据、订单流水数据
func (m *Manager) GenUseBackpackItemData(leftCount, eachOrderNum uint32, orderIdList []string,
	itemList []*pb.UserBackpackItem, baseRecord *store.UseItemMonthRecord) ([]*store.UseItemInfo, []*store.UseItemMonthRecord) {

	updateUserItems := make([]*store.UseItemInfo, 0)
	useItemRecords := make([]*store.UseItemMonthRecord, 0)
	orderIdx := 0
	orderLeftNum := eachOrderNum
	subOrderIdx := 0
	decCount := uint32(0)

	for _, item := range itemList {
		if item.ItemCount < leftCount {
			decCount = item.ItemCount
		} else {
			decCount = leftCount
		}
		leftCount -= decCount
		//物品项扣减值
		updateUserItems = append(updateUserItems, &store.UseItemInfo{
			UserItemId: item.UserItemId,
			UseCount:   decCount,
		})

		//消耗流水日志
		itemCount := item.ItemCount
		item.ItemCount -= decCount
		for {
			mainOrder := orderIdList[orderIdx]
			subOrder := mainOrder
			if subOrderIdx > 0 {
				subOrder = fmt.Sprintf("%s_%d", mainOrder, subOrderIdx)
			}
			record := *baseRecord
			pRecord := &record
			pRecord.MainOrderId = mainOrder
			//pRecord.FinTime = item.FinTime
			if decCount <= orderLeftNum {
				pRecord.CopyBaseFromPb(item, subOrder, decCount, itemCount-decCount)
				useItemRecords = append(useItemRecords, pRecord)
				orderLeftNum -= decCount
				if orderLeftNum == 0 { //下一个订单
					orderIdx++
					orderLeftNum = eachOrderNum
					subOrderIdx = 0
				} else { //子订单
					subOrderIdx++
				}
				break
			} else { //一条物品项对应多个订单的的情况
				itemCount -= orderLeftNum
				pRecord.CopyBaseFromPb(item, subOrder, orderLeftNum, itemCount)
				useItemRecords = append(useItemRecords, pRecord)
				decCount -= orderLeftNum
				//下一个订单
				orderIdx++
				orderLeftNum = eachOrderNum
				subOrderIdx = 0
			}
		}
		if leftCount == 0 {
			break
		}
	}
	return updateUserItems, useItemRecords
}

// UseBackpackItem 物品使用
func (m *Manager) UseBackpackItem(ctx context.Context, req *pb.UseBackpackItemReq) (uint32, uint32, error) {
	uid := req.Uid
	outsideTime := req.OutsideTime
	if outsideTime == 0 {
		outsideTime = uint32(time.Now().Unix())
	}
	extInfo := req.GetExtraInfo()
	if extInfo == nil {
		extInfo = &pb.UseBackpackExtraInfo{}
		req.ExtraInfo = extInfo
	}
	price := req.ItemPrice * req.UseCount
	if len(req.OrderIdList) > 0 {
		price = price / uint32(len(req.OrderIdList))
	}
	dealToken := deal_token.NewDealTokenData(req.OrderId, req.OrderId, "backpack-base", int64(uid), int64(price))
	dealTokenS, err := deal_token.Encode(dealToken)
	if err != nil {
		log.ErrorWithCtx(ctx, "NewDealTokenData Encode fail :%v, err:%v", uid, dealToken, err)
		return 0, 0, protocol.NewExactServerError(nil, status.ErrBackpackInvalidParams)
	}
	extInfo.DealToken = dealTokenS

	useReason := req.UseReasionType
	if useReason == 0 {
		useReason = uint32(pb.LogType_LOG_TYPE_USE)
	}
	if useReason == uint32(pb.LogType_LOG_TYPE_USE) && req.ItemType == uint32(pb.PackageItemType_BACKPACK_LOTTERY_FRAGMENT) { //不能送碎片
		return 0, 0, protocol.NewExactServerError(nil, status.ErrBackpackUseFragmentSendErr)
	}

	itemList, totalNum, err := m.CheckCanUseItem(ctx, uid, req.ItemType, req.SourceId, req.UseCount)
	if err != nil {
		log.ErrorWithCtx(ctx, "CheckCanUseItem store fail uid:%d, item:%d,%d, total:%d, use:%d, err:%v", uid, req.ItemType, req.SourceId, totalNum, req.UseCount, err)
		_ = m.cache.CleanUserBackpack(ctx, uid)
		return 0, 0, err
	}
	orderList := make([]string, 0)
	if len(req.OrderIdList) > 0 {
		orderList = append(orderList, req.OrderIdList...)
	}
	if len(orderList) == 0 {
		orderList = append(orderList, req.OrderId)
	}
	orderSize := uint32(len(orderList))
	eachOrderNum := req.UseCount / uint32(len(orderList))
	if req.UseCount%orderSize != 0 { //不存在这种情况，多订单每个订单的使用数量是一致的。
		eachOrderNum++
	}

	//是否需要主单到子单的映射记录
	order2UseOrder := false
	if useReason == uint32(pb.LogType_LOG_TYPE_USE) {
		if req.PriceType == uint32(userpresent.PresentPriceType_PRESENT_PRICE_TBEAN) {
			order2UseOrder = true
		}
	} else if req.ItemType == uint32(pb.PackageItemType_BACKPACK_LOTTERY_FRAGMENT) || req.ItemType == uint32(pb.PackageItemType_BACKPACK_PRESENT) {
		order2UseOrder = true
	} else {
		order2UseOrder = false
	}

	//拼装扣减数据，订单流水数据
	baseRecord := &store.UseItemMonthRecord{
		Uid:         uid,
		PriceType:   req.PriceType,
		OperateType: useReason,
		OutsideTime: time.Unix(int64(outsideTime), 0),
	}
	updateUserItems, useItemRecords := m.GenUseBackpackItemData(req.UseCount, eachOrderNum, orderList, itemList, baseRecord)

	//log.DebugWithCtx(ctx, "UseBackpackItem: item: %v, record:%v", updateUserItems, useItemRecords)
	if len(useItemRecords) > len(orderList) && len(orderList) > 2 {
		log.InfoWithCtx(ctx, "UseBackpackItem: order:%v", orderList)
	}
	if req.PriceType == uint32(userpresent.PresentPriceType_PRESENT_PRICE_TBEAN) { //批量送礼时，会用着这个字段
		extInfo.UseCount = eachOrderNum
		m.saveUseExtInfo(ctx, uid, extInfo, orderList, req)
	}

	err = m.store.UseBackpackItem(ctx, uid, updateUserItems, useItemRecords, order2UseOrder, false)
	if err != nil {
		log.ErrorWithCtx(ctx, "UseBackpackItem store fail uid:%d, item:%d,%d, err:%v", uid, req.ItemType, req.SourceId, err)
		if errors.Is(err, store.ErrNoEnougthItem) {
			return 0, 0, protocol.NewExactServerError(nil, status.ErrBackpackUserNotEnoughItem)
		}
		if errors.Is(err, store.ErrOrderHasDone) { //订单号已存在
			finTime := itemList[0].FinTime
			return totalNum, finTime, nil
		}
		return 0, 0, protocol.NewExactServerError(nil, status.ErrBackpackSysDbFail)
	}
	totalNum -= req.UseCount

	m.CleanUserBackpack(ctx, uid)

	//异步处理使用后的逻辑
	useItemList := make([]*store.UserBackpackItem, 0)
	finTime := uint32(0)
	useUserItemIds := map[uint32]uint32{}
	for _, useItem := range updateUserItems {
		useUserItemIds[useItem.UserItemId] = 1
	}
	for _, item := range itemList {
		if _, ok := useUserItemIds[item.UserItemId]; ok {
			it := &store.UserBackpackItem{}
			it.FromPb(uid, item)
			useItemList = append(useItemList, it)
		}
		if item.ItemCount > 0 && finTime == 0 {
			finTime = item.FinTime
		}
	}

	m.asyncTaskMgr.AfterUseBackpackItem(ctx, uid, req.ItemType, req.SourceId, req.UseCount, useItemList)

	return totalNum, finTime, nil
}

func (m *Manager) saveUseExtInfo(rootCtx context.Context, uid uint32, extInfo *pb.UseBackpackExtraInfo, orderList []string, req *pb.UseBackpackItemReq) {
	ctx, cancel := protogrpc.NewContextWithInfoTimeout(rootCtx, time.Second*10)
	defer cancel()
	extOrderIdList := make([]string, 0)
	extInfoList := make([]*pb.UseBackpackExtraInfo, 0)
	if len(orderList) == 1 && len(req.TargetUidList) == 0 { //fix:未传TargetUidList时，但可能传了TargetUid.
		extInfoList = append(extInfoList, extInfo)
		extOrderIdList = append(extOrderIdList, orderList[0])
	} else {
		for i, orderId := range orderList {
			targetUid := uint32(0)
			if i < len(req.TargetUidList) {
				targetUid = req.TargetUidList[i]
			} else {
				log.ErrorWithCtx(ctx, "Failed invalid target_uid uid:%d, %v", uid, req.TargetUidList)
				continue
			}
			extInfo.TargetUid = targetUid
			extInfoNew := &pb.UseBackpackExtraInfo{}
			*extInfoNew = *extInfo
			extInfoList = append(extInfoList, extInfoNew)
			extOrderIdList = append(extOrderIdList, orderId)
		}
	}
	if len(extOrderIdList) > 0 {
		_ = m.cache.AddUseItemExtInfo(ctx, extOrderIdList, extInfoList)
	}
}

// CleanUserBackpack 清用户背包缓存
func (m *Manager) CleanUserBackpack(ctx context.Context, uid uint32) {
	rootCtx := protogrpc.NewContextWithInfo(ctx)
	lockKey := GetRebuildingCacheKey(uid)
	rebuilding := true
	if m.cache.Lock(rootCtx, lockKey, time.Second*5, false) { //这里检查一下是否在重建缓存。如果是，清缓存的动作延后，避免缓存和MYSQL不一致的情况
		m.cache.UnLock(rootCtx, lockKey, false)
		rebuilding = false
		err := m.cache.CleanUserBackpack(rootCtx, uid)
		if err == nil {
			return
		}
	}

	go func() {
		subCtx, cancel := protogrpc.InheritContextWithInfoTimeout(rootCtx, time.Second*10)
		defer cancel()
		if rebuilding {
			time.Sleep(time.Second * 2)
			log.WarnWithCtx(subCtx, "CleanUserBackpack rebuilding cache uid：%d", uid)
		}
		var err error
		for i := 0; i < 3; i++ {
			err = m.cache.CleanUserBackpack(subCtx, uid)
			if err == nil {
				break
			}
			time.Sleep(time.Millisecond * 200)
		}
		if err != nil {
			log.ErrorWithCtx(subCtx, "CleanUserBackpack fail uid:%d, err:%v", uid, err)
			utils.SendWaring("CleanUserBackpack err", fmt.Sprintf("CleanUserBackpack uid:%d, err:%v", uid, err))
		}
	}()
}

// RollBackUserItem 回滚已使用的物品
func (m *Manager) RollBackUserItem(ctx context.Context, uid uint32, createTs uint32, orderId string) error {
	createTime := time.Unix(int64(createTs), 0)
	err := m.store.RollBackUserItem(ctx, uid, createTime, orderId)
	if err != nil {
		if errors.Is(err, store.ErrOrderHasDone) { //订单已处理
			log.InfoWithCtx(ctx, "RollBackUserItem store orderId exist orderId:%s", orderId)
			return nil
		}
		log.ErrorWithCtx(ctx, "RollBackUserItem store fail uid:%d, createTs:%d, orderId:%s  err:%v", uid, createTs, orderId, err)
		return protocol.NewExactServerError(nil, status.ErrBackpackSysDbFail)
	}
	m.CleanUserBackpack(ctx, uid)
	return nil
}

// BatchDeductUserItem 批量扣减(如：运营后台-物品回收)
func (m *Manager) BatchDeductUserItem(ctx context.Context, req *pb.BatchDeductUserItemReq) (*pb.BatchDeductUserItemResp, error) {
	log.InfoWithCtx(ctx, "BatchDeductUserItem req:%s", req.String())
	rsp := &pb.BatchDeductUserItemResp{}
	err := m.store.RecordBatchRecoverOrder(ctx, req.OrderId, req.Oper, req.DeductType)
	if err != nil {
		log.InfoWithCtx(ctx, "BatchDeductUserItem RecordBatchRecoverOrder orderId :%s, err:%v", req.OrderId, err)
		if errors.Is(err, store.ErrOrderHasDone) {
			return rsp, protocol.NewExactServerError(nil, status.ErrBackpackOrderExist)
		}
		return rsp, protocol.NewExactServerError(nil, status.ErrBackpackSysDbFail)
	}
	logType := uint32(0)
	if req.DeductType == uint32(pb.DeductType_OPRATE_DEDUCT) {
		logType = uint32(pb.LogType_LOG_TYPE_OPRATE_DEDUCT)
	} else if req.DeductType == uint32(pb.DeductType_BUSINESS_DEDUCT) {
		logType = uint32(pb.LogType_LOG_TYPE_BUSINESS_DEDUCT)
	} else {
		return rsp, protocol.NewExactServerError(nil, status.ErrBackpackInvalidParams)
	}
	for _, deductDetail := range req.DeductList {
		result := m.DeductUserItem(ctx, req.OrderId, logType, deductDetail)
		rsp.DeductList = append(rsp.DeductList, result)
	}
	return rsp, nil
}

// DeductUserItem 回收物品
func (m *Manager) DeductUserItem(ctx context.Context, orderId string, logType uint32, detail *pb.DeductDetail) *pb.DeductResult {
	uid := detail.Uid
	allUserItems, _, err := m.GetUserBackpack(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "DeductUserItem fail uid:%d order:%s, err:%v", uid, orderId, err)
		return &pb.DeductResult{
			Uid:          uid,
			FailItemList: detail.ItemList,
			FailType:     uint32(pb.DeductFailType_DEDUCTFAILTYPE_DEDUCT_ITEM_FAIL),
		}
	}
	now := time.Now()
	nowTs := uint32(time.Now().Unix())
	allUserItemMap := make(map[uint32]map[uint32]*pb.UserBackpackItem)
	//得到当前所有可用物品
	for _, item := range allUserItems {
		if item.UserItemId == 0 || item.ItemCount == 0 {
			continue
		}
		if item.FinTime != 0 && item.FinTime <= nowTs {
			continue
		}
		key := utils.GenItemUniqueId(item.ItemType, item.SourceId)
		if _, ok := allUserItemMap[key]; !ok {
			allUserItemMap[key] = make(map[uint32]*pb.UserBackpackItem)
		}
		allUserItemMap[key][item.UserItemId] = item
	}

	//组装扣减订单和消耗流水记录
	updateUserItems := []*store.UseItemInfo{}
	useItemRecords := []*store.UseItemMonthRecord{}
	result := &pb.DeductResult{
		Uid: uid,
	}
	for _, dectItem := range detail.ItemList {
		key := utils.GenItemUniqueId(dectItem.ItemType, dectItem.SourceId)
		itemMaps, ok := allUserItemMap[key]
		if !ok {
			result.FailItemList = append(result.FailItemList, dectItem)
			result.FailType = uint32(pb.DeductFailType_DEDUCTFAILTYPE_ITEM_NOT_ENOUGH)
			continue
		}
		decreaseCount := dectItem.Count * detail.Count
		index := 0
		for _, item := range itemMaps {
			if !detail.IsAllSource && detail.SourceType != item.SourceType {
				continue
			}
			decCount := decreaseCount
			if item.ItemCount < decCount {
				decCount = item.ItemCount
			}
			//背包修改结构
			updateUserItems = append(updateUserItems, &store.UseItemInfo{
				UserItemId: item.UserItemId,
				UseCount:   decCount,
			})
			//物品使用记录
			subOrder := fmt.Sprintf("%s_%d_%d_%d_%d_%d", orderId, uid, item.ItemType, item.SourceId, item.SourceType, index)
			index++
			useRecord := &store.UseItemMonthRecord{
				Uid:         uid,
				OperateType: logType,
				OutsideTime: now,
				MainOrderId: fmt.Sprintf("%s_%d_%d_%d_%d", orderId, uid, item.ItemType, item.SourceId, item.SourceType),
			}
			useRecord.CopyBaseFromPb(item, subOrder, decCount, item.ItemCount-decCount)
			useItemRecords = append(useItemRecords, useRecord)

			decreaseCount -= decCount
			if decreaseCount == 0 {
				break
			}
		}
		if decreaseCount > 0 {
			result.FailItemList = append(result.FailItemList, &pb.DeductItem{
				ItemType: dectItem.ItemType,
				SourceId: dectItem.SourceId,
				Count:    decreaseCount,
			})
			result.FailType = uint32(pb.DeductFailType_DEDUCTFAILTYPE_ITEM_NOT_ENOUGH)
		}
	}
	if len(updateUserItems) == 0 {
		return result
	}

	err = m.store.UseBackpackItem(ctx, uid, updateUserItems, useItemRecords, false, true)
	if err != nil {
		//事务全失败
		result.FailItemList = detail.ItemList
		result.FailType = uint32(pb.DeductFailType_DEDUCTFAILTYPE_DEDUCT_ITEM_FAIL)
		log.ErrorWithCtx(ctx, "DeductUserItem fail uid:%d order:%s, err:%v", uid, orderId, err)
		return result
	}

	for _, record := range useItemRecords {
		result.SuccessItemList = append(result.SuccessItemList, &pb.DeductItem{
			ItemType: record.ItemType,
			SourceId: record.SourceId,
			Count:    record.UseCount,
		})
	}
	m.CleanUserBackpack(ctx, uid)
	log.InfoWithCtx(ctx, "DeductUserItem success, uid:%d, orderId:%s", uid, orderId)
	return result
}

// BatchUseBackpackItem 批量背包物品使用协议
func (m *Manager) BatchUseBackpackItem(ctx context.Context, req *pb.BatchUseBackpackItemReq) (string, error) {
	uid := req.Uid
	outsideTime := req.OutsideTime
	if outsideTime == 0 {
		outsideTime = uint32(time.Now().Unix())
	}

	//拼装扣减数据，订单流水数据
	baseRecord := &store.UseItemMonthRecord{
		Uid:         uid,
		OperateType: req.UseReasonType,
		OutsideTime: time.Unix(int64(outsideTime), 0),
	}

	//去重叠加使用数量
	items2Info := map[uint32]*pb.BaseItemInfo{}
	for _, item := range req.UseItemInfos {
		key := utils.GenItemUniqueId(item.ItemType, item.SourceId)
		if info, ok := items2Info[key]; ok {
			info.Num += item.Num
			continue
		}
		items2Info[key] = item
	}

	var updateUserItems []*store.UseItemInfo
	var useItemRecords []*store.UseItemMonthRecord
	for _, item := range items2Info {
		itemList, totalNum, err := m.CheckCanUseItem(ctx, uid, item.ItemType, item.SourceId, item.Num)
		if err != nil {
			log.ErrorWithCtx(ctx, "BatchUseBackpackItem  CheckCanUseItem fail uid:%d, orderId:%s item:%d,%d, total:%d, use:%d, err:%v", uid, req.OrderId, item.SourceId, totalNum, item.Num, err)
			_ = m.cache.CleanUserBackpack(ctx, uid)
			return "", err
		}
		orderId := fmt.Sprintf("%s_%d_%d", req.OrderId, item.ItemType, item.SourceId)
		subUpdateUserItems, subUseItemRecords := m.GenUseBackpackItemData(item.Num, item.Num, []string{orderId}, itemList, baseRecord)
		priceType := m.packageCfgMgr.GetItemPriceType(item.ItemType, item.SourceId)
		for _, record := range subUseItemRecords {
			record.MainOrderId = req.OrderId
			record.PriceType = priceType
		}
		updateUserItems = append(updateUserItems, subUpdateUserItems...)
		useItemRecords = append(useItemRecords, subUseItemRecords...)
	}
	dealToken := deal_token.NewDealTokenData(req.OrderId, req.OrderId, "backpack-base", int64(uid), 0)
	dealTokenS, err := deal_token.Encode(dealToken)
	if err != nil {
		log.ErrorWithCtx(ctx, "NewDealTokenData Encode fail :%v, err:%v", uid, dealToken, err)
		return "", protocol.NewExactServerError(nil, status.ErrBackpackInvalidParams)
	}

	err = m.store.UseBackpackItem(ctx, uid, updateUserItems, useItemRecords, false, false)
	if err != nil {
		log.ErrorWithCtx(ctx, "BatchUseBackpackItem UseBackpackItem fail uid:%d, orderId:%s, req:%s, err:%v", uid, req.OrderId, req.String(), err)
		if errors.Is(err, store.ErrNoEnougthItem) {
			return "", protocol.NewExactServerError(nil, status.ErrBackpackUserNotEnoughItem)
		}
		if errors.Is(err, store.ErrOrderHasDone) { //订单号已存在
			return dealTokenS, nil
		}
		return "", protocol.NewExactServerError(nil, status.ErrBackpackSysDbFail)
	}

	m.CleanUserBackpack(ctx, uid)
	return dealTokenS, nil
}
