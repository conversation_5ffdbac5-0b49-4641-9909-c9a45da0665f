package store

import (
	"context"
	"errors"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/services/pia/mgr"
)

type authorStore struct {
	cli *mongo.Collection
}

func NewAuthorStore(client *mongo.Client) mgr.AuthorBaseInfoStore {
	return &authorStore{
		cli: client.Database(DB).Collection(AuthorInfo),
	}
}

func (a *authorStore) Save(ctx context.Context, author *mgr.AuthorBaseInfo) error {
	_, err := a.cli.UpdateOne(ctx, bson.M{"_id": author.ID}, author, options.Update().SetUpsert(true))
	if err != nil {
		log.ErrorWithCtx(ctx, "Save failed to UpdateOne, err:%v", err)
		return err
	}
	return nil
}

// FetchById 根据作者ID获取作者基本信息，如果不存在则返回 NotFoundError
func (a *authorStore) FetchById(ctx context.Context, id uint32) (*mgr.AuthorBaseInfo, error) {
	log.DebugWithCtx(ctx, "FetchById id:%d", id)
	authInfo := &mgr.AuthorBaseInfo{}
	// 根据id 查询作者信息
	err := a.cli.FindOne(ctx, bson.M{"_id": id}, options.FindOne().SetProjection(
		bson.M{"_id": 1, "uid": 1, "nickname": 1})).Decode(authInfo)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil, mgr.NotFoundError
		}
		return nil, err
	}
	return &mgr.AuthorBaseInfo{
		ID:       id,
		Nickname: authInfo.Nickname,
		Uid:      authInfo.Uid,
	}, nil
}

// 批量获取作者信息
func (a *authorStore) BatchFetchByIdList(ctx context.Context, idList []uint32) ([]*mgr.AuthorBaseInfo, error) {
	if len(idList) == 0 {
		log.DebugWithCtx(ctx, "BatchFetchByIdList get empty list")
		return []*mgr.AuthorBaseInfo{}, nil
	}
	log.DebugWithCtx(ctx, "BatchFetchByIdList idList:%v", idList)
	retList := make([]*mgr.AuthorBaseInfo, 0, len(idList))
	cursor, err := a.cli.Find(ctx, bson.M{"_id": bson.M{"$in": idList}})
	if err != nil {
		log.ErrorWithCtx(ctx, "BatchFetchByIdList failed to find, idList:%v, err:%v", idList, err)
		return retList, err
	}
	err = cursor.All(ctx, &retList)
	if err != nil {
		log.ErrorWithCtx(ctx, "BatchFetchByIdList failed to cursor.All, idList:%v, err:%v", idList, err)
		return retList, err
	}
	return retList, nil
}

func (a *authorStore) UpdateMany(ctx context.Context, authorList ...*mgr.AuthorBaseInfo) error {
	log.DebugWithCtx(ctx, "UpdateMany, author count:%d", len(authorList))
	if len(authorList) == 0 {
		log.WarnWithCtx(ctx, "UpdateMany is empty")
		return nil
	}
	models := make([]mongo.WriteModel, len(authorList))
	for i, item := range authorList {
		models[i] = mongo.NewUpdateOneModel().
			SetFilter(bson.M{"_id": item.ID}).
			SetUpdate(bson.M{
				"$set": item,
			}).
			SetUpsert(true)
	}
	_, err := a.cli.BulkWrite(ctx, models, options.BulkWrite().SetOrdered(false))
	if err != nil {
		log.ErrorWithCtx(ctx, "UpdateMany failed,err:%+v", err)
		return err
	}
	return nil
}

func (a *authorStore) GetLocalLatestUpdateTime(ctx context.Context) (int64, error) {
	authorInfo := &mgr.AuthorBaseInfo{}
	err := a.cli.FindOne(ctx, bson.M{}, options.FindOne().SetSort(bson.M{"update_time": -1})).Decode(authorInfo)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return 0, nil
		}
		log.ErrorWithCtx(ctx, "GetLocalLatestUpdateTime failed to FindOne, err:%v", err)
		return 0, err
	}
	return authorInfo.UpdateTime, nil
}
