package mgr

import (
	"github.com/stretchr/testify/assert"
	"testing"
)

func TestChannelPlayingMicInfoSnapshotList_GetAllValidUserId(t *testing.T) {
	tests := []struct {
		name     string
		receiver ChannelPlayingMicInfoSnapshotList
		want     []uint32
	}{
		{
			name:     "空列表",
			receiver: ChannelPlayingMicInfoSnapshotList{},
			want:     []uint32{},
		},
		{
			name: "开始后没有上下麦",
			receiver: ChannelPlayingMicInfoSnapshotList{
				{
					ChannelID:  123,
					RoundID:    123,
					DramaID:    123,
					CreateTime: 1670654411,
					MicUserMap: map[uint32]uint32{1: 1},
				},
				{
					ChannelID:  123,
					RoundID:    123,
					DramaID:    123,
					CreateTime: 1670654411 + 60,
					MicUserMap: map[uint32]uint32{1: 0},
				},
			},
			want: []uint32{1},
		},
		{
			name: "开始后下麦换路人甲人上麦，但是路人甲没有满足条件",
			receiver: ChannelPlayingMicInfoSnapshotList{
				{
					ChannelID:  123,
					RoundID:    123,
					DramaID:    123,
					CreateTime: 1670654411,
					MicUserMap: map[uint32]uint32{1: 1},
				},
				{
					ChannelID:  123,
					RoundID:    123,
					DramaID:    123,
					CreateTime: 1670654411 + 60,
					MicUserMap: map[uint32]uint32{1: 2},
				},
				{
					ChannelID:  123,
					RoundID:    123,
					DramaID:    123,
					CreateTime: 1670654411 + 60 + 20,
					MicUserMap: map[uint32]uint32{1: 0},
				},
			},
			want: []uint32{1},
		},
		{
			name: "开始后下麦换路人甲人上麦，并且路人甲满足条件",
			receiver: ChannelPlayingMicInfoSnapshotList{
				{
					ChannelID:  123,
					RoundID:    123,
					DramaID:    123,
					CreateTime: 1670654411,
					MicUserMap: map[uint32]uint32{1: 1},
				},
				{
					ChannelID:  123,
					RoundID:    123,
					DramaID:    123,
					CreateTime: 1670654411 + 60,
					MicUserMap: map[uint32]uint32{1: 2},
				},
				{
					ChannelID:  123,
					RoundID:    123,
					DramaID:    123,
					CreateTime: 1670654411 + 60 + 60,
					MicUserMap: map[uint32]uint32{1: 0},
				},
			},
			want: []uint32{1, 2},
		},
		{
			name: "开始10秒后下麦，上其他的麦50秒",
			receiver: ChannelPlayingMicInfoSnapshotList{
				{
					ChannelID:  123,
					RoundID:    123,
					DramaID:    123,
					CreateTime: 1670654411,
					MicUserMap: map[uint32]uint32{1: 1, 2: 2},
				},
				{
					ChannelID:  123,
					RoundID:    123,
					DramaID:    123,
					CreateTime: 1670654411 + 10,
					MicUserMap: map[uint32]uint32{2: 1},
				},
				{
					ChannelID:  123,
					RoundID:    123,
					DramaID:    123,
					CreateTime: 1670654411 + 60,
					MicUserMap: map[uint32]uint32{},
				},
			},
			want: []uint32{1},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			assert.Equalf(t, tt.want, tt.receiver.GetAllValidUserId(), "GetAllValidUserId()")
		})
	}
}
