package mgr

import (
	"time"

	app_pb "golang.52tt.com/protocol/app/pia"
	pb "golang.52tt.com/protocol/services/pia"
)

type Convertor interface {
	ChannelRunningDramaInfoToPB(info *ChannelPlayingDramaInfo) *pb.ChannelDramaStatus
	ChannelMicRoleBindingInfoToPB(info *ChannelMicRoleBindingInfo) *pb.MicRoleMap
	ChannelMicRoleBindingInfoToLogic(info *ChannelMicRoleBindingInfo) *app_pb.MicRoleMap
	DramaInfoToLogicSubInfo(info *DramaEntityV2) *app_pb.DramaSubInfo
	DramaInfoToLogicRoleList(info *DramaEntityV2) []*app_pb.PiaRole
	DramaInfoToLogicContentList(info *DramaEntityV2) []*app_pb.PiaContent
	DramaInfoToLogicBgmList(info *DramaEntityV2) []*app_pb.PiaBGM
	DramaInfoToLogicPictureList(info *DramaEntityV2) []*app_pb.PiaPicture
	DramaInfoToLogicDrama(info *DramaEntityV2) *app_pb.DramaV2
	ChannelDramaStatusToLogic(info *ChannelRunningDramaInfo) *app_pb.ChannelDramaStatus
	DramaInfoToPBDrama(info *DramaEntityV2) *pb.DramaV2
	DramaInfoToPBSubInfo(info *DramaEntityV2) *pb.DramaSubInfo
	DramaInfoToPBRoleList(info *DramaEntityV2) []*pb.PiaRole
	DramaInfoToPBContentList(info *DramaEntityV2) []*pb.PiaContent
	DramaInfoToPBBgmList(info *DramaEntityV2) []*pb.PiaBGM
	DramaInfoToPBPictureList(info *DramaEntityV2) []*pb.PiaPicture
	BgmStatusToPB(info *BgmProgress) *pb.DramaBgmStatus
	BgmStatusToLogic(info *BgmProgress) *app_pb.DramaBgmStatus
	BgmVolStatusToPB(info *BgmVolSetting) *pb.DramaBgmVolStatus
	BgmVolStatusToLogic(info *BgmVolSetting) *app_pb.DramaBgmVolStatus
	ChannelPlayingStatusInfoToPB(info *ChannelPlayingStatusInfo) *pb.ChannelDramaStatus
	ChannelPlayingStatusInfoToLogic(info *ChannelPlayingStatusInfo) *app_pb.ChannelDramaStatus
	ChannelPlayingTypeInfoToPB(info *ChannelPlayingTypeInfo) *pb.PiaChannelDramaPlayingType
	ChannelPlayingTypeTimeInfoToPB(info DramaPlayingType) pb.PiaChannelDramaPlayingType_PlayingTypeTime
	ChannelPlayingTypeRoleInfoToPB(info DramaPlayingType) pb.PiaChannelDramaPlayingType_PlayingTypeRole
	ChannelPlayingTypeInfoFromPB(info *pb.PiaChannelDramaPlayingType) *ChannelPlayingTypeInfo
	ChannelPlayingTypeTimeInfoFromPB(info pb.PiaChannelDramaPlayingType_PlayingTypeTime) DramaPlayingType
	ChannelPlayingTypeRoleInfoFromPB(info pb.PiaChannelDramaPlayingType_PlayingTypeRole) DramaPlayingType
	ChannelPlayingTypeInfoToLogic(info *ChannelPlayingTypeInfo) *app_pb.PiaChannelDramaPlayingType
	ChannelPlayingTypeTimeInfoToLogic(info DramaPlayingType) app_pb.PiaChannelDramaPlayingType_PlayingTypeTime
	ChannelPlayingTypeRoleInfoToLogic(info DramaPlayingType) app_pb.PiaChannelDramaPlayingType_PlayingTypeRole
	ChannelPlayingDramaInfoToChangePlayTypeLogic(info *ChannelPlayingDramaInfo) *app_pb.PiaChangePlayType
	DramaInfoToPBDramaWithMaskField(info *DramaEntityV2, status *ChannelPlayingStatusInfo) *pb.DramaV2
	DramaInfoToLogicDramaWithMaskField(info *DramaEntityV2, status *ChannelPlayingStatusInfo) *app_pb.DramaV2
	StickDramaInfoToPB(info *DramaStickEntity) *pb.SearchStickDramaResp_StickDramaInfo
	MyPlayingRecordToPB(info *MyPlayingRecord, dramaEntity *DramaEntityV2) *pb.PiaDramaPlayingRecord
	CopyDramaInfoWithStatusListToPB(info []*DramaEntityV2) []*pb.PiaCopyDramaInfoWithStatus
	CopyDramaInfoWithStatusToPB(info *DramaEntityV2) *pb.PiaCopyDramaInfoWithStatus
	OrderDramaListBizToPB(info *OrderDramaList) *pb.DramaOrderList
	DramaV2PBToLogic(info *pb.DramaV2) *app_pb.DramaV2
	DramaSubInfoPBToLogic(info *pb.DramaSubInfo) *app_pb.DramaSubInfo
	DramaRoleListPBToLogic(info []*pb.PiaRole) []*app_pb.PiaRole
	DramaContentListPBToLogic(info []*pb.PiaContent) []*app_pb.PiaContent
	DramaBgmListPBToLogic(info []*pb.PiaBGM) []*app_pb.PiaBGM
	DramaPictureListPBToLogic(info []*pb.PiaPicture) []*app_pb.PiaPicture
}

type convertor struct {
}

func NewConvertor() Convertor {
	return &convertor{}
}

func (c *convertor) ChannelRunningDramaInfoToPB(info *ChannelPlayingDramaInfo) *pb.ChannelDramaStatus {
	if info == nil {
		return &pb.ChannelDramaStatus{}
	}
	result := &pb.ChannelDramaStatus{
		RoundId:    info.Status.RoundId,
		DramaPhase: 0,
		ChannelId:  info.Status.ChannelId,
		DramaInfo:  c.DramaInfoToPBDrama(info.GetRunningInfo().DramaRawInfo.Info),
		Version:    info.Status.Version,
		Progress: &pb.ChannelDramaProgress{
			CurIndex:   info.Status.CurContentIndex,
			TimeOffset: int64(info.Status.CurContentOffset.Seconds()),
		},
		StartTime:   info.Status.StartTime.Unix(),
		PlayingType: c.ChannelPlayingTypeInfoToPB(info.Status.PlayingType),
		TempDramaId: info.Status.TempDramaID,
	}
	switch info.Status.RunningStatus {
	case DramaRunningStatusSelectingRole:
		result.DramaPhase = pb.DramaPhase_DRAMA_PHASE_SELECT_ROLE
	case DramaRunningStatusPlaying:
		result.DramaPhase = pb.DramaPhase_DRAMA_PHASE_PLAY
	case DramaRunningStatusEnd:
		result.DramaPhase = pb.DramaPhase_DRAMA_PHASE_END
	case DramaRunningStatusPause:
		result.DramaPhase = pb.DramaPhase_DRAMA_PHASE_PAUSE
	default:
		// do nothing
	}
	// 如果剧本信息不为空，则需要根据走本方式动态刷新剧本内容
	if result.DramaInfo != nil {
		// 根据走本方式，动态修改剧本的走本类型和角色列表
		switch info.Status.PlayingType.CurTimeType {
		case DramaPlayingTypeTiming:
			result.DramaInfo.PlayType = pb.PiaPlayType_PLAY_TYPE_AUTO
		case DramaPlayingTypeManual:
			result.DramaInfo.PlayType = pb.PiaPlayType_PLAY_TYPE_MANUAL
		default:
			result.DramaInfo.PlayType = pb.PiaPlayType_PLAY_TYPE_MANUAL
		}
		switch info.Status.PlayingType.CurRoleType {
		case DramaPlayingTypeHasRole:
		// do nothing
		case DramaPlayingTypeNoRole:
			result.DramaInfo.RoleList = nil
		default:
			// 默认无角色
			result.DramaInfo.RoleList = nil
		}
	}
	return result
}

func (c *convertor) ChannelMicRoleBindingInfoToPB(info *ChannelMicRoleBindingInfo) *pb.MicRoleMap {
	if info == nil {
		return &pb.MicRoleMap{}
	}
	result := &pb.MicRoleMap{
		Map:     make(map[uint32]*pb.MicRoleMap_RoleInfoList, len(info.BindingList)),
		Version: info.Version,
	}
	for k, v := range info.BindingList {
		result.Map[k] = &pb.MicRoleMap_RoleInfoList{
			Id:       v.RoleIDs,
			JoinTime: v.JoinTime,
		}
	}
	return result
}

func (c *convertor) DramaInfoToLogicSubInfo(info *DramaEntityV2) *app_pb.DramaSubInfo {
	if info == nil {
		return &app_pb.DramaSubInfo{}
	}
	return &app_pb.DramaSubInfo{
		Id:             info.ID,
		Title:          info.Title,
		CoverUrl:       info.CoverUrl,
		Author:         info.Author,
		Desc:           info.Desc,
		MaleCnt:        info.MaleCnt,
		FemaleCnt:      info.FemaleCnt,
		WordCnt:        info.WordCnt,
		TagList:        info.Tags,
		Type:           info.Type,
		DisplayId:      uint64(info.OriginID),
		AuthorId:       info.CreateUserId,
		Duration:       info.Duration,
		CreateTime:     info.CreateTime,
		RelatedDramaId: info.RelatedID,
	}
}

func (c *convertor) DramaInfoToLogicRoleList(info *DramaEntityV2) []*app_pb.PiaRole {
	if info == nil || len(info.RoleList) == 0 {
		return []*app_pb.PiaRole{}
	}
	list := make([]*app_pb.PiaRole, 0, len(info.RoleList))
	for _, item := range info.RoleList {
		list = append(list, &app_pb.PiaRole{
			Id:            item.RoleID,
			Name:          item.RoleName,
			Sex:           item.Sex,
			Avatar:        item.Avatar,
			Introduction:  item.Introduction,
			Color:         item.Color,
			DialogueRatio: item.DialogueRatio,
		})
	}
	return list
}

func (c *convertor) DramaInfoToLogicContentList(info *DramaEntityV2) []*app_pb.PiaContent {
	if info == nil || len(info.ContentList) == 0 {
		return []*app_pb.PiaContent{}
	}
	list := make([]*app_pb.PiaContent, 0, len(info.ContentList))
	for _, item := range info.ContentList {
		list = append(list, &app_pb.PiaContent{
			Id:       item.ID,
			RoleId:   item.RoleID,
			RoleName: item.RoleName,
			Dialogue: item.Dialogue,
			Duration: &app_pb.PiaDuration{
				BeginTime: item.BeginTime,
				EndTime:   item.EndTime,
			},
			Color: item.Color,
		})
	}
	return list
}

func (c *convertor) DramaInfoToLogicBgmList(info *DramaEntityV2) []*app_pb.PiaBGM {
	if info == nil || len(info.BGMList) == 0 {
		return []*app_pb.PiaBGM{}
	}
	list := make([]*app_pb.PiaBGM, 0, len(info.BGMList))
	for _, item := range info.BGMList {
		list = append(list, &app_pb.PiaBGM{
			Id:  item.ID,
			Url: item.URL,
			Duration: &app_pb.PiaDuration{
				BeginTime: item.BeginTime,
				EndTime:   item.EndTime,
			},
			Name:   item.Name,
			Length: item.Length,
		})
	}
	return list
}

func (c *convertor) DramaInfoToLogicPictureList(info *DramaEntityV2) []*app_pb.PiaPicture {
	if info == nil || len(info.PictureList) == 0 {
		return []*app_pb.PiaPicture{}
	}
	list := make([]*app_pb.PiaPicture, 0, len(info.PictureList))
	for _, item := range info.PictureList {
		list = append(list, &app_pb.PiaPicture{
			Id:  item.ID,
			Url: item.URL,
			Duration: &app_pb.PiaDuration{
				BeginTime: item.BeginTime,
				EndTime:   item.EndTime,
			},
		})
	}
	return list
}

func (c *convertor) DramaInfoToLogicDrama(info *DramaEntityV2) *app_pb.DramaV2 {
	if info == nil {
		return &app_pb.DramaV2{}
	}
	a := &app_pb.DramaV2{
		DramaSubInfo: c.DramaInfoToLogicSubInfo(info),
		RoleList:     c.DramaInfoToLogicRoleList(info),
		ContentList:  c.DramaInfoToLogicContentList(info),
		BgmUrl:       c.DramaInfoToLogicBgmList(info),
		PictureList:  c.DramaInfoToLogicPictureList(info),
		IsCopyDrama:  info.Uid != 0,
	}
	return a
}

func (c *convertor) ChannelDramaStatusToLogic(info *ChannelRunningDramaInfo) *app_pb.ChannelDramaStatus {
	if info == nil {
		return &app_pb.ChannelDramaStatus{}
	}
	a := &app_pb.ChannelDramaStatus{
		RoundId:    info.RoundId,
		DramaPhase: app_pb.DramaPhase_DRAMA_PHASE_UNSPECIFIED,
		ChannelId:  info.ChannelId,
		DramaInfo:  c.DramaInfoToLogicDrama(info.DramaRawInfo.Info),
		Version:    info.Version,
		Progress: &app_pb.ChannelDramaProgress{
			CurIndex:   info.CurContentIndex,
			TimeOffset: int64(info.CurContentOffset.Seconds()),
		},
		StartTime: info.StartTime.Unix(),
	}
	switch info.RunningStatus {
	case DramaRunningStatusSelectingRole:
		a.DramaPhase = app_pb.DramaPhase_DRAMA_PHASE_SELECT_ROLE
	case DramaRunningStatusPlaying:
		a.DramaPhase = app_pb.DramaPhase_DRAMA_PHASE_PLAY
	case DramaRunningStatusEnd:
		a.DramaPhase = app_pb.DramaPhase_DRAMA_PHASE_END
	case DramaRunningStatusPause:
		a.DramaPhase = app_pb.DramaPhase_DRAMA_PHASE_PAUSE
	default:
		// do nothing
	}
	switch info.DramaType {
	case DramaTypeTiming:
		a.DramaInfo.PlayType = app_pb.PiaPlayType_PLAY_TYPE_AUTO
	case DramaTypeManual:
		a.DramaInfo.PlayType = app_pb.PiaPlayType_PLAY_TYPE_MANUAL
	default:
		// do nothing
	}
	return a
}

func (c *convertor) DramaInfoToPBDrama(info *DramaEntityV2) *pb.DramaV2 {
	if info == nil {
		return &pb.DramaV2{}
	}
	result := &pb.DramaV2{
		DramaSubInfo: c.DramaInfoToPBSubInfo(info),
		RoleList:     c.DramaInfoToPBRoleList(info),
		ContentList:  c.DramaInfoToPBContentList(info),
		BgmUrl:       c.DramaInfoToPBBgmList(info),
		PictureList:  c.DramaInfoToPBPictureList(info),
		PlayType:     info.PlayType,
	}
	return result
}

func (c *convertor) DramaInfoToPBSubInfo(info *DramaEntityV2) *pb.DramaSubInfo {
	if info == nil {
		return &pb.DramaSubInfo{}
	}
	result := &pb.DramaSubInfo{
		Id:             info.ID,
		Title:          info.Title,
		CoverUrl:       info.CoverUrl,
		Author:         info.Author,
		Desc:           info.Desc,
		MaleCnt:        info.MaleCnt,
		FemaleCnt:      info.FemaleCnt,
		WordCnt:        info.WordCnt,
		TagList:        info.Tags,
		Type:           info.Type,
		DisplayId:      uint64(info.OriginID),
		AuthorUid:      info.AuthorUid,
		AuthorId:       info.CreateUserId,
		Duration:       info.Duration,
		CreateTime:     info.CreateTime,
		RelatedDramaId: info.RelatedID,
		CreatorUid:     info.Uid,
		IsPrivate:      info.IsPrivate,
		IsFreeze:       info.IsFreeze,
		IsDeleted:      info.Deleted,
	}
	return result
}

func (c *convertor) DramaInfoToPBRoleList(info *DramaEntityV2) []*pb.PiaRole {
	if info == nil || len(info.RoleList) == 0 {
		return []*pb.PiaRole{}
	}
	list := make([]*pb.PiaRole, 0, len(info.RoleList))
	for _, item := range info.RoleList {
		list = append(list, &pb.PiaRole{
			Id:            item.RoleID,
			Name:          item.RoleName,
			Sex:           item.Sex,
			Avatar:        item.Avatar,
			Introduction:  item.Introduction,
			Color:         item.Color,
			DialogueRatio: item.DialogueRatio,
		})
	}
	return list
}

func (c *convertor) DramaInfoToPBContentList(info *DramaEntityV2) []*pb.PiaContent {
	if info == nil || len(info.ContentList) == 0 {
		return []*pb.PiaContent{}
	}
	list := make([]*pb.PiaContent, 0, len(info.ContentList))
	for _, item := range info.ContentList {
		list = append(list, &pb.PiaContent{
			Id:       item.ID,
			RoleId:   item.RoleID,
			Dialogue: item.Dialogue,
			Duration: &pb.PiaDuration{
				BeginTime: item.BeginTime,
				EndTime:   item.EndTime,
			},
			RoleName: item.RoleName,
			Color:    item.Color,
		})
	}
	return list
}

func (c *convertor) DramaInfoToPBBgmList(info *DramaEntityV2) []*pb.PiaBGM {
	if info == nil || len(info.BGMList) == 0 {
		return []*pb.PiaBGM{}
	}
	list := make([]*pb.PiaBGM, 0, len(info.BGMList))
	for _, item := range info.BGMList {
		list = append(list, &pb.PiaBGM{
			Id:  item.ID,
			Url: item.URL,
			Duration: &pb.PiaDuration{
				BeginTime: item.BeginTime,
				EndTime:   item.EndTime,
			},
			Name:   item.Name,
			Length: item.Length,
		})
	}
	return list
}

func (c *convertor) DramaInfoToPBPictureList(info *DramaEntityV2) []*pb.PiaPicture {
	if info == nil || len(info.PictureList) == 0 {
		return []*pb.PiaPicture{}
	}
	list := make([]*pb.PiaPicture, 0, len(info.PictureList))
	for _, item := range info.PictureList {
		list = append(list, &pb.PiaPicture{
			Id:  item.ID,
			Url: item.URL,
			Duration: &pb.PiaDuration{
				BeginTime: item.BeginTime,
				EndTime:   item.EndTime,
			},
		})
	}
	return list
}

func (c *convertor) BgmStatusToPB(info *BgmProgress) *pb.DramaBgmStatus {
	if info == nil {
		return &pb.DramaBgmStatus{}
	}
	p := &pb.DramaBgmStatus{
		BgmId:       info.ID,
		BgmProgress: info.Progress,
		Version:     info.Version,
	}
	switch info.Status {
	default:
		// do nothing
	case BgmStatusEnd:
		p.BgmPhase = pb.DramaBGMPhase_DRAMA_BGM_PHASE_PAUSE
	case BgmStatusPlaying:
		p.BgmPhase = pb.DramaBGMPhase_DRAMA_BGM_PHASE_PLAY
	}
	// 填充操作时间
	if len(info.OperationSnapshot) > 0 {
		p.OperationTime = info.OperationSnapshot[len(info.OperationSnapshot)-1].BeginTime
	}
	return p
}

func (c *convertor) BgmStatusToLogic(info *BgmProgress) *app_pb.DramaBgmStatus {
	if info == nil {
		return &app_pb.DramaBgmStatus{}
	}
	p := &app_pb.DramaBgmStatus{
		BgmId:       info.ID,
		BgmProgress: info.Progress,
		Version:     info.Version,
	}
	switch info.Status {
	default:
		// do nothing
	case BgmStatusEnd:
		p.BgmPhase = app_pb.DramaBGMPhase_DRAMA_BGM_PHASE_PAUSE
	case BgmStatusPlaying:
		p.BgmPhase = app_pb.DramaBGMPhase_DRAMA_BGM_PHASE_PLAY
	}
	// 填充操作时间
	if len(info.OperationSnapshot) > 0 {
		p.OperationTime = info.OperationSnapshot[len(info.OperationSnapshot)-1].BeginTime
	}
	return p
}

func (c *convertor) ChannelMicRoleBindingInfoToLogic(info *ChannelMicRoleBindingInfo) *app_pb.MicRoleMap {
	if info == nil {
		return &app_pb.MicRoleMap{}
	}
	result := &app_pb.MicRoleMap{
		Map:     make(map[uint32]*app_pb.MicRoleMap_RoleInfoList, len(info.BindingList)),
		Version: info.Version,
	}
	for k, v := range info.BindingList {
		result.Map[k] = &app_pb.MicRoleMap_RoleInfoList{
			Id:       v.RoleIDs,
			JoinTime: v.JoinTime,
		}
	}
	return result
}
func (c *convertor) BgmVolStatusToPB(info *BgmVolSetting) *pb.DramaBgmVolStatus {
	if info == nil {
		return &pb.DramaBgmVolStatus{
			Vol: 100,
		}
	}
	return &pb.DramaBgmVolStatus{
		Vol: info.Vol,
	}
}

func (c *convertor) BgmVolStatusToLogic(info *BgmVolSetting) *app_pb.DramaBgmVolStatus {
	if info == nil {
		return &app_pb.DramaBgmVolStatus{
			Vol: 100,
		}
	}
	return &app_pb.DramaBgmVolStatus{
		Vol: info.Vol,
	}
}

func (c *convertor) ChannelPlayingStatusInfoToPB(info *ChannelPlayingStatusInfo) *pb.ChannelDramaStatus {
	if info == nil {
		return &pb.ChannelDramaStatus{}
	}
	p := &pb.ChannelDramaStatus{
		ChannelId: info.ChannelId,
		DramaInfo: nil,
		Version:   info.Version,
		Progress: &pb.ChannelDramaProgress{
			CurIndex:   info.CurContentIndex,
			TimeOffset: int64(info.CurContentOffset.Seconds()),
		},
		StartTime:   info.StartTime.Unix(),
		RoundId:     info.RoundId,
		PlayingType: c.ChannelPlayingTypeInfoToPB(info.PlayingType),
		TempDramaId: info.TempDramaID,
	}
	switch info.RunningStatus {
	case DramaRunningStatusSelectingRole:
		p.DramaPhase = pb.DramaPhase_DRAMA_PHASE_SELECT_ROLE
	case DramaRunningStatusPlaying:
		p.DramaPhase = pb.DramaPhase_DRAMA_PHASE_PLAY
	case DramaRunningStatusEnd:
		p.DramaPhase = pb.DramaPhase_DRAMA_PHASE_END
	case DramaRunningStatusPause:
		p.DramaPhase = pb.DramaPhase_DRAMA_PHASE_PAUSE
	default:
		// do nothing
	}
	return p
}

func (c *convertor) ChannelPlayingStatusInfoToLogic(info *ChannelPlayingStatusInfo) *app_pb.ChannelDramaStatus {
	if info == nil {
		return &app_pb.ChannelDramaStatus{}
	}
	a := &app_pb.ChannelDramaStatus{
		RoundId:    info.RoundId,
		DramaPhase: app_pb.DramaPhase_DRAMA_PHASE_UNSPECIFIED,
		ChannelId:  info.ChannelId,
		Version:    info.Version,
		Progress: &app_pb.ChannelDramaProgress{
			CurIndex:   info.CurContentIndex,
			TimeOffset: int64(info.CurContentOffset.Seconds()),
		},
		StartTime:   info.StartTime.Unix(),
		PlayingType: c.ChannelPlayingTypeInfoToLogic(info.PlayingType),
	}
	switch info.RunningStatus {
	case DramaRunningStatusSelectingRole:
		a.DramaPhase = app_pb.DramaPhase_DRAMA_PHASE_SELECT_ROLE
	case DramaRunningStatusPlaying:
		a.DramaPhase = app_pb.DramaPhase_DRAMA_PHASE_PLAY
	case DramaRunningStatusEnd:
		a.DramaPhase = app_pb.DramaPhase_DRAMA_PHASE_END
	case DramaRunningStatusPause:
		a.DramaPhase = app_pb.DramaPhase_DRAMA_PHASE_PAUSE
	default:
		// do nothing
	}
	return a
}

func (c *convertor) ChannelPlayingTypeInfoToPB(info *ChannelPlayingTypeInfo) *pb.PiaChannelDramaPlayingType {
	p := &pb.PiaChannelDramaPlayingType{}
	if info == nil {
		return p
	}
	p.CurrentPlayingTypeTime = c.ChannelPlayingTypeTimeInfoToPB(info.CurTimeType)
	p.CurrentPlayingTypeRole = c.ChannelPlayingTypeRoleInfoToPB(info.CurRoleType)
	p.SupportPlayingTypeTimes = make([]pb.PiaChannelDramaPlayingType_PlayingTypeTime, 0, len(info.SupportForTime))
	p.SupportPlayingTypeRoles = make([]pb.PiaChannelDramaPlayingType_PlayingTypeRole, 0, len(info.SupportForRole))
	for _, item := range info.SupportForTime {
		p.SupportPlayingTypeTimes = append(p.SupportPlayingTypeTimes, c.ChannelPlayingTypeTimeInfoToPB(item))
	}
	for _, item := range info.SupportForRole {
		p.SupportPlayingTypeRoles = append(p.SupportPlayingTypeRoles, c.ChannelPlayingTypeRoleInfoToPB(item))
	}
	return p
}

func (c *convertor) ChannelPlayingTypeTimeInfoToPB(info DramaPlayingType) pb.PiaChannelDramaPlayingType_PlayingTypeTime {
	switch info {
	default:
		// 无时间兜底
		return pb.PiaChannelDramaPlayingType_PIA_CHANNEL_DRAMA_PLAYING_TYPE_TIME_NONE
	case DramaPlayingTypeUnknown:
		// 无时间兜底
		return pb.PiaChannelDramaPlayingType_PIA_CHANNEL_DRAMA_PLAYING_TYPE_TIME_NONE
	case DramaPlayingTypeTiming:
		return pb.PiaChannelDramaPlayingType_PIA_CHANNEL_DRAMA_PLAYING_TYPE_TIME_HAS
	case DramaPlayingTypeManual:
		return pb.PiaChannelDramaPlayingType_PIA_CHANNEL_DRAMA_PLAYING_TYPE_TIME_NONE
	}
}

func (c *convertor) ChannelPlayingTypeRoleInfoToPB(info DramaPlayingType) pb.PiaChannelDramaPlayingType_PlayingTypeRole {
	switch info {
	default:
		// 无角色兜底
		return pb.PiaChannelDramaPlayingType_PIA_CHANNEL_DRAMA_PLAYING_TYPE_ROLE_NONE
	case DramaPlayingTypeUnknown:
		// 无角色兜底
		return pb.PiaChannelDramaPlayingType_PIA_CHANNEL_DRAMA_PLAYING_TYPE_ROLE_NONE
	case DramaPlayingTypeNoRole:
		// 无角色兜底
		return pb.PiaChannelDramaPlayingType_PIA_CHANNEL_DRAMA_PLAYING_TYPE_ROLE_NONE
	case DramaPlayingTypeHasRole:
		return pb.PiaChannelDramaPlayingType_PIA_CHANNEL_DRAMA_PLAYING_TYPE_ROLE_HAS
	}
}

func (c *convertor) ChannelPlayingTypeInfoToLogic(info *ChannelPlayingTypeInfo) *app_pb.PiaChannelDramaPlayingType {
	p := &app_pb.PiaChannelDramaPlayingType{}
	if info == nil {
		return p
	}
	p.CurrentPlayingTypeTime = c.ChannelPlayingTypeTimeInfoToLogic(info.CurTimeType)
	p.CurrentPlayingTypeRole = c.ChannelPlayingTypeRoleInfoToLogic(info.CurRoleType)
	p.SupportPlayingTypeTimes = make([]app_pb.PiaChannelDramaPlayingType_PlayingTypeTime, 0, len(info.SupportForTime))
	p.SupportPlayingTypeRoles = make([]app_pb.PiaChannelDramaPlayingType_PlayingTypeRole, 0, len(info.SupportForRole))
	for _, item := range info.SupportForTime {
		p.SupportPlayingTypeTimes = append(p.SupportPlayingTypeTimes, c.ChannelPlayingTypeTimeInfoToLogic(item))
	}
	for _, item := range info.SupportForRole {
		p.SupportPlayingTypeRoles = append(p.SupportPlayingTypeRoles, c.ChannelPlayingTypeRoleInfoToLogic(item))
	}
	return p
}

func (c *convertor) ChannelPlayingTypeTimeInfoToLogic(info DramaPlayingType) app_pb.PiaChannelDramaPlayingType_PlayingTypeTime {
	switch info {
	default:
		// 无时间兜底
		return app_pb.PiaChannelDramaPlayingType_PIA_CHANNEL_DRAMA_PLAYING_TYPE_TIME_NONE
	case DramaPlayingTypeUnknown:
		// 无时间兜底
		return app_pb.PiaChannelDramaPlayingType_PIA_CHANNEL_DRAMA_PLAYING_TYPE_TIME_NONE
	case DramaPlayingTypeTiming:
		return app_pb.PiaChannelDramaPlayingType_PIA_CHANNEL_DRAMA_PLAYING_TYPE_TIME_HAS
	case DramaPlayingTypeManual:
		return app_pb.PiaChannelDramaPlayingType_PIA_CHANNEL_DRAMA_PLAYING_TYPE_TIME_NONE
	}
}

func (c *convertor) ChannelPlayingTypeRoleInfoToLogic(info DramaPlayingType) app_pb.PiaChannelDramaPlayingType_PlayingTypeRole {
	switch info {
	default:
		// 无角色兜底
		return app_pb.PiaChannelDramaPlayingType_PIA_CHANNEL_DRAMA_PLAYING_TYPE_ROLE_NONE
	case DramaPlayingTypeUnknown:
		// 无角色兜底
		return app_pb.PiaChannelDramaPlayingType_PIA_CHANNEL_DRAMA_PLAYING_TYPE_ROLE_NONE
	case DramaPlayingTypeNoRole:
		// 无角色兜底
		return app_pb.PiaChannelDramaPlayingType_PIA_CHANNEL_DRAMA_PLAYING_TYPE_ROLE_NONE
	case DramaPlayingTypeHasRole:
		return app_pb.PiaChannelDramaPlayingType_PIA_CHANNEL_DRAMA_PLAYING_TYPE_ROLE_HAS
	}
}

func (c *convertor) ChannelPlayingDramaInfoToChangePlayTypeLogic(info *ChannelPlayingDramaInfo) *app_pb.PiaChangePlayType {
	if info == nil {
		return &app_pb.PiaChangePlayType{}
	}
	result := &app_pb.PiaChangePlayType{
		ChannelId: info.Status.ChannelId,
		Version:   info.Status.Version,
		Progress: &app_pb.ChannelDramaProgress{
			CurIndex:   info.Status.CurContentIndex,
			TimeOffset: int64(info.Status.CurContentOffset.Seconds()),
		},
		StartTime:    info.Status.StartTime.Unix(),
		RoundId:      info.Status.RoundId,
		PlayingType:  c.ChannelPlayingTypeInfoToLogic(info.Status.PlayingType),
		MicRoleMap:   c.ChannelMicRoleBindingInfoToLogic(info.GetMicRoleMap()),
		BgmStatus:    c.BgmStatusToLogic(info.GetBgmProgress()),
		BgmVolStatus: c.BgmVolStatusToLogic(info.GetBgmVolSetting()),
		DramaPhase:   c.channelRunningStatusToLogic(info.Status.RunningStatus),
	}
	return result
}

func (c *convertor) channelRunningStatusToLogic(status DramaRunningStatus) app_pb.DramaPhase {
	switch status {
	case DramaRunningStatusSelectingRole:
		return app_pb.DramaPhase_DRAMA_PHASE_SELECT_ROLE
	case DramaRunningStatusPlaying:
		return app_pb.DramaPhase_DRAMA_PHASE_PLAY
	case DramaRunningStatusEnd:
		return app_pb.DramaPhase_DRAMA_PHASE_END
	case DramaRunningStatusPause:
		return app_pb.DramaPhase_DRAMA_PHASE_PAUSE
	default:
		return app_pb.DramaPhase_DRAMA_PHASE_UNSPECIFIED
	}
}

func (c *convertor) DramaInfoToPBDramaWithMaskField(info *DramaEntityV2, status *ChannelPlayingStatusInfo) *pb.DramaV2 {
	drama := c.DramaInfoToPBDrama(info)
	// 根据走本方式，动态修改剧本的走本类型和角色列表
	switch status.GetPlayingType().CurTimeType {
	case DramaPlayingTypeTiming:
		drama.PlayType = pb.PiaPlayType_PLAY_TYPE_AUTO
	case DramaPlayingTypeManual:
		drama.PlayType = pb.PiaPlayType_PLAY_TYPE_MANUAL
	default:
		drama.PlayType = pb.PiaPlayType_PLAY_TYPE_MANUAL
	}
	switch status.GetPlayingType().CurRoleType {
	case DramaPlayingTypeHasRole:
	// do nothing
	case DramaPlayingTypeNoRole:
		drama.RoleList = nil
	default:
		// 默认无角色
		drama.RoleList = nil
	}
	return drama
}

func (c *convertor) DramaInfoToLogicDramaWithMaskField(info *DramaEntityV2, status *ChannelPlayingStatusInfo) *app_pb.DramaV2 {
	drama := c.DramaInfoToLogicDrama(info)
	// 根据走本方式，动态修改剧本的走本类型和角色列表
	switch status.GetPlayingType().CurTimeType {
	case DramaPlayingTypeTiming:
		drama.PlayType = app_pb.PiaPlayType_PLAY_TYPE_AUTO
	case DramaPlayingTypeManual:
		drama.PlayType = app_pb.PiaPlayType_PLAY_TYPE_MANUAL
	default:
		drama.PlayType = app_pb.PiaPlayType_PLAY_TYPE_MANUAL
	}
	switch status.GetPlayingType().CurRoleType {
	case DramaPlayingTypeHasRole:
	// do nothing
	case DramaPlayingTypeNoRole:
		drama.RoleList = nil
	default:
		// 默认无角色
		drama.RoleList = nil
	}
	return drama
}

func (c *convertor) StickDramaInfoToPB(info *DramaStickEntity) *pb.SearchStickDramaResp_StickDramaInfo {
	if info == nil {
		return &pb.SearchStickDramaResp_StickDramaInfo{}
	}
	elems := &pb.SearchStickDramaResp_StickDramaInfo{
		StickId: info.ID,
		BaseInfo: &pb.StickDramaBaseInfo{
			DramaId:        info.DramaId,
			Tag:            info.Tag,
			Rank:           info.Rank,
			StickStartTime: info.BeginTime,
			StickEndTime:   info.EndTime,
		},
		DramaInfo: &pb.DramaInfoBase{
			Title:    info.DramaTitle,
			Ttid:     info.TTid,
			AuthorId: info.AuthorId,
		},
		StickStatus: pb.StickDramaStatus_STICK_DRAMA_STATUS_ALL,
	}
	ts := time.Now().Unix()
	if info.BeginTime <= ts && info.EndTime > ts {
		elems.StickStatus = pb.StickDramaStatus_STICK_DRAMA_STATUS_STICKING
	} else if ts >= info.EndTime {
		elems.StickStatus = pb.StickDramaStatus_STICK_DRAMA_STATUS_EXPIRED
	} else {
		elems.StickStatus = pb.StickDramaStatus_STICK_DRAMA_STATUS_WAITING
	}
	return elems
}

func (c *convertor) MyPlayingRecordToPB(info *MyPlayingRecord, dramaEntity *DramaEntityV2) *pb.PiaDramaPlayingRecord {
	if info == nil {
		return &pb.PiaDramaPlayingRecord{}
	}
	return &pb.PiaDramaPlayingRecord{
		DramaSubInfo: c.DramaInfoToPBSubInfo(dramaEntity),
		PlayingTime:  info.PlayTime,
		IsPullOff:    dramaEntity.IsFreeze || dramaEntity.IsPrivate || dramaEntity.Deleted,
		Id:           info.ID,
		IsCopy:       dramaEntity.RelatedID != 0, // 副本关联了原本id
	}
}

func (c *convertor) CopyDramaInfoWithStatusListToPB(info []*DramaEntityV2) []*pb.PiaCopyDramaInfoWithStatus {
	if len(info) == 0 {
		return []*pb.PiaCopyDramaInfoWithStatus{}
	}
	result := make([]*pb.PiaCopyDramaInfoWithStatus, 0, len(info))
	for _, v := range info {
		result = append(result, c.CopyDramaInfoWithStatusToPB(v))
	}
	return result
}

func (c *convertor) CopyDramaInfoWithStatusToPB(info *DramaEntityV2) *pb.PiaCopyDramaInfoWithStatus {
	if info == nil {
		return &pb.PiaCopyDramaInfoWithStatus{}
	}
	return &pb.PiaCopyDramaInfoWithStatus{
		DramaSubInfo: c.DramaInfoToPBSubInfo(info),
		IsPublic:     !info.IsPrivate,
		IsDelete:     info.Deleted,
		UseCount:     info.UseCount,
	}
}

func (c *convertor) OrderDramaListBizToPB(info *OrderDramaList) *pb.DramaOrderList {
	if info == nil {
		return &pb.DramaOrderList{}
	}
	result := &pb.DramaOrderList{
		ChannelId: info.ChannelID,
		Version:   info.Version,
		List:      make([]*pb.DramaOrderList_UserOrderInfo, 0, len(info.List)),
	}
	for _, v := range info.List {
		result.List = append(result.List, &pb.DramaOrderList_UserOrderInfo{
			DramaSubInfo: &pb.DramaSubInfo{
				Id: v.DramaID,
			},
			UserId:     v.UserID,
			IndexId:    v.IndexID,
			FakeUserId: v.FakeUserID,
		})
	}
	return result
}

func (c *convertor) ChannelPlayingTypeInfoFromPB(info *pb.PiaChannelDramaPlayingType) *ChannelPlayingTypeInfo {
	p := &ChannelPlayingTypeInfo{}
	if info == nil {
		return p
	}
	p.CurTimeType = c.ChannelPlayingTypeTimeInfoFromPB(info.GetCurrentPlayingTypeTime())
	p.CurRoleType = c.ChannelPlayingTypeRoleInfoFromPB(info.GetCurrentPlayingTypeRole())
	p.SupportForTime = make([]DramaPlayingType, 0, len(info.GetSupportPlayingTypeTimes()))
	p.SupportForRole = make([]DramaPlayingType, 0, len(info.GetSupportPlayingTypeRoles()))
	for _, item := range info.GetSupportPlayingTypeTimes() {
		p.SupportForTime = append(p.SupportForTime, c.ChannelPlayingTypeTimeInfoFromPB(item))
	}
	for _, item := range info.GetSupportPlayingTypeRoles() {
		p.SupportForRole = append(p.SupportForRole, c.ChannelPlayingTypeRoleInfoFromPB(item))
	}
	return p
}

func (c *convertor) ChannelPlayingTypeTimeInfoFromPB(info pb.PiaChannelDramaPlayingType_PlayingTypeTime) DramaPlayingType {
	switch info {
	default:
		// 无时间兜底
		return DramaPlayingTypeManual
	case pb.PiaChannelDramaPlayingType_PIA_CHANNEL_DRAMA_PLAYING_TYPE_TIME_INVALID:
		// 无时间兜底
		return DramaPlayingTypeManual
	case pb.PiaChannelDramaPlayingType_PIA_CHANNEL_DRAMA_PLAYING_TYPE_TIME_HAS:
		return DramaPlayingTypeTiming
	case pb.PiaChannelDramaPlayingType_PIA_CHANNEL_DRAMA_PLAYING_TYPE_TIME_NONE:
		return DramaPlayingTypeManual
	}
}

func (c *convertor) ChannelPlayingTypeRoleInfoFromPB(info pb.PiaChannelDramaPlayingType_PlayingTypeRole) DramaPlayingType {
	switch info {
	default:
		// 无角色兜底
		return DramaPlayingTypeNoRole
	case pb.PiaChannelDramaPlayingType_PIA_CHANNEL_DRAMA_PLAYING_TYPE_ROLE_INVALID:
		// 无角色兜底
		return DramaPlayingTypeNoRole
	case pb.PiaChannelDramaPlayingType_PIA_CHANNEL_DRAMA_PLAYING_TYPE_ROLE_NONE:
		// 无角色兜底
		return DramaPlayingTypeNoRole
	case pb.PiaChannelDramaPlayingType_PIA_CHANNEL_DRAMA_PLAYING_TYPE_ROLE_HAS:
		return DramaPlayingTypeHasRole
	}
}

func (c *convertor) DramaV2PBToLogic(info *pb.DramaV2) *app_pb.DramaV2 {
	if info == nil {
		return nil
	}
	result := &app_pb.DramaV2{
		DramaSubInfo: c.DramaSubInfoPBToLogic(info.GetDramaSubInfo()),
		RoleList:     c.DramaRoleListPBToLogic(info.GetRoleList()),
		ContentList:  c.DramaContentListPBToLogic(info.GetContentList()),
		BgmUrl:       c.DramaBgmListPBToLogic(info.GetBgmUrl()),
		PictureList:  c.DramaPictureListPBToLogic(info.GetPictureList()),
		PlayType:     app_pb.PiaPlayType(info.PlayType),
		IsCopyDrama:  info.GetDramaSubInfo().GetCreatorUid() != 0,
	}
	return result
}

func (c *convertor) DramaSubInfoPBToLogic(info *pb.DramaSubInfo) *app_pb.DramaSubInfo {
	if info == nil {
		return &app_pb.DramaSubInfo{}
	}
	return &app_pb.DramaSubInfo{
		Id:             info.GetId(),
		Title:          info.GetTitle(),
		CoverUrl:       info.GetCoverUrl(),
		Author:         info.GetAuthor(),
		Desc:           info.GetDesc(),
		MaleCnt:        info.GetMaleCnt(),
		FemaleCnt:      info.GetFemaleCnt(),
		WordCnt:        info.GetWordCnt(),
		TagList:        info.GetTagList(),
		Type:           info.GetType(),
		DisplayId:      info.GetDisplayId(),
		AuthorId:       info.GetAuthorId(),
		Duration:       info.GetDuration(),
		CreateTime:     info.GetCreateTime(),
		RelatedDramaId: info.GetRelatedDramaId(),
	}
}

func (c *convertor) DramaRoleListPBToLogic(info []*pb.PiaRole) []*app_pb.PiaRole {
	if len(info) == 0 {
		return []*app_pb.PiaRole{}
	}
	list := make([]*app_pb.PiaRole, 0, len(info))
	for _, item := range info {
		list = append(list, &app_pb.PiaRole{
			Id:            item.GetId(),
			Name:          item.GetName(),
			Sex:           item.GetSex(),
			Avatar:        item.GetAvatar(),
			Introduction:  item.GetIntroduction(),
			Color:         item.GetColor(),
			DialogueRatio: item.GetDialogueRatio(),
		})
	}
	return list
}

func (c *convertor) DramaContentListPBToLogic(info []*pb.PiaContent) []*app_pb.PiaContent {
	if len(info) == 0 {
		return []*app_pb.PiaContent{}
	}
	list := make([]*app_pb.PiaContent, 0, len(info))
	for _, item := range info {
		list = append(list, &app_pb.PiaContent{
			Id:       item.GetId(),
			RoleId:   item.GetRoleId(),
			RoleName: item.GetRoleName(),
			Dialogue: item.Dialogue,
			Duration: &app_pb.PiaDuration{
				BeginTime: item.GetDuration().GetBeginTime(),
				EndTime:   item.GetDuration().GetEndTime(),
			},
			Color: item.GetColor(),
		})
	}
	return list
}

func (c *convertor) DramaBgmListPBToLogic(info []*pb.PiaBGM) []*app_pb.PiaBGM {
	if len(info) == 0 {
		return []*app_pb.PiaBGM{}
	}
	list := make([]*app_pb.PiaBGM, 0, len(info))
	for _, item := range info {
		list = append(list, &app_pb.PiaBGM{
			Id:  item.GetId(),
			Url: item.GetUrl(),
			Duration: &app_pb.PiaDuration{
				BeginTime: item.GetDuration().GetBeginTime(),
				EndTime:   item.GetDuration().GetEndTime(),
			},
			Name:   item.GetName(),
			Length: item.GetLength(),
		})
	}
	return list
}

func (c *convertor) DramaPictureListPBToLogic(info []*pb.PiaPicture) []*app_pb.PiaPicture {
	if len(info) == 0 {
		return []*app_pb.PiaPicture{}
	}
	list := make([]*app_pb.PiaPicture, 0, len(info))
	for _, item := range info {
		list = append(list, &app_pb.PiaPicture{
			Id:  item.GetId(),
			Url: item.GetUrl(),
			Duration: &app_pb.PiaDuration{
				BeginTime: item.GetDuration().GetBeginTime(),
				EndTime:   item.GetDuration().GetEndTime(),
			},
		})
	}
	return list
}
