package mgr

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io/ioutil"
	"net/http"
	"strings"

	"golang.52tt.com/pkg/log"
	"golang.52tt.com/services/pia/conf"
)

var (
	ErrAlreadyReport      = errors.New("已经举报过了")
	ErrDramaNotFoundInWeb = errors.New("网站端不存在该剧本")
)

type CommonEmptyResp struct {
	Code    int    `json:"code"`
	Message string `json:"msg"`
}

//DramaWebApi 网站端提供的api接口集合
type DramaWebApi interface {
	// ReportDrama 上报剧本举报信息，如果已经举报过了，返回 ErrAlreadyReport
	ReportDrama(ctx context.Context, req *ReportDramaReq) error
	// ReportDrama 上报剧本反馈信息，如果已经反馈过了，返回 ErrAlreadyReport
	DramaFeedback(ctx context.Context, req *DramaFeedbackReq) error
}

type dramaWebApi struct {
	config conf.IConfig
}

func NewDramaWebApi(config conf.IConfig) DramaWebApi {
	return &dramaWebApi{
		config: config,
	}
}

func (d *dramaWebApi) ReportDrama(ctx context.Context, req *ReportDramaReq) error {
	data, err := json.Marshal(req)
	if err != nil {
		return fmt.Errorf("json.Marshal failed,err:%w", err)
	}
	log.DebugWithCtx(ctx, "[上报剧本举报信息到网站端]data:%s", string(data))
	request, err := http.NewRequest(http.MethodPost, d.config.GetDramaConfig().DramaReportUrl(), strings.NewReader(string(data)))
	if nil != err {
		log.ErrorWithCtx(ctx, "[上报剧本举报信息到网站端]http.NewRequest failed,err:%v", err)
		return err
	}
	request = request.WithContext(ctx)
	request.Header.Set("Content-Type", "application/json")
	request.Header.Set("Authorization", d.config.GetDramaConfig().SyncToken())
	log.DebugWithCtx(ctx, "[上报剧本举报信息到网站端]req:%v", request)
	resp, err := http.DefaultClient.Do(request)
	if nil != err {
		return fmt.Errorf("http.DefaultClient.Do failed,err:%w", err)
	}
	defer func() {
		if err2 := resp.Body.Close(); err2 != nil {
			log.ErrorWithCtx(ctx, "[上报剧本举报信息到网站端]failed to resp.Body.Close, err:%v", err2)
		}
	}()
	if resp.StatusCode != 200 {
		log.ErrorWithCtx(ctx, "[上报剧本举报信息到网站端]http.Post failed,resp.StatusCode:%v", resp.StatusCode)
		return errors.New("web server fail")
	}
	bodyStr, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("failed to ReadAll, err:%w", err)
	}
	log.DebugWithCtx(ctx, "[上报剧本举报信息到网站端]resp:%v", string(bodyStr))
	var respData CommonEmptyResp
	if err := json.Unmarshal(bodyStr, &respData); err != nil {
		log.ErrorWithCtx(ctx, "[上报剧本举报信息到网站端]json.Unmarshal failed,err:%v", err)
		return fmt.Errorf("解析resp.body出错, err:%w", err)
	}
	if respData.Code != 0 {
		if respData.Code == 2105 {
			return ErrAlreadyReport
		}
		log.ErrorWithCtx(ctx, "[上报剧本举报信息到网站端]respData.Code:%v", respData.Code)
		return errors.New(respData.Message)
	}
	return nil
}

func (d *dramaWebApi) DramaFeedback(ctx context.Context, req *DramaFeedbackReq) error {
	data, err := json.Marshal(req)
	if err != nil {
		log.ErrorWithCtx(ctx, "[上报剧本反馈信息到网站端]json.Marshal failed,err:%v", err)
		return err
	}
	log.DebugWithCtx(ctx, "[上报剧本反馈信息到网站端]data:%s", string(data))
	request, err := http.NewRequest(http.MethodPost, d.config.GetDramaConfig().DramaFeedbackUrl(), strings.NewReader(string(data)))
	if nil != err {
		log.ErrorWithCtx(ctx, "[上报剧本反馈信息到网站端]http.NewRequest failed,err:%v", err)
		return err
	}
	request = request.WithContext(ctx)
	request.Header.Set("Content-Type", "application/json")
	request.Header.Set("Authorization", d.config.GetDramaConfig().SyncToken())
	log.DebugWithCtx(ctx, "[上报剧本反馈信息到网站端]req:%v", request)
	resp, err := http.DefaultClient.Do(request)
	if nil != err {
		log.ErrorWithCtx(ctx, "[上报剧本反馈信息到网站端]http.DefaultClient.Do failed,err:%v", err)
		return err
	}
	defer func() {
		if err2 := resp.Body.Close(); err2 != nil {
			log.ErrorWithCtx(ctx, "[上报剧本反馈信息到网站端]failed to resp.Body.Close, err:%v", err2)
		}
	}()
	if resp.StatusCode != 200 {
		log.ErrorWithCtx(ctx, "[上报剧本反馈信息到网站端]http.Post failed,resp.StatusCode:%v", resp.StatusCode)
		return errors.New("web server fail")
	}
	bodyStr, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		log.ErrorWithCtx(ctx, "[上报剧本反馈信息到网站端]failed to ReadAll, err:%v", err)
		return err
	}
	log.DebugWithCtx(ctx, "[上报剧本反馈信息到网站端]resp:%v", string(bodyStr))
	var respData CommonEmptyResp
	if err := json.Unmarshal(bodyStr, &respData); err != nil {
		log.ErrorWithCtx(ctx, "[上报剧本反馈信息到网站端]json.Unmarshal failed,err:%v", err)
		return err
	}
	// 网站端错误码非0处理
	if respData.Code != 0 {
		switch respData.Code {
		case 1002:
			log.ErrorWithCtx(ctx, "网站端不存在该剧本，dramaId:%d", req.DramaId)
			return ErrDramaNotFoundInWeb
		case 28003:
			log.InfoWithCtx(ctx, "该剧本已经上报过了, dramaId:%d", req.DramaId)
			return ErrAlreadyReport
		default:
			log.ErrorWithCtx(ctx, "[上报剧本反馈信息到网站端]respData.Code:%v", respData.Code)
			return errors.New(respData.Message)
		}
	}
	return nil
}

//ReportDramaReq 上报剧本举报信息请求
type ReportDramaReq struct {
	ReportUserId   string   `json:"reportUserId"`   // 举报人Id
	ReportUserName string   `json:"reportUserName"` // 举报人昵称
	DramaId        string   `json:"dramaId"`        // 剧本_id
	ReasonList     []string `json:"reasonList"`     // 举报原因
	ReasonDetail   string   `json:"reasonDetail"`   // 举报详情
	EvidenceUrl    []string `json:"evidenceUrl"`    // 证据截图
	BusinessId     string   `json:"businessId"`     // 举报业务id，用于业务回调时自身管理参数,业务通过businessId内部查询需要的值
}

//ReportDramaReq 上报剧本举报信息请求
type DramaFeedbackReq struct {
	FeedbackType uint32   `json:"type"`       // 反馈类型
	DramaId      uint32   `json:"dramaDid"`   // 剧本id
	Content      string   `json:"content"`    // 反馈详情
	Pictures     []string `json:"pictures"`   // 反馈截图
	BusinessId   string   `json:"businessId"` // 反馈业务id，用于业务回调时自身管理参数,业务通过businessId内部查询需要的值
}
