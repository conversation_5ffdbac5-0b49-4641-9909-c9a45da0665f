package server

import (
	"context"
	revenue_ext_game "golang.52tt.com/clients/revenue-ext-game"
	"golang.52tt.com/pkg/client"
	client1 "golang.52tt.com/pkg/foundation/grpc/client"
	sentinel_destructive_verify "golang.52tt.com/pkg/sentinel/destructive-verify"
	sentinel_interceptor "golang.52tt.com/pkg/sentinel/interceptor"
	"golang.52tt.com/protocol/services/demo/echo"
	switch_scheme_checker "golang.52tt.com/protocol/services/switch-scheme-checker"
	"golang.52tt.com/services/masked-pk/masked-pk-logic/internal/breaker"
	"golang.52tt.com/services/masked-pk/masked-pk-logic/internal/conf"
	"time"

	"golang.52tt.com/clients/account"
	userprofileapi "golang.52tt.com/clients/user-profile-api"
	youknowwho "golang.52tt.com/clients/you-know-who"
	pgcchannelgamepb "golang.52tt.com/protocol/services/pgc-channel-game"

	"golang.52tt.com/clients/channel"
	channellivefans "golang.52tt.com/clients/channel-live-fans"
	channelmemberviprank "golang.52tt.com/clients/channelmemberVipRank"
	"golang.52tt.com/clients/channelmic"
	knightprivilege "golang.52tt.com/clients/knight-privilege"
	masked_pk_live "golang.52tt.com/clients/masked-pk-live"
	masked_pk_svr "golang.52tt.com/clients/masked-pk-svr"
	"golang.52tt.com/clients/nobility"
	"golang.52tt.com/clients/numeric"
	superPlayerCli "golang.52tt.com/clients/super-player-svr"
	"golang.52tt.com/clients/taillight"
	userPresent "golang.52tt.com/clients/userpresent"
	"golang.52tt.com/pkg/files"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	protogrpc "golang.52tt.com/pkg/protocol/grpc"
	"golang.52tt.com/protocol/app"
	channelpb "golang.52tt.com/protocol/app/channel"
	pb "golang.52tt.com/protocol/app/masked-pk-logic"
	"golang.52tt.com/protocol/common/status"
	tailLightPB "golang.52tt.com/protocol/services/activity-taillight"
	channelMicPb "golang.52tt.com/protocol/services/channelmicsvr"
	knightprivilegePB "golang.52tt.com/protocol/services/knight-privilege"
	masked_pk_svr2 "golang.52tt.com/protocol/services/masked-pk-svr"
	"google.golang.org/grpc"
)

// const configFile = "/data/oss/conf-center/tt/quick-send-present.json"
const ChannelRankCount = 50

type MaskedPKLogic_ struct {
	maskedPKCli           *masked_pk_svr.Client
	maskedPKLiveCli       *masked_pk_live.Client
	channelCli            *channel.Client
	channelMicCli         *channelmic.Client
	fansCli               *channellivefans.Client
	nobilityClient        *nobility.Client
	numericClient         *numeric.Client
	memberRank            *channelmemberviprank.Client
	superPlayerClient     *superPlayerCli.Client
	taillightClient       *taillight.Client
	knightprivilegeClient *knightprivilege.Client
	sc                    *conf.StartConfig
	userProfileClient     *userprofileapi.Client
	youKnowWhoClient      *youknowwho.Client
	accountClient         *account.Client
	pgcChannelGameCli     pgcchannelgamepb.PgcChannelGameClient
	offerRoomCli          switch_scheme_checker.SwitchSchemeCheckerClient
	revenueExtGameCli     revenue_ext_game.IClient
}

func (s *MaskedPKLogic_) Echo(c context.Context, message *echo.StringMessage) (*echo.StringMessage, error) {
	return message, nil
}

func NewMaskedPKLogic_(ctx context.Context, cfg *conf.StartConfig) (*MaskedPKLogic_, error) {

	//初始化熔断器
	err := breaker.Setup(ctx)
	if nil != err {
		log.ErrorWithCtx(ctx, "Failed to setup breaker, err: %+v", err)
		return nil, err
	}

	opts := []grpc.DialOption{
		grpc.WithBlock(),
		grpc.WithChainUnaryInterceptor(
			sentinel_interceptor.UnaryClientInterceptor(),                                 // 熔断拦截器
			sentinel_destructive_verify.UnarySlowCallClientInterceptor("masked-pk-logic"), //熔断慢调用验证拦截器
		),
	}

	maskedPKCli, _ := masked_pk_svr.NewClient()
	maskedPKLiveCli, _ := masked_pk_live.NewClient()

	fansCli, _ := channellivefans.NewClient()
	nobilityClient, _ := nobility.NewClient()
	numericClient := numeric.NewClient()
	memberRank := channelmemberviprank.NewClient()

	presentCli := userPresent.NewClient(grpc.WithBlock())
	superPlayerClient, _ := superPlayerCli.NewClient()
	taillightClient, _ := taillight.NewClient()
	knightprivilegeClient := knightprivilege.NewClient()
	userProfileClient, _ := userprofileapi.NewClient()
	youKnowWhoClient, _ := youknowwho.NewClient(opts...)
	accountClient, _ := account.NewClient()
	offerRoomCli := client.NewInsecureGRPCClient(
		"offer-room",
		func(cc *grpc.ClientConn) interface{} {
			return switch_scheme_checker.NewSwitchSchemeCheckerClient(cc)
		},
		grpc.WithInsecure(),
		grpc.WithUnaryInterceptor(client1.UnaryClientInterceptor),
	)

	sc := &conf.StartConfig{}
	err = sc.ConfigInit(context.Background(), presentCli)
	if err != nil {
		log.ErrorWithCtx(ctx, "ConfigInit err , %v", err)
	}

	pgcChannelGameCli, _ := pgcchannelgamepb.NewClient(ctx)
	revenueExtGameCli, _ := revenue_ext_game.NewClient()

	s := &MaskedPKLogic_{
		maskedPKCli:           maskedPKCli,
		maskedPKLiveCli:       maskedPKLiveCli,
		channelCli:            channel.NewClient(),
		channelMicCli:         channelmic.NewClient(),
		fansCli:               fansCli,
		nobilityClient:        nobilityClient,
		numericClient:         numericClient,
		memberRank:            memberRank,
		sc:                    sc,
		superPlayerClient:     superPlayerClient,
		taillightClient:       taillightClient,
		knightprivilegeClient: knightprivilegeClient,
		userProfileClient:     userProfileClient,
		youKnowWhoClient:      youKnowWhoClient,
		accountClient:         accountClient,
		pgcChannelGameCli:     pgcChannelGameCli,
		offerRoomCli:          offerRoomCli.Stub().(switch_scheme_checker.SwitchSchemeCheckerClient),
		revenueExtGameCli:     revenueExtGameCli,
	}

	s.maintain(sc)

	return s, nil
}

func (s *MaskedPKLogic_) maintain(sc *conf.StartConfig) {
	watch := files.NewFileModifyWatch(conf.ConfigFile, time.Second)
	go watch.Start(func() {
		sc.Load(context.Background())
	})
}

// 开始pk流程（包括领取筹码）
func (s *MaskedPKLogic_) StartChannelMaskedPK(ctx context.Context, in *pb.StartChannelMaskedPKReq) (*pb.StartChannelMaskedPKResp, error) {
	out := &pb.StartChannelMaskedPKResp{}

	serviceInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
	if !ok || in.GetChannelId() == 0 {
		log.ErrorWithCtx(ctx, "StartChannelMaskedPK fail to ServiceInfoFromContext. in:%+v", in)
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}

	opUid := serviceInfo.UserID
	cid := in.GetChannelId()

	err := s.checkClientVersion(serviceInfo)
	if err != nil {
		log.ErrorWithCtx(ctx, "StartChannelMaskedPK fail to checkClientVersion. uid:%v, in:%+v err:%v", opUid, in, err)
		return out, err
	}

	err = s.checkOpUserPermission(ctx, opUid, cid)
	if err != nil {
		log.ErrorWithCtx(ctx, "StartChannelMaskedPK fail to checkOpUserPermission. uid:%v, in:%+v err:%v", opUid, in, err)
		return out, err
	}

	_, err = s.maskedPKCli.StartChannelMaskedPK(ctx, opUid, cid)
	if err != nil {
		log.ErrorWithCtx(ctx, "StartChannelMaskedPK fail to StartChannelMaskedPK. uid:%v, in:%+v err:%v", opUid, in, err)
		return out, err
	}

	log.InfoWithCtx(ctx, "StartChannelMaskedPK uid:%v, in:%+v out:%+v", opUid, in, out)
	return out, nil
}

// 放弃参与本次比赛
func (s *MaskedPKLogic_) GiveUpChannelMaskedPK(ctx context.Context, in *pb.GiveUpChannelMaskedPKReq) (*pb.GiveUpChannelMaskedPKResp, error) {
	out := &pb.GiveUpChannelMaskedPKResp{}

	serviceInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
	if !ok || in.GetChannelId() == 0 {
		log.ErrorWithCtx(ctx, "GiveUpChannelMaskedPK fail to ServiceInfoFromContext. in:%+v", in)
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}

	opUid := serviceInfo.UserID
	cid := in.GetChannelId()

	err := s.checkClientVersion(serviceInfo)
	if err != nil {
		log.ErrorWithCtx(ctx, "GiveUpChannelMaskedPK fail to checkClientVersion. uid:%v, in:%+v err:%v", opUid, in, err)
		return out, err
	}

	err = s.checkOpUserPermission(ctx, opUid, cid)
	if err != nil {
		log.ErrorWithCtx(ctx, "GiveUpChannelMaskedPK fail to checkOpUserPermission. uid:%v, in:%+v err:%v", opUid, in, err)
		return out, err
	}

	_, err = s.maskedPKCli.GiveUpChannelMaskedPK(ctx, opUid, in.GetConfId(), cid)
	if err != nil {
		log.ErrorWithCtx(ctx, "GiveUpChannelMaskedPK fail to StartChannelMaskedPK. uid:%v, in:%+v err:%v", opUid, in, err)
		return out, err
	}

	log.InfoWithCtx(ctx, "GiveUpChannelMaskedPK uid:%v, in:%+v out:%+v", opUid, in, out)
	return out, nil
}

// 取消pk匹配
func (s *MaskedPKLogic_) CancelChannelMaskedPK(ctx context.Context, in *pb.CancelChannelMaskedPKReq) (*pb.CancelChannelMaskedPKResp, error) {
	out := &pb.CancelChannelMaskedPKResp{}

	serviceInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
	if !ok || in.GetChannelId() == 0 {
		log.ErrorWithCtx(ctx, "CancelChannelMaskedPK fail to ServiceInfoFromContext. in:%+v", in)
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}

	opUid := serviceInfo.UserID
	cid := in.GetChannelId()

	err := s.checkClientVersion(serviceInfo)
	if err != nil {
		log.ErrorWithCtx(ctx, "CancelChannelMaskedPK fail to checkClientVersion. uid:%v, in:%+v err:%v", opUid, in, err)
		return out, err
	}

	err = s.checkOpUserPermission(ctx, opUid, cid)
	if err != nil {
		log.ErrorWithCtx(ctx, "CancelChannelMaskedPK fail to checkOpUserPermission. uid:%v, in:%+v err:%v", opUid, in, err)
		return out, err
	}

	_, err = s.maskedPKCli.CancelChannelMaskedPK(ctx, opUid, cid)
	if err != nil {
		log.ErrorWithCtx(ctx, "CancelChannelMaskedPK fail to CancelChannelMaskedPK. uid:%v, in:%+v err:%v", opUid, in, err)
		return out, err
	}

	log.InfoWithCtx(ctx, "CancelChannelMaskedPK uid:%v, in:%+v out:%+v", opUid, in, out)
	return out, nil
}

// 获取pk信息
func (s *MaskedPKLogic_) GetChannelMaskedPKInfo(ctx context.Context, in *pb.GetChannelMaskedPKInfoReq) (*pb.GetChannelMaskedPKInfoResp, error) {
	out := &pb.GetChannelMaskedPKInfoResp{}

	serviceInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
	if !ok || in.GetChannelId() == 0 {
		log.ErrorWithCtx(ctx, "GetChannelMaskedPKInfo fail to ServiceInfoFromContext. in:%+v", in)
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}

	opUid := serviceInfo.UserID
	cid := in.GetChannelId()

	err := s.checkClientVersion(serviceInfo)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetChannelMaskedPKInfo fail to checkClientVersion. uid:%v, in:%+v err:%v", opUid, in, err)
		return out, err
	}

	err = s.checkEntertainmentChannelType(ctx, opUid, cid)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetChannelMaskedPKInfo fail to checkEntertainmentChannelType. uid:%v, in:%+v err:%v", opUid, in, err)
		return out, err
	}

	resp, err := s.maskedPKCli.GetChannelMaskedPKInfo(ctx, opUid, cid)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetChannelMaskedPKInfo fail to GetChannelMaskedPKInfo. uid:%v, in:%+v err:%v", opUid, in, err)
		return out, err
	}

	// 未参与，直接返回空结果
	if resp.GetStatusInfo().GetStatus() == uint32(pb.ChannelMaskedPKStatus_NotParticipating) {
		out.StatusInfo = &pb.ChannelMaskedPKStatus{
			ChannelId: cid,
			Status:    uint32(pb.ChannelMaskedPKStatus_NotParticipating),
			ServerNs:  time.Now().UnixNano(),
		}
		log.DebugfWithCtx(ctx, "GetChannelMaskedPKInfo in:%+v, out:%+v", in, out)
		return out, nil
	}

	out.StatusInfo = s.fillPKStatusPb(resp.GetStatusInfo())
	out.PkBattleInfo = s.fillPKBattlePb(ctx, resp.GetPkBattleInfo())

	out.ReviveInfo = &pb.ChannelMaskedPKRevive{
		ChannelId: resp.GetReviveInfo().GetChannelId(),
		Goal:      resp.GetReviveInfo().GetGoal(),
		Curr:      resp.GetReviveInfo().GetCurr(),
		EndTs:     resp.GetReviveInfo().GetEndTs(),
		ServerNs:  resp.GetReviveInfo().GetServerNs(),
	}

	out.ServerNs = time.Now().UnixNano()

	log.DebugfWithCtx(ctx, "GetChannelMaskedPKInfo in:%+v, out:%+v", in, out)
	return out, nil
}

// 获取当前pk活动配置
func (s *MaskedPKLogic_) GetChannelMaskedPKConf(ctx context.Context, in *pb.GetChannelMaskedPKConfReq) (*pb.GetChannelMaskedPKConfResp, error) {
	out := &pb.GetChannelMaskedPKConfResp{}

	// 判断公会房小游戏玩法, 如果有游戏在进行则不反悔蒙面pk相关配置
	pResp, err := s.pgcChannelGameCli.GetChannelGameInfo(ctx, &pgcchannelgamepb.GetChannelGameInfoReq{ChannelId: in.GetChannelId()})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetChannelMaskedPKCurrConfWithUser.GetChannelGameInfo, channelId: %d, err: %v", in.GetChannelId(), err)
	}
	if pResp.GetPhase() != 0 {
		return out, nil
	}
	// 判断是否在拍卖玩法
	check, err := s.offerRoomCli.Check(ctx, &switch_scheme_checker.CheckReq{
		Cid: in.GetChannelId(),
		FromScheme: &switch_scheme_checker.SchemeInfo{
			SchemeId:            0,
			SchemeName:          "",
			SchemeSvrDetailType: 0,
		},
		ToScheme: &switch_scheme_checker.SchemeInfo{
			SchemeId:            0,
			SchemeName:          "拍卖房",
			SchemeSvrDetailType: 28,
		},
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "checkPKGameStart 检查拍卖房 fail to Check err:%v", err)
	}
	if check.GetCode() != 0 {
		return out, nil
	}

	serviceInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
	if !ok || in.GetChannelId() == 0 {
		log.ErrorWithCtx(ctx, "GetChannelMaskedPKConf fail to ServiceInfoFromContext. in:%+v", in)
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}

	opUid := serviceInfo.UserID
	cid := in.GetChannelId()

	err = s.checkClientVersion(serviceInfo)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetChannelMaskedPKConf fail to checkClientVersion. uid:%v, in:%+v err:%v", opUid, in, err)
		return out, err
	}

	err = s.checkOpUserPermission(ctx, opUid, cid)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetChannelMaskedPKConf fail to checkOpUserPermission. uid:%v, in:%+v err:%v", opUid, in, err)
		return out, err
	}

	resp, err := s.maskedPKCli.GetChannelMaskedPKCurrConfWithUser(ctx, opUid, in.GetChannelId())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetChannelMaskedPKConf fail to GetChannelMaskedPKInfo. uid:%v, in:%+v err:%v", opUid, in, err)
		return out, err
	}

	if resp.GetConf() != nil {
		out.Conf = &pb.ChannelMaskedPKConf{
			BeginTs:          resp.GetConf().GetBeginTs(),
			EndTs:            resp.GetConf().GetEndTs(),
			ChipRole:         resp.GetConf().GetChipRole(),
			Chip:             resp.GetConf().GetChip(),
			ContinueMatch:    resp.GetConf().GetContinueMatch(),
			AutoMatchingCnt:  resp.GetConf().GetAutoMatchingCnt(),
			JumpUrl:          resp.GetConf().GetJumpUrl(),
			ConfId:           resp.GetConf().GetConfId(),
			DivideType:       resp.GetConf().GetDivideType(),
			ServerNs:         time.Now().UnixNano(),
			ChipReceiveEndTs: resp.GetConf().GetChipReceiveEndTs(),
		}
	}
	out.IsGiveUp = resp.GetIsGiveUp()

	log.DebugfWithCtx(ctx, "GetChannelMaskedPKConf in:%+v, out:%+v", in, out)
	return out, nil
}

// 获取主播战力榜
func (s *MaskedPKLogic_) GetChannelMaskedPKAnchorRank(ctx context.Context, in *pb.GetChannelMaskedPKAnchorRankReq) (*pb.GetChannelMaskedPKAnchorRankResp, error) {
	out := &pb.GetChannelMaskedPKAnchorRankResp{}

	serviceInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
	if !ok || in.GetChannelId() == 0 {
		log.ErrorWithCtx(ctx, "GetChannelMaskedPKConf fail to ServiceInfoFromContext. in:%+v", in)
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}

	opUid := serviceInfo.UserID
	cid := in.GetChannelId()

	err := s.checkClientVersion(serviceInfo)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetChannelMaskedPKAnchorRank fail to checkClientVersion. uid:%v, in:%+v err:%v", opUid, in, err)
		return out, err
	}

	err = s.checkEntertainmentChannelType(ctx, opUid, cid)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetChannelMaskedPKAnchorRank fail to checkEntertainmentChannelType. uid:%v, in:%+v err:%v", opUid, in, err)
		return out, err
	}

	resp, err := s.maskedPKCli.GetChannelMaskedPKAnchorRank(ctx, opUid, in.GetChannelId(), in.GetBegin(), in.GetLimit())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetChannelMaskedPKAnchorRank fail to GetChannelMaskedPKAnchorRank. uid:%v, in:%+v err:%v", opUid, in, err)
		return out, err
	}

	out.RankList = make([]*pb.ChannelMaskedPKAnchorRankMem, 0)
	for _, info := range resp.GetRankList() {
		log.Debugf("GetRankList :%+v", info)
		//TopAudienceList
		audienceList := make([]*pb.ChannelMaskedPKAnchor, 0)
		for _, audience := range info.GetTopAudienceList() {
			profile := audience.GetProfile()
			channelMaskedPKAnchor := &pb.ChannelMaskedPKAnchor{
				Uid:      audience.GetUid(),
				Score:    audience.GetScore(),
				NickName: profile.GetNickname(),
				Account:  profile.GetAccount(),
				Sex:      profile.GetSex(),
				UserProfile: &app.UserProfile{
					Uid:      audience.GetUid(),
					Account:  profile.GetAccount(),
					Nickname: profile.GetNickname(),
					Sex:      profile.GetSex(),
				},
			}

			if len(profile.GetPrivilege().GetAccount()) > 0 {
				channelMaskedPKAnchor.UserProfile.Privilege = &app.UserPrivilege{
					Account:  profile.GetPrivilege().GetAccount(),
					Nickname: profile.GetPrivilege().GetNickname(),
					Type:     profile.GetPrivilege().GetType(),
					Options:  profile.GetPrivilege().GetOptions(),
				}
			}
			audienceList = append(audienceList, channelMaskedPKAnchor)
		}

		out.RankList = append(out.RankList, &pb.ChannelMaskedPKAnchorRankMem{
			Uid:             info.GetUid(),
			Account:         info.GetProfile().GetAccount(),
			NickName:        info.GetProfile().GetNickname(),
			Sex:             info.GetProfile().GetSex(),
			Score:           info.GetScore(),
			Bonus:           info.GetBonus(),
			Ratio:           info.GetRatio(),
			TopAudienceList: audienceList,
		})
	}

	log.DebugfWithCtx(ctx, "GetChannelMaskedPKAnchorRank in:%+v, out:%+v", in, out)
	return out, nil
}

// 获取单场pk主播战力榜
func (s *MaskedPKLogic_) GetChannelSinglePKAnchorRank(ctx context.Context, in *pb.GetChannelSinglePKAnchorRankReq) (*pb.GetChannelSinglePKAnchorRankResp, error) {
	out := &pb.GetChannelSinglePKAnchorRankResp{}

	serviceInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
	if !ok || in.GetChannelId() == 0 {
		log.ErrorWithCtx(ctx, "GetChannelSinglePKAnchorRank fail to ServiceInfoFromContext. in:%+v", in)
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}

	opUid := serviceInfo.UserID
	cid := in.GetChannelId()

	err := s.checkClientVersion(serviceInfo)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetChannelSinglePKAnchorRank fail to checkClientVersion. uid:%v, in:%+v err:%v", opUid, in, err)
		return out, err
	}

	err = s.checkEntertainmentChannelType(ctx, opUid, cid)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetChannelSinglePKAnchorRank fail to checkEntertainmentChannelType. uid:%v, in:%+v err:%v", opUid, in, err)
		return out, err
	}

	resp, err := s.maskedPKCli.GetChannelSinglePKAnchorRank(ctx, opUid, in.GetChannelId(), in.GetBegin(), in.GetLimit())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetChannelSinglePKAnchorRank fail to GetChannelSinglePKAnchorRank. uid:%v, in:%+v err:%v", opUid, in, err)
		return out, err
	}

	log.DebugfWithCtx(ctx, "GetChannelSinglePKAnchorRank from svr in:%+v, resp:%+v", in, resp)

	out.RankList = make([]*pb.ChannelMaskedPKAnchorRankMem, 0)
	for _, info := range resp.GetRankList() {
		audienceList := make([]*pb.ChannelMaskedPKAnchor, 0)
		for _, audience := range info.GetTopAudienceList() {
			profile := audience.GetProfile()
			channelMaskedPKAnchor := &pb.ChannelMaskedPKAnchor{
				Uid:      audience.GetUid(),
				Score:    audience.GetScore(),
				Account:  profile.GetAccount(),
				NickName: profile.GetNickname(),
				Sex:      profile.GetSex(),
				UserProfile: &app.UserProfile{
					Uid:      audience.GetUid(),
					Account:  profile.GetAccount(),
					Nickname: profile.GetNickname(),
					Sex:      profile.GetSex(),
				},
			}

			if len(profile.GetPrivilege().GetAccount()) > 0 {
				channelMaskedPKAnchor.UserProfile.Privilege = &app.UserPrivilege{
					Account:  profile.GetPrivilege().GetAccount(),
					Nickname: profile.GetPrivilege().GetNickname(),
					Type:     profile.GetPrivilege().GetType(),
					Options:  profile.GetPrivilege().GetOptions(),
				}
			}
			audienceList = append(audienceList, channelMaskedPKAnchor)
		}

		profile := info.GetProfile()
		out.RankList = append(out.RankList, &pb.ChannelMaskedPKAnchorRankMem{
			Uid:             info.GetUid(),
			Account:         profile.GetAccount(),
			NickName:        profile.GetNickname(),
			Sex:             profile.GetSex(),
			Score:           info.GetScore(),
			Bonus:           info.GetBonus(),
			Ratio:           info.GetRatio(),
			TopAudienceList: audienceList,
		})
	}

	log.DebugfWithCtx(ctx, "GetChannelSinglePKAnchorRank in:%+v, out:%+v", in, out)
	return out, nil
}

// 获取房间有蒙面pk资格的主播列表
func (s *MaskedPKLogic_) GetChannelQualifiedAnchor(ctx context.Context, in *pb.GetChannelQualifiedAnchorReq) (*pb.GetChannelQualifiedAnchorResp, error) {
	out := &pb.GetChannelQualifiedAnchorResp{}
	var err error

	if in.GetChannelId() == 0 {
		return out, nil
	}

	anchorUidList, err := s.maskedPKCli.GetChannelQualifiedAnchorList(ctx, in.GetChannelId())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetChannelQualifiedAnchor fail to GetChannelQualifiedAnchorList. in:%+v err:%v", in, err)
		return out, err
	}

	profileMap, err := s.userProfileClient.BatchGetUserProfileV2(ctx, anchorUidList, true)
	if err != nil {
		out.AnchorUidList = append(out.AnchorUidList, anchorUidList...)
		return out, nil
	}

	for _, uid := range anchorUidList {
		out.AnchorUidList = append(out.AnchorUidList, uid)
		profile := profileMap[uid]
		if profile.GetUid() != uid {
			out.AnchorUidList = append(out.AnchorUidList, profile.GetUid())
		}
	}
	log.Debugf("GetChannelQualifiedAnchor. in:%+v out:%+v", in, out)
	return out, nil
}

// 获取娱乐房快捷送礼列表
func (s *MaskedPKLogic_) GetQuickSendPresentConfig(ctx context.Context, in *pb.GetQuickSendPresentConfigReq) (*pb.GetQuickSendPresentConfigResp, error) {
	out := &pb.GetQuickSendPresentConfigResp{}
	out.Tips = s.sc.Tips
	out.PresentList = s.sc.GetPresentConfig()

	pkUidList, err := s.maskedPKCli.GetChannelQualifiedAnchorList(ctx, in.ChannelId)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetQuickSendPresentConfig fail to GetChannelQualifiedAnchorList. cid:%+v err:%v", in.ChannelId, err)
		return out, err
	}

	profileMap, err := s.userProfileClient.BatchGetUserProfileV2(ctx, pkUidList, true)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetQuickSendPresentConfig fail to BatchGetUserProfileV2. cid:%+v err:%v", in.ChannelId, err)
		return out, err
	}
	for _, uid := range pkUidList {
		out.PkUidList = append(out.PkUidList, uid)
		profile := profileMap[uid]
		if uid != profile.GetUid() {
			out.PkUidList = append(out.PkUidList, profile.GetUid())
		}
	}
	log.DebugWithCtx(ctx, "GetQuickSendPresentConfig  cid:%+v out:%v", in.ChannelId, out)
	return out, nil
}

func (s *MaskedPKLogic_) CheckMaskedPkRankEntry(ctx context.Context, in *pb.CheckMaskedPkRankEntryReq) (out *pb.CheckMaskedPkRankEntryResp, err error) {
	out = &pb.CheckMaskedPkRankEntryResp{}
	serviceInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
	if !ok || in.GetChannelId() == 0 {
		log.ErrorWithCtx(ctx, "CheckMaskedPkRankEntry fail to ServiceInfoFromContext. in:%+v", in)
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}

	opUid := serviceInfo.UserID
	cid := in.GetChannelId()
	out.EntryEnable = false
	switch channelpb.ChannelType(in.GetChannelType()) {
	case channelpb.ChannelType_GUILD_PUBLIC_FUN_CHANNEL_TYPE: // 公会公开房
		out.EntryEnable, err = s.maskedPKCli.CheckMaskedPkRankEntry(ctx)
		if err != nil {
			log.ErrorWithCtx(ctx, "CheckMaskedPkRankEntry failed uid:%d, err:%v", opUid, err)
		}
	case channelpb.ChannelType_RADIO_LIVE_CHANNEL_TYPE: // 直播房
		out.EntryEnable, err = s.maskedPKLiveCli.CheckMaskedPkRankEntry(ctx)
		if err != nil {
			log.ErrorWithCtx(ctx, "CheckMaskedPkRankEntry failed uid:%d, err:%v", opUid, err)
		}
	default:
		log.ErrorWithCtx(ctx, "CheckMaskedPkRankEntry fail. uid:%v, cid:%+v err: wrong channel type ", opUid, cid)
		return out, nil
	}
	return out, nil
}

func (s *MaskedPKLogic_) checkEntertainmentChannelType(ctx context.Context, opUid, cid uint32) error {
	channelInfo, err := s.channelCli.GetChannelSimpleInfo(ctx, opUid, cid)
	if err != nil {
		log.ErrorWithCtx(ctx, "checkEntertainmentChannelType fail to GetChannelSimpleInfo. uid:%v, cid:%+v err:%v", opUid, cid, err)
		return err
	}

	// 检查房间类型
	if channelInfo.GetChannelType() != uint32(channelpb.ChannelType_GUILD_PUBLIC_FUN_CHANNEL_TYPE) {
		log.ErrorWithCtx(ctx, "checkEntertainmentChannelType fail. uid:%v, cid:%+v err: wrong channel type ", opUid, cid)
		return protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}

	resp, err := s.channelMicCli.GetChannelMicMode(ctx, opUid, &channelMicPb.GetChannelMicModeReq{
		ChannelId: cid,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "checkEntertainmentChannelType fail to GetChannelMicMode. uid:%v, cid:%+v err:%v", opUid, cid, err)
		return err
	}

	// 检查麦位模式
	if resp.GetMicMode() == uint32(channelpb.EChannelMicMode_DATING_MIC_SPACE_MODE) ||
		resp.GetMicMode() == uint32(channelpb.EChannelMicMode_CP_GAME_MIC_SPACE_MODE) {
		log.ErrorWithCtx(ctx, "checkEntertainmentChannelType fail. uid:%v, cid:%+v err: mid mod is EChannelMicMode_DATING_MIC_SPACE_MODE ", opUid, cid)
		return protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "该麦位模式不支持蒙面PK")
	}

	return nil
}

func (s *MaskedPKLogic_) checkOpUserPermission(ctx context.Context, opUid, cid uint32) error {
	err := s.checkEntertainmentChannelType(ctx, opUid, cid)
	if err != nil {
		log.ErrorWithCtx(ctx, "checkOpUserPermission fail to checkEntertainmentChannelType. uid:%v, cid:%+v err:%v", opUid, cid, err)
		return err
	}

	resp, err := s.channelMicCli.GetMicrList(ctx, cid, opUid)
	if err != nil {
		log.ErrorWithCtx(ctx, "checkOpUserPermission fail to GetMicrList. uid:%v, cid:%+v err:%v", opUid, cid, err)
		return err
	}

	hasPermission := false
	for _, info := range resp.GetAllMicList() {
		if info.GetMicId() == 1 && info.GetMicUid() == opUid {
			hasPermission = true
		}
	}

	if !hasPermission {
		log.ErrorWithCtx(ctx, "checkOpUserPermission fail. uid:%v, cid:%+v err: user not permission ", opUid, cid)
		return protocol.NewExactServerError(nil, status.ErrAccountPermissionDenied)
	}

	return nil
}

func (s *MaskedPKLogic_) fillPKStatusPb(info *masked_pk_svr2.ChannelMaskedPKStatus) *pb.ChannelMaskedPKStatus {
	return &pb.ChannelMaskedPKStatus{
		ChannelId:     info.GetChannelId(),
		CurrChip:      info.GetCurrChip(),
		WinCnt:        info.GetWinCnt(),
		Status:        info.GetStatus(),
		RevivedCnt:    info.GetRevivedCnt(),
		RestReviveCnt: info.GetRestReviveCnt(),
		LossChip:      info.GetLossChip(),
		StatusEndTs:   info.GetStatusEndTs(),
		StatusDesc:    info.GetStatusDesc(),
		RestCancelCnt: info.GetRestCancelCnt(),
		ServerNs:      info.GetServerNs(),
		PkConf: &pb.ChannelMaskedPKConf{
			BeginTs:          info.GetPkConf().GetBeginTs(),
			EndTs:            info.GetPkConf().GetEndTs(),
			ChipRole:         info.GetPkConf().GetChipRole(),
			Chip:             info.GetPkConf().GetChip(),
			ContinueMatch:    info.GetPkConf().GetContinueMatch(),
			AutoMatchingCnt:  info.GetPkConf().GetAutoMatchingCnt(),
			JumpUrl:          info.GetPkConf().GetJumpUrl(),
			ConfId:           info.GetPkConf().GetConfId(),
			ServerNs:         info.GetPkConf().GetServerNs(),
			DivideType:       info.GetPkConf().GetDivideType(),
			ChipReceiveEndTs: info.GetPkConf().GetChipReceiveEndTs(),
		},
	}
}

func (s *MaskedPKLogic_) fillPKBattlePb(ctx context.Context, info *masked_pk_svr2.ChannelMaskedPKBattle) *pb.ChannelMaskedPKBattle {

	memList := make([]*pb.ChannelMaskedPKInfo, 0)
	for _, info := range info.GetMemList() {
		topAnchorList := make([]*pb.ChannelMaskedPKAnchor, 0)
		for _, anchor := range info.GetTopAnchorList() {
			profile := anchor.GetProfile()
			topAnchorList = append(topAnchorList, &pb.ChannelMaskedPKAnchor{
				Uid:      anchor.GetUid(),
				Score:    anchor.GetScore(),
				Account:  anchor.GetProfile().GetAccount(),
				NickName: anchor.GetProfile().GetNickname(),
				Sex:      anchor.GetProfile().GetSex(),
				UserProfile: &app.UserProfile{
					Uid:      anchor.GetUid(),
					Account:  profile.GetAccount(),
					Nickname: profile.GetNickname(),
					Sex:      profile.GetSex(),
					Privilege: &app.UserPrivilege{
						Account:  profile.GetPrivilege().GetAccount(),
						Nickname: profile.GetPrivilege().GetNickname(),
						Type:     profile.GetPrivilege().GetType(),
						Options:  profile.GetPrivilege().GetOptions(),
					},
				},
			})
		}

		memList = append(memList, &pb.ChannelMaskedPKInfo{
			ChannelId:      info.GetChannelId(),
			CurrChip:       info.GetCurrChip(),
			WinCnt:         info.GetWinCnt(),
			RevivedCnt:     info.GetReviveCnt(),
			Score:          info.GetScore(),
			LossDesc:       info.GetLossDesc(),
			LossChip:       info.GetLossChip(),
			PrefixLossDesc: info.GetPrefixLossDesc(),
			TopAnchorList:  topAnchorList,
			QuickKillInfo: &pb.QuickKillInfo{
				QuickKillDesc:       info.GetQuickKillInfo().GetQuickKillDesc(),
				QuickKillDescPrefix: info.GetQuickKillInfo().GetQuickKillDescPrefix(),
				QuickKillEndTs:      info.GetQuickKillInfo().GetQuickKillEndTs(),
				EnableMinPkSec:      info.GetQuickKillInfo().GetEnableMinPkSec(),
				EnableMaxPkSec:      info.GetQuickKillInfo().GetEnableMaxPkSec(),
				ConditionValue:      info.GetQuickKillInfo().GetConditionValue(),
			},
			PeakPkInfo: &pb.PeakPkInfo{PeakDesc: info.GetPeakPkInfo().GetPeakDesc()},
		})
	}

	out := &pb.ChannelMaskedPKBattle{
		PkId:        info.GetPkId(),
		MemList:     memList,
		PkEndTs:     info.GetPkEndTs(),
		ChipRole:    info.GetChipRole(),
		ServerNs:    time.Now().UnixNano(),
		ValidPkDesc: info.GetValidPkDesc(),
		ValidPk:     info.GetValidPk(),
		SubPhrase:   info.GetSubPhrase(),
	}

	return out
}

func (s *MaskedPKLogic_) checkClientVersion(serviceInfo *protogrpc.ServiceInfo) error {
	if serviceInfo.ClientType == protocol.ClientTypeANDROID && serviceInfo.ClientVersion < protocol.FormatClientVersion(5, 5, 19) ||
		serviceInfo.ClientType == protocol.ClientTypeIOS && serviceInfo.ClientVersion < protocol.FormatClientVersion(5, 5, 35) ||
		serviceInfo.ClientType == protocol.ClientTypePcTT && serviceInfo.ClientVersion < protocol.FormatClientVersion(1, 6, 2) {
		return protocol.NewExactServerError(nil, status.ErrAccountDeviceUnusual, "  ")
	}

	return nil
}

func (s *MaskedPKLogic_) MaskedPkGetConsumeTopN(ctx context.Context, req *pb.MaskedPkGetConsumeTopNReq) (resp *pb.MaskedPkGetConsumeTopNResp, err error) {
	if req.GetChannelType() == uint32(channelpb.ChannelType_GUILD_PUBLIC_FUN_CHANNEL_TYPE) {
		resp, err = s.getMaskedPkSvrConsumeTopN(ctx, req)
		if err != nil {
			return resp, err
		}
	} else {
		resp, err = s.getMaskedPkLiveConsumeTopN(ctx, req)
		if err != nil {
			return resp, err
		}
	}
	log.Debugf("MaskedPkGetConsumeTopN req:%+v, resp:%+v ", req, resp)
	return resp, err
}

func (s *MaskedPKLogic_) ShutDown() {
	s.maskedPKCli.Close()
}

func (s *MaskedPKLogic_) getMaskedPkSvrConsumeTopN(ctx context.Context, req *pb.MaskedPkGetConsumeTopNReq) (*pb.MaskedPkGetConsumeTopNResp, error) {
	resp := &pb.MaskedPkGetConsumeTopNResp{}
	consumeInfo, err := s.maskedPKCli.GetMaskedPkGetConsumeTopN(ctx, req.ChannelId)
	if err != nil {
		return resp, err
	}

	userList := make([]uint32, 0, len(consumeInfo.GetMemberList()))
	for _, member := range consumeInfo.GetMemberList() {
		userList = append(userList, member.GetUid())
	}

	myUid := consumeInfo.GetMyInfo().GetUid()
	userList = append(userList, myUid)

	// 贵族信息
	nobilityInfoMap, err := s.nobilityClient.BatchGetNobilityInfo(ctx, req.ChannelId, userList)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetLivePKAudienceRank fail to BatchGetNobilityInfo. in:%+v err:%v", req, err)
	}

	tailLight, err := s.taillightClient.BatchGetUserTaillight(ctx, userList, req.GetChannelId())
	if err != nil {
		log.ErrorWithCtx(ctx, "BatchGetSuperPlayerInfo failed:%v", err)
	}

	usersMap, err := s.accountClient.GetUsersMap(ctx, userList)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetUsersMap failed:%v", err)
		return resp, err
	}

	for _, member := range consumeInfo.GetMemberList() {
		uid := member.GetUid()
		user := usersMap[uid]
		consume := &pb.MaskedPKConsumeInfo{
			Uid:           member.Uid,
			Account:       user.GetUsername(),
			Nickname:      user.GetNickname(),
			Sex:           uint32(user.GetSex()),
			Rank:          member.Rank,
			Score:         member.GetScore(),
			NobilityLevel: nobilityInfoMap[uid].GetLevel(),
		}

		for _, info := range tailLight.GetUserTaillightMap()[uid].GetTaillightList() {
			if info.BizId == (tailLightPB.TaillightBizId_TaillightBizIdMaskedPKPlatform) {
				consume.PlatformLight = &pb.UserTaillightInfo{
					BizId: uint32(info.BizId),
					Num:   info.GetNum(),
				}
			} else {
				consume.ChannelLight = &pb.UserTaillightInfo{
					BizId: uint32(info.BizId),
					Num:   info.GetNum(),
				}
			}
		}
		resp.MemberList = append(resp.MemberList, consume)
	}

	myInfo := usersMap[myUid]
	resp.MyInfo = &pb.MaskedPKConsumeInfo{
		Uid:           myInfo.GetUid(),
		Score:         consumeInfo.GetMyInfo().GetScore(),
		Account:       myInfo.GetUsername(),
		Nickname:      myInfo.GetNickname(),
		Sex:           uint32(myInfo.GetSex()),
		Rank:          consumeInfo.GetMyInfo().GetRank(),
		NobilityLevel: nobilityInfoMap[myUid].GetLevel(),
	}
	var dValue uint32
	myRank := consumeInfo.GetMyInfo().GetRank()
	if myRank == 0 {
		if consumeInfo.GetMemberList() != nil && len(consumeInfo.GetMemberList()) >= 1 {
			dValue = consumeInfo.MemberList[len(consumeInfo.MemberList)-1].GetScore()
		}
		if dValue <= 10 {
			dValue = 10
		}
	} else if myRank == 1 {
		if len(consumeInfo.GetMemberList()) >= 2 {
			dValue = consumeInfo.GetMemberList()[0].GetScore() - consumeInfo.GetMemberList()[1].GetScore()
		}
	} else if myRank > 1 && myRank <= ChannelRankCount {
		myIdx := consumeInfo.GetMyInfo().Rank - 1
		upIdx := myIdx - 1
		dValue = consumeInfo.GetMemberList()[upIdx].GetScore() - consumeInfo.GetMemberList()[myIdx].GetScore()
	} else if myRank > ChannelRankCount {
		dValue = consumeInfo.GetMyInfo().Rank - ChannelRankCount
	} else {
		log.DebugWithCtx(ctx, "not find rank myRank:%d", myRank)
	}
	resp.MyInfo.DValue = dValue

	for _, info := range tailLight.GetUserTaillightMap()[myUid].GetTaillightList() {
		if info.BizId == (tailLightPB.TaillightBizId_TaillightBizIdMaskedPKPlatform) {
			resp.MyInfo.PlatformLight = &pb.UserTaillightInfo{
				BizId: uint32(info.BizId),
				Num:   info.GetNum(),
			}
		} else {
			resp.MyInfo.ChannelLight = &pb.UserTaillightInfo{
				BizId: uint32(info.BizId),
				Num:   info.GetNum(),
			}
		}
	}
	resp.ViewCnt = uint32(len(resp.MemberList))
	return resp, err
}

func (s *MaskedPKLogic_) getMaskedPkLiveConsumeTopN(ctx context.Context, req *pb.MaskedPkGetConsumeTopNReq) (*pb.MaskedPkGetConsumeTopNResp, error) {
	resp := &pb.MaskedPkGetConsumeTopNResp{}
	consumeInfo, err := s.maskedPKLiveCli.GetMaskedPkGetConsumeTopN(ctx, req.ChannelId)
	if err != nil {
		return resp, err
	}

	userList := make([]uint32, 0, len(consumeInfo.GetMemberList()))
	for _, member := range consumeInfo.GetMemberList() {
		userList = append(userList, member.GetUid())
	}
	userList = append(userList, consumeInfo.GetMyInfo().GetUid())

	// 贵族信息
	nobilityInfoMap, err := s.nobilityClient.BatchGetNobilityInfo(ctx, req.ChannelId, userList)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetLivePKAudienceRank fail to BatchGetNobilityInfo. in:%+v err:%v", req, err)
	}

	tailLight, err := s.taillightClient.BatchGetUserTaillight(ctx, userList, req.GetChannelId())
	if err != nil {
		log.ErrorWithCtx(ctx, "BatchGetSuperPlayerInfo failed:%v", err)
	}

	usersMap, err := s.accountClient.GetUsersMap(ctx, userList)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetUsersMap failed:%v", err)
	}

	// 骑士铭牌
	uid2KnightLevel := map[uint32]uint32{}
	if req.GetChannelType() == uint32(channelpb.ChannelType_RADIO_LIVE_CHANNEL_TYPE) {
		if channelInfo, err := s.channelCli.GetChannelSimpleInfo(ctx, consumeInfo.GetMyInfo().GetUid(), req.GetChannelId()); err == nil {
			knighNameplateResp, err := s.knightprivilegeClient.GetKnightGroupMemberWithNameplate(ctx, channelInfo.GetBindId(), req.GetChannelId())
			if err != nil {
				log.ErrorWithCtx(ctx, "getMaskedPkLiveConsumeTopN fail to GetKnightGroupMemberWithNameplate.  cid:%+v err:%v", req.GetChannelId(), err)
			} else {
				for _, uid := range knighNameplateResp.GetUidList() {
					uid2KnightLevel[uid] = uint32(knightprivilegePB.KnightStatusType_KNIGHT_COMMON)
					if uid == knighNameplateResp.GetChiefUid() {
						uid2KnightLevel[uid] = uint32(knightprivilegePB.KnightStatusType_KNIGHT_CHIEF)
					}
				}
				resp.KnightNameplateInfo = &app.KnightNameplateInfo{
					KnightCommonNameplateResourceUrl: knighNameplateResp.GetKnightCommonNameplateResourceUrl(),
					KnightChiefNameplateResourceUrl:  knighNameplateResp.GetKnightChiefNameplateResourceUrl(),
				}
			}
		}
	}

	for _, member := range consumeInfo.GetMemberList() {
		uid := member.GetUid()
		user := usersMap[uid]
		consume := &pb.MaskedPKConsumeInfo{
			Uid:           member.Uid,
			Account:       user.GetUsername(),
			Nickname:      user.GetNickname(),
			Sex:           uint32(user.GetSex()),
			Rank:          member.Rank,
			Score:         member.GetScore(),
			NobilityLevel: nobilityInfoMap[uid].GetLevel(),
			KnightLevel:   uid2KnightLevel[uid],
		}

		for _, info := range tailLight.GetUserTaillightMap()[uid].GetTaillightList() {
			if info.BizId == (tailLightPB.TaillightBizId_TaillightBizIdMaskedPKPlatform) {
				consume.PlatformLight = &pb.UserTaillightInfo{
					BizId: uint32(info.BizId),
					Num:   info.GetNum(),
				}
			} else {
				consume.ChannelLight = &pb.UserTaillightInfo{
					BizId: uint32(info.BizId),
					Num:   info.GetNum(),
				}
			}
		}
		resp.MemberList = append(resp.MemberList, consume)
	}

	myInfo := consumeInfo.GetMyInfo()
	myUid := consumeInfo.GetMyInfo().GetUid()
	myUser := usersMap[myUid]
	resp.MyInfo = &pb.MaskedPKConsumeInfo{
		Uid:           myInfo.GetUid(),
		Score:         consumeInfo.GetMyInfo().GetScore(),
		Account:       myUser.GetUsername(),
		Sex:           uint32(myUser.GetSex()),
		Nickname:      myUser.GetNickname(),
		Rank:          consumeInfo.GetMyInfo().GetRank(),
		NobilityLevel: nobilityInfoMap[myUid].GetLevel(),
		KnightLevel:   uid2KnightLevel[myUid],
	}
	var dValue uint32
	myRank := consumeInfo.GetMyInfo().GetRank()
	if myRank == 0 {
		if consumeInfo.GetMemberList() != nil && len(consumeInfo.GetMemberList()) >= 1 {
			dValue = consumeInfo.MemberList[len(consumeInfo.MemberList)-1].GetScore()
		}
		if dValue <= 10 {
			dValue = 10
		}
	} else if myRank == 1 {
		if len(consumeInfo.GetMemberList()) >= 2 {
			dValue = consumeInfo.GetMemberList()[0].GetScore() - consumeInfo.GetMemberList()[1].GetScore()
		}
	} else if myRank > 1 && myRank <= ChannelRankCount {
		myIdx := consumeInfo.GetMyInfo().Rank - 1
		upIdx := myIdx - 1
		dValue = consumeInfo.GetMemberList()[upIdx].GetScore() - consumeInfo.GetMemberList()[myIdx].GetScore()
	} else if myRank > ChannelRankCount {
		dValue = consumeInfo.GetMyInfo().Rank - ChannelRankCount
	} else {
		log.DebugWithCtx(ctx, "not find rank myRank:%d", myRank)
	}
	resp.MyInfo.DValue = dValue

	for _, info := range tailLight.GetUserTaillightMap()[myUid].GetTaillightList() {
		if info.BizId == (tailLightPB.TaillightBizId_TaillightBizIdMaskedPKPlatform) {
			resp.MyInfo.PlatformLight = &pb.UserTaillightInfo{
				BizId: uint32(info.BizId),
				Num:   info.GetNum(),
			}
		} else {
			resp.MyInfo.ChannelLight = &pb.UserTaillightInfo{
				BizId: uint32(info.BizId),
				Num:   info.GetNum(),
			}
		}
	}
	resp.ViewCnt = uint32(len(resp.MemberList))
	return resp, err
}
