package mgr

import (
	"context"
	"errors"
	"fmt"
	"golang.52tt.com/protocol/common/status"
	"golang.52tt.com/services/masked-pk/masked-pk-live/internal/cache"
	conf2 "golang.52tt.com/services/masked-pk/masked-pk-live/internal/conf"
	store2 "golang.52tt.com/services/masked-pk/masked-pk-live/internal/store"
	"time"

	"github.com/go-redis/redis"
	"gitlab.ttyuyin.com/avengers/tyr/compatible/proto"
	"golang.52tt.com/clients/push-notification/v2"
	push "golang.52tt.com/clients/push-notification/v2"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	channelPB "golang.52tt.com/protocol/app/channel"
	maskedPkLogic "golang.52tt.com/protocol/app/masked-pk-logic"
	gaPush "golang.52tt.com/protocol/app/push"
	pb "golang.52tt.com/protocol/services/masked-pk-live"
	pushPB "golang.52tt.com/protocol/services/push-notification/v2"
)

type Mgr struct {
	sc      *conf2.StartConfig
	bc      *conf2.BusinessConfManager
	store   *store2.Store
	cache   *cache.Cache
	PushCli *push.Client
}

func NewMgr(sc *conf2.StartConfig, bc *conf2.BusinessConfManager, st *store2.Store, cache *cache.Cache) *Mgr {
	pushCli, _ := push.NewClient()

	mgr := &Mgr{
		sc:      sc,
		bc:      bc,
		store:   st,
		PushCli: pushCli,
		cache:   cache,
	}
	return mgr
}

func (mgr *Mgr) ReloadMaskedGameConfigCache(ctx context.Context, nowTs time.Time) (config *store2.LiveMaskedGameConfig, exist bool, err error) {
	// 缓存没有，查数据库
	config, exist, err = mgr.store.GetMaskedGameConfigByTime(ctx, nowTs)
	if err != nil {
		return config, exist, err
	}
	// 数据库也没有，插一个空缓存防止击穿
	if !exist {
		_ = mgr.cache.AddMaskedGameConfigToCache(nil, nowTs.Add(time.Minute))
		return config, exist, err
	}
	// 如果有，还要查全局配置
	whiteListCfg, err := mgr.store.GetWhiteListConfig(ctx)
	config.UseWhiteList = whiteListCfg.UseWhiteList
	config.UsePrior = whiteListCfg.UsePrior
	config.AllowMatchingWithSameGuild = whiteListCfg.AllowMatchingWithSameGuild


	_ = mgr.cache.AddMaskedGameConfigToCache(config, nowTs)
	// 有可能当前没有，但拿到了将来最近一场的配置，这里要判断一下，具体可以看AddMaskedGameConfigToCache
	if !config.BeginTime.After(nowTs) && !config.EndTime.Before(nowTs) {
		return config, true, nil
	}

	return &store2.LiveMaskedGameConfig{}, false, nil
}

// 获取当前时段的蒙面pk配置
func (mgr *Mgr) GetCurrMaskedGameConfig(ctx context.Context, nowTs time.Time) (config *store2.LiveMaskedGameConfig, exist bool, err error) {
	config, err = mgr.cache.GetMaskedGameConfigFromCache()
	if err != nil && err != redis.Nil {
		log.Errorf("GetCurrMaskedGameConfig fail to GetMaskedGameConfigFromCache ,  err : %v", err)
		return config, false, err
	}

	if err == redis.Nil {
		config, exist, err = mgr.ReloadMaskedGameConfigCache(ctx, nowTs)
		if err != nil {
			log.Errorf("GetCurrMaskedGameConfig fail to reloadMaskedGameConfigCache,  err : %v", err)
			return config, false, err
		}
	}

	// 有可能拿到的是将来的最近一场的配置 ，要判断一下
	if config != nil && !config.BeginTime.After(nowTs) && !config.EndTime.Before(nowTs) {
		return config, true, nil
	}
	return &store2.LiveMaskedGameConfig{}, exist, nil
}

func (mgr *Mgr) HandlePkBeforePeak(pkId, channelId, subPhrase, totalPrice, pkEndTs uint32, gameConf *store2.LiveMaskedGameConfig) error {
	gameId := gameConf.GameId
	now := time.Now()


	if subPhrase != uint32(pb.ChannelMaskedPKBattle_Common) {
		return nil
	}

	pkEndTime := time.Unix(int64(pkEndTs), 0)
	if now.Add(time.Duration(mgr.bc.GetBeforePeakSec()) * time.Second).Before(pkEndTime) {
		// 还没到最后n秒
		return nil
	}

	pkInfoList, err := mgr.cache.GetPKInfoByPkIdRedis(gameId, channelId, pkId)
	if err != nil {
		log.Errorf("HandlePkBeforePeak failed to GetPKInfoByPkIdRedis. gameId:%v, pkId:%v, channelId:%d, err:%v", gameId, pkId, channelId, err)
		return err
	}

	leaderInfo, secondInfo, err := getSortPkInfo(pkInfoList)
	if err != nil {
		log.Errorf("HandlePkBeforePeak failed to getSortPkInfo. gameId:%v, pkId:%v, channelId:%d, err:%v", gameId, pkId, channelId, err)
		return err
	}

	secondCid := secondInfo.ChannelId
	if leaderInfo.Score == secondInfo.Score {
		// 打平就没有落后方
		secondCid = 0
	}

	_, _ = mgr.cache.IncrBeforePeakConditionScore(gameId, pkId, channelId, totalPrice)

	_, err = mgr.cache.SetSecondChannelBeforePeak(gameId, pkId, secondCid)
	if err != nil {
		log.Errorf("HandlePkBeforePeak failed to SetSecondChannelBeforePeak. gameId:%v, pkId:%v, channelId:%d, err:%v", gameId, pkId, channelId, err)
		return err
	}

	return nil
}

func (mgr *Mgr) AddPKScore(gameId, pkId, channelId, score, extraScore uint32) (bool, error) {
	ok, err := mgr.store.UpdatePKScore(gameId, channelId, pkId, score, extraScore)
	if err != nil {
		log.Errorf("addPKScore failed to UpdatePKScore gameId:%d, channelId:%d, pkId:%d, err:%v",
			gameId, channelId, pkId, err)
		return false, err
	}

	if !ok {
		log.Errorf("addPKScore failed to UpdatePKScore gameId:%d, channelId:%d, pkId:%d",
			gameId, channelId, pkId)
		return false, nil
	}

	_, err = mgr.cache.UpdatePKScoreRedis(gameId, channelId, pkId, score+extraScore, time.Now().Unix()+1)
	if err != nil {
		log.Errorf("addPKScore failed to UpdatePKScoreRedis gameId:%d, channelId:%d, pkId:%d, err:%v",
			gameId, channelId, pkId, err)
	}

	return true, nil
}

func getSortPkInfo(list []*store2.PKInfo) (*store2.PKInfo, *store2.PKInfo, error) {
	if len(list) != 2 {
		return nil, nil, errors.New("len(PKInfoList) != 2")
	}

	if list[0].Score > list[1].Score {
		return list[0], list[1], nil
	}

	return list[1], list[0], nil
}

func (mgr *Mgr) IsUserHasEntertainmentQualificationConf(ctx context.Context, uid uint32, channelId uint32) (exist bool, err error) {
	exist, err = mgr.cache.IsUserHasQualificationByCache(store2.QualificationList{ChannelId: channelId, Uid: uid})
	if err != nil {
		if err == redis.Nil {
			exist, err = mgr.store.IsUserHasQualification(ctx, uid, channelId)
			if err != nil {
				return exist, err
			}
			if !exist {
				_ = mgr.cache.AddNoneQualificationForUser(channelId, uid)
				return exist, err
			}
			// 这个会一次性添加两个hash
			_ = mgr.cache.AddQualificationChannelHash(channelId, uid)
		}
		return exist, err
	}
	return exist, nil
}

func (mgr *Mgr) PushPKQuickKillChange(ctx context.Context, channelIdA, channelIdB uint32, opt *maskedPkLogic.QuickKillChangeOpt) error {
	data, _ := proto.Marshal(opt)
	err := mgr.PushMsgToChannel(ctx, channelIdA, uint32(channelPB.ChannelMsgType_LIVE_MASKED_PK_QUICK_KILL_CHANGE),
		"斩杀推送", data)
	if err != nil {
		log.Errorf("pushPKQuickKillChange fail to pushMsgToChannel err: %s, %+v", err.Error(), channelIdA)
	}

	err = mgr.PushMsgToChannel(ctx, channelIdB, uint32(channelPB.ChannelMsgType_LIVE_MASKED_PK_QUICK_KILL_CHANGE),
		"斩杀推送", data)
	if err != nil {
		log.Errorf("pushPKQuickKillChange fail to pushMsgToChannel err: %s, %+v", err.Error(), channelIdB)
	}

	log.Debugf("pushPKQuickKillChange %v %v opt:%+v", channelIdA, channelIdB, opt)
	return nil
}

func (mgr *Mgr) PushMsgToChannel(ctx context.Context, channelId, msgType uint32, content string, data []byte) error {
	msg := &channelPB.ChannelBroadcastMsg{
		Time:         uint64(time.Now().Unix()),
		ToChannelId:  channelId,
		Type:         msgType,
		Content:      []byte(content),
		PbOptContent: data,
	}

	channelMsgBin, err := msg.Marshal()
	if err != nil {
		log.Errorf("pushMsgToChannel marshal err: %s, %+v", err.Error(), msg)
		return err
	}

	err = mgr.PushChannelBroMsgToChannels(ctx, []uint32{channelId}, channelMsgBin)
	if err != nil {
		log.ErrorWithCtx(ctx, "pushMsgToChannel fail to SendChannelBroadcastMsg. channelId:%d err:%v", channelId, err)
		return err
	}

	log.DebugfWithCtx(ctx, "pushMsgToChannel msgType:%v, channelId:%d, content:%s", msgType, channelId, content)
	return nil
}

// 房间广播消息
func (mgr *Mgr) PushChannelBroMsgToChannels(ctx context.Context, channelIds []uint32, channelMsgBin []byte) error {
	pushMessage := &gaPush.PushMessage{
		Cmd:     uint32(gaPush.PushMessage_CHANNEL_MSG_BRO),
		Content: channelMsgBin,
	}
	pushMessageBytes, e := pushMessage.Marshal()
	if e != nil {
		log.Errorf("pushChannelBroMsgToChannels Marshal channelIds:%v, err: %v", channelIds, e)
		return e
	}

	notification := &pushPB.CompositiveNotification{
		Sequence: uint32(time.Now().Unix()),
		TerminalTypeList: []uint32{
			protocol.MobileAndroidTT,
			protocol.MobileIPhoneTT,
		},
		TerminalTypePolicy: PushNotification.DefaultPolicy,
		AppId:              uint32(protocol.TT),
		ProxyNotification: &pushPB.ProxyNotification{
			Type:       uint32(pushPB.ProxyNotification_PUSH),
			Payload:    pushMessageBytes,
			Policy:     pushPB.ProxyNotification_DEFAULT,
			ExpireTime: 60,
		},
	}

	multicastMap := map[uint64]string{}
	for _, channelId := range channelIds {
		if channelId == 0 {
			continue
		}
		multicastMap[uint64(channelId)] = fmt.Sprintf("%d@channel", channelId)
	}

	err := mgr.PushCli.PushMulticasts(ctx, multicastMap, []uint32{}, notification)
	if err != nil {
		log.Errorf("pushChannelBroMsgToChannels PushMulticasts channelIds:%v, err: %s", channelIds, err.Error())
		return err
	}

	return nil
}

func (mgr *Mgr) PushMsgToChannels(ctx context.Context, channelIds []uint32, msgType uint32, content string, data []byte) error {
	msg := &channelPB.ChannelBroadcastMsg{
		Time:         uint64(time.Now().Unix()),
		ToChannelId:  0,
		Type:         msgType,
		Content:      []byte(content),
		PbOptContent: data,
	}

	channelMsgBin, err := msg.Marshal()
	if err != nil {
		log.Errorf("pushMsgToChannels marshal err: %s, %+v", err.Error(), msg)
		return err
	}

	err = mgr.PushChannelBroMsgToChannels(ctx, channelIds, channelMsgBin)
	if err != nil {
		log.ErrorWithCtx(ctx, "pushMsgToChannels fail to SendChannelBroadcastMsg. channelIds:%v err:%v", channelIds, err)
		return err
	}

	log.DebugfWithCtx(ctx, "pushMsgToChannels msgType:%v, channelIds:%v, content:%s", msgType, channelIds, content)
	return nil
}

func (mgr *Mgr) CheckIfCanMatch(gameConf *store2.LiveMaskedGameConfig) error {
	if time.Now().Add(time.Duration(mgr.bc.GetPKSec()+mgr.bc.GetPeakPKSec()+10) * time.Second).After(gameConf.EndTime) {
		return protocol.NewExactServerError(nil, status.ErrMaskedPkGameEndCannotMatching)
	}

	return nil
}

func (mgr *Mgr) PushPKStatusChange(ctx context.Context, game *store2.PkGameChannelInfo, gameCfg *store2.LiveMaskedGameConfig, currentStatus uint32) error {
	lossChip, lossDesc := mgr.getLossInfo(gameCfg, game, 0, false)
	chipReceiveEndTs := uint32(0)
	if gameCfg.ProvideChip == uint32(pb.ChannelMaskedPKConf_HaveChip) {
		chipReceiveEndTs = uint32(gameCfg.BeginTime.Unix()) + mgr.bc.GetJoinGameTimeLimitSec()
	}

	opt := &maskedPkLogic.ChannelMaskedPKStatus{
		ChannelId:     game.ChannelId,
		CurrChip:      game.ChipCnt,
		WinCnt:        game.WinCnt,
		RestReviveCnt: game.RestReviveCnt,
		RestCancelCnt: game.RestCancelCnt,
		RevivedCnt:    game.RevivedCnt,
		Status:        currentStatus,
		LossChip:      lossChip,
		ServerNs:      time.Now().UnixNano(),
		PkConf: &maskedPkLogic.ChannelMaskedPKConf{
			BeginTs:          uint32(gameCfg.BeginTime.Unix()),
			EndTs:            uint32(gameCfg.EndTime.Unix()),
			ChipRole:         gameCfg.ProvideChip,
			JumpUrl:          gameCfg.ActivityUrl,
			Chip:             gameCfg.ChipAvg,
			AutoMatchingCnt:  gameCfg.Withdraw,
			ConfId:           gameCfg.GameId,
			ChipNotEnough:    mgr.cache.CheckGameChipNotEnoughFlag(gameCfg.GameId),
			DivideType:       gameCfg.DivideType,
			ServerNs:         time.Now().UnixNano(),
			ChipReceiveEndTs: chipReceiveEndTs,
		},
	}

	if gameCfg.DivideType == uint32(pb.DivideType_DIVIDE_TYPE_HALF) {
		opt.LossDesc = lossDesc
	}

	if currentStatus == uint32(maskedPkLogic.ChannelMaskedPKStatus_PreMatching) {
		opt.StatusEndTs = uint32(time.Now().Unix() + int64(mgr.bc.GetPreReadySec()))
		opt.StatusDesc = "开始匹配"

	} else if currentStatus == uint32(maskedPkLogic.ChannelMaskedPKStatus_NotMatching) {
		if mgr.CheckIfCanMatch(gameCfg) != nil {
			opt.StatusDesc = "比赛即将结束，无法继续匹配"
		}
	}

	data, _ := proto.Marshal(opt)

	log.Debugf("pushPKStatusChange opt:%+v", opt)
	return mgr.PushMsgToChannel(ctx, opt.ChannelId, uint32(channelPB.ChannelMsgType_LIVE_MASKED_PK_STATUS_CHANGE_V2),
		"状态变更", data)
}

func (mgr *Mgr) getLossInfo(gameConf *store2.LiveMaskedGameConfig, info *store2.PkGameChannelInfo, qkTriggerCid uint32, isQuickKill bool) (lossChip uint32, lossDesc string) {
	chipCnt := info.ChipCnt
	if gameConf.ProvideChip == uint32(pb.ChannelMaskedPKConf_NoChip) {
		// 无筹码局没有奖金概念
		return 0, ""
	}

	if mgr.bc.GetReviveCnt() > 0 {
		lossChip, lossDesc = getLossInfoWithReviveMod(gameConf, info)
	} else {
		lossChip, lossDesc = getLossInfoWithoutReviveMod(gameConf, info)
	}

	if isQuickKill {
		if info.ChannelId != qkTriggerCid {
			lossChip = chipCnt
		}
		lossDesc = ""
	}

	return lossChip, lossDesc
}

func getLossInfoWithReviveMod(gameConf *store2.LiveMaskedGameConfig, info *store2.PkGameChannelInfo) (lossChip uint32, lossDesc string) {
	lossCnt, revivedCnt, chipCnt := info.LossCnt, info.RevivedCnt, info.ChipCnt
	if gameConf.DivideType == uint32(pb.DivideType_DIVIDE_TYPE_PERCENT) {
		// 百分比剥夺
		lossChip = uint32(uint64(chipCnt) * uint64(gameConf.DivideCount) / 100)

		if revivedCnt > 0 {
			// 复活后再次失败会损失所有筹码
			lossChip = chipCnt
		}

	} else {
		if lossCnt > 0 && revivedCnt == 0 {
			// 失败过且未复活过，再次失败会损失所有筹码
			lossChip = chipCnt
			lossDesc = "再次失败将失去全部奖金，\n进入复活模式"

		} else if lossCnt == 0 {
			// 第一场失败会损失1/2筹码
			lossChip = chipCnt / 2
			lossDesc = "失败将失去一半奖金，\n失败2场进入复活模式"

		} else if revivedCnt > 0 {
			// 复活后再次失败会损失所有筹码
			lossChip = chipCnt
			lossDesc = "失败将失去全部奖金\n本轮被淘汰"
		}
	}

	return lossChip, lossDesc
}

func getLossInfoWithoutReviveMod(gameConf *store2.LiveMaskedGameConfig, info *store2.PkGameChannelInfo) (lossChip uint32, lossDesc string) {
	lossCnt, chipCnt := info.LossCnt, info.ChipCnt
	if gameConf.DivideType == uint32(pb.DivideType_DIVIDE_TYPE_PERCENT) {
		// 百分比剥夺
		lossChip = uint32(uint64(chipCnt) * uint64(gameConf.DivideCount) / 100)

	} else {
		if lossCnt > 0 {
			// 失败过且未复活过，再次失败会损失所有筹码
			lossChip = chipCnt
			lossDesc = "再次失败将失去全部奖金"

		} else {
			// 第一场失败会损失1/2筹码
			lossChip = chipCnt / 2
			lossDesc = "失败将失去一半奖金"
		}
	}

	return lossChip, lossDesc
}
