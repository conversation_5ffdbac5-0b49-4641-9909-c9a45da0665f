package mysql

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"time"

	_ "github.com/go-sql-driver/mysql" // MySQL驱动。
	"github.com/jmoiron/sqlx"
	"gitlab.ttyuyin.com/golang/gudetama/log"
	pb "golang.52tt.com/protocol/services/golddiamonn"
	yuyinGold "golang.52tt.com/services/yuyin-gold/mysql"
)

var CreateDayRoomGoldDiamond = `
CREATE TABLE day_room_gold_diamond (
    guild_id int UNSIGNED NOT NULL,
    room_id int UNSIGNED NOT NULL,
    income bigint NOT NULL,
    fee bigint NOT NULL,
    paid_uid_cnt int UNSIGNED NOT NULL,
    cnt int UNSIGNED NOT NULL,
    stat_time bigint NOT NULL,
    PRIMARY KEY (guild_id, room_id, stat_time)
)ENGINE=InnoDB DEFAULT CHARSET=utf8;`

var CreateLockTableGoldDiamond = `
CREATE TABLE lock_table_gold_diamond (
	guild_id int UNSIGNED NOT NULL,
	update_time timestamp ON UPDATE CURRENT_TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
	PRIMARY KEY (guild_id)
)ENGINE=InnoDB DEFAULT CHARSET=utf8;`

var CreateDayGuildGoldDiamond = `
CREATE TABLE day_guild_gold_diamond (
    guild_id int UNSIGNED NOT NULL,
    income bigint NOT NULL,
    fee bigint NOT NULL,
    paid_uid_cnt int UNSIGNED NOT NULL,
    cnt int UNSIGNED NOT NULL,
    stat_time bigint NOT NULL,
    PRIMARY KEY (guild_id, stat_time)
)ENGINE=InnoDB DEFAULT CHARSET=utf8;`

var CreateMonthRoomGoldDiamond = `
CREATE TABLE month_room_gold_diamond (
    guild_id int UNSIGNED NOT NULL,
    room_id int UNSIGNED NOT NULL,
    income bigint NOT NULL,
    fee bigint NOT NULL,
    paid_uid_cnt int UNSIGNED NOT NULL,
    cnt int UNSIGNED NOT NULL,
    stat_time bigint NOT NULL,
    PRIMARY KEY (guild_id, room_id, stat_time)
)ENGINE=InnoDB DEFAULT CHARSET=utf8;`

var CreateMonthGuildGoldDiamond = `
CREATE TABLE month_guild_gold_diamond (
    guild_id int UNSIGNED NOT NULL,
    income bigint NOT NULL,
    fee bigint NOT NULL,
    paid_uid_cnt int UNSIGNED NOT NULL,
    cnt int UNSIGNED NOT NULL,
    stat_time bigint NOT NULL,
    PRIMARY KEY (guild_id, stat_time)
)ENGINE=InnoDB DEFAULT CHARSET=utf8;`

var CreateMonthRoomStat = `
CREATE TABLE month_room_stat
(
    month_date  bigint                                 NOT NULL COMMENT '归属结算周期，当月1日',
    guild_id    int UNSIGNED                           NOT NULL,
    anchor_id   int UNSIGNED DEFAULT 0                 NOT NULL,
    room_id     int UNSIGNED DEFAULT 0                 NOT NULL,
    fee         int UNSIGNED DEFAULT 0                 NOT NULL,
    income      bigint       DEFAULT 0                 NOT NULL,
    update_time timestamp    DEFAULT CURRENT_TIMESTAMP NOT NULL ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (guild_id, month_date, room_id),
    KEY room_idx (room_id),
    KEY up (update_time)
) COMMENT '按结算周期增量统计房间流水收益';
`
var CreateDayRoomStat = `
CREATE TABLE day_room_stat
(
    day_date    bigint                                 NOT NULL COMMENT '日',
    guild_id    int UNSIGNED                           NOT NULL,
    anchor_id   int UNSIGNED DEFAULT 0                 NOT NULL,
    room_id     int UNSIGNED DEFAULT 0                 NOT NULL,
    fee         int UNSIGNED DEFAULT 0                 NOT NULL,
    income      bigint       DEFAULT 0                 NOT NULL,
    update_time timestamp    DEFAULT CURRENT_TIMESTAMP NOT NULL ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (guild_id, day_date, room_id),
    KEY room_idx (room_id),
    KEY up (update_time)
) COMMENT '当日增量统计房间流水收益';
`

type GoldDiamonStore struct {
	ID            uint32    `db:"id"`
	GuildId       uint32    `db:"guild_id"`
	PaidUid       uint32    `db:"paid_uid"`
	UniqSign      string    `db:"uniq_sign"`
	OrderId       string    `db:"order_id"`
	SourceType    uint32    `db:"source_type"`
	Income        int64     `db:"income"`
	IncomeBalance int64     `db:"income_balance"`
	Status        uint32    `db:"status"`
	DescIncome    string    `db:"desc_income"`
	Extand        string    `db:"extand"`
	BoughtTime    int64     `db:"bought_time"`
	UpdateTime    time.Time `db:"update_time"`
	GameId        uint32    `db:"game_id"`
	RoomId        uint32    `db:"room_id"`
	Fee           int64     `db:"fee"`
}

type GoldSettlementInfo struct {
	Income  int64  `db:"income"`
	Fee     int64  `db:"fee"`
	Extand  string `db:"extand"`
	OrderId string `db:"order_id"`
}

type DayRoomGoldDiamondStore struct {
	GuildId    uint32 `db:"guild_id"`
	RoomId     uint32 `db:"room_id"`
	Income     int64  `db:"income"`
	Fee        int64  `db:"fee"`
	PaidUidCnt int64  `db:"paid_uid_cnt"`
	Cnt        uint32 `db:"cnt"`
	StatTime   int64  `db:"stat_time"`
}

type DayGuildGoldDiamondStore struct {
	GuildId    uint32 `db:"guild_id"`
	Income     int64  `db:"income"`
	Fee        int64  `db:"fee"`
	PaidUidCnt int64  `db:"paid_uid_cnt"`
	Cnt        uint32 `db:"cnt"`
	StatTime   int64  `db:"stat_time"`
}

type GuildDayGoldDiamond struct {
	GuildId     uint32 `db:"guild_id"`
	TotalIncome int64  `db:"total_income"`
	TotalCnt    uint32 `db:"total_cnt"`
	TotalFee    int64  `db:"total_fee"`
	PaidUidCnt  int64  `db:"paid_uid_cnt"`
}

type SimpleSummaryInfo struct {
	PaidUidCnt int64
	DayIncome  int64
	DayFee     int64
	StatTime   int64
}

type SimpleRoomSummaryInfo struct {
	GuildId     uint32 `db:"guild_id"`
	RoomId      uint32 `db:"room_id"`
	TotalIncome int64  `db:"total_income"`
	TotalFee    int64  `db:"total_fee"`
	StatTime    int64  `db:"stat_time"`
	PaidUidCnt  int64  `db:"paid_uid_cnt"`
}

type UnSettleSummaryInfo struct {
	RoomId      uint32 `db:"room_id"`
	TotalIncome int64  `db:"total_income"`
	TotalFee    int64  `db:"total_fee"`
	PaidUid     uint32 `db:"paid_uid"`
}

type ExtandInfoT struct {
	ExtandInfo  ExtandDetailT `json:"ori_recharge_info"`
	Diamond     float64       `json:"diamond"`
	GuildId     uint32        `json:"guild_id"`
	VoucherReal int64         `json:"voucher_real"`
	TTGame      uint32        `json:"ttgame"`
}

type ExtandDetailT struct {
	Fee         int64  `json:"fee"`
	ChannelName string `json:"channel_name"`
	Uid         uint32 `json:"uid"`
	ReceiveUid  uint32 `json:"receive_uid"`
	GiftId      uint32 `json:"gift_id"`
	ChannelId   uint32 `json:"channel_id"`
	Order       string `json:"order"`
}

type GoldDiamondAwardStat struct {
	Date    int64  `db:"date"`
	GuildID uint32 `db:"guild_id"`
	RoomID  uint32 `db:"room_id"`
	Fee     int64  `db:"fee"`
	Income  int64  `db:"income"`
}

type MonthRoomStat struct {
	MonthDate uint32 `db:"month_date"`
	GuildId   uint32 `db:"guild_id"`
	RoomId    uint32 `db:"room_id"`
	Fee       uint64 `db:"fee"`
	Income    uint64 `db:"income"`
}

type DayRoomStat struct {
	DayDate uint32 `db:"day_date"`
	GuildId uint32 `db:"guild_id"`
	RoomId  uint32 `db:"room_id"`
	Fee     uint64 `db:"fee"`
	Income  uint64 `db:"income"`
}

func NewMysql(db *sqlx.DB) *Store {
	return &Store{
		db:                        db,
		dayRoomRecordTableName:    "day_room_gold_diamond",
		dayGuildRecordTableName:   "day_guild_gold_diamond",
		monthRoomRecordTableName:  "month_room_gold_diamond",
		monthGuildRecordTableName: "month_guild_gold_diamond",
	}
}

type Store struct {
	db                        *sqlx.DB
	dayRoomRecordTableName    string
	dayGuildRecordTableName   string
	monthRoomRecordTableName  string
	monthGuildRecordTableName string
}

func (s *Store) ExecCmd(cmd string) error {
	_, err := s.db.Exec(cmd)
	if err != nil {
		fmt.Println("exec err:", err.Error())
	}
	return err
}

func (s *Store) CreateDayGoldDiamond() error {
	_, _ = s.db.Exec(CreateDayRoomGoldDiamond)
	_, _ = s.db.Exec(CreateDayGuildGoldDiamond)
	_, _ = s.db.Exec(CreateMonthRoomGoldDiamond)
	_, _ = s.db.Exec(CreateMonthGuildGoldDiamond)
	_, _ = s.db.Exec(CreateLockTableGoldDiamond)
	_, _ = s.db.Exec(CreateMonthRoomStat)
	_, _ = s.db.Exec(CreateDayRoomStat)
	return nil
}

func (s *Store) AddGuildIdIdx(idx uint32) error {
	sql := fmt.Sprintf("Alter table gold_diamond_award_%02d add index guild_idx(guild_id)", idx)
	_, err := s.db.Exec(sql)
	if err != nil {
		log.Errorf("insertRoomDay  db err:%s", err.Error())
		return err
	}
	return nil
}

func (s *Store) getTableIndex(guildId uint32) uint32 {
	return guildId % 100
}

func (s *Store) GenDayGuildInfo(ctx context.Context, handleTime time.Time) (infos []*DayRoomGoldDiamondStore, bOpe bool) {
	beginTime := time.Date(handleTime.Year(), handleTime.Month(), handleTime.Day()-1, 0, 0, 0, 0, time.Local)
	endTime := time.Date(handleTime.Year(), handleTime.Month(), handleTime.Day(), 0, 0, 0, 0, time.Local)
	nBeginTime := beginTime.Unix()
	nEndTime := endTime.Unix()
	for i := 0; i <= 99; i++ {
		infos, err := s.GetDayGuildDetail(ctx, i, nBeginTime, nEndTime)
		if err != nil {
			return nil, false
		}
		_, err = s.InsertGuild(ctx, infos, nBeginTime, false)
		if err != nil {
			return nil, false
		}
	}
	return nil, true
}

func (s *Store) GenDayRoomInfo(ctx context.Context, handleTime time.Time) (infos []*DayRoomGoldDiamondStore, bOpe bool) {
	beginTime := time.Date(handleTime.Year(), handleTime.Month(), handleTime.Day()-1, 0, 0, 0, 0, time.Local)
	endTime := time.Date(handleTime.Year(), handleTime.Month(), handleTime.Day(), 0, 0, 0, 0, time.Local)
	nBeginTime := beginTime.Unix()
	nEndTime := endTime.Unix()
	for i := 0; i <= 99; i++ {
		infos, err := s.GetDayRoomDetail(ctx, i, nBeginTime, nEndTime)
		if err != nil {
			return nil, false
		}
		_, err = s.InsertRoom(ctx, infos, nBeginTime, false)
		if err != nil {
			return nil, false
		}
	}
	return nil, true
}

func (s *Store) GenMonthRoomInfo(ctx context.Context, handleTime time.Time) (infos []*DayRoomGoldDiamondStore, bOpe bool) {
	beginTime := time.Date(handleTime.Year(), handleTime.Month()-1, 1, 0, 0, 0, 0, time.Local)
	endTime := time.Date(handleTime.Year(), handleTime.Month(), 1, 0, 0, 0, 0, time.Local)
	nBeginTime := beginTime.Unix()
	nEndTime := endTime.Unix()
	for i := 0; i <= 99; i++ {
		infos, err := s.GetMonthRoomDetail(ctx, i, nBeginTime, nEndTime)
		if err != nil {
			return nil, false
		}
		_, err = s.InsertRoom(ctx, infos, nBeginTime, true)
		if err != nil {
			return nil, false
		}
	}
	return nil, true
}

func (s *Store) GenMonthGuildInfo(ctx context.Context, handleTime time.Time) (infos []*DayGuildGoldDiamondStore, bOpe bool) {
	beginTime := time.Date(handleTime.Year(), handleTime.Month()-1, 1, 0, 0, 0, 0, time.Local)
	endTime := time.Date(handleTime.Year(), handleTime.Month(), 1, 0, 0, 0, 0, time.Local)
	nBeginTime := beginTime.Unix()
	nEndTime := endTime.Unix()
	for i := 0; i <= 99; i++ {
		infos, err := s.GetMonthGuildDetail(ctx, i, nBeginTime, nEndTime)
		if err != nil {
			return nil, false
		}
		_, err = s.InsertGuild(ctx, infos, nBeginTime, true)
		if err != nil {
			return nil, false
		}
	}
	return nil, true
}

func (s *Store) InsertRoom(ctx context.Context, infos []*DayRoomGoldDiamondStore, statTime int64, isMonth bool) (bool, error) {
	if len(infos) == 0 {
		return false, nil
	}
	tableName := s.dayRoomRecordTableName
	if isMonth {
		tableName = s.monthRoomRecordTableName
	}
	infoLen := len(infos)
	var query = fmt.Sprintf(`INSERT INTO %s (guild_id, room_id, income, fee, paid_uid_cnt, cnt, stat_time) VALUES `, tableName)
	for i, info := range infos {
		query += fmt.Sprintf(`(%d, %d, %d, %d, %d, %d, %d)`, info.GuildId, info.RoomId, info.Income, info.Fee, info.PaidUidCnt, info.Cnt, statTime)
		if i != infoLen-1 {
			query += `,`
		}
	}

	fmt.Printf("query:%s\n", query)
	_, err := s.db.ExecContext(ctx, query)
	if err != nil {
		fmt.Printf("insertRoomDay db err:%s", err.Error())
		log.Errorf("insertRoomDay db err:%s", err.Error())
		return false, err
	}

	return true, nil
}

func (s *Store) DelDataBeforeInsertRoom(ctx context.Context, infos []*DayRoomGoldDiamondStore, statTime int64, isMonth bool) (bool, error) {
	if len(infos) == 0 {
		return false, nil
	}
	tableName := s.dayRoomRecordTableName
	if isMonth {
		tableName = s.monthRoomRecordTableName
	}
	infoLen := len(infos)
	var query = fmt.Sprintf(`DELETE FROM %s where `, tableName)
	for i, info := range infos {
		query += fmt.Sprintf(`(guild_id = %d and room_id = %d and stat_time = %d)`, info.GuildId, info.RoomId, statTime)
		if i != infoLen-1 {
			query += `or`
		}
	}

	fmt.Printf("query:%s\n", query)
	_, err := s.db.ExecContext(ctx, query)
	if err != nil {
		fmt.Printf("insertRoomDay db err:%s", err.Error())
		log.Errorf("insertRoomDay db err:%s", err.Error())
		return false, err
	}

	return true, nil
}

func (s *Store) InsertGuild(ctx context.Context, infos []*DayGuildGoldDiamondStore, statTime int64, isMonth bool) (bool, error) {
	if len(infos) == 0 {
		return false, nil
	}
	tableName := s.dayGuildRecordTableName
	if isMonth {
		tableName = s.monthGuildRecordTableName
	}
	infoLen := len(infos)
	var query = fmt.Sprintf(`INSERT INTO %s (guild_id, income, fee, paid_uid_cnt, cnt, stat_time) VALUES `, tableName)
	for i, info := range infos {
		query += fmt.Sprintf(`(%d, %d, %d, %d, %d, %d)`, info.GuildId, info.Income, info.Fee, info.PaidUidCnt, info.Cnt, statTime)
		if i != infoLen-1 {
			query += `,`
		}
	}

	fmt.Printf("query:%s, begintime:%d\n", query, statTime)
	// log.Debugf("query:%s, begintime:%d\n", query, statTime)
	_, err := s.db.ExecContext(ctx, query)
	if err != nil {
		log.Errorf("insertGuildDay db err:%s", err.Error())
		fmt.Printf("insertGuildDay db err:%s", err.Error())
		return false, err
	}
	return true, nil
}

// for general day table
func (s *Store) GetDayRoomDetail(ctx context.Context, tableIndex int, beginTime, endTime int64) ([]*DayRoomGoldDiamondStore, error) {
	query := fmt.Sprintf(`
	SELECT count(1) as cnt, SUM(income) as income, SUM(fee) as fee, count(distinct paid_uid) as paid_uid_cnt, room_id, guild_id FROM gold_diamond_award_%02d WHERE
	bought_time >= ? AND bought_time < ? AND source_type = ? group by room_id
	`, tableIndex)
	dayGoldDiamonds := make([]*DayRoomGoldDiamondStore, 0, 100)
	err := s.db.SelectContext(ctx, &dayGoldDiamonds, query, beginTime*1000, endTime*1000, pb.GoldIncomeType_ROOM_INCOME)
	if err != nil {
		if err.Error() == "sql: no rows in result set" {
			return dayGoldDiamonds, nil
		} else {
			log.Errorf("getDayRoomDetail db err:%s", err.Error())
			return nil, err
		}
	}
	return dayGoldDiamonds, nil
}

func (s *Store) GetMonthRoomDay(ctx context.Context, beginTime, endTime int64) ([]*DayRoomGoldDiamondStore, error) {
	query := fmt.Sprintf(`
		SELECT count(1) as cnt, SUM(income) as income, SUM(fee) as fee, room_id, guild_id FROM %s WHERE
		stat_time >= ? AND stat_time < ? group by room_id
		`, s.dayRoomRecordTableName)
	dayGoldDiamonds := make([]*DayRoomGoldDiamondStore, 0, 10000)
	err := s.db.SelectContext(ctx, &dayGoldDiamonds, query, beginTime, endTime)
	if err != nil {
		if err.Error() == "sql: no rows in result set" {
			return dayGoldDiamonds, nil
		} else {
			log.Errorf("GetMonthRoomDay db err:%s", err.Error())
			return nil, err
		}
	}
	return dayGoldDiamonds, nil
}

// 只取roomid,guildid
func (s *Store) GetMonthRoomIdsDetail(ctx context.Context, tableIndex int, beginTime, endTime int64) ([]*DayRoomGoldDiamondStore, error) {
	query := fmt.Sprintf(`
	SELECT room_id, guild_id FROM gold_diamond_award_%02d WHERE
	bought_time >= ? AND bought_time < ? AND source_type = ? group by room_id
	`, tableIndex)
	dayGoldDiamonds := make([]*DayRoomGoldDiamondStore, 0, 100)
	err := s.db.SelectContext(ctx, &dayGoldDiamonds, query, beginTime*1000, endTime*1000, pb.GoldIncomeType_ROOM_INCOME)
	if err != nil {
		if err.Error() == "sql: no rows in result set" {
			return dayGoldDiamonds, nil
		} else {
			log.Errorf("getMonthRoomDetail db err:%s", err.Error())
			return nil, err
		}
	}
	return dayGoldDiamonds, nil
}

func (s *Store) GetMonthRoomDetail(ctx context.Context, tableIndex int, beginTime, endTime int64) ([]*DayRoomGoldDiamondStore, error) {
	query := fmt.Sprintf(`
	SELECT count(1) as cnt, SUM(income) as income, SUM(fee) as fee, count(distinct paid_uid) as paid_uid_cnt, room_id, guild_id FROM gold_diamond_award_%02d WHERE
	bought_time >= ? AND bought_time < ? AND source_type = ? group by room_id
	`, tableIndex)
	dayGoldDiamonds := make([]*DayRoomGoldDiamondStore, 0, 100)
	err := s.db.SelectContext(ctx, &dayGoldDiamonds, query, beginTime*1000, endTime*1000, pb.GoldIncomeType_ROOM_INCOME)
	if err != nil {
		if err.Error() == "sql: no rows in result set" {
			return dayGoldDiamonds, nil
		} else {
			log.Errorf("getMonthRoomDetail db err:%s", err.Error())
			return nil, err
		}
	}
	return dayGoldDiamonds, nil
}

func (s *Store) GetMonthRoomPaidUidsDetail(ctx context.Context, tableIndex int, beginTime, endTime int64, roomId uint32) ([]uint32, error) {
	query := fmt.Sprintf(`
	SELECT distinct paid_uid FROM gold_diamond_award_%02d WHERE
	bought_time >= ? AND bought_time < ? AND source_type = ? AND room_id = ?
	`, tableIndex)
	uids := make([]uint32, 0, 100)
	err := s.db.SelectContext(ctx, &uids, query, beginTime*1000, endTime*1000, pb.GoldIncomeType_ROOM_INCOME, roomId)
	if err != nil {
		if err.Error() == "sql: no rows in result set" {
			return uids, nil
		} else {
			log.Errorf("GetMonthRoomPaidUidsDetail db err:%s", err.Error())
			return nil, err
		}
	}
	return uids, nil
}

func (s *Store) GetDayGuildDetail(ctx context.Context, tableIndex int, beginTime, endTime int64) ([]*DayGuildGoldDiamondStore, error) {
	query := fmt.Sprintf(`
	SELECT count(1) as cnt, SUM(income) as income, SUM(fee) as fee, count(distinct paid_uid) as paid_uid_cnt, guild_id FROM gold_diamond_award_%02d WHERE
	bought_time >= ? AND bought_time < ? AND source_type = ? group by guild_id
	`, tableIndex)
	dayGoldDiamonds := make([]*DayGuildGoldDiamondStore, 0, 100)
	err := s.db.SelectContext(ctx, &dayGoldDiamonds, query, beginTime*1000, endTime*1000, pb.GoldIncomeType_ROOM_INCOME)
	if err != nil {
		if err.Error() == "sql: no rows in result set" {
			return dayGoldDiamonds, nil
		} else {
			log.Errorf("GetDayGuildDetail db err:%s", err.Error())
			return nil, err
		}
	}
	return dayGoldDiamonds, nil
}

// dat or month
func (s *Store) GetGuildPaidUidsDetail(ctx context.Context, tableIndex int, beginTime, endTime int64, guildId uint32) ([]uint32, error) {
	query := fmt.Sprintf(`
	SELECT distinct paid_uid FROM gold_diamond_award_%02d WHERE
	bought_time >= ? AND bought_time < ? AND source_type = ? AND guild_id = ?
	`, tableIndex)
	uids := make([]uint32, 0, 100)
	err := s.db.SelectContext(ctx, &uids, query, beginTime*1000, endTime*1000, pb.GoldIncomeType_ROOM_INCOME, guildId)
	if err != nil {
		if err.Error() == "sql: no rows in result set" {
			return uids, nil
		} else {
			log.Errorf("GetDayGuildPaidUidsDetail db err:%s", err.Error())
			return nil, err
		}
	}
	return uids, nil
}

func (s *Store) GetMonthGuildDay(ctx context.Context, beginTime, endTime int64) ([]*DayGuildGoldDiamondStore, error) {
	query := fmt.Sprintf(`
		SELECT count(1) as cnt, SUM(income) as income, SUM(fee) as fee, count(distinct paid_uid) as paid_uid_cnt, guild_id FROM %s WHERE
		stat_time >= ? AND stat_time < ? group by guild_id
		`, s.dayGuildRecordTableName)
	dayGoldDiamonds := make([]*DayGuildGoldDiamondStore, 0, 10000)
	err := s.db.SelectContext(ctx, &dayGoldDiamonds, query, beginTime, endTime)
	if err != nil {
		if err.Error() == "sql: no rows in result set" {
			return dayGoldDiamonds, nil
		} else {
			log.Errorf("GetMonthGuildDay db err:%s", err.Error())
			return nil, err
		}
	}
	return dayGoldDiamonds, nil
}

func (s *Store) GetMonthGuildDetail(ctx context.Context, tableIndex int, beginTime, endTime int64) ([]*DayGuildGoldDiamondStore, error) {
	query := fmt.Sprintf(`
	SELECT count(1) as cnt, SUM(income) as income, SUM(fee) as fee, count(distinct paid_uid) as paid_uid_cnt, guild_id FROM gold_diamond_award_%02d WHERE
	bought_time >= ? AND bought_time < ? AND source_type = ? group by guild_id
	`, tableIndex)
	dayGoldDiamonds := make([]*DayGuildGoldDiamondStore, 0, 100)
	err := s.db.SelectContext(ctx, &dayGoldDiamonds, query, beginTime*1000, endTime*1000, pb.GoldIncomeType_ROOM_INCOME)
	if err != nil {
		if err.Error() == "sql: no rows in result set" {
			return dayGoldDiamonds, nil
		} else {
			log.Errorf("getMonthGuildDetail db err:%s", err.Error())
			return nil, err
		}
	}
	return dayGoldDiamonds, nil
}

// 详情各房
func (s *Store) GetGuildRoomSummaryFromDetail(ctx context.Context, guildId uint32, beginTime, endTime int64) (elems []*DayRoomGoldDiamondStore, err error) {
	query := fmt.Sprintf(`
	SELECT count(1) as cnt, SUM(income) as income, SUM(fee) as fee, count(distinct paid_uid) as paid_uid_cnt, room_id, guild_id FROM gold_diamond_award_%02d WHERE
	guild_id = ? AND bought_time >= ? AND bought_time < ? AND source_type = ? group by room_id order by fee desc
	`, s.getTableIndex(guildId))
	elems = make([]*DayRoomGoldDiamondStore, 0, 100)
	err = s.db.SelectContext(ctx, &elems, query, guildId, beginTime*1000, endTime*1000, pb.GoldIncomeType_ROOM_INCOME)
	if err != nil {
		if err.Error() == "sql: no rows in result set" {
			return elems, nil
		} else {
			log.Errorf("GetGuildRoomSummaryFromDetail db err:%s", err.Error())
			return nil, err
		}
	}
	return elems, nil
}

// 查不到才来这里查详情的summary 详情guildid
func (s *Store) GetGuildSumaryFromDetail(ctx context.Context, guildId uint32, beginTime, endTime int64) (elem *GuildDayGoldDiamond, err error) {
	query := fmt.Sprintf(`
	SELECT count(1) as total_cnt, SUM(income) as total_income, SUM(fee) as total_fee, count(distinct paid_uid) as paid_uid_cnt, guild_id FROM gold_diamond_award_%02d WHERE
	guild_id = ? AND bought_time >= ? AND bought_time < ? AND source_type = ?
	`, s.getTableIndex(guildId))
	elem = &GuildDayGoldDiamond{}
	err = s.db.GetContext(ctx, elem, query, guildId, beginTime*1000, endTime*1000, pb.GoldIncomeType_ROOM_INCOME)
	if err != nil {
		if err.Error() == "sql: no rows in result set" {
			return elem, nil
		} else if strings.Contains(err.Error(), "Scan error on column index") {
			elem.GuildId = guildId
			return elem, nil
		} else {
			log.Errorf("GetGuildSumaryFromDetail db err:%s", err.Error())
			return nil, err
		}
	}
	return
}

func (s *Store) GetDayRoomSummary(ctx context.Context, guildId, roomId uint32, beginTime, endTime int64) (*RoomSummaryyGoldDiamond, error) {
	query := fmt.Sprintf(`
		SELECT SUM(income) as total_income, SUM(fee) as total_fee, room_id, guild_id FROM %s WHERE
		guild_id = ? AND room_id = ? AND stat_time >= ? AND stat_time < ?
		`, s.dayRoomRecordTableName)
	dayGoldDiamonds := &RoomSummaryyGoldDiamond{
		GuildId:  guildId,
		RoomId:   roomId,
		StatTime: beginTime,
	}
	err := s.db.GetContext(ctx, dayGoldDiamonds, query, guildId, roomId, beginTime, endTime)
	log.Debugf("dayGoldDiamonds: %+v", dayGoldDiamonds)
	if err != nil {
		if err.Error() == "sql: no rows in result set" {
			return dayGoldDiamonds, nil
		} else if strings.Contains(err.Error(), "Scan error on column index") {
			return dayGoldDiamonds, nil
		} else {
			log.Errorf("GetDayRoomSummary db err:%s", err.Error())
			return nil, err
		}
	}
	log.Debugf("dayGoldDiamonds: %+v", dayGoldDiamonds)
	return dayGoldDiamonds, nil
}

// 不包括昨天的和今天的
func (s *Store) GetThisMonthGuildInfoNotCache(ctx context.Context, guildId uint32, nowTime time.Time) ([]*DayGuildGoldDiamondStore, error) {
	beginTime := time.Date(nowTime.Year(), nowTime.Month(), 1, 0, 0, 0, 0, time.Local).Unix()
	endTime := time.Date(nowTime.Year(), nowTime.Month(), nowTime.Day()-1, 0, 0, 0, 0, time.Local).Unix()
	if beginTime > endTime {
		return nil, nil
	}
	return s.GetGuildDaysInfo(ctx, guildId, beginTime, endTime)
}

func (s *Store) GetThisMonthGuildInfo(ctx context.Context, guildId uint32, nowTime time.Time) ([]*DayGuildGoldDiamondStore, error) {
	beginTime := time.Date(nowTime.Year(), nowTime.Month(), 1, 0, 0, 0, 0, time.Local).Unix()
	endTime := time.Now().Unix()
	return s.GetGuildDaysInfo(ctx, guildId, beginTime, endTime)
}

/*func (s *Store) GetOneMonthRoomInfo(ctx context.Context, guildId uint32, getTime time.Time) ([]*DayRoomGoldDiamondStore, error) {
	beginTime := time.Date(getTime.Year(), getTime.Month(), 1, 0, 0, 0, 0, time.Local).Unix()
	endTime := time.Date(getTime.Year(), getTime.Month()+1, 1, 0, 0, 0, 0, time.Local).Unix()
	return s.GetGuildRoomSummary(ctx, guildId, beginTime, endTime)
}*/

func (s *Store) GetDayRoomInfoPage(ctx context.Context, guildId, roomId uint32, beginTime, endTime int64, offset, limit uint32, rangeType pb.RangeType) ([]*DayRoomGoldDiamondStore, bool, error) {
	var query string
	if rangeType == pb.RangeType_DAY_RANGE_TYPE {
		query = fmt.Sprintf(`
		SELECT income, fee, room_id, guild_id, stat_time, paid_uid_cnt FROM %s WHERE
		guild_id = %d AND room_id = %d AND stat_time >= %d AND stat_time < %d order by stat_time desc limit %d,%d
		`, s.dayRoomRecordTableName, guildId, roomId, beginTime, endTime, offset, limit)
	} else {
		query = fmt.Sprintf(`
		SELECT income, fee, room_id, guild_id, stat_time, paid_uid_cnt FROM %s WHERE
		guild_id = %d AND room_id = %d AND stat_time >= %d AND stat_time < %d
		`, s.dayRoomRecordTableName, guildId, roomId, beginTime, endTime)
	}
	fmt.Printf("query:%s\n", query)
	elems := make([]*DayRoomGoldDiamondStore, 0, 100)
	err := s.db.SelectContext(ctx, &elems, query)
	if err != nil {
		if err.Error() == "sql: no rows in result set" {
			return elems, false, nil
		} else {
			log.Errorf("GetDayRoomInfoPage db err:%s", err.Error())
			return nil, false, err
		}
	}
	if rangeType == pb.RangeType_DAY_RANGE_TYPE {
		if len(elems) == int(limit) {
			return elems, true, nil
		} else {
			return elems, false, nil
		}
	}
	return elems, false, nil
}

type RoomSummaryyGoldDiamond struct {
	GuildId     uint32 `db:"guild_id"`
	RoomId      uint32 `db:"room_id"`
	TotalIncome int64  `db:"total_income"`
	TotalFee    int64  `db:"total_fee"`
	StatTime    int64  `db:"stat_time"`
}

func (s *Store) GetRoomIncomeSummary(ctx context.Context, guildId uint32, beginTime, endTime int64) ([]*RoomSummaryyGoldDiamond, error) {
	var query string
	if endTime == 0 {
		query = fmt.Sprintf(`SELECT SUM(income) as total_income, SUM(fee) as total_fee, room_id, guild_id, stat_time  FROM %s WHERE 
			guild_id = %d AND stat_time >= %d group by room_id order by total_fee desc`, s.dayRoomRecordTableName, guildId, beginTime)
	} else {
		query = fmt.Sprintf(`
		SELECT SUM(income) as total_income, SUM(fee) as total_fee, room_id, guild_id, stat_time FROM %s WHERE guild_id = %d AND stat_time >= %d AND stat_time < %d group by room_id order by total_fee desc
		`, s.dayRoomRecordTableName, guildId, beginTime, endTime)
	}
	elems := make([]*RoomSummaryyGoldDiamond, 0, 100)
	err := s.db.SelectContext(ctx, &elems, query)
	if err != nil {
		if err.Error() == "sql: no rows in result set" {
			return elems, nil
		} else {
			log.Errorf("GetRoomIncomeSummary db err:%s", err.Error())
			return nil, err
		}
	}
	return elems, nil
}

// GetRoomIncomeSummaryRealTime 获取实时房间收益
// todo 聚合详情表性能较差，暂时实现功能，后期优化
func (s *Store) GetRoomIncomeSummaryRealTime(ctx context.Context, guildId uint32, settleStatus pb.ReqSettleStatus, beginTime, endTime int64, offset, limit uint32) ([]*RoomSummaryyGoldDiamond, error) {
	var query string
	if endTime == 0 {
		query = fmt.Sprintf(`SELECT SUM(income) as total_income, SUM(fee) as total_fee, room_id, guild_id FROM gold_diamond_award_%02d 
			WHERE guild_id = %d AND source_type = %d AND bought_time >= %d`,
			s.getTableIndex(guildId), guildId, int(pb.GoldIncomeType_ROOM_INCOME), beginTime*1000)
	} else {
		query = fmt.Sprintf(`SELECT SUM(income) as total_income, SUM(fee) as total_fee, room_id, guild_id FROM gold_diamond_award_%02d 
			WHERE guild_id = %d AND source_type = %d AND bought_time >= %d AND bought_time < %d`,
			s.getTableIndex(guildId), guildId, int(pb.GoldIncomeType_ROOM_INCOME), beginTime*1000, endTime*1000)
	}
	switch settleStatus {
	case pb.ReqSettleStatus_ReqSettleStatusWait:
		query += fmt.Sprintf(" AND status = %d", 0)
	case pb.ReqSettleStatus_ReqSettleStatusFinished:
		query += fmt.Sprintf(" AND status = %d", 1)
	}
	query += fmt.Sprintf(` GROUP BY room_id ORDER BY total_fee DESC limit %d, %d`, offset, limit)
	fmt.Printf("sql:%s\n", query)
	elems := make([]*RoomSummaryyGoldDiamond, 0, 100)
	err := s.db.SelectContext(ctx, &elems, query)
	if err != nil {
		if err.Error() == "sql: no rows in result set" {
			return elems, nil
		} else {
			log.Errorf("GetRoomIncomeSummary db err:%s", err.Error())
			return nil, err
		}
	}
	return elems, nil
}

type GuildDetails struct {
	RoomId     uint32 `db:"room_id"`
	PaidUid    uint32 `db:"paid_uid"`
	Income     int64  `db:"income"`
	Fee        int64  `db:"fee"`
	BoughtTime int64  `db:"bought_time"`
}

func (s *Store) GetGuildDetailCnt(ctx context.Context, beginTime, endTime int64, guildId uint32, paidUid, channelId uint32) (uint32, error) {
	getSQL := fmt.Sprintf(`SELECT count(1) FROM gold_diamond_award_%02d WHERE guild_id=? AND bought_time >= ? AND bought_time < ? AND source_type = ?`, s.getTableIndex(guildId))
	if paidUid != 0 {
		getSQL += fmt.Sprintf(" AND paid_uid = %d", paidUid)
	}
	if channelId != 0 {
		getSQL += fmt.Sprintf(" AND room_id = %d", channelId)
	}
	var totalCnt uint32
	err := s.db.GetContext(ctx, &totalCnt, getSQL, guildId, beginTime*1000, endTime*1000, pb.GoldIncomeType_ROOM_INCOME)
	if err != nil {
		if err.Error() == "sql: no rows in result set" || strings.Contains(err.Error(), "Scan error on column index") {
			return 0, nil
		} else if strings.Contains(err.Error(), "Error 1146") {
			return 0, nil
		} else {
			log.Errorf("GetGuildDetailCnt db err:%s", err.Error())
			return 0, err
		}
	}
	return totalCnt, nil
}

func (s *Store) GetGuildDetail(ctx context.Context, guildId uint32, beginTime, endTime int64, offset, limit uint32, paidUid, channelId uint32) ([]*GuildDetails, bool, error) {
	items := make([]*GuildDetails, 0, 10)
	totalCnt, err := s.GetGuildDetailCnt(ctx, beginTime, endTime, guildId, paidUid, channelId)
	if err != nil {
		return items, false, nil
	}
	if offset >= totalCnt {
		log.Errorf("GetGuildDetail offset err, offset:%d, totalCnt:%d", offset)
		return items, false, nil
	}
	query := fmt.Sprintf("SELECT paid_uid, room_id, income, bought_time, fee "+
		"FROM gold_diamond_award_%02d WHERE guild_id=? AND bought_time >= ? AND bought_time < ? AND source_type = ?", s.getTableIndex(guildId))
	if paidUid != 0 {
		query += fmt.Sprintf(" AND paid_uid = %d", paidUid)
	}
	if channelId != 0 {
		query += fmt.Sprintf(" AND room_id = %d", channelId)
	}
	query += " ORDER BY id DESC limit ?,?"

	err = s.db.SelectContext(ctx, &items, query, guildId, beginTime*1000, endTime*1000, pb.GoldIncomeType_ROOM_INCOME, offset, limit)
	if err != nil {
		if err.Error() == "sql: no rows in result set" {
			return items, false, nil
		} else {
			log.Errorf("GetGuildDetail db err:%s", err.Error())
			return nil, false, err
		}
	}

	if len(items) == int(limit) {
		return items, true, nil
	}
	return items, false, nil
}

// 查本月的未结算会比较费时，涉及paid_uid group没法用缓存
func (s *Store) GetUnSettlement(ctx context.Context, guildId uint32, beginTime, endTime int64, offset, limit uint32, queryType pb.QueryType) ([]*UnSettleSummaryInfo, bool, error) {
	sql := fmt.Sprintf("SELECT paid_uid, room_id, sum(fee) as total_fee, sum(income) as total_income "+
		"FROM gold_diamond_award_%02d WHERE guild_id = ? AND bought_time >= ? AND bought_time < ? and status = 0 and source_type = ? ",
		s.getTableIndex(guildId))
	if queryType == pb.QueryType_ROOM_ID_TYPE {
		sql += " group by room_id"
	} else if queryType == pb.QueryType_PAID_UID_TYPE {
		sql += " group by paid_uid"
	}
	sql += " ORDER BY id DESC limit ?,?"
	unsettelDetail := make([]*UnSettleSummaryInfo, 0, 100)
	err := s.db.SelectContext(ctx, &unsettelDetail, sql, guildId, beginTime*1000, endTime*1000, pb.GoldIncomeType_ROOM_INCOME, offset, limit)
	if err != nil {
		if err.Error() == "sql: no rows in result set" {
			return unsettelDetail, false, nil
		} else {
			log.Errorf("GetUnSettlement db err:%s", err.Error())
			return nil, false, err
		}
	}
	if len(unsettelDetail) == int(limit) {
		return unsettelDetail, true, nil
	}
	return unsettelDetail, false, nil
}

type GoldDiamonOrder struct {
	GuildId    uint32 `db:"guild_id"`
	OrderId    uint32 `db:"order_id"`
	SourceType uint32 `db:"source_type"`
}

func (s *Store) AwardGameDiamond(ctx context.Context, in *pb.AwardDiamondReq) error {
	var fee int64 = 0
	var gameId uint32 = 0
	if in.GetSourceType() == pb.IncomeType_GAME_RECHARGE_REBATE {
		if in.GetExtand() != "" {
			extandInfo := &ExtandInfoT{}
			err := json.Unmarshal([]byte(in.GetExtand()), &extandInfo)
			if err != nil {
				log.Errorln("AwardGameDiamond Game Unmarshal:", err.Error())
				return err
			} else {
				fmt.Printf("rechangeInfo:%+v\n", extandInfo)
				fee = extandInfo.ExtandInfo.Fee
				voucherAll := extandInfo.VoucherReal
				fee += voucherAll
				gameId = extandInfo.TTGame
				log.Infof("AwardGameDiamond Game gameid:%d, fee:%d", gameId, fee)
			}
		}
	}
	s.AwardDiamond(ctx, in.GetGuildId(), in.GetPaidUid(), in.GetSourceType(), gameId, in.GetOrderId(), in.GetIncome(), int64(in.GetBoughtTime()), fee, in.GetExtand())
	return nil
}

func (s *Store) AwardDiamond(ctx context.Context, guildId, paidUid uint32, sourceType pb.IncomeType, indetifyId uint32, orderId string, income, boughtTime, fee int64, extand string) (err error) {
	var status uint32 = 0
	if sourceType == 4 {
		status = 1
	}
	var gameId uint32 = 0
	var roomId uint32 = 0
	desc := ""
	if sourceType == pb.IncomeType_GAME_RECHARGE_REBATE {
		gameId = indetifyId
	} else if sourceType == pb.IncomeType_CHANNEL_SEND_GIFT {
		// desc = "公会开黑房间送T豆礼物返利"
		desc = "test2"
		roomId = indetifyId
	}

	uniqSign := fmt.Sprintf("sign_%s", orderId)

	var totalIncome int64
	tx, err := s.db.Beginx()
	if err != nil {
		log.Errorln("AwardDiamond Beginx err:", err.Error(), " guildid:", guildId)
		return err
	}
	/*_, err = tx.ExecContext(ctx, "INSERT INTO gold_diamond_uniq_sign(order_id, guild_id, source_type) VALUES(?, ?, ?)", orderId, guildId, sourceType)
	if err != nil {
		log.Errorln("AwardGameDiamond gift insert gold_diamond_uniq_sign err:", err.Error())
		return err
	}*/
	_, err = tx.ExecContext(ctx, `INSERT INTO lock_table_gold_diamond (guild_id) VALUES (?) ON DUPLICATE KEY UPDATE update_time= VALUES(update_time)`, guildId)
	if err != nil {
		log.Errorln("AwardDiamond gift insert lock_table_gold_diamond err:", err.Error(), " guildid:", guildId)
		tx.Rollback()
		return err
	}
	incomeSql := fmt.Sprintf(`SELECT income_balance FROM gold_diamond_award_%02d WHERE guild_id=? order by id desc limit 1`, s.getTableIndex(guildId))
	err = tx.GetContext(ctx, &totalIncome, incomeSql, guildId)
	if err != nil {
		log.Errorf("AwardDiamond get incomebalance err:%s, guildid:%d", err.Error(), guildId)
		if err.Error() == "sql: no rows in result set" {
			totalIncome = 0
		} else {
			tx.Rollback()
			return err
		}
	}
	totalIncome += income
	insertSQL := fmt.Sprintf(`INSERT INTO gold_diamond_award_%02d (guild_id, paid_uid, uniq_sign, 
	order_id, source_type, income, desc_income, extand, bought_time, status, income_balance, fee, game_id, room_id) 
	VALUES(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`, s.getTableIndex(guildId))
	// log.Errorf("insertSQL: %s", insertSQL)
	_, err = tx.ExecContext(ctx, insertSQL, guildId, paidUid, uniqSign, orderId, sourceType, income, desc, extand, boughtTime, status, totalIncome, fee, gameId, roomId)
	if err != nil {
		log.Errorln("AwardDiamond gift insertSQL err:", err.Error(), " guildid:", guildId)
		tx.Rollback()
		return err
	}

	now := time.Now()

	// todo del after 4.25
	publishTime := time.Date(2022, 4, 25, 0, 0, 0, 0, now.Location())

	if now.Unix() > publishTime.Unix() {
		monthDate := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, now.Location())
		roomStatSql := fmt.Sprintf("INSERT INTO month_room_stat (month_date, guild_id, anchor_id, room_id, fee, income)" +
			"VALUES (?, ?, ?, ?, ?, ?) ON DUPLICATE KEY UPDATE fee = fee + ?, income = income + ?")
		_, err = tx.ExecContext(ctx, roomStatSql, monthDate.Unix(), guildId, 0, roomId, fee, income, fee, income)
		if err != nil {
			log.Errorln("AwardDiamond roomStatSql err:", err.Error(), " guildid:", guildId)
			tx.Rollback()
			return err
		}
		dayDate := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())
		dayRoomStatSql := fmt.Sprintf("INSERT INTO day_room_stat (day_date, guild_id, anchor_id, room_id, fee, income)" +
			"VALUES (?, ?, ?, ?, ?, ?) ON DUPLICATE KEY UPDATE fee = fee + ?, income = income + ?")
		_, err = tx.ExecContext(ctx, dayRoomStatSql, dayDate.Unix(), guildId, 0, roomId, fee, income, fee, income)
		if err != nil {
			log.Errorln("AwardDiamond dayRoomStatSql err:", err.Error(), " guildid:", guildId)
			tx.Rollback()
			return err
		}
	}

	err = tx.Commit()
	if err != nil {
		log.Errorln("AwardDiamond gift commit err:", err.Error(), " guildid:", guildId)
		return err
	}
	return nil
}

func (s *Store) GetIncomeBalance(ctx context.Context, guildId uint32) (int64, error) {
	sql := fmt.Sprintf("SELECT income_balance FROM gold_diamond_award_%02d WHERE guild_id=? order by id desc limit 1", s.getTableIndex(guildId))
	var incomeBalance int64
	err := s.db.GetContext(ctx, &incomeBalance, sql, guildId)
	if err != nil {
		if err.Error() == "sql: no rows in result set" {
			return incomeBalance, nil
		} else {
			log.Errorf("GetIncomeBalance db err:%s", err.Error())
			return 0, err
		}
	}

	return incomeBalance, nil
}

func (s *Store) GetRooms(ctx context.Context, guildId uint32, beginTime, endTime, yesterdayTime int64) ([]uint32, error) {
	// 今天之前
	query := fmt.Sprintf(`
		SELECT room_id FROM %s WHERE
		guild_id = ? AND stat_time >= ? AND stat_time < ? group by room_id
		`, s.dayRoomRecordTableName)
	roomids := make([]uint32, 0, 20)
	err := s.db.SelectContext(ctx, &roomids, query, guildId, beginTime, endTime)
	if err != nil {
		log.Errorf("GetRooms db err:%s", err.Error())
		return nil, err
	}

	// 今天,昨天
	todayRoomIds := make([]uint32, 0, 20)
	query = fmt.Sprintf("SELECT room_id FROM gold_diamond_award_%02d WHERE guild_id=? AND bought_time >= ? AND bought_time < ? group by room_id", s.getTableIndex(guildId))
	err = s.db.SelectContext(ctx, &todayRoomIds, query, guildId, yesterdayTime*1000, endTime*1000)
	if err != nil {
		log.Errorf("GetRooms db err:%s", err.Error())
		return nil, err
	}

	for _, todayRoomId := range todayRoomIds {
		bGet := false
		for _, roomid := range roomids {
			if roomid == todayRoomId {
				bGet = true
			}
		}
		if !bGet {
			roomids = append(roomids, todayRoomId)
		}
	}
	return roomids, nil
}

/*func (s *Store) GetGuildRoomSummary(ctx context.Context, guildId, roomId uint32, beginTime, endTime int64) (*DayRoomGoldDiamondStore, error) {
	query := fmt.Sprintf("SELECT * FROM %s WHERE stat_time > ? AND stat_time < ? AND guild_id = ? AND room_id = ?", s.dayRoomRecordTableName)
	dayGoldDiamonds := &DayRoomGoldDiamondStore{}
	err := s.db.SelectContext(ctx, &dayGoldDiamonds, query, beginTime, endTime, guildId, roomId)
	if err != nil {
		log.Errorf("GetGuildRoomSummary db err:%s", err.Error())
		return nil, err
	}
	return dayGoldDiamonds, nil
}*/

// 已生成的每天每个房间数据
func (s *Store) GetDayGuildSummary(ctx context.Context, guildId uint32, beginTime, endTime int64) (*DayGuildGoldDiamondStore, error) {
	log.Debugf("GetDayGuildSummarybeginTime: %s end time %s", time.Unix(beginTime, 0).String(), time.Unix(endTime, 0).String())

	query := fmt.Sprintf("SELECT * FROM %s WHERE stat_time >= ? AND stat_time < ? AND guild_id = ?", s.dayGuildRecordTableName)
	guildInfo := &DayGuildGoldDiamondStore{}
	err := s.db.GetContext(ctx, guildInfo, query, beginTime, endTime, guildId)
	if err != nil {
		if err.Error() == "sql: no rows in result set" {
			return guildInfo, nil
		} else {
			log.Errorf("GetDayGuildSummary db err:%s", err.Error())
			return nil, err
		}
	} else {
		log.Debugf("GetDayGuildSummary guildInfo :%+v", guildInfo)
	}
	return guildInfo, nil
}

// 已生成的每天每个房间数据
func (s *Store) GetDayGuildSummaryV2(ctx context.Context, guildId uint32, beginTime, endTime int64) (*DayGuildGoldDiamondStore, error) {
	log.Debugf("GetDayGuildSummaryV2: %s end time %s", time.Unix(beginTime, 0).String(), time.Unix(endTime, 0).String())

	query := fmt.Sprintf("SELECT sum(income) as income FROM %s WHERE stat_time >= ? AND stat_time < ? AND guild_id = ?", s.dayGuildRecordTableName)
	guildInfo := &DayGuildGoldDiamondStore{}
	err := s.db.GetContext(ctx, guildInfo, query, beginTime, endTime, guildId)
	if err != nil {
		if err.Error() == "sql: no rows in result set" {
			return guildInfo, nil
		} else {
			log.Errorf("GetDayGuildSummaryV2 db err:%s", err.Error())
			return nil, err
		}
	} else {
		log.Debugf("GetDayGuildSummaryV2 guildInfo :%+v", guildInfo)
	}
	return guildInfo, nil
}

func (s *Store) GetMonthGuildSummary(ctx context.Context, guildId uint32, beginTime, endTime int64) (*DayGuildGoldDiamondStore, error) {
	query := fmt.Sprintf("SELECT * FROM %s WHERE stat_time >= ? AND stat_time < ? AND guild_id = ?", s.monthGuildRecordTableName)
	guildInfo := &DayGuildGoldDiamondStore{}
	err := s.db.GetContext(ctx, guildInfo, query, beginTime, endTime, guildId)
	if err != nil {
		if err.Error() == "sql: no rows in result set" {
			return guildInfo, nil
		} else {
			log.Errorf("GetMonthGuildSummary db err:%s", err.Error())
			return nil, err
		}
	}
	return guildInfo, nil
}

func (s *Store) GetPaidUidsFromDetail(ctx context.Context, guildId uint32, beginTime, endTime int64) (elems []uint32, err error) {
	query := fmt.Sprintf(`
	SELECT paid_uid FROM gold_diamond_award_%02d WHERE
	guild_id = ? AND bought_time >= ? AND bought_time < ? AND source_type = ? group by paid_uid
	`, s.getTableIndex(guildId))
	elems = make([]uint32, 0, 30)
	err = s.db.SelectContext(ctx, &elems, query, guildId, beginTime*1000, endTime*1000, pb.GoldIncomeType_ROOM_INCOME)
	if err != nil {
		if err.Error() == "sql: no rows in result set" {
			return elems, nil
		} else {
			log.Errorf("GetPaidUidsFromDetail db err:%s", err.Error())
			return nil, err
		}
	}
	return elems, nil
}

func (s *Store) GetPaidUidsFromDetailEx(ctx context.Context, guildId uint32, beginTime, endTime int64) (elems []*GoldDiamonStore, err error) {
	query := fmt.Sprintf(`
	SELECT paid_uid, sum(fee) as fee FROM gold_diamond_award_%02d WHERE
	guild_id = ? AND bought_time >= ? AND bought_time < ? AND source_type = ? group by paid_uid
	`, s.getTableIndex(guildId))
	elems = make([]*GoldDiamonStore, 0, 30)
	err = s.db.SelectContext(ctx, &elems, query, guildId, beginTime*1000, endTime*1000, pb.GoldIncomeType_ROOM_INCOME)
	if err != nil {
		if err.Error() == "sql: no rows in result set" {
			return elems, nil
		} else {
			log.Errorf("GetPaidUidsFromDetailEx db err:%s", err.Error())
			return nil, err
		}
	}
	return elems, nil
}

/*type PaidUidInfoT struct {
	PaidUidCnt int64 `db:"paid_uid_cnt"`
	StatTime   int64 `db:"stat_time"`
}

func (s *Store) GetGuildDaysPaidUids(ctx context.Context, guildId uint32, beginTime, endTime int64) (elems []*PaidUidInfoT, err error) {
	query := fmt.Sprintf(`
	SELECT paid_uid_cnt, stat_time FROM %s WHERE
	guild_id = ? AND stat_time > ? AND stat_time < ?  group by stat_time
	`, s.dayGuildRecordTableName)
	elems = make([]*PaidUidInfoT, 0, 31)
	err = s.db.SelectContext(ctx, &elems, query, guildId, beginTime, endTime)
	if err != nil {
		log.Errorf("GetPaidUidsFromDetail db err:%s", err.Error())
		return nil, err
	}
	return elems, nil
}*/

func (s *Store) GetGuildDaysInfo(ctx context.Context, guildId uint32, beginTime, endTime int64) (elems []*DayGuildGoldDiamondStore, err error) {
	query := fmt.Sprintf(`
	SELECT * FROM %s WHERE
	guild_id = ? AND stat_time >= ? AND stat_time < ?  group by stat_time order by stat_time desc
	`, s.dayGuildRecordTableName)
	elems = make([]*DayGuildGoldDiamondStore, 0, 31)
	err = s.db.SelectContext(ctx, &elems, query, guildId, beginTime, endTime)
	if err != nil {
		if err.Error() == "sql: no rows in result set" {
			return elems, nil
		} else {
			log.Errorf("GetGuildDaysInfo db err:%s", err.Error())
			return nil, err
		}
	}
	return elems, nil
}

func (s *Store) GetGuildRoomMonthInfo(ctx context.Context, guildId, roomId uint32, beginTime, endTime int64) (infos []*DayRoomGoldDiamondStore, err error) {
	query := fmt.Sprintf(`
	SELECT * FROM %s WHERE
	guild_id = ? AND room_id = ? AND stat_time >= ? AND stat_time < ? group by stat_time  order by stat_time desc
	`, s.monthRoomRecordTableName)
	infos = make([]*DayRoomGoldDiamondStore, 0, 6)
	err = s.db.SelectContext(ctx, &infos, query, guildId, roomId, beginTime, endTime)
	if err != nil {
		if err.Error() == "sql: no rows in result set" {
			return infos, nil
		} else {
			log.Errorf("GetGuildRoomMonthInfo db err:%s", err.Error())
			return nil, err
		}
	}
	return infos, nil
}

func (s *Store) MarkIncomeSettlement(guildId, sourceType uint32, beginTime, endTime int64) bool {
	sql := fmt.Sprintf("UPDATE gold_diamond_award_%02d SET status = 1 WHERE guild_id = %d AND bought_time >= %d "+
		" AND bought_time < %d AND source_type = %d", s.getTableIndex(guildId), guildId, beginTime*1000, endTime*1000, sourceType)
	_, err := s.db.Exec(sql)
	if err != nil {
		log.Errorf("MarkIncomeSettlement db err:%s", err.Error())
		return false
	}
	return true
}

func (s *Store) GetMysqlNowTime() (int64, error) {
	var nowTime int64
	query := "SELECT UNIX_TIMESTAMP()"
	err := s.db.Get(&nowTime, query)
	if err != nil {
		log.Errorf("GetGuildSumaryFromDetail db err:%s", err.Error())
		return 0, err
	}
	return nowTime, nil
}

func (s *Store) GetGuildIdsEx(ctx context.Context, tableIndex uint32, beginTime, endTime int64) ([]uint32, error) {
	var guildIds = make([]uint32, 0, 100)
	query := fmt.Sprintf("select distinct(guild_id) from gold_diamond_award_%02d where bought_time >= %d AND bought_time < %d and (source_type = 6 or source_type = 7)", tableIndex, beginTime*1000, endTime*1000)
	err := s.db.SelectContext(ctx, &guildIds, query)
	if err != nil {
		log.Errorf("GetGuildSumaryFromDetail db err:%s", err.Error())
		return nil, err
	}
	return guildIds, nil
}

func (s *Store) GetGuildIds(ctx context.Context, tableIndex uint32, beginTime, endTime int64) ([]uint32, error) {
	var guildIds = make([]uint32, 0, 100)
	query := fmt.Sprintf("select distinct(guild_id) from gold_diamond_award_%02d where status = 0 AND bought_time >= %d AND bought_time < %d", tableIndex, beginTime*1000, endTime*1000)
	err := s.db.SelectContext(ctx, &guildIds, query)
	if err != nil {
		log.Errorf("GetGuildSumaryFromDetail db err:%s", err.Error())
		return nil, err
	}
	return guildIds, nil
}

func (s *Store) LockTable(guildId, sourceType uint32, beginTime, endTime int64) bool {
	sql := fmt.Sprintf("LOCK TABLES gold_diamond_award_%02d WRITE", s.getTableIndex(guildId))
	_, err := s.db.Exec(sql)
	if err != nil {
		log.Errorf("LockTable db err:%s", err.Error())
		return false
	}
	return true
}

func (s *Store) GetIncomeSettlementDetailByTime(ctx context.Context, guildId uint32, beginTime, endTime int64) ([]*GoldSettlementInfo, error) {
	sql := fmt.Sprintf(
		"SELECT income, fee, extand FROM gold_diamond_award_%02d WHERE guild_id=%d AND bought_time >= %d AND bought_time < %d and status = 0",
		s.getTableIndex(guildId), guildId, beginTime*1000, endTime*1000)
	sql += fmt.Sprintf(" and source_type = %d", pb.IncomeType_CHANNEL_SEND_GIFT)
	infos := make([]*GoldSettlementInfo, 0, 100)
	err := s.db.SelectContext(ctx, &infos, sql)
	if err != nil {
		log.Errorf("GetIncomeDetailByTime db err:%s", err.Error())
		return nil, err
	}
	return infos, nil
}

func (s *Store) GetIncomeSettlementDetailByTime2(ctx context.Context, guildId, roomId uint32, beginTime, endTime int64) ([]*GoldSettlementInfo, error) {
	sql := fmt.Sprintf(
		"SELECT income, fee, extand, order_id FROM gold_diamond_award_%02d WHERE guild_id=%d AND bought_time >= %d AND bought_time < %d AND room_id = %d and status = 0",
		s.getTableIndex(guildId), guildId, beginTime*1000, endTime*1000, roomId)
	sql += fmt.Sprintf(" and source_type = %d", pb.IncomeType_CHANNEL_SEND_GIFT)
	infos := make([]*GoldSettlementInfo, 0, 100)
	err := s.db.SelectContext(ctx, &infos, sql)
	if err != nil {
		log.Errorf("GetIncomeDetailByTime db err:%s", err.Error())
		return nil, err
	}
	return infos, nil
}

func (s *Store) GetIncomesByTime(ctx context.Context, guildId uint32, beginTime, endTime int64, sourceType pb.IncomeType) (int64, error) {
	sql := fmt.Sprintf(
		"SELECT sum(income) FROM gold_diamond_award_%02d WHERE guild_id=%d AND bought_time >= %d AND bought_time < %d  and status = 0",
		s.getTableIndex(guildId), guildId, beginTime*1000, endTime*1000)
	sql += fmt.Sprintf(" and source_type = %d", sourceType)
	var totalIncome int64
	err := s.db.GetContext(ctx, &totalIncome, sql)
	if err != nil {
		if err.Error() == "sql: no rows in result set" {
			return 0, nil
		} else if strings.Contains(err.Error(), "Scan error on column index") {
			return 0, nil
		} else {
			log.Errorf("GetIncomesByTime db err:%s", err.Error())
			return 0, err
		}
	}
	return totalIncome, nil
}

func (s *Store) GetIncomesSixMonthBefore(ctx context.Context, guildId uint32, sixMonthBefore, todayZero, nowTime int64, sourceType pb.IncomeType) (int64, error) {
	// 归档表
	sql := fmt.Sprintf(
		"SELECT sum(income) FROM day_guild_gold_diamond WHERE guild_id=%d AND stat_time >= %d AND stat_time < %d",
		guildId, sixMonthBefore, todayZero)
	var passDayIncome int64
	err := s.db.GetContext(ctx, &passDayIncome, sql)
	if err != nil {
		if err.Error() == "sql: no rows in result set" {
			return 0, nil
		} else if strings.Contains(err.Error(), "Scan error on column index") {
			return 0, nil
		} else {
			log.Errorf("GetIncomesByTime db err:%s", err.Error())
			return 0, err
		}
	}
	// 现在的表
	sql = fmt.Sprintf(
		"SELECT sum(income) FROM gold_diamond_award_%02d WHERE guild_id=%d AND bought_time >= %d AND bought_time < %d",
		s.getTableIndex(guildId), guildId, todayZero*1000, nowTime*1000)
	sql += fmt.Sprintf(" and source_type = %d", sourceType)
	var todayIncome int64
	err = s.db.GetContext(ctx, &todayIncome, sql)
	if err != nil {
		if err.Error() == "sql: no rows in result set" {
			return 0, nil
		} else if strings.Contains(err.Error(), "Scan error on column index") {
			return 0, nil
		} else {
			log.Errorf("GetIncomesByTime db err:%s", err.Error())
			return 0, err
		}
	}
	return passDayIncome + todayIncome, nil
}

type ConsumeRankItem struct {
	TotalFee int64  `db:"total_fee"`
	PaidUid  uint32 `db:"paid_uid"`
}

func (s *Store) GetConsumeRank(ctx context.Context, guildId uint32, beginTime int64) ([]*ConsumeRankItem, error) {
	log.Debugf("beginTime: %s", time.Unix(beginTime, 0).String())
	sql := fmt.Sprintf(
		"SELECT sum(fee) as total_fee, paid_uid  FROM gold_diamond_award_%02d WHERE guild_id=%d AND bought_time >= %d and bought_time < %d", s.getTableIndex(guildId), guildId, beginTime*1000, (beginTime+86400)*1000)
	sql += fmt.Sprintf(" and source_type = %d group by paid_uid order by total_fee desc limit 0, 10", pb.IncomeType_CHANNEL_SEND_GIFT)
	elems := make([]*ConsumeRankItem, 0, 10)
	err := s.db.SelectContext(ctx, &elems, sql)
	if err != nil {
		if err.Error() == "sql: no rows in result set" {
			return elems, nil
		} else {
			log.Errorf("GetConsumeRank db err:%s", err.Error())
			return nil, err
		}
	}
	log.Debugf("len elems: %d", len(elems))
	return elems, nil
}

func (s *Store) FixIncomeBalance(ctx context.Context, guildId uint32) (tx *sqlx.Tx, err error) {
	var incomeBalance int64
	// tx = s.db.MustBegin()
	tx, err = s.db.Beginx()
	if err != nil {
		log.Errorln("FixIncomeBalance Beginx err:", err.Error(), " guildid:", guildId)
		return nil, err
	}
	_, err = tx.ExecContext(ctx, `INSERT INTO lock_table_gold_diamond (guild_id) VALUES (?) ON DUPLICATE KEY UPDATE update_time= VALUES(update_time)`, guildId)
	if err != nil {
		log.Errorln("FixIncomeBalance insert lock_table_gold_diamond err:", err.Error())
		return nil, err
	}

	sql := fmt.Sprintf("SELECT sum(income) FROM gold_diamond_award_%02d WHERE guild_id = ? AND bought_time >= 1567267200000 AND bought_time < 1569859200000 and status = 0 and source_type = 6",
		s.getTableIndex(guildId))
	err = tx.GetContext(ctx, &incomeBalance, sql, guildId)
	if err != nil {
		if err.Error() == "sql: no rows in result set" {
			incomeBalance = 0
		} else if strings.Contains(err.Error(), "Scan error on column index") {
			incomeBalance = 0
		} else {
			log.Errorf("get incomebalance err:%s", err.Error())
			tx.Rollback()
			return nil, err
		}
	}

	sql = fmt.Sprintf("UPDATE gold_diamond_award_%02d SET income_balance = %d WHERE id = (select temp.id from (select id from gold_diamond_award_%02d where guild_id = %d and bought_time >= 1567267200000 AND (source_type = 6 or source_type = 7) order by id desc limit 1) as temp)",
		s.getTableIndex(guildId), incomeBalance, s.getTableIndex(guildId), guildId)
	_, err = tx.ExecContext(ctx, sql)
	if err != nil {
		log.Errorln("FixIncomeBalance update income balance err:", err.Error())
		tx.Rollback()
		return nil, err
	}
	return
}

func (s *Store) SettlementChannel(ctx context.Context, guildId uint32, beginTime, endTime, income int64, sourceType pb.IncomeType, isTest bool) (tx *sqlx.Tx, err error) {
	nowTime := time.Now()
	descTime := time.Date(nowTime.Year(), nowTime.Month(), 1, 0, 0, 0, 0, time.Local).Format("2006-01-02 00:00:00")
	orderId := fmt.Sprintf("order_id_%d_%d", guildId, endTime)
	uniqSign := fmt.Sprintf("sign_%d_%d", guildId, endTime)
	if isTest {
		uniqSign = fmt.Sprintf("sign_%d_%d_%d", guildId, endTime, nowTime.Unix())
	}
	boughtTime := nowTime.Unix()
	var desc string
	if sourceType == pb.IncomeType_SETTLEMENT {
		desc = fmt.Sprintf("结算于:%s 前的金钻收益", descTime)
	} else if sourceType == pb.IncomeType_CHANNEL_SETTLEMENT {
		desc = fmt.Sprintf("结算于:%s 前的房间送礼金钻收益", descTime)
	}

	extand := ""

	// tx = s.db.MustBegin()
	tx, err = s.db.Beginx()
	if err != nil {
		log.Errorln("SettlementChannel Beginx err:", err.Error(), " guildid:", guildId)
		return nil, err
	}

	_, err = tx.ExecContext(ctx, `INSERT INTO lock_table_gold_diamond (guild_id) VALUES (?) ON DUPLICATE KEY UPDATE update_time= VALUES(update_time)`, guildId)
	if err != nil {
		log.Errorln("SettlementChannel insert lock_table_gold_diamond err:", err.Error())
		return nil, err
	}

	sql := fmt.Sprintf("UPDATE gold_diamond_award_%02d SET status = 1 WHERE guild_id = %d AND bought_time >= %d "+
		" AND bought_time < %d AND source_type = %d", s.getTableIndex(guildId), guildId, beginTime*1000, endTime*1000, pb.IncomeType_CHANNEL_SEND_GIFT)
	_, err = tx.ExecContext(ctx, sql)
	if err != nil {
		log.Errorln("SettlementChannel update status=1 err:", err.Error())
		return nil, err
	}

	var incomeBalance int64
	incomeSql := fmt.Sprintf(`SELECT income_balance FROM gold_diamond_award_%02d WHERE guild_id=? order by id desc limit 1`, s.getTableIndex(guildId))
	err = tx.GetContext(ctx, &incomeBalance, incomeSql, guildId)
	if err != nil {
		log.Errorf("SettlementChannel get incomebalance err:%s", err.Error())
		return nil, err
	}
	incomeBalance -= income
	if incomeBalance < 0 {
		log.Errorln("SettlementChannel err incomeBalance < 0, guildId:%d, incomeBalance:%d, income:%d", guildId, incomeBalance, income)
	}

	/*delSql := fmt.Sprintf("delete from gold_diamond_award_%02d where guild_id=%d and status = 1 and bought_time >= 1567267200 and bought_time < 1569859200 and source_type = 7", s.getTableIndex(guildId), guildId)
	_, err = tx.ExecContext(ctx, delSql)
	if err != nil {
		return nil, err
		log.Errorln("SettlementChannel delete old err:", err.Error())
	}*/

	insertSQL := fmt.Sprintf(`INSERT INTO gold_diamond_award_%02d (guild_id, paid_uid, uniq_sign, 
	order_id, source_type, income, desc_income, extand, bought_time, status, income_balance, fee, game_id, room_id) 
	VALUES(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`, s.getTableIndex(guildId))
	_, err = tx.ExecContext(ctx, insertSQL, guildId, 0, uniqSign, orderId, sourceType, -income, desc, extand, boughtTime*1000, 1, incomeBalance, 0, 0, 0)
	if err != nil {
		log.Errorln("SettlementChannel gift insertSQL err:", err.Error())
		return nil, err
	}
	return tx, nil
}

func (s *Store) SettlementCommit(tx *sqlx.Tx) (err error) {
	err = tx.Commit()
	if err != nil {
		log.Errorln("SettlementCommit err:", err.Error())
		return err
	}
	return nil
}

func (s *Store) SettlementRollBack(tx *sqlx.Tx) (err error) {
	err = tx.Rollback()
	if err != nil {
		log.Errorln("SettlementRollBack err:", err.Error())
		return err
	}
	return nil
}

func (s *Store) TestMonthAllGuildId(ctx context.Context) error {
	query := "SELECT guild_id FROM month_guild_gold_diamond WHERE stat_time = 1575129600"
	var guildids = make([]uint32, 0)
	err := s.db.SelectContext(ctx, &guildids, query)
	if err != nil {
		if err.Error() == "sql: no rows in result set" {
			return nil
		} else {
			fmt.Println("get uid:", err)
			return err
		}
	}
	for _, guildid := range guildids {
		if guildid%100 == 0 {
			fmt.Println("get uid:", guildid)
		}
	}
	return nil
}

type IdType struct {
	Id int64 `db:"id"`
}

func (s *Store) GetLimitGuidids(ctx context.Context, tableIndex int, strDelTime string) (elems []*IdType, err error) {
	query := fmt.Sprintf(`
	SELECT id FROM gold_diamond_award_%02d WHERE
	bought_time < 1582992000000 limit 1000`, tableIndex)
	err = s.db.SelectContext(ctx, &elems, query)
	if err != nil {
		if err.Error() == "sql: no rows in result set" {
			return elems, nil
		} else {
			fmt.Printf("GetLimitGuidids db err:%s\n", err.Error())
			return nil, err
		}
	}
	return elems, nil
}

func (s *Store) DeleteByOrderId(ctx context.Context, tableIndex int, orderId string) error {
	sql := fmt.Sprintf(
		"delete FROM gold_diamond_award_%02d WHERE order_id='%s' AND bought_time >= %d AND bought_time < %d",
		tableIndex, orderId, 1614931200000, 1614934800000)
	_, err := s.db.ExecContext(ctx, sql)
	if err != nil {
		log.Errorln("DeleteByOrderId delete err:", err.Error())
		return err
	}
	return nil
}

/*21 03 06 修复*/
func (s *Store) GetAllGoldInfoByOrderId(ctx context.Context, tableIndex int, orderId string) (*GoldDiamonStore, error) {
	sql := fmt.Sprintf(
		"SELECT * FROM gold_diamond_award_%02d WHERE order_id='%s' AND bought_time >= %d AND bought_time < %d",
		tableIndex, orderId, 1614931200000, 1614934800000)
	sql += fmt.Sprintf(" and source_type = %d", pb.IncomeType_CHANNEL_SEND_GIFT)
	feeInfo := &GoldDiamonStore{}
	// fmt.Printf("select sql :%s\n", sql)
	err := s.db.GetContext(ctx, feeInfo, sql)
	if err != nil {
		if err.Error() == "sql: no rows in result set" {
			return feeInfo, nil
		} else if strings.Contains(err.Error(), "Scan error on column index") {
			return feeInfo, nil
		} else {
			log.Errorf("GetAllGoldInfoByOrderId db err:%s, table:%d", err.Error(), tableIndex)
			return feeInfo, err
		}
	}
	return feeInfo, nil
}

func (s *Store) AwardDiamondFix(ctx context.Context, guildId uint32, sourceType pb.IncomeType, orderId string, fee int64) (err error) {
	// var status uint32 = 0
	// var gameId uint32 = 0
	// var roomId uint32 = indetifyId
	// desc := "3月5号订单异常送礼，回滚这批佣金数"

	uniqSign := fmt.Sprintf("sign_%s", orderId)

	// var totalIncome int64
	tx, err := s.db.Beginx()
	if err != nil {
		log.Errorln("AwardDiamond Beginx err:", err.Error(), " guildid:", guildId)
		return err
	}
	_, err = tx.ExecContext(ctx, `INSERT INTO lock_table_gold_diamond (guild_id) VALUES (?) ON DUPLICATE KEY UPDATE update_time= VALUES(update_time)`, guildId)
	if err != nil {
		log.Errorln("AwardDiamond gift insert lock_table_gold_diamond err:", err.Error(), " guildid:", guildId)
		tx.Rollback()
		return err
	}
	updateSQL := fmt.Sprintf("UPDATE gold_diamond_award_%02d SET fee = %d WHERE guild_id = %d AND uniq_sign = '%s' AND source_type = %d", s.getTableIndex(guildId), fee, guildId, uniqSign, sourceType)
	_, err = tx.Exec(updateSQL)
	if err != nil {
		log.Errorln("AwardDiamond gift insertSQL err:", err.Error(), " guildid:", guildId)
		tx.Rollback()
		return err
	}
	err = tx.Commit()
	if err != nil {
		log.Errorln("AwardDiamond gift commit err:", err.Error(), " guildid:", guildId)
		return err
	}
	return nil
}

func (s *Store) AddVerifyGold(ctx context.Context, guildID, paidUid, roomId uint32, orderId string, income, boughtTime, fee int64, status uint32) (err error) {
	var desc = "verify"
	getTime := time.Unix(boughtTime, 0).Format("200601")
	tableName := fmt.Sprintf("gold_verify_%s", getTime)
	insertSQL := fmt.Sprintf(`INSERT INTO %s ( guild_id, paid_uid, room_id, order_id, income, fee, descinfo, bought_time) VALUES(?, ?, ?, ?, ?, ?, ?, ?) ON DUPLICATE KEY UPDATE status=%d`, tableName, status)
	_, err = s.db.ExecContext(ctx, insertSQL, guildID, paidUid, roomId, orderId, income, fee, desc, boughtTime)
	if err != nil {
		if !strings.Contains(err.Error(), "Error 1146") {
			log.Errorf("AddVerifyGold insertSQL err:%v, orderid:%s", err, orderId)
			return err
		}
		tbName := fmt.Sprintf(yuyinGold.CreateVerifyGoldTable, getTime)
		_, err = s.db.Exec(tbName)
		if err != nil {
			return err
		}
		_, err = s.db.ExecContext(ctx, insertSQL, tableName, paidUid, roomId, orderId, income, fee, desc, boughtTime)
		return err
	}
	return nil
}

func (s *Store) UpdateVerifyGoldStatus(ctx context.Context, orderId string, boughtTime int64, status uint32) (err error) {
	getTime := time.Unix(boughtTime, 0).Format("200601")
	tableName := fmt.Sprintf("gold_verify_%s", getTime)
	sql := fmt.Sprintf("UPDATE %s SET status = %d WHERE order_id = '%s'", tableName, status, orderId)
	_, err = s.db.ExecContext(ctx, sql)
	if err != nil {
		log.Errorln("UpdateVerifyGoldStatus update status=1 err:", err.Error())
		return err
	}
	return nil
}

func (s *Store) GetVerifyGold(ctx context.Context, beginTime, endTime int64, status uint32) ([]*yuyinGold.GoldVerifyStoreGuild, error) {
	getTime := time.Unix(beginTime, 0).Format("200601")
	tableName := fmt.Sprintf("gold_verify_%s", getTime)
	getSQL := fmt.Sprintf(`SELECT * FROM %s WHERE bought_time >= ? AND bought_time < ? and status=%d`, tableName, status)
	items := make([]*yuyinGold.GoldVerifyStoreGuild, 0, 10)
	err := s.db.SelectContext(ctx, &items, getSQL, beginTime, endTime)
	if err != nil {
		if err.Error() == "sql: no rows in result set" || strings.Contains(err.Error(), "Scan error on column index") {
			return items, nil
		} else if strings.Contains(err.Error(), "Error 1146") {
			return items, nil
		} else {
			log.Errorf("GetVerifyGold db err:%s", err.Error())
			return nil, err
		}
	}
	return items, nil
}

func (s *Store) GetVerifyGoldByOrderid(ctx context.Context, orderId, getTime string) ([]*yuyinGold.GoldVerifyStoreGuild, error) {
	tableName := fmt.Sprintf("gold_verify_%s", getTime)
	getSQL := fmt.Sprintf(`SELECT * FROM %s WHERE  order_id='%s'`, tableName, orderId)
	items := make([]*yuyinGold.GoldVerifyStoreGuild, 0, 10)
	err := s.db.SelectContext(ctx, &items, getSQL)
	if err != nil {
		if err.Error() == "sql: no rows in result set" || strings.Contains(err.Error(), "Scan error on column index") {
			return items, nil
		} else if strings.Contains(err.Error(), "Error 1146") {
			return items, nil
		} else {
			log.Errorf("GetVerifyGold db err:%s", err.Error())
			return nil, err
		}
	}
	return items, nil
}

func (s *Store) GetVerifyGoldCnt(ctx context.Context, beginTime, endTime int64, status uint32) (uint32, error) {
	getTime := time.Unix(beginTime, 0).Format("200601")
	tableName := fmt.Sprintf("gold_verify_%s", getTime)
	getSQL := fmt.Sprintf(`SELECT count(1) FROM %s WHERE bought_time >= ? AND bought_time < ? and status=%d`, tableName, status)
	var totalCnt uint32
	err := s.db.GetContext(ctx, &totalCnt, getSQL, beginTime, endTime)
	if err != nil {
		if err.Error() == "sql: no rows in result set" || strings.Contains(err.Error(), "Scan error on column index") {
			return 0, nil
		} else if strings.Contains(err.Error(), "Error 1146") {
			return 0, nil
		} else {
			log.Errorf("GetVerifyGoldCnt db err:%s", err.Error())
			return 0, err
		}
	}
	return totalCnt, nil
}

func (s *Store) GetVerifyGoldSum(ctx context.Context, beginTime, endTime int64, status uint32) (uint32, error) {
	getTime := time.Unix(beginTime, 0).Format("200601")
	tableName := fmt.Sprintf("gold_verify_%s", getTime)
	getSQL := fmt.Sprintf(`SELECT sum(fee) FROM %s WHERE bought_time >= ? AND bought_time < ? and status=%d`, tableName, status)
	var sumFee uint32
	err := s.db.GetContext(ctx, &sumFee, getSQL, beginTime, endTime)
	if err != nil {
		if err.Error() == "sql: no rows in result set" || strings.Contains(err.Error(), "Scan error on column index") {
			return 0, nil
		} else if strings.Contains(err.Error(), "Error 1146") {
			return 0, nil
		} else {
			log.Errorf("GetVerifyGoldSum db err:%s", err.Error())
			return 0, err
		}
	}
	return sumFee, nil
}

func (s *Store) GetGuildAwardInfoByOrderId(ctx context.Context, guildid uint32, orderId string) (*GoldDiamonStore, error) {
	sql := fmt.Sprintf(
		"SELECT * FROM gold_diamond_award_%02d WHERE order_id='%s' AND bought_time >= %d AND bought_time < %d AND guild_id=%d",
		s.getTableIndex(guildid), orderId, 1619798400000, 1622476800000, guildid)
	sql += fmt.Sprintf(" and source_type = %d", pb.IncomeType_CHANNEL_SEND_GIFT)
	feeInfo := &GoldDiamonStore{}
	// fmt.Printf("select sql :%s\n", sql)
	err := s.db.GetContext(ctx, feeInfo, sql)
	if err != nil {
		if err.Error() == "sql: no rows in result set" {
			return feeInfo, nil
		} else if strings.Contains(err.Error(), "Scan error on column index") {
			return feeInfo, nil
		} else {
			log.Errorf("GetAllGoldInfoByOrderId db err:%s, guild_id:%d", err.Error(), guildid)
			return feeInfo, err
		}
	}
	return feeInfo, nil
}
func (s *Store) FixAddOrder(ctx context.Context, guildId uint32, orderId string, fee int64, updateTime time.Time) (err error) {

	uniqSign := fmt.Sprintf("sign_%s", orderId)

	updateSQL := fmt.Sprintf("UPDATE gold_diamond_award_%02d SET fee = %d, income = %d, desc_income= '%s',update_time = ? WHERE guild_id = %d AND uniq_sign = '%s'", s.getTableIndex(guildId), fee, fee/10, "", guildId, uniqSign)
	_, err = s.db.Exec(updateSQL, updateTime)
	if err != nil {
		log.Errorln("AwardDiamond gift insertSQL err:", err.Error(), " guildid:", guildId)
		return err
	}
	return nil
}

// GetGuildUnSettlementSummary 获取工会未结算的流水与收益
func (s *Store) GetGuildUnSettlementSummary(ctx context.Context, guildId uint32, beginTime, endTime int64) (int64, int64, error) {
	type UnSettlementSummaryRet struct {
		TotalFee    int64 `db:"total_fee"`
		TotalIncome int64 `db:"total_income"`
	}
	sql := fmt.Sprintf("SELECT sum(fee) as total_fee, sum(income) as total_income "+
		"FROM gold_diamond_award_%02d WHERE guild_id = ? AND bought_time >= ? AND bought_time < ? and status = 0 and source_type = ? ",
		s.getTableIndex(guildId))
	record := new(UnSettlementSummaryRet)
	err := s.db.GetContext(ctx, record, sql, guildId, beginTime*1000, endTime*1000, pb.GoldIncomeType_ROOM_INCOME)
	if err != nil {
		if err.Error() == "sql: no rows in result set" || strings.Contains(err.Error(), "Scan error on column index") {
			return 0, 0, nil
		} else {
			log.Errorf("GetGuildUnSettlementSummary db err:%s", err.Error())
			return 0, 0, err
		}
	}
	return record.TotalFee, record.TotalIncome, nil
}

func (s *Store) GetGuildsRoomStat(ctx context.Context, unit pb.TimeFilterUnit, guildIds []uint32, beginTime, endTime int64, offset, limit uint32) (elems []*GoldDiamondAwardStat, err error) {
	if limit > 500 {
		limit = 500
	}
	inStr := make([]string, 0)
	for _, g := range guildIds {
		inStr = append(inStr, strconv.Itoa(int(g)))
	}
	inItems := strings.Join(inStr, ",")

	var query string
	if unit == pb.TimeFilterUnit_BY_MONTH {
		query = fmt.Sprintf(`SELECT month_date as date, guild_id, room_id, fee, income FROM month_room_stat WHERE guild_id in (%s) AND month_date >= ? AND month_date <= ? LIMIT ?, ?`, inItems)
	} else {
		query = fmt.Sprintf(`SELECT day_date as date, guild_id, room_id, fee, income FROM day_room_stat WHERE guild_id in (%s) AND day_date >= ? AND day_date <= ? LIMIT ?, ?`, inItems)
	}
	fmt.Printf("GetGuildsRoomStat query: %s,beginTime:%d,endtime:%d\n", query, beginTime, endTime)
	elems = make([]*GoldDiamondAwardStat, 0, 100)
	err = s.db.SelectContext(ctx, &elems, query, beginTime, endTime, offset, limit)
	if err != nil {
		if err.Error() == "sql: no rows in result set" || strings.Contains(err.Error(), "Scan error on column index") {
			return elems, nil
		} else {
			log.Errorf("GetGuildsAnchorStat db  err:%s", err.Error())
			return nil, err
		}
	}
	return elems, nil
}

func (s *Store) GetGuildMonthSum(ctx context.Context, guildId uint32, t time.Time) (int64, int64, error) {
	type SummaryRet struct {
		TotalFee    int64 `db:"total_fee"`
		TotalIncome int64 `db:"total_income"`
	}
	monthUnix := time.Date(t.Year(), t.Month(), 1, 0, 0, 0, 0, t.Location()).Unix()
	sql := "SELECT SUM(fee) AS total_fee, SUM(income) AS total_income FROM month_room_stat WHERE month_date = ? AND guild_id = ?"
	record := new(SummaryRet)
	err := s.db.GetContext(ctx, record, sql, monthUnix, guildId)
	if err != nil {
		if err.Error() == "sql: no rows in result set" || strings.Contains(err.Error(), "Scan error on column index") {
			return 0, 0, nil
		} else {
			log.Errorf("GetGuildsMonthSum db err:%s", err.Error())
			return 0, 0, err
		}
	}
	return record.TotalFee, record.TotalIncome, nil
}

func (s *Store) GetGuildRoomMonthSum(ctx context.Context, guildId, roomId uint32, t time.Time) (int64, int64, error) {
	type SummaryRet struct {
		TotalFee    int64 `db:"total_fee"`
		TotalIncome int64 `db:"total_income"`
	}
	monthUnix := time.Date(t.Year(), t.Month(), 1, 0, 0, 0, 0, t.Location()).Unix()
	sql := "SELECT SUM(fee) AS total_fee, SUM(income) AS total_income FROM month_room_stat WHERE month_date = ? AND guild_id = ? AND room_id = ?"
	record := new(SummaryRet)
	err := s.db.GetContext(ctx, record, sql, monthUnix, guildId, roomId)
	if err != nil {
		if err.Error() == "sql: no rows in result set" || strings.Contains(err.Error(), "Scan error on column index") {
			return 0, 0, nil
		} else {
			log.Errorf("GetGuildsMonthSum db err:%s", err.Error())
			return 0, 0, err
		}
	}
	return record.TotalFee, record.TotalIncome, nil
}

func (s *Store) GetGuildDaySum(ctx context.Context, guildId uint32, t time.Time) (int64, int64, error) {
	type SummaryRet struct {
		TotalFee    int64 `db:"total_fee"`
		TotalIncome int64 `db:"total_income"`
	}
	dayUnix := time.Date(t.Year(), t.Month(), t.Day(), 0, 0, 0, 0, t.Location()).Unix()
	sql := "SELECT SUM(fee) AS total_fee, SUM(income) AS total_income FROM day_room_stat WHERE day_date = ? AND guild_id = ?"
	record := new(SummaryRet)
	err := s.db.GetContext(ctx, record, sql, dayUnix, guildId)
	if err != nil {
		if err.Error() == "sql: no rows in result set" || strings.Contains(err.Error(), "Scan error on column index") {
			return 0, 0, nil
		} else {
			log.Errorf("GetGuildsMonthSum db err:%s", err.Error())
			return 0, 0, err
		}
	}
	return record.TotalFee, record.TotalIncome, nil
}

func (s *Store) GetGuildMonthChannelSummary(ctx context.Context, guildId uint32, t time.Time, channelIds []uint32) ([]*MonthRoomStat, uint32, error) {
	monthUnix := time.Date(t.Year(), t.Month(), 1, 0, 0, 0, 0, t.Location()).Unix()
	channelIdString := make([]string, 0)
	if len(channelIds) > 0 {
		for _, c := range channelIds {
			if c > 0 {
				channelIdString = append(channelIdString, strconv.Itoa(int(c)))
			}
		}
	}
	var count uint32
	countSql := "SELECT COUNT(*) AS COUNT FROM month_room_stat WHERE month_date = ? AND guild_id = ?"
	if len(channelIdString) > 0 {
		countSql += fmt.Sprintf(" and room_id in (%s)", strings.Join(channelIdString, ","))
	}
	err := s.db.GetContext(ctx, &count, countSql, monthUnix, guildId)
	if err != nil {
		log.Errorf("GetGuildMonthChannelSummary db count err:%s", err.Error())
		return nil, 0, err
	}
	sql := "SELECT room_id, SUM(fee) AS fee FROM month_room_stat WHERE month_date = ? AND guild_id = ?"
	if len(channelIdString) > 0 {
		sql += fmt.Sprintf(" and room_id in (%s)", strings.Join(channelIdString, ","))
	}
	sql += " group by room_id"
	records := make([]*MonthRoomStat, 0)
	log.Debugf("GetGuildMonthChannelSummary sql: %s", sql)
	err = s.db.SelectContext(ctx, &records, sql, monthUnix, guildId)
	if err != nil {
		if err.Error() == "sql: no rows in result set" || strings.Contains(err.Error(), "Scan error on column index") {
			return records, 0, nil
		} else {
			log.Errorf("GetGuildMonthChannelSummary db err:%s", err.Error())
			return nil, 0, err
		}
	}
	return records, count, nil
}

func (s *Store) GetGuildDayChannelSummary(ctx context.Context, guildId uint32, start, end time.Time, channelIds []uint32, offset, limit uint32) ([]*DayRoomStat, uint32, error) {
	channelIdString := make([]string, 0)
	if len(channelIds) > 0 {
		for _, c := range channelIds {
			if c > 0 {
				channelIdString = append(channelIdString, strconv.Itoa(int(c)))
			}
		}
	}
	if limit > 100 {
		limit = 100
	}
	var count uint32
	var countSql string
	if len(channelIdString) > 0 {
		countSql = fmt.Sprintf("select count(*) from ( select count(1) from day_room_stat where day_date >= ? and day_date < ? and guild_id = ? and room_id in (%s) group by room_id ) t", strings.Join(channelIdString, ","))
	} else {
		countSql = "select count(*) from ( select count(1) from day_room_stat where day_date >= ? and day_date < ? and guild_id = ? group by room_id ) t"
	}
	err := s.db.GetContext(ctx, &count, countSql, start.Unix(), end.Unix(), guildId)
	if err != nil {
		log.Errorf("GetGuildMonthChannelSummary db count err:%s", err.Error())
		return nil, 0, err
	}
	sql := "SELECT room_id, SUM(fee) AS fee FROM day_room_stat WHERE day_date >= ? AND day_date < ? AND guild_id = ?"
	if len(channelIdString) > 0 {
		sql += fmt.Sprintf(" and room_id in (%s)", strings.Join(channelIdString, ","))
	}
	sql += " group by room_id limit ?, ?"
	records := make([]*DayRoomStat, 0)
	fmt.Println("GetGuildMonthChannelSummary", sql, start.Unix(), end.Unix(), guildId, offset, limit)
	err = s.db.SelectContext(ctx, &records, sql, start.Unix(), end.Unix(), guildId, offset, limit)
	if err != nil {
		if err.Error() == "sql: no rows in result set" || strings.Contains(err.Error(), "Scan error on column index") {
			return records, 0, nil
		} else {
			log.Errorf("GetGuildMonthChannelSummary db err:%s", err.Error())
			return nil, 0, err
		}
	}
	return records, count, nil
}

func (s *Store) GetGuildRoomDayRangeSum(ctx context.Context, guildId, roomId uint32, start, end time.Time) (uint64, uint64, error) {
	startUnix := time.Date(start.Year(), start.Month(), start.Day(), 0, 0, 0, 0, start.Location()).Unix()
	endUnix := time.Date(end.Year(), end.Month(), end.Day(), 0, 0, 0, 0, end.Location()).Unix()
	sql := "SELECT SUM(fee) AS fee, SUM(income) AS income FROM day_room_stat WHERE day_date >= ? AND day_date < ? AND guild_id = ? AND room_id = ?"
	record := new(MonthRoomStat)
	err := s.db.GetContext(ctx, record, sql, startUnix, endUnix, guildId, roomId)
	if err != nil {
		if err.Error() == "sql: no rows in result set" || strings.Contains(err.Error(), "Scan error on column index") {
			return 0, 0, nil
		} else {
			log.Errorf("GetGuildRoomDayRangeSum db err:%s", err.Error())
			return 0, 0, err
		}
	}
	return record.Fee, record.Income, nil
}
