package server

import (
	"context"
	"fmt"
	//"crypto/rand"
	"sort"
	"strconv"
	"strings"
	"sync"
	"time"

	"golang.52tt.com/clients/account"
	apicenter "golang.52tt.com/clients/apicenter/apiserver"
	award_center "golang.52tt.com/clients/award-center"
	backpack_base "golang.52tt.com/clients/backpack-base"
	backpacksender "golang.52tt.com/clients/backpack-sender"
	channel_personalization "golang.52tt.com/clients/channel-personalization"
	HeadImage "golang.52tt.com/clients/headimage"
	headwear "golang.52tt.com/clients/headwearer"
	publicnotice "golang.52tt.com/clients/public-notice"
	PushNotification "golang.52tt.com/clients/push-notification/v2"
	userPresent "golang.52tt.com/clients/userpresent-go"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/protocol/common/status"
	apiPB "golang.52tt.com/protocol/services/apicenter/apiserver"
	backPack "golang.52tt.com/protocol/services/backpack-base"
	bpb "golang.52tt.com/protocol/services/backpacksender"
	pb "golang.52tt.com/protocol/services/conversion"
	awardcenter "golang.52tt.com/protocol/services/risk-control/award-center"
	"golang.52tt.com/services/conversion/cache"
	"golang.52tt.com/services/conversion/conf"
	"golang.52tt.com/services/conversion/mysql"
	"golang.52tt.com/services/conversion/report"
	"golang.52tt.com/services/notify"
	butils "golang.52tt.com/services/risk-control/backpack-sender/utils"
)

const (
	RoolBackSec          = 60 * 60 * 6 //6个小时之后还没发礼成功，直接手动处理吧
	ConversionLockKey    = "ConversionLockKey"
	ConversionNodeIdKey  = "ConversionNodeIdKey"
	PageCount            = 1024
	DelMonthsAgoOrderKey = "DelMonthsAgoOrderKey" //定时清理几个月前的订单
)

type ConversionTcc struct {
	db                    *mysql.ConversionMysql
	seq                   uint32
	backpackClient        backpack_base.IClient
	headwearerClient      *headwear.Client
	personalizationClient *channel_personalization.Client
	redis                 *cache.RedisDaoClient
	ApiClient             *apicenter.Client
	accountClient         *account.Client
	userpresentClient     *userPresent.Client
	backpackSenderClient  *backpacksender.Client
	awardCenterClient     *award_center.Client
	pushCli               *PushNotification.Client
	publicNoticeCli       *publicnotice.Client
	headImgCli            *HeadImage.Client

	nodeId               int64
	reportLink           string
	reportSecret         string
	Off                  uint32
	BeginTs              uint32
	fragmentCfgCacheSync sync.Map
	darkMaterialListSync sync.Map
}

type FrameItemList []*backPack.UserBackpackItem

func (w FrameItemList) Len() int {
	return len(w)
}

func (w FrameItemList) Less(i, j int) bool {
	if w[i].FinTime == 0 || w[j].FinTime == 0 {
		return w[i].FinTime > w[j].FinTime
	}
	return w[i].FinTime < w[j].FinTime
}

func (w FrameItemList) Swap(i, j int) {
	w[i], w[j] = w[j], w[i]
}

func (c *ConversionTcc) getUseItemList(useItemList []*pb.UserItemInfo, uid, count uint32) ([]*backPack.UseItemInfo, error) {
	iteminfoList := make([]*backPack.UseItemInfo, 0, 100)

	ctx, cancel := context.WithTimeout(context.Background(), time.Second*3)
	defer cancel()
	bpResp, err := c.backpackClient.GetUserBackpackByItem(ctx, uid, uint32(backPack.PackageItemType_BACKPACK_LOTTERY_FRAGMENT), 0)
	if nil != err {
		log.ErrorWithCtx(ctx, "getUseItemList GetUserBackpack uid:%v err:%v", uid, err)
		return iteminfoList, err
	}
	if len(bpResp.GetUserItemList()) == 0 {
		return iteminfoList, protocol.NewServerError(status.ErrGamelottoFragmentNotEnough, "碎片不足")
	}
	frameItemList := FrameItemList(bpResp.GetUserItemList())
	// var frameItemList FrameItemList
	// for _, item := range bpResp.GetUserItemList() {
	// 	if item.GetItemType() == uint32(backPack.PackageItemType_BACKPACK_LOTTERY_FRAGMENT) {
	// 		frameItemList = append(frameItemList, item)
	// 	}
	// }

	sort.Sort(frameItemList)

	mapUseItem := make(map[uint32]uint32)
	for _, uitem := range useItemList {
		mapUseItem[uitem.SourceId] = mapUseItem[uitem.SourceId] + (uitem.UseCount * count)
	}

	for sourceId, useCount := range mapUseItem {
		tmpUseCount := useCount
		for _, frame := range frameItemList {
			if frame.SourceId != sourceId || frame.ItemCount == 0 {
				continue
			}
			ucount := tmpUseCount
			if ucount > frame.ItemCount {
				ucount = frame.ItemCount
			}
			frame.ItemCount = frame.ItemCount - ucount
			tmpUseCount = tmpUseCount - ucount

			iteminfoList = append(iteminfoList, &backPack.UseItemInfo{
				ItemType:   frame.ItemType,
				UserItemId: frame.UserItemId,
				SourceId:   frame.SourceId,
				UseCount:   ucount,
			})

			if tmpUseCount == 0 {
				break
			}
		}

		if tmpUseCount > 0 {
			return iteminfoList, protocol.NewServerError(status.ErrGamelottoFragmentNotEnough, "碎片不足")
		}
	}

	return iteminfoList, nil
}

func (c *ConversionTcc) Prepare(ctx context.Context, orderId string, uid, count uint32, conversion *pb.ConversionDebris, ts time.Time) error {
	iteminfoList, err := c.getUseItemList(conversion.UserItemList, uid, count)
	if nil != err {
		return err
	}

	req := &backPack.FreeZeItemReq{
		Uid: uid,
		TansacationInfo: &backPack.TransactionInfo{
			FreezeType: uint32(backPack.FREEZETYPE_FREEZETYPE_PREPARE),
			OperTime:   uint32(ts.Unix()),
			OrderId:    orderId,
			ExpireTime: uint32(ts.Add(time.Hour * 24 * 5).Unix()),
			Reason:     uint32(backPack.LogType_LOG_TYPE_FRAGMENT_EXCHANGE),
		},
		ItemInfoList: iteminfoList,
	}
	_, err = c.backpackClient.FreeZeItem(ctx, req)

	return err
}

func (c *ConversionTcc) Commit(orderId string, uid uint32, ts time.Time) (time.Time, error) {
	iteminfoList := make([]*backPack.UseItemInfo, 0)

	nowTs := time.Now()
	req := &backPack.FreeZeItemReq{
		Uid: uid,
		TansacationInfo: &backPack.TransactionInfo{
			FreezeType: uint32(backPack.FREEZETYPE_FREEZETYPE_COMMIT),
			OperTime:   uint32(ts.Unix()),
			OrderId:    orderId,
			ExpireTime: uint32(nowTs.Unix() + 3600),
			Reason:     uint32(backPack.LogType_LOG_TYPE_FRAGMENT_EXCHANGE),
		},
		ItemInfoList: iteminfoList,
	}
	ctx, cancel := context.WithTimeout(context.Background(), time.Second*3)
	defer cancel()
	_, err := c.backpackClient.FreeZeItem(ctx, req)
	return nowTs, err
}

func (c *ConversionTcc) RollBack(orderId string, uid uint32, ts time.Time) error {
	iteminfoList := make([]*backPack.UseItemInfo, 0)
	req := &backPack.FreeZeItemReq{
		Uid: uid,
		TansacationInfo: &backPack.TransactionInfo{
			FreezeType: uint32(backPack.FREEZETYPE_FREEZETYPE_ROLLBACK),
			OperTime:   uint32(ts.Unix()),
			OrderId:    orderId,
			ExpireTime: uint32(time.Now().Unix() + 3600),
			Reason:     uint32(backPack.PackageSourceType_PACKAGE_SOURCE_CONVERSION),
		},
		ItemInfoList: iteminfoList,
	}
	ctx, cancel := context.WithTimeout(context.Background(), time.Second*3)
	defer cancel()
	_, err := c.backpackClient.FreeZeItem(ctx, req)
	return err
}

func (c *ConversionTcc) SendPresent(ctx context.Context, orderId string, uid uint32, giftId uint32, itemNum uint32, nowTs time.Time) error {
	/*	req := &backPack.GiveUserPackageReq{
			Uid:     uid,
			BgId:    giftId,
			Num:     itemNum,
			Source:  uint32(backPack.PackageSourceType_PACKAGE_SOURCE_CONVERSION),
			OrderId: orderId,
		}
		resp, err := c.backpackClient.GiveUserPackage(c.ctx, uid, req)
		if err != nil {
			log.ErrorWithCtx(ctx,"[ConversionTcc] SendPresent GiveUserPackage resp:%v req:%v err:%v uid:%v", resp, req, err, uid)
			return err
		}*/

	ciphertext := butils.AESEncrypt([]byte(orderId), []byte(conf.GetSecretKey()))
	_, err := c.backpackSenderClient.SendBackpackWithRiskControl(ctx, &bpb.SendBackpackWithRiskControlReq{
		BusinessId:  conf.GetBusinessID(),
		BackpackId:  giftId,
		ReceiveUid:  uid,
		BackpackCnt: itemNum,
		ServerTime:  nowTs.Unix(),
		OrderId:     orderId,
		Ciphertext:  ciphertext,
	})

	if nil != err {
		log.ErrorWithCtx(ctx, "[ConversionTcc] SendBackpackWithRiskControl orderId:%v err:%v", orderId, err)
		return err
	}

	log.InfoWithCtx(ctx, "[ConversionTcc] SendPresent success uid:%v giftId:%v orderId:%v", uid, giftId, orderId)

	return nil
}

func (c *ConversionTcc) SendHorse(ctx context.Context, orderId string, uid uint32, itemId string, holdDay uint32, itemNum uint32, nowTs time.Time) error {
	/*effectiveBeginTime := time.Now()
	effectiveEndTime := effectiveBeginTime.AddDate(0, 0, int(holdDay))

	decoration := &channel_personalization.Decoration{
		Id:   itemId,
		Type: channel_personalization.ChannelEnterSpecialEffectType,
		Detail: &channel_personalization.DecorationDetail{
			ChannelEnterSpecialEffect: &channel_personalization.ChannelEnterSpecialEffect{},
		},
	}

	ud := &personalization.UserDecoration{
		Uid:           uid,
		Decoration:    decoration,
		EffectBegin:   uint64(time.Now().Unix()),
		EffectEnd:     uint64(effectiveEndTime.Unix()),
		GrantAt:       uint64(time.Now().Unix()),
		GrantReason:   "converison",
		GrantOperator: "add",
	}

	err := c.personalizationClient.GrantDecoration(c.ctx, ud, true)
	if err != nil {
		log.ErrorWithCtx(ctx,"[ConversionTcc] GrantDecoration fail err:%v uid:%v", err, uid)
		return err
	}*/

	req := &awardcenter.AwardReq{
		OrderId:    orderId,
		TargetUid:  uid,
		BusinessId: conf.GetAwardCenterBusinessID(),
		GiftId:     itemId,
		GiftType:   uint32(awardcenter.EGiftType_Horse),
		HoldingDay: holdDay,
		OutsideTs:  uint32(nowTs.Unix()),
	}

	err := c.awardCenterClient.Award(ctx, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "SendItemToUser SendHorse award_center.Award uid:%v err:%v", uid, err)
		return err
	}
	return nil
}

func (c *ConversionTcc) SendHeadwear(ctx context.Context, orderId string, uid uint32, suiteId string, holdDay uint32, nowTs time.Time) error {
	/*req := &headwearsvr.GiveHeadweartoUserReq{
		Uid:           uid,
		SuiteId:       suiteId,
		Level:         1,
		GiveType:      uint32(headwearsvr.GIVE_TYPE_GIVE_TYPE_APPEND),
		ExpireTime:    0,
		ExpireTimeRel: holdDay * 24 * 3600,
		CpUid:         0,
		OrderId:       orderId,
	}
	err := c.headwearerClient.GiveHeadweartoUserWithExpireTimeRel(c.ctx, uid, req)
	if err != nil {
		log.ErrorWithCtx(ctx,"SendItemToUser GiveHeadweartoUser uid:%v err:%v", uid, err)
		return err
	}*/

	req := &awardcenter.AwardReq{
		OrderId:    orderId,
		TargetUid:  uid,
		BusinessId: conf.GetAwardCenterBusinessID(),
		GiftId:     suiteId,
		GiftType:   uint32(awardcenter.EGiftType_Headwear),
		HoldingDay: holdDay,
		OutsideTs:  uint32(nowTs.Unix()),
	}

	err := c.awardCenterClient.Award(context.Background(), req)
	if err != nil {
		log.ErrorWithCtx(ctx, "SendItemToUser SendHeadwear award_center.Award uid:%v err:%v", uid, err)
		return err
	}
	return nil
}

// orderId string, uid, targetUid, conversionId, price, count, status uint32
func (c *ConversionTcc) UpdateOrderStatus(ctx context.Context, order *mysql.DBOrder, nowTs time.Time) error {
	err := c.db.UpdateOrderStatus(ctx, order, nowTs)
	return err
}

func (c *ConversionTcc) SendGetGiftTTMsg(ctx context.Context, sendUid uint32, order *mysql.DBOrder) error {

	log.DebugfWithCtx(ctx, "[ConversionTcc] SendGetGiftTTMsg sendUid:%v", sendUid)

	giftName, _, day := c.GetGiftInfo(ctx, order)

	content := fmt.Sprintf("我已经收到你赠送的【%s%d天】~谢谢你", giftName, day)

	msg := new(apiPB.ImMsg)
	msg.ImType = &apiPB.ImType{
		SenderType:   uint32(apiPB.IM_SENDER_TYPE_IM_SENDER_NORMAL),
		ReceiverType: uint32(apiPB.IM_RECEIVER_TYPE_IM_RECEIVER_USER),
	}

	msg.Platform = apiPB.Platform_UNSPECIFIED
	msg.AppPlatform = "all"
	msg.BPresentBoth = true
	msg.FromUid = order.TargetUid // TT语音助手
	msg.ToIdList = []uint32{order.Uid}
	msg.ImContent = &apiPB.ImContent{}
	msg.ImContent.TextHlUrl = &apiPB.ImTextWithHighlightUrl{
		Content:    content,
		Hightlight: "",
		Url:        "",
	}
	msg.ImType.ContentType = uint32(apiPB.IM_CONTENT_TYPE_IM_CONTENT_TEXT_WITH_HL_URL)

	err := c.ApiClient.SendImMsg(
		context.Background(), order.TargetUid, protocol.TT, []*apiPB.ImMsg{msg}, true)

	if err != nil {
		log.ErrorWithCtx(ctx, "ConversionTcc SendGetGiftTTMsg get fail uid:%u err:%v", sendUid, err)
	}

	notify.NotifySync(order.TargetUid, notify.ImMsgV2)
	notify.NotifySync(order.Uid, notify.ImMsgV2)

	return nil
}

func (c *ConversionTcc) GetGiftInfo(ctx context.Context, order *mysql.DBOrder) (giftName string, giftType string, day uint32) {

	giftType = ""

	conversionId := order.ConversionId
	conversion := &mysql.DBConversionConfig{}
	configErr := c.db.GetConversionConfigById(context.Background(), conversionId, conversion)
	if configErr != nil {
		log.ErrorWithCtx(ctx, "GetGiftInfo GetConversionConfigById targetUid:%v err:%v", order.TargetUid, configErr)
		return giftName, giftType, 0
	}

	if conversion.ConversionType == uint32(pb.ConversionType_Mic_Style) {
		giftType = "麦位框"

	} else {
		if conversion.ConversionType == uint32(pb.ConversionType_Horse_Type) {
			giftType = "坐骑"
		}
	}

	giftName = conversion.GiftName

	log.DebugfWithCtx(ctx, "GetGiftInfo conversion:%v", conversion)

	return giftName, giftType, conversion.HoldDay
}

func (c *ConversionTcc) SendTTMsg(ctx context.Context, uid uint32, order *mysql.DBOrder) error {

	targetUid := order.TargetUid

	resp, err := c.accountClient.GetUserByUid(context.Background(), targetUid)

	if err != nil {
		log.ErrorWithCtx(ctx, "ConversionTcc SendTTMsg GetUserByUid fail targetUid:%v err:%v", targetUid, err)
		return err
	}

	nickName := resp.GetNickname()

	giftName, giftType, day := c.GetGiftInfo(ctx, order)

	content := fmt.Sprintf("由于长时间未领取，赠送给【%s】 的 %s 【%s %d】已退回", nickName, giftType, giftName, day)

	msg := new(apiPB.ImMsg)
	msg.ImType = &apiPB.ImType{
		SenderType:   uint32(apiPB.IM_SENDER_TYPE_IM_SENDER_NORMAL),
		ReceiverType: uint32(apiPB.IM_RECEIVER_TYPE_IM_RECEIVER_USER),
	}
	msg.FromUid = 10000 // TT语音助手
	msg.ToIdList = []uint32{uid}
	msg.ImContent = &apiPB.ImContent{}
	msg.ImContent.TextHlUrl = &apiPB.ImTextWithHighlightUrl{
		Content:    content,
		Hightlight: "",
		Url:        "",
	}
	msg.ImType.ContentType = uint32(apiPB.IM_CONTENT_TYPE_IM_CONTENT_TEXT_WITH_HL_URL)

	err = c.ApiClient.SendImMsg(
		context.Background(), uid, protocol.TT, []*apiPB.ImMsg{msg}, true)

	if err != nil {
		log.ErrorWithCtx(ctx, "ConversionTcc SendTTMsg get fail uid:%u err:%v", uid, err)
	}

	notify.NotifySync(order.TargetUid, notify.ImMsgV2)
	notify.NotifySync(order.Uid, notify.ImMsgV2)

	return nil
}

func (c *ConversionTcc) HandlerOrderStatus() {

	ctx, cancel := context.WithTimeout(context.Background(), time.Minute*5)
	defer cancel()
	if _, isLock := c.redis.Lock(ConversionLockKey, time.Minute*5); !isLock {
		log.DebugWithCtx(ctx, "HandlerOutDateOrders GetOutDateOrders get lock fail")
		return
	}

	defer c.redis.UnLock(ConversionLockKey)

	nowTs := time.Now()
	if nowTs.Hour() >= 4 && nowTs.Hour() <= 6 {
		//删除已经完成的并且时间超过xx天的订单
		if _, isLock := c.redis.Lock(DelMonthsAgoOrderKey, time.Hour*24); isLock {
			go c.db.DelFinishorders()
		}
	}

	go c.TimerDoRollbackPrepareErrOrder()

	//取30M前兑换，但是状态还没有完结的订单
	var err error
	orderList := make([]*mysql.DBOrder, 0, PageCount)
	c.BeginTs, c.Off, err = c.db.GetOutDateOrders(ctx, c.BeginTs, c.Off, PageCount, &orderList)
	if err != nil {
		log.ErrorWithCtx(ctx, "HandlerOutDateOrders GetOutDateOrders err:%v", err)
		return
	}

	log.InfoWithCtx(ctx, "HandlerOutDateOrders GetOutDateOrders sz:%v", len(orderList))

	for _, order := range orderList {
		uid := order.Uid
		orderId := order.OrderId
		updateAt := order.UpdateAt
		conversionId := order.ConversionId
		targetUid := uid
		count := order.Count
		if order.TargetUid > 0 {
			targetUid = order.TargetUid
		}

		conversion := &mysql.DBConversionConfig{}
		err = c.db.GetConversionConfigById(ctx, conversionId, conversion)
		if err != nil {
			log.ErrorWithCtx(ctx, "HandlerOutDateOrders GetConversionConfigById err:%v", err)
			continue
		}

		//如果是送给好友的订单
		if targetUid != uid {
			if order.Status == uint32(pb.OrderStatus_Prepare_Type) { //好友没有领取
				if (nowTs.Unix() - int64(updateAt)) >= conf.RollBackSec()+60 { //已经到了回滚的时间，但是状态还是预扣状态，发起回退碎片
					//收礼者还没有点击领取操作，进行回退操作.如果已经有领取的操作，则进行发奖重试。
					err = c.RollBack(orderId, uid, time.Unix(int64(order.CreateAt), 0))
					if err != nil {
						log.ErrorWithCtx(ctx, "HandlerOutDateOrders RollBack orderId:%v uid:%v targetUid:%v err:%v", orderId, uid, targetUid, err)
					} else {
						order.Status = uint32(pb.OrderStatus_RollBack_Type)
						err = c.UpdateOrderStatus(ctx, order, nowTs)
						log.InfoWithCtx(ctx, "HandlerOutDateOrders RollBack success orderId:%v uid:%v targetUid:%v updateAt:%v, err:%v", orderId, uid, targetUid, updateAt, err)
						if err == nil {
							c.SendTTMsg(ctx, uid, order)
						}
					}
				}
				//好友还没有领取操作，必须continue不能发货
				continue
			}
		} else { //如果是兑换订单
			//时间超过12小时
			if (nowTs.Unix() - int64(updateAt)) >= RoolBackSec {
				//发送礼物成功，但是更新订单到commit状态失败?手动处理吧
				log.ErrorWithCtx(ctx, "HandlerOutDateOrders Warning SendItemToUser fail orderId:%v conversionId:%v uid:%v", orderId, conversionId, uid)
				continue
			}
		}
		//尝试再次发送物品，调的服务应该是幂等接口
		err = c.SendItemToUser(ctx, orderId, targetUid, pb.ConversionType(conversion.ConversionType), conversion.GiftId,
			conversion.GiftNum*count, conversion.HoldDay, order.Price, time.Unix(int64(order.CreateAt), 0))
		if err != nil {
			log.Errorf("HandlerOutDateOrders SendItemToUser orderId:%v uid:%v targetUid:%v err:%v", orderId, uid, targetUid, err)
			continue
		}

		//兑换单的commit完结状态
		newStatus := uint32(pb.OrderStatus_Commit_Type)
		if targetUid != uid {
			//领取单的GetGift完结状态
			newStatus = uint32(pb.OrderStatus_GetGift_Type)
		}
		order.Status = newStatus
		err = c.UpdateOrderStatus(ctx, order, nowTs)
		if err != nil {
			log.Errorf("HandlerOutDateOrders UpdateOrderStatus orderId:%v uid:%v err:%v", orderId, uid, err)
			continue
		}

		//如果是送礼订单，发送领礼物取消息给送礼人
		if targetUid != uid {
			c.SendGetGiftTTMsg(ctx, order.TargetUid, order)
		}
		log.InfoWithCtx(ctx, "HandlerOutDateOrders Commit success orderId:%v uid:%v targetUid:%v", orderId, uid, targetUid)
	}
}

// 定时发送兑换礼物价值消息
func (c *ConversionTcc) HandlerNotifyMessage() {
	t := time.Now()
	strTime := t.Format("2006-01-02 15:04:05")
	strHour := GetHourKey(t.Add(-3600 * 1e9))
	err, hourVal := c.redis.GetKeyValue(strHour)
	reporter := report.NewReporter(c.reportLink, c.reportSecret)
	if err == nil && hourVal != -1 {
		msg := fmt.Sprintf("%v 小时兑换礼物价值:%0.2f元", strTime, float32(hourVal)/100.0)
		err := reporter.SendReport("兑换礼物价值小时数据", msg)
		c.redis.SetKeyValue(strHour, -1, time.Second*3600*24)
		log.Errorf(fmt.Sprintf("HandlerNotifyMessage hour strHour:%v msg:%v err:%v", strHour, msg, err))
	}

	strDay := GetDayKey(t.AddDate(0, 0, -1))
	err, dayVal := c.redis.GetKeyValue(strDay)
	if err == nil && dayVal != -1 {
		msg := fmt.Sprintf("%v 每天兑换礼物价值:%0.2f元", strTime, float32(dayVal)/100.0)
		err := reporter.SendReport("兑换礼物价值天数据", msg)
		c.redis.SetKeyValue(strDay, -1, time.Second*3600*24)
		log.Errorf(fmt.Sprintf("HandlerNotifyMessage day strDay:%v msg:%v err:%v", strDay, msg, err))
	}
}

// 发放物品接口
func (c *ConversionTcc) SendItemToUser(ctx context.Context, orderId string, targetUid uint32, ct pb.ConversionType, itemId string, itemNum, holdDay, price uint32, nowTs time.Time) error {
	switch ct {
	case pb.ConversionType_Gift_Type:
		giftId, err := strconv.Atoi(itemId)
		if err != nil {
			log.Errorf("[ConversionTcc] SendItemToUser Atoi fail itemId:%v", itemId)
			return err
		}
		err = c.SendPresent(ctx, orderId, targetUid, uint32(giftId), itemNum, nowTs)
		if err != nil {
			return err
		}
		//统计礼物价值

		log.DebugfWithCtx(ctx, "[ConversionTcc] SendPresent success targetUid:%v itemId:%v", targetUid, itemId)

		err = c.RecordconversionVal(ctx, int64(price))
		if err != nil {
			log.Errorf("[ConversionTcc] RecordconversionVal err:%v", err)
		}
		return nil
	case pb.ConversionType_Horse_Type:
		return c.SendHorse(ctx, orderId, targetUid, itemId, holdDay, itemNum, nowTs)
	case pb.ConversionType_Mic_Style:
		/*suiteId, err := strconv.Atoi(itemId)
		if err != nil {
			log.Errorf("SendItemToUser headwear Atoi fail targetUid:%v err:%v", targetUid, err)
			return err
		}*/
		return c.SendHeadwear(ctx, orderId, targetUid, itemId, holdDay, nowTs)
	default:
		log.ErrorWithCtx(ctx, "invalid conversion type:%v", ct)
	}

	log.InfoWithCtx(ctx, "SendItemToUser success targetUid:%v orderId:%v cType:%v itemId:%v itemNum:%v", targetUid, orderId, ct, itemId, itemNum)

	return nil
}

func (c *ConversionTcc) RecordconversionVal(ctx context.Context, val int64) error {

	t := time.Now()
	log.DebugfWithCtx(ctx, fmt.Sprintf("RecordconversionVal begin val:%v str:%v", val, GetHourKey(t)))

	err, newVal := c.redis.IncrVal(GetHourKey(t), val)
	if err != nil {
		log.Errorf(fmt.Sprintf("RecordconversionVal hour err:%v", err))
		return err
	}

	log.DebugfWithCtx(ctx, fmt.Sprintf("RecordconversionVal set err:%v newVal:%v", err, newVal))

	err, _ = c.redis.IncrVal(GetMonthKey(t), val)
	if err != nil {
		log.Errorf(fmt.Sprintf("RecordconversionVal month err:%v", err))
		return err
	}
	err, _ = c.redis.IncrVal(GetDayKey(t), val)
	if err != nil {
		log.Errorf(fmt.Sprintf("RecordconversionVal day err:%v", err))
		return err
	}

	log.DebugfWithCtx(ctx, fmt.Sprintf("RecordconversionVal end val:%v", val))

	return nil
}

func (c *ConversionTcc) GenConversionOrderID(uid uint32) string {
	//r := rand.New(rand.NewSource(99))
	nano := time.Now().UnixNano()

	if c.nodeId == 0 {
		nodeId, err := c.redis.IncrNodeID(ConversionNodeIdKey)
		if err != nil {
			log.Errorf("GenConversionOrderID IncrNodeID err:%v", err)
			//nodeId = int64(rand.Int31())
		}
		c.nodeId = nodeId
	}
	orderId := fmt.Sprintf("%v_coid_%v_%v_%v_%v", conf.GetBusinessID(), uid, c.nodeId, nano, c.seq)
	c.seq = (c.seq + 1) % ConversionMaxSeq

	return orderId
}

func (c *ConversionTcc) GetConversionConfigById(conversionId uint32, config *mysql.DBConversionConfig) error {
	err := c.db.GetConversionConfigById(context.Background(), conversionId, config)
	return err
}

func GetHourKey(t time.Time) string {
	str := fmt.Sprintf("conversion-%v-%v-%v-%v", t.Year(), t.Month(), t.Day(), t.Hour())
	return str
}

func GetMonthKey(t time.Time) string {
	str := fmt.Sprintf("conversion-%v-%v", t.Year(), t.Month())
	return str
}

func GetDayKey(t time.Time) string {
	str := fmt.Sprintf("conversion-%v-%v-%v", t.Year(), t.Month(), t.Day())
	return str
}

// 定时从待回滚列表取出订单回滚
func (s *ConversionTcc) TimerDoRollbackPrepareErrOrder() {
	ctx, cancel := context.WithTimeout(context.Background(), time.Minute*2)
	defer cancel()
	allRollbackOrders, err := s.redis.GetAllHashInfo(PrepareErrOrderKeys).Result()
	if err != nil {
		log.ErrorWithCtx(ctx, "TimerDoRollbackPrepareErrOrder failed err:%v", err)
		return
	}
	log.InfoWithCtx(ctx, "TimerDoRollbackPrepareErrOrder total :%d", len(allRollbackOrders))
	now := uint32(time.Now().Unix())
	for orderId, val := range allRollbackOrders {
		arr := strings.Split(val, "_")
		uid, _ := strconv.Atoi(arr[0])
		ct, _ := strconv.Atoi(arr[1])
		if now <= uint32(ct) {
			continue
		}
		if tc := now - uint32(ct); tc < 120 { //间隔两分钟时间再回滚
			continue
		}
		err := s.RollBack(orderId, uint32(uid), time.Unix(int64(ct), 0))
		if err != nil {
			log.ErrorWithCtx(ctx, "TimerDoRollbackPrepareErrOrder failed orderid:%s, %s, err:%v", orderId, val, err)
			continue
		}
		s.redis.HDel(PrepareErrOrderKeys, orderId)
		log.InfoWithCtx(ctx, "TimerDoRollbackPrepareErrOrder ok orderid:%s, %s", orderId, val)
	}
}
