package server

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"golang.52tt.com/pkg/log"
	reconcile_v2 "golang.52tt.com/protocol/services/reconcile-v2"
	"golang.52tt.com/services/conversion/mysql"
	"time"
)

func getOrderInfoKey(orderId string) string {
	return "conversion_order_info_" + orderId
}

func (s *conversionServerServer) SetOrderInfo(orderId string, info *mysql.DBOrder) error {
	str, err := json.Marshal(info)
	if nil != err {
		return err
	}
	return s.redis.SetInfo(getOrderInfoKey(orderId), string(str), time.Hour*24*5)
}

func (s *conversionServerServer) GetOrderInfo(orderId string) (*mysql.DBOrder, error) {
	str, err := s.redis.GetInfo(getOrderInfoKey(orderId))
	if nil != err {
		return nil, err
	}
	info := &mysql.DBOrder{}
	err = json.Unmarshal([]byte(str), info)
	return info, err
}

func (s *conversionServerServer) ReplaceConversionOrder(ctx context.Context, in *reconcile_v2.ReplaceOrderReq) (out *reconcile_v2.EmptyResp, err error) {
	out = &reconcile_v2.EmptyResp{}
	orderInfo, err := s.GetOrderInfo(in.GetOrderId())
	if nil != err {
		log.ErrorWithCtx(ctx, "ReplaceConversionOrder orderId:%v err:%v", in.GetOrderId(), err)
		return out, err
	}
	if orderInfo.OrderId != in.GetOrderId() {
		log.ErrorWithCtx(ctx, "ReplaceConversionOrder orderId:%v not found", in.GetOrderId())
		return out, errors.New("order not found")
	}
	err = s.db.UpdateOrderStatus(ctx, orderInfo, time.Unix(int64(orderInfo.CreateAt), 0))

	log.InfoWithCtx(ctx, "ReplaceConversionOrder success orderInfo:%+v", orderInfo)

	return out, err
}

const (
	PrepareErrOrderKeys = "conversion_prepare_err_rollbacks"
)

//回滚.ttc.Prepare返回错误的单。因为可能只是网络错误或超时，导致的接口报错，下游背包已执行
func (s *conversionServerServer) RollbackPrepareErrOrder(order *mysql.DBOrder) {
	ctx, cancel := context.WithTimeout(context.Background(), time.Minute*2)
	defer cancel()

	//加到待回滚列表中去回滚，等一些时间再回滚
	log.InfoWithCtx(ctx, "add RollbackPrepareErrOrder rollback order:%s", order.OrderId)
	val := fmt.Sprintf("%d_%d", order.Uid, order.CreateAt)
	err := s.redis.SetHashInfo(PrepareErrOrderKeys, order.OrderId, val)
	if err != nil {
		log.ErrorWithCtx(ctx, "add RollbackPrepareErrOrder rollback failed order:%s, err :%v", order.OrderId, err)
	}
}
