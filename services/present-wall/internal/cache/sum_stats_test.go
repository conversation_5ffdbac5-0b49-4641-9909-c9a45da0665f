package cache

import (
	"context"
	"fmt"
	"gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/redis"
	"testing"
	"time"
)

func TestCache_AddEmptySumStats(t *testing.T) {
	ctx := context.Background()
	miniRedis, rdb := GetRedis(t)
	defer miniRedis.Close()

	type fields struct {
		cmder redis.Cmdable
	}
	type args struct {
		ctx context.Context
		uid uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "测试添加用户空的冠名信息",
			fields: fields{
				cmder: rdb,
			},
			args: args{
				ctx: ctx,
				uid: 123,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &Cache{
				cmder: tt.fields.cmder,
			}
			if err := c.AddEmptySumStats(tt.args.ctx, tt.args.uid); (err != nil) != tt.wantErr {
				t.Errorf("AddEmptySumStats() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestCache_AddSumStats(t *testing.T) {
	ctx := context.Background()
	miniRedis, rdb := GetRedis(t)
	defer miniRedis.Close()

	type fields struct {
		cmder redis.Cmdable
	}
	type args struct {
		ctx       context.Context
		uid       uint32
		itemField string
		itemCount uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "测试添加用户冠名信息",
			fields: fields{
				cmder: rdb,
			},
			args: args{
				ctx:       ctx,
				uid:       123,
				itemField: "1",
				itemCount: 1,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &Cache{
				cmder: tt.fields.cmder,
			}
			if err := c.AddSumStats(tt.args.ctx, tt.args.uid, tt.args.itemField, tt.args.itemCount); (err != nil) != tt.wantErr {
				t.Errorf("AddSumStats() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestCache_BatchSetSumStats(t *testing.T) {
	ctx := context.Background()
	miniRedis, rdb := GetRedis(t)
	defer miniRedis.Close()

	type fields struct {
		cmder redis.Cmdable
	}
	type args struct {
		ctx      context.Context
		uid      uint32
		countMap map[string]uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			// 测试批量添加用户冠名信息
			name: "测试批量添加用户冠名信息",
			fields: fields{
				cmder: rdb,
			},
			args: args{
				ctx: ctx,
				uid: 123,
				countMap: map[string]uint32{
					"1": 1,
					"2": 2,
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &Cache{
				cmder: tt.fields.cmder,
			}
			if err := c.BatchSetSumStats(tt.args.ctx, tt.args.uid, tt.args.countMap); (err != nil) != tt.wantErr {
				t.Errorf("BatchSetSumStats() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestCache_DelSumStats(t *testing.T) {
	ctx := context.Background()
	miniRedis, rdb := GetRedis(t)
	defer miniRedis.Close()

	type fields struct {
		cmder redis.Cmdable
	}
	type args struct {
		ctx context.Context
		uid uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			// 测试删除用户冠名信息
			name: "测试删除用户冠名信息",
			fields: fields{
				cmder: rdb,
			},
			args: args{
				ctx: ctx,
				uid: 123,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &Cache{
				cmder: tt.fields.cmder,
			}
			if err := c.DelSumStats(tt.args.ctx, tt.args.uid); (err != nil) != tt.wantErr {
				t.Errorf("DelSumStats() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestCache_GetSumStats(t *testing.T) {
	ctx := context.Background()
	miniRedis, rdb := GetRedis(t)
	defer miniRedis.Close()

	type fields struct {
		cmder redis.Cmdable
	}
	type args struct {
		ctx context.Context
		uid uint32
	}
	tests := []struct {
		name     string
		fields   fields
		args     args
		wantResp map[uint32]uint32
		wantErr  bool
	}{
		{
			// 测试获取用户冠名信息
			name: "测试获取用户冠名信息",
			fields: fields{
				cmder: rdb,
			},
			args: args{
				ctx: ctx,
				uid: 123,
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &Cache{
				cmder: tt.fields.cmder,
			}
			_, err := c.GetSumStats(tt.args.ctx, tt.args.uid)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetSumStats() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}

func TestCache_SetSumStats(t *testing.T) {
	ctx := context.Background()
	miniRedis, rdb := GetRedis(t)
	defer miniRedis.Close()

	type fields struct {
		cmder redis.Cmdable
	}
	type args struct {
		ctx       context.Context
		uid       uint32
		itemField string
		itemCount uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			// 测试添加用户冠名信息
			name: "测试添加用户冠名信息",
			fields: fields{
				cmder: rdb,
			},
			args: args{
				ctx:       ctx,
				uid:       123,
				itemField: "1",
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &Cache{
				cmder: tt.fields.cmder,
			}
			if err := c.SetSumStats(tt.args.ctx, tt.args.uid, tt.args.itemField, tt.args.itemCount); (err != nil) != tt.wantErr {
				t.Errorf("SetSumStats() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func Test_getSumStatsKey(t *testing.T) {
	//miniRedis, rdb := GetRedis(t)
	//defer miniRedis.Close()

	type args struct {
		uid uint32
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{
			// 测试获取用户冠名信息key
			name: "测试获取用户冠名信息key",
			args: args{
				uid: 123,
			},
			want: fmt.Sprintf("%s_%s_%d", SumStatsKeyPrefix, time.Now().Add(-ThreeHour).Format("20060102"), 123),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := getSumStatsKey(tt.args.uid); got != tt.want {
				t.Errorf("getSumStatsKey() = %v, want %v", got, tt.want)
			}
		})
	}
}
