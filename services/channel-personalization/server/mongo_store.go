package server

import (
	"context"
	"errors"
	"fmt"
	"strconv"
	"time"

	"github.com/globalsign/mgo"
	"github.com/globalsign/mgo/bson"
	"golang.52tt.com/pkg/log"

	"golang.52tt.com/pkg/config"

	pb "golang.52tt.com/protocol/services/channelpersonalization"
	special_effect "golang.52tt.com/services/channel-personalization/server/special-effect"
)

const (
	database                  = "channel_personalization"
	userDecorationC           = "user_decorations"
	userActivateDecorationC   = "user_activated_decoration"
	orderIdC                  = "order_id"
	DecorationConfigC         = "decoration_config"
	userDecorationCustomTextC = "user_decorations_custom_text"
)

var (
	errInvalidEffectDuration = errors.New("effective-end should be less than effective-begin")
	nobilityToDesc           = map[uint32]string{
		7: "亲王专属",
		8: "国王专属",
		9: "神王专属",
	}

	UpdateStatusFinished UpdateStatus = 0
	UpdateStatusUpdating UpdateStatus = 1
)

type UpdateStatus uint32

type M = bson.M

type specialEffect struct {
	ID                string `json:"id" bson:"id"`
	Name              string `json:"name" bson:"name"`
	Version           string `json:"version" bson:"version"`
	RichLevel         uint32 `json:"rich_level" bson:"rich_level"`         //	level, 0表示非等级坐骑
	EffectiveDays     uint32 `json:"effective_days" bson:"effective_days"` // days, 0表示永久有效
	NobilityLevel     uint32 `json:"nobility_level" bson:"nobility_level"`
	NobilityText      string `json:"nobility_text" bson:"nobility_text"`
	SourceUrl         string `json:"source_url" bson:"source_url"`
	PreviewUrl        string `json:"preview_url" bson:"preview_url"`
	OperatorInfo      string `json:"operator_info" bson:"operator_info"`
	Notice            string `json:"notice" bson:"notice"`
	AwardTime         uint32 `json:"award_time" bson:"award_time"`
	EnterEffectType   uint32 `json:"enter_effect_type" bson:"enter_effect_type"`
	UpdateStatus      uint32 `json:"update_status" bson:"update_status"`
	CreateTime        uint32 `json:"create_time" bson:"create_time"`
	UpdateTime        uint32 `json:"update_time" bson:"update_time"`
	CustomType        uint32 `json:"custom_type" bson:"custom_type"`
	CustomColor       string `json:"custom_color" bson:"custom_color"`
	EffectSourceType  uint32 `json:"effect_source_type" bson:"effect_source_type"`
	EffectSpecialType uint32 `json:"effect_special_type" bson:"effect_special_type"`
}

type specialEffectNoCreateTime struct {
	ID                string `json:"id" bson:"id"`
	Name              string `json:"name" bson:"name"`
	Version           string `json:"version" bson:"version"`
	RichLevel         uint32 `json:"rich_level" bson:"rich_level"`         //	level, 0表示非等级坐骑
	EffectiveDays     uint32 `json:"effective_days" bson:"effective_days"` // days, 0表示永久有效
	NobilityLevel     uint32 `json:"nobility_level" bson:"nobility_level"`
	NobilityText      string `json:"nobility_text" bson:"nobility_text"`
	SourceUrl         string `json:"source_url" bson:"source_url"`
	PreviewUrl        string `json:"preview_url" bson:"preview_url"`
	OperatorInfo      string `json:"operator_info" bson:"operator_info"`
	Notice            string `json:"notice" bson:"notice"`
	AwardTime         uint32 `json:"award_time" bson:"award_time"`
	EnterEffectType   uint32 `json:"enter_effect_type" bson:"enter_effect_type"`
	UpdateStatus      uint32 `json:"update_status" bson:"update_status"`
	UpdateTime        uint32 `json:"update_time" bson:"update_time"`
	CustomType        uint32 `json:"custom_type" bson:"custom_type"`
	CustomColor       string `json:"custom_color" bson:"custom_color"`
	EffectSourceType  uint32 `json:"effect_source_type" bson:"effect_source_type"`
	EffectSpecialType uint32 `json:"effect_special_type" bson:"effect_special_type"`
}

type userDecoration struct {
	pb.UserDecoration `bson:",inline"`
	TTL               int64 `bson:"ttl"`
}

type order struct {
	PrimaryKey  string    `bson:"_id"`
	TTL         time.Time `bson:"ttl"`
	OutsideTime int64     `bson:"outside_time"`
	SourceType  uint32    `bson:"source_type"`
}

type DecorationConfig struct {
	PrimaryKey    string `bson:"_id"`
	specialEffect `bson:",inline"`
	Typ           pb.DecorationType `bson:"typ"`
}

type DecorationConfigNoCreateTime struct {
	PrimaryKey                string `bson:"_id"`
	specialEffectNoCreateTime `bson:",inline"`
	Typ                       pb.DecorationType `bson:"typ"`
}

func (ud *userDecoration) effective(now time.Time) bool {
	if ud.TTL > 0 {
		ud.EffectEnd = ud.EffectBegin + uint64(ud.TTL)
	}
	return ud.EffectBegin <= uint64(now.Unix()) && ud.EffectEnd >= uint64(now.Unix())
}

func (ud *userDecoration) expired(now time.Time) bool {
	if ud.TTL > 0 {
		ud.EffectEnd = ud.EffectBegin + uint64(ud.TTL)
	}
	return ud.EffectEnd <= uint64(now.Unix())
}

type mongoStore struct {
	session *mgo.Session
}

func newMongoStore(ctx context.Context, cfg *config.MongoConfig) (*mongoStore, error) {
	bson.SetJSONTagFallback(true)

	cs := cfg.URI()
	s, err := mgo.DialWithTimeout(cs, time.Second*3)
	if err != nil {
		log.ErrorWithCtx(ctx, "mgo.DialWithTimeout %s failed with error: %v", cs, err)
		return nil, err
	}
	s.SetPoolLimit(cfg.MaxPoolSize)
	s.SetMode(mgo.Nearest, true)
	s.SetSafe(&mgo.Safe{})
	log.DebugWithCtx(ctx, "connected to %s", cs)

	ms := &mongoStore{
		session: s,
	}

	err = ms.ensureIndexesForUserDecorations(ctx)
	if err != nil {
		return nil, err
	}

	return ms, nil
}

func (s *mongoStore) ensureIndexesForUserDecorations(ctx context.Context) error {
	c := s.session.DB(database).C(userDecorationC)
	log.DebugWithCtx(ctx, "Ensure indexes for collection `%s`", c.FullName)

	err := c.EnsureIndex(mgo.Index{
		Key:    []string{"uid", "decoration.type", "decoration.id", "custom_text", "fusion_ttid"},
		Unique: true,
	})
	if err != nil {
		return err
	}

	err = c.EnsureIndex(mgo.Index{Key: []string{"-grant_at"}})
	if err != nil {
		return err
	}

	err = c.EnsureIndex(mgo.Index{Key: []string{"effective_end"}, ExpireAfter: time.Hour})
	if err != nil {
		return err
	}
	return nil
}

func (s *mongoStore) GetUserDecorations(ctx context.Context, userID uint32, decorationType pb.DecorationType) ([]*pb.UserDecoration, error) {
	sess := s.session.Clone()
	defer sess.Close()

	var uds []*userDecoration
	c := sess.DB(database).C(userDecorationC)
	//err := c.Find(M{"uid": userID, "decoration.type": decorationType}).Sort("-grant_at").All(&out)
	//if err != nil {
	//	return nil, err
	//}

	//NOTE 利用mongo的ExpireAfter不能写入int,要用Date类型, 这里是临时方案过滤掉过过期的
	err := c.Find(M{
		"uid":             userID,
		"decoration.type": decorationType,
		// "effect_end":      M{"$gt": time.Now().Unix()},
	}).Sort("-grant_at").All(&uds)

	if err != nil {
		return nil, err
	}
	//END 临时方案
	now := time.Now()
	out := make([]*pb.UserDecoration, 0, len(uds))
	for _, ud := range uds {
		if ud.effective(now) {
			out = append(out, &ud.UserDecoration)
		}
	}

	return out, nil
}

func (s *mongoStore) GetUserDecoration(ctx context.Context, userID uint32, dt pb.DecorationType, id string) (*userDecoration, error) {
	sess := s.session.Clone()
	defer sess.Close()

	selector := M{
		"uid":             userID,
		"decoration.type": dt,
		"decoration.id":   id,
	}

	var ud userDecoration
	c := sess.DB(database).C(userDecorationC)
	err := c.Find(selector).One(&ud)
	switch err {
	case nil:
		return &ud, nil
	case mgo.ErrNotFound:
		return nil, nil
	default:
		return nil, err
	}
}

func (s *mongoStore) GetUserDecorationWithText(ctx context.Context, userID uint32, dt pb.DecorationType, id, customText string) (*userDecoration, error) {
	sess := s.session.Clone()
	defer sess.Close()

	selector := M{
		"uid":             userID,
		"decoration.type": dt,
		"decoration.id":   id,
		"custom_text":     customText,
	}

	var ud userDecoration
	c := sess.DB(database).C(userDecorationC)
	err := c.Find(selector).One(&ud)
	switch err {
	case nil:
		return &ud, nil
	case mgo.ErrNotFound:
		return nil, nil
	default:
		return nil, err
	}
}

func (s *mongoStore) SaveUserDecoration(ctx context.Context, d *pb.UserDecoration, addTTLForExisting bool, outsideTime int64, sourceType uint32) (effectEnd int64, err error) {

	sess := s.session.Clone()
	defer sess.Close()

	//没有索引的话先建一个定时过期的索引
	orderCollection := sess.DB(database).C(orderIdC)
	indexes, err := orderCollection.Indexes()
	if len(indexes) == 1 {
		err := orderCollection.EnsureIndex(mgo.Index{Key: []string{"ttl"}, ExpireAfter: time.Hour * 720})
		if err != nil {
			log.ErrorWithCtx(ctx, "SaveUserDecoration EnsureIndex err , in:%+v , err:%+v", d, err)
			return effectEnd, err
		}
	}

	t := time.Now()
	newOrder := &order{PrimaryKey: d.OrderId, TTL: t, OutsideTime: outsideTime, SourceType: sourceType}
	err = orderCollection.Insert(newOrder)
	if mgo.IsDup(err) && d.OrderId != "" {
		//return effectEnd, errGrantingOrderIdDuplicate
		log.ErrorWithCtx(ctx, "SaveUserDecoration errGrantingOrderIdDuplicate ,id %s", d.OrderId)
		return 0, nil
	}

	selector := M{
		"uid":             d.Uid,
		"decoration.type": d.Decoration.Type,
		"decoration.id":   d.Decoration.Id,
	}

	if d.CustomText != "" {
		selector["custom_text"] = d.CustomText
	}

	if d.FusionTtid != "" {
		selector["fusion_ttid"] = d.FusionTtid
	}

	ttl := int64(d.EffectEnd) - int64(d.EffectBegin)
	/*if ttl <= 0 {
		return errInvalidEffectDuration
	}*/

	c := sess.DB(database).C(userDecorationC)

	var ud userDecoration
	err = c.Find(selector).One(&ud)
	isInsert := false
	switch err {
	case nil:
		now := time.Now()
		expired := ud.expired(now)
		log.DebugWithCtx(ctx, "SaveUserDecoration %v %t, already exists: %v expired=%t", d, addTTLForExisting, ud, expired)
		// found
		var update interface{}
		if addTTLForExisting {
			if expired && ttl > 0 {
				// already expired
				update = M{
					"$set": M{
						"effect_begin": now.Unix(),
						"ttl":          ttl,
					},
				}
				effectEnd = now.Unix() + ttl
				isInsert = true
			} else if !expired {
				update = M{
					"$inc": M{"ttl": ttl},
				}
				effectEnd = int64(ud.EffectBegin) + ud.TTL + ttl
			} else {
				// if (expired && ttl <= 0)  do nothing
				effectEnd = int64(ud.EffectBegin) + ud.TTL
				return effectEnd, nil
			}
		} else {
			// replace
			d2 := &userDecoration{UserDecoration: *d, TTL: ttl}
			update = M{
				"$set": d2,
			}

			effectEnd = int64(d.EffectBegin) + ttl
		}
		err = c.Update(selector, update)
		log.DebugWithCtx(ctx, "SaveUserDecoration %v %t: update=%v err=%v", d, addTTLForExisting, update, err)

	case mgo.ErrNotFound:
		d.EffectEnd = 0
		insert := &userDecoration{UserDecoration: *d, TTL: ttl}
		err = c.Insert(insert)
		log.DebugWithCtx(ctx, "SaveUserDecoration %v %t: insert=%v err=%v", d, addTTLForExisting, insert, err)
		//if mgo.IsDup(err) {
		//	// 前面查出来不存在，insert又报dup，一定是并发问题，如果要处理并发，可以加重试逻辑
		//}
		effectEnd = int64(d.EffectBegin) + ttl

		isInsert = true

	default:
		// database error
	}

	if err != nil {
		log.ErrorWithCtx(ctx, "SaveUserDecoration %v failed: %v", d, err)
		return effectEnd, err
	}

	if isInsert {
		_ = s.DelEffectCustomText(ctx, d.GetUid(), d.Decoration.GetId(), d.GetCustomText())
	}

	return effectEnd, nil
}

func (s *mongoStore) userActivatedDecorationKey(uid uint32, decorationType pb.DecorationType) string {
	return fmt.Sprintf("%d-%s", uid, decorationType)
}

func (s *mongoStore) GetUserActivatedDecoration(ctx context.Context, uid uint32, decorationType pb.DecorationType) (string, string, string, error) {
	sess := s.session.Clone()
	defer sess.Close()

	c := sess.DB(database).C(userActivateDecorationC)
	key := s.userActivatedDecorationKey(uid, decorationType)

	var r struct {
		DecorationId string `bson:"decoration_id"`
		FusionTtid   string `bson:"fusion_ttid"`
		CustomText   string `bson:"custom_text"`
	}
	err := c.FindId(key).One(&r)
	if err != nil {
		if err == mgo.ErrNotFound {
			err = nil
		}
		return "", "", "", err
	}

	return r.DecorationId, r.CustomText, r.FusionTtid, nil
}

func (s *mongoStore) ActivateUserDecoration(ctx context.Context, uid uint32, decorationType pb.DecorationType, decorationId string,
	customText string, fusionTtid string) error {
	sess := s.session.Clone()
	defer sess.Close()

	c := sess.DB(database).C(userActivateDecorationC)
	key := s.userActivatedDecorationKey(uid, decorationType)
	update := M{
		"$set": M{
			"decoration_id": decorationId,
			"custom_text":   customText,
			"fusion_ttid":   fusionTtid,
		},
	}
	change, err := c.UpsertId(key, update)
	log.DebugWithCtx(ctx, "ActivateUserDecoration: %s %v change=%v", key, update, change)
	return err
}

func (s *mongoStore) AddUserDecorationConfig(ctx context.Context, decoration *pb.Decoration) error {
	sess := s.session.Clone()
	defer sess.Close()
	c := sess.DB(database).C(DecorationConfigC)
	effect := specialEffect{
		ID:                decoration.GetId(),
		Name:              decoration.GetDetail().GetChannelEnterSpecialEffect().GetName(),
		Version:           "0",
		SourceUrl:         decoration.GetSourceUrl(),
		PreviewUrl:        decoration.GetPreviewUrl(),
		OperatorInfo:      decoration.GetOperatorInfo(),
		Notice:            decoration.GetNotice(),
		AwardTime:         decoration.GetAwardTime(),
		EnterEffectType:   uint32(decoration.GetEnterEffectType()),
		CreateTime:        decoration.GetCreateTime(),
		UpdateTime:        decoration.GetUpdateTime(),
		CustomType:        uint32(decoration.GetCustomType()),
		CustomColor:       decoration.GetCustomColor(),
		EffectSourceType:  uint32(decoration.GetSourceType()),
		EffectSpecialType: uint32(decoration.GetSpecialType()),
	}

	if decoration.Detail.ChannelEnterSpecialEffect.EffectType == pb.EffectType_RICH {
		effect.RichLevel = decoration.GetDetail().GetChannelEnterSpecialEffect().GetMinLevel()
	}

	if decoration.Detail.ChannelEnterSpecialEffect.EffectType == pb.EffectType_NOBILITY {
		effect.NobilityLevel = decoration.GetDetail().GetChannelEnterSpecialEffect().GetMinLevel()
		effect.NobilityText = nobilityToDesc[decoration.GetDetail().GetChannelEnterSpecialEffect().GetMinLevel()]
	}

	newConfig := DecorationConfig{PrimaryKey: decoration.Id, specialEffect: effect, Typ: decoration.Type}
	err := c.Insert(newConfig)
	if mgo.IsDup(err) {
		log.ErrorWithCtx(ctx, "AddUserDecorationConfig: %s %v Duplicate Err", decoration.Id, decoration.Detail)
		return errChannelPslConfigDuplicate
	}

	if err != nil {
		log.ErrorWithCtx(ctx, "UpdateUserDecorationConfig: %s %v Err", decoration.Id, decoration.Detail)
		return errChannelPslConfigError
	}

	log.InfoWithCtx(ctx, "AddUserDecorationConfig: %s %v ", decoration.Id, decoration.Detail)

	return err
}

func (s *mongoStore) UpdateUserDecorationConfig(ctx context.Context, decoration *pb.Decoration) (string, error) {
	sess := s.session.Clone()
	defer sess.Close()

	c := sess.DB(database).C(DecorationConfigC)

	effect := specialEffectNoCreateTime{
		ID:                decoration.GetId(),
		Name:              decoration.GetDetail().GetChannelEnterSpecialEffect().GetName(),
		Version:           decoration.GetVer(),
		SourceUrl:         decoration.GetSourceUrl(),
		PreviewUrl:        decoration.GetPreviewUrl(),
		OperatorInfo:      decoration.GetOperatorInfo(),
		Notice:            decoration.GetNotice(),
		AwardTime:         decoration.GetAwardTime(),
		UpdateTime:        decoration.GetUpdateTime(),
		EnterEffectType:   uint32(decoration.GetEnterEffectType()),
		CustomColor:       decoration.GetCustomColor(),
		CustomType:        uint32(decoration.GetCustomType()),
		EffectSourceType:  uint32(decoration.GetSourceType()),
		EffectSpecialType: uint32(decoration.GetSpecialType()),
	}

	nowVer, _ := strconv.Atoi(decoration.GetVer())
	newVer := strconv.Itoa(nowVer + 1)
	effect.Version = newVer

	if decoration.Detail.ChannelEnterSpecialEffect.EffectType == pb.EffectType_RICH {
		effect.RichLevel = decoration.GetDetail().GetChannelEnterSpecialEffect().GetMinLevel()
	}

	if decoration.Detail.ChannelEnterSpecialEffect.EffectType == pb.EffectType_NOBILITY {
		effect.NobilityLevel = decoration.GetDetail().GetChannelEnterSpecialEffect().GetMinLevel()
		effect.NobilityText = nobilityToDesc[decoration.GetDetail().GetChannelEnterSpecialEffect().GetMinLevel()]
	}

	newConfig := DecorationConfigNoCreateTime{PrimaryKey: decoration.Id, specialEffectNoCreateTime: effect, Typ: decoration.Type}
	bsonConf := bson.M{"$set": newConfig}

	selector := M{
		"_id":     decoration.Id,
		"version": decoration.Ver,
	}

	//update := M{
	//	"$set": M{
	//		"channel_enter_special_effect": decoration.Detail,
	//		"version":                      decoration.Ver,
	//	},
	//}

	err := c.Update(selector, bsonConf)

	if err == mgo.ErrNotFound {
		log.DebugWithCtx(ctx, "UpdateUserDecorationConfig: %s %v Not Exist", decoration.Id, decoration.Detail)
		return newVer, errChannelPslConfigNotExist
	}

	if err != nil {
		log.DebugWithCtx(ctx, "UpdateUserDecorationConfig: %s %v Err", decoration.Id, decoration.Detail)
		return newVer, errChannelPslConfigError
	}

	log.InfoWithCtx(ctx, "UpdateUserDecorationConfig : %+v", decoration)

	return newVer, err
}

func (s *mongoStore) DelUserDecorationConfig(ctx context.Context, id string, typ pb.DecorationType, customText, fusionTtid string) error {
	sess := s.session.Clone()
	defer sess.Close()

	c := sess.DB(database).C(DecorationConfigC)
	selector := M{
		"_id": id,
	}
	err := c.Remove(selector)

	if err == mgo.ErrNotFound {
		log.DebugWithCtx(ctx, "UpdateUserDecorationConfig: %s %s Not Exist", id, typ.String())
		return errChannelPslConfigNotExist
	}

	if err != nil {
		log.DebugWithCtx(ctx, "UpdateUserDecorationConfig: %s %s Err", id, typ.String())
		return errChannelPslConfigError
	}

	log.DebugWithCtx(ctx, "DelUserDecorationConfig: %s %s ", id, typ.String())

	return err
}

func (s *mongoStore) GetAllDecorations(ctx context.Context, cfg special_effect.Config, decorationType pb.DecorationType) error {
	sess := s.session.Clone()
	defer sess.Close()
	c := sess.DB(database).C(DecorationConfigC)
	if decorationType == pb.DecorationType_CHANNEL_ENTER_SPECIAL_EFFECT {
		cfgs := make([]*specialEffect, 0)
		selector := M{
			"typ": int(decorationType),
		}
		err := c.Find(selector).All(&cfgs)

		n := cfg.GetEmptySpecialEffects()
		for _, j := range cfgs {
			n[j.ID] = cfg.GetEmptySpecialEffect()
			n[j.ID].Name = j.Name
			n[j.ID].Version = j.Version
			n[j.ID].RichLevel = j.RichLevel
			n[j.ID].NobilityLevel = j.NobilityLevel
			n[j.ID].NobilityText = j.NobilityText
			n[j.ID].ID = j.ID
			n[j.ID].EffectiveDays = j.EffectiveDays
			n[j.ID].SourceUrl = j.SourceUrl
			n[j.ID].PreviewUrl = j.PreviewUrl
			n[j.ID].EnterEffectType = j.EnterEffectType
			n[j.ID].AwardTime = j.AwardTime
			n[j.ID].Notice = j.Notice
			n[j.ID].OperatorInfo = j.Notice
			n[j.ID].CreateTime = j.CreateTime
			n[j.ID].UpdateTime = j.UpdateTime
			n[j.ID].CustomType = j.CustomType
			n[j.ID].CustomColor = j.CustomColor
			n[j.ID].EffectSourceType = j.EffectSourceType
			n[j.ID].EffectSpecialType = j.EffectSpecialType
		}
		cfg.ChangeCfg(n)
		if err != nil {
			log.DebugWithCtx(ctx, "GetAllDecorations err")
			return err
		}
	}

	return nil
}

func (s *mongoStore) SetUserDecorationSpecialLevel(ctx context.Context, id string, ver string, typ pb.EffectType, level uint32) error {
	sess := s.session.Clone()
	defer sess.Close()

	c := sess.DB(database).C(DecorationConfigC)

	selector := M{
		"_id":     id,
		"version": ver,
	}

	update := M{}

	if typ == pb.EffectType_RICH {
		update = M{
			"$set": M{
				"rich_level": level,
			},
		}
	}

	if typ == pb.EffectType_NOBILITY {
		update = M{
			"$set": M{
				"nobility_level": level,
				"nobility_text":  nobilityToDesc[level],
			},
		}
	}

	err := c.Update(selector, update)

	if err == mgo.ErrNotFound {
		log.DebugWithCtx(ctx, "UpdateUserDecorationConfig: %s %v Not Exist", id, ver)
		return errChannelPslConfigNotExist
	}

	if err != nil {
		log.DebugWithCtx(ctx, "UpdateUserDecorationConfig: %s %v Err", id, ver)
		return errChannelPslConfigError
	}

	return err
}

func (s *mongoStore) DelUserDecorationSpecialLevel(ctx context.Context, typ pb.EffectType, level uint32) error {
	sess := s.session.Clone()
	defer sess.Close()

	c := sess.DB(database).C(DecorationConfigC)

	selector := M{}
	update := M{}

	if typ == pb.EffectType_RICH {
		selector = M{
			"rich_level": level,
		}
		update = M{"$set": M{
			"rich_level": 0,
		}}
	} else {
		if typ == pb.EffectType_NOBILITY {
			selector = M{
				"nobility_level": level,
			}

			update = M{"$set": M{"nobility_level": 0,
				"nobility_text": ""},
			}
		}
	}

	_, err := c.UpdateAll(selector, update)

	if err == mgo.ErrNotFound {
		log.ErrorWithCtx(ctx, "DelUserDecorationSpecialLevel: %s %d Not Exist", typ.String(), level)
		return nil
	}

	if err != nil {
		log.ErrorWithCtx(ctx, "DelUserDecorationSpecialLevel: %s %d Err %v", typ.String(), level, err)
		return nil
	}

	return err
}

func (s *mongoStore) SetUserDecorationUpdateStatus(ctx context.Context, id string, ver string, status uint32) error {
	sess := s.session.Clone()
	defer sess.Close()

	c := sess.DB(database).C(DecorationConfigC)

	selector := M{
		"_id":     id,
		"version": ver,
	}

	update := M{
		"$set": M{
			"update_status": status,
		},
	}

	err := c.Update(selector, update)

	if err == mgo.ErrNotFound {
		log.DebugWithCtx(ctx, "SetUserDecorationUpdateStatus: %s %v Not Exist", id, ver)
		return errChannelPslConfigNotExist
	}

	if err != nil {
		log.DebugWithCtx(ctx, "SetUserDecorationUpdateStatus: %s %v Err", id, ver)
		return errChannelPslConfigError
	}

	return err
}

type failItem struct {
	uid    uint32
	reason string
}

func (s *mongoStore) BatchSaveUserDecoration(ctx context.Context, d *pb.UserDecoration, addTTLForExisting bool, uidList []uint32) ([]*failItem, error) {

	sess := s.session.Clone()
	defer sess.Close()

	failList := make([]*failItem, 0)

	//没有索引的话先建一个定时过期的索引
	orderCollection := sess.DB(database).C(orderIdC)
	indexes, _ := orderCollection.Indexes()
	if len(indexes) == 1 {
		err := orderCollection.EnsureIndex(mgo.Index{Key: []string{"ttl"}, ExpireAfter: time.Hour * 24})
		if err != nil {
			return failList, err
		}
	}

	t := time.Now()
	newOrder := &order{PrimaryKey: d.OrderId, TTL: t}
	err := orderCollection.Insert(newOrder)
	if mgo.IsDup(err) && d.OrderId != "" {
		log.ErrorWithCtx(ctx, "SaveUserDecoration errGrantingOrderIdDuplicate ,id %s", d.OrderId)
		return failList, nil
	}

	selector := M{
		"uid": M{
			"$in": uidList,
		},
		"decoration.type": d.Decoration.Type,
		"decoration.id":   d.Decoration.Id,
	}

	ttl := int64(d.EffectEnd) - int64(d.EffectBegin)
	/*if ttl <= 0 {
		return errInvalidEffectDuration
	}*/

	c := sess.DB(database).C(userDecorationC)

	var ud []*userDecoration
	err = c.Find(selector).All(&ud)

	udMap := make(map[uint32]*userDecoration)
	for _, item := range ud {
		udMap[item.Uid] = item
	}
	for _, uid := range uidList {
		if item, ok := udMap[uid]; ok {
			d.Uid = uid
			now := time.Now()
			expired := item.expired(now)
			log.DebugWithCtx(ctx, "SaveUserDecoration %v %t, already exists: %v expired=%t", d, addTTLForExisting, ud, expired)
			// found
			var update interface{}
			if addTTLForExisting {
				if expired && ttl > 0 {
					// already expired
					update = M{
						"$set": M{
							"effect_begin": now.Unix(),
							"ttl":          ttl,
						},
					}
				} else if !expired {
					update = M{
						"$inc": M{"ttl": ttl},
					}
				} else {
					// if (expired && ttl <= 0)  do nothing
					continue
				}
			} else {
				// replace
				d2 := &userDecoration{UserDecoration: *d, TTL: ttl}
				update = M{
					"$set": d2,
				}
			}

			selector := M{
				"uid":             d.Uid,
				"decoration.type": d.Decoration.Type,
				"decoration.id":   d.Decoration.Id,
			}

			err = c.Update(selector, update)
			log.InfoWithCtx(ctx, "SaveUserDecoration %v %t: update=%v err=%v", d, addTTLForExisting, update, err)
			if err != nil {
				failList = append(failList, &failItem{
					uid:    uid,
					reason: fmt.Sprintf("数据库插入失败：%s", err.Error()),
				})
				continue
			}
		} else {
			d.Uid = uid
			d.EffectEnd = 0
			insert := &userDecoration{UserDecoration: *d, TTL: ttl}
			err = c.Insert(insert)
			log.InfoWithCtx(ctx, "SaveUserDecoration %v %t: insert=%v err=%v", d, addTTLForExisting, insert, err)
			if err != nil {
				failList = append(failList, &failItem{
					uid:    uid,
					reason: fmt.Sprintf("数据库插入失败：%s", err.Error()),
				})
				continue
			}
		}
	}

	if err != nil {
		log.ErrorWithCtx(ctx, "SaveUserDecoration %v failed: %v", d, err)
		return failList, err
	}

	return failList, nil
}

type specialEffectCustomText struct {
	ID                    string `json:"_id" bson:"_id"`
	Uid                   uint32 `json:"uid" bson:"uid"`
	DecorationId          string `json:"decoration_id" bson:"decoration_id"`
	Version               string `json:"version" bson:"version"`
	CustomText            string `json:"custom_text" bson:"custom_text"`
	AwardTime             uint32 `json:"award_time" bson:"award_time"`
	Status                uint32 `json:"status" bson:"status"`
	ShowCustomText        string `json:"show_custom_text" bson:"show_custom_text"`
	CustomTextUnderReview string `json:"custom_text_under_review" bson:"custom_text_under_review"`
	UseChangeCount        uint32 `json:"use_change_count" bson:"use_change_count"`
}

func (s *mongoStore) UpdateEffectCustomText(ctx context.Context, decoration *specialEffectCustomText) (string, error) {
	sess := s.session.Clone()
	defer sess.Close()

	if decoration.ID == "" {
		decoration.ID = bson.NewObjectId().Hex()
	}
	id := decoration.ID

	c := sess.DB(database).C(userDecorationCustomTextC)

	bsonConf := bson.M{"$set": decoration}

	selector := M{
		"uid":           decoration.Uid,
		"decoration_id": decoration.DecorationId,
		"custom_text":   decoration.CustomText,
	}

	info, err := c.Upsert(selector, bsonConf)

	if info != nil && info.UpsertedId != nil {
		id = info.UpsertedId.(string)
	}

	if err != nil {
		log.ErrorWithCtx(ctx, "UpdateEffectCustomText: %v %v Err", decoration, err)
		return id, errChannelPslConfigError
	}

	log.InfoWithCtx(ctx, "UpdateEffectCustomText : %+v", decoration)

	return id, nil
}

func (s *mongoStore) DelEffectCustomText(ctx context.Context, uid uint32, id, customText string) error {
	sess := s.session.Clone()
	defer sess.Close()

	c := sess.DB(database).C(userDecorationCustomTextC)

	selector := M{
		"uid":           uid,
		"decoration_id": id,
		"custom_text":   customText,
	}

	err := c.Remove(selector)

	if err != nil {
		log.ErrorWithCtx(ctx, "DelEffectCustomText: %s %s %v Err", uid, id, err)
		return errChannelPslConfigError
	}

	log.InfoWithCtx(ctx, "DelEffectCustomText : %s %s", uid, id)

	return nil
}

func (s *mongoStore) GetUserDecorationCustomText(ctx context.Context, userID uint32, id, customText string) (*specialEffectCustomText, error) {
	sess := s.session.Clone()
	defer sess.Close()

	selector := M{
		"uid":           userID,
		"decoration_id": id,
		"custom_text":   customText,
	}

	var ud specialEffectCustomText
	c := sess.DB(database).C(userDecorationCustomTextC)
	err := c.Find(selector).One(&ud)
	switch err {
	case nil:
		return &ud, nil
	case mgo.ErrNotFound:
		return nil, nil
	default:
		return nil, err
	}
}

func (s *mongoStore) GetUserDecorationCustomTextByUid(ctx context.Context, userID uint32) ([]*specialEffectCustomText, error) {
	sess := s.session.Clone()
	defer sess.Close()

	selector := M{
		"uid": userID,
	}

	var ud []*specialEffectCustomText
	c := sess.DB(database).C(userDecorationCustomTextC)
	err := c.Find(selector).All(&ud)
	switch err {
	case nil:
		return ud, nil
	case mgo.ErrNotFound:
		return ud, nil
	default:
		return nil, err
	}
}

func (s *mongoStore) GetUserDecorationCustomTextById(ctx context.Context, id string) (*specialEffectCustomText, error) {
	sess := s.session.Clone()
	defer sess.Close()

	selector := M{
		"_id": id,
	}

	var ud specialEffectCustomText
	c := sess.DB(database).C(userDecorationCustomTextC)
	err := c.Find(selector).One(&ud)
	switch err {
	case nil:
		return &ud, nil
	case mgo.ErrNotFound:
		return nil, nil
	default:
		return nil, err
	}
}

func (s *mongoStore) UpdateUserDecorationCustomTextResult(ctx context.Context, id string, ok bool) error {
	sess := s.session.Clone()
	defer sess.Close()

	selector := M{
		"_id": id,
	}

	var ud specialEffectCustomText
	c := sess.DB(database).C(userDecorationCustomTextC)
	err := c.Find(selector).One(&ud)
	if err != nil {
		return err
	}

	if ok {
		err = c.Update(selector, M{
			"$set": M{
				"status":           0,
				"show_custom_text": ud.CustomTextUnderReview,
				"use_change_count": ud.UseChangeCount + 1,
			},
		})
	} else {
		err = c.Update(selector, M{
			"$set": M{
				"status":                   0,
				"custom_text_under_review": "",
			},
		})
	}

	switch err {
	case nil:
		return nil
	case mgo.ErrNotFound:
		return nil
	default:
		return err
	}
}

func (s *mongoStore) GetUserDecorationOrder(ctx context.Context, begin, end int64, sourceType uint32) ([]*order, error) {
	sess := s.session.Clone()
	defer sess.Close()

	selector := M{
		"outside_time": M{"$gte": begin, "$lt": end},
		"source_type":  sourceType,
	}

	list := make([]*order, 0)
	c := sess.DB(database).C(orderIdC)
	err := c.Find(selector).All(&list)
	switch err {
	case nil:
		return list, nil
	case mgo.ErrNotFound:
		return list, nil
	default:
		return list, err
	}
}
