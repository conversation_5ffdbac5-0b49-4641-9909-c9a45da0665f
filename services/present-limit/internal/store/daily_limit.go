package store

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"github.com/jmoiron/sqlx"
	"strings"
	"time"
)

// createTableSQL 是一个模板，用于创建按月分表的表结构。
const createPresentLimitTableSQL = `
CREATE TABLE IF NOT EXISTS %s (
    send_uid BIGINT UNSIGNED NOT NULL COMMENT '送礼用户ID',
    target_uid BIGINT UNSIGNED NOT NULL COMMENT '收礼用户ID',
    total_price BIGINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '当日总送礼价值（分）',
    date DATE NOT NULL COMMENT '记录日期',
    PRIMARY KEY (send_uid, target_uid, date),
    INDEX idx_target_date (target_uid, date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户每日送礼记录表';
`

// UserGiftLog 对应数据库中的单条记录
type UserGiftLog struct {
	SendUid    int64  `db:"send_uid"`
	TargetUid  int64  `db:"target_uid"`
	TotalPrice int64  `db:"total_price"`
	Date       string `db:"date"` // 使用 YYYY-MM-DD 格式的字符串，与 MySQL 的 DATE 类型直接对应
}

// tableName 根据时间生成动态表名，格式为：user_gift_log_YYYYMM
func (s *Store) tableName(t time.Time) string {
	return fmt.Sprintf("user_gift_log_%s", t.Format("200601"))
}

// CreateTableForMonth 会为指定的月份创建表（如果尚不存在）
func (s *Store) CreateTableForMonth(ctx context.Context, t time.Time) error {
	tableName := s.tableName(t)
	query := fmt.Sprintf(createPresentLimitTableSQL, tableName)
	_, err := s.db.ExecContext(ctx, query)
	return err
}

// SaveGiftLog 保存一条送礼记录。如果当天已有记录，则累加金额；否则创建新记录。
func (s *Store) SaveGiftLog(ctx context.Context, log *UserGiftLog) error {
	// 从日期字符串解析出时间，用于确定表名
	logDate, err := time.Parse("2006-01-02", log.Date)
	if err != nil {
		return fmt.Errorf("invalid date format: %w", err)
	}
	tableName := s.tableName(logDate)

	// 使用 ON DUPLICATE KEY UPDATE 实现数据累加
	query := fmt.Sprintf(`
        INSERT INTO %s (send_uid, target_uid, total_price, date) 
        VALUES (? , ?, ?, ?)
        ON DUPLICATE KEY UPDATE total_price = total_price + VALUES(total_price)
    `, tableName)

	_, err = s.db.ExecContext(ctx, query, log.SendUid, log.TargetUid, log.TotalPrice, log.Date)
	return err
}

// GetGiftLogForDay 查询某天特定用户之间的送礼记录
func (s *Store) GetGiftLogForDay(ctx context.Context, sendUid, targetUid int64, date string) (*UserGiftLog, error) {
	logDate, err := time.Parse("2006-01-02", date)
	if err != nil {
		return nil, fmt.Errorf("invalid date format: %w", err)
	}
	tableName := s.tableName(logDate)

	query := fmt.Sprintf(`
        SELECT send_uid, target_uid, total_price, date FROM %s 
        WHERE send_uid = ? AND target_uid = ? AND date = ?
    `, tableName)

	var log UserGiftLog
	err = s.db.GetContext(ctx, &log, query, sendUid, targetUid, date)
	if errors.Is(err, sql.ErrNoRows) {
		return nil, nil // 没有找到记录不应视为错误
	}
	return &log, err
}

// ListSentGiftsByDateRange 查询某个用户在指定日期范围内送出的所有礼物记录
// 注意：此方法会跨多个月份的表进行查询
func (s *Store) ListSentGiftsByDateRange(ctx context.Context, sendUid int64, startDate, endDate string) ([]*UserGiftLog, error) {
	start, err := time.Parse("2006-01-02", startDate)
	if err != nil {
		return nil, fmt.Errorf("invalid start_date format: %w", err)
	}
	end, err := time.Parse("2006-01-02", endDate)
	if err != nil {
		return nil, fmt.Errorf("invalid end_date format: %w", err)
	}

	var allQueries []string
	var allArgs []interface{}

	// 循环遍历从开始到结束的每一个月
	for t := start.Truncate(24 * time.Hour); !t.After(end); t = t.AddDate(0, 1, 0) {
		// 确保只处理月份的第一天，以避免重复添加同一个月的查询
		currentMonth := time.Date(t.Year(), t.Month(), 1, 0, 0, 0, 0, t.Location())
		// 如果我们已经处理过这个月份，就跳到下一个月
		if len(allQueries) > 0 {
			lastMonthStr := s.tableName(currentMonth.AddDate(0, -1, 0))
			if strings.Contains(allQueries[len(allQueries)-1], lastMonthStr) {
				// 如果上一次的表名和当前表名月份一样, 说明已经处理过了
				if s.tableName(currentMonth) == lastMonthStr {
					continue
				}
			}
		}

		tableName := s.tableName(currentMonth)
		query := fmt.Sprintf(`
            (SELECT send_uid, target_uid, total_price, date FROM %s WHERE send_uid = ? AND date BETWEEN ? AND ?)
        `, tableName)
		allQueries = append(allQueries, query)
		allArgs = append(allArgs, sendUid, startDate, endDate)
	}

	if len(allQueries) == 0 {
		return []*UserGiftLog{}, nil
	}

	// 使用 UNION ALL 合并所有查询
	fullQuery := strings.Join(allQueries, " UNION ALL ")

	var logs []*UserGiftLog
	// sqlx的In操作会对参数进行展开，但这里我们每个子查询的参数都一样，可以直接传递
	// 注意：需要确保你的mysql驱动和sqlx版本支持这种类型的查询
	err = s.db.SelectContext(ctx, &logs, fullQuery, allArgs...)

	return logs, err
}

// SaveGiftLogsBatchOptimized 是 SaveGiftLogsBatch 的高性能版本。
// 它将同一月份的记录合并成单条 multi-values INSERT 语句，
// 极大地减少了数据库网络往返次数，显著提升了大批量数据写入的性能。
func (s *Store) SaveGiftLogsBatchOptimized(ctx context.Context, logs []*UserGiftLog) (err error) {
	if len(logs) == 0 {
		return nil
	}

	// 1. 按表名对日志进行分组
	logsByTable := make(map[string][]*UserGiftLog)
	for _, log := range logs {
		logDate, parseErr := time.Parse("2006-01-02", log.Date)
		if parseErr != nil {
			return fmt.Errorf("无效的日期格式: %s, 错误: %w", log.Date, parseErr)
		}
		tableName := s.tableName(logDate)
		logsByTable[tableName] = append(logsByTable[tableName], log)
	}

	// 开启事务
	tx, err := s.db.Beginx()
	if err != nil {
		return fmt.Errorf("开启事务失败: %w", err)
	}
	defer func() {
		if err != nil {
			_ = tx.Rollback()
			return
		}
		err = tx.Commit()
	}()

	// 2. 为每个分组（即每张表）执行一次批量插入
	for tableName, tableLogs := range logsByTable {
		if len(tableLogs) == 0 {
			continue
		}

		// 3. 动态构造 SQL 语句和参数
		// 基础语句
		queryPrefix := fmt.Sprintf(
			"INSERT INTO %s (send_uid, target_uid, total_price, date) VALUES ",
			tableName,
		)
		// ON DUPLICATE 部分
		querySuffix := " ON DUPLICATE KEY UPDATE total_price = total_price + VALUES(total_price)"

		// 用 strings.Builder 高效拼接 VALUES (?,?,?,?), ... 部分
		var valueStrings strings.Builder
		// 用于存放打平后的所有参数
		var args []interface{}

		for i, log := range tableLogs {
			// 拼接占位符
			valueStrings.WriteString("(?,?,?,?)")
			if i < len(tableLogs)-1 {
				valueStrings.WriteString(",")
			}

			// 将结构体字段打平，按顺序添加到参数切片中
			args = append(args, log.SendUid, log.TargetUid, log.TotalPrice, log.Date)
		}

		// 组合成最终的完整 SQL
		finalQuery := queryPrefix + valueStrings.String() + querySuffix

		// 4. 在事务中执行这条拼接好的 SQL
		if _, err = tx.ExecContext(ctx, finalQuery, args...); err != nil {
			return fmt.Errorf("高性能批量插入数据失败 (表: %s): %w", tableName, err)
		}
	}

	return nil
}

// GetGiftLogsForDay 批量查询某天特定发送者给多个接收者的送礼记录。
// 这个方法是为 manager 层的 BatchQueryUserDailyGiftValue 而设计的。
func (s *Store) GetGiftLogsForDay(ctx context.Context, sendUID int64, targetUIDs []int64, date string) ([]*UserGiftLog, error) {
	// 1. 处理边缘情况：如果目标用户列表为空，直接返回空结果，避免无效的数据库查询。
	if len(targetUIDs) == 0 {
		return []*UserGiftLog{}, nil
	}

	// 2. 根据日期解析出时间，用于确定表名。
	logDate, err := time.Parse("2006-01-02", date)
	if err != nil {
		return nil, fmt.Errorf("无效的日期格式: %w", err)
	}
	tableName := s.tableName(logDate)

	// 3. 构建基础查询语句。
	// 使用 `?`作为占位符，`IN (?)` 将由 sqlx.In 自动展开。
	query := fmt.Sprintf(`
		SELECT send_uid, target_uid, total_price, date 
		FROM %s 
		WHERE send_uid = ? AND target_uid IN (?) AND date = ?`,
		tableName)

	// 4. 使用 sqlx.In 来安全地构建查询和参数列表。
	// sqlx.In 会将 `targetUIDs` 切片展开，以匹配 IN 子句中的多个占位符。
	reboundQuery, args, err := sqlx.In(query, sendUID, targetUIDs, date)
	if err != nil {
		return nil, fmt.Errorf("构建 IN 子句失败: %w", err)
	}

	// 5. 执行查询。
	// sqlx 在查询前需要将查询字符串重新绑定到其数据库驱动的特定占位符格式（例如，MySQL是`?`，PostgreSQL是`$1`）。
	// s.db.SelectContext 可以为你完成这个工作。
	var logs []*UserGiftLog
	err = s.db.SelectContext(ctx, &logs, reboundQuery, args...)
	if err != nil {
		// 查询本身出错（例如，连接断开）
		return nil, fmt.Errorf("数据库批量查询送礼记录失败: %w", err)
	}

	// SelectContext 在没有找到记录时会返回一个空的切片和 nil 错误，这正是我们期望的行为。
	return logs, nil
}
