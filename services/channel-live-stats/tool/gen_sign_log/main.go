package main

import (
	"fmt"
	_ "github.com/go-sql-driver/mysql" // MySQL驱动。
	"github.com/jmoiron/sqlx"
	"golang.52tt.com/pkg/config"
	"golang.52tt.com/pkg/log"
	"time"
)

func initDb() (*sqlx.DB, error) {
	mysqlConfig := &config.MysqlConfig{
		Host:         "************",
		Port:         3306,
		UserName:     "godman",
		Password:     "thegodofman",
		Database:     "contract",
		Charset:      "utf8",
		Protocol:     "tcp",
		PingInterval: 300,
		MaxIdleConns: 25,
		MaxOpenConns: 50,
	}

	db, err := sqlx.Connect("mysql", mysqlConfig.ConnectionString())
	if err != nil {
		log.Errorf("Fail to Connect %+v err:%v", mysqlConfig, err)
		return nil, err
	}

	return db, err
}

func initReadOnlyDb() (*sqlx.DB, error) {
	mysqlConfig := &config.MysqlConfig{
		Host:         "************",
		Port:         3306,
		UserName:     "godman",
		Password:     "thegodofman",
		Database:     "contract",
		Charset:      "utf8",
		Protocol:     "tcp",
		PingInterval: 300,
		MaxIdleConns: 25,
		MaxOpenConns: 50,
	}

	db, err := sqlx.Connect("mysql", mysqlConfig.ConnectionString())
	if err != nil {
		log.Errorf("Fail to Connect %+v err:%v", mysqlConfig, err)
		return nil, err
	}

	return db, err
}

type ToolSvr struct {
	db, readOnlyDb *sqlx.DB
}

func (t *ToolSvr) getUidList(begin, limit uint32) []uint32 {
	list := make([]uint32, 0)
	sql := fmt.Sprintf("select distinct uid from tbl_contract_change_log where uid>148798909 limit ?,?")
	err := t.readOnlyDb.Select(&list, sql, begin, limit)
	if err != nil {
		fmt.Printf("getUidList fail err:%v\n", err)
		return list
	}

	//fmt.Printf("getUidList len(list):%v\n", len(list))
	return list
}

type ChangeLog struct {
	Uid              uint32    `db:"uid"`
	GuildId          uint32    `db:"guild_id"`
	ChangeType       uint32    `db:"change_type"`
	SignTime         time.Time `db:"sign_time"`
	ExpireTime       time.Time `db:"contract_expire_time"`
	ContractDuration uint32    `db:"contract_duration"`
	ChangeTime       time.Time `db:"change_time"`
}

func (t *ToolSvr) getUserContractChangeLog(uid uint32) []*ChangeLog {
	list := make([]*ChangeLog, 0)
	sql := fmt.Sprintf("select uid,guild_id,change_type,sign_time,contract_expire_time,contract_duration,change_time from tbl_contract_change_log where uid=? order by change_time, id")
	err := t.readOnlyDb.Select(&list, sql, uid)
	if err != nil {
		fmt.Printf("getUserContractChangeLog fail uid:%v, err:%v\n", uid, err)
		return list
	}

	fmt.Printf("getUserContractChangeLog uid:%v, len(list):%v\n", uid, len(list))
	return list
}

func (t *ToolSvr) insertCancelChangeLog(uid, guildId uint32, beginTime, endTime time.Time) {
	sql := fmt.Sprintf("insert into tbl_contract_sign_record(uid,guild_id,sign_time,expire_time) " +
		"values(?,?,?,?)")
	_, err := t.db.Exec(sql, uid, guildId, beginTime, endTime)
	if err != nil {
		fmt.Printf("insertCancelChangeLog fail  err:%v\n", err)
		return
	}
}

func initTool() *ToolSvr {

	db, err := initDb()
	if err != nil {
		return nil
	}

	readOnlyDb, err := initReadOnlyDb()
	if err != nil {
		return nil
	}

	return &ToolSvr{
		db:         db,
		readOnlyDb: readOnlyDb,
	}
}

func main() {
	toolSvr := initTool()

	begin := uint32(0)
	limit := uint32(100)

	for {
		uidList := toolSvr.getUidList(begin, limit)
		for _, uid := range uidList {
			logList := toolSvr.getUserContractChangeLog(uid)
			i := 0

			for _, l := range logList {
				i++
				if l.ChangeType == 0 && len(logList) == i {
					toolSvr.insertCancelChangeLog(l.Uid, l.GuildId, l.SignTime, l.ExpireTime)

				} else if l.ChangeType == 1 || l.ChangeType == 4 {
					toolSvr.insertCancelChangeLog(l.Uid, l.GuildId, l.SignTime, l.ChangeTime)
				}
			}
		}

		if len(uidList) < int(limit) {
			break
		}

		begin += limit
	}

	fmt.Println("done")
	return
}
