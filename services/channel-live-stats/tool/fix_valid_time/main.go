package main

import (
	"context"
	"fmt"
	"github.com/jinzhu/gorm"
	"golang.52tt.com/pkg/config"
	"golang.52tt.com/services/channel-live-stats/mysql"
	"time"
)

func main() {
	statsMysqlConf := &config.MysqlConfig{
		Host:         "************",
		Port:         3306,
		Protocol:     "tcp",
		Database:     "channel_live_stats",
		UserName:     "godman",
		Password:     "thegodofman",
		Charset:      "utf8",
		PingInterval: 300,
		MaxIdleConns: 25,
		MaxOpenConns: 50,
	}
	mysqlDb, err := gorm.Open("mysql", statsMysqlConf.ConnectionString())
	if err != nil {
		fmt.Printf("Failed to Connect mysql %v", err)
		return
	}

	statsMysqlStore := mysql.NewMysql(mysqlDb, mysqlDb)

	liveMysqlConf := &config.MysqlConfig{
		Host:         "*************",
		Port:         3306,
		Protocol:     "tcp",
		Database:     "channellive",
		UserName:     "godman",
		Password:     "thegodofman",
		Charset:      "utf8",
		PingInterval: 300,
		MaxIdleConns: 25,
		MaxOpenConns: 50,
	}
	liveDb, err := gorm.Open("mysql", liveMysqlConf.ConnectionString())
	if err != nil {
		fmt.Printf("Failed to Connect mysql %v", err)
		return
	}

	ctx := context.Background()
	type Tmp struct {
		Uid   uint32
		Begin uint32
		End   uint32
	}

	list := make([]*Tmp, 0)
	//now := time.Now()
	//dateEnd := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, time.Local)
	dateEnd := time.Unix(1685721599, 0)
	dateBegin := time.Unix(1685635200, 0)

	err = liveDb.Table("tblChannelLiveRecord").Select("uid,unix_timestamp(begin_time) as begin,unix_timestamp(end_time) as end").
		Where("end_time >= ? and end_time < ? and (unix_timestamp(end_time)-unix_timestamp(begin_time)) >= 3600", dateBegin, dateEnd).
		Scan(&list).Error
	if err != nil {
		fmt.Printf("err:%v", err)
		return
	}

	for _, m := range list {

		anchorUid := m.Uid
		endTime := time.Unix(int64(m.End), 0)
		beginTime := time.Unix(int64(m.Begin), 0)

		if beginTime.IsZero() || beginTime.After(endTime) ||
			endTime.Sub(beginTime) >= 24*time.Hour {
			continue
		}

		beginDate := mysql.GetDateTime(beginTime)
		endDate := mysql.GetDateTime(endTime)

		if !endDate.Equal(beginDate) { // 跨天了
			end := endTime
			begin := endDate
			i := 0
			for {
				if end.Sub(begin) >= time.Hour {
					// 记录有效天，,增加有效时长
					UpdateDailyRecord(ctx, statsMysqlStore, anchorUid, uint32(end.Sub(begin).Minutes()), begin)
				}

				end = begin
				begin = begin.AddDate(0, 0, -1)

				if begin.Before(beginTime) {
					begin = beginTime
				}

				if i > 3 || !end.After(begin) {
					break
				}

				i++
				time.Sleep(100 * time.Millisecond)
			}
		} else if endTime.Sub(beginTime) >= time.Hour {
			// 记录有效天,增加有效时长
			UpdateDailyRecord(ctx, statsMysqlStore, anchorUid, uint32(endTime.Sub(beginTime).Minutes()), endTime)
		}
	}
}

func UpdateDailyRecord(ctx context.Context, statsMysqlStore *mysql.Store, anchorUid, incrValidMinutes uint32, t time.Time) {
	info, _, err := statsMysqlStore.GetAnchorBaseInfo(ctx, anchorUid)
	if err != nil {
		fmt.Printf("err:%v", err)
		return
	}

	signGuildId := info.SignGuildId
	// 日记录
	hasChange, err := statsMysqlStore.UpdateAnchorDailyRecordValidDay(ctx, nil, anchorUid, signGuildId, 1, t)
	if err != nil {
		fmt.Printf("err:%v\n", err)
		return
	}

	if !hasChange {
		// 没有变化(即已经记录过有效天了)，返回
		return
	}

	// 月记录
	err = statsMysqlStore.IncrAnchorMonthlyRecordValidDay(ctx, nil, anchorUid, signGuildId, 1, mysql.GetYearMonth(t))
	if err != nil {
		fmt.Printf("err:%v\n", err)
		return
	}

	_ = statsMysqlStore.IncrAnchorDailyValidTime(ctx, nil, anchorUid, signGuildId, incrValidMinutes, 0, t)
	_ = statsMysqlStore.IncrAnchorMonthlyValidTime(ctx, nil, anchorUid, signGuildId, incrValidMinutes, 0, mysql.GetYearMonth(t))

	// 增加每天直播时长
	statsMysqlStore.IncrAnchorDailyLiveTime(ctx, nil, anchorUid, signGuildId, incrValidMinutes, 0, t)
	statsMysqlStore.IncrAnchorMonthlyLiveTime(ctx, nil, anchorUid, signGuildId, incrValidMinutes, 0, mysql.GetYearMonth(t))

	fmt.Printf("uid:%v, signGuildId:%v, incrValidMinutes:%v t:%v\n", anchorUid, signGuildId, incrValidMinutes, t)
	return
}
