package server

import (
	"context"
	"errors"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/protocol/common/status"
	driverMysql "gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
	"time"

	"github.com/go-redis/redis"
	jinzhu "github.com/jinzhu/gorm"

	"gitlab.ttyuyin.com/avengers/tyr/pkg/cluster/timer"
	"golang.52tt.com/pkg/config"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/mapreduce"
	tracing "golang.52tt.com/pkg/tracing/jaeger"
	pb "golang.52tt.com/protocol/services/channel-live-stats"
	"golang.52tt.com/services/channel-live-stats/cache"
	"golang.52tt.com/services/channel-live-stats/conf"
	"golang.52tt.com/services/channel-live-stats/event"
	"golang.52tt.com/services/channel-live-stats/manager"
	"golang.52tt.com/services/channel-live-stats/mysql"
)

type ChannelLiveStatisticsServer struct {
	sc                     *conf.ServiceConfigT
	mgr                    *manager.ChannelLiveStatsManager
	mysqlStore             *mysql.Store
	chLiveStatusKafkaSub   *event.ChLiveStatusKafkaSub
	presentKafkaSub        *event.PresentEventSub
	anchorContractKafkaSub *event.AnchorContractEventSub
	anchorFansKafkaSub     *event.AnchorKafkaSub
	statsCache             *cache.StatsCache
	channelKafkaSub        *event.ChannelKafkaSub
	followKafkaSub         *event.FollowEventSub
	knightGroupKfkSub      *event.KnightGroupSub
	presentKafkaSub2       *event.PresentEventSub2
	extGameKfkSub          *event.RevenueExtGameSub
	timerD                 *timer.Timer
}

func NewChannelLiveStatisticsServer(ctx context.Context, cfg config.Configer) (*ChannelLiveStatisticsServer, error) {

	sc := &conf.ServiceConfigT{}

	cfgPath := ctx.Value("configfile").(string)
	if cfgPath == "" {
		return nil, errors.New("configfile not exist")
	}
	err := sc.Parse(cfgPath)
	if err != nil {
		log.ErrorWithCtx(ctx, "config Parse fail err:%v", err)
		return nil, err
	}

	err = conf.InitLiveStatsConfig()
	if err != nil {
		log.ErrorWithCtx(ctx, "InitLiveStatsConfig fail err:%v", err)
		return nil, err
	}

	redisClient := redis.NewClient(&redis.Options{
		Network:            sc.GetRedisConfig().Protocol,
		Addr:               sc.GetRedisConfig().Addr(),
		PoolSize:           sc.GetRedisConfig().PoolSize,
		IdleCheckFrequency: sc.GetRedisConfig().IdleCheckFrequency(),
		DB:                 sc.GetRedisConfig().DB,
	})
	log.DebugWithCtx(ctx, "Initialized redis connection pool to %s://%s/%d", sc.GetRedisConfig().Protocol, sc.GetRedisConfig().Addr(), sc.GetRedisConfig().DB)
	redisTracer := tracing.Init("channel-live-Statistics_redis")
	cacheClient := cache.NewStatsCache(redisClient, redisTracer)

	mysqlDb, err := jinzhu.Open("mysql", sc.GetMysqlConnectionString())
	if err != nil {
		log.ErrorWithCtx(ctx, "Failed to create mysql %v", err)
		return nil, err
	}

	readonlyMysqlDb, err := jinzhu.Open("mysql", sc.GetReadonlyMysqlConnectionString())
	if err != nil {
		log.ErrorWithCtx(ctx, "Failed to create mysql %v", err)
		return nil, err
	}

	newMysqlDb, err := gorm.Open(driverMysql.Open(sc.GetMysqlConnectionString()), &gorm.Config{
		Logger:                 logger.Default.LogMode(logger.Silent),
		SkipDefaultTransaction: true,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "Failed to create mysql %v", err)
		return nil, err
	}

	mysqlStore := mysql.NewMysql(mysqlDb, readonlyMysqlDb, newMysqlDb)
	mysqlStore.CreateTable()

	// 大小进程的定时器，同一个任务只会在一个节点上执行
	timerD_, err := timer.NewTimerD(ctx, "channel-live-stats", timer.WithV6RedisCmdable(redisClient))
	if err != nil {
		log.ErrorWithCtx(ctx, "Failed to NewTimerD err:%v", err)
		return nil, err
	}

	mgr := manager.NewChannelLiveStatsManager(sc, mysqlStore, cacheClient, timerD_)

	chLiveStatusEvent, err := event.NewChLiveStatusKafkaSub(sc.GetLiveStatusKafkaConfig().ClientID, sc.GetLiveStatusKafkaConfig().GroupID,
		sc.GetLiveStatusKafkaConfig().TopicList(), sc.GetLiveStatusKafkaConfig().BrokerList(), mgr)
	if err != nil {
		log.ErrorWithCtx(ctx, "Failed to NewChLiveStatusKafkaSubscriber err %s", err.Error())
		return nil, err
	}

	/*
		err = chLiveStatusEvent.Start()
		if err != nil {
			log.ErrorWithCtx(ctx, "Failed to Start chLiveStatusEvent.Start() err %s", err.Error())
			return nil, err
		}
	*/

	presentKafkaSub, err := event.NewPresentEventSub(sc.GetPresentKafkaConfig().ClientID, sc.GetPresentKafkaConfig().GroupID,
		sc.GetPresentKafkaConfig().TopicList(), sc.GetPresentKafkaConfig().BrokerList(), mgr)
	if err != nil {
		log.ErrorWithCtx(ctx, "Failed to NewPresentEventSub err %s", err.Error())
		return nil, err
	}

	/*
		err = presentKafkaSub.Start()
		if err != nil {
			log.ErrorWithCtx(ctx, "Failed to Start presentKafkaSub.Start() err %s", err.Error())
			return nil, err
		}
	*/

	presentKafkaSub2, err := event.NewPresentEventSub2(sc.GetPresentKafkaConfig2().ClientID, sc.GetPresentKafkaConfig2().GroupID,
		sc.GetPresentKafkaConfig2().TopicList(), sc.GetPresentKafkaConfig2().BrokerList(), mgr)
	if err != nil {
		log.ErrorWithCtx(ctx, "Failed to NewPresentEventSub2 err %s", err.Error())
		return nil, err
	}

	/*
		err = presentKafkaSub2.Start()
		if err != nil {
			log.ErrorWithCtx(ctx, "Failed to Start presentKafkaSub2.Start() err %s", err.Error())
			return nil, err
		}
	*/

	anchorFansKafkaSub, err := event.NewAnchorKafkaSubscriber(sc.GetAnchorFansKafkaConfig().ClientID, sc.GetAnchorFansKafkaConfig().GroupID,
		sc.GetAnchorFansKafkaConfig().TopicList(), sc.GetAnchorFansKafkaConfig().BrokerList(), mgr)
	if err != nil {
		log.ErrorWithCtx(ctx, "Failed to NewAnchorKafkaSubscriber err %s", err.Error())
		return nil, err
	}

	//err = anchorFansKafkaSub.Start()
	//if err != nil {
	//	log.ErrorWithCtx(ctx, "Failed to anchorFansKafkaSub.Start() err %s", err.Error())
	//	return nil, err
	//}

	anchorContractKafkaSub, err := event.NewAnchorContractEventSub(sc.GetContractKafkaConfig().ClientID, sc.GetContractKafkaConfig().GroupID,
		sc.GetContractKafkaConfig().TopicList(), sc.GetContractKafkaConfig().BrokerList(), mgr)
	if err != nil {
		log.ErrorWithCtx(ctx, "Failed to NewAnchorContractEventSub err %s", err.Error())
		return nil, err
	}

	/*
		err = anchorContractKafkaSub.Start()
		if err != nil {
			log.ErrorWithCtx(ctx, "Failed to Start NewAnchorContractEventSub.Start() err %s", err.Error())
			return nil, err
		}
	*/

	channelKafkaSub, err := event.NewChannelKafkaSub(sc.GetChannelKafkaConfig().ClientID, sc.GetChannelKafkaConfig().GroupID,
		sc.GetChannelKafkaConfig().TopicList(), sc.GetChannelKafkaConfig().BrokerList(), mgr)
	if err != nil {
		log.ErrorWithCtx(ctx, "Failed to NewChannelKafkaSub err %s", err.Error())
		return nil, err
	}

	/*
		err = channelKafkaSub.Start()
		if err != nil {
			log.ErrorWithCtx(ctx, "Failed to Start channelKafkaSub.Start() err %s", err.Error())
			return nil, err
		}
	*/

	followKafkaSub, err := event.NewFollowEventSub(sc.GetFollowKafkaConfig().ClientID, sc.GetFollowKafkaConfig().GroupID,
		sc.GetFollowKafkaConfig().TopicList(), sc.GetFollowKafkaConfig().BrokerList(), mgr)
	if err != nil {
		log.ErrorWithCtx(ctx, "Failed to NewFollowEventSub err %s", err.Error())
		return nil, err
	}

	/*
		err = followKafkaSub.Start()
		if err != nil {
			log.ErrorWithCtx(ctx, "Failed to StartfollowKafkaSub.Start() err %s", err.Error())
			return nil, err
		}
	*/

	knightGroupKfkSub, err := event.NewKnightGroupSub(sc.GetKnightGroupKfkConfig().ClientID, sc.GetKnightGroupKfkConfig().GroupID,
		sc.GetKnightGroupKfkConfig().TopicList(), sc.GetKnightGroupKfkConfig().BrokerList(), mgr)
	if err != nil {
		log.ErrorWithCtx(ctx, "failed to NewKnightGroupSub err:%v", err)
		return nil, err
	}

	/*
		err = knightGroupKfkSub.Start()
		if err != nil {
			log.ErrorWithCtx(ctx, "failed to knightGroupKfkSub.Start() err:%v", err)
			return nil, err
		}
	*/

	extGameKfkSub, err := event.NewRevenueExtGameSub(sc.GetExtGameKfkConfig().ClientID, sc.GetExtGameKfkConfig().GroupID,
		sc.GetExtGameKfkConfig().TopicList(), sc.GetExtGameKfkConfig().BrokerList(), mgr)
	if err != nil {
		log.ErrorWithCtx(ctx, "failed to NewRevenueExtGameSub err:%v", err)
		return nil, err
	}

	/*
		err = extGameKfkSub.Start()
		if err != nil {
			log.ErrorWithCtx(ctx, "failed to extGameKfkSub.Start() err:%v", err)
			return nil, err
		}
	*/

	timerD_.Start()

	return &ChannelLiveStatisticsServer{
		sc:                     sc,
		mgr:                    mgr,
		mysqlStore:             mysqlStore,
		chLiveStatusKafkaSub:   chLiveStatusEvent,
		presentKafkaSub:        presentKafkaSub,
		anchorContractKafkaSub: anchorContractKafkaSub,
		anchorFansKafkaSub:     anchorFansKafkaSub,
		statsCache:             cacheClient,
		channelKafkaSub:        channelKafkaSub,
		followKafkaSub:         followKafkaSub,
		knightGroupKfkSub:      knightGroupKfkSub,
		presentKafkaSub2:       presentKafkaSub2,
		extGameKfkSub:          extGameKfkSub,
		timerD:                 timerD_,
	}, nil

}

func (s *ChannelLiveStatisticsServer) ShutDown() {
	s.mgr.ShutDown()
	s.chLiveStatusKafkaSub.Close()
	s.presentKafkaSub.Close()
	s.anchorContractKafkaSub.Close()
	s.anchorFansKafkaSub.Close()
	s.channelKafkaSub.Close()
	s.followKafkaSub.Close()
	s.knightGroupKfkSub.Close()
	s.presentKafkaSub2.Close()
	s.extGameKfkSub.Close()
	s.timerD.Stop()
}

func (s *ChannelLiveStatisticsServer) GetAnchorBaseInfo(ctx context.Context, in *pb.GetAnchorBaseInfoReq) (*pb.GetAnchorBaseInfoResp, error) {
	out := &pb.GetAnchorBaseInfoResp{}
	uid := in.GetUid()
	var err error

	out.Info, _, err = s.mgr.GetAnchorBaseInfo(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAnchorBaseInfo fail. uid:%v, err:%v", uid, err)
		return out, err
	}

	return out, nil
}

func (s *ChannelLiveStatisticsServer) GetGuildAnchorBaseInfo(ctx context.Context, in *pb.GetGuildAnchorBaseInfoReq) (*pb.GetGuildAnchorBaseInfoResp, error) {
	out := &pb.GetGuildAnchorBaseInfoResp{}

	log.DebugWithCtx(ctx, "GetGuildAnchorBaseInfo begin in:%v", in)
	var err error

	info, isExist, err := s.mysqlStore.GetGuildAnchorBaseInfo(ctx, in.GetGuildId(), in.GetUid(), in.GetAgentUid())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetGuildAnchorBaseInfo fail. in:%v, err:%v", in, err)
		return out, err
	}

	if isExist {
		out.Info = &pb.AnchorBaseInfo{
			Uid:         info.Uid,
			SignGuildId: info.SignGuildId,
			LiveRoomId:  info.LiveRoomId,
			LastLiveAt:  uint32(info.LastLiveAt.Unix()),
		}
	}

	log.DebugWithCtx(ctx, "GetGuildAnchorBaseInfo end in:%v out:%v", in, out)
	return out, nil
}

func (s *ChannelLiveStatisticsServer) BatchGetAnchorBaseInfo(ctx context.Context, in *pb.BatchGetAnchorBaseInfoReq) (*pb.BatchGetAnchorBaseInfoResp, error) {
	return s.mgr.BatchGetAnchorBaseInfo(ctx, in)
}

func (s *ChannelLiveStatisticsServer) GetAnchorBaseInfoList(ctx context.Context, in *pb.GetAnchorBaseInfoListReq) (*pb.GetAnchorBaseInfoListResp, error) {
	return s.mgr.GetAnchorBaseInfoList(ctx, in)
}

func (s *ChannelLiveStatisticsServer) GetAnchorTotalStatsBetweenDate(ctx context.Context, in *pb.GetAnchorTotalStatsBetweenDateReq) (*pb.GetAnchorTotalStatsBetweenDateResp, error) {
	out := &pb.GetAnchorTotalStatsBetweenDateResp{}

	anchorUid := in.GetAnchorUid()
	guildId := in.GetGuildId()
	begin := time.Unix(int64(in.GetBeginDate()), 0)
	end := time.Unix(int64(in.GetEndDate()), 0)

	mapStats, err := s.mysqlStore.BatchGetAnchorTotalStatsBetweenDate(ctx, guildId, []uint32{anchorUid}, begin, end)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAnchorTotalStatsBetweenDate fail to BatchGetAnchorTotalStatsBetweenDate. in:%+v, err:%v", in, err)
		return out, err
	}

	if stats, ok := mapStats[anchorUid]; ok {
		out.TotalChannelFee = stats.ChannelFee
		out.TotalAnchorIncome = stats.AnchorIncome
	}

	log.DebugWithCtx(ctx, "GetAnchorTotalStatsBetweenDate in:%+v, out:%+v", in, out)
	return out, nil
}

func (s *ChannelLiveStatisticsServer) GetGuildDailyTotalStats(ctx context.Context, in *pb.GetGuildDailyTotalStatsReq) (*pb.GetGuildDailyTotalStatsResp, error) {
	out := &pb.GetGuildDailyTotalStatsResp{}

	guildId := in.GetGuildId()
	begin := time.Unix(int64(in.GetBeginDate()), 0)
	end := time.Unix(int64(in.GetEndDate()), 0)

	liveAnchorCnt, totalChannelFee, totalAnchorIncome, validAnchorCnt, newAddAnchorCnt, totalKnightIncome, channelPkgFee, virtualFee, err :=
		s.mysqlStore.GetGuildDailyTotalStats(ctx, guildId, begin, end)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetGuildDailyTotalStats fail. in:%+v, err:%v", in, err)
		return out, err
	}

	out.Stats = &pb.GuildTotalStats{
		LiveAnchorCnt:      liveAnchorCnt,
		TotalChannelFee:    totalChannelFee,
		TotalAnchorIncome:  totalAnchorIncome,
		ValidAnchorCnt:     validAnchorCnt,
		NewAddAnchorCnt:    newAddAnchorCnt,
		AnchorKnightIncome: totalKnightIncome,
		ChannelPkgFee:      channelPkgFee,
		VirtualFee:         virtualFee,
	}

	log.DebugWithCtx(ctx, "GetGuildDailyTotalStats in:%+v, out:%+v", in, out)
	return out, nil
}

func (s *ChannelLiveStatisticsServer) GetGuildDailyStatsList(ctx context.Context, in *pb.GetGuildDailyStatsListReq) (*pb.GetGuildDailyStatsListResp, error) {
	out := &pb.GetGuildDailyStatsListResp{}

	guildId := in.GetGuildId()
	beginTime := time.Unix(int64(in.GetBeginDate()), 0)
	endTime := time.Unix(int64(in.GetEndDate()), 0)

	list, err := s.mysqlStore.GetGuildDailyStatsList(ctx, guildId, beginTime, endTime, in.GetBegin(), in.GetLimit())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetGuildDailyStatsList fail. in:%+v, err:%v", in, err)
		return out, err
	}

	stats := make([]*pb.GuildDailyStats, 0, len(list))
	for _, info := range list {
		stats = append(stats, &pb.GuildDailyStats{
			Stats: &pb.GuildTotalStats{
				LiveAnchorCnt:      info.LiveAnchorCnt,
				TotalAnchorIncome:  info.TotalAnchorIncome,
				TotalChannelFee:    info.TotalChannelFee,
				ValidAnchorCnt:     info.ValidAnchorCnt,
				NewAddAnchorCnt:    info.NewAddAnchorCnt,
				AnchorKnightIncome: info.TotalKnightIncome,
				ChannelPkgFee:      info.TotalChannelPkgFee,
				VirtualFee:         info.TotalVirtualFee,
			},
			DateTime: uint32(info.Date.Unix()),
		})
	}

	out.StatsList = stats

	log.DebugWithCtx(ctx, "GetGuildDailyStatsList in:%+v, out:%+v", in, out)
	return out, nil
}

func (s *ChannelLiveStatisticsServer) GetAnchorDailyRecordWithDateList(ctx context.Context, in *pb.GetAnchorDailyRecordWithDateListReq) (*pb.GetAnchorDailyRecordWithDateListResp, error) {
	out := &pb.GetAnchorDailyRecordWithDateListResp{}

	uid := in.GetUid()
	beginTime := time.Unix(int64(in.GetBeginTime()), 0)
	endTime := time.Unix(int64(in.GetEndTime()), 0)

	list, err := s.mysqlStore.GetAnchorDailyRecordWithDateList(ctx, uid, beginTime, endTime)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAnchorDailyRecordWithDateList fail. in:%+v, err:%v", in, err)
		return out, err
	}

	stats := make([]*pb.AnchorDailyStats, 0, len(list))
	for _, info := range list {
		var isLiveActiveDay, isGameActiveDay, isVirtualActiveDay bool
		if info.LiveValidMinutes >= mysql.LiveActiveAnchorMinutes {
			isLiveActiveDay = true
		}
		if info.GameMin >= mysql.GameActiveDayMin {
			isGameActiveDay = true
		}
		if info.VirtualValidMin >= mysql.VirtualActiveAnchorMinutes {
			isVirtualActiveDay = true
		}

		stats = append(stats, &pb.AnchorDailyStats{
			Uid:                uid,
			Date:               uint32(info.Date.Unix()),
			AnchorIncome:       info.AnchorIncome,
			ChannelFee:         info.ChannelFee,
			LiveValidMinutes:   info.LiveValidMinutes,
			IsValidDay:         info.DayLiveValid > 0,
			DayFollowCnt:       info.DayFollowCnt,
			NewAddFans:         info.NewFansCnt,
			KnightIncome:       info.KnightIncome,
			IsLiveActiveDay:    isLiveActiveDay,
			ConsumerCnt:        info.DayConsumerCnt,
			AudienceCnt:        info.DayAudienceCnt,
			GameFee:            info.GameFee,
			GameChannelFee:     info.GameChannelFee,
			GameMin:            info.GameMin,
			IsGameActiveDay:    isGameActiveDay,
			LiveMinutes:        info.LiveMinutes,
			VirtualFee:         info.VirtualFee,
			VirtualIncome:      info.VirtualIncome,
			VirtualMin:         info.VirtualMin,
			IsVirtualActiveDay: isVirtualActiveDay,
			WeekNewAddFans:     info.WeekNewFansCnt,
			WeekConsumerCnt:    info.WeekConsumerCnt,
			WeekAudienceCnt:    info.WeekAudienceCnt,
			WeekFollowCnt:      info.WeekFollowCnt,
		})
	}

	out.List = stats
	log.DebugWithCtx(ctx, "GetAnchorDailyRecordWithDateList in:%+v, out:%+v", in, out)
	return out, nil
}

func (s *ChannelLiveStatisticsServer) BatchGetAnchorDailyRecord(ctx context.Context, in *pb.BatchGetAnchorDailyRecordReq) (*pb.BatchGetAnchorDailyRecordResp, error) {
	out := &pb.BatchGetAnchorDailyRecordResp{}

	log.DebugWithCtx(ctx, "BatchGetAnchorDailyRecord begin in:%v", in)

	beginTime := time.Unix(int64(in.GetBeginTime()), 0)
	endTime := time.Unix(int64(in.GetEndTime()), 0)

	list, err := s.mysqlStore.BatchGetAnchorDailyRecord(ctx, in.GetGuildId(), in.GetAgentUid(), in.GetUidList(), beginTime, endTime)
	if err != nil {
		log.ErrorWithCtx(ctx, "BatchGetAnchorDailyRecord fail. in:%+v, err:%v", in, err)
		return out, err
	}

	stats := make([]*pb.AnchorDailyStats, 0, len(list))
	for _, info := range list {
		var isLiveActiveDay, isGameActiveDay bool
		if info.GameMin >= mysql.GameActiveDayMin {
			isGameActiveDay = true
		}

		if info.LiveValidMinutes >= mysql.LiveActiveAnchorMinutes {
			isLiveActiveDay = true
		}

		stats = append(stats, &pb.AnchorDailyStats{
			Uid:              info.Uid,
			Date:             uint32(info.Date.Unix()),
			AnchorIncome:     info.AnchorIncome,
			ChannelFee:       info.ChannelFee,
			LiveValidMinutes: info.LiveValidMinutes,
			IsValidDay:       info.DayLiveValid > 0,
			DayActiveFans:    info.DayActiveFans,
			DaySpFans:        info.DaySpFans,
			FansSendFee:      info.FansSendFee,
			DayFollowCnt:     info.DayFollowCnt,
			NewAddFans:       info.NewFansCnt,
			LiveMinutes:      info.LiveMinutes,
			AgentUid:         info.AgentUid,
			KnightIncome:     info.KnightIncome,
			GameFee:          info.GameFee,
			GameChannelFee:   info.GameChannelFee,
			GameMin:          info.GameMin,
			IsGameActiveDay:  isGameActiveDay,
			VirtualFee:       info.VirtualFee,
			VirtualIncome:    info.VirtualIncome,
			VirtualMin:       info.VirtualMin,
			IsLiveActiveDay:  isLiveActiveDay,
			ConsumerCnt:      info.DayConsumerCnt,
		})
	}

	out.List = stats
	log.DebugWithCtx(ctx, "BatchGetAnchorDailyRecord in:%+v, out:%+v", in, out)
	return out, nil
}

func (s *ChannelLiveStatisticsServer) GetAnchorDailyStatsList(ctx context.Context, in *pb.GetAnchorDailyStatsListReq) (*pb.GetAnchorDailyStatsListResp, error) {
	out := &pb.GetAnchorDailyStatsListResp{}

	log.DebugWithCtx(ctx, "GetAnchorDailyStatsList begin in:%v", in)

	beginTime := time.Unix(int64(in.GetBeginTime()), 0)
	endTime := time.Unix(int64(in.GetEndTime()), 0)

	list, err := s.mysqlStore.GetAnchorDailyStatsList(ctx, in.GetGuildId(), in.GetAgentUid(), in.GetUidList(), beginTime, endTime, in.GetOffset(), in.GetLimit())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAnchorDailyStatsList fail. in:%+v, err:%v", in, err)
		return out, err
	}

	totalCnt, err := s.mysqlStore.GetGuildDailyStatsTotalCnt(ctx, in.GetGuildId(), in.GetAgentUid(), in.GetUidList(), beginTime, endTime)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetGuildDailyStatsTotalCnt fail. in:%+v, err:%v", in, err)
		return out, err
	}

	stats := make([]*pb.AnchorDailyStats, 0, len(list))
	for _, info := range list {
		var isLiveActiveDay, isGameActiveDay bool
		if info.LiveValidMinutes >= mysql.LiveActiveAnchorMinutes {
			isLiveActiveDay = true
		}
		if info.GameMin >= mysql.GameActiveDayMin {
			isGameActiveDay = true
		}

		stats = append(stats, &pb.AnchorDailyStats{
			Uid:              info.Uid,
			Date:             uint32(info.Date.Unix()),
			AnchorIncome:     info.AnchorIncome,
			ChannelFee:       info.ChannelFee,
			LiveValidMinutes: info.LiveValidMinutes,
			IsValidDay:       info.DayLiveValid > 0,
			DayActiveFans:    info.DayActiveFans,
			DaySpFans:        info.DaySpFans,
			FansSendFee:      info.FansSendFee,
			DayFollowCnt:     info.DayFollowCnt,
			NewAddFans:       info.NewFansCnt,
			LiveMinutes:      info.LiveMinutes,
			AgentUid:         info.AgentUid,
			KnightIncome:     info.KnightIncome,
			IsLiveActiveDay:  isLiveActiveDay,
			GameFee:          info.GameFee,
			GameChannelFee:   info.GameChannelFee,
			GameMin:          info.GameMin,
			IsGameActiveDay:  isGameActiveDay,
			VirtualFee:       info.VirtualFee,
			VirtualIncome:    info.VirtualIncome,
			VirtualMin:       info.VirtualMin,
			ConsumerCnt:      info.DayConsumerCnt,
		})
	}

	out.List = stats
	out.TotalCnt = totalCnt
	log.DebugWithCtx(ctx, "GetGuildDailyStatsTotalCnt in:%+v, out:%+v", in, out)
	return out, nil
}

func (s *ChannelLiveStatisticsServer) GetGuildDailyAnchorList(ctx context.Context, in *pb.GetGuildDailyAnchorListReq) (*pb.GetGuildDailyAnchorListResp, error) {
	out := &pb.GetGuildDailyAnchorListResp{}

	guildId := in.GetGuildId()
	dateTime := time.Unix(int64(in.GetDate()), 0)

	list, err := s.mysqlStore.GetGuildDailyAnchorList(ctx, guildId, in.GetCondition(), dateTime, in.GetBegin(), in.GetLimit())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetGuildDailyAnchorList fail. in:%+v, err:%v", in, err)
		return out, err
	}

	out.AnchorList = list

	log.DebugWithCtx(ctx, "GetGuildDailyAnchorList in:%+v, out:%+v", in, out)
	return out, nil
}

func (s *ChannelLiveStatisticsServer) GetGuildWeeklyAnchorList(ctx context.Context, in *pb.GetGuildWeeklyAnchorListReq) (*pb.GetGuildWeeklyAnchorListResp, error) {
	out := &pb.GetGuildWeeklyAnchorListResp{}

	guildId := in.GetGuildId()
	beginTime := time.Unix(int64(in.GetWeekBeginDate()), 0)
	endTime := time.Unix(int64(in.GetWeekEndDate()), 0)

	list, err := s.mysqlStore.GetGuildWeeklyAnchorList(ctx, guildId, in.GetCondition(), in.GetActiveMinCnt(), beginTime, endTime, in.GetBegin(), in.GetLimit())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetGuildWeeklyAnchorList fail. in:%+v, err:%v", in, err)
		return out, err
	}

	out.AnchorList = list

	log.DebugWithCtx(ctx, "GetGuildWeeklyAnchorList in:%+v, out:%+v", in, out)
	return out, nil
}

func (s *ChannelLiveStatisticsServer) BatchGetAnchorWeeklyRecord(ctx context.Context, in *pb.BatchGetAnchorWeeklyRecordReq) (*pb.BatchGetAnchorWeeklyRecordResp, error) {
	out := &pb.BatchGetAnchorWeeklyRecordResp{}

	log.DebugWithCtx(ctx, "BatchGetAnchorWeeklyRecord begin in:%v", in)
	beginTime := time.Unix(int64(in.GetBeginTime()), 0)
	endTime := time.Unix(int64(in.GetEndTime()), 0)
	list, err := s.mysqlStore.BatchGetAnchorWeeklyRecord(ctx, in.GetGuildId(), in.GetAgentUid(), in.GetUidList(), beginTime, endTime)
	if err != nil {
		log.ErrorWithCtx(ctx, "BatchGetAnchorWeeklyRecord failed in:%v err:%v", in, err)
		return out, err
	}

	stats := make([]*pb.AnchorWeeklyStats, 0, len(list))
	for _, info := range list {
		stats = append(stats, &pb.AnchorWeeklyStats{
			Uid:                info.Uid,
			AnchorIncome:       info.TotalAnchorIncome,
			ChannelFee:         info.TotalChannelFee,
			LiveValidMinutes:   info.LiveValidMinutes,
			ValidDaysCnt:       info.TotalValidDays,
			WeekActiveFans:     info.TotalActiveFans,
			WeekSpFans:         info.TotalSpFans,
			FansSendFee:        info.TotalFansSendFee,
			WeekFollowCnt:      info.TotalFollowCnt,
			NewAddFans:         info.TotalNewFans,
			LiveMinutes:        info.TotalLiveMinutes,
			AgentUid:           info.AgentUid,
			AnchorKnightIncome: info.TotalKnightIncome,
			LiveActiveDays:     info.TotalActiveDays,
			ConsumerCnt:        info.TotalConsumerCnt,
			AudienceCnt:        info.TotalAudienceCnt,
			GameFee:            info.TotalGameFee,
			GameChannelFee:     info.TotalGameChannelFee,
			GameMin:            info.TotalGameMin,
			GameActiveDays:     info.TotalGameActiveDays,
			VirtualFee:         info.TotalVirtualFee,
			VirtualIncome:      info.TotalVirtualIncome,
			VirtualMin:         info.TotalVirtualMin,
			VirtualActiveDays:  info.TotalVirtualActiveDays,
		})
	}

	out.List = stats
	log.DebugWithCtx(ctx, "BatchGetAnchorWeeklyRecord begin in:%v out:%v", in, out)
	return out, nil
}

func (s *ChannelLiveStatisticsServer) GetAnchorWeeklyStatsList(ctx context.Context, in *pb.GetAnchorWeeklyStatsListReq) (*pb.GetAnchorWeeklyStatsListResp, error) {
	out := &pb.GetAnchorWeeklyStatsListResp{}

	log.DebugWithCtx(ctx, "GetAnchorWeeklyStatsList begin in:%v", in)
	beginTime := time.Unix(int64(in.GetBeginTime()), 0)
	endTime := time.Unix(int64(in.GetEndTime()), 0)

	list, err := s.mysqlStore.GetAnchorWeeklyRecordList(ctx, in.GetGuildId(), in.GetAgentUid(), in.GetAnchorList(), beginTime, endTime, in.GetOffset(), in.GetLimit())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAnchorWeeklyRecordList failed in:%v err:%v", in, err)
		return out, err
	}

	totalCnt, err := s.mysqlStore.GetGuildWeeklyStatsTotalCnt(ctx, in.GetGuildId(), in.GetAgentUid(), in.GetAnchorList(), beginTime, endTime)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAnchorWeeklyStatsList GetGuildWeeklyStatsTotalCnt failed in:%v err:%v", in, err)
		return out, err
	}

	stats := make([]*pb.AnchorWeeklyStats, 0, len(list))
	for _, info := range list {
		stats = append(stats, &pb.AnchorWeeklyStats{
			Uid:                info.Uid,
			AnchorIncome:       info.TotalAnchorIncome,
			ChannelFee:         info.TotalChannelFee,
			LiveValidMinutes:   info.LiveValidMinutes,
			ValidDaysCnt:       info.TotalValidDays,
			WeekActiveFans:     info.TotalActiveFans,
			WeekSpFans:         info.TotalSpFans,
			FansSendFee:        info.TotalFansSendFee,
			WeekFollowCnt:      info.TotalFollowCnt,
			NewAddFans:         info.TotalNewFans,
			LiveMinutes:        info.TotalLiveMinutes,
			AgentUid:           info.AgentUid,
			AnchorKnightIncome: info.TotalKnightIncome,
			LiveActiveDays:     info.TotalActiveDays,
			GameFee:            info.TotalGameFee,
			GameChannelFee:     info.TotalGameChannelFee,
			GameMin:            info.TotalGameMin,
			GameActiveDays:     info.TotalGameActiveDays,
			VirtualFee:         info.TotalVirtualFee,
			VirtualIncome:      info.TotalVirtualIncome,
			VirtualMin:         info.TotalVirtualMin,
			VirtualActiveDays:  info.TotalVirtualActiveDays,
			ConsumerCnt:        info.TotalConsumerCnt,
		})
	}

	out.List = stats
	out.TotalCnt = totalCnt
	log.DebugWithCtx(ctx, "GetAnchorWeeklyStatsList begin in:%v out:%v", in, out)
	return out, nil
}

func (s *ChannelLiveStatisticsServer) GetGuildMonthlyStats(ctx context.Context, in *pb.GetGuildMonthlyStatsReq) (*pb.GetGuildMonthlyStatsResp, error) {
	out := &pb.GetGuildMonthlyStatsResp{}
	guildId := in.GetGuildId()
	t := time.Unix(int64(in.GetMonthTime()), 0)
	yearMonth := mysql.GetYearMonth(t)
	var liveAnchorCnt, validAnchorCnt, newValidAnchorCnt, potentialAnchorCnt, newAddAnchorCnt uint32
	var qualityAnchorCnt, activeAnchorCnt, newActiveAnchorCnt, highIncomeAnchorCnt, professionPracCnt uint32
	var totalChannelFee, totalAnchorIncome, totalKnightIncome, totalPkgFee uint64
	err := mapreduce.Finish(func() error {
		var err error
		liveAnchorCnt, totalChannelFee, totalAnchorIncome, totalKnightIncome, totalPkgFee, err = s.mysqlStore.GetGuildMonthlyStats(ctx, guildId, yearMonth)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetGuildMonthlyStats fail to GetGuildMonthlyStats . in:%+v, err:%v", in, err)
			return err
		}
		return nil
	}, func() error {
		// 有效主播个数
		var err error
		validAnchorCnt, err = s.mysqlStore.GetGuildMonthlyAnchorCntByCondition(ctx, guildId, yearMonth, pb.AnchorFlag_Valid)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetGuildMonthlyStats fail to GetGuildMonthlyAnchorCntByCondition. in:%+v, err:%v", in, err)
			return err
		}
		return nil
	}, func() error {
		// 新增有效主播个数
		var err error
		newValidAnchorCnt, err = s.mysqlStore.GetGuildMonthlyAnchorCntByCondition(ctx, guildId, yearMonth, pb.AnchorFlag_NewValid)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetGuildMonthlyStats fail to GetGuildMonthlyAnchorCntByCondition. in:%+v, err:%v", in, err)
			return err
		}
		return nil
	}, func() error {
		// 潜力主播数
		var err error
		potentialAnchorCnt, err = s.mysqlStore.GetGuildMonthlyAnchorCntByCondition(ctx, guildId, yearMonth, pb.AnchorFlag_Potential)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetGuildMonthlyStats fail to GetGuildMonthlyAnchorCntByCondition. in:%+v, err:%v", in, err)
			return err
		}
		return nil
	}, func() error {
		var err error
		// 新增主播数
		newAddAnchorCnt, err = s.mysqlStore.GetGuildMonthlyAnchorCntByCondition(ctx, guildId, yearMonth, pb.AnchorFlag_NewAdd)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetGuildMonthlyStats fail to GetGuildMonthlyAnchorCntByCondition. in:%+v, err:%v", in, err)
			return err
		}
		return nil
	}, func() error {
		var err error
		// 优质主播数
		qualityAnchorCnt, err = s.mysqlStore.GetGuildMonthlyAnchorCntByCondition(ctx, guildId, yearMonth, pb.AnchorFlag_QUALITY)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetGuildMonthlyStats fail to GetGuildMonthlyAnchorCntByCondition. in:%+v, err:%v", in, err)
			return err
		}
		return nil
	}, func() error {
		var err error
		// 活跃主播数
		activeAnchorCnt, err = s.mysqlStore.GetGuildMonthlyAnchorCntByCondition(ctx, guildId, yearMonth, pb.AnchorFlag_ACTIVE)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetGuildMonthlyStats fail to GetGuildMonthlyAnchorCntByCondition. in:%+v, err:%v", in, err)
			return err
		}
		return nil
	}, func() error {
		var err error
		// 新增活跃主播数
		newActiveAnchorCnt, err = s.mysqlStore.GetGuildMonthlyAnchorCntByCondition(ctx, guildId, yearMonth, pb.AnchorFlag_NEW_ACTIVE)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetGuildMonthlyStats fail to GetGuildMonthlyAnchorCntByCondition. in:%+v, err:%v", in, err)
			return err
		}
		return nil
	}, func() error {
		var err error
		// 高收入主播数
		highIncomeAnchorCnt, err = s.mysqlStore.GetGuildMonthlyAnchorCntByCondition(ctx, guildId, yearMonth, pb.AnchorFlag_HIGH_INCOME)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetGuildMonthlyStats fail to GetGuildMonthlyAnchorCntByCondition. in:%+v, err:%v", in, err)
			return err
		}
		return nil
	}, func() error {
		var err error
		// 专业从业者数
		professionPracCnt, err = s.mysqlStore.GetGuildMonthlyAnchorCntByCondition(ctx, guildId, yearMonth, pb.AnchorFlag_Profession_Prac)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetGuildMonthlyStats fail to GetGuildMonthlyAnchorCntByCondition. in:%+v, err:%v", in, err)
			return err
		}
		return nil
	})

	out.Stats = &pb.GuildMonthlyStats{
		LiveAnchorCnt:       liveAnchorCnt,
		TotalAnchorIncome:   totalAnchorIncome,
		TotalChannelFee:     totalChannelFee,
		ValidAnchorCnt:      validAnchorCnt,
		NewValidAnchorCnt:   newValidAnchorCnt,
		PotentialAnchorCnt:  potentialAnchorCnt,
		NewAddAnchorCnt:     newAddAnchorCnt,
		AnchorKnightIncome:  totalKnightIncome,
		QualityAnchorCnt:    qualityAnchorCnt,
		ActiveAnchorCnt:     activeAnchorCnt,
		NewActiveAnchorCnt:  newActiveAnchorCnt,
		HighIncomeAnchorCnt: highIncomeAnchorCnt,
		ChannelPkgFee:       totalPkgFee,
		ProfessionPracCnt:   professionPracCnt,
	}

	log.DebugWithCtx(ctx, "GetGuildMonthlyStats in:%+v, out:%+v", in, out)
	return out, err
}

func (s *ChannelLiveStatisticsServer) GetAnchorMonthlyStats(ctx context.Context, in *pb.GetAnchorMonthlyStatsReq) (*pb.GetAnchorMonthlyStatsResp, error) {
	out := &pb.GetAnchorMonthlyStatsResp{}
	guildId := in.GetGuildId()
	anchorUid := in.GetAnchorUid()
	monthTime := time.Unix(int64(in.GetMonthTime()), 0)

	record, exist, err := s.mysqlStore.GetAnchorMonthlyRecord(ctx, anchorUid, guildId, mysql.GetYearMonth(monthTime))
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAnchorMonthlyStats fail to GetAnchorMonthlyRecord. in:%+v, err:%v", in, err)
		return out, err
	}

	if exist {
		isProfession := false

		if record.LiveActiveCnt >= mysql.ProfessionMinMonthLiveActiveCnt && record.AnchorIncome+record.KnightIncome >= mysql.ProfessionMinMonthIncome {
			isProfession = true
		}

		out.Stats = &pb.AnchorMonthlyStats{
			Uid:                record.Uid,
			ChannelFee:         record.ChannelFee,
			AnchorIncome:       record.AnchorIncome,
			DayLiveValidCnt:    record.DayLiveValidCnt,
			LiveValidMinutes:   record.LiveValidMinutes,
			NewFlag:            record.Flag,
			AnchorKnightIncome: record.KnightIncome,
			LiveActiveDays:     record.LiveActiveCnt,
			IsQualityAnchor:    record.IsQuality == 1,
			IsActiveAnchor:     record.IsActive == 1,
			ChannelPkgFee:      record.ChannelPkgFee,
			IsProfessionPrac:   isProfession,
		}
	}

	log.DebugWithCtx(ctx, "GetAnchorMonthlyStats in:%+v, out:%+v", in, out)
	return out, err
}

func (s *ChannelLiveStatisticsServer) GetGuildAnchorMonthlyStatsList(ctx context.Context, in *pb.GetGuildAnchorMonthlyStatsListReq) (*pb.GetGuildAnchorMonthlyStatsListResp, error) {
	out := &pb.GetGuildAnchorMonthlyStatsListResp{}
	guildId := in.GetGuildId()
	monthTime := time.Unix(int64(in.GetMonthTime()), 0)

	recordList, err := s.mysqlStore.GetGuildMonthlyAnchorStatsList(ctx, guildId, in.GetAgentUid(), in.GetCondition(), in.GetBegin(), in.GetLimit(), monthTime)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetGuildAnchorMonthlyStatsList fail to GetGuildMonthlyAnchorStatsList. in:%+v, err:%v", in, err)
		return out, err
	}

	// 总数
	totalCnt, err := s.mysqlStore.GetGuildMonthlyAnchorCnt(ctx, guildId, in.GetAgentUid(), mysql.GetYearMonth(monthTime), pb.AnchorFlag(in.GetCondition()))
	if err != nil {
		log.ErrorWithCtx(ctx, "GetGuildMonthlyStats fail to GetGuildMonthlyAnchorCnt. in:%+v, err:%v", in, err)
		return out, err
	}

	anchorStatsList := make([]*pb.AnchorMonthlyStats, 0, in.GetLimit())
	for _, record := range recordList {
		isPro := false
		if record.LiveActiveCnt >= mysql.ProfessionMinMonthLiveActiveCnt && record.AnchorIncome+record.KnightIncome >= mysql.ProfessionMinMonthIncome {
			isPro = true
		}

		anchorStatsList = append(anchorStatsList, &pb.AnchorMonthlyStats{
			Uid:                record.Uid,
			ChannelFee:         record.ChannelFee,
			AnchorIncome:       record.AnchorIncome,
			DayLiveValidCnt:    record.DayLiveValidCnt,
			LiveValidMinutes:   record.LiveValidMinutes,
			NewFlag:            record.Flag,
			LastMonthFansCnt:   record.LastMonthFansCnt,
			NewFansCnt:         record.NewFansCnt,
			AnchorKnightIncome: record.KnightIncome,
			ChannelPkgFee:      record.ChannelPkgFee,
			AnchorPkgIncome:    record.AnchorPkgIncome,
			GameFee:            record.GameFee,
			GameChannelFee:     record.GameChannelFee,
			GameMin:            record.GameMin,
			GameActiveDays:     record.GameActiveDays,
			VirtualFee:         record.VirtualFee,
			VirtualIncome:      record.VirtualIncome,
			VirtualMin:         record.VirtualMin,
			VirtualActiveDays:  record.VirtualActiveDays,
			LiveActiveDays:     record.LiveActiveCnt,
			IsProfessionPrac:   isPro,
			ConsumerCnt:        record.ConsumerCnt,
		})
	}

	out.StatsList = anchorStatsList
	out.TotalCnt = totalCnt

	log.DebugWithCtx(ctx, "GetGuildAnchorMonthlyStatsList in:%+v, out:%+v", in, out)
	return out, nil
}

func (s *ChannelLiveStatisticsServer) BatchGetAnchorMonthlyStats(ctx context.Context, in *pb.BatchGetAnchorMonthlyStatsReq) (*pb.BatchGetAnchorMonthlyStatsResp, error) {
	out := &pb.BatchGetAnchorMonthlyStatsResp{}

	log.DebugWithCtx(ctx, "BatchGetAnchorMonthlyStats begin in:%v", in)
	monthTm := time.Unix(int64(in.GetMonthTs()), 0)
	recordList, err := s.mysqlStore.BatchGetAnchorMonthlyStats(ctx, in.GetUidList(), in.GetGuildId(), monthTm)
	if err != nil {
		log.ErrorWithCtx(ctx, "BatchGetAnchorMonthlyStats failed in:%v err:%v", in, err)
		return out, err
	}

	anchorStatsList := make([]*pb.AnchorMonthlyStats, 0)
	for _, record := range recordList {
		isPro := false
		if record.LiveActiveCnt >= mysql.ProfessionMinMonthLiveActiveCnt && record.AnchorIncome+record.KnightIncome >= mysql.ProfessionMinMonthIncome {
			isPro = true
		}

		anchorStatsList = append(anchorStatsList, &pb.AnchorMonthlyStats{
			Uid:                record.Uid,
			ChannelFee:         record.ChannelFee,
			AnchorIncome:       record.AnchorIncome,
			DayLiveValidCnt:    record.DayLiveValidCnt,
			LiveValidMinutes:   record.LiveValidMinutes,
			NewFlag:            record.Flag,
			LastMonthFansCnt:   record.LastMonthFansCnt,
			NewFansCnt:         record.NewFansCnt,
			LiveMinutes:        record.LiveMinutes,
			AnchorKnightIncome: record.KnightIncome,
			ChannelPkgFee:      record.ChannelPkgFee,
			AnchorPkgIncome:    record.AnchorPkgIncome,
			GameFee:            record.GameFee,
			GameChannelFee:     record.GameChannelFee,
			GameMin:            record.GameMin,
			GameActiveDays:     record.GameActiveDays,
			VirtualFee:         record.VirtualFee,
			VirtualIncome:      record.VirtualIncome,
			VirtualMin:         record.VirtualMin,
			VirtualActiveDays:  record.VirtualActiveDays,
			LiveActiveDays:     record.LiveActiveCnt,
			ConsumerCnt:        record.ConsumerCnt,
			IsProfessionPrac:   isPro,
		})
	}

	out.StatsList = anchorStatsList
	log.DebugWithCtx(ctx, "BatchGetAnchorMonthlyStats begin in:%v out:%v", in, out)
	return out, nil
}

func (s *ChannelLiveStatisticsServer) GetAnchorMonthlyTotalStats(ctx context.Context, in *pb.GetAnchorMonthlyTotalStatsReq) (*pb.GetAnchorMonthlyTotalStatsResp, error) {
	out := &pb.GetAnchorMonthlyTotalStatsResp{}
	anchorUid := in.GetAnchorUid()

	list, err := s.mysqlStore.GetAnchorMonthlyTotalStats(ctx, anchorUid, in.GetMonthTimeList())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAnchorMonthlyTotalStats fail to GetAnchorMonthlyTotalStats. in:%+v, err:%v", in, err)
		return out, err
	}

	for _, info := range list {
		monthTime, _ := time.ParseInLocation("2006-01", info.Yearmonth, time.Local)

		r := &pb.MonthlyTotalStats{
			MonthTime:       uint32(monthTime.Unix()),
			AnchorIncome:    info.Income,
			DayLiveValidCnt: info.ValidDayCnt,
		}

		out.Stats = append(out.Stats, r)
	}

	log.DebugWithCtx(ctx, "GetAnchorMonthlyTotalStats in:%+v, out:%+v", in, out)
	return out, nil
}

func (s *ChannelLiveStatisticsServer) GetAnchorMonthlyStatsList(ctx context.Context, in *pb.GetAnchorMonthlyStatsListReq) (*pb.GetAnchorMonthlyStatsListResp, error) {
	out := &pb.GetAnchorMonthlyStatsListResp{}

	log.DebugWithCtx(ctx, "GetAnchorMonthlyStatsList begin in:%v", in)
	beginMonth := time.Unix(int64(in.GetBeginMonth()), 0)
	endMonth := time.Unix(int64(in.GetEndMonth()), 0)

	recordList, err := s.mysqlStore.GetAnchorMonthlyStatsList(ctx, in.GetGuildId(), in.GetAgentUid(), in.GetAnchorList(), beginMonth, endMonth, in.GetOffset(), in.GetLimit())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAnchorMonthlyStatsList failed in:%v err:%v", in, err)
		return out, err
	}

	totalCnt, err := s.mysqlStore.GetGuildMonthlyStatsTotalCnt(ctx, in.GetGuildId(), in.GetAgentUid(), in.GetAnchorList(), beginMonth, endMonth)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAnchorMonthlyStatsList failed in:%v err:%v", in, err)
		return out, err
	}

	anchorStatsList := make([]*pb.AnchorMonthlyStats, 0)
	for _, record := range recordList {
		dateTime, _ := time.Parse("2006-01", record.Yearmonth)
		isNewActive := false
		isProfession := false
		if record.Flag == 1 && record.LiveValidMinutes >= mysql.NewActiveAnchorValidMinutes && record.ChannelFee >= mysql.NewActiveAnchorChannelFee {
			isNewActive = true
		}

		if record.LiveActiveCnt >= mysql.ProfessionMinMonthLiveActiveCnt && record.AnchorIncome+record.KnightIncome >= mysql.ProfessionMinMonthIncome {
			isProfession = true
		}

		anchorStatsList = append(anchorStatsList, &pb.AnchorMonthlyStats{
			Uid:                record.Uid,
			ChannelFee:         record.ChannelFee,
			AnchorIncome:       record.AnchorIncome,
			DayLiveValidCnt:    record.DayLiveValidCnt,
			LiveValidMinutes:   record.LiveValidMinutes,
			NewFlag:            record.Flag,
			LastMonthFansCnt:   record.LastMonthFansCnt,
			NewFansCnt:         record.NewFansCnt,
			ActiveFans:         record.ActiveFans,
			SpFans:             record.SpFans,
			FansSendFee:        record.FansSendFee,
			FollowCnt:          record.FollowCnt,
			DateTs:             uint32(dateTime.Unix()),
			LiveMinutes:        record.LiveMinutes,
			AgentUid:           record.AgentUid,
			AnchorKnightIncome: record.KnightIncome,
			LiveActiveDays:     record.LiveActiveCnt,
			IsActiveAnchor:     record.IsActive == 1,
			IsQualityAnchor:    record.IsQuality == 1,
			IsNewActiveAnchor:  isNewActive,
			IsProfessionPrac:   isProfession,
			GameFee:            record.GameFee,
			GameChannelFee:     record.GameChannelFee,
			GameMin:            record.GameMin,
			GameActiveDays:     record.GameActiveDays,
			VirtualFee:         record.VirtualFee,
			VirtualIncome:      record.VirtualIncome,
			VirtualMin:         record.VirtualMin,
			VirtualActiveDays:  record.VirtualActiveDays,
			ConsumerCnt:        record.ConsumerCnt,
		})
	}

	out.List = anchorStatsList
	out.TotalCnt = totalCnt
	log.DebugWithCtx(ctx, "GetAnchorMonthlyStatsList begin in:%v out:%v", in, out)
	return out, nil
}

func (s *ChannelLiveStatisticsServer) BatchGetAnchorMonthlyStatsWithDate(ctx context.Context, in *pb.BatchGetAnchorMonthlyStatsWithDateReq) (*pb.BatchGetAnchorMonthlyStatsWithDateResp, error) {
	out := &pb.BatchGetAnchorMonthlyStatsWithDateResp{}

	log.DebugWithCtx(ctx, "BatchGetAnchorMonthlyStatsWithDate begin in:%v", in)
	beginMonth := time.Unix(int64(in.GetBeginTs()), 0)
	endMonth := time.Unix(int64(in.GetEndTs()), 0)

	recordList, err := s.mysqlStore.BatchGetAnchorMonthlyStatsWithDate(ctx, in.GetGuildId(), in.GetAgentUid(), in.GetUidList(), beginMonth, endMonth)
	if err != nil {
		log.ErrorWithCtx(ctx, "BatchGetAnchorMonthlyStatsWithDate failed in:%v err:%v", in, err)
		return out, err
	}

	anchorStatsList := make([]*pb.AnchorMonthlyStats, 0)
	for _, record := range recordList {
		dateTime, _ := time.Parse("2006-01", record.Yearmonth)
		anchorStatsList = append(anchorStatsList, &pb.AnchorMonthlyStats{
			Uid:                record.Uid,
			ChannelFee:         record.ChannelFee,
			AnchorIncome:       record.AnchorIncome,
			DayLiveValidCnt:    record.DayLiveValidCnt,
			LiveValidMinutes:   record.LiveValidMinutes,
			NewFlag:            record.Flag,
			LastMonthFansCnt:   record.LastMonthFansCnt,
			NewFansCnt:         record.NewFansCnt,
			ActiveFans:         record.ActiveFans,
			SpFans:             record.SpFans,
			FansSendFee:        record.FansSendFee,
			FollowCnt:          record.FollowCnt,
			DateTs:             uint32(dateTime.Unix()),
			LiveMinutes:        record.LiveMinutes,
			AgentUid:           record.AgentUid,
			AnchorKnightIncome: record.KnightIncome,
			GameFee:            record.GameFee,
			GameChannelFee:     record.GameChannelFee,
			GameMin:            record.GameMin,
			GameActiveDays:     record.GameActiveDays,
			VirtualFee:         record.VirtualFee,
			VirtualIncome:      record.VirtualIncome,
			VirtualMin:         record.VirtualMin,
			VirtualActiveDays:  record.VirtualActiveDays,
			LiveActiveDays:     record.LiveActiveCnt,
			ConsumerCnt:        record.ConsumerCnt,
		})
	}

	out.StatsList = anchorStatsList

	log.DebugWithCtx(ctx, "BatchGetAnchorMonthlyStatsWithDate begin in:%v out:%v", in, out)
	return out, nil
}

func (s *ChannelLiveStatisticsServer) UpdateGuildAnchorAgentInfo(ctx context.Context, in *pb.UpdateGuildAnchorAgentInfoReq) (*pb.UpdateGuildAnchorAgentInfoResp, error) {
	out := &pb.UpdateGuildAnchorAgentInfoResp{}

	log.InfoWithCtx(ctx, "UpdateGuildAnchorAgentInfo begin in:%v", in)
	err := s.mgr.UpdateGuildAnchorAgentInfo(ctx, in.GetGuildId(), in.GetAgentUid(), in.GetAnchorList())
	if err != nil {
		log.ErrorWithCtx(ctx, "UpdateGuildAnchorAgentInfo failed in:%v err:%v", in, err)
		return out, err
	}

	log.InfoWithCtx(ctx, "UpdateGuildAnchorAgentInfo end in:%v out:%v", in, out)
	return out, nil
}

func (s *ChannelLiveStatisticsServer) BatchGetAnchorMonthlyStatsByUid(ctx context.Context, in *pb.BatchGetAnchorMonthlyStatsByUidReq) (*pb.BatchGetAnchorMonthlyStatsByUidResp, error) {
	out := &pb.BatchGetAnchorMonthlyStatsByUidResp{}

	list, err := s.mysqlStore.BatchGetAnchorMonthlyStatsByUid(ctx, in.AnchorUids, in.MonthTime)
	if err != nil {
		log.ErrorWithCtx(ctx, "BatchGetAnchorMonthlyStatsByUid fail %v, in %+v", err, in)
		return out, err
	}

	for _, info := range list {
		out.List = append(out.List, &pb.BatchGetAnchorMonthlyStatsByUidRespMonthStats{
			AnchorUid:         info.Uid,
			Yearmonth:         info.Yearmonth,
			AnchorIncome:      info.Income,
			NewFansCnt:        info.NewFansCnt,
			LiveActiveCnt:     info.LiveActiveCnt,
			ConsumerCnt:       info.ConsumerCnt,
			KnightIncome:      info.KnightIncome,
			AudienceCnt:       info.AudienceCnt,
			LiveValidMinutes:  info.LiveValidMinutes,
			DayLiveValidCnt:   info.DayLiveValidCnt,
			ChannelFee:        info.ChannelFee,
			FollowCnt:         info.FollowCnt,
			GameFee:           info.GameFee,
			GameChannelFee:    info.GameChannelFee,
			GameMin:           info.GameMin,
			GameActiveDays:    info.GameActiveDays,
			VirtualFee:        info.VirtualFee,
			VirtualIncome:     info.VirtualIncome,
			VirtualMin:        info.VirtualMin,
			VirtualActiveDays: info.VirtualActiveDays,
		})
	}

	log.DebugWithCtx(ctx, "BatchGetAnchorMonthlyStatsByUid in %+v, out %+v", in, out)
	return out, nil
}

func (s *ChannelLiveStatisticsServer) getGuildAllAnchorList(ctx context.Context, req *pb.GetGuildAnchorListReq) (*pb.GetGuildAnchorListResp, error) {
	resp := &pb.GetGuildAnchorListResp{
		AnchorList: make([]*pb.AnchorMatchData, 0),
	}

	infoList := make([]*mysql.AnchorBaseInfo, 0)
	var totalCnt, totalSignCnt, totalLiveCnt uint32

	err := mapreduce.Finish(func() error {
		var err error
		infoList, err = s.mysqlStore.GetAnchorBaseInfoList(ctx, req.GetGuildId(), req.GetAgentUid(), 0, 0, (req.GetPage()-1)*req.GetPageSize(),
			req.GetPageSize(), time.Time{}, req.GetAnchorUid())
		if err != nil {
			log.ErrorWithCtx(ctx, "getGuildAllAnchorList GetAnchorBaseInfoList failed req:%v err:%v", req, err)
			return err
		}
		return nil
	}, func() error {
		var err error
		totalCnt, err = s.mysqlStore.GetAnchorBaseInfoTotalCnt(ctx, req.GetGuildId(), req.GetAgentUid(), 0, 0, time.Time{}, req.GetAnchorUid())
		if err != nil {
			log.ErrorWithCtx(ctx, "getGuildAllAnchorList  GetAnchorBaseInfoTotalCnt failed req:%v err:%v", req, err)
			return err
		}
		return nil
	}, func() error {
		var err error

		agentUid := req.GetAgentUid()
		if req.GetUid() != agentUid {
			// 不是经纪人请求，会长搜索经纪人
			agentUid = 0
		}

		totalSignCnt, err = s.mysqlStore.GetAnchorBaseInfoTotalCnt(ctx, req.GetGuildId(), agentUid, 0, 0, time.Time{}, 0)
		if err != nil {
			log.ErrorWithCtx(ctx, "getGuildAllAnchorList GetAnchorBaseInfoTotalCnt failed req:%v err:%v", req, err)
			return err
		}
		return nil
	}, func() error {
		var err error

		agentUid := req.GetAgentUid()
		if req.GetUid() != agentUid {
			// 不是经纪人请求，会长搜索经纪人
			agentUid = 0
		}

		totalLiveCnt, err = s.mysqlStore.GetAnchorBaseInfoTotalCnt(ctx, req.GetGuildId(), agentUid, uint32(pb.AnchorFlag_Is_Living), 0, time.Time{}, 0)
		if err != nil {
			log.ErrorWithCtx(ctx, "getGuildAllAnchorList GetAnchorBaseInfoTotalCnt failed req:%v err:%v", req, err)
			return err
		}
		return nil
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "getGuildAllAnchorList mapreduce.Finish failed req:%v err:%v", req, err)
		return resp, err
	}

	liveIdList := make([]uint32, 0)
	for _, info := range infoList {
		if info.ChannelLiveId != 0 {
			liveIdList = append(liveIdList, info.ChannelLiveId)
		}
	}

	matchRecordList, err := s.mysqlStore.BatchGetAnchorMatchRecord(ctx, req.GetGuildId(), liveIdList)
	if err != nil {
		log.ErrorWithCtx(ctx, "getGuildAllAnchorList  BatchGetAnchorMatchRecord failed req:%v err:%v", req, err)
		return resp, err
	}

	mapId2Record := make(map[uint32]*mysql.AnchorMatchRecord, 0)
	for _, matchRecord := range matchRecordList {
		mapId2Record[matchRecord.ChannelLiveId] = matchRecord
	}

	for _, info := range infoList {
		baseInfo := &pb.AnchorBaseInfo{
			Uid:           info.Uid,
			SignGuildId:   info.SignGuildId,
			LiveRoomId:    info.LiveRoomId,
			LastLiveAt:    uint32(info.LastLiveAt.Unix()),
			AgentUid:      info.AgentUid,
			FirstLiveTs:   uint32(info.FirstLiveAt.Unix()),
			ChannelLiveId: info.ChannelLiveId,
		}

		matchData := &pb.MatchData{}

		if matchRecord, ok := mapId2Record[info.ChannelLiveId]; ok {
			matchData.ChannelFee = matchRecord.ChannelFee
			matchData.AnchorIncome = matchRecord.AnchorIncome
			matchData.LiveMin = matchRecord.LiveMinutes
			if matchRecord.ChannelFee != 0 {
				matchData.PkgGiftRatio = float32(matchRecord.ChannelPkgFee) / float32(matchRecord.ChannelFee)
			}
			matchData.LiveTs = uint32(matchRecord.CreateTm.Unix())
		}

		resp.AnchorList = append(resp.AnchorList, &pb.AnchorMatchData{
			BaseInfo:  baseInfo,
			MatchData: matchData,
		})
	}

	resp.TotalCnt = totalCnt
	resp.LiveAnchorCnt = totalLiveCnt
	resp.GuildAnchorCnt = totalSignCnt

	log.DebugWithCtx(ctx, "getGuildAllAnchorList end req:%v resp:%v", req, resp)
	return resp, nil
}

func (s *ChannelLiveStatisticsServer) getGuildLiveAnchorList(ctx context.Context, req *pb.GetGuildAnchorListReq) (*pb.GetGuildAnchorListResp, error) {
	resp := &pb.GetGuildAnchorListResp{
		AnchorList: make([]*pb.AnchorMatchData, 0),
	}

	infoList := make([]*mysql.AnchorMatchRecord, 0)
	var totalCnt, totalSignCnt, totalLiveCnt uint32

	err := mapreduce.Finish(func() error {
		var err error
		infoList, err = s.mysqlStore.GetGuildAnchorList(ctx, req.GetGuildId(), req.GetAnchorUid(), req.GetAgentUid(), req.GetLiveType(), req.GetSortType(),
			req.GetSort(), (req.GetPage()-1)*req.GetPageSize(), req.GetPageSize())
		if err != nil {
			log.ErrorWithCtx(ctx, "getGuildLiveAnchorList GetGuildAnchorList failed req:%v err:%v", req, err)
			return err
		}
		return nil
	}, func() error {
		var err error
		totalCnt, err = s.mysqlStore.GetGuildAnchorTotalCnt(ctx, req.GetGuildId(), req.GetAnchorUid(), req.GetAgentUid(), req.GetLiveType())
		if err != nil {
			log.ErrorWithCtx(ctx, "getGuildLiveAnchorList GetGuildAnchorTotalCnt failed req:%v err:%v", req, err)
			return err
		}
		return nil
	}, func() error {
		var err error

		agentUid := req.GetAgentUid()
		if req.GetUid() != agentUid {
			// 不是经纪人请求，会长搜索经纪人
			agentUid = 0
		}

		totalSignCnt, err = s.mysqlStore.GetAnchorBaseInfoTotalCnt(ctx, req.GetGuildId(), agentUid, 0, 0, time.Time{}, 0)
		if err != nil {
			log.ErrorWithCtx(ctx, "getGuildLiveAnchorList GetAnchorBaseInfoTotalCnt failed req:%v err:%v", req, err)
			return err
		}
		return nil
	}, func() error {
		var err error

		agentUid := req.GetAgentUid()
		if req.GetUid() != agentUid {
			// 不是经纪人请求，会长搜索经纪人
			agentUid = 0
		}

		totalLiveCnt, err = s.mysqlStore.GetGuildAnchorTotalCnt(ctx, req.GetGuildId(), 0, agentUid, req.GetLiveType())
		if err != nil {
			log.ErrorWithCtx(ctx, "getGuildLiveAnchorList GetGuildAnchorTotalCnt failed req:%v err:%v", req, err)
			return err
		}
		return nil
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "getGuildLiveAnchorList mapreduce.Finish failed req:%v err:%v", req, err)
		return resp, err
	}

	uidList := make([]uint32, 0)
	for _, info := range infoList {
		uidList = append(uidList, info.Uid)
	}

	baseInfoList, err := s.mysqlStore.BatchGetAnchorBaseInfo(ctx, uidList)
	if err != nil {
		log.ErrorWithCtx(ctx, "getGuildLiveAnchorList  BatchGetAnchorMatchRecord failed req:%v err:%v", req, err)
		return resp, err
	}

	mapUid2Info := make(map[uint32]*mysql.AnchorBaseInfo, 0)
	for _, info := range baseInfoList {
		mapUid2Info[info.Uid] = info
	}

	for _, info := range infoList {
		anchorBaseInfo := &pb.AnchorBaseInfo{
			Uid:           info.Uid,
			SignGuildId:   info.SignGuildId,
			AgentUid:      info.AgentUid,
			ChannelLiveId: info.ChannelLiveId,
		}

		if baseInfo, ok := mapUid2Info[info.Uid]; ok {
			anchorBaseInfo.LiveRoomId = baseInfo.LiveRoomId
			anchorBaseInfo.LastLiveAt = uint32(baseInfo.LastLiveAt.Unix())
			anchorBaseInfo.FirstLiveTs = uint32(baseInfo.FirstLiveAt.Unix())
		}

		matchData := &pb.MatchData{
			ChannelFee:   info.ChannelFee,
			AnchorIncome: info.AnchorIncome,
			LiveMin:      info.LiveMinutes,
			LiveTs:       uint32(info.CreateTm.Unix()),
		}

		if info.ChannelFee != 0 {
			matchData.PkgGiftRatio = float32(info.ChannelPkgFee) / float32(info.ChannelFee)
		}

		resp.AnchorList = append(resp.AnchorList, &pb.AnchorMatchData{
			BaseInfo:  anchorBaseInfo,
			MatchData: matchData,
		})
	}

	resp.TotalCnt = totalCnt
	resp.LiveAnchorCnt = totalLiveCnt
	resp.GuildAnchorCnt = totalSignCnt

	log.DebugWithCtx(ctx, "getGuildLiveAnchorList end req:%v resp:%v", req, resp)
	return resp, nil
}

func (s *ChannelLiveStatisticsServer) GetGuildAnchorList(ctx context.Context, req *pb.GetGuildAnchorListReq) (*pb.GetGuildAnchorListResp, error) {
	resp := &pb.GetGuildAnchorListResp{}
	var err error

	if req.GetPage() <= 0 {
		log.ErrorWithCtx(ctx, "GetGuildAnchorList invalid param req:%v", req)
		return resp, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}

	if req.GetLiveType() == 0 {
		resp, err = s.getGuildAllAnchorList(ctx, req)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetGuildAnchorList getGuildAllAnchorList failed req:%v err:%v", req, err)
			return resp, err
		}
	} else {
		resp, err = s.getGuildLiveAnchorList(ctx, req)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetGuildAnchorList getGuildLiveAnchorList failed req:%v err:%v", req, err)
			return resp, err
		}
	}

	log.DebugWithCtx(ctx, "GetGuildAnchorList end req:%v resp:%v", req, resp)
	return resp, err
}

func (s ChannelLiveStatisticsServer) GetGuildOperationalCapabilities(ctx context.Context, in *pb.GetGuildOperationalCapabilitiesReq) (*pb.GetGuildOperationalCapabilitiesResp, error) {
	out := &pb.GetGuildOperationalCapabilitiesResp{}

	log.InfoWithCtx(ctx, "GetGuildOperationalCapabilities begin in:%v", in)
	endTime := time.Unix(in.GetEndDate(), 0).Format("2006-01-02")
	dataList, err := s.mgr.GetGuildOperationalCapabilities(ctx, in.GetGuildId(), in.GetRevenueLevel(), endTime)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetGuildOperationalCapabilities failed in:%v err:%v", in, err)
		return out, err
	}
	for _, data := range dataList {
		out.DataList = append(out.DataList, &pb.GuildOperationalData{
			GuildId:                 data.GuildID,
			NewSignAnchorCnt:        data.NewSignAnchorCnt,
			EnabledLiveUserCount:    data.EnabledLiveUserCount,
			EnabledLiveNewUserCount: data.EnabledLiveNewUserCount,
			NewActiveAnchorCnt:      data.NewActiveAnchorCnt,
			ProAnchorCnt:            data.ProAnchorCnt,
			MatureAnchorCnt:         data.MatureAnchorCnt,
			PotActiveAnchorCnt:      data.PotActiveAnchorCnt,
			NewSignProAnchor:        data.NewSignProAnchor,
			RevenueLevel:            data.RevenueLevel,
			Revenue:                 data.Revenue,
			RevenueScore:            data.RevenueScore,
			ProAnchorScore:          data.ProAnchorScore,
			NewActiveAnchorScore:    data.NewActiveAnchorScore,
			MatureAnchorScore:       data.MatureAnchorScore,
			PotActiveAnchorScore:    data.PotActiveAnchorScore,
			TotalScore:              data.TotalScore,
			RevenueTime:             uint32(data.RevenueUpdateTime.Unix()),
		})
	}

	log.InfoWithCtx(ctx, "GetGuildOperationalCapabilities end in:%v out:%v", in, out)
	return out, nil
}

func (s *ChannelLiveStatisticsServer) UpdateGuildOperationalCapabilities(ctx context.Context, in *pb.UpdateGuildOperationalCapabilitiesReq) (*pb.UpdateGuildOperationalCapabilitiesResp, error) {
	out := &pb.UpdateGuildOperationalCapabilitiesResp{}
	s.mgr.UpdateGuildOperationalCapabilities(ctx, in)

	log.InfoWithCtx(ctx, "UpdateGuildOperationalCapabilities end in:%v out:%v", in, out)
	return out, nil

}

func (s *ChannelLiveStatisticsServer) GetAnchorMatchList(ctx context.Context, req *pb.GetAnchorMatchListReq) (*pb.GetAnchorMatchListResp, error) {
	resp := &pb.GetAnchorMatchListResp{
		DataList: make([]*pb.MatchData, 0),
	}

	if req.GetPage() <= 0 {
		log.ErrorWithCtx(ctx, "GetAnchorMatchList invalid param req:%v", req)
		return resp, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}

	infoList := make([]*mysql.AnchorMatchRecord, 0)
	var totalCnt uint32

	err := mapreduce.Finish(func() error {
		var err error
		infoList, err = s.mysqlStore.GetAnchorMatchList(ctx, req.GetGuild_Id(), req.GetAnchorUid(), req.GetBeginTs(), req.GetEndTs(), 0,
			(req.GetPage()-1)*req.GetPageSize(), req.GetPageSize())
		if err != nil {
			log.ErrorWithCtx(ctx, "GetAnchorMatchList GetAnchorMatchList failed req:%v err:%v", req, err)
			return err
		}
		return nil
	}, func() error {
		var err error
		totalCnt, err = s.mysqlStore.GetAnchorMatchTotalCnt(ctx, req.GetGuild_Id(), req.GetAnchorUid(), req.GetBeginTs(), req.GetEndTs(), 0)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetAnchorMatchList GetAnchorMatchList failed req:%v err:%v", req, err)
			return err
		}
		return nil
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAnchorMatchList mapreduce.Finish failed req:%v err:%v", req, err)
		return resp, err
	}

	baseInfo, _, err := s.mysqlStore.GetAnchorBaseInfo(ctx, req.GetAnchorUid())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAnchorMatchList GetAnchorBaseInfo failed req:%v err:%v", req, err)
		return resp, err
	}

	resp.AnchorInfo = &pb.AnchorBaseInfo{
		Uid:           baseInfo.Uid,
		SignGuildId:   baseInfo.SignGuildId,
		AgentUid:      baseInfo.AgentUid,
		ChannelLiveId: baseInfo.ChannelLiveId,
		LiveRoomId:    baseInfo.LiveRoomId,
		LastLiveAt:    uint32(baseInfo.LastLiveAt.Unix()),
		FirstLiveTs:   uint32(baseInfo.FirstLiveAt.Unix()),
	}

	for _, info := range infoList {
		matchData := &pb.MatchData{
			ChannelFee:   info.ChannelFee,
			AnchorIncome: info.AnchorIncome,
			LiveMin:      info.LiveMinutes,
			LiveTs:       uint32(info.CreateTm.Unix()),
		}

		if info.ChannelFee != 0 {
			matchData.PkgGiftRatio = float32(info.ChannelPkgFee) / float32(info.ChannelFee)
		}

		resp.DataList = append(resp.DataList, matchData)
	}

	resp.TotalCnt = totalCnt

	log.DebugWithCtx(ctx, "GetAnchorMatchList end req:%v resp:%v", req, resp)
	return resp, nil
}

func (s *ChannelLiveStatisticsServer) GetWeekBusinessAnalysis(ctx context.Context, req *pb.GetWeekBusinessAnalysisReq) (*pb.GetWeekBusinessAnalysisResp, error) {
	return s.mgr.GetWeekBusinessAnalysis(ctx, req)
}

func (s *ChannelLiveStatisticsServer) GetMonthBusinessAnalysis(ctx context.Context, req *pb.GetMonthBusinessAnalysisReq) (*pb.GetMonthBusinessAnalysisResp, error) {
	return s.mgr.GetMonthBusinessAnalysis(ctx, req)
}

func (s *ChannelLiveStatisticsServer) GetCurriculumMessageList(ctx context.Context, req *pb.GetCurriculumMessageListReq) (*pb.GetCurriculumMessageListResp, error) {
	return s.mgr.GetCurriculumMessageList(ctx, req)
}

func (s *ChannelLiveStatisticsServer) TriggerTimer(ctx context.Context, req *pb.TriggerTimerReq) (*pb.TriggerTimerResp, error) {
	resp := &pb.TriggerTimerResp{}

	switch req.GetTimerType() {
	case pb.TriggerTimerReq_Timer_Type_GuildWeekBusinessAnalysis:
		go s.mgr.GuildWeekBusinessAnalysis(context.Background(), time.Unix(int64(req.GetTs()), 0))
	case pb.TriggerTimerReq_Timer_Type_GuildMonthBusinessAnalysis:
		go s.mgr.GuildMonthBusinessAnalysis(context.Background(), time.Unix(int64(req.GetTs()), 0))
	default:
		log.ErrorWithCtx(ctx, "TriggerTimer invalid timer req:%v", req)
	}

	log.InfoWithCtx(ctx, "TriggerTimer begin req:%v", req)
	return resp, nil
}
