package user_present_value

import (
    "context"
    "gitlab.ttyuyin.com/tt-infra/tyr/log"
    "golang.52tt.com/services/channel-wedding/internal/model/comm"
)

// OnUserEnterChannel 用户进房处理
func (m *UserPresentVal) OnUserEnterChannel(ctx context.Context, cid, weddingId, uid uint32) error {
    val, err := m.cache.GetUserSendValue(ctx, cid, weddingId, uid)
    if err != nil {
        log.ErrorWithCtx(ctx, "OnUserEnterChannel failed to GetUserSendValue. cid:%d, weddingId:%d, uid:%d, err:%v", cid, weddingId, uid, err)
        return err
    }

    // 初始化用户在房间的送礼值
    err = m.cache.AddInRoomUserSendValue(ctx, cid, weddingId, uid, val)
    if err != nil {
        log.ErrorWithCtx(ctx, "OnUserEnterChannel failed to AddInRoomUserSendValue. cid:%d, weddingId:%d, uid:%d, err:%v", cid, weddingId, uid, err)
        return err
    }

    if val >= m.bc.GetMvpMinSendValue() {
        // 如果用户送礼值达到MVP最小值，尝试抢夺MVP
        _, _, err = m.TryGrabMvp(ctx, cid, weddingId, uid)
        if err != nil {
            log.ErrorWithCtx(ctx, "OnUserEnterChannel failed to TryGrabMvp. cid:%d, weddingId:%d, uid:%d, err:%v", cid, weddingId, uid, err)
            return err
        }
    }

    log.DebugWithCtx(ctx, "OnUserEnterChannel success. cid:%d, weddingId:%d, uid:%d", cid, weddingId, uid)
    return nil
}

// OnUserLeaveChannel 用户退房处理
func (m *UserPresentVal) OnUserLeaveChannel(ctx context.Context, cid, weddingId, uid uint32) error {
    err := m.cache.DelInRoomUserSendValue(ctx, cid, weddingId, uid)
    if err != nil {
        log.ErrorWithCtx(ctx, "OnUserLeaveChannel failed to DelInRoomUserSendValue. cid:%d, weddingId:%d, uid:%d, err:%v", cid, weddingId, uid, err)
        return err
    }

    // 放弃MVP竞争
    err = m.AbandonMvp(ctx, cid, weddingId, uid)
    if err != nil {
        log.ErrorWithCtx(ctx, "OnUserLeaveChannel failed to AbandonMvp. cid:%d, weddingId:%d, uid:%d, err:%v", cid, weddingId, uid, err)
        return err
    }

    log.DebugWithCtx(ctx, "OnUserLeaveChannel success. cid:%d, weddingId:%d, uid:%d", cid, weddingId, uid)
    return nil
}

// OnUserHoldMic 用户上麦处理
func (m *UserPresentVal) OnUserHoldMic(ctx context.Context, cid, weddingId, uid, micId uint32) error {
    err := m.cache.SetMicUser(ctx, cid, micId, uid)
    if err != nil {
        log.ErrorWithCtx(ctx, "OnUserHoldMic failed to SetMicUser. cid:%d, weddingId:%d, uid:%d, micId:%d, err:%v",
            cid, weddingId, uid, micId, err)
        return err
    }

    if weddingId == 0 {
        return nil
    }

    // 获取用户的收礼值
    val, err := m.cache.GetUserReceivedValue(ctx, cid, weddingId, uid)
    if err != nil {
        log.ErrorWithCtx(ctx, "OnUserHoldMic failed to GetUserReceivedValue. cid:%d, weddingId:%d, uid:%d, err:%v",
            cid, weddingId, uid, err)
        return err
    }

    // 发送麦上用户收礼值变更消息
    err = m.acLayerMgr.SendUserPresentCountChangeMsg(ctx, cid, weddingId, uid, val)
    if err != nil {
        log.ErrorWithCtx(ctx, "OnUserHoldMic failed to SendUserPresentCountChangeMsg. cid:%d, weddingId:%d, uid:%d, value:%d, micId:%d, err:%v",
            cid, weddingId, uid, val, micId, err)
        return err
    }

    if micId == comm.HostMicId {
        // 放弃MVP竞争
        err = m.AbandonMvp(ctx, cid, weddingId, uid)
        if err != nil {
            log.ErrorWithCtx(ctx, "OnUserHoldMic failed to AbandonMvp. cid:%d, weddingId:%d, uid:%d, value:%d, micId:%d, err:%v",
                cid, weddingId, uid, val, micId, err)
            return err
        }
    }

    if m.bc.GetWeddingPresentValLv(val) > 0 {
        // 收礼top1（人气王）是否需要更新
        err = m.TryUpdateReceivedTop(ctx, cid, weddingId, uid, val)
        if err != nil {
            log.ErrorWithCtx(ctx, "OnUserHoldMic failed to TryUpdateReceivedTop. cid:%d, weddingId:%d, uid:%d, value:%d, err:%v",
                cid, weddingId, uid, val, err)
            return err
        }
    }

    log.DebugWithCtx(ctx, "OnUserHoldMic success. cid:%d, weddingId:%d, uid:%d", cid, weddingId, uid)
    return nil
}

// OnUserReleaseMic 用户下麦处理
func (m *UserPresentVal) OnUserReleaseMic(ctx context.Context, cid, weddingId, uid, micId uint32) error {
    err := m.cache.DelMicUser(ctx, cid, micId)
    if err != nil {
        log.ErrorWithCtx(ctx, "OnUserReleaseMic failed to DelMicUser. cid:%d, weddingId:%d, uid:%d, micId:%d, err:%v",
            cid, weddingId, uid, micId, err)
        return err
    }

    if weddingId == 0 {
        return nil
    }

    // 收礼top1（人气王）是否需要更新
    err = m.TryUpdateReceivedTop(ctx, cid, weddingId, uid, 0)
    if err != nil {
        log.ErrorWithCtx(ctx, "OnUserReleaseMic failed to TryUpdateReceivedTop. cid:%d, weddingId:%d, uid:%d, err:%v",
            cid, weddingId, uid, err)
        return err
    }

    if micId == comm.HostMicId {
        // 下主持麦，抢夺mvp
        _, _, err = m.TryGrabMvp(ctx, cid, weddingId, uid)
        if err != nil {
            log.ErrorWithCtx(ctx, "OnUserReleaseMic failed to TryGrabMvp. cid:%d, weddingId:%d, uid:%d, err:%v",
                cid, weddingId, uid, err)
            return err
        }
    }

    log.DebugWithCtx(ctx, "OnUserReleaseMic success. cid:%d, weddingId:%d, uid:%d", cid, weddingId, uid)
    return nil
}

// OnUserChangeMic 用户换麦处理
func (m *UserPresentVal) OnUserChangeMic(ctx context.Context, cid, weddingId, uid, oldMicId, newMicId uint32) error {
    var err error
    err = m.cache.DelMicUser(ctx, cid, oldMicId)
    if err != nil {
        log.ErrorWithCtx(ctx, "OnUserChangeMic failed to DelMicUser. cid:%d, weddingId:%d, uid:%d, oldMicId:%d, err:%v",
            cid, weddingId, uid, oldMicId, err)
        return err
    }

    err = m.cache.SetMicUser(ctx, cid, newMicId, uid)
    if err != nil {
        log.ErrorWithCtx(ctx, "OnUserChangeMic failed to SetMicUser. cid:%d, weddingId:%d, uid:%d, newMicId:%d, err:%v",
            cid, weddingId, uid, newMicId, err)
        return err
    }

    if weddingId == 0 {
        return nil
    }

    if newMicId == comm.HostMicId {
        // 上主持麦
        // 放弃人气王
        err = m.TryUpdateReceivedTop(ctx, cid, weddingId, uid, 0)
        if err != nil {
            log.ErrorWithCtx(ctx, "OnUserChangeMic failed to TryUpdateReceivedTop. cid:%d, weddingId:%d, uid:%d, err:%v",
                cid, weddingId, uid, err)
            return err
        }

        // 放弃MVP竞争
        err = m.AbandonMvp(ctx, cid, weddingId, uid)
        if err != nil {
            log.ErrorWithCtx(ctx, "OnUserChangeMic failed to AbandonMvp. cid:%d, weddingId:%d, uid:%d, err:%v",
                cid, weddingId, uid, err)
            return err
        }
    }

    if oldMicId == comm.HostMicId {
        // 下主持麦
        // 可能需要更新收礼top1
        val, err := m.cache.GetUserReceivedValue(ctx, cid, weddingId, uid)
        if err != nil {
            log.ErrorWithCtx(ctx, "OnUserChangeMic failed to GetUserReceivedValue. cid:%d, weddingId:%d, uid:%d, err:%v",
                cid, weddingId, uid, err)
            return err
        }

        // 收礼top1（人气王）是否需要更新
        err = m.TryUpdateReceivedTop(ctx, cid, weddingId, uid, val)
        if err != nil {
            log.ErrorWithCtx(ctx, "OnUserChangeMic failed to TryUpdateReceivedTop. cid:%d, weddingId:%d, uid:%d, value:%d, err:%v",
                cid, weddingId, uid, val, err)
            return err
        }

        // 抢夺MVP
        _, _, err = m.TryGrabMvp(ctx, cid, weddingId, uid)
        if err != nil {
            log.ErrorWithCtx(ctx, "OnUserChangeMic failed to TryGrabMvp. cid:%d, weddingId:%d, uid:%d, err:%v",
                cid, weddingId, uid, err)
            return err
        }
    }

    log.DebugWithCtx(ctx, "OnUserChangeMic success. cid:%d, weddingId:%d, uid:%d", cid, weddingId, uid)
    return nil
}

// OnWeddingFinish 结婚结束处理
func (m *UserPresentVal) OnWeddingFinish(ctx context.Context, cid, weddingId, themeId uint32) error {
    mvpUid, err := m.cache.GetMvpUser(ctx, cid, weddingId)
    if err != nil {
        log.ErrorWithCtx(ctx, "OnWeddingFinish failed to GetMvpUser. cid:%d, weddingId:%d, err:%v", cid, weddingId, err)
        return err
    }
    if mvpUid > 0 {
        // mvp结算
        err = m.acLayerMgr.SendMvpSettlementMsg(ctx, cid, weddingId, mvpUid, themeId)
        if err != nil {
            log.WarnWithCtx(ctx, "OnWeddingFinish failed to SendMvpSettlementMsg. cid:%d, weddingId:%d, mvpUid:%d, err:%v",
                cid, weddingId, mvpUid, err)
        }
    }

    // 清理缓存
    m.cache.ClearWeddingData(ctx, cid, weddingId)

    log.InfoWithCtx(ctx, "OnWeddingFinish success. cid:%d, weddingId:%d, mvpUid:%d, themeId:%d", cid, weddingId, mvpUid, themeId)
    return nil
}
