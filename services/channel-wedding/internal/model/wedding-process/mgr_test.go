package wedding_process

import (
    "context"
    "github.com/golang/mock/gomock"
    "gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/mysql"
    pb "golang.52tt.com/protocol/services/channel-wedding"
    channel_wedding_conf "golang.52tt.com/protocol/services/channel-wedding-conf"
    "golang.52tt.com/services/channel-wedding/internal/conf"
    mocks_conf "golang.52tt.com/services/channel-wedding/internal/conf/mocks"
    mocksModel "golang.52tt.com/services/channel-wedding/internal/model/mocks"
    "golang.52tt.com/services/channel-wedding/internal/model/wedding-process/cache"
    "golang.52tt.com/services/channel-wedding/internal/model/wedding-process/mocks"
    "golang.52tt.com/services/channel-wedding/internal/model/wedding-process/stages"
    "golang.52tt.com/services/channel-wedding/internal/model/wedding-process/store"
    "testing"
    "time"
)

var (
    testMgr *WeddingProcess

    ctx                   = context.Background()
    mockCache             *mocks.MockICache
    mockStore             *mocks.MockIStore
    mockConf              *mocks_conf.MockIBusinessConfManager
    mockACL               *mocksModel.MockIACLayer
    mockWeddingPose       *mocksModel.MockIWeddingPose
    mockWeddingRecords    *mocksModel.MockIWeddingRecords
    mockWeddingPresentVal *mocksModel.MockIUserPresentVal

    testUid = uint32(1)
)

func initTestMgr(t *testing.T) *gomock.Controller {
    ctrl := gomock.NewController(t)

    mockCache = mocks.NewMockICache(ctrl)
    mockStore = mocks.NewMockIStore(ctrl)
    mockConf = mocks_conf.NewMockIBusinessConfManager(ctrl)
    mockACL = mocksModel.NewMockIACLayer(ctrl)
    mockWeddingPose = mocksModel.NewMockIWeddingPose(ctrl)
    mockWeddingRecords = mocksModel.NewMockIWeddingRecords(ctrl)
    mockWeddingPresentVal = mocksModel.NewMockIUserPresentVal(ctrl)

    testMgr = &WeddingProcess{
        cache:             mockCache,
        store:             mockStore,
        bc:                mockConf,
        acLayerMgr:        mockACL,
        weddingPose:       mockWeddingPose,
        weddingRecords:    mockWeddingRecords,
        stageMgr:          stages.NewStages(),
        weddingPresentVal: mockWeddingPresentVal,
    }

    return ctrl
}

func TestWeddingProcess_ReserveWedding(t *testing.T) {
    type args struct {
        ctx context.Context
        req *pb.ReserveWeddingReq
    }
    tests := []struct {
        name     string
        args     args
        wantErr  bool
        mockFunc func()
    }{
        {
            name: "common",
            args: args{
                ctx: ctx,
                req: &pb.ReserveWeddingReq{
                    Cid:               1,
                    WeddingThemeId:    1,
                    BrideUid:          2,
                    GroomUid:          3,
                    StartTime:         time.Now().Unix() + 10,
                    EndTime:           time.Now().Unix() + 60,
                    BridesmaidUidList: []uint32{5, 6},
                },
            },
            wantErr: false,
            mockFunc: func() {
                mockACL.EXPECT().GetWeddingThemeCfg(gomock.Any(), gomock.Any()).Return(&channel_wedding_conf.ThemeCfg{
                    ThemeLevelCfgList: []*channel_wedding_conf.ThemeLevelCfg{{
                        Level: 1,
                        GuestDressCfgMap: map[uint32]*channel_wedding_conf.GuestDressCfg{
                            1: &channel_wedding_conf.GuestDressCfg{SuitCfg: &channel_wedding_conf.GuestSuitCfg{
                                ItemIds: []uint32{1, 2},
                            }},
                        }}},
                }, nil)
                mockACL.EXPECT().GetUserProfileMap(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
                mockConf.EXPECT().GetBridesmaidSuitRemainDaysLimit().Return(uint32(7)).AnyTimes()
                mockStore.EXPECT().Transaction(gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, f func(tx mysql.Txx) error) error {
                    return f(nil)
                })
                mockStore.EXPECT().AddWeddingSchedule(ctx, gomock.Any(), gomock.Any()).Return(uint32(1), nil)
                mockStore.EXPECT().BatchInsertAwardLog(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)

                mockConf.EXPECT().GetSuitSendImCfg().Return(&conf.SuitSendImCfg{Content: "test"})
                mockACL.EXPECT().SendIMMsgAsync(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
            },
        },
    }

    ctrl := initTestMgr(t)
    defer ctrl.Finish()

    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            tt.mockFunc()
            if _, err := testMgr.ReserveWedding(tt.args.ctx, testUid, tt.args.req); (err != nil) != tt.wantErr {
                t.Errorf("WeddingProcess.ReserveWedding() error = %v, wantErr %v", err, tt.wantErr)
            }
        })
    }
}

func TestWeddingProcess_TryBeginWedding(t *testing.T) {
    type args struct {
        ctx context.Context
        cid uint32
    }
    tests := []struct {
        name     string
        args     args
        wantErr  bool
        mockFunc func()
    }{
        {
            name: "common",
            args: args{
                ctx: ctx,
                cid: 1,
            },
            wantErr: false,
            mockFunc: func() {
                mockCache.EXPECT().GetWeddingInfo(gomock.Any(), gomock.Any()).Return(&cache.WeddingInfo{
                    Cid: 1, Stage: 1, BeginTs: time.Now().Unix(),
                }, false, nil)
                mockConf.EXPECT().GetStageCfgList().Return([]*conf.StageCfg{{StageId: 1}})

                mockACL.EXPECT().GetWeddingThemeCfg(gomock.Any(), gomock.Any()).Return(nil, nil)

                mockStore.EXPECT().Transaction(gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, f func(tx mysql.Txx) error) error {
                    return f(nil)
                })
                mockStore.EXPECT().UpdateWeddingStatus(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
                mockCache.EXPECT().SetWeddingInfo(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
                mockCache.EXPECT().SetUserWeddingInfo(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
                mockCache.EXPECT().AddStageCidQueue(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()

                mockCache.EXPECT().GetWeddingInfo(gomock.Any(), gomock.Any()).Return(&cache.WeddingInfo{
                    Stage: 2,
                }, true, nil)
                mockCache.EXPECT().GetHappiness(gomock.Any(), gomock.Any(), gomock.Any()).Return(uint32(1), nil)
                mockACL.EXPECT().GetWeddingThemeCfg(gomock.Any(), gomock.Any()).Return(&channel_wedding_conf.ThemeCfg{
                    ThemeLevelCfgList: []*channel_wedding_conf.ThemeLevelCfg{{Level: 1}},
                }, nil)
                mockConf.EXPECT().GetStageCfgList().Return([]*conf.StageCfg{{StageId: 1}})
                mockConf.EXPECT().GetHappinessLevelByScore(gomock.Any()).Return(uint32(1))
                mockACL.EXPECT().GetWeddingPlanInfo(gomock.Any(), gomock.Any()).Return(nil, nil)
                mockConf.EXPECT().GetStageCfg(gomock.Any()).Return(&conf.StageCfg{StageId: 1})
                mockConf.EXPECT().GetHappinessConf().Return([]*conf.HappinessConfig{})
                mockConf.EXPECT().GetSingleBoneCfg().Return(&conf.SingleBoneCfg{})
                mockACL.EXPECT().SendStageChangeMsg(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)

                mockACL.EXPECT().GetWeddingThemeCfg(gomock.Any(), gomock.Any()).Return(&channel_wedding_conf.ThemeCfg{
                    ThemeLevelCfgList: []*channel_wedding_conf.ThemeLevelCfg{},
                }, nil).AnyTimes()

                mockACL.EXPECT().KickCommUserOutMicSpace(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
                mockACL.EXPECT().SetMicStatus(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
                mockWeddingPose.EXPECT().SetUserWeddingPoseOrientation(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()

                mockCache.EXPECT().AddHappiness(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(uint32(1), nil).AnyTimes()
                mockConf.EXPECT().GetHappinessConfByScore(gomock.Any()).Return(&conf.HappinessConfig{}).AnyTimes()
                mockACL.EXPECT().SendHappinessChangeMsg(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
                mockConf.EXPECT().GetHappinessUpgrade(gomock.Any(), gomock.Any()).Return(false).AnyTimes()
            },
        },
    }

    ctrl := initTestMgr(t)
    defer ctrl.Finish()

    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            tt.mockFunc()
            if err := testMgr.TryBeginWedding(tt.args.ctx, 0, &store.WeddingSchedule{
                Cid:              tt.args.cid,
                Id:               1,
                InitHappinessVal: 1,
            }); (err != nil) != tt.wantErr {
                t.Errorf("WeddingProcess.TryBeginWedding() error = %v, wantErr %v", err, tt.wantErr)
            }
        })
    }
}

func TestWeddingProcess_intiGroomBrideClothes(t *testing.T) {
    type args struct {
        ctx context.Context
    }
    tests := []struct {
        name     string
        args     args
        wantErr  bool
        mockFunc func()
    }{
        {
            name: "common",
            args: args{
                ctx: ctx,
            },
            wantErr: false,
            mockFunc: func() {
                mockACL.EXPECT().GetWeddingThemeCfg(gomock.Any(), gomock.Any()).Return(&channel_wedding_conf.ThemeCfg{
                    ThemeLevelCfgList: []*channel_wedding_conf.ThemeLevelCfg{{Level: 1}},
                }, nil)
                mockConf.EXPECT().GetAllowChangeSubCategoryList().Return([]uint32{1, 2})
                mockACL.EXPECT().UseVirtualImageSuit(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
                mockACL.EXPECT().UseVirtualImageSuit(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
            },
        },
    }

    ctrl := initTestMgr(t)
    defer ctrl.Finish()

    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            tt.mockFunc()
            if err := testMgr.initGroomBrideClothes(tt.args.ctx, &cache.WeddingInfo{
                Cid: 1, WeddingId: 1,
            }); (err != nil) != tt.wantErr {
                t.Errorf("WeddingProcess.initGroomBrideClothes() error = %v, wantErr %v", err, tt.wantErr)
            }
        })
    }
}

func TestWeddingProcess_intiGuestClothes(t *testing.T) {
    type args struct {
        ctx context.Context
    }
    tests := []struct {
        name     string
        args     args
        wantErr  bool
        mockFunc func()
    }{
        {
            name: "common",
            args: args{
                ctx: ctx,
            },
            wantErr: false,
            mockFunc: func() {
                mockACL.EXPECT().GetWeddingThemeCfg(gomock.Any(), gomock.Any()).Return(&channel_wedding_conf.ThemeCfg{
                    ThemeLevelCfgList: []*channel_wedding_conf.ThemeLevelCfg{{Level: 1}},
                }, nil)
                mockACL.EXPECT().GetUserProfileMap(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
                mockACL.EXPECT().UseVirtualImageSuit(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
            },
        },
    }

    ctrl := initTestMgr(t)
    defer ctrl.Finish()

    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            tt.mockFunc()
            if err := testMgr.initGuestClothes(tt.args.ctx, &pb.WeddingInfo{
                Cid: 1, WeddingId: 1, BridesmaidManList: []uint32{1, 2},
            }); (err != nil) != tt.wantErr {
                t.Errorf("WeddingProcess.initGuestClothes() error = %v, wantErr %v", err, tt.wantErr)
            }
        })
    }
}

func TestWeddingProcess_SwitchWeddingStage(t *testing.T) {
    type args struct {
        ctx             context.Context
        Stage, SubStage uint32
    }
    tests := []struct {
        name     string
        args     args
        wantErr  bool
        mockFunc func()
    }{
        {
            name: "stage 2",
            args: args{
                ctx:   ctx,
                Stage: 2,
            },
            wantErr: false,
            mockFunc: func() {
                mockCache.EXPECT().GetWeddingInfo(gomock.Any(), gomock.Any()).Return(&cache.WeddingInfo{
                    Cid: 1, Stage: 1, BeginTs: time.Now().Unix(),
                }, true, nil)
                mockConf.EXPECT().GetStageSwitchIntervalSec().Return(uint32(1))
                mockCache.EXPECT().SetWeddingInfo(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)

                mockCache.EXPECT().GetWeddingInfo(gomock.Any(), gomock.Any()).Return(&cache.WeddingInfo{
                    Stage: 2,
                }, true, nil)
                mockCache.EXPECT().GetHappiness(gomock.Any(), gomock.Any(), gomock.Any()).Return(uint32(1), nil)
                mockACL.EXPECT().GetWeddingThemeCfg(gomock.Any(), gomock.Any()).Return(&channel_wedding_conf.ThemeCfg{}, nil)
                mockConf.EXPECT().GetStageCfgList().Return([]*conf.StageCfg{{StageId: 1}})
                mockConf.EXPECT().GetHappinessLevelByScore(gomock.Any()).Return(uint32(1))
                mockACL.EXPECT().GetWeddingPlanInfo(gomock.Any(), gomock.Any()).Return(nil, nil)
                mockConf.EXPECT().GetStageCfg(gomock.Any()).Return(&conf.StageCfg{StageId: 1})
                mockConf.EXPECT().GetHappinessConf().Return([]*conf.HappinessConfig{})
                mockConf.EXPECT().GetSingleBoneCfg().Return(&conf.SingleBoneCfg{})
                mockACL.EXPECT().SendStageChangeMsg(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)

                mockACL.EXPECT().GetWeddingThemeCfg(gomock.Any(), gomock.Any()).Return(&channel_wedding_conf.ThemeCfg{}, nil).AnyTimes()
                mockACL.EXPECT().GetWeddingPlanInfo(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
                mockACL.EXPECT().SendSceneNotifyMsg(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()

            },
        },
        {
            name: "stage 4",
            args: args{
                ctx:   ctx,
                Stage: 4,
            },
            wantErr: false,
            mockFunc: func() {
                mockCache.EXPECT().GetWeddingInfo(gomock.Any(), gomock.Any()).Return(&cache.WeddingInfo{
                    Cid: 1, Stage: 3, BeginTs: time.Now().Unix(),
                }, true, nil)
                mockConf.EXPECT().GetStageSwitchIntervalSec().Return(uint32(1))
                mockCache.EXPECT().SetWeddingInfo(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)

                mockCache.EXPECT().GetWeddingInfo(gomock.Any(), gomock.Any()).Return(&cache.WeddingInfo{
                    Stage: 4,
                }, true, nil)
                mockCache.EXPECT().GetHappiness(gomock.Any(), gomock.Any(), gomock.Any()).Return(uint32(1), nil)
                mockACL.EXPECT().GetWeddingThemeCfg(gomock.Any(), gomock.Any()).Return(&channel_wedding_conf.ThemeCfg{
                    ThemeLevelCfgList: []*channel_wedding_conf.ThemeLevelCfg{{Level: 1}},
                }, nil).AnyTimes()
                mockConf.EXPECT().GetStageCfgList().Return([]*conf.StageCfg{{StageId: 1}})
                mockConf.EXPECT().GetHappinessLevelByScore(gomock.Any()).Return(uint32(1))
                mockACL.EXPECT().GetWeddingPlanInfo(gomock.Any(), gomock.Any()).Return(nil, nil)
                mockConf.EXPECT().GetStageCfg(gomock.Any()).Return(&conf.StageCfg{StageId: 1})
                mockConf.EXPECT().GetHappinessConf().Return([]*conf.HappinessConfig{})
                mockConf.EXPECT().GetSingleBoneCfg().Return(&conf.SingleBoneCfg{})
                mockACL.EXPECT().SendStageChangeMsg(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)

                mockACL.EXPECT().GetWeddingThemeCfg(gomock.Any(), gomock.Any()).Return(&channel_wedding_conf.ThemeCfg{}, nil).AnyTimes()
                mockACL.EXPECT().GetWeddingPlanInfo(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
                mockACL.EXPECT().SendSceneNotifyMsg(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()

            },
        },
    }

    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            ctrl := initTestMgr(t)
            defer ctrl.Finish()

            tt.mockFunc()
            if err := testMgr.SwitchWeddingStage(tt.args.ctx, 0, 1, tt.args.Stage, tt.args.SubStage); (err != nil) != tt.wantErr {
                t.Errorf("WeddingProcess.SwitchWeddingStage() error = %v, wantErr %v", err, tt.wantErr)
            }
            <-time.After(time.Second)

        })
    }
}

func TestWeddingProcess_endWeddingHandle(t *testing.T) {
    type args struct {
        ctx context.Context
        cid uint32
    }
    tests := []struct {
        name     string
        args     args
        wantErr  bool
        mockFunc func()
    }{
        {
            name: "common",
            args: args{
                ctx: ctx,
                cid: 1,
            },
            wantErr: false,
            mockFunc: func() {
                mockStore.EXPECT().Transaction(gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, f func(tx mysql.Txx) error) error {
                    return f(nil)
                })
                mockStore.EXPECT().UpdateWeddingStatus(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
                mockCache.EXPECT().DelWeddingInfo(gomock.Any(), gomock.Any()).Return(nil)
                mockCache.EXPECT().DelStageCidQueue(gomock.Any(), gomock.Any()).Return(nil)

                mockCache.EXPECT().DelUserWeddingInfo(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
                mockCache.EXPECT().GetHappiness(gomock.Any(), gomock.Any(), gomock.Any()).Return(uint32(1), nil)
                mockConf.EXPECT().GetHappinessLevelByScore(gomock.Any()).Return(uint32(1))
                mockWeddingRecords.EXPECT().AddWeddingRecord(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)

                mockWeddingPose.EXPECT().SetUserWeddingPose(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
                mockACL.EXPECT().UpdateWeddingPlanStatus(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
                mockACL.EXPECT().EndWeddingChairGame(gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes()

                mockWeddingPresentVal.EXPECT().OnWeddingFinish(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()

                mockACL.EXPECT().SendWeddingAfterSurvey(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
                mockACL.EXPECT().EndGameKickOutMic(gomock.Any(), gomock.Any()).Return( nil)
            },
        },
    }

    ctrl := initTestMgr(t)
    defer ctrl.Finish()

    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            tt.mockFunc()
            if err := testMgr.endWeddingHandle(tt.args.ctx, 0, &cache.WeddingInfo{Cid: tt.args.cid, ThemeType: uint32(pb.WeddingThemeType_WEDDING_THEME_TYPE_PAY)}); (err != nil) != tt.wantErr {
                t.Errorf("WeddingProcess.endWeddingHandle() error = %v, wantErr %v", err, tt.wantErr)
            }
            <-time.After(time.Second)
        })
    }
}

func TestWeddingProcess_AddWeddingBridesmaidMan(t *testing.T) {
    type args struct {
        ctx context.Context
        cid uint32
        uid uint32
    }
    tests := []struct {
        name     string
        args     args
        wantErr  bool
        mockFunc func()
    }{
        {
            name: "common",
            args: args{
                ctx: ctx,
                cid: 1,
                uid: 1,
            },
            wantErr: false,
            mockFunc: func() {
                //mockCache.EXPECT().AddWeddingGrooms(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
            },
        },
    }

    ctrl := initTestMgr(t)
    defer ctrl.Finish()

    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            tt.mockFunc()
            if err := testMgr.AddWeddingBridesmaidMan(tt.args.ctx, tt.args.cid, tt.args.uid, []uint32{1, 2}); (err != nil) != tt.wantErr {
                t.Errorf("WeddingProcess.AddWeddingBridesmaidMan() error = %v, wantErr %v", err, tt.wantErr)
            }
        })
    }
}

func TestWeddingProcess_GetUserWeddingCid(t *testing.T) {
    type args struct {
        ctx context.Context
        uid uint32
    }
    tests := []struct {
        name     string
        args     args
        wantErr  bool
        mockFunc func()
    }{
        {
            name: "common",
            args: args{
                ctx: ctx,
                uid: 1,
            },
            wantErr: false,
            mockFunc: func() {
                mockCache.EXPECT().GetUserWeddingInfo(gomock.Any(), gomock.Any()).Return(&cache.UserWeddingInfo{}, true, nil)
            },
        },
    }

    ctrl := initTestMgr(t)
    defer ctrl.Finish()

    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            tt.mockFunc()
            _, err := testMgr.GetUserWeddingCid(tt.args.ctx, tt.args.uid)
            if (err != nil) != tt.wantErr {
                t.Errorf("WeddingProcess.GetUserWeddingCid() error = %v, wantErr %v", err, tt.wantErr)
            }
        })
    }
}

func TestWeddingProcess_CheckWeddingStart(t *testing.T) {
    type args struct {
    }
    tests := []struct {
        name     string
        args     args
        wantErr  bool
        mockFunc func()
    }{
        {
            name:    "common",
            args:    args{},
            wantErr: true,
            mockFunc: func() {
                mockStore.EXPECT().GetNotStartWeddingScheduleList(gomock.Any(), gomock.Any()).Return([]*store.WeddingSchedule{
                    {Cid: 1},
                }, nil)
                mockCache.EXPECT().Lock(gomock.Any(), gomock.Any(), gomock.Any()).Return(true, nil)
                mockCache.EXPECT().Unlock(gomock.Any(), gomock.Any()).Return(nil)
            },
        },
    }

    ctrl := initTestMgr(t)
    defer ctrl.Finish()

    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            tt.mockFunc()
            testMgr.CheckWeddingStart(ctx)
        })
    }
}

func TestWeddingProcess_CheckFreeWeddingStageSwitch(t *testing.T) {
    type args struct {
    }
    tests := []struct {
        name     string
        args     args
        wantErr  bool
        mockFunc func()
    }{
        {
            name:    "common",
            args:    args{},
            wantErr: true,
            mockFunc: func() {
                mockConf.EXPECT().GetFreeWeddingStageSec().Return(uint32(1))
                mockCache.EXPECT().GetStageExpireCid(gomock.Any(), gomock.Any(), gomock.Any()).Return([]uint32{1}, nil)
                mockCache.EXPECT().Lock(gomock.Any(), gomock.Any(), gomock.Any()).Return(true, nil)
                mockCache.EXPECT().GetWeddingInfo(gomock.Any(), gomock.Any()).Return(nil, false, nil).AnyTimes()
                mockCache.EXPECT().Unlock(gomock.Any(), gomock.Any()).Return(nil)
            },
        },
    }

    ctrl := initTestMgr(t)
    defer ctrl.Finish()

    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            tt.mockFunc()
            testMgr.CheckFreeWeddingStageSwitch(ctx)
        })
    }
}

func TestWeddingProcess_BatchGetWeddingSimpleInfo(t *testing.T) {
    type args struct {
        ctx    context.Context
        cidArr []uint32
    }
    tests := []struct {
        name     string
        args     args
        wantErr  bool
        mockFunc func()
    }{
        {
            name: "common",
            args: args{
                ctx:    ctx,
                cidArr: []uint32{1},
            },
            wantErr: false,
            mockFunc: func() {
                mockCache.EXPECT().BatchGetWeddingInfo(gomock.Any(), gomock.Any()).Return([]*cache.WeddingInfo{{Cid: 1}}, nil)
            },
        },
    }

    ctrl := initTestMgr(t)
    defer ctrl.Finish()

    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            tt.mockFunc()
            _, err := testMgr.BatchGetWeddingSimpleInfo(tt.args.ctx, tt.args.cidArr)
            if (err != nil) != tt.wantErr {
                t.Errorf("WeddingProcess.BatchGetWeddingSimpleInfo() error = %v, wantErr %v", err, tt.wantErr)
            }
        })
    }
}

func TestWeddingProcess_ReportWeddingBridesmaidMan(t *testing.T) {
    type args struct {
        ctx context.Context
        cid uint32
    }
    tests := []struct {
        name     string
        args     args
        wantErr  bool
        mockFunc func()
    }{
        {
            name: "common",
            args: args{
                ctx: ctx,
                cid: 1,
            },
            wantErr: false,
            mockFunc: func() {
                mockStore.EXPECT().GetWeddingScheduleByPlanId(gomock.Any(), gomock.Any()).Return(&store.WeddingSchedule{
                    Id: 1, Cid: 1, Status: 1,
                }, true, nil)
                mockCache.EXPECT().GetWeddingInfo(gomock.Any(), gomock.Any()).Return(&cache.WeddingInfo{}, true, nil)
                mockACL.EXPECT().GetWeddingThemeCfg(gomock.Any(), gomock.Any()).Return(&channel_wedding_conf.ThemeCfg{
                    ThemeLevelCfgList: []*channel_wedding_conf.ThemeLevelCfg{{Level: 1}},
                }, nil)
                mockACL.EXPECT().GetUserProfileMap(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
                mockConf.EXPECT().GetBridesmaidSuitRemainDaysLimit().Return(uint32(7)).AnyTimes()
                mockStore.EXPECT().Transaction(gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, f func(tx mysql.Txx) error) error {
                    return f(nil)
                })
                mockStore.EXPECT().BatchInsertAwardLog(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
                mockConf.EXPECT().GetSuitSendImCfg().Return(nil)
                mockACL.EXPECT().SendWeddingBridesmaidUpdateMsg(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
                mockACL.EXPECT().SendVirtualImageSuitWithUse(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(false, nil)
                mockStore.EXPECT().UpdateAwardLogStatus(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
            },
        },
    }

    ctrl := initTestMgr(t)
    defer ctrl.Finish()

    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            tt.mockFunc()
            if err := testMgr.ReportWeddingBridesmaidMan(tt.args.ctx, &pb.ReportWeddingBridesmaidManReq{
                Cid: tt.args.cid, BridesmaidUidList: []uint32{1, 2},
            }); (err != nil) != tt.wantErr {
                t.Errorf("WeddingProcess.ReportWeddingBridesmaidMan() error = %v, wantErr %v", err, tt.wantErr)
            }
            <-time.After(time.Second)
        })
    }
}

func TestWeddingProcess_happinessExtraSuitAward(t *testing.T) {
    type args struct {
        ctx context.Context
        cid uint32
    }
    tests := []struct {
        name     string
        args     args
        wantErr  bool
        mockFunc func()
    }{
        {
            name: "common",
            args: args{
                ctx: ctx,
                cid: 1,
            },
            wantErr: false,
            mockFunc: func() {
                mockACL.EXPECT().GetWeddingThemeCfg(gomock.Any(), gomock.Any()).Return(&channel_wedding_conf.ThemeCfg{
                    ThemeLevelCfgList: []*channel_wedding_conf.ThemeLevelCfg{
                        {
                            Level: 1,
                            GuestDressCfgMap: map[uint32]*channel_wedding_conf.GuestDressCfg{
                                1: &channel_wedding_conf.GuestDressCfg{
                                    SuitCfg: &channel_wedding_conf.GuestSuitCfg{ItemIds: []uint32{1, 2}},
                                },
                                2: &channel_wedding_conf.GuestDressCfg{
                                    SuitCfg: &channel_wedding_conf.GuestSuitCfg{ItemIds: []uint32{1, 2}},
                                },
                                3: &channel_wedding_conf.GuestDressCfg{
                                    SuitCfg: &channel_wedding_conf.GuestSuitCfg{ItemIds: []uint32{1, 2}},
                                },
                                4: &channel_wedding_conf.GuestDressCfg{
                                    SuitCfg: &channel_wedding_conf.GuestSuitCfg{ItemIds: []uint32{1, 2}},
                                },
                            },
                        },
                    },
                }, nil)
                mockACL.EXPECT().GetUserProfileMap(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
                //mockConf.EXPECT().GetBridesmaidSuitRemainDaysLimit().Return(uint32(7))
                mockStore.EXPECT().Transaction(gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, f func(tx mysql.Txx) error) error {
                    return f(nil)
                })
                mockStore.EXPECT().BatchInsertAwardLog(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)

                mockACL.EXPECT().GetUserProfile(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
                mockACL.EXPECT().SimpleSendTTAssistantText(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
            },
        },
    }

    ctrl := initTestMgr(t)
    defer ctrl.Finish()

    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            tt.mockFunc()
            if err := testMgr.happinessExtraSuitAward(
                tt.args.ctx,
                &pb.WeddingInfo{
                    Cid: tt.args.cid, WeddingId: 1, BridesmaidManList: []uint32{1, 2, 3},
                },
                &conf.HappinessConfig{
                    Level: 1, Score: 1000,
                    ExtraSuitAwardList: []*conf.HappinessExtraSuitAward{
                        {
                            MinVal: 50, GroomBrideExtraSec: 86400, OtherExtraSec: 3600,
                        },
                    },
                },
                0, 110); (err != nil) != tt.wantErr {
                t.Errorf("WeddingProcess.happinessExtraSuitAward() error = %v, wantErr %v", err, tt.wantErr)
            }
            <-time.After(time.Second)
        })
    }
}
