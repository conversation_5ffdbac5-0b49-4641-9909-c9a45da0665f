// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/services/channel-wedding/internal/model/user-present-value (interfaces: IUserPresentVal)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	channel_wedding "golang.52tt.com/protocol/services/channel-wedding"
)

// MockIUserPresentVal is a mock of IUserPresentVal interface.
type MockIUserPresentVal struct {
	ctrl     *gomock.Controller
	recorder *MockIUserPresentValMockRecorder
}

// MockIUserPresentValMockRecorder is the mock recorder for MockIUserPresentVal.
type MockIUserPresentValMockRecorder struct {
	mock *MockIUserPresentVal
}

// NewMockIUserPresentVal creates a new mock instance.
func NewMockIUserPresentVal(ctrl *gomock.Controller) *MockIUserPresentVal {
	mock := &MockIUserPresentVal{ctrl: ctrl}
	mock.recorder = &MockIUserPresentValMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIUserPresentVal) EXPECT() *MockIUserPresentValMockRecorder {
	return m.recorder
}

// AbandonMvp mocks base method.
func (m *MockIUserPresentVal) AbandonMvp(arg0 context.Context, arg1, arg2, arg3 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AbandonMvp", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// AbandonMvp indicates an expected call of AbandonMvp.
func (mr *MockIUserPresentValMockRecorder) AbandonMvp(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AbandonMvp", reflect.TypeOf((*MockIUserPresentVal)(nil).AbandonMvp), arg0, arg1, arg2, arg3)
}

// AddPresentValue mocks base method.
func (m *MockIUserPresentVal) AddPresentValue(arg0 context.Context, arg1, arg2, arg3, arg4, arg5 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddPresentValue", arg0, arg1, arg2, arg3, arg4, arg5)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddPresentValue indicates an expected call of AddPresentValue.
func (mr *MockIUserPresentValMockRecorder) AddPresentValue(arg0, arg1, arg2, arg3, arg4, arg5 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddPresentValue", reflect.TypeOf((*MockIUserPresentVal)(nil).AddPresentValue), arg0, arg1, arg2, arg3, arg4, arg5)
}

// AddUserReceivedValue mocks base method.
func (m *MockIUserPresentVal) AddUserReceivedValue(arg0 context.Context, arg1, arg2, arg3, arg4 uint32) (uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddUserReceivedValue", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddUserReceivedValue indicates an expected call of AddUserReceivedValue.
func (mr *MockIUserPresentValMockRecorder) AddUserReceivedValue(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddUserReceivedValue", reflect.TypeOf((*MockIUserPresentVal)(nil).AddUserReceivedValue), arg0, arg1, arg2, arg3, arg4)
}

// AddUserSendValue mocks base method.
func (m *MockIUserPresentVal) AddUserSendValue(arg0 context.Context, arg1, arg2, arg3, arg4 uint32) (uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddUserSendValue", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddUserSendValue indicates an expected call of AddUserSendValue.
func (mr *MockIUserPresentValMockRecorder) AddUserSendValue(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddUserSendValue", reflect.TypeOf((*MockIUserPresentVal)(nil).AddUserSendValue), arg0, arg1, arg2, arg3, arg4)
}

// GetMicUserReceivedValList mocks base method.
func (m *MockIUserPresentVal) GetMicUserReceivedValList(arg0 context.Context, arg1, arg2 uint32) ([]*channel_wedding.WeddingPresentVal, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMicUserReceivedValList", arg0, arg1, arg2)
	ret0, _ := ret[0].([]*channel_wedding.WeddingPresentVal)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMicUserReceivedValList indicates an expected call of GetMicUserReceivedValList.
func (mr *MockIUserPresentValMockRecorder) GetMicUserReceivedValList(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMicUserReceivedValList", reflect.TypeOf((*MockIUserPresentVal)(nil).GetMicUserReceivedValList), arg0, arg1, arg2)
}

// GetPresentValueInfo mocks base method.
func (m *MockIUserPresentVal) GetPresentValueInfo(arg0 context.Context, arg1, arg2 uint32) (*channel_wedding.WeddingPresentCountInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPresentValueInfo", arg0, arg1, arg2)
	ret0, _ := ret[0].(*channel_wedding.WeddingPresentCountInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPresentValueInfo indicates an expected call of GetPresentValueInfo.
func (mr *MockIUserPresentValMockRecorder) GetPresentValueInfo(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPresentValueInfo", reflect.TypeOf((*MockIUserPresentVal)(nil).GetPresentValueInfo), arg0, arg1, arg2)
}

// OnUserChangeMic mocks base method.
func (m *MockIUserPresentVal) OnUserChangeMic(arg0 context.Context, arg1, arg2, arg3, arg4, arg5 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "OnUserChangeMic", arg0, arg1, arg2, arg3, arg4, arg5)
	ret0, _ := ret[0].(error)
	return ret0
}

// OnUserChangeMic indicates an expected call of OnUserChangeMic.
func (mr *MockIUserPresentValMockRecorder) OnUserChangeMic(arg0, arg1, arg2, arg3, arg4, arg5 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "OnUserChangeMic", reflect.TypeOf((*MockIUserPresentVal)(nil).OnUserChangeMic), arg0, arg1, arg2, arg3, arg4, arg5)
}

// OnUserEnterChannel mocks base method.
func (m *MockIUserPresentVal) OnUserEnterChannel(arg0 context.Context, arg1, arg2, arg3 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "OnUserEnterChannel", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// OnUserEnterChannel indicates an expected call of OnUserEnterChannel.
func (mr *MockIUserPresentValMockRecorder) OnUserEnterChannel(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "OnUserEnterChannel", reflect.TypeOf((*MockIUserPresentVal)(nil).OnUserEnterChannel), arg0, arg1, arg2, arg3)
}

// OnUserHoldMic mocks base method.
func (m *MockIUserPresentVal) OnUserHoldMic(arg0 context.Context, arg1, arg2, arg3, arg4 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "OnUserHoldMic", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(error)
	return ret0
}

// OnUserHoldMic indicates an expected call of OnUserHoldMic.
func (mr *MockIUserPresentValMockRecorder) OnUserHoldMic(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "OnUserHoldMic", reflect.TypeOf((*MockIUserPresentVal)(nil).OnUserHoldMic), arg0, arg1, arg2, arg3, arg4)
}

// OnUserLeaveChannel mocks base method.
func (m *MockIUserPresentVal) OnUserLeaveChannel(arg0 context.Context, arg1, arg2, arg3 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "OnUserLeaveChannel", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// OnUserLeaveChannel indicates an expected call of OnUserLeaveChannel.
func (mr *MockIUserPresentValMockRecorder) OnUserLeaveChannel(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "OnUserLeaveChannel", reflect.TypeOf((*MockIUserPresentVal)(nil).OnUserLeaveChannel), arg0, arg1, arg2, arg3)
}

// OnUserReleaseMic mocks base method.
func (m *MockIUserPresentVal) OnUserReleaseMic(arg0 context.Context, arg1, arg2, arg3, arg4 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "OnUserReleaseMic", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(error)
	return ret0
}

// OnUserReleaseMic indicates an expected call of OnUserReleaseMic.
func (mr *MockIUserPresentValMockRecorder) OnUserReleaseMic(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "OnUserReleaseMic", reflect.TypeOf((*MockIUserPresentVal)(nil).OnUserReleaseMic), arg0, arg1, arg2, arg3, arg4)
}

// OnWeddingFinish mocks base method.
func (m *MockIUserPresentVal) OnWeddingFinish(arg0 context.Context, arg1, arg2, arg3 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "OnWeddingFinish", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// OnWeddingFinish indicates an expected call of OnWeddingFinish.
func (mr *MockIUserPresentValMockRecorder) OnWeddingFinish(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "OnWeddingFinish", reflect.TypeOf((*MockIUserPresentVal)(nil).OnWeddingFinish), arg0, arg1, arg2, arg3)
}

// SelectMvp mocks base method.
func (m *MockIUserPresentVal) SelectMvp(arg0 context.Context, arg1, arg2 uint32) (uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SelectMvp", arg0, arg1, arg2)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SelectMvp indicates an expected call of SelectMvp.
func (mr *MockIUserPresentValMockRecorder) SelectMvp(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SelectMvp", reflect.TypeOf((*MockIUserPresentVal)(nil).SelectMvp), arg0, arg1, arg2)
}

// SelectReceivedTop mocks base method.
func (m *MockIUserPresentVal) SelectReceivedTop(arg0 context.Context, arg1, arg2 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SelectReceivedTop", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// SelectReceivedTop indicates an expected call of SelectReceivedTop.
func (mr *MockIUserPresentValMockRecorder) SelectReceivedTop(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SelectReceivedTop", reflect.TypeOf((*MockIUserPresentVal)(nil).SelectReceivedTop), arg0, arg1, arg2)
}

// SetMvp mocks base method.
func (m *MockIUserPresentVal) SetMvp(arg0 context.Context, arg1, arg2, arg3 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetMvp", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetMvp indicates an expected call of SetMvp.
func (mr *MockIUserPresentValMockRecorder) SetMvp(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetMvp", reflect.TypeOf((*MockIUserPresentVal)(nil).SetMvp), arg0, arg1, arg2, arg3)
}

// Stop mocks base method.
func (m *MockIUserPresentVal) Stop() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Stop")
}

// Stop indicates an expected call of Stop.
func (mr *MockIUserPresentValMockRecorder) Stop() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Stop", reflect.TypeOf((*MockIUserPresentVal)(nil).Stop))
}

// TryGrabMvp mocks base method.
func (m *MockIUserPresentVal) TryGrabMvp(arg0 context.Context, arg1, arg2, arg3 uint32) (uint32, bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TryGrabMvp", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(bool)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// TryGrabMvp indicates an expected call of TryGrabMvp.
func (mr *MockIUserPresentValMockRecorder) TryGrabMvp(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TryGrabMvp", reflect.TypeOf((*MockIUserPresentVal)(nil).TryGrabMvp), arg0, arg1, arg2, arg3)
}

// TryUpdateReceivedTop mocks base method.
func (m *MockIUserPresentVal) TryUpdateReceivedTop(arg0 context.Context, arg1, arg2, arg3, arg4 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TryUpdateReceivedTop", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(error)
	return ret0
}

// TryUpdateReceivedTop indicates an expected call of TryUpdateReceivedTop.
func (mr *MockIUserPresentValMockRecorder) TryUpdateReceivedTop(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TryUpdateReceivedTop", reflect.TypeOf((*MockIUserPresentVal)(nil).TryUpdateReceivedTop), arg0, arg1, arg2, arg3, arg4)
}
