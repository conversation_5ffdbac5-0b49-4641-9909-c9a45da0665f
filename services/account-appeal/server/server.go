package server

import (
	"context"
	"github.com/jmoiron/sqlx"
	"golang.52tt.com/pkg/config"
	"golang.52tt.com/pkg/log"
	pb "golang.52tt.com/protocol/services/account-appeal"
	"golang.52tt.com/services/account-appeal/mysql"
)

type accountAppealServer struct {
	sc *ServiceConfigT
	db *mysql.DB
}

func NewAccountAppealServer(ctx context.Context, cfg config.Configer) (pb.AccountAppealServer, error) {
	sc := &ServiceConfigT{}

	err := sc.Parse(cfg)
	if err != nil {
		return nil, err
	}

	mysqlDb, err := sqlx.ConnectContext(ctx, "mysql", sc.GetMysqlConnectionString())
	if err != nil {
		log.ErrorWithCtx(ctx,"Failed to connect mysql. err:%v", err)
		return nil, err
	}

	db := mysql.NewMysqlDB(mysqlDb)

	err = db.CreateAccountAppealTable(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx,"Failed to CreateAppealInfoTable. err:%v", err)
		return nil, err
	}

	svr := &accountAppealServer{
		sc: sc,
		db: db,
	}

	return svr, nil
}

func (s *accountAppealServer) GetAccountAppealInfo(ctx context.Context, req *pb.GetAccountAppealInfoReq) (*pb.GetAccountAppealInfoResp, error) {
	out := &pb.GetAccountAppealInfoResp{}

	if req.GetPageNumber() == 0 {
		req.PageNumber = 1
	}

	if req.GetPageSize() == 0 {
		req.PageSize = 20
	}

	infoList, totalCnt, err := s.db.GetAppealInfo(ctx, req)
	if err != nil {
		log.ErrorWithCtx(ctx,"GetAccountAppealInfo fail. req:%+v  err:%v", req, err)
		return out, err
	}

	out.AppealInfoList = infoList
	out.Total = totalCnt

	log.DebugWithCtx(ctx,"GetAccountAppealInfo success. req:%+v  resp:%+v", req, out)
	return out, nil
}

func (s *accountAppealServer) AddAccountAppealApply(ctx context.Context, req *pb.AddAccountAppealApplyReq) (*pb.AddAccountAppealApplyResp, error) {
	out := &pb.AddAccountAppealApplyResp{}

	hasAppeal, err := s.db.CheckIfExistAnyAppealInfo(ctx, req.GetAppealInfo().GetUid())
	if err != nil {
		log.ErrorWithCtx(ctx,"CheckIfHasEverAppeal fail. req:%+v  err:%v", req, err)
		return out, err
	}

	req.GetAppealInfo().HasAppeal = hasAppeal

	err = s.db.AddAppealInfo(ctx, req.GetAppealInfo())
	if err != nil {
		log.ErrorWithCtx(ctx,"AddAccountAppealApply fail. req:%+v  err:%v", req, err)
		return out, err
	}

	log.DebugWithCtx(ctx,"AddAccountAppealApply success. req:%+v  resp:%+v", req, out)
	return out, nil
}

func (s *accountAppealServer) CheckIfCanAppeal(ctx context.Context, req *pb.CheckIfCanAppealReq) (*pb.CheckIfCanAppealResp, error) {
	out := &pb.CheckIfCanAppealResp{}

	exist, err := s.db.CheckIfExistPassAppealWithin60Days(ctx, req.GetUid())
	if err != nil {
		log.ErrorWithCtx(ctx,"CheckIfCanAppeal fail. req:%+v  err:%v", req, err)
		return out, err
	}

	// 不存在申诉记录则可以申诉，否则相反
	out.CanAppeal = !exist

	log.DebugWithCtx(ctx,"CheckIfCanAppeal success. req:%+v  resp:%+v", req, out)
	return out, nil
}

func (s *accountAppealServer) HandleAppeal(ctx context.Context, req *pb.HandleAppealReq) (*pb.HandleAppealResp, error) {
	out := &pb.HandleAppealResp{}

	err := s.db.UpdateAppealHandleInfo(ctx, req)
	if err != nil {
		log.ErrorWithCtx(ctx,"HandleAppeal UpdateAppealHandleInfo fail. req:%+v  err:%v", req, err)
		return out, err
	}

	log.DebugWithCtx(ctx,"HandleAppeal success. req:%+v  resp:%+v", req, out)
	return out, nil
}
